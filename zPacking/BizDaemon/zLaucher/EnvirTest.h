﻿// EnvirTest.h: interface for the CEnvirTest class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_ENVIRTEST_H__794A2FCF_0F9F_4358_8F6A_8646ACDB8980__INCLUDED_)
#define AFX_ENVIRTEST_H__794A2FCF_0F9F_4358_8F6A_8646ACDB8980__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#define __TONGBU_AIRDROID__      1   // 1:Airdroid's url, 0:Tongbu's url

#define __INSTALLED_NETFRAMEWORK_4_6__

#ifdef __INSTALLED_NETFRAMEWORK_4_6__
#define __ALSO_INSTALLED_NET40__    1
#else
#define __INSTALLED_NET40__
#endif   // __INSTALLED_NETFRAMEWORK_4_6__

#if __TONGBU_AIRDROID__       // Installing Airdroid

#define __AIRDROID_BUSINESS__ 
//#define __AIRDROID_PARENTAL_CONNECTOR__

#ifdef __AIRDROID_BUSINESS__
#define LAUNCHER_EXE           _T("BizDaemon.exe")
#define APPLICATION_NAME       _T("AirDroid Biz Daemon")
#define LOG_PATH_NAME          _T("BizDaemon\\Logs")
#elif defined(__AIRDROID_PARENTAL_CONNECTOR__)
#define LAUNCHER_EXE           _T("AirDroidParentalConnector.exe")
#define APPLICATION_NAME       _T("AirDroid Parental Connector")
#define LOG_PATH_NAME          _T("AirDroidParentalConnector")
#endif

#else                       // Installing Tongbu

#define __TONGBU_HELPER__
//#define __FLASHGET_CAST__
//#define __PANDA_CONTROL__

#ifdef __TONGBU_HELPER__
#define LAUNCHER_EXE		   _T("Tongbu.exe")
#define APPLICATION_NAME	   _T("Tongbu")
#define LOG_PATH_NAME          _T("Tongbu")
#endif // __TONGBU_HELPER__

#ifdef __FLASHGET_CAST__
#define LAUNCHER_EXE           _T("FlashGetCast.exe")
#define APPLICATION_NAME       _T("FlashGet Cast")
#define LOG_PATH_NAME          _T("FlashGetCast")
#endif // __FLASHGET_CAST__

#ifdef __PANDA_CONTROL__
#define LAUNCHER_EXE           _T("PandaControl.exe")
#define APPLICATION_NAME       _T("Panda Control")
#define LOG_PATH_NAME          _T("PandaControl")
#endif // __PANDA_CONTROL__

#endif 

//#else
//
//#define IDC_LAUNCHER_2022
//
//#ifdef IDC_LAUNCHER_2022
//// Added by Utmost on 2022.12.22
//
//#define __FLASHGET_CAST__
//#ifdef __FLASHGET_CAST__
//
////#define LAUNCHER_EXE           _T("FlashGetCast.exe")
////#define APPLICATION_NAME       _T("FlashGet Cast")
//
//#define LAUNCHER_EXE           _T("PandaSpy.exe")
//#define APPLICATION_NAME       _T("PandaSpy")
//
//#endif // __FLASHGET_CAST__
//
//#else
//// 2022年以前的
//// Added by Utmost20201201
//#if __TONGBU_AIRDROID__      // 使用 AirDroid系列
//
//#if IDC_AIRDROID_PERSION
//#define LAUNCHER_EXE           _T("AirDroid.exe")
//#define APPLICATION_NAME       _T("AirDroid")
//#elif IDC_AIRDROID_BUSINESS
//#define LAUNCHER_EXE           _T("AirDroidBusiness.exe")
//#define APPLICATION_NAME       _T("AirDroid")
//#endif
//
//#else                        // 使用 Tongbu系列
//
//#if IDC_TONGBU_HELPER
//
//#define LAUNCHER_EXE		   _T("Tongbu.exe")
//#define APPLICATION_NAME	   _T("Tongbu")
//
//#elif IDC_PANDA_CONTROL
//
//#define LAUNCHER_EXE           _T("PandaControl.exe")
//#define APPLICATION_NAME       _T("PandaControl")
//
//#elif IDC_POWERCAST
//
//#define LAUNCHER_EXE			L"PowerCast.exe"
//#define APPLICATION_NAME       _T("PowerCast")
//
//#elif IDC_ALTSIGNER              // Added by Utmost20200303
//
//#define LAUNCHER_EXE           _T("AltSigner.exe")
//#define APPLICATION_NAME       _T("Win签名工具")
//
//#elif IDC_VOICEMERGE
//// 定义launcher文件
//#define LAUNCHER_EXE			L"iVoiceMerge.exe"
//#elif IDC_ZXKEFU
//// Added by Utmost20180503
//#define LAUNCHER_EXE			L"zxKefu.exe"
//#elif IDC_WECHATRECOVERY_FOR_ANDROID
//#define LAUNCHER_EXE			L"iWechatRecoveryForAndroid.exe"
//#else
//
//#define IPHONE_JB_EXE L"JBHelper.exe"
//#define TONGBULITE_EXE L"TongbuLite.exe"
////#define IPHONE_EXE L"Dongbo.exe"
//#define iClover_EXE L"iClover.exe"
//#define WM_EXE L"panda pc suite.exe"
//#define ANDROID_EXE L"Android PC Suite.exe"
//#define M8_EXE L"M8 PC Suite.exe"
//#define OMS_EXE L"OPhone PC Suite.exe"
//#define PANDA_EXE L"Panda.exe"
//#define AirDroidClient_EXE	L"Airdroid.exe"
//#define PhoneRecovery_EXE L"PhoneRecovery.exe"
//#define RingtoneLauncher_exe   L"RingtoneLauncher.exe"
//#define AppSearch_exe   L"AppSearch.exe"
//#define WechatMasterWin_EXE  L"WechatMasterWin.exe"
//#define iDataRecovery_exe    L"iDataRecovery.exe"
//#define iWeChatMigration_exe L"iWeChatMigration.exe"
//#define iWechatAssistant_EXE L"iWechatAssistant.exe"
//#endif
//
//#endif
//
//#endif
//#endif // IDC_LAUNCHER_NET46_NET40

#define IPHONE_TYPE  L"iphone"
#define WM_TYPE       L"wm"
#define ANDROID_TYPE L"android"

#define   ACCESS_READ    1   
#define   ACCESS_WRITE   2   

// 如果定义UAC，则exe文件名为launcher_uac.exe
#define UAC

#pragma comment( lib, "shlwapi.lib")//加载动态库为了调用PathFileExists函数

#pragma   warning(disable:4786)

#include <map>
#include <string>
#include <vector>
#include <Winsvc.h>

#define ANETFrameWork4			    "ANETFrameWork4"     // 安装Net4.6需要Net4.0支持

#ifdef NET47
#define ANETFrameWork4_7		    "ANETFrameWork4_7"
#define EdgeWebView2              "EdgeWebView2"
#define Win7KB3033929             "Windows7_KB3033929"
#else
#ifdef __INSTALLED_NETFRAMEWORK_4_6__
#define ANETFrameWork4_6		"ANETFrameWork4_6"   // for FlashGetCast.exe 及其他Airdroid程序
#else
#define ANETFrameWork2			"ANETFrameWork2"
#define ANETFrameWork10			"ANETFrameWork10"
#if IDC_ALTSIGNER
#define ANETFrameWork451	    "ANETFrameWork4_5_1"
#else
#define NETFRAMEWORK40_DOWNLOADED
#endif
#endif // __INSTALLED_NETFRAMEWORK_4_6__

#endif // NET47

#define iTunes			"iTunes"

enum ClientExeType {
	ClientExeType_Tongbu = 0,
	ClientExeType_AirDroid,
	//ClientExeType_AndroidWechat,
	//ClientExeType_WechatAssistant,
	//ClientExeType_WechatMaster,
	//ClientExeType_WechatMigration,
	//ClientExeType_DataRecovery,
	//ClientExeType_VoiceMerge,
	ClientExeType_Other
};

typedef struct {
	std::string md5;
	std::string soft_name;
} software_infor_s;

typedef std::map<std::string, software_infor_s>  SoftwareMaps;
typedef std::pair<std::string, software_infor_s> SoftwarePairs;

class CEnvirTest {

public:
	enum PhoneType { none = 0, iphone, wm, andorid, other };
	enum DotNetFxVersion {
		DotNetFxVersion4_0,
#ifdef NET47
		DotNetFxVersion4_7,
#else
#ifdef __INSTALLED_NETFRAMEWORK_4_6__
		DotNetFxVersion4_6,
#elif !defined(__INSTALLED_NET40__)
		DotNetFxVersion2_0,
		DotNetFxVersion4_5_1,
#endif // __INSTALLED_NETFRAMEWORK_4_6__	

#endif // NET47	
	};

public:
	CEnvirTest();
	virtual ~CEnvirTest();

	bool IsTongbuHostType();
	bool IsAirdroidHostType();

	bool MobileStyle();//判断是哪中类型的手机助手，把类型写在m_currentType中，路径名写在m_lanuchPath中

	bool IsValidDotNetKey(const std::string& sKey);
	bool IsValidiTunesKey(const std::string& sKey);

	bool IsDotNetfxInstalled();
	bool IsNeedSoftInstalled(BOOL bPacked);

	LPCTSTR	GetDotNetFileName();

	ClientExeType GetClientExeType();

#ifdef UAC
	bool RunAsDesktopUser(
		__in    const wchar_t* szApp,
		__in    wchar_t* szCmdLine,
		__in    const wchar_t* szCurrDir,
		__in    STARTUPINFOW& si,
		__inout PROCESS_INFORMATION& pi);
#endif

private:
	void SetVistaAndLater(LPCWSTR lpFile);   

public:
	void Run();
	void Run(LPCWSTR lpFile, LPCWSTR lpParameters, int nShow);
	void RunAsAdmin(LPCWSTR lpFile, LPCWSTR lpParameters, int nShow);

	void ShellExe(LPCTSTR lpFile, LPCTSTR lpParameters, int nShow);
	BOOL IsAdmin(void);

	bool m_isVista;

	void GetOSVer(CString& version, DWORD& dwMajorVersion);
	void GetLastOSVer(CString& version, DWORD& dwMajorVersion);

	static bool IsWow64();
	static bool Is64BitWindows();

	bool IsNetfx4Installed();//add by lmj 20161018
	bool IsNeedSoftInstalledDotNet4();

#ifdef NET47
    bool _IsInstalledEdgeWebView2;

    bool IsEdgeWebViewInstalled();
    bool IsNetFx47Version();
    bool IsNeedSoftInstalledDotNet4_7(BOOL bPacked);
    
#endif // NET47 

#ifndef __INSTALLED_NETFRAMEWORK_4_6__
#ifndef __INSTALLED_NET40__
	bool IsNetfx35Installed();
	bool IsNetfx20Installed();
	bool IsNeedSoftInstalledDotNet2();
#endif // !__INSTALLED_NET40__	

#ifdef ANETFrameWork451	
	bool IsNeedSoftInstalledDotNet451();
	bool IsNetfx451Installed();
#endif

#else
	
#ifdef NET47
    bool IsNetV40Installed();
#else
    bool IsNetfx4_6Installed();
	bool IsNeedSoftInstalledDotNet4_6(BOOL bPacked);
#endif // NET47

#endif // !__INSTALLED_NETFRAMEWORK_4_6__	

	BOOL IsAppleServerInstalled();

	CStringW m_lanuchPath;
	CStringW m_autoUpgradePath;
	CStringW m_HelperPath;

	PhoneType m_currentType;

	//std::map<std::string, std::string> m_mapSoftName;
	SoftwareMaps m_mapSoftName;

	std::vector<std::string> m_softOfficialName;

	DotNetFxVersion mDotNetFxVersion;
	ClientExeType	mHostExeType;
    
public:
	bool RegistryGetValue(HKEY hk, const TCHAR* pszKey, const TCHAR* pszValue, 
						  DWORD dwType, LPBYTE data, DWORD dwSize);
};

#endif // !defined(AFX_ENVIRTEST_H__794A2FCF_0F9F_4358_8F6A_8646ACDB8980__INCLUDED_)
