#pragma code_page(65001)
// Microsoft Visual C++ generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#ifndef APSTUDIO_INVOKED
#include "targetver.h"
#endif
#include "afxres.h"
#include "verrsrc.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// 中文(简体，中国) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_SIMPLIFIED
//#pragma code_page(936)

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE 
BEGIN
    "#ifndef APSTUDIO_INVOKED\r\n"
    "#include ""targetver.h""\r\n"
    "#endif\r\n"
    "#include ""afxres.h""\r\n"
    "#include ""verrsrc.h""\r\n"
    "\0"
END

3 TEXTINCLUDE 
BEGIN
    "#define _AFX_NO_SPLITTER_RESOURCES\r\n"
    "#define _AFX_NO_OLE_RESOURCES\r\n"
    "#define _AFX_NO_TRACKER_RESOURCES\r\n"
    "#define _AFX_NO_PROPERTY_RESOURCES\r\n"
    "\r\n"
    "#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)\r\n"
    "#ifdef _WIN32\r\n"
    "LANGUAGE 4, 2\r\n"
    "#endif //_WIN32\r\n"
    "#include ""res\\Launcher91.rc2""  // non-Microsoft Visual C++ edited resources\r\n"
    "#include ""l.chs\\afxres.rc""          // Standard components\r\n"
    "#if !defined(_AFXDLL)\r\n"
    "#include ""l.CHS\\afxribbon.rc""   // MFC \r\n"
    "#endif\r\n"
    "#endif\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

IDD_LAUNCHER91_DIALOG_MAIN DIALOGEX 0, 0, 327, 215
STYLE DS_SETFONT | DS_MODALFRAME | WS_MINIMIZEBOX | WS_POPUP | WS_VISIBLE | WS_DISABLED | WS_CAPTION | WS_SYSMENU
EXSTYLE WS_EX_APPWINDOW
CAPTION "组件下载安装"
FONT 9, "宋体", 0, 0, 0x1
BEGIN
    CONTROL         "",IDC_STATIC,"Static",SS_WHITERECT,0,0,327,31
    DEFPUSHBUTTON   "完成(F)",IDOK,209,196,50,14
    PUSHBUTTON      "取消(C)",IDCANCEL,270,196,47,14
    LTEXT           "正在下载组件，请稍等...",IDC_STATIC_SOFT_STATE,21,94,292,8
    CONTROL         "",IDC_PROGRESS_DOWNSOFT,"msctls_progress32",WS_BORDER,21,107,293,14
    LTEXT           "为您安装缺少的组件，请稍等",IDC_STATIC_BK_TEXT,23,17,104,8
    CONTROL         "",IDC_STATIC,"Static",SS_ETCHEDHORZ,0,32,327,1
    CONTROL         "",IDC_STATIC,"Static",SS_ETCHEDHORZ,0,189,327,1
    LTEXT           "下载速度：",IDC_STATIC_FILESIZE_LABEL,195,127,41,8
    LTEXT           "",IDC_STATIC_FILESIZE,273,127,38,8
    LTEXT           "已完成：",IDC_STATIC_COMPLATE_LABEL,21,127,44,8
    LTEXT           "",IDC_STATIC_COMPLATE,82,127,60,8
    PUSHBUTTON      "<上一步(B)",IDC_BUTTON_PRE,159,196,50,14
END

IDD_DIALOG_GUIDE DIALOGEX 0, 0, 327, 217
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "环境检测及更新向导"
FONT 9, "宋体", 0, 0, 0x1
BEGIN
    CONTROL         "",IDC_STATIC,"Static",SS_WHITERECT,0,0,327,31
    LTEXT           "正在检测你的系统",IDC_STATIC_TEXT,11,10,100,8
    PUSHBUTTON      "下一步(N)>",IDOK,207,196,50,14
    PUSHBUTTON      "取消(C)",IDCANCEL,270,196,47,14
    CONTROL         "",IDC_STATIC,"Static",SS_ETCHEDHORZ,0,189,327,1
    PUSHBUTTON      "<上一步(B)",IDC_BUTTON_P,157,196,50,14
    CONTROL         "",IDC_STATIC,"Static",SS_ETCHEDHORZ,0,32,327,1
    CONTROL         "List1",IDC_LIST_GUIDE,"SysListView32",LVS_LIST | WS_BORDER | WS_TABSTOP,27,79,272,85
    LTEXT           "检测到  个系统缺少的组件（双击可手动下载）",IDC_STATIC_INSTALLSOFTNUM,26,64,276,8
    LTEXT           "单击""下一步""开始下载并安装缺少的组件",IDC_STATIC_SHOW3,25,172,293,8
END

IDD_DIALOG_IFINSTALLITUNES DIALOGEX 0, 0, 240, 117
STYLE DS_SETFONT | DS_MODALFRAME | WS_POPUP | WS_CAPTION | WS_SYSMENU
CAPTION "Launcher"
FONT 9, "宋体", 0, 0, 0x1
BEGIN
    DEFPUSHBUTTON   "重新安装",IDOK,113,97,50,14
    PUSHBUTTON      "不再安装",IDCANCEL,176,97,50,14
    CONTROL         "",IDC_STATIC_LINE,"Static",SS_ETCHEDHORZ,0,69,240,1
    CONTROL         "下次不显示提示框，按照本次设置执行。",IDC_CHECK_ISHIDE,"Button",BS_AUTOCHECKBOX | WS_TABSTOP,21,79,218,10
    CONTROL         IDB_BITMAP_TITLE,IDC_STATIC,"Static",SS_BITMAP,0,16,36,30
END


/////////////////////////////////////////////////////////////////////////////
//
// Version
//

VS_VERSION_INFO VERSIONINFO
 FILEVERSION 1,0,1,0
 PRODUCTVERSION 1,0,1,0
 FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
 FILEFLAGS 0x1L
#else
 FILEFLAGS 0x0L
#endif
 FILEOS 0x4L
 FILETYPE 0x1L
 FILESUBTYPE 0x0L
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904b0"
        BEGIN
            VALUE "CompanyName", "Sand Studio"
            VALUE "FileDescription", "AirDroid Biz Daemon Launcher"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "Launcher"
            VALUE "LegalCopyright", "Copyright 2024 Sand Studio. All rights reserved."
            VALUE "OriginalFilename", "Launcher.exe"
            VALUE "ProductName", "AirDroid Biz Daemon"
            VALUE "ProductVersion", "*******"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
IDI_ICON_LOGO           ICON                    "res/Launcher.ico"


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO
BEGIN
    IDD_LAUNCHER91_DIALOG_MAIN, DIALOG
    BEGIN
        LEFTMARGIN, 7
        TOPMARGIN, 7
        BOTTOMMARGIN, 210
    END

    IDD_DIALOG_GUIDE, DIALOG
    BEGIN
        TOPMARGIN, 7
        BOTTOMMARGIN, 210
    END

    IDD_DIALOG_IFINSTALLITUNES, DIALOG
    BEGIN
        TOPMARGIN, 7
        BOTTOMMARGIN, 110
    END
END
#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    IDS_STRING_INFO1        "对不起，我们未能在您的计算机检测到iTunes的存在!"
    IDS_STRING_INFO2        "如果您没有安装iTunes的话，只能通过WiFi连接iPhone!"
    IDS_STRING_INFO3        "无法使用USB模式连接!"
    IDS_STRING_INFO4        "是否继续安装iTunes?"
END

STRINGTABLE
BEGIN
    IDS_COMPONENT_CAPTION   "组件下载安装"
    IDS_STRING_FINISHED     "完成(F)"
    IDS_STRING_CANCEL       "取消(C)"
    IDS_STRING_DOWNLOADING  "正在下载组件，请稍等..."
    IDS_STRING_INSTALLED    "为您安装缺少的组件，请稍等"
    IDS_STRING_DOWN_SPEED   "下载速度："
    IDS_STRING_COMPLETED    "已完成："
    IDS_STRING_PREV_STEP    "<上一步(B)"
END

STRINGTABLE
BEGIN
    IDS_STRING_NEXT_STEP    "下一步(N)>"
    IDS_ENVIRONMENT_CAPTION "环境检测及更新向导"
    IDS_STRING_CHECKED_ENVI "正在检测你的系统"
    IDS_STRING_MANUAL_DOWN  "检测到%d个系统缺少的组件（双击可手动下载）"
    IDS_STRING_BEGINING     "单击""下一步""开始下载并安装缺少的组件"
    IDS_STRING_REINSTALL    "重新安装"
    IDS_STRING_NONETIPS     "下次不显示提示框，按照本次设置执行。"
    IDS_STRING_CONTINUE     "继续安装"
    IDS_STRING_NOT_INTALLED "不再安装"
    IDS_STRING_EXIT_DOWNING "正在下载组件是否退出！"
    IDS_STRING_EXIT_INSTALLING "正在安装组件是否退出！"
    IDS_STRING_CANCEL_APP   "系统缺少相应的组件，取消下载安装可能无法正常启动%s！下载安装请点击 确定"
    IDS_STRING_DOWN_PART    "正在下载  "
    IDS_STRING_WAINTING     " 请稍候"
    IDS_STRING_INSTALLING   "正在安装  "
    IDS_STRING_UNKNOWN      "未知"
END

STRINGTABLE
BEGIN
    IDS_STRING_SERVER_BUSY  "服务器忙，文件下载失败，请稍候重试！"
    IDS_STRING_JOIN_MOBILE  "%s可能无法正常连接到您的手机！\r\n \r\n手动安装请点击 确定"
    IDS_STRING_DOWNFAIL     "下载失败,请重试！"
    IDS_STRING_WIN7PACK     "Windows 7系统需要安装Server Pack 1补丁"
    IDS_STRING_FILEPATH     "文件路径不存在，请创建!"
    IDS_STRING_IS64BITOS    "抱歉！%s 当前仅支持系统为64位、Windows 7 及以上的设备。"
    IDS_STRING_HANDLE_OPERATE 
                            "未能成功的在您的电脑上安装%s组件程序，%s可能无法正常运行！\r\n\r\n手动安装请点击 确定"
END

#endif    // 中文(简体，中国) resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// 中文(繁体，中国台湾) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHT)
LANGUAGE LANG_CHINESE, SUBLANG_CHINESE_TRADITIONAL
//#pragma code_page(950)

/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    IDS_STRING_INFO1        "癸ぃ癬иゼ眤筿福盎代iTunes!"
    IDS_STRING_INFO2        "狦眤⊿Τ杆iTunes杠硓筁WiFi硈钡iPhone!"
    IDS_STRING_INFO3        "礚猭ㄏノUSB家Α硈絬!"
    IDS_STRING_INFO4        "琌膥尿杆iTunes?"
END

STRINGTABLE
BEGIN
    IDS_COMPONENT_CAPTION   "じン更杆"
    IDS_STRING_FINISHED     "ЧΘ(F)"
    IDS_STRING_CANCEL       "(C)"
    IDS_STRING_DOWNLOADING  "タ更じン叫祔单..."
    IDS_STRING_INSTALLED    "眤杆ぶじン叫祔单"
    IDS_STRING_DOWN_SPEED   "更硉"
    IDS_STRING_COMPLETED    "ЧΘ"
    IDS_STRING_PREV_STEP    "<˙(B)"
END

STRINGTABLE
BEGIN
    IDS_STRING_NEXT_STEP    "˙(N)>"
    IDS_ENVIRONMENT_CAPTION "吏挂盎代の穝弘艶"
    IDS_STRING_CHECKED_ENVI "タ盎代╰参"
    IDS_STRING_MANUAL_DOWN  "盎代%d╰参ぶじン蛮阑も笆更"
    IDS_STRING_BEGINING     "翴匡""˙""秨﹍更杆ぶじン"
    IDS_STRING_REINSTALL    "穝杆"
    IDS_STRING_NONETIPS     "Ωぃ陪ボ矗ボㄌ酚セΩ砞﹚磅︽"
    IDS_STRING_CONTINUE     "膥尿杆"
    IDS_STRING_NOT_INTALLED "ぃ杆"
    IDS_STRING_EXIT_DOWNING "タ更じン琌癶"
    IDS_STRING_EXIT_INSTALLING "タ杆じン琌癶"
    IDS_STRING_CANCEL_APP   "╰参ぶ癸莱じン更杆礚猭タ盽币笆%s更杆叫翴匡 絋﹚"
    IDS_STRING_DOWN_PART    "タ更 "
    IDS_STRING_WAINTING     "叫祔"
    IDS_STRING_INSTALLING   "タ杆 "
    IDS_STRING_UNKNOWN      "ゼ"
END

STRINGTABLE
BEGIN
    IDS_STRING_SERVER_BUSY  "狝竟Γ郎更ア毖叫祔刚"
    IDS_STRING_JOIN_MOBILE  "%s礚猭タ盽硈钡眤も诀\r\n \r\nも笆杆叫翴阑 絋﹚"
    IDS_STRING_DOWNFAIL     "更ア毖,叫刚"
    IDS_STRING_WIN7PACK     "Windows 7╰参惠璶杆Server Pack 1干"
    IDS_STRING_FILEPATH     "ゅ郎隔畖ぃ叫承"
    IDS_STRING_IS64BITOS    "╆簆%s ヘ玡度や穿穨╰参64じWindows 7 の杆竚"
    IDS_STRING_HANDLE_OPERATE 
                            "ゼΘ眤筿福杆%sじン祘Α%s礚猭タ盽磅︽\r\n\r\nも笆杆叫翴匡 絋﹚"
END

#endif    // 中文(繁体，中国台湾) resources
/////////////////////////////////////////////////////////////////////////////


/////////////////////////////////////////////////////////////////////////////
// 英语(美国) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
//#pragma code_page(1252)

/////////////////////////////////////////////////////////////////////////////
//
// String Table
//

STRINGTABLE
BEGIN
    IDS_STRING_INFO1        "Sorry, we were unable to detect the presence of iTunes on your computer!"
    IDS_STRING_INFO2        "If you do not have iTunes installed, you can only connect to your iPhone via WiFi!"
    IDS_STRING_INFO3        "Cannot connect using USB mode!"
    IDS_STRING_INFO4        "Do you want to continue installing iTunes?"
END

STRINGTABLE
BEGIN
    IDS_COMPONENT_CAPTION   "Component download and installation"
    IDS_STRING_FINISHED     "Finished(F)"
    IDS_STRING_CANCEL       "Cancel(C)"
    IDS_STRING_DOWNLOADING  "Downloading components, please wait..."
    IDS_STRING_INSTALLED    "Installing missing components for you, please wait"
    IDS_STRING_DOWN_SPEED   "Download speed:"
    IDS_STRING_COMPLETED    "Completed:"
    IDS_STRING_PREV_STEP    "<Previous step(B)"
END

STRINGTABLE
BEGIN
    IDS_STRING_NEXT_STEP    "Next(N)>"
    IDS_ENVIRONMENT_CAPTION "Environment Detection and Update Wizard"
    IDS_STRING_CHECKED_ENVI "Checking your system"
    IDS_STRING_MANUAL_DOWN  "%d system missing components detected (double-click to download manually)"
    IDS_STRING_BEGINING     "Click ""Next"" to start downloading and installing missing components"
    IDS_STRING_REINSTALL    "Reinstall"
    IDS_STRING_NONETIPS     "The prompt box will not be displayed next time and will be executed according to this setting."
    IDS_STRING_CONTINUE     "Continue installation"
    IDS_STRING_NOT_INTALLED "No more installation"
    IDS_STRING_EXIT_DOWNING "Do you want to exit while downloading components?"
    IDS_STRING_EXIT_INSTALLING "Exit the component being installed!"
    IDS_STRING_CANCEL_APP   "The system lacks the corresponding components. Canceling the download and installation may not start %s normally! Please click OK to download and install"
    IDS_STRING_DOWN_PART    "Downloading"
    IDS_STRING_WAINTING     "Please wait"
    IDS_STRING_INSTALLING   "Installing"
    IDS_STRING_UNKNOWN      "Unknown"
END

STRINGTABLE
BEGIN
    IDS_STRING_SERVER_BUSY  "The server is busy, file download failed, please try again later!"
    IDS_STRING_JOIN_MOBILE  "%s may not be able to connect to your phone properly! \r\n \r\nPlease click OK to install manually"
    IDS_STRING_DOWNFAIL     "Download failed, please try again!"
    IDS_STRING_WIN7PACK     "Need to install Server Pack 1 for Windows 7"
    IDS_STRING_FILEPATH     "File path does not exist, please create!"
    IDS_STRING_IS64BITOS    "Sorry, %s currently only supports devices running Windows 7 64-bit and above!"
    IDS_STRING_HANDLE_OPERATE 
                            "Failed to install the %s component program on your computer, %s may not function properly!\r\n\r\nTo install manually, please click OK"
END

#endif    // 英语(美国) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//
#define _AFX_NO_SPLITTER_RESOURCES
#define _AFX_NO_OLE_RESOURCES
#define _AFX_NO_TRACKER_RESOURCES
#define _AFX_NO_PROPERTY_RESOURCES

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_CHS)
#ifdef _WIN32
LANGUAGE 4, 2
#endif //_WIN32
#include "res\Launcher91.rc2"  // non-Microsoft Visual C++ edited resources
#include "l.chs\afxres.rc"          // Standard components
#if !defined(_AFXDLL)
#include "l.CHS\afxribbon.rc"   // MFC 
#endif
#endif

/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

