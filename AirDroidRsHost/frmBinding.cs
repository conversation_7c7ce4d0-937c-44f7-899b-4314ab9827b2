﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using System.IO;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;

namespace AirDroidRsHost
{
    public partial class frmBinding : frmBase
    {
        private System.Windows.Forms.Timer mTipTimer;

        private rsOrganizationInfo mCurrentOrg;
        private string mCurrentDeviceName;
        private string mCurrentDeployCode;

        private int mTipTimeCount = 5;
        private int mBtnLocationX;
        private int mBtnWidth;

        private tdActionHelper<tdActionItemForRS> mActionHelper = tdActionHelper<tdActionItemForRS>.Instance();

        public override skSplit skTransparentImageSplit => new skSplit(15);

        public frmBinding()
        {
            InitializeComponent();
            this.InitStyle();
            this.txtDeviceName.MaxLength = 40;
            this.txtDeviceName.PlaceHolder = this.Language.GetString("rs_pc_device_name");
            this.txtDeviceName.Text = Common.GetComputerName(40);
            this.txtDeployCode.PlaceHolder = this.Language.GetString("Common_biz_depploy_code");
        }

        public override void SetSkin()
        {
            try
            {
                this.btnClose.skIcon = MyResource.GetImage("ic_close.png");
                this.btnMax.skIcon = MyResource.GetImage("ic_chromemaximize.png");
                this.btnMin.skIcon = MyResource.GetImage("ic_chromeminimize.png");
                this.btnClose.skIconState = this.btnMax.skIconState = this.btnMin.skIconState = skImageState.FourState;
                this.btnClose.skIconSize = this.btnMax.skIconSize = this.btnMin.skIconSize = new Size(32, 22);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "frmBinding.SetSkin");
            }
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.Location = new Point(-10000, -10000);
            //this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.None;
            this.Icon = Properties.Resources.airdroid;
            this.skShowTitle = false;
            this.skBackgroundColor = Color.White;
            this.skBorderType = skBorderType.Round;
            this.skBorderStrokeColor = Color.White;
            this.skCanbeMove = true;
            this.skTitle = this.Language.GetString("Remote_BIZ_Enroll");

            this.lblDeployCodeTitle.skText = this.Language.GetString("Common_biz_depploy_code");
            //this.txtDeployCode.Text = "";
            this.lblDeviceName.skText = this.Language.GetString("rs_pc_device_name");
            this.lblTitle.skText = this.Language.GetString("Remote_BIZ_Enroll");
            this.lblDescription.skText = this.Language.GetString("rs_enter_deployment_code_below");
            this.lblHelp.skText = this.Language.GetString("rs_unattached_deploy_code_help_title");
            this.btnOK.skText = this.Language.GetString("rs_enroll");
            this.btnCancel.skText = this.Language.GetString("Common.Cancel");
            this.lblCompanyTip.skText = string.Format(this.Language.GetString("rs_pc_company_limit"), this.Language.GetString("rs_policy_title"));
            this.lblCompanyTip.AddSpecial(this.Language.GetString("rs_policy_title"), MyFont.CreateFont(this.lblCompanyTip.skTextFont.FontFamily, this.lblCompanyTip.skTextFont.Size, true), Color.FromArgb(70, 79, 96));

            this.lblSelectAssistanceTitle.skText = string.Format(this.Language.GetString("rs_select_assistance"));//请选择您的远程协助场景
            this.lblSelectAssistanceDetail.SetText(this.Language.GetString("rs_based_needs_choose"));


            this.btnAttended.skText = this.Language.GetString("rs_attached_mode");
            this.btnUnAttended.skText = this.Language.GetString("rs_unattached_mode");
            this.skBorderRadius = new skBorderRadius(8);


            this.txtDeviceName.TextChanged += txtDeployCode_TextChanged;
            this.txtDeviceName.GotFocus += TxtDeployCode_GotFocus;
            this.txtDeviceName.LostFocus += TxtDeployCode_LostFocus;

            this.txtDeployCode.MaxLength = 40;
            this.txtDeployCode.TextChanged += txtDeployCode_TextChanged;
            this.txtDeployCode.GotFocus += TxtDeployCode_GotFocus;
            this.txtDeployCode.LostFocus += TxtDeployCode_LostFocus;
        }

        protected override void OnDpiChanged()
        {
#if DPI
            base.OnDpiChanged();
#endif
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            Screen screen = Screen.PrimaryScreen;

            this.Location = new Point((screen.WorkingArea.Width - this.Width) / 2, (screen.WorkingArea.Height - this.Height) / 2);

            this.txtDeployCode.skEnterWithValidating = true;
            this.txtDeviceName.skEnterWithValidating = true;
            this.InitButton();
            this.SetRadius();
        }

        private void InitButton()
        {
            this.btnCancel.Location = new Point(this.Size.Width - this.btnCancel.Width - 24.ToDPI(this.Dpi), this.Size.Height - this.btnCancel.Height - 24.ToDPI(this.Dpi));
            this.btnOK.Location = new Point(this.btnCancel.Location.X - this.btnOK.Width - 12.ToDPI(this.Dpi), this.btnCancel.Location.Y);
            this.lblHelp.Location = new Point(24.ToDPI(this.Dpi), this.Size.Height - this.lblHelp.Height - 30.ToDPI(this.Dpi));
        }


        private void InitStyle()
        {
            this.Size = new Size(560, 215);
            this.ShowInTaskbar = false;
            this.pnlCompanyInfo.Location = this.pnlCompanyInput.Location;
            this.pnlSelectAssistance.Location = this.pnlCompanyInput.Location;
        }

        private void TxtDeployCode_LostFocus(object sender, EventArgs e)
        {
            skTextBox textBox = sender as skTextBox;
            if (textBox.Name == "txtDeployCode")
            {
                this.SetControlVisible(this.btnClearDeploy, false);
                this.ShowDeployCodeTitle(this.lblDeployCodeTitle, false);
                this.TextBoxBorderHandle(this.pnlLinkCode, false, true);
                return;
            }
            else if (textBox.Name == "txtDeviceName")
            {
                this.SetControlVisible(this.btnClearDeviceName, false);
                this.ShowDeployCodeTitle(this.lblDeviceName, false);
                this.TextBoxBorderHandle(this.pnlDeviceName, false, true);
                return;
            }

        }

        private void TxtDeployCode_GotFocus(object sender, EventArgs e)
        {
            skTextBox textBox = sender as skTextBox;
            bool res = textBox.Text.Length > 0;

            if (textBox.Name == "txtDeployCode")
            {
                this.SetControlVisible(this.btnClearDeploy, res);
                this.ShowDeployCodeTitle(this.lblDeployCodeTitle, true);
                this.TextBoxBorderHandle(this.pnlLinkCode, true, true);
                return;
            }
            else if (textBox.Name == "txtDeviceName")
            {
                this.SetControlVisible(this.btnClearDeviceName, res);
                this.ShowDeployCodeTitle(this.lblDeviceName, true);
                this.TextBoxBorderHandle(this.pnlDeviceName, true, true);
                return;
            }

        }


        private void ShowDeployCodeTitle(skLabel label, bool show, bool isSuccess = true)
        {
            label.Visible = show;

            if (show)
            {
                if (isSuccess)
                {
                    label.skTextColor = Color.FromArgb(0, 98, 246);
                }
                else
                {
                    label.skTextColor = Color.FromArgb(236, 85, 83);
                }
            }
        }

        private void TextBoxBorderHandle(skPanel panel, bool show, bool isSuccess = true)
        {
            this.lblErrorTip.Visible = !isSuccess;

            if (show)
            {
                if (isSuccess)
                {
                    panel.skBorderStrokeColor = Color.FromArgb(0, 98, 246);
                }
                else
                {
                    panel.skBorderStrokeColor = Color.FromArgb(236, 85, 83);
                }
            }
            else
            {
                panel.skBorderStrokeColor = Color.FromArgb(234, 234, 234);
            }
        }

        private void txtDeployCode_TextChanged(object sender, EventArgs e)
        {
            skTextBox textBox = sender as skTextBox;
            bool res = textBox.Text.Length > 0;

            if (textBox.Name == "txtDeployCode")
            {
                this.SetControlEnable(this.btnOK, res);
                if (textBox.Focused)
                    this.SetControlVisible(this.btnClearDeploy, res);
                else
                    this.SetControlVisible(this.btnClearDeploy, false);
            }
            else if (textBox.Name == "txtDeviceName")
            {
                if (textBox.Focused)
                    this.SetControlVisible(this.btnClearDeviceName, res);
                else
                    this.SetControlVisible(this.btnClearDeviceName, false);
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (this.pnlCompanyInfo.Visible == false)
            {
                this.mActionHelper.Add(tbActionModeKeyForRS.DeployJoinEnterprise);
                if (!this.TextBoxCheck(this.txtDeviceName))
                {
                    this.ShowDeployCodeTitle(this.lblDeviceName, true, false);
                    this.TextBoxBorderHandle(this.pnlDeviceName, true, false);
                    this.lblErrorTip.skText = this.Language.GetString("no_special_char");
                    this.lblErrorTip.Visible = true;
                    return;
                }

                this.SetControlEnable(this.btnOK, false);
                this.ShowDeployCodeTitle(this.lblDeviceName, false, true);
                this.TextBoxBorderHandle(this.pnlDeviceName, false, true);
                this.ShowDeployCodeTitle(this.lblDeployCodeTitle, false, true);
                this.TextBoxBorderHandle(this.pnlLinkCode, false, true);

                ThreadMgr.Start(this.ThreadDeviceDeployCheck);
            }
            else
            {
                this.mActionHelper.Add(tbActionModeKeyForRS.DeployJoinEnterpriseOK);
                this.ShowAttendedSelect();
            }
        }

        public bool TextBoxCheck(skTextBox textBox)
        {
            List<string> strs = new List<string>() { "/", @"\", ":", "*", "<", ">", "|", "?", "'", "\"", "=", ";", ",", "&" };
            foreach (string str in strs)
            {
                if (textBox.Text.Contains(str))
                    return false;
            }
            return true;
        }

        private void ThreadDeviceDeployCheck()
        {
            bool suc = false;
            string errorMsg = "";

            try
            {
                if (!Common.NetworkIsAvailable())
                {
                    RsCommon.ShowSplashBox(MyForm.GetMainForm(), this.Language.GetString("Common_check_network"));
                    return;
                }

                this.mCurrentDeployCode = this.txtDeployCode.Text.Trim();
                if (string.IsNullOrEmpty(this.mCurrentDeployCode))
                {
                    //skSplashBox.Show(this.Language.GetString("rs_pc_occurred_deployment"), this);//部署码不能为空
                    return;
                }

                this.mCurrentDeviceName = this.txtDeviceName.Text.Trim();
                if (string.IsNullOrEmpty(this.mCurrentDeviceName))
                {
                    this.mCurrentDeviceName = MachineInfo.CurrentInfo.ComputerName;
                }

                if (string.IsNullOrEmpty(this.mCurrentDeviceName))
                {
                    errorMsg = this.Language.GetString("biz_modify_device_name_empty");//设备名称不能为空
                    goto DoSet;
                }

                ServerArgs<rsOrganizationInfo> args = RsAPI.GetBizInfo(this.mCurrentDeployCode);
                if (args == null)
                {
                    errorMsg = this.Language.GetString("rs_pc_occurred_deployment");//获取部署码出现错误，请稍后重试
                    goto DoSet;
                }

                if (args.Code == -51)
                {
                    errorMsg = this.Language.GetString("rs_bind_code_invaild");//无效的部署码，请检查输入的部署码是否正确！
                    goto DoSet;
                }

                if (args.Code != 1)
                {
                    errorMsg = this.Language.GetString("rs_pc_occurred_deployment");//获取部署码出现错误，请稍后重试
                    goto DoSet;
                }

                suc = true;

                this.mCurrentOrg = args.Data;

                DoSet:
                this.SetCompanyInfo(errorMsg);

            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ThreadDeviceDeployCheck");
            }
            finally
            {
                if (!suc)
                {
                    this.SetControlEnable(this.btnOK, true);
                    this.SetControlEnable(this.btnCancel, true);
                }
            }
        }

        private void ThreadDeviceDeployCallAPI()
        {
            bool res = false;
            try
            {
                ServerCustomArgs<rsDeviceDeployInfo> argDeploy = RsAPI.DeviceDeploy(this.mCurrentDeployCode, RsController.Instance.ShareCode, this.mCurrentDeviceName, string.IsNullOrEmpty(this.mCurrentOrg.company_name) ? this.mCurrentOrg.organization_name : this.mCurrentOrg.company_name, this.mCurrentOrg.unattended == 1 ? skDeviceType.RS_No_Man_Win : skDeviceType.RS_Man_Win);

                if (argDeploy == null)
                {
                    RsCommon.ShowSplashBox(MyForm.GetMainForm(), this.Language.GetString("rs_pc_failed_join_company_network"));
                    this.BackHandle();
                    goto DoExit;
                }

                if (argDeploy.Code == -49 || argDeploy.Code == -97 || argDeploy.Code == -1150001)
                {
                    this.BeginInvoke(new Action(() =>
                    {
                        string content = this.Language.GetString("rs_bound_devices_limit_to_purchase");
                        if (argDeploy.Code == -1150001)
                            content = this.Language.GetString("bind_diff_company");

                        skMsgInfoNew msg = RsCommon.GetDefaultMsgInfo(content, this.Language.GetString("pc_rs_intro_title"), this.Language);
                        msg.Buttons = MessageBoxButtons.OK;
                        msg.FirstButton.skText = this.Language.GetString("Common_gotit_tip");
                        msg.FirstButton.skBackgroundImage = Properties.Resources.btn_blue_4;
                        msg.TopMost = true;
                        msg.FormDialog = false;
                        msg.ButtonPadding = new Padding(10, 2, 10, 0);
                        MsgBoxMgr.Show(null, msg);
                    }));
                    //RsCommon.ShowSplashBox(MyForm.GetMainForm(), );
                    this.BackHandle();
                    goto DoExit;
                }

                if (argDeploy.Code == -51 || argDeploy.Code == -52)
                {
                    this.BackHandle(this.Language.GetString("rs_bind_code_invaild"));
                    goto DoExit;
                }

                if (argDeploy.Code == -53)
                {
                    RsCommon.ShowSplashBox(MyForm.GetMainForm(), this.Language.GetString("rs_pc_deployment_failed"));
                    this.BackHandle();
                    goto DoExit;
                }
                if (argDeploy.Code == -99999) // code值为-99999
                {
                    UserBase.ShowCustomInfo(argDeploy.custom_info, this);
                    goto DoExit;
                }

                if (argDeploy.Code != 1)
                {
                    RsCommon.ShowSplashBox(MyForm.GetMainForm(), this.Language.GetString("rs_pc_failed_join_company"));
                    this.BackHandle();
                    goto DoExit;
                }

                //RsController.Instance.GetRsSettingHandle();
                res = true;

                RsCommon.ShowSplashBox(MyForm.GetMainForm(), this.Language.GetString("rs_enroll_successful"), true);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ThreadDeviceDeployCallAPI");
            }

            DoExit:
            this.OnOK_Click(null, null);

            if (res)
                this.Close();
        }

        private void OnOK_Click(object sender, EventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new EventHandler(this.OnOK_Click), sender, e);
            }
            else
            {
                this.btnAttended.StopAnimate();
                this.btnAttended.skIconAlign = skImageAlignment.Left;
                this.btnAttended.skIconSize = new Size(0, 0);
                this.btnAttended.skIcon = null;
                this.btnAttended.skText = this.Language.GetString("rs_attached_mode");

                this.btnUnAttended.StopAnimate();
                this.btnUnAttended.skIconAlign = skImageAlignment.Left;
                this.btnUnAttended.skIconSize = new Size(0, 0);
                this.btnUnAttended.skIcon = null;
                this.btnUnAttended.skText = this.Language.GetString("rs_unattached_mode");


                this.SetControlEnable(this.btnUnAttended, true);
                this.SetControlEnable(this.btnAttended, true);
            }
        }

        private void ShowAttendedSelect()
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new Action(this.ShowAttendedSelect));
            }
            else
            {
                this.pnlCompanyInfo.Visible = false;
                this.btnOK.Visible = false;
                this.btnCancel.Visible = false;

                this.pnlSelectAssistance.Visible = true;
                this.skShowButtonClose = false;

                Size dpiSize = new Size(560, 269).ToDPI(this.Dpi);
                if (this.Size != dpiSize)
                {
                    this.Size = dpiSize;
                    this.Location = new Point(this.Location.X, this.Location.Y - (269 - 215).ToDPI(this.Dpi));
                    // 重定位panel 和 button
                    this.pnlSelectAssistance.Location = new Point(this.pnlSelectAssistance.Location.X, MyDpi.ToDPI(24, this.Dpi));
                    this.pnlSelectAssistance.Size = new Size(this.Width - MyDpi.ToDPI(35, this.Dpi), this.Height - MyDpi.ToDPI(40, this.Dpi));
                    this.btnAttended.Location = new Point(this.btnAttended.Location.X, MyDpi.ToDPI(195, this.Dpi));
                    this.btnUnAttended.Location = new Point(this.btnUnAttended.Location.X, MyDpi.ToDPI(195, this.Dpi));
                }

                if (this.mCurrentOrg.has_unattended_func <= 0)
                {
                    //this.btnUnAttended.Enabled = false;
                }
            }
        }

        private delegate void SetControlEnableHandler(Control ctl, bool isEnabled);
        private void SetControlEnable(Control ctl, bool isEnabled)
        {
            if (ctl.InvokeRequired)
            {
                ctl.Invoke(new SetControlEnableHandler(this.SetControlEnable), ctl, isEnabled);
                return;
            }

            ctl.Enabled = isEnabled;
        }

        private delegate void SetControlVisibleHandler(Control ctl, bool visible);
        private void SetControlVisible(Control ctl, bool visible)
        {
            if (ctl.InvokeRequired)
            {
                ctl.Invoke(new SetControlVisibleHandler(this.SetControlVisible), ctl, visible);
                return;
            }

            ctl.Visible = visible;
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            this.BackHandle();
        }

        private void BackHandle(string errorMsg = null)
        {
            this.mCurrentOrg = null;
            this.SetCompanyInfo(errorMsg);
        }

        private delegate void SetCompanyInfoHandler(string errorMsg = null);
        private void SetCompanyInfo(string errorMsg = null)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetCompanyInfoHandler(this.SetCompanyInfo), errorMsg);
                return;
            }

            if (this.mCurrentOrg == null)
            {
                this.mCurrentDeployCode = null;
                this.mCurrentDeviceName = null;
                this.pnlCompanyInput.Visible = true;
                this.pnlCompanyInfo.Visible = false;
                this.lblHelp.Visible = true;
                this.btnOK.skText = this.Language.GetString("rs_enroll");
                this.SetControlEnable(this.btnOK, true);

                if (string.IsNullOrEmpty(errorMsg))
                {
                    this.ShowDeployCodeTitle(this.lblDeployCodeTitle, false, true);
                    this.TextBoxBorderHandle(this.pnlLinkCode, false, true);
                }
                else
                {
                    this.lblErrorTip.skText = errorMsg;
                    this.ShowDeployCodeTitle(this.lblDeployCodeTitle, true, false);
                    this.TextBoxBorderHandle(this.pnlLinkCode, true, false);
                }

                if (this.mTipTimer != null)
                {
                    this.mTipTimer.Stop();
                }

                this.pnlCompanyInfo.Visible = false;
                this.btnOK.Visible = true;
                this.btnCancel.Visible = true;

                this.pnlSelectAssistance.Visible = false;
                this.skShowButtonClose = true;

                Size dpiSize = new Size(560, 215).ToDPI(this.Dpi);
                if (this.Size != dpiSize)
                {
                    this.Size = dpiSize;
                    this.Location = new Point(this.Location.X, this.Location.Y + (269 - 215).ToDPI(this.Dpi));
                }
            }
            else
            {
                this.skShowButtonClose = false;
                this.lblCompanyName.skText = string.IsNullOrEmpty(this.mCurrentOrg.organization_name) ? this.mCurrentOrg.company_name : this.mCurrentOrg.organization_name;
                this.pnlCompanyInput.Visible = false;
                this.pnlCompanyInfo.Visible = true;
                this.lblHelp.Visible = false;
                this.SetControlEnable(this.btnOK, false);

                if (this.mTipTimer == null)
                {
                    this.mTipTimer = new System.Windows.Forms.Timer();
                    this.mTipTimer.Interval = 1000;
                    this.mTipTimer.Tick += mTipTimer_Tick;
                }
                else
                {
                    this.mTipTimeCount = 5;
                }

                this.btnOK.skText = string.Format("{0} ({1}s)", this.Language.GetString("rs_enroll"), this.mTipTimeCount);
                this.InitButton();
                this.mTipTimer.Start();
            }
        }

        private void mTipTimer_Tick(object sender, EventArgs e)
        {
            this.mTipTimeCount -= 1;
            if (this.mTipTimeCount > 0)
            {
                this.btnOK.skText = string.Format("{0} ({1}s)", this.Language.GetString("rs_enroll"), this.mTipTimeCount);
            }
            else
            {
                this.mTipTimer.Stop();
                this.btnOK.skText = this.Language.GetString("rs_enroll");
                this.SetControlEnable(this.btnOK, true);
            }

            this.InitButton();
        }

        private void btnClearDeploy_Click(object sender, EventArgs e)
        {
            this.txtDeployCode.Clear();
        }

        private void btnClearDeviceName_Click(object sender, EventArgs e)
        {
            this.txtDeviceName.Clear();
        }

        private void btnAttended_Click(object sender, EventArgs e)
        {
            skButton btn = sender as skButton;

            if (btn.Tag.ToString() == "true")
            {
                this.mCurrentOrg.unattended = 0;

                this.mActionHelper.Add(tbActionModeKeyForRS.DeployClickMan);
            }
            else
            {
                this.mCurrentOrg.unattended = 1;
                this.mActionHelper.Add(tbActionModeKeyForRS.DeployClickNoMan);
            }


            this.SetControlEnable(this.btnUnAttended, false);
            this.SetControlEnable(this.btnAttended, false);

            btn.skIconAlign = skImageAlignment.Center;
            btn.skIconSize = Properties.Resources.loading2.Size;
            btn.skIconState = skImageState.OneState;
            btn.skIcon = Properties.Resources.loading2;
            btn.LoadGif();
            btn.skText = "";

            ThreadMgr.Start(this.ThreadDeviceDeployCallAPI);

            //switch (eventType)
            //{
            //    case RSEventType.DeviceTypeUpdateErrorByPara:
            //        errMsg = "有人、无人值守切换失败，参数错误";
            //        return;

            //    case RSEventType.DeviceTypeUpdateErrorByPwd:
            //        errMsg = "有人、无人值守切换失败，密码错误";
            //        return;

            //    case RSEventType.DeviceTypeUpdateErrorByVipLevelLimit:
            //        errMsg = "有人、无人值守切换失败，需要升级订单";
            //        return;

            //    case RSEventType.DeviceTypeUpdateErrorByVipDeviceNumberLimit:
            //        errMsg = "有人、无人值守切换失败，需要加购设备数据";
            //        return;

            //    case RSEventType.DeviceTypeUpdateErrorByFreeDeviceNumberLimit:
            //        errMsg = "有人、无人值守切换失败，试用设备数量达到上限";
            //        return;
            //}

            //this.DialogResult = DialogResult.OK;
        }

        private void pnlLinkCode_Click(object sender, EventArgs e)
        {
            this.txtDeployCode.Focus();
        }

        private void pnlDeviceName_Click(object sender, EventArgs e)
        {
            this.txtDeviceName.Focus();
        }

        private void lblHelp_Click(object sender, EventArgs e)
        {
            this.mActionHelper.Add(tbActionModeKeyForRS.DeployGetCode);

            Common.OpenExplorer(MyUrl.DeployHelp);
        }
    }
}
