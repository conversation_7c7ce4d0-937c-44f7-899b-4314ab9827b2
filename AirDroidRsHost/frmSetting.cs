﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;

using iTong.CoreModule;
using iTong.Components;
using iTong.CoreFoundation;
using iTong.Android;
using System.Diagnostics;

namespace AirDroidRsHost
{
    public partial class frmSetting : frmBase
    {
        #region 当下拉选择密码更新频率时通过事件传递给主界面
        public delegate void NoticePasswordUpdateDelegate();
        public static event NoticePasswordUpdateDelegate NoticePasswordUpdateEvent;
        #endregion

        private tdActionHelper<tdActionItemForRS> mActionHelper = tdActionHelper<tdActionItemForRS>.Instance();

        public frmSetting()
        {
            InitializeComponent();
            this.Size = new Size(620, 500);
            this.pnlChkSetting.HorizontalScroll.Enabled = false;
            this.pnlChkSetting.VerticalScroll.Enabled = false;
        }

        //public override int skStatusBarHeight => 0;
        public override skSplit skTransparentImageSplit => new skSplit(15);
        //public override int skTitleBarHeight => 30;

        private List<string> TrustedUid = new List<string>();
        private List<Control> TrustedPanel = new List<Control>();
        private List<Control> TrustedDevice = new List<Control>();
        private skTimer mGetSleepTimer = null;

        private void InitPanelLeft()
        {
            //this.pnlNode.Location = new Point(this.Margin.Left, this.Margin.Top + this.skTitleBarHeight);
            this.pnlNode.Location = new Point(this.Margin.Left, this.Margin.Top);
            this.pnlNode.Size = new Size(MyDpi.ToDPI(140, this.Dpi), this.Height - this.Margin.Vertical - this.skStatusBarHeight);
            this.pnlNode.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left;
            this.pnlNode.skBackgroundColor = Color.FromArgb(247, 248, 250);
            //this.pnlNode.skBorderType = skBorderType.Right;
            //this.pnlNode.skBorderStrokeColor = Color.FromArgb(247, 248, 250);
            //this.pnlNode.skBorderStrokeColor = Color.Red;
            //this.pnlNode.Visible = false;
        }

        private void InitPanelRight()
        {
            this.InitPanelRightHandel(this.pnlGeneral);
            this.pnlSettingSplite.Location = new Point(this.pnlSettingSplite.Left, this.lblGeneral.Bottom + MyDpi.ToDPI(10, this.Dpi));
            this.pnlSettingGeneral.Location = new Point(this.pnlSettingGeneral.Left, this.pnlSettingSplite.Bottom + MyDpi.ToDPI(12, this.Dpi));
            this.pnlChkSetting.Location = new Point(this.pnlSettingGeneral.Left, this.pnlSettingGeneral.Bottom + MyDpi.ToDPI(5, this.Dpi));
            this.pnlChkSetting.HorizontalScroll.Maximum = 0;
            this.pnlChkSetting.AutoScroll = false;
            this.chkAutoChangeLinkCode.Size = new Size(this.pnlChkSetting.Width, this.chkAutoChangeLinkCode.Size.Height);
            this.chkLockDevice.Size = new Size(this.pnlChkSetting.Width, this.chkLockDevice.Size.Height);
            this.chkSafe.Size = new Size(this.pnlChkSetting.Width, this.chkSafe.Size.Height);

            this.pnlGeneral.Controls.Remove(this.pnlSettingGeneral);
            this.pnlGeneral.Controls.Remove(this.pnlChkSetting);

            this.pnlSettingByScroll.AddControl(this.pnlSettingGeneral);
            this.pnlSettingByScroll.AddControl(this.pnlChkSetting);

            this.InitPanelRightHandel(this.pnlAboutUs);
            this.pnlAboutSplite.Location = new Point(this.pnlAboutSplite.Left, this.lblAboutus.Bottom + MyDpi.ToDPI(10, this.Dpi));
            this.lblAppDetail.Size = new Size(this.pnlHelpCenter.Width - this.lblAppDetail.Margin.Left - this.lblAppDetail.Margin.Right, this.lblAppDetail.Height);

        }

        private void InitPanelRightHandel(skPanel pnl)
        {
            pnl.Location = new Point(this.pnlNode.Right, this.Margin.Top + this.skTitleBarHeight);
            pnl.Size = new Size(this.Width - this.Margin.Right - this.pnlNode.Right, this.pnlNode.Height - this.Margin.Top - this.skTitleBarHeight);
            pnl.Anchor = AnchorStyles.Left | AnchorStyles.Top;
            pnl.skBorderType = skBorderType.None;
            pnl.skBorderStrokeColor = Color.White;
            pnl.skBackgroundColor = Color.White;

            //pnl.Visible = false;
        }

        private void InitButton()
        {

        }

        public override void SetSkin()
        {
            try
            {
                this.btnClose.skIcon = MyResource.GetImage("ic_close.png");
                this.btnMax.skIcon = MyResource.GetImage("ic_chromemaximize.png");
                this.btnMin.skIcon = MyResource.GetImage("ic_chromeminimize.png");
                this.btnClose.skIconState = this.btnMax.skIconState = this.btnMin.skIconState = skImageState.FourState;
                this.btnClose.skIconSize = this.btnMax.skIconSize = this.btnMin.skIconSize = new Size(32, 22);
                this.skTransparentImagePadding = this.skTransparentImagePadding;
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "frmSetting.SetSkin");
            }
        }

        protected override void InitButtonLocation()
        {
            base.InitButtonLocation();

            if (this.btnMin != null)
                this.btnMin.Location = new Point(this.btnMin.Location.X - MyDpi.ToDPI(13, this.Dpi), this.btnMin.Location.Y);
        }

        protected override void InitControls()
        {
            base.InitControls();

            this.pnlChkSetting.HorizontalScroll.Enabled = false;

            this.Icon = MyResource.GetIcon("airdroid.ico");
            this.skIcon = MyResource.GetImage("airdroid_48.png");

            //this.btnClose.skBackgroundImage = MyResource.GetImage("btn_close_3.png");
            //this.btnClose.skBackgroundImageState = skImageState.ThreeState;
            //this.btnClose.skShowIcon = false;
            //this.btnMin.skBackgroundImage = MyResource.GetImage("btn_min_3.png");
            //this.btnMin.skBackgroundImageState = skImageState.ThreeState;
            //this.btnMin.skShowIcon = false;
            this.skShowButtonMin = true;

            this.skShowStatusBar = true;
            this.skBorderStrokeColor = Color.White;
            this.skStatusBarBackgroundColor = this.skBorderStrokeColor;
            this.skTitleBackgroundColor = this.skBorderStrokeColor;

            this.skTitleBarHeight = 30;
            this.skStatusBarHeight = 0;

            this.InitPanelLeft();
            this.InitPanelRight();
            this.InitButton();

            //语言界面
            string currentLangKey = this.Language.CurrentLanguage.LangName;
            currentLangKey = SettingMgr.GetValue<string>(KeySession.Setting, KeySession.Language, currentLangKey);

            this.cboLanguage.skIconArrowDown = MyResource.GetImage("ic_arrow_down.png");
            this.cboLanguage.skIconArrowUp = MyResource.GetImage("ic_arrow_up.png");
            this.cboLanguage.skMenuIconSelected = MyResource.GetImage("ic_selected.png");
            this.cboLanguage.skIconMore = MyResource.GetImage("ic_arrow_down.png");

            this.cboLanguage.Items.Clear();
            for (int i = 0; i < this.Language.Languages.Count; i++)
            {
                this.cboLanguage.Items.Add(this.Language.Languages[i].LangDisplayName);

                if (string.Compare(this.Language.Languages[i].LangName, currentLangKey, true) == 0)
                {
                    this.cboLanguage.SelectText = this.Language.Languages[i].LangDisplayName;
                    MainForm.CurrentLangDisplayName = this.cboLanguage.SelectText;
                }
            }

            this.cboPwdUpdate.skIconArrowDown = MyResource.GetImage("ic_arrow_down.png");
            this.cboPwdUpdate.skIconArrowUp = MyResource.GetImage("ic_arrow_up.png");
            this.cboPwdUpdate.skMenuIconSelected = MyResource.GetImage("ic_selected.png");
            this.cboPwdUpdate.skIconMore = MyResource.GetImage("ic_arrow_down.png");

            this.InitEvent();
            this.InitLangage();
            this.LoadSetting();
            this.SetPanelVisible(this.pnlGeneral);//默认显示
            this.SetButtonUpdateVisible();

            #region 关于页面
            this.lblAboutus.skAutoSize = true;
            this.lblWebsiteValue.skAutoSize = true;
            this.lblPrivacyPolicy.skAutoSize = true;
            #endregion

            this.lblAppName.skText = this.Language.GetString("pc_rs_intro_title");

            this.chkAutoChangeLinkCode.skAutoHeight = true;
            //bool CanUpdate = SettingMgr.GetValue<bool>(KeySession.Setting, KeyName.CanUpdate, true, SettingMgr.ProgramIniFile);
            //if (!CanUpdate)
            //{
            //    this.rdoFAQs.Visible = false;
            //    this.rdoAboutus.Location = this.rdoFAQs.Location;
            //}

            //this.skTitleBackgroundColor = Color.White;

            MyForm.SetParentCenter(this, MyForm.GetMainForm());

            SocketMgr.RsCallback += OnRsController_Callback;

            //this.mGetSleepTimer = TimerMgr.Create(4, () =>
            //{
            //    SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.SleepTimer));
            //});

            SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.SleepTimer));

            if (OSHelper.OSVersion.Build < (int)OSBuild.Win10_20H1)
            {
                //Win10 20H1(2004)之前版本不支持开启安全模式
                this.chkSafe.skReadOnly = true;
                this.chkSafe.skTextColor = Color.FromArgb(164, 168, 179);
                return;
            }
            this.btnFeedback.Location = new Point(this.btnFeedback.Location.X, this.pnlNode.Height - this.btnFeedback.Height);
        }

        protected override void OnDpiChanged()
        {
#if DPI
            base.OnDpiChanged();

            this.InitPanelLeft();
            this.InitPanelRight();
            this.InitButton();
            this.InitSettingGeneralSize();
            this.btnFeedback.Location = new Point(this.btnFeedback.Location.X, this.pnlNode.Height - this.btnFeedback.Height);
            this.SetRadius();
#endif
        }

        private void InitSettingGeneralSize()
        {
            if (RsAPI.DeviceDeployInfo != null)
                this.pnlChkSetting.Size = new Size(this.pnlChkSetting.Size.Width, this.btnUnBind.Bottom + MyDpi.ToDPI(25, this.Dpi));
            else
                this.pnlChkSetting.Size = new Size(this.pnlChkSetting.Size.Width, this.btnChangeSleepSetting.Bottom + MyDpi.ToDPI(25, this.Dpi));

            this.pnlSettingByScroll.InitScroll();
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            this.SetPanelVisible(this.pnlGeneral);
        }

        private void OnRsController_Callback(object sender, RSEvent e)
        {
            switch (e.EventType)
            {
                case RSEventType.DeviceAuth:
                    this.SocketMsgHandle();
                    break;

                case RSEventType.SleepTimer:
                    this.SleepTimerHandle(e.EventResult);
                    break;
            }
        }

        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);

            if (this.pnlHelpCenter != null && this.pnlHelpCenter.Visible)
            {
                this.lblAppDetail.Size = new Size(this.pnlHelpCenter.Size.Width - this.lblAppDetail.Margin.Left - this.lblAppDetail.Margin.Right, this.lblAppDetail.Height);
            }

            if (this.pnlChkSetting != null && this.pnlChkSetting.Visible)
            {
                this.chkAutoChangeLinkCode.Size = new Size(this.pnlChkSetting.Width, this.chkAutoChangeLinkCode.Size.Height);
                this.chkLockDevice.Size = new Size(this.pnlChkSetting.Width, this.chkLockDevice.Size.Height);
                this.chkSafe.Size = new Size(this.pnlChkSetting.Width, this.chkSafe.Size.Height);
            }
        }

        protected override void BeforeFormClose(ref bool pblnCancelClose)
        {
            base.BeforeFormClose(ref pblnCancelClose);

            SocketMgr.RsCallback -= OnRsController_Callback;

            this.btnSave_Click(null, null);
        }

        private void InitEvent()
        {
            this.lblWebsiteValue.Click += this.lblLinkUrl_Click;

            this.rdoGeneral.Click += this.OnShowPanel;

            this.rdoAboutus.Click += this.OnShowPanel;
        }

        private void OnShowPanel(object sender, EventArgs e)
        {
            Control pnl = null;

            if (sender == this.rdoGeneral)
                pnl = this.pnlGeneral;

            else if (sender == this.rdoAboutus)
            {
                pnl = this.pnlAboutUs;

                this.SetAboutusTip(false);
            }


            this.SetPanelVisible(pnl);
        }

        private void InitLangage()
        {
            this.skTitle = this.Language.GetString("Setting.Label.Settings");

            this.rdoGeneral.skText = string.Format(" {0}", this.Language.GetString("Setting.Button.Set"));//通用设置
            this.lblGeneral.skText = this.Language.GetString("Setting.Button.Set");//通用设置
            this.lblPwdUpdata.skText = string.Format("{0}", this.Language.GetString("rs_temporary_password_update"));//临时密码更新
            this.lblBasicSetting.skText = string.Format("{0}", this.Language.GetString("aircast_tv_security_settings"));//基础设置

            this.cboPwdUpdate.Items.Clear();
            this.cboPwdUpdate.Items.Add(string.Format("{0}", this.Language.GetString("rs_daily_update")));//每日更新
            this.cboPwdUpdate.Items.Add(string.Format("{0}", this.Language.GetString("rs_manual_update")));//手动更新
            this.cboPwdUpdate.Items.Add(string.Format("{0}", this.Language.GetString("rs_update_after_controlled")));//每次连接后更新
            this.cboPwdUpdate.SelectedIndexChanged += cboPwdUpdate_SeletIndexChanged;

            this.chkAutoChangeLinkCode.skText = this.Language.GetString("rs_auto_update_deploy_code_hint");              //每次远程协助服务结束后，自动变换9位连接码
            this.chkLockDevice.skText = this.Language.GetString("rs_automatically_lock_after_controll");              //远控结束后自动锁定本机
            this.chkSafe.skText = this.Language.GetString("Setting.CheckBox.Safe");              //启用安全模式（本机开启无人值守后，连接时自动启用安全模式）
            this.lblExitCompany.skText = string.Format("{0}", this.Language.GetString("rs_unenroll_organization"));//退出企业
            this.lblNoCompany.skText = string.Format("{0}", this.Language.GetString("rs_device_not_enrolled_organization"));//此设备未加入到企业
            this.btnUnBind.skText = string.Format("{0}", this.Language.GetString("rs_undeploy_dialog_title"));//解绑设备

            //this.rdoFileStorage.skText = string.Format(" {0}", this.Language.GetString("Setting.Lable.FileStorage"));//截屏录屏设置
            //this.lblFileStorage.skText = this.Language.GetString("Setting.Lable.FileStorage");//截屏录屏设置
            //this.lblScreenshotPath.skText = this.Language.GetString("Setting.Lable.ScreenshotPath");         //保存路径
            //this.btnChange.skText = "更改";                        //更改
            //this.btnOpenFolder.skText = this.Language.GetString("Button.OpenFolder");                //打开文件夹


            this.rdoAboutus.skText = string.Format(" {0}", this.Language.GetString("Setting.Button.About"));//关于我们
            this.lblAboutus.skText = this.Language.GetString("Setting.Button.About");//关于我们
            this.lblAbout.skText = this.Language.GetString("Setting.Button.About");//关于我们

            this.lblHelpCenter.skText = this.Language.GetString("feedback.help");//帮助中心
            this.btnHelp.skText = this.Language.GetString("Common.Click");//点击进入
            this.lblRenewal.skText = this.Language.GetString("common_update");//更新
            this.lblCurrentVersion.skText = string.Format("{0} {1}", this.Language.GetString("App.Column.CurrentVersion"), Common.GetSoftVersion());         //当前版本
            //this.lblNewestVersion.skText = this.Language.GetString("Setting.Lable.NewestVersion");         //最新版本
            //this.lblCurrentVersionValue.skText = string.Format("v{0}", Common.GetSoftVersion());
            this.lblWebsiteValue.skText = this.Language.GetString("About.Label.OfficialWebsite");                       //官网
            this.lblPrivacyPolicy.skText = this.Language.GetString("About.Label.PrivacyPolicy");                       //隐私条款
            this.btnUpdate.skText = this.Language.GetString("MainMenu_Update");                        //检查更新
            this.lblAppName.skText = this.Language.GetString("rs_for_windows");
            this.lblAppDetail.skText = this.Language.GetString("rs_security_reliability");
            this.lblCopyright.skText = this.Language.GetString("Common_copyright_info");

            this.btnFeedback.skText = this.Language.GetString("Business_Feedback_logs_title");
            //this.lblCurrentVersionValue.Location = new Point(this.lblCurrentVersion.Right, this.lblCurrentVersion.Top);
            //this.lblWebsiteValue.Location = new Point(this.lblWebsite.Right, this.lblWebsiteValue.Top);

            this.lblLanguage.skText = this.Language.GetString("rs_pc_language");//界面语言

            this.lblStartSetting.skText = this.Language.GetString("app_launch_settings");// 应用启动设置
            this.chkStartLaunch.skText = this.Language.GetString("auto_start_on_boot");// 开机自启动
            this.lblNoStartSetting.skText = this.Language.GetString("turn_on_AirDroid_Remote_Support_on_boot");// 计算机开机时自动开启 AirDroid Remote Support，以便连接到设备
            this.lblSleepSetting.skText = this.Language.GetString("change_sleep_settings");// 更改睡眠设置
            this.lblNoSleepSetting.skText = this.Language.GetString("set_never_sleep_for_remote_access");// 设置计算机从不进入睡眠，以便您可以在任何时间远程访问
            this.btnChangeSleepSetting.skText = this.Language.GetString("change_sleep_settings");// 更改睡眠设置
            this.lblSleepTimerTip.skText = this.Language.GetString("disable_sleep_in_power_management_settings"); // 当前系统睡眠功能处于开启模式，可能会导致无法连接无人值守设备，请前往电源管理禁用睡眠
            this.pnlSleepTimerToolTip.Size = new Size(this.lblSleepTimerTip.Size.Width + 16, this.lblSleepTimerTip.Size.Height + 16);
            this.CheckBoxIconOffsetHandle(this.chkAutoChangeLinkCode);
            this.CheckBoxIconOffsetHandle(this.chkLockDevice);
            this.CheckBoxIconOffsetHandle(this.chkStartLaunch);
        }

        private void CheckBoxIconOffsetHandle(skCheckBox control)
        {
            if (control.Height > 20)
                control.skIconOffset = new Point(0, 1);
            else
                control.skIconOffset = new Point(0, 2);
        }

        private void cboPwdUpdate_SeletIndexChanged(object sender, EventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(() =>
                {
                    this.cboPwdUpdate_SeletIndexChanged(sender, e);
                }));
            }
            else
            {
                int pwdUpdateIndex = this.cboPwdUpdate.SelectIndex;

                rsPwdType pwdType = (rsPwdType)pwdUpdateIndex;
                if (RsAPI.LocalSetting.PwdType != pwdType)
                {
                    rsLocalSettingInfo setting = RsAPI.LocalSetting;
                    setting.PwdType = pwdType;
                    RsAPI.SaveLocalSetting(setting);

                    if (NoticePasswordUpdateEvent != null) // 将选择的密码更新方式传递出去
                        NoticePasswordUpdateEvent();
                }
            }
        }

        private void SetPanelVisible(Control pnl)
        {
            if (pnl == null)
                return;

            pnl.BringToFront();
            this.pnlSettingByScroll.Focus();
        }

        private void LoadSetting()
        {
            try
            {
                string currentLangKey = this.Language.CurrentLanguage.LangName;
                currentLangKey = SettingMgr.GetValue<string>(KeySession.Setting, KeySession.Language, currentLangKey);
                foreach (LangInfo info in this.Language.Languages)
                {
                    if (string.Compare(info.LangName, currentLangKey, true) == 0)
                    {
                        this.cboLanguage.SelectText = info.LangDisplayName;
                        break;
                    }
                }

            }
            catch (Exception)
            {

            }

            if (string.IsNullOrEmpty(this.cboLanguage.SelectText))
            {
                //出现异常情况使用默认值进去
                SettingMgr.SetValue(KeySession.Language, "en-US");
                System.Globalization.CultureInfo cultureInfo = new System.Globalization.CultureInfo("en-US");
                this.cboLanguage.SelectText = cultureInfo.NativeName;
                MainForm.CurrentLangDisplayName = cultureInfo.NativeName;
            }

            int selectIndex = (int)RsAPI.LocalSetting.PwdType;

            if (selectIndex == -1)
            {
                selectIndex = (int)rsPwdType.UpdateAfterDisconnect;
            }

            this.cboPwdUpdate.SelectIndex = selectIndex;

            if (ServiceMgr.GetServiceStart(ServiceMgr.ServiceNameForRS) == StartType.auto)
                this.chkStartLaunch.skChecked = true;
            else
                this.chkStartLaunch.skChecked = false;

            this.btnCurrentSleepTip.skText = this.Language.GetString("currently_set_to_disable_sleep");
            this.DeployHandle();
        }

        public void DeployHandle()
        {
            this.chkLockDevice.skChecked = RsAPI.LocalSetting.lock_device_after_disconnect;
            this.chkSafe.skChecked = RsAPI.LocalSetting.display_in_safe_mode;

            this.btnUnBind.Visible = RsAPI.DeviceDeployInfo != null;
            this.lblNoCompany.Visible = false;
            this.lblExitCompany.Visible = this.btnUnBind.Visible;

            this.SocketMsgHandle();

            this.InitSettingGeneralSize();
        }

        public void SocketMsgHandle()
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new ThreadStart(SocketMsgHandle));
                return;
            }

            this.chkAutoChangeLinkCode.skReadOnly = false;

            MyLog.Log(string.Format("attended_auto_update_share_code = {0}", RsAPI.DeviceAuthInfo == null ? "" : ((int)RsAPI.DeviceAuthInfo.attended_auto_update_share_code).ToString()), "Setting");

            if (RsAPI.DeviceDeployInfo != null && RsAPI.DeviceAuthInfo != null && RsAPI.DeviceDeployInfo.device_type == skDeviceType.RS_Man_Win && RsAPI.DeviceAuthInfo.attended_auto_update_share_code == rsDeviceActionStatus.Open)
            {
                this.chkAutoChangeLinkCode.skChecked = true;
                this.chkAutoChangeLinkCode.skReadOnly = true;
            }
            else
            {
                this.chkAutoChangeLinkCode.skReadOnly = false;
                this.chkAutoChangeLinkCode.skChecked = SettingMgr.GetValue<bool>(KeyNameForRS.ChangeLinkCode, false);
            }
        }

        private void lblLinkUrl_Click(object sender, EventArgs e)
        {
            skButton btn = sender as skButton;
            if (btn == null)
                return;

            string lang = this.Language.CurrentLanguage.LangName;

            if (lang.ToLower() == "es-es")
            {
                lang = "es";
            }

            Common.OpenUrl(string.Format("{0}/{1}", btn.skText, lang));
            //Common.OpenUrl(btn.skText);
        }

        private void btnUpdate_Click(object sender, EventArgs e)
        {
            //if (!Common.NetworkIsAvailable())
            //{
            //    if (this.Language == null)
            //        this.Language = LanguageInterface.Instance();

            //    skMsgInfoNew skMsgInfo = RsCommon.GetDefaultMsgInfo(this.Language.GetString("Main.Message.NetworkDisconnectCanNotUpgrade"), this.Language.GetString("pc_rs_intro_title"), this.Language);//网络不通，无法完成升级，请检查您的网络
            //    MsgBoxMgr.Show(this, skMsgInfo);
            //    return;
            //}

            MyUpdate.CheckUpdate(true, false, this.Language.CurrentLanguage.LangName);
        }

        //private void CheckLanguage()
        //{
        //    if (this.cmbLanguage.Text == ProcForm.CurrentLangDisplayName)
        //        return;

        //    foreach (LangInfo info in this.Language.Languages)
        //    {
        //        if (info.LangDisplayName != this.cmbLanguage.Text)
        //            continue;

        //        ProcForm.CurrentLangDisplayName = info.LangDisplayName;
        //        SettingMgr.SetValue(KeyName.Language, info.LangName);
        //    }

        //    if (DialogResult.Yes == skMessageBox.Show(this, this.Language.GetString("Setting.Message.RestarApp"), this.Language.GetString("Parent.Tabbar.Device.Notice"), MessageBoxButtons.YesNo))
        //    {
        //        ProcForm.RestartApp();
        //    }
        //}

        private void btnSave_Click(object sender, EventArgs e)
        {
            //if (!(RsAPI.DeviceDeployInfo != null && RsAPI.DeviceAuthInfo.attended_auto_update_share_code == skDeviceActionStatus.Open))
            //{
            //    SettingMgr.SetValue(KeyName.ChangeLinkCode, this.chkAutoChangeLinkCode.skChecked);
            //}

            //SettingMgr.SetValue(KeyName.LockDevice, this.chkLockDevice.skChecked);
            //SettingMgr.SetValue(KeyName.SafeMode, this.chkSafe.skChecked);
            //SettingMgr.SetValue(KeyName.PwdUpdateType, this.cmbPwdUpdata.SelectedIndex);

            TimerMgr.Stop(this.mGetSleepTimer);

            RsAPI.LoadLocalSetting();

            rsLocalSettingInfo setting = RsAPI.LocalSetting;

            if (setting.lock_device_after_disconnect != this.chkLockDevice.skChecked)
            {
                setting.lock_device_after_disconnect = this.chkLockDevice.skChecked;
                this.mActionHelper.Add(setting.lock_device_after_disconnect ? tbActionModeKeyForRS.SettingLockComputerAfterSelect : tbActionModeKeyForRS.SettingLockComputerAfterDisselect);
            }

            setting.display_in_safe_mode = this.chkSafe.skChecked;

            //int pwdUpdateIndex = 0;
            //for (int i = 0; i < this.cmbPwdUpdate.skDataList.Count; i++)
            //    if (string.Equals(this.cmbPwdUpdate.skDataList[i], this.cmbPwdUpdate.skBtn.skText))
            //    {
            //        pwdUpdateIndex = i;
            //        break;
            //    }

            //if (setting.PwdType != (rsPwdType)pwdUpdateIndex)
            //{
            //    setting.PwdType = (rsPwdType)pwdUpdateIndex;
            //    switch (setting.PwdType)
            //    {
            //        case rsPwdType.UpdateByMan:
            //            this.mActionHelper.Add(tbActionModeKeyForRS.SettingUpdatePwdByMan);
            //            break;
            //        case rsPwdType.UpdateByDay:
            //            this.mActionHelper.Add(tbActionModeKeyForRS.SettingUpdatePwdByDay);
            //            break;
            //        case rsPwdType.UpdateAfterDisconnect:
            //            this.mActionHelper.Add(tbActionModeKeyForRS.SettingUpdatePwdAfterDisconnect);
            //            break;
            //    }
            //}
            //setting.Pwd = string.Empty;//TODO用户主动设置密码

            if (!(RsAPI.DeviceDeployInfo != null && RsAPI.DeviceAuthInfo != null && RsAPI.DeviceDeployInfo.device_type == skDeviceType.RS_Man_Win && RsAPI.DeviceAuthInfo.attended_auto_update_share_code == rsDeviceActionStatus.Open))
            {
                if (SettingMgr.GetValue<bool>(KeyNameForRS.ChangeLinkCode, false) != this.chkAutoChangeLinkCode.skChecked)
                    this.mActionHelper.Add(this.chkAutoChangeLinkCode.skChecked ? tbActionModeKeyForRS.SettingChangeLinkCodeSelect : tbActionModeKeyForRS.SettingChangeLinkCodeDisselect);

                SettingMgr.SetValue(KeyNameForRS.ChangeLinkCode, this.chkAutoChangeLinkCode.skChecked);
            }

            //保存本地设置更新
            RsAPI.SaveLocalSetting(setting);

            //发送本地设置更新
            SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.LocalSettingUpdate, MyJson.SerializeToJsonString(RsAPI.LocalSetting)));

            this.CheckLanguage();

            if (this.chkStartLaunch.skChecked)
            {
                //MySystem.AutoStartApp(true, ParaMgr.CommandAutoRun);
                ServiceMgr.ServiceStartType(ServiceMgr.ServiceNameForRS);
                ServiceMgr.ServiceStartType(ServiceMgr.ServiceNameForRSMonitor, ServiceMgr.ServicePathForMonitor);
            }
            else
            {
                //MySystem.AutoStartApp(false);
                ServiceMgr.ServiceStartType(ServiceMgr.ServiceNameForRS, startType: StartType.demand);
                ServiceMgr.ServiceStartType(ServiceMgr.ServiceNameForRSMonitor, ServiceMgr.ServicePathForMonitor, startType: StartType.demand);
            }
        }

        private void CheckLanguage()
        {
            if (this.cboLanguage.SelectText == MainForm.CurrentLangDisplayName /*|| string.IsNullOrEmpty(MainForm.CurrentLangDisplayName)*/)
                return;

            foreach (LangInfo info in this.Language.Languages)
            {
                if (info.LangDisplayName != this.cboLanguage.SelectText)
                    continue;

                MainForm.CurrentLangDisplayName = info.LangDisplayName;
                SettingMgr.SetValue(KeySession.Language, info.LangName);
            }

            //skMsgInfoNew skMsgInfo = null;

            //if (((MainForm)MyForm.GetMainForm()).IsConnected)
            //{
            //    skMsgInfo = RsCommon.GetDefaultMsgInfo(string.Format(this.Language.GetString("Common_Business_Control_Disconnect"), RsAPI.LocalDevice.name), this.Language.GetString("Common.Info"), this.Language);
            //    skMsgInfo.FirstButton.ButtonText = this.Language.GetString("Common_Confirm");
            //    if (MsgBoxMgr.Show(null, skMsgInfo) != DialogResult.OK)
            //    {
            //        return;
            //    }
            //    else
            //    {
            //        foreach (Form form in Application.OpenForms)
            //        {
            //            if (form is frmConnect connect)
            //            {
            //                RSEvent arg = new RSEvent();
            //                arg.EventType = RSEventType.Disconnect;
            //                arg.TargetDevice = connect.TargetDevice;
            //                SocketMgr.SendMsgFromClient(arg);
            //                break;
            //            }
            //        }
            //    }
            //}
            //else
            //    goto DoChecked;

            //DoChecked:
            skMsgInfoNew skMsgInfo = RsCommon.GetDefaultMsgInfo(this.Language.GetString("Setting.Message.RestarApp"), this.Language.GetString("pc_rs_intro_title"), this.Language);
            skMsgInfo.ButtonPadding = new Padding(10, 2, 10, 0);
            if (MsgBoxMgr.Show(this, skMsgInfo) == DialogResult.OK)
            {
                Process[] pros = Process.GetProcessesByName("Helper");
                if (pros != null && pros.Length > 0)
                {
                    foreach (Process process in pros)
                    {
                        process.Kill();
                        process.Dispose();
                    }
                }

                try
                {
                    MainForm form = MyForm.GetMainForm() as MainForm;
                    //form.BeformExitHandle();
                    SettingMgr.SetValue(KeyNameForRS.RDPSessionId, MyProcesss.System_GetCurrentSession(true));
                }
                catch (Exception)
                {
                    MyLog.LogException("MainForm is no find");
                }

                SocketMgr.SendMsgFromClient(RSEvent.Create(RSEventType.ReStart));

                Thread.Sleep(1000);

                SocketMgr.KillProcessById(SocketMgr.GetAppId(SocketType.UserDisplay), isKillWindows: true);
                //Application.Restart();
            }
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            base.OnFormClosing(e);
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            //SettingCache.Reset();

            this.LoadSetting();
        }

        //public void SetUpdateTip(bool blnVisible)
        //{
        //    this.SetButtonUpdateVisible();

        //    this.SetAboutusTip(blnVisible);

        //    bool showNewestVersion = !string.IsNullOrEmpty(MyUpdate.NewestVersion);

        //    this.lblNewestVersion.Visible = showNewestVersion;
        //    this.lblNewestVersionValue.Visible = showNewestVersion;
        //    this.lblNewestVersionValue.skText = string.Format("v{0}", MyUpdate.NewestVersion);
        //    this.lblNewestVersionValue.ForeColor = Color.Red;
        //    this.lblNewestVersionValue.Left = this.lblCurrentVersionValue.Left;
        //    this.btnUpdate.skText = this.Language.GetString(MyUpdate.IsNewestVersion ? "MainMenu_Update" : "Update.Button.UpdateNow");
        //}

        public void SetAboutusTip(bool showRedDot)
        {
            this.rdoAboutus.skShowRedDot = showRedDot;
        }

        public void SetButtonUpdateVisible()
        {
            //this.btnUpdate.Visible = !MyUpdate.IsNewestVersion;
        }

        public override void LoadData(params object[] args)
        {
            base.LoadData(args);

            this.LoadSetting();

            //if (args.Length == 1)
            //    this.SetUpdateTip((bool)args[0]);
            //else
            //    this.SetUpdateTip(!MyUpdate.IsNewestVersion);
        }

        private void lblGeneral_DoubleClick(object sender, EventArgs e)
        {

        }

        private void btnUnBind_Click(object sender, EventArgs e)
        {
            this.mActionHelper.Add(tbActionModeKeyForRS.SettingUndeploy);

            if (!Common.NetworkIsAvailable())
            {
                RsCommon.ShowSplashBox(this, this.Language.GetString("Common_check_network"));
                return;
            }

            string msgTitle = this.Language.GetString("rs_sure_unenroll_organization");
            string msgContent = string.Empty;
            bool showInputPwd = false;

            if (RsAPI.IsNoMan() && RsAPI.DeviceAuthInfo.close_unattended_daemon_by_pwd == rsDeviceActionStatus.Open)
                showInputPwd = true;


            if (showInputPwd)
            {
                msgContent = this.Language.GetString("rs_enter_password_no_longer_accessible");
            }
            else
            {
                msgContent = this.Language.GetString("rs_controller_not_control_unenrollment");
            }

            skMsgInfoNew skMsgInfo = RsCommon.GetDefaultMsgInfo(msgContent, msgTitle, this.Language);
            skMsgInfo.ButtonPadding = new Padding(10, 2, 10, 0);
            if (showInputPwd)
            {
                skMsgInfo.InputBoxInfo.Visible = true;
                skMsgInfo.InputBoxInfo.Padding = new Padding(2, 8, 0, 24);
                skMsgInfo.MessageInfo.Padding = new Padding(0, 0, 0, 6);

                skMsgInfo = RsCommon.SetClearMsgInfo(skMsgInfo, Properties.Resources.btn_clear_4);

                skMsgInfo = RsCommon.SetPasswordMsgInfo(skMsgInfo, Properties.Resources.btn_main_hide_4, Properties.Resources.btn_main_show_4);
            }

            skMsgInfo.EventCallBack = () =>
            {
                try
                {
                    this.mActionHelper.Add(tbActionModeKeyForRS.SettingUndeployOK);

                    if (showInputPwd && string.IsNullOrEmpty(skMsgInfo.InputBoxInfo.skText))
                    {
                        RsCommon.SetErrorMsgInfo(skMsgInfo, this.Language.GetString("Login.Label.PasswordEmpty"));
                        return;
                    }

                    RSEventType rsEvent = RsAPI.DeviceUndeploy(RsAPI.DeviceDeployInfo, skMsgInfo.InputBoxInfo.skText);
                    if (rsEvent == RSEventType.DeviceUndeploySuccessed)
                    {
                        ////清空聊天记录
                        //RecordMgr.ClearDB();

                        MsgBoxMgr.Remove(skMsgInfo);

                        RsCommon.ShowSplashBox(this, this.Language.GetString("Device_unenrolled"));

                        this.DeployHandle();
                    }
                    else
                    {
                        if (rsEvent == RSEventType.DeviceTokenExpired)
                        {
                            RsCommon.ShowSplashBox(this, this.Language.GetString("rs_pc_request_failed"));
                            RsCommon.DeviceTokenUpdate();
                            return;
                        }
                        else
                        {
                            if (!Common.NetworkIsAvailable())
                            {
                                RsCommon.ShowSplashBox(this, this.Language.GetString("Common_check_network"));
                                return;
                            }
                        }

                        //skMsgInfo.InputBox.InputBoxNorBorderColor = Color.FromArgb(236, 85, 83);
                        //skMsgInfo.InputBox.InputBoxFocusBorderColor = Color.FromArgb(236, 85, 83);
                        //skMsgInfo.InputBox.InputBoxContentPadding = new Padding(2, 8, 2, 0);

                        //skMsgInfo.MsgErrorTextInfo = new skMsgTextInfo()
                        //{
                        //    MsgTextColor = Color.FromArgb(236, 85, 83),
                        //    MsgTextFont = skMsgInfo.MsgTextInfo.MsgTextFont,
                        //    MsgTextPadding = new Padding(0, 0, 0, 18),
                        //    MsgTextAlignment = ContentAlignment.TopLeft
                        //};

                        if (rsEvent == RSEventType.DeviceUndeployErrorByPara)
                        {
                            RsCommon.SetErrorMsgInfo(skMsgInfo, this.Language.GetString("Profile_NickName_ParameterError"));
                        }
                        else if (rsEvent == RSEventType.DeviceUndeployErrorByPwd || rsEvent == RSEventType.DeviceUndeployError)
                        {
                            RsCommon.SetErrorMsgInfo(skMsgInfo, this.Language.GetString("rs_enter_password_error"));
                        }
                        //MsgBoxMgr.UpdateText(skMsgInfo);
                    }
                }
                catch (Exception ex)
                {
                }
                finally
                {
                    MsgBoxMgr.StopBtnLoading(skMsgInfo);
                }
            };

            MsgBoxMgr.Show(this, skMsgInfo);
        }

        private void btnHelp_Click(object sender, EventArgs e)
        {
            Common.OpenExplorer(MyUrl.Help.Replace("test-", ""));
        }

        private void lblWebsiteValue_Click(object sender, EventArgs e)
        {
            Common.OpenExplorer(string.Format(MyUrl.WebSite, this.Language.CurrentLanguage.LangName.ToLower()));
        }

        private void lblPrivacyPolicy_Click(object sender, EventArgs e)
        {
            Common.OpenExplorer(string.Format(MyUrl.PrivacyPolicy, this.Language.CurrentLanguage.LangName.ToLower()));
        }

        private void btnFeedback_Click(object sender, EventArgs e)
        {
            Form form = MyForm.GetMainForm(typeof(frmFeedback).Name, false);

            if (form == null)
            {
                frmFeedback frmFeedback = new frmFeedback();
                frmFeedback.Owner = this;
                frmFeedback.ShowDialog();
            }
        }

        /// <summary>
        /// 打开系统睡眠设置
        /// </summary>
        private void btnChangeSleepSetting_Click(object sender, EventArgs e)
        {
            try
            {
                if (OSHelper.OverWin10)
                    Process.Start("ms-settings:powersleep");
                else
                    Process.Start("control.exe", "/name Microsoft.PowerOptions");
            }
            catch (Exception ex)
            {
                MyLog.LogException(ex, "OpenSleepSet");
            }
        }

        private void SleepTimerHandle(string timer)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(() =>
                {
                    this.SleepTimerHandle(timer);
                }));
            }
            else
            {
                int sleepTime = Common.GetInteger(timer);
                string strHour = "";
                string unitHour = "";
                string strMinute = "";
                string unitMinute = "";

                if (sleepTime == 0)
                {
                    this.btnWarmTip.Visible = false;
                    this.btnCurrentSleepTip.skText = this.Language.GetString("currently_set_to_disable_sleep"); // 此计算机当前已设置为：禁用睡眠
                }
                else
                {
                    // 计算小时和分钟
                    int hours = sleepTime / 3600;
                    int minutes = (sleepTime % 3600) / 60;

                    if (hours > 0)
                    {
                        strHour = hours.ToString();
                        if (hours > 1)
                            unitHour = this.Language.GetString("common_hours"); // {{0}}小时
                        else
                            unitHour = this.Language.GetString("Common.Hour"); // 1小时
                    }

                    if (minutes > 0)
                    {
                        strMinute = minutes.ToString();
                        if (minutes > 1)
                            unitMinute = this.Language.GetString("Common.Minute"); //mins
                        else
                            unitMinute = this.Language.GetString("Parent_TodayEvent_Usage_Time_Minute"); //min
                    }

                    this.btnWarmTip.Visible = true;
                    this.btnCurrentSleepTip.skText = string.Format(this.Language.GetString("currently_set_to_sleep_after_{0}{1}"), strHour, unitHour, strMinute, unitMinute); // 此计算机当前已设置为：{0}{1}{2}{3}后进入睡眠
                }

                //this.pnlSleepFlowLayout.Size = new Size(this.pnlSleepFlowLayout.Width, this.btnCurrentSleepTip.Height);
            }
        }

        private void btnCurrentSleepTip_MoreHover(object sender, MouseEventArgs e)
        {
            bool down = true;
            Point point = btnWarmTip.PointToScreen(e.Location);
            Point mousePoint = this.PointToClient(point);
            int offsetX = point.X - this.Location.X - this.pnlSleepTimerToolTip.Width / 2;

            if (mousePoint.Y + this.pnlSleepTimerToolTip.Size.Height > this.Height - this.skTitleBarHeight.ToDPI(this.Dpi))
                down = false;

            if (mousePoint.X + this.pnlSleepTimerToolTip.Size.Width > this.Width - this.pnlNode.Width)
                offsetX = point.X - this.Location.X - this.pnlSleepTimerToolTip.Width;

            if (down)
                point = new Point(offsetX, point.Y - this.Location.Y + 16);
            else
                point = new Point(offsetX, point.Y - this.Location.Y - this.pnlSleepTimerToolTip.Size.Height - 16);

            this.pnlSleepTimerToolTip.Location = point;
            this.pnlSleepTimerToolTip.Visible = true;
            this.pnlSleepTimerToolTip.BringToFront();

        }

        private void btnCurrentSleepTip_MoreLeave(object sender, MouseEventArgs e)
        {
            this.pnlSleepTimerToolTip.Visible = false;
        }

        private void lblSleepTimerTip_SizeChanged(object sender, EventArgs e)
        {
            this.pnlSleepTimerToolTip.Size = new Size(this.lblSleepTimerTip.Size.Width + 16.ToDPI(this.Dpi), this.lblSleepTimerTip.Size.Height + 16.ToDPI(this.Dpi));
            //this.pnlSleepTimerToolTip.Size = this.lblSleepTimerTip.Size;
        }
    }

}
