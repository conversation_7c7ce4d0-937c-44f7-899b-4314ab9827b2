﻿using iTong.Android;
using iTong.CoreFoundation;
using iTong.CoreModule;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace AirDroidRsHost
{
    public partial class frmSleepNotice : skForm
    {
        public override skSplit skTransparentImageSplit => new skSplit(15);
        public frmSleepNotice(int timer)
        {
            InitializeComponent();
            this.ShowInTaskbar = false;
            this.SleepTimerHandle(timer.ToString());
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.Size = new Size(420, 242);
            this.skTitleBarHeight = 0;
            this.skBorderRadius = new skBorderRadius(8);
            this.btnComfirm.skBorderRadius = new skBorderRadius(2);

            this.lblTitle.skText = this.Language.GetString("keep_computer_awake");// 保持计算机在唤醒状态
            this.chkNoPrompt.skText = this.Language.GetString("Common.NoHint");// 不再提示
            this.btnComfirm.skText = this.Language.GetString("Common.OK");// 确定
            this.lnkSleepSetting.skText = this.Language.GetString("change_sleep_settings");// 更改睡眠设置

            this.lnkSleepSetting.Click += this.lnkSleepSetting_Click;
            SocketMgr.RsCallback += OnRsController_Callback;

            // 启用原生linklabel代替sklabel超连接试试效果
#if !DEBUG
            this.lnkSleepSetting.Visible = false;
#endif
            this.lnkSleepSettingTwo.skText = this.Language.GetString("change_sleep_settings");// 更改睡眠设置
            this.lnkSleepSettingTwo.Click += this.lnkSleepSetting_Click;
            this.TopMost = true;
        }

        protected override void OnDpiChanged()
        {
#if DPI
            base.OnDpiChanged();
            this.RefreshLocation();
#endif
        }

        private void RefreshLocation()
        {
            this.lnkSleepSettingTwo.Location = new Point(this.lnkSleepSettingTwo.Left, this.lblDescUp.Bottom + MyDpi.ToDPI(20, this.Dpi));
            this.lnkSleepSetting.Top = this.lnkSleepSettingTwo.Top;

            this.chkNoPrompt.Location = new Point(this.chkNoPrompt.Left, this.lnkSleepSettingTwo.Bottom + MyDpi.ToDPI(14, this.Dpi));
            this.btnComfirm.Location = new Point(this.btnComfirm.Left, this.lnkSleepSettingTwo.Bottom + MyDpi.ToDPI(10, this.Dpi));
            this.Size = new Size(this.OriginalSize.Width.ToDPI(this.Dpi), this.btnComfirm.Bottom + MyDpi.ToDPI(24, this.Dpi));
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
        }

        protected override void BeforeFormClose(ref bool pblnCancelClose)
        {
            SocketMgr.RsCallback -= OnRsController_Callback;
            base.BeforeFormClose(ref pblnCancelClose);
        }

        private void OnRsController_Callback(object sender, RSEvent e)
        {
            switch (e.EventType)
            {
                case RSEventType.SleepTimer:
                    this.SleepTimerHandle(e.EventResult);
                    break;
            }
        }
        private void SleepTimerHandle(string timer)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(() =>
                {
                    this.SleepTimerHandle(timer);
                }));
            }
            else
            {
                int sleepTime = int.Parse(timer);
                string strHour = "";
                string unitHour = "";
                string strMinute = "";
                string unitMinute = "";

                this.lblDescUp.skText = string.Empty; // 先清空
                if (sleepTime == 0)
                {
                    this.lblDescUp.skText = string.Format(this.Language.GetString("currently_set_to_disable_sleep")) + string.Format("\n{0}", this.Language.GetString("set_never_sleep_for_remote_access"));// 此计算机当前已设为：禁用睡眠
                }
                else
                {
                    string strTime = string.Empty;
                    // 计算小时和分钟
                    int hours = sleepTime / 3600;
                    int minutes = (sleepTime % 3600) / 60;

                    if (hours > 0)
                    {
                        strHour = hours.ToString();
                        if (hours > 1)
                            unitHour = this.Language.GetString("common_hours"); // {{0}}小时
                        else
                            unitHour = this.Language.GetString("Common.Hour"); // 1小时

                    }

                    if (minutes > 0)
                    {
                        strMinute = minutes.ToString();
                        if (minutes > 1)
                            unitMinute = this.Language.GetString("Common.Minute"); //mins
                        else
                            unitMinute = this.Language.GetString("Parent_TodayEvent_Usage_Time_Minute"); //min
                    }

                    strTime = string.Format(this.Language.GetString("set_to_sleep_after_{0}{1}_hours_idle"), strHour, unitHour, strMinute, unitMinute) + string.Format("\n{0}", this.Language.GetString("set_never_sleep_for_remote_access")); // 此计算机当前已设置为：系统空闲状态{0}{1}{2}{3}后进入睡眠。
                    this.lblDescUp.skText = strTime;

                    string[] tempArr = this.lblDescUp.skText.Split(new string[] { ":", "：", "after" }, StringSplitOptions.None);
                    if (tempArr.Length >= 2)
                    {
                        tempArr[1] = tempArr[1].TrimStart();
                        int num = tempArr[1].IndexOf('\n');
                        this.lblDescUp.AddSpecial(tempArr[1].Substring(0, num), MyFont.CreateFont(this.lblDescUp.skTextFont.FontFamily, this.lblDescUp.skTextFont.Size, true), Color.FromArgb(70, 79, 96), "", true, ContentAlignment.TopLeft);
                    }
                }

                this.RefreshLocation();
            }
        }

        private void lnkSleepSetting_Click(object sender, EventArgs e)
        {
            try
            {
                if (OSHelper.OverWin10)
                    Process.Start("ms-settings:powersleep");
                else
                    Process.Start("control.exe", "/name Microsoft.PowerOptions");
            }
            catch (Exception ex)
            {
                MyLog.LogException(ex, "lnkSleepSetting_LinkClicked");
            }
        }


        private void btnComfirm_Click(object sender, EventArgs e)
        {
            if (this.chkNoPrompt.skChecked)
                SettingMgr.SetValue(KeyNameForRS.ShowSleepTip, false);

            this.Close();
        }
    }
}
