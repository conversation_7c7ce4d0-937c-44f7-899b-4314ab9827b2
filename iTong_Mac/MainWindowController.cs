﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Drawing;
using System.Threading;
using System.Threading.Tasks;
using System.IO;

using CoreGraphics;
using Foundation;
using AppKit;

using iTong.CoreModule;

namespace iTongEx
{
	public partial class MainWindowController : NSWindowController
	{
		#region Constructors

		// Called when created from unmanaged code
		public MainWindowController(IntPtr handle) : base(handle)
		{
			Initialize();
		}

		// Called when created directly from a XIB file
		[Export("initWithCoder:")]
		public MainWindowController(NSCoder coder) : base(coder)
		{
			Initialize();
		}

		// Call to load from the XIB/NIB file
		public MainWindowController() : base("MainWindow")
		{
			Initialize();
		}

		public MainWindowController(string newCreate) 
		{
			Initialize();
		}

		#endregion

		//strongly typed view accessor
		public new MainWindow Window
		{
			get { return (MainWindow)base.Window; }
		}

		// Shared initialization code
		void Initialize()
		{
		}

		public tbMainView CreateMainView()
		{
			tbMainView view = new tbMainView();

			view.SetFrameOrigin(CGPoint.Empty);
			view.AutoresizingMask = NSViewResizingMask.WidthSizable | NSViewResizingMask.HeightSizable;
			view.tbBackgroundColor = NSColor.FromRgb(250, 251, 252);

			view.InitSize();
			view.AutoLayoutToolButton();

			return view;
		}

		private tbMainView mMainView = null;
		public void InitWindow(NSWindow win = null)
		{
			this.ShouldCascadeWindows = false;  //层叠弹出窗口

			this.mMainView = this.CreateMainView();

			if (win != null)
				base.Window = win;

			this.Window.SetFrame(new CGRect(CGPoint.Empty, this.mMainView.Frame.Size), true);
			this.Window.ContentView.Superview.AddSubview(this.mMainView, NSWindowOrderingMode.Below, this.Window.ContentView.Superview.Subviews[0]);

			this.Window.tbTitleHeight = this.mMainView.tbTitleHeight;
			this.Window.Center();
			this.Window.BindingWindowEvent(this.mMainView);
		}

		public override void WindowDidLoad()
		{
			base.WindowDidLoad();

			this.StartListen();
		}

		public void StartListen()
		{
			this.mMainView.StartListen();
			this.mMainView.CheckActiveState(this.Window);
		}

		public override void LoadWindow()
		{
			base.LoadWindow();

			this.InitWindow();
		}

	}
}
