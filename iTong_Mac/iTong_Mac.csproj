<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectTypeGuids>{A3F8F2AB-B479-4A4A-A458-A89E7DC349F1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <ProjectGuid>{5188A72F-2A90-4118-B920-72FEE66930DC}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>iTongEx</RootNamespace>
    <MonoMacResourcePrefix>Resources</MonoMacResourcePrefix>
    <AssemblyName>Tongbu</AssemblyName>
    <ReleaseVersion>1.0.0</ReleaseVersion>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <SynchReleaseVersion>false</SynchReleaseVersion>
    <UseXamMacFullFramework>true</UseXamMacFullFramework>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug</OutputPath>
    <DefineConstants>__MACOS__;__UNIFIED__;DEBUG;MAC;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <Profiling>true</Profiling>
    <UseRefCounting>true</UseRefCounting>
    <UseSGen>true</UseSGen>
    <IncludeMonoRuntime>false</IncludeMonoRuntime>
    <CreatePackage>false</CreatePackage>
    <CodeSigningKey>Developer ID Application: Xiamen Tongbu Networks Co., Ltd. (J4745728M6)</CodeSigningKey>
    <EnableCodeSigning>false</EnableCodeSigning>
    <EnablePackageSigning>false</EnablePackageSigning>
    <PackageSigningKey>Developer ID Installer</PackageSigningKey>
    <I18n>cjk,mideast,other,rare,west</I18n>
    <XamMacArch>x86_64</XamMacArch>
    <PlatformTarget>anycpu</PlatformTarget>
    <AOTMode>None</AOTMode>
    <LinkMode>None</LinkMode>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType></DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\bin\Release</OutputPath>
    <DefineConstants>__MACOS__;__UNIFIED__;MAC;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <Profiling>false</Profiling>
    <UseRefCounting>true</UseRefCounting>
    <UseSGen>true</UseSGen>
    <IncludeMonoRuntime>true</IncludeMonoRuntime>
    <CreatePackage>true</CreatePackage>
    <CodeSigningKey>Developer ID Application</CodeSigningKey>
    <EnableCodeSigning>false</EnableCodeSigning>
    <EnablePackageSigning>false</EnablePackageSigning>
    <PackageSigningKey>Developer ID Installer</PackageSigningKey>
    <AOTMode>None</AOTMode>
    <LinkMode>None</LinkMode>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Main.cs" />
    <Compile Include="MainWindow.cs" />
    <Compile Include="MainWindow.designer.cs">
      <DependentUpon>MainWindow.cs</DependentUpon>
    </Compile>
    <Compile Include="MainWindowController.cs" />
    <Compile Include="MainWindowController.designer.cs">
      <DependentUpon>MainWindowController.cs</DependentUpon>
    </Compile>
    <Compile Include="AppDelegate.cs" />
    <Compile Include="AppDelegate.designer.cs">
      <DependentUpon>AppDelegate.cs</DependentUpon>
    </Compile>    
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Mac\Xamarin.Mac.CSharp.targets" />
  <ItemGroup>
    <Reference Include="System.Core" />
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data" />
    <Reference Include="Xamarin.Mac" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Info.plist" />
  </ItemGroup>
  <ItemGroup>
    <InterfaceDefinition Include="MainMenu.xib" />
    <InterfaceDefinition Include="MainWindow.xib" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="English.lproj\InfoPlist.strings" />
    <Content Include="zh_CN.lproj\InfoPlist.strings" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CoreModuleEx\CoreModule_Mac.csproj">
      <Project>{AD5EBBD5-1A3E-4532-A1B2-F8C10003DFD3}</Project>
      <Name>CoreModule_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\Android\Android_Mac.csproj">
      <Project>{A2CAD0B7-AC6D-4E3B-9E3E-EBD69C4871C1}</Project>
      <Name>Android_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc_Mac.csproj">
      <Project>{3D7568EF-2BED-4B71-BBFA-D2F35BE5BF00}</Project>
      <Name>CoreMisc_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag_Mac.csproj">
      <Project>{338F4272-5C9F-47B6-B100-5634EF0D1DDE}</Project>
      <Name>CoreTag_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUpdate\CoreUpdate_Mac.csproj">
      <Project>{B71186D3-DD3B-405B-9C17-217CE7157E7C}</Project>
      <Name>CoreUpdate_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtil_Mac.csproj">
      <Project>{913145D8-1271-42F3-BB1A-5D7694D6CA3C}</Project>
      <Name>CoreUtil_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone_Mac.csproj">
      <Project>{FB6055F3-5096-4D14-A2FE-78571A23C920}</Project>
      <Name>iPhone_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPodDB\iPodDB_Mac.csproj">
      <Project>{CC003CF1-929B-4198-813A-5218AE1AFEC8}</Project>
      <Name>iPodDB_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf_Mac.csproj">
      <Project>{C8C2D342-F711-47C4-95CB-0AF620DA719B}</Project>
      <Name>ProtoBuf_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\SharpZLib_Mac.csproj">
      <Project>{CBCC1A89-433D-4102-97F5-A68A500920EF}</Project>
      <Name>SharpZLib_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\System.Web\System.Web_Mac.csproj">
      <Project>{C5E45B23-1A73-418C-B1CA-B35D970B8D05}</Project>
      <Name>System.Web_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\zlib\zlib_Mac.csproj">
      <Project>{0E70E2EA-72A9-4406-8954-95D663386663}</Project>
      <Name>zlib_Mac</Name>
    </ProjectReference>
  </ItemGroup>
</Project>