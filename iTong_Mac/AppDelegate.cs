﻿using System;
using System.Collections.Generic;
using System.Linq;

using AppKit;
using Foundation;
using ObjCRuntime;
using CoreGraphics;

using System.ComponentModel;
using System.IO;

using iTong.CoreModule;

namespace iTongEx
{
	//public static class AEKeyword
	//{
	//	public static readonly uint DirectObject = Create("----");
	//	public static readonly uint ErrorNumber = Create("errn");
	//	public static readonly uint ErrorString = Create("errs");
	//	public static readonly uint ProcessSerialNumber = Create("psn ");
	//	public static readonly uint PreDispatch = Create("phac");
	//	public static readonly uint SelectProc = Create("selh");
	//	public static readonly uint AERecorderCount = Create("recr");
	//	public static readonly uint AEVersion = Create("vers");

	//	private static uint Create(string key)
	//	{
	//		return (((uint)key[0]) << 24 |
	//				((uint)key[1]) << 16 |
	//				((uint)key[2]) << 8 |
	//				((uint)key[3]) << 0);
	//	}
	//}

	public partial class AppDelegate : NSApplicationDelegate
	{
		MainWindowController mainWindowController;

		public static AppDelegate Instance
		{
			get
			{
				return NSApplication.SharedApplication.Delegate as AppDelegate;
			}
		}

		public MainWindowController MWController
		{
			get
			{
				return mainWindowController;
			}
		}

		public void ExitApp()
		{
			NSApplication.SharedApplication.Terminate(this);
		}	

		public override void DidFinishLaunching (NSNotification notification)
		{
            //通过WebView的策略控制来实现URL跳转打开

			//NSAppleEventManager.SharedAppleEventManager.SetEventHandler(this, new Selector("handleUrlEvent:"), AEEventClass.Internet, AEEventID.GetUrl);
            //NSAppleEventManager.SharedAppleEventManager.SetEventHandler(this, new Selector("handleGetURLEvent:withReplyEvent:"), AEEventClass.Internet, AEEventID.GetUrl);

			mainWindowController = new MainWindowController();
			mainWindowController.Window.MakeKeyAndOrderFront(this);

            // 设置此属性后，内存大概省0.1M左右
            mainWindowController.Window.PreferredBackingLocation = NSWindowBackingLocation.MainMemory;

            tbMainView.InitMenu();
		}
       

		/// <summary>
		/// 协议：weixin://, tongbu:// 避免WebKit弹出窗体(需要设置info.plist中的URL types的URL Schemes
		/// </summary>
		/// <param name="descriptor">Descriptor.</param>
		/// <param name="replyEvent">Reply event.</param>
		//[Export("handleGetURLEvent:withReplyEvent:")]
		//private void HandleGetURLEvent(NSAppleEventDescriptor descriptor, NSAppleEventDescriptor replyEvent)
		//{
		//	try
		//	{
		//		string urlStr = descriptor.ParamDescriptorForKeyword(AEKeyword.DirectObject).StringValue;
		//		Console.WriteLine("HandleGetURLEvent={0}", urlStr);

		//		// 处理微信weixin://的协议
		//		ChatWebPage.Navigating(urlStr);
		//	}
		//	catch (Exception ex)
		//	{
		//		Console.WriteLine(ex.ToString());
		//	}
		//}

		/// <summary>
		/// 处理airdroid://协议的事件，需要info.plist配合
		/// </summary>
		/// <param name="obj">Object.</param>
		//[Export("handleUrlEvent:")]
		//private void handleUrlEvent(NSObject obj)
		//{

		//	if (obj != null)
		//		Console.WriteLine("handleUrlEvent: " + obj.ToString());
		//}

		// 实现单击窗体的X按钮时，关闭窗体
		public override bool ApplicationShouldTerminateAfterLastWindowClosed(NSApplication sender)
		{
			return true;
		}

		// 实现Dock栏退出时触发
		public override NSApplicationTerminateReply ApplicationShouldTerminate(NSApplication sender)
		{
			//if (AppleWeixin.HaveExited)
			//	return NSApplicationTerminateReply.Cancel;


			//AppleWeixin.ClearTemp();
			//WeixinHelper.UpdateSoftRunTime();

			iTong.CoreFoundation.ThreadMgr.AbortAll();

			return NSApplicationTerminateReply.Now;
		}

	}
}
