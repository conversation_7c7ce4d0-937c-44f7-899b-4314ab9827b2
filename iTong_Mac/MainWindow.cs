﻿using System;

using iTong.CoreModule;

using AppKit;
using CoreGraphics;
using Foundation;

namespace iTongEx
{
	public partial class MainWindow : tbWindow
	{

		public MainWindow (IntPtr handle) : base (handle)
		{
		}

		[Export ("initWithCoder:")]
		public MainWindow (NSCoder coder) : base (coder)
		{
			
		}

		[Export("init")]
		public MainWindow() : base()
        {
			
		}

		protected override void OnWindowWillClose(object sender, EventArgs e)
		{
			foreach (tbWindow frm in MyWindow.OpenWindows)
			{
				if (frm != this)
					frm.Close();
			}

			base.OnWindowWillClose(sender, e);
		}

	}
}
