﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{7652AD3D-1B82-4A8F-981E-EA454872C617}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>iTong.My.MyApplication</StartupObject>
    <RootNamespace>iTong</RootNamespace>
    <AssemblyName>Tongbu</AssemblyName>
    <MyType>WindowsForms</MyType>
    <ApplicationIcon>Resources\iTong.ico</ApplicationIcon>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>2.0</OldToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <DocumentationFile>Tongbu.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=False,IS_ITONG_ZJ=False,IS_ITONG_LITE=True</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <DocumentationFile>Tongbu.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleAssemblies>D:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=False,IS_ITONG_ZJ=False,IS_ITONG_LITE=True</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DocumentationFile>Tongbu.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleAssemblies>D:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=False,IS_ITONG_ZJ=False,IS_ITONG_LITE=True</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <DocumentationFile>Tongbu.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleAssemblies>D:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=False,IS_ITONG_ZJ=False,IS_ITONG_LITE=True</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.49.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>IncludeDlls\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net, Version=3.5.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="iTong" />
    <Import Include="iTong.Android" />
    <Import Include="iTong.Components" />
    <Import Include="iTong.CoreFoundation" />
    <Import Include="iTong.CoreModule" />
    <Import Include="iTong.CoreReses" />
    <Import Include="iTong.Device" />
    <Import Include="iTong.Update" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.IO" />
    <Import Include="System.Net" />
    <Import Include="System.Text" />
    <Import Include="System.Web" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\iTong\ApplicationEvents.vb" />
    <Compile Include="..\iTong\zClass\ProcFormHelper.vb">
      <Link>zClass\ProcFormHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceClass\AutoInstallHelper.vb">
      <Link>zDeviceClass\AutoInstallHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\frmSplash.Designer.vb">
      <DependentUpon>frmSplash.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\frmSplash.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zLogin\frmLogin.Designer.vb">
      <Link>zLogin\frmLogin.Designer.vb</Link>
      <DependentUpon>frmLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\zLogin\frmLogin.vb">
      <Link>zLogin\frmLogin.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\SaleInfoHelper.vb">
      <Link>zWelcome\SaleInfoHelper.vb</Link>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="..\iTong\ProcForm.Designer.vb">
      <DependentUpon>ProcForm.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\ProcForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\AndroidAppMgr.vb">
      <Link>zAndroidApp\AndroidAppMgr.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\AndroidDeviceHelper.vb">
      <Link>zAndroidApp\AndroidDeviceHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\AndroidPackageReader.vb">
      <Link>zAndroidApp\AndroidPackageReader.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidAppListBase.Designer.vb">
      <DependentUpon>frmAndroidAppListBase.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidAppListBase.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidAppListBase.vb">
      <Link>zAndroidApp\frmAndroidAppListBase.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidDeviceApp.Designer.vb">
      <DependentUpon>frmAndroidDeviceApp.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidDeviceApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidDeviceApp.vb">
      <Link>zAndroidApp\frmAndroidDeviceApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidLocalApp.Designer.vb">
      <DependentUpon>frmAndroidLocalApp.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidLocalApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidLocalApp.vb">
      <Link>zAndroidApp\frmAndroidLocalApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidUpdate.Designer.vb">
      <DependentUpon>frmAndroidUpdate.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidUpdate.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidUpdate.vb">
      <Link>zAndroidApp\frmAndroidUpdate.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidUpdateIgnore.Designer.vb">
      <DependentUpon>frmAndroidUpdateIgnore.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidUpdateIgnore.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidUpdateIgnore.vb">
      <Link>zAndroidApp\frmAndroidUpdateIgnore.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidFile.designer.vb">
      <DependentUpon>frmAndroidFile.vb</DependentUpon>
      <Link>zAndroidFile\frmAndroidFile.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidFile.vb">
      <Link>zAndroidFile\frmAndroidFile.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidFileBase.Designer.vb">
      <DependentUpon>frmAndroidFileBase.vb</DependentUpon>
      <Link>zAndroidFile\frmAndroidFileBase.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidFileBase.vb">
      <Link>zAndroidFile\frmAndroidFileBase.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidPlistEdit.Designer.vb">
      <DependentUpon>frmAndroidPlistEdit.vb</DependentUpon>
      <Link>zAndroidFile\frmAndroidPlistEdit.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidPlistEdit.vb">
      <Link>zAndroidFile\frmAndroidPlistEdit.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidDriver.Designer.vb">
      <DependentUpon>frmAndroidDriver.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidDriver.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidDriver.vb">
      <Link>zAndroidMsgForm\frmAndroidDriver.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidFeedback.Designer.vb">
      <DependentUpon>frmAndroidFeedback.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidFeedback.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidFeedback.vb">
      <Link>zAndroidMsgForm\frmAndroidFeedback.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidNoConnecting.Designer.vb">
      <DependentUpon>frmAndroidNoConnecting.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidNoConnecting.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidNoConnecting.vb">
      <Link>zAndroidMsgForm\frmAndroidNoConnecting.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUninstallMsg.Designer.vb">
      <DependentUpon>frmAndroidUninstallMsg.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidUninstallMsg.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUninstallMsg.vb">
      <Link>zAndroidMsgForm\frmAndroidUninstallMsg.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugBrowser.Designer.vb">
      <DependentUpon>frmAndroidUSBDebugBrowser.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidUSBDebugBrowser.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugBrowser.vb">
      <Link>zAndroidMsgForm\frmAndroidUSBDebugBrowser.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugSimple.Designer.vb">
      <DependentUpon>frmAndroidUSBDebugSimple.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidUSBDebugSimple.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugSimple.vb">
      <Link>zAndroidMsgForm\frmAndroidUSBDebugSimple.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidDetail.Designer.vb">
      <DependentUpon>frmAndroidDetail.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidDetail.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidDetail.vb">
      <Link>zAndroidWelcome\frmAndroidDetail.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidNoDevice.Designer.vb">
      <DependentUpon>frmAndroidNoDevice.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidNoDevice.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidNoDevice.vb">
      <Link>zAndroidWelcome\frmAndroidNoDevice.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidPreview.Designer.vb">
      <DependentUpon>frmAndroidPreview.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidPreview.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidPreview.vb">
      <Link>zAndroidWelcome\frmAndroidPreview.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidSummary.Designer.vb">
      <DependentUpon>frmAndroidSummary.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidSummary.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidSummary.vb">
      <Link>zAndroidWelcome\frmAndroidSummary.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidWelcome.Designer.vb">
      <DependentUpon>frmAndroidWelcome.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidWelcome.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidWelcome.vb">
      <Link>zAndroidWelcome\frmAndroidWelcome.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroid\AndroidImportHelper.vb">
      <Link>zAndroid\AndroidImportHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroid\frmAndroidBase.Designer.vb">
      <DependentUpon>frmAndroidBase.vb</DependentUpon>
      <Link>zAndroid\frmAndroidBase.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroid\frmAndroidBase.vb">
      <Link>zAndroid\frmAndroidBase.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zClass\AgentHelper.vb">
      <Link>zClass\AgentHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zClass\CommonInfo.vb">
      <Link>zClass\CommonInfo.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zClass\MP3Http.vb">
      <Link>zClass\MP3Http.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zClass\MP3Search.vb">
      <Link>zClass\MP3Search.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbCharBar.vb">
      <Link>zComponents\tbCharBar.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbCheckButton.vb">
      <Link>zComponents\tbCheckButton.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbDownloadBar.vb">
      <Link>zComponents\tbDownloadBar.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbFlowLayoutItem.vb">
      <Link>zComponents\tbFlowLayoutItem.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbFlowLayoutPanelEx.vb">
      <Link>zComponents\tbFlowLayoutPanelEx.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbGroupPanel.vb">
      <Link>zComponents\tbGroupPanel.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbStepView.vb">
      <Link>zComponents\tbStepView.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbTextBoxPath.Designer.vb">
      <DependentUpon>tbTextBoxPath.vb</DependentUpon>
      <Link>zComponents\tbTextBoxPath.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbTextBoxPath.vb">
      <Link>zComponents\tbTextBoxPath.vb</Link>
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbTreeViewEx.vb">
      <Link>zComponents\tbTreeViewEx.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewCollect.vb">
      <Link>zDataGridView\tbDataGridViewCollect.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewEditCell.vb">
      <Link>zDataGridView\tbDataGridViewEditCell.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewEx.vb">
      <Link>zDataGridView\tbDataGridViewEx.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewMediaCell.vb">
      <Link>zDataGridView\tbDataGridViewMediaCell.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewProgressEx.vb">
      <Link>zDataGridView\tbDataGridViewProgressEx.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewShowMoreRow.vb">
      <Link>zDataGridView\tbDataGridViewShowMoreRow.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewTextBoxCellEx.vb">
      <Link>zDataGridView\tbDataGridViewTextBoxCellEx.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmApp.Designer.vb">
      <DependentUpon>frmApp.vb</DependentUpon>
      <Link>zDeviceApp\frmApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmApp.vb">
      <Link>zDeviceApp\frmApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmAppDetail.Designer.vb">
      <DependentUpon>frmAppDetail.vb</DependentUpon>
      <Link>zDeviceApp\frmAppDetail.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmAppDetail.vb">
      <Link>zDeviceApp\frmAppDetail.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmAppFilter.Designer.vb">
      <DependentUpon>frmAppFilter.vb</DependentUpon>
      <Link>zDeviceApp\frmAppFilter.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmAppFilter.vb">
      <Link>zDeviceApp\frmAppFilter.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmCommonApp.Designer.vb">
      <DependentUpon>frmCommonApp.vb</DependentUpon>
      <Link>zDeviceApp\frmCommonApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmCommonApp.vb">
      <Link>zDeviceApp\frmCommonApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmDeviceApp.Designer.vb">
      <DependentUpon>frmDeviceApp.vb</DependentUpon>
      <Link>zDeviceApp\frmDeviceApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmDeviceApp.vb">
      <Link>zDeviceApp\frmDeviceApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmDeviceAppBackup.Designer.vb">
      <DependentUpon>frmDeviceAppBackup.vb</DependentUpon>
      <Link>zDeviceApp\frmDeviceAppBackup.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmDeviceAppBackup.vb">
      <Link>zDeviceApp\frmDeviceAppBackup.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmiTunesLoginV3.Designer.vb">
      <DependentUpon>frmiTunesLoginV3.vb</DependentUpon>
      <Link>zDeviceApp\frmiTunesLoginV3.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmiTunesLoginV3.vb">
      <Link>zDeviceApp\frmiTunesLoginV3.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmLocalApp.Designer.vb">
      <DependentUpon>frmLocalApp.vb</DependentUpon>
      <Link>zDeviceApp\frmLocalApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmLocalApp.vb">
      <Link>zDeviceApp\frmLocalApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmUpdate.Designer.vb">
      <DependentUpon>frmUpdate.vb</DependentUpon>
      <Link>zDeviceApp\frmUpdate.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmUpdate.vb">
      <Link>zDeviceApp\frmUpdate.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmUpdateIgnore.Designer.vb">
      <DependentUpon>frmUpdateIgnore.vb</DependentUpon>
      <Link>zDeviceApp\frmUpdateIgnore.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmUpdateIgnore.vb">
      <Link>zDeviceApp\frmUpdateIgnore.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\IApp.vb">
      <Link>zDeviceApp\IApp.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceClass\DeviceCapacity.vb">
      <Link>zDeviceClass\DeviceCapacity.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceClass\VoiceMemosDB.vb">
      <Link>zDeviceClass\VoiceMemosDB.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFile.Designer.vb">
      <DependentUpon>frmFile.vb</DependentUpon>
      <Link>zDeviceFile\frmFile.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFile.vb">
      <Link>zDeviceFile\frmFile.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFileAppList.Designer.vb">
      <DependentUpon>frmFileAppList.vb</DependentUpon>
      <Link>zDeviceFile\frmFileAppList.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFileAppList.vb">
      <Link>zDeviceFile\frmFileAppList.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFileBase.Designer.vb">
      <DependentUpon>frmFileBase.vb</DependentUpon>
      <Link>zDeviceFile\frmFileBase.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFileBase.vb">
      <Link>zDeviceFile\frmFileBase.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmPlistEdit.Designer.vb">
      <DependentUpon>frmPlistEdit.vb</DependentUpon>
      <Link>zDeviceFile\frmPlistEdit.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmPlistEdit.vb">
      <Link>zDeviceFile\frmPlistEdit.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmAppDownloaded.Designer.vb">
      <DependentUpon>frmAppDownloaded.vb</DependentUpon>
      <Link>zDownload\frmAppDownloaded.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmAppDownloaded.vb">
      <Link>zDownload\frmAppDownloaded.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloadAction.designer.vb">
      <DependentUpon>frmDownloadAction.vb</DependentUpon>
      <Link>zDownload\frmDownloadAction.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloadAction.vb">
      <Link>zDownload\frmDownloadAction.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloadCenter.designer.vb">
      <DependentUpon>frmDownloadCenter.vb</DependentUpon>
      <Link>zDownload\frmDownloadCenter.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloadCenter.vb">
      <Link>zDownload\frmDownloadCenter.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloaded.designer.vb">
      <DependentUpon>frmDownloaded.vb</DependentUpon>
      <Link>zDownload\frmDownloaded.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloaded.vb">
      <Link>zDownload\frmDownloaded.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloading.designer.vb">
      <DependentUpon>frmDownloading.vb</DependentUpon>
      <Link>zDownload\frmDownloading.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloading.vb">
      <Link>zDownload\frmDownloading.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmSelectDevice.designer.vb">
      <DependentUpon>frmSelectDevice.vb</DependentUpon>
      <Link>zDownload\frmSelectDevice.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmSelectDevice.vb">
      <Link>zDownload\frmSelectDevice.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmTip.Designer.vb">
      <DependentUpon>frmTip.vb</DependentUpon>
      <Link>zDownload\frmTip.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmTip.vb">
      <Link>zDownload\frmTip.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zLiveUpdate\LiveUpdateForm.Designer.vb">
      <DependentUpon>LiveUpdateForm.vb</DependentUpon>
      <Link>zLiveUpdate\LiveUpdateForm.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zLiveUpdate\LiveUpdateForm.vb">
      <Link>zLiveUpdate\LiveUpdateForm.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zLiveUpdate\LiveUpdateHelper.vb">
      <Link>zLiveUpdate\LiveUpdateHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\frmFileExist.designer.vb">
      <DependentUpon>frmFileExist.vb</DependentUpon>
      <Link>zMsgForm\frmFileExist.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\frmFileExist.vb">
      <Link>zMsgForm\frmFileExist.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\tbInputBox.vb">
      <Link>zMsgForm\tbInputBox.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\tbSplashBox.Designer.vb">
      <DependentUpon>tbSplashBox.vb</DependentUpon>
      <Link>zMsgForm\tbSplashBox.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\tbSplashBox.vb">
      <Link>zMsgForm\tbSplashBox.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\AuthorityWarningForm.designer.vb">
      <DependentUpon>AuthorityWarningForm.vb</DependentUpon>
      <Link>zSetting\AuthorityWarningForm.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zSetting\AuthorityWarningForm.vb">
      <Link>zSetting\AuthorityWarningForm.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmAbout.Designer.vb">
      <DependentUpon>frmAbout.vb</DependentUpon>
      <Link>zSetting\frmAbout.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmAbout.vb">
      <Link>zSetting\frmAbout.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmAppExist.Designer.vb">
      <DependentUpon>frmAppExist.vb</DependentUpon>
      <Link>zSetting\frmAppExist.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmAppExist.vb">
      <Link>zSetting\frmAppExist.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmSettingV3.Designer.vb">
      <DependentUpon>frmSettingV3.vb</DependentUpon>
      <Link>zSetting\frmSettingV3.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmSettingV3.vb">
      <Link>zSetting\frmSettingV3.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\JBIniSetting.vb">
      <Link>zSetting\JBIniSetting.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zTools\frmTools.Designer.vb">
      <DependentUpon>frmTools.vb</DependentUpon>
      <Link>zTools\frmTools.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zTools\frmTools.vb">
      <Link>zTools\frmTools.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zTools\tbBackgroundWorker.vb">
      <Link>zTools\tbBackgroundWorker.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zWebsite\frmSite.Designer.vb">
      <DependentUpon>frmSite.vb</DependentUpon>
      <Link>zWebsite\frmSite.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWebsite\frmSite.vb">
      <Link>zWebsite\frmSite.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmVerify.Designer.vb">
      <DependentUpon>frmVerify.vb</DependentUpon>
      <Link>zWeibo\frmVerify.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmVerify.vb">
      <Link>zWeibo\frmVerify.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmWeiBoLogin.Designer.vb">
      <DependentUpon>frmWeiBoLogin.vb</DependentUpon>
      <Link>zWeibo\frmWeiBoLogin.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmWeiBoLogin.vb">
      <Link>zWeibo\frmWeiBoLogin.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmWeiboSend.Designer.vb">
      <DependentUpon>frmWeiboSend.vb</DependentUpon>
      <Link>zWeibo\frmWeiboSend.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmWeiboSend.vb">
      <Link>zWeibo\frmWeiboSend.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWeibo\WeiboConfig.vb">
      <Link>zWeibo\WeiboConfig.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWeibo\WeiboHelper.vb">
      <Link>zWeibo\WeiboHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmCommonWel.Designer.vb">
      <DependentUpon>frmCommonWel.vb</DependentUpon>
      <Link>zWelcome\frmCommonWel.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmCommonWel.vb">
      <Link>zWelcome\frmCommonWel.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmDetail.Designer.vb">
      <DependentUpon>frmDetail.vb</DependentUpon>
      <Link>zWelcome\frmDetail.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmDetail.vb">
      <Link>zWelcome\frmDetail.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmPreview.Designer.vb">
      <DependentUpon>frmPreview.vb</DependentUpon>
      <Link>zWelcome\frmPreview.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmPreview.vb">
      <Link>zWelcome\frmPreview.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmSummary.Designer.vb">
      <DependentUpon>frmSummary.vb</DependentUpon>
      <Link>zWelcome\frmSummary.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmSummary.vb">
      <Link>zWelcome\frmSummary.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmSummaryRecommend.Designer.vb">
      <DependentUpon>frmSummaryRecommend.vb</DependentUpon>
      <Link>zWelcome\frmSummaryRecommend.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmSummaryRecommend.vb">
      <Link>zWelcome\frmSummaryRecommend.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmTuiInstall.designer.vb">
      <DependentUpon>frmTuiInstall.vb</DependentUpon>
      <Link>zWelcome\frmTuiInstall.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmTuiInstall.vb">
      <Link>zWelcome\frmTuiInstall.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmTuiInstallTutorial.designer.vb">
      <DependentUpon>frmTuiInstallTutorial.vb</DependentUpon>
      <Link>zWelcome\frmTuiInstallTutorial.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmTuiInstallTutorial.vb">
      <Link>zWelcome\frmTuiInstallTutorial.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmWelcome.Designer.vb">
      <DependentUpon>frmWelcome.vb</DependentUpon>
      <Link>zWelcome\frmWelcome.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmWelcome.vb">
      <Link>zWelcome\frmWelcome.vb</Link>
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="..\iTong\frmSplash.resx">
      <DependentUpon>frmSplash.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zLogin\frmLogin.resx">
      <Link>zLogin\frmLogin.resx</Link>
      <DependentUpon>frmLogin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\ProcForm.resx">
      <DependentUpon>ProcForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidApp\frmAndroidDeviceApp.resx">
      <Link>zAndroidApp\frmAndroidDeviceApp.resx</Link>
      <DependentUpon>frmAndroidDeviceApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidApp\frmAndroidLocalApp.resx">
      <Link>zAndroidApp\frmAndroidLocalApp.resx</Link>
      <DependentUpon>frmAndroidLocalApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidApp\frmAndroidUpdate.resx">
      <Link>zAndroidApp\frmAndroidUpdate.resx</Link>
      <DependentUpon>frmAndroidUpdate.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidApp\frmAndroidUpdateIgnore.resx">
      <Link>zAndroidApp\frmAndroidUpdateIgnore.resx</Link>
      <DependentUpon>frmAndroidUpdateIgnore.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidFile\frmAndroidFile.resx">
      <Link>zAndroidFile\frmAndroidFile.resx</Link>
      <DependentUpon>frmAndroidFile.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidFile\frmAndroidFileBase.resx">
      <Link>zAndroidFile\frmAndroidFileBase.resx</Link>
      <DependentUpon>frmAndroidFileBase.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidFile\frmAndroidPlistEdit.resx">
      <Link>zAndroidFile\frmAndroidPlistEdit.resx</Link>
      <DependentUpon>frmAndroidPlistEdit.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidDriver.resx">
      <Link>zAndroidMsgForm\frmAndroidDriver.resx</Link>
      <DependentUpon>frmAndroidDriver.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidFeedback.resx">
      <Link>zAndroidMsgForm\frmAndroidFeedback.resx</Link>
      <DependentUpon>frmAndroidFeedback.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidNoConnecting.resx">
      <Link>zAndroidMsgForm\frmAndroidNoConnecting.resx</Link>
      <DependentUpon>frmAndroidNoConnecting.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidUninstallMsg.resx">
      <Link>zAndroidMsgForm\frmAndroidUninstallMsg.resx</Link>
      <DependentUpon>frmAndroidUninstallMsg.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugBrowser.resx">
      <Link>zAndroidMsgForm\frmAndroidUSBDebugBrowser.resx</Link>
      <DependentUpon>frmAndroidUSBDebugBrowser.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugSimple.resx">
      <Link>zAndroidMsgForm\frmAndroidUSBDebugSimple.resx</Link>
      <DependentUpon>frmAndroidUSBDebugSimple.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidDetail.resx">
      <Link>zAndroidWelcome\frmAndroidDetail.resx</Link>
      <DependentUpon>frmAndroidDetail.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidNoDevice.resx">
      <Link>zAndroidWelcome\frmAndroidNoDevice.resx</Link>
      <DependentUpon>frmAndroidNoDevice.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidPreview.resx">
      <Link>zAndroidWelcome\frmAndroidPreview.resx</Link>
      <DependentUpon>frmAndroidPreview.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidSummary.resx">
      <Link>zAndroidWelcome\frmAndroidSummary.resx</Link>
      <DependentUpon>frmAndroidSummary.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidWelcome.resx">
      <Link>zAndroidWelcome\frmAndroidWelcome.resx</Link>
      <DependentUpon>frmAndroidWelcome.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroid\frmAndroidBase.resx">
      <Link>zAndroid\frmAndroidBase.resx</Link>
      <DependentUpon>frmAndroidBase.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zComponents\tbTextBoxPath.resx">
      <Link>zComponents\tbTextBoxPath.resx</Link>
      <DependentUpon>tbTextBoxPath.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmApp.resx">
      <Link>zDeviceApp\frmApp.resx</Link>
      <DependentUpon>frmApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmAppDetail.resx">
      <Link>zDeviceApp\frmAppDetail.resx</Link>
      <DependentUpon>frmAppDetail.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmAppFilter.resx">
      <Link>zDeviceApp\frmAppFilter.resx</Link>
      <DependentUpon>frmAppFilter.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmCommonApp.resx">
      <Link>zDeviceApp\frmCommonApp.resx</Link>
      <DependentUpon>frmCommonApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmDeviceApp.resx">
      <Link>zDeviceApp\frmDeviceApp.resx</Link>
      <DependentUpon>frmDeviceApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmDeviceAppBackup.resx">
      <Link>zDeviceApp\frmDeviceAppBackup.resx</Link>
      <DependentUpon>frmDeviceAppBackup.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmiTunesLoginV3.resx">
      <Link>zDeviceApp\frmiTunesLoginV3.resx</Link>
      <DependentUpon>frmiTunesLoginV3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmLocalApp.resx">
      <Link>zDeviceApp\frmLocalApp.resx</Link>
      <DependentUpon>frmLocalApp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmUpdate.resx">
      <Link>zDeviceApp\frmUpdate.resx</Link>
      <DependentUpon>frmUpdate.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmUpdateIgnore.resx">
      <Link>zDeviceApp\frmUpdateIgnore.resx</Link>
      <DependentUpon>frmUpdateIgnore.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceFile\frmFile.resx">
      <Link>zDeviceFile\frmFile.resx</Link>
      <DependentUpon>frmFile.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceFile\frmFileAppList.resx">
      <Link>zDeviceFile\frmFileAppList.resx</Link>
      <DependentUpon>frmFileAppList.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceFile\frmFileBase.resx">
      <Link>zDeviceFile\frmFileBase.resx</Link>
      <DependentUpon>frmFileBase.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceFile\frmPlistEdit.resx">
      <Link>zDeviceFile\frmPlistEdit.resx</Link>
      <DependentUpon>frmPlistEdit.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmAppDownloaded.resx">
      <Link>zDownload\frmAppDownloaded.resx</Link>
      <DependentUpon>frmAppDownloaded.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmDownloadAction.resx">
      <Link>zDownload\frmDownloadAction.resx</Link>
      <DependentUpon>frmDownloadAction.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmDownloadCenter.resx">
      <Link>zDownload\frmDownloadCenter.resx</Link>
      <DependentUpon>frmDownloadCenter.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmDownloaded.resx">
      <Link>zDownload\frmDownloaded.resx</Link>
      <DependentUpon>frmDownloaded.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmDownloading.resx">
      <Link>zDownload\frmDownloading.resx</Link>
      <DependentUpon>frmDownloading.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmSelectDevice.resx">
      <Link>zDownload\frmSelectDevice.resx</Link>
      <DependentUpon>frmSelectDevice.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmTip.resx">
      <Link>zDownload\frmTip.resx</Link>
      <DependentUpon>frmTip.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zLiveUpdate\LiveUpdateForm.resx">
      <Link>zLiveUpdate\LiveUpdateForm.resx</Link>
      <DependentUpon>LiveUpdateForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zMsgForm\frmFileExist.resx">
      <Link>zMsgForm\frmFileExist.resx</Link>
      <DependentUpon>frmFileExist.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zMsgForm\tbSplashBox.resx">
      <Link>zMsgForm\tbSplashBox.resx</Link>
      <DependentUpon>tbSplashBox.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zSetting\AuthorityWarningForm.resx">
      <Link>zSetting\AuthorityWarningForm.resx</Link>
      <DependentUpon>AuthorityWarningForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zSetting\frmAbout.resx">
      <Link>zSetting\frmAbout.resx</Link>
      <DependentUpon>frmAbout.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zSetting\frmAppExist.resx">
      <Link>zSetting\frmAppExist.resx</Link>
      <DependentUpon>frmAppExist.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zSetting\frmSettingV3.resx">
      <Link>zSetting\frmSettingV3.resx</Link>
      <DependentUpon>frmSettingV3.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zTools\frmTools.resx">
      <Link>zTools\frmTools.resx</Link>
      <DependentUpon>frmTools.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWebsite\frmSite.resx">
      <Link>zWebsite\frmSite.resx</Link>
      <DependentUpon>frmSite.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWeibo\frmVerify.resx">
      <Link>zWeibo\frmVerify.resx</Link>
      <DependentUpon>frmVerify.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWeibo\frmWeiBoLogin.resx">
      <Link>zWeibo\frmWeiBoLogin.resx</Link>
      <DependentUpon>frmWeiBoLogin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWeibo\frmWeiboSend.resx">
      <Link>zWeibo\frmWeiboSend.resx</Link>
      <DependentUpon>frmWeiboSend.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmCommonWel.resx">
      <Link>zWelcome\frmCommonWel.resx</Link>
      <DependentUpon>frmCommonWel.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmDetail.resx">
      <Link>zWelcome\frmDetail.resx</Link>
      <DependentUpon>frmDetail.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmPreview.resx">
      <Link>zWelcome\frmPreview.resx</Link>
      <DependentUpon>frmPreview.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmSummary.resx">
      <Link>zWelcome\frmSummary.resx</Link>
      <DependentUpon>frmSummary.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmSummaryRecommend.resx">
      <Link>zWelcome\frmSummaryRecommend.resx</Link>
      <DependentUpon>frmSummaryRecommend.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmTuiInstall.resx">
      <Link>zWelcome\frmTuiInstall.resx</Link>
      <DependentUpon>frmTuiInstall.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmTuiInstallTutorial.resx">
      <Link>zWelcome\frmTuiInstallTutorial.resx</Link>
      <DependentUpon>frmTuiInstallTutorial.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmWelcome.resx">
      <Link>zWelcome\frmWelcome.resx</Link>
      <DependentUpon>frmWelcome.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android.csproj">
      <Project>{898E7D3C-7ED3-4BD4-A668-5FBFB5C3F3AB}</Project>
      <Name>Android</Name>
    </ProjectReference>
    <ProjectReference Include="..\Components\Components.vbproj">
      <Project>{28459798-5E28-45D3-AAE3-74A9F1761D1A}</Project>
      <Name>Components</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc.csproj">
      <Project>{685AE51B-3C37-4B37-B3A2-B485D07A6E6B}</Project>
      <Name>CoreMisc</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreModuleEx\CoreModuleCS.csproj">
      <Project>{608e58d6-914f-45d2-a3a6-55cec62ef261}</Project>
      <Name>CoreModuleCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses.csproj">
      <Project>{65CE9103-521A-49E6-A8CD-89137B452842}</Project>
      <Name>CoreReses</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUpdate\CoreUpdate.csproj">
      <Project>{998A7F44-F7AF-4E25-9EF3-696834EE6242}</Project>
      <Name>CoreUpdate</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS.csproj">
      <Project>{61c62ea1-6ad7-41be-98db-8abb77ee35f3}</Project>
      <Name>CoreUtilCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil.vbproj">
      <Project>{718B167F-1B33-4B7C-A7E3-E15615DDAAC4}</Project>
      <Name>CoreUtil</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone.csproj">
      <Project>{6691EA4B-1EDB-4330-A3A3-E1D47F4D35BE}</Project>
      <Name>iPhone</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\ICSharpCode.SharpZLib.csproj">
      <Project>{0E7413FF-EB9E-4714-ACF2-BE3A6A7B2FFD}</Project>
      <Name>ICSharpCode.SharpZLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\tbFlashHelper\tbFlashHelper.vbproj">
      <Project>{1343347B-ED98-4A99-B9AA-54D10E7C7708}</Project>
      <Name>tbFlashHelper</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\iTong\Lang\zh-CN.lang">
      <Link>Lang\zh-CN.lang</Link>
    </None>
    <None Include="app.config" />
    <None Include="Resources\iTong.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>