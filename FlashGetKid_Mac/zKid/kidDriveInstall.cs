﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Drawing;
using System.Collections.Specialized;
using System.Xml;
using System.Diagnostics;
using System.Management;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreModule;
using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;

using skContextMenuStrip = iTong.CoreModule.skMenu;
using ToolStripMenuItem = iTong.CoreModule.skMenuItem;
using ToolStripItem = iTong.CoreModule.skMenuItem;
using PictureBox = iTong.CoreModule.skPictureBox;
using Label = iTong.CoreModule.skLabel;
using NotifyIcon = iTong.CoreModule.skStatusItem;
using ContentAlignment = iTong.CoreModule.ContentAlignment;

namespace iTong.CoreModule
{
    public partial class kidDriveInstall : frmBase
    {

        public override skSplit skTransparentImageSplit => new skSplit(15);

        private MultiThreadDownload mDownMgr = null;

        private string mDrivePath = string.Empty;

        private skTimer mTimer = null;

        private MultiThreadDownloadItemInfo mDownloadItemInfo = null;

        private tdActionHelper<tdActionItemForFGKid> mActionHelper = null;

        private skTimer progressTimer;

        public kidDriveInstall(tdActionHelper<tdActionItemForFGKid> actionHelper)
        {
            InitializeComponent();

            this.mDownMgr = MultiThreadDownload.Instance();

            this.mActionHelper = actionHelper;
        }

        protected override void InitControls()
        {
            try
            {
                base.InitControls();

                this.InitStyle();

                this.InitPlugins();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "DriveInstallForm.InitControls");
            }
        }

        private void InitStyle()
        {

            this.skShowButtonClose = true;

            this.skShowTitle = true;

            this.skTitle = null;
            this.skTitleFont = MyFont.CreateFont(9.75f, true);
            this.skTitleColor = skColor.FromArgb(45, 47, 51);

            this.Size = new Size(482, 200);

            //if (this.Owner != null)
            //{
            //    MyForm.SetParentCenter(this, this.Owner);

            //    this.Location = new Point(this.Location.X, this.Location.Y - 60);
            //}
            this.skBorderRadius = new skBorderRadius(2);
            this.skTransparentImage = MyResource.GetImage("frm_bg_shadow.png");
            this.skTransparentImagePadding = new Padding(6, 6, 6, 6);
            this.skBorderStrokeColor = Color.White;

            this.progressDriveLoading.skValue = 0;
            this.progressDriveInstalling.skValue = 0;

            this.progressDriveLoading.Enabled = false;
            this.progressDriveInstalling.Enabled = false;

            this.btnDownLoadDrive.skAutoSize = true;
            this.btnDownLoadiTunes.skAutoSize = true;

            this.btnDownLoadDrive.MinimumSize = new Size(90, 28);
            this.btnDownLoadiTunes.MinimumSize = new Size(90, 28);

            this.btnRetryDownload.skAutoSize = true;
            this.btnDownloadFailedCancel.skAutoSize = true;

            this.btnRetryDownload.MinimumSize = new Size(84, 28);
            this.btnDownloadFailedCancel.MinimumSize = new Size(84, 28);

            this.btnRetryInstall.skAutoSize = true;
            this.btnInstallFailedCancel.skAutoSize = true;

            this.btnRetryInstall.MinimumSize = new Size(84, 28);
            this.btnInstallFailedCancel.MinimumSize = new Size(84, 28);

            this.btnDownLoadDrive.Padding = new Padding(0, 2, 0, 0);
            this.btnDownLoadiTunes.Padding = new Padding(0, 2, 0, 0);

            this.btnRetryDownload.Padding = new Padding(0, 2, 0, 0);
            this.btnDownloadFailedCancel.Padding = new Padding(0, 2, 0, 0);

            this.btnRetryInstall.Padding = new Padding(0, 2, 0, 0);
            this.btnInstallFailedCancel.Padding = new Padding(0, 2, 0, 0);

            int diff = 15;
            this.btnDownLoadiTunes.Location = new Point(this.pnlDriveInstall.Width - btnDownLoadiTunes.Width - diff, 110);
            this.btnDownLoadDrive.Location = new Point(this.pnlDriveInstall.Width - btnDownLoadDrive.Width - diff, 110);// new Point(this.btnDownLoadiTunes.Location.X - btnDownLoadDrive.Width - diff, 110);

            int btnY = 95;
            this.btnDownloadFailedCancel.Location = new Point(this.pnlDriveLoading.Width - diff, btnY);
            this.btnRetryDownload.Location = new Point(this.btnDownloadFailedCancel.Location.X - diff - this.btnRetryDownload.Width, btnY);

            this.btnInstallFailedCancel.Location = new Point(this.pnlDriveLoading.Width - diff, btnY);
            this.btnRetryInstall.Location = new Point(this.btnInstallFailedCancel.Location.X - diff - this.btnRetryInstall.Width, btnY);

            this.lblDriveLoading.Width = 282;
            this.lblDriveInstalling.Width = 282;

            this.lblDriveInstalling.skAutoHeight = true;
            this.lblDriveLoading.skAutoHeight = true;

            this.lblDriveInstallTitle.skText = this.Language.GetString("Parental.InstallDrive.title"); //安装驱动

            this.lblDriveInstallContent.skText = this.Language.GetString("Parental.InstallDrive.text"); //检测到当前设备未安装 iTunes 或 相关驱动程序，无法识别和连接iOS设备，请下载并安装iTunes或相关驱动程序，两者中选择一个即可。

            this.lblDriveLoading.skText = this.Language.GetString("Parental.DownloadingDrive.title"); //正在下载IOS相关驱动内容

            this.lblDriveInstalling.skText = this.Language.GetString("Parental.InstallingDrive.title"); //正在安装IOS相关驱动内容

            this.lblDownloadFailed.skText = this.Language.GetString("Parental.DownloadFailed.title"); //下载失败

            this.lblDownloadFailedContent.skText = this.Language.GetString("Parental.DownloadFailed.text"); //请检查电脑的网络状况，然后重新下载

            this.lblInstallFailed.skText = this.Language.GetString("Common.Install.Failed"); //安装失败

            this.lblInstallFailedContent.skText = this.Language.GetString("Parental.InstallFailed.text"); //请检查电脑状态，然后重新安装

            this.btnDownLoadDrive.skText = this.Language.GetString("DownloadKids.Button.Download"); //下载相关驱动

            this.btnRetryDownload.skText = this.Language.GetString("Common.TryAgain.Button"); //重新下载

            this.btnRetryInstall.skText = this.Language.GetString("Common.TryAgain.Button"); //重新安装

            string cancelStr = this.Language.GetString("Common.Cancel"); //取消
            this.btnDriveLoadingCancel.skText = cancelStr;
            this.btnDriveInstallingCancel.skText = cancelStr;
            this.btnDownloadFailedCancel.skText = cancelStr;
            this.btnDriveInstallingCancel.skText = cancelStr;
            this.btnDownloadFailedCancel.skText = cancelStr;
            this.btnInstallFailedCancel.skText = cancelStr;

            this.InitPanel(this.pnlDriveInstall);
        }

        /// <summary>初始化Panel</summary>
        /// <param name="pnl"></param>
        private void InitPanel(skPanel pnl)
        {

            switch (pnl.Name)
            {
                case "pnlDriveInstall":

                    pnl.Size = new Size(480, 168);

                    break;

                case "pnlDriveLoading":
                case "pnlDriveInstalling":

                    this.Size = new Size(382, 134);

                    pnl.Size = new Size(380, 96);

                    //if (this.Owner != null)
                    //{
                    //    MyForm.SetParentCenter(this, this.Owner);

                    //    this.Location = new Point(this.Location.X, this.Location.Y - 60);
                    //}

                    int x = (this.Width - this.lblDriveLoading.Width) / 2;

                    this.lblDriveLoading.Location = new Point(x, 1);
                    this.progressDriveLoading.Location = new Point(x, 50);

                    this.lblDriveInstalling.Location = new Point(x, 1);
                    this.progressDriveInstalling.Location = new Point(x, 50);

                    int btnX = (this.Width - this.btnDriveLoadingCancel.Width) / 2;

                    this.btnDriveLoadingCancel.Location = new Point(btnX, 50);
                    this.btnDriveInstallingCancel.Location = new Point(btnX, 50);

                    this.progressDriveLoading.skValue = 0;
                    this.progressDriveInstalling.skValue = 0;

                    if (pnl == this.pnlDriveInstalling)
                        this.skShowButtonClose = false;

                    break;

                case "pnlDownloadFailed":
                case "pnlInstallFailed":

                    this.Size = new Size(482, 186);
                    pnl.Size = new Size(480, 146);

                    break;

                default:
                    break;
            }

            pnl.Location = new Point(1, 38);
            pnl.BringToFront();

        }

        private void InitPlugins()
        {
            this.mDownMgr.TaskStart += OnTaskStart;
            this.mDownMgr.TaskHasDownloadSucceed += OnTaskCompleted;
            this.mDownMgr.DownloadItemCallBack += OnDownloading;
            this.mDownMgr.DownloadItemCompleted += OnDownloaded;
        }

        private void OnTaskStart(object sender, MultiThreadDownloadItem e)
        {
            if (this.InvokeRequired)
                this.Invoke(new EventHandler<MultiThreadDownloadItem>(OnTaskStart), sender, e);
            else
                this.progressDriveLoading.skValue = 0;
        }

        private void OnTaskCompleted(object sender, MultiThreadDownloadItem e)
        {
            if (this.InvokeRequired)
                this.Invoke(new EventHandler<MultiThreadDownloadItem>(OnTaskCompleted), sender, e);
            else
                this.InstallDrive();
        }

        /// <summary>下载中</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OnDownloading(object sender, MultiThreadDownloadEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new EventHandler<MultiThreadDownloadEventArgs>(OnDownloading), sender, e);
            }
            else
            {
                MultiThreadDownloadItem item = sender as MultiThreadDownloadItem;

                if (item.ItemInfo.Class == ResourceClass.Plugins && e.Progress > 0)
                    progressDriveLoading.skValue = e.Progress;
            }
        }

        /// <summary>下载完成</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void OnDownloaded(object sender, MultiThreadDownloadCompletedEventArgs e)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new EventHandler<MultiThreadDownloadCompletedEventArgs>(OnDownloaded), sender, e);
            }
            else
            {
                MultiThreadDownloadItem item = sender as MultiThreadDownloadItem;

                if (item.ItemInfo.Class == ResourceClass.Plugins)
                {
                    bool blnSuccess = (!e.Cancel && e.ReceiveSize >= e.ResourceSize && e.ResourceSize > 0);

                    if (blnSuccess)
                    {
                        this.progressDriveLoading.skValue = 100;

                        this.InstallDrive();
                    }
                    else
                    {
                        this.InitPanel(this.pnlDownloadFailed);
                    }
                }
            }
        }

        /// <summary>下载驱动</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDownLoadDrive_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DownloadDrive);

            //this.DownloadDrive();

            #region 测试
            //this.InstallDrive();
            #endregion 测试
        }

        /// <summary>下载iTunes</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDownLoadiTunes_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.DownloadiTunes);

            Process.Start("https://www.apple.com/itunes/");
        }

        ////https://dl.airdroid.com/20240417103721_AppleMobileDeviceSupport64_v12.13.1.3.msi
        ///// <summary>下载驱动</summary>
        //private void DownloadDrive()
        //{
        //    try
        //    {
        //        mDownloadItemInfo = new MultiThreadDownloadItemInfo();
        //        string url = "https://dl.airdroid.com/20240417103721_AppleMobileDeviceSupport64_v12.13.1.3.msi";
        //        string ver = string.Empty;

        //        List<object> list = RTCStatistics.GetConfigByUrl(MyUrl.GetBatch, "AppleMobileDeviceSupport");
        //        if (list != null && list.Count > 0)
        //        {
        //            Dictionary<object, object> dic = list[0] as Dictionary<object, object>;
        //            url = dic["url"].ToString();
        //            ver = dic["ver"].ToString();
        //        }

        //        if (AirSetting.IsChina())
        //        {
        //            Uri originUrl = new Uri(url);
        //            Uri newUrl = new UriBuilder(originUrl) { Host = "dl-qn.airdroid.cn" }.Uri;
        //            url = newUrl.ToString();
        //        }

        //        mDownloadItemInfo.Url = url;
        //        mDownloadItemInfo.Name = string.Format("AppleMobileDeviceSupport64_v{0}.msi", ver);
        //        mDownloadItemInfo.Class = ResourceClass.Plugins;
        //        mDownloadItemInfo.SaveFolder = Path.Combine(Folder.ApplicationDataFolder, "Drive");

        //        mDrivePath = Path.Combine(mDownloadItemInfo.SaveFolder, mDownloadItemInfo.Name);
        //    }
        //    catch (Exception ex)
        //    {
        //        Common.LogException(ex.ToString(), "frmiTunes.DownloadiTunes");
        //    }
        //    finally
        //    {
        //        this.ActionDownload();
        //    }
        //}

        private delegate void ActionDownloadHandler();
        private void ActionDownload()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ActionDownloadHandler(ActionDownload));
                return;
            }

            if (File.Exists(mDrivePath))
            {
                this.progressDriveLoading.skValue = 100;

                this.InstallDrive();
            }
            else
            {
                this.InitPanel(this.pnlDriveLoading);

                this.mDownMgr.NewTask(this.mDownloadItemInfo);
            }
        }

        /// <summary>安装驱动</summary>
        private void InstallDrive()
        {
            this.InitPanel(this.pnlDriveInstalling);

            this.progressDriveInstalling.skShowStateText = false;

            progressTimer = TimerMgr.Create(0.3, () =>
            {
                if (this.progressDriveInstalling.skValue == 90)
                    this.progressDriveInstalling.skValue = 10;
                else
                    this.progressDriveInstalling.skValue += 10;
            });
            //this.progressDriveInstalling.skIsWaiting = true;

            ThreadMgr.Start(ThreadInstall);
        }

        private void ThreadInstall()
        {
            bool result = false;

            try
            {
                Process process = new Process();
                process.StartInfo.FileName = "msiexec";
                process.StartInfo.Arguments = "/i \"" + mDrivePath + "\" /quiet";
                process.StartInfo.Verb = "runas";
                process.StartInfo.UseShellExecute = false; // 必须为false才能重定向流
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;
                process.StartInfo.CreateNoWindow = true; // 不创建窗口
                process.Start();

                // 开始异步读取输出和错误流
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();

                process.WaitForExit();

                if (process.ExitCode == 0)// 0 通常表示成功
                    result = true;
            }
            catch (Exception ex)
            {
                // 处理异常，例如安装路径不正确或文件不存在
                Common.LogException(ex, "frmDriveInstall.Install");
            }
            finally
            {
                progressTimer.StopTimer();
                SetInstallState(result);
            }

            //using (Process process = new Process())
            //{
            //    process.StartInfo.FileName = "msiexec";
            //    process.StartInfo.Arguments = "/i \"" + mDrivePath + "\" /quiet";
            //    process.StartInfo.Verb = "runas";

            //    try
            //    {
            //        process.Start();
            //        process.WaitForExit(); // 等待安装完成

            //        // 根据输出判断安装是否成功
            //        if (process.ExitCode == 0)// 0 通常表示成功
            //            result = true;
            //    }
            //    catch (Exception ex)
            //    {
            //        // 处理异常，例如安装路径不正确或文件不存在
            //        Common.LogException(ex, "frmDriveInstall.Install");
            //    }
            //    finally
            //    {
            //        progressTimer.StopTimer();
            //        SetInstallState(result);
            //    }
            //}
        }

        private delegate void SetInstallStateHandler(bool installResult);
        private void SetInstallState(bool installResult)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new SetInstallStateHandler(SetInstallState), installResult);
                return;
            }

            if (installResult)
            {
                //安装成功
                //this.progressDriveInstalling.skIsWaiting = false;
                this.progressDriveInstalling.skValue = 100;

                this.mTimer = TimerMgr.Create(0.5, WaitHandle); //为防止直接返回DialogResult.OK 进度100%显示不出来 弹窗直接关闭，等待0.5s
            }
            else
            {
                // 安装失败
                this.InitPanel(this.pnlInstallFailed);
            }
        }

        private delegate void WaitHandler();
        private void WaitHandle()
        {
            this.mTimer.StopTimer();

            if (this.InvokeRequired)
            {
                this.BeginInvoke(new WaitHandler(WaitHandle));
                return;
            }

            this.DialogResult = DialogResult.OK;
        }

        /// <summary>取消下载</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDriveLoadingCancel_Click(object sender, EventArgs e)
        {
            this.mDownMgr.StopAll();

            this.mDownMgr.TaskStart -= OnTaskStart;
            this.mDownMgr.TaskHasDownloadSucceed -= OnTaskCompleted;
            this.mDownMgr.DownloadItemCallBack -= OnDownloading;
            this.mDownMgr.DownloadItemCompleted -= OnDownloaded;
        }

        /// <summary>取消安装</summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDriveInstallingCancel_Click(object sender, EventArgs e)
        {

        }

        private void btnDownloadFailedCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnRetryDownload_Click(object sender, EventArgs e)
        {
            //this.DownloadDrive();
        }

        private void btnRetryInstall_Click(object sender, EventArgs e)
        {
            this.InstallDrive();
        }

        private void btnInstallFailedCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
