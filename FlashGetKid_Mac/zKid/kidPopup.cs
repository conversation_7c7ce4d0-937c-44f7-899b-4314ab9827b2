﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Drawing;
using System.Collections.Specialized;
using System.Xml;
using System.Diagnostics;
using System.Management;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreModule;
using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;

using skContextMenuStrip = iTong.CoreModule.skMenu;
using ToolStripMenuItem = iTong.CoreModule.skMenuItem;
using ToolStripItem = iTong.CoreModule.skMenuItem;
using PictureBox = iTong.CoreModule.skPictureBox;
using Label = iTong.CoreModule.skLabel;
using NotifyIcon = iTong.CoreModule.skStatusItem;
using ContentAlignment = iTong.CoreModule.ContentAlignment;
using System.Net.NetworkInformation;

namespace iTong.CoreModule
{
    public partial class kidPopup : frmBase
    {

        //public override skSplit skTransparentImageSplit => new skSplit(15);

        private string mWindowType = string.Empty;

        private int mCountDownSeconds = 9;

        private string mEmail = string.Empty;

        private string mDeviceName = string.Empty;

        public string Email { get; private set; }

        private string mPairingCode = string.Empty;

        public string PairingCode { get; private set; }

        public KidProfileInfo KidProfileInfo { get; private set; }

        private Color mErrorTipColor = skColor.FromArgb(225, 62, 62);

        private Color mNormalTipColor = skColor.FromArgb(228, 230, 234);

        private string mOKstr = string.Empty;

        private tdActionHelper<tdActionItemForFGKid> mActionHelper = null;

        private skTimer mTimer = null;

        private skTimer timer_BtnConfirmTimeCount = null;

        public kidPopup(string windowType, tdActionHelper<tdActionItemForFGKid> actionHelper, string deviceName = null, string email = null, string pairingCode = null)
        {
            InitializeComponent();

            this.mWindowType = windowType;

            this.mEmail = email;

            this.mActionHelper = actionHelper;

            this.mPairingCode = pairingCode;

            this.mDeviceName = deviceName;
        }

        protected override void InitControls()
        {
            try
            {
                base.InitControls();

                this.InitStyle();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "PopupForm.InitControls");
            }
        }

        public override void OnShown(object sender, EventArgs e)
        {
            base.OnShown(sender, e);

            InitLocation();

            this.txtEnterPairingCode1.Focus();
        }


        private void InitLocation()
        {
            this.pnlEnterPairingCode.ClearSubView();
            this.pnlConfirm.ClearSubView();
            this.pnlAccountExpired.ClearSubView();

            string cancelStr = this.Language.GetString("Common.Cancel");

            switch (mWindowType)
            {
                case "PairingCode":

                    this.lblPairingCodeNotExistTip.skText = this.Language.GetString("Parental.Connect.code.error1");  //Invalid or expired Pairing Code

                    this.lblPairingCodeTip.SetText(this.Language.GetString("APC.PairingCode.More.Tip"), "link"); //您可以在 AirDroid Parental Control 或 AirDroid Kids iOS 的监督模式开启引导页面中找到配对码。<Link=more>了解更多</Link>
                    List<skItemInfo> skItemInfos = this.lblPairingCodeTip.GetItemList("link");
                    skItemInfos[0].LinkUrl = string.Format("https://parental-control.flashget.com/{0}/how-to-enable-full-mode-on-ios-device", this.Language.CurrentLanguage.LangWithoutRegion);

                    this.lblEnterPairingCode.skText = this.Language.GetString("Parental.Connect.code.title1"); //Enter the Pairing Code

                    int pairingCodePanelHeight = this.lblEnterPairingCode.Height + this.lblEnterPairingCode1.Height + this.lblPairingCodeTip.Height + 106;

                    this.pnlEnterPairingCode.Size = new Size(380, pairingCodePanelHeight);

                    this.SizeForWindow = new Size(380, pairingCodePanelHeight);
                    this.CenterDisplay();

                    this.pnlEnterPairingCode.Location = new Point(1, 0);

                    this.pnlEnterPairingCode.skBorderRadius = new skBorderRadius(5);
                    this.pnlEnterPairingCode.Controls.Add(this.lblEnterPairingCode);
                    this.pnlEnterPairingCode.Controls.Add(this.lblEnterPairingCode1);
                    this.pnlEnterPairingCode.Controls.Add(this.lblEnterPairingCode2);
                    this.pnlEnterPairingCode.Controls.Add(this.lblEnterPairingCode3);
                    this.pnlEnterPairingCode.Controls.Add(this.lblEnterPairingCode4);
                    this.pnlEnterPairingCode.Controls.Add(this.lblEnterPairingCode5);
                    this.pnlEnterPairingCode.Controls.Add(this.lblEnterPairingCode6);
                    this.pnlEnterPairingCode.Controls.Add(this.txtEnterPairingCode1);
                    this.pnlEnterPairingCode.Controls.Add(this.txtEnterPairingCode2);
                    this.pnlEnterPairingCode.Controls.Add(this.txtEnterPairingCode3);
                    this.pnlEnterPairingCode.Controls.Add(this.txtEnterPairingCode4);
                    this.pnlEnterPairingCode.Controls.Add(this.txtEnterPairingCode5);
                    this.pnlEnterPairingCode.Controls.Add(this.txtEnterPairingCode6);
                    this.pnlEnterPairingCode.Controls.Add(this.lblPairingCodeNotExistTip);
                    this.pnlEnterPairingCode.Controls.Add(this.lblPairingCodeTip);
                    this.pnlEnterPairingCode.Controls.Add(this.lblEnterPairingCodeLine);
                    this.pnlEnterPairingCode.Controls.Add(this.picLoading);



                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.lblEnterPairingCode);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.lblEnterPairingCode1);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.lblEnterPairingCode2);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.lblEnterPairingCode3);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.lblEnterPairingCode4);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.lblEnterPairingCode5);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.lblEnterPairingCode6);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.txtEnterPairingCode1);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.txtEnterPairingCode2);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.txtEnterPairingCode3);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.txtEnterPairingCode4);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.txtEnterPairingCode5);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.txtEnterPairingCode6);
                    //MapWindowLayoutToMac.MapPanelLayout(this.pnlEnterPairingCode, this.lblPairingCodeNotExistTip);
                    //MapWindowLayoutToMac.MapPanelLayout(this.pnlEnterPairingCode, this.lblPairingCodeTip);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.lblEnterPairingCodeLine);
                    MyDpi.LayoutSubviewFromWin(this.pnlEnterPairingCode, this.picLoading);

                    this.lblPairingCodeTip.Location = new Point(this.lblPairingCodeTip.Location.X, 30);
                    this.lblPairingCodeNotExistTip.Location = new Point(this.lblPairingCodeNotExistTip.Location.X, this.lblPairingCodeTip.Location.Y + this.lblPairingCodeTip.Height);

                    this.txtEnterPairingCode1.MaxLength = 1;
                    this.txtEnterPairingCode2.MaxLength = 1;
                    this.txtEnterPairingCode3.MaxLength = 1;
                    this.txtEnterPairingCode4.MaxLength = 1;
                    this.txtEnterPairingCode5.MaxLength = 1;
                    this.txtEnterPairingCode6.MaxLength = 1;

                    this.pnlEnterPairingCode.BringToFront();

                    this.TimerStop();
                    this.mTimer = TimerMgr.Create(0.5, SetskTextBoxHandle, "SetskTextBoxTimer", 1); //为了解决配对码弹窗 skTextBox闪动问题
                    break;

                case "Confirm":

                    this.lblConfirmEmail.skText = this.mEmail;

                    this.lblConfirmTitle.skText = string.Format(this.Language.GetString("Parental.Connect.code.title2"), this.mDeviceName); //此设备 ({0}) 将会被绑定至：

                    this.lblConfirmDescribe.skText = this.Language.GetString("Parental.Connect.code.text1"); //After you confirmed the binding and enabled the Supervision Mode, the connected devices will be supervised by the account holder.

                    mOKstr = this.Language.GetString("Common.Confirm"); // OK;
                    this.btnConfirmOK.skText = this.btnConfirmOK.skText = $"{this.mOKstr}(10s)";

                    this.btnConfirmCancel.skText = cancelStr;//Cancel

                    int confirmPanelHeight = this.lblConfirmTitle.Height + this.lblConfirmEmail.Height + lblConfirmDescribe.Height + btnConfirmOK.Height + 111;

                    this.SizeForWindow = new Size(480, confirmPanelHeight);

                    this.CenterDisplay();

                    this.lblConfirmTitle.Location = new Point(this.lblConfirmTitle.Location.X, 38);

                    this.lblConfirmEmail.Location = new Point(24, this.lblConfirmTitle.Location.Y + this.lblConfirmTitle.Height + 15);

                    this.lblConfirmDescribe.Location = new Point(24, this.lblConfirmEmail.Location.Y + this.lblConfirmEmail.Height + 15);

                    nfloat btnY = this.lblConfirmDescribe.Location.Y + this.lblConfirmDescribe.Height + 19;

                    this.btnConfirmOK.Padding = new Padding(0, 2, 0, 0);
                    this.btnConfirmCancel.Padding = new Padding(0, 2, 0, 0);

                    this.btnConfirmOK.Size = new Size(90, 28);
                    this.btnConfirmCancel.Size = new Size(90, 28);
                    
                    this.btnConfirmCancel.Location = new Point(this.pnlConfirm.Width - 20 - this.btnConfirmCancel.Width, btnY);
                    this.btnConfirmOK.Location = new Point(this.btnConfirmCancel.Location.X - 20 - this.btnConfirmOK.Width, btnY);

                    this.btnConfirmCancel.skBorderRadius = new skBorderRadius(14);
                    this.btnConfirmOK.skBorderRadius = new skBorderRadius(14);

                    this.pnlConfirm.Size = new Size(482, confirmPanelHeight);
                    this.pnlConfirm.Location = new Point(0, 0);

                    this.pnlConfirm.Controls.Add(this.lblConfirmTitle);
                    this.pnlConfirm.Controls.Add(this.lblConfirmEmail);
                    this.pnlConfirm.Controls.Add(this.lblConfirmDescribe);
                    this.pnlConfirm.Controls.Add(this.btnConfirmOK);
                    this.pnlConfirm.Controls.Add(this.btnConfirmCancel);


                    MyDpi.LayoutSubviewFromWin(this.pnlConfirm, this.lblConfirmTitle);
                    MyDpi.LayoutSubviewFromWin(this.pnlConfirm, this.lblConfirmEmail);
                    MyDpi.LayoutSubviewFromWin(this.pnlConfirm, this.lblConfirmDescribe);
                    MyDpi.LayoutSubviewFromWin(this.pnlConfirm, this.btnConfirmOK);
                    MyDpi.LayoutSubviewFromWin(this.pnlConfirm, this.btnConfirmCancel);

                    this.pnlConfirm.BringToFront();

                    this.TimerStop();
                    this.BtnConfirmCountDownHandle();

                    break;

                default:
                    break;
            }

            this.lblAccountExpiredTitle.skText = this.Language.GetString("err.account.expire"); //账号已过期
            this.lblAccountExpiredContent.skText = this.Language.GetString("APC.AccountExpiration.Tip"); //当前账号: {0} 已过期，请购买后重新执行开启监督模式操作。
            this.btnBuyNow.skText = this.Language.GetString("rs.purchase.button"); //立即购买
            this.btnBuyCancel.skText = cancelStr;

        }

        private void InitStyle()
        {
            this.skShowStatusBar = false;
            this.skShowButtonMax = false;
            this.skShowButtonMin = false;
            this.skShowButtonClose = true;

            this.skShowTitle = false;

            this.skTitle = null;
            this.skTitleFont = MyFont.CreateFont(9.75f, true);
            this.skTitleColor = Color.White;

            this.skBorderRadius = new skBorderRadius(2);
            //this.skTransparentImage = MyResource.GetImage("frm_bg_shadow.png");
            //this.skTransparentImagePadding = new Padding(6, 6, 6, 6);
            //this.skBorderStrokeColor = Color.White;
        }

        private void TimerStop()
        {
            if (this.mTimer != null)
            {
                this.mTimer.Dispose();
                this.mTimer = null;
            }
        }

        private delegate void SetskTextBoxHandler();
        /// <summary>显示配对码skTextBox</summary>
        private void SetskTextBoxHandle()
        {
            this.TimerStop();

            if (this.InvokeRequired)
            {
                this.Invoke(new SetskTextBoxHandler(SetskTextBoxHandle));
                return;
            }

            this.txtEnterPairingCode1.BringToFront();
            this.txtEnterPairingCode2.BringToFront();
            this.txtEnterPairingCode3.BringToFront();
            this.txtEnterPairingCode4.BringToFront();
            this.txtEnterPairingCode5.BringToFront();
            this.txtEnterPairingCode6.BringToFront();

            this.txtEnterPairingCode1.Visible = true;
            this.txtEnterPairingCode2.Visible = true;
            this.txtEnterPairingCode3.Visible = true;
            this.txtEnterPairingCode4.Visible = true;
            this.txtEnterPairingCode5.Visible = true;
            this.txtEnterPairingCode6.Visible = true;

            this.txtEnterPairingCode1.Focus();
        }

        /// <summary>居中显示</summary>
        private void CenterDisplay()
        {
            //if (this.Owner != null)
            //{
            //    MyForm.SetParentCenter(this, this.Owner);

            //    this.Location = new Point(this.Location.X, this.Location.Y - 60);
            //}
        }

        private delegate void BtnConfirmCountDownHandler();
        /// <summary>确认按钮倒计时</summary>
        private void BtnConfirmCountDownHandle()
        {
            this.TimerStop();

            if (this.InvokeRequired)
            {
                this.Invoke(new BtnConfirmCountDownHandler(BtnConfirmCountDownHandle));
                return;
            }

            if (this.timer_BtnConfirmTimeCount != null)
                return;

            this.btnConfirmOK.skTextColorDisable = Color.White;

            this.mCountDownSeconds = 9;
            this.timer_BtnConfirmTimeCount = new skTimer();

            this.timer_BtnConfirmTimeCount.Interval = 1000;

            this.timer_BtnConfirmTimeCount.Elapsed += timer_BtnConfirmTimeCount_Tick;

            this.timer_BtnConfirmTimeCount.Start();
        }


        private Color mTextPressColor = skColor.FromArgb(40, 41, 48);
        private void txtEnterPairingCode1_KeyPress(object sender, KeyEventArgs e)
        {
            if ((char)e.KeyData == (char)Keys.Enter)
            {
                CheckPairingCode();
                e.Handled = true;
            }

            this.KeyPressAction((char)e.KeyData, this.txtEnterPairingCode1, this.txtEnterPairingCode1, this.txtEnterPairingCode2, this.lblEnterPairingCode1);
        }

        private void txtEnterPairingCode2_KeyPress(object sender, KeyEventArgs e)
        {
            if ((char)e.KeyData == (char)Keys.Enter)
            {
                CheckPairingCode();
                e.Handled = true;
            }

            this.KeyPressAction((char)e.KeyData, this.txtEnterPairingCode1, this.txtEnterPairingCode2, this.txtEnterPairingCode3, this.lblEnterPairingCode2);
        }

        private void txtEnterPairingCode3_KeyPress(object sender, KeyEventArgs e)
        {
            if ((char)e.KeyData == (char)Keys.Enter)
            {
                CheckPairingCode();
                e.Handled = true;
            }

            this.KeyPressAction((char)e.KeyData, this.txtEnterPairingCode2, this.txtEnterPairingCode3, this.txtEnterPairingCode4, this.lblEnterPairingCode3);
        }

        private void txtEnterPairingCode4_KeyPress(object sender, KeyEventArgs e)
        {
            if ((char)e.KeyData == (char)Keys.Enter)
            {
                CheckPairingCode();
                e.Handled = true;
            }

            this.KeyPressAction((char)e.KeyData, this.txtEnterPairingCode3, this.txtEnterPairingCode4, this.txtEnterPairingCode5, this.lblEnterPairingCode4);
        }

        private void txtEnterPairingCode5_KeyPress(object sender, KeyEventArgs e)
        {
            if ((char)e.KeyData == (char)Keys.Enter)
            {
                CheckPairingCode();
                e.Handled = true;
            }

            this.KeyPressAction((char)e.KeyData, this.txtEnterPairingCode4, this.txtEnterPairingCode5, this.txtEnterPairingCode6, this.lblEnterPairingCode5);
        }

        private void txtEnterPairingCode6_KeyPress(object sender, KeyEventArgs e)
        {
            if ((char)e.KeyData == (char)Keys.Enter)
            {
                CheckPairingCode();
                e.Handled = true;
            }

            this.KeyPressAction((char)e.KeyData, this.txtEnterPairingCode5, this.txtEnterPairingCode6, this.txtEnterPairingCode6, this.lblEnterPairingCode6);
        }

        private void txtEnterPairingCode1_TextChanged(object sender, EventArgs e)
        {
            this.TextChangeAction(this.txtEnterPairingCode1, this.txtEnterPairingCode1, this.txtEnterPairingCode2);
        }

        private void txtEnterPairingCode2_TextChanged(object sender, EventArgs e)
        {
            this.TextChangeAction(this.txtEnterPairingCode1, this.txtEnterPairingCode2, this.txtEnterPairingCode3);
        }

        private void txtEnterPairingCode3_TextChanged(object sender, EventArgs e)
        {
            this.TextChangeAction(this.txtEnterPairingCode2, this.txtEnterPairingCode3, this.txtEnterPairingCode4);
        }

        private void txtEnterPairingCode4_TextChanged(object sender, EventArgs e)
        {
            this.TextChangeAction(this.txtEnterPairingCode3, this.txtEnterPairingCode4, this.txtEnterPairingCode5);
        }

        private void txtEnterPairingCode5_TextChanged(object sender, EventArgs e)
        {
            this.TextChangeAction(this.txtEnterPairingCode4, this.txtEnterPairingCode5, this.txtEnterPairingCode6);
        }

        private void txtEnterPairingCode6_TextChanged(object sender, EventArgs e)
        {
            this.TextChangeAction(this.txtEnterPairingCode5, this.txtEnterPairingCode6, this.txtEnterPairingCode6);
        }


        private void KeyPressAction(char keyChar, skTextBox frontTB, skTextBox currentTB, skTextBox nextTB, skLabel currentLbl)
        {
            if (keyChar == (char)Keys.Back)
            {
                if (string.IsNullOrEmpty(currentTB.Text))
                {
                    frontTB.Text = string.Empty;
                    frontTB.Focus();
                }
                else
                {
                    this.ReplyToNormalState(currentLbl);
                    currentTB.Focus();
                }
            }
            //else
            //{
            //    if (!IsNumber(keyChar))
            //        return;

            //    nextTB.Focus();

            //    currentTB.ForeColor = mTextPressColor;

            //    if (currentTB == this.txtEnterPairingCode6)
            //        CheckPairingCode();
            //}
        }

        private void TextChangeAction(skTextBox frontTB, skTextBox currentTB, skTextBox nextTB)
        {
            if (currentTB.Text == "" || currentTB.Text == string.Empty)
                currentTB.Focus();
            else
                nextTB.Focus();

            if (currentTB == this.txtEnterPairingCode6 && currentTB.Text != "" && currentTB.Text != string.Empty)
                CheckPairingCode();
        }

        private bool IsNumber(char keyChar)
        {
            bool result = false;

            if (keyChar >= 65296 && keyChar <= 65305 || keyChar >= (char)Keys.D0 && keyChar <= (char)Keys.D9 || (keyChar >= (char)Keys.NumPad0 && keyChar <= (char)Keys.NumPad9))
                result = true;

            return result;
        }

        private void ReplyToNormalState(skLabel label)
        {
            this.SetLableColor(false);

            this.lblPairingCodeNotExistTip.Visible = false;

            if (this.Height > 229)
                this.Height -= 10;
        }

        private void CheckPairingCode()
        {
            string strCode = string.Concat(this.txtEnterPairingCode1.Text.Replace(" ", ""), this.txtEnterPairingCode2.Text.Replace(" ", ""),
                this.txtEnterPairingCode3.Text.Replace(" ", ""), this.txtEnterPairingCode4.Text.Replace(" ", ""),
                this.txtEnterPairingCode5.Text.Replace(" ", ""), this.txtEnterPairingCode6.Text.Replace(" ", ""));

            if (string.IsNullOrEmpty(strCode))
                return;

            this.mPairingCode = strCode;

            this.SetControlState(true);

            ThreadMgr.Start(() =>
            {
                this.ThreadCheckPairingCode(strCode);

            });
        }

        private CustomInfo mCustomInfo = null;
        private KidProfileInfo mKidPorfileInfo = null;

        private void ThreadCheckPairingCode(string strCode)
        {
            bool result = false;
            ErrorType errorType = ErrorType.Code;
            try
            {
                if (NetworkInterface.GetIsNetworkAvailable())
                {
                    ServerCustomArgs<KidProfileInfo> args = null;

                    this.mCustomInfo = null;

                    if (!string.IsNullOrEmpty(strCode) && strCode.Length == 6)
                    {
                        strCode = strCode.Normalize(NormalizationForm.FormKC);

                        args = FGKidAPI.GetProfile(strCode);

                        if (args != null && args.Code == 1)
                            result = true;

                        if (args.Code == -99999) //自定义错误
                        {
                            this.mCustomInfo = args.custom_info;
                            errorType = ErrorType.Custom;
                        }

                        //if (args.Code == -2000901)  //不是vip用户
                        //{
                        //    this.mKidPorfileInfo = args.Data;
                        //    errorType = ErrorType.NotVip;
                        //}
                    }

                    if (result)
                    {
                        Email = args.Data.mail;

                        KidProfileInfo = args.Data;

                        PairingCode = this.mPairingCode;
                    }
                }
                else
                {
                    errorType = ErrorType.Net;
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ThreadCheckPairingCode");
            }
            finally
            {
                if (result)
                {
                    this.DialogResult = DialogResult.Yes;
                }
                else
                {
                    this.SetControlState(false);

                    this.SetErrorState(true, errorType);
                }
            }
        }

        private delegate void SetErrorStateHandler(bool isError, ErrorType errorType = ErrorType.Code);
        private void SetErrorState(bool isError, ErrorType errorType = ErrorType.Code)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetErrorStateHandler(SetErrorState), isError, errorType);
                return;
            }

            if (isError)
            {
                //this.txtEnterPairingCode6.TextChanged -= this.txtEnterPairingCode6_TextChanged;

                this.txtEnterPairingCode6.Focus();

                if (errorType == ErrorType.NotVip)
                {
                    this.lblAccountExpiredContent.skText = string.Format(this.Language.GetString("APC.AccountExpiration.Tip"), mKidPorfileInfo.mail); //当前账号: {0} 已过期，请购买后重新执行开启监督模式操作。

                    this.btnBuyCancel.Location = new Point(480 - 20 - this.btnBuyCancel.Width, this.lblAccountExpiredContent.Location.Y + this.lblAccountExpiredContent.Height + 18);
                    this.btnBuyNow.Location = new Point(this.btnBuyCancel.Location.X - 15 - this.btnBuyNow.Width, this.btnBuyCancel.Location.Y);

                    int panelHeight = this.lblAccountExpiredTitle.Height + this.lblAccountExpiredContent.Height + this.btnBuyNow.Height + 60;
                    this.Size = new Size(480, panelHeight + 28);

                    this.pnlAccountExpired.Size = new Size(480, panelHeight);
                    this.pnlAccountExpired.Location = new Point(1, 28);
                    this.pnlAccountExpired.Controls.Add(this.lblAccountExpiredTitle);
                    this.pnlAccountExpired.Controls.Add(this.lblAccountExpiredContent);
                    this.pnlAccountExpired.Controls.Add(this.btnBuyNow);
                    this.pnlAccountExpired.Controls.Add(this.btnBuyCancel);

                    this.pnlAccountExpired.BringToFront();

                    return;
                }

                if (errorType == ErrorType.Custom)
                {
                    UserBase.ShowCustomInfo(this.mCustomInfo, this);
                    return;
                }

                if (errorType == ErrorType.Net)
                    this.lblPairingCodeNotExistTip.skText = this.Language.GetString("rs.discount.error.tip"); //网络异常，请检查网络后重试

                //if (this.Height < 221)
                //    this.Height += 10;
            }

            if (errorType == ErrorType.Code)
            {
                this.SetLableColor(isError);
                this.lblPairingCodeNotExistTip.skText = this.Language.GetString("Parental.Connect.code.error1");  //Invalid or expired Pairing Code
            }

            this.lblPairingCodeNotExistTip.Visible = isError;
        }

        private void SetLableColor(bool isError)
        {
            Color boderStrokeColor = isError ? mErrorTipColor : mNormalTipColor;

            this.lblEnterPairingCode1.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode2.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode3.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode4.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode5.skBorderStrokeColor = boderStrokeColor;
            this.lblEnterPairingCode6.skBorderStrokeColor = boderStrokeColor;
        }

        private enum ErrorType
        {
            Net,
            Code,
            Custom,
            NotVip
        }

        private delegate void SetControlStateHandler(bool isLoading);
        /// <summary></summary>
        private void SetControlState(bool isLoading)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetControlStateHandler(SetControlState), isLoading);
                return;
            }

            this.picLoading.Visible = isLoading;

            this.lblEnterPairingCode.Enabled = !isLoading;

            this.txtEnterPairingCode1.Enabled = !isLoading;
            this.txtEnterPairingCode2.Enabled = !isLoading;
            this.txtEnterPairingCode3.Enabled = !isLoading;
            this.txtEnterPairingCode4.Enabled = !isLoading;
            this.txtEnterPairingCode5.Enabled = !isLoading;
            this.txtEnterPairingCode6.Enabled = !isLoading;
        }

        private void btnConfirmOK_Click(object sender, EventArgs e)
        {
            this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.PairingCodeConfirmOK);

            this.DialogResult = DialogResult.OK;
        }

        private void btnConfirmCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void btnEnter_Click(object sender, EventArgs e)
        {
            this.CheckPairingCode();
        }

        private void timer_BtnConfirmTimeCount_Tick(object sender, EventArgs e)
        {
            if (this.mCountDownSeconds != 0)
                this.btnConfirmOK.skText = $"{this.mOKstr}({this.mCountDownSeconds}s)";

            if (this.mCountDownSeconds > 0)
            {
                this.mCountDownSeconds--;
            }
            else
            {
                this.timer_BtnConfirmTimeCount.Stop();

                NSApplication.SharedApplication.BeginInvokeOnMainThread(() =>
                {
                    // 在这里更新 UI 或操作控件
                    this.btnConfirmOK.Enabled = true;
                    this.btnConfirmOK.skText = mOKstr;
                });
            }
        }

        private void btnBuyNow_Click(object sender, EventArgs e)
        {
            Common.OpenUrl(string.Format("https://www.flashget.com/{0}/pricing/parental-control", this.Language.CurrentLanguage.LangWithoutRegion));
        }

        private void btnBuyCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }

        private void lblPairingCodeTip_Click(object sender, EventArgs e)
        {
            skLabel skLabel = sender as skLabel;

            if (skLabel.Cursor == Cursors.Hand)
                this.mActionHelper.AddFGKid(tbActionModeKeyForFGKid.PairingCodeLearnMore);
        }
    }
}
