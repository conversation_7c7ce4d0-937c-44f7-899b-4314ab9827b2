<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1835994D-840A-4336-8784-E24C95AF719C}</ProjectGuid>
    <ProjectTypeGuids>{A3F8F2AB-B479-4A4A-A458-A89E7DC349F1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Exe</OutputType>
    <RootNamespace>FlashGet Kids Connector</RootNamespace>
    <MonoMacResourcePrefix>Resources</MonoMacResourcePrefix>
    <AssemblyName>FlashGet Kids Connector</AssemblyName>
    <ReleaseVersion>1.0.0</ReleaseVersion>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <SynchReleaseVersion>false</SynchReleaseVersion>
    <UseXamMacFullFramework>true</UseXamMacFullFramework>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug</OutputPath>
    <DefineConstants>__MACOS__;__UNIFIED__;DEBUG;MAC;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <Profiling>true</Profiling>
    <UseRefCounting>true</UseRefCounting>
    <UseSGen>true</UseSGen>
    <IncludeMonoRuntime>false</IncludeMonoRuntime>
    <CreatePackage>false</CreatePackage>
    <CodeSigningKey>Developer ID Application: Xiamen Tongbu Networks Co., Ltd. (J4745728M6)</CodeSigningKey>
    <EnableCodeSigning>false</EnableCodeSigning>
    <EnablePackageSigning>false</EnablePackageSigning>
    <PackageSigningKey>Developer ID Installer</PackageSigningKey>
    <I18n>cjk,mideast,other,rare,west</I18n>
    <XamMacArch>x86_64</XamMacArch>
    <PlatformTarget>anycpu</PlatformTarget>
    <AOTMode>None</AOTMode>
    <LinkMode>None</LinkMode>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType></DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\bin\Release</OutputPath>
    <DefineConstants>__MACOS__;__UNIFIED__;MAC;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <Profiling>false</Profiling>
    <UseRefCounting>true</UseRefCounting>
    <UseSGen>true</UseSGen>
    <IncludeMonoRuntime>true</IncludeMonoRuntime>
    <CreatePackage>true</CreatePackage>
    <CodeSigningKey>Developer ID Application</CodeSigningKey>
    <EnableCodeSigning>false</EnableCodeSigning>
    <EnablePackageSigning>false</EnablePackageSigning>
    <PackageSigningKey>Developer ID Installer</PackageSigningKey>
    <AOTMode>None</AOTMode>
    <LinkMode>None</LinkMode>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="Main.cs" />
    <Compile Include="AppDelegate.cs" />
    <Compile Include="AppDelegate.designer.cs">
      <DependentUpon>AppDelegate.cs</DependentUpon>
    </Compile>    
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Mac\Xamarin.Mac.CSharp.targets" />
  <ItemGroup>
    <Reference Include="System.Core" />
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data" />
    <Reference Include="Xamarin.Mac" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Info.plist" />
    <BundleResource Include="..\FlashGetKid\Resources\pic_connect_succes.png">
      <Link>Resources\pic_connect_succes.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_solid_red_40.png">
      <Link>Resources\btn_solid_red_40.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_4_sure.png">
      <Link>Resources\btn_4_sure.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_guide_line.png">
      <Link>Resources\pic_guide_line.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\Inter-Medium.ttf">
      <Link>Resources\Inter-Medium.ttf</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_line_blue.png">
      <Link>Resources\btn_line_blue.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_connect_fail.png">
      <Link>Resources\pic_connect_fail.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_blue_line_4.png">
      <Link>Resources\btn_blue_line_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\gif_remove.gif">
      <Link>Resources\gif_remove.gif</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_select_dis.png">
      <Link>Resources\btn_select_dis.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_bg_remove_nor.png">
      <Link>Resources\pic_bg_remove_nor.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_tips_2.png">
      <Link>Resources\ic_tips_2.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_min_3.png">
      <Link>Resources\btn_min_3.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_tips_3.png">
      <Link>Resources\ic_tips_3.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_tips_1.png">
      <Link>Resources\ic_tips_1.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_language.png">
      <Link>Resources\ic_language.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_select_hover.png">
      <Link>Resources\btn_select_hover.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_4_sure_big.png">
      <Link>Resources\btn_4_sure_big.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_bg_grey2.png">
      <Link>Resources\btn_bg_grey2.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_setting.png">
      <Link>Resources\pic_setting.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_white_4.png">
      <Link>Resources\btn_white_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_bg_open_nor.png">
      <Link>Resources\pic_bg_open_nor.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_black_line_4.png">
      <Link>Resources\btn_black_line_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_connect_succes_disable.png">
      <Link>Resources\pic_connect_succes_disable.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\frm_bg_shadow.png">
      <Link>Resources\frm_bg_shadow.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_success.png">
      <Link>Resources\ic_success.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\setting_setup_nor.png">
      <Link>Resources\setting_setup_nor.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\kidloading.gif">
      <Link>Resources\kidloading.gif</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_guide_01.png">
      <Link>Resources\pic_guide_01.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_guide_03.png">
      <Link>Resources\pic_guide_03.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\Inter-Regular.ttf">
      <Link>Resources\Inter-Regular.ttf</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_select_pre.png">
      <Link>Resources\btn_select_pre.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_guide_02.png">
      <Link>Resources\pic_guide_02.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\gif_turn_on.gif">
      <Link>Resources\gif_turn_on.gif</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_4_cancel.png">
      <Link>Resources\btn_4_cancel.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_register_4.png">
      <Link>Resources\btn_register_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_select_nor.png">
      <Link>Resources\btn_select_nor.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_close_3.png">
      <Link>Resources\btn_close_3.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_connect_usb.png">
      <Link>Resources\pic_connect_usb.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_arrow_up.png">
      <Link>Resources\ic_arrow_up.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_4_close.png">
      <Link>Resources\ic_4_close.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_recover.png">
      <Link>Resources\pic_recover.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\setting_about_nor.png">
      <Link>Resources\setting_about_nor.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\Manifest.db">
      <Link>Resources\Manifest.db</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\logo.png">
      <Link>Resources\logo.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\gif_connect.gif">
      <Link>Resources\gif_connect.gif</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\setting_leftnav_bg.png">
      <Link>Resources\setting_leftnav_bg.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_fireworks.png">
      <Link>Resources\ic_fireworks.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_checkbox.png">
      <Link>Resources\btn_checkbox.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_4_language.png">
      <Link>Resources\ic_4_language.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_4_hide.png">
      <Link>Resources\ic_4_hide.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_4_more_bule_nor.png">
      <Link>Resources\ic_4_more_bule_nor.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_arrow_down.png">
      <Link>Resources\ic_arrow_down.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\logo_48.png">
      <Link>Resources\logo_48.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_bg_remove_hover.png">
      <Link>Resources\pic_bg_remove_hover.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\bg_selection.png">
      <Link>Resources\bg_selection.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\btn_blue_4.png">
      <Link>Resources\btn_blue_4.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_connect.png">
      <Link>Resources\pic_connect.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_4_back_nor.png">
      <Link>Resources\ic_4_back_nor.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_front_disable.png">
      <Link>Resources\pic_front_disable.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\setting_setup_check.png">
      <Link>Resources\setting_setup_check.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\setting_about_check.png">
      <Link>Resources\setting_about_check.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_guide_usb.png">
      <Link>Resources\pic_guide_usb.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_guide_find_2.png">
      <Link>Resources\pic_guide_find_2.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_front_enable.png">
      <Link>Resources\pic_front_enable.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_guide_find_3.png">
      <Link>Resources\pic_guide_find_3.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\logo_all.ico">
      <Link>Resources\logo_all.ico</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_bg_open_hover.png">
      <Link>Resources\pic_bg_open_hover.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_4_feedback.png">
      <Link>Resources\ic_4_feedback.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_guide_find_1.png">
      <Link>Resources\pic_guide_find_1.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_tips.png">
      <Link>Resources\ic_tips.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\Inter-SemiBold.ttf">
      <Link>Resources\Inter-SemiBold.ttf</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\pic_congretuation.png">
      <Link>Resources\pic_congretuation.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Resources\ic_selected.png">
      <Link>Resources\ic_selected.png</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\hu.lang">
      <Link>Resources\Lang\hu.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\tr.lang">
      <Link>Resources\Lang\tr.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\ja.lang">
      <Link>Resources\Lang\ja.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\ms.lang">
      <Link>Resources\Lang\ms.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\zh-CN.lang">
      <Link>Resources\Lang\zh-CN.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\es-ES.lang">
      <Link>Resources\Lang\es-ES.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\ru.lang">
      <Link>Resources\Lang\ru.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\de.lang">
      <Link>Resources\Lang\de.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\hr.lang">
      <Link>Resources\Lang\hr.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\sv-SE.lang">
      <Link>Resources\Lang\sv-SE.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\it.lang">
      <Link>Resources\Lang\it.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\en.lang">
      <Link>Resources\Lang\en.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\fr.lang">
      <Link>Resources\Lang\fr.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\zh-TW.lang">
      <Link>Resources\Lang\zh-TW.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\ko.lang">
      <Link>Resources\Lang\ko.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\pt-BR.lang">
      <Link>Resources\Lang\pt-BR.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\hi.lang">
      <Link>Resources\Lang\hi.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\el.lang">
      <Link>Resources\Lang\el.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\id.lang">
      <Link>Resources\Lang\id.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\cs.lang">
      <Link>Resources\Lang\cs.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\vi.lang">
      <Link>Resources\Lang\vi.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\ar.lang">
      <Link>Resources\Lang\ar.lang</Link>
    </BundleResource>
    <BundleResource Include="..\FlashGetKid\Lang\th.lang">
      <Link>Resources\Lang\th.lang</Link>
    </BundleResource>
    <BundleResource Include="Resources\ic_selected22.png" />
    <BundleResource Include="Resources\btn_nav_close_4.png" />
    <BundleResource Include="Resources\btn_nav_minimize_4.png" />
    <BundleResource Include="Resources\kids.icns" />
    <BundleResource Include="..\AppUnion\ResourcesUrl\weburl_FlashGetKidsConnector">
      <Link>Resources\weburl_FlashGetKidsConnector</Link>
    </BundleResource>
  </ItemGroup>
  <ItemGroup>
    <Content Include="English.lproj\InfoPlist.strings" />
    <Content Include="zh_CN.lproj\InfoPlist.strings" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android_Mac.csproj">
      <Project>{A2CAD0B7-AC6D-4E3B-9E3E-EBD69C4871C1}</Project>
      <Name>Android_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc_Mac.csproj">
      <Project>{3D7568EF-2BED-4B71-BBFA-D2F35BE5BF00}</Project>
      <Name>CoreMisc_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag_Mac.csproj">
      <Project>{338F4272-5C9F-47B6-B100-5634EF0D1DDE}</Project>
      <Name>CoreTag_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtil_Mac.csproj">
      <Project>{913145D8-1271-42F3-BB1A-5D7694D6CA3C}</Project>
      <Name>CoreUtil_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone_Mac.csproj">
      <Project>{FB6055F3-5096-4D14-A2FE-78571A23C920}</Project>
      <Name>iPhone_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf_Mac.csproj">
      <Project>{C8C2D342-F711-47C4-95CB-0AF620DA719B}</Project>
      <Name>ProtoBuf_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\SharpZLib_Mac.csproj">
      <Project>{CBCC1A89-433D-4102-97F5-A68A500920EF}</Project>
      <Name>SharpZLib_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\System.Web\System.Web_Mac.csproj">
      <Project>{C5E45B23-1A73-418C-B1CA-B35D970B8D05}</Project>
      <Name>System.Web_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\zlib\zlib_Mac.csproj">
      <Project>{0E70E2EA-72A9-4406-8954-95D663386663}</Project>
      <Name>zlib_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreKid_Mac\CoreKid_Mac.csproj">
      <Project>{AD5EBBD5-1A3E-4532-A1B2-F8C10003DFA1}</Project>
      <Name>CoreKid_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="FlashGetKid_UI.csproj">
      <Project>{0E0C80D7-8C78-4F0F-B8FC-320F5C97F3C9}</Project>
      <Name>FlashGetKid_UI</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <InterfaceDefinition Include="MainMenu.xib" />
  </ItemGroup>
</Project>