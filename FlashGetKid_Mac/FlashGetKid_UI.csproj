<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0E0C80D7-8C78-4F0F-B8FC-320F5C97F3C9}</ProjectGuid>
    <ProjectTypeGuids>{A3F8F2AB-B479-4A4A-A458-A89E7DC349F1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <RootNamespace>iTong.AppUnion</RootNamespace>
    <AssemblyName>AppUnionFG</AssemblyName>
    <MonoMacResourcePrefix>Resources</MonoMacResourcePrefix>
    <ReleaseVersion>2.0.0</ReleaseVersion>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <UseXamMacFullFramework>true</UseXamMacFullFramework>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug</OutputPath>
    <DefineConstants>__UNIFIED__;__MACOS__;DEBUG;MAC;IS_ITONG;MAC40;LOG1;NET452;AB;FG;TEST;MAC1014X;MAC1015X;MAC_FG;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <EnableCodeSigning>false</EnableCodeSigning>
    <CreatePackage>false</CreatePackage>
    <EnablePackageSigning>false</EnablePackageSigning>
    <IncludeMonoRuntime>false</IncludeMonoRuntime>
    <UseSGen>false</UseSGen>
    <HttpClientHandler>HttpClientHandler</HttpClientHandler>
    <TlsProvider>Default</TlsProvider>
    <LinkMode>None</LinkMode>
    <XamMacArch></XamMacArch>
    <AOTMode>None</AOTMode>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <PackageSigningKey></PackageSigningKey>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>..\bin\Release</OutputPath>
    <DefineConstants>__UNIFIED__;__MACOS__;DEBUG;MAC;IS_ITONG;MAC40;LOG1;NET452;AB;FG;TEST;MAC1014X;MAC1015X;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <EnableCodeSigning>false</EnableCodeSigning>
    <CreatePackage>false</CreatePackage>
    <EnablePackageSigning>false</EnablePackageSigning>
    <IncludeMonoRuntime>false</IncludeMonoRuntime>
    <UseSGen>false</UseSGen>
    <HttpClientHandler>HttpClientHandler</HttpClientHandler>
    <TlsProvider>Default</TlsProvider>
    <XamMacArch></XamMacArch>
    <XamMacArch></XamMacArch>
    <AOTMode>None</AOTMode>
    <PlatformTarget>anycpu</PlatformTarget>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <CodeSigningKey></CodeSigningKey>
    <CodeSigningKey></CodeSigningKey>
    <LinkMode>None</LinkMode>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Drawing" />
    <Reference Include="Mono.Data.Sqlite" />
    <Reference Include="System.Data" />
    <Reference Include="Xamarin.Mac" />
    <Reference Include="System.Security" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android_Mac.csproj">
      <Project>{A2CAD0B7-AC6D-4E3B-9E3E-EBD69C4871C1}</Project>
      <Name>Android_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc_Mac.csproj">
      <Project>{3D7568EF-2BED-4B71-BBFA-D2F35BE5BF00}</Project>
      <Name>CoreMisc_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag_Mac.csproj">
      <Project>{338F4272-5C9F-47B6-B100-5634EF0D1DDE}</Project>
      <Name>CoreTag_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtil_Mac.csproj">
      <Project>{913145D8-1271-42F3-BB1A-5D7694D6CA3C}</Project>
      <Name>CoreUtil_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone_Mac.csproj">
      <Project>{FB6055F3-5096-4D14-A2FE-78571A23C920}</Project>
      <Name>iPhone_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf_Mac.csproj">
      <Project>{C8C2D342-F711-47C4-95CB-0AF620DA719B}</Project>
      <Name>ProtoBuf_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\SharpZLib_Mac.csproj">
      <Project>{CBCC1A89-433D-4102-97F5-A68A500920EF}</Project>
      <Name>SharpZLib_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\System.Web\System.Web_Mac.csproj">
      <Project>{C5E45B23-1A73-418C-B1CA-B35D970B8D05}</Project>
      <Name>System.Web_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\WebSocket4NetSource\WebSocket4Net_Mac.csproj">
      <Project>{6920447F-76B1-4739-822E-9CE3A2882718}</Project>
      <Name>WebSocket4Net_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreKid_Mac\CoreKid_Mac.csproj">
      <Project>{AD5EBBD5-1A3E-4532-A1B2-F8C10003DFA1}</Project>
      <Name>CoreKid_Mac</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="zKid\kidDriveInstall.designer.cs" />
    <Compile Include="zKid\kidLanguageChange.designer.cs" />
    <Compile Include="zKid\kidDriveInstall.cs" />
    <Compile Include="zKid\kidPopup.designer.cs" />
    <Compile Include="zKid\kidLanguageChange.cs" />
    <Compile Include="zKid\kidMainForm.cs" />
    <Compile Include="zKid\kidMainForm_Monitor.cs" />
    <Compile Include="zKid\kidPopup.cs" />
    <Compile Include="zKid\kidMainForm_Device.cs" />
    <Compile Include="zKid\kidMainForm.Designer.cs" />
    <Compile Include="zKid\zClass\KidCommon.cs" />
    <Compile Include="..\AppUnion\zFormFunc\frmBase.cs">
      <Link>zFormFunc\frmBase.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zServer\API\Info\InfoForKid.cs">
      <Link>zServer\Info\InfoForKid.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zServer\TD\tdActionItem.cs">
      <Link>zServer\TD\tdActionItem.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zServer\TD\tdActionModeKey.cs">
      <Link>zServer\TD\tdActionModeKey.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zServer\TD\tdActionHelper.cs">
      <Link>zServer\TD\tdActionHelper.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zServer\API\Url\MyUrlForFGKid.cs">
      <Link>zServer\API\Url\MyUrlForFGKid.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zSocket\Enum\skDeviceType.cs">
      <Link>zServer\API\skDeviceType.cs</Link>
    </Compile>
    <Compile Include="zServer\API\skDevice.cs" />
    <Compile Include="zServer\API\skLbsInfo.cs" />
    <Compile Include="zServer\API\skLoginInfo.cs" />
    <Compile Include="..\AppUnion\zServer\API\Url\MyUrlForRs.cs">
      <Link>zServer\API\Url\MyUrlForRs.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zClass\MyClass.cs">
      <Link>zServer\API\MyClass.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zCommon\AES.cs">
      <Link>zServer\API\AES.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zSocket\Enum\skEncryptType.cs">
      <Link>zServer\API\skEncryptType.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zCommon\RSA.cs">
      <Link>zServer\API\RSA.cs</Link>
    </Compile>
    <Compile Include="zServer\API\_UserBase.cs" />
    <Compile Include="zServer\API\MyApp.cs" />
    <Compile Include="zServer\API\FGKidAPI.cs" />
    <Compile Include="..\AppUnion\zClass\SettingMgr\KeyName_BIZ.cs">
      <Link>zKid\zClass\KeyName_BIZ.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zClass\SettingMgr\KeyName_RS.cs">
      <Link>zKid\zClass\KeyName_RS.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zClass\SettingMgr\SettingMgr.cs">
      <Link>zKid\zClass\SettingMgr.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zSocket\Info\AppBaseInfo.cs">
      <Link>zServer\Info\AppBaseInfo.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zSocket\Info\AppDetailInfo.cs">
      <Link>zServer\Info\AppDetailInfo.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zSocket\Info\AppUserInfo.cs">
      <Link>zServer\Info\AppUserInfo.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zSocket\Enum\skModeType.cs">
      <Link>zServer\Info\skModeType.cs</Link>
    </Compile>
    <Compile Include="zKid\zClass\MyRS.cs" />
    <Compile Include="zServer\API\skDevice_Info.cs" />
    <Compile Include="zKid\zClass\_ClassExtend.cs" />
    <Compile Include="zServer\API\Url\MyUrlForKid.cs" />
    <Compile Include="zServer\API\Url\MyUrlExtention.cs" />
    <Compile Include="zServer\API\Url\MyUrl.cs" />
    <Compile Include="..\AppUnion\zServer\API\Url\MyUrlForPersonal.cs">
      <Link>zServer\API\Url\MyUrlForPersonal.cs</Link>
    </Compile>
    <Compile Include="zKid\zClass\ChargeHelperForCast.cs" />
    <Compile Include="..\AppUnion\zWebRTC\MyLog.cs">
      <Link>MyLog.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zWebRTC\TimerMgr.cs">
      <Link>TimerMgr.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zServer\API\Info\MyTest.cs">
      <Link>MyTest.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zFormFunc\MsgBoxMgr.cs">
      <Link>zFormFunc\MsgBoxMgr.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zFormFunc\frmMessage.cs">
      <Link>zFormFunc\frmMessage.cs</Link>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\AppUnion\ResourcesUrl\weburl_FlashGetKidsConnector">
      <Link>ResourcesUrl\weburl_FlashGetKidsConnector</Link>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Mac\Xamarin.Mac.CSharp.targets" />
  <ProjectExtensions>
    <MonoDevelop>
      <Properties>
        <Policies>
          <TextStylePolicy TabWidth="4" IndentWidth="4" RemoveTrailingWhitespace="True" NoTabsAfterNonTabs="False" EolMarker="Native" FileWidth="80" TabsToSpaces="True" scope="text/plain" />
          <VersionControlPolicy>
            <CommitMessageStyle Header="" Indent="" FirstFilePrefix="* " FileSeparator=":&#xA;* " LastFilePostfix=": " LineAlign="2" InterMessageLines="1" IncludeDirectoryPaths="False" Wrap="True" />
          </VersionControlPolicy>
          <XmlFormattingPolicy scope="application/xml">
            <DefaultFormat OmitXmlDeclaration="False" IndentContent="True" AttributesInNewLine="False" MaxAttributesPerLine="10" WrapAttributes="False" AlignAttributes="False" AlignAttributeValues="False" QuoteChar="&quot;" SpacesBeforeAssignment="0" SpacesAfterAssignment="0" EmptyLinesBeforeStart="0" EmptyLinesAfterStart="0" EmptyLinesBeforeEnd="0" EmptyLinesAfterEnd="0" />
          </XmlFormattingPolicy>
          <CSharpFormattingPolicy IndentBlock="True" IndentBraces="False" IndentSwitchSection="True" IndentSwitchCaseSection="True" LabelPositioning="OneLess" NewLinesForBracesInTypes="True" NewLinesForBracesInMethods="True" NewLinesForBracesInProperties="True" NewLinesForBracesInAccessors="True" NewLinesForBracesInAnonymousMethods="True" NewLinesForBracesInControlBlocks="True" NewLinesForBracesInAnonymousTypes="True" NewLinesForBracesInObjectCollectionArrayInitializers="True" NewLineForElse="True" NewLineForCatch="True" NewLineForFinally="True" NewLineForMembersInObjectInit="True" NewLineForMembersInAnonymousTypes="True" NewLineForClausesInQuery="True" SpacingAfterMethodDeclarationName="False" SpaceWithinMethodDeclarationParenthesis="False" SpaceBetweenEmptyMethodDeclarationParentheses="False" SpaceAfterMethodCallName="False" SpaceWithinMethodCallParentheses="False" SpaceBetweenEmptyMethodCallParentheses="False" SpaceAfterControlFlowStatementKeyword="True" SpaceWithinExpressionParentheses="False" SpaceWithinCastParentheses="False" SpaceWithinOtherParentheses="False" SpaceAfterCast="False" SpacesIgnoreAroundVariableDeclaration="False" SpaceBeforeOpenSquareBracket="False" SpaceBetweenEmptySquareBrackets="False" SpaceWithinSquareBrackets="False" SpaceAfterColonInBaseTypeDeclaration="True" SpaceAfterComma="True" SpaceAfterDot="False" SpaceAfterSemicolonsInForStatement="True" SpaceBeforeColonInBaseTypeDeclaration="True" SpaceBeforeComma="False" SpaceBeforeDot="False" SpaceBeforeSemicolonsInForStatement="False" SpacingAroundBinaryOperator="Single" WrappingPreserveSingleLine="True" WrappingKeepStatementsOnSingleLine="True" NewLinesForBracesInLambdaExpressionBody="False" scope="text/x-csharp" />
          <NameConventionPolicy>
            <Rules>
              <NamingRule Name="Namespaces" AffectedEntity="Namespace" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Types" AffectedEntity="Class, Struct, Enum, Delegate" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Interfaces" AffectedEntity="Interface" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredPrefixes>
                  <String>I</String>
                </RequiredPrefixes>
              </NamingRule>
              <NamingRule Name="Attributes" AffectedEntity="CustomAttributes" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredSuffixes>
                  <String>Attribute</String>
                </RequiredSuffixes>
              </NamingRule>
              <NamingRule Name="Event Arguments" AffectedEntity="CustomEventArgs" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredSuffixes>
                  <String>EventArgs</String>
                </RequiredSuffixes>
              </NamingRule>
              <NamingRule Name="Exceptions" AffectedEntity="CustomExceptions" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredSuffixes>
                  <String>Exception</String>
                </RequiredSuffixes>
              </NamingRule>
              <NamingRule Name="Methods" AffectedEntity="Methods" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Static Readonly Fields" AffectedEntity="ReadonlyField" VisibilityMask="Internal, Protected, Public" NamingStyle="PascalCase" IncludeInstanceMembers="False" IncludeStaticEntities="True" />
              <NamingRule Name="Fields (Non Private)" AffectedEntity="Field" VisibilityMask="Internal, Protected, Public" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="ReadOnly Fields (Non Private)" AffectedEntity="ReadonlyField" VisibilityMask="Internal, Protected, Public" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="False" />
              <NamingRule Name="Fields (Private)" AffectedEntity="Field, ReadonlyField" VisibilityMask="Private" NamingStyle="CamelCase" IncludeInstanceMembers="True" IncludeStaticEntities="False">
                <AllowedPrefixes>
                  <String>_</String>
                  <String>m_</String>
                </AllowedPrefixes>
              </NamingRule>
              <NamingRule Name="Static Fields (Private)" AffectedEntity="Field" VisibilityMask="Private" NamingStyle="CamelCase" IncludeInstanceMembers="False" IncludeStaticEntities="True" />
              <NamingRule Name="ReadOnly Fields (Private)" AffectedEntity="ReadonlyField" VisibilityMask="Private" NamingStyle="CamelCase" IncludeInstanceMembers="True" IncludeStaticEntities="False">
                <AllowedPrefixes>
                  <String>_</String>
                  <String>m_</String>
                </AllowedPrefixes>
              </NamingRule>
              <NamingRule Name="Constant Fields" AffectedEntity="ConstantField" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Properties" AffectedEntity="Property" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Events" AffectedEntity="Event" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Enum Members" AffectedEntity="EnumMember" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Parameters" AffectedEntity="Parameter" VisibilityMask="VisibilityMask" NamingStyle="CamelCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Type Parameters" AffectedEntity="TypeParameter" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredPrefixes>
                  <String>T</String>
                </RequiredPrefixes>
              </NamingRule>
            </Rules>
          </NameConventionPolicy>
          <TextStylePolicy FileWidth="120" TabWidth="4" IndentWidth="4" RemoveTrailingWhitespace="True" EolMarker="Native" TabsToSpaces="True" NoTabsAfterNonTabs="True" scope="text/x-fsharp" />
          <TextStylePolicy TabWidth="4" IndentWidth="4" RemoveTrailingWhitespace="True" NoTabsAfterNonTabs="False" EolMarker="Native" FileWidth="80" TabsToSpaces="True" scope="text/x-csharp" />
          <TextStylePolicy TabsToSpaces="False" RemoveTrailingWhitespace="True" NoTabsAfterNonTabs="False" EolMarker="Native" FileWidth="80" TabWidth="8" IndentWidth="8" scope="application/vnd.apple-interface-builder" />
          <XmlFormattingPolicy scope="application/vnd.apple-interface-builder">
            <DefaultFormat OmitXmlDeclaration="False" IndentContent="True" AttributesInNewLine="False" MaxAttributesPerLine="10" WrapAttributes="False" AlignAttributes="False" AlignAttributeValues="False" QuoteChar="&quot;" SpacesBeforeAssignment="0" SpacesAfterAssignment="0" EmptyLinesBeforeStart="0" EmptyLinesAfterStart="0" EmptyLinesBeforeEnd="0" EmptyLinesAfterEnd="0" />
          </XmlFormattingPolicy>
          <TextStylePolicy TabsToSpaces="False" RemoveTrailingWhitespace="True" NoTabsAfterNonTabs="False" EolMarker="Native" FileWidth="80" TabWidth="8" IndentWidth="8" scope="application/xml" />
        </Policies>
      </Properties>
    </MonoDevelop>
  </ProjectExtensions>
</Project>