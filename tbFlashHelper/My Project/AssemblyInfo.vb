﻿Imports System
Imports System.Reflection
Imports System.Runtime.InteropServices

' 有关程序集的常规信息通过下列特性集
' 控制。更改这些特性值可修改
' 与程序集关联的信息。

' 查看程序集特性的值

<Assembly: AssemblyTitle("iClover 刷机组件")> 
<Assembly: AssemblyDescription("")> 
<Assembly: AssemblyCompany("Xiamen Tongbu Networks Co., Ltd.")> 
<Assembly: AssemblyProduct("iClover")> 
<Assembly: AssemblyCopyright("Copyright © 2013 Tongbu Networks")> 
<Assembly: AssemblyTrademark("")> 

<Assembly: ComVisible(False)>

'如果此项目向 COM 公开，则下列 GUID 用于类型库的 ID
<Assembly: Guid("d4e80a57-373b-48ec-9d00-9ad98ce0bdf5")> 

' 程序集的版本信息由下面四个值组成: 
'
'      主版本
'      次版本
'      生成号
'      修订号
'
' 可以指定所有这些值，也可以使用“生成号”和“修订号”的默认值，
' 方法是按如下所示使用“*”: 
' <Assembly: AssemblyVersion("1.0.*")> 

<Assembly: AssemblyVersion("*******")> 
<Assembly: AssemblyFileVersion("*******")> 
