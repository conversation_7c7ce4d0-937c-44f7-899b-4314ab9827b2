﻿Imports System.IO
Imports System.Text

Public Class FlashProcHelper


    Private Shared mAppFolder As String = String.Empty

    ''' <summary>
    ''' 获取应用程序启动的目录，以“\”结尾
    ''' </summary> 
    Public Shared ReadOnly Property AppFolder() As String
        Get
            Return mAppFolder
        End Get
    End Property

    Private Shared mExceptionFolder As String = ""
    ''' <summary>
    ''' 异常目录
    ''' </summary>
    Public Shared ReadOnly Property ExceptionFolder() As String
        Get
            If mExceptionFolder.Length > 0 Then
                Return mExceptionFolder
            End If

            mExceptionFolder = Path.Combine(FlashProcHelper.AppFolder, "Exception")

            Return mExceptionFolder
        End Get
    End Property

    Shared Sub New()
        mAppFolder = AppDomain.CurrentDomain.BaseDirectory.TrimEnd("\"c) & "\"
        If Not System.IO.Directory.Exists(mAppFolder) Then
            mAppFolder = System.Windows.Forms.Application.StartupPath.TrimEnd("\"c) & "\"
            If Not System.IO.Directory.Exists(mAppFolder) Then
                mAppFolder = Environment.CurrentDirectory.TrimEnd("\"c) & "\"
            End If
        End If
    End Sub

    Public Shared Sub WaitSeconds(ByVal timer As Double)

        Dim dt As Date = Now
        Dim timeOut As Long = CLng(timer * 1000)
        Dim isBackground As Boolean = False
        Try
            isBackground = Threading.Thread.CurrentThread.IsBackground
        Catch ex As Exception
        End Try

        Do
            If Not isBackground Then
                System.Windows.Forms.Application.DoEvents()
            End If

            System.Threading.Thread.Sleep(10)
        Loop Until (Now.Subtract(dt).TotalMilliseconds > timeOut)

    End Sub

    Private Shared lockException As New Object()
    Public Shared Sub LogException(ByVal strText As String, ByVal funcTitle As String)
        If String.IsNullOrEmpty(strText) Then
            Return
        End If

        SyncLock lockException
            Try
                Dim strPath As String = FlashProcHelper.ExceptionFolder
                If Not Directory.Exists(strPath) Then
                    Directory.CreateDirectory(strPath)
                End If

                If Not IsTestMode() Then
                    Dim strLowerText As String = strText.ToLower()

                    '去除可能包含敏感信息的AirDroid日志
                    If strLowerText.Contains("account") OrElse strLowerText.Contains("token") OrElse _
                       strLowerText.Contains("password") OrElse strLowerText.Contains("pwd") OrElse _
                       strLowerText.Contains("acct") OrElse strLowerText.Contains("contact") Then

                        Dim strIdentify As String = "?"
                        Dim iStart As Integer = strLowerText.IndexOf(strIdentify)
                        If iStart < 0 OrElse iStart > strText.Length - 1 Then
                            Return
                        End If

                        '如果是包含问号的， 则去掉参数，只保留问号前面的链接，方便追踪问题

                        strText = strText.Substring(0, iStart - 1)
                    End If
                End If

                strPath = Path.Combine(strPath, String.Format("Exception{0}.txt", Date.Now.ToString("yyyyMMdd")))
                Using writer As New StreamWriter(strPath, True, Encoding.UTF8)
                    writer.WriteLine(String.Format("------------Time:{0}---------------", DateTime.Now.ToString()))
                    If Not String.IsNullOrEmpty(funcTitle) Then
                        writer.WriteLine("Function Name:" & funcTitle)
                    End If
                    writer.WriteLine(strText)
                    writer.Flush()
                End Using

            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine(ex.ToString)
            End Try
        End SyncLock
    End Sub

    Public Shared Function IsTestMode() As Boolean
        If File.Exists(Path.Combine(FlashProcHelper.AppFolder, "Test.dll")) Then
            Return True
        End If

        Return False
    End Function

    Public Shared Function IsDebug() As Boolean
        If File.Exists(Path.Combine(FlashProcHelper.AppFolder, "debug.dll")) Then
            Return True
        End If

        Return False
    End Function


    Private Shared lockerObj As New Object()
    Public Shared Sub SetMsg(ByVal text As String)
        Try
            Dim strAppFolder = AppDomain.CurrentDomain.BaseDirectory.TrimEnd("\"c) & "\"
            If Not System.IO.Directory.Exists(strAppFolder) Then
                strAppFolder = System.Windows.Forms.Application.StartupPath.TrimEnd("\"c) & "\"
                If Not System.IO.Directory.Exists(strAppFolder) Then
                    strAppFolder = Environment.CurrentDirectory.TrimEnd("\"c) & "\"
                End If
            End If

            Dim strPath As String = Path.Combine(strAppFolder, "FlashLogs")
            SyncLock lockerObj
                Try
                    If Not Directory.Exists(strPath) Then
                        Directory.CreateDirectory(strPath)
                    End If
                    Using writer As New StreamWriter(((strPath & Convert.ToString("\")) + "Flash" + DateTime.Now.ToString("yyyyMMdd")) + ".txt", True, Encoding.UTF8)
                        writer.WriteLine("------------Time:" + DateTime.Now.ToString() + "---------------")
                        writer.WriteLine(text)
                        writer.Flush()
                        writer.Close()
                    End Using
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex)
                End Try
            End SyncLock
        Catch ex As Exception
        End Try
    End Sub



End Class
