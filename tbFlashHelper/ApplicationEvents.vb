﻿Imports System.IO
Imports System.Text

Namespace My

    ' 以下事件可用于 MyApplication: 
    ' 
    ' Startup:  应用程序启动时在创建启动窗体之前引发。
    ' Shutdown:  在关闭所有应用程序窗体后引发。  如果应用程序异常终止，则不会引发此事件。
    ' UnhandledException:  在应用程序遇到未经处理的异常时引发。
    ' StartupNextInstance:  在启动单实例应用程序且应用程序已处于活动状态时引发。
    ' NetworkAvailabilityChanged:  在连接或断开网络连接时引发。
    Partial Friend Class MyApplication


        'Protected Overrides Function OnStartup(eventArgs As ApplicationServices.StartupEventArgs) As Boolean
        '    Dim result As Boolean = MyBase.OnStartup(eventArgs)

        '    SetMsg("OnStartup1")
        '    If eventArgs.CommandLine.Count > 0 Then
        '        SetMsg("OnStartup2")
        '        Try
        '            Dim frmMain As Form = Me.MainForm

        '            If frmMain Is Nothing Then
        '                SetMsg("OnStartup-Nothing")
        '            End If

        '            CType(frmMain, FlashProcForm).ProcessAutoInstallArgs(eventArgs.CommandLine.Item(0).Trim)
        '            SetMsg("OnStartup3")
        '        Catch ex As Exception
        '            SetMsg("OnStartup4" & ex.ToString())
        '        End Try
        '    End If
        '    SetMsg("OnStartup5")

        '    Return result
        'End Function

        'Private lockerObj As Object = New Object()
        'Private Sub SetMsg(ByVal text As String)

        '    Dim strAppFolder = AppDomain.CurrentDomain.BaseDirectory.TrimEnd("\"c) & "\"
        '    If Not System.IO.Directory.Exists(strAppFolder) Then
        '        strAppFolder = System.Windows.Forms.Application.StartupPath.TrimEnd("\"c) & "\"
        '        If Not System.IO.Directory.Exists(strAppFolder) Then
        '            strAppFolder = Environment.CurrentDirectory.TrimEnd("\"c) & "\"
        '        End If
        '    End If

        '    Dim strPath As String = Path.Combine(strAppFolder, "Logs")
        '    SyncLock lockerObj
        '        Try
        '            If Not Directory.Exists(strPath) Then
        '                Directory.CreateDirectory(strPath)
        '            End If
        '            Using writer As New StreamWriter(((strPath & Convert.ToString("\")) + "Flash-A" + DateTime.Now.ToString("yyyyMMdd")) + ".txt", True, Encoding.UTF8)
        '                writer.WriteLine("------------Time:" + DateTime.Now.ToString() + "---------------")
        '                writer.WriteLine(text)
        '                writer.Flush()
        '                writer.Close()
        '            End Using
        '        Catch ex As Exception
        '            System.Diagnostics.Debug.WriteLine(ex)
        '        End Try
        '    End SyncLock

        'End Sub

    End Class


End Namespace

