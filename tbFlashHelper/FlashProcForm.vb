﻿Imports System.Threading
Imports System.Runtime.InteropServices
Imports System.IO
Imports System.Text

Public Class FlashProcForm

    Const WM_COPYDATA As Integer = &H4A
    Const SC_RESTORE As Int32 = &HF121

    Private mTdDeviceRestore As Thread
    Private mTdDeviceActivate As Thread
    Private mTdDeviceActivateLogin As Thread

    Private mIsDoFlash As Boolean = False
    Public ReadOnly Property IsDoFlash() As Boolean
        Get
            Return Me.mIsDoFlash
        End Get
    End Property

    Private mIsActivate As Boolean = False
    Private Shared mTongbu As String = Path.Combine(FlashProcHelper.AppFolder, "Tongbu.exe")

    Private mIsActivateLogin = True
    Private mStrAccounts As String = ""
    Private mStrPassword As String = ""
    Private mStrCancel As Boolean = False

    Private mICloverProcFormHandle As Integer = 0

    Public ReadOnly Property IsActivate() As Boolean
        Get
            Return Me.mIsActivate
        End Get
    End Property

#Region "--- 初始化 ---"

    Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.Size = New Size(1, 1)

        Me.m_restoreDelegate = New restore_progress_callback_t(AddressOf OnRestoreProgressCallback)
        Me.m_restoreErrorDelegate = New restore_idevice_err_callback_t(AddressOf OniDeviceErrorCallback)
        Me.m_activeErrorDelegate = New active_idevice_err_callback_t(AddressOf OniDeviceActiveErrorCallback)
        Me.m_activeIcloudCallback = New active_icloud_callback(AddressOf OnActiveIcloudCallback)

        FlashProcHelper.SetMsg("RunTime:2016年5月5日")

    End Sub

    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        Try
            If Me.mTdDeviceRestore IsNot Nothing AndAlso Me.mTdDeviceRestore.ThreadState <> ThreadState.Stopped Then
                Me.mTdDeviceRestore.Abort()
            End If
        Catch
        End Try

        Try
            If Me.mTdDeviceActivate IsNot Nothing AndAlso Me.mTdDeviceActivate.ThreadState <> ThreadState.Stopped Then
                Me.mTdDeviceActivate.Abort()
            End If
        Catch
        End Try

        Try
            If Me.mTdDeviceActivateLogin IsNot Nothing AndAlso Me.mTdDeviceActivateLogin.ThreadState <> ThreadState.Stopped Then
                Me.mTdDeviceActivateLogin.Abort()
            End If
        Catch
        End Try

        MyBase.OnFormClosing(e)
    End Sub

    Protected Overrides Sub DefWndProc(ByRef m As System.Windows.Forms.Message)
        Dim strMessage As String = ""

        If m.Msg = WM_COPYDATA AndAlso CInt(m.WParam) = &HF121 Then
            Select Case m.Msg
                Case WM_COPYDATA
                    Dim mystr As New COPYDATASTRUCT()
                    Dim mytype As Type = mystr.[GetType]()
                    mystr = CType(m.GetLParam(mytype), COPYDATASTRUCT)
                    strMessage = mystr.lpData
                    Exit Select
                Case Else
                    MyBase.DefWndProc(m)
                    Exit Select
            End Select

            DoWork(strMessage)

        End If
        MyBase.DefWndProc(m)
    End Sub

    Protected Overrides Sub OnShown(e As EventArgs)
        MyBase.OnShown(e)

        'Dim strMessage As String = "Restore;D:\iPhone8,4_9.3.1_13E238_Restore.ipsw;False;True;F:\Tongbu\FirmwarePlist.plist"
        'If strMessage.Contains(";") Then
        '    Dim arrMessage As String() = Split(strMessage, ";")
        '    If arrMessage.Length < 0 Then
        '        Return
        '    End If
        '    Me.DeviceRestore(arrMessage(1), CType(arrMessage(2), Boolean), CType(arrMessage(3), Boolean), arrMessage(4))
        'End If
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function CreateJsonStr(jType As JsonType, intPlan As Integer, intPlanEx As Integer, status As RestoreProgressStatus, Optional ByVal strErrorMsg As String = "") As String
        Return String.Format("{0};{1};{2};{3};{4}", jType.ToString(), intPlan, intPlanEx, status, strErrorMsg) 'Type;Plan;PlanEx;Status;ErrorMsg
    End Function

    Private Function CreateActionCollectJsonStr(jType As JsonType, strModelKey As String, startTime As DateTime, consuming As Integer, status As RestoreProgressStatus)
        Dim strJson As String = String.Format("{0};{1};{2};{3};{4}", jType.ToString(), strModelKey, startTime.ToString().Replace(" ", "_"), consuming, status.ToString())
        Return strJson 'Type;ModelKey;StartTime;consuming;StrStatus
    End Function

    Private Sub DoWork(strMessage As String)
        If strMessage.Length < 0 Then
            Return
        End If

        FlashProcHelper.SetMsg("FlashProcFormMessage:" & strMessage)
        If strMessage.Contains(";") Then
            Dim arrMessage As String() = Split(strMessage, ";")
            If arrMessage.Length < 0 Then
                Return
            End If
            If arrMessage(0) = "Restore" Then
                FlashProcHelper.SetMsg("DoRestore")
                Me.DeviceRestore(arrMessage(1), CType(arrMessage(2), Boolean), CType(arrMessage(3), Boolean), arrMessage(4))

            ElseIf arrMessage(0) = "Activate_Login" Then
                'Me.mIsActivateLogin = True
                Me.mStrCancel = CType(arrMessage(1), Boolean)
                Me.mStrAccounts = arrMessage(2).ToString()
                Me.mStrPassword = arrMessage(3).ToString()

                FlashProcHelper.SetMsg(String.Format("Activate_Login:{0} appleid:{1}", Me.mStrCancel, Me.mStrAccounts))
            End If
        ElseIf strMessage.StartsWith("iCloverProcFormHandle") Then
            Try
                Me.mICloverProcFormHandle = CType(strMessage.Replace("iCloverProcFormHandle", ""), Integer)
            Catch ex As Exception
            End Try
            FlashProcHelper.SetMsg(String.Format("iCloverProcFormHandle:{0}", Me.mICloverProcFormHandle))
        Else
            If strMessage = "Activate" Then
                FlashProcHelper.SetMsg("DoActivate")
                Me.DeviceActivate()
            End If
        End If

    End Sub

    <DllImport("User32.dll", EntryPoint:="SendMessage")> _
    Private Shared Function SendMessage(hWnd As Integer, Msg As Integer, wParam As Integer, ByRef lParam As COPYDATASTRUCT) As Integer
    End Function

    <DllImport("User32.dll", EntryPoint:="FindWindow")> _
    Private Shared Function FindWindow(lpClassName As String, lpWindowName As String) As Integer
    End Function

    Private Sub SendProgress(ByVal strMsg As String)
        Dim WINDOW_HANDLER As Integer = Me.mICloverProcFormHandle 'FindWindow(Nothing, "iCloverProcForm")
        FlashProcHelper.SetMsg("WINDOW_HANDLER:" & WINDOW_HANDLER)
        If WINDOW_HANDLER = 0 Then
            Return
        End If
        strMsg = String.Format("FlashProgress{0}", strMsg)

        Dim sarr As Byte() = System.Text.Encoding.[Default].GetBytes(strMsg)
        Dim len As Integer = sarr.Length
        Dim cds As COPYDATASTRUCT
        cds.dwData = CType(100, IntPtr)
        cds.lpData = strMsg
        cds.cbData = len + 1
        SendMessage(WINDOW_HANDLER, WM_COPYDATA, SC_RESTORE, cds)
        FlashProcHelper.SetMsg("SendMessage:" & strMsg)
    End Sub

    Private Function GetIHandle() As Integer
        Dim iHandle As Integer = 0
        Try
            iHandle = FindWindow(Nothing, "frmFlash")
        Catch ex As Exception
            FlashProcHelper.LogException(ex.ToString(), "GetIHandle")
        End Try
        Return iHandle
    End Function

    Public Sub ProcessAutoInstallArgs(ByVal strArgs As String)
        Try
            DoWork(strArgs)
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

#End Region

#Region "-- 刷机 --"

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Private Delegate Sub restore_progress_callback_t(ByVal step_progress As Integer, ByVal userdata As String)

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Private Delegate Sub restore_idevice_err_callback_t(ByVal errcode As Integer, ByVal restore_code As Integer, ByVal errmsg As String)

    ' 保留数据刷机，ipsw:固件路径
    <DllImport(".\Flash\tblibidevicerestore.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function iDeviceRestore(ByVal ipsw As String, ByVal prcb As restore_progress_callback_t, ByVal errcb As restore_idevice_err_callback_t, ByVal logpath As String, ByVal configContent As IntPtr, ByVal configLength As Integer) As Integer
    End Function

    ' 全新刷机，ipsw:固件路径;
    <DllImport(".\Flash\tblibidevicerestore.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function iDeviceNewRestore(ByVal ipsw As String, ByVal prcb As restore_progress_callback_t, ByVal errcb As restore_idevice_err_callback_t, ByVal logpath As String, ByVal configContent As IntPtr, ByVal configLength As Integer) As Integer
    End Function

    ' 检测指定固件、设备是否可以刷机   
    <DllImport(".\Flash\tblibidevicerestore.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function CheckProducttssRequest(ByVal ipswplist As String, ByVal deviceplist As String) As Integer
    End Function

    <DllImport(".\Flash\libideviceactivation.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function iDeviceDeactivate(ByVal errcb As active_idevice_err_callback_t, ByVal logpath As String) As Integer
    End Function

    Private m_restoreDelegate As restore_progress_callback_t
    Private m_restoreErrorDelegate As restore_idevice_err_callback_t

    Private mStartTime As DateTime = DateTime.Now
    Private mOldStatus As RestoreProgressStatus = RestoreProgressStatus.None

    Public Sub OnRestoreProgressCallback(ByVal step_progress As Integer, ByVal userdata As String)
        Try
            FlashProcHelper.SetMsg(String.Format(" Progress: userdata:{0}    step_progress:{1}", userdata, step_progress))
            Dim strJson = ""

            Dim status As RestoreProgressStatus = GetRestoreProgressStatus(userdata)

            If status = RestoreProgressStatus.Done Then
                If mIsActivate Then
                    status = RestoreProgressStatus.ActivateWaiting
                End If
                strJson = CreateJsonStr(JsonType.Restore, 100, 0, status)
            Else
                strJson = CreateJsonStr(JsonType.Restore, step_progress, 0, status)
            End If

            If strJson.Length > 0 Then
                Me.SendProgress(strJson)
            End If

            '行为数据收集
            If status = RestoreProgressStatus.TheRestoreProcess OrElse status = RestoreProgressStatus.CheckingFilesystems OrElse status = RestoreProgressStatus.SendFilesystem Then
                If status <> Me.mOldStatus OrElse Me.mOldStatus = RestoreProgressStatus.None Then
                    Me.mStartTime = DateTime.Now
                    Me.mOldStatus = status
                Else
                    Dim consuming As Integer = DateTime.Now.Subtract(Me.mStartTime).Duration().TotalSeconds()
                    Me.SendProgress(CreateActionCollectJsonStr(JsonType.ActionCollect, "FlashStart", Me.mStartTime, consuming, status))
                End If
            End If
        Catch ex As Exception
            FlashProcHelper.LogException(ex.ToString(), "OnRestoreProgressCallback")
            Me.SendProgress(CreateJsonStr(JsonType.Restore, 3201, 0, RestoreProgressStatus.RestoreException, "RestoreProgressCallbackException"))
        End Try
    End Sub

    Public Sub OniDeviceErrorCallback(ByVal errcode As Integer, ByVal restore_code As Integer, ByVal errmsg As String)
        Try

            FlashProcHelper.SetMsg("DeviceError：" & errmsg)
            Me.SendProgress(CreateJsonStr(JsonType.Restore, errcode, restore_code, RestoreProgressStatus.RestoreError, errmsg.Replace(" ", "-")))

        Catch ex As Exception
            FlashProcHelper.LogException(ex.ToString(), "OniDeviceErrorCallback")
            Me.SendProgress(CreateJsonStr(JsonType.Restore, 3202, 0, RestoreProgressStatus.RestoreException, "DeviceErrorCallbackException"))

        End Try
    End Sub

    Private Function GetRestoreProgressStatus(ByVal strMsg As String) As RestoreProgressStatus
        Dim status As RestoreProgressStatus = RestoreProgressStatus.None
        Dim strMsgEx As String = ""

        If strMsg.Contains("start checking device") Then
            status = RestoreProgressStatus.CheckingDevice
            '   strMsg = Me.Language.GetString("Flash.Progress.CheckingDevice")

        ElseIf strMsg.Contains("Found device in Normal mode") Then
            status = RestoreProgressStatus.InNormalMode
            '  strMsg = Me.Language.GetString("Flash.Progress.NormalMode")

        ElseIf strMsg.Contains("checking restore mode") Then
            status = RestoreProgressStatus.CheckingRestoreMode
            '  strMsg = Me.Language.GetString("Flash.Progress.CheckingRestoreMode")

        ElseIf strMsg.Contains("Extracting BuildManifest from IPSW") Then
            status = RestoreProgressStatus.ExtractingBuildManifest
            '  strMsg = Me.Language.GetString("Flash.Progress.ExtractingBuildManifest")

        ElseIf strMsg.Contains("retrieve shsh blobs if required") Then
            status = RestoreProgressStatus.RetrieveSHSH
            '  strMsg = Me.Language.GetString("Flash.Progress.RetrieveSHSH")

        ElseIf strMsg.Contains("check if we already have an extracted filesystem") Then
            status = RestoreProgressStatus.ExtractedFilesystem
            '   strMsg = Me.Language.GetString("Flash.Progress.CheckExtractedFilesystem")

        ElseIf strMsg.Contains("Entering recovery mode") Then
            status = RestoreProgressStatus.EnteringRecoveryMode
            '  strMsg = Me.Language.GetString("Flash.Progress.EnteringRecoveryMode")

        ElseIf strMsg.Contains("load the iBEC") Then
            status = RestoreProgressStatus.LoadTheIBEC
            '   strMsg = Me.Language.GetString("Flash.Progress.LoadiBEC")

        ElseIf strMsg.Contains("send iBEC finised") Then
            status = RestoreProgressStatus.SendIBECFinised
            '  strMsg = Me.Language.GetString("Flash.Progress.SendiBEC")

        ElseIf strMsg.Contains("Entering restore mode") Then
            status = RestoreProgressStatus.EnteringRestoreMode
            '   strMsg = Me.Language.GetString("Flash.Progress.InRestoreMode")

        ElseIf strMsg.Contains("device is finally in restore mode") Then
            status = RestoreProgressStatus.FinallyInRestoreMode
            ' strMsg = Me.Language.GetString("Flash.Progress.InRestoreMode")

        ElseIf strMsg.Contains("Connected to com.apple.mobile.restored") Then
            status = RestoreProgressStatus.ConnectedToCom
            ' strMsg = Me.Language.GetString("Flash.Progress.ConnectedApple")

        ElseIf strMsg.Contains("Starting FDR listener thread") Then
            status = RestoreProgressStatus.FDRListenerThread
            ' strMsg = Me.Language.GetString("Flash.Progress.StartingFDR")

        ElseIf strMsg.Contains("start the restore process") Then
            status = RestoreProgressStatus.TheRestoreProcess
            ' strMsg = Me.Language.GetString("Flash.Progress.StartRestore")

        ElseIf strMsg.Contains("send NORData") Then
            status = RestoreProgressStatus.SendNORData
            ' strMsg = "Flash.Progress.SendNORData"

        ElseIf strMsg.Contains("send RootTicket") Then
            status = RestoreProgressStatus.SendRootTicket
            '  strMsg = Me.Language.GetString("Flash.Progress.SendRootTicket")

        ElseIf strMsg.Contains("Partition NAND device") Then
            status = RestoreProgressStatus.PartitionNANDDevice
            ' strMsg = Me.Language.GetString("Flash.Progress.PartitionNAND")

        ElseIf strMsg.Contains("Waiting for storage device") Then
            status = RestoreProgressStatus.StorageDevice
            ' strMsg = Me.Language.GetString("Flash.Progress.WaitingStorage")

        ElseIf strMsg.Contains("Checking filesystems") Then
            status = RestoreProgressStatus.CheckingFilesystems
            '  strMsg = Me.Language.GetString("Flash.Progress.CheckExtractedFilesystem")

        ElseIf strMsg.Contains("Waiting for NAND") Then
            status = RestoreProgressStatus.WaitingForNAND
            ' strMsg = Me.Language.GetString("Flash.Progress.WaitingNAND")

        ElseIf strMsg.Contains("About to send filesystem") Then
            status = RestoreProgressStatus.SendFilesystem
            '  strMsg = Me.Language.GetString("Flash.Progress.SendFilesystem")

        ElseIf strMsg.Contains("Connected to ASR") Then
            status = RestoreProgressStatus.ConnectedToASR
            '  strMsg = Me.Language.GetString("Flash.Progress.ConnectedASR")

        ElseIf strMsg.Contains("Sending filesystem") Then
            status = RestoreProgressStatus.SendingFilesystem
            '  strMsg = Me.Language.GetString("Flash.Progress.SendFilesystem")

        ElseIf strMsg.Contains("Restoring image") Then
            status = RestoreProgressStatus.RestoringImage
            '   strMsg = Me.Language.GetString("Flash.Progress.RestoringImage")

        ElseIf strMsg.Contains("send KernelCache") Then
            status = RestoreProgressStatus.SendKernelCache
            '  strMsg = Me.Language.GetString("Flash.Progress.SendKernelCache")

        ElseIf strMsg.Contains("Done") Then
            status = RestoreProgressStatus.Done
            ' strMsg = Me.Language.GetString("Flash.Progress.Done")

        End If

        Return status
    End Function

    Public Sub DeviceRestore(ByVal ipsw As String, ByVal isRetainData As Boolean, ByVal isActivate As Boolean, ByVal strPilstPath As String)
        If Me.mTdDeviceRestore IsNot Nothing Then
            Return
        End If

        Me.mIsActivate = isActivate

        Dim obj() As Object = New Object() {ipsw, isRetainData, strPilstPath}
        Me.mTdDeviceRestore = New Thread(AddressOf DoReserveDeviceRestore)
        Me.mTdDeviceRestore.IsBackground = True
        Me.mTdDeviceRestore.Start(obj)
    End Sub

    Private Sub DoReserveDeviceRestore(ByVal obj As Object)
        Try
            Dim strPath As String = Path.Combine(FlashProcHelper.AppFolder, "FlashLogs")
            Me.mIsDoFlash = True
            Dim isRetainData As Boolean = CType(obj(1), Boolean)

            Dim length As Integer = 0
            Dim ptrConfigContents As IntPtr = IntPtr.Zero
            Dim fs As FileStream = Nothing
            Dim br As BinaryReader = Nothing
            Try
                Dim strPlistPath As String = obj(2)

                FlashProcHelper.SetMsg("Plist路径：" & strPlistPath)

                If File.Exists(strPlistPath) Then

                    fs = New FileStream(strPlistPath, FileMode.Open, FileAccess.Read)
                    br = New BinaryReader(fs)

                    Dim arrContents(fs.Length) As Byte
                    arrContents = br.ReadBytes(fs.Length)
                    br.Close()
                    fs.Close()
                    fs.Dispose()

                    length = arrContents.Length
                    ptrConfigContents = Marshal.AllocCoTaskMem(length)

                    Try
                        FlashProcHelper.SetMsg("ptrConfigContents：" & ptrConfigContents.ToString())
                        FlashProcHelper.SetMsg("length：" & length)
                    Catch ex As Exception
                    End Try

                    Marshal.Copy(arrContents, 0, ptrConfigContents, length)
                End If
            Catch ex As Exception
                If br IsNot Nothing Then
                    br.Close()
                End If
                If fs IsNot Nothing Then
                    fs.Close()
                    fs.Dispose()
                End If
                FlashProcHelper.LogException(ex.ToString(), "DoReserveDeviceRestore")
            End Try

            If isRetainData Then
                iDeviceRestore(obj(0).ToString(), m_restoreDelegate, m_restoreErrorDelegate, strPath, ptrConfigContents, length)
            Else
                iDeviceNewRestore(obj(0).ToString(), m_restoreDelegate, m_restoreErrorDelegate, strPath, ptrConfigContents, length)
            End If

        Catch ex As Exception
            FlashProcHelper.LogException(ex.ToString(), "DoReserveDeviceRestore")
            Me.SendProgress(CreateJsonStr(JsonType.Active, 3204, 0, RestoreProgressStatus.RestoreException, "ReserveDeviceRestoreException"))
        End Try
    End Sub

#End Region

#Region "-- 激活 --"

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Private Delegate Sub active_icloud_callback(ByVal appleid As IntPtr, ByVal apidlength As IntPtr, ByVal password As IntPtr, ByVal pwdlength As IntPtr, ByVal bCancel As IntPtr, ByVal response_description As String, ByVal errCounts As Integer)

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Private Delegate Sub active_idevice_err_callback_t(ByVal errcode As Integer, ByVal errmsg As String)

    ' 刷机激活
    <DllImport(".\Flash\tblibideviceactivation.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function iDeviceActivate(ByVal errcb As active_idevice_err_callback_t, ByVal logpath As String, ByVal icloudcb As active_icloud_callback) As Integer
    End Function

    Private m_activeErrorDelegate As active_idevice_err_callback_t
    Private m_activeIcloudCallback As active_icloud_callback

    Public Sub OniDeviceActiveErrorCallback(ByVal errcode As Integer, ByVal errmsg As String)
        Try
            FlashProcHelper.SetMsg(String.Format(" Active: errcode:{0}    errmsg:{1}", errcode, errmsg))
            Dim strJson = ""

            Dim status As RestoreProgressStatus = GetActiveStatus(errcode)

            strJson = CreateJsonStr(JsonType.Active, errcode, 0, status, errmsg)

            If strJson.Length > 0 Then
                Me.SendProgress(strJson)
            End If
        Catch ex As Exception
            FlashProcHelper.LogException(ex.ToString(), "OniDeviceActiveErrorCallback")
            Me.SendProgress(CreateJsonStr(JsonType.Active, 3203, 0, RestoreProgressStatus.ActiveError, "DeviceActiveErrorCallbackException"))
        End Try
    End Sub

    Private Function GetActiveStatus(ByVal errcode As Integer) As RestoreProgressStatus
        Dim status As RestoreProgressStatus = RestoreProgressStatus.Activateing

        Select Case errcode
            '   E_SUCCESS = 0,
            'E_UNKNOWN_ERROR = -101,
            'E_INITIALIZE_FAILTURE = -102,      // 初始化失败，没有检测到设备
            'E_HANDSHAKE_FAILTURE = -103,       // 设备握手通信失败，需拔插设备
            'E_ACTIVATION_FAILTURE = -104,
            'E_REQUEST_FAILTURE = -105,         // 设备请求激活失败，可能网络不稳（需要访问apple服务器）
            'E_REQUIRED_SIM = -106,             // 需要SIM卡才能激活
            'E_LOCKDOWN_FAILTURE = -107,
            'E_CANCEL_ACTIVATION = -108,        // 取消当前激活
            'E_INCORRECT_APPLEID_PASSWORD = -109,   // 无效的Apple ID或密码

            Case 0
            Case -107
                status = RestoreProgressStatus.ActivateSucceed

            Case -101, -102, -103, -104, -105, -110
                status = RestoreProgressStatus.ActiveError

            Case -106
                status = RestoreProgressStatus.ActivateSIM

            Case -108
                status = RestoreProgressStatus.ActivateCancel

            Case -109
                status = RestoreProgressStatus.ActivateAppleIDError

        End Select

        Return status
    End Function

    Private Sub OnActiveIcloudCallback(ByVal appleid As IntPtr, ByVal apidlength As IntPtr, ByVal password As IntPtr, ByVal pwdlength As IntPtr, _
                                       ByVal bCancel As IntPtr, ByVal response_description As String, ByVal errCounts As Integer)

        Try
            FlashProcHelper.SetMsg(" Progress: Login appleid  errCounts:" & errCounts)

            Dim bC As Byte = 0

            If errCounts = 1 Then
                Me.mStrAccounts = ""
                Me.mStrPassword = ""
                Me.mStrCancel = False
                Dim strJson = CreateJsonStr(JsonType.Active, 100, 0, RestoreProgressStatus.ActivateLogin, response_description)
                If strJson.Length > 0 Then
                    Me.SendProgress(strJson)
                End If
            End If

            Do
                If Me.mStrCancel Then
                    bC = 1
                    Marshal.WriteByte(bCancel, bC)
                    FlashProcHelper.SetMsg(String.Format("ActiveIcloudTrue: appleid:{0} isCancel:{1}", Me.mStrAccounts, Me.mStrCancel))
                    Me.mIsActivateLogin = False
                    Exit Do
                Else
                    Dim arrAppleID() As Byte = Encoding.ASCII.GetBytes(Me.mStrAccounts)
                    Dim arrApplePwd() As Byte = Encoding.ASCII.GetBytes(Me.mStrPassword)

                    Dim hAppleID As IntPtr = Marshal.AllocCoTaskMem(arrAppleID.Length)
                    Dim hApplePwd As IntPtr = Marshal.AllocCoTaskMem(arrApplePwd.Length)

                    Marshal.WriteInt32(apidlength, arrAppleID.Length)
                    Marshal.WriteInt32(pwdlength, arrApplePwd.Length)

                    Marshal.Copy(arrAppleID, 0, hAppleID, arrAppleID.Length)
                    Marshal.Copy(arrApplePwd, 0, hApplePwd, arrApplePwd.Length)

                    Marshal.WriteInt32(appleid, hAppleID.ToInt32())
                    Marshal.WriteInt32(password, hApplePwd.ToInt32())

                    FlashProcHelper.WaitSeconds(0.5)
                    FlashProcHelper.SetMsg(String.Format("ActiveIcloudFalse: appleid:{0} isCancel:{1}", Me.mStrAccounts, Me.mStrCancel))

                    Marshal.WriteByte(bCancel, bC)

                    If Me.mStrAccounts.Length > 0 Then
                        Me.mIsActivateLogin = False
                        Exit Do
                    End If
                End If
            Loop While Me.mIsActivateLogin

        Catch ex As Exception
            FlashProcHelper.LogException(ex.ToString(), "OnActiveIcloudCallback")
            Me.SendProgress(CreateJsonStr(JsonType.Active, 3207, 0, RestoreProgressStatus.ActiveError, "OnActiveIcloudCallback"))
        End Try
    End Sub

    Public Sub DeviceActivate()
        If Me.mTdDeviceActivate IsNot Nothing Then
            Return
        End If
        Me.mTdDeviceActivate = New Thread(AddressOf DoDeviceActivate)
        Me.mTdDeviceActivate.IsBackground = True
        Me.mTdDeviceActivate.Start()
    End Sub

    Private Sub DoDeviceActivate()
        Try
            Me.SendProgress(CreateJsonStr(JsonType.Active, 100, 0, RestoreProgressStatus.Activateing))

            Dim strPath As String = Path.Combine(FlashProcHelper.AppFolder, "FlashLogs")

            Dim intResult As Integer = iDeviceActivate(m_activeErrorDelegate, strPath, m_activeIcloudCallback) '"None Device""Failed Handshake""Failed Deactivate""Failed Request""Failed Response""Error Server" 测试错误
            FlashProcHelper.SetMsg("DoDeviceActivate:" & intResult)
            If intResult = 0 Then
                Me.SendProgress(CreateJsonStr(JsonType.Active, 100, 0, RestoreProgressStatus.ActivateSucceed))
            End If
        Catch ex As Exception
            FlashProcHelper.LogException(ex.ToString(), "DoDeviceActivate")
            Me.SendProgress(CreateJsonStr(JsonType.Active, 3205, 0, RestoreProgressStatus.RestoreException, "DoDeviceActivateException"))
        End Try
    End Sub


#End Region

End Class

Public Structure COPYDATASTRUCT
    Public dwData As IntPtr
    Public cbData As Integer
    <MarshalAs(UnmanagedType.LPStr)> _
    Public lpData As String
End Structure

Public Enum RestoreProgressStatus

    ''' <summary>
    ''' 
    ''' </summary>
    ''' <remarks></remarks>
    None = 0

    ''' <summary>
    ''' 检查设备
    ''' </summary>
    ''' <remarks></remarks>
    CheckingDevice = 1

    ''' <summary>
    ''' 检查正常模式
    ''' </summary>
    ''' <remarks></remarks>
    InNormalMode = 2

    ''' <summary>
    ''' 检查恢复模式
    ''' </summary>
    ''' <remarks></remarks>
    CheckingRestoreMode = 3

    ''' <summary>
    ''' 解析BuildManifest
    ''' </summary>
    ''' <remarks></remarks>
    ExtractingBuildManifest = 4

    ''' <summary>
    ''' 查找SHSH
    ''' </summary>
    ''' <remarks></remarks>
    RetrieveSHSH = 5

    ''' <summary>
    ''' 检查文件系统
    ''' </summary>
    ''' <remarks></remarks>
    ExtractedFilesystem = 6

    ''' <summary>
    ''' 
    ''' </summary>
    ''' <remarks></remarks>
    EnteringRecoveryMode = 7

    ''' <summary>
    ''' 加载iBEC
    ''' </summary>
    ''' <remarks></remarks>
    LoadTheIBEC = 8

    ''' <summary>
    ''' 上传iBEC
    ''' </summary>
    ''' <remarks></remarks>
    SendIBECFinised = 9

    ''' <summary>
    ''' 恢复模式
    ''' </summary>
    ''' <remarks></remarks>
    EnteringRestoreMode = 10

    ''' <summary>
    ''' 
    ''' </summary>
    ''' <remarks></remarks>
    FinallyInRestoreMode = 11

    ''' <summary>
    ''' 连接服务器
    ''' </summary>
    ''' <remarks></remarks>
    ConnectedToCom = 12

    ''' <summary>
    ''' 启动FDR监听
    ''' </summary>
    ''' <remarks></remarks>
    FDRListenerThread = 13

    ''' <summary>
    ''' 开始恢复
    ''' </summary>
    ''' <remarks></remarks>
    TheRestoreProcess = 14

    ''' <summary>
    ''' 发送NORData
    ''' </summary>
    ''' <remarks></remarks>
    SendNORData = 15

    ''' <summary>
    ''' 发送RootTicket
    ''' </summary>
    ''' <remarks></remarks>
    SendRootTicket = 16

    ''' <summary>
    ''' NAND分区
    ''' </summary>
    ''' <remarks></remarks>
    PartitionNANDDevice = 17

    ''' <summary>
    ''' 等待分区
    ''' </summary>
    ''' <remarks></remarks>
    StorageDevice = 18

    ''' <summary>
    ''' 检查文件系统
    ''' </summary>
    ''' <remarks></remarks>
    CheckingFilesystems = 19

    ''' <summary>
    ''' 等待NAND
    ''' </summary>
    ''' <remarks></remarks>
    WaitingForNAND = 20

    ''' <summary>
    ''' 写入filesystem
    ''' </summary>
    ''' <remarks></remarks>
    SendFilesystem = 21

    ''' <summary>
    ''' 连接ASR
    ''' </summary>
    ''' <remarks></remarks>
    ConnectedToASR = 22

    ''' <summary>
    ''' 写入filesystem
    ''' </summary>
    ''' <remarks></remarks>
    SendingFilesystem = 23

    ''' <summary>
    ''' 恢复图像
    ''' </summary>
    ''' <remarks></remarks>
    RestoringImage = 24

    ''' <summary>
    ''' 发送KernelCache
    ''' </summary>
    ''' <remarks></remarks>
    SendKernelCache = 25

    ''' <summary>
    ''' 完成
    ''' </summary>
    ''' <remarks></remarks>
    Done = 26

    ''' <summary>
    ''' 等待激活
    ''' </summary>
    ''' <remarks></remarks>
    ActivateWaiting = 29

    ''' <summary>
    ''' 正在激活
    ''' </summary>
    ''' <remarks></remarks>
    Activateing = 30

    ''' <summary>
    ''' 激活完成
    ''' </summary>
    ''' <remarks></remarks>
    ActivateSucceed = 31

    ''' <summary>
    ''' 刷机失败
    ''' </summary>
    ''' <remarks></remarks>
    RestoreError = 27

    ''' <summary>
    ''' 激活失败
    ''' </summary>
    ''' <remarks></remarks>
    ActiveError = 28

    ''' <summary>
    ''' 刷机出错
    ''' </summary>
    ''' <remarks></remarks>
    RestoreException = 32

    ''' <summary>
    ''' 输入帐号密码
    ''' </summary>
    ''' <remarks></remarks>
    ActivateLogin = 33

    ''' <summary>
    ''' 插入SIM卡
    ''' </summary>
    ''' <remarks></remarks>
    ActivateSIM = 34

    ''' <summary>
    ''' 取消激活
    ''' </summary>
    ''' <remarks></remarks>
    ActivateCancel = 35

    ''' <summary>
    ''' 无效的Apple ID或密码
    ''' </summary>
    ''' <remarks></remarks>
    ActivateAppleIDError = 36


End Enum

Public Enum JsonType
    Restore = 0
    Active = 1
    ActionCollect = 2
End Enum