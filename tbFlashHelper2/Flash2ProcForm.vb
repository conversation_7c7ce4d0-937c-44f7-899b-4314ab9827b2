﻿Imports System.Net
Imports System.IO
Imports System.IO.Compression
Imports System.Text
Imports System.Text.RegularExpressions
Imports System.Threading
Imports iTong.CoreFoundation

Public Class Flash2ProcForm
    Private mListJobs As New List(Of SimulatedInfo)
    Private mSBState As SimulatedInfoState = SimulatedInfoState.None
    Private mIntPageCount As Integer = 1
    Private mCurrentSimulatedInfo As SimulatedInfo = Nothing
    Private mQueueExecute As New Queue(Of SimulatedInfo)()
    Private mLockAnalyse As Object = New Object()
    Private mExecuteCompleted As Boolean = False
    Private mStrUrl As String = "http://pctool.tongbu.com/tools/TbzsSimulationClick?GetSimulationClickList&tbzsversion={0}&mac={1}"
    Private mStrSendUrl As String = "http://pctool.tongbu.com/tools/TbzsSimulationClick?SimulationClickBack&Id={0}&TypeId={1}&Status={2}" '  Id为领取任务时的Id，Status;状态 0：定位失败 1：定位到

#Region "--- 初始化 ---"

    Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Size = New Size(1, 1)
        webSimulated.ScriptErrorsSuppressed = True
        AddHandler webSimulated.DocumentCompleted, AddressOf OnDocumentCompleted
        AddHandler webSimulated.NewWindow3, AddressOf OnNewWindow3
    End Sub

    Private Sub Flash2ProcForm_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        Try
            Common.Log(String.Format("RunWork:{0}", Date.Now))
        Catch 
        End Try
        If Me.bgwSimulated IsNot Nothing AndAlso Me.bgwSimulated.IsBusy Then
            Try
                Me.bgwSimulated.CancelAsync()
            Catch
            End Try
        End If
        Me.bgwSimulated.RunWorkerAsync()
    End Sub

    Protected Overrides Sub OnClosing(e As System.ComponentModel.CancelEventArgs)
        MyBase.OnClosing(e)

        Common.Log(String.Format("EndWork:{0}", Date.Now))
    End Sub

#End Region

    Private Sub bgwSimulated_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles bgwSimulated.DoWork
        '获取任务
        Me.GetJobs()
    End Sub
    Private Sub bgwSimulated_RunWorkerCompleted(sender As Object, e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwSimulated.RunWorkerCompleted
        '执行任务
        If Me.mListJobs.Count = 0 Then
            Me.Close()
            Return
        End If

        For Each item As SimulatedInfo In Me.mListJobs
            Me.QueueAnalyse(item)
        Next
        Me.DoQueueAnalyse()
    End Sub

    Private Sub GetJobs()
        Me.mListJobs.Clear()
        Try
            Dim strTBVersion As String = System.Diagnostics.FileVersionInfo.GetVersionInfo(Path.Combine(Folder.AppFolder, "Tongbu.exe")).FileVersion
            Dim strContent As String = GetContentStringFromUrl(String.Format(Me.mStrUrl, strTBVersion, Common.GetMacAddress()))

            If String.IsNullOrEmpty(strContent) Then
                Return
            End If

            Common.Log(String.Format("GetContent:{0}", strContent))

            Dim objJson As JsonObject = JsonParser.ParseString(strContent)

            If Not objJson.ContainsKey("Data") Then
                Return
            End If
            Dim arrJson As JsonArray = CType(objJson("Data"), JsonArray)
            If arrJson.Count = 0 Then
                Return
            End If

            Dim item As SimulatedInfo = Nothing
            For Each jsonObj As JsonObject In arrJson
                item = New SimulatedInfo()
                If jsonObj.ContainsKey("Id") Then
                    item.IntID = CType(jsonObj("Id"), JsonNumber).Value()
                End If

                If jsonObj.ContainsKey("TypeId") Then
                    item.TypeId = CType(jsonObj("TypeId"), JsonNumber).Value()
                End If

                If jsonObj.ContainsKey("KeyWord") Then
                    item.StrKey = CType(jsonObj("KeyWord"), JsonString).Value()
                End If

                If jsonObj.ContainsKey("MatchKeyWord") Then
                    item.StrTitle = CType(jsonObj("MatchKeyWord"), JsonString).Value()
                End If

                Me.mListJobs.Add(item)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Flash2ProcForm_GetJobs")
        End Try
    End Sub

    Private Function GetContentStringFromUrl(ByVal strURl As String) As String
        Dim strContent As String = ""
        Try
            Dim intCount As Integer = 0
            Dim isAgain As Boolean = False
            Try
                strContent = Utility.GetContentStringFromUrl(strURl, Encoding.UTF8, 20000)
                isAgain = JudgeIsAgain(strContent)

                While intCount < 3 AndAlso isAgain
                    Utility.WaitSeconds(0.5)
                    strContent = Utility.GetContentStringFromUrl(strURl, Encoding.UTF8, 20000)
                    isAgain = JudgeIsAgain(strContent)
                    intCount = intCount + 1
                End While

            Catch ex As Exception
                Common.LogException(ex.ToString(), "GetContentStringFromUrl")
            End Try
            If isAgain Then
                strContent = ""
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetContentStringFromUrl")
        End Try
        Return strContent
    End Function

    Private Function JudgeIsAgain(strContent As String) As Boolean
        Dim isAgain As Boolean = False
        Try
            If String.IsNullOrEmpty(strContent) Then
                isAgain = True
            Else
                Dim objJson As JsonObject = JsonParser.ParseString(strContent)
                Dim strCode As Integer = 0
                If objJson.ContainsKey("Code") Then
                    strCode = CType(objJson("Code"), JsonNumber).Value()
                End If
                If strCode <> 0 Then
                    isAgain = True
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "JudgeIsAgain")
        End Try
        Return isAgain
    End Function

    Private Function QueueAnalyse(item As SimulatedInfo) As SimulatedInfo
        Dim info As SimulatedInfo = Nothing
        Try
            SyncLock mLockAnalyse
                If item Is Nothing Then
                    info = Me.mQueueExecute.Dequeue()
                Else
                    Me.mQueueExecute.Enqueue(item)
                End If
            End SyncLock
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Flash2ProcForm_QueueAnalyse")
        End Try
        Return info
    End Function

    Private Sub DoQueueAnalyse()
        While Me.mQueueExecute.Count > 0
            Try
                mExecuteCompleted = False
                Dim info As SimulatedInfo = QueueAnalyse(Nothing)
                Execute(info)
                Common.Log(String.Format("DoWork:{0}", info.IntID))
                Dim dt As Date = Date.Now
                While Not mExecuteCompleted AndAlso Date.Now.Subtract(dt).Seconds < 300
                    Utility.WaitSeconds(1)
                End While
                Common.Log(String.Format("DoWorkEnd:{0} ExecuteCompleted:{1}", info.IntID, mExecuteCompleted))
                If mExecuteCompleted = True Then
                    Dim ran As Random = New Random()
                    Utility.WaitSeconds(ran.Next(30, 180))
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "Flash2ProcForm_DoQueueAnalyse")
            End Try
        End While

        Me.Close()
    End Sub

    Private Sub Execute(ByVal info As SimulatedInfo)
        Try
            Me.mSBState = SimulatedInfoState.None
            Me.mCurrentSimulatedInfo = info
            Me.mIntPageCount = 1
            webSimulated.Navigate("https://www.baidu.com/")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Flash2ProcForm_Execute")
        End Try
    End Sub

    Private Sub OnDocumentCompleted(sender As Object, e As WebBrowserDocumentCompletedEventArgs)
        Try
            If Me.webSimulated.Document Is Nothing Then
                Return
            End If

            Dim item As HtmlElement = Nothing
            If Me.mCurrentSimulatedInfo Is Nothing Then
                Return
            End If

            Select Case Me.mSBState
                Case SimulatedInfoState.None '输入搜索关键字 查询
                    Dim itemSub As HtmlElement = Nothing
                    itemSub = Me.FindItem("input", "text", "kw")
                    If itemSub IsNot Nothing Then
                        itemSub.SetAttribute("value", Me.mCurrentSimulatedInfo.StrKey)
                    End If
                    item = Me.FindItem("input", "submit", "su")
                    item.InvokeMember("click")
                    Me.mSBState = SimulatedInfoState.Search

                Case SimulatedInfoState.Search
                    Dim isFind As Boolean = False

                    '1、获取所有的 title 和 url
                    '<div class="c-tools" id="tools_16229887887007898381_1" data-tools="{title:苹果助手_苹果手机助手_同步网络官方下载&quot;,&quot;url&quot;:&quot;http://www.baidu.com/link?url=9b0N2DLf20dGJ0_EJvEY1eSB-f6qjoOrST-z5G-67-y&quot;}"><a class="c-tip-icon"><i class="c-icon c-icon-triangle-down-g"></i></a></div>
                    Dim strPattern As String = String.Format("{0}{1}", Me.mCurrentSimulatedInfo.StrTitleRegular, Me.mCurrentSimulatedInfo.StrUrlRegular) '"data-tools='{""title"":""[\s\S]*?"",""url"":""[\s\S]*?""}'"

                    Dim regex As New Regex(strPattern, RegexOptions.IgnoreCase)
                    Dim matchCollection As MatchCollection = regex.Matches(Me.webSimulated.DocumentText.ToLower())
                    For Each mc As Match In matchCollection
                        '2、匹配 title 
                        Dim strTitlePattern As String = Me.mCurrentSimulatedInfo.StrTitleRegular '"data-tools='{""title"":""[\s\S]*?"""
                        Dim regexTitle As New Regex(strTitlePattern, RegexOptions.IgnoreCase)
                        Dim matchTitleCollection As MatchCollection = regexTitle.Matches(mc.Value.ToLower().ToString())

                        Dim strUrlPattern As String = Me.mCurrentSimulatedInfo.StrUrlRegular '"""url"":""[\s\S]*?""}'"
                        Dim regexUrl As New Regex(strUrlPattern, RegexOptions.IgnoreCase)
                        Dim matchUrlCollection As MatchCollection = regexUrl.Matches(mc.Value.ToLower().ToString())

                        If matchTitleCollection.Count = 0 OrElse matchUrlCollection.Count = 0 Then
                            Continue For
                        End If

                        Dim strTitle As String = matchTitleCollection(0).Value.ToLower().ToString().Replace("data-tools='{", "").Replace("title"":", "").Replace("""", "").Replace(",", "")
                        Dim strUrl As String = matchUrlCollection(0).Value.ToLower().ToString().Replace("""url"":""", "").Replace("""}'", "").Replace("""", "").Replace(",", "")

                        If strTitle = Me.mCurrentSimulatedInfo.StrTitle.ToLower() Then
                            '3、找到title 对应的 url 的 A 标签 点击它
                            Dim link As HtmlElementCollection = Me.webSimulated.Document.GetElementsByTagName("A")
                            For Each he As HtmlElement In link
                                If he.GetAttribute("href").ToLower() = strUrl.ToLower() Then
                                    he.InvokeMember("click")
                                    Me.mSBState = SimulatedInfoState.ClickToView
                                    isFind = True
                                    Continue For
                                End If
                            Next
                        End If
                    Next

                    '4、如果当前页没有找到则翻页

                    If Not isFind AndAlso Me.mIntPageCount < 20 Then
                        Dim link As HtmlElementCollection = Me.webSimulated.Document.GetElementsByTagName("A")
                        For Each he As HtmlElement In link
                            Debug.Print(he.GetAttribute("href"))
                            If he.InnerText = "下一页>" Then
                                he.InvokeMember("click")
                                Me.mIntPageCount = Me.mIntPageCount + 1
                                Dim ran As Random = New Random()
                                Utility.WaitSeconds(ran.Next(3, 8))
                            End If
                        Next


                    ElseIf isFind Then
                        '5.1、找到回传给服务器
                        Dim lstTemp As New List(Of Integer)
                        lstTemp.Add(Me.mCurrentSimulatedInfo.IntID)
                        lstTemp.Add(Me.mCurrentSimulatedInfo.TypeId)
                        lstTemp.Add(1)
                        Dim obj As Object() = New Object() {Me.mCurrentSimulatedInfo.IntID, Me.mCurrentSimulatedInfo.TypeId, 1}
                        Dim thr As New Thread(New ParameterizedThreadStart(AddressOf PostBack))
                        thr.IsBackground = True
                        thr.Start(lstTemp)
                        Me.mExecuteCompleted = True

                    ElseIf Not isFind AndAlso Me.mIntPageCount >= 20 Then
                        '5.2、找不到回传给服务器
                        Dim lstTemp As New List(Of Integer)
                        lstTemp.Add(Me.mCurrentSimulatedInfo.IntID)
                        lstTemp.Add(Me.mCurrentSimulatedInfo.TypeId)
                        lstTemp.Add(0)
                        Dim thr As New Thread(New ParameterizedThreadStart(AddressOf PostBack))
                        thr.IsBackground = True
                        thr.Start(lstTemp)
                        Me.mExecuteCompleted = True

                    End If
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Flash2ProcForm_OnDocumentCompleted")
        End Try
    End Sub

    Private Sub PostBack(ByVal obj As Object)
        Try
            Dim lstTemp As List(Of Integer) = CType(obj, List(Of Integer))
            Dim strSendUrl As String = Me.mStrSendUrl
            Dim IntID As Integer = CType(lstTemp(0), Integer)
            Dim TypeId As Integer = CType(lstTemp(1), Integer)
            Dim intRelust As Integer = CType(lstTemp(2), Integer)
            strSendUrl = String.Format(strSendUrl, IntID, TypeId, intRelust)
            Dim strContent As String = GetContentStringFromUrl(strSendUrl)

            If String.IsNullOrEmpty(strContent) Then
                Common.Log(String.Format("Work Failure:{0} {1}", IntID, intRelust))
                Return
            End If

            Common.Log(String.Format("Work:{0} {1}", IntID, intRelust))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "PostBack")
        End Try

    End Sub

    Protected Function FindItem(tagFrameName As String, strClassName As String, strId As String) As HtmlElement
        Dim item As HtmlElement = Nothing
        Try
            If Me.webSimulated.Document IsNot Nothing Then
                If Not String.IsNullOrEmpty(strId) Then
                    item = Me.webSimulated.Document.GetElementById(strId)
                    If item IsNot Nothing Then
                        GoTo DoExit
                    End If
                End If

                Dim list As HtmlElementCollection = Me.webSimulated.Document.GetElementsByTagName(tagFrameName)
                For Each [sub] As HtmlElement In list
                    If String.IsNullOrEmpty([sub].OuterHtml) Then
                        Continue For
                    End If

                    Dim strOuter As String = [sub].OuterHtml.ToLower()
                    If strOuter.Contains(Convert.ToString("class=") & strClassName) OrElse strOuter.Contains(Convert.ToString("class=""") & strClassName) Then
                        item = [sub]
                        Exit For
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Flash2ProcForm_FindItem")
        End Try
DoExit:
        Return item
    End Function
    Private Sub OnNewWindow3(sender As iTong.Components.tbWebBrowserEx, ByRef ppDisp As Object, ByRef Cancel As Boolean, dwFlags As UInteger, bstrUrlContext As String, bstrUrl As String)
        Cancel = True
        sender.Navigate(bstrUrl)
    End Sub
End Class

Public Class SimulatedInfo

    Private mId As Integer = 0
    Private mTypeId As Integer = 0
    Private mStrKey As String = ""
    Private mStrTitle As String = ""
    Private mStrTitleRegular As String = "data-tools='{""title"":""[\s\S]*?"","
    Private mStrUrlRegular As String = """url"":""[\s\S]*?""}'"

    Public Property IntID() As Integer
        Get
            Return Me.mId
        End Get
        Set(ByVal value As Integer)
            Me.mId = value
        End Set
    End Property

    Public Property TypeId() As Integer
        Get
            Return Me.mTypeId
        End Get
        Set(ByVal value As Integer)
            Me.mTypeId = value
        End Set
    End Property
    Public Property StrKey() As String
        Get
            Return Me.mStrKey
        End Get
        Set(ByVal value As String)
            Me.mStrKey = value
        End Set
    End Property

    Public Property StrTitle() As String
        Get
            Return Me.mStrTitle
        End Get
        Set(ByVal value As String)
            Me.mStrTitle = value
        End Set
    End Property

    Public Property StrTitleRegular() As String
        Get
            Return Me.mStrTitleRegular
        End Get
        Set(ByVal value As String)
            Me.mStrTitleRegular = value
        End Set
    End Property

    Public Property StrUrlRegular() As String
        Get
            Return Me.mStrUrlRegular
        End Get
        Set(ByVal value As String)
            Me.mStrUrlRegular = value
        End Set
    End Property

    Public Sub New()

    End Sub

End Class

Public Enum SimulatedInfoState
    None = 0
    Search = 1
    PageDown = 2
    ClickToView = 3
End Enum
