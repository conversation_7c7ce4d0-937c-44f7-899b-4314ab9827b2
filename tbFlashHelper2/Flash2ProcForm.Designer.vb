﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class Flash2ProcForm
    Inherits System.Windows.Forms.Form

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(Flash2ProcForm))
        Me.webSimulated = New iTong.Components.tbWebBrowserEx()
        Me.bgwSimulated = New System.ComponentModel.BackgroundWorker()
        Me.SuspendLayout()
        '
        'webSimulated
        '
        Me.webSimulated.AllowWebBrowserDrop = False
        Me.webSimulated.Client = ""
        Me.webSimulated.DeviceList = Nothing
        Me.webSimulated.Dock = System.Windows.Forms.DockStyle.Fill
        Me.webSimulated.IsConnected = False
        Me.webSimulated.IsWebBrowserContextMenuEnabled = False
        Me.webSimulated.Jailbreaked = False
        Me.webSimulated.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.webSimulated.LoadingFont = Nothing
        Me.webSimulated.LoadingGif = CType(resources.GetObject("webSimulated.LoadingGif"), System.Drawing.Image)
        Me.webSimulated.Location = New System.Drawing.Point(0, 0)
        Me.webSimulated.MinimumSize = New System.Drawing.Size(20, 20)
        Me.webSimulated.Name = "webSimulated"
        Me.webSimulated.ShowLoadingWait = False
        Me.webSimulated.ShowNavigateErrorPage = True
        Me.webSimulated.ShowProgress = True
        Me.webSimulated.Size = New System.Drawing.Size(1272, 774)
        Me.webSimulated.SN = ""
        Me.webSimulated.TabIndex = 2
        Me.webSimulated.UserInfo = ""
        Me.webSimulated.UserInfoEncode = ""
        '
        'bgwSimulated
        '
        '
        'Flash2ProcForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(1272, 774)
        Me.Controls.Add(Me.webSimulated)
        Me.Location = New System.Drawing.Point(-2000, -2000)
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "Flash2ProcForm"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.Manual
        Me.Text = "Flash2ProcForm"
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents webSimulated As iTong.Components.tbWebBrowserEx
    Friend WithEvents bgwSimulated As System.ComponentModel.BackgroundWorker

End Class
