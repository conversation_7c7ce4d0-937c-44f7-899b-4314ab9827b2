﻿<?xml version="1.0" encoding="utf-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
    <Fragment>
        <DirectoryRef Id="INSTALLFOLDER">
            <Directory Id="dir5B673ED707FFD3B07E63CF621B50001B" Name="Codec" />
            <Directory Id="dir608A8623D72D87D1C5E4AC92CD734AE7" Name="Font" />
            <Directory Id="dir23EEB6A7E0429F277985FA4BC5B2C64F" Name="Lang" />
            <Directory Id="dir07100F2E598E14B511BAF734827AE4AB" Name="locales" />
        </DirectoryRef>
    </Fragment>
    <Fragment>
        <ComponentGroup Id="ALLCOMP">
            <Component Id="cmpB3A8302AF92D861DE3B5AE9D65872D85" Directory="INSTALLFOLDER" Guid="338684F6-CA7F-4281-B94F-BABE5F3554E4">
                <File Id="fil5114D6B8032EF2C24581B8E18CB5EA70" KeyPath="yes" Source="$(var.DirPath)\AirDroidBizDaemonMonitor.exe" />
            </Component>
            <Component Id="cmp65C18B94E6B9217C19D4B0DF30CC16C6" Directory="INSTALLFOLDER" Guid="CF12C521-2E85-4A48-8F49-4781228CAD0B">
                <Class Id="{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}" Context="InprocServer32" Description="iTong.CoreModule.DBRecoveryHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.DBRecoveryHelper" Description="iTong.CoreModule.DBRecoveryHelper" />
                </Class>
                <Class Id="{00AA37CB-1997-3403-BC52-22BDC014C701}" Context="InprocServer32" Description="iTong.CoreModule.EmoticonItem" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.EmoticonItem" Description="iTong.CoreModule.EmoticonItem" />
                </Class>
                <Class Id="{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}" Context="InprocServer32" Description="iTong.CoreModule.WhatsAppGroupMember" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WhatsAppGroupMember" Description="iTong.CoreModule.WhatsAppGroupMember" />
                </Class>
                <Class Id="{0433444A-266A-3F82-800A-D25089DC1DBC}" Context="InprocServer32" Description="iTong.CoreModule.SocialUserInfoCompare" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.SocialUserInfoCompare" Description="iTong.CoreModule.SocialUserInfoCompare" />
                </Class>
                <Class Id="{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}" Context="InprocServer32" Description="iTong.CoreModule.CoreSignatureArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.CoreSignatureArgs" Description="iTong.CoreModule.CoreSignatureArgs" />
                </Class>
                <Class Id="{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}" Context="InprocServer32" Description="iTong.CoreModule.ExportProgressEventArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ExportProgressEventArgs" Description="iTong.CoreModule.ExportProgressEventArgs" />
                </Class>
                <Class Id="{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}" Context="InprocServer32" Description="iTong.CoreModule.ChargeResultArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ChargeResultArgs" Description="iTong.CoreModule.ChargeResultArgs" />
                </Class>
                <Class Id="{122D1841-4823-3800-B757-1818AEAF48B1}" Context="InprocServer32" Description="iTong.CoreModule.AppItemEventArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.AppItemEventArgs" Description="iTong.CoreModule.AppItemEventArgs" />
                </Class>
                <Class Id="{16420354-F578-3FC1-A858-FDEA7ED87EFC}" Context="InprocServer32" Description="iTong.CoreModule.PremiumFeatureHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.PremiumFeatureHelper" Description="iTong.CoreModule.PremiumFeatureHelper" />
                </Class>
                <Class Id="{1A383266-1DF6-3724-851A-8010B3B54D6E}" Context="InprocServer32" Description="iTong.CoreModule.WeChatHeadLogoModel" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WeChatHeadLogoModel" Description="iTong.CoreModule.WeChatHeadLogoModel" />
                </Class>
                <Class Id="{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}" Context="InprocServer32" Description="iTong.CoreModule.ContentLayout" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ContentLayout" Description="iTong.CoreModule.ContentLayout" />
                </Class>
                <Class Id="{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}" Context="InprocServer32" Description="iTong.CoreModule.IMInstancePara" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.IMInstancePara" Description="iTong.CoreModule.IMInstancePara" />
                </Class>
                <Class Id="{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}" Context="InprocServer32" Description="iTong.CoreModule.QQFriendListModel" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.QQFriendListModel" Description="iTong.CoreModule.QQFriendListModel" />
                </Class>
                <Class Id="{25279E72-F8B8-36AD-A984-2D052E765496}" Context="InprocServer32" Description="iTong.CoreModule.ShowWeiboArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ShowWeiboArgs" Description="iTong.CoreModule.ShowWeiboArgs" />
                </Class>
                <Class Id="{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}" Context="InprocServer32" Description="iTong.CoreModule.GetDeviceForWebArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.GetDeviceForWebArgs" Description="iTong.CoreModule.GetDeviceForWebArgs" />
                </Class>
                <Class Id="{2C70C3E7-6DD9-379A-9522-60432FDD4D63}" Context="InprocServer32" Description="iTong.CoreModule.QQFriendGroupModel" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.QQFriendGroupModel" Description="iTong.CoreModule.QQFriendGroupModel" />
                </Class>
                <Class Id="{2F4C6EE9-9F3C-3314-98A4-1781CE474303}" Context="InprocServer32" Description="iTong.CoreModule.SocialFriendInfoBase" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.SocialFriendInfoBase" Description="iTong.CoreModule.SocialFriendInfoBase" />
                </Class>
                <Class Id="{3B7C5BB0-F7FF-3545-9492-770861639AC1}" Context="InprocServer32" Description="iTong.CoreModule.IMCachePara" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.IMCachePara" Description="iTong.CoreModule.IMCachePara" />
                </Class>
                <Class Id="{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}" Context="InprocServer32" Description="iTong.CoreModule.WhatsAppMessage" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WhatsAppMessage" Description="iTong.CoreModule.WhatsAppMessage" />
                </Class>
                <Class Id="{3D497C22-738E-3409-B91E-1DA0555C9D17}" Context="InprocServer32" Description="iTong.CoreModule.WhatsAppSession" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WhatsAppSession" Description="iTong.CoreModule.WhatsAppSession" />
                </Class>
                <Class Id="{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}" Context="InprocServer32" Description="iTong.CoreModule.MomentInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.MomentInfo" Description="iTong.CoreModule.MomentInfo" />
                </Class>
                <Class Id="{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}" Context="InprocServer32" Description="iTong.CoreModule.FunctionInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.FunctionInfo" Description="iTong.CoreModule.FunctionInfo" />
                </Class>
                <Class Id="{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}" Context="InprocServer32" Description="iTong.CoreModule.VoiceMemoInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.VoiceMemoInfo" Description="iTong.CoreModule.VoiceMemoInfo" />
                </Class>
                <Class Id="{501113FE-E25F-3827-853D-EF0D4B6903AF}" Context="InprocServer32" Description="iTong.CoreModule.AppItemRecom" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.AppItemRecom" Description="iTong.CoreModule.AppItemRecom" />
                </Class>
                <Class Id="{519366A3-506B-3C6C-B681-64F7EF3EDD4F}" Context="InprocServer32" Description="iTong.CoreModule.WeChatRecoveryPara" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WeChatRecoveryPara" Description="iTong.CoreModule.WeChatRecoveryPara" />
                </Class>
                <Class Id="{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}" Context="InprocServer32" Description="iTong.CoreModule.BackupDecryptArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.BackupDecryptArgs" Description="iTong.CoreModule.BackupDecryptArgs" />
                </Class>
                <Class Id="{57700B26-B806-3A1D-AD86-8C76D452D021}" Context="InprocServer32" Description="iTong.CoreModule.QQFriendModel" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.QQFriendModel" Description="iTong.CoreModule.QQFriendModel" />
                </Class>
                <Class Id="{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}" Context="InprocServer32" Description="iTong.CoreModule.iPhoneInstallHelperEx" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.iPhoneInstallHelperEx" Description="iTong.CoreModule.iPhoneInstallHelperEx" />
                </Class>
                <Class Id="{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}" Context="InprocServer32" Description="iTong.CoreModule.ServerIniSetting" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ServerIniSetting" Description="iTong.CoreModule.ServerIniSetting" />
                </Class>
                <Class Id="{648A8117-BC75-3107-9CBF-209A0C9450A0}" Context="InprocServer32" Description="iTong.CoreModule.SessionDataInfoModel" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.SessionDataInfoModel" Description="iTong.CoreModule.SessionDataInfoModel" />
                </Class>
                <Class Id="{6524F659-E7F3-3585-8A06-C1F37617AF9F}" Context="InprocServer32" Description="ComponentAce.Compression.Libs.zlib.SupportClass" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="ComponentAce.Compression.Libs.zlib.SupportClass" Description="ComponentAce.Compression.Libs.zlib.SupportClass" />
                </Class>
                <Class Id="{673DA492-8505-334A-8F4D-1BD10D3B7949}" Context="InprocServer32" Description="iTong.CoreModule.ApplePackageReaderSort" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ApplePackageReaderSort" Description="iTong.CoreModule.ApplePackageReaderSort" />
                </Class>
                <Class Id="{6A89E11E-6033-35F1-BB71-774C2AF29F58}" Context="InprocServer32" Description="iTong.CoreModule.WeChatRecoveryEventArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WeChatRecoveryEventArgs" Description="iTong.CoreModule.WeChatRecoveryEventArgs" />
                </Class>
                <Class Id="{6F246BBA-4505-3B0E-B841-D4B12D45995E}" Context="InprocServer32" Description="iTong.CoreModule.QQChatInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.QQChatInfo" Description="iTong.CoreModule.QQChatInfo" />
                </Class>
                <Class Id="{6F794178-0DCE-3CD8-89A1-22604A171587}" Context="InprocServer32" Description="iTong.CoreModule.iPhoneDeviceHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.iPhoneDeviceHelper" Description="iTong.CoreModule.iPhoneDeviceHelper" />
                </Class>
                <Class Id="{705B104C-1299-3F75-932E-6ECEE3C8EA53}" Context="InprocServer32" Description="iTong.CoreModule.LoadAloneBackupInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.LoadAloneBackupInfo" Description="iTong.CoreModule.LoadAloneBackupInfo" />
                </Class>
                <Class Id="{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}" Context="InprocServer32" Description="iTong.CoreModule.ShowAppleIdLoginArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ShowAppleIdLoginArgs" Description="iTong.CoreModule.ShowAppleIdLoginArgs" />
                </Class>
                <Class Id="{76717735-5D00-3E21-8DB6-45FA71F5BEBA}" Context="InprocServer32" Description="iTong.CoreModule.frmDeviceBase" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.frmDeviceBase" Description="iTong.CoreModule.frmDeviceBase" />
                </Class>
                <Class Id="{77398F92-00CC-35F9-BC78-DC127B201620}" Context="InprocServer32" Description="iTong.CoreModule.WebSiteObject" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WebSiteObject" Description="iTong.CoreModule.WebSiteObject" />
                </Class>
                <Class Id="{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}" Context="InprocServer32" Description="iTong.CoreModule.JinBaoHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.JinBaoHelper" Description="iTong.CoreModule.JinBaoHelper" />
                </Class>
                <Class Id="{8046429B-DA87-3F28-A19D-A52A1EF98B97}" Context="InprocServer32" Description="iTong.Components.tbSimpleWebBrower" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.Components.tbSimpleWebBrower" Description="iTong.Components.tbSimpleWebBrower" />
                </Class>
                <Class Id="{82534348-BF5A-3870-8EEE-47BC744E8675}" Context="InprocServer32" Description="iTong.CoreModule.ExportHtmlUserInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ExportHtmlUserInfo" Description="iTong.CoreModule.ExportHtmlUserInfo" />
                </Class>
                <Class Id="{84954FA0-A363-3457-BC3A-0DDF249871F0}" Context="InprocServer32" Description="iTong.CoreModule.SiteXmlHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.SiteXmlHelper" Description="iTong.CoreModule.SiteXmlHelper" />
                </Class>
                <Class Id="{877B911D-B856-364D-BCFC-FF01AC7B1425}" Context="InprocServer32" Description="iTong.CoreModule.QQFriendInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.QQFriendInfo" Description="iTong.CoreModule.QQFriendInfo" />
                </Class>
                <Class Id="{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}" Context="InprocServer32" Description="iTong.CoreModule.PremiumFeatureInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.PremiumFeatureInfo" Description="iTong.CoreModule.PremiumFeatureInfo" />
                </Class>
                <Class Id="{8C0CA844-E2E8-335B-8A6E-9154D60F458B}" Context="InprocServer32" Description="iTong.CoreModule.WebSiteHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WebSiteHelper" Description="iTong.CoreModule.WebSiteHelper" />
                </Class>
                <Class Id="{952DE15C-78C5-3746-AB14-58AD7E2083EE}" Context="InprocServer32" Description="iTong.CoreModule.WebSiteHelperBase" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WebSiteHelperBase" Description="iTong.CoreModule.WebSiteHelperBase" />
                </Class>
                <Class Id="{9590E35E-7AC2-3385-A677-E67F2C02397F}" Context="InprocServer32" Description="iTong.CoreModule.IMExportPara" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.IMExportPara" Description="iTong.CoreModule.IMExportPara" />
                </Class>
                <Class Id="{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}" Context="InprocServer32" Description="iTong.CoreModule.IMHtmlNodeHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.IMHtmlNodeHelper" Description="iTong.CoreModule.IMHtmlNodeHelper" />
                </Class>
                <Class Id="{997D8742-9389-396D-8E0C-63621655FAD5}" Context="InprocServer32" Description="iTong.CoreModule.ChargeDevice" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ChargeDevice" Description="iTong.CoreModule.ChargeDevice" />
                </Class>
                <Class Id="{9A4414AE-C267-356A-9299-30E4D3616455}" Context="InprocServer32" Description="iTong.CoreModule.SessionDataModel" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.SessionDataModel" Description="iTong.CoreModule.SessionDataModel" />
                </Class>
                <Class Id="{9A744074-08F2-35CE-ADCE-8484162B1A03}" Context="InprocServer32" Description="iTong.CoreModule.WeChatChatInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WeChatChatInfo" Description="iTong.CoreModule.WeChatChatInfo" />
                </Class>
                <Class Id="{A12D0B16-E995-33DB-AF47-67EB49B03E23}" Context="InprocServer32" Description="iTong.CoreModule.ChargePara" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ChargePara" Description="iTong.CoreModule.ChargePara" />
                </Class>
                <Class Id="{A8047D0E-E5C8-3232-9DCE-9F0509E27559}" Context="InprocServer32" Description="iTong.CoreModule.PopMsgHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.PopMsgHelper" Description="iTong.CoreModule.PopMsgHelper" />
                </Class>
                <Class Id="{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}" Context="InprocServer32" Description="iTong.CoreModule.SessionInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.SessionInfo" Description="iTong.CoreModule.SessionInfo" />
                </Class>
                <Class Id="{AA740403-EF74-3E92-8C48-8630343DAB46}" Context="InprocServer32" Description="iTong.CoreModule.UserInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.UserInfo" Description="iTong.CoreModule.UserInfo" />
                </Class>
                <Class Id="{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}" Context="InprocServer32" Description="iTong.CoreModule.IniSetting" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.IniSetting" Description="iTong.CoreModule.IniSetting" />
                </Class>
                <Class Id="{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}" Context="InprocServer32" Description="iTong.CoreModule.Backup2AloneBackupInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.Backup2AloneBackupInfo" Description="iTong.CoreModule.Backup2AloneBackupInfo" />
                </Class>
                <Class Id="{ADB27205-B8F2-3AC7-AF76-4995C23A8759}" Context="InprocServer32" Description="iTong.CoreModule.WhatsAppGroupInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WhatsAppGroupInfo" Description="iTong.CoreModule.WhatsAppGroupInfo" />
                </Class>
                <Class Id="{AF92F634-57FB-3684-A469-EB7120C74359}" Context="InprocServer32" Description="iTong.CoreModule.ExportHtmlMainInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ExportHtmlMainInfo" Description="iTong.CoreModule.ExportHtmlMainInfo" />
                </Class>
                <Class Id="{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}" Context="InprocServer32" Description="iTong.Components.tbWebBrowser" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.Components.tbWebBrowser" Description="iTong.Components.tbWebBrowser" />
                </Class>
                <Class Id="{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}" Context="InprocServer32" Description="ComponentAce.Compression.Libs.zlib.ZStreamException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="ComponentAce.Compression.Libs.zlib.ZStreamException" Description="ComponentAce.Compression.Libs.zlib.ZStreamException" />
                </Class>
                <Class Id="{BA500650-FE75-3910-99D9-75DEA659ACAD}" Context="InprocServer32" Description="iTong.CoreModule.ImportPhotoPackageInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ImportPhotoPackageInfo" Description="iTong.CoreModule.ImportPhotoPackageInfo" />
                </Class>
                <Class Id="{BD5FDD49-2A60-3292-97E6-6BD6771FD346}" Context="InprocServer32" Description="iTong.CoreModule.IMHtmlNode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.IMHtmlNode" Description="iTong.CoreModule.IMHtmlNode" />
                </Class>
                <Class Id="{C180736A-E1D2-3885-9A90-6EEC512B6258}" Context="InprocServer32" Description="iTong.CoreModule.SocialUserInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.SocialUserInfo" Description="iTong.CoreModule.SocialUserInfo" />
                </Class>
                <Class Id="{C4BC082A-7CBE-3AB6-B980-76B080C599D3}" Context="InprocServer32" Description="iTong.CoreModule.WechatHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WechatHelper" Description="iTong.CoreModule.WechatHelper" />
                </Class>
                <Class Id="{C8204D51-FF87-30C2-94E1-476D9796299A}" Context="InprocServer32" Description="iTong.CoreModule.WeChatFriendInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WeChatFriendInfo" Description="iTong.CoreModule.WeChatFriendInfo" />
                </Class>
                <Class Id="{CB0957A6-2828-318E-BF7B-364B31D30C3C}" Context="InprocServer32" Description="ComponentAce.Compression.Libs.zlib.ZStream" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="ComponentAce.Compression.Libs.zlib.ZStream" Description="ComponentAce.Compression.Libs.zlib.ZStream" />
                </Class>
                <Class Id="{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}" Context="InprocServer32" Description="iTong.CoreModule.ApplePackageReader" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ApplePackageReader" Description="iTong.CoreModule.ApplePackageReader" />
                </Class>
                <Class Id="{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}" Context="InprocServer32" Description="iTong.CoreModule.BackupPwdArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.BackupPwdArgs" Description="iTong.CoreModule.BackupPwdArgs" />
                </Class>
                <Class Id="{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}" Context="InprocServer32" Description="iTong.CoreModule.ScreenshotSetting" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ScreenshotSetting" Description="iTong.CoreModule.ScreenshotSetting" />
                </Class>
                <Class Id="{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}" Context="InprocServer32" Description="iTong.CoreModule.VIPGuideArg" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.VIPGuideArg" Description="iTong.CoreModule.VIPGuideArg" />
                </Class>
                <Class Id="{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}" Context="InprocServer32" Description="iTong.CoreModule.WhatsAppMedia" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WhatsAppMedia" Description="iTong.CoreModule.WhatsAppMedia" />
                </Class>
                <Class Id="{E0644D31-68C2-3124-B042-07B482133BDE}" Context="InprocServer32" Description="iTong.CoreModule.PluginLogin" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.PluginLogin" Description="iTong.CoreModule.PluginLogin" />
                </Class>
                <Class Id="{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}" Context="InprocServer32" Description="iTong.CoreModule.FavouriteInfo" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.FavouriteInfo" Description="iTong.CoreModule.FavouriteInfo" />
                </Class>
                <Class Id="{E68941CB-4938-3A54-9B67-D1FF9477094C}" Context="InprocServer32" Description="iTong.CoreModule.ActionCollectHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ActionCollectHelper" Description="iTong.CoreModule.ActionCollectHelper" />
                </Class>
                <Class Id="{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}" Context="InprocServer32" Description="iTong.CoreModule.IMLoadPara" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.IMLoadPara" Description="iTong.CoreModule.IMLoadPara" />
                </Class>
                <Class Id="{ED592A94-668D-3C53-A75A-906AFBF92F0A}" Context="InprocServer32" Description="iTong.Components.tbWebBrowserEx" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.Components.tbWebBrowserEx" Description="iTong.Components.tbWebBrowserEx" />
                </Class>
                <Class Id="{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}" Context="InprocServer32" Description="ComponentAce.Compression.Libs.zlib.zlibConst" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="ComponentAce.Compression.Libs.zlib.zlibConst" Description="ComponentAce.Compression.Libs.zlib.zlibConst" />
                </Class>
                <Class Id="{********-1E7B-334F-BBAF-914E113ACB97}" Context="InprocServer32" Description="iTong.CoreModule.BackupArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.BackupArgs" Description="iTong.CoreModule.BackupArgs" />
                </Class>
                <Class Id="{********-6E90-36E3-8644-944482ED7671}" Context="InprocServer32" Description="iTong.CoreModule.AloneBackupsHelper" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.AloneBackupsHelper" Description="iTong.CoreModule.AloneBackupsHelper" />
                </Class>
                <Class Id="{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}" Context="InprocServer32" Description="iTong.CoreModule.HelperExeManager" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.HelperExeManager" Description="iTong.CoreModule.HelperExeManager" />
                </Class>
                <Class Id="{F9904FFF-7BC1-3721-8980-7464ABF27211}" Context="InprocServer32" Description="iTong.CoreModule.WeChatFriendModel" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.WeChatFriendModel" Description="iTong.CoreModule.WeChatFriendModel" />
                </Class>
                <Class Id="{FD986FE8-D7C3-3A3C-969B-BA81617B928C}" Context="InprocServer32" Description="iTong.CoreModule.SociaChatBase" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.SociaChatBase" Description="iTong.CoreModule.SociaChatBase" />
                </Class>
                <Class Id="{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}" Context="InprocServer32" Description="iTong.CoreModule.ShowAppDetailArgs" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="iTong.CoreModule.ShowAppDetailArgs" Description="iTong.CoreModule.ShowAppDetailArgs" />
                </Class>
                <File Id="filE85085A783D0212A091B997F7994D835" KeyPath="yes" Source="$(var.DirPath)\Android.dll" />
                <ProgId Id="Record" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.DBRecoveryHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\InprocServer32" Name="Class" Value="iTong.CoreModule.DBRecoveryHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{008C76C2-B4A1-3A9B-8FD1-9B3556224DCC}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.EmoticonItem" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\InprocServer32" Name="Class" Value="iTong.CoreModule.EmoticonItem" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{00AA37CB-1997-3403-BC52-22BDC014C701}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WhatsAppGroupMember" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\InprocServer32" Name="Class" Value="iTong.CoreModule.WhatsAppGroupMember" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{03CE6F80-0ADF-3C9B-AABD-8E82E8779919}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.SocialUserInfoCompare" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\InprocServer32" Name="Class" Value="iTong.CoreModule.SocialUserInfoCompare" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0433444A-266A-3F82-800A-D25089DC1DBC}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.CoreSignatureArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\InprocServer32" Name="Class" Value="iTong.CoreModule.CoreSignatureArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{04C204F8-7A12-36B2-AE54-7AE7AC8B08B9}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ExportProgressEventArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\InprocServer32" Name="Class" Value="iTong.CoreModule.ExportProgressEventArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{08FF24D2-CC1D-3FC3-9867-AE345BD1C201}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ChargeResultArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\InprocServer32" Name="Class" Value="iTong.CoreModule.ChargeResultArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{0E89891B-3FB8-3CF7-ACB0-BBE8DD06A52C}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.AppItemEventArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\InprocServer32" Name="Class" Value="iTong.CoreModule.AppItemEventArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{122D1841-4823-3800-B757-1818AEAF48B1}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.PremiumFeatureHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\InprocServer32" Name="Class" Value="iTong.CoreModule.PremiumFeatureHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{16420354-F578-3FC1-A858-FDEA7ED87EFC}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WeChatHeadLogoModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\InprocServer32" Name="Class" Value="iTong.CoreModule.WeChatHeadLogoModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A383266-1DF6-3724-851A-8010B3B54D6E}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ContentLayout" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\InprocServer32" Name="Class" Value="iTong.CoreModule.ContentLayout" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1A618ACD-6EC4-39CD-BDBC-B2111004DD53}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.IMInstancePara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\InprocServer32" Name="Class" Value="iTong.CoreModule.IMInstancePara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F0689B3-879C-3D2A-9FA6-5DD71DE02F75}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.QQFriendListModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\InprocServer32" Name="Class" Value="iTong.CoreModule.QQFriendListModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{1F9A16A6-EA77-3E07-A117-3A3A514E89FA}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ShowWeiboArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\InprocServer32" Name="Class" Value="iTong.CoreModule.ShowWeiboArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{25279E72-F8B8-36AD-A984-2D052E765496}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.GetDeviceForWebArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\InprocServer32" Name="Class" Value="iTong.CoreModule.GetDeviceForWebArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{280FED4C-0466-33B0-B4CD-97D8C05FE3EA}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.QQFriendGroupModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\InprocServer32" Name="Class" Value="iTong.CoreModule.QQFriendGroupModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2C70C3E7-6DD9-379A-9522-60432FDD4D63}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.SocialFriendInfoBase" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\InprocServer32" Name="Class" Value="iTong.CoreModule.SocialFriendInfoBase" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2F4C6EE9-9F3C-3314-98A4-1781CE474303}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.IMCachePara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\InprocServer32" Name="Class" Value="iTong.CoreModule.IMCachePara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3B7C5BB0-F7FF-3545-9492-770861639AC1}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WhatsAppMessage" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\InprocServer32" Name="Class" Value="iTong.CoreModule.WhatsAppMessage" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3CEBF240-86F2-3733-8732-AE2DFACBAF1A}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WhatsAppSession" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\InprocServer32" Name="Class" Value="iTong.CoreModule.WhatsAppSession" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{3D497C22-738E-3409-B91E-1DA0555C9D17}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.MomentInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\InprocServer32" Name="Class" Value="iTong.CoreModule.MomentInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{47B8AA60-E92A-3E67-ADF0-D84BBC08B2A5}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.FunctionInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\InprocServer32" Name="Class" Value="iTong.CoreModule.FunctionInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4BB44B0D-EB47-38AB-84E1-4955D9BC07E9}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.VoiceMemoInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\InprocServer32" Name="Class" Value="iTong.CoreModule.VoiceMemoInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{4C035E8E-6648-3A58-9277-1A7AFBC9E8EB}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.AppItemRecom" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\InprocServer32" Name="Class" Value="iTong.CoreModule.AppItemRecom" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{501113FE-E25F-3827-853D-EF0D4B6903AF}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WeChatRecoveryPara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\InprocServer32" Name="Class" Value="iTong.CoreModule.WeChatRecoveryPara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{519366A3-506B-3C6C-B681-64F7EF3EDD4F}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.BackupDecryptArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\InprocServer32" Name="Class" Value="iTong.CoreModule.BackupDecryptArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{571AB198-1007-3FAD-BBE1-DA3EF3DB7E61}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.QQFriendModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\InprocServer32" Name="Class" Value="iTong.CoreModule.QQFriendModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{57700B26-B806-3A1D-AD86-8C76D452D021}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.iPhoneInstallHelperEx" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\InprocServer32" Name="Class" Value="iTong.CoreModule.iPhoneInstallHelperEx" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5AC98AF1-FA20-3DF4-8B65-2FF32C2D3FAA}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ServerIniSetting" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\InprocServer32" Name="Class" Value="iTong.CoreModule.ServerIniSetting" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5DB6E0B4-FBE3-34A7-AF2B-384DB95DB9CF}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.SessionDataInfoModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\InprocServer32" Name="Class" Value="iTong.CoreModule.SessionDataInfoModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{648A8117-BC75-3107-9CBF-209A0C9450A0}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\InprocServer32\*******" Name="Class" Value="ComponentAce.Compression.Libs.zlib.SupportClass" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\InprocServer32" Name="Class" Value="ComponentAce.Compression.Libs.zlib.SupportClass" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6524F659-E7F3-3585-8A06-C1F37617AF9F}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ApplePackageReaderSort" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\InprocServer32" Name="Class" Value="iTong.CoreModule.ApplePackageReaderSort" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{673DA492-8505-334A-8F4D-1BD10D3B7949}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WeChatRecoveryEventArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\InprocServer32" Name="Class" Value="iTong.CoreModule.WeChatRecoveryEventArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6A89E11E-6033-35F1-BB71-774C2AF29F58}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.QQChatInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\InprocServer32" Name="Class" Value="iTong.CoreModule.QQChatInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F246BBA-4505-3B0E-B841-D4B12D45995E}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.iPhoneDeviceHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\InprocServer32" Name="Class" Value="iTong.CoreModule.iPhoneDeviceHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6F794178-0DCE-3CD8-89A1-22604A171587}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.LoadAloneBackupInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\InprocServer32" Name="Class" Value="iTong.CoreModule.LoadAloneBackupInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{705B104C-1299-3F75-932E-6ECEE3C8EA53}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ShowAppleIdLoginArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\InprocServer32" Name="Class" Value="iTong.CoreModule.ShowAppleIdLoginArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{75FD2A3C-79F4-3DFD-8F0C-FEB3F889B044}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.frmDeviceBase" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\InprocServer32" Name="Class" Value="iTong.CoreModule.frmDeviceBase" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{76717735-5D00-3E21-8DB6-45FA71F5BEBA}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WebSiteObject" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\InprocServer32" Name="Class" Value="iTong.CoreModule.WebSiteObject" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{77398F92-00CC-35F9-BC78-DC127B201620}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.JinBaoHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\InprocServer32" Name="Class" Value="iTong.CoreModule.JinBaoHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7815DD42-2DE6-383E-A49D-D0DA12CAEA92}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\InprocServer32\*******" Name="Class" Value="iTong.Components.tbSimpleWebBrower" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\InprocServer32" Name="Class" Value="iTong.Components.tbSimpleWebBrower" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8046429B-DA87-3F28-A19D-A52A1EF98B97}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ExportHtmlUserInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\InprocServer32" Name="Class" Value="iTong.CoreModule.ExportHtmlUserInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{82534348-BF5A-3870-8EEE-47BC744E8675}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.SiteXmlHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\InprocServer32" Name="Class" Value="iTong.CoreModule.SiteXmlHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{84954FA0-A363-3457-BC3A-0DDF249871F0}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.QQFriendInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\InprocServer32" Name="Class" Value="iTong.CoreModule.QQFriendInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{877B911D-B856-364D-BCFC-FF01AC7B1425}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.PremiumFeatureInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\InprocServer32" Name="Class" Value="iTong.CoreModule.PremiumFeatureInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8837051F-AE29-3ED9-AAFD-78E9DBBADC94}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WebSiteHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\InprocServer32" Name="Class" Value="iTong.CoreModule.WebSiteHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8C0CA844-E2E8-335B-8A6E-9154D60F458B}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WebSiteHelperBase" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\InprocServer32" Name="Class" Value="iTong.CoreModule.WebSiteHelperBase" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{952DE15C-78C5-3746-AB14-58AD7E2083EE}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.IMExportPara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\InprocServer32" Name="Class" Value="iTong.CoreModule.IMExportPara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9590E35E-7AC2-3385-A677-E67F2C02397F}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.IMHtmlNodeHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\InprocServer32" Name="Class" Value="iTong.CoreModule.IMHtmlNodeHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9828CF42-5BA0-3896-A785-17BAEE0AF7B4}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ChargeDevice" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\InprocServer32" Name="Class" Value="iTong.CoreModule.ChargeDevice" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{997D8742-9389-396D-8E0C-63621655FAD5}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.SessionDataModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\InprocServer32" Name="Class" Value="iTong.CoreModule.SessionDataModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A4414AE-C267-356A-9299-30E4D3616455}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WeChatChatInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\InprocServer32" Name="Class" Value="iTong.CoreModule.WeChatChatInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{9A744074-08F2-35CE-ADCE-8484162B1A03}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ChargePara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\InprocServer32" Name="Class" Value="iTong.CoreModule.ChargePara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A12D0B16-E995-33DB-AF47-67EB49B03E23}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.PopMsgHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\InprocServer32" Name="Class" Value="iTong.CoreModule.PopMsgHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A8047D0E-E5C8-3232-9DCE-9F0509E27559}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.SessionInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\InprocServer32" Name="Class" Value="iTong.CoreModule.SessionInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{A9C48EA3-F7C1-3DA2-9031-1FC928EB7003}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.UserInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\InprocServer32" Name="Class" Value="iTong.CoreModule.UserInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AA740403-EF74-3E92-8C48-8630343DAB46}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.IniSetting" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\InprocServer32" Name="Class" Value="iTong.CoreModule.IniSetting" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AB7271E5-B2F6-3747-BFF3-FB9A0284790A}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.Backup2AloneBackupInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\InprocServer32" Name="Class" Value="iTong.CoreModule.Backup2AloneBackupInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ABCDEF9B-9727-361D-AAB5-1402E9DDDD3D}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WhatsAppGroupInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\InprocServer32" Name="Class" Value="iTong.CoreModule.WhatsAppGroupInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ADB27205-B8F2-3AC7-AF76-4995C23A8759}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ExportHtmlMainInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\InprocServer32" Name="Class" Value="iTong.CoreModule.ExportHtmlMainInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{AF92F634-57FB-3684-A469-EB7120C74359}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\InprocServer32\*******" Name="Class" Value="iTong.Components.tbWebBrowser" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\InprocServer32" Name="Class" Value="iTong.Components.tbWebBrowser" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B01BA3CA-7E8E-3C6A-80E8-A58E962D6BCB}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\InprocServer32\*******" Name="Class" Value="ComponentAce.Compression.Libs.zlib.ZStreamException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\InprocServer32" Name="Class" Value="ComponentAce.Compression.Libs.zlib.ZStreamException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B81BBD40-68A8-3BD9-8BC1-5A395AE17E03}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ImportPhotoPackageInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\InprocServer32" Name="Class" Value="iTong.CoreModule.ImportPhotoPackageInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BA500650-FE75-3910-99D9-75DEA659ACAD}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.IMHtmlNode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\InprocServer32" Name="Class" Value="iTong.CoreModule.IMHtmlNode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{BD5FDD49-2A60-3292-97E6-6BD6771FD346}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.SocialUserInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\InprocServer32" Name="Class" Value="iTong.CoreModule.SocialUserInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C180736A-E1D2-3885-9A90-6EEC512B6258}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WechatHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\InprocServer32" Name="Class" Value="iTong.CoreModule.WechatHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4BC082A-7CBE-3AB6-B980-76B080C599D3}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WeChatFriendInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\InprocServer32" Name="Class" Value="iTong.CoreModule.WeChatFriendInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C8204D51-FF87-30C2-94E1-476D9796299A}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\InprocServer32\*******" Name="Class" Value="ComponentAce.Compression.Libs.zlib.ZStream" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\InprocServer32" Name="Class" Value="ComponentAce.Compression.Libs.zlib.ZStream" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CB0957A6-2828-318E-BF7B-364B31D30C3C}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ApplePackageReader" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\InprocServer32" Name="Class" Value="iTong.CoreModule.ApplePackageReader" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{CE528D37-8251-3E6A-BCC2-0108DBF7FD14}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.BackupPwdArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\InprocServer32" Name="Class" Value="iTong.CoreModule.BackupPwdArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DBD7540D-CA39-35DF-84CD-E6605DEFE6F8}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ScreenshotSetting" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\InprocServer32" Name="Class" Value="iTong.CoreModule.ScreenshotSetting" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DCC04FE8-2A17-3229-B9A9-BD4394DCDE75}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.VIPGuideArg" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\InprocServer32" Name="Class" Value="iTong.CoreModule.VIPGuideArg" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DDEC58C4-4A55-355D-AF65-5C89B9CAD40E}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WhatsAppMedia" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\InprocServer32" Name="Class" Value="iTong.CoreModule.WhatsAppMedia" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DEA5CA6E-4D21-3283-B2CD-36C5C100ED14}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.PluginLogin" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\InprocServer32" Name="Class" Value="iTong.CoreModule.PluginLogin" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E0644D31-68C2-3124-B042-07B482133BDE}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.FavouriteInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\InprocServer32" Name="Class" Value="iTong.CoreModule.FavouriteInfo" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E6079FD1-F2CF-38BA-A612-314E0B63D6EE}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ActionCollectHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\InprocServer32" Name="Class" Value="iTong.CoreModule.ActionCollectHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E68941CB-4938-3A54-9B67-D1FF9477094C}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.IMLoadPara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\InprocServer32" Name="Class" Value="iTong.CoreModule.IMLoadPara" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E874DF5B-6AFC-3A30-BBA4-F9262C794B07}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\InprocServer32\*******" Name="Class" Value="iTong.Components.tbWebBrowserEx" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\InprocServer32" Name="Class" Value="iTong.Components.tbWebBrowserEx" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ED592A94-668D-3C53-A75A-906AFBF92F0A}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\InprocServer32\*******" Name="Class" Value="ComponentAce.Compression.Libs.zlib.zlibConst" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\InprocServer32" Name="Class" Value="ComponentAce.Compression.Libs.zlib.zlibConst" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F0E88EAF-967A-39A4-AFA1-6004B78BE30E}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.BackupArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\InprocServer32" Name="Class" Value="iTong.CoreModule.BackupArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-1E7B-334F-BBAF-914E113ACB97}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.AloneBackupsHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\InprocServer32" Name="Class" Value="iTong.CoreModule.AloneBackupsHelper" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{********-6E90-36E3-8644-944482ED7671}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.HelperExeManager" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\InprocServer32" Name="Class" Value="iTong.CoreModule.HelperExeManager" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6843B84-E2D7-38C5-B7B8-1A8BB8956A57}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.WeChatFriendModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\InprocServer32" Name="Class" Value="iTong.CoreModule.WeChatFriendModel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F9904FFF-7BC1-3721-8980-7464ABF27211}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.SociaChatBase" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\InprocServer32" Name="Class" Value="iTong.CoreModule.SociaChatBase" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FD986FE8-D7C3-3A3C-969B-BA81617B928C}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\InprocServer32\*******" Name="Class" Value="iTong.CoreModule.ShowAppDetailArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\InprocServer32\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\InprocServer32" Name="Class" Value="iTong.CoreModule.ShowAppDetailArgs" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\InprocServer32" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{FEDCBB08-14E8-3B9D-894B-85EE7C5B7E3B}\InprocServer32" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{04ED3517-998A-3F6E-89A5-4367B7273BC7}\*******" Name="Class" Value="iTong.CoreModule.DownloadCenterSource" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{04ED3517-998A-3F6E-89A5-4367B7273BC7}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{04ED3517-998A-3F6E-89A5-4367B7273BC7}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{04ED3517-998A-3F6E-89A5-4367B7273BC7}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{053CE802-84F4-3829-8C24-F69B4A116028}\*******" Name="Class" Value="iTong.CoreModule.ActiveStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{053CE802-84F4-3829-8C24-F69B4A116028}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{053CE802-84F4-3829-8C24-F69B4A116028}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{053CE802-84F4-3829-8C24-F69B4A116028}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{087CFA61-921F-3C9C-800F-6A64DDBCA095}\*******" Name="Class" Value="iTong.CoreModule.WebOperation" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{087CFA61-921F-3C9C-800F-6A64DDBCA095}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{087CFA61-921F-3C9C-800F-6A64DDBCA095}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{087CFA61-921F-3C9C-800F-6A64DDBCA095}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{08CC6B89-6633-3BF8-BAE3-32E5611CB9FA}\*******" Name="Class" Value="iTong.CoreModule.ImportType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{08CC6B89-6633-3BF8-BAE3-32E5611CB9FA}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{08CC6B89-6633-3BF8-BAE3-32E5611CB9FA}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{08CC6B89-6633-3BF8-BAE3-32E5611CB9FA}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0D2F570D-6A8D-36AF-A341-10978563BBCF}\*******" Name="Class" Value="iTong.CoreModule.SortType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0D2F570D-6A8D-36AF-A341-10978563BBCF}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0D2F570D-6A8D-36AF-A341-10978563BBCF}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0D2F570D-6A8D-36AF-A341-10978563BBCF}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0EA37BC6-2652-3CB3-A715-F7C5389529EF}\*******" Name="Class" Value="iTong.CoreModule.ViewStyle" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0EA37BC6-2652-3CB3-A715-F7C5389529EF}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0EA37BC6-2652-3CB3-A715-F7C5389529EF}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0EA37BC6-2652-3CB3-A715-F7C5389529EF}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0FA53361-42FB-35F5-BC05-DD348B3C6698}\*******" Name="Class" Value="iTong.CoreModule.WallpaperType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0FA53361-42FB-35F5-BC05-DD348B3C6698}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0FA53361-42FB-35F5-BC05-DD348B3C6698}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0FA53361-42FB-35F5-BC05-DD348B3C6698}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{14315EB3-C25B-342D-8C73-63CA860BC436}\*******" Name="Class" Value="iTong.CoreModule.IMState" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{14315EB3-C25B-342D-8C73-63CA860BC436}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{14315EB3-C25B-342D-8C73-63CA860BC436}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{14315EB3-C25B-342D-8C73-63CA860BC436}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1730291D-CCEA-37BC-B1A6-D9981BBCEF28}\*******" Name="Class" Value="iTong.CoreModule.AloneBackupType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1730291D-CCEA-37BC-B1A6-D9981BBCEF28}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1730291D-CCEA-37BC-B1A6-D9981BBCEF28}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1730291D-CCEA-37BC-B1A6-D9981BBCEF28}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{19D498CF-E01B-375E-9F9B-1C1CD93CB832}\*******" Name="Class" Value="iTong.CoreModule.ProgressStyle" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{19D498CF-E01B-375E-9F9B-1C1CD93CB832}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{19D498CF-E01B-375E-9F9B-1C1CD93CB832}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{19D498CF-E01B-375E-9F9B-1C1CD93CB832}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1A1105EA-81F5-3B15-9ED6-FB45860F1BF7}\*******" Name="Class" Value="iTong.CoreModule.DownloadView" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1A1105EA-81F5-3B15-9ED6-FB45860F1BF7}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1A1105EA-81F5-3B15-9ED6-FB45860F1BF7}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1A1105EA-81F5-3B15-9ED6-FB45860F1BF7}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1F5BFD66-CCAF-3D3F-88FA-3D2B093DF83D}\*******" Name="Class" Value="iTong.CoreModule.BackupRestore" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1F5BFD66-CCAF-3D3F-88FA-3D2B093DF83D}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1F5BFD66-CCAF-3D3F-88FA-3D2B093DF83D}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{1F5BFD66-CCAF-3D3F-88FA-3D2B093DF83D}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{26B15409-362F-32D6-9997-79DCD623D4C3}\*******" Name="Class" Value="iTong.CoreModule.FileOperate" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{26B15409-362F-32D6-9997-79DCD623D4C3}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{26B15409-362F-32D6-9997-79DCD623D4C3}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{26B15409-362F-32D6-9997-79DCD623D4C3}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2756E93A-AE41-33ED-8710-861924F4ABB5}\*******" Name="Class" Value="iTong.CoreModule.BackupRestoreErrorType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2756E93A-AE41-33ED-8710-861924F4ABB5}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2756E93A-AE41-33ED-8710-861924F4ABB5}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2756E93A-AE41-33ED-8710-861924F4ABB5}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2A67FCEF-C776-37CC-85CA-E6B8292F139A}\*******" Name="Class" Value="iTong.CoreModule.MediaUploadType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2A67FCEF-C776-37CC-85CA-E6B8292F139A}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2A67FCEF-C776-37CC-85CA-E6B8292F139A}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2A67FCEF-C776-37CC-85CA-E6B8292F139A}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2C80B312-95A0-37E1-88CD-41E89BC0CB31}\*******" Name="Class" Value="iTong.CoreModule.Backup2AloneBackupStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2C80B312-95A0-37E1-88CD-41E89BC0CB31}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2C80B312-95A0-37E1-88CD-41E89BC0CB31}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2C80B312-95A0-37E1-88CD-41E89BC0CB31}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{39ACFC8F-E613-35B7-AE09-01B5767FD5A7}\*******" Name="Class" Value="iTong.CoreModule.WebPluginStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{39ACFC8F-E613-35B7-AE09-01B5767FD5A7}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{39ACFC8F-E613-35B7-AE09-01B5767FD5A7}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{39ACFC8F-E613-35B7-AE09-01B5767FD5A7}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{3DA8773A-8326-3010-89DA-1FA0B7A1612C}\*******" Name="Class" Value="iTong.Components.tbAction" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{3DA8773A-8326-3010-89DA-1FA0B7A1612C}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{3DA8773A-8326-3010-89DA-1FA0B7A1612C}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{3DA8773A-8326-3010-89DA-1FA0B7A1612C}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4259D399-C7FF-305C-8A0E-46DC5AB3B596}\*******" Name="Class" Value="iTong.CoreModule.WeixinMainViewStyle" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4259D399-C7FF-305C-8A0E-46DC5AB3B596}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4259D399-C7FF-305C-8A0E-46DC5AB3B596}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4259D399-C7FF-305C-8A0E-46DC5AB3B596}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{45BEBDDD-BD20-38E3-861A-1840B46D3801}\*******" Name="Class" Value="iTong.CoreModule.WhatsAppSessionType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{45BEBDDD-BD20-38E3-861A-1840B46D3801}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{45BEBDDD-BD20-38E3-861A-1840B46D3801}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{45BEBDDD-BD20-38E3-861A-1840B46D3801}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4908C782-37C8-3CB9-A889-9225AF881320}\*******" Name="Class" Value="iTong.CoreModule.DRAppName" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4908C782-37C8-3CB9-A889-9225AF881320}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4908C782-37C8-3CB9-A889-9225AF881320}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4908C782-37C8-3CB9-A889-9225AF881320}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4957E115-E363-36ED-8395-71FD7C25EE9A}\*******" Name="Class" Value="iTong.CoreModule.MediaKey" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4957E115-E363-36ED-8395-71FD7C25EE9A}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4957E115-E363-36ED-8395-71FD7C25EE9A}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4957E115-E363-36ED-8395-71FD7C25EE9A}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4AC28083-4C45-3ED0-BD92-43106295BFF2}\*******" Name="Class" Value="iTong.CoreModule.WeixinState" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4AC28083-4C45-3ED0-BD92-43106295BFF2}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4AC28083-4C45-3ED0-BD92-43106295BFF2}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{4AC28083-4C45-3ED0-BD92-43106295BFF2}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5527B01E-7B89-3996-806C-40FDF259B240}\*******" Name="Class" Value="iTong.CoreModule.WarrantyType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5527B01E-7B89-3996-806C-40FDF259B240}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5527B01E-7B89-3996-806C-40FDF259B240}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5527B01E-7B89-3996-806C-40FDF259B240}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{567AC3AE-D2C3-3EFA-BD16-D59F18F12BDB}\*******" Name="Class" Value="iTong.CoreModule.WhatsAppDeleteType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{567AC3AE-D2C3-3EFA-BD16-D59F18F12BDB}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{567AC3AE-D2C3-3EFA-BD16-D59F18F12BDB}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{567AC3AE-D2C3-3EFA-BD16-D59F18F12BDB}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{56DFD75C-26DD-3B13-971F-AE9E72CDCB1B}\*******" Name="Class" Value="iTong.CoreModule.FindDataStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{56DFD75C-26DD-3B13-971F-AE9E72CDCB1B}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{56DFD75C-26DD-3B13-971F-AE9E72CDCB1B}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{56DFD75C-26DD-3B13-971F-AE9E72CDCB1B}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5D1A390E-597E-39AC-BA04-1E073CFF903C}\*******" Name="Class" Value="iTong.CoreModule.LoadAloneBackupType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5D1A390E-597E-39AC-BA04-1E073CFF903C}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5D1A390E-597E-39AC-BA04-1E073CFF903C}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5D1A390E-597E-39AC-BA04-1E073CFF903C}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{618B877F-CE73-3AB3-91AA-BC925F656D2F}\*******" Name="Class" Value="iTong.CoreModule.FindDataType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{618B877F-CE73-3AB3-91AA-BC925F656D2F}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{618B877F-CE73-3AB3-91AA-BC925F656D2F}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{618B877F-CE73-3AB3-91AA-BC925F656D2F}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{66B2AB9B-7DB8-3E39-A734-4EFADDA6DF37}\*******" Name="Class" Value="iTong.CoreModule.ScreenSkuState" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{66B2AB9B-7DB8-3E39-A734-4EFADDA6DF37}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{66B2AB9B-7DB8-3E39-A734-4EFADDA6DF37}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{66B2AB9B-7DB8-3E39-A734-4EFADDA6DF37}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{66F4C93E-2BFD-323A-AFCA-7DFF3472EA48}\*******" Name="Class" Value="iTong.CoreModule.MediaItem" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{66F4C93E-2BFD-323A-AFCA-7DFF3472EA48}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{66F4C93E-2BFD-323A-AFCA-7DFF3472EA48}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{66F4C93E-2BFD-323A-AFCA-7DFF3472EA48}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6D4681C0-5B1F-31A9-9BCC-B773C83A054D}\*******" Name="Class" Value="iTong.CoreModule.QQState" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6D4681C0-5B1F-31A9-9BCC-B773C83A054D}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6D4681C0-5B1F-31A9-9BCC-B773C83A054D}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6D4681C0-5B1F-31A9-9BCC-B773C83A054D}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6E5A527D-686B-33E5-806A-7480704829B4}\*******" Name="Class" Value="iTong.Components.ApplePackageReaderSchedule" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6E5A527D-686B-33E5-806A-7480704829B4}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6E5A527D-686B-33E5-806A-7480704829B4}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6E5A527D-686B-33E5-806A-7480704829B4}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6E7013E9-2D9F-373A-B6A8-0E881DDFBE38}\*******" Name="Class" Value="iTong.CoreModule.BackupStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6E7013E9-2D9F-373A-B6A8-0E881DDFBE38}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6E7013E9-2D9F-373A-B6A8-0E881DDFBE38}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6E7013E9-2D9F-373A-B6A8-0E881DDFBE38}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{771213EC-5E70-3D6F-99E2-658E66CE5391}\*******" Name="Class" Value="iTong.CoreModule.PackageSource" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{771213EC-5E70-3D6F-99E2-658E66CE5391}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{771213EC-5E70-3D6F-99E2-658E66CE5391}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{771213EC-5E70-3D6F-99E2-658E66CE5391}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{873F457A-F444-3BCA-8D67-DFF2B4A069C7}\*******" Name="Class" Value="iTong.CoreModule.IMHtmlNodeType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{873F457A-F444-3BCA-8D67-DFF2B4A069C7}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{873F457A-F444-3BCA-8D67-DFF2B4A069C7}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{873F457A-F444-3BCA-8D67-DFF2B4A069C7}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{88DB2111-50D7-3FF8-88E4-4C9955628347}\*******" Name="Class" Value="iTong.CoreModule.IMParaType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{88DB2111-50D7-3FF8-88E4-4C9955628347}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{88DB2111-50D7-3FF8-88E4-4C9955628347}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{88DB2111-50D7-3FF8-88E4-4C9955628347}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{89B3FA40-0F56-3E69-A667-6893D3BFDE74}\*******" Name="Class" Value="iTong.CoreModule.AppIconStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{89B3FA40-0F56-3E69-A667-6893D3BFDE74}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{89B3FA40-0F56-3E69-A667-6893D3BFDE74}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{89B3FA40-0F56-3E69-A667-6893D3BFDE74}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{8DB619DE-B296-39DA-BF46-74F4CC74065E}\*******" Name="Class" Value="iTong.CoreModule.ProgressAction" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{8DB619DE-B296-39DA-BF46-74F4CC74065E}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{8DB619DE-B296-39DA-BF46-74F4CC74065E}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{8DB619DE-B296-39DA-BF46-74F4CC74065E}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{902ADCC9-6BE7-3F12-8F72-00C70670F489}\*******" Name="Class" Value="iTong.CoreModule.ExportType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{902ADCC9-6BE7-3F12-8F72-00C70670F489}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{902ADCC9-6BE7-3F12-8F72-00C70670F489}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{902ADCC9-6BE7-3F12-8F72-00C70670F489}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{9270D5FF-3D64-3589-9B57-359FED933112}\*******" Name="Class" Value="iTong.CoreModule.SociaChatType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{9270D5FF-3D64-3589-9B57-359FED933112}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{9270D5FF-3D64-3589-9B57-359FED933112}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{9270D5FF-3D64-3589-9B57-359FED933112}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{929F7114-6092-311D-99AF-99DA3C27BCE9}\*******" Name="Class" Value="iTong.CoreModule.FileFormType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{929F7114-6092-311D-99AF-99DA3C27BCE9}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{929F7114-6092-311D-99AF-99DA3C27BCE9}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{929F7114-6092-311D-99AF-99DA3C27BCE9}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{93D8E7F3-4FC3-330D-9828-C378EE6A175A}\*******" Name="Class" Value="iTong.CoreModule.ActiveCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{93D8E7F3-4FC3-330D-9828-C378EE6A175A}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{93D8E7F3-4FC3-330D-9828-C378EE6A175A}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{93D8E7F3-4FC3-330D-9828-C378EE6A175A}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{AB1235C8-B6CC-3556-8839-97BB7A65545A}\*******" Name="Class" Value="iTong.CoreModule.ChargeProjectType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{AB1235C8-B6CC-3556-8839-97BB7A65545A}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{AB1235C8-B6CC-3556-8839-97BB7A65545A}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{AB1235C8-B6CC-3556-8839-97BB7A65545A}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{AF7D0446-67A2-32C1-A141-77F1B5D3B019}\*******" Name="Class" Value="iTong.CoreModule.SocialFriendType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{AF7D0446-67A2-32C1-A141-77F1B5D3B019}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{AF7D0446-67A2-32C1-A141-77F1B5D3B019}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{AF7D0446-67A2-32C1-A141-77F1B5D3B019}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B37E66C3-2919-3CEB-B703-E272D6BB1088}\*******" Name="Class" Value="iTong.CoreModule.FindDataStyle" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B37E66C3-2919-3CEB-B703-E272D6BB1088}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B37E66C3-2919-3CEB-B703-E272D6BB1088}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B37E66C3-2919-3CEB-B703-E272D6BB1088}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B6C49177-40E0-39DD-A06C-3D76B93A9E03}\*******" Name="Class" Value="iTong.CoreModule.TreeNodeType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B6C49177-40E0-39DD-A06C-3D76B93A9E03}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B6C49177-40E0-39DD-A06C-3D76B93A9E03}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B6C49177-40E0-39DD-A06C-3D76B93A9E03}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BBCF1135-3C5E-311E-B74A-32AED929509E}\*******" Name="Class" Value="iTong.CoreModule.DecipherWechatEnums" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BBCF1135-3C5E-311E-B74A-32AED929509E}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BBCF1135-3C5E-311E-B74A-32AED929509E}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BBCF1135-3C5E-311E-B74A-32AED929509E}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BDB4AF6C-9C71-3124-A1FB-7CFECCF70457}\*******" Name="Class" Value="iTong.CoreModule.PopMsgDeviceType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BDB4AF6C-9C71-3124-A1FB-7CFECCF70457}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BDB4AF6C-9C71-3124-A1FB-7CFECCF70457}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BDB4AF6C-9C71-3124-A1FB-7CFECCF70457}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{C0CDED1B-6F7D-34F6-B21C-F14AA9352444}\*******" Name="Class" Value="iTong.CoreModule.IMAppType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{C0CDED1B-6F7D-34F6-B21C-F14AA9352444}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{C0CDED1B-6F7D-34F6-B21C-F14AA9352444}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{C0CDED1B-6F7D-34F6-B21C-F14AA9352444}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CABF11E4-8358-300C-B02A-719FACA4D830}\*******" Name="Class" Value="iTong.CoreModule.EmotionOp" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CABF11E4-8358-300C-B02A-719FACA4D830}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CABF11E4-8358-300C-B02A-719FACA4D830}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CABF11E4-8358-300C-B02A-719FACA4D830}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CDDA047D-E617-3453-B112-0613EBD073E1}\*******" Name="Class" Value="iTong.CoreModule.EmotionImportSetp" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CDDA047D-E617-3453-B112-0613EBD073E1}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CDDA047D-E617-3453-B112-0613EBD073E1}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CDDA047D-E617-3453-B112-0613EBD073E1}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CF1159F4-8B75-33AF-A083-0A065C181A09}\*******" Name="Class" Value="iTong.CoreModule.LoginMode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CF1159F4-8B75-33AF-A083-0A065C181A09}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CF1159F4-8B75-33AF-A083-0A065C181A09}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CF1159F4-8B75-33AF-A083-0A065C181A09}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CF3783F0-E69D-36EF-9719-D3542683C502}\*******" Name="Class" Value="iTong.CoreModule.WhatsAppGroupEventType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CF3783F0-E69D-36EF-9719-D3542683C502}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CF3783F0-E69D-36EF-9719-D3542683C502}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CF3783F0-E69D-36EF-9719-D3542683C502}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{D9CF1214-7247-31BB-A421-CB4187423571}\*******" Name="Class" Value="iTong.CoreModule.ImportStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{D9CF1214-7247-31BB-A421-CB4187423571}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{D9CF1214-7247-31BB-A421-CB4187423571}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{D9CF1214-7247-31BB-A421-CB4187423571}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E0EF2FE4-A7B6-3C3D-B689-CA503896B4EB}\*******" Name="Class" Value="iTong.CoreModule.DownloadStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E0EF2FE4-A7B6-3C3D-B689-CA503896B4EB}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E0EF2FE4-A7B6-3C3D-B689-CA503896B4EB}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E0EF2FE4-A7B6-3C3D-B689-CA503896B4EB}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E4179265-64E7-37C0-8D0C-EB777489D8D3}\*******" Name="Class" Value="iTong.CoreModule.OperationStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E4179265-64E7-37C0-8D0C-EB777489D8D3}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E4179265-64E7-37C0-8D0C-EB777489D8D3}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E4179265-64E7-37C0-8D0C-EB777489D8D3}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E6AD402C-BABE-3200-B431-90BC284DF742}\*******" Name="Class" Value="iTong.CoreModule.ShowFriendType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E6AD402C-BABE-3200-B431-90BC284DF742}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E6AD402C-BABE-3200-B431-90BC284DF742}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E6AD402C-BABE-3200-B431-90BC284DF742}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E7CEA431-F546-3076-AD7F-5DF62B1BF66F}\*******" Name="Class" Value="iTong.CoreModule.WebSiteType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E7CEA431-F546-3076-AD7F-5DF62B1BF66F}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E7CEA431-F546-3076-AD7F-5DF62B1BF66F}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E7CEA431-F546-3076-AD7F-5DF62B1BF66F}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{F689B509-1A2E-3044-AB17-A772CBB45E4D}\*******" Name="Class" Value="iTong.CoreModule.ExportStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{F689B509-1A2E-3044-AB17-A772CBB45E4D}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{F689B509-1A2E-3044-AB17-A772CBB45E4D}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{F689B509-1A2E-3044-AB17-A772CBB45E4D}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FCC3669A-DD4D-3257-A111-DE9EE1DAAE63}\*******" Name="Class" Value="iTong.CoreModule.UninstallState" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FCC3669A-DD4D-3257-A111-DE9EE1DAAE63}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FCC3669A-DD4D-3257-A111-DE9EE1DAAE63}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FCC3669A-DD4D-3257-A111-DE9EE1DAAE63}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FE0FD5EC-638A-3DBA-B73F-571234857A31}\*******" Name="Class" Value="iTong.CoreModule.WeChatRecoveryStatus" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FE0FD5EC-638A-3DBA-B73F-571234857A31}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FE0FD5EC-638A-3DBA-B73F-571234857A31}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FE0FD5EC-638A-3DBA-B73F-571234857A31}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FF29BEF6-2F41-3A35-903F-91A9567E5EB3}\*******" Name="Class" Value="iTong.CoreModule.ChatMsgType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FF29BEF6-2F41-3A35-903F-91A9567E5EB3}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FF29BEF6-2F41-3A35-903F-91A9567E5EB3}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FF29BEF6-2F41-3A35-903F-91A9567E5EB3}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FF5B15DE-CC7C-3825-BF4E-464AE5B35318}\*******" Name="Class" Value="iTong.CoreModule.SearchDataType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FF5B15DE-CC7C-3825-BF4E-464AE5B35318}\*******" Name="Assembly" Value="Android, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FF5B15DE-CC7C-3825-BF4E-464AE5B35318}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FF5B15DE-CC7C-3825-BF4E-464AE5B35318}\*******" Name="CodeBase" Value="file:///[#filE85085A783D0212A091B997F7994D835]" Type="string" Action="write" />
            </Component>
            <Component Id="cmpF22D40156F8E16FBFA89C752FF9D0EF9" Directory="INSTALLFOLDER" Guid="C02DE333-B18E-4EAE-B255-1D6F69BF738C">
                <File Id="fil2DACDED111D2125FD9BAF52808E5CEB0" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-console-l1-1-0.dll" />
            </Component>
            <Component Id="cmpD571DFC958168320FBD2D9FC886F151C" Directory="INSTALLFOLDER" Guid="AE585F07-C27A-45B7-AD55-B3C7BB349EF7">
                <File Id="fil17B6D4224AEC1885E80DC07CC51923FB" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-console-l1-2-0.dll" />
            </Component>
            <Component Id="cmp742C123932C1A216E185167E7780B396" Directory="INSTALLFOLDER" Guid="28C73192-4394-40B8-9ECE-710B4A5B64CE">
                <File Id="filD4F5DAB6FC380871476D90A9B278B655" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-datetime-l1-1-0.dll" />
            </Component>
            <Component Id="cmp0C2E6E9A00C3293AC27A114F5CD9CE2A" Directory="INSTALLFOLDER" Guid="7CC83809-084C-4207-AF6B-D4D3BAD166C7">
                <File Id="filC4BA033347D114D73E0BFDC4F138ED4B" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-debug-l1-1-0.dll" />
            </Component>
            <Component Id="cmp6EBAE5ACCACC27065A435A0042E7BAED" Directory="INSTALLFOLDER" Guid="66867FBD-F44C-43C1-9BF0-15FF54E7C596">
                <File Id="filBE3E8E1D40266192494C4DD3D256E4B0" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-errorhandling-l1-1-0.dll" />
            </Component>
            <Component Id="cmpF2FDCA6BFBFB7ADB2CFE86FA5F7ABF13" Directory="INSTALLFOLDER" Guid="CCC3C8F7-3FAB-4FC2-886F-5CFD2279D503">
                <File Id="filFA3B01EB2EF30BB2736A84B7279F1110" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-fibers-l1-1-0.dll" />
            </Component>
            <Component Id="cmp8654E1D8A29D75D1863BECEDD37AC0AE" Directory="INSTALLFOLDER" Guid="01ABD323-871B-4B87-BEE9-C863BC6BEB2A">
                <File Id="filDFD8964B5B00E12A86C400251EF6F77E" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-file-l1-1-0.dll" />
            </Component>
            <Component Id="cmp41E89377B0002CC2350CF2F309C7EF96" Directory="INSTALLFOLDER" Guid="02CE9C1D-F16C-445C-BCB4-45C29DD21D22">
                <File Id="filED19878F18A70E195BF8CDAA0D0E8890" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-file-l1-2-0.dll" />
            </Component>
            <Component Id="cmpDEE5CBC14AEA029E9F4401E5B3982607" Directory="INSTALLFOLDER" Guid="C6768BB0-1FD3-4D74-BBA0-CC7F5EFBCC1F">
                <File Id="fil99A16235893D27C698F1132A81059440" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-file-l2-1-0.dll" />
            </Component>
            <Component Id="cmp590BD4AE87E294D1563CF4D557250096" Directory="INSTALLFOLDER" Guid="F10E9C1F-D4B9-4EB4-9FDE-649AF89BDE14">
                <File Id="filD5657AEC19C9FC8E0AF60F470875CF64" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-handle-l1-1-0.dll" />
            </Component>
            <Component Id="cmpCF2CF8C7A0C59052CAA01587AA5785DC" Directory="INSTALLFOLDER" Guid="2A474613-65BD-4CE4-A6C9-64D2FAEF7EA2">
                <File Id="fil522696687E0EECCAA70A0E9D409F1AFF" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-heap-l1-1-0.dll" />
            </Component>
            <Component Id="cmpDD279108FA0E596011006D554F6A3104" Directory="INSTALLFOLDER" Guid="59FCA77A-BBEB-4B3E-8985-A509B11D727B">
                <File Id="fil87D340479265677CE6A63011B082D90C" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-interlocked-l1-1-0.dll" />
            </Component>
            <Component Id="cmpCD74B7BA0C179921851020BBB033014C" Directory="INSTALLFOLDER" Guid="656A403C-F47A-4015-9714-6FBAC9502A55">
                <File Id="fil2B1C02B60D25396B459A4FD75F2C3397" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-libraryloader-l1-1-0.dll" />
            </Component>
            <Component Id="cmpAC8BDC4BFF75C5529DB6BFA54526ED0C" Directory="INSTALLFOLDER" Guid="3FE49245-9F5E-4EAF-8CE6-764ACBE359D1">
                <File Id="fil166AA52FFB5EC305E92912AB1AD3E8BE" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-localization-l1-2-0.dll" />
            </Component>
            <Component Id="cmp8C8498D921E5B275BE6C7E3DA8DCEB68" Directory="INSTALLFOLDER" Guid="A4C47FC5-D861-49B1-B034-3C810BB4DB6E">
                <File Id="fil119FC388EE479D2497F4E5235A4D0A0D" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-memory-l1-1-0.dll" />
            </Component>
            <Component Id="cmp9AE39696CCE4B2BA21A7559EDA2F0D90" Directory="INSTALLFOLDER" Guid="EBA56C69-9C03-4FB5-8768-83A57BC687B1">
                <File Id="filAF1A85247BFD7049AEE00A37035FC723" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-namedpipe-l1-1-0.dll" />
            </Component>
            <Component Id="cmp5D145C20D5E90C9E54B87DDC549105EA" Directory="INSTALLFOLDER" Guid="46B00BC6-3C81-401D-95CB-22C9CB6E8E78">
                <File Id="filED53139C4E70E2ED3BF5C20A13EF04C9" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-processenvironment-l1-1-0.dll" />
            </Component>
            <Component Id="cmp4BC27243200E3663A58563BC8AD5A268" Directory="INSTALLFOLDER" Guid="475927E0-2D3A-4E81-9991-9557220684A7">
                <File Id="fil6C98F7250E35758DF2EAE6D18757B52A" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-processthreads-l1-1-0.dll" />
            </Component>
            <Component Id="cmp3C1343875198F364F70980382E32F197" Directory="INSTALLFOLDER" Guid="C101AFB7-E0C7-4466-9380-2CFC83BD19EB">
                <File Id="filDCFF49060A8650BC3CD76399B278EFB3" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-processthreads-l1-1-1.dll" />
            </Component>
            <Component Id="cmpECDFB8DD1F83358D411D63B1F980153E" Directory="INSTALLFOLDER" Guid="74FB7CEC-AAB1-4DF5-9150-889ADCE777B8">
                <File Id="fil473C62601D1A459892F1035DC46D5837" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-profile-l1-1-0.dll" />
            </Component>
            <Component Id="cmp1E66ED1BF30236B52829E44485A58E50" Directory="INSTALLFOLDER" Guid="C22F5327-2F68-46A6-94F4-4A16FBCC9932">
                <File Id="fil86B1657D34671048F554605B02204614" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-rtlsupport-l1-1-0.dll" />
            </Component>
            <Component Id="cmp43C0DEA19BA8D8C7F25F0DD08B502FBD" Directory="INSTALLFOLDER" Guid="1299BDA2-F48E-4321-864A-D9D2B11C148F">
                <File Id="filFAB349D875247DA20FBC6095A574364F" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-string-l1-1-0.dll" />
            </Component>
            <Component Id="cmpBE4AFE403859DE3CDCB4C17CE29AD9B1" Directory="INSTALLFOLDER" Guid="1810F61F-67BA-4F0C-8900-A3455CEF1745">
                <File Id="filE7772A0EC4C57BADD6BD4261E7E1D733" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-synch-l1-1-0.dll" />
            </Component>
            <Component Id="cmp6FB01AFC7883E57A54B9364FE007F69A" Directory="INSTALLFOLDER" Guid="6AA653DF-F5A8-4BC7-8BEE-DFEBC5C2A6A4">
                <File Id="filC694EB3BC04DCB9743E89D8C9FCA39F3" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-synch-l1-2-0.dll" />
            </Component>
            <Component Id="cmp34831A79EBBEDE365CF8E47BB8DA4AEE" Directory="INSTALLFOLDER" Guid="6CD428D8-0C53-4D2B-AE37-AF43CB2423F3">
                <File Id="fil156E0DB27CC93BDB6B5984208AF58408" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-sysinfo-l1-1-0.dll" />
            </Component>
            <Component Id="cmpC5B1C289707493DC1A9765CBF1619688" Directory="INSTALLFOLDER" Guid="D7BC336E-FD02-4683-AA24-1A5EA6B8FC6F">
                <File Id="filF7650F0EE0391337B997E96186D1FABA" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-timezone-l1-1-0.dll" />
            </Component>
            <Component Id="cmpBDCBA58D640E71BC077901A457EFAA06" Directory="INSTALLFOLDER" Guid="C125A403-37DB-4F4C-9B38-E6ADBD468655">
                <File Id="fil8D8B394F6F988ACC1E93C0D7A19BF8E9" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-core-util-l1-1-0.dll" />
            </Component>
            <Component Id="cmp5555B17514BB9BCBAF7845DEE241FB74" Directory="INSTALLFOLDER" Guid="E47E906C-6D98-49B4-883C-F0D2C5DDDFF4">
                <File Id="filDB552A98BB516D9B0F19F0AE4AAE1489" KeyPath="yes" Source="$(var.DirPath)\API-MS-Win-core-xstate-l2-1-0.dll" />
            </Component>
            <Component Id="cmp74F6E981444243E078C5E033BD078819" Directory="INSTALLFOLDER" Guid="A5F8BF34-003E-4298-84BF-F55420C9C3FC">
                <File Id="fil27C5261AF57F48008AECF60CB43570E2" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-conio-l1-1-0.dll" />
            </Component>
            <Component Id="cmp20DD0168324B65A2B89EB723FB32E7F3" Directory="INSTALLFOLDER" Guid="7FA43EB8-A966-4B40-B03D-9EC602CC6281">
                <File Id="fil857EC09AC99AFE55013ED23276EA7A26" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-convert-l1-1-0.dll" />
            </Component>
            <Component Id="cmpDD9A8434CB4A6317B4BA2A55410983F6" Directory="INSTALLFOLDER" Guid="D9C44117-B575-4AEB-8131-64DAD18A0D11">
                <File Id="fil05F92919DA01CD87C088B1B7CE3AF0D8" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-environment-l1-1-0.dll" />
            </Component>
            <Component Id="cmp11E56753706BD7FE87724F732A73F490" Directory="INSTALLFOLDER" Guid="CF360415-6B2A-4CE6-901E-CF1450A8EFA4">
                <File Id="fil7D01539A9AB967EA680352C503C6DC09" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-filesystem-l1-1-0.dll" />
            </Component>
            <Component Id="cmp8A47DE6FFCA0BA39168EFA51F2E3ECFF" Directory="INSTALLFOLDER" Guid="89B9700E-120B-4EE6-9ED0-2896DAD450BA">
                <File Id="fil27C551710BEF5EC4BC328135EEBEA135" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-heap-l1-1-0.dll" />
            </Component>
            <Component Id="cmp4B8C11AC4FCB7CB42D55737034265B39" Directory="INSTALLFOLDER" Guid="8EB21513-F2CE-49D2-AD5D-23606ECF78DF">
                <File Id="fil0B1F8BD818900489ED53DD592A535C47" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-locale-l1-1-0.dll" />
            </Component>
            <Component Id="cmp3E56BCDB6A50B4AA292F631457D3032D" Directory="INSTALLFOLDER" Guid="716FFDB6-034C-49F4-8CEB-796E56165134">
                <File Id="fil2CF8DDC41ED2CCC246838458C09CB0E7" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-math-l1-1-0.dll" />
            </Component>
            <Component Id="cmp995D66766A5CFBA5514D9EBED950459D" Directory="INSTALLFOLDER" Guid="A31D8CFE-7482-4B65-9BDD-85AB736DDE27">
                <File Id="fil8A07AAC4EC03297A8FE92F1C6005B6CE" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-multibyte-l1-1-0.dll" />
            </Component>
            <Component Id="cmp4823CF61E7C973B92C27FA84E4DB141A" Directory="INSTALLFOLDER" Guid="F8FC6B05-B52D-41D0-9536-0A7C231241BB">
                <File Id="fil3620C932251D7355C13ACBDAF2A84082" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-private-l1-1-0.dll" />
            </Component>
            <Component Id="cmp14FD53BFC562B74A90211F6A7FA45EF8" Directory="INSTALLFOLDER" Guid="AEDC0340-E354-48D6-8DE9-532698B57B89">
                <File Id="filFB0713470FD14BAAA182341CB19AF7EF" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-process-l1-1-0.dll" />
            </Component>
            <Component Id="cmp8C8DF6C3F20B60706F1C2D2317BA7664" Directory="INSTALLFOLDER" Guid="3B43C6EF-F676-4262-8600-A19EBBFDE568">
                <File Id="fil7C86F69C29F28D20530B0BCAF69E9FB9" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-runtime-l1-1-0.dll" />
            </Component>
            <Component Id="cmpFE2451868FE2A2D25EE3375499109233" Directory="INSTALLFOLDER" Guid="6D2863C7-8427-4656-9FC7-B7B6122EC380">
                <File Id="fil1CA9B8B8CFB836B270D48565EC302DA5" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-stdio-l1-1-0.dll" />
            </Component>
            <Component Id="cmpE0DBCBD210EEB84B2FE546162F07C376" Directory="INSTALLFOLDER" Guid="030B0BEF-59B3-4076-A2EA-D57436DC0512">
                <File Id="fil756422CB95AC98A122B628535E348CFD" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-string-l1-1-0.dll" />
            </Component>
            <Component Id="cmpD01CA70C06D9ADCC4F49058182448367" Directory="INSTALLFOLDER" Guid="7DE967C4-0D11-450C-8C93-13D6B1132AB4">
                <File Id="filBA529A8BD882A2CAE02E14EEA7B22ADF" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-time-l1-1-0.dll" />
            </Component>
            <Component Id="cmp21CF0447C915302F93234E31801A67A7" Directory="INSTALLFOLDER" Guid="1A4D4940-0D82-4AEC-8BF4-4726FB711BB7">
                <File Id="fil76FEC3CFC3B4E7E1F7410458BFA45411" KeyPath="yes" Source="$(var.DirPath)\api-ms-win-crt-utility-l1-1-0.dll" />
            </Component>
            <Component Id="cmp9038FC48D8AF00E7C1A4108D81101E13" Directory="INSTALLFOLDER" Guid="55C0BB3C-EA03-492A-81DE-50D573D0BD4E">
                <File Id="filD433E0B105E9EB2618B9F3F2BE1F62FF" KeyPath="yes" Source="$(var.DirPath)\app.manifest" />
            </Component>
            <Component Id="cmpA47AE9A92F4464B8457CADC4DC4D2356" Directory="INSTALLFOLDER" Guid="5D4E7457-89A3-414C-9E27-2485B4F50320">
                <File Id="fil0F1E34305BF0EDD06901D790AE4188FE" KeyPath="yes" Source="$(var.DirPath)\AppUnion.dll" />
            </Component>
            <Component Id="cmpA239A12BCD1CB2E08B3D5953B6EE73CB" Directory="INSTALLFOLDER" Guid="EE2B8AE2-6E50-44DC-976C-2CC6365DA7D2">
                <File Id="fil4BED955AF231156A466CF427D2D8701F" KeyPath="yes" Source="$(var.DirPath)\A_Biz_Clear.bat" />
            </Component>
            <Component Id="cmpD919FEBBA147A91AFAA6E6A593FD7835" Directory="INSTALLFOLDER" Guid="88AA5864-6415-4270-9A5C-257D2708ACC6">
                <File Id="fil2B0E8168B2BE071A20896C7D12DE0087" KeyPath="yes" Source="$(var.DirPath)\A_Biz_ClearService.bat" />
            </Component>
            <Component Id="cmpEC8813147359146D2E375E2A23B16DF6" Directory="INSTALLFOLDER" Guid="AD5DB221-7C26-4D68-9385-551BED379DDF">
                <File Id="filDFAEC3442DD8E0EBE3E941DED5DA4AAB" KeyPath="yes" Source="$(var.DirPath)\A_Biz_RunSafeMode.bat" />
            </Component>
            <Component Id="cmpC249F356E3B392318B0AF8CB0DE35A77" Directory="INSTALLFOLDER" Guid="B64E5846-2EC6-457B-B9C6-D0EA215A51EA">
                <File Id="fil13E41C75A8339257AA978D54EA6010B9" KeyPath="yes" Source="$(var.DirPath)\A_Biz_RunScreen.bat" />
            </Component>
            <Component Id="cmp80A69781C3B72DB4F2637627D68E5EA5" Directory="INSTALLFOLDER" Guid="E8FC22CF-A1BB-4746-B624-22EC636BE45B">
                <File Id="fil56E51615245333BEBC77460E48C9C114" KeyPath="yes" Source="$(var.DirPath)\A_Biz_RunServiceDebug.bat" />
            </Component>
            <Component Id="cmpC090F34637A35E9B22918A32E742841A" Directory="INSTALLFOLDER" Guid="8C39C2FC-1069-4281-ABE9-8DD3B5873AB1">
                <File Id="filA722403AA4D80B5CCF3A9AEC725B8867" KeyPath="yes" Source="$(var.DirPath)\A_Biz_RunWindows.bat" />
            </Component>
            <Component Id="cmp8E42499507806BE90CD89565E569B698" Directory="INSTALLFOLDER" Guid="EF0D5467-F50D-4C39-B94C-E8617ED99772">
                <File Id="filD4767BC5F98137E2CBBA55BD8165CC57" KeyPath="yes" Source="$(var.DirPath)\A_Biz_UninstallDriver.bat" />
            </Component>
            <Component Id="cmp79286F884DB1708DC01E1AF310348896" Directory="INSTALLFOLDER" Guid="02416B94-03F3-4BE7-B353-************">
                <File Id="fil2765FF17FEE64E748F212D52F4BAE101" KeyPath="yes" Source="$(var.DirPath)\CefSharp.BrowserSubprocess.Core.dll" />
            </Component>
            <Component Id="cmp1CDA8B622AD34DED228816495B1F54D1" Directory="INSTALLFOLDER" Guid="6426AA31-02DB-44CB-B211-93063D29E09E">
                <File Id="filD414186516649EBD72D449960A85B6EA" KeyPath="yes" Source="$(var.DirPath)\CefSharp.BrowserSubprocess.exe" />
            </Component>
            <Component Id="cmpD56C9C8669FB90C94EC360E6FCAEC11D" Directory="INSTALLFOLDER" Guid="A5FB11E5-F436-4379-80C9-64F8A53D6C00">
                <File Id="filC533CA4596859F7AE42053FF6C380599" KeyPath="yes" Source="$(var.DirPath)\CefSharp.Core.dll" />
            </Component>
            <Component Id="cmp0AA7D28FF846E8E057B45051E803366F" Directory="INSTALLFOLDER" Guid="6CF2CD15-45CC-4CDF-868B-FBF17C591DAD">
                <File Id="fil2DF8D65E435B4692648D2ABE950C0FA6" KeyPath="yes" Source="$(var.DirPath)\CefSharp.Core.Runtime.dll" />
            </Component>
            <Component Id="cmp0C3CB3D99D82ADCC9DE80403F67B081E" Directory="INSTALLFOLDER" Guid="2CD3452F-C385-4D59-B6FA-D019BC91FE69">
                <File Id="filFD46F57654D5F344C5A9770B8BA2C3C5" KeyPath="yes" Source="$(var.DirPath)\CefSharp.dll" />
            </Component>
            <Component Id="cmp5B56CAB04DFF4B644F50A037029E7857" Directory="INSTALLFOLDER" Guid="058CE808-B913-4F43-A3C8-AF5BCAB294FF">
                <File Id="filAC5682C884D6A91376C05D0FF26962CE" KeyPath="yes" Source="$(var.DirPath)\CefSharp.WinForms.dll" />
            </Component>
            <Component Id="cmpDE54FA1AB52CD37D20D576D2553D9BF6" Directory="INSTALLFOLDER" Guid="41A40946-5667-4899-8B85-A4EA98CD5F40">
                <File Id="fil2FA47E4D8E152EEF8C2F7278A2AECD50" KeyPath="yes" Source="$(var.DirPath)\chrome_100_percent.pak" />
            </Component>
            <Component Id="cmp5074158BA4EC6AA5F044849AE7921837" Directory="INSTALLFOLDER" Guid="AA42EDCF-2C58-49EA-8230-E599F719962D">
                <File Id="fil6D8919F854A902795DEC281D6B0FA071" KeyPath="yes" Source="$(var.DirPath)\chrome_200_percent.pak" />
            </Component>
            <Component Id="cmpE22DA677162A8EC71F9715EF76272D57" Directory="INSTALLFOLDER" Guid="16DA4F10-A0D0-4CBF-AE09-E36633CDA814">
                <File Id="fil386E4B08ECCD30ABCD6DB37ED139B277" KeyPath="yes" Source="$(var.DirPath)\chrome_elf.dll" />
            </Component>
            <Component Id="cmpE4363908C50AA4558F6AA7673DE8CD3F" Directory="INSTALLFOLDER" Guid="A9D15EC9-BA1A-4663-9ABE-FF5DCCA9A7A7">
                <File Id="filAF3B097E1D957160F42CEE1066C4881B" KeyPath="yes" Source="$(var.DirPath)\concrt140.dll" />
            </Component>
            <Component Id="cmp788284CD0DF0E9934125983EA4722C38" Directory="INSTALLFOLDER" Guid="9CC3E47A-EEBF-4A0A-A5E6-29537888BC3D">
                <File Id="fil6427272836E552B33E3D8C76166F5BD0" KeyPath="yes" Source="$(var.DirPath)\CoreCefSharp.dll" />
            </Component>
            <Component Id="cmp41DC9741013CA4AD62A4A5FF421F08BC" Directory="INSTALLFOLDER" Guid="CA7EC467-5733-4AB4-8B1A-80C59BBF3558">
                <File Id="fil84B847A9F8DABE6B95AF875695C9E6D1" KeyPath="yes" Source="$(var.DirPath)\d3dcompiler_47.dll" />
            </Component>
            <Component Id="cmp2DE9AB005DAAA250CE55071A3EF8ACA9" Directory="INSTALLFOLDER" Guid="F0990D6D-465F-4B7A-9D6D-297316AE72EB">
                <File Id="fil12F140DF0E8D64081264243D5B778C5B" KeyPath="yes" Source="$(var.DirPath)\ffmpeg.dll" />
            </Component>
            <Component Id="cmpA405A0E1803E59E0C312C7D782950994" Directory="INSTALLFOLDER" Guid="6D61616C-43D7-4A8D-ADC4-8510D4054221">
                <File Id="filB3505C97243815387B3AE5C484C4BF42" KeyPath="yes" Source="$(var.DirPath)\hash.ini" />
            </Component>
            <Component Id="cmpEDF054B04B10AA876BA563BEB132ED2C" Directory="INSTALLFOLDER" Guid="6CA09716-6603-4F92-B9C9-03D4A8ABB41B">
                <File Id="fil51C0F0D7CDD73E71E94FD7B19D1FECD6" KeyPath="yes" Source="$(var.DirPath)\Helper.exe" />
            </Component>
            <Component Id="cmpBDBA16CBEE531189F4ADEB44F36FA9CA" Directory="INSTALLFOLDER" Guid="217ACAB7-BFEA-4593-BF9E-7C21D1BEC8E7">
                <File Id="fil22B9AE685C747A265E9A4BC34E90558B" KeyPath="yes" Source="$(var.DirPath)\icudtl.dat" />
            </Component>
            <Component Id="cmpCF9EE489CA9CC80D046AFA246B9375DA" Directory="INSTALLFOLDER" Guid="EEBBFD44-D257-4FF5-A140-C3BBBF0739A7">
                <File Id="filF39710D27451E759013C9A985FB34C32" KeyPath="yes" Source="$(var.DirPath)\Launcher.exe" />
            </Component>
            <Component Id="cmp2DBF8B1ADC63628B6A8AC335F1979A4C" Directory="INSTALLFOLDER" Guid="D057B5E3-B7B9-4B5D-B79E-D9AF5298D168">
                <File Id="filD0581EC9D860B37C7035787902F55D10" KeyPath="yes" Source="$(var.DirPath)\libcef.dll" />
            </Component>
            <Component Id="cmp363F691415DD37B081CFC985FD89E1ED" Directory="INSTALLFOLDER" Guid="D737353E-C132-4CFB-9838-30A53CD0C61F">
                <File Id="filE4A8517FAA43FB8132ED89D5B5EB797C" KeyPath="yes" Source="$(var.DirPath)\libEGL.dll" />
            </Component>
            <Component Id="cmp8A64E7CE3EE5BD2D11823B3C59FDDE7C" Directory="INSTALLFOLDER" Guid="678F7A3F-8E5A-4D54-B33E-A1D9C9F3C45D">
                <File Id="fil65C8C2AF07D3CA4AE7D7A2844A4D914E" KeyPath="yes" Source="$(var.DirPath)\libGLESv2.dll" />
            </Component>
            <Component Id="cmp8F8CDE7932249067434E3DDF02482ECB" Directory="INSTALLFOLDER" Guid="F5E6FAFB-7985-437C-BE78-51AAE685945D">
                <File Id="fil53BEF7BD88D59B10FB126146A4F0E317" KeyPath="yes" Source="$(var.DirPath)\libSystemProcess.dll" />
            </Component>
            <Component Id="cmpD133DB1F2F3851BB255D14A2983FDEE1" Directory="INSTALLFOLDER" Guid="F9B59D57-4C96-4B53-A5C6-DE6221CE9642">
                <File Id="fil2CC4A280665E6DEDEBFCFBAF361363E9" KeyPath="yes" Source="$(var.DirPath)\libVD.dll" />
            </Component>
            <Component Id="cmp3C1B30342E55492BFA831EACEA997FE2" Directory="INSTALLFOLDER" Guid="0C343DAD-0F45-4DC9-94AC-2F16FDDBC890">
                <File Id="fil08396EBDC04534D3DBCFC887FCD34BFF" KeyPath="yes" Source="$(var.DirPath)\libWebRTC.dll" />
            </Component>
            <Component Id="cmpB1AC150BF57171E60CE6AF36598B46A9" Directory="INSTALLFOLDER" Guid="5BC028FD-A675-4752-ACF6-40DBADA5277B">
                <File Id="fil0239322223A8B4A59CB7F2C5D0F3A48C" KeyPath="yes" Source="$(var.DirPath)\msvcp140.dll" />
            </Component>
            <Component Id="cmp9F94C6813B338F717BE948E703FD5C5A" Directory="INSTALLFOLDER" Guid="BD0AEC45-9E12-48B8-8B63-6375C84EF304">
                <File Id="fil3A3191C3172B73C647CA5F7A78728414" KeyPath="yes" Source="$(var.DirPath)\msvcp140_1.dll" />
            </Component>
            <Component Id="cmp27F74010AFAEB449A5265EB6407A03B0" Directory="INSTALLFOLDER" Guid="525EDF3F-6BAB-4515-8AD2-9E7A7B92EBC3">
                <File Id="filEF3D11CCC60E31B4353BF2BF72146299" KeyPath="yes" Source="$(var.DirPath)\msvcp140_2.dll" />
            </Component>
            <Component Id="cmpDBBE29F8EC9C141F69806318822103CD" Directory="INSTALLFOLDER" Guid="3C6254E4-9052-4379-93B7-DBBAA715A788">
                <File Id="filF7BE5CD96872E2A928439E2F17B74792" KeyPath="yes" Source="$(var.DirPath)\msvcp140_atomic_wait.dll" />
            </Component>
            <Component Id="cmp43EC82A8FF56A5D6D9AC54BE83E4DB87" Directory="INSTALLFOLDER" Guid="FC3F216C-4D3D-4DF4-9FE4-B9C6A57B2842">
                <File Id="fil6E0D815A962D9E595E977609EE547C1A" KeyPath="yes" Source="$(var.DirPath)\msvcp140_codecvt_ids.dll" />
            </Component>
            <Component Id="cmpC51538CC7360A884AEC7A84A5923018B" Directory="INSTALLFOLDER" Guid="DA694C42-F722-4C50-9840-47E52A46D3AD">
                <Class Id="{2BE1F222-691B-32C0-B4AC-00BDD5009896}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+OneTimePassword" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+OneTimePassword" Description="QRCoder.PayloadGenerator+OneTimePassword" />
                </Class>
                <Class Id="{2FAE4911-F0DA-39F4-9080-78F1547981B3}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+MoneroTransaction+MoneroTransactionException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+MoneroTransaction+MoneroTransactionException" Description="QRCoder.PayloadGenerator+MoneroTransaction+MoneroTransactionException" />
                </Class>
                <Class Id="{5040C9C6-C795-35AB-9023-321CEC8D94D8}" Context="InprocServer32" Description="QRCoder.BitmapByteQRCode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.BitmapByteQRCode" Description="QRCoder.BitmapByteQRCode" />
                </Class>
                <Class Id="{578FE599-7074-3EAF-BF38-C9D59B8F923A}" Context="InprocServer32" Description="QRCoder.AsciiQRCode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.AsciiQRCode" Description="QRCoder.AsciiQRCode" />
                </Class>
                <Class Id="{6802EC3B-230F-37D5-83DB-5284F8050282}" Context="InprocServer32" Description="QRCoder.Base64QRCode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.Base64QRCode" Description="QRCoder.Base64QRCode" />
                </Class>
                <Class Id="{7B41F7A1-B948-3A48-95CA-87CBEC025655}" Context="InprocServer32" Description="QRCoder.QRCode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.QRCode" Description="QRCoder.QRCode" />
                </Class>
                <Class Id="{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+SwissQrCode+Reference+SwissQrCodeReferenceException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+SwissQrCode+Reference+SwissQrCodeReferenceException" Description="QRCoder.PayloadGenerator+SwissQrCode+Reference+SwissQrCodeReferenceException" />
                </Class>
                <Class Id="{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}" Context="InprocServer32" Description="QRCoder.PngByteQRCode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PngByteQRCode" Description="QRCoder.PngByteQRCode" />
                </Class>
                <Class Id="{900171F7-4328-3F59-996F-568BDA26706E}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+Girocode+GirocodeException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+Girocode+GirocodeException" Description="QRCoder.PayloadGenerator+Girocode+GirocodeException" />
                </Class>
                <Class Id="{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+SwissQrCode+AdditionalInformation+SwissQrCodeAdditionalInformationException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+SwissQrCode+AdditionalInformation+SwissQrCodeAdditionalInformationException" Description="QRCoder.PayloadGenerator+SwissQrCode+AdditionalInformation+SwissQrCodeAdditionalInformationException" />
                </Class>
                <Class Id="{92AD0EA0-D28E-36F4-947B-1A31CB331585}" Context="InprocServer32" Description="QRCoder.QRCodeGenerator" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.QRCodeGenerator" Description="QRCoder.QRCodeGenerator" />
                </Class>
                <Class Id="{969014F6-6236-3A93-B1A7-AAC362A94686}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+SwissQrCode+SwissQrCodeException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+SwissQrCode+SwissQrCodeException" Description="QRCoder.PayloadGenerator+SwissQrCode+SwissQrCodeException" />
                </Class>
                <Class Id="{B9F7C80F-A608-3544-ADA4-3F112ED72C49}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+BezahlCode+BezahlCodeException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+BezahlCode+BezahlCodeException" Description="QRCoder.PayloadGenerator+BezahlCode+BezahlCodeException" />
                </Class>
                <Class Id="{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}" Context="InprocServer32" Description="QRCoder.PostscriptQRCode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PostscriptQRCode" Description="QRCoder.PostscriptQRCode" />
                </Class>
                <Class Id="{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+SwissQrCode+Contact+SwissQrCodeContactException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+SwissQrCode+Contact+SwissQrCodeContactException" Description="QRCoder.PayloadGenerator+SwissQrCode+Contact+SwissQrCodeContactException" />
                </Class>
                <Class Id="{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}" Context="InprocServer32" Description="QRCoder.XamlQRCode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.XamlQRCode" Description="QRCoder.XamlQRCode" />
                </Class>
                <Class Id="{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+SwissQrCode+Iban+SwissQrCodeIbanException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+SwissQrCode+Iban+SwissQrCodeIbanException" Description="QRCoder.PayloadGenerator+SwissQrCode+Iban+SwissQrCodeIbanException" />
                </Class>
                <Class Id="{F498926D-E673-373D-833B-DC4F3859E822}" Context="InprocServer32" Description="QRCoder.SvgQRCode" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.SvgQRCode" Description="QRCoder.SvgQRCode" />
                </Class>
                <Class Id="{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}" Context="InprocServer32" Description="QRCoder.PayloadGenerator+ShadowSocksConfig+ShadowSocksConfigException" ThreadingModel="both" ForeignServer="mscoree.dll">
                    <ProgId Id="QRCoder.PayloadGenerator+ShadowSocksConfig+ShadowSocksConfigException" Description="QRCoder.PayloadGenerator+ShadowSocksConfig+ShadowSocksConfigException" />
                </Class>
                <File Id="filED862600816F963DB3B1E6F508456CF0" KeyPath="yes" Source="$(var.DirPath)\QRCoder.dll" />
                <ProgId Id="Record" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+OneTimePassword" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+OneTimePassword" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2BE1F222-691B-32C0-B4AC-00BDD5009896}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+MoneroTransaction+MoneroTransactionException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+MoneroTransaction+MoneroTransactionException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{2FAE4911-F0DA-39F4-9080-78F1547981B3}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\InprocServer32\*******" Name="Class" Value="QRCoder.BitmapByteQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\InprocServer32" Name="Class" Value="QRCoder.BitmapByteQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{5040C9C6-C795-35AB-9023-321CEC8D94D8}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\InprocServer32\*******" Name="Class" Value="QRCoder.AsciiQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\InprocServer32" Name="Class" Value="QRCoder.AsciiQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{578FE599-7074-3EAF-BF38-C9D59B8F923A}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\InprocServer32\*******" Name="Class" Value="QRCoder.Base64QRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\InprocServer32" Name="Class" Value="QRCoder.Base64QRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{6802EC3B-230F-37D5-83DB-5284F8050282}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\InprocServer32\*******" Name="Class" Value="QRCoder.QRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\InprocServer32" Name="Class" Value="QRCoder.QRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{7B41F7A1-B948-3A48-95CA-87CBEC025655}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Reference+SwissQrCodeReferenceException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Reference+SwissQrCodeReferenceException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8D608A9E-3CA4-34C6-AD92-E114B9A36B16}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\InprocServer32\*******" Name="Class" Value="QRCoder.PngByteQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\InprocServer32" Name="Class" Value="QRCoder.PngByteQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{8E2529A9-4BC3-3FB6-B926-11167E5B97C1}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+Girocode+GirocodeException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+Girocode+GirocodeException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{900171F7-4328-3F59-996F-568BDA26706E}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+AdditionalInformation+SwissQrCodeAdditionalInformationException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+AdditionalInformation+SwissQrCodeAdditionalInformationException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{90E074E8-9179-3B0B-B8C4-99A38DE66F5B}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\InprocServer32\*******" Name="Class" Value="QRCoder.QRCodeGenerator" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\InprocServer32" Name="Class" Value="QRCoder.QRCodeGenerator" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{92AD0EA0-D28E-36F4-947B-1A31CB331585}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+SwissQrCodeException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+SwissQrCodeException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{969014F6-6236-3A93-B1A7-AAC362A94686}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+BezahlCode+BezahlCodeException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+BezahlCode+BezahlCodeException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{B9F7C80F-A608-3544-ADA4-3F112ED72C49}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\InprocServer32\*******" Name="Class" Value="QRCoder.PostscriptQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\InprocServer32" Name="Class" Value="QRCoder.PostscriptQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{C4C4D46D-B2DC-3B10-A11E-B3C89B83B1F8}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Contact+SwissQrCodeContactException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Contact+SwissQrCodeContactException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{DD9B9BF1-5BB9-3D11-9C5C-455FCDBD3D29}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\InprocServer32\*******" Name="Class" Value="QRCoder.XamlQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\InprocServer32" Name="Class" Value="QRCoder.XamlQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{E9B6C49A-7F8E-3BFE-A53B-CE75033A8D28}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Iban+SwissQrCodeIbanException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Iban+SwissQrCodeIbanException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{ECEB7DCB-FFE2-3C20-A28B-84CAA184F40D}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\InprocServer32\*******" Name="Class" Value="QRCoder.SvgQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\InprocServer32" Name="Class" Value="QRCoder.SvgQRCode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F498926D-E673-373D-833B-DC4F3859E822}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\Implemented Categories\{62C8FE65-4EBB-45e7-B440-6E39B2CDBF29}" Value="" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\InprocServer32\*******" Name="Class" Value="QRCoder.PayloadGenerator+ShadowSocksConfig+ShadowSocksConfigException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\InprocServer32\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\InprocServer32\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\InprocServer32\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\InprocServer32" Name="Class" Value="QRCoder.PayloadGenerator+ShadowSocksConfig+ShadowSocksConfigException" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\InprocServer32" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\InprocServer32" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="CLSID\{F6BC2D66-13F4-3978-98F7-88DBA3BFD7AB}\InprocServer32" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{03124CD3-FD04-38C6-88B6-A8CAB217577E}\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Reference+ReferenceTextType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{03124CD3-FD04-38C6-88B6-A8CAB217577E}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{03124CD3-FD04-38C6-88B6-A8CAB217577E}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{03124CD3-FD04-38C6-88B6-A8CAB217577E}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0C8EAF6D-EE51-396A-8E8D-9D3A2B651AED}\*******" Name="Class" Value="QRCoder.PayloadGenerator+CalendarEvent+EventEncoding" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0C8EAF6D-EE51-396A-8E8D-9D3A2B651AED}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0C8EAF6D-EE51-396A-8E8D-9D3A2B651AED}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{0C8EAF6D-EE51-396A-8E8D-9D3A2B651AED}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{150E7D10-EF1A-3F04-9C2D-EAAA75AE86EE}\*******" Name="Class" Value="QRCoder.SvgQRCode+SizingMode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{150E7D10-EF1A-3F04-9C2D-EAAA75AE86EE}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{150E7D10-EF1A-3F04-9C2D-EAAA75AE86EE}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{150E7D10-EF1A-3F04-9C2D-EAAA75AE86EE}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2092C611-2ECE-363D-9E43-D6D71DF8E5EF}\*******" Name="Class" Value="QRCoder.PayloadGenerator+ContactData+ContactOutputType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2092C611-2ECE-363D-9E43-D6D71DF8E5EF}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2092C611-2ECE-363D-9E43-D6D71DF8E5EF}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{2092C611-2ECE-363D-9E43-D6D71DF8E5EF}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{3E8976C9-9A34-32F4-A3D9-399E54650541}\*******" Name="Class" Value="QRCoder.PayloadGenerator+BezahlCode+Currency" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{3E8976C9-9A34-32F4-A3D9-399E54650541}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{3E8976C9-9A34-32F4-A3D9-399E54650541}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{3E8976C9-9A34-32F4-A3D9-399E54650541}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5A810102-D04C-3479-877C-283DA56A7A30}\*******" Name="Class" Value="QRCoder.PayloadGenerator+ShadowSocksConfig+Method" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5A810102-D04C-3479-877C-283DA56A7A30}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5A810102-D04C-3479-877C-283DA56A7A30}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5A810102-D04C-3479-877C-283DA56A7A30}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5AC9696C-3FAC-330C-9067-E52039B83A92}\*******" Name="Class" Value="QRCoder.PayloadGenerator+Girocode+GirocodeVersion" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5AC9696C-3FAC-330C-9067-E52039B83A92}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5AC9696C-3FAC-330C-9067-E52039B83A92}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5AC9696C-3FAC-330C-9067-E52039B83A92}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5C490E88-DCEE-3E93-B380-664915CC283D}\*******" Name="Class" Value="QRCoder.PayloadGenerator+ContactData+AddressOrder" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5C490E88-DCEE-3E93-B380-664915CC283D}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5C490E88-DCEE-3E93-B380-664915CC283D}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5C490E88-DCEE-3E93-B380-664915CC283D}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5DBA84AC-6C0B-3A9A-8452-B8A24CD6B120}\*******" Name="Class" Value="QRCoder.QRCodeGenerator+EciMode" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5DBA84AC-6C0B-3A9A-8452-B8A24CD6B120}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5DBA84AC-6C0B-3A9A-8452-B8A24CD6B120}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{5DBA84AC-6C0B-3A9A-8452-B8A24CD6B120}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6095D83F-5335-3802-9473-AB4B3E07ECF0}\*******" Name="Class" Value="QRCoder.PayloadGenerator+Geolocation+GeolocationEncoding" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6095D83F-5335-3802-9473-AB4B3E07ECF0}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6095D83F-5335-3802-9473-AB4B3E07ECF0}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6095D83F-5335-3802-9473-AB4B3E07ECF0}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{60CF7868-12D6-3664-B1D2-58C063F60028}\*******" Name="Class" Value="QRCoder.PayloadGenerator+OneTimePassword+OoneTimePasswordAuthAlgorithm" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{60CF7868-12D6-3664-B1D2-58C063F60028}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{60CF7868-12D6-3664-B1D2-58C063F60028}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{60CF7868-12D6-3664-B1D2-58C063F60028}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{624247C8-CBF4-3912-BDD9-B0FB7D86330D}\*******" Name="Class" Value="QRCoder.PayloadGenerator+Mail+MailEncoding" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{624247C8-CBF4-3912-BDD9-B0FB7D86330D}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{624247C8-CBF4-3912-BDD9-B0FB7D86330D}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{624247C8-CBF4-3912-BDD9-B0FB7D86330D}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6C3E6ED3-8FCB-3B62-AD0F-AC89CA1E152A}\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Iban+IbanType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6C3E6ED3-8FCB-3B62-AD0F-AC89CA1E152A}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6C3E6ED3-8FCB-3B62-AD0F-AC89CA1E152A}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{6C3E6ED3-8FCB-3B62-AD0F-AC89CA1E152A}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{78DCA263-5DDE-36D7-97BA-FA0E305E702F}\*******" Name="Class" Value="QRCoder.PayloadGenerator+BezahlCode+AuthorityType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{78DCA263-5DDE-36D7-97BA-FA0E305E702F}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{78DCA263-5DDE-36D7-97BA-FA0E305E702F}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{78DCA263-5DDE-36D7-97BA-FA0E305E702F}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{996DFCB7-65B2-3EF8-AC88-3CD6A247CC6E}\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Currency" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{996DFCB7-65B2-3EF8-AC88-3CD6A247CC6E}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{996DFCB7-65B2-3EF8-AC88-3CD6A247CC6E}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{996DFCB7-65B2-3EF8-AC88-3CD6A247CC6E}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{9989EFE8-1B77-331B-9286-0A1A431BB2AA}\*******" Name="Class" Value="QRCoder.Base64QRCode+ImageType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{9989EFE8-1B77-331B-9286-0A1A431BB2AA}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{9989EFE8-1B77-331B-9286-0A1A431BB2AA}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{9989EFE8-1B77-331B-9286-0A1A431BB2AA}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{ADFE8E0C-C05E-34C4-8052-F0C79F74354E}\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Contact+AddressType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{ADFE8E0C-C05E-34C4-8052-F0C79F74354E}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{ADFE8E0C-C05E-34C4-8052-F0C79F74354E}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{ADFE8E0C-C05E-34C4-8052-F0C79F74354E}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B546EDEA-35D8-3D22-AD21-CE81AA6008FC}\*******" Name="Class" Value="QRCoder.PayloadGenerator+Girocode+GirocodeEncoding" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B546EDEA-35D8-3D22-AD21-CE81AA6008FC}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B546EDEA-35D8-3D22-AD21-CE81AA6008FC}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{B546EDEA-35D8-3D22-AD21-CE81AA6008FC}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BCF206C8-84A6-380D-9FA7-23CC91E596A2}\*******" Name="Class" Value="QRCoder.PayloadGenerator+OneTimePassword+OneTimePasswordAuthAlgorithm" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BCF206C8-84A6-380D-9FA7-23CC91E596A2}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BCF206C8-84A6-380D-9FA7-23CC91E596A2}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BCF206C8-84A6-380D-9FA7-23CC91E596A2}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BE3E84A5-EC59-360A-8928-7B4F76882BFC}\*******" Name="Class" Value="QRCoder.PayloadGenerator+MMS+MMSEncoding" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BE3E84A5-EC59-360A-8928-7B4F76882BFC}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BE3E84A5-EC59-360A-8928-7B4F76882BFC}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{BE3E84A5-EC59-360A-8928-7B4F76882BFC}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{C9342870-3967-37EB-B894-94A04B0AB13F}\*******" Name="Class" Value="QRCoder.PayloadGenerator+SMS+SMSEncoding" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{C9342870-3967-37EB-B894-94A04B0AB13F}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{C9342870-3967-37EB-B894-94A04B0AB13F}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{C9342870-3967-37EB-B894-94A04B0AB13F}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CDDDCF51-5178-3FE7-84C0-593A1BC84C29}\*******" Name="Class" Value="QRCoder.PayloadGenerator+SwissQrCode+Reference+ReferenceType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CDDDCF51-5178-3FE7-84C0-593A1BC84C29}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CDDDCF51-5178-3FE7-84C0-593A1BC84C29}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{CDDDCF51-5178-3FE7-84C0-593A1BC84C29}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{DA42BDE9-78D2-3B92-8A2A-385B19FEB507}\*******" Name="Class" Value="QRCoder.PayloadGenerator+WiFi+Authentication" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{DA42BDE9-78D2-3B92-8A2A-385B19FEB507}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{DA42BDE9-78D2-3B92-8A2A-385B19FEB507}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{DA42BDE9-78D2-3B92-8A2A-385B19FEB507}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E4C95564-D811-3CB7-A891-1DBCA6155BD8}\*******" Name="Class" Value="QRCoder.PayloadGenerator+OneTimePassword+OneTimePasswordAuthType" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E4C95564-D811-3CB7-A891-1DBCA6155BD8}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E4C95564-D811-3CB7-A891-1DBCA6155BD8}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{E4C95564-D811-3CB7-A891-1DBCA6155BD8}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{EDCE365A-5FB1-36FE-B3D8-CD01122B48CE}\*******" Name="Class" Value="QRCoder.QRCodeData+Compression" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{EDCE365A-5FB1-36FE-B3D8-CD01122B48CE}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{EDCE365A-5FB1-36FE-B3D8-CD01122B48CE}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{EDCE365A-5FB1-36FE-B3D8-CD01122B48CE}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{F294909F-0454-3237-BD62-AE87AF09D38B}\*******" Name="Class" Value="QRCoder.PayloadGenerator+Girocode+TypeOfRemittance" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{F294909F-0454-3237-BD62-AE87AF09D38B}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{F294909F-0454-3237-BD62-AE87AF09D38B}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{F294909F-0454-3237-BD62-AE87AF09D38B}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FBE2891A-02A3-3662-A392-5645DF9C5B64}\*******" Name="Class" Value="QRCoder.QRCodeGenerator+ECCLevel" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FBE2891A-02A3-3662-A392-5645DF9C5B64}\*******" Name="Assembly" Value="QRCoder, Version=*******, Culture=neutral, PublicKeyToken=null" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FBE2891A-02A3-3662-A392-5645DF9C5B64}\*******" Name="RuntimeVersion" Value="v4.0.30319" Type="string" Action="write" />
                <RegistryValue Root="HKCR" Key="Record\{FBE2891A-02A3-3662-A392-5645DF9C5B64}\*******" Name="CodeBase" Value="file:///[#filED862600816F963DB3B1E6F508456CF0]" Type="string" Action="write" />
            </Component>
            <Component Id="cmp6317B4DB93D38598C569B7B72B5E5F74" Directory="INSTALLFOLDER" Guid="E60B96C6-BDC5-4ABA-8D47-BA03613F740E">
                <File Id="fil6CDCB8ACE296A89388AC8A57928C78A5" KeyPath="yes" Source="$(var.DirPath)\resources.pak" />
            </Component>
            <Component Id="cmp77F2718815C37DF1CD595A4164461B7A" Directory="INSTALLFOLDER" Guid="99575FA5-B21F-4834-A63C-BA524886D773">
                <File Id="filC50EEA5B1CD3382FF2ED29C29DF6B8AF" KeyPath="yes" Source="$(var.DirPath)\SDL2.dll" />
            </Component>
            <Component Id="cmp502591F7CF4C605EDE7BA63509CB751F" Directory="INSTALLFOLDER" Guid="C6778F0D-FD3C-4475-B7F2-AD47556D56BA">
                <File Id="fil880CC9252F2536067E68688D233C67AD" KeyPath="yes" Source="$(var.DirPath)\snapshot_blob.bin" />
            </Component>
            <Component Id="cmp49125E5013602B31260BC5A53372B428" Directory="INSTALLFOLDER" Guid="694B0F4A-0D37-47D5-9D68-79AC9CDBFFB6">
                <File Id="fil9DE71016B7D050906084ADC7BE601092" KeyPath="yes" Source="$(var.DirPath)\SQLite.Interop.dll" />
            </Component>
            <Component Id="cmp6B7CE49F5F3A518047B74FB6AC2251C5" Directory="INSTALLFOLDER" Guid="0089E6F1-7490-4B7F-8297-C895B2DF09D7">
                <File Id="fil86030949C08D40073D1F77D572C14B0D" KeyPath="yes" Source="$(var.DirPath)\System.Data.SQLite.dll" />
            </Component>
            <Component Id="cmp9D63659120AD7D1FBA61791D5A3FEE3F" Directory="INSTALLFOLDER" Guid="BB086577-1995-4011-AD43-B627740D00AC">
                <File Id="fil442C9E3BC67BA3ECE92379478FBD30DB" KeyPath="yes" Source="$(var.DirPath)\System.Data.SQLite3.dll" />
            </Component>
            <Component Id="cmp503F6379C3ED55757B0993F479069028" Directory="INSTALLFOLDER" Guid="C95A211D-C69D-4777-92B9-94EBC554D12A">
                <File Id="fil43527833784138E19C9DA22610BD76BE" KeyPath="yes" Source="$(var.DirPath)\tbCmd40.exe" />
            </Component>
            <Component Id="cmp3C2BD1F8CD6F713CAED46F8AD6A0F957" Directory="INSTALLFOLDER" Guid="5E3EDD2D-AE63-4E35-AEFF-78D67C529CD1">
                <File Id="fil14CE7FE14CD8213B75F3B1D4C763E720" KeyPath="yes" Source="$(var.DirPath)\tmp.ini" />
            </Component>
            <Component Id="cmpB67698E8021557AB8B9CC56808EA4FD2" Directory="INSTALLFOLDER" Guid="AD7CDD68-A5B2-431B-8DB1-CEA72C6B63BA">
                <File Id="fil4632E4BAE83C5DDA6408E46C4A1F0259" KeyPath="yes" Source="$(var.DirPath)\ucrtbase.dll" />
            </Component>
            <Component Id="cmp49333AEB5663A910B60F81082A0B89C0" Directory="INSTALLFOLDER" Guid="080586D8-643C-4915-A1E3-5A6BC84DEEB0">
                <File Id="fil00211FD728A098172EDA44617490AD92" KeyPath="yes" Source="$(var.DirPath)\UWPCmpt.dll" />
            </Component>
            <Component Id="cmp49BE6E207D2A07FADE852DF8D84126A7" Directory="INSTALLFOLDER" Guid="89BB2C8C-DAF9-4D60-9F49-1B31DA3CAEF8">
                <File Id="filCF0BD5213106E8B5977F2B9CF578AAE0" KeyPath="yes" Source="$(var.DirPath)\v8_context_snapshot.bin" />
            </Component>
            <Component Id="cmpC19BDAAB2B3EEFF7501D4CCF8B9C0C45" Directory="INSTALLFOLDER" Guid="F9E00620-9AE5-4C98-AA53-F6A17EAC7170">
                <File Id="filE29485439AD7A50A2F3A765E450DED64" KeyPath="yes" Source="$(var.DirPath)\vccorlib140.dll" />
            </Component>
            <Component Id="cmpF4BDAE8A068B7352141D703E6D5E677B" Directory="INSTALLFOLDER" Guid="AEEE598B-A3A4-4815-9D73-DD1C5C1B619F">
                <File Id="fil5DEA999AA1C013B635C8FFBBE3F45E71" KeyPath="yes" Source="$(var.DirPath)\vcruntime140.dll" />
            </Component>
            <Component Id="cmp9CEEA2D31E6170C5D7C8F86282DE70EB" Directory="INSTALLFOLDER" Guid="88E545B7-5417-456B-B23D-512A4D560FD1">
                <File Id="fil7CE6214D6487EB513621BAD8BD9196A2" KeyPath="yes" Source="$(var.DirPath)\vk_swiftshader.dll" />
            </Component>
            <Component Id="cmp68BAEB3C86544ED383AA15DBCBA93B70" Directory="INSTALLFOLDER" Guid="BE5B9BAE-7FC2-485B-B414-385596D00B0D">
                <File Id="fil74F228CE6AB6BCB354762C29AA80E442" KeyPath="yes" Source="$(var.DirPath)\vk_swiftshader_icd.json" />
            </Component>
            <Component Id="cmp66B8AECB6A5283B537E7C1F119DDD25B" Directory="INSTALLFOLDER" Guid="0C39A579-1108-42B6-9E66-A321DFAEE94B">
                <File Id="fil0E1FE0F15EF3503D2E931FFDE68780EF" KeyPath="yes" Source="$(var.DirPath)\vulkan-1.dll" />
            </Component>
            <Component Id="cmpD0208DC18F8A3B2968D84441DBB1935A" Directory="INSTALLFOLDER" Guid="60A199D2-0BB0-42E2-B52A-CC6D132DBBB7">
                <File Id="fil0809AC3203D1E525BC22C6920A9066C0" KeyPath="yes" Source="$(var.DirPath)\Win32Security.dll" />
            </Component>
            <Component Id="cmpD26D99F6DC24F64A41CD8E923E128A28" Directory="dir5B673ED707FFD3B07E63CF621B50001B" Guid="9BBD9A43-9818-4C3F-9D52-AEF218426488">
                <File Id="filCF8E88F2F197A1F5FE31AC96CACAFF14" KeyPath="yes" Source="$(var.DirPath)\Codec\ffmpeg.exe" />
            </Component>
            <Component Id="cmp5F2ADA99943261FBB1996FE7E68889E0" Directory="dir608A8623D72D87D1C5E4AC92CD734AE7" Guid="6EDCCFDE-9461-4ACD-8DB0-F94E0BD666A4">
                <File Id="fil3362A36B40620082A69A89716B2D39A6" KeyPath="yes" Source="$(var.DirPath)\Font\HarmonyOS_Sans_SC_Medium.ttf" />
            </Component>
            <Component Id="cmpC03DA5560D61EC9A4867505218F4D742" Directory="dir608A8623D72D87D1C5E4AC92CD734AE7" Guid="A4C6EE2D-FC31-4882-BF6E-074196F40B94">
                <File Id="filA4D1E1E07EB31E3DDCD0684230FC5E62" KeyPath="yes" Source="$(var.DirPath)\Font\HarmonyOS_Sans_SC_Regular.ttf" />
            </Component>
            <Component Id="cmp7375D33157C44167DEA67CAF877D2F17" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="F49DB13C-0619-4083-B74E-7C006BC214A9">
                <File Id="filE9718D6BBDF94CC746380574027E1E9C" KeyPath="yes" Source="$(var.DirPath)\Lang\de.lang" />
            </Component>
            <Component Id="cmpFD6805E1038482D4A52E6EC4616EB92A" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="88AEBF55-DB08-4208-A50C-A1A7116D291B">
                <File Id="fil932BCE7D59BBE65CC7D19093C9B9323F" KeyPath="yes" Source="$(var.DirPath)\Lang\en.lang" />
            </Component>
            <Component Id="cmp65A872953CB268B230BF99FF6EC2A25F" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="6C166426-E20E-4F0E-94E6-AA19F8A4F65F">
                <File Id="fil15E101B32C1182748A9667F7F755B69A" KeyPath="yes" Source="$(var.DirPath)\Lang\es.lang" />
            </Component>
            <Component Id="cmp33C7BA022043116DDFD01C668065328C" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="8FB45616-2DAB-4112-90BA-0A3AE17775F6">
                <File Id="filE028A6BEDFE0DCCE786A3A4A489985BF" KeyPath="yes" Source="$(var.DirPath)\Lang\fr.lang" />
            </Component>
            <Component Id="cmp137A2D565F92E96E3EC17E8C64DCA350" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="22C0D5D5-**************-F36EC18C8721">
                <File Id="filF988375BD228BB4BE6C7450BEB7D19A5" KeyPath="yes" Source="$(var.DirPath)\Lang\it.lang" />
            </Component>
            <Component Id="cmpEE6B48F391C82521DB2BC46B67C25FFC" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="71E70CA5-3C66-483B-92D0-A93F3C31C36F">
                <File Id="filCEC0A2F37BF63C35A4602D6B9D4CD1EC" KeyPath="yes" Source="$(var.DirPath)\Lang\ja.lang" />
            </Component>
            <Component Id="cmpB670B19BC007C8A2992DCE7827B30DCE" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="D748C022-D83C-4158-9B2B-853DB4F2D05F">
                <File Id="filD38F5A24C3C7C3CA2C1A8EA0BA493C04" KeyPath="yes" Source="$(var.DirPath)\Lang\pt.lang" />
            </Component>
            <Component Id="cmp28CD044E041381DD6D850E45B44F386C" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="D82170B9-8C83-4112-ABF3-C0F610835B0F">
                <File Id="fil75B82A52B5303B78F96285D7F940F813" KeyPath="yes" Source="$(var.DirPath)\Lang\ru.lang" />
            </Component>
            <Component Id="cmpB44EB0C972C106105C9D07205FFCD8E9" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="ED62B062-8196-4E2E-B63A-69BDA5E17E90">
                <File Id="fil5DB4A34D169D65B6318672E92D663D57" KeyPath="yes" Source="$(var.DirPath)\Lang\zh-CN.lang" />
            </Component>
            <Component Id="cmpDAF4E9F590E0BBD786A9CB652C6D516E" Directory="dir23EEB6A7E0429F277985FA4BC5B2C64F" Guid="3E7C7518-208D-4005-B68C-050EB9A20C5C">
                <File Id="fil85C91ADB3022EAC49DD1F9BB7C52380F" KeyPath="yes" Source="$(var.DirPath)\Lang\zh-TW.lang" />
            </Component>
            <Component Id="cmpB9603F36768BB7B27EA6039D6671BCB8" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="10FCE268-1601-4882-A04D-5F17A9F57310">
                <File Id="filFDB6F3CBB5F3014F2CD2D16A524F03E2" KeyPath="yes" Source="$(var.DirPath)\locales\af.pak" />
            </Component>
            <Component Id="cmp93B53FD801CE3270DE5D9AF01811CD8F" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="6D504F86-E44C-4A29-A30A-4545A4083E14">
                <File Id="fil32C51E9E820B1FEE0FB7D2E6E3650736" KeyPath="yes" Source="$(var.DirPath)\locales\am.pak" />
            </Component>
            <Component Id="cmpD2797D63574120F1A02B6E6185F4B7E2" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="B0F90554-BAD3-4952-91ED-A482499F4EFE">
                <File Id="filE62371EEC2BE8913C457C9896BC40E9A" KeyPath="yes" Source="$(var.DirPath)\locales\ar.pak" />
            </Component>
            <Component Id="cmp1AF90EE181A1DEC5823E23D7370B0AC2" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="BB8481D2-5164-4BD9-9882-D177A3A2D473">
                <File Id="fil197468F9B67748B054E36BB298793554" KeyPath="yes" Source="$(var.DirPath)\locales\bg.pak" />
            </Component>
            <Component Id="cmp9122F3FD82E13BBDEB542C177A0EC7D5" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="D4B7895C-C3CF-4B47-AC5F-9A8D271F229E">
                <File Id="fil69C5332C84289AEB85A0CD1354977C6F" KeyPath="yes" Source="$(var.DirPath)\locales\bn.pak" />
            </Component>
            <Component Id="cmp370DC6B00487D74A5F7A2E29559F1D90" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="7042E942-4FA6-43B4-B652-3F669876C086">
                <File Id="fil080B7233B73921355D2AFD19757F80F4" KeyPath="yes" Source="$(var.DirPath)\locales\ca.pak" />
            </Component>
            <Component Id="cmpAB150C6499776718D81F89B0265B385C" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="049C24FE-D6DF-4603-941A-CB58EBCB5112">
                <File Id="fil5711D2A370AA838D04CC84A20FB2D003" KeyPath="yes" Source="$(var.DirPath)\locales\cs.pak" />
            </Component>
            <Component Id="cmpEEA4B811F2050E122093474F4C3DDC22" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="145FAE33-5B7D-448D-B215-030C438539FF">
                <File Id="filF93366DEB72F1954365B01581D840CB2" KeyPath="yes" Source="$(var.DirPath)\locales\da.pak" />
            </Component>
            <Component Id="cmp8E7B894FEEA9D2544CD2711A257B11A4" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="805324C0-B436-45B6-B616-52528C42654A">
                <File Id="filDEA640C897520178D603DAE3884CE9E1" KeyPath="yes" Source="$(var.DirPath)\locales\de.pak" />
            </Component>
            <Component Id="cmpABEF315E588C010C42447949EC808AE4" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="5F586A93-DD73-4ED1-95D2-559CB90DB39A">
                <File Id="filD99255DFF55AFC1FCFB6E5CAA9882BC3" KeyPath="yes" Source="$(var.DirPath)\locales\el.pak" />
            </Component>
            <Component Id="cmp116941C2E2E7C9B899A83AD97EF8E7D7" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="0D270CCB-06BC-4AEE-81DD-96544E947B82">
                <File Id="filD44653A1ED9BC2BFC4319FBAEF2673C4" KeyPath="yes" Source="$(var.DirPath)\locales\en-GB.pak" />
            </Component>
            <Component Id="cmp1E9EB69C90AF854878152528EE404BA8" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="12AB3F01-6F06-45FA-971B-3AAF4B291E02">
                <File Id="filFFCCBA7E104B020272FC529C9BAB0AAC" KeyPath="yes" Source="$(var.DirPath)\locales\en-US.pak" />
            </Component>
            <Component Id="cmp15BDFF12521FE67EBA060B799E1A0D93" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="19BA6E9A-8938-4522-9BB9-F1BB55E6EF38">
                <File Id="fil63E338785F4491D992D3903722D3B6EF" KeyPath="yes" Source="$(var.DirPath)\locales\es-419.pak" />
            </Component>
            <Component Id="cmp1F53EACB722F43303A8863937CAE9291" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="1623E859-FADC-4C41-A7F0-6FBC4C7EC86F">
                <File Id="fil0766667F6ABA83BE7F48FCAC1A2901E1" KeyPath="yes" Source="$(var.DirPath)\locales\es.pak" />
            </Component>
            <Component Id="cmp10AC23BF72464B8B90624A3E997776E4" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="63D46240-A15A-4C83-8143-8C8951667CC9">
                <File Id="filF4514DAA92D828FFB8780676B26FEBC0" KeyPath="yes" Source="$(var.DirPath)\locales\et.pak" />
            </Component>
            <Component Id="cmp8D529815FA519DD03174555F81E9432C" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="3FD05C70-8CD2-4B6A-B8A6-C5FB088DC688">
                <File Id="fil9C1652CFE226171A1DAB8DE1932FC343" KeyPath="yes" Source="$(var.DirPath)\locales\fa.pak" />
            </Component>
            <Component Id="cmpC0300B48619E85CC72FE79E8917363E0" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="BB3EA7B1-E1BF-474D-A1CB-5EEE810B4E89">
                <File Id="fil3586CA3C720B816206B05A4B2FC4AAD3" KeyPath="yes" Source="$(var.DirPath)\locales\fi.pak" />
            </Component>
            <Component Id="cmpBF1200CBA7335661198621024C47AB95" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="D4F707F7-6D69-40C5-AD80-CDD1E3F21004">
                <File Id="fil307AD6DB42F42A7B16855D8BD515BE70" KeyPath="yes" Source="$(var.DirPath)\locales\fil.pak" />
            </Component>
            <Component Id="cmpF0433B61030E830A7CDE05B6B5715E3F" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="067CC06F-529A-43F7-AA48-47261377740D">
                <File Id="filEED9FF376F27B3DEADF4D9F000FF635E" KeyPath="yes" Source="$(var.DirPath)\locales\fr.pak" />
            </Component>
            <Component Id="cmp063FD815C8251B48E56F2C4EADDA9BAD" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="B11069FB-3EB4-46FC-AFE2-436927FEC040">
                <File Id="fil58E5427690D013654296A6731139691E" KeyPath="yes" Source="$(var.DirPath)\locales\gu.pak" />
            </Component>
            <Component Id="cmp3143C2120E07B8B0960135F609F9046A" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="1BACF58E-D8C4-425E-89DB-84B6870FF7F1">
                <File Id="fil6D4577F865C2CA110A3C91456B2B9FFA" KeyPath="yes" Source="$(var.DirPath)\locales\he.pak" />
            </Component>
            <Component Id="cmpF56E022E28C51D72B3E1973D3779D239" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="C93B2B85-CDB9-4095-B4A5-2C10C90850D1">
                <File Id="filD04C5871771E24822D916CBB4F175C48" KeyPath="yes" Source="$(var.DirPath)\locales\hi.pak" />
            </Component>
            <Component Id="cmp57995CAE9ED4DBAF0A8D6A3CFD22B801" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="92F60D48-194E-4025-8B88-6E6484268B5C">
                <File Id="filD841A14EBCF7D80432707F0EA0D9D001" KeyPath="yes" Source="$(var.DirPath)\locales\hr.pak" />
            </Component>
            <Component Id="cmp3FFA918A28CB6851B668FA38EF737013" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="17D2F6E1-1483-46D1-9FD4-950B2E0393E5">
                <File Id="filA1F200EBD3BE190C99D672E49782958C" KeyPath="yes" Source="$(var.DirPath)\locales\hu.pak" />
            </Component>
            <Component Id="cmpD81CBFA59C14DC5F01C36D5F2D70E892" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="D6C67172-57A8-4B77-8256-D39A407003D4">
                <File Id="fil2BEC26B3E56A049BB9FA51553BD6A88C" KeyPath="yes" Source="$(var.DirPath)\locales\id.pak" />
            </Component>
            <Component Id="cmpC4FB3A2F1FEC6CFCDE9A0BE743E7F6D9" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="7561F8E8-637F-40FB-914B-0D5D0A3AF4A4">
                <File Id="filD167F7F85C4873B561A69E160D748432" KeyPath="yes" Source="$(var.DirPath)\locales\it.pak" />
            </Component>
            <Component Id="cmp99DA4070D1A05ED903D0B1045F4CB807" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="7F5B4502-3E79-4C87-B2B5-20FFB9661A49">
                <File Id="fil30C367B34BE066E12F9728A7615E6A88" KeyPath="yes" Source="$(var.DirPath)\locales\ja.pak" />
            </Component>
            <Component Id="cmp6A6473C02425A6733CC160845C56436E" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="12D9F223-53A9-4B9E-A7C0-ACCA290F2056">
                <File Id="fil1C71CED22AA3F3867FBFA98B8B2EDFAF" KeyPath="yes" Source="$(var.DirPath)\locales\kn.pak" />
            </Component>
            <Component Id="cmp4BC8631015E4672D4836CDCE97E2CEF9" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="EDEB790E-61D6-4412-966F-A9BE58ED14B3">
                <File Id="fil577C0634EDCB4B746195D64D9E2F9E81" KeyPath="yes" Source="$(var.DirPath)\locales\ko.pak" />
            </Component>
            <Component Id="cmp5218FB633BEDEE8BECAD031A2F5D0598" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="4941DECF-0C30-4BD6-B951-8CE2D70D3FF4">
                <File Id="fil3E5F1496C928D4B825DCDB0307241AE0" KeyPath="yes" Source="$(var.DirPath)\locales\lt.pak" />
            </Component>
            <Component Id="cmpFBB47518212FA949EBDDB5C9237979DC" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="392F60AD-8EBF-4401-B2B6-77A6ED4E1D92">
                <File Id="filA398FE151CDF7979BF9AC53D8CFA82C5" KeyPath="yes" Source="$(var.DirPath)\locales\lv.pak" />
            </Component>
            <Component Id="cmpACA7EA0460B91B4D1931EB61430E084A" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="3894BE94-C31E-492C-A328-2A79A947CC05">
                <File Id="fil77A7A24D45400F96ECFBB9886F1CEFD6" KeyPath="yes" Source="$(var.DirPath)\locales\ml.pak" />
            </Component>
            <Component Id="cmp51ED07B7B76B22DF2F693142B411B747" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="D3C72791-EFBF-4915-93EC-E100BE046501">
                <File Id="filF20231EEB000EA181CD44731D794D329" KeyPath="yes" Source="$(var.DirPath)\locales\mr.pak" />
            </Component>
            <Component Id="cmpCF56E74D3DEDCFFA6B07A0F646766426" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="622DB94C-5BEC-4585-9526-0A3C7425CA6D">
                <File Id="fil52A07789EE7C0E7157531E2D36180DC3" KeyPath="yes" Source="$(var.DirPath)\locales\ms.pak" />
            </Component>
            <Component Id="cmpE148651493613E026DE50D51E32C9D36" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="C284D50B-0CBE-4363-83E6-9DFA55A5CEA2">
                <File Id="fil7A6CCCAB61609A4FE3516B07CE34CB59" KeyPath="yes" Source="$(var.DirPath)\locales\nb.pak" />
            </Component>
            <Component Id="cmpA550175598E90A95F9EA0FE0B5420674" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="137EE958-2A88-4211-88D0-D2BD9BBF9401">
                <File Id="filCC586543F15D26FEEDB970F9C8E3C1F8" KeyPath="yes" Source="$(var.DirPath)\locales\nl.pak" />
            </Component>
            <Component Id="cmpB8D9D975685D2766C0145A80BBC753D2" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="1513AF34-BAD8-4A84-9346-053F9D12CD18">
                <File Id="filACB5827860225636FDADCBE0A180A72C" KeyPath="yes" Source="$(var.DirPath)\locales\pl.pak" />
            </Component>
            <Component Id="cmp2D2F750D066E5C616BF96CC2D2C19C3F" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="6E073B8F-83A7-4D8C-858F-BCC597A45D43">
                <File Id="fil77BBF2F61A3922ADC5ACE58CB631FBA3" KeyPath="yes" Source="$(var.DirPath)\locales\pt-BR.pak" />
            </Component>
            <Component Id="cmp2C93BD707B4E95E9B4C39746C6AFC072" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="54E3A29E-30DD-4A1C-9A86-71D3EAD7F59F">
                <File Id="fil94C287653A43818B9DB420EEAD365A81" KeyPath="yes" Source="$(var.DirPath)\locales\pt-PT.pak" />
            </Component>
            <Component Id="cmp1CC474982F842464BC57160F3469FE9D" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="AA88A04A-DB52-4DE5-9BBA-FAFB2C87A7F9">
                <File Id="fil660BDA7672AB67027249AC93E7DD2CE1" KeyPath="yes" Source="$(var.DirPath)\locales\ro.pak" />
            </Component>
            <Component Id="cmpA03EC2080F86CAD0190A073F463D0A6A" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="6816FDBD-7129-4BD6-A5A8-C7F233C8E79C">
                <File Id="fil32F940C60F041D18490E308365ADB39C" KeyPath="yes" Source="$(var.DirPath)\locales\ru.pak" />
            </Component>
            <Component Id="cmp46AE10957CC5BFF14ADF2F589AF84ADB" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="FBC55A9A-E716-4936-8807-8BAFD0FE9425">
                <File Id="fil9AA414870F50D9CF1BB2E32C25A51FD5" KeyPath="yes" Source="$(var.DirPath)\locales\sk.pak" />
            </Component>
            <Component Id="cmp966963C734F9127821405781A74C395A" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="D0F2F0C7-E65A-4B21-A88C-67789C7258E8">
                <File Id="filB230566A7E9DCD1BE19EC00D694BA972" KeyPath="yes" Source="$(var.DirPath)\locales\sl.pak" />
            </Component>
            <Component Id="cmp3A37E594568D9C8FC805401E9F537A15" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="CF08FAA6-AC3A-4B11-8B79-21B13BDD18DE">
                <File Id="fil2808F891D8F9BDBE596DCA07A733D482" KeyPath="yes" Source="$(var.DirPath)\locales\sr.pak" />
            </Component>
            <Component Id="cmpB3B6BCE656FBF128D00532AC082351CD" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="480B6EA5-4D84-4C3B-B06D-822079B3B5FF">
                <File Id="fil6D5A260A7B169BE6302A256ED94AE364" KeyPath="yes" Source="$(var.DirPath)\locales\sv.pak" />
            </Component>
            <Component Id="cmp01DA64CB05C8208C5866696CCA3A6F1D" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="0BDCE005-1AFE-42CA-A352-E22D57A45238">
                <File Id="fil33988F1E51A9F13306543F8EABBAB196" KeyPath="yes" Source="$(var.DirPath)\locales\sw.pak" />
            </Component>
            <Component Id="cmp6B2D68FA43306E35219A58389D7E6E72" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="467F6BDD-8549-4870-B36C-8724D6AB5B39">
                <File Id="filA074476FF2286963A6DE4A84A6C38F70" KeyPath="yes" Source="$(var.DirPath)\locales\ta.pak" />
            </Component>
            <Component Id="cmp6EE3CCCBBCF4AF2E6ABEA15A31097EB7" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="F64086DF-C135-454D-A20E-94C90A499C7C">
                <File Id="filDDAA366E0596957885171C41B8C0CED7" KeyPath="yes" Source="$(var.DirPath)\locales\te.pak" />
            </Component>
            <Component Id="cmp2BA1A13793CDF598C1BC89182DAB4F9F" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="1B2C0B41-B976-4DE8-80D5-195D803D20FE">
                <File Id="filB1DBF99CA7F144B8F04607E2FBF3F16A" KeyPath="yes" Source="$(var.DirPath)\locales\th.pak" />
            </Component>
            <Component Id="cmpFB95F9A9F80BAB90CBF5CDABAC0461A2" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="38878FD8-92E0-44D6-88B3-953A471DA399">
                <File Id="filC862018B514C4C7499938D51D5C50B6F" KeyPath="yes" Source="$(var.DirPath)\locales\tr.pak" />
            </Component>
            <Component Id="cmpCDA6E7086714AD0613F7CBA0546FA99F" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="AC8D15B1-CB21-4129-BA14-0A24D263C6F1">
                <File Id="fil8B04B85DE94D1EC009615182A0D5AD74" KeyPath="yes" Source="$(var.DirPath)\locales\uk.pak" />
            </Component>
            <Component Id="cmp94CCDE333CE20A5F7250E09035EA9FEE" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="A99F86E5-D7DC-42C1-B299-659A10EFBAB3">
                <File Id="filAAE567EA8F6B2615BD7BB050474DC77D" KeyPath="yes" Source="$(var.DirPath)\locales\ur.pak" />
            </Component>
            <Component Id="cmp80642D2D1994622CA31AC0ED1ED014D1" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="3FFECC28-794C-4EC2-9222-E239C1EB32A0">
                <File Id="filE214F8C85939B07DAA0CDFAE4A0F5700" KeyPath="yes" Source="$(var.DirPath)\locales\vi.pak" />
            </Component>
            <Component Id="cmp5775E2EF67EC1F2A7A26152C6507EA35" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="04B53AC4-A613-4C9C-B4DA-D65DCCC4DC0C">
                <File Id="fil5445EF7D54ED17AE5131F5EF25F54F2C" KeyPath="yes" Source="$(var.DirPath)\locales\zh-CN.pak" />
            </Component>
            <Component Id="cmpC00155C4F0BBC6D4B881D098F564FA42" Directory="dir07100F2E598E14B511BAF734827AE4AB" Guid="EAA91886-42BD-4FD5-BDE9-48436BC6B53C">
                <File Id="filAD427424F750F9EADEDB5030ACC85834" KeyPath="yes" Source="$(var.DirPath)\locales\zh-TW.pak" />
            </Component>
        </ComponentGroup>
    </Fragment>
</Wix>