﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{4400BC80-4B98-47BF-8790-B74B5A4A2F4A}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>tbRepair.ApplicationEntry</StartupObject>
    <RootNamespace>tbRepair</RootNamespace>
    <AssemblyName>tbRepair</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsFormsWithCustomSubMain</MyType>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>Off</OptionInfer>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <NoWarn>41999,42016,42017,42018,42019,42020,42021,42022,42032,42036</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Resources\iTong.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="iTong.Components" />
    <Import Include="iTong.CoreFoundation" />
    <Import Include="iTong.CoreFoundation.SevenZip" />
    <Import Include="iTong.CoreReses" />
    <Import Include="iTong.Update" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationEntry.vb" />
    <Compile Include="IniClass.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Components\Components.vbproj">
      <Project>{28459798-5e28-45d3-aae3-74a9f1761d1a}</Project>
      <Name>Components</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc.csproj">
      <Project>{685ae51b-3c37-4b37-b3a2-b485d07a6e6b}</Project>
      <Name>CoreMisc</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses.csproj">
      <Project>{65ce9103-521a-49e6-a8cd-89137b452842}</Project>
      <Name>CoreReses</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUpdate\CoreUpdate.csproj">
      <Project>{998a7f44-f7af-4e25-9ef3-696834ee6242}</Project>
      <Name>CoreUpdate</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS.csproj">
      <Project>{61c62ea1-6ad7-41be-98db-8abb77ee35f3}</Project>
      <Name>CoreUtilCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil.vbproj">
      <Project>{718b167f-1b33-4b7c-a7e3-e15615ddaac4}</Project>
      <Name>CoreUtil</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\iTong.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildbinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>