﻿Imports System.Text

Public Class IniClass

    Private Shared mConfigPath As String = ""

    Shared Sub New()
        mConfigPath = Folder.ConfigServerIniFile
    End Sub

    '******************************************************************************************
    '**
    '** 模块名称: IniClass
    '** 作　　者: 熊俊
    '** 创建日期: 2005-3-10
    '** 功　　能: 对 Ini 文件进行操作
    '** 说　　明: 完整的对 Ini 文件进行操作的模块

    '**
    '******************************************************************************************

    '***************************************************
    '**
    '** Ini 文件结构说明：

    '**
    '** [Option]  ------------------------> Section
    '** main = images \ logo.jpg  --------> Key = KeyValue
    '**
    '***************************************************

    '/* 定义 API */
    Private Declare Function GetPrivateProfileString _
       Lib "kernel32" Alias "GetPrivateProfileStringA" _
                            (ByVal lpApplicationName As String, _
                             ByVal lpKeyName As String, _
                             ByVal lpDefault As String, _
                             ByVal lpReturnedString As String, _
                             ByVal nSize As Int32, _
                             ByVal lpFileName As String) _
       As Int32
    '-------------------------------------------------------------
    Private Declare Function WritePrivateProfileString _
       Lib "kernel32" Alias "WritePrivateProfileStringA" _
                            (ByVal lpApplicationName As String, _
                             ByVal lpKeyName As String, _
                             ByVal lpString As String, _
                             ByVal lpFileName As String) _
       As Int32
    '-------------------------------------------------------------
    Private Declare Function GetPrivateProfileSection _
       Lib "kernel32" Alias "GetPrivateProfileSectionA" _
                            (ByVal lpAppName As String, _
                             ByVal lpReturnedString As String, _
                             ByVal nSize As Int32, _
                             ByVal lpFileName As String) _
       As Int32
    '-------------------------------------------------------------
    Private Declare Function WritePrivateProfileSection _
       Lib "kernel32" Alias "WritePrivateProfileSectionA" _
                            (ByVal lpApplicationName As String, _
                             ByVal lpAllKeyName As String, _
                             ByVal lpFileName As String) _
       As Int32

    '******************************************************************************************
    '**
    '** 名　　称: GetIniSectionKey
    '** 备　　注:
    '** 功　　能: 得到段中某健的健值

    '** 返 回 值: As String     '健值（字符串）
    '** 参　　数:
    '**           sSection      As String   ' 段名
    '**           sKey          As String   ' 健名
    '**           sIniFileName  As String   ' Ini文件名

    '**
    '******************************************************************************************
    Public Shared Function GetIniSectionKey(ByVal sSection As String, _
                                            ByVal sKey As String, _
                                            ByVal sIniFileName As String) As String

        Dim lngLen As Int32
        Dim sRet As String = New String(Chr(0), 255)
        Dim strReturn As String

        lngLen = GetPrivateProfileString(sSection, sKey, "", sRet, Len(sRet), sIniFileName)

        strReturn = Strings.Left(sRet, lngLen).Replace(Chr(0), "")

        Return strReturn

    End Function

    '******************************************************************************************
    '**
    '** 名　　称: SetIniSectionKey
    '** 备　　注:
    '** 功　　能: 设置段中某健的健值

    '** 返 回 值: As Boolean    '是否设置成功
    '** 参　　数:
    '**           sSection      As String   ' 段名
    '**           sKey          As String   ' 健名
    '**           sValue        As String   ' 健值

    '**           sIniFileName  As String   ' Ini文件名

    '**
    '******************************************************************************************
    Public Shared Function SetIniSectionKey(ByVal sSection As String, _
                                            ByVal sKey As String, _
                                            ByVal sValue As String, _
                                            ByVal sIniFileName As String) As Boolean

        Dim lngLen As Int32

        lngLen = WritePrivateProfileString(sSection, sKey, sValue, sIniFileName)
        SetIniSectionKey = IIf(lngLen = 0, False, True)

    End Function

#Region "--- （ServerIni）修复红叉 ---"

    Public Shared Function GetFixRedCross() As Boolean
        Dim blnReturn As Boolean = True
        Dim strValue As String = IniClass.GetIniSectionKey("Setting", "FixRedCross", mConfigPath) & ""
        If strValue.ToLower = "false" Then
            blnReturn = False
        End If
        Common.Log("FixRedCross" & mConfigPath.ToString())
        Return blnReturn
    End Function

#End Region

End Class
