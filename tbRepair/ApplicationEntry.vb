﻿Imports Microsoft.Win32
Imports System.IO
Imports System.Windows.Forms

Module ApplicationEntry

    Sub Main()
        Try
            '设置管理员权限（添加或删除）
            If Folder.AppType <> RunType.AirDroid Then
                OperateRunAsAdmin()
            End If

            Dim strFirstValue As String = My.Application.CommandLineArgs(0).ToLower()
            Select Case strFirstValue
                Case "/repair"
                    DoRepairThread()
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Helper.Main")
        End Try
    End Sub

#Region "--- 属性 ---"

    'Private ReadOnly Property AppFolder() As String
    '    Get
    '        Return AppDomain.CurrentDomain.BaseDirectory.TrimEnd("\"c) & "\"
    '    End Get
    'End Property

    Private ReadOnly Property MainProcessName() As String
        Get
            Return Folder.AppType.ToString() & ".exe"
        End Get
    End Property

#End Region

#Region "--- 设置管理员权限 ---"

    Private Sub OperateRunAsAdmin()
        Try
            '如果操作系统的版本号大等于 6 （Vista 版本号 6.0，Win 7 版本号 6.1） 就把 UACHelper 目录下所有的 tongbu.exe 设置成 RUNASADMIN
            If Val(Left(My.Computer.Info.OSVersion, 1)) >= 6 Then

                Dim appPath As String = My.Application.Info.DirectoryPath.TrimEnd("\")

                Dim strAppName As String = MainProcessName

                For Each launcherApp As String In My.Computer.FileSystem.GetFiles(appPath, FileIO.SearchOption.SearchTopLevelOnly, strAppName)
                    If My.Application.CommandLineArgs.Contains("/clear") = True Then
                        '取消 tongbu.exe 使用管理员权限执行
                        ClearRunAsAdmin(launcherApp)
                    Else
                        '设置 tongbu.exe 使用管理员权限执行
                        SetRunAsAdmin(launcherApp)
                    End If
                Next
            End If
        Catch ex As Exception
            Debug.Print(ex.ToString())
        End Try
    End Sub

    Private Sub SetRunAsAdmin(ByVal appPath As String)

        If My.Computer.FileSystem.FileExists(appPath) Then

            Try
                Dim key As Microsoft.Win32.RegistryKey = Microsoft.Win32.Registry.CurrentUser.OpenSubKey("Software\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers", True)

                key.SetValue(appPath, "RUNASADMIN")

            Catch
            End Try

        End If

    End Sub

    Private Sub ClearRunAsAdmin(ByVal appPath As String)
        Try
            Dim key As Microsoft.Win32.RegistryKey = Microsoft.Win32.Registry.CurrentUser.OpenSubKey("Software\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers", True)

            key.DeleteValue(appPath)
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 修复微软补丁造成的红X ---"

    Private Sub DoRepairThread()
        '解决微软的红x的问题。
        Dim thd As New System.Threading.Thread(New System.Threading.ThreadStart(AddressOf DoRepair))
        thd.IsBackground = True
        thd.Start()

        Do
            Utility.WaitSeconds(0.1)
        Loop While thd.ThreadState <> Threading.ThreadState.Stopped AndAlso thd.IsAlive
    End Sub

    Private Sub DoRepair()
        If Not IniClass.GetFixRedCross() OrElse Not IsErrorPC() Then
            Return
        End If

        Try
            '出现问题的电脑直接先引出教程
            Common.OpenExplorer("http://news.tongbu.com/86668.html")
            Dim strMsg As String = "检测到您的助手已经出现异常， " & vbCrLf & _
                                      "是因为安装了微软补丁KB3097877造成的， " & vbCrLf & _
                                      "助手将尝试帮您下载最新的补丁进行修复，是否进行修复？"
            'Common.OpenExplorer("http://news.tongbu.com/86668.html")
            If MessageBox.Show(Nothing, _
                                strMsg, _
                                "信息", _
                                MessageBoxButtons.YesNo, _
                                MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.No Then
                GetOperateActionInfo(ModelKey.ErrorRepair, ActionDataType.View, FunctionSucceed.Failure)
                GoTo DoExit
            Else
                GetOperateActionInfo(ModelKey.ErrorRepair, ActionDataType.View, FunctionSucceed.Succeed)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Helper_DoRepairShowError")
        End Try

        Try
            '1. 下载补丁
            Dim strName As String = MakeDownloadUrlName()
            Dim strUrl As String = String.Format("http://qd.leaderhero.com/qd/tbzsqd/Patch/{0}", strName)
            Common.LogException("<<生成的下载地址：" & strUrl)

            Dim strDownloadFolder As String = Path.Combine(Folder.DownloadFolder(), "Patch")
            Dim strTempFile As String = Path.Combine(strDownloadFolder, strName & ".tmp")
            Dim strFilePath As String = Path.Combine(strDownloadFolder, strName)

            Folder.CheckFolder(strDownloadFolder)
            Common.LogException("<<下载的路径：" & strFilePath)

            Common.LogException("<<开始下载：" & strFilePath)
            If Not File.Exists(strFilePath) AndAlso Utility.DownLoadFile(strUrl, strTempFile, 60, Nothing) Then
                If File.Exists(strFilePath) Then
                    Try
                        File.Delete(strFilePath)
                    Catch
                    End Try
                End If

                File.Move(strTempFile, strFilePath)
            End If

            '2. 安装补丁
            If File.Exists(strFilePath) Then
                GetOperateActionInfo(ModelKey.ErrorRepair, ActionDataType.Download, FunctionSucceed.Succeed)

                Common.LogException("<<开始安装：" & strFilePath)
                PostProtobuf()
                Dim p As System.Diagnostics.Process = System.Diagnostics.Process.Start(strFilePath)
                p.WaitForExit()

                Common.LogException("<<判断补丁KB3097877是否安装：")
                If IsErrorPC() = True Then
                    Common.LogException("<<判断补丁KB3097877是否安装：无")
                    GetOperateActionInfo(ModelKey.ErrorRepair, ActionDataType.Install, FunctionSucceed.Failure)         '安装失败
                    GoTo DoExit
                End If
                GetOperateActionInfo(ModelKey.ErrorRepair, ActionDataType.Install, FunctionSucceed.Succeed)             '安装成功

                Common.LogException("<<判断补丁KB3097877是否安装：有，提示重启电脑")

                '请点击安装补丁， 安装完成后要重启电脑。
                If MessageBox.Show("补丁已经安装成功，需要重启电脑才能生效，是否立即重启？", _
                                                    "提示", _
                                                 MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = DialogResult.Yes Then

                    Common.LogException("<<尝试重启电脑")

                    Dim ps As New ProcessStartInfo
                    ps.FileName = "shutdown.exe"
                    ps.Arguments = "-r -t 1"
                    Process.Start(ps)
                    GetOperateActionInfo(ModelKey.ErrorRepairRestar, ActionDataType.Click, FunctionSucceed.Succeed)     '重启电脑成功
                Else
                    GetOperateActionInfo(ModelKey.ErrorRepairRestar, ActionDataType.Click, FunctionSucceed.Failure)     '取消重启电脑
                End If
            Else
                GetOperateActionInfo(ModelKey.ErrorRepair, ActionDataType.Download, FunctionSucceed.Failure)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Helper_DoRepair")
        End Try

DoExit:
        PostProtobuf()
    End Sub

    Private Sub PostProtobuf()
        Try
            ''提交数据
            ActionCollectDB.GetInstance(ActionCollectType.Helper).SendDataToServer(0, True, My.Application.Info.Version.ToString(4))
            ActionCollectDB.GetInstance(ActionCollectType.Helper).SaveDataTable()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Helper_OperatePost")
        End Try
    End Sub

    Private Function MakeDownloadUrlName() As String
        Common.LogException("<<开始根据操作系统生成下载链接")

        Dim strUrl As String = "tb-Windows{0}-KB3097877-{1}.msu"

        Dim strVer As String = ""
        Dim oType As OSType = OSHelper.OSType
        Common.LogException("<<生成的操作系统类型：" & oType.ToString)

        If oType = OSType.Vista Then
            strVer = "6.0"
        ElseIf oType = OSType.Win7 Then
            strVer = "6.1"
        ElseIf oType = OSType.Win8 Then
            strVer = "8-RT"
        ElseIf oType = OSType.Win10 Then
            strVer = "8.1"
        End If
        If strVer.Length = 0 Then
            Return String.Empty
        End If

        Dim strOSBit As String = GetOSBit()
        Common.LogException("<<生成的操作系统位数：" & oType.ToString)
        If strOSBit.Length = 0 Then
            Return String.Empty
        End If

        strUrl = String.Format(strUrl, strVer, strOSBit)

        Return strUrl
    End Function

    Private Function GetOSBit() As String
        Dim strBit As String = "x64"
        Dim mc As Management.ManagementClass = New Management.ManagementClass("Win32_ComputerSystem")
        Dim moc As Management.ManagementObjectCollection = mc.GetInstances()
        Try
            For Each mo As Management.ManagementObject In moc
                Try
                    Dim strTemp As String = mo("SystemType").ToString()
                    If strTemp.ToLower().StartsWith("x64") Then
                        strBit = "x64"
                    ElseIf strTemp.ToLower().StartsWith("x32") Then
                        strBit = "x32"
                    Else
                        strBit = "ia32"
                    End If
                Catch ex As Exception
                    Continue For
                End Try
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetOSBit")
            strBit = ""
        End Try

        moc = Nothing
        mc = Nothing
        Return strBit
    End Function

    Private Function CheckPatchExist(ByVal strKey As String) As Boolean
        Dim isExist As Boolean = False

        Try
            Dim searchOs As New Management.ManagementObjectSearcher("Select * from Win32_QuickFixEngineering")
            If searchOs Is Nothing Then
                GoTo DoExit
            End If

            For Each item As System.Management.ManagementObject In searchOs.Get()
                If item Is Nothing Then
                    Continue For
                End If

                For Each itemPro As System.Management.PropertyData In item.Properties
                    'Debug.Print(itemPro.Name & ", " & itemPro.Value)
                    If itemPro.Name Is Nothing OrElse itemPro.Value Is Nothing Then
                        Continue For
                    End If

                    If String.Compare(itemPro.Name, "HotFixID", True) = 0 AndAlso String.Compare(itemPro.Value.ToString(), strKey, True) = 0 Then
                        isExist = True
                        GoTo DoExit
                    End If
                Next
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString, "CheckPatchExist")
        End Try

DoExit:
        Return isExist
    End Function

    Private Sub GetOperateActionInfo(ByVal model As ModelKey, ByVal dataType As ActionDataType, _
                                          Optional ByVal Succeed As FunctionSucceed = FunctionSucceed.None, _
                                          Optional ByVal errMsg As String = "")
        Dim data As New OperateActionInfo()

        Try
            data.fromtype = "0"
            data.deviceid = "0"
            data.sourceModel = "0"
            data.model = CType(model, Integer).ToString()
            data.dataType = CType(dataType, Integer).ToString()
            data.operateTime = Now.ToString("yyyy-MM-dd HH:mm:ss")
            data.PhoneNumberDes = ""
            data.SerialNumber = ""
            data.modelIndex = "0"
            data.businessId = "0"
            data.userId = "0"
            data.searchKey = ""
            data.devicemodel = ""
            data.osversion = ""

            Dim strSucceed As String = "0"
            Select Case Succeed
                Case FunctionSucceed.Succeed
                    strSucceed = "1"
                Case FunctionSucceed.Failure
                    strSucceed = "2"
            End Select

            data.succeed = strSucceed
            '如果为空写入一个空串 服务才可以识别。
            If errMsg.Trim.Length = 0 Then
                errMsg = " "
            End If
            data.errormessage = errMsg
            data.memo = " "
        Catch ex As Exception
            Common.LogException(ex.ToString, "Helper_GetOperateActionInfo")
        End Try

        Try
            Dim jsObj As JsonObject = ActionCollectProtobuf.GetJsonObject(data)
            Dim arrJson As New JsonArray
            arrJson.Add(jsObj)
            Dim strJson As String = ActionCollectProtobuf.GetJsonString(arrJson)
            Common.LogTest("Helper:" & strJson)
            ActionCollectDB.GetInstance(ActionCollectType.Helper).AddRow(strJson)
        Catch ex As Exception
            Common.LogException(ex.ToString, "Helper_GetOperateActionInfoJson")
        End Try
    End Sub

    Private Function IsErrorPC() As Boolean
        Dim blnReturn As Boolean = False
        Dim blnInstall As Boolean = CheckPatchExist("KB3097877")
        If blnInstall = False Then
            Return blnReturn
        End If

        Dim strSearchFolder As String = GetWinPackagesPath()
        If strSearchFolder.Length = 0 Then
            Return blnReturn
        End If

        Common.LogException("<<CheckIsErrorPC：" & strSearchFolder)

        Dim is61 As Boolean = False
        Dim is62 As Boolean = False
        Try
            If Not Directory.Exists(strSearchFolder) Then
                Return blnReturn
            End If

            Dim arrFiles As String() = Directory.GetFiles(strSearchFolder, "*KB3097877*6.1.1*.cat")
            If arrFiles Is Nothing OrElse arrFiles.Length = 0 Then
                Return blnReturn
            End If

            For Each strFile As String In arrFiles
                If strFile.Contains("6.1.1.1") Then
                    is61 = True
                End If
                If strFile.Contains("6.1.1.2") Then
                    is62 = True
                End If
            Next
            If is61 AndAlso Not is62 Then
                blnReturn = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Helper_IsErrorPC")
        End Try
        Return blnReturn
    End Function

    Private Function GetWinPackagesPath() As String
        Dim strSearchFolder As String = ""
        Try
            Dim strSystemDirectory As String = System.Environment.SystemDirectory
            strSearchFolder = Path.GetDirectoryName(strSystemDirectory)
            strSearchFolder = Path.Combine(strSearchFolder, "servicing\Packages")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Helper_GetWinPackagesPath")
        End Try
        Return strSearchFolder
    End Function

#End Region

End Module
