﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{898BA758-8E2B-4108-B020-B9444EDCB8CF}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>iTunesUninstaller.My.MyApplication</StartupObject>
    <RootNamespace>iTunesUninstaller</RootNamespace>
    <AssemblyName>iTunesUninstaller</AssemblyName>
    <MyType>WindowsForms</MyType>
    <ApplicationIcon>Resources\iTong.ico</ApplicationIcon>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>2.0</OldToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Release\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="iTong.CoreFoundation" />
    <Import Include="iTong.CoreReses" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\GuiHelper.vb" />
    <Compile Include="Common\GuiResource.vb">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Common\GuiStructure.vb" />
    <Compile Include="Common\NativeMethods.vb" />
    <Compile Include="Language.vb" />
    <Compile Include="MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Include="MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="tbControlBar.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="tbControls.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="tbForm\tbBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="tbForm\tbBaseForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="tbForm\tbBaseGuiForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Uninstaller\AppleAppUnistallInfo.vb" />
    <Compile Include="Uninstaller\ToolHelper32.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="MainForm.resx">
      <SubType>Designer</SubType>
      <DependentUpon>MainForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\app_downloads_background.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\app_downloads_value.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_close.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\frm_bg_blank.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\iTong.ico" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\player_progress_bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\player_progress_bg_H.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\player_progress_play.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\player_progress_play_H.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\player_progress_dot.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting001.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting002.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting003.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting004.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting005.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting006.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting007.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting008.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\icon_itunesuninstall.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\icon_itunes.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_white.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CoreMisc\CoreMisc.csproj">
      <Project>{685ae51b-3c37-4b37-b3a2-b485d07a6e6b}</Project>
      <Name>CoreMisc</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses.csproj">
      <Project>{65CE9103-521A-49E6-A8CD-89137B452842}</Project>
      <Name>CoreReses</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS.csproj">
      <Project>{61c62ea1-6ad7-41be-98db-8abb77ee35f3}</Project>
      <Name>CoreUtilCS</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>