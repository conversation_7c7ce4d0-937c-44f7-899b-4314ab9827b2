﻿Imports System.Drawing
Imports System.Drawing.Imaging
Imports System.Drawing.Drawing2D

Public Class GuiHelper

    Private Shared mDictCacheBrush As New Dictionary(Of Color, SolidBrush)
    Private Shared mDictCachePen As New Dictionary(Of Color, Pen)
    Private Shared mDictCacheImageEmpty As New Dictionary(Of Size, Image)
    Private Shared mDictCacheImageDisabled As New Dictionary(Of Image, Image)

    Public Shared Function RoundRect(ByVal r As RectangleF, ByVal r1 As Single, ByVal r2 As Single, ByVal r3 As Single, ByVal r4 As Single) As GraphicsPath
        Dim x As Single = r.X, y As Single = r.Y, w As Single = r.Width, h As Single = r.Height
        Dim rr As New GraphicsPath()
        rr.AddBezier(x, y + r1, x, y, x + r1, y, _
        x + r1, y)
        rr.AddLine(x + r1, y, x + w - r2, y)
        rr.AddBezier(x + w - r2, y, x + w, y, x + w, y + r2, _
        x + w, y + r2)
        rr.AddLine(x + w, y + r2, x + w, y + h - r3)
        rr.AddBezier(x + w, y + h - r3, x + w, y + h, x + w - r3, y + h, _
        x + w - r3, y + h)
        rr.AddLine(x + w - r3, y + h, x + r4, y + h)
        rr.AddBezier(x + r4, y + h, x, y + h, x, y + h - r4, _
        x, y + h - r4)
        rr.AddLine(x, y + h - r4, x, y + r1)
        Return rr
    End Function


    Public Shared Function GetCachemageDisabled(ByVal srcImage As Image) As Image
        Dim desImage As Image = Nothing

        If mDictCacheImageDisabled.ContainsKey(srcImage) Then
            desImage = mDictCacheImageDisabled(srcImage)
        Else
            desImage = CreateDisabledImage(srcImage)
            mDictCacheImageDisabled.Add(srcImage, desImage)
        End If

        Return desImage
    End Function

    Public Shared Function GetCacheBrush(ByVal color As Color) As SolidBrush
        Dim brush As SolidBrush = Nothing

        If mDictCacheBrush.ContainsKey(color) Then
            brush = mDictCacheBrush(color)
        Else
            brush = New SolidBrush(color)
            mDictCacheBrush.Add(color, brush)
        End If

        Return brush
    End Function

    Public Shared Function GetCachePen(ByVal color As Color) As Pen
        Dim p As Pen = Nothing

        If mDictCachePen.ContainsKey(color) Then
            p = mDictCachePen(color)
        Else
            p = New Pen(color)
            mDictCachePen.Add(color, p)
        End If

        Return p
    End Function

    Public Shared Function CreateDisabledImage(ByVal aImage As Image) As Image
        If aImage Is Nothing Then
            Return aImage
        End If

        Dim disabledImage As Image = New Bitmap(aImage.Width, aImage.Height, PixelFormat.Format32bppArgb)
        Dim g As Graphics = Graphics.FromImage(disabledImage)
        ControlPaint.DrawImageDisabled(g, aImage, 0, 0, Color.White)
        g.Dispose()

        Return disabledImage
    End Function

    Public Shared Sub DrawImage(ByVal g As Graphics, _
                             ByVal clientRect As Rectangle, _
                             ByVal split As tbSplitStructure, _
                             ByVal bgImage As Image)

        DrawImage(g, clientRect, split, bgImage, True, MouseStates.MouseLeave, ImageState.OneState, ImageLayout.Horizontal)
    End Sub

    Public Shared Sub DrawImage(ByVal g As Graphics, _
                                 ByVal clientRect As Rectangle, _
                                 ByVal split As tbSplitStructure, _
                                 ByVal bgImage As Image, _
                                 ByVal enable As Boolean)

        DrawImage(g, clientRect, split, bgImage, enable, MouseStates.MouseLeave, ImageState.OneState, ImageLayout.Horizontal)
    End Sub

    Public Shared Sub DrawImage(ByVal g As Graphics, _
                                 ByVal clientRect As Rectangle, _
                                 ByVal split As tbSplitStructure, _
                                 ByVal bgImage As Image, _
                                 ByVal enable As Boolean, _
                                 ByVal currentState As MouseStates, _
                                 ByVal totalState As ImageState)

        DrawImage(g, clientRect, split, bgImage, enable, currentState, totalState, ImageLayout.Horizontal)
    End Sub

    Public Shared Sub DrawImage(ByVal g As Graphics, _
                                     ByVal clientRect As Rectangle, _
                                     ByVal split As tbSplitStructure, _
                                     ByVal srcImage As Image, _
                                     ByVal enable As Boolean, _
                                     ByVal currentState As MouseStates, _
                                     ByVal totalState As ImageState, _
                                     ByVal imgLayout As ImageLayout)

        If srcImage Is Nothing Then
            Return
        End If

        Dim intIndex As Integer = CInt(currentState)
        Dim intTotal As Integer = CInt(totalState)
        Dim img As Image = srcImage

        If Not enable Then
            Select Case totalState
                Case ImageState.FourState
                    intIndex = 3
                Case ImageState.ThreeState
                    intIndex = 2
                Case ImageState.TwoState
                    intIndex = 1
                Case ImageState.OneState
                    intIndex = 0
            End Select
        End If

        If intIndex >= intTotal Then
            intIndex = intTotal - 1
        End If

        Dim indexX As Integer = 0
        Dim indexY As Integer = 0
        Dim countX As Integer = 1
        Dim countY As Integer = 1

        If imgLayout = ImageLayout.Horizontal Then
            indexX = intIndex
            countX = intTotal
        Else
            indexY = intIndex
            countY = intTotal
        End If

        Dim imgWidth As Integer = img.Width \ countX
        Dim imgHeight As Integer = img.Height \ countY
        Dim imgX As Integer = imgWidth * indexX
        Dim imgY As Integer = imgHeight * indexY

        If CInt(totalState) < 4 AndAlso Not enable Then
            img = GetCachemageDisabled(srcImage)
        End If

        '====== 绘制背景 ========================================================================================
        If split.IsEmpty Then
            g.DrawImage(img, clientRect, _
                                 New Rectangle(imgX, imgY, imgWidth, imgHeight), GraphicsUnit.Pixel)

        ElseIf split.WidthLeft = 0 OrElse split.WidthRight = 0 Then
            '如果背景图切割数据中，任意一个宽度为 0 ，则只绘制 Height 数据，用在
            '
            '   +----------------------------+
            '   |                            |
            '   |----------------------------|
            '   |                            |
            '   |----------------------------|
            '   |                            |
            '   +----------------------------+
            '
            '这样的图形切割中
            If clientRect.Height < split.HeightTop Then
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y, clientRect.Width, clientRect.Height), _
                                     New Rectangle(imgX, imgY, imgY + imgWidth, clientRect.Height), GraphicsUnit.Pixel)
            Else
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y, clientRect.Width, split.HeightTop), _
                                     New Rectangle(imgX, imgY, imgY + imgWidth, split.HeightTop), GraphicsUnit.Pixel)
            End If

            If clientRect.Height >= split.HeightVertical Then
                Dim tmpHeight As Integer = imgHeight - split.HeightVertical
                If tmpHeight > clientRect.Height - split.HeightVertical Then
                    tmpHeight = clientRect.Height - split.HeightVertical
                End If
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y + split.HeightTop, clientRect.Width, clientRect.Height - split.HeightVertical), _
                                     New Rectangle(imgX, imgY + split.HeightTop, imgWidth, tmpHeight), GraphicsUnit.Pixel)

                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y + clientRect.Height - split.HeightBottom, clientRect.Width, split.HeightBottom), _
                                     New Rectangle(imgX, imgY + imgHeight - split.HeightBottom, imgWidth, split.HeightBottom), GraphicsUnit.Pixel)

            ElseIf clientRect.Height > split.HeightTop Then
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y + split.HeightTop, clientRect.Width, clientRect.Height - split.HeightTop), _
                                     New Rectangle(imgX, imgY + imgHeight - split.HeightBottom, imgWidth, clientRect.Height - split.HeightTop), GraphicsUnit.Pixel)

            End If


        ElseIf split.HeightTop = 0 OrElse split.HeightBottom = 0 Then
            '如果背景图切割数据中，任意一个高度为 0 ，则只绘制 Width 数据，用在
            '
            '   +----------------------------+
            '   |        |         |         |
            '   |        |         |         |
            '   |        |         |         |
            '   |        |         |         |
            '   |        |         |         |
            '   +----------------------------+
            '
            '这样的图形切割中
            If clientRect.Width < split.WidthLeft Then
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y, clientRect.Width, clientRect.Height), _
                     New Rectangle(imgX, imgY, clientRect.Width, imgHeight), GraphicsUnit.Pixel)
            Else
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y, split.WidthLeft, clientRect.Height), _
                     New Rectangle(imgX, imgY, split.WidthLeft, imgHeight), GraphicsUnit.Pixel)
            End If

            If clientRect.Width >= split.WidthHorizontal Then
                Dim tmpWidth As Integer = imgWidth - split.WidthHorizontal
                If tmpWidth > clientRect.Width - split.WidthHorizontal Then
                    tmpWidth = clientRect.Width - split.WidthHorizontal
                End If
                g.DrawImage(img, New Rectangle(clientRect.X + split.WidthLeft, clientRect.Y, clientRect.Width - split.WidthHorizontal, clientRect.Height), _
                                     New Rectangle(imgX + split.WidthLeft, imgY, tmpWidth, imgHeight), GraphicsUnit.Pixel)

                g.DrawImage(img, New Rectangle(clientRect.X + clientRect.Width - split.WidthRight, clientRect.Y, split.WidthRight, clientRect.Height), _
                                     New Rectangle(imgX + imgWidth - split.WidthRight, imgY, split.WidthRight, imgHeight), GraphicsUnit.Pixel)

            ElseIf clientRect.Width > split.WidthLeft Then
                g.DrawImage(img, New Rectangle(clientRect.X + split.WidthLeft, clientRect.Y, clientRect.Width - split.WidthLeft, clientRect.Height), _
                                     New Rectangle(imgX + imgWidth - split.WidthRight, imgY, clientRect.Width - split.WidthLeft, imgHeight), GraphicsUnit.Pixel)



            End If

        Else
            '用于 九宫格 切割
            '   | Width 1 |       | Width 2 |
            '   +---------------------------+ <-----------
            '   |         |       |         |    Height 1
            '   |---------------------------| <-----------
            '   |         |       |         |
            '   |---------------------------| <-----------
            '   |         |       |         |    Height 2
            '   +---------------------------+ <-----------

            If clientRect.Width < split.WidthLeft Then
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y, split.WidthLeft, split.HeightTop), _
                                     New Rectangle(imgX, imgY, clientRect.Width, split.HeightTop), GraphicsUnit.Pixel)
            Else
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y, split.WidthLeft, split.HeightTop), _
                                     New Rectangle(imgX, imgY, split.WidthLeft, split.HeightTop), GraphicsUnit.Pixel)
            End If

            If clientRect.Width >= split.WidthHorizontal Then
                Dim tmpWidth As Integer = imgWidth - split.WidthHorizontal
                If tmpWidth > clientRect.Width - split.WidthHorizontal Then
                    tmpWidth = clientRect.Width - split.WidthHorizontal
                End If
                g.DrawImage(img, New Rectangle(clientRect.X + split.WidthLeft, clientRect.Y, clientRect.Width - split.WidthHorizontal, split.HeightTop), _
                                     New Rectangle(imgX + split.WidthLeft, imgY, tmpWidth, split.HeightTop), GraphicsUnit.Pixel)

                g.DrawImage(img, New Rectangle(clientRect.X + clientRect.Width - split.WidthRight, clientRect.Y, split.WidthRight, split.HeightTop), _
                                     New Rectangle(imgX + imgWidth - split.WidthRight, imgY, split.WidthRight, split.HeightTop), GraphicsUnit.Pixel)

            ElseIf clientRect.Width > split.WidthLeft Then
                g.DrawImage(img, New Rectangle(clientRect.X + split.WidthLeft, clientRect.Y, clientRect.Width - split.WidthLeft, split.HeightTop), _
                                     New Rectangle(imgX + split.WidthLeft, imgY, clientRect.Width - split.WidthLeft, split.HeightTop), GraphicsUnit.Pixel)

            End If

            '-----------------------------------------------------------------------------------------------

            Dim tmpHeight As Integer = imgHeight - split.HeightVertical
            If tmpHeight > clientRect.Height - split.HeightVertical Then
                tmpHeight = clientRect.Height - split.HeightVertical
            End If

            If clientRect.Width < split.WidthLeft Then
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y + split.HeightTop, split.WidthLeft, clientRect.Height - split.HeightVertical), _
                                    New Rectangle(imgX, imgY + split.HeightTop, clientRect.Width, tmpHeight), GraphicsUnit.Pixel)

            Else
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y + split.HeightTop, split.WidthLeft, clientRect.Height - split.HeightVertical), _
                                     New Rectangle(imgX, imgY + split.HeightTop, split.WidthLeft, tmpHeight), GraphicsUnit.Pixel)

            End If

            If clientRect.Width >= split.WidthHorizontal Then
                Dim tmpWidth As Integer = imgWidth - split.WidthHorizontal
                If tmpWidth > clientRect.Width - split.WidthHorizontal Then
                    tmpWidth = clientRect.Width - split.WidthHorizontal
                End If
                g.DrawImage(img, New Rectangle(clientRect.X + split.WidthLeft, clientRect.Y + split.HeightTop, clientRect.Width - split.WidthHorizontal, clientRect.Height - split.HeightVertical), _
                                     New Rectangle(imgX + split.WidthLeft, imgY + split.HeightTop, tmpWidth, tmpHeight), GraphicsUnit.Pixel)

                g.DrawImage(img, New Rectangle(clientRect.X + clientRect.Width - split.WidthRight, clientRect.Y + split.HeightTop, split.WidthRight, clientRect.Height - split.HeightVertical), _
                                     New Rectangle(imgX + imgWidth - split.WidthRight, imgY + split.HeightTop, split.WidthRight, tmpHeight), GraphicsUnit.Pixel)

            ElseIf clientRect.Width > split.WidthLeft Then
                g.DrawImage(img, New Rectangle(clientRect.X + split.WidthLeft, clientRect.Y + split.HeightTop, clientRect.Width - split.WidthLeft, clientRect.Height - split.HeightVertical), _
                                     New Rectangle(imgX + split.WidthLeft, imgY + split.HeightTop, clientRect.Width - split.WidthLeft, tmpHeight), GraphicsUnit.Pixel)

            End If

            '-----------------------------------------------------------------------------------------------

            If clientRect.Width < split.WidthLeft Then
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y + clientRect.Height - split.HeightBottom, split.WidthLeft, split.HeightBottom), _
                                    New Rectangle(imgX, imgY + imgHeight - split.HeightBottom, clientRect.Width, split.HeightBottom), GraphicsUnit.Pixel)

            Else
                g.DrawImage(img, New Rectangle(clientRect.X, clientRect.Y + clientRect.Height - split.HeightBottom, split.WidthLeft, split.HeightBottom), _
                                     New Rectangle(imgX, imgY + imgHeight - split.HeightBottom, split.WidthLeft, split.HeightBottom), GraphicsUnit.Pixel)

            End If

            If clientRect.Width >= split.WidthHorizontal Then
                Dim tmpWidth As Integer = imgWidth - split.WidthHorizontal
                If tmpWidth > clientRect.Width - split.WidthHorizontal Then
                    tmpWidth = clientRect.Width - split.WidthHorizontal
                End If
                g.DrawImage(img, New Rectangle(clientRect.X + split.WidthLeft, clientRect.Y + clientRect.Height - split.HeightBottom, clientRect.Width - split.WidthHorizontal, split.HeightBottom), _
                                     New Rectangle(imgX + split.WidthLeft, imgY + imgHeight - split.HeightBottom, tmpWidth, split.HeightBottom), GraphicsUnit.Pixel)

                g.DrawImage(img, New Rectangle(clientRect.X + clientRect.Width - split.WidthRight, clientRect.Y + clientRect.Height - split.HeightBottom, split.WidthRight, split.HeightBottom), _
                                     New Rectangle(imgX + imgWidth - split.WidthRight, imgY + imgHeight - split.HeightBottom, split.WidthRight, split.HeightBottom), GraphicsUnit.Pixel)

            ElseIf clientRect.Width > split.WidthLeft Then
                g.DrawImage(img, New Rectangle(clientRect.X + split.WidthLeft, clientRect.Y + clientRect.Height - split.HeightBottom, clientRect.Width - split.WidthLeft, split.HeightBottom), _
                                     New Rectangle(imgX + split.WidthLeft, imgY + imgHeight - split.HeightBottom, clientRect.Width - split.WidthLeft, split.HeightBottom), GraphicsUnit.Pixel)

            End If
        End If



    End Sub

    Public Shared Sub DrawText(ByVal g As Graphics, _
                           ByVal aText As String, _
                           ByVal aFont As Font, _
                           ByVal aRect As Rectangle, _
                           ByVal aForeColor As Color, _
                           ByVal aBackColor As Color, _
                           ByVal aAlign As ContentAlignment)

        DrawText(g, aText, aFont, aRect, aForeColor, aBackColor, aAlign, True, False, True)
    End Sub

    Public Shared Sub DrawText(ByVal g As Graphics, _
                       ByVal aText As String, _
                       ByVal aFont As Font, _
                       ByVal aRect As Rectangle, _
                       ByVal aForeColor As Color, _
                       ByVal aBackColor As Color, _
                       ByVal aAlign As ContentAlignment, _
                       ByVal aEnabled As Boolean)

        DrawText(g, aText, aFont, aRect, aForeColor, aBackColor, aAlign, aEnabled, False, True)
    End Sub

    Public Shared Sub DrawText(ByVal g As Graphics, _
                           ByVal aText As String, _
                           ByVal aFont As Font, _
                           ByVal aRect As Rectangle, _
                           ByVal aForeColor As Color, _
                           ByVal aBackColor As Color, _
                           ByVal aAlign As ContentAlignment, _
                           ByVal aEnabled As Boolean, _
                           ByVal blnEndEllipsis As Boolean)

        DrawText(g, aText, aFont, aRect, aForeColor, aBackColor, aAlign, aEnabled, blnEndEllipsis, True)
    End Sub

    Public Shared Sub DrawText(ByVal g As Graphics, _
                           ByVal aText As String, _
                           ByVal aFont As Font, _
                           ByVal aRect As Rectangle, _
                           ByVal aForeColor As Color, _
                           ByVal aBackColor As Color, _
                           ByVal aAlign As ContentAlignment, _
                           ByVal aEnabled As Boolean, _
                           ByVal blnEndEllipsis As Boolean, _
                           ByVal blnSingleLine As Boolean)

        DrawText(g, aText, aFont, aRect, aForeColor, aBackColor, aAlign, aEnabled, blnEndEllipsis, blnSingleLine, False)
    End Sub

    Public Shared Sub DrawText(ByVal g As Graphics, _
                               ByVal aText As String, _
                               ByVal aFont As Font, _
                               ByVal aRect As Rectangle, _
                               ByVal aForeColor As Color, _
                               ByVal aBackColor As Color, _
                               ByVal aAlign As ContentAlignment, _
                               ByVal aEnabled As Boolean, _
                               ByVal blnEndEllipsis As Boolean, _
                               ByVal blnSingleLine As Boolean, _
                               ByVal blnNoPrefix As Boolean)

        'AirDroid 按钮不可用的时候，字体颜色不变，这个和iTong的规则不一样，所以过滤掉
        If Not aEnabled Then
            If aForeColor = SystemColors.Control Then
                aForeColor = ControlPaint.Dark(SystemColors.Control)
            ElseIf aForeColor = Color.White Then '
                aForeColor = Color.FromArgb(141, 186, 236)
            ElseIf aForeColor = Color.Black Then
                aForeColor = Color.Gray
            Else
                aForeColor = SystemColors.ControlDark
            End If
        End If

        'If aText.Contains("11111") Then
        '    Stop
        'End If

        Dim flags As TextFormatFlags = GuiHelper.CreateTextFormatFlags(aAlign, blnEndEllipsis, blnNoPrefix, blnSingleLine)
        TextRenderer.DrawText(g, aText, aFont, aRect, aForeColor, aBackColor, flags)
    End Sub

    Public Shared Sub DrawString(ByVal g As Graphics, _
                       ByVal aText As String, _
                       ByVal aFont As Font, _
                       ByVal aRect As RectangleF, _
                       ByVal aForeColor As Color, _
                       ByVal aAlign As ContentAlignment)

        DrawString(g, aText, aFont, aRect, aForeColor, aAlign, True, False)
    End Sub

    Public Shared Sub DrawString(ByVal g As Graphics, _
                       ByVal aText As String, _
                       ByVal aFont As Font, _
                       ByVal aRect As RectangleF, _
                       ByVal aForeColor As Color, _
                       ByVal aAlign As ContentAlignment, _
                       ByVal aEnabled As Boolean)

        DrawString(g, aText, aFont, aRect, aForeColor, aAlign, aEnabled, False)
    End Sub

    Public Shared Sub DrawString(ByVal g As Graphics, _
                               ByVal aText As String, _
                               ByVal aFont As Font, _
                               ByVal aRect As RectangleF, _
                               ByVal aForeColor As Color, _
                               ByVal aAlign As ContentAlignment, _
                               ByVal aEnabled As Boolean, _
                               ByVal noWrap As Boolean)

        Dim strFormat As StringFormat = CreateStringFormat(aAlign, noWrap)

        'AirDroid 按钮不可用的时候，字体颜色不变，这个和iTong的规则不一样，所以过滤掉
        If Not aEnabled Then
            If aForeColor = SystemColors.Control Then
                aForeColor = ControlPaint.Dark(SystemColors.Control)
            ElseIf aForeColor = Color.White Then '
                aForeColor = Color.FromArgb(141, 186, 236)
            ElseIf aForeColor = Color.Black Then
                aForeColor = Color.Gray
            Else
                aForeColor = SystemColors.ControlDark
            End If
        End If

        Dim charactersFitted As Integer
        Dim linesFilled As Integer
        Dim layoutSize As New SizeF(aRect.Width, aRect.Height)
        Dim sizeCommment As SizeF = g.MeasureString(aText, aFont, layoutSize, strFormat, charactersFitted, linesFilled)

        Dim strText As String = aText
        If charactersFitted > aText.Length AndAlso charactersFitted <> aText.Length AndAlso aText.Length > 3 Then
            strText = aText.Substring(0, charactersFitted - 3) & "..."
        End If

        g.DrawString(strText, aFont, GetCacheBrush(aForeColor), aRect, strFormat)
    End Sub

    Public Shared Function GetMouseStateImage(ByVal sourceImage As Image, ByVal imageIndex As Integer, Optional ByVal maxStateCount As Integer = 3) As Image

        Dim imgBlank As System.Drawing.Image = Nothing

        If sourceImage IsNot Nothing AndAlso imageIndex >= 0 Then
            imgBlank = New Bitmap(CInt(sourceImage.Width / maxStateCount), sourceImage.Height)

            Using drwPaint As System.Drawing.Graphics = System.Drawing.Graphics.FromImage(imgBlank)

                drwPaint.DrawImage(sourceImage, New Rectangle(0, 0, imgBlank.Width, imgBlank.Height), _
                                                New Rectangle(imgBlank.Width * imageIndex, 0, imgBlank.Width, imgBlank.Height), _
                                                GraphicsUnit.Pixel)
            End Using
        End If

        Return imgBlank
    End Function

    ''' <summary>
    ''' 创建TextFormatFlags
    ''' </summary>
    ''' <param name="align">文本对齐方式</param>
    Public Shared Function CreateTextFormatFlags(ByVal align As ContentAlignment) As TextFormatFlags
        Return CreateTextFormatFlags(align, True, True, False)
    End Function

    ''' <summary>
    ''' 创建TextFormatFlags
    ''' </summary>
    ''' <param name="align">文本对齐方式</param>
    ''' <param name="blnEndEllipsis">是否移除修整行的结尾，并以省略号进行替换</param>
    Public Shared Function CreateTextFormatFlags(ByVal align As ContentAlignment, _
                                              ByVal blnEndEllipsis As Boolean) As TextFormatFlags

        Return CreateTextFormatFlags(align, blnEndEllipsis, True, False)
    End Function

    ''' <summary>
    ''' 创建TextFormatFlags
    ''' </summary>
    ''' <param name="align">文本对齐方式</param>
    ''' <param name="blnEndEllipsis">是否移除修整行的结尾，并以省略号进行替换</param>
    ''' <param name="blnNoPrefix">关闭对前缀字符的处理，助记键前缀字符被解释为为紧跟的字符添加下划线的指令</param>
    Public Shared Function CreateTextFormatFlags(ByVal align As ContentAlignment, _
                                              ByVal blnEndEllipsis As Boolean, _
                                              ByVal blnNoPrefix As Boolean) As TextFormatFlags

        Return CreateTextFormatFlags(align, blnEndEllipsis, blnNoPrefix, False)
    End Function

    ''' <summary>
    ''' 创建TextFormatFlags
    ''' </summary>
    ''' <param name="align">文本对齐方式</param>
    ''' <param name="blnEndEllipsis">是否移除修整行的结尾，并以省略号进行替换</param>
    ''' <param name="blnNoPrefix">关闭对前缀字符的处理，助记键前缀字符被解释为为紧跟的字符添加下划线的指令</param>
    ''' <param name="blnSingleLine">是否单行显示</param>
    Public Shared Function CreateTextFormatFlags(ByVal align As ContentAlignment, _
                                              ByVal blnEndEllipsis As Boolean, _
                                              ByVal blnNoPrefix As Boolean, _
                                              ByVal blnSingleLine As Boolean) As TextFormatFlags

        Dim textFlags As TextFormatFlags = TextFormatFlags.Default

        If blnEndEllipsis Then
            textFlags = textFlags Or TextFormatFlags.EndEllipsis
        End If

        If blnNoPrefix Then
            textFlags = textFlags Or TextFormatFlags.NoPrefix
        End If

        If blnSingleLine Then
            textFlags = textFlags Or TextFormatFlags.SingleLine
        End If

        Select Case align
            Case ContentAlignment.TopLeft
                textFlags = textFlags Or TextFormatFlags.Top Or TextFormatFlags.Left

            Case ContentAlignment.TopCenter
                textFlags = textFlags Or TextFormatFlags.Top Or TextFormatFlags.HorizontalCenter

            Case ContentAlignment.TopRight
                textFlags = textFlags Or TextFormatFlags.Top Or TextFormatFlags.Right

            Case ContentAlignment.MiddleLeft
                textFlags = textFlags Or TextFormatFlags.VerticalCenter Or TextFormatFlags.Left

            Case ContentAlignment.MiddleCenter
                textFlags = textFlags Or TextFormatFlags.VerticalCenter Or TextFormatFlags.HorizontalCenter

            Case ContentAlignment.MiddleRight
                textFlags = textFlags Or TextFormatFlags.VerticalCenter Or TextFormatFlags.Right

            Case ContentAlignment.BottomLeft
                textFlags = textFlags Or TextFormatFlags.Bottom Or TextFormatFlags.Left

            Case ContentAlignment.BottomCenter
                textFlags = textFlags Or TextFormatFlags.Bottom Or TextFormatFlags.HorizontalCenter

            Case ContentAlignment.BottomRight
                textFlags = textFlags Or TextFormatFlags.Bottom Or TextFormatFlags.Right

        End Select

        Return textFlags
    End Function

    Public Shared Function CreateStringFormat(ByVal align As ContentAlignment, ByVal noWrap As Boolean) As StringFormat
        Dim StrFormat As New StringFormat()

        Select Case align
            Case ContentAlignment.BottomCenter
                StrFormat.LineAlignment = StringAlignment.Far
                StrFormat.Alignment = StringAlignment.Center

            Case ContentAlignment.BottomLeft
                StrFormat.LineAlignment = StringAlignment.Far
                StrFormat.Alignment = StringAlignment.Near

            Case ContentAlignment.BottomRight
                StrFormat.LineAlignment = StringAlignment.Far
                StrFormat.Alignment = StringAlignment.Far

            Case ContentAlignment.MiddleCenter
                StrFormat.LineAlignment = StringAlignment.Center
                StrFormat.Alignment = StringAlignment.Center

            Case ContentAlignment.MiddleLeft
                StrFormat.LineAlignment = StringAlignment.Center
                StrFormat.Alignment = StringAlignment.Near

            Case ContentAlignment.MiddleRight
                StrFormat.LineAlignment = StringAlignment.Center
                StrFormat.Alignment = StringAlignment.Far

            Case ContentAlignment.TopCenter
                StrFormat.LineAlignment = StringAlignment.Near
                StrFormat.Alignment = StringAlignment.Center

            Case ContentAlignment.TopLeft
                StrFormat.LineAlignment = StringAlignment.Near
                StrFormat.Alignment = StringAlignment.Near

            Case ContentAlignment.TopRight
                StrFormat.LineAlignment = StringAlignment.Near
                StrFormat.Alignment = StringAlignment.Far

        End Select

        If noWrap Then
            StrFormat.FormatFlags = StringFormatFlags.NoWrap
        End If

        StrFormat.HotkeyPrefix = Text.HotkeyPrefix.Show

        Return StrFormat
    End Function

    Public Shared Function CalculateLocation(ByVal rect As Rectangle, ByVal textSize As SizeF, ByVal align As ContentAlignment) As PointF
        Return CalculateLocation(rect, textSize, align, New Padding(0), Size.Empty, 0)
    End Function

    Public Shared Function CalculateLocation(ByVal rect As Rectangle, ByVal textSize As SizeF, ByVal align As ContentAlignment, ByVal padding As Padding) As PointF
        Return CalculateLocation(rect, textSize, align, padding, Size.Empty, 0)
    End Function

    Public Shared Function CalculateLocation(ByVal rect As Rectangle, ByVal cSize As SizeF, ByVal align As ContentAlignment, ByVal padding As Padding, ByVal textSize As Size, ByVal iconPlaceText As Integer) As PointF
        Dim intX As Single = CSng(rect.X)
        Dim intY As Single = CSng(rect.Y)

        Select Case align
            Case ContentAlignment.TopLeft
                intX += padding.Left
                intY += padding.Top

            Case ContentAlignment.TopCenter
                intX += (rect.Width - padding.Horizontal - cSize.Width) / 2.0F
                intY += padding.Top

            Case ContentAlignment.TopRight
                intX += (rect.Width - cSize.Width - padding.Right)
                intY += padding.Top

            Case ContentAlignment.MiddleLeft
                intX += padding.Left
                intY += padding.Top + (rect.Height - padding.Vertical - cSize.Height) / 2.0F

                'Case ContentAlignment.MiddleCenter
                '    intX += (rect.Width - cSize.Width) / 2.0F
                '    intY += (rect.Height - cSize.Height) / 2.0F

            Case ContentAlignment.MiddleCenter
                intX += padding.Left + textSize.Width + iconPlaceText
                intY += (rect.Height - padding.Vertical - cSize.Height) / 2.0F

            Case ContentAlignment.MiddleRight
                intX += (rect.Width - cSize.Width - padding.Right)
                intY += (rect.Height - padding.Vertical - cSize.Height) / 2.0F

            Case ContentAlignment.BottomLeft
                intX += padding.Left
                intY += (rect.Height - cSize.Height - padding.Bottom)

            Case ContentAlignment.BottomCenter
                intX += (rect.Width - padding.Horizontal - cSize.Width) / 2.0F
                intY += (rect.Height - cSize.Height - padding.Bottom)

            Case ContentAlignment.BottomRight
                intX += (rect.Width - cSize.Width - padding.Right)
                intY += (rect.Height - cSize.Height - padding.Bottom)

        End Select

        Return New Point(CInt(intX), CInt(intY))
    End Function

    Public Shared Function CalculateImageLoaction(ByVal rect As Rectangle, ByVal pos As Point, ByVal imgSize As Size, ByVal align As System.Drawing.ContentAlignment) As Point
        Dim newPos As Point = Point.Empty

        Select Case align
            Case ContentAlignment.TopLeft
                newPos = pos
            Case ContentAlignment.TopCenter
                newPos = New Point((rect.Width - imgSize.Width) \ 2, pos.Y)
            Case ContentAlignment.TopRight
                newPos = New Point(rect.Width - imgSize.Width - pos.X, pos.Y)
            Case ContentAlignment.MiddleLeft
                newPos = New Point(pos.X, (rect.Height - imgSize.Height) \ 2)
            Case ContentAlignment.MiddleCenter
                newPos = New Point((rect.Width - imgSize.Width) \ 2, (rect.Height - imgSize.Height) \ 2)
            Case ContentAlignment.MiddleRight
                newPos = New Point(rect.Width - imgSize.Width - pos.X, (rect.Height - imgSize.Height) \ 2)
            Case ContentAlignment.BottomLeft
                newPos = New Point(pos.X, rect.Height - imgSize.Height - pos.Y)
            Case ContentAlignment.BottomCenter
                newPos = New Point((rect.Width - imgSize.Width) \ 2, rect.Height - imgSize.Height - pos.Y)
            Case ContentAlignment.BottomRight
                newPos = New Point(rect.Width - imgSize.Width - pos.X, rect.Height - imgSize.Height - pos.Y)

        End Select

        Return newPos + rect.Location
    End Function

    Public Shared Function ImagesMerge(ByVal imgs As Image()) As Image
        If imgs.Length = 1 Then
            Return imgs(0)
        End If

        Dim intDifBg As Integer = 12            '外框间距
        Dim intDifPic As Integer = 8            '图片间距

        Dim totalHeight As Integer = 0
        Dim miniWidth As Integer = 0
        '取出最小的宽度
        For Each img As Image In imgs
            If miniWidth = 0 OrElse miniWidth > img.Width Then
                miniWidth = img.Width
            End If
        Next

        '计算全部图片的高度
        For i As Integer = 0 To imgs.Length - 1
            Dim intHeight As Integer = imgs(i).Height
            If imgs(i).Width <> miniWidth Then
                '宽度比最小宽度还宽的就进行等比缩放
                intHeight = CInt(Math.Truncate(CSng(imgs(i).Height) / CSng(imgs(i).Width) * miniWidth))
            End If
            totalHeight += intHeight + intDifPic
        Next

        If imgs.Length > 0 Then
            totalHeight -= intDifPic
        End If

        Dim imgB As Image = New Bitmap(miniWidth + intDifBg * 2, totalHeight + intDifBg * 2)
        Dim g As Graphics = Graphics.FromImage(imgB)
        g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic
        '填背景色
        g.FillRectangle(New SolidBrush(Color.White), New Rectangle(0, 0, miniWidth + intDifBg * 2, totalHeight + intDifBg * 2))
        '画背景
        'GuiHelper.DrawImage(g, New Rectangle(0, 0, miniWidth + intDifBg * 2, totalHeight + intDifBg * 2), New tbSplitStructure(intDifBg, intDifBg, intDifBg, intDifBg), My.Resources.weibo_merge_bg)

        Dim intY As Integer = intDifBg
        Dim intX As Integer = intDifBg

        For i As Integer = 0 To imgs.Length - 1
            Dim intHeight As Integer = imgs(i).Height
            If imgs(i).Width <> miniWidth Then
                '宽度比最小宽度还宽的就进行等比缩放
                intHeight = CInt(Math.Truncate(CSng(imgs(i).Height) / CSng(imgs(i).Width) * miniWidth))
            End If
            g.DrawImage(imgs(i), New RectangleF(intX, intY, miniWidth, intHeight), New RectangleF(0, 0, imgs(i).Width, imgs(i).Height), GraphicsUnit.Pixel)
            '下一张图片的Y点的位置
            intY += intHeight + intDifPic
        Next

        g.Dispose()

        Return imgB
    End Function

    '''根据传入的基准窗体初始化窗体位置，一般是为了子窗体居中
    Public Shared Sub IniLocation(ByVal frmParent As Form, ByVal frmShow As Form)
        If frmParent Is Nothing OrElse frmParent.IsDisposed OrElse Not frmParent.Visible OrElse frmParent.WindowState = FormWindowState.Minimized Then
            frmShow.StartPosition = FormStartPosition.CenterScreen
        Else
            frmShow.StartPosition = FormStartPosition.Manual
            frmShow.Location = New Point(frmParent.Left + (frmParent.Width - frmShow.Width) \ 2, _
                                         frmParent.Top + (frmParent.Height - frmShow.Height) \ 2)
        End If
    End Sub

End Class

#Region "--- 枚举类型 ---"

Public Enum MouseStates
    '以下枚举值不能修改，否则会影响绘图
    MouseLeave = 0
    MouseHover = 1
    MouseDown = 2
    MouseMove = 3
    MouseEnter = 4
    MouseUp = 5
    Disabled = 6
End Enum

Public Enum ImageLayout
    Horizontal
    Vertical
End Enum

Public Enum ImageState
    OneState = 1
    TwoState = 2
    ThreeState = 3
    FourState = 4
    FiveState = 5
    SixState = 6
    SevenState = 7
    EightState = 8
End Enum

Public Enum FormDock
    TopLeft
    TopCenter
    TopRight
    BottomLeft
    BottomCenter
    BottomRight
    None
End Enum

Public Enum MouseAction
    FormMove
    FormResize
    FormFocus
    None
End Enum

Public Enum MouseLoaction
    TopLeft
    Top
    TopRight
    Left
    Center
    Right
    BottomLeft
    Bottom
    BottomRight
    None
End Enum

#End Region