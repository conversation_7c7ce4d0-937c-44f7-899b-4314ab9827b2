﻿Public Structure tbSplitStructure

    Public Shared Empty As tbSplitStructure
    Shared Sub New()
        Empty = New tbSplitStructure(0)
    End Sub

    Private m_WidthLeft As Integer
    Private m_HeightTop As Integer
    Private m_WidthRight As Integer
    Private m_HeightButtom As Integer

    Public Sub New(ByVal intAll As Integer)
        m_WidthLeft = intAll
        m_HeightTop = intAll
        m_WidthRight = intAll
        m_HeightButtom = intAll
    End Sub

    Public Sub New(ByVal intWidthLeft As Integer, ByVal intHeightTop As Integer, ByVal intWidthRight As Integer, ByVal intHeightButtom As Integer)
        m_WidthLeft = intWidthLeft
        m_HeightTop = intHeightTop
        m_WidthRight = intWidthRight
        m_HeightButtom = intHeightButtom
    End Sub

    Public Sub New(ByVal PointString As String)
        Try
            Dim objPoint As String() = Split(PointString, ",")

            m_WidthLeft = CInt(objPoint(0))
            m_HeightTop = CInt(objPoint(1))
            m_WidthRight = CInt(objPoint(2))
            m_HeightButtom = CInt(objPoint(3))
        Catch ex As Exception
            m_WidthLeft = 0
            m_HeightTop = 0
            m_WidthRight = 0
            m_HeightButtom = 0
        End Try
    End Sub

    Public ReadOnly Property IsEmpty() As Boolean
        Get
            Return (Me.m_WidthLeft = 0 AndAlso Me.m_WidthRight = 0 AndAlso Me.m_HeightTop = 0 AndAlso Me.m_HeightButtom = 0)
        End Get
    End Property

    <System.ComponentModel.Browsable(False)> _
    Public ReadOnly Property GetString() As String
        Get
            'Return Me.m_WidthLeft & "," & Me.m_HeightTop & "," & Me.m_WidthRight & "," & Me.m_HeightButtom
            Return String.Format("{0},{1},{2},{3}", Me.m_WidthLeft, Me.m_HeightTop, Me.m_WidthRight, Me.m_HeightButtom)
        End Get
    End Property

    Public Property WidthLeft() As Integer
        Get
            Return m_WidthLeft
        End Get
        Set(ByVal value As Integer)
            m_WidthLeft = value
        End Set
    End Property

    Public Property HeightTop() As Integer
        Get
            Return m_HeightTop
        End Get
        Set(ByVal value As Integer)
            m_HeightTop = value
        End Set
    End Property

    Public Property WidthRight() As Integer
        Get
            Return m_WidthRight
        End Get
        Set(ByVal value As Integer)
            m_WidthRight = value
        End Set
    End Property

    Public Property HeightBottom() As Integer
        Get
            Return m_HeightButtom
        End Get
        Set(ByVal value As Integer)
            m_HeightButtom = value
        End Set
    End Property

    Public ReadOnly Property WidthHorizontal() As Integer
        Get
            Return Me.m_WidthLeft + Me.m_WidthRight
        End Get
    End Property

    Public ReadOnly Property HeightVertical() As Integer
        Get
            Return Me.m_HeightTop + Me.m_HeightButtom
        End Get
    End Property

    Public Overrides Function ToString() As String
        Return String.Concat(New String() {"Width1=", Me.WidthLeft.ToString(), "Height1=", Me.HeightTop.ToString(), _
                                           "Width2=", Me.WidthRight.ToString(), "Height2=", Me.HeightBottom.ToString()})
    End Function

End Structure