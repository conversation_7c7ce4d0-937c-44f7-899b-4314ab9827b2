﻿Imports System.Runtime.InteropServices

Public NotInheritable Class NativeMethods
    Public Const GWL_STYLE As Integer = -16
    Public Const ES_NUMBER As Integer = 8192

    Public Const MAX_TAB_STOPS As Long = 32
    Public Const PFM_LINESPACING As UInteger = &H100

    Public Shared ReadOnly [True] As IntPtr = New IntPtr(1)
    Public Shared ReadOnly [False] As IntPtr = IntPtr.Zero

    <DllImport("user32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function SendMessage(ByVal hWnd As IntPtr, ByVal Msg As Integer, ByVal wParam As Integer, ByVal lParam As Integer) As Integer
    End Function

    <DllImport("user32.dll", CharSet:=CharSet.Auto)> _
    Public Shared Function SendMessage(ByVal hWnd As HandleRef, ByVal msg As Integer, ByVal wParam As Integer, ByVal lParam As Integer) As IntPtr
    End Function

    <DllImport("user32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function SendMessageA(ByVal hWnd As IntPtr, ByVal Msg As Integer, ByVal wParam As Integer, ByVal sParam As String) As Integer
    End Function

    <DllImport("user32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function SetWindowLong(ByVal hWnd As IntPtr, ByVal nIndex As Integer, ByVal dwNewLong As Long) As Long
    End Function

    <DllImport("user32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function GetWindowLong(ByVal hWnd As IntPtr, ByVal nIndex As Integer) As Long
    End Function

    <DllImport("user32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function SetWindowRgn(ByVal hWnd As IntPtr, ByVal hRgn As IntPtr, ByVal bRedraw As Boolean) As Integer
    End Function

    <DllImport("gdi32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function CreateRoundRectRgn(ByVal rectLeft As Integer, ByVal rectTop As Integer, ByVal rectRight As Integer, ByVal rectBottom As Integer, ByVal WidthEllipse As Integer, ByVal HeighthEllipse As Integer) As IntPtr
    End Function

    <DllImport("kernel32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function SetProcessWorkingSetSize(ByVal hProcess As IntPtr, ByVal dwMinimumWorkingSetSize As Integer, ByVal dwMaximumWorkingSetSize As Integer) As Boolean
    End Function

    <DllImport("kernel32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function GetCurrentProcess() As IntPtr
    End Function

    <DllImport("user32.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Public Shared Function ShowScrollBar(ByVal hWnd As IntPtr, ByVal iBar As Integer, ByVal bShow As Integer) As Integer
    End Function

    <DllImport("User32.dll", EntryPoint:="FindWindow")> _
    Public Shared Function FindWindow(ByVal lpClassName As String, ByVal lpWindowName As String) As IntPtr
    End Function

    <DllImport("User32.dll", EntryPoint:="FindWindowEx")> _
    Public Shared Function FindWindowEx(ByVal hwndParent As IntPtr, ByVal hwndChildAfter As IntPtr, ByVal lpClassName As String, ByVal lpWindowName As String) As IntPtr
    End Function

    Public Shared Function GetLOWORD(ByVal intParam As Integer)
        Return intParam And &HFFFF
    End Function

    Public Shared Function GetHIWORD(ByVal intParam As Integer)
        Return intParam >> 16
    End Function

    <DllImportAttribute("user32.dll")> _
    Private Shared Function ShowWindow(ByVal hWnd As IntPtr, ByVal nCmdShow As Integer) As Boolean
    End Function

    Public Shared Function ShowWindow(ByVal hWnd As IntPtr, ByVal cmd As SW_CMD) As Boolean
        ShowWindow(hWnd, CInt(cmd))
    End Function

#Region "--- Scroll 滚动消息 ---"

    Public Enum ScrollBarDirection
        SB_HORZ = 0
        SB_VERT = 1
        SB_CTL = 2
        SB_BOTH = 3
    End Enum

    <StructLayout(LayoutKind.Sequential)> _
    Public Structure SCROLLINFO
        Public cbSize As Integer
        Public fMask As Integer
        Public nMin As Integer
        Public nMax As Integer
        Public nPage As Integer
        Public nPos As Integer
        Public nTrackPos As Integer
    End Structure

    Public Enum ScrollInfoMask
        SIF_RANGE = &H1
        SIF_PAGE = &H2
        SIF_POS = &H4
        SIF_DISABLENOSCROLL = &H8
        SIF_TRACKPOS = &H10
        SIF_ALL = SIF_RANGE + SIF_PAGE + SIF_POS + SIF_TRACKPOS
    End Enum

    Public Declare Function GetScrollInfo Lib "user32" (ByVal hWnd As IntPtr, ByVal fnBar As ScrollBarDirection, ByRef lpsi As SCROLLINFO) As Integer

#End Region

End Class

Public Enum SW_CMD
    SW_HIDE = 0                  '{隐藏, 并且任务栏也没有最小化图标}
    SW_SHOWNORMAL = 1            '{用最近的大小和位置显示, 激活}
    SW_SHOWMINIMIZED = 2         '{最小化, 激活}
    SW_SHOWMAXIMIZED = 3         '{最大化, 激活}
    SW_SHOWNOACTIVATE = 4        '{用最近的大小和位置显示, 不激活}
    SW_SHOW = 5                  '{同 SW_SHOWNORMAL}
    SW_MINIMIZE = 6              '{最小化, 不激活}
    SW_SHOWMINNOACTIVE = 7       '{同 SW_MINIMIZE}
    SW_SHOWNA = 8                '{同 SW_SHOW}
    SW_RESTORE = 9
    SW_SHOWDEFAULT = 10
    SW_FORCEMINIMIZE = 11
End Enum

Public Enum TB_ScrollBar
    SB_LINEUP = &H0                 '' 滚动条上下移动等消息
    SB_LINEDOWN = &H1
    SB_PAGEUP = &H2
    SB_PAGEDOWN = &H3
    SB_THUMBPOSITION = &H4

    SB_HORZ = 0
    SB_VERT = 1
End Enum

Public Enum TB_ListView
    LVM_FIRST = &H1000
    LVM_SCROLL = (LVM_FIRST + 20)   'ListView滚动消息
    LVM_SETICONSPACING = &H1035     '' ListView控件消息
End Enum

Public Enum WindowMessage As Integer
    WM_USER = &H400
    EM_GETPARAFORMAT = WM_USER + 61
    EM_SETPARAFORMAT = WM_USER + 71

    WM_SETFONT = &H30
    WM_FONTCHANGE = &H1D

    WM_NULL = &H0
    WM_CREATE = &H1
    WM_DESTROY = &H2
    WM_MOVE = &H3
    WM_SIZE = &H5
    WM_ACTIVATE = &H6
    WM_SETFOCUS = &H7
    WM_KILLFOCUS = &H8
    WM_ENABLE = &HA
    WM_SETREDRAW = &HB
    WM_SETTEXT = &HC
    WM_GETTEXT = &HD
    WM_GETTEXTLENGTH = &HE
    WM_PAINT = &HF
    WM_CLOSE = &H10

    WM_QUIT = &H12
    WM_ERASEBKGND = &H14
    WM_SYSCOLORCHANGE = &H15
    WM_SHOWWINDOW = &H18

    WM_ACTIVATEAPP = &H1C

    WM_SETCURSOR = &H20
    WM_MOUSEACTIVATE = &H21
    WM_GETMINMAXINFO = &H24
    WM_WINDOWPOSCHANGING = &H46
    WM_WINDOWPOSCHANGED = &H47

    WM_CONTEXTMENU = &H7B
    WM_STYLECHANGING = &H7C
    WM_STYLECHANGED = &H7D
    WM_DISPLAYCHANGE = &H7E
    WM_GETICON = &H7F
    WM_SETICON = &H80

    ' non client area
    WM_NCCREATE = &H81
    WM_NCDESTROY = &H82
    WM_NCCALCSIZE = &H83
    WM_NCHITTEST = &H84
    WM_NCPAINT = &H85
    WM_NCACTIVATE = &H86

    WM_GETDLGCODE = &H87

    WM_SYNCPAINT = &H88

    ' non client mouse
    WM_NCMOUSEMOVE = &HA0
    WM_NCLBUTTONDOWN = &HA1
    WM_NCLBUTTONUP = &HA2
    WM_NCLBUTTONDBLCLK = &HA3
    WM_NCRBUTTONDOWN = &HA4
    WM_NCRBUTTONUP = &HA5
    WM_NCRBUTTONDBLCLK = &HA6
    WM_NCMBUTTONDOWN = &HA7
    WM_NCMBUTTONUP = &HA8
    WM_NCMBUTTONDBLCLK = &HA9

    ' keyboard
    WM_KEYDOWN = &H100
    WM_KEYUP = &H101
    WM_CHAR = &H102

    WM_SYSCOMMAND = &H112

    ' menu
    WM_INITMENU = &H116
    WM_INITMENUPOPUP = &H117
    WM_MENUSELECT = &H11F
    WM_MENUCHAR = &H120
    WM_ENTERIDLE = &H121
    WM_MENURBUTTONUP = &H122
    WM_MENUDRAG = &H123
    WM_MENUGETOBJECT = &H124
    WM_UNINITMENUPOPUP = &H125
    WM_MENUCOMMAND = &H126

    WM_CHANGEUISTATE = &H127
    WM_UPDATEUISTATE = &H128
    WM_QUERYUISTATE = &H129

    ' mouse
    WM_MOUSEFIRST = &H200
    WM_MOUSEMOVE = &H200
    WM_LBUTTONDOWN = &H201
    WM_LBUTTONUP = &H202
    WM_LBUTTONDBLCLK = &H203
    WM_RBUTTONDOWN = &H204
    WM_RBUTTONUP = &H205
    WM_RBUTTONDBLCLK = &H206
    WM_MBUTTONDOWN = &H207
    WM_MBUTTONUP = &H208
    WM_MBUTTONDBLCLK = &H209
    WM_MOUSEWHEEL = &H20A
    WM_MOUSELAST = &H20D

    WM_PARENTNOTIFY = &H210
    WM_ENTERMENULOOP = &H211
    WM_EXITMENULOOP = &H212

    WM_NEXTMENU = &H213
    WM_SIZING = &H214
    WM_CAPTURECHANGED = &H215
    WM_MOVING = &H216

    WM_ENTERSIZEMOVE = &H231
    WM_EXITSIZEMOVE = &H232

    WM_MOUSELEAVE = &H2A3
    WM_MOUSEHOVER = &H2A1
    WM_NCMOUSEHOVER = &H2A0
    WM_NCMOUSELEAVE = &H2A2

    WM_MDIACTIVATE = &H222
    WM_HSCROLL = &H114
    WM_VSCROLL = &H115

    WM_PRINT = &H317
    WM_PRINTCLIENT = &H318

    WM_PASTE = &H302
    WM_REFLECT_NOTIFY = &H204E
End Enum

Public Enum WindowStyle As UInteger
    WS_OVERLAPPED = &H0
    WS_POPUP = &********0UI
    WS_CHILD = &*********
    WS_MINIMIZE = &*********
    WS_VISIBLE = &*********
    WS_DISABLED = &********
    WS_CLIPSIBLINGS = &********
    WS_CLIPCHILDREN = &********
    WS_MAXIMIZE = &********
    WS_CAPTION = &HC00000
    WS_BORDER = &H800000
    WS_DLGFRAME = &H400000
    WS_VSCROLL = &H200000
    WS_HSCROLL = &H100000
    WS_SYSMENU = &H80000
    WS_THICKFRAME = &H40000
    WS_GROUP = &H20000
    WS_TABSTOP = &H10000
    WS_MINIMIZEBOX = &H20000
    WS_MAXIMIZEBOX = &H10000
    WS_TILED = WS_OVERLAPPED
    WS_ICONIC = WS_MINIMIZE
    WS_SIZEBOX = WS_THICKFRAME
    WS_TILEDWINDOW = WS_OVERLAPPEDWINDOW
    WS_OVERLAPPEDWINDOW = (WS_OVERLAPPED Or WS_CAPTION Or WS_SYSMENU Or WS_THICKFRAME Or WS_MINIMIZEBOX Or WS_MAXIMIZEBOX)
    WS_POPUPWINDOW = (WS_POPUP Or WS_BORDER Or WS_SYSMENU)
    WS_CHILDWINDOW = (WS_CHILD)
End Enum

<Flags()> _
Public Enum WindowStyleEx
    WS_EX_DLGMODALFRAME = &H1
    WS_EX_NOPARENTNOTIFY = &H4
    WS_EX_TOPMOST = &H8
    WS_EX_ACCEPTFILES = &H10
    WS_EX_TRANSPARENT = &H20
    WS_EX_MDICHILD = &H40
    WS_EX_TOOLWINDOW = &H80
    WS_EX_WINDOWEDGE = &H100
    WS_EX_CLIENTEDGE = &H200
    WS_EX_CONTEXTHELP = &H400
    WS_EX_RIGHT = &H1000
    WS_EX_LEFT = &H0
    WS_EX_RTLREADING = &H2000
    WS_EX_LTRREADING = &H0
    WS_EX_LEFTSCROLLBAR = &H4000
    WS_EX_RIGHTSCROLLBAR = &H0
    WS_EX_CONTROLPARENT = &H10000
    WS_EX_STATICEDGE = &H20000
    WS_EX_APPWINDOW = &H40000
    WS_EX_OVERLAPPEDWINDOW = (WS_EX_WINDOWEDGE Or WS_EX_CLIENTEDGE)
    WS_EX_PALETTEWINDOW = (WS_EX_WINDOWEDGE Or WS_EX_TOOLWINDOW Or WS_EX_TOPMOST)
    WS_EX_LAYERED = &H80000
    WS_EX_NOINHERITLAYOUT = &H100000
    ' Disable inheritence of mirroring by children
    WS_EX_LAYOUTRTL = &H400000
    ' Right to left mirroring
    WS_EX_COMPOSITED = &********
    WS_EX_NOACTIVATE = &********
End Enum

''' <summary>
''' Location of cursor hot spot returnet in WM_NCHITTEST.
''' </summary>
Public Enum NCHITTEST
    ''' <summary>
    ''' On the screen background or on a dividing line between windows 
    ''' (same as HTNOWHERE, except that the DefWindowProc function produces a system beep to indicate an error).
    ''' </summary>
    HTERROR = -2

    ''' <summary>
    ''' In a window currently covered by another window in the same thread 
    ''' (the message will be sent to underlying windows in the same thread until one of them returns a code that is not HTTRANSPARENT).
    ''' </summary>
    HTTRANSPARENT = -1

    ''' <summary>
    ''' On the screen background or on a dividing line between windows.
    ''' </summary>
    HTNOWHERE = 0

    ''' <summary>
    '''In a client area.
    ''' </summary>
    HTCLIENT = 1

    ''' <summary>
    '''In a title bar.
    ''' </summary>
    HTCAPTION = 2

    ''' <summary>
    '''In a window menu or in a Close button in a child window.
    ''' </summary>
    HTSYSMENU = 3

    ''' <summary>
    '''In a size box (same as HTSIZE).
    ''' </summary>
    HTGROWBOX = 4

    ''' <summary>
    '''In a menu.
    ''' </summary>
    HTMENU = 5

    ''' <summary>
    '''In a horizontal scroll bar.
    ''' </summary>
    HTHSCROLL = 6

    ''' <summary>
    '''In the vertical scroll bar.
    ''' </summary>
    HTVSCROLL = 7

    ''' <summary>
    '''In a Minimize button.
    ''' </summary>
    HTMINBUTTON = 8

    ''' <summary>
    '''In a Maximize button.
    ''' </summary>
    HTMAXBUTTON = 9

    ''' <summary>
    '''In the left border of a resizable window 
    ''' (the user can click the mouse to resize the window horizontally).
    ''' </summary>
    HTLEFT = 10

    ''' <summary>
    ''' In the right border of a resizable window 
    ''' (the user can click the mouse to resize the window horizontally).
    ''' </summary>
    HTRIGHT = 11

    ''' <summary>
    '''In the upper-horizontal border of a window.
    ''' </summary>
    HTTOP = 12

    ''' <summary>
    '''In the upper-left corner of a window border.
    ''' </summary>
    HTTOPLEFT = 13

    ''' <summary>
    '''In the upper-right corner of a window border.
    ''' </summary>
    HTTOPRIGHT = 14

    ''' <summary>
    '''In the lower-horizontal border of a resizable window 
    ''' (the user can click the mouse to resize the window vertically).
    ''' </summary>
    HTBOTTOM = 15

    ''' <summary>
    '''In the lower-left corner of a border of a resizable window 
    ''' (the user can click the mouse to resize the window diagonally).
    ''' </summary>
    HTBOTTOMLEFT = 16

    ''' <summary>
    '''In the lower-right corner of a border of a resizable window 
    ''' (the user can click the mouse to resize the window diagonally).
    ''' </summary>
    HTBOTTOMRIGHT = 17

    ''' <summary>
    '''In the border of a window that does not have a sizing border.
    ''' </summary>
    HTBORDER = 18

    ''' <summary>
    '''In a object.
    ''' </summary>
    HTOBJECT = 19

    ''' <summary>
    '''In a Close button.
    ''' </summary>
    HTCLOSE = 20
    ''' <summary>
    '''In a Help button.
    ''' </summary>
    HTHELP = 21

End Enum

<StructLayout(LayoutKind.Sequential)> _
Public Structure MINMAXINFO
    Public reserved As Point
    Public maxSize As Size
    Public maxPosition As Point
    Public minTrackSize As Size
    Public maxTrackSize As Size
End Structure