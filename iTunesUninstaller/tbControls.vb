﻿'Option Strict On
'Option Explicit On

Imports System.Drawing
Imports System.Drawing.Drawing2D
Imports System.Windows.Forms
Imports System.Windows.Forms.VisualStyles
Imports System.ComponentModel
Imports System.Runtime.InteropServices

#Region "--- tbButton ---"

<System.ComponentModel.DefaultEvent("Click")> _
Public Class tbButton
    Inherits Control

    Private m_ReadOnly As Boolean = False
    Private m_ReadOnlyText As Boolean = False
    Private m_IconReadOnly As Image = Nothing

    Private m_imgMouseLeave As Image = Nothing
    Private m_imgMouseHover As Image = Nothing
    Private m_imgMouseDown As Image = Nothing
    Protected m_MouseState As MouseStates = MouseStates.MouseLeave
    Protected m_RectIcon As Rectangle = Rectangle.Empty

    Private m_Text As String = ""
    Private m_TextColor As Color = Color.White
    Private m_TextColorHover As Color = Color.White
    Private m_TextColorDown As Color = Color.White
    Private m_TextColorDisable As Color = Color.White
    Private m_TextAlign As System.Drawing.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter

    Private m_Icon As Image = Nothing
    Private m_IconState As ImageState = ImageState.OneState
    Private m_IconMouseLeave As Image = Nothing
    Private m_IconMouseHover As Image = Nothing
    Private m_IconMouseDown As Image = Nothing
    Private m_IconImageAlign As System.Drawing.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
    Private m_IconHoldPlace As Boolean = True
    Private m_IconPlaceText As Integer = 2
    Private m_ShowMoreIcon As Boolean = False
    Private m_AdriftIconWhenHover As Boolean = False

    Protected m_CloseSize As Size = Drawing.Size.Empty
    Protected m_CloseHoldPlace As Integer = 0

    Private m_ToolTip As String = ""
    Private m_ToolTipColor As Color = Color.FromArgb(122, 117, 117)
    Private m_ToolTipFont As Font = New Font(Me.Font.FontFamily, 9, FontStyle.Regular)
    Private m_ShowToolTipOnButton As Boolean = False
    Private m_TextSpaceTip As Integer = 3
    Private m_ToolTipTextAlign As System.Drawing.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter

    Private m_TextMouseDownPlace As Integer = 0
    Private m_ShowToolTip As Boolean = False
    Private m_TextToolTip As ToolTip

    Private m_ImageBackground As Image = Nothing
    Private m_ImageBackgroundState As ImageState = ImageState.OneState
    Private m_AutoSize As Boolean = True
    Private m_AutoSizeEx As Boolean = False
    Private m_Split As tbSplitStructure = tbSplitStructure.Empty     '背景图片的切割位置（Width1,Height1,Width2,Height2）


    Private m_ShadowColor As Color = Color.FromArgb(22, 74, 160)
    Private m_ShadowOffset As Point = New Point(1, 1)
    Private m_Shadow As Boolean = False
    Private m_EndEllipsis As Boolean = False

    Private m_BadgeFont As Font
    Private m_BadgeNumber As Integer = 0
    Private m_BadgeNumberOffset As Point = Point.Empty
    Private m_BindingForm As Form = Nothing
    Private m_ToolTipControl As System.Windows.Forms.ToolTip

    Private m_LastLocation As Point = Point.Empty
    Private m_LastSize As Size = System.Drawing.Size.Empty
    Private m_BadgeImage As Image = Nothing ' My.Resources.icon_badge
    Private m_ShowNew As Boolean = False
    Private m_imgNew As Image = Nothing 'My.Resources.icon_new


#Region "--- 实例化 ---"

    Public Sub New()
        Me.New(True)
    End Sub

    Public Sub New(ByVal blnSelectable As Boolean)
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint, True)
        Me.SetStyle(ControlStyles.OptimizedDoubleBuffer, True)
        Me.SetStyle(ControlStyles.SupportsTransparentBackColor, True)
        Me.SetStyle(ControlStyles.ResizeRedraw, True)
        Me.SetStyle(ControlStyles.Selectable, blnSelectable)
        Me.SetStyle(ControlStyles.UserPaint, True)
        Me.SetStyle(ControlStyles.ContainerControl, False)

        Me.UpdateStyles()

        Me.BackColor = Color.Transparent
        Me.tbAutoSize = False
        Me.tbSplit = "3,3,3,3"

        Me.Padding = New Padding(5, 2, 5, 2)
        Me.Size = New Size(80, 23)

        Try
            Me.m_BadgeFont = New Font("Arial", 9, FontStyle.Bold)
        Catch ex As Exception
            Me.m_BadgeFont = New Font("宋体", 9, FontStyle.Bold)
        End Try
    End Sub

#End Region

#Region "--- 对外属性 ---"

    Public Property Selectable() As Boolean
        Get
            Return Me.GetStyle(ControlStyles.Selectable)
        End Get
        Set(ByVal value As Boolean)
            Me.SetStyle(ControlStyles.Selectable, value)
            Me.Invalidate()
        End Set
    End Property

    Public Shadows Property Enabled() As Boolean
        Get
            Return MyBase.Enabled
        End Get
        Set(ByVal value As Boolean)
            If MyBase.Enabled <> value Then
                MyBase.Enabled = value
                Me.Invalidate()
            End If
        End Set
    End Property

    Public Shadows Property Text() As String
        Get
            Return Me.m_Text
        End Get
        Set(ByVal value As String)
            Me.m_Text = value

            '' Added by Utmost20140715
            '' 避免重复计算字体大小而调整控件大小


            If Not String.IsNullOrEmpty(Me.m_Text) Then
                Me.AdjustSize()
            End If
            Me.Invalidate()
        End Set
    End Property

    Public Property tbSplit() As String
        Get
            Return Me.m_Split.GetString
        End Get
        Set(ByVal value As String)
            Me.m_Split = New tbSplitStructure(value)
            Me.Invalidate()
        End Set
    End Property

    Public Property tbImageMouseLeave() As Image
        Get
            Return Me.m_imgMouseLeave
        End Get
        Set(ByVal value As Image)
            Me.m_imgMouseLeave = value

            Me.ReSetSize()
        End Set
    End Property

    Public Property tbImageMouseHover() As Image
        Get
            Return Me.m_imgMouseHover
        End Get
        Set(ByVal value As Image)
            Me.m_imgMouseHover = value

            Me.ReSetSize()
        End Set
    End Property

    Public Property tbImageMouseDown() As Image
        Get
            Return Me.m_imgMouseDown
        End Get
        Set(ByVal value As Image)
            Me.m_imgMouseDown = value

            Me.ReSetSize()
        End Set
    End Property

    Public Property tbEndEllipsis() As Boolean
        Get
            Return Me.m_EndEllipsis
        End Get
        Set(ByVal value As Boolean)
            Me.m_EndEllipsis = value
            Me.Invalidate()
        End Set
    End Property

    Public Shadows Property AutoSize() As Boolean
        Get
            Return Me.m_AutoSize
        End Get
        Set(ByVal value As Boolean)
            Me.m_AutoSize = value

            If Me.tbAutoSize AndAlso Me.m_ImageBackground IsNot Nothing Then
                Me.Size = Me.m_ImageBackground.Size
            End If
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("是否根据图片大小自动调整按钮大小"), System.ComponentModel.Browsable(True)> _
    Public Property tbAutoSize() As Boolean
        Get
            Return Me.AutoSize
        End Get
        Set(ByVal value As Boolean)
            Me.AutoSize = value
            Me.ReSetSize()
        End Set
    End Property

    <System.ComponentModel.Description("是否根据Icon与Text自动调整按钮大小"), System.ComponentModel.Browsable(True)> _
    Public Property tbAutoSizeEx() As Boolean
        Get
            Return Me.m_AutoSizeEx
        End Get
        Set(ByVal value As Boolean)
            Me.m_AutoSizeEx = value

            Me.AdjustSize()

            If Me.m_AutoSize = False AndAlso Me.DesignMode Then
                If Me.m_LastLocation.IsEmpty = False Then
                    Me.Location = Me.m_LastLocation
                End If
                If Me.m_LastSize.IsEmpty = False Then
                    Me.Size = Me.m_LastSize
                End If
            End If
            Me.Invalidate()
        End Set
    End Property

    Public Property tbText() As String
        Get
            Return Me.m_Text
        End Get
        Set(ByVal value As String)
            Me.m_Text = value

            '' Added by Utmost20140715
            '' 避免重复计算字体大小而调整控件大小


            If Not String.IsNullOrEmpty(Me.m_Text) Then
                Me.AdjustSize()
            End If
            Me.Invalidate()
        End Set
    End Property

    Public Property tbTextColor() As Color
        Get
            Return Me.m_TextColor
        End Get
        Set(ByVal value As Color)
            If value <> Me.m_TextColor Then
                If Me.m_TextColorDown = Me.m_TextColor Then
                    Me.m_TextColorDown = value
                End If
                If Me.m_TextColorHover = Me.m_TextColor Then
                    Me.m_TextColorHover = value
                End If
                If Me.m_TextColorDisable = Me.m_TextColor Then
                    Me.m_TextColorDisable = value
                End If
                Me.m_TextColor = value
            End If

            Me.Invalidate()
        End Set
    End Property

    Public Property tbTextColorHover() As Color
        Get
            Return Me.m_TextColorHover
        End Get
        Set(ByVal value As Color)
            Me.m_TextColorHover = value
        End Set
    End Property

    Public Property tbTextColorDown() As Color
        Get
            Return Me.m_TextColorDown
        End Get
        Set(ByVal value As Color)
            Me.m_TextColorDown = value
        End Set
    End Property

    Public Property tbTextColorDisable() As Color
        Get
            Return Me.m_TextColorDisable
        End Get
        Set(ByVal value As Color)
            Me.m_TextColorDisable = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbTextAlign() As System.Drawing.ContentAlignment
        Get
            Return Me.m_TextAlign
        End Get
        Set(ByVal value As System.Drawing.ContentAlignment)
            Me.m_TextAlign = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbTextMouseDownPlace() As Integer
        Get
            Return Me.m_TextMouseDownPlace
        End Get
        Set(ByVal value As Integer)
            Me.m_TextMouseDownPlace = value
        End Set
    End Property

    Public Property tbIconImage() As Image
        Get
            Return Me.m_Icon
        End Get
        Set(ByVal value As Image)
            Me.m_Icon = value
            Me.ReSetSize()
        End Set
    End Property

    Public Property tbIconMouseLeave() As Image
        Get
            Return Me.m_IconMouseLeave
        End Get
        Set(ByVal value As Image)
            Me.m_IconMouseLeave = value
            Me.ReSetSize()
        End Set
    End Property

    Public Property tbIconMouseHover() As Image
        Get
            Return Me.m_IconMouseHover
        End Get
        Set(ByVal value As Image)
            Me.m_IconMouseHover = value
            Me.ReSetSize()
        End Set
    End Property

    Public Property tbIconMouseDown() As Image
        Get
            Return Me.m_IconMouseDown
        End Get
        Set(ByVal value As Image)
            Me.m_IconMouseDown = value
            Me.ReSetSize()
        End Set
    End Property

    Public Property tbIconImageState() As ImageState
        Get
            Return Me.m_IconState
        End Get
        Set(ByVal value As ImageState)
            If Me.m_IconState <> value Then
                Me.m_IconState = value
                Me.ReSetSize()
            End If
        End Set
    End Property

    Public Property tbIconImageAlign() As System.Drawing.ContentAlignment
        Get
            Return Me.m_IconImageAlign
        End Get
        Set(ByVal value As System.Drawing.ContentAlignment)
            Me.m_IconImageAlign = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbIconHoldPlace() As Boolean
        Get
            Return Me.m_IconHoldPlace
        End Get
        Set(ByVal value As Boolean)
            Me.m_IconHoldPlace = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbIconPlaceText() As Integer
        Get
            Return Me.m_IconPlaceText
        End Get
        Set(ByVal value As Integer)
            Me.m_IconPlaceText = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbIconMore() As Boolean
        Get
            Return Me.m_ShowMoreIcon
        End Get
        Set(ByVal value As Boolean)
            Me.m_ShowMoreIcon = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbToolTip() As String
        Get
            Return Me.m_ToolTip
        End Get
        Set(ByVal value As String)
            Me.m_ToolTip = value

            '' Added by Utmost20140715
            '' 避免重复计算字体大小而调整控件大小


            If Not String.IsNullOrEmpty(Me.m_ToolTip) Then
                Me.AdjustSize()
                Me.SetTipText()
            End If

        End Set
    End Property

    <System.ComponentModel.Description("显示在按钮上的提示文字颜色，仅在允许在按钮上显示提示文本的时候有效")> _
    Public Property tbToolTipColor() As Color
        Get
            Return Me.m_ToolTipColor
        End Get
        Set(ByVal value As Color)
            Me.m_ToolTipColor = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("显示在按钮上的提示文字字体，仅在允许在按钮上显示提示文本的时候有效"), System.ComponentModel.Browsable(True)> _
    Public Property tbToolTipFont() As Font
        Get
            Return Me.m_ToolTipFont
        End Get
        Set(ByVal value As Font)
            Me.m_ToolTipFont = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbToolTipTextAlign() As System.Drawing.ContentAlignment
        Get
            Return Me.m_ToolTipTextAlign
        End Get
        Set(ByVal value As System.Drawing.ContentAlignment)
            Me.m_ToolTipTextAlign = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbShowToolTipOnButton() As Boolean
        Get
            Return Me.m_ShowToolTipOnButton
        End Get
        Set(ByVal value As Boolean)
            Me.m_ShowToolTipOnButton = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbAdriftIconWhenHover() As Boolean
        Get
            Return Me.m_AdriftIconWhenHover
        End Get
        Set(ByVal value As Boolean)
            Me.m_AdriftIconWhenHover = value
        End Set
    End Property

    <System.ComponentModel.Description("是否显示文字阴影"), System.ComponentModel.Browsable(True)> _
    Public Property tbShadow() As Boolean
        Get
            Return Me.m_Shadow
        End Get
        Set(ByVal value As Boolean)
            Me.m_Shadow = value

            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("文字阴影的颜色"), System.ComponentModel.Browsable(True)> _
    Public Property tbShadowColor() As Color
        Get
            Return Me.m_ShadowColor
        End Get
        Set(ByVal value As Color)
            Me.m_ShadowColor = value

            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("文字阴影和文字的偏移量"), System.ComponentModel.Browsable(True)> _
    Public Property tbShadowOffset() As Point
        Get
            Return Me.m_ShadowOffset
        End Get
        Set(ByVal value As Point)
            Me.m_ShadowOffset = value

            Me.Invalidate()
        End Set
    End Property

    Public Property tbBadgeNumber() As Integer
        Get
            Return Me.m_BadgeNumber
        End Get
        Set(ByVal value As Integer)
            If Me.m_BadgeNumber <> value Then
                Me.m_BadgeNumber = value
                Me.Invalidate()
            End If
        End Set
    End Property

    <System.ComponentModel.Description("气泡数字 tbBadgeNumber 的偏移量"), System.ComponentModel.Browsable(True)> _
    Public Property tbBadgeNumberOffset() As Point
        Get
            Return Me.m_BadgeNumberOffset
        End Get
        Set(ByVal value As Point)
            Me.m_BadgeNumberOffset = value

            Me.Invalidate()
        End Set
    End Property

    Public Property BindingForm() As Form
        Get
            Return Me.m_BindingForm
        End Get
        Set(ByVal value As Form)
            If Me.m_BindingForm IsNot value Then
                Me.m_BindingForm = value
            End If
        End Set
    End Property

    Public Property tbBackgroundImage() As Image
        Get
            Return Me.m_ImageBackground
        End Get
        Set(ByVal value As Image)
            Me.m_imgMouseLeave = Nothing
            Me.m_imgMouseHover = Nothing
            Me.m_imgMouseDown = Nothing
            Me.m_ImageBackground = value
            Me.ReSetSize()
        End Set
    End Property

    Public Property tbBackgroundImageState() As ImageState
        Get
            Return Me.m_ImageBackgroundState
        End Get
        Set(ByVal value As ImageState)
            Me.m_ImageBackgroundState = value
            Me.ReSetSize()
        End Set
    End Property

    Public Property tbReadOnly() As Boolean
        Get
            Return Me.m_ReadOnly
        End Get
        Set(ByVal value As Boolean)
            Me.m_ReadOnly = value
            'If value Then
            '    Me.m_imgNew = My.Resources.icon_new_light
            'Else
            '    Me.m_imgNew = My.Resources.icon_new
            'End If
            Me.Invalidate()
        End Set
    End Property

    Public Property tbReadOnlyText() As Boolean
        Get
            Return Me.m_ReadOnlyText
        End Get
        Set(ByVal value As Boolean)
            Me.m_ReadOnlyText = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbIconReadOnly() As Image
        Get
            Return Me.m_IconReadOnly
        End Get
        Set(ByVal value As Image)
            Me.m_IconReadOnly = value
            Me.ReSetSize()
        End Set
    End Property

    Public Property tbShowNew() As Boolean
        Get
            Return Me.m_ShowNew
        End Get
        Set(ByVal value As Boolean)
            Me.m_ShowNew = value
            Me.Invalidate()
        End Set
    End Property

    Private mSize As Size = Drawing.Size.Empty
    Private mMinSize As Size = Drawing.Size.Empty
    Private mVisibleEx As Boolean = True
    Public Property VisibleEx() As Boolean
        Get
            Return Me.mVisibleEx
        End Get
        Set(ByVal value As Boolean)
            If Me.mVisibleEx <> value Then
                If value Then
                    Me.MinimumSize = Me.mMinSize
                    Me.mMinSize = Drawing.Size.Empty

                    Me.Size = Me.mSize
                    Me.mSize = Drawing.Size.Empty

                Else
                    Me.mMinSize = Me.MinimumSize
                    Me.MinimumSize = Drawing.Size.Empty

                    Me.mSize = Me.Size
                    Me.Size = Drawing.Size.Empty
                End If
                Me.mVisibleEx = value
            End If
        End Set
    End Property

#End Region

#Region "--- Overrides ---"

    Private Sub SetTipText()
        If String.IsNullOrEmpty(Me.m_ToolTip) Then
            Return
        End If

        If m_ToolTipControl Is Nothing Then
            Me.m_ToolTipControl = New ToolTip()
            Me.m_ToolTipControl.InitialDelay = 300
            Me.m_ToolTipControl.ReshowDelay = 0
            Me.m_ToolTipControl.Active = False
        End If

        Me.m_ToolTipControl.SetToolTip(Me, Me.m_ToolTip)
    End Sub

    Public Sub AdjustSize()
        If Me.m_AutoSizeEx = False OrElse Me.m_Text.Length = 0 OrElse Me.mVisibleEx = False Then
            Return
        End If

        Dim textSize As Size = TextRenderer.MeasureText(Me.m_Text, Me.Font)
        Dim tipSize As Size = TextRenderer.MeasureText(Me.m_ToolTip, Me.m_ToolTipFont)
        Dim strSize As Size = Drawing.Size.Empty

        If Me.m_ShowToolTipOnButton AndAlso Me.m_ToolTip.Length > 0 Then
            If textSize.Width > tipSize.Width Then
                strSize = New Size(textSize.Width, textSize.Height + Me.m_TextSpaceTip + tipSize.Height)
            Else
                strSize = New Size(tipSize.Width, textSize.Height + Me.m_TextSpaceTip + tipSize.Height)
            End If
        Else
            strSize = textSize
        End If

        Dim newWidth As Integer = Me.Padding.Horizontal
        Dim newHeight As Integer = Me.Padding.Vertical

        If Me.m_Icon IsNot Nothing Then
            Select Case Me.m_IconImageAlign
                Case Drawing.ContentAlignment.MiddleLeft, Drawing.ContentAlignment.MiddleCenter, Drawing.ContentAlignment.MiddleRight
                    If Me.m_Icon.Height > strSize.Height Then
                        newHeight += Me.m_Icon.Height
                    Else
                        newHeight += strSize.Height
                    End If

                Case Else
                    newHeight += Me.m_Icon.Height + Me.m_IconPlaceText + strSize.Height

            End Select

            Select Case Me.m_IconImageAlign
                Case Drawing.ContentAlignment.TopCenter, Drawing.ContentAlignment.MiddleCenter, Drawing.ContentAlignment.BottomCenter
                    If Me.m_Icon.Width / CInt(Me.m_IconState) > strSize.Width Then
                        newWidth += Me.m_Icon.Width \ CInt(Me.m_IconState)
                    Else
                        newWidth += strSize.Width
                    End If

                Case Else
                    newWidth += Me.m_Icon.Width \ CInt(Me.m_IconState) + m_IconPlaceText + strSize.Width

            End Select
        Else
            newWidth += strSize.Width
            newHeight += strSize.Height
        End If

        If Me.m_CloseSize.IsEmpty = False Then
            newWidth += Me.m_CloseSize.Width + Me.m_CloseHoldPlace
        End If

        'If Me.m_ShowMoreIcon Then
        '    newWidth += m_IconPlaceText + My.Resources.icon_more.Width + 2
        'End If

        If Me.tbToolTip.Length > 0 Then
            newHeight += Me.m_TextSpaceTip + TextRenderer.MeasureText(Me.m_ToolTip, Me.m_ToolTipFont).Height
        End If

        If Me.Width <> newWidth OrElse Me.Height <> newHeight Then
            Select Case Me.m_TextAlign
                Case System.Drawing.ContentAlignment.MiddleLeft, System.Drawing.ContentAlignment.MiddleCenter, System.Drawing.ContentAlignment.MiddleRight
                    Me.Size = New Size(newWidth, Me.Height)

                Case System.Drawing.ContentAlignment.BottomLeft, System.Drawing.ContentAlignment.BottomCenter, System.Drawing.ContentAlignment.BottomRight
                    Me.m_LastLocation = Me.Location
                    Me.Location = New Point(Me.Left, Me.Top - (newHeight - Me.Height))
                    Me.Size = New Size(newWidth, newHeight)

                Case Else
                    Me.Size = New Size(newWidth, newHeight)

            End Select

        End If

    End Sub

    Private Sub ReSetSize()
        If Me.tbAutoSize Then
            '优先使用 单独的鼠标状态图片


            If Me.m_ImageBackground IsNot Nothing Then
                Me.Size = New Size(Me.m_ImageBackground.Width \ CInt(Me.m_ImageBackgroundState), Me.m_ImageBackground.Height)
            ElseIf Me.m_IconMouseLeave IsNot Nothing Then
                Me.Size = Me.m_IconMouseLeave.Size
            ElseIf Me.m_IconMouseHover IsNot Nothing Then
                Me.Size = Me.m_IconMouseHover.Size
            ElseIf Me.m_IconMouseDown IsNot Nothing Then
                Me.Size = Me.m_IconMouseDown.Size
            End If
        End If
        Me.Invalidate()
    End Sub

    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        MyBase.Dispose(disposing)

        If disposing Then
            If Me.m_TextToolTip IsNot Nothing Then
                Me.m_TextToolTip.Dispose()
            End If
            If Me.m_ToolTipControl IsNot Nothing Then
                Me.m_ToolTipControl.Dispose()
            End If
        End If
    End Sub

    Protected Overrides Sub OnTextChanged(ByVal e As System.EventArgs)
        MyBase.OnTextChanged(e)

        Me.tbText = Me.Text
    End Sub

    Protected Overrides Sub OnFontChanged(ByVal e As System.EventArgs)
        MyBase.OnFontChanged(e)

        Me.AdjustSize()
    End Sub

    Protected Overrides Sub OnPaddingChanged(ByVal e As System.EventArgs)
        MyBase.OnPaddingChanged(e)

        Me.AdjustSize()
    End Sub

    Protected Overrides Sub OnLocationChanged(ByVal e As System.EventArgs)
        If Me.DesignMode AndAlso Me.tbAutoSize = False Then
            Me.m_LastLocation = Me.Location
        End If

        MyBase.OnLocationChanged(e)
    End Sub

    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        MyBase.OnResize(e)

        If Me.DesignMode AndAlso Me.tbAutoSize = False Then
            Me.m_LastSize = Me.Size
        End If

    End Sub

    Protected Overrides Sub OnMouseEnter(ByVal e As System.EventArgs)
        MyBase.OnMouseEnter(e)

        If Not Me.DesignMode AndAlso Me.m_ShowToolTip Then
            If Me.m_TextToolTip Is Nothing Then
                Me.m_TextToolTip = New ToolTip()
            End If
            If Me.m_ToolTip.Length > 0 AndAlso Not Me.m_ShowToolTipOnButton Then
                Me.m_TextToolTip.Show(Me.m_ToolTip, Me, 0, Me.Height + 15)
            Else
                Me.m_TextToolTip.Show(Me.m_Text, Me, Me.Width, 0)
            End If
        End If
    End Sub

    Protected Overrides Sub OnMouseLeave(ByVal e As System.EventArgs)
        MyBase.OnMouseLeave(e)

        If Not Me.DesignMode AndAlso Me.m_ShowToolTip AndAlso Me.m_TextToolTip IsNot Nothing Then
            Me.m_TextToolTip.Hide(Me)
        End If

        If Me.m_MouseState <> MouseStates.MouseLeave Then
            Me.m_MouseState = MouseStates.MouseLeave

            Me.Invalidate()
        End If
    End Sub

    Protected Overrides Sub OnClick(ByVal e As System.EventArgs)
        If CType(e, System.Windows.Forms.MouseEventArgs).Button = Windows.Forms.MouseButtons.Left Then
            MyBase.OnClick(e)
        End If
    End Sub

    Protected Overrides Sub OnMouseMove(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseMove(e)

        If Me.m_MouseState <> MouseStates.MouseHover Then
            Me.m_MouseState = MouseStates.MouseHover

            Me.Invalidate()
        End If
    End Sub

    Protected Overrides Sub OnMouseDown(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseDown(e)

        If Me.m_MouseState <> MouseStates.MouseDown Then
            Me.m_MouseState = MouseStates.MouseDown

            Me.Invalidate()
        End If
    End Sub

    Protected Overrides Sub OnMouseUp(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseUp(e)

        Me.SetTipText()
    End Sub

    Protected Overrides Sub OnGotFocus(ByVal e As System.EventArgs)
        MyBase.OnGotFocus(e)

        If Me.m_MouseState <> MouseStates.MouseHover Then
            Dim pos As Point = Me.PointToClient(System.Windows.Forms.Cursor.Position)
            If Me.ClientRectangle.Contains(pos) Then
                Me.m_MouseState = MouseStates.MouseHover

                Me.Invalidate()
            End If
        End If
    End Sub

    Protected Overrides Sub OnLostFocus(ByVal e As System.EventArgs)
        MyBase.OnLostFocus(e)

        If Me.m_MouseState <> MouseStates.MouseLeave Then
            Me.m_MouseState = MouseStates.MouseLeave

            Me.Invalidate()
        End If
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)

        If e.KeyCode = Keys.Enter Then
            MyBase.OnClick(New System.EventArgs)
        End If
    End Sub

    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        'MyBase.OnPaint(e)

        Dim g As Graphics = e.Graphics

        Dim blnEnable As Boolean = (Me.Enabled And Not Me.m_ReadOnly) '  Dim blnEnable As Boolean = (Me.Enabled Or Me.m_ReadOnly)
        If Not Me.Parent.Enabled AndAlso blnEnable Then
            blnEnable = False
        End If

        Me.OnPaintBackground(e)
        '====== 绘制背景 ========================================================================================
        If Me.m_ImageBackground IsNot Nothing Then
            GuiHelper.DrawImage(g, Me.ClientRectangle, Me.m_Split, Me.m_ImageBackground, Me.Enabled, IIf(Me.m_ReadOnly, MouseStates.MouseLeave, Me.m_MouseState), Me.m_ImageBackgroundState)
        ElseIf Not Me.m_ReadOnly Then
            If Me.m_imgMouseLeave IsNot Nothing AndAlso Me.m_MouseState = MouseStates.MouseLeave Then
                GuiHelper.DrawImage(g, Me.ClientRectangle, Me.m_Split, Me.m_imgMouseLeave, True, MouseStates.MouseLeave, ImageState.OneState)

            ElseIf Me.m_imgMouseHover IsNot Nothing AndAlso Me.m_MouseState = MouseStates.MouseHover Then
                GuiHelper.DrawImage(g, Me.ClientRectangle, Me.m_Split, Me.m_imgMouseHover, True, MouseStates.MouseHover, ImageState.OneState)

            ElseIf Me.m_imgMouseDown IsNot Nothing AndAlso Me.m_MouseState = MouseStates.MouseDown Then
                GuiHelper.DrawImage(g, Me.ClientRectangle, Me.m_Split, Me.m_imgMouseDown, True, MouseStates.MouseDown, ImageState.OneState)

            End If
        End If

        Dim intMarginLeft As Integer = Me.Padding.Left
        Dim intMarginRight As Integer = Me.Padding.Right
        Dim intMarginTop As Integer = Me.Padding.Top
        Dim intMarginBottom As Integer = Me.Padding.Bottom

        Dim iconSize As Size = Drawing.Size.Empty
        If Me.m_Icon IsNot Nothing Then
            iconSize = New Size(Me.m_Icon.Width \ CInt(Me.m_IconState), Me.m_Icon.Height)
        ElseIf Me.m_IconMouseLeave IsNot Nothing Then
            iconSize = Me.m_IconMouseLeave.Size
        ElseIf Me.m_IconMouseHover IsNot Nothing Then
            iconSize = Me.m_IconMouseHover.Size
        ElseIf Me.m_IconMouseDown IsNot Nothing Then
            iconSize = Me.m_IconMouseDown.Size
        End If


        Dim textSize As Size = TextRenderer.MeasureText(Me.m_Text, Me.Font)
        '=== 绘制按钮小图标 ===========================================================================================
        If Not iconSize.IsEmpty Then
            'Dim imgIcon As Image = Nothing 'CType(IIf(Me.Enabled, Me.m_Icon, GuiHelper.CreateDisabledImage(Me.m_Icon)), Image)
            Dim p As PointF = GuiHelper.CalculateLocation(Me.ClientRectangle, iconSize, Me.m_IconImageAlign, Me.Padding, iconSize, Me.m_IconPlaceText)

            If Me.m_MouseState = MouseStates.MouseHover AndAlso Me.m_AdriftIconWhenHover Then
                p.X -= 1
                p.Y -= 1
            End If

            m_RectIcon = New Rectangle(p.X, p.Y, iconSize.Width, iconSize.Height)
            If Me.m_ReadOnly AndAlso Me.m_IconReadOnly IsNot Nothing Then
                GuiHelper.DrawImage(g, m_RectIcon, tbSplitStructure.Empty, Me.m_IconReadOnly, True, Me.m_MouseState, ImageState.OneState)
            Else
                If Me.m_MouseState = MouseStates.MouseLeave AndAlso Me.m_IconMouseLeave IsNot Nothing Then
                    GuiHelper.DrawImage(g, m_RectIcon, tbSplitStructure.Empty, Me.m_IconMouseLeave, blnEnable, MouseStates.MouseLeave, ImageState.OneState)

                ElseIf Me.m_MouseState = MouseStates.MouseHover AndAlso Me.m_IconMouseHover IsNot Nothing Then
                    GuiHelper.DrawImage(g, m_RectIcon, tbSplitStructure.Empty, Me.m_IconMouseHover, blnEnable, MouseStates.MouseHover, ImageState.OneState)

                ElseIf Me.m_MouseState = MouseStates.MouseDown AndAlso Me.m_IconMouseDown IsNot Nothing Then
                    GuiHelper.DrawImage(g, m_RectIcon, tbSplitStructure.Empty, Me.m_IconMouseDown, blnEnable, MouseStates.MouseDown, ImageState.OneState)

                ElseIf Me.m_Icon IsNot Nothing Then
                    GuiHelper.DrawImage(g, m_RectIcon, tbSplitStructure.Empty, Me.m_Icon, blnEnable, Me.m_MouseState, Me.m_IconState)

                End If
            End If

            '绘制提示气泡
            If Me.m_BadgeNumber > 0 Then
                Dim badgeSize As SizeF = g.MeasureString(Me.m_BadgeNumber.ToString(), m_BadgeFont)
                Dim badgeWidth As Integer = CInt(badgeSize.Width + 14)
                Dim badgeHeight As Integer = Me.m_BadgeImage.Height
                Dim badgeLocX As Integer = CInt(p.X + iconSize.Width - badgeWidth * 0.7 + Me.m_BadgeNumberOffset.X)
                Dim badgeLocY As Integer = CInt(p.Y - badgeHeight * 0.2 + Me.m_BadgeNumberOffset.Y)

                '绘制气泡背景
                g.DrawImage(Me.m_BadgeImage, New Rectangle(badgeLocX, badgeLocY, 10, badgeHeight), _
                                                         New Rectangle(0, 0, 10, badgeHeight), GraphicsUnit.Pixel)

                g.DrawImage(Me.m_BadgeImage, New Rectangle(badgeLocX + 10, badgeLocY, badgeWidth - 20, badgeHeight), _
                                                         New Rectangle(10, 0, Me.m_BadgeImage.Width - 20, badgeHeight), GraphicsUnit.Pixel)

                g.DrawImage(Me.m_BadgeImage, New Rectangle(badgeLocX + badgeWidth - 10, badgeLocY, 10, badgeHeight), _
                                                         New Rectangle(Me.m_BadgeImage.Width - 10, 0, 10, badgeHeight), GraphicsUnit.Pixel)
                '绘制气泡上的数字
                Dim rectBadgeNumber As New Rectangle(badgeLocX, badgeLocY, badgeWidth, badgeHeight - 7)
                GuiHelper.DrawString(g, Me.tbBadgeNumber.ToString(), m_BadgeFont, rectBadgeNumber, Color.White, Drawing.ContentAlignment.MiddleCenter, blnEnable)
            Else
                If Me.m_ShowNew Then
                    Dim badgeSize As SizeF = Me.m_imgNew.Size
                    Dim badgeLocX As Integer = CInt(p.X + iconSize.Width - badgeSize.Width * 0.7 + Me.m_BadgeNumberOffset.X)
                    Dim badgeLocY As Integer = CInt(p.Y - badgeSize.Height * 0.2 + Me.m_BadgeNumberOffset.Y)

                    g.DrawImage(Me.m_imgNew, New Rectangle(badgeLocX, badgeLocY, badgeSize.Width, badgeSize.Height), _
                                                             New Rectangle(0, 0, badgeSize.Width, badgeSize.Height), GraphicsUnit.Pixel)

                End If
            End If

            If Me.m_IconHoldPlace Then
                Select Case Me.tbIconImageAlign
                    Case System.Drawing.ContentAlignment.BottomCenter
                        intMarginBottom += iconSize.Height
                    Case System.Drawing.ContentAlignment.MiddleLeft, System.Drawing.ContentAlignment.TopLeft, System.Drawing.ContentAlignment.BottomLeft

                        intMarginLeft += iconSize.Width + Me.m_IconPlaceText
                    Case System.Drawing.ContentAlignment.MiddleRight, System.Drawing.ContentAlignment.TopRight, System.Drawing.ContentAlignment.BottomRight
                        intMarginRight += iconSize.Width + Me.m_IconPlaceText
                    Case System.Drawing.ContentAlignment.TopCenter
                        intMarginTop += iconSize.Height
                End Select
            End If

        End If
        '==============================================================================================================

        'If Me.m_ShowMoreIcon Then
        '    Dim imgMore As Image = My.Resources.icon_more
        '    Dim rectDraw As New Rectangle(Me.Width - imgMore.Width - 3, (Me.Height - imgMore.Height) \ 2, imgMore.Width, imgMore.Height)
        '    Dim rectImage As New Rectangle(0, 0, imgMore.Width, imgMore.Height)
        '    g.DrawImage(imgMore, rectDraw, rectImage, GraphicsUnit.Pixel)

        '    intMarginRight += imgMore.Width + 2
        'End If

        Dim txtColor As Color = Me.m_ToolTipColor
        If Me.Enabled Then
            Select Case Me.m_MouseState
                Case MouseStates.MouseHover
                    txtColor = Me.m_TextColorHover
                Case MouseStates.MouseDown
                    txtColor = Me.m_TextColorDown
                    intMarginTop += Me.m_TextMouseDownPlace
                Case Else
                    txtColor = Me.m_TextColor
            End Select
        Else
            txtColor = Me.m_TextColorDisable
        End If

        Dim rect As Rectangle = New Rectangle(intMarginLeft, intMarginTop, Me.Width - intMarginLeft - intMarginRight, Me.Height - intMarginTop - intMarginBottom)
        Dim rectText As Rectangle = rect

        If Me.m_ShowToolTipOnButton AndAlso Me.m_ToolTip.Length > 0 Then
            Dim tipSize As SizeF = g.MeasureString(Me.m_ToolTip, Me.m_ToolTipFont)
            Dim tipHeight As Integer = CInt((rect.Height - m_TextSpaceTip) * (tipSize.Height / (textSize.Height + tipSize.Height)))
            Dim rectTip As Rectangle = New Rectangle(rect.Left, rect.Bottom - m_TextSpaceTip - tipHeight, rect.Width, tipHeight)

            rectText = New Rectangle(rect.Left, rect.Top, rect.Width, rect.Height - m_TextSpaceTip - tipHeight)

            '绘制按钮提示文字
            GuiHelper.DrawString(g, Me.m_ToolTip, Me.m_ToolTipFont, rectTip, Me.m_ToolTipColor, Me.m_ToolTipTextAlign, blnEnable)
            'GuiHelper.DrawText(g, Me.m_ToolTip, Me.m_ToolTipFont, rectTip, Me.m_ToolTipColor, Color.Transparent, Me.m_ToolTipTextAlign, blnEnalble)
        Else
            rectText = rect
        End If

        Dim blnEnableText As Boolean = (Me.Enabled Or Me.m_ReadOnly)
        If Me.m_ReadOnlyText Then
            blnEnableText = False
        End If
        If Me.m_Shadow AndAlso Me.Enabled Then
            GuiHelper.DrawText(g, Me.m_Text, Me.Font, New Rectangle(rect.Location + Me.tbShadowOffset, rect.Size), Me.m_ShadowColor, Color.Transparent, Me.tbTextAlign, blnEnableText)
        End If
        GuiHelper.DrawText(g, Me.m_Text, Me.Font, rectText, txtColor, Color.Transparent, Me.tbTextAlign, blnEnableText, Me.m_EndEllipsis)

        Me.m_ShowToolTip = (rectText.Width < textSize.Width - 4 OrElse rectText.Height < textSize.Height - 4 OrElse Me.m_ToolTip.Length > 0 AndAlso Not Me.m_ShowToolTipOnButton)

        'If Me.m_Text = "Add Apps" Then
        '    Stop
        'End If
    End Sub

#End Region

End Class

<System.ComponentModel.DefaultEvent("Click")> _
Public Class tbButtonEx
    Inherits tbButton

    Private mRightIcon As Image
    Private mRingtIconPadding As Padding = Padding.Empty

    Public Property tbRingtIconPadding() As Padding
        Get
            Return Me.mRingtIconPadding
        End Get
        Set(ByVal value As Padding)
            Me.mRingtIconPadding = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbRightIcon() As Image
        Get
            Return Me.mRightIcon
        End Get
        Set(ByVal value As Image)
            Me.mRightIcon = value
            Me.Invalidate()
        End Set
    End Property

    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        MyBase.OnPaint(e)

        Dim img As Image = Me.mRightIcon
        If img IsNot Nothing Then
            Dim rectDraw As New Rectangle(Me.Width - Me.mRingtIconPadding.Right - img.Width, (Me.Height - img.Height) \ 2, img.Width, img.Height)
            e.Graphics.DrawImage(img, rectDraw, New Rectangle(0, 0, img.Width, img.Height), GraphicsUnit.Pixel)
        End If
    End Sub

End Class

#End Region

'#Region "--- tbProgressBar ---"

'<System.ComponentModel.DefaultEvent("ValueChanged")> _
'Public Class tbProgressBar
'    Inherits System.Windows.Forms.Control

'    Private mTimerWaiting As System.Timers.Timer
'    Private mintWaittingValue As Integer = 0

'    Public Sub New()
'        Me.SetStyle(ControlStyles.AllPaintingInWmPaint, True)
'        Me.SetStyle(ControlStyles.DoubleBuffer, True)
'        Me.SetStyle(ControlStyles.ResizeRedraw, True)
'        Me.SetStyle(ControlStyles.SupportsTransparentBackColor, True)
'        Me.SetStyle(ControlStyles.UserPaint, True)
'        Me.SetStyle(ControlStyles.Selectable, True)
'        Me.SetStyle(ControlStyles.ContainerControl, False)

'        Me.BackColor = Color.Transparent
'        Me.Size = New System.Drawing.Size(264, 32)
'    End Sub

'#Region "--- 对外属性 ---"

'#Region "- Value -"

'    Private mValue As Long = 0
'    ''' <summary> 
'    ''' 当前进度值


'    ''' </summary> 
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(0), System.ComponentModel.Description("当前进度值.")> _
'    Public Property Value() As Long
'        Get
'            Return mValue
'        End Get
'        Set(ByVal value As Long)
'            If value > Me.Maximum OrElse value < Me.Minimum Then
'                Return
'            End If
'            Me.mValue = value
'            RaiseEvent ValueChanged(Me, New System.EventArgs())
'            Me.Invalidate()
'        End Set
'    End Property

'    Private mMaxValue As Long = 100
'    ''' <summary> 
'    ''' 最大值. 
'    ''' </summary> 
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(100), System.ComponentModel.Description("最大值.")> _
'    Public Property Maximum() As Long
'        Get
'            Return mMaxValue
'        End Get
'        Set(ByVal value As Long)
'            mMaxValue = value
'            RaiseEvent MaxChanged(Me, New System.EventArgs())
'            Me.Invalidate()
'        End Set
'    End Property

'    Private mMinValue As Long = 0
'    ''' <summary> 
'    ''' 最小值. 
'    ''' </summary> 
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(0), System.ComponentModel.Description("最小值.")> _
'    Public Property Minimum() As Long
'        Get
'            Return mMinValue
'        End Get
'        Set(ByVal value As Long)
'            mMinValue = value
'            RaiseEvent MinChanged(Me, New System.EventArgs())
'            Me.Invalidate()
'        End Set
'    End Property

'    Private mStateText As String = String.Empty
'    ''' <summary>
'    ''' 状态信息


'    ''' </summary>
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(0), System.ComponentModel.Description("状态信息.")> _
'    Public Property StateText() As String
'        Get
'            Return mStateText
'        End Get
'        Set(ByVal value As String)
'            If String.IsNullOrEmpty(value) Then
'                value = String.Empty
'            End If
'            mStateText = value
'            Me.Invalidate()
'        End Set
'    End Property

'    Private mStateTextTextAlign As System.Drawing.ContentAlignment = System.Drawing.ContentAlignment.MiddleCenter
'    ''' <summary>
'    ''' 状态信息文本对齐方式


'    ''' </summary>
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(GetType(System.Drawing.ContentAlignment), "ContentAlignment.MiddleCenter"), System.ComponentModel.Description("状态信息文本对齐方式")> _
'    Public Property StateTextTextAlign() As System.Drawing.ContentAlignment
'        Get
'            Return mStateTextTextAlign
'        End Get
'        Set(ByVal value As System.Drawing.ContentAlignment)
'            Me.mStateTextTextAlign = value
'            Me.Invalidate()
'        End Set
'    End Property

'    Private mAutoProgressText As Boolean = True
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(True), System.ComponentModel.Description("是否自动显示当前进度百分比"), System.ComponentModel.Browsable(True)> _
'    Public Property AutoProgressText() As Boolean
'        Get
'            Return Me.mAutoProgressText
'        End Get
'        Set(ByVal value As Boolean)
'            Me.mAutoProgressText = value
'            Me.Invalidate()
'        End Set
'    End Property

'    Private mAutoProgressCenter As Boolean = True
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(True), System.ComponentModel.Description("显示的文字是否显示在进度中间，而不是整个进度条中间"), System.ComponentModel.Browsable(True)> _
'    Public Property AutoProgressCenter() As Boolean
'        Get
'            Return Me.mAutoProgressCenter
'        End Get
'        Set(ByVal value As Boolean)
'            Me.mAutoProgressCenter = value
'            Me.Invalidate()
'        End Set
'    End Property

'#End Region

'#Region "- Bar -"

'    Private mProgressColor As Color = Color.FromArgb(0, 211, 40)
'    ''' <summary> 
'    ''' The color for the progress bar. 
'    ''' 210, 000, 000 = Red 
'    ''' 210, 202, 000 = Yellow 
'    ''' 000, 163, 211 = Blue 
'    ''' 000, 211, 040 = Green 
'    ''' </summary> 
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(GetType(Color), "0, 211, 40"), System.ComponentModel.Description("The color for the progress bar." + "210, 000, 000 = Red" & Chr(10) & "" + "210, 202, 000 = Yellow" & Chr(10) & "" + "000, 163, 211 = Blue" & Chr(10) & "" + "000, 211, 040 = Green" & Chr(10) & "")> _
'    Public Property ProgressColor() As Color
'        Get
'            Return mProgressColor
'        End Get
'        Set(ByVal value As Color)
'            mProgressColor = value
'            Me.Invalidate()
'        End Set
'    End Property

'#End Region

'#Region "- Highlights -"

'    Private mHighlightColor As Color = Color.White
'    ''' <summary> 
'    ''' The color of the highlights. 
'    ''' </summary> 
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(GetType(Color), "White"), System.ComponentModel.Description("The color of the highlights.")> _
'    Public Property HighlightColor() As Color
'        Get
'            Return mHighlightColor
'        End Get
'        Set(ByVal value As Color)
'            mHighlightColor = value
'            Me.Invalidate()
'        End Set
'    End Property

'    Private mBackgroundColor As Color = Color.FromArgb(201, 201, 201)
'    ''' <summary> 
'    ''' The color of the background. 
'    ''' </summary> 
'    <System.ComponentModel.Category("ProgressBar"), System.ComponentModel.DefaultValue(GetType(Color), "201,201,201"), System.ComponentModel.Description("The color of the background.")> _
'    Public Property BackgroundColor() As Color
'        Get
'            Return mBackgroundColor
'        End Get
'        Set(ByVal value As Color)
'            mBackgroundColor = value
'            Me.Invalidate()
'        End Set
'    End Property

'#End Region

'#Region "- tbIsWaitting -"

'    Private mIsWaiting As Boolean = False

'    Public Property tbIsWaiting() As Boolean
'        Get
'            Return Me.mIsWaiting
'        End Get
'        Set(ByVal value As Boolean)
'            Me.mIsWaiting = value

'            If Me.mIsWaiting = True Then
'                If Me.mTimerWaiting Is Nothing Then
'                    Me.mTimerWaiting = New System.Timers.Timer
'                    With Me.mTimerWaiting
'                        .Interval = 100
'                        RemoveHandler mTimerWaiting.Elapsed, AddressOf TimerWaiting_Tick
'                        AddHandler mTimerWaiting.Elapsed, AddressOf TimerWaiting_Tick
'                    End With
'                End If

'                Me.mProgressWaitingImage = My.Resources.ProgressWaitting001
'                Me.mTimerWaiting.Start()
'            Else
'                If Me.mTimerWaiting IsNot Nothing Then
'                    Me.mTimerWaiting.Stop()
'                End If
'                Me.mProgressWaitingImage = My.Resources.dgv_progress_bg
'            End If

'            Me.Invalidate()
'        End Set
'    End Property

'    Private mProgressWaitingImage As Image = Nothing
'    Private Sub TimerWaiting_Tick(ByVal sender As Object, ByVal e As System.Timers.ElapsedEventArgs)
'        Me.mintWaittingValue += 1
'        If Me.mintWaittingValue > 10 AndAlso Me.Value <= 0 Then
'            Me.mintWaittingValue = 1
'        End If

'        Dim waittingCount As String = "ProgressWaitting" & (Me.mintWaittingValue).ToString.PadLeft(3, "0"c)
'        Me.mProgressWaitingImage = My.Resources.ResourceManager.GetObject(waittingCount)
'        Me.Invalidate()
'    End Sub

'#End Region

'#End Region

'#Region "--- 绘图函数 ---"

'    Private Sub DrawBackground(ByVal g As Graphics)
'        Dim r As Rectangle = Me.ClientRectangle
'        r.Width -= 1
'        r.Height -= 1
'        Dim rr As GraphicsPath = RoundRect(r, 2, 2, 2, 2)
'        g.FillPath(GuiHelper.GetCacheBrush(Me.BackgroundColor), rr)
'        rr.Dispose()
'    End Sub

'    Private Sub DrawBackgroundShadows(ByVal g As Graphics)
'        Dim lr As New Rectangle(2, 2, 10, Me.Height - 5)
'        Dim lg As New LinearGradientBrush(lr, Color.FromArgb(30, 0, 0, 0), Color.Transparent, LinearGradientMode.Horizontal)
'        lr.X -= 1
'        g.FillRectangle(lg, lr)
'        lg.Dispose()

'        Dim rr As New Rectangle(Me.Width - 12, 2, 10, Me.Height - 5)
'        Dim rg As New LinearGradientBrush(rr, Color.Transparent, Color.FromArgb(20, 0, 0, 0), LinearGradientMode.Horizontal)
'        g.FillRectangle(rg, rr)
'        rg.Dispose()
'    End Sub

'    Private Sub DrawBar(ByVal g As Graphics)
'        Dim r As New Rectangle(1, 2, Me.Width - 3, Me.Height - 3)
'        Dim progressWidth As Long = (Me.Maximum - Me.Minimum)

'        If progressWidth > 0 Then
'            r.Width = CInt((Value * 1.0F / progressWidth * Me.Width))
'        Else
'            r.Width = 0
'        End If

'        g.FillRectangle(GuiHelper.GetCacheBrush(Me.ProgressColor), r)
'    End Sub

'    Private Sub DrawBarShadows(ByVal g As Graphics)
'        Dim lr As New Rectangle(1, 2, 15, Me.Height - 3)
'        Dim lg As New LinearGradientBrush(lr, Color.White, Color.White, LinearGradientMode.Horizontal)

'        Dim lc As New ColorBlend(3)
'        lc.Colors = New Color() {Color.Transparent, Color.FromArgb(40, 0, 0, 0), Color.Transparent}
'        lc.Positions = New Single() {0.0F, 0.2F, 1.0F}
'        lg.InterpolationColors = lc

'        lr.X -= 1
'        g.FillRectangle(lg, lr)
'        lg.Dispose()

'        Dim progressWidth As Long = (Me.Maximum - Me.Minimum)
'        Dim rr As New Rectangle(Me.Width - 3, 2, 15, Me.Height - 3)

'        If progressWidth > 0 Then
'            rr.X = CInt((Value * 1.0F / progressWidth * Me.Width)) - 14
'        Else
'            rr.X = 0
'        End If

'        Dim rg As New LinearGradientBrush(rr, Color.Black, Color.Black, LinearGradientMode.Horizontal)

'        Dim rc As New ColorBlend(3)
'        rc.Colors = New Color() {Color.Transparent, Color.FromArgb(40, 0, 0, 0), Color.Transparent}
'        rc.Positions = New Single() {0.0F, 0.8F, 1.0F}
'        rg.InterpolationColors = rc

'        g.FillRectangle(rg, rr)
'        rg.Dispose()
'    End Sub

'    Private Sub DrawHighlight(ByVal g As Graphics)
'        Dim tr As New Rectangle(1, 1, Me.Width - 1, 6)
'        Dim tp As GraphicsPath = RoundRect(tr, 2, 2, 0, 0)
'        Dim tg As New LinearGradientBrush(tr, Color.White, Color.FromArgb(128, Color.White), LinearGradientMode.Vertical)

'        g.SetClip(tp)
'        g.FillPath(tg, tp)
'        g.ResetClip()

'        tg.Dispose()
'        tp.Dispose()

'        Dim br As New Rectangle(1, Me.Height - 8, Me.Width - 1, 6)
'        Dim bp As GraphicsPath = RoundRect(br, 0, 0, 2, 2)
'        Dim bg As New LinearGradientBrush(br, Color.Transparent, Color.FromArgb(100, Me.HighlightColor), LinearGradientMode.Vertical)

'        g.SetClip(bp)
'        g.FillPath(bg, bp)
'        g.ResetClip()

'        bg.Dispose()
'        bp.Dispose()
'    End Sub

'    Private Sub DrawInnerStroke(ByVal g As Graphics)
'        Dim r As Rectangle = Me.ClientRectangle
'        r.X += 1
'        r.Y += 1
'        r.Width -= 3
'        r.Height -= 3
'        Dim rr As GraphicsPath = RoundRect(r, 2, 2, 2, 2)
'        g.DrawPath(GuiHelper.GetCachePen(Color.FromArgb(100, Color.White)), rr)
'    End Sub

'    Private Sub DrawOuterStroke(ByVal g As Graphics)
'        Dim r As Rectangle = Me.ClientRectangle
'        r.Width -= 1
'        r.Height -= 1
'        Dim rr As GraphicsPath = RoundRect(r, 2, 2, 2, 2)
'        g.DrawPath(GuiHelper.GetCachePen(Color.FromArgb(178, 178, 178)), rr)
'    End Sub

'    Private Sub DrawStateText(ByVal g As Graphics, ByVal rec As RectangleF)
'        Dim progressWidth As Long = (Me.Maximum - Me.Minimum)

'        Dim strText As String = Me.StateText

'        If Me.AutoProgressText Then
'            If progressWidth > 0 Then
'                strText = CInt(Me.Value / (Me.Maximum - Me.Minimum) * 100) & " %"
'            Else
'                strText = "0 %"
'            End If
'        End If

'        Dim intX As Single = rec.X
'        Dim intY As Single = rec.Y
'        Dim intWidth As Single = rec.Width
'        Dim intHeight As Single = rec.Height

'        If Me.AutoProgressCenter Then
'            intX = 0
'            intY = 0

'            If progressWidth > 0 Then
'                intWidth = CSng(Me.ClientRectangle.Width * (Me.Value / progressWidth))
'            Else
'                intWidth = 0
'            End If

'            intHeight = Me.ClientRectangle.Height

'            Dim intTextWidth As Single = g.MeasureString(strText, Me.Font).Width + 5
'            If intWidth < intTextWidth Then
'                intWidth = intTextWidth
'            End If
'        End If

'        GuiHelper.DrawText(g, strText, Me.Font, New Rectangle(CInt(intX), CInt(intY), CInt(intWidth), CInt(intHeight)), Me.ForeColor, Me.BackColor, Me.StateTextTextAlign)
'    End Sub

'#End Region

'#Region "--- 内部函数 ---"

'    Private Function RoundRect(ByVal r As RectangleF, ByVal r1 As Single, ByVal r2 As Single, ByVal r3 As Single, ByVal r4 As Single) As GraphicsPath
'        Dim x As Single = r.X, y As Single = r.Y, w As Single = r.Width, h As Single = r.Height
'        Dim rr As New GraphicsPath()
'        rr.AddBezier(x, y + r1, x, y, x + r1, y, x + r1, y)
'        rr.AddLine(x + r1, y, x + w - r2, y)
'        rr.AddBezier(x + w - r2, y, x + w, y, x + w, y + r2, x + w, y + r2)
'        rr.AddLine(x + w, y + r2, x + w, y + h - r3)
'        rr.AddBezier(x + w, y + h - r3, x + w, y + h, x + w - r3, y + h, x + w - r3, y + h)
'        rr.AddLine(x + w - r3, y + h, x + r4, y + h)
'        rr.AddBezier(x + r4, y + h, x, y + h, x, y + h - r4, x, y + h - r4)
'        rr.AddLine(x, y + h - r4, x, y + r1)
'        Return rr
'    End Function

'    Private Function InDesignMode() As Boolean
'        Return (System.ComponentModel.LicenseManager.UsageMode = System.ComponentModel.LicenseUsageMode.Designtime)
'    End Function

'#End Region

'#Region "--- 绘图事件 ---"

'    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
'        'MyBase.OnPaint(e)
'        Dim g As Graphics = e.Graphics
'        g.SmoothingMode = SmoothingMode.AntiAlias
'        g.InterpolationMode = InterpolationMode.HighQualityBicubic
'        If Me.tbIsWaiting Then
'            Dim rect As Rectangle = New Rectangle(0, _
'                                                  e.ClipRectangle.Height / 2 - Me.mProgressWaitingImage.Height / 2, _
'                                                  e.ClipRectangle.Width, _
'                                                  e.ClipRectangle.Height)

'            'progress(背景)
'            g.DrawImage(Me.mProgressWaitingImage, _
'                        e.ClipRectangle, _
'                        New Rectangle(0, 0, Me.mProgressWaitingImage.Width, Me.mProgressWaitingImage.Height), _
'                        GraphicsUnit.Pixel)
'        Else
'            DrawBackground(g)
'            DrawBackgroundShadows(g)
'            DrawBar(g)
'            DrawBarShadows(g)
'            DrawHighlight(g)
'            DrawInnerStroke(g)
'            DrawOuterStroke(g)
'            DrawStateText(g, e.ClipRectangle)
'        End If


'    End Sub

'#End Region

'#Region "--- 事件 ---"

'    Public Event ValueChanged As EventHandler
'    Public Event MinChanged As EventHandler
'    Public Event MaxChanged As EventHandler

'#End Region

'End Class

'#End Region