﻿Imports System.Collections.Generic
Imports System.Text
Imports System.Runtime.InteropServices
Imports System.IO

<StructLayout(LayoutKind.Sequential)> _
   Public Structure HEAPLIST32
    Public dwSize As UInteger
    Public th32ProcessID As UInteger
    Public th32HeapID As UInteger
    Public dwFlags As UInteger
End Structure

<StructLayout(LayoutKind.Sequential)> _
Public Structure HEAPENTRY32
    Public dwSize As UInteger
    Public hHandle As IntPtr
    Public dwAddress As UInteger
    Public dwBlockSize As UInteger
    Public dwFlags As UInteger
    Public dwLockCount As UInteger
    Public dwResvd As UInteger
    Public th32ProcessID As UInteger
    Public th32HeapID As UInteger
End Structure

<StructLayout(LayoutKind.Sequential)> _
Public Structure PROCESSENTRY32W
    Public dwSize As UInteger
    Public cntUsage As UInteger
    Public th32ProcessID As UInteger
    Public th32DefaultHeapID As UIntPtr
    Public th32ModuleID As UInteger
    Public cntThreads As UInteger
    Public th32ParentProcessID As UInteger
    Public pcPriClassBase As Integer
    Public dwFlags As UInteger
    Public szExeFile As String
End Structure

<StructLayout(LayoutKind.Sequential)> _
Public Structure PROCESSENTRY32
    Public dwSize As UInteger
    Public cntUsage As UInteger
    Public th32ProcessID As UInteger
    Public th32DefaultHeapID As UInteger
    Public th32ModuleID As UInteger
    Public cntThreads As UInteger
    Public th32ParentProcessID As UInteger
    Public pcPriClassBase As Integer
    Public dwFlags As UInteger
    Public szExeFile As String
End Structure

<StructLayout(LayoutKind.Sequential)> _
Public Structure THREADENTRY32
    Public dwSize As UInteger
    Public cntUsage As UInteger
    Public th32ThreadID As UInteger
    Public th32OwnerProcessID As UInteger
    Public tpBasePri As Integer
    Public tpDeltaPri As Integer
    Public dwFlags As UInteger
End Structure

<StructLayout(LayoutKind.Sequential)> _
Public Structure MODULEENTRY32W
    Public dwSize As UInteger
    Public th32ModuleID As UInteger
    Public th32ProcessID As UInteger
    Public GlblcntUsage As UInteger
    Public ProccntUsage As UInteger
    Public modBaseAddr As IntPtr
    Public modBaseSize As UInteger
    Public hModule As IntPtr
    <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=256)> _
    Public szModule As String
    <MarshalAs(UnmanagedType.ByValTStr, SizeConst:=260)> _
    Public szExePath As String
End Structure

<StructLayout(LayoutKind.Sequential)> _
Public Structure MODULEENTRY32
    Public dwSize As UInteger
    Public th32ModuleID As UInteger
    Public th32ProcessID As UInteger
    Public GlblcntUsage As UInteger
    Public ProccntUsage As UInteger
    Public modBaseAddr As IntPtr
    Public modBaseSize As UInteger
    Public hModule As IntPtr
    Public szModule As String
    Public szExePath As String
End Structure

Public Class Toolhelp32
    Private Const TH32CS_SNAPHEAPLIST As UInteger = &H1
    Private Const TH32CS_SNAPPROCESS As UInteger = &H2
    Private Const TH32CS_SNAPTHREAD As UInteger = &H4
    Private Const TH32CS_SNAPMODULE As UInteger = &H8
    Private Const TH32CS_SNAPMODULE32 As UInteger = &H10
    Private Const TH32CS_SNAPALL As UInteger = (TH32CS_SNAPHEAPLIST Or TH32CS_SNAPPROCESS Or TH32CS_SNAPTHREAD Or TH32CS_SNAPMODULE)
    Private Const TH32CS_INHERIT As UInteger = &H80000000UI

    Private Const HF32_DEFAULT As UInteger = 1
    Private Const HF32_SHARED As UInteger = 2

    Private Const LF32_FIXED As UInteger = &H1
    Private Const LF32_FREE As UInteger = &H2
    Private Const LF32_MOVEABLE As UInteger = &H4

    <DllImport("kernel32.dll")> _
    Private Shared Function CreateToolhelp32Snapshot(ByVal dwFlags As UInteger, ByVal th32ProcessID As UInteger) As IntPtr
    End Function

    <DllImport("kernel32.dll", SetLastError:=True)> _
    Friend Shared Function CloseHandle(ByVal hSnapshot As IntPtr) As <MarshalAs(UnmanagedType.Bool)> Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Heap32ListFirst(ByVal hSnapshot As IntPtr, ByRef lphl As HEAPLIST32) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Heap32ListNext(ByVal hSnapshot As IntPtr, ByRef lphl As HEAPLIST32) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Heap32First(ByVal hSnapshot As IntPtr, ByRef lphe As HEAPENTRY32, ByVal th32ProcessID As UInteger, ByVal th32HeapID As UInteger) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Heap32Next(ByVal hSnapshot As IntPtr, ByRef lphe As HEAPENTRY32) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Toolhelp32ReadProcessMemory(ByVal th32ProcessID As UInteger, ByVal lpBaseAddress As IntPtr, ByVal lpBuffer As IntPtr, ByVal cbRead As UInteger, ByVal lpNumberOfBytesRead As IntPtr) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Process32FirstW(ByVal hSnapshot As IntPtr, ByRef lppe As PROCESSENTRY32W) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Process32NextW(ByVal hSnapshot As IntPtr, ByRef lppe As PROCESSENTRY32W) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Process32First(ByVal hSnapshot As IntPtr, ByRef lppe As ProcessEntry32) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Process32Next(ByVal hSnapshot As IntPtr, ByRef lppe As ProcessEntry32) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Thread32First(ByVal hSnapshot As IntPtr, ByRef lpte As THREADENTRY32) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Thread32Next(ByVal hSnapshot As IntPtr, ByRef lpte As THREADENTRY32) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Module32FirstW(ByVal hSnapshot As IntPtr, ByRef lpme As MODULEENTRY32W) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Module32NextW(ByVal hSnapshot As IntPtr, ByRef lpme As MODULEENTRY32W) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Module32First(ByVal hSnapshot As IntPtr, ByRef lpme As MODULEENTRY32W) As Boolean
    End Function

    <DllImport("kernel32.dll")> _
    Private Shared Function Module32Next(ByVal hSnapshot As IntPtr, ByRef lpme As MODULEENTRY32W) As Boolean
    End Function

    <DllImport("kernel32.dll", CharSet:=CharSet.Auto)> _
    Private Shared Function GetModuleFileName(ByVal hModule As HandleRef, ByVal buffer As StringBuilder, ByVal length As Integer) As Integer
    End Function

    Public Shared Function CheckCallAppleDll(ByVal ProcId As Integer) As Boolean
        Dim blnResult As Boolean = False

        Try
            Const INVALID_HANDLE_VALUE As Integer = -1
            Const ERROR_BAD_LENGTH As Integer = &H18

            Dim list As New List(Of String)()
            'list.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonProgramFiles), @"Apple\Apple Application Support"));
            'list.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonProgramFiles), @"Apple\Mobile Device Support"));
            list.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonProgramFiles), "Apple"))
            list.Add(Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonProgramFiles), "Apple").Replace("Program Files\Common Files", "Program Files (x86)\Common Files"))

            Dim hModuleSnapshot As IntPtr = Toolhelp32.CreateToolhelp32Snapshot(TH32CS_SNAPMODULE32 Or TH32CS_SNAPMODULE, CUInt(ProcId))
            If hModuleSnapshot.ToInt32() <> INVALID_HANDLE_VALUE AndAlso hModuleSnapshot.ToInt32() <> ERROR_BAD_LENGTH Then
                Dim mEntry As New MODULEENTRY32W()
                mEntry.dwSize = CUInt(Marshal.SizeOf(mEntry))

                If Module32First(hModuleSnapshot, mEntry) Then
                    Do
                        For Each strDir As String In list
                            If mEntry.szExePath.StartsWith(strDir, StringComparison.OrdinalIgnoreCase) Then
                                System.Diagnostics.Debug.Print(mEntry.szExePath)
                                blnResult = True
                                GoTo DoExit
                            End If


                            '
                            '                            if (Heap32First(hHeapSnapshot, ref HeapEntry, HeapList.th32ProcessID, HeapList.th32HeapID))
                            '                            {
                            '                                do
                            '                                {
                            '                                    MemUsage += HeapEntry.dwBlockSize;
                            '                                } while (Heap32Next(hHeapSnapshot, ref HeapEntry));
                            '                            }
                            '                           


                        Next


                    Loop While Module32Next(hModuleSnapshot, mEntry)
                End If

                Dim lasterr As Integer = Marshal.GetLastWin32Error()
                'System.Windows.Forms.MessageBox.Show(Marshal.GetLastWin32Error().ToString());

                CloseHandle(hModuleSnapshot)
            End If
        Catch ex As Exception
        End Try
DoExit:

        Return blnResult
    End Function
End Class


'=======================================================
'Service provided by Telerik (www.telerik.com)
'Conversion powered by NRefactory.
'Twitter: @telerik
'Facebook: facebook.com/telerik
'=======================================================




