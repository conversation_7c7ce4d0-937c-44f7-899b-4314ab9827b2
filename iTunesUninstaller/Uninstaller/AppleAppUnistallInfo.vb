﻿Imports System.Collections.Generic
Imports System.Text
Imports System.Threading
Imports System.Diagnostics
Imports System.IO
Imports System.ServiceProcess
Imports System.Runtime.InteropServices
Imports Microsoft.Win32

    ''' <summary>
    ''' 苹果应用程序卸载器(主要就是卸载iTunes相关产品)
    ''' </summary>
    Public Class AppUnistaller
        ''' <summary>
        ''' 苹果待卸载程序名字
        ''' </summary>
    Private appleAppNames As String() = {"iTunes", "QuickTime", "Apple Mobile Device Support", "Apple Application Support", "Bonjour", "Apple Software Update", "Apple "}

        ''' <summary>
        ''' 需要删除的iTunes音乐文件夹
        ''' </summary>
        Private iTunesLibrary As String = "\iTunes\iTunes Library.itl"
        '"My Music\\iTunes\\";
        ''' <summary>
        ''' 本机搜索到的苹果iTunes相关程序
        ''' </summary>
        Private appleiTunesApps As New List(Of AppUnistallInfo)()

        ''' <summary>
        ''' 出错日志
        ''' </summary>
        Private log As String = String.Empty

        Private Shared locker As New Object()

        ''' <summary>
        ''' 是否匹配苹果AppleAppNames里面的名字
        ''' </summary>
        ''' <param name="displayName"></param>
        ''' <returns></returns>
        Private Function IsMatchAppleAppName(displayName As String) As Boolean
            If String.IsNullOrEmpty(displayName) Then
                Return False
            End If

            For Each appName As String In Me.appleAppNames
                If displayName.StartsWith(appName, StringComparison.OrdinalIgnoreCase) Then
                    Return True
                End If
            Next

            Return False
    End Function

    ''' <summary>
    ''' 查找本机已安装的iTunes相关程序卸载信息
    ''' </summary>
    Private Sub FindAppleAppUninstallInfo()
        Try
            Dim type As RegistryKey64.RegWow64Options = RegistryKey64.RegWow64Options.KEY_WOW64_32KEY
            If Common.IsOS_Of_64Bit Then
                type = RegistryKey64.RegWow64Options.KEY_WOW64_64KEY
            End If
            Dim installerProductsReg As RegistryKey64 = RegistryKey64.OpenKey(RegistryHive.LocalMachine, "SOFTWARE\Microsoft\Windows\CurrentVersion\Installer\UserData\S-1-5-18\Products", False, type)

            Dim Names As String() = installerProductsReg.GetSubKeyNames()

            For i As Integer = 0 To Names.Length - 1
                Dim tempReg As RegistryKey64 = installerProductsReg.OpenSubKey(Names(i)).OpenSubKey("InstallProperties")

                If tempReg IsNot Nothing Then
                    Dim displayName As String = String.Empty
                    Dim value As Object = tempReg.GetValue("DisplayName")

                    If value IsNot Nothing Then
                        displayName = value.ToString()
                    End If

                    If Not String.IsNullOrEmpty(displayName) Then
                        System.Diagnostics.Debug.Print(displayName)
                    End If

                    If Me.IsMatchAppleAppName(displayName) Then
                        Dim unistallInfo As New AppUnistallInfo()
                        unistallInfo.DisplayName = displayName
                        Dim obj As Object = tempReg.GetValue("UninstallString")
                        If obj Is Nothing Then
                            unistallInfo.UninstallString = String.Empty
                        Else
                            unistallInfo.UninstallString = obj.ToString()
                        End If

                        Me.appleiTunesApps.Add(unistallInfo)

                        If Me.appleiTunesApps.Count = Me.appleAppNames.Length Then
                            Exit For
                            '减少检测时间
                        End If
                    End If
                End If
            Next
        Catch e As Exception
            Me.log += e.Message + Environment.NewLine
            System.Diagnostics.Debug.WriteLine(e)
        End Try
    End Sub

        ''' <summary>
        ''' 删除安装的iTunes相关程序卸载信息
        ''' </summary>
        Public Sub ClearAppleAppUninstallInfo()
            Try
                Dim installerProductsReg As RegistryKey = Registry.LocalMachine.CreateSubKey("SOFTWARE\Microsoft\Windows\CurrentVersion\Installer\UserData\S-1-5-18\Products")

                Dim Names As String() = installerProductsReg.GetSubKeyNames()

                For i As Integer = 0 To Names.Length - 1
                    Dim tempReg As RegistryKey = installerProductsReg.OpenSubKey(Names(i)).OpenSubKey("InstallProperties")

                    If tempReg IsNot Nothing Then
                        Dim displayName As String = String.Empty
                        Dim value As Object = tempReg.GetValue("DisplayName")

                        If value IsNot Nothing Then
                            displayName = value.ToString()
                        End If

                        'if (string.IsNullOrEmpty(displayName) == false)
                        '    System.Diagnostics.Debug.Print(displayName);

                        If Me.IsMatchAppleAppName(displayName) Then
                            installerProductsReg.DeleteSubKeyTree(Names(i))
                        End If
                    End If
                Next
            Catch e As Exception
                Me.log += e.Message + Environment.NewLine
                System.Diagnostics.Debug.WriteLine(e)
            End Try

        End Sub

        ''' <summary>
        ''' 要删除的项目数
        ''' </summary>
        Public ReadOnly Property Count() As Integer
            Get
                Return appleiTunesApps.Count + 1
            End Get
        End Property

        ''' <summary>
        ''' 错误日志
        ''' </summary>
        Public ReadOnly Property ErrorLog() As String
            Get
                Return Me.log
            End Get
        End Property

        ''' <summary>
        ''' 卸载进度事件
        ''' </summary>
        Public Event UninstallProgress As EventHandler(Of UnistallProgressArgs)

        Protected Sub OnUninstallProgress(args As UnistallProgressArgs)
            'If Me.UninstallProgress IsNot Nothing Then
            '    Dim handler As EventHandler(Of UnistallProgressArgs) = Me.UninstallProgress
            '    handler(Me, args)
            'End If
            RaiseEvent UninstallProgress(Me, args)

        End Sub
        ''' <summary>
        ''' 初始构造,搜索本机已安装iTunes程序信息
        ''' </summary>
    Public Sub New()
        Me.FindAppleAppUninstallInfo()
    End Sub

        ''' <summary>
        ''' 开始卸载操作
        ''' </summary>
        Public Sub StartUnistall()
            'if (this.appleiTunesApps.Count == 0)
            '{
            '    UnistallProgressArgs args = new UnistallProgressArgs();
            '    args.Count = this.appleiTunesApps.Count;
            '    args.Current = 0;

            '    this.log += "你的系统已没有安装iTunes相关组件" + Environment.NewLine;
            '    args.Finished = true;
            '    args.Log = this.log;
            '    this.OnUninstallProgress(args);
            '}
            'else
            If True Then
                Dim t As New Thread(New ThreadStart(AddressOf UninstallThread))
                t.IsBackground = True
                t.Start()
            End If
        End Sub

        Private Sub UninstallThread()
            Try
                Dim args As New UnistallProgressArgs()
                args.Count = Me.appleiTunesApps.Count
                args.Current = 0

                Me.KillProcess()

                For Each info As AppUnistallInfo In Me.appleiTunesApps
                    Dim canUninstall As Boolean = False
                    For Each appName As String In Me.appleAppNames
                        If appName = info.DisplayName Then
                            canUninstall = True
                        End If
                    Next

                    If Not canUninstall AndAlso info.DisplayName.Contains("Apple") Then
                        canUninstall = True
                    End If

                    If Not canUninstall Then
                        Continue For
                    End If

                    Dim indexStart As Integer = info.UninstallString.IndexOf("{")
                    Dim indexEnd As Integer = info.UninstallString.IndexOf("}")

                    If indexStart < 0 OrElse indexEnd < 0 Then
                        Continue For
                    End If

                    Dim uuid As String = info.UninstallString.Substring(indexStart, indexEnd + 1 - indexStart)

                    Try
                        '每卸载一个应用后，需要再检查进程是否存在调用情况，有些程序会定时自启动像SGTools
                        Me.KillProcess()

                        Using p As New Process()
                            Dim startInfo As New ProcessStartInfo()
                            '''quiet /passive /qn

                            Dim arguments As String = String.Format("/x{0}  /qb /norestart", uuid)
                            ' "/x{" + info.UninstallString.Split("/".ToCharArray())[1].Split("I{".ToCharArray())[2] + " /qb /norestart";
                            startInfo.FileName = info.UninstallString.Split("/".ToCharArray())(0)
                            startInfo.Arguments = arguments
                            p.StartInfo = startInfo
                            p.Start()
                            Dim installTime As DateTime = DateTime.Now

                            args.Name = info.DisplayName

                            Me.OnUninstallProgress(args)

                            While Not p.HasExited
                                System.Threading.Thread.Sleep(100)
                                '超时5分钟
                                If installTime.AddMinutes(7) < DateTime.Now Then
                                    Exit While
                                End If
                            End While

                            args.Current += 1
                            Me.OnUninstallProgress(args)
                        End Using
                    Catch e As Exception
                        System.Diagnostics.Debug.WriteLine(e)
                        Me.log += e.Message + Environment.NewLine

                        args.Finished = False
                        args.Log += Me.log
                        Me.OnUninstallProgress(args)
                    End Try
                Next

                If args.Current = Me.appleiTunesApps.Count Then
                    Me.DeleteiTunesLib()
                Me.DeleteRubbish()
                Me.DeleteRegister()

                'this.log = "iTunes相关组件卸载完毕！";
                'args.Log = this.log;
                args.Finished = True
                End If
                Me.OnUninstallProgress(args)
            Catch
            End Try
        End Sub

        ''' <summary>
        ''' 删除iTunes文件夹
        ''' </summary>
        ''' <returns></returns>
        Public Function DeleteiTunesLib() As Boolean
            Dim myMusicFolder As String = Environment.GetFolderPath(Environment.SpecialFolder.MyMusic)
            Dim strItlFile As String = myMusicFolder & Me.iTunesLibrary
            If System.IO.File.Exists(strItlFile) Then
                Try
                    System.IO.File.Delete(strItlFile)
                    Return True
                Catch e As Exception
                    System.Diagnostics.Debug.WriteLine(e)

                    Return False
                End Try
            End If

            Return True
    End Function

    Public Sub DeleteRegister()
        Try
            Dim key As RegistryKey64 = Nothing
            Dim keyDel As RegistryKey64 = Nothing
            Dim type As RegistryKey64.RegWow64Options = RegistryKey64.RegWow64Options.KEY_WOW64_32KEY
            If Common.IsOS_Of_64Bit Then
                type = RegistryKey64.RegWow64Options.KEY_WOW64_64KEY
            End If

            key = RegistryKey64.OpenKey(RegistryHive.CurrentUser, "Software", True, type)
            If key IsNot Nothing Then
                keyDel = key.OpenSubKey("Apple Computer, Inc.")
                'HKEY_CURRENT_USER\Software\Apple Computer, Inc.
                If keyDel IsNot Nothing Then
                    keyDel.Close()
                    key.DeleteSubKey("Apple Computer, Inc.")
                End If

                keyDel = key.OpenSubKey("Apple Inc.")
                'HKEY_CURRENT_USER\Software\Apple Inc.
                If keyDel IsNot Nothing Then
                    keyDel.Close()
                    key.DeleteSubKey("Apple Inc.")
                End If
                key.Close()
            End If

            key = RegistryKey64.OpenKey(RegistryHive.LocalMachine, "Software", True, type)
            If key IsNot Nothing Then
                keyDel = key.OpenSubKey("Apple Computer, Inc.")
                'HKEY_LOCAL_MACHINE\SOFTWARE\Apple Computer, Inc.
                If keyDel IsNot Nothing Then
                    keyDel.Close()
                    key.DeleteSubKey("Apple Computer, Inc.")
                End If

                keyDel = key.OpenSubKey("Apple Inc.")
                'HKEY_CURRENT_USER\Software\Apple Inc.
                If keyDel IsNot Nothing Then
                    keyDel.Close()
                    key.DeleteSubKey("Apple Inc.")
                End If
                key.Close()
            End If
        Catch
        End Try
    End Sub

        Public Sub DeleteRubbish()
            Dim strDir As String = String.Empty
            Try
                strDir = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "iTunes")
                If Directory.Exists(strDir) Then
                    Directory.Delete(strDir, True)
                End If
            Catch
            End Try
            Try
                strDir = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "iPod")
                If Directory.Exists(strDir) Then
                    Directory.Delete(strDir, True)
                End If
            Catch
            End Try
            Try
                strDir = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "QuickTime")
                If Directory.Exists(strDir) Then
                    Directory.Delete(strDir, True)
                End If
            Catch
            End Try
            Try
                strDir = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "Common Files\Apple")
                If Directory.Exists(strDir) Then
                    Directory.Delete(strDir, True)
                End If
            Catch
            End Try
            Try
                strDir = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.System), "drivers\usbaapl.sys")
                If File.Exists(strDir) Then
                    File.Delete(strDir)
                End If
            Catch
            End Try
            Try
                strDir = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.System), "drivers\usbaapl64.sys")
                If File.Exists(strDir) Then
                    File.Delete(strDir)
                End If
            Catch
            End Try
        End Sub

        Private Sub KillProcess()
            Try
                Dim arrSvr As ServiceController() = ServiceController.GetServices()
                For Each svr As ServiceController In arrSvr
                    Try
                        Select Case svr.ServiceName
                            Case "Apple Mobile Device", "iPod Service", "91MobileDevice", "91DeviceService", "PP Assistant Service", "KYDeviceServer", _
                                "XYService"
                                'case "":
                                If svr.CanStop Then
                                    svr.[Stop]()
                                End If
                                Exit Select
                        End Select
                    Catch
                    End Try
                Next

                For Each p As Process In Process.GetProcesses()
                    Try
                        Select Case p.ProcessName.Trim()


                            Case "AppleMobileDeviceService", "AppleMobileDeviceHelper", "SoftwareUpdate", "iPodService", "QuickTimePlayer", "iTunes", _
                                "iTunesHelper", "APSDaemon", "iPhone PC Suite", "91 PC Suite", "91Mobile", "91MobileDeviceService", _
                                "91Assist", "91DeviceService", "AUTH91", "iTunesMonitor", "Tongbu", "tbLite", _
                                "iFunBox", "iTools", "iToolsDaemon", "iToolsGadget", "ihelper", "SyncServer", _
                                "快用苹果助手", "XYLauncher", "XYService", "XYClient", "SGTool"
                                Debug.Print(p.ProcessName)
                                p.Kill()
                                Exit Select
                            Case Else

                            If Toolhelp32.CheckCallAppleDll(p.Id) Then
                                Debug.Print(p.ProcessName)
                                p.Kill()
                            End If

                                Exit Select
                        End Select
                    Catch
                    End Try
                Next
            Catch
            End Try
        End Sub
    End Class

    ''' <summary>
    '''卸载进度事件参数
    ''' </summary>
    Public Class UnistallProgressArgs
        Inherits EventArgs
        Private mName As String = String.Empty
        Private mLog As String = String.Empty
        Private mCount As Integer = 0
        Private mCurrent As Integer = 0
        Private mFinished As Boolean = False

        ''' <summary>
        ''' 当前卸载程序名字
        ''' </summary>        
        Public Property Name() As String
            Get
                Return mName
            End Get
            Set(value As String)
                mName = value
            End Set
        End Property

        ''' <summary>
        ''' 总的需要卸载数
        ''' </summary>
        Public Property Count() As Integer
            Get
                Return mCount
            End Get
            Set(value As Integer)
                mCount = value
            End Set
        End Property

        ''' <summary>
        ''' 卸载相关信息
        ''' </summary>
        Public Property Log() As String
            Get
                Return mLog
            End Get
            Set(value As String)
                mLog = value
            End Set
        End Property

        ''' <summary>
        ''' 是否完成
        ''' </summary>
        Public Property Finished() As Boolean
            Get
                Return mFinished
            End Get
            Set(value As Boolean)
                mFinished = value
            End Set
        End Property

        ''' <summary>
        ''' 当前卸载数
        ''' </summary>
        Public Property Current() As Integer
            Get
                Return mCurrent
            End Get
            Set(value As Integer)
                mCurrent = value
            End Set
        End Property
    End Class

    ''' <summary>
    ''' 应用程序卸载信息
    ''' </summary>
    Public Class AppUnistallInfo
        Private mDisplayName As String = String.Empty
        Private mUninstallString As String = String.Empty

        Public Property DisplayName() As String
            Get
                Return mDisplayName
            End Get
            Set(value As String)
                mDisplayName = value
            End Set
        End Property

        Public Property UninstallString() As String
            Get
                Return mUninstallString
            End Get
            Set(value As String)
                mUninstallString = value
            End Set
        End Property
End Class

'=======================================================
'Service provided by Telerik (www.telerik.com)
'Conversion powered by NRefactory.
'Twitter: @telerik
'Facebook: facebook.com/telerik
'=======================================================
