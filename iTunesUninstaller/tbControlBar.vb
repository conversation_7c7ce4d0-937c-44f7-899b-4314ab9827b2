﻿Imports System.Drawing
Imports System.Drawing.Imaging
Imports System.Drawing.Drawing2D

<System.ComponentModel.DefaultEvent("ProgressChanged")> _
Public Class tbControlBar
    Inherits System.Windows.Forms.Control

    Public Enum tbBarDirection
        Horizontal
        Vertical
    End Enum

    Private mBarImageBg As Image = My.Resources.player_progress_bg
    Private mBarImageDown As Image = My.Resources.player_progress_bg
    Private mBarImagePlay As Image = My.Resources.player_progress_play

    Private mBarRectBg As Rectangle = Rectangle.Empty
    Private mBarRectPlay As Rectangle = Rectangle.Empty
    Private mBarRectDown As Rectangle = Rectangle.Empty

    Private mDotRect As Rectangle = Rectangle.Empty
    Private mDotSize As Size = Drawing.Size.Empty
    Private mDotImageLeave As Image = Nothing
    Private mDotImageDown As Image = Nothing
    Private mDotImageHover As Image = Nothing
    Private mDotImage As Image = Nothing
    Private mDotImageState As ImageState = ImageState.OneState
    Private mShowDot As Boolean = True

    Private mMouseStatus As MouseStates = MouseStates.MouseLeave
    Private mMouseDown As Boolean = False
    Private mMouseOnDot As Boolean = False

    Private mPlayValueTemp As Integer = 0
    Private mPlayMax As Integer = 100
    Private mPlayValue As Integer = 50
    Private mPlayMin As Integer = 0

    Private mDirectory As tbBarDirection = tbBarDirection.Horizontal

    Private mDownMax As Integer = 100
    Private mDownValue As Integer = 0
    Private mDownMin As Integer = 0

    Private mSplit As tbSplitStructure = tbSplitStructure.Empty

    Private mShowText As Boolean = False
    Private mIsWaiting As Boolean = False
    Private mTimerWaiting As System.Timers.Timer
    Private mintWaittingValue As Integer = 0
    Private mProgressWaitingImage As Image = Nothing
    Private mblnCanDragValue As Boolean = True

    Public Event ProgressChanged As EventHandler(Of ProgressEventArgs)
    Public Event ProgressChanging As EventHandler(Of ProgressEventArgs)

    Public Property tbCanDragValue() As Boolean
        Get
            Return Me.mblnCanDragValue
        End Get
        Set(ByVal value As Boolean)
            Me.mblnCanDragValue = value
        End Set
    End Property

    Public Property tbDirection() As tbBarDirection
        Get
            Return Me.mDirectory
        End Get
        Set(ByVal value As tbBarDirection)
            Me.mDirectory = value

            If Me.mDirectory = tbBarDirection.Horizontal Then
                Me.tbPlayImage = My.Resources.player_progress_play
                Me.tbDownloadImage = My.Resources.player_progress_play
                Me.tbBackgroundImage = My.Resources.player_progress_bg
            Else
                Me.tbPlayImage = My.Resources.player_progress_play_H
                Me.tbDownloadImage = My.Resources.player_progress_play_H
                Me.tbBackgroundImage = My.Resources.player_progress_bg_H
            End If

            Me.InitRect()
            Me.Invalidate()
        End Set
    End Property

    Public Property tbPlayMax() As Integer
        Get
            Return Me.mPlayMax
        End Get
        Set(ByVal value As Integer)
            If value > Me.mPlayMin Then
                Me.mPlayMax = value
                Me.Invalidate()
            End If
        End Set
    End Property

    Public Property tbPlayMin() As Integer
        Get
            Return Me.mPlayMin
        End Get
        Set(ByVal value As Integer)
            If value < Me.mPlayMax Then
                Me.mPlayMin = value
                Me.Invalidate()
            End If
        End Set
    End Property

    Public Property tbPlayValue() As Integer
        Get
            Return Me.mPlayValue
        End Get
        Set(ByVal value As Integer)
            If Me.mMouseDown = False AndAlso value >= Me.mPlayMin AndAlso value <= Me.mPlayMax Then
                Me.mPlayValue = value
                Me.InitPlayRect()
                Me.Invalidate()

            End If
        End Set
    End Property

    Public Property tbDownMax() As Integer
        Get
            Return Me.mDownMax
        End Get
        Set(ByVal value As Integer)
            If value > Me.mDownMin Then
                Me.mDownMax = value
                Me.Invalidate()
            End If
        End Set
    End Property

    Public Property tbDownMin() As Integer
        Get
            Return Me.mDownMin
        End Get
        Set(ByVal value As Integer)
            If value < Me.mDownMax Then
                Me.mDownMin = value
                Me.Invalidate()
            End If
        End Set
    End Property

    Public Property tbDownValue() As Integer
        Get
            Return Me.mDownValue
        End Get
        Set(ByVal value As Integer)
            If value >= Me.mDownMin AndAlso value <= Me.mDownMax Then
                Me.mDownValue = value
                Me.InitRect()
                Me.Invalidate()
            End If
        End Set
    End Property

    Public Property tbDotMouseLeave() As Image
        Get
            Return Me.mDotImageLeave
        End Get
        Set(ByVal value As Image)
            Me.mDotImageLeave = value

            If value IsNot Nothing Then
                Me.mDotSize = value.Size
                Me.InitPlayRect()
            End If

            Me.Invalidate()
        End Set
    End Property

    Public Property tbDotMouseHover() As Image
        Get
            Return Me.mDotImageHover
        End Get
        Set(ByVal value As Image)
            Me.mDotImageHover = value

            If value IsNot Nothing Then
                Me.mDotSize = value.Size
                Me.InitPlayRect()
            End If

            Me.Invalidate()
        End Set
    End Property

    Public Property tbDotMouseDown() As Image
        Get
            Return Me.mDotImageDown
        End Get
        Set(ByVal value As Image)
            Me.mDotImageDown = value

            If value IsNot Nothing Then
                Me.mDotSize = value.Size
                Me.InitPlayRect()
            End If

            Me.Invalidate()
        End Set
    End Property

    Public Property tbDotImage() As Image
        Get
            Return Me.mDotImage
        End Get
        Set(ByVal value As Image)
            If Me.mDotImage IsNot value Then
                Me.mDotImage = value
                Me.mDotSize = New Size(Me.mDotImage.Size.Width \ CInt(Me.mDotImageState), Me.mDotImage.Size.Height)
                Me.InitPlayRect()
                Me.Invalidate()
            End If
        End Set
    End Property

    Public Property tbDotImageState() As ImageState
        Get
            Return Me.mDotImageState
        End Get
        Set(ByVal value As ImageState)
            Me.mDotImageState = value
            Me.mDotSize = New Size(Me.mDotImage.Size.Width \ CInt(Me.mDotImageState), Me.mDotImage.Size.Height)
            Me.InitPlayRect()
            Me.Invalidate()
        End Set
    End Property

    Public Property tbBackgroundImage() As Image
        Get
            Return Me.mBarImageBg
        End Get
        Set(ByVal value As Image)
            Me.mBarImageBg = value

            Me.Invalidate()
        End Set
    End Property

    Public Property tbPlayImage() As Image
        Get
            Return Me.mBarImagePlay
        End Get
        Set(ByVal value As Image)
            Me.mBarImagePlay = value

            Me.Invalidate()
        End Set
    End Property

    Public Property tbDownloadImage() As Image
        Get
            Return Me.mBarImageDown
        End Get
        Set(ByVal value As Image)
            Me.mBarImageDown = value

            Me.Invalidate()
        End Set
    End Property

    Public Property tbSplit() As String
        Get
            Return Me.mSplit.GetString()
        End Get
        Set(ByVal value As String)
            Me.mSplit = New tbSplitStructure(value)
            Me.Invalidate()
        End Set
    End Property

    Public Property tbShowDot() As Boolean
        Get
            Return mShowDot
        End Get
        Set(ByVal value As Boolean)
            Me.mShowDot = value
            Me.InitPlayRect()
            Me.Invalidate()
        End Set
    End Property

    Public Property tbShowText() As Boolean
        Get
            Return Me.mShowText
        End Get
        Set(ByVal value As Boolean)
            Me.mShowText = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbIsWaiting() As Boolean
        Get
            Return Me.mIsWaiting
        End Get
        Set(ByVal value As Boolean)
            Me.mIsWaiting = value

            If Me.mIsWaiting = True Then
                If Me.mTimerWaiting Is Nothing Then
                    Me.mTimerWaiting = New System.Timers.Timer
                    With Me.mTimerWaiting
                        .Interval = 100
                        RemoveHandler mTimerWaiting.Elapsed, AddressOf TimerWaiting_Tick
                        AddHandler mTimerWaiting.Elapsed, AddressOf TimerWaiting_Tick
                    End With
                End If

                Me.mProgressWaitingImage = GuiResource.GetWaitingImageFromValue(1)
                Me.mTimerWaiting.Start()
            Else
                If Me.mTimerWaiting IsNot Nothing Then
                    Me.mTimerWaiting.Stop()
                End If
                Me.mProgressWaitingImage = Nothing
            End If

            Me.Invalidate()
        End Set
    End Property

    Private Sub TimerWaiting_Tick(ByVal sender As Object, ByVal e As System.Timers.ElapsedEventArgs)
        Me.mintWaittingValue += 1
        If Me.mintWaittingValue > 10 AndAlso Me.mintWaittingValue <= 0 Then
            Me.mintWaittingValue = 1
        End If

        Me.mProgressWaitingImage = GuiResource.GetWaitingImageFromValue(Me.mintWaittingValue)
        Me.Invalidate()
    End Sub

    Public Sub New()
        Me.SetStyle(ControlStyles.UserPaint, True)
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint, True)
        Me.SetStyle(ControlStyles.OptimizedDoubleBuffer, True)
        Me.SetStyle(ControlStyles.SupportsTransparentBackColor, True)

        Me.BackColor = Color.Transparent
        Me.Size = New Size(250, 13)

        Me.mDotImageState = ImageState.ThreeState
        Me.tbDotImage = My.Resources.player_progress_dot
        'Me.Font = Common.CreateFont("Arial", 8, FontStyle.Regular)
    End Sub

    Private Sub InitRect()
        Me.mBarRectBg = New Rectangle(Me.Padding.Left, Me.Padding.Top, Me.Width - Me.Padding.Horizontal, Me.Height - Me.Padding.Vertical)

        If Me.mDirectory = tbBarDirection.Horizontal Then
            Me.mBarRectDown = New Rectangle(mBarRectBg.Left, mBarRectBg.Top, mBarRectBg.Width * (Me.mDownValue - Me.mDownMin) / (Me.mDownMax - Me.mDownMin), Me.mBarRectBg.Height)
        Else
            Dim intH As Integer = mBarRectBg.Height * (Me.mDownValue - Me.mDownMin) / (Me.mDownMax - Me.mDownMin)
            Me.mBarRectDown = New Rectangle(mBarRectBg.Left, mBarRectBg.Bottom - intH, Me.mBarRectBg.Width, intH)
        End If

        Me.InitPlayRect()
    End Sub

    Private Sub InitPlayRect()
        Dim tmpValue As Integer = Me.mPlayValue

        If mMouseDown Then
            tmpValue = Me.mPlayValueTemp
        End If

        Dim dot_Bar_diff As Integer = 2
        Dim dot_raduis As Integer = 9
        If Me.mDirectory = tbBarDirection.Horizontal Then
            Me.mBarRectPlay = New Rectangle(mBarRectBg.Left, mBarRectBg.Top, mBarRectBg.Width * (tmpValue - Me.mPlayMin) / (Me.mPlayMax - Me.mPlayMin), Me.mBarRectBg.Height)
            Me.mDotRect = New Rectangle(mBarRectPlay.Left + mBarRectPlay.Width - Math.Ceiling(mDotSize.Width / 2), mBarRectPlay.Top + (mBarRectPlay.Height - mDotSize.Height) / 2, mDotSize.Width, mDotSize.Height)

            If Me.mDotRect.Left < Me.mBarRectBg.Left - dot_Bar_diff Then
                Me.mDotRect = New Rectangle(Me.mBarRectBg.Left - dot_Bar_diff, Me.mDotRect.Top, Me.mDotRect.Width, Me.mDotRect.Height)
            End If

            If Me.mDotRect.Right > Me.mBarRectBg.Right + dot_Bar_diff Then
                Me.mDotRect = New Rectangle(Me.mBarRectBg.Right + dot_Bar_diff - Me.mDotRect.Width, Me.mDotRect.Top, Me.mDotRect.Width, Me.mDotRect.Height)
            End If

            If mShowDot Then
                Me.mBarRectPlay = New Rectangle(mBarRectPlay.Left, mBarRectPlay.Top, mBarRectPlay.Width, mBarRectPlay.Height)
                If Me.mBarRectPlay.Width < dot_raduis Then
                    Me.mBarRectPlay = New Rectangle(mBarRectPlay.Left, mBarRectPlay.Top, dot_raduis, mBarRectPlay.Height)
                End If
            End If

        Else

            Dim intH As Integer = mBarRectBg.Height * (tmpValue - Me.mPlayMin) / (Me.mPlayMax - Me.mPlayMin)
            Me.mBarRectPlay = New Rectangle(mBarRectBg.Left, mBarRectBg.Bottom - intH, Me.mBarRectBg.Width, intH)
            Me.mDotRect = New Rectangle(mBarRectPlay.Left + (mBarRectPlay.Width - mDotSize.Width) / 2, mBarRectPlay.Top - Math.Ceiling(mDotSize.Height / 2), mDotSize.Width, mDotSize.Height)

            If Me.mDotRect.Top < Me.mBarRectBg.Top - dot_Bar_diff Then
                Me.mDotRect = New Rectangle(Me.mDotRect.Left, Me.mBarRectBg.Top - dot_Bar_diff, Me.mDotRect.Width, Me.mDotRect.Height)
            End If

            If Me.mDotRect.Bottom > Me.mBarRectBg.Bottom + dot_Bar_diff Then
                Me.mDotRect = New Rectangle(Me.mDotRect.Left, Me.mBarRectBg.Bottom + dot_Bar_diff - Me.mDotRect.Height, Me.mDotRect.Width, Me.mDotRect.Height)
            End If

            If mShowDot Then
                Me.mBarRectPlay = New Rectangle(mBarRectPlay.Left, mBarRectPlay.Top, mBarRectPlay.Width, mBarRectPlay.Height)
                If Me.mBarRectPlay.Height < dot_raduis Then
                    Me.mBarRectPlay = New Rectangle(mBarRectPlay.Left, mBarRectPlay.Top, mBarRectPlay.Width, dot_raduis)
                End If
            End If

        End If
    End Sub

    Private Sub UpdatePlayValueTemp(ByVal e As MouseEventArgs)
        If Me.mDirectory = tbBarDirection.Horizontal Then
            '拖动进度按钮   
            Dim intX As Integer = e.X
            If intX < Me.mBarRectBg.Left Then
                intX = Me.mBarRectBg.Left
            ElseIf intX > Me.mBarRectBg.Right Then
                intX = Me.mBarRectBg.Right
            End If
            Me.mPlayValueTemp = (intX - Me.mBarRectBg.Left) / Me.mBarRectBg.Width * (Me.mPlayMax - Me.mPlayMin) + Me.mPlayMin

        Else
            '拖动进度按钮   
            Dim intY As Integer = e.Y
            If intY < Me.mBarRectBg.Top Then
                intY = Me.mBarRectBg.Top
            ElseIf intY > Me.mBarRectBg.Bottom Then
                intY = Me.mBarRectBg.Bottom
            End If
            Me.mPlayValueTemp = (Me.mBarRectBg.Bottom - intY) / Me.mBarRectBg.Height * (Me.mPlayMax - Me.mPlayMin) + Me.mPlayMin

        End If
    End Sub

    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        MyBase.OnResize(e)

        Me.InitRect()
    End Sub

    Protected Overrides Sub OnPaddingChanged(ByVal e As System.EventArgs)
        MyBase.OnPaddingChanged(e)

        Me.InitRect()
        Me.Invalidate()
    End Sub

    Protected Overrides Sub OnMouseEnter(ByVal e As System.EventArgs)
        MyBase.OnMouseEnter(e)

        If Me.mMouseStatus <> MouseStates.MouseEnter Then
            Me.mMouseStatus = MouseStates.MouseEnter
            Me.Invalidate()
        End If
    End Sub

    Protected Overrides Sub OnMouseLeave(ByVal e As System.EventArgs)
        MyBase.OnMouseLeave(e)

        If Me.mMouseStatus <> MouseStates.MouseLeave Then
            Me.mMouseStatus = MouseStates.MouseLeave
            Me.Invalidate()
        End If
    End Sub

    Protected Overrides Sub OnMouseDown(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseDown(e)

        If Not Me.tbCanDragValue Then
            Return
        End If

        Me.mMouseDown = True

        Me.UpdatePlayValueTemp(e)
        Me.InitPlayRect()
        Me.Invalidate()
    End Sub

    Protected Overrides Sub OnMouseUp(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseUp(e)

        If Not Me.tbCanDragValue Then
            Return
        End If

        Me.mMouseDown = False

        Me.UpdatePlayValueTemp(e)

        Dim args As New ProgressEventArgs(mPlayValueTemp)
        RaiseEvent ProgressChanged(Me, args)
        If args.Cancel = False Then
            Me.mPlayValue = Me.mPlayValueTemp
        End If

        Me.InitPlayRect()
        Me.Invalidate()
    End Sub

    Protected Overrides Sub OnMouseMove(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseMove(e)

        If Me.mMouseOnDot Then
            Me.UpdatePlayValueTemp(e)
            Me.InitPlayRect()
            Me.Invalidate()

            If Me.mMouseDown Then
                Dim args As New ProgressEventArgs(mPlayValueTemp)
                args.Position = Me.PointToScreen(New Point(Me.mDotRect.Left + Me.mDotRect.Width \ 2, Me.mDotRect.Top))
                RaiseEvent ProgressChanging(Me, args)
            End If

        Else
            '进入进度按钮区域
            If Me.mDotRect.Contains(e.Location) Then
                If mMouseOnDot = False Then
                    Me.mMouseOnDot = True
                    Me.Invalidate()
                End If

            Else
                If mMouseOnDot Then
                    Me.mMouseOnDot = False
                    Me.Invalidate()
                End If

            End If
        End If

    End Sub

    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        MyBase.OnPaint(e)

        Dim g As Graphics = e.Graphics
        g.InterpolationMode = InterpolationMode.HighQualityBicubic

        '等待状态
        If Me.mIsWaiting AndAlso Me.mProgressWaitingImage IsNot Nothing Then
            GuiHelper.DrawImage(g, Me.mBarRectBg, tbSplitStructure.Empty, Me.mProgressWaitingImage, True, MouseStates.MouseLeave, ImageState.OneState, ImageLayout.Horizontal)
            Return
        End If

        GuiHelper.DrawImage(g, Me.mBarRectBg, Me.mSplit, Me.mBarImageBg)

        If mBarRectDown.Width > 0 Then
            GuiHelper.DrawImage(g, Me.mBarRectDown, Me.mSplit, Me.mBarImageDown)
        End If

        If (Me.mMouseDown OrElse Me.mPlayValue - Me.mPlayMin > 0) AndAlso mBarRectPlay.Width > 0 Then
            GuiHelper.DrawImage(g, Me.mBarRectPlay, Me.mSplit, Me.mBarImagePlay)
        End If

        If mShowDot Then
            If Me.mMouseDown AndAlso Me.mMouseOnDot AndAlso Me.mDotImageDown IsNot Nothing Then
                GuiHelper.DrawImage(g, Me.mDotRect, tbSplitStructure.Empty, Me.mDotImageDown, Me.Enabled, MouseStates.MouseDown, ImageState.OneState)

            ElseIf Me.mMouseOnDot AndAlso Me.mDotImageHover IsNot Nothing Then
                GuiHelper.DrawImage(g, Me.mDotRect, tbSplitStructure.Empty, Me.mDotImageHover, Me.Enabled, MouseStates.MouseHover, ImageState.OneState)

            ElseIf Me.mDotImageLeave IsNot Nothing Then
                GuiHelper.DrawImage(g, Me.mDotRect, tbSplitStructure.Empty, Me.mDotImageLeave, Me.Enabled, MouseStates.MouseLeave, ImageState.OneState)

            ElseIf Me.mDotImage IsNot Nothing Then
                Dim mouseState As MouseStates = MouseStates.MouseLeave
                If Me.mMouseDown Then
                    mouseState = MouseStates.MouseDown
                ElseIf Me.mMouseOnDot Then
                    mouseState = MouseStates.MouseHover
                End If

                GuiHelper.DrawImage(g, Me.mDotRect, tbSplitStructure.Empty, Me.mDotImage, Me.Enabled, mouseState, Me.mDotImageState)

            End If
        End If

        '文字
        If Me.mShowText Then
            Dim intValue As Integer = Me.tbPlayValue / (Me.tbPlayMax - Me.tbPlayMin) * 100
            Dim strValue As String = String.Format("{0}%", intValue)

            GuiHelper.DrawString(g, strValue, Me.Font, Me.mBarRectBg, Color.Black, ContentAlignment.MiddleCenter)
        End If

    End Sub

End Class

Public Class ProgressEventArgs
    Inherits System.ComponentModel.CancelEventArgs

    Private mProgressValue As Integer = 0
    Public Property ProgressValue() As Integer
        Get
            Return Me.mProgressValue
        End Get
        Set(ByVal value As Integer)
            Me.mProgressValue = value
        End Set
    End Property

    Public Sub New()

    End Sub

    Public Sub New(ByVal progressValue As Integer)
        Me.mProgressValue = progressValue
    End Sub

    Private mPosition As Point = Point.Empty
    Public Property Position() As Point
        Get
            Return Me.mPosition
        End Get
        Set(ByVal value As Point)
            Me.mPosition = value
        End Set
    End Property

End Class
