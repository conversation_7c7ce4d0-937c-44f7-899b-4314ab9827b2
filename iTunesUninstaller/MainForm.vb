﻿Public Class MainForm

    Protected Delegate Sub Display(ByVal sender As Object, ByVal e As UnistallProgressArgs)
    Private manager As AppUnistaller = Nothing
    Private mRunInChina As Boolean = True
    Private mCurrentUninstall As String = ""

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.StartPosition = FormStartPosition.CenterParent
        Me.CanResize = False
        Me.Icon = My.Resources.iTong
        Me.progressBar1.tbBackgroundImage = My.Resources.app_downloads_background

        Me.manager = New AppUnistaller()
        RemoveHandler Me.manager.UninstallProgress, AddressOf UninstallProgress
        AddHandler Me.manager.UninstallProgress, AddressOf UninstallProgress

        Me.Size = New Size(522, 346)
        Me.InitPanel()
        Me.SetView(True)
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        If Me.mRunInChina Then
            Me.Text = String.Format("iTunes卸载工具 - v{0}", Application.ProductVersion)
            Me.lblUninstall.Text = "您确定要卸载iTunes吗？"
            Me.btnStart.Text = "确定"
            Me.btnCancel.Text = "取消"
            Me.lbCount.Text = "正在卸载...."
        Else
            Me.Text = String.Format("iTunes Uninstaller - v{0}", Application.ProductVersion)
            Me.lblUninstall.Text = "Are You Sure You Want to Uninstall iTunes?"
            Me.btnStart.Text = "OK"
            Me.btnCancel.Text = "Cancel"
            Me.lbCount.Text = "Uninstall ...."
        End If
    End Sub

    Private Sub InitPanel()
        Me.pnlTop.Size = New Size(520, 313)
        Me.pnlTop.Location = New Point(1, 32)
        Me.pnlBottom.Size = New Size(520, 313)
        Me.pnlBottom.Location = New Point(1, 32)
    End Sub

    Private Sub SetView(ByVal showTop As Boolean)
        If showTop Then
            Me.pnlTop.BringToFront()
        Else
            Me.pnlBottom.BringToFront()
        End If
    End Sub

    Private Sub UninstallProgress(ByVal sender As Object, ByVal e As UnistallProgressArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New Display(AddressOf UninstallProgress), sender, e)
        Else
            If Me.mRunInChina Then
                Me.lbCount.Text = String.Format("总共有 {0} 项需要卸载", e.Count)
                If Me.mCurrentUninstall <> e.Name Then
                    Me.tbLog.Text &= vbCrLf & String.Format(" 卸载 : {0}", e.Name)
                    Me.mCurrentUninstall = e.Name
                End If
            Else
                Me.lbCount.Text = String.Format("Total {0} to be uninstalled", e.Count)
                If Me.mCurrentUninstall <> e.Name Then
                    Me.tbLog.Text &= vbCrLf & String.Format(" Uninstalling : {0}", e.Name)
                    Me.mCurrentUninstall = e.Name
                End If
            End If

            If e.Finished Then
                If Me.mRunInChina Then
                    e.Log = " iTunes相关组件卸载完毕！"
                Else
                    e.Log = " Uninstall iTunes success!"
                End If
            End If
            If e.Log.Length > 0 Then
                Me.tbLog.Text &= vbCrLf & e.Log
            End If

            If e.Finished Then
                Me.progressBar1.tbPlayValue = 100
                MessageBox.Show(e.Log)
                Me.Close()
            Else
                If e.Count = 0 Then
                    Me.progressBar1.tbPlayValue = 0
                ElseIf e.Current < e.Count Then
                    Me.progressBar1.tbPlayValue = e.Current / e.Count * 100
                Else
                    Me.progressBar1.tbPlayValue = 100
                End If
            End If
        End If
    End Sub

    Private Sub btnStart_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStart.Click
        Dim strMsg As String = String.Empty
        Dim strInfo As String = String.Empty

        If Me.mRunInChina Then
            strMsg = "在开始卸载iTunes组件前，我们将关闭iTunes以及第三方iOS管理工具，请您先保存数据以免数据丢失。" & vbCrLf & "您确定要立即开始卸载吗？"
            strInfo = "警告"
        Else
            strMsg = "iTunes and third-party iOS management tool will be closed, please make sure you saved important data." & vbCrLf & "Uninstall now?"
            strInfo = "Warning"
        End If

        If MessageBox.Show(strMsg, strInfo, MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
            Me.SetView(False)
            Me.tbLog.Text = String.Empty
            Me.progressBar1.tbPlayValue = 0
            Me.btnStart.Enabled = False
            Me.manager.StartUnistall()
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

#Region "--- 切换皮肤---"

    Protected Overrides Sub WndProc(ByRef m As System.Windows.Forms.Message)
        If m.Msg = &H4A AndAlso CInt(m.WParam) = &HF120 Then
            Try
                Dim mystr As COPYDATASTRUCT = New COPYDATASTRUCT
                Dim mytype As Type = mystr.GetType
                mystr = CType(m.GetLParam(mytype), COPYDATASTRUCT)
                Dim strArgs As String = mystr.lpData
                '重新加载皮肤
                If strArgs = "ReloadSkin" Then
                    Me.ReloadSkinThread()
                End If
            Catch ex As Exception

            End Try
        End If

        MyBase.WndProc(m)
    End Sub

    Private Sub ReloadSkinThread()
        Try
            Skin.Instance.GetSkin(True)
            For Each frmOpen As Form In Application.OpenForms
                Me.tbGuiBackground = Global.iTunesUninstaller.My.Resources.Resources.frm_bg_blank
            Next
        Catch ex As Exception

        End Try
    End Sub

#End Region

End Class