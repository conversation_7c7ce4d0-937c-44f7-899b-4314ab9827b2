﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{4B57464F-A730-4846-8FC6-E7C3F92C77E9}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>iTunesUninstaller.My.MyApplication</StartupObject>
    <RootNamespace>iTunesUninstaller</RootNamespace>
    <AssemblyName>iTunesUninstaller</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <DefineConstants>
    </DefineConstants>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Resources\iTong.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="iTong.CoreFoundation" />
    <Import Include="iTong.CoreReses" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common\GuiHelper.vb" />
    <Compile Include="Common\GuiResource.vb" />
    <Compile Include="Common\GuiStructure.vb" />
    <Compile Include="Common\NativeMethods.vb" />
    <Compile Include="Language.vb" />
    <Compile Include="MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Include="MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="tbControlBar.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="tbControls.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="tbForm\tbBase.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="tbForm\tbBaseForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="tbForm\tbBaseGuiForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Uninstaller\AppleAppUnistallInfo.vb" />
    <Compile Include="Uninstaller\ToolHelper32.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Resources\app_downloads_background.png" />
    <Content Include="Resources\app_downloads_value.png" />
    <Content Include="Resources\btn_4_blue.png" />
    <Content Include="Resources\btn_4_white.png" />
    <Content Include="Resources\btn_close.png" />
    <Content Include="Resources\frm_bg_blank.png" />
    <Content Include="Resources\icon_itunes.png" />
    <Content Include="Resources\icon_itunesuninstall.png" />
    <Content Include="Resources\iTong.ico" />
    <Content Include="Resources\player_progress_bg.png" />
    <Content Include="Resources\player_progress_bg_H.png" />
    <Content Include="Resources\player_progress_dot.png" />
    <Content Include="Resources\player_progress_play.png" />
    <Content Include="Resources\player_progress_play_H.png" />
    <Content Include="Resources\ProgressWaitting001.png" />
    <Content Include="Resources\ProgressWaitting002.png" />
    <Content Include="Resources\ProgressWaitting003.png" />
    <Content Include="Resources\ProgressWaitting004.png" />
    <Content Include="Resources\ProgressWaitting005.png" />
    <Content Include="Resources\ProgressWaitting006.png" />
    <Content Include="Resources\ProgressWaitting007.png" />
    <Content Include="Resources\ProgressWaitting008.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CoreMisc\CoreMisc46.csproj">
      <Project>{596775d3-3eea-4125-bac2-934df77fbeba}</Project>
      <Name>CoreMisc46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses46.csproj">
      <Project>{a5844815-737d-486e-b7de-d57262f9e5f4}</Project>
      <Name>CoreReses46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS46.csproj">
      <Project>{c0b96f67-6997-454c-a762-470f156addf4}</Project>
      <Name>CoreUtilCS46</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>