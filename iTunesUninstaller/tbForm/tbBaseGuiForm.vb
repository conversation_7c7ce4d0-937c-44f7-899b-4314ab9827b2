﻿Imports System.Drawing
Imports System.Windows.Forms
Imports System.Runtime.InteropServices

'用于进程间传值
Public Structure COPYDATASTRUCT
    Public dwData As IntPtr
    Public cbData As Integer
    <Runtime.InteropServices.MarshalAs(Runtime.InteropServices.UnmanagedType.LPStr)> _
    Public lpData As String
End Structure

Public Class tbBaseGuiForm
    Inherits tbBaseForm

    Private m_GuiBackground As Image = Nothing             '窗体的背景图片

    Private m_GuiTitlemark As Image = Nothing              'Titlemark 图片
    Private m_GuiWatermark As Image = Nothing              'Watermark 图片
    Private m_GuiResizemark As Image = Nothing             'Resizemark 图片

    Private m_Split As tbSplitStructure = tbSplitStructure.Empty              '背景图片的切割位置（Width1,Height1,Width2,Height2）
    Private m_GuiTitlemarkLocation As Point = Point.Empty           'Titlemark 的位置坐标（离窗体左上角坐标）
    Private m_GuiWatermarkLocation As Point = Point.Empty           'Watermark 的位置坐标（离窗体右下角坐标）
    Private m_GuiWatermarkAlignment As ContentAlignment = ContentAlignment.TopLeft
    Private m_GuiWatermarkLocationDraw As Point = Point.Empty

    Private m_AutoSetFormSize As Boolean = False
    Private m_ShowTitleOnForm As Boolean = False
    Private m_ShowIconOnForm As Boolean = True
    Private m_TitleForeColor As Color = Color.White
    Private m_TitleBackColor As Color = Color.FromArgb(70, 128, 195)
    Private m_TitleFont As Font = New Font("Tahoma", 8, FontStyle.Bold, GraphicsUnit.Point)
    Private m_TitleLocation As Point = New Point(4, 4)
    Private m_ShowTitlemark As Boolean = False
    Private m_ShowWatermark As Boolean = False

    Private m_functionMappingKey As FunctionKey = FunctionKey.None


#Region "--- 显示系统菜单API ---"

    Private Const TPM_LEFTBUTTON As UInteger = 0
    Private Const TPM_RETURNCMD As UInteger = &H100
    Private Const WM_SYSCOMMAND As UInteger = &H112
    Private Const WS_SYSMENU As UInteger = &H80000

    Private Declare Function GetSystemMenu Lib "User32.dll" (ByVal hWnd As IntPtr, ByVal bRevert As Boolean) As IntPtr
    Private Declare Function GetCursorPos Lib "User32.dll" (ByRef lpPoint As Point) As Boolean
    Private Declare Function TrackPopupMenu Lib "User32.dll" (ByVal hMenu As IntPtr, ByVal uFlags As UInteger, ByVal x As Integer, ByVal y As Integer, ByVal nReserved As Integer, ByVal hWnd As IntPtr, ByRef prcRect As Rectangle) As Integer
    Private Declare Function SendMessage Lib "User32.dll" Alias "SendMessageA" (ByVal hwnd As IntPtr, ByVal wMsg As Integer, ByVal wParam As Integer, ByVal lParam As Integer) As Integer
    Private Declare Function GetWindowLong Lib "User32.dll" Alias "GetWindowLongA" (ByVal hWnd As HandleRef, ByVal nIndex As Integer) As Integer
    Private Declare Function SetWindowLong Lib "User32.dll" Alias "SetWindowLongA" (ByVal hWnd As HandleRef, ByVal nIndex As Integer, ByVal dwNewLong As Integer) As IntPtr

#End Region

#Region "--- 实例化 ---"

    Public Sub New()
        Me.SetStyle(ControlStyles.UserPaint Or _
                    ControlStyles.AllPaintingInWmPaint Or _
                    ControlStyles.OptimizedDoubleBuffer Or _
                    ControlStyles.DoubleBuffer Or _
                    ControlStyles.ResizeRedraw, True)

        Me.UpdateStyles()

        Me.m_GuiWatermark = Nothing ' My.Resources.form_water_mark
        'Me.m_GuiBackground = My.Resources.form_bg

        'Me.m_Split = New tbSplitStructure(20, 104, 20, 36)
        Me.StartPosition = FormStartPosition.CenterScreen
    End Sub

#End Region

#Region "--- 对外属性 ---"

    <System.ComponentModel.Description("Title 位置")> _
      Public Property tbTitleLocation() As Point
        Get
            Return Me.m_TitleLocation
        End Get
        Set(ByVal value As Point)
            Me.m_TitleLocation = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("是否绘制Title Icon")> _
    Public Property tbShowTitlemark() As Boolean
        Get
            Return Me.m_ShowTitlemark
        End Get
        Set(ByVal value As Boolean)
            Me.m_ShowTitlemark = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("是否绘制水印")> _
    Public Property tbShowWatermark() As Boolean
        Get
            Return Me.m_ShowWatermark
        End Get
        Set(ByVal value As Boolean)
            Me.m_ShowWatermark = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("窗体图形背景，支持九宫切割，切割参数使用 tbSplit 指定")> _
    Public Property tbGuiBackground() As Image
        Get
            Return Me.m_GuiBackground
        End Get
        Set(ByVal value As Image)
            Me.m_GuiBackground = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("在窗体中绘制的标题水印图")> _
    Public Property tbGuiTitlemark() As Image
        Get
            Return Me.m_GuiTitlemark
        End Get
        Set(ByVal value As Image)
            Me.m_GuiTitlemark = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("在窗体中绘制的水印图")> _
    Public Property tbGuiWatermark() As Image
        Get
            Return Me.m_GuiWatermark
        End Get
        Set(ByVal value As Image)
            Me.m_GuiWatermark = value
            Me.SetDrawWatermarkLoaction()
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("在窗体中绘制的水印图的方向")> _
    Public Property tbGuiWatermarkAlignment() As ContentAlignment
        Get
            Return Me.m_GuiWatermarkAlignment
        End Get
        Set(ByVal value As ContentAlignment)
            Me.m_GuiWatermarkAlignment = value
            Me.SetDrawWatermarkLoaction()
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("在窗体中绘制Resize Icon")> _
    Public Property tbGuiResizemark() As Image
        Get
            Return Me.m_GuiResizemark
        End Get
        Set(ByVal value As Image)
            Me.m_GuiResizemark = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("九宫切割参数，用于指定 tbGuiBackground 的绘图方式。WidthLeft，HeightTop，WidthRight，HeightBotttom")> _
    Public Property tbSplit() As String
        Get
            Return Me.m_Split.GetString
        End Get
        Set(ByVal value As String)
            Me.m_Split = New tbSplitStructure(value)
            Me.MinimumSize = New Size(Me.m_Split.WidthLeft + Me.m_Split.WidthRight, Me.m_Split.HeightTop + Me.m_Split.HeightBottom)
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("Titlemark 的位置坐标（离窗体左上角坐标）")> _
    Public Property tbGuiTitlemarkLocation() As Point
        Get
            Return Me.m_GuiTitlemarkLocation
        End Get
        Set(ByVal value As Point)
            Me.m_GuiTitlemarkLocation = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("Watermark 的位置坐标")> _
    Public Property tbGuiWatermarkLocation() As Point
        Get
            Return Me.m_GuiWatermarkLocation
        End Get
        Set(ByVal value As Point)
            Me.m_GuiWatermarkLocation = value
            Me.SetDrawWatermarkLoaction()
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("是否启用自动设置窗体大小")> _
    Public Property tbAutoSetFormSize() As Boolean
        Get
            Return Me.m_AutoSetFormSize
        End Get
        Set(ByVal value As Boolean)
            Me.m_AutoSetFormSize = value
        End Set
    End Property

    <System.ComponentModel.Description("是否在图形窗体中绘制自己的标题，用于无边框窗体")> _
    Public Property tbShowTitleOnForm() As Boolean
        Get
            Return Me.m_ShowTitleOnForm
        End Get
        Set(ByVal value As Boolean)
            Me.m_ShowTitleOnForm = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("是否在图形窗体中绘制自己的icon，用于无边框窗体")> _
    Public Property tbShowIconOnForm() As Boolean
        Get
            Return Me.m_ShowIconOnForm
        End Get
        Set(ByVal value As Boolean)
            Me.m_ShowIconOnForm = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("自绘窗体标题的字体颜色")> _
    Public Property tbTitleForeColor() As Color
        Get
            Return Me.m_TitleForeColor
        End Get
        Set(ByVal value As Color)
            Me.m_TitleForeColor = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("自绘窗体标题的阴影颜色")> _
    Public Property tbTitleBackColor() As Color
        Get
            Return Me.m_TitleBackColor
        End Get
        Set(ByVal value As Color)
            Me.m_TitleBackColor = value
            Me.Invalidate()
        End Set
    End Property

    <System.ComponentModel.Description("自绘窗体标题的字体")> _
    Public Property tbTitleFont() As Font
        Get
            Return Me.m_TitleFont
        End Get
        Set(ByVal value As Font)
            Me.m_TitleFont = value
            Me.Invalidate()
        End Set
    End Property

    '功能窗体的key
    Public Property FunctionMappingKey() As FunctionKey
        Get
            Return Me.m_functionMappingKey
        End Get
        Set(ByVal value As FunctionKey)
            Me.m_functionMappingKey = value
        End Set
    End Property

#End Region

#Region "--- Overrides ---"

    Private Sub SetDrawWatermarkLoaction()
        If Me.m_GuiWatermark IsNot Nothing Then
            Me.m_GuiWatermarkLocationDraw = GuiHelper.CalculateImageLoaction(Me.ClientRectangle, Me.m_GuiWatermarkLocation, Me.m_GuiWatermark.Size, Me.m_GuiWatermarkAlignment)
        Else
            Me.m_GuiWatermarkLocationDraw = Point.Empty
        End If
    End Sub

    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        MyBase.OnResize(e)

        Me.SetDrawWatermarkLoaction()
    End Sub

    Protected Overrides Sub OnLoad(ByVal e As System.EventArgs)
        MyBase.OnLoad(e)

        If Me.AutoSetFont AndAlso Not Me.DesignMode Then
            Me.SetControlFont(Me)
            Me.SetFormTitle()
        End If

        '如果窗体是无边框的话，再判断是否要在窗体上自绘标题栏，如果要自绘标题栏的话，需要提供点击自绘图标后弹出系统菜单的功能

        '但无边框窗体不提供系统菜单，因此，在这种情况下，需要调用 API 牵制窗体提供系统菜单的功能。

        If Me.FormBorderStyle = Windows.Forms.FormBorderStyle.None AndAlso _
           Me.tbShowTitleOnForm AndAlso Not Me.IsDisposed Then

            SetWindowLong(New HandleRef(Me, Me.Handle), _
                          -16, _
                          (GetWindowLong(New HandleRef(Me, Me.Handle), -16)) Or WS_SYSMENU)

        End If
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

        '功能窗体统计
        'FunctionMapping.GetInstanse().AddFunctionMapping(Me.m_functionMappingKey)

    End Sub

    Protected Overrides Sub OnMouseClick(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseClick(e)

        '如果要自绘标题栏的话，判断用户是否有去手动点击自绘出来的系统图标，有的话要弹出系统菜单

        If Me.tbShowTitleOnForm = True AndAlso _
           (e.X > Me.m_TitleLocation.X AndAlso _
            e.X < Me.m_TitleLocation.X + 16 AndAlso _
            e.Y > Me.m_TitleLocation.Y AndAlso _
            e.Y < Me.m_TitleLocation.Y + 16) Then

            Dim vPoint As Point, vRect As Rectangle

            GetCursorPos(vPoint)
            SendMessage(Handle, _
                        WM_SYSCOMMAND, _
                        TrackPopupMenu(GetSystemMenu(Handle, False), TPM_RETURNCMD Or TPM_LEFTBUTTON, vPoint.X, vPoint.Y, 0, Handle, vRect), _
                        0)

        End If
    End Sub

    Protected Overrides Sub OnTextChanged(ByVal e As System.EventArgs)
        MyBase.OnTextChanged(e)

        If Me.tbShowTitleOnForm = True Then
            Me.Invalidate(New Rectangle(Me.m_TitleLocation.X + 16, Me.m_TitleLocation.Y, Me.m_MostLeftButtonPosX, Me.m_TitleLocation.Y + 25))
        End If
    End Sub

    'Protected Overrides Sub DrawToBuffer(ByVal g As Graphics)
    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        Dim g As Graphics = e.Graphics
        g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        g.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias

        'Debug.Print("Date: " & Now.ToString())

        '====== 绘制背景 ========================================================================================
        Me.OnPaintBackground(New PaintEventArgs(g, Me.ClientRectangle))
        If Me.m_GuiBackground IsNot Nothing Then
            GuiHelper.DrawImage(g, Me.ClientRectangle, Me.m_Split, Me.m_GuiBackground)
        End If

        '====== 绘制标题 Icon ===================================================================================
        If Me.m_ShowTitlemark AndAlso Me.m_GuiTitlemark IsNot Nothing Then
            g.DrawImage(Me.m_GuiTitlemark, Me.m_GuiTitlemarkLocation.X, Me.m_GuiTitlemarkLocation.Y, Me.m_GuiTitlemark.Width, Me.m_GuiTitlemark.Height)
        End If

        '====== 绘制水印 ========================================================================================
        If Me.m_ShowWatermark AndAlso Me.m_GuiWatermark IsNot Nothing Then
            g.DrawImage(Me.m_GuiWatermark, New Rectangle(Me.m_GuiWatermarkLocationDraw, Me.m_GuiWatermark.Size), New Rectangle(New Point(0, 0), Me.m_GuiWatermark.Size), GraphicsUnit.Pixel)
        End If

        '====== Resize Icon ========================================================================================
        If Me.CanResize AndAlso Me.m_GuiResizemark IsNot Nothing Then
            Dim rectDes As New Rectangle(Me.Width - Me.m_GuiResizemark.Width - 1, Me.Height - Me.m_GuiResizemark.Height - 1, m_GuiResizemark.Width, m_GuiResizemark.Height)
            Dim rectSrc As New Rectangle(0, 0, m_GuiResizemark.Width, m_GuiResizemark.Height)
            g.DrawImage(Me.m_GuiResizemark, rectDes, rectSrc, GraphicsUnit.Pixel)
        End If

        '====== 绘制标题 ========================================================================================
        If Me.tbShowTitleOnForm Then
            Dim iTitleLeft As Integer = Me.m_TitleLocation.X

            If Me.m_ShowIconOnForm AndAlso Me.Icon IsNot Nothing Then
                g.DrawImage(Me.Icon.ToBitmap, New Rectangle(iTitleLeft, Me.m_TitleLocation.Y, 16, 16))
                iTitleLeft += 16
            End If

            Dim textArea As Size = g.MeasureString(Me.Text, Me.tbTitleFont).ToSize

            g.DrawString(Me.Text, Me.tbTitleFont, New SolidBrush(Me.tbTitleBackColor), iTitleLeft + 2, Me.m_TitleLocation.Y + (16 - textArea.Height) / 2 + 1)
            g.DrawString(Me.Text, Me.tbTitleFont, New SolidBrush(Me.tbTitleForeColor), iTitleLeft + 1, Me.m_TitleLocation.Y + (16 - textArea.Height) / 2 + 0)
        End If

    End Sub

    Private Sub SetControlFont(ByVal ctl As Control)
        For Each subCtl As Control In ctl.Controls
            If TypeOf subCtl Is RichTextBox OrElse TypeOf subCtl Is ComboBox Then '
                Continue For
            End If
            Me.SetControlFont(subCtl)
        Next

        'If Not (TypeOf ctl Is ScrollableControl) AndAlso Not (TypeOf ctl Is ScrollBar) Then
        Dim sngSize As Single = ctl.Font.Size
        If sngSize < 9 Then
            sngSize = 9
        End If

        'If TypeOf ctl Is Form Then
        '    CType(ctl, tbBaseGuiForm).tbTitleFont = Common.CreateFont("Arial", ctl.Font.Size, ctl.Font.Style)

        'ElseIf Not (TypeOf ctl Is ScrollableControl) AndAlso Not (TypeOf ctl Is ScrollBar) AndAlso Not (TypeOf ctl Is tbDataGridView) Then
        '    ctl.Font = Common.CreateFont("Arial", sngSize, ctl.Font.Style)

        'ElseIf TypeOf ctl Is tbDataGridView Then
        '    '如果是gridview就设置列的字体
        '    '因为设置gridview的时候会卡死。
        '    Try
        '        Dim dgv As tbDataGridView = ctl
        '        dgv.ColumnHeadersDefaultCellStyle.Font = Common.CreateFont("Arial", sngSize, ctl.Font.Style)
        '        For Each Item As tbDataGridViewColumn In dgv.Columns
        '            Item.DefaultCellStyle.Font = Common.CreateFont("Arial", sngSize, ctl.Font.Style)
        '        Next
        '    Catch ex As Exception
        '        Common.LogException(ex.ToString, "dgv_SetControlFont")
        '    End Try

        'End If
        'End If
    End Sub

    Private Sub SetFormTitle()
        'If Folder.AppType <> RunType.ZJHelper Then
        '    Return
        'End If

        'Me.tbTitleBackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(225, Byte), Integer), CType(CType(242, Byte), Integer))
        'Me.tbTitleForeColor = System.Drawing.Color.FromArgb(CType(CType(101, Byte), Integer), CType(CType(122, Byte), Integer), CType(CType(149, Byte), Integer))
        Me.tbTitleForeColor = Color.Transparent
        Me.tbTitleBackColor = Color.Transparent
    End Sub

#End Region

    Private Sub InitializeComponent()
        Me.SuspendLayout()
        '
        'tbBaseGuiForm
        '
        Me.ClientSize = New System.Drawing.Size(300, 300)
        Me.Name = "tbBaseGuiForm"
        Me.ResumeLayout(False)

    End Sub

End Class

Public Enum FunctionKey
    none
End Enum
