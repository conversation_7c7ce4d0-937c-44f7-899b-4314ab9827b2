﻿Imports System.Drawing
Imports System.Windows.Forms
Imports System.Runtime.InteropServices

Public Class tbBase
    Inherits System.Windows.Forms.Form

    Protected m_CanBeMove As Boolean = True
    Protected m_CanResize As Boolean = False
    Protected mDiffW As Integer = 6
    Protected mDiffH As Integer = 5

    Private m_Active As Boolean = False

    Private m_MousePoint As Point = Point.Empty
    Private m_MouseDown As Boolean = False

    Private m_MouseAction As MouseAction = MouseAction.None
    Private m_MouseLocation As MouseLoaction = MouseLoaction.None

    Private m_FormRect As Rectangle = Rectangle.Empty       ' 窗体大小
    Private mScreenRect As Rectangle = Rectangle.Empty

    Public Event UserResizeFormEvent As EventHandler

#Region "--- 对外属性 ---"

    <System.ComponentModel.Description("窗体在无边框的样式时，是否可以通过鼠标移动")> _
    Public Overridable Property CanbeMove() As Boolean
        Get
            Return Me.m_CanBeMove
        End Get
        Set(ByVal value As Boolean)
            Me.m_CanBeMove = value
        End Set
    End Property

    Public Overridable Property CanResize() As Boolean
        Get
            Return Me.m_CanResize
        End Get
        Set(ByVal value As Boolean)
            Me.m_CanResize = value
        End Set
    End Property

#End Region

#Region "--- Overrides ---"

    Protected Overridable Sub OnActiveApp()

    End Sub

    Protected Overrides Sub OnMouseDown(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseDown(e)

        If e.Button <> Windows.Forms.MouseButtons.Left OrElse Me.m_CanBeMove = False Then
            Return
        End If

        Me.m_MouseDown = True
        Me.m_MousePoint = e.Location

    End Sub

    Protected Overrides Sub OnMouseMove(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseMove(e)

        If Me.m_MouseDown Then
            If m_FormRect = Rectangle.Empty Then
                m_FormRect = New Rectangle(Me.Location.X, Me.Location.Y, Me.Width, Me.Height)
            Else
                ControlPaint.DrawReversibleFrame(m_FormRect, ControlPaint.ContrastControlDark, FrameStyle.Dashed)
            End If

            If Me.m_MouseAction = MouseAction.FormResize AndAlso Me.m_CanResize Then
                Me.ResizeForm(e.Location)

            ElseIf Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None AndAlso Me.m_CanBeMove AndAlso Me.Parent Is Nothing Then
                Me.m_FormRect = New Rectangle(Me.Location + e.Location - Me.m_MousePoint, Me.Size)

            End If
            ControlPaint.DrawReversibleFrame(m_FormRect, ControlPaint.ContrastControlDark, FrameStyle.Dashed)

        ElseIf Me.Parent Is Nothing Then
            Me.SetMouseState(e.Location)

        End If
    End Sub

    Protected Overrides Sub OnMouseUp(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseUp(e)

        m_MouseDown = False

        If m_FormRect.IsEmpty = False Then
            ControlPaint.DrawReversibleFrame(m_FormRect, ControlPaint.ContrastControlDark, FrameStyle.Dashed)

            If Me.m_MouseAction = MouseAction.FormMove Then
                Me.Location = m_FormRect.Location

            ElseIf Me.m_MouseAction = MouseAction.FormResize Then
                Me.Location = m_FormRect.Location
                Me.Size = m_FormRect.Size

                RaiseEvent UserResizeFormEvent(Me, New EventArgs())
            End If

            m_FormRect = Rectangle.Empty
        End If

        Me.m_MouseAction = MouseAction.None
        Me.m_MouseLocation = MouseLoaction.None
        Me.Cursor = Cursors.Default
    End Sub

    Protected Overrides Sub OnMouseLeave(ByVal e As System.EventArgs)
        MyBase.OnMouseLeave(e)

        If Me.m_MouseAction <> MouseAction.None Then
            Me.m_MouseAction = MouseAction.None

            Me.m_MouseLocation = MouseLoaction.None
            Me.Cursor = Cursors.Default
        End If
    End Sub

    Private Function CheckScreenPosition(ByVal oldPos As Point) As Point
        Dim minSize As Size = New Size(800, 600)
        Dim minPos As Point = Point.Empty
        Dim newPos As Point = New Point(oldPos.X, oldPos.Y)

        If My.Computer.Screen.WorkingArea.Size.Width < minSize.Width Then
            If My.Computer.Screen.WorkingArea.Height < minSize.Height Then
                minSize = My.Computer.Screen.WorkingArea.Size
            Else
                minSize = New Size(My.Computer.Screen.WorkingArea.Size.Width, minSize.Height)
            End If

        ElseIf My.Computer.Screen.WorkingArea.Height < minSize.Height Then
            minSize = New Size(minSize.Width, My.Computer.Screen.WorkingArea.Height)

        End If

        Select Case Me.m_MouseLocation
            Case MouseLoaction.TopLeft
                minPos.X = Me.Width - minSize.Width
                minPos.Y = Me.Height - minSize.Height

                If newPos.X > minPos.X Then
                    newPos.X = minPos.X
                End If
                If newPos.Y > minPos.Y Then
                    newPos.Y = minPos.Y
                End If

            Case MouseLoaction.Top
                minPos.Y = Me.Height - minSize.Height

                If newPos.Y > minPos.Y Then
                    newPos.Y = minPos.Y
                End If

            Case MouseLoaction.TopRight
                minPos.X = minSize.Width
                minPos.Y = Me.Height - minSize.Height

                If newPos.X < minPos.X Then
                    newPos.X = minPos.X
                End If
                If newPos.Y > minPos.Y Then
                    newPos.Y = minPos.Y
                End If

            Case MouseLoaction.Left
                minPos.X = Me.Width - minSize.Width

                If newPos.X > minPos.X Then
                    newPos.X = minPos.X
                End If

            Case MouseLoaction.Right
                minPos.X = minSize.Width

                If newPos.X < minPos.X Then
                    newPos.X = minPos.X
                End If

            Case MouseLoaction.BottomLeft
                minPos.X = Me.Width - minSize.Width
                minPos.Y = minSize.Height

                If newPos.X > minPos.X Then
                    newPos.X = minPos.X
                End If
                If newPos.Y < minPos.Y Then
                    newPos.Y = minPos.Y
                End If

            Case MouseLoaction.Bottom
                minPos.Y = minSize.Height

                If newPos.Y < minPos.Y Then
                    newPos.Y = minPos.Y
                End If

            Case MouseLoaction.BottomRight
                minPos.X = minSize.Width
                minPos.Y = minSize.Height

                If newPos.X < minPos.X Then
                    newPos.X = minPos.X
                End If
                If newPos.Y < minPos.Y Then
                    newPos.Y = minPos.Y
                End If

        End Select

        Return newPos
    End Function

    Private Function CheckScreenRectOnResize(ByVal rectClient As Rectangle) As Rectangle
        If mScreenRect.IsEmpty Then
            Dim intRight As Integer = 0
            Dim intBottom As Integer = 0
            Dim intLeft As Integer = 0
            Dim intTop As Integer = 0

            For Each src As Screen In Windows.Forms.Screen.AllScreens
                If src.WorkingArea.Right > intRight Then
                    intRight = src.WorkingArea.Right
                End If
                If src.WorkingArea.Bottom > intBottom Then
                    intBottom = src.WorkingArea.Bottom
                End If
                If src.WorkingArea.Left < intLeft Then
                    intLeft = src.WorkingArea.Left
                End If
                If src.WorkingArea.Top < intTop Then
                    intTop = src.WorkingArea.Top
                End If
            Next

            mScreenRect = New Rectangle(intLeft, intTop, intRight - intLeft, intBottom - intTop)
        End If

        If rectClient.X < mScreenRect.Left Then
            rectClient.Width -= Math.Abs(rectClient.X)
            rectClient.X = 0

        ElseIf rectClient.Right > mScreenRect.Right Then
            rectClient.Width -= Math.Abs(rectClient.Right - mScreenRect.Right)

        End If

        If rectClient.Y < mScreenRect.Top Then
            rectClient.Height -= Math.Abs(rectClient.Y)
            rectClient.Y = 0

        ElseIf rectClient.Bottom > mScreenRect.Bottom Then
            rectClient.Height -= Math.Abs(rectClient.Bottom - mScreenRect.Bottom)

        End If

        Return rectClient
    End Function

    Private Sub ResizeForm(ByVal pos As Point)
        pos = CheckScreenPosition(pos)

        Dim dicX As Integer = (pos.X - Me.m_MousePoint.X)
        Dim dicY As Integer = (pos.Y - Me.m_MousePoint.Y)

        With m_FormRect
            Select Case Me.m_MouseLocation
                Case MouseLoaction.TopLeft
                    .X += dicX
                    .Y += dicY
                    .Width -= dicX
                    .Height -= dicY

                Case MouseLoaction.Top
                    .Y += dicY
                    .Height -= dicY

                Case MouseLoaction.TopRight
                    .Y += dicY
                    .Width += dicX
                    .Height -= dicY

                Case MouseLoaction.Left
                    .X += dicX
                    .Width -= dicX

                Case MouseLoaction.Right
                    .Width += dicX

                Case MouseLoaction.BottomLeft
                    .X += dicX
                    .Width -= dicX
                    .Height += dicY

                Case MouseLoaction.Bottom
                    .Height += dicY

                Case MouseLoaction.BottomRight
                    .Width += dicX
                    .Height += dicY

            End Select
        End With

        Me.m_MousePoint = pos
        Me.m_FormRect = CheckScreenRectOnResize(m_FormRect)
    End Sub

    Private Sub SetMouseState(ByVal pos As Point)
        If Not Me.m_CanResize Then
            If Me.m_CanBeMove Then
                Me.Cursor = Cursors.Hand
                m_MouseLocation = MouseLoaction.Center
                m_MouseAction = MouseAction.FormMove
            End If

            Return
        End If

        If pos.X < mDiffW Then
            If pos.Y < mDiffH Then
                Me.Cursor = Cursors.SizeNWSE
                m_MouseLocation = MouseLoaction.TopLeft
                m_MouseAction = MouseAction.FormResize

            ElseIf pos.Y > Me.Height - mDiffH Then
                Me.Cursor = Cursors.SizeNESW
                m_MouseLocation = MouseLoaction.BottomLeft
                m_MouseAction = MouseAction.FormResize

            Else
                Me.Cursor = Cursors.SizeWE
                m_MouseLocation = MouseLoaction.Left
                m_MouseAction = MouseAction.FormResize

            End If
        ElseIf pos.X > Me.Width - mDiffW Then
            If pos.Y < mDiffH Then
                Me.Cursor = Cursors.SizeNESW
                m_MouseLocation = MouseLoaction.TopRight
                m_MouseAction = MouseAction.FormResize

            ElseIf pos.Y > Me.Height - mDiffH Then
                Me.Cursor = Cursors.SizeNWSE
                m_MouseLocation = MouseLoaction.BottomRight
                m_MouseAction = MouseAction.FormResize

            Else
                Me.Cursor = Cursors.SizeWE
                m_MouseLocation = MouseLoaction.Right
                m_MouseAction = MouseAction.FormResize

            End If
        Else
            If pos.Y < mDiffH Then
                Me.Cursor = Cursors.SizeNS
                m_MouseLocation = MouseLoaction.Top
                m_MouseAction = MouseAction.FormResize

            ElseIf pos.Y > Me.Height - mDiffH Then
                Me.Cursor = Cursors.SizeNS
                m_MouseLocation = MouseLoaction.Bottom
                m_MouseAction = MouseAction.FormResize

            Else
                If Me.m_CanBeMove Then
                    Me.Cursor = Cursors.Default
                    m_MouseLocation = MouseLoaction.Center
                    m_MouseAction = MouseAction.FormMove
                End If
            End If
        End If

    End Sub

    Protected Overrides Sub WndProc(ByRef m As System.Windows.Forms.Message)
        Select Case m.Msg
            Case WindowMessage.WM_NCHITTEST
                If Me.FormBorderStyle = Windows.Forms.FormBorderStyle.None Then
                    Me.WmNcHitTest(m)
                Else
                    MyBase.WndProc(m)
                End If

                'Case WindowMessage.WM_WINDOWPOSCHANGED
                '    Me.m_PosChanged = True
                '    MyBase.WndProc(m)
                '    Me.m_PosChanged = False

                'Case WindowMessage.WM_NCPAINT, WindowMessage.WM_NCCALCSIZE
                '    Exit Select

                'Case WindowMessage.WM_NCACTIVATE
                '    Me.WmNcActive(m)

                'Case WindowMessage.WM_GETMINMAXINFO
                '    Me.WmGetMinMaxInfo(m)

            Case WindowMessage.WM_NCLBUTTONDBLCLK
                If Me.CanResize Then
                    MyBase.WndProc(m)
                Else
                    Return
                End If

            Case WindowMessage.WM_ACTIVATEAPP
                MyBase.WndProc(m)

                Try
                    '向内窗体发送其他应用程序被激活消息
                    For Each frm As Form In Application.OpenForms
                        If frm.Parent Is Nothing OrElse Not (TypeOf frm Is tbBase) Then
                            Continue For
                        End If

                        CType(frm, tbBase).OnActiveApp()
                    Next
                    Me.OnActiveApp()
                Catch ex As Exception
                End Try

            Case Else
                'Debug.Print(m.Msg)
                MyBase.WndProc(m)

        End Select
    End Sub

    Private Sub WmNcHitTest(ByRef m As Message)
        Dim intParam As Integer = CInt(GetIntPtrValue(m.LParam))
        Dim pos As New Point(NativeMethods.GetLOWORD(intParam), NativeMethods.GetHIWORD(intParam))
        Dim screentRect As Rectangle = Screen.FromControl(Me).Bounds

        If screentRect.X < 0 OrElse screentRect.Y < 0 Then
            pos = System.Windows.Forms.Cursor.Position
        End If
        pos = Me.PointToClient(pos)


        If Me.m_CanBeMove Then
            If Me.m_CanResize AndAlso (pos.X < mDiffW OrElse pos.X > Me.Width - mDiffW OrElse pos.Y < mDiffH OrElse pos.Y > Me.Height - mDiffH) Then
                m.Result = New IntPtr(CInt(NCHITTEST.HTCLIENT))
            Else
                m.Result = New IntPtr(CInt(NCHITTEST.HTCAPTION))
            End If
        Else
            m.Result = New IntPtr(CInt(NCHITTEST.HTCLIENT))
        End If


        'If Me.m_CanResize Then
        '    If pos.X < 5 AndAlso pos.Y < 5 Then
        '        m.Result = New IntPtr(CInt(NCHITTEST.HTTOPLEFT))
        '        Return
        '    End If
        '    If pos.X > Width - 5 AndAlso pos.Y < 5 Then
        '        m.Result = New IntPtr(CInt(NCHITTEST.HTTOPRIGHT))
        '        Return
        '    End If
        '    If pos.X < 5 AndAlso pos.Y > Height - 5 Then
        '        m.Result = New IntPtr(CInt(NCHITTEST.HTBOTTOMLEFT))
        '        Return
        '    End If
        '    If pos.X > Width - 5 AndAlso pos.Y > Height - 5 Then
        '        m.Result = New IntPtr(CInt(NCHITTEST.HTBOTTOMRIGHT))
        '        Return
        '    End If
        '    If pos.Y < 3 Then
        '        m.Result = New IntPtr(CInt(NCHITTEST.HTTOP))
        '        Return
        '    End If
        '    If pos.Y > Height - 3 Then
        '        m.Result = New IntPtr(CInt(NCHITTEST.HTBOTTOM))
        '        Return
        '    End If
        '    If pos.X < 3 Then
        '        m.Result = New IntPtr(CInt(NCHITTEST.HTLEFT))
        '        Return
        '    End If
        '    If pos.X > Width - 3 Then
        '        m.Result = New IntPtr(CInt(NCHITTEST.HTRIGHT))
        '        Return
        '    End If
        'End If

        'If Not FormControlBox.CloseBoxRect.Contains(point) AndAlso Not FormControlBox.MaximizeBoxRect.Contains(point) AndAlso Not FormControlBox.MinimizeBoxRect.Contains(point) Then
        '    m.Result = New IntPtr(CInt(NCHITTEST.HTCAPTION))
        '    Return
        'End If
        'm.Result = New IntPtr(CInt(NCHITTEST.HTCLIENT))
    End Sub

    Private Sub WmNcActive(ByRef m As Message)
        If GetIntPtrValue(m.WParam) = 1 Then
            Me.m_Active = True
        Else
            Me.m_Active = False
        End If
        m.Result = NativeMethods.[True]
    End Sub

    Private Sub WmGetMinMaxInfo(ByRef m As Message)
        Dim minmax As MINMAXINFO = DirectCast(Marshal.PtrToStructure(m.LParam, GetType(MINMAXINFO)), MINMAXINFO)

        If Not Me.MaximumSize.IsEmpty Then
            minmax.maxTrackSize = Me.MaximumSize
        Else
            Dim rect As Rectangle = Screen.FromControl(Me).WorkingArea
            rect.X = rect.X Mod (Screen.GetBounds(New Point(0, 0)).Width)

            minmax.maxPosition = New Point(rect.Location.X + 1, rect.Location.Y + 1)
            minmax.maxTrackSize = New Size(rect.Size.Width - 2, rect.Size.Height - 2)
        End If

        If Not Me.MinimumSize.IsEmpty Then
            minmax.minTrackSize = Me.MinimumSize
        Else
            minmax.minTrackSize = New Size(100, 30)
        End If

        Marshal.StructureToPtr(minmax, m.LParam, False)
    End Sub

#End Region


    Public Function GetIntPtrValue(ByVal hSrc As IntPtr) As Long
        If IsX64 Then
            Return hSrc.ToInt64()
        Else
            Return hSrc.ToInt32()
        End If
    End Function

    Public Function IsX64() As Boolean
        Dim bln As Boolean = False
        If Marshal.SizeOf(GetType(IntPtr)) = 8 Then
            bln = True
        End If
        Return bln
    End Function

End Class
