﻿Imports System.Drawing
Imports System.Windows.Forms
Imports System.Runtime.InteropServices

Public Class tbBaseForm
    Inherits tbBase


    Private m_button_close As Control = Nothing             '关闭
    Private m_button_normal As Control = Nothing            '最大化、还原

    Private m_button_minimize As Control = Nothing          '最小化
    Private m_button_exit As Control = Nothing              '退出

    Private m_PressEscCanClose As Boolean = False           '是否可以通过按 ESC 键退出窗体
    Private m_AllowFormClose As Boolean = True              '是否可以退出窗体
    Private m_OnControlsEnabled As Boolean = False          '当前窗体是否在 ControlsEnabled = False 状态下


    Private m_InitCompleted As Boolean = False
    Private m_FilletRadius As Integer = 0

    Private m_PosChanged As Boolean = False
    Private m_AutoSetFont As Boolean = True

    Private m_ControlButtonPadding As Size = Drawing.Size.Empty
    Private m_ControlButtonSpace As Integer = 0
    Protected m_MostLeftButtonPosX As Integer = 0

    Protected Language As LanguageInterface = Nothing

#Region "--- 初始化、关闭 ---"

    Public Sub New()
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.KeyPreview = True
        Me.FormBorderStyle = Windows.Forms.FormBorderStyle.None

        Me.SetStyle(ControlStyles.UserPaint Or ControlStyles.AllPaintingInWmPaint, True)
        Me.UpdateStyles()

        Me.StartPosition = FormStartPosition.CenterScreen
    End Sub

    Private Sub tbBaseForm_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Try
            Me.m_InitCompleted = False

            Me.AddButtonsAction()               ''添加最大化、还原、最小化、关闭等按钮的动作

            Me.InitControls()                   '初始化控件状态和位置
            Me.SetInterface()                   '界面语言设置
            Me.BeforeFormShow()                 '窗体显示前触发

            If Not Me.IsDisposed AndAlso Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None Then
                Dim rect As Rectangle = Screen.FromControl(Me).WorkingArea
                rect.X = rect.X Mod (Screen.GetBounds(New Point(0, 0)).Width)
                Me.MaximizedBounds = rect
            End If

            Me.ResizeControlButton()

            If Not Me.Disposing Then
                If Not Me.DesignMode AndAlso Me.Parent Is Nothing Then
                    Select Case Me.StartPosition
                        Case FormStartPosition.CenterParent
                            MyBase.CenterToParent()

                        Case FormStartPosition.CenterScreen
                            MyBase.CenterToScreen()

                    End Select
                End If

                'System.Windows.Forms.Application.DoEvents()
                Me.Activate()
            End If

        Catch ex As Exception
            'Common.LogException(ex.ToString(), Me.GetType().FullName)
            'MessageBox.Show(ex.Message, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
            Debug.Print(ex.ToString)
        End Try

        Me.m_InitCompleted = True
    End Sub

    Protected Overrides Sub OnFormClosed(ByVal e As System.Windows.Forms.FormClosedEventArgs)
        MyBase.OnFormClosed(e)

        Try
            If Me.m_button_close IsNot Nothing Then
                RemoveHandler Me.m_button_close.Click, AddressOf Me.btn_close_Click
            End If

            If Me.m_button_normal IsNot Nothing Then
                RemoveHandler Me.m_button_normal.Click, AddressOf Me.btn_normal_Click
            End If

            If Me.m_button_minimize IsNot Nothing Then
                RemoveHandler Me.m_button_minimize.Click, AddressOf Me.btn_minimize_Click
            End If

            If Me.m_button_exit IsNot Nothing Then
                RemoveHandler Me.m_button_exit.Click, AddressOf Me.btn_close_Click
            End If

            If Me.Language IsNot Nothing Then
                RemoveHandler Language.DisplayLanguageChanging, AddressOf Me.DisplayLanguageChanging
                RemoveHandler Language.DisplayLanguageChanged, AddressOf Me.DisplayLanguageChanged
            End If

        Catch
        End Try
    End Sub

    Protected Overrides Sub OnFormClosing(ByVal e As System.Windows.Forms.FormClosingEventArgs)
        MyBase.OnFormClosing(e)

        '窗体关闭前


        '-------------------------------------------------------
        Dim blnCancelClose As Boolean = e.Cancel
        Me.BeforeFormClose(blnCancelClose)

        If Not e.CloseReason = CloseReason.WindowsShutDown Then
            e.Cancel = (Me.m_OnControlsEnabled OrElse _
                       ((Not Me.AllowFormClose) Or blnCancelClose))
        End If
    End Sub

    Protected Overrides Sub OnKeyDown(ByVal e As System.Windows.Forms.KeyEventArgs)
        MyBase.OnKeyDown(e)

        If m_PressEscCanClose AndAlso e.KeyCode = Keys.Escape Then
            Me.Close()
        End If
    End Sub

#End Region

#Region "--- 最大化、最小化、关闭按钮 ---"

    Private Sub AddButtonsAction()
        Me.m_button_close = Me.Controls("btn_close")
        Me.m_button_normal = Me.Controls("btn_normal")
        Me.m_button_minimize = Me.Controls("btn_minimize")
        Me.m_button_exit = Me.Controls("btn_exit")

        If Me.m_button_close IsNot Nothing Then
            AddHandler Me.m_button_close.Click, AddressOf Me.btn_close_Click
        End If

        If Me.m_button_normal IsNot Nothing Then
            AddHandler Me.m_button_normal.Click, AddressOf Me.btn_normal_Click
        End If

        If Me.m_button_minimize IsNot Nothing Then
            AddHandler Me.m_button_minimize.Click, AddressOf Me.btn_minimize_Click
        End If

        If Me.m_button_exit IsNot Nothing Then
            AddHandler Me.m_button_exit.Click, AddressOf Me.btn_close_Click
        End If
    End Sub

    Private Sub btn_close_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.Close()
    End Sub

    Private Sub btn_normal_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If Me.WindowState = FormWindowState.Normal Then
            Me.WindowState = FormWindowState.Maximized
        Else
            Me.WindowState = FormWindowState.Normal
        End If
    End Sub

    Private Sub btn_minimize_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        'Me.WindowState = System.Windows.Forms.FormWindowState.Minimized
        If Me.Owner IsNot Nothing AndAlso Me.Modal Then
            Me.Owner.WindowState = FormWindowState.Minimized
        Else
            Me.WindowState = FormWindowState.Minimized
        End If
    End Sub

    Private Sub ResizeControlButton()
        Dim intPos As Integer = Me.Width '- Me.mDiffW

        intPos -= Me.ControlButtonPadding.Width
        If Me.m_button_close IsNot Nothing Then
            intPos -= (Me.m_button_close.Width)
            Me.m_button_close.Location = New Point(intPos, Me.ControlButtonPadding.Height) '+ Me.mDiffH
            intPos -= Me.ControlButtonSpace
        End If

        If Me.m_button_normal IsNot Nothing Then
            intPos -= (Me.m_button_normal.Width)
            Me.m_button_normal.Location = New Point(intPos, Me.ControlButtonPadding.Height)
            intPos -= Me.ControlButtonSpace
        End If

        If Me.m_button_minimize IsNot Nothing Then
            intPos -= (Me.m_button_minimize.Width)
            Me.m_button_minimize.Location = New Point(intPos, Me.ControlButtonPadding.Height)
            intPos -= Me.ControlButtonSpace
        End If

        m_MostLeftButtonPosX = intPos
    End Sub

#End Region

#Region "--- 覆写事件、函数 ---"

    '界面语言设置
    Protected Overridable Sub SetInterface()

        If Me.Language IsNot Nothing Then
            AddHandler Language.DisplayLanguageChanging, AddressOf Me.DisplayLanguageChanging
            AddHandler Language.DisplayLanguageChanged, AddressOf Me.DisplayLanguageChanged
        End If

    End Sub

    '初始化窗体上的控件属性、位置等信息
    Protected Overridable Sub InitControls()

    End Sub

    '窗体显示前

    Protected Overridable Sub BeforeFormShow()

    End Sub

    '窗体关闭前触发

    Protected Overridable Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)

    End Sub

    '此窗体是否可以使用

    Public Overridable Function IsUseable() As Boolean

    End Function

    '界面语言正在切换
    Protected Overridable Sub DisplayLanguageChanging(ByVal e As System.ComponentModel.CancelEventArgs)

    End Sub

    '界面语言切换完毕
    Protected Overridable Sub DisplayLanguageChanged()

    End Sub

    Public Overridable Sub DisposeResources()
        Try
            Me.BeforeFormClose(False)
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 对外属性 ---"

    'Public WriteOnly Property ControlsEnabled() As Boolean
    '    Set(ByVal value As Boolean)

    '        '=== ControlsEnabled = False 的似乎同时禁止用户关闭窗体 ===========
    '        Me.m_OnControlsEnabled = Not value
    '        '==================================================================

    '        Me.FillControlsToCollection()

    '        For Each objControl As Object In Me.FormControls
    '            Try
    '                If objControl.Visible Then

    '                    Select Case TypeName(objControl)
    '                        Case "ndTextc", "ndTextn", "TextBox", "RichTextBox"
    '                            objControl.ReadOnly = Not value
    '                        Case "Label", "ndLabel", "Panel", "ndPanel", "ndLayoutPanel", "ndLayoutContainer", "TableLayoutPanel", "PictureBox", "ndPicture", "ndWaitForm", "WebBrowser"
    '                            '不处理


    '                        Case Else
    '                            objControl.Enabled = value
    '                    End Select

    '                End If

    '            Catch ex As Exception
    '            End Try
    '        Next
    '    End Set
    'End Property

    <System.ComponentModel.Description("窗体是否可以通过按 ESC 退出")> _
    Public Property PressEscCanClose() As Boolean
        Get
            Return Me.m_PressEscCanClose
        End Get
        Set(ByVal value As Boolean)
            Me.m_PressEscCanClose = value
        End Set
    End Property

    Public Property AllowFormClose() As Boolean
        Get
            Return m_AllowFormClose
        End Get
        Set(ByVal value As Boolean)
            Me.m_AllowFormClose = value
        End Set
    End Property

    <System.ComponentModel.Description("最大化、最小化等控制按钮距离右上角的位置")> _
    Public Property ControlButtonPadding() As Size
        Get
            Return Me.m_ControlButtonPadding
        End Get
        Set(ByVal value As Size)
            Me.m_ControlButtonPadding = value
            Me.ResizeControlButton()
        End Set
    End Property

    <System.ComponentModel.Description("最大化、最小化等控制按钮间的间隔大小")> _
    Public Property ControlButtonSpace() As Integer
        Get
            Return Me.m_ControlButtonSpace
        End Get
        Set(ByVal value As Integer)
            Me.m_ControlButtonSpace = value
            Me.ResizeControlButton()
        End Set
    End Property

    <System.ComponentModel.Description("窗体圆角度数，用于绘制圆角窗体")> _
    Public Property FilletRadius() As Integer
        Get
            Return Me.m_FilletRadius
        End Get
        Set(ByVal value As Integer)
            Me.m_FilletRadius = value
            Me.SetWindowRgn()
        End Set
    End Property

    Public Property AutoSetFont() As Boolean
        Get
            Return Me.m_AutoSetFont
        End Get
        Set(ByVal value As Boolean)
            Me.m_AutoSetFont = value
        End Set
    End Property

    Public ReadOnly Property InitCompleted() As Boolean
        Get
            Return Me.m_InitCompleted
        End Get
    End Property

#End Region

#Region "--- Overrides ---"

    '点击状态栏的同步助手能最小化最大化操作
    Protected Overrides ReadOnly Property CreateParams() As System.Windows.Forms.CreateParams
        Get
            Dim cp As CreateParams = MyBase.CreateParams
            cp.Style = cp.Style Or WindowStyle.WS_MINIMIZEBOX   ' 允许最小化操作  

            'If Not Me.DesignMode Then
            '    If Me.m_PosChanged Then
            '        cp.Style = cp.Style And (Not (WindowStyle.WS_THICKFRAME Or WindowStyle.WS_SYSMENU))

            '        If OSHelper.RunOnVista Then
            '            cp.Style = cp.Style And (Not WindowStyle.WS_CAPTION)
            '        End If
            '    Else
            '        cp.Style = cp.Style Or WindowStyle.WS_THICKFRAME
            '        cp.ExStyle = cp.ExStyle And (Not (WindowStyleEx.WS_EX_DLGMODALFRAME Or WindowStyleEx.WS_EX_WINDOWEDGE))
            '        If OSHelper.RunOnVista Then
            '            cp.Style = cp.Style Or WindowStyle.WS_CAPTION
            '        End If
            '    End If
            'End If
            Return cp
        End Get
    End Property

    Private Sub SetWindowRgn()
        'If Me.FilletRadius > 0 AndAlso (Folder.AppType = RunType.AirDroid OrElse Not Common.VerIs30()) Then
        '    Dim hRgn As IntPtr = NativeMethods.CreateRoundRectRgn(0, 0, Me.ClientRectangle.Width + 1, Me.ClientRectangle.Height + 1, Me.FilletRadius, Me.FilletRadius)
        '    NativeMethods.SetWindowRgn(Me.Handle, hRgn, True)
        'End If
    End Sub

    Protected Overrides Sub OnMouseDoubleClick(ByVal e As System.Windows.Forms.MouseEventArgs)
        MyBase.OnMouseDoubleClick(e)

        If Me.Parent Is Nothing AndAlso _
           Me.CanbeMove AndAlso _
           Me.Controls.ContainsKey("btn_normal") AndAlso _
           e.Button = Windows.Forms.MouseButtons.Left AndAlso _
           New Rectangle(0, 0, Me.Width, 90).Contains(e.Location) Then

            If Me.WindowState = FormWindowState.Normal Then
                Me.WindowState = FormWindowState.Maximized
            ElseIf Me.WindowState = FormWindowState.Maximized Then
                Me.WindowState = FormWindowState.Normal
            End If

        End If
    End Sub

    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        MyBase.OnResize(e)

        Me.ResizeControlButton()

        '如果有设置圆角
        Me.SetWindowRgn()

        'If Me.GetType().FullName.Contains("MainForm") Then
        '    Debug.Print("->" & Me.GetType().FullName & vbTab & Me.Location.ToString() & vbTab & Me.Size.ToString())
        'End If
    End Sub

    'Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
    '    MyBase.OnClosed(e)

    '    If Me.Owner IsNot Nothing AndAlso Not (TypeOf Me.Owner Is PNGForm) Then
    '        Me.Owner.Show()
    '        Me.Owner.Activate()
    '    End If
    'End Sub

#End Region

End Class

#Region "---  BaseForm Class  ---"

'当关闭主窗体的时候
Public Enum CloseStatus
    None
    Close
    UnClose
End Enum

#End Region
