﻿'语言包接口
Public Class LanguageInterface

    Private m_LangFolder As String = ""
    Private m_CurrentLang As LangInfo = Nothing

    Public Event DisplayLanguageChanging(ByVal e As System.ComponentModel.CancelEventArgs)
    Public Event DisplayLanguageChanged()

#Region "--- 实例化 ---"

    Private Shared mLanguage As LanguageInterface = Nothing
    Private Shared mLocker As New Object

    Public Shared Function Instance() As LanguageInterface
        Return Instance(Nothing)
    End Function

    Public Shared Function Instance(ByVal cultureInfo As System.Globalization.CultureInfo) As LanguageInterface
        If mLanguage Is Nothing Then
            SyncLock mLocker
                If mLanguage Is Nothing Then
                    Dim strFolder As String = AppDomain.CurrentDomain.BaseDirectory.TrimEnd("\"c) & "\"
                    strFolder = System.IO.Path.Combine(strFolder, "Lang")
                    If cultureInfo Is Nothing Then
                        mLanguage = New LanguageInterface(strFolder)
                    Else
                        mLanguage = New LanguageInterface(strFolder, cultureInfo)
                    End If

                End If
            End SyncLock
        End If
        Return mLanguage
    End Function

    Public Sub New(ByVal LangFolder As String)
        Me.New(LangFolder, System.Globalization.CultureInfo.CurrentCulture)
    End Sub

    Public Sub New(ByVal LangFolder As String, ByVal Language As System.Globalization.CultureInfo)
        Me.m_LangFolder = LangFolder.TrimEnd("\"c, "/"c) & "\"

        Me.m_CurrentLang = New LangInfo(LangFolder, Language)
    End Sub

    Public Sub New(ByVal LangFolder As String, ByVal LanguageName As String)
        Me.m_LangFolder = LangFolder.TrimEnd("\"c, "/"c) & "\"

        Me.m_CurrentLang = New LangInfo(LangFolder, LanguageName)
    End Sub

#End Region

#Region "--- 对外属性 ---"

    Public ReadOnly Property CurrentLanguage() As LangInfo
        Get
            Return Me.m_CurrentLang
        End Get
    End Property

    Public ReadOnly Property Languages() As Collections.ObjectModel.ReadOnlyCollection(Of LangInfo)
        Get
            Return Me.CollectLanguages(Me.m_LangFolder)
        End Get
    End Property

#End Region

#Region "--- 对外函数 ---"

    Public Function GetString(ByVal sKey As String) As String

        Try
            Return Me.m_CurrentLang.GetString(sKey)
        Catch ex As Exception
            Return ""
        End Try

    End Function

    Public Function Contains(ByVal sKey As String) As Boolean

        Try
            Return Me.m_CurrentLang.Contains(sKey)
        Catch ex As Exception
            Return False
        End Try

    End Function

    Public Sub LoadLang(ByVal Language As System.Globalization.CultureInfo)

        '出发将要改变界面语言事件
        Dim e As System.ComponentModel.CancelEventArgs = New System.ComponentModel.CancelEventArgs(False)
        RaiseEvent DisplayLanguageChanging(e)

        If e.Cancel = False Then
            Me.m_CurrentLang = New LangInfo(Me.m_LangFolder, Language)

            '出发界面语言改变完毕事件
            RaiseEvent DisplayLanguageChanged()
        End If

    End Sub

    Public Sub LoadLang(ByVal LanguageName As String)
        '出发将要改变界面语言事件
        Dim e As System.ComponentModel.CancelEventArgs = New System.ComponentModel.CancelEventArgs(False)
        RaiseEvent DisplayLanguageChanging(e)

        If e.Cancel = False Then
            Me.m_CurrentLang = New LangInfo(Me.m_LangFolder, LanguageName)

            '出发界面语言改变完毕事件
            RaiseEvent DisplayLanguageChanged()
        End If

    End Sub

#End Region

#Region "--- 内部函数、事件 ---"

    Private Function CollectLanguages(ByVal LangFolder As String) As Collections.ObjectModel.ReadOnlyCollection(Of LangInfo)

        Dim LangPacks As New List(Of LangInfo)

        For Each strfile As String In My.Computer.FileSystem.GetFiles(LangFolder, FileIO.SearchOption.SearchTopLevelOnly, "*.lang")
            Dim LangPack As New LangInfo(strfile)
            LangPacks.Add(LangPack)

            AddHandler LangPack.LanguageChanged, AddressOf Me.LanguageChanged
        Next

        Return New Collections.ObjectModel.ReadOnlyCollection(Of LangInfo)(LangPacks)
    End Function

    Private Sub LanguageChanged(ByVal LanguageFile As String)
        '改变当前的语言包

        Me.m_CurrentLang = New LangInfo(LanguageFile)

        '触发当前语言包改变事件

        RaiseEvent DisplayLanguageChanged()
    End Sub

#End Region

End Class

'语言包信息

Public Class LangInfo
    Private mLangName As String = ""                            '语言包文件名，不含 .lang
    Private mDisplayName As String = ""                         '语言包显示的名称，如：中文（中华人民共和国）
    Private mLangFile As String = ""                            '语言包文件

    Private mcolLanguage As Collection = CType(Nothing, Collection) '程序语言集合

    Public Event LanguageChanging(ByVal e As System.ComponentModel.CancelEventArgs)
    Public Event LanguageChanged(ByVal LanguageFile As String)

#Region "--- 实例化 ---"

    Public Sub New(ByVal LanguageFile As String)
        Me.New(System.IO.Path.GetDirectoryName(LanguageFile), System.IO.Path.GetFileNameWithoutExtension(LanguageFile))
    End Sub

    Public Sub New(ByVal LangFolder As String, ByVal Language As System.Globalization.CultureInfo)
        Me.New(LangFolder, Language.Name)
    End Sub

    Public Sub New(ByVal LangFolder As String, ByVal LanguageName As String)

        LangFolder = LangFolder & CStr(IIf(LangFolder.EndsWith("\"c), "", "\"))

        If LanguageName.Trim.Length = 0 Then
            LanguageName = "zh-CN"
        End If

        '得到当前运行的程序的可执行文件名称（要去掉 .vshost 这个调试时的文件，不然调试和直接执行的时候会不一样）
        'Dim MainModuleFileName As String = System.IO.Path.GetFileNameWithoutExtension(System.Diagnostics.Process.GetCurrentProcess.MainModule.FileName).Replace(".vshost", "") & "_"
        Dim strLangFile As String = LanguageName & ".lang"

        '如果选择的语言包不存在就试试 en-US.lang
        If My.Computer.FileSystem.FileExists(LangFolder & strLangFile) = False Then

            If LanguageName.ToLower = "zh-hk" OrElse LanguageName.ToLower = "zh-mo" Then
                '香港、澳门的用台湾繁体

                strLangFile = "zh-TW.lang"
            ElseIf LanguageName.ToLower = "zh-sg" Then
                '新加坡用简体

                strLangFile = "zh-CN.lang"
            Else
                '其他用英文

                strLangFile = "en-US.lang"
            End If

            '还不行的话就试试 zh-CN.lang
            If My.Computer.FileSystem.FileExists(LangFolder & strLangFile) = False Then
                strLangFile = "zh-CN.lang"
            End If
        End If

        Me.mLangFile = LangFolder & strLangFile
        Me.mLangName = System.IO.Path.GetFileNameWithoutExtension(strLangFile)

        Try
            Dim cultureInfo As System.Globalization.CultureInfo = System.Globalization.CultureInfo.GetCultureInfo(Me.mLangName)
            If cultureInfo IsNot Nothing Then
                Me.mDisplayName = cultureInfo.Parent.NativeName
            End If
        Catch ex As Exception
            MsgBox("LangInfo.New" & vbCrLf & ex.ToString() & vbTab & mLangName)
            Me.mDisplayName = LanguageName
        End Try

    End Sub

#End Region

    Public ReadOnly Property LangName() As String
        Get
            Return Me.mLangName
        End Get
    End Property

    Public ReadOnly Property LangDisplayName() As String
        Get
            Return Me.mDisplayName
        End Get
    End Property

    Public ReadOnly Property LangFile() As String
        Get
            Return Me.mLangFile
        End Get
    End Property

    Public Sub Load()

        '出发将要改变界面语言事件
        Dim e As System.ComponentModel.CancelEventArgs = New System.ComponentModel.CancelEventArgs(False)
        RaiseEvent LanguageChanging(e)

        If e.Cancel = False Then
            '载入语言包

            Me.LoadLanguagePack(Me.mLangFile)

            '触发语言包改变事件

            RaiseEvent LanguageChanged(Me.LangFile)
        End If

    End Sub

    ''' <summary>
    ''' 从全局语言集合中读取指定 Key 对应的语言内容
    ''' </summary>
    ''' <param name="sKey">Key</param>
    ''' <returns>返回Key对应的语言内容</returns>
    Public Function GetString(ByVal sKey As String) As String
        Dim strReturn As String = ""
        Try

            If Me.mcolLanguage Is Nothing Then
                '如果语言包没有载入，则先载入
                Me.LoadLanguagePack(Me.mLangFile)
            End If

            'LoadLanguage 函数中已经将所有的 Key 转换为小写了，所以这里也转换为小写对应


            '输入的 Key 可以不区分大小写
            sKey = sKey.ToLower

            If Me.mcolLanguage.Contains(sKey) Then
                strReturn = mcolLanguage.Item(sKey).ToString.Replace("\r\n", vbCrLf).Replace("\r", vbCrLf).Replace("\n", vbCrLf)
            Else
                strReturn = ""
            End If

        Catch ex As Exception
            strReturn = ""
        End Try

        Return strReturn
    End Function

    Public Function Contains(ByVal Key As String) As Boolean
        Return Me.mcolLanguage.Contains(Key)
    End Function

    ''' <summary>
    ''' 从指定的语言文件中载入语言信息
    ''' </summary>
    ''' <param name="LangFile">语言包文件地址</param>
    ''' <param name="pstrErrMsg">载入语言包发生错误时的错误信息</param>
    ''' <returns>载入是否成功</returns>
    Private Function LoadLanguagePack(ByVal LangFile As String, Optional ByRef pstrErrMsg As String = "") As Boolean

        Dim blnReturn As Boolean = False

        Try

            Dim strLine As String = CType(Nothing, String)
            Dim strKey As String = ""
            Dim strValue As String = ""
            Dim i As Integer = 0

            mcolLanguage = New Collection

            If System.IO.File.Exists(LangFile) Then
                Using fileLang As New System.IO.StreamReader(LangFile, System.Text.Encoding.UTF8)

                    Do
                        strLine = fileLang.ReadLine()

                        If strLine IsNot Nothing Then
                            strLine = strLine.Trim.Replace(vbTab, "")
                            If Not (strLine.Length = 0 OrElse _
                                    Strings.Left(strLine, 1) = "'" OrElse _
                                    Strings.Left(strLine, 1) = ";" OrElse _
                                    Strings.Left(strLine, 1) = "/") Then

                                i = Strings.InStr(strLine, "=")
                                If i > 0 Then
                                    '全部转换为小写，以便 GetString 读取信息时不区分大小写


                                    strKey = Strings.Left(strLine, i - 1).ToLower.Trim
                                    strValue = Strings.LTrim(Strings.Mid(strLine, i + 1))
                                    strValue = strValue.Replace("\r\n", vbCrLf)
                                    strValue = strValue.Replace("\r", vbCrLf)
                                    strValue = strValue.Replace("\n", vbCrLf)

                                    '添加到全局集合中


                                    If mcolLanguage.Contains(strKey) = False Then
                                        mcolLanguage.Add(strValue, strKey)
                                    End If
                                End If
                            End If
                        End If
                    Loop Until strLine Is Nothing

                End Using

                blnReturn = True
            Else
                Throw New Exception(LangFile & " is not exists!")
            End If

        Catch ex As Exception
            pstrErrMsg = ex.Message
            blnReturn = False
        End Try

        Return blnReturn

    End Function

End Class