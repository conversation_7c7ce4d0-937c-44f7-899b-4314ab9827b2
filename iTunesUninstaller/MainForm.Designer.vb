﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class MainForm
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(MainForm))
        Me.lbCount = New System.Windows.Forms.Label()
        Me.btn_close = New iTunesUninstaller.tbButton()
        Me.btnStart = New iTunesUninstaller.tbButton()
        Me.tbLog = New System.Windows.Forms.TextBox()
        Me.progressBar1 = New iTunesUninstaller.tbControlBar()
        Me.pnlBottom = New System.Windows.Forms.Panel()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.pnlTop = New System.Windows.Forms.Panel()
        Me.lblUninstall = New System.Windows.Forms.Label()
        Me.piciTunes = New System.Windows.Forms.PictureBox()
        Me.pnlButtons1 = New System.Windows.Forms.Panel()
        Me.pnlButtons2 = New System.Windows.Forms.Panel()
        Me.btnCancel = New iTunesUninstaller.tbButton()
        Me.pnlBottom.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTop.SuspendLayout()
        CType(Me.piciTunes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlButtons1.SuspendLayout()
        Me.pnlButtons2.SuspendLayout()
        Me.SuspendLayout()
        '
        'lbCount
        '
        Me.lbCount.AutoSize = True
        Me.lbCount.BackColor = System.Drawing.Color.Transparent
        Me.lbCount.Location = New System.Drawing.Point(133, 39)
        Me.lbCount.Name = "lbCount"
        Me.lbCount.Size = New System.Drawing.Size(101, 12)
        Me.lbCount.TabIndex = 10
        Me.lbCount.Text = "总共几项需要卸载"
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.Location = New System.Drawing.Point(497, 1)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 14
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = True
        Me.btn_close.tbBackgroundImage = Global.iTunesUninstaller.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTunesUninstaller.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTunesUninstaller.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btn_close.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.Gray
        Me.btn_close.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_close.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btnStart
        '
        Me.btnStart.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnStart.BackColor = System.Drawing.Color.Transparent
        Me.btnStart.BindingForm = Nothing
        Me.btnStart.Location = New System.Drawing.Point(327, 16)
        Me.btnStart.Name = "btnStart"
        Me.btnStart.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStart.Selectable = True
        Me.btnStart.Size = New System.Drawing.Size(81, 25)
        Me.btnStart.TabIndex = 15
        Me.btnStart.tbAdriftIconWhenHover = False
        Me.btnStart.tbAutoSize = False
        Me.btnStart.tbAutoSizeEx = False
        Me.btnStart.tbBackgroundImage = Global.iTunesUninstaller.My.Resources.Resources.btn_4_blue
        Me.btnStart.tbBackgroundImageState = iTunesUninstaller.ImageState.FourState
        Me.btnStart.tbBadgeNumber = 0
        Me.btnStart.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStart.tbEndEllipsis = False
        Me.btnStart.tbIconHoldPlace = True
        Me.btnStart.tbIconImage = Nothing
        Me.btnStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.tbIconImageState = iTunesUninstaller.ImageState.OneState
        Me.btnStart.tbIconMore = False
        Me.btnStart.tbIconMouseDown = Nothing
        Me.btnStart.tbIconMouseHover = Nothing
        Me.btnStart.tbIconMouseLeave = Nothing
        Me.btnStart.tbIconPlaceText = 2
        Me.btnStart.tbIconReadOnly = Nothing
        Me.btnStart.tbImageMouseDown = Nothing
        Me.btnStart.tbImageMouseHover = Nothing
        Me.btnStart.tbImageMouseLeave = Nothing
        Me.btnStart.tbReadOnly = False
        Me.btnStart.tbReadOnlyText = False
        Me.btnStart.tbShadow = False
        Me.btnStart.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStart.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStart.tbShowNew = False
        Me.btnStart.tbShowToolTipOnButton = False
        Me.btnStart.tbSplit = "3,3,3,3"
        Me.btnStart.tbText = "确定"
        Me.btnStart.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.tbTextColor = System.Drawing.Color.White
        Me.btnStart.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStart.tbTextColorDown = System.Drawing.Color.White
        Me.btnStart.tbTextColorHover = System.Drawing.Color.White
        Me.btnStart.tbTextMouseDownPlace = 0
        Me.btnStart.tbToolTip = ""
        Me.btnStart.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStart.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStart.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.VisibleEx = True
        '
        'tbLog
        '
        Me.tbLog.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tbLog.BackColor = System.Drawing.Color.White
        Me.tbLog.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.tbLog.Location = New System.Drawing.Point(1, 1)
        Me.tbLog.Multiline = True
        Me.tbLog.Name = "tbLog"
        Me.tbLog.ReadOnly = True
        Me.tbLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical
        Me.tbLog.Size = New System.Drawing.Size(499, 187)
        Me.tbLog.TabIndex = 0
        '
        'progressBar1
        '
        Me.progressBar1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.progressBar1.BackColor = System.Drawing.Color.Transparent
        Me.progressBar1.Location = New System.Drawing.Point(133, 61)
        Me.progressBar1.Name = "progressBar1"
        Me.progressBar1.Size = New System.Drawing.Size(341, 14)
        Me.progressBar1.TabIndex = 0
        Me.progressBar1.tbBackgroundImage = CType(resources.GetObject("progressBar1.tbBackgroundImage"), System.Drawing.Image)
        Me.progressBar1.tbCanDragValue = False
        Me.progressBar1.tbDirection = iTunesUninstaller.tbControlBar.tbBarDirection.Horizontal
        Me.progressBar1.tbDotImage = CType(resources.GetObject("progressBar1.tbDotImage"), System.Drawing.Image)
        Me.progressBar1.tbDotImageState = iTunesUninstaller.ImageState.ThreeState
        Me.progressBar1.tbDotMouseDown = Nothing
        Me.progressBar1.tbDotMouseHover = Nothing
        Me.progressBar1.tbDotMouseLeave = Nothing
        Me.progressBar1.tbDownloadImage = Global.iTunesUninstaller.My.Resources.Resources.app_downloads_background
        Me.progressBar1.tbDownMax = 100
        Me.progressBar1.tbDownMin = 0
        Me.progressBar1.tbDownValue = 0
        Me.progressBar1.tbIsWaiting = False
        Me.progressBar1.tbPlayImage = Global.iTunesUninstaller.My.Resources.Resources.app_downloads_value
        Me.progressBar1.tbPlayMax = 100
        Me.progressBar1.tbPlayMin = 0
        Me.progressBar1.tbPlayValue = 0
        Me.progressBar1.tbShowDot = False
        Me.progressBar1.tbShowText = False
        Me.progressBar1.tbSplit = "3,3,3,3"
        '
        'pnlBottom
        '
        Me.pnlBottom.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlBottom.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlBottom.Controls.Add(Me.Panel1)
        Me.pnlBottom.Controls.Add(Me.PictureBox1)
        Me.pnlBottom.Controls.Add(Me.progressBar1)
        Me.pnlBottom.Controls.Add(Me.lbCount)
        Me.pnlBottom.Location = New System.Drawing.Point(1, 32)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(520, 313)
        Me.pnlBottom.TabIndex = 17
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(226, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(226, Byte), Integer))
        Me.Panel1.Controls.Add(Me.tbLog)
        Me.Panel1.Location = New System.Drawing.Point(10, 114)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(501, 189)
        Me.Panel1.TabIndex = 17
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.iTunesUninstaller.My.Resources.Resources.icon_itunesuninstall
        Me.PictureBox1.Location = New System.Drawing.Point(39, 19)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(85, 83)
        Me.PictureBox1.TabIndex = 16
        Me.PictureBox1.TabStop = False
        '
        'pnlTop
        '
        Me.pnlTop.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.lblUninstall)
        Me.pnlTop.Controls.Add(Me.piciTunes)
        Me.pnlTop.Controls.Add(Me.pnlButtons1)
        Me.pnlTop.Location = New System.Drawing.Point(1, 367)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(520, 313)
        Me.pnlTop.TabIndex = 18
        '
        'lblUninstall
        '
        Me.lblUninstall.BackColor = System.Drawing.Color.Transparent
        Me.lblUninstall.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblUninstall.Location = New System.Drawing.Point(40, 150)
        Me.lblUninstall.Name = "lblUninstall"
        Me.lblUninstall.Size = New System.Drawing.Size(440, 34)
        Me.lblUninstall.TabIndex = 19
        Me.lblUninstall.Text = "总共几项需要卸载"
        Me.lblUninstall.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'piciTunes
        '
        Me.piciTunes.Image = Global.iTunesUninstaller.My.Resources.Resources.icon_itunes
        Me.piciTunes.Location = New System.Drawing.Point(222, 66)
        Me.piciTunes.Name = "piciTunes"
        Me.piciTunes.Size = New System.Drawing.Size(76, 76)
        Me.piciTunes.TabIndex = 18
        Me.piciTunes.TabStop = False
        '
        'pnlButtons1
        '
        Me.pnlButtons1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlButtons1.BackColor = System.Drawing.Color.FromArgb(CType(CType(212, Byte), Integer), CType(CType(212, Byte), Integer), CType(CType(212, Byte), Integer))
        Me.pnlButtons1.Controls.Add(Me.pnlButtons2)
        Me.pnlButtons1.Location = New System.Drawing.Point(0, 255)
        Me.pnlButtons1.Name = "pnlButtons1"
        Me.pnlButtons1.Size = New System.Drawing.Size(520, 57)
        Me.pnlButtons1.TabIndex = 17
        '
        'pnlButtons2
        '
        Me.pnlButtons2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlButtons2.BackColor = System.Drawing.Color.FromArgb(CType(CType(226, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(235, Byte), Integer))
        Me.pnlButtons2.Controls.Add(Me.btnCancel)
        Me.pnlButtons2.Controls.Add(Me.btnStart)
        Me.pnlButtons2.Location = New System.Drawing.Point(0, 1)
        Me.pnlButtons2.Name = "pnlButtons2"
        Me.pnlButtons2.Size = New System.Drawing.Size(520, 56)
        Me.pnlButtons2.TabIndex = 20
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Location = New System.Drawing.Point(423, 16)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(81, 25)
        Me.btnCancel.TabIndex = 16
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTunesUninstaller.My.Resources.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = iTunesUninstaller.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTunesUninstaller.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "3,3,3,3"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.Black
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'MainForm
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(522, 692)
        Me.Controls.Add(Me.pnlTop)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.pnlBottom)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MinimumSize = New System.Drawing.Size(20, 66)
        Me.Name = "MainForm"
        Me.tbGuiBackground = Global.iTunesUninstaller.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,33"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "iTunes卸载"
        Me.pnlBottom.ResumeLayout(False)
        Me.pnlBottom.PerformLayout()
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTop.ResumeLayout(False)
        CType(Me.piciTunes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlButtons1.ResumeLayout(False)
        Me.pnlButtons2.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Private WithEvents lbCount As System.Windows.Forms.Label
    Friend WithEvents btn_close As tbButton
    Friend WithEvents btnStart As tbButton
    Private WithEvents tbLog As System.Windows.Forms.TextBox
    Friend WithEvents progressBar1 As iTunesUninstaller.tbControlBar
    Friend WithEvents pnlBottom As System.Windows.Forms.Panel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents pnlTop As System.Windows.Forms.Panel
    Friend WithEvents pnlButtons1 As System.Windows.Forms.Panel
    Private WithEvents lblUninstall As System.Windows.Forms.Label
    Friend WithEvents piciTunes As System.Windows.Forms.PictureBox
    Friend WithEvents pnlButtons2 As System.Windows.Forms.Panel
    Friend WithEvents btnCancel As iTunesUninstaller.tbButton
End Class
