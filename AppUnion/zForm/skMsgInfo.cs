﻿using System;
using System.Drawing;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Text;

using iTong.CoreFoundation;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Image = AppKit.NSImage;
#else
using System.Drawing.Drawing2D;
using System.Drawing;
#endif

namespace iTong.CoreModule
{
    /// <summary>
    /// 用于获取颜色
    /// </summary>
    public static class skColor
    {
        public static Color Empty
        {
            get
            {
#if MAC
                return null;
#else
                return Color.Empty;
#endif
            }
        }

        public static Color Transparent
        {
            get
            {
#if MAC
                return null;
#else
                return Color.Transparent;
#endif
            }
        }

        public static Color DodgerBlue
        {
            get
            {
#if MAC
                return NSColor.FromRgb(30,144,255);
#else
                return Color.DodgerBlue;
#endif
            }
        }

#if MAC
        public static NSColor FromArgb(int r, int g, int b)
        {
            return NSColor.FromRgb(r, g, b);
        }

        public static NSColor FromArgb(int a, int r, int g, int b)
        {
            return NSColor.FromRgba(r, g, b, a);
        }
#else
        /// <summary>
        /// 获取r b g颜色
        /// </summary>
        /// <returns>返回Color</returns>
        public static Color FromArgb(int r, int g, int b)
        {
            return Color.FromArgb(r, g, b);
        }

        /// <summary>
        /// 获取带透明度的a r b g颜色
        /// </summary>
        /// <returns>返回Color</returns>
        public static Color FromArgb(int a, int r, int g, int b)
        {
            return Color.FromArgb(r, g, b, a);
        }
#endif
    }

    public enum skToolTipType
    {
        None,
        Hint,
        Warning
    }

    public class skMsgInfo
    {
        public string CheckBoxText = string.Empty;
        public string LinkLabelUrl = string.Empty;
        public string LinkLabelText = string.Empty;

        public string FirstButtonText = string.Empty;
        public string SecondButtonText = String.Empty;
        public string ThreeButtonText = String.Empty;

        public string ToolTipText = string.Empty;
        public skToolTipType ToolTipType = skToolTipType.Hint;

        public bool Checked = false;
        public bool IsDelFile = false;
        public bool IsShowTip = true;

        public bool FirstButtonIsEnabledWhenCheckBoxChecked = false;
        public bool IsNeedConfirm = false;
        public Image Picture;
        public Size PictureSize = Size.Empty;

        public string MessageDescription = string.Empty;
       
        public skBorderType FormBorderType = skBorderType.Round;
        public skBorderRadius FormBorderRadius = new skBorderRadius(0);
        public Size IconSize;
        public MessageBoxDefaultButton DefaultButton = MessageBoxDefaultButton.Button1;

        public string ErrorMsg = string.Empty;
        public string InputText = string.Empty;
        public string SourceText = string.Empty;
        public string EmptyText = string.Empty;
        public bool IsPwdMode = false;
        public bool IsFirstButtonOnLeft = false;

        public int OKTime = 0;
        public int CancelTime = 0;

        public Color OKColor = Color.White;
        public Color CancelColor = Color.Black;

        public bool IsTimeout = false;
        public bool TopMost = false;

#if MAC
        public NSImage FormIcon;
        public NSView Window;
#else
         public Icon FormIcon;
        public Form Window;
#endif

        public bool WindowCancel = false;
    }

    public class skMsgControl
    {
        public Size mSize = Size.Empty;
        public Size Size
        {
            get { return mSize; }
            set
            {
                mSize = value;//.ToDPI();
            }
        }

        private Point mLocation = Point.Empty;
        public Point Location
        {
            get { return mLocation; }
            set
            {
                mLocation = value;//.ToDPI();
            }
        }

        public Padding mPadding = Padding.Empty;
        private Padding mPaddingSource = Padding.Empty;
        public Padding Padding
        {
            get { return mPaddingSource; }
            set
            {
                mPaddingSource = value;
                mPadding = value;//.ToDPI();
            }
        }
        public Padding mMargin = Padding.Empty;
        private Padding mMarginSource = Padding.Empty;

        /// <summary>
        /// 与其他控件的间隔
        /// </summary>

        public Padding Margin
        {
            get { return mMarginSource; }
            set
            {
                mMarginSource = value;
                mMargin = value;//.ToDPI();
            }
        }

        public bool Visible = true;

        public Image skBackgroundImage;
        public skImageState skBackgroundImageState = skImageState.OneState;

        /// 边框样式
        /// </summary>
        public skBorderType skBorderType = skBorderType.None;

        /// <summary>
        /// 边框颜色
        /// </summary>
        public Color skBorderStrokeColor = skColor.FromArgb(220, 224, 228);

        /// <summary>
        /// 边框的四个圆角值
        /// </summary>
        public skBorderRadius skBorderRadius = skBorderRadius.Empty;
    }

    public class skMsgTextBase : skMsgControl
    {
        public string skText = string.Empty;
        public Font skTextFont = MyFont.DefaultFont;
        public Color skTextColor = Color.White;

#if MAC
        public NSTextAlignment skTextAlign = NSTextAlignment.Center;
#else
        public ContentAlignment skTextAlign = ContentAlignment.MiddleCenter;
#endif
    }

    public class skMsgPicture : skMsgControl
    {
        public skMsgPicture()
        {
            this.Size = new Size(56, 56);
        }

        public Image Image;    
    }

    public class skMsgButton : skMsgTextBase
    {
        public skMsgButton()
        {
            this.Size = new Size(120, 32);
            this.Padding = new Padding(10, 0, 0, 0);
        }

        /// <summary>
        /// 图标大小
        /// </summary>

        public Size skIconSize = Size.Empty;

        /// <summary>
        /// 按钮图标
        /// </summary>
        public Image skIcon;

        /// <summary>
        /// 按钮图标几态图
        /// </summary>
        public skImageState skIconState = skImageState.OneState;

        /// <summary>
        /// 倒计时ClickTime秒，自动点击按钮
        /// </summary>
        public int ClickTime = 0;

        /// <summary>
        /// 倒计时EnableTime秒，按钮可点击，之前为不可用状态
        /// </summary>
        public int EnableTime = 0;

        /// <summary>
        /// 是否为超时
        /// </summary>
        public bool IsTimeout = false;

        /// <summary>
        /// 非模态弹窗时，点击按钮回调事件
        /// </summary>
        public Action Click;
    }

    public class skMsgCheckBoxInfo : skMsgButton
    {
        public Image skIconCheck;
        public bool skChecked = true;
    }

    public class skMsgPwdButton : skMsgButton
    {
        /// <summary>
        /// 隐藏密码图标
        /// </summary>
        public Image skIconForHide;

        /// <summary>
        /// 显示密码图标
        /// </summary>
        public Image skIconForShow;
    }

    public class skMsgInput : skMsgTextBase
    {
        public skMsgInput()
        {
            this.Visible = false;

            this.skTextFont = MyFont.CreateFont("微软雅黑", 10);
            this.skTextColor = skColor.FromArgb(51, 55, 59);
            this.skBorderStrokeColor= skColor.FromArgb(220, 224, 228);
            this.skBorderStrokeColorFocus = skColor.FromArgb(60, 119, 255);
        }

        public skMsgButton ButtonClear;

        public skMsgPwdButton ButtonPassword;
       
        public bool IsPasswordMode = false;

        public char PasswordChar = '*';

        public int MaxLength = -1;
        public int MinLength = -1;

        /// <summary>
        /// 输入文本框有焦点时边框颜色
        /// </summary>
        public Color skBorderStrokeColorFocus = skColor.FromArgb(60, 119, 255);

        /// <summary>
        /// 输入框为空时的提示语
        /// </summary>
        public string PlaceHolder = string.Empty;
    }

    public class skMsgText : skMsgTextBase
    {
        public skMsgText()
        {
            this.skTextColor = Color.Black;
            this.skTextFont = MyFont.CreateFont("微软雅黑", 10);
        }

        public skMsgLinkText skMsgLinkInfo = new skMsgLinkText();

        public bool skWordWrapping = false;
        public int skLineSpace = 0;
    }

    public class skMsgLinkText : skMsgTextBase
    {
        public skMsgLinkText()
        {
            this.skTextColor = Color.Black;
            this.skTextFont = MyFont.CreateFont("微软雅黑", 10);
        }

        /// <summary>文本中的icon(默认使用%来占位)</summary>
        public Image skTextInsideIcon = null;
        /// <summary>文本中的icon的大小</summary>
        public Size skTextInsideIconSize = new Size(0, 0);
        /// <summary>文本中的icon识别的字符串地方</summary>
        public string skTextInsideIconStrPlace = "";

        public bool skWordWrapping = false;
        public int skLineSpace = 0;
    }

    public class skMsgInfoNew : skMsgInfo
    {
        public Guid MsgGuid = Guid.NewGuid();

        public skMsgButton FirstButton = new skMsgButton();
        public skMsgButton SecondButton = new skMsgButton() { skTextColor = Color.Black };
        public skMsgButton ThreeButton = new skMsgButton() { skTextColor = Color.Black };

        public skMsgInput InputBoxInfo = new skMsgInput();

        public skMsgText MessageInfo = new skMsgText();

        public skMsgText DescriptionInfo = new skMsgText();

        public skMsgPicture PictureInfo = new skMsgPicture();

        public skMsgCheckBoxInfo CheckBoxInfo = new skMsgCheckBoxInfo();

        public skMsgText ErrorInfo;

        public int DiffX = 20/*.ToDPI()*/;
        public int DiffY = 10/*.ToDPI()*/;
        public int DiffButton = 10/*.ToDPI()*/;

        public MessageBoxButtons Buttons = MessageBoxButtons.OKCancel;
        public Padding ButtonPadding = new Padding(10, 0, 10, 0);//.ToDPI();


        public string skTitle = string.Empty;
        public Color skTitleBackgroundColor = Color.White;
        public Color skTitleColor = Color.White;
        public Font skTitleFont = MyFont.DefaultFont;

        public int skTitleBarHeight;


        private Size mFormMinSize = new Size(432, 184);
        public Size FormMinSize
        {
            get { return mFormMinSize/*.ToDPI()*/; }
            set
            {
                mFormMinSize = value;
            }
        }

        //public Size FormMinSize = new Size(432, 184);
        public bool FormDialog = true;
        /// <summary>弹窗的一瞬间置顶一次后续不在置顶</summary>
        public bool FormTopMost = false;
        public Image FormTranslateImage;
        public Padding FormTransparentImagePadding = new Padding(4);
        public Action FormClose;

        private Padding mFormPadding = new Padding(25, 15, 25, 15);//.ToDPI();
        public Padding FormPadding
        {
            get { return mFormPadding; }
            set
            {
                mFormPadding = value;//.ToDPI();
            }
        }

        public Action EventCallBack;
    }

    public class skSplashInfo
    {
        public int ShowTime = -1;
        public int MaxWidth = -1;
        public int MinWidth = -1;

        public Padding Padding = Padding.Empty;

      
        public skSplit skSplit = skSplit.Empty;
        public Image skBackgroundImage = null;
        public skImageState skBackgroundImageState = skImageState.OneState;

        public skImageAlignment skLocationAlignment = skImageAlignment.Bottom;
        public bool IsWordWrap = true;

        public string skText = string.Empty;
        public Font skTextFont = MyFont.DefaultFont;

        /// <summary>
        /// 行间隔
        /// </summary>
        public int LineSpacing = 2;

        public bool TopMost = false;
     
        public Color skTextColor = Color.White;

#if MAC
        public Color skBackgroundColor = null;
        public NSTextAlignment skTextAlign = NSTextAlignment.Left;
#else
        public Color skBackgroundColor = Color.Empty;
        public ContentAlignment skTextAlign = ContentAlignment.TopLeft;
#endif

    }
}
