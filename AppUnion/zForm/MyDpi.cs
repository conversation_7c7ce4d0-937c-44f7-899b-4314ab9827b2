﻿using System;
using System.IO;
using System.Text;
using System.Reflection;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Runtime.InteropServices;

using iTong.CoreFoundation;
using iTong.Android;
using Microsoft.Win32;


namespace iTong.CoreModule
{

    public static class MyDpi
    {
        private const uint LOGPIXELSX = 88;
        private const uint USER_DEFAULT_SCREEN_DPI = 96;

        private enum WINDOWS_POSITION
        {
            HWND_NOTOPMOST = -2,
            HWND_TOPMOST = -1,
            HWND_TOP = 0,
            HWND_BOTTOM = 1
        }

        private enum WINDOWS_FLAGS
        {
            SWP_NOSIZE = 0x0001,
            SWP_NOMOVE = 0x0002,
            SWP_NOZORDER = 0x0004,
            SWP_NOREDRAW = 0x0008,
            SWP_NOACTIVATE = 0x0010,
            SWP_FRAMECHANGED = 0x0020,
            SWP_DRAWFRAME = 0x0020,
            SWP_SHOWWINDOW = 0x0040,
            SWP_HIDEWINDOW = 0x0080,
            SWP_NOCOPYBITS = 0x0100,
            SWP_NOOWNERZORDER = 0x0200,
            SWP_NOREPOSITION = 0x0200,
            SWP_NOSENDCHANGING = 0x0400,
            SWP_DEFERERASE = 0x2000,
            SWP_ASYNCWINDOWPOS = 0x4000
        }


        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }
        private enum DeviceCap
        {
            LOGPIXELSX = 88,
            LOGPIXELSY = 90
        }

        //For Windows 10版本 1607
        [DllImport("user32.dll")]
        private extern static uint GetDpiForSystem();

        [DllImport("user32.dll")]
        private extern static uint GetDpiForWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private extern static IntPtr GetDC(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern IntPtr GetDesktopWindow();

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hWnd, out RECT lpRect);

        [DllImport("user32.dll")]
        private static extern IntPtr GetWindowDC(IntPtr hWnd);

        [DllImport("user32.dll")]
        private extern static int ReleaseDC(IntPtr hWnd, IntPtr hDC);

        [DllImport("user32.dll")]
        private extern static bool SetWindowPos(IntPtr hWnd, IntPtr hWndInsertAfter, int x, int y, int cx, int cy, uint uFlags);

        [DllImport("user32.dll")]
        private static extern IntPtr MonitorFromWindow(IntPtr handle, int flags);

        [DllImport("user32.dll")]
        private static extern bool InvalidateRect(IntPtr hWnd, IntPtr lpRect, bool bErase);

        [DllImport("user32.dll")]
        private static extern bool ValidateRect(IntPtr hWnd, IntPtr lpRect);

        [DllImport("Gdi32.dll", CharSet = CharSet.Auto)]
        private static extern IntPtr CreateDC(string lpszDriver, string lpszDevice, string lpszOutput, IntPtr lpInitData);

        [DllImport("Gdi32.dll")]
        private extern static int GetDeviceCaps(IntPtr hWnd, int index);

        [DllImport("Gdi32.dll")]
        private static extern bool DeleteDC(IntPtr hdc);

        [DllImport("Shcore.dll", SetLastError = true)]
        public extern static int SetProcessDpiAwareness(int value);

        [DllImport("SHCore.dll", SetLastError = true)]
        private static extern void GetProcessDpiAwareness(IntPtr hprocess, out int awareness);

        [DllImport("Shcore.dll")]
        private static extern int GetDpiForMonitor(IntPtr hMonitor, int dpiType, out uint dpiX, out uint dpiY);


        public static int DefaultDPI = (int)USER_DEFAULT_SCREEN_DPI;


        public static bool UseHD { get; set; } = true;

        private static object mLocker = new object();

        public static float DPIScale { get; private set; } = 1.0f;

        public static int DPI { get; private set; } = 96;

        static MyDpi()
        {
            UseHD = UseHD || File.Exists(Path.Combine(Folder.AppFolder, "HD.dll"));

#if DPI
            DPI = MyDpi.GetDPI(IntPtr.Zero);
            if (DPI > MyDpi.DefaultDPI)
            {
                // 主屏即系统屏放大
                DPIScale = DPI / (float)MyDpi.DefaultDPI;
            }
            SystemEvents.DisplaySettingsChanged += SystemEvents_DisplaySettingsChanged;
#endif
        }

#if DPI
        private static async void SystemEvents_DisplaySettingsChanged(object sender, EventArgs e)
        {
            await System.Threading.Tasks.Task.Delay(3000);

            DPI = GetDpiFromWindows(MyForm.GetMainForm());
            DPIScale = DPI / (float)MyDpi.DefaultDPI;

            foreach (object item in MyForm.OpenForms)
            {
                if (item is skForm form)
                    form.DpiChangeCallBack();
            }
        }
#endif

        public static int GetDpiFromWindows(Form form)
        {
            if (form == null)
                return DPI;

            return MyDpi.GetDPI(form.Handle);
        }

        public static int GetDpiFromControl(Control control)
        {
            Form form = null;
            if (control != null)
                form = control.FindForm();
            else
                form = MyForm.GetMainForm();

            if (form is skForm formBase)
                return formBase.Dpi;

            return GetDpiFromWindows(form);
        }

        /// <summary>
        /// 获取DPI
        /// </summary>
        public static int GetDPI(IntPtr hWnd)
        {
            int dpi = (int)DefaultDPI;

            if (!UseHD)
                goto DoExit;

            try
            {
                if (hWnd != IntPtr.Zero)
                    dpi = (int)GetDpiForWindow(hWnd);
                else
                    dpi = (int)GetDpiForSystem();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "GetWindowDPI.1");

                try
                {
                    dpi = GetDeviceCaps(hWnd, (int)LOGPIXELSX);
                }
                catch (Exception ex2)
                {
                    Common.LogException(ex2, "GetWindowDPI.2");
                }
            }

#if DPI
            if (dpi == 0)
            {
                try
                {
                    dpi = GetDeviceCaps(GetDC(IntPtr.Zero), (int)LOGPIXELSX);
                }
                catch
                { }
            }
#endif

            DoExit:
            return dpi;
        }

        public static void CheckDPI(Form frm)
        {
            if (frm == null)
                return;

            int dpi = GetDpiFromWindows(frm);

            skForm frmDpi = frm as skForm;
            if (frmDpi != null && frmDpi.Dpi != dpi)
            {
                frmDpi.DpiChangeCallBack();
            }
        }

        internal static void OnWndProc(object sender, WinMsg e)
        {
            switch ((SK_WindowMessage)e.m.Msg)
            {

                case SK_WindowMessage.WM_WINDOWPOSCHANGED:
                    //case SK_WindowMessage.WM_EXITSIZEMOVE:
                    {
                        CheckDPI(sender as Form);
                        break;
                    }

                    //case SK_WindowMessage.WM_ENTERSIZEMOVE:
                    //    {

                    //        break;
                    //    }
                    //default:
                    //    {
                    //        break;
                    //    }
            }
        }

        internal delegate void DpiChangedHanlder(skForm frm, int newDpi);


        internal static void OnDpiChanged(skForm frm, int newDpi)
        {
            try
            {
                lock (mLocker)
                {
                    if (frm.InvokeRequired)
                    {
                        frm.Invoke(new DpiChangedHanlder(OnDpiChanged), frm, newDpi);
                    }
                    else
                    {
                        double rate = newDpi / (double)DefaultDPI;
                        int newWidth = (int)(frm.OriginalSize.Width * rate);
                        int newHeight = (int)(frm.OriginalSize.Height * rate);

                        int oldDpi = frm.Dpi;
                        Size oldSize = frm.Size;
                        Size newSize = new Size(newWidth, newHeight);

                        frm.Size = newSize;

                        frm.SetDpi(newDpi);

                        string msg = string.Format("{0}->Tile={1}, Location={2}, oldSize={3}, newSize={4}, oldDpi={5}, newDpi={6}",
                            frm.GetType().FullName,
                            frm.Text,
                            frm.Location,
                            oldSize.ToString(),
                            newSize.ToString(),
                            oldDpi,
                            newDpi);
#if !WX
                        MyLog.LogFile(msg, "Dpi");
#endif
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "OnDpiChanged");
            }
        }

        public static int GetDPI(this Control control)
        {
            int frmDpi = DefaultDPI;

            if (control != null)
            {
                Form currentForm = control.FindForm();
                if (currentForm != null)
                {
                    if (currentForm is skForm form && form.HasShown)
                        frmDpi = form.Dpi;
                    else
                        frmDpi = GetDPI(currentForm.Handle);
                }
            }

            return frmDpi;
        }

        public static Padding ToDPI(this Padding padding, Control control)
        {
            int frmDpi = control.GetDPI();
            return padding.ToDPI(frmDpi);
        }

        public static Padding ToDPI(this Padding padding, int frmDpi = 0)
        {
            return new Padding(padding.Left.ToDPI(frmDpi), padding.Top.ToDPI(frmDpi), padding.Right.ToDPI(frmDpi), padding.Bottom.ToDPI(frmDpi));
        }

        public static Size ToDPI(this Size size, Control control)
        {
            int frmDpi = control.GetDPI();
            return size.ToDPI(frmDpi);
        }

        public static Size ToDPI(this Size size, int frmDpi = 0)
        {
            return new Size(size.Width.ToDPI(frmDpi), size.Height.ToDPI(frmDpi));
        }

        public static Point ToDPI(this Point point, Control control)
        {
            int frmDpi = control.GetDPI();
            return point.ToDPI(frmDpi);
        }

        public static Point ToDPI(this Point padding, int frmDpi = 0)
        {
            return new Point(padding.X.ToDPI(frmDpi), padding.Y.ToDPI(frmDpi));
        }

        public static int ToDPI(this int value, Control control)
        {
            int frmDpi = control.GetDPI();
            return value.ToDPI(frmDpi);
        }

        public static int ToDPI(this int source, int frmDpi = 0)
        {
            return ToDPI((double)source, frmDpi);
        }

        public static int ToDPI(this long source, int frmDpi = 0)
        {
            return ToDPI((double)source, frmDpi);
        }

        public static int ToDPI(this short source, int frmDpi = 0)
        {
            return ToDPI((double)source, frmDpi);
        }

        public static int ToDPI(this float source, int frmDpi = 0)
        {
            return ToDPI((double)source, frmDpi);
        }

        public static int ToDPI(this double source, int frmDpi = 0)
        {
#if MAC || !DPI
            return (int)source;
#else
            if (source == 0)
                return 0;

            float scale = DPIScale;
            if (frmDpi > 0)
                scale = (float)frmDpi / (float)DefaultDPI;

            return (int)(source * scale);
#endif
        }

        public static Size ReDPI(this Size size, int frmDpi = 0)
        {
            return new Size(size.Width.ReDPI(frmDpi), size.Height.ReDPI(frmDpi));
        }

        public static int ReDPI(this int source, int frmDpi = 0)
        {
#if MAC || !DPI
            return (int)source;
#else
            if (source == 0)
                return 0;

            float scale = DPIScale;
            if (frmDpi != 0)
                scale = (float)frmDpi / (float)DefaultDPI;

            return (int)(source / scale);
#endif
        }

        public static void Scale(this Control control)
        {
#if DPI
            float scale = (float)GetDPI(control) / (float)MyDpi.DefaultDPI;
            control.Scale(new SizeF(scale, scale));
#endif
        }
    }
}
