﻿using System;
using System.IO;
using System.Text;
using System.Collections.Generic;
using System.ComponentModel;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Globalization;

using iTong.CoreFoundation;
using iTong.Android;


#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Image = AppKit.NSImage;
#else
using System.Drawing.Drawing2D;
using System.Drawing;
#endif

namespace iTong.CoreModule
{
#if AIR
    public class IniSetting
    {
    #region "--- 设置语言 ---"

        public static string GetLanguage()
        {
            return IniClass.GetIniSectionKey("Setting", "Language", Folder.ConfigIniFile);
        }

        public static void SetLanguage(string strLangKey)
        {
            IniClass.SetIniSectionKey("Setting", "Language", strLangKey, Folder.ConfigIniFile);
        }

    #endregion
    }
#endif

    public class skForm : skFormBase
    {

        static skForm()
        {
            MyFont.Init();
        }

        public static void ReplaceFont(Control ctl)
        {
            try
            {
                //if (ctl is skForm)
                //{
                //    skForm frmSK = ctl as skForm;
                //    frmSK.skTitleFont = frmSK.skTitleFont.ReplaceFont();
                //}
                //else if (ctl is skButton)
                //{
                //    skButton btn = ctl as skButton;
                //    btn.skTextFont = btn.skTextFont.ReplaceFont();
                //    btn.skToolTipFont = btn.skToolTipFont.ReplaceFont();
                //    btn.skBadgeFont = btn.skBadgeFont.ReplaceFont();
                //}
                //else if (ctl is skLabel)
                //{
                //    skLabel lbl = ctl as skLabel;
                //    lbl.skTextFont = lbl.skTextFont.ReplaceFont();
                //}
                //else if (ctl is skTextBoxNew)
                //{
                //    skTextBoxNew txt = ctl as skTextBoxNew;
                //    txt.skTitleFont = txt.skTitleFont.ReplaceFont();
                //}
                //else if (ctl is skTextBox)
                //{
                //    skTextBox txt = ctl as skTextBox;
                //    txt.Font = txt.Font.ReplaceFont();
                //}

                if (ctl is Form || ctl is Panel || ctl.GetType().Name.StartsWith("sk"))
                {

                }
                else
                {
                    if (ctl.Font != null)
                        ctl.Font = ctl.Font.ReplaceFont();
                }

                foreach (Control ctlSub in ctl.Controls)
                {
                    ReplaceFont(ctlSub);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "skForm.ReplaceFont");
            }
        }

        #region DPI变化

        [Browsable(false)]
        public bool HasShown { get; protected set; } = false;

        [Browsable(false)]
        public int Dpi { get; private set; } = MyDpi.DefaultDPI;

        [Browsable(false)]
        public int OldDpi { get; private set; } = MyDpi.DefaultDPI;

        [Browsable(false)]
        public float DpiScale { get { return this.Dpi / (float)MyDpi.DefaultDPI; } }

        [Browsable(false)]
        public Size OriginalSize { get; set; } = Size.Empty;

        [Browsable(false)]
        public virtual bool InitDpi { get; set; } = true;

        public void SetDpi(int value)
        {
            this.OldDpi = this.Dpi;
            this.Dpi = value;
        }

        #endregion


        public skForm() : base()
        {
#if DPI
            if (InitDpi)
                this.UpdateDpi();
#endif

            this.SetStyle(ControlStyles.UserPaint, true);
            this.SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            this.SetStyle(ControlStyles.ResizeRedraw, true);
            this.SetStyle(ControlStyles.DoubleBuffer, true);
            this.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            this.SetStyle(ControlStyles.ContainerControl, true);    //为容器时，才能在设计中添加控件
            this.SetStyle(ControlStyles.Selectable, false);

            this.KeyPreview = true;         //向窗体注册键盘事件

            this.Language = LanguageInterface.Instance();

            this.mIcon = this.GetImage("icon_panda_80.png");
            this.mIconSize = new Size(20, 20);

            this.mBackgroundColor = skColor.FromArgb(241, 242, 243);

            this.skTitleBackgroundColor = skColor.FromArgb(0, 118, 246);

            this.mStatusBarLableTextColor = skColor.FromArgb(127, 128, 129);
            this.mStatusBarBackgroundColor = skColor.FromArgb(235, 235, 235);

            this.FormBorderStyle = FormBorderStyle.None;

            this.btnClose = this.CreateButton("btnClose", "btn_close_3.png", skImageState.ThreeState);
            this.btnMax = this.CreateButton("btnMax", "btn_max_3.png", skImageState.ThreeState);
            this.btnMin = this.CreateButton("btnMin", "btn_min_3.png", skImageState.ThreeState);

            this.InitButtonIcon();
        }


        protected virtual void InitButtonIcon()
        {
            try
            {
                if (Folder.IsAirDroidCast || Folder.IsAirDroidPersonal)
                {
                    Image imgClose = this.GetImage("btn_3_close_white.png");
                    if (imgClose != null)
                        this.btnClose.skIcon = imgClose;
                }

                if (Folder.AppType == RunType.RemoteSupport)
                {
                    Image imgClose = this.GetImage("ic_close_2x.png");
                    if (imgClose != null)
                    {
                        this.btnClose.skIcon = imgClose;
                        this.btnClose.skIconState = skImageState.FourState;
                        this.btnClose.skIconSize = new Size(32, 22);
                        this.btnClose.Size = new Size(32, 22);
                    }

                    Image imgMin = this.GetImage("btn_minimize_4_2x.png");
                    if (imgMin != null)
                    {
                        this.btnMin.skIcon = imgMin;
                        this.btnMin.skIconState = skImageState.FourState;
                        this.btnMin.skIconSize = new Size(24, 24);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "skForm.InitButton");
            }
        }

        public virtual bool CanReplaceFont { get; set; } = true;

        protected bool mHasLoadForm = false;
        protected bool mHasLoadFormButton = false;

        public virtual LanguageInterface Language { get; set; }

        protected int mTitleBarHeight = 36;
        protected int mTitleBarHeightSource = 36;

        public int skTitleBarHeight
        {
            get { return mTitleBarHeight; }
            set
            {
                //if (mTitleBarHeight != value)
                //{
                mTitleBarHeightSource = value;
                mTitleBarHeight = value.ToDPI(this.Dpi);

                // 当显示标题栏且高度大于0时对移动窗体的区域进行设置
                if (this.skShowTitle && this.mTitleBarHeight > 0)
                    this.skMoveHeight = this.mTitleBarHeight;

                this.Invalidate(this.Region);
                //}
            }
        }


        protected int mStatusBarHeight = 26;
        protected int mStatusBarHeightSource = 26;

        public int skStatusBarHeight
        {
            get { return mStatusBarHeight; }
            set
            {
                //if (mStatusBarHeight != value)
                //{
                mStatusBarHeightSource = value;
                mStatusBarHeight = value.ToDPI(this.Dpi);
                this.Invalidate(this.Region);
                //}
            }
        }

        public virtual Image skBackgroundImage { get; set; }

        [Browsable(false)]
        public virtual skSplit skBackgroundImageSplit { get; set; } = skSplit.Empty;

        public virtual string skBackgroundImageSplitString
        {
            get { return this.skBackgroundImageSplit.ToString(); }
            set
            {
                this.skBackgroundImageSplit = new skSplit(value);
            }
        }

        #region StatusBar

        protected bool mShowStatusBar = false;
        public bool skShowStatusBar
        {
            get { return this.mShowStatusBar; }
            set { this.mShowStatusBar = value; }
        }

        protected string mStatusBarLableText = string.Empty;
        public string skStatusBarLableText
        {
            get { return this.mStatusBarLableText; }
            set { this.mStatusBarLableText = value; }
        }

        protected Color mStatusBarLableTextColor = Color.Black;
        public Color skStatusBarLableTextColor
        {
            get { return this.mStatusBarLableTextColor; }
            set
            {
                this.mStatusBarLableTextColor = value;
                this.Invalidate();
            }
        }

        protected Font mStatusBarLableTextFont = MyFont.DefaultFontButton;
        public Font skStatusBarLableTextFont
        {
            get { return this.mStatusBarLableTextFont; }
            set
            {
                this.mStatusBarLableTextFont = value;
                this.Invalidate();
            }
        }

        protected ContentAlignment mStatusBarLableTextAlign = ContentAlignment.MiddleCenter;
        public ContentAlignment skStatusBarLableTextAlign
        {
            get { return this.mStatusBarLableTextAlign; }
            set
            {
                this.mStatusBarLableTextAlign = value;
                this.Invalidate();
            }
        }

        protected Padding mStatusBarLableTextPadding = Padding.Empty;
        public Padding skStatusBarLableTextPadding
        {
            get { return this.mStatusBarLableTextPadding; }
            set
            {
                this.mStatusBarLableTextPadding = value;
                this.Invalidate();
            }
        }

        #endregion

        #region Background

        protected Color mBackgroundColor = Color.White;
        [Description("控件的背景颜色")]
        public Color skBackgroundColor
        {
            get { return this.mBackgroundColor; }
            set
            {
                this.mBackgroundColor = value;
                this.Invalidate();
            }
        }

        protected Color mBorderStrokeColor = Color.Empty;
        public Color skBorderStrokeColor
        {
            get { return this.mBorderStrokeColor; }
            set
            {
                this.mBorderStrokeColor = value;
                this.Invalidate();
            }
        }

        protected Padding mBorderPadding = Padding.Empty;
        public Padding skBorderPadding
        {
            get { return this.mBorderPadding; }
            set
            {
                this.mBorderPadding = value;

                if (this.Padding == Padding.Empty)
                    this.Padding = value;

                this.Invalidate();
            }
        }

        protected skBorderType mBorderType = skBorderType.None;
        public skBorderType skBorderType
        {
            get { return this.mBorderType; }
            set
            {
                if (value != this.mBorderType)
                {
                    this.mBorderType = value;

                    int marginLeft = ((this.mBorderType & skBorderType.Left) == skBorderType.Left ? 1 : 0);
                    int marginRight = ((this.mBorderType & skBorderType.Right) == skBorderType.Right ? 1 : 0);
                    int marginTop = ((this.mBorderType & skBorderType.Top) == skBorderType.Top ? 1 : 0);
                    int marginBottom = ((this.mBorderType & skBorderType.Bottom) == skBorderType.Bottom ? 1 : 0);

                    this.Margin = new Padding(marginLeft, marginTop, marginRight, marginBottom);

                    this.Invalidate();
                }
            }
        }

        protected skBorderRadius mBorderRadius = skBorderRadius.Empty;

        [AmbientValue(typeof(skBorderRadius), "0,0,0,0")]

        public skBorderRadius skBorderRadius
        {
            get { return this.mBorderRadius; }
            set
            {
                this.mBorderRadius = value;
                this.Invalidate();
            }
        }

        protected int mBorderWidth = 1;
        /// <summary>
        /// 边框线的宽度
        /// </summary>
        /// <value>The width of the sk border.</value>
        public int skBorderWidth
        {
            get { return this.mBorderWidth; }
            set
            {
                this.mBorderWidth = value;
                this.Invalidate();
            }
        }

        protected Color mStatusBarBackgroundColor = Color.Gray;
        public Color skStatusBarBackgroundColor
        {
            get { return this.mStatusBarBackgroundColor; }
            set
            {
                this.mStatusBarBackgroundColor = value;
                this.Invalidate();
            }
        }

        #endregion

        #region TitleBar

        protected Color mTitleBackgroundColor = Color.Blue;
        public Color skTitleBackgroundColor
        {
            get { return this.mTitleBackgroundColor; }
            set
            {
                this.mTitleBackgroundColor = value;
                this.Invalidate();
            }
        }

        protected Font mTitleFont = MyFont.DefaultFont;
        public Font skTitleFont
        {
            get { return this.mTitleFont; }
            set
            {
                this.mTitleFont = value.ReplaceFont();
                this.Invalidate();
            }
        }

        protected Color mTitleColor = Color.White;
        public Color skTitleColor
        {
            get { return this.mTitleColor; }
            set
            {
                this.mTitleColor = value;
                this.Invalidate();
            }
        }

        protected bool mShowTitle = true;
        public bool skShowTitle
        {
            get { return this.mShowTitle; }
            set
            {
                this.mShowTitle = value;
                this.Invalidate();
            }
        }

        protected string mTitle = string.Empty;
        public string skTitle
        {
            get { return this.mTitle; }
            set
            {
                this.mTitle = value;
                this.Text = value;
                this.Invalidate();
            }
        }

        protected Image mIcon = null;
        public Image skIcon
        {
            get { return this.mIcon; }
            set
            {
                this.mIcon = value;
                this.Invalidate();
            }
        }

        protected Size mIconSize = Size.Empty;
        public Size skIconSize
        {
            get { return this.mIconSize; }
            set
            {
                this.mIconSize = value;
                this.Invalidate();
            }
        }

        protected int mIconPlaceTitle = 3;
        public int skIconPlaceTitle
        {
            get { return this.mIconPlaceTitle; }
            set
            {
                this.mIconPlaceTitle = value;
                this.Invalidate();
            }
        }

        protected Padding mIconPadding = new Padding(10, 0, 10, 0);
        public Padding skIconPadding
        {
            get { return this.mIconPadding; }
            set
            {
                this.mIconPadding = value;
                this.Invalidate();
            }
        }

        #endregion

        #region 是否初始化背景色
        public virtual bool skInitBackColor
        {
            get { return true; }
        }

        #endregion

        #region 关闭、最大化、最小化按钮

        protected Padding mButtonPadding = new Padding(10, 0, 10, 0);
        public Padding skButtonPadding
        {
            get { return this.mButtonPadding; }
            set
            {
                this.mButtonPadding = value;
                this.InitButtonLocation();
            }
        }

        protected int mButtonDiff = 3;
        public int skButtonDiff
        {
            get { return this.mButtonDiff; }
            set
            {
                this.mButtonDiff = value;
                this.InitButtonLocation();
            }
        }

        protected Size mButtonSize = new Size(28, 28);
        public virtual Size skButtonSize
        {
            get { return this.mButtonSize; }
            set
            {
                this.mButtonSize = value;
                this.InitButtonLocation();
            }
        }

        protected bool mShowButtonClose = true;
        public virtual bool skShowButtonClose
        {
            get { return this.mShowButtonClose; }
            set
            {
                this.mShowButtonClose = value;
                this.InitButtonLocation();
                this.Invalidate();
            }
        }

        protected bool mShowButtonMin = true;
        public virtual bool skShowButtonMin
        {
            get { return this.mShowButtonMin; }
            set
            {
                this.mShowButtonMin = value;
                this.InitButtonLocation();
                this.Invalidate();
            }
        }

        protected bool mShowButtonMax = true;
        public virtual bool skShowButtonMax
        {
            get { return this.mShowButtonMax; }
            set
            {
                this.mShowButtonMax = value;
                this.InitButtonLocation();
                this.Invalidate();
            }
        }

        protected skButton btnClose;
        public virtual skButton skButtonClose
        {
            get { return this.btnClose; }
            set { this.btnClose = value; }
        }

        protected skButton btnMax;
        public virtual skButton skButtonMax
        {
            get { return this.btnMax; }
            set { this.btnMax = value; }
        }

        protected skButton btnMin;
        public virtual skButton skButtonMin
        {
            get { return this.btnMin; }
            set { this.btnMin = value; }
        }

        protected skButton CreateButton(string btnName, string iconName, skImageState iconState)
        {
            skButton btn = new skButton();
            this.Controls.Add(btn);

            btn.Anchor = AnchorStyles.Top | AnchorStyles.Left;
            btn.Name = btnName;
            btn.Size = this.mButtonSize;
            btn.skIcon = this.GetImage(iconName);
            btn.skIconState = iconState;
            btn.skIconAlign = skImageAlignment.Center;
            btn.skIconSize = new Size(24, 24);
            btn.skBackgroundColor = Color.Transparent;
            btn.Click += this.OnButtonClick;

            return btn;
        }

        protected virtual void OnButtonClick(object sender, EventArgs e)
        {
            skButton btn = sender as skButton;
            if (btn == null)
                return;

            switch (btn.Name)
            {
                case "btnClose":
                    this.Close();
                    break;

                case "btnMax":
                    if (this.WindowState == FormWindowState.Normal)
                    {
                        this.WindowState = FormWindowState.Maximized;
                    }
                    else
                    {
                        this.WindowState = FormWindowState.Normal;
                    }
                    break;

                case "btnMin":
                    this.WindowState = FormWindowState.Minimized;
                    break;
            }
        }

        /// <summary>
        /// 最大、最小、关闭按钮的定位
        /// </summary>
        protected virtual void InitButtonLocation()
        {
            if (!this.DesignMode && !this.mHasLoadFormButton)
                return;

            int right = this.Width - this.mButtonPadding.Right;
            int top = this.mButtonPadding.Top;
            int bottom = this.skTitleBarHeight - this.mButtonPadding.Bottom;

            this.btnClose.Visible = this.mShowButtonClose;
            if (this.mShowButtonClose)
            {
                this.btnClose.Location = new Point(right - this.btnClose.Width, top + (bottom - top - this.btnClose.Height) / 2);
                right = this.btnClose.Left - this.mButtonDiff;
            }

            this.btnMax.Visible = this.mShowButtonMax;
            if (this.mShowButtonMax)
            {
                this.btnMax.Location = new Point(right - this.btnMax.Width, top + (bottom - top - this.btnMax.Height) / 2);
                right = this.btnMax.Left - this.mButtonDiff;
            }

            this.btnMin.Visible = this.mShowButtonMin;
            if (this.mShowButtonMin)
            {
                this.btnMin.Location = new Point(right - this.btnMin.Width, top + (bottom - top - this.btnMin.Height) / 2);
                right = this.btnMin.Left - this.mButtonDiff;
            }
        }

        #endregion

        #region 背景阴影

        /// <summary>
        /// 窗体背景图片，可带阴影效果
        /// </summary>
        [Description("窗体背景图片，可带阴影效果")]
        public virtual Image skTransparentImage { get; set; }

        private Padding mTransparentImagePadding;
        /// <summary>
        /// 窗体上下左右的外沿阴影的边距
        /// </summary>
        [Description("窗体上下左右的外沿阴影的边距")]
        public Padding skTransparentImagePadding
        {
            get { return mTransparentImagePadding; }
            set
            {
                mTransparentImagePadding = value;
            }
        }

        private skSplit mTransparentImageSplit = iTong.CoreModule.skSplit.Empty;
        /// <summary>
        /// 透明窗口背景图九宫格切割
        /// </summary>
        [Description("背景图片的九宫格切割")]
        [Browsable(false)]
        public virtual skSplit skTransparentImageSplit
        {
            get { return this.mTransparentImageSplit; }
            set { this.mTransparentImageSplit = value; }
        }

        public virtual string skTransparentImageSplitString
        {
            get { return this.mTransparentImageSplit.GetString; }
            set { this.mTransparentImageSplit = new skSplit(value); }
        }

        private skTransparentForm skTransparentForm;

        public void SetTransparentImage(Image img, skSplit split, Padding padding)
        {
            this.skTransparentImage = img;
            this.skTransparentImagePadding = padding;
            this.skTransparentImageSplit = split;
        }

        public void SetCustomGraphics(GraphicsPath graphicsPath, SolidBrush color = null)
        {
            if (this.skTransparentForm != null)
            {
                this.skTransparentForm.SetPngBackground(graphicsPath, color);
            }
        }

        public void SetRadius()
        {
            if (this.skTransparentForm != null)
            {
                this.skTransparentForm.SetPngBackground(this.skBorderRadius, new Rectangle(this.skTransparentImagePadding.Left, this.skTransparentImagePadding.Top, this.ClientRectangle.Width, this.ClientRectangle.Height));
            }
        }

        #endregion

        protected Icon IconClone;
        protected Dictionary<string, Image> mDictImageClone = new Dictionary<string, Image>();

        public virtual Image GetImage(string imgName, Image imgSource = null)
        {
            Image img = null;

            if (!string.IsNullOrEmpty(imgName) && this.mDictImageClone.ContainsKey(imgName))
            {
                img = this.mDictImageClone[imgName];
                goto DoExit;
            }

            if (imgSource != null)
                img = imgSource;
            else
                img = MyResource.GetImage(imgName);

            if (img != null && System.Threading.Thread.CurrentThread.IsBackground)
            {
                img = (Image)img.Clone();

                if (!string.IsNullOrEmpty(imgName))
                    this.mDictImageClone[imgName] = img;
            }

        DoExit:
            return img;
        }

        public bool UpdateDpi()
        {
            bool dpiChanged = false;
#if DPI

            int currentDPI = MyDpi.GetDPI(this.Handle);
            if (currentDPI != this.Dpi)
            {
                this.SetDpi(currentDPI);
                dpiChanged = true;
            }
#endif
            return dpiChanged;
        }

        /// <summary>
        /// DPI变更
        /// </summary>
        /// <param name="needChange"></param>
        public void DpiChangeCallBack(bool needChange = false)
        {
#if DPI
            //窗体DPI没有变化，并且不需要变更窗口，直接返回
            if (!this.UpdateDpi() && !needChange)
                return;

            float dpiScale = (float)this.Dpi / (float)this.OldDpi;
            this.Scale(new SizeF(dpiScale, dpiScale));
            this.OnDpiChanged();
#endif
        }

        /// <summary>
        /// 界面有变化的时候需要用这个来调用
        /// 先还原放大的比例，执行完变更后。再重新放大当前比例
        /// </summary>
        /// <param name="action"></param>
        public void ControlSizeChangeHandle(Action action)
        {
#if DPI
            float dpiScale = (float)MyDpi.DefaultDPI / (float)this.Dpi;
            this.Scale(new SizeF(dpiScale, dpiScale));
            this.SetDpi(MyDpi.DefaultDPI);
            this.OnDpiChanged();
#endif
            action.Invoke();
#if DPI
            this.DpiChangeCallBack();
#endif
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            if (!this.DesignMode)//&& OSHelper.OSVersion.Build > (int)OSBuild.Win10_1607 
            {
#if DPI
                //初始化原始大小
                this.OriginalSize = this.Size;
                this.SetContorlAnchorForDpi(this);
                this.DpiChangeCallBack(true);
#endif
            }

            this.HasShown = true;

            this.ShowShadow();
            this.RefreshRegion();

            MyForm.OpenForms.Add(this);
        }

        private void SetContorlAnchorForDpi(Control control)
        {
#if DPI
            foreach (Control ctl in control.Controls)
            {
                ctl.Anchor = AnchorStyles.Left | AnchorStyles.Top;
                SetContorlAnchorForDpi(ctl);
            }
#endif
        }

        protected override void OnClosed(EventArgs e)
        {
            base.OnClosed(e);

            if (this.IconClone != null)
            {
                this.IconClone.Dispose();
                this.IconClone = null;
            }

            foreach (Image img in this.mDictImageClone.Values)
                img.Dispose();

            this.mDictImageClone.Clear();

            MyForm.OpenForms.Remove(this);

        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            this.LoadForm();
        }

        public void LoadForm()
        {
            try
            {
                if (!this.mHasLoadForm)
                {
                    //初始化过，就不能再次被调用
                    this.mHasLoadForm = true;

                    //先替换皮肤，再进行InitControl，现在很多初始化按钮的InitControl中
                    this.SetSkin();

                    this.InitControls();
                    this.SetInterface();


                    if (this.CanReplaceFont && !string.IsNullOrEmpty(MyFont.ReplaceFontFamily))
                        skForm.ReplaceFont(this);

                    this.mHasLoadFormButton = true;
                    this.InitButtonLocation();
                }
            }
            catch (Exception ex)
            {
                //if (Folder.AppFolder.Contains("bin/Debug/"))
                //	skMessageBox.Show(ex.ToString());

                Common.LogException(ex.ToString(), "skFormBase.LoadForm");
            }
            finally
            {
                this.AutoScaleMode = AutoScaleMode.None;
            }
        }

        protected virtual void InitControls()
        {

        }

        protected virtual void SetInterface()
        {

        }

        public virtual void LoadData(params object[] args)
        {

        }

        public virtual void SetSkin()
        {

        }

        protected virtual void SetTransparentFormLocation()
        {
            if (this.skTransparentForm == null)
                return;

            Padding imagePadding = this.skTransparentImagePadding;
            this.skTransparentForm.Location = new Point(this.Location.X - imagePadding.Left, this.Location.Y - imagePadding.Top);
        }

        protected virtual void SetTransparentFormSize()
        {
            if (this.skTransparentForm == null)
                return;
            Padding imagePadding = this.skTransparentImagePadding;
            this.skTransparentForm.Size = new Size(this.Size.Width + imagePadding.Horizontal, this.Size.Height + imagePadding.Vertical);
            this.skTransparentForm.SetPngBackground();
        }

        public virtual void ShowShadow()
        {
            if (this.DesignMode || this.Parent != null || this.skTransparentImage == null)
                return;

            if (this.skTransparentForm == null)
                this.skTransparentForm = new skTransparentForm();

            if (this.skTransparentForm.InvokeRequired)
            {
                this.skTransparentForm.Invoke(new System.Threading.ThreadStart(this.ShowShadow));
            }
            else
            {
                this.skTransparentForm.TransparentImage = this.skTransparentImage;
                this.skTransparentForm.TransparentImageSplit = this.skTransparentImageSplit;
                this.skTransparentForm.Owner = this;
                this.skTransparentForm.TopMost = this.TopMost;
                this.skTransparentForm.StartPosition = FormStartPosition.Manual;
                this.SetTransparentFormSize();
                this.SetTransparentFormLocation();
                this.skTransparentForm.Show();
            }
        }

        public virtual void HideShadow()
        {
            if (this.skTransparentForm == null)
                return;

            if (this.skTransparentForm.InvokeRequired)
            {
                this.skTransparentForm.Invoke(new System.Threading.ThreadStart(this.HideShadow));
            }
            else
            {
                this.skTransparentForm.Owner = null;
                this.skTransparentForm.Hide();
                this.skTransparentForm.Close();
                this.skTransparentForm = null;
            }
        }

        public virtual void RefreshRegion()
        {
            if (!this.HasShown)
                return;

            this.Region = null;

            if (this.skBorderType != skBorderType.None)
            {
                GraphicsPath graphicsForm = skGuiHelper.CreateRoundPath(this.ClientRectangle, this.skBorderRadius, penWidth: 0);

                this.Region = new Region(graphicsForm);
            }
        }

        private Screen mScreen;
        protected override void OnLocationChanged(EventArgs e)
        {
            base.OnLocationChanged(e);
            this.SetTransparentFormLocation();

            Point point = new Point(this.Location.X + this.Width / 2, this.Location.Y + this.Height / 2);
            Screen currentScreen = Screen.FromPoint(point);

            Point top = this.Location;
            Point bottom = new Point(this.Location.X + this.Width, this.Location.Y + this.Height);
            Screen topScreen = Screen.FromPoint(top);
            Screen bottomScreen = Screen.FromPoint(bottom);

            //判断屏幕所在位置变化
            //判断整个窗口都在一个屏幕内
            if (mScreen != null && currentScreen.DeviceName != mScreen.DeviceName && topScreen.DeviceName == bottomScreen.DeviceName)
            {
                mScreen = currentScreen;
                MyDpi.CheckDPI(this);
            }

            if (mScreen == null)
                mScreen = currentScreen;

        }

        protected virtual void OnDpiChanged()
        {
#if DPI
            this.skTitleBarHeight = mTitleBarHeightSource;
            this.skStatusBarHeight = mStatusBarHeightSource;

            this.InitButtonLocation();
            this.ControlHandle(this);
            this.ShowShadow();
            this.SetRadius();
            this.Invalidate(true);
#endif
        }
        public void ControlHandle(Control ctl)
        {
            try
            {
                if (ctl is skTextBox)
                {
                    skTextBox txt = ctl as skTextBox;
                    txt.Font = txt.Font;
                    return;
                }

                foreach (Control ctlSub in ctl.Controls)
                {
                    ControlHandle(ctlSub);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "skForm.ReplaceFont");
            }
        }

        protected override void OnSizeChanged(EventArgs e)
        {
            base.OnSizeChanged(e);

            this.RefreshRegion();
            this.SetTransparentFormSize();
            this.InitButtonLocation();
        }

        protected override void OnVisibleChanged(EventArgs e)
        {
            base.OnVisibleChanged(e);

            if (this.skTransparentForm != null && this.Visible != this.skTransparentForm.Visible)
            {
                this.skTransparentForm.Visible = this.Visible;
                if (this.Visible)
                {
                    this.SetTransparentFormSize();
                    this.InitButtonLocation();
                }
            }
            //else if(this.skTransparentForm != null)
            //{
            //    Common.LogException("skTransparentForm IsDisposed", "skForm.OnVisibleChanged");
            //}
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            bool pblnCancelClose = false;
            this.BeforeFormClose(ref pblnCancelClose);

            e.Cancel = pblnCancelClose;

            base.OnClosing(e);
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                if (this.skTransparentForm != null)
                {
                    this.skTransparentForm.Hide();
                    this.skTransparentForm.Close();
                    this.skTransparentForm = null;
                }
            }
            catch (Exception)
            {
            }

            base.OnFormClosed(e);
        }

        protected virtual void BeforeFormClose(ref bool pblnCancelClose)
        {

        }

        protected override void OnPaint(PaintEventArgs e)
        {
            //base.OnPaint(e);
            Graphics g = e.Graphics;
            g.InterpolationMode = InterpolationMode.HighQualityBicubic;
            g.SmoothingMode = SmoothingMode.HighQuality;

            ////绘制窗体背景				
            Color colorFill = this.mBackgroundColor;
            Color colorStroke = (this.mBorderStrokeColor == Color.Empty ? colorFill : this.mBorderStrokeColor);
            //colorStroke = Color.Red;
            skGuiHelper.FillRoundRect(g, this.ClientRectangle, this.skBorderType, this.mBorderRadius, this.skBorderWidth, colorStroke, colorFill);

            if (this.skBackgroundImage != null)
            {
                //绘制窗体背景图片
                skGuiHelper.DrawImage(g, this.ClientRectangle, this.skBackgroundImageSplit, this.skBackgroundImage);
            }
            else
            {
                //绘制窗体标题栏背景
                if (mTitleBarHeight > 0)
                {
                    Rectangle rectTitleBar = new Rectangle(this.Margin.Left, this.Margin.Top, this.Width - this.Margin.Horizontal, mTitleBarHeight);
                    skGuiHelper.FillRoundRect(g, rectTitleBar, skBorderType.Round, new skBorderRadius(this.mBorderRadius.TopLeft, this.mBorderRadius.TopRight, 0, 0), 1, this.mTitleBackgroundColor, this.mTitleBackgroundColor);
                }
            }

            //绘制窗体Icon和Title
            if (this.mShowTitle)
            {
                int iconPlaceTitle = mIconPlaceTitle.ToDPI(this.Dpi);
                Size iconSizeForDpi = mIconSize.ToDPI(this.Dpi);
                Padding iconPaddingForDpi = mIconPadding.ToDPI(this.Dpi);
                Padding borderPaddingForDpi = mBorderPadding.ToDPI(this.Dpi);



                int left = borderPaddingForDpi.Left + iconPaddingForDpi.Left;
                int top = borderPaddingForDpi.Top + iconPaddingForDpi.Top;
                int bottom = borderPaddingForDpi.Top + mTitleBarHeight - iconPaddingForDpi.Bottom;

                //绘制窗体图标
                if (!iconSizeForDpi.IsEmpty)
                {
                    Rectangle rectIcon = new Rectangle(left, top + (bottom - top - iconSizeForDpi.Height) / 2, iconSizeForDpi.Width, iconSizeForDpi.Height);
                    skGuiHelper.DrawImage(g, this.mIcon, rectIcon);

                    left = rectIcon.Right + iconPlaceTitle;
                }

                //绘制窗体标题
                if (!string.IsNullOrEmpty(this.mTitle))
                {
                    Size sizeText = skGuiHelper.MeasureString(g, this.mTitle, this.mTitleFont, control: this);
                    Rectangle rectText = new Rectangle(left, top + (bottom - top - sizeText.Height) / 2, sizeText.Width, sizeText.Height);

                    skGuiHelper.DrawString(g, this.mTitle, this.mTitleColor, this.mTitleFont, rectText, ContentAlignment.MiddleLeft, control: this);
                }
            }

            //绘制状态栏
            if (this.mShowStatusBar)
            {
                int topStatus = this.Height - this.mStatusBarHeight + this.mStatusBarLableTextPadding.Top;

                Rectangle rectStatus = new Rectangle(this.Margin.Left, this.Height - this.Margin.Bottom - this.mStatusBarHeight, this.Width - this.Margin.Horizontal, this.mStatusBarHeight);
                skGuiHelper.FillRoundRect(g, rectStatus, skBorderType.Round, skBorderRadius.Empty, 1, this.mStatusBarBackgroundColor, this.mStatusBarBackgroundColor);

                if (!string.IsNullOrEmpty(this.mStatusBarLableText))
                {
                    Rectangle rectStatusText = new Rectangle(this.mStatusBarLableTextPadding.Left, topStatus, this.Width - this.mStatusBarLableTextPadding.Horizontal, this.mStatusBarHeight - this.mStatusBarLableTextPadding.Vertical);
                    skGuiHelper.DrawString(g, this.mStatusBarLableText, this.mStatusBarLableTextColor, this.mStatusBarLableTextFont, rectStatusText, this.mStatusBarLableTextAlign, control: this);
                }
            }
        }

    }
}
