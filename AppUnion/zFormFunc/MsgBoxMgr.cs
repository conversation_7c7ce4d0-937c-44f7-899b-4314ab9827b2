﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;

namespace iTong.CoreModule
{
    /// <summary>
    /// 弹窗管理类，用于弹出 skMsgInfoNew 对话框
    /// </summary>
    public class MsgBoxMgr
    {
        /// <summary>消息窗体句柄列表</summary>
        public static List<frmMessage> mListMessageForm = new List<frmMessage>();

        /// <summary>
        /// 获取消息弹窗句柄
        /// </summary>
        /// <param name="msg">消息弹窗</param>
        /// <returns>返回的是消息弹窗的frmMessageForm对象(优先返回查找的第一个对象)</returns>
        public static frmMessage Get(skMsgInfoNew msg)
        {
            if (msg == null)
                return null;

            frmMessage frm = mListMessageForm.FirstOrDefault(f => f.mInfo != null && f.mInfo.MsgGuid == msg.MsgGuid);
            return frm;
        }

        /// <summary>
        /// 添加消息弹窗
        /// </summary>
        /// <param name="form">需要消息弹窗的窗体</param>
        /// <param name="msg">消息弹窗</param>
        public static void Add(frmMessage form, skMsgInfoNew msg)
        {
            frmMessage frm = Get(msg);

            //已经存在直接返回
            if (frm != null)
                return;

            mListMessageForm.Add(form);
        }

        /// <summary>
        /// 移除消息弹窗
        /// </summary>
        /// <param name="msg">消息弹窗</param>
        public static void Remove(skMsgInfoNew msg)
        {
            frmMessage frm = Get(msg);
            if (frm == null)
                return;

            mListMessageForm.Remove(frm);

            msg.FormClose?.Invoke();

            CloseHandle(frm);
        }

        /// <summary>
        /// 移除所有消息弹窗
        /// </summary>
        public static void RemoveAll()
        {
            try
            {
                foreach (frmMessage frm in mListMessageForm)
                {
                    CloseHandle(frm);
                }

                mListMessageForm.Clear();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MsgBoxMgr.RemoveAll");
            }
        }

        /// <summary>
        /// 关闭弹窗句柄
        /// </summary>
        /// <param name="frm">需要被关闭的窗体</param>
        private static void CloseHandle(frmMessage frm)
        {
            try
            {
                if (frm == null)
                    return;

                if (frm.InvokeRequired)
                {
                    frm.Invoke(new ThreadStart(() =>
                    {
                        frm.Close();
                    }));
                }
                else
                {
                    frm.Close();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MsgBoxMgr.CloseHandle");
            }
        }

        /// <summary>
        /// 更新弹窗显示文本
        /// </summary>
        /// <param name="msg">消息弹窗</param>
        public static void UpdateText(skMsgInfoNew msg)
        {
            try
            {
                frmMessage frm = Get(msg);
                if (frm == null)
                    return;

#if MAC
                frm.UpdataText(msg);
#else
                if (frm.InvokeRequired)
                {
                    frm.Invoke(new ThreadStart(() =>
                    {
                        frm.ControlSizeChangeHandle(new Action(() => { frm.UpdataText(msg); }));
                    }));
                }
                else
                {
                    frm.ControlSizeChangeHandle(new Action(() => { frm.UpdataText(msg); }));
                }
#endif

            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MsgBoxMgr.UpdateText");
            }
        }

        /// <summary>
        /// 停止弹窗按钮加载动画
        /// </summary>
        /// <param name="msg">消息弹窗</param>
        public static void StopBtnLoading(skMsgInfoNew msg)
        {
            try
            {
                frmMessage frm = Get(msg);
                if (frm == null)
                    return;

                if (frm.InvokeRequired)
                {
                    frm.Invoke(new ThreadStart(() =>
                    {
                        frm.StopLoading();
                    }));
                }
                else
                {
                    frm.StopLoading();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MsgBoxMgr.StopBtnLoading");
            }
        }

        /// <summary>
        /// 弹出对话框
        /// </summary>
        /// <param name="owner">需要弹窗的窗体</param>
        /// <param name="skMsgInfo">消息弹窗对象</param>
        /// <returns>对话框结果枚举</returns>
        public static DialogResult Show(object owner, skMsgInfoNew skMsgInfo)
        {
            return frmMessage.ShowCore(owner, null, null, null, skMsgInfo);
        }

        /// <summary>
        /// 弹出对话框
        /// </summary>
        /// <param name="owner">需要弹窗的窗体</param>
        /// <param name="msgText">显示的文本</param>
        /// <param name="caption">标题</param>
        /// <returns>对话框结果枚举</returns>
        public static DialogResult Show(object owner, string msgText, string caption)
        {
            return frmMessage.ShowCore(owner, msgText, caption, null, new skMsgInfoNew());
        }

        /// <summary>
        /// 弹出对话框
        /// </summary>
        /// <param name="owner">需要弹窗的窗体</param>
        /// <param name="msgText">显示的文本</param>
        /// <param name="caption">标题</param>
        /// <param name="buttonStyle">按钮样式</param>
        /// <returns>对话框结果枚举</returns>
        public static DialogResult Show(object owner, string msgText, string caption, MessageBoxButtons buttonStyle)
        {
            return frmMessage.ShowCore(owner, msgText, caption, buttonStyle, new skMsgInfoNew());
        }

        /// <summary>
        /// 弹出对话框
        /// </summary>
        /// <param name="owner">需要弹窗的窗体</param>
        /// <param name="msgText">显示的文本</param>
        /// <param name="caption">标题</param>
        /// <param name="skMsgInfo">消息弹窗对象</param>
        /// <returns>对话框结果枚举</returns>
        public static DialogResult Show(object owner, string msgText, string caption, skMsgInfoNew skMsgInfo)
        {
            return frmMessage.ShowCore(owner, msgText, caption, null, skMsgInfo);
        }
    }

}
