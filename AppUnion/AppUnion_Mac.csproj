<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{0E0C80D7-8C78-4F0F-B8FC-320F5C97F3F6}</ProjectGuid>
    <ProjectTypeGuids>{A3F8F2AB-B479-4A4A-A458-A89E7DC349F1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <RootNamespace>iTong.AppUnion</RootNamespace>
    <AssemblyName>AppUnion</AssemblyName>
    <MonoMacResourcePrefix>Resources</MonoMacResourcePrefix>
    <ReleaseVersion>2.0.0</ReleaseVersion>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <UseXamMacFullFramework>true</UseXamMacFullFramework>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Debug</OutputPath>
    <DefineConstants>__UNIFIED__;__MACOS__;DEBUG;MAC;IS_ITONG;MAC40;LOG1;NET452;AB;FG;TEST;MAC1014X;MAC1015X;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <EnableCodeSigning>false</EnableCodeSigning>
    <CreatePackage>false</CreatePackage>
    <EnablePackageSigning>false</EnablePackageSigning>
    <IncludeMonoRuntime>false</IncludeMonoRuntime>
    <UseSGen>false</UseSGen>
    <HttpClientHandler>HttpClientHandler</HttpClientHandler>
    <TlsProvider>Default</TlsProvider>
    <LinkMode>None</LinkMode>
    <XamMacArch></XamMacArch>
    <AOTMode>None</AOTMode>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <PackageSigningKey></PackageSigningKey>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <Optimize>true</Optimize>
    <OutputPath>..\bin\Release</OutputPath>
    <DefineConstants>__UNIFIED__;__MACOS__;DEBUG;MAC;IS_ITONG;MAC40;LOG1;NET452;AB;FG;TEST;MAC1014X;MAC1015X;</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <ConsolePause>false</ConsolePause>
    <EnableCodeSigning>false</EnableCodeSigning>
    <CreatePackage>false</CreatePackage>
    <EnablePackageSigning>false</EnablePackageSigning>
    <IncludeMonoRuntime>false</IncludeMonoRuntime>
    <UseSGen>false</UseSGen>
    <HttpClientHandler>HttpClientHandler</HttpClientHandler>
    <TlsProvider>Default</TlsProvider>
    <XamMacArch></XamMacArch>
    <XamMacArch></XamMacArch>
    <AOTMode>None</AOTMode>
    <PlatformTarget>anycpu</PlatformTarget>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <CodeSigningKey></CodeSigningKey>
    <CodeSigningKey></CodeSigningKey>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Drawing" />
    <Reference Include="Mono.Data.Sqlite" />
    <Reference Include="System.Data" />
    <Reference Include="Xamarin.Mac" />
    <Reference Include="System.Security" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="zServer\API\Info\InfoForBiz.cs" />
    <Compile Include="zServer\API\Info\InfoForUser.cs" />
    <Compile Include="zServer\API\Url\MyUrlForPersonal.cs" />
    <Compile Include="zServer\TD\tdActionHelper.cs" />
    <Compile Include="zServer\TD\tdActionModeKey.cs" />
    <Compile Include="zServer\TD\tdActionItem.cs" />
    <Compile Include="zService\ParaArgs.cs" />
    <Compile Include="zWebRTC\MyProcesss.cs" />
    <Compile Include="zWebRTC\TimerMgr.cs" />
    <Compile Include="zWinSDK\AppCapability.cs" />
    <Compile Include="zClass\ActionEnum.cs" />
    <Compile Include="zClass\ActionHelper.cs" />
    <Compile Include="zClass\ActionItem.cs" />
    <Compile Include="zClass\AirPlayDevice.cs" />
    <Compile Include="zClass\BluetoothDevice.cs" />
    <Compile Include="zClass\BluetoothDevice_Array.cs" />
    <Compile Include="zClass\BluetoothDevice_iPad_1400.cs" />
    <Compile Include="zClass\BluetoothDevice_iPhone_1400.cs" />
    <Compile Include="zClass\ChargeHelperForCast.cs" />
    <Compile Include="zClass\DllImport.cs" />
    <Compile Include="zClass\DllImportSystem.cs" />
    <Compile Include="zClass\INetFwMgr.cs" />
    <Compile Include="zClass\IniHelper.cs" />
    <Compile Include="zClass\MyAirplay.cs" />
    <Compile Include="zClass\MyAirplayUSB.cs" />
    <Compile Include="zClass\MyAirPlayUSBAndroid.cs" />
    <Compile Include="zClass\MyAirPlayUsbAndroidEnum.cs" />
    <Compile Include="zClass\MyAirPlayWiFi.cs" />
    <Compile Include="zClass\MyAudio.cs" />
    <Compile Include="zClass\MyAuthorize.cs" />
    <Compile Include="zClass\MyBluetooth.cs" />
    <Compile Include="zClass\MyClass.cs" />
    <Compile Include="zClass\MyCode.cs" />
    <Compile Include="zClass\SettingMgr\KeyName_RS.cs" />
    <Compile Include="zClass\SettingMgr\SettingMgr.cs" />
    <Compile Include="zClass\MyScript.cs" />
    <Compile Include="zClass\MySystem.cs" />
    <Compile Include="zClass\MyTask.cs" />
    <Compile Include="zClass\MyUpdate.cs" />
    <Compile Include="zServer\API\Url\MyUrlForBiz.cs" />
    <Compile Include="zServer\RS\RsClientStatic.cs" />
    <Compile Include="zServer\API\Url\MyUrlForKid.cs" />
    <Compile Include="zServer\API\Url\MyUrlForRs.cs" />
    <Compile Include="zServer\API\Url\MyUrl.cs" />
    <Compile Include="zFile\FileUploadEnum.cs" />
    <Compile Include="zFile\FileUploadTask.cs" />
    <Compile Include="zFile\FileUploadDB.cs" />
    <Compile Include="zFile\FileUploadMgr.cs" />
    <Compile Include="zFile\FileAPI_QiNiu.cs" />
    <Compile Include="zFile\FileUploadTaskEx.cs" />
    <Compile Include="zPC\ActionClass.cs" />
    <Compile Include="zPC\InputClass.cs" />
    <Compile Include="zPC\InputCode.cs" />
    <Compile Include="zPC\InputEnum.cs" />
    <Compile Include="zPC\InputScanCode.cs" />
    <Compile Include="zPC\InputSender.cs" />
    <Compile Include="zPC\InputStruct.cs" />
    <Compile Include="zFile\FileAPI.cs" />
    <Compile Include="zFile\FileAPI_S3.cs" />
    <Compile Include="zServer\API\KidAPI.cs" />
    <Compile Include="zServer\RS\RsClient.cs" />
    <Compile Include="zServer\RS\RsClientInput.cs" />
    <Compile Include="zServer\RS\RsController.cs" />
    <Compile Include="zServer\RS\RsControllerRTC.cs" />
    <Compile Include="zServer\API\Info\InfoForKid.cs" />
    <Compile Include="zServer\TaskMgr.cs" />
    <Compile Include="zService\ParaMgr.cs" />
    <Compile Include="zSocketLocal\SocketArgs.cs" />
    <Compile Include="zSocketLocal\SocketHandlerRecv.cs" />
    <Compile Include="zSocketLocal\SocketMgr.cs" />
    <Compile Include="zServer\API\_UserBase.cs" />
    <Compile Include="zSocketLocal\SocketHandler.cs" />
    <Compile Include="zSocketLocal\SocketMgrForClient.cs" />
    <Compile Include="zSocketLocal\SocketMgrForService.cs" />
    <Compile Include="zSocket\Class\MsgKey.cs" />
    <Compile Include="zSocket\Class\MsgType.cs" />
    <Compile Include="zSocket\Class\PushEvent.cs" />
    <Compile Include="zCommon\_ClassExtend.cs" />
    <Compile Include="zSocket\Enum\skWallpaperResult.cs" />
    <Compile Include="zSocket\Enum\skWallpaperStatus.cs" />
    <Compile Include="zServer\API\Info\InfoForRS.cs" />
    <Compile Include="zFile\skFileUploadInfo.cs" />
    <Compile Include="zSocket\Info\skDevice_Info.cs" />
    <Compile Include="zSocket\RS\PushKey.cs" />
    <Compile Include="zSocket\RS\RSEvent.cs" />
    <Compile Include="zSocket\RS\RSKey.cs" />
    <Compile Include="zSocket\Class\skGestureArgs.cs" />
    <Compile Include="zSocket\Class\skGestureRtcArgs.cs" />
    <Compile Include="zSocket\Class\skSocketBodyForPush.cs" />
    <Compile Include="zSocket\Class\skSocketBodyForRS.cs" />
    <Compile Include="zSocket\Class\skSocketDataForPush.cs" />
    <Compile Include="zSocket\Class\skSocketDataForRS.cs" />
    <Compile Include="zSocket\Class\skSocketMsgBase.cs" />
    <Compile Include="zSocket\Class\skSocketMsgForPush.cs" />
    <Compile Include="zSocket\Class\skSocketMsgForRS.cs" />
    <Compile Include="zSocket\Class\skTcpListener.cs" />
    <Compile Include="zSocket\Class\skWebSocket.cs" />
    <Compile Include="zSocket\RS\skWebSocketForPush.cs" />
    <Compile Include="zSocket\RS\skWebSocketForPushRSHost.cs" />
    <Compile Include="zSocket\RS\skWebSocketForRS.cs" />
    <Compile Include="zSocket\RS\skWebSocketForRSHost.cs" />
    <Compile Include="zSocket\RS\skWebSocketForRS_Send.cs" />
    <Compile Include="zWave\FFMpegHelper.cs" />
    <Compile Include="zWave\WaveFileWriter.cs" />
    <Compile Include="zWave\WaveInterop.cs" />
    <Compile Include="zCommon\AES.cs" />
    <Compile Include="zCommon\RSA.cs" />
    <Compile Include="zServer\API\RsAPI.cs" />
    <Compile Include="zSocket\RS\RSEventType.cs" />
    <Compile Include="zSocket\RS\RSStep.cs" />
    <Compile Include="zSocket\Enum\skAddonActionType.cs" />
    <Compile Include="zSocket\Enum\skAddonFeatureResult.cs" />
    <Compile Include="zSocket\Enum\skAddonStatus.cs" />
    <Compile Include="zSocket\Enum\skBroadcastStatus.cs" />
    <Compile Include="zSocket\Enum\skConnectCode.cs" />
    <Compile Include="zSocket\Enum\skDeviceType.cs" />
    <Compile Include="zSocket\Enum\skEncryptType.cs" />
    <Compile Include="zSocket\Enum\skFeatureType.cs" />
    <Compile Include="zSocket\Enum\skGestureType.cs" />
    <Compile Include="zSocket\Enum\skImeInputType.cs" />
    <Compile Include="zSocket\Enum\skImeStatus.cs" />
    <Compile Include="zSocket\Enum\skMicrophoneStatus.cs" />
    <Compile Include="zSocket\Enum\skMirrrorMode.cs" />
    <Compile Include="zSocket\Enum\skModeType.cs" />
    <Compile Include="zSocket\Enum\skMsgType.cs" />
    <Compile Include="zSocket\Enum\skRtcStatus.cs" />
    <Compile Include="zSocket\Enum\skSafeModeStatus.cs" />
    <Compile Include="zSocket\Enum\skTipType.cs" />
    <Compile Include="zSocket\Enum\skVoIPStatus.cs" />
    <Compile Include="zSocket\Info\AppBaseInfo.cs" />
    <Compile Include="zSocket\Info\AppDetailInfo.cs" />
    <Compile Include="zSocket\Info\AppUserInfo.cs" />
    <Compile Include="zSocket\Info\skDevice.cs" />
    <Compile Include="zSocket\Info\skForwardUrl.cs" />
    <Compile Include="zSocket\Info\skHeartBeatInfo.cs" />
    <Compile Include="zSocket\Info\skLbsInfo.cs" />
    <Compile Include="zSocket\Info\skLoginInfo.cs" />
    <Compile Include="zSocket\Info\skPingInfo.cs" />
    <Compile Include="zSocket\Info\skSignalInfo.cs" />
    <Compile Include="zSocket\MyApp.cs" />
    <Compile Include="zSocket\MyDevice.cs" />
    <Compile Include="zSocket\MyRS.cs" />
    <Compile Include="zSocket\MySetting.cs" />
    <Compile Include="zSocket\MySocket.cs" />
    <Compile Include="zServer\API\CastAPI.cs" />
    <Compile Include="zServer\ServerPush.cs" />
    <Compile Include="zWave\WaveMgr.cs" />
    <Compile Include="zWebPage\AudioPlayerMgr.cs" />
    <Compile Include="zWebPage\RecordDB.cs" />
    <Compile Include="zWebPage\PageEnum.cs" />
    <Compile Include="zWebPage\PageHelper.cs" />
    <Compile Include="zWebPage\RecordDetail.cs" />
    <Compile Include="zWebPage\RecordFileOperation.cs" />
    <Compile Include="zWebPage\RecordFileState.cs" />
    <Compile Include="zWebPage\RecordMgr.cs" />
    <Compile Include="zWebPage\RecordFromType.cs" />
    <Compile Include="zWebPage\RecordTask.cs" />
    <Compile Include="zWebPage\RecordTipType.cs" />
    <Compile Include="zWebPage\RecordType.cs" />
    <Compile Include="zWebRTC\LivingClient.cs" />
    <Compile Include="zWebRTC\LivingEnum.cs" />
    <Compile Include="zWebRTC\LivingEventArgs.cs" />
    <Compile Include="zWebRTC\MqttEnum.cs" />
    <Compile Include="zWebRTC\MqttEventArgs.cs" />
    <Compile Include="zWebRTC\MqttPassword.cs" />
    <Compile Include="zWebRTC\MqttRTCClient.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\IMqttChannelAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\IMqttClientAdapterFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\IMqttServerAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\MqttChannelAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\MqttConnectingFailedException.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\MqttPacketInspector.cs" />
    <Compile Include="zWebRTC\Mqtt\Adapter\ReceivedMqttPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Certificates\BlobCertificateProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Certificates\ICertificateProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Certificates\X509CertificateProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Channel\IMqttChannel.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectingEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectResultCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Connecting\MqttClientConnectResultFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Disconnecting\MqttClientDisconnectedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Disconnecting\MqttClientDisconnectOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Disconnecting\MqttClientDisconnectOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Disconnecting\MqttClientDisconnectReason.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Exceptions\MqttClientDisconnectedException.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Exceptions\MqttClientUnexpectedDisconnectReceivedException.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\ExtendedAuthenticationExchange\IMqttExtendedAuthenticationExchangeHandler.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\ExtendedAuthenticationExchange\MqttExtendedAuthenticationExchangeContext.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\ExtendedAuthenticationExchange\MqttExtendedAuthenticationExchangeData.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\IMqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\MqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\MqttClientConnectionStatus.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\MqttClientExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\MqttPacketIdentifierProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\IMqttClientChannelOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\IMqttClientCredentialsProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientCertificateValidationEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientCredentials.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientDefaultCertificateValidationHandler.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientOptionsBuilderTlsParameters.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientOptionsBuilderWebSocketParameters.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientTcpOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientTcpOptionsExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientTlsOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientWebSocketOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Options\MqttClientWebSocketProxyOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Publishing\MqttClientPublishReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Publishing\MqttClientPublishResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Publishing\MqttClientPublishResultFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Receiving\MqttApplicationMessageReceivedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Receiving\MqttApplicationMessageReceivedReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeResultCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeResultFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Subscribing\MqttClientSubscribeResultItem.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeResultCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeResultFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Client\Unsubscribing\MqttClientUnsubscribeResultItem.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\IMqttNetLogger.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetEventLogger.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetLogLevel.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetLogMessage.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetLogMessagePublishedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetNullLogger.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetSourceLogger.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Logger\MqttNetSourceLoggerExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\PacketInspection\InspectMqttPacketEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\PacketInspection\MqttPacketFlowDirection.cs" />
    <Compile Include="zWebRTC\Mqtt\Diagnostics\Runtime\TargetFrameworkProvider.cs" />
    <Compile Include="zWebRTC\Mqtt\Exceptions\MqttCommunicationException.cs" />
    <Compile Include="zWebRTC\Mqtt\Exceptions\MqttCommunicationTimedOutException.cs" />
    <Compile Include="zWebRTC\Mqtt\Exceptions\MqttConfigurationException.cs" />
    <Compile Include="zWebRTC\Mqtt\Exceptions\MqttProtocolViolationException.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\IMqttPacketFormatter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttApplicationMessageFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttBufferReader.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttBufferWriter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttConnAckPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttConnectPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttConnectReasonCodeConverter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttDisconnectPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttFixedHeader.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPacketBuffer.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPacketFactories.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPacketFormatterAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttProtocolVersion.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPubAckPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPubCompPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPublishPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPubRecPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttPubRelPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttSubAckPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttSubscribePacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttUnsubAckPacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\MqttUnsubscribePacketFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\ReadFixedHeaderResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V3\MqttV3PacketFormatter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PacketDecoder.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PacketEncoder.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PacketFormatter.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PropertiesReader.cs" />
    <Compile Include="zWebRTC\Mqtt\Formatter\V5\MqttV5PropertiesWriter.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\CrossPlatformSocket.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttClientAdapterFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpChannel.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpChannel.Uwp.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpServerAdapter.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpServerAdapter.Uwp.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttTcpServerListener.cs" />
    <Compile Include="zWebRTC\Mqtt\Implementations\MqttWebSocketChannel.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncEvent.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncEventInvocator.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncLock.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncQueue.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncQueueDequeueResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncSignal.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\AsyncTaskCompletionSource.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\BlockingQueue.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\CompletedTask.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\Disposable.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\EmptyBuffer.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\MqttPacketBus.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\MqttPacketBusItem.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\MqttPacketBusPartition.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\MqttTaskTimeout.cs" />
    <Compile Include="zWebRTC\Mqtt\Internal\TaskExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\LowLevelClient\ILowLevelMqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\LowLevelClient\LowLevelMqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttApplicationMessage.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttApplicationMessageBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttApplicationMessageExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttFactory.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttTopicFilterBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttTopicFilterComparer.cs" />
    <Compile Include="zWebRTC\Mqtt\MqttTopicFilterCompareResult.cs" />
    <Compile Include="zWebRTC\Mqtt\PacketDispatcher\IMqttPacketAwaitable.cs" />
    <Compile Include="zWebRTC\Mqtt\PacketDispatcher\MqttPacketAwaitable.cs" />
    <Compile Include="zWebRTC\Mqtt\PacketDispatcher\MqttPacketAwaitableFilter.cs" />
    <Compile Include="zWebRTC\Mqtt\PacketDispatcher\MqttPacketDispatcher.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttAuthPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttConnAckPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttConnectPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttDisconnectPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPacketWithIdentifier.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPingReqPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPingRespPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPubAckPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPubCompPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPublishPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPubRecPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttPubRelPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttSubAckPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttSubscribePacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttTopicFilter.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttUnsubAckPacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttUnsubscribePacket.cs" />
    <Compile Include="zWebRTC\Mqtt\Packets\MqttUserProperty.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttAuthenticateReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttConnectReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttConnectReturnCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttControlPacketType.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttDisconnectReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPayloadFormatIndicator.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPropertyId.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPubAckReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPubCompReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPubRecReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttPubRelReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttQualityOfServiceLevel.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttRetainHandling.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttSubscribeReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttSubscribeReturnCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttTopicValidator.cs" />
    <Compile Include="zWebRTC\Mqtt\Protocol\MqttUnsubscribeReasonCode.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ApplicationMessageNotConsumedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientAcknowledgedPublishPacketEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientConnectedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientDisconnectedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientSubscribedTopicEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ClientUnsubscribedTopicEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\InterceptingPacketEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\InterceptingPublishEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\InterceptingSubscriptionEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\InterceptingUnsubscriptionEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\LoadingRetainedMessagesEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\PreparingSessionEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\RetainedMessageChangedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\SessionDeletedEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Events\ValidatingConnectionEventArgs.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\InjectedMqttApplicationMessage.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\CheckSubscriptionsResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\DispatchApplicationMessageResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\ISubscriptionChangedNotification.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttClient.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttClientSessionsManager.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttClientStatistics.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttClientSubscriptionsManager.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttRetainedMessagesManager.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttServerEventContainer.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttServerKeepAliveMonitor.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttSession.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\MqttSubscription.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\SubscribeResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\TopicHashMaskSubscriptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Internal\UnsubscribeResult.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\MqttClientDisconnectType.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\MqttRetainedMessageMatch.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\MqttServer.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\MqttServerExtensions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\IMqttServerCertificateCredentials.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttPendingMessagesOverflowStrategy.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerCertificateCredentials.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerOptionsBuilder.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerTcpEndpointBaseOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerTcpEndpointOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Options\MqttServerTlsTcpEndpointOptions.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\PublishResponse.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Status\MqttClientStatus.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\Status\MqttSessionStatus.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\SubscribeResponse.cs" />
    <Compile Include="zWebRTC\Mqtt\Server\UnsubscribeResponse.cs" />
    <Compile Include="zWebRTC\MyLiving.cs" />
    <Compile Include="zWebRTC\MyLog.cs" />
    <Compile Include="zWebRTC\MyMqtt.cs" />
    <Compile Include="zWebRTC\MyPeerTalk.cs" />
    <Compile Include="zWebRTC\MyReset.cs" />
    <Compile Include="zWebRTC\MyRTC.cs" />
    <Compile Include="zWebRTC\MyTouch.cs" />
    <Compile Include="zWebRTC\MyVirtualDesktop.cs" />
    <Compile Include="zWebRTC\RTCClient.cs" />
    <Compile Include="zWebRTC\RTCEnum.cs" />
    <Compile Include="zWebRTC\RTCErrorType.cs" />
    <Compile Include="zWebRTC\RTCEventArgs.cs" />
    <Compile Include="zWebRTC\RTCStatistics.cs" />
    <Compile Include="zWebRTC\RTCTurn.cs" />   
    <Compile Include="zServer\RS\RsControllerBiz.cs" />
    <Compile Include="zServer\API\BizAPI.cs" />
    <Compile Include="zCommon\_MDM.cs" />
    <Compile Include="zClass\MyRegistry.cs" />
    <Compile Include="zFormFunc\frmNotify.cs" />
    <Compile Include="zFormFunc\frmDevice.cs" />
    <Compile Include="zFormFunc\frmMessage.cs" />
    <Compile Include="zFormFunc\MsgBoxMgr.cs" />
    <Compile Include="zFormFunc\NotifyMgr.cs" />
    <Compile Include="zFormFunc\frmBase.cs" />
    <Compile Include="zFormRS\frmConnect.cs" />
    <Compile Include="zFormRS\frmSafeMode.cs" />
    <Compile Include="zFormRS\frmVoiceMsg.cs" />
    <Compile Include="zFormRS\RsCommon.cs" />
    <Compile Include="zWebPage\frmChat.cs" />
    <Compile Include="zWebPage\frmChatWeb.cs" />
    <Compile Include="zWebPage\frmFeedback.cs" />
    <Compile Include="zWebPage\frmPage.cs" />
    <Compile Include="zWebPage\frmChatMac.cs" />
    <Compile Include="zWebPage\frmFeedbackMac.cs" />
    <Compile Include="zFormRS\frmConnectMac.cs" />
    <Compile Include="zFormRS\frmSafeModeMac.cs" />
    <Compile Include="zFormRS\frmVoiceMsgMac.cs" />
    <Compile Include="zServer\API\Url\MyUrlExtention.cs" />
    <Compile Include="zService\ServiceMgr.cs" />
    <Compile Include="zSystem\PatchClass.cs" />
    <Compile Include="zSystem\AmsMgr.cs" />
    <Compile Include="zSystem\TaskSchedulerMgr.cs" />
    <Compile Include="zSystem\UwpMgr.cs" />
    <Compile Include="zSystem\AppMgr.cs" />
    <Compile Include="zSystem\AppHelper.cs" />
    <Compile Include="zSystem\TaskSchedulerMgr_System.cs" />
    <Compile Include="zSystem\WlanMgr.cs" />
    <Compile Include="zSystem\PatchMgr.cs" />
    <Compile Include="zSystem\DirectoryInfoMgr.cs" />
    <Compile Include="zFormRS\HookMgr.cs" />
    <Compile Include="zService\Service_MDM.cs" />
    <Compile Include="zService\Service_RS.cs" />
    <Compile Include="zService\Service_Watcher.cs" />
    <Compile Include="zService\ServiceMgr_Plist.cs" />
    <Compile Include="zClass\SettingMgr\KeyName_BIZ.cs" />
    <Compile Include="zServer\API\_UserBaseEx.cs" />
    <Compile Include="zService\ServiceMgr_Launch.cs" />
    <Compile Include="zMac\MiniAPI.cs" />
    <Compile Include="zMac\MyAPI_Mac.cs" />
    <Compile Include="zMac\PowerMgr.cs" />
    <Compile Include="zClass\MyAirPlayUsbMonitor.cs" />
    <Compile Include="zClass\MyAirPlayUsbSetupAPI.cs" />
    <Compile Include="zClass\MyAirPlayUSBVendor.cs" />
    <Compile Include="zServer\API\Url\MyUrlForFGKid.cs" />
    <Compile Include="zServer\API\FGKidAPI.cs" />
    <Compile Include="zServer\API\Info\MyTest.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android_Mac.csproj">
      <Project>{A2CAD0B7-AC6D-4E3B-9E3E-EBD69C4871C1}</Project>
      <Name>Android_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc_Mac.csproj">
      <Project>{3D7568EF-2BED-4B71-BBFA-D2F35BE5BF00}</Project>
      <Name>CoreMisc_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag_Mac.csproj">
      <Project>{338F4272-5C9F-47B6-B100-5634EF0D1DDE}</Project>
      <Name>CoreTag_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtil_Mac.csproj">
      <Project>{913145D8-1271-42F3-BB1A-5D7694D6CA3C}</Project>
      <Name>CoreUtil_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone_Mac.csproj">
      <Project>{FB6055F3-5096-4D14-A2FE-78571A23C920}</Project>
      <Name>iPhone_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf_Mac.csproj">
      <Project>{C8C2D342-F711-47C4-95CB-0AF620DA719B}</Project>
      <Name>ProtoBuf_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\SharpZLib_Mac.csproj">
      <Project>{CBCC1A89-433D-4102-97F5-A68A500920EF}</Project>
      <Name>SharpZLib_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\System.Web\System.Web_Mac.csproj">
      <Project>{C5E45B23-1A73-418C-B1CA-B35D970B8D05}</Project>
      <Name>System.Web_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\WebSocket4NetSource\WebSocket4Net_Mac.csproj">
      <Project>{6920447F-76B1-4739-822E-9CE3A2882718}</Project>
      <Name>WebSocket4Net_Mac</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreKid_Mac\CoreKid_Mac.csproj">
      <Project>{AD5EBBD5-1A3E-4532-A1B2-F8C10003DFA1}</Project>
      <Name>CoreKid_Mac</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="zFormRS\" />
    <Folder Include="ResourceWeb\" />
    <Folder Include="Resources\Dlls\" />
    <Folder Include="zMac\" />
  </ItemGroup>
  <ItemGroup>
    <BundleResource Include="Resources\frm_bg_state.png" />
    <BundleResource Include="Resources\ring_bg.png" />
    <BundleResource Include="Resources\btn_blue_line_4.png" />
    <BundleResource Include="Resources\frm_bg_tip.png" />
    <BundleResource Include="Resources\dgv_desc.png" />
    <BundleResource Include="Resources\ring_progress_bg.png" />
    <BundleResource Include="Resources\btn_minimize_4.png" />
    <BundleResource Include="Resources\btn_phone_reject_red_4.png" />
    <BundleResource Include="Resources\ring_progress_selected.png" />
    <BundleResource Include="Resources\btn_clear_4.png" />
    <BundleResource Include="Resources\pnl_bg_navigation.png" />
    <BundleResource Include="Resources\pgb_wait_008.png" />
    <BundleResource Include="Resources\btn_prev_3.png" />
    <BundleResource Include="Resources\pgb_wait_009.png" />
    <BundleResource Include="Resources\btn_file_4.png" />
    <BundleResource Include="Resources\btn_search_3.png" />
    <BundleResource Include="Resources\dgv_bg_header.png" />
    <BundleResource Include="Resources\btn_closure_4.png" />
    <BundleResource Include="Resources\dgv_asc.png" />
    <BundleResource Include="Resources\btn_clear_3.png" />
    <BundleResource Include="Resources\icon_music_100.png" />
    <BundleResource Include="Resources\airdroid_48.png" />
    <BundleResource Include="Resources\pgb_wait_007.png" />
    <BundleResource Include="Resources\pgb_wait_006.png" />
    <BundleResource Include="Resources\ring_progress_play.png" />
    <BundleResource Include="Resources\pgb_wait_004.png" />
    <BundleResource Include="Resources\pgb_wait_005.png" />
    <BundleResource Include="Resources\pgb_wait_001.png" />
    <BundleResource Include="Resources\ring_coordinate.png" />
    <BundleResource Include="Resources\pgb_wait_000.png" />
    <BundleResource Include="Resources\pgb_wait_002.png" />
    <BundleResource Include="Resources\pgb_wait_003.png" />
    <BundleResource Include="Resources\btn_voice_4.png" />
    <BundleResource Include="Resources\btn_blue_4.png" />
    <BundleResource Include="Resources\btn_next_3.png" />
    <BundleResource Include="Resources\ic_calling.png" />
    <BundleResource Include="Resources\airdroid.ico" />
    <BundleResource Include="Resources\btn_selectmusic_4.png" />
    <BundleResource Include="Resources\ring_unselect.png" />
    <BundleResource Include="Resources\bg_message.png" />
    <BundleResource Include="Resources\btn_close.png" />
    <BundleResource Include="Resources\Dlls\libWebRTC.dylib" />
    <BundleResource Include="Resources\Dlls\libComponentsUtil.dylib" />
  </ItemGroup>
  <ItemGroup>
    <None Include="ResourcesUrl\weburl_PandaSpy" />
    <None Include="ResourcesUrl\weburl_RemoteSupport" />
    <None Include="ResourcesUrl\weburl_BizDaemon" />
    <None Include="ResourcesUrl\weburl_AirDroidParentalConnector" />
    <None Include="ResourcesUrl\weburl_FlashGetCast" />
    <None Include="ResourcesCommon\btn_min_3.png" />
    <None Include="ResourcesCommon\btn_close_3_exit.png" />
    <None Include="ResourcesCommon\chk_unselect.png" />
    <None Include="ResourcesCommon\btn_close_3.png" />
    <None Include="ResourcesCommon\chk_selected.png" />
    <None Include="ResourcesCommon\btn_fullscreen_3.png" />
    <None Include="ResourcesCommon\cms_bg.png" />
    <None Include="ResourcesCommon\cms_item_2_select.png" />
    <None Include="ResourcesCommon\tvw_battery_bg.png" />
    <None Include="ResourcesCommon\tvw_node_close.png" />
    <None Include="ResourcesCommon\frm_bg_shadow.png" />
    <None Include="ResourcesCommon\rdo_selected.png" />
    <None Include="ResourcesCommon\scrollbar_v_bg.png" />
    <None Include="ResourcesCommon\rdo_bg_selected.png" />
    <None Include="ResourcesCommon\tvw_node_open.png" />
    <None Include="ResourcesCommon\rdo_unselect.png" />
    <None Include="ResourcesCommon\tvw_drapdrop.png" />
    <None Include="ResourcesCommon\pnl_bg_gray_line.png" />
    <None Include="ResourcesCommon\pgb_dot_blue.png" />
    <None Include="ResourcesCommon\scrollbar_h_split.png" />
    <None Include="ResourcesCommon\scrollbar_h_bg.png" />
    <None Include="ResourcesCommon\ctl_btn_dot.png" />
    <None Include="ResourcesCommon\tvw_battery_charging.png" />
    <None Include="ResourcesCommon\rdo_unselect_hover.png" />
    <None Include="ResourcesCommon\scrollbar_v_split.png" />
    <None Include="ResourceWeb\audio_incoming_call" />
    <None Include="ResourceWeb\audio_message" />
    <None Include="ResourceWeb\audio_transfer_end" />
    <None Include="ResourceWeb\audio_transfer_start" />
    <None Include="ResourceWeb\chat_gif_voice.gif" />
    <None Include="ResourceWeb\web_page.zip" />
    <None Include="ResourceWeb\chat_btn_4_closure.png" />
    <None Include="ResourceWeb\chat_btn_4_minimize.png" />
    <None Include="ResourceWeb\chat_btn_4_phone.png" />
    <None Include="ResourceWeb\chat_btn_4_send_file.png" />
    <None Include="ResourceWeb\chat_btn_4_send_msg.png" />
    <None Include="ResourceWeb\chat_btn_4_voice_cancel.png" />
    <None Include="ResourceWeb\chat_btn_4_voice_ok.png" />
    <None Include="ResourceWeb\chat_btn_4_voice.png" />
    <None Include="ResourceWeb\chat_btn_4_voip.png" />
    <None Include="ResourceWeb\chat_voip_connecting.png" />
    <None Include="ResourceWeb\icon_file_3gp_32.png" />
    <None Include="ResourceWeb\icon_file_aac_32.png" />
    <None Include="ResourceWeb\icon_file_amr_32.png" />
    <None Include="ResourceWeb\icon_file_apk_32.png" />
    <None Include="ResourceWeb\icon_file_avi_32.png" />
    <None Include="ResourceWeb\icon_file_dng_32.png" />
    <None Include="ResourceWeb\icon_file_doc_32.png" />
    <None Include="ResourceWeb\icon_file_docx_32.png" />
    <None Include="ResourceWeb\icon_file_exe_32.png" />
    <None Include="ResourceWeb\icon_file_flac_32.png" />
    <None Include="ResourceWeb\icon_file_folder_32.png" />
    <None Include="ResourceWeb\icon_file_gif_32.png" />
    <None Include="ResourceWeb\icon_file_gz_32.png" />
    <None Include="ResourceWeb\icon_file_jpeg_32.png" />
    <None Include="ResourceWeb\icon_file_jpg_32.png" />
    <None Include="ResourceWeb\icon_file_m4a_32.png" />
    <None Include="ResourceWeb\icon_file_mkv_32.png" />
    <None Include="ResourceWeb\icon_file_mov_32.png" />
    <None Include="ResourceWeb\icon_file_mp3_32.png" />
    <None Include="ResourceWeb\icon_file_mp4_32.png" />
    <None Include="ResourceWeb\icon_file_music_32.png" />
    <None Include="ResourceWeb\icon_file_opus_32.png" />
    <None Include="ResourceWeb\icon_file_other_32.png" />
    <None Include="ResourceWeb\icon_file_pdf_32.png" />
    <None Include="ResourceWeb\icon_file_photo_32.png" />
    <None Include="ResourceWeb\icon_file_pic_32.png" />
    <None Include="ResourceWeb\icon_file_png_32.png" />
    <None Include="ResourceWeb\icon_file_ppt_32.png" />
    <None Include="ResourceWeb\icon_file_pptx_32.png" />
    <None Include="ResourceWeb\icon_file_rar_32.png" />
    <None Include="ResourceWeb\icon_file_text_32.png" />
    <None Include="ResourceWeb\icon_file_txt_32.png" />
    <None Include="ResourceWeb\icon_file_video_32.png" />
    <None Include="ResourceWeb\icon_file_wav_32.png" />
    <None Include="ResourceWeb\icon_file_wma_32.png" />
    <None Include="ResourceWeb\icon_file_xls_32.png" />
    <None Include="ResourceWeb\icon_file_xlsx_32.png" />
    <None Include="ResourceWeb\icon_file_zip_32.png" />
    <None Include="ResourcesRS\rs_msg_4%402x.png" />
    <None Include="ResourcesRS\rs_pic_disconnect_Icon.png" />
    <None Include="ResourcesRS\rs_btn_blue_4%402x.png" />
    <None Include="ResourcesRS\rs_close_4.png" />
    <None Include="ResourcesRS\rs_arrow_down.png" />
    <None Include="ResourcesRS\rs_btn_green_4.png" />
    <None Include="ResourcesRS\rs_user_head%402x.png" />
    <None Include="ResourcesRS\rs_arrow_right%402x.png" />
    <None Include="ResourcesRS\rs_frm_bg_shadow%402x.png" />
    <None Include="ResourcesRS\rs_btn_blue_4.png" />
    <None Include="ResourcesRS\rs_reject_4%402x.png" />
    <None Include="ResourcesRS\rs_btn_red_4%402x.png" />
    <None Include="ResourcesRS\rs_btn_red_4.png" />
    <None Include="ResourcesRS\rs_tip%402x.png" />
    <None Include="ResourcesRS\rs_chk_hover%402x.png" />
    <None Include="ResourcesRS\rs_btn_green_4%402x.png" />
    <None Include="ResourcesRS\rs_chk%402x.png" />
    <None Include="ResourcesRS\rs_btn_grey_4%402x.png" />
    <None Include="ResourcesRS\rs_frm_shadow%402x.png" />
    <None Include="ResourcesRS\rs_calling.png" />
    <None Include="ResourcesRS\rs_calling%402x.png" />
    <None Include="ResourcesRS\rs_arrow_up.png" />
    <None Include="ResourcesRS\rs_frm_corner.png" />
    <None Include="ResourcesRS\rs_chk_hover.png" />
    <None Include="ResourcesRS\rs_arrow_up%402x.png" />
    <None Include="ResourcesRS\rs_msg_new_4.png" />
    <None Include="ResourcesRS\rs_frm_shadow.png" />
    <None Include="ResourcesRS\rs_voip_reject.png" />
    <None Include="ResourcesRS\rs_tip.png" />
    <None Include="ResourcesRS\rs_btn_white_4.png" />
    <None Include="ResourcesRS\rs_chk_disabled.png" />
    <None Include="ResourcesRS\rs_chk_check%402x.png" />
    <None Include="ResourcesRS\rs_btn_grey_4.png" />
    <None Include="ResourcesRS\rs_frm_bg_shadow.png" />
    <None Include="ResourcesRS\rs_voip_connect.png" />
    <None Include="ResourcesRS\rs_msg_new_4%402x.png" />
    <None Include="ResourcesRS\rs_close_4%402x.png" />
    <None Include="ResourcesRS\rs_chk.png" />
    <None Include="ResourcesRS\rs_msg_4.png" />
    <None Include="ResourcesRS\rs_user_head.png" />
    <None Include="ResourcesRS\rs_chk_check.png" />
    <None Include="ResourcesRS\rs_calling_4.png" />
    <None Include="ResourcesRS\rs_arrow_down%402x.png" />
    <None Include="ResourcesRS\rs_calling_4%402x.png" />
    <None Include="ResourcesRS\rs_arrow_left.png" />
    <None Include="ResourcesRS\rs_arrow_left%402x.png" />
    <None Include="ResourcesRS\rs_voip_reject%402x.png" />
    <None Include="ResourcesRS\rs_pic_disconnect_Icon%402x.png" />
    <None Include="ResourcesRS\rs_voip_connect%402x.png" />
    <None Include="ResourcesRS\rs_arrow_right.png" />
    <None Include="ResourcesRS\rs_reject_4.png" />
    <None Include="ResourcesRS\rs_chk_disabled%402x.png" />
    <None Include="ResourcesRS\rs_btn_white_4%402x.png" />
    <None Include="ResourcesRS\rs_frm_corner%402x.png" />
    <None Include="ResourcesUrl\weburl_FlashGetKidsConnector" />
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath)\Xamarin\Mac\Xamarin.Mac.CSharp.targets" />
  <ProjectExtensions>
    <MonoDevelop>
      <Properties>
        <Policies>
          <TextStylePolicy TabWidth="4" IndentWidth="4" RemoveTrailingWhitespace="True" NoTabsAfterNonTabs="False" EolMarker="Native" FileWidth="80" TabsToSpaces="True" scope="text/plain" />
          <VersionControlPolicy>
            <CommitMessageStyle Header="" Indent="" FirstFilePrefix="* " FileSeparator=":&#xA;* " LastFilePostfix=": " LineAlign="2" InterMessageLines="1" IncludeDirectoryPaths="False" Wrap="True" />
          </VersionControlPolicy>
          <XmlFormattingPolicy scope="application/xml">
            <DefaultFormat OmitXmlDeclaration="False" IndentContent="True" AttributesInNewLine="False" MaxAttributesPerLine="10" WrapAttributes="False" AlignAttributes="False" AlignAttributeValues="False" QuoteChar="&quot;" SpacesBeforeAssignment="0" SpacesAfterAssignment="0" EmptyLinesBeforeStart="0" EmptyLinesAfterStart="0" EmptyLinesBeforeEnd="0" EmptyLinesAfterEnd="0" />
          </XmlFormattingPolicy>
          <CSharpFormattingPolicy IndentBlock="True" IndentBraces="False" IndentSwitchSection="True" IndentSwitchCaseSection="True" LabelPositioning="OneLess" NewLinesForBracesInTypes="True" NewLinesForBracesInMethods="True" NewLinesForBracesInProperties="True" NewLinesForBracesInAccessors="True" NewLinesForBracesInAnonymousMethods="True" NewLinesForBracesInControlBlocks="True" NewLinesForBracesInAnonymousTypes="True" NewLinesForBracesInObjectCollectionArrayInitializers="True" NewLineForElse="True" NewLineForCatch="True" NewLineForFinally="True" NewLineForMembersInObjectInit="True" NewLineForMembersInAnonymousTypes="True" NewLineForClausesInQuery="True" SpacingAfterMethodDeclarationName="False" SpaceWithinMethodDeclarationParenthesis="False" SpaceBetweenEmptyMethodDeclarationParentheses="False" SpaceAfterMethodCallName="False" SpaceWithinMethodCallParentheses="False" SpaceBetweenEmptyMethodCallParentheses="False" SpaceAfterControlFlowStatementKeyword="True" SpaceWithinExpressionParentheses="False" SpaceWithinCastParentheses="False" SpaceWithinOtherParentheses="False" SpaceAfterCast="False" SpacesIgnoreAroundVariableDeclaration="False" SpaceBeforeOpenSquareBracket="False" SpaceBetweenEmptySquareBrackets="False" SpaceWithinSquareBrackets="False" SpaceAfterColonInBaseTypeDeclaration="True" SpaceAfterComma="True" SpaceAfterDot="False" SpaceAfterSemicolonsInForStatement="True" SpaceBeforeColonInBaseTypeDeclaration="True" SpaceBeforeComma="False" SpaceBeforeDot="False" SpaceBeforeSemicolonsInForStatement="False" SpacingAroundBinaryOperator="Single" WrappingPreserveSingleLine="True" WrappingKeepStatementsOnSingleLine="True" NewLinesForBracesInLambdaExpressionBody="False" scope="text/x-csharp" />
          <NameConventionPolicy>
            <Rules>
              <NamingRule Name="Namespaces" AffectedEntity="Namespace" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Types" AffectedEntity="Class, Struct, Enum, Delegate" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Interfaces" AffectedEntity="Interface" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredPrefixes>
                  <String>I</String>
                </RequiredPrefixes>
              </NamingRule>
              <NamingRule Name="Attributes" AffectedEntity="CustomAttributes" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredSuffixes>
                  <String>Attribute</String>
                </RequiredSuffixes>
              </NamingRule>
              <NamingRule Name="Event Arguments" AffectedEntity="CustomEventArgs" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredSuffixes>
                  <String>EventArgs</String>
                </RequiredSuffixes>
              </NamingRule>
              <NamingRule Name="Exceptions" AffectedEntity="CustomExceptions" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredSuffixes>
                  <String>Exception</String>
                </RequiredSuffixes>
              </NamingRule>
              <NamingRule Name="Methods" AffectedEntity="Methods" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Static Readonly Fields" AffectedEntity="ReadonlyField" VisibilityMask="Internal, Protected, Public" NamingStyle="PascalCase" IncludeInstanceMembers="False" IncludeStaticEntities="True" />
              <NamingRule Name="Fields (Non Private)" AffectedEntity="Field" VisibilityMask="Internal, Protected, Public" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="ReadOnly Fields (Non Private)" AffectedEntity="ReadonlyField" VisibilityMask="Internal, Protected, Public" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="False" />
              <NamingRule Name="Fields (Private)" AffectedEntity="Field, ReadonlyField" VisibilityMask="Private" NamingStyle="CamelCase" IncludeInstanceMembers="True" IncludeStaticEntities="False">
                <AllowedPrefixes>
                  <String>_</String>
                  <String>m_</String>
                </AllowedPrefixes>
              </NamingRule>
              <NamingRule Name="Static Fields (Private)" AffectedEntity="Field" VisibilityMask="Private" NamingStyle="CamelCase" IncludeInstanceMembers="False" IncludeStaticEntities="True" />
              <NamingRule Name="ReadOnly Fields (Private)" AffectedEntity="ReadonlyField" VisibilityMask="Private" NamingStyle="CamelCase" IncludeInstanceMembers="True" IncludeStaticEntities="False">
                <AllowedPrefixes>
                  <String>_</String>
                  <String>m_</String>
                </AllowedPrefixes>
              </NamingRule>
              <NamingRule Name="Constant Fields" AffectedEntity="ConstantField" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Properties" AffectedEntity="Property" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Events" AffectedEntity="Event" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Enum Members" AffectedEntity="EnumMember" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Parameters" AffectedEntity="Parameter" VisibilityMask="VisibilityMask" NamingStyle="CamelCase" IncludeInstanceMembers="True" IncludeStaticEntities="True" />
              <NamingRule Name="Type Parameters" AffectedEntity="TypeParameter" VisibilityMask="VisibilityMask" NamingStyle="PascalCase" IncludeInstanceMembers="True" IncludeStaticEntities="True">
                <RequiredPrefixes>
                  <String>T</String>
                </RequiredPrefixes>
              </NamingRule>
            </Rules>
          </NameConventionPolicy>
          <TextStylePolicy FileWidth="120" TabWidth="4" IndentWidth="4" RemoveTrailingWhitespace="True" EolMarker="Native" TabsToSpaces="True" NoTabsAfterNonTabs="True" scope="text/x-fsharp" />
          <TextStylePolicy TabWidth="4" IndentWidth="4" RemoveTrailingWhitespace="True" NoTabsAfterNonTabs="False" EolMarker="Native" FileWidth="80" TabsToSpaces="True" scope="text/x-csharp" />
          <TextStylePolicy TabsToSpaces="False" RemoveTrailingWhitespace="True" NoTabsAfterNonTabs="False" EolMarker="Native" FileWidth="80" TabWidth="8" IndentWidth="8" scope="application/vnd.apple-interface-builder" />
          <XmlFormattingPolicy scope="application/vnd.apple-interface-builder">
            <DefaultFormat OmitXmlDeclaration="False" IndentContent="True" AttributesInNewLine="False" MaxAttributesPerLine="10" WrapAttributes="False" AlignAttributes="False" AlignAttributeValues="False" QuoteChar="&quot;" SpacesBeforeAssignment="0" SpacesAfterAssignment="0" EmptyLinesBeforeStart="0" EmptyLinesAfterStart="0" EmptyLinesBeforeEnd="0" EmptyLinesAfterEnd="0" />
          </XmlFormattingPolicy>
          <TextStylePolicy TabsToSpaces="False" RemoveTrailingWhitespace="True" NoTabsAfterNonTabs="False" EolMarker="Native" FileWidth="80" TabWidth="8" IndentWidth="8" scope="application/xml" />
        </Policies>
      </Properties>
    </MonoDevelop>
  </ProjectExtensions>
</Project>