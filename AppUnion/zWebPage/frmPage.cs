﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using System.Diagnostics;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;
using iTong.Android.Wave;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
using SizeF = CoreGraphics.CGSize;
#else
using System.Drawing;
using iTong.CoreCefSharp;
#endif

namespace iTong.Android
{
    public partial class frmPage : frmBase
    {
        protected int mMaxImageWidth = 180;
        protected int mMaxImageHeight = 350;

        protected FileUploadMgr mUploadMgr = null;
        protected MultiThreadDownload mDownloadMgr = null;
        protected skDevice mDevice;
        protected Dictionary<long, RecordTask> mDictRecord = new Dictionary<long, RecordTask>();

#if CEF
        protected CefWebBrowser mBrowser;
#elif MAC
        protected skWebView mBrowser;
#else
        protected iTong.Components.tbWebBrowserEx Browser;
#endif

        public frmPage()
        {
            InitializeComponent();
        }

#if MAC
        private void InitializeComponent()
        {

        }

        public override void OnWindowCanClose(object sender, CancelEventArgs e)
        {
            this.DisposeResource(e);

            base.OnWindowCanClose(sender, e);
        }
#else
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            this.DisposeResource(e);

            base.OnFormClosing(e);
        }
#endif

        protected void DisposeResource(CancelEventArgs e)
        {
            try
            {
                if (!e.Cancel)
                {
                    if (this.mUploadMgr != null)
                    {
                        this.mUploadMgr.UploadCallBack -= OnUploadCallBack;
                        this.mUploadMgr.UploadCompleted -= OnUploadCompleted;
                        this.mUploadMgr.TaskStart -= OnUploadTaskStart;
                        this.mUploadMgr = null;
                    }

                    if (this.mDownloadMgr != null)
                    {
                        this.mDownloadMgr.TaskStart -= OnDownloadTaskStart;
                        this.mDownloadMgr.DownloadItemCallBack -= OnDownloadCallBack;
                        this.mDownloadMgr.DownloadItemCompleted -= OnDownloadCompleted;
                        this.mDownloadMgr = null;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "frmPage.DisposeResource");
            }
        }

        #region Browser初始化

        protected delegate void InitWebViewDelegate(Control pnlBrowser, string url, string moduleName);
        protected void InitWebBrowser(Control pnlBrowser, string url = "", string moduleName = "RSChat")
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.BeginInvoke(new InitWebViewDelegate(InitWebBrowser), pnlBrowser, url);
                    return;
                }

                int count = 0;
                DoChecked:
                if (!PageHelper.HasInitCef && count <= 3)
                {
                    count += 1;
                    Thread.Sleep(2000);
                    goto DoChecked;
                }
                else
                {
                    //初始化内嵌页
                    PageHelper.InitPage();
                }

                if (this.mBrowser != null)
                    return;

                //string url = "http://news.tongbu.com/96019.html";

#if MAC
                this.mBrowser = pnlBrowser.AddScrollView<skWebView>();
                this.mBrowser.skBackgroundColor = Color.White;
                this.mBrowser.Dock = DockStyle.Fill;
                this.mBrowser.ScrollBarsEnabled = false;
                this.mBrowser.AllowDrop = true;
                this.mBrowser.DragEnter += OnBrowser_DragEnter;
                this.mBrowser.DragDrop += OnBrowser_DragDrop;

#elif CEF
                //this.mBrowser = new CefWebBrowser(url, pnlBrowser.Width - MyDpi.ToDPI(2), pnlBrowser.Height - MyDpi.ToDPI(2), Folder.LogFolder);
                //this.mBrowser.MaximumSize = this.mBrowser.Size;

                int dpi = MyDpi.ToDPI(2, this.Dpi);
                this.mBrowser = new CefWebBrowser(url, pnlBrowser.Width - dpi, pnlBrowser.Height - dpi, Folder.LogFolder);
                                
                this.mBrowser.AllowDrop = true;
                this.mBrowser.DragEnter += OnBrowser_DragEnter;
                this.mBrowser.DragDrop += OnBrowser_DragDrop;
#else
                this.mBrowser = new iTong.Components.tbWebBrowserEx();
                this.mBrowser.ScrollBarsEnabled = false;
                this.mBrowser.Size = new Size(this.pnlBrowser.Width - 2, this.pnlBrowser.Height - 2);
#endif

#if MAC
                //加载缓存页面
                //this.mBrowser.Navigate(url, Folder.ApplicationDataFolder);
                this.mBrowser.Navigate(url);
#else
                pnlBrowser.Controls.Add(this.mBrowser);
                this.mBrowser.BackColor = Color.White;
                this.mBrowser.TabIndex = 0;
                this.mBrowser.Location = new Point(0, 0);
                this.mBrowser.Dock = DockStyle.None;
                //this.mBrowser.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom;
                this.mBrowser.Anchor = AnchorStyles.Left | AnchorStyles.Top;
                this.mBrowser.AccessibleDescription = moduleName;

                //加载缓存页面
                this.mBrowser.Navigate(url);
#endif


            }
            catch (Exception ex)
            {
                Common.LogException(ex, "frmChat.InitWebBrowser");
            }
        }


        protected void OnBrowser_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
                e.Effect = DragDropEffects.All; //重要代码：表明是所有类型的数据，比如文件路径
            else
                e.Effect = DragDropEffects.Copy;
        }

        protected void OnBrowser_DragDrop(object sender, DragEventArgs e)
        {
            object objData = e.Data.GetData(DataFormats.FileDrop);
            if (objData == null)
                return;

            string[] arrFile = objData as string[];
            if (arrFile == null || arrFile.Length == 0)
                return;

            this.UploadFile(arrFile);
        }

        #endregion Browser初始化


        #region 文件上传

        protected void InitFileUploadMgr()
        {
            this.mUploadMgr = FileUploadMgr.Instance;
            this.mUploadMgr.UploadCallBack += OnUploadCallBack;
            this.mUploadMgr.UploadCompleted += OnUploadCompleted;
            this.mUploadMgr.TaskStart += OnUploadTaskStart;
        }

        protected void OnUploadTaskStart(object sender, FileUploadTask e)
        {
            if (!RecordMgr.DictUpload.ContainsKey(e.TaskID))
                return;

            long recordID = RecordMgr.DictUpload[e.TaskID];

            if (e.Type == ResourceType.Voice)
                PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.WaitingResult);
            else
                PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.Transfering);
        }

        protected virtual void OnUploadCompleted(object sender, FileUploadTask e)
        {
            if (!RecordMgr.DictUpload.ContainsKey(e.TaskID))
                return;

            ThreadMgr.Start(() =>
            {
                //更新文件状态
                RecordFileState state = (e.IsCompleted ? RecordFileState.WaitingResult : RecordFileState.Failed);

                long recordID = RecordMgr.DictUpload[e.TaskID];
                if (this.mDictRecord.ContainsKey(recordID))
                {
                    RecordTask record = this.mDictRecord[recordID];
                    if (record.Content != null)
                    {
                        //更新文件状态到数据库
                        record.Content.state = state.ToStr();
                        RecordMgr.UpdateRecord(record);
                    }
                }

                PageHelper.SetFileState(this.mBrowser, recordID, state);
            });
        }

        protected virtual void OnUploadCallBack(object sender, FileUploadTask e)
        {
            if (!RecordMgr.DictUpload.ContainsKey(e.TaskID))
                return;

            long recordID = RecordMgr.DictUpload[e.TaskID];

            if (e.Type == ResourceType.Voice)
            {
                PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.WaitingResult);
            }
            else
            {
                PageHelper.UpdateFileProgress(this.mBrowser, recordID, e.ProgressString, e.UploadSize);
                PageHelper.UpdateFileSpeed(this.mBrowser, recordID, e.Speed);
            }
        }

        protected void UploadFile(string strFile, bool convertToAMR = false, double duration = 0)
        {
            UploadFile(new string[] { strFile }, convertToAMR, duration);
        }

        protected void UploadFile(string[] arrFile, bool convertToAMR = false, double duration = 0)
        {
            ThreadMgr.Start(() =>
            {
                try
                {
                    List<string> cacheFile = new List<string>();
                    foreach (string file in arrFile)
                    {
                        if (Utility.IsDirectory(file))
                        {
                            RsCommon.ShowSplashBox(this, this.Language.GetString("RemoteSupport.Message.FolderCannotUploadToCloud"));
                            //skSplashBox.Show(string.Format(this.Language.GetString("RemoteSupport.Message.FolderCannotUploadToCloud")), this);
                            continue;
                        }

                        if (Utility.HasOutSize(file, 1000000000))
                        {
                            RsCommon.ShowSplashBox(this, string.Format(this.Language.GetString("RemoteSupport.Message.SingeFileSpaceNotEnough"), "1GB"));
                            //skSplashBox.Show(string.Format(this.Language.GetString("RemoteSupport.Message.SingeFileSpaceNotEnough"), "1GB"), this);
                        }
                        else
                        {
                            cacheFile.Add(file);
                        }
                    }

                    arrFile = cacheFile.ToArray();

                    skDevice device = this.mDevice != null ? this.mDevice : UserBase.LocalDevice;

                    string extVoice = ".wav";
#if MAC
                    extVoice = ".aac";
#endif
                    foreach (string strFile in arrFile)
                    {
                        string newFile = strFile;
                        if (convertToAMR && Path.GetExtension(strFile).ToLower() == extVoice)
                        {
                            //替换路径
                            newFile = strFile.Replace(extVoice, ".amr");

                            //将wav语音转化为amr
                            if (!FFMpegHelper.ConvertToAmr(strFile, newFile))
                            {
                                //PageHelper.SetFileState(this.mBrowser, record.TaskID, RecordFileState.Failed);
                                skSplashBox.Show("wav convert arm failed.", this);
                                continue;
                            }

                            if (!MyLog.IsTestMode && !MyLog.ShowLog)
                                Utility.DeleteFile(strFile);
                        }

                        skFileUploadRequest uploadRequest = this.CreateFileUploadRequest(skFileUploadBucket.RS, newFile);

                        RecordTask record = RecordMgr.NewUpload(device, newFile, uploadRequest, duration);
                        if (record == null)
                            return;

                        this.AddFileItems(record);

                        if (uploadRequest == null)
                        {
                            PageHelper.SetFileState(this.mBrowser, record.TaskID, RecordFileState.Failed);
                            skSplashBox.Show("User session expires.", this);
                            return;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex, "frmChat.UploadFile");
                }
            });
        }

        protected void AddFileItems(RecordTask record)
        {
            if (record == null)
                return;

            //保存到字典中
            this.mDictRecord[record.TaskID] = record;

            //插入一条记录
            PageHelper.AddFileItems(this.mBrowser, record);
        }

        //protected virtual skFileUploadRequest CreateFileUploadRequest()
        //{
        //    skDevice device = this.mDevice != null ? this.mDevice : UserBase.LocalDevice;
        //    skDeviceDeployInfo deploy = RsAPI.DeviceDeployInfo;
        //    if (deploy == null || string.IsNullOrEmpty(deploy.dtoken) || string.IsNullOrEmpty(deploy.drefresh_token))
        //    {
        //        Console.WriteLine("no binding");
        //        return null;
        //    }

        //    if (!RsAPI.GetDeviceToken(deploy))
        //    {
        //        Console.WriteLine("Token refresh error");
        //        return null;
        //    }

        //    string strCountry = "US";
        //    if (AirSetting.InChina)
        //        strCountry = "CN";

        //    string Country = IniClass.GetIniSectionKey("Setting", "Country", Folder.ConfigIniFile);
        //    if (!string.IsNullOrEmpty(Country))
        //        strCountry = Country;

        //    skFileUploadRequest request = new skFileUploadRequest()
        //    {
        //        account_id = deploy.account_id,
        //        device_id = deploy.device_id,
        //        device_type = ((int)deploy.device_type).ToString(),
        //        multiple_part = true,
        //        country = strCountry,//US
        //        from = deploy.account_id,
        //        to = string.IsNullOrEmpty(device.user_id) ? device.user_id : device.unique_id,
        //        from_type = ServerPush.GetPushType(deploy.device_type),
        //        to_type = ServerPush.GetPushType(device.device_type),
        //        device_model = OSHelper.OSName,
        //        mode_type = MyApp.Current.mode_type,
        //    };

        //    //request.country = "US";

        //    return request;
        //}

        protected virtual skFileUploadRequest CreateFileUploadRequest(skFileUploadBucket bucket, string uploadFilePath, int feedback_id = 0)
        {
            skDevice device = this.mDevice != null ? this.mDevice : UserBase.LocalDevice;

            string strCountry = "US";

            if (RsAPI.DeviceDeployInfo != null && !string.IsNullOrEmpty(RsAPI.DeviceDeployInfo.country))
            {
                if (RsAPI.DeviceDeployInfo.country.ToLower() == "cn")
                    strCountry = "CN";
                else
                    strCountry = "US";
            }
            else if (AirSetting.InChina)
            {
                strCountry = "CN";
            }

            string strExt = Path.GetExtension(uploadFilePath);
            if (!string.IsNullOrEmpty(strExt))
                strExt = strExt.ToLower().Substring(1);

            skFileUploadRequest request = new skFileUploadRequest()
            {
                BucketInternal = bucket,
                feedback_id = feedback_id,
                filetype = strExt,
                filename = Path.GetFileName(uploadFilePath),
                account_id = device.account_id,
                device_id = device.device_id,
                device_type = ((int)device.device_type).ToString(),
                multiple_part = true,
                country = strCountry,//US
                from = device.account_id,
                to = string.IsNullOrEmpty(device.user_id) ? device.unique_id : device.user_id,
                from_type = UserBase.GetPushType(device.device_type),
                to_type = UserBase.GetPushType(device.device_type),
                device_model = OSHelper.OSName,
                mode_type = MyApp.Current.mode_type,
            };

            //request.country = "US";

            return request;
        }
        #endregion 文件上传


        #region 文件下载

        protected void InitDownloadMgr()
        {
            this.mDownloadMgr = MultiThreadDownload.Instance();
            this.mDownloadMgr.TaskStart += OnDownloadTaskStart;
            this.mDownloadMgr.DownloadItemCallBack += OnDownloadCallBack;
            this.mDownloadMgr.DownloadItemCompleted += OnDownloadCompleted;
        }

        protected void OnDownloadTaskStart(object sender, MultiThreadDownloadItem e)
        {
            if (!RecordMgr.DictDownload.ContainsKey(e.ItemInfo.TaskID))
                return;

            long recordID = RecordMgr.DictDownload[e.ItemInfo.TaskID];
            PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.Transfering);
        }

        protected void OnDownloadCallBack(object sender, MultiThreadDownloadEventArgs e)
        {
            if (!RecordMgr.DictDownload.ContainsKey(e.TaskID))
                return;

            long recordID = RecordMgr.DictDownload[e.TaskID];
            PageHelper.UpdateFileProgress(this.mBrowser, recordID, e.Progress.ToString(), e.ReceiveSize);
            PageHelper.UpdateFileSpeed(this.mBrowser, recordID, e.Speed);
        }

        protected void OnDownloadCompleted(object sender, MultiThreadDownloadCompletedEventArgs e)
        {
            if (!RecordMgr.DictDownload.ContainsKey(e.TaskID))
                return;

            long recordID = RecordMgr.DictDownload[e.TaskID];
            RecordTask record = this.mDictRecord[recordID];
            RecordFileState fileState = e.IsSuccessed ? RecordFileState.Successed : RecordFileState.Failed;

            // 下载下来的大小不一致问题
            if (File.Exists(e.FilePath) && record.Content != null && fileState == RecordFileState.Successed)
            {
                FileInfo fileInfo = new FileInfo(e.FilePath);
                if (Utility.FormatFileSize(fileInfo.Length) != record.Content.size)
                {
                    fileState = RecordFileState.Failed;
                }
            }

            string extension = Path.GetExtension(e.FilePath).ToLower();

            if (RecordMgr.GetFileType(e.FilePath) == skSocketFileType.Voice && record.Type == RecordType.Audio && record.Duration > 0)
            {
                fileState = RecordFileState.AudioUnRead;
            }

            if (record.Content != null)
            {
                record.Content.state = fileState.ToStr();
                RecordMgr.UpdateRecord(record);
            }

            if (fileState == RecordFileState.Successed && (extension.Contains("png") || extension.Contains("jpg") || extension.Contains("jpeg") || extension.Contains("gif") || extension.Contains("bmp") || extension.Contains("wmf")))
            {
                if (record.Content != null)
                {
                    Image img = Common.ImageFromFile(e.FilePath);
#if !MAC
                    img = Utility.AdjustImage(img);
#endif

                    if (img != null)
                    {
                        Utility.DeleteFile(e.FilePath);
                        img.Save(e.FilePath);
                    }
                    else
                    {
                        img = Common.ImageFromFile(e.FilePath);
                    }

                    //图片
                    JsonObject dictFileState = new JsonObject();
                    record.Content.icon = e.FilePath;
                    record.Content.imgWidth = (int)img.Size.Width;
                    record.Content.imgHeight = (int)img.Size.Height;
                    dictFileState.Add("id", recordID.ToString());
                    dictFileState.Add("icon", e.FilePath);
                    SizeF size = ImageSizeHandle(record.Content.imgWidth, record.Content.imgHeight);
                    dictFileState.Add("imgWidth", size.Width);
                    dictFileState.Add("imgHeight", size.Height);
                    string strFileState = JsonParser.SaveString(dictFileState);
                    PageHelper.ChangeFileAttribute(this.mBrowser, strFileState);

                    img.Dispose();
                }
            }

            PageHelper.SetFileState(this.mBrowser, recordID, fileState);

            //更新文件状态到数据库
            RecordMgr.UpdateRecord(record);
        }


        public SizeF ImageSizeHandle(int imgWidth, int imgHeight)
        {
            float width = imgWidth;
            float height = imgHeight;

            if (width > mMaxImageWidth)
            {
                width = mMaxImageWidth;
                height = height / ((float)imgWidth / mMaxImageWidth);

                if (height > mMaxImageHeight)
                {
                    width = width / ((float)height / mMaxImageHeight);
                    height = mMaxImageHeight;
                }
                goto DoExit;
            }

            if (height > mMaxImageHeight)
            {
                height = mMaxImageHeight;
                width = width / ((float)imgHeight / mMaxImageHeight);

                if (width > mMaxImageWidth)
                {
                    height = height / ((float)width / mMaxImageWidth);
                    width = mMaxImageWidth;
                }
                goto DoExit;
            }


            DoExit:
            return new SizeF(width, height);
        }

        #endregion

    }
}
