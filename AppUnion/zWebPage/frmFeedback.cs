﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using System.Diagnostics;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;
using iTong.Android.Wave;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
#else
using System.Drawing;
using iTong.CoreCefSharp;
#endif

namespace iTong.Android
{
    public partial class frmFeedback : frmPage
    {
        public override skSplit skTransparentImageSplit => new skSplit(15);
        //public override int skTitleBarHeight => 30;

        private bool mHasInit;
        private Dictionary<long, skFileUploadResponse> mDictUploaded = new Dictionary<long, skFileUploadResponse>();

        public frmFeedback()
        {
            InitializeComponent();
        }
#if DPI
        protected override void OnDpiChanged()
        {
            base.OnDpiChanged();
            if (this.mBrowser != null)
            {
                this.mBrowser.ZoomFactor = this.DpiScale;
                this.mBrowser.Refresh();
            }

            //InitButtonLocation();
            //this.btnSend.Location = this.btnSend.Location.ToDPI();
            //this.lblSendTip.Location = this.lblSendTip.Location.ToDPI();

        }
#endif

        protected override void InitControls()
        {
            base.InitControls();
            this.Size = new Size(620, 500);
            this.skShowStatusBar = true;
            this.skTitleBarHeight = 30;


            this.StartPosition = FormStartPosition.CenterParent;

            this.skTitle = this.Language.GetString("Business_Feedback_logs_title");
            this.skShowTitle = true;
            this.skBorderType = skBorderType.None;
            this.skTitleBackgroundColor = skColor.FromArgb(245, 246, 248);
            this.skBorderStrokeColor = skColor.FromArgb(216, 218, 224);
            this.Text = this.skTitle;
            this.skShowButtonMin = true;
            //this.skShowTitle = false;

            //this.skIcon = null;
#if MAC
            this.pnlBrowser.Frame = new Rectangle(0, 0, this.Width, this.Height - this.skTitleBarHeight);
            this.pnlBrowser.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom;

            //this.skBackgroundColor = NSColor.Red;
            //this.pnlBrowser.skBackgroundColor = NSColor.Blue;
            //this.mBrowser.skBackgroundColor = NSColor.Brown;
#else
            this.btnMin.skShowIcon = false;
            this.Icon = MyResource.GetIcon("airdroid.ico");
            this.skIcon = MyResource.GetImage("airdroid_48.png");
#endif
            this.ShowIcon = true;
            this.skIconSize = Size.Empty;
            this.mIconPadding = new Padding(20, -2, 0, 0);

            this.skTitleFont = MyFont.CreateFont("微软雅黑", 9.75f, true);
            this.skTitleColor = skColor.FromArgb(45, 47, 51);
            this.skTitleBackgroundColor = skColor.FromArgb(245, 246, 248);
            this.skBackgroundColor = Color.White;

            this.InitFileUploadMgr();
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            //this.Size = this.Size.ToDPI();
            //this.pnlBrowser.MaximumSize = this.pnlBrowser.MaximumSize.ToDPI();
            //this.pnlBrowser.MinimumSize = this.pnlBrowser.MinimumSize.ToDPI();
            //this.pnlBrowser.Size = this.pnlBrowser.Size.ToDPI();

            this.InitWebBrowser(this.pnlBrowser, PageHelper.PathFeedback.ToUrl(), "Feedback");

            this.mBrowser.ScrollBarsEnabled = false;
            this.mBrowser.AllowDrop = false; 
            
            this.mBrowser.NewMessage -= OnBrowser_NewMessage;
            this.mBrowser.NewMessage += OnBrowser_NewMessage;
#if NET47
            this.mBrowser.ZoomFactor = MyDpi.DPIScale;
#endif

            this.pnlBrowser.Location = new Point(0, this.skTitleBarHeight);
        }

        protected override void OnUploadCallBack(object sender, FileUploadTask e)
        {
            if (!mDictRecord.ContainsKey(e.TaskID))
                return;

            long recordID = e.TaskID;

            if (e.Type == ResourceType.Voice)
            {
                PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.WaitingResult);
            }
            else
            {
                PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.Transfering);

                string progress = e.ProgressString;
                if (e.ProgressInteger == 0 && e.ProgressIntegerFake > 0)
                    progress = e.ProgressIntegerFake.ToString();

                PageHelper.UpdateFileProgress(this.mBrowser, recordID, progress, e.UploadSize);
                PageHelper.UpdateFileSpeed(this.mBrowser, recordID, e.Speed);

                if (e.UploadSize >= e.FileSize)
                {
                    PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.Successed);

                    this.mDictUploaded.Add(e.TaskID, e.FileUploadResponse);
                }
            }
        }

        protected override void OnUploadCompleted(object sender, FileUploadTask e)
        {
            if (!mDictRecord.ContainsKey(e.TaskID))
                return;

            long recordID = e.TaskID;

            if (!e.IsCompleted)
            {
                PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.Failed);
            }
            else
            {
                PageHelper.SetFileState(this.mBrowser, recordID, RecordFileState.Successed);

                this.mDictUploaded.Add(e.TaskID, e.FileUploadResponse);
            }
        }

#if MAC
        protected void OnBrowser_NewMessage(object sender, WebBrowserMessageEventArgs e)
        {
            string message = e.Message;
#else
        protected void OnBrowser_NewMessage(object sender, string message)
        {
#endif
            try
            {
                if (string.IsNullOrEmpty(message))
                    return;

                MyLog.LogFile(message, "Web");

                PageMessage msg = MyJson.DeserializeFromJsonString<PageMessage>(message);
                if (msg == null || string.IsNullOrEmpty(msg.action))
                    return;

                switch (msg.ActionType)
                {
                    case PageAction.triggerPageLoaded://网页初始化
                        //{ "action":"triggerPageLoaded","isInit":0,"module":"RSChat"}
                        if (msg.isInit == 0)
                        {
                            //保存当前页面的模块信息
                            this.mBrowser.AccessibleDescription = msg.module;

                            //指定网页大小
                            PageInit pageInit = new PageInit(this.mBrowser, this.Language.CurrentLanguage.LangStr);

                            switch (Folder.AppType)
                            {
                                case RunType.RemoteSupport:
                                    pageInit.appType = "4";
                                    break;
                                case RunType.AirDroidParentalConnector:
                                    pageInit.appType = "5";
                                    break;
                                case RunType.BizDaemon:
                                    pageInit.appType = "6";
                                    break;
                                case RunType.GoInsightDaemon:
                                    pageInit.appType = "7";
                                    break;
                                default:
                                    break;
                            }

                            //this.mBrowser.MinimumSize = this.mBrowser.Size;
                            //this.mBrowser.MaximumSize = this.mBrowser.Size;

                            //网页初始化
                            PageHelper.InitEnv(this.mBrowser, pageInit);
                        }
                        else if (msg.isInit == 1)
                        {
                            this.mHasInit = true;
                        }
                        break;

                    case PageAction.triggerActive://浏览文件、关闭窗口
                        switch ((int)msg.operation)
                        {
                            case 11:
                                //浏览文件
                                this.BrowseFile();
                                break;
                            case 5: //关闭
                                this.BeginInvoke(new ThreadStart(() =>
                                {
                                    this.Close();
                                }));
                                break;
                            default:
                                break;
                        }
                        break;
                    case PageAction.feedbackFileAction:
                        long taskId = Common.GetLong(msg.id);
                        switch ((int)msg.operation)
                        {
                            case 1:
                                PageHelper.FileFeedbackOperation(this.mBrowser, taskId.ToString(), "1");
                                if (this.mDictUploaded.ContainsKey(taskId))
                                {
                                    this.mDictUploaded.Remove(taskId);
                                    FileUploadMgr.Instance.Stop(taskId);
                                }

                                if (this.mDictRecord.ContainsKey(taskId))
                                    this.mDictRecord.Remove(taskId);
                                break;
                            case 8:
                                try
                                {
                                    FileUploadTask uploadItem = this.mUploadMgr.GetTask(taskId, true);
                                    PageHelper.FileFeedbackOperation(this.mBrowser, taskId.ToString(), "8");
                                    if (uploadItem != null)
                                    {
                                        this.mUploadMgr.Start(uploadItem);
                                    }
                                }
                                catch (Exception)
                                {

                                }
                                break;
                            default:
                                break;
                        }
                        break;
                    case PageAction.triggerWebviewResize://浏览器大小改变
                        if (!this.mHasInit)
                            return;

                        if (msg.height > 0)
                            this.UpdateSize(msg.height);

                        break;

                    case PageAction.sendFeedback://发送反馈
                        if (string.IsNullOrEmpty(msg.email))
                        {
                            skSplashBox.Show(this.Language.GetString("feedback.empty_email"), this);
                            return;
                        }

                        if (string.IsNullOrEmpty(msg.content))
                        {
                            skSplashBox.Show(this.Language.GetString("feedback.empty_content"), this);
                            return;
                        }

                        OutputFeedbackLog("---------------------------Start Send Feedback!!!-------------------------\n");

                        ThreadMgr.StartWithPara(this.ThreadSendFeedback, msg);
                        break;
                    case PageAction.triggerNavigating://打开连接
                        Common.OpenExplorer(msg.url);
                        break;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmFeedback.OnBrowser_NewMessage");
            }
        }

        private void ThreadSendFeedback(object state)
        {
            try
            {
                OutputFeedbackLog("----------------------- Start SendFeedback Thread----------------------\n");

                PageMessage msg = state as PageMessage;
                if (msg == null)
                    return;

                string product_type = "2";

                if (ParaMgr.IsBiz)
                    product_type = "1";

                OutputFeedbackLog($"product_type={product_type}\n");

                ServerArgs<FeedbackInfo> info = RsAPI.SendFeedback(msg, product_type);

                if (info != null && info.Code == 1 && info.Data != null)
                {
                    OutputFeedbackLog($"info.Code={info.Code} info.Data.feedbackid={info.Data.feedbackid}\n");

                    List<string> fileKeys = new List<string>();

                    try
                    {
                        foreach (skFileUploadResponse item in this.mDictUploaded.Values)
                        {
                            if (item.data == null)
                                continue;

                            skFileUploadAuth data = item.data;

                            if (!string.IsNullOrEmpty(data.key))
                                fileKeys.Add(data.key);

                            else if (!string.IsNullOrEmpty(data.file_key))
                                fileKeys.Add(data.file_key);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex.ToString());
                    }

                    OutputFeedbackLog($"fileKeys.Count={fileKeys.Count}\n");

                    if (fileKeys.Count > 0)
                    {
                        ServerArgs<string> suc = RsAPI.BindFilesToFeedback(info.Data.feedbackid, fileKeys);
                    }

                    OutputFeedbackLog($"msg.uploadLog={msg.uploadLog}\n");

                    if (msg.uploadLog == "1")
                    {
                        RsCommon.DoCreateLogByFeedbackId(info.Data.feedbackid);
                    }

                    this.mDictUploaded.Clear();

                    OutputFeedbackLog("----------------------- End SendFeedback Thread----------------------\n");

                    PageHelper.ShowFeedbackSuccess(this.mBrowser);
                }
                else
                {
                    Dictionary<object, object> dictErrorCode = new Dictionary<object, object>();
                    if (info != null)
                        dictErrorCode.Add("code", info.Code);
                    else
                        dictErrorCode.Add("code", 0);

                    string strErrorCode = MyJson.SerializeToJsonString(dictErrorCode);
                    PageHelper.ShowFeedbackFileError(this.mBrowser, strErrorCode);

                    if (!Common.NetworkIsAvailable())
                    {
                        RsCommon.ShowSplashBox(this, this.Language.GetString("Common_check_network"));
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "frmFeedback.ThreadSendFeedback");
            }
        }

        protected void BrowseFile()
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new ThreadStart(BrowseFile));
                return;
            }

            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = this.Language.GetString("Common.OpenFileDialog.PicFile") + "(*.PNG;*.JPG;*.JPEG;*.GIF)|*.PNG;*.JPG;*.JPEG;*.GIF"; // 图片文件
            ofd.Multiselect = false;

            if (ofd.ShowDialog() == DialogResult.OK)
            {
                RecordList recordList = new RecordList();
                List<RecordDetail> recordDetail = new List<RecordDetail>();
                foreach (string strFileName in ofd.FileNames)
                {
                    if (!File.Exists(strFileName))
                        continue;

                    skFileUploadRequest uploadRequest = FileUploadTask.CreateFileUploadRequest(skFileUploadBucket.Feedback, strFileName);
                    FileUploadTask uploadTask = FileUploadMgr.Instance.NewTask(strFileName, uploadRequest, true, true);

                    RecordTask record = RecordMgr.CreateRecord(UserBase.LocalDevice, strFileName, 0);
                    record.TaskID = uploadTask.TaskID;
                    record.Content.id = uploadTask.TaskID.ToString();
                    record.Content.state = uploadTask.IsCompleted ? RecordFileState.Successed.ToStr() : record.Content.state;

                    recordDetail.Add(record.Content);

                    //保存到字典
                    mDictRecord[record.TaskID] = record;
                }

                recordList.order = "1";
                recordList.time = 0;
                recordList.list = recordDetail;
                PageHelper.AddFileItems(this.mBrowser, recordList);
            }
        }

        private void UpdateSize(int height)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new ThreadStart(() =>
                {
                    this.UpdateSize(height);
                }));
                return;
            }

#if MAC
            //this.pnlBrowser.Anchor = AnchorStyles.None;
            //this.pnlBrowser.Frame = new CGRect(0, 0, this.Width, height);
            this.SizeForWindow = new Size(620, height + this.skTitleBarHeight);            
#else
            this.Size = new Size(MyDpi.ToDPI(620, this.Dpi), MyDpi.ToDPI(height, this.Dpi) + skTitleBarHeight);
            this.OriginalSize = this.Size.ReDPI(this.Dpi);
            this.pnlBrowser.Size = new Size(620, height).ToDPI(this.Dpi);
            this.mBrowser.Size = this.pnlBrowser.Size;
            this.pnlBrowser.Location = new Point(0, this.skTitleBarHeight);
#endif
        }

        /// <summary>
        /// 输出反馈日志
        /// </summary>
        private void OutputFeedbackLog(string msg)
        {
            MyLog.LogFile(msg, "feedback");
        }
    }
}
