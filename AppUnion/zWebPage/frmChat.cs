﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Threading;
using System.IO;
using System.Diagnostics;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;
using iTong.Android.Wave;


#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
#else
using System.Drawing;
using iTong.CoreCefSharp;
#endif

namespace iTong.Android
{
    public enum ViewType
    {
        None,
        VoIPLink,
        VoIPTime,
    }

    public partial class frmChat : frmPage
    {
        private Color mColorSplit = skColor.FromArgb(228, 230, 236);

        private bool mHasInit = false;
        private ViewType mViewType = ViewType.None;
        private string mVoicePath = string.Empty;
        private WaveRecord mVoiceRecord = null;
        //private System.Timers.Timer mTimerVoIP = null;
        private int mVoIPTime = 0;
        private bool mHasShowTip = false;

        private skWebSocketForRS mSocket;
        private string mLocalHtmlPath = string.Empty;
        private string mCurrentVoice;
        private long mCurrentRecordId;
        private string mCacheFolder;
        //private skDevice mDevice;
        //private FileUploadMgr mUploadMgr = null;
        //private MultiThreadDownload mDownloadMgr = null;

        public static bool IsDebugMode { get; set; } = false;

        public bool DEBUG { get => false; }

        public bool CanClose { get; set; } = false;

        public Action<object> Callback = null;

        public override skSplit skTransparentImageSplit => new skSplit(15);


        public frmChat(skDevice dev, string pathHtml, bool hasShowVoiceTip, skWebSocketForRS socket = null)
        {
            InitializeComponent();

            this.mSocket = socket;
            this.mDevice = dev;
            this.mLocalHtmlPath = pathHtml;
            this.mHasShowTip = hasShowVoiceTip;
            this.mCacheFolder = Path.Combine(Folder.CacheFolder, "Devices", "Cache");
        }

#if MAC
        protected void InitFrame()
        {
            Padding padingBottom = new Padding(10, 5, 10, 10);

            this.pnlBottom.Frame = new Rectangle(0, 0, this.Width, 132);
            this.pnlBottom.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;

            this.btnSend.Location = new Point(this.Width - padingBottom.Right - this.btnSend.Width, padingBottom.Bottom);
            this.lblSendTip.Frame = new Rectangle(padingBottom.Left, padingBottom.Bottom + (this.btnSend.Height - this.lblSendTip.Height) / 2, this.btnSend.Left - 20 - padingBottom.Left, this.lblSendTip.Height);
            this.pnlContentContainer.Frame = new Rectangle(padingBottom.Left, this.btnSend.Bottom + 10, this.Width - padingBottom.Horizontal, 40);
            //this.btnSelectFile.Location = new Point(padingBottom.Left, this.rtbContent.Bottom + (this.pnlBottom.Height - this.rtbContent.Bottom - this.btnSelectFile.Height) / 2);
            this.btnSelectFile.Location = new Point(padingBottom.Left, pnlContentContainer.Bottom + (this.pnlBottom.Height - this.pnlContentContainer.Frame.Bottom - this.btnSelectFile.Height) / 2);
            this.btnVoice.Location = new Point(this.btnSelectFile.Right + 10, this.btnSelectFile.Top + (this.btnSelectFile.Height - this.btnVoice.Height) / 2);
            this.btnVoIP.Location = new Point(this.Width - padingBottom.Right - this.btnVoIP.Width, this.btnSelectFile.Top + (this.btnSelectFile.Height - this.btnVoIP.Height) / 2);

            this.rtbContent.FocusRingType = NSFocusRingType.None;
            //this.rtbContent.Bordered = false;
            this.rtbContent.TextChanged += OnRtbContent_TextChanged;
            this.rtbContent.KeyUped += rtbContent_KeyDown;
            this.rtbContent.AllowDrop = true;
            this.rtbContent.DragEnter += rtbContent_DragEnter;

            this.pnlVoIP.Frame = new Rectangle(0, this.pnlBottom.Bottom, this.Width, 24);
            this.pnlVoIP.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;
            this.lblVoIPTime.Location = Point.Empty;
            this.lblVoIPTime.Size = this.pnlVoIP.Size;

            //this.pnlVoice.skTrianglePosition = skTrianglePosition.BottomLeft;
            //this.pnlVoice.Anchor = AnchorStyles.Left | AnchorStyles.Top;
            //this.pnlVoice.Size = new Size(131 + 2, 50 + 26 + this.pnlVoice.skTriangleHeight + 2);
            //this.pnlVoice.Location = new Point(this.btnVoice.Left - this.pnlVoice.skBorderRadius.BottomLeft - this.pnlVoice.skTrianglePlaceCorner - this.pnlVoice.skTriangleWidth / 2, this.pnlVoIP.Top);

            //this.btnVoiceOK.Location = new Point((this.pnlVoice.Width - this.btnVoiceOK.Width - this.btnVoiceCancel.Width) / 2 + 1, this.pnlVoice.skTriangleHeight + 1);
            //this.btnVoiceOK.Anchor = AnchorStyles.Left | AnchorStyles.Top;

            //this.btnVoiceCancel.Location = new Point(this.btnVoiceOK.Right, this.btnVoiceOK.Top);
            //this.btnVoiceCancel.Anchor = AnchorStyles.Left | AnchorStyles.Top;

            //this.picVoiceGif.Location = new Point(0, this.btnVoiceOK.Bottom);
            //this.picVoiceGif.Anchor = AnchorStyles.Left | AnchorStyles.Top;

            this.pnlBrowser.Frame = new Rectangle(0, this.pnlBottom.Bottom, this.Width, this.Height - this.skTitleBarHeight - this.pnlBottom.Bottom);
            this.pnlBrowser.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom | AnchorStyles.Top;

            //this.skBackgroundColor = NSColor.Red;
            //this.pnlBrowser.skBackgroundColor = NSColor.Yellow;
            //this.pnlBottom.skBackgroundColor = NSColor.Brown;
            //this.pnlVoIP.skBackgroundColor = NSColor.Green;
        }
#else
        /// <summary>最小化按钮重新定位</summary>
        protected override void InitButtonLocation()
        {
            base.InitButtonLocation();

            if (this.btnMin != null)
                this.btnMin.Location = new Point(this.btnMin.Location.X - 13, this.btnMin.Location.Y);
        }
#endif

#if DPI
        protected override void OnDpiChanged()
        {
            base.OnDpiChanged();

            this.ControlDpiHandle();
            if (this.mBrowser != null)
            {
                this.mBrowser.ZoomFactor = this.DpiScale;
                this.mBrowser.Refresh();
            }
        }
#endif
        protected override void InitControls()
        {
            base.InitControls();

#if !MAC
            this.Size = new Size(400, 560);
            this.skTitleBarHeight = 30;

            //先初始化实时语音面板，再初始浏览器
            this.ShowPanelVoIP(this.mViewType);

            //初始化录音面板
            this.ShowPanelVoice(false);

            this.skShowButtonMin = true;

            //初始化浏览器容器
            this.pnlBrowser.Location = new Point(this.pnlBottom.Left, this.skTitleBarHeight);
            this.pnlBrowser.Size = new Size(this.pnlBottom.Width, this.pnlBottom.Top - this.skTitleBarHeight);
            //this.pnlBrowser.MaximumSize = this.pnlBrowser.Size;
            //this.pnlBrowser.Height -= 10;
            this.pnlBrowser.skBorderType = skBorderType.None;
            this.pnlBrowser.skBorderRadius = skBorderRadius.Empty;
            this.pnlBrowser.skBorderStrokeColor = this.mColorSplit;

            this.pnlBottom.skBorderType = skBorderType.Top;
            this.pnlBottom.skBorderRadius = skBorderRadius.Empty;
            this.pnlBottom.skBorderStrokeColor = this.mColorSplit;

            this.Icon = MyResource.GetIcon("airdroid.ico");
            this.skIcon = MyResource.GetImage("airdroid_48.png");
            this.ShowIcon = true;
            this.skIconSize = Size.Empty;
            this.FormBorderStyle = FormBorderStyle.None;
            this.skBorderType = skBorderType.None;
            this.skTitleBackgroundColor = skColor.FromArgb(0, 98, 246);
            this.skTitleFont = MyFont.CreateFont("微软雅黑", 9, true);
            this.skTitleColor = skColor.FromArgb(255, 255, 255);
            this.skTitle = this.mDevice?.user_info.mail;


            this.Text = this.Language.GetString("rs_chat");
            this.skButtonClose.skIcon = MyResource.GetImage("chat_btn_4_closure.png");
            this.skButtonMin.skIcon = MyResource.GetImage("chat_btn_4_minimize.png");

            this.rtbContent.ReComputeWidth();
            this.rtbContent.textBox.TextChanged += OnRtbContent_TextChanged;
            this.rtbContent.textBox.KeyDown += rtbContent_KeyDown;
            this.rtbContent.textBox.AllowDrop = true;
            this.rtbContent.textBox.DragEnter += rtbContent_DragEnter;

            this.btnSelectFile.skToolTip = this.Language.GetString("File.Label.FileTransfer");
            this.btnVoice.skToolTip = this.Language.GetString("RemoteSupport.Button.AddVoice");
            this.btnVoIP.skToolTip = this.Language.GetString("RemoteSupport.Button.Voip");
            this.btnSend.skText = this.Language.GetString("Popup.Button.Send");
            this.lblSendTip.skText = this.Language.GetString("Common.SendAndEndLine");


            AudioPlayerMgr.PlayCompleted += OnAudioPlayCompleted;

#endif

        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
#if !MAC
            this.InitWebBrowser(this.pnlBrowser, this.mLocalHtmlPath.ToUrl(), "RSChat");

            if (this.mBrowser != null)
            {                
                this.mBrowser.NewMessage += OnBrowser_NewMessage;                
#if NET47
                this.mBrowser.ZoomFactor = MyDpi.DPIScale;
#else
                this.mBrowser.SizeChanged += OnBrowser_SizeChanged;
#endif
            }

            if (this.CheckSendFile())
            {
                this.rtbContent.textBox.AllowDrop = true;
                this.rtbContent.textBox.DragEnter += OnBrowser_DragEnter;
                this.rtbContent.textBox.DragDrop += OnBrowser_DragDrop;
            }
            else
            {
                this.mBrowser.AllowDrop = false;
                this.mBrowser.DragEnter -= OnBrowser_DragEnter;
                this.mBrowser.DragDrop -= OnBrowser_DragDrop;
                this.btnVoice.Location = this.btnSelectFile.Location;
                this.btnSelectFile.Visible = false;
            }

            this.InitFileUploadMgr();
            this.mUploadMgr.UploadCompleted -= OnUploadCompleted;
            this.mUploadMgr.UploadCompleted += OnFileUploadCompleted;

            this.InitDownloadMgr();
#endif
        }


        public void ControlDpiHandle()
        {
            this.pnlBottom.Location = new Point(0, this.Size.Height - this.pnlBottom.Size.Height);
            this.pnlBrowser.Location = new Point(this.pnlBottom.Left, this.skTitleBarHeight);
            this.pnlBrowser.Size = new Size(this.pnlBottom.Width, this.pnlBottom.Top - this.skTitleBarHeight);

#if !MAC
            this.rtbContent.skFont = new Font(this.rtbContent.skFont.FontFamily, 10.5F * ((float)this.Dpi / (float)this.DeviceDpi), this.rtbContent.skFont.Style);
#endif
        }

#if MAC
        public void InitControlsEx()
        {
            //先初始化实时语音面板，再初始浏览器
            this.ShowPanelVoIP(this.mViewType);

            //初始化录音面板
            this.ShowPanelVoice(false);

            this.skShowButtonMin = true;

            //初始化浏览器容器
            this.pnlBrowser.Location = new Point(this.pnlBottom.Left, this.pnlBrowser.Top);
            this.pnlBrowser.Size = new Size(this.pnlBottom.Width, this.pnlBottom.Top - this.pnlBrowser.Top);
            this.pnlBrowser.Height -= 10;
            this.pnlBrowser.skBorderType = skBorderType.None;
            this.pnlBrowser.skBorderRadius = skBorderRadius.Empty;
            this.pnlBrowser.skBorderStrokeColor = this.mColorSplit;

            this.pnlBottom.skBorderType = skBorderType.Top;
            this.pnlBottom.skBorderRadius = skBorderRadius.Empty;
            this.pnlBottom.skBorderStrokeColor = this.mColorSplit;

            this.Icon = MyResource.GetIcon("airdroid.ico");
            this.skIcon = MyResource.GetImage("airdroid_48.png");
            this.ShowIcon = true;
            this.skIconSize = Size.Empty;
            this.FormBorderStyle = FormBorderStyle.None;
            this.skBorderType = skBorderType.None;
            this.skTitleBackgroundColor = skColor.FromArgb(0, 98, 246);
            this.skTitleFont = MyFont.CreateFont("微软雅黑", 9, true);
            this.skTitleColor = skColor.FromArgb(255, 255, 255);
            this.skTitle = this.mDevice?.user_info.mail;

            this.InitFrame();

            this.btnSelectFile.skToolTip = this.Language.GetString("File.Label.FileTransfer");
            this.btnVoice.skToolTip = this.Language.GetString("RemoteSupport.Button.AddVoice");
            this.btnVoIP.skToolTip = this.Language.GetString("RemoteSupport.Button.Voip");
            this.btnSend.skText = this.Language.GetString("Popup.Button.Send");
            this.lblSendTip.skText = this.Language.GetString("Common.SendAndEndLine");

            this.InitWebBrowser(this.pnlBrowser, this.mLocalHtmlPath.ToUrl(), "RSChat");

            if (this.mBrowser != null)
            {
                this.mBrowser.IsResized = true;
                this.mBrowser.NewMessage += OnBrowser_NewMessage;
                this.mBrowser.SizeChanged += OnBrowser_SizeChanged;
            }

            if (this.CheckSendFile())
            {
                this.rtbContent.AllowDrop = true;
                this.rtbContent.DragEnter += OnBrowser_DragEnter;
                this.rtbContent.DragDrop += OnBrowser_DragDrop;
                this.btnSelectFile.Visible = true;
            }
            else
            {
                this.mBrowser.AllowDrop = false;
                this.mBrowser.DragEnter -= OnBrowser_DragEnter;
                this.mBrowser.DragDrop -= OnBrowser_DragDrop;
                this.btnVoice.Location = this.btnSelectFile.Location;
                this.btnSelectFile.Visible = false;
            }

            this.InitFileUploadMgr();
            this.mUploadMgr.UploadCompleted -= OnUploadCompleted;
            this.mUploadMgr.UploadCompleted += OnFileUploadCompleted;

            this.InitDownloadMgr();

            AudioPlayerMgr.PlayCompleted += OnAudioPlayCompleted;
        }
#endif

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (this.mVoiceRecord != null)
                this.btnVoiceCancel_Click(this.btnVoiceCancel, null);

            //根据设备是否连接来判断是否取消连接
            if (!this.CanClose && this.mDevice != null/* && this.mSocket != null && this.mSocket.IsConnect*/)
            {
                e.Cancel = true;
            }

            //回调基类
            base.OnFormClosing(e);

            if (e.Cancel)
            {
#if !MAC
                this.btnClose.ReMouseState();
#endif
                //取消关闭、最小化隐藏窗口
                this.ShowInTaskbar = false;
                this.Hide();
                return;
            }
            else
            {
                Utility.DeleteDirectory(this.mCacheFolder);

                AudioPlayerMgr.PlayCompleted -= OnAudioPlayCompleted;

                if (this.mSocket != null)
                    this.mSocket.Callback -= OnSocketCallback;
                else
                    SocketMgr.RsCallback -= OnSocketCallback;
            }
        }

        public void Log(string errMsg)
        {
            MyLog.LogFile(errMsg, "Chat");
        }

        public void LogException(string errMsg, string funcName)
        {
            MyLog.LogFile(funcName + "\r\n" + errMsg, "Chat");
        }

        public void SetAsDockFill()
        {
#if MAC
            this.pnlBottom.Frame = new Rectangle(0, 0, this.Width, this.pnlBottom.Height);
            this.pnlBrowser.Frame = new Rectangle(0, this.pnlBottom.Bottom, this.Width, this.Height - this.pnlBottom.Bottom);
#else
            this.pnlBottom.Location = new Point(0, this.Height - this.pnlBottom.Height);
            this.pnlBottom.Size = new Size(this.Width, this.pnlBottom.Height);

            this.pnlBrowser.Location = new Point(0, 0);
            this.pnlBrowser.Size = new Size(this.Width, this.pnlBottom.Bottom);
#endif
        }


        private delegate void ShowPanelVoiceHanlder(bool blnShow);
        private void ShowPanelVoice(bool blnShow)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new ShowPanelVoiceHanlder(this.ShowPanelVoice), blnShow);
            }
            else
            {
#if MAC
                //this.pnlVoice.skBorderWidth = 1;
                //this.pnlVoice.skBorderStrokeColor = skColor.FromArgb(228, 230, 236);
                //this.pnlVoice.skBorderFillColor = Color.White;
                //this.pnlVoice.skBackgroundColor = null;
                //this.pnlVoice.Name = "pnlVoice";
                //this.pnlVoice.skTrianglePosition = skTrianglePosition.BottomLeft;
                //this.pnlVoice.skBorderType = skBorderType.Round;
                this.pnlVoice.Anchor = AnchorStyles.None;
                this.pnlVoice.Size = new Size(this.btnVoiceOK.Width * 2 + 3, this.picVoiceGif.Height + this.btnVoiceOK.Height + this.pnlVoice.skTriangleHeight + 3);
                this.pnlVoice.Location = new Point(this.btnVoice.Left + this.btnVoice.Width / 2 - this.pnlVoice.skTrianglePlaceCorner - this.pnlVoice.skTriangleWidth / 2, this.pnlBottom.Bottom + 3);

                //this.pnlVoice.Invalidate();

                this.btnVoiceOK.Anchor = AnchorStyles.None;
                this.btnVoiceOK.Size = new Size(51, 26);
                this.btnVoiceOK.Location = new Point(1, this.pnlVoice.skTriangleHeight + 1);

                this.btnVoiceCancel.Anchor = AnchorStyles.None;
                this.btnVoiceCancel.Size = new Size(51, 26);
                this.btnVoiceCancel.Location = new Point(this.btnVoiceOK.Right + 1, this.btnVoiceOK.Top);

                this.picVoiceGif.Anchor = AnchorStyles.None;
                this.picVoiceGif.Size = new Size(102, 42);
                this.picVoiceGif.Location = new Point((this.pnlVoice.Width - this.picVoiceGif.Width) / 2, this.btnVoiceOK.Bottom + 1);
#else
                this.pnlVoice.Anchor = AnchorStyles.Left | AnchorStyles.Top;
#endif
                this.pnlVoice.Visible = blnShow;

                if (blnShow)
                    this.pnlVoice.BringToFront();

                Console.WriteLine(string.Format("this.pnlVoice.Frame    = {0}, Visible = {1}", this.pnlVoice.Frame.ToString(), this.pnlVoice.Visible));
                Console.WriteLine(string.Format("this.picVoiceGif.Frame = {0}, Visible = {1}", this.picVoiceGif.Frame.ToString(), this.picVoiceGif.Visible));
                Console.WriteLine(string.Format("this.btnVoiceOK.Frame  = {0}, Visible = {1}", this.btnVoiceOK.Frame.ToString(), this.btnVoiceOK.Visible));
                Console.WriteLine(string.Format("this.btnCancel.Frame   = {0}, Visible = {1}", this.btnVoiceCancel.Frame.ToString(), this.btnVoiceCancel.Visible));

                this.btnVoice.Enabled = !blnShow;

                //this.btnVoiceOK.Visible = false;
                //this.btnVoiceCancel.Visible = false;
                //this.picVoiceGif.Visible = false;
            }
        }

        //private void StopTimerVoIP()
        //{
        //    if (this.mTimerVoIP != null)
        //        this.mTimerVoIP.Stop();
        //}

        //private void StartTimerVoIP()
        //{
        //    if (this.mTimerVoIP == null)
        //    {
        //        this.mTimerVoIP = new System.Timers.Timer();
        //        this.mTimerVoIP.Interval = 1000;
        //        this.mTimerVoIP.Elapsed += OnTimerVoIP_Elapsed;
        //    }

        //    this.mVoIPTime = 0;
        //    this.mTimerVoIP.Start();
        //}

        //private void OnTimerVoIP_Elapsed(object sender, System.Timers.ElapsedEventArgs e)
        //{
        //    this.mVoIPTime += 1;
        //    this.SetVoIPTime(this.mVoIPTime);
        //}

        private delegate void SetVoIPTimeHanlder(long secondPass);
        public void SetVoIPTime(long secondPass)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new SetVoIPTimeHanlder(this.SetVoIPTime), secondPass);
            }
            else
            {
                this.lblVoIPTime.skText = Utility.FormatDuration(secondPass, true, true).ToString();
            }
        }

        private delegate void ShowPanelVoIPHanlder(ViewType type);
        public void ShowPanelVoIP(ViewType type)
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new ShowPanelVoIPHanlder(this.ShowPanelVoIP), type);
            }
            else
            {
                this.mViewType = type;

                if (this.mViewType == ViewType.VoIPTime)
                {
                    this.SetVoIPTime(0);
                    //this.StartTimerVoIP();
                    this.btnVoIP.skIcon = MyResource.GetImage("rs_reject_4.png");
                    this.btnVoIP.skToolTip = this.Language.GetString("rs_end_speech");
                }
                else
                {
                    this.SetVoIPTime(0);
                    //this.StopTimerVoIP();
                    this.btnVoIP.skIcon = MyResource.GetImage("chat_btn_4_phone.png");
                    this.btnVoIP.skToolTip = this.Language.GetString("RemoteSupport.Button.Voip");
                }

                switch (type)
                {
                    case ViewType.VoIPTime:
                        this.lblVoIPTime.Visible = (type == ViewType.VoIPTime);
                        this.pnlVoIP.Visible = true;

#if MAC
                        //this.pnlVoIP.Height = this.pnlBottom.Top - this.pnlVoIP.Top;                     
                        this.pnlBrowser.Frame = new Rectangle(0, this.pnlVoIP.Bottom, this.Width, this.Height - this.skTitleBarHeight - this.pnlVoIP.Bottom);
#else
                        this.pnlVoIP.Height = this.pnlBottom.Top - this.pnlVoIP.Top + 1; // 这里的+1是因为pnlVoIP计算出来的高度会与pnlBottom接合时差一个像素                        
                        this.pnlBrowser.Height = this.pnlVoIP.Top - this.pnlBrowser.Top;
#endif

                        this.pnlVoIP.BringToFront();
                        break;

                    case ViewType.None:
                    default:
                        this.pnlVoIP.Visible = false;
                        this.pnlVoIP.Size = new Size(this.pnlBottom.Width, MyDpi.ToDPI(24, this.Dpi));

#if MAC
                        this.pnlVoIP.Location = new Point(this.pnlBottom.Left, this.pnlBottom.Bottom);
                        this.pnlBrowser.Frame = new Rectangle(0, this.pnlBottom.Bottom, this.Width, this.Height - this.skTitleBarHeight - this.pnlBottom.Bottom);
#else
                        this.pnlVoIP.Location = new Point(this.pnlBottom.Left, this.pnlBottom.Top - this.pnlVoIP.Height);
                        this.pnlBrowser.Height = this.pnlVoIP.Top - this.pnlBrowser.Top;
                        this.pnlBrowser.Height = this.pnlBottom.Top - this.pnlBrowser.Top;
#endif

                        this.pnlVoIP.Anchor = AnchorStyles.Left | AnchorStyles.Bottom | AnchorStyles.Right;
                        this.pnlVoIP.skBorderType = skBorderType.None;
                        this.pnlVoIP.skBorderStrokeColor = this.mColorSplit;
                        this.pnlVoIP.skBorderPadding = new Padding(0, 0, 0, 0);
                        this.lblVoIPTime.skBorderType = skBorderType.None;


                        break;
                }
                this.pnlBrowser.Height -= 10;

                PageHelper.RenderView(this.mBrowser, this.pnlBrowser.Size);
            }
        }

        public void SetBtnVoIPHandle(bool visible)
        {
            this.btnVoIP.Visible = visible;
        }

        protected virtual bool CheckSendFile()
        {
            bool blnResult = true;

            if (RsAPI.DeviceAuthInfo != null && RsAPI.IsMan() && RsAPI.DeviceAuthInfo.attended_prohibition_file_transfer_func == rsDeviceActionType.Forbit)
                blnResult = false;

            return blnResult;
        }

        private void OnFileUploadCompleted(object sender, FileUploadTask e)
        {
            if (!RecordMgr.DictUpload.ContainsKey(e.TaskID))
                return;

            ThreadMgr.Start(() =>
            {
                bool blnResult = false;
                long recordID = RecordMgr.DictUpload[e.TaskID];
                int duration = 0;

                //更新文件状态
                RecordFileState state = (e.IsCompleted ? RecordFileState.WaitingResult : RecordFileState.Failed);


                if (this.mDictRecord.ContainsKey(recordID))
                {
                    RecordTask record = this.mDictRecord[recordID];
                    duration = (int)Convert.ToInt64(record.Duration);

                    if (record.Content != null)
                    {
                        if (record.Content.state == RecordFileState.Cancel.ToStr())
                            state = RecordFileState.Cancel;

                        //更新文件状态到数据库
                        record.Content.state = state.ToStr();
                        RecordMgr.UpdateRecord(record);
                    }
                }

                PageHelper.SetFileState(this.mBrowser, recordID, state);

                if (this.mSocket != null)
                {
                    blnResult = this.mSocket.SendRsFileExt(this.mDevice, e, 10);
                }
                else
                {
                    JsonObject json = new JsonObject();
                    json.Add("duration", duration);
                    json.Add("fileUploadTask", MyJson.SerializeToJsonString(e));
                    string strInitPara = JsonParser.SaveString(json);

                    RSEvent msg = new RSEvent();
                    msg.EventType = RSEventType.File;
                    msg.EventResult = strInitPara;
                    msg.TargetDevice = this.mDevice;
                    msg.EventID = RSEvent.NewEventID();

                    blnResult = SocketMgr.SendMsgFromClientHandle(msg, 10);

                    //blnResult = SocketMgr.SendMsgFromClient(RSEventType.File, json, true, 10, this.mDevice);
                }

                if (DEBUG)
                {
                    blnResult = true;
                    RecordTask record = this.mDictRecord[recordID];
                    this.OnSocketCallback(this, RecordMgr.CreateTestRsFile(e, record.Duration));
                }

                state = blnResult ? RecordFileState.Successed : RecordFileState.Failed;

                this.SetFileState(recordID, state);

                if (this.mDictRecord.ContainsKey(recordID))
                {
                    RecordTask record = this.mDictRecord[recordID];
                    if (record.Content != null)
                    {
                        if (record.Content.state == RecordFileState.Cancel.ToStr())
                            state = RecordFileState.Cancel;

                        //更新文件状态到数据库
                        record.Content.state = state.ToStr();
                        RecordMgr.UpdateRecord(record);
                    }
                }

            });
        }

        public void btnVoIP_Click(object sender, EventArgs e)
        {
            if (this.mViewType == ViewType.VoIPTime)
            {
                this.btnVoIPOver_Click(null, null);
            }
            else
            {
                if (this.IsWaveInterop())
                {
                    //请插入麦克风
                    RsCommon.ShowSplashBox(this, this.Language.GetString("RemoteSupport.Message.VoIPWhileRecording"));
                    //skSplashBox.Show(this.Language.GetString("RemoteSupport.Message.VoIPWhileRecording"), this);
                    return;
                }
                if (WaveInterop.IsDisabledMicrophone())
                {
                    //没有麦克风权限
                    RsCommon.ShowSplashBox(this, this.Language.GetString("Cast_Microphone_Permissions_Not_Open"));
                    //skSplashBox.Show(this.Language.GetString("RemoteSupport.Message.NoMicrophone"), this);
                    return;
                }

                if (!WaveInterop.HasMicrophone())
                {
                    //请插入麦克风
                    RsCommon.ShowSplashBox(this, this.Language.GetString("Cast_Microphone_Permissions_Not_Open"));
                    //skSplashBox.Show(this.Language.GetString("RemoteSupport.Message.NoMicrophone"), this);
                    return;
                }

                this.ShowPanelVoIP(ViewType.VoIPLink);
                this.Callback?.Invoke(skVoIPStatus.Call);
            }
        }

        private void btnSelectFile_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog ofd = new OpenFileDialog())
            {
                ofd.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);
                ofd.Filter = "(所有文件)*.*|*.*";
                ofd.Multiselect = true;

                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    //string dirTest = @"F:\Users\Documents\Tencent Files\271038889\FileRecv";
                    //if (Directory.Exists(dirTest))
                    //{
                    //    this.UploadFile(Path.Combine(dirTest, "Screenrecording_20220107_141551.mp4"));
                    //    //this.UploadFile(Path.Combine(dirTest, "平安厦门建设宣传图.jpg"));
                    //    //this.UploadFile(Path.Combine(dirTest, "1_1_1_4_全国文化统计直报系统移动App企业用户使用手册v1.1.pdf"));
                    //    //this.UploadFile(Path.Combine(dirTest, "”三非“关J词第二批.docx"));
                    //    //this.UploadFile(Path.Combine(dirTest, "PaoMo.mp3"));
                    //}
                    //else
                    {
                        this.UploadFile(ofd.FileNames);
                    }
                }
            }
        }

        private string GetSendText()
        {
#if MAC
            string strText = this.rtbContent.skText;
#else
            string strText = this.rtbContent.textBox.Text;
#endif
            return strText;
        }

        private void OnRtbContent_TextChanged(object sender, EventArgs e)
        {
            string strText = this.GetSendText();
            btnSend.Enabled = (!string.IsNullOrEmpty(strText.Replace("\r\n", "")));
        }


        private void ClearText()
        {
#if MAC
            this.rtbContent.skText = "";
            //this.rtbContent.Focus();
#else
            this.rtbContent.textBox.Clear();
            this.rtbContent.textBox.Focus();
#endif

            //this.btnSend.Enabled = false;
        }

        private void btnSend_Click(object sender, EventArgs e)
        {
            string strText = this.GetSendText();

            this.ClearText();

            if (!string.IsNullOrEmpty(strText.Replace("\r\n", "").TrimEnd(' ')))
            {
                string msgSend = strText;

                msgSend = msgSend.Replace("\r\n", "\n");
                SendRsText(msgSend);
            }
            else
            {
                RsCommon.ShowSplashBox(this, this.Language.GetString("File.Message.EmptyMessage"));
                //skSplashBox.Show(this.Language.GetString("File.Message.EmptyMessage"), this);
            }
        }

        private void btnVoIPCancel_Click(object sender, EventArgs e)
        {
            this.Callback?.Invoke(skVoIPStatus.Cancel);
            //this.ShowPanelVoIP(ViewType.None);
        }

        private void btnVoIPOver_Click(object sender, EventArgs e)
        {
            this.Callback?.Invoke(skVoIPStatus.Finish);
            //this.ShowPanelVoIP(ViewType.None);
        }

        private void btnVoice_Click(object sender, EventArgs e)
        {

            if (!WaveInterop.IsDisabledMicrophone()/* && !IsDebugMode*/)
            {
                //请插入麦克风
                RsCommon.ShowSplashBox(this, this.Language.GetString("Cast_Microphone_Permissions_Not_Open"));
                //skSplashBox.Show(this.Language.GetString("Cast_Microphone_Permissions_Not_Open"), this);
                return;
            }

            if (!WaveInterop.HasMicrophone() /*&& !IsDebugMode*/)
            {
                //请插入麦克风
                RsCommon.ShowSplashBox(this, this.Language.GetString("Cast_Microphone_Permissions_Not_Open"));
                //skSplashBox.Show(this.Language.GetString("Cast_Microphone_Permissions_Not_Open"), this);
                return;
            }

            if (this.mViewType == ViewType.VoIPTime)
            {
                //请插入麦克风
                RsCommon.ShowSplashBox(this, this.Language.GetString("RemoteSupport.Message.InRecording"));
                //skSplashBox.Show(this.Language.GetString("RemoteSupport.Message.InRecording"), this);
                return;
            }

            if (this.mViewType != ViewType.None)
            {
                RsCommon.ShowSplashBox(this, this.Language.GetString("RemoteSupport.Message.InRecording"));
                //skSplashBox.Show(this.Language.GetString("RemoteSupport.Message.InRecording"), this);
                return;
            }

            if (this.pnlVoice.Visible)
            {
                this.ShowPanelVoice(false);
                return;
            }

            if (!this.mHasShowTip)
            {
                this.Callback?.Invoke(RecordTipType.Voice);
                this.mHasShowTip = true;

                //开始录制语音消息，最多可录制60秒，点击“√”结束录制
                this.AddCommonTimeTip(RecordTipType.Voice);
            }

            //关闭程序正在释放的声音
            AudioPlayerMgr.StopAll();

            //显示录音面板
            this.ShowPanelVoice(true);

            string fileExt = ".wav";
#if MAC
            fileExt = ".aac";
#endif

            //生成语音保留文件路径
            this.mVoicePath = RecordMgr.GetFilePath(this.mDevice.unique_id, skSocketFileType.Voice, fileExt);

            //开启录音
            this.mVoiceRecord = WaveInterop.StartRecording(this.mVoicePath, 60, this.OnVoiceCompleted);
        }

        public bool IsWaveInterop()
        {
            return this.pnlVoice.Visible;
        }

        private void btnVoiceOK_Click(object sender, EventArgs e)
        {
            //隐藏录音面板
            this.ShowPanelVoice(false);

            //停止录音
            WaveInterop.StopRecording(this.mVoiceRecord, false);
        }

        private void btnVoiceCancel_Click(object sender, EventArgs e)
        {
            //隐藏录音面板
            this.ShowPanelVoice(false);

            //停止录音
            WaveInterop.StopRecording(this.mVoiceRecord);
        }

        private void OnVoiceCompleted(object sender, WaveRecord e)
        {
            //取消录制
            if (e.IsCancel)
                return;

            if (e.Duration < 1)
            {
                RsCommon.ShowSplashBox(this, this.Language.GetString("RemoteSupport.Message.VoiceTooShort"));
                //skSplashBox.Show(this.Language.GetString("RemoteSupport.Message.VoiceTooShort"), this);
                return;
            }

            //达到最大录制时间
            if (e.IsReachMaxRecordTime)
            {
                RsCommon.ShowSplashBox(this, this.Language.GetString("rs_voicemessage_autosend"));
                //skSplashBox.Show(this.Language.GetString("rs_voicemessage_autosend"), this);
                this.ShowPanelVoice(false);
            }

            //上传语音
            this.UploadFile(this.mVoicePath, true, e.Duration);
        }

        private void rtbContent_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter && !e.Shift)
            {
                e.SuppressKeyPress = true;
                e.Handled = true;
                this.btnSend_Click(null, null);
            }
            else if (e.KeyCode == Keys.Enter && e.Shift)
            {
                e.SuppressKeyPress = true;
                e.Handled = true;

#if MAC
                NSRange selectedRange = this.rtbContent.SelectedRange;//.CurrentEditor.SelectedRange; 
                this.rtbContent.StringValue = this.rtbContent.StringValue.Insert((int)selectedRange.Location, "\n");
                //this.rtbContent.CurrentEditor.SelectedRange = new NSRange(selectedRange.Location + 1, 0);
                this.rtbContent.SelectedRange = new NSRange(selectedRange.Location + 1, 0);
#endif
            }
        }

        private void rtbContent_DragEnter(object sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.Copy;
        }

        private void rtbContent_OnPaste(object sender, EventArgs e)
        {
            //try
            //{
            //    string content = Clipboard.GetText();

            //    if (content.Length < 5000)
            //    {
            //        rtbContent.SelectedText = content;
            //    }
            //    else
            //    {
            //        this.Callback?.Invoke("Paste");
            //    }
            //}
            //catch (Exception)
            //{

            //}
        }

        public void PasteHandle()
        {
            //if (this.InvokeRequired)
            //{
            //    this.BeginInvoke(new ThreadStart(PasteHandle));
            //    return;
            //}
            //else
            //{
            //    try
            //    {
            //        string content = Clipboard.GetText();
            //        string dirSave = Path.Combine(RecordMgr.CacheFolder, "Cache");
            //        string filepath = Path.Combine(dirSave, Guid.NewGuid().ToString("N") + ".txt");
            //        Folder.CheckFolder(dirSave);
            //        using (FileStream fileStream = new FileStream(filepath, FileMode.OpenOrCreate, FileAccess.ReadWrite))
            //        {
            //            byte[] bs = Encoding.Default.GetBytes(content);
            //            fileStream.Write(bs, 0, bs.Length);
            //            fileStream.Flush();
            //            fileStream.Close();
            //        };

            //        this.UploadFile(filepath);
            //    }
            //    catch (Exception ex)
            //    {
            //    }
            //}
        }
    }
}
