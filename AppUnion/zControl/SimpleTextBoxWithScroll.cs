﻿using iTong.CoreModule;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static iTong.CoreModule.MyAPI;

namespace iTong.AppUnion.zControl
{
    public class SimpleTextBox : TextBox
    {
        public delegate void NoticeMouseWheelDelegate();
        public event NoticeMouseWheelDelegate NoticeMouseWheelEvent;

        protected override void WndProc(ref Message m)
        {
            base.WndProc(ref m);

            if (m.Msg == (int)SK_WindowMessage.WM_MOUSEWHEEL)
            {
                // 处理鼠标滚轮事件
                if (NoticeMouseWheelEvent != null)
                {
                    NoticeMouseWheelEvent();
                }
            }
        }
    }

    public class SimpleTextBoxWithScroll : skPanel
    {
        public SimpleTextBox textBox;
        public skVerticalScrollbar scrollBar;

        SK_SCROLLINFO scrollInfo = new SK_SCROLLINFO();

        // 当没有滚动条时保持的宽度
        public int textBoxKeepWidth = 0;

        // 隐藏原生滚动条所需的宽度
        public int hideNativeScrollWidth = SystemInformation.VerticalScrollBarWidth;

        // 当出现滚动条时输入框需要调整的宽度
        public int adjustTextBoxWidthWhenShowScroll = 8;

        // 上一次滚动条刻度
        public int beforeScaleValue = 0;

        public Font skFont
        {
            get { return textBox.Font; }
            set
            {
                if (textBox != null)
                    textBox.Font = value;
            }
        }

        public SimpleTextBoxWithScroll()
        {
            this.Size = new Size(200, 40);
            this.skBackgroundColor = Color.White;

            textBox = new SimpleTextBox
            {
                Multiline = true,
                WordWrap = true,
                ScrollBars = ScrollBars.Vertical
            };
            textBox.BackColor = this.skBackgroundColor;
            textBox.BorderStyle = BorderStyle.None;
            textBox.Size = new Size(this.Size.Width + hideNativeScrollWidth, this.Size.Height);
            textBoxKeepWidth = textBox.Width;
            textBox.Location = new Point(0, 0);
            textBox.NoticeMouseWheelEvent += TextBox_NoticeMouseWheelEvent;
            textBox.TextChanged += TextBox_TextChanged;

            scrollBar = new skVerticalScrollbar
            {
                skSmallChange = 1,
                skLargeChange = 5,
                skMaxValue = 10,
                Visible = false,
            };

            scrollBar.Location = new Point(this.Size.Width - adjustTextBoxWidthWhenShowScroll, 0);
            scrollBar.Size = new Size(adjustTextBoxWidthWhenShowScroll, this.Size.Height);
            scrollBar.ValueChanged += OnScrollValueChanged;

            this.Controls.Add(textBox);
            this.Controls.Add(scrollBar);
        }

        /// <summary>重新计算宽度</summary>
        public void ReComputeWidth()
        {
            textBox.Size = new Size(this.Size.Width + hideNativeScrollWidth, this.Size.Height);
            textBoxKeepWidth = textBox.Width;

            scrollBar.Location = new Point(this.Size.Width - adjustTextBoxWidthWhenShowScroll, 0);
            scrollBar.Size = new Size(adjustTextBoxWidthWhenShowScroll, this.Size.Height);
        }

        /// <summary>文本改变事件</summary>
        private void TextBox_TextChanged(object sender, EventArgs e)
        {
            // 获取文本行数
            int lineCount = textBox.GetLineFromCharIndex(textBox.Text.Length) + 1;
            if (lineCount > 2)
            {
                // 如果此时宽度等于保持宽度说明需要退8个像素给接下来的滚动条腾出位置
                if (textBox.Width == textBoxKeepWidth)
                    textBox.Width -= adjustTextBoxWidthWhenShowScroll;
                scrollBar.BringToFront();
                TextBoxSlide();
            }
            else
            {
                scrollBar.SendToBack();

                // 如果此时宽度小于保持宽度说明需要进8个像素占住滚动条隐藏的位置
                if (textBox.Width < textBoxKeepWidth)
                    textBox.Width += adjustTextBoxWidthWhenShowScroll;
            }
        }

        /// <summary>鼠标在输入框滚动事件</summary>
        private void TextBox_NoticeMouseWheelEvent()
        {
            if (!this.scrollBar.Visible)
                return;

            TextBoxSlide();
        }

        /// <summary>鼠标在滚动条拉动</summary>
        private void OnScrollValueChanged(object sender, EventArgs e)
        {
            //double diff = scrollBar.skValue - this.scrollBar.skValueOld;

            /*
             * 思路：
             * 1、总的移动长度 / 总行数 = 每一段长度的间距
             * 2、当前的value / 每段间距 = 此时value是几倍于间距
             * 3、此时位置与上次的作差若差值大于等于1说明需要移动
             */

            // 此时value值
            double currentValue = Math.Round(scrollBar.skValue, 2);
            // 总行数
            int lineCount = textBox.GetLineFromCharIndex(textBox.Text.Length) + 1;
            // 每段间距
            double span = Math.Round((scrollBar.skMaxValue - scrollBar.skMinValue) / lineCount, 2); // 保留两位小数
            // 当前处于几倍间距的位置
            int currentLocation = (int)(currentValue / span);

            // 去掉最大刻度值
            if (currentLocation != lineCount)
            {
                // 若当前位置与上一次不同
                if (currentLocation != beforeScaleValue)
                {
                    // 跨度
                    int distance = currentLocation - beforeScaleValue;
                    MyAPI.SendMessage(textBox.Handle, (int)SK_WindowMessage.EM_LINESCROLL, 0, distance);
                    beforeScaleValue = currentLocation;
                }
            }

            //int result = 0;
            //if (diff > 0)
            //{
            //    result = (int)Math.Ceiling(diff);
            //}
            //else
            //{
            //    result = (int)Math.Floor(diff);
            //}
            //MyAPI.SendMessage(textBox.Handle, (int)SK_WindowMessage.EM_LINESCROLL, 0, result);
        }

        /// <summary>滚动条赋值事件</summary>
        private void TextBoxSlide()
        {
            scrollInfo.cbSize = 0;
            scrollInfo.fMask = 0;
            scrollInfo.nMin = 0;
            scrollInfo.nMax = 0;
            scrollInfo.nPage = 0;
            scrollInfo.nPos = 0;
            scrollInfo.nTrackPos = 0;

            scrollInfo.cbSize = Marshal.SizeOf(scrollInfo);
            scrollInfo.fMask = (int)SK_ScrollInfoMask.SIF_ALL;

            int result = MyAPI.GetScrollInfo(this.textBox.Handle, SK_ScrollBarDirection.SB_VERT, ref scrollInfo);

            if (result == 1)
            {
                scrollBar.Visible = (scrollInfo.nMax >= scrollInfo.nPage);
                scrollBar.skMinValue = scrollInfo.nMin;
                scrollBar.skMaxValue = scrollInfo.nMax;
                scrollBar.skLargeChange = scrollInfo.nPage;
                scrollBar.skSmallChange = 1;

                scrollBar.SetValueWithNoEvent(scrollInfo.nPos);
            }
        }
    }
}
