﻿using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iTong.Android
{
    /// <summary>
    /// 用于RS的事件类型
    /// </summary>
    public enum RSEventType
    {
        /// <summary>
        /// 未指定错误
        /// </summary>
        None,

        /// <summary>
        /// 检查更新
        /// </summary>
        CheckUpdate,

        /// <summary>
        /// 日志
        /// </summary>
        PrintLog,

        /// <summary>
        /// 查询9位码当前状态
        /// </summary>
        ShareCodeNew,

        /// <summary>
        /// 9位码加载成功
        /// </summary>
        ShareCodeNewSuccessed,

        /// <summary>
        /// 9位码加载失败
        /// </summary>
        ShareCodeNewFailed,

        /// <summary>
        /// 9位码加载成功，但小数据URL加载失败
        /// </summary>
        ShareCodeNewFailedForwardUrlEmpty,

        /// <summary>
        /// 9位码加载异常
        /// </summary>
        ShareCodeNewException,

        /// <summary>
        /// 设备Token刷新
        /// </summary>
        DeviceTokenUpdate,

        /// <summary>
        /// 设备Token过期
        /// </summary>
        DeviceTokenExpired,

        /// <summary>
        /// 设备重新绑定
        /// </summary>
        DeviceRebind,

        /// <summary>
        /// 本地设置更新
        /// </summary>
        LocalSettingUpdate,

        /// <summary>
        /// 暂停或者恢复画面
        /// </summary>
        DebugClick,

        /// <summary>
        /// 同步剪切板信息
        /// </summary>
        SyncClipboard,

        /// <summary>
        /// 同步屏幕信息
        /// </summary>
        SyncDisplaySetting,

        /// <summary>
        /// 远程控制状态变化
        /// </summary>
        ControlChange,

        /// <summary>
        /// 设备Token刷新失败
        /// </summary>
        DeviceTokenUpdateFailed,

        /// <summary>
        /// 设备安全设置
        /// </summary>
        DeviceAuth,

        /// <summary>
        /// 设备摘要信息
        /// </summary>
        DeviceSummary,

        /// <summary>
        /// 小数据正常开启
        /// </summary>
        SocketOpen,

        /// <summary>
        /// 小数据异常断开
        /// </summary>
        SocketClose,

        /// <summary>
        /// 本地Socket监听断开
        /// </summary>
        SocketListenDisconnect,

        /// <summary>
        /// 部署设备
        /// </summary>
        DeviceDeploy,

        /// <summary>
        /// 设备解绑成功
        /// </summary>
        DeviceUndeploySuccessed,

        /// <summary>
        /// 设备解绑失败
        /// </summary>
        DeviceUndeployError,

        /// <summary>
        /// 设备解绑失败，参数错误
        /// </summary>
        DeviceUndeployErrorByPara,

        /// <summary>
        /// 设备解绑失败，密码错误
        /// </summary>
        DeviceUndeployErrorByPwd,

        /// <summary>
        /// 有人、无人值守切换成功
        /// </summary>
        DeviceTypeUpdateSuccessed,

        /// <summary>
        /// 有人、无人值守切换失败，参数错误
        /// </summary>
        DeviceTypeUpdateErrorByPara,

        /// <summary>
        /// 有人、无人值守切换失败，密码错误
        /// </summary>
        DeviceTypeUpdateErrorByPwd,

        /// <summary>
        /// 有人、无人值守切换失败，需要升级订单
        /// </summary>
        DeviceTypeUpdateErrorByVipLevelLimit,

        /// <summary>
        /// 有人、无人值守切换失败，需要加购设备数据
        /// </summary>
        DeviceTypeUpdateErrorByVipDeviceNumberLimit,

        /// <summary>
        /// 有人、无人值守切换失败，试用设备数量达到上限
        /// </summary>
        DeviceTypeUpdateErrorByFreeDeviceNumberLimit,

        /// <summary>
        /// 退出程序
        /// </summary>
        ExitApp,

        /// <summary>
        /// 关闭此连接的所有相关弹窗
        /// </summary>
        CloseWindows,

        /// <summary>
        /// 关闭用户界面进程
        /// </summary>
        CloseUserDisplay,

        /// <summary>
        /// remote_support/heartbeat 
        /// 端间心跳超时
        /// </summary>
        HeartBeatError,

        /// <summary>
        /// remote_support/security_auth
        /// 连接安全码、临时密码
        /// </summary>
        SecurityAuth,

        /// <summary>
        /// remote_support/security_auth
        /// 连接安全码、临时密码成功
        /// </summary>
        SecurityAuthSuccessed,

        /// <summary>
        /// remote_support/security_auth
        /// 连接安全码、临时密码失败
        /// </summary>
        SecurityAuthFailed,

        /// <summary>
        /// remote_support/security_auth_result
        /// 同步安全码状态
        /// </summary>
        SecurityAuthResult,

        /// <summary>
        /// remote_support/connect
        /// 请求连接
        /// </summary>
        Connect,

        /// <summary>
        /// remote_support/connect : code
        /// 请求连接被拒绝
        /// </summary>
        ConnectRefuse,

        /// <summary>
        /// remote_support/connect : code
        /// 连接被占用， 一般是投屏到手机端容易出现此情况
        /// </summary>
        ConnectOccupied,

        /// <summary>
        /// remote_support/connect : code
        /// 请求连接超时，60秒未响应
        /// </summary>
        ConnectTimeout,

        /// <summary>
        /// remote_support/connect : code
        /// 未登录或已登录帐号没有VIP权限，只能进行本地投屏，主要靠网域网IP的ping情况
        /// </summary>
        ConnectLocalOnly,

        /// <summary>
        /// remote_support/connect : code
        /// 达到同时连接的最大数量
        /// </summary>
        ConnectMaxConcurrent,


        /// <summary>
        /// remote_support/connect : unattend_error_code
        /// 插件未下载
        /// </summary>
        ConnectAddonNotDownload,

        /// <summary>
        /// remote_support/connect : unattend_error_code
        /// 插件未打开
        /// </summary>
        ConnectAddonNotOpen,

        /// <summary>
        /// remote_support/connect : unattend_error_code
        /// 无截屏权限
        /// </summary>
        ConnectNoScreenPermission,

        /// <summary>
        /// remote_support/connect : unattend_error_code
        /// 屏幕锁屏
        /// </summary>
        ConnectScreenLocked,

        /// <summary>
        /// remote_support/connect : unattend_error_code
        /// 禁止组织外的远程协助
        /// </summary>
        ConnectForbidExternalAccess,

        /// <summary>
        /// remote_support/connect : unattend_error_code
        /// 插件版本过低
        /// </summary>
        ConnectAddonVersionTooLow,

        /// <summary>
        /// remote_support/connect : unattend_error_code
        /// 没有浮层权限
        /// </summary>
        ConnectNoOverlayPermision,


        /// <summary>
        /// remote_support/set_config
        /// 设置mqtt连接参数
        /// </summary>
        SetConfig,


        /// <summary>
        /// remote_support/disconnect 
        /// 断开连接
        /// </summary>
        Disconnect,

        /// <summary>
        /// 断开连接（创建RTC发生错误）
        /// </summary>
        DisconnectError,

        /// <summary>
        /// remote_support/text
        /// 发送文本
        /// </summary>
        Text,

        /// <summary>
        /// remote_support/tip
        /// 发送Tip（用于服务端记录状态）
        /// </summary>
        Tip,

        /// <summary>
        /// remote_support/file
        /// 发送文件
        /// </summary>
        File,


        /// <summary>
        /// remote_support/request_screen_share
        /// RS有人值守专用：发送屏幕共享请求
        /// </summary>
        ScreenShareRequest,

        /// <summary>
        /// remote_support/request_ar_camera
        /// RS有人值守专用：发送开启相机请求
        /// </summary>
        CameraOpen,

        /// <summary>
        /// remote_support/close_ar_camera
        /// RS有人值守专用：发送关闭相机请求
        /// </summary>
        CameraClose,


        /// <summary>
        /// remote_support/webrtc_status : status_code
        /// ICE连接状态
        /// </summary>
        RtcStatus,

        /// <summary>
        /// remote_support/webrtc_status : status_code
        /// ICE未连接失败
        /// </summary>
        RtcStatusFailure,

        /// <summary>
        /// remote_support/webrtc_status : status_code
        /// ICE未连接成功，中途取消连接
        /// </summary>
        RtcStatusCancel,



        /// <summary>
        /// remote_support/broadcast : code
        /// 广播状态
        /// </summary>
        Broadcast,


        /// <summary>
        /// remote_support/broadcast : code
        /// 停止传输画面，注意并非断开连接
        /// </summary>
        BroadcastClose,

        /// <summary>
        /// remote_support/broadcast : code
        /// 开始传输画面
        /// </summary>
        BroadcastOpen,

        /// <summary>
        /// remote_support/broadcast : code
        /// 暂停传输画面
        /// </summary>
        BroadcastPause,

        /// <summary>
        /// remote_support/broadcast : code
        /// 恢复传输画面，与pause对应使用
        /// </summary>
        BroadcastResume,

        /// <summary>
        /// remote_support/broadcast : code
        /// 无法获取画面数据
        /// </summary>
        BroadcastNoData,

        /// <summary>
        /// remote_support/broadcast : code
        /// app进入后台，iOS进入后台无法更新相机画面需要通知控制端
        /// </summary>
        BroadcastEnterBackground,

        /// <summary>
        /// remote_support/broadcast : code
        /// app进入前台，iOS进入前台恢复更新相机画面需要通知控制端
        /// </summary>
        BroadcastEnterForeground,

        /// <summary>
        /// remote_support/broadcast : code
        /// 通知控制端，可以让用户放置箭头了
        /// </summary>
        BroadcastARTouchReady,

        /// <summary>
        /// remote_support/broadcast : code
        /// 通知控制端，告诉用户点击的位置距离相机太近了
        /// </summary>
        BroadcastARTouchTooClose,

        /// <summary>
        /// remote_support/broadcast : code
        /// 无法找到匹配的特征点，请用户移动设备以检测更多的特征点
        /// </summary>
        BroadcastARTouchHitTestFailed,

        /// <summary>
        /// remote_support/broadcast : code
        /// AR 隐私弹窗用户点拒绝
        /// </summary>
        BroadcastARPrivacyRefused,

        /// <summary>
        /// remote_support/broadcast : code
        /// 控制端接收到第一帧画面，有些端需要收到这个消息才能显示工具栏
        /// </summary>
        BroadcastReceivedFirstFrame,

        /// <summary>
        /// remote_support/broadcast : code
        /// 控制端登出账户通知
        /// </summary>
        BroadcastLogout,

        /// <summary>
        /// remote_support/broadcast : code
        /// 控制端Vip变化
        /// </summary>
        BroadcastVipCheck,
        

        /// <summary>
        /// remote_support/voip : type
        /// VoIP（语音通话）
        /// </summary>
        VoIP,

        /// <summary>
        /// remote_support/voip : type
        /// 主动呼叫
        /// </summary>
        VoIPCall,

        /// <summary>
        /// remote_support/voip : type
        /// 取消呼叫（已取消实时语音）-记入历史聊天
        /// </summary>
        VoIPCancel,

        /// <summary>
        /// remote_support/voip : type
        /// 接通语音（实时语音已开启）
        /// </summary>
        VoIPAccept,

        /// <summary>
        /// remote_support/voip : type
        /// 拒绝语音（对方已拒绝实时语音）-记入历史聊天
        /// </summary>
        VoIPRefuse,

        /// <summary>
        /// remote_support/voip : type
        /// 结束语音（通话时长 00:20:15）-记入历史聊天
        /// </summary>
        VoIPFinish,

        /// <summary>
        /// remote_support/voip : type
        /// 呼叫超时，发起人发起语音通话后设置60秒超时，超时后发送此消息(对方无应答) -记入历史聊天
        /// </summary>
        VoIPTimedout,

        /// <summary>
        /// remote_support/voip : type
        /// 没有权限
        /// </summary>
        VoIPPermissionDenied,


        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// 麦克风功能不支持
        /// </summary>
        MicrophoneUnsupport,

        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// 麦克风硬件不存在
        /// </summary>
        MicrophoneHardwareNotExists,

        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// 没有麦克风权限
        /// </summary>
        MicrophonePermissionDenied,


        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// VoIP 麦克风状态同步
        /// </summary>
        MicrophoneStatus,

        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// 麦克风打开失败
        /// </summary>
        MicrophoneStatusFailed,

        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// 麦克风打开成功
        /// </summary>
        MicrophoneStatusSuccess,


        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// VoIP 扬声器状态同步
        /// </summary>
        SpeakerStatus,

        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// 扬声器打开失败
        /// </summary>
        SpeakerStatusFailed,

        /// <summary>
        /// remote_support/voip_microphone_status_sync : code
        /// 扬声器打开成功
        /// </summary>
        SpeakerStatusSuccess,



        /// <summary>
        /// remote_support/request_device_info
        /// 获取设备信息请求
        /// </summary>
        DeviceInfoRequest,


        /// <summary>
        /// remote_support/addon_check : code
        /// addon_check消息
        /// </summary>
        AddonCheck,

        /// <summary>
        /// remote_support/addon_status : code
        /// addon_status消息
        /// </summary>
        AddonStatus,

        /// <summary>
        /// remote_support/request_addon : code
        /// 请求开启控制
        /// </summary>
        AddonRequest,

        /// <summary>
        /// remote_support/addon_status : code
        /// remote_support/addon_check : code
        /// 插件准备就绪
        /// </summary>
        AddonReady,

        /// <summary>
        /// remote_support/addon_status : code
        /// remote_support/request_addon : code
        /// remote_support/addon_check : code
        /// 未安装插件
        /// </summary>
        AddonNotInstalled,

        /// <summary>
        /// remote_support/addon_status : code
        /// remote_support/request_addon : code
        /// remote_support/addon_check : code
        /// 插件需要升级
        /// </summary>
        AddonNeedsUpgrad,

        /// <summary>
        /// remote_support/addon_status : code
        /// remote_support/request_addon : code
        /// remote_support/addon_check : code
        /// 插件没有权限
        /// </summary>
        AddonNoPermission,



        /// <summary>
        /// remote_support/set_safe_mode : code
        /// 安全模式插件
        /// </summary>
        SafeModeSet,


        /// <summary>
        /// remote_support/set_safe_mode : code
        /// 安全模式插件程序发生异常
        /// </summary>
        SafeModeSetException,

        /// <summary>
        /// remote_support/set_safe_mode : code
        /// 安全模式插件未被授予辅助权限
        /// </summary>
        SafeModeSetNoAccessibliityPermission,

        /// <summary>
        /// remote_support/set_safe_mode : code
        /// 安全模式插件需要升级
        /// </summary>
        SafeModeSetNeedsUpgrad,

        /// <summary>
        /// remote_support/set_safe_mode : code
        /// 安全模式插件未安装
        /// </summary>
        SafeModeSetNotInstalle,

        /// <summary>
        /// remote_support/set_safe_mode : code
        /// 安全模式插件安装失败
        /// </summary>
        SafeModeSetFailed,

        /// <summary>
        /// remote_support/set_safe_mode : code
        /// 安全模式插件安装成功
        /// </summary>
        SafeModeSetSuccess,


        /// <summary>
        /// remote_support/safe_mode_status : code
        /// 安全模式状态
        /// </summary>
        SafeModeStatus,

        /// <summary>
        /// remote_support/safe_mode_status : code
        /// 安全模式正常开启
        /// </summary>
        SafeModeStatusOpen,

        /// <summary>
        /// remote_support/safe_mode_status : code
        /// 安全模式失效，因為系統限制部分畫面無法處理
        /// </summary>
        SafeModeStatusSystemRestriction,

        /// <summary>
        /// remote_support/safe_mode_status : code
        /// 安全模式失效，請確認輔助權限是否開啟
        /// </summary>
        SafeModeStatusAccessibliityLost,

        /// <summary>
        /// remote_support/safe_mode_status : code
        /// 安全模式关闭
        /// </summary>
        SafeModeStatusClose,

        /// <summary>
        /// remote_support/safe_mode_status : code
        /// 插件被人为卸载
        /// </summary>
        SafeModeStatusAddonHasUninstalled,


        /// <summary>
        /// remote_support/set_wallpaper 
        /// 设置壁纸
        /// </summary>
        WallpaperSet,

        /// <summary>
        /// remote_support/set_wallpaper 
        /// 设置壁纸：不支持
        /// </summary>
        WallpaperSetNotSupport,

        /// <summary>
        /// remote_support/set_wallpaper 
        /// 设置壁纸：失败
        /// </summary>
        WallpaperSetFailed,

        /// <summary>
        /// remote_support/set_wallpaper 
        /// 设置壁纸：成功
        /// </summary>
        WallpaperSetSuccess,



        /// <summary>
        /// remote_support/set_wallpaper_result 
        /// 设置壁纸
        /// </summary>
        WallpaperSetResult,

        /// <summary>
        /// remote_support/set_wallpaper_result 
        /// 设置壁纸：不支持
        /// </summary>
        WallpaperSetResultShow,

        /// <summary>
        /// remote_support/set_wallpaper_result 
        /// 设置壁纸：失败
        /// </summary>
        WallpaperSetResultHide,


        /// <summary>
        /// remote_support/set_display_screen 
        /// 设置分享屏幕索引
        /// </summary>
        DisplayScreenSet,

        /// <summary>
        /// remote_support/set_display_screen 
        /// 设置分享屏幕索引：成功
        /// </summary>
        DisplayScreenSetSuccess,

        /// <summary>
        /// remote_support/display_screen_status 
        /// 同步分享屏幕索引信息
        /// </summary>
        DisplayScreenChanged,



        /// <summary>
        /// remote_support/switch_ime : code
        /// IME插件消息
        /// </summary>
        ImeSwitch,

        /// <summary>
        /// remote_support/switch_ime : code
        /// IME插件程序发生异常
        /// </summary>
        ImeSwitchException,

        /// <summary>
        /// remote_support/switch_ime : code
        /// IME插件未被授予辅助权限
        /// </summary>
        ImeSwitchNoAccessibliityPermission,

        /// <summary>
        /// remote_support/switch_ime : code
        /// IME插件需要升级
        /// </summary>
        ImeSwitchNeedsUpgrad,

        /// <summary>
        /// remote_support/switch_ime : code
        /// IME插件未安装
        /// </summary>
        ImeSwitchNotInstalle,

        /// <summary>
        /// remote_support/switch_ime : code
        /// IME插件安装失败
        /// </summary>
        ImeSwitchFailed,

        /// <summary>
        /// remote_support/switch_ime : code
        /// IME插件安装成功
        /// </summary>
        ImeSwitchSuccess,


        /// <summary>
        /// remote_support/ime_status : code
        /// 自定义键盘消息
        /// </summary>
        ImeStatus,

        /// <summary>
        /// remote_support/ime_status : code
        /// 自定义键盘打开
        /// </summary>
        ImeStatusOpen,

        /// <summary>
        /// remote_support/ime_status : code
        /// 自定义键盘关闭
        /// </summary>
        ImeStatusClose,

        /// <summary>
        /// comm_function : code
        /// 踢出登录
        /// </summary>
        PushLogout,

        /// <summary>
        /// 解绑
        /// </summary>
        RsDeviceUndeploy,

        /// <summary>
        /// 设备名改变
        /// </summary>
        DeviceNameChange,

        /// <summary>
        /// 用户信息变更，需要重新获取用户信息(现在只有订单信息)
        /// </summary>
        UpdateUserInfo,

        /// <summary>
        /// 更新文件传输功能状态
        /// </summary>
        AttendedFileTransfer,

        /// <summary>
        /// 更新远程控制功能状态
        /// </summary>
        AttendedRemoteControl,

        /// <summary>
        /// 有人值守禁止客户端接受来自组织外的远程协助
        /// </summary>
        ChangeAttendedRemoteAssistance,

        /// <summary>
        /// 无人值值守禁止客户端接受来自组织外的远程协助
        /// </summary>
        ChangeUnattendedRemoteAssistance,

        /// <summary>
        /// 关闭无人值守時需輸入密碼
        /// </summary>
        ChangeDeviceCloseUnattendedPasswordVerify,

        /// <summary>
        /// 有人值守强制客户端在每次远程协助结束后自动更换新的九位连接码
        /// </summary>
        ChangeAttendedAutoUpdateShareCode,

        /// <summary>
        /// RS Host 专用
        /// 画面接收成功
        /// </summary>
        ImageRecvSuccess,

        /// <summary>
        /// RS Host 专用 客户端主动发起请求刷新九位码
        /// 画面接收成功
        /// </summary>
        ShowCodeRefresh,

        /// <summary>
        /// RS Host 专用 客户端主动发起请求刷新九位码
        /// 画面接收成功
        /// </summary>
        ShowPwdRefresh,

        /// <summary>
        /// 9位码连接密码加载成功
        /// </summary>
        SharePwdSuccessed,

        /// <summary>
        /// 发送Plus心跳包
        /// </summary>
        SendHeartBeatPlus,

        /// <summary>
        /// 开启语音
        /// </summary>
        OpenVoIP,

        /// <summary>
        /// RS Host 专用 客户端发送密码变更类型（每日更新，手动更新，每次连接后更新）
        /// 画面接收成功
        /// </summary>
        RsPwdRefreshStatu,

        /// <summary>
        /// RS Host 专用 客户端发送密码变更类型（每日更新，手动更新，每次连接后更新）
        /// 断开连接，界面出现断开连接弹窗
        /// </summary>
        RsShowDisConnectBox,

        /// <summary>
        /// 停止通知栏图标闪烁
        /// </summary>
        StopNotifyTimer,

        /// <summary>
        /// MDM注册
        /// </summary>
        MDMEnrollment,

        /// <summary>
        /// MDM注册成功
        /// </summary>
        MDMEnrollmentSuccess,

        /// <summary>
        /// MDM注册失败
        /// </summary>
        MDMEnrollmentError,

        /// <summary>
        /// MDM反注册
        /// </summary>
        MDMUnenrollment,

        /// <summary>
        /// MDM反注册成功
        /// </summary>
        MDMUnenrollmentSuccess,

        /// <summary>
        /// MDM反注册失败
        /// </summary>
        MDMUnenrollmentError,

        /// <summary>
        /// MDM注册绑定设备
        /// </summary>
        MDMDeviceDeploy,

        /// <summary>
        /// RS被控端专用-发送需要锁屏消息给界面
        /// </summary>
        LockScreen,

        /// <summary>
        /// RS被控端专用发送被控端的日志到指定的id里
        /// </summary>
        UploadLog,

        /// <summary>
        /// RS被控端专用发送无人值守验证成功
        /// </summary>
        ConnectCheckSuccess,

        /// <summary>
        /// 重启
        /// </summary>
        ReStart,

        /// <summary>
        /// 获取睡眠时间
        /// </summary>
        SleepTimer,

        /// <summary>
        /// Mqtt信息包
        /// </summary>
        SignalingWrapper,

        /// <summary>
        /// 展示重启提示
        /// </summary>
        ShowReStartTip,

        /// <summary>
        /// MacOS在重启系统的登录界面，通过Proxy启动Video进程
        /// </summary>
        ProxyRunVideo,

        /// <summary>
        /// MacOS在用户登录之后，关闭通过Proxy启动Video进程
        /// </summary>
        ProxyCloseVideo,

        /// <summary>
        /// 展示安装应用弹窗
        /// </summary>
        ShowInstallAppTip,

        /// <summary>
        /// 展示远控Fps的数据
        /// </summary>
        /// 
        ShowFpsData,

        /// <summary>
        /// 永不休眠
        /// </summary>
        NeverSleep,

        #region MCP

        /// <summary>
        /// MCP Client连接成功
        /// </summary>
        McpClientConnect,

        /// <summary>
        /// MCP Client断开连接
        /// </summary>
        McpClientDisconnect,

        /// <summary>
        /// MCP Server连接失败
        /// </summary>
        McpServerError,

        /// <summary>
        /// MCP Server连接成功
        /// </summary>
        McpServerConnect,

        /// <summary>
        /// MCP Server断开连接
        /// </summary>
        McpServerDisconnect,

        /// <summary>
        /// MCP Server添加配置
        /// </summary>
        McpServerAddConfig,

        /// <summary>
        /// MCP Server删除配置
        /// </summary>
        McpServerRemoveConfig,

        /// <summary>
        /// MCP Server更新配置
        /// </summary>
        McpServerUpdateConfig,

        /// <summary>
        /// MCP Server列表
        /// </summary>
        McpServerList,

        /// <summary>
        /// MCP Server获取工具列表
        /// </summary>
        McpServerFecthToolsList,

        /// <summary>
        /// MCP Server获取资源列表
        /// </summary>
        McpServerFecthResourcesList,

        /// <summary>
        /// MCP Server获取资源模板列表
        /// </summary>
        McpServerFecthResourceTemplatesList,

        /// <summary>
        /// MCP Server调用工具
        /// </summary>
        McpServerCallTool,

        /// <summary>
        /// MCP Server读取资源
        /// </summary>
        McpServerReadResource,

        /// <summary>
        /// 更新app
        /// </summary>
        UpdateApp,

        /// <summary>
        /// 卸载app
        /// </summary>
        UnloadApp,


        #endregion

    }
}
