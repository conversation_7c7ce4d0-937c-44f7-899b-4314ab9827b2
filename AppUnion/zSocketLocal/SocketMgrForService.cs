﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Drawing;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Threading;
using System.Drawing.Drawing2D;
using System.Diagnostics;
using System.Text;
using System.ComponentModel;
using System.Net.NetworkInformation;
using System.Net;
using System.Net.Sockets;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Device;
using iTong.Android;


namespace iTong.Android
{
    public partial class SocketMgr
    {
        protected static Thread mThreadUpdataToken;
        protected static Thread mThreadMDMUnenrollmentn;

        /// <summary>
        /// 服务发起socket消息(创建RSEvent发送)
        /// </summary>
        /// <param name="socketType">socket类型</param>
        /// <param name="eventType">RSEvent事件类型</param>
        /// <param name="eventResult">事件结果</param>
        /// <param name="needCallback">是否需要回调</param>
        public static void SendMsgFromService(SocketType socketType, RSEventType eventType, object eventResult = null, bool needCallback = true)
        {
            RSEvent e = new RSEvent();
            e.EventType = eventType;
            e.EventResult = MyJson.SerializeToJsonString(eventResult);

            if (!needCallback)
            {
                e.EventID = RSEvent.NewEventID();
            }

            SendMsgFromService(e, socketType);
        }

        /// <summary>
        /// 服务发起socket消息(需要类型解析)
        /// </summary>
        /// <param name="socketType">socket类型</param>
        /// <param name="msgType">socket消息类型</param>
        /// <param name="msgContent">需要发送的内容</param>
        /// <param name="waitSecond">等待时间</param>
        public static void SendMsgFromServiceByType(SocketType socketType, SocketMsgType msgType, object msgContent = null, int waitSecond = 0)
        {
            SocketHandler socket = null;

            try
            {
                socket = SocketMgr.GetSocket(socketType, false);
                if (socket == null)
                {
                    Log(string.Format("socket is null. socketType={0}, msgType={1}", socketType, msgType));
                    return;
                }

                //发送通用消息
                socket.SendCommonInfo(msgType, msgContent, waitSecond);
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "SendMsgFromService");
            }
        }

        /// <summary>
        /// 服务发起socket消息
        /// </summary>
        /// <param name="e">需要发送的消息</param>
        /// <param name="socketType">socket类型</param>
        /// <param name="autoCreate">是否自动socket</param>
        public static bool SendMsgFromService(RSEvent e, SocketType socketType = SocketType.UserDisplay, bool autoCreate = false)
        {
            bool blnResult = false;

            string errMsg = string.Empty;
            SocketHandler socket = null;

            try
            {
                if (e != null && e.EventType == RSEventType.Connect)
                    autoCreate = true;

                if (!autoCreate)
                    autoCreate = socketType != SocketType.SystemSafeMode;

                if (!ParaMgr.IsService)
                    autoCreate = false;

                socket = SocketMgr.GetSocket(socketType, autoCreate);
                if (socket == null)
                {
                    Log(string.Format("socket is null. Command={0}", e.Command));
                    goto DoExit;
                }

                //发送RSEvent消息
                socket.SendRsEvent(e);

                blnResult = true;
            }
            catch (Exception ex)
            {
                errMsg = ex.Message;

                Common.LogException(ex, "SendMsgFromService");
            }

            DoExit:
            Log(string.Format("SendMsgFromService Command={0}, Result={1}, ErrMsg={2}", e.Command, blnResult, errMsg));

            return blnResult;
        }

        /// <summary>
        /// 退出服务
        /// </summary>
        public static void ExitService()
        {
            //释放已有连接
            RsController.Instance.GetClient(RsOpType.Clear, null);

            Thread.Sleep(1000);

            //退出程序，释放已有连接
            SocketMgr.KillCurrentApp();
        }

        /// <summary>
        /// 解绑设备
        /// </summary>
        /// <param name="rs">RsController对象</param>
        public static void UndeployDevice(RsController rs)
        {
            RsController.Instance.GetClient(RsOpType.Clear, null);
            MyLog.LogFile(string.Format("RecvMsgOnService.DeviceUndeploySuccessed"), "DeployInfo");
            //解绑设备
            RsAPI.ClearDeployInfo();
            RsAPI.StopPush();

            //biz产品解绑不需要去连小数据
            if (!ParaMgr.IsBiz)
            {
                //重新连接设备
                MySocket.OpenSocketForRecv(RsController.Instance.Socket, UserBase.LocalDevice, true);
                ////发送设备类型变化给服务端，用于监控有/无值守状态变更
                rs.SendDeviceTypeChanged();
            }

            rs.StopHeartBeatPlusTimer();
            rs.StopTimer();
        }

        /// <summary>
        /// 对socket进行分类执行
        /// </summary>
        /// <param name="sender">SocketHandler对象</param>
        /// <param name="e">RSEvent对象</param>
        public static void RecvMsgOnService(object sender, RSEvent e)
        {
            try
            {
                SocketHandler socket = null;
                if (sender != null)
                    socket = sender as SocketHandler;

                RsController rs = RsController.Instance;

                int code = -1;
                switch (e.EventType)
                {
                    case RSEventType.ExitApp:
                        //标识为用户主动退出
                        ParaMgr.IsUserClose = true;

                        SocketMgr.LogApp("Service.RecvMsgOnService -> ParaMgr.IsUserClose = true;");

                        ThreadMgr.Start(() =>
                        {
#if MAC
                            KillProcessById(socket.AppInfo.Pid);

#else
                            if (!ParaMgr.IsBiz || ParaMgr.IsServiceDebug)
                            {
                                //释放已有连接、退出程序
                                rs.StopHeartBeatPlusTimer();
                                ExitService();
                            }
#endif
                        });
                        break;

                    case RSEventType.ProxyCloseVideo:
                        SocketMgr.LogApp("RecvMsgOnService.ProxyCloseVideo -> ProxyCloseVideo.");

                        string threadName = "RecvMsgOnService.ProxyCloseVideo";
                        if (ThreadMgr.CheckThreadIsAlive(threadName))
                            return;

                        ThreadMgr.Start(() =>
                        {
#if MAC
                            SocketHandler socketVideo = SocketMgr.GetSocket(SocketType.SystemVideo);

                            SocketMgr.LogApp("RecvMsgOnService.ProxyCloseVideo.StopVideoApp.");

                            //杀掉旧的视频捕获进程
                            rs.StopVideoApp();

                            //等待旧进程退出
                            if (socketVideo != null)
                            {
                                int pid = socketVideo.AppInfo.Pid;
                                int waitCount = 0;

                                while (true)
                                {
                                    waitCount++;
                                    Thread.Sleep(500);

                                    if (!ServiceMgr.CheckCurrentProcessExist(ParaMgr.CommandRunVideo))
                                    {
                                        SocketMgr.LogApp(string.Format("RecvMsgOnService.ProxyCloseVideo.CheckCurrentProcessExist {0} is not exist, pid = {1}", ParaMgr.CommandRunVideo, pid));
                                        break;
                                    }

                                    if (waitCount > 6)
                                    {
                                        SocketMgr.LogApp(string.Format("RecvMsgOnService.ProxyCloseVideo.CheckCurrentProcessExist {0} is exist, KillProcessById -> pid = {1}", ParaMgr.CommandRunVideo, pid));

                                        SocketMgr.KillProcessById(pid);
                                        break;
                                    }
                                }
                            }

                            SocketMgr.LogApp("RecvMsgOnService.ProxyCloseVideo.StartVideoApp.");

                            //重启新的视频捕获进程
                            rs.StartVideoApp(true);
#endif
                        }, threadName);
                        break;

                    case RSEventType.DebugClick:
                        //暂停或者恢复画面
                        rs.DebugClick();
                        break;

                    case RSEventType.CheckUpdate:
                        //检查更新
                        MyUpdate.CheckUpdate(true, true);
                        break;

                    case RSEventType.SyncClipboard:
                        //同步剪切板内容
                        SocketClipboardInfo clipboardInfo = MyJson.DeserializeFromJsonString<SocketClipboardInfo>(e.EventResult);
                        if (clipboardInfo == null || clipboardInfo.Data == null)
                            return;

                        rs.SendDataFromClipboard(clipboardInfo);
                        break;

                    case RSEventType.SyncDisplaySetting:
                        //已在投屏过程中，由Video进程发送显示信息，Sevice设置显示器信息
                        SocketAppInfo appInfo = MyJson.DeserializeFromJsonString<SocketAppInfo>(e.EventResult);
                        if (appInfo == null || appInfo.Screens.Count == 0)
                            return;

                        rs.SetScreens(appInfo.ScreenSize, appInfo.Screens);
                        break;

                    case RSEventType.LocalSettingUpdate:
                        //重新加载用户设置
                        rsLocalSettingInfo windowSetting = new rsLocalSettingInfo();
                        RsAPI.LoadLocalSetting();
                        if (windowSetting.lock_device_after_disconnect != RsAPI.LocalSetting.lock_device_after_disconnect ||
                            windowSetting.display_in_safe_mode != RsAPI.LocalSetting.display_in_safe_mode ||
                            windowSetting.PwdType != RsAPI.LocalSetting.PwdType ||
                            windowSetting.Pwd != RsAPI.LocalSetting.Pwd ||
                            windowSetting.LastPwd != RsAPI.LocalSetting.LastPwd ||
                            windowSetting.Code != RsAPI.LocalSetting.LastPwd)
                        {
                            RsAPI.LocalSetting = windowSetting;
                            RsAPI.SaveLocalSetting(RsAPI.LocalSetting);
                        }

                        RsController.Instance.Password = RsAPI.LocalSetting.Pwd;
                        break;

                    case RSEventType.MDMEnrollment:
                        //MDM设备注册
                        if (string.IsNullOrEmpty(e.EventResult))
                            return;

                        rsDeviceDeployInfo deploy = MyJson.DeserializeFromJsonString<rsDeviceDeployInfo>(e.EventResult);
                        if (deploy == null)
                            return;

                        ThreadMgr.Start(() =>
                        {
                            MdmRegArgs mdm = MdmRegHelper.EnrollmentMDM(deploy.mail, deploy.discovery_url, deploy.deploy_code);

                            //保存MDM注册信息
                            if (mdm.Result)
                            {
                                BizAPI.SaveMDM(deploy, mdm);
                            }

                            if (mdm.ErrorType == MdmErrorType.Successs || mdm.ErrorType == MdmErrorType.ErrorOfExistMDM)
                            {
                                rs.MDMRegisterSuccessHandle(mdm.ErrorType == MdmErrorType.Successs, mdm.ErrorType, true);
                            }
                            else
                            {
                                MdmRegHelper.EnrollmentMDMByUWP(deploy.mail, deploy.discovery_url, deploy.deploy_code);
                            }
                        });
                        break;

                    case RSEventType.MDMUnenrollment:
                        //MDM设备反注册
                        if (ThreadMgr.CheckThreadIsAlive(mThreadMDMUnenrollmentn))
                            return;

                        mThreadMDMUnenrollmentn = ThreadMgr.Start(() =>
                        {
                            //同步前缓存下当前EnrollmentID，避免同步后注册表被删除，进而找不到EnrollmentID，用于后续清理一些任务
                            MdmRegArgs mdm = new MdmRegArgs();
                            mdm.UPN = BizAPI.DeviceDeployInfo.mail;
                            mdm.MDMProviderName = BizAPI.DeviceDeployInfo.mdm_provider_name;
                            MdmRegHelper.CheckExistMDM(mdm);
                            MdmRegHelper.GetMDMFromRegister(mdm);

                            string mTaskFolder = string.Format(@"\Microsoft\Windows\EnterpriseMgmt\{0}", mdm.EnrollmentID);

                            //主动同步
                            BizAPI.SyncMDM();

                            int tryCount = 0;
                            bool blnResult = false;

                            while (true)
                            {
                                tryCount++;
                                Thread.Sleep(3000);

                                //不存在MDM，说明解绑成功
                                if (!MdmRegHelper.CheckExistMDM(mdm))
                                {
                                    blnResult = true;
                                    break;
                                }

                                //等待125秒
                                if (tryCount > 200)
                                    break;

                                //每20秒主动再触发同步事件
                                if (tryCount % 4 == 0)
                                {
                                    //主动同步
                                    BizAPI.SyncMDM();
                                }

                                //blnResult = MdmRegHelper.UnenrollmentMDM(mdmReg);
                            }

                            //清空MDM注册信息
                            if (blnResult)
                            {
#if !MAC
                                RegEditMgr.SetLegalNotice("", "");

                                //清空所有定时任务
                                RsController.ClearAllTask();

                                //清空安装任务
                                SettingMgr.SetValue(KeyNameForBIZ.AppReleaseIds, "");
                                SettingMgr.SetValue(KeyNameForBIZ.AwaitInstallReleaseIds, "");
#endif
                                BizAPI.SaveDeployInfo(null, true);
                            }

                            RSEventType rsEventType = (blnResult ? RSEventType.MDMUnenrollmentSuccess : RSEventType.MDMUnenrollmentError);
                            RSEvent mdmEvent = RSEvent.Create(rsEventType, MyJson.SerializeToJsonString(mdm));

                            //通知界面MDM反注册结果
                            SocketMgr.SendMsgFromService(mdmEvent);

                            //执行解绑设备处理逻辑
                            if (blnResult)
                            {
                                UndeployDevice(rs);

                                //如果解绑成功，停止日活计时器
                                if (AppMgr.GetAppListTimer != null)
                                    TimerMgr.Stop(AppMgr.GetAppListTimer);
                            }
                        });
                        break;

                    case RSEventType.DeviceDeploy:
                    case RSEventType.DeviceTypeUpdateSuccessed:
                        if (string.IsNullOrEmpty(e.EventResult))
                            return;

                        rsDeviceDeployInfo deployRS = MyJson.DeserializeFromJsonString<rsDeviceDeployInfo>(e.EventResult);
                        if (deployRS == null)
                            return;

                        //更新内存绑定设备信息、以及设备的DeviceType与DeviceID
                        RsAPI.SaveDeployInfo(deployRS, false);

                        //更新配置
                        rs.GetSettingHandle();

                        //重新连接设备
                        MySocket.OpenSocketForRecv(RsController.Instance.Socket, UserBase.LocalDevice, true);

                        ////发送设备类型变化给服务端，用于监控有/无值守状态变更
                        rs.SendDeviceTypeChanged();

                        //ThreadMgr.Start(() =>
                        //{
                        //    Thread.Sleep(1000);
                        //    rs.SendHeartBeatPlus();
                        //});

                        if (RsAPI.DeviceDeployInfo != null)
                        {
                            //连接Push服务
                            rs.StartPush(deployRS, true);
                        }
                        break;

                    case RSEventType.DeviceUndeploySuccessed:
                        UndeployDevice(rs);
                        break;

                    case RSEventType.ScreenShareRequest:
                        code = Common.GetInteger(e.EventResult);
                        rs.Socket.SendRsResponse(e.Msg, code);

                        //打开屏幕视频
                        if (code == (int)skConnectCode.Accept)
                            rs.StartVideoApp();
                        break;

                    case RSEventType.CameraOpen:
                        int ar_install = Common.GetInteger(e.EventResult);
                        rs.Socket.SendRsAROpenResponse(e.Msg, 1, ar_install);

                        //打开摄像头视频
                        if (ar_install == 1)
                            rs.StartVideoApp();
                        break;

                    case RSEventType.CameraClose:
                        //关闭摄像头视频
                        rs.StopShareVideo();
                        break;

                    case RSEventType.SafeModeStatusOpen:
                    case RSEventType.SafeModeStatusClose:
                    case RSEventType.SafeModeStatus:
                        //设置安全模式状态
                        rs.SendRsSafeModeStatus(e);
                        SocketMgr.SendMsgFromService(e);

                        if (e.EventType == RSEventType.SafeModeStatusClose)
                        {
                            Thread.Sleep(500);
                            SocketMgr.StopApp(SocketType.SystemSafeMode);
                        }

                        break;


                    case RSEventType.WallpaperSet:
                        skWallpaperResult resultWallpaper = (skWallpaperResult)Common.GetInteger(e.EventResult);
                        skDevice devWallpaper = MyDevice.GetByKey(e.TargetDevice.rs_key);
                        if (devWallpaper == null)
                            return;

                        devWallpaper.WallpaperSetResult = e.TargetDevice.WallpaperSetResult;

                        //更新连接事件的设备对象
                        e.TargetDevice = devWallpaper;

                        //返回设置壁纸结果
                        code = Common.GetInteger(e.EventResult);
                        rs.Socket.SendRsResponse(e.Msg, code);
                        break;

                    case RSEventType.WallpaperSetResult:
                        //发送当前设置壁纸结果
                        rs.SendRsWallpaperStatus(e, (skWallpaperResult)Common.GetInteger(e.EventResult));
                        break;

                    case RSEventType.DisplayScreenSet:
                    case RSEventType.DisplayScreenChanged:
                        //发送屏幕状态变化
                        rs.SendRsDisplayScreenChanged(e);
                        break;

                    case RSEventType.VoIP:
                        //发送当前语音通话状态
                        rs.SendRsVoIP(e.TargetDevice, e.Code);
                        break;

                    case RSEventType.ControlChange:
                        //从MyDevice设备字典中获取缓存的设备对象
                        skDevice devControl = MyDevice.GetByKey(e.TargetDevice.rs_key);
                        devControl.ControlEnable = e.TargetDevice.ControlEnable;

                        RsClient clientControl = rs.GetClient(RsOpType.Get, devControl);
                        if (clientControl != null)
                        {
                            clientControl.CanControl = devControl.ControlEnable;

                            //发送控制状态消息
                            clientControl.Socket.SendRsAddonStatus(e.TargetDevice, clientControl.CanControl);
                        }
                        break;

                    case RSEventType.Connect:
                        //请求连接，界面返回的结果
                        code = Common.GetInteger(e.EventResult);

                        //根据设备的unique_id从MyDevice设备字典中获取缓存的设备对象
                        skDevice devConnect = MyDevice.GetByKey(e.TargetDevice.rs_key);
                        if (devConnect != null)
                        {
                            devConnect.ControlEnable = e.TargetDevice.ControlEnable; //保存是否可控制
                            devConnect.VoiceEnable = e.TargetDevice.VoiceEnable; //保存是否可控制

                            //更新连接事件的设备对象
                            e.TargetDevice = devConnect;

                            //发送是否可允许连接请求的回执
                            rs.StartShare(e, (skConnectCode)code, e.TargetDevice.ControlEnable, e.TargetDevice.VoiceEnable);
                        }
                        break;

                    case RSEventType.Disconnect:
                        rs.Disconnect(e.TargetDevice, RSEventType.None);
                        break;

                    //case RSEventType.ShowCodeRefresh:
                    //    rs.StartLoadQrCode(bool.Parse(e.EventResult));
                    //    break;

                    case RSEventType.ShowPwdRefresh:
                        MyLog.LogFile(string.Format("RecvMsgOnService.ShowPwdRefresh"), "QrCode");
                        rs.StartLoadQrCode(false, true, false);
                        break;

                    case RSEventType.AddonRequest:
                        //请求打开控制，界面返回结果
                        code = Common.GetInteger(e.EventResult);

                        rs.Socket.SendRsResponse(e.Msg, code);

                        //允许控制
                        if (code == 1)
                        {
                            RsClient client = rs.GetClientByMsg(e.Msg);
                            if (client != null)
                                client.CanControl = true;//开启控制
                        }
                        break;

                    case RSEventType.File:
                        //发送文件
                        JsonObject json = JsonParser.ParseString(e.EventResult);

                        FileUploadTask file = MyJson.DeserializeFromJsonString<FileUploadTask>(json["fileUploadTask"].ToString());

                        int duration = int.Parse(json["duration"].ToString());

                        if (file == null)
                            return;

                        rs.Socket.SendRsFileExt(e.TargetDevice, file, 0, e.EventID, duration);
                        break;
                    case RSEventType.Text:
                        //发送文本
                        string strContent = e.EventResult;
                        if (string.IsNullOrEmpty(strContent))
                            return;

                        rs.Socket.SendRsText(e.TargetDevice, strContent, 0, e.EventID);
                        break;

                    case RSEventType.OpenVoIP:
                        rs.MicrophoneAndSpeakerStatus(e.TargetDevice, true);
                        return;

                    case RSEventType.RsPwdRefreshStatu:
                        rs.SavePwdUpdateTime();
                        rs.IniPwdRefreshTimer();
                        return;

                    case RSEventType.DeviceTokenExpired:
                        MyLog.LogFile(string.Format("Token time out"), "Token");
                        if (mThreadUpdataToken != null)
                        {
                            MyLog.LogFile(string.Format("DeviceTokenUpdate -> ThreadMgr.Abort"), "Token");
                            ThreadMgr.Abort(mThreadUpdataToken);
                        }

                        mThreadUpdataToken = ThreadMgr.Start(() =>
                        {
                            MyLog.LogFile(string.Format("DeviceTokenUpdate -> ThreadMgr.Start"), "Token");

                            if (ParaMgr.IsBiz)
                                rs.GetTokenForBiz(false);
                            else
                                rs.DeviceTokenHandle();
                        });
                        break;
                    case RSEventType.ReStart:
#if MAC
                        SocketHandler socketWindow = SocketMgr.GetSocket(SocketType.UserDisplay);
                        if (socketWindow != null)
                        {
                            SocketMgr.KillProcessById(SocketMgr.GetAppId(SocketType.UserDisplay));

                            SocketMgr.StopApp(SocketType.UserDisplay);
                        }
#else
                        Thread.Sleep(2000);
#endif 

                        SocketMgr.StartApp(SocketType.UserDisplay);
                        break;
                    case RSEventType.SleepTimer:
#if !MAC
                        int sleepTimer = RsCommon.GetSleepTimer();
                        SocketMgr.SendMsgFromService(RSEvent.Create(RSEventType.SleepTimer, sleepTimer.ToString()));
#else

#endif
                        break;
                    case RSEventType.ShowInstallAppTip:
                        RsController.ReStartAppAutoUpdate();
                        break;

#if MAC
                    case RSEventType.NeverSleep:
                        //设置永不休眠
                        ServiceMgr.RunAppleScript("pmset -a sleep 0");
                        break;

                    case RSEventType.UpdateApp:
                        //ServiceMgr.RunAppleScript("installer -pkg /Users/<USER>/Software/RS安装包/AirDroidRS_20250509_1546.pkg -target / -verbose -dumplog -allowUntrusted");
                        break;

                    case RSEventType.UnloadApp: //卸载app
                        bool isRetainedData = Convert.ToBoolean(e.EventResult);
                        ServiceMgr.RunAppleScript(GetUnloadAppScript(isRetainedData));
                        break;                  
#endif

                    case RSEventType.McpServerConnect:
                    case RSEventType.McpServerDisconnect:
                        SocketMgr.SendMsgFromService(e, SocketType.UserDisplay);
                        break;

                    case RSEventType.McpServerList:
                        if (e.SocketType == SocketType.McpClient)
                        {
                            //将服务列表转发给UI界面
                            SocketMgr.SendMsgFromService(e);
                        }
                        break;

                    case RSEventType.McpServerAddConfig:
                    case RSEventType.McpServerRemoveConfig:
                    case RSEventType.McpServerUpdateConfig:
                    case RSEventType.McpServerCallTool:
                        if (e.SocketType == SocketType.UserDisplay)
                        {
                            //将调用工具请求转发给McpClient
                            SocketMgr.SendMsgFromService(e, SocketType.McpClient);
                        }
                        else if (e.SocketType == SocketType.McpClient)
                        {
                            //McpClient返回的工具结果转发给UI界面
                            SocketMgr.SendMsgFromService(e, SocketType.UserDisplay);
                        }
                        break;

                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "RecvMsgOnService");
            }
        }

        /// <summary></summary>
        /// <param name="isRetainedData">是否保留个人数据</param>
        /// <returns></returns>
        private static string GetUnloadAppScript(bool isRetainedData)
        {
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.AppendLine("sudo pkill -f \"Remote Support\"");

            strBuilder.AppendLine("sudo launchctl stop com.airdroid.remotesupport.service");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchDaemons / com.airdroid.remotesupport.service.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchDaemons / com.airdroid.remotesupport.service.plist");

            strBuilder.AppendLine("sudo launchctl stop com.airdroid.remotesupport.windows");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchAgents / com.airdroid.remotesupport.windows.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchAgents / com.airdroid.remotesupport.windows.plist");

            strBuilder.AppendLine("sudo launchctl stop com.airdroid.remotesupport.proxy");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchAgents / com.airdroid.remotesupport.proxy.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchAgents / com.airdroid.remotesupport.proxy.plist");

            strBuilder.AppendLine("sudo launchctl stop com.airdroid.remotesupport.safemode");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchAgents / com.airdroid.remotesupport.safemode.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchAgents / com.airdroid.remotesupport.safemode.plist");

            strBuilder.AppendLine("sudo launchctl stop com.airdroid.remotesupport.video");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchAgents / com.airdroid.remotesupport.video.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchAgents / com.airdroid.remotesupport.video.plist");

            strBuilder.AppendLine("sudo launchctl stop com.sandstudio.remotesupport.service");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchDaemons / com.sandstudio.remotesupport.service.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchDaemons / com.sandstudio.remotesupport.service.plist");

            strBuilder.AppendLine("sudo launchctl stop com.sandstudio.remotesupport.windows");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchAgents / com.sandstudio.remotesupport.windows.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchAgents / com.sandstudio.remotesupport.windows.plist");

            strBuilder.AppendLine("sudo launchctl stop com.sandstudio.remotesupport.proxy");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchAgents / com.sandstudio.remotesupport.proxy.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchAgents / com.sandstudio.remotesupport.proxy.plist");

            strBuilder.AppendLine("sudo launchctl stop com.sandstudio.remotesupport.safemode");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchAgents / com.sandstudio.remotesupport.safemode.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchAgents / com.sandstudio.remotesupport.safemode.plist");

            strBuilder.AppendLine("sudo launchctl stop com.sandstudio.remotesupport.video");
            strBuilder.AppendLine("sudo launchctl unload / Library / LaunchAgents / com.sandstudio.remotesupport.video.plist");
            strBuilder.AppendLine("sudo rm -f / Library / LaunchAgents / com.sandstudio.remotesupport.video.plist");

            strBuilder.AppendLine("sudo rm -rf '/Applications/Remote Support.app'");

            if (!isRetainedData)
            {
                strBuilder.AppendLine("sudo rm -rf /Library/Caches/AirDroidRemoteSupport");
            }

            strBuilder.AppendLine("sudo pkill -f \"Remote Support\"");

            return strBuilder.ToString();
        }
    }
}
