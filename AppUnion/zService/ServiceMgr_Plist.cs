﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Threading;
using System.IO;
using System.ServiceProcess;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreFoundation;
using iTong.CoreModule;
using iTong.Android;
using iTong.Device;

#if MAC
using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;
#else
using System.Drawing;
using Microsoft.Win32;
#endif

namespace iTong.Android
{
    public partial class ServiceMgr
    {
        protected static StringBuilder mScriptBuilder = new StringBuilder();

        public static StringBuilder Script
        {
            get
            {
                return mScriptBuilder;
            }
        }

        public static void ScriptClear()
        {
            mScriptBuilder.Clear();
        }

        public static void ScriptAppendLaunchctlLoad(string serviceName)
        {
            ScriptAppend(string.Format("launchctl load {0}", GetServicePlistPath(serviceName)));
            ScriptAppend(string.Format("launchctl start {0}", serviceName));
        }

        public static void ScriptAppendKillSelf()
        {
            Process p = Process.GetCurrentProcess();
            ScriptAppendKillPid(p.Id);
        }

        public static void ScriptAppendKillPid(int pid)
        {
            ScriptAppend(string.Format("sudo kill -9 {0}", pid));
        }

        public static void ScriptAppend(string cmd)
        {
            mScriptBuilder.AppendLine(cmd);
        }

        public static void ScriptRun()
        {
            try
            {
                string strScript = mScriptBuilder.ToString();
                if (string.IsNullOrEmpty(strScript))
                    return;


            }
            catch (Exception ex)
            {
                Common.LogException(ex, "ScriptRun");
            }
        }

        /// <summary>
        /// /Library/LaunchDaemons
        /// </summary>
        public static string PathLaunchDaemons
        {
            get
            {
                return "/Library/LaunchDaemons";
            }
        }

        /// <summary>
        /// /Library/LaunchAgents
        /// </summary>
        public static string PathLaunchAgents
        {
            get
            {
                return "/Library/LaunchAgents";
            }
        }

        /// <summary>
        /// /bin/launchctl
        /// </summary>
        public static string PathLaunchctl
        {
            get
            {
                return "/bin/launchctl";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport
        /// </summary>
        public static string ServiceNameForRS_Host { get; } = "com.sandstudio.remotesupport";

        /// <summary>
        /// com.sandstudio.remotesupport.service
        /// </summary>
        public static string ServiceNameForRS_Service
        {
            get
            {
                return ServiceNameForRS_Host + ".service";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.proxy
        /// </summary>
        public static string ServiceNameForRS_Proxy
        {
            get
            {
                return ServiceNameForRS_Host + ".proxy";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.video
        /// </summary>
        public static string ServiceNameForRS_Video
        {
            get
            {
                return ServiceNameForRS_Host + ".video";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.windows
        /// </summary>
        public static string ServiceNameForRS_Windows
        {
            get
            {
                return ServiceNameForRS_Host + ".windows";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.screen
        /// </summary>
        public static string ServiceNameForRS_Screen
        {
            get
            {
                return ServiceNameForRS_Host + ".screen";
            }
        }

        /// <summary>
        /// com.sandstudio.remotesupport.safemode
        /// </summary>
        public static string ServiceNameForRS_SafeMode
        {
            get
            {
                return ServiceNameForRS_Host + ".safemode";
            }
        }

        public static bool IsRoot { get; private set; } = false;

        // Importing necessary SystemConfiguration functions
        [DllImport("/System/Library/Frameworks/SystemConfiguration.framework/SystemConfiguration")]
        private static extern IntPtr SCDynamicStoreCreate(IntPtr allocator, IntPtr name, IntPtr callout, IntPtr context);

        [DllImport("/System/Library/Frameworks/SystemConfiguration.framework/SystemConfiguration")]
        private static extern IntPtr SCDynamicStoreCopyConsoleUser(IntPtr store, out uid_t uid, IntPtr gid);

        // Define uid_t to match the native type
        private struct uid_t
        {
            public uint Value;
        }

        public static string GetConsoleUserName(out uint userID)
        {
            string userName = string.Empty;

            uid_t uid;
            IntPtr hStore = IntPtr.Zero;
            IntPtr hName = IntPtr.Zero;

            userID = 0;

            try
            {
                // Create a dynamic store reference
                CFString cfName = new CFString("GetConsoleUser");
                hStore = SCDynamicStoreCreate(IntPtr.Zero, cfName.Handle, IntPtr.Zero, IntPtr.Zero);
                cfName.Dispose();

                if (hStore == IntPtr.Zero)
                {
                    Log("SCDynamicStoreCreate GetConsoleUser failed.");
                    goto DoExit;
                }

                // Copy the console user
                hName = SCDynamicStoreCopyConsoleUser(hStore, out uid, IntPtr.Zero);

                if (hName == IntPtr.Zero)
                {
                    // If there's no console user, assume pre-login
                    goto DoExit;
                }

                userName = iTong.Device.CoreFoundation.ReadCFStringFromIntPtr(hName);
                userID = uid.Value;
            }
            finally
            {
                if (hStore != IntPtr.Zero)
                    iTong.Device.CoreFoundation.CFRelease(hStore);

                if (hName != IntPtr.Zero)
                    iTong.Device.CoreFoundation.CFRelease(hName);
            }

        DoExit:
            WriteLine(string.Format("GetConsoleUserName -> userName = {0}, userID = {1}", userName, userID));
            return userName;
        }

        public static string GetConsoleUserName()
        {
            uint userID;

            return GetConsoleUserName(out userID);
        }

        public static uint GetConsoleUserID()
        {
            uint userID;

            GetConsoleUserName(out userID);

            return userID;
        }

        public static bool IsLoginWindowUser()
        {
            string userName = GetConsoleUserName();

            return string.IsNullOrEmpty(userName) || userName.Contains("loginwindow");
        }

        public static bool IsUserLogin()
        {
            string userName = GetConsoleUserName();

            return !string.IsNullOrEmpty(userName) && !userName.Contains("loginwindow");
        }

        public static string GetCurrentUser()
        {
            string userName = string.Empty;

            try
            {
                userName = MyAPI.Username;

                if (string.IsNullOrEmpty(userName))
                    userName = NSProcessInfo.ProcessInfo.GetUserName();
            }
            catch (Exception ex)
            {
                userName = Common.RunShell("/usr/bin/whoami");
            }

            WriteLine(string.Format("GetCurrentUser -> {0}", userName));

            return userName;
        }

        public static bool CheckCurrentUserIsAdmin(string userName = "")
        {
            bool blnResult = false;

            try
            {
                if (string.IsNullOrEmpty(userName))
                    userName = GetConsoleUserName();

                if (string.IsNullOrEmpty(userName) || userName == "loginwindow")
                    goto DoExit;

                string strLog = Common.RunShell("/usr/bin/id", "-Gn", userName);
                if (strLog.Contains("admin"))
                    blnResult = true;
            }
            catch (Exception ex)
            {

            }

        DoExit:
            return blnResult;
        }

        public static void Chown(string strDir = "")
        {
            if (!IsRoot)
                return;

            if (string.IsNullOrEmpty(strDir))
                strDir = Folder.ApplicationDataFolder;

            string userName = GetConsoleUserName();
            if (string.IsNullOrEmpty(userName) || userName == "loginwindow")
                return;

            if (!CheckCurrentUserIsAdmin())
                return;

            Common.Chown(strDir, userName);
        }

        public static bool LaunchApp(string serviceName, string exePath, params string[] exeArgs)
        {
            bool blnResult = true;
            
            if (IsRoot && !IsLoginWindowUser())
            {
                SocketMgr.LogApp(string.Format("LaunchApp -> serviceName={0}", serviceName));

                //用户已经登录，非loginWindow用户
                LaunchctlLoad(serviceName);
            }
            else
            {
                SocketMgr.LogApp(string.Format("RunApp -> exePath={0}, exeArgs={1}", exePath, exeArgs.Join()));

                Common.RunApp(exePath, exeArgs);
            }

            return blnResult;
        }

        public static string GetServicePlistPath(string serviceName)
        {
            string plistName = string.Format("{0}.plist", serviceName);
            string plistPath = Path.Combine(PathLaunchAgents, plistName);

            if (!File.Exists(plistPath))
            {
                plistPath = Path.Combine(PathLaunchDaemons, plistName);
                if (!File.Exists(plistPath))
                    return string.Empty;
            }

            return plistPath;
        }

        public static void LaunchctlLoad(string serviceName, bool autoStart = true)
        {
            string plistPath = GetServicePlistPath(serviceName);
            if (string.IsNullOrEmpty(plistPath))
            {
                WriteLine(string.Format("LaunchctlLoad failed, Service is not exist. -> {0}", serviceName));
                return;
            }

            //先加载服务
            string strLog = string.Empty;

            if (IsRoot && plistPath.StartsWith(PathLaunchAgents, StringComparison.OrdinalIgnoreCase))// && !IsLoginWindowUser()
            {
                //LaunchctlStop(serviceName);

                //用户已经登录，非loginWindow用户
                string guiUserID = string.Format("gui/{0}", GetConsoleUserID());
                strLog = Common.RunShell(PathLaunchctl, "bootout", guiUserID, plistPath);
                strLog = Common.RunShell(PathLaunchctl, "bootstrap", guiUserID, plistPath);

                //LaunchctlStart(serviceName);
            }
            else
            {
                strLog = Common.RunShell(PathLaunchctl, "load", plistPath);

                if (!autoStart)
                    return;

                ////若plist文件中RunAtLoad=false，则再运行start命令
                //bool RunAtLoad = false;
                //Dictionary<object, object> dictPlist = iTong.Device.CoreFoundation.ReadPlist_managed(plistPath) as Dictionary<object, object>;
                //if (dictPlist != null && dictPlist.ContainsKey("RunAtLoad"))
                //    RunAtLoad = (bool)dictPlist["RunAtLoad"];

                //if (!RunAtLoad)
                {
                    //即使plist文件中RunAtLoad=true，也要重新调用start，避免有时候服务已经加载，但是服务没有被正常唤起执行
                    LaunchctlStart(serviceName);
                }
            }
        }

        public static void LaunchctlUnload(string serviceName)
        {
            string plistPath = GetServicePlistPath(serviceName);
            string strLog = string.Empty;

            if (string.IsNullOrEmpty(plistPath))
            {
                WriteLine(string.Format("LaunchctlUnload failed, Service is not exist. -> {0}", serviceName));
                strLog = Common.RunShell(PathLaunchctl, "stop", serviceName);
                return;
            }

            if (IsRoot && plistPath.StartsWith(PathLaunchAgents, StringComparison.OrdinalIgnoreCase))// && !IsLoginWindowUser()
            {
                string guiUserID = string.Format("gui/{0}", GetConsoleUserID());
                //用户已经登录，非loginWindow用户
                strLog = Common.RunShell(PathLaunchctl, "bootout", guiUserID, plistPath);
            }
            else
            {
                //先加载服务
                strLog = Common.RunShell(PathLaunchctl, "unload", plistPath);
            }
        }

        public static void LaunchctlStart(string serviceName)
        {
            Common.RunShell(PathLaunchctl, "start", serviceName);
        }

        public static void LaunchctlStop(string serviceName)
        {
            Common.RunShell(PathLaunchctl, "stop", serviceName);
        }

        public static List<string> LaunchctlList()
        {
            string strLog = Common.RunShell(PathLaunchctl, "list");
            return Common.GetDataByIndex(strLog, 2);
        }

        public static Dictionary<int, string> GetRunningServices()
        {
            Dictionary<int, string> dict = new Dictionary<int, string>();

            string strLog = Common.RunShell(PathLaunchctl, "list");
            List<string[]> listRows = Common.FormatData(strLog, -1, true);
            foreach (string[] arrItem in listRows)
            {
                int pid = Common.GetInteger(arrItem[0]);
                if (pid <= 0)
                    continue;

                dict[pid] = arrItem[2];
            }

            return dict;
        }

        public static Dictionary<int, string> GetRunningProcess(string includeProcessName = "")
        {
            Dictionary<int, string> dict = new Dictionary<int, string>();

            /*
  PID TTY           TIME CMD
    1 ??         0:39.91 /sbin/launchd
  120 ??         0:03.67 /usr/sbin/syslogd
             * */
            string strLog = Common.RunShell("/bin/ps", "-ax");
            int maxColumnCount = 4;
            List<string[]> listRows = Common.FormatData(strLog, maxColumnCount, true, includeProcessName);
            foreach (string[] arrItem in listRows)
            {
                if (arrItem.Length < 4)
                    continue;

                int pid = Common.GetInteger(arrItem[0]);
                if (pid <= 0)
                    continue;

                dict[pid] = arrItem[maxColumnCount - 1];
            }

            return dict;
        }

        public static Dictionary<int, string> GetProcess(string includeKeyword = "/")
        {
            Dictionary<int, string> dict = new Dictionary<int, string>();

            /*
 1218 ??         1:13.42 /Library/Frameworks/Mono.framework/Versions/6.0.0/bin/mono64 --debug /Users/<USER>/Library/Caches/VisualStudio/8.0/MSBuild/1047_1/MonoDevelop.MSBuildBuilder.exe 49440 False
 9022 ??         0:00.03 /usr/bin/sysdiagnose

             * */
            string strLog = Common.RunShell("/usr/bin/pgrep", "-f", "-l", includeKeyword);
            int maxColumnCount = 2;
            List<string[]> listRows = Common.FormatData(strLog, maxColumnCount, false);
            foreach (string[] arrItem in listRows)
            {
                if (arrItem.Length < maxColumnCount)
                    continue;

                int pid = Common.GetInteger(arrItem[0]);
                if (pid <= 0)
                    continue;

                dict[pid] = arrItem[maxColumnCount - 1];
            }

            return dict;
        }

        public static int GetProcessPID(string processName, string paraArg = "")
        {
            int exePID = 0;

            int currentPID = Process.GetCurrentProcess().Id;

            string exePath = string.Empty;

            Dictionary<int, string> dict = GetRunningProcess(processName);
            foreach (KeyValuePair<int, string> item in dict)
            {
                if (item.Key == currentPID)
                    continue;

                // 改进 PSN 检测逻辑：PSN 参数是动态的，不应该用于进程检测
                if (string.IsNullOrEmpty(paraArg))
                {
                    // 如果没有指定参数，检查是否为有效的活跃进程
                    if (IsProcessAlive(item.Key))
                    {
                        exePID = item.Key;
                        exePath = item.Value;
                        break;
                    }
                }
                else if (paraArg.StartsWith("psn_"))
                {
                    // PSN 参数特殊处理：检查是否为用户启动的进程（不包含服务参数）
                    if (!item.Value.Contains("t=run") && IsProcessAlive(item.Key))
                    {
                        exePID = item.Key;
                        exePath = item.Value;
                        break;
                    }
                }
                else if (item.Value.Contains(paraArg) && IsProcessAlive(item.Key))
                {
                    exePID = item.Key;
                    exePath = item.Value;
                    break;
                }
            }

            ServiceMgr.WriteLine(string.Format("GetProcessPID -> ProcessName = {0}, Path = {1}, PID = {2}, currentPID = {3}, paraArg = {4}", processName, exePath, exePID, currentPID, paraArg));

            return exePID;
        }

        /// <summary>
        /// 检查进程是否真实存在且活跃
        /// </summary>
        /// <param name="pid">进程ID</param>
        /// <returns>true表示进程活跃，false表示进程不存在或为僵尸进程</returns>
        public static bool IsProcessAlive(int pid)
        {
            try
            {
                // 使用 kill -0 检查进程是否存在且活跃
                string result = Common.RunShell("/bin/kill", "-0", pid.ToString());

                // 如果命令执行成功（返回码为0），说明进程存在
                // 同时检查进程状态，确保不是僵尸进程
                string statusResult = Common.RunShell("/bin/ps", "-p", pid.ToString(), "-o", "stat=");

                // 如果状态包含 Z，说明是僵尸进程
                if (!string.IsNullOrEmpty(statusResult) && statusResult.Trim().Contains("Z"))
                {
                    ServiceMgr.WriteLine(string.Format("IsProcessAlive -> PID {0} is zombie process", pid));
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                ServiceMgr.WriteLine(string.Format("IsProcessAlive -> PID {0} check failed: {1}", pid, ex.Message));
                return false;
            }
        }

        public static int GetCurrentAppProcessPID(string cmd)
        {
            string processName = Path.GetFileName(Application.ExecutablePath);
            return GetProcessPID(processName, cmd);
        }

        public static bool CheckCurrentProcessExist(string cmd)
        {
            bool blnResult = GetCurrentAppProcessPID(cmd) > 0;

            return blnResult;
        }

        public static void KillCurrentAppContainKey(string cmd)
        {
            int currentPID = Process.GetCurrentProcess().Id;

            string processName = Path.GetFileName(Application.ExecutablePath);

            Dictionary<int, string> dict = GetRunningProcess(processName);
            foreach (KeyValuePair<int, string> item in dict)
            {
                if (item.Key == currentPID)
                    continue;

                if (item.Value.Contains(cmd))
                {
                    ServiceMgr.WriteLine(string.Format("KillCurrentAppContainKey -> ProcessName = {0}, Path = {1}, PID = {2}, currentPID = {3}", processName, item.Value, item.Key, currentPID));
                    SocketMgr.KillProcessById(item.Key);
                }
            }
        }

        public static void KillCurrentExistApp()
        {
            int currentPID = Process.GetCurrentProcess().Id;

            string processName = Path.GetFileName(Application.ExecutablePath);

            Dictionary<int, string> dict = GetRunningProcess(processName);
            foreach (KeyValuePair<int, string> item in dict)
            {
                if (item.Key == currentPID)
                    continue;

                ScriptAppendKillPid(item.Key);
            }
        }

        public static void LaunchctlRestart(string serviceName)
        {
            Common.RunShell(PathLaunchctl, "kickstart", "-k", serviceName);
        }

        public static void ExitAppForRS()
        {
            ExitAppForRS(true);
        }

        public static void ExitAppForRS(bool dockerExit)
        {
            MyLog.WriteLine("OnExitApp -> Kill Process");

            //隐藏Dock图标
            Application.SetActivationPolicy(NSApplicationActivationPolicy.Prohibited);

            //隐藏状态栏图标
            if (skStatusItem.Current != null)
                skStatusItem.Current.Visible = false;

            //通知Service用户主动退出
            if (SocketMgr.SendMsgFromClient(RSEventType.ExitApp))
            {
                //通过Socket发送退出消息失败，尝试直接Kill进程
                SocketMgr.KillProcessByName(Path.GetFileNameWithoutExtension(ServiceMgr.ServicePath), false);
            }

            // 增强清理机制：确保所有相关进程都被清理
            CleanupAllRelatedProcesses();

            if (dockerExit)
            {
                //增加等待时间，确保清理完成
                Thread.Sleep(2000);

                // 最后验证清理是否成功
                VerifyCleanupComplete();
            }
        }

        /// <summary>
        /// 清理所有相关进程
        /// </summary>
        public static void CleanupAllRelatedProcesses()
        {
            try
            {
                string processName = Path.GetFileNameWithoutExtension(Application.ExecutablePath);
                int currentPID = Process.GetCurrentProcess().Id;

                MyLog.WriteLine($"CleanupAllRelatedProcesses -> Current PID: {currentPID}, ProcessName: {processName}");

                // 获取所有相关进程
                Dictionary<int, string> dict = GetRunningProcess(processName);
                List<int> pidsToKill = new List<int>();

                foreach (KeyValuePair<int, string> item in dict)
                {
                    if (item.Key == currentPID)
                        continue;

                    // 检查是否为真实活跃的进程
                    if (IsProcessAlive(item.Key))
                    {
                        pidsToKill.Add(item.Key);
                        MyLog.WriteLine($"CleanupAllRelatedProcesses -> Found process to kill: PID={item.Key}, CMD={item.Value}");
                    }
                }

                // 逐个终止进程
                foreach (int pid in pidsToKill)
                {
                    try
                    {
                        // 先尝试温和终止
                        Common.RunShell("/bin/kill", "-TERM", pid.ToString());
                        Thread.Sleep(500);

                        // 检查是否还存在，如果存在则强制终止
                        if (IsProcessAlive(pid))
                        {
                            Common.RunShell("/bin/kill", "-KILL", pid.ToString());
                            MyLog.WriteLine($"CleanupAllRelatedProcesses -> Force killed PID: {pid}");
                        }
                    }
                    catch (Exception ex)
                    {
                        MyLog.WriteLine($"CleanupAllRelatedProcesses -> Failed to kill PID {pid}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                MyLog.WriteLine($"CleanupAllRelatedProcesses -> Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证清理是否完成
        /// </summary>
        private static void VerifyCleanupComplete()
        {
            try
            {
                string processName = Path.GetFileNameWithoutExtension(Application.ExecutablePath);
                int currentPID = Process.GetCurrentProcess().Id;

                Dictionary<int, string> remainingProcesses = GetRunningProcess(processName);
                foreach (KeyValuePair<int, string> item in remainingProcesses)
                {
                    if (item.Key != currentPID && IsProcessAlive(item.Key))
                    {
                        MyLog.WriteLine($"VerifyCleanupComplete -> Warning: Process still running: PID={item.Key}, CMD={item.Value}");
                    }
                }
            }
            catch (Exception ex)
            {
                MyLog.WriteLine($"VerifyCleanupComplete -> Error: {ex.Message}");
            }

            //LaunchctlUnload(ServiceNameForRS_Service);
            //LaunchctlUnload(ServiceNameForRS_Video);
            //LaunchctlUnload(ServiceNameForRS_SafeMode);
            //LaunchctlUnload(ServiceNameForRS_Windows);

            //SocketMgr.KillCurrentApp();
        }

        public static string ToArgPathFormat(string strArg)
        {
            if (string.IsNullOrEmpty(strArg))
                return string.Empty;
            else
                return strArg.Replace(@"""", @"\""");
        }

        public static string ConvertToPlistString(NSObject nsValue)
        {
            string strPlist = string.Empty;

            string tmpFile = Folder.GetTempFilePath(".plist");
            object objValue = iTong.Device.CoreFoundation.ManagedTypeFromCFType(nsValue.Handle);
            if (objValue != null)
                if (iTong.Device.CoreFoundation.WritePlist(objValue, tmpFile))
                    strPlist = iTong.Device.CoreFoundation.ReadPlist(tmpFile);

            return strPlist;
        }


        public static string RunAppleScript(string strScript)
        {
            StringBuilder sbLog = new StringBuilder();

            if (string.IsNullOrEmpty(strScript))
                goto DoExit;

            string[] arrLine = strScript.Split(new string[] { "\r\n", "\n" }, StringSplitOptions.RemoveEmptyEntries);
            if (arrLine.Length == 0)
                goto DoExit;

            StringBuilder sb = new StringBuilder();
            foreach (string strLine in arrLine)
            {
                sb.AppendFormat("{0};", ToArgPathFormat(strLine));
            }

            string command = string.Format("do shell script \"{0}\" with prompt \"{1} want to install daemon and agent\" with administrator privileges", sb.ToString(), ServiceMgr.ServiceNameForRS);
            sbLog.AppendLine(command);

            string strLog = Common.RunShell("/usr/bin/osascript", "-e", command);
            sbLog.AppendLine(strLog);

            //下面脚本会导致当前程序的进程卡住无法结束
            {
                //NSDictionary dictErrorReturn = null;
                //NSAppleScript appleScript = null;

                //try
                //{
                //    appleScript = new NSAppleScript(command);
                //    NSAppleEventDescriptor descriptor = appleScript.ExecuteAndReturnError(out dictErrorReturn);
                //    if (descriptor != null)
                //        sbLog.AppendLine(descriptor.StringValue + "");
                //}
                //catch (Exception ex)
                //{
                //    WriteLine(ex.ToString());
                //}
                //finally
                //{
                //    if (dictErrorReturn != null)
                //    {
                //        sbLog.AppendLine(ConvertToPlistString(dictErrorReturn));
                //        dictErrorReturn.Dispose();
                //    }

                //    if (appleScript != null)
                //        appleScript.Dispose();
                //}
            }

        DoExit:
            WriteLine(sbLog.ToString());

            return sbLog.ToString();
        }

        public static void WriteLine(string msg, string prefixName = "Shell")
        {
            Common.WriteLine(msg, prefixName, true);
        }

        public static string CreatePlistFile(string strPlist)
        {
            string pathPlist = Folder.GetTempFilePath(".plist");
            File.WriteAllText(pathPlist, strPlist, Encoding.UTF8);

            return pathPlist;
        }

        public static string CreatePlistAsService(string pathExcute = "")
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF - 8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>StandardErrorPath</key>
	<string>/var/log/AirDroidRemoteSupport</string>
	<key>StandardOutPath</key>
	<string>/var/log/AirDroidRemoteSupport</string>
	<key>KeepAlive</key>
	<{2}/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.service</string>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runService</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";

            string logFolder = Folder.LogFolder;

            /*
             * sleep 3;
             * if pgrep -f '/Applications/RustDesk.app/Contents/MacOS/RustDesk --server' > /dev/null; then
             *      /Applications/RustDesk.app/Contents/MacOS/RustDesk --service;
             * fi
             */
            string cmd = string.Format("sleep 3; if pgrep -f '{0} {1}' > /dev/null; then '{0}' {1}; fi",
                Application.ExecutablePath,
                ParaMgr.CommandRunService);

            string exePath = Application.ExecutablePath + " Service";
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Service,
                ProgramArguments = new List<string> { exePath, ParaMgr.CommandRunService },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                Disabled = false,
                KeepAlive = exePath.StartsWith("/Applications/", StringComparison.OrdinalIgnoreCase),
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,

                //暂不设置日志目录，否则会导致服务启动异常
                //StandardOutPath = Path.Combine(Folder.LogFolder, "Out.log"),
                //StandardErrorPath = Path.Combine(Folder.LogFolder, "Error.log"),
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlistAsProxy()
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF - 8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<true/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.proxy</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>LoginWindow</string>
		<string>Aqua</string>
	</array>
	<key>ProcessType</key>
	<string>Interactive</string>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runVideo</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";
            string exePath = Application.ExecutablePath + " Service";//Proxy
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Proxy,
                LimitLoadToSessionType = LimitLoadToSessionType.LoginWindow,
                ProgramArguments = new List<string> { exePath, ParaMgr.CommandRunProxy },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                //Disabled = true,
                KeepAlive = exePath.StartsWith("/Applications/", StringComparison.OrdinalIgnoreCase),
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlistAsVideo()
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF - 8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<true/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.video</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>LoginWindow</string>
		<string>Aqua</string>
	</array>
	<key>ProcessType</key>
	<string>Interactive</string>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runVideo</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";
            string exePath = Application.ExecutablePath + " Service";
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Video,
                LimitLoadToSessionType = LimitLoadToSessionType.Aqua | LimitLoadToSessionType.LoginWindow,
                ProgramArguments = new List<string> { exePath, ParaMgr.CommandRunVideo },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                //Disabled = true,
                KeepAlive = false,
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlistAsWindows()
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF - 8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.windows</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>Aqua</string>
	</array>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runWindows</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_Windows,
                LimitLoadToSessionType = LimitLoadToSessionType.Aqua,
                ProgramArguments = new List<string> { ParaMgr.CommandRunWidnows },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                Disabled = false,
                KeepAlive = false,
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,
            };

            return CreatePlist(args);
        }

        public static string CreatePlistAsSafeMode()
        {
            string strPlist = @"<?xml version=""1.0"" encoding=""UTF - 8""?>
<!DOCTYPE plist PUBLIC ""-//Apple//DTD PLIST 1.0//EN"" ""http://www.apple.com/DTDs/PropertyList-1.0.dtd"" >
<plist version = ""1.0"" >
<dict>
	<key>AssociatedBundleIdentifiers</key>
	<array>
		<string>com.sandstudio.remotesupport</string>
	</array>
	<key>Disabled</key>
	<false/>
	<key>KeepAlive</key>
	<false/>
	<key>Label</key>
	<string>com.sandstudio.remotesupport.safemode</string>
	<key>LimitLoadToSessionType</key>
	<array>
		<string>Aqua</string>
	</array>
	<key>ProgramArguments</key>
	<array>
		<string>{0}</string>
		<string>t=runSafeMode</string>
	</array>
	<key>RunAtLoad</key>
	<true/>
	<key>WorkingDirectory</key>
	<string>{1}</string>
</dict>
</plist>
";

            string exePath = Application.ExecutablePath + " Service";
            if (!File.Exists(exePath))
                exePath = Application.ExecutablePath;

            LaunchArgs args = new LaunchArgs()
            {
                Label = ServiceNameForRS_SafeMode,
                LimitLoadToSessionType = LimitLoadToSessionType.Aqua,
                ProgramArguments = new List<string> { exePath, ParaMgr.CommandRunSafeMode },
                AssociatedBundleIdentifiers = ServiceNameForRS_Host,
                Disabled = false,
                KeepAlive = false,
                RunAtLoad = true,
                WorkingDirectory = Folder.MacOSFolder,
            };

            return CreatePlist(args, false);
        }

        public static string CreatePlist(LaunchArgs args, bool appendExePath = true, string cmdFormat = "")
        {
            if (args.AssociatedBundleIdentifiersList == null)
                args.AssociatedBundleIdentifiersList = new List<string>();

            if (args.ProgramArguments == null)
                args.ProgramArguments = new List<string>();

            if (string.IsNullOrEmpty(args.WorkingDirectory))
                args.WorkingDirectory = Folder.MacOSFolder;

            if (string.IsNullOrEmpty(args.Program))
                args.Program = Application.ExecutablePath;

            if (args.AssociatedBundleIdentifiersList.Count == 0)
            {
                if (string.IsNullOrEmpty(args.AssociatedBundleIdentifiers))
                    args.AssociatedBundleIdentifiers = ServiceNameForRS_Host;

                args.AssociatedBundleIdentifiersList.Add(args.AssociatedBundleIdentifiers);
            }

            Dictionary<object, object> dictPlist = new Dictionary<object, object>();

            dictPlist["AssociatedBundleIdentifiers"] = args.AssociatedBundleIdentifiersList;

            if (args.Disabled != null)
                dictPlist["Disabled"] = args.Disabled;

            dictPlist["KeepAlive"] = args.KeepAlive;
            dictPlist["Label"] = args.Label;

            if (args.LimitLoadToSessionType != LimitLoadToSessionType.None)
                dictPlist["LimitLoadToSessionType"] = args.GetLimitLoadToSessionType();


            if (args.ProgramArguments.Count > 0)
            {
                //程序路径不在参数列表中，将程序路径插入在第一个参数列表
                if (appendExePath && !string.IsNullOrEmpty(args.Program) && !args.ProgramArguments.Contains(args.Program))
                    args.ProgramArguments.Insert(0, args.Program);

                dictPlist["ProgramArguments"] = args.ProgramArguments;
            }
            else if (!string.IsNullOrEmpty(args.Program))
            {
                dictPlist["Program"] = args.Program;
            }

            dictPlist["RunAtLoad"] = args.RunAtLoad;

            if (args.LaunchOnlyOnce != null)
                dictPlist["LaunchOnlyOnce"] = args.LaunchOnlyOnce;

            if (args.OnDemand != null)
                dictPlist["OnDemand"] = args.OnDemand;

            if (!string.IsNullOrEmpty(args.WorkingDirectory))
                dictPlist["WorkingDirectory"] = args.WorkingDirectory;

            if (!string.IsNullOrEmpty(args.StandardOutPath))
                dictPlist["StandardOutPath"] = args.StandardOutPath;

            if (!string.IsNullOrEmpty(args.StandardErrorPath))
                dictPlist["StandardErrorPath"] = args.StandardErrorPath;

            if (!string.IsNullOrEmpty(args.EnvironmentVariables))
                dictPlist["EnvironmentVariables"] = args.EnvironmentVariables;

            if (!string.IsNullOrEmpty(args.UserName))
                dictPlist["UserName"] = args.UserName;

            if (!string.IsNullOrEmpty(args.GroupName))
                dictPlist["GroupName"] = args.GroupName;

            if (!string.IsNullOrEmpty(args.ProcessType))
                dictPlist["ProcessType"] = args.ProcessType;

            if (args.StartCalendarInterval != null)
            {
                object objValue = args.StartCalendarInterval.GetValue();
                if (objValue != null)
                    dictPlist["StartCalendarInterval"] = objValue;
            }

            if (args.MachServices != null && args.MachServices.Count > 0)
                dictPlist["MachServices"] = args.MachServices;

            if (args.Sockets != null && args.Sockets.Count > 0)
                dictPlist["Sockets"] = args.Sockets;

            string strPlist = iTong.Device.CoreFoundation.CreatePlistString(dictPlist);
            if (!string.IsNullOrEmpty(cmdFormat))
            {
                strPlist = string.Format(strPlist, cmdFormat);
            }

            return CreatePlistFile(strPlist);
        }
    }


}
