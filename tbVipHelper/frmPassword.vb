﻿Imports iTong.CoreFoundation
Public Class frmPassword

#Region "--- 初始化 ---"
    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong

    End Sub
#End Region

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Try
            If String.IsNullOrEmpty(txtPwd.Text.Trim()) Then
                lblMsg.Text = "请输入秘钥"
                Return
            End If

            If txtPwd.Text.Trim() = VipHelper.GetAdminPassword() Then
                Me.DialogResult = Windows.Forms.DialogResult.OK
                Me.Close()
            Else
                lblMsg.Text = "密码错误"
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnOK_Click")
        End Try
    End Sub

End Class