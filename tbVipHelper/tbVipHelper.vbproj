﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9973B34C-7938-4C4A-AA05-C56DDF1994DB}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>tbVipHelper.My.MyApplication</StartupObject>
    <RootNamespace>tbVipHelper</RootNamespace>
    <AssemblyName>tbVipHelper</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DocumentationFile>tbVipHelper.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <DocumentationFile>tbVipHelper.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Resources\iTong.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="iTong.CoreReses" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="frmActivation.Designer.vb">
      <DependentUpon>frmActivation.vb</DependentUpon>
    </Compile>
    <Compile Include="frmActivation.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmAddDevice.Designer.vb">
      <DependentUpon>frmAddDevice.vb</DependentUpon>
    </Compile>
    <Compile Include="frmAddDevice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmCreatePremiumFeature.Designer.vb">
      <DependentUpon>frmCreatePremiumFeature.vb</DependentUpon>
    </Compile>
    <Compile Include="frmCreatePremiumFeature.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmPassword.Designer.vb">
      <DependentUpon>frmPassword.vb</DependentUpon>
    </Compile>
    <Compile Include="frmPassword.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmSearchDevice.Designer.vb">
      <DependentUpon>frmSearchDevice.vb</DependentUpon>
    </Compile>
    <Compile Include="frmSearchDevice.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVip.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmVip.Designer.vb">
      <DependentUpon>frmVip.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="VipHelper.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="frmActivation.resx">
      <DependentUpon>frmActivation.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmAddDevice.resx">
      <DependentUpon>frmAddDevice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmCreatePremiumFeature.resx">
      <DependentUpon>frmCreatePremiumFeature.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmPassword.resx">
      <DependentUpon>frmPassword.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmSearchDevice.resx">
      <DependentUpon>frmSearchDevice.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmVip.resx">
      <DependentUpon>frmVip.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Components\Components.vbproj">
      <Project>{28459798-5e28-45d3-aae3-74a9f1761d1a}</Project>
      <Name>Components</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc.csproj">
      <Project>{685ae51b-3c37-4b37-b3a2-b485d07a6e6b}</Project>
      <Name>CoreMisc</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses.csproj">
      <Project>{65ce9103-521a-49e6-a8cd-89137b452842}</Project>
      <Name>CoreReses</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS.csproj">
      <Project>{61c62ea1-6ad7-41be-98db-8abb77ee35f3}</Project>
      <Name>CoreUtilCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil.vbproj">
      <Project>{718b167f-1b33-4b7c-a7e3-e15615ddaac4}</Project>
      <Name>CoreUtil</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_1_transparent.png" />
    <None Include="Resources\btn_4_refresh.png" />
    <Content Include="Resources\btn_4_white.png" />
    <Content Include="Resources\btn_close.png" />
    <Content Include="Resources\btn_min.png" />
    <None Include="Resources\gif_loading_32.gif" />
    <None Include="Resources\frm_bg_blank.png" />
    <None Include="Resources\btn_setting.png" />
    <Content Include="Resources\frm_bg_state.png" />
    <None Include="Resources\iTong.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildbinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>