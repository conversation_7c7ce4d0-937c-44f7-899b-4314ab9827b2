﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmAddDevice
    Inherits iTong.Components.tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmAddDevice))
        Me.btnAdd = New iTong.Components.tbButton()
        Me.txtUdid = New iTong.Components.tbTextBox()
        Me.lblUdid = New System.Windows.Forms.Label()
        Me.lblMsg = New System.Windows.Forms.Label()
        Me.btn_close = New iTong.Components.tbButton()
        Me.SuspendLayout()
        '
        'btnAdd
        '
        Me.btnAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAdd.BackColor = System.Drawing.Color.Transparent
        Me.btnAdd.BindingForm = Nothing
        Me.btnAdd.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAdd.Location = New System.Drawing.Point(208, 110)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnAdd.Selectable = True
        Me.btnAdd.Size = New System.Drawing.Size(65, 23)
        Me.btnAdd.TabIndex = 38
        Me.btnAdd.tbAdriftIconWhenHover = False
        Me.btnAdd.tbAutoSize = False
        Me.btnAdd.tbAutoSizeEx = False
        Me.btnAdd.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnAdd.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnAdd.tbBadgeNumber = 0
        Me.btnAdd.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAdd.tbEndEllipsis = False
        Me.btnAdd.tbIconHoldPlace = True
        Me.btnAdd.tbIconImage = Nothing
        Me.btnAdd.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAdd.tbIconMore = False
        Me.btnAdd.tbIconMouseDown = Nothing
        Me.btnAdd.tbIconMouseHover = Nothing
        Me.btnAdd.tbIconMouseLeave = Nothing
        Me.btnAdd.tbIconPlaceText = 2
        Me.btnAdd.tbIconReadOnly = Nothing
        Me.btnAdd.tbImageMouseDown = Nothing
        Me.btnAdd.tbImageMouseHover = Nothing
        Me.btnAdd.tbImageMouseLeave = Nothing
        Me.btnAdd.tbProgressValue = 50
        Me.btnAdd.tbReadOnly = False
        Me.btnAdd.tbReadOnlyText = False
        Me.btnAdd.tbShadow = False
        Me.btnAdd.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnAdd.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnAdd.tbShowDot = False
        Me.btnAdd.tbShowMoreIconImg = CType(resources.GetObject("btnAdd.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAdd.tbShowNew = False
        Me.btnAdd.tbShowProgress = False
        Me.btnAdd.tbShowTip = True
        Me.btnAdd.tbShowToolTipOnButton = False
        Me.btnAdd.tbSplit = "13,11,13,11"
        Me.btnAdd.tbText = "添加"
        Me.btnAdd.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAdd.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAdd.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAdd.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAdd.tbTextMouseDownPlace = 0
        Me.btnAdd.tbToolTip = ""
        Me.btnAdd.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAdd.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAdd.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.VisibleEx = True
        '
        'txtUdid
        '
        Me.txtUdid.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtUdid.BackColor = System.Drawing.Color.White
        Me.txtUdid.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtUdid.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtUdid.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtUdid.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtUdid.ForeColor = System.Drawing.Color.Black
        Me.txtUdid.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtUdid.Location = New System.Drawing.Point(28, 71)
        Me.txtUdid.MaxLength = 256
        Me.txtUdid.Name = "txtUdid"
        Me.txtUdid.Size = New System.Drawing.Size(245, 21)
        Me.txtUdid.TabIndex = 37
        Me.txtUdid.Tag = Nothing
        Me.txtUdid.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtUdid.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtUdid.tbSelMark = True
        Me.txtUdid.tbTextBind = ""
        Me.txtUdid.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtUdid.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtUdid.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtUdid.TextTip = ""
        '
        'lblUdid
        '
        Me.lblUdid.AutoSize = True
        Me.lblUdid.BackColor = System.Drawing.Color.Transparent
        Me.lblUdid.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblUdid.Location = New System.Drawing.Point(28, 49)
        Me.lblUdid.Name = "lblUdid"
        Me.lblUdid.Size = New System.Drawing.Size(41, 12)
        Me.lblUdid.TabIndex = 36
        Me.lblUdid.Text = "udid："
        Me.lblUdid.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblMsg
        '
        Me.lblMsg.AutoSize = True
        Me.lblMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblMsg.ForeColor = System.Drawing.Color.Red
        Me.lblMsg.Location = New System.Drawing.Point(26, 110)
        Me.lblMsg.Name = "lblMsg"
        Me.lblMsg.Size = New System.Drawing.Size(0, 12)
        Me.lblMsg.TabIndex = 39
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(273, 3)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 40
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'frmAddDevice
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(300, 149)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.lblMsg)
        Me.Controls.Add(Me.btnAdd)
        Me.Controls.Add(Me.txtUdid)
        Me.Controls.Add(Me.lblUdid)
        Me.Name = "frmAddDevice"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.tbVipHelper.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "6,33,7,28"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "添加设备"
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnAdd As iTong.Components.tbButton
    Friend WithEvents txtUdid As iTong.Components.tbTextBox
    Friend WithEvents lblUdid As System.Windows.Forms.Label
    Friend WithEvents lblMsg As System.Windows.Forms.Label
    Friend WithEvents btn_close As iTong.Components.tbButton
End Class
