﻿Imports iTong.CoreFoundation
Public Class frmAddDevice

    Private mPostUrl As String = "http://pc.api.tongbu.com/vip/v.html?t=6"

#Region "--- 初始化 ---"
    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong

    End Sub
#End Region

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        Try
            If String.IsNullOrEmpty(txtUdid.Text.Trim()) Then
                Me.lblMsg.Text = "请输入当前服务设备的udid"
                Return
            End If
            Dim mail As String = VipHelper.GetConfigMail()
            Dim dicContent As New JsonObject
            dicContent.Add("mail", mail)
            dicContent.Add("projectid", 1)
            dicContent.Add("macid", VipHelper.GetMacid())
            dicContent.Add("hdd", Common.GetHardDiskID2())
            dicContent.Add("cpu", Common.GetProcessorId())
            dicContent.Add("wifimac", Common.GetMacAddress().Trim())
            dicContent.Add("phoneid", txtUdid.Text.Trim())
            Dim writer As New JsonWriter()
            dicContent.Write(writer)
            Dim strContent As String = writer.ToString()
            Dim strResult As String = VipHelper.PostData(mPostUrl, strContent)
            strResult = VipHelper.DecryptDES(strResult)
            Dim objJson As JsonObject = JsonParser.ParseString(strResult)
            '{"state": "1","data": :{"code":"-1失败，0不可用，1成功，2授权已过期,"msg":""}}
            If objJson IsNot Nothing AndAlso objJson.ContainsKey("data") Then
                Dim jsonData As JsonObject = CType(objJson("data"), JsonObject)
                Dim code As Integer = CType(jsonData("code"), JsonNumber).Value
                Dim msg As String = CType(jsonData("msg"), JsonString).Value
                If code = 1 Then
                    Me.DialogResult = Windows.Forms.DialogResult.OK
                    Me.Close()
                Else
                    lblMsg.Text = msg
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnAdd_Click")
        End Try
    End Sub
End Class