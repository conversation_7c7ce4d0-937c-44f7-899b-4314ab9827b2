﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmActivation
    Inherits iTong.Components.tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmActivation))
        Me.txtEmail = New iTong.Components.tbTextBox()
        Me.lblEmail = New System.Windows.Forms.Label()
        Me.lblLicense = New System.Windows.Forms.Label()
        Me.txtLicense = New iTong.Components.tbTextBox()
        Me.lblMsg = New System.Windows.Forms.Label()
        Me.btnActivation = New iTong.Components.tbButton()
        Me.btn_close = New iTong.Components.tbButton()
        Me.SuspendLayout()
        '
        'txtEmail
        '
        Me.txtEmail.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtEmail.BackColor = System.Drawing.Color.White
        Me.txtEmail.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtEmail.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtEmail.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtEmail.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtEmail.ForeColor = System.Drawing.Color.Black
        Me.txtEmail.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtEmail.Location = New System.Drawing.Point(41, 67)
        Me.txtEmail.MaxLength = 256
        Me.txtEmail.Name = "txtEmail"
        Me.txtEmail.Size = New System.Drawing.Size(215, 21)
        Me.txtEmail.TabIndex = 30
        Me.txtEmail.Tag = Nothing
        Me.txtEmail.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtEmail.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtEmail.tbSelMark = True
        Me.txtEmail.tbTextBind = ""
        Me.txtEmail.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtEmail.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtEmail.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtEmail.TextTip = ""
        '
        'lblEmail
        '
        Me.lblEmail.AutoSize = True
        Me.lblEmail.BackColor = System.Drawing.Color.Transparent
        Me.lblEmail.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblEmail.Location = New System.Drawing.Point(41, 42)
        Me.lblEmail.Name = "lblEmail"
        Me.lblEmail.Size = New System.Drawing.Size(173, 12)
        Me.lblEmail.TabIndex = 29
        Me.lblEmail.Text = "邮箱：(开通会员时填写的邮箱)"
        '
        'lblLicense
        '
        Me.lblLicense.AutoSize = True
        Me.lblLicense.BackColor = System.Drawing.Color.Transparent
        Me.lblLicense.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblLicense.Location = New System.Drawing.Point(41, 101)
        Me.lblLicense.Name = "lblLicense"
        Me.lblLicense.Size = New System.Drawing.Size(53, 12)
        Me.lblLicense.TabIndex = 29
        Me.lblLicense.Text = "激活码："
        '
        'txtLicense
        '
        Me.txtLicense.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtLicense.BackColor = System.Drawing.Color.White
        Me.txtLicense.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtLicense.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtLicense.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtLicense.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtLicense.ForeColor = System.Drawing.Color.Black
        Me.txtLicense.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtLicense.Location = New System.Drawing.Point(41, 126)
        Me.txtLicense.MaxLength = 256
        Me.txtLicense.Name = "txtLicense"
        Me.txtLicense.Size = New System.Drawing.Size(215, 21)
        Me.txtLicense.TabIndex = 30
        Me.txtLicense.Tag = Nothing
        Me.txtLicense.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtLicense.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtLicense.tbSelMark = True
        Me.txtLicense.tbTextBind = ""
        Me.txtLicense.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtLicense.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtLicense.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtLicense.TextTip = ""
        '
        'lblMsg
        '
        Me.lblMsg.AutoSize = True
        Me.lblMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblMsg.ForeColor = System.Drawing.Color.Red
        Me.lblMsg.Location = New System.Drawing.Point(43, 177)
        Me.lblMsg.Name = "lblMsg"
        Me.lblMsg.Size = New System.Drawing.Size(0, 12)
        Me.lblMsg.TabIndex = 29
        '
        'btnActivation
        '
        Me.btnActivation.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnActivation.BackColor = System.Drawing.Color.Transparent
        Me.btnActivation.BindingForm = Nothing
        Me.btnActivation.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnActivation.Location = New System.Drawing.Point(195, 172)
        Me.btnActivation.Name = "btnActivation"
        Me.btnActivation.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnActivation.Selectable = True
        Me.btnActivation.Size = New System.Drawing.Size(61, 23)
        Me.btnActivation.TabIndex = 32
        Me.btnActivation.tbAdriftIconWhenHover = False
        Me.btnActivation.tbAutoSize = False
        Me.btnActivation.tbAutoSizeEx = False
        Me.btnActivation.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnActivation.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnActivation.tbBadgeNumber = 0
        Me.btnActivation.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnActivation.tbEndEllipsis = False
        Me.btnActivation.tbIconHoldPlace = True
        Me.btnActivation.tbIconImage = Nothing
        Me.btnActivation.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivation.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnActivation.tbIconMore = False
        Me.btnActivation.tbIconMouseDown = Nothing
        Me.btnActivation.tbIconMouseHover = Nothing
        Me.btnActivation.tbIconMouseLeave = Nothing
        Me.btnActivation.tbIconPlaceText = 2
        Me.btnActivation.tbIconReadOnly = Nothing
        Me.btnActivation.tbImageMouseDown = Nothing
        Me.btnActivation.tbImageMouseHover = Nothing
        Me.btnActivation.tbImageMouseLeave = Nothing
        Me.btnActivation.tbProgressValue = 50
        Me.btnActivation.tbReadOnly = False
        Me.btnActivation.tbReadOnlyText = False
        Me.btnActivation.tbShadow = False
        Me.btnActivation.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnActivation.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnActivation.tbShowDot = False
        Me.btnActivation.tbShowMoreIconImg = CType(resources.GetObject("btnActivation.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnActivation.tbShowNew = False
        Me.btnActivation.tbShowProgress = False
        Me.btnActivation.tbShowTip = True
        Me.btnActivation.tbShowToolTipOnButton = False
        Me.btnActivation.tbSplit = "13,11,13,11"
        Me.btnActivation.tbText = "激活"
        Me.btnActivation.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivation.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnActivation.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnActivation.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnActivation.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnActivation.tbTextMouseDownPlace = 0
        Me.btnActivation.tbToolTip = ""
        Me.btnActivation.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnActivation.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnActivation.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivation.VisibleEx = True
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(267, 3)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 31
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'frmActivation
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(300, 223)
        Me.Controls.Add(Me.btnActivation)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.txtLicense)
        Me.Controls.Add(Me.lblMsg)
        Me.Controls.Add(Me.lblLicense)
        Me.Controls.Add(Me.txtEmail)
        Me.Controls.Add(Me.lblEmail)
        Me.Name = "frmActivation"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.tbVipHelper.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitlemark = True
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "6,33,7,28"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "激活VIP客服服务"
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents txtEmail As iTong.Components.tbTextBox
    Friend WithEvents lblEmail As System.Windows.Forms.Label
    Friend WithEvents lblLicense As System.Windows.Forms.Label
    Friend WithEvents txtLicense As iTong.Components.tbTextBox
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents btnActivation As iTong.Components.tbButton
    Friend WithEvents lblMsg As System.Windows.Forms.Label
End Class
