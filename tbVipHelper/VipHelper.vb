﻿Imports System.Net
Imports System.IO
Imports System.Text
Imports iTong.CoreFoundation
Imports System.Management
Imports System.Runtime.InteropServices

Public Class VipHelper
    Private Shared desKey As String = "v[ip2016"
    Private Shared desUV As String = "abcdi8fd"
    Public Shared mailDesKey As String = "am58!2#0"
    Public Shared Function PostData(url As String, strContent As String) As String
        Dim strTemp As String = ""
        Dim blnRetry As Boolean = False
DoRetry:

        Try
            '加密
            Dim strEncyptContent As String = Common.EncryptDES(strContent, desKey, desUV).Replace("+", "_@_")
            Dim buffer As Byte() = Encoding.UTF8.GetBytes(strEncyptContent)
            Dim req As WebRequest = WebRequest.Create(url)
            req.Method = "POST"
            req.Timeout = 60000
            'req.ContentType = "multipart/form-data; boundary=boundary";
            req.ContentType = "application/x-www-form-urlencoded"
            '在向服务器发送大量的文本、包含非ASCII字符的文本或二进制数据时这种编码方式效率很低
            req.ContentLength = buffer.Length
            Using stream As Stream = req.GetRequestStream()
                stream.Write(buffer, 0, buffer.Length)
            End Using
            Dim response As WebResponse = req.GetResponse()
            Dim reader As New IO.StreamReader(response.GetResponseStream(), Encoding.UTF8)
            strTemp = reader.ReadToEnd()
            reader.Close()
            response.Close()
        Catch ex As Exception
            If Not blnRetry AndAlso (ex.Message.Contains("405") OrElse ex.Message.Contains("502") OrElse ex.Message.Contains("无法解析此远程名称")) Then
                blnRetry = True
                GoTo DoRetry
            End If

            strTemp = "error"
            Common.LogException(ex.ToString(), "VipHelper PostData")
        End Try

        Return strTemp
    End Function

    Public Shared Function DecryptDES(ByVal strContent As String) As String
        Dim strResult As String = ""
        If String.IsNullOrEmpty(strContent) Then
            Return strResult
        End If

        strResult = strContent.Replace("_@_", "+")
        strResult = Common.DecryptDES(strResult, desKey, desUV)
        Return strResult
    End Function
    Private Shared _IniPath As String = ""
    Public Shared Function GetIniPath() As String
        If Not String.IsNullOrEmpty(_IniPath) Then
            Return _IniPath
        End If

        Dim strFolder As String = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "tbVipHelper")
        Folder.CheckFolder(strFolder)
        _IniPath = Path.Combine(strFolder, "Setting.ini")
        Return _IniPath
    End Function

    Public Shared Function GetConfigMail() As String
        Return Common.DecryptDES(IniClass.GetIniSectionKey("Setting", "1", GetIniPath()), mailDesKey, mailDesKey)
    End Function

    Public Shared Sub SetConfigMail(ByVal strMail As String)
        IniClass.SetIniSectionKey("Setting", "1", Common.EncryptDES(strMail, mailDesKey, mailDesKey), GetIniPath())
    End Sub


    Public Shared Function GetAdminPassword() As String
        Dim strRelust As String = IniClass.GetIniSectionKey("Setting", "tbzsvip_adminaccount", Folder.ConfigServerIniFile)
        If String.IsNullOrEmpty(strRelust) Then
            strRelust = "tbzs2017vip"
        End If
        Return strRelust
    End Function

    ''' <summary>
    ''' 计算电脑的macid
    ''' </summary>
    ''' <remarks></remarks>
    Private Shared _Macid As String = ""
    Public Shared Function GetMacid() As String
        If Not String.IsNullOrEmpty(_Macid) Then
            Return _Macid
        End If
        Dim strHardDiskID As String = Common.GetHardDiskID2() '硬盘序列号
        Dim strCpuId As String = Common.GetProcessorId().Trim()  '处理器ID
        Dim strMacAddress As String = Common.GetMacAddress().Trim()
        Dim strCombine As String = "tb" + strHardDiskID + strCpuId + strMacAddress
        _Macid = Common.GetMd5Hex(strCombine).ToLower()
        Return _Macid
    End Function

    ''' <summary>
    ''' 获取硬盘序列号
    ''' </summary>
    ''' <remarks></remarks>
    Private Shared _HardDiskID As String = ""
    Public Shared Function GetHardDiskId() As String
        If Not String.IsNullOrEmpty(_HardDiskID) Then
            Return _HardDiskID
        End If

        Using cimobject1 As New ManagementClass("Win32_DiskDrive")
            Dim moc1 As ManagementObjectCollection = cimobject1.GetInstances()
            For Each mo As ManagementObject In moc1
                _HardDiskID = DirectCast(mo.Properties("SerialNumber").Value, String)
                mo.Dispose()
            Next
        End Using
        Return _HardDiskID.ToString()
    End Function

    ''' <summary>
    ''' 读取cpu
    ''' </summary>
    ''' <remarks></remarks>
    Private Shared _CpuId As String = ""
    Public Shared Function GetCpuId() As String
        If Not String.IsNullOrEmpty(_CpuId) Then
            Return _CpuId
        End If

        Dim mc As ManagementClass = New ManagementClass("Win32_Processor")
        Dim moc As ManagementObjectCollection = mc.GetInstances()
        For Each mo As ManagementObject In moc
            _CpuId = mo.Properties("ProcessorId").Value.ToString()
        Next
        moc = Nothing
        mc = Nothing
        Return _CpuId
    End Function

    <DllImport("iphlpapi.dll")> _
    Private Shared Function GetAdaptersAddresses(Family As UInt32, Flags As UInt32, Reserved As IntPtr, pAdapterAddresses As IntPtr, ByRef pOutBufLen As UInt32) As [ERROR]
    End Function

    Public Enum [ERROR]
        ERROR_SUCCESS = 0
        ERROR_NO_DATA = 232
        ERROR_BUFFER_OVERFLOW = 111
        ERROR_INVALID_PARAMETER = 87
    End Enum

    Public Enum FAMILY
        ''' <summary>
        ''' IPv4
        ''' </summary>
        AF_INET = 2
        ''' <summary>
        ''' IPv6
        ''' </summary>
        AF_INET6 = 23
        ''' <summary>
        ''' Unpecified. Includes both IPv4 and IPv4
        ''' </summary>
        AF_UNSPEC = 0
    End Enum

    Public Enum IF_TYPE
        IF_TYPE_OTHER = 1
        ' None of the below
        IF_TYPE_REGULAR_1822 = 2
        IF_TYPE_HDH_1822 = 3
        IF_TYPE_DDN_X25 = 4
        IF_TYPE_RFC877_X25 = 5
        IF_TYPE_ETHERNET_CSMACD = 6
        IF_TYPE_IS088023_CSMACD = 7
        IF_TYPE_ISO88024_TOKENBUS = 8
        IF_TYPE_ISO88025_TOKENRING = 9
        IF_TYPE_ISO88026_MAN = 10
        IF_TYPE_STARLAN = 11
        IF_TYPE_PROTEON_10MBIT = 12
        IF_TYPE_PROTEON_80MBIT = 13
        IF_TYPE_HYPERCHANNEL = 14
        IF_TYPE_FDDI = 15
        IF_TYPE_LAP_B = 16
        IF_TYPE_SDLC = 17
        IF_TYPE_DS1 = 18
        ' DS1-MIB
        IF_TYPE_E1 = 19
        ' Obsolete; see DS1-MIB
        IF_TYPE_BASIC_ISDN = 20
        IF_TYPE_PRIMARY_ISDN = 21
        IF_TYPE_PROP_POINT2POINT_SERIAL = 22
        ' proprietary serial
        IF_TYPE_PPP = 23
        IF_TYPE_SOFTWARE_LOOPBACK = 24
        IF_TYPE_EON = 25
        ' CLNP over IP
        IF_TYPE_ETHERNET_3MBIT = 26
        IF_TYPE_NSIP = 27
        ' XNS over IP
        IF_TYPE_SLIP = 28
        ' Generic Slip
        IF_TYPE_ULTRA = 29
        ' ULTRA Technologies
        IF_TYPE_DS3 = 30
        ' DS3-MIB
        IF_TYPE_SIP = 31
        ' SMDS, coffee
        IF_TYPE_FRAMERELAY = 32
        ' DTE only
        IF_TYPE_RS232 = 33
        IF_TYPE_PARA = 34
        ' Parallel port
        IF_TYPE_ARCNET = 35
        IF_TYPE_ARCNET_PLUS = 36
        IF_TYPE_ATM = 37
        ' ATM cells
        IF_TYPE_MIO_X25 = 38
        IF_TYPE_SONET = 39
        ' SONET or SDH
        IF_TYPE_X25_PLE = 40
        IF_TYPE_ISO88022_LLC = 41
        IF_TYPE_LOCALTALK = 42
        IF_TYPE_SMDS_DXI = 43
        IF_TYPE_FRAMERELAY_SERVICE = 44
        ' FRNETSERV-MIB
        IF_TYPE_V35 = 45
        IF_TYPE_HSSI = 46
        IF_TYPE_HIPPI = 47
        IF_TYPE_MODEM = 48
        ' Generic Modem
        IF_TYPE_AAL5 = 49
        ' AAL5 over ATM
        IF_TYPE_SONET_PATH = 50
        IF_TYPE_SONET_VT = 51
        IF_TYPE_SMDS_ICIP = 52
        ' SMDS InterCarrier Interface
        IF_TYPE_PROP_VIRTUAL = 53
        ' Proprietary virtual/internal
        IF_TYPE_PROP_MULTIPLEXOR = 54
        ' Proprietary multiplexing
        IF_TYPE_IEEE80212 = 55
        ' 100BaseVG
        IF_TYPE_FIBRECHANNEL = 56
        IF_TYPE_HIPPIINTERFACE = 57
        IF_TYPE_FRAMERELAY_INTERCONNECT = 58
        ' Obsolete, use 32 or 44
        IF_TYPE_AFLANE_8023 = 59
        ' ATM Emulated LAN for 802.3
        IF_TYPE_AFLANE_8025 = 60
        ' ATM Emulated LAN for 802.5
        IF_TYPE_CCTEMUL = 61
        ' ATM Emulated circuit
        IF_TYPE_FASTETHER = 62
        ' Fast Ethernet (100BaseT)
        IF_TYPE_ISDN = 63
        ' ISDN and X.25
        IF_TYPE_V11 = 64
        ' CCITT V.11/X.21
        IF_TYPE_V36 = 65
        ' CCITT V.36
        IF_TYPE_G703_64K = 66
        ' CCITT G703 at 64Kbps
        IF_TYPE_G703_2MB = 67
        ' Obsolete; see DS1-MIB
        IF_TYPE_QLLC = 68
        ' SNA QLLC
        IF_TYPE_FASTETHER_FX = 69
        ' Fast Ethernet (100BaseFX)
        IF_TYPE_CHANNEL = 70
        IF_TYPE_IEEE80211 = 71
        ' Radio spread spectrum
        IF_TYPE_IBM370PARCHAN = 72
        ' IBM System 360/370 OEMI Channel
        IF_TYPE_ESCON = 73
        ' IBM Enterprise Systems Connection
        IF_TYPE_DLSW = 74
        ' Data Link Switching
        IF_TYPE_ISDN_S = 75
        ' ISDN S/T interface
        IF_TYPE_ISDN_U = 76
        ' ISDN U interface
        IF_TYPE_LAP_D = 77
        ' Link Access Protocol D
        IF_TYPE_IPSWITCH = 78
        ' IP Switching Objects
        IF_TYPE_RSRB = 79
        ' Remote Source Route Bridging
        IF_TYPE_ATM_LOGICAL = 80
        ' ATM Logical Port
        IF_TYPE_DS0 = 81
        ' Digital Signal Level 0
        IF_TYPE_DS0_BUNDLE = 82
        ' Group of ds0s on the same ds1
        IF_TYPE_BSC = 83
        ' Bisynchronous Protocol
        IF_TYPE_ASYNC = 84
        ' Asynchronous Protocol
        IF_TYPE_CNR = 85
        ' Combat Net Radio
        IF_TYPE_ISO88025R_DTR = 86
        ' ISO 802.5r DTR
        IF_TYPE_EPLRS = 87
        ' Ext Pos Loc Report Sys
        IF_TYPE_ARAP = 88
        ' Appletalk Remote Access Protocol
        IF_TYPE_PROP_CNLS = 89
        ' Proprietary Connectionless Proto
        IF_TYPE_HOSTPAD = 90
        ' CCITT-ITU X.29 PAD Protocol
        IF_TYPE_TERMPAD = 91
        ' CCITT-ITU X.3 PAD Facility
        IF_TYPE_FRAMERELAY_MPI = 92
        ' Multiproto Interconnect over FR
        IF_TYPE_X213 = 93
        ' CCITT-ITU X213
        IF_TYPE_ADSL = 94
        ' Asymmetric Digital Subscrbr Loop
        IF_TYPE_RADSL = 95
        ' Rate-Adapt Digital Subscrbr Loop
        IF_TYPE_SDSL = 96
        ' Symmetric Digital Subscriber Loop
        IF_TYPE_VDSL = 97
        ' Very H-Speed Digital Subscrb Loop
        IF_TYPE_ISO88025_CRFPRINT = 98
        ' ISO 802.5 CRFP
        IF_TYPE_MYRINET = 99
        ' Myricom Myrinet
        IF_TYPE_VOICE_EM = 100
        ' Voice recEive and transMit
        IF_TYPE_VOICE_FXO = 101
        ' Voice Foreign Exchange Office
        IF_TYPE_VOICE_FXS = 102
        ' Voice Foreign Exchange Station
        IF_TYPE_VOICE_ENCAP = 103
        ' Voice encapsulation
        IF_TYPE_VOICE_OVERIP = 104
        ' Voice over IP encapsulation
        IF_TYPE_ATM_DXI = 105
        ' ATM DXI
        IF_TYPE_ATM_FUNI = 106
        ' ATM FUNI
        IF_TYPE_ATM_IMA = 107
        ' ATM IMA
        IF_TYPE_PPPMULTILINKBUNDLE = 108
        ' PPP Multilink Bundle
        IF_TYPE_IPOVER_CDLC = 109
        ' IBM ipOverCdlc
        IF_TYPE_IPOVER_CLAW = 110
        ' IBM Common Link Access to Workstn
        IF_TYPE_STACKTOSTACK = 111
        ' IBM stackToStack
        IF_TYPE_VIRTUALIPADDRESS = 112
        ' IBM VIPA
        IF_TYPE_MPC = 113
        ' IBM multi-proto channel support
        IF_TYPE_IPOVER_ATM = 114
        ' IBM ipOverAtm
        IF_TYPE_ISO88025_FIBER = 115
        ' ISO 802.5j Fiber Token Ring
        IF_TYPE_TDLC = 116
        ' IBM twinaxial data link control
        IF_TYPE_GIGABITETHERNET = 117
        IF_TYPE_HDLC = 118
        IF_TYPE_LAP_F = 119
        IF_TYPE_V37 = 120
        IF_TYPE_X25_MLP = 121
        ' Multi-Link Protocol
        IF_TYPE_X25_HUNTGROUP = 122
        ' X.25 Hunt Group
        IF_TYPE_TRANSPHDLC = 123
        IF_TYPE_INTERLEAVE = 124
        ' Interleave channel
        IF_TYPE_FAST = 125
        ' Fast channel
        IF_TYPE_IP = 126
        ' IP (for APPN HPR in IP networks)
        IF_TYPE_DOCSCABLE_MACLAYER = 127
        ' CATV Mac Layer
        IF_TYPE_DOCSCABLE_DOWNSTREAM = 128
        ' CATV Downstream interface
        IF_TYPE_DOCSCABLE_UPSTREAM = 129
        ' CATV Upstream interface
        IF_TYPE_A12MPPSWITCH = 130
        ' Avalon Parallel Processor
        IF_TYPE_TUNNEL = 131
        ' Encapsulation interface
        IF_TYPE_COFFEE = 132
        ' Coffee pot
        IF_TYPE_CES = 133
        ' Circuit Emulation Service
        IF_TYPE_ATM_SUBINTERFACE = 134
        ' ATM Sub Interface
        IF_TYPE_L2_VLAN = 135
        ' Layer 2 Virtual LAN using 802.1Q
        IF_TYPE_L3_IPVLAN = 136
        ' Layer 3 Virtual LAN using IP
        IF_TYPE_L3_IPXVLAN = 137
        ' Layer 3 Virtual LAN using IPX
        IF_TYPE_DIGITALPOWERLINE = 138
        ' IP over Power Lines
        IF_TYPE_MEDIAMAILOVERIP = 139
        ' Multimedia Mail over IP
        IF_TYPE_DTM = 140
        ' Dynamic syncronous Transfer Mode
        IF_TYPE_DCN = 141
        ' Data Communications Network
        IF_TYPE_IPFORWARD = 142
        ' IP Forwarding Interface
        IF_TYPE_MSDSL = 143
        ' Multi-rate Symmetric DSL
        IF_TYPE_IEEE1394 = 144
        ' IEEE1394 High Perf Serial Bus
        IF_TYPE_RECEIVE_ONLY = 145
        ' TV adapter type
    End Enum

    Public Enum IF_OPER_STATUS
        IfOperStatusUp = 1
        IfOperStatusDown
        IfOperStatusTesting
        IfOperStatusUnknown
        IfOperStatusDormant
        IfOperStatusNotPresent
        IfOperStatusLowerLayerDown
    End Enum

    <StructLayout(LayoutKind.Sequential)> _
    Public Structure SOCKET_ADDRESS
        Public lpSockAddr As IntPtr
        Public iSockaddrLength As Integer
    End Structure


    Public Enum NET_IF_CONNECTION_TYPE
        NET_IF_CONNECTION_DEDICATED = 1
        NET_IF_CONNECTION_PASSIVE = 2
        NET_IF_CONNECTION_DEMAND = 3
        NET_IF_CONNECTION_MAXIMUM = 4
    End Enum

    Public Enum TUNNEL_TYPE
        TUNNEL_TYPE_NONE = 0
        TUNNEL_TYPE_OTHER = 1
        TUNNEL_TYPE_DIRECT = 2
        TUNNEL_TYPE_6TO4 = 11
        TUNNEL_TYPE_ISATAP = 13
        TUNNEL_TYPE_TEREDO = 14
        TUNNEL_TYPE_IPHTTPS = 15
    End Enum

    Private Const MAX_ADAPTER_ADDRESS_LENGTH As Integer = 8
    Private Const MAX_DHCPV6_DUID_LENGTH As Integer = 130

    <StructLayout(LayoutKind.Sequential)> _
    Public Structure IP_ADAPTER_ADDRESSES
        Public Alignment As UInt64
        Public [Next] As IntPtr
        Public AdapterName As IntPtr
        Public FirstUnicastAddress As IntPtr
        Public FirstAnycastAddress As IntPtr
        Public FirstMulticastAddress As IntPtr
        Public FirstDnsServerAddress As IntPtr

        <MarshalAs(UnmanagedType.LPWStr)> _
        Public DnsSuffix As String
        <System.Runtime.InteropServices.MarshalAs(UnmanagedType.LPWStr)> _
        Public Description As String
        <System.Runtime.InteropServices.MarshalAs(UnmanagedType.LPWStr)> _
        Public FriendlyName As String
        <MarshalAs(UnmanagedType.ByValArray, SizeConst:=MAX_ADAPTER_ADDRESS_LENGTH)> _
        Public PhysicalAddress As Byte()

        Public PhysicalAddressLength As UInt32
        Public Flags As UInt32
        Public Mtu As UInt32
        Public IfType As IF_TYPE
        Public OperStatus As IF_OPER_STATUS
        Private Ipv6IfIndex As UInt32
        <MarshalAs(UnmanagedType.ByValArray, SizeConst:=16)> _
        Public ZoneIndices As UInt32()
        Public FirstPrefix As IntPtr

        ' Items added for Vista
        ' May need to be removed on Windows versions below Vista to work properly (?)
        Public TrasmitLinkSpeed As UInt64
        Public ReceiveLinkSpeed As UInt64
        Public FirstWinsServerAddress As IntPtr
        Public FirstGatewayAddress As IntPtr
        Public Ipv4Metric As UInt32
        Public Ipv6Metric As UInt32
        Public Luid As UInt64
        Public Dhcpv4Server As SOCKET_ADDRESS
        Public CompartmentId As UInt32
        Public NetworkGuid As Guid
        Public ConnectionType As NET_IF_CONNECTION_TYPE
        Public TunnelType As TUNNEL_TYPE
        Public Dhcpv6Server As SOCKET_ADDRESS
        <MarshalAs(UnmanagedType.ByValArray, SizeConst:=MAX_DHCPV6_DUID_LENGTH)> _
        Public Dhcpv6ClientDuid As Byte()
        Public Dhcpv6ClientDuidLength As UInt32
        Public Dhcpv6Iaid As UInt32
        Public FirstDnsSuffix As UInt32
    End Structure
    Public Shared Function GetIPAdapters() As String
        Dim strMac As String = String.Empty

        Try
            Dim outBufLen As UInt32 = 0
            Dim err As [ERROR] = GetAdaptersAddresses(System.Convert.ToUInt32(FAMILY.AF_UNSPEC), CUInt(0), IntPtr.Zero, IntPtr.Zero, outBufLen)

            If [ERROR].ERROR_BUFFER_OVERFLOW = err Then
                Dim pAdapterAddresses As IntPtr = Marshal.AllocHGlobal(System.Convert.ToInt32(outBufLen))
                Try
                    err = GetAdaptersAddresses(System.Convert.ToUInt32(FAMILY.AF_INET), CUInt(0), IntPtr.Zero, pAdapterAddresses, outBufLen)
                    If [ERROR].ERROR_SUCCESS = err Then
                        Dim currPtr As IntPtr = pAdapterAddresses
                        'While IntPtr.Zero <> currPtr
                        Dim pAddress As IP_ADAPTER_ADDRESSES = DirectCast(Marshal.PtrToStructure(currPtr, GetType(IP_ADAPTER_ADDRESSES)), IP_ADAPTER_ADDRESSES)
                        Dim listAddress As New List(Of Byte)()
                        For intI As Integer = 0 To pAddress.PhysicalAddressLength - 1
                            listAddress.Add(pAddress.PhysicalAddress(intI))
                        Next

                        'for (int intI = 0; intI <= pAddress.PhysicalAddressLength - 1; intI++)
                        '{
                        '    strMac += Convert.ToString(pAddress.PhysicalAddress[intI], 16).ToUpper().PadLeft(2, "0");
                        '}

                        'currPtr = addr.[Next]
                        'End While
                        strMac = Common.ToHexString(listAddress.ToArray())
                    End If
                Finally
                    Marshal.FreeHGlobal(pAdapterAddresses)
                End Try

            End If
        Catch exA As Exception
            Common.LogException(exA.ToString(), "MacInfo.GetIPAdapters")
        End Try

        If String.IsNullOrEmpty(strMac) Then
            ' "000000000000"
            strMac = Common.GetMacAddress()
        End If

        Return strMac
    End Function

End Class

Public Class VipUserInfo
    Public mail As String = ""
    Public status As String = ""
    Public starttime As String = ""
    Public endtime As String = ""
    Public computer As Integer = 0
    Public device As Integer = 0
    Public vipType As VipType
    Public listBindingComputer As List(Of VipBaseComputer)
    Public listBindingDevice As List(Of String)
End Class

Public Class VipType
    Public projectId As Integer = 0
    Public id As Integer = 0
    Public month As Integer = 0
    Public computer As Integer = 0
    Public device As Integer = 0
    Public money As Single = 0
End Class

Public Class VipBaseComputer
    Public macid As String = ""
    Public hdd As String = ""
    Public cpu As String = ""
    Public wifimac As String = ""
End Class
