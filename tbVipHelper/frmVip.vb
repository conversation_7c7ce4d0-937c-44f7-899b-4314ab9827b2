﻿Imports System.Text
Imports System.Text.RegularExpressions
Imports iTong.CoreFoundation
Imports System.IO

Public Class frmVip
    Dim mPostUrl As String = "http://pc.api.tongbu.com/vip/v.html?t=4"
    Dim mUrlGetUserInfo As String = "http://pc.api.tongbu.com/vip/v.html?t=2&mail={0}&projectid={1}"
    Dim mail As String = VipHelper.GetConfigMail()

#Region "--- 初始化 ---"
    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong
        Me.Text = String.Format("{0} v{1}", Me.Text, My.Application.Info.Version().ToString())
    End Sub
#End Region

#Region "--- 窗体事件 ---"
    Private Sub frmVip_Shown(sender As Object, e As EventArgs) Handles MyBase.Shown
        btnRefresh.Location = New Point(lblWelcome.Right + 50, btnRefresh.Top)
        If String.IsNullOrEmpty(mail) Then
            lblMailMsg.Visible = False
            LoadGotoActivation()
        Else
            lblMailMsg.Visible = True
            'lblMailMsg.Text = "您当前使用的邮箱是: " & VipHelper.GetConfigMail()
            txtMail.Text = VipHelper.GetConfigMail()
            LoadDetailInfo()
        End If
    End Sub

    Private Sub btnActivation_Click(sender As Object, e As EventArgs) Handles btnActivation.Click
        Dim frm As frmActivation = New frmActivation()
        frm.ShowDialog()
        If frm.DialogResult = Windows.Forms.DialogResult.OK Then
            lblMailMsg.Visible = True
            txtMail.Text = VipHelper.GetConfigMail()
            LoadDetailInfo()
        End If

    End Sub

    Private Sub btnRenew_Click(sender As Object, e As EventArgs) Handles btnRenew.Click
        Common.OpenExplorer("http://zs.tongbu.com/vip.html")
    End Sub

    Private Sub btnAddDevice_Click(sender As Object, e As EventArgs) Handles btnSearchDevice.Click
        Dim frm As frmSearchDevice = New frmSearchDevice()
        frm.ShowDialog()
        If frm.DialogResult = Windows.Forms.DialogResult.OK Then
            LoadDetailInfo()
        End If

    End Sub

    Private Sub btnGetPCInfo_Click(sender As Object, e As EventArgs) Handles btnGetPCInfo.Click
        Try
            Dim strFolder As String = Path.Combine(Folder.AppFolder, "MacInfo")
            If Directory.Exists(strFolder) Then
                Directory.Delete(strFolder, True)
            End If

            Folder.CheckFolder(strFolder)
            Dim strFilePath As String = Path.Combine(strFolder, String.Format("Computer at {0}.txt", DateTime.Now.ToString("yyyyMMdd")))
            Dim hardDiskId As String = Common.EncryptDES("DiskId=" & Common.GetHardDiskID2(), VipHelper.mailDesKey, VipHelper.mailDesKey)
            Dim cpuid As String = Common.EncryptDES("CpuId=" & Common.GetProcessorId(), VipHelper.mailDesKey, VipHelper.mailDesKey)
            Dim macAddress As String = Common.EncryptDES("WifiMac=" & Common.GetMacAddress(), VipHelper.mailDesKey, VipHelper.mailDesKey)
            Dim macid As String = Common.EncryptDES("MacId=" & VipHelper.GetMacid(), VipHelper.mailDesKey, VipHelper.mailDesKey)
            Dim allInfo As String = String.Format("{0}_@_{1}_@_{2}_@_{3}", hardDiskId, cpuid, macAddress, macid)
            If File.Exists(strFilePath) Then
                File.Delete(strFilePath)
            End If
            Using sw As New StreamWriter(strFilePath, True, Encoding.UTF8)
                'sw.WriteLine(hardDiskId)
                'sw.WriteLine(cpuid)
                'sw.WriteLine(macAddress)
                'sw.WriteLine(macid)
                'sw.WriteLine("")
                sw.WriteLine(allInfo)
            End Using
            Dim strNeedInfo As String = VipHelper.GetConfigMail() & Environment.NewLine & allInfo
            Clipboard.SetText(strNeedInfo)
            Common.OpenExplorer(strFolder)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnGetPCInfo_Click")
        End Try
    End Sub

    Private Sub btnContact_Click(sender As Object, e As EventArgs) Handles btnContact.Click
        Try
            Common.OpenExplorer("http://wpa.b.qq.com/cgi/wpa.php?ln=1&key=XzkzODAxNDg3NF80NDU1NDZfNDAwOTk4MTM4OV8yXw")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "lblContact_Click")
        End Try
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        rtxContent.Clear()
        If String.IsNullOrEmpty(mail) Then
            lblMailMsg.Visible = False
            LoadGotoActivation()
        Else
            'lblMailMsg.Text = "您当前使用的邮箱是: " & VipHelper.GetConfigMail()
            lblMailMsg.Visible = True
            txtMail.Text = VipHelper.GetConfigMail()
            LoadDetailInfo()
        End If
    End Sub

    Private Sub btnAddDevice_Click_1(sender As Object, e As EventArgs) Handles btnAddDevice.Click
        Try
            Dim frm As frmPassword = New frmPassword()
            frm.ShowDialog()
            If frm.DialogResult = Windows.Forms.DialogResult.OK Then
                Dim frmSetting As frmCreatePremiumFeature = New frmCreatePremiumFeature()
                frmSetting.ShowDialog()
                If frmSetting.DialogResult = Windows.Forms.DialogResult.OK Then
                    LoadDetailInfo()
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnAddDevice_Click_1")
        End Try
    End Sub
#End Region

#Region "--- 私有方法 ---"

    Private Sub bgwGetInfo_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles bgwGetInfo.DoWork
        Try
            SetPnlLoadVisible(True)
            SetRtxContentVisible(False)
            SetLblWelcome("感谢您激活了同步推VIP服务")
            mail = VipHelper.GetConfigMail()
            Dim dicContent As New JsonObject
            dicContent.Add("mail", mail)
            dicContent.Add("projectid", 1)
            dicContent.Add("macid", VipHelper.GetMacid())
            dicContent.Add("hdd", Common.GetHardDiskID2())
            dicContent.Add("cpu", Common.GetProcessorId())
            dicContent.Add("wifimac", Common.GetMacAddress().Trim())

            Dim writer As New JsonWriter()
            dicContent.Write(writer)
            Dim strContent As String = writer.ToString()
            Dim strResult As String = VipHelper.PostData(mPostUrl, strContent)
            strResult = VipHelper.DecryptDES(strResult)
            Dim objJson As JsonObject = JsonParser.ParseString(strResult)
            If objJson Is Nothing OrElse Not objJson.ContainsKey("data") Then
                SetRtxContentVisible(True)
                SetPnlLoadVisible(False)
                SetLblWelcome("获取信息失败，请联系客服")
                Return
            End If

            Dim jsonData As JsonObject = CType(objJson("data"), JsonObject)
            Dim code As Integer = CType(jsonData("code"), JsonNumber).Value
            Dim msg As String = CType(jsonData("msg"), JsonString).Value
            SetRtxContentVisible(True)
            SetPnlLoadVisible(False)
            If code = 1 Then
                Dim userInfo As VipUserInfo = GetUserInfo(mail)
                If userInfo Is Nothing Then
                    ShowErrorGetInfo()
                Else
                    ShowUserInfo(userInfo)
                End If

            Else
                SetLblWelcome("获取当前电脑是否可用: " + msg)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwGetInfo_DoWork")
        End Try
    End Sub

    Private Sub LoadDetailInfo()
        If Me.bgwGetInfo.IsBusy Then
            Return
        Else
            Me.bgwGetInfo.RunWorkerAsync()
        End If

    End Sub

    Private Function GetUserInfo(ByVal strMail As String) As VipUserInfo
        Dim vipUserInfo As VipUserInfo = New VipUserInfo()
        Try
            Dim strContent As String = Utility.GetContentStringFromUrl(String.Format(mUrlGetUserInfo, strMail, 1), Encoding.UTF8)
            strContent = VipHelper.DecryptDES(strContent)
            If strContent Is Nothing OrElse String.IsNullOrEmpty(strContent) Then
                strContent = Utility.GetContentStringFromUrl(mUrlGetUserInfo, Encoding.UTF8)
            End If
            If strContent Is Nothing OrElse String.IsNullOrEmpty(strContent) Then
                rtxContent.Text = "无法获取到当前账号的授权信息"
                Return VipUserInfo
            End If

            Dim objJson As JsonObject = JsonParser.ParseString(strContent)
            Dim jsonData As JsonObject = CType(objJson("data"), JsonObject)

            VipUserInfo.mail = CType(jsonData("mail"), JsonString).Value
            vipUserInfo.starttime = DateTime.Parse(CType(jsonData("starttime"), JsonString).Value).ToString("yyyy-MM-dd")
            vipUserInfo.endtime = DateTime.Parse(CType(jsonData("endtime"), JsonString).Value).ToString("yyyy-MM-dd")
            VipUserInfo.computer = CType(jsonData("computer"), JsonNumber).Value
            VipUserInfo.device = CType(jsonData("device"), JsonNumber).Value
            VipUserInfo.status = CType(jsonData("status"), JsonNumber).Value

            '套餐类型
            Dim vipType As New VipType()
            Dim jsonVipType As JsonObject = CType(jsonData("vipType"), JsonObject)
            vipType.month = CType(jsonVipType("month"), JsonNumber).Value
            vipType.computer = CType(jsonVipType("computer"), JsonNumber).Value
            vipType.device = CType(jsonVipType("device"), JsonNumber).Value
            vipType.money = CType(jsonVipType("money"), JsonNumber).Value
            vipUserInfo.vipType = vipType

            '已绑定的电脑信息
            Dim listBindComputer As New List(Of VipBaseComputer)
            Dim arrayComputer As JsonArray = CType(jsonData("computers"), JsonArray)
            If arrayComputer IsNot Nothing AndAlso arrayComputer.Count > 0 Then
                For Each joComputer As JsonObject In arrayComputer
                    Dim computerInfo As New VipBaseComputer()
                    computerInfo.macid = CType(joComputer("macid"), JsonString).Value
                    computerInfo.hdd = CType(joComputer("hdd"), JsonString).Value
                    computerInfo.cpu = CType(joComputer("cpu"), JsonString).Value
                    computerInfo.wifimac = CType(joComputer("wifimac"), JsonString).Value
                    listBindComputer.Add(computerInfo)
                Next
            End If
            VipUserInfo.listBindingComputer = listBindComputer

            '已绑定的设备
            Dim listBindDevice As New List(Of String)

            If jsonData.ContainsKey("devices") AndAlso Not TypeOf jsonData("devices") Is JsonNull Then
                Dim arrayDevice As JsonArray = CType(jsonData("devices"), JsonArray)
                If arrayDevice IsNot Nothing AndAlso arrayDevice.Count > 0 Then
                    For Each joDevice As JsonObject In arrayDevice
                        Dim deviceudid As String = CType(joDevice("phoneid"), JsonString).Value
                        listBindDevice.Add(deviceudid)
                    Next
                End If
            End If
           
            VipUserInfo.listBindingDevice = listBindDevice

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetUserInfo")
        End Try
        Return vipUserInfo
    End Function

    Private Sub ShowUserInfo(ByVal vipUserInfo As VipUserInfo)
        Try
            Dim strInfo As New StringBuilder()
            'Dim viptime As String = String.Format("1. 您的服务期限为 {0} 个月，开始时间为 {1} ，到期时间为 {2} ", vipUserInfo.vipType.month, vipUserInfo.starttime, vipUserInfo.endtime)
            Dim viptime As String = String.Format("1. 您的服务开始时间为 {0} ，到期时间为 {1} ", vipUserInfo.starttime, vipUserInfo.endtime)
            'If viptime.Length > 40 Then
            '    strInfo.AppendLine(viptime.Substring(0, 40))
            '    strInfo.AppendLine("   " & viptime.Substring(40, viptime.Length - 40).TrimStart())
            'Else
            '    strInfo.AppendLine(viptime)
            'End If
            strInfo.AppendLine(viptime)

            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine(String.Format("2. 您绑定的电脑有 {0} 台，还可以绑定 {1} 台", vipUserInfo.listBindingComputer.Count, vipUserInfo.computer))
            'strInfo.AppendLine("")
            For i As Integer = 0 To vipUserInfo.listBindingComputer.Count - 1
                strInfo.AppendLine(String.Format("   电脑{0}: {1}", i + 1, vipUserInfo.listBindingComputer(i).macid))
            Next
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine(String.Format("3. 您绑定的设备有 {0} 台，还可以绑定 {1} 台", vipUserInfo.listBindingDevice.Count, vipUserInfo.device))
            For item As Integer = 0 To vipUserInfo.listBindingDevice.Count - 1
                strInfo.AppendLine(String.Format("   设备{0}: {1}", item + 1, vipUserInfo.listBindingDevice(item)))
            Next
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("如需提供远程协助，请点击右上角联系客服，选择产品客服")
            'rtxContent.Text = strInfo.ToString()
            SetRtxContentVisible(True)
            SetPnlLoadVisible(False)
            SetRtxContentText(strInfo.ToString())
            ChangeKeyColor(String.Format(" {0} ", vipUserInfo.vipType.month), Color.Red)
            ChangeKeyColor(String.Format(" {0} ", vipUserInfo.starttime), Color.Red)
            ChangeKeyColor(String.Format(" {0} ", vipUserInfo.endtime), Color.Red)
            ChangeKeyColor(String.Format(" {0} ", vipUserInfo.listBindingComputer.Count), Color.Red)
            ChangeKeyColor(String.Format(" {0} ", vipUserInfo.computer), Color.Red)
            ChangeKeyColor(String.Format(" {0} ", vipUserInfo.listBindingDevice.Count), Color.Red)
            ChangeKeyColor(String.Format(" {0} ", vipUserInfo.device), Color.Red)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowUserInfo")
        End Try
    End Sub

    Private Sub ShowErrorGetInfo()
        Try
            Dim strInfo As New StringBuilder()
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("        获取用户信息出错，请联系客服")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            SetRtxContentText(strInfo.ToString())
            ChangeKeyColor("获取用户信息出错，请联系客服", Color.Red)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowErrorGetInfo")
        End Try
    End Sub

    Private Delegate Sub SetLblWelcomeHandler(ByVal text As String)
    Private Sub SetLblWelcome(ByVal text As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetLblWelcomeHandler(AddressOf SetLblWelcome), text)
        Else
            lblWelcome.Text = text
        End If
    End Sub

    Private Delegate Sub SetPnlLoadVisibleHandler(ByVal blnVisible As Boolean)
    Private Sub SetPnlLoadVisible(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetPnlLoadVisibleHandler(AddressOf SetPnlLoadVisible), blnVisible)
        Else
            pnlLoading.Visible = blnVisible
        End If
    End Sub

    Private Delegate Sub SetRtxContentVisibleHandler(ByVal blnVisible As Boolean)
    Private Sub SetRtxContentVisible(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetRtxContentVisibleHandler(AddressOf SetRtxContentVisible), blnVisible)
        Else
            rtxContent.Visible = blnVisible
        End If
    End Sub
    Private Delegate Sub SetRtxContentTextHandler(ByVal text As String)
    Private Sub SetRtxContentText(ByVal text As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetRtxContentTextHandler(AddressOf SetRtxContentText), text)
        Else
            rtxContent.Text = text
        End If
    End Sub

    Private Delegate Sub ChangeKeyColorHandler(ByVal key As String, ByVal color As Color)
    Private Sub ChangeKeyColor(ByVal key As String, ByVal color As Color)
        If Me.InvokeRequired Then
            Me.Invoke(New ChangeKeyColorHandler(AddressOf ChangeKeyColor), key, color)
        Else
            Dim regex As Regex = New Regex(key)
            Dim collection As MatchCollection = regex.Matches(rtxContent.Text)
            For Each match As Match In collection
                rtxContent.SelectionStart = match.Index
                rtxContent.SelectionLength = key.Length
                rtxContent.SelectionColor = color
            Next
        End If
    End Sub

    Private Sub LoadGotoActivation()
        Try
            Me.lblWelcome.Text = "您当前的电脑尚未激活VIP服务"
            SetPnlLoadVisible(False)
            SetRtxContentVisible(True)
            Dim strInfo As New StringBuilder()
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("")
            strInfo.AppendLine("            请点击下方激活按钮，激活您的当前电脑")
            SetRtxContentText(strInfo.ToString())
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadGotoActivation")
        End Try
    End Sub
#End Region


    Private Sub btnCreatePremiumFeature_Click(sender As Object, e As EventArgs)
        'Try
        '    Dim frm As frmCreatePremiumFeature = New frmCreatePremiumFeature()
        '    frm.ShowDialog()
        'Catch ex As Exception
        '    Common.LogException(ex.ToString(), "btnCreatePremiumFeature_Click")
        'End Try
    End Sub
End Class
