﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmCreatePremiumFeature
    Inherits iTong.Components.tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmCreatePremiumFeature))
        Me.btn_close = New iTong.Components.tbButton()
        Me.txtSku = New iTong.Components.tbTextBox()
        Me.lblSku = New System.Windows.Forms.Label()
        Me.lblUdid = New System.Windows.Forms.Label()
        Me.txtUdid = New iTong.Components.tbTextBox()
        Me.lblNewSku = New System.Windows.Forms.Label()
        Me.txtNewSku = New iTong.Components.tbTextBox()
        Me.btnOK = New iTong.Components.tbButton()
        Me.lblMsg = New System.Windows.Forms.Label()
        Me.lblComputer = New System.Windows.Forms.Label()
        Me.txtComputer = New iTong.Components.tbTextBox()
        Me.btnAddChageskuDll = New iTong.Components.tbButton()
        Me.btnDeleteChageskuDll = New iTong.Components.tbButton()
        Me.lblInstallPath = New System.Windows.Forms.Label()
        Me.txtInstallPath = New iTong.Components.tbTextBox()
        Me.GroupBox1 = New System.Windows.Forms.GroupBox()
        Me.GroupBox2 = New System.Windows.Forms.GroupBox()
        Me.lblMsgChageSku = New System.Windows.Forms.Label()
        Me.GroupBox3 = New System.Windows.Forms.GroupBox()
        Me.btnAdd = New iTong.Components.tbButton()
        Me.lblMsgAddDevice = New System.Windows.Forms.Label()
        Me.txtDeviceUdid = New iTong.Components.tbTextBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.GroupBox1.SuspendLayout()
        Me.GroupBox2.SuspendLayout()
        Me.GroupBox3.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(396, 0)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 44
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'txtSku
        '
        Me.txtSku.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSku.BackColor = System.Drawing.Color.White
        Me.txtSku.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtSku.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtSku.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtSku.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtSku.ForeColor = System.Drawing.Color.Black
        Me.txtSku.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtSku.Location = New System.Drawing.Point(87, 20)
        Me.txtSku.MaxLength = 256
        Me.txtSku.Name = "txtSku"
        Me.txtSku.Size = New System.Drawing.Size(291, 21)
        Me.txtSku.TabIndex = 46
        Me.txtSku.Tag = Nothing
        Me.txtSku.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtSku.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtSku.tbSelMark = True
        Me.txtSku.tbTextBind = ""
        Me.txtSku.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtSku.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtSku.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtSku.TextTip = ""
        '
        'lblSku
        '
        Me.lblSku.AutoSize = True
        Me.lblSku.BackColor = System.Drawing.Color.Transparent
        Me.lblSku.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblSku.Location = New System.Drawing.Point(40, 24)
        Me.lblSku.Name = "lblSku"
        Me.lblSku.Size = New System.Drawing.Size(35, 12)
        Me.lblSku.TabIndex = 45
        Me.lblSku.Text = "sku："
        Me.lblSku.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblUdid
        '
        Me.lblUdid.AutoSize = True
        Me.lblUdid.BackColor = System.Drawing.Color.Transparent
        Me.lblUdid.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblUdid.Location = New System.Drawing.Point(34, 60)
        Me.lblUdid.Name = "lblUdid"
        Me.lblUdid.Size = New System.Drawing.Size(41, 12)
        Me.lblUdid.TabIndex = 45
        Me.lblUdid.Text = "udid："
        Me.lblUdid.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'txtUdid
        '
        Me.txtUdid.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtUdid.BackColor = System.Drawing.Color.White
        Me.txtUdid.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtUdid.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtUdid.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtUdid.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtUdid.ForeColor = System.Drawing.Color.Black
        Me.txtUdid.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtUdid.Location = New System.Drawing.Point(87, 56)
        Me.txtUdid.MaxLength = 256
        Me.txtUdid.Name = "txtUdid"
        Me.txtUdid.Size = New System.Drawing.Size(291, 21)
        Me.txtUdid.TabIndex = 46
        Me.txtUdid.Tag = Nothing
        Me.txtUdid.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtUdid.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtUdid.tbSelMark = True
        Me.txtUdid.tbTextBind = ""
        Me.txtUdid.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtUdid.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtUdid.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtUdid.TextTip = ""
        '
        'lblNewSku
        '
        Me.lblNewSku.AutoSize = True
        Me.lblNewSku.BackColor = System.Drawing.Color.Transparent
        Me.lblNewSku.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblNewSku.Location = New System.Drawing.Point(28, 114)
        Me.lblNewSku.Name = "lblNewSku"
        Me.lblNewSku.Size = New System.Drawing.Size(47, 12)
        Me.lblNewSku.TabIndex = 45
        Me.lblNewSku.Text = "新sku："
        Me.lblNewSku.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'txtNewSku
        '
        Me.txtNewSku.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtNewSku.BackColor = System.Drawing.Color.White
        Me.txtNewSku.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtNewSku.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtNewSku.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtNewSku.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtNewSku.ForeColor = System.Drawing.Color.Black
        Me.txtNewSku.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNewSku.Location = New System.Drawing.Point(87, 110)
        Me.txtNewSku.MaxLength = 256
        Me.txtNewSku.Name = "txtNewSku"
        Me.txtNewSku.Size = New System.Drawing.Size(291, 21)
        Me.txtNewSku.TabIndex = 46
        Me.txtNewSku.Tag = Nothing
        Me.txtNewSku.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtNewSku.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtNewSku.tbSelMark = True
        Me.txtNewSku.tbTextBind = "com.tencent.xin.mjuruangong"
        Me.txtNewSku.Text = "com.tencent.xin.mjuruangong"
        Me.txtNewSku.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtNewSku.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNewSku.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtNewSku.TextTip = ""
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(313, 152)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(65, 23)
        Me.btnOK.TabIndex = 47
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "生成"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOK.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOK.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'lblMsg
        '
        Me.lblMsg.AutoSize = True
        Me.lblMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblMsg.ForeColor = System.Drawing.Color.Red
        Me.lblMsg.Location = New System.Drawing.Point(33, 152)
        Me.lblMsg.Name = "lblMsg"
        Me.lblMsg.Size = New System.Drawing.Size(0, 12)
        Me.lblMsg.TabIndex = 48
        '
        'lblComputer
        '
        Me.lblComputer.AutoSize = True
        Me.lblComputer.BackColor = System.Drawing.Color.Transparent
        Me.lblComputer.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblComputer.Location = New System.Drawing.Point(10, 87)
        Me.lblComputer.Name = "lblComputer"
        Me.lblComputer.Size = New System.Drawing.Size(65, 12)
        Me.lblComputer.TabIndex = 45
        Me.lblComputer.Text = "computer："
        Me.lblComputer.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'txtComputer
        '
        Me.txtComputer.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtComputer.BackColor = System.Drawing.Color.White
        Me.txtComputer.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtComputer.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtComputer.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtComputer.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtComputer.ForeColor = System.Drawing.Color.Black
        Me.txtComputer.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtComputer.Location = New System.Drawing.Point(87, 83)
        Me.txtComputer.MaxLength = 256
        Me.txtComputer.Name = "txtComputer"
        Me.txtComputer.Size = New System.Drawing.Size(291, 21)
        Me.txtComputer.TabIndex = 46
        Me.txtComputer.Tag = Nothing
        Me.txtComputer.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtComputer.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtComputer.tbSelMark = True
        Me.txtComputer.tbTextBind = ""
        Me.txtComputer.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtComputer.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtComputer.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtComputer.TextTip = ""
        '
        'btnAddChageskuDll
        '
        Me.btnAddChageskuDll.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAddChageskuDll.BackColor = System.Drawing.Color.Transparent
        Me.btnAddChageskuDll.BindingForm = Nothing
        Me.btnAddChageskuDll.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAddChageskuDll.Location = New System.Drawing.Point(156, 82)
        Me.btnAddChageskuDll.Name = "btnAddChageskuDll"
        Me.btnAddChageskuDll.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnAddChageskuDll.Selectable = True
        Me.btnAddChageskuDll.Size = New System.Drawing.Size(94, 23)
        Me.btnAddChageskuDll.TabIndex = 47
        Me.btnAddChageskuDll.tbAdriftIconWhenHover = False
        Me.btnAddChageskuDll.tbAutoSize = False
        Me.btnAddChageskuDll.tbAutoSizeEx = False
        Me.btnAddChageskuDll.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnAddChageskuDll.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnAddChageskuDll.tbBadgeNumber = 0
        Me.btnAddChageskuDll.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAddChageskuDll.tbEndEllipsis = False
        Me.btnAddChageskuDll.tbIconHoldPlace = True
        Me.btnAddChageskuDll.tbIconImage = Nothing
        Me.btnAddChageskuDll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddChageskuDll.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAddChageskuDll.tbIconMore = False
        Me.btnAddChageskuDll.tbIconMouseDown = Nothing
        Me.btnAddChageskuDll.tbIconMouseHover = Nothing
        Me.btnAddChageskuDll.tbIconMouseLeave = Nothing
        Me.btnAddChageskuDll.tbIconPlaceText = 2
        Me.btnAddChageskuDll.tbIconReadOnly = Nothing
        Me.btnAddChageskuDll.tbImageMouseDown = Nothing
        Me.btnAddChageskuDll.tbImageMouseHover = Nothing
        Me.btnAddChageskuDll.tbImageMouseLeave = Nothing
        Me.btnAddChageskuDll.tbProgressValue = 50
        Me.btnAddChageskuDll.tbReadOnly = False
        Me.btnAddChageskuDll.tbReadOnlyText = False
        Me.btnAddChageskuDll.tbShadow = False
        Me.btnAddChageskuDll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnAddChageskuDll.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnAddChageskuDll.tbShowDot = False
        Me.btnAddChageskuDll.tbShowMoreIconImg = CType(resources.GetObject("btnAddChageskuDll.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAddChageskuDll.tbShowNew = False
        Me.btnAddChageskuDll.tbShowProgress = False
        Me.btnAddChageskuDll.tbShowTip = True
        Me.btnAddChageskuDll.tbShowToolTipOnButton = False
        Me.btnAddChageskuDll.tbSplit = "13,11,13,11"
        Me.btnAddChageskuDll.tbText = "生成ChangeSku"
        Me.btnAddChageskuDll.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddChageskuDll.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddChageskuDll.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddChageskuDll.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddChageskuDll.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddChageskuDll.tbTextMouseDownPlace = 0
        Me.btnAddChageskuDll.tbToolTip = ""
        Me.btnAddChageskuDll.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAddChageskuDll.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAddChageskuDll.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddChageskuDll.VisibleEx = True
        '
        'btnDeleteChageskuDll
        '
        Me.btnDeleteChageskuDll.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDeleteChageskuDll.BackColor = System.Drawing.Color.Transparent
        Me.btnDeleteChageskuDll.BindingForm = Nothing
        Me.btnDeleteChageskuDll.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDeleteChageskuDll.Location = New System.Drawing.Point(284, 82)
        Me.btnDeleteChageskuDll.Name = "btnDeleteChageskuDll"
        Me.btnDeleteChageskuDll.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnDeleteChageskuDll.Selectable = True
        Me.btnDeleteChageskuDll.Size = New System.Drawing.Size(94, 23)
        Me.btnDeleteChageskuDll.TabIndex = 47
        Me.btnDeleteChageskuDll.tbAdriftIconWhenHover = False
        Me.btnDeleteChageskuDll.tbAutoSize = False
        Me.btnDeleteChageskuDll.tbAutoSizeEx = False
        Me.btnDeleteChageskuDll.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnDeleteChageskuDll.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDeleteChageskuDll.tbBadgeNumber = 0
        Me.btnDeleteChageskuDll.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDeleteChageskuDll.tbEndEllipsis = False
        Me.btnDeleteChageskuDll.tbIconHoldPlace = True
        Me.btnDeleteChageskuDll.tbIconImage = Nothing
        Me.btnDeleteChageskuDll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteChageskuDll.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDeleteChageskuDll.tbIconMore = False
        Me.btnDeleteChageskuDll.tbIconMouseDown = Nothing
        Me.btnDeleteChageskuDll.tbIconMouseHover = Nothing
        Me.btnDeleteChageskuDll.tbIconMouseLeave = Nothing
        Me.btnDeleteChageskuDll.tbIconPlaceText = 2
        Me.btnDeleteChageskuDll.tbIconReadOnly = Nothing
        Me.btnDeleteChageskuDll.tbImageMouseDown = Nothing
        Me.btnDeleteChageskuDll.tbImageMouseHover = Nothing
        Me.btnDeleteChageskuDll.tbImageMouseLeave = Nothing
        Me.btnDeleteChageskuDll.tbProgressValue = 50
        Me.btnDeleteChageskuDll.tbReadOnly = False
        Me.btnDeleteChageskuDll.tbReadOnlyText = False
        Me.btnDeleteChageskuDll.tbShadow = False
        Me.btnDeleteChageskuDll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDeleteChageskuDll.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDeleteChageskuDll.tbShowDot = False
        Me.btnDeleteChageskuDll.tbShowMoreIconImg = CType(resources.GetObject("btnDeleteChageskuDll.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDeleteChageskuDll.tbShowNew = False
        Me.btnDeleteChageskuDll.tbShowProgress = False
        Me.btnDeleteChageskuDll.tbShowTip = True
        Me.btnDeleteChageskuDll.tbShowToolTipOnButton = False
        Me.btnDeleteChageskuDll.tbSplit = "13,11,13,11"
        Me.btnDeleteChageskuDll.tbText = "删除ChangeSku"
        Me.btnDeleteChageskuDll.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteChageskuDll.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDeleteChageskuDll.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDeleteChageskuDll.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDeleteChageskuDll.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDeleteChageskuDll.tbTextMouseDownPlace = 0
        Me.btnDeleteChageskuDll.tbToolTip = ""
        Me.btnDeleteChageskuDll.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDeleteChageskuDll.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDeleteChageskuDll.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteChageskuDll.VisibleEx = True
        '
        'lblInstallPath
        '
        Me.lblInstallPath.AutoSize = True
        Me.lblInstallPath.BackColor = System.Drawing.Color.Transparent
        Me.lblInstallPath.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.lblInstallPath.Location = New System.Drawing.Point(9, 29)
        Me.lblInstallPath.Name = "lblInstallPath"
        Me.lblInstallPath.Size = New System.Drawing.Size(89, 12)
        Me.lblInstallPath.TabIndex = 45
        Me.lblInstallPath.Text = "助手安装目录："
        Me.lblInstallPath.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'txtInstallPath
        '
        Me.txtInstallPath.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtInstallPath.BackColor = System.Drawing.Color.White
        Me.txtInstallPath.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtInstallPath.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtInstallPath.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtInstallPath.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtInstallPath.ForeColor = System.Drawing.Color.Black
        Me.txtInstallPath.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtInstallPath.Location = New System.Drawing.Point(11, 51)
        Me.txtInstallPath.MaxLength = 256
        Me.txtInstallPath.Name = "txtInstallPath"
        Me.txtInstallPath.Size = New System.Drawing.Size(367, 21)
        Me.txtInstallPath.TabIndex = 46
        Me.txtInstallPath.Tag = Nothing
        Me.txtInstallPath.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtInstallPath.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtInstallPath.tbSelMark = True
        Me.txtInstallPath.tbTextBind = ""
        Me.txtInstallPath.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtInstallPath.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtInstallPath.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtInstallPath.TextTip = ""
        '
        'GroupBox1
        '
        Me.GroupBox1.Controls.Add(Me.txtSku)
        Me.GroupBox1.Controls.Add(Me.lblMsg)
        Me.GroupBox1.Controls.Add(Me.lblSku)
        Me.GroupBox1.Controls.Add(Me.txtUdid)
        Me.GroupBox1.Controls.Add(Me.lblUdid)
        Me.GroupBox1.Controls.Add(Me.btnOK)
        Me.GroupBox1.Controls.Add(Me.txtComputer)
        Me.GroupBox1.Controls.Add(Me.lblComputer)
        Me.GroupBox1.Controls.Add(Me.txtNewSku)
        Me.GroupBox1.Controls.Add(Me.lblNewSku)
        Me.GroupBox1.Location = New System.Drawing.Point(17, 124)
        Me.GroupBox1.Name = "GroupBox1"
        Me.GroupBox1.Size = New System.Drawing.Size(387, 199)
        Me.GroupBox1.TabIndex = 49
        Me.GroupBox1.TabStop = False
        Me.GroupBox1.Text = "生成  PremiumFeature.ini"
        '
        'GroupBox2
        '
        Me.GroupBox2.Controls.Add(Me.btnDeleteChageskuDll)
        Me.GroupBox2.Controls.Add(Me.lblMsgChageSku)
        Me.GroupBox2.Controls.Add(Me.btnAddChageskuDll)
        Me.GroupBox2.Controls.Add(Me.txtInstallPath)
        Me.GroupBox2.Controls.Add(Me.lblInstallPath)
        Me.GroupBox2.Location = New System.Drawing.Point(17, 336)
        Me.GroupBox2.Name = "GroupBox2"
        Me.GroupBox2.Size = New System.Drawing.Size(387, 120)
        Me.GroupBox2.TabIndex = 50
        Me.GroupBox2.TabStop = False
        Me.GroupBox2.Text = "创建  ChangeSku.dll"
        '
        'lblMsgChageSku
        '
        Me.lblMsgChageSku.AutoSize = True
        Me.lblMsgChageSku.BackColor = System.Drawing.Color.Transparent
        Me.lblMsgChageSku.ForeColor = System.Drawing.Color.Red
        Me.lblMsgChageSku.Location = New System.Drawing.Point(15, 85)
        Me.lblMsgChageSku.Name = "lblMsgChageSku"
        Me.lblMsgChageSku.Size = New System.Drawing.Size(0, 12)
        Me.lblMsgChageSku.TabIndex = 48
        '
        'GroupBox3
        '
        Me.GroupBox3.Controls.Add(Me.btnAdd)
        Me.GroupBox3.Controls.Add(Me.lblMsgAddDevice)
        Me.GroupBox3.Controls.Add(Me.txtDeviceUdid)
        Me.GroupBox3.Controls.Add(Me.Label1)
        Me.GroupBox3.Location = New System.Drawing.Point(18, 39)
        Me.GroupBox3.Name = "GroupBox3"
        Me.GroupBox3.Size = New System.Drawing.Size(386, 79)
        Me.GroupBox3.TabIndex = 51
        Me.GroupBox3.TabStop = False
        Me.GroupBox3.Text = "绑定设备"
        '
        'btnAdd
        '
        Me.btnAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAdd.BackColor = System.Drawing.Color.Transparent
        Me.btnAdd.BindingForm = Nothing
        Me.btnAdd.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAdd.Location = New System.Drawing.Point(312, 51)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnAdd.Selectable = True
        Me.btnAdd.Size = New System.Drawing.Size(65, 23)
        Me.btnAdd.TabIndex = 40
        Me.btnAdd.tbAdriftIconWhenHover = False
        Me.btnAdd.tbAutoSize = False
        Me.btnAdd.tbAutoSizeEx = False
        Me.btnAdd.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnAdd.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnAdd.tbBadgeNumber = 0
        Me.btnAdd.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAdd.tbEndEllipsis = False
        Me.btnAdd.tbIconHoldPlace = True
        Me.btnAdd.tbIconImage = Nothing
        Me.btnAdd.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAdd.tbIconMore = False
        Me.btnAdd.tbIconMouseDown = Nothing
        Me.btnAdd.tbIconMouseHover = Nothing
        Me.btnAdd.tbIconMouseLeave = Nothing
        Me.btnAdd.tbIconPlaceText = 2
        Me.btnAdd.tbIconReadOnly = Nothing
        Me.btnAdd.tbImageMouseDown = Nothing
        Me.btnAdd.tbImageMouseHover = Nothing
        Me.btnAdd.tbImageMouseLeave = Nothing
        Me.btnAdd.tbProgressValue = 50
        Me.btnAdd.tbReadOnly = False
        Me.btnAdd.tbReadOnlyText = False
        Me.btnAdd.tbShadow = False
        Me.btnAdd.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnAdd.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnAdd.tbShowDot = False
        Me.btnAdd.tbShowMoreIconImg = CType(resources.GetObject("btnAdd.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAdd.tbShowNew = False
        Me.btnAdd.tbShowProgress = False
        Me.btnAdd.tbShowTip = True
        Me.btnAdd.tbShowToolTipOnButton = False
        Me.btnAdd.tbSplit = "13,11,13,11"
        Me.btnAdd.tbText = "添加"
        Me.btnAdd.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAdd.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAdd.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAdd.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAdd.tbTextMouseDownPlace = 0
        Me.btnAdd.tbToolTip = ""
        Me.btnAdd.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAdd.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAdd.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.VisibleEx = True
        '
        'lblMsgAddDevice
        '
        Me.lblMsgAddDevice.AutoSize = True
        Me.lblMsgAddDevice.BackColor = System.Drawing.Color.Transparent
        Me.lblMsgAddDevice.ForeColor = System.Drawing.Color.Red
        Me.lblMsgAddDevice.Location = New System.Drawing.Point(34, 51)
        Me.lblMsgAddDevice.Name = "lblMsgAddDevice"
        Me.lblMsgAddDevice.Size = New System.Drawing.Size(0, 12)
        Me.lblMsgAddDevice.TabIndex = 48
        '
        'txtDeviceUdid
        '
        Me.txtDeviceUdid.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtDeviceUdid.BackColor = System.Drawing.Color.White
        Me.txtDeviceUdid.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtDeviceUdid.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtDeviceUdid.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtDeviceUdid.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtDeviceUdid.ForeColor = System.Drawing.Color.Black
        Me.txtDeviceUdid.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDeviceUdid.Location = New System.Drawing.Point(87, 20)
        Me.txtDeviceUdid.MaxLength = 256
        Me.txtDeviceUdid.Name = "txtDeviceUdid"
        Me.txtDeviceUdid.Size = New System.Drawing.Size(290, 21)
        Me.txtDeviceUdid.TabIndex = 39
        Me.txtDeviceUdid.Tag = Nothing
        Me.txtDeviceUdid.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtDeviceUdid.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtDeviceUdid.tbSelMark = True
        Me.txtDeviceUdid.tbTextBind = ""
        Me.txtDeviceUdid.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtDeviceUdid.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDeviceUdid.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtDeviceUdid.TextTip = ""
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(34, 23)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(41, 12)
        Me.Label1.TabIndex = 38
        Me.Label1.Text = "udid："
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmCreatePremiumFeature
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(422, 470)
        Me.Controls.Add(Me.GroupBox3)
        Me.Controls.Add(Me.GroupBox2)
        Me.Controls.Add(Me.GroupBox1)
        Me.Controls.Add(Me.btn_close)
        Me.Name = "frmCreatePremiumFeature"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.tbVipHelper.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "6,33,7,28"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "生成PremiumFeature"
        Me.GroupBox1.ResumeLayout(False)
        Me.GroupBox1.PerformLayout()
        Me.GroupBox2.ResumeLayout(False)
        Me.GroupBox2.PerformLayout()
        Me.GroupBox3.ResumeLayout(False)
        Me.GroupBox3.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents txtSku As iTong.Components.tbTextBox
    Friend WithEvents lblSku As System.Windows.Forms.Label
    Friend WithEvents lblUdid As System.Windows.Forms.Label
    Friend WithEvents txtUdid As iTong.Components.tbTextBox
    Friend WithEvents lblNewSku As System.Windows.Forms.Label
    Friend WithEvents txtNewSku As iTong.Components.tbTextBox
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents lblMsg As System.Windows.Forms.Label
    Friend WithEvents lblComputer As System.Windows.Forms.Label
    Friend WithEvents txtComputer As iTong.Components.tbTextBox
    Friend WithEvents btnAddChageskuDll As iTong.Components.tbButton
    Friend WithEvents btnDeleteChageskuDll As iTong.Components.tbButton
    Friend WithEvents lblInstallPath As System.Windows.Forms.Label
    Friend WithEvents txtInstallPath As iTong.Components.tbTextBox
    Friend WithEvents GroupBox1 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox2 As System.Windows.Forms.GroupBox
    Friend WithEvents GroupBox3 As System.Windows.Forms.GroupBox
    Friend WithEvents txtDeviceUdid As iTong.Components.tbTextBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents btnAdd As iTong.Components.tbButton
    Friend WithEvents lblMsgChageSku As System.Windows.Forms.Label
    Friend WithEvents lblMsgAddDevice As System.Windows.Forms.Label
End Class
