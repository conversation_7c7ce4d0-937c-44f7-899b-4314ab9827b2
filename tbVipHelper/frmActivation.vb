﻿Imports iTong.CoreFoundation
Imports System.Text

Public Class frmActivation
    Private mPostUrl As String = "http://pc.api.tongbu.com/vip/v.html?t=5"

#Region "--- 初始化 ---"
    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong

    End Sub
#End Region

#Region "--- 窗体事件 ---"
    Private Sub btnActivation_Click(sender As Object, e As EventArgs) Handles btnActivation.Click
        Try
            Me.lblMsg.Text = ""
            If String.IsNullOrEmpty(txtEmail.Text.Trim()) Then
                Me.lblMsg.Text = "请输入购买时提供的邮箱"
                Me.txtEmail.Focus()
                Return
            End If

            If String.IsNullOrEmpty(txtLicense.Text.Trim()) Then
                Me.lblMsg.Text = "请输入激活码"
                Me.txtLicense.Focus()
                Return
            End If

            Dim dicContent As New JsonObject
            dicContent.Add("mail", txtEmail.Text.Trim())
            dicContent.Add("projectid", 1)
            dicContent.Add("macid", VipHelper.GetMacid())
            dicContent.Add("hdd", Common.GetHardDiskID2())
            dicContent.Add("cpu", Common.GetProcessorId())
            dicContent.Add("wifimac", Common.GetMacAddress().Trim())
            dicContent.Add("code", txtLicense.Text.Trim())

            Dim writer As New JsonWriter()
            dicContent.Write(writer)
            Dim strContent As String = writer.ToString()
            Dim strResult As String = VipHelper.PostData(mPostUrl, strContent)
            strResult = VipHelper.DecryptDES(strResult)
            Dim objJson As JsonObject = JsonParser.ParseString(strResult)
            If objJson Is Nothing OrElse Not objJson.ContainsKey("data") Then
                lblMsg.Text = "请求失败，请联系客服"
                Return
            End If

            Dim jsonData As JsonObject = CType(objJson("data"), JsonObject)
            Dim code As Integer = CType(jsonData("code"), JsonNumber).Value
            Dim msg As String = CType(jsonData("msg"), JsonString).Value
            If code = 1 Then
                VipHelper.SetConfigMail(txtEmail.Text.Trim())
                Me.DialogResult = Windows.Forms.DialogResult.OK
                Me.Close()
            Else
                lblMsg.Text = "激活失败:" & msg
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnActivation_Click")
        End Try
       
    End Sub
#End Region

End Class