﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmPassword
    Inherits iTong.Components.tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmPassword))
        Me.btnOK = New iTong.Components.tbButton()
        Me.txtPwd = New iTong.Components.tbTextBox()
        Me.lblMsg = New System.Windows.Forms.Label()
        Me.btn_close = New iTong.Components.tbButton()
        Me.SuspendLayout()
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(208, 104)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(65, 23)
        Me.btnOK.TabIndex = 41
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "确定"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOK.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOK.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'txtPwd
        '
        Me.txtPwd.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtPwd.BackColor = System.Drawing.Color.White
        Me.txtPwd.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtPwd.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtPwd.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtPwd.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtPwd.ForeColor = System.Drawing.Color.Black
        Me.txtPwd.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtPwd.Location = New System.Drawing.Point(28, 59)
        Me.txtPwd.MaxLength = 256
        Me.txtPwd.Name = "txtPwd"
        Me.txtPwd.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.txtPwd.Size = New System.Drawing.Size(245, 21)
        Me.txtPwd.TabIndex = 40
        Me.txtPwd.Tag = Nothing
        Me.txtPwd.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtPwd.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtPwd.tbSelMark = True
        Me.txtPwd.tbTextBind = ""
        Me.txtPwd.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtPwd.TextImeMode = System.Windows.Forms.ImeMode.Disable
        Me.txtPwd.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtPwd.TextTip = ""
        '
        'lblMsg
        '
        Me.lblMsg.AutoSize = True
        Me.lblMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblMsg.ForeColor = System.Drawing.Color.Red
        Me.lblMsg.Location = New System.Drawing.Point(31, 104)
        Me.lblMsg.Name = "lblMsg"
        Me.lblMsg.Size = New System.Drawing.Size(0, 12)
        Me.lblMsg.TabIndex = 42
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(273, 1)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 43
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'frmPassword
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(300, 149)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.lblMsg)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.txtPwd)
        Me.Name = "frmPassword"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.tbVipHelper.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "6,33,7,28"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "秘钥"
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents txtPwd As iTong.Components.tbTextBox
    Friend WithEvents lblMsg As System.Windows.Forms.Label
    Friend WithEvents btn_close As iTong.Components.tbButton
End Class
