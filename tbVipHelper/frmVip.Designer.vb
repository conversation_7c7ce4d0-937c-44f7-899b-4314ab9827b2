﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmVip
    Inherits iTong.Components.tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmVip))
        Me.btn_close = New iTong.Components.tbButton()
        Me.btn_minimize = New iTong.Components.tbButton()
        Me.btnActivation = New iTong.Components.tbButton()
        Me.btnRenew = New iTong.Components.tbButton()
        Me.btnSearchDevice = New iTong.Components.tbButton()
        Me.btnGetPCInfo = New iTong.Components.tbButton()
        Me.lblWelcome = New System.Windows.Forms.Label()
        Me.rtxContent = New iTong.Components.tbRichTextBox()
        Me.pnlLoading = New iTong.Components.tbPanel()
        Me.lblLoadInfo = New iTong.Components.tbLabel()
        Me.TbPictureBox1 = New iTong.Components.tbPictureBox()
        Me.bgwGetInfo = New System.ComponentModel.BackgroundWorker()
        Me.btnContact = New iTong.Components.tbButton()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.lblMailMsg = New iTong.Components.tbLabel()
        Me.txtMail = New System.Windows.Forms.TextBox()
        Me.btnAddDevice = New iTong.Components.tbButton()
        Me.pnlLoading.SuspendLayout()
        CType(Me.TbPictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(424, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 26
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btn_minimize
        '
        Me.btn_minimize.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_minimize.BackColor = System.Drawing.Color.Transparent
        Me.btn_minimize.BindingForm = Nothing
        Me.btn_minimize.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_minimize.Location = New System.Drawing.Point(405, 2)
        Me.btn_minimize.Name = "btn_minimize"
        Me.btn_minimize.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_minimize.Selectable = True
        Me.btn_minimize.Size = New System.Drawing.Size(24, 24)
        Me.btn_minimize.TabIndex = 25
        Me.btn_minimize.tbAdriftIconWhenHover = False
        Me.btn_minimize.tbAutoSize = True
        Me.btn_minimize.tbAutoSizeEx = False
        Me.btn_minimize.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_min
        Me.btn_minimize.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_minimize.tbBadgeNumber = 0
        Me.btn_minimize.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_minimize.tbEndEllipsis = False
        Me.btn_minimize.tbIconHoldPlace = True
        Me.btn_minimize.tbIconImage = Nothing
        Me.btn_minimize.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_minimize.tbIconMore = False
        Me.btn_minimize.tbIconMouseDown = Nothing
        Me.btn_minimize.tbIconMouseHover = Nothing
        Me.btn_minimize.tbIconMouseLeave = Nothing
        Me.btn_minimize.tbIconPlaceText = 2
        Me.btn_minimize.tbIconReadOnly = Nothing
        Me.btn_minimize.tbImageMouseDown = Nothing
        Me.btn_minimize.tbImageMouseHover = Nothing
        Me.btn_minimize.tbImageMouseLeave = Nothing
        Me.btn_minimize.tbProgressValue = 50
        Me.btn_minimize.tbReadOnly = False
        Me.btn_minimize.tbReadOnlyText = False
        Me.btn_minimize.tbShadow = False
        Me.btn_minimize.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_minimize.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_minimize.tbShowDot = False
        Me.btn_minimize.tbShowMoreIconImg = CType(resources.GetObject("btn_minimize.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_minimize.tbShowNew = False
        Me.btn_minimize.tbShowProgress = False
        Me.btn_minimize.tbShowTip = True
        Me.btn_minimize.tbShowToolTipOnButton = False
        Me.btn_minimize.tbSplit = "3,3,3,3"
        Me.btn_minimize.tbText = ""
        Me.btn_minimize.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.tbTextColor = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorDown = System.Drawing.Color.White
        Me.btn_minimize.tbTextColorHover = System.Drawing.Color.White
        Me.btn_minimize.tbTextMouseDownPlace = 0
        Me.btn_minimize.tbToolTip = ""
        Me.btn_minimize.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_minimize.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_minimize.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_minimize.VisibleEx = True
        '
        'btnActivation
        '
        Me.btnActivation.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnActivation.BackColor = System.Drawing.Color.Transparent
        Me.btnActivation.BindingForm = Nothing
        Me.btnActivation.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.btnActivation.Location = New System.Drawing.Point(29, 419)
        Me.btnActivation.Name = "btnActivation"
        Me.btnActivation.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnActivation.Selectable = True
        Me.btnActivation.Size = New System.Drawing.Size(69, 25)
        Me.btnActivation.TabIndex = 27
        Me.btnActivation.tbAdriftIconWhenHover = False
        Me.btnActivation.tbAutoSize = False
        Me.btnActivation.tbAutoSizeEx = False
        Me.btnActivation.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnActivation.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnActivation.tbBadgeNumber = 0
        Me.btnActivation.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnActivation.tbEndEllipsis = False
        Me.btnActivation.tbIconHoldPlace = True
        Me.btnActivation.tbIconImage = Nothing
        Me.btnActivation.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivation.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnActivation.tbIconMore = False
        Me.btnActivation.tbIconMouseDown = Nothing
        Me.btnActivation.tbIconMouseHover = Nothing
        Me.btnActivation.tbIconMouseLeave = Nothing
        Me.btnActivation.tbIconPlaceText = 5
        Me.btnActivation.tbIconReadOnly = Nothing
        Me.btnActivation.tbImageMouseDown = Nothing
        Me.btnActivation.tbImageMouseHover = Nothing
        Me.btnActivation.tbImageMouseLeave = Nothing
        Me.btnActivation.tbProgressValue = 50
        Me.btnActivation.tbReadOnly = False
        Me.btnActivation.tbReadOnlyText = False
        Me.btnActivation.tbShadow = False
        Me.btnActivation.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnActivation.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnActivation.tbShowDot = False
        Me.btnActivation.tbShowMoreIconImg = CType(resources.GetObject("btnActivation.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnActivation.tbShowNew = False
        Me.btnActivation.tbShowProgress = False
        Me.btnActivation.tbShowTip = True
        Me.btnActivation.tbShowToolTipOnButton = False
        Me.btnActivation.tbSplit = "13,11,13,11"
        Me.btnActivation.tbText = "激活"
        Me.btnActivation.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivation.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnActivation.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnActivation.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnActivation.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnActivation.tbTextMouseDownPlace = 0
        Me.btnActivation.tbToolTip = ""
        Me.btnActivation.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnActivation.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnActivation.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivation.VisibleEx = True
        '
        'btnRenew
        '
        Me.btnRenew.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRenew.BackColor = System.Drawing.Color.Transparent
        Me.btnRenew.BindingForm = Nothing
        Me.btnRenew.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.btnRenew.Location = New System.Drawing.Point(119, 419)
        Me.btnRenew.Name = "btnRenew"
        Me.btnRenew.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRenew.Selectable = True
        Me.btnRenew.Size = New System.Drawing.Size(74, 25)
        Me.btnRenew.TabIndex = 27
        Me.btnRenew.tbAdriftIconWhenHover = False
        Me.btnRenew.tbAutoSize = False
        Me.btnRenew.tbAutoSizeEx = False
        Me.btnRenew.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnRenew.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRenew.tbBadgeNumber = 0
        Me.btnRenew.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRenew.tbEndEllipsis = False
        Me.btnRenew.tbIconHoldPlace = True
        Me.btnRenew.tbIconImage = Nothing
        Me.btnRenew.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRenew.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRenew.tbIconMore = False
        Me.btnRenew.tbIconMouseDown = Nothing
        Me.btnRenew.tbIconMouseHover = Nothing
        Me.btnRenew.tbIconMouseLeave = Nothing
        Me.btnRenew.tbIconPlaceText = 5
        Me.btnRenew.tbIconReadOnly = Nothing
        Me.btnRenew.tbImageMouseDown = Nothing
        Me.btnRenew.tbImageMouseHover = Nothing
        Me.btnRenew.tbImageMouseLeave = Nothing
        Me.btnRenew.tbProgressValue = 50
        Me.btnRenew.tbReadOnly = False
        Me.btnRenew.tbReadOnlyText = False
        Me.btnRenew.tbShadow = False
        Me.btnRenew.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRenew.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRenew.tbShowDot = False
        Me.btnRenew.tbShowMoreIconImg = CType(resources.GetObject("btnRenew.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRenew.tbShowNew = False
        Me.btnRenew.tbShowProgress = False
        Me.btnRenew.tbShowTip = True
        Me.btnRenew.tbShowToolTipOnButton = False
        Me.btnRenew.tbSplit = "13,11,13,11"
        Me.btnRenew.tbText = "续费"
        Me.btnRenew.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRenew.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnRenew.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnRenew.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnRenew.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnRenew.tbTextMouseDownPlace = 0
        Me.btnRenew.tbToolTip = ""
        Me.btnRenew.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRenew.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRenew.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRenew.VisibleEx = True
        '
        'btnSearchDevice
        '
        Me.btnSearchDevice.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSearchDevice.BackColor = System.Drawing.Color.Transparent
        Me.btnSearchDevice.BindingForm = Nothing
        Me.btnSearchDevice.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.btnSearchDevice.Location = New System.Drawing.Point(211, 419)
        Me.btnSearchDevice.Name = "btnSearchDevice"
        Me.btnSearchDevice.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSearchDevice.Selectable = True
        Me.btnSearchDevice.Size = New System.Drawing.Size(75, 25)
        Me.btnSearchDevice.TabIndex = 27
        Me.btnSearchDevice.tbAdriftIconWhenHover = False
        Me.btnSearchDevice.tbAutoSize = False
        Me.btnSearchDevice.tbAutoSizeEx = False
        Me.btnSearchDevice.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnSearchDevice.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSearchDevice.tbBadgeNumber = 0
        Me.btnSearchDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSearchDevice.tbEndEllipsis = False
        Me.btnSearchDevice.tbIconHoldPlace = True
        Me.btnSearchDevice.tbIconImage = Nothing
        Me.btnSearchDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearchDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSearchDevice.tbIconMore = False
        Me.btnSearchDevice.tbIconMouseDown = Nothing
        Me.btnSearchDevice.tbIconMouseHover = Nothing
        Me.btnSearchDevice.tbIconMouseLeave = Nothing
        Me.btnSearchDevice.tbIconPlaceText = 5
        Me.btnSearchDevice.tbIconReadOnly = Nothing
        Me.btnSearchDevice.tbImageMouseDown = Nothing
        Me.btnSearchDevice.tbImageMouseHover = Nothing
        Me.btnSearchDevice.tbImageMouseLeave = Nothing
        Me.btnSearchDevice.tbProgressValue = 50
        Me.btnSearchDevice.tbReadOnly = False
        Me.btnSearchDevice.tbReadOnlyText = False
        Me.btnSearchDevice.tbShadow = False
        Me.btnSearchDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSearchDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSearchDevice.tbShowDot = False
        Me.btnSearchDevice.tbShowMoreIconImg = CType(resources.GetObject("btnSearchDevice.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSearchDevice.tbShowNew = False
        Me.btnSearchDevice.tbShowProgress = False
        Me.btnSearchDevice.tbShowTip = True
        Me.btnSearchDevice.tbShowToolTipOnButton = False
        Me.btnSearchDevice.tbSplit = "13,11,13,11"
        Me.btnSearchDevice.tbText = "查询设备"
        Me.btnSearchDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearchDevice.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnSearchDevice.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnSearchDevice.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnSearchDevice.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnSearchDevice.tbTextMouseDownPlace = 0
        Me.btnSearchDevice.tbToolTip = ""
        Me.btnSearchDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSearchDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSearchDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSearchDevice.VisibleEx = True
        '
        'btnGetPCInfo
        '
        Me.btnGetPCInfo.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnGetPCInfo.BackColor = System.Drawing.Color.Transparent
        Me.btnGetPCInfo.BindingForm = Nothing
        Me.btnGetPCInfo.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.btnGetPCInfo.Location = New System.Drawing.Point(299, 419)
        Me.btnGetPCInfo.Name = "btnGetPCInfo"
        Me.btnGetPCInfo.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnGetPCInfo.Selectable = True
        Me.btnGetPCInfo.Size = New System.Drawing.Size(111, 25)
        Me.btnGetPCInfo.TabIndex = 27
        Me.btnGetPCInfo.tbAdriftIconWhenHover = False
        Me.btnGetPCInfo.tbAutoSize = False
        Me.btnGetPCInfo.tbAutoSizeEx = False
        Me.btnGetPCInfo.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_4_white
        Me.btnGetPCInfo.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnGetPCInfo.tbBadgeNumber = 0
        Me.btnGetPCInfo.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGetPCInfo.tbEndEllipsis = False
        Me.btnGetPCInfo.tbIconHoldPlace = True
        Me.btnGetPCInfo.tbIconImage = Nothing
        Me.btnGetPCInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGetPCInfo.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGetPCInfo.tbIconMore = False
        Me.btnGetPCInfo.tbIconMouseDown = Nothing
        Me.btnGetPCInfo.tbIconMouseHover = Nothing
        Me.btnGetPCInfo.tbIconMouseLeave = Nothing
        Me.btnGetPCInfo.tbIconPlaceText = 5
        Me.btnGetPCInfo.tbIconReadOnly = Nothing
        Me.btnGetPCInfo.tbImageMouseDown = Nothing
        Me.btnGetPCInfo.tbImageMouseHover = Nothing
        Me.btnGetPCInfo.tbImageMouseLeave = Nothing
        Me.btnGetPCInfo.tbProgressValue = 50
        Me.btnGetPCInfo.tbReadOnly = False
        Me.btnGetPCInfo.tbReadOnlyText = False
        Me.btnGetPCInfo.tbShadow = False
        Me.btnGetPCInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGetPCInfo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGetPCInfo.tbShowDot = False
        Me.btnGetPCInfo.tbShowMoreIconImg = CType(resources.GetObject("btnGetPCInfo.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGetPCInfo.tbShowNew = False
        Me.btnGetPCInfo.tbShowProgress = False
        Me.btnGetPCInfo.tbShowTip = True
        Me.btnGetPCInfo.tbShowToolTipOnButton = False
        Me.btnGetPCInfo.tbSplit = "13,11,13,11"
        Me.btnGetPCInfo.tbText = "生成电脑信息"
        Me.btnGetPCInfo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGetPCInfo.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnGetPCInfo.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnGetPCInfo.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnGetPCInfo.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnGetPCInfo.tbTextMouseDownPlace = 0
        Me.btnGetPCInfo.tbToolTip = ""
        Me.btnGetPCInfo.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGetPCInfo.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGetPCInfo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGetPCInfo.VisibleEx = True
        '
        'lblWelcome
        '
        Me.lblWelcome.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWelcome.AutoSize = True
        Me.lblWelcome.BackColor = System.Drawing.Color.Transparent
        Me.lblWelcome.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWelcome.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.lblWelcome.Location = New System.Drawing.Point(16, 75)
        Me.lblWelcome.Name = "lblWelcome"
        Me.lblWelcome.Size = New System.Drawing.Size(155, 12)
        Me.lblWelcome.TabIndex = 31
        Me.lblWelcome.Text = "感谢您激活了同步推VIP服务"
        Me.lblWelcome.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'rtxContent
        '
        Me.rtxContent.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxContent.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.rtxContent.Location = New System.Drawing.Point(30, 119)
        Me.rtxContent.Name = "rtxContent"
        Me.rtxContent.ReadOnly = True
        Me.rtxContent.Size = New System.Drawing.Size(384, 286)
        Me.rtxContent.TabIndex = 32
        Me.rtxContent.Text = "" & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.rtxContent.Visible = False
        '
        'pnlLoading
        '
        Me.pnlLoading.BackColor = System.Drawing.Color.Transparent
        Me.pnlLoading.Controls.Add(Me.lblLoadInfo)
        Me.pnlLoading.Controls.Add(Me.TbPictureBox1)
        Me.pnlLoading.Location = New System.Drawing.Point(116, 184)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(200, 160)
        Me.pnlLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoading.TabIndex = 33
        Me.pnlLoading.tbBackgroundImage = Nothing
        Me.pnlLoading.tbShowWatermark = False
        Me.pnlLoading.tbSplit = "0,0,0,0"
        Me.pnlLoading.tbWatermark = Nothing
        Me.pnlLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoadInfo
        '
        Me.lblLoadInfo.AutoSize = True
        Me.lblLoadInfo.Location = New System.Drawing.Point(63, 101)
        Me.lblLoadInfo.Name = "lblLoadInfo"
        Me.lblLoadInfo.Size = New System.Drawing.Size(71, 12)
        Me.lblLoadInfo.TabIndex = 35
        Me.lblLoadInfo.tbAdriftWhenHover = False
        Me.lblLoadInfo.tbAutoEllipsis = False
        Me.lblLoadInfo.tbAutoSize = True
        Me.lblLoadInfo.tbHideImage = False
        Me.lblLoadInfo.tbIconImage = Nothing
        Me.lblLoadInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblLoadInfo.tbIconPlaceText = 5
        Me.lblLoadInfo.tbShadow = False
        Me.lblLoadInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLoadInfo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLoadInfo.tbShowScrolling = False
        Me.lblLoadInfo.Text = "正在加载..."
        '
        'TbPictureBox1
        '
        Me.TbPictureBox1.Image = Global.tbVipHelper.My.Resources.Resources.gif_loading_32
        Me.TbPictureBox1.Location = New System.Drawing.Point(79, 49)
        Me.TbPictureBox1.Name = "TbPictureBox1"
        Me.TbPictureBox1.Size = New System.Drawing.Size(32, 32)
        Me.TbPictureBox1.TabIndex = 34
        Me.TbPictureBox1.TabStop = False
        Me.TbPictureBox1.tbAutoSize = False
        Me.TbPictureBox1.tbBackgroundImage = Nothing
        Me.TbPictureBox1.tbSplit = "0,0,0,0"
        '
        'bgwGetInfo
        '
        '
        'btnContact
        '
        Me.btnContact.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnContact.BackColor = System.Drawing.Color.Transparent
        Me.btnContact.BindingForm = Nothing
        Me.btnContact.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnContact.Location = New System.Drawing.Point(316, 4)
        Me.btnContact.Margin = New System.Windows.Forms.Padding(0)
        Me.btnContact.Name = "btnContact"
        Me.btnContact.Padding = New System.Windows.Forms.Padding(4, 0, 0, 0)
        Me.btnContact.Selectable = True
        Me.btnContact.Size = New System.Drawing.Size(57, 21)
        Me.btnContact.TabIndex = 35
        Me.btnContact.tbAdriftIconWhenHover = False
        Me.btnContact.tbAutoSize = True
        Me.btnContact.tbAutoSizeEx = True
        Me.btnContact.tbBackgroundImage = Nothing
        Me.btnContact.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnContact.tbBadgeNumber = 0
        Me.btnContact.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnContact.tbEndEllipsis = False
        Me.btnContact.tbIconHoldPlace = True
        Me.btnContact.tbIconImage = Nothing
        Me.btnContact.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnContact.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnContact.tbIconMore = False
        Me.btnContact.tbIconMouseDown = Nothing
        Me.btnContact.tbIconMouseHover = Nothing
        Me.btnContact.tbIconMouseLeave = Nothing
        Me.btnContact.tbIconPlaceText = 2
        Me.btnContact.tbIconReadOnly = Nothing
        Me.btnContact.tbImageMouseDown = Nothing
        Me.btnContact.tbImageMouseHover = Global.tbVipHelper.My.Resources.Resources.btn_1_transparent
        Me.btnContact.tbImageMouseLeave = Nothing
        Me.btnContact.tbProgressValue = 50
        Me.btnContact.tbReadOnly = False
        Me.btnContact.tbReadOnlyText = False
        Me.btnContact.tbShadow = False
        Me.btnContact.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnContact.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnContact.tbShowDot = False
        Me.btnContact.tbShowMoreIconImg = CType(resources.GetObject("btnContact.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnContact.tbShowNew = False
        Me.btnContact.tbShowProgress = False
        Me.btnContact.tbShowTip = True
        Me.btnContact.tbShowToolTipOnButton = False
        Me.btnContact.tbSplit = "4,4,4,4"
        Me.btnContact.tbText = "联系客服"
        Me.btnContact.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnContact.tbTextColor = System.Drawing.Color.White
        Me.btnContact.tbTextColorDisable = System.Drawing.Color.White
        Me.btnContact.tbTextColorDown = System.Drawing.Color.White
        Me.btnContact.tbTextColorHover = System.Drawing.Color.White
        Me.btnContact.tbTextMouseDownPlace = 0
        Me.btnContact.tbToolTip = ""
        Me.btnContact.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnContact.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnContact.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnContact.VisibleEx = True
        '
        'btnRefresh
        '
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(181, 68)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(57, 23)
        Me.btnRefresh.TabIndex = 36
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.tbVipHelper.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = "刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 0
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'lblMailMsg
        '
        Me.lblMailMsg.AutoSize = True
        Me.lblMailMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblMailMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.lblMailMsg.Location = New System.Drawing.Point(28, 97)
        Me.lblMailMsg.Name = "lblMailMsg"
        Me.lblMailMsg.Size = New System.Drawing.Size(125, 12)
        Me.lblMailMsg.TabIndex = 37
        Me.lblMailMsg.tbAdriftWhenHover = False
        Me.lblMailMsg.tbAutoEllipsis = False
        Me.lblMailMsg.tbAutoSize = True
        Me.lblMailMsg.tbHideImage = False
        Me.lblMailMsg.tbIconImage = Nothing
        Me.lblMailMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMailMsg.tbIconPlaceText = 5
        Me.lblMailMsg.tbShadow = False
        Me.lblMailMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMailMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMailMsg.tbShowScrolling = False
        Me.lblMailMsg.Text = "您当前使用的邮箱是: "
        '
        'txtMail
        '
        Me.txtMail.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.txtMail.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.txtMail.Location = New System.Drawing.Point(158, 97)
        Me.txtMail.Name = "txtMail"
        Me.txtMail.ReadOnly = True
        Me.txtMail.Size = New System.Drawing.Size(147, 14)
        Me.txtMail.TabIndex = 38
        '
        'btnAddDevice
        '
        Me.btnAddDevice.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAddDevice.BackColor = System.Drawing.Color.Transparent
        Me.btnAddDevice.BindingForm = Nothing
        Me.btnAddDevice.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnAddDevice.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAddDevice.Location = New System.Drawing.Point(380, 2)
        Me.btnAddDevice.Name = "btnAddDevice"
        Me.btnAddDevice.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnAddDevice.Selectable = True
        Me.btnAddDevice.Size = New System.Drawing.Size(24, 24)
        Me.btnAddDevice.TabIndex = 46
        Me.btnAddDevice.tbAdriftIconWhenHover = False
        Me.btnAddDevice.tbAutoSize = True
        Me.btnAddDevice.tbAutoSizeEx = True
        Me.btnAddDevice.tbBackgroundImage = Global.tbVipHelper.My.Resources.Resources.btn_setting
        Me.btnAddDevice.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnAddDevice.tbBadgeNumber = 0
        Me.btnAddDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAddDevice.tbEndEllipsis = False
        Me.btnAddDevice.tbIconHoldPlace = True
        Me.btnAddDevice.tbIconImage = Nothing
        Me.btnAddDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAddDevice.tbIconMore = False
        Me.btnAddDevice.tbIconMouseDown = Nothing
        Me.btnAddDevice.tbIconMouseHover = Nothing
        Me.btnAddDevice.tbIconMouseLeave = Nothing
        Me.btnAddDevice.tbIconPlaceText = 2
        Me.btnAddDevice.tbIconReadOnly = Nothing
        Me.btnAddDevice.tbImageMouseDown = Nothing
        Me.btnAddDevice.tbImageMouseHover = Nothing
        Me.btnAddDevice.tbImageMouseLeave = Nothing
        Me.btnAddDevice.tbProgressValue = 50
        Me.btnAddDevice.tbReadOnly = False
        Me.btnAddDevice.tbReadOnlyText = False
        Me.btnAddDevice.tbShadow = False
        Me.btnAddDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAddDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAddDevice.tbShowDot = False
        Me.btnAddDevice.tbShowMoreIconImg = CType(resources.GetObject("btnAddDevice.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAddDevice.tbShowNew = False
        Me.btnAddDevice.tbShowProgress = False
        Me.btnAddDevice.tbShowTip = True
        Me.btnAddDevice.tbShowToolTipOnButton = False
        Me.btnAddDevice.tbSplit = "3,3,3,3"
        Me.btnAddDevice.tbText = ""
        Me.btnAddDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnAddDevice.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.btnAddDevice.tbTextColorDisable = System.Drawing.Color.Gray
        Me.btnAddDevice.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btnAddDevice.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btnAddDevice.tbTextMouseDownPlace = 0
        Me.btnAddDevice.tbToolTip = ""
        Me.btnAddDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAddDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAddDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddDevice.VisibleEx = True
        '
        'frmVip
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(450, 505)
        Me.Controls.Add(Me.btnAddDevice)
        Me.Controls.Add(Me.txtMail)
        Me.Controls.Add(Me.lblMailMsg)
        Me.Controls.Add(Me.btnRefresh)
        Me.Controls.Add(Me.btnContact)
        Me.Controls.Add(Me.pnlLoading)
        Me.Controls.Add(Me.rtxContent)
        Me.Controls.Add(Me.lblWelcome)
        Me.Controls.Add(Me.btnGetPCInfo)
        Me.Controls.Add(Me.btnSearchDevice)
        Me.Controls.Add(Me.btnRenew)
        Me.Controls.Add(Me.btnActivation)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.btn_minimize)
        Me.Name = "frmVip"
        Me.tbGuiBackground = Global.tbVipHelper.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "6,33,7,28"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "VIP帮助工具"
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.TbPictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents btn_minimize As iTong.Components.tbButton
    Friend WithEvents btnActivation As iTong.Components.tbButton
    Friend WithEvents btnRenew As iTong.Components.tbButton
    Friend WithEvents btnSearchDevice As iTong.Components.tbButton
    Friend WithEvents btnGetPCInfo As iTong.Components.tbButton
    Friend WithEvents lblWelcome As System.Windows.Forms.Label
    Friend WithEvents rtxContent As iTong.Components.tbRichTextBox
    Friend WithEvents pnlLoading As iTong.Components.tbPanel
    Friend WithEvents lblLoadInfo As iTong.Components.tbLabel
    Friend WithEvents TbPictureBox1 As iTong.Components.tbPictureBox
    Friend WithEvents bgwGetInfo As System.ComponentModel.BackgroundWorker
    Friend WithEvents btnContact As iTong.Components.tbButton
    Friend WithEvents btnRefresh As iTong.Components.tbButton
    Friend WithEvents lblMailMsg As iTong.Components.tbLabel
    Friend WithEvents txtMail As System.Windows.Forms.TextBox
    Friend WithEvents btnAddDevice As iTong.Components.tbButton

End Class
