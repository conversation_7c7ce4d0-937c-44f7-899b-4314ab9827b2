﻿Imports iTong.CoreFoundation
Imports System.IO
Imports Microsoft.Win32

Public Class frmCreatePremiumFeature

#Region "--- 初始化 ---"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong
        Me.txtComputer.Text = VipHelper.GetMacid()
        Me.txtInstallPath.Text = Path.GetDirectoryName(Me.GetZSInstallPath(mZhuShouName))
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Try
            If String.IsNullOrEmpty(txtSku.Text.Trim) Then
                Me.lblMsg.Text = "请输入sku"
                Me.txtSku.Focus()
                Return
            End If

            If String.IsNullOrEmpty(txtUdid.Text.Trim) Then
                Me.lblMsg.Text = "请输入udid"
                Me.txtUdid.Focus()
                Return
            End If

            If String.IsNullOrEmpty(txtComputer.Text.Trim) Then
                Me.lblMsg.Text = "请输入电脑信息"
                Me.txtComputer.Focus()
                Return
            End If

            If String.IsNullOrEmpty(txtNewSku.Text.Trim()) Then
                Me.lblMsg.Text = "请输入新的sku"
                Me.txtNewSku.Focus()
                Return
            End If


            Dim info As New PremiumFeatureInfo

            '----------------------配置生成-----------------------
            info.IsOverdue = False
            info.StrEmail = VipHelper.GetConfigMail()  '"<EMAIL>"

            Dim lstSku As New List(Of String)
            'lstSku.Add("com.tencent.weixin.********.x")
            lstSku.Add(txtSku.Text.Trim())
            info.LstSku = lstSku

            Dim lstDevice As New List(Of String)
            lstDevice.Add(txtUdid.Text.Trim())
            info.LstDevice = lstDevice

            Dim lstComputer As New List(Of String)
            'lstComputer.Add("e89bcb415ddaf8a8dd6e7d576e44b56e")
            lstComputer.Add(txtComputer.Text.Trim())
            info.LstComputer = lstComputer

            info.StrNewSku = txtNewSku.Text.Trim()    '"com.tencent.xin.mjuruangong1"

            Dim blnResult As Boolean = SetWeChatVIPSpareInfo(info)
            If Not blnResult Then
                MessageBox.Show("生成失败", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnOK_Click")
        End Try
    End Sub

    Private Sub btnAddChageskuDll_Click(sender As Object, e As EventArgs) Handles btnAddChageskuDll.Click
        Try
            Dim strFolder As String = txtInstallPath.Text.Trim()
            If String.IsNullOrEmpty(strFolder) Then
                Me.lblMsgChageSku.Text = "请输入助手安装目录"
                Me.txtInstallPath.Focus()
                Return
            End If

            Dim strPathChangeSku As String = Path.Combine(strFolder, "ChangeSku.dll")
            If Not File.Exists(strPathChangeSku) Then
                File.Create(strPathChangeSku).Close()
            End If
            MessageBox.Show("生成ChangeSku.dll成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnAddChageskuDll_Click")
            MessageBox.Show("生成ChangeSku.dll失败", "提示", MessageBoxButtons.OK)
        End Try
    End Sub

    Private Sub btnDeleteChageskuDll_Click(sender As Object, e As EventArgs) Handles btnDeleteChageskuDll.Click
        Try
            Dim strFolder As String = txtInstallPath.Text.Trim()
            If String.IsNullOrEmpty(strFolder) Then
                Me.lblMsg.Text = "请输入助手安装目录"
                Me.txtInstallPath.Focus()
                Return
            End If

            Dim strPathChangeSku As String = Path.Combine(strFolder, "ChangeSku.dll")
            If File.Exists(strPathChangeSku) Then
                File.Delete(strPathChangeSku)
            End If
            MessageBox.Show("删除ChangeSku.dll成功", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnDeleteChageskuDll_Click")
            MessageBox.Show("删除ChangeSku.dll失败", "提示", MessageBoxButtons.OK)
        End Try
    End Sub

    Private mPostUrl As String = "http://pc.api.tongbu.com/vip/v.html?t=6"
    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        Try
            If String.IsNullOrEmpty(txtDeviceUdid.Text.Trim()) Then
                Me.lblMsgAddDevice.Text = "请输入当前服务设备的udid"
                Return
            End If
            Dim mail As String = VipHelper.GetConfigMail()
            Dim dicContent As New JsonObject
            dicContent.Add("mail", mail)
            dicContent.Add("projectid", 1)
            dicContent.Add("macid", VipHelper.GetMacid())
            dicContent.Add("hdd", Common.GetHardDiskID2())
            dicContent.Add("cpu", Common.GetProcessorId())
            dicContent.Add("wifimac", Common.GetMacAddress().Trim())
            dicContent.Add("phoneid", txtDeviceUdid.Text.Trim())
            Dim writer As New JsonWriter()
            dicContent.Write(writer)
            Dim strContent As String = writer.ToString()
            Dim strResult As String = VipHelper.PostData(mPostUrl, strContent)
            strResult = VipHelper.DecryptDES(strResult)
            Dim objJson As JsonObject = JsonParser.ParseString(strResult)
            '{"state": "1","data": :{"code":"-1失败，0不可用，1成功，2授权已过期,"msg":""}}
            If objJson IsNot Nothing AndAlso objJson.ContainsKey("data") Then
                Dim jsonData As JsonObject = CType(objJson("data"), JsonObject)
                Dim code As Integer = CType(jsonData("code"), JsonNumber).Value
                Dim msg As String = CType(jsonData("msg"), JsonString).Value
                If code = 1 Then
                    'Me.DialogResult = Windows.Forms.DialogResult.OK
                    'Me.Close()
                    MessageBox.Show("绑定成功", "提示信息", MessageBoxButtons.OK, MessageBoxIcon.Information)
                Else
                    lblMsgAddDevice.Text = msg
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnAdd_Click")
        End Try
    End Sub
#End Region

#Region "--- 私有方法 ---"

    Private Shared VIPWC As String = "&88%19@@"
    Public Shared Function SetWeChatVIPSpareInfo(ByVal PFInfo As PremiumFeatureInfo) As Boolean
        'sku  电脑信息  设备  邮箱  容错次数 是否过期 
        Dim isRelust As Boolean = False
        Try
            Dim strValue As String = ""
            Dim strSku As String = ""
            If PFInfo.LstSku.Count > 0 Then
                For Each sku As String In PFInfo.LstSku
                    strSku = sku & "『"
                Next
            End If

            Dim strComputer As String = ""
            If PFInfo.LstComputer.Count > 0 Then
                For Each computer As String In PFInfo.LstComputer
                    strComputer = computer & "『"
                Next
            End If

            Dim strDevice As String = ""
            If PFInfo.LstDevice.Count > 0 Then
                For Each device As String In PFInfo.LstDevice
                    strDevice = device & "『"
                Next
            End If

            strValue = String.Format("{0}〖{1}〖{2}〖{3}〖{4}〖{5}〖{6}", strSku, strComputer, strDevice, PFInfo.StrEmail, PFInfo.IntTryCount, PFInfo.IsOverdue, PFInfo.StrNewSku)
            Dim strFolderPreminmIniFile As String = Path.Combine(Folder.TempFolder, "PremiumFeature")
            Folder.CheckFolder(strFolderPreminmIniFile)
            Dim strPathPreminmIniFile As String = Path.Combine(strFolderPreminmIniFile, "PremiumFeature.ini")
            isRelust = IniClass.SetIniSectionKey("WeChatVIPCoexist", "WeChatVIPSpareInfo", Common.EncryptDES(strValue, VIPWC, VIPWC), strPathPreminmIniFile)
            IniClass.SetIniSectionKey("WeChatVIPCoexist", "ShowWeChatVIPSpare", "True", strPathPreminmIniFile)
            Common.OpenExplorer(strFolderPreminmIniFile)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetWeChatVIPSpareInfo")
        End Try
        Return isRelust
    End Function

    Private mZhuShouName As String = "Tongbu3"
    Private Function GetZSInstallPath(ByVal strKey As String) As String


        Dim strDir As String = String.Empty

        Try
            If Common.IsOS_Of_64Bit() AndAlso Common.IsX64 = False Then
                Dim key64 As RegistryKey64 = RegistryKey64.OpenKey(RegistryHive.LocalMachine, "Software\Microsoft\Windows\CurrentVersion\Uninstall\" & strKey, False, RegistryKey64.RegWow64Options.KEY_WOW64_64KEY)

                If key64 IsNot Nothing Then
                    Dim objValue As Object = key64.GetValue("DisplayIcon")
                    If objValue IsNot Nothing Then
                        strDir = objValue.ToString()
                    End If
                    key64.Close()
                Else
                    Dim key As RegistryKey = Registry.LocalMachine.OpenSubKey("SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\" & strKey)
                    If key IsNot Nothing Then
                        Dim objValue As Object = key.GetValue("DisplayIcon")
                        If objValue IsNot Nothing Then
                            strDir = objValue.ToString()
                        End If
                        key.Close()
                    End If
                End If
            Else
                Dim key As RegistryKey = Registry.LocalMachine.OpenSubKey("Software\Microsoft\Windows\CurrentVersion\Uninstall\" & strKey)
                If key IsNot Nothing Then
                    Dim objValue As Object = key.GetValue("DisplayIcon")
                    If objValue IsNot Nothing Then
                        strDir = objValue.ToString()
                    End If
                    key.Close()
                End If
            End If

            If String.IsNullOrEmpty(strDir) Or Directory.Exists(strDir) = False Then
                Dim key As RegistryKey = Registry.LocalMachine.OpenSubKey("SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\" & strKey)
                If key IsNot Nothing Then
                    Dim objValue As Object = key.GetValue("DisplayIcon")
                    If objValue IsNot Nothing Then
                        strDir = objValue.ToString()
                    End If
                    key.Close()
                End If

            End If


        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetZSInstallPath")
        Finally
            If String.IsNullOrEmpty(strDir) Or Directory.Exists(strDir) = False Then
                Dim strTemp As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonProgramFiles), "Tongbu\" & strKey)
                If Directory.Exists(strTemp) Then
                    strDir = strTemp
                ElseIf Common.IsOS_Of_64Bit() AndAlso Common.IsX64 = False Then
                    strTemp = strTemp.Replace("Program Files", "Program Files (x86)")
                    If Directory.Exists(strTemp) Then
                        strDir = strTemp
                    End If
                End If
            End If
        End Try

        Return strDir
    End Function
#End Region

    Public Class PremiumFeatureInfo

        Private _IsLoadServer As Boolean = False
        Public Property IsLoadServer() As Boolean
            Get
                Return Me._IsLoadServer
            End Get
            Set(ByVal value As Boolean)
                Me._IsLoadServer = value
            End Set
        End Property

        'sku  电脑信息  设备  邮箱  容错次数 是否过期 

        Private _LstSku As New List(Of String)
        Public Property LstSku() As List(Of String)
            Get
                Return Me._LstSku
            End Get
            Set(ByVal value As List(Of String))
                Me._LstSku = value
            End Set
        End Property

        Private _LstComputer As New List(Of String)
        Public Property LstComputer() As List(Of String)
            Get
                Return Me._LstComputer
            End Get
            Set(ByVal value As List(Of String))
                Me._LstComputer = value
            End Set
        End Property

        Private _LstDevice As New List(Of String)
        Public Property LstDevice() As List(Of String)
            Get
                Return Me._LstDevice
            End Get
            Set(ByVal value As List(Of String))
                Me._LstDevice = value
            End Set
        End Property

        Private _StrEmail As String = ""
        Public Property StrEmail() As String
            Get
                Return Me._StrEmail
            End Get
            Set(ByVal value As String)
                Me._StrEmail = value
            End Set
        End Property

        Private _IntTryCount As Integer = 0
        Public Property IntTryCount() As Integer
            Get
                Return Me._IntTryCount
            End Get
            Set(ByVal value As Integer)
                Me._IntTryCount = value
            End Set
        End Property

        Private _IsOverdue As Boolean = False
        Public Property IsOverdue() As Boolean
            Get
                Return Me._IsOverdue
            End Get
            Set(ByVal value As Boolean)
                Me._IsOverdue = value
            End Set
        End Property

        Private _StrNewSku As String = ""
        Public Property StrNewSku() As String
            Get
                Return Me._StrNewSku
            End Get
            Set(ByVal value As String)
                Me._StrNewSku = value
            End Set
        End Property

    End Class

   
End Class