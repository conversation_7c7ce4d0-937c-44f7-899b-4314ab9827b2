﻿using System;
using System.Drawing;
using System.Collections;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Text;

using Foundation;
using CoreGraphics;
using AppKit;
using CoreText;

using iTong.CoreFoundation;
using CoreImage;

namespace iTong.CoreModule
{
    public static class skGuiHelperExtend
    {
        public static skImageAlignment ToImageAlign(this NSTextAlignment align)
        {
            skImageAlignment imgAlign = skImageAlignment.Left;

            if (align == NSTextAlignment.Center)
                imgAlign = skImageAlignment.Center;
            else if (align == NSTextAlignment.Right)
                imgAlign = skImageAlignment.Right;

            return imgAlign;
        }
    }

    public static class skGuiHelper
    {



        private static bool mCanDrawWithCGContent = false;
        private static object mLockerCache = new object();
        private static Dictionary<NSImage, NSImage> mDictImageDisabled = new Dictionary<NSImage, NSImage>();
        private static Dictionary<NSFont, CGSize> mDictFont = new Dictionary<NSFont, CGSize>();
        private static List<string> mListPunctution = new List<string>();

        static skGuiHelper()
        {
            mCanDrawWithCGContent = (Common.OSVersion >= new Version("10.10"));

            mListPunctution.Add(":");
            mListPunctution.Add(",");
            mListPunctution.Add(".");
            mListPunctution.Add("!");
            mListPunctution.Add("?");
            mListPunctution.Add(")");
            mListPunctution.Add("]");
            mListPunctution.Add("}");
            mListPunctution.Add(">");
            mListPunctution.Add("\"");

            mListPunctution.Add("：");
            mListPunctution.Add("，");
            mListPunctution.Add("。");
            mListPunctution.Add("！");
            mListPunctution.Add("？");
            mListPunctution.Add("）");
            mListPunctution.Add("】");
            mListPunctution.Add("、");
            mListPunctution.Add("》");
            mListPunctution.Add("”");
        }

        public static bool CanDrawWithCGContent
        {
            get
            {
                return mCanDrawWithCGContent;
            }
        }

        public static NSImage CreateDisabledImage(NSImage srcImage)
        {
            NSImage disabledImage = null;

            try
            {
                if (srcImage == null)
                    goto DoExit;

                // 创建一个新的CIImage
                using (CIImage ciImage = new CIImage(srcImage.CGImage))
                {
                    // 创建颜色矩阵滤镜
                    using (CIFilter colorControls = CIFilter.FromName("CIColorControls"))
                    {
                        colorControls.SetValueForKey(ciImage, CIFilterInputKey.Image);
                        colorControls.SetValueForKey(NSNumber.FromFloat(0.0f), CIFilterInputKey.Saturation); // 设置饱和度为0，使图片变灰
                        colorControls.SetValueForKey(NSNumber.FromFloat(0.5f), CIFilterInputKey.Brightness); // 调整亮度

                        // 获取处理后的图片
                        using (CIImage outputImage = colorControls.OutputImage)
                        {

                            // 创建CIContext
                            using (CIContext context = new CIContext())
                            {

                                // 将CIImage转换为CGImage
                                using (CGImage cgImage = context.CreateCGImage(outputImage, outputImage.Extent))
                                {

                                    // 创建新的NSImage
                                    disabledImage = new NSImage(cgImage, srcImage.Size);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                disabledImage = srcImage;
                Common.LogException(ex, "CreateDisabledImage");
            }

        DoExit:
            return disabledImage;
        }

        public static NSImage GetDisabledImage(NSImage srcImage)
        {
            NSImage img = null;

            lock (mLockerCache)
            {
                if (mDictImageDisabled.ContainsKey(srcImage))
                    img = mDictImageDisabled[srcImage];
                else
                    img = CreateDisabledImage(srcImage);
            }

            return img;
        }

        public static nfloat FormatToPI(int angle)
        {
            double newAngle = (angle % 360 + 360) % 360 / 360.0;

            return (nfloat)(newAngle * 2 * Math.PI);
        }

        public static NSBezierPath CreateRoundPath(CGRect r, nfloat r1, nfloat r2, nfloat r3, nfloat r4)
        {
            NSBezierPath p = new NSBezierPath();

            if (r.Width > 0 && r.Height > 0)
            {
                nfloat x = r.X;
                nfloat y = r.Y;
                nfloat w = r.Width;
                nfloat h = r.Height;

                //AppendPathWithArc为顺时针方向，角度分别为：0、270、180、90
                //AppendPathWithArc为逆时针方向，角度分别为：0、90、180、270
                p.MoveTo(new CGPoint(x, y + r1));

                p.LineTo(new CGPoint(x, y + h - r2));
                p.AppendPathWithArc(new CGPoint(x + r2, y + h - r2), r2, 180, 90, true);

                p.LineTo(new CGPoint(x + w - r3, y + h));
                p.AppendPathWithArc(new CGPoint(x + w - r3, y + h - r3), r3, 90, 0, true);

                p.LineTo(new CGPoint(x + w, y + r4));
                p.AppendPathWithArc(new CGPoint(x + w - r4, y + r4), r4, 0, 270, true);

                p.LineTo(new CGPoint(x + r1, y));
                p.AppendPathWithArc(new CGPoint(x + r1, y + r1), r1, 270, 180, true);

                p.ClosePath();
            }

            return p;
        }

        public static NSBezierPath CreateRoundPath(CGRect rect,
                                                    skBorderRadius radius,
                                                    skTrianglePosition trianglePosition = skTrianglePosition.None,
                                                    int triangleWidth = 10,
                                                    int triangleHeight = 15,
                                                    int trianglePlaceCorner = 30,
                                                    int lineWidth = 1)
        {
            NSBezierPath p = new NSBezierPath();

            if (rect.Width <= 0 || rect.Height <= 0)
                goto DoExit;

            //if (trianglePosition == tbTrianglePosition.None)
            //{
            //    p = CreateRoundPath(rect, radius.TopLeft, radius.TopRight, radius.BottomRight, radius.BottomLeft);
            //    goto DoExit;
            //}

            nfloat rTopLeft = radius.TopLeft;
            nfloat rTopRight = radius.TopRight;
            nfloat rBottomRight = radius.BottomRight;
            nfloat rBottomLeft = radius.BottomLeft;

            nfloat x = rect.X;
            nfloat y = rect.Y;
            nfloat w = rect.Width;
            nfloat h = rect.Height;

            if (ExtendControl.IsWindowAnchor)
            {
                if (trianglePosition == skTrianglePosition.Top)
                {
                    trianglePosition = skTrianglePosition.Bottom;
                }
                else if (trianglePosition == skTrianglePosition.TopLeft)
                {
                    trianglePosition = skTrianglePosition.BottomLeft;
                }
                else if (trianglePosition == skTrianglePosition.TopRight)
                {
                    trianglePosition = skTrianglePosition.BottomRight;
                }
                else if (trianglePosition == skTrianglePosition.Bottom)
                {
                    trianglePosition = skTrianglePosition.Top;
                }
                else if (trianglePosition == skTrianglePosition.BottomLeft)
                {
                    trianglePosition = skTrianglePosition.TopLeft;
                }
                else if (trianglePosition == skTrianglePosition.BottomRight)
                {
                    trianglePosition = skTrianglePosition.TopRight;
                }
            }

            if (trianglePosition == skTrianglePosition.Left || trianglePosition == skTrianglePosition.LeftTop || trianglePosition == skTrianglePosition.LeftBottom)
            {
                x = rect.X + triangleHeight;
                w = rect.Width - triangleHeight;
            }
            else if (trianglePosition == skTrianglePosition.Top || trianglePosition == skTrianglePosition.TopLeft || trianglePosition == skTrianglePosition.TopRight)
            {
                y = rect.Y + triangleHeight;
                h = rect.Height - triangleHeight;
            }
            else if (trianglePosition == skTrianglePosition.Right || trianglePosition == skTrianglePosition.RightTop || trianglePosition == skTrianglePosition.TopRight)
            {
                w = rect.Width - triangleHeight;
            }
            else if (trianglePosition == skTrianglePosition.Bottom || trianglePosition == skTrianglePosition.BottomLeft || trianglePosition == skTrianglePosition.BottomRight)
            {
                h = rect.Height - triangleHeight;
            }

            //AppendPathWithArc为逆时针方向，角度分别为：0PI、0.5PI、1PI、1.5PI，水平正方向为0PI、垂直负方向为0.5PI、水平负方法为1PI、垂直正方向为1.5PI

            p.MoveTo(new CGPoint(x + rBottomLeft, y));

            //绘制下边框
            if (trianglePosition == skTrianglePosition.TopLeft)
            {
                p.LineTo(new CGPoint(x + rBottomLeft + trianglePlaceCorner, y));
                p.LineTo(new CGPoint(x + rBottomLeft + trianglePlaceCorner + triangleWidth / 2, y - triangleHeight));
                p.LineTo(new CGPoint(x + rBottomLeft + trianglePlaceCorner + triangleWidth, y));
                p.LineTo(new CGPoint(x + w - rBottomRight, y));
            }
            else if (trianglePosition == skTrianglePosition.Top)
            {
                p.LineTo(new CGPoint(x + w / 2 - triangleWidth / 2, y));
                p.LineTo(new CGPoint(x + w / 2, y - triangleHeight));
                p.LineTo(new CGPoint(x + w / 2 + triangleWidth / 2, y));
                p.LineTo(new CGPoint(x + w - rBottomRight, y));
            }
            else if (trianglePosition == skTrianglePosition.TopRight)
            {
                p.LineTo(new CGPoint(x + w - trianglePlaceCorner - triangleWidth, y));
                p.LineTo(new CGPoint(x + w - trianglePlaceCorner - triangleWidth / 2, y - triangleHeight));
                p.LineTo(new CGPoint(x + w - trianglePlaceCorner, y));
                p.LineTo(new CGPoint(x + w - rBottomRight, y));
            }
            else
            {
                p.LineTo(new CGPoint(x + w - rBottomRight, y));
            }

            if (rBottomRight > 0)
            {
                //绘制下右圆角
                p.AppendPathWithArc(new CGPoint(x + w - rBottomRight, y + rBottomRight), rBottomRight, 270, 0, false);
            }


            //绘制右边框
            if (trianglePosition == skTrianglePosition.RightTop)
            {
                p.LineTo(new CGPoint(x + w, y + rTopRight + trianglePlaceCorner));
                p.LineTo(new CGPoint(x + w + triangleHeight, y + rTopRight + trianglePlaceCorner + triangleWidth / 2));
                p.LineTo(new CGPoint(x + w, y + rTopRight + trianglePlaceCorner + triangleWidth));
                p.LineTo(new CGPoint(x + w, y + h - rTopRight));
            }
            else if (trianglePosition == skTrianglePosition.Right)
            {
                p.LineTo(new CGPoint(x + w, y + h / 2 - triangleWidth / 2));
                p.LineTo(new CGPoint(x + w + triangleHeight, y + h / 2));
                p.LineTo(new CGPoint(x + w, y + h / 2 + triangleWidth / 2));
                p.LineTo(new CGPoint(x + w, y + h - rTopRight));
            }
            else if (trianglePosition == skTrianglePosition.RightBottom)
            {
                p.LineTo(new CGPoint(x + w, y + h - rTopRight - trianglePlaceCorner - triangleWidth));
                p.LineTo(new CGPoint(x + w + triangleHeight, y + h - rTopRight - trianglePlaceCorner - triangleWidth / 2));
                p.LineTo(new CGPoint(x + w, y + h - rTopRight - trianglePlaceCorner));
                p.LineTo(new CGPoint(x + w, y + h - rTopRight));
            }
            else
            {
                p.LineTo(new CGPoint(x + w, y + h - rTopRight));
            }

            if (rTopRight > 0)
            {
                //绘制上右圆角
                p.AppendPathWithArc(new CGPoint(x + w - rTopRight, y + h - rTopRight), rTopRight, 0, 90, false);
            }
            

            //绘制上边框
            if (trianglePosition == skTrianglePosition.BottomRight)
            {
                p.LineTo(new CGPoint(x + w - rBottomRight - trianglePlaceCorner, y + h));
                p.LineTo(new CGPoint(x + w - rBottomRight - trianglePlaceCorner - triangleWidth / 2, y + h + triangleHeight));
                p.LineTo(new CGPoint(x + w - rBottomRight - trianglePlaceCorner - triangleWidth, y + h));
                p.LineTo(new CGPoint(x + rTopLeft, y + h));
            }
            else if (trianglePosition == skTrianglePosition.Bottom)
            {
                p.LineTo(new CGPoint(x + w / 2 + triangleWidth / 2, y + h));
                p.LineTo(new CGPoint(x + w / 2, y + h + triangleHeight));
                p.LineTo(new CGPoint(x + w / 2 - triangleWidth / 2, y + h));
                p.LineTo(new CGPoint(x + rTopLeft, y + h));
            }
            else if (trianglePosition == skTrianglePosition.BottomLeft)
            {
                p.LineTo(new CGPoint(x + rTopLeft + trianglePlaceCorner + triangleWidth, y + h));
                p.LineTo(new CGPoint(x + rTopLeft + trianglePlaceCorner + triangleWidth / 2, y + h + triangleHeight));
                p.LineTo(new CGPoint(x + rTopLeft + trianglePlaceCorner, y + h));
                p.LineTo(new CGPoint(x + rTopLeft, y + h));
            }
            else
            {
                p.LineTo(new CGPoint(x + rTopLeft, y + h));
            }

            if (rBottomLeft > 0)
            {
                //绘制上左圆角           
                p.AppendPathWithArc(new CGPoint(x + rTopLeft, y + h - rTopLeft), rTopLeft, 90, 180, false);
            }

            //绘制左边框
            if (trianglePosition == skTrianglePosition.LeftBottom)
            {
                p.LineTo(new CGPoint(x, y + rBottomLeft + trianglePlaceCorner + triangleWidth));
                p.LineTo(new CGPoint(x - triangleHeight, y + rBottomLeft + trianglePlaceCorner + triangleWidth / 2));
                p.LineTo(new CGPoint(x, y + rBottomLeft + trianglePlaceCorner));
                p.LineTo(new CGPoint(x, y + rBottomLeft));
            }
            else if (trianglePosition == skTrianglePosition.Left)
            {
                p.LineTo(new CGPoint(x, y + h / 2 + triangleWidth / 2));
                p.LineTo(new CGPoint(x - triangleHeight, y + h / 2));
                p.LineTo(new CGPoint(x, y + h / 2 - triangleWidth / 2));
                p.LineTo(new CGPoint(x, y + rBottomLeft));
            }
            else if (trianglePosition == skTrianglePosition.LeftTop)
            {
                p.LineTo(new CGPoint(x, y + h - rTopLeft - trianglePlaceCorner));
                p.LineTo(new CGPoint(x - triangleHeight, y + h - rTopLeft - trianglePlaceCorner - triangleWidth / 2));
                p.LineTo(new CGPoint(x, y + h - rTopLeft - trianglePlaceCorner - triangleWidth));
                p.LineTo(new CGPoint(x, y + rBottomLeft));
            }
            else
            {
                p.LineTo(new CGPoint(x, y + rBottomLeft));
            }

            if (rBottomLeft > 0)
            {
                //绘制左下圆角           
                p.AppendPathWithArc(new CGPoint(x + rBottomLeft, y + rBottomLeft), rBottomLeft, 180, 270, false);
            }

            p.ClosePath();

        DoExit:
            return p;
        }

        public static NSBezierPath CreateTrianglePath(CGRect rect,
                                                skTrianglePosition trianglePosition = skTrianglePosition.None,
                                                int intTriangleWidth = 0,
                                                int intTriangleHeight = 0)
        {
            NSBezierPath p = new NSBezierPath();

            if (rect.Width <= 0 || rect.Height <= 0)
                goto DoExit;

            nfloat x = rect.X;
            nfloat y = rect.Y;
            nfloat w = rect.Width;
            nfloat h = rect.Height;
            nfloat xCenter = rect.X + rect.Width / 2;
            nfloat yCenter = rect.Y + rect.Height / 2;
            nfloat triangleWidth = intTriangleWidth;
            nfloat triangleHeight = intTriangleHeight;

            switch (trianglePosition)
            {
                case skTrianglePosition.Left:
                case skTrianglePosition.LeftTop:
                case skTrianglePosition.LeftBottom:
                    if (triangleWidth == 0)
                        triangleWidth = rect.Width / 2;
                    if (triangleHeight == 0)
                        triangleHeight = rect.Height;

                    p.MoveTo(new CGPoint(xCenter + triangleWidth / 2, y));
                    p.LineTo(new CGPoint(xCenter + triangleWidth / 2, y + h));
                    p.LineTo(new CGPoint(xCenter - triangleWidth / 2, yCenter));
                    p.LineTo(new CGPoint(xCenter + triangleWidth / 2, y));

                    break;

                case skTrianglePosition.Right:
                case skTrianglePosition.RightTop:
                case skTrianglePosition.RightBottom:
                    if (triangleWidth == 0)
                        triangleWidth = rect.Width / 2;
                    if (triangleHeight == 0)
                        triangleHeight = rect.Height;

                    p.MoveTo(new CGPoint(xCenter - triangleWidth / 2, y));
                    p.LineTo(new CGPoint(xCenter - triangleWidth / 2, y + h));
                    p.LineTo(new CGPoint(xCenter + triangleWidth / 2, yCenter));
                    p.LineTo(new CGPoint(xCenter - triangleWidth / 2, y));

                    break;

                case skTrianglePosition.Top:
                case skTrianglePosition.TopLeft:
                case skTrianglePosition.TopRight:
                    if (triangleWidth == 0)
                        triangleWidth = rect.Width;
                    if (triangleHeight == 0)
                        triangleHeight = rect.Height / 2;

                    p.MoveTo(new CGPoint(x, yCenter + triangleHeight / 2));
                    p.LineTo(new CGPoint(x + w, yCenter + triangleHeight / 2));
                    p.LineTo(new CGPoint(xCenter, yCenter - triangleHeight / 2));
                    p.LineTo(new CGPoint(x, yCenter + triangleHeight / 2));

                    break;

                case skTrianglePosition.Bottom:
                case skTrianglePosition.BottomLeft:
                case skTrianglePosition.BottomRight:
                    if (triangleWidth == 0)
                        triangleWidth = rect.Width;
                    if (triangleHeight == 0)
                        triangleHeight = rect.Height / 2;

                    p.MoveTo(new CGPoint(x, yCenter - triangleHeight / 2));
                    p.LineTo(new CGPoint(x + w, yCenter - triangleHeight / 2));
                    p.LineTo(new CGPoint(xCenter, yCenter + triangleHeight / 2));
                    p.LineTo(new CGPoint(x, yCenter - triangleHeight / 2));

                    break;
            }

            p.ClosePath();

        DoExit:
            return p;
        }

        public static void DrawDashPath(CGPath path, NSColor color, float lineWidth = 1f, nfloat[] arrDashLine = null, CGPathDrawingMode mode = CGPathDrawingMode.Fill)
        {
            if (arrDashLine == null)
                arrDashLine = new nfloat[] { 4, 3 };

            DrawPath(path, color, lineWidth, arrDashLine, mode);
        }

        public static void DrawPath(CGPath path, NSColor color, float lineWidth = 1f, nfloat[] arrDashLine = null, CGPathDrawingMode mode = CGPathDrawingMode.Fill)
        {
            if (!CanDrawWithCGContent)
                return;

            CGContext g = NSGraphicsContext.CurrentContext.CGContext;

            g.AddPath(path);
            g.SetStrokeColor(color.CGColor);

            if (arrDashLine != null)
                g.SetLineDash(0, arrDashLine);

            if (lineWidth > 0)
                g.SetLineWidth(lineWidth);

            g.DrawPath(mode);
        }

        public static void FillPath(CGPath path, NSColor color)
        {
            if (!CanDrawWithCGContent)
                return;

            CGContext g = NSGraphicsContext.CurrentContext.CGContext;

            g.AddPath(path);
            g.SetFillColor(color.CGColor);
            g.FillPath();
        }

        public static void DrawPath(NSBezierPath path, NSColor color, CGPathDrawingMode mode = CGPathDrawingMode.Fill, float lineWidth = 1f)
        {
            path.Flatness = 0.1f;
            path.SetClip();
            path.LineWidth = lineWidth;

            color.Set();

            if (mode == CGPathDrawingMode.Stroke)
            {
                path.Stroke();
            }
            else if (mode == CGPathDrawingMode.Fill)
            {
                path.Fill();
            }
            else if (mode == CGPathDrawingMode.FillStroke)
            {
                path.Stroke();
                path.Fill();
            }

            path.Dispose();
        }

        public static CGRect CalcTextRect(CGRect rect, CGSize imgSize, skImageAlignment align, Padding paddingSource, CGSize textSize, int iconPlaceText, bool upToDown = true, bool iconBeforeText = true)
        {
            if (imgSize.IsEmpty)
                iconPlaceText = 0;

            Padding padding = ExtendControl.IsWindowAnchor ? new Padding(paddingSource.Left, paddingSource.Bottom, paddingSource.Right, paddingSource.Top) : paddingSource;

            int intMaxW = (int)rect.Width - padding.Horizontal;
            if (textSize.Width > intMaxW)
                textSize = new CGSize(intMaxW, textSize.Height);

            nfloat intX = rect.X;
            nfloat intY = rect.Y;
            nfloat intW = 0;
            nfloat intH = 0;

            switch (align)
            {
                case skImageAlignment.TopLeft:
                case skImageAlignment.Top:
                case skImageAlignment.TopRight:
                    //intX += padding.Left;
                    //intY += padding.Top + (int)imgSize.Height + iconPlaceText;
                    //intW = (int)rect.Width - padding.Horizontal;
                    //intH = (int)rect.Bottom - padding.Vertical - (int)imgSize.Height - iconPlaceText;
                    if (upToDown)
                    {
                        if (iconBeforeText)
                        {
                            intX += padding.Left;
                            intY = (int)(rect.Bottom - (padding.Bottom + imgSize.Height + iconPlaceText));
                            intW = (int)(rect.Width - padding.Horizontal);
                            intH = (int)(rect.Bottom - padding.Vertical - imgSize.Height - iconPlaceText);
                        }
                        else
                        {
                            intX += padding.Left;
                            intY = (int)(rect.Top + (padding.Top + imgSize.Height + iconPlaceText));
                            intW = (int)rect.Width - padding.Horizontal;
                            intH = (int)(rect.Bottom - padding.Vertical - imgSize.Height - iconPlaceText);
                        }
                    }
                    else
                    {
                        if (iconBeforeText)
                        {
                            intX += padding.Left + (int)imgSize.Width + iconPlaceText;
                            intY += padding.Top + ((int)imgSize.Height - (int)textSize.Height) / 2.0f;
                            intW = (int)(rect.Width - padding.Horizontal - imgSize.Width - iconPlaceText);
                            intH = (int)textSize.Height;
                        }
                        else
                        {
                            intX += padding.Left;
                            intY += padding.Top + ((int)imgSize.Height - (int)textSize.Height) / 2.0f;
                            intW = (int)textSize.Width;
                            intH = (int)textSize.Height;
                        }
                    }
                    break;

                case skImageAlignment.Left:
                    if (iconBeforeText)
                    {
                        intX += padding.Left + (int)imgSize.Width + iconPlaceText;
                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)textSize.Height) / 2.0f;
                        intW = (int)rect.Width - padding.Horizontal - (int)imgSize.Width - iconPlaceText;
                        intH = (int)textSize.Height;
                    }
                    else
                    {
                        intX += padding.Left;
                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)textSize.Height) / 2.0f;
                        intW = (int)rect.Width - padding.Horizontal - (int)imgSize.Width - iconPlaceText;
                        intH = (int)textSize.Height;
                    }
                    break;

                case skImageAlignment.Center:
                    if (upToDown)
                    {
                        //从上到下居中显示
                        intX += padding.Left;

                        if (iconBeforeText)
                            intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height - (int)textSize.Height - iconPlaceText) / 2.0f;
                        else
                            intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height - (int)textSize.Height - iconPlaceText) / 2.0f + (int)imgSize.Height + iconPlaceText;

                        intW = (int)rect.Width - padding.Horizontal;
                        intH = (int)textSize.Height;
                    }
                    else
                    {
                        //从左到右居中显示
                        if (iconBeforeText)
                            intX += padding.Left + ((int)rect.Width - padding.Horizontal - (int)imgSize.Width - (int)textSize.Width - iconPlaceText) / 2.0f + (int)imgSize.Width + iconPlaceText;
                        else
                            intX += padding.Left + ((int)rect.Width - padding.Horizontal - (int)imgSize.Width - (int)textSize.Width - iconPlaceText) / 2.0f;

                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)textSize.Height) / 2.0f;
                        intW = (int)textSize.Width;
                        intH = (int)textSize.Height;
                    }
                    break;

                case skImageAlignment.Right:
                    if (iconBeforeText)
                    {
                        intX += padding.Left + ((int)rect.Width - padding.Right - (int)imgSize.Width - iconPlaceText - (int)textSize.Width) + (int)imgSize.Width + iconPlaceText;
                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)textSize.Height) / 2.0f;
                        intW = (int)textSize.Width;
                        intH = (int)textSize.Height;
                    }
                    else
                    {
                        intX += padding.Left + ((int)rect.Width - padding.Right - (int)imgSize.Width - iconPlaceText - (int)textSize.Width);
                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)textSize.Height) / 2.0f;
                        intW = (int)textSize.Width;
                        intH = (int)textSize.Height;
                    }
                    break;

                case skImageAlignment.BottomLeft:
                case skImageAlignment.Bottom:
                case skImageAlignment.BottomRight:
                    intX += padding.Left;
                    intY += padding.Top;
                    intW = (int)rect.Width - padding.Horizontal;
                    intH = (int)rect.Height - padding.Vertical - (int)imgSize.Height - iconPlaceText;
                    break;

            }

            return new CGRect(intX, intY, intW, intH);
        }

        public static CGPoint CalcImageOrigin(CGRect rect, CGSize imgSize, skImageAlignment align, Padding paddingSource, CGSize textSize, int iconPlaceText, bool upToDownWhenCenter = true, bool iconBeforeText = true)
        {
            if (imgSize.IsEmpty)
                iconPlaceText = 0;

            nfloat intX = rect.X;
            nfloat intY = rect.Y;

            Padding padding = ExtendControl.IsWindowAnchor ? new Padding(paddingSource.Left, paddingSource.Bottom, paddingSource.Right, paddingSource.Top) : paddingSource;

            switch (align)
            {
                case skImageAlignment.TopLeft:
                    intX += padding.Left;
                    intY += padding.Top;
                    break;

                case skImageAlignment.Top:
                    intX += (rect.Width - padding.Horizontal - imgSize.Width) / 2.0f;
                    intY += padding.Top;
                    break;

                case skImageAlignment.TopRight:
                    intX += ((int)rect.Width - (int)imgSize.Width - padding.Right);
                    intY += padding.Top;
                    break;

                case skImageAlignment.Left:
                    if (iconBeforeText)
                    {
                        intX += padding.Left;
                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height) / 2.0f;
                    }
                    else
                    {
                        intX += padding.Left + (int)textSize.Width + iconPlaceText;
                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height) / 2.0f;
                    }
                    break;

                case skImageAlignment.Center:
                    if (upToDownWhenCenter)
                    {
                        intX += padding.Left + ((int)rect.Width - padding.Horizontal - (int)imgSize.Width) / 2.0f;

                        if (iconBeforeText)
                            intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height - (int)textSize.Height - iconPlaceText) / 2.0f + (int)textSize.Height + iconPlaceText;
                        else
                            intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height - (int)textSize.Height - iconPlaceText) / 2.0f;
                    }
                    else
                    {
                        if (iconBeforeText)
                            intX += padding.Left + ((int)rect.Width - padding.Horizontal - (int)imgSize.Width - (int)textSize.Width - iconPlaceText) / 2.0f;
                        else
                            intX += padding.Left + (int)textSize.Width + iconPlaceText + ((int)rect.Width - padding.Horizontal - (int)imgSize.Width - (int)textSize.Width - iconPlaceText) / 2.0f;

                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height) / 2.0f;
                    }
                    break;

                case skImageAlignment.Right:
                    if (iconBeforeText)
                    {
                        intX += ((int)rect.Width - padding.Right - (int)textSize.Width - iconPlaceText - (int)imgSize.Width);
                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height) / 2.0f;
                    }
                    else
                    {
                        intX += ((int)rect.Width - padding.Right - (int)textSize.Width - iconPlaceText - (int)imgSize.Width) + (int)textSize.Width + iconPlaceText;
                        intY += padding.Top + ((int)rect.Height - padding.Vertical - (int)imgSize.Height) / 2.0f;
                    }
                    break;

                case skImageAlignment.BottomLeft:
                    intX += padding.Left;
                    intY += ((int)rect.Height - (int)imgSize.Height - padding.Bottom);
                    break;

                case skImageAlignment.Bottom:
                    intX += ((int)rect.Width - padding.Horizontal - (int)imgSize.Width) / 2.0f;
                    intY += ((int)rect.Height - (int)imgSize.Height - padding.Bottom);
                    break;

                case skImageAlignment.BottomRight:
                    intX += ((int)rect.Width - (int)imgSize.Width - padding.Right);
                    intY += ((int)rect.Height - (int)imgSize.Height - padding.Bottom);
                    break;

            }

            return new CGPoint(intX, intY);
        }

        public static void DrawPie(CGPoint centerOfCircle, nfloat radiusOfCircle, nfloat percentOf100, NSColor colorStroke)
        {
            DrawPie(centerOfCircle, radiusOfCircle, 90, 90 - (nfloat)Math.Ceiling(360 * percentOf100 / 100f), colorStroke, colorStroke, true);
        }

        public static void DrawPie(CGPoint centerOfCircle, nfloat radiusOfCircle, nfloat startAngle, nfloat endAngle, NSColor colorStroke, NSColor colorFill = null, bool clockWise = true)
        {
            NSGraphicsContext.CurrentContext.SaveGraphicsState();

            NSBezierPath path = null;
            path = new NSBezierPath();

            //path.MoveTo(centerOfCircle);
            //path.LineTo(new CGPoint(centerOfCircle.X, centerOfCircle.Y + radiusOfCircle));

            //圆心右侧为0度，圆心上面为90度，圆心右侧为180度，圆心下面为270度，一圈360度，clockWise = false;
            //圆心右侧为0度，圆心上面为-90度，圆心右侧为-180度，圆心下面为-270度，一圈-360度，clockWise = true;
            path.AppendPathWithArc(centerOfCircle, radiusOfCircle, startAngle, endAngle, clockWise);//第三个参数表示顺时针

            path.LineTo(centerOfCircle); //将圆心与绘制扇形结束点连接起来
            path.ClosePath();//通过关闭路径，将圆心与绘制扇形开始点连接起来
            path.SetClip();

            if (colorStroke != null)
            {
                colorStroke.Set();
                path.Stroke();
            }

            if (colorFill != null)
            {
                colorFill.Set();
                path.Fill();
            }

            path.Dispose();

            NSGraphicsContext.CurrentContext.RestoreGraphicsState();
        }

        public static void DrawCircle(CGRect rect,
            int widthBorder,
            NSColor colorStroke,
            NSColor colorFill = null)
        {
            DrawPie(new CGPoint(rect.Left + rect.Width / 2, rect.Top + rect.Height / 2), rect.Width / 2, 0, -360, colorStroke, colorFill, true);
        }

        /// <summary>
        /// 绘制带圆角的矩形
        /// </summary>
        /// <param name="rect"></param>
        /// <param name="borderType"></param>
        /// <param name="rectRadius"></param>
        /// <param name="widthBorder"></param>
        /// <param name="colorStroke"></param>
        /// <param name="colorFill">为null时，则不进行背景色填充</param>
        public static void FillRoundRect(CGRect rect,
            skBorderType borderType,
            skBorderRadius rectRadius,
            int widthBorder,
            NSColor colorStroke,
            NSColor colorFill = null,
            nfloat[] arrDashLine = null,
            skTrianglePosition trianglePosition = skTrianglePosition.None,
            int triangleWidth = 12,
            int triangleHeight = 5,
            int trianglePlaceCorner = 15)
        {
            try
            {
                if (rect.IsEmpty || colorStroke == null)
                    return;

                CGPoint pt1 = CGPoint.Empty;
                CGPoint pt2 = CGPoint.Empty;


                //绘制边框
                if (borderType == skBorderType.Round)
                {
                    NSGraphicsContext g = NSGraphicsContext.CurrentContext;
                    g.SaveGraphicsState();

                    NSBezierPath path = null;                   

                    if (rectRadius.IsEmpty && trianglePosition == skTrianglePosition.None)
                    {
                        path = NSBezierPath.FromRoundedRect(rect, 0, 0);                        
                    }
                    else
                    {
                        path = skGuiHelper.CreateRoundPath(rect, rectRadius, trianglePosition, triangleWidth, triangleHeight, trianglePlaceCorner, widthBorder);
                    }

                    //设置画布
                    path.SetClip();

                    //先填充再画边框，否则边框线会被覆盖
                    if (colorFill != null)
                    {
                        //添加填充颜色
                        colorFill.Set();

                        //填充路径
                        path.Fill();
                    }

                    //设置边框颜色 
                    colorStroke.Set();

                    //设置边框线宽度
                    path.LineWidth = widthBorder;

                    //设置是否画虚线
                    if (arrDashLine != null && arrDashLine.Length > 0)
                        path.SetLineDash(arrDashLine, 0);

                    //画边框
                    path.Stroke();


                    path.Dispose();

                    g.RestoreGraphicsState();
                }
                else
                {
                    skBorderType bottom = ExtendControl.IsWindowAnchor ? skBorderType.Top : skBorderType.Bottom;
                    if ((borderType & bottom) == bottom)
                    {
                        pt1 = new CGPoint(rect.X, rect.Height);
                        pt2 = new CGPoint(rect.Width, rect.Height);
                        //path.MoveTo(pt1);
                        //path.LineTo(pt2);

                        DrawLine(pt1, pt2, colorStroke, widthBorder);
                    }

                    if ((borderType & skBorderType.Right) == skBorderType.Right)
                    {
                        pt1 = new CGPoint(rect.Width, rect.Y);
                        pt2 = new CGPoint(rect.Width, rect.Height);
                        //path.MoveTo(pt1);
                        //path.LineTo(pt2);

                        DrawLine(pt1, pt2, colorStroke, widthBorder);
                    }

                    if ((borderType & skBorderType.Left) == skBorderType.Left)
                    {
                        pt1 = new CGPoint(rect.X, rect.Y);
                        pt2 = new CGPoint(rect.X, rect.Height);
                        //path.MoveTo(pt1);
                        //path.LineTo(pt2);

                        DrawLine(pt1, pt2, colorStroke, widthBorder);
                    }

                    skBorderType top = ExtendControl.IsWindowAnchor ? skBorderType.Bottom : skBorderType.Top;
                    if ((borderType & top) == top)
                    {
                        pt1 = new CGPoint(rect.X, rect.Y);
                        pt2 = new CGPoint(rect.Width, rect.Y);
                        //path.MoveTo(pt1);
                        //path.LineTo(pt2);

                        DrawLine(pt1, pt2, colorStroke, widthBorder);
                    }
                }               
            }
            catch (Exception ex)
            {
                Console.Write(ex);
            }
        }

        public static void FillTriangle(CGRect rect,
           NSColor colorStroke,
           NSColor colorFill = null,
           skTrianglePosition trianglePosition = skTrianglePosition.Right,
           nfloat[] arrDashLine = null,
           int widthBorder = 1,
           int triangleWidth = 0,
           int triangleHeight = 0)
        {
            try
            {
                if (rect.IsEmpty || colorStroke == null)
                    return;


                NSGraphicsContext g = NSGraphicsContext.CurrentContext;
                g.SaveGraphicsState();

                NSBezierPath path = CreateTrianglePath(rect, trianglePosition, triangleWidth, triangleHeight);

                //设置画布
                path.SetClip();

                //设置边框颜色 
                colorStroke.Set();

                //设置是否画虚线
                if (arrDashLine != null && arrDashLine.Length > 0)
                    path.SetLineDash(arrDashLine, 0);

                //画边框
                path.Stroke();

                if (colorFill != null)
                {
                    //添加填充颜色
                    colorFill.Set();

                    //填充路径
                    path.Fill();
                }

                path.Dispose();

                g.RestoreGraphicsState();
            }
            catch (Exception ex)
            {
                Console.Write(ex);
            }
        }

        ///// <summary>
        ///// 绘制带圆角的矩形
        ///// </summary>
        ///// <param name="rect"></param>
        ///// <param name="borderType"></param>
        ///// <param name="rectRadius"></param>
        ///// <param name="widthBorder"></param>
        ///// <param name="colorStroke"></param>
        ///// <param name="colorFill">为null时，则不进行背景色填充</param>
        //public static void FillRoundRect(CGRect rect,
        //    tbBorderType borderType,
        //    tbBorderRadius rectRadius,
        //    int widthBorder,
        //    NSColor colorStroke,
        //    NSColor colorFill = null,
        //    bool borderUseDashLine = false,
        //    tbTrianglePosition trianglePosition = tbTrianglePosition.None,
        //    int triangleWidth = 12,
        //    int triangleHeight = 5,
        //    int trianglePlaceCorner = 15)
        //{
        //    try
        //    {
        //        if (rect.IsEmpty || colorStroke == null)
        //            return;

        //        //画边线
        //        CGPoint pt1 = CGPoint.Empty;
        //        CGPoint pt2 = CGPoint.Empty;

        //        CGContext g = NSGraphicsContext.CurrentContext.CGContext;
        //        g.SaveState();

        //        g.SetLineWidth(widthBorder);

        //        if (borderType == tbBorderType.Round)
        //        {
        //            NSBezierPath path = null;

        //            if (!rectRadius.IsEmpty)
        //            {
        //                path = GuiHelper.CreateRoundPath(rect, rectRadius, trianglePosition, triangleWidth, triangleHeight, trianglePlaceCorner);
        //                //path = GuiHelper.CreateRoundPath(rect, rectRadius.TopLeft, rectRadius.TopRight, rectRadius.BottomRight, rectRadius.BottomLeft);
        //            }
        //            else
        //            {
        //                path = NSBezierPath.FromRoundedRect(rect, 0, 0);
        //            }

        //            path.SetClip();
        //            path.Stroke();
        //            path.Dispose();
        //        }
        //        else
        //        {
        //            if ((borderType & tbBorderType.Bottom) == tbBorderType.Bottom)
        //            {
        //                pt1 = new CGPoint(0f, rect.Height);
        //                pt2 = new CGPoint(rect.Width, rect.Height);
        //                g.AddLines(new CGPoint[] { pt1, pt2 });
        //                //g.MoveTo(pt1.X, pt1.Y);
        //                //g.AddLineToPoint(pt2.X, pt2.Y);
        //            }

        //            if ((borderType & tbBorderType.Right) == tbBorderType.Right)
        //            {
        //                pt1 = new CGPoint(rect.Width, 0f);
        //                pt2 = new CGPoint(rect.Width, rect.Height);
        //                g.AddLines(new CGPoint[] { pt1, pt2 });
        //                //g.MoveTo(pt1.X, pt1.Y);
        //                //g.AddLineToPoint(pt2.X, pt2.Y);
        //            }

        //            if ((borderType & tbBorderType.Left) == tbBorderType.Left)
        //            {
        //                pt1 = new CGPoint(0f, 0f);
        //                pt2 = new CGPoint(0f, rect.Height);
        //                g.AddLines(new CGPoint[] { pt1, pt2 });
        //                //g.MoveTo(pt1.X, pt1.Y);
        //                //g.AddLineToPoint(pt2.X, pt2.Y);
        //            }

        //            if ((borderType & tbBorderType.Top) == tbBorderType.Top)
        //            {
        //                pt1 = new CGPoint(0f, 0f);
        //                pt2 = new CGPoint(rect.Width, 0f);
        //                g.AddLines(new CGPoint[] { pt1, pt2 });
        //                //g.MoveTo(pt1.X, pt1.Y);
        //                //g.AddLineToPoint(pt2.X, pt2.Y);
        //            }
        //        }


        //        g.SetStrokeColor(colorStroke.CGColor);

        //        if (borderUseDashLine)
        //            g.SetLineDash(0, new nfloat[] { 4, 3 });


        //        if (colorFill != null)
        //        {
        //            g.SetFillColor(colorFill.CGColor);
        //            g.DrawPath(CGPathDrawingMode.FillStroke);
        //        }
        //        else
        //        {
        //            g.DrawPath(CGPathDrawingMode.Stroke);
        //            //g.StrokePath();
        //        }

        //        //GuiHelper.DrawText(DateTime.Now.ToString(), NSColor.White, MyFont.DefaultFont, frame, UITextAlignment.Center);

        //        g.RestoreState();
        //    }
        //    catch (Exception ex)
        //    {
        //        Console.Write(ex);
        //    }
        //}

        public static void FillRect(CGRect rect, NSColor color)
        {
            if (color == null)
                return;

            color.Set();
            NSGraphics.RectFill(rect);
        }

        public static void StrokeRect(CGRect rect, NSColor color, int lineWidth = 1)
        {
            color.Set();
            NSBezierPath.DefaultLineWidth = lineWidth;
            NSBezierPath.StrokeLine(new CGPoint(rect.Left, rect.Top), new CGPoint(rect.Right, rect.Top));
            NSBezierPath.StrokeLine(new CGPoint(rect.Right, rect.Top), new CGPoint(rect.Right, rect.Bottom));
            NSBezierPath.StrokeLine(new CGPoint(rect.Right, rect.Bottom), new CGPoint(rect.Left, rect.Bottom));
            NSBezierPath.StrokeLine(new CGPoint(rect.Left, rect.Bottom), new CGPoint(rect.Left, rect.Top));
        }

        public static void DrawUnderLine(NSFont font, CGPoint point1, CGPoint point2, NSColor color, int penWidth = 1)
        {
            //if (!font.Underline)
            //    return;

            int diffLeft = 1;
            int diffRight = 2;
            //if (font.IsHarmonyFont())
            //{
            //    diffLeft = 2;
            //    diffRight = 4;
            //}

            DrawLine(new CGPoint(point1.X + diffLeft, point1.Y - penWidth), new CGPoint(point2.X - diffRight, point2.Y - penWidth), color, penWidth);
        }

        public static void DrawLine(CGPoint pos1, CGPoint pos2, NSColor color, int lineWidth = 1)
        {
            color.Set();
            NSBezierPath.DefaultLineWidth = lineWidth;
            NSBezierPath.StrokeLine(pos1, pos2);
        }

        //public static void DrawLinearGradient(NSBezierPath path, NSColor startColor, NSColor endColor, CGPoint startPoint, CGPoint endPoint)
        //{
        //    DrawLinearGradient(path, new NSColor[] { startColor, endColor }, startPoint, endPoint);
        //}

        //public static void DrawLinearGradient(NSBezierPath path, NSColor[] arrColor, CGPoint startPoint, CGPoint endPoint)
        //{
        //    List<CGColor> list = new List<CGColor>();

        //    foreach (NSColor c in arrColor)
        //        list.Add(c.ToCGColor());

        //    DrawLinearGradient(path, list.ToArray(), startPoint, endPoint);
        //}

        public static void DrawLinearGradient(NSBezierPath path, NSColor startColor, NSColor endColor, CGPoint startPoint, CGPoint endPoint)
        {
            DrawLinearGradient(path, new NSColor[] { startColor, endColor }, startPoint, endPoint);
        }

        public static void DrawLinearGradient(NSBezierPath path, NSColor[] arrColor, CGPoint startPoint, CGPoint endPoint)
        {
            if (CanDrawWithCGContent)
            {
                CGContext g = NSGraphicsContext.CurrentContext.CGContext;
                g.SaveState();  //保存当前绘图状态

                path.SetClip(); //将当前绘图区域设置为path

                List<CGColor> list = new List<CGColor>();

                foreach (NSColor color in arrColor)
                    list.Add(color.ToCGColor());

                CGColorSpace colorSpace = CGColorSpace.CreateDeviceRGB();
                CGGradient gradient = new CGGradient(colorSpace, list.ToArray());

                g.DrawLinearGradient(gradient, startPoint, endPoint, CGGradientDrawingOptions.None);

                gradient.Dispose();
                colorSpace.Dispose();

                g.RestoreState();   //恢复到上一次保存的绘图状态
                //g.SetShadow(CGSize.Empty, 1, null);
            }
            else
            {
                NSGraphicsContext.CurrentContext.SaveGraphicsState();

                path.SetClip(); //将当前绘图区域设置为path

                if (arrColor.Length > 0)
                    arrColor[0].Set();

                path.Stroke();

                if (arrColor.Length > 1)
                    arrColor[1].Set();

                path.Fill();

                NSGraphicsContext.CurrentContext.RestoreGraphicsState();
            }
        }

        public static void DrawImage(NSImage srcImage,
                     nfloat posX, nfloat posY)
        {
            if (srcImage == null)
                return;

            DrawImage(srcImage, new CGRect(posY, posY, srcImage.Size.Width, srcImage.Size.Height), new CGRect(0, 0, srcImage.Size.Width, srcImage.Size.Height));
        }

        public static void DrawImage(NSImage srcImage,
                                     CGRect dstRect)
        {
            DrawImage(srcImage, dstRect, CGRect.Empty);
        }

        public static void DrawImage(NSImage srcImage,
                                     CGRect dstRect,
                                     CGRect srcRect)
        {
            if (srcImage == null)
                return;

            if(srcRect.Size.IsEmpty)
                srcRect = new CGRect(srcRect.Left, srcRect.Top, srcImage.Size.Width, srcImage.Size.Height);

            srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
        }

        public static void DrawImageInCell(CGRect rectDraw,
                                     skSplit splitSrc,
                                     NSImage srcImage,
                                     bool blnEnabled = true,
                                     skMouseState currentMouseState = skMouseState.MouseLeave,
                                     skImageState totalImageState = skImageState.OneState,
                                     skImageLayout imgLayout = skImageLayout.Horizontal,
                                     bool blnAutoResizeWidth = false)
        {
            if (srcImage == null)
                return;

            int intIndex = (int)currentMouseState;
            int intTotal = (int)totalImageState;

            if (!blnEnabled)
                intIndex = (int)totalImageState - 1;

            if (intIndex >= intTotal)
                intIndex = intTotal - 1;

            int indexWidth = 0;
            int indexHeight = 0;
            int countWidth = 1;
            int countHeight = 1;

            if (imgLayout == skImageLayout.Horizontal)
            {
                indexWidth = intIndex;
                countWidth = intTotal;
            }
            else
            {
                indexHeight = intIndex;
                countHeight = intTotal;
            }

            int imgWidth = (int)srcImage.Size.Width / countWidth;
            int imgHeight = (int)srcImage.Size.Height / countHeight;
            int imgLeft = imgWidth * indexWidth;
            int imgTop = imgHeight * indexHeight;

            if (blnAutoResizeWidth && imgWidth > rectDraw.Width)
                imgWidth = (int)rectDraw.Width;

            //if (imgHeight > rectDraw.Height)
            //    imgHeight = (int)rectDraw.Height;

            int x = (int)rectDraw.X;
            int y = (int)rectDraw.Y;

            CGRect dstRect = CGRect.Empty;
            CGRect srcRect = CGRect.Empty;
            CGSize imgSize = new CGSize(imgWidth, imgHeight);

            skSplit split = splitSrc;

            if (blnAutoResizeWidth && split.Horizontal >= imgWidth)
            {
                split.Left = 0;
                split.Right = 0;
            }

            //if (split.Vertical >= imgHeight)
            //{
            //    split.Top = 0;
            //    split.Bottom = 0;
            //}

            //LeftTop
            if (split.Left > 0 && split.Top > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop, split.Left, split.Top);
                dstRect = new CGRect(x, y + rectDraw.Height - split.Bottom, split.Left, split.Bottom);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }

            //Left
            if (split.Left > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop + split.Top, split.Left, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x, y + split.Top, split.Left, rectDraw.Height - split.Vertical);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }

            //LeftBottom
            if (split.Left > 0 && split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop + imgSize.Height - split.Bottom, split.Left, split.Bottom);
                dstRect = new CGRect(x, y, split.Left, split.Top);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }

            //Top
            if (split.Top > 0)
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop, imgSize.Width - split.Horizontal, split.Top);
                dstRect = new CGRect(x + split.Left, y + rectDraw.Height - split.Bottom, rectDraw.Width - split.Horizontal, split.Bottom);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }

            //Center
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop + split.Top, imgSize.Width - split.Horizontal, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x + split.Left, y + split.Top, rectDraw.Width - split.Horizontal, rectDraw.Height - split.Vertical);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }

            //Bottom
            if (split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop + imgSize.Height - split.Bottom, imgSize.Width - split.Horizontal, split.Bottom);
                dstRect = new CGRect(x + split.Left, y, rectDraw.Width - split.Horizontal, split.Top);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }

            //RightTop
            if (split.Right > 0 && split.Top > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop, split.Right, split.Top);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y + rectDraw.Height - split.Bottom, split.Right, split.Bottom);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }

            //Right
            if (split.Right > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop + split.Top, split.Right, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y + split.Top, split.Right, rectDraw.Height - split.Vertical);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }

            //RightBottom
            if (split.Right > 0 && split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop + imgSize.Height - split.Bottom, split.Right, split.Bottom);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y, split.Right, split.Top);
                srcImage.Draw(dstRect, srcRect, NSCompositingOperation.SourceOver, 1, true, null);
            }
        }

        public static CGRect GetBackgroundImageRect(CGRect rect, NSImage srcImage, skImageAlignment imageAlign, bool isStretchImage = false)
        {
            CGSize srcSize = srcImage.Size;
            CGSize dstSize = rect.Size;

            nfloat left = 0;
            nfloat top = 0;
            nfloat width = 0;
            nfloat height = 0;

            if (srcSize.Width > dstSize.Width || srcSize.Height > dstSize.Height)
            {
                //生成按原始图片大小进行缩放的缩略图
                if (srcSize.Width / (double)srcSize.Height > dstSize.Width / (double)dstSize.Height)
                {
                    if (isStretchImage)
                    {
                        height = dstSize.Height;
                        width = height * srcSize.Width / srcSize.Height;
                    }
                    else
                    {
                        width = dstSize.Width;
                        height = srcSize.Height * width / srcSize.Width;
                    }
                }
                else
                {
                    if (isStretchImage)
                    {
                        width = dstSize.Width;
                        height = width * srcSize.Height / srcSize.Width;
                    }
                    else
                    {
                        height = dstSize.Height;
                        width = srcSize.Width * height / srcSize.Height;
                    }
                }
            }
            else
            {
                width = srcSize.Width;
                height = srcSize.Height;
            }

            switch (imageAlign)
            {
                case skImageAlignment.None:
                    left = rect.Left;
                    top = rect.Top;
                    width = rect.Width;
                    height = rect.Height;
                    break;

                case skImageAlignment.Center:
                    left = rect.Left + (rect.Width - width) / 2;
                    top = rect.Top + (rect.Height - height) / 2;
                    break;

                case skImageAlignment.Left:
                    left = rect.Left;
                    top = rect.Top + (rect.Height - height) / 2;
                    break;

                case skImageAlignment.TopLeft:
                    left = rect.Left;
                    top = rect.Top;
                    break;

                case skImageAlignment.BottomLeft:
                    left = rect.Left;
                    top = rect.Bottom - height;
                    break;

                case skImageAlignment.Right:
                    left = rect.Left + (rect.Right - width);
                    top = rect.Top + (rect.Height - height) / 2;
                    break;

                case skImageAlignment.TopRight:
                    left = rect.Left + (rect.Right - width);
                    top = rect.Top;
                    break;

                case skImageAlignment.BottomRight:
                    left = rect.Left + (rect.Right - width);
                    top = rect.Bottom - height;
                    break;

                case skImageAlignment.Top:
                    left = rect.Left + (rect.Width - width) / 2;
                    top = rect.Top;
                    break;

                case skImageAlignment.Bottom:
                    left = rect.Left + (rect.Width - width) / 2;
                    top = rect.Top + (rect.Bottom - height);
                    break;
            }

            return new CGRect(left, top, width, height);
        }

        public static void DrawBackgroudImage(CGRect rect, NSImage srcImage, skImageAlignment imageAlign, bool isStretchImage = false)
        {
            if (srcImage == null || rect.Width == 0 || rect.Height == 0)
                return;

            CGRect rectDest = GetBackgroundImageRect(rect, srcImage, imageAlign, isStretchImage);

            DrawImage(rectDest, skSplit.Empty, srcImage);
        }

        public static void DrawImage(CGRect rectDraw,
                                    skSplit splitSrc,
                                    NSImage srcImage,
                                    bool blnEnabled = true,
                                    skMouseState currentMouseState = skMouseState.MouseLeave,
                                    skImageState totalImageState = skImageState.OneState,
                                    skImageLayout imgLayout = skImageLayout.Horizontal,
                                    bool blnAutoResizeWidth = false)
        {
            if (srcImage == null)
                return;

            int intIndex = (int)currentMouseState;
            int intTotal = (int)totalImageState;

            if (!blnEnabled)
                intIndex = (int)totalImageState - 1;

            if (intIndex >= intTotal)
                intIndex = intTotal - 1;

            int indexWidth = 0;
            int indexHeight = 0;
            int countWidth = 1;
            int countHeight = 1;

            if (imgLayout == skImageLayout.Horizontal)
            {
                indexWidth = intIndex;
                countWidth = intTotal;
            }
            else
            {
                indexHeight = intIndex;
                countHeight = intTotal;
            }

            int imgWidth = (int)srcImage.Size.Width / countWidth;
            int imgHeight = (int)srcImage.Size.Height / countHeight;
            int imgLeft = imgWidth * indexWidth;
            int imgTop = imgHeight * indexHeight;

            if (blnAutoResizeWidth && imgWidth > rectDraw.Width)
                imgWidth = (int)rectDraw.Width;

            //if (imgHeight > rectDraw.Height)
            //    imgHeight = (int)rectDraw.Height;

            int x = (int)rectDraw.X;
            int y = (int)rectDraw.Y;

            CGRect dstRect = CGRect.Empty;
            CGRect srcRect = CGRect.Empty;
            CGSize imgSize = new CGSize(imgWidth, imgHeight);

            skSplit split = splitSrc;

            if (blnAutoResizeWidth && split.Horizontal >= imgWidth)
            {
                split.Left = 0;
                split.Right = 0;
            }

            //if (split.Vertical >= imgHeight)
            //{
            //    split.Top = 0;
            //    split.Bottom = 0;
            //}

            //LeftTop
            if (split.Left > 0 && split.Top > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop, split.Left, split.Top);
                dstRect = new CGRect(x, y, split.Left, split.Top);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Left
            if (split.Left > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop + split.Top, split.Left, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x, y + split.Top, split.Left, rectDraw.Height - split.Vertical);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //LeftBottom
            if (split.Left > 0 && split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop + imgSize.Height - split.Bottom, split.Left, split.Bottom);
                dstRect = new CGRect(x, y + rectDraw.Height - split.Bottom, split.Left, split.Bottom);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Top
            if (split.Top > 0)
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop, imgSize.Width - split.Horizontal, split.Top);
                dstRect = new CGRect(x + split.Left, y, rectDraw.Width - split.Horizontal, split.Top);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Center
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop + split.Top, imgSize.Width - split.Horizontal, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x + split.Left, y + split.Top, rectDraw.Width - split.Horizontal, rectDraw.Height - split.Vertical);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Bottom
            if (split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop + imgSize.Height - split.Bottom, imgSize.Width - split.Horizontal, split.Bottom);
                dstRect = new CGRect(x + split.Left, y + rectDraw.Height - split.Bottom, rectDraw.Width - split.Horizontal, split.Bottom);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //RightTop
            if (split.Right > 0 && split.Top > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop, split.Right, split.Top);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y, split.Right, split.Top);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Right
            if (split.Right > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop + split.Top, split.Right, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y + split.Top, split.Right, rectDraw.Height - split.Vertical);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //RightBottom
            if (split.Right > 0 && split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop + imgSize.Height - split.Bottom, split.Right, split.Bottom);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y + rectDraw.Height - split.Bottom, split.Right, split.Bottom);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }
        }

        public static void DrawImage(CGRect rectDraw,
                            skSplit splitSrc,
                            NSImageRep srcImage,
                            bool blnEnabled = true,
                            skMouseState currentMouseState = skMouseState.MouseLeave,
                            skImageState totalImageState = skImageState.OneState,
                            skImageLayout imgLayout = skImageLayout.Horizontal,
                            bool blnAutoResizeWidth = false)
        {
            if (srcImage == null)
                return;

            int intIndex = (int)currentMouseState;
            int intTotal = (int)totalImageState;

            if (!blnEnabled)
                intIndex = (int)totalImageState - 1;

            if (intIndex >= intTotal)
                intIndex = intTotal - 1;

            int indexWidth = 0;
            int indexHeight = 0;
            int countWidth = 1;
            int countHeight = 1;

            if (imgLayout == skImageLayout.Horizontal)
            {
                indexWidth = intIndex;
                countWidth = intTotal;
            }
            else
            {
                indexHeight = intIndex;
                countHeight = intTotal;
            }

            int imgWidth = (int)srcImage.Size.Width / countWidth;
            int imgHeight = (int)srcImage.Size.Height / countHeight;
            int imgLeft = imgWidth * indexWidth;
            int imgTop = imgHeight * indexHeight;

            if (blnAutoResizeWidth && imgWidth > rectDraw.Width)
                imgWidth = (int)rectDraw.Width;

            //if (imgHeight > rectDraw.Height)
            //    imgHeight = (int)rectDraw.Height;

            int x = (int)rectDraw.X;
            int y = (int)rectDraw.Y;

            CGRect dstRect = CGRect.Empty;
            CGRect srcRect = CGRect.Empty;
            CGSize imgSize = new CGSize(imgWidth, imgHeight);

            skSplit split = splitSrc;

            if (blnAutoResizeWidth && split.Horizontal >= imgWidth)
            {
                split.Left = 0;
                split.Right = 0;
            }

            //if (split.Vertical >= imgHeight)
            //{
            //    split.Top = 0;
            //    split.Bottom = 0;
            //}

            //LeftTop
            if (split.Left > 0 && split.Top > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop, split.Left, split.Top);
                dstRect = new CGRect(x, y, split.Left, split.Top);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Left
            if (split.Left > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop + split.Top, split.Left, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x, y + split.Top, split.Left, rectDraw.Height - split.Vertical);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //LeftBottom
            if (split.Left > 0 && split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft, imgTop + imgSize.Height - split.Bottom, split.Left, split.Bottom);
                dstRect = new CGRect(x, y + rectDraw.Height - split.Bottom, split.Left, split.Bottom);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Top
            if (split.Top > 0)
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop, imgSize.Width - split.Horizontal, split.Top);
                dstRect = new CGRect(x + split.Left, y, rectDraw.Width - split.Horizontal, split.Top);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Center
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop + split.Top, imgSize.Width - split.Horizontal, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x + split.Left, y + split.Top, rectDraw.Width - split.Horizontal, rectDraw.Height - split.Vertical);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Bottom
            if (split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft + split.Left, imgTop + imgSize.Height - split.Bottom, imgSize.Width - split.Horizontal, split.Bottom);
                dstRect = new CGRect(x + split.Left, y + rectDraw.Height - split.Bottom, rectDraw.Width - split.Horizontal, split.Bottom);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //RightTop
            if (split.Right > 0 && split.Top > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop, split.Right, split.Top);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y, split.Right, split.Top);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //Right
            if (split.Right > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop + split.Top, split.Right, imgSize.Height - split.Vertical);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y + split.Top, split.Right, rectDraw.Height - split.Vertical);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }

            //RightBottom
            if (split.Right > 0 && split.Bottom > 0)
            {
                srcRect = new CGRect(imgLeft + imgSize.Width - split.Right, imgTop + imgSize.Height - split.Bottom, split.Right, split.Bottom);
                dstRect = new CGRect(x + rectDraw.Width - split.Right, y + rectDraw.Height - split.Bottom, split.Right, split.Bottom);
                srcImage.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
            }
        }

        public static void TileImage(NSImage img, CGRect rectDraw)
        {
            if (img == null || rectDraw.Width == 0 || rectDraw.Height == 0)
                return;

            CGSize imgSize = img.Size;
            int countW = (int)Math.Ceiling((int)rectDraw.Width / (float)imgSize.Width);
            int countH = (int)Math.Ceiling((int)rectDraw.Height / (float)imgSize.Height);
            int intW = (int)((imgSize.Width > rectDraw.Width) ? rectDraw.Width : imgSize.Width);
            int intH = (int)((imgSize.Height > rectDraw.Height) ? rectDraw.Height : imgSize.Height);

            for (int intJ = 0; intJ < countH; intJ++)
            {
                int intY = (int)rectDraw.Top + intJ * intH;
                int tmpH = (intJ != countH - 1) ? intH : (int)rectDraw.Bottom - intY;

                for (int intI = 0; intI < countW; intI++)
                {
                    int intX = (int)rectDraw.Left + intI * intW;
                    int tmpW = (intI != countW - 1) ? intW : (int)rectDraw.Right - intX;

                    CGRect dstRect = new CGRect(intX, intY, tmpW, tmpH);
                    CGRect srcRect = new CGRect(0, 0, tmpW, tmpH);

                    img.DrawInRect(dstRect, srcRect, NSCompositingOperation.SourceOver, 1);
                }
            }
        }

        public static NSMutableDictionary CreateTextAttribute(NSFont font,
                               NSColor color,
                               NSTextAlignment align = NSTextAlignment.Left,
                               NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                               int lineSpacing = 5,
                               bool isFontBold = false,
                               bool isFontUnderLine = false,
                               NSMutableParagraphStyle dictStyle = null)
        {
            if (dictStyle == null)
                dictStyle = new NSMutableParagraphStyle();

            dictStyle.LineSpacing = lineSpacing;
            dictStyle.Alignment = align;
            dictStyle.LineBreakMode = mode;

            //NSParagraphStyleAttributeName 段落的风格（设置首行，行间距，对齐方式什么的）看自己需要什么属性，写什么    
            //NSMutableParagraphStyle* paragraphStyle = [[NSMutableParagraphStyle alloc] init];
            //paragraphStyle.lineSpacing = 10;// 字体的行间距    
            //paragraphStyle.firstLineHeadIndent = 20.0f;//首行缩进    
            //paragraphStyle.alignment = NSTextAlignmentJustified;//（两端对齐的）文本对齐方式：（左，中，右，两端对齐，自然）    
            //paragraphStyle.lineBreakMode = NSLineBreakByTruncatingTail;//结尾部分的内容以……方式省略 ( "...wxyz" ,"abcd..." ,"ab...yz")    
            //paragraphStyle.headIndent = 20;//整体缩进(首行除外)    
            //paragraphStyle.tailIndent = 20;//    
            //paragraphStyle.minimumLineHeight = 10;//最低行高    
            //paragraphStyle.maximumLineHeight = 20;//最大行高    
            //paragraphStyle.paragraphSpacing = 15;//段与段之间的间距    
            //paragraphStyle.paragraphSpacingBefore = 22.0f;//段首行空白空间/* Distance between the bottom of the previous paragraph (or the end of its paragraphSpacing, if any) and the top of this paragraph. */    
            //paragraphStyle.baseWritingDirection = NSWritingDirectionLeftToRight;//从左到右的书写方向（一共➡️三种）    
            //paragraphStyle.lineHeightMultiple = 15;/* Natural line height is multiplied by this factor (if positive) before being constrained by minimum and maximum line height. */
            //paragraphStyle.hyphenationFactor = 1;//连字属性 在iOS，唯一支持的值分别为0和1 

            // NSFontAttributeName                设置字体属性，默认值：字体：Helvetica(Neue) 字号：12  
            // NSForegroundColorAttributeNam      设置字体颜色，取值为 NSColor对象，默认值为黑色  
            // NSBackgroundColorAttributeName     设置字体所在区域背景颜色，取值为 NSColor对象，默认值为nil, 透明色  
            // NSLigatureAttributeName            设置连体属性，取值为NSNumber 对象(整数)，0 表示没有连体字符，1 表示使用默认的连体字符  
            // NSKernAttributeName                设定字符间距，取值为 NSNumber 对象（整数），正值间距加宽，负值间距变窄  
            // NSStrikethroughStyleAttributeName  设置删除线，取值为 NSNumber 对象（整数）  
            // NSStrikethroughColorAttributeName  设置删除线颜色，取值为 NSColor 对象，默认值为黑色  
            // NSUnderlineStyleAttributeName      设置下划线，取值为 NSNumber 对象（整数），枚举常量 NSUnderlineStyle中的值，与删除线类似  
            // NSUnderlineColorAttributeName      设置下划线颜色，取值为 NSColor 对象，默认值为黑色  
            // NSStrokeWidthAttributeName         设置笔画宽度，取值为 NSNumber 对象（整数），负值填充效果，正值中空效果  
            // NSStrokeColorAttributeName         填充部分颜色，不是字体颜色，取值为 NSColor 对象  
            // NSShadowAttributeName              设置阴影属性，取值为 NSShadow 对象  
            // NSTextEffectAttributeName          设置文本特殊效果，取值为 NSString 对象，目前只有图版印刷效果可用：  
            // NSBaselineOffsetAttributeName      设置基线偏移值，取值为 NSNumber （float）,正值上偏，负值下偏  
            // NSObliquenessAttributeName         设置字形倾斜度，取值为 NSNumber （float）,正值右倾，负值左倾  
            // NSExpansionAttributeName           设置文本横向拉伸属性，取值为 NSNumber （float）,正值横向拉伸文本，负值横向压缩文本  
            // NSWritingDirectionAttributeName    设置文字书写方向，从左向右书写或者从右向左书写  
            // NSVerticalGlyphFormAttributeName   设置文字排版方向，取值为 NSNumber 对象(整数)，0 表示横排文本，1 表示竖排文本  
            // NSLinkAttributeName                设置链接属性，点击后调用浏览器打开指定URL地址  
            // NSAttachmentAttributeName          设置文本附件,取值为NSTextAttachment对象,常用于文字图片混排  
            // NSParagraphStyleAttributeName      设置文本段落排版格式，取值为 NSParagraphStyle 对象  

            if (font == null)
                font = MyFont.DefaultFont;

            NSMutableDictionary dict = new NSMutableDictionary();
            dict.SetValueForKey(font, NSStringAttributeKey.Font);
            dict.SetValueForKey(color, NSStringAttributeKey.ForegroundColor);
            dict.SetValueForKey(dictStyle, NSStringAttributeKey.ParagraphStyle);

            if (isFontBold)
                dict.SetValueForKey(new NSNumber(1), NSStringAttributeKey.StrokeWidth);

            if (isFontUnderLine)
            {
                //dict.SetValueForKey(color, NSStringAttributeKey.UnderlineColor);
                dict.SetValueForKey(new NSNumber(1), NSStringAttributeKey.UnderlineStyle);
            }


            return dict;
        }

        public static void DrawText(string strText,
                                    NSColor textColor,
                                    NSFont textFont,
                                    CGRect rectText,
                                    NSTextAlignment align = NSTextAlignment.Center,
                                    NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                                    int lineSpacing = 5,
                                   bool isFontBold = false,
                                   bool isFontUnderLine = false,
                                   NSMutableParagraphStyle dictStyle = null)
        {
            if (string.IsNullOrEmpty(strText))
                return;

            if (textColor == null)
                textColor = NSColor.Black;

            if (textFont == null)
                textFont = MyFont.DefaultFont;

            NSMutableDictionary nsDict = CreateTextAttribute(textFont, textColor, align, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle);
            NSString nsText = new NSString(strText);

            nsText.DrawInRect(rectText.ToInt(), nsDict);
            nsDict.Dispose();
            nsText.Dispose();
        }

        public static void DrawString(string strText, NSColor textColor, NSFont textFont, CGRect textRect, NSTextAlignment textAlignment = NSTextAlignment.Left)
        {
            DrawString(strText, textColor, textFont, textRect, Padding.Empty, textAlignment);
        }

        public static CGRect DrawString(string strText,
                                        NSColor textColor,
                                        NSFont textFont,
                                        CGRect textRect,
                                        Padding padding,
                                        NSTextAlignment align = NSTextAlignment.Center,
                                        NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                                        int lineSpacing = 5,
                                        bool isFontBold = false,
                                       bool isFontUnderLine = false,
                                        NSMutableParagraphStyle dictStyle = null)
        {
            skImageAlignment imgAlign = skImageAlignment.Left;

            if (align == NSTextAlignment.Center)
                imgAlign = skImageAlignment.Center;
            else if (align == NSTextAlignment.Right)
                imgAlign = skImageAlignment.Right;


            return DrawString(strText, textColor, textFont, textRect, padding, imgAlign, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle);
        }



        public static CGRect DrawString(string strText,
                                        NSColor textColor,
                                        NSFont textFont,
                                        CGRect textRect,
                                        Padding padding,
                                        skImageAlignment align = skImageAlignment.Center,
                                        NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                                        int lineSpacing = 5,
                                        bool isFontBold = false,
                                        bool isFontUnderLine = false,
                                        NSMutableParagraphStyle dictStyle = null,
                                        bool textRectIsOK = false)
        {
            if (string.IsNullOrEmpty(strText))
                return textRect;

            if (textColor == null)
                textColor = NSColor.Black;

            if (textFont == null)
                textFont = MyFont.DefaultFont;

            NSTextAlignment textAlign = NSTextAlignment.Center;
            switch (align)
            {
                case skImageAlignment.Left:
                case skImageAlignment.TopLeft:
                case skImageAlignment.BottomLeft:
                    textAlign = NSTextAlignment.Left;
                    break;

                case skImageAlignment.Right:
                case skImageAlignment.TopRight:
                case skImageAlignment.BottomRight:
                    textAlign = NSTextAlignment.Right;
                    break;
            }

            NSMutableDictionary nsDict = CreateTextAttribute(textFont, textColor, textAlign, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle);
            NSString nsText = new NSString(strText);

            CGRect rectText = textRect;
            if (!textRectIsOK)
            {
                CGSize sizeText = MeasureString(strText, textFont);
                CGSize newSize = new CGSize(Math.Ceiling(sizeText.Width), Math.Ceiling(sizeText.Height));

                if (newSize.Width > textRect.Width)
                    newSize.Width = textRect.Width;

                if (newSize.Height > textRect.Height)
                    newSize.Height = textRect.Height;

                switch (align)
                {
                    case skImageAlignment.Left:
                        rectText = new CGRect(textRect.Left + padding.Left, textRect.Top + padding.Top + (int)(textRect.Height - padding.Vertical - newSize.Height) / 2, newSize.Width, newSize.Height);
                        break;
                    case skImageAlignment.TopLeft:
                        rectText = new CGRect(textRect.Left + padding.Left, textRect.Top + padding.Top, newSize.Width, newSize.Height);
                        break;
                    case skImageAlignment.BottomLeft:
                        rectText = new CGRect(textRect.Left + padding.Left, textRect.Bottom - padding.Bottom - newSize.Height, newSize.Width, newSize.Height);
                        break;
                    case skImageAlignment.Right:
                        rectText = new CGRect(textRect.Right - padding.Right - newSize.Width, textRect.Top + padding.Top + (int)(textRect.Height - padding.Vertical - newSize.Height) / 2, newSize.Width, newSize.Height);
                        break;
                    case skImageAlignment.TopRight:
                        rectText = new CGRect(textRect.Right - padding.Right - newSize.Width, textRect.Top + padding.Top, newSize.Width, newSize.Height);
                        break;
                    case skImageAlignment.BottomRight:
                        rectText = new CGRect(textRect.Right - padding.Right - newSize.Width, textRect.Bottom - padding.Bottom - newSize.Height, newSize.Width, newSize.Height);
                        break;
                    case skImageAlignment.Center:
                        rectText = new CGRect(textRect.Left + padding.Left + (int)(textRect.Width - padding.Horizontal - newSize.Width) / 2, textRect.Top + padding.Top + (int)(textRect.Height - padding.Vertical - newSize.Height) / 2, newSize.Width, newSize.Height);
                        break;
                    case skImageAlignment.Top:
                        rectText = new CGRect(textRect.Left + padding.Left + (int)(textRect.Width - padding.Horizontal - newSize.Width) / 2, textRect.Top + padding.Top, newSize.Width, newSize.Height);
                        break;
                    case skImageAlignment.Bottom:
                        rectText = new CGRect(textRect.Left + padding.Left + (int)(textRect.Width - padding.Horizontal - newSize.Width) / 2, textRect.Bottom - padding.Bottom - newSize.Height, newSize.Width, newSize.Height);
                        break;
                }
            }

            nsText.DrawInRect(rectText.ToInt(), nsDict);

            nsDict.Dispose();
            nsText.Dispose();

            return rectText;
        }

        public static CGRect DrawStringByRect(string strText,
                               NSColor textColor,
                               NSFont textFont,
                               CGRect textRect,
                               Padding padding,
                               skImageAlignment align = skImageAlignment.Center,
                               NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                               int lineSpacing = 5,
                               bool isFontBold = false,
                              bool isFontUnderLine = false,
                              NSMutableParagraphStyle dictStyle = null,
                              bool textRectIsOK = true)
        {
            return DrawString(strText, textColor, textFont, textRect, padding, align, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle, textRectIsOK);
        }

        public static CGSize MeasureText(string strText,
                              NSFont textFont,
                              int maxTextWidth = -1,
                              NSTextAlignment align = NSTextAlignment.Left,
                              NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                              int lineSpacing = 5,
                              bool isFontBold = false,
                              bool isFontUnderLine = false,
                              NSMutableParagraphStyle dictStyle = null,
                              NSStringDrawingOptions options = NSStringDrawingOptions.UsesLineFragmentOrigin)
        {
            return MeasureString(strText, textFont, maxTextWidth, align, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle, options);
        }

        public static CGSize MeasureString(string strText,
                               NSFont textFont,
                               int maxTextWidth = -1,
                               NSTextAlignment align = NSTextAlignment.Left,
                               NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                               int lineSpacing = 5,
                               bool isFontBold = false,
                               bool isFontUnderLine = false,
                               NSMutableParagraphStyle dictStyle = null,
                               NSStringDrawingOptions options = NSStringDrawingOptions.UsesLineFragmentOrigin,
                               int maxTextHeight = -1)
        {                        
            CGSize sizeText = CGSize.Empty;

            if (strText == null)
                strText = string.Empty;

            if (textFont == null)
                textFont = MyFont.DefaultFont;

            if (lineSpacing < 0)
                lineSpacing = 0;

            NSMutableDictionary dictText = skGuiHelper.CreateTextAttribute(textFont, NSColor.Black, align, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle);

            if (maxTextWidth > 0 && Common.OSVersion >= new Version("10.11"))
            {
                //NSStringDrawingOptions枚举成员介绍
                //NSStringDrawingUsesLineFragmentOrigin：使用整个矩形区域来计算文本布局。
                //NSStringDrawingUsesFontLeading：使用字体行距来计算文本布局。
                //NSStringDrawingUsesDeviceMetrics：使用设备的字体度量来计算文本布局。
                //NSStringDrawingTruncatesLastVisibleLine：如果文本超出矩形区域，则截断最后一行

                //CGSize size = new CGSize(35, 1000);
                //string sText = "NSLigatureAttributeName            设置连体属性，取值为NSNumber 对象(整数)，0 表示没有连体字符，1 表示使用默认的连体字符";

                //NSMutableDictionary dict = skGuiHelper.CreateTextAttribute(MyFont.DefaultFont, NSColor.Black, NSTextAlignment.Left, NSLineBreakMode.ByWordWrapping, true, true);
                //NSMutableAttributedString S = new NSMutableAttributedString(sText, dict);
                //NSAttributedString S1 = new NSAttributedString(sText);

                //foreach (NSStringDrawingOptions o in System.Enum.GetValues(typeof(NSStringDrawingOptions)))
                //{
                //    CGRect rectTemp = S.BoundingRectWithSize(size, o);
                //    Console.WriteLine(string.Format("NSStringDrawingOptions={0}, Rect = {1}", o.ToString(), rectTemp.ToString()));
                //}

                //输出结果：
                //NSStringDrawingOptions = UsesLineFragmentOrigin, Rect = { X = 0,Y = 0,Width = 35,Height = 476}
                //NSStringDrawingOptions = UsesFontLeading, Rect = { X = 0,Y = -3,Width = 35,Height = 17}
                //NSStringDrawingOptions = DisableScreenFontSubstitution, Rect = { X = 0,Y = -3,Width = 35,Height = 17}
                //NSStringDrawingOptions = UsesDeviceMetrics, Rect = { X = 1.16796875,Y = -2.6279296875,Width = 764.46698046875,Height = 13.3659296875}
                //NSStringDrawingOptions = OneShot, Rect = { X = 0,Y = -3,Width = 35,Height = 17}
                //NSStringDrawingOptions = TruncatesLastVisibleLine, Rect = { X = 0,Y = -3,Width = 35,Height = 17}

                if (maxTextHeight <= 0)
                    maxTextHeight = 10000;

                NSMutableAttributedString nsText = new NSMutableAttributedString(strText, dictText);
                CGRect rect = nsText.BoundingRectWithSize(new CGSize(maxTextWidth, maxTextHeight), options);
                sizeText = rect.Size;

                nsText.Dispose();

            }
            else
            {
                NSString nsText = new NSString(strText);
                sizeText = nsText.StringSize(dictText);

                nsText.Dispose();
            }

            dictText.Dispose();

            return sizeText;
        }

        public static CGSize MeasureStringAsSingleLine(string strText,
                            NSFont textFont,
                            int maxTextWidth,
                            NSTextAlignment align = NSTextAlignment.Left,
                            NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                            int lineSpacing = 5,
                            bool isFontBold = false,
                            bool isFontUnderLine = false,
                            NSMutableParagraphStyle dictStyle = null)
        {
            return MeasureString(strText, textFont, maxTextWidth, align, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle, NSStringDrawingOptions.UsesFontLeading);
        }



        public static CGSize MeasureStringAsSingleLine(string strText,
                            NSFont textFont,
                            int maxTextWidth,
                            out int chatFited,
                            NSTextAlignment align = NSTextAlignment.Left,
                            NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                            int lineSpacing = 5,
                            bool isFontBold = false,
                            bool isFontUnderLine = false,
                            NSMutableParagraphStyle dictStyle = null)
        {
            int lineFited = 0;
            return MeasureString(strText, textFont, maxTextWidth, 0, align, out chatFited, out lineFited, 1, 0, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle, NSStringDrawingOptions.UsesFontLeading);
        }

        public static CGSize MeasureString(string strText,
                  NSFont textFont,
                  int maxTextWidth,
                  NSTextAlignment align,
                  out int charFited,
                  out int lineFilled,
                  NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                  int lineSpacing = 5,
                  bool isFontBold = false,
                  bool isFontUnderLine = false,
                  NSMutableParagraphStyle dictStyle = null)
        {
            return MeasureString(strText, textFont, maxTextWidth, 0, align, out charFited, out lineFilled, 0, 0, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle, NSStringDrawingOptions.UsesFontLeading);
        }

        public static CGSize MeasureString(string strText,
                            NSFont textFont,
                            int maxTextWidth,
                            int maxTextHeight,
                            NSTextAlignment align,
                            out int chatFited,
                            out int lineFited,
                            int maxTextLine = 0,
                            int linePadding = 0,
                            NSLineBreakMode mode = NSLineBreakMode.ByWordWrapping,
                            int lineSpacing = 5,
                            bool isFontBold = false,
                            bool isFontUnderLine = false,
                            NSMutableParagraphStyle dictStyle = null,
                            NSStringDrawingOptions options = NSStringDrawingOptions.UsesLineFragmentOrigin)
        {

            if (maxTextWidth <= 0)
                maxTextWidth = 10000;

            if (maxTextHeight <= 0)
                maxTextHeight = 10000;

            if (maxTextLine < 0)
                maxTextLine = 0;

            if (linePadding < 0)
                linePadding = 0;

            chatFited = 0;

            // Create an attributed string with the specified font
            NSMutableDictionary dictText = skGuiHelper.CreateTextAttribute(textFont, NSColor.Black, align, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle);

            NSAttributedString attributedString = new NSAttributedString(strText, dictText);

            //// Create a text storage and layout manager
            //NSTextStorage textStorage = new NSTextStorage();
            //NSLayoutManager layoutManager = new NSLayoutManager();
            //NSTextContainer textContainer = new NSTextContainer(new CGSize(maxTextWidth, maxTextHeight));


            //// Configure the text container
            //textContainer.LineFragmentPadding = linePadding;
            //textContainer.MaximumNumberOfLines = (nuint)maxTextLine;
            //textContainer.LineBreakMode = mode;

            //// Add the text container to the layout manager and the layout manager to the text storage
            //layoutManager.AddTextContainer(textContainer);
            //textStorage.AddLayoutManager(layoutManager);
            //textStorage.SetString(attributedString);

            //// Force layout to get the number of glyphs that fit in the text container
            //layoutManager.GlyphRangeForBoundingRect(new CGRect(0, 0, maxTextWidth, maxTextHeight), textContainer);

            //// Get the range of glyphs that fit in the specified rectangle
            //NSRange glyphRange = layoutManager.GetGlyphRange(textContainer);

            //// Convert the glyph range to a character range
            //NSRange characterRange = layoutManager.GetCharacterRange(glyphRange);

            //chatFited = (int)characterRange.Length;

            //textContainer.Dispose();
            //layoutManager.Dispose();
            //textStorage.Dispose();

            if (maxTextLine >= 0)
            {
                CTFramesetter framesetter = new CTFramesetter(attributedString);

                CGPath rectPath = new CGPath();
                rectPath.AddRect(new CGRect(0, 0, maxTextWidth, maxTextHeight));

                CTFrame frame = framesetter.GetFrame(new NSRange(0, 0), rectPath, null);
                CTLine[] arrLine = frame.GetLines();

                lineFited = arrLine.Length;

                int charCount = 0;
                int charCountFirstLine = 0;
                foreach (CTLine line in arrLine)
                {
                    int lenChar  = (int)line.StringRange.Length;

                    if (charCountFirstLine == 0)
                        charCountFirstLine = lenChar;

                    charCount += lenChar;
                    line.Dispose();
                }

                //单行显示
                if (options == NSStringDrawingOptions.UsesFontLeading)
                    chatFited = charCountFirstLine;
                else
                    chatFited = charCount;

                frame.Dispose();
                rectPath.Dispose();

                framesetter.Dispose();
            }
            else
            {
                lineFited = 1;
            }

            attributedString.Dispose();
            dictText.Dispose();

            return MeasureString(strText, textFont, maxTextWidth, align, mode, lineSpacing, isFontBold, isFontUnderLine, dictStyle, options, maxTextHeight);
        }


        public static CGRect GetImageDrawRect(CGRect rect, NSImage img, PictureBoxSizeMode mode = PictureBoxSizeMode.Zoom)
        {
            if (img == null)
                goto DoExit;

            switch (mode)
            {
                case PictureBoxSizeMode.Normal:
                case PictureBoxSizeMode.AutoSize:
                    rect.Size = img.Size;
                    break;

                case PictureBoxSizeMode.StretchImage:
                    break;

                case PictureBoxSizeMode.CenterImage:
                    rect.X = rect.X + (rect.Width - img.Size.Width) / 2;
                    rect.Y = rect.Y + (rect.Height - img.Size.Height) / 2;
                    rect.Size = img.Size;
                    break;

                case PictureBoxSizeMode.Zoom:
                    CGSize size = img.Size;
                    double minRate = Math.Min(rect.Width / (double)size.Width, rect.Height / (double)size.Height);

                    int newWidth = Convert.ToInt32(size.Width * minRate);
                    int newHeight = Convert.ToInt32(size.Height * minRate);

                    rect.X += (rect.Width - newWidth) / 2;
                    rect.Y += (rect.Height - newHeight) / 2;
                    rect.Width = newWidth;
                    rect.Height = newHeight;
                    break;
            }

        DoExit:
            return rect;
        }

        private static Dictionary<int, string> FindText(string strText, Dictionary<string, skItemInfo> listSpecial)
        {
            Dictionary<int, string> dict = new Dictionary<int, string>();

            if (string.IsNullOrEmpty(strText) || listSpecial.Count == 0)
                goto DoExit;

            foreach (skItemInfo item in listSpecial.Values)
            {
                if (string.IsNullOrEmpty(item.Text))
                    continue;

                int indexStart = 0;
                while (true)
                {
                    int indexFind = strText.IndexOf(item.Text, indexStart);
                    if (indexFind < 0 || indexFind >= strText.Length)
                        break;

                    dict[indexFind] = item.Text;
                    indexStart = indexFind + item.Text.Length;
                }
            }

        DoExit:
            return dict;
        }

        private static List<int> SortIndex(Dictionary<int, string> dict)
        {
            List<int> list = new List<int>();

            foreach (int index in dict.Keys)
                list.Add(index);

            list.Sort();

            return list;
        }

        private static CGSize GetFontSize(NSFont font)
        {
            CGSize size = CGSize.Empty;

            if (!mDictFont.ContainsKey(font))
            {
                //使用空格来计算该字体的高度
                size = skGuiHelper.MeasureString(" ", font);

                mDictFont[font] = size;
            }
            else
            {
                size = mDictFont[font];
            }

            return size;
        }

        private static CGSize GetFontSize(NSFont font, string strText)
        {
            CGSize size = skGuiHelper.MeasureString(strText, font);

            return size;
        }

        public static bool IsCJK(char chr)
        {
            bool blnResult = false;

            //https://unicode-table.com/en/#cjk-unified-ideographs

            //CJK Radicals Supplement
            if (chr >= 0x2e80 && chr <= 0x2eff)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Symbols and Punctuation
            if (chr >= 0x3000 && chr <= 0x303f)
            {
                blnResult = true;
                goto DoExit;
            }

            //日文平假名 (Hiragana)  
            if (chr >= 0x3040 && chr <= 0x309F)
            {
                blnResult = true;
                goto DoExit;
            }

            //日文平假名 (Katakana)  
            if (chr >= 0x30A0 && chr <= 0x30FF)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Strokes
            if (chr >= 0x31c0 && chr <= 0x31ef)
            {
                blnResult = true;
                goto DoExit;
            }

            //日文片假名语音扩展 (Katakana Phonetic Extensions)  
            if (chr >= 0x31F0 && chr <= 0x31FF)
            {
                blnResult = true;
                goto DoExit;
            }

            //Enclosed CJK Letters and Months
            if (chr >= 0x3200 && chr <= 0x32ff)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Compatibility
            if (chr >= 0x3300 && chr <= 0x33ff)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Unified Ideographs Extension A
            if (chr >= 0x3400 && chr <= 0x4dbf)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Unified Ideographs
            if (chr >= 0x4e00 && chr <= 0x9fff)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Compatibility Ideographs
            if (chr >= 0xf900 && chr <= 0xfaff)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Compatibility Forms
            if (chr >= 0xfe30 && chr <= 0xfe4f)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Unified Ideographs Extension B
            if (chr >= 0x20000 && chr <= 0x2A6DF)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Unified Ideographs Extension C
            if (chr >= 0x2A700 && chr <= 0x2B73F)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Unified Ideographs Extension D
            if (chr >= 0x2B740 && chr <= 0x2B81F)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Unified Ideographs Extension E
            if (chr >= 0x2B820 && chr <= 0x2CEAF)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Unified Ideographs Extension F
            if (chr >= 0x2CEB0 && chr <= 0x2EBEF)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Compatibility Ideographs Supplement
            if (chr >= 0x2F800 && chr <= 0x2FA1F)
            {
                blnResult = true;
                goto DoExit;
            }

            //CJK Unified Ideographs Extension G
            if (chr >= 0x30000 && chr <= 0x3134F)
            {
                blnResult = true;
                goto DoExit;
            }

        DoExit:
            return blnResult;
        }

        /// <summary>
        /// 是否为换行符号
        /// </summary>
        public static bool CheckIsLineSplitChar(char currentChar)
        {
            //【换行：10、\n】【空格：32】【&：38】【-：45】【空格：160】【CJK：中日韩文字】
            return currentChar == 10 || currentChar == 32 || currentChar == 38 || currentChar == 45 || currentChar == 160 || currentChar == '\\' || currentChar == '/' || IsCJK(currentChar);
        }

        /// <summary>
        /// 是否为标题符号
        /// </summary>
        public static bool CheckIsPunctution(string currentChar)
        {
            return mListPunctution.Contains(currentChar);
        }

        /// <summary>
        /// 自定义文本绘图
        /// </summary>
        /// <param name="g"></param>
        /// <param name="strContent">文本内容</param>
        /// <param name="rectContent">绘制区域</param>
        /// <param name="colorContent">颜色</param>
        /// <param name="fontContent">字体</param>
        /// <param name="alignContent">字体布局</param>
        /// <param name="paddingContent">四周间距</param>
        /// <param name="lineDiff">行间距</param>
        /// <param name="byWordWrapping">按单词换行</param>
        /// <param name="dictSpecial">指定Content中的特殊显示文本</param>
        public static List<object> DrawLabel(
            string strContent,
            CGRect rectContent,
            NSColor colorContent,
            NSFont fontContent,
            NSTextAlignment alignContent,
            Padding paddingContent,
            int lineDiff = 5,
            bool byWordWrapping = true,
            Dictionary<string, skItemInfo> dictSpecial = null,
            int showLineCount = -1)
        {
            List<object> listReturn = new List<object>();

            nfloat labelHeight = 0;
            nfloat lableWidth = 0;
            bool autoEllipsis = false;

            if (dictSpecial == null)
                dictSpecial = new Dictionary<string, skItemInfo>();

            //从文本中查找特殊标记字符串及其索引
            Dictionary<int, string> dictSpecialTextIndexAndText = skGuiHelper.FindText(strContent, dictSpecial);

            //将特殊标记字符串的索引进行从小到大排序
            List<int> listSpecialTextIndex = skGuiHelper.SortIndex(dictSpecialTextIndexAndText);

            //整个文本对应的字符串索引及其对应的字体大小
            Dictionary<int, CGSize> dictAllTextIndexAndSize = new Dictionary<int, CGSize>();

            //整个文本对应的字符串索引及其对应的文本
            Dictionary<int, string> dictAllTextIndexAndText = new Dictionary<int, string>();

            int indexStart = 0;
            
            for (int intI = 0; intI < listSpecialTextIndex.Count; intI++)
            {
                int index = listSpecialTextIndex[intI];

                if (index > indexStart)
                {
                    //添加前面
                    string textPrev = strContent.Substring(indexStart, index - indexStart);
                    if (dictSpecial.ContainsKey(textPrev))
                    {
                        if (dictSpecial[textPrev].IconSize.IsEmpty)
                            dictAllTextIndexAndSize[indexStart] = skGuiHelper.GetFontSize(dictSpecial[textPrev].TextFont);
                        else
                            dictAllTextIndexAndSize[indexStart] = dictSpecial[textPrev].IconSize;
                    }
                    else
                    {
                        dictAllTextIndexAndSize[indexStart] = skGuiHelper.GetFontSize(fontContent);
                    }

                    dictAllTextIndexAndText[indexStart] = textPrev;
                }

                //添加当前
                string textSpecial = dictSpecialTextIndexAndText[index];
                if (dictSpecial.ContainsKey(textSpecial))
                {
                    if (dictSpecial[textSpecial].IconSize.IsEmpty)
                        dictAllTextIndexAndSize[index] = skGuiHelper.GetFontSize(dictSpecial[textSpecial].TextFont);
                    else
                        dictAllTextIndexAndSize[index] = dictSpecial[textSpecial].IconSize;
                }
                else
                {
                    dictAllTextIndexAndSize[index] = skGuiHelper.GetFontSize(fontContent);
                }

                dictAllTextIndexAndText[index] = textSpecial;

                //更新启始位置
                indexStart = index + textSpecial.Length;
            }

            if (indexStart < strContent.Length)
            {
                //添加最后
                string textEnd = strContent.Substring(indexStart);

                if (dictSpecial.ContainsKey(textEnd))
                {                          
                    if (dictSpecial[textEnd].IconSize.IsEmpty)
                        dictAllTextIndexAndSize[indexStart] = skGuiHelper.GetFontSize(dictSpecial[textEnd].TextFont);
                    else
                        dictAllTextIndexAndSize[indexStart] = dictSpecial[textEnd].IconSize;
                }
                else
                {
                    dictAllTextIndexAndSize[indexStart] = skGuiHelper.GetFontSize(fontContent);
                }

                dictAllTextIndexAndText[indexStart] = textEnd;
            }

            nfloat maxLineHeight = 0;
            foreach (CGSize size in dictAllTextIndexAndSize.Values)
            {
                if (size.Height > maxLineHeight)
                    maxLineHeight = size.Height;
            }

            //可进行绘制的最大区域
            nfloat left = rectContent.Left + paddingContent.Left;
            nfloat right = rectContent.Right - paddingContent.Right;
            nfloat top = rectContent.Bottom - paddingContent.Bottom;
            nfloat bottom = rectContent.Top + paddingContent.Top;

            nfloat w = rectContent.Width - paddingContent.Horizontal;
            nfloat h = rectContent.Height - paddingContent.Vertical;
            nfloat x = left;
            nfloat y = top - maxLineHeight;

            Dictionary<int, List<skDrawInfo>> mDictDraw = new Dictionary<int, List<skDrawInfo>>();

            int currentLine = 1;
            int currentIndex = 0;
            foreach (int index in dictAllTextIndexAndSize.Keys)
            {
                currentIndex++;

                string strText = dictAllTextIndexAndText[index];
                CGSize textSize = dictAllTextIndexAndSize[index];
                NSColor textColor = colorContent;
                NSFont textFont = fontContent;
                NSTextAlignment textAlign = alignContent;
                bool isUnderLine = false;

                skItemInfo item = null;

                //if (strText.Contains("将受到此公司的") && dictSpecialTextIndexAndText.Count > 0)
                //{
                //    Console.WriteLine(strContent);
                //}

                if (dictSpecial.ContainsKey(strText))
                {
                    item = dictSpecial[strText];
                    item.ListTextRect.Clear();

                    textFont = item.TextFont;
                    textColor = item.TextColor;
                    textAlign = item.TextAlign;
                    isUnderLine = item.IsUnderLine;

                    if (!item.IconSize.IsEmpty && item.Icon != null)
                    {
                        if (item.IconSize.Width > right - left)
                            break;

                        if (x + item.IconSize.Width > right)
                        {
                            y -= maxLineHeight;
                            x = left;
                            currentLine++;
                        }



                        //Rectangle rectIcon = new Rectangle(x, y + (maxLineHeight - item.IconSize.Height) / 2, item.IconSize.Width, item.IconSize.Height);
                        //g.DrawImage(item.Icon, rectIcon, new Rectangle(Point.Empty, item.Icon.Size), GraphicsUnit.Pixel);

                        //item.ListTextRect.Add(rectIcon);

                        skDrawInfo info = new skDrawInfo();
                        info.Item = item;
                        info.Icon = item.Icon;
                        info.IconRectDst = new CGRect(x, y + (maxLineHeight - item.IconSize.Height) / 2, item.IconSize.Width, item.IconSize.Height);
                        info.IconRectSrc = new CGRect(CGPoint.Empty, item.Icon.Size);
                        info.RowIndex = currentLine;

                        List<skDrawInfo> list = null;
                        if (mDictDraw.ContainsKey(currentLine))
                        {
                            list = mDictDraw[currentLine];
                        }
                        else
                        {
                            list = new List<skDrawInfo>();
                            mDictDraw[currentLine] = list;
                        }

                        list.Add(info);

                        x += item.IconSize.Width;
                        continue;
                    }
                }

                string textLeft = strText;
                int lineCount = 0;
                int lineCountEmpty = 0;

                //if (strContent.Contains("PandaControl2"))
                //{

                //}

                while (true)
                {
                    int charFited = 0;
                    int lineFilled = 0;
                    string textDraw = string.Empty;

                    CGSize sizeText = CGSize.Empty;
                    CGSize sizeDraw = CGSize.Empty;

                    //将文本高度限制在一行高度内，计算出可绘制字符的个数
                    sizeDraw = new CGSize(right - x, textSize.Height);
                    if (sizeDraw.Width <= 0)
                        goto DoNext;

                    //if (textLeft == "远程")
                    //{

                    //}

                    //计算字符可显示大小
                    //sizeText = g.MeasureString(textLeft, textFont, sizeDraw, format, out charFited, out lineFilled);
                    //注意计算字符可适配的长度，需要将&替换为&&
                    sizeText = skGuiHelper.MeasureStringAsSingleLine(textLeft, textFont, sizeDraw.Width.ToFloorInt(), out charFited);

                    //本行适合绘制的文本
                    textDraw = textLeft.Substring(0, charFited);

                    if (lableWidth < sizeText.Width)
                        lableWidth = sizeText.Width.ToCeilingInt();

                    //if (textDraw == "vor Ort")
                    //{

                        //}

                    if (showLineCount > 0 && currentLine > showLineCount)
                    {
                        //超过可显示行数
                        charFited = 0;
                        autoEllipsis = true;
                        break;//while
                    }
                    else if (showLineCount > 0 && currentLine == showLineCount && charFited < textLeft.Length)
                    {
                        //等于可显示行数，且可绘制字符数量大于剩下字符数量

                        if (textDraw.Length > 3)
                        {
                            ///ARS-1145 缩略少了很多字符问题
                            StringBuilder newText = new StringBuilder();
                            foreach (char str in textLeft)
                            {
                                newText.Append(str);
                                string gStr = newText.ToString() + "...";
                                skGuiHelper.MeasureStringAsSingleLine(gStr, textFont, sizeDraw.Width.ToFloorInt(), out charFited);
                                if (charFited < gStr.Length)
                                {
                                    textDraw = newText.ToString().Substring(0, newText.Length - 1) + "...";
                                    goto DoBreak;
                                }
                            }
                            textDraw = textDraw.Substring(0, textDraw.Length - 3) + "...";
                        }

                    DoBreak:
                        autoEllipsis = true;
                    }
                    else
                    {
                        /* 
                         * 避免换行首字符为下面符号时，往前取字符直到非下面的字符                        
                         * 英文   :,.!?)]}>"                        
                         * 中文   ：，。！？）】、》”                        
                         */
                        if (charFited > 0 && charFited < textLeft.Length)
                        {
                            for (int indexFind = charFited; indexFind > 0; indexFind--)
                            {
                                string currentCharStr = textLeft.Substring(indexFind, 1);

                                //英文   :,.!?)]}>"       
                                //中文   ：，。！？）】、》” 
                                if (CheckIsPunctution(currentCharStr))
                                {
                                    charFited = indexFind - 1;
                                }
                                else
                                {
                                    break;
                                }
                            }
                        }

                        //按单词换行
                        if (byWordWrapping && charFited > 0 && charFited < textLeft.Length)
                        {
                            //ASCII可显示字符区域【空格：32】至【~：126】
                            char currentChar = textDraw[textDraw.Length - 1];

                            //【换行：10、\n】【空格：32】【@：64】【A-Z：65-90】【`：96】【A-Z：97-122	】【0-9：48-57】

                            //【换行：10、\n】【空格：32】【&：38】【空格：160】【CJK：中日韩文字】
                            if (CheckIsLineSplitChar(currentChar))
                                goto DoNext;

                            //从已经配置字符串中，倒序查找最后一个字符空格、换行、中日韩字符
                            int indexFit = -1;
                            for (int indexFind = textDraw.Length - 1; indexFind >= 0; indexFind--)
                            {
                                char chrFind = textDraw[indexFind];
                                if (CheckIsLineSplitChar(chrFind))
                                {
                                    //找到空格或者CJK字符串
                                    indexFit = indexFind;
                                    break;
                                }
                            }

                            if (indexFit >= 0)
                            {
                                //根据字符串最后空格位置，重新调整可绘制的字符串
                                charFited = indexFit + 1;
                                textDraw = textLeft.Substring(0, charFited);
                            }
                            else if (indexFit == -1 && right - left == sizeDraw.Width)
                            {
                                textDraw = textLeft.Substring(0, charFited);
                            }
                            else
                            {
                                //当前绘制字符串不存在空格，直接尝试换行再尝试适配
                                charFited = 0;
                            }
                        }
                    }

                DoNext:
                    if (charFited == 0)
                        lineCountEmpty++;
                    else
                        lineCountEmpty = 0;

                    if (charFited > 0)
                    {
                        nfloat tmpX = x;
                        //nfloat tmpY = y + (maxLineHeight - textSize.Height);
                        nfloat tmpY = y;

                        //if (textDraw.EndsWith("\n") || charFited == textLeft.Length)
                        //{
                        //    if (textAlign.ToString().Contains("Center"))
                        //        tmpX = left + (right - left - (int)sizeText.Width) / 2;

                        //    else if (textAlign == ContentAlignment.MiddleRight)
                        //        tmpX = right - (int)sizeText.Width;
                        //}

                        if (currentIndex == dictAllTextIndexAndSize.Count && charFited == textLeft.Length && y == top)
                        {
                            tmpY = top + (bottom - top - textSize.Height) / 2;
                            //switch (textAlign)
                            //{
                            //    case ContentAlignment.MiddleLeft:
                            //    case ContentAlignment.MiddleCenter:
                            //    case ContentAlignment.MiddleRight:
                            //        tmpY = top + (bottom - top - textSize.Height) / 2;
                            //        break;
                            //}
                        }

                        //Console.WriteLine(string.Format("DrawString:  {0},{1}\t{2}", tmpX.ToString().PadLeft(3, ' '), tmpY.ToString().PadLeft(3, ' '), textDraw));

                        //以最大字体的高度来计算每行文本的行高
                        //g.DrawString(textDraw, textFont, skGuiHelper.GetBrush(textColor), tmpX, tmpY);

                        //if (item != null)
                        //    item.ListTextRect.Add(new Rectangle(tmpX, tmpY, (int)sizeText.Width, sizeDraw.Height));

                        skDrawInfo info = new skDrawInfo();
                        info.Item = item;
                        info.Text = textDraw;
                        info.TextFont = textFont;
                        info.TextColor = textColor;
                        info.TextLeft = tmpX;
                        info.TextRight = tmpX + sizeText.Width;
                        info.TextTop = tmpY + sizeText.Height;
                        info.TextBottom = tmpY;
                        info.TextSize = new CGSize(sizeText.Width, sizeDraw.Height);
                        info.RowIndex = currentLine;
                        info.IsLink = isUnderLine;

                        if (info.TextBottom > rectContent.Bottom)
                            info.TextBottom = rectContent.Bottom;

                        List<skDrawInfo> list = null;
                        if (mDictDraw.ContainsKey(currentLine))
                        {
                            list = mDictDraw[currentLine];
                        }
                        else
                        {
                            list = new List<skDrawInfo>();
                            mDictDraw[currentLine] = list;
                        }

                        list.Add(info);

                        if (charFited >= textLeft.Length)
                        {
                            x += (int)Math.Ceiling((decimal)sizeText.Width);
                            break;
                        }
                    }

                    textLeft = textLeft.Substring(charFited);
                    y -= (maxLineHeight + lineDiff);
                    x = left;
                    currentLine++;
                    lineCount++;

                    if (y >= bottom && lineCountEmpty > 1)
                    {
                        //Console.WriteLine("未找到适配字符串，且已经超出文本可绘制高度");
                        break;//while
                    }

                    if (autoEllipsis)
                    {
                        //结束绘制计算字符
                        break;//while
                    }
                }

                if (autoEllipsis)
                {
                    //结束绘制计算字符
                    break;//foreach
                }
            }

            if (autoEllipsis && showLineCount > 0)
                currentLine = showLineCount;

            labelHeight = currentLine * maxLineHeight + (currentLine - 1) * lineDiff + paddingContent.Vertical;

            string alignText = alignContent.ToString();
            if (alignText.Contains("Center") || alignText.Contains("Right"))
            {
                bool isCenter = alignText.Contains("Center");

                //重新分配行坐标
                foreach (List<skDrawInfo> list in mDictDraw.Values)
                {
                    nfloat totalWidth = 0;
                    foreach (skDrawInfo info in list)
                    {
                        if (info.Icon != null)
                            totalWidth += info.IconRectDst.Width;
                        else
                            totalWidth += info.TextSize.Width;
                    }

                    nfloat tmpX = 0;
                    if (isCenter)
                    {
                        if (rectContent.Width >= 10000)
                            tmpX = left;
                        else
                            tmpX = left + (right - left - totalWidth) / 2;
                    }
                    else
                    {
                        if (rectContent.Width >= 10000)
                            tmpX = left;
                        else
                            tmpX = right - totalWidth;
                    }

                    foreach (skDrawInfo info in list)
                    {
                        if (info.Icon != null)
                        {
                            info.IconRectDst = new CGRect(tmpX, info.IconRectDst.Y, info.IconRectDst.Width, info.IconRectDst.Height);

                            tmpX += info.IconRectDst.Width;
                        }
                        else
                        {
                            info.TextLeft = tmpX;
                            tmpX += info.TextSize.Width;
                        }
                    }
                }
            }

            foreach (List<skDrawInfo> list in mDictDraw.Values)
            {
                foreach (skDrawInfo info in list)
                {
                    if (info.Icon != null)
                    {
                        skGuiHelper.DrawImage(info.Icon, info.IconRectDst, info.IconRectSrc);

                        if (info.Item != null)
                            info.Item.ListTextRect.Add(info.IconRectDst);
                    }
                    else
                    {
                        skGuiHelper.DrawString(info.Text, info.TextColor, info.TextFont, new CGRect(info.TextLeft, info.TextBottom, info.TextSize.Width, info.TextSize.Height));

                        if (info.IsLink)
                            skGuiHelper.DrawUnderLine(info.TextFont, new CGPoint(info.TextLeft, info.TextBottom), new CGPoint(info.TextRight, info.TextBottom), info.TextColor);

                        if (info.Item != null)
                            info.Item.ListTextRect.Add(new CGRect(info.TextLeft, info.TextTop, info.TextSize.Width, info.TextSize.Height));
                    }
                }

                list.Clear();
            }

            mDictDraw.Clear();

            listReturn.Add(labelHeight);
            listReturn.Add(autoEllipsis);
            listReturn.Add(lableWidth);

            return listReturn;
        }

    }

}

