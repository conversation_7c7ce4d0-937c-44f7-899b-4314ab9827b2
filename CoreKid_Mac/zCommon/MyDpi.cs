﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.IO;
using System.Threading;
using System.Security.Cryptography;
using System.Drawing;
using System.Collections.Specialized;
using System.Xml;
using System.Diagnostics;
using System.Management;
using System.Globalization;
using System.Runtime.InteropServices;
using System.Windows.Forms;

using iTong.CoreModule;
using iTong.CoreFoundation;
using iTong.Device;
using iTong.Android;

using CoreFoundation;
using ObjCRuntime;
using CoreGraphics;
using AppKit;
using Foundation;
using Size = CoreGraphics.CGSize;
using Point = CoreGraphics.CGPoint;
using Rectangle = CoreGraphics.CGRect;
using Font = AppKit.NSFont;
using Color = AppKit.NSColor;
using Icon = AppKit.NSImage;
using Image = AppKit.NSImage;

using skContextMenuStrip = iTong.CoreModule.skMenu;
using ToolStripMenuItem = iTong.CoreModule.skMenuItem;
using ToolStripItem = iTong.CoreModule.skMenuItem;
using PictureBox = iTong.CoreModule.skPictureBox;
using Label = iTong.CoreModule.skLabel;
//using NotifyIcon = iTong.CoreModule.skStatusItem;
using ContentAlignment = iTong.CoreModule.ContentAlignment;


namespace iTong.CoreModule
{
    public static class MyDpi
    {
        static MyDpi()
        {

        }

        public static double DPIScale { get; set; } = 1.0;

        /// <summary>
        /// 映射window上的布局到mac
        /// </summary>
        /// <param name="panel"></param>
        public static void LayoutSubviewFromWin(this NSView panel,Action action = null)
        {
            foreach (var item in panel.Subviews)
            {
                if (item is Control control)
                {
                    LayoutSubviewFromWin(panel, control);
                }
            }

            if (action != null)
                action.Invoke();
        }

        /// <summary>
        /// 映射window上的布局到mac
        /// </summary>
        /// <param name="panel"></param>
        public static void LayoutSubviewFromWin(NSView panel, NSView control)
        {
            control.SetFrameOrigin(new Point(control.Frame.Left, panel.Frame.Height - control.Frame.Top - control.Frame.Height));
        }

        public static Padding ToDPI(this Padding padding, int frmDpi = 0)
        {
            return new Padding(padding.Left.ToDPI(frmDpi), padding.Top.ToDPI(frmDpi), padding.Right.ToDPI(frmDpi), padding.Bottom.ToDPI(frmDpi));
        }

        public static Size ToDPI(this Size size, int frmDpi = 0)
        {
            return new Size(size.Width.ToDPI(frmDpi), size.Height.ToDPI(frmDpi));
        }

        public static Point ToDPI(this Point pos, int frmDpi = 0)
        {
            return new Point(pos.X.ToDPI(frmDpi), pos.Y.ToDPI(frmDpi));
        }

        public static int ToDPI(this int source, int frmDpi = 0)
        {
            return ToDPI((double)source, frmDpi);
        }

        public static int ToDPI(this long source, int frmDpi = 0)
        {
            return ToDPI((double)source, frmDpi);
        }

        public static int ToDPI(this short source, int frmDpi = 0)
        {
            return ToDPI((double)source, frmDpi);
        }

        public static int ToDPI(this nfloat source, int frmDpi = 0)
        {
            return (int)source;
        }

        public static int ToDPI(this double source, int frmDpi = 0)
        {
            return (int)source;
        }


        public static int FromDPI(this double source, int frmDpi = 0)
        {
#if MAC || !DPI
            return (int)source;
#else
            if (source == 0)
                return 0;

            return (int)(source / DPIScale);
#endif
        }

        public static void ToDPI(skForm form)
        {
#if DPI
            form.SuspendLayout();
            form.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            form.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            form.ResumeLayout(false);
#endif
        }
    }
}
