﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Windows.Forms;

using Foundation;
using CoreGraphics;
using AppKit;

using iTong.CoreFoundation;

namespace iTong.CoreModule
{
    public class FolderBrowserDialog : OpenFileDialog
    {
        public bool ShowNewFolderButton = true;
        public string Description = string.Empty;
        public string SelectedPath = string.Empty;

        protected override void InitPanel(NSSavePanel pnl)
        {
            base.InitPanel(pnl);

            if (pnl is NSOpenPanel)
            {
                ((NSOpenPanel)pnl).CanChooseDirectories = true;
                ((NSOpenPanel)pnl).CanChooseFiles = false;
            }

            pnl.CanCreateDirectories = this.ShowNewFolderButton;
            pnl.Message = this.Description;
        }

        protected override void SavePanel(NSSavePanel pnl)
        {
            base.SavePanel(pnl);

            this.SelectedPath = pnl.Url.Path;
        }
    }

    public class OpenFileDialog : SaveFileDialog
    {
        protected override void InitPanel(NSSavePanel pnl)
        {
            base.InitPanel(pnl);

            ((NSOpenPanel)pnl).CanChooseFiles = this.mCanChooseFiles;
            ((NSOpenPanel)pnl).CanChooseDirectories = this.mCanChooseDirectories;
            ((NSOpenPanel)pnl).AllowsMultipleSelection = this.mMultiselect;
        }

        private bool mMultiselect = true;
        public bool Multiselect
        {
            get { return this.mMultiselect; }
            set { this.mMultiselect = value; }
        }

        private bool mCanChooseFiles = true;
        public bool CanChooseFiles
        {
            get { return this.mCanChooseFiles;}
            set { this.mCanChooseFiles = value; }
        }

        private bool mCanChooseDirectories = true;
        public bool CanChooseDirectories
        {
            get { return this.mCanChooseDirectories; }
            set { this.mCanChooseDirectories = value; }
        }
    }

    public class SaveFileDialog : System.IDisposable
    {
        public bool CheckFileExists = true;
        public bool CheckPathExists = true;

        public string Title = string.Empty;
        public string Filter = string.Empty;
        public string InitialDirectory = string.Empty;

        public string FileName = string.Empty;
        public string[] FileNames = new string[] { };
        public List<string> FileNamesList = new List<string>();
        public object Tag;

        private string[] SetFilters(NSSavePanel pnl)
        {
            List<string> list = new List<string>();

            string[] arrValue = this.Filter.Split(new char[] { '|' });

            for (int intI = 1; intI < arrValue.Length; intI += 2)
            {
                string[] arrFilter = arrValue[intI].Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
                foreach (string item in arrFilter)
                {
                    string strExt = Path.GetExtension(item).TrimStart('.');
                    if (strExt == "*" || list.Contains(strExt))
                        continue;

                    list.Add(strExt);
                }
            }

            if (list.Count > 0)
                pnl.AllowedFileTypes = list.ToArray();

            return list.ToArray();
        }

        private string GetMessage()
        {
            string[] arrValue = this.Filter.Split(new char[] { '|' });
            return arrValue[0];
        }

        protected virtual void InitPanel(NSSavePanel pnl)
        {
          
        }

        protected virtual void SavePanel(NSSavePanel pnl)
        {

        }

        NSSavePanel pnl = null;

        public DialogResult ShowDialog()
        {
            DialogResult result = DialogResult.Cancel;

            if (MyWindow.InvokeRequired)
            {
                NSBundle.MainBundle.InvokeOnMainThread(() => result = this.ShowDialog());
            }
            else
            {
                //if (Common.OSVersion >= new Version("10.15"))
                //{
                    if (this.GetType() == typeof(SaveFileDialog))
                        pnl = NSSavePanel.SavePanel;
                    else
                        pnl = NSOpenPanel.OpenPanel;
                //}
                //else
                //{
                //    if (this.GetType() == typeof(tbSaveFileDialog))
                //        pnl = new NSSavePanel();
                //    else
                //        pnl = new tbOpenPanel();
                //}

                pnl.ReleasedWhenClosed = true;
                pnl.ShowsHiddenFiles = false;
                pnl.AllowsOtherFileTypes = false;
                pnl.CanCreateDirectories = true;
                pnl.CanSelectHiddenExtension = false;
                pnl.ExtensionHidden = false;

                if (string.IsNullOrEmpty(this.InitialDirectory) || !Directory.Exists(this.InitialDirectory))
                    this.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory);

                if (Common.OSVersion >= new Version("10.10"))
                    pnl.TitleVisibility = NSWindowTitleVisibility.Visible;

                pnl.Title = this.Title;
                pnl.Message = this.GetMessage();
                pnl.DirectoryUrl = new NSUrl(this.InitialDirectory, true);
                pnl.NameFieldStringValue = this.FileName;
                pnl.AllowsOtherFileTypes = true;

                this.SetFilters(pnl);
                this.InitPanel(pnl);

                pnl.AnimationBehavior = NSWindowAnimationBehavior.AlertPanel;
                pnl.Center();

                bool blnOK = false;

                if (pnl is skOpenPanel)
                    blnOK = ((pnl as skOpenPanel).ShowDialog(MyWindow.CurrentWindow.ContentView) == DialogResult.OK);
                else
                    blnOK = (pnl.RunModal() == (int)NSPanelButtonType.Ok);

                if (blnOK)
                {
                    result = DialogResult.OK;

                    List<string> list = new List<string>();

                    if (this.GetType() == typeof(SaveFileDialog))
                    {
                        list.Add(pnl.Url.Path);
                    }
                    else 
                    {
                        foreach (NSUrl url in ((NSOpenPanel)pnl).Urls)
                            list.Add(url.Path);
                    }

                    this.FileName = pnl.Url.Path;
                    this.FileNames = list.ToArray();
                    this.FileNamesList = list;

                    this.SavePanel(pnl);
                }
            }

            return result;
        }

        public void Dispose()
        {
            if (pnl != null)
            {
                pnl.Dispose();
            }
        }
    }

    [Register("skOpenPanel")]
    public class skOpenPanel : NSOpenPanel
    {
        #region Constructors

        // Called when created from unmanaged code
        public skOpenPanel(IntPtr handle) : base(handle)
        {
            this.Initialize();
        }

        // Called when created directly from a XIB file
        [Export("initWithCoder:")]
        public skOpenPanel(NSCoder coder) : base(coder)
        {
            this.Initialize();
        }

        [Export("init")]
        public skOpenPanel() : base()
        {
            this.Initialize();
        }


        #endregion

        protected bool mRunModal = false;

        private void Initialize()
        {

        }

        public DialogResult ShowDialog()
        {
            return ShowDialog(null, CGPoint.Empty, CGPoint.Empty);
        }

        public DialogResult ShowDialog(NSView parent)
        {
            return ShowDialog(parent, CGPoint.Empty, CGPoint.Empty);
        }

        public DialogResult ShowDialog(NSView parent, CGPoint parentOffset)
        {
            return ShowDialog(parent, parentOffset, CGPoint.Empty);
        }

        public DialogResult ShowDialog(NSView parent, CGPoint parentOffset, CGPoint posOnScreen)
        {
            DialogResult result = DialogResult.Cancel;

            try
            {
                this.mRunModal = true;
                this.InitLoaction(parent, parentOffset, CGPoint.Empty);
                nint iResult = NSApplication.SharedApplication.RunModalForWindow(this);

                Console.WriteLine("ShowDialog:" + this.Frame.ToString());

                this.Close();
                this.mRunModal = false;
                NSApplication.SharedApplication.StopModal();

                if (iResult == (int)NSPanelButtonType.Ok)
                    result = DialogResult.OK;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbOpenPanel.ShowDialog"); 
            }

            return result;
        }

        protected virtual void InitLoaction(NSView parent, CGPoint parentOffset, CGPoint posOnScreen)
        {
            CGPoint pos = CGPoint.Empty;
            if (posOnScreen != CGPoint.Empty)
            {
                pos = posOnScreen;
            }
            else if (parent != null)
            {
                CGSize tmpSize = parent.Frame.Size;
                CGPoint tmpWindowLocation = parent.Window.Frame.Location;
                CGPoint tmpPost = new CGPoint(parentOffset.X + (tmpSize.Width - this.Frame.Size.Width) / 2, parentOffset.Y + (tmpSize.Height - this.Frame.Size.Height) / 2);
                CGPoint tmpLoactionToWindow = parent.ConvertPointToView(tmpPost, null);
                pos = new CGPoint(tmpWindowLocation.X + tmpLoactionToWindow.X, tmpWindowLocation.Y + tmpLoactionToWindow.Y);
            }
            else
            {
                CGSize tmpSize = NSScreen.MainScreen.VisibleFrame.Size;
                pos = new CGPoint((tmpSize.Width - this.Frame.Size.Width) / 2, (tmpSize.Height - this.Frame.Size.Height) / 2);
            }

            this.SetFrameOrigin(pos);

            Console.WriteLine("InitLoaction:" + this.Frame.ToString());
        }

        public override void Center()
        {
            if (!this.mRunModal)
                base.Center();
        }
    }

}

