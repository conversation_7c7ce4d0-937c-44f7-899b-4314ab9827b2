<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="14460.31" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="14460.31"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="MainWindowWechatController">
            <connections>
                <outlet property="window" destination="2" id="6"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" hasShadow="NO" appearanceType="aqua" animationBehavior="default" id="2" customClass="MainWindowWechat">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <rect key="contentRect" x="131" y="74" width="890" height="610"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1920" height="1057"/>
            <view key="contentView" wantsLayer="YES" appearanceType="aqua" id="3">
                <rect key="frame" x="0.0" y="0.0" width="890" height="610"/>
                <autoresizingMask key="autoresizingMask"/>
            </view>
            <point key="canvasLocation" x="344" y="150"/>
        </window>
    </objects>
</document>
