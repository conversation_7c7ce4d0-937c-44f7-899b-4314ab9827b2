﻿using System;
using System.Collections.Generic;
using System.Linq;

using AppKit;
using Foundation;
using ObjCRuntime;

using System.ComponentModel;
using System.IO;

using iTong.CoreModule;

namespace iTongEx
{
	public partial class AppDelegateWechat : NSApplicationDelegate
	{
		MainWindowWechatController mainWindowController;

		public static AppDelegateWechat Instance
		{
			get
			{
				return NSApplication.SharedApplication.Delegate as AppDelegateWechat;
			}
		}

		public MainWindowWechatController MWController
		{
			get
			{
				return mainWindowController;
			}
		}

		public void ExitApp()
		{
			NSApplication.SharedApplication.Terminate(this);
		}

		public override void DidFinishLaunching (NSNotification notification)
		{			
			mainWindowController = new MainWindowWechatController();
			mainWindowController.Window.MakeKeyAndOrderFront (this);

			// 设置此属性后，内存大概省0.1M左右
			mainWindowController.Window.PreferredBackingLocation = NSWindowBackingLocation.MainMemory;

            tbMainView.InitMenu();
		}	

		// 实现单击窗体的X按钮时，关闭窗体
		public override bool ApplicationShouldTerminateAfterLastWindowClosed(NSApplication sender)
		{
			return true;
		}

		// 实现Dock栏退出时触发
		public override NSApplicationTerminateReply ApplicationShouldTerminate(NSApplication sender)
		{
			//if (AppleWeixin.HaveExited)
			//	return NSApplicationTerminateReply.Cancel;


			//AppleWeixin.ClearTemp();
			//WeixinHelper.UpdateSoftRunTime();

			return NSApplicationTerminateReply.Now;
		}

	}
}
