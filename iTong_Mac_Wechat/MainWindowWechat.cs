﻿using System;

using iTong.CoreModule;

using AppKit;
using CoreGraphics;
using Foundation;

namespace iTongEx
{
	public partial class MainWindowWechat : tbWindow
	{

		public MainWindowWechat(IntPtr handle) : base(handle)
		{
		}

		[Export("initWithCoder:")]
		public MainWindowWechat(NSCoder coder) : base(coder)
		{

		}

		[Export("init")]
		public MainWindowWechat() : base()
		{

		}

		protected override void OnWindowWillClose(object sender, EventArgs e)
		{
			foreach (tbWindow frm in MyWindow.OpenWindows)
			{
				if (frm != this)
					frm.Close();
			}

			base.OnWindowWillClose(sender, e);
		}

	}
}
