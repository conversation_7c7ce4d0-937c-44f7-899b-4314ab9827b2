﻿Public Class WealthLoginHelper
    Inherits PluginLogin

    Private mUrlLogin As String = "http://zj.tongbu.com/api/login"
    Private mWealthHelper As WealthHelper = Nothing

#Region "--- 单例 ---"

    Public Overloads Shared Function Instance() As PluginLogin
        Try
            SyncLock mLocker
                If mPluginLogin Is Nothing Then
                    mPluginLogin = New WealthLoginHelper()
                End If
            End SyncLock
        Catch ex As Exception
        End Try

        Return mPluginLogin
    End Function

#End Region

    Sub New()
        mWealthHelper = WealthHelper.Instance()
    End Sub

    Protected Overrides Sub LoginInThread()
        Try
            Me.mWealthHelper.YuzhuangList = Nothing

            Me.mUId = 0
            Me.mName = String.Empty
            Me.mIsLogin = False

            Dim account As String = Me.Account
            Dim password As String = Me.PwdMd5

            Dim cpuInfo As KeyValuePair(Of String, String) = Common.GetCpuInfo()
            Dim strJson As String = "{" + String.Format("""appversion"":""{0}"",", Me.mWealthHelper.AppVersion) + """pc"":{" + _
                                                 String.Format("""cpuid"":""{0}"",""mac"":""{1}"",""arch"":""{2}"",""platform"":""{3}"",""version"":""{4}""", _
                                                 cpuInfo.Key, Common.GetMacAddress, cpuInfo.Value, My.Computer.Info.OSPlatform, My.Computer.Info.OSVersion) + _
                                                 "},""user"":{" + String.Format("""email"":""{0}"",""password"":""{1}""", account, WealthHelper.GetHashHex(password)) + "}}"

            Dim strPost As String = String.Format("json={0}", Common.EncryptDES(strJson, Common.RgbKeyString, Common.RgbKeyString))
            strPost = WealthHelper.FormatPostData(strPost)
            Common.Log(String.Format("-------- 登录：UrlLogin:{0} Post:{1}", Me.mUrlLogin, strPost))
            Dim strResult As String = WealthHelper.PostData(Me.mUrlLogin, strPost)
            Common.Log(String.Format("-------- 登录：Result:{0} ", strResult))
            Try
                If String.IsNullOrEmpty(strResult) Then
                    Me.mDes = "服务器无返回数据，请稍后再试"
                    GoTo DO_EXIT
                End If

                Dim objJson As JsonObject = JsonParser.ParseString(strResult)
                If objJson Is Nothing Then
                    Me.mDes = "返回数据错误"
                    GoTo DO_EXIT
                End If

                If objJson.ContainsKey("code") Then
                    Dim code As String = CType(objJson("code"), JsonNumber).ToString()

                    If String.Compare(code, "200") <> 0 Then
                        Me.mDes = CType(objJson("message"), JsonString).Value

                        If String.IsNullOrEmpty(Me.mDes) Then
                            Me.mDes = "帐号或密码错误"
                        End If
                        GoTo DO_EXIT
                    End If

                    If Not objJson.ContainsKey("uid") Then
                        Me.mDes = "ID为空，请联系客服人员解决"
                        GoTo DO_EXIT
                    End If
                    Me.mUid = CType(objJson("uid"), JsonString).Value

                    If Not objJson.ContainsKey("profile") Then
                        Me.mDes = "昵称为空，请联系客服人员解决"
                        GoTo DO_EXIT
                    End If
                    Dim jsonProfile As JsonObject = objJson("profile")
                    If Not jsonProfile.ContainsKey("nickname") Then
                        Me.mDes = "昵称为空，请联系客服人员解决"
                        GoTo DO_EXIT
                    End If
                    Me.mName = CType(jsonProfile("nickname"), JsonString).Value

                    '用户登录信息
                    Me.mToken = CType(jsonProfile("usertoken"), JsonString).Value

                    Me.mAccount = account
                    Me.mIsLogin = True
                    IniSetting.SetAccoutInfo(account, password)

                    Me.mWealthHelper.Start2CheckPop(False)

                    Me.mWealthHelper.tmrRefresh.Start()
                    Common.LogException("自动刷新推广列表计时器开始")
                End If
            Catch
            End Try

DO_EXIT:
            Me.RaiseLoginEvent()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoginInThread")
        End Try
    End Sub

    Public Overrides Sub CheckAutoLogin()
        Dim strUserAccount As String = ""
        Dim strPwd As String = ""

        IniSetting.GetAccoutInfo(strUserAccount, strPwd)
        If strUserAccount.Length > 0 AndAlso strUserAccount.Length > 0 Then
            Me.mAccount = strUserAccount
            Me.PwdMd5 = strPwd
            Me.Login()
        End If
    End Sub

    ''取消登录
    'Public Function CancelLogin() As Boolean
    '    Dim isCancel As Boolean = False
    '    If mtdLogin IsNot Nothing AndAlso mtdLogin.ThreadState <> ThreadState.Stopped Then
    '        Try
    '            isCancel = True
    '            Me.mtdLogin.Abort()
    '            RaiseEvent LogoutEvent(Me, EventArgs.Empty)
    '        Catch
    '        End Try
    '    End If
    '    Return isCancel
    'End Function

    Protected Overrides Sub SetLogout()
        Try
            Me.mIsLogin = False
            Me.mUId = 0
            Me.mAccount = String.Empty
            Me.mName = String.Empty

            Me.mWealthHelper.YuzhuangList = Nothing

            IniSetting.ClearAccountInfo()

            Me.mWealthHelper.tmrRefresh.Stop()
            Common.LogException("自动刷新推广列表计时器停止")
        Catch ex As Exception
        End Try
    End Sub

End Class
