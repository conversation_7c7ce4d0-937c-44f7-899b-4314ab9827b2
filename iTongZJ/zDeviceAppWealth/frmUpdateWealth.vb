﻿Imports System.Threading

Public Class frmUpdateWealth
    Private Delegate Sub SwitchViewInCheckUpdateHandler(ByVal value As Boolean)

    Private mFrmApp As IApp = Nothing
    Private dgvUpdate As tbDataGridViewEx

    Private mlstUpdateSoftResult As New List(Of UpdatePackageInfo)
    Private mlstUpdateIgnore As New List(Of UpdatePackageInfo)
    Private mIsDeviceUpdate As Boolean = False
    Private mHadCheckSoftUpdate As Boolean = False
    Private mTdCheckSoftUpdate As Thread = Nothing
    Private mtdSearch As Thread
    Private mtdRefleshIcon As Thread

    Private mDownloadManage As MultiThreadDownload = Nothing
    Private mInstallHelper As iPhoneInstallHelper
    Private miPhoneAuthorizeHelper As AuthorizeHelper = Nothing
    Private mCoreUpdate As CoreUpdateHelper
    Private mIsRefresh As Boolean = False

    Private mPersonSignatureHelper As PersonSignatureHelper

    Protected mWealthHelper As WealthHelper = Nothing
    Private mLogin As PluginLogin = Nothing
    Public Shared Event JudgeInstallChangedEventHandler(ByVal sender As Object, ByVal args As ValueChangeArgs)

    Dim mFrmSetLocalPackageFolder As New frmSetLocalPackageFolder()

    Private mStrGroupLocalText As String = "本地分组，免费软件不参与结算"

    Protected Enum ViewStyle
        Loading
        NoLogin
        Nomal
    End Enum

#Region "--- 初始化 ---"

    Public Sub New()
        MyBase.New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Public Sub New(ByVal application As IApplication, _
                                  ByVal frmApp As IApp, _
                                  ByVal device As IDevice, _
                                  ByVal strName As String, _
                                  ByVal key As FunctionKey)

        MyBase.New(application, device)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language

        Me.Name = strName
        Me.mFrmApp = frmApp
        Me.FunctionMappingKey = key

        Me.InitUpdateDataGridView()

        Me.SetViewStyle(ViewStyle.Loading)

        If Common.IsTestMode Then
            Me.btnTest.Visible = True
        End If

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitDownloadManage()
        Me.InitiPhoneInstallHelper()
        Me.InitWealthHelper()
        Me.InitiPhoneAuthorizeHelper()
        Me.InitCoreUpdateHelper()

        Me.chkJudgeInstall.Checked = IniSetting.GetJudgeInstalled
        Me.chkScore.Checked = IniSetting.GetShowScore
        Me.SetPriceColumnVisible()

        '显示获取设备教程
        Me.ShowAppInfo()

        RemoveHandler JudgeInstallChangedEventHandler, AddressOf OnJudgeInstallChanged
        AddHandler JudgeInstallChangedEventHandler, AddressOf OnJudgeInstallChanged

        RemoveHandler LocalAppHelper.LocalAppEventHandler, AddressOf OnLoadLocalApp
        AddHandler LocalAppHelper.LocalAppEventHandler, AddressOf OnLoadLocalApp
    End Sub

    Private Sub OnJudgeInstallChanged(ByVal sender As Object, ByVal args As ValueChangeArgs)
        If Me.Name = CType(sender, frmUpdateWealth).Name Then
            Return
        End If

        If Me.chkJudgeInstall.Checked <> args.IsCheck Then
            Me.chkJudgeInstall.Checked = args.IsCheck
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.tsmiUpdate.Text = Me.Language.GetString("App.Button.Update") '"Update"
        Me.tsmiGoToiTunes.Text = Me.Language.GetString("App.Button.GoToiTunes") '"Go to iTunes"
        Me.tsmiIgnore.Text = Me.Language.GetString("Common.Ignore") '"Ignore"
        Me.lblLoading.Text = Me.Language.GetString("App.Cell.Loading") 'loading

        Me.btnRepair.tbToolTip = "如果运行正版软件时闪退或需要输入Apple ID和密码，请尝试修复。"
    End Sub

    Private Sub frmUpdate_Shown(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Shown
        Try
            If PluginLogin.Instance.IsLogin Then
                If Not Me.bgwWealth.IsBusy Then
                    Me.bgwWealth.RunWorkerAsync()
                End If
            Else
                Me.SetViewStyle(ViewStyle.NoLogin)

                '判断自动登录
                If IniSetting.GetAutoLogin() = True Then
                    PluginLogin.Instance.CheckAutoLogin()
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmUpdate_Shown")
        End Try
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        Try
            If Me.mTdCheckSoftUpdate IsNot Nothing AndAlso Me.mTdCheckSoftUpdate.ThreadState <> ThreadState.Stopped Then
                Me.mTdCheckSoftUpdate.Abort()
            End If
        Catch
        End Try

        Try
            If Me.mtdSearch IsNot Nothing AndAlso Me.mtdSearch.ThreadState <> ThreadState.Stopped Then
                Me.mtdSearch.Abort()
            End If
        Catch
        End Try

        Try
            If Me.mQueueRefleshPackageIcon IsNot Nothing Then
                Me.mQueueRefleshPackageIcon.Clear()
                Me.mQueueRefleshPackageIcon = Nothing
            End If

        Catch
        End Try

        Try
            If Me.mTdRefleshPackageIcon IsNot Nothing Then
                Me.mTdRefleshPackageIcon.Abort()
            End If
        Catch
        End Try

        RemoveHandler LocalAppHelper.LocalAppEventHandler, AddressOf OnLoadLocalApp

        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)

        Me.ChangeConnectState(device)

        If Me.mInstallHelper Is Nothing Then
            Me.InitiPhoneInstallHelper()
        End If

        If Me.mPersonSignatureHelper Is Nothing Then
            Me.InitPersonSignatureHelper()
        End If

        If Me.miPhoneAuthorizeHelper Is Nothing Then
            Me.InitiPhoneAuthorizeHelper()
        End If

        If Me.mQueueCheckAppInstall Is Nothing Then
            Me.mQueueCheckAppInstall = New Queue(Of UpdatePackageInfo)
        End If

    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        If Me.InitCompleted Then
            Me.SetProgressStateWithConnect(False)
        End If

        If Me.mInstallHelper IsNot Nothing Then
            RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
            Me.mInstallHelper = Nothing
        End If

        If Me.mPersonSignatureHelper IsNot Nothing Then
            RemoveHandler Me.mPersonSignatureHelper.AppDownloadEventHandler, AddressOf mPersonSignatureHelper_AppDownloadEventHandler
            Me.mPersonSignatureHelper.Dispose()
            Me.mPersonSignatureHelper = Nothing
        End If

        If Me.miPhoneAuthorizeHelper IsNot Nothing Then
            RemoveHandler miPhoneAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart
            RemoveHandler miPhoneAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted

            Me.miPhoneAuthorizeHelper = Nothing
            Me.btnRepair.Text = "修复授权"
            Me.btnRepair.Enabled = False
        End If

        If Me.mQueueCheckAppInstall IsNot Nothing Then
            Me.mQueueCheckAppInstall.Clear()
            Me.mQueueCheckAppInstall = Nothing
        End If

        Try
            If Me.mTdCheckAppInstall IsNot Nothing AndAlso Me.mTdCheckAppInstall.ThreadState <> ThreadState.Stopped Then
                Me.mTdCheckAppInstall.Abort()
            End If
        Catch
        End Try

        If Me.bgwCheckAppInstall IsNot Nothing AndAlso Not Me.bgwCheckAppInstall.IsBusy Then
            Me.bgwGetDeviceApp.RunWorkerAsync()
        End If

        Me.mFrmApp.ShowInfoMsg(Me.FunctionMappingKey, "")
    End Sub

    Private Sub InitUpdateDataGridView()
        Me.dgvUpdate = New tbDataGridViewEx

        With Me.dgvUpdate
            .SuspendLayout()

            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.Font = Common.CreateFont("Arial", 9, FontStyle.Bold)
            cellStyle.ForeColor = Color.Black
            cellStyle.WrapMode = DataGridViewTriState.True

            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Common.Label.Name"), 310, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            colName.DefaultCellStyle = cellStyle

            .tbMouseHoverResponse = True
            .RowTemplate.Height = 58

            .Columns.Clear()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False))
            .Columns.Add(colName)
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colVersion", Me.Language.GetString("App.Column.Version"), 80, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)) '"Version"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colSize", Me.Language.GetString("Common.Columns.Size"), 80, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) 'Size
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colPrice", "积分", 100, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)) 'Price
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", "", 244, True, False, DataGridViewContentAlignment.MiddleRight, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True))

            .AllowDrop = True
            .Dock = DockStyle.Fill
            .GroupRowTemplateArray = Me.GetGroupTemplate()

            .ResumeLayout()
        End With

        RemoveHandler dgvUpdate.CellButtonClick, AddressOf dgvUpdate_CellButtonClick
        AddHandler dgvUpdate.CellButtonClick, AddressOf dgvUpdate_CellButtonClick

        'RemoveHandler dgvUpdate.ShowToolTip, AddressOf dgvUpdate_ShowToolTip
        'AddHandler dgvUpdate.ShowToolTip, AddressOf dgvUpdate_ShowToolTip

        RemoveHandler dgvUpdate.GroupTextClick, AddressOf dgvUpdate_GroupTextClick
        AddHandler dgvUpdate.GroupTextClick, AddressOf dgvUpdate_GroupTextClick

        Utility.AddForm2Panel(Me.dgvUpdate, Me.pnlContainer)
    End Sub

    Private Sub InitiPhoneInstallHelper()
        If Me.mDevice Is Nothing Then
            Return
        End If

        Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)

        RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
        AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall

        RemoveHandler Me.mInstallHelper.UpdateInstallCountEventHandler, AddressOf OnUpdateInstallCountEventHandler
        AddHandler Me.mInstallHelper.UpdateInstallCountEventHandler, AddressOf OnUpdateInstallCountEventHandler
    End Sub

    Private Sub InitPersonSignatureHelper()
        If Me.mDevice Is Nothing Then
            Return
        End If

        Me.mPersonSignatureHelper = PersonSignatureHelper.GetInstance(Me.mDevice)

        RemoveHandler Me.mPersonSignatureHelper.AppDownloadEventHandler, AddressOf mPersonSignatureHelper_AppDownloadEventHandler
        AddHandler Me.mPersonSignatureHelper.AppDownloadEventHandler, AddressOf mPersonSignatureHelper_AppDownloadEventHandler

    End Sub

    Private Sub InitDownloadManage()
        Me.mDownloadManage = MultiThreadDownload.Instance()

        RemoveHandler Me.mDownloadManage.TaskAdd, AddressOf OnTaskAdd
        AddHandler Me.mDownloadManage.TaskAdd, AddressOf OnTaskAdd

        RemoveHandler Me.mDownloadManage.TaskUpdate, AddressOf OnTaskUpdate
        AddHandler Me.mDownloadManage.TaskUpdate, AddressOf OnTaskUpdate

        RemoveHandler Me.mDownloadManage.TaskStart, AddressOf OnTaskStart
        AddHandler Me.mDownloadManage.TaskStart, AddressOf OnTaskStart

        RemoveHandler Me.mDownloadManage.DownloadItemCallBack, AddressOf OnDownloading
        AddHandler Me.mDownloadManage.DownloadItemCallBack, AddressOf OnDownloading

        RemoveHandler Me.mDownloadManage.TaskDelete, AddressOf OnTaskDelete
        AddHandler Me.mDownloadManage.TaskDelete, AddressOf OnTaskDelete

        RemoveHandler Me.mDownloadManage.DownloadItemCompleted, AddressOf OnDownloaded
        AddHandler Me.mDownloadManage.DownloadItemCompleted, AddressOf OnDownloaded

        RemoveHandler Me.mDownloadManage.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed
        AddHandler Me.mDownloadManage.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed
    End Sub

    Private Sub InitWealthHelper()
        Me.mWealthHelper = WealthHelper.Instance()
        Me.mLogin = PluginLogin.Instance()

        RemoveHandler mLogin.LoginEvent, AddressOf WealthHelper_LoginEvent
        AddHandler mLogin.LoginEvent, AddressOf WealthHelper_LoginEvent

        RemoveHandler mLogin.LogoutEvent, AddressOf WealthHelper_LogoutEvent
        AddHandler mLogin.LogoutEvent, AddressOf WealthHelper_LogoutEvent

        RemoveHandler mWealthHelper.CheckSoftExistEvent, AddressOf OnCheckSoftExistEvent
        AddHandler mWealthHelper.CheckSoftExistEvent, AddressOf OnCheckSoftExistEvent

        RemoveHandler mWealthHelper.CheckCanInstallEventHandler, AddressOf CoreUpdate_CheckCanInstall
        AddHandler mWealthHelper.CheckCanInstallEventHandler, AddressOf CoreUpdate_CheckCanInstall

        '财富节点下的软件才需要定时刷新， 其他热门软件不需要
        If Me.CheckIsCaifuView Then
            RemoveHandler mWealthHelper.RefreshListEvent, AddressOf OnRefreshListEvent
            AddHandler mWealthHelper.RefreshListEvent, AddressOf OnRefreshListEvent
        End If
    End Sub

    Private Sub InitiPhoneAuthorizeHelper()
        If Me.mDevice Is Nothing Then
            Me.btnRepair.Enabled = False
            Return
        End If

        Me.btnRepair.Enabled = True

        Me.miPhoneAuthorizeHelper = AuthorizeHelper.GetInstance(Me.mDevice)

        RemoveHandler miPhoneAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart
        AddHandler miPhoneAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart

        RemoveHandler miPhoneAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted
        AddHandler miPhoneAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted

        If Me.miPhoneAuthorizeHelper.IsAuthorizing Then
            Me.SwitchAuthorizeView(True)
        End If
    End Sub

    Private Sub InitCoreUpdateHelper()
        Me.mCoreUpdate = CoreUpdateHelper.Instance()
    End Sub

    Private Sub CoreUpdate_CheckCanInstall(ByVal sender As Object, ByVal args As ZJCheckResultArgs)
        Dim item As MultiThreadDownloadItem = CType(sender, MultiThreadDownloadItem)
        Dim row As tbDataGridViewRow = Me.GetRowByIdentifier(item.ItemInfo.Identifier, item.ItemInfo.ItemId)
        If row Is Nothing Then
            Return
        End If

        Me.ChangeDownloadState(row, args.DownloadStatus, item)
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnRepair_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRepair.Click
        If Me.miPhoneAuthorizeHelper Is Nothing Then
            Me.InitiPhoneAuthorizeHelper()
        End If

        If Me.miPhoneAuthorizeHelper IsNot Nothing Then
            Me.miPhoneAuthorizeHelper.Authorize(True)
        End If
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        Me.DoRefresh()
    End Sub

    Private Sub DoRefresh()
        If PluginLogin.Instance().IsLogin Then
            Me.mIsRefresh = True

            If Not Me.bgwWealth.IsBusy Then
                Me.bgwWealth.RunWorkerAsync(True)
            End If
        End If
    End Sub

    Private Sub dgvUpdate_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Select Case e.CellButton
            Case CellButtonStyle.OneButton, CellButtonStyle.OneRetryButton
                Me.RetryUpdate(e)

                'Case CellButtonStyle.Info
                '    '跳转iTunes链接
                '    Dim pInfo As PackageInfo = CType(Me.dgvUpdate.Rows(e.Cell.RowIndex).Tag, PackageInfo)
                '    Me.tipDgvCell.Hide(Me)

            Case CellButtonStyle.Cancel
                Dim row As tbDataGridViewRow = e.Cell.OwningRow
                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")

                If cellProgress.Tag = DownloadStatus.Downloading Then
                    '取消下载
                    Dim cellOthers As tbDataGridViewTextBoxCell = row.Cells("colOther")
                    Dim item As MultiThreadDownloadItem = CType(cellOthers.Tag, MultiThreadDownloadItem)
                    Me.mDownloadManage.Stop(item)

                ElseIf cellProgress.Tag = DownloadStatus.Transfering OrElse cellProgress.Tag = DownloadStatus.Waiting Then
                    '取消安装
                    Dim pInfo As PackageInfo = CType(Me.dgvUpdate.Rows(e.Cell.RowIndex).Tag, PackageInfo)
                    Me.mInstallHelper.CancelInstallApp(pInfo)
                End If

            Case CellButtonStyle.Failed
                If CType(e.Cell, tbDataGridViewProgressCellEx).tbText = Me.Language.GetString("Common.Retry") Then
                    Me.RetryUpdate(e)
                Else
                    Dim url As String = String.Format("http://www.tongbu.com/apperr.html", Me.Language.CurrentLanguage.LangName)
                    Common.OpenExplorer(url)
                End If

            Case CellButtonStyle.Reinstall
                Dim row As tbDataGridViewRow = e.Cell.OwningRow
                Me.DoInstall(row)
        End Select
    End Sub

    Private Sub DoInstall(ByVal row As tbDataGridViewRow)
        Dim packInfo As UpdatePackageInfo = row.Tag

        If File.Exists(packInfo.PackagePath) Then
            packInfo.wCheck2Install = True
            Me.Start2CheckAppInstall(packInfo)
        Else
            'TODO 文件不存在
            Me.ChangeDownloadState(row, DownloadStatus.FileNotExist)
        End If
    End Sub

    Private Sub dgvUpdate_GroupTextClick(ByVal sender As Object, ByVal args As System.Windows.Forms.MouseEventArgs)

        Dim rowCurrent As tbDataGridViewRow = CType(sender, tbDataGridViewRow)

        Dim lstRows As List(Of DataGridViewRow) = rowCurrent.Group.Rows()

        If Me.CheckNoDevice() Then
            Return
        End If

        For Each row As tbDataGridViewRow In lstRows
            If row.isGroup Then
                Continue For
            End If

            Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
            If Not (cell.Tag Is Nothing OrElse _
                    cell.Tag = DownloadStatus.Nomal OrElse _
                    cell.Tag = DownloadStatus.DownloadFailed OrElse _
                    cell.Tag = DownloadStatus.DownloadSucceed OrElse _
                    cell.Tag = DownloadStatus.InstallUnSucceed) Then
                Continue For
            End If

            Dim pInfo As UpdatePackageInfo = CType(row.Tag, UpdatePackageInfo)

            If Not iPhoneInstallHelper.GetInstance(Me.mDevice).CheckPackageFitDeviceProductType(pInfo.UIDeviceFamily) Then
                Continue For
            End If

            'Debug.Print(pInfo.Name & "," & pInfo.wDownloadUrl)
            Dim strPkgPath As String = Me.mWealthHelper.GetFileDownloadPath(pInfo, Me.FunctionMappingKey)

            '文件存在则直接安装（只安装到当前设备）
            '文件不存在则下载（下载完，安装到连接的所有设备）
            If File.Exists(strPkgPath) Then
                If Not Me.CheckNoDevice() Then
                    Me.DoInstall(row)
                End If
            Else
                pInfo.wIsNewAreaApp = IIf(Me.FunctionMappingKey = FunctionKey.IOSZHUANQU, False, True)
                Me.DoDownloadSoft(pInfo)
            End If
        Next


        'For index As Integer = lstRows.Count - 1 To 0 Step -1
        '    Dim row As tbDataGridViewRow = lstRows(index)
        '    If row.isGroup Then
        '        Continue For
        '    End If

        '    Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        '    If Not (cell.Tag Is Nothing OrElse _
        '            cell.Tag = DownloadStatus.Nomal OrElse _
        '            cell.Tag = DownloadStatus.DownloadFailed OrElse _
        '            cell.Tag = DownloadStatus.DownloadSucceed OrElse _
        '            cell.Tag = DownloadStatus.InstallUnSucceed) Then
        '        Continue For
        '    End If

        '    Dim pInfo As UpdatePackageInfo = CType(row.Tag, UpdatePackageInfo)

        '    If Not Me.CheckNoDevice() AndAlso Not iPhoneInstallHelper.GetInstance(Me.mDevice).CheckPackageFitDeviceProductType(pInfo.UIDeviceFamily) Then
        '        Continue For
        '    End If

        '    'Debug.Print(pInfo.Name & "," & pInfo.wDownloadUrl)
        '    Dim strPkgPath As String = Me.mWealthHelper.GetFileDownloadPath(pInfo, Me.FunctionMappingKey)

        '    '文件存在则直接安装（只安装到当前设备）
        '    '文件不存在则下载（下载完，安装到连接的所有设备）
        '    If File.Exists(strPkgPath) Then
        '        If Not Me.CheckNoDevice() Then
        '            Me.DoInstall(row)
        '        End If
        '    Else
        '        pInfo.wIsNewAreaApp = IIf(Me.FunctionMappingKey = FunctionKey.IOSZHUANQU, False, True)
        '        Me.DoDownloadSoft(pInfo)
        '    End If
        'Next


    End Sub

    'Private Sub dgvUpdate_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)
    '    Dim row As tbDataGridViewRow = Me.dgvUpdate.Rows(e.Cell.RowIndex)
    '    Dim pos As Point = Windows.Forms.Cursor.Position
    '    Dim secPos As Point = Me.PointToClient(pos)
    '    Dim newPos As New Point(secPos.X + 10, secPos.Y + 10)

    '    Select Case e.CellButton
    '        Case CellButtonStyle.Info
    '            Me.tipDgvCell.Show(Me.Language.GetString("App.Tip.AppDetail"), Me, newPos) '查看软件介绍
    '            'Case CellButtonStyle.Ignore
    '            '    Me.tipDgvCell.Show(Me.Language.GetString("Common.Ignore"), Me, newPos) '"忽略"
    '        Case CellButtonStyle.None
    '            Me.tipDgvCell.Hide(Me)
    '    End Select

    'End Sub

    'Private Sub dgvUpdate_GroupTextClick(ByVal sender As Object, ByVal args As System.Windows.Forms.MouseEventArgs)
    '    Dim rowCurrent As tbDataGridViewRow = CType(sender, tbDataGridViewRow)
    '    Me.DoAllUpdate(rowCurrent.Group.Rows)
    'End Sub

    'Private Sub DoAllUpdate(ByVal lstRows As List(Of DataGridViewRow))
    '    Dim lstUpdate As New List(Of DataGridViewRow)

    '    For index As Integer = 0 To lstRows.Count - 1
    '        If Not lstRows(index).Visible Then
    '            Continue For
    '        End If

    '        '过滤正在下载或安装的软件
    '        Dim cell As tbDataGridViewProgressCellEx = lstRows(index).Cells("colProgress")
    '        If Not (cell.Tag Is Nothing OrElse cell.Tag = DownloadStatus.Nomal OrElse cell.Tag = DownloadStatus.DownloadFailed) Then
    '            Continue For
    '        End If

    '        lstUpdate.Add(lstRows(index))
    '    Next

    '    If lstUpdate.Count = 0 Then
    '        '"没有需要升级的软件。"
    '        tbMessageBox.Show(Me.Language.GetString("App.Message.NoAppForUpdate"), _
    '                        Me.Language.GetString("Common.Info"), _
    '                        MessageBoxButtons.OK, _
    '                        MessageBoxIcon.Information)
    '        Return
    '    Else
    '        Debug.Print("lstUpdate count:" & lstUpdate.Count)
    '    End If

    '    Dim isDownload As Boolean = True
    '    Dim strLoginAppleId As String = String.Empty
    '    For index As Integer = 0 To lstUpdate.Count - 1
    '        '用户取消下载
    '        If Not isDownload Then
    '            Exit For
    '        End If

    '        isDownload = Me.DoUpdate(lstUpdate(index), strLoginAppleId)

    '        Dim uInfo As UpdatePackageInfo = CType(lstUpdate(index).Tag, UpdatePackageInfo)
    '        If strLoginAppleId.Length = 0 Then
    '            strLoginAppleId = uInfo.LoginAppleId
    '        End If
    '    Next

    '    lstUpdate.Clear()
    'End Sub

    'Private Sub tsmiUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiUpdate.Click
    '    If Me.dgvUpdate.SelectedRows Is Nothing Then
    '        Return
    '    End If

    '    Dim row As tbDataGridViewRow = Me.dgvUpdate.SelectedRows(0)
    '    Me.DoUpdate(row)
    'End Sub

    'Private Function DoUpdate(ByVal row As tbDataGridViewRow, Optional ByVal strLoginAppleId As String = "") As Boolean
    '    Dim isDownload As Boolean = True
    '    Dim uInfo As UpdatePackageInfo = CType(row.Tag, UpdatePackageInfo)
    '    If strLoginAppleId.Length > 0 Then
    '        uInfo.LoginAppleId = strLoginAppleId
    '    End If

    '    '标志正在等待升级
    '    Me.ChangeDownloadState(row, DownloadStatus.Waiting)

    '    '设备升级
    '    If Me.mDevice IsNot Nothing Then
    '        uInfo.DeviceId = Me.mDevice.DeviceID    '标志安装到哪个设备
    '    End If

    '    '如果本地已经存在软件，则直接安装
    '    Dim pInfo As PackageInfo = iPhoneDeviceHelper.GetPackageInfoFromLocalPackages(Me.mApplication, uInfo.Identifier, False, uInfo.Version)
    '    If pInfo IsNot Nothing Then
    '        Dim infoRow As PackageInfo = row.Tag
    '        infoRow.PackagePath = pInfo.PackagePath
    '        Me.DoInstall(row)

    '        Return isDownload
    '    End If

    '    '标志此软件是升级的
    '    uInfo.IsUpdate = True

    '    '下载
    '    isDownload = Me.mCoreUpdate.DownloadAppleSoft(uInfo)
    '    If Not isDownload Then
    '        uInfo.LoginAppleId = ""
    '    End If

    '    Return isDownload
    'End Function

    'Private Sub tsmiGoToiTunes_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiGoToiTunes.Click
    '    If Me.dgvUpdate.SelectedRows Is Nothing Then
    '        Return
    '    End If

    '    '跳转iTunes链接
    '    Dim row As tbDataGridViewRow = Me.dgvUpdate.SelectedRows(0)
    '    Dim pInfo As PackageInfo = CType(row.Tag, PackageInfo)
    'End Sub

    'Private Sub llblLogin_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles llblLogin.LinkClicked
    '    WealthHelper.Instance().Login(CType(Me.mApplication, MainForm))
    'End Sub

    'Protected Overridable Function GetPackageInfoFromLocalPackages(ByVal uInfo As UpdatePackageInfo) As PackageInfo
    '    Return iPhoneDeviceHelper.GetPackageInfoFromLocalPackages(Me.mApplication, uInfo.Identifier, False, uInfo.Version)
    'End Function

    Private Sub btnDownloadBatch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDownloadBatch.Click
        Me.btnDownloadBatch.Enabled = False
        Me.DoDownloadBatch()
        Me.btnDownloadBatch.Enabled = True
    End Sub

    Private Sub btnOpenDownloadFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOpenDownloadFolder.Click
        Common.OpenExplorer(IIf(Me.FunctionMappingKey = FunctionKey.IOSNEWDEVICE, Folder.DownloadNewAreaFolder, Folder.DownloadFolder))
    End Sub

    Private Sub btnLocalPackageFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLocalPackageFolder.Click
        mFrmSetLocalPackageFolder.StartPosition = FormStartPosition.CenterParent
        mFrmSetLocalPackageFolder.ShowDialog(Me)
        LoadLoaclApp(True)
    End Sub

    Private Sub btnCourse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCourse.Click
        Dim frm As New frmActivationHelp(frmActivationHelp.View.CourseGetDeviceInfo, False)
        frm.Show(Me)
    End Sub

    Private Sub chkJudgeInstall_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkJudgeInstall.CheckedChanged
        IniSetting.SetJudgeInstalled(Me.chkJudgeInstall.Checked)

        RaiseEvent JudgeInstallChangedEventHandler(Me, New ValueChangeArgs(Me.chkJudgeInstall.Checked))
    End Sub

    Private Sub chkScore_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkScore.CheckedChanged
        Me.SetPriceColumnVisible()
        IniSetting.SetShowScore(Me.chkScore.Checked)
    End Sub

    Private Sub SetPriceColumnVisible()
        Me.dgvUpdate.Columns("colPrice").Visible = Me.chkScore.Checked
    End Sub

    Private Sub llblLogin_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles llblLogin.LinkClicked

        If Not PluginLogin.Instance.CheckLogin Then
            frmLogin.ShowLogin(Nothing)
            Return
        End If

    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Sub SetProgressStateWithConnect(ByVal value As Boolean)
        Try
            If value Then
                If Me.bgwGetDeviceApp.IsBusy Then
                    Return
                End If

                If Not Me.bgwGetDeviceApp.IsBusy Then
                    Me.bgwGetDeviceApp.RunWorkerAsync()
                End If

                Common.Log("获取设备上的软件信息")

            Else


                Common.Log("设备的软件信息清空")

                '变换下载完成的按钮状态
                Dim lstRows As New List(Of tbDataGridViewRow)
                For Each row As tbDataGridViewRow In Me.dgvUpdate.Rows
                    lstRows.Add(row)
                Next

                For index As Integer = lstRows.Count - 1 To 0 Step -1
                    Dim row As tbDataGridViewRow = lstRows(index)
                    If row.isGroup Then
                        Continue For
                    End If

                    Dim pInfo As UpdatePackageInfo = CType(row.Tag, UpdatePackageInfo)
                    'Debug.Print(pInfo.Name)

                    Dim isExist As Boolean = Me.mWealthHelper.CheckFileExist(pInfo, Me.FunctionMappingKey)
                    Dim state As DownloadStatus = DownloadStatus.Nomal
                    If isExist Then
                        state = DownloadStatus.DownloadSucceed
                    End If
                    Me.ChangeDownloadState(row, state)
                Next

                lstRows.Clear()
                lstRows = Nothing

                'Me.ShowAppInfo()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetProgressStateWithConnect")
        End Try
    End Sub

    Private Sub bgwGetDeviceApp_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwGetDeviceApp.DoWork
        Try
            Dim dictInstallApps As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User, Me.mIsRefresh)
            If dictInstallApps IsNot Nothing AndAlso dictInstallApps.Count = 0 Then
                Debug.Print("获取软件数量为空，等待重新获取")
                Utility.WaitSeconds(0.5)
                dictInstallApps = Me.mDevice.InstalledApplications(ApplicationType.User, Me.mIsRefresh)
            End If

            e.Result = dictInstallApps
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub ChangeRowDelegate(ByVal isAdd As Boolean, ByVal row As tbDataGridViewRow)
    Private Sub ChangeRow(ByVal isAdd As Boolean, ByVal row As tbDataGridViewRow)
        If Me.InvokeRequired Then
            Me.Invoke(New ChangeRowDelegate(AddressOf ChangeRow), isAdd, row)
        Else
            If isAdd Then
                Me.dgvUpdate.Rows.Add(row)
            Else
                Me.dgvUpdate.Rows.Remove(row)
            End If
        End If
    End Sub

    Private Sub bgwGetDeviceApp_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwGetDeviceApp.RunWorkerCompleted
        Try
            Me.mIsRefresh = False
            Dim dictInstallApps As Dictionary(Of String, FileSharingPackageInfo) = CType(e.Result, Dictionary(Of String, FileSharingPackageInfo))
            If dictInstallApps Is Nothing Then
                Return
            End If

            'Debug.Print(dictInstallApps.Count)

            If Me.mInstallHelper Is Nothing Then
                Me.InitiPhoneInstallHelper()
            End If

            Dim lstRows As New List(Of tbDataGridViewRow)
            For Each row As tbDataGridViewRow In Me.dgvUpdate.Rows
                If row.isGroup Then
                    Continue For
                End If

                lstRows.Add(row)
            Next

            For index As Integer = lstRows.Count - 1 To 0 Step -1
                Dim pInfo As UpdatePackageInfo = CType(lstRows(index).Tag, UpdatePackageInfo)

                If iPhoneDeviceHelper.CheckPackageIsInstalled(pInfo, dictInstallApps) Then
                    '已经安装
                    Dim cell As tbDataGridViewProgressCellEx = lstRows(index).Cells("colProgress")
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "已安装"
                    cell.Tag = DownloadStatus.InstallSucceed

                    Continue For
                End If

                If SaleInfoHelper.GetInstance(Me.mDevice).IsNewDevice = False AndAlso Me.FunctionMappingKey = FunctionKey.IOSNEWDEVICE Then
                    '不是新设备
                    Dim cell As tbDataGridViewProgressCellEx = lstRows(index).Cells("colProgress")
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "该频道无法适用于该设备"
                    cell.Tag = DownloadStatus.InstallSucceed

                    Continue For
                End If


                If Not iPhoneInstallHelper.GetInstance(Me.mDevice).CheckPackageFitDeviceProductType(pInfo.UIDeviceFamily) Then
                    '不适合当前设备
                    Dim cell As tbDataGridViewProgressCellEx = lstRows(index).Cells("colProgress")
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "不适用于您的设备"
                    cell.Tag = DownloadStatus.InstallSucceed

                    'If Me.FunctionMappingKey <> FunctionKey.LocalUpdate Then
                    '    Me.ChangeRow(False, lstRows(index))
                    '    Me.ChangeRow(True, lstRows(index))
                    'End If

                    Continue For
                End If

                If Me.mDevice IsNot Nothing AndAlso Not Me.mDevice.Jailbreaked AndAlso pInfo.CrackedInfo = CrakedInfo.Craked Then
                    '设备未越狱，无法安装
                    Dim cell As tbDataGridViewProgressCellEx = lstRows(index).Cells("colProgress")
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "设备未越狱"
                    cell.Tag = DownloadStatus.InstallSucceed

                    Continue For
                End If

                Dim isCrack As Boolean = True
                If pInfo.CrackedInfo = CrakedInfo.UnCraked Then
                    isCrack = False
                End If
                Dim isExist As Boolean = Me.mWealthHelper.CheckFileExist(pInfo, Me.FunctionMappingKey)
                Dim state As DownloadStatus = DownloadStatus.Nomal

                If isExist Then
                    state = DownloadStatus.DownloadSucceed
                End If
                Me.ChangeDownloadState(lstRows(index), state)
            Next

            lstRows.Clear()
            lstRows = Nothing

            'Me.ShowAppInfo()

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Sub SwitchViewInCheckUpdate(ByVal value As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SwitchViewInCheckUpdateHandler(AddressOf SwitchViewInCheckUpdate), value)
        Else
            Me.btnRefresh.Enabled = Not value

            Dim key As FunctionKey = Me.FunctionMappingKey
            If Me.mIsDeviceUpdate Then
                key = FunctionKey.DeviceUpdate
            End If

            Dim view As ViewStyle = ViewStyle.Loading
            If Not value Then
                view = ViewStyle.Nomal
            End If
            Me.SetViewStyle(view)

            If Not value Then
                If Me.InitCompleted Then
                    Me.ChangeNodeCount()
                    'Else
                    'Me.ChangeNodeCount(Me.GetUpdateCountExceptIgnore())
                End If
            End If
        End If
    End Sub

    Private Delegate Sub SetViewStyleHandler(ByVal view As ViewStyle)
    Protected Sub SetViewStyle(ByVal view As ViewStyle)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetViewStyleHandler(AddressOf SetViewStyle), view)
            Else
                Me.tpnlContent.SuspendLayout()

                Select Case view
                    Case ViewStyle.Nomal
                        Me.tpnlContent.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(0).Width = 100
                        Me.tpnlContent.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(1).Width = 0
                        Me.tpnlContent.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(2).Width = 0

                        'Me.SetStatePanelVisible(False, "")

                    Case ViewStyle.NoLogin
                        Me.tpnlContent.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(0).Width = 0
                        Me.tpnlContent.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(1).Width = 0
                        Me.tpnlContent.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(2).Width = 100

                    Case ViewStyle.Loading
                        Me.tpnlContent.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(0).Width = 0
                        Me.tpnlContent.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(1).Width = 100
                        Me.tpnlContent.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tpnlContent.ColumnStyles(2).Width = 0
                End Select

                Me.tpnlContent.ResumeLayout()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "SetViewStyle")
        End Try
    End Sub

    Private Delegate Sub SetStatePanelVisibleHandler(ByVal visible As Boolean, ByVal text As String)
    Private Sub SetStatePanelVisible(ByVal visible As Boolean, ByVal text As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetStatePanelVisibleHandler(AddressOf SetStatePanelVisible), visible, text)
        Else
            If visible Then
                Me.lblState.Text = text
                If Me.tpnlContent.RowStyles(2).Height <> 30 Then
                    Me.tpnlContent.RowStyles(2).SizeType = SizeType.Absolute
                    Me.tpnlContent.RowStyles(2).Height = 30
                End If
            Else
                Me.tpnlContent.RowStyles(2).SizeType = SizeType.Absolute
                Me.tpnlContent.RowStyles(2).Height = 0
            End If
        End If
    End Sub

    Private Sub ChangeAllCount()
        '改变btnIngore的Text
        'Me.btnIngore.Text = String.Format(Me.Language.GetString("App.Button.IgnoreNumber") & "({0})", Me.mlstUpdateIgnore.Count) '"Ignore({0})"

        '改变node 的数量
        Me.ChangeNodeCount()
    End Sub

    Private Sub ChangeNodeCount()
        Dim key As FunctionKey = Me.FunctionMappingKey
        If Me.mIsDeviceUpdate Then
            key = FunctionKey.DeviceUpdate
        End If

        If Me.mFrmApp IsNot Nothing Then
            Me.mFrmApp.UpdateNodeNumber(key, Me.mlstUpdateSoftResult.Count - Me.mlstUpdateIgnore.Count)
        End If
    End Sub

    Private Sub ChangeNodeCount(ByVal count As Integer)
        Dim key As FunctionKey = Me.FunctionMappingKey
        If Me.mIsDeviceUpdate Then
            key = FunctionKey.DeviceUpdate
        End If

        If Me.mFrmApp IsNot Nothing Then
            Me.mFrmApp.UpdateNodeNumber(key, count)
        End If
    End Sub

    Private Function GetRowByIdentifier(ByVal identifer As String, ByVal itemID As String) As tbDataGridViewRow
        Dim row As tbDataGridViewRow = Nothing
        If identifer.Length = 0 Then
            Return row
        End If

        For Each item As tbDataGridViewRow In Me.dgvUpdate.Rows
            If item.isGroup Then
                Continue For
            End If

            Dim package As UpdatePackageInfo = CType(item.Tag, UpdatePackageInfo)

            If String.Compare(package.Identifier, identifer, True) = 0 Then
                row = item

                If String.Compare(itemID, "-1", True) = 0 Then
                    If package.CrackedInfo = CrakedInfo.UnCraked Then
                        Exit For
                    End If
                Else
                    If package.CrackedInfo <> CrakedInfo.UnCraked Then
                        Exit For
                    End If
                End If

            End If
        Next

        Return row
    End Function

    Private Function GetRowByFilePath(ByVal filePath As String) As tbDataGridViewRow
        Dim row As tbDataGridViewRow = Nothing
        Try
            If filePath.Length = 0 Then
                Return row
            End If

            For Each item As tbDataGridViewRow In Me.dgvUpdate.Rows
                If item.isGroup Then
                    Continue For
                End If

                Dim package As UpdatePackageInfo = CType(item.Tag, UpdatePackageInfo)
                'Debug.Print(package.Identifier)

                If String.Compare(package.PackagePath, filePath, True) = 0 Then
                    row = item
                    Exit For

                    'If version.Length = 0 AndAlso VersionId < 1 Then
                    '    row = item
                    '    Exit For
                    'End If

                    ''如果有传入版本号，则比较版本号
                    'If VersionId > 0 Then
                    '    If package.VersionId = VersionId Then
                    '        row = item
                    '        Exit For
                    '    End If
                    'End If

                    ''如果有版本，则比较版本
                    'If version.Length > 0 Then
                    '    If String.Compare(package.Version, version, True) = 0 Then
                    '        row = item
                    '        Exit For
                    '    End If
                    'End If
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetRowByFilePath")
        End Try

        Return row
    End Function

    Private Function GetGroupTemplate() As Object()
        Return New Object() {False, _
                                          "", _
                                          "", _
                                          "", _
                                          "", _
                                          "", _
                                          "", _
                                          "", _
                                          ""}
    End Function

    'Private Function GetUpdateCountExceptIgnore() As Integer
    '    Try
    '        '从数据库获取忽略列表
    '        Dim lstIgnore As List(Of String) = AppDeviceDB.GetInstance.Get_Ignore_Package_Info(Me.mDeviceID)
    '        Dim count As Integer = 0

    '        For Each item As UpdatePackageInfo In Me.mlstUpdateSoftResult
    '            If Not lstIgnore.Contains(item.Identifier) Then
    '                count = count + 1
    '            End If
    '        Next

    '        lstIgnore.Clear()
    '        lstIgnore = Nothing

    '        Return count
    '    Catch ex As Exception
    '        Debug.Print(ex.ToString)
    '    End Try
    'End Function

    Private Function CheckIsCaifuView() As Boolean
        Dim isValue As Boolean = False

        Select Case Me.FunctionMappingKey
            Case FunctionKey.IOSNEWDEVICE, FunctionKey.IOSZHUANQU
                isValue = True
        End Select

        Return isValue
    End Function

#End Region

#Region "--- 公有方法 ---"

    'Public Sub ShowItemByIdentifier(ByVal identifier As String)
    '    Dim row As tbDataGridViewRow = Me.GetRowByIdentifier(identifier)

    '    If row Is Nothing Then
    '        Return
    '    End If

    '    If Not row.Visible Then
    '        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
    '        Dim package As New UpdatePackageInfo
    '        package = row.Tag

    '        If AppDeviceDB.GetInstance.Remove_Ignore_Package_Info(identifier, Me.mDeviceID) Then
    '            If Me.mlstUpdateIgnore.Contains(package) Then
    '                Me.mlstUpdateIgnore.Remove(package)
    '            End If
    '            row.Visible = True
    '            Me.ChangeAllCount()
    '        End If
    '    End If
    'End Sub

#End Region

#Region "--- 进度反馈 ---"

#Region "--- 下载进度 ---"

    Private Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Me.ChangeDownloadState(item, DownloadStatus.Waiting)
    End Sub

    Private Sub OnTaskUpdate(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
    End Sub

    Private Sub OnTaskStart(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Me.ChangeDownloadState(item, DownloadStatus.Waiting)
    End Sub

    Private Sub OnTaskDelete(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Me.ChangeDownloadState(item, DownloadStatus.Nomal)
    End Sub

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        Me.ChangeDownloadState(item, DownloadStatus.Downloading, e.Progress)
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        If item.ItemInfo.Status = TaskState.Completed Then
            '安装
            Me.ChangeDownloadState(item, DownloadStatus.DownloadSucceed)

        ElseIf item.ItemInfo.IsDeleted Then
            '删除
            Me.ChangeDownloadState(item, DownloadStatus.DownloadDeleted)

        ElseIf e.Cancel AndAlso item.ItemInfo.Status <> TaskState.Completed Then
            '取消
            Me.ChangeDownloadState(item, DownloadStatus.DownloadCancel)

        Else
            '下载失败
            Me.ChangeDownloadState(item, DownloadStatus.DownloadFailed)
        End If
    End Sub

    Private Sub OnTaskHasDownloadSucceed(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        '安装
        Me.ChangeDownloadState(item, DownloadStatus.DownloadSucceed)

        'Debug.Print("开始下载：" & Me.mDevice.DeviceName)
    End Sub

#End Region

#Region "--- 安装进度 ---"

    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        Try
            If args.PackageInfo Is Nothing Then
                Return
            End If

            Dim row As tbDataGridViewRow = Me.GetRowByFilePath(args.PackageInfo.PackagePath)
            If row Is Nothing Then
                Return
            End If

            Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
            cell.tbForColor = Color.Black

            Select Case args.InstallState
                Case InstallState.Nomal

                Case InstallState.AuthBegin
                    cell.Enabled = True
                    cell.tbShowCancelButton = True
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "正在授权"
                    cell.Tag = DownloadStatus.Waiting

                Case InstallState.Waiting
                    cell.Enabled = True
                    cell.tbShowCancelButton = True
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = Me.Language.GetString("App.Cell.WaitingInstall") '"Waiting Install"
                    cell.Tag = DownloadStatus.Waiting

                Case InstallState.LackExecutableOrUrlSchemes
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = args.ErrorMsg
                    cell.Tag = DownloadStatus.Nomal
                    cell.tbForColor = Color.Red

                Case InstallState.Transfering
                    cell.Enabled = True
                    cell.tbShowCancelButton = True
                    cell.tbProgressStyle = ProgressStyle.Progress
                    cell.tbProgressValue = args.TransferProgress
                    cell.tbText = Me.Language.GetString("File.Label.Uploading") '"Uploading"
                    cell.Tag = DownloadStatus.Transfering

                Case InstallState.Installing
                    cell.Enabled = False
                    cell.tbShowCancelButton = False
                    If args.InstallApplicationEventArgs IsNot Nothing Then
                        Select Case args.InstallApplicationEventArgs.InstallProgress
                            Case InstallapplicationProgress.OnUnzip, _
                                 InstallapplicationProgress.OnRestoreContainer
                                cell.tbProgressStyle = ProgressStyle.UnsureProgress
                                cell.tbText = Me.Language.GetString("App.Cell.Restoring") '"Restoring"

                            Case InstallapplicationProgress.OnInstall
                                cell.tbProgressStyle = ProgressStyle.Progress
                                cell.tbProgressValue = 0
                                cell.tbText = Me.Language.GetString("App.Cell.Installing") '"Installing"
                        End Select
                    Else
                        cell.tbProgressStyle = ProgressStyle.Progress
                        cell.tbProgressValue = args.TransferProgress
                        cell.tbText = Me.Language.GetString("App.Cell.Installing") ' "Installing"
                    End If


                Case InstallState.Succeed
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = Me.Language.GetString("App.Cell.InstallSucceed") '"安装成功"

                Case InstallState.HaveNotInstall
                    cell.Enabled = False
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = args.ErrorMsg
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.UnSucceed
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Failed
                    cell.tbTextFail = Me.Language.GetString("Download.Label.Failed") ' "Fail"
                    cell.tbText = args.ErrorMsg
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.AuthFail
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Reinstall
                    cell.tbText = "授权失败"
                    cell.tbTextReInstall = Me.Language.GetString("App.Button.Reinstall") '"ReInstall"
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.CancelInstall
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.OneButton
                    cell.tbText = Me.Language.GetString("App.Button.Install") ' "Install"
                    cell.Tag = DownloadStatus.DownloadSucceed

                Case InstallState.AlreadyInstall
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.OneButton
                    cell.tbText = Me.Language.GetString("App.Button.Reinstall") ' "ReInstall"
                    cell.Tag = DownloadStatus.DownloadSucceed

                Case InstallState.AllInstallCompleted

                Case InstallState.CannotGetOpenUDIDInfo
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Reinstall
                    cell.tbText = "请打开【苹果游戏商店】获取设备信息，" & vbCrLf & "完成后会自动安装"
                    cell.tbTextReInstall = Me.Language.GetString("App.Button.Reinstall") '"ReInstall"
                    cell.tbForColor = Color.FromArgb(0, 133, 218)
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.Need2AppstoreGetInfo
                    cell.Enabled = True
                    cell.tbShowCancelButton = True
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "正在获取设备信息"
                    cell.Tag = DownloadStatus.Waiting

                Case InstallState.SettingError
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "该软件上线过程中出错，请联系客服"
                    cell.tbForColor = Color.Red
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.IOS7CannotInstall
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "该广告主不支持iOS7.x以上的固件"
                    cell.tbForColor = Color.Red
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.IOS6CannotInstall
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "该广告主不支持iOS6.x的固件"
                    cell.tbForColor = Color.Red
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.IOS5CannotInstall
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "该广告主不支持iOS5.x的固件"
                    cell.tbForColor = Color.Red
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.IOS4CannotInstall
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "该广告主不支持iOS5.x以下的固件"
                    cell.tbForColor = Color.Red
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.PostRealtimeDataFailed
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "实时软件订阅失败"
                    cell.tbForColor = Color.Red
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.PostRealtimeDataWIDNULL
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "实时软件ID为空"
                    cell.tbForColor = Color.Red
                    cell.Tag = DownloadStatus.InstallUnSucceed

                Case InstallState.CheckInstalling
                    '正在校验
                    Me.ChangeDownloadState(args.PackageInfo.Identifier, "", DownloadStatus.JudgeExistBefore)

                Case InstallState.CheckInstalled
                    'web返回存在
                    Me.ChangeDownloadState(args.PackageInfo.Identifier, "", DownloadStatus.JudgeExisted)

                Case InstallState.CheckNotInstalled
                    'web返回不存在
                    Me.ChangeDownloadState(args.PackageInfo.Identifier, "", DownloadStatus.JudgeNotExist)
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Sub OnUpdateInstallCountEventHandler(ByVal sender As Object, ByVal args As UpdateInstallCountArgs)
        Try
            If mApplication IsNot Nothing AndAlso args IsNot Nothing AndAlso Me.mDevice IsNot Nothing Then
                Me.mApplication.SetItemNumber(FunctionKey.App, args.InstallCount, Me.mDevice.DeviceID)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnUpdateInstallCountEventHandler")
        End Try
    End Sub

#End Region

    Private Sub ChangeDownloadState(ByVal row As tbDataGridViewRow, _
                                                                    ByVal state As DownloadStatus, _
                                                                    Optional ByVal item As MultiThreadDownloadItem = Nothing, _
                                                                    Optional ByVal iProgress As Integer = 0)

        Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        cell.tbForColor = Color.Black

        Select Case state
            Case DownloadStatus.Waiting
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = Me.Language.GetString("Download.Label.Waiting") '"Waitting"
                cell.Tag = DownloadStatus.Waiting

            Case DownloadStatus.Downloading
                Dim cellOthers As tbDataGridViewTextBoxCell = row.Cells("colOther")

                '下载的ID不一样的时候也要修改。不然开始下载后删除任务就无法取消下载了。
                If cellOthers.Tag Is Nothing OrElse item.ItemInfo.TaskID <> CType(cellOthers.Tag, MultiThreadDownloadItem).ItemInfo.TaskID Then
                    '以便取消下载的时候可以获取到这个数据
                    cellOthers.Tag = item
                End If

                cell.Enabled = True
                cell.tbShowCancelButton = True
                cell.tbProgressStyle = ProgressStyle.Progress
                cell.tbProgressValue = iProgress
                cell.tbText = Me.Language.GetString("App.Cell.Downloading") '"Downloading"
                cell.Tag = DownloadStatus.Downloading

            Case DownloadStatus.Nomal, DownloadStatus.DownloadCancel, DownloadStatus.DownloadDeleted
                cell.tbText = "下载安装"
                cell.tbProgressStyle = iTong.ProgressStyle.OneButton
                cell.tbShowCancelButton = False
                cell.tbShowIgnoreButton = False
                cell.Tag = DownloadStatus.Nomal

                Dim info As PackageInfo = CType(row.Tag, PackageInfo)
                info.LoginAppleId = ""

            Case DownloadStatus.DownloadFailed
                cell.tbProgressStyle = iTong.ProgressStyle.OneButton
                cell.tbShowCancelButton = False
                cell.tbShowIgnoreButton = False
                cell.tbText = Me.Language.GetString("Common.Retry") '"Retry"
                cell.Tag = DownloadStatus.Nomal

                Dim info As PackageInfo = CType(row.Tag, PackageInfo)
                info.LoginAppleId = ""
                cell.tbForColor = Color.Red

            Case DownloadStatus.DownloadSucceed
                If Not Me.CheckNoDevice() Then
                    cell.tbProgressStyle = iTong.ProgressStyle.OneButton
                    cell.tbShowCancelButton = False
                    cell.tbShowIgnoreButton = False
                    cell.tbText = Me.Language.GetString("App.Button.Install") '"Install"
                    cell.Tag = DownloadStatus.DownloadSucceed
                Else
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = "等待设备连接"
                    cell.Tag = DownloadStatus.DownloadSucceed
                End If

                '下载完成给界面赋值
                If item IsNot Nothing Then
                    Dim info As UpdatePackageInfo = CType(row.Tag, UpdatePackageInfo)
                    info.PackagePath = item.ItemInfo.FilePath
                    info.Identifier = item.ItemInfo.Identifier
                Else
                    Dim info As UpdatePackageInfo = CType(row.Tag, UpdatePackageInfo)
                    Dim isCrack As Boolean = True
                    If info.CrackedInfo = CrakedInfo.UnCraked Then
                        isCrack = False
                    End If
                    info.PackagePath = Me.mWealthHelper.GetFileDownloadPath(info, Me.FunctionMappingKey)
                End If

            Case DownloadStatus.FileNotExist
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = "文件不存在"
                cell.Tag = DownloadStatus.Nomal
                cell.tbForColor = Color.Red

            Case DownloadStatus.ComputeUrl
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = "正在获取下载地址，请稍候"
                cell.Tag = DownloadStatus.Waiting

            Case DownloadStatus.UrlError
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = "此链接已失效，无法下载"
                cell.Tag = DownloadStatus.Nomal
                cell.tbForColor = Color.Red

            Case DownloadStatus.JudgeExistBefore
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = "正在验证是否安装过 ..."
                cell.Tag = DownloadStatus.Nomal

            Case DownloadStatus.JudgeNotExist
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = "验证正常"
                cell.Tag = DownloadStatus.Nomal

            Case DownloadStatus.JudgeExisted
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = "该软件曾被安装过，不参与佣金结算"
                cell.Tag = DownloadStatus.Nomal
                cell.tbForColor = Color.Red

            Case DownloadStatus.HashError
                cell.Enabled = True
                cell.tbShowCancelButton = False
                'cell.tbProgressStyle = ProgressStyle.Text
                cell.tbProgressStyle = iTong.ProgressStyle.OneRetryButton
                cell.tbText = "重试"
                cell.tbTextFail = "下载的文件MD5与服务器不符"
                cell.Tag = DownloadStatus.Nomal
                cell.tbForColor = Color.Red



        End Select
    End Sub

    Private Sub ChangeDownloadState(ByVal item As MultiThreadDownloadItem, ByVal state As DownloadStatus, Optional ByVal iProgress As Integer = 0)
        If item.ItemInfo.Class <> ResourceClass.Software Then
            Return
        End If

        Dim row As tbDataGridViewRow = Me.GetRowByIdentifier(item.ItemInfo.Identifier, item.ItemInfo.ItemId)
        If row Is Nothing Then
            Return
        End If

        Me.ChangeDownloadState(row, state, item, iProgress)
    End Sub

    Private Sub ChangeDownloadState(ByVal identifier As String, ByVal version As String, ByVal state As DownloadStatus)
        Dim row As tbDataGridViewRow = Me.GetRowByIdentifier(identifier, version)
        If row Is Nothing Then
            Return
        End If

        Me.ChangeDownloadState(row, state, Nothing, 0)
    End Sub

#End Region

#Region "--- 界面变化 ---"

    Public Sub ChangeConnectState(ByVal device As IDevice)
        If Me.IsDisposed OrElse Not Me.IsHandleCreated Then
            Return
        End If

        If Me.InitCompleted Then
            Me.SetProgressStateWithConnect(True)
        End If
    End Sub

    Private Delegate Sub AddItemToDgvHandler(ByVal mlstPackages As List(Of UpdatePackageInfo), ByVal isLocal As Boolean)
    Private Sub AddItemToDgv(ByVal mlstPackages As List(Of UpdatePackageInfo), ByVal isLocal As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New AddItemToDgvHandler(AddressOf AddItemToDgv), mlstPackages, isLocal)
        Else
            Try
                Me.dgvUpdate.SuspendLayout()

                If Not isLocal Then
                    Me.dgvUpdate.Rows.Clear()
                    Common.Log("加载不是本地 清空所有数据")
                End If

                Dim isAddByGroup As Boolean = (Me.FunctionMappingKey = FunctionKey.IOSZHUANQU OrElse Me.FunctionMappingKey = FunctionKey.IOSNEWDEVICE)
                Dim strGroupFZ As String = ""
                If Me.FunctionMappingKey = FunctionKey.IOSZHUANQU Then
                    strGroupFZ = "iOS分组，依照激活条件进行激活"
                ElseIf Me.FunctionMappingKey = FunctionKey.IOSNEWDEVICE Then
                    strGroupFZ = "新机分组，依照激活条件进行激活"
                End If

                'Dim strGroupZhuce As String = "推荐分组，优质软件，依照激活条件进行激活"
                'Dim strGroupJihuo As String = "工具分组，依照激活条件进行激活"
                'Dim strGroupAnzhuang As String = "安装分组，免费软件不参与结算" '安装分组，安装后勿删除，留存至少7天，否则影响结算
                Dim strGroupLocal As String = mStrGroupLocalText

                Dim strGroupClick As String = "批量安装此分组"

                If isAddByGroup Then

                    If Not isLocal Then
                        Me.dgvUpdate.DictGroupKeys.Clear()

                        'Me.dgvUpdate.AddGroup(strGroupZhuce, Me.GetGroupTemplate(), strGroupClick, -1)
                        'Me.dgvUpdate.AddGroup(strGroupJihuo, Me.GetGroupTemplate(), strGroupClick, -1)
                        'Me.dgvUpdate.AddGroup(strGroupAnzhuang, Me.GetGroupTemplate(), strGroupClick, -1)
                        Me.dgvUpdate.AddGroup(strGroupFZ, Me.GetGroupTemplate(), strGroupClick, -1)
                        Me.dgvUpdate.AddGroup(strGroupLocal, Me.GetGroupTemplate(), strGroupClick, -1)

                    ElseIf Not Me.dgvUpdate.DictGroupKeys.ContainsKey(strGroupLocal) Then
                        Me.dgvUpdate.AddGroup(strGroupLocal, Me.GetGroupTemplate(), strGroupClick, -1)

                    End If

                End If

                Dim appDefaultIcon As Image = My.Resources.app_icon_default.Clone

                Dim iCount As Integer = 0
                Common.Log("应用个数" & mlstPackages.Count)

                For i As Integer = 0 To mlstPackages.Count - 1
                    Dim package As UpdatePackageInfo = mlstPackages(i)
                    Dim row As New tbDataGridViewRow()
                    row.Height = 58

                    Dim strName As String = package.Name
                    If package.wIsRealTime AndAlso Not strName.Contains("【实时】") Then
                        strName = String.Format("【实时】{0}", strName)
                    End If

                    '--------------
                    'Dim strtag As String = ""
                    'Select Case package.wRegion
                    '    Case RegionState.ZhuCe
                    '        strtag = "推荐分组"
                    '    Case RegionState.JiHuo
                    '        strtag = "工具分组"
                    '    Case RegionState.AnZhuang
                    '        strtag = "安装分组"
                    'End Select
                    '-----------

                    Dim aryParameters As Object() = New Object() {False, _
                              IIf(isLocal, String.Format("{0}{1}{2}", strName, vbCrLf, "免费应用，不参加结算。"), String.Format("{0}{1}{2}", strName, vbCrLf, package.wPopularize)), _
                                package.Version, _
                                Utility.FormatFileSize(package.FileSize), _
                              IIf(isLocal, "0", package.Price), _
                                "", _
                                ""}

                    If package.wID.Length = 0 OrElse package.wID = "0" Then
                        strName = "数据无效，联系客服 - " & strName
                    End If

                    'Debug.Print("sku：" & package.Name & ", " & package.wRegion.ToString)

                    If isAddByGroup Then
                        Dim strGroupText As String = String.Empty
                        If Not isLocal Then
                            Select Case package.wRegion
                                'Case RegionState.ZhuCe
                                '    strGroupText = strGroupZhuce
                                'Case RegionState.JiHuo
                                '    strGroupText = strGroupJihuo
                                'Case RegionState.AnZhuang
                                '    strGroupText = strGroupAnzhuang
                                Case RegionState.ZhuCe, RegionState.JiHuo, RegionState.AnZhuang
                                    strGroupText = strGroupFZ
                                Case RegionState.LocalSource
                                    strGroupText = strGroupLocal
                            End Select

                        Else
                            strGroupText = strGroupLocal

                        End If

                        'Dim strGroupClick As String = "批量安装此分组"
                        row = Me.dgvUpdate.AddGroupItem(strGroupText, aryParameters, strGroupClick)
                    Else
                        Dim index As Integer = Me.dgvUpdate.Rows.Count

                        '话费充值放第一个位置
                        If package.Identifier = "com.huafei.huafeipro" Then
                            index = 0
                        End If

                        Me.dgvUpdate.Rows.Insert(index, aryParameters)
                        row = Me.dgvUpdate.Rows(index)
                    End If

                    Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                    cellName.tbIcon = appDefaultIcon.Clone
                    cellName.tbIconShadow = True
                    cellName.CrackedInfo = package.CrackedInfo

                    Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")

                    '判断是否本地已有此包
                    cellProgress.tbProgressStyle = iTong.ProgressStyle.Text
                    cellProgress.tbShowIgnoreButton = False
                    cellProgress.tbText = ""
                    cellProgress.tbShowIgnoreButton = False

                    row.Tag = package

                    Me.RefleshPackageIcon(row)
                    iCount = iCount + 1
                Next
                Common.Log("加载个数" & iCount)

                'Me.SetProgressStateWithConnect(Me.mDevice IsNot Nothing)
                Me.dgvUpdate.Sort(Me.dgvUpdate.Columns("colVersion"), System.ComponentModel.ListSortDirection.Descending)
                Me.dgvUpdate.ResumeLayout()

            Catch ex As Exception
                Common.LogException(ex.ToString(), "AddItemToDgv")
            End Try
        End If
    End Sub

    Private Delegate Sub UpdateRowIconToDataGridViewHandler(ByVal row As tbDataGridViewRow, ByVal appIcon As Image)
    Private Sub UpdateRowIconToDataGridView(ByVal row As tbDataGridViewRow, ByVal appIcon As Image)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New UpdateRowIconToDataGridViewHandler(AddressOf UpdateRowIconToDataGridView), row, appIcon)
            Else

                If appIcon IsNot Nothing Then
                    Dim cellName As tbDataGridViewTextBoxCell = row.Cells("colName")
                    cellName.tbIcon = appIcon.Clone()
                End If

            End If
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

    Private Delegate Sub ChangeLoadingTextHandler(ByVal text As String)
    Protected Sub ChangeLoadingText(ByVal text As String)
        If Me.InvokeRequired Then
            Me.Invoke(New ChangeLoadingTextHandler(AddressOf ChangeLoadingText), text)
        Else
            Me.lblLoading.Text = text
        End If
    End Sub

    Private Sub ShowStateInfo(ByVal text As String)
        If Not Me.mFrmApp.IsNodeChecked(Me.FunctionMappingKey) Then
            Return
        End If

        Me.SetStatePanelVisible(True, text)
        Utility.WaitSeconds(2)
        Me.SetStatePanelVisible(False, "")
    End Sub

#End Region

#Region "---  WealthHelper事件  ---"

    Private Sub WealthHelper_LoginEvent(ByVal sender As Object, ByVal e As EventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(AddressOf WealthHelper_LoginEvent), sender, e)
        Else
            If Me.mLogin.IsLogin Then
                If Not Me.bgwWealth.IsBusy Then
                    Me.bgwWealth.RunWorkerAsync()
                End If
            Else
                Me.SetViewStyle(ViewStyle.NoLogin)
            End If
        End If
    End Sub

    Private Sub WealthHelper_LogoutEvent(ByVal sender As Object, ByVal e As EventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(AddressOf WealthHelper_LogoutEvent), sender, e)
        Else
            Me.Logout()
        End If
    End Sub

    Public Sub Logout()
        Try
            Me.dgvUpdate.Rows.Clear()
            Me.SetViewStyle(ViewStyle.NoLogin)

            '财富节点是加载一个界面，要其他界面的数量都跟着变化，其他免费的暂时不这样处理
            If Me.CheckIsCaifuView Then
                Me.mFrmApp.UpdateNodeNumber(FunctionKey.LocalUpdate, 0)
                Me.mFrmApp.UpdateNodeNumber(FunctionKey.tbAnZhuang, 0)
                Me.mFrmApp.UpdateNodeNumber(FunctionKey.tbZhuCe, 0)
                Me.mFrmApp.UpdateNodeNumber(FunctionKey.tbLianYun, 0)
            Else
                Me.mFrmApp.UpdateNodeNumber(Me.FunctionMappingKey, Me.dgvUpdate.Rows.Count - Me.dgvUpdate.DictGroupKeys.Count)
            End If
        Catch
        End Try
    End Sub

    Private Sub OnCheckSoftExistEvent(ByVal sender As Object, ByVal args As WealthHelperArgs)
        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.DeviceID = args.Device.DeviceID Then
            If args.IsCheckSoftInstall Then
                '正在校验
                Me.ChangeDownloadState(args.SKU, "", DownloadStatus.JudgeExistBefore)
            ElseIf args.WebReturnSoftInstalled Then
                'web返回存在
                Me.ChangeDownloadState(args.SKU, "", DownloadStatus.JudgeExisted)
            Else
                'web返回不存在
                Me.ChangeDownloadState(args.SKU, "", DownloadStatus.JudgeNotExist)
            End If
        End If
    End Sub

    Private Sub OnRefreshListEvent(ByVal sender As Object, ByVal args As WealthHelperArgs)
        Dim lstPackages As List(Of UpdatePackageInfo) = Nothing
        If args.wFunctionKey = Me.FunctionMappingKey AndAlso Me.FunctionMappingKey = FunctionKey.IOSZHUANQU Then
            lstPackages = WealthHelper.Instance.YuzhuangList
        ElseIf args.wFunctionKey = Me.FunctionMappingKey AndAlso Me.FunctionMappingKey = FunctionKey.IOSNEWDEVICE Then
            lstPackages = WealthHelper.Instance.NewDeviceList
        End If
        If lstPackages Is Nothing Then
            Return
        End If

        Me.CheckFileDownload(lstPackages)
        Me.GetPackagesCompleted(lstPackages)
    End Sub

#End Region

#Region "--- 获取软件列表 ---"

    Private Sub bgwWealth_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwWealth.DoWork
        Try
            Common.Log("正在获取软件列表:" & Me.FunctionMappingKey)
            Me.ChangeLoadingText("正在获取应用列表...")
            Me.SetViewStyle(ViewStyle.Loading)

            Dim blnCheckPkg As Boolean = False
            Dim lstPackages As List(Of UpdatePackageInfo) = Me.GetPackageFromServer(blnCheckPkg, CType(e.Argument, Boolean))
            If lstPackages IsNot Nothing Then
                Common.Log("获取到的列表数量:" & lstPackages.Count)
            End If

            e.Result = lstPackages

            ''检查已下载的包是否为新包，如果是旧包则删除
            'If blnCheckPkg Then
            '    Me.CheckFileDownload(lstPackages)
            'End If

            If IniSetting.GetCheckClearCache() = False Then
                IniSetting.SetCheckClearCache(True)
                Me.ClearPackageCache()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwWealth_DoWork")
        End Try
    End Sub

    Private Sub ClearPackageCache()
        Try
            Dim strFolder As String = Folder.CachePackagesFolder
            Dim aryFolder As String() = Directory.GetDirectories(strFolder, "*.*", SearchOption.TopDirectoryOnly)
            Dim index As Integer = 0

            If aryFolder IsNot Nothing AndAlso aryFolder.Length > 0 Then
                For Each strDirectroy As String In aryFolder
                    index += 1
                    Directory.Delete(strDirectroy, True)
                    Me.ChangeLoadingText(String.Format("正在清理缓存，请稍候 {0}/{1} ...", index, aryFolder.Length))

                    Application.DoEvents()
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ClearPackageCache")
        End Try
    End Sub

    Protected Overridable Function GetPackageFromServer(ByRef blnCheckPkg As Boolean, ByVal arg As Boolean) As List(Of UpdatePackageInfo)
        Dim lstPackages As List(Of UpdatePackageInfo) = Nothing

        Select Case Me.FunctionMappingKey
            Case FunctionKey.IOSZHUANQU
                lstPackages = Me.mWealthHelper.GetPackages(AreaType.Yuzhuang, arg)
                blnCheckPkg = True

            Case FunctionKey.IOSNEWDEVICE
                lstPackages = Me.mWealthHelper.GetPackages(AreaType.NewDevice, arg)
                blnCheckPkg = True

                'Case FunctionKey.TONGBUZHUANGJIBIBEI
                '    '获取装机必备列表
                '    lstPackages = Me.mWealthHelper.GetPackages(AreaType.ReMenBiBei, arg)

                'Case FunctionKey.TONGBUREMENYOUXI
                '    '获取热门游戏列表
                '    lstPackages = Me.mWealthHelper.GetPackages(AreaType.ReMenYouXi, arg)

                'Case FunctionKey.TONGBUREMENRUANJIAN
                '    '获取热门软件列表
                '    lstPackages = Me.mWealthHelper.GetPackages(AreaType.ReMenYingYong, arg)
        End Select

        Return lstPackages
    End Function

    Private Sub bgwWealth_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwWealth.RunWorkerCompleted
        Try
            Common.Log("获取软件列表完成:" & Me.FunctionMappingKey)
            Me.GetPackagesCompleted(e.Result)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwWealth_RunWorkerCompleted")
        End Try
    End Sub

    Private Sub RefleshPackageIcon()
        Try
            If Me.mtdRefleshIcon IsNot Nothing AndAlso Me.mtdRefleshIcon.ThreadState <> ThreadState.Stopped Then
                Return
            End If
        Catch
        End Try

        Me.mtdRefleshIcon = New Thread(AddressOf RefleshPackageIconInThread)
        With Me.mtdRefleshIcon
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub RefleshPackageIconInThread()
        Try
            '获取软件图标
            For index As Integer = 0 To Me.dgvUpdate.Rows.Count - 1
                Dim row As tbDataGridViewRow = Me.dgvUpdate.Rows(index)
                If row.isGroup Then
                    Continue For
                End If

                Try
                    Dim info As UpdatePackageInfo = row.Tag
                    Dim imgIcon As Image = WealthHelper.GetIconCahceImage(info.ItemId, info.Version, info.Identifier, info.wIconUrl)

                    If imgIcon IsNot Nothing Then
                        '委托主线程UI，执行完就不会占用
                        Me.UpdateRowIconToDataGridView(row, imgIcon)
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString())
                End Try
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "RefleshPackageIconInThread")
        End Try
    End Sub

    Protected Sub CheckFileDownload(ByVal lstPackages As List(Of UpdatePackageInfo))
        Try
            If lstPackages Is Nothing Then
                Return
            End If

            For index As Integer = lstPackages.Count - 1 To 0 Step -1
                Me.mWealthHelper.CheckFileMd5(lstPackages(index), Me.FunctionMappingKey)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "CheckFileDownload")
        End Try
    End Sub

    Private Sub GetPackagesCompleted(ByVal lstPackages As List(Of UpdatePackageInfo))
        Try
            Me.SetViewStyle(ViewStyle.Nomal)

            If lstPackages IsNot Nothing Then
                '添加到界面
                Me.AddItemToDgv(lstPackages, False)
                Common.Log("添加到界面" & lstPackages.Count)

                '加载本地应用’
                LoadLoaclApp(False)
                Common.Log("加载本地应用")

                '单独线程获取图片资源
                'Me.RefleshPackageIcon()

                Me.SetProgressStateWithConnect(Me.mDevice IsNot Nothing)

            Else
                '提示获取失败，请手动刷新获取
                Me.dgvUpdate.Rows.Clear()
                Me.ShowStateInfo("获取软件列表失败，请手动刷新！")
            End If

            Me.mFrmApp.UpdateNodeNumber(Me.FunctionMappingKey, Me.dgvUpdate.Rows.Count - Me.dgvUpdate.DictGroupKeys.Count)

        Catch ex As Exception
            Common.LogException(ex.ToString, "GetPackagesCompleted")
        End Try
    End Sub

#End Region

#Region "--- 下载 ---"

    Private Sub RetryUpdate(ByVal e As CellButtonEventArgs)
        Dim row As tbDataGridViewRow = e.Cell.OwningRow
        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")

        If cellProgress.Tag = DownloadStatus.DownloadSucceed Then
            '安装
            Me.DoInstall(row)
        Else
            '下载财富通道软件
            Dim pInfo As UpdatePackageInfo = CType(Me.dgvUpdate.Rows(e.Cell.RowIndex).Tag, UpdatePackageInfo)
            If Me.FunctionMappingKey = FunctionKey.IOSZHUANQU Then
                pInfo.wIsNewAreaApp = False
            ElseIf Me.FunctionMappingKey = FunctionKey.IOSNEWDEVICE Then
                pInfo.wIsNewAreaApp = True
            End If

            Me.DoDownloadSoft(pInfo)
        End If
    End Sub

    Private Sub DoDownloadBatch()

        If Me.dgvUpdate.SelectedRows.Count = 0 Then
            tbMessageBox.Show("您没有选择任何软件，请重新选择。", _
                           Me.Language.GetString("Common.Info"), _
                           MessageBoxButtons.OK, _
                           MessageBoxIcon.Information)
        End If

        Dim lstUpdate As New List(Of tbDataGridViewRow)
        For index As Integer = 0 To Me.dgvUpdate.SelectedRows.Count - 1
            If Not Me.dgvUpdate.SelectedRows(index).Visible Then
                Continue For
            End If

            '过滤正在下载或安装的软件，但失败支持重装
            Dim cell As tbDataGridViewProgressCellEx = Me.dgvUpdate.SelectedRows(index).Cells("colProgress")
            If Not (cell.Tag Is Nothing OrElse _
                    cell.Tag = DownloadStatus.Nomal OrElse _
                    cell.Tag = DownloadStatus.DownloadFailed OrElse _
                    cell.Tag = DownloadStatus.DownloadSucceed OrElse _
                    cell.Tag = DownloadStatus.InstallUnSucceed) Then
                Continue For
            End If

            lstUpdate.Add(Me.dgvUpdate.SelectedRows(index))
        Next

        If lstUpdate.Count = 0 Then
            Return
        End If


        For index As Integer = lstUpdate.Count - 1 To 0 Step -1
            Dim row As tbDataGridViewRow = lstUpdate(index)
            If row.isGroup Then
                Continue For
            End If

            Dim pInfo As UpdatePackageInfo = CType(row.Tag, UpdatePackageInfo)
            'Debug.Print(pInfo.Name & "," & pInfo.wDownloadUrl)
            Dim strPkgPath As String = Me.mWealthHelper.GetFileDownloadPath(pInfo, Me.FunctionMappingKey)

            '文件存在则直接安装（只安装到当前设备）
            '文件不存在则下载（下载完，安装到连接的所有设备）
            If File.Exists(strPkgPath) Then
                Me.DoInstall(row)
            Else
                pInfo.wIsNewAreaApp = IIf(Me.FunctionMappingKey = FunctionKey.IOSZHUANQU, False, True)
                Me.DoDownloadSoft(pInfo)
            End If
        Next
        lstUpdate.Clear()
    End Sub

    Private Sub DownloadWealthSoft(ByVal pInfo As UpdatePackageInfo)
        If Me.mDevice IsNot Nothing Then
            pInfo.DeviceId = Me.mDevice.DeviceID

            '安装前预判
            pInfo.wCheck2Install = False
            Me.Start2CheckAppInstall(pInfo)

        Else
            '直接下载
            mWealthHelper.DownloadWealthSoft(pInfo)
        End If
    End Sub

    Private Sub DoDownloadSoft(ByVal pinfo As UpdatePackageInfo)
        If pinfo.wDownloadUrl.EndsWith(".ipa") OrElse pinfo.wDownloadUrl.EndsWith(".apk") Then
            Me.DownloadWealthSoft(pinfo)
        Else
            Me.Start2Download(pinfo)
        End If
    End Sub

    Private mTdDownloadApp As Thread = Nothing
    Private mQueueDownInfo As New Queue(Of UpdatePackageInfo)

    Private Sub Start2Download(ByVal pinfo As UpdatePackageInfo)
        Me.mQueueDownInfo.Enqueue(pinfo)

        If Me.mTdDownloadApp IsNot Nothing AndAlso Me.mTdDownloadApp.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTdDownloadApp = New Thread(AddressOf DownloadApp)
        Me.mTdDownloadApp.IsBackground = True
        Me.mTdDownloadApp.Start()
    End Sub

    Private Sub DownloadApp()
        While Me.mQueueDownInfo.Count > 0
            Try
                Dim pinfo As UpdatePackageInfo = Me.mQueueDownInfo.Dequeue

                Me.ChangeDownloadState(pinfo.Identifier, pinfo.Version, DownloadStatus.ComputeUrl)

                Dim strDownloadUrl As String = Utility.GetParamValueFromQuery("url", pinfo.wDownloadUrl, "&", True)
                Dim strContent As String = Utility.GetContentStringFromUrl(strDownloadUrl, System.Text.Encoding.UTF8)
                If strContent.Length = 0 Then

                    '如果失败，则打log。输出请求的url。
                    Dim strText As String = "此链接已失效 frmUpdateWealth：" & vbCrLf & pinfo.Name & "," & strDownloadUrl
                    Debug.Print(strText)
                    Common.LogException(strText)
                    Me.ChangeDownloadState(pinfo.Identifier, pinfo.Version, DownloadStatus.UrlError)

                    Continue While
                End If

                '在1.35版本的时候，web要求每个下载链接都进行解密
                strContent = Common.DecryptDES(strContent, "ac68!3#1", "ac68!3#1")
                strDownloadUrl = Utility.GetParamValueFromQuery("url", strContent, "@@")    '下载地址
                pinfo.wDownloadUrl = strDownloadUrl

                Me.DownloadWealthSoft(pinfo)

            Catch ex As Exception
                Common.LogException(ex.ToString, "DownloadApp")
            End Try
        End While
    End Sub

#End Region

#Region "--- 软件授权 ---"

    Private Sub AuthorizeHelper_AuthorizeStart(ByVal sender As Object, ByVal e As EventArgs)
        Me.SwitchAuthorizeView(True)
        Me.mFrmApp.ShowInfoMsg(Me.FunctionMappingKey, "")
    End Sub

    Private Sub AuthorizeHelper_AuthorizeCompleted(ByVal sender As Object, ByVal e As AuthorizeEventArgs)
        If e.Result = SoftDownloadErrorState.AuthNoCotinue Then
            GoTo DOEXIT
        End If

        Dim strReason As String = ""
        Dim strResult As String = ""

        Select Case e.Result
            Case SoftDownloadErrorState.AuthFailed
            Case SoftDownloadErrorState.AuthFailedGetFairPlayCertificate
                strReason = "无法获取设备数据，请尝试重启设备。"
            Case SoftDownloadErrorState.LoginFailedWithNetworkError
                strReason = "网络超时。"
            Case SoftDownloadErrorState.Succeed
            Case SoftDownloadErrorState.AuthSucceedServerNoData
                strReason = "如有闪退问题，请修复。"
        End Select

        If e.Result = SoftDownloadErrorState.AuthFailed OrElse _
           e.Result = SoftDownloadErrorState.AuthFailedGetFairPlayCertificate OrElse _
           e.Result = SoftDownloadErrorState.LoginFailedWithNetworkError Then

            If e.IsRepaire Then
                strResult = "修复失败。"
            Else
                strResult = "授权失败。"
            End If
        Else
            If e.IsRepaire Then
                strResult = "修复成功。"
            Else
                strResult = "授权成功。"
            End If
        End If

        strResult = Me.mDevice.DeviceName & " " & strResult & strReason

        Me.mFrmApp.ShowInfoMsg(Me.FunctionMappingKey, strResult)

DOEXIT:
        Me.SwitchAuthorizeView(False)
    End Sub

    Private Delegate Sub SwitchAuthorizeViewHandler(ByVal isAuthorizing As Boolean)
    Private Sub SwitchAuthorizeView(ByVal isAuthorizing As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SwitchAuthorizeViewHandler(AddressOf SwitchAuthorizeView), isAuthorizing)
        Else
            If isAuthorizing Then
                Me.btnRepair.Text = "正在修复..."
                Me.btnRepair.Enabled = False
            Else
                Me.btnRepair.Text = "修复授权"
                Me.btnRepair.Enabled = True
            End If
        End If
    End Sub

#End Region

#Region "--- 安装前预判软件是否安装过 ---"

    Private mQueueCheckAppInstall As New Queue(Of UpdatePackageInfo)
    Private Shared mLockCheckAppInstal As Object = New Object()
    Private mTdCheckAppInstall As Thread = Nothing

    Private Sub Start2CheckAppInstall(ByVal pinfo As UpdatePackageInfo)
        'If Not Me.mQueueCheckAppInstall.Contains(pinfo) Then
        '    Me.mQueueCheckAppInstall.Enqueue(pinfo)
        '    Common.Log("加进安装队列" & pinfo.PackagePath)
        'End If

        'If Me.bgwCheckAppInstall.IsBusy Then
        '    Return
        'End If

        'Me.bgwCheckAppInstall.RunWorkerAsync()

        Me.QueueStart2CheckAppInstall(pinfo)

        If Me.mTdCheckAppInstall IsNot Nothing AndAlso Me.mTdCheckAppInstall.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTdCheckAppInstall = New Thread(AddressOf DomCheckAppInstall)
        Me.mTdCheckAppInstall.IsBackground = True
        Me.mTdCheckAppInstall.Start()

    End Sub

    Private Function QueueStart2CheckAppInstall(ByVal item As UpdatePackageInfo) As UpdatePackageInfo
        Dim info As UpdatePackageInfo = Nothing

        Try
            SyncLock mLockCheckAppInstal
                If item Is Nothing Then
                    If mQueueCheckAppInstall.Count > 0 Then
                        info = Me.mQueueCheckAppInstall.Dequeue()
                    End If
                Else
                    Me.mQueueCheckAppInstall.Enqueue(item)
                End If
            End SyncLock
        Catch ex As Exception
            Common.LogException(ex.ToString(), "QueueStart2CheckAppInstall")
        End Try

        Return info
    End Function

    Private Sub DomCheckAppInstall()
        While True
            Try
                Dim pinfo As UpdatePackageInfo = Me.QueueStart2CheckAppInstall(Nothing) ' Me.mQueueCheckAppInstall.Dequeue

                If pinfo Is Nothing Then
                    System.Threading.Thread.Sleep(300)
                    Continue While
                    'Return
                End If

                '检验后判断是安装还是下载操作
                If pinfo.wCheck2Install Then
                    '进行安装
                    If Me.mDevice IsNot Nothing Then
                        Me.mInstallHelper.InstallApplication(pinfo.PackagePath, True, True)
                    End If
                Else
                    '直接下载
                    Me.mWealthHelper.DownloadWealthSoft(pinfo)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "DomCheckAppInstall")
            End Try
        End While
    End Sub

    'Private Sub bgwCheckAppInstall_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwCheckAppInstall.DoWork
    '    Try
    '        While Me.mQueueCheckAppInstall.Count > 0
    '            Dim pinfo As UpdatePackageInfo = Me.QueueStart2CheckAppInstall(Nothing) ' Me.mQueueCheckAppInstall.Dequeue

    '            '检验后判断是安装还是下载操作
    '            If pinfo.wCheck2Install Then
    '                '进行安装
    '                If Me.mDevice IsNot Nothing Then
    '                    Me.mInstallHelper.InstallApplication(pinfo.PackagePath, True, True)
    '                End If
    '            Else
    '                '直接下载
    '                Me.mWealthHelper.DownloadWealthSoft(pinfo)
    '            End If
    '        End While
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "bgwCheckAppInstall_DoWork")
    '    End Try
    'End Sub

#End Region

#Region "--- 底部状态显示 ---"

    Private Sub ShowAppInfo()
        Dim strText As String = "如何打开苹果游戏商店获取设备信息"
        'If Me.FunctionMappingKey = FunctionKey.LocalUpdate Then
        '    strText = "此列表按激活数付费，激活后不能删除，否则会影响激活率"
        'ElseIf Me.FunctionMappingKey = FunctionKey.tbAnZhuang Then
        '    strText = "此列表按安装数付费，安装后不能删除"
        'ElseIf Me.FunctionMappingKey = FunctionKey.tbZhuCe Then
        '    strText = "此列表按注册数付费，安装后不能删除"
        'ElseIf Me.FunctionMappingKey = FunctionKey.tbLianYun Then
        '    strText = "此列表按安装数付费，安装后不能删除"
        'End If

        'pnlState.tbBackgroundImage = My.Resources.notify_bg
        'pnlState.tbSplit = "1, 20, 2, 20"
        Me.lblState.BackColor = Color.Transparent
        Me.lblState.ForeColor = Color.FromArgb(68, 68, 68) ' Color.FromArgb(123, 93, 3)

        Me.SetStatePanelVisible(True, strText)
    End Sub

#End Region

#Region "--- 本地App加载 ---"

    Private Sub LoadLoaclApp(ByVal isSetLocalFrm As Boolean)
        '如果 本地App文件夹 路径有修改则需要重新去获取’
        If mFrmSetLocalPackageFolder.IsChage Then
            Try
                If Me.dgvUpdate.DictGroupKeys.ContainsKey(mStrGroupLocalText) Then
                    Dim listRemove As New List(Of DataGridViewRow)
                    For Each row As tbDataGridViewRow In Me.dgvUpdate.DictGroupKeys(mStrGroupLocalText).Rows
                        listRemove.Add(row)
                    Next

                    For Each row As tbDataGridViewRow In listRemove
                        Me.dgvUpdate.RemoveGroupItem(row)
                    Next
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "LoadLoaclApp")
            End Try

            LocalAppHelper.Instance().Start2LoadLocalApp()
            mFrmSetLocalPackageFolder.IsChage = False
            Return

        ElseIf isSetLocalFrm Then
            '如果用户 打开设置窗体  但是没有修改设置 则不做任何处理
            Return
        End If

        If Not LocalAppHelper.Instance().IsReady AndAlso LocalAppHelper.Instance().ListTotalLocalApp.Count <= 0 Then
            LocalAppHelper.Instance().Start2LoadLocalApp()
        Else
            Me.DoAddLocalApp(LocalAppHelper.Instance().ListTotalLocalApp)
        End If
    End Sub

    Private Sub OnLoadLocalApp(ByVal sender As Object, ByVal args As LocalAppEventArgs)
        Try
            Me.DoAddLocalApp(args.mListDelPackageInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnLoadLocalApp")
        End Try

    End Sub

    Private Sub DoAddLocalApp(ByVal listDelPackageInfo As List(Of UpdatePackageInfo))
        Try
            SetBtnEnabled(False)

            Me.AddItemToDgv(listDelPackageInfo, True)
            Me.SetProgressStateWithConnect(Me.mDevice IsNot Nothing)
            Me.UpdateDGVRow()

            SetBtnEnabled(True)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoAddLocalApp")
        End Try
    End Sub

    Private Delegate Sub UpdateDGVRowHandler()
    Private Sub UpdateDGVRow()
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateDGVRowHandler(AddressOf UpdateDGVRow))
        Else
            Me.mFrmApp.UpdateNodeNumber(Me.FunctionMappingKey, Me.dgvUpdate.Rows.Count - Me.dgvUpdate.DictGroupKeys.Count)
        End If
    End Sub

    Private Delegate Sub SetBtnEnabledHandler(ByVal isEnabled As Boolean)
    Private Sub SetBtnEnabled(ByVal isEnabled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetBtnEnabledHandler(AddressOf SetBtnEnabled), isEnabled)
        Else
            btnLocalPackageFolder.Enabled = isEnabled
        End If
    End Sub


    'Private Delegate Sub AddLocalItemToDgvHandler(ByVal mlstPackages As List(Of UpdatePackageInfo))
    'Private Sub AddLocalItemToDgv(ByVal mlstPackages As List(Of UpdatePackageInfo))
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New AddLocalItemToDgvHandler(AddressOf AddLocalItemToDgv), mlstPackages)
    '    Else
    '        Try
    '            Me.dgvUpdate.SuspendLayout()

    '            Dim isAddByGroup As Boolean = (Me.FunctionMappingKey = FunctionKey.IOSZHUANQU OrElse Me.FunctionMappingKey = FunctionKey.IOSNEWDEVICE)
    '            Dim strGroupLocal As String = mStrGroupLocalText

    '            If isAddByGroup AndAlso Not Me.dgvUpdate.DictGroupKeys.ContainsKey(strGroupLocal) Then
    '                Me.dgvUpdate.AddGroup(strGroupLocal, Me.GetGroupTemplate(), "", -1)
    '            End If

    '            Dim appDefaultIcon As Image = My.Resources.app_icon_default.Clone
    '            For i As Integer = 0 To mlstPackages.Count - 1
    '                Dim package As UpdatePackageInfo = mlstPackages(i)
    '                Dim row As New tbDataGridViewRow()
    '                row.Height = 58

    '                Dim strName As String = package.Name
    '                If package.wIsRealTime AndAlso Not strName.Contains("(实时)") Then
    '                    strName = String.Format("{0} （实时）", strName)
    '                End If

    '                Dim aryParameters As Object() = New Object() {False, _
    '                            String.Format("{0}{1}{2}", strName, vbCrLf, "免费应用，不参加结算。"), _
    '                            package.Version, _
    '                            Utility.FormatFileSize(package.FileSize), _
    '                            0, _
    '                            "", _
    '                            ""}

    '                If isAddByGroup Then
    '                    row = Me.dgvUpdate.AddGroupItem(strGroupLocal, aryParameters)
    '                Else
    '                    Dim index As Integer = Me.dgvUpdate.Rows.Count

    '                    '话费充值放第一个位置
    '                    If package.Identifier = "com.huafei.huafeipro" Then
    '                        index = 0
    '                    End If

    '                    Me.dgvUpdate.Rows.Insert(index, aryParameters)
    '                    row = Me.dgvUpdate.Rows(index)
    '                End If

    '                Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
    '                cellName.tbIcon = appDefaultIcon.Clone
    '                cellName.tbIconShadow = True
    '                cellName.CrackedInfo = package.CrackedInfo

    '                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")

    '                '判断是否本地已有此包
    '                cellProgress.tbProgressStyle = iTong.ProgressStyle.Text
    '                cellProgress.tbShowIgnoreButton = False
    '                cellProgress.tbText = ""
    '                cellProgress.tbShowIgnoreButton = False

    '                row.Tag = package

    '                Me.RefleshPackageIcon(row)

    '            Next

    '            Me.dgvUpdate.ResumeLayout()

    '        Catch ex As Exception
    '            Common.LogException(ex.ToString(), "AddLocalItemToDgv")
    '        End Try
    '    End If
    'End Sub

#End Region

#Region "--- 队列 加载App Logo ---"

    Private Shared mLockRefleshPackageIcon As Object = New Object()
    Private mQueueRefleshPackageIcon As New Queue(Of tbDataGridViewRow)
    Private mTdRefleshPackageIcon As Thread = Nothing

    Private Sub RefleshPackageIcon(ByVal item As tbDataGridViewRow)
        Me.QueueRefleshPackageIcon(item)

        If Me.mTdRefleshPackageIcon IsNot Nothing AndAlso Me.mTdRefleshPackageIcon.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        If Me.mQueueRefleshPackageIcon Is Nothing Then
            Me.mQueueRefleshPackageIcon = New Queue(Of tbDataGridViewRow)
        End If

        Me.mTdRefleshPackageIcon = New Thread(AddressOf DoRefleshPackageIcon)
        Me.mTdRefleshPackageIcon.IsBackground = True
        Me.mTdRefleshPackageIcon.Start()

    End Sub

    Private Function QueueRefleshPackageIcon(ByVal item As tbDataGridViewRow) As tbDataGridViewRow
        Dim info As tbDataGridViewRow = Nothing

        Try
            SyncLock mLockRefleshPackageIcon
                If item Is Nothing Then
                    If Me.mQueueRefleshPackageIcon.Count > 0 Then
                        info = Me.mQueueRefleshPackageIcon.Dequeue()
                    End If
                Else
                    Me.mQueueRefleshPackageIcon.Enqueue(item)
                End If
            End SyncLock
        Catch ex As Exception
            Common.LogException(ex.ToString(), "QueueRefleshPackageIcon")
        End Try

        Return info
    End Function

    Private Sub DoRefleshPackageIcon()
        While True
            Try
                Dim row As tbDataGridViewRow = Me.QueueRefleshPackageIcon(Nothing)

                If row Is Nothing Then
                    System.Threading.Thread.Sleep(100)
                    Continue While
                    'Return
                End If

                Dim info As UpdatePackageInfo = row.Tag
                Dim imgIcon As Image = My.Resources.app_icon_default.Clone

                If info.wRegion = RegionState.LocalSource Then
                    If IniSetting.GetLoadAppIcon() AndAlso info.AppIcon IsNot Nothing Then
                        imgIcon = info.AppIcon.Clone()
                    End If
                Else
                    imgIcon = WealthHelper.GetIconCahceImage(info.ItemId, info.Version, info.Identifier, info.wIconUrl)
                End If

                If imgIcon IsNot Nothing Then
                    '委托主线程UI，执行完就不会占用
                    Me.UpdateRowIconToDataGridView(row, imgIcon)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "DoRefleshPackageIcon")
            End Try
        End While
    End Sub

#End Region

#Region "--- 测试 ---"
    Private Sub btnTest_Click(sender As Object, e As EventArgs) Handles btnTest.Click
        Dim ofd As OpenFileDialog = New OpenFileDialog()
        ofd.Filter = "(*.ipa)|*.ipa"
        ofd.FilterIndex = 2
        ofd.RestoreDirectory = True
        If ofd.ShowDialog() = DialogResult.OK Then
            Me.mInstallHelper.TestInstallRealTimeApp(ofd.FileName)
        End If
    End Sub

#End Region

#Region "--- 个人签名 ---"
    Private Sub mPersonSignatureHelper_AppDownloadEventHandler(sender As Object, args As PersonSignatureProgressEventArgs)
        Try
            If args.UniqueDeviceIDPS = Me.mDevice.UniqueDeviceID Then
                Select Case args.PersonSignatureStatePS
                    Case PersonSignatureState.Unknown
                        Me.SetPersonSignatureProgress("尚未安装【苹果游戏商店】", False)

                    Case PersonSignatureState.GetUrlWaiting
                        Me.SetPersonSignatureProgress("正在获取【苹果游戏商店】下载地址", False)

                    Case PersonSignatureState.GetUrlSucceed
                        Me.SetPersonSignatureProgress("【苹果游戏商店】下载地址，获取成功", False)

                    Case PersonSignatureState.GetUrlFailure
                        Me.SetPersonSignatureProgress("【苹果游戏商店】下载地址，获取失败", True)

                    Case PersonSignatureState.GetUrlOvertime
                        Me.SetPersonSignatureProgress("【苹果游戏商店】下载地址，获取超时", True)

                    Case PersonSignatureState.Waiting
                        Me.SetPersonSignatureProgress("【苹果游戏商店】等待安装...", False)

                    Case PersonSignatureState.Succeed
                        Me.SetPersonSignatureProgress("【苹果游戏商店】安装成功", False)

                    Case PersonSignatureState.Failure
                        Me.SetPersonSignatureProgress("【苹果游戏商店】安装失败", True)

                    Case PersonSignatureState.Unavailable
                        Me.SetPersonSignatureProgress("该软件上线过程中出错，请联系客服！", True)

                    Case PersonSignatureState.Installing
                        Me.SetPersonSignatureProgress(String.Format("【苹果游戏商店】安装中... {0}% ", args.IntProgress), False)

                    Case PersonSignatureState.Transfering
                        Me.SetPersonSignatureProgress(String.Format("【苹果游戏商店】上传中... {0}% ", args.IntProgress), False)

                    Case PersonSignatureState.WaitDownloading
                        Me.SetPersonSignatureProgress("【苹果游戏商店】等待下载中...", False)

                    Case PersonSignatureState.Downloading
                        Me.SetPersonSignatureProgress(String.Format("【苹果游戏商店】下载中... {0}% ", args.IntProgress), False)

                    Case PersonSignatureState.DownloadFailure
                        Me.SetPersonSignatureProgress("【苹果游戏商店】下载完成", False)

                End Select
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "mPersonSignatureHelper_AppDownloadEventHandler")
        End Try
    End Sub

    Private Delegate Sub SetPersonSignatureProgressHandler(ByVal text As String, ByVal isFailure As Boolean)
    Private Sub SetPersonSignatureProgress(ByVal text As String, ByVal isFailure As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetPersonSignatureProgressHandler(AddressOf SetPersonSignatureProgress), text, isFailure)
        Else
            Me.lblPersonSignatureProgress.Text = text
            Me.lblAGSDowmload.Visible = isFailure
            Me.lblAGSDowmload.Location = New Point(Me.lblPersonSignatureProgress.Width + Me.lblPersonSignatureProgress.Left, Me.lblPersonSignatureProgress.Location.Y)
        End If
    End Sub

    Private Sub lblAGSDowmload_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lblAGSDowmload.LinkClicked
        If Me.mPersonSignatureHelper IsNot Nothing Then
            RemoveHandler Me.mPersonSignatureHelper.AppDownloadEventHandler, AddressOf mPersonSignatureHelper_AppDownloadEventHandler
            Me.mPersonSignatureHelper.Dispose()
            Me.mPersonSignatureHelper = Nothing
        End If

        Me.InitPersonSignatureHelper()

        If Me.mPersonSignatureHelper IsNot Nothing Then
            Me.mPersonSignatureHelper.AppDowmload()
        End If

    End Sub

#End Region



 
End Class

Public Class ValueChangeArgs
    Inherits EventArgs

    Private mChecked As Boolean = False
    Public Property IsCheck()
        Get
            Return Me.mChecked
        End Get
        Set(ByVal value)
            Me.mChecked = value
        End Set
    End Property

    Sub New(ByVal isCheck As Boolean)
        Me.mChecked = isCheck
    End Sub

End Class
