﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmPop
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmPop))
        Me.wbContent = New iTong.Components.tbWebBrowserEx
        Me.btnClose = New iTong.Components.tbButton
        Me.pnlContent = New System.Windows.Forms.Panel
        Me.pnlContent.SuspendLayout()
        Me.SuspendLayout()
        '
        'wbContent
        '
        Me.wbContent.AllowWebBrowserDrop = False
        Me.wbContent.Client = ""
        Me.wbContent.IsConnected = False
        Me.wbContent.IsWebBrowserContextMenuEnabled = False
        Me.wbContent.Jailbreaked = False
        Me.wbContent.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.wbContent.LoadingFont = Nothing
        Me.wbContent.LoadingGif = CType(resources.GetObject("wbContent.LoadingGif"), System.Drawing.Image)
        Me.wbContent.Location = New System.Drawing.Point(0, 0)
        Me.wbContent.Margin = New System.Windows.Forms.Padding(0)
        Me.wbContent.MinimumSize = New System.Drawing.Size(20, 20)
        Me.wbContent.Name = "wbContent"
        Me.wbContent.ShowLoadingWait = False
        Me.wbContent.ShowNavigateErrorPage = True
        Me.wbContent.ShowProgress = True
        Me.wbContent.Size = New System.Drawing.Size(322, 144)
        Me.wbContent.SN = ""
        Me.wbContent.TabIndex = 0
        Me.wbContent.UserInfo = ""
        Me.wbContent.UserInfoEncode = ""
        Me.wbContent.WebBrowserShortcutsEnabled = False
        '
        'btnClose
        '
        Me.btnClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClose.BackColor = System.Drawing.Color.Transparent
        Me.btnClose.BindingForm = Nothing
        Me.btnClose.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClose.Location = New System.Drawing.Point(301, 2)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnClose.Selectable = True
        Me.btnClose.Size = New System.Drawing.Size(24, 24)
        Me.btnClose.TabIndex = 9
        Me.btnClose.tbAdriftIconWhenHover = False
        Me.btnClose.tbAutoSize = False
        Me.btnClose.tbAutoSizeEx = False
        Me.btnClose.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btnClose.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnClose.tbBadgeNumber = 0
        Me.btnClose.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClose.tbEndEllipsis = False
        Me.btnClose.tbIconHoldPlace = True
        Me.btnClose.tbIconImage = Nothing
        Me.btnClose.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClose.tbIconMore = False
        Me.btnClose.tbIconMouseDown = Nothing
        Me.btnClose.tbIconMouseHover = Nothing
        Me.btnClose.tbIconMouseLeave = Nothing
        Me.btnClose.tbIconPlaceText = 2
        Me.btnClose.tbIconReadOnly = Nothing
        Me.btnClose.tbImageMouseDown = Nothing
        Me.btnClose.tbImageMouseHover = Nothing
        Me.btnClose.tbImageMouseLeave = Nothing
        Me.btnClose.tbReadOnly = False
        Me.btnClose.tbReadOnlyText = False
        Me.btnClose.tbShadow = False
        Me.btnClose.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnClose.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnClose.tbShowNew = False
        Me.btnClose.tbShowToolTipOnButton = False
        Me.btnClose.tbSplit = "3,3,3,3"
        Me.btnClose.tbText = ""
        Me.btnClose.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbTextColor = System.Drawing.Color.White
        Me.btnClose.tbTextColorDisable = System.Drawing.Color.White
        Me.btnClose.tbTextColorDown = System.Drawing.Color.White
        Me.btnClose.tbTextColorHover = System.Drawing.Color.White
        Me.btnClose.tbTextMouseDownPlace = 0
        Me.btnClose.tbToolTip = ""
        Me.btnClose.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClose.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClose.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.VisibleEx = True
        '
        'pnlContent
        '
        Me.pnlContent.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContent.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlContent.Controls.Add(Me.wbContent)
        Me.pnlContent.Location = New System.Drawing.Point(2, 33)
        Me.pnlContent.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContent.Name = "pnlContent"
        Me.pnlContent.Size = New System.Drawing.Size(323, 144)
        Me.pnlContent.TabIndex = 10
        '
        'frmPop
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(327, 181)
        Me.Controls.Add(Me.pnlContent)
        Me.Controls.Add(Me.btnClose)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.MinimumSize = New System.Drawing.Size(11, 40)
        Me.Name = "frmPop"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,8"
        Me.tbTitleFont = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "公告"
        Me.pnlContent.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents wbContent As tbWebBrowserEx
    Friend WithEvents btnClose As iTong.Components.tbButton
    Friend WithEvents pnlContent As System.Windows.Forms.Panel
End Class
