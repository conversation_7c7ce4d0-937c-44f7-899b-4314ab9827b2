﻿Public Class DeviceInfo

    Public Shared ReadOnly Property VersionNumber(ByVal device As IDevice) As Integer
        Get
            If device.DeviceType = DeviceType.Android Then
                Return CType(device, AndroidDevice).VersionNumber
            End If

            Return CType(device, iPhoneDevice).VersionNumber
        End Get
    End Property

    Public Shared ReadOnly Property WiFiAddress(ByVal device As IDevice) As String
        Get
            If device.DeviceType = DeviceType.Android Then
                Return CType(device, AndroidDevice).WifiMac
            End If

            Return CType(device, iPhoneDevice).WiFiAddress
        End Get
    End Property

    Public Shared ReadOnly Property SerialNumber(ByVal device As IDevice) As String
        Get
            If device.DeviceType = DeviceType.Android Then
                Return CType(device, AndroidDevice).SerialNumber
            End If

            Return CType(device, iPhoneDevice).SerialNumber
        End Get
    End Property

    Public Shared ReadOnly Property ZJ_OpenUDID(ByVal device As IDevice) As String
        Get
            If device.DeviceType = DeviceType.Android Then
                Return String.Empty
            End If

            Return CType(device, iPhoneDevice).ZJ_OpenUDID
        End Get
    End Property

    Public Shared ReadOnly Property ZJ_IDFA(ByVal device As IDevice) As String
        Get
            If device.DeviceType = DeviceType.Android Then
                Return String.Empty
            End If

            Return CType(device, iPhoneDevice).ZJ_IDFA
        End Get
    End Property

    Public Shared ReadOnly Property ZJ_IDFV(ByVal device As IDevice) As String
        Get
            If device.DeviceType = DeviceType.Android Then
                Return String.Empty
            End If

            Return CType(device, iPhoneDevice).ZJ_IDFV
        End Get
    End Property

    Public Shared ReadOnly Property ProductType(ByVal device As IDevice) As String
        Get
            If device.DeviceType = DeviceType.Android Then
                Return CType(device, AndroidDevice).ProductType
            End If

            Return CType(device, iPhoneDevice).ProductType
        End Get
    End Property

    Public Shared ReadOnly Property ProductVersion(ByVal device As IDevice) As String
        Get
            If device.DeviceType = DeviceType.Android Then
                Return CType(device, AndroidDevice).ProductVersion
            End If

            Return CType(device, iPhoneDevice).ProductVersion
        End Get
    End Property

End Class
