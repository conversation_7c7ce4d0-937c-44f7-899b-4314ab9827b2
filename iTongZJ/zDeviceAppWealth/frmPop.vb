﻿Imports System.Runtime.InteropServices
Imports System.Threading

Public Class frmPop

    Private mJsonData As String = ""


    <DllImport("user32.dll", EntryPoint:="AnimateWindow")> _
    Private Shared Function AnimateWindow(ByVal handle As IntPtr, ByVal ms As Integer, ByVal flags As Integer) As Boolean
    End Function

    Protected Overrides ReadOnly Property CreateParams() As System.Windows.Forms.CreateParams
        Get
            MyBase.CreateParams.ExStyle = MyBase.CreateParams.ExStyle Or &********
            Return MyBase.CreateParams
        End Get
    End Property

#Region "--- 初始化 ---"

    Sub New(ByVal jsonData As String)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong

        Me.wbContent.Location = New Point(-2, -2)
        Me.wbContent.Size = New Size(Me.pnlContent.Width + 4, Me.pnlContent.Height + 4)

        Me.mJsonData = jsonData

        Dim x As Integer = Screen.PrimaryScreen.WorkingArea.Right - Me.Width
        Dim y As Integer = Screen.PrimaryScreen.WorkingArea.Bottom - Me.Height

        Me.StartPosition = FormStartPosition.Manual
        Me.Location = New Point(x, y)
        Me.TopMost = True
        Me.FilletRadius = 6

        RemoveHandler wbContent.NewMessage, AddressOf WebBrowser_NewMessage
        RemoveHandler wbContent.NewWindow3, AddressOf WebBrowser_NewWindow3
        AddHandler wbContent.NewMessage, AddressOf WebBrowser_NewMessage
        AddHandler wbContent.NewWindow3, AddressOf WebBrowser_NewWindow3

        Dim strPath As String = Folder.CacheFolder & "Web\pop.html"
        ReleaseResource(strPath, My.Resources.html_pop)

        Me.ReNavigate()
    End Sub

    Private Shared Sub ReleaseResource(ByVal FilePath As String, ByVal Resource As Byte())
        If File.Exists(FilePath) Then
            File.Delete(FilePath)
        End If

        If Not File.Exists(FilePath) Then
            Using objWriter As New FileStream(FilePath, FileMode.OpenOrCreate, FileAccess.Write)
                objWriter.Write(Resource, 0, Resource.Length)
            End Using
        End If
    End Sub

    Private Sub WebBrowser_NewMessage(ByVal sender As tbWebBrowser, ByVal message As String)
        If message.Contains("loadcomplete") Then
            If Not Me.wbContent.IsDisposed AndAlso Me.wbContent.Document IsNot Nothing Then
                Me.wbContent.Document.InvokeScript("showNote", New Object() {Me.mJsonData, PluginLogin.Instance.Token})
            End If
        End If
    End Sub

    Private Sub WebBrowser_NewWindow3(ByVal sender As tbWebBrowserEx, ByRef ppDisp As Object, ByRef Cancel As Boolean, ByVal dwFlags As UInteger, ByVal bstrUrlContext As String, ByVal bstrUrl As String)
        Common.OpenExplorer(bstrUrl)
        Cancel = True
    End Sub

    Private Sub ReNavigate()
        Dim strPath As String = Folder.CacheFolder & "Web\pop.html"
        Me.wbContent.Navigate(strPath)
    End Sub

#End Region

    Public Shared Function ShowForm(ByVal strData As String, ByVal frmTemp As frmPop) As frmPop

        'Dim frmTemp As frmPop = Nothing
        'For Each frm As Form In Application.OpenForms
        '    Debug.Print("frm.Name" & frm.Name)
        '    If frm IsNot Nothing AndAlso TypeOf frm Is frmPop Then
        '        frmTemp = frm
        '        Exit For
        '    End If
        'Next

        If frmTemp IsNot Nothing AndAlso Not frmTemp.IsDisposed Then
            frmTemp.mJsonData = strData
            frmTemp.ReNavigate()

        Else
            frmTemp = New frmPop(strData)
            frmTemp.ShowForm(frmTemp)
        End If

        Return frmTemp
    End Function

    Private Sub ShowForm(ByVal form As Object)
        If form.InvokeRequired Then
            form.Invoke(New Threading.ParameterizedThreadStart(AddressOf ShowForm), form)
        Else
            NativeMethods.ShowWindow(form.Handle, SW_CMD.SW_SHOWNOACTIVATE)
            'form.Show()
        End If
    End Sub

    Private Sub CloseForm()
        If Me.InvokeRequired Then
            Me.Invoke(New Threading.ThreadStart(AddressOf CloseForm))
        Else
            Me.Close()
        End If
    End Sub

    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

End Class