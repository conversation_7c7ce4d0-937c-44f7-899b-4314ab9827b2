﻿Imports System.Threading
Imports iTong.CoreModule.WebSiteHelperBase

Public Class PersonSignatureHelper
    Implements IDisposable

    Private mDevice As iPhoneDevice
    Private mPluginLogin As PluginLogin
    Private Shared mDictInstances As New Dictionary(Of String, PersonSignatureHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Private disposedValue As Boolean = False        ' 检测冗余的调用

    Private mWebSiteHelperBase As WebSiteHelperBase = Nothing

    Private mTdAppDownload As Thread = Nothing
    Public Event AppDownloadEventHandler(ByVal sender As Object, ByVal args As PersonSignatureProgressEventArgs)

    Private mDownManage As MultiThreadDownload = Nothing
    Private mInstallHelper As iPhoneInstallHelper = Nothing
    Private mStrSku As String = "com.tongbu.AGSPlan"
    Private mIsDo As Boolean = False

    Private mTdCheckGameStore As Thread

    Private mStrURL As String = ""
    Private mStrVersion As String = ""
    Private mStrProfileExpirationDate As String = ""

    Private mGameStorePath As String = ""


#Region "--- 单例 ---"

    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieUDID() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New(ByVal iPhoneDeviceInstance As iPhoneDevice)
        mDevice = iPhoneDeviceInstance
        Me.mPluginLogin = PluginLogin.Instance()
        Me.mWebSiteHelperBase = New WebSiteHelperBase()

        Me.mDownManage = MultiThreadDownload.Instance
        Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)

        RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
        AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
        RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
        AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
        RemoveHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
        AddHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
        RemoveHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse
        AddHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse

        RemoveHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd
        AddHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd

        RemoveHandler CoreUpdateHelper.Instance().TaskExist, AddressOf OnTaskExist
        AddHandler CoreUpdateHelper.Instance().TaskExist, AddressOf OnTaskExist

        RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
        AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall

        RemoveHandler Me.mDevice.UninstallApplicationSucceed, AddressOf OnUninstallApplicationSucceed
        AddHandler Me.mDevice.UninstallApplicationSucceed, AddressOf OnUninstallApplicationSucceed

        RemoveHandler Me.mDownManage.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed
        AddHandler Me.mDownManage.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed

    End Sub

    Public Shared Function GetInstance(ByVal device As iPhoneDevice) As PersonSignatureHelper
        Dim helper As PersonSignatureHelper = Nothing
        Dim blnRetry As Boolean = False

        SyncLock _lockGetInstance
DO_RETRY:
            Try
                If device Is Nothing Then
                    If Not mDictInstances.ContainsKey(NoDevcieUDID) Then
                        mDictInstances.Add(NoDevcieUDID, New PersonSignatureHelper(device))
                    End If
                    helper = mDictInstances.Item(NoDevcieUDID)

                Else
                    If mDictInstances.ContainsKey(device.UniqueDeviceID) Then
                        Dim tmpHelper As PersonSignatureHelper = mDictInstances(device.UniqueDeviceID)
                        If tmpHelper.mDevice.IsConnected = False AndAlso device.IsConnected Then
                            mDictInstances.Remove(device.UniqueDeviceID)
                            mDictInstances.Add(device.UniqueDeviceID, New PersonSignatureHelper(device))

                            '释放资源
                            tmpHelper.Dispose()
                        End If

                    Else
                        If Not mDictInstances.ContainsKey(device.UniqueDeviceID) Then
                            mDictInstances.Add(device.UniqueDeviceID, New PersonSignatureHelper(device))
                        End If

                    End If

                    helper = mDictInstances.Item(device.UniqueDeviceID)
                End If

            Catch ex As Exception
                If Not blnRetry Then
                    blnRetry = True
                    GoTo DO_RETRY
                End If
            End Try
        End SyncLock

        Return helper

    End Function

#End Region

#Region "--- 释放 ---"

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        'If Not disposedValue Then
        If disposing Then
            ' TODO: 显式调用时释放非托管资源
            RemoveHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd
            RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            RemoveHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
            RemoveHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse
            RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
            RemoveHandler CoreUpdateHelper.Instance().TaskExist, AddressOf OnTaskExist
            RemoveHandler Me.mDownManage.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed

            RemoveHandler Me.mDevice.UninstallApplicationSucceed, AddressOf OnUninstallApplicationSucceed

            Me.mIsDo = False

            Try
                If Me.mTdAppDownload IsNot Nothing Then
                    If Me.mTdAppDownload.ThreadState <> ThreadState.Stopped Then
                        Me.mTdAppDownload.Abort()
                    End If

                    Me.mTdAppDownload = Nothing
                End If
            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try
            Try
                If Me.mTdCheckGameStore IsNot Nothing Then
                    If Me.mTdCheckGameStore.ThreadState <> ThreadState.Stopped Then
                        Me.mTdCheckGameStore.Abort()
                    End If

                    Me.mTdCheckGameStore = Nothing
                End If
            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try



        End If
        '  End If
        disposedValue = True
    End Sub

#End Region

#Region "--- 获取、下载个人签名包 ---"

    Public Sub AppDowmload()
        If Me.mIsDo Then
            Return
        End If
        Me.mIsDo = True

        If Me.mTdAppDownload IsNot Nothing AndAlso Me.mTdAppDownload.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTdAppDownload = New Thread(AddressOf DoAppDowmload)
        Me.mTdAppDownload.IsBackground = True
        Me.mTdAppDownload.Start()
    End Sub

    Private Sub DoAppDowmload()
        Try
            '获取下载信息 URL Version ProfileExpirationDate
            If Me.mStrURL.Length <= 0 OrElse Me.mStrVersion.Length <= 0 OrElse Me.mStrProfileExpirationDate.Length <= 0 Then
                Me.GetAGSPlanUrl(Me.mStrURL, Me.mStrVersion, Me.mStrProfileExpirationDate, mStrSku)
                Try
                    Dim dt As DateTime = Convert.ToDateTime(Me.mStrProfileExpirationDate)
                    Me.mStrProfileExpirationDate = dt.ToString("yyyyMMddHHmmss")
                Catch
                End Try
            End If

            '在名字里面添加时候过期日期，如果过期的包就会重新下载。
            Dim strSoftName As String = String.Format("{0}_{1}_{2}_{3}", "苹果游戏商店", Me.mStrVersion, Me.mDevice.UniqueDeviceID, Me.mStrProfileExpirationDate)

            '个人签名包获取url失败
            If Me.mStrURL.Length <= 0 Then
                Common.LogException("Url is empty.", "Personal AGSPlan get url failure.")
            End If

            '开始下载
            Me.AutoInstallFromWeb(Me.mStrURL, mDevice.Identifier, mStrSku, strSoftName, Me.mStrVersion, ResourceClass.Software, "")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoAppDowmload")
        End Try
    End Sub

    Private Sub GetAGSPlanUrl(ByRef strURL As String, ByRef strVersion As String, ByRef strProfileExpirationDate As String, strSku As String, Optional ByVal isShowProgress As Boolean = True)
        Try
            Dim intCount As Integer = 0
            Dim args As PersonSignatureProgressEventArgs = New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, PersonSignatureState.GetUrlWaiting)

            While True
                'Dim dict As Dictionary(Of String, String) = TuiInstallHelper.GetTuiPersonalUrl(Me.mDevice, strSku)
                Dim dict As Dictionary(Of String, String) = Me.GetAGSPlanPersonalUrl(strSku)
                If dict Is Nothing OrElse dict.Count = 0 Then
                    args.PersonSignatureStatePS = PersonSignatureState.GetUrlFailure '获取失败
                    If isShowProgress Then
                        RaiseEvent AppDownloadEventHandler(Nothing, args)
                    End If
                    Exit While
                End If

                If dict.ContainsKey("IPA") AndAlso dict.ContainsKey("State") AndAlso dict("State") = "1" Then
                    strURL = dict("IPA")
                    If dict.ContainsKey("version") Then
                        strVersion = dict("version")
                    End If
                    If dict.ContainsKey("ProfileExpirationDate") Then
                        strProfileExpirationDate = dict("ProfileExpirationDate")
                    End If

                    args.PersonSignatureStatePS = PersonSignatureState.GetUrlSucceed '获取成功
                    If isShowProgress Then
                        RaiseEvent AppDownloadEventHandler(Nothing, args)
                    End If
                    Exit While
                End If

                intCount += 1
                args.PersonSignatureStatePS = PersonSignatureState.GetUrlWaiting '继续等待
                If isShowProgress Then
                    RaiseEvent AppDownloadEventHandler(Nothing, args)
                End If
                Utility.WaitSeconds(5)
                If intCount > 18 Then
                    args.PersonSignatureStatePS = PersonSignatureState.GetUrlOvertime  '获取超时
                    If isShowProgress Then
                        RaiseEvent AppDownloadEventHandler(Nothing, args)
                    End If
                    Exit While
                End If
            End While
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetAGSPlanUrl")
        End Try
    End Sub

    Private Function GetAGSPlanPersonalUrl(strSku As String) As Dictionary(Of String, String)
        Dim dict As New Dictionary(Of String, String)()
        Try

            Dim strChannel As String = ""

            Dim strURL As String = String.Format(WebUrl.ServerTuiPersonal, Me.mDevice.UniqueDeviceID, strSku, strChannel, Me.mDevice.IDFA(), Me.mDevice.SerialNumber)
            Dim strResult As String = Utility.GetContentStringFromUrl(strURL, System.Text.Encoding.UTF8, 20000).Trim()
            '输出log
            Common.Log(Convert.ToString("GetAGSPlanPersonalUrl : ") & strResult)
            Dim jObj As JsonObject = JsonParser.ParseString(strResult)

            If jObj Is Nothing Then
                Return dict
            End If

            If jObj.ContainsKey("Code") AndAlso Not (TypeOf jObj("Code") Is JsonNull) Then
                Dim strPara As String = TryCast(jObj("Code"), JsonNumber).Value.ToString()
                dict.Add("Code", strPara)
            End If
            If jObj.ContainsKey("Message") AndAlso Not (TypeOf jObj("Message") Is JsonNull) Then
                Dim strPara As String = TryCast(jObj("Message"), JsonString).Value
                dict.Add("Message", strPara)
            End If
            If jObj.ContainsKey("Data") AndAlso Not (TypeOf jObj("Data") Is JsonNull) Then
                Dim jDataObj As JsonObject = TryCast(jObj("Data"), JsonObject)
                If jDataObj Is Nothing Then
                    Return dict
                End If
                If jDataObj.ContainsKey("UDID") AndAlso Not (TypeOf jDataObj("UDID") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("UDID"), JsonString).Value
                    dict.Add("UDID", strPara)
                End If
                If jDataObj.ContainsKey("Progcess") AndAlso Not (TypeOf jDataObj("Progcess") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("Progcess"), JsonNumber).Value.ToString()
                    dict.Add("Progcess", strPara)
                End If
                If jDataObj.ContainsKey("State") AndAlso Not (TypeOf jDataObj("State") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("State"), JsonNumber).Value.ToString()
                    dict.Add("State", strPara)
                End If
                If jDataObj.ContainsKey("msg") AndAlso Not (TypeOf jDataObj("msg") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("msg"), JsonString).Value
                    dict.Add("msg", strPara)
                End If
                If jDataObj.ContainsKey("IPA") AndAlso Not (TypeOf jDataObj("IPA") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("IPA"), JsonString).Value
                    dict.Add("IPA", strPara)
                End If
                If jDataObj.ContainsKey("plist") AndAlso Not (TypeOf jDataObj("plist") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("plist"), JsonString).Value
                    dict.Add("plist", strPara)
                End If
                If jDataObj.ContainsKey("iTunesAccount") AndAlso Not (TypeOf jDataObj("iTunesAccount") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("iTunesAccount"), JsonString).Value
                    dict.Add("iTunesAccount", strPara)
                End If
                If jDataObj.ContainsKey("updateTime") AndAlso Not (TypeOf jDataObj("updateTime") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("updateTime"), JsonString).Value
                    dict.Add("updateTime", strPara)
                End If
                If jDataObj.ContainsKey("version") AndAlso Not (TypeOf jDataObj("version") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("version"), JsonString).Value
                    dict.Add("version", strPara)
                End If
                If jDataObj.ContainsKey("ProfileExpirationDate") AndAlso Not (TypeOf jDataObj("ProfileExpirationDate") Is JsonNull) Then
                    Dim strPara As String = TryCast(jDataObj("ProfileExpirationDate"), JsonString).Value
                    dict.Add("ProfileExpirationDate", strPara)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetAGSPlanPersonalUrl")
        End Try
        Return dict
    End Function

    Private Sub AutoInstallFromWeb(strUrl As String, deviceID As String, softSKU As String, softName As String, softVersion As String, downClass As ResourceClass, strUserInstall As String)
        Dim info As New MultiThreadDownloadItemInfo()
        info.DeviceId = deviceID
        info.Url = strUrl
        info.Identifier = softSKU
        info.Version = softVersion

        info.IsSingleThread = False
        info.SaveFolder = Folder.CacheGameStoreFolder
        info.Source = TaskSource.Tongbu

        Dim strUrlPath As String = ""
        If Not String.IsNullOrEmpty(info.Url) Then
            strUrlPath = New Uri(info.Url).AbsolutePath
        End If

        If String.IsNullOrEmpty(softName) Then
            softName = System.IO.Path.GetFileNameWithoutExtension(strUrlPath)
        End If
        Dim strName As String = String.Format("{0}{1}{2}", softName, strUserInstall, System.IO.Path.GetExtension(strUrlPath))

        info.Name = System.Web.HttpUtility.UrlDecode(strName)
        info.IsUpdate = True
        info.Class = downClass
        info.Type = ResourceType.IPA

        If Utility.IsPanda() Then
            info.Name = info.Name.Replace("tbtui", "panda")
        End If

        Me.mDownManage.NewTask(info)
    End Sub

    Private Sub OnUninstallApplicationSucceed(ByVal sender As Object, ByVal e As iTong.CoreFoundation.UninstallApplicationCompletedEventArgs)
        If Me.mStrSku = e.Identifier Then
            Me.mIsDo = False
        End If
    End Sub

#End Region

#Region "--- 检测本地个人签名包 ---"
    Public Sub Start2CheckPersonSignatureGameStore()
        If mTdCheckGameStore IsNot Nothing AndAlso mTdCheckGameStore.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        mTdCheckGameStore = New Thread(AddressOf CheckPersonSignatureGameStore)
        mTdCheckGameStore.IsBackground = True
        mTdCheckGameStore.Start()
    End Sub

    Private Sub CheckPersonSignatureGameStore()
        Try
            '本地保存的信息
            Dim InfoInSetting As PackageInfo = New PackageInfo
            InfoInSetting.Version = IniSetting.GetPersonSignatureGameStoreVersion(Me.mDevice.UniqueDeviceID)
            InfoInSetting.PackagePath = IniSetting.GetPersonSignatureGameStorePath(Me.mDevice.UniqueDeviceID)
            Me.mGameStorePath = InfoInSetting.PackagePath

            If Me.mStrURL.Length <= 0 OrElse Me.mStrVersion.Length <= 0 OrElse Me.mStrProfileExpirationDate.Length <= 0 Then
                '服务器上的商店信息没获取到，再尝试获取一次
                Me.GetAGSPlanUrl(Me.mStrURL, Me.mStrVersion, Me.mStrProfileExpirationDate, mStrSku, False)
            End If
            If Me.mStrURL.Length <= 0 OrElse Me.mStrVersion.Length <= 0 OrElse Me.mStrProfileExpirationDate.Length <= 0 Then
                GoTo DoExit
            End If

            Dim isNeed2Download As Boolean = False
            Dim iSettingAndWeb As Integer = Common.CompareVer(InfoInSetting.Version, mStrVersion)
            If iSettingAndWeb < 0 Then
                '网络中获取
                isNeed2Download = True
                GoTo DoExit
            End If

            If InfoInSetting.PackagePath.Length = 0 OrElse Not File.Exists(InfoInSetting.PackagePath) Then
                '配置中可以用，文件不存在 重新下载
                isNeed2Download = True
            End If

DoExit:
            If isNeed2Download Then
                Me.AppDowmload()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckPersonSignatureGameStore")
        End Try
    End Sub

    Public Function GetGameStoreFilePath() As String
        Me.mGameStorePath = IniSetting.GetPersonSignatureGameStorePath(Me.mDevice.UniqueDeviceID)

        If Me.mGameStorePath.Length = 0 OrElse Not File.Exists(Me.mGameStorePath) Then
            Me.Start2CheckPersonSignatureGameStore()
        End If

        Return Me.mGameStorePath
    End Function

#End Region

#Region "--- 下载相关 ---"

    Private Sub OnTaskHasDownloadSucceed(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If Not item.ItemInfo.Identifier.Contains(Me.mStrSku) Then
                Return
            End If

            IniSetting.SetPersonSignatureGameStorePath(Me.mDevice.UniqueDeviceID, item.DownloadInfo.FilePath)
            IniSetting.SetPersonSignatureGameStoreVersion(Me.mDevice.UniqueDeviceID, item.ItemInfo.Version)

            Dim arg As New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, PersonSignatureState.DownloadSucceed)
            RaiseEvent AppDownloadEventHandler(Me, arg)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnTaskExist")
        End Try
    End Sub

    Private Sub OnTaskExist(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.Identifier.Contains(Me.mStrSku) = False Then
                Return
            End If

            IniSetting.SetPersonSignatureGameStorePath(Me.mDevice.UniqueDeviceID, item.DownloadInfo.FilePath)
            IniSetting.SetPersonSignatureGameStoreVersion(Me.mDevice.UniqueDeviceID, item.ItemInfo.Version)

            If item.Action = DownloadAction.Cancel Then
                Dim arg As New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, PersonSignatureState.Unknown)
                RaiseEvent AppDownloadEventHandler(Me, arg)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnTaskExist")
        End Try
    End Sub

    Public Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If item.ItemInfo.Identifier.Contains(Me.mStrSku) = False Then
            Return
        End If
        If String.IsNullOrEmpty(item.ItemInfo.Url) = False Then
            Try
                Dim arg As New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, PersonSignatureState.WaitDownloading, item.DownloadInfo.Progress)
                RaiseEvent AppDownloadEventHandler(Me, arg)

            Catch ex As Exception
                Common.LogException(ex.ToString, "OnTaskAdd")
            End Try
        End If
    End Sub

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Try
            Dim item As MultiThreadDownloadItem = sender
            If item.ItemInfo.Identifier.Contains(Me.mStrSku) = False Then
                Return
            End If
            If item.ItemInfo.Class = ResourceClass.Software Then
                Dim arg As New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, PersonSignatureState.Downloading, item.DownloadInfo.Progress)
                RaiseEvent AppDownloadEventHandler(Me, arg)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "OnDownloading")
        End Try
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Try
            Dim item As MultiThreadDownloadItem = sender
            If item.ItemInfo.Identifier.Contains(Me.mStrSku) = False Then
                Return
            End If
            Dim status As PersonSignatureState = PersonSignatureState.DownloadSucceed
            If item.ItemInfo.Class = ResourceClass.Software Then

                If e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
                    status = PersonSignatureState.Waiting

                    IniSetting.SetPersonSignatureGameStorePath(Me.mDevice.UniqueDeviceID, item.DownloadInfo.FilePath)
                    IniSetting.SetPersonSignatureGameStoreVersion(Me.mDevice.UniqueDeviceID, item.ItemInfo.Version)

                ElseIf item.DownloadInfo.Cancel Then
                    status = PersonSignatureState.DownloadFailure

                Else
                    status = PersonSignatureState.DownloadFailure

                End If

                Dim arg As New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, status, item.DownloadInfo.Progress)
                RaiseEvent AppDownloadEventHandler(Me, arg)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "OnDownloaded")
        End Try
    End Sub

    Private Sub OnTaskDelete(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If Not item.ItemInfo.Identifier.Contains(Me.mStrSku) Then
                Return
            End If

            If item.ItemInfo.Status <> TaskState.Completed AndAlso item.ItemInfo.Class = ResourceClass.Software Then
                Dim arg As New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, PersonSignatureState.DownloadFailure, item.DownloadInfo.Progress)
                RaiseEvent AppDownloadEventHandler(Me, arg)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "OnTaskDelete")
        End Try
    End Sub

    Private Sub OnTaskAppleStoreAnalyse(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.Identifier.Contains(Me.mStrSku) = False Then
                Return
            End If
            If item.ItemInfo.Class = ResourceClass.Software Then
                If Not item.ItemInfo.IsDeleted AndAlso Not String.IsNullOrEmpty(item.ItemInfo.ErrorMsg) Then
                    Dim arg As New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, PersonSignatureState.DownloadFailure, item.DownloadInfo.Progress)
                    RaiseEvent AppDownloadEventHandler(Me, arg)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnTaskAppleStoreAnalyse")
        End Try

    End Sub

#End Region

#Region "--- 安装相关 ---"

    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        Try

            If args.PackageInfo.Identifier.Contains(Me.mStrSku) = False Then
                Return
            End If

            Dim appStatus As PersonSignatureState = PersonSignatureState.Unknown
            Select Case args.InstallState

                Case InstallState.FailRetry
                    appStatus = PersonSignatureState.Failure

                Case InstallState.Waiting
                    appStatus = PersonSignatureState.Waiting

                Case InstallState.AuthBegin
                    appStatus = PersonSignatureState.Transfering

                Case InstallState.Transfering
                    appStatus = PersonSignatureState.Transfering

                Case InstallState.Installing
                    appStatus = PersonSignatureState.Installing

                Case InstallState.CancelInstall
                    appStatus = PersonSignatureState.Failure

                Case InstallState.UnSucceed, InstallState.HaveNotInstall
                    appStatus = PersonSignatureState.Failure

                Case InstallState.Succeed
                    appStatus = PersonSignatureState.Succeed

                Case InstallState.AllInstallCompleted
                    Return

                Case Else
                    appStatus = PersonSignatureState.Waiting

            End Select

            Dim arg As New PersonSignatureProgressEventArgs(Me.mDevice.UniqueDeviceID, appStatus, args.TransferProgress)
            RaiseEvent AppDownloadEventHandler(Me, arg)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "iPhoneHotAppsHelper_mInstallHelper_OnInstall")
        End Try
    End Sub

#End Region

End Class

Public Class PersonSignatureProgressEventArgs
    Inherits EventArgs

    Dim mUniqueDeviceID As String = ""
    Dim mPersonSignatureState As PersonSignatureState = PersonSignatureState.Unknown
    Dim mIntProgress As Integer = 0

    Public Property UniqueDeviceIDPS() As String
        Get
            Return Me.mUniqueDeviceID
        End Get
        Set(ByVal value As String)
            Me.mUniqueDeviceID = value
        End Set
    End Property

    Public Property PersonSignatureStatePS() As PersonSignatureState
        Get
            Return Me.mPersonSignatureState
        End Get
        Set(ByVal value As PersonSignatureState)
            Me.mPersonSignatureState = value
        End Set
    End Property

    Public Property IntProgress() As Integer
        Get
            Return Me.mIntProgress
        End Get
        Set(ByVal value As Integer)
            Me.mIntProgress = value
        End Set
    End Property

    Public Sub New()

    End Sub

    Public Sub New(strUniqueDeviceID As String, sTaskState As PersonSignatureState, Optional ByVal iProgress As Integer = 0)
        Me.mUniqueDeviceID = strUniqueDeviceID
        Me.mPersonSignatureState = sTaskState
        Me.mIntProgress = iProgress
    End Sub

End Class
Public Enum PersonSignatureState
    Unknown = 0
    GetUrlWaiting = 1
    GetUrlSucceed = 2
    GetUrlFailure = 3
    GetUrlOvertime = 4
    Waiting = 5
    Repairing = 6
    Succeed = 7
    Failure = 8
    Unavailable = 9
    Installing = 10   '修复过程中软件上传后正在安装的过程。
    Transfering = 11
    WaitDownloading = 12
    Downloading = 13
    DownloadFailure = 14
    DownloadSucceed = 15
End Enum
