﻿Imports System.Threading

Public Class LocalAppHelper

    Private mTdLoadLocalApp As Thread
    Private mListAddPackageInfo As List(Of UpdatePackageInfo) = New List(Of UpdatePackageInfo)
    Private mListDelPackageInfo As List(Of UpdatePackageInfo) = New List(Of UpdatePackageInfo)

    Private mLocalSoftManageDB As LocalSoftManageDB = Nothing

    Public Shared Event LocalAppEventHandler As EventHandler(Of LocalAppEventArgs)

    Private mIsReady As Boolean = False
    Private mListTotalLocalApp As List(Of UpdatePackageInfo) = New List(Of UpdatePackageInfo)

    Public ReadOnly Property IsReady() As Boolean
        Get
            Return mIsReady
        End Get
    End Property

    Public ReadOnly Property ListTotalLocalApp() As List(Of UpdatePackageInfo)
        Get
            Return mListTotalLocalApp
        End Get
    End Property


#Region "---  单实例  ---"

    Private Shared mInstance As LocalAppHelper = Nothing
    Private Shared ReadOnly locker As New Object

    Public Shared Function Instance() As LocalAppHelper
        If mInstance Is Nothing Then
            SyncLock locker
                If mInstance Is Nothing Then
                    mInstance = New LocalAppHelper()
                End If
            End SyncLock
        End If

        Return mInstance
    End Function

    Public Sub New()
        If Me.mLocalSoftManageDB Is Nothing Then
            Me.mLocalSoftManageDB = LocalSoftManageDB.GetInstance()
        End If
    End Sub

#End Region

    Public Sub Start2LoadLocalApp()
        If Me.mTdLoadLocalApp IsNot Nothing AndAlso Me.mTdLoadLocalApp.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTdLoadLocalApp = New Thread(AddressOf LoadLocalApp)
        Me.mTdLoadLocalApp.IsBackground = True
        Me.mTdLoadLocalApp.Start()
    End Sub

    Private Sub LoadLocalApp()
        Try
            mListDelPackageInfo = New List(Of UpdatePackageInfo)
            mListAddPackageInfo = New List(Of UpdatePackageInfo)
            '获取数据库 保存的数据 并 筛选有效数据
            Dim listAppDB As List(Of UpdatePackageInfo) = New List(Of UpdatePackageInfo)
            Dim dictAppDB As Dictionary(Of String, UpdatePackageInfo) = mLocalSoftManageDB.Get_AllSoft_info(IniSetting.GetZJLocalPackageFolder)

            If dictAppDB.Count > 0 Then
                For Each item As KeyValuePair(Of String, UpdatePackageInfo) In dictAppDB
                    Dim info As UpdatePackageInfo = item.Value
                    info.wRegion = RegionState.LocalSource
                    If File.Exists(info.PackagePath) Then
                        listAppDB.Add(info)

                    ElseIf Not mListDelPackageInfo.Contains(info) Then
                        mListDelPackageInfo.Add(info)

                    End If
                Next

                RaiseEvent LocalAppEventHandler(Me, New LocalAppEventArgs(listAppDB))
            End If

            '获取配置路径下面的ipa包信息’
            Dim listAppFolder As List(Of UpdatePackageInfo) = New List(Of UpdatePackageInfo)
            Dim strLocalAppPath As String = IniSetting.GetZJLocalPackageFolder
            Dim listAppPaths As New List(Of String)
            If Not String.IsNullOrEmpty(strLocalAppPath) AndAlso Directory.Exists(strLocalAppPath) Then

                Try
                    '有些目录没有权限读取，暂时Try Catch处理
                    listAppPaths.AddRange(My.Computer.FileSystem.GetFiles(strLocalAppPath, FileIO.SearchOption.SearchAllSubDirectories, "*.ipa", "*.pxl"))
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "LoadLocalApp")
                End Try

                For Each file As String In listAppPaths
                    Try
                        If Not file.ToLower.EndsWith(".ipa") AndAlso Not file.ToLower.EndsWith(".pxl") Then
                            Continue For
                        End If
                        Dim package As UpdatePackageInfo = Me.GetPackageInfo(dictAppDB, file)
                        listAppFolder.Add(package)

                    Catch ex As Exception
                        Common.LogException(ex.ToString, "LoadLocalApp1")
                    End Try
                Next

                For Each info As UpdatePackageInfo In listAppDB
                    If listAppFolder.Contains(info) Then
                        listAppFolder.Remove(info)
                    End If
                Next

                For Each newInfo As UpdatePackageInfo In listAppFolder
                    If Not mListAddPackageInfo.Contains(newInfo) Then
                        mListAddPackageInfo.Add(newInfo)
                    End If
                Next


                RaiseEvent LocalAppEventHandler(Me, New LocalAppEventArgs(listAppFolder))
            End If

            '更新数据库’
            mLocalSoftManageDB.Insert_soft_info(mListAddPackageInfo)
            mLocalSoftManageDB.Delete_soft_info(mListDelPackageInfo)

            '
            mListTotalLocalApp = New List(Of UpdatePackageInfo)

            For Each item As UpdatePackageInfo In listAppDB
                If Not mListTotalLocalApp.Contains(item) AndAlso File.Exists(item.PackagePath) Then
                    mListTotalLocalApp.Add(item)
                End If
            Next

            For Each item As UpdatePackageInfo In listAppFolder
                If Not mListTotalLocalApp.Contains(item) AndAlso File.Exists(item.PackagePath) Then
                    mListTotalLocalApp.Add(item)
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadLocalApp")
        End Try

        Me.mIsReady = True

    End Sub

    Private Function GetPackageInfo(ByVal dicPackagesInDatabase As Dictionary(Of String, UpdatePackageInfo), ByVal file As String) As UpdatePackageInfo

        Dim package As UpdatePackageInfo = Nothing

        '数据库获取软件信息
        If dicPackagesInDatabase.ContainsKey(file.ToLower) Then
            package = dicPackagesInDatabase(file.ToLower)
        End If

        '数据库获取不到，认为是新增加入 新增列表
        If package Is Nothing Then
            Dim temp As PackageInfo = PackageInfoReader.GetPackageInfo(file, True)
            package = Me.CTypePackageInfo2UpdatePackageInfo(temp)

            mListAddPackageInfo.Add(package)
        End If

        '获取软件名称的大写拼音，以便排序使用
        If package.Name.Length > 0 AndAlso package.PinyinSort.Length = 0 Then
            package.PinyinSort = PinYinClass.MakePinYin(package.Name, PinYinOptions.FirstCharacterOnly)
        End If
        package.wRegion = RegionState.LocalSource
        Return package
    End Function

    Private Function CTypePackageInfo2UpdatePackageInfo(ByVal info As PackageInfo) As UpdatePackageInfo
        Dim rPackage As UpdatePackageInfo = New UpdatePackageInfo
        rPackage.acctHashId = info.acctHashId
        rPackage.wIconUrl = info.AppIconUrl
        rPackage.AppIconOriginal = info.AppIconOriginal
        rPackage.AppIconUrl = info.AppIconUrl
        rPackage.AppleId = info.AppleId
        rPackage.AppleVersion = info.AppleVersion
        rPackage.ApplicationDSID = info.ApplicationDSID
        rPackage.ApplicationType = info.ApplicationType
        rPackage.ArtistId = info.ArtistId
        rPackage.ArtistName = info.ArtistName
        rPackage.CFBundleVersion = info.CFBundleVersion
        rPackage.CosignedName = info.CosignedName
        rPackage.CrackedInfo = info.CrackedInfo
        rPackage.DeviceId = info.DeviceId
        rPackage.DisplayName = info.DisplayName
        rPackage.ExecutableName = info.ExecutableName
        rPackage.FileSize = info.FileSize
        rPackage.Genre = info.Genre
        rPackage.GenreId = info.GenreId
        rPackage.Identifier = info.Identifier
        rPackage.InSDCard = info.InSDCard
        rPackage.InstallDate = info.InstallDate
        rPackage.IsAbroad = info.IsAbroad
        rPackage.IsDownFromCacheFirst = info.IsDownFromCacheFirst
        rPackage.IsDownFromCacheFirst = info.IsFree
        rPackage.IsRepair = info.IsRepair
        rPackage.IsUpdate = info.IsUpdate
        rPackage.ItemId = info.ItemId
        rPackage.LastModifyDate = info.LastModifyDate
        rPackage.LocalHighestVersion = info.LocalHighestVersion
        rPackage.LoginAppleId = info.LoginAppleId
        rPackage.MinimumOSVersion = info.MinimumOSVersion
        rPackage.UpdateType = info.UpdateType
        rPackage.Name = info.Name
        rPackage.OnlyInstallRecord = info.OnlyInstallRecord
        rPackage.PackagePath = info.PackagePath
        rPackage.PackageType = info.PackageType
        rPackage.Permissions = info.Permissions
        rPackage.PinyinSort = info.PinyinSort
        rPackage.Price = info.Price
        rPackage.PurchaseDate = info.PurchaseDate
        rPackage.SDKInnerVersion = info.SDKInnerVersion
        rPackage.SDKVersion = info.SDKVersion
        rPackage.Size = info.Size
        rPackage.SoftWatchState = info.SoftWatchState
        rPackage.StoreId = info.StoreId
        rPackage.TuiType = info.TuiType
        rPackage.UIDeviceFamily = info.UIDeviceFamily
        rPackage.UIFileSharingEnabled = info.UIFileSharingEnabled
        rPackage.UrlSchemes = info.UrlSchemes
        rPackage.Version = info.Version
        rPackage.VersionId = info.VersionId
        rPackage.VersionNumber = info.VersionNumber
        rPackage.wIconUrl = info.AppIconUrl
        rPackage.wRegion = RegionState.LocalSource
        Return rPackage
    End Function

End Class

Public Class LocalAppEventArgs
    Inherits EventArgs

    Dim listDelPackageInfo As List(Of UpdatePackageInfo)

    Public Sub New(ByVal list As List(Of UpdatePackageInfo))
        Me.listDelPackageInfo = list
    End Sub

    Public Property mListDelPackageInfo() As List(Of UpdatePackageInfo)
        Get
            Return Me.listDelPackageInfo
        End Get
        Set(ByVal value As List(Of UpdatePackageInfo))
            Me.listDelPackageInfo = value
        End Set
    End Property

End Class