﻿Imports System.Threading

Public Class AppleStoreHelper

    'Private mTdCheckDownload As Thread
    'Private mVersion As String = ""

    'Private mVersionInResource As String = "1.0.4"
    'Private mVersionInWeb As String = ""
    'Private mVersionInSetting As String = ""
    'Private mFilePathGameStore As String = ""

    Private mInfoInResource As PackageInfo = New PackageInfo()
    Private mInfoInWeb As PackageInfo = New PackageInfo
    Private mInfoInSetting As PackageInfo = New PackageInfo

    '如果替换包，要修改此版本好，与资源中的GameStore版本号一致
    Private mVersion As String = "1.2.6"
    Private mGameStorePath As String = ""

    Private mTdCheckGameStore As Thread

    Private Shared mLstSku As New List(Of String)

    Private mIsPersonSignature As Boolean = False

    Public ReadOnly Property IsPersonSignature() As Boolean
        Get
            Return Me.mIsPersonSignature
        End Get
    End Property

    Public Shared ReadOnly Property ListSku() As List(Of String)
        Get
            If mLstSku.Count = 0 Then
                mLstSku.Add("com.zjhelper.AGSPlan")
                mLstSku.Add("com.tongbu.AGSPlan")
            End If
            Return mLstSku
        End Get
    End Property
    '    Get
    '        Return mSKU
    '    End Get
    'End Property

    'Private mSKU As String = "com.tongbu.AGSPlan"
    'Public ReadOnly Property SKU() As String
    '    Get
    '        Return mSKU
    '    End Get
    'End Property

    Public ReadOnly Property GameStorePath()
        Get
            Return Me.mGameStorePath
        End Get
    End Property

#Region "---  单实例  ---"

    Private Shared mInstance As AppleStoreHelper = Nothing
    Private Shared ReadOnly locker As New Object

    Public Shared Function Instance() As AppleStoreHelper
        If mInstance Is Nothing Then
            SyncLock locker
                If mInstance Is Nothing Then
                    mInstance = New AppleStoreHelper()
                End If
            End SyncLock
        End If

        Return mInstance
    End Function

    Public Sub New()
    End Sub

#End Region

    Public Sub Start2CheckGameStore()
        If mTdCheckGameStore IsNot Nothing AndAlso mTdCheckGameStore.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        mTdCheckGameStore = New Thread(AddressOf CheckGameStore)
        mTdCheckGameStore.IsBackground = True
        mTdCheckGameStore.Start()
    End Sub

    Private Sub CheckGameStore()
        Try
            Me.GetGameStoreInfoFromWeb()

            Me.mInfoInSetting.Version = IniSetting.GetGameStoreVersion
            Me.mInfoInSetting.PackagePath = IniSetting.GetGameStorePath

            Me.mInfoInResource.Name = "com.zjhelper.AGSPlan"
            Me.mInfoInResource.Version = mVersion

            Dim iSettingAndResource As Integer = Common.CompareVer(Me.mInfoInSetting.Version, Me.mInfoInResource.Version)
            Dim iSettingAndWeb As Integer = Common.CompareVer(Me.mInfoInSetting.Version, Me.mInfoInWeb.Version)
            Dim iResourceAndWeb As Integer = Common.CompareVer(Me.mInfoInResource.Version, Me.mInfoInWeb.Version)
            Dim isNeed2ReleaseResource As Boolean = True

            If iSettingAndResource >= 0 AndAlso iSettingAndWeb >= 0 Then
                If Me.mInfoInSetting.PackagePath.Length > 0 AndAlso File.Exists(Me.mInfoInSetting.PackagePath) Then
                    '配置中可以用，文件是否存在
                    Me.mGameStorePath = Me.mInfoInSetting.PackagePath
                    isNeed2ReleaseResource = False

                ElseIf iResourceAndWeb < 0 Then
                    '虽然配置的版本比较高，但是文件不存在；并且服务器的包已经有更新了，所以需要再下载一份最新的包下来，防止资源的包签名过期了
                    AppleStoreHelper.DownloadCrakedSoft(Me.mInfoInWeb.AppIconUrl, "", "", Me.mInfoInWeb.Name, Me.mInfoInWeb.Version, Me.mInfoInWeb.Identifier, Folder.CacheGameStoreFolder, True, DeviceType.iOS)
                End If

            ElseIf iSettingAndWeb < 0 AndAlso iResourceAndWeb < 0 Then
                '从网络中获取
                AppleStoreHelper.DownloadCrakedSoft(Me.mInfoInWeb.AppIconUrl, "", "", Me.mInfoInWeb.Name, Me.mInfoInWeb.Version, Me.mInfoInWeb.Identifier, Folder.CacheGameStoreFolder, True, DeviceType.iOS)

                If File.Exists(Me.mInfoInSetting.PackagePath) Then
                    isNeed2ReleaseResource = False
                End If
            End If

            If isNeed2ReleaseResource Then
                '先使用资源的，如果有下载再更新
                Me.ReleaseGameStoreFromResource()
            End If

        Catch ex As Exception
        End Try
    End Sub

    Private Sub GetGameStoreInfoFromWeb()
        'Dim strContent As String = "{""name"": ""苹果游戏商店_0527"",""sku"": ""com.tongbu.AGSPlan"",""version"": ""1.0"",""url"": """"}"
        Dim strRequestUrl As String = String.Format("http://cdata.tongbu.com/tbCloud/ZJDS/Update.aspx?uuid={0}", Guid.NewGuid().ToString("N"))
        Dim strContent As String = Utility.GetContentStringFromUrl(strRequestUrl, System.Text.Encoding.UTF8)
        If strContent.Length = 0 Then
            Me.mIsPersonSignature = True
            Return
        End If

        Dim objJson As JsonObject = JsonParser.ParseString(strContent)
        If objJson.ContainsKey("name") Then
            Me.mInfoInWeb.Name = CType(objJson("name"), JsonString).ToString()
        End If
        If objJson.ContainsKey("sku") Then
            Me.mInfoInWeb.Identifier = CType(objJson("sku"), JsonString).ToString()
        End If
        If objJson.ContainsKey("version") Then
            Me.mInfoInWeb.Version = CType(objJson("version"), JsonString).ToString()
        End If
        If objJson.ContainsKey("url") Then
            Me.mInfoInWeb.AppIconUrl = CType(objJson("url"), JsonString).ToString()
            If Me.mInfoInWeb.AppIconUrl.Length <= 0 Then
                Me.mIsPersonSignature = True
            End If
        End If


    End Sub

    Private Sub ReleaseGameStoreFromResource()
        Dim strName As String = String.Format("{0}_v{1}_{2}.ipa", "com.zjhelper.AGSPlan", Me.mVersion, Now.ToString("yyMMddHH"))
        Dim strPath As String = Path.Combine(Folder.CacheGameStoreFolder, strName)

        If Not File.Exists(strPath) Then
            Using objWriter As New FileStream(strPath, FileMode.OpenOrCreate, FileAccess.Write)
                objWriter.Write(My.Resources.ipa_AGSPlan, 0, My.Resources.ipa_AGSPlan.Length)
            End Using
        End If

        IniSetting.SetGameStorePath(strPath)
        IniSetting.SetGameStoreVersion(Me.mVersion)
        Me.mInfoInSetting.Version = Me.mVersion
        Me.mGameStorePath = strPath
    End Sub

    Public Sub CheckUpdateFilePathSetting(ByVal info As MultiThreadDownloadItemInfo)
        For Each strSku As String In ListSku
            If String.Compare(strSku, info.Identifier, True) = 0 Then
                Me.mGameStorePath = info.FilePath
                IniSetting.SetGameStorePath(info.FilePath)

                Me.mInfoInSetting.Version = info.Version
                IniSetting.SetGameStoreVersion(info.Version)
                Exit For
            End If
        Next
    End Sub

    Public Function GetGameStoreFilePath() As String
        If Me.mGameStorePath.Length = 0 OrElse Not File.Exists(Me.mGameStorePath) Then
            Me.Start2CheckGameStore()
        End If

        While Me.mGameStorePath.Length = 0
            Utility.WaitSeconds(0.5)
        End While

        Return Me.mGameStorePath
    End Function

    Public Function GetOpenUDIDInfoFromAppleStore(ByVal iPhone As iPhoneDevice) As InstallState
        Dim result As InstallState = InstallState.CannotGetOpenUDIDInfo
        Try
            If iPhone.ZJ_OpenUDID.Length > 0 OrElse iPhone.ZJ_IDFA.Length > 0 OrElse iPhone.ZJ_IDFV.Length > 0 Then
                result = InstallState.Nomal
                GoTo DoExit
            End If

            Dim afcHandler As System.IntPtr = GetAfcByFileSharingEx(iPhone) ' iPhone.GetAfcByFileSharing(mSKU, HouseArrestType.None, True)
            If afcHandler = IntPtr.Zero Then
                Common.Log("没有安装苹果游戏商店")
                'iPhoneInstallHelper.GetInstance(iPhone).InstallAGSPlan()
                GoTo DoExit
            End If

            Dim strDevicePathOnPC As String = Path.Combine(Folder.TempFolder, Guid.NewGuid().ToString("N"))
            If Not iPhone.DownFromPhone(afcHandler, "/Documents/DeviceInfo", strDevicePathOnPC) Then
                Common.Log(String.Format("没有找到设备信息 afcHandler:{0}", afcHandler))
                GoTo DoExit
            End If

            Dim strInfoDes As String = String.Empty
            Using sr As New StreamReader(strDevicePathOnPC)
                strInfoDes = sr.ReadToEnd
            End Using

            Dim strInfo As String = Common.DecryptDES(strInfoDes, "&22%383@", "&22%383@")
            Dim infoJson As JsonObject = JsonParser.ParseString(strInfo)
            If infoJson IsNot Nothing Then
                Try
                    iPhone.ZJ_OpenUDID = CType(infoJson("OPENUDID"), JsonString).Value
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "GetOpenUDIDInfoFromAppleStore-OPENUDID")
                End Try
                Try
                    iPhone.ZJ_IDFA = CType(infoJson("IDFA"), JsonString).Value
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "GetOpenUDIDInfoFromAppleStore-IDFA")
                End Try
                Try
                    iPhone.ZJ_IDFV = CType(infoJson("IDFV"), JsonString).Value
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "GetOpenUDIDInfoFromAppleStore-IDFV")
                End Try

                result = InstallState.Nomal
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

DoExit:
        Return result
    End Function

#Region "---  上传安装记录  ---"

    Private Shared mLockUploadIsNewDevice As New Object
    Private mListUploadIsNewDevice As New List(Of String)

    Public Sub UploadIsNewDevice(ByVal device As iPhoneDevice, ByVal isNewDevice As Boolean)
        Dim iNewDeviceSign As Integer = IIf(isNewDevice, 1, 0)

        SyncLock mLockUploadIsNewDevice
            Try
                If device Is Nothing OrElse device.IsConnected = False OrElse Not PluginLogin.Instance.IsLogin Then
                    Return
                End If
                If Me.mListUploadIsNewDevice.Contains(device.SerialNumber) Then
                    Return
                End If

                Dim result As Boolean = False
                Dim afcHandler As System.IntPtr = GetAfcByFileSharingEx(device) 'device.GetAfcByFileSharing(mSKU, HouseArrestType.None, True)

                If InstallStore(afcHandler, device) = False Then
                    Return
                End If
                If afcHandler = System.IntPtr.Zero Then
                    Return
                End If

                Dim strDesResult As String = CreateJsonDataByNewDevice(device, iNewDeviceSign)
                If String.IsNullOrEmpty(strDesResult) Then
                    Common.Log("苹果游戏商店生成json为空")
                    Return
                End If

                Dim strFilePath As String = Folder.GetTempFilePath()
                Using sw As New StreamWriter(strFilePath, False, System.Text.Encoding.UTF8)
                    sw.Write(strDesResult)
                End Using
                If Not File.Exists(strFilePath) Then
                    Common.Log("生成文件为空:" & strFilePath)
                End If

                Dim strPhonePath As String = "/Documents/info/device.plist"
                Dim iTryCount As Integer = 0
                Dim isExist As Boolean = device.Exists(afcHandler, "/Documents/info")
                While isExist = False AndAlso iTryCount < 3
                    Common.LogException(String.Format("/Documents/info不存在 TryCount:{0} afcHandler:{1}", iTryCount, afcHandler))
                    iTryCount += 1
                    device.CreateDirectory(afcHandler, "/Documents/info")

                    isExist = device.Exists(afcHandler, "/Documents/info")
                    If Not isExist Then
                        Utility.WaitSeconds(0.3)
                    End If
                End While

                result = device.CopyToPhone(afcHandler, strFilePath, strPhonePath)
                If result Then
                    If isNewDevice Then
                        '有可能用户一开始读取的是老设备，就先写上去，后续读取到新设备，要重新写一次，所以不是新设备的话，就不缓存起来。
                        Me.mListUploadIsNewDevice.Add(device.SerialNumber)
                    End If
                    Common.LogException("上传txt到苹果游戏商店成功:" & device.SerialNumber)
                Else
                    Common.LogException("上传txt到苹果游戏商店失败:" & device.SerialNumber)
                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "UploadIsNewDevice")
            End Try
        End SyncLock
    End Sub

    '上传txt和Icon文件到AGSPlan
    Public Sub UploadRecord(ByVal args As WealthHelperArgs)
        Dim afcHandler As System.IntPtr = GetAfcByFileSharingEx(CType(args.Device, iPhoneDevice)) ' CType(args.Device, iPhoneDevice).GetAfcByFileSharing(mSKU, HouseArrestType.None, False)

        If InstallStore(afcHandler, CType(args.Device, iPhoneDevice)) = False Then
            Return
        End If

        '1. 生成json文件
        Dim strDesResult As String = CreateJsonData(args.PackagesFromLocal)

        If String.IsNullOrEmpty(strDesResult) Then
            Common.LogException("苹果游戏商店生成json为空")
            Return
        End If

        Dim strFilePath As String = Path.Combine(Folder.TempFolder, Guid.NewGuid().ToString)
        If Not Directory.Exists(Folder.TempFolder) Then
            Directory.CreateDirectory(Folder.TempFolder)
        End If

        Using sw As New StreamWriter(strFilePath, False, System.Text.Encoding.UTF8)
            sw.Write(strDesResult)
        End Using
        If Not File.Exists(strFilePath) Then
            Common.LogException("生成文件为空:" & strFilePath)
        End If

        '2. 上传Icon到设备
        Dim result As Boolean = False
        Dim strIconName As String = ""

        strIconName = String.Format("/Documents/icon/{0}_v{1}.png", args.PackagesFromLocal.Identifier, args.PackagesFromLocal.Version)
        If CType(args.Device, iPhoneDevice).Exists(afcHandler, strIconName) Then
            CType(args.Device, iPhoneDevice).DeleteFile(afcHandler, strIconName)
        End If

        If Not CType(args.Device, iPhoneDevice).Exists(afcHandler, "/Documents/icon") Then
            CType(args.Device, iPhoneDevice).CreateDirectory(afcHandler, "/Documents/icon")
        End If

        result = CType(args.Device, iPhoneDevice).CopyToPhone(afcHandler, args.PackagesFromLocal.AppIconUrl, strIconName)

        If result Then
            Common.LogException(String.Format("上传Icon到苹果游戏商店成功：{0}", args.PackagesFromLocal.Identifier))
        Else
            Common.LogException(String.Format("上传Icon到苹果游戏商店失败：{0}", args.PackagesFromLocal.Identifier))
        End If

        '3. 上传txt到设备
        Dim strPhonePath As String = String.Format("/Documents/txt/TBJSON{0}.txt", Now.ToString("yyyyMMddHHmmss"))
        If Not CType(args.Device, iPhoneDevice).Exists(afcHandler, "/Documents/txt") Then
            CType(args.Device, iPhoneDevice).CreateDirectory(afcHandler, "/Documents/txt")
        End If
        result = CType(args.Device, iPhoneDevice).CopyToPhone(afcHandler, strFilePath, strPhonePath)
        If result Then
            Common.LogException("上传txt到苹果游戏商店成功")
        Else
            Common.LogException("上传txt到苹果游戏商店失败")
        End If

        Me.SaveWriteLog(args.Device, result, args.PackagesFromLocal)
    End Sub

    Private Function InstallStore(ByRef afcHandler As System.IntPtr, ByVal device As iPhoneDevice) As Boolean
        Dim isResult As Boolean = True

        If afcHandler <> IntPtr.Zero Then
            GoTo Do_Exit
        End If
        Utility.WaitSeconds(0.5)
        afcHandler = GetAfcByFileSharingEx(device) ' device.GetAfcByFileSharing(mSKU, HouseArrestType.None, True)
        If afcHandler <> IntPtr.Zero Then
            GoTo Do_Exit
        End If

        If Not IniSetting.GetCheckInstallGameStore Then
            isResult = False
            GoTo Do_Exit
        End If

        Common.Log("没有安装苹果游戏商店，尝试安装")
        '尝试安装
        If Not File.Exists(Me.mGameStorePath) Then
            Common.Log("安装文件不存在")
            isResult = False
            GoTo Do_Exit
        End If
        Dim isSucceed As Boolean = device.InstallApplication(Me.mGameStorePath, Nothing, Nothing, Nothing, Nothing)
        If Not isSucceed Then
            Common.Log("安装苹果游戏商店失败")
            isResult = False
            GoTo Do_Exit
        End If

        Common.Log("安装苹果游戏商店成功")

        '获取AFC
        afcHandler = GetAfcByFileSharingEx(device) ' device.GetAfcByFileSharing(mSKU, HouseArrestType.None, True)
        If afcHandler = IntPtr.Zero Then
            Common.Log("苹果游戏商店afc为0")
            isResult = False
            GoTo Do_Exit
        End If

Do_Exit:
        Return isResult
    End Function

    Private Function CreateJsonDataByNewDevice(ByVal device As iPhoneDevice, ByVal newDeviceSign As Integer) As String
        Dim strJsonResult As String = String.Empty
        Dim isRetry As Boolean = False

DoRetry:
        Try
            Dim writer As New JsonWriter()
            Dim dicJson As New JsonObject()
            dicJson.Add("uid", PluginLogin.Instance().Uid)
            dicJson.Add("sn", device.SerialNumber)
            dicJson.Add("new", newDeviceSign)
            dicJson.Add("imei", device.IMEI)
            dicJson.Add("udid", device.UniqueDeviceID)
            dicJson.Add("devicemac", device.WiFiAddress)

            dicJson.Write(writer)
            strJsonResult = writer.ToString()

            Common.Log(strJsonResult)
            strJsonResult = Utility.EncryptDES(strJsonResult, "&22%383@", "&22%383@")

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateJsonDataByNewDevice")

            If isRetry Then
                Return strJsonResult
            End If

            If Not isRetry Then
                isRetry = True
                GoTo DoRetry
            End If
        End Try

        Return strJsonResult
    End Function

    Private Function CreateJsonData(ByVal package As PackageInfo) As String
        Dim strJsonResult As String = String.Empty
        Dim strIconName As String = String.Empty
        Dim objJson As New JsonObject()
        Dim arrJson As New JsonArray()
        Dim dicJson As New JsonObject()
        Dim writer As New JsonWriter()

        Dim tempPackage As PackageInfo = Nothing

        If String.IsNullOrEmpty(package.ExecutableName) OrElse _
           String.IsNullOrEmpty(package.UrlSchemes) OrElse _
           String.IsNullOrEmpty(package.Tag) Then

            Debug.Print(package.PackagePath & vbCrLf & "ExecutableName：" & package.ExecutableName & vbCrLf & "UrlSchemes：" & package.UrlSchemes)

            Common.LogException("此包不需要添加到苹果游戏商店")
            Common.LogException(package.PackagePath & vbCrLf & "ExecutableName：" & package.ExecutableName & vbCrLf & "UrlSchemes：" & package.UrlSchemes)
            GoTo DoCreate
        End If

        strIconName = String.Format("{0}_v{1}.png", package.Identifier, package.Version)
        dicJson.Add("sku", package.Identifier)
        dicJson.Add("process", package.ExecutableName)
        dicJson.Add("url", package.UrlSchemes)
        dicJson.Add("appname", package.Name)
        dicJson.Add("version", package.Version)
        dicJson.Add("icon", strIconName)
        dicJson.Add("pcdata", package.Tag.ToString())
        arrJson.Add(dicJson)

        tempPackage = package

DoCreate:
        If arrJson.Count > 0 Then
            objJson.Add("apps", arrJson)
            objJson.Write(writer)
            strJsonResult = writer.ToString()

            Common.Log(String.Format("生成json：{0}", tempPackage.Name))
            Common.Log(strJsonResult)
            strJsonResult = Utility.EncryptDES(strJsonResult, "&22%383@", "&22%383@")
        End If

        Return strJsonResult
    End Function

#End Region

#Region "--- 记录日志 ---"

    Public Sub SaveWriteLog(ByVal device As iPhoneDevice, ByVal isSucceed As Boolean, ByVal package As FileSharingPackageInfo)
        Try
            Dim time As String = Common.StringFormat(DateTime.Now.ToLongTimeString(), 10, False, True)
            Dim strIsSucceed As String = Common.StringFormat(isSucceed.ToString(), 15, False, True)
            Dim sku As String = Common.StringFormat(package.Identifier, 70, False, True)
            Dim name As String = Common.StringFormat(package.Name, 50, False, True)
            Dim version As String = Common.StringFormat(package.Version, 10, False, True)
            Dim process As String = Common.StringFormat(package.ExecutableName, 50, False, True)
            Dim url As String = Common.StringFormat(package.UrlSchemes, 50, False, True)
            Dim packagePath As String = Common.StringFormat(package.PackagePath, 100, False, True)

            Dim text As String = time & strIsSucceed & sku & name & version & process & url & packagePath

            LogForWrite(device.DeviceName, device.Identifier, text)

        Catch ex As Exception
            Common.LogException("SaveWriteLog保存安装出现异常。", "SaveWriteLog")
        End Try
    End Sub

    Private lockerWrite As New Object
    Public Sub LogForWrite(ByVal deviceName As String, ByVal udid As String, ByVal text As String)
        'If Not File.Exists(System.IO.Path.Combine(Folder.AppFolder, "debug.dll")) Then
        '    Return
        'End If

        Dim dir As String = "Data\GameStore"
        Dim path As String = Folder.DocumentFolder.TrimEnd("\"c) & "\" & dir & "\" & DateTime.Now.ToString("yyyyMMdd")

        SyncLock lockerWrite
            Try
                If Not Directory.Exists(path) Then
                    Directory.CreateDirectory(path)
                End If

                Dim name As String = Common.ReplaceWinIllegalName(deviceName & "_" & udid)
                Dim txtPath As String = path & "\" & name & ".txt"

                Dim head As String = ""
                If Not File.Exists(txtPath) Then
                    head = Common.StringFormat("Time", 10, False, True) & _
                                Common.StringFormat("Succeed", 15, False, True) & _
                                Common.StringFormat("SKU", 70, False, True) & _
                                Common.StringFormat("Name", 50, False, True) & _
                                Common.StringFormat("Version", 10, False, True) & _
                                Common.StringFormat("Process", 20, False, True) & _
                                Common.StringFormat("Url", 50, False, True) & _
                                Common.StringFormat("Path", 100, False, True)
                End If

                Using writer As New StreamWriter(txtPath, True, Encoding.GetEncoding("gb2312"))
                    If head.Length > 0 Then
                        writer.WriteLine(head)
                    End If
                    writer.WriteLine(text)
                    writer.Flush()
                    writer.Close()
                End Using
            Catch ex As Exception
                Common.LogException(ex.ToString(), "LogForInstall")
            End Try
        End SyncLock
    End Sub

#End Region

#Region "--- 安装游戏商店  ---"

    '判断AGSPlan是否有安装
    Public Function IsInstallGameStore(ByVal device As iPhoneDevice) As String
        Dim afcHandler As System.IntPtr = GetAfcByFileSharingEx(device) 'device.GetAfcByFileSharing(mSKU, HouseArrestType.None, True)
        Dim isExist As Boolean = System.IntPtr.Zero <> afcHandler

        Return isExist
    End Function

#End Region

    Public Function DownloadWealthSoft(ByVal package As UpdatePackageInfo) As Boolean

        Dim isCrack As Boolean = True
        If package.CrackedInfo = CrakedInfo.UnCraked Then
            isCrack = False
        End If

        AppleStoreHelper.DownloadCrakedSoft(package.wDownloadUrl, package.wIconUrl, package.DeviceId, package.Name, package.Version, package.Identifier, "", isCrack, package.wDeviceType, package.wIsNewAreaApp)
    End Function

    Public Shared Function DownloadCrakedSoft(ByVal url As String, _
                                   ByVal iconUrl As String, _
                                   ByVal deviceId As String, _
                                   ByVal name As String, _
                                   ByVal version As String, _
                                   ByVal identifier As String, _
                                   ByVal type As DeviceType, Optional ByVal rClass As ResourceClass = ResourceClass.Software) As Boolean

        DownloadCrakedSoft(url, iconUrl, deviceId, name, version, identifier, "", True, type, rClass)
    End Function

    '下载财富通道的接口
    Public Shared Function DownloadCrakedSoft(ByVal url As String, _
                                       ByVal iconUrl As String, _
                                       ByVal deviceId As String, _
                                       ByVal name As String, _
                                       ByVal version As String, _
                                       ByVal identifier As String, _
                                       ByVal savePath As String, _
                                       ByVal isCrack As Boolean, _
                                       ByVal type As DeviceType, _
                                       Optional ByVal rClass As ResourceClass = ResourceClass.Software, _
                                      Optional ByVal isNameFormat As Boolean = True) As Boolean

        Dim info As MultiThreadDownloadItemInfo = Nothing
        Dim listUrl As New List(Of KeyValuePair(Of String, String))

        url = System.Web.HttpUtility.UrlDecode(url)

        Try
            Dim key As New KeyValuePair(Of String, String)(url, "")
            listUrl.Add(key)
            listUrl.Add(key)

            info = New MultiThreadDownloadItemInfo
            With info
                .ListUrl = listUrl
                .Identifier = identifier
                .Version = version
                .LoadId = ""
                .SourceUrl = ""
                .SaveFolder = Folder.DownloadFolder
                .Type = ResourceType.IPA
                .Class = rClass
                .IsUpdate = True
                .DeviceId = deviceId
                .IconUrl = iconUrl

                If isNameFormat Then
                    .Name = GetDownloadFileName(name, version, isCrack, type)
                Else
                    .Name = name
                End If

                If savePath.Length > 0 Then
                    .SaveFolder = savePath
                End If

                If Not isCrack Then
                    .ItemId = "-1" '如ItemId=-1表示这是正版软件
                End If

                If .Name.Length = 0 Then
                    info.Name = HttpUtility.UrlDecode(System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath))
                End If
            End With

        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try

        MultiThreadDownload.Instance().NewTask(info)
    End Function

    Public Shared Function GetDownloadFileName(ByVal name As String, ByVal version As String, ByVal isCrack As Boolean, ByVal type As DeviceType) As String
        If String.IsNullOrEmpty(name) AndAlso String.IsNullOrEmpty(version) Then
            Return ""
        End If

        Dim strCrack As String = ""
        If Not isCrack Then
            strCrack = "_正版"
        End If
        Dim strSuffix As String = IIf(type = DeviceType.iOS, "ipa", "apk")

        Return String.Format("{0}_v{1}{2}.{3}", name, version, strCrack, strSuffix)
    End Function

    Public Shared Function GetAfcByFileSharingEx(ByVal iPhone As iPhoneDevice) As System.IntPtr
        Dim afcHandler As System.IntPtr = IntPtr.Zero
        For Each strSku As String In ListSku
            afcHandler = iPhone.GetAfcByFileSharing(strSku, HouseArrestType.None, True)
            If afcHandler <> IntPtr.Zero Then
                Exit For
            End If
        Next
        Return afcHandler
    End Function

End Class
