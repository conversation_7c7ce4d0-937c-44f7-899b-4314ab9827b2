﻿Public Class frmActivationHelp

    Private mAllowShowCheckBox As Boolean = True
    Private mView As View

#Region "--- 初始化 ---"

    Public Sub New(ByVal view As View, Optional ByVal AllowShowCheckBox As Boolean = True)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong
        Me.FilletRadius = 6
        Me.AutoSetFont = False

        '多线程中new出来的窗体需要clone，防止出现 System.InvalidOperationException: 对象当前正在其他地方使用。
        Me.tbGuiBackground = My.Resources.frm_bg_blank.Clone()

        Me.mView = view
        Me.mAllowShowCheckBox = AllowShowCheckBox
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.SetViewStyle()
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click, btn_Visible.Click
        Me.CloseForm()
    End Sub

    Private Sub CloseForm()

        If Me.chkRemember.Visible Then
            Dim blnValue As Boolean = Me.chkRemember.Checked
            Select Case Me.mView
                Case View.TipAppleStore
                    IniSetting.SetRemindOnCloseFormActivation(Not blnValue)

                Case View.CourseGetDeviceInfo
                    IniSetting.SetShowGetDeviceInfoCourse(Not blnValue)
            End Select
        End If

        Me.Close()
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Sub SetViewStyle()
        Me.chkRemember.Visible = False
        If Me.mAllowShowCheckBox = True Then
            Me.chkRemember.Visible = (Me.mView <> View.CourseActive)
            Me.chkRemember.Checked = False
        End If

        Select Case Me.mView
            Case View.TipAppleStore
                Me.tpnlContainer.ColumnStyles(0).SizeType = SizeType.Percent
                Me.tpnlContainer.ColumnStyles(0).Width = 0
                Me.tpnlContainer.ColumnStyles(1).SizeType = SizeType.Percent
                Me.tpnlContainer.ColumnStyles(1).Width = 100

                Me.Size = New Size(400, 200)
                Me.Text = "激活通知"

            Case View.CourseActive
                Me.tpnlContainer.ColumnStyles(0).SizeType = SizeType.Percent
                Me.tpnlContainer.ColumnStyles(0).Width = 100
                Me.tpnlContainer.ColumnStyles(1).SizeType = SizeType.Percent
                Me.tpnlContainer.ColumnStyles(1).Width = 0

                Me.Size = New Size(600, 500)
                Me.Text = "激活教程"

                '展示网页
                With Me.wbsHelper
                    .Anchor = AnchorStyles.None
                    .Dock = DockStyle.None
                    .Location = New Point(-2, -2)
                    .Size = New Size(Me.Size.Width - 3, Me.Size.Height - 75)
                    .Anchor = AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top Or AnchorStyles.Bottom
                End With

                Me.wbsHelper.Navigate("http://zj.tongbu.com/html/zjTutorials_2.0/index.html") 'http://zj.tongbu.com/html/zjTutorials/index.html’

            Case View.CourseGetDeviceInfo
                Me.tpnlContainer.ColumnStyles(0).SizeType = SizeType.Percent
                Me.tpnlContainer.ColumnStyles(0).Width = 100
                Me.tpnlContainer.ColumnStyles(1).SizeType = SizeType.Percent
                Me.tpnlContainer.ColumnStyles(1).Width = 0

                Me.Size = New Size(600, 500)
                Me.Text = "如何获取设备信息"

                '展示网页
                With Me.wbsHelper
                    .Anchor = AnchorStyles.None
                    .Dock = DockStyle.None
                    .Location = New Point(-2, -2)
                    .Size = New Size(Me.Size.Width - 3, Me.Size.Height - 75)
                    .Anchor = AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top Or AnchorStyles.Bottom
                End With

                Me.wbsHelper.Navigate("http://zj.tongbu.com/html/help.html")
        End Select
    End Sub

#End Region

    Public Enum View
        TipAppleStore
        CourseActive
        CourseGetDeviceInfo
    End Enum

End Class