﻿Imports System.Threading

Public Class WealthHelper

    Public Event CheckSoftExistEvent As EventHandler(Of WealthHelperArgs)
    Public Event RefreshListEvent As EventHandler(Of WealthHelperArgs)
    Public Event CheckCanInstallEventHandler As EventHandler(Of ZJCheckResultArgs)

    Private Const DESKEY As String = "ac68!3# 1"
    Private mCookieContainer As New System.Net.CookieContainer

    Private mAppVersion As String = String.Empty

    Private mtdLogin As Thread
    Private mtdInstallSucceed As Thread
    Private mQueueInstallSucceed As New Queue(Of WealthHelperArgs)
    Private mLockInstallSucceed As New Object
    Private mtdPostInstallSucceed As Thread

    Private mLstPackagesTotal As List(Of UpdatePackageInfo) = Nothing
    Private mLstPackageNewDevice As List(Of UpdatePackageInfo) = Nothing
    'Private mLstPackageAnzhuo As List(Of UpdatePackageInfo) = Nothing

    'Private mLstPackages_Zhuangjibibei As List(Of UpdatePackageInfo) = Nothing
    'Private mLstPackages_RemenYouxi As List(Of UpdatePackageInfo) = Nothing
    'Private mLstPackages_Ruanjian As List(Of UpdatePackageInfo) = Nothing

    Private mUrlInstall As String = "http://zj.tongbu.com/api/install"
    Public mUrlPostRealtime As String = "http://zjds.api.tongbu.com/Api.ashx?SendToAdvertiser"

    '"http://cdata.tongbu.com/tbCloud/ZJDS/DataCenter.aspx" 正版化 新接口 20160131 by chenbihai


    Private mUrlList As String = "http://zj.tongbu.com/api/regionlist" '包含4个分区的链接
    Private mUrlNewDevice As String = "http://zj.tongbu.com/api/regionlist"

    'Private mUrlList_Zhuangjibibei = "http://zj.tongbu.com/api/topiclist"                   '热门必备
    'Private mUrlList_RemenYouxi = "http://zj.tongbu.com/api/topiclist"                     '热门游戏
    'Private mUrlList_Ruanjian = "http://zj.tongbu.com/api/topiclist"                           '热门应用

    Private mUrlCheckPop As String = "http://zj.tongbu.com/api/notes?time={0}&ver={1}"

    Public tmrRefresh As System.Windows.Forms.Timer
    Private _lngPassTime As Double = 0

#Region "--- delete ---"
    'Private mIsLogin As Boolean = False
    'Public ReadOnly Property IsLogin() As Boolean
    '    Get
    '        Return mIsLogin
    '    End Get
    'End Property

    'Private mUid As String = String.Empty      '用户ID
    'Public ReadOnly Property Uid() As String
    '    Get
    '        If String.IsNullOrEmpty(Me.mUid) Then
    '            Return "0"
    '        Else
    '            Return Me.mUid
    '        End If
    '    End Get
    'End Property

    'Public mEmail As String = String.Empty     '登录邮箱
    'Public ReadOnly Property Email() As String
    '    Get
    '        Return Me.mEmail
    '    End Get
    'End Property

    'Private mNickName As String = String.Empty   '用户昵称
    'Public ReadOnly Property NickName() As String
    '    Get
    '        Return Me.mNickName
    '    End Get
    'End Property

    'Private mLevel As String = String.Empty    '用户级别
    'Public ReadOnly Property Level() As String
    '    Get
    '        Return Me.mLevel
    '    End Get
    'End Property

    'Private mErrMsg As String = String.Empty    '登录错误信息
    'Public ReadOnly Property ErrMsg() As String
    '    Get
    '        Return Me.mErrMsg
    '    End Get
    'End Property

    'Private mUsertoken As String = String.Empty '用户登录信息
    'Public ReadOnly Property Usertoken()
    '    Get
    '        Return Me.mUsertoken
    '    End Get
    'End Property

    '获取所有预装软件列表

#End Region

    Public Property YuzhuangList() As List(Of UpdatePackageInfo)
        Get
            Return Me.mLstPackagesTotal
        End Get
        Set(ByVal value As List(Of UpdatePackageInfo))
            Me.mLstPackagesTotal = value
        End Set
    End Property

    Public ReadOnly Property NewDeviceList() As List(Of UpdatePackageInfo)
        Get
            Return Me.mLstPackageNewDevice
        End Get
    End Property

    Public ReadOnly Property AppVersion() As String
        Get
            Return Me.mAppVersion
        End Get
    End Property

    '2015-07-07 by chenbihai 弹公告
    Private mIsChangeNotice As Boolean = False
    Private mJsonobj As JsonObject
    Private mTimer As Integer = 0
    Private mCheckPopMinite As Integer = 0

    Private mFrmPop As frmPop = Nothing


    '2015-08-20 by chenbihai 保存最后一次获取列表的sku 拼装成字符串 用于比较是否有发生上下架
    Private mIsChangePackagesTotal As Boolean = False
    Private mStrPackagesTotal As String = String.Empty
    Private mIsChangePackageNewDevice As Boolean = False
    Private mStrPackageNewDevice As String = String.Empty

#Region "--- 初始化 ---"

    Private Shared mWealth As WealthHelper = Nothing
    Private Shared mLocker As Object = New Object

    Public Shared Function Instance() As WealthHelper
        If mWealth Is Nothing Then
            SyncLock mLocker
                If mWealth Is Nothing Then
                    mWealth = New WealthHelper()
                End If
            End SyncLock
        End If
        Return mWealth
    End Function

    Private Sub New()
        Dim tempVersion As String = My.Application.Info.Version.ToString().Replace(".", "")
        Me.mAppVersion = CType(tempVersion, Integer).ToString()

        Me.InitTimer()
    End Sub


    Private Sub InitTimer()
        tmrRefresh = New System.Windows.Forms.Timer
        tmrRefresh.Interval = 1000
        Me.tmrRefresh.Enabled = True

        mCheckPopMinite = 300 * 1000
        Dim iInterval As Integer = IniSetting.GetCheckPopMinite()
        If iInterval > 0 Then
            'tmrRefresh.Interval = iInterval * 60000
            mCheckPopMinite = iInterval * 60000
        End If
        'tmrRefresh.AutoReset = True

        RemoveHandler tmrRefresh.Tick, AddressOf tmrRefresh_Elapsed
        AddHandler tmrRefresh.Tick, AddressOf tmrRefresh_Elapsed

        'RemoveHandler tmrRefresh.Elapsed, AddressOf tmrRefresh_Elapsed
        'AddHandler tmrRefresh.Elapsed, AddressOf tmrRefresh_Elapsed
    End Sub

#End Region

#Region "---  获取应用列表   ---"

    '包括四个专区列表和三个热门列表
    Public Function GetPackages(ByVal region As AreaType, ByVal blnReload As Boolean) As List(Of UpdatePackageInfo)
        Dim lstPackages As List(Of UpdatePackageInfo) = Nothing

        Try
            Dim url As String = String.Empty
            Dim topic As Integer = 0
            Dim channel As Integer = 1 '1-iOS频道，2-新机频道，3-刷榜频道
            Select Case region
                Case AreaType.Yuzhuang
                    url = Me.mUrlList
                    lstPackages = Me.mLstPackagesTotal
                    channel = 1

                Case AreaType.NewDevice
                    url = Me.mUrlNewDevice
                    lstPackages = Me.mLstPackageNewDevice
                    channel = 2
            End Select

            '如果不需要重新加载，已获取过一次，则直接返回。
            If Not blnReload AndAlso lstPackages IsNot Nothing Then
                Return lstPackages
            End If
            Common.Log(String.Format(""))

            Dim lstPackagesTemp As List(Of UpdatePackageInfo) = Nothing
            If url.Length > 0 Then
                lstPackagesTemp = Me.GetPackagesNew(url, topic, channel)
            Else
                '安卓构造软件出来测试
                Dim package As UpdatePackageInfo = New UpdatePackageInfo
                With package
                    .wID = -500
                    .Identifier = "com.tencent.mobileqq"
                    .Name = "QQ"
                    .Version = "4.6.2"
                    .FileSize = 24484250
                    .wMd5 = "nG2551KN/onk4F++iq5rug=="
                    .wPopularize = "测试qq，稳定后陆续接入广告"
                    .wDownloadUrl = "http://qd.leaderhero.com/gpan/tbzjhelper/test/QQ-4.6.2.apk"
                    .wRegion = RegionState.Anzhuo
                    .wAdReturnType = AdReturnType.Mac
                    .wIsRealTime = False
                    .wDeviceType = DeviceType.Android
                End With
                lstPackagesTemp = New List(Of UpdatePackageInfo)
                lstPackagesTemp.Add(package)
            End If

            Select Case region
                Case AreaType.Yuzhuang
                    Me.mLstPackagesTotal = lstPackagesTemp
                    lstPackages = Me.mLstPackagesTotal

                Case AreaType.NewDevice
                    Me.mLstPackageNewDevice = lstPackagesTemp
                    lstPackages = Me.mLstPackageNewDevice

                    'Case AreaType.ReMenBiBei
                    '    Me.mLstPackages_Zhuangjibibei = lstPackagesTemp
                    '    lstPackages = Me.mLstPackages_Zhuangjibibei

                    'Case AreaType.ReMenYouXi
                    '    Me.mLstPackages_RemenYouxi = lstPackagesTemp
                    '    lstPackages = Me.mLstPackages_RemenYouxi

                    'Case AreaType.ReMenYingYong
                    '    Me.mLstPackages_Ruanjian = lstPackagesTemp
                    '    lstPackages = Me.mLstPackages_Ruanjian

                    'Case AreaType.Android
                    '    Me.mLstPackageAnzhuo = lstPackagesTemp
                    '    lstPackages = Me.mLstPackageAnzhuo
            End Select

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

        If lstPackages Is Nothing Then
            lstPackages = New List(Of UpdatePackageInfo)
        End If
        Return lstPackages
    End Function

    Private Function GetPackagesNew(ByVal url As String, ByVal topic As Integer, ByVal channel As Integer) As List(Of UpdatePackageInfo)
        Common.Log("开始获取：" & url)

        Dim lstPackages As List(Of UpdatePackageInfo) = Nothing
        Try
            Dim isNormal As Boolean = False

            'Dim strJson As String = "{" + String.Format("""appversion"":""{0}"",", Me.mAppVersion) + _
            '                                               String.Format("""topic"":{0},", topic) + _
            '                                               String.Format("""uid"":{0},", Me.Uid) + _
            '                                               """package"":{}}"

            Dim jsPost As New JsonObject
            jsPost.Add("client", "pc")
            jsPost.Add("ver", String.Format("""{0}""", Me.mAppVersion))
            jsPost.Add("mtype", "0")
            jsPost.Add("channel", channel)
            jsPost.Add("uid", PluginLogin.Instance().Uid)
            jsPost.Add("t", HtmlHelper.GetServerTime())

            url = "http://zj.tongbu.com/api/applist"

            Dim strPost As String = JsonParser.SaveString(jsPost)
            Dim strResult As String = HtmlHelper.PostEncryptData(url, strPost)
            Common.Log(String.Format("----获取财富专区数据---- {0}", strResult))
            Dim objJson As JsonObject = JsonParser.ParseString(strResult)

            If objJson Is Nothing Then
                GoTo DO_EXIT
            End If

            If objJson.ContainsKey("code") Then
                Dim code As String = CType(objJson("code"), JsonNumber).ToString()
                If String.Compare(code, "200") <> 0 Then
                    GoTo DO_EXIT
                Else
                    isNormal = True
                End If
            Else
                GoTo DO_EXIT
            End If

            If Not objJson.ContainsKey("packages") Then
                GoTo DO_EXIT
            End If

            lstPackages = New List(Of UpdatePackageInfo)

            Dim strPackagesTotal As String = String.Empty
            Dim strPackageNewDevice As String = String.Empty

            Dim objItems As JsonArray = objJson("packages")
            For Each Item As JsonObject In objItems
                Dim package As UpdatePackageInfo = GetPackageInfo(Item)

                If package Is Nothing Then
                    Continue For
                End If

                If channel = 1 Then '1-iOS频道
                    strPackagesTotal = strPackagesTotal & package.Identifier
                ElseIf channel = 2 Then '2-新机频道
                    strPackageNewDevice = strPackageNewDevice & package.Identifier
                End If

                lstPackages.Add(package)
            Next
            mIsChangePackagesTotal = False
            mIsChangePackageNewDevice = False
            If channel = 1 AndAlso strPackagesTotal <> mStrPackagesTotal Then '1-iOS频道
                mIsChangePackagesTotal = True
                mStrPackagesTotal = strPackagesTotal
            ElseIf channel = 2 AndAlso strPackageNewDevice <> mStrPackageNewDevice Then '2-新机频道
                mIsChangePackageNewDevice = True
                mStrPackageNewDevice = strPackageNewDevice
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetPackages")
        End Try

DO_EXIT:
        Common.Log("结束获取：" & url)
        Return lstPackages
    End Function
    '    Private Function GetPackages(ByVal url As String, ByVal topic As Integer) As List(Of UpdatePackageInfo)
    '        Dim lstPackages As List(Of UpdatePackageInfo) = Nothing
    '        Try
    '            Dim isNormal As Boolean = False
    '            Dim strJson As String = "{" + String.Format("""appversion"":""{0}"",", Me.mAppVersion) + _
    '                                                String.Format("""topic"":{0},", topic) + _
    '                                                String.Format("""uid"":{0},", PluginLogin.Instance.Uid) + _
    '                                                """package"":{}}"

    '            Dim strPost As String = String.Format("json={0}", Common.EncryptDES(strJson, Common.RgbKeyString, Common.RgbKeyString))
    '            strPost = FormatPostData(strPost)

    '            Dim strResult As String = PostData(url, strPost)
    '            Dim objJson As JsonObject = JsonParser.ParseString(strResult)

    '            If objJson Is Nothing Then
    '                GoTo DO_EXIT
    '            End If

    '            If objJson.ContainsKey("code") Then
    '                Dim code As String = CType(objJson("code"), JsonNumber).ToString()
    '                If String.Compare(code, "200") <> 0 Then
    '                    GoTo DO_EXIT
    '                Else
    '                    isNormal = True
    '                End If
    '            Else
    '                GoTo DO_EXIT
    '            End If

    '            If Not objJson.ContainsKey("packages") Then
    '                GoTo DO_EXIT
    '            End If

    '            lstPackages = New List(Of UpdatePackageInfo)

    '            Dim objItems As JsonArray = objJson("packages")
    '            For Each Item As JsonObject In objItems
    '                Dim package As UpdatePackageInfo = GetPackageInfo(Item)

    '                If package Is Nothing Then
    '                    Continue For
    '                End If

    '                lstPackages.Add(package)
    '            Next

    '        Catch ex As Exception
    '            Common.LogException(ex.ToString(), "GetPackages")
    '        End Try

    'DO_EXIT:
    '        Return lstPackages
    '    End Function

    Private Function GetPackageInfo(ByVal objPackage As JsonObject) As UpdatePackageInfo
        Dim package As New UpdatePackageInfo

        Try
            package.wID = CType(objPackage("id"), JsonString).Value()
        Catch
        End Try

        Try
            'SKU
            package.Identifier = CType(objPackage("sku"), JsonString).Value()
        Catch
        End Try

        Try
            package.Name = CType(objPackage("name"), JsonString).Value().TrimEnd()
        Catch
        End Try


        Try
            '版本号
            package.Version = CType(objPackage("version"), JsonString).Value()
        Catch
        End Try

        Try
            '文件大小
            Dim strSize As String = CType(objPackage("filesize"), JsonString).Value()
            If Not String.IsNullOrEmpty(strSize) Then
                package.FileSize = CType(strSize, Long)
            End If
        Catch
        End Try

        Dim crackType As CrakedInfo = CrakedInfo.UnCrakedTongbu
        Try
            Dim strType As String = CType(objPackage("type"), JsonString).Value()
            If strType = "0" Then
                crackType = CrakedInfo.Craked
            ElseIf strType = "2" Then
                crackType = CrakedInfo.Enterprise
            End If
        Catch
        End Try
        package.CrackedInfo = crackType
        'Debug.Print(package.Name & "," & package.CrackedInfo)


        Dim deviceType As UIDeviceFamily = UIDeviceFamily.Unknow
        Try
            Dim strDeviceType As String = CType(objPackage("devicetype"), JsonString).Value()
            If strDeviceType = "1" Then
                deviceType = UIDeviceFamily.iPhone
                'package.Name = package.Name + "iPhone"

            ElseIf strDeviceType = "2" Then
                deviceType = UIDeviceFamily.iPad
                'package.Name = package.Name + "iPad"

            ElseIf strDeviceType = "3" Then
                deviceType = UIDeviceFamily.iPhoneAndiPad
                'package.Name = package.Name + "iPhoneAndiPad"

            End If
        Catch
        End Try
        package.UIDeviceFamily = deviceType

        Try
            package.wPopularize = CType(objPackage("popularize"), JsonString).Value().TrimEnd()
        Catch
        End Try

        Try
            package.Price = CType(objPackage("price"), JsonString).Value()

            Dim iPrice As Double = 0
            Double.TryParse(package.Price, iPrice)
            package.Price = iPrice * 100
        Catch
        End Try

        Try

            If Not (TypeOf (objPackage("md5")) Is JsonNull) Then
                package.wMd5 = CType(objPackage("md5"), JsonString).Value()

                '测试
                'If String.Compare(package.Identifier, "com.taobao.fireeye") = 0 Then
                '    package.wMd5 = "xgs++WJ1H98+zVQVcF0HzQ=="
                'End If
            End If
        Catch
        End Try

        Try
            package.wIconUrl = CType(objPackage("icon"), JsonString).Value()
        Catch
        End Try

        Try
            If objPackage.ContainsKey("download") Then
                package.wDownloadUrl = CType(objPackage("download"), JsonString).Value()
            End If
        Catch
        End Try

        Try
            '更新时间
            Dim strDate As String = CType(objPackage("utime"), JsonString).Value()
            Dim tempDate As Date = Date.MinValue
            If Date.TryParse(strDate, tempDate) Then
                package.LastModifyDate = tempDate
            End If
        Catch
        End Try

        Try
            '更新分区
            If objPackage.ContainsKey("region") Then
                package.wRegion = CType(objPackage("region"), JsonString).Value()
            End If
        Catch
        End Try

        'If package.Name.Contains("博雅德州扑克") Then
        '    Stop
        'End If

        Try
            '更新广告主反量方式
            If objPackage.ContainsKey("adreturntype") Then
                package.wAdReturnType = CType(objPackage("adreturntype"), JsonString).Value()
            End If
        Catch
        End Try

        Try
            '是否实时激活
            If objPackage.ContainsKey("realtime") Then
                package.wIsRealTime = (CType(objPackage("realtime"), JsonString).Value() = "1")
            End If
        Catch
        End Try

        Try
            '支持固件，有的固件仅支持iOS7
            If objPackage.ContainsKey("hardware") Then
                package.wSupportOSVersion = CType(objPackage("hardware"), JsonString).Value()
            End If
        Catch
        End Try

        Return package
    End Function

#End Region

#Region "--- 提交应用安装成功 ---"

#Region "--- 添加安装记录到数据库 ---"

    Public Sub InstallSucceed(ByVal args As WealthHelperArgs)

        Me.OperateQueueInstallSucceed(True, args)

        If mtdInstallSucceed IsNot Nothing AndAlso mtdInstallSucceed.ThreadState <> ThreadState.Stopped Then
            Try
                Return
            Catch
            End Try
        End If

        mtdInstallSucceed = New Thread(AddressOf InstallSucceedInThreadNew)
        With mtdInstallSucceed
            .IsBackground = True
            .Start()
        End With
    End Sub

    Public Sub InstallSucceedInThread()
        While Me.mQueueInstallSucceed.Count > 0
            Try
                Dim valueArgs As WealthHelperArgs = Me.OperateQueueInstallSucceed(False, Nothing)
                Dim info As UpdatePackageInfo = Me.GetPackageFromListBySKU(valueArgs.SKU, RegionState.Other, valueArgs.PackagesFromLocal.PackagePath)
                Dim strMD5 As String = "'"

                If info IsNot Nothing Then
                    strMD5 = info.wMd5
                Else
                    strMD5 = Common.GetMd5Base64FromFile(valueArgs.PackagesFromLocal.PackagePath)
                End If

                '强推软件和推广软件需要回传记录，其它的不用回传
                If info Is Nothing AndAlso valueArgs.Type = "0" Then
                    Me.SaveNotSendLog(valueArgs)
                    Continue While
                End If

                '-----------  判断是否已经安装过
                If Not IniSetting.GetJudgeInstalled Then
                    valueArgs.IsCheckSoftInstall = True
                    valueArgs.PackageFromWeb = info

                    RaiseEvent CheckSoftExistEvent(Me, valueArgs)

                    Dim strSerialExist As String = ""
                    Dim isInstallFromWeb As Boolean = Me.CheckSoftExist(valueArgs, strSerialExist)
                    valueArgs.WebReturnSoftInstalled = isInstallFromWeb
                    valueArgs.IsCheckSoftInstall = False
                    If isInstallFromWeb Then
                        valueArgs.PhoneSerialExist = strSerialExist
                    End If
                    RaiseEvent CheckSoftExistEvent(Me, valueArgs)
                End If
                '-------------------------------

                Dim strUid As String = String.Format("""appversion"":""{0}"",""uid"":""{1}"",""email"":""{2}"",""nickname"":""{3}"",""pcmac"":""{4}"",", _
                                                                         Me.mAppVersion, PluginLogin.Instance().Uid, PluginLogin.Instance.Account, PluginLogin.Instance.Name, Common.GetMacAddress)

                Dim strDeviceInfo As String = """udid"":""{0}"",""sn"":""{1}"",""wifimac"":""{2}"",""producttype"":""{3}""," + _
                                                              """productversion"":""{4}"",""telnumber"":""{5}"",""jailbreak"":""{6}""," + _
                                                              """imei"":""{7}"",""versionnumber"":{8},""openudid"":""{9}""," + _
                                                              """idfv"":""{10}"",""idfa"":""{11}"""

                Dim isJailbreak As Boolean = False
                If valueArgs.Device.DeviceType = DeviceType.iOS Then
                    isJailbreak = CType(valueArgs.Device, iPhoneDevice).Jailbreaked
                End If

                Dim strDeviceInfoValue As String = """device"":{" & _
                                                                      String.Format(strDeviceInfo, valueArgs.Device.DeviceID, DeviceInfo.SerialNumber(valueArgs.Device), _
                                                                                          DeviceInfo.WiFiAddress(valueArgs.Device), DeviceInfo.ProductType(valueArgs.Device), DeviceInfo.ProductVersion(valueArgs.Device), _
                                                                                            valueArgs.PhoneNumber, IIf(isJailbreak, "0", "1"), _
                                                                                             valueArgs.IMEI, DeviceInfo.VersionNumber(valueArgs.Device), DeviceInfo.ZJ_OpenUDID(valueArgs.Device), _
                                                                                            DeviceInfo.ZJ_IDFV(valueArgs.Device), DeviceInfo.ZJ_IDFA(valueArgs.Device)) & "},"

                Dim strPackage As String = ""
                Dim strTime As String = Now.ToString("yyyy-MM-dd HH:mm:ss")

                If valueArgs.Type = "0" Then
                    strPackage = """package"":{" + String.Format("""sku"":""{0}"",""md5"":""{1}"",""region"":""{2}"",""installtime"":""{3}"",""applistid"":{4},""adreturntype"":{5}", _
                                          valueArgs.SKU, strMD5, Convert.ToInt32(info.wRegion), strTime, info.wID, Integer.Parse(info.wAdReturnType)) + "}"
                Else
                    strPackage = """package"":{" + String.Format("""sku"":""{0}"",""md5"":""{1}"",""type"":""{2}"",""succeed"":""{3}"",""errmsg"":""{4}"",""installtime"":""{5}"",""applistid"":{6},""adreturntype"":{7}", _
                                         valueArgs.SKU, strMD5, valueArgs.Type, valueArgs.Succeed, valueArgs.ErrMsg, strTime, "-1", 16) + "}"
                End If

                Dim strJsonValue As String = Common.EncryptDES("{" + strUid + strDeviceInfoValue + strPackage + "}", Common.RgbKeyString, Common.RgbKeyString)
                Dim strJson As String = "json=" + strJsonValue
                strJson = FormatPostData(strJson)

                SoftInstallDB.GetInstance().AddSoftInfo(strJson, valueArgs.Device.DeviceID, strMD5)
                Me.PostInstallSucceed()

                '如果属于激活专区软件，则需要写文件到iOS端
                If info IsNot Nothing AndAlso info.wRegion = RegionState.JiHuo Then
                    Common.LogException(String.Format("开始写数据到苹果游戏商店：{0}  {1}", valueArgs.Device.DeviceName, valueArgs.Device.DeviceID))
                    valueArgs.PackagesFromLocal.Tag = strJsonValue

                    Me.WriteDataToAGSPlan(valueArgs)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "InstallSucceedInThread")
            End Try
        End While
    End Sub

    Public Sub InstallSucceedInThreadNew()
        While Me.mQueueInstallSucceed.Count > 0
            Try
                Dim valueArgs As WealthHelperArgs = Me.OperateQueueInstallSucceed(False, Nothing)
                Dim info As UpdatePackageInfo = Me.GetPackageFromListBySKU(valueArgs.SKU, RegionState.Other, valueArgs.PackagesFromLocal.PackagePath)

                If Common.IsTestMode AndAlso info Is Nothing AndAlso Not valueArgs.PackagesFromLocal.Identifier.StartsWith("com.tongbu.tui") Then
                    info = New UpdatePackageInfo
                    '--- 测试激活 ---
                    Dim arrName As String() = Split(Path.GetFileNameWithoutExtension(valueArgs.PackagesFromLocal.PackagePath), "_")
                    If arrName.Length < 2 Then
                        MessageBox.Show("缺少ID信息，命名规则 名字_ID.ipa")
                        Return
                    End If

                    info.wIsRealTime = True
                    info.wID = arrName(1)
                    '----------------
                End If

                Dim strMD5 As String = "'"

                Dim iChannel As Integer = 1

                If valueArgs.PackagesFromLocal.PackagePath.Contains(Folder.DownloadNewAreaFolder) Then
                    iChannel = 2
                End If

                If info IsNot Nothing Then
                    strMD5 = info.wMd5
                Else
                    strMD5 = Common.GetMd5Base64FromFile(valueArgs.PackagesFromLocal.PackagePath)
                End If

                '强推软件和推广软件需要回传记录，其它的不用回传
                If info Is Nothing AndAlso valueArgs.Type = "0" Then
                    Me.SaveNotSendLog(valueArgs)
                    Continue While
                End If

                '-----------  判断是否已经安装过
                If Not IniSetting.GetJudgeInstalled Then
                    valueArgs.IsCheckSoftInstall = True
                    valueArgs.PackageFromWeb = info

                    RaiseEvent CheckSoftExistEvent(Me, valueArgs)

                    Dim strSerialExist As String = ""
                    Dim isInstallFromWeb As Boolean = Me.CheckSoftExist(valueArgs, strSerialExist)
                    valueArgs.WebReturnSoftInstalled = isInstallFromWeb
                    valueArgs.IsCheckSoftInstall = False
                    If isInstallFromWeb Then
                        valueArgs.PhoneSerialExist = strSerialExist
                    End If
                    RaiseEvent CheckSoftExistEvent(Me, valueArgs)
                End If
                '-------------------------------

                Dim jsPost As New JsonObject
                jsPost.Add("channel", iChannel)   '需要判断是哪个频道的数据。

                Dim jsPC As New JsonObject
                jsPC.Add("appversion", Me.mAppVersion)
                jsPC.Add("mac", Common.GetMacAddress())
                jsPost.Add("pc", jsPC)

                Dim jsUserInfo As New JsonObject
                jsUserInfo.Add("uid", PluginLogin.Instance().Uid)
                jsPost.Add("userinfo", jsUserInfo)

                Dim isJailbreak As Boolean = False
                If valueArgs.Device.DeviceType = DeviceType.iOS Then
                    isJailbreak = CType(valueArgs.Device, iPhoneDevice).Jailbreaked
                End If

                Dim jsDevice As New JsonObject
                jsDevice.Add("udid", valueArgs.Device.DeviceID)
                jsDevice.Add("sn", DeviceInfo.SerialNumber(valueArgs.Device))
                jsDevice.Add("wifimac", IIf(valueArgs.RepairWifiMac = "", DeviceInfo.WiFiAddress(valueArgs.Device), valueArgs.RepairWifiMac)) '如果 valueArgs.RepairWifiMac 有值 表示为修复数据 
                jsDevice.Add("producttype", DeviceInfo.ProductType(valueArgs.Device))
                jsDevice.Add("productversion", DeviceInfo.ProductVersion(valueArgs.Device))
                jsDevice.Add("telnumber", valueArgs.PhoneNumber)
                jsDevice.Add("jailbreak", IIf(isJailbreak, "0", "1"))
                jsDevice.Add("imei", valueArgs.IMEI)
                jsDevice.Add("versionnumber", DeviceInfo.VersionNumber(valueArgs.Device))
                jsDevice.Add("openudid", DeviceInfo.ZJ_OpenUDID(valueArgs.Device))
                jsDevice.Add("idfv", DeviceInfo.ZJ_IDFV(valueArgs.Device))
                jsDevice.Add("idfa", DeviceInfo.ZJ_IDFA(valueArgs.Device))
                jsPost.Add("device", jsDevice)

                Dim jsPackage As New JsonObject

                If valueArgs.Type = "1" Then
                    jsPackage.Add("applistid", "-1")
                    jsPackage.Add("sku", valueArgs.SKU)
                    jsPackage.Add("succeed", valueArgs.Succeed)
                    jsPackage.Add("errmsg", valueArgs.ErrMsg)
                Else
                    'jsPackage.Add("applistid", "6723")\
                    If info IsNot Nothing Then
                        jsPackage.Add("applistid", info.wID)
                    End If
                    jsPackage.Add("sku", valueArgs.SKU)
                End If

                If valueArgs.RepairTime.Length > 0 Then
                    jsPackage.Add("installtime", valueArgs.RepairTime)
                Else
                    jsPackage.Add("installtime", Now.ToString("yyyy-MM-dd HH:mm:ss"))
                End If

                jsPost.Add("package", jsPackage)

                Dim strPost As String = JsonParser.SaveString(jsPost)

                If valueArgs.RepairUDID.Length > 0 Then
                    SoftInstallDB.GetInstance().AddSoftInfo(strPost, valueArgs.RepairUDID, strMD5)
                Else
                    SoftInstallDB.GetInstance().AddSoftInfo(strPost, valueArgs.Device.DeviceID, strMD5)
                End If
                Me.PostInstallSucceed()

                '如果属于激活专区软件，则需要写文件到iOS端   --注释2016-02-26 去除写到商店
                'If info IsNot Nothing AndAlso info.wRegion = RegionState.JiHuo Then
                '    Common.LogException(String.Format("开始写数据到苹果游戏商店：{0}  {1}", valueArgs.Device.DeviceName, valueArgs.Device.DeviceID))

                '    Dim strJsonValue As String = Common.EncryptDES(GetAGSPlanData(valueArgs, info, isJailbreak, strMD5), Common.RgbKeyString, Common.RgbKeyString)
                '    'Dim strJson As String = "json=" + strJsonValue
                '    'strJson = FormatPostData(strJson)
                '    valueArgs.PackagesFromLocal.Tag = strJsonValue

                '    Me.WriteDataToAGSPlan(valueArgs)
                'End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "InstallSucceedInThread")
            End Try
        End While
    End Sub

    Private Function GetAGSPlanData(ByVal valueArgs As WealthHelperArgs, ByVal info As UpdatePackageInfo, ByVal isJailbreak As Boolean, ByVal strMD5 As String)
        Dim strJsonValue As String = String.Empty

        Dim strUid As String = String.Format("""appversion"":""{0}"",""uid"":""{1}"",""email"":""{2}"",""nickname"":""{3}"",""pcmac"":""{4}"",", _
                                                         Me.mAppVersion, PluginLogin.Instance().Uid, PluginLogin.Instance.Account, PluginLogin.Instance.Name, Common.GetMacAddress)
        Dim strDeviceInfo As String = """udid"":""{0}"",""sn"":""{1}"",""wifimac"":""{2}"",""producttype"":""{3}""," + _
                                                             """productversion"":""{4}"",""telnumber"":""{5}"",""jailbreak"":""{6}""," + _
                                                             """imei"":""{7}"",""versionnumber"":{8},""openudid"":""{9}""," + _
                                                             """idfv"":""{10}"",""idfa"":""{11}"""


        Dim strDeviceInfoValue As String = """device"":{" & _
                                                                      String.Format(strDeviceInfo, valueArgs.Device.DeviceID, DeviceInfo.SerialNumber(valueArgs.Device), _
                                                                                          DeviceInfo.WiFiAddress(valueArgs.Device), DeviceInfo.ProductType(valueArgs.Device), DeviceInfo.ProductVersion(valueArgs.Device), _
                                                                                            valueArgs.PhoneNumber, IIf(isJailbreak, "0", "1"), _
                                                                                             valueArgs.IMEI, DeviceInfo.VersionNumber(valueArgs.Device), DeviceInfo.ZJ_OpenUDID(valueArgs.Device), _
                                                                                            DeviceInfo.ZJ_IDFV(valueArgs.Device), DeviceInfo.ZJ_IDFA(valueArgs.Device)) & "},"

        Dim strPackage As String = ""
        Dim strTime As String = Now.ToString("yyyy-MM-dd HH:mm:ss")

        If valueArgs.Type = "0" Then
            strPackage = """package"":{" + String.Format("""sku"":""{0}"",""md5"":""{1}"",""region"":""{2}"",""installtime"":""{3}"",""applistid"":{4},""adreturntype"":{5}", _
                                  valueArgs.SKU, strMD5, Convert.ToInt32(info.wRegion), strTime, info.wID, Integer.Parse(info.wAdReturnType)) + "}"
        Else
            strPackage = """package"":{" + String.Format("""sku"":""{0}"",""md5"":""{1}"",""type"":""{2}"",""succeed"":""{3}"",""errmsg"":""{4}"",""installtime"":""{5}"",""applistid"":{6},""adreturntype"":{7}", _
                                 valueArgs.SKU, strMD5, valueArgs.Type, valueArgs.Succeed, valueArgs.ErrMsg, strTime, "-1", 16) + "}"
        End If

        strJsonValue = "{" + strUid + strDeviceInfoValue + strPackage + "}"

        Return strJsonValue
    End Function

    Private Function OperateQueueInstallSucceed(ByVal isEnqueue As Boolean, ByVal args As WealthHelperArgs) As WealthHelperArgs
        SyncLock Me.mLockInstallSucceed
            If isEnqueue Then
                If Not Me.mQueueInstallSucceed.Contains(args) Then
                    Me.mQueueInstallSucceed.Enqueue(args)
                End If
                Return Nothing
            Else
                Dim returnValue As WealthHelperArgs = Me.mQueueInstallSucceed.Dequeue()
                Return returnValue
            End If
        End SyncLock
    End Function

#End Region

#Region "--- 发送安装记录 ---"

    Public Sub PostInstallSucceed()
        If mtdPostInstallSucceed IsNot Nothing AndAlso mtdPostInstallSucceed.ThreadState <> ThreadState.Stopped Then
            Try
                Return
            Catch
            End Try
        End If

        mtdPostInstallSucceed = New Thread(AddressOf PostInstallSucceedInThread)
        With mtdPostInstallSucceed
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub PostInstallSucceedInThread()
        Try
            Dim isRetry As Boolean = False

DoRetry:
            Dim isSuccced As Boolean = True

            '从数据库中读取数据发送
            Dim rows As DataRow() = SoftInstallDB.GetInstance.GetAllSoftInfo()
            If rows Is Nothing OrElse rows.Length = 0 Then
                Return
            End If

            Dim lstRows As New List(Of DataRow)
            If rows IsNot Nothing AndAlso rows.Length > 0 Then
                For Each row As DataRow In rows
                    lstRows.Add(row)
                Next
            End If

            For Each row As DataRow In lstRows
                Try
                    Dim strJson As String = Common.GetValue(Of String)(row("Json"), "")

                    If strJson.Length = 0 Then
                        Continue For
                    End If
                    mUrlInstall = "http://zj.tongbu.com/api/appinstall"
                    Dim postWebResult As String = ""
                    postWebResult = HtmlHelper.PostEncryptData(mUrlInstall, strJson)   '   Me.PostData(Me.mUrlInstall, strJson)
                    Me.Log("上传安装记录：" & postWebResult, Me.GetUpdatePkgInfo(strJson))

                    Dim objJson As JsonObject = JsonParser.ParseString(postWebResult)

                    If objJson IsNot Nothing AndAlso objJson.ContainsKey("code") Then
                        Dim code As String = CType(objJson("code"), JsonNumber).ToString()
                        If String.Compare(code, "200") = 0 Then
                            SoftInstallDB.GetInstance.DeleteSoftInfo(row)
                        Else
                            isSuccced = False
                        End If
                        Common.Log("上传安装记录回传code：" & code)
                    End If

                    SaveSendLog(postWebResult, strJson)
                Catch ee As Exception
                    Common.LogException(ee.ToString(), "PostInstallSucceedInThread-1")
                End Try
            Next
            lstRows.Clear()
            lstRows = Nothing

            '如果本批软件都上传成功，则重新再从数据库里面获取是否有软件需要再上传
            If isSuccced AndAlso Not isRetry Then
                isRetry = True
                GoTo DoRetry
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "PostInstallSucceedInThread")
        End Try
    End Sub

#End Region

#End Region

    '#Region "--- 提交应用安装成功 ---"

    '#Region "--- 添加安装记录到数据库 ---"

    '    Public Sub InstallSucceed(ByVal args As WealthHelperArgs)

    '        Me.OperateQueueInstallSucceed(True, args)

    '        If mtdInstallSucceed IsNot Nothing AndAlso mtdInstallSucceed.ThreadState <> ThreadState.Stopped Then
    '            Try
    '                Return
    '            Catch
    '            End Try
    '        End If

    '        mtdInstallSucceed = New Thread(AddressOf InstallSucceedInThread)
    '        With mtdInstallSucceed
    '            .IsBackground = True
    '            .Start()
    '        End With

    '    End Sub

    '    Public Sub InstallSucceedInThread()
    '        While Me.mQueueInstallSucceed.Count > 0
    '            Try
    '                Dim valueArgs As WealthHelperArgs = Me.OperateQueueInstallSucceed(False, Nothing)
    '                Dim info As UpdatePackageInfo = Me.GetPackageFromListBySKU(valueArgs.SKU, RegionState.Other)
    '                Dim strMD5 As String = "'"

    '                If info IsNot Nothing Then
    '                    strMD5 = info.wMd5
    '                Else
    '                    strMD5 = Common.GetHash(valueArgs.PackagesFromLocal.PackagePath)
    '                End If

    '                '强推软件和推广软件需要回传记录，其它的不用回传
    '                If info Is Nothing AndAlso valueArgs.Type = "0" Then
    '                    Me.SaveNotSendLog(valueArgs)
    '                    Continue While
    '                End If

    '                '-----------  判断是否已经安装过
    '                If Not IniSetting.GetJudgeInstalled Then
    '                    valueArgs.IsCheckSoftInstall = True
    '                    valueArgs.PackageFromWeb = info

    '                    RaiseEvent CheckSoftExistEvent(Me, valueArgs)

    '                    Dim strSerialExist As String = ""
    '                    Dim isInstallFromWeb As Boolean = Me.CheckSoftExist(valueArgs, strSerialExist)
    '                    valueArgs.WebReturnSoftInstalled = isInstallFromWeb
    '                    valueArgs.IsCheckSoftInstall = False
    '                    If isInstallFromWeb Then
    '                        valueArgs.PhoneSerialExist = strSerialExist
    '                    End If
    '                    RaiseEvent CheckSoftExistEvent(Me, valueArgs)
    '                End If
    '                '-------------------------------

    '                Dim strUid As String = String.Format("""appversion"":""{0}"",""uid"":""{1}"",""email"":""{2}"",""nickname"":""{3}"",""pcmac"":""{4}"",", _
    '                                                                         Me.mAppVersion, PluginLogin.Instance.Uid, PluginLogin.Instance.Account, PluginLogin.Instance.Name, Common.GetMacAddress)

    '                Dim strDeviceInfo As String = """udid"":""{0}"",""sn"":""{1}"",""wifimac"":""{2}"",""producttype"":""{3}""," + _
    '                                                              """productversion"":""{4}"",""telnumber"":""{5}"",""jailbreak"":""{6}""," + _
    '                                                              """imei"":""{7}"",""versionnumber"":{8},""openudid"":""{9}""," + _
    '                                                              """idfv"":""{10}"",""idfa"":""{11}"""

    '                Dim isJailbreak As Boolean = False
    '                If valueArgs.Device.DeviceType = DeviceType.iOS Then
    '                    isJailbreak = CType(valueArgs.Device, iPhoneDevice).Jailbreaked
    '                End If

    '                Dim strDeviceInfoValue As String = """device"":{" & _
    '                                                                      String.Format(strDeviceInfo, valueArgs.Device.DeviceID, DeviceInfo.SerialNumber(valueArgs.Device), _
    '                                                                                          DeviceInfo.WiFiAddress(valueArgs.Device), DeviceInfo.ProductType(valueArgs.Device), DeviceInfo.ProductVersion(valueArgs.Device), _
    '                                                                                            valueArgs.PhoneNumber, IIf(isJailbreak, "0", "1"), _
    '                                                                                             valueArgs.IMEI, DeviceInfo.VersionNumber(valueArgs.Device), DeviceInfo.ZJ_OpenUDID(valueArgs.Device), _
    '                                                                                            DeviceInfo.ZJ_IDFV(valueArgs.Device), DeviceInfo.ZJ_IDFA(valueArgs.Device)) & "},"

    '                Dim strPackage As String = ""
    '                Dim strTime As String = Now.ToString("yyyy-MM-dd HH:mm:ss")

    '                If valueArgs.Type = "0" Then
    '                    strPackage = """package"":{" + String.Format("""sku"":""{0}"",""md5"":""{1}"",""region"":""{2}"",""installtime"":""{3}"",""applistid"":{4},""adreturntype"":{5}", _
    '                                          valueArgs.SKU, strMD5, Convert.ToInt32(info.wRegion), strTime, info.wID, Integer.Parse(info.wAdReturnType)) + "}"
    '                Else
    '                    strPackage = """package"":{" + String.Format("""sku"":""{0}"",""md5"":""{1}"",""type"":""{2}"",""succeed"":""{3}"",""errmsg"":""{4}"",""installtime"":""{5}"",""applistid"":{6},""adreturntype"":{7}", _
    '                                         valueArgs.SKU, strMD5, valueArgs.Type, valueArgs.Succeed, valueArgs.ErrMsg, strTime, "-1", 16) + "}"
    '                End If

    '                Dim strJsonValue As String = Common.EncryptDES("{" + strUid + strDeviceInfoValue + strPackage + "}", Common.RgbKeyString, Common.RgbKeyString)
    '                Dim strJson As String = "json=" + strJsonValue
    '                strJson = FormatPostData(strJson)

    '                SoftInstallDB.GetInstance().AddSoftInfo(strJson, valueArgs.Device.DeviceID, strMD5)
    '                Me.PostInstallSucceed()

    '                '如果属于激活专区软件，则需要写文件到iOS端
    '                If info IsNot Nothing AndAlso info.wRegion = RegionState.JiHuo Then
    '                    Common.LogException(String.Format("开始写数据到苹果游戏商店：{0}  {1}", valueArgs.Device.DeviceName, valueArgs.Device.DeviceID))
    '                    valueArgs.PackagesFromLocal.Tag = strJsonValue

    '                    Me.WriteDataToAGSPlan(valueArgs)
    '                End If
    '            Catch ex As Exception
    '                Common.LogException(ex.ToString(), "InstallSucceedInThread")
    '            End Try
    '        End While
    '    End Sub

    '    Private Function OperateQueueInstallSucceed(ByVal isEnqueue As Boolean, ByVal args As WealthHelperArgs) As WealthHelperArgs
    '        SyncLock Me.mLockInstallSucceed
    '            If isEnqueue Then
    '                If Not Me.mQueueInstallSucceed.Contains(args) Then
    '                    Me.mQueueInstallSucceed.Enqueue(args)
    '                End If
    '                Return Nothing
    '            Else
    '                Dim returnValue As WealthHelperArgs = Me.mQueueInstallSucceed.Dequeue()
    '                Return returnValue
    '            End If
    '        End SyncLock
    '    End Function

    '#End Region

    '#Region "--- 发送安装记录 ---"

    '    Public Sub PostInstallSucceed()
    '        If mtdPostInstallSucceed IsNot Nothing AndAlso mtdPostInstallSucceed.ThreadState <> ThreadState.Stopped Then
    '            Try
    '                Return
    '            Catch
    '            End Try
    '        End If

    '        mtdPostInstallSucceed = New Thread(AddressOf PostInstallSucceedInThread)
    '        With mtdPostInstallSucceed
    '            .IsBackground = True
    '            .Start()
    '        End With
    '    End Sub

    '    Private Sub PostInstallSucceedInThread()
    '        Try
    '            '从数据库中读取数据发送
    '            Dim rows As DataRow() = SoftInstallDB.GetInstance.GetAllSoftInfo()
    '            If rows Is Nothing OrElse rows.Length = 0 Then
    '                Return
    '            End If

    '            Dim lstRows As New List(Of DataRow)
    '            If rows IsNot Nothing AndAlso rows.Length > 0 Then
    '                For Each row As DataRow In rows
    '                    lstRows.Add(row)
    '                Next
    '            End If

    '            For Each row As DataRow In lstRows
    '                Try
    '                    Dim strJson As String = Common.GetValue(Of String)(row("Json"), "")

    '                    If strJson.Length = 0 Then
    '                        Continue For
    '                    End If

    '                    Dim postWebResult As String = Me.PostData(Me.mUrlInstall, strJson)
    '                    Me.Log("上传安装记录：" & postWebResult, Me.GetUpdatePkgInfo(strJson))

    '                    Dim objJson As JsonObject = JsonParser.ParseString(postWebResult)

    '                    If objJson IsNot Nothing AndAlso objJson.ContainsKey("code") Then
    '                        Dim code As String = CType(objJson("code"), JsonNumber).ToString()
    '                        If String.Compare(code, "200") = 0 Then
    '                            SoftInstallDB.GetInstance.DeleteSoftInfo(row)
    '                            'result = True
    '                        End If
    '                    End If

    '                    SaveSendLog(postWebResult, strJson)
    '                Catch
    '                End Try
    '            Next
    '            lstRows.Clear()
    '            lstRows = Nothing
    '        Catch ex As Exception
    '            Common.LogException(ex.ToString(), "PostInstallSucceedInThread")
    '        End Try
    '    End Sub

    '#End Region

    '#End Region

#Region "--- 定时刷新 ---"

    Public Sub tmrRefresh_Elapsed(ByVal sender As Object, ByVal e As System.EventArgs)
        mTimer = mTimer + 1

        Try
            If mIsChangeNotice Then
                mIsChangeNotice = False
                mFrmPop = frmPop.ShowForm(JsonParser.SaveString(mJsonobj), mFrmPop)

            End If
            If mTimer * 1000 >= mCheckPopMinite Then
                mTimer = 0
                Me.DoRefresh()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    '登录之后便开启计时器，10分钟检查是否更新，有更新的话刷新界面
    Public Sub DoRefresh()
        If Not PluginLogin.Instance.IsLogin Then
            Return
        End If

        Me.Start2CheckPop(True)
    End Sub

#End Region

#Region "--- post数据 ---"

    Public Shared Function PostData(ByVal strUrl As String, ByVal strPost As String) As String
        Dim strResult As String = ""
        Try
            Dim postBuffer As Byte() = System.Text.Encoding.UTF8.GetBytes(strPost)
            Dim webRequest As System.Net.HttpWebRequest = Nothing
            Dim webResponse As System.Net.HttpWebResponse = Nothing
            Dim requestStream As System.IO.Stream = Nothing
            Dim responseStream As System.IO.Stream = Nothing

            webRequest = System.Net.WebRequest.Create(strUrl)

            webRequest.Method = "POST"
            webRequest.ContentType = "application/x-www-form-urlencoded"   '在向服务器发送大量的文本、包含非ASCII字符的文本或二进制数据时这种编码方式效率很低
            'webRequest.ContentType = "multipart/form-data"                '既可以发送文本数据，也支持二进制数据上载
            webRequest.KeepAlive = False
            webRequest.ContentLength = postBuffer.Length
            webRequest.Timeout = 40000
            webRequest.AllowAutoRedirect = True

            requestStream = webRequest.GetRequestStream()
            requestStream.Write(postBuffer, 0, postBuffer.Length)
            requestStream.Close()

            webResponse = webRequest.GetResponse()
            responseStream = webResponse.GetResponseStream()

            Dim intCount As Integer = 0
            Dim listByte As New List(Of Byte)
            Do
                Dim buffer(5012 - 1) As Byte
                intCount = responseStream.Read(buffer, 0, buffer.Length)
                For intI As Integer = 0 To intCount - 1
                    listByte.Add(buffer(intI))
                Next

                System.Windows.Forms.Application.DoEvents()
            Loop While intCount > 0
            responseStream.Close()

            strResult = System.Text.Encoding.UTF8.GetString(listByte.ToArray())
        Catch ex As Exception
            Common.LogException(strUrl & "," & ex.ToString, "PostData")
        End Try
        Return strResult
    End Function

#End Region

#Region "--- 私有方法 ---"

    Private Function TranslateCookie2String(ByVal colCookie As CookieCollection) As String
        If colCookie Is Nothing Then
            Return ""
        End If

        Dim sbCookie As New System.Text.StringBuilder

        For Each Item As Cookie In colCookie
            sbCookie.AppendFormat("{0}{4}{1}{4}{2}{4}{3}{5}", Item.Name, Item.Value, Item.Path, Item.Domain, ";", vbCrLf)
        Next

        Return sbCookie.ToString
    End Function

    Private Function TranslateString2Cookie(ByVal strCookie As String) As CookieContainer
        Dim colCookie As New CookieContainer

        Try
            For Each itemLine As String In strCookie.Split(vbCrLf)
                Dim aryItem As String() = itemLine.Split(";")
                If aryItem.Length >= 4 Then
                    Dim c As New Cookie(aryItem(0).Trim, aryItem(1).Trim, aryItem(2).Trim, aryItem(3).Trim)
                    colCookie.Add(c)
                End If
            Next
        Catch ex As Exception
        End Try

        Return colCookie
    End Function

    Private Function EncryptDES(ByVal strText As String)
        Dim strResult As String = ""

        strResult = Common.EncryptDES(strText, DESKEY, DESKEY)

        Return strResult
    End Function

    Private Sub Log(ByVal strTitle As String, ByVal strText As String)
        Common.Log(strTitle)
        Common.Log(strText)
    End Sub

    '判断是否在激活列表
    'Private Function IsActivePackages(ByVal strHash As String)
    '    Dim result As Boolean = False

    '    If String.IsNullOrEmpty(strHash) OrElse Me.mLstPackagesTotal Is Nothing Then
    '        Return result
    '    End If

    '    For Each Item As UpdatePackageInfo In Me.mLstPackagesTotal
    '        If Item.wRegion = RegionState.JiHuo AndAlso String.Compare(Item.wMd5, strHash, True) = 0 Then
    '            result = True
    '            Exit For
    '        End If
    '    Next

    '    Return result
    'End Function

    'Private Function CheckInRecommendPackages(ByVal strHash As String) As RegionState
    '    Dim result As RegionState = RegionState.Other

    '    Try
    '        If String.IsNullOrEmpty(strHash) OrElse Me.mLstPackagesTotal Is Nothing Then
    '            Return result
    '        End If

    '        For index As Integer = Me.mLstPackagesTotal.Count - 1 To 0 Step -1
    '            If String.Compare(Me.mLstPackagesTotal(index).wMd5, strHash, True) = 0 Then
    '                result = Me.mLstPackagesTotal(index).wRegion
    '                Exit For
    '            End If
    '        Next

    '    Catch ex As Exception
    '        Common.LogException(ex.ToString)
    '    End Try

    '    Return result
    'End Function

    '    Private Function CheckInRecommendPackages(ByVal strHash As String) As RegionType
    '        Dim type As RegionType = RegionType.None

    '        '判断在激活列表
    '        Dim isExist As Boolean = Me.IsActivePackages(strHash)
    '        If isExist Then
    '            type = RegionType.JiHuo
    '            GoTo DoExit
    '        End If

    '        '判断在安装列表
    '        isExist = Me.IsInstallPackages(strHash)
    '        If isExist Then
    '            type = RegionType.AnZhuang
    '            GoTo DoExit
    '        End If

    'DoExit:
    '        Return type
    '    End Function

    Private Function GetUpdatePkgInfo(ByVal strJson As String) As String
        Dim strHash As String = ""
        Dim strKey As String = """md5"":"""
        Dim iStart As Integer = strJson.IndexOf(strKey)
        If iStart > 0 Then
            Dim iEnd As Integer = strJson.IndexOf("""", iStart + strKey.Length)
            If iEnd > iStart Then
                strHash = strJson.Substring(iStart + strKey.Length, iEnd - iStart - strKey.Length)
            End If
        End If

        Dim result As String = strJson
        If String.IsNullOrEmpty(strHash) OrElse Me.mLstPackagesTotal Is Nothing Then
            Return result
        End If

        For Each Item As UpdatePackageInfo In Me.mLstPackagesTotal
            If String.Compare(Item.wMd5, strHash, True) = 0 Then
                result = Item.Name & ", " & strJson
                Exit For
            End If
        Next

        Return result
    End Function

    Public Shared Function FormatPostData(ByVal strData As String)
        Dim result As String = strData

        '之前有接口测试的时候发现有问题，所以进行替换
        result = result.Replace("+", "%2B")
        result = result.Replace("&", "%26")

        Return result
    End Function

#End Region

#Region "---  对外方法  ---"

    Public Function GetPackageFromListBySKU(ByVal strSKU As String, ByVal state As RegionState, ByVal strPackagePath As String) As UpdatePackageInfo
        Dim package As UpdatePackageInfo = Nothing
        Dim strLocalFolder As String = IniSetting.GetZJLocalPackageFolder

        If strLocalFolder.Length > 0 AndAlso strPackagePath.StartsWith(strLocalFolder) Then
            package = Nothing
        ElseIf strPackagePath.Contains(Folder.DownloadNewAreaFolder) Then
            package = GetPackageFromListBySKU(strSKU, state, Me.mLstPackageNewDevice)
        Else
            package = GetPackageFromListBySKU(strSKU, state, Me.mLstPackagesTotal)
        End If

        Return package
    End Function

    Private Function GetPackageFromListBySKU(ByVal strSKU As String, ByVal state As RegionState, ByVal listPackage As List(Of UpdatePackageInfo)) As UpdatePackageInfo
        Dim package As UpdatePackageInfo = Nothing
        If String.IsNullOrEmpty(strSKU) OrElse listPackage Is Nothing Then
            Return package
        End If

        For Each Item As UpdatePackageInfo In listPackage
            If String.Compare(Item.Identifier, strSKU, True) = 0 Then
                ''判断是否在相同的专区
                If state <> RegionState.Other AndAlso Item.wRegion <> state Then
                    Continue For
                End If

                package = Item
                Exit For
            End If
        Next
        Return package
    End Function

    Public Function GetPackageRegionStateBySKU(ByVal strSKU As String, ByVal strPackagePath As String) As RegionState
        Dim region As RegionState = RegionState.Other

        If IniSetting.GetZJLocalPackageFolder.Length > 0 AndAlso strPackagePath.StartsWith(IniSetting.GetZJLocalPackageFolder) Then
            Dim package As UpdatePackageInfo = GetPackageFromListBySKU(strSKU, RegionState.LocalSource, LocalAppHelper.Instance().ListTotalLocalApp)
            If package IsNot Nothing Then
                region = package.wRegion
            End If
        End If

        Return region
    End Function

#End Region

#Region "--- 记录日志 ---"

    Public Sub SaveInstallLog(ByVal deviceName As String, ByVal strUDID As String, ByVal isReinstall As Boolean, ByVal package As FileSharingPackageInfo)
        Try
            Dim time As String = Common.StringFormat(DateTime.Now.ToLongTimeString(), 10, False, True)
            Dim strIsReinstall As String = Common.StringFormat(isReinstall.ToString(), 15, False, True)
            Dim sku As String = Common.StringFormat(package.Identifier, 70, False, True)
            Dim name As String = Common.StringFormat(package.Name, 50, False, True)
            Dim version As String = Common.StringFormat(package.Version, 10, False, True)
            Dim hash As String = Common.StringFormat(Common.GetMd5Base64FromFile(package.PackagePath), 50, False, True)
            Dim packagePath As String = Common.StringFormat(package.PackagePath, 100, False, True)

            Dim text As String = time & strIsReinstall & sku & name & version & hash & packagePath

            LogForInstall(deviceName, strUDID, text)

        Catch ex As Exception
            Common.LogException("SaveInstallLog保存安装出现异常。")
        End Try
    End Sub

    Private lockerInstall As New Object
    Public Sub LogForInstall(ByVal deviceName As String, ByVal udid As String, ByVal text As String)
        'If Not File.Exists(System.IO.Path.Combine(Folder.AppFolder, "debug.dll")) Then
        '    Return
        'End If

        Dim dir As String = "Data\Install"
        Dim path As String = Folder.DocumentFolder.TrimEnd("\"c) & "\" & dir & "\" & DateTime.Now.ToString("yyyyMMdd")

        SyncLock lockerInstall
            Try
                If Not Directory.Exists(path) Then
                    Directory.CreateDirectory(path)
                End If

                Dim name As String = Common.ReplaceWinIllegalName(deviceName & "_" & udid)
                Dim txtPath As String = path & "\" & name & ".txt"

                Dim head As String = ""
                If Not File.Exists(txtPath) Then
                    head = Common.StringFormat("Time", 10, False, True) & _
                                Common.StringFormat("IsReinstall", 15, False, True) & _
                                Common.StringFormat("SKU", 70, False, True) & _
                                Common.StringFormat("Name", 50, False, True) & _
                                Common.StringFormat("Version", 10, False, True) & _
                                Common.StringFormat("Hash", 50, False, True) & _
                                Common.StringFormat("Path", 100, False, True)
                End If

                Using writer As New StreamWriter(txtPath, True, Encoding.GetEncoding("gb2312"))
                    If head.Length > 0 Then
                        writer.WriteLine(head)
                    End If
                    writer.WriteLine(text)
                    writer.Flush()
                    writer.Close()
                End Using
            Catch ex As Exception
                Common.LogException(ex.ToString(), "LogForInstall")
            End Try
        End SyncLock
    End Sub

    Private Sub SaveSendLog(ByVal strResult As String, ByVal strText As String)
        Try
            Dim text As String = ""

            If String.IsNullOrEmpty(strText) Then
                text = strResult & strText
                GoTo DO_EXIT
            End If

            'Dim strTextDes As String = strText.Replace("json=", "").Replace("%2B", "+").Replace("%26", "&")
            'Dim strTextDes As String = Common.DecryptDES(strTextDes, Common.RgbKeyString, Common.RgbKeyString)
            Dim strTextDes As String = strText

            Dim objJson As JsonObject = JsonParser.ParseString(strTextDes)
            If objJson Is Nothing Then
                text = strResult & strText
                GoTo DO_EXIT
            End If

            Dim sn As String = ""
            Dim sku As String = ""
            Dim hash As String = ""
            Dim udid As String = ""
            Dim uid As String = ""

            If objJson.ContainsKey("device") Then
                Dim jsonDevice As JsonObject = CType(objJson("device"), JsonObject)
                If jsonDevice IsNot Nothing AndAlso jsonDevice.ContainsKey("sn") Then
                    sn = CType(jsonDevice("sn"), JsonString).Value()
                End If
                If jsonDevice IsNot Nothing AndAlso jsonDevice.ContainsKey("udid") Then
                    udid = CType(jsonDevice("udid"), JsonString).Value()
                End If
            End If

            If objJson.ContainsKey("package") Then
                Dim jsonUserInfo As JsonObject = CType(objJson("package"), JsonObject)
                If jsonUserInfo IsNot Nothing AndAlso jsonUserInfo.ContainsKey("sku") Then
                    sku = CType(jsonUserInfo("sku"), JsonString).Value()
                End If
            End If

            If objJson.ContainsKey("userinfo") Then
                Dim jsonUserInfo As JsonObject = CType(objJson("userinfo"), JsonObject)
                If jsonUserInfo IsNot Nothing AndAlso jsonUserInfo.ContainsKey("uid") Then
                    uid = CType(jsonUserInfo("uid"), JsonNumber).Value()
                End If
            End If

            Try
                strResult = JsonParser.ParseString(strResult).ToString().Replace(vbCrLf, "")
            Catch
            End Try
            strResult = Common.StringFormat(strResult, 100, False, True)
            sku = Common.StringFormat(sku, 70, False, True)
            hash = Common.StringFormat(hash, 50, False, True)
            udid = Common.StringFormat(udid, 50, False, True)
            sn = Common.StringFormat(sn, 20, False, True)
            uid = Common.StringFormat(uid, 20, False, True)

            text = strResult & sku & hash & udid & sn & uid

DO_EXIT:
            Dim time As String = Common.StringFormat(DateTime.Now.ToLongTimeString(), 10, False, True)
            text = time & text

            LogForSend(text)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "SaveSendLog保存发送数据出现异常。")
        End Try
    End Sub

    Private Sub SaveNotSendLog(ByVal valueArgs As WealthHelperArgs)
        Try
            Dim strResult As String = Common.StringFormat("不在推广中", 10, False, True)
            Dim sku As String = Common.StringFormat(valueArgs.SKU, 70, False, True)
            Dim udid As String = Common.StringFormat(valueArgs.Device.DeviceID, 50, False, True)
            Dim strSN As String = Common.StringFormat(DeviceInfo.SerialNumber(valueArgs.Device), 20, False, True)
            Dim uid As String = Common.StringFormat(PluginLogin.Instance.Uid, 20, False, True)
            Dim time As String = Common.StringFormat(DateTime.Now.ToLongTimeString(), 10, False, True)

            Dim text As String = time & strResult & sku & udid & strSN & uid

            Me.LogForSend(text)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SaveNotSendLog")
        End Try
    End Sub

    Private lockerSend As New Object
    Public Sub LogForSend(ByVal text As String)
        'If Not File.Exists(System.IO.Path.Combine(Folder.AppFolder, "debug.dll")) Then
        '    Return
        'End If

        Dim dir As String = "Data\Send"
        Dim path As String = Folder.DocumentFolder.TrimEnd("\"c) & "\" & dir

        SyncLock lockerSend
            Try
                If Not Directory.Exists(path) Then
                    Directory.CreateDirectory(path)
                End If

                Dim txtPath As String = (path & "\" & DateTime.Now.ToString("yyyyMMdd")) & ".txt"

                Dim head As String = ""
                If Not File.Exists(txtPath) Then
                    head = Common.StringFormat("Time", 10, False, True) & _
                                Common.StringFormat("Result", 10, False, True) & _
                                Common.StringFormat("SKU", 70, False, True) & _
                                Common.StringFormat("Hash", 50, False, True) & _
                                Common.StringFormat("UDID", 50, False, True) & _
                                Common.StringFormat("SN", 20, False, True) & _
                                Common.StringFormat("UID", 20, False, True)
                End If

                Using writer As New StreamWriter(txtPath, True, Encoding.GetEncoding("gb2312"))
                    'writer.WriteLine("------------Time:" + DateTime.Now.ToString() + "---------------");
                    If head.Length > 0 Then
                        writer.WriteLine(head)
                    End If
                    writer.WriteLine(text)
                    writer.Flush()
                    writer.Close()
                End Using
            Catch ex As Exception
                Common.LogException(ex.ToString(), "LogForSend")
            End Try
        End SyncLock
    End Sub

#End Region

#Region "--- 判断软件是否安装 ---"

    Public Function CheckSoftExist(ByVal arg As WealthHelperArgs, ByRef strSerialExist As String) As Boolean
        If arg.PackageFromWeb Is Nothing Then
            Common.LogException(arg.SKU & "没有info")
            Return False
        End If

        Dim strIdentifier As String = arg.SKU
        Dim isResult As Boolean = True
        Dim strData As String = String.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8}", arg.Device.DeviceID, DeviceInfo.WiFiAddress(arg.Device), DeviceInfo.SerialNumber(arg.Device), strIdentifier, _
                                                                                                                                DeviceInfo.VersionNumber(arg.Device), DeviceInfo.ZJ_OpenUDID(arg.Device), DeviceInfo.ZJ_IDFA(arg.Device), _
                                                                                                                                DeviceInfo.ZJ_IDFV(arg.Device), Integer.Parse(arg.PackageFromWeb.wAdReturnType))
        'Debug.Print(strData)

        strData = Common.EncryptDES(strData, "55%g7z!@", "55%g7z!@").Replace("+", "_@_")

        '返回格式，-1非法输入，0不存在， 返回的如果是序列号则存在
        Dim strUrl As String = String.Format("http://server.tongbu.com/tbCloud/tbZJDS/appcheck.aspx?op=1&v={0}", strData)
        Dim strResult As String = Utility.GetContentStringFromUrl(strUrl, Encoding.UTF8)
        If strResult.Length = 0 OrElse strResult = "0" OrElse strResult = "-1" Then
            isResult = False
        Else
            strSerialExist = strResult
        End If

        Return isResult
    End Function

#End Region

#Region "--- 与iOS客户端交互数据 ---"

    Public Sub WriteDataToAGSPlan(ByVal args As WealthHelperArgs)
        Try
            If args.Device Is Nothing Then
                Return
            End If

            AppleStoreHelper.Instance().UploadRecord(args)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WriteDataToAGSPlan")
        End Try
    End Sub

#End Region

    Public Shared Function GetHashHex(ByVal strContent As String) As String
        Dim md5 As New System.Security.Cryptography.MD5CryptoServiceProvider()
        Dim bytHashResult As Byte() = md5.ComputeHash(Encoding.UTF8.GetBytes(strContent))

        Dim sb As New StringBuilder()
        For index As Integer = 0 To bytHashResult.Length - 1
            sb.Append(bytHashResult(index).ToString("X2"))
        Next

        Return sb.ToString().ToLower()
    End Function

#Region "--- 右下角弹框 ---"

    Private mTdCheckPop As Thread = Nothing

    Public Sub Start2CheckPop(ByVal isGetNewData As Boolean)
        If Me.mTdCheckPop IsNot Nothing AndAlso Me.mTdCheckPop.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTdCheckPop = New Thread(New ParameterizedThreadStart(AddressOf DoCheck2Pop))
        Me.mTdCheckPop.IsBackground = True
        Me.mTdCheckPop.SetApartmentState(ApartmentState.STA)
        Me.mTdCheckPop.Start(isGetNewData)
    End Sub

    Public Sub DoCheck2Pop(ByVal isGetNewData As Object)
        Try
            CheckApplistChange(isGetNewData)
            CheckPublicNotic(isGetNewData)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DoCheck2Pop")
        End Try
    End Sub

    Public Sub CheckApplistChange(ByVal isGetNewData As Object)
        Try
            Dim whArgs As New WealthHelperArgs

            Dim lstPTotal As List(Of UpdatePackageInfo) = DoCheckApplistChange(1)
            If Me.mIsChangePackagesTotal Then
                whArgs.wFunctionKey = FunctionKey.IOSZHUANQU
                Me.mLstPackagesTotal = lstPTotal
                RaiseEvent RefreshListEvent(Me, whArgs)
            End If

            Dim lstPNewDevice As List(Of UpdatePackageInfo) = DoCheckApplistChange(2)
            If mIsChangePackageNewDevice Then
                whArgs.wFunctionKey = FunctionKey.IOSNEWDEVICE
                Me.mLstPackageNewDevice = lstPNewDevice
                RaiseEvent RefreshListEvent(Me, whArgs)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "ChechApplistChange")
        End Try
    End Sub

    Private Function DoCheckApplistChange(ByVal channel As Integer) As List(Of UpdatePackageInfo)
        Dim strUrl As String = String.Empty
        Dim lstPackagesTemp As New List(Of UpdatePackageInfo)

        If channel = 1 Then
            strUrl = Me.mUrlList
        ElseIf channel = 2 Then
            strUrl = Me.mUrlNewDevice
        End If
        If strUrl.Length > 0 Then
            lstPackagesTemp = Me.GetPackagesNew(strUrl, 0, channel)
            If lstPackagesTemp Is Nothing Then
                lstPackagesTemp = New List(Of UpdatePackageInfo)
            End If
        End If

        Return lstPackagesTemp
    End Function

    Public Sub CheckPublicNotic(ByVal isGetNewData As Object)
        Try
            '1. 获取数据
            Dim strTime As String = IniSetting.GetPopString()
            If strTime.Length = 0 Then
                strTime = Date.MinValue.ToString("yyyy-MM-dd HH:mm:ss")
            End If

            'strTime = Date.MinValue.ToString("yyyy-MM-dd HH:mm:ss")
            Dim strUrl As String = String.Format(Me.mUrlCheckPop, HttpUtility.UrlEncode(strTime), Me.mAppVersion)

            'strUrl = "http://zj.tongbu.com/api/notes?time=2015-09-15+16%3a26%3a41&ver=1271"

            Dim strContent As String = Utility.GetContentStringFromUrl(strUrl, Encoding.UTF8)

            Common.Log("CheckPublicNotic:strUrl" & strUrl)
            Common.Log("CheckPublicNotic:strContent" & strContent)

            If strContent.Length = 0 Then
                Return
            End If
            Dim objJson As JsonObject = JsonParser.ParseString(strContent)
            If Not objJson.ContainsKey("stat") OrElse Not objJson.ContainsKey("data") OrElse Not objJson.ContainsKey("update_time") Then
                Return
            End If
            Dim iStat As Integer = CType(objJson("stat"), JsonNumber).Value
            If iStat <> 1 Then
                Return
            End If

            '时间没有变化说明没更新，不需要弹出提示框
            Dim strTimeNew As String = CType(objJson("update_time"), JsonString).Value
            If String.Compare(strTime, strTimeNew, True) = 0 Then
                Return
            End If

            '存储最新的时间
            IniSetting.SetPopString(strTimeNew)

            Dim arrData As JsonArray = CType(objJson("data"), JsonArray)
            If arrData IsNot Nothing AndAlso arrData.Count > 0 Then
                Dim jsonobj As New JsonObject
                jsonobj.Add("data", arrData)

                '弹出数据框
                mJsonobj = jsonobj
                mIsChangeNotice = True
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckPublicNotic")
        End Try
    End Sub

#End Region

#Region "--- 下载相关 ---"

    '下载破解软件的接口
    'Public Function DownloadCrakedSoft(ByVal url As String, ByVal deviceId As String) As Boolean
    '    Me.DownloadCrakedSoft(url, "", deviceId, "", "", "", DeviceType.iOS)
    'End Function

    '下载财富通道的接口
    Public Function DownloadWealthSoft(ByVal package As UpdatePackageInfo) As Boolean

        Dim isCrack As Boolean = True
        If package.CrackedInfo = CrakedInfo.UnCraked Then
            isCrack = False
        End If

        Me.DownloadCrakedSoft(package.wDownloadUrl, package.wIconUrl, package.DeviceId, package.Name, package.Version, package.Identifier, "", isCrack, package.wDeviceType, package.wIsNewAreaApp, True, package.wID)
    End Function

    'Public Function DownloadCrakedSoft(ByVal url As String, _
    '                                   ByVal iconUrl As String, _
    '                                   ByVal deviceId As String, _
    '                                   ByVal name As String, _
    '                                   ByVal version As String, _
    '                                   ByVal identifier As String, _
    '                                   ByVal type As DeviceType) As Boolean

    '    Me.DownloadCrakedSoft(url, iconUrl, deviceId, name, version, identifier, "", True, type)
    'End Function

    '下载财富通道的接口
    Public Function DownloadCrakedSoft(ByVal url As String, _
                                       ByVal iconUrl As String, _
                                       ByVal deviceId As String, _
                                       ByVal name As String, _
                                       ByVal version As String, _
                                       ByVal identifier As String, _
                                       ByVal savePath As String, _
                                       ByVal isCrack As Boolean, _
                                       ByVal type As DeviceType, _
                                       ByVal isNewAreaApp As Boolean, _
                                       Optional ByVal isNameFormat As Boolean = True, Optional ByVal wid As String = "") As Boolean

        Dim info As MultiThreadDownloadItemInfo = Nothing
        Dim listUrl As New List(Of KeyValuePair(Of String, String))

        url = System.Web.HttpUtility.UrlDecode(url)

        Try
            Dim key As New KeyValuePair(Of String, String)(url, "")
            listUrl.Add(key)
            listUrl.Add(key)

            info = New MultiThreadDownloadItemInfo
            With info
                .ListUrl = listUrl
                .Identifier = identifier
                .Version = version
                .LoadId = ""
                .SourceUrl = ""
                .SaveFolder = IIf(isNewAreaApp, Folder.DownloadNewAreaFolder, Folder.DownloadFolder)
                .Type = ResourceType.IPA
                .Class = ResourceClass.Software
                .IsUpdate = True
                .DeviceId = deviceId
                .IconUrl = iconUrl

                If isNameFormat Then
                    .Name = Me.GetDownloadFileName(name, version, isCrack, type, wid)
                Else
                    .Name = name
                End If

                If savePath.Length > 0 Then
                    .SaveFolder = savePath
                End If

                If Not isCrack Then
                    .ItemId = "-1" '如ItemId=-1表示这是正版软件
                End If

                If .Name.Length = 0 Then
                    info.Name = HttpUtility.UrlDecode(System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath))
                End If
            End With

        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try

        MultiThreadDownload.Instance().NewTask(info)
    End Function

    '检查此推广包，是否存在（1、文件存在   2、hash值相同）
    Public Function CheckFileMd5(ByVal pInfo As UpdatePackageInfo, ByVal functionKey As FunctionKey) As Boolean
        Dim result As Boolean = False
        Try
            Dim strPath As String = Me.GetFileDownloadPath(pInfo, functionKey)
            If System.IO.File.Exists(strPath) Then
                Dim strHash As String = Common.GetMd5Base64FromFile(strPath)
                If String.Compare(pInfo.wMd5, strHash, True) = 0 Then
                    result = True
                Else
                    File.Delete(strPath)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckFileDownload")
        End Try
        Return result
    End Function

    Public Function CheckFileExist(ByVal pInfo As UpdatePackageInfo, ByVal functionKey As FunctionKey) As Boolean
        Dim result As Boolean = False
        Try
            Dim strPath As String = Me.GetFileDownloadPath(pInfo, functionKey)
            If System.IO.File.Exists(strPath) Then
                result = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckFileDownload")
        End Try
        Return result
    End Function

    Public Function GetFileDownloadPath(ByVal info As UpdatePackageInfo, ByVal functionKey As FunctionKey) As String
        Dim strFolder As String = Folder.DownloadFolder

        If functionKey = CoreFoundation.FunctionKey.IOSNEWDEVICE Then
            strFolder = Folder.DownloadNewAreaFolder
        End If

        Dim strPath As String = String.Empty

        If info.wRegion = RegionState.LocalSource Then
            strFolder = IniSetting.GetZJLocalPackageFolder
            strPath = info.PackagePath

        Else
            strPath = Path.Combine(strFolder, _
                                    Common.ReplaceWinIllegalName(GetDownloadFileName(info.Name, info.Version, info.CrackedInfo <> CrakedInfo.UnCraked, info.wDeviceType, info.wID)))
        End If

        Return strPath
    End Function

    Public Function GetDownloadFileName(ByVal name As String, ByVal version As String, ByVal isCrack As Boolean, ByVal type As DeviceType, ByVal wid As String) As String
        If String.IsNullOrEmpty(name) AndAlso String.IsNullOrEmpty(version) Then
            Return ""
        End If

        Dim strCrack As String = ""
        If Not isCrack Then
            strCrack = "_正版"
        End If
        Dim strSuffix As String = IIf(type = DeviceType.iOS, "ipa", "apk")

        Return String.Format("{0}_v{1}{2}_{4}.{3}", name, version, strCrack, strSuffix, wid)
    End Function

#End Region

#Region "--- 图标缓存路径 ---"

    Public Shared Function GetIconCahceImage(ByVal itemId As String, ByVal version As String, ByVal identifier As String, ByVal iconUrl As String) As Image
        Dim imgValue As Image = Nothing
        Try
            If iconUrl.Length = 0 Then
                Return imgValue
            End If

            Dim strIconPath As String = GetIconCahcePath(itemId, version, identifier)
            If iconUrl.Length = 0 Then
                Return imgValue
            End If

            '下载图片
            If Not File.Exists(strIconPath) Then
                Utility.DownLoadFile(iconUrl, strIconPath)
            End If

            If File.Exists(strIconPath) Then
                imgValue = Utility.GetImageFormFile(strIconPath)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetIconCahceImage")
        End Try
        Return imgValue
    End Function

    Public Shared Function GetIconCahcePath(ByVal itemId As String, ByVal version As String, ByVal identifier As String) As String
        If itemId.Length = 0 AndAlso identifier.Length = 0 Then
            Return String.Empty
        End If
        '卓大师下载，一定会有identifier
        Dim cacheName As String = identifier
        If cacheName.Length = 0 Then
            cacheName = itemId
        End If

        Dim strWinllegalName As String = Common.ReplaceWinIllegalName(cacheName)
        Dim strJpgCachePath As String = Path.Combine(Folder.CacheFolder, "Download")

        If Not Directory.Exists(strJpgCachePath) Then
            Directory.CreateDirectory(strJpgCachePath)
        End If
        strJpgCachePath = Path.Combine(strJpgCachePath, strWinllegalName)
        If Not Directory.Exists(strJpgCachePath) Then
            Directory.CreateDirectory(strJpgCachePath)
        End If
        If version.Length > 0 Then
            strJpgCachePath = Path.Combine(strJpgCachePath, Common.ReplaceWinIllegalName([String].Format("{0}.png", version)))
        Else
            strJpgCachePath = Path.Combine(strJpgCachePath, Common.ReplaceWinIllegalName([String].Format("{0}.png", strWinllegalName)))
        End If

        Return strJpgCachePath
    End Function

#End Region

#Region "--- 判定软件完成要处理的事情 ---"

    Public Function DoAfterDownload(ByVal item As MultiThreadDownloadItem) As Boolean
        Dim isInstall As Boolean = False

        Dim package As UpdatePackageInfo = WealthHelper.Instance().GetPackageFromListBySKU(item.ItemInfo.Identifier, RegionState.Other, item.ItemInfo.FilePath)
        If package IsNot Nothing Then
            Dim strHash As String = Common.GetMd5Base64FromFile(item.ItemInfo.FilePath)

            If String.Compare(strHash, package.wMd5) <> 0 Then
                Common.LogException(String.Format("{0}{1}{2}{1}{3}", package.Name, vbCrLf, _
                String.Format("服务器MD5值：{0}", package.wMd5), _
                String.Format("下载的文件MD5值：{0}", strHash)))

                RaiseEvent CheckCanInstallEventHandler(item, New ZJCheckResultArgs(package, DownloadStatus.HashError))

                If File.Exists(item.ItemInfo.FilePath) Then
                    Try
                        File.Delete(item.ItemInfo.FilePath)
                    Catch
                    End Try
                End If

                Return isInstall
            End If
        End If

        '校验是否需要强装
        StrongInstallHelper.Instance().CheckUpdateStrongTongbutuiPath(item.ItemInfo)

        '设置苹果游戏商店下载完成之后文件名
        AppleStoreHelper.Instance.CheckUpdateFilePathSetting(item.ItemInfo)

        isInstall = True
        Return isInstall
    End Function

#End Region

#Region "--- 安装相关 ---"

    Public Sub SendInstallSucceed(ByVal device As IDevice, ByVal package As PackageInfo, _
                                                   ByVal type As String, ByVal isSucceed As Boolean, ByVal errMsg As String, ByVal strIMEI As String, ByVal srtPhoneNumber As String, Optional ByVal strRepairWifiMac As String = "", Optional ByVal strRepairUDID As String = "", Optional ByVal strTime As String = "")

        Dim args As New WealthHelperArgs

        args.SKU = package.Identifier
        args.Device = device
        args.PackagesFromLocal = package

        args.PhoneNumber = srtPhoneNumber
        args.IMEI = strIMEI

        args.Type = type '0: 推广软件; 1:强推软件
        args.Succeed = IIf(isSucceed, "1", "0")
        args.ErrMsg = errMsg

        args.RepairWifiMac = strRepairWifiMac '修复数据是 要自己构造这个值
        args.RepairUDID = strRepairUDID
        args.RepairTime = strTime

        WealthHelper.Instance().InstallSucceed(args)
    End Sub

    Public Function CheckCanInstall(ByVal pinfo As UpdatePackageInfo, ByVal versionNumber As Integer) As InstallState
        Dim result As InstallState = InstallState.Nomal

        If versionNumber < 600 Then
            result = InstallState.IOS4CannotInstall
            GoTo DoExit
        End If

        Select Case pinfo.wAdReturnType
            Case AdReturnType.Mac
                'ios7.x不允许安装
                If versionNumber >= 700 Then
                    result = InstallState.IOS7CannotInstall
                    GoTo DoExit
                End If

            Case AdReturnType.IDFA, AdReturnType.IDFV
                'ios5.x不允许安装
                If versionNumber < 600 Then
                    result = InstallState.IOS5CannotInstall
                    GoTo DoExit
                End If
        End Select

        '判断版本支持，有的广告主支持iOS7
        If pinfo.wSupportOSVersion = SupportOSVersion.None Then
            result = InstallState.SettingError
            GoTo DoExit
        End If
        If pinfo.wSupportOSVersion <> SupportOSVersion.All Then
            If (pinfo.wSupportOSVersion And SupportOSVersion.IOS5) <> SupportOSVersion.IOS5 Then
                'ios5.x不允许安装
                If versionNumber < 600 Then
                    result = InstallState.IOS5CannotInstall
                    GoTo DoExit
                End If
            ElseIf (pinfo.wSupportOSVersion And SupportOSVersion.IOS6) <> SupportOSVersion.IOS6 Then
                'ios6.x不允许安装
                If versionNumber < 700 AndAlso versionNumber >= 600 Then
                    result = InstallState.IOS6CannotInstall
                    GoTo DoExit
                End If
            ElseIf (pinfo.wSupportOSVersion And SupportOSVersion.IOS7) <> SupportOSVersion.IOS7 Then
                'ios7.x不允许安装
                If versionNumber < 800 AndAlso versionNumber >= 700 Then
                    result = InstallState.IOS7CannotInstall
                    GoTo DoExit
                End If
            End If
        End If

        If (pinfo.wAdReturnType And AdReturnType.Mac) <> 1 Then
            '该广告主不支持mac对账方式，需要获取设备信息
            result = InstallState.Need2AppstoreGetInfo

        ElseIf pinfo.wAdReturnType <> AdReturnType.Mac AndAlso versionNumber >= 700 Then
            '该软件是7.x以下的用mac对账，7.x以上的用其他方式对账
            result = InstallState.Need2AppstoreGetInfo
        End If

DoExit:
        Return result
    End Function

    Public Function CheckSendWillInstallData(ByVal info As UpdatePackageInfo, ByVal _iPhone As IDevice) As InstallState
        Dim result As InstallState = InstallState.Nomal

        If info.wIsRealTime Then
            result = Me.PostWillInstallRealTimeData(info, _iPhone)
        End If

        Return result
    End Function

    Private Function PostWillInstallRealTimeData(ByVal info As UpdatePackageInfo, ByVal _iPhone As IDevice) As InstallState
        Dim result As InstallState = InstallState.PostRealtimeDataFailed
        Dim strMac As String = DeviceInfo.WiFiAddress(_iPhone)

        Try

            Dim iChannel As Integer = 1

            If info.PackagePath.Contains(Folder.DownloadNewAreaFolder) Then
                iChannel = 2
            End If

            'Dim strJson As String = String.Format("{{""softid"":""{0}"",""sku"":""{1}"",""appleid"":""{2}"",""mac"":""{3}"",""sn"":""{4}"",""udid"":""{5}"", " & _
            '"""openudid"":""{6}"",""installtime"":""{7}"",""IDFA"":""{8}"",""IDFV"":""{9}"", ""versionnumber"":""{10}"", ""channel"":""{11}""}}", _
            '        info.wID, info.Identifier, info.ItemId, strMac, DeviceInfo.SerialNumber(_iPhone), _
            '        _iPhone.DeviceID, DeviceInfo.ZJ_OpenUDID(_iPhone), Now.ToString(), _
            '        DeviceInfo.ZJ_IDFA(_iPhone), DeviceInfo.ZJ_IDFV(_iPhone), DeviceInfo.VersionNumber(_iPhone), iChannel)


            Dim jsDevice As New JsonObject
            jsDevice.Add("installtime", Now.ToString())
            jsDevice.Add("sendurl", "")
            jsDevice.Add("appleid", info.ItemId)
            jsDevice.Add("mac", strMac)
            jsDevice.Add("idfa", DeviceInfo.ZJ_IDFA(_iPhone))
            jsDevice.Add("adFrom", "4")
            jsDevice.Add("IPAddress", Common.GetIPAddress())
            jsDevice.Add("ua", "")
            jsDevice.Add("versionnumber", DeviceInfo.VersionNumber(_iPhone))
            jsDevice.Add("sku", info.Identifier)
            jsDevice.Add("softid", info.wID)
            jsDevice.Add("idfv", DeviceInfo.ZJ_IDFV(_iPhone))
            jsDevice.Add("from", "0")
            jsDevice.Add("udid", _iPhone.DeviceID)
            jsDevice.Add("md5", "")
            jsDevice.Add("sn", DeviceInfo.SerialNumber(_iPhone))
            jsDevice.Add("openudid", DeviceInfo.ZJ_OpenUDID(_iPhone))
            jsDevice.Add("channel", iChannel)

            Dim strJson As String = JsonParser.SaveString(jsDevice)

            'Dim strPost As String = String.Format("{0}", Common.EncryptDES(strJson, "+*^$@)%!", "+*^$@)%!"))
            'Dim strResult As String = WealthHelper.PostData("http://zjds.api.tongbu.com/Api.ashx?SendToAdvertiser", strPost)

            Dim strPost As String = String.Format("{0}", Common.EncryptDES(strJson, Common.RgbKeyString, Common.RgbKeyString))
            Dim strResult As String = WealthHelper.PostData(WealthHelper.Instance.mUrlPostRealtime, strPost)

            Dim objJson As JsonObject = JsonParser.ParseString(strResult)

            If objJson.ContainsKey("Code") Then
                Dim code As JsonNumber = CType(objJson("Code"), JsonNumber)
                Dim jData As JsonNumber = CType(objJson("Data"), JsonNumber)

                If code.Value = 0 Then
                    If jData.Value = 1 Then
                        result = InstallState.Nomal
                    ElseIf jData.Value = -1 Then
                        '兼容安装的时候发现广告主已下架的情况
                        result = InstallState.AdOutofDate
                    End If
                End If
            End If

            If result <> InstallState.Nomal Then
                If info.wID.Length = 0 OrElse info.wID = "0" Then
                    result = InstallState.PostRealtimeDataWIDNULL
                End If

                Common.LogException("PostWillInstallRealTimeData error:" & strResult)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "PostWillInstallRealTimeData")
        End Try

        Return result
    End Function

#End Region

End Class

Public Class WealthHelperArgs
    Inherits EventArgs

    Private mSku As String = String.Empty
    Public Property SKU() As String
        Get
            Return Me.mSku
        End Get
        Set(ByVal value As String)
            Me.mSku = value
        End Set
    End Property

    Private mPackage As PackageInfo = Nothing
    Public Property PackagesFromLocal() As PackageInfo
        Get
            Return mPackage
        End Get
        Set(ByVal value As PackageInfo)
            mPackage = value
        End Set
    End Property

    Private mUpdatePackageInfo As UpdatePackageInfo = Nothing
    Public Property PackageFromWeb() As UpdatePackageInfo
        Get
            Return mUpdatePackageInfo
        End Get
        Set(ByVal value As UpdatePackageInfo)
            mUpdatePackageInfo = value
        End Set
    End Property

    Private mDevice As IDevice
    Public Property Device() As IDevice
        Get
            Return mDevice
        End Get
        Set(ByVal value As IDevice)
            mDevice = value
        End Set
    End Property

    Private mIMEI As String
    Public Property IMEI() As String
        Get
            Return mIMEI
        End Get
        Set(ByVal value As String)
            mIMEI = value
        End Set
    End Property

    Private mPhoneNumber As String
    Public Property PhoneNumber() As String
        Get
            Return mPhoneNumber
        End Get
        Set(ByVal value As String)
            mPhoneNumber = value
        End Set
    End Property

    Private mType As String = "-1" '0: 推广软件; 1:强退软件
    Public Property Type() As String
        Get
            Return mType
        End Get
        Set(ByVal value As String)
            mType = value
        End Set
    End Property

    Private mSucceed As String '0: 安装失败; 1: 安装成功
    Public Property Succeed() As String
        Get
            Return mSucceed
        End Get
        Set(ByVal value As String)
            mSucceed = value
        End Set
    End Property

    Private mErrMsg As String  '安装失败原因
    Public Property ErrMsg() As String
        Get
            Return mErrMsg
        End Get
        Set(ByVal value As String)
            mErrMsg = value
        End Set
    End Property

    Private mPhoneSerialExist As String
    Public Property PhoneSerialExist() As String
        Get
            Return mPhoneSerialExist
        End Get
        Set(ByVal value As String)
            mPhoneSerialExist = value
        End Set
    End Property

    Private mWebReturnSoftInstalled As Boolean
    Public Property WebReturnSoftInstalled() As Boolean
        Get
            Return mWebReturnSoftInstalled
        End Get
        Set(ByVal value As Boolean)
            mWebReturnSoftInstalled = value
        End Set
    End Property

    Private mIsCheckSoftInstall As Boolean
    Public Property IsCheckSoftInstall() As Boolean
        Get
            Return mIsCheckSoftInstall
        End Get
        Set(ByVal value As Boolean)
            mIsCheckSoftInstall = value
        End Set
    End Property

    Private mFunctionKey As FunctionKey = FunctionKey.IOSZHUANQU
    Public Property wFunctionKey() As FunctionKey
        Get
            Return Me.mFunctionKey
        End Get
        Set(ByVal value As FunctionKey)
            Me.mFunctionKey = value
        End Set
    End Property


    '修复数据 需要
    Private mRepairWifiMac As String = ""
    Public Property RepairWifiMac() As String
        Get
            Return mRepairWifiMac
        End Get
        Set(ByVal value As String)
            mRepairWifiMac = value
        End Set
    End Property

    Private mRepairUDID As String = ""
    Public Property RepairUDID() As String
        Get
            Return mRepairUDID
        End Get
        Set(ByVal value As String)
            mRepairUDID = value
        End Set
    End Property

    Private mRepairTime As String = ""
    Public Property RepairTime() As String
        Get
            Return mRepairTime
        End Get
        Set(ByVal value As String)
            mRepairTime = value
        End Set
    End Property

    'Private mlstPackages As List(Of UpdatePackageInfo) = Nothing
    'Public Property lstPackages() As List(Of UpdatePackageInfo)
    '    Get
    '        Return mlstPackages
    '    End Get
    '    Set(ByVal value As List(Of UpdatePackageInfo))
    '        mlstPackages = value
    '    End Set
    'End Property

End Class

Public Enum AreaType
    None = -1
    Yuzhuang = 1                '预装应用
    NewDevice = 2             '新机专区

    'ReMenBiBei = 4            '热门必备
    'ReMenYouXi = 5          '热门游戏
    'ReMenYingYong = 6   '热门应用
    'Android = 7                  'Android预装
End Enum

Public Class ZJCheckResultArgs
    Inherits EventArgs

    Private mPackage As UpdatePackageInfo
    Public Property Package() As UpdatePackageInfo
        Get
            Return mPackage
        End Get
        Set(ByVal value As UpdatePackageInfo)
            mPackage = value
        End Set
    End Property

    Private mDownloadStatus As DownloadStatus
    Public Property DownloadStatus() As DownloadStatus
        Get
            Return Me.mDownloadStatus
        End Get
        Set(ByVal value As DownloadStatus)
            Me.mDownloadStatus = value
        End Set
    End Property

    Sub New(ByVal tempPackage As UpdatePackageInfo, ByVal status As DownloadStatus)
        Me.mPackage = tempPackage
        Me.mDownloadStatus = status
    End Sub

End Class
