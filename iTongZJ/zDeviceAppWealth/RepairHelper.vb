﻿Public Class RepairHelper

    Private Shared mDicPackageInfo As Dictionary(Of String, PackageInfo) = Nothing
    Private Shared mDicPackageInfoNewArea As Dictionary(Of String, PackageInfo) = Nothing
    Private Shared ReadOnly mStrPath As String = Folder.DownloadFolder
    Private Shared ReadOnly mStrPathNewArea As String = Folder.DownloadNewAreaFolder
    Private Shared mDicWifiMac As New Dictionary(Of String, String)
    Private Shared constant As Char() = {"0"c, "1"c, "2"c, "3"c, "4"c, "5"c, _
 "6"c, "7"c, "8"c, "9"c, "a"c, "b"c, _
 "c"c, "d"c, "e"c, "f"c, "g"c, "h"c, _
 "i"c, "j"c, "k"c, "l"c, "m"c, "n"c, _
 "o"c, "p"c, "q"c, "r"c, "s"c, "t"c, _
 "u"c, "v"c, "w"c, "x"c, "y"c, "z"c}
    Private Shared mStrFilePath As String = String.Format("{0}\Data\Send\20151016.txt", Folder.DocumentFolder.TrimEnd("\"c))
    Private Shared mStrFileRenamePath As String = String.Format("{0}\Data\Send\20151016l.txt", Folder.DocumentFolder.TrimEnd("\"c))
    
    Private Shared mStrFilePath2 As String = String.Format("{0}\Data\Send\20151017.txt", Folder.DocumentFolder.TrimEnd("\"c))
    Private Shared mStrFileRenamePath2 As String = String.Format("{0}\Data\Send\20151017l.txt", Folder.DocumentFolder.TrimEnd("\"c))


    Public Shared Sub Repair() '20151017

        '服务器setting开关控制
        If ServerIniSetting.GetZJCanRepair Then
            SetLog("UnRepair-C")
            Return
        End If

        If Not IniSetting.GetZJIsRepair Then
            SetLog("UnRepair-I")
            Return
        End If

        Try
            Dim idevice As iPhoneDevice = Nothing

            '获取 需要修复的数据
            Dim lstInfos As List(Of RepairInfo) = GetExceptionData()

            If lstInfos.Count <= 0 Then
                GoTo Do_Exit
            End If

            '获取 本地软件列表
            If mDicPackageInfo Is Nothing Then
                mDicPackageInfo = LoadLocalPackageInfo(mStrPath)
            End If
            If mDicPackageInfoNewArea Is Nothing Then
                mDicPackageInfoNewArea = LoadLocalPackageInfo(mStrPathNewArea)
            End If


            Dim pacakge As New PackageInfo

            For Each item As RepairInfo In lstInfos

                If item.StrSku = "com.tongbu.AGSPlan" Then
                    SetLog("过滤的数据：" & item.StrTag)
                    Continue For
                End If

                Dim isFromNew As Boolean = False
                '找包 优先从ios专区找 找不到再从新机专区找
                If mDicPackageInfo.ContainsKey(item.StrSku) Then
                    pacakge = mDicPackageInfo(item.StrSku)
                ElseIf mDicPackageInfoNewArea.ContainsKey(item.StrSku) Then
                    pacakge = mDicPackageInfoNewArea(item.StrSku)
                    isFromNew = True
                End If

                If Not isFromNew AndAlso WealthHelper.Instance().GetPackageFromListBySKU(pacakge.Identifier, RegionState.Other, pacakge.PackagePath) Is Nothing AndAlso mDicPackageInfoNewArea.ContainsKey(item.StrSku) Then
                    pacakge = mDicPackageInfoNewArea(item.StrSku)
                End If

                If String.IsNullOrEmpty(pacakge.PackagePath) Then
                    SetLog("找不到包的数据：" & item.StrTag)
                    Continue For
                End If

                '找到 wifimac 同一个sn 都用同一个 wifimac
                Dim strWifiMac As String = ""
                If mDicWifiMac.ContainsKey(item.StrSN) Then
                    strWifiMac = mDicWifiMac(item.StrSN)
                Else
                    strWifiMac = CreateWifiMac(item.StrSN, 8)
                End If

                If strWifiMac.Length <= 0 Then
                    SetLog("WifiMac 生成失败：" & item.StrTag)
                    Continue For
                End If

                SetLog("处理数据：WifiMac " & strWifiMac & "      " & item.StrTag)

                idevice = New iPhoneDevice(item.StrSN, item.StrUdid)
                If IniSetting.GetZJSendRepair Then
                    WealthHelper.Instance().SendInstallSucceed(idevice, pacakge, "0", True, "数据修复" & Date.Now, "", "", strWifiMac, item.StrUdid, item.StrTime)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Repair")
        End Try
Do_Exit:
        Try
            If File.Exists(mStrFilePath) Then
                File.Move(mStrFilePath, mStrFileRenamePath)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Repair-1")
        End Try

        Try
            If File.Exists(mStrFilePath2) Then
                File.Move(mStrFilePath2, mStrFileRenamePath2)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Repair-2")
        End Try

        IniSetting.SetZJIsRepair(False)

    End Sub

    Private Shared Function LoadLocalPackageInfo(ByVal strPath As String) As Dictionary(Of String, PackageInfo)
        Dim dicPackageInfo As New Dictionary(Of String, PackageInfo)

        If Not Directory.Exists(strPath) Then
            GoTo Do_Exit
        End If

        Dim strDirInfo As New System.IO.DirectoryInfo(strPath)

        Try
            For Each strFileInfo As FileInfo In strDirInfo.GetFiles("*.ipa", SearchOption.TopDirectoryOnly)
                Dim pinfo As PackageInfo = PackageInfoReader.GetPackageInfo(strFileInfo.FullName)

                If String.IsNullOrEmpty(pinfo.Identifier) Then
                    Continue For
                End If
                If Not dicPackageInfo.ContainsKey(pinfo.Identifier) Then
                    dicPackageInfo.Add(pinfo.Identifier, pinfo)
                Else
                    Dim fi As FileInfo = New FileInfo(dicPackageInfo(pinfo.Identifier).PackagePath)
                    If Date.Compare(fi.CreationTime, strFileInfo.CreationTime) < 1 Then
                        dicPackageInfo(pinfo.Identifier) = pinfo
                    End If
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadLocalPackageInfo")
        End Try
Do_Exit:
        Return dicPackageInfo
    End Function

    Private Shared Function GetExceptionData() As List(Of RepairInfo)
        Dim lstInfos As New List(Of RepairInfo)

        Try
            Dim lstInfo1 As List(Of String) = GetFile(mStrFilePath)
            SetLog("16 匹配数量为：" & lstInfo1.Count)
            File2Data(lstInfo1, "2015-10-16", lstInfos)

            Dim lstInfo2 As List(Of String) = GetFile(mStrFilePath2)
            SetLog("17 匹配数量为：" & lstInfo2.Count)
            File2Data(lstInfo2, "2015-10-17", lstInfos)

Do_Exit:
            SetLog("任务数量为：" & lstInfos.Count)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetExceptionData")
        End Try

        Return lstInfos
    End Function

    Private Shared Function GetFile(ByVal strFilePath As String) As List(Of String)
        Dim lstInfo As New List(Of String)
        Try
            If Not File.Exists(strFilePath) Then
                SetLog("未找到文件：" & strFilePath)
                GoTo Do_Exit
            End If

            For Each strItem As String In File.ReadAllLines(strFilePath, Encoding.GetEncoding("gb2312"))
                If strItem.Contains("不在推广中") Then
                    lstInfo.Add(strItem)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetFile")
        End Try
Do_Exit:
        Return lstInfo
    End Function

    Private Shared Sub File2Data(ByVal lstInfo As List(Of String), ByVal strT As String, ByRef lstInfos As List(Of RepairInfo))
        Try
            If lstInfo.Count <= 0 Then
                Return
            End If

            Dim temp As RepairInfo
            For Each strItem As String In lstInfo
                If strItem.Length <= 0 Then
                    Continue For
                End If
                Dim strTime = strItem.Substring(0, 10).Trim() 'time
                Dim strResult = strItem.Substring(9, 6).Trim() 'Result
                Dim strsku = strItem.Substring(15, 70).Trim() 'sku
                Dim strudid = strItem.Substring(85, 50).Trim() 'udid
                Dim strSN = strItem.Substring(135, 20).Trim() 'SN
                Dim struid = strItem.Substring(155, 20).Trim() 'uid
                temp = New RepairInfo(String.Format("{0} {1}", strT, strTime), strResult, strsku, strudid, strSN, struid, strItem)
                lstInfos.Add(temp)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "File2Data")
        End Try

    End Sub

    Private Shared Function CreateWifiMac(ByVal strSN As String, ByVal iLength As Integer) 't0:0b:f1:10:4b:b2
        Dim strResult As String = ""

        Try
            Dim rd As New Random()
DO_CONTINUE:
            strResult = "t0:0b:"
            For i As Integer = 1 To iLength
                If i Mod 2 = 0 AndAlso i <> iLength Then
                    strResult = strResult & constant(rd.[Next](36)) & ":"
                Else
                    strResult = strResult & constant(rd.[Next](36))
                End If
            Next

            If mDicWifiMac.ContainsValue(strResult) Then
                GoTo DO_CONTINUE
            Else
                mDicWifiMac.Add(strSN, strResult)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateWifiMac")
        End Try

        Return strResult
    End Function

    Private Shared Sub SetLog(ByVal strMsg As String)
        Common.Log(String.Format("----修复入库失败数据----  :{0}", strMsg))
    End Sub

End Class

Public Class RepairInfo

    Private mStrTime As String
    Private mStrResult As String
    Private mStrSku As String
    Private mStrUdid As String
    Private mStrSN As String
    Private mStrUid As String
    Private mStrTag As String


    Public Property StrTime() As String
        Get
            Return Me.mStrTime
        End Get
        Set(ByVal value As String)
            Me.mStrTime = value
        End Set
    End Property

    Public Property StrResult() As String
        Get
            Return Me.mStrResult
        End Get
        Set(ByVal value As String)
            Me.mStrResult = value
        End Set
    End Property

    Public Property StrSku() As String
        Get
            Return Me.mStrSku
        End Get
        Set(ByVal value As String)
            Me.mStrSku = value
        End Set
    End Property

    Public Property StrUdid() As String
        Get
            Return Me.mStrUdid
        End Get
        Set(ByVal value As String)
            Me.mStrUdid = value
        End Set
    End Property

    Public Property StrSN() As String
        Get
            Return Me.mStrSN
        End Get
        Set(ByVal value As String)
            Me.mStrSN = value
        End Set
    End Property

    Public Property StrUid() As String
        Get
            Return Me.mStrUid
        End Get
        Set(ByVal value As String)
            Me.mStrUid = value
        End Set
    End Property

    Public Property StrTag() As String
        Get
            Return Me.mStrTag
        End Get
        Set(ByVal value As String)
            Me.mStrTag = value
        End Set
    End Property

    Public Sub New(ByVal strTime As String, ByVal strResult As String, ByVal strSku As String, ByVal strUdid As String, ByVal strSN As String, ByVal strUid As String, ByVal strTag As String)
        mStrTime = strTime
        mStrResult = strResult
        mStrSku = strSku
        mStrUdid = strUdid
        mStrSN = strSN
        mStrUid = strUid
        mStrTag = strTag
    End Sub

End Class
