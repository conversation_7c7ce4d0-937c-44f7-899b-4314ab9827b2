﻿Public Class AutoRunHelper

    Public Shared Function GetAutoRunState() As Boolean
        Dim strAutoRun As String = String.Empty

        Try
            Dim key As Microsoft.Win32.RegistryKey = Microsoft.Win32.Registry.CurrentUser.OpenSubKey("Software\Microsoft\Windows\CurrentVersion\Run", False)

            If key IsNot Nothing Then
                Dim value As Object = key.GetValue(Path.GetFileNameWithoutExtension(Application.ExecutablePath))
                If value IsNot Nothing Then
                    strAutoRun = value.ToString()
                End If
            End If
        Catch
        End Try

        Dim result As Boolean = False
        If Not String.IsNullOrEmpty(strAutoRun) Then
            result = True
        End If

        Return result
    End Function

    Public Shared Function SetAutoRun(ByVal blnAutoRun As Boolean) As Boolean
        Dim result As Boolean = True
        Dim value As Boolean = GetAutoRunState()

        '如果设置的值，与注册表中相同，则不需要重新设置
        If value = blnAutoRun Then
            Return result
        End If

        Try
            Dim key As Microsoft.Win32.RegistryKey = Microsoft.Win32.Registry.CurrentUser.OpenSubKey("Software\Microsoft\Windows\CurrentVersion\Run", True)

            If key Is Nothing Then
                result = False
                Return result
            End If

            If blnAutoRun Then
                key.SetValue(Path.GetFileNameWithoutExtension(Application.ExecutablePath), String.Format("""{0}"" /AutoStart", Application.ExecutablePath))
            Else
                key.DeleteValue(Path.GetFileNameWithoutExtension(Application.ExecutablePath))
            End If

        Catch ex As Exception
            result = False
            Common.LogException(ex.ToString(), "SetAutoRun")
        End Try

        Return result
    End Function

    Public Shared Sub CheckAutoRun()
        If IniSetting.GetAutoRunState() AndAlso Not AutoRunHelper.GetAutoRunState() Then
            AutoRunHelper.SetAutoRun(True)
        End If
    End Sub

End Class
