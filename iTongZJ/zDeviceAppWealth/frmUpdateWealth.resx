<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btn_normal.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEgAAAAYCAYAAABZY7uwAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAAHkSURBVFhH7di/TgJBEAZwBCFESAxIYQSDNhYYwWiM
        IRJNxD+FWKiVMTEGO7WwsOcFfAF7Wws73+6cWTS3O353y8FxNlv8Ynbdb5KZEI69lOd5Tgi46fjgpuOD
        m44Pbk7RDFkiTbIdAZ/nHOdRXWV2fjGTLVVPs5X6S66yMshVVkewMuDznOO8rGksElAlaACj4jyqq2QX
        6sf52sbj3FrnutDoXhXXTy5s+Byf5xznZU1jkYAWQY2PivOorkJN9out815p//6gfPjQKXef9qzoHJ/n
        HOdlTWORANR0VKiuwp8ENRw0CAvOcV7WNBZSauf2zQblQhjNLveeP2xkhqC6SqFxdKk3nd69+7TRz3Ne
        1jQWEhqIhHIhjGbRQCSZIaiuwt8pesNoIJJ+nvOyprGQfgawFSSOAdHfmyBxDOj1/asfJK4Bwf+xmAZk
        7OniGJC+ltyA3IBMbkBDqK7iBjSE6ir/MiAblAthNMsDsJEZguoqaEA2+vnIAyLw8S6gXBDZLHy8CzKD
        6ipyQOjRLunnxxlQ3KZ6F6Nfwmd6w1FxXtY0FgmY9DZfI6iuQnep1oR3sU1Z01gkgN/ncJPjvA/iXOj7
        ILqNZ+hW3qBm2xFv823O5WvNtKxpLJy/4Kbjg5vOLy/1DXS5rbOTehFFAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnCourse.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnSplit.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="chkScore.tbIconChecked" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAAr/AAAK/wE0YpqCAAAEmElE
        QVRYR9WXbUxTZxTH+2GyKciQSwv08lLc5kywNQvRzQ0xkMgygWEWWDIjathMlg1KWAOUOdZ06WIIAQQH
        8aUysaAIZtkX9sJcgJm4oKBCLNmiTZN9I2SaMLaoW3L3/1/vbW5rG+3ol53kl/R5zvnf+5zznHvvU936
        E7/pYALIAyWgPAL0MUaARg/aQTL1evtMPhgEfwApAvQxJj+W+rPnL2BJuniQBTYBSwToY0w8NHrQDpIx
        1qUAJmcGDBAjQJ85TtxYafri9s3srvkJjJ/Cgor0TVfvJh+88OHTGwo3aOKDoI8xhubp3009fl+s9CAB
        cO05IAOE1QP6cjaZLa94zp6f6+sfUPW6LSAXsBqJYG0EEuPzyozZHd4bpu5bfyYWVBVKksTdG1n33vBB
        +J9M3/mrz9R9++9Y6WHZwAQeq9/26mvGL88MXkMBlncWv16odI+uDLALwokCrLEUC7j5pKnbJwmVzmHM
        xSkJ3F+VlZcWGh+KVm/46Nt/MBcTPYy7z0c4rE5ly9aXBSQ+iaSlD2qsJzEXpxagPLX23Gb9u8deZGA4
        1piLhcy2+dGsjluS6Lx8BXP7eXMlASlavVD7A6Sx0cMsDqdrs62hKaKeyZ8ZHBpl8r3H3Vy/vPuBAojO
        qa7MVq83qczOZ+WRC2QcnhvObPtFQsxyfF75LsyVaxOIVk9NrPQwy4lTp7uwu969+w6E1fcPnBtm8mz9
        7QU7uH5LUAGMjqmJjMPzkuiam1yVvjGJIhXROdNOH0nZ39uKObZbUAGi1YcWYCV6mMXd1z+hJDiZbTIF
        6fHM820vkYamZnX9wQVIyK+uEJ1z98TPbkrGlmk35mRxetOlas6RtMYJts56xRdUgGj1oQVYiR5mKXtz
        d8Xg0Mg9JomEA/pjJ/uq1eSV1lfXH1wAYBCqjh81OmaxG7NSuv2yI9U6Wmp03Hggj1tmllZbStk68oWp
        0SYQrT60ACvRw/iNN3z8yadH1WRPnfY4jnT3lKIoDzhGZywpra+u/5ECcPIFQ93FmbRD16WHXFtWf697
        u0ttHfUCoQWISh+mAP9ZD2MBZD12eUYtApJfVn9rWl/Wg6AC8DP4LFj7TO6uktTGqaVU3EBFXzumbR3C
        2DJNAvdxSJEv/iR6xlITKz2Mn0H5+5+/vaCEu60mTkJanzDWrC0AD0LPATqFpN1trtSGq5KM7eclLErb
        OoQX26pJYESo+b5K8T1WL9R8twear2Klh/EgpJ4jhHpbg0tNPkzrE8ZmawvAQxCDngd8g+Ykln7uSnrr
        SGfCDqsVY7V16GOh3gB6TQJFevv0HcF6sXr1SxUG+MLq6WOiiF2EZmes9DAehXnOZ2LszpzaunpXY/Oh
        zj1792nXTx9jeOpNCBTAYLui8/v9vR6Pp10UxUo4+U7gjhwA7yjjcvrcbnerz+froUZdAH9/4707/v6Q
        X8psuS7/cRHqfpSE2jEppf6nwJ8Z+qoHfNLXs3cuxVLPJBYXFzvGx8ftubm57Ga+EwpAEdimjC30jY2N
        2RYWFvhZlHVyAWi4UDIYBH+BSEbfCNArGpn/q/6h6XT/AlxjTq8+2Lu3AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="chkScore.tbIconIndeterminate" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAAsMAAALDAE/QCLIAAACr0lE
        QVRYR+2XzWtTQRTF30ZEbLTNhwixrV90EytI0ZUbBRe1lEKrG8FNNCBYXNSFgdA/IIG6a1fZxUKxUlxJ
        G4RAutQiLlyGrEoMkXYhCK7Gc4YZnTEz5IW8ZS78oO/dc+bN3L43cxOoSIAZMAcWPDBHDbWBEEKSyh+Q
        22AT/ATCA3PUUBuZX8VpMAGugesemKOGWsufBFzcNKAg7YE5au6DlB4Ak7mbevXpOJ579/zk1J0ppe2C
        OWqg/QHPvaj8iBHAeV0CF4DTD5ijhoUYMfzBTZABcXAGxDwwRw21t/QAmMz22NO3OZXr6R/Lbj2BZycq
        P2ISXGQO9PQDFmHS8AfzgG9BLJ7bWUyu1FvJlX1hU28xR43SzusBMJnfJyZmzof1U0tPVH4E//v8LGOF
        QmGxWCy2SqWSMOE95qhR2mnDL79vWSX3wzX1ltbRowfAZDhKX356ovIj+H3L+67Fa5jTOnoMv1kA14P/
        oXX06AHsBbh9Gq3zF8Dt07j8iL8FcC3cROvoMfzDAgwLoG4OC9BjE/qudfToAewFhPP7C9C/HxF2EzTn
        bxWAx+BZwGNoiQ9yPPyQOWqU1joG0aTIYyiMn1p6ovIjeAzK8x9H3RIX6lj8IXPUKK11DLIRugJ0dXpx
        GViNUGJ57/F/Gi+J5d1H8FiN0CB+BBsh2UeEhFqrEWJjw/b2KhgFLhNhjoWaBXYrnP98lHjxMXvqxoNz
        StsFc1wotB147FZ4AD+CrTDbWy5MvskemKOGnazVCgfNZnOjUqmspdPph7jkntAFc+VyudhoNNZxLc0E
        kwk+fDuuPdtqivHVL64fMRLmsm8a4v3Xo31cR+ZndDqd17VaLZ/JZPg2c0/ogrlqtfqy3W6v4dry8484
        2AS/gC+Y2wYp5ZGoyfTlNxcwqH+w+YvgD0uwxJYL/0c2AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="chkScore.tbIconUnChecked" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAAr/AAAK/wE0YpqCAAACPElE
        QVRYR+2Xv0tbURTH3yKlaFtNTCmk/mqLS6pQxE5dLHSwIi66CC5phULFxaFZ/AM66KZTtihILaWTtKGQ
        wdGW0qFjyBwiOgiFTq/fz+M9eDe5Nyp5U8mBD+TmfL+X807ee/fECyMtpsScWHBADg1az/f9gEzhBzwT
        ++JC+A7IoUGbmD+MXjEsHotJB+TQoDX8g4KLmxAIsg7IoXkpMtEGKuZ55t3JeWr149sb4zPjobYFcmik
        PZXnRVJ+RZ+grjFxX1j9ghwaGtEX83vTIidS4ra45YAcGrRPow1UzOHA6w+rYe5S/0D+4JU8n5LyK0bE
        KDlxqV/QhJGY35sX3AU2kw2089EGKuZvz/DUvSaNE7R4kvIr+PV5LK16C2gnYv7g+bYJ27EQbaBi2MWm
        cYInKb+C59uqa8NkzN9tQLcB4ZfXoduAaINuA/6DBnAM3hE2oQ20xjGoIeXKxxBaPEn5FRyD7c7/ZtAa
        xyCD0ENhE9t4IIxBKL32daVJ4yS99mVZHmMQ6sSvYBC68hwh0BqDEIMN4+0j0S9sJiBHo2aFOQoXvp+l
        17/lbz5ZvBtqWyDHhUrbkMcchTvwKxiFGW+5sHZ3Mjk0TLLGKOzVarXdUqm0lc1ml7TkndACuWKx+L5a
        re5oHZhBxXhHv88rbw5q/tDmT9ufmABy+b2q//nX2bHWifmJRqOxXalUCrlcjruZd0IL5Mrl8ka9Xt/S
        2vDzISX2xR/hCnKHIhN6AsJiruWPX0Cn/s7q971/RQyElM1CWLMAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="tipDgvCell.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>279, 17</value>
  </metadata>
  <data name="chkJudgeInstall.tbIconChecked" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAAr/AAAK/wE0YpqCAAAEmElE
        QVRYR9WXbUxTZxTH+2GyKciQSwv08lLc5kywNQvRzQ0xkMgygWEWWDIjathMlg1KWAOUOdZ06WIIAQQH
        8aUysaAIZtkX9sJcgJm4oKBCLNmiTZN9I2SaMLaoW3L3/1/vbW5rG+3ol53kl/R5zvnf+5zznHvvU936
        E7/pYALIAyWgPAL0MUaARg/aQTL1evtMPhgEfwApAvQxJj+W+rPnL2BJuniQBTYBSwToY0w8NHrQDpIx
        1qUAJmcGDBAjQJ85TtxYafri9s3srvkJjJ/Cgor0TVfvJh+88OHTGwo3aOKDoI8xhubp3009fl+s9CAB
        cO05IAOE1QP6cjaZLa94zp6f6+sfUPW6LSAXsBqJYG0EEuPzyozZHd4bpu5bfyYWVBVKksTdG1n33vBB
        +J9M3/mrz9R9++9Y6WHZwAQeq9/26mvGL88MXkMBlncWv16odI+uDLALwokCrLEUC7j5pKnbJwmVzmHM
        xSkJ3F+VlZcWGh+KVm/46Nt/MBcTPYy7z0c4rE5ly9aXBSQ+iaSlD2qsJzEXpxagPLX23Gb9u8deZGA4
        1piLhcy2+dGsjluS6Lx8BXP7eXMlASlavVD7A6Sx0cMsDqdrs62hKaKeyZ8ZHBpl8r3H3Vy/vPuBAojO
        qa7MVq83qczOZ+WRC2QcnhvObPtFQsxyfF75LsyVaxOIVk9NrPQwy4lTp7uwu969+w6E1fcPnBtm8mz9
        7QU7uH5LUAGMjqmJjMPzkuiam1yVvjGJIhXROdNOH0nZ39uKObZbUAGi1YcWYCV6mMXd1z+hJDiZbTIF
        6fHM820vkYamZnX9wQVIyK+uEJ1z98TPbkrGlmk35mRxetOlas6RtMYJts56xRdUgGj1oQVYiR5mKXtz
        d8Xg0Mg9JomEA/pjJ/uq1eSV1lfXH1wAYBCqjh81OmaxG7NSuv2yI9U6Wmp03Hggj1tmllZbStk68oWp
        0SYQrT60ACvRw/iNN3z8yadH1WRPnfY4jnT3lKIoDzhGZywpra+u/5ECcPIFQ93FmbRD16WHXFtWf697
        u0ttHfUCoQWISh+mAP9ZD2MBZD12eUYtApJfVn9rWl/Wg6AC8DP4LFj7TO6uktTGqaVU3EBFXzumbR3C
        2DJNAvdxSJEv/iR6xlITKz2Mn0H5+5+/vaCEu60mTkJanzDWrC0AD0LPATqFpN1trtSGq5KM7eclLErb
        OoQX26pJYESo+b5K8T1WL9R8twear2Klh/EgpJ4jhHpbg0tNPkzrE8ZmawvAQxCDngd8g+Ykln7uSnrr
        SGfCDqsVY7V16GOh3gB6TQJFevv0HcF6sXr1SxUG+MLq6WOiiF2EZmes9DAehXnOZ2LszpzaunpXY/Oh
        zj1792nXTx9jeOpNCBTAYLui8/v9vR6Pp10UxUo4+U7gjhwA7yjjcvrcbnerz+froUZdAH9/4707/v6Q
        X8psuS7/cRHqfpSE2jEppf6nwJ8Z+qoHfNLXs3cuxVLPJBYXFzvGx8ftubm57Ga+EwpAEdimjC30jY2N
        2RYWFvhZlHVyAWi4UDIYBH+BSEbfCNArGpn/q/6h6XT/AlxjTq8+2Lu3AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="chkJudgeInstall.tbIconIndeterminate" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAAsMAAALDAE/QCLIAAACr0lE
        QVRYR+2XzWtTQRTF30ZEbLTNhwixrV90EytI0ZUbBRe1lEKrG8FNNCBYXNSFgdA/IIG6a1fZxUKxUlxJ
        G4RAutQiLlyGrEoMkXYhCK7Gc4YZnTEz5IW8ZS78oO/dc+bN3L43cxOoSIAZMAcWPDBHDbWBEEKSyh+Q
        22AT/ATCA3PUUBuZX8VpMAGugesemKOGWsufBFzcNKAg7YE5au6DlB4Ak7mbevXpOJ579/zk1J0ppe2C
        OWqg/QHPvaj8iBHAeV0CF4DTD5ijhoUYMfzBTZABcXAGxDwwRw21t/QAmMz22NO3OZXr6R/Lbj2BZycq
        P2ISXGQO9PQDFmHS8AfzgG9BLJ7bWUyu1FvJlX1hU28xR43SzusBMJnfJyZmzof1U0tPVH4E//v8LGOF
        QmGxWCy2SqWSMOE95qhR2mnDL79vWSX3wzX1ltbRowfAZDhKX356ovIj+H3L+67Fa5jTOnoMv1kA14P/
        oXX06AHsBbh9Gq3zF8Dt07j8iL8FcC3cROvoMfzDAgwLoG4OC9BjE/qudfToAewFhPP7C9C/HxF2EzTn
        bxWAx+BZwGNoiQ9yPPyQOWqU1joG0aTIYyiMn1p6ovIjeAzK8x9H3RIX6lj8IXPUKK11DLIRugJ0dXpx
        GViNUGJ57/F/Gi+J5d1H8FiN0CB+BBsh2UeEhFqrEWJjw/b2KhgFLhNhjoWaBXYrnP98lHjxMXvqxoNz
        StsFc1wotB147FZ4AD+CrTDbWy5MvskemKOGnazVCgfNZnOjUqmspdPph7jkntAFc+VyudhoNNZxLc0E
        kwk+fDuuPdtqivHVL64fMRLmsm8a4v3Xo31cR+ZndDqd17VaLZ/JZPg2c0/ogrlqtfqy3W6v4dry8484
        2AS/gC+Y2wYp5ZGoyfTlNxcwqH+w+YvgD0uwxJYL/0c2AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="chkJudgeInstall.tbIconUnChecked" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAACXBIWXMAAAr/AAAK/wE0YpqCAAACPElE
        QVRYR+2Xv0tbURTH3yKlaFtNTCmk/mqLS6pQxE5dLHSwIi66CC5phULFxaFZ/AM66KZTtihILaWTtKGQ
        wdGW0qFjyBwiOgiFTq/fz+M9eDe5Nyp5U8mBD+TmfL+X807ee/fECyMtpsScWHBADg1az/f9gEzhBzwT
        ++JC+A7IoUGbmD+MXjEsHotJB+TQoDX8g4KLmxAIsg7IoXkpMtEGKuZ55t3JeWr149sb4zPjobYFcmik
        PZXnRVJ+RZ+grjFxX1j9ghwaGtEX83vTIidS4ra45YAcGrRPow1UzOHA6w+rYe5S/0D+4JU8n5LyK0bE
        KDlxqV/QhJGY35sX3AU2kw2089EGKuZvz/DUvSaNE7R4kvIr+PV5LK16C2gnYv7g+bYJ27EQbaBi2MWm
        cYInKb+C59uqa8NkzN9tQLcB4ZfXoduAaINuA/6DBnAM3hE2oQ20xjGoIeXKxxBaPEn5FRyD7c7/ZtAa
        xyCD0ENhE9t4IIxBKL32daVJ4yS99mVZHmMQ6sSvYBC68hwh0BqDEIMN4+0j0S9sJiBHo2aFOQoXvp+l
        17/lbz5ZvBtqWyDHhUrbkMcchTvwKxiFGW+5sHZ3Mjk0TLLGKOzVarXdUqm0lc1ml7TkndACuWKx+L5a
        re5oHZhBxXhHv88rbw5q/tDmT9ufmABy+b2q//nX2bHWifmJRqOxXalUCrlcjruZd0IL5Mrl8ka9Xt/S
        2vDzISX2xR/hCnKHIhN6AsJiruWPX0Cn/s7q971/RQyElM1CWLMAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnRepair.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnLocalPackageFolder.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnOpenDownloadFolder.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnDownloadBatch.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnRefresh.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="menuUpdate.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="tipDgvCell.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>279, 17</value>
  </metadata>
  <metadata name="bgwGetDeviceApp.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>392, 17</value>
  </metadata>
  <metadata name="bgwWealth.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>554, 17</value>
  </metadata>
  <metadata name="bgwCheckAppInstall.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>673, 17</value>
  </metadata>
</root>