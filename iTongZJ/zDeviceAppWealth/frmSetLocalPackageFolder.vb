﻿Public Class frmSetLocalPackageFolder

    Private mFolderiApp As String = String.Empty

    Private mIsChage As Boolean = False

    Public Property IsChage() As Boolean
        Get
            Return mIsChage
        End Get
        Set(ByVal value As Boolean)
            mIsChage = value
        End Set
    End Property

#Region "--- 初始化 ---"

    Public Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FilletRadius = 5
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.mFolderiApp = IniSetting.GetZJLocalPackageFolder
        Me.txtDownloadFolder.Text = Me.mFolderiApp

    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnDownloadFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDownloadFolder.Click
        Dim strFolder As String = Me.txtDownloadFolder.Text.Trim
        Me.txtDownloadFolder.Text = Me.SelectFolder(strFolder)

        If Me.mFolderiApp <> Me.txtDownloadFolder.Text Then
            mIsChage = True
        End If

        If File.Exists(Me.txtDownloadFolder.Text) Then
            tbMessageBox.Show(Me, "输入的地址不存在！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1)
            Return
        End If

        IniSetting.SetZJLocalPackageFolder(Me.txtDownloadFolder.Text.Trim)

    End Sub

    Private Sub btnOpenFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOpenFolder.Click
        Common.OpenExplorer(Me.txtDownloadFolder.Text.Trim)
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If Me.mFolderiApp <> Me.txtDownloadFolder.Text Then
            mIsChage = True
        End If

        If File.Exists(Me.txtDownloadFolder.Text) Then
            tbMessageBox.Show(Me, "输入的地址不存在！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1)
            Return
        End If

        Me.mFolderiApp = Me.txtDownloadFolder.Text
        IniSetting.SetZJLocalPackageFolder(Me.mFolderiApp)
        Me.Close()

    End Sub

    Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
        MyBase.OnClosed(e)
        If Me.Owner IsNot Nothing AndAlso TypeOf Me.Owner Is MainForm Then
            Me.Owner.BringToFront()
        End If
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function SelectFolder(ByVal strFolder As String) As String
        Dim folder As String = strFolder
        Dim dialog As FolderBrowserDialog = New FolderBrowserDialog()

        If Directory.Exists(strFolder) Then
            dialog.SelectedPath = strFolder
        End If

        If dialog.ShowDialog() = Windows.Forms.DialogResult.OK Then
            folder = dialog.SelectedPath
        End If

        Return folder
    End Function

#End Region

End Class