﻿Imports System.Threading
Imports System.IO

Public Class StrongInstallHelper

    Private mTdCheckDownload As Thread
    Private miPadKey As String = "_HD"
    Private mDictInfo As New Dictionary(Of String, StrongInstallInfo)

#Region "--- 单实例 ---"

    Private Shared mInstance As StrongInstallHelper = Nothing
    Private Shared ReadOnly locker As New Object

    Public Shared Function Instance() As StrongInstallHelper
        If mInstance Is Nothing Then
            SyncLock locker
                If mInstance Is Nothing Then
                    mInstance = New StrongInstallHelper()
                End If
            End SyncLock
        End If

        Return mInstance
    End Function

#End Region

#Region "--- 下载强推软件 ---"

    '检测是否需要下载同步推
    Public Sub Start2CheckDownloadTBTui()
        If Me.mTdCheckDownload IsNot Nothing AndAlso Me.mTdCheckDownload.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTdCheckDownload = New Thread(AddressOf CheckDownloadSoft)
        Me.mTdCheckDownload.IsBackground = True
        Me.mTdCheckDownload.Start()
    End Sub

    Private Sub CheckDownloadSoft()
        Try
            ''同步推Store iPhone版
            Me.CheckDownloadSoft(False, False)

            ''同步推Store iPad版
            Me.CheckDownloadSoft(False, True)

            ''同步推越狱iPhone版
            Me.CheckDownloadSoft(True, False)

            ''同步推越狱iPad版
            Me.CheckDownloadSoft(True, True)
        Catch ex As Exception
        End Try
    End Sub


    'iPhone正版：http://zjds.api.tongbu.com/Api.ashx?GetiPhoneZbDownUrl
    Private mDownloadUrlForZJ As String = "http://zjds.api.tongbu.com/Api.ashx?GetiPhoneZbDownUrl"

    'iPhone破解版：http://zjds.api.tongbu.com/Api.ashx?GetiPhoneJbDownUrl
    Private mJailbreakDownloadUrlForZJ As String = "http://zjds.api.tongbu.com/Api.ashx?GetiPhoneJbDownUrl"

    'iPad正版：http://zjds.api.tongbu.com/Api.ashx?GetiPadZbDownUrl
    Private mIpadForZJ As String = "http://zjds.api.tongbu.com/Api.ashx?GetiPadZbDownUrl"

    'iPad破解版：http://zjds.api.tongbu.com/Api.ashx?GetiPadJbDownUrl
    Private mJailbreakIpadForZJ As String = "http://zjds.api.tongbu.com/Api.ashx?GetiPadJbDownUrl"

    ''' <summary>
    ''' 获取同步推下载地址，区分iPhone，iPad版本，区分是否越狱，总共4个版本
    ''' </summary>
    ''' <remarks></remarks>
    Private Sub CheckDownloadSoft(ByVal isJailbreak As Boolean, ByVal isPad As Boolean)
        Dim info As New StrongInstallInfo(isJailbreak, isPad)
        Dim strDiretory As String = Folder.CacheStrongInstallAuthorizedFolder
        If info.Jailbreak Then
            strDiretory = Folder.CacheStrongInstallFolder
        End If

        Dim strUrl As String = ""
        Try
            '获取同步推下载地址，（装机大师专用）。

            If info.Jailbreak Then
                strUrl = IIf(info.IsPad, mJailbreakIpadForZJ, mJailbreakDownloadUrlForZJ)
            Else
                strUrl = IIf(info.IsPad, mIpadForZJ, mDownloadUrlForZJ)
            End If
        Catch
        End Try

        Dim strContent As String = Utility.GetContentStringFromUrl(strUrl, Encoding.UTF8, 20000)

        If String.IsNullOrEmpty(strContent) Then
            Return
        End If

        Dim objJson As JsonObject = JsonParser.ParseString(strContent)

        Dim strCode As Integer = 0
        Dim strMessage As String = String.Empty
        Dim strJson As String = String.Empty

        Try
            If objJson.ContainsKey("Code") Then
                strCode = CType(objJson("Code"), JsonNumber).Value()
            End If

            If objJson.ContainsKey("Message") Then
                strMessage = CType(objJson("Message"), JsonString).Value()
            End If

            If strCode <> "0" Then
                Common.LogException(String.Format("访问{0}错误 Code:{1} Message:{2}", strUrl, strCode, strMessage))
                Return
            End If

            If objJson.ContainsKey("Data") = False Then
                Return
            End If

            strJson = CType(objJson("Data"), JsonString).ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckDownloadSoft")
        End Try


        '1.1.4@@com.tongbu.tbtui@@http://t.tongbu.com/tbtui/channel/tbtui_v1.14_1_zds.ipa
        If strJson.Length > 0 Then
            Dim aryContent As String() = strJson.Split("@@".ToCharArray(), StringSplitOptions.RemoveEmptyEntries)
            If aryContent IsNot Nothing AndAlso aryContent.Length > 0 Then
                info.Url = aryContent(2)
                info.Identifier = Utility.ReplaceWinIllegalName(aryContent(1).ToLower)
                info.Version = Utility.ReplaceWinIllegalName(aryContent(0))
            End If
        End If
        If info.Url.Length = 0 Then
            Return
        End If

        Dim strName As String = String.Format("{0}_v{1}.ipa", info.Identifier, info.Version)
        If info.IsPad Then
            strName = String.Format("{0}_v{1}_{2}.ipa", info.Identifier, info.Version, Me.miPadKey)
        End If
        Dim strPath As String = Path.Combine(strDiretory, strName)
        info.Path = strPath
        Me.mDictInfo(info.Path.ToLower) = info

        If File.Exists(strPath) Then
            IniSetting.SetStrongTuiPath(info)

            '清除旧版本的数据
            Me.ClearOldPackages(info)

            Return
        End If

        '查看是否有旧版本的同步推数据，在下载新数据的时候，可以安装旧的同步推
        strPath = Me.GetOldPackagePath(info)
        If strPath.Length > 0 Then
            info.Path = strPath
            Me.mDictInfo(info.Path.ToLower) = info
            IniSetting.SetStrongTuiPath(info)
        End If

        '下载新的地址
        AppleStoreHelper.DownloadCrakedSoft(info.Url, "", "", strName, info.Version, info.Identifier, strDiretory, info.Jailbreak, DeviceType.iOS, ResourceClass.Plugins, False)
    End Sub

    Private Sub ClearOldPackages(ByVal info As StrongInstallInfo)
        Try
            Dim strDiretory As String = Folder.CacheStrongInstallAuthorizedFolder
            If info.Jailbreak Then
                strDiretory = Folder.CacheStrongInstallFolder
            End If

            Folder.CheckFolder(strDiretory)
            Dim aryFile As String() = Directory.GetFiles(strDiretory, "*.ipa", SearchOption.TopDirectoryOnly)
            If aryFile Is Nothing OrElse aryFile.Length = 0 Then
                Return
            End If

            Dim strKey As String = info.Identifier & "_"

            For Each strFile As String In aryFile
                Dim strFileName As String = Path.GetFileName(strFile)
                If strFileName.StartsWith(strKey) AndAlso String.Compare(info.Path, strFile, True) <> 0 Then
                    Try
                        File.Delete(strFile)
                    Catch
                    End Try
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Function GetOldPackagePath(ByVal info As StrongInstallInfo) As String
        Dim strPath As String = String.Empty
        Dim strDiretory As String = Folder.CacheStrongInstallAuthorizedFolder
        If info.Jailbreak Then
            strDiretory = Folder.CacheStrongInstallFolder
        End If

        Try
            Folder.CheckFolder(strDiretory)
            Dim aryFile As String() = Directory.GetFiles(strDiretory, "*.ipa", SearchOption.TopDirectoryOnly)
            If aryFile Is Nothing OrElse aryFile.Length = 0 Then
                Return strPath
            End If

            Dim strKey As String = info.Identifier & "_"
            For Each strFile As String In aryFile
                Dim strFileName As String = Path.GetFileName(strFile)
                If strFileName.StartsWith(strKey) Then
                    strPath = strFile
                    Exit For
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

        Return strPath
    End Function

#End Region

#Region "--- 安装强推软件 ---"

    '检测是否安装同步推
    Public Sub CheckInstallTongbutui(ByVal device As iPhoneDevice)
        Dim isJailbreak As Boolean = False
        If device.Jailbreaked OrElse device.InstallCydia Then
            isJailbreak = True
        End If

        Dim isPad As Boolean = False
        If device.ProductType.ToLower.StartsWith("ipad") Then
            isPad = True
        End If

        Dim info As New StrongInstallInfo(isJailbreak, isPad)
        IniSetting.GetStrongTuiPath(info)

        '如果没有获取到同步推的下载路径，可能是没有启动，再启动下载同步推的操作。
        If info.Path.Length = 0 OrElse Not File.Exists(info.Path) Then
            Me.Start2CheckDownloadTBTui()
            Return
        End If

        '如果有信息，要对全局赋值，否则安装判断强装的逻辑可能会判断不到

        Me.mDictInfo(info.Path.ToLower) = info

        Dim state As PackageInstallState = iPhoneInstallHelper.GetInstance(device).CheckPackageInstalled(info.Identifier, info.Version, "")
        If state = PackageInstallState.Installed OrElse state = PackageInstallState.InstallHigher Then
            Return
        End If

        iPhoneInstallHelper.GetInstance(device).InstallApplication(info.Path, False, False)
    End Sub

    Public Sub CheckUpdateStrongTongbutuiPath(ByVal info As MultiThreadDownloadItemInfo)
        If Not CheckIsStrongInstall(info.FilePath) Then
            Return
        End If

        Dim isCracked As Boolean = True
        If info.ItemId = "-1" Then
            isCracked = False
        End If

        Dim isPad As Boolean = False
        If info.FilePath.Contains(miPadKey) Then
            isPad = True
        End If

        Dim sInfo As New StrongInstallInfo(isCracked, isPad)
        sInfo.Identifier = info.Identifier
        sInfo.Version = info.Version
        sInfo.Path = info.FilePath
        sInfo.Url = info.Url

        IniSetting.SetStrongTuiPath(sInfo)

        Try
            For Each device As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
                iPhoneInstallHelper.GetInstance(device).InstallApplication(info.FilePath, False, False)
            Next
        Catch
        End Try
    End Sub

    Public Function CheckIsStrongInstall(ByVal strPath As String) As Boolean
        Dim isContain As Boolean = False

        Try
            isContain = Me.mDictInfo.ContainsKey(strPath.ToLower)
            'If strPath.Contains("com.tongbu.tui") Then
            '    'If isContain Then
            '    '    Common.Log("同步推判断是强推软件")
            '    'Else
            '    '    Dim strDict As String = ""
            '    '    For Each Item As String In Me.mDictInfo.Keys
            '    '        strDict = strDict & "," & Item
            '    '    Next

            '    '    Common.Log(String.Format("同步推判断不是强推软件,{0}{1}", vbCrLf, strDict))
            '    'End If
            'End If

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

        Return isContain
    End Function

#End Region

End Class

