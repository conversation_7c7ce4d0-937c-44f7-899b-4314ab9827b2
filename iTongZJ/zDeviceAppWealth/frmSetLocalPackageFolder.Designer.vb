﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSetLocalPackageFolder
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.btnOpenFolder = New iTong.Components.tbButton
        Me.btnDownloadFolder = New iTong.Components.tbButton
        Me.txtDownloadFolder = New iTong.Components.tbTextBox
        Me.lblApp = New System.Windows.Forms.Label
        Me.btn_close = New iTong.Components.tbButton
        Me.SuspendLayout()
        '
        'btnOpenFolder
        '
        Me.btnOpenFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOpenFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnOpenFolder.BindingForm = Nothing
        Me.btnOpenFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOpenFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnOpenFolder.Location = New System.Drawing.Point(123, 134)
        Me.btnOpenFolder.Name = "btnOpenFolder"
        Me.btnOpenFolder.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOpenFolder.Selectable = True
        Me.btnOpenFolder.Size = New System.Drawing.Size(88, 26)
        Me.btnOpenFolder.TabIndex = 12
        Me.btnOpenFolder.tbAdriftIconWhenHover = False
        Me.btnOpenFolder.tbAutoSize = False
        Me.btnOpenFolder.tbAutoSizeEx = False
        Me.btnOpenFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnOpenFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOpenFolder.tbBadgeNumber = 0
        Me.btnOpenFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpenFolder.tbEndEllipsis = False
        Me.btnOpenFolder.tbIconHoldPlace = True
        Me.btnOpenFolder.tbIconImage = Nothing
        Me.btnOpenFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOpenFolder.tbIconMore = False
        Me.btnOpenFolder.tbIconMouseDown = Nothing
        Me.btnOpenFolder.tbIconMouseHover = Nothing
        Me.btnOpenFolder.tbIconMouseLeave = Nothing
        Me.btnOpenFolder.tbIconPlaceText = 2
        Me.btnOpenFolder.tbIconReadOnly = Nothing
        Me.btnOpenFolder.tbImageMouseDown = Nothing
        Me.btnOpenFolder.tbImageMouseHover = Nothing
        Me.btnOpenFolder.tbImageMouseLeave = Nothing
        Me.btnOpenFolder.tbReadOnly = False
        Me.btnOpenFolder.tbReadOnlyText = False
        Me.btnOpenFolder.tbShadow = False
        Me.btnOpenFolder.tbShadowColor = System.Drawing.Color.Black
        Me.btnOpenFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOpenFolder.tbShowDot = False
        Me.btnOpenFolder.tbShowNew = False
        Me.btnOpenFolder.tbShowToolTipOnButton = False
        Me.btnOpenFolder.tbSplit = "13,11,13,11"
        Me.btnOpenFolder.tbText = "打开文件夹"
        Me.btnOpenFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnOpenFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnOpenFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnOpenFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnOpenFolder.tbTextMouseDownPlace = 0
        Me.btnOpenFolder.tbToolTip = ""
        Me.btnOpenFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpenFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpenFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenFolder.VisibleEx = True
        '
        'btnDownloadFolder
        '
        Me.btnDownloadFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDownloadFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnDownloadFolder.BindingForm = Nothing
        Me.btnDownloadFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDownloadFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnDownloadFolder.Location = New System.Drawing.Point(25, 134)
        Me.btnDownloadFolder.Name = "btnDownloadFolder"
        Me.btnDownloadFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnDownloadFolder.Selectable = True
        Me.btnDownloadFolder.Size = New System.Drawing.Size(88, 26)
        Me.btnDownloadFolder.TabIndex = 61
        Me.btnDownloadFolder.tbAdriftIconWhenHover = False
        Me.btnDownloadFolder.tbAutoSize = False
        Me.btnDownloadFolder.tbAutoSizeEx = False
        Me.btnDownloadFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnDownloadFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDownloadFolder.tbBadgeNumber = 0
        Me.btnDownloadFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDownloadFolder.tbEndEllipsis = False
        Me.btnDownloadFolder.tbIconHoldPlace = True
        Me.btnDownloadFolder.tbIconImage = Nothing
        Me.btnDownloadFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDownloadFolder.tbIconMore = False
        Me.btnDownloadFolder.tbIconMouseDown = Nothing
        Me.btnDownloadFolder.tbIconMouseHover = Nothing
        Me.btnDownloadFolder.tbIconMouseLeave = Nothing
        Me.btnDownloadFolder.tbIconPlaceText = 2
        Me.btnDownloadFolder.tbIconReadOnly = Nothing
        Me.btnDownloadFolder.tbImageMouseDown = Nothing
        Me.btnDownloadFolder.tbImageMouseHover = Nothing
        Me.btnDownloadFolder.tbImageMouseLeave = Nothing
        Me.btnDownloadFolder.tbReadOnly = False
        Me.btnDownloadFolder.tbReadOnlyText = False
        Me.btnDownloadFolder.tbShadow = False
        Me.btnDownloadFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDownloadFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDownloadFolder.tbShowDot = False
        Me.btnDownloadFolder.tbShowNew = False
        Me.btnDownloadFolder.tbShowToolTipOnButton = False
        Me.btnDownloadFolder.tbSplit = "13,11,13,11"
        Me.btnDownloadFolder.tbText = "更换目录"
        Me.btnDownloadFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDownloadFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDownloadFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDownloadFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDownloadFolder.tbTextMouseDownPlace = 0
        Me.btnDownloadFolder.tbToolTip = ""
        Me.btnDownloadFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDownloadFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDownloadFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.VisibleEx = True
        '
        'txtDownloadFolder
        '
        Me.txtDownloadFolder.BackColor = System.Drawing.Color.White
        Me.txtDownloadFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtDownloadFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtDownloadFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtDownloadFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtDownloadFolder.ForeColor = System.Drawing.Color.Black
        Me.txtDownloadFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDownloadFolder.Location = New System.Drawing.Point(26, 91)
        Me.txtDownloadFolder.MaxLength = 256
        Me.txtDownloadFolder.Name = "txtDownloadFolder"
        Me.txtDownloadFolder.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtDownloadFolder.Size = New System.Drawing.Size(332, 30)
        Me.txtDownloadFolder.TabIndex = 62
        Me.txtDownloadFolder.Tag = Nothing
        Me.txtDownloadFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtDownloadFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtDownloadFolder.tbSelMark = True
        Me.txtDownloadFolder.tbTextBind = ""
        Me.txtDownloadFolder.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtDownloadFolder.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtDownloadFolder.TextImeMode = System.Windows.Forms.ImeMode.Disable
        Me.txtDownloadFolder.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtDownloadFolder.TextTip = ""
        '
        'lblApp
        '
        Me.lblApp.AutoSize = True
        Me.lblApp.BackColor = System.Drawing.Color.Transparent
        Me.lblApp.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblApp.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblApp.Location = New System.Drawing.Point(23, 60)
        Me.lblApp.Name = "lblApp"
        Me.lblApp.Size = New System.Drawing.Size(104, 16)
        Me.lblApp.TabIndex = 63
        Me.lblApp.Text = "应用目录路径"
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(354, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 64
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'frmSetLocalPackageFolder
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(380, 200)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.btnDownloadFolder)
        Me.Controls.Add(Me.txtDownloadFolder)
        Me.Controls.Add(Me.lblApp)
        Me.Controls.Add(Me.btnOpenFolder)
        Me.FilletRadius = 3
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New System.Drawing.Size(40, 140)
        Me.Name = "frmSetLocalPackageFolder"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbShowWatermark = True
        Me.tbSplit = "20,104,20,36"
        Me.tbTitleLocation = New System.Drawing.Point(4, 8)
        Me.Text = "本地软件目录设置"
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnOpenFolder As iTong.Components.tbButton
    Friend WithEvents btnDownloadFolder As iTong.Components.tbButton
    Friend WithEvents txtDownloadFolder As iTong.Components.tbTextBox
    Friend WithEvents lblApp As System.Windows.Forms.Label
    Friend WithEvents btn_close As iTong.Components.tbButton
End Class
