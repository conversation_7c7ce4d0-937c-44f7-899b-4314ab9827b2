﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmUpdateWealth
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmUpdateWealth))
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.tpnlContent = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlState = New iTong.Components.tbPanel()
        Me.lblAGSDowmload = New System.Windows.Forms.LinkLabel()
        Me.lblPersonSignatureProgress = New System.Windows.Forms.Label()
        Me.btnCourse = New iTong.Components.tbButton()
        Me.lblState = New System.Windows.Forms.Label()
        Me.pnlNoApp = New iTong.Components.tbPanel()
        Me.llblLogin = New System.Windows.Forms.LinkLabel()
        Me.pbImportFolder = New System.Windows.Forms.PictureBox()
        Me.lblDescription = New iTong.Components.tbLabel()
        Me.lblTitle = New iTong.Components.tbLabel()
        Me.btnSplit = New iTong.Components.tbButton()
        Me.pnlLoading = New System.Windows.Forms.Panel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.btnTest = New System.Windows.Forms.Button()
        Me.chkScore = New iTong.Components.tbCheckBox()
        Me.chkJudgeInstall = New iTong.Components.tbCheckBox()
        Me.btnRepair = New iTong.Components.tbButton()
        Me.btnLocalPackageFolder = New iTong.Components.tbButton()
        Me.btnOpenDownloadFolder = New iTong.Components.tbButton()
        Me.btnDownloadBatch = New iTong.Components.tbButton()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.menuUpdate = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiUpdate = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiGoToiTunes = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiIgnore = New System.Windows.Forms.ToolStripMenuItem()
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.bgwGetDeviceApp = New System.ComponentModel.BackgroundWorker()
        Me.bgwWealth = New System.ComponentModel.BackgroundWorker()
        Me.bgwCheckAppInstall = New System.ComponentModel.BackgroundWorker()
        Me.tpnlContent.SuspendLayout()
        Me.pnlState.SuspendLayout()
        Me.pnlNoApp.SuspendLayout()
        CType(Me.pbImportFolder, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTop.SuspendLayout()
        Me.menuUpdate.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.btn_close.Location = New System.Drawing.Point(1421, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.btn_normal.Location = New System.Drawing.Point(1397, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Font = New System.Drawing.Font("Microsoft Sans Serif", 9.0!)
        Me.btn_minimize.Location = New System.Drawing.Point(1373, 0)
        '
        'pnlContainer
        '
        Me.pnlContainer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContainer.Location = New System.Drawing.Point(0, 32)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(800, 538)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 2
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'tpnlContent
        '
        Me.tpnlContent.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.tpnlContent.ColumnCount = 3
        Me.tpnlContent.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 800.0!))
        Me.tpnlContent.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.tpnlContent.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlContent.Controls.Add(Me.pnlState, 0, 2)
        Me.tpnlContent.Controls.Add(Me.pnlNoApp, 2, 0)
        Me.tpnlContent.Controls.Add(Me.pnlLoading, 1, 0)
        Me.tpnlContent.Controls.Add(Me.pnlTop, 0, 0)
        Me.tpnlContent.Controls.Add(Me.pnlContainer, 0, 1)
        Me.tpnlContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tpnlContent.Location = New System.Drawing.Point(0, 0)
        Me.tpnlContent.Name = "tpnlContent"
        Me.tpnlContent.RowCount = 3
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32.0!))
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.tpnlContent.Size = New System.Drawing.Size(1445, 600)
        Me.tpnlContent.TabIndex = 0
        '
        'pnlState
        '
        Me.pnlState.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(131, Byte), Integer))
        Me.pnlState.Controls.Add(Me.lblAGSDowmload)
        Me.pnlState.Controls.Add(Me.lblPersonSignatureProgress)
        Me.pnlState.Controls.Add(Me.btnCourse)
        Me.pnlState.Controls.Add(Me.lblState)
        Me.pnlState.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlState.Location = New System.Drawing.Point(0, 570)
        Me.pnlState.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlState.Name = "pnlState"
        Me.pnlState.Size = New System.Drawing.Size(800, 30)
        Me.pnlState.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlState.TabIndex = 10
        Me.pnlState.tbBackgroundImage = Nothing
        Me.pnlState.tbShowWatermark = False
        Me.pnlState.tbSplit = "0,0,0,0"
        Me.pnlState.tbWatermark = Nothing
        Me.pnlState.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlState.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblAGSDowmload
        '
        Me.lblAGSDowmload.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(210, Byte), Integer))
        Me.lblAGSDowmload.AutoSize = True
        Me.lblAGSDowmload.DisabledLinkColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(210, Byte), Integer))
        Me.lblAGSDowmload.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblAGSDowmload.LinkColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(210, Byte), Integer))
        Me.lblAGSDowmload.Location = New System.Drawing.Point(59, 7)
        Me.lblAGSDowmload.Name = "lblAGSDowmload"
        Me.lblAGSDowmload.Size = New System.Drawing.Size(29, 12)
        Me.lblAGSDowmload.TabIndex = 25
        Me.lblAGSDowmload.TabStop = True
        Me.lblAGSDowmload.Text = "重试"
        Me.lblAGSDowmload.Visible = False
        Me.lblAGSDowmload.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(210, Byte), Integer))
        '
        'lblPersonSignatureProgress
        '
        Me.lblPersonSignatureProgress.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblPersonSignatureProgress.AutoSize = True
        Me.lblPersonSignatureProgress.Location = New System.Drawing.Point(12, 7)
        Me.lblPersonSignatureProgress.Name = "lblPersonSignatureProgress"
        Me.lblPersonSignatureProgress.Size = New System.Drawing.Size(0, 12)
        Me.lblPersonSignatureProgress.TabIndex = 24
        Me.lblPersonSignatureProgress.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnCourse
        '
        Me.btnCourse.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCourse.BackColor = System.Drawing.Color.Transparent
        Me.btnCourse.BindingForm = Nothing
        Me.btnCourse.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCourse.Location = New System.Drawing.Point(707, 3)
        Me.btnCourse.Name = "btnCourse"
        Me.btnCourse.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCourse.Selectable = True
        Me.btnCourse.Size = New System.Drawing.Size(67, 25)
        Me.btnCourse.TabIndex = 23
        Me.btnCourse.tbAdriftIconWhenHover = False
        Me.btnCourse.tbAutoSize = False
        Me.btnCourse.tbAutoSizeEx = True
        Me.btnCourse.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnCourse.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCourse.tbBadgeNumber = 0
        Me.btnCourse.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCourse.tbEndEllipsis = False
        Me.btnCourse.tbIconHoldPlace = True
        Me.btnCourse.tbIconImage = Nothing
        Me.btnCourse.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCourse.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnCourse.tbIconMore = False
        Me.btnCourse.tbIconMouseDown = Nothing
        Me.btnCourse.tbIconMouseHover = Nothing
        Me.btnCourse.tbIconMouseLeave = Nothing
        Me.btnCourse.tbIconPlaceText = 2
        Me.btnCourse.tbIconReadOnly = Nothing
        Me.btnCourse.tbImageMouseDown = Nothing
        Me.btnCourse.tbImageMouseHover = Nothing
        Me.btnCourse.tbImageMouseLeave = Nothing
        Me.btnCourse.tbProgressValue = 50
        Me.btnCourse.tbReadOnly = False
        Me.btnCourse.tbReadOnlyText = False
        Me.btnCourse.tbShadow = False
        Me.btnCourse.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCourse.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCourse.tbShowDot = False
        Me.btnCourse.tbShowMoreIconImg = CType(resources.GetObject("btnCourse.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCourse.tbShowNew = False
        Me.btnCourse.tbShowProgress = False
        Me.btnCourse.tbShowTip = True
        Me.btnCourse.tbShowToolTipOnButton = False
        Me.btnCourse.tbSplit = "13,11,13,11"
        Me.btnCourse.tbText = "查看教程"
        Me.btnCourse.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCourse.tbTextColor = System.Drawing.Color.White
        Me.btnCourse.tbTextColorDisable = System.Drawing.Color.White
        Me.btnCourse.tbTextColorDown = System.Drawing.Color.White
        Me.btnCourse.tbTextColorHover = System.Drawing.Color.White
        Me.btnCourse.tbTextMouseDownPlace = 2
        Me.btnCourse.tbToolTip = ""
        Me.btnCourse.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCourse.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCourse.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCourse.VisibleEx = True
        '
        'lblState
        '
        Me.lblState.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(393, 5)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(308, 21)
        Me.lblState.TabIndex = 0
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'pnlNoApp
        '
        Me.pnlNoApp.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlNoApp.Controls.Add(Me.llblLogin)
        Me.pnlNoApp.Controls.Add(Me.pbImportFolder)
        Me.pnlNoApp.Controls.Add(Me.lblDescription)
        Me.pnlNoApp.Controls.Add(Me.lblTitle)
        Me.pnlNoApp.Controls.Add(Me.btnSplit)
        Me.pnlNoApp.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlNoApp.Location = New System.Drawing.Point(1200, 0)
        Me.pnlNoApp.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlNoApp.Name = "pnlNoApp"
        Me.tpnlContent.SetRowSpan(Me.pnlNoApp, 3)
        Me.pnlNoApp.Size = New System.Drawing.Size(245, 600)
        Me.pnlNoApp.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlNoApp.TabIndex = 8
        Me.pnlNoApp.tbBackgroundImage = Nothing
        Me.pnlNoApp.tbShowWatermark = False
        Me.pnlNoApp.tbSplit = "0,0,0,0"
        Me.pnlNoApp.tbWatermark = Nothing
        Me.pnlNoApp.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlNoApp.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'llblLogin
        '
        Me.llblLogin.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.llblLogin.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.llblLogin.Location = New System.Drawing.Point(21, 334)
        Me.llblLogin.Name = "llblLogin"
        Me.llblLogin.Size = New System.Drawing.Size(165, 34)
        Me.llblLogin.TabIndex = 8
        Me.llblLogin.TabStop = True
        Me.llblLogin.Text = "点我登录"
        Me.llblLogin.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pbImportFolder
        '
        Me.pbImportFolder.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.pbImportFolder.Image = Global.iTong.My.Resources.Resources.pic_login
        Me.pbImportFolder.Location = New System.Drawing.Point(55, 236)
        Me.pbImportFolder.Name = "pbImportFolder"
        Me.pbImportFolder.Size = New System.Drawing.Size(96, 96)
        Me.pbImportFolder.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.pbImportFolder.TabIndex = 7
        Me.pbImportFolder.TabStop = False
        '
        'lblDescription
        '
        Me.lblDescription.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblDescription.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(127, Byte), Integer), CType(CType(160, Byte), Integer), CType(CType(193, Byte), Integer))
        Me.lblDescription.Location = New System.Drawing.Point(44, 104)
        Me.lblDescription.Name = "lblDescription"
        Me.lblDescription.Size = New System.Drawing.Size(180, 23)
        Me.lblDescription.TabIndex = 5
        Me.lblDescription.tbAdriftWhenHover = False
        Me.lblDescription.tbAutoEllipsis = False
        Me.lblDescription.tbAutoSize = False
        Me.lblDescription.tbHideImage = False
        Me.lblDescription.tbIconImage = Nothing
        Me.lblDescription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDescription.tbIconPlaceText = 5
        Me.lblDescription.tbShadow = False
        Me.lblDescription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDescription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDescription.tbShowScrolling = False
        Me.lblDescription.Text = "登录后即可获取到推广软件"
        '
        'lblTitle
        '
        Me.lblTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 21.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(101, Byte), Integer), CType(CType(122, Byte), Integer), CType(CType(149, Byte), Integer))
        Me.lblTitle.Location = New System.Drawing.Point(44, 43)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(151, 46)
        Me.lblTitle.TabIndex = 4
        Me.lblTitle.tbAdriftWhenHover = False
        Me.lblTitle.tbAutoEllipsis = False
        Me.lblTitle.tbAutoSize = False
        Me.lblTitle.tbHideImage = False
        Me.lblTitle.tbIconImage = Nothing
        Me.lblTitle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTitle.tbIconPlaceText = 5
        Me.lblTitle.tbShadow = False
        Me.lblTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTitle.tbShowScrolling = False
        Me.lblTitle.Text = "推广应用中没有软件"
        '
        'btnSplit
        '
        Me.btnSplit.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSplit.BackColor = System.Drawing.Color.Transparent
        Me.btnSplit.BindingForm = Nothing
        Me.btnSplit.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSplit.Location = New System.Drawing.Point(12, 149)
        Me.btnSplit.Name = "btnSplit"
        Me.btnSplit.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSplit.Selectable = True
        Me.btnSplit.Size = New System.Drawing.Size(220, 2)
        Me.btnSplit.TabIndex = 3
        Me.btnSplit.tbAdriftIconWhenHover = False
        Me.btnSplit.tbAutoSize = False
        Me.btnSplit.tbAutoSizeEx = False
        Me.btnSplit.tbBackgroundImage = Global.iTong.My.Resources.Resources.welcome_btn_split
        Me.btnSplit.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSplit.tbBadgeNumber = 0
        Me.btnSplit.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSplit.tbEndEllipsis = False
        Me.btnSplit.tbIconHoldPlace = True
        Me.btnSplit.tbIconImage = Nothing
        Me.btnSplit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSplit.tbIconMore = False
        Me.btnSplit.tbIconMouseDown = Nothing
        Me.btnSplit.tbIconMouseHover = Nothing
        Me.btnSplit.tbIconMouseLeave = Nothing
        Me.btnSplit.tbIconPlaceText = 2
        Me.btnSplit.tbIconReadOnly = Nothing
        Me.btnSplit.tbImageMouseDown = Nothing
        Me.btnSplit.tbImageMouseHover = Nothing
        Me.btnSplit.tbImageMouseLeave = Nothing
        Me.btnSplit.tbProgressValue = 50
        Me.btnSplit.tbReadOnly = False
        Me.btnSplit.tbReadOnlyText = False
        Me.btnSplit.tbShadow = False
        Me.btnSplit.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSplit.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSplit.tbShowDot = False
        Me.btnSplit.tbShowMoreIconImg = CType(resources.GetObject("btnSplit.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSplit.tbShowNew = False
        Me.btnSplit.tbShowProgress = False
        Me.btnSplit.tbShowTip = True
        Me.btnSplit.tbShowToolTipOnButton = False
        Me.btnSplit.tbSplit = "35,0,35,0"
        Me.btnSplit.tbText = ""
        Me.btnSplit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.tbTextColor = System.Drawing.Color.White
        Me.btnSplit.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSplit.tbTextColorDown = System.Drawing.Color.White
        Me.btnSplit.tbTextColorHover = System.Drawing.Color.White
        Me.btnSplit.tbTextMouseDownPlace = 0
        Me.btnSplit.tbToolTip = ""
        Me.btnSplit.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSplit.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSplit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.VisibleEx = True
        '
        'pnlLoading
        '
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlLoading.Location = New System.Drawing.Point(800, 0)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.tpnlContent.SetRowSpan(Me.pnlLoading, 3)
        Me.pnlLoading.Size = New System.Drawing.Size(400, 600)
        Me.pnlLoading.TabIndex = 7
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(83, 278)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(267, 45)
        Me.lblLoading.TabIndex = 10
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbLoading.Location = New System.Drawing.Point(51, 289)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(26, 26)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 0
        Me.pbLoading.TabStop = False
        '
        'pnlTop
        '
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.btnTest)
        Me.pnlTop.Controls.Add(Me.chkScore)
        Me.pnlTop.Controls.Add(Me.chkJudgeInstall)
        Me.pnlTop.Controls.Add(Me.btnRepair)
        Me.pnlTop.Controls.Add(Me.btnLocalPackageFolder)
        Me.pnlTop.Controls.Add(Me.btnOpenDownloadFolder)
        Me.pnlTop.Controls.Add(Me.btnDownloadBatch)
        Me.pnlTop.Controls.Add(Me.btnRefresh)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTop.Location = New System.Drawing.Point(0, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(800, 32)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 1
        Me.pnlTop.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnTest
        '
        Me.btnTest.Location = New System.Drawing.Point(303, 4)
        Me.btnTest.Name = "btnTest"
        Me.btnTest.Size = New System.Drawing.Size(75, 23)
        Me.btnTest.TabIndex = 22
        Me.btnTest.Text = "测试"
        Me.btnTest.UseVisualStyleBackColor = True
        Me.btnTest.Visible = False
        '
        'chkScore
        '
        Me.chkScore.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chkScore.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkScore.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold)
        Me.chkScore.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkScore.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkScore.Location = New System.Drawing.Point(384, 5)
        Me.chkScore.Name = "chkScore"
        Me.chkScore.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkScore.Size = New System.Drawing.Size(85, 22)
        Me.chkScore.TabIndex = 21
        Me.chkScore.tbAdriftIconWhenHover = False
        Me.chkScore.tbAutoSize = False
        Me.chkScore.tbAutoSizeEx = False
        Me.chkScore.tbIconChecked = CType(resources.GetObject("chkScore.tbIconChecked"), System.Drawing.Image)
        Me.chkScore.tbIconCheckedMouseDown = Nothing
        Me.chkScore.tbIconCheckedMouseHover = Nothing
        Me.chkScore.tbIconCheckedMouseLeave = Nothing
        Me.chkScore.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkScore.tbIconHoldPlace = True
        Me.chkScore.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkScore.tbIconIndeterminate = CType(resources.GetObject("chkScore.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkScore.tbIconIndeterminateMouseDown = Nothing
        Me.chkScore.tbIconIndeterminateMouseHover = Nothing
        Me.chkScore.tbIconIndeterminateMouseLeave = Nothing
        Me.chkScore.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkScore.tbIconPlaceText = 1
        Me.chkScore.tbIconUnChecked = CType(resources.GetObject("chkScore.tbIconUnChecked"), System.Drawing.Image)
        Me.chkScore.tbIconUnCheckedMouseDown = Nothing
        Me.chkScore.tbIconUnCheckedMouseHover = Nothing
        Me.chkScore.tbIconUnCheckedMouseLeave = Nothing
        Me.chkScore.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkScore.tbImageBackground = Nothing
        Me.chkScore.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkScore.tbImageCheckedMouseDown = Nothing
        Me.chkScore.tbImageCheckedMouseHover = Nothing
        Me.chkScore.tbImageCheckedMouseLeave = Nothing
        Me.chkScore.tbImageUnCheckedMouseDown = Nothing
        Me.chkScore.tbImageUnCheckedMouseHover = Nothing
        Me.chkScore.tbImageUnCheckedMouseLeave = Nothing
        Me.chkScore.tbReadOnly = False
        Me.chkScore.tbShadow = False
        Me.chkScore.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkScore.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkScore.tbSplit = "3,3,3,3"
        Me.chkScore.tbToolTip = ""
        Me.chkScore.Text = "显示积分"
        Me.chkScore.UseVisualStyleBackColor = True
        '
        'chkJudgeInstall
        '
        Me.chkJudgeInstall.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.chkJudgeInstall.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkJudgeInstall.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold)
        Me.chkJudgeInstall.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkJudgeInstall.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkJudgeInstall.Location = New System.Drawing.Point(474, 5)
        Me.chkJudgeInstall.Name = "chkJudgeInstall"
        Me.chkJudgeInstall.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkJudgeInstall.Size = New System.Drawing.Size(190, 22)
        Me.chkJudgeInstall.TabIndex = 20
        Me.chkJudgeInstall.tbAdriftIconWhenHover = False
        Me.chkJudgeInstall.tbAutoSize = False
        Me.chkJudgeInstall.tbAutoSizeEx = False
        Me.chkJudgeInstall.tbIconChecked = CType(resources.GetObject("chkJudgeInstall.tbIconChecked"), System.Drawing.Image)
        Me.chkJudgeInstall.tbIconCheckedMouseDown = Nothing
        Me.chkJudgeInstall.tbIconCheckedMouseHover = Nothing
        Me.chkJudgeInstall.tbIconCheckedMouseLeave = Nothing
        Me.chkJudgeInstall.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkJudgeInstall.tbIconHoldPlace = True
        Me.chkJudgeInstall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkJudgeInstall.tbIconIndeterminate = CType(resources.GetObject("chkJudgeInstall.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkJudgeInstall.tbIconIndeterminateMouseDown = Nothing
        Me.chkJudgeInstall.tbIconIndeterminateMouseHover = Nothing
        Me.chkJudgeInstall.tbIconIndeterminateMouseLeave = Nothing
        Me.chkJudgeInstall.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkJudgeInstall.tbIconPlaceText = 1
        Me.chkJudgeInstall.tbIconUnChecked = CType(resources.GetObject("chkJudgeInstall.tbIconUnChecked"), System.Drawing.Image)
        Me.chkJudgeInstall.tbIconUnCheckedMouseDown = Nothing
        Me.chkJudgeInstall.tbIconUnCheckedMouseHover = Nothing
        Me.chkJudgeInstall.tbIconUnCheckedMouseLeave = Nothing
        Me.chkJudgeInstall.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkJudgeInstall.tbImageBackground = Nothing
        Me.chkJudgeInstall.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkJudgeInstall.tbImageCheckedMouseDown = Nothing
        Me.chkJudgeInstall.tbImageCheckedMouseHover = Nothing
        Me.chkJudgeInstall.tbImageCheckedMouseLeave = Nothing
        Me.chkJudgeInstall.tbImageUnCheckedMouseDown = Nothing
        Me.chkJudgeInstall.tbImageUnCheckedMouseHover = Nothing
        Me.chkJudgeInstall.tbImageUnCheckedMouseLeave = Nothing
        Me.chkJudgeInstall.tbReadOnly = False
        Me.chkJudgeInstall.tbShadow = False
        Me.chkJudgeInstall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkJudgeInstall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkJudgeInstall.tbSplit = "3,3,3,3"
        Me.chkJudgeInstall.tbToolTip = ""
        Me.chkJudgeInstall.Text = "安装前预先判断是否安装过"
        Me.tipDgvCell.SetToolTip(Me.chkJudgeInstall, "如果选中，软件安装前会预先判断之前是否安装过；" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "如果不选中，软件安装后激活的时候会判断是否安装过；")
        Me.chkJudgeInstall.UseVisualStyleBackColor = True
        '
        'btnRepair
        '
        Me.btnRepair.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRepair.BackColor = System.Drawing.Color.Transparent
        Me.btnRepair.BindingForm = Nothing
        Me.btnRepair.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRepair.Location = New System.Drawing.Point(676, 5)
        Me.btnRepair.Name = "btnRepair"
        Me.btnRepair.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRepair.Selectable = True
        Me.btnRepair.Size = New System.Drawing.Size(85, 23)
        Me.btnRepair.TabIndex = 18
        Me.btnRepair.tbAdriftIconWhenHover = False
        Me.btnRepair.tbAutoSize = False
        Me.btnRepair.tbAutoSizeEx = False
        Me.btnRepair.tbBackgroundImage = Nothing
        Me.btnRepair.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnRepair.tbBadgeNumber = 0
        Me.btnRepair.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRepair.tbEndEllipsis = False
        Me.btnRepair.tbIconHoldPlace = True
        Me.btnRepair.tbIconImage = Nothing
        Me.btnRepair.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRepair.tbIconMore = False
        Me.btnRepair.tbIconMouseDown = Nothing
        Me.btnRepair.tbIconMouseHover = Nothing
        Me.btnRepair.tbIconMouseLeave = Nothing
        Me.btnRepair.tbIconPlaceText = 2
        Me.btnRepair.tbIconReadOnly = Nothing
        Me.btnRepair.tbImageMouseDown = Nothing
        Me.btnRepair.tbImageMouseHover = Nothing
        Me.btnRepair.tbImageMouseLeave = Nothing
        Me.btnRepair.tbProgressValue = 50
        Me.btnRepair.tbReadOnly = False
        Me.btnRepair.tbReadOnlyText = False
        Me.btnRepair.tbShadow = False
        Me.btnRepair.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRepair.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRepair.tbShowDot = False
        Me.btnRepair.tbShowMoreIconImg = CType(resources.GetObject("btnRepair.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRepair.tbShowNew = False
        Me.btnRepair.tbShowProgress = False
        Me.btnRepair.tbShowTip = True
        Me.btnRepair.tbShowToolTipOnButton = False
        Me.btnRepair.tbSplit = "3,3,3,3"
        Me.btnRepair.tbText = "修复授权"
        Me.btnRepair.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRepair.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRepair.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepair.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepair.tbTextMouseDownPlace = 2
        Me.btnRepair.tbToolTip = ""
        Me.btnRepair.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRepair.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRepair.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.VisibleEx = True
        '
        'btnLocalPackageFolder
        '
        Me.btnLocalPackageFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnLocalPackageFolder.BindingForm = Nothing
        Me.btnLocalPackageFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnLocalPackageFolder.Location = New System.Drawing.Point(189, 5)
        Me.btnLocalPackageFolder.Name = "btnLocalPackageFolder"
        Me.btnLocalPackageFolder.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnLocalPackageFolder.Selectable = True
        Me.btnLocalPackageFolder.Size = New System.Drawing.Size(94, 23)
        Me.btnLocalPackageFolder.TabIndex = 17
        Me.btnLocalPackageFolder.tbAdriftIconWhenHover = False
        Me.btnLocalPackageFolder.tbAutoSize = False
        Me.btnLocalPackageFolder.tbAutoSizeEx = False
        Me.btnLocalPackageFolder.tbBackgroundImage = Nothing
        Me.btnLocalPackageFolder.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnLocalPackageFolder.tbBadgeNumber = 0
        Me.btnLocalPackageFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnLocalPackageFolder.tbEndEllipsis = False
        Me.btnLocalPackageFolder.tbIconHoldPlace = True
        Me.btnLocalPackageFolder.tbIconImage = Nothing
        Me.btnLocalPackageFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLocalPackageFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnLocalPackageFolder.tbIconMore = False
        Me.btnLocalPackageFolder.tbIconMouseDown = Nothing
        Me.btnLocalPackageFolder.tbIconMouseHover = Nothing
        Me.btnLocalPackageFolder.tbIconMouseLeave = Nothing
        Me.btnLocalPackageFolder.tbIconPlaceText = 2
        Me.btnLocalPackageFolder.tbIconReadOnly = Nothing
        Me.btnLocalPackageFolder.tbImageMouseDown = Nothing
        Me.btnLocalPackageFolder.tbImageMouseHover = Nothing
        Me.btnLocalPackageFolder.tbImageMouseLeave = Nothing
        Me.btnLocalPackageFolder.tbProgressValue = 50
        Me.btnLocalPackageFolder.tbReadOnly = False
        Me.btnLocalPackageFolder.tbReadOnlyText = False
        Me.btnLocalPackageFolder.tbShadow = False
        Me.btnLocalPackageFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnLocalPackageFolder.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnLocalPackageFolder.tbShowDot = False
        Me.btnLocalPackageFolder.tbShowMoreIconImg = CType(resources.GetObject("btnLocalPackageFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnLocalPackageFolder.tbShowNew = False
        Me.btnLocalPackageFolder.tbShowProgress = False
        Me.btnLocalPackageFolder.tbShowTip = True
        Me.btnLocalPackageFolder.tbShowToolTipOnButton = False
        Me.btnLocalPackageFolder.tbSplit = "3,3,3,3"
        Me.btnLocalPackageFolder.tbText = "本地软件目录"
        Me.btnLocalPackageFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLocalPackageFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnLocalPackageFolder.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnLocalPackageFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnLocalPackageFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnLocalPackageFolder.tbTextMouseDownPlace = 2
        Me.btnLocalPackageFolder.tbToolTip = ""
        Me.btnLocalPackageFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnLocalPackageFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnLocalPackageFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLocalPackageFolder.VisibleEx = True
        '
        'btnOpenDownloadFolder
        '
        Me.btnOpenDownloadFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnOpenDownloadFolder.BindingForm = Nothing
        Me.btnOpenDownloadFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOpenDownloadFolder.Location = New System.Drawing.Point(89, 5)
        Me.btnOpenDownloadFolder.Name = "btnOpenDownloadFolder"
        Me.btnOpenDownloadFolder.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOpenDownloadFolder.Selectable = True
        Me.btnOpenDownloadFolder.Size = New System.Drawing.Size(94, 23)
        Me.btnOpenDownloadFolder.TabIndex = 17
        Me.btnOpenDownloadFolder.tbAdriftIconWhenHover = False
        Me.btnOpenDownloadFolder.tbAutoSize = False
        Me.btnOpenDownloadFolder.tbAutoSizeEx = False
        Me.btnOpenDownloadFolder.tbBackgroundImage = Nothing
        Me.btnOpenDownloadFolder.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnOpenDownloadFolder.tbBadgeNumber = 0
        Me.btnOpenDownloadFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpenDownloadFolder.tbEndEllipsis = False
        Me.btnOpenDownloadFolder.tbIconHoldPlace = True
        Me.btnOpenDownloadFolder.tbIconImage = Nothing
        Me.btnOpenDownloadFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenDownloadFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOpenDownloadFolder.tbIconMore = False
        Me.btnOpenDownloadFolder.tbIconMouseDown = Nothing
        Me.btnOpenDownloadFolder.tbIconMouseHover = Nothing
        Me.btnOpenDownloadFolder.tbIconMouseLeave = Nothing
        Me.btnOpenDownloadFolder.tbIconPlaceText = 2
        Me.btnOpenDownloadFolder.tbIconReadOnly = Nothing
        Me.btnOpenDownloadFolder.tbImageMouseDown = Nothing
        Me.btnOpenDownloadFolder.tbImageMouseHover = Nothing
        Me.btnOpenDownloadFolder.tbImageMouseLeave = Nothing
        Me.btnOpenDownloadFolder.tbProgressValue = 50
        Me.btnOpenDownloadFolder.tbReadOnly = False
        Me.btnOpenDownloadFolder.tbReadOnlyText = False
        Me.btnOpenDownloadFolder.tbShadow = False
        Me.btnOpenDownloadFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOpenDownloadFolder.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOpenDownloadFolder.tbShowDot = False
        Me.btnOpenDownloadFolder.tbShowMoreIconImg = CType(resources.GetObject("btnOpenDownloadFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOpenDownloadFolder.tbShowNew = False
        Me.btnOpenDownloadFolder.tbShowProgress = False
        Me.btnOpenDownloadFolder.tbShowTip = True
        Me.btnOpenDownloadFolder.tbShowToolTipOnButton = False
        Me.btnOpenDownloadFolder.tbSplit = "3,3,3,3"
        Me.btnOpenDownloadFolder.tbText = "打开下载目录"
        Me.btnOpenDownloadFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenDownloadFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnOpenDownloadFolder.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnOpenDownloadFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnOpenDownloadFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnOpenDownloadFolder.tbTextMouseDownPlace = 2
        Me.btnOpenDownloadFolder.tbToolTip = ""
        Me.btnOpenDownloadFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpenDownloadFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpenDownloadFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenDownloadFolder.VisibleEx = True
        '
        'btnDownloadBatch
        '
        Me.btnDownloadBatch.BackColor = System.Drawing.Color.Transparent
        Me.btnDownloadBatch.BindingForm = Nothing
        Me.btnDownloadBatch.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDownloadBatch.Location = New System.Drawing.Point(7, 5)
        Me.btnDownloadBatch.Name = "btnDownloadBatch"
        Me.btnDownloadBatch.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDownloadBatch.Selectable = True
        Me.btnDownloadBatch.Size = New System.Drawing.Size(77, 23)
        Me.btnDownloadBatch.TabIndex = 17
        Me.btnDownloadBatch.tbAdriftIconWhenHover = False
        Me.btnDownloadBatch.tbAutoSize = False
        Me.btnDownloadBatch.tbAutoSizeEx = False
        Me.btnDownloadBatch.tbBackgroundImage = Nothing
        Me.btnDownloadBatch.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDownloadBatch.tbBadgeNumber = 0
        Me.btnDownloadBatch.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDownloadBatch.tbEndEllipsis = False
        Me.btnDownloadBatch.tbIconHoldPlace = True
        Me.btnDownloadBatch.tbIconImage = Nothing
        Me.btnDownloadBatch.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadBatch.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDownloadBatch.tbIconMore = False
        Me.btnDownloadBatch.tbIconMouseDown = Nothing
        Me.btnDownloadBatch.tbIconMouseHover = Nothing
        Me.btnDownloadBatch.tbIconMouseLeave = Nothing
        Me.btnDownloadBatch.tbIconPlaceText = 2
        Me.btnDownloadBatch.tbIconReadOnly = Nothing
        Me.btnDownloadBatch.tbImageMouseDown = Nothing
        Me.btnDownloadBatch.tbImageMouseHover = Nothing
        Me.btnDownloadBatch.tbImageMouseLeave = Nothing
        Me.btnDownloadBatch.tbProgressValue = 50
        Me.btnDownloadBatch.tbReadOnly = False
        Me.btnDownloadBatch.tbReadOnlyText = False
        Me.btnDownloadBatch.tbShadow = False
        Me.btnDownloadBatch.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDownloadBatch.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDownloadBatch.tbShowDot = False
        Me.btnDownloadBatch.tbShowMoreIconImg = CType(resources.GetObject("btnDownloadBatch.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDownloadBatch.tbShowNew = False
        Me.btnDownloadBatch.tbShowProgress = False
        Me.btnDownloadBatch.tbShowTip = True
        Me.btnDownloadBatch.tbShowToolTipOnButton = False
        Me.btnDownloadBatch.tbSplit = "3,3,3,3"
        Me.btnDownloadBatch.tbText = "批量安装"
        Me.btnDownloadBatch.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadBatch.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnDownloadBatch.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnDownloadBatch.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDownloadBatch.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDownloadBatch.tbTextMouseDownPlace = 2
        Me.btnDownloadBatch.tbToolTip = ""
        Me.btnDownloadBatch.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDownloadBatch.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDownloadBatch.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadBatch.VisibleEx = True
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(765, 5)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(25, 23)
        Me.btnRefresh.TabIndex = 12
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = ""
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 0
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'menuUpdate
        '
        Me.menuUpdate.AccessibleDescription = "161x92"
        Me.menuUpdate.DropShadowEnabled = False
        Me.menuUpdate.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.menuUpdate.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiUpdate, Me.tsmiGoToiTunes, Me.tsmiIgnore})
        Me.menuUpdate.Name = "menuLocalApp"
        Me.menuUpdate.Size = New System.Drawing.Size(161, 70)
        Me.menuUpdate.tbBackColor = System.Drawing.Color.White
        Me.menuUpdate.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuUpdate.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuUpdate.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiUpdate
        '
        Me.tsmiUpdate.Name = "tsmiUpdate"
        Me.tsmiUpdate.Size = New System.Drawing.Size(160, 22)
        Me.tsmiUpdate.Text = "#Update"
        Me.tsmiUpdate.Visible = False
        '
        'tsmiGoToiTunes
        '
        Me.tsmiGoToiTunes.Name = "tsmiGoToiTunes"
        Me.tsmiGoToiTunes.Size = New System.Drawing.Size(160, 22)
        Me.tsmiGoToiTunes.Text = "#View in iTunes"
        '
        'tsmiIgnore
        '
        Me.tsmiIgnore.Name = "tsmiIgnore"
        Me.tsmiIgnore.Size = New System.Drawing.Size(160, 22)
        Me.tsmiIgnore.Text = "#Ignore"
        '
        'bgwGetDeviceApp
        '
        Me.bgwGetDeviceApp.WorkerSupportsCancellation = True
        '
        'bgwWealth
        '
        Me.bgwWealth.WorkerSupportsCancellation = True
        '
        'bgwCheckAppInstall
        '
        Me.bgwCheckAppInstall.WorkerSupportsCancellation = True
        '
        'frmUpdateWealth
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(1445, 600)
        Me.Controls.Add(Me.tpnlContent)
        Me.Name = "frmUpdateWealth"
        Me.tbShowIconOnForm = False
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmUpdate"
        Me.Controls.SetChildIndex(Me.tpnlContent, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.tpnlContent.ResumeLayout(False)
        Me.pnlState.ResumeLayout(False)
        Me.pnlState.PerformLayout()
        Me.pnlNoApp.ResumeLayout(False)
        Me.pnlNoApp.PerformLayout()
        CType(Me.pbImportFolder, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTop.ResumeLayout(False)
        Me.menuUpdate.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlContainer As iTong.Components.tbPanel
    Friend WithEvents menuUpdate As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiUpdate As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiGoToiTunes As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiIgnore As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents tpnlContent As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents pnlTop As iTong.Components.tbPanel
    Friend WithEvents btnRefresh As iTong.Components.tbButton
    Friend WithEvents bgwGetDeviceApp As System.ComponentModel.BackgroundWorker
    Friend WithEvents bgwWealth As System.ComponentModel.BackgroundWorker
    Friend WithEvents btnDownloadBatch As iTong.Components.tbButton
    Friend WithEvents pnlNoApp As iTong.Components.tbPanel
    Friend WithEvents llblLogin As System.Windows.Forms.LinkLabel
    Friend WithEvents pbImportFolder As System.Windows.Forms.PictureBox
    Friend WithEvents lblDescription As iTong.Components.tbLabel
    Friend WithEvents lblTitle As iTong.Components.tbLabel
    Friend WithEvents btnSplit As iTong.Components.tbButton
    Friend WithEvents btnOpenDownloadFolder As iTong.Components.tbButton
    Friend WithEvents btnRepair As iTong.Components.tbButton
    Friend WithEvents pnlState As iTong.Components.tbPanel
    Friend WithEvents lblState As System.Windows.Forms.Label
    Friend WithEvents bgwCheckAppInstall As System.ComponentModel.BackgroundWorker
    Friend WithEvents chkJudgeInstall As iTong.Components.tbCheckBox
    Friend WithEvents btnCourse As iTong.Components.tbButton
    Friend WithEvents chkScore As iTong.Components.tbCheckBox
    Friend WithEvents btnLocalPackageFolder As iTong.Components.tbButton
    Friend WithEvents btnTest As System.Windows.Forms.Button
    Friend WithEvents lblPersonSignatureProgress As System.Windows.Forms.Label
    Friend WithEvents lblAGSDowmload As System.Windows.Forms.LinkLabel
End Class
