﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmActivationHelp
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmActivationHelp))
        Me.pnlMain = New System.Windows.Forms.Panel
        Me.wbsHelper = New System.Windows.Forms.WebBrowser
        Me.pnlState = New System.Windows.Forms.Panel
        Me.chkRemember = New iTong.Components.tbCheckBox
        Me.btnClose = New iTong.Components.tbButton
        Me.btn_Visible = New iTong.Components.tbButton
        Me.tpnlContainer = New System.Windows.Forms.TableLayoutPanel
        Me.pnlInfo = New System.Windows.Forms.Panel
        Me.picApp = New System.Windows.Forms.PictureBox
        Me.lblAppName = New System.Windows.Forms.Label
        Me.lblInfo1 = New System.Windows.Forms.Label
        Me.lblInfo = New System.Windows.Forms.Label
        Me.pnlMain.SuspendLayout()
        Me.pnlState.SuspendLayout()
        Me.tpnlContainer.SuspendLayout()
        Me.pnlInfo.SuspendLayout()
        CType(Me.picApp, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlMain.Controls.Add(Me.wbsHelper)
        Me.pnlMain.Location = New System.Drawing.Point(0, 0)
        Me.pnlMain.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(194, 426)
        Me.pnlMain.TabIndex = 22
        '
        'wbsHelper
        '
        Me.wbsHelper.AllowWebBrowserDrop = False
        Me.wbsHelper.IsWebBrowserContextMenuEnabled = False
        Me.wbsHelper.Location = New System.Drawing.Point(23, 59)
        Me.wbsHelper.Margin = New System.Windows.Forms.Padding(0)
        Me.wbsHelper.MinimumSize = New System.Drawing.Size(20, 20)
        Me.wbsHelper.Name = "wbsHelper"
        Me.wbsHelper.ScriptErrorsSuppressed = True
        Me.wbsHelper.Size = New System.Drawing.Size(139, 293)
        Me.wbsHelper.TabIndex = 1
        '
        'pnlState
        '
        Me.pnlState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlState.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.tpnlContainer.SetColumnSpan(Me.pnlState, 2)
        Me.pnlState.Controls.Add(Me.chkRemember)
        Me.pnlState.Controls.Add(Me.btnClose)
        Me.pnlState.Location = New System.Drawing.Point(0, 426)
        Me.pnlState.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlState.Name = "pnlState"
        Me.pnlState.Size = New System.Drawing.Size(594, 40)
        Me.pnlState.TabIndex = 24
        '
        'chkRemember
        '
        Me.chkRemember.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.chkRemember.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkRemember.BackColor = System.Drawing.Color.Transparent
        Me.chkRemember.Checked = True
        Me.chkRemember.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkRemember.ForeColor = System.Drawing.Color.Black
        Me.chkRemember.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.chkRemember.Location = New System.Drawing.Point(9, 6)
        Me.chkRemember.Name = "chkRemember"
        Me.chkRemember.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkRemember.Size = New System.Drawing.Size(80, 27)
        Me.chkRemember.TabIndex = 17
        Me.chkRemember.tbAdriftIconWhenHover = False
        Me.chkRemember.tbAutoSize = False
        Me.chkRemember.tbAutoSizeEx = True
        Me.chkRemember.tbIconChecked = CType(resources.GetObject("chkRemember.tbIconChecked"), System.Drawing.Image)
        Me.chkRemember.tbIconCheckedMouseDown = Nothing
        Me.chkRemember.tbIconCheckedMouseHover = Nothing
        Me.chkRemember.tbIconCheckedMouseLeave = Nothing
        Me.chkRemember.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkRemember.tbIconHoldPlace = True
        Me.chkRemember.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkRemember.tbIconIndeterminate = CType(resources.GetObject("chkRemember.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkRemember.tbIconIndeterminateMouseDown = Nothing
        Me.chkRemember.tbIconIndeterminateMouseHover = Nothing
        Me.chkRemember.tbIconIndeterminateMouseLeave = Nothing
        Me.chkRemember.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkRemember.tbIconPlaceText = 1
        Me.chkRemember.tbIconUnChecked = CType(resources.GetObject("chkRemember.tbIconUnChecked"), System.Drawing.Image)
        Me.chkRemember.tbIconUnCheckedMouseDown = Nothing
        Me.chkRemember.tbIconUnCheckedMouseHover = Nothing
        Me.chkRemember.tbIconUnCheckedMouseLeave = Nothing
        Me.chkRemember.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkRemember.tbImageBackground = Nothing
        Me.chkRemember.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkRemember.tbImageCheckedMouseDown = Nothing
        Me.chkRemember.tbImageCheckedMouseHover = Nothing
        Me.chkRemember.tbImageCheckedMouseLeave = Nothing
        Me.chkRemember.tbImageUnCheckedMouseDown = Nothing
        Me.chkRemember.tbImageUnCheckedMouseHover = Nothing
        Me.chkRemember.tbImageUnCheckedMouseLeave = Nothing
        Me.chkRemember.tbReadOnly = False
        Me.chkRemember.tbShadow = False
        Me.chkRemember.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkRemember.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkRemember.tbSplit = "3,3,3,3"
        Me.chkRemember.tbToolTip = ""
        Me.chkRemember.Text = "不再提醒"
        Me.chkRemember.UseVisualStyleBackColor = False
        '
        'btnClose
        '
        Me.btnClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClose.BackColor = System.Drawing.Color.Transparent
        Me.btnClose.BindingForm = Nothing
        Me.btnClose.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClose.Location = New System.Drawing.Point(509, 8)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnClose.Selectable = True
        Me.btnClose.Size = New System.Drawing.Size(65, 23)
        Me.btnClose.TabIndex = 16
        Me.btnClose.tbAdriftIconWhenHover = False
        Me.btnClose.tbAutoSize = False
        Me.btnClose.tbAutoSizeEx = False
        Me.btnClose.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnClose.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnClose.tbBadgeNumber = 0
        Me.btnClose.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClose.tbEndEllipsis = False
        Me.btnClose.tbIconHoldPlace = True
        Me.btnClose.tbIconImage = Nothing
        Me.btnClose.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClose.tbIconMore = False
        Me.btnClose.tbIconMouseDown = Nothing
        Me.btnClose.tbIconMouseHover = Nothing
        Me.btnClose.tbIconMouseLeave = Nothing
        Me.btnClose.tbIconPlaceText = 2
        Me.btnClose.tbIconReadOnly = Nothing
        Me.btnClose.tbImageMouseDown = Nothing
        Me.btnClose.tbImageMouseHover = Nothing
        Me.btnClose.tbImageMouseLeave = Nothing
        Me.btnClose.tbReadOnly = False
        Me.btnClose.tbReadOnlyText = False
        Me.btnClose.tbShadow = False
        Me.btnClose.tbShadowColor = System.Drawing.Color.Black
        Me.btnClose.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnClose.tbShowNew = False
        Me.btnClose.tbShowToolTipOnButton = False
        Me.btnClose.tbSplit = "13,11,13,11"
        Me.btnClose.tbText = "关闭"
        Me.btnClose.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbTextColor = System.Drawing.Color.Black
        Me.btnClose.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnClose.tbTextColorDown = System.Drawing.Color.Black
        Me.btnClose.tbTextColorHover = System.Drawing.Color.Black
        Me.btnClose.tbTextMouseDownPlace = 0
        Me.btnClose.tbToolTip = ""
        Me.btnClose.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClose.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClose.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.VisibleEx = True
        '
        'btn_Visible
        '
        Me.btn_Visible.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_Visible.BackColor = System.Drawing.Color.Transparent
        Me.btn_Visible.BindingForm = Nothing
        Me.btn_Visible.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_Visible.Location = New System.Drawing.Point(573, 3)
        Me.btn_Visible.Name = "btn_Visible"
        Me.btn_Visible.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_Visible.Selectable = True
        Me.btn_Visible.Size = New System.Drawing.Size(24, 24)
        Me.btn_Visible.TabIndex = 25
        Me.btn_Visible.tbAdriftIconWhenHover = False
        Me.btn_Visible.tbAutoSize = False
        Me.btn_Visible.tbAutoSizeEx = False
        Me.btn_Visible.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_Visible.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_Visible.tbBadgeNumber = 0
        Me.btn_Visible.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_Visible.tbEndEllipsis = False
        Me.btn_Visible.tbIconHoldPlace = True
        Me.btn_Visible.tbIconImage = Nothing
        Me.btn_Visible.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_Visible.tbIconMore = False
        Me.btn_Visible.tbIconMouseDown = Nothing
        Me.btn_Visible.tbIconMouseHover = Nothing
        Me.btn_Visible.tbIconMouseLeave = Nothing
        Me.btn_Visible.tbIconPlaceText = 2
        Me.btn_Visible.tbIconReadOnly = Nothing
        Me.btn_Visible.tbImageMouseDown = Nothing
        Me.btn_Visible.tbImageMouseHover = Nothing
        Me.btn_Visible.tbImageMouseLeave = Nothing
        Me.btn_Visible.tbReadOnly = False
        Me.btn_Visible.tbReadOnlyText = False
        Me.btn_Visible.tbShadow = False
        Me.btn_Visible.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_Visible.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_Visible.tbShowNew = False
        Me.btn_Visible.tbShowToolTipOnButton = False
        Me.btn_Visible.tbSplit = "3,3,3,3"
        Me.btn_Visible.tbText = ""
        Me.btn_Visible.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.tbTextColor = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorDown = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorHover = System.Drawing.Color.White
        Me.btn_Visible.tbTextMouseDownPlace = 0
        Me.btn_Visible.tbToolTip = ""
        Me.btn_Visible.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_Visible.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Visible.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.VisibleEx = True
        '
        'tpnlContainer
        '
        Me.tpnlContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tpnlContainer.ColumnCount = 2
        Me.tpnlContainer.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlContainer.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.tpnlContainer.Controls.Add(Me.pnlState, 0, 1)
        Me.tpnlContainer.Controls.Add(Me.pnlMain, 0, 0)
        Me.tpnlContainer.Controls.Add(Me.pnlInfo, 1, 0)
        Me.tpnlContainer.Location = New System.Drawing.Point(3, 33)
        Me.tpnlContainer.Name = "tpnlContainer"
        Me.tpnlContainer.RowCount = 2
        Me.tpnlContainer.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlContainer.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.tpnlContainer.Size = New System.Drawing.Size(594, 466)
        Me.tpnlContainer.TabIndex = 3
        '
        'pnlInfo
        '
        Me.pnlInfo.BackColor = System.Drawing.Color.White
        Me.pnlInfo.Controls.Add(Me.picApp)
        Me.pnlInfo.Controls.Add(Me.lblAppName)
        Me.pnlInfo.Controls.Add(Me.lblInfo1)
        Me.pnlInfo.Controls.Add(Me.lblInfo)
        Me.pnlInfo.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlInfo.Location = New System.Drawing.Point(194, 0)
        Me.pnlInfo.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlInfo.Name = "pnlInfo"
        Me.pnlInfo.Size = New System.Drawing.Size(400, 426)
        Me.pnlInfo.TabIndex = 25
        '
        'picApp
        '
        Me.picApp.BackgroundImage = Global.iTong.My.Resources.Resources.file_app_large
        Me.picApp.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.picApp.Location = New System.Drawing.Point(19, 26)
        Me.picApp.Name = "picApp"
        Me.picApp.Size = New System.Drawing.Size(55, 55)
        Me.picApp.TabIndex = 5
        Me.picApp.TabStop = False
        '
        'lblAppName
        '
        Me.lblAppName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblAppName.AutoSize = True
        Me.lblAppName.ForeColor = System.Drawing.Color.Black
        Me.lblAppName.Location = New System.Drawing.Point(8, 86)
        Me.lblAppName.Name = "lblAppName"
        Me.lblAppName.Size = New System.Drawing.Size(77, 12)
        Me.lblAppName.TabIndex = 4
        Me.lblAppName.Text = "苹果游戏商店"
        Me.lblAppName.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblInfo1
        '
        Me.lblInfo1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblInfo1.AutoSize = True
        Me.lblInfo1.ForeColor = System.Drawing.Color.Red
        Me.lblInfo1.Location = New System.Drawing.Point(89, 34)
        Me.lblInfo1.Name = "lblInfo1"
        Me.lblInfo1.Size = New System.Drawing.Size(311, 12)
        Me.lblInfo1.TabIndex = 2
        Me.lblInfo1.Text = "必须通过""苹果游戏商店""APP激活软件，才能算有效激活。"
        Me.lblInfo1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblInfo
        '
        Me.lblInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblInfo.AutoSize = True
        Me.lblInfo.ForeColor = System.Drawing.Color.Black
        Me.lblInfo.Location = New System.Drawing.Point(89, 59)
        Me.lblInfo.Name = "lblInfo"
        Me.lblInfo.Size = New System.Drawing.Size(275, 12)
        Me.lblInfo.TabIndex = 2
        Me.lblInfo.Text = "安装激活专区软件会默认安装""苹果游戏商店""APP。"
        Me.lblInfo.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmActivationHelp
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(600, 500)
        Me.Controls.Add(Me.tpnlContainer)
        Me.Controls.Add(Me.btn_Visible)
        Me.MinimumSize = New System.Drawing.Size(20, 50)
        Me.Name = "frmActivationHelp"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,40,10,10"
        Me.tbTitleFont = New System.Drawing.Font("Tahoma", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "激活教程"
        Me.pnlMain.ResumeLayout(False)
        Me.pnlState.ResumeLayout(False)
        Me.tpnlContainer.ResumeLayout(False)
        Me.pnlInfo.ResumeLayout(False)
        Me.pnlInfo.PerformLayout()
        CType(Me.picApp, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlMain As System.Windows.Forms.Panel
    Friend WithEvents pnlState As System.Windows.Forms.Panel
    Friend WithEvents btn_Visible As iTong.Components.tbButton
    Friend WithEvents chkRemember As iTong.Components.tbCheckBox
    Friend WithEvents btnClose As iTong.Components.tbButton
    Friend WithEvents tpnlContainer As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlInfo As System.Windows.Forms.Panel
    Friend WithEvents lblInfo As System.Windows.Forms.Label
    Friend WithEvents picApp As System.Windows.Forms.PictureBox
    Friend WithEvents lblAppName As System.Windows.Forms.Label
    Friend WithEvents lblInfo1 As System.Windows.Forms.Label
    Friend WithEvents wbsHelper As System.Windows.Forms.WebBrowser
End Class
