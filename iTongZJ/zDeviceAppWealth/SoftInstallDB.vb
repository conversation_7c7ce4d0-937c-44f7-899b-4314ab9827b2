﻿Public Class SoftInstallDB
    Private mSoftDBFile As String = Path.Combine(Folder.CacheFolder, "Cache.dll")
    Private mDataSet As DataSet
    Private mSoftTable As DataTable

#Region "--- 单实例 ---"

    Private Shared instance As SoftInstallDB = Nothing
    Private Shared ReadOnly locker As New Object()
    Private Shared ReadOnly lockerSoftTable As New Object()

    Public Shared Function GetInstance() As SoftInstallDB
        If instance Is Nothing Then
            SyncLock locker
                If instance Is Nothing Then
                    instance = New SoftInstallDB()
                End If
            End SyncLock
        End If

        Return instance
    End Function

#End Region

#Region "--- 构造函数 ---"

    Private Sub New()
        If System.IO.File.Exists(mSoftDBFile) Then
            '已经初始化
            Try
                mDataSet = Common.Deserialize(Of DataSet)(mSoftDBFile)
                mSoftTable = mDataSet.Tables("SoftInfo")
            Catch
            End Try
        End If

        If Me.mDataSet Is Nothing Then
            Me.mDataSet = New DataSet()
        End If

        If Me.mSoftTable Is Nothing Then
            '首次初始化
            mSoftTable = New DataTable()
            mSoftTable.TableName = "SoftInfo"

            mSoftTable.Columns.Add("Json", Type.[GetType]("System.String"))
            mSoftTable.Columns.Add("UDID", Type.[GetType]("System.String"))
            mSoftTable.Columns.Add("MD5", Type.[GetType]("System.String"))

            Me.mDataSet.Tables.Add(Me.mSoftTable)
        End If
    End Sub

#End Region

#Region "--- SoftTable表---"

    Public Sub AddSoftInfo(ByVal strJson As String, ByVal strUDID As String, ByVal strMD5 As String)
        Try
            'If String.IsNullOrEmpty(strJson) Then
            '    Return
            'End If

            'If Me.IsContainInSoftTable(strUDID, strMD5) Then
            '    Return
            'End If

            'Dim row As DataRow = mSoftTable.NewRow()
            'row("Json") = strJson
            'row("UDID") = strUDID
            'row("MD5") = strMD5

            'Dim rows As DataRow() = New DataRow(0) {}
            'rows(0) = row
            Me.AddOrDeleteSoftRow(New DataRow(0) {}, True, strJson, strUDID, strMD5)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddSoftInfo")
        End Try
    End Sub

    Public Function GetAllSoftInfo() As DataRow()
        Dim count As Integer = mSoftTable.Rows.Count
        'if (count > 100)
        '{
        '    count = 100;
        '}

        Dim rows As DataRow() = New DataRow(count - 1) {}

        For i As Integer = 0 To count - 1
            rows(i) = mSoftTable.Rows(i)
        Next

        Return rows
    End Function

    Public Sub DeleteSoftInfo(ByVal row As DataRow)
        If row Is Nothing Then
            Return
        End If

        Dim rows As DataRow() = New DataRow(0) {}
        rows(0) = row

        Me.AddOrDeleteSoftRow(rows, False, "", "", "")
    End Sub

    Public Sub DeleteSoftInfo(ByVal rows As DataRow())
        If rows Is Nothing OrElse rows.Length = 0 Then
            Return
        End If

        Me.AddOrDeleteSoftRow(rows, False, "", "", "")
    End Sub

    Public Function IsContainInSoftTable(ByVal strUDID As String, ByVal strMD5 As String) As Boolean
        Dim srcCondition As String = "UDID='{0}' AND MD5='{1}'"
        Dim strSQL As String = String.Format(srcCondition, strUDID, strMD5)
        Dim arrResult As DataRow() = mSoftTable.[Select](strSQL)

        Return (arrResult IsNot Nothing AndAlso arrResult.Length > 0)

        'bool isContain = false;
        'for (int i = mSoftTable.Rows.Count - 1; i >= 0; i--)
        '{
        '    if (string.Compare(mSoftTable.Rows[i]["UDID"].ToString(), device.Identifier , true) == 0 &&
        '         string.Compare(mSoftTable.Rows[i]["SKU"].ToString(), package.Identifier , true) == 0 &&
        '         string.Compare(mSoftTable.Rows[i]["Version"].ToString(), package.Version , true) == 0)

        '    {
        '        isContain = true;
        '        break;
        '    }
        '}

        'return isContain;
    End Function

    Private Sub AddOrDeleteSoftRow(ByVal rows As DataRow(), ByVal IsAdd As Boolean, ByVal strJson As String, ByVal strUDID As String, ByVal strMD5 As String)
        SyncLock lockerSoftTable
            Try
                If IsAdd Then

                    If String.IsNullOrEmpty(strJson) Then
                        Common.Log("添加回传数据时Json为空")
                        Return
                    End If

                    If Me.IsContainInSoftTable(strUDID, strMD5) Then
                        Common.Log("添加回传数据时strUDID：" & strUDID & " strMD5： " & strMD5 & " 已存在")
                        Return
                    End If

                    Dim row As DataRow = mSoftTable.NewRow()
                    row("Json") = strJson
                    row("UDID") = strUDID
                    row("MD5") = strMD5

                    Dim mrows As DataRow() = New DataRow(0) {}
                    mrows(0) = row

                    For Each item As DataRow In mrows
                        Me.mSoftTable.Rows.Add(row)
                    Next
                Else
                    For i As Integer = 0 To rows.Length - 1
                        For j As Integer = mSoftTable.Rows.Count - 1 To 0 Step -1
                            If mSoftTable.Rows(j)("UDID").ToString() = rows(i)("UDID").ToString() AndAlso mSoftTable.Rows(j)("MD5").ToString() = rows(i)("MD5").ToString() Then
                                Me.mSoftTable.Rows.RemoveAt(j)
                                Exit For
                            End If
                        Next
                    Next
                End If

                rows = Nothing
            Catch e As Exception
                Common.LogException("AddOrDeleteSoftRow：" & e.ToString())
            End Try
        End SyncLock
    End Sub

#End Region

    Public Sub SaveDataTable()
        Try
            Common.Serialize(Of DataSet)(mSoftDBFile, Me.mDataSet)
        Catch
        End Try
    End Sub

End Class