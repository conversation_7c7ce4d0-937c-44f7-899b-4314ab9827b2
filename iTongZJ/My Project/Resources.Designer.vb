﻿'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:2.0.50727.5485
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    '此类是由 StronglyTypedResourceBuilder
    '类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    '若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    '(以 /str 作为命令选项)，或重新生成 VS 项目。
    '''<summary>
    '''  强类型资源类，用于查找本地化字符串等。
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "2.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  返回此类使用的缓存 ResourceManager 实例。
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("iTong.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  为使用此强类型资源类的所有资源查找
        '''  重写当前线程的 CurrentUICulture 属性。
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        Friend ReadOnly Property androidSummary_contacts() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("androidSummary_contacts", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property androidSummary_SMS() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("androidSummary_SMS", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property androidSummary_weixin() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("androidSummary_weixin", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_ascend_arrow() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_ascend_arrow", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_bg_btn_msg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_bg_btn_msg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_btn_2_more() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_btn_2_more", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_btn_4_ignore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_btn_4_ignore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_btn_4_open() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_btn_4_open", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_btn_4_showmore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_btn_4_showmore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_cell_fail() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_cell_fail", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_descend_arrow() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_descend_arrow", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_downloads_background() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_downloads_background", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_downloads_value() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_downloads_value", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_drop_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_drop_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_drop_bg_h() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_drop_bg_h", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_icon_57_white() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_icon_57_white", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_icon_default() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_icon_default", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_icon_gray() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_icon_gray", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_icon_jinbao() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_icon_jinbao", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_icon_noapp() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_icon_noapp", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_icon_select() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_icon_select", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_icon_white() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_icon_white", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找类似 &lt;!DOCTYPE HTML&gt;
        '''&lt;html&gt;
        '''    &lt;head&gt;
        '''        &lt;title&gt;#TrackName#&lt;/title&gt;
        '''        &lt;meta http-equiv=&quot;Content-Language&quot; content=&quot;zh-cn&quot;/&gt;
        '''        &lt;meta http-equiv=&quot;Content-Type&quot; content=&quot;text/html; charset=utf-8&quot;/&gt;
        '''        &lt;link rel=&quot;stylesheet&quot; href=&quot;#AppDetailCSS#&quot; type=&quot;text/css&quot;&gt;
        '''        &lt;script src=&quot;#AppDetailjQuery#&quot; type=&quot;text/javascript&quot;&gt;&lt;/script&gt;
        '''    &lt;/head&gt;
        '''&lt;body&gt;
        '''
        '''  &lt;div class=&quot;mod&quot; &gt;
        '''    &lt;div class=&quot;artwork&quot;&gt;
        '''        &lt;i&gt;&lt;/i&gt;
        '''        &lt;img width=&quot;145&quot; height=&quot;145&quot; alt=&quot;PowerSketch&quot; class=&quot;a [字符串的其余部分被截断]&quot;; 的本地化字符串。
        '''</summary>
        Friend ReadOnly Property app_info() As String
            Get
                Return ResourceManager.GetString("app_info", resourceCulture)
            End Get
        End Property
        
        Friend ReadOnly Property app_pic_imporFolder() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_pic_imporFolder", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_pic_importFiles() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_pic_importFiles", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_pic_importitunes() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_pic_importitunes", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_product_all() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_product_all", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_product_iPad() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_product_iPad", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_product_iPhone() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_product_iPhone", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_progress_background() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_progress_background", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property app_progress_value() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("app_progress_value", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找类似 &lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
        '''&lt;!DOCTYPE plist PUBLIC &quot;-//Apple Computer//DTD PLIST 1.0//EN&quot; &quot;http://www.apple.com/DTDs/PropertyList-1.0.dtd&quot;&gt;
        '''&lt;plist version=&quot;1.0&quot;&gt;
        '''&lt;dict&gt;
        '''         &lt;key&gt;commands&lt;/key&gt;
        '''         &lt;array&gt;
        '''                 &lt;dict&gt;
        '''                         &lt;key&gt;command&lt;/key&gt;
        '''                         &lt;string&gt;remove&lt;/string&gt;
        '''                         &lt;key&gt;package&lt;/key&gt;
        '''                         &lt;string&gt;%s&lt;/string&gt;
        '''                 &lt;/dict&gt;
        '''         &lt;/array&gt;
        '''&lt;/dict&gt;
        '''&lt;/plist&gt;
        ''' 的本地化字符串。
        '''</summary>
        Friend ReadOnly Property app_pxl_uninstall() As String
            Get
                Return ResourceManager.GetString("app_pxl_uninstall", resourceCulture)
            End Get
        End Property
        
        Friend ReadOnly Property appitem_check() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("appitem_check", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property appitem_installed() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("appitem_installed", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property appitem_uncheck() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("appitem_uncheck", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property authority_icon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("authority_icon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property authority_user() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("authority_user", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property bar_update_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bar_update_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property bar_update_play() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bar_update_play", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property bar_update_play1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bar_update_play1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property bg_Flash_Guide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bg_Flash_Guide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property bg_guide_median() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bg_guide_median", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property bg_Jailbreak_Guide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bg_Jailbreak_Guide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property bg_light_blue() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("bg_light_blue", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_1_checked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_1_checked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_1_transparent() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_1_transparent", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_2_tab() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_2_tab", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_3_cancel() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_cancel", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_3_fullScreen() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_fullScreen", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_3_goto() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_goto", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_3_openfolder() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_openfolder", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_3_screenshot() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_screenshot", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_3_search() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_search", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_3_setting() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_setting", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_3_weiBo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_weiBo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_backup() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_backup", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_blue() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_blue", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_blue_jaibreak() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_blue_jaibreak", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_blue1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_blue1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_clearrubbish() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_clearrubbish", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_close() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_close", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_copy() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_copy", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_failurerestore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_failurerestore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_fullScreen() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_fullScreen", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_next() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_next", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_prev() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_prev", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_refresh() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_refresh", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_refresh1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_refresh1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_refresh2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_refresh2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_reply() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_reply", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_restore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_restore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_screenshot() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_screenshot", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_setting() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_setting", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_sharing() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_sharing", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_view() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_view", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_white() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_white", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_4_white1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_white1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_add_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_add_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_addfolder_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_addfolder_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_backup_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_backup_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_buy_3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_buy_3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_checked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_checked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_close() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_close", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_close1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_close1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_closedeye() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_closedeye", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_delete_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_delete_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_exchange() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_exchange", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_export_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_export_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_feedback_3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_feedback_3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_file_arrow() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_file_arrow", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_file_copy() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_file_copy", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_filter() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_filter", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_hide() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_hide", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_ignore_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_ignore_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_import_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_import_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_install_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_install_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_max() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_max", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_min() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_min", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_openeye() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_openeye", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_openlog() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_openlog", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_pause_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_pause_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_play_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_play_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_refresh() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_refresh", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_repair_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_repair_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_restore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_restore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_revert_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_revert_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_save_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_save_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_setting() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_setting", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_Setup() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_Setup", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_skin_3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_skin_3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_turn_left() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_turn_left", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_turn_right() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_turn_right", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_unchecked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_unchecked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_update_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_update_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_view() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_view", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_viewlargeicon_checked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_viewlargeicon_checked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_viewlargeicon_unchecked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_viewlargeicon_unchecked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_viewlist_checked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_viewlist_checked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_viewlist_selected() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_viewlist_selected", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_viewlist_unchecked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_viewlist_unchecked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_viewlist_unselected() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_viewlist_unselected", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property btn_welcome_connect() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_welcome_connect", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property Connection_icon_a() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Connection_icon_a", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property Connection_icon_b() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Connection_icon_b", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property Connection_icon_c() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Connection_icon_c", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property Connection_icon_s() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("Connection_icon_s", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property connection_image() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("connection_image", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property contact_btn_down() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("contact_btn_down", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property contact_btn_hover() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("contact_btn_hover", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property device_default() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("device_default", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_33_loading() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_33_loading", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_delete() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_delete", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_download_pause() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_download_pause", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_download_start() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_download_start", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_edit() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_edit", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_play() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_play", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_setalarm() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_setalarm", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_setnotification() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_setnotification", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_setringtone() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_setringtone", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_4_stop() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_4_stop", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_craked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_craked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_filenoexist() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_filenoexist", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_info() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_info", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_payment() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_payment", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_personal() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_personal", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_playing() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_playing", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_uncraked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_uncraked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_uncrakedwepay() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_uncrakedwepay", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_update() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_update", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property dgv_waiting() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_waiting", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property download_downloading() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("download_downloading", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property download_stop() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("download_stop", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_app_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_app_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_app_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_app_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_bg_applistbottom() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_bg_applistbottom", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_bg_filepath() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_bg_filepath", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_btn_2_myfavorite() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_btn_2_myfavorite", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_btn_4_back() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_btn_4_back", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_btn_myfavorite() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_btn_myfavorite", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_cydia_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_cydia_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_cydia_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_cydia_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_documents_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_documents_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_documents_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_documents_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_excel_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_excel_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_excel_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_excel_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_excel_75() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_excel_75", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_file_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_file_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_file_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_file_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_fileapp_113() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_fileapp_113", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_fileapp_47() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_fileapp_47", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_fileempty() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_fileempty", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_fileempty_smile() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_fileempty_smile", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_filesearch_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_filesearch_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_filesearch_30() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_filesearch_30", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_filesearch_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_filesearch_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_filesearch_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_filesearch_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_fileshortcut_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_fileshortcut_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_fileshortcut_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_fileshortcut_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_filesystem_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_filesystem_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_folder_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_folder_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_folder_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_folder_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_folder_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_folder_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_foldershortcut_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_foldershortcut_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_foldershortcut_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_foldershortcut_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_icon_ipad() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_icon_ipad", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_icon_iphone() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_icon_iphone", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_icon_no_app() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_icon_no_app", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_icon_searchnext() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_icon_searchnext", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_icon_searchprevious() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_icon_searchprevious", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_installous_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_installous_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_installous_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_installous_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_ipa_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_ipa_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_letv() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_letv", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_myfavorite_largel() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_myfavorite_largel", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_myfavorite_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_myfavorite_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_oplayer_113() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_oplayer_113", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_oplayer_47() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_oplayer_47", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_pdf_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_pdf_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_pdf_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_pdf_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_pdf_75() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_pdf_75", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_pic_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_pic_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_pic_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_pic_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_plist_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_plist_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_plist_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_plist_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_ppt_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_ppt_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_ppt_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_ppt_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_ppt_75() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_ppt_75", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_qqvideo_113() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_qqvideo_113", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_qqvideo_47() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_qqvideo_47", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_tbtui_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_tbtui_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_tbtui_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_tbtui_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_tbtui_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_tbtui_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_txt_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_txt_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_txt_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_txt_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_txt_75() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_txt_75", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_unfind_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_unfind_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_unfind_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_unfind_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_unfindfile_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_unfindfile_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_unfindfile_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_unfindfile_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_video_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_video_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_video_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_video_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_video_75() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_video_75", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_weixin_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_weixin_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_word_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_word_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_word_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_word_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property file_word_75() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_word_75", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_blank() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_blank", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_login() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_login", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_shadow() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_shadow", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_state() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_state", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_sub() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_sub", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_Tip() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_Tip", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_tip_binding() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_tip_binding", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_tip_binding1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_tip_binding1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_bg_Tip_down() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_Tip_down", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_splashmsgbox_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_splashmsgbox_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property frm_top_split() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_top_split", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property gif_getinivationcode() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gif_getinivationcode", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property gif_installtui() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gif_installtui", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property gif_loading_14() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gif_loading_14", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property gif_loading_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gif_loading_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property gif_loading_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gif_loading_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property gif_loading_32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gif_loading_32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property gif_loading_summary_16() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gif_loading_summary_16", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property h5_game() As Byte()
            Get
                Dim obj As Object = ResourceManager.GetObject("h5_game", resourceCulture)
                Return CType(obj,Byte())
            End Get
        End Property
        
        Friend ReadOnly Property h5game_install_detail() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("h5game_install_detail", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property H5Game_install_step1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("H5Game_install_step1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property H5Game_install_step2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("H5Game_install_step2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property html_pop() As Byte()
            Get
                Dim obj As Object = ResourceManager.GetObject("html_pop", resourceCulture)
                Return CType(obj,Byte())
            End Get
        End Property
        
        Friend ReadOnly Property icon_android_feedback() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_android_feedback", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_appleid() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_appleid", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_appnodata_100() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_appnodata_100", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_appnodata_120() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_appnodata_120", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_attention() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_attention", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_battery() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_battery", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_booknodata_100() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_booknodata_100", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_booknodata_120() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_booknodata_120", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_Discount() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_Discount", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_downnodata_100() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_downnodata_100", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_error() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_error", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_error_14() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_error_14", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_firmwarenodata_100() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_firmwarenodata_100", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_hot() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_hot", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_lock() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_lock", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_login() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_login", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_login_empty() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_login_empty", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_login_empty_light() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_login_empty_light", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_musicnodata_100() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_musicnodata_100", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_musicnodata_120() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_musicnodata_120", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_ok() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_ok", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_photonodata_100() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_photonodata_100", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_photonodata_120() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_photonodata_120", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_prompt() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_prompt", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_qqweibo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_qqweibo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_question() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_question", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_ringnodata_100() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_ringnodata_100", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_ringnodata_120() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_ringnodata_120", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_shield() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_shield", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_sinaweibo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_sinaweibo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_time() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_time", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_upgrade() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_upgrade", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_usb_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_usb_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_videonodata_100() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_videonodata_100", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_videonodata_120() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_videonodata_120", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_warning() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_warning", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_warning1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_warning1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_warning2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_warning2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icon_wifi_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_wifi_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property icons_itong_downloading_116() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icons_itong_downloading_116", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property ipa_AGSPlan() As Byte()
            Get
                Dim obj As Object = ResourceManager.GetObject("ipa_AGSPlan", resourceCulture)
                Return CType(obj,Byte())
            End Get
        End Property
        
        Friend ReadOnly Property iTong() As System.Drawing.Icon
            Get
                Dim obj As Object = ResourceManager.GetObject("iTong", resourceCulture)
                Return CType(obj,System.Drawing.Icon)
            End Get
        End Property
        
        Friend ReadOnly Property iTong_check1() As System.Drawing.Icon
            Get
                Dim obj As Object = ResourceManager.GetObject("iTong_check1", resourceCulture)
                Return CType(obj,System.Drawing.Icon)
            End Get
        End Property
        
        Friend ReadOnly Property iTong_check2() As System.Drawing.Icon
            Get
                Dim obj As Object = ResourceManager.GetObject("iTong_check2", resourceCulture)
                Return CType(obj,System.Drawing.Icon)
            End Get
        End Property
        
        Friend ReadOnly Property iTong_check3() As System.Drawing.Icon
            Get
                Dim obj As Object = ResourceManager.GetObject("iTong_check3", resourceCulture)
                Return CType(obj,System.Drawing.Icon)
            End Get
        End Property
        
        Friend ReadOnly Property iTong_check4() As System.Drawing.Icon
            Get
                Dim obj As Object = ResourceManager.GetObject("iTong_check4", resourceCulture)
                Return CType(obj,System.Drawing.Icon)
            End Get
        End Property
        
        Friend ReadOnly Property logo() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("logo", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property logo_13() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("logo_13", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property more_repair() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("more_repair", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property nav_top_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("nav_top_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_bottom_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_bottom_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_dev_exit_hover() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_dev_exit_hover", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_dev_exit_select() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_dev_exit_select", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_dev_exit_unselect() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_dev_exit_unselect", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_device_airdroid() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_device_airdroid", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_device_airdroid_gray() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_device_airdroid_gray", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_device_default() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_device_default", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_device_ios() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_device_ios", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_device_ios_gray() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_device_ios_gray", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_frm_bg_sub() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_frm_bg_sub", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_icon_repair() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_icon_repair", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_tool_hover() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_tool_hover", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_tool_select() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_tool_select", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_activation() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_activation", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_activation_light() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_activation_light", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_app() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_app", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_data() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_data", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_data_light() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_data_light", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_file() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_file", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_file_light() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_file_light", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_fiveyear() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_fiveyear", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_home() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_home", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_jailbreak() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_jailbreak", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_jailbreak_light() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_jailbreak_light", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_light() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_light", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_line() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_line", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_site() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_site", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property new_top_watermark() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("new_top_watermark", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property notify_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("notify_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property notify_bg1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("notify_bg1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property photo_continuousshots() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_continuousshots", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property photo_livephoto() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_livephoto", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pic_login() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pic_login", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pic_login_apple_id() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pic_login_apple_id", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pic_trust() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pic_trust", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property player_progress_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("player_progress_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property player_progress_play() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("player_progress_play", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pnl_bg_about() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_bg_about", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pnl_bg_about_bottom() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_bg_about_bottom", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pnl_bg_info() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_bg_info", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pnl_bg_navigation() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_bg_navigation", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pnl_bg_state() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_bg_state", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pnl_bg_status() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_bg_status", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pnl_loading() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_loading", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property pnl_loading_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_loading_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property progress_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("progress_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property repair_green() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("repair_green", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property repair_red() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("repair_red", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property screenshot_btn_4_refresh() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("screenshot_btn_4_refresh", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property settingtype_hover() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("settingtype_hover", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property settingtype_selected() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("settingtype_selected", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property skin_default_setting() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("skin_default_setting", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property skin_select() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("skin_select", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property splash_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("splash_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_1_w() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_1_w", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_2_w() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_2_w", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_3_w() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_3_w", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_bg_w() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_bg_w", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_download() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_download", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_install() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_install", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_ok() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_ok", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property step_restart() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("step_restart", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_appinfo_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_appinfo_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_appleid_split() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_appleid_split", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_apprange() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_apprange", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_apprange_white() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_apprange_white", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_backup() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_backup", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_binding_apple_id() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_binding_apple_id", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_btn_3_disconnect() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_btn_3_disconnect", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_btn_3_edit() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_btn_3_edit", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_btn_3_info() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_btn_3_info", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_btn_4_close() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_btn_4_close", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_btn_4_ok() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_btn_4_ok", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_defaulticon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_defaulticon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_error() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_error", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_h5game() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_h5game", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_icon_jailbreak() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_icon_jailbreak", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_nav_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_nav_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_nav_split() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_nav_split", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_qq() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_qq", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_repair_pay_v3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_repair_pay_v3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_repair_v3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_repair_v3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_select() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_select", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_tui_v3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_tui_v3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_unselect() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_unselect", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_weixin() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_weixin", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_weixinemoticon_v3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_weixinemoticon_v3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property summary_zj_v3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("summary_zj_v3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tongbu_bg_256() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tongbu_bg_256", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tongbu_bg_32() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tongbu_bg_32", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tongbu_bg_48() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tongbu_bg_48", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tsmi_checked() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tsmi_checked", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tui_tutorial_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tui_tutorial_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tui_web_InstallOperate() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tui_web_InstallOperate", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tui_web_installStep1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tui_web_installStep1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tui_web_installStep2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tui_web_installStep2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tui_web_installStep3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tui_web_installStep3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tui_web_mobliePicture() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tui_web_mobliePicture", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_apps_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_apps_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_filesharing_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_filesharing_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_firmware_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_firmware_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_myfav_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_myfav_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_node_bg_select() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_node_bg_select", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_plug_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_plug_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_tongbuapp_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_tongbuapp_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_tongbuappjail_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_tongbuappjail_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_tutorials_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_tutorials_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_update_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_update_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_zjnewdervice_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_zjnewdervice_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property tvw_zjzhuanqu_2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_zjzhuanqu_2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_combo_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_combo_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_coolpad1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_coolpad1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_coolpad2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_coolpad2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_coolpad3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_coolpad3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_coolpad4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_coolpad4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_coolpad5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_coolpad5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_coolpad6() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_coolpad6", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_flyme1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_flyme1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_flyme2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_flyme2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_flyme3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_flyme3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_flyme4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_flyme4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_flyme5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_flyme5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_flyme6() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_flyme6", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_flyme7() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_flyme7", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_flyme8() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_flyme8", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc6() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc6", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc7() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc7", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc8() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc8", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_htc9() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_htc9", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_icecream1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_icecream1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_icecream2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_icecream2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_icecream3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_icecream3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_icecream4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_icecream4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_icecream5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_icecream5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_icecream6() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_icecream6", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_jellybean1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_jellybean1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_jellybean2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_jellybean2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_jellybean3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_jellybean3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_jellybean4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_jellybean4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_jellybean5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_jellybean5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_jellybean6() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_jellybean6", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_jellybean7() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_jellybean7", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_miui1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_miui1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_miui2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_miui2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_miui3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_miui3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_miui4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_miui4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_miui5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_miui5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_miui6() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_miui6", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_miui7() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_miui7", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_miui8() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_miui8", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_next() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_next", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property usb_debug_pre() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("usb_debug_pre", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property webclip_note() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("webclip_note", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property webclip_step1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("webclip_step1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property webclip_step2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("webclip_step2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property wechat_message_lock() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("wechat_message_lock", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property weibo_facebook() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weibo_facebook", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property weibo_sina() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weibo_sina", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property weibo_tecent() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weibo_tecent", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property weibo_twitter() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weibo_twitter", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property weibo_v3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weibo_v3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property weixin_close3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weixin_close3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property weixin_dll_silk() As Byte()
            Get
                Dim obj As Object = ResourceManager.GetObject("weixin_dll_silk", resourceCulture)
                Return CType(obj,Byte())
            End Get
        End Property
        
        Friend ReadOnly Property weixin_guide_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weixin_guide_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_android_driver() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_android_driver", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_btn_split() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_btn_split", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_connect_helper() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_connect_helper", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_connect_helper_step1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_connect_helper_step1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_connect_helper_step2() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_connect_helper_step2", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_connect_helper_step3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_connect_helper_step3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_connect_helper_step4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_connect_helper_step4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_connect_helper_step5() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_connect_helper_step5", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_connect_phone() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_connect_phone", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_dev_coming() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_dev_coming", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_gif_error() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_gif_error", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找类似 &lt;!DOCTYPE html PUBLIC &quot;-//W3C//DTD XHTML 1.0 Transitional//EN&quot; &quot;http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd&quot;&gt;
        '''&lt;html xmlns=&quot;http://www.w3.org/1999/xhtml&quot; xml:lang=&quot;en&quot; lang=&quot;en&quot;&gt;
        '''&lt;head&gt;
        '''    &lt;meta http-equiv=&quot;Content-Type&quot; content=&quot;text/html; charset=utf-8&quot; /&gt;
        '''    &lt;link rel=&quot;stylesheet&quot; type=&quot;text/css&quot; href=&quot;/han-zhushou/css/inside.css&quot; /&gt;
        '''    &lt;title&gt;Error Inside&lt;/title&gt;
        '''&lt;/head&gt;
        '''&lt;style&gt;
        '''a{ color:#8DA2B7; text-decoration: none;}
        '''body, div{background:#E9EDF3;font-size: 12px;font-family: &apos;He [字符串的其余部分被截断]&quot;; 的本地化字符串。
        '''</summary>
        Friend ReadOnly Property welcome_htm_error() As String
            Get
                Return ResourceManager.GetString("welcome_htm_error", resourceCulture)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_install_daemon_auth() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_install_daemon_auth", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_phone_empty() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_phone_empty", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_phone_frame() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_phone_frame", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_reconnect() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_reconnect", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        Friend ReadOnly Property welcome_usb_auth() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("welcome_usb_auth", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
