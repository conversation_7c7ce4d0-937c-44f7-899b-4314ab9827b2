﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{045062F0-92EB-43D3-A40B-DA27BB14F28D}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>iTong.My.MyApplication</StartupObject>
    <RootNamespace>iTong</RootNamespace>
    <AssemblyName>ZJHelper</AssemblyName>
    <MyType>WindowsForms</MyType>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <ApplicationIcon>..\iTong\Resources\iTong.ico</ApplicationIcon>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>2.0</OldToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <DocumentationFile>ZJHelper.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=False,IS_ITONG_ZJ=True</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <DocumentationFile>ZJHelper.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleAssemblies>D:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=False,IS_ITONG_ZJ=True</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DocumentationFile>ZJHelper.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleAssemblies>D:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=False,IS_ITONG_ZJ=True</DefineConstants>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <DocumentationFile>ZJHelper.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleAssemblies>D:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
    <DefineConstants>IS_ITONG=False,IS_ITONG_ZJ=True</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.49.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\iTong\IncludeDlls\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="iTong.Android" />
    <Import Include="iTong.Components" />
    <Import Include="iTong.CoreFoundation" />
    <Import Include="iTong.CoreModule" />
    <Import Include="iTong.CoreReses" />
    <Import Include="iTong.Device" />
    <Import Include="iTong.Update" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.IO" />
    <Import Include="System.Net" />
    <Import Include="System.Text" />
    <Import Include="System.Web" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\iTong\zClass\ProcFormHelper.vb">
      <Link>zClass\ProcFormHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zLogin\frmLogin.Designer.vb">
      <DependentUpon>frmLogin.vb</DependentUpon>
      <Link>zLogin\frmLogin.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zLogin\frmLogin.vb">
      <Link>zLogin\frmLogin.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\ApplicationEvents.vb" />
    <Compile Include="..\iTong\frmSplash.Designer.vb">
      <DependentUpon>frmSplash.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\frmSplash.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="..\iTong\ProcForm.Designer.vb">
      <DependentUpon>ProcForm.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\ProcForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\AndroidAppMgr.vb">
      <Link>zAndroidApp\AndroidAppMgr.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\AndroidDeviceHelper.vb">
      <Link>zAndroidApp\AndroidDeviceHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\AndroidPackageReader.vb">
      <Link>zAndroidApp\AndroidPackageReader.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidAppListBase.Designer.vb">
      <DependentUpon>frmAndroidAppListBase.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidAppListBase.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidAppListBase.vb">
      <Link>zAndroidApp\frmAndroidAppListBase.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidDeviceApp.Designer.vb">
      <DependentUpon>frmAndroidDeviceApp.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidDeviceApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidDeviceApp.vb">
      <Link>zAndroidApp\frmAndroidDeviceApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidLocalApp.Designer.vb">
      <DependentUpon>frmAndroidLocalApp.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidLocalApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidLocalApp.vb">
      <Link>zAndroidApp\frmAndroidLocalApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidUpdate.Designer.vb">
      <DependentUpon>frmAndroidUpdate.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidUpdate.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidUpdate.vb">
      <Link>zAndroidApp\frmAndroidUpdate.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidUpdateIgnore.Designer.vb">
      <DependentUpon>frmAndroidUpdateIgnore.vb</DependentUpon>
      <Link>zAndroidApp\frmAndroidUpdateIgnore.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidApp\frmAndroidUpdateIgnore.vb">
      <Link>zAndroidApp\frmAndroidUpdateIgnore.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidFile.designer.vb">
      <DependentUpon>frmAndroidFile.vb</DependentUpon>
      <Link>zAndroidFile\frmAndroidFile.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidFile.vb">
      <Link>zAndroidFile\frmAndroidFile.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidFileBase.Designer.vb">
      <DependentUpon>frmAndroidFileBase.vb</DependentUpon>
      <Link>zAndroidFile\frmAndroidFileBase.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidFileBase.vb">
      <Link>zAndroidFile\frmAndroidFileBase.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidPlistEdit.Designer.vb">
      <DependentUpon>frmAndroidPlistEdit.vb</DependentUpon>
      <Link>zAndroidFile\frmAndroidPlistEdit.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidFile\frmAndroidPlistEdit.vb">
      <Link>zAndroidFile\frmAndroidPlistEdit.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidDriver.Designer.vb">
      <DependentUpon>frmAndroidDriver.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidDriver.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidDriver.vb">
      <Link>zAndroidMsgForm\frmAndroidDriver.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidFeedback.Designer.vb">
      <DependentUpon>frmAndroidFeedback.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidFeedback.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidFeedback.vb">
      <Link>zAndroidMsgForm\frmAndroidFeedback.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidNoConnecting.Designer.vb">
      <DependentUpon>frmAndroidNoConnecting.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidNoConnecting.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidNoConnecting.vb">
      <Link>zAndroidMsgForm\frmAndroidNoConnecting.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUninstallMsg.Designer.vb">
      <DependentUpon>frmAndroidUninstallMsg.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidUninstallMsg.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUninstallMsg.vb">
      <Link>zAndroidMsgForm\frmAndroidUninstallMsg.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugBrowser.Designer.vb">
      <DependentUpon>frmAndroidUSBDebugBrowser.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidUSBDebugBrowser.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugBrowser.vb">
      <Link>zAndroidMsgForm\frmAndroidUSBDebugBrowser.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugSimple.Designer.vb">
      <DependentUpon>frmAndroidUSBDebugSimple.vb</DependentUpon>
      <Link>zAndroidMsgForm\frmAndroidUSBDebugSimple.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugSimple.vb">
      <Link>zAndroidMsgForm\frmAndroidUSBDebugSimple.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidDetail.Designer.vb">
      <DependentUpon>frmAndroidDetail.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidDetail.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidDetail.vb">
      <Link>zAndroidWelcome\frmAndroidDetail.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidNoDevice.Designer.vb">
      <DependentUpon>frmAndroidNoDevice.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidNoDevice.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidNoDevice.vb">
      <Link>zAndroidWelcome\frmAndroidNoDevice.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidPreview.Designer.vb">
      <DependentUpon>frmAndroidPreview.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidPreview.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidPreview.vb">
      <Link>zAndroidWelcome\frmAndroidPreview.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidSummary.Designer.vb">
      <DependentUpon>frmAndroidSummary.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidSummary.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidSummary.vb">
      <Link>zAndroidWelcome\frmAndroidSummary.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidWelcome.Designer.vb">
      <DependentUpon>frmAndroidWelcome.vb</DependentUpon>
      <Link>zAndroidWelcome\frmAndroidWelcome.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroidWelcome\frmAndroidWelcome.vb">
      <Link>zAndroidWelcome\frmAndroidWelcome.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zAndroid\AndroidImportHelper.vb">
      <Link>zAndroid\AndroidImportHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroid\frmAndroidBase.Designer.vb">
      <DependentUpon>frmAndroidBase.vb</DependentUpon>
      <Link>zAndroid\frmAndroidBase.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zAndroid\frmAndroidBase.vb">
      <Link>zAndroid\frmAndroidBase.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zClass\AgentHelper.vb">
      <Link>zClass\AgentHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zClass\CommonInfo.vb">
      <Link>zClass\CommonInfo.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zClass\MP3Http.vb">
      <Link>zClass\MP3Http.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zClass\MP3Search.vb">
      <Link>zClass\MP3Search.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbCharBar.vb">
      <SubType>Component</SubType>
      <Link>zComponents\tbCharBar.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbCheckButton.vb">
      <Link>zComponents\tbCheckButton.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbDownloadBar.vb">
      <SubType>Component</SubType>
      <Link>zComponents\tbDownloadBar.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbFlowLayoutItem.vb">
      <SubType>Component</SubType>
      <Link>zComponents\tbFlowLayoutItem.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbFlowLayoutPanelEx.vb">
      <SubType>Component</SubType>
      <Link>zComponents\tbFlowLayoutPanelEx.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbGroupPanel.vb">
      <SubType>Component</SubType>
      <Link>zComponents\tbGroupPanel.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbStepView.vb">
      <SubType>Component</SubType>
      <Link>zComponents\tbStepView.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbTextBoxPath.Designer.vb">
      <DependentUpon>tbTextBoxPath.vb</DependentUpon>
      <Link>zComponents\tbTextBoxPath.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbTextBoxPath.vb">
      <SubType>UserControl</SubType>
      <Link>zComponents\tbTextBoxPath.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zComponents\tbTreeViewEx.vb">
      <SubType>Component</SubType>
      <Link>zComponents\tbTreeViewEx.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewCollect.vb">
      <Link>zDataGridView\tbDataGridViewCollect.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewEditCell.vb">
      <Link>zDataGridView\tbDataGridViewEditCell.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewEx.vb">
      <Link>zDataGridView\tbDataGridViewEx.vb</Link>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewMediaCell.vb">
      <Link>zDataGridView\tbDataGridViewMediaCell.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewProgressEx.vb">
      <Link>zDataGridView\tbDataGridViewProgressEx.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewShowMoreRow.vb">
      <Link>zDataGridView\tbDataGridViewShowMoreRow.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDataGridView\tbDataGridViewTextBoxCellEx.vb">
      <Link>zDataGridView\tbDataGridViewTextBoxCellEx.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmApp.Designer.vb">
      <DependentUpon>frmApp.vb</DependentUpon>
      <Link>zDeviceApp\frmApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmApp.vb">
      <Link>zDeviceApp\frmApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmAppDetail.Designer.vb">
      <DependentUpon>frmAppDetail.vb</DependentUpon>
      <Link>zDeviceApp\frmAppDetail.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmAppDetail.vb">
      <Link>zDeviceApp\frmAppDetail.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmAppFilter.Designer.vb">
      <DependentUpon>frmAppFilter.vb</DependentUpon>
      <Link>zDeviceApp\frmAppFilter.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmAppFilter.vb">
      <Link>zDeviceApp\frmAppFilter.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmCommonApp.Designer.vb">
      <DependentUpon>frmCommonApp.vb</DependentUpon>
      <Link>zDeviceApp\frmCommonApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmCommonApp.vb">
      <Link>zDeviceApp\frmCommonApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmDeviceApp.Designer.vb">
      <DependentUpon>frmDeviceApp.vb</DependentUpon>
      <Link>zDeviceApp\frmDeviceApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmDeviceApp.vb">
      <Link>zDeviceApp\frmDeviceApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmDeviceAppBackup.Designer.vb">
      <DependentUpon>frmDeviceAppBackup.vb</DependentUpon>
      <Link>zDeviceApp\frmDeviceAppBackup.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmDeviceAppBackup.vb">
      <Link>zDeviceApp\frmDeviceAppBackup.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmiTunesLoginV3.Designer.vb">
      <DependentUpon>frmiTunesLoginV3.vb</DependentUpon>
      <Link>zDeviceApp\frmiTunesLoginV3.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmiTunesLoginV3.vb">
      <Link>zDeviceApp\frmiTunesLoginV3.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmLocalApp.Designer.vb">
      <DependentUpon>frmLocalApp.vb</DependentUpon>
      <Link>zDeviceApp\frmLocalApp.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmLocalApp.vb">
      <Link>zDeviceApp\frmLocalApp.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmUpdate.Designer.vb">
      <DependentUpon>frmUpdate.vb</DependentUpon>
      <Link>zDeviceApp\frmUpdate.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmUpdate.vb">
      <Link>zDeviceApp\frmUpdate.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmUpdateIgnore.Designer.vb">
      <DependentUpon>frmUpdateIgnore.vb</DependentUpon>
      <Link>zDeviceApp\frmUpdateIgnore.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\frmUpdateIgnore.vb">
      <Link>zDeviceApp\frmUpdateIgnore.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceApp\IApp.vb">
      <Link>zDeviceApp\IApp.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceClass\DeviceCapacity.vb">
      <Link>zDeviceClass\DeviceCapacity.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceClass\VoiceMemosDB.vb">
      <Link>zDeviceClass\VoiceMemosDB.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFile.Designer.vb">
      <DependentUpon>frmFile.vb</DependentUpon>
      <Link>zDeviceFile\frmFile.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFile.vb">
      <Link>zDeviceFile\frmFile.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFileAppList.Designer.vb">
      <DependentUpon>frmFileAppList.vb</DependentUpon>
      <Link>zDeviceFile\frmFileAppList.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFileAppList.vb">
      <Link>zDeviceFile\frmFileAppList.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFileBase.Designer.vb">
      <DependentUpon>frmFileBase.vb</DependentUpon>
      <Link>zDeviceFile\frmFileBase.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmFileBase.vb">
      <Link>zDeviceFile\frmFileBase.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmPlistEdit.Designer.vb">
      <DependentUpon>frmPlistEdit.vb</DependentUpon>
      <Link>zDeviceFile\frmPlistEdit.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDeviceFile\frmPlistEdit.vb">
      <Link>zDeviceFile\frmPlistEdit.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmAppDownloaded.Designer.vb">
      <DependentUpon>frmAppDownloaded.vb</DependentUpon>
      <Link>zDownload\frmAppDownloaded.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmAppDownloaded.vb">
      <Link>zDownload\frmAppDownloaded.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloadAction.designer.vb">
      <DependentUpon>frmDownloadAction.vb</DependentUpon>
      <Link>zDownload\frmDownloadAction.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloadAction.vb">
      <Link>zDownload\frmDownloadAction.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloadCenter.designer.vb">
      <DependentUpon>frmDownloadCenter.vb</DependentUpon>
      <Link>zDownload\frmDownloadCenter.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloadCenter.vb">
      <Link>zDownload\frmDownloadCenter.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloading.designer.vb">
      <DependentUpon>frmDownloading.vb</DependentUpon>
      <Link>zDownload\frmDownloading.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloading.vb">
      <Link>zDownload\frmDownloading.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmSelectDevice.Designer.vb">
      <DependentUpon>frmSelectDevice.vb</DependentUpon>
      <Link>zDownload\frmSelectDevice.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmSelectDevice.vb">
      <Link>zDownload\frmSelectDevice.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmTip.Designer.vb">
      <DependentUpon>frmTip.vb</DependentUpon>
      <Link>zDownload\frmTip.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmTip.vb">
      <Link>zDownload\frmTip.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zLiveUpdate\LiveUpdateForm.Designer.vb">
      <DependentUpon>LiveUpdateForm.vb</DependentUpon>
      <Link>zLiveUpdate\LiveUpdateForm.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zLiveUpdate\LiveUpdateForm.vb">
      <Link>zLiveUpdate\LiveUpdateForm.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zLiveUpdate\LiveUpdateHelper.vb">
      <Link>zLiveUpdate\LiveUpdateHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\frmFileExist.designer.vb">
      <DependentUpon>frmFileExist.vb</DependentUpon>
      <Link>zMsgForm\frmFileExist.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\frmFileExist.vb">
      <Link>zMsgForm\frmFileExist.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\tbInputBox.vb">
      <Link>zMsgForm\tbInputBox.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\tbSplashBox.Designer.vb">
      <DependentUpon>tbSplashBox.vb</DependentUpon>
      <Link>zMsgForm\tbSplashBox.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zMsgForm\tbSplashBox.vb">
      <Link>zMsgForm\tbSplashBox.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\AuthorityWarningForm.designer.vb">
      <DependentUpon>AuthorityWarningForm.vb</DependentUpon>
      <Link>zSetting\AuthorityWarningForm.designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zSetting\AuthorityWarningForm.vb">
      <Link>zSetting\AuthorityWarningForm.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmAbout.Designer.vb">
      <DependentUpon>frmAbout.vb</DependentUpon>
      <Link>zSetting\frmAbout.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmAbout.vb">
      <Link>zSetting\frmAbout.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmAppExist.Designer.vb">
      <DependentUpon>frmAppExist.vb</DependentUpon>
      <Link>zSetting\frmAppExist.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmAppExist.vb">
      <Link>zSetting\frmAppExist.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmSettingV3.Designer.vb">
      <DependentUpon>frmSettingV3.vb</DependentUpon>
      <Link>zSetting\frmSettingV3.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zSetting\frmSettingV3.vb">
      <Link>zSetting\frmSettingV3.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zSetting\JBIniSetting.vb">
      <Link>zSetting\JBIniSetting.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zTools\frmTools.Designer.vb">
      <DependentUpon>frmTools.vb</DependentUpon>
      <Link>zTools\frmTools.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zTools\frmTools.vb">
      <Link>zTools\frmTools.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zTools\tbBackgroundWorker.vb">
      <SubType>Component</SubType>
      <Link>zTools\tbBackgroundWorker.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWebsite\frmSite.Designer.vb">
      <DependentUpon>frmSite.vb</DependentUpon>
      <Link>zWebsite\frmSite.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWebsite\frmSite.vb">
      <Link>zWebsite\frmSite.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmVerify.Designer.vb">
      <DependentUpon>frmVerify.vb</DependentUpon>
      <Link>zWeibo\frmVerify.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmVerify.vb">
      <Link>zWeibo\frmVerify.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmWeiBoLogin.Designer.vb">
      <DependentUpon>frmWeiBoLogin.vb</DependentUpon>
      <Link>zWeibo\frmWeiBoLogin.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmWeiBoLogin.vb">
      <Link>zWeibo\frmWeiBoLogin.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmWeiboSend.Designer.vb">
      <DependentUpon>frmWeiboSend.vb</DependentUpon>
      <Link>zWeibo\frmWeiboSend.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWeibo\frmWeiboSend.vb">
      <Link>zWeibo\frmWeiboSend.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWeibo\WeiboConfig.vb">
      <Link>zWeibo\WeiboConfig.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWeibo\WeiboHelper.vb">
      <Link>zWeibo\WeiboHelper.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmCommonWel.Designer.vb">
      <DependentUpon>frmCommonWel.vb</DependentUpon>
      <Link>zWelcome\frmCommonWel.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmCommonWel.vb">
      <Link>zWelcome\frmCommonWel.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmDetail.Designer.vb">
      <DependentUpon>frmDetail.vb</DependentUpon>
      <Link>zWelcome\frmDetail.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmDetail.vb">
      <Link>zWelcome\frmDetail.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmPreview.Designer.vb">
      <DependentUpon>frmPreview.vb</DependentUpon>
      <Link>zWelcome\frmPreview.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmPreview.vb">
      <Link>zWelcome\frmPreview.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmSummary.Designer.vb">
      <DependentUpon>frmSummary.vb</DependentUpon>
      <Link>zWelcome\frmSummary.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmSummary.vb">
      <Link>zWelcome\frmSummary.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmSummaryRecommend.Designer.vb">
      <DependentUpon>frmSummaryRecommend.vb</DependentUpon>
      <Link>zWelcome\frmSummaryRecommend.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmSummaryRecommend.vb">
      <Link>zWelcome\frmSummaryRecommend.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmTuiInstall.Designer.vb">
      <DependentUpon>frmTuiInstall.vb</DependentUpon>
      <Link>zWelcome\frmTuiInstall.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmTuiInstall.vb">
      <Link>zWelcome\frmTuiInstall.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmWelcome.Designer.vb">
      <DependentUpon>frmWelcome.vb</DependentUpon>
      <Link>zWelcome\frmWelcome.Designer.vb</Link>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmWelcome.vb">
      <Link>zWelcome\frmWelcome.vb</Link>
      <SubType>Form</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="..\iTong\zDownload\frmDownloaded.resx">
      <Link>zDownload\frmDownloaded.resx</Link>
      <DependentUpon>frmDownloaded.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zLogin\frmLogin.resx">
      <DependentUpon>frmLogin.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zLogin\frmLogin.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\frmSplash.resx">
      <DependentUpon>frmSplash.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmTuiInstallTutorial.resx">
      <Link>zWelcome\frmTuiInstallTutorial.resx</Link>
      <DependentUpon>frmTuiInstallTutorial.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\ProcForm.resx">
      <DependentUpon>ProcForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\MainForm.resx">
      <DependentUpon>MainForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="..\iTong\MainForm.Designer.vb">
      <DependentUpon>MainForm.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\MainForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloaded.designer.vb">
      <Link>zDownload\frmDownloaded.designer.vb</Link>
      <DependentUpon>frmDownloaded.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\zDownload\frmDownloaded.vb">
      <Link>zDownload\frmDownloaded.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmTuiInstallTutorial.designer.vb">
      <Link>zWelcome\frmTuiInstallTutorial.designer.vb</Link>
      <DependentUpon>frmTuiInstallTutorial.vb</DependentUpon>
    </Compile>
    <Compile Include="..\iTong\zWelcome\frmTuiInstallTutorial.vb">
      <Link>zWelcome\frmTuiInstallTutorial.vb</Link>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="..\iTong\zWelcome\SaleInfoHelper.vb">
      <Link>zWelcome\SaleInfoHelper.vb</Link>
    </Compile>
    <Compile Include="zDeviceAppWealth\AppleStoreHelper.vb" />
    <Compile Include="zDeviceAppWealth\AutoRunHelper.vb" />
    <Compile Include="zDeviceAppWealth\DeviceInfo.vb" />
    <Compile Include="zDeviceAppWealth\frmActivationHelp.Designer.vb">
      <DependentUpon>frmActivationHelp.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceAppWealth\frmActivationHelp.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceAppWealth\frmPop.Designer.vb">
      <DependentUpon>frmPop.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceAppWealth\frmPop.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceAppWealth\frmSetLocalPackageFolder.Designer.vb">
      <DependentUpon>frmSetLocalPackageFolder.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceAppWealth\frmSetLocalPackageFolder.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceAppWealth\frmUpdateWealth.Designer.vb">
      <DependentUpon>frmUpdateWealth.vb</DependentUpon>
    </Compile>
    <Compile Include="zDeviceAppWealth\frmUpdateWealth.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zDeviceAppWealth\LocalAppHelper.vb" />
    <Compile Include="zDeviceAppWealth\PersonSignatureHelper.vb" />
    <Compile Include="zDeviceAppWealth\RepairHelper.vb" />
    <Compile Include="zDeviceAppWealth\SoftInstallDB.vb" />
    <Compile Include="zDeviceAppWealth\StrongInstallHelper.vb" />
    <Compile Include="zDeviceAppWealth\WealthHelper.vb" />
    <Compile Include="zDeviceAppWealth\WealthLoginHelper.vb" />
    <Compile Include="zWelcome\frmSummaryZJ.designer.vb">
      <DependentUpon>frmSummaryZJ.vb</DependentUpon>
    </Compile>
    <Compile Include="zWelcome\frmSummaryZJ.vb">
      <SubType>Form</SubType>
    </Compile>
    <EmbeddedResource Include="..\iTong\zAndroidApp\frmAndroidDeviceApp.resx">
      <DependentUpon>frmAndroidDeviceApp.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidApp\frmAndroidDeviceApp.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidApp\frmAndroidLocalApp.resx">
      <DependentUpon>frmAndroidLocalApp.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidApp\frmAndroidLocalApp.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidApp\frmAndroidUpdate.resx">
      <DependentUpon>frmAndroidUpdate.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidApp\frmAndroidUpdate.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidApp\frmAndroidUpdateIgnore.resx">
      <DependentUpon>frmAndroidUpdateIgnore.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidApp\frmAndroidUpdateIgnore.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidFile\frmAndroidFile.resx">
      <DependentUpon>frmAndroidFile.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidFile\frmAndroidFile.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidFile\frmAndroidFileBase.resx">
      <DependentUpon>frmAndroidFileBase.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidFile\frmAndroidFileBase.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidFile\frmAndroidPlistEdit.resx">
      <DependentUpon>frmAndroidPlistEdit.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidFile\frmAndroidPlistEdit.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidDriver.resx">
      <DependentUpon>frmAndroidDriver.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidMsgForm\frmAndroidDriver.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidFeedback.resx">
      <DependentUpon>frmAndroidFeedback.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidMsgForm\frmAndroidFeedback.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidNoConnecting.resx">
      <DependentUpon>frmAndroidNoConnecting.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidMsgForm\frmAndroidNoConnecting.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidUninstallMsg.resx">
      <DependentUpon>frmAndroidUninstallMsg.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidMsgForm\frmAndroidUninstallMsg.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugBrowser.resx">
      <DependentUpon>frmAndroidUSBDebugBrowser.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidMsgForm\frmAndroidUSBDebugBrowser.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidMsgForm\frmAndroidUSBDebugSimple.resx">
      <DependentUpon>frmAndroidUSBDebugSimple.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidMsgForm\frmAndroidUSBDebugSimple.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidDetail.resx">
      <DependentUpon>frmAndroidDetail.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidWelcome\frmAndroidDetail.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidNoDevice.resx">
      <DependentUpon>frmAndroidNoDevice.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidWelcome\frmAndroidNoDevice.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidPreview.resx">
      <DependentUpon>frmAndroidPreview.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidWelcome\frmAndroidPreview.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidSummary.resx">
      <DependentUpon>frmAndroidSummary.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidWelcome\frmAndroidSummary.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroidWelcome\frmAndroidWelcome.resx">
      <DependentUpon>frmAndroidWelcome.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroidWelcome\frmAndroidWelcome.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zAndroid\frmAndroidBase.resx">
      <DependentUpon>frmAndroidBase.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zAndroid\frmAndroidBase.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zComponents\tbTextBoxPath.resx">
      <DependentUpon>tbTextBoxPath.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zComponents\tbTextBoxPath.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmApp.resx">
      <DependentUpon>frmApp.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmApp.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmAppDetail.resx">
      <DependentUpon>frmAppDetail.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmAppDetail.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmAppFilter.resx">
      <DependentUpon>frmAppFilter.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmAppFilter.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmCommonApp.resx">
      <DependentUpon>frmCommonApp.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmCommonApp.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmDeviceApp.resx">
      <DependentUpon>frmDeviceApp.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmDeviceApp.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmDeviceAppBackup.resx">
      <DependentUpon>frmDeviceAppBackup.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmDeviceAppBackup.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmiTunesLoginV3.resx">
      <DependentUpon>frmiTunesLoginV3.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmiTunesLoginV3.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmLocalApp.resx">
      <DependentUpon>frmLocalApp.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmLocalApp.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmUpdate.resx">
      <DependentUpon>frmUpdate.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmUpdate.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceApp\frmUpdateIgnore.resx">
      <DependentUpon>frmUpdateIgnore.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceApp\frmUpdateIgnore.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceFile\frmFile.resx">
      <DependentUpon>frmFile.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceFile\frmFile.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceFile\frmFileAppList.resx">
      <DependentUpon>frmFileAppList.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceFile\frmFileAppList.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceFile\frmFileBase.resx">
      <DependentUpon>frmFileBase.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceFile\frmFileBase.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDeviceFile\frmPlistEdit.resx">
      <DependentUpon>frmPlistEdit.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDeviceFile\frmPlistEdit.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmAppDownloaded.resx">
      <DependentUpon>frmAppDownloaded.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDownload\frmAppDownloaded.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmDownloadAction.resx">
      <DependentUpon>frmDownloadAction.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDownload\frmDownloadAction.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmDownloadCenter.resx">
      <DependentUpon>frmDownloadCenter.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDownload\frmDownloadCenter.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmDownloading.resx">
      <DependentUpon>frmDownloading.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDownload\frmDownloading.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmSelectDevice.resx">
      <DependentUpon>frmSelectDevice.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDownload\frmSelectDevice.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zDownload\frmTip.resx">
      <DependentUpon>frmTip.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zDownload\frmTip.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zLiveUpdate\LiveUpdateForm.resx">
      <DependentUpon>LiveUpdateForm.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zLiveUpdate\LiveUpdateForm.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zMsgForm\frmFileExist.resx">
      <DependentUpon>frmFileExist.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zMsgForm\frmFileExist.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zMsgForm\tbSplashBox.resx">
      <DependentUpon>tbSplashBox.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zMsgForm\tbSplashBox.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zSetting\AuthorityWarningForm.resx">
      <DependentUpon>AuthorityWarningForm.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zSetting\AuthorityWarningForm.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zSetting\frmAbout.resx">
      <DependentUpon>frmAbout.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zSetting\frmAbout.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zSetting\frmAppExist.resx">
      <DependentUpon>frmAppExist.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zSetting\frmAppExist.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zSetting\frmSettingV3.resx">
      <DependentUpon>frmSettingV3.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zSetting\frmSettingV3.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zTools\frmTools.resx">
      <DependentUpon>frmTools.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zTools\frmTools.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWebsite\frmSite.resx">
      <DependentUpon>frmSite.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWebsite\frmSite.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWeibo\frmVerify.resx">
      <DependentUpon>frmVerify.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWeibo\frmVerify.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWeibo\frmWeiBoLogin.resx">
      <DependentUpon>frmWeiBoLogin.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWeibo\frmWeiBoLogin.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWeibo\frmWeiboSend.resx">
      <DependentUpon>frmWeiboSend.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWeibo\frmWeiboSend.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmCommonWel.resx">
      <DependentUpon>frmCommonWel.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWelcome\frmCommonWel.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmDetail.resx">
      <DependentUpon>frmDetail.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWelcome\frmDetail.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmPreview.resx">
      <DependentUpon>frmPreview.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWelcome\frmPreview.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmSummary.resx">
      <DependentUpon>frmSummary.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWelcome\frmSummary.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmSummaryRecommend.resx">
      <DependentUpon>frmSummaryRecommend.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWelcome\frmSummaryRecommend.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmTuiInstall.resx">
      <DependentUpon>frmTuiInstall.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWelcome\frmTuiInstall.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="..\iTong\zWelcome\frmWelcome.resx">
      <DependentUpon>frmWelcome.vb</DependentUpon>
      <SubType>Designer</SubType>
      <Link>zWelcome\frmWelcome.resx</Link>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceAppWealth\frmActivationHelp.resx">
      <DependentUpon>frmActivationHelp.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceAppWealth\frmPop.resx">
      <DependentUpon>frmPop.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceAppWealth\frmSetLocalPackageFolder.resx">
      <SubType>Designer</SubType>
      <DependentUpon>frmSetLocalPackageFolder.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zDeviceAppWealth\frmUpdateWealth.resx">
      <DependentUpon>frmUpdateWealth.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="zWelcome\frmSummaryZJ.resx">
      <DependentUpon>frmSummaryZJ.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="Resources\ipa_AGSPlan.ipa" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android.csproj">
      <Project>{898E7D3C-7ED3-4BD4-A668-5FBFB5C3F3AB}</Project>
      <Name>Android</Name>
    </ProjectReference>
    <ProjectReference Include="..\Components\Components.vbproj">
      <Project>{28459798-5E28-45D3-AAE3-74A9F1761D1A}</Project>
      <Name>Components</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc.csproj">
      <Project>{685AE51B-3C37-4B37-B3A2-B485D07A6E6B}</Project>
      <Name>CoreMisc</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreModuleEx\CoreModuleCS.csproj">
      <Project>{608E58D6-914F-45D2-A3A6-55CEC62EF261}</Project>
      <Name>CoreModuleCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses.csproj">
      <Project>{65CE9103-521A-49E6-A8CD-89137B452842}</Project>
      <Name>CoreReses</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUpdate\CoreUpdate.csproj">
      <Project>{998A7F44-F7AF-4E25-9EF3-696834EE6242}</Project>
      <Name>CoreUpdate</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS.csproj">
      <Project>{61c62ea1-6ad7-41be-98db-8abb77ee35f3}</Project>
      <Name>CoreUtilCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil.vbproj">
      <Project>{718B167F-1B33-4B7C-A7E3-E15615DDAAC4}</Project>
      <Name>CoreUtil</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone.csproj">
      <Project>{6691EA4B-1EDB-4330-A3A3-E1D47F4D35BE}</Project>
      <Name>iPhone</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\ICSharpCode.SharpZLib.csproj">
      <Project>{0E7413FF-EB9E-4714-ACF2-BE3A6A7B2FFD}</Project>
      <Name>ICSharpCode.SharpZLib</Name>
    </ProjectReference>
    <ProjectReference Include="..\tbFlashHelper\tbFlashHelper.vbproj">
      <Project>{1343347B-ED98-4A99-B9AA-54D10E7C7708}</Project>
      <Name>tbFlashHelper</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\html_pop" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\progress_bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\player_progress_bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\player_progress_play.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="..\iTong\Lang\zh-CN.lang">
      <Link>Lang\zh-CN.lang</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pic_login.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new_top_activation.png" />
    <None Include="Resources\new_top_activation_light.png" />
    <None Include="Resources\new_top_data_light.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\new_top_data.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\tvw_zjnewdervice_2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\tvw_zjzhuanqu_2.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\notify_bg1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_openlog.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\app_bg_btn_msg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_copy.png" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>