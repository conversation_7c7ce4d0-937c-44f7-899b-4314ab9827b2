﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSummaryZJ
    Inherits frmDeviceBase

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSummaryZJ))
        Me.btnDisconnect = New iTong.Components.tbButton
        Me.lblDownloadProgress = New iTong.Components.tbLabel
        Me.pnlChangeDeviceName = New iTong.Components.tbPanel
        Me.btnChangeCancel = New iTong.Components.tbButton
        Me.btnChangeOK = New iTong.Components.tbButton
        Me.txtNameValue = New iTong.Components.tbTextBox
        Me.pbDevice = New System.Windows.Forms.PictureBox
        Me.pnlScreenshot = New iTong.Components.tbPanel
        Me.btnScreenshotSetUp = New iTong.Components.tbButton
        Me.btnScreenshotPlay = New iTong.Components.tbButton
        Me.btnScreenshot = New iTong.Components.tbButton
        Me.btnScreenshotWeiBo = New iTong.Components.tbButton
        Me.btn_EntryRecovery = New iTong.Components.tbButton
        Me.btnRename = New iTong.Components.tbButton
        Me.btnRefresh = New iTong.Components.tbButton
        Me.lblNameValue = New iTong.Components.tbLabel
        Me.cmsSetting = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiScreenshot = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiScreenshotShell = New System.Windows.Forms.ToolStripMenuItem
        Me.btnJailbreak = New iTong.Components.tbButton
        Me.lblData = New System.Windows.Forms.Label
        Me.lblSystem = New System.Windows.Forms.Label
        Me.lblKeyActive = New System.Windows.Forms.Label
        Me.lblActive = New System.Windows.Forms.Label
        Me.lblKeyJB = New System.Windows.Forms.Label
        Me.lblJB = New System.Windows.Forms.Label
        Me.lblKeyProduct = New System.Windows.Forms.Label
        Me.lblProduct = New System.Windows.Forms.Label
        Me.lblKeyColor = New System.Windows.Forms.Label
        Me.lblColor = New System.Windows.Forms.Label
        Me.lblKeySN = New System.Windows.Forms.Label
        Me.lblSN = New System.Windows.Forms.Label
        Me.lblKeyMode = New System.Windows.Forms.Label
        Me.lblMode = New System.Windows.Forms.Label
        Me.lblKeyFM = New System.Windows.Forms.Label
        Me.lblFM = New System.Windows.Forms.Label
        Me.lblKeyPN = New System.Windows.Forms.Label
        Me.lblPN = New System.Windows.Forms.Label
        Me.pnlDetail = New System.Windows.Forms.Panel
        Me.btnAgain = New iTong.Components.tbButton
        Me.btnFindMyPhone = New iTong.Components.tbButton
        Me.btnCopyIMEI = New iTong.Components.tbButton
        Me.picLoading = New System.Windows.Forms.PictureBox
        Me.lblCopyInfo = New iTong.Components.tbLabel
        Me.btnCopySN = New iTong.Components.tbButton
        Me.lblIMEI = New System.Windows.Forms.Label
        Me.lblKeyIMEI = New System.Windows.Forms.Label
        Me.lblBlueAddress = New System.Windows.Forms.Label
        Me.lblWifi = New System.Windows.Forms.Label
        Me.lblKeyBlueAddress = New System.Windows.Forms.Label
        Me.lblKeyWifi = New System.Windows.Forms.Label
        Me.lblDataUsed = New System.Windows.Forms.Label
        Me.lblSystemUsed = New System.Windows.Forms.Label
        Me.lblKeyBaseband = New System.Windows.Forms.Label
        Me.lblKeySaleArea = New System.Windows.Forms.Label
        Me.lblKeyExpireDate = New System.Windows.Forms.Label
        Me.lblKeylBuyDate = New System.Windows.Forms.Label
        Me.pgbData = New iTong.Components.tbControlBar
        Me.pgbSystem = New iTong.Components.tbControlBar
        Me.lblBaseband = New System.Windows.Forms.Label
        Me.lblFindMyPhone = New System.Windows.Forms.Label
        Me.lblSaleArea = New System.Windows.Forms.Label
        Me.lblExpireDate = New System.Windows.Forms.Label
        Me.lblBuyDate = New System.Windows.Forms.Label
        Me.lblUDID = New System.Windows.Forms.Label
        Me.lblKeyUDID = New System.Windows.Forms.Label
        Me.lblKeyFindMyPhone = New System.Windows.Forms.Label
        Me.btnDetail = New iTong.Components.tbButton
        Me.tmrShowCopyInfo = New System.Windows.Forms.Timer(Me.components)
        Me.lblCharging = New System.Windows.Forms.Label
        Me.picCharging = New System.Windows.Forms.PictureBox
        Me.tmrRefresh = New System.Windows.Forms.Timer(Me.components)
        Me.bgwStartJailbreak = New System.ComponentModel.BackgroundWorker
        Me.pnlChangeDeviceName.SuspendLayout()
        CType(Me.pbDevice, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlScreenshot.SuspendLayout()
        Me.cmsSetting.SuspendLayout()
        Me.pnlDetail.SuspendLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picCharging, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(884, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(860, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(836, 0)
        '
        'btnDisconnect
        '
        Me.btnDisconnect.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnDisconnect.BackColor = System.Drawing.Color.Transparent
        Me.btnDisconnect.BindingForm = Nothing
        Me.btnDisconnect.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDisconnect.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDisconnect.Location = New System.Drawing.Point(609, 16)
        Me.btnDisconnect.Name = "btnDisconnect"
        Me.btnDisconnect.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDisconnect.Selectable = True
        Me.btnDisconnect.Size = New System.Drawing.Size(15, 15)
        Me.btnDisconnect.TabIndex = 16
        Me.btnDisconnect.TabStop = False
        Me.btnDisconnect.tbAdriftIconWhenHover = False
        Me.btnDisconnect.tbAutoSize = False
        Me.btnDisconnect.tbAutoSizeEx = False
        Me.btnDisconnect.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_3_disconnect
        Me.btnDisconnect.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnDisconnect.tbBadgeNumber = 0
        Me.btnDisconnect.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDisconnect.tbEndEllipsis = False
        Me.btnDisconnect.tbIconHoldPlace = True
        Me.btnDisconnect.tbIconImage = Nothing
        Me.btnDisconnect.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDisconnect.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDisconnect.tbIconMore = False
        Me.btnDisconnect.tbIconMouseDown = Nothing
        Me.btnDisconnect.tbIconMouseHover = Nothing
        Me.btnDisconnect.tbIconMouseLeave = Nothing
        Me.btnDisconnect.tbIconPlaceText = 2
        Me.btnDisconnect.tbIconReadOnly = Nothing
        Me.btnDisconnect.tbImageMouseDown = Nothing
        Me.btnDisconnect.tbImageMouseHover = Nothing
        Me.btnDisconnect.tbImageMouseLeave = Nothing
        Me.btnDisconnect.tbReadOnly = False
        Me.btnDisconnect.tbReadOnlyText = False
        Me.btnDisconnect.tbShadow = False
        Me.btnDisconnect.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDisconnect.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDisconnect.tbShowDot = False
        Me.btnDisconnect.tbShowNew = False
        Me.btnDisconnect.tbShowToolTipOnButton = False
        Me.btnDisconnect.tbSplit = "0,0,0,0"
        Me.btnDisconnect.tbText = ""
        Me.btnDisconnect.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDisconnect.tbTextColor = System.Drawing.Color.Black
        Me.btnDisconnect.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnDisconnect.tbTextColorDown = System.Drawing.Color.Black
        Me.btnDisconnect.tbTextColorHover = System.Drawing.Color.Black
        Me.btnDisconnect.tbTextMouseDownPlace = 0
        Me.btnDisconnect.tbToolTip = ""
        Me.btnDisconnect.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDisconnect.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDisconnect.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDisconnect.VisibleEx = True
        '
        'lblDownloadProgress
        '
        Me.lblDownloadProgress.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDownloadProgress.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDownloadProgress.ForeColor = System.Drawing.SystemColors.ControlDarkDark
        Me.lblDownloadProgress.Location = New System.Drawing.Point(68, 450)
        Me.lblDownloadProgress.Name = "lblDownloadProgress"
        Me.lblDownloadProgress.Size = New System.Drawing.Size(216, 17)
        Me.lblDownloadProgress.TabIndex = 6
        Me.lblDownloadProgress.tbAdriftWhenHover = False
        Me.lblDownloadProgress.tbAutoEllipsis = False
        Me.lblDownloadProgress.tbAutoSize = False
        Me.lblDownloadProgress.tbHideImage = False
        Me.lblDownloadProgress.tbIconImage = Nothing
        Me.lblDownloadProgress.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDownloadProgress.tbIconPlaceText = 5
        Me.lblDownloadProgress.tbShadow = False
        Me.lblDownloadProgress.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDownloadProgress.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDownloadProgress.tbShowScrolling = False
        Me.lblDownloadProgress.Text = "正在下载插件： 10%"
        Me.lblDownloadProgress.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDownloadProgress.Visible = False
        '
        'pnlChangeDeviceName
        '
        Me.pnlChangeDeviceName.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlChangeDeviceName.Controls.Add(Me.btnChangeCancel)
        Me.pnlChangeDeviceName.Controls.Add(Me.btnChangeOK)
        Me.pnlChangeDeviceName.Controls.Add(Me.txtNameValue)
        Me.pnlChangeDeviceName.Location = New System.Drawing.Point(319, 1)
        Me.pnlChangeDeviceName.Name = "pnlChangeDeviceName"
        Me.pnlChangeDeviceName.Size = New System.Drawing.Size(226, 34)
        Me.pnlChangeDeviceName.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlChangeDeviceName.TabIndex = 14
        Me.pnlChangeDeviceName.tbBackgroundImage = Nothing
        Me.pnlChangeDeviceName.tbShowWatermark = False
        Me.pnlChangeDeviceName.tbSplit = "0,0,0,0"
        Me.pnlChangeDeviceName.tbWatermark = Nothing
        Me.pnlChangeDeviceName.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlChangeDeviceName.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlChangeDeviceName.Visible = False
        '
        'btnChangeCancel
        '
        Me.btnChangeCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnChangeCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnChangeCancel.BindingForm = Nothing
        Me.btnChangeCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnChangeCancel.Location = New System.Drawing.Point(199, 0)
        Me.btnChangeCancel.Margin = New System.Windows.Forms.Padding(0)
        Me.btnChangeCancel.Name = "btnChangeCancel"
        Me.btnChangeCancel.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnChangeCancel.Selectable = True
        Me.btnChangeCancel.Size = New System.Drawing.Size(25, 24)
        Me.btnChangeCancel.TabIndex = 0
        Me.btnChangeCancel.TabStop = False
        Me.btnChangeCancel.tbAdriftIconWhenHover = False
        Me.btnChangeCancel.tbAutoSize = False
        Me.btnChangeCancel.tbAutoSizeEx = False
        Me.btnChangeCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_4_close
        Me.btnChangeCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnChangeCancel.tbBadgeNumber = 0
        Me.btnChangeCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnChangeCancel.tbEndEllipsis = False
        Me.btnChangeCancel.tbIconHoldPlace = True
        Me.btnChangeCancel.tbIconImage = Nothing
        Me.btnChangeCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnChangeCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnChangeCancel.tbIconMore = False
        Me.btnChangeCancel.tbIconMouseDown = Nothing
        Me.btnChangeCancel.tbIconMouseHover = Nothing
        Me.btnChangeCancel.tbIconMouseLeave = Nothing
        Me.btnChangeCancel.tbIconPlaceText = 2
        Me.btnChangeCancel.tbIconReadOnly = Nothing
        Me.btnChangeCancel.tbImageMouseDown = Nothing
        Me.btnChangeCancel.tbImageMouseHover = Nothing
        Me.btnChangeCancel.tbImageMouseLeave = Nothing
        Me.btnChangeCancel.tbReadOnly = False
        Me.btnChangeCancel.tbReadOnlyText = False
        Me.btnChangeCancel.tbShadow = False
        Me.btnChangeCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnChangeCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnChangeCancel.tbShowDot = False
        Me.btnChangeCancel.tbShowNew = False
        Me.btnChangeCancel.tbShowToolTipOnButton = False
        Me.btnChangeCancel.tbSplit = "0,0,0,0"
        Me.btnChangeCancel.tbText = ""
        Me.btnChangeCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnChangeCancel.tbTextColor = System.Drawing.Color.Black
        Me.btnChangeCancel.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnChangeCancel.tbTextColorDown = System.Drawing.Color.Black
        Me.btnChangeCancel.tbTextColorHover = System.Drawing.Color.Black
        Me.btnChangeCancel.tbTextMouseDownPlace = 0
        Me.btnChangeCancel.tbToolTip = ""
        Me.btnChangeCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnChangeCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnChangeCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnChangeCancel.Visible = False
        Me.btnChangeCancel.VisibleEx = True
        '
        'btnChangeOK
        '
        Me.btnChangeOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnChangeOK.BackColor = System.Drawing.Color.Transparent
        Me.btnChangeOK.BindingForm = Nothing
        Me.btnChangeOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnChangeOK.Location = New System.Drawing.Point(175, 0)
        Me.btnChangeOK.Margin = New System.Windows.Forms.Padding(0)
        Me.btnChangeOK.Name = "btnChangeOK"
        Me.btnChangeOK.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnChangeOK.Selectable = True
        Me.btnChangeOK.Size = New System.Drawing.Size(25, 24)
        Me.btnChangeOK.TabIndex = 1
        Me.btnChangeOK.TabStop = False
        Me.btnChangeOK.tbAdriftIconWhenHover = False
        Me.btnChangeOK.tbAutoSize = False
        Me.btnChangeOK.tbAutoSizeEx = False
        Me.btnChangeOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_4_ok
        Me.btnChangeOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnChangeOK.tbBadgeNumber = 0
        Me.btnChangeOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnChangeOK.tbEndEllipsis = False
        Me.btnChangeOK.tbIconHoldPlace = True
        Me.btnChangeOK.tbIconImage = Nothing
        Me.btnChangeOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnChangeOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnChangeOK.tbIconMore = False
        Me.btnChangeOK.tbIconMouseDown = Nothing
        Me.btnChangeOK.tbIconMouseHover = Nothing
        Me.btnChangeOK.tbIconMouseLeave = Nothing
        Me.btnChangeOK.tbIconPlaceText = 2
        Me.btnChangeOK.tbIconReadOnly = Nothing
        Me.btnChangeOK.tbImageMouseDown = Nothing
        Me.btnChangeOK.tbImageMouseHover = Nothing
        Me.btnChangeOK.tbImageMouseLeave = Nothing
        Me.btnChangeOK.tbReadOnly = False
        Me.btnChangeOK.tbReadOnlyText = False
        Me.btnChangeOK.tbShadow = False
        Me.btnChangeOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnChangeOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnChangeOK.tbShowDot = False
        Me.btnChangeOK.tbShowNew = False
        Me.btnChangeOK.tbShowToolTipOnButton = False
        Me.btnChangeOK.tbSplit = "0,0,0,0"
        Me.btnChangeOK.tbText = ""
        Me.btnChangeOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnChangeOK.tbTextColor = System.Drawing.Color.Black
        Me.btnChangeOK.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnChangeOK.tbTextColorDown = System.Drawing.Color.Black
        Me.btnChangeOK.tbTextColorHover = System.Drawing.Color.Black
        Me.btnChangeOK.tbTextMouseDownPlace = 0
        Me.btnChangeOK.tbToolTip = ""
        Me.btnChangeOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnChangeOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnChangeOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnChangeOK.Visible = False
        Me.btnChangeOK.VisibleEx = True
        '
        'txtNameValue
        '
        Me.txtNameValue.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtNameValue.BackColor = System.Drawing.Color.White
        Me.txtNameValue.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtNameValue.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtNameValue.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtNameValue.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtNameValue.Font = New System.Drawing.Font("宋体", 15.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.txtNameValue.ForeColor = System.Drawing.Color.Black
        Me.txtNameValue.ImeMode = System.Windows.Forms.ImeMode.Hangul
        Me.txtNameValue.Location = New System.Drawing.Point(0, 0)
        Me.txtNameValue.Margin = New System.Windows.Forms.Padding(0)
        Me.txtNameValue.MaxLength = 50
        Me.txtNameValue.Name = "txtNameValue"
        Me.txtNameValue.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtNameValue.Size = New System.Drawing.Size(225, 33)
        Me.txtNameValue.TabIndex = 0
        Me.txtNameValue.Tag = Nothing
        Me.txtNameValue.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtNameValue.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtNameValue.tbSelMark = True
        Me.txtNameValue.tbTextBind = "设备名称"
        Me.txtNameValue.Text = "设备名称"
        Me.txtNameValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtNameValue.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtNameValue.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNameValue.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtNameValue.TextTip = ""
        '
        'pbDevice
        '
        Me.pbDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbDevice.Image = Global.iTong.My.Resources.Resources.device_default
        Me.pbDevice.Location = New System.Drawing.Point(56, 36)
        Me.pbDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.pbDevice.Name = "pbDevice"
        Me.pbDevice.Size = New System.Drawing.Size(240, 321)
        Me.pbDevice.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.pbDevice.TabIndex = 0
        Me.pbDevice.TabStop = False
        '
        'pnlScreenshot
        '
        Me.pnlScreenshot.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshotSetUp)
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshotPlay)
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshot)
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshotWeiBo)
        Me.pnlScreenshot.Location = New System.Drawing.Point(117, 410)
        Me.pnlScreenshot.Name = "pnlScreenshot"
        Me.pnlScreenshot.Size = New System.Drawing.Size(123, 24)
        Me.pnlScreenshot.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlScreenshot.TabIndex = 7
        Me.pnlScreenshot.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_nav_bg
        Me.pnlScreenshot.tbShowWatermark = False
        Me.pnlScreenshot.tbSplit = "5,5,5,5"
        Me.pnlScreenshot.tbWatermark = Nothing
        Me.pnlScreenshot.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlScreenshot.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnScreenshotSetUp
        '
        Me.btnScreenshotSetUp.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnScreenshotSetUp.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshotSetUp.BindingForm = Nothing
        Me.btnScreenshotSetUp.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshotSetUp.Location = New System.Drawing.Point(91, 3)
        Me.btnScreenshotSetUp.Name = "btnScreenshotSetUp"
        Me.btnScreenshotSetUp.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshotSetUp.Selectable = True
        Me.btnScreenshotSetUp.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshotSetUp.TabIndex = 3
        Me.btnScreenshotSetUp.TabStop = False
        Me.btnScreenshotSetUp.tbAdriftIconWhenHover = False
        Me.btnScreenshotSetUp.tbAutoSize = False
        Me.btnScreenshotSetUp.tbAutoSizeEx = False
        Me.btnScreenshotSetUp.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_setting
        Me.btnScreenshotSetUp.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnScreenshotSetUp.tbBadgeNumber = 0
        Me.btnScreenshotSetUp.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshotSetUp.tbEndEllipsis = False
        Me.btnScreenshotSetUp.tbIconHoldPlace = True
        Me.btnScreenshotSetUp.tbIconImage = Nothing
        Me.btnScreenshotSetUp.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshotSetUp.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshotSetUp.tbIconMore = False
        Me.btnScreenshotSetUp.tbIconMouseDown = Nothing
        Me.btnScreenshotSetUp.tbIconMouseHover = Nothing
        Me.btnScreenshotSetUp.tbIconMouseLeave = Nothing
        Me.btnScreenshotSetUp.tbIconPlaceText = 2
        Me.btnScreenshotSetUp.tbIconReadOnly = Nothing
        Me.btnScreenshotSetUp.tbImageMouseDown = Nothing
        Me.btnScreenshotSetUp.tbImageMouseHover = Nothing
        Me.btnScreenshotSetUp.tbImageMouseLeave = Nothing
        Me.btnScreenshotSetUp.tbReadOnly = False
        Me.btnScreenshotSetUp.tbReadOnlyText = False
        Me.btnScreenshotSetUp.tbShadow = False
        Me.btnScreenshotSetUp.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshotSetUp.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshotSetUp.tbShowDot = False
        Me.btnScreenshotSetUp.tbShowNew = False
        Me.btnScreenshotSetUp.tbShowToolTipOnButton = False
        Me.btnScreenshotSetUp.tbSplit = "0,0,0,0"
        Me.btnScreenshotSetUp.tbText = ""
        Me.btnScreenshotSetUp.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotSetUp.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextMouseDownPlace = 0
        Me.btnScreenshotSetUp.tbToolTip = ""
        Me.btnScreenshotSetUp.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshotSetUp.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshotSetUp.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotSetUp.VisibleEx = True
        '
        'btnScreenshotPlay
        '
        Me.btnScreenshotPlay.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.btnScreenshotPlay.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshotPlay.BindingForm = Nothing
        Me.btnScreenshotPlay.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshotPlay.Location = New System.Drawing.Point(52, 3)
        Me.btnScreenshotPlay.Name = "btnScreenshotPlay"
        Me.btnScreenshotPlay.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshotPlay.Selectable = True
        Me.btnScreenshotPlay.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshotPlay.TabIndex = 2
        Me.btnScreenshotPlay.TabStop = False
        Me.btnScreenshotPlay.tbAdriftIconWhenHover = False
        Me.btnScreenshotPlay.tbAutoSize = False
        Me.btnScreenshotPlay.tbAutoSizeEx = False
        Me.btnScreenshotPlay.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_fullScreen
        Me.btnScreenshotPlay.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnScreenshotPlay.tbBadgeNumber = 0
        Me.btnScreenshotPlay.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshotPlay.tbEndEllipsis = False
        Me.btnScreenshotPlay.tbIconHoldPlace = True
        Me.btnScreenshotPlay.tbIconImage = Nothing
        Me.btnScreenshotPlay.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshotPlay.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshotPlay.tbIconMore = False
        Me.btnScreenshotPlay.tbIconMouseDown = Nothing
        Me.btnScreenshotPlay.tbIconMouseHover = Nothing
        Me.btnScreenshotPlay.tbIconMouseLeave = Nothing
        Me.btnScreenshotPlay.tbIconPlaceText = 2
        Me.btnScreenshotPlay.tbIconReadOnly = Nothing
        Me.btnScreenshotPlay.tbImageMouseDown = Nothing
        Me.btnScreenshotPlay.tbImageMouseHover = Nothing
        Me.btnScreenshotPlay.tbImageMouseLeave = Nothing
        Me.btnScreenshotPlay.tbReadOnly = False
        Me.btnScreenshotPlay.tbReadOnlyText = False
        Me.btnScreenshotPlay.tbShadow = False
        Me.btnScreenshotPlay.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshotPlay.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshotPlay.tbShowDot = False
        Me.btnScreenshotPlay.tbShowNew = False
        Me.btnScreenshotPlay.tbShowToolTipOnButton = False
        Me.btnScreenshotPlay.tbSplit = "0,0,0,0"
        Me.btnScreenshotPlay.tbText = ""
        Me.btnScreenshotPlay.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotPlay.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshotPlay.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshotPlay.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshotPlay.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshotPlay.tbTextMouseDownPlace = 0
        Me.btnScreenshotPlay.tbToolTip = ""
        Me.btnScreenshotPlay.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshotPlay.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshotPlay.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotPlay.VisibleEx = True
        '
        'btnScreenshot
        '
        Me.btnScreenshot.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshot.BindingForm = Nothing
        Me.btnScreenshot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshot.Location = New System.Drawing.Point(14, 3)
        Me.btnScreenshot.Name = "btnScreenshot"
        Me.btnScreenshot.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshot.Selectable = True
        Me.btnScreenshot.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshot.TabIndex = 0
        Me.btnScreenshot.TabStop = False
        Me.btnScreenshot.tbAdriftIconWhenHover = False
        Me.btnScreenshot.tbAutoSize = False
        Me.btnScreenshot.tbAutoSizeEx = False
        Me.btnScreenshot.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_screenshot
        Me.btnScreenshot.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnScreenshot.tbBadgeNumber = 0
        Me.btnScreenshot.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshot.tbEndEllipsis = False
        Me.btnScreenshot.tbIconHoldPlace = True
        Me.btnScreenshot.tbIconImage = Nothing
        Me.btnScreenshot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshot.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshot.tbIconMore = False
        Me.btnScreenshot.tbIconMouseDown = Nothing
        Me.btnScreenshot.tbIconMouseHover = Nothing
        Me.btnScreenshot.tbIconMouseLeave = Nothing
        Me.btnScreenshot.tbIconPlaceText = 2
        Me.btnScreenshot.tbIconReadOnly = Nothing
        Me.btnScreenshot.tbImageMouseDown = Nothing
        Me.btnScreenshot.tbImageMouseHover = Nothing
        Me.btnScreenshot.tbImageMouseLeave = Nothing
        Me.btnScreenshot.tbReadOnly = False
        Me.btnScreenshot.tbReadOnlyText = False
        Me.btnScreenshot.tbShadow = False
        Me.btnScreenshot.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshot.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshot.tbShowDot = False
        Me.btnScreenshot.tbShowNew = False
        Me.btnScreenshot.tbShowToolTipOnButton = False
        Me.btnScreenshot.tbSplit = "0,0,0,0"
        Me.btnScreenshot.tbText = ""
        Me.btnScreenshot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshot.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextMouseDownPlace = 0
        Me.btnScreenshot.tbToolTip = ""
        Me.btnScreenshot.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshot.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshot.VisibleEx = True
        '
        'btnScreenshotWeiBo
        '
        Me.btnScreenshotWeiBo.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnScreenshotWeiBo.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshotWeiBo.BindingForm = Nothing
        Me.btnScreenshotWeiBo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshotWeiBo.Location = New System.Drawing.Point(16, 5)
        Me.btnScreenshotWeiBo.Name = "btnScreenshotWeiBo"
        Me.btnScreenshotWeiBo.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshotWeiBo.Selectable = True
        Me.btnScreenshotWeiBo.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshotWeiBo.TabIndex = 1
        Me.btnScreenshotWeiBo.TabStop = False
        Me.btnScreenshotWeiBo.tbAdriftIconWhenHover = False
        Me.btnScreenshotWeiBo.tbAutoSize = False
        Me.btnScreenshotWeiBo.tbAutoSizeEx = False
        Me.btnScreenshotWeiBo.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_sharing
        Me.btnScreenshotWeiBo.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnScreenshotWeiBo.tbBadgeNumber = 0
        Me.btnScreenshotWeiBo.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshotWeiBo.tbEndEllipsis = False
        Me.btnScreenshotWeiBo.tbIconHoldPlace = True
        Me.btnScreenshotWeiBo.tbIconImage = Nothing
        Me.btnScreenshotWeiBo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshotWeiBo.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshotWeiBo.tbIconMore = False
        Me.btnScreenshotWeiBo.tbIconMouseDown = Nothing
        Me.btnScreenshotWeiBo.tbIconMouseHover = Nothing
        Me.btnScreenshotWeiBo.tbIconMouseLeave = Nothing
        Me.btnScreenshotWeiBo.tbIconPlaceText = 2
        Me.btnScreenshotWeiBo.tbIconReadOnly = Nothing
        Me.btnScreenshotWeiBo.tbImageMouseDown = Nothing
        Me.btnScreenshotWeiBo.tbImageMouseHover = Nothing
        Me.btnScreenshotWeiBo.tbImageMouseLeave = Nothing
        Me.btnScreenshotWeiBo.tbReadOnly = False
        Me.btnScreenshotWeiBo.tbReadOnlyText = False
        Me.btnScreenshotWeiBo.tbShadow = False
        Me.btnScreenshotWeiBo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshotWeiBo.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshotWeiBo.tbShowDot = False
        Me.btnScreenshotWeiBo.tbShowNew = False
        Me.btnScreenshotWeiBo.tbShowToolTipOnButton = False
        Me.btnScreenshotWeiBo.tbSplit = "0,0,0,0"
        Me.btnScreenshotWeiBo.tbText = ""
        Me.btnScreenshotWeiBo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotWeiBo.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshotWeiBo.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshotWeiBo.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshotWeiBo.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshotWeiBo.tbTextMouseDownPlace = 0
        Me.btnScreenshotWeiBo.tbToolTip = ""
        Me.btnScreenshotWeiBo.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshotWeiBo.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshotWeiBo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotWeiBo.Visible = False
        Me.btnScreenshotWeiBo.VisibleEx = True
        '
        'btn_EntryRecovery
        '
        Me.btn_EntryRecovery.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btn_EntryRecovery.BackColor = System.Drawing.Color.Transparent
        Me.btn_EntryRecovery.BindingForm = Nothing
        Me.btn_EntryRecovery.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_EntryRecovery.Location = New System.Drawing.Point(109, 410)
        Me.btn_EntryRecovery.Name = "btn_EntryRecovery"
        Me.btn_EntryRecovery.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btn_EntryRecovery.Selectable = True
        Me.btn_EntryRecovery.Size = New System.Drawing.Size(134, 25)
        Me.btn_EntryRecovery.TabIndex = 8
        Me.btn_EntryRecovery.TabStop = False
        Me.btn_EntryRecovery.tbAdriftIconWhenHover = False
        Me.btn_EntryRecovery.tbAutoSize = False
        Me.btn_EntryRecovery.tbAutoSizeEx = False
        Me.btn_EntryRecovery.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btn_EntryRecovery.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btn_EntryRecovery.tbBadgeNumber = 0
        Me.btn_EntryRecovery.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_EntryRecovery.tbEndEllipsis = False
        Me.btn_EntryRecovery.tbIconHoldPlace = True
        Me.btn_EntryRecovery.tbIconImage = Nothing
        Me.btn_EntryRecovery.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btn_EntryRecovery.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_EntryRecovery.tbIconMore = False
        Me.btn_EntryRecovery.tbIconMouseDown = Nothing
        Me.btn_EntryRecovery.tbIconMouseHover = Nothing
        Me.btn_EntryRecovery.tbIconMouseLeave = Nothing
        Me.btn_EntryRecovery.tbIconPlaceText = 2
        Me.btn_EntryRecovery.tbIconReadOnly = Nothing
        Me.btn_EntryRecovery.tbImageMouseDown = Nothing
        Me.btn_EntryRecovery.tbImageMouseHover = Nothing
        Me.btn_EntryRecovery.tbImageMouseLeave = Nothing
        Me.btn_EntryRecovery.tbReadOnly = False
        Me.btn_EntryRecovery.tbReadOnlyText = False
        Me.btn_EntryRecovery.tbShadow = False
        Me.btn_EntryRecovery.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btn_EntryRecovery.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btn_EntryRecovery.tbShowDot = False
        Me.btn_EntryRecovery.tbShowNew = False
        Me.btn_EntryRecovery.tbShowToolTipOnButton = False
        Me.btn_EntryRecovery.tbSplit = "13,11,13,11"
        Me.btn_EntryRecovery.tbText = "进入恢复模式"
        Me.btn_EntryRecovery.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_EntryRecovery.tbTextColor = System.Drawing.Color.Black
        Me.btn_EntryRecovery.tbTextColorDisable = System.Drawing.Color.Black
        Me.btn_EntryRecovery.tbTextColorDown = System.Drawing.Color.Black
        Me.btn_EntryRecovery.tbTextColorHover = System.Drawing.Color.Black
        Me.btn_EntryRecovery.tbTextMouseDownPlace = 0
        Me.btn_EntryRecovery.tbToolTip = ""
        Me.btn_EntryRecovery.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_EntryRecovery.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_EntryRecovery.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_EntryRecovery.VisibleEx = True
        '
        'btnRename
        '
        Me.btnRename.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnRename.BackColor = System.Drawing.Color.Transparent
        Me.btnRename.BindingForm = Nothing
        Me.btnRename.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnRename.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRename.Location = New System.Drawing.Point(563, 16)
        Me.btnRename.Name = "btnRename"
        Me.btnRename.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRename.Selectable = True
        Me.btnRename.Size = New System.Drawing.Size(15, 15)
        Me.btnRename.TabIndex = 15
        Me.btnRename.TabStop = False
        Me.btnRename.tbAdriftIconWhenHover = False
        Me.btnRename.tbAutoSize = True
        Me.btnRename.tbAutoSizeEx = False
        Me.btnRename.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_3_edit
        Me.btnRename.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnRename.tbBadgeNumber = 0
        Me.btnRename.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRename.tbEndEllipsis = False
        Me.btnRename.tbIconHoldPlace = True
        Me.btnRename.tbIconImage = Nothing
        Me.btnRename.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRename.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRename.tbIconMore = False
        Me.btnRename.tbIconMouseDown = Nothing
        Me.btnRename.tbIconMouseHover = Nothing
        Me.btnRename.tbIconMouseLeave = Nothing
        Me.btnRename.tbIconPlaceText = 2
        Me.btnRename.tbIconReadOnly = Nothing
        Me.btnRename.tbImageMouseDown = Nothing
        Me.btnRename.tbImageMouseHover = Nothing
        Me.btnRename.tbImageMouseLeave = Nothing
        Me.btnRename.tbReadOnly = False
        Me.btnRename.tbReadOnlyText = False
        Me.btnRename.tbShadow = False
        Me.btnRename.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRename.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRename.tbShowDot = False
        Me.btnRename.tbShowNew = False
        Me.btnRename.tbShowToolTipOnButton = False
        Me.btnRename.tbSplit = "0,0,0,0"
        Me.btnRename.tbText = ""
        Me.btnRename.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRename.tbTextColor = System.Drawing.Color.Black
        Me.btnRename.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnRename.tbTextColorDown = System.Drawing.Color.Black
        Me.btnRename.tbTextColorHover = System.Drawing.Color.Black
        Me.btnRename.tbTextMouseDownPlace = 0
        Me.btnRename.tbToolTip = ""
        Me.btnRename.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRename.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRename.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRename.VisibleEx = True
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(491, 353)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(16, 16)
        Me.btnRefresh.TabIndex = 0
        Me.btnRefresh.TabStop = False
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = True
        Me.btnRefresh.tbAutoSizeEx = False
        Me.btnRefresh.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Nothing
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = ""
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.Black
        Me.btnRefresh.tbTextMouseDownPlace = 0
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'lblNameValue
        '
        Me.lblNameValue.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNameValue.AutoSize = True
        Me.lblNameValue.Font = New System.Drawing.Font("宋体", 15.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblNameValue.Location = New System.Drawing.Point(323, 38)
        Me.lblNameValue.Name = "lblNameValue"
        Me.lblNameValue.Size = New System.Drawing.Size(97, 24)
        Me.lblNameValue.TabIndex = 0
        Me.lblNameValue.tbAdriftWhenHover = False
        Me.lblNameValue.tbAutoEllipsis = False
        Me.lblNameValue.tbAutoSize = True
        Me.lblNameValue.tbHideImage = False
        Me.lblNameValue.tbIconImage = Nothing
        Me.lblNameValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNameValue.tbIconPlaceText = 5
        Me.lblNameValue.tbShadow = False
        Me.lblNameValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNameValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNameValue.tbShowScrolling = False
        Me.lblNameValue.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cmsSetting
        '
        Me.cmsSetting.DropShadowEnabled = False
        Me.cmsSetting.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiScreenshot, Me.tsmiScreenshotShell})
        Me.cmsSetting.Name = "munSearch"
        Me.cmsSetting.Size = New System.Drawing.Size(145, 48)
        Me.cmsSetting.tbBackColor = System.Drawing.Color.White
        Me.cmsSetting.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsSetting.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsSetting.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiScreenshot
        '
        Me.tsmiScreenshot.Name = "tsmiScreenshot"
        Me.tsmiScreenshot.Size = New System.Drawing.Size(144, 22)
        Me.tsmiScreenshot.Text = "截屏(无外壳)"
        '
        'tsmiScreenshotShell
        '
        Me.tsmiScreenshotShell.Name = "tsmiScreenshotShell"
        Me.tsmiScreenshotShell.Size = New System.Drawing.Size(144, 22)
        Me.tsmiScreenshotShell.Text = "截屏(带外壳)"
        '
        'btnJailbreak
        '
        Me.btnJailbreak.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnJailbreak.BackColor = System.Drawing.Color.Transparent
        Me.btnJailbreak.BindingForm = Nothing
        Me.btnJailbreak.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnJailbreak.Location = New System.Drawing.Point(97, 481)
        Me.btnJailbreak.Name = "btnJailbreak"
        Me.btnJailbreak.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnJailbreak.Selectable = True
        Me.btnJailbreak.Size = New System.Drawing.Size(158, 47)
        Me.btnJailbreak.TabIndex = 23
        Me.btnJailbreak.tbAdriftIconWhenHover = False
        Me.btnJailbreak.tbAutoSize = False
        Me.btnJailbreak.tbAutoSizeEx = False
        Me.btnJailbreak.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnJailbreak.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnJailbreak.tbBadgeNumber = 0
        Me.btnJailbreak.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnJailbreak.tbEndEllipsis = False
        Me.btnJailbreak.tbIconHoldPlace = True
        Me.btnJailbreak.tbIconImage = Nothing
        Me.btnJailbreak.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJailbreak.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnJailbreak.tbIconMore = False
        Me.btnJailbreak.tbIconMouseDown = Nothing
        Me.btnJailbreak.tbIconMouseHover = Nothing
        Me.btnJailbreak.tbIconMouseLeave = Nothing
        Me.btnJailbreak.tbIconPlaceText = 2
        Me.btnJailbreak.tbIconReadOnly = Nothing
        Me.btnJailbreak.tbImageMouseDown = Nothing
        Me.btnJailbreak.tbImageMouseHover = Nothing
        Me.btnJailbreak.tbImageMouseLeave = Nothing
        Me.btnJailbreak.tbReadOnly = False
        Me.btnJailbreak.tbReadOnlyText = False
        Me.btnJailbreak.tbShadow = False
        Me.btnJailbreak.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnJailbreak.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnJailbreak.tbShowDot = False
        Me.btnJailbreak.tbShowNew = False
        Me.btnJailbreak.tbShowToolTipOnButton = False
        Me.btnJailbreak.tbSplit = "13,11,13,11"
        Me.btnJailbreak.tbText = "开始越狱"
        Me.btnJailbreak.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJailbreak.tbTextColor = System.Drawing.Color.White
        Me.btnJailbreak.tbTextColorDisable = System.Drawing.Color.White
        Me.btnJailbreak.tbTextColorDown = System.Drawing.Color.White
        Me.btnJailbreak.tbTextColorHover = System.Drawing.Color.White
        Me.btnJailbreak.tbTextMouseDownPlace = 0
        Me.btnJailbreak.tbToolTip = ""
        Me.btnJailbreak.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnJailbreak.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnJailbreak.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJailbreak.VisibleEx = True
        '
        'lblData
        '
        Me.lblData.AutoSize = True
        Me.lblData.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblData.ForeColor = System.Drawing.Color.Black
        Me.lblData.Location = New System.Drawing.Point(38, 383)
        Me.lblData.Name = "lblData"
        Me.lblData.Size = New System.Drawing.Size(107, 12)
        Me.lblData.TabIndex = 29
        Me.lblData.Text = "数据容量: 6.39 GB"
        '
        'lblSystem
        '
        Me.lblSystem.AutoSize = True
        Me.lblSystem.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblSystem.ForeColor = System.Drawing.Color.Black
        Me.lblSystem.Location = New System.Drawing.Point(38, 330)
        Me.lblSystem.Name = "lblSystem"
        Me.lblSystem.Size = New System.Drawing.Size(107, 12)
        Me.lblSystem.TabIndex = 29
        Me.lblSystem.Text = "系统容量: 1.04 GB"
        '
        'lblKeyActive
        '
        Me.lblKeyActive.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyActive.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyActive.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyActive.Location = New System.Drawing.Point(-8, 11)
        Me.lblKeyActive.Name = "lblKeyActive"
        Me.lblKeyActive.Size = New System.Drawing.Size(108, 17)
        Me.lblKeyActive.TabIndex = 30
        Me.lblKeyActive.Text = "激活状态："
        Me.lblKeyActive.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblActive
        '
        Me.lblActive.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblActive.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblActive.ForeColor = System.Drawing.Color.Black
        Me.lblActive.Location = New System.Drawing.Point(106, 11)
        Me.lblActive.Name = "lblActive"
        Me.lblActive.Size = New System.Drawing.Size(179, 17)
        Me.lblActive.TabIndex = 31
        Me.lblActive.Text = "未激活"
        Me.lblActive.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyJB
        '
        Me.lblKeyJB.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyJB.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyJB.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyJB.Location = New System.Drawing.Point(-8, 43)
        Me.lblKeyJB.Name = "lblKeyJB"
        Me.lblKeyJB.Size = New System.Drawing.Size(108, 17)
        Me.lblKeyJB.TabIndex = 30
        Me.lblKeyJB.Text = "越狱状态："
        Me.lblKeyJB.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblJB
        '
        Me.lblJB.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblJB.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblJB.ForeColor = System.Drawing.Color.Black
        Me.lblJB.Location = New System.Drawing.Point(106, 43)
        Me.lblJB.Name = "lblJB"
        Me.lblJB.Size = New System.Drawing.Size(114, 17)
        Me.lblJB.TabIndex = 31
        Me.lblJB.Text = "未激活"
        Me.lblJB.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyProduct
        '
        Me.lblKeyProduct.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyProduct.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyProduct.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyProduct.Location = New System.Drawing.Point(261, 11)
        Me.lblKeyProduct.Name = "lblKeyProduct"
        Me.lblKeyProduct.Size = New System.Drawing.Size(90, 17)
        Me.lblKeyProduct.TabIndex = 30
        Me.lblKeyProduct.Text = "产品型号："
        Me.lblKeyProduct.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblProduct
        '
        Me.lblProduct.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblProduct.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblProduct.ForeColor = System.Drawing.Color.Black
        Me.lblProduct.Location = New System.Drawing.Point(357, 11)
        Me.lblProduct.Name = "lblProduct"
        Me.lblProduct.Size = New System.Drawing.Size(201, 17)
        Me.lblProduct.TabIndex = 31
        Me.lblProduct.Text = "未激活"
        Me.lblProduct.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyColor
        '
        Me.lblKeyColor.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyColor.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyColor.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyColor.Location = New System.Drawing.Point(-8, 107)
        Me.lblKeyColor.Name = "lblKeyColor"
        Me.lblKeyColor.Size = New System.Drawing.Size(108, 17)
        Me.lblKeyColor.TabIndex = 30
        Me.lblKeyColor.Text = "产品颜色："
        Me.lblKeyColor.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblColor
        '
        Me.lblColor.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblColor.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblColor.ForeColor = System.Drawing.Color.Black
        Me.lblColor.Location = New System.Drawing.Point(106, 107)
        Me.lblColor.Name = "lblColor"
        Me.lblColor.Size = New System.Drawing.Size(179, 17)
        Me.lblColor.TabIndex = 31
        Me.lblColor.Text = "未激活"
        Me.lblColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeySN
        '
        Me.lblKeySN.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeySN.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeySN.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeySN.Location = New System.Drawing.Point(-8, 139)
        Me.lblKeySN.Name = "lblKeySN"
        Me.lblKeySN.Size = New System.Drawing.Size(108, 17)
        Me.lblKeySN.TabIndex = 30
        Me.lblKeySN.Text = "序列号："
        Me.lblKeySN.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblSN
        '
        Me.lblSN.AutoSize = True
        Me.lblSN.BackColor = System.Drawing.Color.Transparent
        Me.lblSN.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblSN.ForeColor = System.Drawing.Color.Black
        Me.lblSN.Location = New System.Drawing.Point(106, 141)
        Me.lblSN.Name = "lblSN"
        Me.lblSN.Size = New System.Drawing.Size(41, 12)
        Me.lblSN.TabIndex = 31
        Me.lblSN.Text = "未激活"
        Me.lblSN.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyMode
        '
        Me.lblKeyMode.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyMode.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyMode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyMode.Location = New System.Drawing.Point(259, 75)
        Me.lblKeyMode.Name = "lblKeyMode"
        Me.lblKeyMode.Size = New System.Drawing.Size(92, 17)
        Me.lblKeyMode.TabIndex = 30
        Me.lblKeyMode.Text = "模型型号："
        Me.lblKeyMode.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblMode
        '
        Me.lblMode.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblMode.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblMode.ForeColor = System.Drawing.Color.Black
        Me.lblMode.Location = New System.Drawing.Point(357, 75)
        Me.lblMode.Name = "lblMode"
        Me.lblMode.Size = New System.Drawing.Size(145, 17)
        Me.lblMode.TabIndex = 31
        Me.lblMode.Text = "未激活"
        Me.lblMode.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyFM
        '
        Me.lblKeyFM.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyFM.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyFM.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyFM.Location = New System.Drawing.Point(8, 75)
        Me.lblKeyFM.Name = "lblKeyFM"
        Me.lblKeyFM.Size = New System.Drawing.Size(92, 17)
        Me.lblKeyFM.TabIndex = 30
        Me.lblKeyFM.Text = "固件版本："
        Me.lblKeyFM.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblFM
        '
        Me.lblFM.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblFM.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblFM.ForeColor = System.Drawing.Color.Black
        Me.lblFM.Location = New System.Drawing.Point(106, 75)
        Me.lblFM.Name = "lblFM"
        Me.lblFM.Size = New System.Drawing.Size(179, 17)
        Me.lblFM.TabIndex = 31
        Me.lblFM.Text = "未激活"
        Me.lblFM.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyPN
        '
        Me.lblKeyPN.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyPN.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyPN.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyPN.Location = New System.Drawing.Point(259, 107)
        Me.lblKeyPN.Name = "lblKeyPN"
        Me.lblKeyPN.Size = New System.Drawing.Size(92, 17)
        Me.lblKeyPN.TabIndex = 30
        Me.lblKeyPN.Text = "电话号码："
        Me.lblKeyPN.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblPN
        '
        Me.lblPN.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblPN.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblPN.ForeColor = System.Drawing.Color.Black
        Me.lblPN.Location = New System.Drawing.Point(357, 107)
        Me.lblPN.Name = "lblPN"
        Me.lblPN.Size = New System.Drawing.Size(145, 17)
        Me.lblPN.TabIndex = 31
        Me.lblPN.Text = "未激活"
        Me.lblPN.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlDetail
        '
        Me.pnlDetail.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlDetail.Controls.Add(Me.btnFindMyPhone)
        Me.pnlDetail.Controls.Add(Me.btnCopyIMEI)
        Me.pnlDetail.Controls.Add(Me.picLoading)
        Me.pnlDetail.Controls.Add(Me.lblCopyInfo)
        Me.pnlDetail.Controls.Add(Me.btnCopySN)
        Me.pnlDetail.Controls.Add(Me.lblProduct)
        Me.pnlDetail.Controls.Add(Me.lblIMEI)
        Me.pnlDetail.Controls.Add(Me.lblKeyIMEI)
        Me.pnlDetail.Controls.Add(Me.lblBlueAddress)
        Me.pnlDetail.Controls.Add(Me.lblWifi)
        Me.pnlDetail.Controls.Add(Me.lblKeyBlueAddress)
        Me.pnlDetail.Controls.Add(Me.lblKeyWifi)
        Me.pnlDetail.Controls.Add(Me.lblDataUsed)
        Me.pnlDetail.Controls.Add(Me.lblSystemUsed)
        Me.pnlDetail.Controls.Add(Me.lblKeyBaseband)
        Me.pnlDetail.Controls.Add(Me.lblKeyPN)
        Me.pnlDetail.Controls.Add(Me.lblKeyFM)
        Me.pnlDetail.Controls.Add(Me.lblKeySaleArea)
        Me.pnlDetail.Controls.Add(Me.lblKeyMode)
        Me.pnlDetail.Controls.Add(Me.pgbData)
        Me.pnlDetail.Controls.Add(Me.pgbSystem)
        Me.pnlDetail.Controls.Add(Me.lblBaseband)
        Me.pnlDetail.Controls.Add(Me.lblKeyActive)
        Me.pnlDetail.Controls.Add(Me.lblPN)
        Me.pnlDetail.Controls.Add(Me.btnRefresh)
        Me.pnlDetail.Controls.Add(Me.lblFM)
        Me.pnlDetail.Controls.Add(Me.lblFindMyPhone)
        Me.pnlDetail.Controls.Add(Me.lblData)
        Me.pnlDetail.Controls.Add(Me.lblSN)
        Me.pnlDetail.Controls.Add(Me.lblSystem)
        Me.pnlDetail.Controls.Add(Me.lblActive)
        Me.pnlDetail.Controls.Add(Me.lblKeySN)
        Me.pnlDetail.Controls.Add(Me.lblSaleArea)
        Me.pnlDetail.Controls.Add(Me.lblMode)
        Me.pnlDetail.Controls.Add(Me.lblUDID)
        Me.pnlDetail.Controls.Add(Me.lblKeyJB)
        Me.pnlDetail.Controls.Add(Me.lblKeyUDID)
        Me.pnlDetail.Controls.Add(Me.lblColor)
        Me.pnlDetail.Controls.Add(Me.lblKeyFindMyPhone)
        Me.pnlDetail.Controls.Add(Me.lblJB)
        Me.pnlDetail.Controls.Add(Me.lblKeyColor)
        Me.pnlDetail.Controls.Add(Me.lblKeyProduct)
        Me.pnlDetail.Location = New System.Drawing.Point(326, 116)
        Me.pnlDetail.Name = "pnlDetail"
        Me.pnlDetail.Size = New System.Drawing.Size(570, 441)
        Me.pnlDetail.TabIndex = 32
        '
        'btnAgain
        '
        Me.btnAgain.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnAgain.BackColor = System.Drawing.Color.Transparent
        Me.btnAgain.BindingForm = Nothing
        Me.btnAgain.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnAgain.Enabled = False
        Me.btnAgain.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAgain.Location = New System.Drawing.Point(797, 56)
        Me.btnAgain.Name = "btnAgain"
        Me.btnAgain.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnAgain.Selectable = True
        Me.btnAgain.Size = New System.Drawing.Size(63, 17)
        Me.btnAgain.TabIndex = 44
        Me.btnAgain.tbAdriftIconWhenHover = False
        Me.btnAgain.tbAutoSize = False
        Me.btnAgain.tbAutoSizeEx = True
        Me.btnAgain.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_copy
        Me.btnAgain.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnAgain.tbBadgeNumber = 0
        Me.btnAgain.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAgain.tbEndEllipsis = False
        Me.btnAgain.tbIconHoldPlace = True
        Me.btnAgain.tbIconImage = Nothing
        Me.btnAgain.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAgain.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAgain.tbIconMore = False
        Me.btnAgain.tbIconMouseDown = Nothing
        Me.btnAgain.tbIconMouseHover = Nothing
        Me.btnAgain.tbIconMouseLeave = Nothing
        Me.btnAgain.tbIconPlaceText = 2
        Me.btnAgain.tbIconReadOnly = Nothing
        Me.btnAgain.tbImageMouseDown = Nothing
        Me.btnAgain.tbImageMouseHover = Nothing
        Me.btnAgain.tbImageMouseLeave = Nothing
        Me.btnAgain.tbReadOnly = False
        Me.btnAgain.tbReadOnlyText = False
        Me.btnAgain.tbShadow = False
        Me.btnAgain.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAgain.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAgain.tbShowDot = False
        Me.btnAgain.tbShowNew = False
        Me.btnAgain.tbShowToolTipOnButton = False
        Me.btnAgain.tbSplit = "13,11,13,11"
        Me.btnAgain.tbText = "重新获取"
        Me.btnAgain.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAgain.tbTextColor = System.Drawing.Color.CornflowerBlue
        Me.btnAgain.tbTextColorDisable = System.Drawing.Color.CornflowerBlue
        Me.btnAgain.tbTextColorDown = System.Drawing.Color.White
        Me.btnAgain.tbTextColorHover = System.Drawing.Color.White
        Me.btnAgain.tbTextMouseDownPlace = 0
        Me.btnAgain.tbToolTip = ""
        Me.btnAgain.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAgain.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAgain.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAgain.Visible = False
        Me.btnAgain.VisibleEx = True
        '
        'btnFindMyPhone
        '
        Me.btnFindMyPhone.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnFindMyPhone.BackColor = System.Drawing.Color.Transparent
        Me.btnFindMyPhone.BindingForm = Nothing
        Me.btnFindMyPhone.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnFindMyPhone.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFindMyPhone.Location = New System.Drawing.Point(157, 203)
        Me.btnFindMyPhone.Name = "btnFindMyPhone"
        Me.btnFindMyPhone.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnFindMyPhone.Selectable = True
        Me.btnFindMyPhone.Size = New System.Drawing.Size(63, 17)
        Me.btnFindMyPhone.TabIndex = 40
        Me.btnFindMyPhone.tbAdriftIconWhenHover = False
        Me.btnFindMyPhone.tbAutoSize = False
        Me.btnFindMyPhone.tbAutoSizeEx = True
        Me.btnFindMyPhone.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_copy
        Me.btnFindMyPhone.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnFindMyPhone.tbBadgeNumber = 0
        Me.btnFindMyPhone.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFindMyPhone.tbEndEllipsis = False
        Me.btnFindMyPhone.tbIconHoldPlace = True
        Me.btnFindMyPhone.tbIconImage = Nothing
        Me.btnFindMyPhone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFindMyPhone.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnFindMyPhone.tbIconMore = False
        Me.btnFindMyPhone.tbIconMouseDown = Nothing
        Me.btnFindMyPhone.tbIconMouseHover = Nothing
        Me.btnFindMyPhone.tbIconMouseLeave = Nothing
        Me.btnFindMyPhone.tbIconPlaceText = 2
        Me.btnFindMyPhone.tbIconReadOnly = Nothing
        Me.btnFindMyPhone.tbImageMouseDown = Nothing
        Me.btnFindMyPhone.tbImageMouseHover = Nothing
        Me.btnFindMyPhone.tbImageMouseLeave = Nothing
        Me.btnFindMyPhone.tbReadOnly = False
        Me.btnFindMyPhone.tbReadOnlyText = False
        Me.btnFindMyPhone.tbShadow = False
        Me.btnFindMyPhone.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFindMyPhone.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFindMyPhone.tbShowDot = False
        Me.btnFindMyPhone.tbShowNew = False
        Me.btnFindMyPhone.tbShowToolTipOnButton = False
        Me.btnFindMyPhone.tbSplit = "13,11,13,11"
        Me.btnFindMyPhone.tbText = "精确查询"
        Me.btnFindMyPhone.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFindMyPhone.tbTextColor = System.Drawing.Color.CornflowerBlue
        Me.btnFindMyPhone.tbTextColorDisable = System.Drawing.Color.CornflowerBlue
        Me.btnFindMyPhone.tbTextColorDown = System.Drawing.Color.White
        Me.btnFindMyPhone.tbTextColorHover = System.Drawing.Color.White
        Me.btnFindMyPhone.tbTextMouseDownPlace = 0
        Me.btnFindMyPhone.tbToolTip = ""
        Me.btnFindMyPhone.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFindMyPhone.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFindMyPhone.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFindMyPhone.Visible = False
        Me.btnFindMyPhone.VisibleEx = True
        '
        'btnCopyIMEI
        '
        Me.btnCopyIMEI.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnCopyIMEI.BackColor = System.Drawing.Color.Transparent
        Me.btnCopyIMEI.BindingForm = Nothing
        Me.btnCopyIMEI.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnCopyIMEI.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCopyIMEI.Location = New System.Drawing.Point(157, 171)
        Me.btnCopyIMEI.Name = "btnCopyIMEI"
        Me.btnCopyIMEI.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCopyIMEI.Selectable = True
        Me.btnCopyIMEI.Size = New System.Drawing.Size(39, 17)
        Me.btnCopyIMEI.TabIndex = 43
        Me.btnCopyIMEI.tbAdriftIconWhenHover = False
        Me.btnCopyIMEI.tbAutoSize = False
        Me.btnCopyIMEI.tbAutoSizeEx = True
        Me.btnCopyIMEI.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_copy
        Me.btnCopyIMEI.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCopyIMEI.tbBadgeNumber = 0
        Me.btnCopyIMEI.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopyIMEI.tbEndEllipsis = False
        Me.btnCopyIMEI.tbIconHoldPlace = True
        Me.btnCopyIMEI.tbIconImage = Nothing
        Me.btnCopyIMEI.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyIMEI.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCopyIMEI.tbIconMore = False
        Me.btnCopyIMEI.tbIconMouseDown = Nothing
        Me.btnCopyIMEI.tbIconMouseHover = Nothing
        Me.btnCopyIMEI.tbIconMouseLeave = Nothing
        Me.btnCopyIMEI.tbIconPlaceText = 2
        Me.btnCopyIMEI.tbIconReadOnly = Nothing
        Me.btnCopyIMEI.tbImageMouseDown = Nothing
        Me.btnCopyIMEI.tbImageMouseHover = Nothing
        Me.btnCopyIMEI.tbImageMouseLeave = Nothing
        Me.btnCopyIMEI.tbReadOnly = False
        Me.btnCopyIMEI.tbReadOnlyText = False
        Me.btnCopyIMEI.tbShadow = False
        Me.btnCopyIMEI.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCopyIMEI.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCopyIMEI.tbShowDot = False
        Me.btnCopyIMEI.tbShowNew = False
        Me.btnCopyIMEI.tbShowToolTipOnButton = False
        Me.btnCopyIMEI.tbSplit = "13,11,13,11"
        Me.btnCopyIMEI.tbText = "复制"
        Me.btnCopyIMEI.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyIMEI.tbTextColor = System.Drawing.Color.CornflowerBlue
        Me.btnCopyIMEI.tbTextColorDisable = System.Drawing.Color.CornflowerBlue
        Me.btnCopyIMEI.tbTextColorDown = System.Drawing.Color.White
        Me.btnCopyIMEI.tbTextColorHover = System.Drawing.Color.White
        Me.btnCopyIMEI.tbTextMouseDownPlace = 0
        Me.btnCopyIMEI.tbToolTip = ""
        Me.btnCopyIMEI.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCopyIMEI.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopyIMEI.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyIMEI.Visible = False
        Me.btnCopyIMEI.VisibleEx = True
        '
        'picLoading
        '
        Me.picLoading.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.picLoading.BackColor = System.Drawing.Color.Transparent
        Me.picLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_14
        Me.picLoading.Location = New System.Drawing.Point(491, 353)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(14, 14)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picLoading.TabIndex = 42
        Me.picLoading.TabStop = False
        Me.picLoading.Visible = False
        '
        'lblCopyInfo
        '
        Me.lblCopyInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblCopyInfo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblCopyInfo.ForeColor = System.Drawing.Color.Green
        Me.lblCopyInfo.Location = New System.Drawing.Point(202, 167)
        Me.lblCopyInfo.Name = "lblCopyInfo"
        Me.lblCopyInfo.Size = New System.Drawing.Size(65, 25)
        Me.lblCopyInfo.TabIndex = 41
        Me.lblCopyInfo.tbAdriftWhenHover = False
        Me.lblCopyInfo.tbAutoEllipsis = False
        Me.lblCopyInfo.tbAutoSize = False
        Me.lblCopyInfo.tbHideImage = False
        Me.lblCopyInfo.tbIconImage = Nothing
        Me.lblCopyInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCopyInfo.tbIconPlaceText = 5
        Me.lblCopyInfo.tbShadow = False
        Me.lblCopyInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCopyInfo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCopyInfo.tbShowScrolling = False
        Me.lblCopyInfo.Text = "复制成功！"
        Me.lblCopyInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblCopyInfo.Visible = False
        '
        'btnCopySN
        '
        Me.btnCopySN.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnCopySN.BackColor = System.Drawing.Color.Transparent
        Me.btnCopySN.BindingForm = Nothing
        Me.btnCopySN.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnCopySN.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCopySN.Location = New System.Drawing.Point(157, 139)
        Me.btnCopySN.Name = "btnCopySN"
        Me.btnCopySN.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCopySN.Selectable = True
        Me.btnCopySN.Size = New System.Drawing.Size(39, 17)
        Me.btnCopySN.TabIndex = 40
        Me.btnCopySN.tbAdriftIconWhenHover = False
        Me.btnCopySN.tbAutoSize = False
        Me.btnCopySN.tbAutoSizeEx = True
        Me.btnCopySN.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_copy
        Me.btnCopySN.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCopySN.tbBadgeNumber = 0
        Me.btnCopySN.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopySN.tbEndEllipsis = False
        Me.btnCopySN.tbIconHoldPlace = True
        Me.btnCopySN.tbIconImage = Nothing
        Me.btnCopySN.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopySN.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCopySN.tbIconMore = False
        Me.btnCopySN.tbIconMouseDown = Nothing
        Me.btnCopySN.tbIconMouseHover = Nothing
        Me.btnCopySN.tbIconMouseLeave = Nothing
        Me.btnCopySN.tbIconPlaceText = 2
        Me.btnCopySN.tbIconReadOnly = Nothing
        Me.btnCopySN.tbImageMouseDown = Nothing
        Me.btnCopySN.tbImageMouseHover = Nothing
        Me.btnCopySN.tbImageMouseLeave = Nothing
        Me.btnCopySN.tbReadOnly = False
        Me.btnCopySN.tbReadOnlyText = False
        Me.btnCopySN.tbShadow = False
        Me.btnCopySN.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCopySN.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCopySN.tbShowDot = False
        Me.btnCopySN.tbShowNew = False
        Me.btnCopySN.tbShowToolTipOnButton = False
        Me.btnCopySN.tbSplit = "13,11,13,11"
        Me.btnCopySN.tbText = "复制"
        Me.btnCopySN.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopySN.tbTextColor = System.Drawing.Color.CornflowerBlue
        Me.btnCopySN.tbTextColorDisable = System.Drawing.Color.CornflowerBlue
        Me.btnCopySN.tbTextColorDown = System.Drawing.Color.White
        Me.btnCopySN.tbTextColorHover = System.Drawing.Color.White
        Me.btnCopySN.tbTextMouseDownPlace = 0
        Me.btnCopySN.tbToolTip = ""
        Me.btnCopySN.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCopySN.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopySN.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopySN.Visible = False
        Me.btnCopySN.VisibleEx = True
        '
        'lblIMEI
        '
        Me.lblIMEI.AutoSize = True
        Me.lblIMEI.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblIMEI.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblIMEI.ForeColor = System.Drawing.Color.Black
        Me.lblIMEI.Location = New System.Drawing.Point(106, 173)
        Me.lblIMEI.Name = "lblIMEI"
        Me.lblIMEI.Size = New System.Drawing.Size(41, 12)
        Me.lblIMEI.TabIndex = 39
        Me.lblIMEI.Text = "未激活"
        Me.lblIMEI.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyIMEI
        '
        Me.lblKeyIMEI.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyIMEI.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyIMEI.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyIMEI.Location = New System.Drawing.Point(8, 171)
        Me.lblKeyIMEI.Name = "lblKeyIMEI"
        Me.lblKeyIMEI.Size = New System.Drawing.Size(92, 17)
        Me.lblKeyIMEI.TabIndex = 38
        Me.lblKeyIMEI.Text = "IMEI："
        Me.lblKeyIMEI.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblBlueAddress
        '
        Me.lblBlueAddress.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblBlueAddress.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblBlueAddress.ForeColor = System.Drawing.Color.Black
        Me.lblBlueAddress.Location = New System.Drawing.Point(357, 171)
        Me.lblBlueAddress.Name = "lblBlueAddress"
        Me.lblBlueAddress.Size = New System.Drawing.Size(145, 17)
        Me.lblBlueAddress.TabIndex = 37
        Me.lblBlueAddress.Text = "未激活"
        Me.lblBlueAddress.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblWifi
        '
        Me.lblWifi.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblWifi.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblWifi.ForeColor = System.Drawing.Color.Black
        Me.lblWifi.Location = New System.Drawing.Point(357, 139)
        Me.lblWifi.Name = "lblWifi"
        Me.lblWifi.Size = New System.Drawing.Size(145, 17)
        Me.lblWifi.TabIndex = 37
        Me.lblWifi.Text = "未激活"
        Me.lblWifi.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyBlueAddress
        '
        Me.lblKeyBlueAddress.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyBlueAddress.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyBlueAddress.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyBlueAddress.Location = New System.Drawing.Point(259, 171)
        Me.lblKeyBlueAddress.Name = "lblKeyBlueAddress"
        Me.lblKeyBlueAddress.Size = New System.Drawing.Size(92, 17)
        Me.lblKeyBlueAddress.TabIndex = 36
        Me.lblKeyBlueAddress.Text = "蓝牙地址："
        Me.lblKeyBlueAddress.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblKeyWifi
        '
        Me.lblKeyWifi.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyWifi.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyWifi.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyWifi.Location = New System.Drawing.Point(259, 139)
        Me.lblKeyWifi.Name = "lblKeyWifi"
        Me.lblKeyWifi.Size = New System.Drawing.Size(92, 17)
        Me.lblKeyWifi.TabIndex = 36
        Me.lblKeyWifi.Text = "Wi-Fi地址："
        Me.lblKeyWifi.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblDataUsed
        '
        Me.lblDataUsed.AutoSize = True
        Me.lblDataUsed.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblDataUsed.ForeColor = System.Drawing.Color.Black
        Me.lblDataUsed.Location = New System.Drawing.Point(197, 383)
        Me.lblDataUsed.Name = "lblDataUsed"
        Me.lblDataUsed.Size = New System.Drawing.Size(107, 12)
        Me.lblDataUsed.TabIndex = 35
        Me.lblDataUsed.Text = "数据容量: 6.39 GB"
        '
        'lblSystemUsed
        '
        Me.lblSystemUsed.AutoSize = True
        Me.lblSystemUsed.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblSystemUsed.ForeColor = System.Drawing.Color.Black
        Me.lblSystemUsed.Location = New System.Drawing.Point(197, 330)
        Me.lblSystemUsed.Name = "lblSystemUsed"
        Me.lblSystemUsed.Size = New System.Drawing.Size(107, 12)
        Me.lblSystemUsed.TabIndex = 34
        Me.lblSystemUsed.Text = "系统容量: 1.04 GB"
        '
        'lblKeyBaseband
        '
        Me.lblKeyBaseband.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyBaseband.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyBaseband.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyBaseband.Location = New System.Drawing.Point(259, 203)
        Me.lblKeyBaseband.Name = "lblKeyBaseband"
        Me.lblKeyBaseband.Size = New System.Drawing.Size(92, 17)
        Me.lblKeyBaseband.TabIndex = 30
        Me.lblKeyBaseband.Text = "基带版本："
        Me.lblKeyBaseband.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblKeySaleArea
        '
        Me.lblKeySaleArea.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeySaleArea.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeySaleArea.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeySaleArea.Location = New System.Drawing.Point(259, 43)
        Me.lblKeySaleArea.Name = "lblKeySaleArea"
        Me.lblKeySaleArea.Size = New System.Drawing.Size(92, 17)
        Me.lblKeySaleArea.TabIndex = 30
        Me.lblKeySaleArea.Text = "销售地区："
        Me.lblKeySaleArea.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblKeyExpireDate
        '
        Me.lblKeyExpireDate.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyExpireDate.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyExpireDate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyExpireDate.Location = New System.Drawing.Point(587, 88)
        Me.lblKeyExpireDate.Name = "lblKeyExpireDate"
        Me.lblKeyExpireDate.Size = New System.Drawing.Size(92, 17)
        Me.lblKeyExpireDate.TabIndex = 30
        Me.lblKeyExpireDate.Text = "过保日期："
        Me.lblKeyExpireDate.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lblKeyExpireDate.Visible = False
        '
        'lblKeylBuyDate
        '
        Me.lblKeylBuyDate.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeylBuyDate.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeylBuyDate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeylBuyDate.Location = New System.Drawing.Point(587, 56)
        Me.lblKeylBuyDate.Name = "lblKeylBuyDate"
        Me.lblKeylBuyDate.Size = New System.Drawing.Size(92, 17)
        Me.lblKeylBuyDate.TabIndex = 30
        Me.lblKeylBuyDate.Text = "购买日期："
        Me.lblKeylBuyDate.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lblKeylBuyDate.Visible = False
        '
        'pgbData
        '
        Me.pgbData.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pgbData.BackColor = System.Drawing.Color.Transparent
        Me.pgbData.Font = New System.Drawing.Font("宋体", 8.0!)
        Me.pgbData.Location = New System.Drawing.Point(38, 407)
        Me.pgbData.Name = "pgbData"
        Me.pgbData.Size = New System.Drawing.Size(447, 17)
        Me.pgbData.TabIndex = 33
        Me.pgbData.tbBackgroundImage = CType(resources.GetObject("pgbData.tbBackgroundImage"), System.Drawing.Image)
        Me.pgbData.tbCanDragValue = False
        Me.pgbData.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pgbData.tbDotImage = CType(resources.GetObject("pgbData.tbDotImage"), System.Drawing.Image)
        Me.pgbData.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pgbData.tbDotMouseDown = Nothing
        Me.pgbData.tbDotMouseHover = Nothing
        Me.pgbData.tbDotMouseLeave = Nothing
        Me.pgbData.tbDownloadImage = CType(resources.GetObject("pgbData.tbDownloadImage"), System.Drawing.Image)
        Me.pgbData.tbDownMax = 100
        Me.pgbData.tbDownMin = 0
        Me.pgbData.tbDownValue = 0
        Me.pgbData.tbIsWaiting = False
        Me.pgbData.tbPlayImage = Global.iTong.My.Resources.Resources.player_progress_play
        Me.pgbData.tbPlayMax = 100
        Me.pgbData.tbPlayMin = 0
        Me.pgbData.tbPlayValue = 0
        Me.pgbData.tbShowDot = False
        Me.pgbData.tbShowText = False
        Me.pgbData.tbSplit = "4,3,5,4"
        Me.pgbData.Text = "TbControlBar2"
        '
        'pgbSystem
        '
        Me.pgbSystem.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pgbSystem.BackColor = System.Drawing.Color.Transparent
        Me.pgbSystem.Font = New System.Drawing.Font("宋体", 8.0!)
        Me.pgbSystem.Location = New System.Drawing.Point(38, 353)
        Me.pgbSystem.Name = "pgbSystem"
        Me.pgbSystem.Size = New System.Drawing.Size(447, 17)
        Me.pgbSystem.TabIndex = 32
        Me.pgbSystem.tbBackgroundImage = CType(resources.GetObject("pgbSystem.tbBackgroundImage"), System.Drawing.Image)
        Me.pgbSystem.tbCanDragValue = False
        Me.pgbSystem.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pgbSystem.tbDotImage = CType(resources.GetObject("pgbSystem.tbDotImage"), System.Drawing.Image)
        Me.pgbSystem.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pgbSystem.tbDotMouseDown = Nothing
        Me.pgbSystem.tbDotMouseHover = Nothing
        Me.pgbSystem.tbDotMouseLeave = Nothing
        Me.pgbSystem.tbDownloadImage = Global.iTong.My.Resources.Resources.player_progress_bg
        Me.pgbSystem.tbDownMax = 100
        Me.pgbSystem.tbDownMin = 0
        Me.pgbSystem.tbDownValue = 0
        Me.pgbSystem.tbIsWaiting = False
        Me.pgbSystem.tbPlayImage = Global.iTong.My.Resources.Resources.player_progress_play
        Me.pgbSystem.tbPlayMax = 100
        Me.pgbSystem.tbPlayMin = 0
        Me.pgbSystem.tbPlayValue = 0
        Me.pgbSystem.tbShowDot = False
        Me.pgbSystem.tbShowText = False
        Me.pgbSystem.tbSplit = "4,3,5,4"
        Me.pgbSystem.Text = "TbControlBar1"
        '
        'lblBaseband
        '
        Me.lblBaseband.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblBaseband.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblBaseband.ForeColor = System.Drawing.Color.Black
        Me.lblBaseband.Location = New System.Drawing.Point(357, 203)
        Me.lblBaseband.Name = "lblBaseband"
        Me.lblBaseband.Size = New System.Drawing.Size(145, 17)
        Me.lblBaseband.TabIndex = 31
        Me.lblBaseband.Text = "未激活"
        Me.lblBaseband.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblFindMyPhone
        '
        Me.lblFindMyPhone.AutoSize = True
        Me.lblFindMyPhone.BackColor = System.Drawing.Color.Transparent
        Me.lblFindMyPhone.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblFindMyPhone.ForeColor = System.Drawing.Color.Black
        Me.lblFindMyPhone.Location = New System.Drawing.Point(106, 205)
        Me.lblFindMyPhone.Name = "lblFindMyPhone"
        Me.lblFindMyPhone.Size = New System.Drawing.Size(41, 12)
        Me.lblFindMyPhone.TabIndex = 31
        Me.lblFindMyPhone.Text = "未激活"
        Me.lblFindMyPhone.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblSaleArea
        '
        Me.lblSaleArea.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblSaleArea.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblSaleArea.ForeColor = System.Drawing.Color.Black
        Me.lblSaleArea.Location = New System.Drawing.Point(357, 43)
        Me.lblSaleArea.Name = "lblSaleArea"
        Me.lblSaleArea.Size = New System.Drawing.Size(145, 17)
        Me.lblSaleArea.TabIndex = 31
        Me.lblSaleArea.Text = "未激活"
        Me.lblSaleArea.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblExpireDate
        '
        Me.lblExpireDate.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblExpireDate.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblExpireDate.ForeColor = System.Drawing.Color.Black
        Me.lblExpireDate.Location = New System.Drawing.Point(685, 88)
        Me.lblExpireDate.Name = "lblExpireDate"
        Me.lblExpireDate.Size = New System.Drawing.Size(145, 17)
        Me.lblExpireDate.TabIndex = 31
        Me.lblExpireDate.Text = "未激活"
        Me.lblExpireDate.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblExpireDate.Visible = False
        '
        'lblBuyDate
        '
        Me.lblBuyDate.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblBuyDate.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblBuyDate.ForeColor = System.Drawing.Color.Black
        Me.lblBuyDate.Location = New System.Drawing.Point(685, 56)
        Me.lblBuyDate.Name = "lblBuyDate"
        Me.lblBuyDate.Size = New System.Drawing.Size(145, 17)
        Me.lblBuyDate.TabIndex = 31
        Me.lblBuyDate.Text = "未激活"
        Me.lblBuyDate.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblBuyDate.Visible = False
        '
        'lblUDID
        '
        Me.lblUDID.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblUDID.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblUDID.ForeColor = System.Drawing.Color.Black
        Me.lblUDID.Location = New System.Drawing.Point(106, 235)
        Me.lblUDID.Name = "lblUDID"
        Me.lblUDID.Size = New System.Drawing.Size(398, 17)
        Me.lblUDID.TabIndex = 31
        Me.lblUDID.Text = "未激活"
        Me.lblUDID.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblKeyUDID
        '
        Me.lblKeyUDID.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyUDID.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyUDID.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyUDID.Location = New System.Drawing.Point(-8, 235)
        Me.lblKeyUDID.Name = "lblKeyUDID"
        Me.lblKeyUDID.Size = New System.Drawing.Size(108, 17)
        Me.lblKeyUDID.TabIndex = 30
        Me.lblKeyUDID.Text = "设备标识："
        Me.lblKeyUDID.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblKeyFindMyPhone
        '
        Me.lblKeyFindMyPhone.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblKeyFindMyPhone.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblKeyFindMyPhone.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(165, Byte), Integer), CType(CType(166, Byte), Integer))
        Me.lblKeyFindMyPhone.Location = New System.Drawing.Point(-8, 203)
        Me.lblKeyFindMyPhone.Name = "lblKeyFindMyPhone"
        Me.lblKeyFindMyPhone.Size = New System.Drawing.Size(108, 17)
        Me.lblKeyFindMyPhone.TabIndex = 30
        Me.lblKeyFindMyPhone.Text = "查找iPhone："
        Me.lblKeyFindMyPhone.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'btnDetail
        '
        Me.btnDetail.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnDetail.BackColor = System.Drawing.Color.Transparent
        Me.btnDetail.BindingForm = Nothing
        Me.btnDetail.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDetail.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDetail.Location = New System.Drawing.Point(586, 16)
        Me.btnDetail.Name = "btnDetail"
        Me.btnDetail.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDetail.Selectable = True
        Me.btnDetail.Size = New System.Drawing.Size(15, 15)
        Me.btnDetail.TabIndex = 15
        Me.btnDetail.TabStop = False
        Me.btnDetail.tbAdriftIconWhenHover = False
        Me.btnDetail.tbAutoSize = False
        Me.btnDetail.tbAutoSizeEx = False
        Me.btnDetail.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_3_info
        Me.btnDetail.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnDetail.tbBadgeNumber = 0
        Me.btnDetail.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDetail.tbEndEllipsis = False
        Me.btnDetail.tbIconHoldPlace = True
        Me.btnDetail.tbIconImage = Nothing
        Me.btnDetail.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDetail.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDetail.tbIconMore = False
        Me.btnDetail.tbIconMouseDown = Nothing
        Me.btnDetail.tbIconMouseHover = Nothing
        Me.btnDetail.tbIconMouseLeave = Nothing
        Me.btnDetail.tbIconPlaceText = 2
        Me.btnDetail.tbIconReadOnly = Nothing
        Me.btnDetail.tbImageMouseDown = Nothing
        Me.btnDetail.tbImageMouseHover = Nothing
        Me.btnDetail.tbImageMouseLeave = Nothing
        Me.btnDetail.tbReadOnly = False
        Me.btnDetail.tbReadOnlyText = False
        Me.btnDetail.tbShadow = False
        Me.btnDetail.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDetail.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDetail.tbShowDot = False
        Me.btnDetail.tbShowNew = False
        Me.btnDetail.tbShowToolTipOnButton = False
        Me.btnDetail.tbSplit = "0,0,0,0"
        Me.btnDetail.tbText = ""
        Me.btnDetail.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDetail.tbTextColor = System.Drawing.Color.Black
        Me.btnDetail.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnDetail.tbTextColorDown = System.Drawing.Color.Black
        Me.btnDetail.tbTextColorHover = System.Drawing.Color.Black
        Me.btnDetail.tbTextMouseDownPlace = 0
        Me.btnDetail.tbToolTip = ""
        Me.btnDetail.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDetail.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDetail.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDetail.VisibleEx = True
        '
        'tmrShowCopyInfo
        '
        Me.tmrShowCopyInfo.Interval = 1000
        '
        'lblCharging
        '
        Me.lblCharging.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblCharging.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblCharging.Location = New System.Drawing.Point(357, 71)
        Me.lblCharging.Name = "lblCharging"
        Me.lblCharging.Size = New System.Drawing.Size(149, 21)
        Me.lblCharging.TabIndex = 34
        Me.lblCharging.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picCharging
        '
        Me.picCharging.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picCharging.Location = New System.Drawing.Point(328, 68)
        Me.picCharging.Margin = New System.Windows.Forms.Padding(0)
        Me.picCharging.Name = "picCharging"
        Me.picCharging.Size = New System.Drawing.Size(26, 26)
        Me.picCharging.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picCharging.TabIndex = 33
        Me.picCharging.TabStop = False
        '
        'tmrRefresh
        '
        Me.tmrRefresh.Interval = 90000
        '
        'bgwStartJailbreak
        '
        Me.bgwStartJailbreak.WorkerSupportsCancellation = True
        '
        'frmSummaryZJ
        '
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(908, 580)
        Me.Controls.Add(Me.btnAgain)
        Me.Controls.Add(Me.btnJailbreak)
        Me.Controls.Add(Me.pnlDetail)
        Me.Controls.Add(Me.lblCharging)
        Me.Controls.Add(Me.picCharging)
        Me.Controls.Add(Me.lblNameValue)
        Me.Controls.Add(Me.btnDisconnect)
        Me.Controls.Add(Me.lblDownloadProgress)
        Me.Controls.Add(Me.pbDevice)
        Me.Controls.Add(Me.pnlScreenshot)
        Me.Controls.Add(Me.btn_EntryRecovery)
        Me.Controls.Add(Me.pnlChangeDeviceName)
        Me.Controls.Add(Me.btnRename)
        Me.Controls.Add(Me.btnDetail)
        Me.Controls.Add(Me.lblKeylBuyDate)
        Me.Controls.Add(Me.lblBuyDate)
        Me.Controls.Add(Me.lblKeyExpireDate)
        Me.Controls.Add(Me.lblExpireDate)
        Me.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.MinimumSize = New System.Drawing.Size(6, 6)
        Me.Name = "frmSummaryZJ"
        Me.tbShowIconOnForm = False
        Me.tbSplit = "3,3,3,3"
        Me.tbTitleBackColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(196, Byte), Integer))
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Controls.SetChildIndex(Me.lblExpireDate, 0)
        Me.Controls.SetChildIndex(Me.lblKeyExpireDate, 0)
        Me.Controls.SetChildIndex(Me.lblBuyDate, 0)
        Me.Controls.SetChildIndex(Me.lblKeylBuyDate, 0)
        Me.Controls.SetChildIndex(Me.btnDetail, 0)
        Me.Controls.SetChildIndex(Me.btnRename, 0)
        Me.Controls.SetChildIndex(Me.pnlChangeDeviceName, 0)
        Me.Controls.SetChildIndex(Me.btn_EntryRecovery, 0)
        Me.Controls.SetChildIndex(Me.pnlScreenshot, 0)
        Me.Controls.SetChildIndex(Me.pbDevice, 0)
        Me.Controls.SetChildIndex(Me.lblDownloadProgress, 0)
        Me.Controls.SetChildIndex(Me.btnDisconnect, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.lblNameValue, 0)
        Me.Controls.SetChildIndex(Me.picCharging, 0)
        Me.Controls.SetChildIndex(Me.lblCharging, 0)
        Me.Controls.SetChildIndex(Me.pnlDetail, 0)
        Me.Controls.SetChildIndex(Me.btnJailbreak, 0)
        Me.Controls.SetChildIndex(Me.btnAgain, 0)
        Me.pnlChangeDeviceName.ResumeLayout(False)
        CType(Me.pbDevice, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlScreenshot.ResumeLayout(False)
        Me.cmsSetting.ResumeLayout(False)
        Me.pnlDetail.ResumeLayout(False)
        Me.pnlDetail.PerformLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picCharging, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pbDevice As System.Windows.Forms.PictureBox
    Friend WithEvents lblNameValue As iTong.Components.tbLabel
    Friend WithEvents btn_EntryRecovery As iTong.Components.tbButton
    Friend WithEvents btnRefresh As iTong.Components.tbButton
    Friend WithEvents txtNameValue As iTong.Components.tbTextBox
    Friend WithEvents pnlChangeDeviceName As iTong.Components.tbPanel
    Friend WithEvents btnChangeCancel As iTong.Components.tbButton
    Friend WithEvents btnChangeOK As iTong.Components.tbButton
    Friend WithEvents btnRename As iTong.Components.tbButton
    Friend WithEvents pnlScreenshot As iTong.Components.tbPanel
    Friend WithEvents btnScreenshot As iTong.Components.tbButton
    Friend WithEvents btnScreenshotSetUp As iTong.Components.tbButton
    Friend WithEvents btnScreenshotPlay As iTong.Components.tbButton
    Friend WithEvents btnScreenshotWeiBo As iTong.Components.tbButton
    Friend WithEvents cmsSetting As tbContextMenuStrip
    Friend WithEvents tsmiScreenshot As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiScreenshotShell As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lblDownloadProgress As iTong.Components.tbLabel
    Friend WithEvents btnDisconnect As iTong.Components.tbButton
    Friend WithEvents btnJailbreak As iTong.Components.tbButton
    Friend WithEvents lblData As System.Windows.Forms.Label
    Friend WithEvents lblSystem As System.Windows.Forms.Label
    Friend WithEvents lblKeyActive As System.Windows.Forms.Label
    Friend WithEvents lblActive As System.Windows.Forms.Label
    Friend WithEvents lblKeyJB As System.Windows.Forms.Label
    Friend WithEvents lblJB As System.Windows.Forms.Label
    Friend WithEvents lblKeyProduct As System.Windows.Forms.Label
    Friend WithEvents lblProduct As System.Windows.Forms.Label
    Friend WithEvents lblKeyColor As System.Windows.Forms.Label
    Friend WithEvents lblColor As System.Windows.Forms.Label
    Friend WithEvents lblKeySN As System.Windows.Forms.Label
    Friend WithEvents lblSN As System.Windows.Forms.Label
    Friend WithEvents lblKeyMode As System.Windows.Forms.Label
    Friend WithEvents lblMode As System.Windows.Forms.Label
    Friend WithEvents lblKeyFM As System.Windows.Forms.Label
    Friend WithEvents lblFM As System.Windows.Forms.Label
    Friend WithEvents lblKeyPN As System.Windows.Forms.Label
    Friend WithEvents lblPN As System.Windows.Forms.Label
    Friend WithEvents pnlDetail As System.Windows.Forms.Panel
    Friend WithEvents btnDetail As iTong.Components.tbButton
    Friend WithEvents pgbSystem As tbControlBar
    Friend WithEvents pgbData As tbControlBar
    Friend WithEvents lblSystemUsed As System.Windows.Forms.Label
    Friend WithEvents lblDataUsed As System.Windows.Forms.Label
    Friend WithEvents lblIMEI As System.Windows.Forms.Label
    Friend WithEvents lblKeyIMEI As System.Windows.Forms.Label
    Friend WithEvents lblWifi As System.Windows.Forms.Label
    Friend WithEvents lblKeyWifi As System.Windows.Forms.Label
    Friend WithEvents btnCopySN As iTong.Components.tbButton
    Friend WithEvents lblCopyInfo As iTong.Components.tbLabel
    Friend WithEvents tmrShowCopyInfo As System.Windows.Forms.Timer
    Friend WithEvents lblCharging As System.Windows.Forms.Label
    Friend WithEvents picCharging As System.Windows.Forms.PictureBox
    Friend WithEvents tmrRefresh As System.Windows.Forms.Timer
    Friend WithEvents bgwStartJailbreak As System.ComponentModel.BackgroundWorker
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents lblKeySaleArea As System.Windows.Forms.Label
    Friend WithEvents lblKeyExpireDate As System.Windows.Forms.Label
    Friend WithEvents lblKeylBuyDate As System.Windows.Forms.Label
    Friend WithEvents lblSaleArea As System.Windows.Forms.Label
    Friend WithEvents lblExpireDate As System.Windows.Forms.Label
    Friend WithEvents lblBuyDate As System.Windows.Forms.Label
    Friend WithEvents lblBlueAddress As System.Windows.Forms.Label
    Friend WithEvents lblKeyBlueAddress As System.Windows.Forms.Label
    Friend WithEvents btnCopyIMEI As iTong.Components.tbButton
    Friend WithEvents lblKeyBaseband As System.Windows.Forms.Label
    Friend WithEvents lblBaseband As System.Windows.Forms.Label
    Friend WithEvents lblUDID As System.Windows.Forms.Label
    Friend WithEvents lblKeyUDID As System.Windows.Forms.Label
    Friend WithEvents lblKeyFindMyPhone As System.Windows.Forms.Label
    Friend WithEvents btnFindMyPhone As iTong.Components.tbButton
    Friend WithEvents lblFindMyPhone As System.Windows.Forms.Label
    Friend WithEvents btnAgain As iTong.Components.tbButton

End Class
