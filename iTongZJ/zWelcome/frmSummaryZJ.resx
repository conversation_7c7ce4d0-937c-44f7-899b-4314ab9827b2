<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btn_normal.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEgAAAAYCAYAAABZY7uwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADIGlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20v
        eGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxu
        czpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1w
        OkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9
        InhtcC5paWQ6N0ExMzM2OUI2RTgzMTFFMUE2NUQ4RkMwRkE5Njg0ODAiIHhtcE1NOkRvY3VtZW50SUQ9
        InhtcC5kaWQ6N0ExMzM2OUM2RTgzMTFFMUE2NUQ4RkMwRkE5Njg0ODAiPiA8eG1wTU06RGVyaXZlZEZy
        b20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDo3QTEzMzY5OTZFODMxMUUxQTY1RDhGQzBGQTk2ODQ4
        MCIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDo3QTEzMzY5QTZFODMxMUUxQTY1RDhGQzBGQTk2ODQ4
        MCIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVu
        ZD0iciI/PtpkAfMAAAHkSURBVFhH7di/TgJBEAZwBCFESAxIYQSDNhYYwWiMIRJNxD+FWKiVMTEGO7Ww
        sOcFfAF7Wws73+6cWTS3O353y8FxNlv8Ynbdb5KZEI69lOd5Tgi46fjgpuODm44Pbk7RDFkiTbIdAZ/n
        HOdRXWV2fjGTLVVPs5X6S66yMshVVkewMuDznOO8rGksElAlaACj4jyqq2QX6sf52sbj3FrnutDoXhXX
        Ty5s+Byf5xznZU1jkYAWQY2PivOorkJN9out815p//6gfPjQKXef9qzoHJ/nHOdlTWORANR0VKiuwp8E
        NRw0CAvOcV7WNBZSauf2zQblQhjNLveeP2xkhqC6SqFxdKk3nd69+7TRz3Ne1jQWEhqIhHIhjGbRQCSZ
        Iaiuwt8pesNoIJJ+nvOyprGQfgawFSSOAdHfmyBxDOj1/asfJK4Bwf+xmAZk7OniGJC+ltyA3IBMbkBD
        qK7iBjSE6ir/MiAblAthNMsDsJEZguoqaEA2+vnIAyLw8S6gXBDZLHy8CzKD6ipyQOjRLunnxxlQ3KZ6
        F6Nfwmd6w1FxXtY0FgmY9DZfI6iuQnep1oR3sU1Z01gkgN/ncJPjvA/iXOj7ILqNZ+hW3qBm2xFv823O
        5WvNtKxpLJy/4Kbjg5vOLy/1DXS5rbOTehFFAAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="cmsSetting.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="pgbData.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADZmlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4zLWMwMTEgNjYuMTQ1NjYxLCAyMDEyLzAyLzA2LTE0OjU2OjI3ICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNv
        bS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlw
        ZS9SZXNvdXJjZVJlZiMiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1w
        TU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOkZFREJCQzk5N0FDNEUzMTE4RTE1Q0REMEQyNDNE
        Q0Q2IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkRFNjBFNTNCQzQ3RDExRTNBMEY5ODZFMTlBQjgx
        RjNDIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkRFNjBFNTNBQzQ3RDExRTNBMEY5ODZFMTlBQjgx
        RjNDIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDUzYgKFdpbmRvd3MpIj4gPHhtcE1N
        OkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MDNFQjE1QUQ3REM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RkVEQkJDOTk3QUM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/
        eHBhY2tldCBlbmQ9InIiPz6GepxFAAAAXklEQVQoU7XRMQ6AMAxD0V6mai/fCzExd0HAAYz/HpBaleFt
        sZUoads7sjU7TYPIkM2SEmXFDouGR9BRKKQ9GpjRKJw58839S+Hyk6utekqlEHyaTS+Lhr+QIVskpQeb
        8Di5DbtoagAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pgbData.tbDotImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADYAAAASCAYAAAAQeC39AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADZmlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4zLWMwMTEgNjYuMTQ1NjYxLCAyMDEyLzAyLzA2LTE0OjU2OjI3ICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNv
        bS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlw
        ZS9SZXNvdXJjZVJlZiMiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1w
        TU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOjFDNzIzMzYwN0JDNEUzMTE4RTE1Q0REMEQyNDNE
        Q0Q2IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkM0OUU3Qjg4QzQ3QjExRTNBMDU5RUExNTNCQzMx
        M0NDIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkM0OUU3Qjg3QzQ3QjExRTNBMDU5RUExNTNCQzMx
        M0NDIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDUzYgKFdpbmRvd3MpIj4gPHhtcE1N
        OkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MUQ3MjMzNjA3QkM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MUM3MjMzNjA3QkM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/
        eHBhY2tldCBlbmQ9InIiPz6s0zEOAAADU0lEQVRYR82WSUxTQRjHS+lDkIi4YAIt0IpYUGSJlJiQ6EmW
        o2DiQRIvmnDRixdjiFw0RDTGBbQmVUkw4hISDkYgQFjSUtrSYisKaq0LwQUNRpa2LH3j9z3eM/T1dYGL
        70v+6WTefL/ON/PNNyOxjbrWrbhDVaJVwGTBokBSkAwUA4plhW3sw29R4QLDMezYkBwh39XCMezYNXH4
        QUml8YkxD5+2HTSYHXaL/d2i9dUHgoL2gsFsH2561KqBcRSO5cM4iYHjH9SW5PjuAdNjdPw29Zt4vIvE
        R9PE56OhvUC+/pgGqJPu7B3UShOS4vgwBigSzr+gorcpNrb36Js/TkyRebeXBLOZOTcBH/K8a6AxACYi
        DloUbnd9g6582OEkc/Me1iW4zcy6icXxnq6pu14M/pgGeA7ExoHV2Z6a2NFrfI3bzdkSbPdtvYtUPDAx
        wjb2cYZp8KLHYI7empKAjJAc609S2epihO31cBaXaXKx7zPRaG2MsI19nAlwJDKZPCtNbxldwhzm7I7B
        RUq0Bj/phj6xXwmTHgMmh1cmVyuQEZQDgZS2OP107+Uv9mvknEv9X4j6hsVPV/UT7FdBjiRGlr4vG7ed
        hoPJWSXsEj8w7ONsedlHTCNvafBVIyMY5yjsEj8w7OMsUk7R3ZGAwLCPMwGOJJZS5uZj5VnFCRvYEoCG
        RsZp8M0FxoZgnHCBRcoJF5gAZwXUP2Rfcyp2661e/oQCOAKpqOOlYiQcoVS8wktFHmdl65tb211rKR6T
        cFh1LW2j/BQS5IQoHpFywhUPAQ4cVkV2asnx6rOQo2R2Lnx5/QPl1Wgbo4srTlSDrxwZIuRAeU1K30zt
        LDhQe03bi5fdbIi7A+8MzP+a+sZ2SlWg8SvT4uIwlxmFZZLKKCw/f7mx22gbp/FecHsWmGqDBxNzePL7
        NKzMOH2u7mYXlbG/FHxS0JdliIsDKwK/8C6D5wjkp4raVViWV3astqHpmauz3+I1WsfoweE3dEef2XPr
        /hPnnsOVFxhI2l6l0FNILBy/RzC+z2SKrGQYqKEyNUeozKJToDOg06CTTB9sN64MfzKrJyUGTgAMJIve
        oUqQpeXIKWWemlLl5zBS5u7Ggwl/tgnG4HYHTIbT/+dUSf4CbQnJ0oRkD1YAAAAASUVORK5CYII=
</value>
  </data>
  <data name="pgbData.tbDownloadImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADZmlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4zLWMwMTEgNjYuMTQ1NjYxLCAyMDEyLzAyLzA2LTE0OjU2OjI3ICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNv
        bS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlw
        ZS9SZXNvdXJjZVJlZiMiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1w
        TU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOjZEMTc1NzgzNzhDNEUzMTE4RTE1Q0REMEQyNDNE
        Q0Q2IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkNDQjhENEJDQzQ3RDExRTM4RTE5RTZFNEJCOEU1
        QTU1IiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkNDQjhENEJCQzQ3RDExRTM4RTE5RTZFNEJCOEU1
        QTU1IiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDUzYgKFdpbmRvd3MpIj4gPHhtcE1N
        OkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MDJFQjE1QUQ3REM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6NkQxNzU3ODM3OEM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/
        eHBhY2tldCBlbmQ9InIiPz4svkeTAAAAX0lEQVQoU7XRwQ2AMAxD0S5TtSOxI+o+3QIBAxj/e0BqVQ7v
        FluJkra9I1uz0zSIDNksKVFW7LBoeAQdhULao4EZjcKZM9/cvxQuP7naqqdUCsGn2fSyaPgLGbJFUnoA
        0iO8nKYjHSkAAAAASUVORK5CYII=
</value>
  </data>
  <data name="pgbSystem.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADZmlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4zLWMwMTEgNjYuMTQ1NjYxLCAyMDEyLzAyLzA2LTE0OjU2OjI3ICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNv
        bS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlw
        ZS9SZXNvdXJjZVJlZiMiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1w
        TU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOkZFREJCQzk5N0FDNEUzMTE4RTE1Q0REMEQyNDNE
        Q0Q2IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkRFNjBFNTNCQzQ3RDExRTNBMEY5ODZFMTlBQjgx
        RjNDIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkRFNjBFNTNBQzQ3RDExRTNBMEY5ODZFMTlBQjgx
        RjNDIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDUzYgKFdpbmRvd3MpIj4gPHhtcE1N
        OkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MDNFQjE1QUQ3REM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RkVEQkJDOTk3QUM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/
        eHBhY2tldCBlbmQ9InIiPz6GepxFAAAAXklEQVQoU7XRMQ6AMAxD0V6mai/fCzExd0HAAYz/HpBaleFt
        sZUoads7sjU7TYPIkM2SEmXFDouGR9BRKKQ9GpjRKJw58839S+Hyk6utekqlEHyaTS+Lhr+QIVskpQeb
        8Di5DbtoagAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pgbSystem.tbDotImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADYAAAASCAYAAAAQeC39AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADZmlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4zLWMwMTEgNjYuMTQ1NjYxLCAyMDEyLzAyLzA2LTE0OjU2OjI3ICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNv
        bS94YXAvMS4wL21tLyIgeG1sbnM6c3RSZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlw
        ZS9SZXNvdXJjZVJlZiMiIHhtbG5zOnhtcD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wLyIgeG1w
        TU06T3JpZ2luYWxEb2N1bWVudElEPSJ4bXAuZGlkOjFDNzIzMzYwN0JDNEUzMTE4RTE1Q0REMEQyNDNE
        Q0Q2IiB4bXBNTTpEb2N1bWVudElEPSJ4bXAuZGlkOkM0OUU3Qjg4QzQ3QjExRTNBMDU5RUExNTNCQzMx
        M0NDIiB4bXBNTTpJbnN0YW5jZUlEPSJ4bXAuaWlkOkM0OUU3Qjg3QzQ3QjExRTNBMDU5RUExNTNCQzMx
        M0NDIiB4bXA6Q3JlYXRvclRvb2w9IkFkb2JlIFBob3Rvc2hvcCBDUzYgKFdpbmRvd3MpIj4gPHhtcE1N
        OkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6MUQ3MjMzNjA3QkM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6MUM3MjMzNjA3QkM0RTMxMThFMTVD
        REQwRDI0M0RDRDYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/
        eHBhY2tldCBlbmQ9InIiPz6s0zEOAAADU0lEQVRYR82WSUxTQRjHS+lDkIi4YAIt0IpYUGSJlJiQ6EmW
        o2DiQRIvmnDRixdjiFw0RDTGBbQmVUkw4hISDkYgQFjSUtrSYisKaq0LwQUNRpa2LH3j9z3eM/T1dYGL
        70v+6WTefL/ON/PNNyOxjbrWrbhDVaJVwGTBokBSkAwUA4plhW3sw29R4QLDMezYkBwh39XCMezYNXH4
        QUml8YkxD5+2HTSYHXaL/d2i9dUHgoL2gsFsH2561KqBcRSO5cM4iYHjH9SW5PjuAdNjdPw29Zt4vIvE
        R9PE56OhvUC+/pgGqJPu7B3UShOS4vgwBigSzr+gorcpNrb36Js/TkyRebeXBLOZOTcBH/K8a6AxACYi
        DloUbnd9g6582OEkc/Me1iW4zcy6icXxnq6pu14M/pgGeA7ExoHV2Z6a2NFrfI3bzdkSbPdtvYtUPDAx
        wjb2cYZp8KLHYI7empKAjJAc609S2epihO31cBaXaXKx7zPRaG2MsI19nAlwJDKZPCtNbxldwhzm7I7B
        RUq0Bj/phj6xXwmTHgMmh1cmVyuQEZQDgZS2OP107+Uv9mvknEv9X4j6hsVPV/UT7FdBjiRGlr4vG7ed
        hoPJWSXsEj8w7ONsedlHTCNvafBVIyMY5yjsEj8w7OMsUk7R3ZGAwLCPMwGOJJZS5uZj5VnFCRvYEoCG
        RsZp8M0FxoZgnHCBRcoJF5gAZwXUP2Rfcyp2661e/oQCOAKpqOOlYiQcoVS8wktFHmdl65tb211rKR6T
        cFh1LW2j/BQS5IQoHpFywhUPAQ4cVkV2asnx6rOQo2R2Lnx5/QPl1Wgbo4srTlSDrxwZIuRAeU1K30zt
        LDhQe03bi5fdbIi7A+8MzP+a+sZ2SlWg8SvT4uIwlxmFZZLKKCw/f7mx22gbp/FecHsWmGqDBxNzePL7
        NKzMOH2u7mYXlbG/FHxS0JdliIsDKwK/8C6D5wjkp4raVViWV3astqHpmauz3+I1WsfoweE3dEef2XPr
        /hPnnsOVFxhI2l6l0FNILBy/RzC+z2SKrGQYqKEyNUeozKJToDOg06CTTB9sN64MfzKrJyUGTgAMJIve
        oUqQpeXIKWWemlLl5zBS5u7Ggwl/tgnG4HYHTIbT/+dUSf4CbQnJ0oRkD1YAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="tmrShowCopyInfo.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>134, 17</value>
  </metadata>
  <metadata name="tmrRefresh.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>289, 15</value>
  </metadata>
  <metadata name="bgwStartJailbreak.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>406, 15</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>45</value>
  </metadata>
</root>