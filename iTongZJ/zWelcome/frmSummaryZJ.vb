﻿Imports System.Threading
Imports System.net
Imports System.Text
Imports System.IO
Imports System.Runtime.InteropServices
Imports iTong.CoreFoundation

Public Class frmSummaryZJ
    Private _tdUpdateSaleInfo As Thread

    Private _bytkey As Byte() = New Byte() {&HA3, &HB4, &H62, &H5D, &HC8, &H59, &H2D, &HEC}
    Private _nodePhone As tbTreeNode = Nothing
    Private _batteryIndex As Integer = 0

    Private _DeviceProduct As String = ""
    Private _DeviceColor As PHBDeviceColorVariation = PHBDeviceColorVariation.Default
    Private _infoDevice As New tbDeviceInfo
    Private _IsRecoverMode As Boolean = False

    Private mCapacityShow As DeviceCapacityShow = Nothing
    Private mModelNumber As String = String.Empty
    Private mRegionInfo As String = String.Empty
    Private mDownManage As MultiThreadDownload
    Public mDataSource As DataTable  '展示设备详细信息的数据源

    Private mThreadSreenshot As Thread
    Private mActivatedDevice As Boolean = True          '记录当前设备有没有激活
    Private mFormDetail As frmDetail
    Private mbtnLastClick As tbButton = Nothing
    Private mDictValue As New Dictionary(Of DeviceInfoKey, String)

    Private mTdGetPurchase As Thread

    Private mSaleInfoHelper As SaleInfoHelper = Nothing

    Private mPersonSignatureHelper As PersonSignatureHelper

#Region "--- 初始化，关闭窗体 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As Object)
        MyBase.New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me._IsRecoverMode = (TypeOf device Is iPhoneRecoveryDevice)
        Me.txtNameValue.MaxLength = 61

        If Me._IsRecoverMode Then
            Me.mDeviceRecovery = device
        Else
            Me.mDevice = device
        End If

        Me.InitJailbreakTool()
    End Sub

    Public Overrides Sub CheckInit()
        MyBase.CheckInit()

        Me.mSaleInfoHelper = SaleInfoHelper.GetInstance(Me.mDevice)

        If Not _IsRecoverMode AndAlso Me.mDevice IsNot Nothing Then  '' Added by Utmost20141015   '' 避免拔掉设备后，程序延时还读取设备属性
            Me.InitCapacityShow()
            Me.mCapacityShow.LoadCapacity()

            RemoveHandler mDevice.DeviceNameChanged, AddressOf OnDeviceNameChanged
            AddHandler mDevice.DeviceNameChanged, AddressOf OnDeviceNameChanged
        End If

        Me.SetControls()

        If Not Me._IsRecoverMode AndAlso _
          Me.mDevice IsNot Nothing Then

            If Not Me.tmrRefresh.Enabled Then
                '初始化电量信息
                Me.InitCharging()
            End If
        End If

    End Sub

    Private Sub InitCapacityShow()
        If Me.mDevice IsNot Nothing Then  '' Added by Utmost20141015   '' 避免拔掉设备后，程序延时还读取设备属性
            If mCapacityShow IsNot Nothing Then
                RemoveHandler mCapacityShow.CapacityCallback, AddressOf OnCapacityCallback
                RemoveHandler mCapacityShow.CapacityLoadCompleted, AddressOf OnCapacityLoadCompleted
            End If

            mCapacityShow = DeviceCapacityShow.GetInstance(Me.mDevice)

            If mCapacityShow IsNot Nothing Then   '' Added by Utmost20141015   '' 避免拔掉设备后，程序延时还读取设备属性
                AddHandler mCapacityShow.CapacityCallback, AddressOf OnCapacityCallback
                AddHandler mCapacityShow.CapacityLoadCompleted, AddressOf OnCapacityLoadCompleted
            End If
        End If
    End Sub

    Private Sub ClearLabel()
        Me.lblActive.Text = String.Empty
        Me.lblMode.Text = String.Empty
        Me.lblPN.Text = String.Empty
        Me.lblProduct.Text = String.Empty
        Me.lblSN.Text = String.Empty
        Me.lblColor.Text = String.Empty
        Me.lblFM.Text = String.Empty
        Me.lblJB.Text = String.Empty
        Me.lblUDID.Text = String.Empty
        Me.lblIMEI.Text = String.Empty
        Me.lblFindMyPhone.Text = String.Empty
        Me.lblBaseband.Text = String.Empty
        Me.lblBlueAddress.Text = String.Empty
        Me.lblWifi.Text = String.Empty
        Me.lblBuyDate.Text = String.Empty
        Me.lblExpireDate.Text = String.Empty
        Me.lblSaleArea.Text = String.Empty


        Me.lblSystem.Text = String.Empty
        Me.lblData.Text = String.Empty

        Me.lblSystem.Text = Me.Language.GetString("Welcome.Label.Loading")      '"获取中..."
        Me.lblData.Text = Me.Language.GetString("Welcome.Label.Loading")        '"获取中..."
        Me.lblSystemUsed.Text = ""
        Me.lblDataUsed.Text = ""
        Me.pgbSystem.tbPlayValue = 0
        Me.pgbData.tbPlayValue = 0

        'Me.lblUUID.Text = Me.Language.GetString("Welcome.Label.Loading")        '"获取中..."
        'Me.lblPurchase.Text = Me.Language.GetString("Welcome.Label.Loading")    '"获取中..."
        'Me.lblActiveDate.Text = Me.Language.GetString("Welcome.Label.Loading")  '"获取中..."
        'Me.lblProtect.Text = Me.Language.GetString("Welcome.Label.Loading")     '"获取中..."
    End Sub

    Private Sub SetControls()
        Me.pnlScreenshot.Visible = Not Me._IsRecoverMode
        Me.btn_EntryRecovery.Visible = Me._IsRecoverMode
        'Me.tbCapacity.Visible = Not Me._IsRecoverMode
        Me.pnlDetail.Visible = Not Me._IsRecoverMode
        Me.btnRefresh.Visible = Not Me._IsRecoverMode
        Me.btnDetail.Visible = Not Me._IsRecoverMode
        Me.btnRename.Visible = Not Me._IsRecoverMode
        Me.btnDisconnect.Visible = Not Me._IsRecoverMode
        Me.pnlScreenshot.Visible = Not Me._IsRecoverMode

        Me.ClearLabel()

        If Me._IsRecoverMode Then
            Me.LoadPhoneInfoOnRecoverMode()

        Else
            Me.LoadPhoneInfo()
        End If
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)

        Me.lblDownloadProgress.Visible = False
        Me.lblDownloadProgress.Text = String.Empty

        Me._IsRecoverMode = False

        If Me.mPersonSignatureHelper Is Nothing Then
            Me.mPersonSignatureHelper = PersonSignatureHelper.GetInstance(Me.mDevice)
            Me.mPersonSignatureHelper.Start2CheckPersonSignatureGameStore()
        End If

    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        Try
            Me.tmrRefresh.Stop()

            If Me.mfrmScreenshotPreview IsNot Nothing Then
                Me.mfrmScreenshotPreview.Close()
            End If
            If Me.frmImageView IsNot Nothing Then
                Me.frmImageView.Close()
            End If

            If Me.mFormDetail IsNot Nothing Then
                Me.mFormDetail.Close()
            End If

            If Me.mPersonSignatureHelper IsNot Nothing Then
                Me.mPersonSignatureHelper.Dispose()
                Me.mPersonSignatureHelper = Nothing
            End If

        Catch
        End Try

        Try
            If Me.mThreadSreenshot IsNot Nothing AndAlso Me.mThreadSreenshot.ThreadState <> ThreadState.Stopped Then
                Me.mThreadSreenshot.Abort()
            End If
        Catch ex As Exception
        End Try

        Try
            If Me._tdUpdateSaleInfo IsNot Nothing AndAlso Me._tdUpdateSaleInfo.ThreadState <> ThreadState.Stopped Then
                Me._tdUpdateSaleInfo.Abort()
            End If
        Catch ex As Exception
        End Try

        '' Added by Utmost20141206
        If mDataSource IsNot Nothing Then
            mDataSource.Clear()
            mDataSource = Nothing
        End If

        If mCapacityShow IsNot Nothing Then
            mCapacityShow.Dispose()
            mCapacityShow = Nothing
        End If

        If mFormDetail IsNot Nothing AndAlso Not mFormDetail.IsDisposed Then
            mFormDetail.Dispose()
            mFormDetail.Close()
            mFormDetail = Nothing
        End If
    End Sub

    Public Overrides Sub OnRecoverConnect(ByVal device As iPhoneRecoveryDevice)
        MyBase.OnRecoverConnect(device)

        Me._IsRecoverMode = True
        Me.SetControls()
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.btnDetail.tbToolTip = Me.Language.GetString("Welcome.Button.DeviceDetails")                              '"Device Details"
        Me.btnDisconnect.tbToolTip = Me.Language.GetString("Welcome.Button.PopupDevice")                            '"Pop-up device"
        Me.btnRename.tbToolTip = Me.Language.GetString("Welcome.Button.ModifyDeviceName")                           '"Modify the device name"

        'Me.lblDeviceDetailInfo.Text = Me.Language.GetString("Device.Recovery.Label.DetailInformation")             '"详细信息"
        Me.btnRefresh.tbToolTip = Me.Language.GetString("Common.Button.Refresh")

        If Me._IsRecoverMode Then
            Me.btn_EntryRecovery.tbText = Me.Language.GetString("Welcome.Button.RestoreModesOut")                   '"退出恢复模式"
        Else
            Me.btn_EntryRecovery.tbText = Me.Language.GetString("Welcome.Button.RestoreModesIn")                    '"进入恢复模式"
        End If

        'Me.tbCapacity.BeginUpdate()

        'Me.tbCapacity.TotalCapacity.Text = Me.Language.GetString("Welcome.Label.TotalCapacity")                     '"容量"
        'Me.tbCapacity.AudioCapacity.Text = Me.Language.GetString("Welcome.Label.AudioCapacity")                     '"音频"
        'Me.tbCapacity.VedioCapacity.Text = Me.Language.GetString("Main.Button.Video")                               '"影片"
        'Me.tbCapacity.CameraCapacity.Text = Me.Language.GetString("Main.Button.Photo")                              '"照片"
        'Me.tbCapacity.AppCapacity.Text = Me.Language.GetString("Main.Button.App")                                   '"程序"
        'Me.tbCapacity.EBookCapacity.Text = Me.Language.GetString("Main.Button.Book")                                '"图书"
        'Me.tbCapacity.OtherCapacity.Text = Me.Language.GetString("Common.Label.Other")                              '"其它"
        'Me.tbCapacity.AvailableCapacity.Text = Me.Language.GetString("Welcome.Label.AvailableCapacity")             '"可用空间"


        '截屏
        Me.tsmiScreenshot.Text = Me.Language.GetString("Welcome.Text.ScreenshotNoShell")                            '"截屏(无外壳)"
        Me.tsmiScreenshotShell.Text = Me.Language.GetString("Welcome.Text.ScreenshotHaveShell")                     '"截屏(带外壳)"

        Me.btnScreenshot.tbToolTip = Me.Language.GetString("Welcome.Button.Screenshot")                             '"屏幕截屏"
        Me.btnScreenshotWeiBo.tbToolTip = Me.Language.GetString("Welcome.Button.SharingToWeiBo")                    '"分享截屏到微博"
        Me.btnScreenshotPlay.tbToolTip = Me.Language.GetString("Welcome.Text.ScreenshotTitle")                      '"实时桌面"
        Me.btnScreenshotSetUp.tbToolTip = Me.Language.GetString("Common.Button.Setting")                            '"设置"

        'Me.tbCapacity.EndUpdate()
        lblKeyActive.Text = Me.Language.GetString("Welcome.Label.ActivationState") & Me.Language.GetString("Common.Symbol.Colon")       '"激活状态："
        'lblKeyPurchase.Text = Me.Language.GetString("Welcome.Label.PurchaseDate") & Me.Language.GetString("Common.Symbol.Colon")        '"购买时间："
        lblKeyJB.Text = Me.Language.GetString("Welcome.Label.JailbreakStatus") & Me.Language.GetString("Common.Symbol.Colon")           '"越狱状态："
        'lblKeyActiveDate.Text = Me.Language.GetString("Welcome.Label.ActivatedDate") & Me.Language.GetString("Common.Symbol.Colon")     '"激活时间："
        lblKeyProduct.Text = Me.Language.GetString("Welcome.Label.ProductType") & Me.Language.GetString("Common.Symbol.Colon")          '"产品型号："
        'lblKeyProtect.Text = Me.Language.GetString("Welcome.Label.WarrantyDate") & Me.Language.GetString("Common.Symbol.Colon")         '"保修到期："
        lblKeyColor.Text = Me.Language.GetString("Welcome.Label.DeviceColor") & Me.Language.GetString("Common.Symbol.Colon")            '"产品颜色："
        lblKeyMode.Text = Me.Language.GetString("Welcome.Label.ModelNumber") & Me.Language.GetString("Common.Symbol.Colon")             '"模型型号："
        lblKeySN.Text = Me.Language.GetString("Welcome.Label.SerialNumber") & Me.Language.GetString("Common.Symbol.Colon")              '"序 列 号："
        lblKeyFM.Text = Me.Language.GetString("Welcome.Label.FirmwareVersion") & Me.Language.GetString("Common.Symbol.Colon")           '"固件版本："
        'lblKeyUUID.Text = Me.Language.GetString("Welcome.Label.SalesArea") & Me.Language.GetString("Common.Symbol.Colon")               '"销售区域："
        lblKeyPN.Text = Me.Language.GetString("Welcome.Label.PhoneNumber") & Me.Language.GetString("Common.Symbol.Colon")               '"电话号码："

        lblKeyWifi.Text = Me.Language.GetString("Welcome.Label.WiFiAddress") & Me.Language.GetString("Common.Symbol.Colon")             '"Wi-Fi 地址"
        lblKeyIMEI.Text = "IMEI" & Me.Language.GetString("Common.Symbol.Colon")

    End Sub

    Private tbWBPurchaseTime As tbWebBrowser = New tbWebBrowser
        

    Protected Overrides Sub InitControls()
        Me.mCanInitCheck = False

        MyBase.InitControls()

        Me.mCanInitCheck = True

        Me.lblNameValue.MaximumSize = New Size(300, 30)
        Me.lblNameValue.AutoEllipsis = True
        'Me.tbCapacity.Font = Common.CreateFont("Arial", 9, FontStyle.Regular)

        Me.CanbeMove = False

        If Me.Language.CurrentLanguage.LangName.ToLower <> "zh-cn" Then
            Me.btnJailbreak.Visible = False
        End If


        '防止查看详细按钮位置偏移
        Me.btnDetail.Location = New Point(Me.btnRename.Right + 10, Me.btnRename.Top + (Me.btnRename.Height - btnDetail.Height - 1) \ 2)

        AddHandler SaleInfoHelper.LoadPhoneSaleInfoEventHandler, AddressOf OnLoadPhoneSaleInfo

        tbWBPurchaseTime.ScriptErrorsSuppressed = True

        RemoveHandler tbWBPurchaseTime.DocumentCompleted, AddressOf OnDocumentCompleted
        AddHandler tbWBPurchaseTime.DocumentCompleted, AddressOf OnDocumentCompleted
    End Sub

    Private Sub InitCharging()
        If Me.mDevice IsNot Nothing Then
            If Me.mDevice.ConnectMode = ConnectMode.USB Then
                'Me.picMode.Image = My.Resources.icon_usb_24
            Else
                'Me.picMode.Image = My.Resources.icon_wifi_24
            End If
            Me.RefreshBattery()

        ElseIf Me.mDeviceRecovery IsNot Nothing Then
            'Me.picMode.Image = My.Resources.icon_usb_24
            Me.picCharging.Image = Nothing
            Me.lblCharging.Text = ""

        Else
            'Me.picMode.Image = Nothing
            Me.picCharging.Image = Nothing
            Me.lblCharging.Text = ""

        End If
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        RemoveHandler SaleInfoHelper.LoadPhoneSaleInfoEventHandler, AddressOf OnLoadPhoneSaleInfo

        If Me._IsRecoverMode Then
            Me.ClosingFormOnRecoverMode()
        Else
            Me.OnDisconnect(Me.mDevice)
        End If
    End Sub

    Private Sub OnDeviceNameChanged(ByVal sender As Object, ByVal e As EventArgs)
        Me.lblNameValue.Text = Me.mDevice.DeviceName

        Dim strValue As String = Me.mDevice.DeviceName
        If Not String.IsNullOrEmpty(strValue) Then
            Me.mDataSource.Rows(0).Item(1) = strValue
            Me._infoDevice.DeviceName = strValue
        End If
    End Sub

#End Region

#Region "--- RecoverMode ---"

    Private Sub TreeNode_DischargeButtonClick(ByVal sender As Object, ByVal e As tbTreeView_DischargeButtonClick_EventArgs)
        Me.btn_EntryRecovery_Click(Me.btn_EntryRecovery, New System.EventArgs)
    End Sub

    '加载设备信息
    Private Sub LoadPhoneInfoOnRecoverMode()
        Me._infoDevice = tbDeviceCache.GetInstanse().GetLastDeviceInfo(Me.mDeviceRecovery.SerialNumber)
        If _infoDevice Is Nothing Then
            _infoDevice = New tbDeviceInfo()
            _infoDevice.SerialNumber = Me.mDeviceRecovery.SerialNumber
            _infoDevice.DeviceName = Me.Language.GetString("Common.Label.Unknow")
            '不适用
            _infoDevice.ActivationState = Me.Language.GetString("Welcome.Label.Inapplicability")
            _infoDevice.JailbreakStatus = Me.Language.GetString("Welcome.Label.Inapplicability")
            _infoDevice.ProductVersion = Me.Language.GetString("Welcome.Label.Inapplicability")
            _infoDevice.ModelNumber = Me.Language.GetString("Welcome.Label.Inapplicability")
            _infoDevice.DeviceColor = Me.Language.GetString("Welcome.Label.Inapplicability")

            Me.lblNameValue.Text = _infoDevice.DeviceName
            'Me.lblVersion.Text = String.Empty
        Else
            Me.SetMainInfo()
        End If
    End Sub

    Private Sub ClosingFormOnRecoverMode()

        Me.mDeviceRecovery = Nothing
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub lblNameValue_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblNameValue.SizeChanged
        'Debug.Print(Me.lblNameValue.Text)
        Me.btnRename.Location = New Point(Me.lblNameValue.Right + 20, Me.lblNameValue.Top + (Me.lblNameValue.Height - btnRename.Height - 1) \ 2)
        Me.btnDetail.Location = New Point(Me.btnRename.Right + 10, Me.btnRename.Top + (Me.btnRename.Height - btnDetail.Height - 1) \ 2)
        Me.btnDisconnect.Location = New Point(Me.btnDetail.Right + 10, Me.btnDetail.Top + (Me.btnDetail.Height - btnDisconnect.Height - 1) \ 2)

        If Me.btnDetail.Right - Me.lblNameValue.Left > Me.pnlChangeDeviceName.Width Then
            Me.pnlChangeDeviceName.Size = New Size(Me.btnDetail.Right - Me.lblNameValue.Left, Me.pnlChangeDeviceName.Height)
        End If

        Me.pnlChangeDeviceName.Location = New Point(Me.lblNameValue.Left, Me.lblNameValue.Bottom - pnlChangeDeviceName.Height + 2)
    End Sub

    Private Sub btn_EntryRecovery_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btn_EntryRecovery.Click

        If Me._IsRecoverMode Then
            If Me.mDeviceRecovery IsNot Nothing Then
                Me.mDeviceRecovery.ExitRecoveryMode()
            End If
        Else
            If Me.mDevice IsNot Nothing AndAlso _
               Me.mDevice.IsConnected AndAlso _
               MessageBox.Show(Me.Language.GetString("Welcome.Message.IfInRestoreMode"), _
                                Me.Language.GetString("Welcome.Button.RestoreModesIn"), _
                                MessageBoxButtons.YesNo, MessageBoxIcon.Question) = Windows.Forms.DialogResult.Yes Then
                '"此操作将会让您的手机进入到恢复模式！是否仍要继续？"
                '"进入恢复模式"
                Me.mDevice.EnterRecoveryMode()

                Me.btn_EntryRecovery.Text = Me.Language.GetString("Welcome.Button.InRestoreingMode") '"正在进入恢复模式..."
                Me.btn_EntryRecovery.Enabled = False
            End If
        End If
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        Me.btnRefresh.Visible = False
        Me.picLoading.Visible = True

        mCapacityShow = DeviceCapacityShow.GetInstance(Me.mDevice)
        If Me.mCapacityShow IsNot Nothing Then
            Me.mCapacityShow.LoadCapacity(True)
        End If
    End Sub

    Private Sub btnDetail_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDetail.Click
        'Me.LoadDetailInfo(Me._infoDevice)

        If mFormDetail IsNot Nothing Then
            mFormDetail.Close()
            mFormDetail.Dispose()
        End If

        mFormDetail = New frmDetail(Me.mApplication, Me.mDataSource, Me)
        mFormDetail.StartPosition = FormStartPosition.CenterParent

        mFormDetail.ShowDialog(Me.mApplication)
    End Sub

    Private Sub btnDisconnect_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDisconnect.Click
        If Me.mDevice IsNot Nothing Then
            MobileDeviceManager.Instance().OnRemoveDevice(Me.mDevice, 0)
        ElseIf Me.mDeviceRecovery IsNot Nothing Then
            MobileDeviceManager.Instance().OnRemoveDeviceRecovery(Me.mDeviceRecovery)
        End If
    End Sub

    Private Sub btnCopySN_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCopySN.Click
        Try
            If Me.mDevice IsNot Nothing Then
                Clipboard.SetDataObject(Me.mDevice.SerialNumber)

                If Not Me.tmrShowCopyInfo.Enabled Then
                    Me.lblCopyInfo.Location = New Point(Me.btnCopySN.Left + (Me.btnCopySN.Width - Me.lblCopyInfo.Width) \ 2, Me.btnCopySN.Top - Me.lblCopyInfo.Height - 3)
                    Me.lblCopyInfo.Visible = True

                    Me.tmrShowCopyInfo.Start()
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnCopySN_Click")
        End Try
    End Sub

    Private Sub btnCopyIMEI_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCopyIMEI.Click
        Try
            If Me.mDevice IsNot Nothing Then
                Clipboard.SetDataObject(Me.mDevice.IMEI)

                If Not Me.tmrShowCopyInfo.Enabled Then
                    Me.lblCopyInfo.Location = New Point(Me.btnCopyIMEI.Left + (Me.btnCopyIMEI.Width - Me.lblCopyInfo.Width) \ 2, Me.btnCopyIMEI.Bottom + +3) 'Me.lblCopyInfo.Height
                    Me.lblCopyInfo.Visible = True

                    Me.tmrShowCopyInfo.Start()
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnCopyIMEI_Click")
        End Try
    End Sub

    Private Sub btnFindMyPhone_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFindMyPhone.Click
        Common.OpenExplorer("https://www.icloud.com/activationlock/")
    End Sub

    Private mTickCount As Integer = 0
    Private Sub tmrShowCopyInfo_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrShowCopyInfo.Tick
        Me.mTickCount = Me.mTickCount + 1

        If Me.mTickCount = 3 Then
            Me.tmrShowCopyInfo.Stop()
            Me.ChangelblCopyInfoState(False)
            Me.mTickCount = 0
        End If
    End Sub

    Private Delegate Sub ChangelblCopyInfoStateHandler(ByVal blnVisible As Boolean)
    Private Sub ChangelblCopyInfoState(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New ChangelblCopyInfoStateHandler(AddressOf ChangelblCopyInfoState), blnVisible)
        Else
            Me.lblCopyInfo.Visible = blnVisible
        End If
    End Sub

    Private Sub btnAgain_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAgain.Click
        LoadPhoneSaleInfo()
    End Sub

#End Region

#Region "--- 内部函数 ---"

    Private Function GetDeviceBackupPath() As String
        Dim pathOnPhone As String = "/ApplicationArchives/"

        If Not Me.mDevice.ExistsByAFC(pathOnPhone) Then
            Return String.Empty
        End If

        Return pathOnPhone
    End Function

    '判断是否有备份缓存
    Private Function CheckHasBackupCache() As Boolean
        Dim pathOnPhone As String = Me.GetDeviceBackupPath()

        If pathOnPhone.Length > 0 Then
            Return (Me.mDevice.GetFiles(pathOnPhone, True).Length > 0)
        End If

        Return False
    End Function

    ''加载销售地信息
    'Private Sub LoadPhoneSaleInfo()
    '    Try
    '        If Me._tdUpdateSaleInfo IsNot Nothing AndAlso Me._tdUpdateSaleInfo.ThreadState <> ThreadState.Stopped Then
    '            Me._tdUpdateSaleInfo.Abort()
    '        End If
    '    Catch
    '    End Try

    '    Me._tdUpdateSaleInfo = New Thread(AddressOf LoadPhoneSaleInfoInThread)
    '    Me._tdUpdateSaleInfo.IsBackground = True
    '    Me._tdUpdateSaleInfo.Start()
    'End Sub

    'Private Delegate Sub UpdateSaleInfoHandler(ByVal dicSalesInfo As SalesInfoPara, ByVal strSaleArea As String)
    'Private Sub UpdateSaleInfo(ByVal dicSalesInfo As SalesInfoPara, ByVal strSaleArea As String)
    '    Try
    '        If Me.InvokeRequired Then
    '            Me.Invoke(New UpdateSaleInfoHandler(AddressOf UpdateSaleInfo), dicSalesInfo, strSaleArea)
    '        Else
    '            If dicSalesInfo Is Nothing Then
    '                '未知
    '                Me.lblPurchase.Text = Me.Language.GetString("Common.Label.Unknow")
    '                Me.lblActiveDate.Text = Me.Language.GetString("Common.Label.Unknow")
    '                '"已过保"
    '                Me.lblProtect.Text = Me.Language.GetString("Common.Label.Unknow")
    '                Me.lblUUID.Text = Me.Language.GetString("Common.Label.Unknow")
    '            Else
    '                '未知
    '                Me.lblPurchase.Text = IIf(String.IsNullOrEmpty(dicSalesInfo.PurchaseDate), Me.Language.GetString("Common.Label.Unknow"), dicSalesInfo.PurchaseDate)
    '                Me.lblActiveDate.Text = IIf(String.IsNullOrEmpty(dicSalesInfo.LastUnbrickDate), Me.Language.GetString("Common.Label.Unknow"), dicSalesInfo.LastUnbrickDate)
    '                '"已过保"
    '                Me.lblProtect.Text = IIf(String.IsNullOrEmpty(dicSalesInfo.CovEndDate), IIf(String.IsNullOrEmpty(dicSalesInfo.PurchaseDate), Me.Language.GetString("Common.Label.Unknow"), Me.Language.GetString("Welcome.Label.OverInsurance")), dicSalesInfo.CovEndDate)
    '                Me.lblUUID.Text = IIf(String.IsNullOrEmpty(strSaleArea), Me.Language.GetString("Common.Label.Unknow"), strSaleArea)
    '            End If
    '        End If
    '    Catch ex As Exception
    '    End Try
    'End Sub

    ''请求销售地
    'Private Sub LoadPhoneSaleInfoInThread(ByVal obj As Object)
    '    Try
    '        Dim dicSalesInfo As SalesInfoPara = IniSetting.GetSalesInfo(Me.mDevice.Identifier)
    '        If dicSalesInfo Is Nothing OrElse String.IsNullOrEmpty(dicSalesInfo.PurchCountry) Then
    '            dicSalesInfo = Me.GetSaleInfoFromApple()
    '        End If

    '        If dicSalesInfo IsNot Nothing Then
    '            Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.PurchaseDate") & Me.Language.GetString("Common.Symbol.Colon"), dicSalesInfo.PurchaseDate)                                 '"Purchase Date:"
    '            '"Warranty Date:"                       "Out of Warranty"
    '            Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.WarrantyDate") & Me.Language.GetString("Common.Symbol.Colon"), IIf(String.IsNullOrEmpty(dicSalesInfo.CovEndDate), Me.Language.GetString("Welcome.Label.OutWarranty"), dicSalesInfo.CovEndDate))

    '            Dim strSalesArea As String = dicSalesInfo.PurchCountry
    '            Try
    '                If Not String.IsNullOrEmpty(dicSalesInfo.PurchCountry) Then
    '                    Dim infoArea As New System.Globalization.RegionInfo(dicSalesInfo.PurchCountry)
    '                    strSalesArea = infoArea.DisplayName
    '                End If

    '            Catch ex As Exception
    '            End Try
    '            Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.SalesArea") & Me.Language.GetString("Common.Symbol.Colon"), strSalesArea)

    '            Me.UpdateSaleInfo(dicSalesInfo, strSalesArea)

    '            IniSetting.SetSalesInfo(Me.mDevice.Identifier, dicSalesInfo)
    '        Else
    '            Me.UpdateSaleInfo(dicSalesInfo, "")
    '        End If

    '    Catch ex As Exception
    '        Debug.Print("GetSalesInfoError:" & ex.ToString)
    '    End Try
    'End Sub

    'Private Function GetSaleInfoFromApple() As SalesInfoPara
    '    Dim salesInfo As SalesInfoPara = Nothing
    '    Try
    '        Dim strValue As String = Me.mDevice.SerialNumber
    '        Dim strUrl As String = "https://selfsolve.apple.com/warrantyChecker.do?sn={0}&cb=crossDomainAjax.successResponse"

    '        '请求苹果接口
    '        Dim strResult As String = Utility.GetContentStringFromUrl(String.Format(strUrl, strValue), System.Text.Encoding.UTF8, 40000)
    '        If strResult Is Nothing OrElse strResult.Length = 0 Then
    '            Return salesInfo
    '        End If

    '        '处理返回结果
    '        Dim strCrossDomain As String = "crossDomainAjax.successResponse("

    '        If strResult.StartsWith(strCrossDomain) Then
    '            strResult = strResult.Substring(strCrossDomain.Length, strResult.Length - strCrossDomain.Length)
    '        End If
    '        If strResult.EndsWith(")") Then
    '            strResult = strResult.TrimEnd(")")
    '        End If

    '        'json转换返回结果
    '        Dim objJson As JsonObject = JsonParser.ParseString(strResult)
    '        If objJson Is Nothing OrElse objJson.Count = 0 Then
    '            '"苹果服务器返回值为空。"
    '            Return salesInfo
    '        End If

    '        salesInfo = New SalesInfoPara()
    '        If objJson.ContainsKey("PURCHASE_DATE") Then
    '            salesInfo.PurchaseDate = CType(objJson.Item("PURCHASE_DATE"), JsonString).Value
    '        Else
    '            '取不到数据
    '            salesInfo.PurchaseDate = ""
    '        End If

    '        If objJson.ContainsKey("LAST_UNBRICK_DT") Then
    '            salesInfo.LastUnbrickDate = CType(objJson.Item("LAST_UNBRICK_DT"), JsonString).Value
    '        Else
    '            '取不到数据
    '            salesInfo.LastUnbrickDate = ""
    '        End If

    '        If objJson.ContainsKey("COV_END_DATE") Then
    '            salesInfo.CovEndDate = CType(objJson.Item("COV_END_DATE"), JsonString).Value
    '        Else
    '            '取不到数据
    '            salesInfo.CovEndDate = ""
    '        End If

    '        If objJson.ContainsKey("PURCH_COUNTRY") Then
    '            salesInfo.PurchCountry = CType(objJson.Item("PURCH_COUNTRY"), JsonString).Value
    '        Else
    '            '取不到数据
    '            salesInfo.PurchCountry = ""
    '        End If
    '    Catch ex As Exception
    '        Debug.Print(String.Format("从Apple官网取得销售地信息失败：{0}", ex.ToString()))
    '        salesInfo = Nothing
    '    End Try

    '    Return salesInfo
    'End Function

    '格式化型号
    'Private Function FormatProduct(ByVal strProduct As String) As String
    '    Dim strResult As String = ""

    '    If String.IsNullOrEmpty(strProduct) Then
    '        Return strResult
    '    End If

    '    Select Case strProduct
    '        Case "iPad1,1"
    '            strResult = "iPad 1"

    '        Case "iPad2,1"
    '            strResult = "iPad 2(WiFi)"

    '        Case "iPad2,2"
    '            strResult = "iPad 2(GSM)"

    '        Case "iPad2,3"
    '            strResult = "iPad 2(CDMA)"

    '        Case "iPad3,1"
    '            strResult = "New iPad(WiFi)"

    '        Case "iPad3,2"
    '            strResult = "New iPad(WiFi + 4G-AT&T)"

    '        Case "iPad3,3"
    '            strResult = "New iPad(WiFi + 4G-Verizon)"

    '        Case "iPhone1,1"
    '            strResult = "iPhone 2G"

    '        Case "iPhone1,2"
    '            strResult = "iPhone 3G"

    '        Case "iPhone2,1"
    '            strResult = "iPhone 3GS"

    '        Case "iPhone3,1"
    '            strResult = "iPhone 4"

    '        Case "iPhone3,3"
    '            strResult = "iPhone 4(CDMA)"

    '        Case "iPhone4,1"
    '            strResult = "iPhone 4S"

    '        Case "iPod1,1"
    '            strResult = "iPod Touch 1G"

    '        Case "iPod2,1"
    '            strResult = "iPod Touch 2G"

    '        Case "iPod3,1"
    '            strResult = "iPod Touch 3G"

    '        Case "iPod4,1"
    '            strResult = "iPod Touch 4"

    '        Case Else
    '            strResult = strProduct

    '    End Select

    '    Return strResult
    'End Function

    '格式化激活状态
    Private Function FormatActivationState(ByVal str As String) As String
        Dim strResult As String = ""
        Me.mActivatedDevice = True
        Select Case str
            Case "Activated"
                strResult = Me.Language.GetString("Welcome.Label.Activated")    '"已激活"

            Case "WildcardActivated"
                strResult = Me.Language.GetString("Welcome.Label.Activated")    '"已激活"

            Case "FactoryActivated"
                strResult = Me.Language.GetString("Welcome.Label.Activated")    '"已激活"

            Case "Unactivated"
                strResult = Me.Language.GetString("Welcome.Label.Unactivated")  '"未激活"
                Me.mActivatedDevice = False '记录激活状态，截屏的时候如果没激活是无法使用的。

            Case Else
                strResult = str
        End Select

        Return strResult
    End Function

    '格式化颜色
    'Private Function FormatDeviceColor(ByVal deviceColor As String) As String
    '    Dim strResult As String = ""

    '    Me._DeviceColor = deviceColor    '缓存设备的颜色，以便【我来纠错】的时候可以用到

    '    Select Case deviceColor
    '        Case PHBDeviceColorVariation.Black
    '            strResult = Me.Language.GetString("Welcome.Label.Black") '"黑色"
    '        Case PHBDeviceColorVariation.White
    '            strResult = Me.Language.GetString("Welcome.Label.White") '"白色"
    '        Case Else
    '            strResult = Me.Language.GetString("Common.Label.Unknow") '"未知"
    '    End Select

    '    Return strResult
    'End Function

    '通过序列号计算出第几周生产的
    Private Function ComputeWeekOfDeviceBySerial(ByVal strSerial As String) As String
        Dim strYear As String = ""
        '如果是iPhone4s就不进行计算
        '在恢复模式下　Me._iPhone　是为空，所以得加一个判断
        If String.IsNullOrEmpty(strSerial) = False AndAlso Me.mDevice IsNot Nothing Then

            '序列号类似880184BJ8M7，其中第三位表示年份，第四第五位表示周数
            Dim charArray As Char() = strSerial.ToCharArray()
            If charArray IsNot Nothing AndAlso charArray.Length > 5 Then
                If Char.IsDigit(charArray(2)) = False OrElse Char.IsDigit(charArray(3)) = False OrElse Char.IsDigit(charArray(4)) = False Then
                    Return ""
                End If

                Dim chYear As Char = charArray(2)
                Dim strWeek As String = charArray(3) & charArray(4)

                If chYear > "6" Then
                    strYear = "200" & chYear
                Else
                    strYear = "201" & chYear
                End If

                '"{0}年,{1}周,{2}月"
                strYear = String.Format(Me.Language.GetString("Welcome.Label.DeviceSerialOfWeek"), strYear, strWeek, ChangeWeekToDay(strYear, strWeek))

            End If
        End If
        If strYear.Length > 0 Then
            strYear = String.Format(" ({0})", strYear)
        End If
        Return strYear
    End Function

    '把第几周转换成月份格式(03月)
    Private Function ChangeWeekToDay(ByVal year As String, ByVal week As String) As String
        Dim strReturn As String = ""

        Try
            strReturn = DateAdd(DateInterval.WeekOfYear, Val(week), CDate(Val(year) & "-01-01")).ToString("MM")
        Catch ex As Exception
            Debug.Print(ex.ToString())
        End Try

        Return strReturn
    End Function

#End Region

#Region "--- 设备图片获取逻辑 ---"

    Private Sub GetDeviceImage(ByVal strProduct As String, ByVal iPhoneColor As PHBDeviceColorVariation)
        Dim objpara As Object() = New Object() {strProduct, iPhoneColor}

        Dim thd As New Thread(AddressOf GetDeviceImageFromServer)
        thd.IsBackground = True
        thd.Start(objpara)

        Dim dt As Date = Now
        Do
            Utility.WaitSeconds(0.2)
        Loop While thd.ThreadState <> ThreadState.Stopped AndAlso New TimeSpan(Now.Ticks - dt.Ticks).TotalSeconds < 3
    End Sub

    Private Sub GetDeviceImageFromServer(ByVal objPara As Object)
        Try
            Dim paras As Object() = objPara
            If paras.Length <> 2 Then
                Return
            End If
            Dim strProduct As String = paras(0)
            Dim iPhoneColor As PHBDeviceColorVariation = paras(1)
            Dim strFilePath As String = SummaryInfo.GetDeviceImage(strProduct, iPhoneColor)
            If File.Exists(strFilePath) Then
                Me.SetImageForDevice(Common.ImageFromFile(strFilePath))
            Else
                Me.SetImageForDevice(My.Resources.device_default)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetDeviceImageFromServer")
        End Try
    End Sub

    Private Delegate Sub SetImageForDeviceHandler(ByVal img As Image)
    Private Sub SetImageForDevice(ByVal img As Image)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New SetImageForDeviceHandler(AddressOf SetImageForDevice), img)
        Else
            If img IsNot Nothing Then
                Me.pbDevice.Image = img
            End If
        End If
    End Sub

#End Region

#Region "--- 修改设备名称 ---"

    Private Sub btnRename_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRename.Click
        Me.lblNameValue.Visible = False
        Me.btnRename.Visible = False
        Me.btnDisconnect.Visible = False
        Me.pnlChangeDeviceName.Visible = True

        Me.txtNameValue.Enabled = True
        Me.txtNameValue.ReadOnly = False
        Me.txtNameValue.Text = Me.lblNameValue.Text.ToString()
        Me.txtNameValue.Focus()
    End Sub

    Private Sub txtNameValue_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNameValue.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.ChangeDeviceName()
        End If
    End Sub

    Private Sub btnChangeOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnChangeOK.Click, pnlChangeDeviceName.LostFocus 'txtNameValue.LostFocus,
        Me.ChangeDeviceName()
    End Sub

    Private Sub btnChangeCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnChangeCancel.Click
        Me.ChangeDeviceName(True)

    End Sub

    Private Sub ChangeDeviceName(Optional ByVal IsCancel As Boolean = False)
        If Not Me.txtNameValue.Visible Then
            Return
        End If

        Debug.Print(Me.txtNameValue.Text & vbTab & Me.txtNameValue.Text.Length)

        If Not IsCancel Then
            Dim strText As String = Me.txtNameValue.Text.Trim()
            If strText.Length > 0 AndAlso strText <> Me.lblNameValue.Text Then
                Me.mDevice.DeviceName = Me.txtNameValue.Text.ToString()
            End If
        End If
        Me.lblNameValue.Visible = True
        Me.btnRename.Visible = True
        Me.btnDisconnect.Visible = True
        Me.pnlChangeDeviceName.Visible = False
    End Sub

    Private Sub pnlChangeDeviceName_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles pnlChangeDeviceName.Validating
        Me.ChangeDeviceName()
    End Sub

#End Region

#Region "--- 截屏插件处理事件 ---"

    Private mblnCloseImgView As Boolean = False
    Private frmImageView As tbImageViewForm = Nothing
    Private mThreadInstallPlugin As Thread = Nothing

    '截图
    Private mfrmScreenshotPreview As frmPreview = Nothing
    Private mThreadGetScreenshotForPreview As Thread

#Region "--- 视频播放 ---"

    Public Sub frmImageView_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs)
        Me.mblnCloseImgView = True
    End Sub

    Private Sub GetScreenshotPlayThread()
        Try
            While True
                If Me.mblnCloseImgView = True Then
                    Exit While
                End If
                Dim imgTemp As Image = Me.mDevice.GetScreenshotImage()
                If imgTemp IsNot Nothing Then
                    Me.SetImageForScreenshot(imgTemp)
                End If
            End While
        Catch ex As Exception

        End Try
    End Sub

    '大屏时时播放设备上的图片
    Private Sub GetScreenshotPlay()

        If Not Me.mActivatedDevice Then
            '"您的设备还没有激活，请激活后再尝试使用此功能！"
            MessageBox.Show(Me.Language.GetString("Welcome.Message.DeviceUnactivated"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        '用启动服务的方式判断是否挂载截屏插件
        If Me.StartScreenshotServer() Then
            '通览的时候先显示窗体再取图片
            frmImageView = New tbImageViewForm()

            RemoveHandler frmImageView.FormClosed, AddressOf frmImageView_FormClosed
            AddHandler frmImageView.FormClosed, AddressOf frmImageView_FormClosed

            frmImageView.ShowListView = False
            frmImageView.ShowPath = False
            frmImageView.ViewType = ViewType.Screenshot
            frmImageView.Text = Me.Language.GetString("Welcome.Text.ScreenshotTitle")                       '"实时桌面"
            frmImageView.HideButton = ToolBarButtonType.WeiBo2

            frmImageView.Show()

            Me.mblnCloseImgView = False
            If Me.mThreadSreenshot IsNot Nothing AndAlso Me.mThreadSreenshot.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadSreenshot.Abort()
                Catch ex As Exception
                End Try
            End If

            Me.mThreadSreenshot = New Thread(AddressOf GetScreenshotPlayThread)
            With Me.mThreadSreenshot
                .IsBackground = True
                .Start()
            End With
        Else
            Me.DownloadScreenshotPlugin()

        End If
    End Sub

    Private Sub btnScreenshotPlay_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScreenshotPlay.Click
        Me.mbtnLastClick = sender
        Me.SetScreenshotEnable(False)
        Me.GetScreenshotPlay()
        Me.SetScreenshotEnable(True)
    End Sub

#End Region

#Region "--- 截图 ---"

    Private Sub GetShellThread(ByVal state As Object)
        Try
            Dim strShellPath As String = state
            '"http://t.tongbu.com/shell/"
            Dim imgShell As Image = Common.DownloadImage(WebUrl.ResourceScreenShotShell & Path.GetFileName(strShellPath), 3000)
            If imgShell IsNot Nothing Then
                imgShell.Save(strShellPath, Imaging.ImageFormat.Png)
                imgShell.Dispose()
            End If
        Catch ex As Exception
        End Try
    End Sub

    '取得外壳
    Private Function GetShell(ByVal strProductType As String) As Image
        Dim imgShell As Image = Nothing

        Try
            Dim dInfo As tbDeviceInfo = tbDeviceCache.GetInstanse().GetLastDeviceInfo(Me.mDevice.SerialNumber)
            Dim strShellFilePath As String = Folder.CacheFolder & "ShellEx\"

            Folder.CheckFolder(strShellFilePath)

            Dim strShellFileName As String = ""
            '----------------------------------------------------------------------------------------
            '取得图片的名称
            If Me.mDevice.ProductType.ToLower.StartsWith("iphone1") Then
                strShellFileName = "iphone1.png"

            ElseIf Me.mDevice.ProductType.ToLower.StartsWith("iphone2") Then
                strShellFileName = "iphone2.png"

            ElseIf Me.mDevice.ProductType.ToLower.StartsWith("iphone3") Then
                If dInfo IsNot Nothing AndAlso dInfo.DeviceColor = Me.Language.GetString("Welcome.Label.White") Then
                    strShellFileName = "iphone4_white.png"
                Else
                    strShellFileName = "iphone4_black.png"
                End If

            ElseIf Me.mDevice.ProductType.ToLower.StartsWith("iphone4") Then
                If dInfo IsNot Nothing AndAlso dInfo.DeviceColor = Me.Language.GetString("Welcome.Label.White") Then
                    strShellFileName = "iphone4s_white.png"
                Else
                    strShellFileName = "iphone4s_black.png"
                End If

            ElseIf Me.mDevice.ProductType.ToLower.StartsWith("ipod1") OrElse _
            Me.mDevice.ProductType.ToLower.StartsWith("ipod2") OrElse _
            Me.mDevice.ProductType.ToLower.StartsWith("ipod3") Then
                strShellFileName = "ipod1.png"

            ElseIf Me.mDevice.ProductType.ToLower.StartsWith("ipod4") Then
                strShellFileName = "ipod4.png"

            ElseIf Me.mDevice.ProductType.ToLower.StartsWith("ipad1") Then
                strShellFileName = "ipad1.png"

            ElseIf Me.mDevice.ProductType.ToLower.StartsWith("ipad2") Then

                If dInfo IsNot Nothing AndAlso dInfo.DeviceColor = Me.Language.GetString("Welcome.Label.White") Then
                    strShellFileName = "ipad2_white.png"
                Else
                    strShellFileName = "ipad2_black.png"
                End If
            ElseIf Me.mDevice.ProductType.ToLower.StartsWith("ipad3") Then
                If dInfo IsNot Nothing AndAlso dInfo.DeviceColor = Me.Language.GetString("Welcome.Label.White") Then
                    strShellFileName = "ipad3_white.png"
                Else
                    strShellFileName = "ipad3_black.png"
                End If
                'strShellFileName = "ipad3_black.png"

            End If
            '----------------------------------------------------------------------------------

            strShellFilePath = strShellFilePath & strShellFileName
            If Not File.Exists(strShellFilePath) AndAlso Common.NetworkIsAvailable() Then
                '下载图片
                Dim thd As New Thread(AddressOf GetShellThread)
                thd.IsBackground = True
                thd.Start(strShellFilePath)

                Dim dt As Date = Now
                Do
                    Utility.WaitSeconds(0.2)
                Loop While thd.ThreadState <> ThreadState.Stopped AndAlso New TimeSpan(Now.Ticks - dt.Ticks).TotalSeconds < 3
            End If

            If File.Exists(strShellFilePath) Then
                imgShell = Common.ImageFromFile(strShellFilePath)
            End If

        Catch ex As Exception
        End Try

        Return imgShell
    End Function

    '取得带壳的iPhone图标
    Private Function GetScreenshotShell(ByVal imgSource As Image) As Image
        Dim imgShell As Image = imgSource

        If IniSetting.GetScreenshotShell() Then
            imgShell = Me.GetShell(Me.mDevice.ProductType)
            If imgShell Is Nothing Then
                Return imgSource
            End If

            ''iPad3就把大小缩放不iPad2的大小
            'If Me.mDevice.ProductType.ToLower.StartsWith("ipad3") Then
            '    imgSource = Utility.GetThumbnail(imgSource, New Size(imgSource.Width / 2, imgSource.Height / 2))
            'End If

            If imgSource.Width > imgSource.Height Then
                imgShell.RotateFlip(RotateFlipType.Rotate270FlipNone)
            End If

            Dim imgTemp As New Bitmap(imgShell.Width, imgShell.Height)
            Dim g As Graphics = Graphics.FromImage(imgTemp)
            '把背景填充白色
            g.Clear(Color.White)
            g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBilinear
            '画外框
            g.DrawImage(imgShell, New Rectangle(0, 0, imgShell.Width, imgShell.Height), New Rectangle(0, 0, imgShell.Width, imgShell.Height), GraphicsUnit.Pixel)
            '画图片
            g.DrawImage(imgSource, New Rectangle((imgShell.Width - imgSource.Width) \ 2, (imgShell.Height - imgSource.Height) \ 2, imgSource.Width, imgSource.Height), New Rectangle(0, 0, imgSource.Width, imgSource.Height), GraphicsUnit.Pixel)
            g.Dispose()
            imgShell = imgTemp

        End If

        Return imgShell
    End Function

    '截屏处理事件
    Private Sub GetScreenshotNormal()
        If Not Me.mActivatedDevice Then
            '您的设备还没有激活，请激活后再尝试使用此功能！
            MessageBox.Show(Me.Language.GetString("Welcome.Message.DeviceUnactivated"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        '用启动服务的方式判断是否挂载截屏插件
        If Me.StartScreenshotServer() Then
            Me.mfrmScreenshotPreview = New frmPreview(Me.mApplication)

            RemoveHandler Me.mfrmScreenshotPreview.ReGetScreenshot, AddressOf ReGetScreenshot_Click
            AddHandler Me.mfrmScreenshotPreview.ReGetScreenshot, AddressOf ReGetScreenshot_Click

            '线程去取截屏图片，先显示窗体。
            If Me.mThreadGetScreenshotForPreview IsNot Nothing AndAlso Me.mThreadGetScreenshotForPreview.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadGetScreenshotForPreview.Abort()
                Catch ex As Exception
                End Try
            End If

            Me.mThreadGetScreenshotForPreview = New Thread(New ParameterizedThreadStart(AddressOf GetScreenshotThread))
            With Me.mThreadGetScreenshotForPreview
                .IsBackground = True
                .Start(Me.mfrmScreenshotPreview)
            End With

            Me.mfrmScreenshotPreview.ShowDialog(Me.mApplication)
            Me.mfrmScreenshotPreview = Nothing

        Else
            Me.DownloadScreenshotPlugin()

        End If
    End Sub

    Private Sub GetScreenshotThread(ByVal frmObj As Object)
        Try
            If frmObj Is Nothing OrElse Not (TypeOf frmObj Is frmPreview) Then
                Return
            End If

            Dim frm As frmPreview = frmObj
            Dim img As Image = Nothing

            frm.SetLabelMessageStatus(False, False)
            frm.SetPanelLoading(True)
            img = Me.GetGetScreenshot()
            frm.SetPanelLoading(False)

            If img Is Nothing Then
                frm.SetPictureBoxImage(Nothing)
                Return
            End If

            Dim blnShowLabel As Boolean = Me.CheckPicBlack(img)

            '这个代码必须放在 “CheckPicBlack” 之后，因为判断是不是黑色的图片得在取得外壳之前判断。
            img = Me.GetScreenshotShell(img)
            frm.SetPictureBoxImage(img)

            If blnShowLabel Then
                Dim is_iPhone As Boolean = Not Me.mDevice.ProductType.ToLower().Contains("ipad")
                frm.SetLabelMessageStatus(True, is_iPhone)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub ReGetScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If sender IsNot Nothing AndAlso TypeOf sender Is frmPreview Then
            'Me.GetScreenshotThread(sender)
            '线程去取截屏图片，先显示窗体。
            If Me.mThreadGetScreenshotForPreview IsNot Nothing AndAlso Me.mThreadGetScreenshotForPreview.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadGetScreenshotForPreview.Abort()
                Catch ex As Exception
                End Try
            End If

            Me.mThreadGetScreenshotForPreview = New Thread(New ParameterizedThreadStart(AddressOf GetScreenshotThread))
            With Me.mThreadGetScreenshotForPreview
                .IsBackground = True
                .Start(sender)
            End With

            'ElseIf sender IsNot Nothing AndAlso TypeOf sender Is FrmTUpdate Then
            'Dim img As Image = Me.GetGetScreenshot()

            ''weiBo重新取得截屏图片的处理事件
            'Dim frmPreview As FrmTUpdate = sender
            'If Me.CheckPicBlack(img) = False Then
            '    frmPreview.SetMsgPnlStatus = False
            'Else
            '    frmPreview.SetMsgPnlStatus = True
            'End If
            'img = Me.GetScreenshotShell(img)
            'frmPreview.SetScreenshotContralStatus(True, Me.mDevice.ProductType)
            'Dim imgPath As String = Folder.TempFolder + Common.GenerateGuid + ".jpg"
            'img.Save(imgPath, Imaging.ImageFormat.Jpeg)
            'frmPreview.ReSetWeiBoImage(imgPath)

        End If

    End Sub

    Private Sub btnScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScreenshot.Click
        Me.mbtnLastClick = sender
        Me.SetScreenshotEnable(False)
        Me.GetScreenshotNormal()
        Me.SetScreenshotEnable(True)
    End Sub

    '取得截屏图片，方法有处理是否要加壳
    Private Function GetGetScreenshot() As Image
        Dim img As Image = Me.mDevice.GetScreenshotImage()
        '有的时候一次会取不到图片，就暂停0.3秒再取一次
        If img Is Nothing Then
            Utility.WaitSeconds(0.5)
            img = Me.mDevice.GetScreenshotImage()
        End If
        Dim intTryCount As Integer = 2
        While img Is Nothing AndAlso intTryCount > 0
            Utility.WaitSeconds(0.5)
            img = Me.mDevice.GetScreenshotImage()
            intTryCount -= 1
        End While

        If img Is Nothing Then
            '"很抱歉，截屏失败请稍候再重试！"
            MessageBox.Show(Me.Language.GetString("Welcome.Message.GetScreenshotFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If

        Return img
    End Function

    '判断图片是不是黑色的
    Private Function CheckPicBlack(ByVal img As Image) As Boolean
        Dim blnReturn As Boolean = True

        If img Is Nothing Then
            Return blnReturn
        End If

        Dim bmpobj As New Bitmap(img)
        Dim intCenterX As Integer = bmpobj.Width / 2
        Dim intCenterY As Integer = bmpobj.Height / 2
        For index As Integer = 0 To bmpobj.Height - 1
            Dim colorPoint As Color = bmpobj.GetPixel(intCenterX, index)
            If colorPoint.A <> Color.Black.A OrElse colorPoint.R <> Color.Black.R OrElse colorPoint.G <> Color.Black.G OrElse colorPoint.B <> Color.Black.B Then
                blnReturn = False
                Return blnReturn
            End If
        Next

        For index As Integer = 0 To bmpobj.Width - 1
            Dim colorPoint As Color = bmpobj.GetPixel(index, intCenterY)
            If colorPoint.A <> Color.Black.A OrElse colorPoint.R <> Color.Black.R OrElse colorPoint.G <> Color.Black.G OrElse colorPoint.B <> Color.Black.B Then
                blnReturn = False
                Return blnReturn
            End If
        Next
        Return blnReturn
    End Function

#End Region

#Region "--- 设置 ---"

    '设置处理事件
    Private Sub btnScreenshotSetUp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScreenshotSetUp.Click
        If IniSetting.GetScreenshotShell() Then
            Me.tsmiScreenshotShell.Image = My.Resources.tsmi_checked
            Me.tsmiScreenshot.Image = Nothing
        Else
            Me.tsmiScreenshotShell.Image = Nothing
            Me.tsmiScreenshot.Image = My.Resources.tsmi_checked
        End If
        Me.cmsSetting.Show(Me.btnScreenshotSetUp, New Point(0, Me.btnScreenshotSetUp.Bottom + 1))
    End Sub

    Private Sub tsmiScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiScreenshot.Click
        Me.tsmiScreenshotShell.Image = Nothing
        Me.tsmiScreenshot.Image = My.Resources.tsmi_checked
        IniSetting.SetScreenshotShell(False)
    End Sub

    Private Sub tsmiScreenshotShell_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiScreenshotShell.Click

        Me.tsmiScreenshotShell.Image = My.Resources.tsmi_checked
        Me.tsmiScreenshot.Image = Nothing
        IniSetting.SetScreenshotShell(True)
    End Sub

#End Region

    Private Delegate Sub SetImageForScreenshotHandler(ByVal img As Image)
    Private Sub SetImageForScreenshot(ByVal img As Image)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New SetImageForScreenshotHandler(AddressOf SetImageForScreenshot), img)
        Else
            frmImageView.SrcPicture = img

        End If

    End Sub

    '下载插件
    Private Sub DownloadScreenshotPlugin()
        If Me.mDevice Is Nothing OrElse Not Me.mDevice.IsConnected Then
            Return
        End If
        '从配制文件里查找当前版本的文件是否存在，如果不存在就下载。
        Dim strFileName As String = ScreenshotSetting.GetFileNameForVersion(Me.mDevice.ProductVersion)
        If strFileName.Trim.Length <= 0 Then
            '"很抱歉，没有找到与当前固件匹配的苹果开发者插件！"
            tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Welcome.Message.CanNotFindPluginForDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
            Return
        End If

        Dim strScreenshorPluginPath As String = Folder.Plugins & strFileName
        If System.IO.File.Exists(strScreenshorPluginPath) = False Then

            '判断插件下载逻辑
            If Me.CheckDownloading() Then
                '正在下载插件，请稍候！
                tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Welcome.Message.DownloadingPlugin"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
                Return

            Else
                '"使用此功能需要下载插件，点击确定进行下载！"
                If tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Welcome.Message.ShouldDownloadPlugin"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.None, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Cancel Then
                    Return
                End If

            End If

            'If Me.mDownManage Is Nothing Then
            '    ' Me.mDownManage = PluginDownloadManage
            'End If
            Me.mbtnLastClick = Nothing

            RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            '下载:添加插件下载任务
            Me.ScreenshotPluginDownload(Me.mDevice.ProductVersion)
        Else
            Me.InstallScreenshotPluginThread()
        End If
    End Sub

    Public Sub ScreenshotPluginDownload(ByVal strVersion As String)
        Dim lstItem As List(Of MultiThreadDownloadItem) = Me.mDownManage.GetItems(ResourceClass.Plugins, ResourceType.pScreenShot, TaskState.All)
        Dim strUrl As String = ScreenshotSetting.GetScreenshotPluginDownloadUrl(strVersion)

        For Each Item As MultiThreadDownloadItem In lstItem
            If Item.ItemInfo.Url = strUrl Then

                Me.mDownManage.Start(Item)
                Return
            End If
        Next

        Dim info As New MultiThreadDownloadItemInfo
        If String.IsNullOrEmpty(strUrl.Trim) = False Then
            info.Url = strUrl
            info.Type = ResourceType.pScreenShot
            info.Class = ResourceClass.Plugins
            info.Name = Path.GetFileName(strUrl)
            info.SaveFolder = Folder.Plugins
            Me.mDownManage.NewTask(info)
        End If

    End Sub

    Private Function CheckDownloading() As Boolean
        Dim result As Boolean = False

        If Me.mDownManage Is Nothing Then
            Me.mDownManage = MultiThreadDownload.Instance
        End If

        Dim strUrl As String = ScreenshotSetting.GetScreenshotPluginDownloadUrl(Me.mDevice.ProductVersion)

        For Each item As MultiThreadDownloadItem In Me.mDownManage.DownloadingList
            If item.ItemInfo.Url = strUrl Then
                result = True
                '如果发现正在下载重新绑定下载的事件，不然重新插入拔就看不到状态了。
                RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
                AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
                RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                Exit For
            End If
        Next

        Return result
    End Function

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New EventHandler(Of MultiThreadDownloadEventArgs)(AddressOf OnDownloading), sender, e)
        Else
            Dim item As MultiThreadDownloadItem = sender
            If item.ItemInfo.Class = ResourceClass.Plugins Then
                Dim strText As String = String.Empty
                If item.ItemInfo.Type = ResourceType.pScreenShot Then
                    strText = String.Format(Me.Language.GetString("Welcome.Message.DownlaodingPlugin"), e.Progress.ToString().PadLeft(2, " "c))          '"正在下载插件：{0}%"
                ElseIf item.ItemInfo.Type = ResourceType.pEXE Then
                    strText = String.Format("正在下载越狱工具：{0}%", e.Progress.ToString().PadLeft(2, " "c))
                End If
                If strText.Length > 0 Then
                    Me.lblDownloadProgress.Text = strText
                End If
                Me.lblDownloadProgress.Visible = True
            End If
        End If
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New EventHandler(Of MultiThreadDownloadCompletedEventArgs)(AddressOf OnDownloaded), sender, e)
        Else
            Dim item As MultiThreadDownloadItem = sender
            If item.ItemInfo.Class = ResourceClass.Plugins Then
                If File.Exists(item.ItemInfo.FilePath) Then
                    RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
                    RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded

                    If item.ItemInfo.Type = ResourceType.pScreenShot Then
                        Me.lblDownloadProgress.Text = Me.Language.GetString("Welcome.Message.DownloadScreenshotPluginSuccessed") '"截屏插件下载成功！"
                        Utility.WaitSeconds(0.5)

                        '安装插件
                        Me.InstallScreenshotPluginThread()
                    ElseIf item.ItemInfo.Type = ResourceType.pEXE Then
                        Me.btnJailbreak.Enabled = True
                        Me.lblDownloadProgress.Text = "越狱工具下载成功！"
                        Utility.WaitSeconds(0.5)

                        '启动越狱工具
                        Me.StartJailbreakTool()
                    End If

                End If

            End If
        End If
    End Sub

    Private Delegate Sub SetMainFormStatusMessageHandler(ByVal strMessage As String)
    Private Sub SetMainFormStatusMessage(ByVal strMessage As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetMainFormStatusMessageHandler(AddressOf SetMainFormStatusMessage), strMessage)
        Else
            If String.IsNullOrEmpty(strMessage.Length) = False Then
                Me.lblDownloadProgress.Visible = True
            Else
                Me.lblDownloadProgress.Visible = False
            End If
            Me.lblDownloadProgress.Text = strMessage
        End If
    End Sub

    '用线程去安装插件，就不会卡
    Private Sub InstallScreenshotPluginThread()
        If Me.mThreadInstallPlugin IsNot Nothing AndAlso Me.mThreadInstallPlugin.ThreadState <> ThreadState.Stopped Then
            Try
                Me.mThreadInstallPlugin.Abort()
            Catch ex As Exception
            End Try
        End If

        Me.mThreadInstallPlugin = New Thread(AddressOf InstallScreenshotPlugin)
        With Me.mThreadInstallPlugin
            .IsBackground = True
            .Start()
        End With
    End Sub

    '安装插件
    Private Sub InstallScreenshotPlugin()
        Try
            Me.SetScreenshotEnable(False)
            If Me.StartScreenshotServer() Then
                Me.SetScreenshotEnable(True)
                Return
            End If

            '下载后把文件解压出
            Dim strFileName As String = ScreenshotSetting.GetFileNameForVersion(Me.mDevice.ProductVersion)
            Dim strScreenshorPluginPath As String = Folder.Plugins & strFileName

            If Not File.Exists(strScreenshorPluginPath) Then
                Return
            End If

            Me.SetMainFormStatusMessage(Me.Language.GetString("Welcome.Message.MounterThePlugin")) ' '"正在挂载插件,请稍候..."

            Dim strPathOnPC As String = Path.Combine(Folder.TempFolder, Me.mDevice.Identifier.ToString()) & "\"

            '解压
            Dim lstFolders As New List(Of String)
            Dim listInfoFilters As New List(Of UnzipInfo)
            listInfoFilters.Add(New UnzipInfo("/DeveloperDiskImage.dmg.signature", False, False, False, True))
            listInfoFilters.Add(New UnzipInfo("/DeveloperDiskImage.dmg", False, False, False, True))

            If Utility.unzip(strScreenshorPluginPath, strPathOnPC, listInfoFilters, lstFolders, True, False, True, Nothing) <= 0 Then
                Common.LogException(String.Format("插件解压失败：{0}", strScreenshorPluginPath))
            End If

            Dim filePath As String = strPathOnPC & "DeveloperDiskImage.dmg"
            If Me.mDevice.VersionNumber >= 600 Then
                Try
                    Dim filePathTmp As String = strPathOnPC & "staging.dimage"
                    If File.Exists(filePathTmp) Then
                        File.Delete(filePathTmp)
                    End If
                    File.Move(filePath, filePathTmp)
                    filePath = filePathTmp
                Catch ex As Exception
                End Try
            End If

            Me.mDevice.SendMobileImageMounter(filePath, strPathOnPC & "DeveloperDiskImage.dmg.signature")

            Me.SetMainFormStatusMessage(Me.Language.GetString("Welcome.Message.MounterPluginSucceed"))      '"插件挂载成功！"

            Me.SetScreenshotEnable(True)
            Me.ReDoclick()

            Utility.WaitSeconds(3)
            Me.SetMainFormStatusMessage("")
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub ReDoclickHandler()
    Private Sub ReDoclick()
        If Me.InvokeRequired Then
            Me.Invoke(New ReDoclickHandler(AddressOf ReDoclick))
        Else
            If Me.mbtnLastClick IsNot Nothing Then
                Me.SetScreenshotEnable(False)
                If Me.mbtnLastClick.Name = Me.btnScreenshot.Name Then
                    Me.GetScreenshotNormal()

                ElseIf Me.mbtnLastClick.Name = Me.btnScreenshotWeiBo.Name Then
                    'Me.ShareScreenshotToWeiBo()

                ElseIf Me.mbtnLastClick.Name = Me.btnScreenshotPlay.Name Then
                    Me.GetScreenshotPlay()

                End If
                Me.SetScreenshotEnable(True)
                Me.mbtnLastClick = Nothing
            End If
        End If

    End Sub

    Private Delegate Sub SetScreenshotEnableHandler(ByVal blnEnable As Boolean)
    Private Sub SetScreenshotEnable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetScreenshotEnableHandler(AddressOf SetScreenshotEnable), blnEnable)
        Else
            Me.btnScreenshot.Enabled = blnEnable
            Me.btnScreenshotWeiBo.Enabled = blnEnable
            Me.btnScreenshotPlay.Enabled = blnEnable
            Me.btnScreenshotSetUp.Enabled = blnEnable
            Application.DoEvents()
        End If
    End Sub

    Private Function StartScreenshotServer()
        '尝试启动截屏服务，如果启动不了再等0.5秒再启动一次
        Dim blnScreenshorServerStart As Boolean = Me.mDevice.StartScreenshot()
        If Not blnScreenshorServerStart Then
            Utility.WaitSeconds(0.5)
            blnScreenshorServerStart = Me.mDevice.StartScreenshot()
        End If

        Return blnScreenshorServerStart
    End Function

#End Region

#Region "---  电量  ---"

    Private Sub tmrRefresh_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrRefresh.Tick
        Try
            Me.RefreshBattery()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "RefreshBattery")
        End Try
    End Sub

    Public Sub RefreshBattery()
        Dim count As Integer = 12
        Dim index As Integer = 0

        Dim batteryCapacity As Integer = Me.mDevice.BatteryCurrentCapacity

        If batteryCapacity = 100 Then
            index = count - 1    '电量100的时候取最后一张图片
        Else
            index = Math.Floor(batteryCapacity / 100 * (count - 1))
        End If

        If batteryCapacity < 0 Then
            Me.tmrRefresh.Interval = 30000
            Return
        Else
            If Me.tmrRefresh.Interval <> 90000 Then
                Me.tmrRefresh.Interval = 90000
            End If
        End If

        Dim strBattery As String = String.Format("{0}%", batteryCapacity.ToString().PadLeft(2, " "c))
        If Me.mDevice.BatteryIsCharging Then
            Me.picCharging.Image = GuiHelper.GetMouseStateImage(My.Resources.icon_battery, index, count)
            Me.lblCharging.Text = String.Format(Me.Language.GetString("Welcome.Label.Charging"), strBattery)   '"Charging {0}" 
        Else
            Me.picCharging.Image = GuiHelper.GetMouseStateImage(My.Resources.icon_battery, index, count)
            Me.lblCharging.Text = strBattery
        End If
    End Sub

#End Region

#Region "--- 越狱工具下载及启动 ---"

    Private mJailbreakPath As String = String.Empty

    Private Sub InitJailbreakTool()
        Try
            Me.mJailbreakPath = Path.Combine(Folder.DocumentFolder, "Jailbreak")

            Folder.CheckFolder(Me.mJailbreakPath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitJailbreakTool")
        End Try
    End Sub

    Private Enum OperateType
        None
        OpenCourse
        OpenExe
        Download
    End Enum

#Region "--- 开始越狱 ---"

    Private Function FormatProductForJailbreak(ByVal strProduct As String) As String
        Dim strResult As String = ""

        If String.IsNullOrEmpty(strProduct) Then
            Return strResult
        End If

        Select Case strProduct
            Case "iPad1,1"
                strResult = "iPad1"

            Case "iPad2,1"
                strResult = "iPad2"

            Case "iPad2,2"
                strResult = "iPad2"

            Case "iPad2,3"
                strResult = "iPad2"

            Case "iPad2,4"
                strResult = "iPad2"

            Case "iPad2,5"
                strResult = "iPadmini"

            Case "iPad2,6"
                strResult = "iPadmini"

            Case "iPad2,7"
                strResult = "iPadmini"

            Case "iPad3,1"
                strResult = "iPad3"

            Case "iPad3,2"
                strResult = "iPad3"

            Case "iPad3,3"
                strResult = "iPad3"

            Case "iPad3,4"
                strResult = "iPad4"

            Case "iPad3,5"
                strResult = "iPad4"

            Case "iPad3,6"
                strResult = "iPad4"

            Case "iPad4,1"
                strResult = "iPadAir"

            Case "iPad4,2"
                strResult = "iPadAir"

            Case "iPad4,4"
                strResult = "iPadmini2"

            Case "iPad4,5"
                strResult = "iPadmini2"

            Case "iPhone1,1"
                strResult = "iPhone2G"

            Case "iPhone1,2"
                strResult = "iPhone3G"

            Case "iPhone2,1"
                strResult = "iPhone3GS"

            Case "iPhone3,1"
                strResult = "iPhone4"

            Case "iPhone3,2"
                strResult = "iPhone4"

            Case "iPhone3,3"
                strResult = "iPhone4"

            Case "iPhone4,1"
                strResult = "iPhone4S"

            Case "iPhone5,1"
                strResult = "iPhone5"

            Case "iPhone5,2"
                strResult = "iPhone5"

            Case "iPhone5,3"
                strResult = "iPhone5C"

            Case "iPhone5,4"
                strResult = "iPhone5C"

            Case "iPhone6,1"
                strResult = "iPhone5S"

            Case "iPhone6,2"
                strResult = "iPhone5S"

            Case "iPod1,1"
                strResult = "iPodTouch1G"

            Case "iPod2,1"
                strResult = "iPodTouch2G"

            Case "iPod3,1"
                strResult = "iPodTouch3G"

            Case "iPod4,1"
                strResult = "iPodTouch4"

            Case "iPod5,1"
                strResult = "iPodTouch5"

            Case Else
                strResult = strProduct

        End Select

        Return strResult
    End Function

    '开始越狱
    Private Sub StartJailbreakTool()
        Try
            If Me.bgwStartJailbreak.IsBusy Then
                Return
            End If

            Me.bgwStartJailbreak.RunWorkerAsync()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "StartJailbreakTool")
        End Try
    End Sub

    Private Sub bgwStartJailbreak_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwStartJailbreak.DoWork
        Try
            Dim type As OperateType = OperateType.None
            Me.SetDownloadProgressText("启动越狱工具...")

            If Me.mDevice Is Nothing Then
                GoTo DO_EXIT
            End If

            '1、从运行目录拷贝越狱工具
            Me.CopyFileFromAppFolder()

            JBIniSetting.DownloadConfig()

            Dim strProductVersion As String = Me.mDevice.ProductVersion
            Dim strProductType As String = Me.FormatProductForJailbreak(Me.mDevice.ProductType)
            Dim strExeFileName As String = JBIniSetting.GetExeFileName(strProductVersion, strProductType)   'exe启动路径
            Dim strFileName As String = JBIniSetting.GetFileName(strProductVersion, strProductType)              '下载文件路径
            Dim strToolUrl As String = JBIniSetting.GetFileUrl(strProductVersion, strProductType)                      '下载地址

            If String.IsNullOrEmpty(strExeFileName) OrElse _
               String.IsNullOrEmpty(strFileName) OrElse _
               String.IsNullOrEmpty(strToolUrl) Then

                type = OperateType.OpenCourse
                GoTo DO_EXIT
            End If

            '2、检查越狱工具
            type = Me.CheckJailbreakTool(strExeFileName, strFileName)

            '3、下载越狱工具
            If type = OperateType.Download Then
                Me.DownloadJailbreakTool(strToolUrl, strFileName)
            End If

DO_EXIT:
            e.Result = type
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwStartJailbreak_DoWork")
        End Try
    End Sub

    Private Sub bgwStartJailbreak_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwStartJailbreak.RunWorkerCompleted
        Try
            Me.btnJailbreak.Enabled = True
            Me.SetDownloadProgressText("")

            If e.Result Is Nothing Then
                Return
            End If

            Dim type As OperateType = CType(e.Result, OperateType)
            If type = OperateType.OpenCourse Then
                Me.OpenCourse()
            ElseIf type = OperateType.Download Then
                Me.btnJailbreak.Enabled = False
                Me.SetDownloadProgressText("正在下载越狱工具...")
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwStartJailbreak_RunWorkerCompleted")
        End Try
    End Sub

    '检查越狱工具
    Private Function CheckJailbreakTool(ByVal strExeFileName As String, ByVal strFileName As String) As OperateType
        '参数一：exe启动路径；参数二：下载文件路径（如果下载文件的后缀为exe，则直接运行，否则需要解压）
        Dim type As OperateType = OperateType.None

        If String.IsNullOrEmpty(strExeFileName) OrElse String.IsNullOrEmpty(strFileName) Then
            type = OperateType.OpenCourse
            GoTo DO_EXIT
        End If

        Dim strExePath As String = String.Empty
        Dim strExtension As String = Path.GetExtension(strFileName).ToLower()

        '1、如果配置为exe文件
        If String.Compare(strExtension, ".exe") = 0 Then
            strExePath = Path.Combine(Me.mJailbreakPath, strFileName)
            If File.Exists(strExePath) Then
                Me.OpenJailbreakTool(strExePath)
                type = OperateType.OpenExe
            Else
                type = OperateType.Download
            End If

            GoTo DO_EXIT
        End If

        '2、如果配置为zip、rar文件
        Dim strExeFileFolder As String = Me.mJailbreakPath & "\" & Path.GetFileNameWithoutExtension(strFileName)
        strExePath = Me.GetExeFilePath(strExeFileFolder, strExeFileName)

        If Not String.IsNullOrEmpty(strExePath) AndAlso File.Exists(strExePath) Then
            Me.OpenJailbreakTool(strExePath)
            type = OperateType.OpenExe
        Else
            Dim strZipPath As String = Path.Combine(Me.mJailbreakPath, strFileName)
            If File.Exists(strZipPath) Then
                type = Me.UnzipJailbreakTool(strZipPath, strExeFileName)
            Else
                type = OperateType.Download
            End If
        End If

DO_EXIT:
        Return type
    End Function

    '解压zip文件
    Private Function UnzipJailbreakTool(ByVal strZipPath As String, ByVal strExeFileName As String) As OperateType
        Dim type As OperateType = OperateType.None

        Try
            If String.IsNullOrEmpty(strZipPath) OrElse String.IsNullOrEmpty(strExeFileName) Then
                type = OperateType.OpenCourse
                GoTo DO_EXIT
            End If

            Me.SetDownloadProgressText("解压越狱工具...")
            Me.SetButtonJailbreakEnabled(False)

            '解压目录
            Dim strExeFileFolder As String = Me.mJailbreakPath & "\" & Path.GetFileNameWithoutExtension(strZipPath)
            If Utility.unzip(strZipPath, strExeFileFolder) = 0 Then
                Common.LogException("文件路径：" & strZipPath, "解压失败")
                Try
                    File.Delete(strZipPath)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "删除无法解压的文件")
                End Try
            End If

            Me.SetDownloadProgressText("")
            Me.SetButtonJailbreakEnabled(True)

            Dim strExePath As String = Me.GetExeFilePath(strExeFileFolder, strExeFileName)
            If File.Exists(strExePath) Then
                Me.OpenJailbreakTool(strExePath)
                type = OperateType.OpenExe
            Else
                type = OperateType.Download
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "UnzipJailbreakTool")
        End Try

DO_EXIT:
        Return type
    End Function

#End Region

#Region "--- 结果处理 ---"

    '打开教程
    Private Sub OpenCourse()
        If Me.InvokeRequired Then
            Me.Invoke(New Threading.ThreadStart(AddressOf OpenCourse))
        Else
            Me.mApplication.GotoItem(ActionFuncType.Jailbreak)
        End If
    End Sub

    '打开越狱工具
    Private Sub OpenJailbreakTool(ByVal strToolPath As String)
        Common.LogException("打开工具：" & strToolPath)

        If String.IsNullOrEmpty(strToolPath) OrElse Not File.Exists(strToolPath) Then
            Return
        End If

        Me.OpenCourse()

        Dim strAppName As String = Path.GetFileNameWithoutExtension(strToolPath)
        Dim arrProcess() As Process = Process.GetProcessesByName(strAppName)

        '1、如果不存在，则启动
        If arrProcess.Length = 0 Then
            Try
                Dim pro As New System.Diagnostics.Process
                pro.StartInfo.FileName = strToolPath
                pro.Start()
            Catch
            End Try

            Return
        End If

        '2、如果存在，则激活
        Try
            Dim process As Process = arrProcess(0)

            ShowWindow(process.MainWindowHandle, 9) '9，代表恢复
            SetActiveWindow(process.MainWindowHandle)
            SetForegroundWindow(process.MainWindowHandle)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetActiveWindow")
        End Try

    End Sub

    '下载越狱工具
    Private Sub DownloadJailbreakTool(ByVal strUrl As String, ByVal strFileName As String)
        Try
            If Me.mDownManage Is Nothing Then
                Me.mDownManage = MultiThreadDownload.Instance
            End If

            RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded

            Dim lstItem As List(Of MultiThreadDownloadItem) = Me.mDownManage.GetItems(ResourceClass.Plugins, ResourceType.pEXE, TaskState.All)
            For Each Item As MultiThreadDownloadItem In lstItem
                If Item.ItemInfo.Url = strUrl Then
                    If Item.ItemInfo.Name = strFileName Then
                        Me.mDownManage.Start(Item)
                        Return
                    Else
                        Me.mDownManage.Delete(Item)
                        Exit For
                    End If
                End If
            Next

            Dim info As New MultiThreadDownloadItemInfo
            If String.IsNullOrEmpty(strUrl.Trim) = False Then

                '多线程下载
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))

                info.Class = ResourceClass.Plugins
                info.Type = ResourceType.pEXE
                info.Name = strFileName
                info.SaveFolder = Me.mJailbreakPath

                Me.mDownManage.NewTask(info)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadJailbreakTool")
        End Try
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Delegate Sub SetDownloadProgressTextHandler(ByVal strText As String)
    Private Sub SetDownloadProgressText(ByVal strText As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetDownloadProgressTextHandler(AddressOf SetDownloadProgressText), strText)
        Else
            Me.lblDownloadProgress.Visible = True
            Me.lblDownloadProgress.Text = strText
        End If
    End Sub

    Private Delegate Sub SetButtonJailbreakEnabledHandler(ByVal blnEnabled As Boolean)
    Private Sub SetButtonJailbreakEnabled(ByVal blnEnabled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetButtonJailbreakEnabledHandler(AddressOf SetButtonJailbreakEnabled), blnEnabled)
        Else
            Me.btnJailbreak.Enabled = blnEnabled
        End If
    End Sub

    Private Function GetExeFilePath(ByVal strExeFileFolder As String, ByVal strExeFileName As String) As String
        Dim strExeFilePath As String = String.Empty
        If String.IsNullOrEmpty(strExeFileFolder) OrElse String.IsNullOrEmpty(strExeFileName) Then
            Return strExeFilePath
        End If

        If Not Directory.Exists(strExeFileFolder) Then
            Return strExeFilePath
        End If

        Dim appList As New List(Of String)
        appList.AddRange(My.Computer.FileSystem.GetFiles(strExeFileFolder, FileIO.SearchOption.SearchAllSubDirectories, "*.exe"))

        For Each Item As String In appList
            Dim strTempName As String = Path.GetFileName(Item)

            If String.Compare(strTempName, strExeFileName, True) = 0 Then
                strExeFilePath = Item
                Exit For
            End If
        Next

        Return strExeFilePath
    End Function

    Private Sub CopyFileFromAppFolder()
        Try
            Dim strToolPath As String = Path.Combine(Folder.AppFolder, "Jailbreak")

            If Not Directory.Exists(strToolPath) Then
                Return
            End If

            My.Computer.FileSystem.CopyDirectory(strToolPath, Me.mJailbreakPath, True)

            Directory.Delete(strToolPath, True)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CopyFileFromAppFolder")
        End Try
    End Sub

#End Region

#Region "--- 激活教程 ---"

    <Runtime.InteropServices.DllImport("user32.dll", EntryPoint:="SendMessage")> _
    Public Shared Function SendMessage(ByVal hWnd As IntPtr, ByVal wMsg As Integer, ByVal wParam As Integer, ByVal lParam As Integer) As Integer
    End Function

    <Runtime.InteropServices.DllImport("user32.dll", CharSet:=CharSet.Auto, ExactSpelling:=True)> _
    Public Shared Function GetForegroundWindow() As IntPtr
    End Function

    <Runtime.InteropServices.DllImport("user32.dll")> _
    Public Shared Function SetActiveWindow(ByVal hWnd As IntPtr) As IntPtr
    End Function

    <Runtime.InteropServices.DllImport("user32.dll")> _
    Public Shared Function SetForegroundWindow(ByVal hWnd As IntPtr) As IntPtr
    End Function

    <Runtime.InteropServices.DllImport("user32.dll")> _
    Public Shared Function ShowWindow(ByVal hWnd As IntPtr, ByVal nCmdShow As Integer) As IntPtr
    End Function

#End Region

#End Region

#Region "--- 获取设备信息 ---"

    Private Sub SwithViewInLoadDeviceInfo(ByVal obj As Object)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ParameterizedThreadStart(AddressOf SwithViewInLoadDeviceInfo), obj)
            Else
                Dim isLoading As Boolean = CType(obj, Object)
                Me.btnRename.Enabled = Not isLoading
                Me.btnDetail.Enabled = Not isLoading
                Me.btnDisconnect.Enabled = Not isLoading
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SwithViewInLoadDeviceInfo")
        End Try
    End Sub

    Private Sub Start2LoadDeviceInfo(ByVal isOnlyLoadMainInfo As Boolean)
        Try
            Dim strProduct As String = String.Empty
            Dim iPhoneColor As PHBDeviceColorVariation = PHBDeviceColorVariation.Default

            If Me.mDevice IsNot Nothing Then
                Me.lblNameValue.Text = Me.mDevice.DeviceName
                strProduct = Me.mDevice.ProductType
                iPhoneColor = Me.mDevice.DeviceColor
            End If

            'Me.lblVersion.Text = Me.Language.GetString("Welcome.Label.Loading") '"正在获取 ..."
            Me.GetDeviceImageFromLocal(strProduct, iPhoneColor)
            Me.pbDevice.Image = Me._infoDevice.DeviceImage

            Me.SwithViewInLoadDeviceInfo(True)

            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf LoadDeviceInfo), isOnlyLoadMainInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Start2LoadDeviceInfo")
        End Try
    End Sub

    Private Sub LoadDeviceInfo(ByVal obj As Object)
        Dim isOnlyLoadMainInfo As Boolean = CType(obj, Boolean)

        Try
            If isOnlyLoadMainInfo Then
                Me.SetMainInfo()
                GoTo DOEXIT
            End If
            If Me.mDevice Is Nothing OrElse Me.mDevice.IsConnected = False Then
                GoTo DOEXIT
            End If

            Me.InitDataSource()
            Me.SetMainInfo()
            Me.LoadDetailInfo()
            Me.LoadPhoneSaleInfo()
            Me.WriteInfoToDevice()

            If Me.mDevice.VersionNumber >= 800 AndAlso Me.mDevice.CheckTuiAppExist() Then
                Dim strIDFA As String = Me.mDevice.IDFA
                Dim intCount As Integer = 0

                '设备刚接入的时候，可能无法取得数据
                While String.IsNullOrEmpty(strIDFA) AndAlso intCount < 3
                    intCount += 1
                    Threading.Thread.Sleep(1000)

                    If Me.mDevice Is Nothing Then
                        GoTo DO_NEXT
                    End If

                    strIDFA = Me.mDevice.IDFA
                End While

                Me._infoDevice.advertisingIdentifier = Me.mDevice.IDFA
                Me._infoDevice.identifierForVendor = Me.mDevice.IDFV(False)
            End If

DO_NEXT:
            '收集手机信息
            Me.CollectDevicData(Me._infoDevice)

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

DOEXIT:
        Me.SwithViewInLoadDeviceInfo(False)
    End Sub

    Private Function GetTuiIdentify(ByVal strSku As String)
        Dim strId As String = strSku
        Dim dict As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.Any)

        For Each Item As String In dict.Keys
            If Item.StartsWith(strId) Then
                strId = Item
                Exit For
            End If
        Next

        Return strId
    End Function

    Private Sub WriteInfoToDevice()

        'udid现在全部设备都写
        'If Me.mDevice.VersionNumber < 800 Then
        '    Return
        'End If

        '写入信息
        Dim strWritPathOnPhone As String = "/iTunes_Control/iTunes/Tongbu/d.plist"
        Me.mDevice.CheckFolderByAFC("/iTunes_Control/iTunes/Tongbu/")

        Dim dict As New Dictionary(Of Object, Object)
        dict.Add("uniqueIdentifier", Me.mDevice.UniqueDeviceID)
        dict.Add("serialnumber", Me.mDevice.SerialNumber)
        dict.Add("macaddress", Me.mDevice.WiFiAddress)

        Dim strWritPathOnPC As String = Folder.GetTempFilePath()
        If iTong.Device.CoreFoundation.CreatePlist(dict, strWritPathOnPC) AndAlso Common.EncryptDESFile(strWritPathOnPC, strWritPathOnPC) Then
            Me.mDevice.CopyToPhoneByAFC(strWritPathOnPC, strWritPathOnPhone)
        End If
    End Sub

    '首页展示设备信息
    Private Sub SetMainInfo()
        Try
            '首页展示设备信息
            If Me.mDevice IsNot Nothing Then
                With Me._infoDevice
                    .IsDeviceExist = True

                    '是否越狱
                    If Me.mDevice.Jailbreaked Then
                        .IsJailbreak = True
                        .IsAppSyncInstalled = TuiInstallHelper.CheckAppSyncInstall(Me.mDevice)
                    ElseIf Me.mDevice.InstallCydia Then
                        .IsCydiaInstalled = Me.mDevice.InstallCydia
                    Else
                        .IsJailbreak = False
                        .IsCydiaInstalled = False
                    End If

                    .DeviceName = Me.mDevice.DeviceName
                    .ProductVersion = Me.mDevice.ProductVersion
                    .ProductType = Me.mDevice.ProductType

                    'color ProductType 第一次取的时候可能会取不出来。取两次基本上都可以取成功的。
                    Dim iPhoneColor As PHBDeviceColorVariation = Me.mDevice.DeviceColor
                    If iPhoneColor = PHBDeviceColorVariation.Default Then
                        iPhoneColor = Me.mDevice.DeviceColor
                    End If
                    .DeviceColor = iPhoneColor
                End With
            Else
                Me._infoDevice.IsDeviceExist = False
            End If

            '获取设备图片
            Me.DeleteLocalErrorImage(Me._infoDevice.ProductType, Me._infoDevice.DeviceColor)
            Dim strFilePath As String = SummaryInfo.GetDeviceImage(Me._infoDevice.ProductType, Me._infoDevice.DeviceColor)
            Me.GetDeviceImageFromLocal(Me._infoDevice.ProductType, Me._infoDevice.DeviceColor)

            Me.SetMainInfoOnForm()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary.SetMainInfo")

        End Try
    End Sub

    Private Sub SetMainInfoOnForm()
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ThreadStart(AddressOf SetMainInfoOnForm))
        Else
            With Me._infoDevice
                Dim strJail As String = String.Empty
                If .IsDeviceExist Then
                    If .IsJailbreak Then
                        If .IsAppSyncInstalled Then
                            '"已越狱"
                            strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("Welcome.Label.InstalledAppSync"), "")  '"已安装AppSync"
                        Else
                            '"已越狱"
                            strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("Welcome.Label.UnInstallAppSync"), "")  '"未安装AppSync"
                            'Me.btnJump.Visible = IIf(Not Folder.LangType = LanguageType.en_US, True, False)
                        End If
                    ElseIf .IsCydiaInstalled Then
                        '"已越狱"
                        strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("File.Label.NoInstallAfc2"), "")           '"未安装afc2补丁"
                    Else
                        strJail = Me.Language.GetString("Welcome.Label.UnJailbreaked")  '"未越狱"
                    End If

                    Me.lblNameValue.Text = .DeviceName
                    'Me.lblVersion.Text = String.Format("{0}, {1}, {2}, {3}", .ProductVersion, SummaryInfo.FormatProduct(.ProductType), Me.FormatDeviceColor(.DeviceColor), strJail)
                Else
                    '概要信息
                    Me.lblNameValue.Text = _infoDevice.DeviceName
                    Me.txtNameValue.Text = Me.lblNameValue.Text
                    'Me.lblVersion.Text = String.Format("{0}, {1}", _infoDevice.ProductVersion, SummaryInfo.FormatProduct(_infoDevice.ProductType))
                End If

                If .DeviceImage IsNot Nothing Then
                    Me.pbDevice.Image = .DeviceImage
                End If
            End With

            Application.DoEvents()
        End If
    End Sub

    Private Sub GetDeviceImageFromLocal(ByVal productType As String, ByVal iPhoneColor As PHBDeviceColorVariation)
        Dim strFilePath As String = SummaryInfo.GetIconPathByDevice(productType, iPhoneColor)
        If File.Exists(strFilePath) Then
            Me._infoDevice.DeviceImage = Common.ImageFromFile(strFilePath)
        Else
            Me._infoDevice.DeviceImage = My.Resources.device_default
        End If
    End Sub

    '加载设备所有信息
    Public Sub LoadPhoneInfo()
        Try
            With Me._infoDevice
                .Key = Me.mDevice.SerialNumber
                .startTime = Now
                .mac = Common.GetMacAddress()

                Dim pluginLogin As IPluginLogin = Me.GetService(GetType(IPluginLogin))
                If pluginLogin IsNot Nothing AndAlso String.IsNullOrEmpty(pluginLogin.Uid) = False Then
                    .uid = pluginLogin.Uid
                Else
                    .uid = 0
                End If
            End With
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try

        Me.Start2LoadDeviceInfo(False)
    End Sub

    '收集设备信息
    Private Sub CollectDevicData(ByVal info As tbDeviceInfo)
        Try

            If Folder.LangType = LanguageType.en_US Then
                Return
            End If

            Dim tbDeviceCache As tbDeviceInfoCache = tbDeviceInfoCache.GetInstanse()
            tbDeviceCache.AddDeviceCacheData(info)
            tbDeviceCache.Save()

            info = Nothing

            '发送设备数据,以便服务器能及时收到wifimac和serial的对应关系
            ProcForm.Instance().Start2SendDeviceData()

        Catch ex As Exception
            Debug.Print(ex.ToString())
        End Try
    End Sub

#Region "--- 获取设备基本信息 ---"

    Private Sub LoadDetailInfo()
        Try
            If Me.mDevice Is Nothing Then
                Exit Sub
            End If

            With Me._infoDevice

                Me.mDevice.Connect()
                Me.mDevice.StartSession()

                '0 未知 1 越狱 2 未越狱
                If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
                    .JailbreakStatus = "1"
                    Me.mDictValue(DeviceInfoKey.InstallCydia) = Me.Language.GetString("File.Label.Jailbreaked")
                Else
                    .JailbreakStatus = "2"
                    Me.mDictValue(DeviceInfoKey.InstallCydia) = Me.Language.GetString("Welcome.Label.UnJailbreaked")    '"未越狱"
                End If

                Dim strValue As String = String.Empty

                strValue = Me.mDevice.DeviceName
                If Not String.IsNullOrEmpty(strValue) Then
                    .DeviceName = strValue
                End If

                strValue = Me.mDevice.ProductVersion
                If Not String.IsNullOrEmpty(strValue) Then
                    .ProductVersion = strValue
                End If
                Me.mDictValue(DeviceInfoKey.ProductVersion) = strValue


                strValue = Me.mDevice.ProductType
                If Not String.IsNullOrEmpty(strValue) Then
                    .ProductType = strValue
                End If
                Me.mDictValue(DeviceInfoKey.ProductType) = strValue & String.Format("({0})", SummaryInfo.FormatProduct(.ProductType))


                strValue = Me.mDevice.DeviceColor
                If Not String.IsNullOrEmpty(strValue) Then
                    .DeviceColor = strValue
                End If
                Me.mDictValue(DeviceInfoKey.DeviceColor) = frmSummary.FormatDeviceColor(Me.mDevice, strValue, Me.Language)


                Dim dicDeviceInfo As New Dictionary(Of Object, Object)
                Dim objDeviceInfo As Object = Me.mDevice.GetDeviceValue(CStr(Nothing), CStr(Nothing)) '获取设备的所有信息

                If objDeviceInfo Is Nothing Then
                    objDeviceInfo = Me.mDevice.GetDeviceValue(CStr(Nothing), CStr(Nothing))
                End If

                If objDeviceInfo IsNot Nothing AndAlso TypeOf (objDeviceInfo) Is Dictionary(Of Object, Object) Then
                    dicDeviceInfo = CType(objDeviceInfo, Dictionary(Of Object, Object))
                End If

                If objDeviceInfo IsNot Nothing Then
                    Debug.Print(iTong.Device.CoreFoundation.CreatePlistString(objDeviceInfo))
                End If

                Me.mDevice.StopSession()
                Me.mDevice.Disconnect()

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.PhoneNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .PhoneNumber = strValue
                End If
                Me.mDictValue(DeviceInfoKey.PhoneNumber) = strValue


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.SerialNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .SerialNumber = strValue
                End If
                Me.mDictValue(DeviceInfoKey.SerialNumber) = strValue


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.UniqueDeviceID)
                If Not String.IsNullOrEmpty(strValue) Then
                    .UniqueDeviceID = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.ModelNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .ModelNumber = strValue
                End If
                Me.mDictValue(DeviceInfoKey.ModelNumber) = strValue


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.RegionInfo)
                If Not String.IsNullOrEmpty(strValue) Then
                    .RegionInfo = strValue
                End If
                Me.mDictValue(DeviceInfoKey.RegionInfo) = strValue


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.CPUArchitecture)
                If Not String.IsNullOrEmpty(strValue) Then
                    .CPUArchitecture = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.TimeZoneOffsetFromUTC)
                If Not String.IsNullOrEmpty(strValue) Then
                    Dim intHour As Integer = Val(strValue) \ 3600
                    Dim strHour As String = String.Empty
                    If intHour > 0 Then
                        strHour = "+" & intHour.ToString()
                    Else
                        strHour = "-" & intHour.ToString()
                    End If
                    Dim strTempValue As String = String.Format("UTC/GMT {0} {1}", strHour, Me.Language.GetString("Music.Label.Hour"))
                    .TimeZoneOffsetFromUTC = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.Uses24HourClock)
                If Not String.IsNullOrEmpty(strValue) Then
                    .Uses24HourClock = strValue
                End If

                strValue = Me.mDevice.WiFiAddress
                If Not String.IsNullOrEmpty(strValue) Then
                    .WiFiAddress = strValue
                End If
                Me.mDictValue(DeviceInfoKey.WiFiAddress) = strValue


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BluetoothAddress)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BluetoothAddress = strValue
                End If
                Me.mDictValue(DeviceInfoKey.BluetoothAddress) = strValue


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.InternationalMobileEquipmentIdentity)
                If Not String.IsNullOrEmpty(strValue) Then
                    .InternationalMobileEquipmentIdentity = strValue
                End If
                Me.mDictValue(DeviceInfoKey.InternationalMobileEquipmentIdentity) = strValue


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.IntegratedCircuitCardIdentity)
                If Not String.IsNullOrEmpty(strValue) Then
                    .IntegratedCircuitCardIdentity = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.InternationalMobileSubscriberIdentity)
                If Not String.IsNullOrEmpty(strValue) Then
                    .InternationalMobileSubscriberIdentity = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.ActivationState)
                If Not String.IsNullOrEmpty(strValue) Then
                    .ActivationState = strValue
                End If
                Me.mDictValue(DeviceInfoKey.ActivationState) = IIf(strValue.ToLower().Contains("activated"), Me.Language.GetString("Welcome.Label.Activated"), Me.Language.GetString("Welcome.Label.Unactivated"))


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.PasswordProtected)
                If Not String.IsNullOrEmpty(strValue) Then
                    .PasswordProtected = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.MLBSerialNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .MLBSerialNumber = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandVersion)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BasebandVersion = strValue
                End If
                Me.mDictValue(DeviceInfoKey.BasebandVersion) = strValue


                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.FirmwareVersion)
                If Not String.IsNullOrEmpty(strValue) Then
                    .FirmwareVersion = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.HardwareModel)
                If Not String.IsNullOrEmpty(strValue) Then
                    .HardwareModel = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.UniqueChipID)
                If Not String.IsNullOrEmpty(strValue) Then
                    .UniqueChipID = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandGoldCertId)
                If String.IsNullOrEmpty(strValue) Then
                    strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandCertId)
                End If
                If Not String.IsNullOrEmpty(strValue) Then
                    .BasebandGoldCertId = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandSerialNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BasebandSerialNumber = strValue
                End If

                strValue = Me.mDevice.GetDeviceValue("com.apple.itunesstored", "AppleID")
                If Not String.IsNullOrEmpty(strValue) Then
                    .AppleID = strValue
                End If

                strValue = Me.mDevice.GetDeviceValue("com.apple.international", "Keyboard")
                If Not String.IsNullOrEmpty(strValue) Then
                    .Keyboard = strValue
                End If

                Me.mDictValue(DeviceInfoKey.UniqueDeviceID) = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.UniqueDeviceID)


            End With

            Me.LoadDetailInfoOnForm()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary.LoadDetailInfo")
        End Try
    End Sub

    Private Sub LoadDetailInfoOnForm()
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ThreadStart(AddressOf LoadDetailInfoOnForm))
        Else
            Try
                If Me._infoDevice Is Nothing Then
                    Return
                End If

                With Me._infoDevice
                    Me.lblJB.Text = Me.mDictValue(DeviceInfoKey.InstallCydia)
                    Me.lblActive.Text = Me.mDictValue(DeviceInfoKey.ActivationState)
                    Me.lblProduct.Text = Me.mDictValue(DeviceInfoKey.ProductType)
                    Me.lblFM.Text = Me.mDictValue(DeviceInfoKey.ProductVersion)
                    Me.lblColor.Text = Me.mDictValue(DeviceInfoKey.DeviceColor)
                    Me.lblSN.Text = Me.mDictValue(DeviceInfoKey.SerialNumber)
                    Me.lblIMEI.Text = Me.mDictValue(DeviceInfoKey.InternationalMobileEquipmentIdentity)
                    Me.lblUDID.Text = Me.mDictValue(DeviceInfoKey.UniqueDeviceID)
                    Me.lblPN.Text = Me.mDictValue(DeviceInfoKey.PhoneNumber)
                    Me.lblWifi.Text = Me.mDictValue(DeviceInfoKey.WiFiAddress)
                    Me.lblBlueAddress.Text = Me.mDictValue(DeviceInfoKey.BluetoothAddress)
                    Me.lblBaseband.Text = Me.mDictValue(DeviceInfoKey.BasebandVersion)
                    Me.lblMode.Text = Me.mDictValue(DeviceInfoKey.ModelNumber) & Me.mDictValue(DeviceInfoKey.RegionInfo)

                    Me.lblBuyDate.Text = "获取中..."
                    Me.lblExpireDate.Text = "获取中..."
                    Me.lblSaleArea.Text = Me.mDictValue(DeviceInfoKey.RegionInfo)

                    Me.lblSN.Top = Me.lblKeySN.Top - 1
                    Me.lblIMEI.Top = Me.lblKeyIMEI.Top - 1
                    Me.btnCopySN.Location = New Point(Me.lblSN.Right + 10, Me.btnCopySN.Top)
                    Me.btnCopySN.Visible = True
                    Me.btnCopyIMEI.Location = New Point(Me.lblIMEI.Right + 10, Me.btnCopyIMEI.Top)
                    Me.btnCopyIMEI.Visible = True

                    Me.btnFindMyPhone.Location = New Point(Me.lblFindMyPhone.Right + 10, Me.btnFindMyPhone.Top)
                    Me.btnFindMyPhone.Visible = True
                    'me.lblFindMyPhone.Text =""

                    If Not String.IsNullOrEmpty(.DeviceName) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.DeviceName") & Me.Language.GetString("Common.Symbol.Colon"), .DeviceName)                '"Device Name:"
                    End If

                    If Not String.IsNullOrEmpty(.ProductVersion) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.ProductVersion") & Me.Language.GetString("Common.Symbol.Colon"), .ProductVersion)            '"Product Version:"
                    End If

                    If Not String.IsNullOrEmpty(.ProductType) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.ProductType") & Me.Language.GetString("Common.Symbol.Colon"), String.Format("{0}({1})", SummaryInfo.FormatProduct(.ProductType), .ProductType))    '"Product Type:"
                    End If

                    If Not String.IsNullOrEmpty(.DeviceColor) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.DeviceColor") & Me.Language.GetString("Common.Symbol.Colon"), frmSummary.FormatDeviceColor(Me.mDevice, .DeviceColor, Me.Language))               '"Device Color:"
                    End If

                    If Not String.IsNullOrEmpty(.PhoneNumber) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.PhoneNumber") & Me.Language.GetString("Common.Symbol.Colon"), .PhoneNumber)               '"Phone Number:"
                    End If

                    If Not String.IsNullOrEmpty(.SerialNumber) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.SerialNumber") & Me.Language.GetString("Common.Symbol.Colon"), .SerialNumber)              '"Serial Number:"
                    End If

                    If Not String.IsNullOrEmpty(.UniqueDeviceID) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.UniqueDeviceId") & Me.Language.GetString("Common.Symbol.Colon"), .UniqueDeviceID)            '"Unique Device ID:"
                    End If

                    If Not String.IsNullOrEmpty(.ModelNumber) OrElse Not String.IsNullOrEmpty(.RegionInfo) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.ModelNumber") & Me.Language.GetString("Common.Symbol.Colon"), .ModelNumber & .RegionInfo & "")               '"Model Number:"
                    End If

                    If Not String.IsNullOrEmpty(.CPUArchitecture) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.CPUArchitecture") & Me.Language.GetString("Common.Symbol.Colon"), .CPUArchitecture)           '"CPU Architecture:"
                    End If

                    If Not String.IsNullOrEmpty(.TimeZoneOffsetFromUTC) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.TimeZone") & Me.Language.GetString("Common.Symbol.Colon"), .TimeZoneOffsetFromUTC)                  '"Time Zone:"
                    End If

                    If Not String.IsNullOrEmpty(.TimeZoneOffsetFromUTC) Then
                        Dim intHour As Integer = Val(.TimeZoneOffsetFromUTC) \ 3600
                        Dim strHour As String = String.Empty
                        If intHour > 0 Then
                            strHour = "+" & intHour.ToString()
                        Else
                            strHour = "-" & intHour.ToString()
                        End If
                        Dim strTempValue As String = String.Format("UTC/GMT {0} {1}", strHour, Me.Language.GetString("Music.Label.Hour"))
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.TimeOffset") & Me.Language.GetString("Common.Symbol.Colon"), strTempValue)                '"Time Offset:"
                    End If

                    If Not String.IsNullOrEmpty(.Uses24HourClock) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.TwentyFourHourTime") & Me.Language.GetString("Common.Symbol.Colon"), .Uses24HourClock)        '"24-Hour Time:"
                    End If

                    If Not String.IsNullOrEmpty(.WiFiAddress) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.WiFiAddress") & Me.Language.GetString("Common.Symbol.Colon"), .WiFiAddress)               '"Wi-Fi Address:"
                    End If

                    If Not String.IsNullOrEmpty(.BluetoothAddress) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.Bluetooth") & Me.Language.GetString("Common.Symbol.Colon"), .BluetoothAddress)                 '"Bluetooth:"
                    End If

                    If Not String.IsNullOrEmpty(.InternationalMobileEquipmentIdentity) Then
                        Me.mDataSource.Rows.Add(String.Format("IMEI{0}", Me.Language.GetString("Common.Symbol.Colon")), .InternationalMobileEquipmentIdentity)
                    End If

                    If Not String.IsNullOrEmpty(.IntegratedCircuitCardIdentity) Then
                        Me.mDataSource.Rows.Add(String.Format("ICCID{0}", Me.Language.GetString("Common.Symbol.Colon")), .IntegratedCircuitCardIdentity)
                    End If

                    If Not String.IsNullOrEmpty(.InternationalMobileSubscriberIdentity) Then
                        Me.mDataSource.Rows.Add(String.Format("IMSI{0}", Me.Language.GetString("Common.Symbol.Colon")), .InternationalMobileSubscriberIdentity)
                    End If

                    If Not String.IsNullOrEmpty(.ActivationState) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.ActivationState") & Me.Language.GetString("Common.Symbol.Colon"), .ActivationState)           '"Activation State:"
                    End If

                    If Not String.IsNullOrEmpty(.PasswordProtected) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.PasswordProtected") & Me.Language.GetString("Common.Symbol.Colon"), .PasswordProtected)         '"Password Protected:"
                    End If

                    If Not String.IsNullOrEmpty(.MLBSerialNumber) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.MLBSerialNumber") & Me.Language.GetString("Common.Symbol.Colon"), .MLBSerialNumber)           '"MLB Serial Number:"
                    End If

                    If Not String.IsNullOrEmpty(.BasebandVersion) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.BasebandVersion") & Me.Language.GetString("Common.Symbol.Colon"), .BasebandVersion)           '"Baseband Version:"
                    End If

                    If Not String.IsNullOrEmpty(.FirmwareVersion) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.FirmwareVersion") & Me.Language.GetString("Common.Symbol.Colon"), .FirmwareVersion)           '"Firmware Version:"
                    End If

                    If Not String.IsNullOrEmpty(.HardwareModel) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.HardwareModel") & Me.Language.GetString("Common.Symbol.Colon"), .HardwareModel)             '"Hardware Model:"
                    End If

                    If Not String.IsNullOrEmpty(.UniqueChipID) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.UniqueChipId") & Me.Language.GetString("Common.Symbol.Colon"), .UniqueChipID)              '"Unique Chip ID:"
                    End If

                    If Not String.IsNullOrEmpty(.BasebandGoldCertId) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.BasebandGoldCertId") & Me.Language.GetString("Common.Symbol.Colon"), .BasebandGoldCertId)        '"Baseband Gold Cert Id:"
                    End If

                    If Not String.IsNullOrEmpty(.BasebandSerialNumber) Then
                        Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.BasebandSerialNumber") & Me.Language.GetString("Common.Symbol.Colon"), .BasebandSerialNumber)      '"Baseband Serial Number:"
                    End If

                    If Not String.IsNullOrEmpty(.AppleID) Then
                        Dim strPhoneNumber As String = Me.Language.GetString("Welcome.Label.PhoneNumber") & Me.Language.GetString("Common.Symbol.Colon")
                        Dim startIndex As Integer = Me.GetIndexByNameFromDataTable(strPhoneNumber)

                        Dim row As DataRow = Me.CreateNewDataRow(Me.Language.GetString("Welcome.Label.AppleId") & Me.Language.GetString("Common.Symbol.Colon"), .AppleID) '"Apple ID:"
                        startIndex += 1
                        Me.mDataSource.Rows.InsertAt(row, startIndex)
                    End If

                    If Not String.IsNullOrEmpty(.Keyboard) Then
                        Dim strTwentyFourHourTime As String = Me.Language.GetString("Welcome.Label.TwentyFourHourTime") & Me.Language.GetString("Common.Symbol.Colon")
                        Dim startIndex As Integer = Me.GetIndexByNameFromDataTable(strTwentyFourHourTime)

                        Dim row As DataRow = Me.CreateNewDataRow(Me.Language.GetString("Welcome.Label.Keyboard") & Me.Language.GetString("Common.Symbol.Colon"), .Keyboard)   '"Keyboard:"
                        startIndex += 1
                        Me.mDataSource.Rows.InsertAt(row, startIndex)
                    End If
                End With

                Application.DoEvents()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "LoadDetailInfoOnForm")
            End Try

        End If
    End Sub

    Private Function GetDeviceValue(ByVal dicDeviceInfo As Dictionary(Of Object, Object), ByVal key As DeviceInfoKey) As String
        Return Me.GetDeviceValue(dicDeviceInfo, key.ToString())
    End Function

    Private Function GetDeviceValue(ByVal dicDeviceInfo As Dictionary(Of Object, Object), ByVal key As String) As String
        Dim strValue As String = String.Empty

        If dicDeviceInfo Is Nothing Then
            Return strValue
        End If

        If dicDeviceInfo.ContainsKey(key) Then
            Dim objValue As Object = dicDeviceInfo(key)

            If objValue IsNot Nothing Then
                If TypeOf objValue Is Byte() Then
                    strValue = Convert.ToBase64String(objValue)

                Else
                    strValue = objValue.ToString()
                End If
            End If
            'Else
            '    Debug.Print("设备信息不包括：" & key)
        End If

        If String.IsNullOrEmpty(strValue) Then
            Dim objValue As Object = Me.mDevice.GetDeviceValue(key)
            If objValue IsNot Nothing Then
                If TypeOf objValue Is Byte() Then
                    strValue = Convert.ToBase64String(objValue)
                Else
                    strValue = objValue.ToString()
                End If
            End If
        End If

        If String.IsNullOrEmpty(strValue) Then
            Debug.Print("设备信息不包括：" & key)
        End If

        Return strValue
    End Function

#End Region

#Region "--- 获取销售信息 ---"

    '请求获取销售信息
    Private Sub LoadPhoneSaleInfo()
        Try
            If Me.mDevice Is Nothing OrElse Me.mDevice.IsConnected = False Then
                Return
            End If

            If Me._infoDevice.SalesInfo IsNot Nothing AndAlso mSaleInfoHelper.IsReady Then
                Me.LoadPhoneSaleInfoOnForm()

                If Not mSaleInfoHelper.IsWebbrowser AndAlso _
                   mSaleInfoHelper.IsFromAppleWeb Then
                    Me.GetPurchase2WebBrowser()
                End If
            Else
                mSaleInfoHelper.Start2LoadPhoneSaleInfo(_infoDevice.RegionInfo)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadPhoneSaleInfo")
        End Try
    End Sub

    Private Sub OnLoadPhoneSaleInfo(ByVal sender As Object, ByVal args As SaleInfoEventArgs)
        Common.Log("测试日志*-*-*-*回调方法" & mSaleInfoHelper.IsReady & "mStrSN" & args.mStrSN & "SerialNumber" & Me.mDevice.SerialNumber)
        If args.mStrSN <> Me.mDevice.SerialNumber OrElse Not mSaleInfoHelper.IsReady Then
            Return
        End If

        If Me.mDevice Is Nothing OrElse Me.mDevice.IsConnected = False Then
            Return
        End If

        '更新到界面
        LoadPhoneSaleInfoOnForm()

        ''通过webbrower获取数据
        'If Not mSaleInfoHelper.IsWebbrowser AndAlso _
        '   mSaleInfoHelper.IsFromAppleWeb Then

        '    Me.GetPurchase2WebBrowser()
        'End If
    End Sub

    '请求获取销售信息
    Private Sub LoadPhoneSaleInfoOnForm()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf LoadPhoneSaleInfoOnForm))
        Else
            Try

                _infoDevice.SalesInfo = mSaleInfoHelper.SalesInfo
                _infoDevice.IsFromAppleWeb = mSaleInfoHelper.IsFromAppleWeb
                _infoDevice.SalesAreaValue = mSaleInfoHelper.SalesAreaValue

                With Me._infoDevice
                    '添加销售地
                    Dim strDeviceColorName As String = Me.Language.GetString("Welcome.Label.DeviceColor") & Me.Language.GetString("Common.Symbol.Colon")
                    Dim startIndex As Integer = Me.GetIndexByNameFromDataTable(strDeviceColorName)

                    Dim row As DataRow = Me.CreateNewDataRow(Me.Language.GetString("Welcome.Label.SalesArea") & Me.Language.GetString("Common.Symbol.Colon"), .SalesAreaValue)
                    startIndex += 1
                    Me.mDataSource.Rows.InsertAt(row, startIndex)

                    '展示到界面
                    If .SalesInfo IsNot Nothing Then
                        Me.lblSaleArea.Text = .SalesAreaValue

                        Dim strPurchDateName As String = Me.Language.GetString("Welcome.Label.PurchaseDate") & Me.Language.GetString("Common.Symbol.Colon")
                        Dim strCovEndDateName As String = Me.Language.GetString("Welcome.Label.WarrantyDate") & Me.Language.GetString("Common.Symbol.Colon")

                        '购买日期，没有值显示未知。
                        Dim strPurchaseDateValue As String = .SalesInfo.PurchaseDate
                        If String.IsNullOrEmpty(strPurchaseDateValue) Then
                            strPurchaseDateValue = Me.Language.GetString("Common.Label.Unknow")          '"未知"
                        End If
                        Me.lblBuyDate.Text = strPurchaseDateValue

                        Dim rowPurchaseDate As DataRow = Me.CreateNewDataRow(strPurchDateName, strPurchaseDateValue)
                        startIndex += 1
                        Me.mDataSource.Rows.InsertAt(rowPurchaseDate, startIndex)

                        '过保日期
                        Dim strCovEndDateValue As String = .SalesInfo.CovEndDate
                        If strCovEndDateValue = "2000-01-01" Then
                            strCovEndDateValue = Me.Language.GetString("Welcome.Label.OutWarranty")
                        End If

                        If String.IsNullOrEmpty(strCovEndDateValue) Then
                            strCovEndDateValue = Me.Language.GetString("Common.Label.Unknow")          '"未知"
                        End If
                        Me.lblExpireDate.Text = strCovEndDateValue

                        Dim rowCovEndDate As DataRow = Me.CreateNewDataRow(strCovEndDateName, strCovEndDateValue)
                        startIndex += 1
                        Me.mDataSource.Rows.InsertAt(rowCovEndDate, startIndex)

                        '写到本地配置
                        IniSetting.SetSalesInfo(.Identifier, .SalesInfo)
                    End If
                End With

                '如果是通过 post webbrowser 获取购买时间都获取不到 就显示重试按钮
                If _infoDevice.SalesInfo Is Nothing Then
                    'If Not mSaleInfoHelper.IsWebbrowser AndAlso mSaleInfoHelper.IsFromAppleWeb Then
                    '    '如果是通过 post 获取时间  webbrowser还没有执行 则 不显示重试按钮
                    '    Me.btnAgain.Enabled = False
                    'Else
                    '    Me.btnAgain.Enabled = True
                    'End If
                    Me.btnAgain.Enabled = True

                Else
                    Me.btnAgain.Enabled = False

                End If


                Application.DoEvents()

            Catch ex As Exception
                Debug.Print("GetSalesInfoError:" & ex.ToString)
                Common.LogException(ex.ToString(), "LoadPhoneSaleInfoOnForm")
            End Try
        End If
    End Sub

    Private Sub GetPurchase2WebBrowser()
        If mTdGetPurchase IsNot Nothing AndAlso mTdGetPurchase.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTdGetPurchase = New Thread(AddressOf DoGetPurchase2WebBrowser)
        Me.mTdGetPurchase.SetApartmentState(ApartmentState.STA)
        Me.mTdGetPurchase.IsBackground = True
        Me.mTdGetPurchase.Start()
    End Sub

    Private Sub DoGetPurchase2WebBrowser()
        Try
            '如果是从苹果服务器获取的数据 则使用  webbrowser 方式 也获取一次
            If Not mSaleInfoHelper.IsWebbrowser Then

                DevicePurchaseToWebBrowser()
                mSaleInfoHelper.IsWebbrowser = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoGetPurchase2WebBrowser")
        End Try
    End Sub

    Private Sub DevicePurchaseToWebBrowser()
        Try
            '获取设备购买时间用 webbrowser 方式
            'Dim strUrl As String = String.Format("https://selfsolve.apple.com/wcResults.do?sn={0}&cn=&locale=&caller=&num=26686", Me.mDevice.SerialNumber)
            Dim sbHead As StringBuilder = New StringBuilder()
            sbHead.AppendLine("User-Agent: Mozilla/5.0 (Windows NT 6.1) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/29.0.1547.57 Safari/537.36 ")
            sbHead.AppendLine("Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
            sbHead.AppendLine("Origin: https://selfsolve.apple.com")
            sbHead.AppendLine("Content-Type: application/x-www-form-urlencoded")
            sbHead.AppendLine("Referer: https://selfsolve.apple.com/agreementWarrantyDynamic.do")

            Dim strUrl As String = String.Format("https://selfsolve.apple.com/wcResults.do?sn={0}&cn=&locale=&caller=&num=0", Me.mDevice.SerialNumber)
            tbWBPurchaseTime.Navigate(strUrl, String.Empty, Nothing, sbHead.ToString())

            'Dim strUrl As String = "https://selfsolve.apple.com/wcResults.do"
            'tbWBPurchaseTime.Navigate(strUrl, String.Empty, System.Text.Encoding.UTF8.GetBytes(String.Format("sn={0}&cn=&locale=&caller=&num=26686", Me.mDevice.SerialNumber)), sbHead.ToString())

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DevicePurchaseToWebBrowser")
        End Try
    End Sub

    Private Sub OnDocumentCompleted(ByVal sender As Object, ByVal e As WebBrowserDocumentCompletedEventArgs)
        Try
            Dim salesInfo2wb As SalesInfoPara = Nothing
            Dim strPurchaseDateByWeb As String = String.Empty

            Dim strContent As String = String.Empty
            Dim status As FunctionSucceed = FunctionSucceed.None

            Dim strPurchaseDateByPost As String = String.Empty
            Dim salesInfo2Postdata As SalesInfoPara = mSaleInfoHelper.SalesInfo
            If salesInfo2Postdata IsNot Nothing AndAlso salesInfo2Postdata.PurchaseDate.Length > 0 Then
                strPurchaseDateByPost = salesInfo2Postdata.PurchaseDate
            End If

            '解析web返回的数据’
            If tbWBPurchaseTime.Document IsNot Nothing AndAlso tbWBPurchaseTime.Document.Body IsNot Nothing AndAlso tbWBPurchaseTime.Document.Body.InnerText IsNot Nothing Then
                strContent = tbWBPurchaseTime.Document.Body.InnerHtml

                '判断内容是不是已经包含了 displayHWSupportInfo 如果没有每隔一秒在赋值一次 一共执行3次
                Dim strTryCount As Integer = 0
                While Not strContent.Contains("displayHWSupportInfo") AndAlso strTryCount <= 3
                    Utility.WaitSeconds(1)

                    strContent = tbWBPurchaseTime.Document.Body.InnerHtml
                    strTryCount = strTryCount + 1
                End While

                Dim dateExpire As KeyValuePair(Of WarrantyType, Date) = SaleInfoHelper.GetExpireDateByRegularExpressions(strContent)
                salesInfo2wb = mSaleInfoHelper.CreateSalesInfoPara(dateExpire)

                '收集 webbrowser 获取的数据信息
                If salesInfo2wb IsNot Nothing AndAlso salesInfo2wb.PurchaseDate.Length > 0 Then
                    strPurchaseDateByWeb = salesInfo2wb.PurchaseDate
                End If

                Dim strPost As String = SaleInfoHelper.FormatContent(strContent)

                Dim statusP As FunctionSucceed = FunctionSucceed.Succeed
                If dateExpire.Key = WarrantyType.Unknow Then
                    statusP = FunctionSucceed.Failure
                ElseIf dateExpire.Key = WarrantyType.Expire Then
                    statusP = FunctionSucceed.Overdue
                End If

                ActionCollectHelper.ZJHelperGetInformation(Me.mDevice, ModelKey.ZJHelperPTimeByWebBrowser, ActionDataType.Search, statusP, String.Format("SN:{0} UID:{2} {1}", Me.mDevice.SerialNumber, strPost, PluginLogin.Instance().Uid)) 'IIf(strPurchaseDateByWeb.Length > 0, FunctionSucceed.Succeed, FunctionSucceed.Failure)
            End If

            If strPurchaseDateByWeb.Length = 0 Then
                If strPurchaseDateByPost.Length = 0 Then
                    '两种方式都为空’
                    status = FunctionSucceed.Empty2
                Else
                    'post的方式有数据， webbrowser的方式没数据’
                    status = FunctionSucceed.OnlyP
                End If
                GoTo Do_Exit
            End If

            'web的方式有数据，post的方式没数据
            If strPurchaseDateByPost.Length = 0 Then
                status = FunctionSucceed.OnlyW
                '保存web的数据
                Me.SaveSaleWithWebBrower(salesInfo2wb)
                GoTo Do_Exit
            End If

            '如果 p 获取的时间值 较早 结果为正数；反之 w 获取的时间值 较早 结果为负数；相等结果为 0
            Dim longDiffDay As Long = DateDiff(DateInterval.Day, Convert.ToDateTime(salesInfo2Postdata.PurchaseDate), Convert.ToDateTime(salesInfo2wb.PurchaseDate))
            If longDiffDay = 0 Then
                '如果时间相等 直接用p 
                status = FunctionSucceed.Equally2

            ElseIf longDiffDay < 0 Then
                status = FunctionSucceed.PEarlyW

            ElseIf DateDiff(DateInterval.Day, Date.Now, Convert.ToDateTime(salesInfo2Postdata.PurchaseDate)) < 0 Then
                '如果 p 获取的时间值 较晚 且比当前时间早  就直接用 p
                status = FunctionSucceed.WEarlyP
                '保存web的数据
                Me.SaveSaleWithWebBrower(salesInfo2wb)
            End If

Do_Exit:
            '收集两种获取方式  时间的值
            ActionCollectHelper.ZJHelperGetInformation(Me.mDevice, _
                                                      ModelKey.ZJHelperPTimeWebBrowserVsPostData, _
                                                      ActionDataType.Search, _
                                                      status, _
                                                      String.Format("SN:{2} UID:{3}  PostData:{0};Webbrowser:{1}", strPurchaseDateByPost, strPurchaseDateByWeb, Me.mDevice.SerialNumber, PluginLogin.Instance().Uid))

        Catch ex As Exception
            Common.LogException(ex.ToString, "OnDocumentCompleted")
        End Try
    End Sub

    Private Sub SaveSaleWithWebBrower(ByVal salesInfo2wb As SalesInfoPara)
        '保存数据库 
        mSaleInfoHelper.SaveSalesInfo(salesInfo2wb, Me.mDevice, Me._infoDevice.RegionInfo, True)
        '判断是否新机
        'mSaleInfoHelper.JudgeIsNewDevice(salesInfo2wb)
        mSaleInfoHelper.JudgeIsNewDeviceEx()
    End Sub

    Private Function GetSaleInfoFromAppleWeb() As SalesInfoPara
        Dim dicSalesInfo As New SalesInfoPara()
        Dim result As KeyValuePair(Of WarrantyType, Date) = SaleInfoHelper.GetExpireDate(Me.mDevice)
        Try
            Select Case result.Key
                Case WarrantyType.Active     '未过保
                    dicSalesInfo.CovEndDate = result.Value.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDay))                                          '过保日期
                    dicSalesInfo.PurchaseDate = result.Value.AddYears(-1).AddDays(1).ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDay))  '购买日期

                Case WarrantyType.Expire     '已过保
                    dicSalesInfo.CovEndDate = "2000-01-01"       '代表已过期
                    dicSalesInfo.PurchaseDate = ""                       '购买日期

                Case WarrantyType.Unknow  '未知
                    dicSalesInfo = Nothing

            End Select
        Catch ex As Exception
        End Try
        Return dicSalesInfo
    End Function

    Private Function CheckSalesInfo(ByVal info As SalesInfoPara) As Boolean
        Dim blnReturn As Boolean = True
        If info Is Nothing Then
            blnReturn = False
        Else
            If info.CovEndDate.Length = 0 Then
                blnReturn = False
            End If
        End If
        Return blnReturn
    End Function

    Private Function GetSaleInfoFromApple() As SalesInfoPara
        Dim salesInfo As SalesInfoPara = Nothing
        Try
            Dim strValue As String = Me.mDevice.SerialNumber
            Dim strUrl As String = "https://selfsolve.apple.com/warrantyChecker.do?sn={0}&cb=crossDomainAjax.successResponse"

            '请求苹果接口
            Dim strResult As String = Utility.GetContentStringFromUrl(String.Format(strUrl, strValue), System.Text.Encoding.UTF8, 40000)
            If strResult Is Nothing OrElse strResult.Length = 0 Then
                Return salesInfo
            End If

            '处理返回结果
            Dim strCrossDomain As String = "crossDomainAjax.successResponse("

            If strResult.StartsWith(strCrossDomain) Then
                strResult = strResult.Substring(strCrossDomain.Length, strResult.Length - strCrossDomain.Length)
            End If
            If strResult.EndsWith(")") Then
                strResult = strResult.TrimEnd(")")
            End If

            'json转换返回结果
            Dim objJson As JsonObject = JsonParser.ParseString(strResult)
            If objJson Is Nothing OrElse objJson.Count = 0 Then
                '"苹果服务器返回值为空。"
                Return salesInfo
            End If

            salesInfo = New SalesInfoPara()
            If objJson.ContainsKey("PURCHASE_DATE") Then
                salesInfo.PurchaseDate = CType(objJson.Item("PURCHASE_DATE"), JsonString).Value
            Else
                '取不到数据
                salesInfo.PurchaseDate = ""
            End If

            If objJson.ContainsKey("LAST_UNBRICK_DT") Then
                salesInfo.LastUnbrickDate = CType(objJson.Item("LAST_UNBRICK_DT"), JsonString).Value
            Else
                '取不到数据
                salesInfo.LastUnbrickDate = ""
            End If

            If objJson.ContainsKey("COV_END_DATE") Then
                salesInfo.CovEndDate = CType(objJson.Item("COV_END_DATE"), JsonString).Value
            Else
                '取不到数据
                salesInfo.CovEndDate = ""
            End If

            If objJson.ContainsKey("PURCH_COUNTRY") Then
                salesInfo.PurchCountry = CType(objJson.Item("PURCH_COUNTRY"), JsonString).Value
            Else
                '取不到数据
                salesInfo.PurchCountry = ""
            End If
        Catch ex As Exception
            Debug.Print(String.Format("从Apple官网取得销售地信息失败：{0}", ex.ToString()))
            salesInfo = Nothing
        End Try

        Return salesInfo
    End Function

    Private Sub UploadSalesInfoToServer(ByVal dicSalesInfo As SalesInfoPara, ByVal strSalesArea As String)
        Try
            Dim jsonData As New JsonObject()
            jsonData.Add("SN", Me.mDevice.SerialNumber)
            jsonData.Add("UDID", Me.mDevice.Identifier)
            jsonData.Add("PURCH_COUNTRY", strSalesArea)
            jsonData.Add("PURCHASE_DATE", dicSalesInfo.PurchaseDate)
            jsonData.Add("COV_END_DATE", dicSalesInfo.CovEndDate)

            Dim strData As String = Common.EncryptDES(JsonParser.SaveString(jsonData), Common.RgbKeyString, Common.RgbKeyString)
            strData = String.Format("data={0}", System.Web.HttpUtility.UrlEncode(strData))

            '"http://192.168.40.251/tbzs/api.aspx?type=1"
            Dim strUrl As String = String.Format(WebUrl.PostSalesInfo, "1")
            Dim strWebResult As String = Utility.PostData(strUrl, strData)

            If String.IsNullOrEmpty(strWebResult) Then
                Return
            End If

            'If strWebResult.Length > 0 Then
            '    Debug.Print("提交设备销售信息失败，返回信息为空")
            '    Return
            'End If
            Dim blnResult As Boolean = False
            Dim objJson As JsonObject = JsonParser.ParseString(strWebResult)

            If objJson.ContainsKey("state") Then
                If CType(objJson("state"), JsonString).Value = "1" Then
                    blnResult = True
                End If
            End If

            If Not blnResult Then
                Debug.Print("提交设备销售信息失败：" & strWebResult)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UploadSalesInfoToServer")
        End Try
    End Sub

    Private Function GetSalesInfoFromServer() As SalesInfoPara
        Dim dicSalesInfo As SalesInfoPara = Nothing
        Try
            '1、post数据
            Dim jsonData As New JsonObject()
            jsonData.Add("SN", Me.mDevice.SerialNumber)
            jsonData.Add("UDID", Me.mDevice.Identifier)

            Dim strData As String = Common.EncryptDES(JsonParser.SaveString(jsonData), Common.RgbKeyString, Common.RgbKeyString)
            strData = String.Format("data={0}", System.Web.HttpUtility.UrlEncode(strData))

            '"http://192.168.40.251/tbzs/api.aspx?type=2"
            Dim strUrl As String = String.Format(WebUrl.PostSalesInfo, "2")
            Dim strWebResult As String = Utility.PostData(strUrl, strData)

            If String.IsNullOrEmpty(strWebResult) Then
                GoTo DO_EXIT
            End If

            '2、分析服务器返回值
            Dim blnResult As Boolean = False
            Dim objJson As JsonObject = JsonParser.ParseString(strWebResult)

            If objJson.ContainsKey("state") Then
                If CType(objJson("state"), JsonString).Value = "1" Then
                    blnResult = True
                End If
            End If

            If Not blnResult Then
                Debug.Print("获取设备销售信息失败：" & strWebResult)
                GoTo DO_EXIT
            End If

            If objJson.ContainsKey("data") Then
                Dim dataJson As JsonObject = objJson("data")
                dicSalesInfo = New SalesInfoPara()

                If dataJson.ContainsKey("PURCH_COUNTRY") Then
                    dicSalesInfo.PurchCountry = CType(dataJson("PURCH_COUNTRY"), JsonString)
                End If

                If dataJson.ContainsKey("PURCHASE_DATE") Then
                    dicSalesInfo.PurchaseDate = CType(dataJson("PURCHASE_DATE"), JsonString)
                End If

                If dataJson.ContainsKey("COV_END_DATE") Then
                    dicSalesInfo.CovEndDate = CType(dataJson("COV_END_DATE"), JsonString)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSalesInfoFromServer")
        End Try

DO_EXIT:
        Return dicSalesInfo
    End Function

#End Region

#Region "--- 获取设备容量 ---"

    Private Sub OnCapacityCallback(ByVal sender As Object, ByVal e As DeviceCapacityArgs)
        If e Is Nothing Then
            Return
        End If

        ''偶尔在拔插设备的时候会出现崩溃， 目前还未发现原因，暂时捕获异常处理
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of DeviceCapacityArgs)(AddressOf OnCapacityCallback), sender, e)
            Else
                Try
                    Dim list As New List(Of KeyValuePair(Of DeviceCapacityType, Long))
                    For Each pair As KeyValuePair(Of DeviceCapacityType, Long) In e.ListCapacityType
                        list.Add(pair)
                    Next
                    Dim strSystem As String = String.Empty
                    Dim strData As String = String.Empty

                    Dim lngTotal As Long = 0

                    Dim lngSystem As Long = 0
                    Dim lngSystemUse As Long = 0

                    Dim lngData As Long = 0
                    Dim lngDataUse As Long = 0

                    If e.ListCapacityType.ContainsKey(DeviceCapacityType.TotalDiskCapacity) Then
                        lngTotal = e.ListCapacityType(DeviceCapacityType.TotalDiskCapacity)
                    End If

                    Dim strTemp As String = Me.Language.GetString("Welcome.Label.SystemCapacity") & Me.Language.GetString("Common.Symbol.Colon") & " {0}"
                    strTemp = strTemp.PadRight(21, " ")
                    If e.ListCapacityType.ContainsKey(DeviceCapacityType.TotalSystemCapacity) Then
                        lngSystem = e.ListCapacityType(DeviceCapacityType.TotalSystemCapacity)
                        strSystem &= String.Format(strTemp, Utility.FormatFileSize(lngSystem).PadRight(10, " "))
                        Me.lblSystem.Text = strSystem

                    ElseIf Not e.ListCapacityType.ContainsKey(DeviceCapacityType.TotalDataCapacity) Then
                        strSystem &= String.Format(strTemp, Me.Language.GetString("Common.Label.Unknow").PadRight(10, " "))
                        Me.lblSystem.Text = strSystem

                    End If


                    '"已用：{0}        空闲：{1}"
                    strTemp = Me.Language.GetString("Welcome.Label.UsedSpace") & Me.Language.GetString("Common.Symbol.Colon") & " {0}          " & Me.Language.GetString("Welcome.Label.Empty") & Me.Language.GetString("Common.Symbol.Colon") & " {1}"
                    If e.ListCapacityType.ContainsKey(DeviceCapacityType.TotalSystemAvailable) Then
                        lngSystemUse = e.ListCapacityType(DeviceCapacityType.TotalSystemAvailable)
                        strSystem = String.Format(strTemp, Utility.FormatFileSize(lngSystem - lngSystemUse).PadRight(10, " "), Utility.FormatFileSize(lngSystemUse).PadRight(10, " "))
                        Me.lblSystemUsed.Text = strSystem

                    ElseIf Not e.ListCapacityType.ContainsKey(DeviceCapacityType.TotalDataCapacity) Then
                        strSystem = String.Format(strTemp, Me.Language.GetString("Common.Label.Unknow").PadRight(10, " "), Me.Language.GetString("Common.Label.Unknow").PadRight(10, " "))
                        Me.lblSystemUsed.Text = strSystem

                    End If


                    ' "数据容量：{0}        "
                    strTemp = Me.Language.GetString("Welcome.Label.DataCapacity") & Me.Language.GetString("Common.Symbol.Colon") & " {0}"
                    strTemp = strTemp.PadRight(21, " ")
                    If e.ListCapacityType.ContainsKey(DeviceCapacityType.TotalDataCapacity) Then
                        lngData = e.ListCapacityType(DeviceCapacityType.TotalDataCapacity)
                        strData &= String.Format(strTemp, Utility.FormatFileSize(lngData).PadRight(10, " "))
                    Else
                        strData &= String.Format(strTemp, Me.Language.GetString("Common.Label.Unknow").PadRight(10, " "))
                    End If
                    Me.lblData.Text = strData
                    '"已用：{0}        空闲：{1}"
                    strTemp = Me.Language.GetString("Welcome.Label.UsedSpace") & Me.Language.GetString("Common.Symbol.Colon") & " {0}          " & Me.Language.GetString("Welcome.Label.Empty") & Me.Language.GetString("Common.Symbol.Colon") & " {1}"
                    If e.ListCapacityType.ContainsKey(DeviceCapacityType.TotalDataAvailable) Then
                        lngDataUse = e.ListCapacityType(DeviceCapacityType.TotalDataAvailable)
                        strData = String.Format(strTemp, Utility.FormatFileSize(lngData - lngDataUse).PadRight(10, " "), Utility.FormatFileSize(lngDataUse).PadRight(10, " "))
                    Else
                        strData = String.Format(strTemp, Me.Language.GetString("Common.Label.Unknow").PadRight(10, " "), Me.Language.GetString("Common.Label.Unknow").PadRight(10, " "))
                    End If
                    Me.lblDataUsed.Text = strData

                    'Me.lblSystem.Text = strSystem
                    'Me.lblData.Text = strData
                    Me.btnRefresh.Enabled = True

                    Me.pgbSystem.tbPlayValue = (lngSystem - lngSystemUse) / lngSystem * 100
                    Me.pgbData.tbPlayValue = (lngData - lngDataUse) / lngData * 100

                Catch ex As Exception
                End Try
            End If
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

    'OnCapacityLoadCompleted：回调一次，将结果都返回。（新的获取容量的方式）
    Private Sub OnCapacityLoadCompleted(ByVal sender As Object, ByVal e As DeviceCapacityArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of DeviceCapacityArgs)(AddressOf OnCapacityLoadCompleted), sender, e)
        Else
            Me.OnCapacityCallback(sender, e)

            If Me.mDevice IsNot Nothing Then
                Me.btnRefresh.Visible = True
                Me.picLoading.Visible = False
            End If
        End If
    End Sub

#End Region

#Region "--- 初始化DataTable ---"

    Private Sub InitDataSource()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf InitDataSource))
        Else
            If mDataSource Is Nothing Then
                mDataSource = New DataTable()
                With mDataSource
                    .Columns.Add("Key", GetType(String))
                    .Columns.Add("Value", GetType(String))
                End With
            Else
                mDataSource.Rows.Clear()
            End If
        End If
    End Sub

    '根据name获取其index
    Private Function GetIndexByNameFromDataTable(ByVal name As String) As Integer
        Dim startIndex As Integer = 0

        Try
            For index As Integer = 0 To Me.mDataSource.Rows.Count - 1
                If String.Compare(Me.mDataSource.Rows(index).Item(0).ToString(), name, True) = 0 Then
                    startIndex = index
                    Exit For
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetStartIndexFromDataTable")
        End Try

        Return startIndex
    End Function

    Private Function CreateNewDataRow(ByVal name As String, ByVal value As String) As DataRow
        Dim row As DataRow = Me.mDataSource.NewRow()
        row(0) = name
        row(1) = value

        Return row
    End Function

#End Region

#Region "--- 获取设备图片 ---"

    '删除本地错误的图片（之前服务器部署的有问题图片）
    Private Sub DeleteLocalErrorImage(ByVal strProduct As String, ByVal iPhoneColor As PHBDeviceColorVariation)
        Try
            Dim strImagePath As String = SummaryInfo.GetIconPathByDevice(strProduct, iPhoneColor)
            Dim strImageName As String = Path.GetFileName(strImagePath)
            Dim strHash As String = ""
            Dim blnDelete As Boolean = False

            If Not File.Exists(strImagePath) Then
                Return
            End If

            Select Case strImageName
                Case "iphone5s_black.png"
                    strHash = Common.GetMd5Base64FromFile(strImagePath)
                    If String.Compare(strHash, "Ptw5ugGdNyqJmympbURvFw==") = 0 Then
                        blnDelete = True
                    End If

                Case "iphone5s_white.png"
                    strHash = Common.GetMd5Base64FromFile(strImagePath)
                    If String.Compare(strHash, "URowrh04V4I/xX91OCzwPg==") = 0 Then
                        blnDelete = True
                    End If

                Case "iphone5s_gold.png"
                    strHash = Common.GetMd5Base64FromFile(strImagePath)
                    If String.Compare(strHash, "0ZgK6xsoW4YU0zajG/Uwsg==") = 0 Then
                        blnDelete = True
                    End If
            End Select

            If blnDelete Then
                File.Delete(strImagePath)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeleteLocalErrorImage")
        End Try
    End Sub

#End Region

#End Region

    Private Sub txtNameValue_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles txtNameValue.Validating
        Me.ChangeDeviceName()
    End Sub

    Private Sub frmSummary_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Click
        Me.ChangeDeviceName()
    End Sub

    Private Sub pnlDetail_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pnlDetail.Click
        Me.ChangeDeviceName()
    End Sub

    Private Sub btnAppSearch_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Common.OpenExplorer("http://cx.tongbu.com/?s=jbhelper_cn")
    End Sub

    Private Sub btnGuide_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Common.OpenExplorer("http://app.tongbu.com/article/15647.html")
    End Sub

    Private Sub btnJailbreak_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnJailbreak.Click
        Me.btnJailbreak.Enabled = False

        Me.StartJailbreakTool()

        Me.mApplication.GotoItem(ActionFuncType.Course)
    End Sub

    Public Sub StopRefresh()
        Me.tmrRefresh.Stop()
    End Sub

End Class
