<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
	<meta http-equiv="Content-Language" content="zh-cn">
	<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
	<title>同步助手（商家版）公告</title>
	<style type="text/css">
	  body {font-family: "microsoft yahei",Arial,Helvetica,sans-serif,"宋体";font-size: 13px;color: #666;}
	  body, ul {padding: 0;margin: 0;}
	  a {text-decoration: none;}
	  .notes {list-style: none;padding: 0;}
    .notes li {padding: 10px 15px;}
    .notes .even {background-color: #FFFFFF;}
    .notes .odd {background-color: #EEF4FA;}
	  .notes a {color: #666;display: inline-block;width: 100%;color: #1E8BF4;}
	  .notes a:hover {text-decoration: underline;}
    .notes span {float: left;width: 230px;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;cursor: pointer;}
	  .notes i {float: right;zoom: 1;width: 40px;font-style: normal;cursor: pointer;}
	  .notes .red {color: #F63732;}
	</style>
</head>
<body>
	<ul class="notes" id="notes">
		<!-- <li>
			<a class="red" href="#">12月份结算公告<span>2015-01-01</span></a>
		</li>
		<li>
			<a href="#">12月份结算通知<span>2015-01-01</span></a>
		</li>
		<li>
			<a href="#">全民奇迹上架<span>2015-01-01</span></a>
		</li>
		<li>
			<a href="#">西游大乱斗上架<span>2015-01-01</span></a>
		</li> -->
	</ul>
</body>
<script type="text/javascript">
// var data = '{"data":[{"title":"【下架】内涵段子","id":582,"type":3,"time":"2015-07-09 14:26:05"}]}';
// var usertoken = '8d2fc45e1c94349aa3ad0807bf20081137fe09804d23fc94e2358b705966aa2267aca307ddc7fcb70049fe2a92deeef1272238be168a97774ca4fbdf08abcfad20d95f73b34c3fe6';
// showNote(data, usertoken);

function showNote(data, usertoken) {
  /*document.getElementById('notes').innerHTML = data;
  return;*/
  var json;

  try {
    json = (new Function("return " + data))();
  } catch(err) {
    var txt = '';
    txt = "json格式错误: \n\n"
        + "错误描述: " + err.message + "\n\n"
        + "错误格式: "+data+"\n\n";
    document.getElementById('notes').innerHTML = '<li>'+txt+'</li>';
  }

  var html = '';
  var notesData = undefined != json.data ? json.data : [];

  if (notesData.length > 0) {
  	for (var i = 0, len = notesData.length; i < len; i++) {
  		html += '<li class="'+((i+1) % 2 == 0 ? 'even' : 'odd')+'">'
  		     +  '<a target="_blank" '
  		        + (notesData[i].type == 1 ? 'class="red" ' : '')
  		        + 'href="http://zj.tongbu.com/article/notic/id/'+notesData[i].id+'?usertoken='+usertoken+'">'
  		     +  '<span>'+notesData[i].title+'</span>'
  		     +  '<i>'+dateFormat(notesData[i].time, 'MM-dd')+'</i>'
  		     +  '</a>'
  		     +  '</li>';
  	};
  	document.getElementById('notes').innerHTML = html;
  }
}

//时间格式化
function dateFormat (time, format) {
    var parseDate = function (str) {  

      if (typeof str == 'string') {  
        var results = str.match(/^ *(\d{4})[\/|-](\d{1,2})[\/|-](\d{1,2}) *$/); 

        if(results && results.length>3) {
          return new Date(parseInt(results[1], '10'),parseInt(results[2], '10') -1,parseInt(results[3], '10'));  
        }

        results = str.match(/^ *(\d{4})[\/|-](\d{1,2})[\/|-](\d{1,2}) +(\d{1,2}):(\d{1,2}):(\d{1,2}) *$/);  
        if(results && results.length>6) {
          return new Date(parseInt(results[1], '10'),parseInt(results[2], '10') -1,parseInt(results[3], '10'),parseInt(results[4], '10'),parseInt(results[5], '10'),parseInt(results[6], '10'));  
        }

        results = str.match(/^ *(\d{4})[\/|-](\d{1,2})[\/|-](\d{1,2}) +(\d{1,2}):(\d{1,2}):(\d{1,2})\.(\d{1,9}) *$/);  
        if(results && results.length>7) {
          return new Date(parseInt(results[1], '10'),parseInt(results[2], '10') -1,parseInt(results[3], '10'),parseInt(results[4], '10'),parseInt(results[5], '10'),parseInt(results[6], '10'),parseInt(results[7], '10'));   
        }

      }  
      return null;
    }  

    var formatFn = function (dateStr, format) {
      var date = parseDate(dateStr);

      var o = {
              "M+" : date.getMonth()+1, //month
              "d+" : date.getDate(), //day
              "h+" : date.getHours(), //hour
              "m+" : date.getMinutes(), //minute
              "s+" : date.getSeconds(), //second
              "q+" : Math.floor((date.getMonth()+3)/3), //quarter
              "S" : date.getMilliseconds() //millisecond
          }
        if(/(y+)/.test(format))
        format=format.replace(RegExp.$1,(date.getFullYear()+"").substr(4 - RegExp.$1.length));
        for(var k in o)
        if(new RegExp("("+ k +")").test(format))
        format = format.replace(RegExp.$1,RegExp.$1.length==1 ? o[k] : ("00"+ o[k]).substr((""+ o[k]).length));
        return format;
    }
    
    var format = undefined != format ? format : 'yyyy-MM-dd';
    return formatFn(time, format);
  }

  (function run() {
    if (window.external == undefined || typeof window.external.SendMessage == 'undefined') {
      return
    }

    window.external.SendMessage('{action:"loadcomplete"}');

  })()
  
</script>
</html>