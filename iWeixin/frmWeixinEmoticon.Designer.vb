﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeixinEmoticon
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeixinEmoticon))
        Me.pnlInstall = New iTong.Components.tbPanel()
        Me.btnDownloadWeixin = New iTong.Components.tbButton()
        Me.btnReCheck = New iTong.Components.tbButton()
        Me.lblDownloadWeixin = New System.Windows.Forms.Label()
        Me.lblWeixinDescription = New System.Windows.Forms.Label()
        Me.lblWeixinTitle = New System.Windows.Forms.Label()
        Me.picWeixinIcon = New System.Windows.Forms.PictureBox()
        Me.lblState = New iTong.Components.tbLabel()
        Me.pnlMain = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlWeb = New iTong.Components.tbPanel()
        Me.wbsSite = New iTong.Components.tbWebBrowserEx()
        Me.pnlTreeview = New iTong.Components.tbPanel()
        Me.pnlSplit = New iTong.Components.tbPanel()
        Me.tvwEmotion = New iTong.Components.tbTreeView()
        Me.pnlLoading = New iTong.Components.tbPanel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.pnlItems = New iTong.Components.tbPanel()
        Me.pnlControlIcon = New iTong.Components.tbPanel()
        Me.btnBack = New iTong.Components.tbButton()
        Me.btnImportIcon = New iTong.Components.tbButton()
        Me.btnExportIcon = New iTong.Components.tbButton()
        Me.btnSelectAll = New iTong.Components.tbButton()
        Me.btnDeleteIcon = New iTong.Components.tbButton()
        Me.pnlGroups = New iTong.Components.tbPanel()
        Me.pnlControlGroup = New iTong.Components.tbPanel()
        Me.lblHelper = New iTong.Components.tbLabel()
        Me.btnSelectAllEmoticon = New iTong.Components.tbButton()
        Me.btnRestore = New iTong.Components.tbButton()
        Me.btnImportGroup = New iTong.Components.tbButton()
        Me.btnExportGroup = New iTong.Components.tbButton()
        Me.btnDeleteGroup = New iTong.Components.tbButton()
        Me.btnAddGroup = New iTong.Components.tbButton()
        Me.btnRefreshGroup = New iTong.Components.tbButton()
        Me.pnlBottom = New iTong.Components.tbPanel()
        Me.pbarShowProgress = New iTong.Components.tbControlBar()
        Me.btnAbrot = New iTong.Components.tbButton()
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.pnlGuide = New iTong.Components.tbPanel()
        Me.lblImportDescription = New System.Windows.Forms.Label()
        Me.picError = New iTong.Components.tbPictureBox()
        Me.lblSetp2 = New System.Windows.Forms.Label()
        Me.lblSetp1 = New System.Windows.Forms.Label()
        Me.btnOK = New iTong.Components.tbButton()
        Me.lblTitle = New System.Windows.Forms.Label()
        Me.chkCheckCloseWeixin = New iTong.Components.tbCheckBox()
        Me.picclose = New iTong.Components.tbPictureBox()
        Me.picTitle = New iTong.Components.tbPictureBox()
        Me.cmsRestore = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.AToolStripMenuItem = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlInstall.SuspendLayout()
        CType(Me.picWeixinIcon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlMain.SuspendLayout()
        Me.pnlWeb.SuspendLayout()
        Me.pnlTreeview.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlControlIcon.SuspendLayout()
        Me.pnlControlGroup.SuspendLayout()
        Me.pnlBottom.SuspendLayout()
        Me.pnlGuide.SuspendLayout()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picclose, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picTitle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.cmsRestore.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(1850, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(1826, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(1802, 0)
        '
        'pnlInstall
        '
        Me.pnlInstall.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlInstall.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlInstall.Controls.Add(Me.btnDownloadWeixin)
        Me.pnlInstall.Controls.Add(Me.btnReCheck)
        Me.pnlInstall.Controls.Add(Me.lblDownloadWeixin)
        Me.pnlInstall.Controls.Add(Me.lblWeixinDescription)
        Me.pnlInstall.Controls.Add(Me.lblWeixinTitle)
        Me.pnlInstall.Controls.Add(Me.picWeixinIcon)
        Me.pnlInstall.Location = New System.Drawing.Point(0, 0)
        Me.pnlInstall.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlInstall.Name = "pnlInstall"
        Me.pnlMain.SetRowSpan(Me.pnlInstall, 2)
        Me.pnlInstall.Size = New System.Drawing.Size(120, 511)
        Me.pnlInstall.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlInstall.TabIndex = 7
        Me.pnlInstall.tbBackgroundImage = Nothing
        Me.pnlInstall.tbShowWatermark = False
        Me.pnlInstall.tbSplit = "0,0,0,0"
        Me.pnlInstall.tbWatermark = Nothing
        Me.pnlInstall.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlInstall.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnDownloadWeixin
        '
        Me.btnDownloadWeixin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnDownloadWeixin.BackColor = System.Drawing.Color.Transparent
        Me.btnDownloadWeixin.BindingForm = Nothing
        Me.btnDownloadWeixin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDownloadWeixin.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDownloadWeixin.Location = New System.Drawing.Point(172, 314)
        Me.btnDownloadWeixin.Name = "btnDownloadWeixin"
        Me.btnDownloadWeixin.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDownloadWeixin.Selectable = True
        Me.btnDownloadWeixin.Size = New System.Drawing.Size(12, 12)
        Me.btnDownloadWeixin.TabIndex = 31
        Me.btnDownloadWeixin.tbAdriftIconWhenHover = False
        Me.btnDownloadWeixin.tbAutoSize = False
        Me.btnDownloadWeixin.tbAutoSizeEx = True
        Me.btnDownloadWeixin.tbBackgroundImage = Global.iTong.iWeixin.My.Resources.Resources.btn_3_goto
        Me.btnDownloadWeixin.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnDownloadWeixin.tbBadgeNumber = 0
        Me.btnDownloadWeixin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDownloadWeixin.tbEndEllipsis = False
        Me.btnDownloadWeixin.tbIconHoldPlace = True
        Me.btnDownloadWeixin.tbIconImage = Nothing
        Me.btnDownloadWeixin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDownloadWeixin.tbIconMore = False
        Me.btnDownloadWeixin.tbIconMouseDown = Nothing
        Me.btnDownloadWeixin.tbIconMouseHover = Nothing
        Me.btnDownloadWeixin.tbIconMouseLeave = Nothing
        Me.btnDownloadWeixin.tbIconPlaceText = 2
        Me.btnDownloadWeixin.tbIconReadOnly = Nothing
        Me.btnDownloadWeixin.tbImageMouseDown = Nothing
        Me.btnDownloadWeixin.tbImageMouseHover = Nothing
        Me.btnDownloadWeixin.tbImageMouseLeave = Nothing
        Me.btnDownloadWeixin.tbProgressValue = 50
        Me.btnDownloadWeixin.tbReadOnly = False
        Me.btnDownloadWeixin.tbReadOnlyText = False
        Me.btnDownloadWeixin.tbShadow = False
        Me.btnDownloadWeixin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDownloadWeixin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDownloadWeixin.tbShowDot = False
        Me.btnDownloadWeixin.tbShowMoreIconImg = CType(resources.GetObject("btnDownloadWeixin.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDownloadWeixin.tbShowNew = False
        Me.btnDownloadWeixin.tbShowProgress = False
        Me.btnDownloadWeixin.tbShowTip = True
        Me.btnDownloadWeixin.tbShowToolTipOnButton = False
        Me.btnDownloadWeixin.tbSplit = "0,0,0,0"
        Me.btnDownloadWeixin.tbText = ""
        Me.btnDownloadWeixin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.tbTextColor = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorDisable = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorDown = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorHover = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextMouseDownPlace = 0
        Me.btnDownloadWeixin.tbToolTip = ""
        Me.btnDownloadWeixin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDownloadWeixin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDownloadWeixin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.VisibleEx = True
        '
        'btnReCheck
        '
        Me.btnReCheck.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnReCheck.BackColor = System.Drawing.Color.Transparent
        Me.btnReCheck.BindingForm = Nothing
        Me.btnReCheck.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReCheck.Location = New System.Drawing.Point(-39, 309)
        Me.btnReCheck.Name = "btnReCheck"
        Me.btnReCheck.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnReCheck.Selectable = True
        Me.btnReCheck.Size = New System.Drawing.Size(63, 22)
        Me.btnReCheck.TabIndex = 30
        Me.btnReCheck.tbAdriftIconWhenHover = False
        Me.btnReCheck.tbAutoSize = True
        Me.btnReCheck.tbAutoSizeEx = True
        Me.btnReCheck.tbBackgroundImage = Nothing
        Me.btnReCheck.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnReCheck.tbBadgeNumber = 0
        Me.btnReCheck.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReCheck.tbEndEllipsis = False
        Me.btnReCheck.tbIconHoldPlace = True
        Me.btnReCheck.tbIconImage = Nothing
        Me.btnReCheck.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheck.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReCheck.tbIconMore = False
        Me.btnReCheck.tbIconMouseDown = Nothing
        Me.btnReCheck.tbIconMouseHover = Nothing
        Me.btnReCheck.tbIconMouseLeave = Nothing
        Me.btnReCheck.tbIconPlaceText = 2
        Me.btnReCheck.tbIconReadOnly = Nothing
        Me.btnReCheck.tbImageMouseDown = Nothing
        Me.btnReCheck.tbImageMouseHover = Nothing
        Me.btnReCheck.tbImageMouseLeave = Nothing
        Me.btnReCheck.tbProgressValue = 50
        Me.btnReCheck.tbReadOnly = False
        Me.btnReCheck.tbReadOnlyText = False
        Me.btnReCheck.tbShadow = False
        Me.btnReCheck.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnReCheck.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnReCheck.tbShowDot = False
        Me.btnReCheck.tbShowMoreIconImg = CType(resources.GetObject("btnReCheck.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReCheck.tbShowNew = False
        Me.btnReCheck.tbShowProgress = False
        Me.btnReCheck.tbShowTip = True
        Me.btnReCheck.tbShowToolTipOnButton = False
        Me.btnReCheck.tbSplit = "13,11,13,11"
        Me.btnReCheck.tbText = "重新检查"
        Me.btnReCheck.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheck.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnReCheck.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReCheck.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnReCheck.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(200, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnReCheck.tbTextMouseDownPlace = 0
        Me.btnReCheck.tbToolTip = ""
        Me.btnReCheck.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReCheck.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReCheck.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheck.VisibleEx = True
        '
        'lblDownloadWeixin
        '
        Me.lblDownloadWeixin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDownloadWeixin.AutoSize = True
        Me.lblDownloadWeixin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblDownloadWeixin.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblDownloadWeixin.Location = New System.Drawing.Point(64, 311)
        Me.lblDownloadWeixin.Name = "lblDownloadWeixin"
        Me.lblDownloadWeixin.Size = New System.Drawing.Size(101, 12)
        Me.lblDownloadWeixin.TabIndex = 29
        Me.lblDownloadWeixin.Text = "立即免费获取微信"
        '
        'lblWeixinDescription
        '
        Me.lblWeixinDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinDescription.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWeixinDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer))
        Me.lblWeixinDescription.Location = New System.Drawing.Point(-36, 211)
        Me.lblWeixinDescription.Name = "lblWeixinDescription"
        Me.lblWeixinDescription.Size = New System.Drawing.Size(373, 83)
        Me.lblWeixinDescription.TabIndex = 28
        Me.lblWeixinDescription.Text = "微信是一款手机通信软件，支持通过手机网络发送语音短信、" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "视频、图片和文字，可以单聊及群聊，还能根据地理位置找到" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "附近的人，带给朋友们全新的移动沟通体验" & _
    "。"
        '
        'lblWeixinTitle
        '
        Me.lblWeixinTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinTitle.AutoSize = True
        Me.lblWeixinTitle.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblWeixinTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblWeixinTitle.Location = New System.Drawing.Point(-36, 176)
        Me.lblWeixinTitle.Name = "lblWeixinTitle"
        Me.lblWeixinTitle.Size = New System.Drawing.Size(119, 16)
        Me.lblWeixinTitle.TabIndex = 27
        Me.lblWeixinTitle.Text = "尚未安装 微信"
        '
        'picWeixinIcon
        '
        Me.picWeixinIcon.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picWeixinIcon.Image = Global.iTong.iWeixin.My.Resources.Resources.file_weixin_large
        Me.picWeixinIcon.Location = New System.Drawing.Point(-175, 195)
        Me.picWeixinIcon.Name = "picWeixinIcon"
        Me.picWeixinIcon.Size = New System.Drawing.Size(114, 114)
        Me.picWeixinIcon.TabIndex = 26
        Me.picWeixinIcon.TabStop = False
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(277, 578)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(1320, 18)
        Me.lblState.TabIndex = 29
        Me.lblState.tbAdriftWhenHover = False
        Me.lblState.tbAutoEllipsis = False
        Me.lblState.tbAutoSize = False
        Me.lblState.tbHideImage = False
        Me.lblState.tbIconImage = Nothing
        Me.lblState.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblState.tbIconPlaceText = 5
        Me.lblState.tbShadow = False
        Me.lblState.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblState.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblState.tbShowScrolling = False
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.ColumnCount = 7
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 120.0!))
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 120.0!))
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 198.0!))
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 600.0!))
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 213.0!))
        Me.pnlMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 870.0!))
        Me.pnlMain.Controls.Add(Me.pnlWeb, 5, 0)
        Me.pnlMain.Controls.Add(Me.pnlTreeview, 2, 0)
        Me.pnlMain.Controls.Add(Me.pnlLoading, 1, 0)
        Me.pnlMain.Controls.Add(Me.pnlInstall, 0, 0)
        Me.pnlMain.Controls.Add(Me.pnlItems, 4, 1)
        Me.pnlMain.Controls.Add(Me.pnlControlIcon, 4, 0)
        Me.pnlMain.Controls.Add(Me.pnlGroups, 3, 1)
        Me.pnlMain.Controls.Add(Me.pnlControlGroup, 3, 0)
        Me.pnlMain.Controls.Add(Me.pnlBottom, 0, 2)
        Me.pnlMain.Controls.Add(Me.pnlGuide, 6, 0)
        Me.pnlMain.Location = New System.Drawing.Point(1, 32)
        Me.pnlMain.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.RowCount = 3
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 33.0!))
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.pnlMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.pnlMain.Size = New System.Drawing.Size(1872, 541)
        Me.pnlMain.TabIndex = 30
        '
        'pnlWeb
        '
        Me.pnlWeb.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlWeb.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlWeb.Controls.Add(Me.wbsSite)
        Me.pnlWeb.Location = New System.Drawing.Point(1438, 0)
        Me.pnlWeb.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlWeb.Name = "pnlWeb"
        Me.pnlMain.SetRowSpan(Me.pnlWeb, 2)
        Me.pnlWeb.Size = New System.Drawing.Size(213, 511)
        Me.pnlWeb.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlWeb.TabIndex = 14
        Me.pnlWeb.tbBackgroundImage = Nothing
        Me.pnlWeb.tbShowWatermark = False
        Me.pnlWeb.tbSplit = "0,0,0,0"
        Me.pnlWeb.tbWatermark = Nothing
        Me.pnlWeb.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlWeb.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'wbsSite
        '
        Me.wbsSite.AllowWebBrowserDrop = False
        Me.wbsSite.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.wbsSite.Client = ""
        Me.wbsSite.DeviceList = Nothing
        Me.wbsSite.IsConnected = False
        Me.wbsSite.IsWebBrowserContextMenuEnabled = False
        Me.wbsSite.Jailbreaked = False
        Me.wbsSite.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.wbsSite.LoadingFont = Nothing
        Me.wbsSite.LoadingGif = CType(resources.GetObject("wbsSite.LoadingGif"), System.Drawing.Image)
        Me.wbsSite.Location = New System.Drawing.Point(-2, -2)
        Me.wbsSite.MinimumSize = New System.Drawing.Size(20, 20)
        Me.wbsSite.Name = "wbsSite"
        Me.wbsSite.ShowLoadingWait = False
        Me.wbsSite.ShowNavigateErrorPage = True
        Me.wbsSite.ShowProgress = True
        Me.wbsSite.Size = New System.Drawing.Size(216, 514)
        Me.wbsSite.SN = ""
        Me.wbsSite.TabIndex = 0
        Me.wbsSite.UserInfo = ""
        Me.wbsSite.UserInfoEncode = ""
        '
        'pnlTreeview
        '
        Me.pnlTreeview.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlTreeview.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlTreeview.Controls.Add(Me.pnlSplit)
        Me.pnlTreeview.Controls.Add(Me.tvwEmotion)
        Me.pnlTreeview.Location = New System.Drawing.Point(240, 0)
        Me.pnlTreeview.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTreeview.Name = "pnlTreeview"
        Me.pnlMain.SetRowSpan(Me.pnlTreeview, 2)
        Me.pnlTreeview.Size = New System.Drawing.Size(198, 511)
        Me.pnlTreeview.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTreeview.TabIndex = 12
        Me.pnlTreeview.tbBackgroundImage = Nothing
        Me.pnlTreeview.tbShowWatermark = False
        Me.pnlTreeview.tbSplit = "0,0,0,0"
        Me.pnlTreeview.tbWatermark = Nothing
        Me.pnlTreeview.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTreeview.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlSplit
        '
        Me.pnlSplit.BackColor = System.Drawing.Color.FromArgb(CType(CType(188, Byte), Integer), CType(CType(197, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.pnlSplit.Dock = System.Windows.Forms.DockStyle.Right
        Me.pnlSplit.Location = New System.Drawing.Point(197, 0)
        Me.pnlSplit.Name = "pnlSplit"
        Me.pnlSplit.Size = New System.Drawing.Size(1, 511)
        Me.pnlSplit.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlSplit.TabIndex = 2
        Me.pnlSplit.tbBackgroundImage = Nothing
        Me.pnlSplit.tbShowWatermark = False
        Me.pnlSplit.tbSplit = "0,0,0,0"
        Me.pnlSplit.tbWatermark = Nothing
        Me.pnlSplit.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlSplit.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'tvwEmotion
        '
        Me.tvwEmotion.AutoScroll = True
        Me.tvwEmotion.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.tvwEmotion.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tvwEmotion.ImageList = Nothing
        Me.tvwEmotion.Indent = 13
        Me.tvwEmotion.ItemHeight = 28
        Me.tvwEmotion.Location = New System.Drawing.Point(0, 0)
        Me.tvwEmotion.Margin = New System.Windows.Forms.Padding(0)
        Me.tvwEmotion.Name = "tvwEmotion"
        Me.tvwEmotion.Padding = New System.Windows.Forms.Padding(0, 0, 0, 10)
        Me.tvwEmotion.PathSeparator = "\"
        Me.tvwEmotion.SelectedNode = Nothing
        Me.tvwEmotion.Size = New System.Drawing.Size(198, 511)
        Me.tvwEmotion.TabIndex = 1
        Me.tvwEmotion.TabStop = True
        Me.tvwEmotion.tbBackgroundImage = Nothing
        Me.tvwEmotion.tbNodeCountSelectedColor = System.Drawing.Color.White
        Me.tvwEmotion.tbNodeDiff = 20
        Me.tvwEmotion.tbNodeFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.tvwEmotion.tbNodeForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.tvwEmotion.tbNodeLabelFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.tvwEmotion.tbNodeLabelForeColor = System.Drawing.Color.Black
        Me.tvwEmotion.tbNodePadding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.tvwEmotion.tbNodeSelectedBackground = CType(resources.GetObject("tvwEmotion.tbNodeSelectedBackground"), System.Drawing.Image)
        Me.tvwEmotion.tbNodeShadowColor = System.Drawing.Color.Empty
        Me.tvwEmotion.tbSplit = "1,27,3,3"
        Me.tvwEmotion.WrapContents = False
        '
        'pnlLoading
        '
        Me.pnlLoading.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.picLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(120, 0)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlMain.SetRowSpan(Me.pnlLoading, 2)
        Me.pnlLoading.Size = New System.Drawing.Size(120, 511)
        Me.pnlLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoading.TabIndex = 8
        Me.pnlLoading.tbBackgroundImage = Nothing
        Me.pnlLoading.tbShowWatermark = False
        Me.pnlLoading.tbSplit = "0,0,0,0"
        Me.pnlLoading.tbWatermark = Nothing
        Me.pnlLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(73, 234)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(171, 45)
        Me.lblLoading.TabIndex = 13
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picLoading
        '
        Me.picLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.picLoading.Image = Global.iTong.iWeixin.My.Resources.Resources.pnl_loading
        Me.picLoading.Location = New System.Drawing.Point(-21, 208)
        Me.picLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(91, 95)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picLoading.TabIndex = 12
        Me.picLoading.TabStop = False
        '
        'pnlItems
        '
        Me.pnlItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlItems.BackColor = System.Drawing.Color.White
        Me.pnlItems.Location = New System.Drawing.Point(1038, 33)
        Me.pnlItems.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlItems.Name = "pnlItems"
        Me.pnlItems.Size = New System.Drawing.Size(400, 478)
        Me.pnlItems.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlItems.TabIndex = 9
        Me.pnlItems.tbBackgroundImage = Nothing
        Me.pnlItems.tbShowWatermark = False
        Me.pnlItems.tbSplit = "0,0,0,0"
        Me.pnlItems.tbWatermark = Nothing
        Me.pnlItems.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlItems.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlControlIcon
        '
        Me.pnlControlIcon.BackColor = System.Drawing.Color.FromArgb(CType(CType(237, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlControlIcon.Controls.Add(Me.btnBack)
        Me.pnlControlIcon.Controls.Add(Me.btnImportIcon)
        Me.pnlControlIcon.Controls.Add(Me.btnExportIcon)
        Me.pnlControlIcon.Controls.Add(Me.btnSelectAll)
        Me.pnlControlIcon.Controls.Add(Me.btnDeleteIcon)
        Me.pnlControlIcon.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlControlIcon.Location = New System.Drawing.Point(1038, 0)
        Me.pnlControlIcon.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlControlIcon.Name = "pnlControlIcon"
        Me.pnlControlIcon.Size = New System.Drawing.Size(400, 33)
        Me.pnlControlIcon.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlControlIcon.TabIndex = 11
        Me.pnlControlIcon.tbBackgroundImage = Global.iTong.iWeixin.My.Resources.Resources.pnl_bg_navigation
        Me.pnlControlIcon.tbShowWatermark = False
        Me.pnlControlIcon.tbSplit = "3,3,3,3"
        Me.pnlControlIcon.tbWatermark = Nothing
        Me.pnlControlIcon.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlControlIcon.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnBack
        '
        Me.btnBack.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnBack.BackColor = System.Drawing.Color.Transparent
        Me.btnBack.BindingForm = Nothing
        Me.btnBack.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBack.Location = New System.Drawing.Point(8, 5)
        Me.btnBack.Name = "btnBack"
        Me.btnBack.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBack.Selectable = True
        Me.btnBack.Size = New System.Drawing.Size(57, 23)
        Me.btnBack.TabIndex = 19
        Me.btnBack.tbAdriftIconWhenHover = False
        Me.btnBack.tbAutoSize = False
        Me.btnBack.tbAutoSizeEx = True
        Me.btnBack.tbBackgroundImage = Nothing
        Me.btnBack.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBack.tbBadgeNumber = 0
        Me.btnBack.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBack.tbEndEllipsis = False
        Me.btnBack.tbIconHoldPlace = True
        Me.btnBack.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_back_4
        Me.btnBack.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBack.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnBack.tbIconMore = False
        Me.btnBack.tbIconMouseDown = Nothing
        Me.btnBack.tbIconMouseHover = Nothing
        Me.btnBack.tbIconMouseLeave = Nothing
        Me.btnBack.tbIconPlaceText = 2
        Me.btnBack.tbIconReadOnly = Nothing
        Me.btnBack.tbImageMouseDown = Nothing
        Me.btnBack.tbImageMouseHover = Nothing
        Me.btnBack.tbImageMouseLeave = Nothing
        Me.btnBack.tbProgressValue = 50
        Me.btnBack.tbReadOnly = False
        Me.btnBack.tbReadOnlyText = False
        Me.btnBack.tbShadow = False
        Me.btnBack.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBack.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBack.tbShowDot = False
        Me.btnBack.tbShowMoreIconImg = CType(resources.GetObject("btnBack.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBack.tbShowNew = False
        Me.btnBack.tbShowProgress = False
        Me.btnBack.tbShowTip = True
        Me.btnBack.tbShowToolTipOnButton = False
        Me.btnBack.tbSplit = "13,11,13,11"
        Me.btnBack.tbText = "返回"
        Me.btnBack.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnBack.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnBack.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnBack.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnBack.tbTextMouseDownPlace = 2
        Me.btnBack.tbToolTip = ""
        Me.btnBack.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBack.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBack.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.VisibleEx = True
        '
        'btnImportIcon
        '
        Me.btnImportIcon.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnImportIcon.BackColor = System.Drawing.Color.Transparent
        Me.btnImportIcon.BindingForm = Nothing
        Me.btnImportIcon.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImportIcon.Location = New System.Drawing.Point(74, 5)
        Me.btnImportIcon.Name = "btnImportIcon"
        Me.btnImportIcon.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImportIcon.Selectable = True
        Me.btnImportIcon.Size = New System.Drawing.Size(57, 23)
        Me.btnImportIcon.TabIndex = 18
        Me.btnImportIcon.tbAdriftIconWhenHover = False
        Me.btnImportIcon.tbAutoSize = False
        Me.btnImportIcon.tbAutoSizeEx = True
        Me.btnImportIcon.tbBackgroundImage = Nothing
        Me.btnImportIcon.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnImportIcon.tbBadgeNumber = 0
        Me.btnImportIcon.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImportIcon.tbEndEllipsis = False
        Me.btnImportIcon.tbIconHoldPlace = True
        Me.btnImportIcon.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_import_4
        Me.btnImportIcon.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImportIcon.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImportIcon.tbIconMore = False
        Me.btnImportIcon.tbIconMouseDown = Nothing
        Me.btnImportIcon.tbIconMouseHover = Nothing
        Me.btnImportIcon.tbIconMouseLeave = Nothing
        Me.btnImportIcon.tbIconPlaceText = 2
        Me.btnImportIcon.tbIconReadOnly = Nothing
        Me.btnImportIcon.tbImageMouseDown = Nothing
        Me.btnImportIcon.tbImageMouseHover = Nothing
        Me.btnImportIcon.tbImageMouseLeave = Nothing
        Me.btnImportIcon.tbProgressValue = 50
        Me.btnImportIcon.tbReadOnly = False
        Me.btnImportIcon.tbReadOnlyText = False
        Me.btnImportIcon.tbShadow = False
        Me.btnImportIcon.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImportIcon.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImportIcon.tbShowDot = False
        Me.btnImportIcon.tbShowMoreIconImg = CType(resources.GetObject("btnImportIcon.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImportIcon.tbShowNew = False
        Me.btnImportIcon.tbShowProgress = False
        Me.btnImportIcon.tbShowTip = True
        Me.btnImportIcon.tbShowToolTipOnButton = False
        Me.btnImportIcon.tbSplit = "13,11,13,11"
        Me.btnImportIcon.tbText = "导入"
        Me.btnImportIcon.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportIcon.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImportIcon.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnImportIcon.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnImportIcon.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnImportIcon.tbTextMouseDownPlace = 2
        Me.btnImportIcon.tbToolTip = ""
        Me.btnImportIcon.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImportIcon.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImportIcon.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportIcon.VisibleEx = True
        '
        'btnExportIcon
        '
        Me.btnExportIcon.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnExportIcon.BackColor = System.Drawing.Color.Transparent
        Me.btnExportIcon.BindingForm = Nothing
        Me.btnExportIcon.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExportIcon.Location = New System.Drawing.Point(140, 5)
        Me.btnExportIcon.Name = "btnExportIcon"
        Me.btnExportIcon.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExportIcon.Selectable = True
        Me.btnExportIcon.Size = New System.Drawing.Size(57, 23)
        Me.btnExportIcon.TabIndex = 17
        Me.btnExportIcon.tbAdriftIconWhenHover = False
        Me.btnExportIcon.tbAutoSize = False
        Me.btnExportIcon.tbAutoSizeEx = True
        Me.btnExportIcon.tbBackgroundImage = Nothing
        Me.btnExportIcon.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExportIcon.tbBadgeNumber = 0
        Me.btnExportIcon.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExportIcon.tbEndEllipsis = False
        Me.btnExportIcon.tbIconHoldPlace = True
        Me.btnExportIcon.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_export_4
        Me.btnExportIcon.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExportIcon.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExportIcon.tbIconMore = False
        Me.btnExportIcon.tbIconMouseDown = Nothing
        Me.btnExportIcon.tbIconMouseHover = Nothing
        Me.btnExportIcon.tbIconMouseLeave = Nothing
        Me.btnExportIcon.tbIconPlaceText = 2
        Me.btnExportIcon.tbIconReadOnly = Nothing
        Me.btnExportIcon.tbImageMouseDown = Nothing
        Me.btnExportIcon.tbImageMouseHover = Nothing
        Me.btnExportIcon.tbImageMouseLeave = Nothing
        Me.btnExportIcon.tbProgressValue = 50
        Me.btnExportIcon.tbReadOnly = False
        Me.btnExportIcon.tbReadOnlyText = False
        Me.btnExportIcon.tbShadow = False
        Me.btnExportIcon.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExportIcon.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExportIcon.tbShowDot = False
        Me.btnExportIcon.tbShowMoreIconImg = CType(resources.GetObject("btnExportIcon.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExportIcon.tbShowNew = False
        Me.btnExportIcon.tbShowProgress = False
        Me.btnExportIcon.tbShowTip = True
        Me.btnExportIcon.tbShowToolTipOnButton = False
        Me.btnExportIcon.tbSplit = "13,11,13,11"
        Me.btnExportIcon.tbText = "导出"
        Me.btnExportIcon.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportIcon.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnExportIcon.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnExportIcon.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnExportIcon.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnExportIcon.tbTextMouseDownPlace = 2
        Me.btnExportIcon.tbToolTip = ""
        Me.btnExportIcon.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExportIcon.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExportIcon.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportIcon.VisibleEx = True
        '
        'btnSelectAll
        '
        Me.btnSelectAll.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnSelectAll.BackColor = System.Drawing.Color.Transparent
        Me.btnSelectAll.BindingForm = Nothing
        Me.btnSelectAll.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSelectAll.Location = New System.Drawing.Point(272, 5)
        Me.btnSelectAll.Name = "btnSelectAll"
        Me.btnSelectAll.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSelectAll.Selectable = True
        Me.btnSelectAll.Size = New System.Drawing.Size(57, 23)
        Me.btnSelectAll.TabIndex = 13
        Me.btnSelectAll.tbAdriftIconWhenHover = False
        Me.btnSelectAll.tbAutoSize = False
        Me.btnSelectAll.tbAutoSizeEx = True
        Me.btnSelectAll.tbBackgroundImage = Nothing
        Me.btnSelectAll.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSelectAll.tbBadgeNumber = 0
        Me.btnSelectAll.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSelectAll.tbEndEllipsis = False
        Me.btnSelectAll.tbIconHoldPlace = True
        Me.btnSelectAll.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_selectall_4
        Me.btnSelectAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSelectAll.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnSelectAll.tbIconMore = False
        Me.btnSelectAll.tbIconMouseDown = Nothing
        Me.btnSelectAll.tbIconMouseHover = Nothing
        Me.btnSelectAll.tbIconMouseLeave = Nothing
        Me.btnSelectAll.tbIconPlaceText = 2
        Me.btnSelectAll.tbIconReadOnly = Nothing
        Me.btnSelectAll.tbImageMouseDown = Nothing
        Me.btnSelectAll.tbImageMouseHover = Nothing
        Me.btnSelectAll.tbImageMouseLeave = Nothing
        Me.btnSelectAll.tbProgressValue = 50
        Me.btnSelectAll.tbReadOnly = False
        Me.btnSelectAll.tbReadOnlyText = False
        Me.btnSelectAll.tbShadow = False
        Me.btnSelectAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSelectAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSelectAll.tbShowDot = False
        Me.btnSelectAll.tbShowMoreIconImg = CType(resources.GetObject("btnSelectAll.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSelectAll.tbShowNew = False
        Me.btnSelectAll.tbShowProgress = False
        Me.btnSelectAll.tbShowTip = True
        Me.btnSelectAll.tbShowToolTipOnButton = False
        Me.btnSelectAll.tbSplit = "13,11,13,11"
        Me.btnSelectAll.tbText = "全选"
        Me.btnSelectAll.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectAll.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSelectAll.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnSelectAll.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnSelectAll.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSelectAll.tbTextMouseDownPlace = 2
        Me.btnSelectAll.tbToolTip = ""
        Me.btnSelectAll.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSelectAll.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSelectAll.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectAll.VisibleEx = True
        '
        'btnDeleteIcon
        '
        Me.btnDeleteIcon.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnDeleteIcon.BackColor = System.Drawing.Color.Transparent
        Me.btnDeleteIcon.BindingForm = Nothing
        Me.btnDeleteIcon.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDeleteIcon.Location = New System.Drawing.Point(206, 5)
        Me.btnDeleteIcon.Name = "btnDeleteIcon"
        Me.btnDeleteIcon.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDeleteIcon.Selectable = True
        Me.btnDeleteIcon.Size = New System.Drawing.Size(57, 23)
        Me.btnDeleteIcon.TabIndex = 12
        Me.btnDeleteIcon.tbAdriftIconWhenHover = False
        Me.btnDeleteIcon.tbAutoSize = False
        Me.btnDeleteIcon.tbAutoSizeEx = True
        Me.btnDeleteIcon.tbBackgroundImage = Nothing
        Me.btnDeleteIcon.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDeleteIcon.tbBadgeNumber = 0
        Me.btnDeleteIcon.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDeleteIcon.tbEndEllipsis = False
        Me.btnDeleteIcon.tbIconHoldPlace = True
        Me.btnDeleteIcon.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_delete_4
        Me.btnDeleteIcon.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDeleteIcon.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDeleteIcon.tbIconMore = False
        Me.btnDeleteIcon.tbIconMouseDown = Nothing
        Me.btnDeleteIcon.tbIconMouseHover = Nothing
        Me.btnDeleteIcon.tbIconMouseLeave = Nothing
        Me.btnDeleteIcon.tbIconPlaceText = 2
        Me.btnDeleteIcon.tbIconReadOnly = Nothing
        Me.btnDeleteIcon.tbImageMouseDown = Nothing
        Me.btnDeleteIcon.tbImageMouseHover = Nothing
        Me.btnDeleteIcon.tbImageMouseLeave = Nothing
        Me.btnDeleteIcon.tbProgressValue = 50
        Me.btnDeleteIcon.tbReadOnly = False
        Me.btnDeleteIcon.tbReadOnlyText = False
        Me.btnDeleteIcon.tbShadow = False
        Me.btnDeleteIcon.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDeleteIcon.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDeleteIcon.tbShowDot = False
        Me.btnDeleteIcon.tbShowMoreIconImg = CType(resources.GetObject("btnDeleteIcon.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDeleteIcon.tbShowNew = False
        Me.btnDeleteIcon.tbShowProgress = False
        Me.btnDeleteIcon.tbShowTip = True
        Me.btnDeleteIcon.tbShowToolTipOnButton = False
        Me.btnDeleteIcon.tbSplit = "13,11,13,11"
        Me.btnDeleteIcon.tbText = "删除"
        Me.btnDeleteIcon.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteIcon.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnDeleteIcon.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDeleteIcon.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnDeleteIcon.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnDeleteIcon.tbTextMouseDownPlace = 2
        Me.btnDeleteIcon.tbToolTip = ""
        Me.btnDeleteIcon.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDeleteIcon.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDeleteIcon.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteIcon.VisibleEx = True
        '
        'pnlGroups
        '
        Me.pnlGroups.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlGroups.BackColor = System.Drawing.Color.White
        Me.pnlGroups.Location = New System.Drawing.Point(438, 33)
        Me.pnlGroups.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlGroups.Name = "pnlGroups"
        Me.pnlGroups.Size = New System.Drawing.Size(600, 478)
        Me.pnlGroups.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlGroups.TabIndex = 11
        Me.pnlGroups.tbBackgroundImage = Nothing
        Me.pnlGroups.tbShowWatermark = False
        Me.pnlGroups.tbSplit = "0,0,0,0"
        Me.pnlGroups.tbWatermark = Nothing
        Me.pnlGroups.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlGroups.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlControlGroup
        '
        Me.pnlControlGroup.BackColor = System.Drawing.Color.FromArgb(CType(CType(237, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlControlGroup.Controls.Add(Me.lblHelper)
        Me.pnlControlGroup.Controls.Add(Me.btnSelectAllEmoticon)
        Me.pnlControlGroup.Controls.Add(Me.btnRestore)
        Me.pnlControlGroup.Controls.Add(Me.btnImportGroup)
        Me.pnlControlGroup.Controls.Add(Me.btnExportGroup)
        Me.pnlControlGroup.Controls.Add(Me.btnDeleteGroup)
        Me.pnlControlGroup.Controls.Add(Me.btnAddGroup)
        Me.pnlControlGroup.Controls.Add(Me.btnRefreshGroup)
        Me.pnlControlGroup.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlControlGroup.Location = New System.Drawing.Point(438, 0)
        Me.pnlControlGroup.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlControlGroup.Name = "pnlControlGroup"
        Me.pnlControlGroup.Size = New System.Drawing.Size(600, 33)
        Me.pnlControlGroup.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlControlGroup.TabIndex = 10
        Me.pnlControlGroup.tbBackgroundImage = Global.iTong.iWeixin.My.Resources.Resources.pnl_bg_navigation
        Me.pnlControlGroup.tbShowWatermark = False
        Me.pnlControlGroup.tbSplit = "3,3,3,3"
        Me.pnlControlGroup.tbWatermark = Nothing
        Me.pnlControlGroup.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlControlGroup.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblHelper
        '
        Me.lblHelper.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.lblHelper.BackColor = System.Drawing.Color.Transparent
        Me.lblHelper.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblHelper.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblHelper.ForeColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(116, Byte), Integer), CType(CType(203, Byte), Integer))
        Me.lblHelper.Location = New System.Drawing.Point(416, 5)
        Me.lblHelper.Name = "lblHelper"
        Me.lblHelper.Size = New System.Drawing.Size(119, 23)
        Me.lblHelper.TabIndex = 40
        Me.lblHelper.tbAdriftWhenHover = False
        Me.lblHelper.tbAutoEllipsis = False
        Me.lblHelper.tbAutoSize = False
        Me.lblHelper.tbHideImage = False
        Me.lblHelper.tbIconImage = Nothing
        Me.lblHelper.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblHelper.tbIconPlaceText = 5
        Me.lblHelper.tbShadow = False
        Me.lblHelper.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblHelper.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblHelper.tbShowScrolling = False
        Me.lblHelper.Text = "重新下载"
        Me.lblHelper.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'btnSelectAllEmoticon
        '
        Me.btnSelectAllEmoticon.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnSelectAllEmoticon.BackColor = System.Drawing.Color.Transparent
        Me.btnSelectAllEmoticon.BindingForm = Nothing
        Me.btnSelectAllEmoticon.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSelectAllEmoticon.Location = New System.Drawing.Point(343, 5)
        Me.btnSelectAllEmoticon.Name = "btnSelectAllEmoticon"
        Me.btnSelectAllEmoticon.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSelectAllEmoticon.Selectable = True
        Me.btnSelectAllEmoticon.Size = New System.Drawing.Size(57, 23)
        Me.btnSelectAllEmoticon.TabIndex = 18
        Me.btnSelectAllEmoticon.tbAdriftIconWhenHover = False
        Me.btnSelectAllEmoticon.tbAutoSize = False
        Me.btnSelectAllEmoticon.tbAutoSizeEx = True
        Me.btnSelectAllEmoticon.tbBackgroundImage = Nothing
        Me.btnSelectAllEmoticon.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSelectAllEmoticon.tbBadgeNumber = 0
        Me.btnSelectAllEmoticon.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSelectAllEmoticon.tbEndEllipsis = False
        Me.btnSelectAllEmoticon.tbIconHoldPlace = True
        Me.btnSelectAllEmoticon.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_selectall_4
        Me.btnSelectAllEmoticon.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSelectAllEmoticon.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnSelectAllEmoticon.tbIconMore = False
        Me.btnSelectAllEmoticon.tbIconMouseDown = Nothing
        Me.btnSelectAllEmoticon.tbIconMouseHover = Nothing
        Me.btnSelectAllEmoticon.tbIconMouseLeave = Nothing
        Me.btnSelectAllEmoticon.tbIconPlaceText = 2
        Me.btnSelectAllEmoticon.tbIconReadOnly = Nothing
        Me.btnSelectAllEmoticon.tbImageMouseDown = Nothing
        Me.btnSelectAllEmoticon.tbImageMouseHover = Nothing
        Me.btnSelectAllEmoticon.tbImageMouseLeave = Nothing
        Me.btnSelectAllEmoticon.tbProgressValue = 50
        Me.btnSelectAllEmoticon.tbReadOnly = False
        Me.btnSelectAllEmoticon.tbReadOnlyText = False
        Me.btnSelectAllEmoticon.tbShadow = False
        Me.btnSelectAllEmoticon.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSelectAllEmoticon.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSelectAllEmoticon.tbShowDot = False
        Me.btnSelectAllEmoticon.tbShowMoreIconImg = CType(resources.GetObject("btnSelectAllEmoticon.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSelectAllEmoticon.tbShowNew = False
        Me.btnSelectAllEmoticon.tbShowProgress = False
        Me.btnSelectAllEmoticon.tbShowTip = True
        Me.btnSelectAllEmoticon.tbShowToolTipOnButton = False
        Me.btnSelectAllEmoticon.tbSplit = "13,11,13,11"
        Me.btnSelectAllEmoticon.tbText = "全选"
        Me.btnSelectAllEmoticon.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectAllEmoticon.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnSelectAllEmoticon.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnSelectAllEmoticon.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnSelectAllEmoticon.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSelectAllEmoticon.tbTextMouseDownPlace = 2
        Me.btnSelectAllEmoticon.tbToolTip = ""
        Me.btnSelectAllEmoticon.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSelectAllEmoticon.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSelectAllEmoticon.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectAllEmoticon.VisibleEx = True
        '
        'btnRestore
        '
        Me.btnRestore.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRestore.BackColor = System.Drawing.Color.Transparent
        Me.btnRestore.BindingForm = Nothing
        Me.btnRestore.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRestore.Location = New System.Drawing.Point(223, 5)
        Me.btnRestore.Name = "btnRestore"
        Me.btnRestore.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRestore.Selectable = True
        Me.btnRestore.Size = New System.Drawing.Size(57, 23)
        Me.btnRestore.TabIndex = 17
        Me.btnRestore.tbAdriftIconWhenHover = False
        Me.btnRestore.tbAutoSize = False
        Me.btnRestore.tbAutoSizeEx = True
        Me.btnRestore.tbBackgroundImage = Nothing
        Me.btnRestore.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRestore.tbBadgeNumber = 0
        Me.btnRestore.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRestore.tbEndEllipsis = False
        Me.btnRestore.tbIconHoldPlace = True
        Me.btnRestore.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_4_restore
        Me.btnRestore.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRestore.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRestore.tbIconMore = False
        Me.btnRestore.tbIconMouseDown = Nothing
        Me.btnRestore.tbIconMouseHover = Nothing
        Me.btnRestore.tbIconMouseLeave = Nothing
        Me.btnRestore.tbIconPlaceText = 2
        Me.btnRestore.tbIconReadOnly = Nothing
        Me.btnRestore.tbImageMouseDown = Nothing
        Me.btnRestore.tbImageMouseHover = Nothing
        Me.btnRestore.tbImageMouseLeave = Nothing
        Me.btnRestore.tbProgressValue = 50
        Me.btnRestore.tbReadOnly = False
        Me.btnRestore.tbReadOnlyText = False
        Me.btnRestore.tbShadow = False
        Me.btnRestore.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRestore.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRestore.tbShowDot = False
        Me.btnRestore.tbShowMoreIconImg = CType(resources.GetObject("btnRestore.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRestore.tbShowNew = False
        Me.btnRestore.tbShowProgress = False
        Me.btnRestore.tbShowTip = True
        Me.btnRestore.tbShowToolTipOnButton = False
        Me.btnRestore.tbSplit = "13,11,13,11"
        Me.btnRestore.tbText = "还原"
        Me.btnRestore.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestore.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnRestore.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRestore.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnRestore.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnRestore.tbTextMouseDownPlace = 2
        Me.btnRestore.tbToolTip = ""
        Me.btnRestore.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRestore.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRestore.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestore.VisibleEx = True
        '
        'btnImportGroup
        '
        Me.btnImportGroup.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnImportGroup.BackColor = System.Drawing.Color.Transparent
        Me.btnImportGroup.BindingForm = Nothing
        Me.btnImportGroup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImportGroup.Location = New System.Drawing.Point(99, 5)
        Me.btnImportGroup.Name = "btnImportGroup"
        Me.btnImportGroup.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImportGroup.Selectable = True
        Me.btnImportGroup.Size = New System.Drawing.Size(57, 23)
        Me.btnImportGroup.TabIndex = 16
        Me.btnImportGroup.tbAdriftIconWhenHover = False
        Me.btnImportGroup.tbAutoSize = False
        Me.btnImportGroup.tbAutoSizeEx = True
        Me.btnImportGroup.tbBackgroundImage = Nothing
        Me.btnImportGroup.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnImportGroup.tbBadgeNumber = 0
        Me.btnImportGroup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImportGroup.tbEndEllipsis = False
        Me.btnImportGroup.tbIconHoldPlace = True
        Me.btnImportGroup.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_import_4
        Me.btnImportGroup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImportGroup.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImportGroup.tbIconMore = False
        Me.btnImportGroup.tbIconMouseDown = Nothing
        Me.btnImportGroup.tbIconMouseHover = Nothing
        Me.btnImportGroup.tbIconMouseLeave = Nothing
        Me.btnImportGroup.tbIconPlaceText = 2
        Me.btnImportGroup.tbIconReadOnly = Nothing
        Me.btnImportGroup.tbImageMouseDown = Nothing
        Me.btnImportGroup.tbImageMouseHover = Nothing
        Me.btnImportGroup.tbImageMouseLeave = Nothing
        Me.btnImportGroup.tbProgressValue = 50
        Me.btnImportGroup.tbReadOnly = False
        Me.btnImportGroup.tbReadOnlyText = False
        Me.btnImportGroup.tbShadow = False
        Me.btnImportGroup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImportGroup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImportGroup.tbShowDot = False
        Me.btnImportGroup.tbShowMoreIconImg = CType(resources.GetObject("btnImportGroup.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImportGroup.tbShowNew = False
        Me.btnImportGroup.tbShowProgress = False
        Me.btnImportGroup.tbShowTip = True
        Me.btnImportGroup.tbShowToolTipOnButton = False
        Me.btnImportGroup.tbSplit = "13,11,13,11"
        Me.btnImportGroup.tbText = "导入"
        Me.btnImportGroup.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportGroup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImportGroup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnImportGroup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnImportGroup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnImportGroup.tbTextMouseDownPlace = 2
        Me.btnImportGroup.tbToolTip = ""
        Me.btnImportGroup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImportGroup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImportGroup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportGroup.VisibleEx = True
        '
        'btnExportGroup
        '
        Me.btnExportGroup.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnExportGroup.BackColor = System.Drawing.Color.Transparent
        Me.btnExportGroup.BindingForm = Nothing
        Me.btnExportGroup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExportGroup.Location = New System.Drawing.Point(164, 5)
        Me.btnExportGroup.Name = "btnExportGroup"
        Me.btnExportGroup.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExportGroup.Selectable = True
        Me.btnExportGroup.Size = New System.Drawing.Size(57, 23)
        Me.btnExportGroup.TabIndex = 15
        Me.btnExportGroup.tbAdriftIconWhenHover = False
        Me.btnExportGroup.tbAutoSize = False
        Me.btnExportGroup.tbAutoSizeEx = True
        Me.btnExportGroup.tbBackgroundImage = Nothing
        Me.btnExportGroup.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExportGroup.tbBadgeNumber = 0
        Me.btnExportGroup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExportGroup.tbEndEllipsis = False
        Me.btnExportGroup.tbIconHoldPlace = True
        Me.btnExportGroup.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_export_4
        Me.btnExportGroup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExportGroup.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExportGroup.tbIconMore = False
        Me.btnExportGroup.tbIconMouseDown = Nothing
        Me.btnExportGroup.tbIconMouseHover = Nothing
        Me.btnExportGroup.tbIconMouseLeave = Nothing
        Me.btnExportGroup.tbIconPlaceText = 2
        Me.btnExportGroup.tbIconReadOnly = Nothing
        Me.btnExportGroup.tbImageMouseDown = Nothing
        Me.btnExportGroup.tbImageMouseHover = Nothing
        Me.btnExportGroup.tbImageMouseLeave = Nothing
        Me.btnExportGroup.tbProgressValue = 50
        Me.btnExportGroup.tbReadOnly = False
        Me.btnExportGroup.tbReadOnlyText = False
        Me.btnExportGroup.tbShadow = False
        Me.btnExportGroup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExportGroup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExportGroup.tbShowDot = False
        Me.btnExportGroup.tbShowMoreIconImg = CType(resources.GetObject("btnExportGroup.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExportGroup.tbShowNew = False
        Me.btnExportGroup.tbShowProgress = False
        Me.btnExportGroup.tbShowTip = True
        Me.btnExportGroup.tbShowToolTipOnButton = False
        Me.btnExportGroup.tbSplit = "13,11,13,11"
        Me.btnExportGroup.tbText = "导出"
        Me.btnExportGroup.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportGroup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnExportGroup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnExportGroup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnExportGroup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnExportGroup.tbTextMouseDownPlace = 2
        Me.btnExportGroup.tbToolTip = ""
        Me.btnExportGroup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExportGroup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExportGroup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportGroup.VisibleEx = True
        '
        'btnDeleteGroup
        '
        Me.btnDeleteGroup.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnDeleteGroup.BackColor = System.Drawing.Color.Transparent
        Me.btnDeleteGroup.BindingForm = Nothing
        Me.btnDeleteGroup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDeleteGroup.Location = New System.Drawing.Point(280, 5)
        Me.btnDeleteGroup.Name = "btnDeleteGroup"
        Me.btnDeleteGroup.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDeleteGroup.Selectable = True
        Me.btnDeleteGroup.Size = New System.Drawing.Size(57, 23)
        Me.btnDeleteGroup.TabIndex = 14
        Me.btnDeleteGroup.tbAdriftIconWhenHover = False
        Me.btnDeleteGroup.tbAutoSize = False
        Me.btnDeleteGroup.tbAutoSizeEx = True
        Me.btnDeleteGroup.tbBackgroundImage = Nothing
        Me.btnDeleteGroup.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDeleteGroup.tbBadgeNumber = 0
        Me.btnDeleteGroup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDeleteGroup.tbEndEllipsis = False
        Me.btnDeleteGroup.tbIconHoldPlace = True
        Me.btnDeleteGroup.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_delete_4
        Me.btnDeleteGroup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDeleteGroup.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDeleteGroup.tbIconMore = False
        Me.btnDeleteGroup.tbIconMouseDown = Nothing
        Me.btnDeleteGroup.tbIconMouseHover = Nothing
        Me.btnDeleteGroup.tbIconMouseLeave = Nothing
        Me.btnDeleteGroup.tbIconPlaceText = 2
        Me.btnDeleteGroup.tbIconReadOnly = Nothing
        Me.btnDeleteGroup.tbImageMouseDown = Nothing
        Me.btnDeleteGroup.tbImageMouseHover = Nothing
        Me.btnDeleteGroup.tbImageMouseLeave = Nothing
        Me.btnDeleteGroup.tbProgressValue = 50
        Me.btnDeleteGroup.tbReadOnly = False
        Me.btnDeleteGroup.tbReadOnlyText = False
        Me.btnDeleteGroup.tbShadow = False
        Me.btnDeleteGroup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDeleteGroup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDeleteGroup.tbShowDot = False
        Me.btnDeleteGroup.tbShowMoreIconImg = CType(resources.GetObject("btnDeleteGroup.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDeleteGroup.tbShowNew = False
        Me.btnDeleteGroup.tbShowProgress = False
        Me.btnDeleteGroup.tbShowTip = True
        Me.btnDeleteGroup.tbShowToolTipOnButton = False
        Me.btnDeleteGroup.tbSplit = "13,11,13,11"
        Me.btnDeleteGroup.tbText = "删除"
        Me.btnDeleteGroup.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteGroup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnDeleteGroup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnDeleteGroup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnDeleteGroup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnDeleteGroup.tbTextMouseDownPlace = 2
        Me.btnDeleteGroup.tbToolTip = ""
        Me.btnDeleteGroup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDeleteGroup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDeleteGroup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteGroup.VisibleEx = True
        '
        'btnAddGroup
        '
        Me.btnAddGroup.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnAddGroup.BackColor = System.Drawing.Color.Transparent
        Me.btnAddGroup.BindingForm = Nothing
        Me.btnAddGroup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAddGroup.Location = New System.Drawing.Point(8, 5)
        Me.btnAddGroup.Name = "btnAddGroup"
        Me.btnAddGroup.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnAddGroup.Selectable = True
        Me.btnAddGroup.Size = New System.Drawing.Size(81, 23)
        Me.btnAddGroup.TabIndex = 12
        Me.btnAddGroup.tbAdriftIconWhenHover = False
        Me.btnAddGroup.tbAutoSize = False
        Me.btnAddGroup.tbAutoSizeEx = True
        Me.btnAddGroup.tbBackgroundImage = Nothing
        Me.btnAddGroup.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnAddGroup.tbBadgeNumber = 0
        Me.btnAddGroup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAddGroup.tbEndEllipsis = False
        Me.btnAddGroup.tbIconHoldPlace = True
        Me.btnAddGroup.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_add_4
        Me.btnAddGroup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnAddGroup.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnAddGroup.tbIconMore = False
        Me.btnAddGroup.tbIconMouseDown = Nothing
        Me.btnAddGroup.tbIconMouseHover = Nothing
        Me.btnAddGroup.tbIconMouseLeave = Nothing
        Me.btnAddGroup.tbIconPlaceText = 2
        Me.btnAddGroup.tbIconReadOnly = Nothing
        Me.btnAddGroup.tbImageMouseDown = Nothing
        Me.btnAddGroup.tbImageMouseHover = Nothing
        Me.btnAddGroup.tbImageMouseLeave = Nothing
        Me.btnAddGroup.tbProgressValue = 50
        Me.btnAddGroup.tbReadOnly = False
        Me.btnAddGroup.tbReadOnlyText = False
        Me.btnAddGroup.tbShadow = False
        Me.btnAddGroup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAddGroup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAddGroup.tbShowDot = False
        Me.btnAddGroup.tbShowMoreIconImg = CType(resources.GetObject("btnAddGroup.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAddGroup.tbShowNew = False
        Me.btnAddGroup.tbShowProgress = False
        Me.btnAddGroup.tbShowTip = True
        Me.btnAddGroup.tbShowToolTipOnButton = False
        Me.btnAddGroup.tbSplit = "13,11,13,11"
        Me.btnAddGroup.tbText = "新建分组"
        Me.btnAddGroup.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddGroup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnAddGroup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnAddGroup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnAddGroup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnAddGroup.tbTextMouseDownPlace = 2
        Me.btnAddGroup.tbToolTip = ""
        Me.btnAddGroup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAddGroup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAddGroup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddGroup.VisibleEx = True
        '
        'btnRefreshGroup
        '
        Me.btnRefreshGroup.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnRefreshGroup.BackColor = System.Drawing.Color.Transparent
        Me.btnRefreshGroup.BindingForm = Nothing
        Me.btnRefreshGroup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefreshGroup.Location = New System.Drawing.Point(538, 8)
        Me.btnRefreshGroup.Name = "btnRefreshGroup"
        Me.btnRefreshGroup.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefreshGroup.Selectable = True
        Me.btnRefreshGroup.Size = New System.Drawing.Size(57, 16)
        Me.btnRefreshGroup.TabIndex = 2
        Me.btnRefreshGroup.tbAdriftIconWhenHover = False
        Me.btnRefreshGroup.tbAutoSize = False
        Me.btnRefreshGroup.tbAutoSizeEx = True
        Me.btnRefreshGroup.tbBackgroundImage = Nothing
        Me.btnRefreshGroup.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefreshGroup.tbBadgeNumber = 0
        Me.btnRefreshGroup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefreshGroup.tbEndEllipsis = False
        Me.btnRefreshGroup.tbIconHoldPlace = True
        Me.btnRefreshGroup.tbIconImage = Global.iTong.iWeixin.My.Resources.Resources.btn_4_refresh
        Me.btnRefreshGroup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefreshGroup.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefreshGroup.tbIconMore = False
        Me.btnRefreshGroup.tbIconMouseDown = Nothing
        Me.btnRefreshGroup.tbIconMouseHover = Nothing
        Me.btnRefreshGroup.tbIconMouseLeave = Nothing
        Me.btnRefreshGroup.tbIconPlaceText = 2
        Me.btnRefreshGroup.tbIconReadOnly = Nothing
        Me.btnRefreshGroup.tbImageMouseDown = Nothing
        Me.btnRefreshGroup.tbImageMouseHover = Nothing
        Me.btnRefreshGroup.tbImageMouseLeave = Nothing
        Me.btnRefreshGroup.tbProgressValue = 50
        Me.btnRefreshGroup.tbReadOnly = False
        Me.btnRefreshGroup.tbReadOnlyText = False
        Me.btnRefreshGroup.tbShadow = False
        Me.btnRefreshGroup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefreshGroup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefreshGroup.tbShowDot = False
        Me.btnRefreshGroup.tbShowMoreIconImg = CType(resources.GetObject("btnRefreshGroup.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefreshGroup.tbShowNew = False
        Me.btnRefreshGroup.tbShowProgress = False
        Me.btnRefreshGroup.tbShowTip = True
        Me.btnRefreshGroup.tbShowToolTipOnButton = False
        Me.btnRefreshGroup.tbSplit = "0,0,0,0"
        Me.btnRefreshGroup.tbText = "刷新"
        Me.btnRefreshGroup.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefreshGroup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnRefreshGroup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRefreshGroup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnRefreshGroup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnRefreshGroup.tbTextMouseDownPlace = 0
        Me.btnRefreshGroup.tbToolTip = ""
        Me.btnRefreshGroup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefreshGroup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefreshGroup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefreshGroup.VisibleEx = True
        '
        'pnlBottom
        '
        Me.pnlBottom.BackColor = System.Drawing.Color.FromArgb(CType(CType(206, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(215, Byte), Integer))
        Me.pnlMain.SetColumnSpan(Me.pnlBottom, 6)
        Me.pnlBottom.Controls.Add(Me.pbarShowProgress)
        Me.pnlBottom.Controls.Add(Me.btnAbrot)
        Me.pnlBottom.Controls.Add(Me.lblMessage)
        Me.pnlBottom.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlBottom.Location = New System.Drawing.Point(0, 511)
        Me.pnlBottom.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(1651, 30)
        Me.pnlBottom.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlBottom.TabIndex = 15
        Me.pnlBottom.tbBackgroundImage = Nothing
        Me.pnlBottom.tbShowWatermark = False
        Me.pnlBottom.tbSplit = "1,20,2,20"
        Me.pnlBottom.tbWatermark = Nothing
        Me.pnlBottom.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBottom.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pbarShowProgress
        '
        Me.pbarShowProgress.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pbarShowProgress.BackColor = System.Drawing.Color.Transparent
        Me.pbarShowProgress.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.pbarShowProgress.Location = New System.Drawing.Point(1506, 10)
        Me.pbarShowProgress.Name = "pbarShowProgress"
        Me.pbarShowProgress.Size = New System.Drawing.Size(110, 10)
        Me.pbarShowProgress.TabIndex = 26
        Me.pbarShowProgress.tbBackgroundImage = CType(resources.GetObject("pbarShowProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.pbarShowProgress.tbCanDragValue = False
        Me.pbarShowProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pbarShowProgress.tbDotImage = CType(resources.GetObject("pbarShowProgress.tbDotImage"), System.Drawing.Image)
        Me.pbarShowProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pbarShowProgress.tbDotMouseDown = Nothing
        Me.pbarShowProgress.tbDotMouseHover = Nothing
        Me.pbarShowProgress.tbDotMouseLeave = Nothing
        Me.pbarShowProgress.tbDownloadImage = Global.iTong.iWeixin.My.Resources.Resources.dgv_progress_value
        Me.pbarShowProgress.tbDownMax = 100
        Me.pbarShowProgress.tbDownMin = 0
        Me.pbarShowProgress.tbDownValue = 0
        Me.pbarShowProgress.tbIsWaiting = False
        Me.pbarShowProgress.tbPlayImage = Global.iTong.iWeixin.My.Resources.Resources.dgv_progress_value
        Me.pbarShowProgress.tbPlayMax = 100
        Me.pbarShowProgress.tbPlayMin = 0
        Me.pbarShowProgress.tbPlayValue = 50
        Me.pbarShowProgress.tbShowDot = False
        Me.pbarShowProgress.tbShowText = True
        Me.pbarShowProgress.tbSplit = "4,0,4,0"
        Me.pbarShowProgress.Text = "TbControlBar1"
        '
        'btnAbrot
        '
        Me.btnAbrot.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnAbrot.BackColor = System.Drawing.Color.Transparent
        Me.btnAbrot.BindingForm = Nothing
        Me.btnAbrot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAbrot.Location = New System.Drawing.Point(1628, 7)
        Me.btnAbrot.Name = "btnAbrot"
        Me.btnAbrot.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnAbrot.Selectable = True
        Me.btnAbrot.Size = New System.Drawing.Size(15, 15)
        Me.btnAbrot.TabIndex = 13
        Me.btnAbrot.tbAdriftIconWhenHover = False
        Me.btnAbrot.tbAutoSize = False
        Me.btnAbrot.tbAutoSizeEx = True
        Me.btnAbrot.tbBackgroundImage = Global.iTong.iWeixin.My.Resources.Resources.btn_3_cancel
        Me.btnAbrot.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnAbrot.tbBadgeNumber = 0
        Me.btnAbrot.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAbrot.tbEndEllipsis = False
        Me.btnAbrot.tbIconHoldPlace = True
        Me.btnAbrot.tbIconImage = Nothing
        Me.btnAbrot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAbrot.tbIconMore = False
        Me.btnAbrot.tbIconMouseDown = Nothing
        Me.btnAbrot.tbIconMouseHover = Nothing
        Me.btnAbrot.tbIconMouseLeave = Nothing
        Me.btnAbrot.tbIconPlaceText = 2
        Me.btnAbrot.tbIconReadOnly = Nothing
        Me.btnAbrot.tbImageMouseDown = Nothing
        Me.btnAbrot.tbImageMouseHover = Nothing
        Me.btnAbrot.tbImageMouseLeave = Nothing
        Me.btnAbrot.tbProgressValue = 50
        Me.btnAbrot.tbReadOnly = False
        Me.btnAbrot.tbReadOnlyText = False
        Me.btnAbrot.tbShadow = False
        Me.btnAbrot.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAbrot.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAbrot.tbShowDot = False
        Me.btnAbrot.tbShowMoreIconImg = CType(resources.GetObject("btnAbrot.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAbrot.tbShowNew = False
        Me.btnAbrot.tbShowProgress = False
        Me.btnAbrot.tbShowTip = True
        Me.btnAbrot.tbShowToolTipOnButton = False
        Me.btnAbrot.tbSplit = "0,0,0,0"
        Me.btnAbrot.tbText = ""
        Me.btnAbrot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.tbTextColor = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorDisable = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorDown = System.Drawing.Color.White
        Me.btnAbrot.tbTextColorHover = System.Drawing.Color.White
        Me.btnAbrot.tbTextMouseDownPlace = 0
        Me.btnAbrot.tbToolTip = ""
        Me.btnAbrot.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAbrot.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAbrot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAbrot.VisibleEx = True
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(14, 5)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(516, 20)
        Me.lblMessage.TabIndex = 0
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = False
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = False
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.Text = "导入表情后，记得要从后台退出微信再打开哦！"
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlGuide
        '
        Me.pnlGuide.BackgroundImage = Global.iTong.iWeixin.My.Resources.Resources.weixin_guide_bg
        Me.pnlGuide.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Center
        Me.pnlGuide.Controls.Add(Me.lblImportDescription)
        Me.pnlGuide.Controls.Add(Me.picError)
        Me.pnlGuide.Controls.Add(Me.lblSetp2)
        Me.pnlGuide.Controls.Add(Me.lblSetp1)
        Me.pnlGuide.Controls.Add(Me.btnOK)
        Me.pnlGuide.Controls.Add(Me.lblTitle)
        Me.pnlGuide.Controls.Add(Me.chkCheckCloseWeixin)
        Me.pnlGuide.Controls.Add(Me.picclose)
        Me.pnlGuide.Controls.Add(Me.picTitle)
        Me.pnlGuide.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlGuide.Location = New System.Drawing.Point(1651, 0)
        Me.pnlGuide.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlGuide.Name = "pnlGuide"
        Me.pnlMain.SetRowSpan(Me.pnlGuide, 3)
        Me.pnlGuide.Size = New System.Drawing.Size(870, 541)
        Me.pnlGuide.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlGuide.TabIndex = 16
        Me.pnlGuide.tbBackgroundImage = Nothing
        Me.pnlGuide.tbShowWatermark = False
        Me.pnlGuide.tbSplit = "0,0,0,0"
        Me.pnlGuide.tbWatermark = Nothing
        Me.pnlGuide.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlGuide.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblImportDescription
        '
        Me.lblImportDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblImportDescription.BackColor = System.Drawing.Color.Transparent
        Me.lblImportDescription.ForeColor = System.Drawing.Color.Red
        Me.lblImportDescription.Location = New System.Drawing.Point(452, 281)
        Me.lblImportDescription.Name = "lblImportDescription"
        Me.lblImportDescription.Size = New System.Drawing.Size(511, 66)
        Me.lblImportDescription.TabIndex = 79
        Me.lblImportDescription.Text = "6.2.2以上版本的微信，无法新建分组，仅能将表情导入已有的分组中。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "助手将默认把表情导入""我的收藏""，当分组中的表情超过140个时，" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "自动导入下一个分组。"
        Me.lblImportDescription.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picError
        '
        Me.picError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picError.BackColor = System.Drawing.Color.Transparent
        Me.picError.Image = Global.iTong.iWeixin.My.Resources.Resources.dgv_filenoexist
        Me.picError.Location = New System.Drawing.Point(441, 359)
        Me.picError.Name = "picError"
        Me.picError.Size = New System.Drawing.Size(12, 12)
        Me.picError.TabIndex = 78
        Me.picError.TabStop = False
        Me.picError.tbAutoSize = False
        Me.picError.tbBackgroundImage = Nothing
        Me.picError.tbSplit = "0,0,0,0"
        Me.picError.Visible = False
        '
        'lblSetp2
        '
        Me.lblSetp2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetp2.BackColor = System.Drawing.Color.Transparent
        Me.lblSetp2.ForeColor = System.Drawing.Color.White
        Me.lblSetp2.Location = New System.Drawing.Point(452, 213)
        Me.lblSetp2.Name = "lblSetp2"
        Me.lblSetp2.Size = New System.Drawing.Size(213, 41)
        Me.lblSetp2.TabIndex = 77
        Me.lblSetp2.Text = "第二步：关闭微信"
        Me.lblSetp2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblSetp1
        '
        Me.lblSetp1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetp1.BackColor = System.Drawing.Color.Transparent
        Me.lblSetp1.ForeColor = System.Drawing.Color.White
        Me.lblSetp1.Location = New System.Drawing.Point(452, 150)
        Me.lblSetp1.Name = "lblSetp1"
        Me.lblSetp1.Size = New System.Drawing.Size(213, 41)
        Me.lblSetp1.TabIndex = 76
        Me.lblSetp1.Text = "第一步：双击home键"
        Me.lblSetp1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnOK
        '
        Me.btnOK.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(457, 391)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(118, 34)
        Me.btnOK.TabIndex = 73
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.iWeixin.My.Resources.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "确定"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'lblTitle
        '
        Me.lblTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblTitle.ForeColor = System.Drawing.Color.White
        Me.lblTitle.Location = New System.Drawing.Point(226, 42)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(695, 37)
        Me.lblTitle.TabIndex = 75
        Me.lblTitle.Text = "请务必从后台退出微信 ，否则导入时可能丢失记录"
        Me.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'chkCheckCloseWeixin
        '
        Me.chkCheckCloseWeixin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.chkCheckCloseWeixin.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkCheckCloseWeixin.BackColor = System.Drawing.Color.Transparent
        Me.chkCheckCloseWeixin.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkCheckCloseWeixin.ForeColor = System.Drawing.Color.FromArgb(CType(CType(163, Byte), Integer), CType(CType(163, Byte), Integer), CType(CType(163, Byte), Integer))
        Me.chkCheckCloseWeixin.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(163, Byte), Integer), CType(CType(163, Byte), Integer), CType(CType(163, Byte), Integer))
        Me.chkCheckCloseWeixin.Location = New System.Drawing.Point(452, 349)
        Me.chkCheckCloseWeixin.Name = "chkCheckCloseWeixin"
        Me.chkCheckCloseWeixin.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkCheckCloseWeixin.Size = New System.Drawing.Size(128, 29)
        Me.chkCheckCloseWeixin.TabIndex = 74
        Me.chkCheckCloseWeixin.tbAdriftIconWhenHover = False
        Me.chkCheckCloseWeixin.tbAutoSize = False
        Me.chkCheckCloseWeixin.tbAutoSizeEx = True
        Me.chkCheckCloseWeixin.tbIconChecked = CType(resources.GetObject("chkCheckCloseWeixin.tbIconChecked"), System.Drawing.Image)
        Me.chkCheckCloseWeixin.tbIconCheckedMouseDown = Nothing
        Me.chkCheckCloseWeixin.tbIconCheckedMouseHover = Nothing
        Me.chkCheckCloseWeixin.tbIconCheckedMouseLeave = Nothing
        Me.chkCheckCloseWeixin.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkCheckCloseWeixin.tbIconHoldPlace = True
        Me.chkCheckCloseWeixin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkCheckCloseWeixin.tbIconIndeterminate = CType(resources.GetObject("chkCheckCloseWeixin.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkCheckCloseWeixin.tbIconIndeterminateMouseDown = Nothing
        Me.chkCheckCloseWeixin.tbIconIndeterminateMouseHover = Nothing
        Me.chkCheckCloseWeixin.tbIconIndeterminateMouseLeave = Nothing
        Me.chkCheckCloseWeixin.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkCheckCloseWeixin.tbIconPlaceText = 1
        Me.chkCheckCloseWeixin.tbIconUnChecked = CType(resources.GetObject("chkCheckCloseWeixin.tbIconUnChecked"), System.Drawing.Image)
        Me.chkCheckCloseWeixin.tbIconUnCheckedMouseDown = Nothing
        Me.chkCheckCloseWeixin.tbIconUnCheckedMouseHover = Nothing
        Me.chkCheckCloseWeixin.tbIconUnCheckedMouseLeave = Nothing
        Me.chkCheckCloseWeixin.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkCheckCloseWeixin.tbImageBackground = Nothing
        Me.chkCheckCloseWeixin.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkCheckCloseWeixin.tbImageCheckedMouseDown = Nothing
        Me.chkCheckCloseWeixin.tbImageCheckedMouseHover = Nothing
        Me.chkCheckCloseWeixin.tbImageCheckedMouseLeave = Nothing
        Me.chkCheckCloseWeixin.tbImageUnCheckedMouseDown = Nothing
        Me.chkCheckCloseWeixin.tbImageUnCheckedMouseHover = Nothing
        Me.chkCheckCloseWeixin.tbImageUnCheckedMouseLeave = Nothing
        Me.chkCheckCloseWeixin.tbReadOnly = False
        Me.chkCheckCloseWeixin.tbShadow = False
        Me.chkCheckCloseWeixin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkCheckCloseWeixin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkCheckCloseWeixin.tbSplit = "3,3,3,3"
        Me.chkCheckCloseWeixin.tbToolTip = ""
        Me.chkCheckCloseWeixin.Text = "我已退出微信后台"
        Me.chkCheckCloseWeixin.UseVisualStyleBackColor = False
        '
        'picclose
        '
        Me.picclose.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picclose.Image = Global.iTong.iWeixin.My.Resources.Resources.weixin_close3
        Me.picclose.Location = New System.Drawing.Point(233, 115)
        Me.picclose.Name = "picclose"
        Me.picclose.Size = New System.Drawing.Size(156, 328)
        Me.picclose.TabIndex = 1
        Me.picclose.TabStop = False
        Me.picclose.tbAutoSize = False
        Me.picclose.tbBackgroundImage = Nothing
        Me.picclose.tbSplit = "0,0,0,0"
        '
        'picTitle
        '
        Me.picTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picTitle.BackColor = System.Drawing.Color.Transparent
        Me.picTitle.Image = Global.iTong.iWeixin.My.Resources.Resources.icon_prompt
        Me.picTitle.Location = New System.Drawing.Point(185, 42)
        Me.picTitle.Name = "picTitle"
        Me.picTitle.Size = New System.Drawing.Size(37, 37)
        Me.picTitle.TabIndex = 0
        Me.picTitle.TabStop = False
        Me.picTitle.tbAutoSize = False
        Me.picTitle.tbBackgroundImage = Nothing
        Me.picTitle.tbSplit = "0,0,0,0"
        '
        'cmsRestore
        '
        Me.cmsRestore.AccessibleDescription = "153x48"
        Me.cmsRestore.DropShadowEnabled = False
        Me.cmsRestore.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsRestore.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.AToolStripMenuItem})
        Me.cmsRestore.Name = "munOperate"
        Me.cmsRestore.Size = New System.Drawing.Size(77, 26)
        Me.cmsRestore.tbBackColor = System.Drawing.Color.White
        Me.cmsRestore.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsRestore.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsRestore.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'AToolStripMenuItem
        '
        Me.AToolStripMenuItem.Name = "AToolStripMenuItem"
        Me.AToolStripMenuItem.Size = New System.Drawing.Size(76, 22)
        Me.AToolStripMenuItem.Text = "A"
        '
        'frmWeixinEmoticon
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(206, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(215, Byte), Integer))
        Me.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Zoom
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(1874, 600)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.lblState)
        Me.MinimumSize = New System.Drawing.Size(870, 600)
        Me.Name = "frmWeixinEmoticon"
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "微信表情"
        Me.Controls.SetChildIndex(Me.lblState, 0)
        Me.Controls.SetChildIndex(Me.pnlMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlInstall.ResumeLayout(False)
        Me.pnlInstall.PerformLayout()
        CType(Me.picWeixinIcon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlMain.ResumeLayout(False)
        Me.pnlWeb.ResumeLayout(False)
        Me.pnlTreeview.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlControlIcon.ResumeLayout(False)
        Me.pnlControlGroup.ResumeLayout(False)
        Me.pnlBottom.ResumeLayout(False)
        Me.pnlGuide.ResumeLayout(False)
        CType(Me.picError, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picclose, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picTitle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.cmsRestore.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlInstall As iTong.Components.tbPanel
    Friend WithEvents btnDownloadWeixin As iTong.Components.tbButton
    Friend WithEvents btnReCheck As iTong.Components.tbButton
    Friend WithEvents lblDownloadWeixin As System.Windows.Forms.Label
    Friend WithEvents lblWeixinDescription As System.Windows.Forms.Label
    Friend WithEvents lblWeixinTitle As System.Windows.Forms.Label
    Friend WithEvents picWeixinIcon As System.Windows.Forms.PictureBox
    Friend WithEvents lblState As iTong.Components.tbLabel
    Friend WithEvents pnlMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlLoading As iTong.Components.tbPanel
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pnlItems As iTong.Components.tbPanel
    Friend WithEvents pnlControlGroup As iTong.Components.tbPanel
    Friend WithEvents btnRefreshGroup As iTong.Components.tbButton
    Friend WithEvents btnAddGroup As iTong.Components.tbButton
    Friend WithEvents pnlControlIcon As iTong.Components.tbPanel
    Friend WithEvents btnDeleteIcon As iTong.Components.tbButton
    Friend WithEvents pnlGroups As iTong.Components.tbPanel
    Friend WithEvents btnDeleteGroup As iTong.Components.tbButton
    Friend WithEvents btnSelectAll As iTong.Components.tbButton
    Friend WithEvents btnExportGroup As iTong.Components.tbButton
    Friend WithEvents btnImportGroup As iTong.Components.tbButton
    Friend WithEvents btnImportIcon As iTong.Components.tbButton
    Friend WithEvents btnExportIcon As iTong.Components.tbButton
    Friend WithEvents pnlTreeview As iTong.Components.tbPanel
    Friend WithEvents pnlWeb As iTong.Components.tbPanel
    Public WithEvents tvwEmotion As iTong.Components.tbTreeView
    Friend WithEvents pnlBottom As iTong.Components.tbPanel
    Friend WithEvents lblMessage As iTong.Components.tbLabel
    Friend WithEvents btnAbrot As iTong.Components.tbButton
    Friend WithEvents btnBack As iTong.Components.tbButton
    Friend WithEvents wbsSite As iTong.Components.tbWebBrowserEx
    Friend WithEvents pnlSplit As iTong.Components.tbPanel
    Friend WithEvents btnRestore As iTong.Components.tbButton
    Friend WithEvents cmsRestore As iTong.Components.tbContextMenuStrip
    Friend WithEvents AToolStripMenuItem As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnSelectAllEmoticon As iTong.Components.tbButton
    Friend WithEvents pbarShowProgress As iTong.Components.tbControlBar
    Friend WithEvents lblHelper As iTong.Components.tbLabel
    Friend WithEvents pnlGuide As iTong.Components.tbPanel
    Friend WithEvents picclose As iTong.Components.tbPictureBox
    Friend WithEvents picTitle As iTong.Components.tbPictureBox
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents lblTitle As System.Windows.Forms.Label
    Friend WithEvents chkCheckCloseWeixin As iTong.Components.tbCheckBox
    Friend WithEvents lblSetp2 As System.Windows.Forms.Label
    Friend WithEvents lblSetp1 As System.Windows.Forms.Label
    Friend WithEvents picError As iTong.Components.tbPictureBox
    Friend WithEvents lblImportDescription As System.Windows.Forms.Label
End Class
