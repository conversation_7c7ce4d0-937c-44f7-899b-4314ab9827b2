﻿Imports System.Collections
Imports System.IO
Imports System.Drawing
Imports System.Windows.Forms
Imports iTong.Components
Imports iTong.CoreFoundation
Imports iTong.Device

Public Class frmWeixinEmoticonNotify

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.FunctionMappingKey = FunctionKey.Weixin
        'Me.Size = New Size(555, 470)
        Me.Icon = My.Resources.iTong
        Me.btn_normal.Visible = False
        Me.btn_minimize.Visible = False
        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbSplit = "10,33,10,33"
        Me.StartPosition = FormStartPosition.CenterParent

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.lblDescription.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Bold)
        'Me.lblDescription2.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        'Me.lbl1.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Bold)
        'Me.lbl2.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Bold)
        Me.lblSetp1.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Bold)
        Me.lblStep2.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Bold)
        Me.lblClickHome.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Bold)
        Me.lblCloseWeixin.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Bold)
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lblDescription.Text = Me.Language.GetString("Main.Message.MakesureCloseWeixin")  '"记得要从后台退出微信，再导入表情哦！"
        Me.chkNoShow.Text = Me.Language.GetString("App.Tip.Ignore")                         '"不再提示"
        Me.btnOK.Text = Me.Language.GetString("Main.Button.iSee")                           '"知道了"
        Me.Text = Me.Language.GetString("Common.Info")
        'Me.lblDescription2.Text = Me.Language.GetString("Main.Message.WeixinBackupFaceDB")  '"提示：助手将自动帮您备份微信记录，以便在您需要时进行恢复。"
        Me.lblSetp1.Text = Me.Language.GetString("Welcome.Label.StepOne")
        Me.lblStep2.Text = Me.Language.GetString("Welcome.Label.StepTwo")
        Me.lblClickHome.Text = Me.Language.GetString("Photo.Label.DoubleClickHone")
        Me.lblCloseWeixin.Text = Me.Language.GetString("Weixin.Lable.CloseWeixin")          '"关闭微信后台"
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As CoreFoundation.IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub

    Private Sub chkNoShow_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkNoShow.CheckedChanged
        'IniSetting.SetNoShowWeixinEmoticonNotify(Me.chkNoShow.Checked)
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Me.Close()
    End Sub

End Class