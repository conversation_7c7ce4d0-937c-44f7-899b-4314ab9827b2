﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{E35CAF1B-F6C3-4C47-8434-F1D20AD919B0}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>iTong.iWeixin</RootNamespace>
    <AssemblyName>iWeixin</AssemblyName>
    <MyType>Windows</MyType>
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>2.0</OldToolsVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DocumentationFile>iWeixin.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <DocumentationFile>iWeixin.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <CodeAnalysisRuleAssemblies>C:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x64' ">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <DocumentationFile>iWeixin.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleAssemblies>C:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x64' ">
    <DefineTrace>true</DefineTrace>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <DocumentationFile>iWeixin.xml</DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleAssemblies>C:\Program Files (x86)\Microsoft Visual Studio 8\Team Tools\Static Analysis Tools\FxCop\\rules</CodeAnalysisRuleAssemblies>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.vb</CodeAnalysisModuleSuppressionsFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.49.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\CoreMisc\IncludeDlls\x86\System.Data.SQLite.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="iTong.Android" />
    <Import Include="iTong.AppUnion" />
    <Import Include="iTong.Components" />
    <Import Include="iTong.CoreFoundation" />
    <Import Include="iTong.CoreModule" />
    <Import Include="iTong.CoreReses" />
    <Import Include="iTong.Device" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="frmWeixinEmoticon.Designer.vb">
      <DependentUpon>frmWeixinEmoticon.vb</DependentUpon>
    </Compile>
    <Compile Include="frmWeixinEmoticon.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmWeixinEmoticonNotify.Designer.vb">
      <DependentUpon>frmWeixinEmoticonNotify.vb</DependentUpon>
    </Compile>
    <Compile Include="frmWeixinEmoticonNotify.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmWeixinRepair.Designer.vb">
      <DependentUpon>frmWeixinRepair.vb</DependentUpon>
    </Compile>
    <Compile Include="frmWeixinRepair.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="WeixinEmoticonHepler.vb">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="WeixinHelper.vb">
      <SubType>Code</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="frmWeixinEmoticon.resx">
      <DependentUpon>frmWeixinEmoticon.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmWeixinEmoticonNotify.resx">
      <DependentUpon>frmWeixinEmoticonNotify.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="frmWeixinRepair.resx">
      <DependentUpon>frmWeixinRepair.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android.csproj">
      <Project>{898E7D3C-7ED3-4BD4-A668-5FBFB5C3F3AB}</Project>
      <Name>Android</Name>
    </ProjectReference>
    <ProjectReference Include="..\AppUnion\AppUnion.csproj">
      <Project>{D5156F82-717C-4C8E-A513-5343566FB2EA}</Project>
      <Name>AppUnion</Name>
    </ProjectReference>
    <ProjectReference Include="..\Components\Components.vbproj">
      <Project>{28459798-5E28-45D3-AAE3-74A9F1761D1A}</Project>
      <Name>Components</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc.csproj">
      <Project>{685AE51B-3C37-4B37-B3A2-B485D07A6E6B}</Project>
      <Name>CoreMisc</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreModuleEx\CoreModuleCS.csproj">
      <Project>{608e58d6-914f-45d2-a3a6-55cec62ef261}</Project>
      <Name>CoreModuleCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses.csproj">
      <Project>{65CE9103-521A-49E6-A8CD-89137B452842}</Project>
      <Name>CoreReses</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag.csproj">
      <Project>{B02CE9CF-6163-411C-870E-1508DDD16CFC}</Project>
      <Name>CoreTag</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUpdate\CoreUpdate.csproj">
      <Project>{998A7F44-F7AF-4E25-9EF3-696834EE6242}</Project>
      <Name>CoreUpdate</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS.csproj">
      <Project>{61c62ea1-6ad7-41be-98db-8abb77ee35f3}</Project>
      <Name>CoreUtilCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil.vbproj">
      <Project>{718B167F-1B33-4B7C-A7E3-E15615DDAAC4}</Project>
      <Name>CoreUtil</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone.csproj">
      <Project>{6691EA4B-1EDB-4330-A3A3-E1D47F4D35BE}</Project>
      <Name>iPhone</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPodDB\iPodDB.csproj">
      <Project>{8B5C5494-3487-4632-AB5F-9193E29623DB}</Project>
      <Name>iPodDB</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_delete_4.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_editor_1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_export_4.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_import_4.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_photo_delete_one.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_photo_export_one.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_photo_share_one.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_selectall_4.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\file_pic_large.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\file_weixin_large.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\frm_bg_blank.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\gif_loading_24.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\icon_emotion.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\more_weixinrepair.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\photo_chk_false.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\photo_chk_true.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\photo_toolbar_bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\tvw_emoticon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\tvw_emoticonstore.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\wallpaper_default_ipad.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\weixin_close3.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_3_goto.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_refresh.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_restore.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_add_4.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_back_4.png" />
    <None Include="Resources\weixin_dll_aud" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pnl_bg_navigation.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pnl_loading.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\pnl_loading_small.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\frm_bg_sub.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_3_cancel.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\iTong.ico" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_close.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\dgv_progress_bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\dgv_progress_value.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\weixin_dll_silk.dll" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\weixin_guide_bg.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\icon_prompt.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\dgv_filenoexist.png" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <PropertyGroup>
    <PreBuildEvent>copy $(SolutionDir)\iTong\IncludeDlls\WeChat $(TargetDir)
copy $(SolutionDir)\iTong\IncludeDlls\Flash $(TargetDir)
copy $(SolutionDir)\iTong\IncludeDlls\$(PlatformName) $(TargetDir)
del /s /q $(TargetDir)\ASL.dll
del /s /q $(TargetDir)\CoreFoundation.dll
del /s /q $(TargetDir)\icudt55.dll
del /s /q $(TargetDir)\libdispatch.dll
del /s /q $(TargetDir)\libicuin.dll
del /s /q $(TargetDir)\libicuuc.dll</PreBuildEvent>
  </PropertyGroup>
</Project>