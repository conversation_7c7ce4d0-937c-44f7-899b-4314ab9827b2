﻿'------------------------------------------------------------------------------
' <auto-generated>
'     此代码由工具生成。
'     运行时版本:4.0.30319.18444
'
'     对此文件的更改可能会导致不正确的行为，并且如果
'     重新生成代码，这些更改将会丢失。
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict On
Option Explicit On

Imports System

Namespace My.Resources
    
    '此类是由 StronglyTypedResourceBuilder
    '类通过类似于 ResGen 或 Visual Studio 的工具自动生成的。
    '若要添加或移除成员，请编辑 .ResX 文件，然后重新运行 ResGen
    '(以 /str 作为命令选项)，或重新生成 VS 项目。
    '''<summary>
    '''  一个强类型的资源类，用于查找本地化的字符串等。
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0"),  _
     Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.Runtime.CompilerServices.CompilerGeneratedAttribute(),  _
     Global.Microsoft.VisualBasic.HideModuleNameAttribute()>  _
    Friend Module Resources
        
        Private resourceMan As Global.System.Resources.ResourceManager
        
        Private resourceCulture As Global.System.Globalization.CultureInfo
        
        '''<summary>
        '''  返回此类使用的缓存的 ResourceManager 实例。
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend ReadOnly Property ResourceManager() As Global.System.Resources.ResourceManager
            Get
                If Object.ReferenceEquals(resourceMan, Nothing) Then
                    Dim temp As Global.System.Resources.ResourceManager = New Global.System.Resources.ResourceManager("iTong.iWeixin.Resources", GetType(Resources).Assembly)
                    resourceMan = temp
                End If
                Return resourceMan
            End Get
        End Property
        
        '''<summary>
        '''  使用此强类型资源类，为所有资源查找
        '''  重写当前线程的 CurrentUICulture 属性。
        '''</summary>
        <Global.System.ComponentModel.EditorBrowsableAttribute(Global.System.ComponentModel.EditorBrowsableState.Advanced)>  _
        Friend Property Culture() As Global.System.Globalization.CultureInfo
            Get
                Return resourceCulture
            End Get
            Set
                resourceCulture = value
            End Set
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_3_cancel() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_cancel", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_3_goto() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_3_goto", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_4_blue() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_blue", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_4_refresh() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_refresh", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_4_restore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_4_restore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_add_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_add_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_back_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_back_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_close() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_close", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_delete_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_delete_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_editor_1() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_editor_1", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_export_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_export_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_import_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_import_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_photo_delete_one() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_photo_delete_one", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_photo_export_one() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_photo_export_one", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_photo_share_one() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_photo_share_one", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property btn_selectall_4() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("btn_selectall_4", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property dgv_filenoexist() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_filenoexist", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property dgv_progress_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_progress_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property dgv_progress_value() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("dgv_progress_value", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property file_pic_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_pic_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property file_weixin_large() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("file_weixin_large", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property frm_bg_blank() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_blank", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property frm_bg_sub() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("frm_bg_sub", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property gif_loading_24() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("gif_loading_24", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property icon_emotion() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_emotion", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property icon_prompt() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("icon_prompt", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找类似于 (Icon) 的 System.Drawing.Icon 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property iTong() As System.Drawing.Icon
            Get
                Dim obj As Object = ResourceManager.GetObject("iTong", resourceCulture)
                Return CType(obj,System.Drawing.Icon)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property more_weixinrepair() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("more_weixinrepair", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property photo_chk_false() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_chk_false", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property photo_chk_true() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_chk_true", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property photo_toolbar_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("photo_toolbar_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property pnl_bg_navigation() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_bg_navigation", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property pnl_loading() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_loading", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property pnl_loading_small() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("pnl_loading_small", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property tvw_emoticon() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_emoticon", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property tvw_emoticonstore() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("tvw_emoticonstore", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property wallpaper_default_ipad() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("wallpaper_default_ipad", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property weixin_close3() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weixin_close3", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Byte[] 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property weixin_dll_aud() As Byte()
            Get
                Dim obj As Object = ResourceManager.GetObject("weixin_dll_aud", resourceCulture)
                Return CType(obj,Byte())
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Byte[] 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property weixin_dll_silk() As Byte()
            Get
                Dim obj As Object = ResourceManager.GetObject("weixin_dll_silk", resourceCulture)
                Return CType(obj,Byte())
            End Get
        End Property
        
        '''<summary>
        '''  查找 System.Drawing.Bitmap 类型的本地化资源。
        '''</summary>
        Friend ReadOnly Property weixin_guide_bg() As System.Drawing.Bitmap
            Get
                Dim obj As Object = ResourceManager.GetObject("weixin_guide_bg", resourceCulture)
                Return CType(obj,System.Drawing.Bitmap)
            End Get
        End Property
    End Module
End Namespace
