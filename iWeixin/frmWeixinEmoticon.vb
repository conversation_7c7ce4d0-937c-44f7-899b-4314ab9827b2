﻿Imports System.Threading
Imports System.Web
Imports System.Text.RegularExpressions
Imports System.Collections
Imports System.IO
Imports System.Drawing
Imports System.Windows.Forms
Imports iTong.Components
Imports iTong.CoreFoundation
Imports iTong.Device

Public Class frmWeixinEmoticon

    Protected mDataObject As DataObject = Nothing
    Private mDownManage As MultiThreadDownload
    Private mCurrentEmoticon As Emoticon = Nothing
    Private mCurrentListViewItem As ListViewItem = Nothing

    Private mlvwGroups As tbListViewEx = Nothing
    Private mlvwItems As tbListViewEx = Nothing
    Private mLargeImageGroups As ImageList = Nothing
    Private mLargeImageItems As ImageList = Nothing

    Private mrectExportOne As Rectangle = New Rectangle()
    Private mrectDeleteOne As Rectangle = New Rectangle()
    Private mrectToolBar As Rectangle = New Rectangle()
    Private mCurrentHoverItem As ListViewItem = Nothing
    Private mPenSelectedListViewItem As New Pen(Color.FromArgb(255, 23, 120, 205), 2)
    Private mPenUnSelectedListViewItem As New Pen(Color.FromArgb(255, 221, 221, 221))
    Private mStatus As WeixinState = WeixinState.Loading

    Private mDictThumbnail As Dictionary(Of String, Image) = New Dictionary(Of String, Image)(StringComparer.InvariantCultureIgnoreCase)
    Private mDictResources As Dictionary(Of String, Image) = Nothing
    Private mGroupIconSize As New Size(140, 140)
    Private mListViewImgSize As New Size(120, 145)

    Private mDeviceNode As tbTreeNode = Nothing
    Private mWebNode As tbTreeNode = Nothing
    Private mNodeEmoticon As tbTreeNode = Nothing
    Private mNodeSite As tbTreeNode = Nothing
    Private mEmoticonHelper As WeixinEmoticonHepler = Nothing
    Private mblnFirstLoadSite As Boolean = True
    Private mblnFirstLoad As Boolean = True
    Private mstrItemId As String = "414478124"
    Private mDictThumbUrl As New Dictionary(Of String, String)      '存放缩回图片的地址

    Public Event OnLoadFinished(ByVal sender As Object, ByVal args As WeixinEmoticonLoadArgs)

#Region "---  初始化  ---"

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice)
        MyBase.New(application, device)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.FunctionMappingKey = FunctionKey.Weixin
        Me.tbAutoSetFormSize = True
        Me.Size = New Size(870, 630)
        Me.Icon = My.Resources.iTong
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Try
            If Me.Parent IsNot Nothing Then
                Me.pnlMain.Dock = DockStyle.Fill
                Me.pnlMain.BringToFront()
            End If

            Me.pnlMain.RowStyles(2).SizeType = SizeType.Absolute
            Me.pnlMain.RowStyles(2).Height = 0

            Me.AddResources2Dictionary()
            Me.InitDownload()

            If Not Folder.LangType = LanguageType.en_US Then
                Common.SetImageLocation(Me.picLoading, My.Resources.gif_loading_24)
            End If

            Me.InitListView()
            Me.InitEmoticonHelper()
            Me.LoadEmoticon()
            With Me.wbsSite
                .IsWebBrowserContextMenuEnabled = True
                .ScriptErrorsSuppressed = True
                '.ScrollBarsEnabled = True
                .WebBrowserShortcutsEnabled = True
                .ShowNavigateErrorPage = False
                '.ShowLoadingWait = True
                If Not Folder.LangType = LanguageType.en_US Then
                    .LoadingGif = My.Resources.gif_loading_24
                Else
                    .LoadingGif = My.Resources.pnl_loading_small
                End If
                .LoadingFont = Common.CreateFont("Arial", 10.0F, FontStyle.Regular)
                If Utility.IsPanda Then
                    .Client = "panda"
                End If
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmWeixinEmoticon_InitControls")
        End Try
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.lblTitle.Font = Common.CreateFont("微软雅黑", 17.0!, FontStyle.Regular)
        Me.lblSetp1.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Regular)
        Me.lblSetp2.Font = Common.CreateFont("微软雅黑", 15.0!, FontStyle.Regular)

        Me.Text = Me.Language.GetString("Weixin.Lable.Emoticon")                        '"微信表情"
        Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Lable.NotInstall")       '"尚未安装微信"
        Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Lable.Description") '"微信是一款手机通信软件，支持通过手机网络发送语音短信、视频、图片和文字，可以单聊及群聊，还能根据地理位置找到附近的人，带给朋友们全新的移动沟通体验。"
        Me.btnReCheck.Text = Me.Language.GetString("File.Button.CheckInstall")          '"重新检查"
        Me.lblDownloadWeixin.Text = Me.Language.GetString("Weixin.Lable.GetWeixin")     '"立即免费获取微信"
        Me.lblLoading.Text = Me.Language.GetString("Common.Loading")                    '""
        Me.btnAddGroup.Text = Me.Language.GetString("Contact.Button.NewGroup")          '"新建分组"
        Me.btnImportGroup.Text = Me.Language.GetString("Media.Button.Import")           '"导入"
        Me.btnExportGroup.Text = Me.Language.GetString("File.Button.DownloadToPC")      '"导出"
        Me.btnDeleteGroup.Text = Me.Language.GetString("Common.Delete")                 '"删除"
        Me.btnBack.Text = Me.Language.GetString("Common.Button.Return")                 '"返回"
        Me.btnImportIcon.Text = Me.Language.GetString("Media.Button.Import")            '"导入"
        Me.btnExportIcon.Text = Me.Language.GetString("File.Button.DownloadToPC")       '"导出"
        Me.btnDeleteIcon.Text = Me.Language.GetString("Common.Delete")                  '"删除"
        Me.btnSelectAll.Text = Me.Language.GetString("Common.SelectAll")                '"全选"
        Me.btnRestore.Text = Me.Language.GetString("Weixin.Button.RestoreChat")         '"恢复聊天记录" 'Me.Language.GetString("App.Cell.Restoring")                '"恢复"
        'Me.btnSync.Text = Me.Language.GetString("Common.Button.Save")                  '"保存"
        Me.btnSelectAllEmoticon.Text = Me.Language.GetString("Common.SelectAll")        '"全选"
        Me.lblHelper.Text = Me.Language.GetString("Weixin.Lable.ImportFailuerHelp")     '"导入失败教程"
        Me.lblTitle.Text = Me.Language.GetString("Main.Message.MakesureCloseWeixin")    ' "请务必从后台退出微信 ，否则导入时可能丢失记录"
        Me.lblSetp1.Text = Me.Language.GetString("Welcome.Label.StepOne") & Me.Language.GetString("Common.Symbol.Colon") & Me.Language.GetString("Photo.Label.DoubleClickHone")
        Me.lblSetp2.Text = Me.Language.GetString("Welcome.Label.StepTwo") & Me.Language.GetString("Common.Symbol.Colon") & Me.Language.GetString("Weixin.Lable.CloseWeixin")
        Me.chkCheckCloseWeixin.Text = Me.Language.GetString("Weixin.Lable.AlreadyCloseWeixin")  '"我已退出微信后台"
        Me.btnOK.Text = Me.Language.GetString("Common.OK")                              '"确定"
        Me.btnRefreshGroup.Text = Me.Language.GetString("Common.Button.Refresh")        '"刷新"

        Me.lblImportDescription.Text = Me.Language.GetString("Weixin.Message.SmiliesFavorites") '"6.2.2以上版本的微信，无法新建分组，仅能将表情导入已有的分组中。" & vbCrLf & "助手将默认把表情导入""我的收藏""，当分组中的表情超过140个时，" & vbCrLf & "自动导入下一个分组。"
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        '如果选中的是设备结点，则要切换到本地所有软件结点
        '关闭窗体
        Me.Close()
    End Sub

    Private Sub InitListView()
        Me.mLargeImageGroups = New ImageList()
        Me.mLargeImageGroups.ColorDepth = ColorDepth.Depth32Bit
        Me.mLargeImageGroups.ImageSize = Me.mListViewImgSize

        Me.mLargeImageItems = New ImageList()
        Me.mLargeImageItems.ColorDepth = ColorDepth.Depth32Bit
        Me.mLargeImageItems.ImageSize = New Size(118, 130)

        Dim img As Image = Utility.GetThumbnail(My.Resources.wallpaper_default_ipad.Clone, Me.mGroupIconSize)
        Me.mDictThumbnail.Add("Default", img)
        Me.mLargeImageGroups.Images.Add("Default", My.Resources.file_pic_large.Clone)
        Me.mLargeImageItems.Images.Add("Default", My.Resources.file_pic_large.Clone)

        Me.mlvwGroups = Me.CreateListView(Me.mLargeImageGroups, True)
        Me.pnlGroups.Controls.Add(Me.mlvwGroups)
        Me.mlvwGroups.Dock = DockStyle.Fill

        Me.mlvwItems = Me.CreateListView(Me.mLargeImageItems, False)
        Me.pnlItems.Controls.Add(Me.mlvwItems)
        Me.mlvwItems.Dock = DockStyle.Fill
    End Sub

    Protected Overrides Sub InitTreeView()
        Me.mWebNode = New tbTreeNode("web", Me.Language.GetString("Site.Label.WebResource"), Color.Black, True) '网络资源
        Me.mDeviceNode = New tbTreeNode("device", Me.Language.GetString("App.TreeNode.NotConnectDevice"), Color.Black, True) 'Device(设备未连接)
        If Me.mDevice IsNot Nothing Then
            Dim strDevice As String = String.Format("{0} - {1}", Me.Language.GetString("App.TreeNode.Device"), Me.mDevice.DeviceName)
            Me.mDeviceNode.Text = strDevice
        End If

        Me.tvwEmotion.Nodes.Add(Me.mWebNode)
        Me.tvwEmotion.Nodes.Add(Me.mDeviceNode)

        Me.mNodeSite = New tbTreeNode("nodeSite", Me.Language.GetString("Weixin.Lable.EmoticonSite"), Color.Black, False)  '"表情商店"
        Me.mNodeSite.NodeIcon = My.Resources.tvw_emoticonstore
        Me.mNodeSite.NodeIconState = ImageState.TwoState

        Me.mNodeEmoticon = New tbTreeNode("nodeEmoticon", Me.Language.GetString("Weixin.Lable.EmoticonModify"), Color.Black, False) '"表情管理"
        Me.mNodeEmoticon.NodeIcon = My.Resources.tvw_emoticon
        Me.mNodeEmoticon.NodeIconState = ImageState.TwoState

        Me.mWebNode.ChildNodes.Add(Me.mNodeSite)
        Me.mDeviceNode.ChildNodes.Add(Me.mNodeEmoticon)
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            RemoveHandler Me.mEmoticonHelper.OnLoadFinished, AddressOf mEmoticonHelper_LoadFinished

            Me.mEmoticonHelper.Close()
        Catch ex As Exception
        End Try

        Try
            RemoveHandler Me.mDownManage.TaskStart, AddressOf OnTaskStart
            RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloadItemCallBack
            RemoveHandler Me.mDownManage.IconDownloadedEvent, AddressOf OnIconDownloadedEvent
            RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloadItemCompleted
            RemoveHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
            RemoveHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd
        Catch ex As Exception
        End Try
    End Sub

    Private Sub InitEmoticonHelper()
        Me.mEmoticonHelper = WeixinEmoticonHepler.Instance(Me.mDevice)
        AddHandler Me.mEmoticonHelper.OnLoadFinished, AddressOf mEmoticonHelper_LoadFinished
        AddHandler Me.mEmoticonHelper.OnRestore, AddressOf mEmoticonHelper_RestoreFinished
        AddHandler Me.mEmoticonHelper.OnSync, AddressOf mEmoticonHelper_Sync
    End Sub

    Private Sub InitDownload()
        Me.mDownManage = MultiThreadDownload.Instance()
        AddHandler Me.mDownManage.TaskStart, AddressOf OnTaskStart
        AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloadItemCallBack
        AddHandler Me.mDownManage.IconDownloadedEvent, AddressOf OnIconDownloadedEvent
        AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloadItemCompleted
        AddHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
        AddHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd
    End Sub

#End Region

#Region "---  表情操作事件  ---"

    Private Sub mEmoticonHelper_RestoreFinished(ByVal sender As Object, ByVal e As EventArgs)
        Me.LoadEmoticon()
        Me.SetEnable(True)
    End Sub

    Private Sub mEmoticonHelper_LoadFinished(ByVal sender As Object, ByVal e As WeixinEmoticonLoadArgs)
        If e.Status = WeixinState.Succeed AndAlso Me.mblnFirstLoad Then
            Me.SetPanelStatus(WeixinState.Guide)
            Me.mblnFirstLoad = False
            If e.Is622 Then
                Me.lblWeixinDescription.Visible = True
            Else
                Me.lblWeixinDescription.Visible = False
            End If
            Return

        ElseIf e.Status = WeixinState.Above6220 Then
            RaiseEvent OnLoadFinished(Me, e)
            Me.Close()
        End If
        Me.Loaded(e.Status)
    End Sub

    Private Delegate Sub LoadedHandler(ByVal status As WeixinState)
    Private Sub Loaded(ByVal status As WeixinState)
        If Me.InvokeRequired Then
            Me.Invoke(New LoadedHandler(AddressOf Loaded), status)
        Else
            Try
                If status <> WeixinState.Succeed Then
                    Me.SetPanelStatus(status)
                Else

                    Me.ShowGroups()
                    'Me.tvwEmotion.SelectedNode = Me.mNodeEmoticon
                    If Me.tvwEmotion.SelectedNode Is Nothing Then
                        Me.SetPanelStatus(WeixinState.WebSite)
                        Me.tvwEmotion.SelectedNode = Me.mNodeSite
                    Else
                        Me.SetPanelStatus(WeixinState.UpdateGroup)
                        Me.tvwEmotion.SelectedNode = Me.mNodeEmoticon

                    End If

                    'If Me.mblnFirstLoad AndAlso Not IniSetting.GetNoShowWeixinEmoticonNotify Then
                    '    Me.mblnFirstLoad = False
                    '    Dim frm As New frmWeixinEmoticonNotify(Me.mApplication, Me.mDevice)
                    '    frm.ShowDialog(Me)
                    'End If

                End If
                If Me.mEmoticonHelper.Emoticons Is Nothing Then
                    Return
                End If
                For Each Item As Emoticon In Me.mEmoticonHelper.Emoticons.Values
                    If Item.Icon IsNot Nothing Then
                        If Me.mDictThumbnail.ContainsKey(Item.EmoticonPackageId) Then
                            Me.mDictThumbnail(Item.EmoticonPackageId) = Item.Icon
                        End If
                    End If
                Next
                Me.SetCount()
                Me.SetContralsEnable()
                Me.SetWebButtonDisable()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "bgwLoadFriend_RunWorkerCompleted")
            End Try
        End If
    End Sub

    Private Function ReplaceWinIllegalName(ByVal strNmae As String) As String
        Dim strReturn As String = ""
        Try
            strReturn = Utility.ReplaceWinIllegalName(strNmae).Replace("（", "").Replace("）", "")
        Catch ex As Exception
        End Try
        Return strReturn
    End Function

    Private Sub mEmoticonHelper_Sync(ByVal sender As Object, ByVal e As WeixinEmoticonArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of WeixinEmoticonArgs)(AddressOf mEmoticonHelper_Sync), sender, e)
        Else
            Try
                If e.Device Is Nothing OrElse e.Device IsNot Me.mDevice Then
                    Return
                End If

                Select Case e.Status
                    Case EmoticonStatus.Process
                        Me.lblState.Text = Me.Language.GetString("Media.Message.UpdateDB")            '"正在更新数据库"

                    Case EmoticonStatus.Succeed
                        Me.lblState.Text = Me.Language.GetString("Weixin.Message.UpdateSucceed")     '"更新成功"
                        Me.SetEnable(True)

                        '6.2.2是导入到收藏夹里的，所以设置所有正在导入的状态。

                        For Each Item As Emoticon In e.LstEmoticon
                            If Item Is Nothing Then
                                Continue For
                            End If
                            WebSiteHelperBase.InstanceBase().SetWebAppMarketStatus(Me.wbsSite, "importFinish", Me.ReplaceWinIllegalName(Item.EmoticonPackageName), "")

                            For Each itemName As String In Item.ContainGroup
                                WebSiteHelperBase.InstanceBase().SetWebAppMarketStatus(Me.wbsSite, "importFinish", Me.ReplaceWinIllegalName(itemName), "")
                            Next
                        Next

                    Case EmoticonStatus.Failure
                        Me.lblState.Text = Me.Language.GetString("Weixin.Message.UpdateFailure")        '"更新失败"
                        Me.SetEnable(True)

                        For Each Item As Emoticon In e.LstEmoticon
                            If Item Is Nothing Then
                                Continue For
                            End If
                            Common.LogException(String.Format("更新失败{0}", Item.EmoticonPackageName))
                            WebSiteHelperBase.InstanceBase().SetWebAppMarketStatus(Me.wbsSite, "setEnable", Me.ReplaceWinIllegalName(Item.EmoticonPackageName), "")
                        Next

                End Select
                If e.Status <> EmoticonStatus.Succeed Then
                    Me.lblState.Text &= String.Format("{0}%", e.Progress).PadLeft(3, " "c)
                End If


            Catch ex As Exception
                Common.LogException(ex.ToString, "mEmoticonHelper_Add")
            End Try
        End If
    End Sub

#End Region

#Region "---  窗体事件  ---"

    Private Sub lblDownloadWeixin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblDownloadWeixin.Click, btnDownloadWeixin.Click
        WebSiteHelperBase.InstanceBase.DownloadAppByIDEx(Me.mApplication, Me.mDevice, Me.mstrItemId, "", "WeixinEmo")
        'Me.Close()
    End Sub

    Private Sub btnReCheck_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReCheck.Click
        Me.DisposeWeixinDB()
        Me.LoadEmoticon()
    End Sub

    Private Sub DisposeWeixinDB()
        Try
            Me.mEmoticonHelper.Close()
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnRefreshGroup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefreshGroup.Click
        Me.LoadEmoticon()
    End Sub

    Private Sub btnSelectAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSelectAll.Click
        Dim intIndex As Integer = 0
        For Each Item As ListViewItem In Me.mlvwItems.Items
            Item.Selected = True
            intIndex += 1
            If intIndex Mod 50 = 0 Then
                Application.DoEvents()
            End If
        Next
    End Sub

    Private Sub btnAddGroup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddGroup.Click
        For Each Item As ListViewItem In Me.mlvwGroups.Items
            Item.Selected = False
            Application.DoEvents()
        Next

        Dim em As Emoticon = Me.AddGroup(Me.GetNewGroupName())
        Dim lvwItem As ListViewItem = Me.GetCurrentListViewItem(em)
        If lvwItem IsNot Nothing Then
            lvwItem.BeginEdit()
        End If
        Me.SetContralsEnable(lvwItem)
    End Sub

    Private Function GetNewGroupName() As String
        Dim strName As String = Me.Language.GetString("Contact.Button.NewGroup")        '"新建分组"
        Dim index As Integer = 0
        For Each Item As Emoticon In Me.mEmoticonHelper.Emoticons.Values
            If Item.EmoticonPackageName.Contains(Me.Language.GetString("Contact.Button.NewGroup")) Then
                Dim intVal As Integer = Val(Item.EmoticonPackageName.Replace(Me.Language.GetString("Contact.Button.NewGroup"), "").Replace("(", "").Replace(")", ""))
                If index < intVal Then
                    index = intVal
                End If
            End If
        Next
        index = index + 1
        strName = String.Format(Me.Language.GetString("Contact.Button.NewGroup") & "({0})", index)
        Return strName
    End Function

    Private Sub btnDeleteGroup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDeleteGroup.Click
        Me.DeleteGroup()
    End Sub

    Private Sub btnUpdateGroup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If Me.mlvwGroups.Items.Count > 0 AndAlso Me.mlvwGroups.SelectedItems IsNot Nothing AndAlso Me.mlvwGroups.SelectedItems.Count > 0 Then
            Me.mlvwGroups.SelectedItems(0).BeginEdit()
        End If
    End Sub

    Private Sub btnDeleteIcon_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDeleteIcon.Click
        Me.DeleteItem()
    End Sub

    Private Sub btnExportIcon_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExportIcon.Click
        Me.ExportIcon()
    End Sub

    Private Sub btnImportIcon_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImportIcon.Click
        Dim openDlg As New OpenFileDialog()
        openDlg.Multiselect = True
        openDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") & "(*.GIF;*.PNG;*.JPG;*.JPEG;*.BMP)|*.GIF;*.PNG;*.JPG;*.JPEG;*.BMP"
        If openDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
            Dim files As String() = openDlg.FileNames()
            Me.AddItems(Me.mCurrentEmoticon, files)
        End If
    End Sub

    Private Function CheckIsImage(ByVal strFileName As String) As Boolean
        Dim blnReturn As Boolean = False
        If strFileName.ToLower.EndsWith(".gif") OrElse _
           strFileName.ToLower.EndsWith(".png") OrElse _
           strFileName.ToLower.EndsWith(".jpg") OrElse _
           strFileName.ToLower.EndsWith(".jpeg") OrElse _
           strFileName.ToLower.EndsWith(".bmp") Then
            blnReturn = True

        End If
        Return blnReturn
    End Function

    Private Sub btnGoUp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBack.Click
        Me.SetPanelStatus(WeixinState.UpdateGroup)
        Me.SetCount()
        Me.SetContralsEnable()
    End Sub

    Private Sub btnImportGroup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnImportGroup.Click
        Dim openDlg As New OpenFileDialog()
        openDlg.Multiselect = True
        openDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") & "(*.GIF;*.PNG;*.JPG;*.JPEG;*.BMP;*.EIF;*.EIP;*.ZIP)|*.GIF;*.PNG;*.JPG;*.JPEG;*.BMP;*.EIF;*.EIP;*.ZIP"

        If openDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
            Dim files As String() = openDlg.FileNames()
            Me.DoImportGroup(files)
        End If
    End Sub

    Private Delegate Sub DoImportGroupHandler(ByVal files As String())
    Private Sub DoImportGroup(ByVal files As String())
        If Me.InvokeRequired Then
            Me.Invoke(New DoImportGroupHandler(AddressOf DoImportGroup), files)
        Else
            Dim filesPic As New List(Of String)
            Dim filesEip As New List(Of String)
            Dim filesPath As New List(Of String)
            For Each Item As String In files
                If Item.ToLower.EndsWith(".eip") OrElse Item.ToLower.EndsWith(".eif") OrElse Item.ToLower.EndsWith(".zip") Then
                    filesEip.Add(Item)
                ElseIf Me.CheckIsImage(Item) Then
                    filesPic.Add(Item)
                ElseIf Directory.Exists(Item) Then
                    filesPath.Add(Item)
                End If
            Next

            For Each Item As String In filesEip
                Me.DoImportEif(Item)
            Next

            If filesPic.Count > 0 Then
                Dim em As Emoticon = Me.AddGroup(Me.GetNewGroupName())
                Me.AddItems(em, filesPic.ToArray)
            End If

            Dim lstImg As New List(Of String)
            For Each Item As String In filesPath
                For Each imgPath As String In Directory.GetFiles(Item, "*.*", SearchOption.AllDirectories)
                    If Me.CheckIsImage(imgPath) Then
                        lstImg.Add(imgPath)
                    End If
                    Application.DoEvents()
                Next
                If lstImg.Count > 0 Then
                    Dim em As Emoticon = Me.AddGroup(Path.GetFileName(Item))
                    Me.AddItems(em, lstImg.ToArray)
                    lstImg.Clear()
                End If
            Next
        End If
    End Sub

    Private Delegate Sub DoImportEifHandler(ByVal strItem As String)
    Private Sub DoImportEif(ByVal strItem As String)
        If Me.InvokeRequired Then
            Me.Invoke(New DoImportEifHandler(AddressOf DoImportEif), strItem)
        Else
            Dim strName As String = Path.GetFileNameWithoutExtension(strItem)
            Dim em As Emoticon = Me.AddGroup(strName)
            Dim strDesDirectory As String = Folder.GetTempFilePath()

            If strItem.ToLower.EndsWith(".zip") Then
                Utility.unzip(strItem, strDesDirectory)
            Else
                Dim emFile As WeixinEmoticonFile = New WeixinEmoticonFile()
                emFile.Decompress(strItem, strDesDirectory)
            End If
            Dim lstFiles As New List(Of String)
            Dim faces() As String = Directory.GetFiles(strDesDirectory, "*.gif", SearchOption.AllDirectories)
            lstFiles.AddRange(faces)

            faces = Directory.GetFiles(strDesDirectory, "*.jpg", SearchOption.AllDirectories)
            lstFiles.AddRange(faces)
            faces = Directory.GetFiles(strDesDirectory, "*.png", SearchOption.AllDirectories)
            lstFiles.AddRange(faces)
           
            If lstFiles.Count = 0 Then
                faces = Directory.GetFiles(strDesDirectory, "*.bmp", SearchOption.AllDirectories)
                lstFiles.AddRange(faces)
            End If

            Me.AddItems(em, lstFiles.ToArray)
        End If
    End Sub

    Private Sub btnExportGroup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExportGroup.Click
        Me.Export()
    End Sub

    Private Sub tvwEmotion_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As iTong.Components.tbTreeView_SelectedIndexChanged_EventArgs) Handles tvwEmotion.SelectedIndexChanged
        If e.Node.Name = "nodeSite" Then
            Me.lblState.Text = ""
            If Me.mblnFirstLoadSite = True Then
                Me.mblnFirstLoadSite = False
                'http://v3.tongbu.com/V3-face/
                Me.wbsSite.Navigate(WebUrl.PageWeixinEmoticon)
            End If
            Me.SetPanelStatus(WeixinState.WebSite)
        ElseIf e.Node.Name = "nodeEmoticon" Then
            If Me.mStatus = WeixinState.Loading Then
                Me.mStatus = WeixinState.UpdateGroup
            End If
            Me.SetPanelStatus(Me.mStatus)
        End If
    End Sub

    Private Sub btnAbrot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAbrot.Click
        Me.pnlMain.RowStyles(2).SizeType = SizeType.Absolute
        Me.pnlMain.RowStyles(2).Height = 0
    End Sub

    Private Sub wbsSite_NavigateError(ByVal sender As Object, ByVal e As Components.WebBrowserNavigateErrorEventArgs) Handles wbsSite.NavigateError
        WebSiteHelperBase.InstanceBase.WebBrowserNavigateError(Me.mApplication, Me.wbsSite, e, 90)
    End Sub

    Private Sub wbsSite_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs) Handles wbsSite.Navigating
        Dim url As String = e.Url.ToString
        If url.Contains("Loaded") Then
            Me.SetWebButtonDisable()
            Application.DoEvents()
            e.Cancel = True
        ElseIf url.Contains("itong://refresh/") Then
            Me.wbsSite.Navigate(WebUrl.PageWeixinEmoticon)
        Else
            Me.GetThumbUrl(url)
            WebSiteHelperBase.InstanceBase().WebBrowserNavigating(Me.mApplication, Me.mDevice, sender, e)
        End If
    End Sub

    Private Sub webMsg_NewMessage(ByVal sender As tbWebBrowser, ByVal message As String) Handles wbsSite.NewMessage
        If String.IsNullOrEmpty(message) Then
            Return
        End If
        Common.Log("Emotion Message")
        Common.Log(message)
        Dim strMsg As String = message
        If strMsg.ToLower.Contains("emoticon_loaded") Then
            Me.SetWebButtonDisable()
            Application.DoEvents()
        End If
    End Sub

    Private Sub GetThumbUrl(ByVal strUrl As String)
        Dim strName As String = Utility.GetParamValueFromQuery("filename", strUrl)
        Dim strThumbUrl As String = Utility.GetParamValueFromQuery("thumb", strUrl)
        If strName.Length > 0 AndAlso strThumbUrl.Length > 0 Then
            If Me.mDictThumbUrl.ContainsKey(strName) Then
                Me.mDictThumbUrl(strName) = strThumbUrl
            Else
                Me.mDictThumbUrl.Add(strName, strThumbUrl)
            End If
        End If
    End Sub

    Private Sub SetWebButtonDisable()
        Dim strNames As String = ""
        For Each Item As Emoticon In Me.mEmoticonHelper.Emoticons.Values
            strNames &= Me.ReplaceWinIllegalName(Item.EmoticonPackageName) & ","
        Next
        strNames = strNames.Trim(",")
        WebSiteHelperBase.InstanceBase().SetWebAppMarketStatus(Me.wbsSite, "disEnable", strNames, "")
    End Sub

    Private Sub btnAddGroup_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddGroup.SizeChanged, btnImportGroup.SizeChanged, btnExportGroup.SizeChanged, btnDeleteGroup.SizeChanged, btnRestore.SizeChanged, btnSelectAllEmoticon.SizeChanged
        Me.btnImportGroup.Location = New Point(Me.btnAddGroup.Right + 2, Me.btnImportGroup.Top)
        Me.btnExportGroup.Location = New Point(Me.btnImportGroup.Right + 2, Me.btnExportGroup.Top)
        Me.btnDeleteGroup.Location = New Point(Me.btnExportGroup.Right + 2, Me.btnDeleteGroup.Top)
        Me.btnSelectAllEmoticon.Location = New Point(Me.btnDeleteGroup.Right + 2, Me.btnSelectAllEmoticon.Top)
        Me.btnRestore.Location = New Point(Me.btnSelectAllEmoticon.Right + 2, Me.btnRestore.Top)
    End Sub

    Private Sub btnBack_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBack.SizeChanged, btnImportIcon.SizeChanged, btnExportIcon.SizeChanged, btnDeleteIcon.SizeChanged, btnSelectAll.SizeChanged
        Me.btnImportIcon.Location = New Point(Me.btnBack.Right + 2, Me.btnImportIcon.Top)
        Me.btnExportIcon.Location = New Point(Me.btnImportIcon.Right + 2, Me.btnExportIcon.Top)
        Me.btnDeleteIcon.Location = New Point(Me.btnExportIcon.Right + 2, Me.btnDeleteIcon.Top)
        Me.btnSelectAll.Location = New Point(Me.btnDeleteIcon.Right + 2, Me.btnSelectAll.Top)
    End Sub

    Private Sub btnRestore_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRestore.Click
        Dim dlg As New OpenFileDialog
        dlg.Multiselect = False
        dlg.InitialDirectory = Me.mEmoticonHelper.BackDBPath
        dlg.Filter = Me.Language.GetString("Common.File") & "(*.tmp)|*.tmp"
        If dlg.ShowDialog = Windows.Forms.DialogResult.OK Then
            Me.DoRestore(dlg.FileName)
        End If
        'Me.cmsRestore.Show(Me.btnRestore, 0, Me.btnRestore.Height + 3)
    End Sub

    Private Sub cmsRestore_Opening(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles cmsRestore.Opening
        Dim strFiles() As String = Directory.GetFiles(Me.mEmoticonHelper.BackDBPath, "*.tmp", SearchOption.TopDirectoryOnly)
        Me.cmsRestore.Items.Clear()
        Try
            Me.SortPath(strFiles)
            Dim index As Integer = 0
            For Each Item As String In strFiles
                Dim menu As New ToolStripMenuItem
                menu.Text = Path.GetFileNameWithoutExtension(Item).Replace("_", "-")
                menu.Tag = Item
                AddHandler menu.Click, AddressOf OnMenuClick
                Me.cmsRestore.Items.Add(menu)
                index += 1
                If index > 10 Then
                    Exit For
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmWeixinEmoticon_cmsRestore_Opening")
        End Try
    End Sub

    Private Sub SortPath(ByRef strFiles() As String)
        If strFiles Is Nothing OrElse strFiles.Length <= 1 Then
            Return
        End If
        Dim blnChanged As Boolean = False
        Try
            For index As Integer = 1 To strFiles.Length - 1
                Dim strTemp As String = ""
                If String.Compare(strFiles(index - 1), strFiles(index)) < 0 Then
                    strTemp = strFiles(index - 1)
                    strFiles(index - 1) = strFiles(index)
                    strFiles(index) = strTemp
                    blnChanged = True
                End If
            Next

            If blnChanged Then
                Me.SortPath(strFiles)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmWeixin_SortPath")
        End Try
    End Sub

    Private Sub DoRestore(ByVal strFile As String)
        Me.SetEnable(False)
        Me.mEmoticonHelper.RestoreDB(strFile)
    End Sub

    Private Sub OnMenuClick(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim menu As ToolStripMenuItem = sender
        If menu.Tag IsNot Nothing AndAlso TypeOf menu.Tag Is String Then
            Me.DoRestore(menu.Tag)
        End If
    End Sub

    Private Sub lblHelper_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblHelper.Click
        Common.OpenExplorer("http://news.tongbu.com/77087.html")
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        If Not Me.chkCheckCloseWeixin.Checked Then
            Me.picError.Visible = True
            Return
        End If
        Me.Loaded(WeixinState.Succeed)
    End Sub

    Private Sub chkCheckCloseWeixin_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles chkCheckCloseWeixin.CheckedChanged
        If Me.chkCheckCloseWeixin.Checked Then
            Me.picError.Visible = False
        End If
    End Sub

#End Region

#Region "---  ListView 操作  ---"

    Private Function CreateListView(ByVal imlLarge As ImageList, Optional ByVal blnLabelEdit As Boolean = True) As tbListViewEx
        Dim lvw As New tbListViewEx

        With lvw
            .Items.Clear()
            .Columns.Clear()
            .View = View.LargeIcon
            .GridLines = False
            .FullRowSelect = True
            .HideSelection = False
            .MultiSelect = True
            .LabelEdit = blnLabelEdit
            .LargeImageList = imlLarge
            .Dock = DockStyle.Fill
            .BorderStyle = BorderStyle.None
            .AllowDrop = True
            .BackColor = Color.FromArgb(250, 251, 252)
            .FullRowSelect = True
            .ShowItemToolTips = True
            .OwnerDraw = True
            .Tag = True
            .MoveFoces = False

            .Columns.Add("colName", Me.Language.GetString("Common.Label.Name"), 220, HorizontalAlignment.Left, -1) '"名称"
        End With

        RemoveHandler lvw.AfterLabelEdit, AddressOf lvw_AfterLabelEdit
        RemoveHandler lvw.MouseDoubleClick, AddressOf lvw_DoubleClick
        RemoveHandler lvw.DragDrop, AddressOf lvw_DragDrop
        RemoveHandler lvw.DragEnter, AddressOf lvw_DragEnter
        RemoveHandler lvw.ItemDrag, AddressOf lvw_ItemDrag
        RemoveHandler lvw.SelectedIndexChanged, AddressOf lvw_SelectedIndexChanged
        RemoveHandler lvw.BeforeLabelEdit, AddressOf lvw_BeforeLabelEdit


        'If Me.mPhotoFormType = AndroidPhotoFormType.Photo Then
        RemoveHandler lvw.DrawItem, AddressOf lvw_DrawItem
        AddHandler lvw.DrawItem, AddressOf lvw_DrawItem
        RemoveHandler lvw.MouseMove, AddressOf lvw_MouseMove
        AddHandler lvw.MouseMove, AddressOf lvw_MouseMove
        RemoveHandler lvw.MouseClick, AddressOf lvw_MouseClick
        AddHandler lvw.MouseClick, AddressOf lvw_MouseClick

        AddHandler lvw.AfterLabelEdit, AddressOf lvw_AfterLabelEdit
        AddHandler lvw.MouseDoubleClick, AddressOf lvw_DoubleClick
        AddHandler lvw.DragDrop, AddressOf lvw_DragDrop
        AddHandler lvw.DragEnter, AddressOf lvw_DragEnter
        AddHandler lvw.ItemDrag, AddressOf lvw_ItemDrag
        AddHandler lvw.SelectedIndexChanged, AddressOf lvw_SelectedIndexChanged
        AddHandler lvw.BeforeLabelEdit, AddressOf lvw_BeforeLabelEdit

        Return lvw
    End Function

    Private Delegate Sub DrawItemDeletegate(ByVal sender As Object, ByVal e As DrawListViewItemEventArgs)
    Private Sub lvw_DrawItem(ByVal sender As Object, ByVal e As DrawListViewItemEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New DrawItemDeletegate(AddressOf lvw_DrawItem), sender, e)
        Else
            DrawItemInvoke(sender, e)
        End If
    End Sub

    Private Sub AddResources2Dictionary()
        If Me.mDictResources Is Nothing Then
            Me.mDictResources = New Dictionary(Of String, Image)(StringComparer.InvariantCultureIgnoreCase)
        End If

        If Me.mDictResources.Count = 0 Then
            With Me.mDictResources
                '.Add("file_pic_large", My.Resources.file_pic_large)
                .Add("btn_photo_export_one", My.Resources.btn_photo_export_one)
                .Add("btn_photo_delete_one", My.Resources.btn_photo_delete_one)
                .Add("btn_photo_share_one", My.Resources.btn_photo_share_one)
                .Add("photo_toolbar_bg", My.Resources.photo_toolbar_bg)
                .Add("photo_chk_false", My.Resources.photo_chk_false)
                .Add("photo_chk_true", My.Resources.photo_chk_true)
                '.Add("gif_loading_24", My.Resources.gif_loading_24)
                '.Add("wallpaper_bg", My.Resources.wallpaper_bg)
                .Add("wallpaper_default_ipad", Utility.GetThumbnail(My.Resources.wallpaper_default_ipad, New Size(152, 152), True, False))
                '.Add("tvw_wallpaper_2", My.Resources.tvw_wallpaper_2)
                '.Add("tvw_camera_2", My.Resources.tvw_camera_2)
                '.Add("tvw_photolib_2", My.Resources.tvw_photolib_2)
                '.Add("tvw_video_2", My.Resources.tvw_video_2)
                '.Add("file_btn_myfavorite", My.Resources.file_btn_myfavorite)
                '.Add("file_btn_2_myfavorite", My.Resources.file_btn_2_myfavorite)
                '.Add("file_fileempty", My.Resources.file_fileempty)
                .Add("btn_editor_1", My.Resources.btn_editor_1)
            End With
        End If
    End Sub

    Private Sub DrawItemInvoke(ByVal sender As Object, ByVal e As DrawListViewItemEventArgs)
        Try
            Dim img As Image = Nothing

            If Me.mDictThumbnail.ContainsKey(e.Item.Name) Then
                img = Me.mDictThumbnail(e.Item.Name)
            End If

            If img Is Nothing Then
                img = Utility.GetThumbnail(Me.mDictResources("wallpaper_default_ipad"), Me.mGroupIconSize)
            End If

            e.Item.ImageKey = String.Empty
            If img IsNot Nothing Then
                Dim intBtnTopDiff As Integer = 24
                Dim intBtnLeftDiff As Integer = 10

                Dim rect As New Rectangle(e.Bounds.X + (e.Bounds.Width - Me.mGroupIconSize.Width) / 2, e.Bounds.Y + 5, Me.mGroupIconSize.Width, Me.mGroupIconSize.Height)

                Dim rectImage As New Rectangle(e.Bounds.X + (e.Bounds.Width - img.Width) / 2, e.Bounds.Y + 5 + (Me.mGroupIconSize.Height - img.Height) / 2, img.Width, img.Height)
                Dim rectBolder As New Rectangle(rect.X - 1, rect.Y - 1, Me.mGroupIconSize.Width + 2, Me.mGroupIconSize.Height + 2)
                Dim rectBackground As New Rectangle(rectBolder.X + 1, rectBolder.Y + 1, rectBolder.Width - 1, rectBolder.Height)

                Dim rectDraw As New Rectangle(rectImage.X + (rectImage.Width - img.Width) \ 2, rectImage.Y + (rectImage.Height - img.Height) \ 2, img.Width, img.Height)

                e.Graphics.FillRectangle(Brushes.White, rectBolder)
                e.Graphics.DrawImage(img, rectImage, New Rectangle(0, 0, img.Width, img.Height), GraphicsUnit.Pixel)


                Dim rectCheckPic As New Rectangle(rectBolder.Right - intBtnTopDiff, rectBolder.Bottom - intBtnTopDiff + 1, _
                                                  Me.mDictResources("photo_chk_true").Width, Me.mDictResources("photo_chk_true").Height)

                If Me.mCurrentHoverItem Is e.Item OrElse e.Item.Selected Then
                    If e.Item.ListView Is Me.mlvwItems Then
                        mrectExportOne = New Rectangle(rectBolder.X + intBtnLeftDiff, rectBolder.Bottom - intBtnTopDiff, _
                                                                     0, Me.mDictResources("btn_editor_1").Height)
                        mrectDeleteOne = New Rectangle(rectBolder.X + intBtnLeftDiff, rectBolder.Bottom - intBtnTopDiff, _
                                                  Me.mDictResources("btn_photo_share_one").Width, Me.mDictResources("btn_photo_share_one").Height)
                    Else
                        mrectExportOne = New Rectangle(rectBolder.X + intBtnLeftDiff, rectBolder.Bottom - intBtnTopDiff, _
                                                                      Me.mDictResources("btn_editor_1").Width, Me.mDictResources("btn_editor_1").Height)

                        mrectDeleteOne = New Rectangle(mrectExportOne.Right + intBtnLeftDiff, rectBolder.Bottom - intBtnTopDiff, _
                                                       Me.mDictResources("btn_photo_share_one").Width, Me.mDictResources("btn_photo_share_one").Height)
                    End If


                    mrectToolBar = New Rectangle(rectBolder.X + 1, rectBolder.Bottom - 30, rectBolder.Width - 1, 30)

                    e.Graphics.DrawImage(Me.mDictResources("photo_toolbar_bg"), mrectToolBar, _
                                         New Rectangle(0, 0, Me.mDictResources("photo_toolbar_bg").Width, Me.mDictResources("photo_toolbar_bg").Height), GraphicsUnit.Pixel)
                    If e.Item.Name <> "custom_emoticon_pid" Then
                        e.Graphics.DrawImage(Me.mDictResources("btn_editor_1"), mrectExportOne, _
                                             New Rectangle(0, 0, Me.mDictResources("btn_editor_1").Width, Me.mDictResources("btn_editor_1").Height), GraphicsUnit.Pixel)


                        e.Graphics.DrawImage(Me.mDictResources("btn_photo_delete_one"), mrectDeleteOne, _
                                             New Rectangle(0, 0, Me.mDictResources("btn_photo_delete_one").Width, Me.mDictResources("btn_photo_delete_one").Height), GraphicsUnit.Pixel)
                    End If



                    If Not e.Item.Selected Then
                        e.Graphics.DrawImage(Me.mDictResources("photo_chk_false"), rectCheckPic, _
                                             New Rectangle(0, 0, Me.mDictResources("photo_chk_false").Width, Me.mDictResources("photo_chk_false").Height), GraphicsUnit.Pixel)
                    End If
                End If

                If e.Item.Selected Then
                    e.Graphics.DrawRectangle(mPenSelectedListViewItem, rectBolder)
                    e.Graphics.DrawImage(Me.mDictResources("photo_chk_true"), rectCheckPic, _
                                         New Rectangle(0, 0, Me.mDictResources("photo_chk_true").Width, Me.mDictResources("photo_chk_true").Height), GraphicsUnit.Pixel)
                Else
                    e.Graphics.DrawRectangle(mPenUnSelectedListViewItem, rectBolder)
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmWeixinEmoticon_paint")
        End Try
    End Sub

    Public Sub lvw_BeforeLabelEdit(ByVal sender As Object, ByVal e As System.Windows.Forms.LabelEditEventArgs)
        'Dim lvw As ListView = sender
        'Dim item As ListViewItem = lvw.FocusedItem
        'Dim em As Emoticon = Nothing
        'If item IsNot Nothing AndAlso item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is Emoticon Then
        '    em = item.Tag
        '    If em.EmoticonPackageId = "custom_emoticon_pid" Then
        '        e.CancelEdit = True
        '    End If
        'End If
    End Sub

    Private Function CheckIsDrfaultItem(Optional ByVal item As ListViewItem = Nothing) As Boolean
        Dim blnReturn As Boolean = False
        Dim currentItem As ListViewItem = item
        Dim em As Emoticon = Nothing
        If currentItem Is Nothing Then
            currentItem = Me.mCurrentHoverItem
        End If
        If currentItem IsNot Nothing AndAlso currentItem.Tag IsNot Nothing AndAlso TypeOf currentItem.Tag Is Emoticon Then
            em = currentItem.Tag
            If em.EmoticonPackageId = "custom_emoticon_pid" Then
                blnReturn = True
            End If
        End If
        Return blnReturn
    End Function

    Private Sub lvw_AfterLabelEdit(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LabelEditEventArgs)
        If e.Label Is Nothing OrElse e.Label.Trim.Length <= 0 Then
            e.CancelEdit = True
            Return
        End If
        Dim lvw As ListView = sender
        Dim item As ListViewItem = lvw.FocusedItem
        Dim strNewValue As String = e.Label & ""

        If strNewValue = lvw.FocusedItem.Text Then
            e.CancelEdit = True
            Return
        End If

        If strNewValue.Length > 100 Then
            strNewValue = e.Label.Substring(0, 100)
        End If

        Dim em As Emoticon = Nothing
        If item IsNot Nothing AndAlso item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is Emoticon Then
            em = item.Tag
            em.EmoticonPackageName = strNewValue
            item.Text = strNewValue
            Me.mEmoticonHelper.UpdateGroups(em)
            Me.Sync()
        End If
    End Sub

    Private Sub lvw_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.SetCount()
        Me.SetContralsEnable()
    End Sub

    Private Sub SetContralsEnable(Optional ByVal item As ListViewItem = Nothing)
        Dim lvw As ListView = Me.GetCurrentListView
        If lvw Is Nothing Then
            Return
        End If

        If Me.mStatus = WeixinState.UpdateGroup AndAlso lvw.SelectedItems.Count = 1 AndAlso Me.CheckIsDrfaultItem(item) Then
            Me.btnDeleteGroup.Enabled = False
            Me.btnDeleteIcon.Enabled = False
        ElseIf lvw.SelectedItems.Count = 0 Then
            Me.btnDeleteGroup.Enabled = False
            Me.btnDeleteIcon.Enabled = False
        Else
            Me.btnDeleteGroup.Enabled = True
            Me.btnDeleteIcon.Enabled = True
        End If
    End Sub

    Private Sub SetCount()
        Dim lvw As ListView = Me.GetCurrentListView
        If lvw Is Nothing OrElse (Me.mStatus <> WeixinState.UpdateGroup AndAlso Me.mStatus <> WeixinState.UpdateIcon) Then
            Return
        End If
        Dim count As Integer = lvw.Items.Count
        Dim selectCount As Integer = lvw.SelectedItems.Count
        If lvw Is Me.mlvwGroups Then
            If selectCount > 0 Then
                'Dim item As ListViewItem = lvw.SelectedItems(0)
                'If item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is Emoticon Then
                '    Dim emitem As Emoticon = item.Tag
                '    If emitem.Items IsNot Nothing AndAlso emitem.Items.Count > 0 Then
                '        Me.lblState.Text = String.Format(Me.Language.GetString("Weixin.Message.EmoticonCount"), emitem.EmoticonPackageName, emitem.Items.Count) '"{0}:{1}个表情"
                '        Return
                '    End If
                'End If
                Me.lblState.Text = String.Format(Me.Language.GetString("Weixin.Message.SelectEmoticonGroup"), selectCount)      '"已选 {0} 个分组"
            Else
                Me.lblState.Text = String.Format(Me.Language.GetString("Weixin.Message.EmoticonTotal"), count)                  '"共有 {0} 个分组"
            End If
        Else
            If selectCount > 0 Then
                Me.lblState.Text = String.Format(Me.Language.GetString("Weixin.Message.SelectEmoticonItem"), selectCount)       '"已选 {0} 个表情"
            Else
                Me.lblState.Text = String.Format(Me.Language.GetString("Weixin.Message.EmoticonItemTotal"), count)              '"共有 {0} 个表情"
            End If
        End If
    End Sub

    Private Sub lvw_DragDrop(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DragEventArgs)
        Dim objFiles As Object = e.Data.GetData(DataFormats.FileDrop)
        '系统产生的拖拽事件，objFiles类型为Array，其它则为控件本身
        If Not (TypeOf objFiles Is Array) Then
            Return
        End If

        Dim strFiles As String() = objFiles

        If Me.mStatus = WeixinState.UpdateGroup Then
            Me.DoImportGroup(strFiles)

        ElseIf Me.mStatus = WeixinState.UpdateIcon Then
            Dim lstImg As New List(Of String)

            For Each Item As String In strFiles
                If Me.CheckIsImage(Item) Then
                    lstImg.Add(Item)
                ElseIf Directory.Exists(Item) Then
                    For Each pathImage As String In Directory.GetFiles(Item, "*.*", SearchOption.AllDirectories)
                        If Me.CheckIsImage(pathImage) Then
                            lstImg.Add(pathImage)
                            Application.DoEvents()
                        End If
                    Next

                End If
            Next
            Me.AddItems(Me.mCurrentEmoticon, lstImg.ToArray)
        End If
    End Sub

    Private Sub lvw_DragEnter(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DragEventArgs)
        If e.Data.GetDataPresent("FileDrop") OrElse e.Data.GetDataPresent("FilenameW") Then
            e.Effect = DragDropEffects.Copy
        End If
    End Sub

    Private Sub lvw_ItemDrag(ByVal sender As System.Object, ByVal e As System.Windows.Forms.ItemDragEventArgs)
        Dim lvw_files As ListView = sender

        '取得当前拖拽的 Item
        Dim listItem As List(Of ListViewItem) = New List(Of ListViewItem)
        For Each Item As ListViewItem In lvw_files.SelectedItems
            listItem.Add(Item)
        Next

        If listItem.Count > 0 Then
            Try
                'listItem的类似必须为List，否则会触发控件内拖拽放开接收事件
                mDataObject = New DataObject("FileDrop", listItem)
                Me.DoDragDrop(mDataObject, DragDropEffects.Copy)
            Catch ex As Exception
            End Try
        End If
    End Sub

    Protected Overrides Sub OnActiveApp()
        Try
            If Me.mDataObject Is Nothing Then
                Return
            End If

            Dim listFiles As List(Of ListViewItem) = Me.mDataObject.GetData("FileDrop", True)
            Me.mDataObject = Nothing

            Dim strDir As String = DragDropHelper.GetCurrentWindowsExplorerPath()
            If Not Directory.Exists(strDir) Then
                Return
            End If

            If Me.mStatus = WeixinState.UpdateGroup Then
                Me.DoExportGroup(strDir)
            ElseIf Me.mStatus = WeixinState.UpdateIcon Then
                Me.DoExportIcon(strDir)
            End If

            'Dim listText As New List(Of String)
            'For Each strFile As String In ListViewItem
            '    listText.Add(Common.ReplaceWinIllegalName(Path.GetFileName(strFile)))
            'Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmFile.OnActiveApp")
        End Try
    End Sub

    Private Sub lvw_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs)

    End Sub

    Private Sub lvw_DoubleClick(ByVal sender As System.Object, ByVal e As MouseEventArgs)
        Dim hitTest = Me.mlvwGroups.HitTest(e.X, e.Y)
        If hitTest Is Nothing AndAlso hitTest.Item Is Nothing Then
            Return
        End If
        If Me.mStatus = WeixinState.UpdateGroup Then
            Dim item As ListViewItem = hitTest.item
            Me.mlvwItems.Items.Clear()
            Me.SetPanelStatus(WeixinState.UpdateIcon)
            Me.LoadEmoticonItem(item)
            Me.SetCount()
            Me.SetContralsEnable()
        Else

        End If
    End Sub

    Private Sub lvw_MouseMove(ByVal sender As System.Object, ByVal e As MouseEventArgs)
        Try
            Dim lvw As tbListViewEx = Me.GetCurrentListView()
            Dim item As ListViewItem = lvw.GetItemAt(e.X, e.Y)
            If item IsNot Nothing Then
                mCurrentHoverItem = item
                lvw.mrectDeleteOne = mrectDeleteOne
                lvw.mrectExportOne = mrectExportOne
                lvw.Invalidate()
                If Me.mrectExportOne.Contains(e.Location) OrElse Me.mrectDeleteOne.Contains(e.Location) Then
                    Me.Cursor = Cursors.Hand
                Else
                    Me.Cursor = Cursors.Arrow
                End If

            Else
                Me.Cursor = Cursors.Arrow
                Me.mCurrentHoverItem = Nothing
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
    End Sub

    Private Sub lvw_MouseClick(ByVal sender As System.Object, ByVal e As MouseEventArgs)
        Try
            If mCurrentHoverItem IsNot Nothing Then
                If mrectExportOne.Contains(e.Location) Then
                    If Me.CheckIsDrfaultItem Then
                        Return
                    End If
                    Dim arrSelectedItems As List(Of ListViewItem) = New List(Of ListViewItem)()
                    arrSelectedItems.Add(mCurrentHoverItem)
                    'TODOExport
                    Me.mCurrentHoverItem.BeginEdit()
                    If mCurrentHoverItem IsNot Nothing Then
                        mCurrentHoverItem.Selected = False
                    End If
                    Me.mrectExportOne = Nothing
                ElseIf mrectDeleteOne.Contains(e.Location) Then
                    If Me.CheckIsDrfaultItem Then
                        Return
                    End If

                    Dim arrSelectedItems As List(Of ListViewItem) = New List(Of ListViewItem)()
                    arrSelectedItems.Add(mCurrentHoverItem)
                    'TODODelete
                    If Me.mStatus = WeixinState.UpdateGroup Then
                        Me.DeleteGroup()
                    Else
                        Me.DeleteItem()
                    End If

                    If mCurrentHoverItem IsNot Nothing Then
                        mCurrentHoverItem.Selected = False
                    End If
                    Me.mrectDeleteOne = Nothing

                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try

    End Sub

    Protected Function CreateListViewItem(ByVal emoticonItem As Emoticon) As ListViewItem  'AndroidListViewItem
        Dim item As New ListViewItem()
        With item
            .Name = emoticonItem.EmoticonPackageId
            .Text = emoticonItem.EmoticonPackageName

            If emoticonItem.Icon IsNot Nothing Then
                If Not Me.mDictThumbnail.ContainsKey(emoticonItem.EmoticonPackageId) Then
                    Me.mDictThumbnail.Add(emoticonItem.EmoticonPackageId, emoticonItem.Icon)
                End If
                .ImageKey = emoticonItem.EmoticonPackageId
            Else
                .ImageKey = "Default"
            End If
            item.Tag = emoticonItem
        End With
        Return item
    End Function

    Protected Function CreateListViewItem(ByVal eItem As EmoticonItem) As ListViewItem  'AndroidListViewItem
        Dim item As New ListViewItem()
        With item
            .Name = eItem.MD5
            .Text = ""
            If eItem.Icon IsNot Nothing Then
                If Not Me.mDictThumbnail.ContainsKey(eItem.MD5) Then
                    Me.mDictThumbnail.Add(eItem.MD5, eItem.Icon)
                End If
                .ImageKey = eItem.MD5
            Else
                .ImageKey = "Default"
            End If
            item.Tag = eItem
        End With
        Return item
    End Function

#End Region

#Region "---  获取表情,新增，修改，删除  ---"

    Private Delegate Sub LoadEmoticonHandler()
    Private Sub LoadEmoticon()
        If Me.InvokeRequired Then
            Me.Invoke(New LoadEmoticonHandler(AddressOf LoadEmoticon))
        Else
            If Me.mEmoticonHelper IsNot Nothing AndAlso Me.mEmoticonHelper.Emoticons IsNot Nothing AndAlso Me.mEmoticonHelper.Emoticons.Count > 0 Then
                For Each Item As ListViewItem In Me.mlvwGroups.Items
                    If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is Emoticon Then
                        WebSiteHelperBase.InstanceBase().SetWebAppMarketStatus(Me.wbsSite, "setEnable", Me.ReplaceWinIllegalName(Item.Tag.EmoticonPackageName), "")
                    End If
                Next
            End If
            'Loading 修改状态
            Me.SetPanelStatus(WeixinState.Loading)
            Me.mlvwGroups.Items.Clear()
            Me.mEmoticonHelper.Reload()
        End If
    End Sub

    Private Sub ShowGroups()
        For Each Item As Emoticon In Me.mEmoticonHelper.Emoticons.Values
            Dim lvwItem As ListViewItem = Me.CreateListViewItem(Item)
            Me.mlvwGroups.Items.Add(lvwItem)
        Next
    End Sub

    Private Sub LoadEmoticonItem(ByVal lvwItem As ListViewItem)
        Me.mlvwItems.Items.Clear()
        If lvwItem IsNot Nothing AndAlso lvwItem.Tag IsNot Nothing AndAlso TypeOf lvwItem.Tag Is Emoticon Then
            Dim emo As Emoticon = lvwItem.Tag
            Me.mCurrentEmoticon = emo
            Me.mCurrentListViewItem = lvwItem
            If emo.Items Is Nothing OrElse emo.Items.Count <= 0 Then
                Return
            End If
            For Each Item As EmoticonItem In emo.Items
                Dim itememo As ListViewItem = Me.CreateListViewItem(Item)
                Me.mlvwItems.Items.Add(itememo)
            Next
        End If

        'Try
        '    If Me.mthrLoadItemIcon IsNot Nothing AndAlso Me.mthrLoadItemIcon.ThreadState <> ThreadState.Stopped Then
        '        Me.mthrLoadItemIcon.Abort()
        '    End If
        '    Me.mthrLoadItemIcon = New Thread(AddressOf LoadEmoticonItemIcon)
        '    Me.mthrLoadItemIcon.IsBackground = True
        '    Me.mthrLoadItemIcon.Start()
        'Catch ex As Exception
        'End Try

        Me.LoadEmoticonItemIcon()
    End Sub

    Private Sub LoadEmoticonItemIcon()
        Try
            For Each Item As ListViewItem In Me.mlvwItems.Items
                If Item.Tag Is Nothing OrElse Not TypeOf Item.Tag Is EmoticonItem Then
                    Continue For
                End If

                Dim itememo As EmoticonItem = Item.Tag
                If Me.mDictThumbnail.ContainsKey(itememo.MD5) Then
                    Continue For
                End If

                If itememo.CreateIconForView(Me.mDevice, Me.mEmoticonHelper.AfcHandler) Then
                    Me.mDictThumbnail.Add(itememo.MD5, itememo.Icon)
                    Item.ImageKey = itememo.MD5
                Else
                    Item.ImageKey = "Default"
                End If
                Application.DoEvents()
            Next

            Me.mlvwItems.Refresh()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWeixinEmo_LoadEmoticonItemIcon")
        End Try
    End Sub

    Private Sub AddItems(ByVal emoticon As Emoticon, ByVal strFiles As String())
        Dim strThumbPath As String = Me.GetDefaultIconPath(emoticon.EmoticonPackageName, strFiles)

        Dim list As List(Of EmoticonItem) = Me.mEmoticonHelper.AddItems(emoticon, strFiles, strThumbPath)

        For Each item As EmoticonItem In list
            Me.mlvwItems.Items.Add(Me.CreateListViewItem(item))
        Next

        If Me.mDictThumbnail.ContainsKey(emoticon.EmoticonPackageId) Then
            Me.mDictThumbnail(emoticon.EmoticonPackageId) = emoticon.Icon

        Else
            Me.mDictThumbnail.Add(emoticon.EmoticonPackageId, emoticon.Icon)
            Me.mCurrentListViewItem = Me.GetCurrentListViewItem(emoticon)
            If Me.mCurrentListViewItem IsNot Nothing Then
                Me.mCurrentListViewItem.ImageKey = emoticon.EmoticonPackageId
                Me.mlvwGroups.Refresh()
            End If
        End If

        'emoticon.EmoticonPackageId = "com.tencent.xin.emoticon.tusiji"
        'emoticon.EmoticonPackageName = "兔斯基"
        'emoticon.EmoticonPackageIconUrl = String.Empty
        'emoticon.EmoticonPackagePannelUrl = String.Empty

        'WebSiteHelper.Instance().SetWebAppMarketStatus(Me.wbsSite, "importFinish", Me.ReplaceWinIllegalName(emoticon.EmoticonPackageName), "")
        Me.SetMessage(Me.Language.GetString("RingtoneMaker.Message.ImportSucceed"))
        Me.Sync()
    End Sub

    Private Function GetDefaultIconPath(ByVal strName As String, ByVal strFiles As String()) As String
        Dim strPath As String = ""
        Try
            If strFiles.Length > 0 Then
                strPath = strFiles(0)
            End If
            Dim img As Image = Nothing
            If Me.mDictThumbUrl.ContainsKey(strName) Then
                img = Common.DownloadImage(Me.mDictThumbUrl(strName))
            End If
            If img IsNot Nothing Then
                Dim pathTmp As String = Path.Combine(Folder.TempFolder, "EmotionThumb")
                Folder.CheckFolder(pathTmp)
                pathTmp = Path.Combine(pathTmp, Guid.NewGuid.ToString("N") & ".png")
                img.Save(pathTmp, Imaging.ImageFormat.Png)
                If File.Exists(pathTmp) Then
                    strPath = pathTmp
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmEmotion_GetDefaultIconPath")
        End Try

        Return strPath
    End Function

    Private Function AddGroup(ByVal strName As String, Optional ByVal strThumbURL As String = "") As Emoticon
        If String.IsNullOrEmpty(strThumbURL) AndAlso Me.mDictThumbUrl.ContainsKey(strName) Then
            strThumbURL = Me.mDictThumbUrl(strName)
        End If
        Dim em As Emoticon = Me.mEmoticonHelper.CreateGroup(strName, strThumbURL)
        em.Icon = Me.mEmoticonHelper.CreateGroupIconShow(em)

        If em.EmotionOp = EmotionOp.Insert Then
            Dim item As ListViewItem = Me.CreateListViewItem(em)
            Me.mlvwGroups.Items.Add(item)
        End If

        Return em
    End Function

    Private Sub DeleteGroup()
        If Me.mlvwGroups.Items.Count <= 0 OrElse Me.mlvwGroups.SelectedItems.Count <= 0 Then
            '"请选择要删除的分组！"
            tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.SelectEmotionDelete"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        Else
            '"您确定要删除选中的分组？"
            If tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.IfDelectSelectEmotion"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) <> Windows.Forms.DialogResult.OK Then
                Return
            End If
        End If

        Dim lstEm As New List(Of Emoticon)
        Dim lstItem As New List(Of ListViewItem)
        For Each Item As ListViewItem In Me.mlvwGroups.SelectedItems
            If Not Me.CheckIsDrfaultItem(Item) AndAlso Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is Emoticon Then
                lstEm.Add(Item.Tag)
                lstItem.Add(Item)
            End If
        Next

        Dim index As Integer = 0
        For Each Item As ListViewItem In lstItem
            Me.mlvwGroups.Items.Remove(Item)
            If index Mod 20 = 0 Then
                Application.DoEvents()
            End If
        Next

        Me.mEmoticonHelper.DeleteGroups(lstEm)

        For Each Item As Emoticon In lstEm
            WebSiteHelperBase.InstanceBase().SetWebAppMarketStatus(Me.wbsSite, "setEnable", Me.ReplaceWinIllegalName(Item.EmoticonPackageName), "")
        Next
        Me.SetCount()
        Me.SetContralsEnable()
        Me.Sync()
    End Sub

    Private Sub DeleteItem()
        If Me.mlvwItems.Items.Count <= 0 OrElse Me.mlvwItems.SelectedItems.Count <= 0 Then
            '"请选择要删除的表情！"
            tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.SelectEmotionItemDelete"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Return
        Else
            '"您确定要删除选中的表情？"
            If tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.IfDelectEmotionItem"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) <> Windows.Forms.DialogResult.OK Then
                Return
            End If
        End If

        Dim lstEmItem As New List(Of EmoticonItem)
        Dim lstItem As New List(Of ListViewItem)
        For Each Item As ListViewItem In Me.mlvwItems.SelectedItems
            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is EmoticonItem Then
                lstEmItem.Add(Item.Tag)
                lstItem.Add(Item)
            End If
        Next

        For Each Item As ListViewItem In lstItem
            Me.mlvwItems.Items.Remove(Item)
        Next

        Me.mEmoticonHelper.DeleteItems(Me.mCurrentEmoticon, lstEmItem)

        'If Me.mCurrentEmoticon.Items.Count = 0 Then
        '    Me.mCurrentListViewItem.ImageKey = "Default"

        'ElseIf Me.mCurrentEmoticon.Items.Count < 4 Then
        If Not Me.mDictThumbnail.ContainsKey(Me.mCurrentEmoticon.EmoticonPackageId) Then
            Me.mDictThumbnail.Add(Me.mCurrentEmoticon.EmoticonPackageId, Me.mCurrentEmoticon.Icon)
            Me.mCurrentListViewItem.ImageKey = Me.mCurrentEmoticon.EmoticonPackageId
        Else
            Me.mDictThumbnail(Me.mCurrentEmoticon.EmoticonPackageId) = Me.mCurrentEmoticon.Icon
        End If
        'End If
        Me.Sync()
    End Sub

    Private Sub Export()
        If Me.mlvwGroups.SelectedItems.Count <= 0 Then
            '"请选择导出的分组！"
            tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.SelectEmotionExport"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Dim folderDlg As FolderBrowserDialog = New FolderBrowserDialog()
        If folderDlg.ShowDialog = Windows.Forms.DialogResult.OK Then
            Dim strPath As String = folderDlg.SelectedPath
            Me.DoExportGroup(strPath)
        End If

    End Sub

    Private Sub DoExportGroup(ByVal strPath As String)
        Me.SetEnable(False)
        For Each Item As ListViewItem In Me.mlvwGroups.SelectedItems
            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is Emoticon Then
                Dim em As Emoticon = Item.Tag
                Dim strSavePats As String = Me.GetFolderName(strPath, IIf(em.EmoticonPackageName.Length > 0, em.EmoticonPackageName, em.EmoticonPackageId))
                Folder.CheckFolder(strSavePats)

                Dim index As Integer = 0
                For Each emItem As EmoticonItem In em.Items
                    index += 1
                    Dim picPath As String = Path.Combine(strSavePats, emItem.MD5 & IIf(emItem.Type = 2, ".gif", ".png"))
                    Me.mDevice.DownFromPhone(Me.mEmoticonHelper.AfcHandler, emItem.PicOnPhone, picPath)
                    Dim intPercent As Integer = index * 100 / em.Items.Count
                    Me.lblState.Text = String.Format(Me.Language.GetString("Weixin.Message.ExportMsg"), String.Format("：{0} {1}%", em.EmoticonPackageName, intPercent))
                    Application.DoEvents()
                Next

                'Dim emFile As WeixinEmoticonFile = New WeixinEmoticonFile()
                'Dim grp As New WeixinEmoticonGroup("拍照拼星星")

                'emFile.GroupCollection.Add(grp)
                'emFile.Compress(strSavePats)

            End If
        Next
        Me.lblState.Text = Me.Language.GetString("Note.Message.ExportSucceed")
        Me.SetEnable(True)
    End Sub

    Private Function GetFolderName(ByVal strPath As String, ByVal strName As String) As String
        Dim strReturn As String = Path.Combine(strPath, strName)
        If Directory.Exists(strReturn) Then
            Dim intIndex As Integer = 1
            While True
                strReturn = Path.Combine(strPath, strName & String.Format("({0})", intIndex))
                If Not Directory.Exists(strReturn) Then
                    Exit While
                End If
                intIndex += 1
            End While
        End If
        Return strReturn
    End Function

    Private Sub ExportIcon()
        If Me.mlvwItems.SelectedItems.Count <= 0 Then
            '"请选择导出的表情！"
            tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.SelectEmoticonItemExport"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        'Dim saveDelg As New SaveFileDialog()
        Dim folderDlg As FolderBrowserDialog = New FolderBrowserDialog()
        If folderDlg.ShowDialog = Windows.Forms.DialogResult.OK Then
            Dim strPath As String = folderDlg.SelectedPath
            Me.DoExportIcon(strPath)
        End If
    End Sub

    Private Sub DoExportIcon(ByVal strPath As String)
        Me.SetEnable(False)
        For Each Item As ListViewItem In Me.mlvwGroups.SelectedItems
            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is Emoticon Then

                Dim strSavePats As String = strPath
                Dim index As Integer = 0
                For Each lvwItem As ListViewItem In Me.mlvwItems.SelectedItems
                    If lvwItem.Tag IsNot Nothing AndAlso TypeOf lvwItem.Tag Is EmoticonItem Then
                        index += 1
                        Dim emItem As EmoticonItem = lvwItem.Tag
                        Dim picPath As String = Path.Combine(strSavePats, emItem.MD5 & IIf(emItem.Type = 2, ".gif", ".png"))
                        Me.mDevice.DownFromPhone(Me.mEmoticonHelper.AfcHandler, emItem.PicOnPhone, picPath)
                        Dim intPercent As Integer = index * 100 / Me.mlvwItems.SelectedItems.Count
                        '正在导出{0}...
                        Me.lblState.Text = String.Format(Me.Language.GetString("Weixin.Message.ExportMsg"), String.Format("：{0} {1}%", "", intPercent))
                        Application.DoEvents()
                    End If
                Next

            End If
        Next
        Me.lblState.Text = Me.Language.GetString("Note.Message.ExportSucceed")
        Me.SetEnable(True)
    End Sub

#End Region

#Region "---  下载逻辑  ---"

    Private Sub OnTaskStart(ByVal sender As Object, ByVal e As MultiThreadDownloadItem)
        If e.ItemInfo.Class <> ResourceClass.Emoticon Then
            Return
        End If
    End Sub

    Public Sub OnDownloadItemCallBack(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        '下载兼容表情的微信安装包
        If item.ItemInfo.Class = ResourceClass.Emoticon AndAlso item.ItemInfo.Identifier = Me.mDevice.Identifier Then
            If e.FilePath.ToLower.EndsWith(".eif") OrElse e.FilePath.ToLower.EndsWith(".eip") OrElse e.FilePath.ToLower.EndsWith(".zip") Then
                Me.SetMessage(String.Format(Me.Language.GetString("ImageSearch.Label.Downloading"), Path.GetFileNameWithoutExtension(e.FilePath), ""))
            End If
        ElseIf item.ItemInfo.ItemId = Me.mstrItemId AndAlso item.ItemInfo.Class = ResourceClass.Software Then
            Me.SetInstallStatus(True, String.Format(Me.Language.GetString("ImageSearch.Label.Downloading"), Path.GetFileNameWithoutExtension(e.FilePath), ""), item.DownloadInfo.Progress)
            'Me.SetMessage(String.Format(Me.Language.GetString("ImageSearch.Label.Downloading"), Path.GetFileNameWithoutExtension(e.FilePath), "") & String.Format(" {0}%", item.DownloadInfo.Progress))
        End If
    End Sub

    Public Sub OnIconDownloadedEvent(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)

    End Sub

    Private Sub OnDownloadItemCompleted(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        If item.ItemInfo.Class = ResourceClass.Emoticon AndAlso item.ItemInfo.Identifier = Me.mDevice.Identifier Then

            If e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
                If File.Exists(e.FilePath) AndAlso (e.FilePath.ToLower.EndsWith(".eif") OrElse e.FilePath.ToLower.EndsWith(".eip") OrElse e.FilePath.ToLower.EndsWith(".zip")) Then
                    Me.DoImportEif(e.FilePath)
                End If
            Else
                '下载失败或者取消
                WebSiteHelperBase.InstanceBase().SetWebAppMarketStatus(Me.wbsSite, "setEnable", Path.GetFileNameWithoutExtension(item.DownloadInfo.FilePath), "")
            End If

        ElseIf item.ItemInfo.ItemId = Me.mstrItemId AndAlso item.ItemInfo.Class = ResourceClass.Software Then
            '微信下载完成进度显示
            If e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
                Me.InstallWeixin(item.ItemInfo.FilePath)
            Else

            End If
        End If
    End Sub

    Private Sub OnTaskDelete(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If item.ItemInfo.Status <> TaskState.Completed AndAlso item.ItemInfo.Identifier = Me.mDevice.Identifier AndAlso item.ItemInfo.Class = ResourceClass.Emoticon Then
            WebSiteHelperBase.InstanceBase().SetWebAppMarketStatus(Me.wbsSite, "setEnable", Me.ReplaceWinIllegalName(Path.GetFileNameWithoutExtension(item.ItemInfo.FilePath)), "")
        End If
    End Sub

    Private Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)

    End Sub

    Private Delegate Sub SetMessageHandler(ByVal strMessage As String)
    Private Sub SetMessage(ByVal strMessage As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetMessageHandler(AddressOf SetMessage), strMessage)
        Else
            Me.lblState.Text = strMessage
        End If
    End Sub

#End Region

    Private Delegate Sub SetEnableHandler(ByVal blnEnable As Boolean)
    Private Sub SetEnable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetEnableHandler(AddressOf SetEnable), blnEnable)
        Else
            Try
                Me.pnlControlGroup.Enabled = blnEnable
                Me.pnlControlIcon.Enabled = blnEnable
                Me.pnlGroups.Enabled = blnEnable
                Me.pnlItems.Enabled = blnEnable
                If Me.btn_close IsNot Nothing Then
                    Me.btn_close.Enabled = blnEnable
                End If
                'Me.btnSync.Enabled = blnEnable
                Me.btnRestore.Enabled = blnEnable
            Catch ex As Exception

            End Try
        End If
    End Sub

    Private Delegate Sub SetPanelStatusHandler(ByVal status As WeixinState)
    Private Sub SetPanelStatus(ByVal status As WeixinState)
        If Me.InvokeRequired Then
            Me.Invoke(New SetPanelStatusHandler(AddressOf SetPanelStatus), status)
        Else
            If status <> WeixinState.WebSite AndAlso status <> WeixinState.Guide Then
                Me.mStatus = status
            End If

            Me.pnlMain.Size = New Size(Me.Width - 2, Me.pnlMain.Height)
            Me.pnlMain.Location = New Size(1, 32)
            Me.btn_normal.Visible = True
            Me.btn_minimize.Location = New Point(Me.btn_normal.Left - Me.btn_minimize.Width, Me.btn_minimize.Top)
            Me.CanResize = True

            Me.pnlMain.SuspendLayout()
            Select Case status
                Case WeixinState.NoAccount, WeixinState.NoInstalled, WeixinState.NoDB, WeixinState.DownloadDBFailed, WeixinState.CanNotAccess
                    Me.pnlMain.ColumnStyles(0).SizeType = SizeType.Percent
                    Me.pnlMain.ColumnStyles(0).Width = 100
                    Me.pnlMain.ColumnStyles(1).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(1).Width = 0
                    Me.pnlMain.ColumnStyles(2).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(2).Width = 0
                    Me.pnlMain.ColumnStyles(3).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(3).Width = 0
                    Me.pnlMain.ColumnStyles(4).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(4).Width = 0
                    Me.pnlMain.ColumnStyles(5).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(5).Width = 0
                    Me.pnlMain.ColumnStyles(6).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(6).Width = 0

                    Me.lblDownloadWeixin.Visible = True
                    Me.btnDownloadWeixin.Visible = True

                    Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Lable.NotInstall") '"尚未安装微信"
                    If status = WeixinState.NoAccount Then
                        Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Lable.NotLogin") '"尚未登录微信"
                        Me.lblDownloadWeixin.Visible = False
                        Me.btnDownloadWeixin.Visible = False
                    ElseIf status = WeixinState.NoDB OrElse status = WeixinState.DownloadDBFailed OrElse status = WeixinState.CanNotAccess Then
                        Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Lable.LoadDataFailed") '"获取微信数据失败"
                        Me.lblDownloadWeixin.Visible = False
                        Me.btnDownloadWeixin.Visible = False
                    End If

                Case WeixinState.Loading
                    Me.pnlMain.ColumnStyles(0).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(0).Width = 0
                    Me.pnlMain.ColumnStyles(1).SizeType = SizeType.Percent
                    Me.pnlMain.ColumnStyles(1).Width = 100
                    Me.pnlMain.ColumnStyles(2).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(2).Width = 0
                    Me.pnlMain.ColumnStyles(3).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(3).Width = 0
                    Me.pnlMain.ColumnStyles(4).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(4).Width = 0
                    Me.pnlMain.ColumnStyles(5).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(5).Width = 0
                    Me.pnlMain.ColumnStyles(6).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(6).Width = 0
                Case WeixinState.UpdateGroup
                    Me.pnlMain.ColumnStyles(0).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(0).Width = 0
                    Me.pnlMain.ColumnStyles(1).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(1).Width = 0
                    Me.pnlMain.ColumnStyles(2).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(2).Width = 195
                    Me.pnlMain.ColumnStyles(3).SizeType = SizeType.Percent
                    Me.pnlMain.ColumnStyles(3).Width = 100
                    Me.pnlMain.ColumnStyles(4).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(4).Width = 0
                    Me.pnlMain.ColumnStyles(5).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(5).Width = 0
                    Me.pnlMain.ColumnStyles(6).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(6).Width = 0
                Case WeixinState.UpdateIcon
                    Me.pnlMain.ColumnStyles(0).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(0).Width = 0
                    Me.pnlMain.ColumnStyles(1).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(1).Width = 0
                    Me.pnlMain.ColumnStyles(2).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(2).Width = 195
                    Me.pnlMain.ColumnStyles(3).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(3).Width = 0
                    Me.pnlMain.ColumnStyles(4).SizeType = SizeType.Percent
                    Me.pnlMain.ColumnStyles(4).Width = 100
                    Me.pnlMain.ColumnStyles(5).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(5).Width = 0
                    Me.pnlMain.ColumnStyles(6).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(6).Width = 0
                Case WeixinState.WebSite
                    Me.pnlMain.ColumnStyles(0).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(0).Width = 0
                    Me.pnlMain.ColumnStyles(1).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(1).Width = 0
                    Me.pnlMain.ColumnStyles(2).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(2).Width = 195
                    Me.pnlMain.ColumnStyles(3).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(3).Width = 0
                    Me.pnlMain.ColumnStyles(4).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(4).Width = 0
                    Me.pnlMain.ColumnStyles(5).SizeType = SizeType.Percent
                    Me.pnlMain.ColumnStyles(5).Width = 100
                    Me.pnlMain.ColumnStyles(6).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(6).Width = 0
                Case WeixinState.Guide
                    Me.pnlMain.ColumnStyles(0).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(0).Width = 0
                    Me.pnlMain.ColumnStyles(1).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(1).Width = 0
                    Me.pnlMain.ColumnStyles(2).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(2).Width = 0
                    Me.pnlMain.ColumnStyles(3).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(3).Width = 0
                    Me.pnlMain.ColumnStyles(4).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(4).Width = 0
                    Me.pnlMain.ColumnStyles(5).SizeType = SizeType.Absolute
                    Me.pnlMain.ColumnStyles(5).Width = 0
                    Me.pnlMain.ColumnStyles(6).SizeType = SizeType.Percent
                    Me.pnlMain.ColumnStyles(6).Width = 100

                    '只显示最后一个列的时候左边会出现一个白边，为了去掉这个白边把控件放大两个像素隐藏左边的白边。
                    Me.pnlMain.Size = New Size(Me.Width + 2, Me.pnlMain.Height)
                    Me.pnlMain.Location = New Size(-1, 32)
                    Me.btn_normal.Visible = False
                    Me.btn_minimize.Location = Me.btn_normal.Location
                    Me.CanResize = False

            End Select
            Me.pnlMain.ResumeLayout()
        End If
    End Sub

    Private Function GetCurrentListView() As tbListViewEx
        Select Case Me.mStatus
            Case WeixinState.UpdateGroup
                Return Me.mlvwGroups
            Case Else
                Return Me.mlvwItems
        End Select
        Return Me.mlvwItems
    End Function

    Private Function GetCurrentListViewItem(ByVal emoticon As Emoticon) As ListViewItem
        Dim itemReturn As ListViewItem = Nothing
        For Each Item As ListViewItem In Me.mlvwGroups.Items
            If Item.Tag IsNot Nothing AndAlso Item.Tag Is emoticon Then
                itemReturn = Item
                Exit For
            End If
        Next
        Return itemReturn
    End Function

    'Private Sub btnSync_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Me.Sync()
    'End Sub

    Private Sub Sync()
        Me.SetEnable(False)
        Me.mEmoticonHelper.SyncData()
    End Sub

    'Private Sub btnSync_SizeChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSync.SizeChanged, btnRestore.SizeChanged

    'End Sub

    'Private Sub pnlMain_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pnlMain.SizeChanged
    '    Me.btnRestore.Location = New Point(Me.btnRefreshGroup.Left - Me.btnRestore.Width - 3, Me.btnRestore.Top)
    '    Me.btnSync.Location = New Point(Me.btnRestore.Left - Me.btnSync.Width - 3, Me.btnSync.Top)
    'End Sub

    Private Sub btnSelectAllEmoticon_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSelectAllEmoticon.Click
        Dim intIndex As Integer = 0
        For Each Item As ListViewItem In Me.mlvwGroups.Items
            Item.Selected = True
            intIndex += 1
            If intIndex Mod 50 = 0 Then
                Application.DoEvents()
            End If
        Next
    End Sub

    Private Sub lblState_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblState.TextChanged
        If Me.Parent IsNot Nothing Then
            Me.mApplication.SetLabelText(FunctionKey.Weixin, Me.lblState.Text)
        End If
    End Sub

#Region "---  安装逻辑  ---"

    Private Sub InstallWeixin(ByVal strFilePath As String)
        If Me.mDevice Is Nothing Then
            Return
        End If
        Dim info As PackageInfo = PackageInfoReader.GetPackageInfo(strFilePath)
        Dim isSucceed As Boolean = False
        isSucceed = Me.mDevice.InstallApplication(strFilePath.ToLower, _
                                                  New InstallApplicationEventHandler(AddressOf OnInstallApplication), _
                                                  New DataTransferEventHandler(AddressOf OnDataUpload), _
                                                  Nothing, New DataTransferEventHandler(AddressOf OnRecordBackup), New TalkingStatusEventHandler(AddressOf OnInstallInDevice), info.CrackedInfo <> CrakedInfo.Craked)
        If isSucceed Then
            Me.SetInstallStatus(False, "", 100)
            Me.LoadEmoticon()
        End If
    End Sub

    Private Sub OnInstallApplication(ByVal sender As Object, ByVal e As InstallApplicationEventArgs)
        If e.InstallProgress = InstallapplicationProgress.InstallFailed Then
            '如果是取消安装，不算是错误
            Dim strMsg As String = ""

            Select Case e.LastErrType
                Case InstallFailureType.InstallFailureUnzip
                    strMsg = Me.Language.GetString("App.Message.UnZipFailure")          '"解压失败，该软件包可能已损坏！"
                Case InstallFailureType.InstallFailureFileNoExist
                    strMsg = Me.Language.GetString("App.Message.CanNotFindApp")          '"没有发现 app 程序，不能安装此 pxl！"
                Case InstallFailureType.InstallFailureFileNoMatch
                    strMsg = Me.Language.GetString("App.Message.NoMatchApp")             '"软件包格式不符合"
                Case InstallFailureType.InstallFailureStartServer
                    strMsg = Me.Language.GetString("App.Message.InstallFailure")         '"安装服务启动失败！"
                Case InstallFailureType.InstallFailureUpload
                    strMsg = Me.Language.GetString("App.Message.UploadFailure")          '"连接不稳，请拔插设备后重试！"
                Case Else
                    strMsg = e.LastErrMsg
            End Select
        End If

        If e.InstallProgress <> InstallapplicationProgress.OnTransfer Then
            Dim a As String = ""
        End If
    End Sub

    Private Sub OnDataUpload(ByVal e As DataTransferEventArgs)
        Dim intProgress As Integer = 0
        If e.FileSize > 0 Then
            intProgress = e.TransSize * 100 / e.FileSize
        End If
        'Me.SetMessage(String.Format("正在上传 {0}%", intProgress))
        Me.SetInstallStatus(True, Me.Language.GetString("File.Label.Uploading"), intProgress)
    End Sub

    Private Sub OnRecordBackup(ByVal e As DataTransferEventArgs)
        Dim intProgress As Integer = 0
        If e.FileSize > 0 Then
            intProgress = e.TransSize * 100 / e.FileSize
        End If
        Me.SetInstallStatus(True, Me.Language.GetString("App.Cell.BackupRecord"), intProgress)      '"正在备份记录"
    End Sub

    Private Sub OnInstallInDevice(ByVal e As TalkingStatusEventArgs)
        'Me.SetMessage(String.Format("正在安装...{0}", e.PercentComplete))
        Me.SetInstallStatus(True, Me.Language.GetString("App.Cell.Installing"), e.PercentComplete)
    End Sub

#End Region

    Private Delegate Sub SetInstallStatusHandler(ByVal blnShowStatus As Boolean, ByVal msg As String, ByVal intProgress As Integer)
    Private Sub SetInstallStatus(ByVal blnShowStatus As Boolean, ByVal msg As String, ByVal intProgress As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New SetInstallStatusHandler(AddressOf SetInstallStatus), blnShowStatus, msg, intProgress)
        Else
            If blnShowStatus Then
                Me.pnlMain.RowStyles(2).SizeType = SizeType.Absolute
                Me.pnlMain.RowStyles(2).Height = 30
                Me.pnlMain.RowStyles(1).SizeType = SizeType.Percent
                Me.pnlMain.RowStyles(1).Height = 100

            Else
                Me.pnlMain.RowStyles(2).SizeType = SizeType.Absolute
                Me.pnlMain.RowStyles(2).Height = 0
            End If

            Me.lblMessage.Text = msg
            Me.pbarShowProgress.tbPlayValue = intProgress
            Application.DoEvents()
        End If
    End Sub

    Public Sub SelectWebNode()
        Try
            If Me.mNodeSite IsNot Nothing AndAlso Not Me.tvwEmotion.SelectedNode Is Me.mNodeSite Then
                Me.tvwEmotion.SelectedNode = Me.mNodeSite
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmWeixinEmo_SelectWebNode")
        End Try
    End Sub

    Private Sub btnRefreshGroup_SizeChanged(sender As Object, e As EventArgs) Handles btnRefreshGroup.SizeChanged, lblHelper.SizeChanged
        Me.btnRefreshGroup.Location = New Point(Me.pnlControlGroup.Width - Me.btnRefreshGroup.Width, Me.btnRefreshGroup.Top)
        Me.lblHelper.Location = New Point(Me.btnRefreshGroup.Left - Me.lblHelper.Width - 10, Me.lblHelper.Top)
    End Sub

End Class

Public Class EmoticonComparer
    Implements IComparer(Of Emoticon)

    Public Function Compare(ByVal x As Emoticon, ByVal y As Emoticon) As Integer Implements System.Collections.Generic.IComparer(Of Emoticon).Compare
        Return y.EmoticonPackageInstallTime - x.EmoticonPackageInstallTime
    End Function

End Class

Public Class Emoticon

    Sub New(ByVal strMd5 As String)
        Me.New(strMd5, String.Format("com.tencent.xin.emoticon.{0}", Guid.NewGuid.ToString("N").Substring(0, 8)))
    End Sub

    Sub New(ByVal strMd5 As String, ByVal strId As String)
        Me.SetIcon()
        Me.MD5Myself = strMd5
        Me.EmoticonPackageId = strId
    End Sub

    Private Sub SetIcon()
        If Utility.IsPanda Then
            Me.EmoticonPackageIconUrl = ""
            Me.EmoticonPackagePannelUrl = ""
        End If
    End Sub

    Public MD5Myself As String = ""
    Public EmoticonPackageId As String = ""
    Public EmoticonPackageName As String = ""
    Public EmoticonPackageIconUrl As String = "http://t.tongbu.com/tbzs/WeixinEmoticon.png"
    Public EmoticonPackagePannelUrl As String = "http://t.tongbu.com/tbzs/WeixinEmoticon.png"
    Public EmoticonPackagePStatus As Long = 1
    Public EmoticonPackageDLStatus As Long = 1
    Public EmoticonPackageInstallTime As Long = 0
    Public EmoticonPackageRemoveTime As Long = 0
    Public DefaultIconPath As String = String.Empty

    Public Items As New List(Of EmoticonItem)
    Public Icon As Image = Nothing
    Public EmotionOp As EmotionOp = EmotionOp.None
    Public ContainGroup As List(Of String) = New List(Of String)

    Public ReadOnly Property IconPathForWeiXin() As String
        Get
            Return Path.Combine(Folder.TempFolder, Me.EmoticonPackageId & ".png")
        End Get
    End Property

    Public Function EmoticonPackageThumbPath() As String
        Return String.Format("/Library/WechatPrivate/{0}/emoticon_package/{1}.pic.thumb", Me.MD5Myself, Me.EmoticonPackageId)
    End Function

End Class

Public Class EmoticonItem

    Private mPathOnPhone As String = "/Library/WechatPrivate/emoticon1/"
    Private mSize As New Size(140, 140)

    Public Type As Integer = 2
    Public MD5 As String = String.Empty

    Public ConStrRes2 As String = String.Empty
    Public EmoticonPackageId As String = String.Empty
    Public EmotionOp As EmotionOp = EmotionOp.None
    Public Icon As Image = Nothing

    Public PicOnPCTemp As String = String.Empty
    Public ReadOnly Property PicOnPC() As String
        Get
            Return Path.Combine(Folder.TempFolder, MD5 & ".pic")
        End Get
    End Property

    Public ReadOnly Property PicThumbOnPC() As String
        Get
            Return Me.PicOnPC & ".thumb"
        End Get
    End Property


    Public ReadOnly Property PicOnPhone() As String
        Get
            Return mPathOnPhone & MD5 & ".pic"
        End Get
    End Property

    Public ReadOnly Property PicThumbOnPhone() As String
        Get
            Return PicOnPhone & ".thumb"
        End Get
    End Property

    Public Function CreateIconForView(ByVal dev As iPhoneDevice, ByVal afc As System.IntPtr) As Boolean
        Dim blnReturn As Boolean = False

        Try
            Dim imgTemp As Image = Nothing
            If File.Exists(Me.PicOnPCTemp) Then
                imgTemp = Utility.GetImageFormFile(Me.PicThumbOnPC)

            ElseIf File.Exists(Me.PicThumbOnPC) OrElse dev.DownFromPhone(afc, Me.PicThumbOnPhone, Me.PicThumbOnPC) Then
                imgTemp = Utility.GetImageFormFile(Me.PicThumbOnPC)

            ElseIf File.Exists(Me.PicOnPC) OrElse dev.DownFromPhone(afc, Me.PicOnPhone, Me.PicOnPC) Then
                imgTemp = Utility.GetImageFormFile(Me.PicOnPC)
            End If

            If imgTemp IsNot Nothing Then
                Me.Icon = Utility.GetThumbnail(imgTemp, Me.mSize)
                imgTemp.Dispose()
            Else
                Me.Icon = Utility.GetThumbnail(My.Resources.file_pic_large, Me.mSize)
            End If

            blnReturn = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "EmoticonItem_GreateIconImage")
        End Try

        Return blnReturn
    End Function

End Class

Public Enum EmotionOp
    None
    Insert
    Update
    Delete
End Enum

