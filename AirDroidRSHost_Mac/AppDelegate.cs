﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.ComponentModel;
using System.IO;

using AppKit;
using Foundation;
using ObjCRuntime;
using CoreGraphics;


using iTong.Android;
using iTong.CoreFoundation;
using iTong.CoreModule;

namespace AirDroidRemoteSupportHost
{
	//public static class AEKeyword
	//{
	//	public static readonly uint DirectObject = Create("----");
	//	public static readonly uint ErrorNumber = Create("errn");
	//	public static readonly uint ErrorString = Create("errs");
	//	public static readonly uint ProcessSerialNumber = Create("psn ");
	//	public static readonly uint PreDispatch = Create("phac");
	//	public static readonly uint SelectProc = Create("selh");
	//	public static readonly uint AERecorderCount = Create("recr");
	//	public static readonly uint AEVersion = Create("vers");

	//	private static uint Create(string key)
	//	{
	//		return (((uint)key[0]) << 24 |
	//				((uint)key[1]) << 16 |
	//				((uint)key[2]) << 8 |
	//				((uint)key[3]) << 0);
	//	}
	//}

	public partial class AppDelegate : NSApplicationDelegate
	{

        protected skWindow MainWindow { get; set; }

		public static AppDelegate Instance
		{
			get
			{
				return NSApplication.SharedApplication.Delegate as AppDelegate;
			}
		}

		public void ExitApp()
		{
			NSApplication.SharedApplication.Terminate(this);
		}

		public override void DidFinishLaunching (NSNotification notification)
		{
            //通过WebView的策略控制来实现URL跳转打开

            string[] arrPara = ParaMgr.Arguments;
            if (arrPara == null || arrPara.Length == 0)
                return;

            // 初始化权限状态记录
            InitializePermissionTracking();

            System.Windows.Forms.ExtendControl.IsWindowAnchor = true;

            switch (arrPara[0])
            {
                case ParaMgr.CommandRunWidnows:
                    {
                        rsMainForm view = new rsMainForm();
                        view.Show();

                        //保存MainWindow
                        this.MainWindow = (skWindow)view.Window;

                        break;
                    }

                case ParaMgr.CommandRunSafeMode:
                    {
                        frmSafeMode.Init();
                        break;
                    }
            }
        }

        /// <summary>
        /// 初始化权限跟踪
        /// </summary>
        private void InitializePermissionTracking()
        {
            try
            {
                // 记录应用启动时的权限状态
                bool currentScreenCapture = MyAPI.AuthStatusForScreenCapture;
                bool currentAccessibility = MyAPI.AuthStatusForAccessibility;
                bool currentAllFiles = MyAPI.AuthStatusForAllFiles;

                // 如果是首次启动或权限状态未记录，则初始化
                if (!SettingMgr.HasKey("LastScreenCaptureStatus"))
                {
                    SettingMgr.SetValue("LastScreenCaptureStatus", currentScreenCapture);
                    SettingMgr.SetValue("LastAccessibilityStatus", currentAccessibility);
                    SettingMgr.SetValue("LastAllFilesStatus", currentAllFiles);

                    ServiceMgr.WriteLine($"InitializePermissionTracking -> Initial status: " +
                        $"ScreenCapture: {currentScreenCapture}, " +
                        $"Accessibility: {currentAccessibility}, " +
                        $"AllFiles: {currentAllFiles}");
                }

                // 清除权限请求标志（新启动时重置）
                SettingMgr.SetValue("HasTccRequestScreenCapture", false);
                SettingMgr.SetValue("HasTccRequestAccessibility", false);
                SettingMgr.SetValue("HasTccRequestAllFiles", false);
            }
            catch (Exception ex)
            {
                ServiceMgr.WriteLine($"InitializePermissionTracking -> Error: {ex.Message}");
            }
        }

        /// <summary>
        /// 协议：weixin://, tongbu:// 避免WebKit弹出窗体(需要设置info.plist中的URL types的URL Schemes
        /// </summary>
        /// <param name="descriptor">Descriptor.</param>
        /// <param name="replyEvent">Reply event.</param>
        //[Export("handleGetURLEvent:withReplyEvent:")]
        //private void HandleGetURLEvent(NSAppleEventDescriptor descriptor, NSAppleEventDescriptor replyEvent)
        //{
        //	try
        //	{
        //		string urlStr = descriptor.ParamDescriptorForKeyword(AEKeyword.DirectObject).StringValue;
        //		Console.WriteLine("HandleGetURLEvent={0}", urlStr);

        //		// 处理微信weixin://的协议
        //		ChatWebPage.Navigating(urlStr);
        //	}
        //	catch (Exception ex)
        //	{
        //		Console.WriteLine(ex.ToString());
        //	}
        //}

        /// <summary>
        /// 处理airdroid://协议的事件，需要info.plist配合
        /// </summary>
        /// <param name="obj">Object.</param>
        //[Export("handleUrlEvent:")]
        //private void handleUrlEvent(NSObject obj)
        //{

        //	if (obj != null)
        //		Console.WriteLine("handleUrlEvent: " + obj.ToString());
        //}

        //实现单击窗体的X按钮时，关闭窗体
        //public override bool ApplicationShouldTerminateAfterLastWindowClosed(NSApplication sender)
        //{
        //    ServiceMgr.LaunchctlUnload(ServiceMgr.ServiceNameForRS_Windows);
        //    return false;
        //}

        
        public override void WillTerminate(NSNotification notification)
        {
            // 检测退出原因并相应处理
            ExitReason exitReason = DetectExitReason();

            ServiceMgr.WriteLine($"WillTerminate -> ExitReason: {exitReason}");

            switch (exitReason)
            {
                case ExitReason.UserExit:
                    // 用户主动退出：正常清理
                    ServiceMgr.WriteLine("User initiated exit - performing normal cleanup");
                    ServiceMgr.ExitAppForRS();
                    break;

                case ExitReason.SystemPermissionRestart:
                    // 系统权限重启：保留状态，快速清理
                    ServiceMgr.WriteLine("System permission restart detected - performing minimal cleanup");
                    ServiceMgr.ExitAppForRS(false); // 不等待，快速退出
                    break;

                case ExitReason.SystemForceExit:
                    // 系统强制退出：快速清理
                    ServiceMgr.WriteLine("System force exit detected - performing quick cleanup");
                    ServiceMgr.ExitAppForRS(false);
                    break;

                default:
                    // 未知原因：默认处理
                    ServiceMgr.WriteLine("Unknown exit reason - performing default cleanup");
                    ServiceMgr.ExitAppForRS();
                    break;
            }
        }

        /// <summary>
        /// 检测应用退出原因
        /// </summary>
        /// <returns>退出原因枚举</returns>
        private ExitReason DetectExitReason()
        {
            try
            {
                // 1. 检查是否正在请求权限
                if (IsRequestingPermissions())
                {
                    ServiceMgr.WriteLine("DetectExitReason -> Permission request in progress");
                    return ExitReason.SystemPermissionRestart;
                }

                // 2. 检查最近是否有权限状态变化
                if (HasRecentPermissionChange())
                {
                    ServiceMgr.WriteLine("DetectExitReason -> Recent permission change detected");
                    return ExitReason.SystemPermissionRestart;
                }

                // 3. 检查系统通知类型
                if (notification?.UserInfo != null)
                {
                    var userInfo = notification.UserInfo;
                    ServiceMgr.WriteLine($"DetectExitReason -> Notification UserInfo: {userInfo}");

                    // 检查是否包含系统重启相关信息
                    if (userInfo.ContainsKey(new NSString("NSApplicationTerminateReply")))
                    {
                        return ExitReason.SystemForceExit;
                    }
                }

                // 4. 检查进程启动参数（是否为权限重启）
                string[] args = Environment.GetCommandLineArgs();
                if (args != null && args.Length > 1)
                {
                    foreach (string arg in args)
                    {
                        if (arg.Contains("permission") || arg.Contains("restart"))
                        {
                            ServiceMgr.WriteLine($"DetectExitReason -> Permission restart arg found: {arg}");
                            return ExitReason.SystemPermissionRestart;
                        }
                    }
                }

                // 5. 检查应用运行时间（权限重启通常很快）
                TimeSpan runTime = DateTime.Now - Process.GetCurrentProcess().StartTime;
                if (runTime.TotalSeconds < 30)
                {
                    ServiceMgr.WriteLine($"DetectExitReason -> Short runtime detected: {runTime.TotalSeconds}s");
                    return ExitReason.SystemPermissionRestart;
                }

                // 6. 检查权限状态变化（如果权限刚刚被授予，可能是权限重启）
                if (CheckPermissionJustGranted())
                {
                    ServiceMgr.WriteLine("DetectExitReason -> Permission just granted");
                    return ExitReason.SystemPermissionRestart;
                }

                // 默认为用户退出
                return ExitReason.UserExit;
            }
            catch (Exception ex)
            {
                ServiceMgr.WriteLine($"DetectExitReason -> Error: {ex.Message}");
                return ExitReason.UserExit;
            }
        }

        /// <summary>
        /// 检查是否正在请求权限
        /// </summary>
        private bool IsRequestingPermissions()
        {
            try
            {
                // 检查权限请求标志
                bool hasScreenCaptureRequest = SettingMgr.GetValue<bool>("HasTccRequestScreenCapture", false);
                bool hasAccessibilityRequest = SettingMgr.GetValue<bool>("HasTccRequestAccessibility", false);
                bool hasAllFilesRequest = SettingMgr.GetValue<bool>("HasTccRequestAllFiles", false);

                return hasScreenCaptureRequest || hasAccessibilityRequest || hasAllFilesRequest;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查最近是否有权限状态变化
        /// </summary>
        private bool HasRecentPermissionChange()
        {
            try
            {
                DateTime lastPermissionCheck = SettingMgr.GetValue<DateTime>("LastPermissionCheck", DateTime.MinValue);

                // 如果最近5分钟内检查过权限，可能是权限变化导致的重启
                if (DateTime.Now.Subtract(lastPermissionCheck).TotalMinutes < 5)
                {
                    return true;
                }

                // 检查系统日志中是否有TCC相关的记录
                return CheckSystemLogForTCCActivity();
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查系统日志中的TCC活动
        /// </summary>
        private bool CheckSystemLogForTCCActivity()
        {
            try
            {
                string bundleId = NSBundle.MainBundle.BundleIdentifier;
                if (string.IsNullOrEmpty(bundleId))
                    return false;

                // 检查最近1分钟的TCC日志
                string logCommand = $"log show --predicate 'subsystem == \"com.apple.TCC\" AND composedMessage CONTAINS \"{bundleId}\"' --last 1m";
                string logResult = Common.RunShell("/usr/bin/log", "show", "--predicate", $"subsystem == \"com.apple.TCC\" AND composedMessage CONTAINS \"{bundleId}\"", "--last", "1m");

                if (!string.IsNullOrEmpty(logResult) && logResult.Contains(bundleId))
                {
                    ServiceMgr.WriteLine($"CheckSystemLogForTCCActivity -> Found TCC activity: {logResult.Substring(0, Math.Min(200, logResult.Length))}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                ServiceMgr.WriteLine($"CheckSystemLogForTCCActivity -> Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查权限是否刚刚被授予
        /// </summary>
        private bool CheckPermissionJustGranted()
        {
            try
            {
                // 获取当前权限状态
                bool currentScreenCapture = MyAPI.AuthStatusForScreenCapture;
                bool currentAccessibility = MyAPI.AuthStatusForAccessibility;
                bool currentAllFiles = MyAPI.AuthStatusForAllFiles;

                // 获取上次记录的权限状态
                bool lastScreenCapture = SettingMgr.GetValue<bool>("LastScreenCaptureStatus", false);
                bool lastAccessibility = SettingMgr.GetValue<bool>("LastAccessibilityStatus", false);
                bool lastAllFiles = SettingMgr.GetValue<bool>("LastAllFilesStatus", false);

                // 检查是否有权限从false变为true
                bool permissionGranted = (currentScreenCapture && !lastScreenCapture) ||
                                        (currentAccessibility && !lastAccessibility) ||
                                        (currentAllFiles && !lastAllFiles);

                if (permissionGranted)
                {
                    ServiceMgr.WriteLine($"CheckPermissionJustGranted -> Permission change detected: " +
                        $"ScreenCapture: {lastScreenCapture}->{currentScreenCapture}, " +
                        $"Accessibility: {lastAccessibility}->{currentAccessibility}, " +
                        $"AllFiles: {lastAllFiles}->{currentAllFiles}");
                }

                // 更新权限状态记录
                SettingMgr.SetValue("LastScreenCaptureStatus", currentScreenCapture);
                SettingMgr.SetValue("LastAccessibilityStatus", currentAccessibility);
                SettingMgr.SetValue("LastAllFilesStatus", currentAllFiles);

                return permissionGranted;
            }
            catch (Exception ex)
            {
                ServiceMgr.WriteLine($"CheckPermissionJustGranted -> Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 退出原因枚举
        /// </summary>
        public enum ExitReason
        {
            UserExit,               // 用户主动退出
            SystemPermissionRestart, // 系统权限重启
            SystemForceExit,        // 系统强制退出
            Unknown                 // 未知原因
        }

        // 实现Dock栏退出时触发
        //public override NSApplicationTerminateReply ApplicationShouldTerminate(NSApplication sender)
        //{
        //          ThreadMgr.AbortAll();
        //	TimerMgr.DisposeAll();

        //	return NSApplicationTerminateReply.Now;
        //}

        //      NSMenu dockMenu;

        //      public override NSMenu ApplicationDockMenu(NSApplication sender)
        //      {
        //          // 如果dockMenu为空，创建一个新的菜单
        //          if (dockMenu == null)
        //          {
        //              dockMenu = new NSMenu();

        //              // 创建菜单项
        //              var menuItem = new NSMenuItem("Say Hello", SayHello);
        //              dockMenu.AddItem(menuItem);
        //          }

        //          return dockMenu;
        //      }

        //      void SayHello(object sender, EventArgs e)
        //      {
        //          // 在这里处理菜单项点击事件
        //          var alert = new NSAlert
        //          {
        //              MessageText = "Hello from Dock Menu!",
        //              AlertStyle = NSAlertStyle.Informational
        //          };
        //          alert.RunModal();
        //      }


        public override bool ApplicationShouldHandleReopen(NSApplication sender, bool hasVisibleWindows)
        {
            if (this.MainWindow != null && !this.MainWindow.IsVisible)
            {
                //NSApplication.SharedApplication.doe
                this.MainWindow.OrderFront(null);               
            }

            return false;
        }
    }
}
