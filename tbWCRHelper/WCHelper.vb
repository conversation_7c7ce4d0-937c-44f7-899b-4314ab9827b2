﻿Imports System.IO
Imports System.Text
Imports System.Threading

Public Class WCHelper
    Private Shared mAppFolder As String = String.Empty

    ''' <summary>
    ''' 获取应用程序启动的目录，以“\”结尾
    ''' </summary> 
    Public Shared ReadOnly Property AppFolder() As String
        Get
            Return mAppFolder
        End Get
    End Property

    Private Shared mExceptionFolder As String = ""
    ''' <summary>
    ''' 异常目录
    ''' </summary>
    Public Shared ReadOnly Property ExceptionFolder() As String
        Get
            If mExceptionFolder.Length > 0 Then
                Return mExceptionFolder
            End If

            mExceptionFolder = Path.Combine(WCHelper.AppFolder, "Exception")

            Return mExceptionFolder
        End Get
    End Property

    Shared Sub New()
        mAppFolder = AppDomain.CurrentDomain.BaseDirectory.TrimEnd("\"c) & "\"
        If Not System.IO.Directory.Exists(mAppFolder) Then
            mAppFolder = System.Windows.Forms.Application.StartupPath.TrimEnd("\"c) & "\"
            If Not System.IO.Directory.Exists(mAppFolder) Then
                mAppFolder = Environment.CurrentDirectory.TrimEnd("\"c) & "\"
            End If
        End If
    End Sub

    Public Shared Sub WaitSeconds(ByVal timer As Double)

        Dim dt As Date = Now
        Dim timeOut As Long = CLng(timer * 1000)
        Dim isBackground As Boolean = False
        Try
            isBackground = Threading.Thread.CurrentThread.IsBackground
        Catch ex As Exception
        End Try

        Do
            If Not isBackground Then
                System.Windows.Forms.Application.DoEvents()
            End If

            System.Threading.Thread.Sleep(10)
        Loop Until (Now.Subtract(dt).TotalMilliseconds > timeOut)

    End Sub

    Private Shared lockException As New Object()
    Public Shared Sub LogException(ByVal strText As String, ByVal funcTitle As String)
        If String.IsNullOrEmpty(strText) Then
            Return
        End If

        SyncLock lockException
            Try
                Dim strAppFolder = AppDomain.CurrentDomain.BaseDirectory.TrimEnd("\"c) & "\"
                If Not System.IO.Directory.Exists(strAppFolder) Then
                    strAppFolder = System.Windows.Forms.Application.StartupPath.TrimEnd("\"c) & "\"
                    If Not System.IO.Directory.Exists(strAppFolder) Then
                        strAppFolder = Environment.CurrentDirectory.TrimEnd("\"c) & "\"
                    End If
                End If

                Dim strPath As String = Path.Combine(strAppFolder, "WCRException")

                If Not Directory.Exists(strPath) Then
                    Directory.CreateDirectory(strPath)
                End If

                strPath = Path.Combine(strPath, String.Format("WCRException{0}.txt", Date.Now.ToString("yyyyMMdd")))
                Using writer As New StreamWriter(strPath, True, Encoding.UTF8)
                    writer.WriteLine(String.Format("------------Time:{0}---------------", DateTime.Now.ToString()))
                    If Not String.IsNullOrEmpty(funcTitle) Then
                        writer.WriteLine("Function Name:" & funcTitle)
                    End If
                    writer.WriteLine(strText)
                    writer.Flush()
                End Using

            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine(ex.ToString)
            End Try
        End SyncLock
    End Sub

    Public Shared Function IsTestMode() As Boolean
        If File.Exists(Path.Combine(WCHelper.AppFolder, "Test.dll")) Then
            Return True
        End If

        Return False
    End Function

    Public Shared Function IsDebug() As Boolean
        If File.Exists(Path.Combine(WCHelper.AppFolder, "debug.dll")) Then
            Return True
        End If

        Return False
    End Function


    Private Shared lockerObj As New Object()
    Public Shared Sub Log(ByVal text As String)
        If Not IsDebug() Then
            Return
        End If
        Try
            Dim strAppFolder = AppDomain.CurrentDomain.BaseDirectory.TrimEnd("\"c) & "\"
            If Not System.IO.Directory.Exists(strAppFolder) Then
                strAppFolder = System.Windows.Forms.Application.StartupPath.TrimEnd("\"c) & "\"
                If Not System.IO.Directory.Exists(strAppFolder) Then
                    strAppFolder = Environment.CurrentDirectory.TrimEnd("\"c) & "\"
                End If
            End If

            Dim strPath As String = Path.Combine(strAppFolder, "WCRLogs")
            SyncLock lockerObj
                Try
                    If Not Directory.Exists(strPath) Then
                        Directory.CreateDirectory(strPath)
                    End If
                    Using writer As New StreamWriter(((strPath & Convert.ToString("\")) + "WCR" + DateTime.Now.ToString("yyyyMMdd")) + ".txt", True, Encoding.UTF8)
                        writer.WriteLine("------------Time:" + DateTime.Now.ToString() + "---------------")
                        writer.WriteLine(text)
                        writer.Flush()
                        writer.Close()
                    End Using
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex)
                End Try
            End SyncLock
        Catch ex As Exception
        End Try
    End Sub

    Private Shared mLstIllegalASCII As List(Of Char) = Nothing
    ''' <summary>
    ''' 不可见字符  http://ascii.911cha.com/
    ''' </summary>
    ''' <value></value>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared ReadOnly Property LstIllegalASCII As List(Of Char)
        Get
            If mLstIllegalASCII Is Nothing Then
                mLstIllegalASCII = New List(Of Char)
                mLstIllegalASCII.Add(Convert.ToChar(0))
                mLstIllegalASCII.Add(Convert.ToChar(1))
                mLstIllegalASCII.Add(Convert.ToChar(2))
                mLstIllegalASCII.Add(Convert.ToChar(3))
                mLstIllegalASCII.Add(Convert.ToChar(4))
                mLstIllegalASCII.Add(Convert.ToChar(5))
                mLstIllegalASCII.Add(Convert.ToChar(6))
                mLstIllegalASCII.Add(Convert.ToChar(7))
                mLstIllegalASCII.Add(Convert.ToChar(8))
                mLstIllegalASCII.Add(Convert.ToChar(9))
                mLstIllegalASCII.Add(Convert.ToChar(10))
                mLstIllegalASCII.Add(Convert.ToChar(11))
                mLstIllegalASCII.Add(Convert.ToChar(12))
                mLstIllegalASCII.Add(Convert.ToChar(13))
                mLstIllegalASCII.Add(Convert.ToChar(14))
                mLstIllegalASCII.Add(Convert.ToChar(15))
                mLstIllegalASCII.Add(Convert.ToChar(16))
                mLstIllegalASCII.Add(Convert.ToChar(17))
                mLstIllegalASCII.Add(Convert.ToChar(18))
                mLstIllegalASCII.Add(Convert.ToChar(19))
                mLstIllegalASCII.Add(Convert.ToChar(20))
                mLstIllegalASCII.Add(Convert.ToChar(21))
                mLstIllegalASCII.Add(Convert.ToChar(22))
                mLstIllegalASCII.Add(Convert.ToChar(23))
                mLstIllegalASCII.Add(Convert.ToChar(24))
                mLstIllegalASCII.Add(Convert.ToChar(25))
                mLstIllegalASCII.Add(Convert.ToChar(26))
                mLstIllegalASCII.Add(Convert.ToChar(27))
                mLstIllegalASCII.Add(Convert.ToChar(28))
                mLstIllegalASCII.Add(Convert.ToChar(29))
                mLstIllegalASCII.Add(Convert.ToChar(30))
                mLstIllegalASCII.Add(Convert.ToChar(31))
                mLstIllegalASCII.Add(Convert.ToChar(127))
            End If
            Return mLstIllegalASCII
        End Get
    End Property

    Public Shared Function CheckThreadIsStop(ByVal thd As Thread) As Boolean
        Dim blnResult As Boolean = False

        If thd Is Nothing Then
            blnResult = True
        Else
            If (thd.ThreadState And System.Threading.ThreadState.Stopped) = System.Threading.ThreadState.Stopped Then blnResult = True
            If thd.IsAlive Then blnResult = False
        End If

        Return blnResult
    End Function

End Class
