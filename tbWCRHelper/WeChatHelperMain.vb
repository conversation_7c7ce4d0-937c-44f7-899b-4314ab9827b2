﻿Imports System.Runtime.InteropServices
Imports System.Threading

Public Class WeChatHelperMain

    Const WM_COPYDATA As Integer = &H4A
    Const SC_RESTORE As Int32 = &HF122

    Private mFindDataRecord As WCH_record_callback_t
    Private mFindDataProgress As WCH_progress_callback_t
    Private mFindDataErr As WCH_err_callback_t

    Private mICloverProcFormHandle As Integer = 0

#Region "--- 初始化 ---"

    Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.Size = New Size(1, 1)

        Me.Icon = My.Resources.iTong

        Me.mFindDataRecord = New WCH_record_callback_t(AddressOf OnRecordCallback)
        Me.mFindDataProgress = New WCH_progress_callback_t(AddressOf OnProgressCallback)
        Me.mFindDataErr = New WCH_err_callback_t(AddressOf OnErrCallback)

        WCHelper.Log("RunTime:" & Date.Now)

    End Sub

    Protected Overrides Sub OnLoad(e As EventArgs)
        MyBase.OnLoad(e)
        Me.ShowInTaskbar = False
    End Sub

    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        'Try
        '    If Me.mTdDeviceActivateLogin IsNot Nothing AndAlso Me.mTdDeviceActivateLogin.ThreadState <> ThreadState.Stopped Then
        '        Me.mTdDeviceActivateLogin.Abort()
        '    End If
        'Catch
        'End Try

        MyBase.OnFormClosing(e)
    End Sub

    Protected Overrides Sub DefWndProc(ByRef m As System.Windows.Forms.Message)
        Dim strMessage As String = ""

        If m.Msg = WM_COPYDATA AndAlso CInt(m.WParam) = &HF122 Then
            Select Case m.Msg
                Case WM_COPYDATA
                    Dim mystr As New COPYDATASTRUCT()
                    Dim mytype As Type = mystr.[GetType]()
                    mystr = CType(m.GetLParam(mytype), COPYDATASTRUCT)
                    strMessage = mystr.lpData
                    Exit Select
                Case Else
                    MyBase.DefWndProc(m)
                    Exit Select
            End Select
            DoFindWeChatDataByDll(strMessage)
        End If
        MyBase.DefWndProc(m)
    End Sub

    Protected Overrides Sub OnShown(e As EventArgs)
        MyBase.OnShown(e)

    End Sub

#End Region

#Region "--- dll方式查找删除数据 ---"
    Private mTdWeChatHelperMain As Thread
    Private Sub DoFindWeChatDataByDll(ByVal strCmdLine As String)
        WCHelper.Log(String.Format("DoFindWeChatDataByDll:{0} {1}", strCmdLine, Date.Now))
        Try
            If strCmdLine.StartsWith("iCloverProcFormHandle") Then
                Try
                    Me.mICloverProcFormHandle = CType(strCmdLine.Replace("iCloverProcFormHandle", ""), Integer)
                Catch ex As Exception
                    WCHelper.LogException(ex.ToString(), "DoFindWeChatDataByDll_iCloverProcFormHandle")
                End Try
            ElseIf strCmdLine.StartsWith("qqRcovery") Then
                WCHelper.Log("cmd " & strCmdLine)
                'Dim arrPars As String() = strCmdLine.Split(" ")
                'WCHelper.Log("length" & arrPars.Length)
                Dim strQQPath As String = strCmdLine.Replace("qqRcovery:", "")
                WCHelper.Log("QQDB:" & strQQPath)
                Dim iResult As Integer = qqTableRecovery(strQQPath, New recovery_err_result_callback_t(AddressOf OnQQErrCallback), New QQrecovery_result_callback_t_b(AddressOf OnQQRecordCallbackByte), New QQrecovery_progress_callback_t(AddressOf OnQQProgressCallback))
                WCHelper.Log("qqTableRecoveryResult:" & iResult)
                If iResult <> 0 Then
                    Me.SendProgress(CreateJsonStr(WCRType.ErrorM, iResult, ""))
                End If

            Else
                WCHelper.LogException("ZSWXDo", "DoFindWeChatDataByDll")
                Try
                    If Me.mTdWeChatHelperMain IsNot Nothing AndAlso Me.mTdWeChatHelperMain.ThreadState <> ThreadState.Stopped Then
                        Me.mTdWeChatHelperMain.Abort()
                    End If
                Catch
                End Try

                Me.mTdWeChatHelperMain = New Thread(AddressOf DoTdWeChatHelperMain)
                With Me.mTdWeChatHelperMain
                    .IsBackground = True
                    .Start(strCmdLine)
                End With
                'WeChatHelperMain(strCmdLine, Me.mFindDataRecord, Me.mFindDataProgress, Me.mFindDataErr)
            End If
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "DoFindWeChatDataByDll")
        End Try
    End Sub

    Private Sub DoTdWeChatHelperMain(ByVal obj As Object)
        Try
            Dim intE As Integer = WeChatHelperMain(obj.ToString(), Me.mFindDataRecord, Me.mFindDataProgress, Me.mFindDataErr)
            If intE = 0 Then
                Me.SendProgress(CreateJsonStr(WCRType.Success, "", ""))
            Else
                Me.SendProgress(CreateJsonStr(WCRType.ErrorM, "", intE))
            End If
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "DoTdWeChatHelperMain")
        End Try
    End Sub


    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Public Delegate Sub WCH_record_callback_t(ByVal len As Integer, <MarshalAs(UnmanagedType.LPArray, SizeParamIndex:=0)> ByVal jsonbyte As Byte())

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Private Delegate Sub WCH_progress_callback_t(ByVal progress As Integer, ByVal errmsg As String)

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Private Delegate Sub WCH_err_callback_t(ByVal errcode As Integer, ByVal errmsg As String)

    <DllImport(".\WeChat\WeChatHelper.dll", CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function WeChatHelperMain(ByVal strCmdLine As String, ByVal record As WCH_record_callback_t, ByVal progress As WCH_progress_callback_t, err As WCH_err_callback_t) As Integer
    End Function

    Private mTdRecordCallback As Thread
    Private mQueueRecordCallback As New Queue(Of WCHRecordProgressArgs)
    Private mIntCount As Integer = 0

    Public Sub OnRecordCallback(ByVal len As Integer, <MarshalAs(UnmanagedType.LPArray, SizeParamIndex:=0)> ByVal jsonbyte As Byte())

        Try
            Dim arg As WCHRecordProgressArgs = Nothing
            Try
                Dim record As String = System.Text.Encoding.UTF8.GetString(jsonbyte)
                arg = New WCHRecordProgressArgs(WCRType.Record, len, record)
            Catch ex As Exception
                WCHelper.LogException(ex.ToString(), "OnRecordCallback")
                arg = New WCHRecordProgressArgs(WCRType.Exception, 601, "OnRecordCallbackException")
            End Try

            Me.SendProgress(CreateJsonStr(arg.WCHRType, arg.WCHCode, arg.WCHContent))

            mIntCount = mIntCount + 1
            If mIntCount >= 500 Then
                mIntCount = 0
                Application.DoEvents()
            End If
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "OnRecordCallback")
            Me.SendProgress(CreateJsonStr(WCRType.Exception, 604, "OnRecordCallbackException"))
        End Try


        'mQueueRecordCallback.Enqueue(arg)

        'If Not WCHelper.CheckThreadIsStop(mTdRecordCallback) Then
        '    Return
        'End If

        'mTdRecordCallback = New Thread(AddressOf DoQueueRecordCallback)
        'mTdRecordCallback.IsBackground = True
        'mTdRecordCallback.Start()
        'WCHelper.WaitSeconds(0.1)
        'Try
        '    Dim record As String = System.Text.Encoding.UTF8.GetString(jsonbyte)
        '    Me.SendProgress(CreateJsonStr(WCRType.Record, len, record))
        'Catch ex As Exception
        '    WCHelper.LogException(ex.ToString(), "OnRecordCallback")
        '    Me.SendProgress(CreateJsonStr(WCRType.Exception, 601, "OnRecordCallbackException"))
        'End Try
    End Sub

    Private Sub DoQueueRecordCallback()
        Try
            While mQueueRecordCallback.Count > 0
                Dim arg As WCHRecordProgressArgs = mQueueRecordCallback.Dequeue()
                Me.SendProgress(CreateJsonStr(arg.WCHRType, arg.WCHCode, arg.WCHContent))
                'WCHelper.WaitSeconds(0.1)
            End While
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "DoQueueRecordCallback")
        End Try
    End Sub


    Private mTdProgressCallback As Thread
    Private mQueueProgressCallback As New Queue(Of WCHRecordProgressArgs)
    Public Sub OnProgressCallback(ByVal progress As Integer, ByVal errmsg As String)
        'Dim arg As WCHRecordProgressArgs = Nothing
        'Try
        '    arg = New WCHRecordProgressArgs(WCRType.Progress, progress, errmsg)
        'Catch ex As Exception
        '    WCHelper.LogException(ex.ToString(), "OnProgressCallback")
        '    arg = New WCHRecordProgressArgs(WCRType.Exception, 602, "OnProgressCallbackException")
        'End Try

        'Me.SendProgress(CreateJsonStr(arg.WCHRType, arg.WCHCode, arg.WCHContent))

        'mQueueProgressCallback.Enqueue(arg)
        'If Not WCHelper.CheckThreadIsStop(mTdProgressCallback) Then
        '    Return
        'End If

        'mTdProgressCallback = New Thread(AddressOf DoQueueProgressCallback)
        'mTdProgressCallback.IsBackground = True
        'mTdProgressCallback.Start()

        Try
            Me.SendProgress(CreateJsonStr(WCRType.Progress, progress, errmsg))
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "OnProgressCallback")
            Me.SendProgress(CreateJsonStr(WCRType.Exception, 602, "OnProgressCallbackException"))
        End Try
    End Sub

    Private Sub DoQueueProgressCallback()
        Try
            While mQueueProgressCallback.Count > 0
                Dim arg As WCHRecordProgressArgs = mQueueProgressCallback.Dequeue()
                Me.SendProgress(CreateJsonStr(arg.WCHRType, arg.WCHCode, arg.WCHContent))
                WCHelper.WaitSeconds(0.1)
            End While
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "DoQueueProgressCallback")
        End Try
    End Sub

    Public Sub OnErrCallback(ByVal errcode As Integer, ByVal errmsg As String)
        Try
            'Me.SendProgress(CreateJsonStr(WCRType.ErrorM, errcode, errmsg))
            WCHelper.LogException(String.Format("{0}:{1}", errcode, errmsg), "OnErrCallback")
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "OnErrCallback")
            Me.SendProgress(CreateJsonStr(WCRType.Exception, 603, "OnErrCallbackException"))
        End Try
    End Sub

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Public Delegate Sub QQrecovery_result_callback_t(ByVal len As Integer, ByVal jsonmsg As String)

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Public Delegate Sub QQrecovery_result_callback_t_b(ByVal len As Integer, <MarshalAs(UnmanagedType.LPArray, SizeParamIndex:=0)> ByVal jsonbyte As Byte())

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Public Delegate Sub QQrecovery_progress_callback_t(ByVal progress As Integer)

    <UnmanagedFunctionPointer(CallingConvention.Cdecl)> _
    Public Delegate Sub recovery_err_result_callback_t(ByVal errcode As Integer, ByVal errmsg As String)
    <DllImport("qqsqlre.dll", CharSet:=CharSet.Ansi, CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function qqTableRecovery(ByVal dbDir As String, ByVal err As recovery_err_result_callback_t, ByVal result As QQrecovery_result_callback_t_b, ByVal progress As QQrecovery_progress_callback_t) As Integer
    End Function
    Public Sub OnQQRecordCallback(ByVal len As Integer, ByVal jsonMsg As String)
        Try
            WCHelper.Log(String.Format("OnQQRecordCallback:{0} {1}", jsonMsg, Date.Now))
            'Dim record As String = System.Text.Encoding.UTF8.GetString(jsonbyte)
            Me.SendProgress(CreateJsonStr(WCRType.Record, len, jsonMsg))
            'Dim strRecord As String = System.Text.Encoding.UTF8.GetString()
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "OnRecordCallback")
            Me.SendProgress(CreateJsonStr(WCRType.Exception, 601, "OnRecordCallbackException"))
        End Try
    End Sub

    Public Sub OnQQRecordCallbackByte(ByVal len As Integer, <MarshalAs(UnmanagedType.LPArray, SizeParamIndex:=0)> ByVal jsonbyte As Byte())
        Try
            Dim jsonMsg As String = System.Text.Encoding.UTF8.GetString(jsonbyte)
            WCHelper.Log(String.Format("OnQQRecordCallback:{0} {1}", jsonMsg, Date.Now))
            'Dim record As String = System.Text.Encoding.UTF8.GetString(jsonbyte)
            Me.SendProgress(CreateJsonStr(WCRType.Record, len, jsonMsg))
        Catch ex As Exception

        End Try
    End Sub

    Public Sub OnQQProgressCallback(ByVal progress As Integer)
        Try
            Me.SendProgress(CreateJsonStr(WCRType.Progress, progress, ""))
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "OnProgressCallback")
            Me.SendProgress(CreateJsonStr(WCRType.Exception, 602, "OnProgressCallbackException"))
        End Try
    End Sub

    Public Sub OnQQErrCallback(ByVal errcode As Integer, ByVal errmsg As String)
        Try
            'If errcode <> 0 Then
            '    Me.SendProgress(CreateJsonStr(WCRType.ErrorM, errcode, errmsg))
            'End If
            WCHelper.Log(String.Format("OnErrCallback: errcode:{0} errmsg:{1}", errcode, errmsg))
        Catch ex As Exception
            WCHelper.LogException(ex.ToString(), "OnErrCallback")
            Me.SendProgress(CreateJsonStr(WCRType.Exception, 603, "OnErrCallbackException"))
        End Try
    End Sub

#End Region

#Region "--- 给主程序发消息 ---"

    ''' <summary>
    ''' 构造给主程序消息结构
    ''' </summary>
    ''' <param name="strType">Exception程序异常、Progress进度消息、Record找回信息、Error找回异常 </param>
    ''' <param name="intErrorCodeOrProgress"></param>
    ''' <param name="strContent"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Private Function CreateJsonStr(ByVal wType As WCRType, ByVal intErrorCodeOrProgress As Integer, ByVal strContent As String) As String
        Return String.Format("{0}﹁{1}﹁{2}", wType.ToString(), intErrorCodeOrProgress, strContent)
    End Function

    <DllImport("User32.dll", EntryPoint:="SendMessage")> _
    Private Shared Function SendMessage(hWnd As Integer, Msg As Integer, wParam As Integer, ByRef lParam As COPYDATASTRUCT) As Integer
    End Function

    Dim ia As Integer = 1

    Private Sub SendProgress(ByVal strMsg As String)
        Dim WINDOW_HANDLER As Integer = Me.mICloverProcFormHandle
        WCHelper.Log("WINDOW_HANDLER:" & WINDOW_HANDLER)
        If WINDOW_HANDLER = 0 Then
            Return
        End If

        strMsg = String.Format("WCRProgress{0}", strMsg)
        WCHelper.Log("SendProgress:" & strMsg)

        strMsg = EncodeBase64(System.Text.Encoding.UTF8, strMsg)
        WCHelper.Log("SendProgressReplaceEncodeBase64:" & strMsg)

        strMsg = strMsg.Replace(" ", "")
        Dim sarr As Byte() = System.Text.Encoding.[Default].GetBytes(strMsg)
        Dim len As Integer = sarr.Length
        Dim cds As COPYDATASTRUCT
        cds.dwData = CType(100, IntPtr)
        cds.lpData = strMsg
        cds.cbData = len + 1
        SendMessage(WINDOW_HANDLER, WM_COPYDATA, SC_RESTORE, cds)
        WCHelper.Log("SendProgress:" & strMsg)
    End Sub

    Private Function EncodeBase64(ByVal encode As System.Text.Encoding, ByVal strSource As String)
        Dim bt As Byte() = encode.GetBytes(strSource)
        Try
            strSource = Convert.ToBase64String(bt)
        Catch
            strSource = strSource
        End Try
        Return strSource
    End Function

#End Region

End Class

Public Class WCHRecordProgressArgs

    Private _WCHRType As WCRType
    Public Property WCHRType() As WCRType
        Get
            Return _WCHRType
        End Get
        Set(ByVal value As WCRType)
            _WCHRType = value
        End Set
    End Property

    Private _WCHCode As Integer
    Public Property WCHCode() As Integer
        Get
            Return _WCHCode
        End Get
        Set(ByVal value As Integer)
            _WCHCode = value
        End Set
    End Property

    Private _WCHContent As String
    Public Property WCHContent() As String
        Get
            Return _WCHContent
        End Get
        Set(ByVal value As String)
            _WCHContent = value
        End Set
    End Property

    Public Sub New(ByVal t As WCRType, ByVal c As Integer, ByVal ct As String)
        _WCHRType = t
        _WCHCode = c
        _WCHContent = ct
    End Sub

End Class

Public Structure COPYDATASTRUCT
    Public dwData As IntPtr
    Public cbData As Integer
    <MarshalAs(UnmanagedType.LPStr)> _
    Public lpData As String
End Structure

Public Enum WCRType
    None = 0
    Exception = 1
    Progress = 2
    Record = 3
    ErrorM = 4
    Success = 5
End Enum
