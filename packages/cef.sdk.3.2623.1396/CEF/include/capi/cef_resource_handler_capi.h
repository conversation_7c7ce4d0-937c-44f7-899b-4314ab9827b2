// Copyright (c) 2016 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool and should not edited
// by hand. See the translator.README.txt file in the tools directory for
// more information.
//

#ifndef CEF_INCLUDE_CAPI_CEF_RESOURCE_HANDLER_CAPI_H_
#define CEF_INCLUDE_CAPI_CEF_RESOURCE_HANDLER_CAPI_H_
#pragma once

#include "include/capi/cef_base_capi.h"
#include "include/capi/cef_browser_capi.h"
#include "include/capi/cef_callback_capi.h"
#include "include/capi/cef_cookie_capi.h"
#include "include/capi/cef_request_capi.h"
#include "include/capi/cef_response_capi.h"

#ifdef __cplusplus
extern "C" {
#endif


///
// Structure used to implement a custom request handler structure. The functions
// of this structure will always be called on the IO thread.
///
typedef struct _cef_resource_handler_t {
  ///
  // Base structure.
  ///
  cef_base_t base;

  ///
  // Begin processing the request. To handle the request return true (1) and
  // call cef_callback_t::cont() once the response header information is
  // available (cef_callback_t::cont() can also be called from inside this
  // function if header information is available immediately). To cancel the
  // request return false (0).
  ///
  int (CEF_CALLBACK *process_request)(struct _cef_resource_handler_t* self,
      struct _cef_request_t* request, struct _cef_callback_t* callback);

  ///
  // Retrieve response header information. If the response length is not known
  // set |response_length| to -1 and read_response() will be called until it
  // returns false (0). If the response length is known set |response_length| to
  // a positive value and read_response() will be called until it returns false
  // (0) or the specified number of bytes have been read. Use the |response|
  // object to set the mime type, http status code and other optional header
  // values. To redirect the request to a new URL set |redirectUrl| to the new
  // URL.
  ///
  void (CEF_CALLBACK *get_response_headers)(
      struct _cef_resource_handler_t* self, struct _cef_response_t* response,
      int64* response_length, cef_string_t* redirectUrl);

  ///
  // Read response data. If data is available immediately copy up to
  // |bytes_to_read| bytes into |data_out|, set |bytes_read| to the number of
  // bytes copied, and return true (1). To read the data at a later time set
  // |bytes_read| to 0, return true (1) and call cef_callback_t::cont() when the
  // data is available. To indicate response completion return false (0).
  ///
  int (CEF_CALLBACK *read_response)(struct _cef_resource_handler_t* self,
      void* data_out, int bytes_to_read, int* bytes_read,
      struct _cef_callback_t* callback);

  ///
  // Return true (1) if the specified cookie can be sent with the request or
  // false (0) otherwise. If false (0) is returned for any cookie then no
  // cookies will be sent with the request.
  ///
  int (CEF_CALLBACK *can_get_cookie)(struct _cef_resource_handler_t* self,
      const struct _cef_cookie_t* cookie);

  ///
  // Return true (1) if the specified cookie returned with the response can be
  // set or false (0) otherwise.
  ///
  int (CEF_CALLBACK *can_set_cookie)(struct _cef_resource_handler_t* self,
      const struct _cef_cookie_t* cookie);

  ///
  // Request processing has been canceled.
  ///
  void (CEF_CALLBACK *cancel)(struct _cef_resource_handler_t* self);
} cef_resource_handler_t;


#ifdef __cplusplus
}
#endif

#endif  // CEF_INCLUDE_CAPI_CEF_RESOURCE_HANDLER_CAPI_H_
