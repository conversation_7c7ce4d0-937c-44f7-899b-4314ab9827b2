// Copyright (c) 2016 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file is generated by the make_pack_header.py tool.
//

#ifndef CEF_INCLUDE_CEF_PACK_RESOURCES_H_
#define CEF_INCLUDE_CEF_PACK_RESOURCES_H_
#pragma once

// ---------------------------------------------------------------------------
// From blink_resources.h:

#define IDR_UASTYLE_HTML_CSS 30370
#define IDR_UASTYLE_QUIRKS_CSS 30371
#define IDR_UASTYLE_VIEW_SOURCE_CSS 30372
#define IDR_UASTYLE_THEME_CHROMIUM_ANDROID_CSS 30373
#define IDR_UASTYLE_MEDIA_CONTROLS_ANDROID_CSS 30374
#define IDR_UASTYLE_MEDIA_CONTROLS_ANDROID_NEW_CSS 30375
#define IDR_UASTYLE_THEME_CHROMIUM_LINUX_CSS 30376
#define IDR_UASTYLE_THEME_MAC_CSS 30377
#define IDR_UASTYLE_THEME_INPUT_MULTIPLE_FIELDS_CSS 30378
#define IDR_UASTYLE_THEME_WIN_CSS 30379
#define IDR_UASTYLE_THEME_WIN_QUIRKS_CSS 30380
#define IDR_UASTYLE_SVG_CSS 30381
#define IDR_UASTYLE_MATHML_CSS 30382
#define IDR_UASTYLE_MEDIA_CONTROLS_CSS 30383
#define IDR_UASTYLE_MEDIA_CONTROLS_NEW_CSS 30384
#define IDR_UASTYLE_FULLSCREEN_CSS 30385
#define IDR_UASTYLE_XHTMLMP_CSS 30386
#define IDR_UASTYLE_VIEWPORT_ANDROID_CSS 30387
#define IDR_INSPECTOR_OVERLAY_PAGE_HTML 30388
#define IDR_INSPECTOR_INJECTED_SCRIPT_SOURCE_JS 30389
#define IDR_INSPECTOR_DEBUGGER_SCRIPT_SOURCE_JS 30390
#define IDR_PRIVATE_SCRIPT_DOCUMENTEXECCOMMAND_JS 30391
#define IDR_PRIVATE_SCRIPT_DOCUMENTXMLTREEVIEWER_CSS 30392
#define IDR_PRIVATE_SCRIPT_DOCUMENTXMLTREEVIEWER_JS 30393
#define IDR_PRIVATE_SCRIPT_HTMLMARQUEEELEMENT_JS 30394
#define IDR_PRIVATE_SCRIPT_PRIVATESCRIPTRUNNER_JS 30395
#define IDR_PICKER_COMMON_JS 30396
#define IDR_PICKER_COMMON_CSS 30397
#define IDR_CALENDAR_PICKER_CSS 30398
#define IDR_CALENDAR_PICKER_JS 30399
#define IDR_PICKER_BUTTON_CSS 30400
#define IDR_SUGGESTION_PICKER_CSS 30401
#define IDR_SUGGESTION_PICKER_JS 30402
#define IDR_COLOR_SUGGESTION_PICKER_CSS 30403
#define IDR_COLOR_SUGGESTION_PICKER_JS 30404
#define IDR_LIST_PICKER_CSS 30405
#define IDR_LIST_PICKER_JS 30406
#define IDR_AUDIO_SPATIALIZATION_COMPOSITE 30407
#define IDR_AUDIO_SPATIALIZATION_T000_P000 30408
#define IDR_AUDIO_SPATIALIZATION_T000_P015 30409
#define IDR_AUDIO_SPATIALIZATION_T000_P030 30410
#define IDR_AUDIO_SPATIALIZATION_T000_P045 30411
#define IDR_AUDIO_SPATIALIZATION_T000_P060 30412
#define IDR_AUDIO_SPATIALIZATION_T000_P075 30413
#define IDR_AUDIO_SPATIALIZATION_T000_P090 30414
#define IDR_AUDIO_SPATIALIZATION_T000_P315 30415
#define IDR_AUDIO_SPATIALIZATION_T000_P330 30416
#define IDR_AUDIO_SPATIALIZATION_T000_P345 30417
#define IDR_AUDIO_SPATIALIZATION_T015_P000 30418
#define IDR_AUDIO_SPATIALIZATION_T015_P015 30419
#define IDR_AUDIO_SPATIALIZATION_T015_P030 30420
#define IDR_AUDIO_SPATIALIZATION_T015_P045 30421
#define IDR_AUDIO_SPATIALIZATION_T015_P060 30422
#define IDR_AUDIO_SPATIALIZATION_T015_P075 30423
#define IDR_AUDIO_SPATIALIZATION_T015_P090 30424
#define IDR_AUDIO_SPATIALIZATION_T015_P315 30425
#define IDR_AUDIO_SPATIALIZATION_T015_P330 30426
#define IDR_AUDIO_SPATIALIZATION_T015_P345 30427
#define IDR_AUDIO_SPATIALIZATION_T030_P000 30428
#define IDR_AUDIO_SPATIALIZATION_T030_P015 30429
#define IDR_AUDIO_SPATIALIZATION_T030_P030 30430
#define IDR_AUDIO_SPATIALIZATION_T030_P045 30431
#define IDR_AUDIO_SPATIALIZATION_T030_P060 30432
#define IDR_AUDIO_SPATIALIZATION_T030_P075 30433
#define IDR_AUDIO_SPATIALIZATION_T030_P090 30434
#define IDR_AUDIO_SPATIALIZATION_T030_P315 30435
#define IDR_AUDIO_SPATIALIZATION_T030_P330 30436
#define IDR_AUDIO_SPATIALIZATION_T030_P345 30437
#define IDR_AUDIO_SPATIALIZATION_T045_P000 30438
#define IDR_AUDIO_SPATIALIZATION_T045_P015 30439
#define IDR_AUDIO_SPATIALIZATION_T045_P030 30440
#define IDR_AUDIO_SPATIALIZATION_T045_P045 30441
#define IDR_AUDIO_SPATIALIZATION_T045_P060 30442
#define IDR_AUDIO_SPATIALIZATION_T045_P075 30443
#define IDR_AUDIO_SPATIALIZATION_T045_P090 30444
#define IDR_AUDIO_SPATIALIZATION_T045_P315 30445
#define IDR_AUDIO_SPATIALIZATION_T045_P330 30446
#define IDR_AUDIO_SPATIALIZATION_T045_P345 30447
#define IDR_AUDIO_SPATIALIZATION_T060_P000 30448
#define IDR_AUDIO_SPATIALIZATION_T060_P015 30449
#define IDR_AUDIO_SPATIALIZATION_T060_P030 30450
#define IDR_AUDIO_SPATIALIZATION_T060_P045 30451
#define IDR_AUDIO_SPATIALIZATION_T060_P060 30452
#define IDR_AUDIO_SPATIALIZATION_T060_P075 30453
#define IDR_AUDIO_SPATIALIZATION_T060_P090 30454
#define IDR_AUDIO_SPATIALIZATION_T060_P315 30455
#define IDR_AUDIO_SPATIALIZATION_T060_P330 30456
#define IDR_AUDIO_SPATIALIZATION_T060_P345 30457
#define IDR_AUDIO_SPATIALIZATION_T075_P000 30458
#define IDR_AUDIO_SPATIALIZATION_T075_P015 30459
#define IDR_AUDIO_SPATIALIZATION_T075_P030 30460
#define IDR_AUDIO_SPATIALIZATION_T075_P045 30461
#define IDR_AUDIO_SPATIALIZATION_T075_P060 30462
#define IDR_AUDIO_SPATIALIZATION_T075_P075 30463
#define IDR_AUDIO_SPATIALIZATION_T075_P090 30464
#define IDR_AUDIO_SPATIALIZATION_T075_P315 30465
#define IDR_AUDIO_SPATIALIZATION_T075_P330 30466
#define IDR_AUDIO_SPATIALIZATION_T075_P345 30467
#define IDR_AUDIO_SPATIALIZATION_T090_P000 30468
#define IDR_AUDIO_SPATIALIZATION_T090_P015 30469
#define IDR_AUDIO_SPATIALIZATION_T090_P030 30470
#define IDR_AUDIO_SPATIALIZATION_T090_P045 30471
#define IDR_AUDIO_SPATIALIZATION_T090_P060 30472
#define IDR_AUDIO_SPATIALIZATION_T090_P075 30473
#define IDR_AUDIO_SPATIALIZATION_T090_P090 30474
#define IDR_AUDIO_SPATIALIZATION_T090_P315 30475
#define IDR_AUDIO_SPATIALIZATION_T090_P330 30476
#define IDR_AUDIO_SPATIALIZATION_T090_P345 30477
#define IDR_AUDIO_SPATIALIZATION_T105_P000 30478
#define IDR_AUDIO_SPATIALIZATION_T105_P015 30479
#define IDR_AUDIO_SPATIALIZATION_T105_P030 30480
#define IDR_AUDIO_SPATIALIZATION_T105_P045 30481
#define IDR_AUDIO_SPATIALIZATION_T105_P060 30482
#define IDR_AUDIO_SPATIALIZATION_T105_P075 30483
#define IDR_AUDIO_SPATIALIZATION_T105_P090 30484
#define IDR_AUDIO_SPATIALIZATION_T105_P315 30485
#define IDR_AUDIO_SPATIALIZATION_T105_P330 30486
#define IDR_AUDIO_SPATIALIZATION_T105_P345 30487
#define IDR_AUDIO_SPATIALIZATION_T120_P000 30488
#define IDR_AUDIO_SPATIALIZATION_T120_P015 30489
#define IDR_AUDIO_SPATIALIZATION_T120_P030 30490
#define IDR_AUDIO_SPATIALIZATION_T120_P045 30491
#define IDR_AUDIO_SPATIALIZATION_T120_P060 30492
#define IDR_AUDIO_SPATIALIZATION_T120_P075 30493
#define IDR_AUDIO_SPATIALIZATION_T120_P090 30494
#define IDR_AUDIO_SPATIALIZATION_T120_P315 30495
#define IDR_AUDIO_SPATIALIZATION_T120_P330 30496
#define IDR_AUDIO_SPATIALIZATION_T120_P345 30497
#define IDR_AUDIO_SPATIALIZATION_T135_P000 30498
#define IDR_AUDIO_SPATIALIZATION_T135_P015 30499
#define IDR_AUDIO_SPATIALIZATION_T135_P030 30500
#define IDR_AUDIO_SPATIALIZATION_T135_P045 30501
#define IDR_AUDIO_SPATIALIZATION_T135_P060 30502
#define IDR_AUDIO_SPATIALIZATION_T135_P075 30503
#define IDR_AUDIO_SPATIALIZATION_T135_P090 30504
#define IDR_AUDIO_SPATIALIZATION_T135_P315 30505
#define IDR_AUDIO_SPATIALIZATION_T135_P330 30506
#define IDR_AUDIO_SPATIALIZATION_T135_P345 30507
#define IDR_AUDIO_SPATIALIZATION_T150_P000 30508
#define IDR_AUDIO_SPATIALIZATION_T150_P015 30509
#define IDR_AUDIO_SPATIALIZATION_T150_P030 30510
#define IDR_AUDIO_SPATIALIZATION_T150_P045 30511
#define IDR_AUDIO_SPATIALIZATION_T150_P060 30512
#define IDR_AUDIO_SPATIALIZATION_T150_P075 30513
#define IDR_AUDIO_SPATIALIZATION_T150_P090 30514
#define IDR_AUDIO_SPATIALIZATION_T150_P315 30515
#define IDR_AUDIO_SPATIALIZATION_T150_P330 30516
#define IDR_AUDIO_SPATIALIZATION_T150_P345 30517
#define IDR_AUDIO_SPATIALIZATION_T165_P000 30518
#define IDR_AUDIO_SPATIALIZATION_T165_P015 30519
#define IDR_AUDIO_SPATIALIZATION_T165_P030 30520
#define IDR_AUDIO_SPATIALIZATION_T165_P045 30521
#define IDR_AUDIO_SPATIALIZATION_T165_P060 30522
#define IDR_AUDIO_SPATIALIZATION_T165_P075 30523
#define IDR_AUDIO_SPATIALIZATION_T165_P090 30524
#define IDR_AUDIO_SPATIALIZATION_T165_P315 30525
#define IDR_AUDIO_SPATIALIZATION_T165_P330 30526
#define IDR_AUDIO_SPATIALIZATION_T165_P345 30527
#define IDR_AUDIO_SPATIALIZATION_T180_P000 30528
#define IDR_AUDIO_SPATIALIZATION_T180_P015 30529
#define IDR_AUDIO_SPATIALIZATION_T180_P030 30530
#define IDR_AUDIO_SPATIALIZATION_T180_P045 30531
#define IDR_AUDIO_SPATIALIZATION_T180_P060 30532
#define IDR_AUDIO_SPATIALIZATION_T180_P075 30533
#define IDR_AUDIO_SPATIALIZATION_T180_P090 30534
#define IDR_AUDIO_SPATIALIZATION_T180_P315 30535
#define IDR_AUDIO_SPATIALIZATION_T180_P330 30536
#define IDR_AUDIO_SPATIALIZATION_T180_P345 30537
#define IDR_AUDIO_SPATIALIZATION_T195_P000 30538
#define IDR_AUDIO_SPATIALIZATION_T195_P015 30539
#define IDR_AUDIO_SPATIALIZATION_T195_P030 30540
#define IDR_AUDIO_SPATIALIZATION_T195_P045 30541
#define IDR_AUDIO_SPATIALIZATION_T195_P060 30542
#define IDR_AUDIO_SPATIALIZATION_T195_P075 30543
#define IDR_AUDIO_SPATIALIZATION_T195_P090 30544
#define IDR_AUDIO_SPATIALIZATION_T195_P315 30545
#define IDR_AUDIO_SPATIALIZATION_T195_P330 30546
#define IDR_AUDIO_SPATIALIZATION_T195_P345 30547
#define IDR_AUDIO_SPATIALIZATION_T210_P000 30548
#define IDR_AUDIO_SPATIALIZATION_T210_P015 30549
#define IDR_AUDIO_SPATIALIZATION_T210_P030 30550
#define IDR_AUDIO_SPATIALIZATION_T210_P045 30551
#define IDR_AUDIO_SPATIALIZATION_T210_P060 30552
#define IDR_AUDIO_SPATIALIZATION_T210_P075 30553
#define IDR_AUDIO_SPATIALIZATION_T210_P090 30554
#define IDR_AUDIO_SPATIALIZATION_T210_P315 30555
#define IDR_AUDIO_SPATIALIZATION_T210_P330 30556
#define IDR_AUDIO_SPATIALIZATION_T210_P345 30557
#define IDR_AUDIO_SPATIALIZATION_T225_P000 30558
#define IDR_AUDIO_SPATIALIZATION_T225_P015 30559
#define IDR_AUDIO_SPATIALIZATION_T225_P030 30560
#define IDR_AUDIO_SPATIALIZATION_T225_P045 30561
#define IDR_AUDIO_SPATIALIZATION_T225_P060 30562
#define IDR_AUDIO_SPATIALIZATION_T225_P075 30563
#define IDR_AUDIO_SPATIALIZATION_T225_P090 30564
#define IDR_AUDIO_SPATIALIZATION_T225_P315 30565
#define IDR_AUDIO_SPATIALIZATION_T225_P330 30566
#define IDR_AUDIO_SPATIALIZATION_T225_P345 30567
#define IDR_AUDIO_SPATIALIZATION_T240_P000 30568
#define IDR_AUDIO_SPATIALIZATION_T240_P015 30569
#define IDR_AUDIO_SPATIALIZATION_T240_P030 30570
#define IDR_AUDIO_SPATIALIZATION_T240_P045 30571
#define IDR_AUDIO_SPATIALIZATION_T240_P060 30572
#define IDR_AUDIO_SPATIALIZATION_T240_P075 30573
#define IDR_AUDIO_SPATIALIZATION_T240_P090 30574
#define IDR_AUDIO_SPATIALIZATION_T240_P315 30575
#define IDR_AUDIO_SPATIALIZATION_T240_P330 30576
#define IDR_AUDIO_SPATIALIZATION_T240_P345 30577
#define IDR_AUDIO_SPATIALIZATION_T255_P000 30578
#define IDR_AUDIO_SPATIALIZATION_T255_P015 30579
#define IDR_AUDIO_SPATIALIZATION_T255_P030 30580
#define IDR_AUDIO_SPATIALIZATION_T255_P045 30581
#define IDR_AUDIO_SPATIALIZATION_T255_P060 30582
#define IDR_AUDIO_SPATIALIZATION_T255_P075 30583
#define IDR_AUDIO_SPATIALIZATION_T255_P090 30584
#define IDR_AUDIO_SPATIALIZATION_T255_P315 30585
#define IDR_AUDIO_SPATIALIZATION_T255_P330 30586
#define IDR_AUDIO_SPATIALIZATION_T255_P345 30587
#define IDR_AUDIO_SPATIALIZATION_T270_P000 30588
#define IDR_AUDIO_SPATIALIZATION_T270_P015 30589
#define IDR_AUDIO_SPATIALIZATION_T270_P030 30590
#define IDR_AUDIO_SPATIALIZATION_T270_P045 30591
#define IDR_AUDIO_SPATIALIZATION_T270_P060 30592
#define IDR_AUDIO_SPATIALIZATION_T270_P075 30593
#define IDR_AUDIO_SPATIALIZATION_T270_P090 30594
#define IDR_AUDIO_SPATIALIZATION_T270_P315 30595
#define IDR_AUDIO_SPATIALIZATION_T270_P330 30596
#define IDR_AUDIO_SPATIALIZATION_T270_P345 30597
#define IDR_AUDIO_SPATIALIZATION_T285_P000 30598
#define IDR_AUDIO_SPATIALIZATION_T285_P015 30599
#define IDR_AUDIO_SPATIALIZATION_T285_P030 30600
#define IDR_AUDIO_SPATIALIZATION_T285_P045 30601
#define IDR_AUDIO_SPATIALIZATION_T285_P060 30602
#define IDR_AUDIO_SPATIALIZATION_T285_P075 30603
#define IDR_AUDIO_SPATIALIZATION_T285_P090 30604
#define IDR_AUDIO_SPATIALIZATION_T285_P315 30605
#define IDR_AUDIO_SPATIALIZATION_T285_P330 30606
#define IDR_AUDIO_SPATIALIZATION_T285_P345 30607
#define IDR_AUDIO_SPATIALIZATION_T300_P000 30608
#define IDR_AUDIO_SPATIALIZATION_T300_P015 30609
#define IDR_AUDIO_SPATIALIZATION_T300_P030 30610
#define IDR_AUDIO_SPATIALIZATION_T300_P045 30611
#define IDR_AUDIO_SPATIALIZATION_T300_P060 30612
#define IDR_AUDIO_SPATIALIZATION_T300_P075 30613
#define IDR_AUDIO_SPATIALIZATION_T300_P090 30614
#define IDR_AUDIO_SPATIALIZATION_T300_P315 30615
#define IDR_AUDIO_SPATIALIZATION_T300_P330 30616
#define IDR_AUDIO_SPATIALIZATION_T300_P345 30617
#define IDR_AUDIO_SPATIALIZATION_T315_P000 30618
#define IDR_AUDIO_SPATIALIZATION_T315_P015 30619
#define IDR_AUDIO_SPATIALIZATION_T315_P030 30620
#define IDR_AUDIO_SPATIALIZATION_T315_P045 30621
#define IDR_AUDIO_SPATIALIZATION_T315_P060 30622
#define IDR_AUDIO_SPATIALIZATION_T315_P075 30623
#define IDR_AUDIO_SPATIALIZATION_T315_P090 30624
#define IDR_AUDIO_SPATIALIZATION_T315_P315 30625
#define IDR_AUDIO_SPATIALIZATION_T315_P330 30626
#define IDR_AUDIO_SPATIALIZATION_T315_P345 30627
#define IDR_AUDIO_SPATIALIZATION_T330_P000 30628
#define IDR_AUDIO_SPATIALIZATION_T330_P015 30629
#define IDR_AUDIO_SPATIALIZATION_T330_P030 30630
#define IDR_AUDIO_SPATIALIZATION_T330_P045 30631
#define IDR_AUDIO_SPATIALIZATION_T330_P060 30632
#define IDR_AUDIO_SPATIALIZATION_T330_P075 30633
#define IDR_AUDIO_SPATIALIZATION_T330_P090 30634
#define IDR_AUDIO_SPATIALIZATION_T330_P315 30635
#define IDR_AUDIO_SPATIALIZATION_T330_P330 30636
#define IDR_AUDIO_SPATIALIZATION_T330_P345 30637
#define IDR_AUDIO_SPATIALIZATION_T345_P000 30638
#define IDR_AUDIO_SPATIALIZATION_T345_P015 30639
#define IDR_AUDIO_SPATIALIZATION_T345_P030 30640
#define IDR_AUDIO_SPATIALIZATION_T345_P045 30641
#define IDR_AUDIO_SPATIALIZATION_T345_P060 30642
#define IDR_AUDIO_SPATIALIZATION_T345_P075 30643
#define IDR_AUDIO_SPATIALIZATION_T345_P090 30644
#define IDR_AUDIO_SPATIALIZATION_T345_P315 30645
#define IDR_AUDIO_SPATIALIZATION_T345_P330 30646
#define IDR_AUDIO_SPATIALIZATION_T345_P345 30647

// ---------------------------------------------------------------------------
// From cef_resources.h:

#define IDR_CEF_DEVTOOLS_DISCOVERY_PAGE 27500
#define IDR_CEF_LICENSE_TXT 27501
#define IDR_CEF_VERSION_HTML 27502
#define IDR_CEF_EXTENSION_API_FEATURES 27503
#define IDR_PDF_MANIFEST 27504
#define IDR_BLOCKED_PLUGIN_HTML 27505
#define IDR_PLUGIN_POSTER_HTML 27506
#define IDR_PLUGIN_DB_JSON 27507

// ---------------------------------------------------------------------------
// From component_extension_resources.h:

#define IDR_BOOKMARK_MANAGER_MAIN 1450
#define IDR_HOTWORD_AUDIO_VERIFICATION_MAIN 1451
#define IDR_WALLPAPER_MANAGER_MAIN 1452
#define IDR_FIRST_RUN_DIALOG_MAIN 1453
#define IDR_NETWORK_SPEECH_SYNTHESIS_JS 1000
#define IDR_BRAILLE_IME_JS 1001
#define IDR_BRAILLE_IME_MAIN_JS 1002
#define IDR_BOOKMARK_MANAGER_BOOKMARK_MANAGER_SEARCH 1003
#define IDR_BOOKMARK_MANAGER_BOOKMARK_MANAGER_SEARCH_RTL 1004
#define IDR_BOOKMARK_MANAGER_BOOKMARK_MAIN_JS 1005
#define IDR_BOOKMARK_MANAGER_BOOKMARK_BMM_LIST_JS 1006
#define IDR_BOOKMARK_MANAGER_BOOKMARK_BMM_TREE_JS 1007
#define IDR_BOOKMARK_MANAGER_BOOKMARK_DND_JS 1008
#define IDR_BOOKMARK_MANAGER_BOOKMARK_BMM_JS 1009
#define IDR_GAIA_AUTH_MAIN 1010
#define IDR_GAIA_AUTH_MAIN_JS 1011
#define IDR_GAIA_AUTH_MAIN_CSS 1012
#define IDR_GAIA_AUTH_OFFLINE 1013
#define IDR_GAIA_AUTH_OFFLINE_JS 1014
#define IDR_GAIA_AUTH_OFFLINE_CSS 1015
#define IDR_GAIA_AUTH_SUCCESS 1016
#define IDR_GAIA_AUTH_UTIL_JS 1017
#define IDR_GAIA_AUTH_BACKGROUND_JS 1018
#define IDR_GAIA_AUTH_SAML_INJECTED_JS 1019
#define IDR_GAIA_AUTH_CHANNEL_JS 1020
#define IDR_HANGOUT_SERVICES_BACKGROUND_HTML 1021
#define IDR_HANGOUT_SERVICES_THUNK_JS 1022
#define IDR_HOTWORD_AUDIO_VERIFICATION_BACKGROUND_JS 1023
#define IDR_HOTWORD_AUDIO_VERIFICATION_MAIN_JS 1024
#define IDR_HOTWORD_AUDIO_VERIFICATION_FLOW_JS 1025
#define IDR_START_STEP_HTML 1026
#define IDR_AUDIO_HISTORY_STEP_HTML 1027
#define IDR_SPEECH_TRAINING_STEP_HTML 1028
#define IDR_FINISHED_STEP_HTML 1029
#define IDR_HOTWORD_AUDIO_VERIFICATION_STYLE_CSS 1030
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_CLOSE_1X 1031
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_CLOSE_2X 1032
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_INTRO_1X 1033
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_INTRO_2X 1034
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_HEADER_1X 1035
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_HEADER_2X 1036
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_CHECK_BLUE_1X 1037
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_CHECK_BLUE_2X 1038
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_CHECK_GRAY_1X 1039
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_CHECK_GRAY_2X 1040
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_LOADER_1X 1041
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_LOADER_2X 1042
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_ERROR_1X 1043
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_ERROR_2X 1044
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_ICON_16 1045
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_ICON_48 1046
#define IDR_HOTWORD_AUDIO_VERIFICATION_IMAGE_ICON_128 1047
#define IDR_HOTWORD_ALWAYS_ON_MANAGER_JS 1048
#define IDR_HOTWORD_AUDIO_CLIENT_JS 1049
#define IDR_HOTWORD_BASE_SESSION_MANAGER_JS 1050
#define IDR_HOTWORD_CONSTANTS_JS 1051
#define IDR_HOTWORD_KEEP_ALIVE_JS 1052
#define IDR_HOTWORD_LAUNCHER_MANAGER_JS 1053
#define IDR_HOTWORD_LOGGING_JS 1054
#define IDR_HOTWORD_MANAGER_JS 1055
#define IDR_HOTWORD_METRICS_JS 1056
#define IDR_HOTWORD_NACL_MANAGER_JS 1057
#define IDR_HOTWORD_PAGE_AUDIO_MANAGER_JS 1058
#define IDR_HOTWORD_STATE_MANAGER_JS 1059
#define IDR_HOTWORD_TRAINING_MANAGER_JS 1060
#define IDR_FEEDBACK_DEFAULT_HTML 1061
#define IDR_FEEDBACK_SYSINFO_HTML 1062
#define IDR_FEEDBACK_EVENT_HANDLER_JS 1063
#define IDR_FEEDBACK_FEEDBACK_JS 1064
#define IDR_FEEDBACK_SYSINFO_JS 1065
#define IDR_FEEDBACK_TAKE_SCREENSHOT_JS 1066
#define IDR_FEEDBACK_TOPBAR_HANDLER_JS 1067
#define IDR_FEEDBACK_FEEDBACK_CSS 1068
#define IDR_FEEDBACK_ICON_32 1069
#define IDR_FEEDBACK_ICON_64 1070
#define IDR_GOOGLE_NOW_BACKGROUND_JS 1071
#define IDR_GOOGLE_NOW_CARDS_JS 1072
#define IDR_GOOGLE_NOW_UTILITY_JS 1073
#define IDR_GOOGLE_NOW_ICON_16 1074
#define IDR_GOOGLE_NOW_ICON_48 1075
#define IDR_GOOGLE_NOW_ICON_128 1076
#define IDR_IDENTITY_API_SCOPE_APPROVAL_BACKGROUND_JS 1077
#define IDR_IDENTITY_API_SCOPE_APPROVAL_DIALOG_CSS 1078
#define IDR_IDENTITY_API_SCOPE_APPROVAL_DIALOG 1079
#define IDR_IDENTITY_API_SCOPE_APPROVAL_DIALOG_JS 1080
#define IDR_WALLPAPER_MANAGER_CONSTANTS_JS 1081
#define IDR_WALLPAPER_MANAGER_EVENT_JS 1082
#define IDR_WALLPAPER_MANAGER_ICON_16 1083
#define IDR_WALLPAPER_MANAGER_ICON_32 1084
#define IDR_WALLPAPER_MANAGER_ICON_48 1085
#define IDR_WALLPAPER_MANAGER_ICON_64 1086
#define IDR_WALLPAPER_MANAGER_ICON_96 1087
#define IDR_WALLPAPER_MANAGER_ICON_128 1088
#define IDR_WALLPAPER_MANAGER_ICON_256 1089
#define IDR_WALLPAPER_MANAGER_MAIN_JS 1090
#define IDR_WALLPAPER_MANAGER_UTIL_JS 1091
#define IDR_FIRST_RUN_DIALOG_BACKGROUND_JS 1092
#define IDR_FIRST_RUN_DIALOG_MAIN_JS 1093
#define IDR_FIRST_RUN_DIALOG_ICON_16 1094
#define IDR_FIRST_RUN_DIALOG_ICON_32 1095
#define IDR_FIRST_RUN_DIALOG_ICON_48 1096
#define IDR_FIRST_RUN_DIALOG_ICON_64 1097
#define IDR_FIRST_RUN_DIALOG_ICON_96 1098
#define IDR_FIRST_RUN_DIALOG_ICON_128 1099
#define IDR_FIRST_RUN_DIALOG_ICON_256 1100
#define IDR_SETTINGS_APP_JS 1101
#define IDR_PDF_INDEX_CSS 1102
#define IDR_PDF_INDEX_HTML 1103
#define IDR_PDF_MAIN_JS 1104
#define IDR_PDF_PDF_JS 1105
#define IDR_PDF_UI_MANAGER_JS 1106
#define IDR_PDF_VIEWPORT_JS 1107
#define IDR_PDF_OPEN_PDF_PARAMS_PARSER_JS 1108
#define IDR_PDF_NAVIGATOR_JS 1109
#define IDR_PDF_VIEWPORT_SCROLLER_JS 1110
#define IDR_PDF_PDF_SCRIPTING_API_JS 1111
#define IDR_PDF_ZOOM_MANAGER_JS 1112
#define IDR_PDF_BROWSER_API_JS 1113
#define IDR_PDF_CONTENT_SCRIPT_JS 1114
#define IDR_PDF_SHARED_ICON_STYLE_CSS 1115
#define IDR_PDF_VIEWER_BOOKMARK_CSS 1116
#define IDR_PDF_VIEWER_BOOKMARK_HTML 1117
#define IDR_PDF_VIEWER_BOOKMARK_JS 1118
#define IDR_PDF_VIEWER_BOOKMARKS_CONTENT_HTML 1119
#define IDR_PDF_VIEWER_BOOKMARKS_CONTENT_JS 1120
#define IDR_PDF_VIEWER_ERROR_SCREEN_CSS 1121
#define IDR_PDF_VIEWER_ERROR_SCREEN_HTML 1122
#define IDR_PDF_VIEWER_ERROR_SCREEN_JS 1123
#define IDR_PDF_VIEWER_PAGE_INDICATOR_CSS 1124
#define IDR_PDF_VIEWER_PAGE_INDICATOR_HTML 1125
#define IDR_PDF_VIEWER_PAGE_INDICATOR_JS 1126
#define IDR_PDF_VIEWER_PAGE_SELECTOR_CSS 1127
#define IDR_PDF_VIEWER_PAGE_SELECTOR_HTML 1128
#define IDR_PDF_VIEWER_PAGE_SELECTOR_JS 1129
#define IDR_PDF_VIEWER_PASSWORD_SCREEN_HTML 1130
#define IDR_PDF_VIEWER_PASSWORD_SCREEN_JS 1131
#define IDR_PDF_VIEWER_PDF_TOOLBAR_CSS 1132
#define IDR_PDF_VIEWER_PDF_TOOLBAR_HTML 1133
#define IDR_PDF_VIEWER_PDF_TOOLBAR_JS 1134
#define IDR_PDF_VIEWER_TOOLBAR_DROPDOWN_CSS 1135
#define IDR_PDF_VIEWER_TOOLBAR_DROPDOWN_HTML 1136
#define IDR_PDF_VIEWER_TOOLBAR_DROPDOWN_JS 1137
#define IDR_PDF_VIEWER_ZOOM_BUTTON_CSS 1138
#define IDR_PDF_VIEWER_ZOOM_BUTTON_HTML 1139
#define IDR_PDF_VIEWER_ZOOM_BUTTON_JS 1140
#define IDR_PDF_VIEWER_ZOOM_SELECTOR_CSS 1141
#define IDR_PDF_VIEWER_ZOOM_SELECTOR_HTML 1142
#define IDR_PDF_VIEWER_ZOOM_SELECTOR_JS 1143
#define IDR_CRYPTOTOKEN_UTIL_JS 1144
#define IDR_CRYPTOTOKEN_B64_JS 1145
#define IDR_CRYPTOTOKEN_CLOSEABLE_JS 1146
#define IDR_CRYPTOTOKEN_COUNTDOWN_JS 1147
#define IDR_CRYPTOTOKEN_COUNTDOWNTIMER_JS 1148
#define IDR_CRYPTOTOKEN_SHA256_JS 1149
#define IDR_CRYPTOTOKEN_TIMER_JS 1150
#define IDR_CRYPTOTOKEN_HIDGNUBBYDEVICE_JS 1151
#define IDR_CRYPTOTOKEN_USBGNUBBYDEVICE_JS 1152
#define IDR_CRYPTOTOKEN_GNUBBIES_JS 1153
#define IDR_CRYPTOTOKEN_GNUBBY_JS 1154
#define IDR_CRYPTOTOKEN_GNUBBY_U2F_JS 1155
#define IDR_CRYPTOTOKEN_GNUBBYCODETYPES_JS 1156
#define IDR_CRYPTOTOKEN_GNUBBYFACTORY_JS 1157
#define IDR_CRYPTOTOKEN_GNUBBYMSGTYPES_JS 1158
#define IDR_CRYPTOTOKEN_USBGNUBBYFACTORY_JS 1159
#define IDR_CRYPTOTOKEN_DEVICESTATUSCODES_JS 1160
#define IDR_CRYPTOTOKEN_ENROLLER_JS 1161
#define IDR_CRYPTOTOKEN_USBENROLLHANDLER_JS 1162
#define IDR_CRYPTOTOKEN_REQUESTQUEUE_JS 1163
#define IDR_CRYPTOTOKEN_SIGNER_JS 1164
#define IDR_CRYPTOTOKEN_SINGLESIGNER_JS 1165
#define IDR_CRYPTOTOKEN_MULTIPLESIGNER_JS 1166
#define IDR_CRYPTOTOKEN_USBSIGNHANDLER_JS 1167
#define IDR_CRYPTOTOKEN_WEBREQUEST_JS 1168
#define IDR_CRYPTOTOKEN_APPID_JS 1169
#define IDR_CRYPTOTOKEN_USBHELPER_JS 1170
#define IDR_CRYPTOTOKEN_TEXTFETCHER_JS 1171
#define IDR_CRYPTOTOKEN_REQUESTHELPER_JS 1172
#define IDR_CRYPTOTOKEN_MESSAGETYPES_JS 1173
#define IDR_CRYPTOTOKEN_INHERITS_JS 1174
#define IDR_CRYPTOTOKEN_GNUBBYDEVICE_JS 1175
#define IDR_CRYPTOTOKEN_GENERICHELPER_JS 1176
#define IDR_CRYPTOTOKEN_FACTORYREGISTRY_JS 1177
#define IDR_CRYPTOTOKEN_ERRORCODES_JS 1178
#define IDR_CRYPTOTOKEN_DEVICEFACTORYREGISTRY_JS 1179
#define IDR_CRYPTOTOKEN_ORIGINCHECK_JS 1180
#define IDR_CRYPTOTOKEN_INDIVIDUALATTEST_JS 1181
#define IDR_CRYPTOTOKEN_GOOGLECORPINDIVIDUALATTEST_JS 1182
#define IDR_CRYPTOTOKEN_APPROVEDORIGINS_JS 1183
#define IDR_CRYPTOTOKEN_WEBREQUESTSENDER_JS 1184
#define IDR_CRYPTOTOKEN_WINDOW_TIMER_JS 1185
#define IDR_CRYPTOTOKEN_WATCHDOG_JS 1186
#define IDR_CRYPTOTOKEN_LOGGING_JS 1187
#define IDR_CRYPTOTOKEN_CRYPTOTOKENAPPROVEDORIGIN_JS 1188
#define IDR_CRYPTOTOKEN_CRYPTOTOKENORIGINCHECK_JS 1189
#define IDR_CRYPTOTOKEN_CRYPTOTOKENBACKGROUND_JS 1190
#define IDR_WHISPERNET_PROXY_BACKGROUND_HTML 1191
#define IDR_WHISPERNET_PROXY_INIT_JS 1192
#define IDR_WHISPERNET_PROXY_NACL_JS 1193
#define IDR_WHISPERNET_PROXY_WRAPPER_JS 1194
#define IDR_WHISPERNET_PROXY_WHISPERNET_PROXY_PROXY_NMF 1195
#define IDR_WHISPERNET_PROXY_WHISPERNET_PROXY_PROXY_PEXE 1196

// ---------------------------------------------------------------------------
// From content_resources.h:

#define IDR_ACCESSIBILITY_HTML 24150
#define IDR_ACCESSIBILITY_CSS 24151
#define IDR_ACCESSIBILITY_JS 24152
#define IDR_APPCACHE_INTERNALS_HTML 24153
#define IDR_APPCACHE_INTERNALS_JS 24154
#define IDR_APPCACHE_INTERNALS_CSS 24155
#define IDR_DEVTOOLS_PINCH_CURSOR_ICON 24156
#define IDR_DEVTOOLS_PINCH_CURSOR_ICON_2X 24157
#define IDR_DEVTOOLS_TOUCH_CURSOR_ICON 24158
#define IDR_DEVTOOLS_TOUCH_CURSOR_ICON_2X 24159
#define IDR_GPU_INTERNALS_HTML 24160
#define IDR_GPU_INTERNALS_JS 24161
#define IDR_INDEXED_DB_INTERNALS_HTML 24162
#define IDR_INDEXED_DB_INTERNALS_JS 24163
#define IDR_INDEXED_DB_INTERNALS_CSS 24164
#define IDR_MEDIA_INTERNALS_HTML 24165
#define IDR_MEDIA_INTERNALS_JS 24166
#define IDR_NETWORK_ERROR_LISTING_HTML 24167
#define IDR_NETWORK_ERROR_LISTING_JS 24168
#define IDR_NETWORK_ERROR_LISTING_CSS 24169
#define IDR_SERVICE_WORKER_INTERNALS_HTML 24170
#define IDR_SERVICE_WORKER_INTERNALS_JS 24171
#define IDR_SERVICE_WORKER_INTERNALS_CSS 24172
#define IDR_WEBRTC_INTERNALS_HTML 24173
#define IDR_WEBRTC_INTERNALS_JS 24174
#define IDR_GPU_SANDBOX_PROFILE 24175
#define IDR_COMMON_SANDBOX_PROFILE 24176
#define IDR_PPAPI_SANDBOX_PROFILE 24177
#define IDR_RENDERER_SANDBOX_PROFILE 24178
#define IDR_UTILITY_SANDBOX_PROFILE 24179
#define IDR_MOJO_BINDINGS_JS 24180
#define IDR_MOJO_BUFFER_JS 24181
#define IDR_MOJO_CODEC_JS 24182
#define IDR_MOJO_CONNECTION_JS 24183
#define IDR_MOJO_CONNECTOR_JS 24184
#define IDR_MOJO_ROUTER_JS 24185
#define IDR_MOJO_UNICODE_JS 24186
#define IDR_MOJO_VALIDATOR_JS 24187

// ---------------------------------------------------------------------------
// From devtools_resources.h:

#define INSPECTOR_CSS 21650
#define INSPECTOR_HTML 21651
#define INSPECTOR_JS 21652
#define TOOLBOX_CSS 21653
#define TOOLBOX_HTML 21654
#define TOOLBOX_JS 21655
#define ACCESSIBILITY_MODULE_JS 21656
#define ANIMATION_MODULE_JS 21657
#define AUDITS_MODULE_JS 21658
#define COMPONENTS_LAZY_MODULE_JS 21659
#define CONSOLE_MODULE_JS 21660
#define DEVICES_MODULE_JS 21661
#define DIFF_MODULE_JS 21662
#define ELEMENTS_MODULE_JS 21663
#define HEAP_SNAPSHOT_WORKER_MODULE_JS 21664
#define LAYERS_MODULE_JS 21665
#define NETWORK_MODULE_JS 21666
#define PROFILER_MODULE_JS 21667
#define PROMISES_MODULE_JS 21668
#define RESOURCES_MODULE_JS 21669
#define SECURITY_MODULE_JS 21670
#define SCRIPT_FORMATTER_WORKER_MODULE_JS 21671
#define SETTINGS_MODULE_JS 21672
#define SNIPPETS_MODULE_JS 21673
#define SOURCE_FRAME_MODULE_JS 21674
#define SOURCES_MODULE_JS 21675
#define TEMP_STORAGE_SHARED_WORKER_MODULE_JS 21676
#define TIMELINE_MODULE_JS 21677
#define UI_LAZY_MODULE_JS 21678
#define DEVTOOLS_EXTENSION_API_JS 21679
#define DEVTOOLS_JS 21680
#define IMAGES_APPLICATIONCACHE_PNG 21681
#define IMAGES_BREAKPOINT_PNG 21682
#define IMAGES_BREAKPOINTCONDITIONAL_PNG 21683
#define IMAGES_BREAKPOINTCONDITIONAL_2X_PNG 21684
#define IMAGES_BREAKPOINT_2X_PNG 21685
#define IMAGES_CHECKER_PNG 21686
#define IMAGES_CHROMEDISABLEDSELECT_PNG 21687
#define IMAGES_CHROMEDISABLEDSELECT_2X_PNG 21688
#define IMAGES_CHROMELEFT_PNG 21689
#define IMAGES_CHROMEMIDDLE_PNG 21690
#define IMAGES_CHROMERIGHT_PNG 21691
#define IMAGES_CHROMESELECT_PNG 21692
#define IMAGES_CHROMESELECT_2X_PNG 21693
#define IMAGES_COOKIE_PNG 21694
#define IMAGES_DATABASE_PNG 21695
#define IMAGES_DATABASETABLE_PNG 21696
#define IMAGES_DELETEICON_PNG 21697
#define IMAGES_DOMAIN_PNG 21698
#define IMAGES_ERRORWAVE_PNG 21699
#define IMAGES_ERRORWAVE_2X_PNG 21700
#define IMAGES_FILESYSTEM_PNG 21701
#define IMAGES_FORWARD_PNG 21702
#define IMAGES_FRAME_PNG 21703
#define IMAGES_GRAPHLABELCALLOUTLEFT_PNG 21704
#define IMAGES_GRAPHLABELCALLOUTRIGHT_PNG 21705
#define IMAGES_IC_INFO_BLACK_18DP_SVG 21706
#define IMAGES_IC_WARNING_BLACK_18DP_SVG 21707
#define IMAGES_INDEXEDDB_PNG 21708
#define IMAGES_INDEXEDDBINDEX_PNG 21709
#define IMAGES_INDEXEDDBOBJECTSTORE_PNG 21710
#define IMAGES_LOCALSTORAGE_PNG 21711
#define IMAGES_NAVIGATIONCONTROLS_PNG 21712
#define IMAGES_NAVIGATIONCONTROLS_2X_PNG 21713
#define IMAGES_NOTIFICATIONS_SVG 21714
#define IMAGES_PANEADDBUTTONS_PNG 21715
#define IMAGES_PANEFILTERBUTTONS_PNG 21716
#define IMAGES_PANEREFRESHBUTTONS_PNG 21717
#define IMAGES_POPOVERARROWS_PNG 21718
#define IMAGES_PROFILEGROUPICON_PNG 21719
#define IMAGES_PROFILEICON_PNG 21720
#define IMAGES_PROFILESMALLICON_PNG 21721
#define IMAGES_RADIODOT_PNG 21722
#define IMAGES_RESIZEDIAGONAL_PNG 21723
#define IMAGES_RESIZEDIAGONAL_2X_PNG 21724
#define IMAGES_RESIZEHORIZONTAL_PNG 21725
#define IMAGES_RESIZEHORIZONTAL_2X_PNG 21726
#define IMAGES_RESIZEVERTICAL_PNG 21727
#define IMAGES_RESIZEVERTICAL_2X_PNG 21728
#define IMAGES_RESOURCECSSICON_PNG 21729
#define IMAGES_RESOURCEDOCUMENTICON_PNG 21730
#define IMAGES_RESOURCEDOCUMENTICONSMALL_PNG 21731
#define IMAGES_RESOURCEJSICON_PNG 21732
#define IMAGES_RESOURCEPLAINICON_PNG 21733
#define IMAGES_RESOURCEPLAINICONSMALL_PNG 21734
#define IMAGES_RESOURCESTIMEGRAPHICON_PNG 21735
#define IMAGES_RESPONSIVEDESIGN_PNG 21736
#define IMAGES_RESPONSIVEDESIGN_2X_PNG 21737
#define IMAGES_SEARCHNEXT_PNG 21738
#define IMAGES_SEARCHPREV_PNG 21739
#define IMAGES_SECURITYPROPERTYINFO_SVG 21740
#define IMAGES_SECURITYPROPERTYINSECURE_SVG 21741
#define IMAGES_SECURITYPROPERTYSECURE_SVG 21742
#define IMAGES_SECURITYPROPERTYUNKNOWN_SVG 21743
#define IMAGES_SECURITYPROPERTYWARNING_SVG 21744
#define IMAGES_SECURITYSTATEINSECURE_SVG 21745
#define IMAGES_SECURITYSTATENEUTRAL_SVG 21746
#define IMAGES_SECURITYSTATESECURE_SVG 21747
#define IMAGES_SERVICEWORKER_SVG 21748
#define IMAGES_SESSIONSTORAGE_PNG 21749
#define IMAGES_SETTINGSLISTREMOVE_PNG 21750
#define IMAGES_SETTINGSLISTREMOVE_2X_PNG 21751
#define IMAGES_SPEECH_PNG 21752
#define IMAGES_TOOLBARBUTTONGLYPHS_PNG 21753
#define IMAGES_TOOLBARBUTTONGLYPHS_2X_PNG 21754
#define IMAGES_TOOLBARITEMSELECTED_PNG 21755
#define IMAGES_TOOLBARRESIZERHORIZONTAL_PNG 21756
#define IMAGES_TOOLBARRESIZERVERTICAL_PNG 21757
#define IMAGES_TOUCHCURSOR_PNG 21758
#define IMAGES_TOUCHCURSOR_2X_PNG 21759
#define IMAGES_TRANSFORMCONTROLS_PNG 21760
#define IMAGES_TRANSFORMCONTROLS_2X_PNG 21761
#define IMAGES_UPDATESERVICEWORKER_SVG 21762

// ---------------------------------------------------------------------------
// From extensions_browser_resources.h:

#define IDR_APP_DEFAULT_ICON 25950
#define IDR_EXTENSION_DEFAULT_ICON 25951
#define IDR_EXTENSION_ACTION_PLAIN_BACKGROUND 25952
#define IDR_EXTENSION_ICON_PLAIN_BACKGROUND 25953

// ---------------------------------------------------------------------------
// From extensions_renderer_resources.h:

#define IDR_APP_VIEW_JS 26000
#define IDR_ASYNC_WAITER_JS 26001
#define IDR_BROWSER_TEST_ENVIRONMENT_SPECIFIC_BINDINGS_JS 26002
#define IDR_DATA_RECEIVER_JS 26003
#define IDR_DATA_SENDER_JS 26004
#define IDR_DATA_STREAM_MOJOM_JS 26005
#define IDR_DATA_STREAM_SERIALIZATION_MOJOM_JS 26006
#define IDR_ENTRY_ID_MANAGER 26007
#define IDR_EVENT_BINDINGS_JS 26008
#define IDR_EXTENSION_OPTIONS_JS 26009
#define IDR_EXTENSION_OPTIONS_ATTRIBUTES_JS 26010
#define IDR_EXTENSION_OPTIONS_CONSTANTS_JS 26011
#define IDR_EXTENSION_OPTIONS_EVENTS_JS 26012
#define IDR_EXTENSION_VIEW_JS 26013
#define IDR_EXTENSION_VIEW_API_METHODS_JS 26014
#define IDR_EXTENSION_VIEW_ATTRIBUTES_JS 26015
#define IDR_EXTENSION_VIEW_CONSTANTS_JS 26016
#define IDR_EXTENSION_VIEW_EVENTS_JS 26017
#define IDR_EXTENSION_VIEW_INTERNAL_CUSTOM_BINDINGS_JS 26018
#define IDR_GUEST_VIEW_ATTRIBUTES_JS 26019
#define IDR_GUEST_VIEW_CONTAINER_JS 26020
#define IDR_GUEST_VIEW_DENY_JS 26021
#define IDR_GUEST_VIEW_EVENTS_JS 26022
#define IDR_GUEST_VIEW_IFRAME_CONTAINER_JS 26023
#define IDR_GUEST_VIEW_IFRAME_JS 26024
#define IDR_GUEST_VIEW_JS 26025
#define IDR_IMAGE_UTIL_JS 26026
#define IDR_JSON_SCHEMA_JS 26027
#define IDR_KEEP_ALIVE_JS 26028
#define IDR_KEEP_ALIVE_MOJOM_JS 26029
#define IDR_LAST_ERROR_JS 26030
#define IDR_MESSAGING_JS 26031
#define IDR_MESSAGING_UTILS_JS 26032
#define IDR_MIME_HANDLER_PRIVATE_CUSTOM_BINDINGS_JS 26033
#define IDR_MIME_HANDLER_MOJOM_JS 26034
#define IDR_SCHEMA_UTILS_JS 26035
#define IDR_SEND_REQUEST_JS 26036
#define IDR_SERIAL_CUSTOM_BINDINGS_JS 26037
#define IDR_SERIAL_MOJOM_JS 26038
#define IDR_SERIAL_SERIALIZATION_MOJOM_JS 26039
#define IDR_SERIAL_SERVICE_JS 26040
#define IDR_SET_ICON_JS 26041
#define IDR_STASH_CLIENT_JS 26042
#define IDR_STASH_MOJOM_JS 26043
#define IDR_TEST_CUSTOM_BINDINGS_JS 26044
#define IDR_UNCAUGHT_EXCEPTION_HANDLER_JS 26045
#define IDR_UTILS_JS 26046
#define IDR_WEB_VIEW_ACTION_REQUESTS_JS 26047
#define IDR_WEB_VIEW_API_METHODS_JS 26048
#define IDR_WEB_VIEW_ATTRIBUTES_JS 26049
#define IDR_WEB_VIEW_CONSTANTS_JS 26050
#define IDR_WEB_VIEW_EVENTS_JS 26051
#define IDR_WEB_VIEW_IFRAME_JS 26052
#define IDR_WEB_VIEW_INTERNAL_CUSTOM_BINDINGS_JS 26053
#define IDR_WEB_VIEW_JS 26054
#define IDR_APP_RUNTIME_CUSTOM_BINDINGS_JS 26055
#define IDR_APP_WINDOW_CUSTOM_BINDINGS_JS 26056
#define IDR_BINDING_JS 26057
#define IDR_CONTEXT_MENUS_CUSTOM_BINDINGS_JS 26058
#define IDR_CONTEXT_MENUS_HANDLERS_JS 26059
#define IDR_DECLARATIVE_WEBREQUEST_CUSTOM_BINDINGS_JS 26060
#define IDR_DISPLAY_SOURCE_CUSTOM_BINDINGS_JS 26061
#define IDR_EXTENSION_CUSTOM_BINDINGS_JS 26062
#define IDR_GREASEMONKEY_API_JS 26063
#define IDR_I18N_CUSTOM_BINDINGS_JS 26064
#define IDR_MOJO_PRIVATE_CUSTOM_BINDINGS_JS 26065
#define IDR_PERMISSIONS_CUSTOM_BINDINGS_JS 26066
#define IDR_PRINTER_PROVIDER_CUSTOM_BINDINGS_JS 26067
#define IDR_RUNTIME_CUSTOM_BINDINGS_JS 26068
#define IDR_SERVICE_WORKER_BINDINGS_JS 26069
#define IDR_WEB_REQUEST_CUSTOM_BINDINGS_JS 26070
#define IDR_WEB_REQUEST_INTERNAL_CUSTOM_BINDINGS_JS 26071
#define IDR_WINDOW_CONTROLS_JS 26072
#define IDR_WINDOW_CONTROLS_TEMPLATE_HTML 26073
#define IDR_WEB_VIEW_REQUEST_CUSTOM_BINDINGS_JS 26074
#define IDR_STORAGE_AREA_JS 26075
#define IDR_PLATFORM_APP_CSS 26076
#define IDR_PLATFORM_APP_JS 26077
#define IDR_EXTENSION_FONTS_CSS 26078
#define IDR_MEDIA_ROUTER_MOJOM_JS 26079
#define IDR_MEDIA_ROUTER_BINDINGS_JS 26080
#define IDR_EXTENSION_CSS 26100

// ---------------------------------------------------------------------------
// From extensions_resources.h:

#define IDR_EXTENSION_API_FEATURES 25750
#define IDR_EXTENSION_API_JSON_DECLARATIVE_WEBREQUEST 25751
#define IDR_EXTENSION_API_JSON_WEB_VIEW_REQUEST 25752
#define IDR_EXTENSION_MANIFEST_FEATURES 25753
#define IDR_EXTENSION_PERMISSION_FEATURES 25754
#define IDR_EXTENSION_BEHAVIOR_FEATURES 25755

// ---------------------------------------------------------------------------
// From net_resources.h:

#define IDR_DIR_HEADER_HTML 4000

// ---------------------------------------------------------------------------
// From ui_resources.h:

#define IDR_AURA_CURSOR_BIG_ALIAS 5500
#define IDR_AURA_CURSOR_BIG_CELL 5501
#define IDR_AURA_CURSOR_BIG_COL_RESIZE 5502
#define IDR_AURA_CURSOR_BIG_CONTEXT_MENU 5503
#define IDR_AURA_CURSOR_BIG_COPY 5504
#define IDR_AURA_CURSOR_BIG_CROSSHAIR 5505
#define IDR_AURA_CURSOR_BIG_EAST_RESIZE 5506
#define IDR_AURA_CURSOR_BIG_EAST_WEST_RESIZE 5507
#define IDR_AURA_CURSOR_BIG_HAND 5508
#define IDR_AURA_CURSOR_BIG_HELP 5509
#define IDR_AURA_CURSOR_BIG_IBEAM 5510
#define IDR_AURA_CURSOR_BIG_MOVE 5511
#define IDR_AURA_CURSOR_BIG_NORTH_EAST_RESIZE 5512
#define IDR_AURA_CURSOR_BIG_NORTH_EAST_SOUTH_WEST_RESIZE 5513
#define IDR_AURA_CURSOR_BIG_NORTH_RESIZE 5514
#define IDR_AURA_CURSOR_BIG_NORTH_SOUTH_RESIZE 5515
#define IDR_AURA_CURSOR_BIG_NORTH_WEST_RESIZE 5516
#define IDR_AURA_CURSOR_BIG_NORTH_WEST_SOUTH_EAST_RESIZE 5517
#define IDR_AURA_CURSOR_BIG_NO_DROP 5518
#define IDR_AURA_CURSOR_BIG_PTR 5519
#define IDR_AURA_CURSOR_BIG_ROW_RESIZE 5520
#define IDR_AURA_CURSOR_BIG_SOUTH_EAST_RESIZE 5521
#define IDR_AURA_CURSOR_BIG_SOUTH_RESIZE 5522
#define IDR_AURA_CURSOR_BIG_SOUTH_WEST_RESIZE 5523
#define IDR_AURA_CURSOR_BIG_WEST_RESIZE 5524
#define IDR_AURA_CURSOR_BIG_XTERM_HORIZ 5525
#define IDR_AURA_CURSOR_BIG_ZOOM_IN 5526
#define IDR_AURA_CURSOR_BIG_ZOOM_OUT 5527
#define IDR_AURA_CURSOR_BIG_GRAB 5528
#define IDR_AURA_CURSOR_BIG_GRABBING 5529
#define IDR_AURA_CURSOR_ALIAS 5530
#define IDR_AURA_CURSOR_CELL 5531
#define IDR_AURA_CURSOR_COL_RESIZE 5532
#define IDR_AURA_CURSOR_CONTEXT_MENU 5533
#define IDR_AURA_CURSOR_COPY 5534
#define IDR_AURA_CURSOR_CROSSHAIR 5535
#define IDR_AURA_CURSOR_EAST_RESIZE 5536
#define IDR_AURA_CURSOR_EAST_WEST_RESIZE 5537
#define IDR_AURA_CURSOR_HAND 5538
#define IDR_AURA_CURSOR_HELP 5539
#define IDR_AURA_CURSOR_IBEAM 5540
#define IDR_AURA_CURSOR_MOVE 5541
#define IDR_AURA_CURSOR_NORTH_EAST_RESIZE 5542
#define IDR_AURA_CURSOR_NORTH_EAST_SOUTH_WEST_RESIZE 5543
#define IDR_AURA_CURSOR_NORTH_RESIZE 5544
#define IDR_AURA_CURSOR_NORTH_SOUTH_RESIZE 5545
#define IDR_AURA_CURSOR_NORTH_WEST_RESIZE 5546
#define IDR_AURA_CURSOR_NORTH_WEST_SOUTH_EAST_RESIZE 5547
#define IDR_AURA_CURSOR_NO_DROP 5548
#define IDR_AURA_CURSOR_PTR 5549
#define IDR_AURA_CURSOR_ROW_RESIZE 5550
#define IDR_AURA_CURSOR_SOUTH_EAST_RESIZE 5551
#define IDR_AURA_CURSOR_SOUTH_RESIZE 5552
#define IDR_AURA_CURSOR_SOUTH_WEST_RESIZE 5553
#define IDR_AURA_CURSOR_THROBBER 5554
#define IDR_AURA_CURSOR_WEST_RESIZE 5555
#define IDR_AURA_CURSOR_XTERM_HORIZ 5556
#define IDR_AURA_CURSOR_ZOOM_IN 5557
#define IDR_AURA_CURSOR_ZOOM_OUT 5558
#define IDR_AURA_CURSOR_GRAB 5559
#define IDR_AURA_CURSOR_GRABBING 5560
#define IDR_AURA_SHADOW_ACTIVE 5561
#define IDR_AURA_SHADOW_INACTIVE 5562
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL 5563
#define IDR_BACK_ARROW 5564
#define IDR_FORWARD_ARROW 5565
#define IDR_BROWSER_ACTION_BADGE_CENTER 5566
#define IDR_BROWSER_ACTION_BADGE_LEFT 5567
#define IDR_BROWSER_ACTION_BADGE_RIGHT 5568
#define IDR_CLOSE_2 5569
#define IDR_CLOSE_2_H 5570
#define IDR_CLOSE_2_MASK 5571
#define IDR_CLOSE_2_P 5572
#define IDR_CLOSE_3_MASK 5573
#define IDR_CLOSE_4_BUTTON 5574
#define IDR_CLOSE_DIALOG 5575
#define IDR_CLOSE_DIALOG_H 5576
#define IDR_CLOSE_DIALOG_P 5577
#define IDR_DISABLE 5578
#define IDR_DISABLE_H 5579
#define IDR_DISABLE_P 5580
#define IDR_DEFAULT_FAVICON 5581
#define IDR_DEFAULT_FAVICON_32 5582
#define IDR_DEFAULT_FAVICON_64 5583
#define IDR_EASY_UNLOCK_HARDLOCKED 5584
#define IDR_EASY_UNLOCK_HARDLOCKED_HOVER 5585
#define IDR_EASY_UNLOCK_HARDLOCKED_PRESSED 5586
#define IDR_EASY_UNLOCK_LOCKED 5587
#define IDR_EASY_UNLOCK_LOCKED_HOVER 5588
#define IDR_EASY_UNLOCK_LOCKED_PRESSED 5589
#define IDR_EASY_UNLOCK_LOCKED_TO_BE_ACTIVATED 5590
#define IDR_EASY_UNLOCK_LOCKED_TO_BE_ACTIVATED_HOVER 5591
#define IDR_EASY_UNLOCK_LOCKED_TO_BE_ACTIVATED_PRESSED 5592
#define IDR_EASY_UNLOCK_LOCKED_WITH_PROXIMITY_HINT 5593
#define IDR_EASY_UNLOCK_LOCKED_WITH_PROXIMITY_HINT_HOVER 5594
#define IDR_EASY_UNLOCK_LOCKED_WITH_PROXIMITY_HINT_PRESSED 5595
#define IDR_EASY_UNLOCK_SPINNER 5596
#define IDR_EASY_UNLOCK_UNLOCKED 5597
#define IDR_EASY_UNLOCK_UNLOCKED_HOVER 5598
#define IDR_EASY_UNLOCK_UNLOCKED_PRESSED 5599
#define IDR_FOLDER_CLOSED 5600
#define IDR_FOLDER_CLOSED_RTL 5601
#define IDR_MENU_CHECK_CHECKED 5602
#define IDR_MENU_HIERARCHY_ARROW 5603
#define IDR_MENU_DROPARROW 5604
#define IDR_MESSAGE_CLOSE 5605
#define IDR_NOTIFICATION_ARROW 5606
#define IDR_NOTIFICATION_ARROW_HOVER 5607
#define IDR_NOTIFICATION_ARROW_PRESSED 5608
#define IDR_NOTIFICATION_ADVANCED_SETTINGS 5609
#define IDR_NOTIFICATION_ADVANCED_SETTINGS_HOVER 5610
#define IDR_NOTIFICATION_ADVANCED_SETTINGS_PRESSED 5611
#define IDR_NOTIFICATION_CLEAR_ALL 5612
#define IDR_NOTIFICATION_CLEAR_ALL_DISABLED 5613
#define IDR_NOTIFICATION_CLEAR_ALL_HOVER 5614
#define IDR_NOTIFICATION_CLEAR_ALL_PRESSED 5615
#define IDR_NOTIFICATION_CLOSE 5616
#define IDR_NOTIFICATION_CLOSE_HOVER 5617
#define IDR_NOTIFICATION_CLOSE_PRESSED 5618
#define IDR_NOTIFICATION_BUBBLE_CLOSE 5619
#define IDR_NOTIFICATION_BUBBLE_CLOSE_HOVER 5620
#define IDR_NOTIFICATION_BUBBLE_CLOSE_PRESSED 5621
#define IDR_NOTIFICATION_DO_NOT_DISTURB 5622
#define IDR_NOTIFICATION_DO_NOT_DISTURB_HOVER 5623
#define IDR_NOTIFICATION_DO_NOT_DISTURB_PRESSED 5624
#define IDR_NOTIFICATION_SETTINGS 5625
#define IDR_NOTIFICATION_SETTINGS_BUTTON_ICON 5626
#define IDR_NOTIFICATION_SETTINGS_BUTTON_ICON_HOVER 5627
#define IDR_NOTIFICATION_SETTINGS_BUTTON_ICON_PRESSED 5628
#define IDR_NOTIFICATION_SETTINGS_HOVER 5629
#define IDR_NOTIFICATION_SETTINGS_PRESSED 5630
#define IDR_NTP_DEFAULT_FAVICON 5631
#define IDR_OOBE_ACTION_BOX_BUTTON_HOVER 5632
#define IDR_OOBE_ACTION_BOX_BUTTON_NORMAL 5633
#define IDR_OOBE_ACTION_BOX_BUTTON_PRESSED 5634
#define IDR_PANEL_TOP_LEFT_CORNER 5635
#define IDR_PANEL_TOP_RIGHT_CORNER 5636
#define IDR_PANEL_BOTTOM_LEFT_CORNER 5637
#define IDR_PANEL_BOTTOM_RIGHT_CORNER 5638
#define IDR_TEXT_SELECTION_HANDLE_CENTER 5639
#define IDR_TEXT_SELECTION_HANDLE_LEFT 5640
#define IDR_TEXT_SELECTION_HANDLE_RIGHT 5641
#define IDR_THROBBER 5642
#define IDR_TOUCH_DRAG_TIP_COPY 5643
#define IDR_TOUCH_DRAG_TIP_MOVE 5644
#define IDR_TOUCH_DRAG_TIP_LINK 5645
#define IDR_TOUCH_DRAG_TIP_NODROP 5646

// ---------------------------------------------------------------------------
// From views_resources.h:

#define IDR_APP_TOP_CENTER 5800
#define IDR_APP_TOP_LEFT 5801
#define IDR_APP_TOP_RIGHT 5802
#define IDR_BUBBLE_B 5803
#define IDR_BUBBLE_BL 5804
#define IDR_BUBBLE_BR 5805
#define IDR_BUBBLE_B_ARROW 5806
#define IDR_BUBBLE_L 5807
#define IDR_BUBBLE_L_ARROW 5808
#define IDR_BUBBLE_R 5809
#define IDR_BUBBLE_R_ARROW 5810
#define IDR_BUBBLE_T 5811
#define IDR_BUBBLE_TL 5812
#define IDR_BUBBLE_TR 5813
#define IDR_BUBBLE_T_ARROW 5814
#define IDR_BUTTON_DISABLED 5815
#define IDR_BUTTON_FOCUSED_HOVER 5816
#define IDR_BUTTON_FOCUSED_NORMAL 5817
#define IDR_BUTTON_FOCUSED_PRESSED 5818
#define IDR_BUTTON_HOVER 5819
#define IDR_BUTTON_NORMAL 5820
#define IDR_BUTTON_PRESSED 5821
#define IDR_BLUE_BUTTON_DISABLED 5822
#define IDR_BLUE_BUTTON_FOCUSED_HOVER 5823
#define IDR_BLUE_BUTTON_FOCUSED_NORMAL 5824
#define IDR_BLUE_BUTTON_FOCUSED_PRESSED 5825
#define IDR_BLUE_BUTTON_HOVER 5826
#define IDR_BLUE_BUTTON_NORMAL 5827
#define IDR_BLUE_BUTTON_PRESSED 5828
#define IDR_CHECKBOX 5829
#define IDR_CHECKBOX_CHECKED 5830
#define IDR_CHECKBOX_CHECKED_DISABLED 5831
#define IDR_CHECKBOX_CHECKED_HOVER 5832
#define IDR_CHECKBOX_CHECKED_PRESSED 5833
#define IDR_CHECKBOX_DISABLED 5834
#define IDR_CHECKBOX_FOCUSED 5835
#define IDR_CHECKBOX_FOCUSED_CHECKED 5836
#define IDR_CHECKBOX_FOCUSED_CHECKED_HOVER 5837
#define IDR_CHECKBOX_FOCUSED_CHECKED_PRESSED 5838
#define IDR_CHECKBOX_FOCUSED_HOVER 5839
#define IDR_CHECKBOX_FOCUSED_PRESSED 5840
#define IDR_CHECKBOX_HOVER 5841
#define IDR_CHECKBOX_PRESSED 5842
#define IDR_CLOSE 5843
#define IDR_CLOSE_H 5844
#define IDR_CLOSE_P 5845
#define IDR_COMBOBOX_BUTTON_BOTTOM 5846
#define IDR_COMBOBOX_BUTTON_H_BOTTOM 5847
#define IDR_COMBOBOX_BUTTON_P_BOTTOM 5848
#define IDR_COMBOBOX_BUTTON_BOTTOM_LEFT 5849
#define IDR_COMBOBOX_BUTTON_H_BOTTOM_LEFT 5850
#define IDR_COMBOBOX_BUTTON_P_BOTTOM_LEFT 5851
#define IDR_COMBOBOX_BUTTON_BOTTOM_RIGHT 5852
#define IDR_COMBOBOX_BUTTON_H_BOTTOM_RIGHT 5853
#define IDR_COMBOBOX_BUTTON_P_BOTTOM_RIGHT 5854
#define IDR_COMBOBOX_BUTTON_CENTER 5855
#define IDR_COMBOBOX_BUTTON_H_CENTER 5856
#define IDR_COMBOBOX_BUTTON_P_CENTER 5857
#define IDR_COMBOBOX_BUTTON_LEFT 5858
#define IDR_COMBOBOX_BUTTON_H_LEFT 5859
#define IDR_COMBOBOX_BUTTON_P_LEFT 5860
#define IDR_COMBOBOX_BUTTON_RIGHT 5861
#define IDR_COMBOBOX_BUTTON_H_RIGHT 5862
#define IDR_COMBOBOX_BUTTON_P_RIGHT 5863
#define IDR_COMBOBOX_BUTTON_MENU_BOTTOM 5864
#define IDR_COMBOBOX_BUTTON_H_MENU_BOTTOM 5865
#define IDR_COMBOBOX_BUTTON_P_MENU_BOTTOM 5866
#define IDR_COMBOBOX_BUTTON_MENU_CENTER 5867
#define IDR_COMBOBOX_BUTTON_H_MENU_CENTER 5868
#define IDR_COMBOBOX_BUTTON_P_MENU_CENTER 5869
#define IDR_COMBOBOX_BUTTON_MENU_TOP 5870
#define IDR_COMBOBOX_BUTTON_H_MENU_TOP 5871
#define IDR_COMBOBOX_BUTTON_P_MENU_TOP 5872
#define IDR_COMBOBOX_BUTTON_TOP 5873
#define IDR_COMBOBOX_BUTTON_H_TOP 5874
#define IDR_COMBOBOX_BUTTON_P_TOP 5875
#define IDR_COMBOBOX_BUTTON_TOP_LEFT 5876
#define IDR_COMBOBOX_BUTTON_H_TOP_LEFT 5877
#define IDR_COMBOBOX_BUTTON_P_TOP_LEFT 5878
#define IDR_COMBOBOX_BUTTON_TOP_RIGHT 5879
#define IDR_COMBOBOX_BUTTON_H_TOP_RIGHT 5880
#define IDR_COMBOBOX_BUTTON_P_TOP_RIGHT 5881
#define IDR_COMBOBOX_BUTTON_F_BOTTOM 5882
#define IDR_COMBOBOX_BUTTON_F_H_BOTTOM 5883
#define IDR_COMBOBOX_BUTTON_F_P_BOTTOM 5884
#define IDR_COMBOBOX_BUTTON_F_BOTTOM_LEFT 5885
#define IDR_COMBOBOX_BUTTON_F_H_BOTTOM_LEFT 5886
#define IDR_COMBOBOX_BUTTON_F_P_BOTTOM_LEFT 5887
#define IDR_COMBOBOX_BUTTON_F_BOTTOM_RIGHT 5888
#define IDR_COMBOBOX_BUTTON_F_H_BOTTOM_RIGHT 5889
#define IDR_COMBOBOX_BUTTON_F_P_BOTTOM_RIGHT 5890
#define IDR_COMBOBOX_BUTTON_F_CENTER 5891
#define IDR_COMBOBOX_BUTTON_F_H_CENTER 5892
#define IDR_COMBOBOX_BUTTON_F_P_CENTER 5893
#define IDR_COMBOBOX_BUTTON_F_LEFT 5894
#define IDR_COMBOBOX_BUTTON_F_H_LEFT 5895
#define IDR_COMBOBOX_BUTTON_F_P_LEFT 5896
#define IDR_COMBOBOX_BUTTON_F_RIGHT 5897
#define IDR_COMBOBOX_BUTTON_F_H_RIGHT 5898
#define IDR_COMBOBOX_BUTTON_F_P_RIGHT 5899
#define IDR_COMBOBOX_BUTTON_F_MENU_BOTTOM 5900
#define IDR_COMBOBOX_BUTTON_F_H_MENU_BOTTOM 5901
#define IDR_COMBOBOX_BUTTON_F_P_MENU_BOTTOM 5902
#define IDR_COMBOBOX_BUTTON_F_MENU_CENTER 5903
#define IDR_COMBOBOX_BUTTON_F_H_MENU_CENTER 5904
#define IDR_COMBOBOX_BUTTON_F_P_MENU_CENTER 5905
#define IDR_COMBOBOX_BUTTON_F_MENU_TOP 5906
#define IDR_COMBOBOX_BUTTON_F_H_MENU_TOP 5907
#define IDR_COMBOBOX_BUTTON_F_P_MENU_TOP 5908
#define IDR_COMBOBOX_BUTTON_F_TOP 5909
#define IDR_COMBOBOX_BUTTON_F_H_TOP 5910
#define IDR_COMBOBOX_BUTTON_F_P_TOP 5911
#define IDR_COMBOBOX_BUTTON_F_TOP_LEFT 5912
#define IDR_COMBOBOX_BUTTON_F_H_TOP_LEFT 5913
#define IDR_COMBOBOX_BUTTON_F_P_TOP_LEFT 5914
#define IDR_COMBOBOX_BUTTON_F_TOP_RIGHT 5915
#define IDR_COMBOBOX_BUTTON_F_H_TOP_RIGHT 5916
#define IDR_COMBOBOX_BUTTON_F_P_TOP_RIGHT 5917
#define IDR_CONTENT_BOTTOM_CENTER 5918
#define IDR_CONTENT_BOTTOM_LEFT_CORNER 5919
#define IDR_CONTENT_BOTTOM_RIGHT_CORNER 5920
#define IDR_CONTENT_LEFT_SIDE 5921
#define IDR_CONTENT_RIGHT_SIDE 5922
#define IDR_FOLDER_OPEN 5923
#define IDR_FOLDER_OPEN_RTL 5924
#define IDR_FRAME 5925
#define IDR_FRAME_INACTIVE 5926
#define IDR_MAXIMIZE 5927
#define IDR_MAXIMIZE_H 5928
#define IDR_MAXIMIZE_P 5929
#define IDR_MENU_CHECK 5930
#define IDR_MENU_RADIO_EMPTY 5931
#define IDR_MENU_RADIO_SELECTED 5932
#define IDR_SLIDER_ACTIVE_LEFT 5933
#define IDR_SLIDER_ACTIVE_RIGHT 5934
#define IDR_SLIDER_ACTIVE_CENTER 5935
#define IDR_SLIDER_DISABLED_LEFT 5936
#define IDR_SLIDER_DISABLED_RIGHT 5937
#define IDR_SLIDER_DISABLED_CENTER 5938
#define IDR_SLIDER_PRESSED_LEFT 5939
#define IDR_SLIDER_PRESSED_RIGHT 5940
#define IDR_SLIDER_PRESSED_CENTER 5941
#define IDR_SLIDER_ACTIVE_THUMB 5942
#define IDR_SLIDER_DISABLED_THUMB 5943
#define IDR_MINIMIZE 5944
#define IDR_MINIMIZE_H 5945
#define IDR_MINIMIZE_P 5946
#define IDR_RADIO 5947
#define IDR_RADIO_CHECKED 5948
#define IDR_RADIO_CHECKED_DISABLED 5949
#define IDR_RADIO_CHECKED_HOVER 5950
#define IDR_RADIO_CHECKED_PRESSED 5951
#define IDR_RADIO_DISABLED 5952
#define IDR_RADIO_FOCUSED 5953
#define IDR_RADIO_FOCUSED_CHECKED 5954
#define IDR_RADIO_FOCUSED_CHECKED_HOVER 5955
#define IDR_RADIO_FOCUSED_CHECKED_PRESSED 5956
#define IDR_RADIO_FOCUSED_HOVER 5957
#define IDR_RADIO_FOCUSED_PRESSED 5958
#define IDR_RADIO_HOVER 5959
#define IDR_RADIO_PRESSED 5960
#define IDR_RESTORE 5961
#define IDR_RESTORE_H 5962
#define IDR_RESTORE_P 5963
#define IDR_TEXTBUTTON_HOVER_BOTTOM 5964
#define IDR_TEXTBUTTON_HOVER_BOTTOM_LEFT 5965
#define IDR_TEXTBUTTON_HOVER_BOTTOM_RIGHT 5966
#define IDR_TEXTBUTTON_HOVER_CENTER 5967
#define IDR_TEXTBUTTON_HOVER_LEFT 5968
#define IDR_TEXTBUTTON_HOVER_RIGHT 5969
#define IDR_TEXTBUTTON_HOVER_TOP 5970
#define IDR_TEXTBUTTON_HOVER_TOP_LEFT 5971
#define IDR_TEXTBUTTON_HOVER_TOP_RIGHT 5972
#define IDR_TEXTBUTTON_PRESSED_BOTTOM 5973
#define IDR_TEXTBUTTON_PRESSED_BOTTOM_LEFT 5974
#define IDR_TEXTBUTTON_PRESSED_BOTTOM_RIGHT 5975
#define IDR_TEXTBUTTON_PRESSED_CENTER 5976
#define IDR_TEXTBUTTON_PRESSED_LEFT 5977
#define IDR_TEXTBUTTON_PRESSED_RIGHT 5978
#define IDR_TEXTBUTTON_PRESSED_TOP 5979
#define IDR_TEXTBUTTON_PRESSED_TOP_LEFT 5980
#define IDR_TEXTBUTTON_PRESSED_TOP_RIGHT 5981
#define IDR_WINDOW_BOTTOM_CENTER 5982
#define IDR_WINDOW_BOTTOM_LEFT_CORNER 5983
#define IDR_WINDOW_BOTTOM_RIGHT_CORNER 5984
#define IDR_WINDOW_LEFT_SIDE 5985
#define IDR_WINDOW_RIGHT_SIDE 5986
#define IDR_WINDOW_TOP_CENTER 5987
#define IDR_WINDOW_TOP_LEFT_CORNER 5988
#define IDR_WINDOW_TOP_RIGHT_CORNER 5989
#define IDR_WINDOW_BUBBLE_SHADOW_BIG_BOTTOM 5990
#define IDR_WINDOW_BUBBLE_SHADOW_BIG_BOTTOM_LEFT 5991
#define IDR_WINDOW_BUBBLE_SHADOW_BIG_BOTTOM_RIGHT 5992
#define IDR_WINDOW_BUBBLE_SHADOW_BIG_LEFT 5993
#define IDR_WINDOW_BUBBLE_SHADOW_BIG_RIGHT 5994
#define IDR_WINDOW_BUBBLE_SHADOW_BIG_TOP 5995
#define IDR_WINDOW_BUBBLE_SHADOW_BIG_TOP_LEFT 5996
#define IDR_WINDOW_BUBBLE_SHADOW_BIG_TOP_RIGHT 5997
#define IDR_WINDOW_BUBBLE_SHADOW_SPIKE_BIG_BOTTOM 5998
#define IDR_WINDOW_BUBBLE_SHADOW_SPIKE_BIG_LEFT 5999
#define IDR_WINDOW_BUBBLE_SHADOW_SPIKE_BIG_RIGHT 6000
#define IDR_WINDOW_BUBBLE_SHADOW_SPIKE_BIG_TOP 6001
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL_BOTTOM 6002
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL_BOTTOM_LEFT 6003
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL_BOTTOM_RIGHT 6004
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL_LEFT 6005
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL_RIGHT 6006
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL_TOP 6007
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL_TOP_LEFT 6008
#define IDR_WINDOW_BUBBLE_SHADOW_SMALL_TOP_RIGHT 6009
#define IDR_WINDOW_BUBBLE_SHADOW_SPIKE_SMALL_BOTTOM 6010
#define IDR_WINDOW_BUBBLE_SHADOW_SPIKE_SMALL_LEFT 6011
#define IDR_WINDOW_BUBBLE_SHADOW_SPIKE_SMALL_RIGHT 6012
#define IDR_WINDOW_BUBBLE_SHADOW_SPIKE_SMALL_TOP 6013

// ---------------------------------------------------------------------------
// From webui_resources.h:

#define IDR_WEBUI_I18N_TEMPLATE_JS 2000
#define IDR_WEBUI_JSTEMPLATE_JS 2001
#define IDR_WEBUI_ANALYTICS_JS 2002
#define IDR_WEBUI_ROBOTO_ROBOTO_LIGHT_WOFF2 2003
#define IDR_WEBUI_ROBOTO_ROBOTO_REGULAR_WOFF2 2004
#define IDR_WEBUI_ROBOTO_ROBOTO_MEDIUM_WOFF2 2005
#define IDR_WEBUI_IMAGES_APPS_BUTTON 2006
#define IDR_WEBUI_IMAGES_APPS_BUTTON_PRESSED 2007
#define IDR_WEBUI_IMAGES_APPS_BUTTON_HOVER 2008
#define IDR_WEBUI_IMAGES_APPS_BUTTON_DISABLED 2009
#define IDR_WEBUI_IMAGES_APPS_BUTTON_FOCUSED 2010
#define IDR_WEBUI_IMAGES_APPS_BUTTON_FOCUSED_PRESSED 2011
#define IDR_WEBUI_IMAGES_APPS_BUTTON_FOCUSED_HOVER 2012
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON 2013
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_PRESSED 2014
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_HOVER 2015
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_DISABLED 2016
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_FOCUSED 2017
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_FOCUSED_PRESSED 2018
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_FOCUSED_HOVER 2019
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX 2020
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_HOVER 2021
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_PRESSED 2022
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED 2023
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED_HOVER 2024
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED_PRESSED 2025
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_INACTIVE 2026
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_CHECKED 2027
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_CHECKED_HOVER 2028
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_CHECKED_PRESSED 2029
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED_CHECKED 2030
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED_CHECKED_HOVER 2031
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED_CHECKED_PRESSED 2032
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_CHECKED_INACTIVE 2033
#define IDR_WEBUI_IMAGES_APPS_TOPBAR_BUTTON_CLOSE 2034
#define IDR_WEBUI_IMAGES_APPS_TOPBAR_BUTTON_MAXIMIZE 2035
#define IDR_WEBUI_IMAGES_APPS_TOPBAR_BUTTON_MINIMIZE 2036
#define IDR_WEBUI_IMAGES_APPS_BUTTON_BUTTER_BAR_CLOSE 2037
#define IDR_WEBUI_IMAGES_APPS_BUTTON_BUTTER_BAR_CLOSE_HOVER 2038
#define IDR_WEBUI_IMAGES_APPS_BUTTON_BUTTER_BAR_CLOSE_PRESSED 2039
#define IDR_WEBUI_IMAGES_APPS_BUTTON_2X 2040
#define IDR_WEBUI_IMAGES_APPS_BUTTON_2X_PRESSED 2041
#define IDR_WEBUI_IMAGES_APPS_BUTTON_2X_HOVER 2042
#define IDR_WEBUI_IMAGES_APPS_BUTTON_2X_DISABLED 2043
#define IDR_WEBUI_IMAGES_APPS_BUTTON_2X_FOCUSED 2044
#define IDR_WEBUI_IMAGES_APPS_BUTTON_2X_FOCUSED_PRESSED 2045
#define IDR_WEBUI_IMAGES_APPS_BUTTON_2X_FOCUSED_HOVER 2046
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_2X 2047
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_2X_PRESSED 2048
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_2X_HOVER 2049
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_2X_DISABLED 2050
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_2X_FOCUSED 2051
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_2X_FOCUSED_PRESSED 2052
#define IDR_WEBUI_IMAGES_APPS_BLUE_BUTTON_2X_FOCUSED_HOVER 2053
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_2X 2054
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_2X_HOVER 2055
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_2X_PRESSED 2056
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_2X_FOCUSED 2057
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_2X_FOCUSED_HOVER 2058
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_2X_FOCUSED_PRESSED 2059
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_INACTIVE_2X 2060
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_CHECKED_2X 2061
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_CHECKED_2X_HOVER 2062
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_CHECKED_2X_PRESSED 2063
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED_CHECKED_2X 2064
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED_CHECKED_2X_HOVER 2065
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_FOCUSED_CHECKED_2X_PRESSED 2066
#define IDR_WEBUI_IMAGES_APPS_CHECKBOX_CHECKED_INACTIVE_2X 2067
#define IDR_WEBUI_IMAGES_APPS_TOPBAR_BUTTON_2X_CLOSE 2068
#define IDR_WEBUI_IMAGES_APPS_TOPBAR_BUTTON_2X_MAXIMIZE 2069
#define IDR_WEBUI_IMAGES_APPS_TOPBAR_BUTTON_2X_MINIMIZE 2070
#define IDR_WEBUI_IMAGES_APPS_BUTTON_BUTTER_BAR_CLOSE_2X 2071
#define IDR_WEBUI_IMAGES_APPS_BUTTON_BUTTER_BAR_CLOSE_HOVER_2X 2072
#define IDR_WEBUI_IMAGES_APPS_BUTTON_BUTTER_BAR_CLOSE_PRESSED_2X 2073
#define IDR_WEBUI_IMAGES_CHECK 2074
#define IDR_WEBUI_IMAGES_CHECKBOX_BLACK 2075
#define IDR_WEBUI_IMAGES_CHECKBOX_WHITE 2076
#define IDR_WEBUI_IMAGES_DISABLED_SELECT 2077
#define IDR_WEBUI_IMAGES_ERROR 2078
#define IDR_WEBUI_IMAGES_SELECT 2079
#define IDR_WEBUI_IMAGES_THROBBER_MEDIUM 2080
#define IDR_WEBUI_IMAGES_THROBBER_SMALL 2081
#define IDR_WEBUI_IMAGES_TRASH 2082
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_CDMA1XRTT 2083
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_3G 2084
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_4G 2085
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_EDGE 2086
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_EVDO 2087
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_GPRS 2088
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_HSPA 2089
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_HSPA_PLUS 2090
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_LTE 2091
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_LTE_ADVANCED 2092
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_ROAMING 2093
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_BADGE_SECURE 2094
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_ICON_ETHERNET 2095
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_ICON_MOBILE 2096
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_ICON_VPN 2097
#define IDR_WEBUI_CR_ELEMENTS_NETWORK_ICON_WIFI 2098
#define IDR_WEBUI_CSS_ACTION_LINK 2200
#define IDR_WEBUI_CSS_ALERT_OVERLAY 2201
#define IDR_WEBUI_CSS_APPS_COMMON 2202
#define IDR_WEBUI_CSS_APPS_TOPBUTTON_BAR 2203
#define IDR_WEBUI_CSS_BUBBLE 2204
#define IDR_WEBUI_CSS_BUBBLE_BUTTON 2205
#define IDR_WEBUI_CSS_BUTTER_BAR 2206
#define IDR_WEBUI_CSS_CHROME 2207
#define IDR_WEBUI_CSS_CONTROLLED_INDICATOR 2208
#define IDR_WEBUI_CSS_DIALOGS 2209
#define IDR_WEBUI_CSS_I18N_PROCESS 2210
#define IDR_WEBUI_CSS_LIST 2211
#define IDR_WEBUI_CSS_MENU 2212
#define IDR_WEBUI_CSS_MENU_BUTTON 2213
#define IDR_WEBUI_CSS_TEXT_DEFAULTS 2214
#define IDR_WEBUI_CSS_TEXT_DEFAULTS_MD 2215
#define IDR_WEBUI_CSS_OVERLAY 2216
#define IDR_WEBUI_CSS_ROBOTO 2217
#define IDR_WEBUI_CSS_SPINNER 2218
#define IDR_WEBUI_CSS_TABLE 2219
#define IDR_WEBUI_CSS_TABS 2220
#define IDR_WEBUI_CSS_THROBBER 2221
#define IDR_WEBUI_CSS_TRASH 2222
#define IDR_WEBUI_CSS_TREE 2223
#define IDR_WEBUI_CSS_WIDGETS 2224
#define IDR_WEBUI_HTML_ACTION_LINK 2225
#define IDR_WEBUI_HTML_ASSERT 2226
#define IDR_WEBUI_HTML_CR 2227
#define IDR_WEBUI_HTML_CR_EVENT_TARGET 2228
#define IDR_WEBUI_HTML_CR_UI 2229
#define IDR_WEBUI_HTML_CR_UI_ALERT_OVERLAY 2230
#define IDR_WEBUI_HTML_CR_UI_COMMAND 2231
#define IDR_WEBUI_HTML_CR_UI_CONTEXT_MENU_BUTTON 2232
#define IDR_WEBUI_HTML_CR_UI_CONTEXT_MENU_HANDLER 2233
#define IDR_WEBUI_HTML_CR_UI_FOCUS_GRID 2234
#define IDR_WEBUI_HTML_CR_UI_FOCUS_MANAGER 2235
#define IDR_WEBUI_HTML_CR_UI_FOCUS_OUTLINE_MANAGER 2236
#define IDR_WEBUI_HTML_CR_UI_FOCUS_ROW 2237
#define IDR_WEBUI_HTML_CR_UI_MENU 2238
#define IDR_WEBUI_HTML_CR_UI_MENU_BUTTON 2239
#define IDR_WEBUI_HTML_CR_UI_MENU_ITEM 2240
#define IDR_WEBUI_HTML_CR_UI_OVERLAY 2241
#define IDR_WEBUI_HTML_CR_UI_POSITION_UTIL 2242
#define IDR_WEBUI_HTML_EVENT_TRACKER 2243
#define IDR_WEBUI_HTML_I18N_TEMPLATE 2244
#define IDR_WEBUI_HTML_LOAD_TIME_DATA 2245
#define IDR_WEBUI_HTML_POLYMER_CONFIG 2246
#define IDR_WEBUI_HTML_I18N_BEHAVIOR 2247
#define IDR_WEBUI_HTML_UTIL 2248
#define IDR_WEBUI_JS_ACTION_LINK 2249
#define IDR_WEBUI_JS_ASSERT 2250
#define IDR_WEBUI_JS_CR 2251
#define IDR_WEBUI_JS_CR_EVENT_TARGET 2252
#define IDR_WEBUI_JS_CR_LINK_CONTROLLER 2253
#define IDR_WEBUI_JS_CR_UI 2254
#define IDR_WEBUI_JS_CR_UI_ALERT_OVERLAY 2255
#define IDR_WEBUI_JS_CR_UI_ARRAY_DATA_MODEL 2256
#define IDR_WEBUI_JS_CR_UI_AUTOCOMPLETE_LIST 2257
#define IDR_WEBUI_JS_CR_UI_BUBBLE 2258
#define IDR_WEBUI_JS_CR_UI_BUBBLE_BUTTON 2259
#define IDR_WEBUI_JS_CR_UI_CARD_SLIDER 2260
#define IDR_WEBUI_JS_CR_UI_COMMAND 2261
#define IDR_WEBUI_JS_CR_UI_CONTEXT_MENU_BUTTON 2262
#define IDR_WEBUI_JS_CR_UI_CONTEXT_MENU_HANDLER 2263
#define IDR_WEBUI_JS_CR_UI_CONTROLLED_INDICATOR 2264
#define IDR_WEBUI_JS_CR_UI_DIALOGS 2265
#define IDR_WEBUI_JS_CR_UI_DRAG_WRAPPER 2266
#define IDR_WEBUI_JS_CR_UI_FOCUS_GRID 2267
#define IDR_WEBUI_JS_CR_UI_FOCUS_MANAGER 2268
#define IDR_WEBUI_JS_CR_UI_FOCUS_OUTLINE_MANAGER 2269
#define IDR_WEBUI_JS_CR_UI_FOCUS_ROW 2270
#define IDR_WEBUI_JS_CR_UI_LIST 2271
#define IDR_WEBUI_JS_CR_UI_LIST_ITEM 2272
#define IDR_WEBUI_JS_CR_UI_LIST_SELECTION_CONTROLLER 2273
#define IDR_WEBUI_JS_CR_UI_LIST_SELECTION_MODEL 2274
#define IDR_WEBUI_JS_CR_UI_LIST_SINGLE_SELECTION_MODEL 2275
#define IDR_WEBUI_JS_CR_UI_MENU 2276
#define IDR_WEBUI_JS_CR_UI_MENU_BUTTON 2277
#define IDR_WEBUI_JS_CR_UI_MENU_ITEM 2278
#define IDR_WEBUI_JS_CR_UI_NODE_UTILS 2279
#define IDR_WEBUI_JS_CR_UI_OVERLAY 2280
#define IDR_WEBUI_JS_CR_UI_PAGE_MANAGER_PAGE 2281
#define IDR_WEBUI_JS_CR_UI_PAGE_MANAGER_PAGE_MANAGER 2282
#define IDR_WEBUI_JS_CR_UI_POSITION_UTIL 2283
#define IDR_WEBUI_JS_CR_UI_SPLITTER 2284
#define IDR_WEBUI_JS_CR_UI_GRID 2285
#define IDR_WEBUI_JS_CR_UI_REPEATING_BUTTON 2286
#define IDR_WEBUI_JS_CR_UI_TABLE 2287
#define IDR_WEBUI_JS_CR_UI_TABLE_COLUMN 2288
#define IDR_WEBUI_JS_CR_UI_TABLE_COLUMN_MODEL 2289
#define IDR_WEBUI_JS_CR_UI_TABLE_HEADER 2290
#define IDR_WEBUI_JS_CR_UI_TABLE_LIST 2291
#define IDR_WEBUI_JS_CR_UI_TABLE_SPLITTER 2292
#define IDR_WEBUI_JS_CR_UI_TABS 2293
#define IDR_WEBUI_JS_CR_UI_TREE 2294
#define IDR_WEBUI_JS_CR_UI_TOUCH_HANDLER 2295
#define IDR_WEBUI_JS_EVENT_TRACKER 2296
#define IDR_WEBUI_JS_I18N_TEMPLATE_NO_PROCESS 2297
#define IDR_WEBUI_JS_LOAD_TIME_DATA 2298
#define IDR_WEBUI_JS_MEDIA_COMMON 2299
#define IDR_WEBUI_JS_PARSE_HTML_SUBSET 2300
#define IDR_WEBUI_JS_POLYMER_CONFIG 2301
#define IDR_WEBUI_JS_I18N_BEHAVIOR 2302
#define IDR_WEBUI_JS_UTIL 2303
#define IDR_WEBUI_JS_WEBUI_RESOURCE_TEST 2304
#define IDR_WEBUI_CSS_UI_ACCOUNT_TWEAKS 2305
#define IDR_WEBUI_HTML_UI_ACCOUNT_TWEAKS 2306
#define IDR_WEBUI_JS_UI_ACCOUNT_TWEAKS 2307
#define IDR_CR_ELEMENTS_CR_DEMO_ELEMENT_CSS 2308
#define IDR_CR_ELEMENTS_CR_DEMO_ELEMENT_HTML 2309
#define IDR_CR_ELEMENTS_CR_DEMO_ELEMENT_JS 2310
#define IDR_CR_ELEMENTS_CR_DEMO_CONFIG_JS 2311
#define IDR_CR_ELEMENTS_CR_DEMO_PAGE_HTML 2312
#define IDR_CR_ELEMENTS_CR_EVENTS_HTML 2313
#define IDR_CR_ELEMENTS_CR_EVENTS_JS 2314
#define IDR_CR_ELEMENTS_CR_EXPAND_BUTTON_CSS 2315
#define IDR_CR_ELEMENTS_CR_EXPAND_BUTTON_HTML 2316
#define IDR_CR_ELEMENTS_CR_EXPAND_BUTTON_JS 2317
#define IDR_CR_ELEMENTS_CR_NETWORK_ICON_CSS 2318
#define IDR_CR_ELEMENTS_CR_NETWORK_ICON_HTML 2319
#define IDR_CR_ELEMENTS_CR_NETWORK_ICON_JS 2320
#define IDR_CR_ELEMENTS_CR_NETWORK_LIST_CSS 2321
#define IDR_CR_ELEMENTS_CR_NETWORK_LIST_HTML 2322
#define IDR_CR_ELEMENTS_CR_NETWORK_LIST_JS 2323
#define IDR_CR_ELEMENTS_CR_NETWORK_LIST_ITEM_CSS 2324
#define IDR_CR_ELEMENTS_CR_NETWORK_LIST_ITEM_HTML 2325
#define IDR_CR_ELEMENTS_CR_NETWORK_LIST_ITEM_JS 2326
#define IDR_CR_ELEMENTS_CR_NETWORK_SELECT_CSS 2327
#define IDR_CR_ELEMENTS_CR_NETWORK_SELECT_HTML 2328
#define IDR_CR_ELEMENTS_CR_NETWORK_SELECT_JS 2329
#define IDR_CR_ELEMENTS_CR_ONC_TYPES_HTML 2330
#define IDR_CR_ELEMENTS_CR_ONC_TYPES_JS 2331
#define IDR_CR_ELEMENTS_CR_POLICY_INDICATOR_CSS 2332
#define IDR_CR_ELEMENTS_CR_POLICY_INDICATOR_BEHAVIOR_HTML 2333
#define IDR_CR_ELEMENTS_CR_POLICY_INDICATOR_BEHAVIOR_JS 2334
#define IDR_CR_ELEMENTS_CR_POLICY_NETWORK_BEHAVIOR_HTML 2335
#define IDR_CR_ELEMENTS_CR_POLICY_NETWORK_BEHAVIOR_JS 2336
#define IDR_CR_ELEMENTS_CR_POLICY_NETWORK_INDICATOR_JS 2337
#define IDR_CR_ELEMENTS_CR_POLICY_NETWORK_INDICATOR_HTML 2338
#define IDR_CR_ELEMENTS_CR_POLICY_PREF_BEHAVIOR_HTML 2339
#define IDR_CR_ELEMENTS_CR_POLICY_PREF_BEHAVIOR_JS 2340
#define IDR_CR_ELEMENTS_CR_POLICY_PREF_INDICATOR_JS 2341
#define IDR_CR_ELEMENTS_CR_POLICY_PREF_INDICATOR_HTML 2342
#define IDR_CR_ELEMENTS_CR_SEARCH_FIELD_CSS 2343
#define IDR_CR_ELEMENTS_CR_SEARCH_FIELD_HTML 2344
#define IDR_CR_ELEMENTS_CR_SEARCH_FIELD_JS 2345
#define IDR_CR_ELEMENTS_SHARED_CSS 2346
#define IDR_POLYMER_1_0_FONT_ROBOTO_ROBOTO_HTML 2347
#define IDR_POLYMER_1_0_IRON_A11Y_KEYS_BEHAVIOR_IRON_A11Y_KEYS_BEHAVIOR_EXTRACTED_JS 2348
#define IDR_POLYMER_1_0_IRON_A11Y_KEYS_BEHAVIOR_IRON_A11Y_KEYS_BEHAVIOR_HTML 2349
#define IDR_POLYMER_1_0_IRON_A11Y_KEYS_IRON_A11Y_KEYS_EXTRACTED_JS 2350
#define IDR_POLYMER_1_0_IRON_A11Y_KEYS_IRON_A11Y_KEYS_HTML 2351
#define IDR_POLYMER_1_0_IRON_BEHAVIORS_IRON_BUTTON_STATE_EXTRACTED_JS 2352
#define IDR_POLYMER_1_0_IRON_BEHAVIORS_IRON_BUTTON_STATE_HTML 2353
#define IDR_POLYMER_1_0_IRON_BEHAVIORS_IRON_CONTROL_STATE_EXTRACTED_JS 2354
#define IDR_POLYMER_1_0_IRON_BEHAVIORS_IRON_CONTROL_STATE_HTML 2355
#define IDR_POLYMER_1_0_IRON_CHECKED_ELEMENT_BEHAVIOR_IRON_CHECKED_ELEMENT_BEHAVIOR_EXTRACTED_JS 2356
#define IDR_POLYMER_1_0_IRON_CHECKED_ELEMENT_BEHAVIOR_IRON_CHECKED_ELEMENT_BEHAVIOR_HTML 2357
#define IDR_POLYMER_1_0_IRON_COLLAPSE_IRON_COLLAPSE_EXTRACTED_JS 2358
#define IDR_POLYMER_1_0_IRON_COLLAPSE_IRON_COLLAPSE_HTML 2359
#define IDR_POLYMER_1_0_IRON_DROPDOWN_IRON_DROPDOWN_EXTRACTED_JS 2360
#define IDR_POLYMER_1_0_IRON_DROPDOWN_IRON_DROPDOWN_SCROLL_MANAGER_EXTRACTED_JS 2361
#define IDR_POLYMER_1_0_IRON_DROPDOWN_IRON_DROPDOWN_SCROLL_MANAGER_HTML 2362
#define IDR_POLYMER_1_0_IRON_DROPDOWN_IRON_DROPDOWN_HTML 2363
#define IDR_POLYMER_1_0_IRON_FIT_BEHAVIOR_IRON_FIT_BEHAVIOR_EXTRACTED_JS 2364
#define IDR_POLYMER_1_0_IRON_FIT_BEHAVIOR_IRON_FIT_BEHAVIOR_HTML 2365
#define IDR_POLYMER_1_0_IRON_FLEX_LAYOUT_CLASSES_IRON_FLEX_LAYOUT_HTML 2366
#define IDR_POLYMER_1_0_IRON_FLEX_LAYOUT_CLASSES_IRON_SHADOW_FLEX_LAYOUT_HTML 2367
#define IDR_POLYMER_1_0_IRON_FLEX_LAYOUT_IRON_FLEX_LAYOUT_HTML 2368
#define IDR_POLYMER_1_0_IRON_FORM_ELEMENT_BEHAVIOR_IRON_FORM_ELEMENT_BEHAVIOR_EXTRACTED_JS 2369
#define IDR_POLYMER_1_0_IRON_FORM_ELEMENT_BEHAVIOR_IRON_FORM_ELEMENT_BEHAVIOR_HTML 2370
#define IDR_POLYMER_1_0_IRON_ICON_IRON_ICON_EXTRACTED_JS 2371
#define IDR_POLYMER_1_0_IRON_ICON_IRON_ICON_HTML 2372
#define IDR_POLYMER_1_0_IRON_ICONS_AV_ICONS_HTML 2373
#define IDR_POLYMER_1_0_IRON_ICONS_COMMUNICATION_ICONS_HTML 2374
#define IDR_POLYMER_1_0_IRON_ICONS_DEVICE_ICONS_HTML 2375
#define IDR_POLYMER_1_0_IRON_ICONS_HARDWARE_ICONS_HTML 2376
#define IDR_POLYMER_1_0_IRON_ICONS_IMAGE_ICONS_HTML 2377
#define IDR_POLYMER_1_0_IRON_ICONS_IRON_ICONS_HTML 2378
#define IDR_POLYMER_1_0_IRON_ICONS_NOTIFICATION_ICONS_HTML 2379
#define IDR_POLYMER_1_0_IRON_ICONS_PLACES_ICONS_HTML 2380
#define IDR_POLYMER_1_0_IRON_ICONS_SOCIAL_ICONS_HTML 2381
#define IDR_POLYMER_1_0_IRON_ICONSET_SVG_IRON_ICONSET_SVG_EXTRACTED_JS 2382
#define IDR_POLYMER_1_0_IRON_ICONSET_SVG_IRON_ICONSET_SVG_HTML 2383
#define IDR_POLYMER_1_0_IRON_INPUT_IRON_INPUT_EXTRACTED_JS 2384
#define IDR_POLYMER_1_0_IRON_INPUT_IRON_INPUT_HTML 2385
#define IDR_POLYMER_1_0_IRON_LIST_IRON_LIST_EXTRACTED_JS 2386
#define IDR_POLYMER_1_0_IRON_LIST_IRON_LIST_HTML 2387
#define IDR_POLYMER_1_0_IRON_MEDIA_QUERY_IRON_MEDIA_QUERY_EXTRACTED_JS 2388
#define IDR_POLYMER_1_0_IRON_MEDIA_QUERY_IRON_MEDIA_QUERY_HTML 2389
#define IDR_POLYMER_1_0_IRON_MENU_BEHAVIOR_IRON_MENU_BEHAVIOR_EXTRACTED_JS 2390
#define IDR_POLYMER_1_0_IRON_MENU_BEHAVIOR_IRON_MENU_BEHAVIOR_HTML 2391
#define IDR_POLYMER_1_0_IRON_MENU_BEHAVIOR_IRON_MENUBAR_BEHAVIOR_EXTRACTED_JS 2392
#define IDR_POLYMER_1_0_IRON_MENU_BEHAVIOR_IRON_MENUBAR_BEHAVIOR_HTML 2393
#define IDR_POLYMER_1_0_IRON_META_IRON_META_EXTRACTED_JS 2394
#define IDR_POLYMER_1_0_IRON_META_IRON_META_HTML 2395
#define IDR_POLYMER_1_0_IRON_OVERLAY_BEHAVIOR_IRON_OVERLAY_BACKDROP_EXTRACTED_JS 2396
#define IDR_POLYMER_1_0_IRON_OVERLAY_BEHAVIOR_IRON_OVERLAY_BACKDROP_HTML 2397
#define IDR_POLYMER_1_0_IRON_OVERLAY_BEHAVIOR_IRON_OVERLAY_BEHAVIOR_EXTRACTED_JS 2398
#define IDR_POLYMER_1_0_IRON_OVERLAY_BEHAVIOR_IRON_OVERLAY_BEHAVIOR_HTML 2399
#define IDR_POLYMER_1_0_IRON_OVERLAY_BEHAVIOR_IRON_OVERLAY_MANAGER_EXTRACTED_JS 2400
#define IDR_POLYMER_1_0_IRON_OVERLAY_BEHAVIOR_IRON_OVERLAY_MANAGER_HTML 2401
#define IDR_POLYMER_1_0_IRON_PAGES_IRON_PAGES_EXTRACTED_JS 2402
#define IDR_POLYMER_1_0_IRON_PAGES_IRON_PAGES_HTML 2403
#define IDR_POLYMER_1_0_IRON_RANGE_BEHAVIOR_IRON_RANGE_BEHAVIOR_EXTRACTED_JS 2404
#define IDR_POLYMER_1_0_IRON_RANGE_BEHAVIOR_IRON_RANGE_BEHAVIOR_HTML 2405
#define IDR_POLYMER_1_0_IRON_RESIZABLE_BEHAVIOR_IRON_RESIZABLE_BEHAVIOR_EXTRACTED_JS 2406
#define IDR_POLYMER_1_0_IRON_RESIZABLE_BEHAVIOR_IRON_RESIZABLE_BEHAVIOR_HTML 2407
#define IDR_POLYMER_1_0_IRON_SELECTOR_IRON_MULTI_SELECTABLE_EXTRACTED_JS 2408
#define IDR_POLYMER_1_0_IRON_SELECTOR_IRON_MULTI_SELECTABLE_HTML 2409
#define IDR_POLYMER_1_0_IRON_SELECTOR_IRON_SELECTABLE_EXTRACTED_JS 2410
#define IDR_POLYMER_1_0_IRON_SELECTOR_IRON_SELECTABLE_HTML 2411
#define IDR_POLYMER_1_0_IRON_SELECTOR_IRON_SELECTION_EXTRACTED_JS 2412
#define IDR_POLYMER_1_0_IRON_SELECTOR_IRON_SELECTION_HTML 2413
#define IDR_POLYMER_1_0_IRON_SELECTOR_IRON_SELECTOR_EXTRACTED_JS 2414
#define IDR_POLYMER_1_0_IRON_SELECTOR_IRON_SELECTOR_HTML 2415
#define IDR_POLYMER_1_0_IRON_TEST_HELPERS_IRON_TEST_HELPERS_EXTRACTED_JS 2416
#define IDR_POLYMER_1_0_IRON_TEST_HELPERS_IRON_TEST_HELPERS_HTML 2417
#define IDR_POLYMER_1_0_IRON_TEST_HELPERS_MOCK_INTERACTIONS_HTML 2418
#define IDR_POLYMER_1_0_IRON_TEST_HELPERS_MOCK_INTERACTIONS_JS 2419
#define IDR_POLYMER_1_0_IRON_TEST_HELPERS_TEST_HELPERS_HTML 2420
#define IDR_POLYMER_1_0_IRON_TEST_HELPERS_TEST_HELPERS_JS 2421
#define IDR_POLYMER_1_0_IRON_VALIDATABLE_BEHAVIOR_IRON_VALIDATABLE_BEHAVIOR_EXTRACTED_JS 2422
#define IDR_POLYMER_1_0_IRON_VALIDATABLE_BEHAVIOR_IRON_VALIDATABLE_BEHAVIOR_HTML 2423
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_FADE_IN_ANIMATION_EXTRACTED_JS 2424
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_FADE_IN_ANIMATION_HTML 2425
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_FADE_OUT_ANIMATION_EXTRACTED_JS 2426
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_FADE_OUT_ANIMATION_HTML 2427
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_HERO_ANIMATION_EXTRACTED_JS 2428
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_HERO_ANIMATION_HTML 2429
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_OPAQUE_ANIMATION_EXTRACTED_JS 2430
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_OPAQUE_ANIMATION_HTML 2431
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_DOWN_ANIMATION_EXTRACTED_JS 2432
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_DOWN_ANIMATION_HTML 2433
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_FROM_LEFT_ANIMATION_EXTRACTED_JS 2434
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_FROM_LEFT_ANIMATION_HTML 2435
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_FROM_RIGHT_ANIMATION_EXTRACTED_JS 2436
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_FROM_RIGHT_ANIMATION_HTML 2437
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_LEFT_ANIMATION_EXTRACTED_JS 2438
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_LEFT_ANIMATION_HTML 2439
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_RIGHT_ANIMATION_EXTRACTED_JS 2440
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_RIGHT_ANIMATION_HTML 2441
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_UP_ANIMATION_EXTRACTED_JS 2442
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_SLIDE_UP_ANIMATION_HTML 2443
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_TRANSFORM_ANIMATION_EXTRACTED_JS 2444
#define IDR_POLYMER_1_0_NEON_ANIMATION_ANIMATIONS_TRANSFORM_ANIMATION_HTML 2445
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATABLE_BEHAVIOR_EXTRACTED_JS 2446
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATABLE_BEHAVIOR_HTML 2447
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATABLE_EXTRACTED_JS 2448
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATABLE_HTML 2449
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATED_PAGES_EXTRACTED_JS 2450
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATED_PAGES_HTML 2451
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATION_BEHAVIOR_EXTRACTED_JS 2452
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATION_BEHAVIOR_HTML 2453
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATION_RUNNER_BEHAVIOR_EXTRACTED_JS 2454
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_ANIMATION_RUNNER_BEHAVIOR_HTML 2455
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_SHARED_ELEMENT_ANIMATABLE_BEHAVIOR_EXTRACTED_JS 2456
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_SHARED_ELEMENT_ANIMATABLE_BEHAVIOR_HTML 2457
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_SHARED_ELEMENT_ANIMATION_BEHAVIOR_EXTRACTED_JS 2458
#define IDR_POLYMER_1_0_NEON_ANIMATION_NEON_SHARED_ELEMENT_ANIMATION_BEHAVIOR_HTML 2459
#define IDR_POLYMER_1_0_NEON_ANIMATION_WEB_ANIMATIONS_HTML 2460
#define IDR_POLYMER_1_0_PAPER_BEHAVIORS_PAPER_BUTTON_BEHAVIOR_EXTRACTED_JS 2461
#define IDR_POLYMER_1_0_PAPER_BEHAVIORS_PAPER_BUTTON_BEHAVIOR_HTML 2462
#define IDR_POLYMER_1_0_PAPER_BEHAVIORS_PAPER_CHECKED_ELEMENT_BEHAVIOR_EXTRACTED_JS 2463
#define IDR_POLYMER_1_0_PAPER_BEHAVIORS_PAPER_CHECKED_ELEMENT_BEHAVIOR_HTML 2464
#define IDR_POLYMER_1_0_PAPER_BEHAVIORS_PAPER_INKY_FOCUS_BEHAVIOR_EXTRACTED_JS 2465
#define IDR_POLYMER_1_0_PAPER_BEHAVIORS_PAPER_INKY_FOCUS_BEHAVIOR_HTML 2466
#define IDR_POLYMER_1_0_PAPER_BEHAVIORS_PAPER_RIPPLE_BEHAVIOR_EXTRACTED_JS 2467
#define IDR_POLYMER_1_0_PAPER_BEHAVIORS_PAPER_RIPPLE_BEHAVIOR_HTML 2468
#define IDR_POLYMER_1_0_PAPER_BUTTON_PAPER_BUTTON_EXTRACTED_JS 2469
#define IDR_POLYMER_1_0_PAPER_BUTTON_PAPER_BUTTON_HTML 2470
#define IDR_POLYMER_1_0_PAPER_CARD_PAPER_CARD_EXTRACTED_JS 2471
#define IDR_POLYMER_1_0_PAPER_CARD_PAPER_CARD_HTML 2472
#define IDR_POLYMER_1_0_PAPER_CHECKBOX_PAPER_CHECKBOX_EXTRACTED_JS 2473
#define IDR_POLYMER_1_0_PAPER_CHECKBOX_PAPER_CHECKBOX_HTML 2474
#define IDR_POLYMER_1_0_PAPER_DIALOG_BEHAVIOR_PAPER_DIALOG_BEHAVIOR_EXTRACTED_JS 2475
#define IDR_POLYMER_1_0_PAPER_DIALOG_BEHAVIOR_PAPER_DIALOG_BEHAVIOR_HTML 2476
#define IDR_POLYMER_1_0_PAPER_DIALOG_BEHAVIOR_PAPER_DIALOG_COMMON_CSS 2477
#define IDR_POLYMER_1_0_PAPER_DIALOG_BEHAVIOR_PAPER_DIALOG_SHARED_STYLES_HTML 2478
#define IDR_POLYMER_1_0_PAPER_DIALOG_PAPER_DIALOG_EXTRACTED_JS 2479
#define IDR_POLYMER_1_0_PAPER_DIALOG_PAPER_DIALOG_HTML 2480
#define IDR_POLYMER_1_0_PAPER_DRAWER_PANEL_PAPER_DRAWER_PANEL_EXTRACTED_JS 2481
#define IDR_POLYMER_1_0_PAPER_DRAWER_PANEL_PAPER_DRAWER_PANEL_HTML 2482
#define IDR_POLYMER_1_0_PAPER_DROPDOWN_MENU_PAPER_DROPDOWN_MENU_EXTRACTED_JS 2483
#define IDR_POLYMER_1_0_PAPER_DROPDOWN_MENU_PAPER_DROPDOWN_MENU_HTML 2484
#define IDR_POLYMER_1_0_PAPER_FAB_PAPER_FAB_EXTRACTED_JS 2485
#define IDR_POLYMER_1_0_PAPER_FAB_PAPER_FAB_HTML 2486
#define IDR_POLYMER_1_0_PAPER_HEADER_PANEL_PAPER_HEADER_PANEL_EXTRACTED_JS 2487
#define IDR_POLYMER_1_0_PAPER_HEADER_PANEL_PAPER_HEADER_PANEL_HTML 2488
#define IDR_POLYMER_1_0_PAPER_ICON_BUTTON_PAPER_ICON_BUTTON_EXTRACTED_JS 2489
#define IDR_POLYMER_1_0_PAPER_ICON_BUTTON_PAPER_ICON_BUTTON_HTML 2490
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_ADDON_BEHAVIOR_EXTRACTED_JS 2491
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_ADDON_BEHAVIOR_HTML 2492
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_BEHAVIOR_EXTRACTED_JS 2493
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_BEHAVIOR_HTML 2494
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_CHAR_COUNTER_EXTRACTED_JS 2495
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_CHAR_COUNTER_HTML 2496
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_CONTAINER_EXTRACTED_JS 2497
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_CONTAINER_HTML 2498
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_ERROR_EXTRACTED_JS 2499
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_ERROR_HTML 2500
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_EXTRACTED_JS 2501
#define IDR_POLYMER_1_0_PAPER_INPUT_PAPER_INPUT_HTML 2502
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ICON_ITEM_EXTRACTED_JS 2503
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ICON_ITEM_HTML 2504
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ITEM_BEHAVIOR_EXTRACTED_JS 2505
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ITEM_BEHAVIOR_HTML 2506
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ITEM_BODY_EXTRACTED_JS 2507
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ITEM_BODY_HTML 2508
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ITEM_EXTRACTED_JS 2509
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ITEM_SHARED_STYLES_HTML 2510
#define IDR_POLYMER_1_0_PAPER_ITEM_PAPER_ITEM_HTML 2511
#define IDR_POLYMER_1_0_PAPER_MATERIAL_PAPER_MATERIAL_EXTRACTED_JS 2512
#define IDR_POLYMER_1_0_PAPER_MATERIAL_PAPER_MATERIAL_SHARED_STYLES_HTML 2513
#define IDR_POLYMER_1_0_PAPER_MATERIAL_PAPER_MATERIAL_HTML 2514
#define IDR_POLYMER_1_0_PAPER_MENU_BUTTON_PAPER_MENU_BUTTON_ANIMATIONS_EXTRACTED_JS 2515
#define IDR_POLYMER_1_0_PAPER_MENU_BUTTON_PAPER_MENU_BUTTON_ANIMATIONS_HTML 2516
#define IDR_POLYMER_1_0_PAPER_MENU_BUTTON_PAPER_MENU_BUTTON_EXTRACTED_JS 2517
#define IDR_POLYMER_1_0_PAPER_MENU_BUTTON_PAPER_MENU_BUTTON_HTML 2518
#define IDR_POLYMER_1_0_PAPER_MENU_PAPER_MENU_EXTRACTED_JS 2519
#define IDR_POLYMER_1_0_PAPER_MENU_PAPER_MENU_SHARED_STYLES_HTML 2520
#define IDR_POLYMER_1_0_PAPER_MENU_PAPER_MENU_HTML 2521
#define IDR_POLYMER_1_0_PAPER_MENU_PAPER_SUBMENU_EXTRACTED_JS 2522
#define IDR_POLYMER_1_0_PAPER_MENU_PAPER_SUBMENU_HTML 2523
#define IDR_POLYMER_1_0_PAPER_PROGRESS_PAPER_PROGRESS_EXTRACTED_JS 2524
#define IDR_POLYMER_1_0_PAPER_PROGRESS_PAPER_PROGRESS_HTML 2525
#define IDR_POLYMER_1_0_PAPER_RADIO_BUTTON_PAPER_RADIO_BUTTON_EXTRACTED_JS 2526
#define IDR_POLYMER_1_0_PAPER_RADIO_BUTTON_PAPER_RADIO_BUTTON_HTML 2527
#define IDR_POLYMER_1_0_PAPER_RADIO_GROUP_PAPER_RADIO_GROUP_EXTRACTED_JS 2528
#define IDR_POLYMER_1_0_PAPER_RADIO_GROUP_PAPER_RADIO_GROUP_HTML 2529
#define IDR_POLYMER_1_0_PAPER_RIPPLE_PAPER_RIPPLE_EXTRACTED_JS 2530
#define IDR_POLYMER_1_0_PAPER_RIPPLE_PAPER_RIPPLE_HTML 2531
#define IDR_POLYMER_1_0_PAPER_SLIDER_PAPER_SLIDER_EXTRACTED_JS 2532
#define IDR_POLYMER_1_0_PAPER_SLIDER_PAPER_SLIDER_HTML 2533
#define IDR_POLYMER_1_0_PAPER_SPINNER_PAPER_SPINNER_BEHAVIOR_EXTRACTED_JS 2534
#define IDR_POLYMER_1_0_PAPER_SPINNER_PAPER_SPINNER_BEHAVIOR_HTML 2535
#define IDR_POLYMER_1_0_PAPER_SPINNER_PAPER_SPINNER_EXTRACTED_JS 2536
#define IDR_POLYMER_1_0_PAPER_SPINNER_PAPER_SPINNER_LITE_EXTRACTED_JS 2537
#define IDR_POLYMER_1_0_PAPER_SPINNER_PAPER_SPINNER_LITE_HTML 2538
#define IDR_POLYMER_1_0_PAPER_SPINNER_PAPER_SPINNER_STYLES_HTML 2539
#define IDR_POLYMER_1_0_PAPER_SPINNER_PAPER_SPINNER_HTML 2540
#define IDR_POLYMER_1_0_PAPER_STYLES_CLASSES_SHADOW_LAYOUT_HTML 2541
#define IDR_POLYMER_1_0_PAPER_STYLES_CLASSES_SHADOW_HTML 2542
#define IDR_POLYMER_1_0_PAPER_STYLES_CLASSES_TYPOGRAPHY_HTML 2543
#define IDR_POLYMER_1_0_PAPER_STYLES_COLOR_HTML 2544
#define IDR_POLYMER_1_0_PAPER_STYLES_DEFAULT_THEME_HTML 2545
#define IDR_POLYMER_1_0_PAPER_STYLES_PAPER_STYLES_CLASSES_HTML 2546
#define IDR_POLYMER_1_0_PAPER_STYLES_PAPER_STYLES_HTML 2547
#define IDR_POLYMER_1_0_PAPER_STYLES_SHADOW_HTML 2548
#define IDR_POLYMER_1_0_PAPER_STYLES_TYPOGRAPHY_HTML 2549
#define IDR_POLYMER_1_0_PAPER_TABS_PAPER_TAB_EXTRACTED_JS 2550
#define IDR_POLYMER_1_0_PAPER_TABS_PAPER_TAB_HTML 2551
#define IDR_POLYMER_1_0_PAPER_TABS_PAPER_TABS_EXTRACTED_JS 2552
#define IDR_POLYMER_1_0_PAPER_TABS_PAPER_TABS_ICONS_HTML 2553
#define IDR_POLYMER_1_0_PAPER_TABS_PAPER_TABS_HTML 2554
#define IDR_POLYMER_1_0_PAPER_TOGGLE_BUTTON_PAPER_TOGGLE_BUTTON_EXTRACTED_JS 2555
#define IDR_POLYMER_1_0_PAPER_TOGGLE_BUTTON_PAPER_TOGGLE_BUTTON_HTML 2556
#define IDR_POLYMER_1_0_PAPER_TOOLBAR_PAPER_TOOLBAR_EXTRACTED_JS 2557
#define IDR_POLYMER_1_0_PAPER_TOOLBAR_PAPER_TOOLBAR_HTML 2558
#define IDR_POLYMER_1_0_PAPER_TOOLTIP_PAPER_TOOLTIP_EXTRACTED_JS 2559
#define IDR_POLYMER_1_0_PAPER_TOOLTIP_PAPER_TOOLTIP_HTML 2560
#define IDR_POLYMER_1_0_POLYMER_POLYMER_EXTRACTED_JS 2561
#define IDR_POLYMER_1_0_POLYMER_POLYMER_MICRO_EXTRACTED_JS 2562
#define IDR_POLYMER_1_0_POLYMER_POLYMER_MICRO_HTML 2563
#define IDR_POLYMER_1_0_POLYMER_POLYMER_MINI_EXTRACTED_JS 2564
#define IDR_POLYMER_1_0_POLYMER_POLYMER_MINI_HTML 2565
#define IDR_POLYMER_1_0_POLYMER_POLYMER_HTML 2566
#define IDR_POLYMER_1_0_WEB_ANIMATIONS_JS_WEB_ANIMATIONS_NEXT_LITE_MIN_JS 2567

#endif  // CEF_INCLUDE_CEF_PACK_RESOURCES_H_
