// Copyright (c) 2016 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file is generated by the make_pack_header.py tool.
//

#ifndef CEF_INCLUDE_CEF_PACK_STRINGS_H_
#define CEF_INCLUDE_CEF_PACK_STRINGS_H_
#pragma once

// ---------------------------------------------------------------------------
// From cef_strings.h:

#define IDS_MENU_BACK 28000
#define IDS_MENU_FORWARD 28001
#define IDS_MENU_RELOAD 28002
#define IDS_MENU_RELOAD_NOCACHE 28003
#define IDS_MENU_STOPLOAD 28004
#define IDS_MENU_UNDO 28005
#define IDS_MENU_REDO 28006
#define IDS_MENU_CUT 28007
#define IDS_MENU_COPY 28008
#define IDS_MENU_PASTE 28009
#define IDS_MENU_DELETE 28010
#define IDS_MENU_SELECT_ALL 28011
#define IDS_MENU_FIND 28012
#define IDS_MENU_PRINT 28013
#define IDS_MENU_VIEW_SOURCE 28014
#define IDS_APP_AUDIO_FILES 28015
#define IDS_APP_IMAGE_FILES 28016
#define IDS_APP_TEXT_FILES 28017
#define IDS_APP_VIDEO_FILES 28018
#define IDS_DEFAULT_PRINT_DOCUMENT_TITLE 28019
#define IDS_PRINT_SPOOL_FAILED_TITLE_TEXT 28020
#define IDS_PRINT_SPOOL_FAILED_ERROR_TEXT 28021
#define IDS_PRINT_INVALID_PRINTER_SETTINGS 28022
#define IDS_UTILITY_PROCESS_EMF_CONVERTOR_NAME 28023
#define IDS_UTILITY_PROCESS_PROXY_RESOLVER_NAME 28024
#define IDS_SPELLCHECK_DICTIONARY 28025
#define IDS_CONTENT_CONTEXT_ADD_TO_DICTIONARY 28026
#define IDS_CONTENT_CONTEXT_NO_SPELLING_SUGGESTIONS 28027
#define IDS_UTILITY_PROCESS_FONT_CACHE_BUILDER_NAME 28028
#define IDS_PLUGIN_HIDE 28029
#define IDS_PLUGIN_NOT_SUPPORTED 28030
#define IDS_PLUGIN_BLOCKED 28031
#define IDS_PLUGIN_NOT_SUPPORTED_METRO 28032
#define IDS_PLUGIN_OUTDATED 28033
#define IDS_PLUGIN_NOT_AUTHORIZED 28034
#define IDS_PLUGIN_BLOCKED_BY_POLICY 28035
#define IDS_CONTENT_CONTEXT_PLUGIN_RUN 28036
#define IDS_CONTENT_CONTEXT_PLUGIN_HIDE 28037
#define IDS_ACCEPT_LANGUAGES 28038
#define IDS_DEFAULT_ENCODING 28039
#define IDS_USES_UNIVERSAL_DETECTOR 28040
#define IDS_STATIC_ENCODING_LIST 28041
#define IDS_ENCODING_DISPLAY_TEMPLATE 28042
#define IDS_ENCODING_UNICODE 28043
#define IDS_ENCODING_WESTERN 28044
#define IDS_ENCODING_SIMP_CHINESE 28045
#define IDS_ENCODING_TRAD_CHINESE 28046
#define IDS_ENCODING_KOREAN 28047
#define IDS_ENCODING_JAPANESE 28048
#define IDS_ENCODING_THAI 28049
#define IDS_ENCODING_CENTRAL_EUROPEAN 28050
#define IDS_ENCODING_CYRILLIC 28051
#define IDS_ENCODING_GREEK 28052
#define IDS_ENCODING_BALTIC 28053
#define IDS_ENCODING_SOUTH_EUROPEAN 28054
#define IDS_ENCODING_NORDIC 28055
#define IDS_ENCODING_CELTIC 28056
#define IDS_ENCODING_ROMANIAN 28057
#define IDS_ENCODING_TURKISH 28058
#define IDS_ENCODING_ARABIC 28059
#define IDS_ENCODING_HEBREW 28060
#define IDS_ENCODING_VIETNAMESE 28061

// ---------------------------------------------------------------------------
// From components_strings.h:

#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE 29160
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_IFRAME 29161
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_NONSTANDARD_URL 29162
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_NONSTANDARD_URL_IFRAME 29163
#define IDS_JAVASCRIPT_MESSAGEBOX_SUPPRESS_OPTION 29164
#define IDS_BEFOREUNLOAD_MESSAGEBOX_TITLE 29165
#define IDS_BEFOREUNLOAD_MESSAGEBOX_FOOTER 29166
#define IDS_BEFOREUNLOAD_MESSAGEBOX_OK_BUTTON_LABEL 29167
#define IDS_BEFOREUNLOAD_MESSAGEBOX_CANCEL_BUTTON_LABEL 29168
#define IDS_BEFORERELOAD_MESSAGEBOX_TITLE 29169
#define IDS_BEFORERELOAD_MESSAGEBOX_FOOTER 29170
#define IDS_BEFORERELOAD_MESSAGEBOX_OK_BUTTON_LABEL 29171
#define IDS_BEFORERELOAD_MESSAGEBOX_CANCEL_BUTTON_LABEL 29172
#define IDS_AUTOFILL_CLEAR_FORM_MENU_ITEM 29173
#define IDS_AUTOFILL_CLEAR_LOCAL_COPY_BUTTON 29174
#define IDS_AUTOFILL_WARNING_FORM_DISABLED 29175
#define IDS_AUTOFILL_WARNING_INSECURE_CONNECTION 29176
#define IDS_AUTOFILL_DELETE_AUTOCOMPLETE_SUGGESTION_CONFIRMATION_BODY 29177
#define IDS_AUTOFILL_DELETE_CREDIT_CARD_SUGGESTION_CONFIRMATION_BODY 29178
#define IDS_AUTOFILL_DELETE_PROFILE_SUGGESTION_CONFIRMATION_BODY 29179
#define IDS_AUTOFILL_CC_AMEX 29180
#define IDS_AUTOFILL_CC_AMEX_SHORT 29181
#define IDS_AUTOFILL_CC_DINERS 29182
#define IDS_AUTOFILL_CC_DISCOVER 29183
#define IDS_AUTOFILL_CC_JCB 29184
#define IDS_AUTOFILL_CC_MASTERCARD 29185
#define IDS_AUTOFILL_CC_UNION_PAY 29186
#define IDS_AUTOFILL_CC_VISA 29187
#define IDS_AUTOFILL_CC_GENERIC 29188
#define IDS_AUTOFILL_ADDRESS_LINE_SEPARATOR 29189
#define IDS_AUTOFILL_ADDRESS_SUMMARY_SEPARATOR 29190
#define IDS_AUTOFILL_FIELD_LABEL_STATE 29191
#define IDS_AUTOFILL_FIELD_LABEL_AREA 29192
#define IDS_AUTOFILL_FIELD_LABEL_COUNTY 29193
#define IDS_AUTOFILL_FIELD_LABEL_DEPARTMENT 29194
#define IDS_AUTOFILL_FIELD_LABEL_DISTRICT 29195
#define IDS_AUTOFILL_FIELD_LABEL_EMIRATE 29196
#define IDS_AUTOFILL_FIELD_LABEL_ISLAND 29197
#define IDS_AUTOFILL_FIELD_LABEL_PARISH 29198
#define IDS_AUTOFILL_FIELD_LABEL_PREFECTURE 29199
#define IDS_AUTOFILL_FIELD_LABEL_PROVINCE 29200
#define IDS_AUTOFILL_FIELD_LABEL_ZIP_CODE 29201
#define IDS_AUTOFILL_FIELD_LABEL_POSTAL_CODE 29202
#define IDS_AUTOFILL_FIELD_LABEL_COUNTRY 29203
#define IDS_AUTOFILL_SHOW_PREDICTIONS_TITLE 29204
#define IDS_AUTOFILL_DIALOG_PRIVACY_POLICY_LINK 29205
#define IDS_AUTOFILL_OPTIONS_POPUP 29206
#define IDS_AUTOFILL_OPTIONS_CONTENT_DESCRIPTION 29207
#define IDS_AUTOFILL_CREDIT_CARD_NOT_SUPPORTED_BY_WALLET 29208
#define IDS_AUTOFILL_CREDIT_CARD_NOT_SUPPORTED_BY_WALLET_FOR_MERCHANT 29209
#define IDS_AUTOFILL_SCAN_CREDIT_CARD 29211
#define IDS_AUTOFILL_PASSWORD_FIELD_SUGGESTIONS_TITLE 29212
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_ACCEPT 29213
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_DENY 29214
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_LOCAL 29215
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_TO_CLOUD 29216
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION 29217
#define IDS_AUTOFILL_CREDIT_CARD_EXPIRATION_DATE_ABBR 29218
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_TRY_AGAIN 29219
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_TRY_AGAIN_WITH_EXPIRATION 29220
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_PERMANENT 29221
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_NETWORK 29222
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_TITLE 29223
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_UPDATE_TITLE 29224
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS 29225
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS_AMEX 29226
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS_EXPIRED 29227
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS_EXPIRED_AMEX 29228
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_STORAGE_CHECKBOX 29229
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_STORAGE_TOOLTIP 29230
#define IDS_AUTOFILL_CARD_UNMASK_CONFIRM_BUTTON 29231
#define IDS_AUTOFILL_CARD_UNMASK_VERIFICATION_IN_PROGRESS 29232
#define IDS_AUTOFILL_CARD_UNMASK_VERIFICATION_SUCCESS 29233
#define IDS_AUTOFILL_CARD_UNMASK_INVALID_EXPIRATION_DATE 29234
#define IDS_AUTOFILL_CARD_UNMASK_EXPIRATION_DATE_SEPARATOR 29235
#define IDS_AUTOFILL_CARD_UNMASK_NEW_CARD_LINK 29236
#define IDS_AUTOFILL_DIALOG_PLACEHOLDER_CVC 29237
#define IDS_BOOKMARK_BAR_FOLDER_NAME 29238
#define IDS_BOOKMARK_BAR_MOBILE_FOLDER_NAME 29240
#define IDS_BOOKMARK_BAR_OTHER_FOLDER_NAME 29241
#define IDS_BOOKMARK_BAR_MANAGED_FOLDER_DOMAIN_NAME 29242
#define IDS_BOOKMARK_BAR_MANAGED_FOLDER_DEFAULT_NAME 29243
#define IDS_BOOKMARK_BAR_SUPERVISED_FOLDER_DEFAULT_NAME 29244
#define IDS_SYNC_TIME_NEVER 29245
#define IDS_SYNC_TIME_JUST_NOW 29246
#define IDS_SETTINGS_TITLE 29247
#define IDS_SETTINGS_HIDE_ADVANCED_SETTINGS 29248
#define IDS_SETTINGS_SHOW_ADVANCED_SETTINGS 29249
#define IDS_NETWORK_PREDICTION_ENABLED_DESCRIPTION 29250
#define IDS_OPTIONS_PROXIES_CONFIGURE_BUTTON 29251
#define IDS_CRASH_TITLE 29252
#define IDS_CRASH_CRASH_COUNT_BANNER_FORMAT 29253
#define IDS_CRASH_CRASH_HEADER_FORMAT 29254
#define IDS_CRASH_CRASH_TIME_FORMAT 29255
#define IDS_CRASH_BUG_LINK_LABEL 29256
#define IDS_CRASH_NO_CRASHES_MESSAGE 29257
#define IDS_CRASH_DISABLED_HEADER 29258
#define IDS_CRASH_UPLOAD_MESSAGE 29259
#define IDS_DATA_REDUCTION_PROXY_TITLE 29260
#define IDS_DATA_REDUCTION_PROXY_BACK_BUTTON 29261
#define IDS_DATA_REDUCTION_PROXY_CONTINUE_BUTTON 29262
#define IDS_DATA_REDUCTION_PROXY_CANNOT_PROXY_HEADING 29263
#define IDS_DATA_REDUCTION_PROXY_CANNOT_PROXY_PRIMARY_PARAGRAPH 29264
#define IDS_DATA_REDUCTION_PROXY_CANNOT_PROXY_SECONDARY_PARAGRAPH 29265
#define IDS_HTTP_POST_WARNING_TITLE 29266
#define IDS_HTTP_POST_WARNING 29267
#define IDS_HTTP_POST_WARNING_RESEND 29268
#define IDS_DOM_DISTILLER_JAVASCRIPT_DISABLED_CONTENT 29269
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_URL 29270
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_ADD 29271
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_ADD_FAILED 29272
#define IDS_DOM_DISTILLER_WEBUI_VIEW_URL 29273
#define IDS_DOM_DISTILLER_WEBUI_VIEW_URL_FAILED 29274
#define IDS_DOM_DISTILLER_WEBUI_REFRESH 29275
#define IDS_DOM_DISTILLER_WEBUI_FETCHING_ENTRIES 29276
#define IDS_DOM_DISTILLER_VIEWER_CLOSE_READER_VIEW 29277
#define IDS_DOM_DISTILLER_VIEWER_FAILED_TO_FIND_ARTICLE_TITLE 29278
#define IDS_DOM_DISTILLER_VIEWER_FAILED_TO_FIND_ARTICLE_CONTENT 29279
#define IDS_DOM_DISTILLER_VIEWER_LOADING_TITLE 29280
#define IDS_DOM_DISTILLER_VIEWER_NO_DATA_CONTENT 29281
#define IDS_DOM_DISTILLER_VIEWER_LOADING_STRING 29282
#define IDS_DOM_DISTILLER_QUALITY_QUESTION 29283
#define IDS_DOM_DISTILLER_QUALITY_ANSWER_YES 29284
#define IDS_DOM_DISTILLER_QUALITY_ANSWER_NO 29285
#define IDS_DOM_DISTILLER_WEBUI_TITLE 29286
#define IDS_FLAGS_ENHANCED_BOOKMARKS_NAME 29287
#define IDS_FLAGS_ENHANCED_BOOKMARKS_DESCRIPTION 29288
#define IDS_ERRORPAGE_NET_BUTTON_DETAILS 29289
#define IDS_ERRORPAGE_NET_BUTTON_HIDE_DETAILS 29290
#define IDS_ERRORPAGES_BUTTON_MORE 29291
#define IDS_ERRORPAGES_BUTTON_LESS 29292
#define IDS_ERRORPAGES_BUTTON_RELOAD 29293
#define IDS_ERRORPAGES_BUTTON_SHOW_SAVED_COPY 29294
#define IDS_ERRORPAGES_BUTTON_SHOW_SAVED_COPY_HELP 29295
#define IDS_ERRORPAGE_FUN_DISABLED 29299
#define IDS_ERRORPAGES_BUTTON_DIAGNOSE 29300
#define IDS_ERRORPAGES_SUGGESTION_VISIT_GOOGLE_CACHE 29302
#define IDS_ERRORPAGES_SUGGESTION_CORRECTED_URL 29303
#define IDS_ERRORPAGES_SUGGESTION_ALTERNATE_URL 29304
#define IDS_ERRORPAGES_SUGGESTION_RELOAD_REPOST_HEADER 29305
#define IDS_ERRORPAGES_SUGGESTION_RELOAD_REPOST_BODY 29306
#define IDS_ERRORPAGES_SUGGESTION_DISABLE_EXTENSION_HEADER 29307
#define IDS_ERRORPAGES_SUGGESTION_CHECK_CONNECTION_HEADER 29308
#define IDS_ERRORPAGES_SUGGESTION_CHECK_CONNECTION_BODY 29309
#define IDS_ERRORPAGES_SUGGESTION_DNS_CONFIG_HEADER 29310
#define IDS_ERRORPAGES_SUGGESTION_DNS_CONFIG_BODY 29311
#define IDS_ERRORPAGES_SUGGESTION_NETWORK_PREDICTION_HEADER 29312
#define IDS_ERRORPAGES_SUGGESTION_FIREWALL_CONFIG_BODY 29313
#define IDS_ERRORPAGES_SUGGESTION_PROXY_CONFIG_HEADER 29314
#define IDS_ERRORPAGES_SUGGESTION_PROXY_CONFIG_BODY 29315
#define IDS_ERRORPAGES_SUGGESTION_LEARNMORE_BODY 29317
#define IDS_ERRORPAGES_SUGGESTION_VIEW_POLICIES_HEADER 29318
#define IDS_ERRORPAGES_SUGGESTION_VIEW_POLICIES_BODY 29319
#define IDS_ERRORPAGES_SUGGESTION_CONTACT_ADMINISTRATOR_BODY 29320
#define IDS_ERRORPAGES_SUGGESTION_GOOGLE_SEARCH 29321
#define IDS_ERRORPAGES_TITLE_NOT_AVAILABLE 29322
#define IDS_ERRORPAGES_TITLE_ACCESS_DENIED 29323
#define IDS_ERRORPAGES_TITLE_NOT_FOUND 29324
#define IDS_ERRORPAGES_TITLE_LOAD_FAILED 29325
#define IDS_ERRORPAGES_TITLE_BLOCKED 29326
#define IDS_ERRORPAGES_HEADING_NOT_AVAILABLE 29327
#define IDS_ERRORPAGES_HEADING_NETWORK_ACCESS_DENIED 29328
#define IDS_ERRORPAGES_HEADING_INTERNET_DISCONNECTED 29329
#define IDS_ERRORPAGES_HEADING_CACHE_READ_FAILURE 29330
#define IDS_ERRORPAGES_HEADING_CONNECTION_INTERRUPTED 29331
#define IDS_ERRORPAGES_HEADING_NOT_FOUND 29332
#define IDS_ERRORPAGES_HEADING_FILE_NOT_FOUND 29333
#define IDS_ERRORPAGES_HEADING_TOO_MANY_REDIRECTS 29334
#define IDS_ERRORPAGES_HEADING_EMPTY_RESPONSE 29335
#define IDS_ERRORPAGES_HEADING_BLOCKED 29336
#define IDS_ERRORPAGES_SUMMARY_NOT_AVAILABLE 29337
#define IDS_ERRORPAGES_SUMMARY_TIMED_OUT 29338
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_RESET 29339
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_CLOSED 29340
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_FAILED 29341
#define IDS_ERRORPAGES_SUMMARY_NETWORK_CHANGED 29342
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_REFUSED 29343
#define IDS_ERRORPAGES_SUMMARY_NAME_NOT_RESOLVED 29344
#define IDS_ERRORPAGES_SUMMARY_DNS_DEFINITION 29345
#define IDS_ERRORPAGES_SUMMARY_ICANN_NAME_COLLISION 29346
#define IDS_ERRORPAGES_SUMMARY_ADDRESS_UNREACHABLE 29347
#define IDS_ERRORPAGES_SUMMARY_FILE_ACCESS_DENIED 29348
#define IDS_ERRORPAGES_SUMMARY_NETWORK_ACCESS_DENIED 29349
#define IDS_ERRORPAGES_SUMMARY_PROXY_CONNECTION_FAILED 29350
#define IDS_ERRORPAGES_SUMMARY_INTERNET_DISCONNECTED_INSTRUCTIONS_TEMPLATE 29352
#define IDS_ERRORPAGES_SUMMARY_INTERNET_DISCONNECTED 29351
#define IDS_ERRORPAGES_SUMMARY_INTERNET_DISCONNECTED_PLATFORM 29353
#define IDS_ERRORPAGES_SUMMARY_INTERNET_DISCONNECTED_PLATFORM_VISTA 29354
#define IDS_ERRORPAGES_SUMMARY_INTERNET_DISCONNECTED_PLATFORM_XP 29355
#define IDS_ERRORPAGES_SUMMARY_CACHE_READ_FAILURE 29356
#define IDS_ERRORPAGES_SUMMARY_NETWORK_IO_SUSPENDED 29357
#define IDS_ERRORPAGES_SUMMARY_NOT_FOUND 29358
#define IDS_ERRORPAGES_SUMMARY_FILE_NOT_FOUND 29359
#define IDS_ERRORPAGES_SUMMARY_TOO_MANY_REDIRECTS 29360
#define IDS_ERRORPAGES_SUMMARY_EMPTY_RESPONSE 29361
#define IDS_ERRORPAGES_SUMMARY_INVALID_RESPONSE 29362
#define IDS_ERRORPAGES_SUMMARY_DNS_PROBE_RUNNING 29363
#define IDS_ERRORPAGES_DETAILS_TIMED_OUT 29364
#define IDS_ERRORPAGES_DETAILS_CONNECTION_CLOSED 29365
#define IDS_ERRORPAGES_DETAILS_CONNECTION_RESET 29366
#define IDS_ERRORPAGES_DETAILS_CONNECTION_REFUSED 29367
#define IDS_ERRORPAGES_DETAILS_CONNECTION_FAILED 29368
#define IDS_ERRORPAGES_DETAILS_NETWORK_CHANGED 29369
#define IDS_ERRORPAGES_DETAILS_NAME_NOT_RESOLVED 29370
#define IDS_ERRORPAGES_DETAILS_ICANN_NAME_COLLISION 29371
#define IDS_ERRORPAGES_DETAILS_ADDRESS_UNREACHABLE 29372
#define IDS_ERRORPAGES_DETAILS_NETWORK_ACCESS_DENIED 29373
#define IDS_ERRORPAGES_DETAILS_FILE_ACCESS_DENIED 29374
#define IDS_ERRORPAGES_DETAILS_PROXY_CONNECTION_FAILED 29375
#define IDS_ERRORPAGES_DETAILS_INTERNET_DISCONNECTED 29376
#define IDS_ERRORPAGES_DETAILS_CACHE_READ_FAILURE 29377
#define IDS_ERRORPAGES_DETAILS_NETWORK_IO_SUSPENDED 29378
#define IDS_ERRORPAGES_DETAILS_FILE_NOT_FOUND 29379
#define IDS_ERRORPAGES_DETAILS_TOO_MANY_REDIRECTS 29380
#define IDS_ERRORPAGES_DETAILS_EMPTY_RESPONSE 29381
#define IDS_ERRORPAGES_DETAILS_RESPONSE_HEADERS_MULTIPLE_CONTENT_LENGTH 29382
#define IDS_ERRORPAGES_DETAILS_RESPONSE_HEADERS_MULTIPLE_CONTENT_DISPOSITION 29383
#define IDS_ERRORPAGES_DETAILS_RESPONSE_HEADERS_MULTIPLE_LOCATION 29384
#define IDS_ERRORPAGES_DETAILS_DNS_PROBE_RUNNING 29385
#define IDS_ERRORPAGES_DETAILS_UNKNOWN 29386
#define IDS_ERRORPAGES_HEADING_ACCESS_DENIED 29387
#define IDS_ERRORPAGES_HEADING_FILE_ACCESS_DENIED 29388
#define IDS_ERRORPAGES_SUMMARY_FORBIDDEN 29389
#define IDS_ERRORPAGES_DETAILS_FORBIDDEN 29390
#define IDS_ERRORPAGES_SUMMARY_GONE 29391
#define IDS_ERRORPAGES_DETAILS_GONE 29392
#define IDS_ERRORPAGES_HEADING_PAGE_NOT_WORKING 29393
#define IDS_ERRORPAGES_DETAILS_INTERNAL_SERVER_ERROR 29394
#define IDS_ERRORPAGES_SUMMARY_WEBSITE_CANNOT_HANDLE_REQUEST 29395
#define IDS_ERRORPAGES_DETAILS_NOT_IMPLEMENTED 29396
#define IDS_ERRORPAGES_DETAILS_BAD_GATEWAY 29397
#define IDS_ERRORPAGES_SUMMARY_SERVICE_UNAVAILABLE 29398
#define IDS_ERRORPAGES_DETAILS_SERVICE_UNAVAILABLE 29399
#define IDS_ERRORPAGES_SUMMARY_GATEWAY_TIMEOUT 29400
#define IDS_ERRORPAGES_DETAILS_GATEWAY_TIMEOUT 29401
#define IDS_ERRORPAGES_SUMMARY_SSL_SECURITY_ERROR 29402
#define IDS_ERRORPAGES_DETAILS_SSL_PROTOCOL_ERROR 29403
#define IDS_ERRORPAGES_DETAILS_SSL_FALLBACK_BEYOND_MINIMUM_VERSION 29404
#define IDS_ERRORPAGES_SUMMARY_SSL_VERSION_OR_CIPHER_MISMATCH 29405
#define IDS_ERRORPAGES_DETAILS_SSL_VERSION_OR_CIPHER_MISMATCH 29406
#define IDS_ERRORPAGES_SUGGESTION_UNSUPPORTED_CIPHER 29407
#define IDS_ERRORPAGES_SUMMARY_PINNING_FAILURE_DETAILS 29408
#define IDS_ERRORPAGES_HEADING_INSECURE_CONNECTION 29409
#define IDS_ERRORPAGES_SUMMARY_BAD_SSL_CLIENT_AUTH_CERT 29410
#define IDS_ERRORPAGES_DETAILS_BAD_SSL_CLIENT_AUTH_CERT 29411
#define IDS_ERRORPAGES_DETAILS_TEMPORARILY_THROTTLED 29412
#define IDS_ERRORPAGES_SUMMARY_TEMPORARY_BACKOFF 29413
#define IDS_ERRORPAGES_DETAILS_TEMPORARY_BACKOFF 29414
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_EXTENSION 29415
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_ADMINISTRATOR 29416
#define IDS_ERRORPAGES_DETAILS_BLOCKED_BY_EXTENSION 29417
#define IDS_ERRORPAGES_DETAILS_BLOCKED_BY_ADMINISTRATOR 29418
#define IDS_ERRORPAGES_DETAILS_BLOCKED_ENROLLMENT_CHECK_PENDING 29419
#define IDS_ERRORPAGES_HTTP_POST_WARNING 29420
#define IDS_FLAGS_UI_LONG_TITLE 29432
#define IDS_FLAGS_UI_TABLE_TITLE 29433
#define IDS_FLAGS_UI_WARNING_HEADER 29434
#define IDS_FLAGS_UI_WARNING_TEXT 29435
#define IDS_FLAGS_UI_PROMOTE_BETA_CHANNEL 29436
#define IDS_FLAGS_UI_PROMOTE_DEV_CHANNEL 29437
#define IDS_FLAGS_UI_DISABLE 29438
#define IDS_FLAGS_UI_ENABLE 29439
#define IDS_FLAGS_UI_RESET_ALL_BUTTON 29440
#define IDS_FLAGS_UI_UNSUPPORTED_TABLE_TITLE 29441
#define IDS_FLAGS_UI_NOT_AVAILABLE 29442
#define IDS_FLAGS_UI_ENABLE_NACL_NAME 29443
#define IDS_FLAGS_UI_RELAUNCH_BUTTON 29444
#define IDS_GENERIC_EXPERIMENT_CHOICE_AUTOMATIC 29446
#define IDS_GENERIC_EXPERIMENT_CHOICE_DEFAULT 29447
#define IDS_GENERIC_EXPERIMENT_CHOICE_ENABLED 29448
#define IDS_GENERIC_EXPERIMENT_CHOICE_DISABLED 29449
#define IDS_HISTORY_ACTION_MENU_DESCRIPTION 29452
#define IDS_HISTORY_BLOCKED_VISIT_TEXT 29453
#define IDS_HISTORY_BROWSERESULTS 29454
#define IDS_HISTORY_CONTINUED 29455
#define IDS_HISTORY_DATE_WITH_RELATIVE_TIME 29456
#define IDS_HISTORY_DELETE_PRIOR_VISITS_CONFIRM_BUTTON 29457
#define IDS_HISTORY_DELETE_PRIOR_VISITS_WARNING 29458
#define IDS_HISTORY_DELETE_PRIOR_VISITS_WARNING_NO_INCOGNITO 29459
#define IDS_HISTORY_ENTRY_BOOKMARKED 29460
#define IDS_HISTORY_ENTRY_SUMMARY 29461
#define IDS_HISTORY_FILTER_ALLOW_ITEMS 29462
#define IDS_HISTORY_FILTER_ALLOWED 29463
#define IDS_HISTORY_FILTER_BLOCK_ITEMS 29464
#define IDS_HISTORY_FILTER_BLOCKED 29465
#define IDS_HISTORY_FOUND_SEARCH_RESULTS 29466
#define IDS_HISTORY_GROUP_BY_DOMAIN_LABEL 29467
#define IDS_HISTORY_HAS_SYNCED_RESULTS 29468
#define IDS_HISTORY_IN_CONTENT_PACK 29469
#define IDS_HISTORY_INTERVAL 29470
#define IDS_HISTORY_LOADING 29471
#define IDS_HISTORY_MORE_FROM_SITE 29472
#define IDS_HISTORY_NEWER 29473
#define IDS_HISTORY_NEWEST 29474
#define IDS_HISTORY_NO_RESULTS 29475
#define IDS_HISTORY_NO_SEARCH_RESULTS 29476
#define IDS_HISTORY_NO_SYNCED_RESULTS 29477
#define IDS_HISTORY_NUMBER_VISITS 29478
#define IDS_HISTORY_OLDER 29479
#define IDS_HISTORY_OPEN_CLEAR_BROWSING_DATA_DIALOG 29480
#define IDS_HISTORY_OTHER_DEVICES_X_MORE 29481
#define IDS_HISTORY_OTHER_SESSIONS_COLLAPSE_SESSION 29482
#define IDS_HISTORY_OTHER_SESSIONS_EXPAND_SESSION 29483
#define IDS_HISTORY_OTHER_SESSIONS_OPEN_ALL 29484
#define IDS_HISTORY_RANGE_ALL_TIME 29485
#define IDS_HISTORY_RANGE_LABEL 29486
#define IDS_HISTORY_RANGE_MONTH 29487
#define IDS_HISTORY_RANGE_NEXT 29488
#define IDS_HISTORY_RANGE_PREVIOUS 29489
#define IDS_HISTORY_RANGE_TODAY 29490
#define IDS_HISTORY_RANGE_WEEK 29491
#define IDS_HISTORY_REMOVE_BOOKMARK 29492
#define IDS_HISTORY_REMOVE_PAGE 29493
#define IDS_HISTORY_REMOVE_SELECTED_ITEMS 29494
#define IDS_HISTORY_SEARCH_BUTTON 29495
#define IDS_HISTORY_SEARCH_RESULT 29496
#define IDS_HISTORY_SEARCH_RESULTS 29497
#define IDS_HISTORY_SEARCHRESULTSFOR 29498
#define IDS_HISTORY_TITLE 29499
#define IDS_HISTORY_UNKNOWN_DEVICE 29500
#define IDS_AUTOCOMPLETE_SEARCH_DESCRIPTION 29501
#define IDS_KEYWORD_SEARCH 29502
#define IDS_EXTENSION_KEYWORD_COMMAND 29503
#define IDS_EMPTY_KEYWORD_VALUE 29504
#define IDS_LINK_FROM_CLIPBOARD 29505
#define IDS_PASSWORD_MANAGER_EMPTY_LOGIN 29506
#define IDS_PASSWORD_MANAGER_SMART_LOCK 29507
#define IDS_PDF_NEED_PASSWORD 29508
#define IDS_PDF_PASSWORD_SUBMIT 29509
#define IDS_PDF_PASSWORD_INVALID 29510
#define IDS_PDF_PAGE_LOADING 29511
#define IDS_PDF_PAGE_LOAD_FAILED 29512
#define IDS_PDF_PAGE_RELOAD_BUTTON 29513
#define IDS_PDF_BOOKMARKS 29514
#define IDS_PDF_TOOLTIP_ROTATE_CW 29515
#define IDS_PDF_TOOLTIP_DOWNLOAD 29516
#define IDS_PDF_TOOLTIP_PRINT 29517
#define IDS_PDF_TOOLTIP_FIT_PAGE 29518
#define IDS_PDF_TOOLTIP_FIT_WIDTH 29519
#define IDS_PDF_TOOLTIP_ZOOM_IN 29520
#define IDS_PDF_TOOLTIP_ZOOM_OUT 29521
#define IDS_PDF_LABEL_PAGE_NUMBER 29522
#define IDS_POLICY_DM_STATUS_SUCCESS 29523
#define IDS_POLICY_DM_STATUS_REQUEST_INVALID 29524
#define IDS_POLICY_DM_STATUS_REQUEST_FAILED 29525
#define IDS_POLICY_DM_STATUS_TEMPORARY_UNAVAILABLE 29526
#define IDS_POLICY_DM_STATUS_HTTP_STATUS_ERROR 29527
#define IDS_POLICY_DM_STATUS_RESPONSE_DECODING_ERROR 29528
#define IDS_POLICY_DM_STATUS_SERVICE_MANAGEMENT_NOT_SUPPORTED 29529
#define IDS_POLICY_DM_STATUS_SERVICE_DEVICE_NOT_FOUND 29530
#define IDS_POLICY_DM_STATUS_SERVICE_MANAGEMENT_TOKEN_INVALID 29531
#define IDS_POLICY_DM_STATUS_SERVICE_ACTIVATION_PENDING 29532
#define IDS_POLICY_DM_STATUS_SERVICE_INVALID_SERIAL_NUMBER 29533
#define IDS_POLICY_DM_STATUS_SERVICE_DEVICE_ID_CONFLICT 29534
#define IDS_POLICY_DM_STATUS_SERVICE_MISSING_LICENSES 29535
#define IDS_POLICY_DM_STATUS_SERVICE_DEPROVISIONED 29536
#define IDS_POLICY_DM_STATUS_SERVICE_POLICY_NOT_FOUND 29537
#define IDS_POLICY_DM_STATUS_UNKNOWN_ERROR 29538
#define IDS_POLICY_DM_STATUS_SERVICE_DOMAIN_MISMATCH 29539
#define IDS_POLICY_VALIDATION_OK 29540
#define IDS_POLICY_VALIDATION_BAD_INITIAL_SIGNATURE 29541
#define IDS_POLICY_VALIDATION_BAD_SIGNATURE 29542
#define IDS_POLICY_VALIDATION_ERROR_CODE_PRESENT 29543
#define IDS_POLICY_VALIDATION_PAYLOAD_PARSE_ERROR 29544
#define IDS_POLICY_VALIDATION_WRONG_POLICY_TYPE 29545
#define IDS_POLICY_VALIDATION_WRONG_SETTINGS_ENTITY_ID 29546
#define IDS_POLICY_VALIDATION_BAD_TIMESTAMP 29547
#define IDS_POLICY_VALIDATION_WRONG_TOKEN 29548
#define IDS_POLICY_VALIDATION_BAD_USERNAME 29549
#define IDS_POLICY_VALIDATION_POLICY_PARSE_ERROR 29550
#define IDS_POLICY_VALIDATION_BAD_KEY_VERIFICATION_SIGNATURE 29551
#define IDS_POLICY_VALIDATION_UNKNOWN_ERROR 29552
#define IDS_POLICY_STORE_STATUS_OK 29553
#define IDS_POLICY_STORE_STATUS_LOAD_ERROR 29554
#define IDS_POLICY_STORE_STATUS_STORE_ERROR 29555
#define IDS_POLICY_STORE_STATUS_PARSE_ERROR 29556
#define IDS_POLICY_STORE_STATUS_SERIALIZE_ERROR 29557
#define IDS_POLICY_STORE_STATUS_VALIDATION_ERROR 29558
#define IDS_POLICY_STORE_STATUS_BAD_STATE 29559
#define IDS_POLICY_STORE_STATUS_UNKNOWN_ERROR 29560
#define IDS_POLICY_ASSOCIATION_STATE_ACTIVE 29561
#define IDS_POLICY_ASSOCIATION_STATE_UNMANAGED 29562
#define IDS_POLICY_ASSOCIATION_STATE_DEPROVISIONED 29563
#define IDS_POLICY_TYPE_ERROR 29564
#define IDS_POLICY_OUT_OF_RANGE_ERROR 29565
#define IDS_POLICY_VALUE_FORMAT_ERROR 29566
#define IDS_POLICY_DEFAULT_SEARCH_DISABLED 29567
#define IDS_POLICY_NOT_SPECIFIED_ERROR 29568
#define IDS_POLICY_SUBKEY_ERROR 29569
#define IDS_POLICY_LIST_ENTRY_ERROR 29570
#define IDS_POLICY_SCHEMA_VALIDATION_ERROR 29571
#define IDS_POLICY_INVALID_SEARCH_URL_ERROR 29572
#define IDS_POLICY_INVALID_PROXY_MODE_ERROR 29573
#define IDS_POLICY_INVALID_UPDATE_URL_ERROR 29574
#define IDS_POLICY_PROXY_MODE_DISABLED_ERROR 29575
#define IDS_POLICY_PROXY_MODE_AUTO_DETECT_ERROR 29576
#define IDS_POLICY_PROXY_MODE_PAC_URL_ERROR 29577
#define IDS_POLICY_PROXY_MODE_FIXED_SERVERS_ERROR 29578
#define IDS_POLICY_PROXY_MODE_SYSTEM_ERROR 29579
#define IDS_POLICY_PROXY_BOTH_SPECIFIED_ERROR 29580
#define IDS_POLICY_PROXY_NEITHER_SPECIFIED_ERROR 29581
#define IDS_POLICY_OVERRIDDEN 29582
#define IDS_POLICY_DEPRECATED 29583
#define IDS_POLICY_VALUE_DEPRECATED 29584
#define IDS_POLICY_LEVEL_ERROR 29589
#define IDS_POLICY_OK 29590
#define IDS_POLICY_UNSET 29591
#define IDS_POLICY_UNKNOWN 29592
#define IDS_POLICY_TITLE 29593
#define IDS_POLICY_FILTER_PLACEHOLDER 29594
#define IDS_POLICY_RELOAD_POLICIES 29595
#define IDS_POLICY_STATUS 29596
#define IDS_POLICY_STATUS_DEVICE 29597
#define IDS_POLICY_STATUS_USER 29598
#define IDS_POLICY_LABEL_DOMAIN 29599
#define IDS_POLICY_LABEL_USERNAME 29600
#define IDS_POLICY_LABEL_CLIENT_ID 29601
#define IDS_POLICY_LABEL_ASSET_ID 29602
#define IDS_POLICY_LABEL_LOCATION 29603
#define IDS_POLICY_LABEL_DIRECTORY_API_ID 29604
#define IDS_POLICY_LABEL_TIME_SINCE_LAST_REFRESH 29605
#define IDS_POLICY_NOT_SPECIFIED 29606
#define IDS_POLICY_NEVER_FETCHED 29607
#define IDS_POLICY_LABEL_REFRESH_INTERVAL 29608
#define IDS_POLICY_LABEL_STATUS 29609
#define IDS_POLICY_SHOW_UNSET 29610
#define IDS_POLICY_NO_POLICIES_SET 29611
#define IDS_POLICY_HEADER_SCOPE 29612
#define IDS_POLICY_HEADER_LEVEL 29613
#define IDS_POLICY_HEADER_NAME 29614
#define IDS_POLICY_HEADER_VALUE 29615
#define IDS_POLICY_HEADER_STATUS 29616
#define IDS_POLICY_HEADER_SOURCE 29617
#define IDS_POLICY_SHOW_EXPANDED_VALUE 29618
#define IDS_POLICY_HIDE_EXPANDED_VALUE 29619
#define IDS_POLICY_SCOPE_USER 29620
#define IDS_POLICY_SCOPE_DEVICE 29621
#define IDS_POLICY_LEVEL_RECOMMENDED 29622
#define IDS_POLICY_LEVEL_MANDATORY 29623
#define IDS_POLICY_INVALID_BOOKMARK 29624
#define IDS_POLICY_SOURCE_ENTERPRISE_DEFAULT 29625
#define IDS_POLICY_SOURCE_CLOUD 29626
#define IDS_POLICY_SOURCE_PLATFORM 29627
#define IDS_POLICY_SOURCE_PUBLIC_SESSION_OVERRIDE 29628
#define IDS_POLICY_RISK_TAG_FULL_ADMIN_ACCESS 29629
#define IDS_POLICY_RISK_TAG_SYSTEM_SECURITY 29630
#define IDS_POLICY_RISK_TAG_WEBSITE_SHARING 29631
#define IDS_POLICY_RISK_TAG_ADMIN_SHARING 29632
#define IDS_POLICY_RISK_TAG_FILTERING 29633
#define IDS_POLICY_RISK_TAG_LOCAL_DATA_ACCESS 29634
#define IDS_POLICY_RISK_TAG_GOOGLE_SHARING 29635
#define IDS_SSL_OPEN_DETAILS_BUTTON 29636
#define IDS_SSL_CLOSE_DETAILS_BUTTON 29637
#define IDS_CLOCK_ERROR_TITLE 29638
#define IDS_CLOCK_ERROR_AHEAD_HEADING 29639
#define IDS_CLOCK_ERROR_BEHIND_HEADING 29640
#define IDS_CLOCK_ERROR_UPDATE_DATE_AND_TIME 29641
#define IDS_CLOCK_ERROR_PRIMARY_PARAGRAPH 29642
#define IDS_CLOCK_ERROR_EXPLANATION 29643
#define IDS_SAFE_BROWSING_PRIVACY_POLICY_URL 29644
#define IDS_SSL_V2_TITLE 29645
#define IDS_SSL_V2_HEADING 29646
#define IDS_SSL_V2_PRIMARY_PARAGRAPH 29647
#define IDS_SSL_NONOVERRIDABLE_MORE 29648
#define IDS_SSL_NONOVERRIDABLE_INVALID 29649
#define IDS_SSL_OVERRIDABLE_SAFETY_BUTTON 29650
#define IDS_SSL_OVERRIDABLE_PROCEED_PARAGRAPH 29651
#define IDS_SSL_RELOAD 29652
#define IDS_SSL_NONOVERRIDABLE_PINNED 29653
#define IDS_SSL_NONOVERRIDABLE_HSTS 29654
#define IDS_SSL_NONOVERRIDABLE_REVOKED 29655
#define IDS_CERT_ERROR_COMMON_NAME_INVALID_DETAILS 29656
#define IDS_CERT_ERROR_COMMON_NAME_INVALID_DESCRIPTION 29657
#define IDS_CERT_ERROR_EXPIRED_DETAILS 29658
#define IDS_CERT_ERROR_EXPIRED_DESCRIPTION 29659
#define IDS_CERT_ERROR_NOT_YET_VALID_DETAILS 29660
#define IDS_CERT_ERROR_NOT_YET_VALID_DESCRIPTION 29661
#define IDS_CERT_ERROR_NOT_VALID_AT_THIS_TIME_DETAILS 29662
#define IDS_CERT_ERROR_NOT_VALID_AT_THIS_TIME_DESCRIPTION 29663
#define IDS_CERT_ERROR_CHAIN_EXPIRED_DETAILS 29664
#define IDS_CERT_ERROR_CHAIN_EXPIRED_DESCRIPTION 29665
#define IDS_CERT_ERROR_AUTHORITY_INVALID_DESCRIPTION 29666
#define IDS_CERT_ERROR_CONTAINS_ERRORS_DETAILS 29667
#define IDS_CERT_ERROR_CONTAINS_ERRORS_DESCRIPTION 29668
#define IDS_CERT_ERROR_UNABLE_TO_CHECK_REVOCATION_DETAILS 29669
#define IDS_CERT_ERROR_UNABLE_TO_CHECK_REVOCATION_DESCRIPTION 29670
#define IDS_CERT_ERROR_NO_REVOCATION_MECHANISM_DETAILS 29671
#define IDS_CERT_ERROR_NO_REVOCATION_MECHANISM_DESCRIPTION 29672
#define IDS_CERT_ERROR_REVOKED_CERT_DETAILS 29673
#define IDS_CERT_ERROR_REVOKED_CERT_DESCRIPTION 29674
#define IDS_CERT_ERROR_INVALID_CERT_DETAILS 29675
#define IDS_CERT_ERROR_INVALID_CERT_DESCRIPTION 29676
#define IDS_CERT_ERROR_WEAK_SIGNATURE_ALGORITHM_DETAILS 29677
#define IDS_CERT_ERROR_WEAK_SIGNATURE_ALGORITHM_DESCRIPTION 29678
#define IDS_CERT_ERROR_WEAK_KEY_DETAILS 29679
#define IDS_CERT_ERROR_WEAK_KEY_DESCRIPTION 29680
#define IDS_CERT_ERROR_NAME_CONSTRAINT_VIOLATION_DETAILS 29681
#define IDS_CERT_ERROR_NAME_CONSTRAINT_VIOLATION_DESCRIPTION 29682
#define IDS_CERT_ERROR_VALIDITY_TOO_LONG_DETAILS 29683
#define IDS_CERT_ERROR_VALIDITY_TOO_LONG_DESCRIPTION 29684
#define IDS_CERT_ERROR_UNKNOWN_ERROR_DETAILS 29685
#define IDS_CERT_ERROR_UNKNOWN_ERROR_DESCRIPTION 29686
#define IDS_CERT_ERROR_SUMMARY_PINNING_FAILURE_DETAILS 29687
#define IDS_CERT_ERROR_SUMMARY_PINNING_FAILURE_DESCRIPTION 29688
#define IDS_CERT_ERROR_AUTHORITY_INVALID_DETAILS 29689
#define IDS_SYNC_BASIC_ENCRYPTION_DATA 29690
#define IDS_SYNC_CONFIGURE_ENCRYPTION 29691
#define IDS_SYNC_DATATYPE_AUTOFILL 29692
#define IDS_SYNC_DATATYPE_BOOKMARKS 29693
#define IDS_SYNC_DATATYPE_PASSWORDS 29694
#define IDS_SYNC_DATATYPE_TABS 29695
#define IDS_SYNC_DATATYPE_TYPED_URLS 29696
#define IDS_SYNC_EMPTY_PASSPHRASE_ERROR 29697
#define IDS_SYNC_ENCRYPTION_SECTION_TITLE 29698
#define IDS_SYNC_ENTER_GOOGLE_PASSPHRASE_BODY 29699
#define IDS_SYNC_FULL_ENCRYPTION_DATA 29700
#define IDS_SYNC_LOGIN_INFO_OUT_OF_DATE 29701
#define IDS_SYNC_LOGIN_SETTING_UP 29702
#define IDS_SYNC_PASSPHRASE_LABEL 29703
#define IDS_SYNC_PASSPHRASE_MISMATCH_ERROR 29704
#define IDS_SYNC_SERVICE_UNAVAILABLE 29705
#define IDS_SYNC_ENTER_PASSPHRASE_BODY_WITH_DATE 29706
#define IDS_SYNC_ENTER_PASSPHRASE_BODY 29707
#define IDS_TRANSLATE_INFOBAR_OPTIONS 29708
#define IDS_TRANSLATE_INFOBAR_OPTIONS_NEVER_TRANSLATE_LANG 29709
#define IDS_TRANSLATE_INFOBAR_OPTIONS_NEVER_TRANSLATE_SITE 29710
#define IDS_TRANSLATE_INFOBAR_OPTIONS_ALWAYS 29711
#define IDS_TRANSLATE_INFOBAR_OPTIONS_REPORT_ERROR 29712
#define IDS_TRANSLATE_INFOBAR_OPTIONS_ABOUT 29713
#define IDS_TRANSLATE_INFOBAR_BEFORE_MESSAGE 29714
#define IDS_TRANSLATE_INFOBAR_ACCEPT 29717
#define IDS_TRANSLATE_INFOBAR_DENY 29718
#define IDS_TRANSLATE_INFOBAR_NEVER_TRANSLATE 29719
#define IDS_TRANSLATE_INFOBAR_ALWAYS_TRANSLATE 29720
#define IDS_TRANSLATE_INFOBAR_TRANSLATING_TO 29721
#define IDS_TRANSLATE_INFOBAR_AFTER_MESSAGE 29722
#define IDS_TRANSLATE_INFOBAR_AFTER_MESSAGE_AUTODETERMINED_SOURCE_LANGUAGE 29723
#define IDS_TRANSLATE_INFOBAR_REVERT 29725
#define IDS_TRANSLATE_INFOBAR_RETRY 29726
#define IDS_TRANSLATE_INFOBAR_ERROR_CANT_CONNECT 29727
#define IDS_TRANSLATE_INFOBAR_ERROR_CANT_TRANSLATE 29728
#define IDS_TRANSLATE_INFOBAR_UNKNOWN_PAGE_LANGUAGE 29729
#define IDS_TRANSLATE_INFOBAR_ERROR_SAME_LANGUAGE 29730
#define IDS_TRANSLATE_INFOBAR_UNSUPPORTED_PAGE_LANGUAGE 29731
#define IDS_BOOKMARK_BAR_UNDO 29733
#define IDS_BOOKMARK_BAR_REDO 29734
#define IDS_BOOKMARK_BAR_UNDO_ADD 29735
#define IDS_BOOKMARK_BAR_REDO_ADD 29736
#define IDS_BOOKMARK_BAR_UNDO_DELETE 29737
#define IDS_BOOKMARK_BAR_REDO_DELETE 29738
#define IDS_BOOKMARK_BAR_UNDO_EDIT 29739
#define IDS_BOOKMARK_BAR_REDO_EDIT 29740
#define IDS_BOOKMARK_BAR_UNDO_MOVE 29741
#define IDS_BOOKMARK_BAR_REDO_MOVE 29742
#define IDS_BOOKMARK_BAR_UNDO_REORDER 29743
#define IDS_BOOKMARK_BAR_REDO_REORDER 29744
#define IDS_VERSION_UI_TITLE 29745
#define IDS_VERSION_UI_OFFICIAL 29746
#define IDS_VERSION_UI_UNOFFICIAL 29747
#define IDS_VERSION_UI_32BIT 29748
#define IDS_VERSION_UI_64BIT 29749
#define IDS_VERSION_UI_REVISION 29750
#define IDS_VERSION_UI_OS 29751
#define IDS_VERSION_UI_USER_AGENT 29752
#define IDS_VERSION_UI_COMMAND_LINE 29753
#define IDS_VERSION_UI_EXECUTABLE_PATH 29755
#define IDS_VERSION_UI_PROFILE_PATH 29756
#define IDS_VERSION_UI_PATH_NOTFOUND 29757
#define IDS_VERSION_UI_VARIATIONS 29758
#define IDS_CANCEL 29760
#define IDS_CLOSE 29761
#define IDS_DONE 29762
#define IDS_LEARN_MORE 29763
#define IDS_OK 29764
#define IDS_PRINT 29765
#define IDS_ACCNAME_BACK 29766
#define IDS_ACCNAME_FORWARD 29767
#define IDS_ACCNAME_CLOSE 29768
#define IDS_ACCNAME_LOCATION 29769
#define IDS_UTILITY_PROCESS_JSON_PARSER_NAME 29770

// ---------------------------------------------------------------------------
// From content_strings.h:

#define IDS_DETAILS_WITHOUT_SUMMARY_LABEL 18900
#define IDS_SEARCHABLE_INDEX_INTRO 18901
#define IDS_FORM_CALENDAR_CLEAR 18902
#define IDS_FORM_CALENDAR_TODAY 18903
#define IDS_FORM_DATE_FORMAT_DAY_IN_MONTH 18904
#define IDS_FORM_DATE_FORMAT_MONTH 18905
#define IDS_FORM_DATE_FORMAT_YEAR 18906
#define IDS_FORM_SUBMIT_LABEL 18907
#define IDS_FORM_INPUT_ALT 18908
#define IDS_FORM_RESET_LABEL 18909
#define IDS_FORM_FILE_BUTTON_LABEL 18910
#define IDS_FORM_MULTIPLE_FILES_BUTTON_LABEL 18911
#define IDS_FORM_FILE_NO_FILE_LABEL 18912
#define IDS_FORM_FILE_MULTIPLE_UPLOAD 18913
#define IDS_FORM_OTHER_COLOR_LABEL 18914
#define IDS_FORM_OTHER_DATE_LABEL 18915
#define IDS_FORM_OTHER_MONTH_LABEL 18916
#define IDS_FORM_OTHER_TIME_LABEL 18917
#define IDS_FORM_OTHER_WEEK_LABEL 18918
#define IDS_FORM_PLACEHOLDER_FOR_DAY_OF_MONTH_FIELD 18919
#define IDS_FORM_PLACEHOLDER_FOR_MONTH_FIELD 18920
#define IDS_FORM_PLACEHOLDER_FOR_YEAR_FIELD 18921
#define IDS_FORM_SELECT_MENU_LIST_TEXT 18922
#define IDS_FORM_THIS_MONTH_LABEL 18923
#define IDS_FORM_THIS_WEEK_LABEL 18924
#define IDS_FORM_WEEK_NUMBER_LABEL 18925
#define IDS_RECENT_SEARCHES_NONE 18926
#define IDS_RECENT_SEARCHES 18927
#define IDS_RECENT_SEARCHES_CLEAR 18928
#define IDS_AX_CALENDAR_SHOW_MONTH_SELECTOR 18929
#define IDS_AX_CALENDAR_SHOW_NEXT_MONTH 18930
#define IDS_AX_CALENDAR_SHOW_PREVIOUS_MONTH 18931
#define IDS_AX_CALENDAR_WEEK_DESCRIPTION 18932
#define IDS_AX_ROLE_ADDRESS 18933
#define IDS_AX_ROLE_ARTICLE 18934
#define IDS_AX_ROLE_BANNER 18935
#define IDS_AX_ROLE_COMPLEMENTARY 18936
#define IDS_AX_ROLE_CHECK_BOX 18937
#define IDS_AX_ROLE_DESCRIPTION_DETAIL 18938
#define IDS_AX_ROLE_DESCRIPTION_LIST 18939
#define IDS_AX_ROLE_DESCRIPTION_TERM 18940
#define IDS_AX_ROLE_FIGURE 18941
#define IDS_AX_ROLE_FORM 18942
#define IDS_AX_ROLE_HEADING 18946
#define IDS_AX_ROLE_IMAGE_MAP 18947
#define IDS_AX_ROLE_LINK 18948
#define IDS_AX_ROLE_LIST_MARKER 18949
#define IDS_AX_ROLE_MAIN_CONTENT 18950
#define IDS_AX_ROLE_MARK 18951
#define IDS_AX_ROLE_MATH 18952
#define IDS_AX_ROLE_NAVIGATIONAL_LINK 18953
#define IDS_AX_ROLE_REGION 18954
#define IDS_AX_ROLE_SEARCH_BOX 18955
#define IDS_AX_ROLE_STATUS 18956
#define IDS_AX_ROLE_SWITCH 18957
#define IDS_AX_ROLE_WEB_AREA 18958
#define IDS_AX_BUTTON_ACTION_VERB 18959
#define IDS_AX_RADIO_BUTTON_ACTION_VERB 18960
#define IDS_AX_TEXT_FIELD_ACTION_VERB 18961
#define IDS_AX_CHECKED_CHECK_BOX_ACTION_VERB 18962
#define IDS_AX_UNCHECKED_CHECK_BOX_ACTION_VERB 18963
#define IDS_AX_LINK_ACTION_VERB 18964
#define IDS_AX_AM_PM_FIELD_TEXT 18965
#define IDS_AX_DAY_OF_MONTH_FIELD_TEXT 18966
#define IDS_AX_DATE_TIME_FIELD_EMPTY_VALUE_TEXT 18967
#define IDS_AX_HOUR_FIELD_TEXT 18968
#define IDS_AX_MEDIA_DEFAULT 18969
#define IDS_AX_MEDIA_AUDIO_ELEMENT 18970
#define IDS_AX_MEDIA_VIDEO_ELEMENT 18971
#define IDS_AX_MEDIA_MUTE_BUTTON 18972
#define IDS_AX_MEDIA_UNMUTE_BUTTON 18973
#define IDS_AX_MEDIA_PLAY_BUTTON 18974
#define IDS_AX_MEDIA_PAUSE_BUTTON 18975
#define IDS_AX_MEDIA_SLIDER 18976
#define IDS_AX_MEDIA_SLIDER_THUMB 18977
#define IDS_AX_MEDIA_CURRENT_TIME_DISPLAY 18978
#define IDS_AX_MEDIA_TIME_REMAINING_DISPLAY 18979
#define IDS_AX_MEDIA_STATUS_DISPLAY 18980
#define IDS_AX_MEDIA_ENTER_FULL_SCREEN_BUTTON 18981
#define IDS_AX_MEDIA_EXIT_FULL_SCREEN_BUTTON 18982
#define IDS_AX_MEDIA_SHOW_CLOSED_CAPTIONS_BUTTON 18983
#define IDS_AX_MEDIA_HIDE_CLOSED_CAPTIONS_BUTTON 18984
#define IDS_AX_MEDIA_CAST_OFF_BUTTON 18985
#define IDS_AX_MEDIA_CAST_ON_BUTTON 18986
#define IDS_AX_MEDIA_AUDIO_ELEMENT_HELP 18987
#define IDS_AX_MEDIA_VIDEO_ELEMENT_HELP 18988
#define IDS_AX_MEDIA_MUTE_BUTTON_HELP 18989
#define IDS_AX_MEDIA_UNMUTE_BUTTON_HELP 18990
#define IDS_AX_MEDIA_PLAY_BUTTON_HELP 18991
#define IDS_AX_MEDIA_PAUSE_BUTTON_HELP 18992
#define IDS_AX_MEDIA_AUDIO_SLIDER_HELP 18993
#define IDS_AX_MEDIA_VIDEO_SLIDER_HELP 18994
#define IDS_AX_MEDIA_SLIDER_THUMB_HELP 18995
#define IDS_AX_MEDIA_CURRENT_TIME_DISPLAY_HELP 18996
#define IDS_AX_MEDIA_TIME_REMAINING_DISPLAY_HELP 18997
#define IDS_AX_MEDIA_STATUS_DISPLAY_HELP 18998
#define IDS_AX_MEDIA_ENTER_FULL_SCREEN_BUTTON_HELP 18999
#define IDS_AX_MEDIA_EXIT_FULL_SCREEN_BUTTON_HELP 19000
#define IDS_AX_MEDIA_SHOW_CLOSED_CAPTIONS_BUTTON_HELP 19001
#define IDS_AX_MEDIA_HIDE_CLOSED_CAPTIONS_BUTTON_HELP 19002
#define IDS_AX_MEDIA_CAST_OFF_BUTTON_HELP 19003
#define IDS_AX_MEDIA_CAST_ON_BUTTON_HELP 19004
#define IDS_AX_MILLISECOND_FIELD_TEXT 19005
#define IDS_AX_MINUTE_FIELD_TEXT 19006
#define IDS_AX_MONTH_FIELD_TEXT 19007
#define IDS_AX_SECOND_FIELD_TEXT 19008
#define IDS_AX_WEEK_OF_YEAR_FIELD_TEXT 19009
#define IDS_AX_YEAR_FIELD_TEXT 19010
#define IDS_KEYGEN_HIGH_GRADE_KEY 19011
#define IDS_KEYGEN_MED_GRADE_KEY 19012
#define IDS_FORM_INPUT_WEEK_TEMPLATE 19013
#define IDS_FORM_VALIDATION_VALUE_MISSING_MULTIPLE_FILE 19014
#define IDS_FORM_VALIDATION_TYPE_MISMATCH 19015
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY 19016
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY_DOMAIN 19017
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY_LOCAL 19018
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_DOMAIN 19019
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_DOTS 19020
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_LOCAL 19021
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_NO_AT_SIGN 19022
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_MULTIPLE_EMAIL 19023
#define IDS_FORM_VALIDATION_RANGE_UNDERFLOW 19024
#define IDS_FORM_VALIDATION_RANGE_UNDERFLOW_DATETIME 19025
#define IDS_FORM_VALIDATION_RANGE_OVERFLOW 19026
#define IDS_FORM_VALIDATION_RANGE_OVERFLOW_DATETIME 19027
#define IDS_FORM_VALIDATION_BAD_INPUT_DATETIME 19028
#define IDS_FORM_VALIDATION_BAD_INPUT_NUMBER 19029
#define IDS_FORM_VALIDATION_VALUE_MISSING 19030
#define IDS_FORM_VALIDATION_VALUE_MISSING_CHECKBOX 19031
#define IDS_FORM_VALIDATION_VALUE_MISSING_FILE 19032
#define IDS_FORM_VALIDATION_VALUE_MISSING_RADIO 19033
#define IDS_FORM_VALIDATION_VALUE_MISSING_SELECT 19034
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL 19035
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_URL 19036
#define IDS_FORM_VALIDATION_PATTERN_MISMATCH 19037
#define IDS_FORM_VALIDATION_STEP_MISMATCH 19038
#define IDS_FORM_VALIDATION_STEP_MISMATCH_CLOSE_TO_LIMIT 19039
#define IDS_FORM_VALIDATION_TOO_LONG 19040
#define IDS_FORM_VALIDATION_TOO_SHORT 19041
#define IDS_PLUGIN_INITIALIZATION_ERROR 19042

// ---------------------------------------------------------------------------
// From extensions_strings.h:

#define IDS_EXTENSION_CONTAINS_PRIVATE_KEY 26150
#define IDS_EXTENSION_LOAD_ABOUT_PAGE_FAILED 26151
#define IDS_EXTENSION_LOAD_BACKGROUND_SCRIPT_FAILED 26152
#define IDS_EXTENSION_LOAD_BACKGROUND_PAGE_FAILED 26153
#define IDS_EXTENSION_LOAD_ICON_FAILED 26154
#define IDS_EXTENSION_LOAD_LAUNCHER_PAGE_FAILED 26155
#define IDS_EXTENSION_LOAD_OPTIONS_PAGE_FAILED 26156
#define IDS_EXTENSION_LOCALES_NO_DEFAULT_LOCALE_SPECIFIED 26157
#define IDS_EXTENSION_MANIFEST_UNREADABLE 26158
#define IDS_EXTENSION_MANIFEST_INVALID 26159
#define IDS_EXTENSION_PACKAGE_DIRECTORY_ERROR 26160
#define IDS_EXTENSION_PACKAGE_IMAGE_PATH_ERROR 26161
#define IDS_EXTENSION_PACKAGE_IMAGE_ERROR 26162
#define IDS_EXTENSION_PACKAGE_UNZIP_ERROR 26163
#define IDS_LOAD_STATE_PARAMETER_EXTENSION 26164
#define IDS_EXTENSION_PROMPT_WARNING_HOST_AND_SUBDOMAIN 26165
#define IDS_EXTENSION_PROMPT_WARNING_HOST_AND_SUBDOMAIN_LIST 26166
#define IDS_EXTENSION_CANT_INSTALL_POLICY_BLOCKED 26167
#define IDS_EXTENSION_CANT_MODIFY_POLICY_REQUIRED 26168
#define IDS_EXTENSION_CANT_UNINSTALL_POLICY_REQUIRED 26169
#define IDS_EXTENSION_DISABLED_UPDATE_REQUIRED_BY_POLICY 26170
#define IDS_DEVICE_NAME_WITH_PRODUCT_SERIAL 26171
#define IDS_DEVICE_NAME_WITH_PRODUCT_UNKNOWN_VENDOR 26172
#define IDS_DEVICE_NAME_WITH_PRODUCT_UNKNOWN_VENDOR_SERIAL 26173
#define IDS_DEVICE_NAME_WITH_PRODUCT_VENDOR 26174
#define IDS_DEVICE_NAME_WITH_PRODUCT_VENDOR_SERIAL 26175
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_UNKNOWN_VENDOR 26176
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_UNKNOWN_VENDOR_SERIAL 26177
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_VENDOR 26178
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_VENDOR_SERIAL 26179
#define IDS_DEVICE_PERMISSIONS_PROMPT 26180
#define IDS_EXTENSION_USB_DEVICE_PRODUCT_NAME_AND_VENDOR 26181
#define IDS_HID_DEVICE_PERMISSIONS_PROMPT_TITLE 26182
#define IDS_USB_DEVICE_PERMISSIONS_PROMPT_TITLE 26183
#define IDS_EXTENSION_TASK_MANAGER_APPVIEW_TAG_PREFIX 26184
#define IDS_EXTENSION_TASK_MANAGER_EXTENSIONOPTIONS_TAG_PREFIX 26185
#define IDS_EXTENSION_TASK_MANAGER_EXTENSIONVIEW_TAG_PREFIX 26186
#define IDS_EXTENSION_TASK_MANAGER_MIMEHANDLERVIEW_TAG_PREFIX 26187
#define IDS_EXTENSION_TASK_MANAGER_WEBVIEW_TAG_PREFIX 26188
#define IDS_EXTENSION_WARNINGS_NETWORK_DELAY 26189
#define IDS_EXTENSION_WARNINGS_NETWORK_CONFLICT 26190
#define IDS_EXTENSION_WARNINGS_REDIRECT_CONFLICT 26191
#define IDS_EXTENSION_WARNINGS_REQUEST_HEADER_CONFLICT 26192
#define IDS_EXTENSION_WARNINGS_RESPONSE_HEADER_CONFLICT 26193
#define IDS_EXTENSION_WARNINGS_CREDENTIALS_CONFLICT 26194
#define IDS_EXTENSION_WARNINGS_DOWNLOAD_FILENAME_CONFLICT 26195
#define IDS_EXTENSION_WARNING_RELOAD_TOO_FREQUENT 26196
#define IDS_EXTENSION_INSTALL_PROCESS_CRASHED 26197
#define IDS_EXTENSION_PACKAGE_ERROR_CODE 26198
#define IDS_EXTENSION_PACKAGE_ERROR_MESSAGE 26199
#define IDS_EXTENSION_PACKAGE_INSTALL_ERROR 26200
#define IDS_EXTENSION_UNPACK_FAILED 26201
#define IDS_UTILITY_PROCESS_EXTENSION_UNPACKER_NAME 26202
#define IDS_UTILITY_PROCESS_MANIFEST_PARSER_NAME 26203

// ---------------------------------------------------------------------------
// From platform_locale_settings.h:

#define IDS_STANDARD_FONT_FAMILY 10500
#define IDS_FIXED_FONT_FAMILY 10501
#define IDS_FIXED_FONT_FAMILY_ALT_WIN 10502
#define IDS_SERIF_FONT_FAMILY 10503
#define IDS_SANS_SERIF_FONT_FAMILY 10504
#define IDS_CURSIVE_FONT_FAMILY 10505
#define IDS_FANTASY_FONT_FAMILY 10506
#define IDS_PICTOGRAPH_FONT_FAMILY 10507
#define IDS_STANDARD_FONT_FAMILY_CYRILLIC 10508
#define IDS_FIXED_FONT_FAMILY_ARABIC 10509
#define IDS_FIXED_FONT_FAMILY_CYRILLIC 10510
#define IDS_SANS_SERIF_FONT_FAMILY_ARABIC 10511
#define IDS_SERIF_FONT_FAMILY_CYRILLIC 10512
#define IDS_SANS_SERIF_FONT_FAMILY_CYRILLIC 10513
#define IDS_STANDARD_FONT_FAMILY_GREEK 10514
#define IDS_FIXED_FONT_FAMILY_GREEK 10515
#define IDS_SERIF_FONT_FAMILY_GREEK 10516
#define IDS_SANS_SERIF_FONT_FAMILY_GREEK 10517
#define IDS_STANDARD_FONT_FAMILY_JAPANESE 10518
#define IDS_FIXED_FONT_FAMILY_JAPANESE 10519
#define IDS_SERIF_FONT_FAMILY_JAPANESE 10520
#define IDS_SANS_SERIF_FONT_FAMILY_JAPANESE 10521
#define IDS_STANDARD_FONT_FAMILY_KOREAN 10522
#define IDS_FIXED_FONT_FAMILY_KOREAN 10523
#define IDS_SERIF_FONT_FAMILY_KOREAN 10524
#define IDS_SANS_SERIF_FONT_FAMILY_KOREAN 10525
#define IDS_CURSIVE_FONT_FAMILY_KOREAN 10526
#define IDS_STANDARD_FONT_FAMILY_SIMPLIFIED_HAN 10527
#define IDS_FIXED_FONT_FAMILY_SIMPLIFIED_HAN 10528
#define IDS_SERIF_FONT_FAMILY_SIMPLIFIED_HAN 10529
#define IDS_SANS_SERIF_FONT_FAMILY_SIMPLIFIED_HAN 10530
#define IDS_STANDARD_FONT_FAMILY_TRADITIONAL_HAN 10531
#define IDS_FIXED_FONT_FAMILY_TRADITIONAL_HAN 10532
#define IDS_SERIF_FONT_FAMILY_TRADITIONAL_HAN 10533
#define IDS_SANS_SERIF_FONT_FAMILY_TRADITIONAL_HAN 10534
#define IDS_DEFAULT_FONT_SIZE 10535
#define IDS_DEFAULT_FIXED_FONT_SIZE 10536
#define IDS_MINIMUM_FONT_SIZE 10537
#define IDS_MINIMUM_LOGICAL_FONT_SIZE 10538

// ---------------------------------------------------------------------------
// From ui_strings.h:

#define IDS_TIME_SECS 11000
#define IDS_TIME_LONG_SECS 11001
#define IDS_TIME_LONG_SECS_2ND 11002
#define IDS_TIME_MINS 11003
#define IDS_TIME_LONG_MINS 11004
#define IDS_TIME_LONG_MINS_1ST 11005
#define IDS_TIME_LONG_MINS_2ND 11006
#define IDS_TIME_HOURS 11007
#define IDS_TIME_HOURS_1ST 11008
#define IDS_TIME_HOURS_2ND 11009
#define IDS_TIME_DAYS 11010
#define IDS_TIME_DAYS_1ST 11011
#define IDS_TIME_REMAINING_SECS 11012
#define IDS_TIME_REMAINING_LONG_SECS 11013
#define IDS_TIME_REMAINING_MINS 11014
#define IDS_TIME_REMAINING_LONG_MINS 11015
#define IDS_TIME_REMAINING_HOURS 11016
#define IDS_TIME_REMAINING_DAYS 11017
#define IDS_TIME_ELAPSED_SECS 11018
#define IDS_TIME_ELAPSED_MINS 11019
#define IDS_TIME_ELAPSED_HOURS 11020
#define IDS_TIME_ELAPSED_DAYS 11021
#define IDS_PAST_TIME_TODAY 11022
#define IDS_PAST_TIME_YESTERDAY 11023
#define IDS_APP_MENU_EMPTY_SUBMENU 11024
#define IDS_APP_UNTITLED_SHORTCUT_FILE_NAME 11025
#define IDS_APP_SAVEAS_ALL_FILES 11026
#define IDS_APP_SAVEAS_EXTENSION_FORMAT 11027
#define IDS_SELECT_UPLOAD_FOLDER_DIALOG_TITLE 11028
#define IDS_SELECT_FOLDER_DIALOG_TITLE 11031
#define IDS_SAVE_AS_DIALOG_TITLE 11032
#define IDS_OPEN_FILE_DIALOG_TITLE 11033
#define IDS_OPEN_FILES_DIALOG_TITLE 11034
#define IDS_SAVEAS_ALL_FILES 11035
#define IDS_SELECT_UPLOAD_FOLDER_DIALOG_UPLOAD_BUTTON 11036
#define IDS_APP_ACCACTION_PRESS 11037
#define IDS_APP_ACCNAME_CLOSE 11038
#define IDS_APP_ACCNAME_MINIMIZE 11039
#define IDS_APP_ACCNAME_MAXIMIZE 11040
#define IDS_APP_ACCNAME_RESTORE 11041
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLHERE 11042
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLLEFTEDGE 11043
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLRIGHTEDGE 11044
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLHOME 11045
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLEND 11046
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLPAGEUP 11047
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLPAGEDOWN 11048
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLLEFT 11049
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLRIGHT 11050
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLUP 11051
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLDOWN 11052
#define IDS_APP_UNDO 11053
#define IDS_APP_CUT 11054
#define IDS_APP_COPY 11055
#define IDS_APP_PASTE 11056
#define IDS_APP_DELETE 11057
#define IDS_APP_SELECT_ALL 11058
#define IDS_DELETE_BACKWARD 11059
#define IDS_DELETE_FORWARD 11060
#define IDS_DELETE_TO_BEGINNING_OF_LINE 11061
#define IDS_DELETE_TO_END_OF_LINE 11062
#define IDS_DELETE_WORD_BACKWARD 11063
#define IDS_DELETE_WORD_FORWARD 11064
#define IDS_MOVE_DOWN 11065
#define IDS_MOVE_LEFT 11066
#define IDS_MOVE_LEFT_AND_MODIFY_SELECTION 11067
#define IDS_MOVE_RIGHT 11068
#define IDS_MOVE_RIGHT_AND_MODIFY_SELECTION 11069
#define IDS_MOVE_WORD_LEFT 11070
#define IDS_MOVE_WORD_LEFT_AND_MODIFY_SELECTION 11071
#define IDS_MOVE_WORD_RIGHT 11072
#define IDS_MOVE_WORD_RIGHT_AND_MODIFY_SELECTION 11073
#define IDS_MOVE_TO_BEGINNING_OF_LINE 11074
#define IDS_MOVE_TO_BEGINNING_OF_LINE_AND_MODIFY_SELECTION 11075
#define IDS_MOVE_TO_END_OF_LINE 11076
#define IDS_MOVE_TO_END_OF_LINE_AND_MODIFY_SELECTION 11077
#define IDS_MOVE_UP 11078
#define IDS_APP_REDO 11079
#define IDS_APP_OK 11080
#define IDS_APP_CANCEL 11081
#define IDS_APP_CLOSE 11082
#define IDS_APP_ESC_KEY 11083
#define IDS_APP_TAB_KEY 11084
#define IDS_APP_INSERT_KEY 11085
#define IDS_APP_HOME_KEY 11086
#define IDS_APP_DELETE_KEY 11087
#define IDS_APP_END_KEY 11088
#define IDS_APP_PAGEUP_KEY 11089
#define IDS_APP_PAGEDOWN_KEY 11090
#define IDS_APP_LEFT_ARROW_KEY 11091
#define IDS_APP_RIGHT_ARROW_KEY 11092
#define IDS_APP_UP_ARROW_KEY 11093
#define IDS_APP_DOWN_ARROW_KEY 11094
#define IDS_APP_ENTER_KEY 11095
#define IDS_APP_SPACE_KEY 11096
#define IDS_APP_F1_KEY 11097
#define IDS_APP_F11_KEY 11098
#define IDS_APP_BACKSPACE_KEY 11099
#define IDS_APP_COMMA_KEY 11100
#define IDS_APP_PERIOD_KEY 11101
#define IDS_APP_MEDIA_NEXT_TRACK_KEY 11102
#define IDS_APP_MEDIA_PLAY_PAUSE_KEY 11103
#define IDS_APP_MEDIA_PREV_TRACK_KEY 11104
#define IDS_APP_MEDIA_STOP_KEY 11105
#define IDS_APP_CONTROL_MODIFIER 11106
#define IDS_APP_ALT_MODIFIER 11107
#define IDS_APP_SHIFT_MODIFIER 11108
#define IDS_APP_COMMAND_MODIFIER 11109
#define IDS_APP_SEARCH_MODIFIER 11110
#define IDS_APP_BYTES 11111
#define IDS_APP_KIBIBYTES 11112
#define IDS_APP_MEBIBYTES 11113
#define IDS_APP_GIBIBYTES 11114
#define IDS_APP_TEBIBYTES 11115
#define IDS_APP_PEBIBYTES 11116
#define IDS_APP_BYTES_PER_SECOND 11117
#define IDS_APP_KIBIBYTES_PER_SECOND 11118
#define IDS_APP_MEBIBYTES_PER_SECOND 11119
#define IDS_APP_GIBIBYTES_PER_SECOND 11120
#define IDS_APP_TEBIBYTES_PER_SECOND 11121
#define IDS_APP_PEBIBYTES_PER_SECOND 11122
#define IDS_MESSAGE_CENTER_ACCESSIBLE_NAME 11123
#define IDS_MESSAGE_CENTER_NOTIFIER_DISABLE 11124
#define IDS_MESSAGE_CENTER_FOOTER_TITLE 11125
#define IDS_MESSAGE_CENTER_SETTINGS_BUTTON_LABEL 11126
#define IDS_MESSAGE_CENTER_SETTINGS_DIALOG_DESCRIPTION 11128
#define IDS_MESSAGE_CENTER_SETTINGS_DESCRIPTION_MULTIUSER 11129
#define IDS_MESSAGE_CENTER_SETTINGS 11130
#define IDS_MESSAGE_CENTER_CLEAR_ALL 11131
#define IDS_MESSAGE_CENTER_QUIET_MODE_BUTTON_TOOLTIP 11132
#define IDS_MESSAGE_CENTER_NO_MESSAGES 11133
#define IDS_MESSAGE_CENTER_QUIET_MODE 11134
#define IDS_MESSAGE_CENTER_QUIET_MODE_1HOUR 11135
#define IDS_MESSAGE_CENTER_QUIET_MODE_1DAY 11136
#define IDS_MESSAGE_CENTER_CLOSE_NOTIFICATION_BUTTON_ACCESSIBLE_NAME 11137
#define IDS_MESSAGE_NOTIFICATION_SETTINGS_BUTTON_ACCESSIBLE_NAME 11138
#define IDS_APP_LIST_HELP 11141
#define IDS_APP_LIST_OPEN_SETTINGS 11142
#define IDS_APP_LIST_OPEN_FEEDBACK 11143
#define IDS_APP_LIST_BACK 11144
#define IDS_APP_LIST_ALL_APPS 11145
#define IDS_APP_LIST_FOLDER_NAME_PLACEHOLDER 11146
#define IDS_APP_LIST_FOLDER_BUTTON_ACCESSIBILE_NAME 11147
#define IDS_APP_LIST_FOLDER_OPEN_FOLDER_ACCESSIBILE_NAME 11148
#define IDS_APP_LIST_FOLDER_CLOSE_FOLDER_ACCESSIBILE_NAME 11149
#define IDS_APP_LIST_SPEECH_HINT_TEXT 11150
#define IDS_APP_LIST_SPEECH_NETWORK_ERROR_HINT_TEXT 11151

#endif  // CEF_INCLUDE_CEF_PACK_STRINGS_H_
