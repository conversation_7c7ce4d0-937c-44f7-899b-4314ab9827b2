﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="btn_3_Contact" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_3_Contact.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_3_setting" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_3_setting.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_3_Video" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_3_Video.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_4_blue" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_4_blue.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_4_blueleft" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_4_blueleft.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_4_blueright" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_4_blueright.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_4_retreatex" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_4_retreatex.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_close" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_close.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_ddl_selecttype" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_ddl_selecttype.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_max" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_max.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_min" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_min.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="dcf" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\dcf.dll;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="excel_header" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\excel_header.txt;System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089;gb2312</value>
  </data>
  <data name="frm_bg_sub" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\frm_bg_sub1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="gif_loading_16" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\gif_loading_16.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="gif_loading_231" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\gif_loading_231.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="gif_loading_24" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\gif_loading_24.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="gif_loading_32" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\gif_loading_32.gif;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="main" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\main.ico;System.Drawing.Icon, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="weixin_dll_aud" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\weixin_dll_aud;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="weixin_dll_silk" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\weixin_dll_silk.dll;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="weixin_icon_default" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\weixin_icon_default.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="weixin_zip_ChatWebPage" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\weixin_zip_ChatWebPage.zip;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="weixin_zip_exporthtml" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\weixin_zip_exporthtml.zip;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="btn_4_timescreen" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_4_timescreen.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="TimeScreenUpDowm" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\TimeScreenUpDowm.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="TimeScreenUp" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\TimeScreenUp.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="dunpai" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\dunpai.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_1" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\1.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_2" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\2.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="_3" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\3.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_4_white" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_4_white.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_3_more" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_3_more.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="ExportPreview" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\ExportPreview.zip;System.Byte[], mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="icon_export" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\icon_export.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="icon_yuyinhecheng" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\icon_yuyinhecheng.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_feedback" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_feedback.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="cell_false" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\cell_false.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="cell_true" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\cell_true.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="cell_question" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\cell_question.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="homepage" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\homepage.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="nextpage" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\nextpage.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="previouspage" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\previouspage.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="trailerpage" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\trailerpage.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="btn_copy" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\btn_copy.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="icon_qq_group" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\icon_qq_group.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
  <data name="QQNum" type="System.Resources.ResXFileRef, System.Windows.Forms">
    <value>..\Resources\QQNum.png;System.Drawing.Bitmap, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a</value>
  </data>
</root>