﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace iWechatAssistant
{
    public class WechatHelp
    {
        public static bool CheckIs64()
        {
            bool is64 = true;
            try
            {
                var pro = System.Diagnostics.Process.GetProcessesByName("WeChat");
                if (pro == null || pro.Count() == 0)
                {
                    return is64;
                }

                if (Environment.Is64BitOperatingSystem)
                {
                    Common.Log(string.Format("Current os is 64"), "DoSWeHelpWork", true);

                    bool retVal = false;

                    NativeMethods.IsWow64Process(pro[0].Handle, out retVal);

                    if (retVal == true)
                    {
                        Common.Log(string.Format("Current Wechat is 32"), "DoSWeHelpWork", true);
                        is64 = false;
                    }
                    else
                    {
                        Common.Log(string.Format("Current Wechat is 64"), "DoSWeHelpWork", true);
                        is64 = true;
                    }
                }
                else
                {
                    Common.Log(string.Format("Current os is 32"), "DoSWeHelpWork", true);
                    is64 = false;
                }
            }
            catch (Exception)
            {

                throw;
            }
            

            return is64;
        }
    }
}
