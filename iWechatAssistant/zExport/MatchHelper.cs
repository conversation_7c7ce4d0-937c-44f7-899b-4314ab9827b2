﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.IO;
using System.Text.RegularExpressions;

namespace iWechatAssistant
{
    public class MatchHelper
    {
        // Methods
        public static DataTable GetMatchStrings(string strInput, string[] patterns)
        {
            DataTable table = new DataTable("patternResult");
            table.Columns.Add(new DataColumn("index", typeof(int)));
            table.Columns.Add(new DataColumn("pattern", typeof(string)));
            table.Columns.Add(new DataColumn("result", typeof(string)));
            foreach (string str in patterns)
            {
                if (str != string.Empty)
                {
                    for (Match match = new Regex(str, RegexOptions.IgnorePatternWhitespace | RegexOptions.Compiled | RegexOptions.Multiline).Match(strInput); match.Success; match = match.NextMatch())
                    {
                        DataRow row = table.NewRow();
                        row["index"] = match.Index;
                        row["pattern"] = str;
                        row["result"] = match.ToString();
                        table.Rows.Add(row);
                    }
                }
            }
            table.DefaultView.Sort = "index asc";
            return table.DefaultView.ToTable();
        }

        public static DataTable GetMatchStrings(string strInput, string[] patterns, string[] groupNames)
        {
            DataTable table = new DataTable("patternResult");
            table.Columns.Add(new DataColumn("index", typeof(int)));
            table.Columns.Add(new DataColumn("pattern", typeof(string)));
            table.Columns.Add(new DataColumn("result", typeof(string)));
            if (groupNames.Length > 0)
            {
                table.Columns.Add(new DataColumn("flag", typeof(string)));
            }
            foreach (string str in groupNames)
            {
                if (str.Trim() != string.Empty)
                {
                    table.Columns.Add(new DataColumn(str, typeof(string)));
                }
            }
            foreach (string str2 in patterns)
            {
                if (str2 != string.Empty)
                {
                    Match match = new Regex(str2, RegexOptions.IgnorePatternWhitespace | RegexOptions.Compiled | RegexOptions.Multiline).Match(strInput);
                    while (match.Success)
                    {
                        GroupCollection groups = match.Groups;
                        DataRow row = table.NewRow();
                        row["index"] = match.Index;
                        row["pattern"] = str2;
                        row["result"] = match.ToString();
                        string[] strArray3 = groupNames;
                        int index = 0;
                        while (true)
                        {
                            if (index >= strArray3.Length)
                            {
                                table.Rows.Add(row);
                                match = match.NextMatch();
                                break;
                            }
                            string str3 = strArray3[index];
                            if (str3.Trim() != string.Empty)
                            {
                                row[str3] = groups[str3].ToString();
                                if (groups[str3].ToString() != string.Empty)
                                {
                                    row["flag"] = str3;
                                }
                            }
                            index++;
                        }
                    }
                }
            }
            table.DefaultView.Sort = "index asc";
            return table.DefaultView.ToTable();
        }

        public static string RemoveHtmlTag(string strInput)
        {
            char[] trimChars = new char[] { ' ', '\r', '\n' };
            return RemoveMatchString(strInput, "<(.|\n)+?>", "").Replace("<", "").Replace(">", "").Trim(trimChars);
        }

        private static string RemoveMatchString(string strInput, string pattern, string replacement)
        {
            return new Regex(pattern, RegexOptions.Compiled | RegexOptions.Multiline).Replace(strInput, replacement);
        }
    }
}
