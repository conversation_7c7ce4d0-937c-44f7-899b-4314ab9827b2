﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Web;
using System.Windows.Forms;
using System.ComponentModel;
using System.Text.RegularExpressions;

//using Bamboo.ClientLib;
using iTong.CoreFoundation;
using iTong.CoreModule;
using System.Runtime.InteropServices;

namespace iWechatAssistant
{
    public class ExportHelper : IDisposable
    {
        public event EventHandler<ExportProgressEventArgs> ExportProgressEventHandler;

        private Thread mThreadExportHtml = null;
        private Thread mThreadExportFriend = null;
        private WechatHeplerPC mWechatHeplerPC = null;

        private WechatAccInfo mCurrentWAInfo = null;
        private string mStrCurrentBackupDBPath = "";
        private string mStrCurrentBackupDBPassword = "";
        private string mStrCurrentBackupMediaFile = "";
        private bool mCurrentWeChatiOSDevice = true;
        private List<ContentLayout> mLstContentLayout = new List<ContentLayout>();
        private static string mRegular = string.Empty;

        private static string mStrTrialMark = string.Format("【试用版只能导出预览部分内容，购买激活后可导出整条文本信息，请前往{0}激活后再重新导出。】", LanguageInterface.Instance().GetString("Common.SoftwareName"));

        #region "--- 单例 ---"
        //单一例，一个设备返回一个实例
        private static Dictionary<string, ExportHelper> mDictInstances = new Dictionary<string, ExportHelper>(StringComparer.InvariantCultureIgnoreCase);
        // 检测冗余的调用
        private bool disposedValue = false;
        private static object _lockGetInstance = new object();
        WeChatMsgWebPage mWeChatMsgWebPage;

        private static readonly object threadLock_ = new object();

        public ExportHelper(WechatHeplerPC heplerPC, WeChatMsgWebPage webPage)
        {
            mWechatHeplerPC = heplerPC;
            mWeChatMsgWebPage = webPage;
        }

        public static ExportHelper GetInstance(string strKey, WechatHeplerPC heplerPC, WeChatMsgWebPage webPage)
        {
            ExportHelper helper = null;
            bool blnRetry = false;

            lock (_lockGetInstance)
            {
                DO_RETRY:
                try
                {
                    if (mDictInstances.ContainsKey(strKey))
                    {
                        ExportHelper tmpHelper = mDictInstances[strKey];
                        mDictInstances.Remove(strKey);
                        mDictInstances.Add(strKey, new ExportHelper(heplerPC, webPage));
                        //释放资源
                        tmpHelper.Dispose();
                    }
                    else
                    {
                        if (!mDictInstances.ContainsKey(strKey))
                        {
                            mDictInstances.Add(strKey, new ExportHelper(heplerPC, webPage));
                        }
                    }
                    helper = mDictInstances[strKey];
                }
                catch (Exception ex)
                {
                    if (!blnRetry)
                    {
                        blnRetry = true;
                        goto DO_RETRY;
                    }
                }
            }
            return helper;
        }

        #endregion

        #region "--- 释放 ---"

        public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: 显式调用时释放非托管资源                 
                    mDictInstances.Clear();
                    try
                    {
                        if (this.mThreadExportHtml != null && this.mThreadExportHtml.ThreadState != ThreadState.Stopped)
                        {
                            this.mThreadExportHtml.Abort();
                        }
                    }
                    catch { }
                    try
                    {
                        if (this.mThreadExportFriend != null && this.mThreadExportFriend.ThreadState != ThreadState.Stopped)
                        {
                            this.mThreadExportFriend.Abort();
                        }
                    }
                    catch { }
                }
            }
            disposedValue = true;
        }

        #endregion

        #region 解析wxgf

        public delegate void WXLogCB(string log, int len);

        [DllImport("wxgf2Pic.dll", EntryPoint = "decWxgfFunInit", CallingConvention = CallingConvention.Cdecl)]
        public static extern int decWxgfFunInit(string dllPath, WXLogCB LogCB);

        [DllImport("wxgf2Pic.dll", EntryPoint = "isWxgf", CallingConvention = CallingConvention.Cdecl)]
        public static extern byte isWxgf(string dllPath);

        [DllImport("wxgf2Pic.dll", EntryPoint = "wxgfDec", CallingConvention = CallingConvention.Cdecl)]
        public static extern int wxgfDec(string srcPath, string outPath);

        [DllImport("wxgf2Pic.dll", EntryPoint = "decWxgfDir", CallingConvention = CallingConvention.Cdecl)]
        public static extern int decWxgfDir(string encDir, string outDir, int copyOther);
        #endregion

        internal void Export(List<DataGridViewRow> lstRows, List<DateTime> lstTime,
                             string account, string strDeviceName, ExportType exportType,
                             object[] obj, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo)
        {
            try
            {
                mCurrentWAInfo = (WechatAccInfo)obj[0];
                mStrCurrentBackupDBPath = obj[1].ToString();
                mStrCurrentBackupDBPassword = obj[2].ToString();
                mStrCurrentBackupMediaFile = obj[4].ToString();
                mCurrentWeChatiOSDevice = Convert.ToBoolean(obj[5]);

                string selPath = GetSelFolderPath("导出");
                if (string.IsNullOrWhiteSpace(selPath)) return;
                //FolderBrowserDialog fbdWeixin = new FolderBrowserDialog();
                //if (!string.IsNullOrEmpty(IniSetting.GetWCAExportDefaultFolder()))
                //    fbdWeixin.SelectedPath = IniSetting.GetWCAExportDefaultFolder();

                //fbdWeixin.Description = "导出";
                ////导出
                //fbdWeixin.ShowNewFolderButton = true;

                //if (fbdWeixin.ShowDialog() != DialogResult.OK)
                //    return;

                string sAcct = Utility.ReplaceWinIllegalName(account);
                string strPath = Path.Combine(selPath/*fbdWeixin.SelectedPath*/, "微信消息记录-" + sAcct);

                //"wx消息记录"
                //strPath = Path.Combine(strPath, System.DateTime.Now.ToString("yyyyMMddHHmm") + "-" + Utility.ReplaceWinIllegalNameToHtml(strDeviceName)).Replace(" ", "");
                strPath = Path.Combine(strPath, string.Format("{0:yyyyMMddHHmmss}-{1}", DateTime.Now,
                                    Utility.ReplaceWinIllegalNameToHtml(strDeviceName)).Replace(" ", ""));

                while (!Folder.CheckFolder(strPath))
                {
                    int dir = Convert.ToInt32(strPath[0]) + 1;
                    strPath = string.Format("{0}{1}", Convert.ToChar(dir), strPath.Remove(0, 1));
                }

                //object[] objPara = new object[6];
                //objPara[0] = lstRows;
                //objPara[1] = lstTime;
                //objPara[2] = strPath;
                //objPara[3] = exportType;
                //objPara[4] = dicWeChatFriendInfo;
                //objPara[5] = obj[3];
                object[] objPara = new object[] { lstRows,
                                                  lstTime,
                                                  strPath,
                                                  exportType,
                                                  dicWeChatFriendInfo,
                                                  obj[3],
                                                  obj[6]
                                                };
                if (this.mThreadExportHtml != null && this.mThreadExportHtml.ThreadState != ThreadState.Stopped)
                {
                    try
                    {
                        this.mThreadExportHtml.Abort();
                    }
                    catch { }
                }

                this.mThreadExportHtml = new Thread(new ParameterizedThreadStart(DoExport));
                this.mThreadExportHtml.IsBackground = true;
                //this.mThreadExportHtml.SetApartmentState(ApartmentState.STA);
                this.mThreadExportHtml.Start(objPara);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ExportHtml");
            }
        }

        List<string> mLstCurrentExportInfo = new List<string>();

        List<WechatMessageItem>[] listWeMsgItems_ = null;
        private void SortMessageItemDateThread(object objItems)
        {
            object[] aryItems = objItems as object[];
            WechatMessageItem[] aryMsgItems = aryItems[0] as WechatMessageItem[];
            DateTime dtStart = (DateTime)aryItems[1];
            DateTime dtEnd = (DateTime)aryItems[2];
            int iThread = (int)aryItems[3];
            ManualResetEvent e = aryItems[4] as ManualResetEvent;
            string searchText = aryItems[5].ToString();

            DateTime dtMessage = DateTime.Now;
            int iIndex = 0;

            listWeMsgItems_[iThread] = new List<WechatMessageItem>();
            foreach (WechatMessageItem witem in aryMsgItems)
            {
                dtMessage = Convert.ToDateTime(witem.MessageDT.ToString("yyyy-MM-dd HH:mm:ss"));
                if (DateTime.Compare(dtStart, dtMessage) <= 0 && DateTime.Compare(dtEnd, dtMessage) >= 0
                    && witem.MessageContent.ToLowerInvariant().Contains(searchText))
                {
                    listWeMsgItems_[iThread].Add(witem);
                }
            }
            if (e != null)
                e.Set();
        }

        private WechatTalk DoExportThread(WechatTalk tTalk, DateTime dtStart, DateTime dtEnd, string searchText)
        {
            WechatTalk wTalk = new WechatTalk();
            wTalk = tTalk.Clone();

            // Added by Utmost on 2020.06.02
            const int MAXCOUNTS = 1000;
            int nCounts = tTalk.MessageItems.Count;
            bool bIsThread = false;//暂时用就方式
            if (bIsThread)//nCounts > MAXCOUNTS
            {
                int thread_counts = nCounts / MAXCOUNTS;
                if (nCounts % MAXCOUNTS > 0)
                    thread_counts++;

                listWeMsgItems_ = new List<WechatMessageItem>[thread_counts];
                int nSpareCounts = 0;
                List<ManualResetEvent> listEvents = new List<ManualResetEvent>();
                for (int iIndex = 0; iIndex < thread_counts; iIndex++)
                {
                    OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.Exporting,
                        string.Format("正在准备导出第{0}批数据......", iIndex + 1)));

                    ManualResetEvent e = new ManualResetEvent(false);
                    listEvents.Add(e);

                    if (iIndex == (thread_counts - 1))
                        nSpareCounts = nCounts % MAXCOUNTS;
                    else
                        nSpareCounts = MAXCOUNTS;

                    List<WechatMessageItem> listMsgItems = tTalk.MessageItems.GetRange(iIndex * MAXCOUNTS, nSpareCounts);
                    ThreadPool.QueueUserWorkItem(new WaitCallback(SortMessageItemDateThread), new object[] {
                                        listMsgItems.ToArray(),
                                        Convert.ToDateTime(dtStart.ToString("yyyy-MM-dd 00:00:00")),
                                        Convert.ToDateTime(dtEnd.ToString("yyyy-MM-dd 23:59:59")),
                                        iIndex,
                                        e,
                                        searchText
                                    });
                }
                WaitHandle.WaitAll(listEvents.ToArray());

                for (int k = 0; k < listWeMsgItems_.Length; k++)
                {
                    wTalk.MessageItems.AddRange(listWeMsgItems_[k]);
                    listWeMsgItems_[k].Clear();
                }
            }
            else
            {
                listWeMsgItems_ = new List<WechatMessageItem>[1];
                SortMessageItemDateThread(new object[] { tTalk.MessageItems.ToArray(),
                                            Convert.ToDateTime(dtStart.ToString("yyyy-MM-dd 00:00:00")),
                                            Convert.ToDateTime(dtEnd.ToString("yyyy-MM-dd 23:59:59")),
                                            0, null, searchText
                                        });

                wTalk.MessageItems.AddRange(listWeMsgItems_[0]);
                listWeMsgItems_[0].Clear();
            }

            return wTalk;
        }

        private void DoExport(object obj)
        {
            try
            {
                OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.Exporting, "正在准备导出数据......"));

                object[] objPara = (object[])obj;
                List<DataGridViewRow> lstRows = (List<DataGridViewRow>)objPara[0];
                List<DateTime> lstTime = (List<DateTime>)objPara[1];
                string strPath = objPara[2].ToString();
                ExportType eType = ExportType.ExportToAll;
                Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo = (Dictionary<string, WeChatFriendInfo>)objPara[4];
                bool isPreview = (bool)objPara[5];//预览打印
                string searchText = objPara[6].ToString();

                string strHtmlPath = Path.Combine(strPath, "网页格式");
                string strTxtPath = Path.Combine(strPath, "文本格式");
                string strExcelPath = Path.Combine(strPath, "表格格式");
                string strContentPath = Path.Combine(strHtmlPath, "content");

                string strPicture = Path.Combine(strPath, "图片");
                string strVideo = Path.Combine(strPath, "视频");
                string strVoice = Path.Combine(strPath, "语音");

                eType = (ExportType)Enum.Parse(typeof(ExportType), objPara[3].ToString());

                if (eType == ExportType.ExportToAll || eType == ExportType.ExportToHtml)
                {
                    if (!string.IsNullOrEmpty(strHtmlPath) && !Directory.Exists(strHtmlPath) && !Folder.CheckFolder(strHtmlPath))
                    {
                        iTong.Components.tbMessageBox.Show(this, string.Format("导出失败。该路径 {0} 可能无法读写或不存在，请重新选择导出路径。", strHtmlPath), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    if (!string.IsNullOrEmpty(strTxtPath) && !Directory.Exists(strTxtPath) && !Folder.CheckFolder(strTxtPath))
                    {
                        iTong.Components.tbMessageBox.Show(this, string.Format("导出失败。该路径 {0} 可能无法读写或不存在，请重新选择导出路径。", strTxtPath), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    if (!string.IsNullOrEmpty(strExcelPath) && !Directory.Exists(strExcelPath) && !Folder.CheckFolder(strExcelPath))
                    {
                        iTong.Components.tbMessageBox.Show(this, string.Format("导出失败。该路径 {0} 可能无法读写或不存在，请重新选择导出路径。", strExcelPath), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    if (!string.IsNullOrEmpty(strContentPath) && !Directory.Exists(strContentPath) && !Folder.CheckFolder(strContentPath))
                    {
                        iTong.Components.tbMessageBox.Show(this, string.Format("导出失败。该路径 {0} 可能无法读写或不存在，请重新选择导出路径。", strContentPath), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                }

                //Common.Log(string.Format("ExportHelper's DoExport()>>>>>>>>正在准备导出{0}数据......({1})", eType, strPath), true);                              

                List<ExportHtmlMainInfo> lstExportHtmlMainInfo = null;
                if (eType == ExportType.ExportToHtml || eType == ExportType.ExportToAll)
                {
                    lstExportHtmlMainInfo = new List<ExportHtmlMainInfo>();
                    try
                    {
                        //解压Html资源
                        string strFileZipPath = Path.Combine(Folder.TempFolder, "weixin_zip_exporthtml.zip");
                        ReleaseResource(strFileZipPath, iWechatAssistant.Properties.Resources.weixin_zip_exporthtml);

                        if (Utility.unzip(strFileZipPath, strHtmlPath) == 0)
                        {
                            Common.Log("wx导出Html 资源文件解压失败", "DoExport");

                            OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.ExportFailure, "资源文件解压失败......"));
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "DoExportHtml_1");
                        OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.ExportFailure, ex.ToString()));
                    }
                }

                string strFileName = "";
                int intIndex = 0;

                bool isDB = IniSetting.GetIsTalkSaveDB();

                foreach (DataGridViewRow item in lstRows)
                {
                    if (item.Tag == null) continue;

                    intIndex += 1;
                    WechatTalk tTalk = (WechatTalk)item.Tag;
                    WechatTalk wTalk = new WechatTalk();

                    ExportRange er = ExportRange.All;

                    string strAttachment, sName, strFolder;

                    //----------内存方式 时间过滤----------
                    if (!isDB && lstTime.Count > 1)
                    {
                        DateTime dtStart = Convert.ToDateTime(lstTime[0]);
                        DateTime dtEnd = Convert.ToDateTime(lstTime[1]);

                        bool bIsThread = false;
                        if (!bIsThread)
                        {
                            WechatMessageItem[] aryMsgItems = tTalk.MessageItems.ToArray();
                            List<WechatMessageItem> lstTemp = new List<WechatMessageItem>();

                            wTalk = tTalk;
                            wTalk.MessageItems.Clear();

                            DateTime dtMessage = DateTime.Now;
                            foreach (WechatMessageItem witem in aryMsgItems)
                            {
                                dtMessage = Convert.ToDateTime(witem.MessageDT.ToString("yyyy-MM-dd HH:mm:ss"));
                                if (DateTime.Compare(dtStart, dtMessage) <= 0 &&
                                    DateTime.Compare(dtEnd, dtMessage) >= 0 &&
                                    witem.MessageContent.ToLowerInvariant().Contains(searchText))
                                {
                                    lstTemp.Add(witem);
                                }
                            }
                            wTalk.MessageItems.AddRange(lstTemp);
                            lstTemp.Clear();
                        }
                        else
                        {
                            DoExportThread(tTalk, dtStart, dtEnd, searchText);
                        }
                    }

                    WeChatTalkDB db = WeChatTalkDB.GetInstance(mCurrentWAInfo.StrWeChatTalkDBPath);

                    if (isDB)
                    {
                        int nCount = db.GetTalkInfoCount(tTalk.Md5, lstTime, "");
                        double tmpC = Math.Ceiling(Convert.ToDouble(nCount) / Convert.ToDouble(1000));
                        double pTotal = Math.Ceiling(tmpC);

                        for (int i = 0; i < pTotal; i++)
                        {
                            int intPage = 0;
                            int intPageCount = 1000;
                            int currentExcelCount = 0;

                            if (i == 0)
                                er = ExportRange.Head;
                            else if (i + 1 == pTotal)
                                er = ExportRange.Tail;
                            else
                                er = ExportRange.Middle;

                            intPage = nCount - 1000 * (i + 1);
                            if (intPage < 0)
                            {
                                intPageCount = 1000 + intPage;
                                intPage = 0;

                                if (pTotal == 1)
                                    er = ExportRange.All;
                            }

                            currentExcelCount = 1000 * i;

                            tTalk.MessageItems = db.GetTalkInfo(tTalk.Md5, tTalk.NickName,
                                                      lstTime, "", intPage, intPageCount, ExportProgressEventHandler, i + 1, (int)pTotal);

                            bool res = ExportInitDo(lstRows, ref strPath, eType, dicWeChatFriendInfo, isPreview, strHtmlPath, strTxtPath, strExcelPath, strContentPath, strPicture, strVideo, strVoice, ref lstExportHtmlMainInfo, out strFileName, intIndex, tTalk, tTalk, er, out strAttachment, out sName, out strFolder, string.Format("第【{0}/{1}】批 ", i + 1, pTotal), currentExcelCount);

                            if (!res)
                                break;
                        }
                    }
                    else
                    {
                        bool res = ExportInitDo(lstRows, ref strPath, eType, dicWeChatFriendInfo, isPreview, strHtmlPath, strTxtPath, strExcelPath, strContentPath, strPicture, strVideo, strVoice, ref lstExportHtmlMainInfo, out strFileName, intIndex, tTalk, wTalk, er, out strAttachment, out sName, out strFolder);

                        if (!res)
                            break;
                    }
                }

                //------------------------导出 MainHtml------------------------                                
                if ((eType == ExportType.ExportToHtml || eType == ExportType.ExportToAll) &&
                    lstExportHtmlMainInfo != null && lstExportHtmlMainInfo.Count > 0)
                {
                    string strMainHtml = Path.Combine(strHtmlPath, "全部聊天记录.html");
                    lstExportHtmlMainInfo.Sort(new CreateIDComparer(SortType.DESC));
                    IMHtmlNodeHelper.CreateMainHtml("", strMainHtml, lstExportHtmlMainInfo);

                    if (eType == ExportType.ExportToHtml)
                        strPath = strMainHtml;
                }

                if (!string.IsNullOrEmpty(strPath))
                    OpenExportFolder(strPath);

                OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.ExportSuccess, ""));

                // 删除所有临时文件
                WechatHeplerPC.DeleteXmlTempDir();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoExportHtml");
                OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.ExportFailure, ""));
            }
        }

        private bool ExportInitDo(List<DataGridViewRow> lstRows, ref string strPath, ExportType eType, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, bool isPreview, string strHtmlPath, string strTxtPath, string strExcelPath, string strContentPath, string strPicture, string strVideo, string strVoice, ref List<ExportHtmlMainInfo> lstExportHtmlMainInfo, out string strFileName, int intIndex, WechatTalk tTalk, WechatTalk wTalk, ExportRange er, out string strAttachment, out string sName, out string strFolder, string tip = null, int currentCount = 0)
        {
            //wTalk = tTalk.Clone();
            //wTalk = wTalk.Clone();
            strFolder = string.Empty;
            strFileName = Utility.ReplaceWinIllegalName(wTalk.NickName);
            // 文件名中可以包含#&+.等字符                    
            if (string.IsNullOrEmpty(strFileName))
                strFileName = Utility.ReplaceWinIllegalName(wTalk.UserName);
            strAttachment = @"attachment\" + strFileName;
            sName = wTalk.NickName;
            if (string.IsNullOrEmpty(sName))
                sName = wTalk.UserName;
            sName = Utility.ReplaceWinIllegalName(sName);

            string checkFolder = string.Empty;
            switch (eType)
            {
                case ExportType.ExportToTxt:
                    checkFolder = strTxtPath;
                    break;
                case ExportType.ExportToExcel:
                    checkFolder = strExcelPath;
                    break;
                case ExportType.ExportToPicture://--------导出图片--------
                    checkFolder = Path.Combine(strPicture, sName);
                    break;
                case ExportType.ExportToVideo://--------导出视频--------
                    checkFolder = Path.Combine(strVideo, sName);
                    break;
                case ExportType.ExportToVoice: //--------导出语音--------
                    checkFolder = Path.Combine(strVoice, sName);
                    break;
                default:
                    break;
            }

            if (!string.IsNullOrEmpty(checkFolder) && !Directory.Exists(checkFolder) && !Folder.CheckFolder(checkFolder))
            {
                iTong.Components.tbMessageBox.Show(this, string.Format("导出失败。该路径 {0} 可能无法读写或不存在，请重新选择导出路径。", checkFolder), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            //----------进度展示----------
            mLstCurrentExportInfo = new List<string>();
            mLstCurrentExportInfo.Add(wTalk.NickName);
            mLstCurrentExportInfo.Add(intIndex.ToString());
            mLstCurrentExportInfo.Add(lstRows.Count.ToString());
            mCurrentProgress = 0;
            //----------------------------

            ExportMultipleFormats(ref strPath, eType, dicWeChatFriendInfo, isPreview, strHtmlPath, strTxtPath, strExcelPath, strContentPath, strPicture, strVideo, strVoice, ref lstExportHtmlMainInfo, strFileName, tTalk, wTalk, strAttachment, sName, ref strFolder, er, tip, currentCount);
            return true;
        }

        private void ExportMultipleFormats(ref string strPath, ExportType eType, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, bool isPreview, string strHtmlPath, string strTxtPath, string strExcelPath, string strContentPath, string strPicture, string strVideo, string strVoice, ref List<ExportHtmlMainInfo> lstExportHtmlMainInfo, string strFileName, WechatTalk tTalk, WechatTalk wTalk, string strAttachment, string sName, ref string strFolder, ExportRange export, string tip = null, int currentCount = 0)
        {
            try
            {
                if (eType == ExportType.ExportToAll || eType == ExportType.ExportToHtml)
                {
                    lstExportHtmlMainInfo = DoHtml(dicWeChatFriendInfo, strHtmlPath, strContentPath, lstExportHtmlMainInfo,
                                                    strFileName, wTalk, strAttachment, isPreview, export, tip);

                    if (eType == ExportType.ExportToAll)
                    {
                        DoTxt(dicWeChatFriendInfo, strTxtPath, strFileName, wTalk, isPreview, export);
                        DoExcel(dicWeChatFriendInfo, strExcelPath, strFileName, wTalk, isPreview, export, currentCount);
                    }
                }
                else
                {
                    switch (eType)
                    {
                        case ExportType.ExportToTxt:
                            strFolder = strTxtPath;
                            break;
                        case ExportType.ExportToExcel:
                            strFolder = strExcelPath;
                            break;
                        case ExportType.ExportToPicture://--------导出图片--------
                            strFolder = Path.Combine(strPicture, sName);
                            break;
                        case ExportType.ExportToVideo://--------导出视频--------
                            strFolder = Path.Combine(strVideo, sName);
                            break;
                        case ExportType.ExportToVoice: //--------导出语音--------
                            strFolder = Path.Combine(strVoice, sName);
                            break;
                        default:
                            break;
                    }

                    if (string.IsNullOrEmpty(strFolder))
                        return;

                    if (eType == ExportType.ExportToTxt)
                    {
                        DoTxt(dicWeChatFriendInfo, strTxtPath, strFileName, wTalk, isPreview, export);
                        strPath = strTxtPath;
                    }
                    else if (eType == ExportType.ExportToExcel)
                    {
                        DoExcel(dicWeChatFriendInfo, strExcelPath, strFileName, wTalk, isPreview, export, currentCount);
                        strPath = strExcelPath;
                    }
                    else
                    {
                        ExportMedia(wTalk, strFolder, isPreview, eType, tip);
                        strPath = strFolder;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoExport");

                OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.ExportFailure, ""));
            }
            try
            {
                if (IniSetting.GetIsTalkSaveDB())
                {
                    if (tTalk != null)
                        tTalk.MessageItems.Clear();
                    if (wTalk != null)
                        wTalk.MessageItems.Clear();
                }
            }
            catch { }
        }

        private void DoExcel(Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, string strExcelPath, string strFileName, WechatTalk wTalk, bool isPreview, ExportRange export, int currentCount = 0)
        {
            int count = 0;
            DoRetry:
            //--------导出Excel格式--------
            if (!Directory.Exists(strExcelPath))
                Folder.CheckFolder(strExcelPath);

            string strExeclFileName = strFileName + ".xls";

            if (count > 0)
                strExeclFileName = strFileName + $"({count}).xls";

            string strExeclFilePath = Path.Combine(strExcelPath, strExeclFileName);

            if (File.Exists(strExeclFilePath))
            {
                count += 1;
                goto DoRetry;
            }

            this.ExportToFile(wTalk, strExeclFilePath, true, dicWeChatFriendInfo, isPreview, export, currentCount);
        }

        private void DoTxt(Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, string strTxtPath, string strFileName, WechatTalk wTalk, bool isPreview, ExportRange export)
        {
            int count = 0;
            DoRetry:
            //--------导出文本格式 导出为txt--------
            if (!Directory.Exists(strTxtPath))
                Folder.CheckFolder(strTxtPath);

            string strTxtFileName = strFileName + ".txt";

            if (count > 0)
                strTxtFileName = strFileName + $"({count}).txt";

            string strTxtFilePath = Path.Combine(strTxtPath, strTxtFileName);

            if (File.Exists(strTxtFilePath))
            {
                count += 1;
                goto DoRetry;
            }

            this.ExportToFile(wTalk, strTxtFilePath, false, dicWeChatFriendInfo, isPreview, export);
        }

        private List<ExportHtmlMainInfo> DoHtml(Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo,
            string strHtmlPath, string strContentPath,
            List<ExportHtmlMainInfo> lstExportHtmlMainInfo,
            string strFileName, WechatTalk wTalk, string strAttachment,
            bool isPreview,
            ExportRange export,
            string tip = null)
        {
            if (wTalk.MessageItems == null || wTalk.MessageItems.Count == 0) return lstExportHtmlMainInfo;

            //--------导出Html格式--------                     
            string strAttachmentPath = Path.Combine(strHtmlPath, strAttachment);
            //Common.Log(string.Format("ExportHelper's DoHtml()>>>>>>>>>>>>>>{0}\tMessageItems Count={1}", strAttachmentPath, wTalk.MessageItems.Count));

            //var s1 = System.Diagnostics.Stopwatch.StartNew();

            //导出媒体           
            ExportMedia(wTalk, strAttachmentPath, isPreview, tip: tip);

            mCurrentProgress = 0;
            strFileName = strFileName.Replace(" ", "");
            string strHtmlFileName = strFileName + ".html";
            string strHtmlFilePath = Path.Combine(strContentPath, strHtmlFileName);

            const int MAXCOUNTS = 1000;
            bool bIsThread = (wTalk.MessageItems.Count >= MAXCOUNTS);
            string fname = Path.Combine(Folder.AppFolder, "notthread.dll");
            if (File.Exists(fname))
                bIsThread = false;

            bIsThread = false;//暂时用就方式
            //DoExportHtmlInfo(wTalk, strAttachmentPath, strHtmlFilePath,
            //                    @"../" + strAttachment.Replace(@"\", @"/"), strFileName,
            //                    ref lstExportHtmlMainInfo, dicWeChatFriendInfo, isPreview, bIsThread);

            //-------------------
            if (export == ExportRange.All || export == ExportRange.Head)
                IMHtmlNodeHelper.CreateHtmlHeader(strFileName, strHtmlFilePath);
            DoExportHtmlInfo(wTalk, strAttachmentPath, strHtmlFilePath,
                               @"../" + strAttachment.Replace(@"\", @"/"), strFileName,
                               ref lstExportHtmlMainInfo, dicWeChatFriendInfo, isPreview, bIsThread, tip);
            if (export == ExportRange.All || export == ExportRange.Tail)
                IMHtmlNodeHelper.CreateHtmlEnd(strHtmlFilePath);
            //-------------------

            //s1.Stop();
            //Common.Log(string.Format("{1}的总导出时间：{0}ms", s1.Elapsed.TotalMilliseconds, (bIsThread ? "使用线程" : "未使用线程")));

            return lstExportHtmlMainInfo;
        }

        private void OnExportProgressEventHandler(ExportProgressEventArgs e)
        {
            if (ExportProgressEventHandler != null)
                ExportProgressEventHandler(null, e);
        }

        double mCurrentProgress = 0;
        private void ExportProgress(double intIndex, double intCount, string strType = "")
        {
            if (mLstCurrentExportInfo == null || mLstCurrentExportInfo.Count < 3)
                return;

            if (intIndex == 0 && intCount == 0)
                return;


            if (intIndex == 0 && intCount == 1)
            {
                OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.Exporting,
                    string.Format("正在导出【{0}】 {1}", mLstCurrentExportInfo[0], strType)));

                return;
            }
            double intProgress = Math.Round((intIndex / intCount) * 100);
            if (mCurrentProgress < intProgress)
            {
                ExportProgressEventArgs epeArgs = new ExportProgressEventArgs(ExportStatus.Exporting, "");
                mCurrentProgress = intProgress;
                epeArgs.Msg = string.Format("正在导出【{0}】{3} {4}% （{1}/{2}）",
                                            mLstCurrentExportInfo[0], mLstCurrentExportInfo[1], mLstCurrentExportInfo[2],
                                            strType, intProgress);
                OnExportProgressEventHandler(epeArgs);
            }
            else if (mCurrentProgress == 0.0)
            {
                OnExportProgressEventHandler(new ExportProgressEventArgs(ExportStatus.Exporting,
                    string.Format("正在导出【{0}】", mLstCurrentExportInfo[0])));
            }

        }

        private bool ExportToFile(WechatTalk wTalk, string strTxtFilePath, bool isExcel, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, bool isPreview, ExportRange export, int currentCount = 0)
        {
            bool result = false;
            if (!isExcel)
                result = this.ExportToTxt(wTalk, strTxtFilePath, dicWeChatFriendInfo, isPreview, export);
            else
                result = this.ExportToExcel(wTalk, strTxtFilePath, dicWeChatFriendInfo, isPreview, export, currentCount);
            return result;
        }

        private List<ContentLayout>[] aryListContents_ = null;
        private bool ExportToTxt(WechatTalk wTalk, string strFilePath, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, bool isPreview, ExportRange export)
        {
            bool result = false;
            try
            {
                List<ContentLayout> lstHead = new List<ContentLayout>();
                if (export == ExportRange.All || export == ExportRange.Head)
                {
                    lstHead.Add(this.GetContentLayout("Len45", "ID"));
                    lstHead.Add(this.GetContentLayout("Len35", "时间"));
                    lstHead.Add(this.GetContentLayout("Len35", "联系人"));
                    lstHead.Add(this.GetContentLayout("Len45", "微信号"));
                    lstHead.Add(this.GetContentLayout("Len40", "昵称"));
                    lstHead.Add(this.GetContentLayout("Len20", "状态"));
                    lstHead.Add(this.GetContentLayout("Len20", "类型"));
                    lstHead.Add(this.GetContentLayout("Len2000", "消息"));
                }

                int iCount = 0;
                if (wTalk.MessageItems == null)
                    return result;

                //var s1 = System.Diagnostics.Stopwatch.StartNew();

                int intIndex = 0;
                const int MAXCOUNTS = 1000;
                bool bIsThread = (wTalk.MessageItems.Count >= MAXCOUNTS);
                bIsThread = false;//暂时用就方式
                if (!bIsThread)
                {
                    Dictionary<int, List<ContentLayout>> dictContents = new Dictionary<int, List<ContentLayout>>();
                    List<ContentLayout> lstInfo = null;
                    WechatMessageItem[] aryItems = wTalk.MessageItems.ToArray();
                    for (int i = aryItems.Length - 1; i >= 0; i--)
                    {
                        intIndex += 1;
                        ExportProgress(intIndex, aryItems.Length);
                        WechatMessageItem wItem = aryItems[i];

                        lstInfo = new List<ContentLayout>();
                        string strNickName = "";
                        string strWeChatNO = "";
                        string strWeChatID = "";
                        string strUsrName = "";
                        string strFromForWeb = "";
                        string strPetName = "";
                        GetFriendInformation(wTalk, dicWeChatFriendInfo, wItem, ref strNickName, ref strWeChatNO, ref strWeChatID, ref strUsrName, ref strFromForWeb, ref strPetName);

                        lstInfo.Add(this.GetContentLayout("Len35", strWeChatID));
                        lstInfo.Add(this.GetContentLayout("Len25", WechatHeplerPC.ConvertWeixinToPcTime(wItem.MessageDateTime).ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))));
                        lstInfo.Add(this.GetContentLayout("Len25", strNickName));
                        lstInfo.Add(this.GetContentLayout("Len40", strWeChatNO));
                        lstInfo.Add(this.GetContentLayout("Len30", strPetName));
                        lstInfo.Add(this.GetContentLayout("Len20", wItem.ReceiveId == mCurrentWAInfo.Account ? "接收" : "发送"));
                        lstInfo.Add(this.GetContentLayout("Len20", GetChatType(wItem.MessageType)));
                        string strMsg = GetChatMsg(wItem, ChatMsgType.ShowOnExcelFile, isPreview, ref strWeChatID);
                        lstInfo.Add(this.GetContentLayout("Len2000", strMsg));

                        dictContents.Add(iCount, lstInfo);
                        iCount = iCount + 1;
                    }
                    result = this.ExportToTxtFile(lstHead, dictContents, strFilePath, export);
                }
                else
                {
                    int nCounts = wTalk.MessageItems.Count;
                    int thread_counts = nCounts / MAXCOUNTS;
                    if (nCounts % MAXCOUNTS > 0)
                        thread_counts++;

                    aryListContents_ = new List<ContentLayout>[thread_counts];

                    int nSpareCounts = 0;
                    List<ManualResetEvent> listEvents = new List<ManualResetEvent>();
                    for (int iIndex = 0; iIndex < thread_counts; iIndex++)
                    {
                        ManualResetEvent e = new ManualResetEvent(false);
                        listEvents.Add(e);

                        if (iIndex == (thread_counts - 1))
                            nSpareCounts = nCounts % MAXCOUNTS;
                        else
                            nSpareCounts = MAXCOUNTS;

                        List<WechatMessageItem> listMsgItems = wTalk.MessageItems.GetRange(iIndex * MAXCOUNTS, nSpareCounts);
                        ThreadPool.QueueUserWorkItem(new WaitCallback(ExportToTextFileThread), new object[] {
                            wTalk, listMsgItems, e, isPreview, iIndex
                        });
                    }
                    WaitHandle.WaitAll(listEvents.ToArray());

                    Dictionary<int, List<ContentLayout>> dictContents = new Dictionary<int, List<ContentLayout>>();
                    iCount = 0;
                    for (int i = aryListContents_.Length - 1; i >= 0; i--)
                    {
                        dictContents.Add(iCount++, aryListContents_[i]);
                    }
                    result = this.ExportToTxtFile(lstHead, dictContents, strFilePath, export);
                }

                //s1.Stop();
                //Common.Log(string.Format("{1}的总导出时间：{0}ms", s1.Elapsed.TotalMilliseconds, (bIsThread?"使用线程":"未使用线程")));
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ExportToExcel");
            }
            return result;
        }

        private void ExportToTextFileThread(object objArgs)
        {
            object[] objItems = (object[])objArgs;
            WechatTalk wTalk = (objItems[0] as WechatTalk);
            List<WechatMessageItem> listMsgItems = (objItems[1] as List<WechatMessageItem>);
            ManualResetEvent e = (objItems[2] as ManualResetEvent);
            bool bIsPreview = (bool)objItems[3];
            int iThread = (int)objItems[4]; // 当前线程偏移
            aryListContents_[iThread] = new List<ContentLayout>();

            int iStep = 0;
            WechatMessageItem[] aryItems = listMsgItems.ToArray();
            for (int iIndex = aryItems.Length - 1; iIndex >= 0; iIndex--)
            {
                WechatMessageItem wItem = aryItems[iIndex];
                ExportProgress(++iStep, aryItems.Length);

                string strNickName = "";
                string strWeChatNO = "";
                string strWeChatID = "";
                string strUsrName = "";
                string strFromForWeb = "";
                string strPetName = "";
                GetFriendInformation(wTalk, mCurrentWAInfo.DicWeChatFriendInfo, wItem,
                    ref strNickName, ref strWeChatNO, ref strWeChatID, ref strUsrName, ref strFromForWeb, ref strPetName);

                aryListContents_[iThread].Add(this.GetContentLayout("Len30", strWeChatID));
                aryListContents_[iThread].Add(this.GetContentLayout("Len25", WechatHeplerPC.ConvertWeixinToPcTime(wItem.MessageDateTime).ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))));
                aryListContents_[iThread].Add(this.GetContentLayout("Len25", strNickName));
                aryListContents_[iThread].Add(this.GetContentLayout("Len25", strWeChatNO));
                aryListContents_[iThread].Add(this.GetContentLayout("Len20", (wItem.ReceiveId == mCurrentWAInfo.Account) ? "接收" : "发送"));
                aryListContents_[iThread].Add(this.GetContentLayout("Len20", GetChatType(wItem.MessageType)));

                string strMsg = GetChatMsg(wItem, ChatMsgType.ShowOnExcelFile, bIsPreview, ref strWeChatID);
                aryListContents_[iThread].Add(this.GetContentLayout("Len2000", strMsg + "\r\n"));
            }

            if (e != null)
                e.Set();
        }

        private void GetFriendInformation(WechatTalk wTalk, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, WechatMessageItem Item,
                                          ref string strNickName, ref string strWeChatNO, ref string strWeChatID,
                                          ref string strUsrName, ref string strFromForWeb, ref string strPetName)
        {
            bool isChatRoom = WechatHeplerPC.CheckIsChatRoom(wTalk.UserName);

            if (WechatHeplerPC.IsReceivedMsg(Item, this.mCurrentWAInfo))
            {
                strFromForWeb = "1";

                if (isChatRoom)
                    strNickName = WechatHeplerPC.GetChatRoomNickName(wTalk.UserName, mCurrentWAInfo.Account, mCurrentWAInfo.LstWeChatGroupFriendInfo);

                if (strNickName.Length == 0)
                    strNickName = this.mCurrentWAInfo.Name;

                strWeChatNO = this.mCurrentWAInfo.InnerAccount;
                strWeChatID = this.mCurrentWAInfo.Account;
                strPetName = this.mCurrentWAInfo.Name;
            }
            else
            {
                strFromForWeb = "0";
                if (isChatRoom)
                {
                    string strMessageContent = Item.MessageContent;
                    WechatHeplerPC.AnalyseGroup(dicWeChatFriendInfo,
                                                ref strUsrName, ref strWeChatNO,
                                                ref strNickName, ref strMessageContent, ref strPetName,
                                                mCurrentWAInfo.LstWeChatGroupFriendInfo, wTalk.UserName);

                    //strWeChatNO = strUsrName;
                    strWeChatID = strUsrName;
                }
                else
                {
                    strNickName = wTalk.NickName;
                    strWeChatNO = wTalk.UserName;
                    strWeChatID = Item.SendId;
                    strUsrName = Item.SendId;
                }
            }
            strUsrName = WechatHeplerPC.ReplaceImproperCharacter(strUsrName);
            if (strUsrName.Length == 0 && Item.MessageType == 49)
            {
                JsonArray webLinkJsonArray = ExportHelper.GetWebLinkJsonArray(Item.MessageContent);

                if (webLinkJsonArray != null && webLinkJsonArray.Count > 0)
                {
                    JsonObject dicWebPage = (JsonObject)webLinkJsonArray[0];
                    strUsrName = ((JsonString)dicWebPage["fromusername"]).Value;
                }
            }

            if (!string.IsNullOrWhiteSpace(strUsrName) && (!isChatRoom || (strWeChatNO.Length == 0 && strWeChatID.Length == 0)))
            {
                if (dicWeChatFriendInfo != null && dicWeChatFriendInfo.ContainsKey(strUsrName))
                {
                    WeChatFriendInfo wInfo = dicWeChatFriendInfo[strUsrName];
                    strNickName = wInfo.StrRemark.Length > 0 ? wInfo.StrRemark : wInfo.StrNickName;
                    strWeChatNO = wInfo.StrInnerAccount.Length > 0 ? wInfo.StrInnerAccount : wInfo.StrWXID;
                    strWeChatID = wInfo.StrWXID;
                    strPetName = wInfo.StrNickName;
                }
                //foreach (string strKey in dicWeChatFriendInfo.Keys)
                //{
                //    if (strUsrName == strKey)
                //    {
                //        WeChatFriendInfo wInfo = dicWeChatFriendInfo[strKey];
                //        strNickName = wInfo.StrRemark.Length > 0 ? wInfo.StrRemark : wInfo.StrNickName;
                //        strWeChatNO = wInfo.StrInnerAccount.Length > 0 ? wInfo.StrInnerAccount : wInfo.StrWXID;
                //        strWeChatID = wInfo.StrWXID;
                //        break;
                //    }
                //}
            }

            if (strUsrName.Length == 0 && WechatHeplerPC.IsReceivedMsg(Item, this.mCurrentWAInfo))
                strUsrName = this.mCurrentWAInfo.Account;
        }

        private bool ExportToTxtFile(List<ContentLayout> lstHead, Dictionary<int, List<ContentLayout>> dictVlues, string strFilePath, ExportRange export)
        {
            bool result = false;
            try
            {
                StringBuilder sbLines = new StringBuilder();
                if (export == ExportRange.All || export == ExportRange.Head)
                {
                    //"导出时间"  "wx消息记录"
                    sbLines.AppendLine(string.Format("{0}     {1}:{2}", "微信消息记录", "导出时间", DateTime.Now.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))));
                    sbLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－");

                    StringBuilder sbHead = new StringBuilder();
                    foreach (ContentLayout item in lstHead)
                    {
                        //strHead = strHead + Common.StringFormat(item.CLValue, item.CLLength, item.CLIsDoc, item.CLIsPad, item.CLPadRight, item.CLIsCut);
                        sbHead.Append(Common.StringFormat(item.CLValue, item.CLLength, item.CLIsDoc, item.CLIsPad, item.CLPadRight, item.CLIsCut));
                    }
                    sbLines.AppendLine(sbHead.ToString());

                    sbLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－");
                }

                // Modified by Utmost 2020.05.21
                foreach (KeyValuePair<int, List<ContentLayout>> pair in dictVlues)
                {
                    StringBuilder sbValue = new StringBuilder();
                    foreach (ContentLayout info in pair.Value)
                        sbValue.Append(Common.StringFormat(info.CLValue, info.CLLength, info.CLIsDoc, info.CLIsPad, info.CLPadRight, info.CLIsCut));

                    sbLines.AppendLine(sbValue.ToString());
                }

                if (export == ExportRange.All || export == ExportRange.Tail)
                    sbLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－");

                File.AppendAllText(strFilePath, sbLines.ToString(), System.Text.Encoding.UTF8);                    //导出韩文乱码
                result = true;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ExportToTxtFile");
            }
            return result;
        }

        private bool ExportToExcel(WechatTalk wTalk, string strFilePath, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, bool isPreview, ExportRange export, int currentCount = 0)
        {
            bool result = false;

            int intCount = currentCount % 65500;
            int intExcelIndex = (currentCount / 65500);
            int intIndex = 0;

            if (wTalk.MessageItems == null)
                return false;

            string strTempFilePath = strFilePath + ".Temp";

            string strFileName = Path.GetFileNameWithoutExtension(strFilePath);
            //判断是否是数据库模式需要做分页
            if (currentCount != 0 && intExcelIndex != 0)
            {
                string strFileNewName = string.Format("{0}_{1}.xls", strFileName, intExcelIndex);

                strFilePath = Path.Combine(Path.GetDirectoryName(strFilePath), strFileNewName);

                strTempFilePath = strFilePath + ".Temp";
            }

            try
            {
                if (export == ExportRange.All || export == ExportRange.Head)
                {
                    if (File.Exists(strTempFilePath))
                    {
                        File.Delete(strTempFilePath);
                    }
                }
            }
            catch
            {
            }

            try
            {
                //var s1 = System.Diagnostics.Stopwatch.StartNew();

                //创建头部
                if (export == ExportRange.All || export == ExportRange.Head)
                    CreateExcelHead(strTempFilePath);

                //写入内容
                string strLineTd = "<td class=xl24  style='border-bottom:.5pt solid black;border-top:none;' x:str>{0}</td>";
                string strLineTr = "<tr style='mso-height-source:userset;height:25.00pt'>{0}</tr>";
                StringBuilder strExcelContacts = new StringBuilder();
                StringBuilder strLine = new StringBuilder();

                WechatMessageItem[] aryItems = wTalk.MessageItems.ToArray();
                for (int i = aryItems.Length - 1; i >= 0; i--)
                {
                    intIndex += 1;
                    ExportProgress(intIndex, aryItems.Length);
                    WechatMessageItem item = aryItems[i];

                    strLine.Remove(0, strLine.Length);

                    string strNickName = "";
                    string strWeChatNO = "";
                    string strWeChatID = "";
                    string strUsrName = "";
                    string strFromForWeb = "";
                    string strPetName = "";
                    GetFriendInformation(wTalk, dicWeChatFriendInfo, item, ref strNickName, ref strWeChatNO, ref strWeChatID, ref strUsrName, ref strFromForWeb, ref strPetName);

                    //strLine.Append(string.Format(strLineTd, strWeChatID));
                    //strLine.Append(string.Format(strLineTd, WechatHeplerPC.ConvertWeixinToPcTime(item.MessageDateTime).ToString("yyyy-MM-dd HH:mm:ss")));
                    //strLine.Append(string.Format(strLineTd, this.FormatExcelCellText(strNickName)));
                    //strLine.Append(string.Format(strLineTd, strWeChatNO));
                    //strLine.Append(string.Format(strLineTd, item.ReceiveId == this.mCurrentWAInfo.Account ? "接收" : "发送"));
                    //strLine.Append(string.Format(strLineTd, GetChatType(item.MessageType)));

                    // Modified by Utmost on 2020.05.21
                    strLine.AppendFormat(strLineTd, strWeChatID);
                    strLine.AppendFormat(strLineTd, WechatHeplerPC.ConvertWeixinToPcTime(item.MessageDateTime).ToString("yyyy-MM-dd HH:mm:ss"));
                    strLine.AppendFormat(strLineTd, this.FormatExcelCellText(strNickName));
                    strLine.AppendFormat(strLineTd, strWeChatNO);
                    strLine.AppendFormat(strLineTd, this.FormatExcelCellText(strPetName));
                    strLine.AppendFormat(strLineTd, item.ReceiveId == mCurrentWAInfo.Account ? "接收" : "发送");
                    strLine.AppendFormat(strLineTd, GetChatType(item.MessageType));

                    string strMsg = HttpUtility.HtmlEncode(GetChatMsg(item, ChatMsgType.ShowOnExcelFile, isPreview, ref strWeChatID));
                    strLine.AppendFormat(strLineTd, this.FormatExcelCellText(strMsg));
                    strExcelContacts.AppendFormat(strLineTr, strLine.ToString()).AppendLine();

                    if (strExcelContacts.Length > 1000)
                    {
                        this.WriteText(strTempFilePath, strExcelContacts.ToString());
                        strExcelContacts.Remove(0, strExcelContacts.Length);
                    }

                    intCount = intCount + 1;

                    //如果一次内容写入的行数 超过65500 则需要在创建一个excel
                    if (intCount >= 65500)
                    {
                        if (strExcelContacts.Length > 0)
                        {
                            this.WriteText(strTempFilePath, strExcelContacts.ToString());
                            strExcelContacts.Remove(0, strExcelContacts.Length);
                        }

                        //创建execl 尾部
                        CreateExcelFoot(strTempFilePath);
                        //保存 Excel
                        SaveCreateExce(strTempFilePath, strFilePath);

                        string strFileNewName = string.Format("{0}_{1}.xls", strFileName, intExcelIndex + 1);
                        strFilePath = Path.Combine(Path.GetDirectoryName(strFilePath), strFileNewName);
                        strTempFilePath = strFilePath + ".Temp";

                        try
                        {
                            if (File.Exists(strTempFilePath))
                            {
                                File.Delete(strTempFilePath);
                            }
                        }
                        catch
                        {
                        }

                        //用于判断是否内存导出
                        if (currentCount == 0)
                            intExcelIndex += 1;

                        //创建头部
                        CreateExcelHead(strTempFilePath);

                        intCount = 0;
                    }
                }


                if (strExcelContacts.Length > 0)
                {
                    this.WriteText(strTempFilePath, strExcelContacts.ToString());
                }
                //创建execl 尾部
                if (export == ExportRange.All || export == ExportRange.Tail)
                {
                    CreateExcelFoot(strTempFilePath);
                    //保存 Excel
                    SaveCreateExce(strTempFilePath, strFilePath);
                }
                result = File.Exists(strFilePath);

                //s1.Stop();
                //Common.Log(string.Format("总导出时间：{0}ms", s1.Elapsed.TotalMilliseconds));
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ExportHelper_ExportToExcelFileEx");
            }
            return result;
        }

        private string FormatExcelCellText(string strText)
        {
            string result = strText;
            string strTextTemp = strText;

            if (string.IsNullOrEmpty(strText) || !strTextTemp.StartsWith("="))
            {
                goto DO_EIXT;
            }

            strTextTemp = strText.Replace(" ", "");

            if (!strTextTemp.StartsWith("=="))
            {
                goto DO_EIXT;
            }

            result = "'" + strText;
            DO_EIXT:

            return result;
        }

        private void SaveCreateExce(string strTempFilePath, string strFilePath)
        {
            try
            {
                if (File.Exists(strFilePath))
                {
                    File.Delete(strFilePath);
                }
            }
            catch { }
            try
            {
                File.Move(strTempFilePath, strFilePath);
            }
            catch { }
        }

        private void CreateExcelHead(string strTempFilePath)
        {
            StringBuilder strExcelContacts = new StringBuilder();
            strExcelContacts.AppendLine("<html xmlns:v=\"urn:schemas-microsoft-com:vml\"");
            strExcelContacts.AppendLine("xmlns:o=\"urn:schemas-microsoft-com:office:office\"");
            strExcelContacts.AppendLine("xmlns:x=\"urn:schemas-microsoft-com:office:excel\"");
            strExcelContacts.AppendLine("xmlns=\"http://www.w3.org/TR/REC-html40\">");

            string strExcelHeader = iWechatAssistant.Properties.Resources.excel_header.Replace("<meta http-equiv=Content-Type content=\"text/html; charset=utf8\">", string.Format("<meta http-equiv=Content-Type content=\"text/html; charset={0}\">", IniSetting.GetWeChatExportExcelCoding()));

            strExcelContacts.AppendLine(strExcelHeader);
            strExcelContacts.AppendLine("<body link=blue vlink=purple><table x:str border=0 cellpadding=0 cellspacing=0 width=1296 style='border-collapse:collapse;table-layout:fixed;width:972pt'>");

            string strTitleTr = "<tr style='mso-height-source:userset;height:30.00pt'>{0}</tr>";
            string strTitleTd = "<td rowspan=2 class=xl300  style='border-bottom:.5pt solid black;border-top:none;' >{0}</td>";
            string strLineTr = "<tr style='mso-height-source:userset;height:25.00pt'>{0}</tr>";

            StringBuilder strTitle = new StringBuilder();
            strTitle.Append(string.Format(strTitleTd, "ID"));
            strTitle.Append(string.Format(strTitleTd, "时间"));
            strTitle.Append(string.Format(strTitleTd, "联系人"));
            strTitle.Append(string.Format(strTitleTd, "微信号"));
            strTitle.Append(string.Format(strTitleTd, "昵称"));
            strTitle.Append(string.Format(strTitleTd, "状态"));
            strTitle.Append(string.Format(strTitleTd, "类型"));
            strTitle.Append(string.Format(strTitleTd, "消息"));
            strExcelContacts.AppendLine(string.Format(strTitleTr, strTitle));
            strExcelContacts.AppendLine(string.Format(strLineTr, ""));

            this.WriteText(strTempFilePath, strExcelContacts.ToString());
        }

        private void CreateExcelFoot(string strTempFilePath)
        {
            this.WriteText(strTempFilePath, "</table></body></html>");
        }

        private void WriteText(string strTempFilePath, string strExcelContacts)
        {
            try
            {
                byte[] myByte = System.Text.Encoding.UTF8.GetBytes(strExcelContacts);
                //导出韩文乱码
                using (FileStream fsWrite = new FileStream(strTempFilePath, FileMode.Append))
                {
                    fsWrite.Write(myByte, 0, myByte.Length);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WriteText");
            }
        }


        private void DoExportHtmlInfo(WechatTalk wTalk, string strAttachmentPath, string strHtmlFilePath, string strAttachment, string strFileName,
            ref List<ExportHtmlMainInfo> lstExportHtmlMainInfo, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, bool isPreview, bool bIsThread = false, string tip = null)
        {
            if (bIsThread)
            {
                List<object> listArgs = new List<object>();

                listArgs.Add(strAttachmentPath);
                listArgs.Add(strHtmlFilePath);
                listArgs.Add(strAttachment);
                listArgs.Add(strFileName);
                listArgs.Add(isPreview);

                listHtmlMainInfors_ = lstExportHtmlMainInfo;

                const int MAXCOUNTS = 1000;
                int nCounts = wTalk.MessageItems.Count;
                int thread_counts = nCounts / MAXCOUNTS;
                if (nCounts % MAXCOUNTS > 0)
                    thread_counts++;

                aryListIMHtmlNodes_ = new List<IMHtmlNode>[thread_counts];

                int nSpareCounts = 0;
                List<ManualResetEvent> listEvents = new List<ManualResetEvent>();
                for (int iIndex = 0; iIndex < thread_counts; iIndex++)
                {
                    ManualResetEvent e = new ManualResetEvent(false);
                    listEvents.Add(e);

                    if (iIndex == (thread_counts - 1))
                        nSpareCounts = nCounts % MAXCOUNTS;
                    else
                        nSpareCounts = MAXCOUNTS;

                    List<WechatMessageItem> listMsgItems = wTalk.MessageItems.GetRange(iIndex * MAXCOUNTS, nSpareCounts);

                    ThreadPool.QueueUserWorkItem(new WaitCallback(ExportHtmlInfoThread), new object[] {
                        wTalk, listMsgItems.ToArray(), listArgs.ToArray(), e, iIndex, thread_counts - 1
                    });
                }
                WaitHandle.WaitAll(listEvents.ToArray());

                if (aryListIMHtmlNodes_.Length > 0)
                {
                    for (int iIndex = aryListIMHtmlNodes_.Length - 1; iIndex >= 0; iIndex--)
                    {
                        IMHtmlNodeHelper.CreateHtml(strFileName, strHtmlFilePath, aryListIMHtmlNodes_[iIndex], isPreview);
                    }
                }
            }
            else
            {
                try
                {
                    List<IMHtmlNode> lstIMHtmlNode = new List<IMHtmlNode>();
                    IMHtmlNode smsNode = new IMHtmlNode();
                    string strTypeForWeb = string.Empty;        // '0：文本，1：语音，2：图片，3：网页，4：系统消息
                    string strMsgForWeb = "";
                    string strFromForWeb = "";     //'0：对方，1：自己
                    string strNickName = "";
                    string strWeChatNO = "";
                    string strWeChatID = "";
                    string strUsrName = "";
                    string strPetName = "";


                    string strSvrTypeForWeb = string.Empty;        // '0：文本，1：语音，2：图片，3：网页，4：系统消息
                    string strSvrFromForWeb = "";     //'0：对方，1：自己
                    string strSvrNickName = "";
                    string strSvrWeChatNO = "";
                    string strSvrWeChatID = "";
                    string strSvrUsrName = "";
                    string strSvrPetName = "";

                    JsonArray arrHtml5ContentForWeb = null;
                    string strIconPath = "";
                    bool isLinkA = false;
                    int intIndex = 0;

                    WechatMessageItem[] aryItems = wTalk.MessageItems.ToArray();
                    for (int i = aryItems.Length - 1; i >= 0; i--)
                    {
                        intIndex += 1;

                        if (string.IsNullOrEmpty(tip))
                            ExportProgress(intIndex, aryItems.Length, "HTML");
                        else
                            ExportProgress(intIndex, aryItems.Length, tip + "HTML");

                        //strUsrName = "";
                        //strNickName = "";

                        //isLinkA = false;

                        WechatMessageItem wItem = aryItems[i];

                        //strMsgForWeb = wItem.MessageContent;//群组的信息里面包括联系人名称
                        //GetChatMsg(wItem, ChatMsgType.ShowOnExcelFile, isPreview, ref strWeChatID);

                        //GetFriendInformation(wTalk, dicWeChatFriendInfo, wItem, ref strNickName, ref strWeChatNO, ref strWeChatID, ref strUsrName, ref strFromForWeb, ref strPetName);

                        //if (wItem.SvrMsg != null)
                        //    GetFriendInformation(wTalk, dicWeChatFriendInfo, wItem.SvrMsg, ref strSvrNickName, ref strSvrWeChatNO, ref strSvrWeChatID, ref strSvrUsrName, ref strSvrFromForWeb, ref strSvrPetName);

                        //smsNode = new IMHtmlNode();
                        //smsNode.IsItemMe = (strFromForWeb == "0" ? false : true);
                        //smsNode.UserID = strWeChatID;

                        //if (strWeChatID.Length > 0 && strNickName.Length == 0)
                        //{
                        //    foreach (WeChatFriendInfo wcfi in this.mCurrentWAInfo.DicWeChatFriendInfo.Values)
                        //    {
                        //        if (wcfi.StrWXID.Equals(strWeChatID, StringComparison.InvariantCultureIgnoreCase))
                        //        {
                        //            strNickName = wcfi.StrNickName;
                        //            strUsrName = strWeChatID;

                        //            break;
                        //        }
                        //    }
                        //}
                        //smsNode.NickName = strNickName;
                        //smsNode.UserName = strWeChatNO;
                        //smsNode.WeChatID = strNickName;
                        //smsNode.IsChatRoom = WechatHeplerPC.CheckIsChatRoom(wItem.SendId);

                        //string strHeadIcon = WechatHeplerPC.GetHeadIconPathFromPC(strUsrName, this.mCurrentWAInfo);
                        //if (File.Exists(strHeadIcon))
                        //{
                        //    string strHeadIconPath = Path.Combine(Path.GetDirectoryName(strAttachmentPath), "HeadIcon");
                        //    Folder.CheckFolder(strHeadIconPath);
                        //    strIconPath = string.Format("../attachment/HeadIcon/{0}", Path.GetFileName(strHeadIcon));
                        //    File.Copy(strHeadIcon, Path.Combine(strHeadIconPath, Path.GetFileName(strHeadIcon)), true);
                        //}
                        //else
                        //{
                        //    strIconPath = WechatHeplerPC.GetHeadIconUrl(strUsrName, this.mCurrentWAInfo);
                        //}
                        //if (string.IsNullOrEmpty(strIconPath))
                        //    strIconPath = "../assets/images/default.png";

                        //smsNode.HeaderIconUrl = strIconPath;

                        ////3、消息类型与内容
                        ////wx： 1文本；3图片；34语音；42名片；43视频；47特殊表情；48地理位置；49网页；50视频未接通的消息；1000系统消息              
                        //arrHtml5ContentForWeb = null;
                        //strTypeForWeb = "0";

                        //int intMapKey = 0;

                        //string strNickNameEx = Utility.ReplaceWinIllegalName(strNickName);
                        //strNickNameEx = Utility.ReplaceWinIllegalNameToHtml(strNickNameEx);
                        //try
                        //{
                        //    switch (wItem.MessageType)
                        //    {
                        //        case 1:
                        //            smsNode.NType = IMHtmlNodeType.IMHtmlNode_Text;
                        //            strTypeForWeb = "0";

                        //            //检查是否有url
                        //            //string strCheckIsContainUrl = strMsgForWeb;
                        //            //bool blnHighlight = true;
                        //            //if (string.Compare(strCheckIsContainUrl, strMsgForWeb) != 0)
                        //            //{
                        //            //    blnHighlight = false;
                        //            //    strMsgForWeb = strCheckIsContainUrl;
                        //            //}

                        //            //表情符号转换成表情图片
                        //            strMsgForWeb = this.OperateForExpression(strMsgForWeb, true);

                        //            break;
                        //        case 34:
                        //            smsNode.NType = IMHtmlNodeType.IMHtmlNode_Voice;
                        //            string strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, true, this.mCurrentWeChatiOSDevice, out intMapKey);
                        //            strMsgForWeb = WechatHeplerPC.GetChatVoiceLength(wItem.MessageContent, true).ToString();
                        //            //smsNode.AttachPath = strAttachment + @"/" + string.Format("{0}.wav", Item.MessageDT.ToString("yyyyMMddHHmmss") + "-" + intMapKey);
                        //            smsNode.AttachPath = strAttachment + string.Format(@"/{0}-{1}-{2}.mp3", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        //            strTypeForWeb = "1";
                        //            break;

                        //        case 3:
                        //            smsNode.NType = IMHtmlNodeType.IMHtmlNode_Image;
                        //            strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, false, this.mCurrentWeChatiOSDevice, out intMapKey);
                        //            if (strTempFilePath.Length == 0)
                        //                strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, true, this.mCurrentWeChatiOSDevice, out intMapKey);
                        //            string strImagePath = strAttachment + string.Format(@"/{0}-{1}-{2}.png", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        //            smsNode.AttachPath = strImagePath;
                        //            smsNode.ThumbnailPath = strImagePath;//strAttachment + string.Format(@"/{0}-{1}-{2}.png", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);

                        //            strTypeForWeb = "2";

                        //            break;
                        //        case 62:
                        //            smsNode.NType = IMHtmlNodeType.IMHtmlNode_Image;
                        //            strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, false, this.mCurrentWeChatiOSDevice, out intMapKey);
                        //            if (strTempFilePath.Length > 0)
                        //                smsNode.AttachPath = strAttachment + string.Format(@"/{0}-{1}-{2}.mp4", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        //            else
                        //                //备份的时候手机上没有预览视频，无法加载视频，请重新备份
                        //                smsNode.AttachPath = "抱歉，暂时无法加载此小视频！";

                        //            smsNode.ThumbnailPath = "../assets/images/default_video.png";
                        //            strTypeForWeb = "2";
                        //            break;

                        //        case 42:
                        //            strMsgForWeb = "[名片]";
                        //            break;

                        //        case 43:
                        //            smsNode.NType = IMHtmlNodeType.IMHtmlNode_Video;
                        //            strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, false, this.mCurrentWeChatiOSDevice, out intMapKey);
                        //            smsNode.AttachPath = strAttachment + string.Format(@"/{0}-{1}-{2}.mp4", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        //            strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, true, this.mCurrentWeChatiOSDevice, out intMapKey);
                        //            smsNode.ThumbnailPath = strAttachment + string.Format(@"/{0}-{1}-{2}.png", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        //            strTypeForWeb = "2";
                        //            break;

                        //        case 47:
                        //            strMsgForWeb = "[动画表情]";
                        //            break;

                        //        case 48:
                        //            strMsgForWeb = "[地理位置]";
                        //            break;

                        //        case 49:
                        //            if (!Common.OldAnalysisWebpage())
                        //            {
                        //                IMHtmlNodeType imtype = IMHtmlNodeType.IMHtmlNode_Media;

                        //                strTypeForWeb = "3";
                        //                string strExport = "";

                        //                this.mWeChatMsgWebPage.AnalysisNewWebpage(wItem, ref strMsgForWeb, ref arrHtml5ContentForWeb, ref strTypeForWeb, ref strExport, ref imtype, ref isLinkA);
                        //                smsNode.NType = imtype;
                        //                switch (strTypeForWeb)
                        //                {
                        //                    case "9":
                        //                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_MergerForwarding;
                        //                        smsNode.SmsContent = strExport;
                        //                        smsNode.StrTitle = strMsgForWeb;
                        //                        if (this.mWeChatMsgWebPage.lstMergerForwardInfo.Count > 0)
                        //                        {
                        //                            List<IMHtmlNode> lstNode = new List<IMHtmlNode>();
                        //                            //_MFdMsgId = wItem.MessageId;
                        //                            //List<MergerForwardInfo> lstInfo = this.mWeChatMsgWebPage.lstMergerForwardInfo.FindAll(ListFind);
                        //                            List<MergerForwardInfo> lstInfo = mWeChatMsgWebPage.lstMergerForwardInfo.FindAll((m) =>
                        //                            {
                        //                                return m.MsgId.Equals(wItem.MessageId, StringComparison.OrdinalIgnoreCase);
                        //                            });
                        //                            foreach (MergerForwardInfo item in lstInfo)
                        //                            {
                        //                                IMHtmlNode hnode = new IMHtmlNode();
                        //                                string stricopath = WechatHeplerPC.GetHeadIconUrl(item.Realchatname, this.mCurrentWAInfo);
                        //                                if (string.IsNullOrEmpty(stricopath))
                        //                                    stricopath = "../../assets/images/default.png";

                        //                                hnode.HeaderIconUrl = stricopath;
                        //                                hnode.NickName = item.Sourcename;
                        //                                string strContent = item.Datadesc;
                        //                                switch (item.MsgType)
                        //                                {
                        //                                    case "1":
                        //                                        strContent = item.Datadesc;
                        //                                        break;
                        //                                    case "2":
                        //                                        strContent = "[这是张图片，暂未支持显示]";
                        //                                        break;
                        //                                    case "3":
                        //                                        strContent = "[这是语音，暂未支持显示]";
                        //                                        break;
                        //                                    case "4":
                        //                                        strContent = "[这是个视频，暂未支持显示]";
                        //                                        break;
                        //                                    case "5":
                        //                                        if (item.Link.Length > 0)
                        //                                            strContent = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", item.Link, item.Title);// string.Format("[{0}]({1})", item.Datadesc, item.Link);
                        //                                        else
                        //                                            strContent = string.Format("{0}  {1}", item.Title, item.Datadesc);
                        //                                        break;
                        //                                    case "6":
                        //                                        strContent = "[这是位置信息，暂未支持显示]";
                        //                                        break;
                        //                                    case "8":
                        //                                        strContent = "[这是个文件，暂未支持显示]";
                        //                                        break;
                        //                                    case "17":
                        //                                        strContent = item.Datadesc;
                        //                                        break;
                        //                                    case "19":
                        //                                    case "40":
                        //                                        strContent = "[小程序]" + item.Title;
                        //                                        break;

                        //                                    default:
                        //                                        if (item.Title.Length > 0)
                        //                                            strContent = item.Title;
                        //                                        else if (item.Datadesc.Length > 0)
                        //                                            strContent = item.Datadesc;
                        //                                        else
                        //                                            //Common.LogException(wItem.MessageContent, "导出暂未兼容异常");
                        //                                            Common.Log(string.Format("导出暂未兼容异常类型{0}，内容：{1}", wItem.MessageType, wItem.MessageContent), true);
                        //                                        break;
                        //                                }
                        //                                hnode.SmsContent = strContent;
                        //                                hnode.SmsTime = item.Sourcetime;
                        //                                hnode.UserName = item.Realchatname;
                        //                                lstNode.Add(hnode);
                        //                            }
                        //                            Folder.CheckFolder(strHtmlFilePath.Replace(".html", ""));
                        //                            string strDetailHtml = string.Format("{0}-{1}-{2}.html", wItem.MessageDT.ToString("yyyyMMddHHmmss"), wItem.MessageId, strNickNameEx);
                        //                            string strDetailHtmlPath = Path.Combine(strHtmlFilePath.Replace(".html", ""), strDetailHtml);

                        //                            IMHtmlNodeHelper.CreateDetail(strFileName.Replace("_", "-"), strDetailHtmlPath, lstNode, isPreview);
                        //                            smsNode.StrLink = Path.GetFileName(strHtmlFilePath.Replace(".html", "")) + @"\" + strDetailHtml;
                        //                        }
                        //                        break;
                        //                    case "10":
                        //                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_DocumentMessage;
                        //                        smsNode.SmsContent = strExport;
                        //                        string strSuffix = "";
                        //                        string strFName = "";
                        //                        if (strExport.Contains("."))
                        //                        {
                        //                            strSuffix = strExport.Substring(strExport.IndexOf(".")).Replace("[", "").Replace("]", "");
                        //                            strFName = strExport.Replace("[文件:", "").Replace("]", "").Replace(strSuffix, "");
                        //                        }
                        //                        string strIconName = "";
                        //                        if (strSuffix.Contains(".doc"))
                        //                            strIconName = "pic_doc.png";
                        //                        else if (strSuffix.Contains(".ppt"))
                        //                            strIconName = "pic_ppt.png";
                        //                        else if (strSuffix.Contains(".xls"))
                        //                            strIconName = "pic_xls.png";
                        //                        else
                        //                            strIconName = "pic_what.png";

                        //                        smsNode.StrMsgIcon = string.Format("../assets/images/{0}", strIconName);
                        //                        string strPath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, false, this.mCurrentWeChatiOSDevice, out intMapKey);
                        //                        string assetsname = string.Format("{0}-{1}-{2}{4}{3}", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx, strSuffix, strFName.Length > 0 ? "-" + strFName : "");

                        //                        // 文件名可以包含#&+ 等字符
                        //                        assetsname = Utility.ReplaceWinIllegalName(assetsname);//.Replace("#", "").Replace("&", "").Replace("+", "").Replace("%", "").Replace(" ", "");

                        //                        if (File.Exists(strPath))
                        //                        {
                        //                            Folder.CheckFolder(strAttachmentPath);

                        //                            File.Copy(strPath, Path.Combine(strAttachmentPath, assetsname), true);
                        //                            smsNode.StrSize = Utility.FormatFileSize(new FileInfo(strPath).Length);
                        //                        }
                        //                        smsNode.StrLink = strAttachment + @"/" + assetsname;
                        //                        break;
                        //                    default:
                        //                        break;
                        //                }

                        //                if (isLinkA && strExport.Length > 0)
                        //                    strMsgForWeb = strExport;

                        //            }
                        //            else
                        //            {
                        //                smsNode.NType = IMHtmlNodeType.IMHtmlNode_Media;
                        //                strTypeForWeb = "3";
                        //                arrHtml5ContentForWeb = GetWebPageJsonArray(strMsgForWeb);
                        //                //网页json
                        //                try
                        //                {
                        //                    //兼容链接（只有title和url）
                        //                    if (arrHtml5ContentForWeb == null || arrHtml5ContentForWeb.Count == 0)
                        //                    {
                        //                        JsonArray webLinkJsonArray = GetWebLinkJsonArray(strMsgForWeb);

                        //                        if (webLinkJsonArray != null && webLinkJsonArray.Count > 0)
                        //                        {
                        //                            JsonObject dicWebPage = (JsonObject)webLinkJsonArray[0];
                        //                            string strTitle = ((JsonString)dicWebPage["title"]).Value;
                        //                            string strUrl = ((JsonString)dicWebPage["url"]).Value;

                        //                            if (!string.IsNullOrEmpty(strTitle) && !string.IsNullOrEmpty(strUrl))
                        //                            {
                        //                                strTypeForWeb = "0";
                        //                                arrHtml5ContentForWeb = null;
                        //                                isLinkA = true;
                        //                                strMsgForWeb = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</span></a>", strUrl, strTitle);
                        //                            }
                        //                            else
                        //                            {
                        //                                strTypeForWeb = "0";
                        //                                arrHtml5ContentForWeb = null;
                        //                                strMsgForWeb = "[动画表情]";
                        //                            }
                        //                        }
                        //                        else
                        //                        {
                        //                            string strTemp = ExportHelper.GetWebTagFromReg(strMsgForWeb, "<title>(?<num>.*?)</title>", "num");

                        //                            if (strTemp.Length > 0)
                        //                            {

                        //                                if (strTemp.Contains("<![CDATA["))
                        //                                    strTemp = strTemp.Replace("<![CDATA[", "");

                        //                                if (strTemp.Contains("]]>"))
                        //                                    strTemp = strTemp.Replace("]]>", "");

                        //                                strTypeForWeb = "0";
                        //                                arrHtml5ContentForWeb = null;
                        //                                strMsgForWeb = strTemp;
                        //                            }
                        //                        }
                        //                    }
                        //                }
                        //                catch (Exception ex)
                        //                {
                        //                    Common.LogException(ex.ToString(), "GetWebLinkJsonArray2");
                        //                }
                        //            }
                        //            break;
                        //        case 50:
                        //            strMsgForWeb = "[视频未接通]";
                        //            break;

                        //        case 10000:
                        //            if (strMsgForWeb.Contains("img src=\"SystemMessages_HongbaoIcon.png"))
                        //                strMsgForWeb = ExportHelper.GetWebTagFromReg(strMsgForWeb, ">(?<num>.*?)<", "num");
                        //            strTypeForWeb = "4";
                        //            smsNode.NType = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        //            break;
                        //        default:
                        //            strMsgForWeb = wItem.MessageContent;

                        //            break;
                        //    }
                        //}
                        //catch { }
                        //if (arrHtml5ContentForWeb != null)
                        //{
                        //    smsNode.ArrContentForWeb = arrHtml5ContentForWeb;
                        //}
                        //else
                        //{
                        //    string strSW = "";
                        //    if (!isLinkA)
                        //        strMsgForWeb = HttpUtility.HtmlEncode(GetChatMsgForText(strMsgForWeb, wItem.SendId, ChatMsgType.ShowOnWeb, ref strSW));

                        //    if (isPreview)
                        //    {
                        //        int mt = wItem.MessageType;

                        //        if (mt != 3 && mt != 34 && mt != 43 && mt != 42 && mt != 62 && mt != 49 && mt != 10000)
                        //        {
                        //            if (strMsgForWeb.Contains("^../assets/expression"))
                        //                strMsgForWeb = strMsgForWeb.Substring(0, strMsgForWeb.IndexOf("^../assets/expression"));

                        //            if (strMsgForWeb.Length <= 1)
                        //                strMsgForWeb = mStrTrialMark;
                        //            else
                        //                strMsgForWeb = strMsgForWeb.Substring(0, strMsgForWeb.Length / 2) + mStrTrialMark;
                        //        }
                        //    }

                        //    if (isLinkA)
                        //        smsNode.SmsContent = strMsgForWeb;
                        //    else
                        //        smsNode.SmsContent = MakeText(strMsgForWeb, true);
                        //}

                        //smsNode.SmsTime = WechatHeplerPC.ConvertWeixinToPcTime(wItem.MessageDateTime).ToString("yyyy-MM-dd HH:mm");

                        smsNode = CreateIMHtmlNodeHandle(wItem, wTalk, dicWeChatFriendInfo, isPreview, strAttachmentPath, strHtmlFilePath, strAttachment, strFileName);

                        if (wItem.SvrMsg != null)
                        {
                            IMHtmlNode svrNode = CreateIMHtmlNodeHandle(wItem.SvrMsg, wTalk, dicWeChatFriendInfo, isPreview, strAttachmentPath, strHtmlFilePath, strAttachment, strFileName);
                            smsNode.SvrIMHtmlNode = svrNode;
                        }

                        lstIMHtmlNode.Add(smsNode);

                        string strPreview = "";
                        if (isPreview)
                            strPreview = "preview";
                        IMHtmlNodeHelper.CreateHtmlContent(strHtmlFilePath, lstIMHtmlNode, strPreview);
                        lstIMHtmlNode.Clear();
                        if (intIndex == wTalk.MessageItems.Count)
                        {
                            try
                            {
                                ExportHtmlMainInfo mInfo = new ExportHtmlMainInfo();
                                mInfo.id = lstExportHtmlMainInfo.Count + 1;
                                mInfo.LastMessage = smsNode.SmsContent;
                                mInfo.IconImage = WechatHeplerPC.GetHeadIconUrl(wTalk.UserName, this.mCurrentWAInfo);
                                if (string.IsNullOrEmpty(mInfo.IconImage))
                                    mInfo.IconImage = "assets/images/default.png";

                                mInfo.LastMessageTime = WechatHeplerPC.ConvertWeixinToPcTime(wItem.MessageDateTime);
                                if (wTalk.NickName.Length > 0)
                                {
                                    mInfo.UserName = wTalk.NickName;
                                }
                                else
                                {
                                    mInfo.UserName = wItem.SendId;
                                }
                                mInfo.StrUrl = Path.Combine("content", Path.GetFileName(strHtmlFilePath));
                                bool isExist = false;
                                ExportHtmlMainInfo tmp = null;
                                foreach (ExportHtmlMainInfo item in lstExportHtmlMainInfo)
                                {
                                    if (mInfo.UserName == item.UserName)
                                    {
                                        tmp = item;
                                        mInfo.id = item.id;
                                        isExist = true;
                                        continue;
                                    }
                                }
                                if (isExist)
                                    lstExportHtmlMainInfo.Remove(tmp);
                                lstExportHtmlMainInfo.Add(mInfo);
                            }
                            catch (Exception ex)
                            {
                                Common.LogException(ex.ToString(), "DoExportHtmlInfo_AddMainInfo");
                            }
                        }
                    }

                    //IMHtmlNodeHelper.CreateHtml(strFileName, strHtmlFilePath, lstIMHtmlNode, isPreview);
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "DoExportHtmlInfo");
                }
            }
        }

        private IMHtmlNode CreateIMHtmlNodeHandle(WechatMessageItem wItem, WechatTalk wTalk, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo, bool isPreview,
            string strAttachmentPath, string strHtmlFilePath, string strAttachment, string strFileName)
        {
            string strTypeForWeb = string.Empty;        // '0：文本，1：语音，2：图片，3：网页，4：系统消息
            string strMsgForWeb = "";
            string strFromForWeb = "";     //'0：对方，1：自己
            string strNickName = "";
            string strWeChatNO = "";
            string strWeChatID = "";
            string strUsrName = "";
            string strPetName = "";
            JsonArray arrHtml5ContentForWeb = null;
            string strIconPath = "";
            int intIndex = 0;
            bool isLinkA = false;

            IMHtmlNode smsNode = new IMHtmlNode();

            strMsgForWeb = wItem.MessageContent;//群组的信息里面包括联系人名称
            GetChatMsg(wItem, ChatMsgType.ShowOnExcelFile, isPreview, ref strWeChatID);

            GetFriendInformation(wTalk, dicWeChatFriendInfo, wItem, ref strNickName, ref strWeChatNO, ref strWeChatID, ref strUsrName, ref strFromForWeb, ref strPetName);

            smsNode = new IMHtmlNode();
            smsNode.IsItemMe = (strFromForWeb == "0" ? false : true);
            smsNode.UserID = strWeChatID;

            if (strWeChatID.Length > 0 && strNickName.Length == 0)
            {
                foreach (WeChatFriendInfo wcfi in this.mCurrentWAInfo.DicWeChatFriendInfo.Values)
                {
                    if (wcfi.StrWXID.Equals(strWeChatID, StringComparison.InvariantCultureIgnoreCase))
                    {
                        strNickName = wcfi.StrNickName;
                        strUsrName = strWeChatID;

                        break;
                    }
                }
            }
            smsNode.NickName = strNickName;
            smsNode.UserName = strWeChatNO;
            smsNode.WeChatID = strNickName;
            smsNode.IsChatRoom = WechatHeplerPC.CheckIsChatRoom(wItem.SendId);

            string strHeadIcon = WechatHeplerPC.GetHeadIconPathFromPC(strUsrName, this.mCurrentWAInfo);
            if (File.Exists(strHeadIcon))
            {
                string strHeadIconPath = Path.Combine(Path.GetDirectoryName(strAttachmentPath), "HeadIcon");
                Folder.CheckFolder(strHeadIconPath);
                strIconPath = string.Format("../attachment/HeadIcon/{0}", Path.GetFileName(strHeadIcon));
                File.Copy(strHeadIcon, Path.Combine(strHeadIconPath, Path.GetFileName(strHeadIcon)), true);
            }
            else
            {
                strIconPath = WechatHeplerPC.GetHeadIconUrl(strUsrName, this.mCurrentWAInfo);
            }
            if (string.IsNullOrEmpty(strIconPath))
                strIconPath = "../assets/images/default.png";

            smsNode.HeaderIconUrl = strIconPath;

            //3、消息类型与内容
            //wx： 1文本；3图片；34语音；42名片；43视频；47特殊表情；48地理位置；49网页；50视频未接通的消息；1000系统消息              
            arrHtml5ContentForWeb = null;
            strTypeForWeb = "0";

            int intMapKey = 0;

            string strNickNameEx = Utility.ReplaceWinIllegalName(strNickName);
            strNickNameEx = Utility.ReplaceWinIllegalNameToHtml(strNickNameEx);
            try
            {
                switch (wItem.MessageType)
                {
                    case 1:
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Text;
                        strTypeForWeb = "0";

                        //检查是否有url
                        //string strCheckIsContainUrl = strMsgForWeb;
                        //bool blnHighlight = true;
                        //if (string.Compare(strCheckIsContainUrl, strMsgForWeb) != 0)
                        //{
                        //    blnHighlight = false;
                        //    strMsgForWeb = strCheckIsContainUrl;
                        //}

                        //表情符号转换成表情图片
                        strMsgForWeb = this.OperateForExpression(strMsgForWeb, true);

                        break;
                    case 34:
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Voice;
                        string strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, true, this.mCurrentWeChatiOSDevice, out intMapKey);
                        strMsgForWeb = WechatHeplerPC.GetChatVoiceLength(wItem.MessageContent, true).ToString();
                        //smsNode.AttachPath = strAttachment + @"/" + string.Format("{0}.wav", Item.MessageDT.ToString("yyyyMMddHHmmss") + "-" + intMapKey);
                        smsNode.AttachPath = strAttachment + string.Format(@"/{0}-{1}-{2}.mp3", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        strTypeForWeb = "1";
                        break;

                    case 3:
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Image;
                        strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, false, this.mCurrentWeChatiOSDevice, out intMapKey);
                        if (strTempFilePath.Length == 0)
                            strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, true, this.mCurrentWeChatiOSDevice, out intMapKey);
                        string strImagePath = strAttachment + string.Format(@"/{0}-{1}-{2}.png", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        smsNode.AttachPath = strImagePath;
                        smsNode.ThumbnailPath = strImagePath;//strAttachment + string.Format(@"/{0}-{1}-{2}.png", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);

                        strTypeForWeb = "2";

                        break;
                    case 62:
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Image;
                        strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, false, this.mCurrentWeChatiOSDevice, out intMapKey);
                        if (strTempFilePath.Length > 0)
                            smsNode.AttachPath = strAttachment + string.Format(@"/{0}-{1}-{2}.mp4", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        else
                            //备份的时候手机上没有预览视频，无法加载视频，请重新备份
                            smsNode.AttachPath = "抱歉，暂时无法加载此小视频！";

                        smsNode.ThumbnailPath = "../assets/images/default_video.png";
                        strTypeForWeb = "2";
                        break;

                    case 42:
                        strMsgForWeb = "[名片]";
                        break;

                    case 43:
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Video;
                        strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, false, this.mCurrentWeChatiOSDevice, out intMapKey);
                        smsNode.AttachPath = strAttachment + string.Format(@"/{0}-{1}-{2}.mp4", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, true, this.mCurrentWeChatiOSDevice, out intMapKey);
                        smsNode.ThumbnailPath = strAttachment + string.Format(@"/{0}-{1}-{2}.png", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        strTypeForWeb = "2";
                        break;

                    case 47:
                        strMsgForWeb = "[动画表情]";
                        break;

                    case 48:
                        strMsgForWeb = "[地理位置]";
                        break;

                    case 49:
                        if (!Common.OldAnalysisWebpage())
                        {
                            IMHtmlNodeType imtype = IMHtmlNodeType.IMHtmlNode_Media;

                            strTypeForWeb = "3";
                            string strExport = "";

                            this.mWeChatMsgWebPage.AnalysisNewWebpage(wItem, ref strMsgForWeb, ref arrHtml5ContentForWeb, ref strTypeForWeb, ref strExport, ref imtype, ref isLinkA);
                            smsNode.NType = imtype;
                            switch (strTypeForWeb)
                            {
                                case "9":
                                    smsNode.NType = IMHtmlNodeType.IMHtmlNode_MergerForwarding;
                                    smsNode.SmsContent = strExport;
                                    smsNode.StrTitle = strMsgForWeb;
                                    if (this.mWeChatMsgWebPage.lstMergerForwardInfo.Count > 0)
                                    {
                                        List<IMHtmlNode> lstNode = new List<IMHtmlNode>();
                                        //_MFdMsgId = wItem.MessageId;
                                        //List<MergerForwardInfo> lstInfo = this.mWeChatMsgWebPage.lstMergerForwardInfo.FindAll(ListFind);
                                        List<MergerForwardInfo> lstInfo = mWeChatMsgWebPage.lstMergerForwardInfo.FindAll((m) =>
                                        {
                                            return m.MsgId.Equals(wItem.MessageId, StringComparison.OrdinalIgnoreCase);
                                        });
                                        foreach (MergerForwardInfo item in lstInfo)
                                        {
                                            IMHtmlNode hnode = new IMHtmlNode();
                                            string stricopath = WechatHeplerPC.GetHeadIconUrl(item.Realchatname, this.mCurrentWAInfo);
                                            if (string.IsNullOrEmpty(stricopath))
                                                stricopath = "../../assets/images/default.png";

                                            hnode.HeaderIconUrl = stricopath;
                                            hnode.NickName = item.Sourcename;
                                            string strContent = item.Datadesc;
                                            switch (item.MsgType)
                                            {
                                                case "1":
                                                    strContent = item.Datadesc;
                                                    break;
                                                case "2":
                                                    strContent = "[这是张图片，暂未支持显示]";
                                                    break;
                                                case "3":
                                                    strContent = "[这是语音，暂未支持显示]";
                                                    break;
                                                case "4":
                                                    strContent = "[这是个视频，暂未支持显示]";
                                                    break;
                                                case "5":
                                                    if (item.Link.Length > 0)
                                                        strContent = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", item.Link, item.Title);// string.Format("[{0}]({1})", item.Datadesc, item.Link);
                                                    else
                                                        strContent = string.Format("{0}  {1}", item.Title, item.Datadesc);
                                                    break;
                                                case "6":
                                                    strContent = "[这是位置信息，暂未支持显示]";
                                                    break;
                                                case "8":
                                                    strContent = "[这是个文件，暂未支持显示]";
                                                    break;
                                                case "17":
                                                    strContent = item.Datadesc;
                                                    break;
                                                case "19":
                                                case "40":
                                                    strContent = "[小程序]" + item.Title;
                                                    break;

                                                default:
                                                    if (item.Title.Length > 0)
                                                        strContent = item.Title;
                                                    else if (item.Datadesc.Length > 0)
                                                        strContent = item.Datadesc;
                                                    else
                                                        //Common.LogException(wItem.MessageContent, "导出暂未兼容异常");
                                                        Common.Log(string.Format("导出暂未兼容异常类型{0}，内容：{1}", wItem.MessageType, wItem.MessageContent), true);
                                                    break;
                                            }
                                            hnode.SmsContent = strContent;
                                            hnode.SmsTime = item.Sourcetime;
                                            hnode.UserName = item.Realchatname;
                                            lstNode.Add(hnode);
                                        }
                                        Folder.CheckFolder(strHtmlFilePath.Replace(".html", ""));
                                        string strDetailHtml = string.Format("{0}-{1}-{2}.html", wItem.MessageDT.ToString("yyyyMMddHHmmss"), wItem.MessageId, strNickNameEx);
                                        string strDetailHtmlPath = Path.Combine(strHtmlFilePath.Replace(".html", ""), strDetailHtml);

                                        IMHtmlNodeHelper.CreateDetail(strFileName.Replace("_", "-"), strDetailHtmlPath, lstNode, isPreview);
                                        smsNode.StrLink = Path.GetFileName(strHtmlFilePath.Replace(".html", "")) + @"\" + strDetailHtml;
                                    }
                                    break;
                                case "10":
                                    smsNode.NType = IMHtmlNodeType.IMHtmlNode_DocumentMessage;
                                    smsNode.SmsContent = strExport;
                                    string strSuffix = "";
                                    string strFName = "";
                                    if (strExport.Contains("."))
                                    {
                                        strSuffix = strExport.Substring(strExport.IndexOf(".")).Replace("[", "").Replace("]", "");
                                        strFName = strExport.Replace("[文件:", "").Replace("]", "").Replace(strSuffix, "");
                                    }
                                    string strIconName = "";
                                    if (strSuffix.Contains(".doc"))
                                        strIconName = "pic_doc.png";
                                    else if (strSuffix.Contains(".ppt"))
                                        strIconName = "pic_ppt.png";
                                    else if (strSuffix.Contains(".xls"))
                                        strIconName = "pic_xls.png";
                                    else if (strSuffix.Contains(".txt"))
                                        strIconName = "icon_quote_document_default.svg";
                                    else if (strSuffix.Contains(".pdf"))
                                        strIconName = "icon_quote_document_default.svg";
                                    else
                                        strIconName = "pic_what.png";

                                    smsNode.StrMsgIcon = string.Format("../assets/images/{0}", strIconName);
                                    string strPath = mWechatHeplerPC.GetMediaFilePath(wItem, this.mStrCurrentBackupDBPath, this.mStrCurrentBackupDBPassword, this.mStrCurrentBackupMediaFile, this.mCurrentWAInfo.PwdBuffer, false, this.mCurrentWeChatiOSDevice, out intMapKey);
                                    string assetsname = string.Format("{0}-{1}-{2}{4}{3}", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx, strSuffix, strFName.Length > 0 ? "-" + strFName : "");

                                    // 文件名可以包含#&+ 等字符
                                    assetsname = Utility.ReplaceWinIllegalName(assetsname);//.Replace("#", "").Replace("&", "").Replace("+", "").Replace("%", "").Replace(" ", "");

                                    if (File.Exists(strPath))
                                    {
                                        Folder.CheckFolder(strAttachmentPath);

                                        File.Copy(strPath, Path.Combine(strAttachmentPath, assetsname), true);
                                        smsNode.StrSize = Utility.FormatFileSize(new FileInfo(strPath).Length);
                                    }
                                    smsNode.StrLink = strAttachment + @"/" + assetsname;
                                    break;
                                default:
                                    break;
                            }

                            if (isLinkA && strExport.Length > 0)
                                strMsgForWeb = strExport;

                        }
                        else
                        {
                            smsNode.NType = IMHtmlNodeType.IMHtmlNode_Media;
                            strTypeForWeb = "3";
                            arrHtml5ContentForWeb = GetWebPageJsonArray(strMsgForWeb);
                            //网页json
                            try
                            {
                                //兼容链接（只有title和url）
                                if (arrHtml5ContentForWeb == null || arrHtml5ContentForWeb.Count == 0)
                                {
                                    JsonArray webLinkJsonArray = GetWebLinkJsonArray(strMsgForWeb);

                                    if (webLinkJsonArray != null && webLinkJsonArray.Count > 0)
                                    {
                                        JsonObject dicWebPage = (JsonObject)webLinkJsonArray[0];
                                        string strTitle = ((JsonString)dicWebPage["title"]).Value;
                                        string strUrl = ((JsonString)dicWebPage["url"]).Value;

                                        if (!string.IsNullOrEmpty(strTitle) && !string.IsNullOrEmpty(strUrl))
                                        {
                                            strTypeForWeb = "0";
                                            arrHtml5ContentForWeb = null;
                                            isLinkA = true;
                                            strMsgForWeb = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</span></a>", strUrl, strTitle);
                                        }
                                        else
                                        {
                                            strTypeForWeb = "0";
                                            arrHtml5ContentForWeb = null;
                                            strMsgForWeb = "[动画表情]";
                                        }
                                    }
                                    else
                                    {
                                        string strTemp = ExportHelper.GetWebTagFromReg(strMsgForWeb, "<title>(?<num>.*?)</title>", "num");

                                        if (strTemp.Length > 0)
                                        {

                                            if (strTemp.Contains("<![CDATA["))
                                                strTemp = strTemp.Replace("<![CDATA[", "");

                                            if (strTemp.Contains("]]>"))
                                                strTemp = strTemp.Replace("]]>", "");

                                            strTypeForWeb = "0";
                                            arrHtml5ContentForWeb = null;
                                            strMsgForWeb = strTemp;
                                        }
                                    }
                                }
                            }
                            catch (Exception ex)
                            {
                                Common.LogException(ex.ToString(), "GetWebLinkJsonArray2");
                            }
                        }
                        break;
                    case 50:
                        strMsgForWeb = "[视频未接通]";
                        break;

                    case 10000:
                        if (strMsgForWeb.Contains("img src=\"SystemMessages_HongbaoIcon.png"))
                            strMsgForWeb = ExportHelper.GetWebTagFromReg(strMsgForWeb, ">(?<num>.*?)<", "num");
                        strTypeForWeb = "4";
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        break;
                    default:
                        strMsgForWeb = wItem.MessageContent;

                        break;
                }
            }
            catch { }
            if (arrHtml5ContentForWeb != null)
            {
                smsNode.ArrContentForWeb = arrHtml5ContentForWeb;
            }
            else
            {
                string strSW = "";
                if (!isLinkA)
                    strMsgForWeb = HttpUtility.HtmlEncode(GetChatMsgForText(strMsgForWeb, wItem.SendId, ChatMsgType.ShowOnWeb, ref strSW));

                if (isPreview)
                {
                    int mt = wItem.MessageType;

                    if (mt != 3 && mt != 34 && mt != 43 && mt != 42 && mt != 62 && mt != 49 && mt != 10000)
                    {
                        if (strMsgForWeb.Contains("^../assets/expression"))
                            strMsgForWeb = strMsgForWeb.Substring(0, strMsgForWeb.IndexOf("^../assets/expression"));

                        if (strMsgForWeb.Length <= 1)
                            strMsgForWeb = mStrTrialMark;
                        else
                            strMsgForWeb = strMsgForWeb.Substring(0, strMsgForWeb.Length / 2) + mStrTrialMark;
                    }
                }

                if (isLinkA)
                    smsNode.SmsContent = strMsgForWeb;
                else
                    smsNode.SmsContent = MakeText(strMsgForWeb, true);
            }

            smsNode.SmsTime = WechatHeplerPC.ConvertWeixinToPcTime(wItem.MessageDateTime).ToString("yyyy-MM-dd HH:mm");


            return smsNode;
        }

        private List<ExportHtmlMainInfo> listHtmlMainInfors_ = new List<ExportHtmlMainInfo>();
        private List<IMHtmlNode>[] aryListIMHtmlNodes_ = null;

        private void ExportHtmlInfoThread(object objHtmls)
        {
            ExportHtmlInfoThread(objHtmls, null);
        }
        private void ExportHtmlInfoThread(object objHtmls, string tip = null)
        {
            object[] objItems = (object[])objHtmls;
            WechatTalk wTalk = objItems[0] as WechatTalk;
            WechatMessageItem[] aryItems = objItems[1] as WechatMessageItem[];
            object[] aryArgs = objItems[2] as object[];
            ManualResetEvent e = objItems[3] as ManualResetEvent;
            int iThread = (int)objItems[4];
            int thdTotal = (int)objItems[5];

            aryListIMHtmlNodes_[iThread] = new List<IMHtmlNode>();

            string strAttachmentPath = aryArgs[0].ToString();
            string strHtmlFilePath = aryArgs[1].ToString();
            string strAttachment = aryArgs[2].ToString();
            string strFileName = aryArgs[3].ToString();
            bool isPreview = (bool)aryArgs[4];

            string strTypeForWeb = string.Empty;        // '0：文本，1：语音，2：图片，3：网页，4：系统消息
            string strMsgForWeb = "";
            string strFromForWeb = "";     //'0：对方，1：自己
            string strNickName = "";
            string strWeChatNO = "";
            string strWeChatID = "";
            string strUsrName = "";
            string strIconPath = "";
            string strPetName = "";
            bool isLinkA = false;

            int iIndex = 0;
            for (int i = aryItems.Length - 1; i >= 0; i--)
            {
                WechatMessageItem wItem = aryItems[i];
                strMsgForWeb = wItem.MessageContent;
                if (string.IsNullOrWhiteSpace(strMsgForWeb))
                    continue;

                if (string.IsNullOrEmpty(tip))
                    ExportProgress(++iIndex, aryItems.Length, "HTML");
                else
                    ExportProgress(++iIndex, aryItems.Length, tip + "HTML");


                strUsrName = "";
                strNickName = "";
                isLinkA = false;

                GetChatMsg(wItem, ChatMsgType.ShowOnExcelFile, isPreview, ref strWeChatID);

                GetFriendInformation(wTalk, mCurrentWAInfo.DicWeChatFriendInfo, wItem, ref strNickName, ref strWeChatNO, ref strWeChatID, ref strUsrName, ref strFromForWeb, ref strPetName);

                IMHtmlNode htmlNode = new IMHtmlNode();
                htmlNode.IsItemMe = !strFromForWeb.Equals("0", StringComparison.OrdinalIgnoreCase);
                htmlNode.UserID = strWeChatID;

                if (strWeChatID.Length > 0 && strNickName.Length == 0)
                {
                    foreach (WeChatFriendInfo wcfi in this.mCurrentWAInfo.DicWeChatFriendInfo.Values)
                    {
                        if (wcfi.StrWXID.Equals(strWeChatID, StringComparison.InvariantCultureIgnoreCase))
                        {
                            strNickName = wcfi.StrNickName;
                            strUsrName = strWeChatID;

                            break;
                        }
                    }
                }

                htmlNode.NickName = strNickName;
                htmlNode.UserName = strWeChatNO;
                htmlNode.WeChatID = strNickName;
                htmlNode.IsChatRoom = WechatHeplerPC.CheckIsChatRoom(wItem.SendId);

                strIconPath = WechatHeplerPC.GetHeadIconUrl(strUsrName, mCurrentWAInfo);
                if (string.IsNullOrEmpty(strIconPath))
                    strIconPath = "../assets/images/default.png";

                htmlNode.HeaderIconUrl = strIconPath;

                JsonArray arrHtml5ContentForWeb = null;
                strTypeForWeb = "0";

                int intMapKey = 0;
                string strNickNameEx = Utility.ReplaceWinIllegalName(strNickName);
                switch (wItem.MessageType)
                {
                    case 1:
                        strTypeForWeb = "0";
                        htmlNode.NType = IMHtmlNodeType.IMHtmlNode_Text;
                        strMsgForWeb = this.OperateForExpression(strMsgForWeb, true);
                        break;
                    case 34:
                        strTypeForWeb = "1";
                        htmlNode.NType = IMHtmlNodeType.IMHtmlNode_Voice;
                        string strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                            mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, true, mCurrentWeChatiOSDevice, out intMapKey);

                        strMsgForWeb = WechatHeplerPC.GetChatVoiceLength(wItem.MessageContent, true).ToString();

                        htmlNode.AttachPath = string.Format(@"{3}/{0}-{1}-{2}.mp3", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx, strAttachment);
                        break;
                    case 3:
                        strTypeForWeb = "2";
                        htmlNode.NType = IMHtmlNodeType.IMHtmlNode_Image;
                        strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                            mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, false, mCurrentWeChatiOSDevice, out intMapKey);

                        if (strTempFilePath.Length == 0)
                            strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                                mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, true, mCurrentWeChatiOSDevice, out intMapKey);

                        string strImagePath = string.Format(@"{3}/{0}-{1}-{2}.png", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx, strAttachment);
                        htmlNode.AttachPath = strImagePath;
                        htmlNode.ThumbnailPath = strImagePath;
                        break;
                    case 62:
                        strTypeForWeb = "2";
                        htmlNode.NType = IMHtmlNodeType.IMHtmlNode_Image;
                        strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                            mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, false, mCurrentWeChatiOSDevice, out intMapKey);

                        if (strTempFilePath.Length > 0)
                            htmlNode.AttachPath = strAttachment + string.Format(@"/{0}-{1}-{2}.mp4", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        else
                            htmlNode.AttachPath = "抱歉，暂时无法加载此小视频！";

                        htmlNode.ThumbnailPath = "../assets/images/default_video.png";
                        break;
                    case 42:
                        strMsgForWeb = "[名片]";
                        break;
                    case 43:
                        strTypeForWeb = "2";
                        htmlNode.NType = IMHtmlNodeType.IMHtmlNode_Video;

                        strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                            mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, false, mCurrentWeChatiOSDevice, out intMapKey);

                        htmlNode.AttachPath = strAttachment + string.Format(@"/{0}-{1}-{2}.mp4", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);

                        strTempFilePath = mWechatHeplerPC.GetMediaFilePath(wItem, mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                            mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, true, mCurrentWeChatiOSDevice, out intMapKey);

                        htmlNode.ThumbnailPath = strAttachment + string.Format(@"/{0}-{1}-{2}.png", wItem.MessageDT.ToString("yyyyMMddHHmmss"), intMapKey, strNickNameEx);
                        break;
                    case 47:
                        strMsgForWeb = "[动画表情]";
                        break;
                    case 48:
                        strMsgForWeb = "[地理位置]";
                        break;
                    case 49:
                        if (!Common.OldAnalysisWebpage())
                        {
                            IMHtmlNodeType imtype = IMHtmlNodeType.IMHtmlNode_Media;
                            strTypeForWeb = "3";
                            string strExport = "";

                            mWeChatMsgWebPage.AnalysisNewWebpage(wItem, ref strMsgForWeb, ref arrHtml5ContentForWeb,
                                ref strTypeForWeb, ref strExport, ref imtype, ref isLinkA);

                            htmlNode.NType = imtype;
                            switch (strTypeForWeb)
                            {
                                case "9":
                                    htmlNode.NType = IMHtmlNodeType.IMHtmlNode_MergerForwarding;
                                    htmlNode.SmsContent = strExport;
                                    htmlNode.StrTitle = strMsgForWeb;

                                    if (mWeChatMsgWebPage.lstMergerForwardInfo.Count > 0)
                                    {
                                        List<IMHtmlNode> listNodes = new List<IMHtmlNode>();
                                        List<MergerForwardInfo> listInfos = mWeChatMsgWebPage.lstMergerForwardInfo.FindAll((m) =>
                                        {
                                            return m.MsgId.Equals(wItem.MessageId, StringComparison.OrdinalIgnoreCase);
                                        });

                                        foreach (MergerForwardInfo m in listInfos)
                                        {
                                            IMHtmlNode node = new IMHtmlNode();
                                            string stricopath = WechatHeplerPC.GetHeadIconUrl(m.Realchatname, mCurrentWAInfo);
                                            if (string.IsNullOrEmpty(stricopath))
                                                stricopath = "../../assets/images/default.png";

                                            node.HeaderIconUrl = stricopath;
                                            node.NickName = m.Sourcename;
                                            string strContent = m.Datadesc;
                                            switch (m.MsgType)
                                            {
                                                case "1":
                                                case "17":
                                                    strContent = m.Datadesc;
                                                    break;
                                                case "2":
                                                    strContent = "[这是张图片，暂未支持显示]";
                                                    break;
                                                case "3":
                                                    strContent = "[这是语音，暂未支持显示]";
                                                    break;
                                                case "4":
                                                    strContent = "[这是个视频，暂未支持显示]";
                                                    break;
                                                case "5":
                                                    if (m.Link.Length > 0)
                                                        strContent = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", m.Link, m.Title);
                                                    else
                                                        strContent = string.Format("{0}  {1}", m.Title, m.Datadesc);
                                                    break;
                                                case "6":
                                                    strContent = "[这是位置信息，暂未支持显示]";
                                                    break;
                                                case "8":
                                                    strContent = "[这是个文件，暂未支持显示]";
                                                    break;
                                                case "19":
                                                case "40":
                                                    strContent = "[小程序]" + m.Title;
                                                    break;
                                                default:
                                                    if (m.Title.Length > 0)
                                                        strContent = m.Title;
                                                    else if (m.Datadesc.Length > 0)
                                                        strContent = m.Datadesc;
                                                    else
                                                        Common.Log(string.Format("导出暂未兼容异常类型{0}，内容：{1}", wItem.MessageType, wItem.MessageContent), true);

                                                    break;
                                            }
                                            node.SmsContent = strContent;
                                            node.SmsTime = m.Sourcename;
                                            node.UserName = m.Realchatname;
                                            listNodes.Add(node);
                                        }
                                        Folder.CheckFolder(strHtmlFilePath.Replace(".html", ""));
                                        string strDetailHtml = string.Format("{0}-{1}-{2}.html", wItem.MessageDT.ToString("yyyyMMddHHmmss"), wItem.MessageId, strNickNameEx);
                                        string strDetailHtmlPath = Path.Combine(strHtmlFilePath.Replace(".html", ""), strDetailHtml);
                                        IMHtmlNodeHelper.CreateDetail(strFileName.Replace("_", "-"), strDetailHtmlPath, listNodes, isPreview);
                                        htmlNode.StrLink = Path.GetFileName(strHtmlFilePath.Replace(".html", "")) + @"\" + strDetailHtml;
                                    }
                                    break;
                                case "10":
                                    htmlNode.NType = IMHtmlNodeType.IMHtmlNode_DocumentMessage;
                                    htmlNode.SmsContent = strExport;

                                    string strSuffix = "";
                                    string strFName = "";
                                    if (strExport.Contains("."))
                                    {
                                        strSuffix = strExport.Substring(strExport.IndexOf(".")).Replace("[", "").Replace("]", "");
                                        strFName = strExport.Replace("[文件:", "").Replace("]", "").Replace(strSuffix, "");
                                    }
                                    string strIconName = "";
                                    if (strSuffix.Contains(".doc"))
                                        strIconName = "pic_doc.png";
                                    else if (strSuffix.Contains(".ppt"))
                                        strIconName = "pic_ppt.png";
                                    else if (strSuffix.Contains(".xls"))
                                        strIconName = "pic_xls.png";
                                    else if (strSuffix.Contains(".pdf"))
                                        strIconName = "icon_quote_document_default.svg";
                                    else
                                        strIconName = "pic_what.png";

                                    htmlNode.StrMsgIcon = string.Format("../assets/images/{0}", strIconName);
                                    string tmpPath = mWechatHeplerPC.GetMediaFilePath(wItem, mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                                        mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, false, mCurrentWeChatiOSDevice, out intMapKey);

                                    string assetsname = string.Format("{0}-{1}-{2}{4}{3}", wItem.MessageDT.ToString("yyyyMMddHHmmss"),
                                            intMapKey, strNickNameEx, strSuffix, strFName.Length > 0 ? "-" + strFName : "");
                                    assetsname = Utility.ReplaceWinIllegalName(assetsname);
                                    //.Replace("#", "")
                                    //.Replace("&", "")
                                    //.Replace("+", "")
                                    //.Replace("%", "")
                                    //.Replace(" ", "");

                                    if (File.Exists(tmpPath))
                                    {
                                        Folder.CheckFolder(strAttachmentPath);

                                        File.Copy(tmpPath, Path.Combine(strAttachmentPath, assetsname));
                                        htmlNode.StrSize = Utility.FormatFileSize(new FileInfo(tmpPath).Length);
                                    }
                                    htmlNode.StrLink = strAttachment + @"/" + assetsname;
                                    break;
                            }

                            if (isLinkA && strExport.Length > 0)
                                strMsgForWeb = strExport;
                        }
                        break;
                    case 50:
                        strMsgForWeb = "[视频未接通]";
                        break;
                    case 10000:
                        strTypeForWeb = "4";
                        if (strMsgForWeb.Contains("img src=\"SystemMessages_HongbaoIcon.png"))
                            strMsgForWeb = ExportHelper.GetWebTagFromReg(strMsgForWeb, ">(?<num>.*?)<", "num");

                        htmlNode.NType = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        break;
                }

                if (arrHtml5ContentForWeb != null)
                {
                    htmlNode.ArrContentForWeb = arrHtml5ContentForWeb;
                }
                else
                {
                    string strSW = "";
                    if (!isLinkA)
                        strMsgForWeb = HttpUtility.HtmlEncode(GetChatMsgForText(strMsgForWeb, wItem.SendId, ChatMsgType.ShowOnWeb, ref strSW));

                    if (isPreview)
                    {
                        int mt = wItem.MessageType;
                        if (mt != 3 && mt != 34 && mt != 43 && mt != 42 && mt != 62 && mt != 49 && mt != 10000)
                        {
                            if (strMsgForWeb.Contains("^../assets/expression"))
                                strMsgForWeb = strMsgForWeb.Substring(0, strMsgForWeb.IndexOf("^../assets/expression"));

                            if (strMsgForWeb.Length <= 1)
                                strMsgForWeb = mStrTrialMark;
                            else
                                strMsgForWeb = strMsgForWeb.Substring(0, strMsgForWeb.Length / 2) + mStrTrialMark;
                        }
                    }

                    if (isLinkA)
                        htmlNode.SmsContent = strMsgForWeb;
                    else
                        htmlNode.SmsContent = MakeText(strMsgForWeb, true);
                }
                htmlNode.SmsTime = WechatHeplerPC.ConvertWeixinToPcTime(wItem.MessageDateTime).ToString("yyyy-MM-dd HH:mm");

                aryListIMHtmlNodes_[iThread].Add(htmlNode);

                if (iThread == 0 && i == 0)
                {
                    try
                    {
                        ExportHtmlMainInfo htmlInfor = new ExportHtmlMainInfo();
                        htmlInfor.id = listHtmlMainInfors_.Count + 1;
                        htmlInfor.LastMessage = htmlNode.SmsContent;
                        htmlInfor.IconImage = WechatHeplerPC.GetHeadIconUrl(wTalk.UserName, mCurrentWAInfo);
                        if (string.IsNullOrEmpty(htmlInfor.IconImage))
                            htmlInfor.IconImage = "assets/images/default.png";

                        htmlInfor.LastMessageTime = WechatHeplerPC.ConvertWeixinToPcTime(wItem.MessageDateTime);
                        if (wTalk.NickName.Length > 0)
                        {
                            htmlInfor.UserName = wTalk.NickName;
                        }
                        else
                        {
                            htmlInfor.UserName = wItem.SendId;
                        }
                        htmlInfor.StrUrl = Path.Combine("content", Path.GetFileName(strHtmlFilePath));
                        listHtmlMainInfors_.Add(htmlInfor);
                    }
                    catch { }
                }
            } // for...

            if (e != null)
                e.Set();

        }

        //private string _MFDetailId = "";
        //private string _MFdMsgId = "";

        //private bool ListFind(MergerForwardInfo info)
        //{
        //    if (string.IsNullOrEmpty(_MFDetailId))
        //        return info.MsgId.Equals(_MFdMsgId) ? true : false;
        //    else
        //        return info.MsgId.Equals(_MFdMsgId) && info.SrcMsgLocalid.Equals(_MFDetailId) ? true : false;
        //}

        private string OperateForExpression(string strContent, bool isExport = false)
        {
            string regular = @"\[(?<expression>.*?)\]";
            DataTable dt = Utility.GetMatchStringByRegularExpressions(strContent, new string[1] { regular }, new string[1] { "expression" });

            if (dt != null && dt.Rows.Count > 0)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    string strKey = FormatStringForWebPage(HttpUtility.UrlDecode(dr["expression"].ToString()));
                    strKey = "[" + strKey + "]";

                    string strExpressionPath = GetExpressionPath(strKey, isExport);

                    if (!string.IsNullOrEmpty(strExpressionPath))
                    {
                        strContent = strContent.Replace(strKey, strExpressionPath);
                    }
                }
            }

            return strContent;
        }

        private string MakeText(string strMsgForWeb, bool isExport = false)
        {
            try
            {
                Regex UrlRegex = default(Regex);
                MatchCollection matches = default(MatchCollection);

                //处理url形式超链
                //var reg_url = /((https?:\/\/)?[a-zA-Z0-9][a-zA-Z0-9\?;=\+_\-&\/\.#]*\.(com|cn|net|org|info)\/?[a-zA-Z0-9\?;=\+_\-&\/\.#]*)/g;
                //var link_url = text.replace(reg_url, '<a href="weixin://navigate?url=$1">$1</a>');
                try
                {
                    UrlRegex = new Regex(@"((https?:\/\/)?[a-zA-Z0-9][a-zA-Z0-9\?;=\+_\-&\/\.#]*\.(com|cn|net|org|info)\/?[a-zA-Z0-9\?;=\+_\-&\/\.#]*)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
                    matches = UrlRegex.Matches(strMsgForWeb);
                    List<string> lstValue = new List<string>();
                    foreach (Match match in matches)
                    {
                        if (!string.IsNullOrEmpty(match.Value) && !lstValue.Contains(match.Value))
                            lstValue.Add(match.Value);

                    }
                    foreach (string item in lstValue)
                    {
                        strMsgForWeb = strMsgForWeb.Replace(item, string.Format("<a href=\"weixin://navigate?url={0}\">{0}</a>", item));
                    }
                }
                catch (Exception ex)
                {
                }

                //处理文本超链
                //[链接](http://www.weixin.com/) 转化成 <a href="http://www.weixin.com/">链接</a>
                //var reg_text = /\[(.*?)\]\(<a\shref=".*">(.*)<\/a>\)/g;
                //var link_text = link_url.replace(reg_text, '<a href="weixin://navigate?url=$2">$1</a>');

                //try
                //{
                //    UrlRegex = new Regex(@"[(.*?)\]\(<a\shref="".*"">(.*)<\/a>\)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
                //    matches = UrlRegex.Matches(strMsgForWeb);
                //    foreach (Match match in matches)
                //    {
                //        strMsgForWeb = strMsgForWeb.Replace(match.Value, string.Format(@"<a href=""weixin://navigate?url={0}"">{0}</a>", match.Value));
                //    }

                //}
                //catch (Exception ex)
                //{
                //}

                //高亮处理
                //~高亮文字~(background:#000;) 转化成 <span style="background:#000;">高亮文字</span>
                //var reg_bg = /~(.*?)~\((([_*\-a-zA-Z]+:\s*#[0-9a-zA-Z]{3,6};?\s*)+)\)/g;
                //var bg_text = link_text.replace(reg_bg, '<span style="$2">$1</span>');
                try
                {
                    UrlRegex = new Regex(@"~(.*?)~\((([_*\-a-zA-Z]+:\s*#[0-9a-zA-Z]{3,6};?\s*)+)\)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
                    matches = UrlRegex.Matches(strMsgForWeb);
                    foreach (Match match in matches)
                    {
                        strMsgForWeb = strMsgForWeb.Replace(match.Value, string.Format(@"<span style=""{0}"">{0}</span>", match.Value));
                    }

                }
                catch (Exception ex)
                {
                }

                //处理表情图片
                //^(biaoqong.jpg)^(21,21) 转化成 <img src="biaoqing.jpg" width="21px" height="21px"/>
                //var reg_img = /\^(.*?\.[a-zA-Z0-1]+)\^\((\d+),\s*(\d+)\)/g;
                //var img_text = bg_text.replace(reg_img, '<img src="$1" width="$2px" height="$3px"/>');
                try
                {
                    UrlRegex = new Regex(@"\^(.*?\.[a-zA-Z0-1]+)\^\((\d+),\s*(\d+)\)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
                    matches = UrlRegex.Matches(strMsgForWeb);
                    foreach (Match match in matches)
                    {
                        strMsgForWeb = strMsgForWeb.Replace(match.Value, string.Format(@"<img src=""{0}"" width=""21px"" height=""21px""/>", match.Value.Replace("^", "").Replace("(21,21)", "")));
                    }

                }
                catch (Exception ex)
                {
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MakeText");
            }
            return strMsgForWeb;
        }

        public static string GetExpressionPath(string strKey, bool isExport = false)
        {
            string strName = string.Empty;

            switch (strKey)
            {
                case "[微笑]":
                    strName = "Expression_1";
                    break;
                case "[撇嘴]":
                    strName = "Expression_2";
                    break;
                case "[色]":
                    strName = "Expression_3";
                    break;
                case "[发呆]":
                case "[發呆]":
                    strName = "Expression_4";
                    break;
                case "[得意]":
                    strName = "Expression_5";
                    break;
                case "[流泪]":
                case "[流淚]":
                    strName = "Expression_6";
                    break;
                case "[害羞]":
                    strName = "Expression_7";
                    break;
                case "[闭嘴]":
                case "[閉嘴]":
                    strName = "Expression_8";
                    break;
                case "[睡]":
                    strName = "Expression_9";
                    break;
                case "[大哭]":
                    strName = "Expression_10";
                    break;
                case "[尴尬]":
                case "[尷尬]":
                    strName = "Expression_11";
                    break;
                case "[发怒]":
                case "[發怒]":
                    strName = "Expression_12";
                    break;
                case "[调皮]":
                case "[調皮]":
                    strName = "Expression_13";
                    break;
                case "[呲牙]":
                    strName = "Expression_14";
                    break;
                case "[惊讶]":
                case "[驚訝]":
                    strName = "Expression_15";
                    break;
                case "[难过]":
                case "[難過]":
                    strName = "Expression_16";
                    break;
                case "[酷]":
                    strName = "Expression_17";
                    break;
                case "[冷汗]":
                    strName = "Expression_18";
                    break;
                case "[抓狂]":
                    strName = "Expression_19";
                    break;
                case "[吐]":
                    strName = "Expression_20";
                    break;
                case "[偷笑]":
                    strName = "Expression_21";
                    break;
                case "[愉快]":
                    strName = "Expression_22";
                    break;
                case "[白眼]":
                    strName = "Expression_23";
                    break;
                case "[傲慢]":
                    strName = "Expression_24";
                    break;
                case "[饥饿]":
                case "[饑餓]":
                    strName = "Expression_25";
                    break;
                case "[困]":
                    strName = "Expression_26";
                    break;
                case "[惊恐]":
                case "[驚恐]":
                    strName = "Expression_27";
                    break;
                case "[流汗]":
                    strName = "Expression_28";
                    break;
                case "[憨笑]":
                    strName = "Expression_29";
                    break;
                case "[悠闲]":
                case "[悠閒]":
                    strName = "Expression_30";
                    break;
                case "[奋斗]":
                case "[奮鬥]":
                    strName = "Expression_31";
                    break;
                case "[咒骂]":
                case "[咒罵]":
                    strName = "Expression_32";
                    break;
                case "[疑问]":
                    strName = "Expression_33";
                    break;
                case "[嘘]":
                    strName = "Expression_34";
                    break;
                case "[晕]":
                case "[暈]":
                    strName = "Expression_35";
                    break;
                case "[疯了]":
                case "[瘋了]":
                    strName = "Expression_36";
                    break;
                case "[衰]":
                    strName = "Expression_37";
                    break;
                case "[骷髅]":
                case "[骷髏]":
                    strName = "Expression_38";
                    break;
                case "[敲打]":
                    strName = "Expression_39";
                    break;
                case "[再见]":
                    strName = "Expression_40";
                    break;
                case "[擦汗]":
                    strName = "Expression_41";
                    break;
                case "[抠鼻]":
                case "[摳鼻]":
                    strName = "Expression_42";
                    break;
                case "[鼓掌]":
                    strName = "Expression_43";
                    break;
                case "[糗大了]":
                    strName = "Expression_44";
                    break;
                case "[坏笑]":
                case "[壞笑]":
                    strName = "Expression_45";
                    break;
                case "[左哼哼]":
                    strName = "Expression_46";
                    break;
                case "[右哼哼]":
                    strName = "Expression_47";
                    break;
                case "[哈欠]":
                    strName = "Expression_48";
                    break;
                case "[鄙视]":
                case "[鄙視]":
                    strName = "Expression_49";
                    break;
                case "[委屈]":
                    strName = "Expression_50";
                    break;
                case "[快哭了]":
                    strName = "Expression_51";
                    break;
                case "[阴险]":
                case "[陰險]":
                    strName = "Expression_52";
                    break;
                case "[亲亲]":
                case "[親親]":
                    strName = "Expression_53";
                    break;
                case "[吓]":
                case "[嚇]":
                    strName = "Expression_54";
                    break;
                case "[可怜]":
                case "[可憐]":
                    strName = "Expression_55";
                    break;
                case "[菜刀]":
                    strName = "Expression_56";
                    break;
                case "[西瓜]":
                    strName = "Expression_57";
                    break;
                case "[啤酒]":
                    strName = "Expression_58";
                    break;
                case "[篮球]":
                case "[籃球]":
                    strName = "Expression_59";
                    break;
                case "[乒乓]":
                    strName = "Expression_60";
                    break;
                case "[咖啡]":
                    strName = "Expression_61";
                    break;
                case "[饭]":
                case "[飯]":
                    strName = "Expression_62";
                    break;
                case "[猪头]":
                case "[豬頭]":
                    strName = "Expression_63";
                    break;
                case "[玫瑰]":
                    strName = "Expression_64";
                    break;
                case "[凋谢]":
                    strName = "Expression_65";
                    break;
                case "[嘴唇]":
                    strName = "Expression_66";
                    break;
                case "[爱心]":
                case "[愛心]":
                    strName = "Expression_67";
                    break;
                case "[心碎]":
                    strName = "Expression_68";
                    break;
                case "[蛋糕]":
                    strName = "Expression_69";
                    break;
                case "[闪电]":
                case "[閃電]":
                    strName = "Expression_70";
                    break;
                case "[炸弹]":
                case "[炸彈]":
                    strName = "Expression_71";
                    break;
                case "[刀]":
                    strName = "Expression_72";
                    break;
                case "[足球]":
                    strName = "Expression_73";
                    break;
                case "[瓢虫]":
                case "[瓢蟲]":
                    strName = "Expression_74";
                    break;
                case "[便便]":
                    strName = "Expression_75";
                    break;
                case "[月亮]":
                    strName = "Expression_76";
                    break;
                case "[太阳]":
                case "[太陽]":
                    strName = "Expression_77";
                    break;
                case "[礼物]":
                case "[禮物]":
                    strName = "Expression_78";
                    break;
                case "[拥抱]":
                case "[擁抱]":
                    strName = "Expression_79";
                    break;
                case "[强]":
                    strName = "Expression_80";
                    break;
                case "[弱]":
                    strName = "Expression_81";
                    break;
                case "[握手]":
                    strName = "Expression_82";
                    break;
                case "[胜利]":
                case "[勝利]":
                    strName = "Expression_83";
                    break;
                case "[抱拳]":
                    strName = "Expression_84";
                    break;
                case "[勾引]":
                    strName = "Expression_85";
                    break;
                case "[拳头]":
                case "[拳頭]":
                    strName = "Expression_86";
                    break;
                case "[差劲]":
                case "[差勁]":
                    strName = "Expression_87";
                    break;
                case "[爱你]":
                case "[愛你]":
                    strName = "Expression_88";
                    break;
                case "[NO]":
                    strName = "Expression_89";
                    break;
                case "[OK]":
                    strName = "Expression_90";
                    break;
                case "[爱情]":
                case "[愛情]":
                    strName = "Expression_91";
                    break;
                case "[飞吻]":
                case "[飛吻]":
                    strName = "Expression_92";
                    break;
                case "[跳跳]":
                    strName = "Expression_93";
                    break;
                case "[发抖]":
                case "[發抖]":
                    strName = "Expression_94";
                    break;
                case "[怄火]":
                case "[慪火]":
                    strName = "Expression_95";
                    break;
                case "[转圈]":
                case "[轉圈]":
                    strName = "Expression_96";
                    break;
                case "[磕头]":
                case "[磕頭]":
                    strName = "Expression_97";
                    break;
                case "[回头]":
                case "[回頭]":
                    strName = "Expression_98";
                    break;
                case "[跳绳]":
                case "[跳繩]":
                    strName = "Expression_99";
                    break;
                case "[挥手]":
                case "[揮手]":
                    strName = "Expression_100";
                    break;
                case "[激动]":
                case "[激動]":
                    strName = "Expression_101";
                    break;
                case "[街舞]":
                    strName = "Expression_102";
                    break;
                case "[献吻]":
                case "[獻吻]":
                    strName = "Expression_103";
                    break;
                case "[左太极]":
                case "[左太極]":
                    strName = "Expression_104";
                    break;
                case "[右太极]":
                case "[右太極]":
                    strName = "Expression_105";
                    break;
            }

            string result = string.Empty;
            if (!string.IsNullOrEmpty(strName))
            {
                if (isExport)
                {
                    result = string.Format("^../assets/expression/{0}.png^(21,21)", strName);
                }
                else
                {
                    result = string.Format("^./expression/{0}.png^(21,21)", strName);
                }
            }
            //else
            //{
            //    Common.Log(strKey + "---GetExpressionPath");
            //}

            return result;
        }

        private void ExportMediaAttachment(object objArgs)
        {
            ExportMediaAttachment(objArgs, null);
        }

        private void ExportMediaAttachment(object objArgs, string tip = null)
        {
            int filter = IniSetting.GetExportFilter();

            //不导出图片、音频跟视频
            if (filter == 7)
                return;

            object[] objItems = (object[])objArgs;
            WechatTalk wTalk = objItems[0] as WechatTalk;
            //List<WechatMessageItem> listMessageItems = objItems[1] as List<WechatMessageItem>;
            WechatMessageItem[] aryItems = (objItems[1] as WechatMessageItem[]);
            ExportType et = (ExportType)objItems[2];
            string strPath = objItems[3].ToString();
            bool bPreview = (bool)objItems[4];
            ManualResetEvent e = objItems[5] as ManualResetEvent;

            //OnExportComplete callback = msgItems[6] as OnExportComplete;
            //int lastIndex = (int)msgItems[7];
            //int thread_totals = (int)msgItems[8];

            //int nCounts = listMessageItems.Count;
            //WechatMessageItem[] aryItems = listMessageItems.ToArray();


            for (int iIndex = 0; iIndex < aryItems.Length; iIndex++)
            {
                WechatMessageItem item = aryItems[iIndex];
                if (et == ExportType.ExportToPicture && item.MessageType != 3)
                    continue;
                else if (et == ExportType.ExportToVideo && item.MessageType != 43)
                    continue;
                else if (et == ExportType.ExportToVoice && item.MessageType != 34)
                    continue;

                //不导出图片
                if (filter == 1 && item.MessageType == 3)
                    continue;

                //不导出音频
                if (filter == 2 && item.MessageType == 34)
                    continue;

                //不导出视频
                if (filter == 3 && (item.MessageType == 43 || item.MessageType == 62))
                    continue;

                //不导出图片跟音频
                if (filter == 4 && (item.MessageType == 3 || item.MessageType == 34))
                    continue;

                //不导出图片跟视频
                if (filter == 5 && (item.MessageType == 3 || item.MessageType == 43 || item.MessageType == 62))
                    continue;

                //不导出音频跟视频
                if (filter == 6 && (item.MessageType == 34 || item.MessageType == 43 || item.MessageType == 62))
                    continue;

                if (tip == null)
                    ExportProgress(iIndex + 1, aryItems.Length, "媒体附件：");
                else
                    ExportProgress(iIndex + 1, aryItems.Length, tip + "媒体附件：");

                int iMapKey = 0;
                string strTemp = "";

                if (item.MessageType == 43 || item.MessageType == 62 ||
                    item.MessageType == 3 || item.MessageType == 34)
                {
                    strTemp = mWechatHeplerPC.GetMediaFilePath(item,
                                          mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                                          mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, false,
                                          mCurrentWeChatiOSDevice, out iMapKey);

                    if (strTemp.Length == 0)
                    {
                        if (item.MessageType == 3 || item.MessageType == 34)
                        {
                            strTemp = mWechatHeplerPC.GetMediaFilePath(item,
                                                    mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                                                    mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, true,
                                                    mCurrentWeChatiOSDevice, out iMapKey);
                        }
                    } // if (strTemp.Length == 0)

                    string strNickName = "";
                    string strWeChatNO = "";
                    string strWeChatID = "";
                    string strUsrName = "";
                    string strFromForWeb = "";
                    string strPetName = "";
                    GetFriendInformation(wTalk, mCurrentWAInfo.DicWeChatFriendInfo, item,
                        ref strNickName, ref strWeChatNO, ref strWeChatID, ref strUsrName, ref strFromForWeb, ref strPetName);

                    if (strWeChatID.Length > 0 && strNickName.Length == 0)
                    {
                        foreach (WeChatFriendInfo wcfi in mCurrentWAInfo.DicWeChatFriendInfo.Values)
                        {
                            if (wcfi.StrWXID.Equals(strWeChatID, StringComparison.InvariantCultureIgnoreCase))
                            {
                                strNickName = wcfi.StrNickName;
                                strUsrName = strWeChatID;
                                break;
                            }
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(strNickName))
                        strNickName = Utility.ReplaceWinIllegalName(strNickName);

                    string strSuffix = "";
                    if (strTemp.Length > 0)
                    {
                        if (item.MessageType == 3)
                        {
                            strSuffix = ".png";
                        }
                        else if (item.MessageType == 34)
                        {
                            strSuffix = ".mp3";
                            strTemp = WechatHeplerPC.GetChatMp3PathFromPC(strTemp, item.MessageContent);
                        }
                        else
                        {
                            strSuffix = ".mp4";
                        }

                        try
                        {
                            string fName = Path.Combine(strPath, string.Format("{0}-{1}-{2}{3}", item.MessageDT.ToString("yyyyMMddHHmmss"), iMapKey, strNickName, strSuffix));
                            if (File.Exists(strTemp))
                            {
                                Common.Log(string.Format("正在复制临时文件：strTmp={0}，strPath={1},bPreview={2}", strTemp, fName, bPreview), "ExportHelp", true);
                                if (bPreview)
                                {
                                    Image imgSrc = Common.ImageFromFile(strTemp);
                                    Image imgCut = Utility.GetThumbnail(imgSrc, new Size(30, 40), true);
                                    imgCut.Save(fName);
                                    imgCut.Dispose();
                                    imgCut = null;
                                    imgSrc.Dispose();
                                    imgSrc = null;
                                }
                                else
                                {
                                    File.Copy(strTemp, fName);

                                    if (item.MessageType == 3)
                                    {
                                        WxgfDec(fName);
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Common.LogException(ex.ToString(), "ImageSave");
                        }
                    } // if (strTemp.Length > 0)

                    if (et == ExportType.None && item.MessageType == 43)
                    {
                        //如果是视频还要一张缩略图
                        strTemp = mWechatHeplerPC.GetMediaFilePath(item,
                                            mStrCurrentBackupDBPath, mStrCurrentBackupDBPassword,
                                            mStrCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, true,
                                            mCurrentWeChatiOSDevice, out iMapKey);

                        try
                        {
                            if (File.Exists(strTemp))
                                File.Copy(strTemp, Path.Combine(strPath, string.Format("{0}-{1}-{2}{3}", item.MessageDT.ToString("yyyyMMddHHmmss"), iMapKey, strNickName, ".png")));
                        }
                        catch (Exception ex)
                        {
                            Common.LogException(ex.ToString(), "FileCopy_1");
                        }
                    }
                } // if (item.MessageType....)
            } // for...

            if (e != null)
                e.Set();
        }

        public void WxgfDec(string filepath)
        {
            string dllPath = Environment.CurrentDirectory + "\\VoipEngine.dll";
            //1.初始化
            decWxgfFunInit(dllPath, log_callback);

            if (File.Exists(filepath) && isWxgf(filepath) == 1)
            {
                string outPath = Path.Combine(Path.GetDirectoryName(filepath), Path.GetFileName(filepath));
                int ret = wxgfDec(filepath, outPath);
            }
        }
        public void log_callback(string log, int len)
        {

        }

        private void ExportMedia(WechatTalk wTalk, string strPath, bool isPreview, ExportType et = ExportType.None, string tip = null)
        {
            string strTemp = "";
            string strSuffix = "";

            Folder.CheckFolder(strPath);

            WechatMessageItem[] aryMsgItems = wTalk.MessageItems.ToArray();
            int nCounts = aryMsgItems.Length; ;
            //Common.Log(string.Format("Exporting MessageItems Counts={0}", nCounts));

            const int MAXCOUNTS = 1000;
            bool bIsThread = (nCounts >= MAXCOUNTS);
            bIsThread = false;//暂时用就方式
            if (bIsThread)
            {
                int thread_counts = nCounts / MAXCOUNTS;
                if (nCounts % MAXCOUNTS > 0)
                    thread_counts++;

                int nSpareCounts = 0;
                List<ManualResetEvent> listEvents = new List<ManualResetEvent>();
                for (int iIndex = 0; iIndex < thread_counts; iIndex++)
                {
                    ManualResetEvent e = new ManualResetEvent(false);
                    listEvents.Add(e);

                    if (iIndex == (thread_counts - 1))
                        nSpareCounts = nCounts % MAXCOUNTS;
                    else
                        nSpareCounts = MAXCOUNTS;


                    List<WechatMessageItem> listMsgItems = wTalk.MessageItems.GetRange(iIndex * MAXCOUNTS, nSpareCounts);

                    //new Thread(new ParameterizedThreadStart((o) =>
                    //{
                    //    ((ManualResetEvent)o).Set();
                    //    ExportMediaAttachment(new object[] { wTalk, listMsgItems, et, strPath, isPreview, false });
                    //})).Start(e);

                    // 此处使用线程池
                    ThreadPool.QueueUserWorkItem(new WaitCallback(ExportMediaAttachment), new object[] {
                        wTalk, listMsgItems.ToArray(), et, strPath, isPreview, e
                    });
                }
                WaitHandle.WaitAll(listEvents.ToArray());

            }
            else
            {
                ExportMediaAttachment(new object[] { wTalk, aryMsgItems, et, strPath, isPreview, null }, tip);
            }
        }

        private static void ReleaseResource(string FilePath, byte[] Resource)
        {
            try
            {
                lock (threadLock_)
                {
                    if (File.Exists(FilePath))
                    {
                        FileInfo fileInfo = new FileInfo(FilePath);
                        if (fileInfo.Length != Resource.Length)
                        {
                            try
                            {
                                File.Delete(FilePath);
                            }
                            catch { }
                        }
                    }

                    if (!File.Exists(FilePath))
                    {
                        using (FileStream objWriter = new FileStream(FilePath, FileMode.OpenOrCreate, FileAccess.Write))
                        {
                            objWriter.Write(Resource, 0, Resource.Length);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ReleaseResource");
            }
        }

        private string GetChatType(int mt)
        {
            string strText = string.Empty;
            switch (mt)
            {
                case 1:
                    strText = "文本";
                    break;
                case 3:
                    strText = "图片";
                    break;
                case 34:
                    strText = "语音";
                    break;
                case 42:
                    strText = "名片";
                    break;
                case 43:
                    strText = "视频";
                    break;
                case 47:
                    strText = "动画表情";
                    break;
                case 48:
                    strText = "地理位置";
                    break;
                case 49:
                    strText = "网页";
                    break;
                case 50:
                    strText = "视频聊天";
                    break;
                case 62:
                    strText = "小视频";
                    break;
                case 10000:
                    strText = "系统消息";
                    break;
            }
            return strText;
        }

        private string GetChatMsg(WechatMessageItem wmItem, ChatMsgType type, bool isPreview, ref string strSendwxid)
        {
            string strText = string.Empty;
            switch (wmItem.MessageType)
            {
                case 1:
                    strText = GetChatMsgForText(wmItem.MessageContent, wmItem.SendId, type, ref strSendwxid);
                    break;
                case 3:
                    strText = "[图片]";
                    break;
                case 34:
                    strText = "[语音]";
                    break;
                case 42:
                    strText = "[名片]" + GetChatMsgForText(wmItem.MessageContent, wmItem.SendId, type, ref strSendwxid);
                    break;
                case 43:
                    strText = "[视频]";
                    try
                    {
                        string strKey = "fromusername=";
                        int iStart = wmItem.MessageContent.IndexOf(strKey);
                        iStart = iStart + strKey.Length + 1;
                        if (iStart > 0 && iStart < wmItem.MessageContent.Length - 1)
                        {
                            int iEnd = wmItem.MessageContent.IndexOf(@"""", iStart);
                            if (iEnd > iStart)
                            {
                                strSendwxid = wmItem.MessageContent.Substring(iStart, iEnd - iStart);
                            }
                        }
                    }
                    catch { }
                    break;
                case 47:
                    strText = "[动画表情]";
                    break;
                case 48:
                    strText = "[地理位置]" + GetChatMsgForText(wmItem.MessageContent, wmItem.SendId, type, ref strSendwxid);
                    break;
                case 49:
                    {
                        string strMsgForWeb = wmItem.MessageContent;
                        JsonArray arrHtml5ContentForWeb = null;
                        string strTypeForWeb = "";
                        IMHtmlNodeType imtype = IMHtmlNodeType.IMHtmlNode_Media;
                        bool isLinkA = false;
                        mWeChatMsgWebPage.AnalysisNewWebpage(wmItem, ref strMsgForWeb, ref arrHtml5ContentForWeb,
                            ref strTypeForWeb, ref strText, ref imtype, ref isLinkA);
                    }
                    break;
                case 50:
                    strText = "[视频聊天]";
                    break;
                case 62:
                    strText = "[视频聊天]";
                    break;
                case 10000:
                    strText = wmItem.MessageContent;
                    break;
                default:
                    strText = wmItem.MessageContent;
                    break;
            }

            if (isPreview)
            {
                int mtype = wmItem.MessageType;

                if (mtype != 3 && mtype != 34 && mtype != 43 && mtype != 42 && mtype != 62 && mtype != 49 && mtype != 10000)
                {
                    if (strText.Length <= 1)
                        strText = mStrTrialMark;
                    else
                        strText = strText.Substring(0, strText.Length / 2) + mStrTrialMark;
                }
            }

            return strText;
        }

        private string GetChatMsgForText(string strMessage, string strSendid, ChatMsgType type, ref string strSendwxid)
        {
            string strText = strMessage;
            switch (type)
            {
                case ChatMsgType.ShowOnList:
                    if (WechatHeplerPC.CheckIsChatRoom(strSendid))
                    {
                        string[] arr = strText.Split(new char[] { '\n', '\r' });
                        if (arr.Length > 1)
                        {
                            string strNickName = arr[0].TrimEnd(':');
                            strSendwxid = arr[0].TrimEnd(':');
                            string strMsg = arr[1];
                            strText = string.Format("{0}:{1}", strNickName, strMsg);
                        }

                    }
                    else
                    {
                        string[] arr = strText.Split(new char[] { '\n', '\r' });
                        strText = arr[0];
                    }

                    break;

                case ChatMsgType.ShowOnTxtFile:
                case ChatMsgType.ShowOnExcelFile:
                    if (WechatHeplerPC.CheckIsChatRoom(strSendid))
                    {
                        string[] arr = strText.Split(new char[] { '\n', '\r' });
                        if (arr.Length > 1)
                        {
                            strSendwxid = arr[0].TrimEnd(':');
                            string strMsgTemp = string.Empty;
                            //第一个值为用户名，不显示
                            for (int index = 1; index <= arr.Length - 1; index++)
                            {
                                strMsgTemp = strMsgTemp + " " + arr[index];
                            }
                            strText = strMsgTemp.TrimStart(' ');
                        }
                        if (strText.Length == 0)
                        {
                            strText = strMessage;
                        }

                    }
                    else
                    {
                        string[] arr = strText.Split(new char[] { '\n', '\r' });
                        if (arr.Length > 1)
                        {
                            strSendwxid = arr[0].TrimEnd(':');
                            string strMsgTemp = string.Empty;
                            for (int index = 0; index <= arr.Length - 1; index++)
                            {
                                strMsgTemp = strMsgTemp + " " + arr[index];
                            }
                            strText = strMsgTemp.TrimStart(' ');
                        }
                    }

                    break;

                case ChatMsgType.ShowOnWeb:
                    if (WechatHeplerPC.CheckIsChatRoom(strSendid))
                    {
                        string[] arr = strText.Split(new char[] { '\n', '\r' });
                        if (arr.Length > 1)
                        {
                            strSendwxid = arr[0].TrimEnd(':');
                            string strMsgTemp = string.Empty;
                            //第一个值为用户名，不显示
                            for (int index = 1; index <= arr.Length - 1; index++)
                            {
                                strMsgTemp = strMsgTemp + "\r\n" + arr[index];
                            }
                            strText = strMsgTemp.TrimStart(new char[] { '\n', '\r' });
                        }

                    }
                    else
                    {
                        string[] arr = strText.Split(new char[] { '\n', '\r' });
                        if (arr.Length > 1)
                        {
                            strSendwxid = arr[0].TrimEnd(':');
                            string strMsgTemp = string.Empty;
                            for (int index = 0; index <= arr.Length - 1; index++)
                            {
                                strMsgTemp = strMsgTemp + "\r\n" + arr[index];
                            }
                            strText = strMsgTemp.TrimStart(new char[] { '\n', '\r' });
                        }
                    }
                    break;
            }
            return strText;
        }

        private string GetChatMsgForWebPage(string strMsg, ChatMsgType type, ref string strSendwxid)
        {
            string strText = string.Empty;

            if (type == ChatMsgType.ShowOnList)
            {
                strText = "[网页]";
            }
            else
            {
                string strMsgForWeb = strMsg;
                JsonArray arrHtml5ContentForWeb = GetWebPageJsonArray(strMsgForWeb);
                //网页json

                //兼容链接（只有title和url）
                if (arrHtml5ContentForWeb == null || arrHtml5ContentForWeb.Count == 0)
                {
                    arrHtml5ContentForWeb = GetWebLinkJsonArray(strMsgForWeb);
                }

                if (arrHtml5ContentForWeb != null && arrHtml5ContentForWeb.Count > 0)
                {
                    foreach (JsonObject Item in arrHtml5ContentForWeb)
                    {
                        try
                        {
                            string strTitle = ((JsonString)Item["title"]).Value;
                            string strUrl = ((JsonString)Item["url"]).Value;
                            strSendwxid = ((JsonString)Item["fromusername"]).Value;
                            string strSeparator = "\r\n";
                            if (type == ChatMsgType.ShowOnTxtFile)
                            {
                                strSeparator = strSeparator + GetSpaces(90);
                            }

                            if (string.IsNullOrEmpty(strText))
                            {
                                strText = strTitle + strSeparator + strUrl;
                            }
                            else
                            {
                                strText = strText + strSeparator + strTitle + strSeparator + strUrl;
                            }
                        }
                        catch
                        {
                        }
                    }
                }
            }

            if (string.IsNullOrEmpty(strText))
            {
                strText = strMsg;
            }

            return strText;
        }

        public static JsonArray GetWebLinkJsonArray(string strContent)
        {
            //string regular = "<title>(?<title>[\\w\\W]*?)</title>[\\w\\W]*?<url>(?<url>[\\w\\W]*?)</url>";
            string regular = "<title>(?<title>[\\w\\W]*?)</title>[\\w\\W]*?<url>(?<url>[\\w\\W]*?)</url>[\\w\\W]*?<fromusername>(?<fromusername>[\\w\\W]*?)</fromusername>";
            return GetWebPageJsonArray(strContent, regular);
        }

        public static JsonArray GetWebPageJsonArray(string strContent)
        {
            //.*?代表任意字符（不包括换行及特殊字符）
            //[\w\W]*?代表任意字符（包括换行及特殊字符）
            //Dim regular As String = "<title>(?<title>.*?)</title>[\w\W]*?<url>(?<url>.*?)</url>[\w\W]*?<pub_time>(?<pub_time>.*?)</pub_time>[\w\W]*?<cover>(?<cover>.*?)</cover>[\w\W]*?<digest>(?<digest>.*?)</digest>"
            string regular = "<title>(?<title>[\\w\\W]*?)</title>[\\w\\W]*?<url>(?<url>[\\w\\W]*?)</url>[\\w\\W]*?<pub_time>(?<pub_time>[\\w\\W]*?)</pub_time>[\\w\\W]*?<cover>(?<cover>[\\w\\W]*?)</cover>[\\w\\W]*?<digest>(?<digest>[\\w\\W]*?)</digest>";

            //如果服务器有设置值则使用服务上的值
            if (!string.IsNullOrEmpty(mRegular))
            {
                regular = mRegular;
            }

            return GetWebPageJsonArray(strContent, regular);
        }

        public static JsonArray GetWebPageJsonArray(string strContent, string regular)
        {
            JsonArray arrWebPages = new JsonArray();
            JsonObject dicWebPage = default(JsonObject);

            DataTable dt = Utility.GetMatchStringByRegularExpressions(strContent, new string[1] { regular },
                new string[7] { "title", "url", "pub_time", "cover", "digest", "type", "fromusername" });

            if (dt != null && dt.Rows.Count > 0)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    try
                    {
                        dicWebPage = new JsonObject();
                        //标题
                        string strTitle = FormatStringForWebPage(HttpUtility.UrlDecode(dr["title"].ToString()));
                        dicWebPage.Add("title", strTitle);

                        //网页url
                        string strUrl = FormatStringForWebPage(HttpUtility.UrlDecode(dr["url"].ToString()));
                        dicWebPage.Add("url", strUrl);

                        string strfromusername = FormatStringForWebPage(HttpUtility.UrlDecode(dr["fromusername"].ToString()));
                        dicWebPage.Add("fromusername", strfromusername);

                        //发布时间
                        string strPubTime = FormatStringForWebPage(HttpUtility.UrlDecode(dr["pub_time"].ToString()));
                        DateTime PubTime = DateTime.MinValue;

                        try
                        {
                            if (!string.IsNullOrEmpty(strPubTime))
                            {
                                long longPubTime = long.Parse(strPubTime);
                                //PubTime = WeixinHelper.ConvertWeixinToPcTime(longPubTime);
                            }
                        }
                        catch { }

                        if (PubTime == DateTime.MinValue)
                        {
                            strPubTime = string.Empty;
                        }
                        else
                        {
                            strPubTime = PubTime.ToString("MM-dd");
                        }

                        dicWebPage.Add("pub_time", strPubTime);

                        //封面地址
                        string strCover = FormatStringForWebPage(HttpUtility.UrlDecode(dr["cover"].ToString()));
                        dicWebPage.Add("cover", strCover);

                        //摘要
                        string strDigest = FormatStringForWebPage(HttpUtility.UrlDecode(dr["digest"].ToString()));
                        dicWebPage.Add("digest", strDigest);

                        arrWebPages.Add(dicWebPage);
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "GetWebPageJsonArray");
                    }
                }
            }
            else
            {
                //Common.LogException(strContent, "无法兼容的网页")
            }

            return arrWebPages;
        }

        private static string FormatStringForWebPage(string strContent)
        {
            string value = strContent;

            if (!string.IsNullOrEmpty(value))
            {

                if (value.StartsWith("<![CDATA[") && value.EndsWith("]]>"))
                {
                    value = value.TrimStart(new char[] {
                '<',
                '!',
                '[',
                'C',
                'D',
                'A',
                'T',
                'A',
                '['
            });
                    value = value.TrimEnd(new char[] {
                ']',
                ']',
                '>'
            });

                }
            }

            return value;
        }

        private string GetSpaces(int count)
        {
            string strSpaces = "";

            for (int index = 1; index <= count; index++)
            {
                strSpaces = strSpaces + " ";
            }

            return strSpaces;
        }

        private ContentLayout GetContentLayout(string strName, string strValue = "")
        {
            ContentLayout info = new ContentLayout();

            if (this.mLstContentLayout.Count == 0)
            {
                this.CreateContentLayout();
            }

            foreach (ContentLayout item in this.mLstContentLayout)
            {
                if (item.CLName == strName)
                {
                    info.CLIsCut = item.CLIsCut;
                    info.CLIsDoc = item.CLIsDoc;
                    info.CLIsPad = item.CLIsPad;
                    info.CLLength = item.CLLength;
                    info.CLName = item.CLName;
                    info.CLPadRight = item.CLPadRight;
                    break; // TODO: might not be correct. Was : Exit For
                }
            }

            info.CLValue = strValue;

            return info;
        }

        private void CreateContentLayout()
        {
            this.mLstContentLayout = new List<ContentLayout>();

            ContentLayout info = new ContentLayout();
            info.CLName = "Len20";
            info.CLLength = 20;
            info.CLIsDoc = false;
            info.CLIsPad = true;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);

            info = new ContentLayout();
            info.CLName = "Len25";
            info.CLLength = 25;
            info.CLIsDoc = false;
            info.CLIsPad = true;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);

            info = new ContentLayout();
            info.CLName = "Len30";
            info.CLLength = 30;
            info.CLIsDoc = false;
            info.CLIsPad = true;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);

            info = new ContentLayout();
            info.CLName = "Len35";
            info.CLLength = 35;
            info.CLIsDoc = false;
            info.CLIsPad = true;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);

            info = new ContentLayout();
            info.CLName = "Len40";
            info.CLLength = 40;
            info.CLIsDoc = false;
            info.CLIsPad = true;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);

            info = new ContentLayout();
            info.CLName = "Len45";
            info.CLLength = 45;
            info.CLIsDoc = false;
            info.CLIsPad = true;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);

            info = new ContentLayout();
            info.CLName = "Len50";
            info.CLLength = 50;
            info.CLIsDoc = false;
            info.CLIsPad = true;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);

            info = new ContentLayout();
            info.CLName = "Len55";
            info.CLLength = 55;
            info.CLIsDoc = false;
            info.CLIsPad = true;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);

            info = new ContentLayout();
            info.CLName = "Len2000";
            info.CLLength = 2000;
            info.CLIsDoc = false;
            info.CLIsPad = false;
            info.CLPadRight = true;
            info.CLIsCut = false;
            this.mLstContentLayout.Add(info);
        }

        private static string GetSelFolderPath(string desc)
        {
            string selPath = "";
            using (FolderBrowserDialog fbdWeixin = new FolderBrowserDialog())
            {
                fbdWeixin.Description = desc;
                fbdWeixin.ShowNewFolderButton = true;
                string defFolder = IniSetting.GetWCAExportDefaultFolder();
                if (!string.IsNullOrEmpty(defFolder))
                    fbdWeixin.SelectedPath = defFolder;

                if (fbdWeixin.ShowDialog() == DialogResult.OK)
                    selPath = fbdWeixin.SelectedPath;
            }

            return selPath;
        }
        public void ExportFriendInfo(WechatAccInfo waInfo, string strDeviceName)
        {
            try
            {
                string selPath = GetSelFolderPath("导出");
                if (string.IsNullOrWhiteSpace(selPath)) return;
                //using (FolderBrowserDialog fbdWeixin = new FolderBrowserDialog())
                //{
                //    fbdWeixin.Description = "导出";
                //    fbdWeixin.ShowNewFolderButton = true;
                //    string defFolder = IniSetting.GetWCAExportDefaultFolder();
                //    if (!string.IsNullOrEmpty(defFolder))
                //        fbdWeixin.SelectedPath = defFolder;

                //    if (fbdWeixin.ShowDialog() != DialogResult.OK)
                //        return;

                //    selPath = fbdWeixin.SelectedPath;
                //}                

                string strAccount = Utility.ReplaceWinIllegalName(waInfo.InnerAccount == "" ? waInfo.Account : waInfo.InnerAccount);
                string strPath = Path.Combine(selPath, "wx好友信息-" + strAccount);

                strPath = Path.Combine(strPath, System.DateTime.Now.ToString("yyyyMMddHHmm") + "-" + Utility.ReplaceWinIllegalNameToHtml(strDeviceName));
                Folder.CheckFolder(strPath);

                strPath = Path.Combine(strPath, Utility.ReplaceWinIllegalName(waInfo.Name) + "-" + strAccount + ".xls");

                if (this.mThreadExportFriend != null && this.mThreadExportFriend.ThreadState != ThreadState.Stopped)
                    this.mThreadExportFriend.Abort();

                object[] objPara = new object[2];
                objPara[0] = waInfo;
                objPara[1] = strPath;

                this.mThreadExportFriend = new Thread(new ParameterizedThreadStart(DoExportFriend));
                this.mThreadExportFriend.IsBackground = true;
                this.mThreadExportFriend.SetApartmentState(ApartmentState.STA);
                this.mThreadExportFriend.Start(objPara);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ExportFriendInfo");
            }
        }

        private void OpenExportFolder(string pathStr)
        {
            string strFolder = pathStr;
            try
            {
                FileInfo info = new FileInfo(pathStr);
                if (info.Exists)
                    strFolder = info.DirectoryName;
            }
            catch
            {
                strFolder = pathStr;
            }

            Common.OpenExplorer(strFolder);
        }
        private void DoExportFriend(object obj)
        {
            ExportProgressEventArgs epeArgs = new ExportProgressEventArgs(ExportStatus.Exporting, "正在导出好友信息......");
            try
            {
                object[] objPara = (object[])obj;
                WechatAccInfo waInfo = (WechatAccInfo)objPara[0];
                Dictionary<string, WeChatFriendInfo> dictFInfo = waInfo.DicWeChatFriendInfo;
                List<WeChatGroupFriendInfo> lstWCGFInfo = waInfo.LstWeChatGroupFriendInfo;
                string strPath = objPara[1].ToString();

                string strTempFilePath = strPath + ".Temp";
                try
                {
                    if (File.Exists(strTempFilePath))
                        File.Delete(strTempFilePath);
                }
                catch { }

                OnExportProgressEventHandler(epeArgs);

                this.CreateExcelFriendHead(strTempFilePath);

                //写入内容
                string strLineTd = "<td class=xl24  style='border-bottom:.5pt solid black;border-top:none;' x:str>{0}</td>";
                string strLineTr = "<tr style='mso-height-source:userset;height:25.00pt'>{0}</tr>";
                StringBuilder strLine = new StringBuilder();
                StringBuilder strExcelContacts = new StringBuilder();
                decimal decIndex = 0.0M;
                decimal decCounts = (decimal)dictFInfo.Count;
                foreach (WeChatFriendInfo item in dictFInfo.Values)
                {
                    //ExportProgress(intIndex, dictFInfo.Count);

                    decimal dblProgress = decimal.Round((decIndex / decCounts) * 100.0M, 2);
                    epeArgs.Msg = string.Format("正在导出好友信息{0:0.00}%", dblProgress);
                    OnExportProgressEventHandler(epeArgs);

                    CreateRow(strLineTd, strLineTr, strLine, strExcelContacts, item);

                    if (item.StrWXID.EndsWith("@chatroom"))
                    {
                        foreach (WeChatGroupFriendInfo gfInfo in lstWCGFInfo)
                        {
                            if (gfInfo.StrChatRoomName != item.StrWXID)
                                continue;
                            if (gfInfo.LstRoomData == null || gfInfo.LstRoomData.Count == 0)
                                continue;

                            foreach (WeChatFriendInfo gfInfos in gfInfo.LstRoomData)
                            {
                                string strGroupName = item.StrWXID;
                                if (item.StrNickName.Length > 0)
                                    strGroupName = item.StrNickName;
                                else if (item.StrRemark.Length > 0)
                                    strGroupName = item.StrRemark;
                                CreateRow(strLineTd, strLineTr, strLine, strExcelContacts, gfInfos, strGroupName, "是");
                            }
                        }
                    }

                    if (strExcelContacts.Length > 1000)
                    {
                        this.WriteText(strTempFilePath, strExcelContacts.ToString());
                        strExcelContacts.Remove(0, strExcelContacts.Length);
                    }
                    decIndex++;
                }

                if (strExcelContacts.Length > 0)
                    this.WriteText(strTempFilePath, strExcelContacts.ToString());

                this.CreateExcelFriendFoot(strTempFilePath);

                this.SaveCreateExce(strTempFilePath, strPath);
                epeArgs = new ExportProgressEventArgs(ExportStatus.ExportSuccess, "");

                OpenExportFolder(strPath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoExportFriend");
                epeArgs = new ExportProgressEventArgs(ExportStatus.ExportFailure, "");
            }
            OnExportProgressEventHandler(epeArgs);
        }

        private static void CreateRow(string strLineTd, string strLineTr, StringBuilder strLine, StringBuilder strExcelContacts, WeChatFriendInfo item, string strGroupName = "", string strIsGroup = "否")
        {
            strLine.Remove(0, strLine.Length);
            strLine.Append(string.Format(strLineTd, item.StrWXID));
            strLine.Append(string.Format(strLineTd, item.StrInnerAccount));
            strLine.Append(string.Format(strLineTd, item.StrNickName));
            strLine.Append(string.Format(strLineTd, item.StrRemark));
            strLine.Append(string.Format(strLineTd, item.StrHeadImgUrl));
            strLine.Append(string.Format(strLineTd, strIsGroup));
            strLine.Append(string.Format(strLineTd, strGroupName));
            strExcelContacts.AppendLine(string.Format(strLineTr, strLine.ToString()));
        }

        private void CreateExcelFriendHead(string strTempFilePath)
        {
            StringBuilder strExcelContacts = new StringBuilder();
            strExcelContacts.AppendLine("<html xmlns:v=\"urn:schemas-microsoft-com:vml\"");
            strExcelContacts.AppendLine("xmlns:o=\"urn:schemas-microsoft-com:office:office\"");
            strExcelContacts.AppendLine("xmlns:x=\"urn:schemas-microsoft-com:office:excel\"");
            strExcelContacts.AppendLine("xmlns=\"http://www.w3.org/TR/REC-html40\">");

            string strExcelHeader = iWechatAssistant.Properties.Resources.excel_header.Replace("<meta http-equiv=Content-Type content=\"text/html; charset=utf8\">", string.Format("<meta http-equiv=Content-Type content=\"text/html; charset={0}\">", "gb2312"));

            strExcelContacts.AppendLine(strExcelHeader);
            strExcelContacts.AppendLine("<body link=blue vlink=purple><table x:str border=0 cellpadding=0 cellspacing=0 width=1296 style='border-collapse:collapse;table-layout:fixed;width:972pt'>");

            string strTitleTr = "<tr style='mso-height-source:userset;height:30.00pt'>{0}</tr>";
            string strTitleTd = "<td rowspan=2 class=xl300  style='border-bottom:.5pt solid black;border-top:none;' >{0}</td>";
            string strLineTr = "<tr style='mso-height-source:userset;height:25.00pt'>{0}</tr>";

            StringBuilder strTitle = new StringBuilder();
            strTitle.Append(string.Format(strTitleTd, "WXID"));
            strTitle.Append(string.Format(strTitleTd, "微信号"));
            strTitle.Append(string.Format(strTitleTd, "昵称"));
            strTitle.Append(string.Format(strTitleTd, "备注"));
            strTitle.Append(string.Format(strTitleTd, "头像地址"));
            strTitle.Append(string.Format(strTitleTd, "群成员"));
            strTitle.Append(string.Format(strTitleTd, "所属群"));
            strExcelContacts.AppendLine(string.Format(strTitleTr, strTitle));
            strExcelContacts.AppendLine(string.Format(strLineTr, ""));

            this.WriteText(strTempFilePath, strExcelContacts.ToString());
        }

        private void CreateExcelFriendFoot(string strTempFilePath)
        {
            this.WriteText(strTempFilePath, "</table></body></html>");
        }

        public static string GetWebTagFromReg(string content, string re, string item)
        {
            string strResult = "";
            try
            {
                string pattern = re;
                DataTable dtAppIconUrl = MatchHelper.GetMatchStrings(content, new string[1] { pattern }, new string[] { item });

                if (dtAppIconUrl != null && dtAppIconUrl.Rows.Count >= 1)
                {
                    strResult = ReplaceMySqlStrEx(Common.GetValue<string>(dtAppIconUrl.Rows[0][item], string.Empty));
                }
            }
            catch { }
            return strResult;
        }

        public static string ReplaceMySqlStrEx(string sql)
        {
            if (string.IsNullOrEmpty(sql))
                return string.Empty;
            else
                return sql.Replace(@"\", "").Replace("'", @"\'");
        }

        public static void ExportPreview()
        {
            try
            {
                //FolderBrowserDialog fbdWeixin = new FolderBrowserDialog();
                //if (!string.IsNullOrEmpty(IniSetting.GetWCAExportDefaultFolder()))
                //    fbdWeixin.SelectedPath = IniSetting.GetWCAExportDefaultFolder();
                //fbdWeixin.Description = "导出预览";
                //fbdWeixin.ShowNewFolderButton = true;
                //if (fbdWeixin.ShowDialog() != DialogResult.OK)
                //    return;
                string selPath = GetSelFolderPath("导出预览");
                if (string.IsNullOrWhiteSpace(selPath)) return;

                string strFileZipPath = Path.Combine(Folder.TempFolder, "导出预览.zip");
                ReleaseResource(strFileZipPath, iWechatAssistant.Properties.Resources.ExportPreview);
                string strPath = Path.Combine(selPath/*fbdWeixin.SelectedPath*/, "导出预览");
                Folder.CheckFolder(strPath);
                if (Utility.unzip(strFileZipPath, strPath, false) == 0)
                    Common.LogException("导出预览解压失败", "导出预览解压失败");

                Common.OpenExplorer(strPath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ExportPreview");
            }
        }
    }

    public class ExportProgressEventArgs : EventArgs
    {

        ExportStatus mExportStatus = 0;

        string mMsg = "";
        public ExportStatus PExportStatus
        {
            get { return this.mExportStatus; }
            set { this.mExportStatus = value; }
        }

        public string Msg
        {
            get { return this.mMsg; }
            set { this.mMsg = value; }
        }

        public ExportProgressEventArgs(ExportStatus statu, string strMsg)
        {
            this.mExportStatus = statu;
            this.mMsg = strMsg;
        }

    }

    public enum ExportStatus
    {
        Copying,
        //正在复制
        CopySuccess,
        //复制成功
        CopyFailure,
        //复制失败
        Exporting,
        //正在导出
        ExportSuccess,
        //导出成功
        ExportFailure
        //导出失败
    }

    public enum ExportType
    {
        None = 0,
        Txt = 1,
        Excel = 2,
        CSV = 3,
        ExportToTxt = 4,
        ExportToHtml = 5,
        ExportToExcel = 6,
        ExportToPicture = 7,
        ExportToVideo = 8,
        ExportToVoice = 10,
        ExportToAll = 11
    }

    public class ExportHtmlMainInfo
    {
        int _id = 0;
        string _strUrl = "";
        string _userName = "";
        string _iconImage = "";
        string _lastMessage = "";

        DateTime _lastMessageTime;
        public int id
        {
            get { return this._id; }
            set { this._id = value; }
        }

        public string StrUrl
        {
            get { return this._strUrl; }
            set { this._strUrl = value; }
        }

        public string UserName
        {
            get { return this._userName; }
            set { this._userName = value; }
        }

        public string IconImage
        {
            get { return this._iconImage; }
            set { this._iconImage = value; }
        }

        public string LastMessage
        {
            get { return this._lastMessage; }
            set { this._lastMessage = value; }
        }

        public DateTime LastMessageTime
        {
            get { return this._lastMessageTime; }
            set { this._lastMessageTime = value; }
        }

    }

    public class CreateIDComparer : IComparer<ExportHtmlMainInfo>
    {
        private SortType type = SortType.ASC;
        public CreateIDComparer(SortType tempType)
        {
            type = tempType;
        }

        public int Compare(ExportHtmlMainInfo x, ExportHtmlMainInfo y)
        {
            int result = 0;

            if (type == SortType.ASC)
            {
                result = x.id.CompareTo(y.id);
            }
            else
            {
                result = y.id.CompareTo(x.id);
            }

            return result;
        }
    }

    public enum SortType
    {
        ASC = 0,
        DESC = 1
    }

    public class ContentLayout
    {
        string strName = "";
        int length = 0;
        bool isDoc = false;
        bool isPad = false;
        bool padRight = false;

        bool isCut = false;

        string strValue = "";
        int iColumnIndex = 1;

        int iColumnWidth = 0;

        public string CLName
        {
            get { return this.strName; }
            set { this.strName = value; }
        }

        public int CLLength
        {
            get { return this.length; }
            set { this.length = value; }
        }

        public bool CLIsDoc
        {
            get { return this.isDoc; }
            set { this.isDoc = value; }
        }

        public bool CLIsPad
        {
            get { return this.isPad; }
            set { this.isPad = value; }
        }

        public bool CLPadRight
        {
            get { return this.padRight; }
            set { this.padRight = value; }
        }

        public bool CLIsCut
        {
            get { return this.isCut; }
            set { this.isCut = value; }
        }

        public string CLValue
        {
            get { return this.strValue; }
            set { this.strValue = value; }
        }

        public int CLColumnIndex
        {
            get { return this.iColumnIndex; }
            set { this.iColumnIndex = value; }
        }

        public int CLColumnWidth
        {
            get { return this.iColumnWidth; }
            set { this.iColumnWidth = value; }
        }

    }

    public enum ChatMsgType
    {
        ShowOnList,        //展示在联系人列表上      
        ShowOnWeb,        //展示在web页面上
        ShowOnTxtFile,
        ShowOnExcelFile
    }

    public enum ExportRange
    {
        All,
        Head,
        Middle,
        Tail
    }


}
