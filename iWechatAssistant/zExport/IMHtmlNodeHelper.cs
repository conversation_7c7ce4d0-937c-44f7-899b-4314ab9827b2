﻿using iTong.CoreFoundation;
using iTong.CoreModule;
using Microsoft.VisualBasic;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Text;

namespace iWechatAssistant
{
    public class IMHtmlNodeHelper
    {
        public static object CreateDetail(string strTitle, string strFilePath, List<IMHtmlNode> lstIMHtmlNode, bool isPreview)
        {
            bool isRelust = true;
            try
            {
                //创建头部
                CreateDetailHeader(strTitle, strFilePath);

                string strPreview = "";
                if (isPreview)
                    strPreview = "preview";

                //创建内容
                CreateDetailContent(strFilePath, lstIMHtmlNode, strPreview);

                //创建尾部
                CreateDetailEnd(strFilePath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateDetail");
            }
            return isRelust;
        }

        private static bool CreateDetailHeader(string strTitle, string strFilePath)
        {
            bool isRelust = true;
            try
            {
                StringBuilder sbContent = new StringBuilder();
                sbContent.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
                sbContent.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
                sbContent.AppendLine("<head>");
                sbContent.AppendLine("<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">");
                sbContent.AppendLine("<meta http-equiv=\"Content-Language\" content=\"zh-cn\">");
                sbContent.AppendLine("<title>同步微信</title>");
                sbContent.AppendLine("<link rel=\"stylesheet\" type=\"text/css\" href=\"../../assets/css/export_detail.css?2014070501\">");
                sbContent.AppendLine("</head>");
                sbContent.AppendLine("<body>");
                sbContent.AppendLine("<div class=\"goback\">");
                sbContent.AppendLine("<a class=\"comeback\" href=\"javascript:history.back(-1)\">");
                sbContent.AppendLine("<span>");
                sbContent.AppendLine("<img src=\"../../assets/images/goback.png\">");
                sbContent.AppendLine("</span>");
                sbContent.AppendLine("返回");
                sbContent.AppendLine("</a>");
                sbContent.AppendLine("</div>");

                WriteText(strFilePath, sbContent.ToString());
            }
            catch (Exception ex)
            {
                isRelust = false;
                Common.LogException(ex.ToString(), "CreateHtmlHeader");
            }
            return isRelust;
        }

        private static bool CreateDetailContent(string strFilePath, List<IMHtmlNode> lstIMHtmlNode, string strPreview)
        {
            bool isRelust = true;
            try
            {
                string strContent = "";
                string strTime = "";
                bool isShowTime = false;

                foreach (IMHtmlNode info in lstIMHtmlNode)
                {
                    if (strTime.Length == 0 || strTime != info.SmsTime)
                    {
                        isShowTime = true;
                        strTime = info.SmsTime;
                    }
                    else
                    {
                        isShowTime = false;
                    }

                    if (info.SmsContent.Length == 0)
                        continue;

                    switch (info.NType)
                    {
                        case IMHtmlNodeType.IMHtmlNode_Text:
                            strContent = CreateDetailContentText(strFilePath, info, isShowTime);
                            break;
                    }

                    WriteText(strFilePath, strContent);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContent");
            }
            return isRelust;
        }

        private static string CreateDetailContentText(string strFilePath, IMHtmlNode info, bool isShowTime)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();

                strContent.AppendLine("<div id=\"chat_chatmsglist\" class=\"chatContent\">");
                strContent.AppendLine("<div class=\"chatItem\">");
                strContent.AppendLine(string.Format("<img class=\"avatar\" src=\"{0}\">", info.HeaderIconUrl));
                strContent.AppendLine("<div class=\"wrapper\">");
                strContent.AppendLine("<div class=\"info\">");
                //if (info.IsChatRoom && !info.IsItemMe)
                //{
                strContent.AppendLine(string.Format("<span class=\"nickname\">{0}</span>", info.NickName));
                //}
                strContent.AppendLine("<div class=\"cloudContent\">");
                strContent.AppendLine(string.Format("<pre class=\"des\" style=\"white-space:pre-wrap;*white-space: pre;*word-wrap:break-word;overflow:hidden\">{0}</pre>", info.SmsContent));
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine(string.Format("<span class=\"time\">{0}</span>", info.SmsTime));
                strContent.AppendLine("<div class=\"line\"></div>");
                strContent.AppendLine("<div class=\"clearfloat\"></div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");

                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentText");
            }
            return strRelust;
        }

        private static bool CreateDetailEnd(string strFilePath)
        {
            bool isRelust = true;
            try
            {
                StringBuilder sbContent = new StringBuilder();

                sbContent.AppendLine("<script src=\"../assets/js/jquery.1.8.3.min.js\"></script>");
                sbContent.AppendLine("<script type=\"text/javascript\">");
                sbContent.AppendLine("$(function(){");
                sbContent.AppendLine("$(\".cloudContent .des\").each(function() {");
                sbContent.AppendLine("var maxwidth = 85;");
                sbContent.AppendLine("var text = $(this).text();");
                sbContent.AppendLine("var changeText = text.replace(/\\n/g,'<br>');");
                sbContent.AppendLine("$(this).html(changeText);");
                sbContent.AppendLine("if ($(this).text().length > maxwidth) {");
                sbContent.AppendLine("$(this).html($(this).html().substring(0, maxwidth));");
                sbContent.AppendLine("$(this).html($(this).html() + '...');");
                sbContent.AppendLine("}");
                sbContent.AppendLine("});");
                sbContent.AppendLine("})");
                sbContent.AppendLine("</script>");
                sbContent.AppendLine("</body>");
                sbContent.AppendLine("</html>");

                WriteText(strFilePath, sbContent.ToString());
            }
            catch (Exception ex)
            {
                isRelust = false;
                Common.LogException(ex.ToString(), "CreateHtmlEnd");
            }
            return isRelust;
        }


        public static object CreateHtml(string strTitle, string strFilePath, List<IMHtmlNode> lstIMHtmlNode, bool isPreview)
        {
            bool isRelust = true;
            try
            {
                //创建头部
                CreateHtmlHeader(strTitle, strFilePath);

                string strPreview = "";
                if (isPreview)
                    strPreview = "preview";

                //创建内容
                CreateHtmlContent(strFilePath, lstIMHtmlNode, strPreview);

                //创建尾部
                CreateHtmlEnd(strFilePath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtml");
            }
            return isRelust;
        }

        public static object CreateHtmlContent(string strFilePath, List<IMHtmlNode> lstIMHtmlNode, string strPreview)
        {
            bool isRelust = true;
            try
            {
                string strContent = "";
                string strTime = "";
                bool isShowTime = false;

                foreach (IMHtmlNode info in lstIMHtmlNode)
                {
                    if (strTime.Length == 0 || strTime != info.SmsTime)
                    {
                        isShowTime = true;
                        strTime = info.SmsTime;
                    }
                    else
                    {
                        isShowTime = false;
                    }

                    if (info.SmsContent.Length == 0)
                        continue;

                    switch (info.NType)
                    {
                        case IMHtmlNodeType.IMHtmlNode_Text:
                            strContent = CreateHtmlContentText(strFilePath, info, isShowTime);
                            break;

                        case IMHtmlNodeType.IMHtmlNode_SystemTip:
                            strContent = CreateHtmlContentSystemTip(strFilePath, info, isShowTime);
                            break;

                        case IMHtmlNodeType.IMHtmlNode_Media:
                            if (info.ArrContentForWeb == null)
                                strContent = CreateHtmlContentText(strFilePath, info, isShowTime);
                            else
                                strContent = CreateHtmlContentMedia(strFilePath, info, isShowTime);
                            break;

                        case IMHtmlNodeType.IMHtmlNode_Image:
                            strContent = CreateHtmlContentImage(strFilePath, info, isShowTime, strPreview);
                            break;

                        case IMHtmlNodeType.IMHtmlNode_Voice:
                            strContent = CreateHtmlContentVoice(strFilePath, info, isShowTime, strPreview);
                            break;

                        case IMHtmlNodeType.IMHtmlNode_Video:
                            strContent = CreateHtmlContentVideo(strFilePath, info, isShowTime, strPreview);
                            break;

                        case IMHtmlNodeType.IMHtmlNode_DocumentMessage:
                            strContent = CreateHtmlContentDocumentMessage(strFilePath, info, isShowTime, strPreview);
                            break;

                        case IMHtmlNodeType.IMHtmlNode_MergerForwarding:
                            strContent = CreateHtmlContentMergerForwarding(strFilePath, info, isShowTime, strPreview);
                            break;
                    }

                    WriteText(strFilePath, strContent);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContent");
            }
            return isRelust;
        }

        private static string CreateHtmlContentText(string strFilePath, IMHtmlNode node, bool isShowTime)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", (node.IsItemMe ? "me" : "you")));
                if (isShowTime)
                {
                    strContent.AppendLine(string.Format("<div class=\"time\"><span class=\"timeText\">{0}</span></div>", node.SmsTime));
                }
                strContent.AppendLine("<div class=\"clear\"></div>");
                strContent.AppendLine("<div class=\"chatItemContent\">");
                strContent.AppendLine(string.Format("<img class=\"avatar\" src=\"{0}\" title=\"{1}\">", node.HeaderIconUrl, node.WeChatID));
                strContent.AppendLine(string.Format("<div class=\"cloud cloudText {0}\" cloudfriend=\"{1}\" cloudid=\"{2}\">", (node.IsDeleted ? "deleted" : ""), node.UserName, node.UserID));
                if (node.IsChatRoom && !node.IsItemMe)
                {
                    strContent.AppendLine(string.Format("<div class=\"nickName\">{0}</div>", node.NickName));
                }
                strContent.AppendLine("<div class=\"cloudPannel\">");
                strContent.AppendLine("<div class=\"cloudBody\">");
                strContent.AppendLine("<div class=\"cloudContent\">");
                strContent.AppendLine(string.Format("<pre style=\"white-space:pre-wrap;*white-space:pre;*word-wrap:break-word;\">{0}</pre>", node.SmsContent));
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("<div class=\"cloudArrow\"></div>");
                strContent.AppendLine("</div>");

                //判断是否需要插入引用
                if (node.SvrIMHtmlNode != null)
                {
                    strContent.AppendLine(CreateHtmlContentQuoteHandle(node.SvrIMHtmlNode));
                }

                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentText");
            }
            return strRelust;
        }

        private static string CreateHtmlContentTextEx(string strFilePath, IMHtmlNode node, bool isShowTime)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", (node.IsItemMe ? "me" : "you")));
                if (isShowTime)
                {
                    strContent.AppendLine(string.Format("<div class=\"time\"><span class=\"timeText\">{0}</span></div>", node.SmsTime));
                }
                strContent.AppendLine("<div class=\"clear\"></div>");
                strContent.AppendLine("<div class=\"chatItemContent\">");
                strContent.AppendLine(string.Format("<img class=\"avatar\" src=\"{0}\" title=\"{1}\">", node.HeaderIconUrl, node.WeChatID));
                strContent.AppendLine(string.Format("<div class=\"cloud cloudText {0}\" cloudfriend=\"{1}\" cloudid=\"{2}\">", (node.IsDeleted ? "deleted" : ""), node.UserName, node.UserID));
                if (node.IsChatRoom && !node.IsItemMe)
                {
                    strContent.AppendLine(string.Format("<div class=\"nickName\">{0}</div>", node.NickName));
                }
                strContent.AppendLine("<div class=\"cloudPannel\">");
                strContent.AppendLine("<div class=\"cloudBody\">");
                strContent.AppendLine("<div class=\"cloudContent\">");
                strContent.AppendLine(string.Format("<pre style=\"white-space:pre-wrap;*white-space:pre;*word-wrap:break-word;\">{0}</pre>", node.SmsContent));
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("<div class=\"cloudArrow\"></div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentText");
            }
            return strRelust;
        }

        private static string CreateHtmlContentSystemTip(string strFilePath, IMHtmlNode node, bool isShowTime)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", (node.IsItemMe ? "me" : "you")));
                if (isShowTime)
                {
                    strContent.AppendLine(string.Format("<div class=\"time\"><span class=\"timeText\">{0}</span></div>", node.SmsTime));
                }
                strContent.AppendLine("<div class=\"clear\"></div>");
                strContent.AppendLine(string.Format("<div class=\"systemTip\"><span>{0}</span></div>", node.SmsContent));
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentSystemTip");
            }
            return strRelust;
        }

        private static string CreateHtmlContentMedia(string strFilePath, IMHtmlNode node, bool isShowTime)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", (node.IsItemMe ? "me" : "you")));
                if (isShowTime)
                {
                    strContent.AppendLine(string.Format("<div class=\"time\"><span class=\"timeText\">{0}</span></div>", node.SmsTime));
                }
                if (node.IsChatRoom && !node.IsItemMe)
                {
                    strContent.AppendLine(string.Format("<div class=\"nickName\">{0}</div>", node.NickName));
                }
                strContent.AppendLine("<div class=\"clear\"></div>");
                string sMediaNode = CloudMedia(node);
                strContent.AppendLine(sMediaNode);
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentMedia");
            }
            return strRelust;
        }

        private static string CloudMedia(IMHtmlNode node)
        {
            string strDiv = "";
            try
            {
                JsonArray arrJson = node.ArrContentForWeb;
                //TryCast(CType(node.SmsContent, Object), JsonArray)
                StringBuilder strContent = new StringBuilder();

                string wsNodeUrl = "";
                string wsNodeTitle = "";
                string wsNodePubtime = "";
                string wsNodeCover = "";
                string wsNodeDigest = "";

                if (arrJson == null || arrJson.Count == 0)
                {
                    goto Do_Exit;
                }

                if (arrJson.Count > 1)
                {
                    strContent.AppendLine(string.Format("<div class=\"media {0}\">", (node.IsDeleted ? "deleted" : "")));
                    strContent.AppendLine("<div class=\"mediaPanel\">");
                    int intIndex = 0;
                    foreach (JsonObject item in arrJson)
                    {
                        wsNodeUrl = ((JsonString)item["url"]).Value;
                        wsNodeTitle = ((JsonString)item["title"]).Value;
                        wsNodePubtime = ((JsonString)item["pub_time"]).Value;
                        wsNodeCover = ((JsonString)item["cover"]).Value;
                        wsNodeDigest = ((JsonString)item["digest"]).Value;

                        if (intIndex < 1)
                        {
                            strContent.AppendLine("<div class=\"mediaImg\">");
                            strContent.AppendLine("<div class=\"mediaImgPanel\">");
                            strContent.AppendLine(string.Format("<a href=\"{0}\" target=\"_blank\"><img class=\"lazy\" src=\"{1}\" data-original=\"{2}\" /></a>", wsNodeUrl, wsNodeCover, wsNodeCover));
                            strContent.AppendLine("</div>");
                            strContent.AppendLine("<div class=\"mediaImgFooter\">");
                            strContent.AppendLine(string.Format("<p class=\"mesgTitleTitle left\"><a  title=\"{0}\" href=\"{1}\">{2}</a></p>", wsNodeTitle, wsNodeUrl, wsNodeTitle));
                            strContent.AppendLine("<div class=\"clr\"></div>");
                            strContent.AppendLine("</div>");
                            strContent.AppendLine("</div>");
                        }
                        else
                        {
                            if (intIndex == 1)
                            {
                                strContent.AppendLine("<div class=\"mediaContent\">");
                            }

                            strContent.AppendLine("<div class=\"mediaMesg\">");
                            strContent.AppendLine("<span class=\"mediaMesgDot\"></span>");
                            strContent.AppendLine(string.Format("<div class=\"mediaMesgTitle left\"><p class=\"left\"><a href=\"{0}\" target=\"_blank\">{1}</a></p></div>", wsNodeUrl, wsNodeTitle));
                            strContent.AppendLine("<div class=\"mediaMesgIcon right\">");
                            strContent.AppendLine(string.Format("<a href=\"{0}\" target=\"_blank\"><img class=\"lazy\" src=\"{1}\" data-original=\"{2}\"></a>", wsNodeUrl, wsNodeCover, wsNodeCover));
                            strContent.AppendLine("</div>");
                            strContent.AppendLine("<div class=\"clr\"></div>");
                            strContent.AppendLine("</div>");

                            if ((intIndex == arrJson.Count - 1))
                            {
                                strContent.AppendLine("</div>");
                            }
                        }

                        intIndex = intIndex + 1;
                    }

                    strContent.AppendLine("</div>");
                    strContent.AppendLine("</div>");


                }
                else
                {
                    JsonObject item = (JsonObject)arrJson[0];

                    wsNodeUrl = ((JsonString)item["url"]).Value;
                    wsNodeTitle = ((JsonString)item["title"]).Value;

                    if (item.ContainsKey("pub_time"))
                    {
                        wsNodePubtime = ((JsonString)item["pub_time"]).Value;
                    }

                    if (item.ContainsKey("cover"))
                    {
                        wsNodeCover = ((JsonString)item["cover"]).Value;
                    }

                    if (item.ContainsKey("digest"))
                    {
                        wsNodeDigest = ((JsonString)item["digest"]).Value;
                    }

                    strContent.AppendLine(string.Format("<div class=\"media mediaFullText {0}\">", (node.IsDeleted ? "deleted" : "")));

                    strContent.AppendLine("<div class=\"mediaPanel\"><div class=\"mediaHead\">");
                    strContent.AppendLine(string.Format("<div class=\"title left\"><a href=\"{0}\" target=\"_blank\">{1}</a></div>", wsNodeUrl, wsNodeTitle));
                    strContent.AppendLine("<div class=\"clr\"></div></div>");

                    if (wsNodeCover.Length != 0)
                    {
                        strContent.AppendLine(string.Format("<div class=\"mediaImg\"><a href=\"{0}\" target=\"_blank\"><img class=\"lazy\" src=\"{1}\" data-original=\"{2}\"></a></div>", wsNodeUrl, wsNodeCover, wsNodeCover));
                    }

                    strContent.AppendLine(string.Format("<div class=\"mediaContent mediaContentP\"><p>{0}</p></div>", wsNodeDigest));
                    strContent.AppendLine("<div class=\"mediaFooter\">");
                    strContent.AppendLine(string.Format("<a href=\"{0}\" target=\"_blank\"><span class=\"mesgText left\">查看全文</span></a>", wsNodeUrl));
                    strContent.AppendLine("<div class=\"clr\"></div>");
                    strContent.AppendLine("</div>");

                    strContent.AppendLine("</div>");
                    strContent.AppendLine("</div>");

                }
                strDiv = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CloudMedia");
            }
        Do_Exit:
            return strDiv;
        }

        private static string CreateHtmlContentImage(string strFilePath, IMHtmlNode node, bool isShowTime, string strPreview)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", (node.IsItemMe ? "me" : "you")));
                if (isShowTime)
                {
                    strContent.AppendLine(string.Format("<div class=\"time\"><span class=\"timeText\">{0}</span></div>", node.SmsTime));
                }
                strContent.AppendLine("<div class=\"clear\"></div>");
                strContent.AppendLine("<div class=\"chatItemContent\">");
                strContent.AppendLine(string.Format("<img class=\"avatar\" src=\"{0}\" title=\"{1}\">", node.HeaderIconUrl, node.WeChatID));
                string sImageNode = CloudImage(node, strPreview);
                strContent.AppendLine(sImageNode);
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentImage");
            }
            return strRelust;
        }

        private static string CloudImage(IMHtmlNode node, string strPreview)
        {
            string strDiv = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"cloud cloudImg {0} {1}\">", node.IsDeleted ? "deleted" : "", strPreview));
                if (node.IsChatRoom && !node.IsItemMe)
                {
                    strContent.AppendLine(string.Format("<div class=\"nickName\">{0}</div>", node.NickName));
                }
                strContent.AppendLine("<div class=\"cloudPannel\">");
                strContent.AppendLine("<div class=\"cloudBody\">");
                strContent.AppendLine("<div class=\"cloudContent\">");
                strContent.AppendLine(string.Format("<a href=\"{0}\" target=\"_blank\"><img class=\"zoomIn imageBorder\" src=\"{1}\" /></a>", node.AttachPath, node.ThumbnailPath));
                strContent.AppendLine("</div>");
                strContent.AppendLine("<div class=\"cloudArrow\"></div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");

                //判断是否需要插入引用
                if (node.SvrIMHtmlNode != null)
                {
                    strContent.AppendLine(CreateHtmlContentQuoteHandle(node.SvrIMHtmlNode));
                }

                strContent.AppendLine("</div>");

                strDiv = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CloudImage");
            }
            return strDiv;
        }

        private static string CreateHtmlContentVideo(string strFilePath, IMHtmlNode node, bool isShowTime, string strPreview)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", (node.IsItemMe ? "me" : "you")));
                strContent.AppendLine("<div class=\"chatItemContent\">");
                if (isShowTime)
                {
                    strContent.AppendLine(string.Format("<div class=\"time\"><span class=\"timeText\">{0}</span></div>", node.SmsTime));
                }
                strContent.AppendLine("<div class=\"clear\"></div>");
                strContent.AppendLine(string.Format("<img class=\"avatar\" src=\"{0}\" title=\"{1}\">", node.HeaderIconUrl, node.WeChatID));
                string sImageNode = CloudVideo(node, strPreview);
                strContent.AppendLine(sImageNode);
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentImage");
            }
            return strRelust;
        }

        private static string CreateHtmlContentMergerForwarding(string strFilePath, IMHtmlNode node, bool isShowTime, string strPreview)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", node.IsItemMe ? "me" : "you"));
                if (isShowTime)
                {
                    strContent.AppendLine("<div class=\"time\">");
                    strContent.AppendLine(string.Format("<span class=\"timeText\">{0}</span>", node.SmsTime));
                    strContent.AppendLine(string.Format("</div>"));
                }
                strContent.AppendLine("<div class=\"clear\"></div>");
                strContent.AppendLine("<div class=\"chatItemContent\">");
                strContent.AppendLine(string.Format("<img class=\"avatar\" src=\"{0}\" title=\"{1}\">", node.HeaderIconUrl, node.WeChatID));
                strContent.AppendLine("<div class=\"cloud cloudCombined\" style=\"width: 300px;\">");
                strContent.AppendLine(string.Format("<a href=\"{0}\">", node.StrLink));
                if (node.IsChatRoom && !node.IsItemMe)
                    strContent.AppendLine(string.Format("<div class=\"nickName\">{0}</div>", node.NickName));
                strContent.AppendLine("<div class=\"cloudPannel\">");
                strContent.AppendLine("<div class=\"cloudBody\">");
                strContent.AppendLine("<div class=\"cloudContent\">");
                strContent.AppendLine(string.Format("<p class=\"title\">{0}</p>", node.StrTitle));
                strContent.AppendLine(string.Format("<p class=\"des\" style=\"font-size: 12px;color:#888888;white-space:pre-wrap;*white-space:pre;*word-wrap:break-word;overflow:hidden\">{0}</p>", node.SmsContent));
                strContent.AppendLine("<div class=\"line\"></div>");
                strContent.AppendLine("<div class=\"footer\">聊天记录</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("<div class=\"cloudArrow\"></div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</a>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");

                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentDocumentMessage");
            }
            return strRelust;
        }

        private static string CreateHtmlContentDocumentMessage(string strFilePath, IMHtmlNode node, bool isShowTime, string strPreview)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", node.IsItemMe ? "me" : "you"));
                if (isShowTime)
                {
                    strContent.AppendLine("<div class=\"time\">");
                    strContent.AppendLine("<span class=\"timeText\">");
                    strContent.AppendLine(node.SmsTime);
                    strContent.AppendLine("</span>");
                    strContent.AppendLine("</div>");
                }
                strContent.AppendLine("<div class=\"clear\">");
                strContent.AppendLine("</div>");
                strContent.AppendLine("<div class=\"chatItemContent\">");
                strContent.AppendLine(string.Format("<img class=\"avatar\" src=\"{0}\" title=\"{1}\">", node.HeaderIconUrl, node.WeChatID));
                strContent.AppendLine("<div class=\"cloud cloudDocument\">");
                if (node.IsChatRoom && !node.IsItemMe)
                    strContent.AppendLine(string.Format("<div class=\"nickName\">{0}</div>", node.NickName));
                strContent.AppendLine("<div class=\"cloudPannel\">");
                strContent.AppendLine("<div class=\"cloudBody\">");
                strContent.AppendLine(string.Format("<a href=\"{0}\" target=\"_blank\" download>", node.StrLink));
                strContent.AppendLine("<div class=\"cloudContent\">");
                strContent.AppendLine("<div class=\"content\">");
                strContent.AppendLine("<div class=\"info\">");
                strContent.AppendLine("<p class=\"title\">");
                strContent.AppendLine(node.SmsContent);
                strContent.AppendLine("</p>");
                strContent.AppendLine("<p class=\"size\">");
                strContent.AppendLine(node.StrSize);
                strContent.AppendLine("</p>");
                strContent.AppendLine("</div>");
                strContent.AppendLine(string.Format("<img src=\"{0}\">", node.StrMsgIcon));
                strContent.AppendLine("<div class=\"clearfloat\">");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</a>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("<div class=\"cloudArrow\">");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");

                //判断是否需要插入引用
                if (node.SvrIMHtmlNode != null)
                {
                    strContent.AppendLine(CreateHtmlContentQuoteHandle(node.SvrIMHtmlNode));
                }

                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentDocumentMessage");
            }
            return strRelust;
        }

        private static string CloudVideo(IMHtmlNode node, string strPreview)
        {
            string strDiv = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"cloud cloudVedio {0} {1}\">", node.IsDeleted ? "deleted" : "", strPreview));
                if (node.IsChatRoom && !node.IsItemMe)
                {
                    strContent.AppendLine(string.Format("<div class=\"nickName\">{0}</div>", node.NickName));
                }
                strContent.AppendLine("<div class=\"cloudPannel\">");
                strContent.AppendLine("<div class=\"cloudBody\">");
                strContent.AppendLine("<div class=\"cloudContent\">");
                strContent.AppendLine(string.Format("<a href=\"{1}\"id=\"{0}\" target=\"_blank\">", node.UserID, node.AttachPath));
                strContent.AppendLine(string.Format("<img class=\"zoomIn imageBorder thumb\" src=\"{0}\">", node.ThumbnailPath));
                strContent.AppendLine("<img class=\"play\" src=\"../assets/images/play.png\">");
                strContent.AppendLine("</a>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("<div class=\"cloudArrow \"></div>");
                strContent.AppendLine("</div>");

                //判断是否需要插入引用
                if (node.SvrIMHtmlNode != null)
                {
                    strContent.AppendLine(CreateHtmlContentQuoteHandle(node.SvrIMHtmlNode));
                }

                strContent.AppendLine("</div>");

                strDiv = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CloudVoice");
            }
            return strDiv;
        }

        private static string CreateHtmlContentVoice(string strFilePath, IMHtmlNode node, bool isShowTime, string strPreview)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"chatItem {0}\">", (node.IsItemMe ? "me" : "you")));
                if (isShowTime)
                {
                    strContent.AppendLine(string.Format("<div class=\"time\"><span class=\"timeText\">{0}</span></div>", node.SmsTime));
                }
                strContent.AppendLine("<div class=\"clear\"></div>");
                strContent.AppendLine("<div class=\"chatItemContent\">");
                strContent.AppendLine(string.Format("<img class=\"avatar\" src=\"{0}\" title=\"{1}\">", node.HeaderIconUrl, node.WeChatID));
                string sVoiceNode = CloudVoice(node, strPreview);
                strContent.AppendLine(sVoiceNode);
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentVoice");
            }
            return strRelust;
        }

        private static string CloudVoice(IMHtmlNode node, string strPreview)
        {
            string strDiv = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"cloud cloudVoice {0} {1}\">", node.IsDeleted ? "deleted" : "", strPreview));
                if (node.IsChatRoom && !node.IsItemMe)
                {
                    strContent.AppendLine(string.Format("<div class=\"nickName\">{0}</div>", node.NickName));
                }
                strContent.AppendLine("<div class=node\"cloudPannel\"node>");
                strContent.AppendLine(string.Format("<div class=\"sendStatus\"><span class=\"second\" >{0}</span></div>", node.SmsContent));
                strContent.AppendLine("<div class=\"cloudBody\">");
                strContent.AppendLine("<div class=\"cloudContent\">");
                strContent.AppendLine(string.Format("<a id=\"icoVoice_{0}\" target=\"_blank\" class=\"icoVoice stop\" href=\"{1}\"><span></span></a>", node.UserID, node.AttachPath));
                strContent.AppendLine("</div>");
                strContent.AppendLine("<div class=\"cloudArrow\"></div>");
                strContent.AppendLine("</div>");

                //判断是否需要插入引用
                if (node.SvrIMHtmlNode != null)
                {
                    strContent.AppendLine(CreateHtmlContentQuoteHandle(node.SvrIMHtmlNode));
                }

                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");

                strDiv = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CloudVoice");
            }
            return strDiv;
        }

        private static string CreateHtmlContentQuoteHandle(IMHtmlNode node)
        {
            switch (node.NType)
            {
                case IMHtmlNodeType.IMHtmlNode_SystemTip:
                case IMHtmlNodeType.IMHtmlNode_Media:
                case IMHtmlNodeType.IMHtmlNode_Text:
                    return CreateHtmlContentQuoteText(string.Format("{0}：{1}", node.NickName, node.SmsContent));
                case IMHtmlNodeType.IMHtmlNode_Image:
                    return CreateHtmlContentQuoteImage(node.NickName, node.ThumbnailPath);
                case IMHtmlNodeType.IMHtmlNode_Voice:
                    return CreateHtmlContentQuoteVoice(node.NickName, node.SmsContent);
                case IMHtmlNodeType.IMHtmlNode_Video:
                    return CreateHtmlContentQuoteVedio(node.NickName, node.ThumbnailPath);
                case IMHtmlNodeType.IMHtmlNode_DocumentMessage:
                    return CreateHtmlContentQuoteDocument(node.NickName, node.StrLink, node.SmsContent);
                default:
                    return null;
            }
        }

        private static string CreateHtmlContentQuoteText(string content)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"quoteBox\">"));
                strContent.AppendLine("<div class=\"quoteContent\"></div>");
                strContent.AppendLine(string.Format("<pre class=\"chatItemContent\">{0}</pre>", content));
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentQuoteText");
            }
            return strRelust;
        }

        private static string CreateHtmlContentQuoteImage(string userName, string picPath)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"quoteBox quoteImage\">"));
                strContent.AppendLine("<div class=\"quoteContent\"></div>");
                strContent.AppendLine(string.Format("<span class=\"userName\">{0}</span>：", userName));
                strContent.AppendLine(string.Format("<img class=\"quotePlay\" src=\"{0}\">", picPath));
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentQuoteText");
            }
            return strRelust;
        }

        private static string CreateHtmlContentQuoteVoice(string userName, string time)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"quoteBox quoteVoice\">"));
                strContent.AppendLine("<div class=\"quoteContent\">");
                strContent.AppendLine(string.Format("<span class=\"userName\">{0}</span>：", userName));
                strContent.AppendLine(string.Format("<span class=\"quoteVoiceIcon\"></span>{0}", time));
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentQuoteText");
            }
            return strRelust;
        }

        private static string CreateHtmlContentQuoteVedio(string userName, string picPath)
        {
            string strRelust = "";
            try
            {
                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"quoteBox quoteVedio\">"));
                strContent.AppendLine("<div class=\"quoteContent\"></div>");
                strContent.AppendLine(string.Format("<span class=\"userName\">{0}</span>：", userName));
                strContent.AppendLine("<span class=\"quoteVedioBox\">");
                strContent.AppendLine(string.Format("<img class=\"quoteVedioImg\" src=\"{0}\">", picPath));
                strContent.AppendLine(string.Format("<img class=\"quotePlay\" src=\"../assets/images/icon_video.png\">"));
                strContent.AppendLine("</span>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentQuoteText");
            }
            return strRelust;
        }

        private static string CreateHtmlContentQuoteDocument(string userName, string filePath, string fileName)
        {
            string strRelust = "";
            try
            {
                string extension = Path.GetExtension(filePath);
                string img = "../assets/images/icon_quote_document_default.svg";

                if (extension.ToLower() == ".xls")
                {
                    img = "../assets/images/pic_xls.png";
                }
                else if (extension.ToLower() == ".xlsx")
                {
                    img = "../assets/images/pic_xlsx.png";
                }
                else if (extension.ToLower() == ".doc")
                {
                    img = "../assets/images/pic_doc.png";
                }
                else if (extension.ToLower() == ".docx")
                {
                    img = "../assets/images/pic_docx.png";
                }
                else if (extension.ToLower() == ".ppt")
                {
                    img = "../assets/images/pic_ppt.png";
                }
                else if (extension.ToLower() == ".pptx")
                {
                    img = "../assets/images/pic_pptx.png";
                }


                StringBuilder strContent = new StringBuilder();
                strContent.AppendLine(string.Format("<div class=\"quoteBox quoteDocument\">"));
                strContent.AppendLine("<div class=\"quoteContent\"></div>");
                strContent.AppendLine(string.Format("<pre><span class=\"userName\">{0}</span>：{1}</pre>", userName, fileName));
                strContent.AppendLine(string.Format("<span class=\"quoteDocumentBox\">"));
                strContent.AppendLine(string.Format("<img class=\"quoteDocumentImg\" src=\"{0}\">", img));
                strContent.AppendLine("</span>");
                strContent.AppendLine("</div>");
                strContent.AppendLine("</div>");
                strRelust = strContent.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtmlContentQuoteText");
            }
            return strRelust;
        }

        public static object CreateHtmlHeader(string strTitle, string strFilePath)
        {
            bool isRelust = true;
            try
            {
                StringBuilder sbContent = new StringBuilder();
                sbContent.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
                sbContent.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
                sbContent.AppendLine("<head>");
                sbContent.AppendLine(string.Format("<meta http-equiv=\"Content-Type\" content=\"text/html; charset={0}\">", IniSetting.GetWeChatExportExcelCoding()));
                sbContent.AppendLine("<meta http-equiv=\"Content-Language\" content=\"zh-cn\">");
                sbContent.AppendLine(string.Format("<title>{0}</title>", strTitle));
                sbContent.AppendLine("<script type=\"text/javascript\">");
                sbContent.AppendLine("window.onload=function(){");
                sbContent.AppendLine("document.body.scrollTop = 1000000;");
                sbContent.AppendLine("}");
                sbContent.AppendLine("</script>");
                sbContent.AppendLine("<link rel=\"stylesheet\" type=\"text/css\" href=\"../assets/css/main.css?2014070501\">");
                sbContent.AppendLine("</head>");
                sbContent.AppendLine("<body>");

                sbContent.AppendLine("<div id=\"chat_chatmsglist\" class=\"chatContent\">");

                WriteText(strFilePath, sbContent.ToString());
            }
            catch (Exception ex)
            {
                isRelust = false;
                Common.LogException(ex.ToString(), "CreateHtmlHeader");
            }
            return isRelust;
        }

        public static object CreateHtmlEnd(string strFilePath)
        {
            bool isRelust = true;
            try
            {
                StringBuilder sbContent = new StringBuilder();
                sbContent.AppendLine("</div>");

                //------预览导出------
                sbContent.AppendLine("<div class=\"mask\">");
                sbContent.AppendLine("<div class=\"content\">");
                sbContent.AppendLine("<div class=\"title\">");
                sbContent.AppendLine("<img src=\"../assets/images/icon_pop_logo.png\" alt=\"\">");
                sbContent.AppendLine("<p>提示</p>");
                sbContent.AppendLine("</div>");
                sbContent.AppendLine("<div class=\"warn\">");
                sbContent.AppendLine("<img class=\"warnImg\"src=\"../assets/images/icon_warning.png\" alt=\"\">");
                sbContent.AppendLine("<p class=\"promptText\">1111111111111</p>");
                sbContent.AppendLine("</div>");
                sbContent.AppendLine("<button class=\"confirm\" type=\"button\" name=\"button\">好的</button>");
                sbContent.AppendLine("</div>");
                sbContent.AppendLine("</div>");
                sbContent.AppendLine("<script src=\"../assets/js/jquery.1.8.3.min.js\"></script>");
                sbContent.AppendLine("<script type=\"text/javascript\">");

                //合并转发 附件文件
                sbContent.AppendLine("$(\".cloudCombined .cloudContent .des\").each(function() {");
                sbContent.AppendLine("var maxwidth = 85;");
                sbContent.AppendLine("var text = $(this).text();");
                sbContent.AppendLine("var changeText = text.replace(/\\n/g,'<br>');");
                sbContent.AppendLine("$(this).html(changeText);");
                sbContent.AppendLine("if ($(this).text().length > maxwidth) {");
                sbContent.AppendLine("$(this).html($(this).html().substring(0, maxwidth));");
                sbContent.AppendLine("$(this).html($(this).html() + '...');}});");


                sbContent.AppendLine("$('.cloud').click(function(){");
                sbContent.AppendLine("if ($(this).hasClass('preview')) {");
                sbContent.AppendLine("setTimeout(function(){");
                sbContent.AppendLine("top.postMessage('open', \"*\");");
                sbContent.AppendLine("$('.mask').show();");
                sbContent.AppendLine("});");
                sbContent.AppendLine("$('.mask').show();");
                sbContent.AppendLine("if ($(this).hasClass('cloudImg')) {");
                sbContent.AppendLine(string.Format("$('.mask .promptText').text('试用版导出的图片无法查看，购买激活后即可查看，请前往{0}激活后再重新导出。');", LanguageInterface.Instance().GetString("Common.SoftwareName")));
                sbContent.AppendLine("}else if($(this).hasClass('cloudVoice')){");
                sbContent.AppendLine(string.Format("$('.mask .promptText').text('试用版导出的语音无法播放，购买激活后即可播放，请前往{0}激活后再重新导出。');", LanguageInterface.Instance().GetString("Common.SoftwareName")));
                sbContent.AppendLine("}else if($(this).hasClass('cloudVedio')){");
                sbContent.AppendLine(string.Format("$('.mask .promptText').text('试用版导出的视频无法播放，购买激活后即可播放，请前往{0}激活后再重新导出。');", LanguageInterface.Instance().GetString("Common.SoftwareName")));
                sbContent.AppendLine("}");
                sbContent.AppendLine("return false;");
                sbContent.AppendLine("}");
                sbContent.AppendLine("});");
                sbContent.AppendLine("$('.confirm').click(function(){");
                sbContent.AppendLine("setTimeout(function(){");
                sbContent.AppendLine("top.postMessage(\"close\", \"*\");");
                sbContent.AppendLine("$('.mask').hide();");
                sbContent.AppendLine("});");
                sbContent.AppendLine("});");
                sbContent.AppendLine("</script>");
                //--------------------

                sbContent.AppendLine("</body>");
                sbContent.AppendLine("</html>");

                WriteText(strFilePath, sbContent.ToString());
            }
            catch (Exception ex)
            {
                isRelust = false;
                Common.LogException(ex.ToString(), "CreateHtmlEnd");
            }
            return isRelust;
        }

        private static void WriteText(string strTempFilePath, string strExcelContacts)
        {
            try
            {
                //Dim myByte As Byte() = System.Text.Encoding.Default.GetBytes(strExcelContacts) '导出韩文乱码
                //Using fsWrite As New FileStream(strTempFilePath, FileMode.Append)
                //    fsWrite.Write(myByte, 0, myByte.Length)
                //End Using
                using (StreamWriter writer = new StreamWriter(strTempFilePath, true, Encoding.UTF8))
                {
                    writer.WriteLine(strExcelContacts);
                    writer.Flush();
                    //writer.Close();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WriteText");
            }
        }

        public static bool CreateMainHtml(string strName, string strPath, List<ExportHtmlMainInfo> lstExportHtmlMainInfo)
        {
            bool isRelust = true;
            try
            {
                //创建头部
                CreateMainHtmlHeader(strName, strPath);

                //创建内容
                CreateMainHtmlContent(strPath, lstExportHtmlMainInfo);

                //创建尾部
                CreateMainHtmlEnd(strPath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateHtml");
            }
            return isRelust;
        }

        private static bool CreateMainHtmlHeader(string strName, string strPath)
        {
            bool isRelust = true;
            try
            {
                StringBuilder sbContent = new StringBuilder();
                sbContent.AppendLine("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">");
                sbContent.AppendLine("<html xmlns=\"http://www.w3.org/1999/xhtml\">");
                sbContent.AppendLine("<head>");
                sbContent.AppendLine(string.Format("<meta http-equiv=\"Content-Type\" content=\"text/html; charset={0}\">", IniSetting.GetWeChatExportExcelCoding()));
                sbContent.AppendLine("<meta http-equiv=\"Content-Language\" content=\"zh-cn\">");
                sbContent.AppendLine(string.Format("<title>{0}</title>", strName));

                sbContent.AppendLine("<link href=\"./assets/css/reset.css\" rel=\"stylesheet\"/>");
                sbContent.AppendLine("<link href=\"./assets/css/style.css\" rel=\"stylesheet\"/>");
                sbContent.AppendLine("<script type=\"text/javascript\" src=\"./assets/js/jquery.1.8.3.min.js\"></script>");

                sbContent.AppendLine("<script type=\"text/javascript\">");
                sbContent.AppendLine("function changeIframe() {");
                sbContent.AppendLine("var path = $(this).attr('path')");
                sbContent.AppendLine("$(this).addClass('is-active').siblings().removeClass(\"is-active\")");
                sbContent.AppendLine("$('.main--right iframe').attr('src', path)");
                sbContent.AppendLine("}");
                sbContent.AppendLine("$(document).ready(function() {");
                sbContent.AppendLine("if(window.ActiveXObject || \"ActiveXObject\" in window) {");
                sbContent.AppendLine("document.getElementById('browsehappy').style.display = 'block'");
                sbContent.AppendLine("} else {");
                sbContent.AppendLine("document.getElementById('browsehappy').style.display = 'none'");
                sbContent.AppendLine("}");
                sbContent.AppendLine("$('.nav__item').bind('click',changeIframe)");
                sbContent.AppendLine("});");
                sbContent.AppendLine("</script>");

                sbContent.AppendLine("</head>");
                sbContent.AppendLine("<body>");
                sbContent.AppendLine("  <p id=\"browsehappy\" class=\"browsehappy\">为了获得更好的浏览体验，推荐您使用最新<a href=\"http://www.google.cn/chrome/browser/desktop/index.html\" target=\"_blank\">Chrome浏览器、</a><a href=\"http://www.firefox.com.cn/download/\" target=\"_blank\">Firefox浏览器、</a>或者<a href=\"http://ie.sogou.com/\" target=\"_blank\">搜狗浏览器</a>打开本聊天记录HTML文件</p>");
                sbContent.AppendLine(string.Format("  <p class=\"browsehappy\">如果出现大部分好友头像显示为默认图片、好友昵称显示错误，请前往{0}消息界面点击左上角【更新好友信息】后重新导出。</p>", LanguageInterface.Instance().GetString("Common.SoftwareName")));
                sbContent.AppendLine("<div class=\"container\">");
                sbContent.AppendLine("<div class=\"top\">");
                sbContent.AppendLine("<img src=\"assets/images/icon_weixin_title_logo.png\" alt=\"\">");
                sbContent.AppendLine(string.Format("<p>{0}</p>", LanguageInterface.Instance().GetString("Common.SoftwareName")));
                sbContent.AppendLine("</div>");
                sbContent.AppendLine("<div class=\"line\"></div>");
                sbContent.AppendLine("<div class=\"wrap\">");


                WriteText(strPath, sbContent.ToString());
            }
            catch (Exception ex)
            {
                isRelust = false;
                Common.LogException(ex.ToString(), "CreateMainHtmlHeader");
            }
            return isRelust;
        }

        private static bool CreateMainHtmlContent(string strPath, List<ExportHtmlMainInfo> lstExportHtmlMainInfo)
        {
            bool isRelust = true;
            try
            {
                StringBuilder strContent = new StringBuilder();

                //创建左边
                strContent.AppendLine("<div class=\"main--left\" >");
                strContent.AppendLine("<ul class=\"nav__box\">");
                foreach (ExportHtmlMainInfo info in lstExportHtmlMainInfo)
                {
                    try
                    {
                        strContent.AppendLine(string.Format("<li class=\"{0}\" path=\"{1}\">", (info.id % 2 == 1 ? "nav__item" : "nav__item even"), info.StrUrl));
                        strContent.AppendLine(string.Format("<div class=\"icon\"><img src=\"{0}\" /></div>", info.IconImage));
                        strContent.AppendLine("<div class=\"content\">");
                        strContent.AppendLine(string.Format("<p>{0}</p>", info.UserName));
                        //strContent.AppendLine(String.Format("<p class=""summary"">{0}</p>", info.LastMessage))
                        strContent.AppendLine("</div>");
                        strContent.AppendLine("<div class=\"time\">");
                        strContent.AppendLine(string.Format("<p>{0}</p>", GetLastChatTime(info.LastMessageTime)));
                        strContent.AppendLine("</div>");
                        strContent.AppendLine("</li>");

                        if (strContent.Length >= 1000)
                        {
                            WriteText(strPath, strContent.ToString());
                            strContent.Remove(0, strContent.Length);
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "CreateMainHtmlContent_1");
                    }

                }
                strContent.AppendLine("</ul>");
                strContent.AppendLine("</div>");

                //创建右边
                strContent.AppendLine("<div class=\"main--right\">");
                strContent.AppendLine(string.Format("<iframe src=\"{0}\" frameborder=\"no\"></iframe>", lstExportHtmlMainInfo[0].StrUrl));
                strContent.AppendLine("</div>");


                WriteText(strPath, strContent.ToString());
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateMainHtmlContent");
            }
            return isRelust;
        }

        private static string GetLastChatTime(DateTime LastChatTime)
        {
            string strTime = string.Empty;
            TimeSpan ts = DateTime.Now.Date.Subtract(LastChatTime.Date);
            //只需比较日期就好

            switch (ts.Days)
            {
                case 0:
                    strTime = LastChatTime.ToString("HH:mm");

                    break;
                case 1:
                    strTime = "昨天";

                    break;
                default:
                    strTime = LastChatTime.ToString("yyyy-MM-dd");

                    break;
            }

            return strTime;
        }

        private static bool CreateMainHtmlEnd(string strPath)
        {
            bool isRelust = true;
            try
            {
                StringBuilder sbContent = new StringBuilder();
                sbContent.AppendLine("</div>");
                sbContent.AppendLine("<div class=\"mask\"></div>");
                sbContent.AppendLine("</div>");
                sbContent.AppendLine("<script type=\"text/javascript\">");
                sbContent.AppendLine("$(function() {");
                sbContent.AppendLine("window.addEventListener(\"message\",function(e) {");
                sbContent.AppendLine("if (e.data == 'open') {");
                sbContent.AppendLine("$('.mask').show();");
                sbContent.AppendLine("}else{");
                sbContent.AppendLine("$('.mask').hide();");
                sbContent.AppendLine("}");
                sbContent.AppendLine("}, false);");
                sbContent.AppendLine("});");
                sbContent.AppendLine("</script>");
                sbContent.AppendLine("</body>");
                sbContent.AppendLine("</html>");

                WriteText(strPath, sbContent.ToString());
            }
            catch (Exception ex)
            {
                isRelust = false;
                Common.LogException(ex.ToString(), "CreateMainHtmlEnd");
            }
            return isRelust;

        }

    }

    public enum IMHtmlNodeType
    {
        IMHtmlNode_Text,
        IMHtmlNode_Image,
        IMHtmlNode_Voice,
        IMHtmlNode_Media,
        IMHtmlNode_Video,
        IMHtmlNode_SystemTip,
        IMHtmlNode_MergerForwarding,
        IMHtmlNode_DocumentMessage
    }

    public class IMHtmlNode
    {

        IMHtmlNodeType mType;
        bool mDeleted;
        bool mItemMe;
        string mUserName = "";
        string mUserID = "";
        string mNickName = "";
        string mHeaderIconUrl = "";
        string mSmsTime = "";
        string mSmsContent = "";
        string mThumbnailPath = "";
        string mAttachPath = "";
        string mWeChatID = "";
        bool mIsChatRoom = false;
        JsonArray mArrContentForWeb;
        string mStrLink = "";
        string mStrSize = "";
        string mStrMsgIcon = "";
        string mStrTitle = "";
        IMHtmlNode mSvrIMHtmlNode;

        public string StrTitle
        {
            get { return mStrTitle; }
            set { mStrTitle = value; }
        }

        public string StrMsgIcon
        {
            get { return mStrMsgIcon; }
            set { mStrMsgIcon = value; }
        }

        public string StrSize
        {
            get { return mStrSize; }
            set { mStrSize = value; }
        }

        public string StrLink
        {
            get { return mStrLink; }
            set { mStrLink = value; }
        }

        public IMHtmlNodeType NType
        {
            get { return this.mType; }
            set { this.mType = value; }
        }

        public bool IsDeleted
        {
            get { return this.mDeleted; }
            set { this.mDeleted = value; }
        }

        public bool IsItemMe
        {
            get { return this.mItemMe; }
            set { this.mItemMe = value; }
        }

        public string UserName
        {
            get { return this.mUserName; }
            set { this.mUserName = value; }
        }

        public string UserID
        {
            get { return this.mUserID; }
            set { this.mUserID = value; }
        }

        public string NickName
        {
            get { return this.mNickName; }
            set { this.mNickName = value; }
        }

        public string WeChatID
        {
            get { return this.mWeChatID; }
            set { this.mWeChatID = value; }
        }

        public string HeaderIconUrl
        {
            get { return this.mHeaderIconUrl; }
            set { this.mHeaderIconUrl = value; }
        }

        public string SmsTime
        {
            get { return this.mSmsTime; }
            set { this.mSmsTime = value; }
        }

        public string SmsContent
        {
            get { return this.mSmsContent; }
            set { this.mSmsContent = value; }
        }

        public string ThumbnailPath
        {
            get { return this.mThumbnailPath; }
            set { this.mThumbnailPath = value; }
        }

        public string AttachPath
        {
            get { return this.mAttachPath; }
            set { this.mAttachPath = value; }
        }

        public JsonArray ArrContentForWeb
        {
            get { return this.mArrContentForWeb; }
            set { this.mArrContentForWeb = value; }
        }

        public bool IsChatRoom
        {
            get { return this.mIsChatRoom; }
            set { this.mIsChatRoom = value; }
        }

        public IMHtmlNode SvrIMHtmlNode
        {
            get { return this.mSvrIMHtmlNode; }
            set { this.mSvrIMHtmlNode = value; }
        }
    }

}



