﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{7DA7C952-ED28-464E-A49C-EF29BA83C164}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>iWechatAssistant</RootNamespace>
    <AssemblyName>iWechatAssistant</AssemblyName>
    <TargetFrameworkVersion>v2.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>bin\x86\Release\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>bin\x64\Release\</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="System" />
    <Reference Include="System.Data.SQLite">
      <HintPath>..\..\Tongbu_Assistant_ios\iTong\IncludeDlls\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ExportHelper.cs" />
    <Compile Include="frmCharge.cs" />
    <Compile Include="frmCharge.Designer.cs">
      <DependentUpon>frmCharge.cs</DependentUpon>
    </Compile>
    <Compile Include="IMHtmlNodeHelper.cs" />
    <Compile Include="MainForm.cs" />
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ProtobufClass\BackConfigMast.cs" />
    <Compile Include="ProtobufClass\WechatAccMast.cs" />
    <Compile Include="ProtobufClass\WechatMessageMast.cs" />
    <Compile Include="WechatAccInfo.cs" />
    <Compile Include="WechatHeplerDB.cs" />
    <Compile Include="WechatHeplerPC.cs" />
    <Compile Include="WechatTalk.cs" />
    <EmbeddedResource Include="frmCharge.resx">
      <DependentUpon>frmCharge.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Resources\weixin_dll_aud" />
    <None Include="Resources\weixin_zip_ChatWebPage.zip" />
    <None Include="Resources\weixin_zip_exporthtml.zip" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Components\Components.vbproj">
      <Project>{28459798-5E28-45D3-AAE3-74A9F1761D1A}</Project>
      <Name>Components</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc.csproj">
      <Project>{685ae51b-3c37-4b37-b3a2-b485d07a6e6b}</Project>
      <Name>CoreMisc</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses.csproj">
      <Project>{65ce9103-521a-49e6-a8cd-89137b452842}</Project>
      <Name>CoreReses</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag.csproj">
      <Project>{b02ce9cf-6163-411c-870e-1508ddd16cfc}</Project>
      <Name>CoreTag</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS.csproj">
      <Project>{61c62ea1-6ad7-41be-98db-8abb77ee35f3}</Project>
      <Name>CoreUtilCS</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil.vbproj">
      <Project>{718b167f-1b33-4b7c-a7e3-e15615ddaac4}</Project>
      <Name>CoreUtil</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf.csproj">
      <Project>{d8ab1668-b7ab-45d6-8c6c-bfc132a37ec9}</Project>
      <Name>ProtoBuf</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\ICSharpCode.SharpZLib.csproj">
      <Project>{0e7413ff-eb9e-4714-acf2-be3a6a7b2ffd}</Project>
      <Name>ICSharpCode.SharpZLib</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\dcf.dll" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\weixin_icon_default.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\weixin_dll_silk.dll" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\excel_header.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\gif_loading_16.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\gif_loading_231.gif" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>