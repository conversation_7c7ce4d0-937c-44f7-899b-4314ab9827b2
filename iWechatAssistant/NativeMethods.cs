﻿using System;
using System.Runtime.InteropServices;

namespace iWechatAssistant
{
    internal class NativeMethods
    {
        //导入判断网络是否连接的 .dll  
        [DllImport("wininet.dll", EntryPoint = "InternetGetConnectedState")]
        //判断网络状况的方法,返回值true为连接，false为未连接  
        internal extern static bool InternetGetConnectedState(out int conState, int reder);


        [DllImport("user32.dll", EntryPoint = "FindWindow", CharSet = CharSet.Auto)]
        internal extern static IntPtr FindWindow(string classname, string captionName);

        [DllImport("kernel32.dll", SetLastError = true, CallingConvention = CallingConvention.Winapi)]
        [return: Marshal<PERSON>(UnmanagedType.Bool)]
        internal static extern bool IsWow64Process([In] IntPtr hProcess, [Out] out bool lpSystemInfo);
    }
}
