﻿namespace iWechatAssistant
{
    partial class frmAccredit
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmAccredit));
            this.btn_close = new iTong.Components.tbButton();
            this.btnOK = new iTong.Components.tbButton();
            this.tbPanel1 = new iTong.Components.tbPanel();
            this.lblMsg = new iTong.Components.tbLabel();
            this.btnOld = new iTong.Components.tbButton();
            this.tbPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // btn_close
            // 
            this.btn_close.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_close.BackColor = System.Drawing.Color.Transparent;
            this.btn_close.BindingForm = null;
            this.btn_close.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_close.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_close.Location = new System.Drawing.Point(289, 3);
            this.btn_close.Name = "btn_close";
            this.btn_close.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_close.Selectable = true;
            this.btn_close.Size = new System.Drawing.Size(26, 26);
            this.btn_close.TabIndex = 48;
            this.btn_close.tbAdriftIconWhenHover = false;
            this.btn_close.tbAutoSize = true;
            this.btn_close.tbAutoSizeEx = false;
            this.btn_close.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_close;
            this.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbBadgeNumber = 0;
            this.btn_close.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_close.tbEnableEnter = true;
            this.btn_close.tbEndEllipsis = false;
            this.btn_close.tbIconHoldPlace = true;
            this.btn_close.tbIconImage = null;
            this.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbIconMore = false;
            this.btn_close.tbIconMouseDown = null;
            this.btn_close.tbIconMouseHover = null;
            this.btn_close.tbIconMouseLeave = null;
            this.btn_close.tbIconPlaceText = 2;
            this.btn_close.tbIconReadOnly = null;
            this.btn_close.tbIconSize = new System.Drawing.Size(0, 0);
            this.btn_close.tbImageMouseDown = null;
            this.btn_close.tbImageMouseHover = null;
            this.btn_close.tbImageMouseLeave = null;
            this.btn_close.tbProgressValue = 50;
            this.btn_close.tbReadOnly = false;
            this.btn_close.tbReadOnlyText = false;
            this.btn_close.tbShadow = false;
            this.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_close.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_close.tbShowDot = false;
            this.btn_close.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_close.tbShowMoreIconImg")));
            this.btn_close.tbShowNew = false;
            this.btn_close.tbShowProgress = false;
            this.btn_close.tbShowTip = true;
            this.btn_close.tbShowToolTipOnButton = false;
            this.btn_close.tbSplit = "3,3,3,3";
            this.btn_close.tbText = "";
            this.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbTextColor = System.Drawing.Color.White;
            this.btn_close.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_close.tbTextColorDown = System.Drawing.Color.White;
            this.btn_close.tbTextColorHover = System.Drawing.Color.White;
            this.btn_close.tbTextMouseDownPlace = 0;
            this.btn_close.tbToolTip = "";
            this.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_close.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbUpToDownWhenCenter = false;
            this.btn_close.VisibleEx = true;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.BackColor = System.Drawing.Color.Transparent;
            this.btnOK.BindingForm = null;
            this.btnOK.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnOK.Font = new System.Drawing.Font("宋体", 9F);
            this.btnOK.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnOK.Location = new System.Drawing.Point(246, 112);
            this.btnOK.Name = "btnOK";
            this.btnOK.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnOK.Selectable = true;
            this.btnOK.Size = new System.Drawing.Size(39, 24);
            this.btnOK.TabIndex = 51;
            this.btnOK.tbAdriftIconWhenHover = false;
            this.btnOK.tbAutoSize = false;
            this.btnOK.tbAutoSizeEx = true;
            this.btnOK.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnOK.tbBadgeNumber = 0;
            this.btnOK.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnOK.tbEnableEnter = true;
            this.btnOK.tbEndEllipsis = false;
            this.btnOK.tbIconHoldPlace = true;
            this.btnOK.tbIconImage = null;
            this.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnOK.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnOK.tbIconMore = false;
            this.btnOK.tbIconMouseDown = null;
            this.btnOK.tbIconMouseHover = null;
            this.btnOK.tbIconMouseLeave = null;
            this.btnOK.tbIconPlaceText = 2;
            this.btnOK.tbIconReadOnly = null;
            this.btnOK.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnOK.tbImageMouseDown = null;
            this.btnOK.tbImageMouseHover = null;
            this.btnOK.tbImageMouseLeave = null;
            this.btnOK.tbProgressValue = 50;
            this.btnOK.tbReadOnly = false;
            this.btnOK.tbReadOnlyText = false;
            this.btnOK.tbShadow = false;
            this.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnOK.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnOK.tbShowDot = false;
            this.btnOK.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnOK.tbShowMoreIconImg")));
            this.btnOK.tbShowNew = false;
            this.btnOK.tbShowProgress = false;
            this.btnOK.tbShowTip = true;
            this.btnOK.tbShowToolTipOnButton = false;
            this.btnOK.tbSplit = "13,11,13,11";
            this.btnOK.tbText = "确定";
            this.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnOK.tbTextColor = System.Drawing.Color.White;
            this.btnOK.tbTextColorDisable = System.Drawing.Color.White;
            this.btnOK.tbTextColorDown = System.Drawing.Color.White;
            this.btnOK.tbTextColorHover = System.Drawing.Color.White;
            this.btnOK.tbTextMouseDownPlace = 2;
            this.btnOK.tbToolTip = "";
            this.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnOK.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnOK.tbUpToDownWhenCenter = false;
            this.btnOK.Visible = false;
            this.btnOK.VisibleEx = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // tbPanel1
            // 
            this.tbPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbPanel1.BackColor = System.Drawing.Color.White;
            this.tbPanel1.Controls.Add(this.lblMsg);
            this.tbPanel1.Controls.Add(this.btnOld);
            this.tbPanel1.Controls.Add(this.btnOK);
            this.tbPanel1.Location = new System.Drawing.Point(1, 42);
            this.tbPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel1.Name = "tbPanel1";
            this.tbPanel1.Size = new System.Drawing.Size(316, 142);
            this.tbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel1.TabIndex = 52;
            this.tbPanel1.tbBackgroundImage = null;
            this.tbPanel1.tbShowWatermark = false;
            this.tbPanel1.tbSplit = "0,0,0,0";
            this.tbPanel1.tbWatermark = null;
            this.tbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel1.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // lblMsg
            // 
            this.lblMsg.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblMsg.BackColor = System.Drawing.Color.Transparent;
            this.lblMsg.Location = new System.Drawing.Point(31, 16);
            this.lblMsg.Name = "lblMsg";
            this.lblMsg.Size = new System.Drawing.Size(254, 83);
            this.lblMsg.TabIndex = 49;
            this.lblMsg.tbAdriftWhenHover = false;
            this.lblMsg.tbAutoEllipsis = false;
            this.lblMsg.tbAutoSize = false;
            this.lblMsg.tbHideImage = false;
            this.lblMsg.tbIconImage = null;
            this.lblMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblMsg.tbIconPlaceText = 5;
            this.lblMsg.tbShadow = false;
            this.lblMsg.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblMsg.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.lblMsg.tbShowScrolling = false;
            this.lblMsg.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnOld
            // 
            this.btnOld.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOld.BackColor = System.Drawing.Color.Transparent;
            this.btnOld.BindingForm = null;
            this.btnOld.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnOld.Font = new System.Drawing.Font("宋体", 9F);
            this.btnOld.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnOld.Location = new System.Drawing.Point(150, 112);
            this.btnOld.Name = "btnOld";
            this.btnOld.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnOld.Selectable = true;
            this.btnOld.Size = new System.Drawing.Size(92, 24);
            this.btnOld.TabIndex = 51;
            this.btnOld.tbAdriftIconWhenHover = false;
            this.btnOld.tbAutoSize = false;
            this.btnOld.tbAutoSizeEx = false;
            this.btnOld.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnOld.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnOld.tbBadgeNumber = 0;
            this.btnOld.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnOld.tbEnableEnter = true;
            this.btnOld.tbEndEllipsis = false;
            this.btnOld.tbIconHoldPlace = true;
            this.btnOld.tbIconImage = null;
            this.btnOld.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnOld.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnOld.tbIconMore = false;
            this.btnOld.tbIconMouseDown = null;
            this.btnOld.tbIconMouseHover = null;
            this.btnOld.tbIconMouseLeave = null;
            this.btnOld.tbIconPlaceText = 2;
            this.btnOld.tbIconReadOnly = null;
            this.btnOld.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnOld.tbImageMouseDown = null;
            this.btnOld.tbImageMouseHover = null;
            this.btnOld.tbImageMouseLeave = null;
            this.btnOld.tbProgressValue = 50;
            this.btnOld.tbReadOnly = false;
            this.btnOld.tbReadOnlyText = false;
            this.btnOld.tbShadow = false;
            this.btnOld.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnOld.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnOld.tbShowDot = false;
            this.btnOld.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnOld.tbShowMoreIconImg")));
            this.btnOld.tbShowNew = false;
            this.btnOld.tbShowProgress = false;
            this.btnOld.tbShowTip = true;
            this.btnOld.tbShowToolTipOnButton = false;
            this.btnOld.tbSplit = "13,11,13,11";
            this.btnOld.tbText = "推荐版本授权";
            this.btnOld.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnOld.tbTextColor = System.Drawing.Color.White;
            this.btnOld.tbTextColorDisable = System.Drawing.Color.White;
            this.btnOld.tbTextColorDown = System.Drawing.Color.White;
            this.btnOld.tbTextColorHover = System.Drawing.Color.White;
            this.btnOld.tbTextMouseDownPlace = 2;
            this.btnOld.tbToolTip = "";
            this.btnOld.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnOld.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnOld.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnOld.tbUpToDownWhenCenter = false;
            this.btnOld.Visible = false;
            this.btnOld.VisibleEx = true;
            this.btnOld.Click += new System.EventHandler(this.btnOld_Click);
            // 
            // frmAccredit
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(318, 194);
            this.Controls.Add(this.tbPanel1);
            this.Controls.Add(this.btn_close);
            this.Name = "frmAccredit";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.tbAutoSetFormSize = true;
            this.tbGuiBackground = global::iWechatAssistant.Properties.Resources.frm_bg_sub;
            this.tbShowTitleOnForm = true;
            this.tbSplit = "6,35,7,50";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.tbTitleForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.tbTitleLocation = new System.Drawing.Point(4, 10);
            this.Text = "授权";
            this.tbPanel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        internal iTong.Components.tbButton btn_close;
        internal iTong.Components.tbButton btnOK;
        private iTong.Components.tbPanel tbPanel1;
        private iTong.Components.tbLabel lblMsg;
        internal iTong.Components.tbButton btnOld;
    }
}