﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace iWechatAssistant
{
    public partial class frmAccredit : tbBaseGuiForm
    {
        private WechatAccInfo mCurrentWAInfo = null;
        private string mCurrentSelectPath = "";
        private string mWechatPath = "";
        public string mStrBackupkey = "";
        public string mStrContactPath = "";
        private int mIntOnlyContact = 0;
        public bool mIsGetFriend = false;

        public frmAccredit(WechatAccInfo info, string strPath, string strWPath, int intOnlyContact = 0)
        {
            InitializeComponent();

            this.Icon = global::iWechatAssistant.Properties.Resources.main;

            this.mCurrentWAInfo = info;
            this.mCurrentSelectPath = strPath;
            this.mWechatPath = strWPath;
            this.mIntOnlyContact = intOnlyContact;
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);

            SelfAccreditHelper helper = SelfAccreditHelper.GetInstance(this.mCurrentSelectPath, this.mCurrentWAInfo, this.mWechatPath);
            helper.SWeHelpWork(this.mIntOnlyContact);
            helper.SelfAccreditHandler -= helper_SelfAccreditHandler;
            helper.SelfAccreditHandler += helper_SelfAccreditHandler;
        }

        private void helper_SelfAccreditHandler(object sender, SelfAccreditEventArgs e)
        {
            this.mStrBackupkey = e.StrBackupkey;
            this.mStrContactPath = e.StrContactPath;
            this.mIsGetFriend = e.IsGetFriend;
            this.SetMessage(e.StrMessage, e.IsOver);
        }

        private delegate void SetMessageHandler(string strMsg, bool isRelust);
        private void SetMessage(string strMsg, bool isRelust = false)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetMessageHandler(SetMessage), strMsg, isRelust);
            }
            else
            {
                this.lblMsg.TextAlign = ContentAlignment.MiddleLeft;
                if (strMsg.Contains("建议您使用【推荐版本授权】功能进行授权。"))
                    this.btnOld.Visible = isRelust;

                if (strMsg.EndsWith("..."))
                    this.lblMsg.TextAlign = ContentAlignment.MiddleCenter;

                if (strMsg.Length<=12)
                    this.lblMsg.TextAlign = ContentAlignment.MiddleCenter;

                this.lblMsg.Text = strMsg;
                this.btnOK.Visible = isRelust;

            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            this.DialogResult = System.Windows.Forms.DialogResult.OK;
        }

        private void btnOld_Click(object sender, EventArgs e)
        {
            Common.OpenExplorer("http://news.tongbu.com/96466.html");
            this.Close();
        }
    }
}
