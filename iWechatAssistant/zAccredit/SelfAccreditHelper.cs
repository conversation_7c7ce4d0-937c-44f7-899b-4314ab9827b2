﻿using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;

using iTong.CoreFoundation;

namespace iWechatAssistant
{
    public class SelfAccreditHelper
    {
        const int WM_COPYDATA = 0x4A;

        private string mStrContactPath = "";
        private string mStrBackupkeyJson = "";
        public string mStrBackupkey = "";

        private Thread mTDStartWeHelpWork = null;
        private string mContactPath = "";

        //[StructLayout(LayoutKind.Sequential)]
        //public struct COPYDATASTRUCT
        //{
        //    public int dwData;//类型
        //    public int cbData;//长度
        //    //[MarshalAs(UnmanagedType.LPStr)]
        //    public IntPtr lpData;//json数据
        //}

        private string mCurrentSelectPath = "";
        private WechatAccInfo mCurrentWAInfo = null;
        private string mWechatPath = "";

        public event EventHandler<SelfAccreditEventArgs> SelfAccreditHandler;

        private string strText = "授权";

        #region "--- 单例 ---"
        //单一例，一个设备返回一个实例
        private static Dictionary<string, SelfAccreditHelper> mDictInstances = new Dictionary<string, SelfAccreditHelper>(StringComparer.InvariantCultureIgnoreCase);
        // 检测冗余的调用
        private bool disposedValue = false;
        private static object _lockGetInstance = new object();

        public SelfAccreditHelper(string strCurrentSelectPath, WechatAccInfo CurrentWAInfo, string strWechatPath)
        {
            this.mCurrentSelectPath = strCurrentSelectPath;
            this.mCurrentWAInfo = CurrentWAInfo;
            this.mWechatPath = strWechatPath;
        }

        public static SelfAccreditHelper GetInstance(string strCurrentSelectPath, WechatAccInfo CurrentWAInfo, string strWechatPath)
        {
            SelfAccreditHelper helper = null;
            bool blnRetry = false;

            lock (_lockGetInstance)
            {
            DO_RETRY:
                try
                {
                    if (mDictInstances.ContainsKey(CurrentWAInfo.Account))
                    {
                        SelfAccreditHelper tmpHelper = mDictInstances[CurrentWAInfo.Account];
                        mDictInstances.Remove(CurrentWAInfo.Account);
                        //释放资源
                        tmpHelper.Dispose();

                        mDictInstances.Add(CurrentWAInfo.Account, new SelfAccreditHelper(strCurrentSelectPath, CurrentWAInfo, strWechatPath));
                    }
                    else
                    {
                        if (!mDictInstances.ContainsKey(CurrentWAInfo.Account))
                        {
                            mDictInstances.Add(CurrentWAInfo.Account, new SelfAccreditHelper(strCurrentSelectPath, CurrentWAInfo, strWechatPath));
                        }
                    }
                    helper = mDictInstances[CurrentWAInfo.Account];
                }
                catch (Exception ex)
                {
                    if (!blnRetry)
                    {
                        blnRetry = true;
                        goto DO_RETRY;
                    }
                }
            }
            return helper;
        }

        #endregion

        #region "--- 释放 ---"

        public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: 显式调用时释放非托管资源                 
                    mDictInstances.Clear();
                    try
                    {
                        if (this.mTDStartWeHelpWork != null && this.mTDStartWeHelpWork.ThreadState != ThreadState.Stopped)
                        {
                            this.mTDStartWeHelpWork.Abort();
                        }
                    }
                    catch { }
                }
            }
            disposedValue = true;
        }

        #endregion

        #region "--- 授权 ---"

        public delegate void WXBackupKeyCB(string key, int keylen);
        public delegate void WXContactCB(IntPtr contact, int len);
        public delegate void WXLogCB(string log, int len);

        [DllImport(".\\IncludeDlls\\CWeHelp.dll", EntryPoint = "StartWeHelpWork", CallingConvention = CallingConvention.Cdecl)]
        private static extern int StartWeHelpWork(IntPtr MicroMsgDBPath, int dbPathSize, string Offset, IntPtr ContactPath, int ContactPathSize,
                                        string UserName, //wxid
                                         string AliasName, //用户设置的微信号
                                         int OnlyContact,//0获取密钥和联系人，1(非0)为只获取联系人
                                         WXBackupKeyCB keyCB, WXContactCB ContactCB, WXLogCB LogCB);

        //[DllImport(".\\IncludeDlls\\CWeHelp.dll", CallingConvention = CallingConvention.Cdecl)]
        //public static extern void StopWeHelpWork();

        private void contact_callback(IntPtr contact, int len)
        {
            Common.Log("Do--2");
            try
            {
                byte[] bt = new byte[len];
                Marshal.Copy(contact, bt, 0, len);
                this.mStrContactPath = System.Text.Encoding.UTF8.GetString(bt);
                Common.LogException("contact_callback", "contact_callback");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "contact_callback");
            }

        }

        private void backupkey_callback(string key, int keylen)
        {
            Common.Log("Do--1");
            try
            {
                JsonObject jObject = new JsonObject();
                jObject.Add("wxid", this.mCurrentWAInfo.Account);
                jObject.Add("pwd", key);
                jObject.Add("nickname", this.mCurrentWAInfo.Name);
                this.mStrBackupkey = key;
                this.mStrBackupkeyJson = JsonParser.SaveString(jObject);
                Common.LogException("backupkey_callback", "backupkey_callback");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "backupkey_callback");
            }
        }
        string mStrVerMeaage = "";
        string mStrUserVer = "";
        bool mIsSendVer = false;
        private void log_callback(string log, int len)
        {
            if (log.StartsWith("[Ver];"))
            {
                string[] arrVer = log.Split(';');
                string strLstVersion = arrVer[1];
                long intLstVersion = VersionFormation.FormatVersion(strLstVersion);
                mStrUserVer = arrVer[2];
                long intUserVersion = VersionFormation.FormatVersion(mStrUserVer);

                if (intLstVersion > intUserVersion)
                {
                    mStrVerMeaage = string.Format("您当前微信电脑版本为【{0}】,\n\r请升级微信电脑版至最新版。\n\r建议您使用【推荐版本授权】功能进行授权。", strLstVersion);
                }
                else if (intLstVersion < intUserVersion)
                {
                    mStrVerMeaage = string.Format("您当前微信电脑版本为【{0}】尚未兼容,\n\r技术人员需要1-3个工作日兼容，请耐心等待。\n\r建议您使用【推荐版本授权】功能进行授权。", mStrUserVer);
                    mIsSendVer = true;
                }
            }
            Common.LogException(log, string.Format("微信{0}日志", strText));
        }

        public void SWeHelpWork(int intOnlyContact = 0)
        {
            if (intOnlyContact == 0)
                strText = "授权";
            else
                strText = "更新";

            if (mTDStartWeHelpWork != null && mTDStartWeHelpWork.ThreadState != ThreadState.Stopped)
                return;

            this.OnSelfAccreditHandler(new SelfAccreditEventArgs(0, string.Format("开始{0},请耐心等待...", strText), this.mStrContactPath, this.mStrBackupkey));

            Common.Log(string.Format("开始{0},请耐心等待...", strText), "DoSWeHelpWork", true);

            mTDStartWeHelpWork = new Thread(new ParameterizedThreadStart(DoSWeHelpWork));
            mTDStartWeHelpWork.IsBackground = true;
            mTDStartWeHelpWork.Start(new object[] { this.mCurrentWAInfo.Account, this.mCurrentWAInfo.InnerAccount, intOnlyContact });
        }

        private void DoSWeHelpWork(object obj)
        {
            try
            {
                int ret = 0;
                object[] arrObj = (object[])obj;
                string strUserName = arrObj[0].ToString();
                string strAliasName = arrObj[1].ToString();
                int intOnlyContact = (int)arrObj[2];

                try
                {
                    //开始授权
                    if (intOnlyContact == 0)
                    {
                        Utility.PostData("https://at.umeng.com/XP1jai?cid=481", "", 2000);
                    }
                }
                catch { }
                this.OnSelfAccreditHandler(new SelfAccreditEventArgs(0, string.Format("{0}中,请耐心等待...", strText), this.mStrContactPath, this.mStrBackupkey));

                string dbpath = Path.Combine(this.mCurrentSelectPath, @"Msg\MicroMsg.db");
                //string offset = this.GetOffset();
                string strTempF = Folder.GetTempFilePath();
                Folder.CheckFolder(strTempF);
                mContactPath = Path.Combine(strTempF, "TempFriend.txt");

                byte[] dbbuf = System.Text.Encoding.UTF8.GetBytes(dbpath);
                byte[] contactbuf = System.Text.Encoding.UTF8.GetBytes(mContactPath);
                IntPtr dbPtr = Marshal.UnsafeAddrOfPinnedArrayElement(dbbuf, 0);
                IntPtr contactPtr = Marshal.UnsafeAddrOfPinnedArrayElement(contactbuf, 0);

                try
                {
                    Common.Log(string.Format("正在对用户名：{0} 进行授权", strUserName), "DoSWeHelpWork", true);

                    ret = CWeHelpAnalysis(dbpath, strUserName, strAliasName, intOnlyContact);

                    //ret = StartWeHelpWork(dbPtr, dbbuf.Length, offset, contactPtr,
                    //                      contactbuf.Length, strUserName, strAliasName, intOnlyContact,
                    //                      backupkey_callback, contact_callback, log_callback);
                }
                catch (Exception e)
                {
                    Common.LogException(e.ToString(), "StartWeHelpWork__1");
                    ret = 20;
                }
                bool isBackupkey = false;
                if (intOnlyContact == 0)
                    isBackupkey = HtmlHelper.iWechatAssistant_Backupkey(this.mStrBackupkeyJson); //上传密钥

                //上传好友
                bool isFriendFile = false;
                //Common.Log(string.Format("DoSWeHelpWork:{0}", this.mStrContactPath));
                if (!File.Exists(this.mStrContactPath))
                    Common.LogException("好友列表异常-不存在", string.Format("{0}发现错误", strText));
                else
                {
                    isFriendFile = HtmlHelper.iWechatAssistant_FriendFile(this.mStrContactPath, this.mCurrentWAInfo.Account);
                    if (!isFriendFile)
                        isFriendFile = HtmlHelper.iWechatAssistant_FriendFile(this.mStrContactPath, this.mCurrentWAInfo.InnerAccount);

                    try
                    {
                        File.Copy(this.mStrContactPath, Path.Combine(this.mCurrentWAInfo.PathOnPC, @"Assistant\Friend.txt"), true);
                    }
                    catch { }
                }

                string msg = "";
                bool isFriend = false;
                switch (ret)
                {
                    case 0:
                        msg = string.Format("{0}成功。", strText);
                        if (!isBackupkey && intOnlyContact == 0)
                            msg += "密钥异常。";

                        if (!isFriendFile)
                            msg += "好友列表异常。";
                        else
                            isFriend = true;
                        break;

                    case 1://打开联系人数据库失败
                        msg = "数据库打开失败，\r\n请重启电脑端微信（错误-1）";
                        break;

                    case 2://获取联系人数据库密钥出现异常
                        msg = "解密异常，\r\n请重启电脑端微信（错误-2）";
                        break;

                    case 3://获取联系人数据库密钥为空
                        msg = "解密异常为空，\r\n请重启电脑端微信（错误-3）";
                        break;

                    case 4://查询联系人信息出现异常
                        msg = "查询联系人信息出现异常，\r\n请重启电脑端微信（错误-4)";
                        break;

                    case 5://查询联系人信息发生错误
                        msg = "查询联系人信息出现异常，\r\n请重启电脑端微信(错误-5)";
                        break;

                    case 6://dll客户端窗口未建立
                        msg = "请重新授权，\n\r如果多次出现请重启微信备份助手。(错误-6)";
                        break;

                    case 7://调用者不符合
                        msg = "未知错误（错误-7）";
                        break;

                    case 8://微信窗口没找到，微信未登录
                        msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-8）";
                        break;

                    case 9://设置hook失败
                        msg = "请重新授权，\n\r如果多次出现请重启微信备份助手。(错误-9)";
                        break;

                    case 10://微信帮助窗口未找到
                        msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-10）";
                        break;

                    case 11://获取备份密钥出错
                        msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-11）";
                        break;

                    case 12://微信偏移信息为空
                        msg = "重启微信备份助手后重试（错误-12）";
                        break;

                    case 13://偏移base64解密失败
                        msg = "请重启微信备份助手后重试，\n\r如果多次失败请联系客服处理。（错误-13）";
                        break;

                    case 14://偏移json数据分析失败
                        msg = "请重启微信备份助手后重试，\n\r如果多次失败请联系客服处理。（错误-14）";
                        break;

                    case 15://偏移数量出错
                        msg = "请重启微信备份助手后重试，\n\r如果多次失败请联系客服处理。（错误-15）";
                        break;

                    case 16://微信版本不支持
                    case 17:
                        if (ret == 16)
                            msg = mStrVerMeaage.Length > 0 ? mStrVerMeaage : "微信PC版本暂不兼容。（错误-16）";
                        else
                            msg = "请升级电脑端微信至最新版。（错误-17）";

                        if (!string.IsNullOrEmpty(mStrUserVer) && mIsSendVer)
                        {
                            //上传版本预警信息
                            string strUrl = "http://pc2.api.tongbu.com/vip/v.html?t=7302";
                            JsonObject jObject = new JsonObject();
                            jObject.Add("version", mStrUserVer);
                            string contents = JsonParser.SaveString(jObject);
                            contents = Utility.EncryptDES(contents, "em031#qw", "3e4i#ek3").Replace("+", "_@_");
                            string strResult = Utility.PostData(strUrl, contents, 20000);
                            strResult = Utility.DecryptDES(strResult.Replace("_@_", "+"), "em031#qw", "3e4i#ek3");
                        }
                        //msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-25）";
                        break;

                    //case 17://获取微信版本失败
                    //    msg = "请升级微信电脑端至最新版。（错误-17）";
                    //    break;

                    case 18://MicroMsgDB路径错误
                        msg = "请重启微信备份助手后重试，\n\r如果多次失败请联系客服处理。（错误-18）";
                        break;

                    case 19://微信偏移初始化错误
                        msg = "请重启微信备份助手后重试，\n\r如果多次失败请联系客服处理。（错误-19）";
                        break;

                    case 20://未知异常
                        {
                            //判断文件是否存在
                            string path = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "IncludeDlls", "CWehelp_x86", "CWeHelp.dll");
                            string path1 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "IncludeDlls", "CWehelp_x64", "CWeHelp.dll");
                            string path2 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "IncludeDlls", "CWehelp_x86", "tbCmd40.exe");
                            string path3 = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "IncludeDlls", "CWehelp_x64", "tbCmd64.exe");

                            if (!File.Exists(path) || !File.Exists(path1) || !File.Exists(path2) || !File.Exists(path3))
                            {
                                msg = "程序文件丢失，请关闭其他软件文件防护跟系统防火墙防护后，重新安装软件，再进行授权。（错误-20）";
                                break;
                            }

                            msg = "未知异常。（错误-20）";
                        }
                        break;

                    case 21://微信帮助窗口异常
                        msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-21）";
                        break;

                    case 22://dll窗口异常
                        msg = "请重启微信备份助手后重试。（错误-22）";
                        break;

                    case 23://dll窗口注册失败
                        msg = "请重启微信备份助手后重试。（错误-23）";
                        break;

                    case 24://创建微信帮助窗口失败
                        msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-24）";
                        break;

                    case 25://微信帮助窗口注册失败
                        msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-25）";
                        break;

                    case 26://获取微信版本信息失败
                        msg = "请重试，如果多次异常请联系客服。（错误-26）";
                        break;

                    case 27://查找微信进程信息失败
                        msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-27）";
                        break;

                    case 28://联系人文件创建失败
                        msg = "请重试，如果多次异常请联系客服。（错误-28）";
                        break;

                    case 29://获取登录账号错误
                        msg = "请打开或重启电脑端微信，\n\r并登录需要授权的帐号后重试（错误-29）";
                        break;

                    case 30: //账号错误
                        msg = "当前查看账号与电脑端微信登入的帐号不一致。\n\r请检查电脑端微信是否多开（错误-30）";
                        break;

                    default:
                        msg = "未知错误。";
                        break;
                }

                //授权结果收集
                if (intOnlyContact == 0)
                {
                    if (ret == 0)
                    {
                        //成功
                        Utility.PostData("https://at.umeng.com/imeSvq?cid=480", "", 2000);

                        if (!isBackupkey)
                            //密钥异常
                            Utility.PostData("https://at.umeng.com/0Hj0by?cid=6768", "", 2000);

                        if (!isFriendFile)
                            //好友列表异常
                            Utility.PostData("https://at.umeng.com/qeGbii?cid=481", "", 2000);
                    }
                    else
                        //失败
                        Utility.PostData("https://at.umeng.com/5na0LD?cid=6768", "", 2000);
                }

                SelfAccreditEventArgs args = new SelfAccreditEventArgs(ret, msg, this.mStrBackupkey, this.mStrContactPath, true);
                args.IsGetFriend = isFriend;
                this.OnSelfAccreditHandler(args);

                log_callback(msg, msg.Length);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoSWeHelpWork");
            }
        }

        /// <summary>
        /// CWehelp解析
        /// </summary>
        /// <param name="dbpath"></param>
        /// <param name="offset"></param>
        /// <param name="strUserName"></param>
        /// <param name="strAliasName"></param>
        /// <param name="intOnlyContact"></param>
        /// <returns></returns>
        private int CWeHelpAnalysis(string dbpath, string strUserName, string strAliasName, int intOnlyContact)
        {
            string offset = string.Empty;
            string ver = string.Empty;
            //RegistryKey installerProductsReg = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\WeChat");

            string iniPath = string.Empty;
            string cmdPath = string.Empty;

            int ret;
            bool is64 = false;

            //if (installerProductsReg != null)
            //{
            //string installLocation = installerProductsReg.GetValue("InstallLocation") as string;

            var pro = System.Diagnostics.Process.GetProcessesByName("WeChat");
            if (pro == null || pro.Count() == 0)
            {
                ret = 27;
                goto DoExit;
            }

            is64 = WechatHelp.CheckIs64();

            //if (Environment.Is64BitOperatingSystem)
            //{
            //    Common.Log(string.Format("Current os is 64"), "DoSWeHelpWork", true);

            //    bool retVal = false;

            //    NativeMethods.IsWow64Process(pro[0].Handle, out retVal);

            //    if (retVal == true)
            //    {
            //        Common.Log(string.Format("Current Wechat is 32"), "DoSWeHelpWork", true);
            //        is64 = false;
            //    }
            //    else
            //    {
            //        Common.Log(string.Format("Current Wechat is 64"), "DoSWeHelpWork", true);
            //        is64 = true;
            //    }
            //}
            //else
            //{
            //    Common.Log(string.Format("Current os is 32"), "DoSWeHelpWork", true);
            //    is64 = false;
            //}
            //}
            //else
            //{
            //    ret = 27;
            //    goto DoExit;
            //}

            offset = GetOffset(is64);

            if (is64)
            {
                iniPath = Path.Combine(Folder.AppFolder, "IncludeDlls", "CWehelp_x64", "iWechatAssistant.ini");
                cmdPath = Path.Combine(Folder.AppFolder, "IncludeDlls", "CWehelp_x64", "tbCmd64.exe");
            }
            else
            {
                iniPath = Path.Combine(Folder.AppFolder, "IncludeDlls", "CWehelp_x86", "iWechatAssistant.ini");
                cmdPath = Path.Combine(Folder.AppFolder, "IncludeDlls", "CWehelp_x86", "tbCmd40.exe");
            }

            if (File.Exists(iniPath))
                File.Delete(iniPath);

            Common.CallCmdExe(new string[] { "iWechatAssistant40", this.mCurrentWAInfo.Account, this.mCurrentWAInfo.Name, dbpath, offset, mContactPath, strUserName, strAliasName, intOnlyContact.ToString() },
                0, pathExe: cmdPath);

            backupkey_callback(IniClass.GetIniSectionKey(this.mCurrentWAInfo.Account, "pwd", iniPath), 0);

            mStrContactPath = IniClass.GetIniSectionKey(this.mCurrentWAInfo.Account, "mStrContactPath", iniPath);

            string log = IniClass.GetIniSectionKey(this.mCurrentWAInfo.Account, "log", iniPath);
            if (!string.IsNullOrEmpty(log))
            {
                log_callback(log, 0);
            }

            ret = int.Parse(IniClass.GetIniSectionKey(this.mCurrentWAInfo.Account, "ret", iniPath));
        DoExit:
            return ret;

        }

        private string GetOffset(bool is64)
        {
            string strContent = "";
            try
            {
                string strOffsetPath = Path.Combine(this.mWechatPath, @"All Users\encOffset.txt");
                string strTempPath = string.Format("{0}.tmp", strOffsetPath);

                //bool isDownload = false;
                //if (File.Exists(strOffsetPath))
                //{
                //    //如果文件存在比较创建时间 
                //    if (new FileInfo(strOffsetPath).LastWriteTime.Date != DateTime.Now.Date)
                //        isDownload = true;
                //}
                //else
                //    isDownload = true;

                ////一天下载一次 保存到本地
                //if (isDownload)
                //{

                string url = "http://pc2.api.tongbu.com/vip/offset/encOffset.aspx";

                if (!is64)
                    url = "http://pc2.api.tongbu.com/vip/offset/encoffset32.aspx";

                int intTry = 0;
                strContent = Utility.GetContentStringFromUrl(url, Encoding.UTF8, 20000);
                while (string.IsNullOrEmpty(strContent) && intTry < 3)
                {
                    Utility.WaitSeconds(0.5);
                    strContent = Utility.GetContentStringFromUrl(url, Encoding.UTF8, 20000);
                    intTry += 1;
                }
                if (!string.IsNullOrEmpty(strContent))
                {
                    using (StreamWriter sWriter = new StreamWriter(strTempPath, false, Encoding.UTF8))
                    {
                        sWriter.Write(strContent);
                    }

                    if (File.Exists(strTempPath))
                    {
                        try
                        {
                            File.Delete(strOffsetPath);
                        }
                        catch { }
                        try
                        {
                            File.Move(strTempPath, strOffsetPath);
                        }
                        catch { }
                    }
                }



                //}
                //else
                //{
                strContent = File.ReadAllText(strOffsetPath, Encoding.UTF8);
                //}
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetOffset");
            }
            return strContent;
        }

        #endregion

        private void OnSelfAccreditHandler(SelfAccreditEventArgs e)
        {
            if (SelfAccreditHandler != null)
                SelfAccreditHandler(null, e);
        }
    }

    public class SelfAccreditEventArgs : EventArgs
    {

        private int _IntCode = 0;

        public int IntCode
        {
            get { return _IntCode; }
            set { _IntCode = value; }
        }

        private string _StrMessage = "";

        public string StrMessage
        {
            get { return _StrMessage; }
            set { _StrMessage = value; }
        }

        private bool _IsOver = false;

        public bool IsOver
        {
            get { return _IsOver; }
            set { _IsOver = value; }
        }

        private string _StrBackupkey = "";

        public string StrBackupkey
        {
            get { return _StrBackupkey; }
            set { _StrBackupkey = value; }
        }

        private string _StrContactPath = "";

        public string StrContactPath
        {
            get { return _StrContactPath; }
            set { _StrContactPath = value; }
        }

        private bool _IsGetFriend;

        public bool IsGetFriend
        {
            get { return _IsGetFriend; }
            set { _IsGetFriend = value; }
        }

        public SelfAccreditEventArgs(int code, string msg, string key, string cpath, bool over = false)
        {
            this._IntCode = code;
            this._StrMessage = msg;
            this._IsOver = over;
            this._StrBackupkey = key;
            this._StrContactPath = cpath;
        }
    }
}
