﻿<?xml version="1.0" encoding="utf-8"?>
<Project xmlns="http://schemas.microsoft.com/developer/msbuild/2003" DefaultTargets="Build" ToolsVersion="12.0">
  <Import Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E93BD7F5-06CA-4763-9663-EE02E1588115}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>iWechatAssistant</RootNamespace>
    <AssemblyName>iWechatAssistant</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;NET46;</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <WarningLevel>2</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE;NET46;</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DefineConstants>TRACE;DEBUG;NET46;</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE;NET46;</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Resources\main.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="CefSharp, Version=49.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Cef\4.6\CefSharp.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp.Controls, Version=49.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Cef\4.6\CefSharp.Controls.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp.Core, Version=49.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Cef\4.6\CefSharp.Core.dll</HintPath>
    </Reference>
    <Reference Include="ComputerInfor, Version=1.0.8704.26423, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\ComputerInfor.dll</HintPath>
    </Reference>
    <Reference Include="CoreCefSharp, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\DllImport\x86\Cef\4.6\CoreCefSharp.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.5.1.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>IncludeDlls\x86\Google.Protobuf.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.SQLite, Version=1.0.116.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\CoreMisc\IncludeDlls\x86\4.6\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Design" />
    <Reference Include="System.Management" />
    <Reference Include="System.Web" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="NativeMethods.cs" />
    <Compile Include="WechatHelp.cs" />
    <Compile Include="zComponents\tbPage.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="zComponents\tbPage.Designer.cs">
      <DependentUpon>tbPage.cs</DependentUpon>
    </Compile>
    <Compile Include="zExport\ExportHelper.cs" />
    <Compile Include="zAccredit\frmAccredit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zAccredit\frmAccredit.Designer.cs">
      <DependentUpon>frmAccredit.cs</DependentUpon>
    </Compile>
    <Compile Include="zCharge\frmCharge.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zCharge\frmCharge.Designer.cs">
      <DependentUpon>frmCharge.cs</DependentUpon>
    </Compile>
    <Compile Include="zExport\MatchHelper.cs" />
    <Compile Include="zSetting\AccountIniSetting.cs" />
    <Compile Include="zSetting\frmFeedback.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\frmFeedback.Designer.cs">
      <DependentUpon>frmFeedback.cs</DependentUpon>
    </Compile>
    <Compile Include="zSetting\frmService.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\frmService.Designer.cs">
      <DependentUpon>frmService.cs</DependentUpon>
    </Compile>
    <Compile Include="zSetting\frmSetting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="zSetting\frmSetting.Designer.cs">
      <DependentUpon>frmSetting.cs</DependentUpon>
    </Compile>
    <Compile Include="zExport\IMHtmlNodeHelper.cs" />
    <Compile Include="zLiveUpdate\LiveUpdateHelper.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ProtobufClass\BackConfigMast.cs" />
    <Compile Include="ProtobufClass\WechatAccMast.cs" />
    <Compile Include="ProtobufClass\WechatMessageMast.cs" />
    <Compile Include="ProtobufClass\WxGroupRoomData.cs" />
    <Compile Include="zAccredit\SelfAccreditHelper.cs" />
    <Compile Include="zWechatHepler\WechatAccInfo.cs" />
    <Compile Include="zWechatHepler\WechatHeplerDB.cs" />
    <Compile Include="zWechatHepler\WechatHeplerPC.cs" />
    <Compile Include="zWechatTalk\WeChatMsgWebPage.cs" />
    <Compile Include="zWechatTalk\WechatTalk.cs" />
    <Compile Include="zWechatTalk\WeChatTalkDB.cs" />
    <Compile Include="zComponents\tbBattery.cs">
      <SubType>Component</SubType>
    </Compile>
    <EmbeddedResource Include="zAccredit\frmAccredit.resx">
      <DependentUpon>frmAccredit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zCharge\frmCharge.resx">
      <DependentUpon>frmCharge.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zComponents\tbPage.resx">
      <DependentUpon>tbPage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmFeedback.resx">
      <DependentUpon>frmFeedback.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmService.resx">
      <DependentUpon>frmService.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="zSetting\frmSetting.resx">
      <DependentUpon>frmSetting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="Lang\zh-CN.lang">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Resources\ExportPreview.zip" />
    <None Include="Resources\weixin_dll_aud" />
    <None Include="Resources\weixin_zip_ChatWebPage.zip" />
    <None Include="Resources\weixin_zip_exporthtml.zip" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\dcf.dll" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\gif_loading_16.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\gif_loading_231.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\weixin_icon_default.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\weixin_dll_silk.dll" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\excel_header.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\main.ico" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_close.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_max.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_min.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\frm_bg_sub1.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_retreatex.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_blueleft.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_blueright.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_ddl_selecttype.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_blue.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\gif_loading_32.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_3_Video.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_3_Contact.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\gif_loading_24.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_3_setting.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_4_timescreen.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\TimeScreenUp.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\TimeScreenUpDowm.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\previouspage.png" />
    <None Include="Resources\nextpage.png" />
    <None Include="Resources\homepage.png" />
    <None Include="Resources\trailerpage.png" />
    <None Include="Resources\cell_question.png" />
    <None Include="Resources\cell_false.png" />
    <None Include="Resources\cell_true.png" />
    <None Include="Resources\btn_feedback.png" />
    <None Include="Resources\icon_yuyinhecheng.png" />
    <None Include="Resources\detail.css" />
    <None Include="Resources\icon_export.png" />
    <None Include="Resources\btn_3_more.png" />
    <None Include="Resources\btn_4_white.png" />
    <None Include="Resources\3.png" />
    <None Include="Resources\2.png" />
    <None Include="Resources\1.png" />
    <None Include="Resources\dunpai.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\QQNum.png" />
    <None Include="Resources\icon_qq_group.png" />
    <None Include="Resources\btn_copy.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\AppUnion\AppUnion46.csproj">
      <Project>{e340f460-3005-4abd-b511-a8aa4d749f68}</Project>
      <Name>AppUnion46</Name>
    </ProjectReference>
    <ProjectReference Include="..\Components\Components46.vbproj">
      <Project>{f9779807-fdba-4be9-9da2-3746bb24597b}</Project>
      <Name>Components46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc46.csproj">
      <Project>{596775d3-3eea-4125-bac2-934df77fbeba}</Project>
      <Name>CoreMisc46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreModuleEx\CoreModuleCS46.csproj">
      <Project>{2435928b-bf66-4791-b976-aa373477fbfe}</Project>
      <Name>CoreModuleCS46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreModule\CoreModule46.vbproj">
      <Project>{722e0eb6-5dc6-4672-bf79-7e33a3008135}</Project>
      <Name>CoreModule46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses46.csproj">
      <Project>{a5844815-737d-486e-b7de-d57262f9e5f4}</Project>
      <Name>CoreReses46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag46.csproj">
      <Project>{7f12e495-c239-4b39-8156-aff1ad7a95b6}</Project>
      <Name>CoreTag46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS46.csproj">
      <Project>{c0b96f67-6997-454c-a762-470f156addf4}</Project>
      <Name>CoreUtilCS46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil46.vbproj">
      <Project>{0c859628-5952-44d7-8c32-ab838ef37ede}</Project>
      <Name>CoreUtil46</Name>
    </ProjectReference>
    <ProjectReference Include="..\iCloudeRescue\ProtocolBuffers\ProtocolBuffers46.csproj">
      <Project>{2b7f60aa-56c2-451d-a879-679696e2a4f6}</Project>
      <Name>ProtocolBuffers46</Name>
    </ProjectReference>
    <ProjectReference Include="..\iPhoneCS\iPhone46.csproj">
      <Project>{6939a9e2-8f07-4834-9403-2cfb06ec466e}</Project>
      <Name>iPhone46</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf46.csproj">
      <Project>{3fd45453-6069-4dd0-bbf0-17b25606e359}</Project>
      <Name>ProtoBuf46</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\ICSharpCode.SharpZLib46.csproj">
      <Project>{786969bf-d271-49a4-a98e-5affa29e2a55}</Project>
      <Name>ICSharpCode.SharpZLib46</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>copy $(ProjectDir)IncludeDlls\CWehelp_$(PlatformName) $(TargetDir)

copy $(SolutionDir)DllImport\$(PlatformName) $(TargetDir)
copy $(SolutionDir)DllImport\$(PlatformName)\Cef $(TargetDir)
copy $(SolutionDir)DllImport\$(PlatformName)\Cef\4.6 $(TargetDir)

</PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
</Project>