﻿using iTong.CoreFoundation;
using iTong.CoreModule;
using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite3;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Windows.Forms;
using System.Xml;
using System.Linq;

namespace iWechatAssistant
{
    public class WechatHeplerPC
    {
        [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
        private delegate int DecodeDBHandler(string strPwd, string strSrcPath, string strDecPath);       //解密数据库的方法，动态调整外部dll文件
        private static int mUTC2LocalTime = TimeZone.CurrentTimeZone.GetUtcOffset(DateTime.Now).Hours;
        private static DecodeDBHandler mDBDecoder;
        public static event EventHandler<ActivateAccountEventArgs> ActivateAccountHandler;
        public static event EventHandler<EventArgs> LoadServerSettingHandler;
        public static event EventHandler<WechatAccountEventArgs> GetWechatAccountFromPCHandler;
        private string mStrWeChatFiles = "";
        private string mStrAccountPwd = "";
        private static Thread mTDActivateAccount;
        private static Thread mTDGetAccountLoginDevice;

        private static readonly string mStrKey = "em031#qw";
        private static readonly string mStrIV = "3e4i#ek3";

        public static AssistInfo mAssistInfo = new AssistInfo();

        public static List<WechatAccInfo> mLstWechatAccounts = new List<WechatAccInfo>();

        #region "初始化"

        public WechatHeplerPC(string strWeChatFiles, string strAccountPwd)
        {
            this.mStrWeChatFiles = strWeChatFiles;
            this.mStrAccountPwd = strAccountPwd;

            ////初始化解密dll
            this.InitDll();

            InitEnvironmentPath();
            ReleaseAudDll();
            ReleaseSilkDll();
        }

        private void InitDll()
        {
            try
            {
                byte[] arrLibrary = global::iWechatAssistant.Properties.Resources.dcf;
                IntPtr iLibrary = MemoryLibrary.MemoryLoadLibrary(arrLibrary);
                if (iLibrary == IntPtr.Zero)
                {
                    Common.LogException(string.Format("WechatHeplerPC.InitFunc Library Value:{0}", iLibrary));
                }

                IntPtr funcAdrress = IntPtr.Zero;
                //Init
                funcAdrress = MemoryLibrary.MemoryGetProcAddress(iLibrary, "bk_d");
                mDBDecoder = Marshal.GetDelegateForFunctionPointer(funcAdrress, typeof(DecodeDBHandler)) as DecodeDBHandler;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHelperPC_Init_Error");
            }
        }

        #endregion

        #region "--- 单例 ---"
        //单一例，一个设备返回一个实例
        private static Dictionary<string, WechatHeplerPC> mDictInstances = new Dictionary<string, WechatHeplerPC>(StringComparer.InvariantCultureIgnoreCase);
        // 检测冗余的调用
        private bool disposedValue = false;

        private static object _lockGetInstance = new object();

        public static WechatHeplerPC GetInstance(string strWeChatFiles, string strAccountPwd)
        {
            WechatHeplerPC helper = null;
            bool blnRetry = false;
            string strKey = string.Format("{0};{1}", strWeChatFiles, strAccountPwd);
            lock (_lockGetInstance)
            {
                DO_RETRY:
                try
                {
                    if (!mDictInstances.ContainsKey(strKey))
                    {
                        mDictInstances.Add(strKey, new WechatHeplerPC(strWeChatFiles, strAccountPwd));
                    }
                    helper = mDictInstances[strKey];
                }
                catch (Exception)
                {
                    if (!blnRetry)
                    {
                        blnRetry = true;
                        goto DO_RETRY;
                    }
                }
            }
            return helper;
        }

        #endregion

        #region "--- 释放 ---"

        public void Dispose()
        {
            // 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    // TODO: 显式调用时释放非托管资源
                    mDictInstances.Clear();

                    //try
                    //{
                    //    if (this.mThreadExportMedia != null && this.mThreadExportMedia.ThreadState != ThreadState.Stopped)
                    //    {
                    //        this.mThreadExportMedia.Abort();
                    //    }
                    //}
                    //catch
                    //{
                    //}

                }
            }
            disposedValue = true;
        }

        #endregion

        #region "--- 取得wx目录下的账号配制信息 ---"

        //1.通过配制信息取得账号信息和备份文件路径
        //2.通过取和的账号信息去服务器匹配相应的密钥
        public static void GetWechatAccountFromPC(string strWechatFiles)
        {
            mLstWechatAccounts = new List<WechatAccInfo>();
            try
            {
                if (string.IsNullOrEmpty(strWechatFiles) || !Directory.Exists(strWechatFiles))
                    return;

                //取得账号配制信息 在 accInfo.dat
                string[] arrDir = Directory.GetDirectories(strWechatFiles);

                int index = 0;
                foreach (string strDir in arrDir)
                {
                    try
                    {
                        string dirBackup = Path.Combine(strDir, "BackupFiles");
                        string fileAccInfo = Path.Combine(strDir, @"config\AccInfo.dat");
                        string fileAconfig = Path.Combine(strDir, @"config\aconfig.dat");

                        if (!Directory.Exists(dirBackup) && !File.Exists(fileAccInfo) && !File.Exists(fileAconfig))
                            continue;

                        string strBackupFilePath = string.Empty;
                        string strAconfigdat = Path.Combine(strDir, @"config\aconfig.dat"); // item.Replace("AccInfo.dat", "aconfig.dat");
                        string strPCPath = strDir;// item.Replace(@"\config\AccInfo.dat", "");

                        index++;
                        if (GetWechatAccountFromPCHandler != null)
                            //GetWechatAccountFromPCHandler(null, new WechatAccountEventArgs(string.Format("解析备份数据，请耐心等待... {0}/{1}", index, arrfiles.Length)));
                            GetWechatAccountFromPCHandler(null, new WechatAccountEventArgs("解析备份数据，请耐心等待... "));

                        if (File.Exists(strAconfigdat))
                        {
                            byte[] bt = File.ReadAllBytes(strAconfigdat);
                            using (MemoryStream ms = new MemoryStream(bt))
                            {
                                //ms.Write(bt, 0, bt.Length);
                                //ms.Position = 0;
                                WechatBackupFilesMast info = ProtoBuf.Serializer.Deserialize<WechatBackupFilesMast>(ms);

                                if (info != null && info.LstWCBFiles != null && info.LstWCBFiles.Count > 0)
                                {
                                    foreach (WechatBackupFilesItem witem in info.LstWCBFiles)
                                    {
                                        if (witem.WCType == 325)
                                        {
                                            strBackupFilePath = witem.WCValue;
                                            break;
                                        }
                                    }
                                }
                            }
                        }

                        byte[] bugger = File.ReadAllBytes(fileAccInfo);
                        using (MemoryStream ms = new MemoryStream(bugger))
                        {
                            //ms.Write(bugger, 0, bugger.Length);
                            //ms.Position = 0;
                            WechatAccMast info = ProtoBuf.Serializer.Deserialize<WechatAccMast>(ms);
                            if (info != null)
                                mLstWechatAccounts.Add(GetAccountInfo(info, strWechatFiles, strBackupFilePath, strPCPath));
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException("读取" + strDir + "失败", "WechatHeplerPC_LoadDBFile");
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHeplerPC_LoadDBFile");
            }
            //GetBackupInfoByComputer(mLstWechatAccounts, strWechatFiles, ref  strErrorMsg);
        }

        private static bool CheckBackupFolder()
        {
            bool isAccountFolder = false;


            return isAccountFolder;
        }


        private static WechatAccInfo GetAccountInfo(WechatAccMast infoMast, string strFolder, string strBackupFilePath, string strPCPath)
        {
            WechatAccInfo info = new WechatAccInfo();
            foreach (WechatAccItem item in infoMast.WechatAccList)
            {
                if (item.WechatAccType == 4)
                {
                    // wx号
                    info.Account = item.WechatAccValue;
                }
                if (item.WechatAccType == 64)
                {
                    // wx内部号
                    info.InnerAccount = item.WechatAccValue;
                }
                if (item.WechatAccType == 10)
                {
                    // wx名称
                    info.Name = item.WechatAccValue;
                }
                if (item.WechatAccType == 102)
                {
                    // wx头像地址
                    info.IconUrl = item.WechatAccValue;
                }
                //if (item.WechatAccType == 52)
                //{
                //    // 个性签名
                //}
            }
            info.StrBackupFilesPath = strBackupFilePath;

            info.PathOnPC = strPCPath;

            bool bIsDebug = false;
            try
            {
                bIsDebug = File.Exists(Path.Combine(Folder.AppFolder, "debug.dll"));
            }
            catch { }

            Common.Log(string.Format("wx号信息===>Account:{0}  InnerAccount:{1} Name:{2}",
                        info.Account, info.InnerAccount, info.Name), bIsDebug);

            ////取得在电脑中的路径
            //if (string.IsNullOrEmpty(info.InnerAccount))
            //    info.PathOnPC = Path.Combine(strFolder, info.Account);
            //else
            //{
            //    if (strFolder.EndsWith(info.InnerAccount))
            //        info.PathOnPC = strFolder;
            //    else
            //        info.PathOnPC = Path.Combine(strFolder, info.InnerAccount);
            //}

            //if (!Directory.Exists(info.PathOnPC))
            //{
            //    info.PathOnPC = Path.Combine(strFolder, info.Account);
            //}

            return info;
        }

        #endregion

        public void DailyLiving(string strWxid, ActivateState astate)
        {
            try
            {
                if (string.IsNullOrEmpty(strWxid))
                    return;
                JsonObject jsonObj = new JsonObject();
                jsonObj.Add("projectid", "6");
                jsonObj.Add("macid", Common.GetComputerID());
                jsonObj.Add("macidnew", Common.GetComputerIDNew());
                jsonObj.Add("weixin", strWxid);
                jsonObj.Add("isvip", astate == ActivateState.Normal ? 1 : 0);

                JsonWriter writer = new JsonWriter();
                jsonObj.Write(writer);
                string strPostJson = writer.ToString();
                strPostJson = Utility.EncryptDES(strPostJson, "em031#qw", "3e4i#ek3");
                string strURLForPwd = "http://pc.api.tongbu.com/vip/v.html?t=7701&projectid=6";
                string strResult = Utility.PostData(strURLForPwd, strPostJson, 20000);
                strResult = strResult.Replace("_@_", "+");
                strResult = Utility.DecryptDES(strResult, "em031#qw", "3e4i#ek3");

                Common.Log(strResult, "DailyLiving", true);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DailyLiving");
            }
        }

        #region "--- 服务器获取当前电脑已经激活的wx号列表 ---"

        // 添加新的电脑信息
        private static JsonObject GetComputerInformation()
        {
            JsonObject ciJsons = new JsonObject();
            try
            {
                string fname = Path.Combine(Folder.LogFolder, "Log" + DateTime.Now.ToString("yyyyMMdd")) + ".txt";
                ComputerInfor ci = new ComputerInfor(fname);

                ciJsons.Add("cpuid", ci.cpuid);
                ciJsons.Add("ipaddr", ci.IPAddress);
                ciJsons.Add("mainboard", ci.MainBoardInfor);
                ciJsons.Add("macaddr", ci.MacAddress);
                ciJsons.Add("os", ci.SystemVersion);
                ciJsons.Add("sysdisk", ci.SysDiskSerial);
                ciJsons.Add("systemuuid", ci.SystemUUID);
                ciJsons.Add("diskfilesystem", ci.DiskFileSystem);
                ciJsons.Add("bootdisk", ci.BootDisk);
                ciJsons.Add("memorychip", ci.MemoryChip);
                ciJsons.Add("bios", ci.Bios);

                JsonArray arySerials = new JsonArray();
                for (int i = 0; i < ci.DiskSerial.Count; i++)
                {
                    string disk = string.Format("{0}:{1}", ci.DiskType[i], ci.DiskSerial[i]);
                    arySerials.Add(disk);
                }
                ciJsons.Add("serials", arySerials);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "C++'s GetComputerInformation");
            }

            return ciJsons;
        }
        public static void GetBackupInfoByComputer(List<WechatAccInfo> lstWechatAccounts, string strWechatFiles, ref string strErrorMsg)
        {
            try
            {
                if (GetWechatAccountFromPCHandler != null)
                    GetWechatAccountFromPCHandler(null, new WechatAccountEventArgs("获取激活授权信息，请耐心等待..."));

                string strPostJson = "";
                JsonObject jsonObj = new JsonObject();
                jsonObj.Add("projectid", "6");
                jsonObj.Add("macid", Common.GetComputerID());
                jsonObj.Add("macidnew", Common.GetComputerIDNew());
                jsonObj.Add("macudid", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddress().Trim()));
                jsonObj.Add("macudidnew", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddressFixed().Trim()));
                jsonObj.Add("sn", Common.GetHardDiskID2());
                jsonObj.Add("version", System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString(4));
                jsonObj.Add("channel", Common.GetChannelID());

                JsonArray arr = new JsonArray();
                if (lstWechatAccounts != null && lstWechatAccounts.Count > 0)
                {
                    foreach (WechatAccInfo item in lstWechatAccounts)
                    {
                        if (!string.IsNullOrEmpty(item.Account))
                            arr.Add(item.Account);
                        else
                            arr.Add(item.InnerAccount);
                    }
                }
                jsonObj.Add("wechatlist", arr);

                ////////////////////////////////////////////////
                // 使用C++库读取电脑信息
                //JsonObject ci = GetComputerInformation();
                //if (ci.Count > 0)
                //{
                //    jsonObj.Add("newkeys", ci);
                //}
                ///////////////////////////////////////////////

                JsonWriter writer = new JsonWriter();
                jsonObj.Write(writer);
                strPostJson = writer.ToString();

                //Common.Log("Before Posting:" + strPostJson);

                strPostJson = Utility.EncryptDES(strPostJson, mStrKey, mStrIV);

                //Common.Log("Q:" + strPostJson);

                string strURLForPwd = "http://pc.api.tongbu.com/vip/v.html?t=77&projectid=6";
                string strResult = Utility.PostData(strURLForPwd, strPostJson, 20000);
                strResult = strResult.Replace("_@_", "+");
                //Common.Log("Q:" + strResult);
                strResult = Utility.DecryptDES(strResult, mStrKey, mStrIV);

                int intTryCount = 0;
                while (string.IsNullOrEmpty(strResult) && intTryCount <= 3)
                {
                    Utility.WaitSeconds(0.5);
                    strResult = Utility.PostData(strURLForPwd, strPostJson, 20000);
                    strResult = strResult.Replace("_@_", "+");
                    strResult = Utility.DecryptDES(strResult, mStrKey, mStrIV);
                    intTryCount += 1;
                }

                //---------------------------------------------------------------------------------------------------
                //访问服务器异常 可以容错10次 联网成功后 次数清空
                //每次访问成功后 服务器获取的数据加密保存到本地 如果失败且在容错次数内 直接取本地的数据；  容错的信息 次数加密保存到本地
                //如果取不到容错信息 或者 最后一次 保存本地的信息 则提示 网络异常
                //容错次数超过后 提示网络异常                
                string strAssistantPath = Path.Combine(strWechatFiles, "Assistant");
                Folder.CheckFolder(strAssistantPath);

                string strTryCountFilePath = Path.Combine(strAssistantPath, "configuration");
                string strContentFilePath = Path.Combine(strAssistantPath, "configurationEx");

                string strK = "sn812#qw";
                string strV = "8c1z#ek3";

                if (string.IsNullOrEmpty(strResult))
                {
                    bool isErr = true;
                    try
                    {
                        if (File.Exists(strTryCountFilePath) && File.Exists(strContentFilePath))
                        {
                            int intCount = Convert.ToInt32(Utility.DecryptDES(File.ReadAllText(strTryCountFilePath), strK, strV));
                            if (intCount < 10)
                            {
                                strResult = Utility.DecryptDES(File.ReadAllText(strContentFilePath), strK, strV);

                                using (FileStream objWriter = new FileStream(strTryCountFilePath, FileMode.OpenOrCreate, FileAccess.Write))
                                {
                                    intCount++;
                                    string strTryCount = Utility.EncryptDES(intCount + "", strK, strV);
                                    byte[] bt = System.Text.Encoding.Default.GetBytes(strTryCount);
                                    objWriter.Write(bt, 0, bt.Length);
                                }
                                isErr = false;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "GetBackupInfoByComputer容错");
                    }

                    if (isErr)
                    {
                        strErrorMsg = "服务器访问异常，请检查网络连接后重启该程序。";
                        return;
                    }
                }
                else
                {
                    try
                    {
                        using (FileStream objWriter = new FileStream(strTryCountFilePath, FileMode.OpenOrCreate, FileAccess.Write))
                        {
                            string strTryCount = Utility.EncryptDES("0", strK, strV);
                            byte[] bt = System.Text.Encoding.Default.GetBytes(strTryCount);
                            objWriter.Write(bt, 0, bt.Length);
                        }

                        using (FileStream objWriter = new FileStream(strContentFilePath, FileMode.OpenOrCreate, FileAccess.Write))
                        {
                            string strContent = Utility.EncryptDES(strResult, strK, strV);
                            byte[] bt = System.Text.Encoding.Default.GetBytes(strContent);
                            objWriter.Write(bt, 0, bt.Length);
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "GetBackupInfoByComputer容错1");
                    }
                }
                //---------------------------------------------------------------------------------------------------
                //Common.Log("<<<<<<GetPwdFromService:" + strResult);

                GetChargeObjectFromContact(strResult, lstWechatAccounts);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetPwdFromService");
            }
        }

        private static void GetChargeObjectFromContact(string strContact, List<WechatAccInfo> lstWechatAccounts)
        {
            try
            {
                JsonObject jObj = JsonParser.ParseString(strContact);
                if (jObj == null)
                {
                    return;
                }

                if (jObj.ContainsKey("state") && jObj["state"] is JsonString)
                {
                    JsonString para = (JsonString)jObj["state"];
                    if (!(para.Value == "1"))
                        return;
                }

                JsonObject dataObject = null;
                if (jObj.ContainsKey("data") && jObj["data"] is JsonObject)
                    dataObject = (JsonObject)jObj["data"];

                if (dataObject.ContainsKey("code") && dataObject["code"] is JsonNumber && dataObject.ContainsKey("msg") && dataObject["msg"] is JsonString)
                {
                    JsonNumber jCode = (JsonNumber)dataObject["code"];
                    JsonString jMsg = (JsonString)dataObject["msg"];

                    //返回macid
                    // 去掉这段验证，要不 ischarge 没机会赋值，永远不会出现付费激活窗体， at by zsh
                    //if (jCode.Value != 1)
                    //return;

                    // 客户端失效
                    if (jCode.Value == 3)
                    {
                        if (mAssistInfo.IsVersionDisabled == false)
                        {
                            mAssistInfo.IsVersionDisabled = (jCode.Value == 3 ? true : false);
                        }
                    }
                }

                //--------------------------

                if (dataObject.ContainsKey("ischarge") && dataObject["ischarge"] is JsonNumber)
                {
                    JsonNumber jPara = (JsonNumber)dataObject["ischarge"];
                    mAssistInfo.IsPay = jPara.Value == 1 ? true : false;

                    //Common.Log(string.Format("WechatHeplerPC>>>>>>>提示：是否付费?>>>>>>>>({0})", WechatHeplerPC.mAssistInfo.IsPay));
                }

                if (dataObject.ContainsKey("officialUrl") && dataObject["officialUrl"] is JsonString)
                {
                    JsonString jPara = (JsonString)dataObject["officialUrl"];
                    mAssistInfo.StrOfficialUrl = jPara.Value;
                }

                if (dataObject.ContainsKey("chargeUrl") && dataObject["chargeUrl"] is JsonString)
                {
                    JsonString jPara = (JsonString)dataObject["chargeUrl"];
                    mAssistInfo.StrChargeUrl = jPara.Value;
                }

                if (dataObject.ContainsKey("videoUrl") && dataObject["videoUrl"] is JsonString)
                {
                    JsonString jPara = (JsonString)dataObject["videoUrl"];
                    mAssistInfo.StrVideoCourse = jPara.Value;
                }

                if (dataObject.ContainsKey("numberQQ") && dataObject["numberQQ"] is JsonString)
                {
                    JsonString jPara = (JsonString)dataObject["numberQQ"];
                    mAssistInfo.numberQQ = jPara.Value;
                }

                if (dataObject.ContainsKey("qrCodeQQ") && dataObject["qrCodeQQ"] is JsonString)
                {
                    JsonString jPara = (JsonString)dataObject["qrCodeQQ"];
                    mAssistInfo.qrCodeQQ = jPara.Value;
                }

                if (dataObject.ContainsKey("customerQQ") && dataObject["customerQQ"] is JsonString)
                {
                    JsonString jPara = (JsonString)dataObject["customerQQ"];
                    mAssistInfo.StrLinkQQ = jPara.Value;
                }

                if (dataObject.ContainsKey("versiondisabled") && dataObject["versiondisabled"] is JsonNumber)
                {
                    JsonNumber jPara = (JsonNumber)dataObject["versiondisabled"];

                    if (mAssistInfo.IsVersionDisabled == false)
                    {
                        mAssistInfo.IsVersionDisabled = jPara.Value == 1 ? true : false;
                    }
                }

                if (LoadServerSettingHandler != null)
                    LoadServerSettingHandler(null, new EventArgs());

                //--------------------------

                if (dataObject.ContainsKey("datas") && dataObject["datas"] is JsonArray)
                {
                    JsonArray arrD = (JsonArray)dataObject["datas"];
                    Dictionary<string, string> dictTemp = new Dictionary<string, string>(StringComparer.InvariantCultureIgnoreCase);
                    if (arrD != null && arrD.Count > 0)
                    {
                        string strWXID = "";
                        string strPWD = "";
                        string strFriendlist = "";
                        DateTime dtEndTime = DateTime.Now;
                        ActivateState AState = ActivateState.Nonactivated;
                        int intTypeEx = 0;
                        foreach (JsonObject joComputer in arrD)
                        {
                            strWXID = "";
                            strPWD = "";
                            dtEndTime = DateTime.Now;
                            AState = ActivateState.Nonactivated;
                            intTypeEx = 0;
                            if (joComputer.ContainsKey("wxid") && joComputer["wxid"] is JsonString)
                            {
                                JsonString jPara = (JsonString)joComputer["wxid"];
                                strWXID = jPara.Value;
                            }

                            if (joComputer.ContainsKey("pwd") && joComputer["pwd"] is JsonString)
                            {
                                JsonString jPara = (JsonString)joComputer["pwd"];
                                strPWD = jPara.Value;
                            }

                            if (joComputer.ContainsKey("endtime") && joComputer["endtime"] is JsonString)
                            {
                                JsonString jPara = (JsonString)joComputer["endtime"];
                                try { dtEndTime = Convert.ToDateTime(jPara.Value); }
                                catch { }
                            }

                            if (joComputer.ContainsKey("friendlistnew") && joComputer["friendlistnew"] is JsonString)
                            {
                                JsonString jPara = (JsonString)joComputer["friendlistnew"];
                                strFriendlist = jPara.Value;
                            }

                            if (string.IsNullOrEmpty(strFriendlist) && joComputer.ContainsKey("friendlist") && joComputer["friendlist"] is JsonString)
                            {
                                JsonString jPara = (JsonString)joComputer["friendlist"];
                                strFriendlist = jPara.Value;
                            }

                            if (joComputer.ContainsKey("status") && joComputer["status"] is JsonNumber)
                            {
                                JsonNumber jPara = (JsonNumber)joComputer["status"];
                                //AState = jPara.Value.ToString() == "1" ? ActivateState.Normal : ActivateState.Overdue;
                                AState = (jPara.ValueInt == 1 ? ActivateState.Normal : ActivateState.Overdue);
                            }
                            if (joComputer.ContainsKey("typeex") && joComputer["typeex"] is JsonNumber)
                            {
                                //599 799套餐标记
                                JsonNumber jPara = (JsonNumber)joComputer["typeex"];
                                intTypeEx = jPara.ValueInt;//(int)jPara.Value;
                            }

                            //Common.Log(string.Format("WechatHelperPC>>>>>>>>>>>>{0}", strWXID));

                            List<WechatAccInfo> lstWAInfo = lstWechatAccounts.FindAll(delegate (WechatAccInfo info)
                            {
                                return (info.Account == strWXID || info.InnerAccount == strWXID);
                            });

                            if (lstWAInfo != null && lstWAInfo.Count > 0)
                            {
                                foreach (WechatAccInfo waInfo in lstWAInfo)
                                {
                                    if (waInfo.EndTime > DateTime.MinValue && waInfo.EndTime < dtEndTime)
                                    {
                                        waInfo.Password = strPWD;
                                        waInfo.EndTime = dtEndTime;
                                        waInfo.AState = AState;
                                        waInfo.FriendlistURL = strFriendlist;
                                        waInfo.TypeEx = intTypeEx;
                                    }

                                    //try
                                    //{
                                    //    Common.Log("<<<  账号信息：" + waInfo.Account + " " + waInfo.AState);
                                    //}
                                    //catch (Exception ex)
                                    //{
                                    //    Common.LogException(ex.ToString(), "账号信息2");
                                    //}
                                }
                            }

                        }
                    }
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ChargeHelper_GetChargeObjectFromContact");
            }

        }

        #endregion

        #region "--- 激活 ---"

        public static void ActivateAccount(WechatAccInfo waInfo, string strCDKEY, bool isShowSucceedHint)
        {
            try
            {
                if (mTDActivateAccount != null && mTDActivateAccount.ThreadState != System.Threading.ThreadState.Stopped)
                {
                    mTDActivateAccount.Abort();
                }
            }
            catch { }

            mTDActivateAccount = new Thread(new ParameterizedThreadStart(DoActivateAccount));
            mTDActivateAccount.IsBackground = true;
            mTDActivateAccount.Start(new object[] { waInfo, strCDKEY, isShowSucceedHint });
        }

        private static void DoActivateAccount(object objPara)
        {
            string strMessage = "";
            bool isRelust = false;
            int intTypeEx = 0;
            WechatAccInfo waInfo = new WechatAccInfo();
            try
            {
                object[] arrObj = (object[])objPara;
                waInfo = (WechatAccInfo)arrObj[0];
                string strCDKEY = arrObj[1].ToString();
                bool isShowSucceedHint = Convert.ToBoolean(arrObj[2]);

                string strUrl = "http://pc.api.tongbu.com/vip/v.html?t=78&projectid=6";

                string strPostJson = "";
                JsonObject jsonObj = new JsonObject();
                jsonObj.Add("projectid", "6");
                jsonObj.Add("macid", Common.GetComputerID());
                jsonObj.Add("macidnew", Common.GetComputerIDNew());
                jsonObj.Add("macudid", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddress().Trim()));
                jsonObj.Add("macudidnew", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddressFixed().Trim()));
                jsonObj.Add("sn", Common.GetHardDiskID2());
                jsonObj.Add("version", System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString(4));
                jsonObj.Add("channel", Common.GetChannelID());
                jsonObj.Add("code", strCDKEY);
                jsonObj.Add("wxid", waInfo.Account);
                jsonObj.Add("nickname", waInfo.Name);
                JsonWriter writer = new JsonWriter();
                jsonObj.Write(writer);
                strPostJson = writer.ToString();
                strPostJson = Utility.EncryptDES(strPostJson, mStrKey, mStrIV);

                bool blnSuredelay = false;

                string strResult = Utility.PostData(strUrl, strPostJson, 20000);
                strResult = strResult.Replace("_@_", "+");
                strResult = Utility.DecryptDES(strResult, mStrKey, mStrIV);
                isRelust = GetRegistrationResult(strResult, isShowSucceedHint, ref blnSuredelay, ref strMessage, ref waInfo, ref intTypeEx);

                //重新确认注册
                if (blnSuredelay)
                {
                    jsonObj.Add("suredelay", 1);
                    writer = new JsonWriter();
                    jsonObj.Write(writer);
                    strPostJson = writer.ToString();

                    strResult = Utility.PostData(strUrl, strPostJson, 20000);
                    strResult = strResult.Replace("_@_", "+");
                    strResult = Utility.DecryptDES(strResult, mStrKey, mStrIV);
                    isRelust = GetRegistrationResult(strResult, isShowSucceedHint, ref blnSuredelay, ref strMessage, ref waInfo, ref intTypeEx);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoActivateAccount");
            }
            OnActivateAccountHandler(new ActivateAccountEventArgs(isRelust, strMessage, waInfo, intTypeEx));
        }

        private static void OnActivateAccountHandler(ActivateAccountEventArgs e)
        {
            if (ActivateAccountHandler != null)
                ActivateAccountHandler(null, e);
        }

        private static bool GetRegistrationResult(string strContact, bool isShowSucceedHint, ref bool blnSuredelay, ref string strMsg, ref WechatAccInfo waInfo, ref int intTypeEx)
        {
            bool isRelust = false;
            strMsg = "";
            intTypeEx = 0;

            try
            {
                JsonObject jObj = JsonParser.ParseString(strContact);
                if (jObj == null)
                {
                    MessageBox.Show("访问服务器异常", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    goto Do_Exit;
                }

                if (jObj.ContainsKey("state") && jObj["state"] is JsonString)
                {
                    JsonString para = (JsonString)jObj["state"];
                    if (!(para.Value == "1"))
                        goto Do_Exit;
                }

                JsonObject dataObject = null;
                if (jObj.ContainsKey("data") && jObj["data"] is JsonObject)
                    dataObject = (JsonObject)jObj["data"];
                else
                    goto Do_Exit;

                if (dataObject.ContainsKey("code") && dataObject["code"] is JsonNumber && dataObject.ContainsKey("msg") && dataObject["msg"] is JsonString)
                {
                    JsonNumber jCode = (JsonNumber)dataObject["code"];
                    JsonString jMsg = (JsonString)dataObject["msg"];

                    //返回macid
                    DateTime endTime = DateTime.MinValue;
                    DateTime today = DateTime.MinValue;
                    if (dataObject.ContainsKey("endtime") && dataObject["endtime"] is JsonString)
                    {
                        JsonString jEndTime = (JsonString)dataObject["endtime"];
                        try { endTime = Convert.ToDateTime(jEndTime.Value); }
                        catch { }
                    }

                    if (dataObject.ContainsKey("today") && dataObject["today"] is JsonString)
                    {
                        JsonString jToday = (JsonString)dataObject["today"];
                        try { today = Convert.ToDateTime(jToday.Value); }
                        catch { }
                    }

                    MessageBoxIcon mbIcon = MessageBoxIcon.Information;
                    bool isblnSuredelay = false;

                    bool SucceedShow = true;

                    if (jCode.Value == 1 && (jMsg.Value == Common.GetComputerID() || jMsg.Value == waInfo.Account))
                    {
                        waInfo.EndTime = endTime;
                        waInfo.AState = ActivateState.Normal;

                        //try
                        //{
                        //    Common.Log("<<<  账号信息：" + waInfo.Account + " " + waInfo.AState);
                        //}
                        //catch (Exception ex)
                        //{
                        //    Common.LogException(ex.ToString(), "账号信息1");
                        //}

                        strMsg = "激活成功";
                        mbIcon = MessageBoxIcon.Information;

                        if (dataObject.ContainsKey("typeex") && dataObject["typeex"] is JsonNumber)
                        {
                            JsonNumber jTypeEx = (JsonNumber)dataObject["typeex"];
                            intTypeEx = Convert.ToInt32(jTypeEx.Value);
                        }

                        isRelust = true;
                        if (!isShowSucceedHint)
                            SucceedShow = false;
                    }
                    else if (jCode.Value == 2)
                    {
                        strMsg = "授权已过期！";
                        mbIcon = MessageBoxIcon.Warning;
                    }
                    else if (jCode.Value == 4 && (jMsg.Value == Common.GetComputerID() || jMsg.Value == waInfo.Account))
                    {
                        int intMonth = 0;
                        if (dataObject.ContainsKey("months") && dataObject["months"] is JsonNumber)
                        {
                            JsonNumber jMonth = (JsonNumber)dataObject["months"];
                            intMonth = Convert.ToInt32(jMonth.Value);
                        }
                        if (MessageBox.Show(string.Format("已经授权过此电脑！当前到期时间为{0},是否续期{1}个月？", endTime.ToString("yyyy-MM-dd"), intMonth), "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                        {
                            blnSuredelay = true;
                        }
                        isblnSuredelay = true;
                    }
                    else
                    {
                        strMsg = jMsg.Value;
                        mbIcon = MessageBoxIcon.Error;
                    }
                    //else if (jCode.Value == 3)
                    //{
                    //    strMsg = "已超过授权电脑数！";
                    //    mbIcon = MessageBoxIcon.Warning;
                    //}

                    //else if (jCode.Value == -1)
                    //{
                    //    strMsg = "参数错误！";
                    //    mbIcon = MessageBoxIcon.Error;
                    //}
                    //else if (jCode.Value == -2)
                    //{
                    //    strMsg = "服务器处理异常";
                    //    mbIcon = MessageBoxIcon.Error;
                    //}
                    //else if (jCode.Value == -5)
                    //{
                    //    strMsg = "您的渠道信息有异常！";
                    //    mbIcon = MessageBoxIcon.Error;
                    //}
                    //else
                    //{
                    //    strMsg = "激活码无效！";
                    //    mbIcon = MessageBoxIcon.Error;
                    //}

                    if (!isblnSuredelay && SucceedShow)
                    {
                        MessageBox.Show(strMsg, "提示", MessageBoxButtons.OK, mbIcon);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetRegistrationResult");
            }

            Do_Exit:
            return isRelust;
        }

        public static bool ActiveComputeWithoutCode(WechatAccInfo waInfo)
        {
            //Common.Log("<<<<<<<<<< 激活账号与电脑的关系");

            string strMessage = "";
            int intTypeEx = 0;
            bool isRelust = false;

            try
            {
                string strUrl = "http://pc.api.tongbu.com/vip/v.html?t=79&projectid=6";

                string strPostJson = "";
                JsonObject jsonObj = new JsonObject();
                //jsonObj.Add("projectid", "6");
                jsonObj.Add("macid", Common.GetComputerID());
                jsonObj.Add("macidnew", Common.GetComputerIDNew());
                jsonObj.Add("macudid", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddress().Trim()));
                jsonObj.Add("macudidnew", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddressFixed().Trim()));
                jsonObj.Add("sn", Common.GetHardDiskID2());
                jsonObj.Add("version", System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString(4));
                jsonObj.Add("channel", Common.GetChannelID());
                //jsonObj.Add("code", strCDKEY);
                jsonObj.Add("wxid", waInfo.Account);
                jsonObj.Add("nickname", waInfo.Name);

                JsonWriter writer = new JsonWriter();
                jsonObj.Write(writer);
                strPostJson = writer.ToString();
                strPostJson = Utility.EncryptDES(strPostJson, mStrKey, mStrIV);

                bool blnSuredelay = false;

                string strResult = Utility.PostData(strUrl, strPostJson, 20000);
                strResult = strResult.Replace("_@_", "+");
                strResult = Utility.DecryptDES(strResult, mStrKey, mStrIV);

                isRelust = GetRegistrationResult(strResult, false, ref blnSuredelay, ref strMessage, ref waInfo, ref intTypeEx);

                //Common.Log(string.Format("<<<<<<<<<< 返回结果：{0}", strResult));

                // 如果失败，记录相应信息到异常日志里面，可以给客服添加
                if (!isRelust)
                {
                    Common.LogException(string.Format("获取需要的日志为：\r\n{0}====>{1}", strPostJson, strResult));
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoActivateAccount");
            }

            return isRelust;
        }

        #endregion

        #region "--- 加载媒体文件 ---"

        public int FileLength_ = 0;

        public string GetMediaFilePath(WechatMessageItem wmItem, string strBackupDBPath, string strPassword,
                                       string strBackupMediaFile, byte[] buffer, bool blnThumbFile,
                                       bool WeChatiOSDevice, out int intMapKey)
        {
            intMapKey = 0;
            string strMediaFilePath = string.Empty;
            string strMediaThumbFilePath = string.Empty;

            //如果只有一个文件并且缩略图有取过就取缩略图的
            if (wmItem.MediaFileName != null && wmItem.MediaFileName.Count == 1 && strMediaThumbFilePath.Length > 0)
            {
                strMediaFilePath = strMediaThumbFilePath;
            }
            else
            {
                strMediaFilePath = this.LoadMediaPath(blnThumbFile, wmItem, strBackupDBPath, strPassword, strBackupMediaFile, buffer, WeChatiOSDevice, out intMapKey);
            }

            return strMediaFilePath.Trim();
        }

        public string GetMediaThumbFilePath(WechatMessageItem wmItem, string strBackupDBPath, string strPassword, string strBackupMediaFile,
                                            byte[] buffer, bool WeChatiOSDevice, out int intMapKey)
        {
            intMapKey = 0;
            string strMediaFilePath = string.Empty;
            string strMediaThumbFilePath = string.Empty;

            //如果只有一个文件并且原图有取过就取原图的
            if (wmItem.MediaFileName != null && wmItem.MediaFileName.Count == 1 && strMediaFilePath.Length > 0)
            {
                strMediaThumbFilePath = strMediaFilePath;
            }
            else
            {
                strMediaThumbFilePath = this.LoadMediaPath(true, wmItem, strBackupDBPath, strPassword, strBackupMediaFile, buffer, WeChatiOSDevice, out intMapKey);
            }

            return strMediaThumbFilePath.Trim();
        }

        private string LoadMediaPath(bool blnThumbFile, WechatMessageItem wmItem, string strBackupDBPath, string strPassword,
                                     string strBackupMediaFile, byte[] buffer, bool WeChatiOSDevice, out int intMapKey)
        {
            string strMediaFilePath = string.Empty;
            intMapKey = 0;
            // 上面是iOS语音的判断格式， 下面是安卓语音的判断格式
            if ((wmItem == null || wmItem.MediaFileName == null) &&
                 wmItem.AndroidVoiceMsgList == null)
            {
                return string.Empty;
            }

            //if (wmItem.AndroidVoiceMsgList == null)
            if (WeChatiOSDevice)
            {
                //ios
                strMediaFilePath = this.LoadMediaPathIOS(blnThumbFile, wmItem, strBackupDBPath, strPassword, strBackupMediaFile, buffer, out intMapKey);
            }
            else
            {
                //android
                strMediaFilePath = this.LoadMediaPathAndroid(blnThumbFile, wmItem, strBackupDBPath, strPassword, strBackupMediaFile, buffer, out intMapKey);
            }

            return strMediaFilePath;
        }

        private string LoadMediaPathIOS(bool blnThumbFile, WechatMessageItem wmItem, string strBackupDBPath, string strPassword,
                                        string strBackupMediaFile, byte[] buffer, out int intMapKey)
        {
            intMapKey = 0;
            if ((wmItem == null || wmItem.MediaFileName == null))
            {
                return string.Empty;
            }
            if (blnThumbFile && wmItem.MediaCacheThumbPath.Length > 0)
            {
                try
                {
                    intMapKey = Convert.ToInt32(Path.GetFileName(wmItem.MediaCacheThumbPath));
                }
                catch { }

                return wmItem.MediaCacheThumbPath;
            }
            if (!blnThumbFile && wmItem.MediaCacheBigPath.Length > 0)
            {
                try
                {
                    intMapKey = Convert.ToInt32(Path.GetFileName(wmItem.MediaCacheBigPath));
                }
                catch { }
                return wmItem.MediaCacheBigPath;
            }

            string strFilePathTemp = "";

            if (wmItem.MediaFileName != null && wmItem.MediaFileName.Count > 0)
            {
                if (wmItem.MediaFileName.Count == 1)
                {
                    strFilePathTemp = wmItem.MediaFileName[0];
                }
                else
                {
                    foreach (string item in wmItem.MediaFileName)
                    {
                        bool flag = item.EndsWith("_thumb", StringComparison.InvariantCultureIgnoreCase);
                        //取缩略图
                        if (blnThumbFile && flag)
                        {
                            strFilePathTemp = item;
                            break;
                        }
                        else if (!blnThumbFile && !flag)
                        {
                            strFilePathTemp = item;
                            break;
                        }
                    }
                }

                Common.Log(string.Format("正在从数据库中导出文件：psd={0}，strFilePathTemp={1}, type=ios", strPassword, strFilePathTemp), "WechatHeplerPC", true);
                WechatHeplerDB db = WechatHeplerDB.GetInstance(strBackupDBPath, strPassword, strBackupMediaFile);
                db.FileLength_ = FileLength_;
                strFilePathTemp = db.GetMediaRecode(strFilePathTemp, buffer, out intMapKey);
                Common.Log(string.Format("从数据库中导出文件完成：strFilePathTemp={0}，type=ios", strFilePathTemp), "WechatHeplerPC", true);
            }

            //缓存起来，以便下次可以直接取
            if (blnThumbFile)
            {
                wmItem.MediaCacheThumbPath = strFilePathTemp;
            }
            else
            {
                wmItem.MediaCacheBigPath = strFilePathTemp;
            }

            return strFilePathTemp;
        }

        private string LoadMediaPathAndroid(bool blnThumbFile, WechatMessageItem wmItem, string strBackupDBPath,
                                            string strPassword, string strBackupMediaFile, byte[] buffer, out int intMapKey)
        {
            intMapKey = 0;
            // 上面是iOS语音的判断格式， 下面是安卓语音的判断格式
            if ((wmItem == null || wmItem.MediaFileName == null) &&
                 wmItem.AndroidVoiceMsgList == null)
            {
                return string.Empty;
            }
            if (blnThumbFile && wmItem.MediaCacheThumbPath.Length > 0)
            {
                try
                {
                    intMapKey = Convert.ToInt32(Path.GetFileName(wmItem.MediaCacheThumbPath));
                }
                catch { }
                return wmItem.MediaCacheThumbPath;
            }
            if (!blnThumbFile && wmItem.MediaCacheBigPath.Length > 0)
            {
                try
                {
                    intMapKey = Convert.ToInt32(Path.GetFileName(wmItem.MediaCacheBigPath));
                }
                catch { }
                return wmItem.MediaCacheBigPath;
            }

            string strFilePathTemp = "";
            bool isLoadAndroid = false;

            if (blnThumbFile && wmItem.AndroidVoiceMsgList != null && wmItem.AndroidVoiceMsgList.Count > 0 && wmItem.AndroidVoiceMsgList.Any(f => f.ArrayMessageContent != null && f.MessageLength > 0))
            {
                //优先加载安卓的缩略图
                isLoadAndroid = true;
                strFilePathTemp = GetMediaCachePathByAndroid(wmItem, strBackupDBPath);
            }

            if (strFilePathTemp.Length == 0 && wmItem.MediaFileName != null && wmItem.MediaFileName.Count > 0)
            {
                if (wmItem.MediaFileName.Count == 1)
                {
                    strFilePathTemp = wmItem.MediaFileName[0];
                }
                foreach (string item in wmItem.MediaFileName)
                {
                    //取缩略图
                    if (blnThumbFile)
                    {
                        if (item.EndsWith("_thumb"))
                        {
                            strFilePathTemp = item;
                            break;
                        }
                    }
                    else
                    {
                        if (!item.EndsWith("_thumb"))
                        {
                            strFilePathTemp = item;
                            break;
                        }
                    }
                }

                Common.Log(string.Format("正在从数据库中导出文件：psd={0}，strBackupMediaFile={1}，strFilePathTemp={2}，type=Android", strPassword, strBackupMediaFile, strFilePathTemp), "WechatHeplerPC", true);
                strFilePathTemp = WechatHeplerDB.GetInstance(strBackupDBPath, strPassword, strBackupMediaFile).GetMediaRecode(strFilePathTemp, buffer, out intMapKey);
                Common.Log(string.Format("从数据库中导出文件完成：strFilePathTemp={0}，type=Android", strFilePathTemp), "WechatHeplerPC", true);
            }

            //缓存起来，以便下次可以直接取
            if (isLoadAndroid)
            {
                // 目前看到安卓读取缩略图的方式才会放在这边，大图和iOS的存放地址是一样的。
                wmItem.MediaCacheThumbPath = strFilePathTemp;
            }
            else if (blnThumbFile)
            {
                wmItem.MediaCacheThumbPath = strFilePathTemp;
            }
            else
            {
                wmItem.MediaCacheBigPath = strFilePathTemp;
            }

            return strFilePathTemp;
        }

        private static string GetMediaCachePathByAndroid(WechatMessageItem wmItem, string strBackupDBPath)
        {
            string strFilePathTemp = "";

            try
            {
                byte[] arrBufferDecode = wmItem.AndroidVoiceMsgList[0].ArrayMessageContent;
                if (arrBufferDecode == null)
                {
                    return strFilePathTemp;
                }

                string strMediaFolder = Path.Combine(Path.GetDirectoryName(strBackupDBPath), "Media");
                Folder.CheckFolder(strMediaFolder);
                strFilePathTemp = Path.Combine(strMediaFolder, "" + wmItem.MessageId);

                FileStream fs = new FileStream(strFilePathTemp, FileMode.Create);
                fs.Write(arrBufferDecode, 0, arrBufferDecode.Length);
                fs.Close();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetMediaCachePathByAndroid");
            }
            return strFilePathTemp;
        }

        #endregion

        // 分析sysmsg
        public string ParseSysmsg(string sysmsg)
        {
            string msgXml = sysmsg;
            string strpath = Folder.TempFolder;
            Folder.CheckFolder(strpath);

            try
            {
                strpath = Path.Combine(strpath, "sysmg_temp.xml");
                if (File.Exists(strpath))
                    File.Delete(strpath);

                if (msgXml.StartsWith("<sysmsg type="))
                {
                    using (StreamWriter ws = new StreamWriter(strpath, true))
                    {
                        ws.Write(msgXml);
                    }
                    if (File.Exists(strpath))
                    {
                        string strName = "";
                        string strText = "";
                        string strLink = "";
                        string strAdder = "";
                        string strFrom = "";
                        using (XmlTextReader reader = new XmlTextReader(strpath))
                        {
                            while (reader.Read())
                            {
                                switch (reader.NodeType)
                                {
                                    case XmlNodeType.Element:
                                        strName = reader.Name;
                                        if (strName.Equals("link"))
                                        {
                                            while (reader.MoveToNextAttribute())
                                            {
                                                if (reader.Name.Equals("name"))
                                                {
                                                    strLink = reader.Value;
                                                    break;
                                                }
                                            }
                                        }
                                        break;
                                    case XmlNodeType.CDATA:
                                        if (strName.Equals("template"))
                                            msgXml = reader.Value;
                                        else if (strLink.Equals("adder") && strName.Equals("nickname"))
                                        {
                                            strAdder = reader.Value;
                                        }
                                        else if (strLink.Equals("from") && strName.Equals("nickname"))
                                        {
                                            strFrom = reader.Value;
                                        }
                                        else if (strLink.Equals("username") && strName.Equals("nickname"))
                                        {
                                            strAdder = reader.Value;
                                        }
                                        else if (strLink.Equals("names") && strName.Equals("nickname"))
                                        {
                                            strFrom = reader.Value;
                                        }
                                        break;
                                }
                            }
                        }

                        if (!string.IsNullOrEmpty(strAdder) && !string.IsNullOrEmpty(strFrom))
                        {
                            msgXml = msgXml.Replace("\" $adder$", strAdder)
                                           .Replace("$from$", strFrom)
                                           .Replace("$username$", strAdder)
                                           .Replace("$names$", strFrom);
                        }
                    }
                }
            }
            catch (Exception ex)
            {

            }

            return msgXml;
        }
        public void GetAccountBackupedDevice(WechatAccInfo waInfo)
        {
            try
            {
                // 清空选择的设备信息
                waInfo.SelectedDeviceUDID = string.Empty;

                //获取当前帐号 备份的所有设备
                List<string> lstBackupDevices = new List<string>();
                string strBackupFiles = Path.Combine(waInfo.PathOnPC, "BackupFiles");

                //Common.LogException(string.Format("当前用户的文件路径为：{0}", waInfo.PathOnPC));

                if (!string.IsNullOrEmpty(waInfo.StrBackupFilesPath) && Directory.Exists(waInfo.StrBackupFilesPath))
                {
                    strBackupFiles = waInfo.StrBackupFilesPath;
                    this.mStrWeChatFiles = strBackupFiles;
                }


                //Common.LogException(string.Format("当前备份路径为：{0}", waInfo.StrBackupFilesPath));

                if (Directory.Exists(strBackupFiles))
                {
                    foreach (DirectoryInfo item in new DirectoryInfo(strBackupFiles).GetDirectories())
                    {
                        if (!lstBackupDevices.Contains(item.FullName))
                            lstBackupDevices.Add(item.FullName);
                    }
                }

                //Common.LogException(string.Format("当前备份内包含的文件数量为：{0}", lstBackupDevices.Count));

                if (lstBackupDevices.Count == 0)
                {
                    return;
                    //goto DoExit;
                }

                List<WeChatAccBackupInfo> lstWCABInfo = new List<WeChatAccBackupInfo>();
                int index = 0;

                //string tempfolder = Folder.GetTempFilePath();
                string tempfolder = Path.Combine(IniSetting.GetWechatAssistantDBFolder(), "cache");

                if (string.IsNullOrEmpty(tempfolder))
                {
                    tempfolder = Folder.GetTempFilePath();
                }

                //Common.LogException(string.Format("创建缓存文件夹，该路径为：{0}", tempfolder));

                foreach (string item in lstBackupDevices)
                {
                    index++;
                    //if (GetWechatAccountFromPCHandler != null)
                    //    GetWechatAccountFromPCHandler(null, new WechatAccountEventArgs(string.Format("解析备份数据，请耐心等候... {0}/{1}", index, lstBackupDevices.Count)));

                    string strFolderPath = Path.GetFileNameWithoutExtension(this.mStrWeChatFiles) + item.Replace(this.mStrWeChatFiles, "");
                    strFolderPath = Path.Combine(tempfolder, strFolderPath);
                    Folder.CheckFolder(strFolderPath);


                    string strBackupPath = Path.Combine(strFolderPath, "Backup.db");
                    Common.Log(string.Format("当前数据库备份地址：{0}", strBackupPath), "DoSWeHelpWork", true);

                    try
                    {
                        if (File.Exists(strBackupPath))
                        {
                            WechatHeplerDB.GetInstance(strBackupPath, waInfo.Password, item).CloseDBConnection(strBackupPath, waInfo.Password);
                            File.Delete(strBackupPath);
                        }
                    }
                    catch { }

                    ////TODO 此处需要判断一下微信版本

                    //string exePath = Path.Combine(@"E:\GitCode\tongbu_assistant\bin\x86\Debug\IncludeDlls\Cmd\x86", "iWechatAssistantCmd40.exe");

                    //Common.ExecuteCmdExe(pathExe: exePath, new string[] { waInfo.Password, Path.Combine(item, "Backup.db"), strBackupPath });

                    Common.Log(string.Format("开始解析数据文件"), "DoSWeHelpWork", true);

                    int intResult = mDBDecoder(waInfo.Password, Path.Combine(item, "Backup.db"), strBackupPath);

                    Common.Log(string.Format("解析完成：{0}", intResult), "DoSWeHelpWork", true);

                    if (intResult == 0 && File.Exists(strBackupPath))
                    {
                        BackConfigInfo infoConfig = WechatHeplerDB.GetInstance(strBackupPath, waInfo.Password, item).GetWechatConfig();
                        if (infoConfig != null)
                        {
                            //Common.LogException(string.Format("获取到备份信息"));
                            infoConfig.BackupDBPath = strBackupPath;
                            //去掉拷贝媒体文件数据到临时目录 优化加载速度 by-cbh
                            //string[] files = Directory.GetFiles(item, "*");

                            //string strTempPath = "";
                            //foreach (string file in files)
                            //{
                            //    if (file.EndsWith("Backup.db") || file.EndsWith("Backup.db-shm") || file.EndsWith("Backup.db-wal"))
                            //        continue;

                            //    strTempPath = Path.Combine(strFolderPath, Path.GetFileName(file));
                            //    try
                            //    {
                            //        if (File.Exists(file))
                            //        {
                            //            File.Copy(file, strTempPath, true);
                            //        }
                            //    }
                            //    catch { }
                            //}

                            WeChatAccBackupInfo WCABInfo = new WeChatAccBackupInfo();
                            WCABInfo.BCConfigInfo = infoConfig;
                            WCABInfo.DeviceName = string.Format("{0}_{1}_{2}", infoConfig.BackConfigList.DeviceName, infoConfig.BackConfigList.DeviceType, infoConfig.BackConfigList.OSVersion);
                            WCABInfo.DeviceUDID = infoConfig.DeviceIndntify;
                            WCABInfo.SavePath = item;
                            WCABInfo.TempSavePath = strFolderPath;
                            WCABInfo.MediaFilePath = item;

                            // 选择一台设备
                            if (waInfo.SelectedDeviceUDID.Length == 0)
                            {
                                waInfo.SelectedDeviceUDID = WCABInfo.DeviceUDID;
                            }

                            lstWCABInfo.Add(WCABInfo);
                        }
                    }

                }
                waInfo.LstWCABInfo = lstWCABInfo;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetAccountLoginDevice");
            }

            //DoExit:
            //    mDoGetAccountLoginDevice = false;   
        }

        //加载聊天记录
        public Dictionary<string, WechatTalk> GetTalkList(string strAccountPath, string strAccountPwd, string strBackupMediaFile, WechatAccInfo waInfo)
        {
            WechatHeplerDB.GetTalkListHandler -= WechatHeplerDB_GetTalkListHandler;
            WechatHeplerDB.GetTalkListHandler += WechatHeplerDB_GetTalkListHandler;
            return WechatHeplerDB.GetInstance(strAccountPath, strAccountPwd, strBackupMediaFile).GetTalkList(waInfo);
        }

        private void WechatHeplerDB_GetTalkListHandler(object sender, GetTalkListEventArgs e)
        {
            if (GetWechatAccountFromPCHandler != null)
                GetWechatAccountFromPCHandler(null, new WechatAccountEventArgs(e.StrMessage));
        }

        #region "--- 时间转换 ---"

        // UTC 时间，Windows的时间是有时区的，要加上 UTC 和当前时区相差的小时数
        public static DateTime ConvertWeixinToPcTime(long timeMillis)
        {
            if (timeMillis.ToString().Length > 10)
            {
                timeMillis = Convert.ToInt64(timeMillis.ToString().Substring(0, 10));
            }

            System.DateTime deviceTime = new System.DateTime(0);
            try
            {
                if (timeMillis == 0)
                {
                    return System.DateTime.MinValue;
                }

                //Java中可以用System.currentTimeMillis() 获取当前时间的long形式，它的标示形式是从1970年1月1日起的到当前的毫秒的数。
                long timeTicks = timeMillis * ******** + new DateTime(1970, 1, 1, 0, 0, 0).Ticks;
                deviceTime = new System.DateTime(timeTicks);
            }
            catch { }

            return deviceTime.AddHours(mUTC2LocalTime);
        }

        //搜索的时间范围，开始时间：即这天的最早时间，结束时间：即这天的最晚时间。
        public static long ConvertPcTimeToWeixin(DateTime time)
        {
            long longTime = 0;
            try
            {
                DateTime TempTime = time.AddHours(-mUTC2LocalTime);

                longTime = TempTime.Ticks;
                longTime = (longTime - new DateTime(1970, 1, 1, 0, 0, 0).Ticks) / ********;
            }
            catch { }

            return longTime;
        }

        #endregion

        #region "--- 语音转换 ---"

        public static void InitEnvironmentPath()
        {
            string strPath = Environment.GetEnvironmentVariable("Path");
            string strNewPath = Path.Combine(Folder.CacheFolder, "RubbishClearWebPage");
            strPath = strPath.TrimEnd(";".ToCharArray()) + ";" + strNewPath;
            Environment.SetEnvironmentVariable("Path", strPath, EnvironmentVariableTarget.Process);
        }


        public static void ReleaseAudDll()
        {
            try
            {
                string strFilePath = Path.Combine(Folder.CacheFolder, "RubbishClearWebPage");
                Folder.CheckFolder(strFilePath);
                string strFile = Path.Combine(strFilePath, "tbChatWebPage.temp");

                if (File.Exists(strFile))
                {
                    FileInfo fileInfo = new FileInfo(strFile);

                    if (fileInfo.Length != iWechatAssistant.Properties.Resources.weixin_dll_aud.Length)
                    {
                        try
                        {
                            File.Delete(strFile);
                        }
                        catch
                        {
                        }
                    }
                }

                if (!File.Exists(strFile))
                {
                    using (FileStream objWriter = new FileStream(strFile, FileMode.OpenOrCreate, FileAccess.Write))
                    {
                        objWriter.Write(iWechatAssistant.Properties.Resources.weixin_dll_aud, 0, iWechatAssistant.Properties.Resources.weixin_dll_aud.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeixinHelper_ReleaseAudDll");
            }
        }

        public static void ReleaseSilkDll()
        {
            try
            {
                string strFilePath = Path.Combine(Folder.CacheFolder, "RubbishClearWebPage");
                Folder.CheckFolder(strFilePath);
                string strFile = Path.Combine(strFilePath, "tbChatWebPageEx.temp");

                if (File.Exists(strFile))
                {
                    FileInfo fileInfo = new FileInfo(strFile);

                    if (fileInfo.Length != iWechatAssistant.Properties.Resources.weixin_dll_silk.Length)
                    {
                        try
                        {
                            File.Delete(strFile);
                        }
                        catch
                        {
                        }
                    }
                }

                if (!File.Exists(strFile))
                {
                    using (FileStream objWriter = new FileStream(strFile, FileMode.OpenOrCreate, FileAccess.Write))
                    {
                        objWriter.Write(iWechatAssistant.Properties.Resources.weixin_dll_silk, 0, iWechatAssistant.Properties.Resources.weixin_dll_silk.Length);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeixinHelper_ReleaseSilkDll");
            }
        }

        private enum FileType
        {
            Amr = 0,
            Silk = 1
        }

        [DllImport("tbChatWebPage.temp", CallingConvention = CallingConvention.Cdecl)]
        private static extern int Aud2Wav(string armFile, string wavFile, string key);

        [DllImport("tbChatWebPageEx.temp", CallingConvention = CallingConvention.Cdecl)]
        private static extern int SilkToWav(string silkfilename, string wavfilename);

        public static int Wav2Mp3(string armFile, string wavFile, string mp3File, string key, string strMessage = "")
        {
            int iReusult = 0;
            try
            {
                iReusult = Aud2WavEx(armFile, wavFile, key, strMessage);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "Wav2Mp3_1");
            }

            try
            {
                string strArguments = string.Format(@" -V0 ""{0}"" ""{1}"" ", wavFile, mp3File);
                string strUndark = Path.Combine(Folder.AppFolder, @"IncludeDlls\lame.exe");

                ProcessStartInfo info = new ProcessStartInfo();
                info.FileName = strUndark;
                info.CreateNoWindow = true;
                info.UseShellExecute = false;
                info.Arguments = strArguments;

                using (Process process = new Process())
                {
                    process.StartInfo = info;
                    process.Start();
                    process.WaitForExit();
                    process.Close();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "Wav2Mp3_2");
            }
            return iReusult;
        }

        public static int Aud2WavEx(string armFile, string wavFile, string key, string strMessage = "")
        {
            FileType type = CheckFileType(armFile, strMessage);
            int iReusult = 0;
            try
            {
                if (Common.IsX64)
                {
                    List<string> list = new List<string>();
                    if (type == FileType.Amr)
                    {
                        list.Add("Aud2Wav");
                        list.Add(armFile);
                        list.Add(wavFile);
                        list.Add(key);
                    }
                    else
                    {
                        list.Add("SilkToWav");
                        list.Add(armFile);
                        list.Add(wavFile);
                    }
                    Common.CallCmdExe(list.ToArray());

                }
                else
                {
                    switch (type)
                    {
                        case FileType.Amr:
                            iReusult = Aud2Wav(armFile, wavFile, key);

                            break;
                        case FileType.Silk:

                            string strDBFolder = Path.Combine(IniSetting.GetWechatAssistantDBFolder(), "Audio");
                            Folder.CheckFolder(strDBFolder);
                            string filename = Path.Combine(strDBFolder, Path.GetFileName(armFile));
                            File.Copy(armFile, filename, true);
                            iReusult = SilkToWav(armFile, wavFile);
                            File.Delete(filename);

                            break;
                    }

                    //返回值:
                    //-1  打开失败
                    //-2	 转化失败
                    //-3  读取失败
                    //-4  格式错误
                    //0:  成功
                    //wx主意导出失败的时候输出错误类型后面可以追踪
                    if (iReusult < 0)
                    {
                        Common.Log(string.Format("Audio Convert Failure {0}  {1}", iReusult, armFile));
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "Aud2WavEx");
            }

            return iReusult;
        }

        private static FileType CheckFileType(string strFile, string strMessage)
        {
            FileType type = FileType.Silk;
            try
            {
                byte[] byffer = File.ReadAllBytes(strFile);
                if (byffer.Length <= 0)
                {
                    return type;
                }
                //Amr文件
                if (byffer.Length > 13 && byffer[0] == 0x23 && byffer[1] == 0x21 && byffer[2] == 0x41 && byffer[3] == 0x4d && byffer[4] == 0x52 && byffer[5] == 0xa && !(byffer[9] == 0x53) && !(byffer[10] == 0x49) && !(byffer[11] == 0x4c) && !(byffer[12] == 0x4b))
                {
                    type = FileType.Amr;
                }
                else if (byffer.Length > 7 && byffer[3] == 0x53 && byffer[4] == 0x49 && byffer[5] == 0x4c && byffer[6] == 0x4b)
                {
                    type = FileType.Silk;
                }
                else if (!string.IsNullOrEmpty(strMessage) && strMessage.Contains("voiceformat=\"0\""))
                {
                    type = FileType.Amr;
                    //低版本的wx发消息给高版本的微信会出现语音无法转换的问题。
                    UpdateAmrFile(strFile, byffer);
                }
            }
            catch { }

            return type;
        }

        private static bool UpdateAmrFile(string strFilePath, byte[] byffer)
        {
            bool blnReturn = false;
            try
            {
                if (byffer.Length < 10)
                {
                    Common.LogException("File Error", "WeixinHelper_UpdateAmrFile");
                    return blnReturn;
                }
                byte[] byfferNew = new byte[byffer.Length + 7];
                byfferNew[0] = 0x23;
                byfferNew[1] = 0x21;
                byfferNew[2] = 0x41;
                byfferNew[3] = 0x4d;
                byfferNew[4] = 0x52;
                byfferNew[5] = 0xa;
                int intIndex = 0;
                foreach (byte item in byffer)
                {
                    byfferNew[intIndex + 6] = item;
                    intIndex += 1;
                }
                File.WriteAllBytes(strFilePath, byfferNew);
                blnReturn = true;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeixinHelper_UpdateAmrFile");
            }
            return blnReturn;
        }

        #endregion

        public static string GetChatWavPathFromPC(string audioPath, string strMessage)
        {
            //获取语音路径
            string strWavFile = string.Empty;

            if (File.Exists(audioPath))
            {
                string strWavFilePath = Path.GetDirectoryName(audioPath);
                string strWavFileName = Path.GetFileNameWithoutExtension(audioPath) + ".wav";
                strWavFile = Path.Combine(strWavFilePath, strWavFileName);

                if (!File.Exists(strWavFile))
                {
                    try
                    {
                        WechatHeplerPC.Aud2WavEx(audioPath, strWavFile, "2013042508181124", strMessage);
                    }
                    catch (Exception ex)
                    {
                        string strErrMsga = string.Format("{0} {1} {2}", ex.ToString(), audioPath, strMessage);
                        Common.LogException(strErrMsga, "转换格式出错");
                    }
                }
            }
            return strWavFile;
        }

        public static string GetChatMp3PathFromPC(string audioPath, string strMessage)
        {
            string strMp3File = string.Empty;
            if (File.Exists(audioPath))
            {
                string strFilePath = Path.GetDirectoryName(audioPath);
                string strWavFilePath = Path.Combine(strFilePath, Path.GetFileNameWithoutExtension(audioPath) + ".wav");
                strMp3File = Path.Combine(strFilePath, Path.GetFileNameWithoutExtension(audioPath) + ".mp3");

                if (!File.Exists(strMp3File))
                {
                    try
                    {

                        WechatHeplerPC.Wav2Mp3(audioPath, strWavFilePath, strMp3File, "2013042508181124", strMessage);
                    }
                    catch (Exception ex)
                    {
                        string strErrMsga = string.Format("{0} {1} {2}", ex.ToString(), audioPath, strMessage);
                        Common.LogException(strErrMsga, "转换格式出错");
                    }
                }
            }
            return strMp3File;
        }

        public static double GetChatVoiceLength(string strContent, bool blnRound)
        {
            double singleLength = 0;
            string strContent1 = strContent;
            string strKey = "voicelength=\"";
            if (strContent1.Length > 0 && strContent1.Contains(strKey))
            {
                int startIndex = strContent1.IndexOf(strKey);
                strContent1 = strContent.Substring(startIndex + strKey.Length);
                startIndex = strContent1.IndexOf("\"");
                strContent1 = strContent1.Substring(0, startIndex);
                try
                {
                    singleLength = float.Parse(strContent1);
                }
                catch { }
                singleLength = singleLength / 1000;
                if (blnRound)
                {
                    singleLength = System.Math.Floor(singleLength + 0.5);
                    //四舍五入
                }
            }
            return singleLength;
        }

        public static bool IsReceivedMsg(WechatMessageItem wItem, WechatAccInfo waInfo)
        {
            //bool blnReceive = true;

            //if (wItem.ReceiveId == waInfo.Account)
            //{
            //    blnReceive = false;
            //}

            //return blnReceive;

            return (!wItem.ReceiveId.Equals(waInfo.Account, StringComparison.InvariantCultureIgnoreCase));
        }

        public static bool CheckIsChatRoom(string UserName)
        {
            return UserName.EndsWith("@chatroom", StringComparison.InvariantCultureIgnoreCase);
        }

        private static string RandomString(int size, bool lowerCase = true)
        {
            StringBuilder sb = new StringBuilder();
            Random r = new Random();
            char ch;
            for (int i = 0; i < size; i++)
            {
                ch = Convert.ToChar(Convert.ToInt32(Math.Floor(26 * r.NextDouble() + 65)));
                sb.Append(ch);
            }

            if (lowerCase)
                return sb.ToString().ToLowerInvariant();

            return sb.ToString();
        }

        private static void DeleteXmlTempDirThread(object tmpdir)
        {
            try
            {
                string dirXml = tmpdir.ToString();
                Directory.Delete(dirXml, true);
            }
            catch { }
        }
        public static void DeleteXmlTempDir()
        {
            // 删除所有临时文件
            string dirXml = Path.Combine(Folder.TempFolder, "xmltmp");
            if (Directory.Exists(dirXml))
                new Thread(new ParameterizedThreadStart(DeleteXmlTempDirThread)).Start(dirXml);
        }


        public static void AnalyseGroup(Dictionary<string, WeChatFriendInfo> DicWeChatFriendInfo,
                                        ref string strUsrName, ref string wxAccount,
                                        ref string strNickName, ref string strMsgForWeb, ref string strPetName,
                                        List<WeChatGroupFriendInfo> lstWeChatGroupFriendInfo, string strChatRoomName)
        {
            string[] values = strMsgForWeb.Split(new char[] { '\n', '\r' });
            string strWXID = "";
            if (strMsgForWeb.Contains("videomsg"))
            {
                try
                {
                    string strVideomsg = "";
                    if (values.Length == 2)
                        strVideomsg = values[1];
                    else if (values.Length > 2)
                        strVideomsg = values[2];
                    else
                        strVideomsg = values[0];

                    if (strVideomsg.Contains("fromusername="))
                    {
                        string strTemp = strVideomsg.Substring(strVideomsg.IndexOf("fromusername="), strVideomsg.Length - strVideomsg.IndexOf("fromusername="));
                        strTemp = strTemp.Substring(strTemp.IndexOf("\"") + 1);
                        strUsrName = strTemp.Substring(0, strTemp.IndexOf("\""));
                    }
                    strNickName = strUsrName;
                }
                catch { }
            }
            else if (strMsgForWeb.Contains("fromusername"))
            {
                try
                {
                    if (strMsgForWeb.Contains("fromusername=") || strMsgForWeb.Contains("fromusername ="))
                    {
                        string dirXml = Path.Combine(Folder.TempFolder, "xmltmp");
                        Folder.CheckFolder(dirXml);

                        string strpath = Path.Combine(dirXml, string.Format("xmltemp_{0}.xml", RandomString(4)));
                        //try
                        //{
                        //    if (File.Exists(strpath))
                        //        File.Delete(strpath);
                        //}
                        //catch { }

                        string strXml = strMsgForWeb.Substring(strMsgForWeb.IndexOf("<"));
                        if (!strXml.StartsWith("<?xml version=\"1.0\"?>"))
                            strXml = "<?xml version=\"1.0\"?>\r\n" + strXml;

                        if (!strXml.EndsWith("</msg>"))
                        {
                            // 处理表情其他字符结尾
                            string msg = "</msg>";
                            int pos = strXml.LastIndexOf(msg);
                            strXml = strXml.Substring(0, pos + msg.Length);
                        }
                        // Added by Utmost on 2020.06.09
                        //
                        XmlDocument doc = LoadXmlStrings(strXml);

                        if (doc == null)
                        {
                            using (StreamWriter file = new StreamWriter(strpath, false))
                            {
                                file.Write(strXml);
                            }
                            if (!File.Exists(strpath)) return;

                            doc = Common.XmlLoader(strpath);
                        }

                        if (doc.ChildNodes.Count > 0)
                        {
                            XmlNode nodeMsg = doc.ChildNodes[1];
                            XmlNodeList listMsgs = nodeMsg.ChildNodes;
                            foreach (XmlNode item in listMsgs)
                            {
                                if (item.Name.ToLowerInvariant() == "emoji")
                                {
                                    if (item.Attributes["fromusername"] != null)
                                        strUsrName = item.Attributes["fromusername"].Value;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (strMsgForWeb.Contains("<fromusername>") && strMsgForWeb.Contains("</fromusername>"))
                        {
                            strUsrName = strMsgForWeb.Substring(strMsgForWeb.IndexOf("<fromusername>") + "<fromusername>".Length,
                                                strMsgForWeb.IndexOf("</fromusername>") - strMsgForWeb.IndexOf("<fromusername>") - "</fromusername>".Length + 1);
                        }
                    }

                    if (!string.IsNullOrWhiteSpace(strUsrName))
                    {
                        Regex re = new Regex(@"(<!\[CDATA\[(.*)\]\]>)", RegexOptions.IgnoreCase | RegexOptions.Compiled);
                        Match m = re.Match(strUsrName);
                        if (m.Success)
                        {
                            strUsrName = strUsrName.Replace("<![CDATA[", "").Replace("]]>", "");
                        }
                    }
                }
                catch { }
                strNickName = strUsrName;
            }

            if (values.Length > 1)
            {
                if (strUsrName.Length == 0 && values.Length == 3 && values[0].Contains(":"))
                {
                    string[] arrV = values[0].ToString().Split(':');
                    strUsrName = arrV[0].ToString();
                }
                else if (strUsrName.Length == 0 && values[0].EndsWith(":"))
                {
                    strUsrName = values[0].TrimEnd(':');
                    strNickName = strUsrName;
                }
                else if (strUsrName.Length == 0 && values[1].EndsWith(":"))
                {
                    strUsrName = values[1].TrimEnd(':');
                    strNickName = strUsrName;
                }
                //else
                // Common.Log(string.Format("WechatHelperPC's AnalysetGroup >>>>> 用户昵称: {0}>>>>>>{1}", values[0], values[1]));
                //Common.Log(string.Format("{2}  {0}  {1}", values[0], values[1], "wx显示用户昵称"));

                string strMsgTemp = string.Empty;

                for (int index = 0; index <= values.Length - 1; index++)
                {
                    if (index == 0 && !values[0].Contains("<msg>"))//兼容第一行数据包含msg 如果没有没有添加进去 xml解析会失败
                        continue;

                    if (string.IsNullOrEmpty(strMsgTemp))
                        strMsgTemp = values[index];
                    else
                        strMsgTemp = strMsgTemp + "\r\n" + values[index];
                }

                if (strMsgTemp.Length > 0)
                    strMsgForWeb = strMsgTemp;
            }

            if (!string.IsNullOrWhiteSpace(strUsrName))
            {
                //兼容wxid_ 前有其他字符
                if (strUsrName.Contains("wxid_"))
                    strUsrName = strUsrName.Substring(strUsrName.IndexOf("wxid_"));

                //兼容 wxid开头有 \b
                if (strUsrName.StartsWith("\b"))
                    strUsrName = strUsrName.Substring(strUsrName.IndexOf("\b") + 1);


                strUsrName = WechatHeplerPC.ReplaceImproperCharacter(strUsrName);
            }

            if (!string.IsNullOrWhiteSpace(strNickName))
            {
                if (strNickName.Contains("wxid_"))
                    strNickName = strNickName.Substring(strNickName.IndexOf("wxid_"));

                strNickName = WechatHeplerPC.ReplaceImproperCharacter(strNickName);

                strWXID = strNickName;
            }


            if (DicWeChatFriendInfo != null)
            {
                // 好友备注-群备注-昵称-wx号
                string strFRName = "";
                string strGRName = "";
                string strNName = "";

                if (!string.IsNullOrWhiteSpace(strUsrName) && DicWeChatFriendInfo.ContainsKey(strUsrName))
                {
                    WeChatFriendInfo weInfo = DicWeChatFriendInfo[strUsrName];
                    strNName = weInfo.StrNickName;
                    strFRName = weInfo.StrRemark;

                    wxAccount = weInfo.StrInnerAccount;
                    if (String.IsNullOrEmpty(wxAccount))
                    {
                        wxAccount = strUsrName;
                    }
                }
                //foreach (KeyValuePair<string, WeChatFriendInfo> weInfo in DicWeChatFriendInfo)
                //{
                //    if (weInfo.Value.StrWXID.Equals(strUsrName, StringComparison.InvariantCultureIgnoreCase))
                //    {

                //        strNName = weInfo.Value.StrNickName;
                //        strFRName = weInfo.Value.StrRemark;
                //        break;
                //    }
                //}                

                strGRName = GetChatRoomNickName(strChatRoomName, strWXID, lstWeChatGroupFriendInfo);

                if (!IniSetting.GetWeChatExportRemarkName())
                {
                    if (!string.IsNullOrEmpty(strFRName))
                        strNickName = strFRName;
                    else if (!string.IsNullOrEmpty(strGRName))
                        strNickName = strGRName;
                    else
                        strNickName = strNName;
                }
                else
                {
                    if (!string.IsNullOrEmpty(strFRName))
                        strNickName = strFRName;
                    else if (!string.IsNullOrEmpty(strGRName))
                        strNickName = strGRName;
                    else
                        strNickName = strNName;
                }
                //------------------------------       
                strPetName = strNName;
            }

            if (string.IsNullOrEmpty(strNickName))
                strNickName = strUsrName;
        }

        // Added by Utmost on 2020.06.03

        public static XmlDocument LoadXmlStrings(string xmlStrings)
        {
            XmlDocument xmlDoc = null;
            XmlReaderSettings xmlSet = new XmlReaderSettings();
            xmlSet.XmlResolver = null;
            xmlSet.CheckCharacters = false;
            xmlSet.IgnoreComments = true;
            xmlSet.IgnoreWhitespace = true;
            xmlSet.ValidationType = ValidationType.None;

            try
            {
                using (MemoryStream ms = new MemoryStream(Encoding.UTF8.GetBytes(xmlStrings)))
                {
                    xmlDoc = new XmlDocument();
                    using (XmlReader xr = XmlReader.Create(ms, xmlSet))
                    {
                        xmlDoc.Load(xr);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHelperPC>>>>>>LoadXmlStrings");

                Common.Log(string.Format("error xml:{0}", xmlStrings), true);

                xmlDoc = null;
            }

            return xmlDoc;
        }

        public static string ParseWechatMsgId(string msgId)
        {
            const string specailString = "('`$@%&#!{}[]~-=+_,.;^)";
            StringBuilder id = new StringBuilder();
            foreach (char c in msgId)
            {
                if ((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') ||
                    specailString.Contains(c) || (c >= '0' && c <= '9') ||
                    c > 127)
                {
                    id.Append(c);
                }
            }
            return id.ToString();
        }

        public static string ReplaceHexadecimalSymbols(string txt)
        {
            string r = "[\x00-\x08\x0B\x0C\x0E-\x1F]"; // '&' = 0x26
            return Regex.Replace(txt, r, "", RegexOptions.Compiled);
        }

        public static bool IsNoneValidChars(string text)
        {
            foreach (char c in text)
            {
                int dec = Convert.ToInt32(c);
                if ((dec >= 0 && dec <= 31) ||
                    dec == 34 || dec == 42 || dec == 47 ||
                    dec == 58 || dec == 60 || dec == 62 || dec == 63 ||
                    dec == 92 || dec == 124 || dec == 127)
                {
                    return true;
                }
            }
            return false;
        }
        //////////////////////////////////////////////////////////////////////////////////


        public static string GetChatRoomNickName(string strChatRoomName, string strWXID, List<WeChatGroupFriendInfo> lstWeChatGroupFriendInfo)
        {
            string strResult = "";
            try
            {
                if (lstWeChatGroupFriendInfo != null && lstWeChatGroupFriendInfo.Count > 0)
                {
                    WeChatGroupFriendInfo[] aryGroups = lstWeChatGroupFriendInfo.ToArray();
                    foreach (WeChatGroupFriendInfo item in aryGroups)
                    {
                        if (strChatRoomName.Equals(item.StrChatRoomName, StringComparison.InvariantCultureIgnoreCase))
                        {
                            if (item.LstRoomData == null) return strResult;

                            WeChatFriendInfo[] aryFriends = item.LstRoomData.ToArray();
                            foreach (WeChatFriendInfo winfo in aryFriends)
                            {
                                if (winfo.StrWXID.Equals(strWXID, StringComparison.InvariantCultureIgnoreCase))
                                {
                                    strResult = winfo.StrNickName;

                                    return strResult;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetChatRoomNickName");
            }

            return strResult;
        }

        #region "--- 处理不可见字符 ---"

        private static List<char> mLstIllegalASCII = null;
        /// <summary>
        /// 不可见字符  http://ascii.911cha.com/
        /// </summary>
        /// <value></value>
        /// <returns></returns>
        /// <remarks></remarks>
        public static List<char> LstIllegalASCII
        {
            get
            {
                if (mLstIllegalASCII == null)
                {
                    mLstIllegalASCII = new List<char>();
                    mLstIllegalASCII.Add(Convert.ToChar(0));
                    mLstIllegalASCII.Add(Convert.ToChar(1));
                    mLstIllegalASCII.Add(Convert.ToChar(2));
                    mLstIllegalASCII.Add(Convert.ToChar(3));
                    mLstIllegalASCII.Add(Convert.ToChar(4));
                    mLstIllegalASCII.Add(Convert.ToChar(5));
                    mLstIllegalASCII.Add(Convert.ToChar(6));
                    mLstIllegalASCII.Add(Convert.ToChar(7));
                    mLstIllegalASCII.Add(Convert.ToChar(8));
                    mLstIllegalASCII.Add(Convert.ToChar(9));
                    mLstIllegalASCII.Add(Convert.ToChar(10));
                    mLstIllegalASCII.Add(Convert.ToChar(11));
                    mLstIllegalASCII.Add(Convert.ToChar(12));
                    mLstIllegalASCII.Add(Convert.ToChar(13));
                    mLstIllegalASCII.Add(Convert.ToChar(14));
                    mLstIllegalASCII.Add(Convert.ToChar(15));
                    mLstIllegalASCII.Add(Convert.ToChar(16));
                    mLstIllegalASCII.Add(Convert.ToChar(17));
                    mLstIllegalASCII.Add(Convert.ToChar(18));
                    mLstIllegalASCII.Add(Convert.ToChar(19));
                    mLstIllegalASCII.Add(Convert.ToChar(20));
                    mLstIllegalASCII.Add(Convert.ToChar(21));
                    mLstIllegalASCII.Add(Convert.ToChar(22));
                    mLstIllegalASCII.Add(Convert.ToChar(23));
                    mLstIllegalASCII.Add(Convert.ToChar(24));
                    mLstIllegalASCII.Add(Convert.ToChar(25));
                    mLstIllegalASCII.Add(Convert.ToChar(26));
                    mLstIllegalASCII.Add(Convert.ToChar(27));
                    mLstIllegalASCII.Add(Convert.ToChar(28));
                    mLstIllegalASCII.Add(Convert.ToChar(29));
                    mLstIllegalASCII.Add(Convert.ToChar(30));
                    mLstIllegalASCII.Add(Convert.ToChar(31));
                    mLstIllegalASCII.Add(Convert.ToChar(127));
                }
                return mLstIllegalASCII;
            }
        }


        public static string ReplaceImproperCharacter(string strValue)
        {
            string strRelust = strValue;
            try
            {
                foreach (char item in LstIllegalASCII)
                {
                    strRelust = strRelust.Replace(item.ToString(), "");
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ReplaceImproperCharacter");
            }
            return strRelust;
        }

        #endregion

        #region "--- 语音转换 ---"

        public static Image GetHeadIconFromPC(WechatTalk item, WechatAccInfo currentWAInfo)
        {
            Image img = null;
            try
            {
                WeChatFriendInfo info = null;
                if (currentWAInfo.DicWeChatFriendInfo.ContainsKey(item.UserName))
                {
                    info = currentWAInfo.DicWeChatFriendInfo[item.UserName];
                }
                if (info == null)
                    return img;

                string imgPath = string.Format("{0}\\HeadIcon\\{1}\\{2}.pic", Folder.CacheFolder, info.StrWXID, info.StrWXID);

                if (File.Exists(imgPath))
                {
                    img = Utility.GetImageFormFile(imgPath, true);
                }
                //如果图片无法使用就删除掉，下次进来还会重新加载
                if (img == null)
                {
                    try
                    {
                        if (Common.IsTestMode() && File.Exists(imgPath))
                        {
                            File.Delete(imgPath);
                        }
                    }
                    catch { }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetHeadIconFromPC");
            }
            return img;
        }

        public static string GetHeadIconUrl(string wxid, WechatAccInfo currentWAInfo)
        {
            string strUrl = "";
            try
            {
                WeChatFriendInfo info = null;
                if (currentWAInfo.DicWeChatFriendInfo.ContainsKey(wxid))
                {
                    info = currentWAInfo.DicWeChatFriendInfo[wxid];
                    strUrl = info.StrHeadImgUrl;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetHeadIconUrl");
            }
            return strUrl;
        }

        public static string GetHeadIconPathFromPC(string wxid, WechatAccInfo currentWAInfo)
        {
            string imgPath = "";
            try
            {
                WeChatFriendInfo info = null;
                if (currentWAInfo.DicWeChatFriendInfo.ContainsKey(wxid))
                {
                    info = currentWAInfo.DicWeChatFriendInfo[wxid];
                }

                if (info == null)
                {
                    return imgPath;
                }

                string imgTempPath = Path.Combine(Folder.CacheFolder, string.Format("HeadIcon\\{0}\\{1}.pic", info.StrWXID, info.StrWXID));

                if (File.Exists(imgTempPath))
                {
                    imgPath = imgTempPath;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetHeadIconPathFromPC");
            }
            return imgPath;
        }

        #endregion

        public static void LogContext(string context)
        {
            try
            {
                string debugLog = Path.Combine(Folder.AppFolder, "debug.dll");
                if (!File.Exists(debugLog))
                    File.Create(debugLog);

                new Thread(new ThreadStart(delegate
                {
                    Common.Log(context);
                })).Start();
            }
            catch { }
        }
    }

    public class ActivateAccountEventArgs : EventArgs
    {

        private bool mIsSucceed = false;
        public bool IsSucceed
        {
            get { return this.mIsSucceed; }
            set { this.mIsSucceed = value; }
        }

        private string mStrMessage = "";
        public string StrMessage
        {
            get { return this.mStrMessage; }
            set { this.mStrMessage = value; }
        }

        private WechatAccInfo mWAInfo = null;
        public WechatAccInfo WAInfo
        {
            get { return this.mWAInfo; }
            set { this.mWAInfo = value; }
        }


        private int mTypeEx = 0;
        public int TypeEx
        {
            get { return this.mTypeEx; }
            set { this.mTypeEx = value; }
        }

        public ActivateAccountEventArgs(bool iss, string strmsg, WechatAccInfo info, int tEx)
        {
            this.mIsSucceed = iss;
            this.mStrMessage = strmsg;
            this.mWAInfo = info;
            this.mTypeEx = tEx;
        }
    }

    public class WechatAccountEventArgs : EventArgs
    {
        private string mStrMessage = "";
        public string StrMessage
        {
            get { return this.mStrMessage; }
            set { this.mStrMessage = value; }
        }

        public WechatAccountEventArgs(string strmsg)
        {
            this.mStrMessage = strmsg;
        }
    }

    public class GetTalkListEventArgs : EventArgs
    {
        private string mStrMessage = "";
        public string StrMessage
        {
            get { return this.mStrMessage; }
            set { this.mStrMessage = value; }
        }

        public GetTalkListEventArgs(string strmsg)
        {
            this.mStrMessage = strmsg;
        }
    }

    public class AssistInfo
    {
        private bool _IsPay = false;
        /// <summary>
        /// 是否付费
        /// </summary>
        public bool IsPay
        {
            get { return _IsPay; }
            set { _IsPay = value; }
        }

        private string _StrOfficialUrl = "";
        /// <summary>
        /// 官方链接
        /// </summary>
        public string StrOfficialUrl
        {
            get { return _StrOfficialUrl; }
            set { _StrOfficialUrl = value; }
        }

        private string _StrChargeUrl = "";
        /// <summary>
        /// 付费链接
        /// </summary>
        public string StrChargeUrl
        {
            get { return _StrChargeUrl; }
            set { _StrChargeUrl = value; }
        }

        private string _StrLinkQQ = "";
        /// <summary>
        /// 联系客服
        /// </summary>
        public string StrLinkQQ
        {
            get { return _StrLinkQQ; }
            set { _StrLinkQQ = value; }
        }

        private string _StrVideoCourse = "";
        /// <summary>
        /// 视频教程
        /// </summary>
        public string StrVideoCourse
        {
            get { return _StrVideoCourse; }
            set { _StrVideoCourse = value; }
        }

        private bool _IsVersionDisabled = false;

        public bool IsVersionDisabled
        {
            get { return _IsVersionDisabled; }
            set { _IsVersionDisabled = value; }
        }

        private string _qrCodeQQ = "";
        /// <summary>
        /// 视频教程
        /// </summary>
        public string qrCodeQQ
        {
            get { return _qrCodeQQ; }
            set { _qrCodeQQ = value; }
        }

        private string _numberQQ = "";
        /// <summary>
        /// 视频教程
        /// </summary>
        public string numberQQ
        {
            get { return _numberQQ; }
            set { _numberQQ = value; }
        }
    }
}
