﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Text;
using System.Data.SQLite3;
using iTong.CoreModule;
using iTong.Components;
using System.Windows.Forms;

namespace iWechatAssistant
{
    public class WechatHeplerDB
    {
        private string mDBPath = "";
        private string mStrAccountPwd = "";
        private string mStrMediaFilePath = "";
        private SQLiteConnection mConn = null;
        private static object mLocker = new object();
        private static WechatHeplerDB mInstance = null;

        public static event EventHandler<GetTalkListEventArgs> GetTalkListHandler;
        public static event EventHandler<EventArgs> OccupiedHandler;

        #region "--- 初始化 ---"

        private static Dictionary<string, WechatHeplerDB> mDictInstances = new Dictionary<string, WechatHeplerDB>(StringComparer.InvariantCultureIgnoreCase);

        private static object _locker = new object();

        public static WechatHeplerDB GetInstance(string strAccountFolder, string strAccountPwd, string strMediaFilePath)
        {
            WechatHeplerDB helper = null;
            bool blnRetry = false;
            string strKey = string.Format("{0};{1}", strAccountFolder, strAccountPwd);
            lock (_locker)
            {
            DO_RETRY:
                try
                {
                    if (!mDictInstances.ContainsKey(strKey))
                    {
                        mDictInstances.Add(strKey, new WechatHeplerDB(strAccountFolder, strAccountPwd, strMediaFilePath));
                    }
                    helper = mDictInstances[strKey];
                }
                catch (Exception)
                {
                    if (!blnRetry)
                    {
                        blnRetry = true;
                        goto DO_RETRY;
                    }
                }
            }
            return helper;
        }

        public WechatHeplerDB(string strAccountFolder, string strAccountPwd, string strMediaFilePath)
        {
            this.mDBPath = strAccountFolder;
            this.mStrAccountPwd = strAccountPwd;
            this.mStrMediaFilePath = strMediaFilePath;
            //创建数据库连接
            this.InitDBConnection();
        }

        private void InitDBConnection()
        {
            try
            {
                this.mConn = SQLiteClass3.CreateConnectionFromFile(this.mDBPath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHelperPC_Init");
            }
        }

        public void CloseDBConnection(string strAccountFolder, string strAccountPwd)
        {
            try
            {
                if (mConn != null)
                    mConn.Close();
                mConn = null;

                string strKey = string.Format("{0};{1}", strAccountFolder, strAccountPwd);
                if (mDictInstances.ContainsKey(strKey))
                {
                    mDictInstances.Remove(strKey);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHelperPC_Init");
            }
        }

        #endregion


        //加载聊天列表
        public Dictionary<string, WechatTalk> GetTalkList(WechatAccInfo waInfo)
        {
            //Common.Log("WechatAccInfo：" + MyJson.SerializeToJsonString(waInfo), "WechatHelperPC_GetTalkList", true);
            bool ret = false;
            //DateTime dtTemp = DateTime.Now;
            Dictionary<string, WechatTalk> dictMessage = new Dictionary<string, WechatTalk>(StringComparer.InvariantCultureIgnoreCase);

            var s1 = System.Diagnostics.Stopwatch.StartNew();

            // sql不能变化DESC， 下面的写法是直接按照从高到底排序的，这样可以不用重新排序，减少加载时间。by zsh
            string strSql = "SELECT A.*,B.[NickName] " +
                            "FROM MsgSegments AS A  " +
                            "LEFT JOIN Session AS B ON A.[UsrName]=B.[talker] " +
                            "ORDER BY EndTime DESC";

            DataTable dt = SQLiteClass3.ExecuteSQL(strSql, this.mConn);
            Common.Log("dbExecute first Finish", "WechatHelperPC_GetTalkList", true);
            if (dt == null)
                return dictMessage;

            if (dt.Rows.Count == 0)
            {
                // 仅处理文字内容
                strSql = "SELECT A.*,B.[NickName] " +
                            "FROM TXTMsgSegments AS A  " +
                            "LEFT JOIN Session AS B ON A.[UsrName]=B.[talker] " +
                            "ORDER BY EndTime DESC";
                dt = SQLiteClass3.ExecuteSQL(strSql, this.mConn);
                Common.Log("dbExecute second Finish", "WechatHelperPC_GetTalkList", true);
                if (dt == null)
                    return dictMessage;
            }

            bool isSaveDb = IniSetting.GetIsTalkSaveDB();

            string directoryBackup = Path.Combine(waInfo.PathOnPC, "BackupFiles");

            Common.Log("directoryBackup：", directoryBackup, true);

            if (Directory.Exists(directoryBackup) && !isSaveDb)
            {
                Common.Log("未启用数据库模式", true);
                string[] filesPath = Directory.GetFiles(directoryBackup, "*.*", SearchOption.AllDirectories);
                long size = 0;
                foreach (string path in filesPath)
                {
                    FileInfo fileInfo = new FileInfo(path);
                    size += fileInfo.Length;
                }
                if (size > 1024 * 1024 * 1024)
                {
                    Common.Log("提示是否开启数据库备份模式", true);
                    DialogResult dr = tbMessageBox.Show(this, "备份文件过大，可能会加载闪退的情况发生，建议采用数据库备份模式。是否立即开启？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information);
                    if (dr == DialogResult.Yes)
                    {
                        Common.Log("开启数据库备份模式", true);
                        IniSetting.SetIsTalkSaveDB(true);

                        isSaveDb = true;
                        //Application.Restart();

                        //return dictMessage;
                    }
                }
            }


            if (dt.Rows.Count > 0)
            {
                Common.Log(string.Format("WechatHelpDB>>>>>GetTalkList()=====>Rows={0} [{1}]", dt.Rows.Count, DateTime.Now.ToLongTimeString()),true);

                decimal index = 0;

                WeChatTalkDB db = WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath);

                string iniPath = Path.GetDirectoryName(waInfo.StrWeChatTalkDBPath);


                foreach (DataRow row in dt.Rows)
                {
                    index++;
                    if (GetTalkListHandler != null)
                        GetTalkListHandler(null, new GetTalkListEventArgs(string.Format("加载好友列表，请耐心等候... {0}%", decimal.Round((index / (decimal)dt.Rows.Count), 2) * 100)));

                    try
                    {
                        WechatTalk talk = new WechatTalk();

                        talk.FilePath = Path.Combine(this.mStrMediaFilePath, Common.GetValue<string>(row["filePath"], string.Empty));//Path.GetDirectoryName(this.mDBPath)
                        talk.UserName = Common.GetValue<string>(row["UsrName"], string.Empty);
                        talk.NickName = Common.GetValue<string>(row["NickName"], string.Empty);
                        talk.TalkId = Common.GetValue<string>(row["talkerId"], string.Empty);
                        talk.StartTime = WechatHeplerPC.ConvertWeixinToPcTime(Common.GetValue<long>(row["StartTime"], 0));
                        talk.EndTime = WechatHeplerPC.ConvertWeixinToPcTime(Common.GetValue<long>(row["EndTime"], 0));
                        talk.OffSet = Common.GetValue<int>(row["OffSet"], 0);
                        talk.Md5 = Common.ToHexString(Common.GetMd5Array(talk.UserName));
                        talk.Length = Common.GetValue<int>(row["Length"], 0);

                        //判断是否需要建表(如果是数据库备份模式，需要提前建表)
                        if (isSaveDb)
                        {
                            HasCreateDB(dictMessage, talk, waInfo.Account, db, iniPath);
                        }

                        ret = talk.LoadMessagesFromFile(waInfo.PwdBuffer, talk.Length, waInfo.Account, isSaveDb, db, iniPath);

                        if (!isSaveDb && ret)
                        {
                            this.TalkMessagesSort(dictMessage, talk);
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "WechatHelperPC_GetTalkList");
                    }
                }

                //// 分离到选择每个wx群保存
                //if (IniSetting.GetIsTalkSaveDB())
                //{
                //    if (GetTalkListHandler != null)
                //        GetTalkListHandler(null, new GetTalkListEventArgs("保存获取的数据到本地缓存，请耐心等候..."));

                //    WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).Commit();
                //    WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).Loaded = true;
                //}

                //// 保存最新一条时间戳
                if (isSaveDb)
                {
                    Common.Log("开始保存对话聊天最新的时间戳", true);
                    foreach (var talk in dictMessage)
                    {
                        try
                        {
                            IniSetting.SetTalkMessageTimeSpan(waInfo.Account, talk.Value.UserName, talk.Value.LastWechatMessageItem.MessageDateTime, Path.Combine(iniPath, "Wix.ini"));
                        }
                        catch (Exception ex)
                        {
                            Common.LogException(ex.ToString());
                        }  
                    }

                    db.Commit();
                    Common.Log("保存对话聊天最新的时间戳完毕", true);
                }
            }

            //TimeSpan timeSpan = DateTime.Now - dtTemp;
            string strType = "聊天记录读到内存时间:";
            if (isSaveDb)
            {
                strType = "聊天记录入库时间:";
            }
            //Common.Log(string.Format("WechatHelpDB>>>>>GetTalkList()=====>{0} {1}s", strType, timeSpan.TotalSeconds));

            s1.Stop();
            Common.Log(string.Format("WechatHelpDB>>>>>GetTalkList()=====>{0} {1}ms", strType, s1.Elapsed.TotalMilliseconds),true);

            return dictMessage;
        }

        /// <summary>
        /// 判断是否需要建好友表
        /// 将好友信息加入字典
        /// </summary>
        /// <param name="dictMessage"></param>
        /// <param name="talk"></param>
        /// <param name="wixId"></param>
        /// <param name="db"></param>
        private void HasCreateDB(Dictionary<string, WechatTalk> dictMessage, WechatTalk talk, string wixId, WeChatTalkDB db, string dbPath)
        {
            try
            {
                if (!dictMessage.ContainsKey(talk.Md5))
                {
                    string iniPath = dbPath;
                    //判断是否需要建表
                    long time = IniSetting.GetTalkMessageTimeSpan(wixId, talk.UserName, Path.Combine(iniPath, "Wix.ini"));
                    if (time <= 0)
                    {
                        db.CreateTable_Messages(talk.Md5, Math.Abs(talk.Md5.GetHashCode()));
                        //db.CreateTable_MediaFiles(talk.Md5);
                    }

                    dictMessage.Add(talk.Md5, talk);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "TalkSaveCache");
            }
        }

        private void TalkSaveDB(Dictionary<string, WechatTalk> dictMessage, WechatTalk talk, WechatAccInfo waInfo)
        {
            try
            {
                List<WechatMessageItem> listTemp = null;
                WechatTalk talkResult = null;
                if (dictMessage.ContainsKey(talk.Md5))
                {
                    ////已经存在数据 先查数据库把之前数据加载出来
                    //talkResult = dictMessage[talk.Md5];
                    ////talkResult.MessageItems = WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).GetTalkInfo(talk.Md5);

                    //if (DateTime.Compare(talkResult.StartTime, talk.StartTime) > 0)
                    //{
                    //    talkResult.StartTime = talk.StartTime;
                    //}
                    //if (DateTime.Compare(talkResult.EndTime, talk.EndTime) < 0)
                    //{
                    //    talkResult.EndTime = talk.EndTime;
                    //    talkResult.LastChatText = talk.LastChatText;
                    //}

                    ////从晚到早排序，里面默认是从早到晚，所以需要倒序下
                    //if (talkResult.MessageItems == null)
                    //{
                    //    talkResult.MessageItems.AddRange(talk.MessageItems);
                    //}
                    //else if (talk.MessageItems != null)
                    //{
                    //    // 需求是从晚到早排序，后续取出来的talk都是比较早的，所以直接追加在后面即可
                    //    listTemp = new List<WechatMessageItem>();
                    //    listTemp.AddRange(talkResult.MessageItems);
                    //    for (int i = talk.MessageItems.Count - 1; i >= 0; i--)
                    //    {
                    //        listTemp.Add(talk.MessageItems[i]);
                    //    }
                    //    talkResult.MessageItems.Clear();
                    //    talkResult.MessageItems.AddRange(listTemp);
                    //    listTemp.Clear();
                    //}
                }
                else
                {
                    ////没有存在数据 从晚到早排序，里面默认是从早到晚，所以需要倒序下   
                    //listTemp = new List<WechatMessageItem>();
                    //for (int i = talk.MessageItems.Count - 1; i >= 0; i--)
                    //{
                    //    listTemp.Add(talk.MessageItems[i]);
                    //}
                    //talk.MessageItems.Clear();
                    //talk.MessageItems.AddRange(listTemp);

                    //listTemp.Clear();
                    dictMessage.Add(talk.Md5, talk);
                }
                ////加载过聊天记录的就不重复加载了
                //if (!WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).Loaded)
                //{
                //    // 插入数据库 已经存在先删除 然后全部重新插入
                //    WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).InsertTalkInfo(talk.Md5, new List<WechatMessageItem>(talk.MessageItems.ToArray()));
                //}

                //if (talkResult != null && talkResult.MessageItems != null)
                //    talkResult.MessageItems.Clear();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "TalkSaveCache");
            }
        }

        private void TalkMessagesSort(Dictionary<string, WechatTalk> dictMessage, WechatTalk talk)
        {
            try
            {
                WechatTalk talkResult = null;
                if (dictMessage.ContainsKey(talk.Md5))
                {
                    talkResult = dictMessage[talk.Md5];

                    if (DateTime.Compare(talkResult.StartTime, talk.StartTime) > 0)
                    {
                        talkResult.StartTime = talk.StartTime;
                    }
                    if (DateTime.Compare(talkResult.EndTime, talk.EndTime) < 0)
                    {
                        talkResult.EndTime = talk.EndTime;
                        talkResult.LastChatText = talk.LastChatText;
                    }

                    if (talkResult.MessageItems == null)
                    {
                        talkResult.MessageItems = new List<WechatMessageItem>();
                        talkResult.MessageItems.AddRange(talk.MessageItems);
                    }
                    else if (talk.MessageItems != null)
                    {
                        // 需求是从晚到早排序，后续取出来的talk都是比较早的，所以直接追加在后面即可
                        // 优化......
                        WechatMessageItem[] aryItems = talk.MessageItems.ToArray();
                        List<WechatMessageItem> listMsgItems = new List<WechatMessageItem>();
                        for (int i = aryItems.Length - 1; i >= 0; i--)
                        {

                            // 优化 on 2020.06.02
                            listMsgItems.Add(aryItems[i]);

                            // 循环设置属性值
                            //talkResult.MessageItems.Add(aryItems[i]);
                        }
                        // 一次设置属性值
                        talkResult.MessageItems.AddRange(listMsgItems);
                        listMsgItems.Clear();
                    }
                }
                else
                {
                    // 需求是从晚到早排序，里面默认是从早到晚，所以需要倒序下
                    // 优化......
                    WechatMessageItem[] aryItems = talk.MessageItems.ToArray();
                    talk.MessageItems.Clear();
                    List<WechatMessageItem> listMsgTemps = new List<WechatMessageItem>();
                    for (int i = aryItems.Length - 1; i >= 0; i--)
                    {
                        // 循环设置属性值
                        //talk.MessageItems.Add(aryItems[i]);

                        // 优化 on 2020.06.02
                        listMsgTemps.Add(aryItems[i]);
                    }
                    // 一次设置属性值
                    talk.MessageItems.AddRange(listMsgTemps);
                    listMsgTemps.Clear();
                    dictMessage.Add(talk.Md5, talk);

                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHelper_MessagesSort");
            }
        }

        //加载媒体文件
        public int FileLength_ = 0;

        private readonly object thisLock_ = new object();
        private MemoryStream ReadMediaBuffer(DataTable dt, out int intMapKey)
        {
            intMapKey = 0;
            MemoryStream ms = new MemoryStream();

            try
            {
                foreach (DataRow row in dt.Rows)
                {
                    string strFileName = Common.GetValue<string>(row["FileName"], string.Empty);
                    int intOffSet = Common.GetValue<int>(row["OffSet"], 0);
                    int intLength = Common.GetValue<int>(row["Length"], 0);
                    intMapKey = Common.GetValue<int>(row["MapKey"], 0);

                    byte[] buffer = GetMediaBuffer(Path.Combine(mStrMediaFilePath, strFileName), intOffSet, intLength);
                    ms.Write(buffer, 0, buffer.Length);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHelperDB_ReadMediaBuffer");
                ms = null;
            }

            return ms;
        }

        private FileStream ReadFileBuffer(DataTable dt, string mediaName, out int intMapKey, out int fsLength)
        {
            string ftemp = Folder.GetTempFilePathByName(mediaName);

            intMapKey = 0;
            fsLength = 0;
            FileStream fs = new FileStream(ftemp, FileMode.Create);

            try
            {
                foreach (DataRow row in dt.Rows)
                {
                    string strFileName = Common.GetValue<string>(row["FileName"], string.Empty);
                    int intOffSet = Common.GetValue<int>(row["OffSet"], 0);
                    int intLength = Common.GetValue<int>(row["Length"], 0);
                    intMapKey = Common.GetValue<int>(row["MapKey"], 0);

                    byte[] buffer = GetMediaBuffer(Path.Combine(mStrMediaFilePath, strFileName), intOffSet, intLength);
                    fs.Write(buffer, 0, buffer.Length);
                    fsLength += buffer.Length;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHelperDB_ReadFileBuffer");
                fs = null;
                fsLength = 0;

            }

            return fs;
        }

        public string GetMediaRecode(string strMediaName, byte[] aryBuffer, out int intMapKey)
        {
            intMapKey = 0;
            string strFilePath = string.Empty;
           
            string strSql = "SELECT MediaId from MsgMedia where MediaIdStr=\"{0}\"";

            //string strSql = "SELECT A.[FileName],A.[OffSet],A.[Length],A.[InnerOffSet],A.[TotalLen],A.[MapKey] " +
            //                "FROM MsgFileSegment AS A " +
            //                "LEFT JOIN MsgMedia AS B ON A.[MapKey]=B.[MediaId] " +
            //                "WHERE B.[MediaIdStr]=\"{0}\" " +
            //                "ORDER BY A.[MapKey] ";

            strSql = string.Format(strSql, strMediaName.Trim());
            DataTable dt = null;
            string mediaId = string.Empty;
            try
            {
                lock (thisLock_)
                {
                    dt = SQLiteClass3.ExecuteSQL(strSql, this.mConn);

                    if (dt == null || dt.Rows == null || dt.Rows.Count == 0)
                    {
                        Common.Log(string.Format("从数据库中导出文件返回1：strFilePath={0}", strFilePath), "WechatHeplerPC", true);
                        return strFilePath;
                    }
                    else
                    {
                        mediaId = Common.GetValue<string>(dt.Rows[0]["MediaId"], string.Empty);
                    }

                    strSql = "SELECT FileName,OffSet,Length,MapKey FROM MsgFileSegment where MapKey = \"{0}\"";
                    strSql = string.Format(strSql, mediaId);
                    dt = null;
                    dt = SQLiteClass3.ExecuteSQL(strSql, this.mConn);

                    if (dt == null || dt.Rows == null || dt.Rows.Count == 0)
                    {
                        Common.Log(string.Format("从数据库中导出文件返回2：strFilePath={0}", strFilePath), "WechatHeplerPC", true);
                        return strFilePath;
                    }
                    int intOffSet = 0;
                    int intLength = 0;
                    string strFileName = "";
                    byte[] arrBufferDecode = null;

                    if (dt.Rows.Count == 1)
                    {
                        foreach (DataRow row in dt.Rows)
                        {
                            strFileName = Common.GetValue<string>(row["FileName"], string.Empty);
                            intOffSet = Common.GetValue<int>(row["OffSet"], 0);
                            intLength = Common.GetValue<int>(row["Length"], 0);
                            intMapKey = Common.GetValue<int>(row["MapKey"], 0);
                        }

                        byte[] buffer = GetMediaBuffer(Path.Combine(mStrMediaFilePath, strFileName), intOffSet, intLength);
                        arrBufferDecode = AESFunctionHelper.AES_decrypt(buffer, aryBuffer, System.Security.Cryptography.PaddingMode.Zeros);

                        if (FileLength_ % 2 != 0 && FileLength_ < 1024 && intLength < 1024)
                        {
                            for (int i = FileLength_; i < intLength; i++)
                            {
                                arrBufferDecode[i] = (byte)0;
                            }
                        }
                    }
                    else
                    {
                        //using (MemoryStream ms = new MemoryStream())
                        //{
                        //    foreach (DataRow row in dt.Rows)
                        //    {
                        //        strFileName = Common.GetValue<string>(row["FileName"], string.Empty);
                        //        intOffSet = Common.GetValue<int>(row["OffSet"], 0);
                        //        intLength = Common.GetValue<int>(row["Length"], 0);
                        //        intMapKey = Common.GetValue<int>(row["MapKey"], 0);

                        //        byte[] buffer = GetMediaBuffer(Path.Combine(mStrMediaFilePath, strFileName), intOffSet, intLength);
                        //        ms.Write(buffer, 0, buffer.Length);
                        //    }

                        //    ms.Position = 0;
                        //    arrBufferDecode = AESFunctionHelper.AES_decrypt(ms.GetBuffer(), aryBuffer, System.Security.Cryptography.PaddingMode.Zeros);
                        //}

                        // Added by Utmost20200715
                        using (MemoryStream ms = ReadMediaBuffer(dt, out intMapKey))
                        {
                            if (ms != null)
                            {
                                ms.Position = 0;
                                arrBufferDecode = AESFunctionHelper.AES_decrypt(ms.GetBuffer(), aryBuffer, System.Security.Cryptography.PaddingMode.Zeros);
                                ms.Close();
                            }
                            else
                            {
                                // 使用MemoryStream出现OutofMemory错误，则使用文件模式
                                int fsLength = 0;
                                using (FileStream fsMedia = ReadFileBuffer(dt, strMediaName, out intMapKey, out fsLength))
                                {
                                    if (fsLength != 0 && fsMedia != null)
                                    {
                                        string ftemp = Folder.GetTempFilePathByName(strMediaName);
                                        byte[] fsBuffer = new byte[fsLength];
                                        fsMedia.Read(fsBuffer, 0, fsLength);
                                        arrBufferDecode = AESFunctionHelper.AES_decrypt(fsBuffer, aryBuffer, System.Security.Cryptography.PaddingMode.Zeros);

                                        fsMedia.Close();
                                    }
                                }
                            }
                        }
                    }
                    string strMediaFolder = Path.Combine(Path.GetDirectoryName(this.mDBPath), "Media");
                    Folder.CheckFolder(strMediaFolder);

                    strFilePath = Path.Combine(strMediaFolder, intMapKey.ToString());
                    using (FileStream fs = new FileStream(strFilePath, FileMode.Create))
                    {
                        fs.Write(arrBufferDecode, 0, arrBufferDecode.Length);
                    }

                    arrBufferDecode = null;
                }
            }
            catch (Exception ex)
            {
                Common.Log(string.Format("从数据库中导出文件返回异常：{0}", ex.ToString()), "WechatHeplerPC", true);
                Common.LogException(ex.ToString(), "WechatHelperDB_GetMediaRecode");
            }
            finally
            {
                if (dt != null)
                {
                    dt.Dispose();
                    dt = null;
                }
            }

            return strFilePath;
        }

        private MemoryStream ReadMediaBuffer(DataTable dt, int intMapKey)
        {
            throw new NotImplementedException();
        }

        private byte[] GetMediaBuffer(string strFilePath, int intOffset, int intLength)
        {
            byte[] arrBuffer = new byte[intLength];
            try
            {
                using (FileStream fs = new FileStream(strFilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    fs.Seek(intOffset, SeekOrigin.Begin);
                    fs.Read(arrBuffer, 0, intLength);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetMediaBuffer");
            }
            return arrBuffer;
        }

        //加载备份信息
        public BackConfigInfo GetWechatConfig()
        {
            BackConfigInfo bcMast = null;
            try
            {
                string strSql = "SELECT * FROM Config";
                DataTable dt = SQLiteClass3.ExecuteSQL(strSql, this.mConn);
                if (dt.Rows.Count > 0)
                {
                    byte[] bufferConfig = Common.GetValue<byte[]>(dt.Rows[0]["Buf"], null);
                    if (bufferConfig != null)
                    {
                        using (MemoryStream ms = new MemoryStream())
                        {
                            try
                            {
                                ms.Write(bufferConfig, 0, bufferConfig.Length);
                                ms.Position = 0;
                                bcMast = ProtoBuf.Serializer.Deserialize<BackConfigInfo>(ms);
                            }
                            catch (Exception)
                            {
                            }
                            //finally
                            //{
                            //    ms.Dispose();
                            //}
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetWechatConfig");
            }
            return bcMast;
        }

    }
}
