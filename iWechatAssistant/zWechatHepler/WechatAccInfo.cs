﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Text;
using iTong.CoreFoundation;
using System.IO;
using ProtoBuf;

namespace iWechatAssistant
{
    public class WechatAccInfo
    {
        private string mAccount = string.Empty;
        private string mInnerAccount = string.Empty;
        private string mName = string.Empty;
        private string mIconUrl = string.Empty;
        private Image mIcon = null;
        private string mPassword = string.Empty;
        private byte[] mPwdBuffer = null;
        private string mIconFilePath = string.Empty;
        private string mPathOnPC = string.Empty;                            //在电脑文件夹位置。
        private List<WeChatAccBackupInfo> mLstWCABInfo = null;
        private string mSelectedDeviceName = string.Empty;
        private string mStrBackupFilesPath = string.Empty;
        private DateTime mdtEndTime = DateTime.Now;//DateTime.MinValue ;
        private ActivateState mAState = ActivateState.Nonactivated;
        private string mStrFriendlistUrl = "";
        private string mStrWeChatTalkDBPath = "";
        private string mStrFriendlistPCPath = "";
        private Dictionary<string, WeChatFriendInfo> mDicWeChatFriendInfo = new Dictionary<string, WeChatFriendInfo>(StringComparer.InvariantCultureIgnoreCase);
        private List<WeChatGroupFriendInfo> mLstWeChatGroupFriendInfo;
        private int mIntTypeEx = 0;


        public int TypeEx
        {
            get { return this.mIntTypeEx; }
            set { this.mIntTypeEx = value; }
        }

        public string StrWeChatTalkDBPath
        {
            get { return mStrWeChatTalkDBPath; }
            set { mStrWeChatTalkDBPath = value; }
        }

        public string StrBackupFilesPath
        {
            get { return mStrBackupFilesPath; }
            set { mStrBackupFilesPath = value; }
        }

        public DateTime EndTime
        {
            get { return mdtEndTime; }
            set { mdtEndTime = value; }
        }

        public ActivateState AState
        {
            get { return mAState; }
            set { mAState = value; }
        }

        public List<WeChatAccBackupInfo> LstWCABInfo
        {
            get { return mLstWCABInfo; }
            set { mLstWCABInfo = value; }
        }

        /// <summary>
        /// 当前选中的设备名字，每次切换都要对应切换
        /// </summary>
        public string SelectedDeviceUDID
        {
            get { return mSelectedDeviceName; }
            set { mSelectedDeviceName = value; }
        }

        public string Account
        {
            get { return this.mAccount; }
            set { this.mAccount = value; }
        }

        public string InnerAccount
        {
            get { return this.mInnerAccount; }
            set { this.mInnerAccount = value; }
        }

        public string Name
        {
            get { return this.mName; }
            set { this.mName = value; }
        }

        public string IconUrl
        {
            get { return this.mIconUrl; }
            set { this.mIconUrl = value; }
        }

        public Image Icon
        {
            get { return this.mIcon; }
            set { this.mIcon = value; }
        }

        public string Password
        {
            get { return this.mPassword; }
            set
            {
                this.mPwdBuffer = null;
                this.mPassword = value;
            }
        }

        public byte[] PwdBuffer
        {
            get
            {
                try
                {
                    if (this.mPwdBuffer == null)
                    {
                        byte[] bb = Encoding.UTF8.GetBytes(this.mPassword);
                        this.mPwdBuffer = new Byte[16];
                        Array.Copy(bb, this.mPwdBuffer, 16);
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "WechatAccInfo_GetPwdError");
                }
                return this.mPwdBuffer;
            }
        }

        public string IconFilePath
        {
            get
            {
                if (string.IsNullOrEmpty(this.mIconFilePath))
                {
                    this.mIconFilePath = Path.Combine(Folder.TempFolder, Path.GetFileName(this.mIconUrl) + this.Account);
                }
                return this.mIconFilePath;
            }
        }

        public string PathOnPC
        {
            get { return this.mPathOnPC; }
            set
            {
                this.mPathOnPC = value;
            }
        }

        public string FriendlistURL
        {
            get { return mStrFriendlistUrl; }
            set { mStrFriendlistUrl = value; }
        }

        public string FriendlistPCPath
        {
            get { return mStrFriendlistPCPath; }
            set { mStrFriendlistPCPath = value; }
        }

        public Dictionary<string, WeChatFriendInfo> DicWeChatFriendInfo
        {
            get { return mDicWeChatFriendInfo; }
            set { mDicWeChatFriendInfo = value; }
        }

        public WeChatAccBackupInfo GetSelectedDevice()
        {
            WeChatAccBackupInfo result = null;

            if (this.mLstWCABInfo == null || this.mLstWCABInfo.Count == 0)
            {
                goto DoExit;
            }
            if (this.SelectedDeviceUDID.Length == 0)
            {
                result = this.mLstWCABInfo[0];
                goto DoExit;
            }

            foreach (WeChatAccBackupInfo item in this.mLstWCABInfo)
            {
                //if (string.Compare(item.DeviceUDID, this.mSelectedDeviceName) == 0)
                if (mSelectedDeviceName.Equals(item.DeviceUDID, StringComparison.InvariantCultureIgnoreCase))
                {
                    result = item;
                    break;
                }
            }
            if (result == null)
            {
                result = this.mLstWCABInfo[0];
                goto DoExit;
            }


        DoExit:
            if (result != null)
            {
                this.SelectedDeviceUDID = result.DeviceUDID;
            }
            return result;
        }

        public List<WeChatGroupFriendInfo> LstWeChatGroupFriendInfo
        {
            get { return mLstWeChatGroupFriendInfo; }
            set { mLstWeChatGroupFriendInfo = value; }
        }

    }

    public class WeChatGroupFriendInfo
    {

        private string _strChatRoomName = "";

        public string StrChatRoomName
        {
            get { return _strChatRoomName; }
            set { _strChatRoomName = value; }
        }

        private string _strReserved2 = "";

        public string StrReserved2
        {
            get { return _strReserved2; }
            set { _strReserved2 = value; }
        }

        private List<WeChatFriendInfo> _lstRoomData = new List<WeChatFriendInfo>();

        public List<WeChatFriendInfo> LstRoomData
        {
            get { return _lstRoomData; }
            set { _lstRoomData = value; }
        }

        private string _strSelfDisplayName = "";

        public string StrSelfDisplayName
        {
            get { return _strSelfDisplayName; }
            set { _strSelfDisplayName = value; }
        }

    }

    public class WeChatFriendInfo
    {
        private string _StrWXID = "";
        public string StrWXID
        {
            get { return _StrWXID; }
            set { _StrWXID = value; }
        }

        private string _StrNickName = "";
        public string StrNickName
        {
            get { return _StrNickName; }
            set { _StrNickName = value; }
        }

        private string _StrRemark = "";
        public string StrRemark
        {
            get { return _StrRemark; }
            set { _StrRemark = value; }
        }

        private string _StrInnerAccount = "";
        public string StrInnerAccount
        {
            get { return _StrInnerAccount; }
            set { _StrInnerAccount = value; }
        }

        private string _StrHeadImgUrl = "";
        public string StrHeadImgUrl
        {
            get { return _StrHeadImgUrl; }
            set { _StrHeadImgUrl = value; }
        }

        private string _StrHeadImgPath = "";
        public string StrHeadImgPath
        {
            get { return _StrHeadImgPath; }
            set { _StrHeadImgPath = value; }
        }
    }

    public enum ActivateState
    {
        Normal = 0,
        Overdue = 1,
        Nonactivated = 2
    }

    public class WeChatAccBackupInfo
    {
        private string _StrSavePath = "";
        public string SavePath
        {
            get { return _StrSavePath; }
            set { _StrSavePath = value; }
        }

        private string _StrTempPath = "";
        public string TempSavePath
        {
            get { return _StrTempPath; }
            set { _StrTempPath = value; }
        }

        private string _StrMediaFilePath = "";
        public string MediaFilePath
        {
            get { return _StrMediaFilePath; }
            set { _StrMediaFilePath = value; }
        }

        private string _StrDeviceName = "";

        public string DeviceName
        {
            get { return _StrDeviceName; }
            set { _StrDeviceName = value; }
        }
        private String _StrDeviceUDID = "";

        public String DeviceUDID
        {
            get { return _StrDeviceUDID; }
            set { _StrDeviceUDID = value; }
        }
        private BackConfigInfo _BCMast = null;

        public BackConfigInfo BCConfigInfo
        {
            get { return _BCMast; }
            set { _BCMast = value; }
        }
    }

    [ProtoContract]
    public class WeChatFriendInfoModel
    {
        // 添加特性，表示该字段可以被序列化，1可以理解为下标  
        [ProtoMember(1)]
        public List<FriendInfoModel> lstFriendInfoModel;
    }

    [ProtoContract]
    public class FriendInfoModel
    {
        //添加特性，表示该字段可以被序列化，1可以理解为下标  
        [ProtoMember(1)]
        public string strWXID;
        [ProtoMember(2)]
        public string strNickname;
    }


}
