﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace iWechatAssistant
{
    public class AccountIniSetting
    {
        public static void SetBackupkey(string strKey, string strValue)
        {
            strKey = Utility.EncryptDES(strKey, "gh193#qw", "5h1j#ek3").Replace("=", "_@_");
            strValue = string.Format("{0}^{1}", Utility.EncryptDES(strValue, "ea534#qw", "3g4i#ek3"), Utility.EncryptDES(DateTime.Now.ToString("yyyy-MM-dd"), "gh182#qw", "5h1j#ee3"));
            IniClass.SetIniSectionKey("Account", strKey, strValue, Folder.AccountIniFile);
        }

        public static string GetBackupkey(string strKey)
        {
            strKey = Utility.EncryptDES(str<PERSON><PERSON>, "gh193#qw", "5h1j#ek3").Replace("=", "_@_");
            string strValue = "";
            string strTemp = IniClass.GetIniSectionKey("Account", strKey, Folder.AccountIniFile);
            if (strTemp.Length > 0 && strTemp.Contains("^"))
            {
                string[] arrTemp = strTemp.Split('^');
                strValue = Utility.DecryptDES(arrTemp[0], "ea534#qw", "3g4i#ek3");
            }
            return strValue;
        }

        public static DateTime GetBackupTime(string strKey)
        {
            DateTime dtValue = DateTime.Now;
            try
            {
                string strTemp = IniClass.GetIniSectionKey("Account", strKey, Folder.AccountIniFile);
                if (strTemp.Length > 0 && strTemp.Contains("^"))
                {
                    string[] arrTemp = strTemp.Split('^');
                    dtValue = Convert.ToDateTime(Utility.DecryptDES(arrTemp[1], "gh182#qw", "5h1j#ee3"));
                }
            }
            catch { }

            return dtValue;
        }
        
        public static void DeleteBackupkey(string strKey = "")
        {
            if (strKey.Length > 0)
            {
                strKey = Utility.EncryptDES(strKey, "gh193#qw", "5h1j#ek3").Replace("=", "_@_");
                IniClass.DeleteSectionKey("Account", strKey, Folder.AccountIniFile);
            }
            else
            {
                try
                {
                    string[] arrAccount = IniClass.GetIniSectionAllKey("Account", Folder.AccountIniFile);
                    if (arrAccount.Length > 0)
                    {
                        foreach (string item in arrAccount)
                        {
                            try
                            {
                                TimeSpan ts = DateTime.Now - GetBackupTime(item);
                                if (ts.Days > 7)
                                    IniClass.DeleteSectionKey("Account", item, Folder.AccountIniFile);
                            }
                            catch { }
                        }
                    }
                }
                catch { }
            }
        }
    }
}
