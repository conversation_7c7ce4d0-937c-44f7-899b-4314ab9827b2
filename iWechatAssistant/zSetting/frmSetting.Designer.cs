﻿namespace iWechatAssistant
{
    partial class frmSetting
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmSetting));
            this.tbPanel1 = new iTong.Components.tbPanel();
            this.chkDeleteCache = new iTong.Components.tbCheckBox();
            this.cbxArtificialAccredit = new iTong.Components.tbCheckBox();
            this.cbxOldAnalysisWebpage = new iTong.Components.tbCheckBox();
            this.btnDBPath = new iTong.Components.tbButton();
            this.btnDefaultFolder = new iTong.Components.tbButton();
            this.txtDBPath = new iTong.Components.tbTextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.txtDefaultFolder = new iTong.Components.tbTextBox();
            this.label1 = new System.Windows.Forms.Label();
            this.cbxExportRemark = new iTong.Components.tbCheckBox();
            this.cbxLog = new iTong.Components.tbCheckBox();
            this.cbxExcelCoding = new System.Windows.Forms.ComboBox();
            this.lblExportExcel = new System.Windows.Forms.Label();
            this.cbxLoadDB = new iTong.Components.tbCheckBox();
            this.btnOK = new iTong.Components.tbButton();
            this.btnCancel = new iTong.Components.tbButton();
            this.btn_close = new iTong.Components.tbButton();
            this.label3 = new System.Windows.Forms.Label();
            this.cbxExportFilter = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.tbPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tbPanel1
            // 
            this.tbPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.tbPanel1.Controls.Add(this.chkDeleteCache);
            this.tbPanel1.Controls.Add(this.cbxArtificialAccredit);
            this.tbPanel1.Controls.Add(this.cbxOldAnalysisWebpage);
            this.tbPanel1.Controls.Add(this.btnDBPath);
            this.tbPanel1.Controls.Add(this.btnDefaultFolder);
            this.tbPanel1.Controls.Add(this.txtDBPath);
            this.tbPanel1.Controls.Add(this.label2);
            this.tbPanel1.Controls.Add(this.txtDefaultFolder);
            this.tbPanel1.Controls.Add(this.label1);
            this.tbPanel1.Controls.Add(this.cbxExportRemark);
            this.tbPanel1.Controls.Add(this.cbxLog);
            this.tbPanel1.Controls.Add(this.cbxExportFilter);
            this.tbPanel1.Controls.Add(this.cbxExcelCoding);
            this.tbPanel1.Controls.Add(this.label4);
            this.tbPanel1.Controls.Add(this.label3);
            this.tbPanel1.Controls.Add(this.lblExportExcel);
            this.tbPanel1.Controls.Add(this.cbxLoadDB);
            this.tbPanel1.Controls.Add(this.btnOK);
            this.tbPanel1.Controls.Add(this.btnCancel);
            this.tbPanel1.Location = new System.Drawing.Point(1, 37);
            this.tbPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel1.Name = "tbPanel1";
            this.tbPanel1.Size = new System.Drawing.Size(541, 501);
            this.tbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel1.TabIndex = 78;
            this.tbPanel1.tbBackgroundColorShadow = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(255)))));
            this.tbPanel1.tbBackgroundColorShadowString = "0,255,255,255";
            this.tbPanel1.tbBackgroundImage = null;
            this.tbPanel1.tbShowWatermark = false;
            this.tbPanel1.tbSplit = "0,0,0,0";
            this.tbPanel1.tbText = "";
            this.tbPanel1.tbWatermark = null;
            this.tbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel1.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // chkDeleteCache
            // 
            this.chkDeleteCache.Appearance = System.Windows.Forms.Appearance.Button;
            this.chkDeleteCache.BackColor = System.Drawing.Color.Transparent;
            this.chkDeleteCache.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.chkDeleteCache.ForeColor_Checked = System.Drawing.Color.FromArgb(((int)(((byte)(86)))), ((int)(((byte)(86)))), ((int)(((byte)(86)))));
            this.chkDeleteCache.Location = new System.Drawing.Point(55, 401);
            this.chkDeleteCache.Name = "chkDeleteCache";
            this.chkDeleteCache.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.chkDeleteCache.Size = new System.Drawing.Size(144, 22);
            this.chkDeleteCache.TabIndex = 90;
            this.chkDeleteCache.tbAdriftIconWhenHover = false;
            this.chkDeleteCache.tbAutoSize = false;
            this.chkDeleteCache.tbAutoSizeEx = false;
            this.chkDeleteCache.tbIconChecked = ((System.Drawing.Image)(resources.GetObject("chkDeleteCache.tbIconChecked")));
            this.chkDeleteCache.tbIconCheckedMouseDown = null;
            this.chkDeleteCache.tbIconCheckedMouseHover = null;
            this.chkDeleteCache.tbIconCheckedMouseLeave = null;
            this.chkDeleteCache.tbIconCheckedState = iTong.Components.ImageState.FourState;
            this.chkDeleteCache.tbIconHoldPlace = true;
            this.chkDeleteCache.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.chkDeleteCache.tbIconIndeterminate = ((System.Drawing.Image)(resources.GetObject("chkDeleteCache.tbIconIndeterminate")));
            this.chkDeleteCache.tbIconIndeterminateMouseDown = null;
            this.chkDeleteCache.tbIconIndeterminateMouseHover = null;
            this.chkDeleteCache.tbIconIndeterminateMouseLeave = null;
            this.chkDeleteCache.tbIconIndeterminateState = iTong.Components.ImageState.FourState;
            this.chkDeleteCache.tbIconPlaceText = 1;
            this.chkDeleteCache.tbIconUnChecked = ((System.Drawing.Image)(resources.GetObject("chkDeleteCache.tbIconUnChecked")));
            this.chkDeleteCache.tbIconUnCheckedMouseDown = null;
            this.chkDeleteCache.tbIconUnCheckedMouseHover = null;
            this.chkDeleteCache.tbIconUnCheckedMouseLeave = null;
            this.chkDeleteCache.tbIconUnCheckedState = iTong.Components.ImageState.FourState;
            this.chkDeleteCache.tbImageBackground = null;
            this.chkDeleteCache.tbImageBackgroundState = iTong.Components.ImageState.TwoState;
            this.chkDeleteCache.tbImageCheckedMouseDown = null;
            this.chkDeleteCache.tbImageCheckedMouseHover = null;
            this.chkDeleteCache.tbImageCheckedMouseLeave = null;
            this.chkDeleteCache.tbImageUnCheckedMouseDown = null;
            this.chkDeleteCache.tbImageUnCheckedMouseHover = null;
            this.chkDeleteCache.tbImageUnCheckedMouseLeave = null;
            this.chkDeleteCache.tbReadOnly = false;
            this.chkDeleteCache.tbShadow = false;
            this.chkDeleteCache.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.chkDeleteCache.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.chkDeleteCache.tbSplit = "3,3,3,3";
            this.chkDeleteCache.tbToolTip = "";
            this.chkDeleteCache.Text = "启动时清空缓存";
            this.chkDeleteCache.UseVisualStyleBackColor = false;
            // 
            // cbxArtificialAccredit
            // 
            this.cbxArtificialAccredit.Appearance = System.Windows.Forms.Appearance.Button;
            this.cbxArtificialAccredit.BackColor = System.Drawing.Color.Transparent;
            this.cbxArtificialAccredit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.cbxArtificialAccredit.ForeColor_Checked = System.Drawing.Color.FromArgb(((int)(((byte)(86)))), ((int)(((byte)(86)))), ((int)(((byte)(86)))));
            this.cbxArtificialAccredit.Location = new System.Drawing.Point(55, 429);
            this.cbxArtificialAccredit.Name = "cbxArtificialAccredit";
            this.cbxArtificialAccredit.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cbxArtificialAccredit.Size = new System.Drawing.Size(144, 22);
            this.cbxArtificialAccredit.TabIndex = 89;
            this.cbxArtificialAccredit.tbAdriftIconWhenHover = false;
            this.cbxArtificialAccredit.tbAutoSize = false;
            this.cbxArtificialAccredit.tbAutoSizeEx = false;
            this.cbxArtificialAccredit.tbIconChecked = ((System.Drawing.Image)(resources.GetObject("cbxArtificialAccredit.tbIconChecked")));
            this.cbxArtificialAccredit.tbIconCheckedMouseDown = null;
            this.cbxArtificialAccredit.tbIconCheckedMouseHover = null;
            this.cbxArtificialAccredit.tbIconCheckedMouseLeave = null;
            this.cbxArtificialAccredit.tbIconCheckedState = iTong.Components.ImageState.FourState;
            this.cbxArtificialAccredit.tbIconHoldPlace = true;
            this.cbxArtificialAccredit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbxArtificialAccredit.tbIconIndeterminate = ((System.Drawing.Image)(resources.GetObject("cbxArtificialAccredit.tbIconIndeterminate")));
            this.cbxArtificialAccredit.tbIconIndeterminateMouseDown = null;
            this.cbxArtificialAccredit.tbIconIndeterminateMouseHover = null;
            this.cbxArtificialAccredit.tbIconIndeterminateMouseLeave = null;
            this.cbxArtificialAccredit.tbIconIndeterminateState = iTong.Components.ImageState.FourState;
            this.cbxArtificialAccredit.tbIconPlaceText = 1;
            this.cbxArtificialAccredit.tbIconUnChecked = ((System.Drawing.Image)(resources.GetObject("cbxArtificialAccredit.tbIconUnChecked")));
            this.cbxArtificialAccredit.tbIconUnCheckedMouseDown = null;
            this.cbxArtificialAccredit.tbIconUnCheckedMouseHover = null;
            this.cbxArtificialAccredit.tbIconUnCheckedMouseLeave = null;
            this.cbxArtificialAccredit.tbIconUnCheckedState = iTong.Components.ImageState.FourState;
            this.cbxArtificialAccredit.tbImageBackground = null;
            this.cbxArtificialAccredit.tbImageBackgroundState = iTong.Components.ImageState.TwoState;
            this.cbxArtificialAccredit.tbImageCheckedMouseDown = null;
            this.cbxArtificialAccredit.tbImageCheckedMouseHover = null;
            this.cbxArtificialAccredit.tbImageCheckedMouseLeave = null;
            this.cbxArtificialAccredit.tbImageUnCheckedMouseDown = null;
            this.cbxArtificialAccredit.tbImageUnCheckedMouseHover = null;
            this.cbxArtificialAccredit.tbImageUnCheckedMouseLeave = null;
            this.cbxArtificialAccredit.tbReadOnly = false;
            this.cbxArtificialAccredit.tbShadow = false;
            this.cbxArtificialAccredit.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.cbxArtificialAccredit.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.cbxArtificialAccredit.tbSplit = "3,3,3,3";
            this.cbxArtificialAccredit.tbToolTip = "";
            this.cbxArtificialAccredit.Text = "开启人工授权入口";
            this.cbxArtificialAccredit.UseVisualStyleBackColor = false;
            this.cbxArtificialAccredit.Visible = false;
            // 
            // cbxOldAnalysisWebpage
            // 
            this.cbxOldAnalysisWebpage.Appearance = System.Windows.Forms.Appearance.Button;
            this.cbxOldAnalysisWebpage.BackColor = System.Drawing.Color.Transparent;
            this.cbxOldAnalysisWebpage.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cbxOldAnalysisWebpage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.cbxOldAnalysisWebpage.ForeColor_Checked = System.Drawing.Color.FromArgb(((int)(((byte)(86)))), ((int)(((byte)(86)))), ((int)(((byte)(86)))));
            this.cbxOldAnalysisWebpage.Location = new System.Drawing.Point(55, 347);
            this.cbxOldAnalysisWebpage.Name = "cbxOldAnalysisWebpage";
            this.cbxOldAnalysisWebpage.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cbxOldAnalysisWebpage.Size = new System.Drawing.Size(128, 18);
            this.cbxOldAnalysisWebpage.TabIndex = 88;
            this.cbxOldAnalysisWebpage.tbAdriftIconWhenHover = false;
            this.cbxOldAnalysisWebpage.tbAutoSize = false;
            this.cbxOldAnalysisWebpage.tbAutoSizeEx = true;
            this.cbxOldAnalysisWebpage.tbIconChecked = ((System.Drawing.Image)(resources.GetObject("cbxOldAnalysisWebpage.tbIconChecked")));
            this.cbxOldAnalysisWebpage.tbIconCheckedMouseDown = null;
            this.cbxOldAnalysisWebpage.tbIconCheckedMouseHover = null;
            this.cbxOldAnalysisWebpage.tbIconCheckedMouseLeave = null;
            this.cbxOldAnalysisWebpage.tbIconCheckedState = iTong.Components.ImageState.FourState;
            this.cbxOldAnalysisWebpage.tbIconHoldPlace = true;
            this.cbxOldAnalysisWebpage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbxOldAnalysisWebpage.tbIconIndeterminate = ((System.Drawing.Image)(resources.GetObject("cbxOldAnalysisWebpage.tbIconIndeterminate")));
            this.cbxOldAnalysisWebpage.tbIconIndeterminateMouseDown = null;
            this.cbxOldAnalysisWebpage.tbIconIndeterminateMouseHover = null;
            this.cbxOldAnalysisWebpage.tbIconIndeterminateMouseLeave = null;
            this.cbxOldAnalysisWebpage.tbIconIndeterminateState = iTong.Components.ImageState.FourState;
            this.cbxOldAnalysisWebpage.tbIconPlaceText = 1;
            this.cbxOldAnalysisWebpage.tbIconUnChecked = ((System.Drawing.Image)(resources.GetObject("cbxOldAnalysisWebpage.tbIconUnChecked")));
            this.cbxOldAnalysisWebpage.tbIconUnCheckedMouseDown = null;
            this.cbxOldAnalysisWebpage.tbIconUnCheckedMouseHover = null;
            this.cbxOldAnalysisWebpage.tbIconUnCheckedMouseLeave = null;
            this.cbxOldAnalysisWebpage.tbIconUnCheckedState = iTong.Components.ImageState.FourState;
            this.cbxOldAnalysisWebpage.tbImageBackground = null;
            this.cbxOldAnalysisWebpage.tbImageBackgroundState = iTong.Components.ImageState.TwoState;
            this.cbxOldAnalysisWebpage.tbImageCheckedMouseDown = null;
            this.cbxOldAnalysisWebpage.tbImageCheckedMouseHover = null;
            this.cbxOldAnalysisWebpage.tbImageCheckedMouseLeave = null;
            this.cbxOldAnalysisWebpage.tbImageUnCheckedMouseDown = null;
            this.cbxOldAnalysisWebpage.tbImageUnCheckedMouseHover = null;
            this.cbxOldAnalysisWebpage.tbImageUnCheckedMouseLeave = null;
            this.cbxOldAnalysisWebpage.tbReadOnly = false;
            this.cbxOldAnalysisWebpage.tbShadow = false;
            this.cbxOldAnalysisWebpage.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.cbxOldAnalysisWebpage.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.cbxOldAnalysisWebpage.tbSplit = "3,3,3,3";
            this.cbxOldAnalysisWebpage.tbToolTip = "";
            this.cbxOldAnalysisWebpage.Text = "开启旧版消息解析";
            this.cbxOldAnalysisWebpage.UseVisualStyleBackColor = false;
            this.cbxOldAnalysisWebpage.CheckedChanged += new System.EventHandler(this.cbxOldAnalysisWebpage_CheckedChanged);
            // 
            // btnDBPath
            // 
            this.btnDBPath.BackColor = System.Drawing.Color.Transparent;
            this.btnDBPath.BindingForm = null;
            this.btnDBPath.Location = new System.Drawing.Point(320, 171);
            this.btnDBPath.Name = "btnDBPath";
            this.btnDBPath.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnDBPath.Selectable = true;
            this.btnDBPath.Size = new System.Drawing.Size(51, 23);
            this.btnDBPath.TabIndex = 87;
            this.btnDBPath.tbAdriftIconWhenHover = false;
            this.btnDBPath.tbAutoSize = false;
            this.btnDBPath.tbAutoSizeEx = false;
            this.btnDBPath.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_white;
            this.btnDBPath.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnDBPath.tbBadgeNumber = 0;
            this.btnDBPath.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnDBPath.tbDefaultText = "";
            this.btnDBPath.tbEnableEnter = true;
            this.btnDBPath.tbEndEllipsis = false;
            this.btnDBPath.tbIconHoldPlace = true;
            this.btnDBPath.tbIconImage = null;
            this.btnDBPath.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDBPath.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnDBPath.tbIconMore = false;
            this.btnDBPath.tbIconMouseDown = null;
            this.btnDBPath.tbIconMouseHover = null;
            this.btnDBPath.tbIconMouseLeave = null;
            this.btnDBPath.tbIconPlaceText = 2;
            this.btnDBPath.tbIconReadOnly = null;
            this.btnDBPath.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnDBPath.tbImageMouseDown = null;
            this.btnDBPath.tbImageMouseHover = null;
            this.btnDBPath.tbImageMouseLeave = null;
            this.btnDBPath.tbProgressValue = 50;
            this.btnDBPath.tbReadOnly = false;
            this.btnDBPath.tbReadOnlyText = false;
            this.btnDBPath.tbShadow = false;
            this.btnDBPath.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnDBPath.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnDBPath.tbShowDot = false;
            this.btnDBPath.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnDBPath.tbShowMoreIconImg")));
            this.btnDBPath.tbShowNew = false;
            this.btnDBPath.tbShowProgress = false;
            this.btnDBPath.tbShowTip = true;
            this.btnDBPath.tbShowToolTipOnButton = false;
            this.btnDBPath.tbSplit = "3,3,3,3";
            this.btnDBPath.tbText = "浏览";
            this.btnDBPath.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDBPath.tbTextColor = System.Drawing.Color.Gray;
            this.btnDBPath.tbTextColorDisable = System.Drawing.Color.Gray;
            this.btnDBPath.tbTextColorDown = System.Drawing.Color.Gray;
            this.btnDBPath.tbTextColorHover = System.Drawing.Color.Gray;
            this.btnDBPath.tbTextMouseDownPlace = 0;
            this.btnDBPath.tbToolTip = "";
            this.btnDBPath.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnDBPath.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnDBPath.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDBPath.tbUpToDownWhenCenter = false;
            this.btnDBPath.TimerCountdownNum = 3;
            this.btnDBPath.TimerInterval = 1;
            this.btnDBPath.VisibleEx = true;
            this.btnDBPath.Click += new System.EventHandler(this.btnDBPath_Click);
            // 
            // btnDefaultFolder
            // 
            this.btnDefaultFolder.BackColor = System.Drawing.Color.Transparent;
            this.btnDefaultFolder.BindingForm = null;
            this.btnDefaultFolder.Location = new System.Drawing.Point(320, 111);
            this.btnDefaultFolder.Name = "btnDefaultFolder";
            this.btnDefaultFolder.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnDefaultFolder.Selectable = true;
            this.btnDefaultFolder.Size = new System.Drawing.Size(51, 23);
            this.btnDefaultFolder.TabIndex = 87;
            this.btnDefaultFolder.tbAdriftIconWhenHover = false;
            this.btnDefaultFolder.tbAutoSize = false;
            this.btnDefaultFolder.tbAutoSizeEx = false;
            this.btnDefaultFolder.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_white;
            this.btnDefaultFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnDefaultFolder.tbBadgeNumber = 0;
            this.btnDefaultFolder.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnDefaultFolder.tbDefaultText = "";
            this.btnDefaultFolder.tbEnableEnter = true;
            this.btnDefaultFolder.tbEndEllipsis = false;
            this.btnDefaultFolder.tbIconHoldPlace = true;
            this.btnDefaultFolder.tbIconImage = null;
            this.btnDefaultFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDefaultFolder.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnDefaultFolder.tbIconMore = false;
            this.btnDefaultFolder.tbIconMouseDown = null;
            this.btnDefaultFolder.tbIconMouseHover = null;
            this.btnDefaultFolder.tbIconMouseLeave = null;
            this.btnDefaultFolder.tbIconPlaceText = 2;
            this.btnDefaultFolder.tbIconReadOnly = null;
            this.btnDefaultFolder.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnDefaultFolder.tbImageMouseDown = null;
            this.btnDefaultFolder.tbImageMouseHover = null;
            this.btnDefaultFolder.tbImageMouseLeave = null;
            this.btnDefaultFolder.tbProgressValue = 50;
            this.btnDefaultFolder.tbReadOnly = false;
            this.btnDefaultFolder.tbReadOnlyText = false;
            this.btnDefaultFolder.tbShadow = false;
            this.btnDefaultFolder.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnDefaultFolder.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnDefaultFolder.tbShowDot = false;
            this.btnDefaultFolder.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnDefaultFolder.tbShowMoreIconImg")));
            this.btnDefaultFolder.tbShowNew = false;
            this.btnDefaultFolder.tbShowProgress = false;
            this.btnDefaultFolder.tbShowTip = true;
            this.btnDefaultFolder.tbShowToolTipOnButton = false;
            this.btnDefaultFolder.tbSplit = "3,3,3,3";
            this.btnDefaultFolder.tbText = "浏览";
            this.btnDefaultFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDefaultFolder.tbTextColor = System.Drawing.Color.Gray;
            this.btnDefaultFolder.tbTextColorDisable = System.Drawing.Color.Gray;
            this.btnDefaultFolder.tbTextColorDown = System.Drawing.Color.Gray;
            this.btnDefaultFolder.tbTextColorHover = System.Drawing.Color.Gray;
            this.btnDefaultFolder.tbTextMouseDownPlace = 0;
            this.btnDefaultFolder.tbToolTip = "";
            this.btnDefaultFolder.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnDefaultFolder.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnDefaultFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDefaultFolder.tbUpToDownWhenCenter = false;
            this.btnDefaultFolder.TimerCountdownNum = 3;
            this.btnDefaultFolder.TimerInterval = 1;
            this.btnDefaultFolder.VisibleEx = true;
            this.btnDefaultFolder.Click += new System.EventHandler(this.btnDefaultFolder_Click);
            // 
            // txtDBPath
            // 
            this.txtDBPath.BackColor = System.Drawing.Color.White;
            this.txtDBPath.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(189)))), ((int)(((byte)(189)))));
            this.txtDBPath.BorderColorFocus = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(227)))), ((int)(((byte)(245)))));
            this.txtDBPath.BorderColorFocusIn = System.Drawing.Color.FromArgb(((int)(((byte)(63)))), ((int)(((byte)(131)))), ((int)(((byte)(209)))));
            this.txtDBPath.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle;
            this.txtDBPath.ForeColor = System.Drawing.Color.Black;
            this.txtDBPath.ImeMode = System.Windows.Forms.ImeMode.On;
            this.txtDBPath.Location = new System.Drawing.Point(55, 172);
            this.txtDBPath.MaxLength = 32767;
            this.txtDBPath.Name = "txtDBPath";
            this.txtDBPath.Size = new System.Drawing.Size(260, 21);
            this.txtDBPath.TabIndex = 86;
            this.txtDBPath.Tag = null;
            this.txtDBPath.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown;
            this.txtDBPath.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal;
            this.txtDBPath.tbSelMark = true;
            this.txtDBPath.tbTextBind = "";
            this.txtDBPath.TextFormat = iTong.Components.tbTextFormat.Normal;
            this.txtDBPath.TextImeMode = System.Windows.Forms.ImeMode.On;
            this.txtDBPath.TextPadding = new System.Windows.Forms.Padding(3);
            this.txtDBPath.TextTip = "";
            // 
            // label2
            // 
            this.label2.BackColor = System.Drawing.Color.Transparent;
            this.label2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label2.Location = new System.Drawing.Point(35, 146);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(239, 17);
            this.label2.TabIndex = 85;
            this.label2.Text = "临时数据库存放目录";
            this.label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtDefaultFolder
            // 
            this.txtDefaultFolder.BackColor = System.Drawing.Color.White;
            this.txtDefaultFolder.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(189)))), ((int)(((byte)(189)))));
            this.txtDefaultFolder.BorderColorFocus = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(227)))), ((int)(((byte)(245)))));
            this.txtDefaultFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(((int)(((byte)(63)))), ((int)(((byte)(131)))), ((int)(((byte)(209)))));
            this.txtDefaultFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle;
            this.txtDefaultFolder.ForeColor = System.Drawing.Color.Black;
            this.txtDefaultFolder.ImeMode = System.Windows.Forms.ImeMode.On;
            this.txtDefaultFolder.Location = new System.Drawing.Point(55, 112);
            this.txtDefaultFolder.MaxLength = 32767;
            this.txtDefaultFolder.Name = "txtDefaultFolder";
            this.txtDefaultFolder.Size = new System.Drawing.Size(260, 21);
            this.txtDefaultFolder.TabIndex = 86;
            this.txtDefaultFolder.Tag = null;
            this.txtDefaultFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown;
            this.txtDefaultFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal;
            this.txtDefaultFolder.tbSelMark = true;
            this.txtDefaultFolder.tbTextBind = "";
            this.txtDefaultFolder.TextFormat = iTong.Components.tbTextFormat.Normal;
            this.txtDefaultFolder.TextImeMode = System.Windows.Forms.ImeMode.On;
            this.txtDefaultFolder.TextPadding = new System.Windows.Forms.Padding(3);
            this.txtDefaultFolder.TextTip = "";
            // 
            // label1
            // 
            this.label1.BackColor = System.Drawing.Color.Transparent;
            this.label1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label1.Location = new System.Drawing.Point(35, 86);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(239, 17);
            this.label1.TabIndex = 85;
            this.label1.Text = "微信导出默认目录";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cbxExportRemark
            // 
            this.cbxExportRemark.Appearance = System.Windows.Forms.Appearance.Button;
            this.cbxExportRemark.BackColor = System.Drawing.Color.Transparent;
            this.cbxExportRemark.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cbxExportRemark.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.cbxExportRemark.ForeColor_Checked = System.Drawing.Color.FromArgb(((int)(((byte)(86)))), ((int)(((byte)(86)))), ((int)(((byte)(86)))));
            this.cbxExportRemark.Location = new System.Drawing.Point(55, 318);
            this.cbxExportRemark.Name = "cbxExportRemark";
            this.cbxExportRemark.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cbxExportRemark.Size = new System.Drawing.Size(260, 18);
            this.cbxExportRemark.TabIndex = 84;
            this.cbxExportRemark.tbAdriftIconWhenHover = false;
            this.cbxExportRemark.tbAutoSize = false;
            this.cbxExportRemark.tbAutoSizeEx = true;
            this.cbxExportRemark.tbIconChecked = ((System.Drawing.Image)(resources.GetObject("cbxExportRemark.tbIconChecked")));
            this.cbxExportRemark.tbIconCheckedMouseDown = null;
            this.cbxExportRemark.tbIconCheckedMouseHover = null;
            this.cbxExportRemark.tbIconCheckedMouseLeave = null;
            this.cbxExportRemark.tbIconCheckedState = iTong.Components.ImageState.FourState;
            this.cbxExportRemark.tbIconHoldPlace = true;
            this.cbxExportRemark.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbxExportRemark.tbIconIndeterminate = ((System.Drawing.Image)(resources.GetObject("cbxExportRemark.tbIconIndeterminate")));
            this.cbxExportRemark.tbIconIndeterminateMouseDown = null;
            this.cbxExportRemark.tbIconIndeterminateMouseHover = null;
            this.cbxExportRemark.tbIconIndeterminateMouseLeave = null;
            this.cbxExportRemark.tbIconIndeterminateState = iTong.Components.ImageState.FourState;
            this.cbxExportRemark.tbIconPlaceText = 1;
            this.cbxExportRemark.tbIconUnChecked = ((System.Drawing.Image)(resources.GetObject("cbxExportRemark.tbIconUnChecked")));
            this.cbxExportRemark.tbIconUnCheckedMouseDown = null;
            this.cbxExportRemark.tbIconUnCheckedMouseHover = null;
            this.cbxExportRemark.tbIconUnCheckedMouseLeave = null;
            this.cbxExportRemark.tbIconUnCheckedState = iTong.Components.ImageState.FourState;
            this.cbxExportRemark.tbImageBackground = null;
            this.cbxExportRemark.tbImageBackgroundState = iTong.Components.ImageState.TwoState;
            this.cbxExportRemark.tbImageCheckedMouseDown = null;
            this.cbxExportRemark.tbImageCheckedMouseHover = null;
            this.cbxExportRemark.tbImageCheckedMouseLeave = null;
            this.cbxExportRemark.tbImageUnCheckedMouseDown = null;
            this.cbxExportRemark.tbImageUnCheckedMouseHover = null;
            this.cbxExportRemark.tbImageUnCheckedMouseLeave = null;
            this.cbxExportRemark.tbReadOnly = false;
            this.cbxExportRemark.tbShadow = false;
            this.cbxExportRemark.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.cbxExportRemark.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.cbxExportRemark.tbSplit = "3,3,3,3";
            this.cbxExportRemark.tbToolTip = "";
            this.cbxExportRemark.Text = "群消息导出群昵称（取消则导出好友备注）";
            this.cbxExportRemark.UseVisualStyleBackColor = false;
            this.cbxExportRemark.CheckedChanged += new System.EventHandler(this.cbxLog_CheckedChanged);
            // 
            // cbxLog
            // 
            this.cbxLog.Appearance = System.Windows.Forms.Appearance.Button;
            this.cbxLog.BackColor = System.Drawing.Color.Transparent;
            this.cbxLog.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cbxLog.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.cbxLog.ForeColor_Checked = System.Drawing.Color.FromArgb(((int)(((byte)(86)))), ((int)(((byte)(86)))), ((int)(((byte)(86)))));
            this.cbxLog.Location = new System.Drawing.Point(55, 288);
            this.cbxLog.Name = "cbxLog";
            this.cbxLog.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cbxLog.Size = new System.Drawing.Size(104, 18);
            this.cbxLog.TabIndex = 84;
            this.cbxLog.tbAdriftIconWhenHover = false;
            this.cbxLog.tbAutoSize = false;
            this.cbxLog.tbAutoSizeEx = true;
            this.cbxLog.tbIconChecked = ((System.Drawing.Image)(resources.GetObject("cbxLog.tbIconChecked")));
            this.cbxLog.tbIconCheckedMouseDown = null;
            this.cbxLog.tbIconCheckedMouseHover = null;
            this.cbxLog.tbIconCheckedMouseLeave = null;
            this.cbxLog.tbIconCheckedState = iTong.Components.ImageState.FourState;
            this.cbxLog.tbIconHoldPlace = true;
            this.cbxLog.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbxLog.tbIconIndeterminate = ((System.Drawing.Image)(resources.GetObject("cbxLog.tbIconIndeterminate")));
            this.cbxLog.tbIconIndeterminateMouseDown = null;
            this.cbxLog.tbIconIndeterminateMouseHover = null;
            this.cbxLog.tbIconIndeterminateMouseLeave = null;
            this.cbxLog.tbIconIndeterminateState = iTong.Components.ImageState.FourState;
            this.cbxLog.tbIconPlaceText = 1;
            this.cbxLog.tbIconUnChecked = ((System.Drawing.Image)(resources.GetObject("cbxLog.tbIconUnChecked")));
            this.cbxLog.tbIconUnCheckedMouseDown = null;
            this.cbxLog.tbIconUnCheckedMouseHover = null;
            this.cbxLog.tbIconUnCheckedMouseLeave = null;
            this.cbxLog.tbIconUnCheckedState = iTong.Components.ImageState.FourState;
            this.cbxLog.tbImageBackground = null;
            this.cbxLog.tbImageBackgroundState = iTong.Components.ImageState.TwoState;
            this.cbxLog.tbImageCheckedMouseDown = null;
            this.cbxLog.tbImageCheckedMouseHover = null;
            this.cbxLog.tbImageCheckedMouseLeave = null;
            this.cbxLog.tbImageUnCheckedMouseDown = null;
            this.cbxLog.tbImageUnCheckedMouseHover = null;
            this.cbxLog.tbImageUnCheckedMouseLeave = null;
            this.cbxLog.tbReadOnly = false;
            this.cbxLog.tbShadow = false;
            this.cbxLog.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.cbxLog.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.cbxLog.tbSplit = "3,3,3,3";
            this.cbxLog.tbToolTip = "";
            this.cbxLog.Text = "输出详细日志";
            this.cbxLog.UseVisualStyleBackColor = false;
            this.cbxLog.CheckedChanged += new System.EventHandler(this.cbxLog_CheckedChanged);
            // 
            // cbxExcelCoding
            // 
            this.cbxExcelCoding.FormattingEnabled = true;
            this.cbxExcelCoding.Items.AddRange(new object[] {
            "utf8",
            "gb2312",
            "gbk",
            "unicode"});
            this.cbxExcelCoding.Location = new System.Drawing.Point(55, 55);
            this.cbxExcelCoding.Name = "cbxExcelCoding";
            this.cbxExcelCoding.Size = new System.Drawing.Size(260, 20);
            this.cbxExcelCoding.TabIndex = 83;
            // 
            // lblExportExcel
            // 
            this.lblExportExcel.BackColor = System.Drawing.Color.Transparent;
            this.lblExportExcel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblExportExcel.Location = new System.Drawing.Point(35, 29);
            this.lblExportExcel.Name = "lblExportExcel";
            this.lblExportExcel.Size = new System.Drawing.Size(239, 17);
            this.lblExportExcel.TabIndex = 82;
            this.lblExportExcel.Text = "微信导出Excel设置";
            this.lblExportExcel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cbxLoadDB
            // 
            this.cbxLoadDB.Appearance = System.Windows.Forms.Appearance.Button;
            this.cbxLoadDB.BackColor = System.Drawing.Color.Transparent;
            this.cbxLoadDB.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.cbxLoadDB.ForeColor_Checked = System.Drawing.Color.FromArgb(((int)(((byte)(86)))), ((int)(((byte)(86)))), ((int)(((byte)(86)))));
            this.cbxLoadDB.Location = new System.Drawing.Point(55, 373);
            this.cbxLoadDB.Name = "cbxLoadDB";
            this.cbxLoadDB.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cbxLoadDB.Size = new System.Drawing.Size(469, 22);
            this.cbxLoadDB.TabIndex = 81;
            this.cbxLoadDB.tbAdriftIconWhenHover = false;
            this.cbxLoadDB.tbAutoSize = false;
            this.cbxLoadDB.tbAutoSizeEx = false;
            this.cbxLoadDB.tbIconChecked = ((System.Drawing.Image)(resources.GetObject("cbxLoadDB.tbIconChecked")));
            this.cbxLoadDB.tbIconCheckedMouseDown = null;
            this.cbxLoadDB.tbIconCheckedMouseHover = null;
            this.cbxLoadDB.tbIconCheckedMouseLeave = null;
            this.cbxLoadDB.tbIconCheckedState = iTong.Components.ImageState.FourState;
            this.cbxLoadDB.tbIconHoldPlace = true;
            this.cbxLoadDB.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbxLoadDB.tbIconIndeterminate = ((System.Drawing.Image)(resources.GetObject("cbxLoadDB.tbIconIndeterminate")));
            this.cbxLoadDB.tbIconIndeterminateMouseDown = null;
            this.cbxLoadDB.tbIconIndeterminateMouseHover = null;
            this.cbxLoadDB.tbIconIndeterminateMouseLeave = null;
            this.cbxLoadDB.tbIconIndeterminateState = iTong.Components.ImageState.FourState;
            this.cbxLoadDB.tbIconPlaceText = 1;
            this.cbxLoadDB.tbIconUnChecked = ((System.Drawing.Image)(resources.GetObject("cbxLoadDB.tbIconUnChecked")));
            this.cbxLoadDB.tbIconUnCheckedMouseDown = null;
            this.cbxLoadDB.tbIconUnCheckedMouseHover = null;
            this.cbxLoadDB.tbIconUnCheckedMouseLeave = null;
            this.cbxLoadDB.tbIconUnCheckedState = iTong.Components.ImageState.FourState;
            this.cbxLoadDB.tbImageBackground = null;
            this.cbxLoadDB.tbImageBackgroundState = iTong.Components.ImageState.TwoState;
            this.cbxLoadDB.tbImageCheckedMouseDown = null;
            this.cbxLoadDB.tbImageCheckedMouseHover = null;
            this.cbxLoadDB.tbImageCheckedMouseLeave = null;
            this.cbxLoadDB.tbImageUnCheckedMouseDown = null;
            this.cbxLoadDB.tbImageUnCheckedMouseHover = null;
            this.cbxLoadDB.tbImageUnCheckedMouseLeave = null;
            this.cbxLoadDB.tbReadOnly = false;
            this.cbxLoadDB.tbShadow = false;
            this.cbxLoadDB.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.cbxLoadDB.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.cbxLoadDB.tbSplit = "3,3,3,3";
            this.cbxLoadDB.tbToolTip = "";
            this.cbxLoadDB.Text = "数据库读取方式（备份大建议使用此方法防止内存不足，缺点速度慢。）";
            this.cbxLoadDB.UseVisualStyleBackColor = false;
            // 
            // btnOK
            // 
            this.btnOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnOK.BackColor = System.Drawing.Color.Transparent;
            this.btnOK.BindingForm = null;
            this.btnOK.Location = new System.Drawing.Point(312, 449);
            this.btnOK.Name = "btnOK";
            this.btnOK.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnOK.Selectable = true;
            this.btnOK.Size = new System.Drawing.Size(87, 23);
            this.btnOK.TabIndex = 79;
            this.btnOK.tbAdriftIconWhenHover = false;
            this.btnOK.tbAutoSize = false;
            this.btnOK.tbAutoSizeEx = false;
            this.btnOK.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnOK.tbBadgeNumber = 0;
            this.btnOK.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnOK.tbDefaultText = "";
            this.btnOK.tbEnableEnter = true;
            this.btnOK.tbEndEllipsis = false;
            this.btnOK.tbIconHoldPlace = true;
            this.btnOK.tbIconImage = null;
            this.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnOK.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnOK.tbIconMore = false;
            this.btnOK.tbIconMouseDown = null;
            this.btnOK.tbIconMouseHover = null;
            this.btnOK.tbIconMouseLeave = null;
            this.btnOK.tbIconPlaceText = 2;
            this.btnOK.tbIconReadOnly = null;
            this.btnOK.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnOK.tbImageMouseDown = null;
            this.btnOK.tbImageMouseHover = null;
            this.btnOK.tbImageMouseLeave = null;
            this.btnOK.tbProgressValue = 50;
            this.btnOK.tbReadOnly = false;
            this.btnOK.tbReadOnlyText = false;
            this.btnOK.tbShadow = false;
            this.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnOK.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnOK.tbShowDot = false;
            this.btnOK.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnOK.tbShowMoreIconImg")));
            this.btnOK.tbShowNew = false;
            this.btnOK.tbShowProgress = false;
            this.btnOK.tbShowTip = true;
            this.btnOK.tbShowToolTipOnButton = false;
            this.btnOK.tbSplit = "3,3,3,3";
            this.btnOK.tbText = "保存";
            this.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnOK.tbTextColor = System.Drawing.Color.White;
            this.btnOK.tbTextColorDisable = System.Drawing.Color.White;
            this.btnOK.tbTextColorDown = System.Drawing.Color.White;
            this.btnOK.tbTextColorHover = System.Drawing.Color.White;
            this.btnOK.tbTextMouseDownPlace = 0;
            this.btnOK.tbToolTip = "";
            this.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnOK.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnOK.tbUpToDownWhenCenter = false;
            this.btnOK.TimerCountdownNum = 3;
            this.btnOK.TimerInterval = 1;
            this.btnOK.VisibleEx = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // btnCancel
            // 
            this.btnCancel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCancel.BackColor = System.Drawing.Color.Transparent;
            this.btnCancel.BindingForm = null;
            this.btnCancel.Location = new System.Drawing.Point(411, 449);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnCancel.Selectable = true;
            this.btnCancel.Size = new System.Drawing.Size(87, 23);
            this.btnCancel.TabIndex = 80;
            this.btnCancel.tbAdriftIconWhenHover = false;
            this.btnCancel.tbAutoSize = false;
            this.btnCancel.tbAutoSizeEx = false;
            this.btnCancel.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_white;
            this.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnCancel.tbBadgeNumber = 0;
            this.btnCancel.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnCancel.tbDefaultText = "";
            this.btnCancel.tbEnableEnter = true;
            this.btnCancel.tbEndEllipsis = false;
            this.btnCancel.tbIconHoldPlace = true;
            this.btnCancel.tbIconImage = null;
            this.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnCancel.tbIconMore = false;
            this.btnCancel.tbIconMouseDown = null;
            this.btnCancel.tbIconMouseHover = null;
            this.btnCancel.tbIconMouseLeave = null;
            this.btnCancel.tbIconPlaceText = 2;
            this.btnCancel.tbIconReadOnly = null;
            this.btnCancel.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnCancel.tbImageMouseDown = null;
            this.btnCancel.tbImageMouseHover = null;
            this.btnCancel.tbImageMouseLeave = null;
            this.btnCancel.tbProgressValue = 50;
            this.btnCancel.tbReadOnly = false;
            this.btnCancel.tbReadOnlyText = false;
            this.btnCancel.tbShadow = false;
            this.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnCancel.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnCancel.tbShowDot = false;
            this.btnCancel.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnCancel.tbShowMoreIconImg")));
            this.btnCancel.tbShowNew = false;
            this.btnCancel.tbShowProgress = false;
            this.btnCancel.tbShowTip = true;
            this.btnCancel.tbShowToolTipOnButton = false;
            this.btnCancel.tbSplit = "3,3,3,3";
            this.btnCancel.tbText = "取消";
            this.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCancel.tbTextColor = System.Drawing.Color.Gray;
            this.btnCancel.tbTextColorDisable = System.Drawing.Color.Gray;
            this.btnCancel.tbTextColorDown = System.Drawing.Color.Gray;
            this.btnCancel.tbTextColorHover = System.Drawing.Color.Gray;
            this.btnCancel.tbTextMouseDownPlace = 0;
            this.btnCancel.tbToolTip = "";
            this.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnCancel.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCancel.tbUpToDownWhenCenter = false;
            this.btnCancel.TimerCountdownNum = 3;
            this.btnCancel.TimerInterval = 1;
            this.btnCancel.VisibleEx = true;
            this.btnCancel.Click += new System.EventHandler(this.btnCancel_Click);
            // 
            // btn_close
            // 
            this.btn_close.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_close.BackColor = System.Drawing.Color.Transparent;
            this.btn_close.BindingForm = null;
            this.btn_close.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_close.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_close.Location = new System.Drawing.Point(505, 5);
            this.btn_close.Name = "btn_close";
            this.btn_close.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_close.Selectable = true;
            this.btn_close.Size = new System.Drawing.Size(26, 26);
            this.btn_close.TabIndex = 79;
            this.btn_close.tbAdriftIconWhenHover = false;
            this.btn_close.tbAutoSize = true;
            this.btn_close.tbAutoSizeEx = false;
            this.btn_close.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_close;
            this.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbBadgeNumber = 0;
            this.btn_close.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_close.tbDefaultText = "";
            this.btn_close.tbEnableEnter = true;
            this.btn_close.tbEndEllipsis = false;
            this.btn_close.tbIconHoldPlace = true;
            this.btn_close.tbIconImage = null;
            this.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbIconMore = false;
            this.btn_close.tbIconMouseDown = null;
            this.btn_close.tbIconMouseHover = null;
            this.btn_close.tbIconMouseLeave = null;
            this.btn_close.tbIconPlaceText = 2;
            this.btn_close.tbIconReadOnly = null;
            this.btn_close.tbIconSize = new System.Drawing.Size(0, 0);
            this.btn_close.tbImageMouseDown = null;
            this.btn_close.tbImageMouseHover = null;
            this.btn_close.tbImageMouseLeave = null;
            this.btn_close.tbProgressValue = 50;
            this.btn_close.tbReadOnly = false;
            this.btn_close.tbReadOnlyText = false;
            this.btn_close.tbShadow = false;
            this.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_close.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_close.tbShowDot = false;
            this.btn_close.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_close.tbShowMoreIconImg")));
            this.btn_close.tbShowNew = false;
            this.btn_close.tbShowProgress = false;
            this.btn_close.tbShowTip = true;
            this.btn_close.tbShowToolTipOnButton = false;
            this.btn_close.tbSplit = "3,3,3,3";
            this.btn_close.tbText = "";
            this.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbTextColor = System.Drawing.Color.White;
            this.btn_close.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_close.tbTextColorDown = System.Drawing.Color.White;
            this.btn_close.tbTextColorHover = System.Drawing.Color.White;
            this.btn_close.tbTextMouseDownPlace = 0;
            this.btn_close.tbToolTip = "";
            this.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_close.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbUpToDownWhenCenter = false;
            this.btn_close.TimerCountdownNum = 3;
            this.btn_close.TimerInterval = 1;
            this.btn_close.VisibleEx = true;
            // 
            // label3
            // 
            this.label3.BackColor = System.Drawing.Color.Transparent;
            this.label3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label3.Location = new System.Drawing.Point(35, 206);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(239, 17);
            this.label3.TabIndex = 82;
            this.label3.Text = "微信导出过滤设置";
            this.label3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cbxExportFilter
            // 
            this.cbxExportFilter.FormattingEnabled = true;
            this.cbxExportFilter.Items.AddRange(new object[] {
            "全部导出",
            "不导出图片",
            "不导出音频",
            "不导出视频",
            "不导出图片跟音频",
            "不导出图片跟视频",
            "不导出音频跟视频",
            "不导出图片、音频跟视频"});
            this.cbxExportFilter.Location = new System.Drawing.Point(55, 232);
            this.cbxExportFilter.Name = "cbxExportFilter";
            this.cbxExportFilter.Size = new System.Drawing.Size(260, 20);
            this.cbxExportFilter.TabIndex = 83;
            // 
            // label4
            // 
            this.label4.BackColor = System.Drawing.Color.Transparent;
            this.label4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label4.Location = new System.Drawing.Point(35, 266);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(239, 17);
            this.label4.TabIndex = 82;
            this.label4.Text = "其它设置";
            this.label4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // frmSetting
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(543, 539);
            this.Controls.Add(this.btn_close);
            this.Controls.Add(this.tbPanel1);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.MinimumSize = new System.Drawing.Size(530, 360);
            this.Name = "frmSetting";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.tbGuiBackground = global::iWechatAssistant.Properties.Resources.frm_bg_sub;
            this.tbShowTitleOnForm = true;
            this.tbSplit = "6,39,7,28";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.tbTitleForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(104)))), ((int)(((byte)(104)))));
            this.tbTitleLocation = new System.Drawing.Point(4, 10);
            this.Text = "设置";
            this.tbPanel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private iTong.Components.tbPanel tbPanel1;
        private iTong.Components.tbButton btnOK;
        private iTong.Components.tbButton btnCancel;
        private iTong.Components.tbCheckBox cbxLoadDB;
        internal iTong.Components.tbButton btn_close;
        internal System.Windows.Forms.Label lblExportExcel;
        internal System.Windows.Forms.ComboBox cbxExcelCoding;
        internal iTong.Components.tbCheckBox cbxLog;
        private iTong.Components.tbButton btnDefaultFolder;
        private iTong.Components.tbTextBox txtDefaultFolder;
        internal System.Windows.Forms.Label label1;
        internal iTong.Components.tbCheckBox cbxExportRemark;
        internal iTong.Components.tbCheckBox cbxOldAnalysisWebpage;
        private iTong.Components.tbCheckBox cbxArtificialAccredit;
        private iTong.Components.tbButton btnDBPath;
        private iTong.Components.tbTextBox txtDBPath;
        internal System.Windows.Forms.Label label2;
        private iTong.Components.tbCheckBox chkDeleteCache;
        internal System.Windows.Forms.ComboBox cbxExportFilter;
        internal System.Windows.Forms.Label label3;
        internal System.Windows.Forms.Label label4;
    }
}