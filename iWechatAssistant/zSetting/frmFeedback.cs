﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Windows.Forms;

namespace iWechatAssistant
{
    public partial class frmFeedback : tbBaseGuiForm
    {
        private Thread mTdCommit = null;

        public frmFeedback()
        {
            InitializeComponent();
            this.Icon = global::iWechatAssistant.Properties.Resources.main;
        }

        private void btnCommit_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(this.txtContent.Text.Trim()))
            {
                tbMessageBox.Show(this, "请输入内容！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.txtContent.Focus();
                return;
            }

            if (string.IsNullOrEmpty(this.txtEmail.Text.Trim()))
            {
                tbMessageBox.Show(this, "请输入邮箱！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.txtEmail.Focus();
                return;
            }

            Regex re = new Regex(@"[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?");
            if (!re.IsMatch(this.txtEmail.Text))
            {
                tbMessageBox.Show(this, "请输入正确的邮箱地址！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.txtEmail.Focus();
                return;
            }

            try
            {
                if (this.mTdCommit != null && this.mTdCommit.ThreadState != ThreadState.Stopped)
                    this.mTdCommit.Abort();
            }
            catch { }

            this.mTdCommit = new Thread(new ParameterizedThreadStart(DoCommit));
            this.mTdCommit.IsBackground = true;
            this.mTdCommit.Start(new object[] { this.txtContent.Text, this.txtEmail.Text });
        }

        private void DoCommit(object obj)
        {
            try
            {
                object[] arrobj = (object[])obj;
                string strContent = arrobj[0].ToString();
                string strEmail = arrobj[1].ToString();

                string softversion = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();//软件版本
                string bit = Common.IsOS_Of_64Bit() ? "64bit" : "32bit";//这个没啥用,传空字符串
                string os = OSHelper.OSType.ToString();//用户的电脑系统
                string mem = Utility.FormatFileSize(Common.GetPhisicalMemory());//内存
                string vmem = Utility.FormatFileSize(Common.GetVirtualMemory());//虚拟内存
                string cpu = ""; //用户电脑CPU
                string userparam = string.Format("0@{0}|{1}|{2}|{3}|{4}|{5}", softversion, bit, os, mem, vmem, cpu);

                string strUrl = string.Format("http://cdata.tongbu.com/tbcloud/api/advice.ashx?mode=post&tbtype=9&questionbody={0}&mail={1}&ip={2}&userparam={3}", strContent, strEmail, "", userparam);

                string strResult = Utility.PostData(strUrl, "", 20000);
                //if (strResult.ToLower() == "{\"mode\":\"ajax\",\"result\":\"1\"}")

                tbMessageBox.Show(this, "谢谢您的反馈，已提交！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = System.Windows.Forms.DialogResult.OK;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoCommit");
            }
        }
    }
}
