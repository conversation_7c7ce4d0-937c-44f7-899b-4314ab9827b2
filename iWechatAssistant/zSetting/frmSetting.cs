﻿using iTong.Components;
using iTong.CoreFoundation;
using iTong.CoreModule;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace iWechatAssistant
{
    public partial class frmSetting : tbBaseGuiForm
    {
        public static event EventHandler<EventArgs> WeChatDataLoadDBHandler;

        public static event EventHandler<EventArgs> WeChatArtificialAccreditHandler;

        public frmSetting()
        {
            InitializeComponent();

            this.Icon = (Icon)global::iWechatAssistant.Properties.Resources.main.Clone();

            this.cbxLoadDB.Checked = IniSetting.GetIsTalkSaveDB();

            cbxArtificialAccredit.Visible = false;
            //if(File.Exists(Path.Combine(Application.StartupPath,"log.dll")))
            //    cbxArtificialAccredit.Visible = true;

        }

        protected override void InitControls()
        {
            base.InitControls();
            this.cbxExcelCoding.Text = IniSetting.GetWeChatExportExcelCoding();
            this.txtDefaultFolder.Text = IniSetting.GetWCAExportDefaultFolder();
            this.txtDBPath.Text = IniSetting.GetWechatAssistantDBFolder();
            this.chkDeleteCache.Checked = IniSetting.GetIsDeleteChache();
            this.cbxExportFilter.SelectedIndex = IniSetting.GetExportFilter();
            
            try
            {
                this.cbxLog.Checked = File.Exists(Path.Combine(Folder.AppFolder, "debug.dll"));
            }
            catch { }

            try
            {
                this.cbxOldAnalysisWebpage.Checked = File.Exists(Path.Combine(Folder.AppFolder, "OldAnalysisWebpage.dll"));
            }
            catch { }

            this.cbxExportRemark.Checked = IniSetting.GetWeChatExportRemarkName();

            this.cbxArtificialAccredit.Checked = IniSetting.GetWeChatArtificialAccredit();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            SaveLoadDB();

            SaveArtificialAccredit();

            IniSetting.SetWeChatExportExcelCoding(this.cbxExcelCoding.Text);
            IniSetting.SetWeChatExportRemarkName(this.cbxExportRemark.Checked);
            IniSetting.SetWCAExportDefaultFolder(this.txtDefaultFolder.Text);
            IniSetting.SetWechatAssistantDBFolder(this.txtDBPath.Text);
            IniSetting.SetExportFilter(this.cbxExportFilter.SelectedIndex);
            IniSetting.SetIsDeleteChache(this.chkDeleteCache.Checked);

            CheckCacheDbDirectory();

            this.Close();
        }

        private void CheckCacheDbDirectory()
        {
            //判断临时数据库默认保存路径是否有配置 是否存在  如果不存在寻找一个剩余最大盘符
            string strDBFolder = IniSetting.GetWechatAssistantDBFolder();

            bool deleteCache = IniSetting.GetIsDeleteChache();

            if (string.IsNullOrEmpty(strDBFolder))
            {
                string strTemp = Path.Combine(Folder.MaxDrives(), "WechatAssistantDB");
                Folder.CheckFolder(strTemp);
                IniSetting.SetWechatAssistantDBFolder(strTemp);
            }
            else
            {
                try
                {
                    if (deleteCache)
                    {
                        Directory.Delete(strDBFolder, true);
                    }
                    else if (Directory.Exists(Path.Combine(strDBFolder, "cache")))
                    {
                        Directory.Delete(Path.Combine(strDBFolder, "cache"), true);
                    }
                }
                catch
                {

                }
                Folder.CheckFolder(strDBFolder);

                if (!Directory.Exists(strDBFolder))
                {
                    IniSetting.SetWechatAssistantDBFolder("");
                    CheckCacheDbDirectory();
                }
            }
        }

        private void SaveArtificialAccredit()
        {
            IniSetting.SetWeChatArtificialAccredit(this.cbxArtificialAccredit.Checked);
            if (WeChatArtificialAccreditHandler != null)
                WeChatArtificialAccreditHandler(null, new EventArgs());
        }

        private void SaveLoadDB()
        {
            if (IniSetting.GetIsTalkSaveDB() != this.cbxLoadDB.Checked)
            {
                if (WeChatDataLoadDBHandler != null)
                {
                    if (tbMessageBox.Show(this, "您修改数据读取方式需要重新加载数据，确认修改？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) != DialogResult.OK)
                        return;

                    WeChatDataLoadDBHandler(null, new EventArgs());
                }
            }

            IniSetting.SetIsTalkSaveDB(this.cbxLoadDB.Checked);
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void cbxLog_CheckedChanged(object sender, EventArgs e)
        {
            if (cbxLog.Checked)
            {
                try
                {
                    if (!File.Exists(Path.Combine(Folder.AppFolder, "debug.dll")))
                        File.Create(Path.Combine(Folder.AppFolder, "debug.dll")).Close();
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "cbxLog_CheckedChanged_Create");
                }
            }
            else
            {
                try
                {
                    if (File.Exists(Path.Combine(Folder.AppFolder, "debug.dll")))
                        File.Delete(Path.Combine(Folder.AppFolder, "debug.dll"));

                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "cbxLog_CheckedChanged_Delete");
                }
            }
        }

        private void btnDefaultFolder_Click(object sender, EventArgs e)
        {
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择导出文件默认存储目录";

            if (!string.IsNullOrEmpty(this.txtDefaultFolder.Text))
                dialog.SelectedPath = this.txtDefaultFolder.Text;

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                this.txtDefaultFolder.Text = dialog.SelectedPath;
        }

        private void cbxOldAnalysisWebpage_CheckedChanged(object sender, EventArgs e)
        {
            if (cbxOldAnalysisWebpage.Checked)
            {
                try
                {
                    if (!File.Exists(Path.Combine(Folder.AppFolder, "OldAnalysisWebpage.dll")))
                        File.Create(Path.Combine(Folder.AppFolder, "OldAnalysisWebpage.dll")).Close();
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "cbxOldAnalysisWebpage_CheckedChanged_Create");
                }
            }
            else
            {
                try
                {
                    if (File.Exists(Path.Combine(Folder.AppFolder, "OldAnalysisWebpage.dll")))
                        File.Delete(Path.Combine(Folder.AppFolder, "OldAnalysisWebpage.dll"));

                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "cbxOldAnalysisWebpage_CheckedChanged_Delete");
                }
            }
        }

        private void btnDBPath_Click(object sender, EventArgs e)
        {            
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择导出文件默认存储目录";

            if (!string.IsNullOrEmpty(this.txtDBPath.Text))
                dialog.SelectedPath = this.txtDBPath.Text;

            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                this.txtDBPath.Text = dialog.SelectedPath;        
        }
    }
}
