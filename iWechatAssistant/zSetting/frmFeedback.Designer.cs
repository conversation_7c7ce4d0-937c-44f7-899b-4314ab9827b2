﻿namespace iWechatAssistant
{
    partial class frmFeedback
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmFeedback));
            this.tbPanel1 = new iTong.Components.tbPanel();
            this.tbPanel2 = new iTong.Components.tbPanel();
            this.txtContent = new System.Windows.Forms.RichTextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.txtEmail = new iTong.Components.tbTextBox();
            this.lblExportExcel = new System.Windows.Forms.Label();
            this.btnCommit = new iTong.Components.tbButton();
            this.btn_close = new iTong.Components.tbButton();
            this.tbPanel1.SuspendLayout();
            this.tbPanel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // tbPanel1
            // 
            this.tbPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.tbPanel1.Controls.Add(this.tbPanel2);
            this.tbPanel1.Controls.Add(this.label2);
            this.tbPanel1.Controls.Add(this.label1);
            this.tbPanel1.Controls.Add(this.txtEmail);
            this.tbPanel1.Controls.Add(this.lblExportExcel);
            this.tbPanel1.Controls.Add(this.btnCommit);
            this.tbPanel1.Location = new System.Drawing.Point(1, 37);
            this.tbPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel1.Name = "tbPanel1";
            this.tbPanel1.Size = new System.Drawing.Size(528, 322);
            this.tbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel1.TabIndex = 1;
            this.tbPanel1.tbBackgroundImage = null;
            this.tbPanel1.tbShowWatermark = false;
            this.tbPanel1.tbSplit = "0,0,0,0";
            this.tbPanel1.tbWatermark = null;
            this.tbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel1.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel2
            // 
            this.tbPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(189)))), ((int)(((byte)(189)))));
            this.tbPanel2.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.tbPanel2.Controls.Add(this.txtContent);
            this.tbPanel2.Location = new System.Drawing.Point(76, 23);
            this.tbPanel2.Name = "tbPanel2";
            this.tbPanel2.Size = new System.Drawing.Size(422, 185);
            this.tbPanel2.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel2.TabIndex = 0;
            this.tbPanel2.tbBackgroundImage = null;
            this.tbPanel2.tbShowWatermark = false;
            this.tbPanel2.tbSplit = "0,0,0,0";
            this.tbPanel2.tbWatermark = null;
            this.tbPanel2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel2.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // txtContent
            // 
            this.txtContent.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtContent.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtContent.Location = new System.Drawing.Point(0, 0);
            this.txtContent.MaxLength = 5000;
            this.txtContent.Name = "txtContent";
            this.txtContent.Size = new System.Drawing.Size(420, 183);
            this.txtContent.TabIndex = 0;
            this.txtContent.Text = "";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(74, 256);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(341, 12);
            this.label2.TabIndex = 89;
            this.label2.Text = "请在反馈内容中留下您的QQ，以便我们联系并帮助您解决问题。";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.BackColor = System.Drawing.Color.Transparent;
            this.label1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.label1.Location = new System.Drawing.Point(32, 224);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(41, 12);
            this.label1.TabIndex = 88;
            this.label1.Text = "邮箱：";
            this.label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // txtEmail
            // 
            this.txtEmail.BackColor = System.Drawing.Color.White;
            this.txtEmail.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(189)))), ((int)(((byte)(189)))));
            this.txtEmail.BorderColorFocus = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(227)))), ((int)(((byte)(245)))));
            this.txtEmail.BorderColorFocusIn = System.Drawing.Color.FromArgb(((int)(((byte)(63)))), ((int)(((byte)(131)))), ((int)(((byte)(209)))));
            this.txtEmail.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle;
            this.txtEmail.ForeColor = System.Drawing.Color.Black;
            this.txtEmail.ImeMode = System.Windows.Forms.ImeMode.On;
            this.txtEmail.Location = new System.Drawing.Point(76, 220);
            this.txtEmail.MaxLength = 32767;
            this.txtEmail.Name = "txtEmail";
            this.txtEmail.Size = new System.Drawing.Size(422, 21);
            this.txtEmail.TabIndex = 1;
            this.txtEmail.Tag = null;
            this.txtEmail.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown;
            this.txtEmail.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal;
            this.txtEmail.tbSelMark = true;
            this.txtEmail.tbTextBind = "";
            this.txtEmail.TextFormat = iTong.Components.tbTextFormat.Normal;
            this.txtEmail.TextImeMode = System.Windows.Forms.ImeMode.On;
            this.txtEmail.TextPadding = new System.Windows.Forms.Padding(3);
            this.txtEmail.TextTip = "";
            // 
            // lblExportExcel
            // 
            this.lblExportExcel.AutoSize = true;
            this.lblExportExcel.BackColor = System.Drawing.Color.Transparent;
            this.lblExportExcel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.lblExportExcel.Location = new System.Drawing.Point(30, 27);
            this.lblExportExcel.Name = "lblExportExcel";
            this.lblExportExcel.Size = new System.Drawing.Size(41, 12);
            this.lblExportExcel.TabIndex = 82;
            this.lblExportExcel.Text = "内容：";
            this.lblExportExcel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnCommit
            // 
            this.btnCommit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCommit.BackColor = System.Drawing.Color.Transparent;
            this.btnCommit.BindingForm = null;
            this.btnCommit.Location = new System.Drawing.Point(411, 288);
            this.btnCommit.Name = "btnCommit";
            this.btnCommit.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnCommit.Selectable = true;
            this.btnCommit.Size = new System.Drawing.Size(87, 23);
            this.btnCommit.TabIndex = 2;
            this.btnCommit.tbAdriftIconWhenHover = false;
            this.btnCommit.tbAutoSize = false;
            this.btnCommit.tbAutoSizeEx = false;
            this.btnCommit.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnCommit.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnCommit.tbBadgeNumber = 0;
            this.btnCommit.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnCommit.tbEndEllipsis = false;
            this.btnCommit.tbIconHoldPlace = true;
            this.btnCommit.tbIconImage = null;
            this.btnCommit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCommit.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnCommit.tbIconMore = false;
            this.btnCommit.tbIconMouseDown = null;
            this.btnCommit.tbIconMouseHover = null;
            this.btnCommit.tbIconMouseLeave = null;
            this.btnCommit.tbIconPlaceText = 2;
            this.btnCommit.tbIconReadOnly = null;
            this.btnCommit.tbImageMouseDown = null;
            this.btnCommit.tbImageMouseHover = null;
            this.btnCommit.tbImageMouseLeave = null;
            this.btnCommit.tbProgressValue = 50;
            this.btnCommit.tbReadOnly = false;
            this.btnCommit.tbReadOnlyText = false;
            this.btnCommit.tbShadow = false;
            this.btnCommit.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnCommit.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnCommit.tbShowDot = false;
            this.btnCommit.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnCommit.tbShowMoreIconImg")));
            this.btnCommit.tbShowNew = false;
            this.btnCommit.tbShowProgress = false;
            this.btnCommit.tbShowTip = true;
            this.btnCommit.tbShowToolTipOnButton = false;
            this.btnCommit.tbSplit = "3,3,3,3";
            this.btnCommit.tbText = "提交";
            this.btnCommit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCommit.tbTextColor = System.Drawing.Color.White;
            this.btnCommit.tbTextColorDisable = System.Drawing.Color.White;
            this.btnCommit.tbTextColorDown = System.Drawing.Color.White;
            this.btnCommit.tbTextColorHover = System.Drawing.Color.White;
            this.btnCommit.tbTextMouseDownPlace = 0;
            this.btnCommit.tbToolTip = "";
            this.btnCommit.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnCommit.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnCommit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCommit.VisibleEx = true;
            this.btnCommit.Click += new System.EventHandler(this.btnCommit_Click);
            // 
            // btn_close
            // 
            this.btn_close.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_close.BackColor = System.Drawing.Color.Transparent;
            this.btn_close.BindingForm = null;
            this.btn_close.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_close.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_close.Location = new System.Drawing.Point(492, 5);
            this.btn_close.Name = "btn_close";
            this.btn_close.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_close.Selectable = true;
            this.btn_close.Size = new System.Drawing.Size(26, 26);
            this.btn_close.TabIndex = 1;
            this.btn_close.tbAdriftIconWhenHover = false;
            this.btn_close.tbAutoSize = true;
            this.btn_close.tbAutoSizeEx = false;
            this.btn_close.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_close;
            this.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbBadgeNumber = 0;
            this.btn_close.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_close.tbEndEllipsis = false;
            this.btn_close.tbIconHoldPlace = true;
            this.btn_close.tbIconImage = null;
            this.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbIconMore = false;
            this.btn_close.tbIconMouseDown = null;
            this.btn_close.tbIconMouseHover = null;
            this.btn_close.tbIconMouseLeave = null;
            this.btn_close.tbIconPlaceText = 2;
            this.btn_close.tbIconReadOnly = null;
            this.btn_close.tbImageMouseDown = null;
            this.btn_close.tbImageMouseHover = null;
            this.btn_close.tbImageMouseLeave = null;
            this.btn_close.tbProgressValue = 50;
            this.btn_close.tbReadOnly = false;
            this.btn_close.tbReadOnlyText = false;
            this.btn_close.tbShadow = false;
            this.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_close.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_close.tbShowDot = false;
            this.btn_close.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_close.tbShowMoreIconImg")));
            this.btn_close.tbShowNew = false;
            this.btn_close.tbShowProgress = false;
            this.btn_close.tbShowTip = true;
            this.btn_close.tbShowToolTipOnButton = false;
            this.btn_close.tbSplit = "3,3,3,3";
            this.btn_close.tbText = "";
            this.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbTextColor = System.Drawing.Color.White;
            this.btn_close.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_close.tbTextColorDown = System.Drawing.Color.White;
            this.btn_close.tbTextColorHover = System.Drawing.Color.White;
            this.btn_close.tbTextMouseDownPlace = 0;
            this.btn_close.tbToolTip = "";
            this.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_close.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.VisibleEx = true;
            // 
            // frmFeedback
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(530, 360);
            this.Controls.Add(this.btn_close);
            this.Controls.Add(this.tbPanel1);
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(530, 360);
            this.MinimizeBox = false;
            this.MinimumSize = new System.Drawing.Size(530, 360);
            this.Name = "frmFeedback";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.tbGuiBackground = global::iWechatAssistant.Properties.Resources.frm_bg_sub;
            this.tbShowTitleOnForm = true;
            this.tbSplit = "6,39,7,28";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.tbTitleForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(104)))), ((int)(((byte)(104)))));
            this.tbTitleLocation = new System.Drawing.Point(4, 10);
            this.Text = "问题反馈";
            this.tbPanel1.ResumeLayout(false);
            this.tbPanel1.PerformLayout();
            this.tbPanel2.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private iTong.Components.tbPanel tbPanel1;
        private iTong.Components.tbTextBox txtEmail;
        internal System.Windows.Forms.Label lblExportExcel;
        private iTong.Components.tbButton btnCommit;
        internal iTong.Components.tbButton btn_close;
        internal System.Windows.Forms.Label label1;
        private System.Windows.Forms.RichTextBox txtContent;
        private System.Windows.Forms.Label label2;
        private iTong.Components.tbPanel tbPanel2;
    }
}