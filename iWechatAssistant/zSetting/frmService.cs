﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace iWechatAssistant
{
    public partial class frmService : tbBaseGuiForm
    {
        private string mNumImgUrl =  WechatHeplerPC.mAssistInfo.qrCodeQQ;
        private string mQQ = WechatHeplerPC.mAssistInfo.numberQQ;

        private string QRCode = Folder.GetTempFilePath();

        public frmService()
        {
            InitializeComponent();
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            if (!string.IsNullOrEmpty(mNumImgUrl) && !string.IsNullOrEmpty(mQQ))
            {
                Thread thd = new Thread(GetQRCodeThread);
                thd.IsBackground = true;
                thd.Start(new object[] { mNumImgUrl });
            }
            else
                mQQ = "750471026";

            tbButton4.Text = "QQ群：" + mQQ;
        }

        private void GetQRCodeThread(object state)
        {
            Image imgShell = Common.DownloadImage(mNumImgUrl, 3000);
            if (imgShell != null)
            {
                imgShell.Save(QRCode, System.Drawing.Imaging.ImageFormat.Png);
                imgShell.Dispose();

                SetPictureBox();
            }
        }

        private delegate void SetPictureBoxHandler();
        public void SetPictureBox( )
        {
            if (this.InvokeRequired)
            {
                this.BeginInvoke(new SetPictureBoxHandler(SetPictureBox));
            }
            else
            {
                if (File.Exists(QRCode))
                {
                    tbPictureBox1.Image = Utility.GetImageFormFile(QRCode, true);
                }            }
        }

        private void tbButton4_Click(object sender, EventArgs e)
        {
            CopyText();
        }

        private void tbPictureBox2_Click(object sender, EventArgs e)
        {
            CopyText();
        }

        private void tbButton7_Click(object sender, EventArgs e)
        {
            CopyText();
        }

        private void CopyText()
        {
            Clipboard.SetText(mQQ);
        }
    }
}
