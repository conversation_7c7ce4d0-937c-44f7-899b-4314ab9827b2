﻿using GroupRoomData;
using iTong.Components;
using iTong.CoreFoundation;
using iTong.CoreModule;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Timers;
using System.Web;
using System.Windows.Forms;

namespace iWechatAssistant
{
    public class ElectronAPI
    {
        private static List<WechatAccInfo> mlstInfos = new List<WechatAccInfo>();
        private static WechatHeplerPC mWechatHeplerPC = null;
        #region Demo
        
        private Func<object, Task<object>> showFile = null;
        public async Task<object> GetFiles(dynamic input)
        {
            string strPath = (string)input.path;
            this.showFile = (Func<object, Task<object>>)input.callback;

            if (string.IsNullOrEmpty(strPath) || strPath == "computer")
            {
                this.GetDrives();
            }
            else
            {
                this.GetFolder(strPath);
            }
            return null;
        }

        public void GetFolder(string strPath)
        {
            DirectoryInfo folderInfo = new DirectoryInfo(strPath);

            foreach (DirectoryInfo info in folderInfo.GetDirectories())
            {
                this.showFile(new { label = info.Name, path = info.FullName, type = "folder" });
            }

            foreach (FileInfo info in folderInfo.GetFiles())
            {
                this.showFile(new { label = info.Name, path = info.FullName, type = "file" });
            }
            folderInfo = null;
        }

        public void GetDrives()
        {
            System.IO.DriveInfo[] drives = System.IO.DriveInfo.GetDrives();
            foreach (System.IO.DriveInfo di in drives)
            {
                if (di.DriveType != DriveType.Fixed)
                    continue;
                this.showFile(new { label = di.Name, path = di.Name, type = "folder" });
            }

        }

        public async Task<object> ShowMsg(dynamic input)
        {
            string strPath = (string)input.path;
            this.showFile = (Func<object, Task<object>>)input.callback;

            this.showFile(new { label = string.Format("Msg:{0}",strPath) });
            return null;
        }

        #endregion

        #region 取得电脑微信备份路径

        private Func<object, Task<object>> funcBackupPath = null;
        public async Task<object> GetPCWeChatBackupPath(dynamic input)
        {
            string strReturn = string.Empty;
            this.funcBackupPath = (Func<object, Task<object>>)input.callback;
            //获取微信PC 设置--通用设置--文件管理 的配置目录
            string strConfigFilePath = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"Tencent\WeChat\All Users\config\3ebffe94.ini");
            Common.LogException(strConfigFilePath, "同步PCWeChat备份路径");
            if (File.Exists(strConfigFilePath))
            {
                try
                {
                    using (StreamReader sr = new StreamReader(strConfigFilePath, Encoding.UTF8))
                    {
                        string strTemp = sr.ReadLine();
                        string strTempFolder = "";
                        if (strTemp == "MyDocument:")
                            strTempFolder = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "WeChat Files");
                        else
                            strTempFolder = strTemp + @"\WeChat Files";
                        if (Directory.Exists(strTempFolder))
                        {
                            this.funcBackupPath(new { path = strTempFolder });
                        }
                    }
                }
                catch (IOException ioe)
                {
                    Common.LogException(ioe.ToString(), "ElectronAPI_GetPCWeChatBackupPath");
                }
            }
            return null;
        }

        #endregion

        #region 取得所有微信账号

        private Func<object, Task<object>> funcGetWechatAccount = null;
        public async Task<object> GetWechatAccountFromPC(dynamic input)
        {
            this.funcGetWechatAccount = (Func<object, Task<object>>)input.callback;
            string strPath = (string)input.path;
            try
            {
                WechatHeplerPC.DeleteBackupkey();
            }
            catch (Exception) {}

            try
            {
                string strErrorMsg = "";
                //SetLoadingMsg("获取备份列表，请耐心等候...");
                List<WechatAccInfo> lstInfos = WechatHeplerPC.GetWechatAccountFromPC(strPath, ref strErrorMsg);
                mlstInfos = lstInfos;
                foreach (WechatAccInfo item in lstInfos)
                {
                    if (!File.Exists(item.IconFilePath))
                    {
                        Utility.DownLoadFile(item.IconUrl, item.IconFilePath, 20000);
                    }
                    string strType = GetType(item);
                    string strImageBase64 = string.Empty;
                    string strIconPath = string.Empty;
                    if (File.Exists(item.IconFilePath))
                    {
                        string strIconFolder = Path.Combine(Folder.CacheFolder, string.Format(@"HeadIcon\{0}", item.Account));
                        Folder.CheckFolder(strIconFolder);
                        strIconPath = Path.Combine(strIconFolder, string.Format(@"{0}.png", item.Account));
                        try
                        {
                            File.Copy(item.IconFilePath, strIconPath, true);
                        }
                        catch 
                        { }
                    }
                    Bitmap bmp = new Bitmap(strIconPath);
                    MemoryStream ms = new MemoryStream();
                    bmp.Save(ms, System.Drawing.Imaging.ImageFormat.Jpeg);
                    byte[] arr = new byte[ms.Length];
                    ms.Position = 0;
                    ms.Read(arr, 0, (int)ms.Length);
                    ms.Close();
                    strImageBase64 = Convert.ToBase64String(arr);
                    this.funcGetWechatAccount(new { status = "LoadData" , password = item.Password, account = item.Account, name = item.Name, type = strType, path = item.PathOnPC, iconpath = strIconPath, iconbase64 = strImageBase64});
                }
                this.funcGetWechatAccount(new { status = "Finished" });
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ElectronAPI_GetWechatAccountFromPC");
            }
            return null;
        }

        private string GetType(WechatAccInfo item)
        {
            string strReulst = "未知状态";
            try
            {
                string s = "未授权";
                if (item.Password.Length > 0)
                    s = "已授权";
                else
                {
                    if (item.AState == ActivateState.Nonactivated)
                    {
                        string strKey = WechatHeplerPC.GetBackupkey(item.Account);
                        if (strKey.Length > 0)
                        {
                            item.Password = strKey;
                            s = "已授权";
                        }
                    }
                }

                string j = "未激活";
                if (item.AState == ActivateState.Nonactivated)
                    j = "未激活";
                else if (item.AState == ActivateState.Overdue)
                    j = "已过期";
                else
                    j = "已激活";

                strReulst = string.Format("{0}，{1}", j, s);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetType");
            }
            return strReulst;
        }

        #endregion

        #region 点击账号加载当前账号信息相关方法

        private Func<object, Task<object>> funcCreateDeviceInfo = null;
        public async Task<object> CreateDeviceInfo(dynamic input)
        {
            this.funcCreateDeviceInfo = (Func<object, Task<object>>)input.callback;
            string strId = (string)input.wechatid;

            try
            {
                WechatAccInfo waInfo = this.GetWechatAccountByID(strId);
                mWechatHeplerPC = WechatHeplerPC.GetInstance(waInfo.PathOnPC, waInfo.Password);
                mWechatHeplerPC.GetAccountBackupedDevice(waInfo);
                foreach (WeChatAccBackupInfo item in waInfo.LstWCABInfo)
                {
                    this.funcCreateDeviceInfo(new { status = "LoadData", configinfo = item.BCConfigInfo, deviceName = item.DeviceName, UDID = item.DeviceUDID });
                }
                this.funcCreateDeviceInfo(new { status = "Finished" });
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ElectronAPI_CreateDeviceInfo");
            }
            return null;
        }

        #endregion

        #region  加载联系人聊天记录列表 
  
        private Func<object, Task<object>> funcGetContactList = null;
        public async Task<object> GetContactList(dynamic input)
        {
            this.funcGetContactList = (Func<object, Task<object>>)input.callback;
            string strId = (string)input.wcid;
            string strWechatId = (string)input.deviceid;
            //string strWechatId = "";
           
            try
            {
                WechatAccInfo wcInfo = this.GetWechatAccountByID(strId);
                WeChatAccBackupInfo info = this.GetWechatBackupByID(wcInfo, strId);
                BackConfigInfo bcConfigInfo = info.BCConfigInfo;
                string strBackDBPath = bcConfigInfo.BackupDBPath;
                string strDBPassword = wcInfo.Password;
                wcInfo.StrWeChatTalkDBPath = Path.Combine(Path.GetDirectoryName(strBackDBPath), "WeChatTalkDB.db");
                Dictionary<string, WechatTalk> dictWechatTalk = mWechatHeplerPC.LoadTalkList(strBackDBPath, strDBPassword, wcInfo);

                if (dictWechatTalk != null && dictWechatTalk.Count > 0)
                {
                    foreach (WechatTalk item in dictWechatTalk.Values)
                    {
                        string strName = "";
                        string strDescription = "";
                        string strLastTime = "";
                        if (strLastTime.Length == 0)
                        {
                            strLastTime = this.GetLastChatTime(item.EndTime);
                        }
                        if (strName.Length == 0)
                        {
                            strName = item.NickName;
                            if (strName.Length <= 0)
                            {
                                strName = item.UserName;
                            }
                            strName = Utility.ReplaceWinIllegalName(strName);
                        }
                        if (strDescription.Length == 0)
                        {
                            string strMsg = this.GetViewLastChat(wcInfo, bcConfigInfo, item);
                            if (ServerIniSetting.IsMsgHide() && WechatHeplerPC.mAssistInfo.IsPay && wcInfo.AState != ActivateState.Normal)
                                strMsg = MsgHide(strMsg);

                            strDescription =Common.StringFormat(strMsg,18,true,false);
                        }
                        this.funcGetContactList(new { talkid = item.TalkId, username = strName, lasttime = strLastTime, description = strDescription });
                    }
                    this.funcGetContactList(new { status = "Finished" });
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ElectronAPI_GetContactList");
            }
            return null;
        }

        #endregion

        #region  加载联系人聊头像

        private Func<object, Task<object>> funcGetContactIcon = null;
         public async Task<object> GetContactIcon(dynamic input)
         {
             this.funcGetContactIcon = (Func<object, Task<object>>)input.callback;
             string strId = (string)input.wcid;
             string strWechatId = (string)input.deviceid;
             WechatAccInfo wcInfo = this.GetWechatAccountByID(strId);
             WeChatAccBackupInfo info = this.GetWechatBackupByID(wcInfo, strId);
             BackConfigInfo bcConfigInfo = info.BCConfigInfo;

             try
             {
                 if (File.Exists(wcInfo.IconFilePath))
                 {
                     Image imgIcon = Utility.GetImageFormFile(wcInfo.IconFilePath);

                     string strIconFolder = Path.Combine(Folder.CacheFolder, string.Format(@"HeadIcon\{0}", wcInfo.Account));
                     Folder.CheckFolder(strIconFolder);
                     string strIconPath = Path.Combine(strIconFolder, string.Format(@"{0}.pic", wcInfo.Account));
                     try
                     {
                         File.Copy(wcInfo.IconFilePath, strIconPath, true);
                     }
                     catch 
                     {
                     }
                 }
             }
             catch { }

             //加载头像
             try
             {
                 string strBackDBPath = bcConfigInfo.BackupDBPath;
                 string strDBPassword = wcInfo.Password;
                 wcInfo.StrWeChatTalkDBPath = Path.Combine(Path.GetDirectoryName(strBackDBPath), "WeChatTalkDB.db");
                 Dictionary<string, WechatTalk> dictWechatTalk = mWechatHeplerPC.LoadTalkList(strBackDBPath, strDBPassword, wcInfo);

                 Dictionary<tbDataGridViewRow, List<string>> dicChat = new Dictionary<tbDataGridViewRow, List<string>>();
                 foreach (WechatTalk Item in dictWechatTalk.Values)
                 {
                     //1、需要下载的头像列表
                     bool blnIsChat = false;
                     List<string> lstItems = new List<string>();

                     if (WechatHeplerPC.CheckIsChatRoom(Item.UserName))
                     {
                         blnIsChat = true;
                         List<string> arrMems = new List<string>();
                         //群组需要把成员的头像都下载下来
                         foreach (WechatMessageItem mItem in Item.MessageItems)
                         {
                             string strUsrName = "";
                             string strNickName = "";
                             string strMsgForWeb = mItem.MessageContent;
                             WechatHeplerPC.AnalyseGroup(wcInfo.DicWeChatFriendInfo, ref  strUsrName, ref  strNickName, ref  strMsgForWeb, wcInfo.LstWeChatGroupFriendInfo, Item.UserName);
                             strUsrName = WechatHeplerPC.ReplaceImproperCharacter(strUsrName);
                             arrMems.Add(strUsrName);
                         }
                         foreach (string strAccount in arrMems)
                         {
                             if (strAccount.Length == 0)
                             {
                                 continue;
                             }
                             if (!lstItems.Contains(strAccount))
                             {
                                 Common.Log(string.Format("加载头像:{0}", strAccount));
                                 lstItems.Add(strAccount);
                             }
                         }
                     }

                     //if (!lstItems.Contains(Item.UserName))
                     //{
                     //    lstItems.Add(Item.UserName);
                     //}

                     //if (blnIsChat)
                     //{
                     //    //群组头像添加到队列，最后获取
                     //    dicChat.Add(row, lstItems);
                     //}
                     //else
                     //{
                     //    //2、下载头像
                     //    this.DownHeadIcon(wcInfo,lstItems);

                     //    //3、更新界面
                     //    this.UpdateHeadIconToDataGridView(row);
                     //}
                 }

             }
             catch (Exception ex)
             {
                 Common.LogException(ex.ToString(), "LoadHeadIconInThread");
             }
             return null;
         }

         private void DownHeadIcon(WechatAccInfo wcInfo ,List<string> lstItems)
         {
             try
             {
                 string strFileOnPC = "";
                 string strFolderOnPC = "";
                 bool isExist = false;
                 foreach (string item in lstItems)
                 {
                     if (wcInfo.DicWeChatFriendInfo.ContainsKey(item))
                     {
                         WeChatFriendInfo info = wcInfo.DicWeChatFriendInfo[item];
                         strFolderOnPC = Path.Combine(Folder.CacheFolder, string.Format(@"HeadIcon\{0}\", info.StrWXID));
                         Folder.CheckFolder(strFolderOnPC);
                         strFileOnPC = Path.Combine(strFolderOnPC, string.Format("{0}.pic", info.StrWXID));
                         if (!string.IsNullOrEmpty(info.StrHeadImgUrl))
                         {
                             string iconUrl = info.StrHeadImgUrl;

                             if (File.Exists(strFileOnPC))
                             {
                                 isExist = true;
                             }
                             else if (!string.IsNullOrEmpty(iconUrl))
                             {
                                 isExist = Common.DownloadImage(iconUrl, 20000, strFileOnPC);
                             }
                         }
                         else
                         {
                             Common.Log(item + "----" + info.StrHeadImgUrl);
                         }
                     }
                 }
             }
             catch (Exception ex)
             {
                 Common.LogException(ex.ToString(), "DownHeadIcon");
             }
         }

         private void LoadFriends(WechatAccInfo waInfo)
         {
             Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo = new Dictionary<string, WeChatFriendInfo>();
             List<WeChatGroupFriendInfo> lstWCGFInfo = new List<WeChatGroupFriendInfo>();
             WeChatFriendInfo wInfo = new WeChatFriendInfo();
             string strAssistantPath = Path.Combine(waInfo.PathOnPC, "Assistant");
             Folder.CheckFolder(strAssistantPath);
             string strFriendPath = Path.Combine(strAssistantPath, string.Format("Friend.txt"));
             try
             {
                 string strTempPath = string.Format("{0}.tmp", strFriendPath);

                 string strContent = "";
                 if (!File.Exists(strFriendPath))
                 {
                     JsonObject jObject = new JsonObject();
                     jObject.Add("wxid", waInfo.Account);
                     waInfo.StrUrlFriendlist = HtmlHelper.GetFriendTxtUrl(JsonParser.SaveString(jObject));

                     if (!string.IsNullOrEmpty(waInfo.StrUrlFriendlist))
                     {
                         int intTry = 0;
                         strContent = Utility.GetContentStringFromUrl(waInfo.StrUrlFriendlist, Encoding.UTF8, 20000);
                         while (string.IsNullOrEmpty(strContent) && intTry < 3)
                         {
                             Utility.WaitSeconds(0.5);
                             strContent = Utility.GetContentStringFromUrl(waInfo.StrUrlFriendlist, Encoding.UTF8, 20000);
                             intTry += 1;
                         }
                         strContent = SaveFriendFile(strFriendPath, strTempPath, strContent);
                     }
                 }
                 else
                 {
                     strContent = File.ReadAllText(strFriendPath, Encoding.UTF8);
                 }

                 //解密
                 strContent = Common.DecryptDES(strContent, "pw91!3#1", "19wp!3#1");

                 if (string.IsNullOrEmpty(strContent))
                 {
                     Common.Log(string.Format("访问：{0}  得到的数据：{1}", waInfo.StrUrlFriendlist, strContent));
                     goto Do_Exit;
                 }

                 JsonObject objJson = JsonParser.ParseString(strContent);

                 if (!objJson.ContainsKey("data"))
                 {
                     Common.Log(string.Format("得到的数据：{0}  不包含：data", strContent));
                 }
                 else
                 {
                     JsonObject jsonData = objJson["data"] as JsonObject;

                     foreach (JsonObject item in jsonData.Values)
                     {
                         try
                         {
                             wInfo = new WeChatFriendInfo();

                             if (item.ContainsKey("m_nsUsrName") && item["m_nsUsrName"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["m_nsUsrName"];
                                 wInfo.StrWXID = jPara.Value;
                             }

                             if (item.ContainsKey("nickname") && item["nickname"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["nickname"];
                                 wInfo.StrNickName = jPara.Value;
                             }

                             if (item.ContainsKey("m_nsRemark") && item["m_nsRemark"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["m_nsRemark"];
                                 wInfo.StrRemark = jPara.Value;
                             }

                             if (item.ContainsKey("m_nsAliasName") && item["m_nsAliasName"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["m_nsAliasName"];
                                 wInfo.StrInnerAccount = jPara.Value;
                             }

                             if (item.ContainsKey("m_nsHeadImgUrl") && item["m_nsHeadImgUrl"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["m_nsHeadImgUrl"];
                                 wInfo.StrHeadImgUrl = jPara.Value;
                             }

                             if (!dicWeChatFriendInfo.ContainsKey(wInfo.StrWXID))
                                 dicWeChatFriendInfo.Add(wInfo.StrWXID, wInfo);
                         }
                         catch { }
                     }
                 }

                 WeChatGroupFriendInfo WCGFInfo = new WeChatGroupFriendInfo();
                 if (!objJson.ContainsKey("groupdata"))
                 {
                     Common.Log(string.Format("得到的数据：{0}  不包含：groupdata", strContent));
                 }
                 else
                 {
                     JsonArray jsonGroupdata = objJson["groupdata"] as JsonArray;
                     if (jsonGroupdata.Count > 0)
                     {
                         foreach (JsonObject item in jsonGroupdata)
                         {
                             WCGFInfo = new WeChatGroupFriendInfo();
                             if (item.ContainsKey("ChatRoomName") && item["ChatRoomName"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["ChatRoomName"];
                                 WCGFInfo.StrChatRoomName = jPara.Value;
                             }

                             if (item.ContainsKey("Reserved2") && item["Reserved2"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["Reserved2"];
                                 WCGFInfo.StrReserved2 = jPara.Value;
                             }

                             if (item.ContainsKey("RoomData") && item["RoomData"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["RoomData"];
                                 WCGFInfo.LstRoomData = ConvertBuffToInfo(jPara.Value);
                             }

                             if (item.ContainsKey("SelfDisplayName") && item["SelfDisplayName"] is JsonString)
                             {
                                 JsonString jPara = (JsonString)item["SelfDisplayName"];
                                 WCGFInfo.StrSelfDisplayName = jPara.Value;
                             }
                             if (!lstWCGFInfo.Contains(WCGFInfo) && WCGFInfo.LstRoomData.Count > 0)
                                 lstWCGFInfo.Add(WCGFInfo);
                         }
                     }
                 }
             }
             catch (Exception ex)
             {
                 Common.LogException(ex.ToString(), "LoadFriends-groupdata");
             }
         Do_Exit:
             waInfo.StrFriendlistPCPath = strFriendPath;
             waInfo.DicWeChatFriendInfo = dicWeChatFriendInfo;
             waInfo.LstWeChatGroupFriendInfo = lstWCGFInfo;
         }

         private List<WeChatFriendInfo> ConvertBuffToInfo(string strBuff)
         {
             List<WeChatFriendInfo> lstInfo = new List<WeChatFriendInfo>();
             try
             {
                 byte[] bytes = Convert.FromBase64String(strBuff);
                 GroupRoomData_t grpb1 = GroupRoomData_t.Parser.ParseFrom(bytes);
                 Google.Protobuf.Collections.RepeatedField<member_t> members1 = grpb1.Member;
                 WeChatFriendInfo info = null;
                 foreach (member_t item in members1)
                 {
                     info = new WeChatFriendInfo();
                     info.StrWXID = item.Wxid.ToStringUtf8();
                     info.StrNickName = item.MarkName.ToStringUtf8();
                     if (!lstInfo.Contains(info) && info.StrNickName.Length > 0)
                         lstInfo.Add(info);
                 }
             }
             catch (Exception ex)
             {
                 Common.LogException(ex.ToString(), "ConvertBuffToInfo");
             }
             return lstInfo;
         }

         private static string SaveFriendFile(string strFriendPath, string strTempPath, string strContent)
         {
             if (!string.IsNullOrEmpty(strContent))
             {
                 //加密
                 strContent = Common.EncryptDES(strContent, "pw91!3#1", "19wp!3#1");
                 using (StreamWriter sWriter = new StreamWriter(strTempPath, false, Encoding.UTF8))
                 {
                     sWriter.Write(strContent);
                 }

                 if (File.Exists(strTempPath))
                 {
                     try
                     {
                         File.Delete(strFriendPath);
                     }
                     catch { }
                     try
                     {
                         File.Move(strTempPath, strFriendPath);
                     }
                     catch { }
                 }
             }
             return strContent;
         }


        #endregion

        #region 私有方法

        private WechatAccInfo GetWechatAccountByID(string strId)
        {
            WechatAccInfo waInfo = null;
            if(mlstInfos.Count>0)
            {
                foreach (WechatAccInfo item in mlstInfos)
                {
                    if(item.Account==strId)
                    {
                        waInfo = item;
                        break;
                    }
                }
            }
            return waInfo;
        }

        private WeChatAccBackupInfo GetWechatBackupByID(WechatAccInfo wcInfo, string strId)
        {
            WeChatAccBackupInfo result = null;
            foreach (WeChatAccBackupInfo item in wcInfo.LstWCABInfo)
            {
                if (string.Compare(item.DeviceUDID, strId) == 0)
                {
                    result = item;
                    break;
                }
            }
            if (result == null && wcInfo.LstWCABInfo.Count>0)
            {
                result = wcInfo.LstWCABInfo[0];
            }
            return result;
        }

        private string GetLastChatTime(DateTime LastChatTime)
        {
            string strTime = string.Empty;
            TimeSpan ts = DateTime.Now.Date.Subtract(LastChatTime.Date);
            //只需比较日期就好
            try
            {
                switch (ts.Days)
                {
                    case 0:
                        strTime = LastChatTime.ToString("HH:mm");
                        break;
                    case 1:
                        strTime = "昨天";
                        break;
                    default:
                        string strYear = LastChatTime.Year != DateTime.Now.Year ? LastChatTime.Year.ToString() : LastChatTime.Year.ToString().Substring(2, 2);
                        string strMonth = LastChatTime.Month < 10 ? "0" + LastChatTime.Month.ToString() : LastChatTime.Month.ToString();
                        string strDay = LastChatTime.Day < 10 ? "0" + LastChatTime.Day.ToString() : LastChatTime.Day.ToString();
                        strTime = string.Format("{0}-{1}-{2}", strYear, strMonth, strDay);
                        break;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetLastChatTime");
            }
            return strTime;
        }

        private string GetViewLastChat(WechatAccInfo wcInfo, BackConfigInfo bcConfigInfo, WechatTalk item)
        {
            string strRelust = item.LastChatText;
            string strNickNameEx = "";
            try
            {
                if (item.LastWechatMessageItem != null)
                {
                    string strTypeForWeb = "";
                    string strMsgForWeb = item.LastWechatMessageItem.MessageContent;
                    JsonArray arrHtml5ContentForWeb = null;

                    if (!WechatHeplerPC.IsReceivedMsg(item.LastWechatMessageItem, wcInfo) && WechatHeplerPC.CheckIsChatRoom(item.UserName))
                    {
                        string strUsrName = "";
                        string strNickName = "";
                        WechatHeplerPC.AnalyseGroup(wcInfo.DicWeChatFriendInfo, ref strUsrName, ref strNickName, ref strMsgForWeb, wcInfo.LstWeChatGroupFriendInfo, item.UserName);
                    }

                    this.AnalyseMessageType(wcInfo, bcConfigInfo, DataFilterType.Normal, "", ref strTypeForWeb, ref strMsgForWeb, ref arrHtml5ContentForWeb, item.LastWechatMessageItem, ref strNickNameEx);

                    switch (item.LastWechatMessageItem.SociaChatType)
                    {
                        case SociaChatType.Text:
                            strRelust = strMsgForWeb;
                            break;
                        case SociaChatType.Picture:
                            strRelust = "[图片]";
                            break;
                        case SociaChatType.Voice:
                            strRelust = "[语音]";
                            break;
                        case SociaChatType.NameCard:
                            strRelust = "[名片]";
                            break;
                        case SociaChatType.Video:
                            strRelust = "[视频]";
                            break;
                        case SociaChatType.Location:
                            strRelust = "[位置]";
                            break;
                        case SociaChatType.webpage:
                            strRelust = "[网页]";
                            break;
                        case SociaChatType.VideoConnected:
                            strRelust = "[视频未接通]";
                            break;
                        case SociaChatType.SystemMessages:
                            strRelust = "[系统消息]";
                            break;
                        case SociaChatType.SmallVideo:
                            strRelust = "[视频]";
                            break;
                        case SociaChatType.SpecialExpression:
                            strRelust = "[动画表情]";
                            break;
                        case SociaChatType.File:
                            strRelust = "[文件]";
                            break;
                        default:
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetViewLastChat");
            }
            return strRelust;
        }
        private void AnalyseMessageType(WechatAccInfo wcInfo, BackConfigInfo bcConfigInfo, DataFilterType dType, string strSearchText, ref string strTypeForWeb, ref string strMsgForWeb, ref JsonArray arrHtml5ContentForWeb, WechatMessageItem item, ref string strNickNameEx)
        {
            strTypeForWeb = "0";
            arrHtml5ContentForWeb = null;

            //微信： 1文本；3图片；34语音；42名片；43视频；47特殊表情；48地理位置；49网页；50视频未接通的消息；1000系统消息
            //web页 0文本，1语音，2图片，3网页
            int intMapKey = 0;
            switch (item.MessageType)
            {
                case 1:
                    // 处理文本
                    item.SociaChatType = SociaChatType.Text;

                    strTypeForWeb = "0";
                    //检查是否有url
                    string strCheckIsContainUrl = strMsgForWeb;
                    bool blnHighlight = true;
                    if (string.Compare(strCheckIsContainUrl, strMsgForWeb) != 0)
                    {
                        blnHighlight = false;
                        strMsgForWeb = strCheckIsContainUrl;
                    }
                    //表情符号转换成表情图片
                    strMsgForWeb = this.OperateForExpression(strMsgForWeb);
                    //搜索加高亮效果（搜索只搜索文本类型），数据库搜索不区分大小写
                    if (dType == DataFilterType.Search && blnHighlight)
                    {
                        if (!string.IsNullOrEmpty(strSearchText) && strMsgForWeb.Contains(strSearchText))
                        {
                            strMsgForWeb = strMsgForWeb.Replace(strSearchText, "~" + strSearchText + "~(background-color:#ffff00;color:#fffff;)");
                        }
                    }
                    break;

                case 3:
                    // 处理图片
                    item.SociaChatType = SociaChatType.Picture;
                    strTypeForWeb = "2";
                    //strMsgForWeb = this.GetChatImagePathFromPC(Item, true);     
                    strMsgForWeb = mWechatHeplerPC.GetMediaThumbFilePath(item, bcConfigInfo.BackupDBPath, wcInfo.Password, wcInfo.PwdBuffer, out intMapKey);
                    break;

                case 34:
                    // 处理语音
                    item.SociaChatType = SociaChatType.Voice;
                    strTypeForWeb = "1";
                    mWechatHeplerPC.GetMediaThumbFilePath(item, bcConfigInfo.BackupDBPath, wcInfo.Password, wcInfo.PwdBuffer, out intMapKey);
                    strMsgForWeb = WechatHeplerPC.GetChatVoiceLength(item.MessageContent, true).ToString();
                    break;

                case 62:
                    //  ?? 还不知道是啥？
                    strTypeForWeb = "2";
                    //strMsgForWeb = this.GetChatVideoPathFromPC(Item, true);       
                    strMsgForWeb = mWechatHeplerPC.GetMediaFilePath(item, bcConfigInfo.BackupDBPath, wcInfo.Password, wcInfo.PwdBuffer, true, out intMapKey);
                    break;

                case 42:
                    // 处理名片
                    item.SociaChatType = SociaChatType.NameCard;
                    strMsgForWeb = "[名片]";
                    break;

                case 43:
                    // 处理视频
                    item.SociaChatType = SociaChatType.Video;
                    strMsgForWeb = "[视频]";
                    strTypeForWeb = "5";
                    strMsgForWeb = mWechatHeplerPC.GetMediaFilePath(item, bcConfigInfo.BackupDBPath, wcInfo.Password, wcInfo.PwdBuffer, true, out intMapKey);

                    //strMsgForWeb = this.GetChatVideoPathFromPC(Item, true);
                    break;

                case 47:
                    // 处理特殊表情
                    item.SociaChatType = SociaChatType.SpecialExpression;
                    strMsgForWeb = "[动画表情]";
                    break;

                case 48:
                    // 处理地理位置
                    strMsgForWeb = "[地理位置]";
                    item.SociaChatType = SociaChatType.Location;
                    break;

                case 49:
                    // 处理网页
                    item.SociaChatType = SociaChatType.webpage;
                    strTypeForWeb = "3";
                    arrHtml5ContentForWeb = ExportHelper.GetWebPageJsonArray(strMsgForWeb);
                    //网页json
                    try
                    {
                        //兼容链接（只有title和url）
                        if (arrHtml5ContentForWeb == null || arrHtml5ContentForWeb.Count == 0)
                        {
                            JsonArray webLinkJsonArray = ExportHelper.GetWebLinkJsonArray(strMsgForWeb);

                            if (webLinkJsonArray != null && webLinkJsonArray.Count > 0)
                            {
                                JsonObject dicWebPage = (JsonObject)webLinkJsonArray[0];
                                string strTitle = ((JsonString)dicWebPage["title"]).Value;
                                string strUrl = ((JsonString)dicWebPage["url"]).Value;
                                strNickNameEx = ((JsonString)dicWebPage["fromusername"]).Value;

                                if (!string.IsNullOrEmpty(strTitle) && !string.IsNullOrEmpty(strUrl))
                                {
                                    strTypeForWeb = "0";
                                    arrHtml5ContentForWeb = null;
                                    strMsgForWeb = string.Format("[{1}]({0})", strUrl, strTitle);
                                }
                                //else if (strTitle.EndsWith (".xlsx"))
                                //{
                                //    item.SociaChatType = SociaChatType.File ;
                                //    strTypeForWeb = "2";                                   
                                //    strMsgForWeb = mWechatHeplerPC.GetMediaThumbFilePath(item, mCurrentBackupDBPath, mCurrentBackupDBPassword, mCurrentWAInfo.PwdBuffer);

                                //}
                                else
                                {
                                    arrHtml5ContentForWeb = null;
                                    strTypeForWeb = "0";
                                    strMsgForWeb = "[动画表情]";
                                }
                            }
                            //搜索加高亮效果（搜索只搜索文本类型），数据库搜索不区分大小写
                            if (dType == DataFilterType.Search)
                            {
                                if (!string.IsNullOrEmpty(strSearchText) && strMsgForWeb.Contains(strSearchText))
                                {
                                    strMsgForWeb = strMsgForWeb.Replace(strSearchText, "~" + strSearchText + "~(background-color:#ffff00;color:#fffff;)");
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "GetWebLinkJsonArray2");
                    }

                    break;

                case 50:
                    // 处理视频未接通
                    item.SociaChatType = SociaChatType.VideoConnected;
                    strMsgForWeb = "[视频未接通]";
                    break;

                case 10000:
                    // 处理系统消息
                    item.SociaChatType = SociaChatType.SystemMessages;
                    strTypeForWeb = "4";
                    break;
            }
        }

        //提取表情的正则方法
        private string OperateForExpression(string strContent)
        {
            string regular = "\\[(?<expression>.*?)\\]";
            DataTable dt = Utility.GetMatchStringByRegularExpressions(strContent, new string[1] { regular }, new string[1] { "expression" });

            if (dt != null && dt.Rows.Count > 0)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    string strKey = this.FormatStringForWebPage(HttpUtility.UrlDecode(dr["expression"].ToString()));
                    strKey = "[" + strKey + "]";

                    string strExpressionPath = this.GetExpressionPath(strKey);

                    if (!string.IsNullOrEmpty(strExpressionPath))
                    {
                        strContent = strContent.Replace(strKey, strExpressionPath);
                    }
                }
            }

            return strContent;
        }

        private string FormatStringForWebPage(string strContent)
        {
            string value = strContent;

            if (!string.IsNullOrEmpty(value))
            {

                if (value.StartsWith("<![CDATA[") && value.EndsWith("]]>"))
                {
                    value = value.TrimStart(new char[] {
				'<',
				'!',
				'[',
				'C',
				'D',
				'A',
				'T',
				'A',
				'['
			});
                    value = value.TrimEnd(new char[] {
				']',
				']',
				'>'
			});

                }
            }

            return value;
        }

        //获取表情路径（相对路径）
        private string GetExpressionPath(string strKey)
        {
            string strName = string.Empty;

            switch (strKey)
            {
                case "[微笑]":
                    strName = "Expression_1";
                    break;
                case "[撇嘴]":
                    strName = "Expression_2";
                    break;
                case "[色]":
                    strName = "Expression_3";
                    break;
                case "[发呆]":
                case "[發呆]":
                    strName = "Expression_4";
                    break;
                case "[得意]":
                    strName = "Expression_5";
                    break;
                case "[流泪]":
                case "[流淚]":
                    strName = "Expression_6";
                    break;
                case "[害羞]":
                    strName = "Expression_7";
                    break;
                case "[闭嘴]":
                case "[閉嘴]":
                    strName = "Expression_8";
                    break;
                case "[睡]":
                    strName = "Expression_9";
                    break;
                case "[大哭]":
                    strName = "Expression_10";
                    break;
                case "[尴尬]":
                case "[尷尬]":
                    strName = "Expression_11";
                    break;
                case "[发怒]":
                case "[發怒]":
                    strName = "Expression_12";
                    break;
                case "[调皮]":
                case "[調皮]":
                    strName = "Expression_13";
                    break;
                case "[呲牙]":
                    strName = "Expression_14";
                    break;
                case "[惊讶]":
                case "[驚訝]":
                    strName = "Expression_15";
                    break;
                case "[难过]":
                case "[難過]":
                    strName = "Expression_16";
                    break;
                case "[酷]":
                    strName = "Expression_17";
                    break;
                case "[冷汗]":
                    strName = "Expression_18";
                    break;
                case "[抓狂]":
                    strName = "Expression_19";
                    break;
                case "[吐]":
                    strName = "Expression_20";
                    break;
                case "[偷笑]":
                    strName = "Expression_21";
                    break;
                case "[愉快]":
                    strName = "Expression_22";
                    break;
                case "[白眼]":
                    strName = "Expression_23";
                    break;
                case "[傲慢]":
                    strName = "Expression_24";
                    break;
                case "[饥饿]":
                case "[饑餓]":
                    strName = "Expression_25";
                    break;
                case "[困]":
                    strName = "Expression_26";
                    break;
                case "[惊恐]":
                case "[驚恐]":
                    strName = "Expression_27";
                    break;
                case "[流汗]":
                    strName = "Expression_28";
                    break;
                case "[憨笑]":
                    strName = "Expression_29";
                    break;
                case "[悠闲]":
                case "[悠閒]":
                    strName = "Expression_30";
                    break;
                case "[奋斗]":
                case "[奮鬥]":
                    strName = "Expression_31";
                    break;
                case "[咒骂]":
                case "[咒罵]":
                    strName = "Expression_32";
                    break;
                case "[疑问]":
                    strName = "Expression_33";
                    break;
                case "[嘘]":
                    strName = "Expression_34";
                    break;
                case "[晕]":
                case "[暈]":
                    strName = "Expression_35";
                    break;
                case "[疯了]":
                case "[瘋了]":
                    strName = "Expression_36";
                    break;
                case "[衰]":
                    strName = "Expression_37";
                    break;
                case "[骷髅]":
                case "[骷髏]":
                    strName = "Expression_38";
                    break;
                case "[敲打]":
                    strName = "Expression_39";
                    break;
                case "[再见]":
                    strName = "Expression_40";
                    break;
                case "[擦汗]":
                    strName = "Expression_41";
                    break;
                case "[抠鼻]":
                case "[摳鼻]":
                    strName = "Expression_42";
                    break;
                case "[鼓掌]":
                    strName = "Expression_43";
                    break;
                case "[糗大了]":
                    strName = "Expression_44";
                    break;
                case "[坏笑]":
                case "[壞笑]":
                    strName = "Expression_45";
                    break;
                case "[左哼哼]":
                    strName = "Expression_46";
                    break;
                case "[右哼哼]":
                    strName = "Expression_47";
                    break;
                case "[哈欠]":
                    strName = "Expression_48";
                    break;
                case "[鄙视]":
                case "[鄙視]":
                    strName = "Expression_49";
                    break;
                case "[委屈]":
                    strName = "Expression_50";
                    break;
                case "[快哭了]":
                    strName = "Expression_51";
                    break;
                case "[阴险]":
                case "[陰險]":
                    strName = "Expression_52";
                    break;
                case "[亲亲]":
                case "[親親]":
                    strName = "Expression_53";
                    break;
                case "[吓]":
                case "[嚇]":
                    strName = "Expression_54";
                    break;
                case "[可怜]":
                case "[可憐]":
                    strName = "Expression_55";
                    break;
                case "[菜刀]":
                    strName = "Expression_56";
                    break;
                case "[西瓜]":
                    strName = "Expression_57";
                    break;
                case "[啤酒]":
                    strName = "Expression_58";
                    break;
                case "[篮球]":
                case "[籃球]":
                    strName = "Expression_59";
                    break;
                case "[乒乓]":
                    strName = "Expression_60";
                    break;
                case "[咖啡]":
                    strName = "Expression_61";
                    break;
                case "[饭]":
                case "[飯]":
                    strName = "Expression_62";
                    break;
                case "[猪头]":
                case "[豬頭]":
                    strName = "Expression_63";
                    break;
                case "[玫瑰]":
                    strName = "Expression_64";
                    break;
                case "[凋谢]":
                    strName = "Expression_65";
                    break;
                case "[嘴唇]":
                    strName = "Expression_66";
                    break;
                case "[爱心]":
                case "[愛心]":
                    strName = "Expression_67";
                    break;
                case "[心碎]":
                    strName = "Expression_68";
                    break;
                case "[蛋糕]":
                    strName = "Expression_69";
                    break;
                case "[闪电]":
                case "[閃電]":
                    strName = "Expression_70";
                    break;
                case "[炸弹]":
                case "[炸彈]":
                    strName = "Expression_71";
                    break;
                case "[刀]":
                    strName = "Expression_72";
                    break;
                case "[足球]":
                    strName = "Expression_73";
                    break;
                case "[瓢虫]":
                case "[瓢蟲]":
                    strName = "Expression_74";
                    break;
                case "[便便]":
                    strName = "Expression_75";
                    break;
                case "[月亮]":
                    strName = "Expression_76";
                    break;
                case "[太阳]":
                case "[太陽]":
                    strName = "Expression_77";
                    break;
                case "[礼物]":
                case "[禮物]":
                    strName = "Expression_78";
                    break;
                case "[拥抱]":
                case "[擁抱]":
                    strName = "Expression_79";
                    break;
                case "[强]":
                    strName = "Expression_80";
                    break;
                case "[弱]":
                    strName = "Expression_81";
                    break;
                case "[握手]":
                    strName = "Expression_82";
                    break;
                case "[胜利]":
                case "[勝利]":
                    strName = "Expression_83";
                    break;
                case "[抱拳]":
                    strName = "Expression_84";
                    break;
                case "[勾引]":
                    strName = "Expression_85";
                    break;
                case "[拳头]":
                case "[拳頭]":
                    strName = "Expression_86";
                    break;
                case "[差劲]":
                case "[差勁]":
                    strName = "Expression_87";
                    break;
                case "[爱你]":
                case "[愛你]":
                    strName = "Expression_88";
                    break;
                case "[NO]":
                    strName = "Expression_89";
                    break;
                case "[OK]":
                    strName = "Expression_90";
                    break;
                case "[爱情]":
                case "[愛情]":
                    strName = "Expression_91";
                    break;
                case "[飞吻]":
                case "[飛吻]":
                    strName = "Expression_92";
                    break;
                case "[跳跳]":
                    strName = "Expression_93";
                    break;
                case "[发抖]":
                case "[發抖]":
                    strName = "Expression_94";
                    break;
                case "[怄火]":
                case "[慪火]":
                    strName = "Expression_95";
                    break;
                case "[转圈]":
                case "[轉圈]":
                    strName = "Expression_96";
                    break;
                case "[磕头]":
                case "[磕頭]":
                    strName = "Expression_97";
                    break;
                case "[回头]":
                case "[回頭]":
                    strName = "Expression_98";
                    break;
                case "[跳绳]":
                case "[跳繩]":
                    strName = "Expression_99";
                    break;
                case "[挥手]":
                case "[揮手]":
                    strName = "Expression_100";
                    break;
                case "[激动]":
                case "[激動]":
                    strName = "Expression_101";
                    break;
                case "[街舞]":
                    strName = "Expression_102";
                    break;
                case "[献吻]":
                case "[獻吻]":
                    strName = "Expression_103";
                    break;
                case "[左太极]":
                case "[左太極]":
                    strName = "Expression_104";
                    break;
                case "[右太极]":
                case "[右太極]":
                    strName = "Expression_105";
                    break;
            }

            string result = string.Empty;
            if (!string.IsNullOrEmpty(strName))
            {
                //result = String.Format("<img src=""./expression/{0}.png"" width=""21px"" height=""21px""/>", strName)
                result = string.Format("^./expression/{0}.png^(21,21)", strName);
            }
            else
            {
                // Common.LogException(strKey, "GetExpressionPath")
                Common.Log(strKey + "---GetExpressionPath");
            }

            return result;
        }
        private string MsgHide(string strMsgForWeb)
        {
            string strRelust = "";
            try
            {
                double dTLength = (double)strMsgForWeb.Length;
                double dSLength = dTLength / 2;
                dSLength = Math.Floor(dSLength);

                double dCurrentIndex = 1;
                foreach (Char item in strMsgForWeb)
                {
                    if (dCurrentIndex <= dSLength)
                        strRelust = strRelust + item;
                    else
                        strRelust = strRelust + "*";

                    dCurrentIndex++;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MsgHide");
            }
            return strRelust;
        }

        #endregion
    }
}
