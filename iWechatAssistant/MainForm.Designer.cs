﻿namespace iWechatAssistant
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            this.tblayoutMain = new System.Windows.Forms.TableLayoutPanel();
            this.tbPanel2 = new iTong.Components.tbPanel();
            this.lblRecommend = new tbBattery();
            this.tbPanel13 = new iTong.Components.tbPanel();
            this.pnlAccredit = new System.Windows.Forms.Panel();
            this.label4 = new System.Windows.Forms.Label();
            this.pictureBox3 = new System.Windows.Forms.PictureBox();
            this.btnReturnAccredit = new iTong.Components.tbButton();
            this.pnlzzsq = new iTong.Components.tbPanel();
            this.label15 = new System.Windows.Forms.Label();
            this.label9 = new System.Windows.Forms.Label();
            this.lblArtificialAccredit = new System.Windows.Forms.Label();
            this.label11 = new System.Windows.Forms.Label();
            this.label12 = new System.Windows.Forms.Label();
            this.pictureBox7 = new System.Windows.Forms.PictureBox();
            this.pbArtificialAccredit = new System.Windows.Forms.PictureBox();
            this.pictureBox9 = new System.Windows.Forms.PictureBox();
            this.rtxtAccreditEx = new System.Windows.Forms.RichTextBox();
            this.btnrgsq = new iTong.Components.tbButton();
            this.btnzzsq = new iTong.Components.tbButton();
            this.pnlrgsq = new iTong.Components.tbPanel();
            this.label14 = new System.Windows.Forms.Label();
            this.label13 = new System.Windows.Forms.Label();
            this.btnContact = new iTong.Components.tbButton();
            this.label8 = new System.Windows.Forms.Label();
            this.label7 = new System.Windows.Forms.Label();
            this.label6 = new System.Windows.Forms.Label();
            this.label5 = new System.Windows.Forms.Label();
            this.pictureBox6 = new System.Windows.Forms.PictureBox();
            this.pictureBox5 = new System.Windows.Forms.PictureBox();
            this.pictureBox4 = new System.Windows.Forms.PictureBox();
            this.rtxtAccredit = new System.Windows.Forms.RichTextBox();
            this.btnAccredit = new iTong.Components.tbButton();
            this.pnlWelcome = new iTong.Components.tbPanel();
            this.pbRecommend = new System.Windows.Forms.PictureBox();
            this.tbbRecommend = new tbBattery();
            this.tbLabel3 = new iTong.Components.tbLabel();
            this.llblBackupCourse = new System.Windows.Forms.LinkLabel();
            this.richTextBox3 = new System.Windows.Forms.RichTextBox();
            this.richTextBox2 = new System.Windows.Forms.RichTextBox();
            this.btnStart = new iTong.Components.tbButton();
            this.lblWelcone = new iTong.Components.tbLabel();
            this.pnlLoad = new iTong.Components.tbPanel();
            this.pbMsg = new System.Windows.Forms.PictureBox();
            this.lblMsg = new System.Windows.Forms.Label();
            this.pnlWechatList = new iTong.Components.tbPanel();
            this.tbPanel17 = new iTong.Components.tbPanel();
            this.tbButton1 = new iTong.Components.tbButton();
            this.btnWechatPath = new iTong.Components.tbButton();
            this.btnRefreshAccount = new iTong.Components.tbButton();
            this.llblPCPath = new System.Windows.Forms.LinkLabel();
            this.label2 = new System.Windows.Forms.Label();
            this.txtWechatPath = new System.Windows.Forms.TextBox();
            this.lblWechatPath = new System.Windows.Forms.Label();
            this.pnlWechatAccounts = new iTong.Components.tbPanel();
            this.tbPanel15 = new iTong.Components.tbPanel();
            this.btnGetBack = new iTong.Components.tbButton();
            this.tbPanel12 = new iTong.Components.tbPanel();
            this.plnShowChat = new iTong.Components.tbPanel();
            this.tbPanel5 = new iTong.Components.tbPanel();
            this.tbPanel3 = new iTong.Components.tbPanel();
            this.pnlPage = new iTong.Components.tbPanel();
            this.tbpPage = new iWechatAssistant.zComponents.tbPage();
            this.tbPanel18 = new iTong.Components.tbPanel();
            this.tbPanel4 = new iTong.Components.tbPanel();
            this.lblSystemInfo = new iTong.Components.tbLabel();
            this.pnlTitle = new iTong.Components.tbPanel();
            this.chkLock = new iTong.Components.tbCheckBox();
            this.dtpEnd = new System.Windows.Forms.DateTimePicker();
            this.tbPanel19 = new iTong.Components.tbPanel();
            this.TbPanel8 = new iTong.Components.tbPanel();
            this.btnTimeSelect = new iTong.Components.tbButton();
            this.lblStartTime = new System.Windows.Forms.Label();
            this.lblEndTime = new System.Windows.Forms.Label();
            this.btnOK = new iTong.Components.tbButton();
            this.dtpEndTime = new System.Windows.Forms.DateTimePicker();
            this.dtpStartTime = new System.Windows.Forms.DateTimePicker();
            this.dtpStart = new System.Windows.Forms.DateTimePicker();
            this.pnlLoadChat = new iTong.Components.tbPanel();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.lblLoading = new System.Windows.Forms.Label();
            this.pnlFriend = new iTong.Components.tbPanel();
            this.tbPanel7 = new iTong.Components.tbPanel();
            this.tbPanel10 = new iTong.Components.tbPanel();
            this.lblLinkmanMsg = new iTong.Components.tbLabel();
            this.tbPanel1 = new iTong.Components.tbPanel();
            this.btnUpdateFriend = new iTong.Components.tbButton();
            this.tbPanel9 = new iTong.Components.tbPanel();
            this.cbxSelectAll = new iTong.Components.tbCheckBox();
            this.tbPanel6 = new iTong.Components.tbPanel();
            this.tbPanel11 = new iTong.Components.tbPanel();
            this.btnNewExport = new iTong.Components.tbButton();
            this.btnExport = new iTong.Components.tbButton();
            this.lblState = new System.Windows.Forms.Label();
            this.pnlTop = new iTong.Components.tbPanel();
            this.llblExportPreview = new System.Windows.Forms.LinkLabel();
            this.lblRegister = new tbBattery();
            this.picTimeScreen = new System.Windows.Forms.PictureBox();
            this.btnTimeScreen = new iTong.Components.tbButton();
            this.txtSearchChat = new iTong.Components.tbSearch();
            this.tbPanel14 = new iTong.Components.tbPanel();
            this.btnSuperior = new iTong.Components.tbButton();
            this.btnWeCahtUsers = new iTong.Components.tbButton();
            this.txtSearchFriend = new iTong.Components.tbSearch();
            this.btnPCPath = new iTong.Components.tbButton();
            this.lblCurrentAccount = new iTong.Components.tbLabel();
            this.cmsExportNews = new iTong.Components.tbContextMenuStrip(this.components);
            this.按格式导出ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiHtml = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiExcelEx = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiTxtEx = new System.Windows.Forms.ToolStripMenuItem();
            this.按媒体导出ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiPicture = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiVoice = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiVideo = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiFriend = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiAll = new System.Windows.Forms.ToolStripMenuItem();
            this.bgwShowAccount = new System.ComponentModel.BackgroundWorker();
            this.tmrTimer = new System.Windows.Forms.Timer(this.components);
            this.mNotifyIcon = new System.Windows.Forms.NotifyIcon(this.components);
            this.mMenu = new iTong.Components.tbContextMenuStrip(this.components);
            this.tss4 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiCreateError = new System.Windows.Forms.ToolStripMenuItem();
            this.tss2 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiExist = new System.Windows.Forms.ToolStripMenuItem();
            this.tmrUpgrade = new System.Windows.Forms.Timer(this.components);
            this.munOperator = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiOpenFolder = new System.Windows.Forms.ToolStripMenuItem();
            this.ToolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiOpen = new System.Windows.Forms.ToolStripMenuItem();
            this.btn_normal = new iTong.Components.tbButton();
            this.btn_close = new iTong.Components.tbButton();
            this.btn_minimize = new iTong.Components.tbButton();
            this.btnToVideo = new iTong.Components.tbButton();
            this.btnContactEx = new iTong.Components.tbButton();
            this.btnSetting = new iTong.Components.tbButton();
            this.btnFeedback = new iTong.Components.tbButton();
            this.tblayoutMain.SuspendLayout();
            this.tbPanel2.SuspendLayout();
            this.pnlAccredit.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).BeginInit();
            this.pnlzzsq.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox7)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pbArtificialAccredit)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox9)).BeginInit();
            this.pnlrgsq.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox6)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox5)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).BeginInit();
            this.pnlWelcome.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pbRecommend)).BeginInit();
            this.pnlLoad.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pbMsg)).BeginInit();
            this.pnlWechatList.SuspendLayout();
            this.tbPanel17.SuspendLayout();
            this.tbPanel15.SuspendLayout();
            this.tbPanel12.SuspendLayout();
            this.plnShowChat.SuspendLayout();
            this.tbPanel5.SuspendLayout();
            this.tbPanel3.SuspendLayout();
            this.pnlPage.SuspendLayout();
            this.pnlTitle.SuspendLayout();
            this.pnlLoadChat.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.pnlFriend.SuspendLayout();
            this.tbPanel7.SuspendLayout();
            this.tbPanel1.SuspendLayout();
            this.tbPanel6.SuspendLayout();
            this.pnlTop.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picTimeScreen)).BeginInit();
            this.cmsExportNews.SuspendLayout();
            this.mMenu.SuspendLayout();
            this.munOperator.SuspendLayout();
            this.SuspendLayout();
            // 
            // tblayoutMain
            // 
            this.tblayoutMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tblayoutMain.ColumnCount = 5;
            this.tblayoutMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 20F));
            this.tblayoutMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 20F));
            this.tblayoutMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 35F));
            this.tblayoutMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 5F));
            this.tblayoutMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 20F));
            this.tblayoutMain.Controls.Add(this.tbPanel2, 0, 1);
            this.tblayoutMain.Controls.Add(this.pnlAccredit, 4, 0);
            this.tblayoutMain.Controls.Add(this.pnlWelcome, 0, 0);
            this.tblayoutMain.Controls.Add(this.pnlLoad, 3, 0);
            this.tblayoutMain.Controls.Add(this.pnlWechatList, 1, 0);
            this.tblayoutMain.Controls.Add(this.tbPanel12, 2, 0);
            this.tblayoutMain.Location = new System.Drawing.Point(1, 38);
            this.tblayoutMain.Margin = new System.Windows.Forms.Padding(0);
            this.tblayoutMain.Name = "tblayoutMain";
            this.tblayoutMain.RowCount = 2;
            this.tblayoutMain.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tblayoutMain.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 44F));
            this.tblayoutMain.Size = new System.Drawing.Size(2505, 682);
            this.tblayoutMain.TabIndex = 24;
            // 
            // tbPanel2
            // 
            this.tbPanel2.BackColor = System.Drawing.Color.Transparent;
            this.tblayoutMain.SetColumnSpan(this.tbPanel2, 5);
            this.tbPanel2.Controls.Add(this.lblRecommend);
            this.tbPanel2.Controls.Add(this.tbPanel13);
            this.tbPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tbPanel2.Location = new System.Drawing.Point(0, 638);
            this.tbPanel2.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel2.Name = "tbPanel2";
            this.tbPanel2.Size = new System.Drawing.Size(2505, 44);
            this.tbPanel2.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel2.TabIndex = 14;
            this.tbPanel2.tbBackgroundImage = null;
            this.tbPanel2.tbShowWatermark = false;
            this.tbPanel2.tbSplit = "0,0,0,0";
            this.tbPanel2.tbWatermark = null;
            this.tbPanel2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel2.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // lblRecommend
            // 
            this.lblRecommend.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblRecommend.BackColor = System.Drawing.Color.Transparent;
            this.lblRecommend.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lblRecommend.ForeColor = System.Drawing.Color.Maroon;
            this.lblRecommend.Location = new System.Drawing.Point(907, 13);
            this.lblRecommend.Name = "lblRecommend";
            this.lblRecommend.Size = new System.Drawing.Size(700, 18);
            this.lblRecommend.TabIndex = 105;
            this.lblRecommend.Text = "人工授权服务在线客服支持：周一至周五10：00-12：00  13：30-18：30   周六14：00-18：00 周日休息，法定节假日另行通知";
            this.lblRecommend.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblRecommend.Click += new System.EventHandler(this.lblRecommend_Click);
            // 
            // tbPanel13
            // 
            this.tbPanel13.BackColor = System.Drawing.Color.Gainsboro;
            this.tbPanel13.Dock = System.Windows.Forms.DockStyle.Top;
            this.tbPanel13.Location = new System.Drawing.Point(0, 0);
            this.tbPanel13.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel13.Name = "tbPanel13";
            this.tbPanel13.Size = new System.Drawing.Size(2505, 1);
            this.tbPanel13.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel13.TabIndex = 86;
            this.tbPanel13.tbBackgroundImage = null;
            this.tbPanel13.tbShowWatermark = false;
            this.tbPanel13.tbSplit = "0,0,0,0";
            this.tbPanel13.tbWatermark = null;
            this.tbPanel13.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel13.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // pnlAccredit
            // 
            this.pnlAccredit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.pnlAccredit.Controls.Add(this.label4);
            this.pnlAccredit.Controls.Add(this.pictureBox3);
            this.pnlAccredit.Controls.Add(this.btnReturnAccredit);
            this.pnlAccredit.Controls.Add(this.pnlzzsq);
            this.pnlAccredit.Controls.Add(this.pnlrgsq);
            this.pnlAccredit.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlAccredit.Location = new System.Drawing.Point(2003, 0);
            this.pnlAccredit.Margin = new System.Windows.Forms.Padding(0);
            this.pnlAccredit.Name = "pnlAccredit";
            this.pnlAccredit.Size = new System.Drawing.Size(502, 638);
            this.pnlAccredit.TabIndex = 13;
            // 
            // label4
            // 
            this.label4.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label4.AutoSize = true;
            this.label4.Font = new System.Drawing.Font("宋体", 21.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label4.Location = new System.Drawing.Point(118, 117);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(303, 29);
            this.label4.TabIndex = 88;
            this.label4.Text = "您尚未授权或授权过期";
            // 
            // pictureBox3
            // 
            this.pictureBox3.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox3.Image = global::iWechatAssistant.Properties.Resources.dunpai;
            this.pictureBox3.Location = new System.Drawing.Point(81, 115);
            this.pictureBox3.Name = "pictureBox3";
            this.pictureBox3.Size = new System.Drawing.Size(33, 37);
            this.pictureBox3.TabIndex = 87;
            this.pictureBox3.TabStop = false;
            // 
            // btnReturnAccredit
            // 
            this.btnReturnAccredit.BackColor = System.Drawing.Color.Transparent;
            this.btnReturnAccredit.BindingForm = null;
            this.btnReturnAccredit.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnReturnAccredit.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnReturnAccredit.Location = new System.Drawing.Point(10, 13);
            this.btnReturnAccredit.Name = "btnReturnAccredit";
            this.btnReturnAccredit.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnReturnAccredit.Selectable = true;
            this.btnReturnAccredit.Size = new System.Drawing.Size(16, 14);
            this.btnReturnAccredit.TabIndex = 85;
            this.btnReturnAccredit.tbAdriftIconWhenHover = false;
            this.btnReturnAccredit.tbAutoSize = false;
            this.btnReturnAccredit.tbAutoSizeEx = true;
            this.btnReturnAccredit.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_retreatex;
            this.btnReturnAccredit.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnReturnAccredit.tbBadgeNumber = 0;
            this.btnReturnAccredit.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnReturnAccredit.tbDefaultText = "";
            this.btnReturnAccredit.tbEnableEnter = true;
            this.btnReturnAccredit.tbEndEllipsis = false;
            this.btnReturnAccredit.tbIconHoldPlace = true;
            this.btnReturnAccredit.tbIconImage = null;
            this.btnReturnAccredit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnReturnAccredit.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnReturnAccredit.tbIconMore = false;
            this.btnReturnAccredit.tbIconMouseDown = null;
            this.btnReturnAccredit.tbIconMouseHover = null;
            this.btnReturnAccredit.tbIconMouseLeave = null;
            this.btnReturnAccredit.tbIconPlaceText = 2;
            this.btnReturnAccredit.tbIconReadOnly = null;
            this.btnReturnAccredit.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnReturnAccredit.tbImageMouseDown = null;
            this.btnReturnAccredit.tbImageMouseHover = null;
            this.btnReturnAccredit.tbImageMouseLeave = null;
            this.btnReturnAccredit.tbProgressValue = 50;
            this.btnReturnAccredit.tbReadOnly = false;
            this.btnReturnAccredit.tbReadOnlyText = false;
            this.btnReturnAccredit.tbShadow = false;
            this.btnReturnAccredit.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnReturnAccredit.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnReturnAccredit.tbShowDot = false;
            this.btnReturnAccredit.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnReturnAccredit.tbShowMoreIconImg")));
            this.btnReturnAccredit.tbShowNew = false;
            this.btnReturnAccredit.tbShowProgress = false;
            this.btnReturnAccredit.tbShowTip = true;
            this.btnReturnAccredit.tbShowToolTipOnButton = false;
            this.btnReturnAccredit.tbSplit = "12,1,1,1";
            this.btnReturnAccredit.tbText = "";
            this.btnReturnAccredit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnReturnAccredit.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnReturnAccredit.tbTextColorDisable = System.Drawing.Color.LightGray;
            this.btnReturnAccredit.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnReturnAccredit.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnReturnAccredit.tbTextMouseDownPlace = 2;
            this.btnReturnAccredit.tbToolTip = "";
            this.btnReturnAccredit.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnReturnAccredit.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btnReturnAccredit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnReturnAccredit.tbUpToDownWhenCenter = false;
            this.btnReturnAccredit.TimerCountdownNum = 3;
            this.btnReturnAccredit.TimerInterval = 1;
            this.btnReturnAccredit.VisibleEx = true;
            this.btnReturnAccredit.Click += new System.EventHandler(this.btnReturnAccredit_Click);
            // 
            // pnlzzsq
            // 
            this.pnlzzsq.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pnlzzsq.Controls.Add(this.label15);
            this.pnlzzsq.Controls.Add(this.label9);
            this.pnlzzsq.Controls.Add(this.lblArtificialAccredit);
            this.pnlzzsq.Controls.Add(this.label11);
            this.pnlzzsq.Controls.Add(this.label12);
            this.pnlzzsq.Controls.Add(this.pictureBox7);
            this.pnlzzsq.Controls.Add(this.pbArtificialAccredit);
            this.pnlzzsq.Controls.Add(this.pictureBox9);
            this.pnlzzsq.Controls.Add(this.rtxtAccreditEx);
            this.pnlzzsq.Controls.Add(this.btnrgsq);
            this.pnlzzsq.Controls.Add(this.btnzzsq);
            this.pnlzzsq.Location = new System.Drawing.Point(-40, 156);
            this.pnlzzsq.Name = "pnlzzsq";
            this.pnlzzsq.Size = new System.Drawing.Size(582, 355);
            this.pnlzzsq.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlzzsq.TabIndex = 86;
            this.pnlzzsq.tbBackgroundImage = null;
            this.pnlzzsq.tbShowWatermark = false;
            this.pnlzzsq.tbSplit = "0,0,0,0";
            this.pnlzzsq.tbWatermark = null;
            this.pnlzzsq.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlzzsq.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // label15
            // 
            this.label15.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label15.AutoSize = true;
            this.label15.Font = new System.Drawing.Font("宋体", 10F);
            this.label15.ForeColor = System.Drawing.Color.Red;
            this.label15.Location = new System.Drawing.Point(230, 49);
            this.label15.Name = "label15";
            this.label15.Size = new System.Drawing.Size(63, 14);
            this.label15.TabIndex = 96;
            this.label15.Text = "要查看的";
            // 
            // label9
            // 
            this.label9.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label9.AutoSize = true;
            this.label9.Font = new System.Drawing.Font("宋体", 10F);
            this.label9.ForeColor = System.Drawing.Color.Red;
            this.label9.Location = new System.Drawing.Point(90, 122);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(329, 14);
            this.label9.TabIndex = 92;
            this.label9.Text = "至提示授权成功前，请勿关闭微信PC版或退出帐号。";
            // 
            // lblArtificialAccredit
            // 
            this.lblArtificialAccredit.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblArtificialAccredit.AutoSize = true;
            this.lblArtificialAccredit.Font = new System.Drawing.Font("宋体", 10F);
            this.lblArtificialAccredit.Location = new System.Drawing.Point(88, 170);
            this.lblArtificialAccredit.Name = "lblArtificialAccredit";
            this.lblArtificialAccredit.Size = new System.Drawing.Size(357, 28);
            this.lblArtificialAccredit.TabIndex = 93;
            this.lblArtificialAccredit.Text = "如果多次授权失败，请点击“人工授权”联系客服授权。\r\n（授权服务时间见下方）";
            // 
            // label11
            // 
            this.label11.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label11.AutoSize = true;
            this.label11.Font = new System.Drawing.Font("宋体", 10F);
            this.label11.Location = new System.Drawing.Point(88, 104);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(280, 14);
            this.label11.TabIndex = 94;
            this.label11.Text = "登入成功后，点击“自助授权 beta”按钮。";
            // 
            // label12
            // 
            this.label12.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label12.AutoSize = true;
            this.label12.Font = new System.Drawing.Font("宋体", 10F);
            this.label12.Location = new System.Drawing.Point(86, 49);
            this.label12.Name = "label12";
            this.label12.Size = new System.Drawing.Size(287, 14);
            this.label12.TabIndex = 95;
            this.label12.Text = "打开电脑版微信登录您要查看的  微信帐号。";
            // 
            // pictureBox7
            // 
            this.pictureBox7.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox7.Image = global::iWechatAssistant.Properties.Resources._2;
            this.pictureBox7.Location = new System.Drawing.Point(55, 107);
            this.pictureBox7.Name = "pictureBox7";
            this.pictureBox7.Size = new System.Drawing.Size(25, 25);
            this.pictureBox7.TabIndex = 91;
            this.pictureBox7.TabStop = false;
            // 
            // pbArtificialAccredit
            // 
            this.pbArtificialAccredit.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pbArtificialAccredit.Image = global::iWechatAssistant.Properties.Resources._3;
            this.pbArtificialAccredit.Location = new System.Drawing.Point(55, 170);
            this.pbArtificialAccredit.Name = "pbArtificialAccredit";
            this.pbArtificialAccredit.Size = new System.Drawing.Size(25, 25);
            this.pbArtificialAccredit.TabIndex = 90;
            this.pbArtificialAccredit.TabStop = false;
            // 
            // pictureBox9
            // 
            this.pictureBox9.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox9.Image = global::iWechatAssistant.Properties.Resources._1;
            this.pictureBox9.Location = new System.Drawing.Point(55, 44);
            this.pictureBox9.Name = "pictureBox9";
            this.pictureBox9.Size = new System.Drawing.Size(25, 25);
            this.pictureBox9.TabIndex = 89;
            this.pictureBox9.TabStop = false;
            // 
            // rtxtAccreditEx
            // 
            this.rtxtAccreditEx.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.rtxtAccreditEx.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.rtxtAccreditEx.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.rtxtAccreditEx.Location = new System.Drawing.Point(435, 319);
            this.rtxtAccreditEx.Name = "rtxtAccreditEx";
            this.rtxtAccreditEx.ReadOnly = true;
            this.rtxtAccreditEx.Size = new System.Drawing.Size(132, 61);
            this.rtxtAccreditEx.TabIndex = 4;
            this.rtxtAccreditEx.Text = resources.GetString("rtxtAccreditEx.Text");
            this.rtxtAccreditEx.Visible = false;
            // 
            // btnrgsq
            // 
            this.btnrgsq.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnrgsq.BackColor = System.Drawing.Color.Transparent;
            this.btnrgsq.BindingForm = null;
            this.btnrgsq.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnrgsq.Location = new System.Drawing.Point(452, 170);
            this.btnrgsq.Name = "btnrgsq";
            this.btnrgsq.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnrgsq.Selectable = true;
            this.btnrgsq.Size = new System.Drawing.Size(106, 23);
            this.btnrgsq.TabIndex = 87;
            this.btnrgsq.tbAdriftIconWhenHover = false;
            this.btnrgsq.tbAutoSize = false;
            this.btnrgsq.tbAutoSizeEx = false;
            this.btnrgsq.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_white;
            this.btnrgsq.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnrgsq.tbBadgeNumber = 0;
            this.btnrgsq.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnrgsq.tbDefaultText = "";
            this.btnrgsq.tbEnableEnter = true;
            this.btnrgsq.tbEndEllipsis = false;
            this.btnrgsq.tbIconHoldPlace = true;
            this.btnrgsq.tbIconImage = null;
            this.btnrgsq.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnrgsq.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnrgsq.tbIconMore = false;
            this.btnrgsq.tbIconMouseDown = null;
            this.btnrgsq.tbIconMouseHover = null;
            this.btnrgsq.tbIconMouseLeave = null;
            this.btnrgsq.tbIconPlaceText = 2;
            this.btnrgsq.tbIconReadOnly = null;
            this.btnrgsq.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnrgsq.tbImageMouseDown = null;
            this.btnrgsq.tbImageMouseHover = null;
            this.btnrgsq.tbImageMouseLeave = null;
            this.btnrgsq.tbProgressValue = 50;
            this.btnrgsq.tbReadOnly = false;
            this.btnrgsq.tbReadOnlyText = false;
            this.btnrgsq.tbShadow = false;
            this.btnrgsq.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnrgsq.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnrgsq.tbShowDot = false;
            this.btnrgsq.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnrgsq.tbShowMoreIconImg")));
            this.btnrgsq.tbShowNew = false;
            this.btnrgsq.tbShowProgress = false;
            this.btnrgsq.tbShowTip = true;
            this.btnrgsq.tbShowToolTipOnButton = false;
            this.btnrgsq.tbSplit = "3,3,3,3";
            this.btnrgsq.tbText = "人工授权";
            this.btnrgsq.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnrgsq.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnrgsq.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnrgsq.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnrgsq.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnrgsq.tbTextMouseDownPlace = 0;
            this.btnrgsq.tbToolTip = "";
            this.btnrgsq.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnrgsq.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnrgsq.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnrgsq.tbUpToDownWhenCenter = false;
            this.btnrgsq.TimerCountdownNum = 3;
            this.btnrgsq.TimerInterval = 1;
            this.btnrgsq.VisibleEx = true;
            this.btnrgsq.Click += new System.EventHandler(this.btnrgsq_Click);
            // 
            // btnzzsq
            // 
            this.btnzzsq.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnzzsq.BackColor = System.Drawing.Color.Transparent;
            this.btnzzsq.BindingForm = null;
            this.btnzzsq.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnzzsq.Location = new System.Drawing.Point(452, 109);
            this.btnzzsq.Name = "btnzzsq";
            this.btnzzsq.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnzzsq.Selectable = true;
            this.btnzzsq.Size = new System.Drawing.Size(106, 23);
            this.btnzzsq.TabIndex = 87;
            this.btnzzsq.tbAdriftIconWhenHover = false;
            this.btnzzsq.tbAutoSize = false;
            this.btnzzsq.tbAutoSizeEx = false;
            this.btnzzsq.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnzzsq.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnzzsq.tbBadgeNumber = 0;
            this.btnzzsq.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnzzsq.tbDefaultText = "";
            this.btnzzsq.tbEnableEnter = true;
            this.btnzzsq.tbEndEllipsis = false;
            this.btnzzsq.tbIconHoldPlace = true;
            this.btnzzsq.tbIconImage = null;
            this.btnzzsq.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnzzsq.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnzzsq.tbIconMore = false;
            this.btnzzsq.tbIconMouseDown = null;
            this.btnzzsq.tbIconMouseHover = null;
            this.btnzzsq.tbIconMouseLeave = null;
            this.btnzzsq.tbIconPlaceText = 2;
            this.btnzzsq.tbIconReadOnly = null;
            this.btnzzsq.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnzzsq.tbImageMouseDown = null;
            this.btnzzsq.tbImageMouseHover = null;
            this.btnzzsq.tbImageMouseLeave = null;
            this.btnzzsq.tbProgressValue = 50;
            this.btnzzsq.tbReadOnly = false;
            this.btnzzsq.tbReadOnlyText = false;
            this.btnzzsq.tbShadow = false;
            this.btnzzsq.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnzzsq.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnzzsq.tbShowDot = false;
            this.btnzzsq.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnzzsq.tbShowMoreIconImg")));
            this.btnzzsq.tbShowNew = false;
            this.btnzzsq.tbShowProgress = false;
            this.btnzzsq.tbShowTip = true;
            this.btnzzsq.tbShowToolTipOnButton = false;
            this.btnzzsq.tbSplit = "3,3,3,3";
            this.btnzzsq.tbText = "自助授权 beta";
            this.btnzzsq.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnzzsq.tbTextColor = System.Drawing.Color.White;
            this.btnzzsq.tbTextColorDisable = System.Drawing.Color.White;
            this.btnzzsq.tbTextColorDown = System.Drawing.Color.White;
            this.btnzzsq.tbTextColorHover = System.Drawing.Color.White;
            this.btnzzsq.tbTextMouseDownPlace = 0;
            this.btnzzsq.tbToolTip = "";
            this.btnzzsq.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnzzsq.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnzzsq.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnzzsq.tbUpToDownWhenCenter = false;
            this.btnzzsq.TimerCountdownNum = 3;
            this.btnzzsq.TimerInterval = 1;
            this.btnzzsq.VisibleEx = true;
            this.btnzzsq.Click += new System.EventHandler(this.btnzzsq_Click);
            // 
            // pnlrgsq
            // 
            this.pnlrgsq.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pnlrgsq.Controls.Add(this.label14);
            this.pnlrgsq.Controls.Add(this.label13);
            this.pnlrgsq.Controls.Add(this.btnContact);
            this.pnlrgsq.Controls.Add(this.label8);
            this.pnlrgsq.Controls.Add(this.label7);
            this.pnlrgsq.Controls.Add(this.label6);
            this.pnlrgsq.Controls.Add(this.label5);
            this.pnlrgsq.Controls.Add(this.pictureBox6);
            this.pnlrgsq.Controls.Add(this.pictureBox5);
            this.pnlrgsq.Controls.Add(this.pictureBox4);
            this.pnlrgsq.Controls.Add(this.rtxtAccredit);
            this.pnlrgsq.Controls.Add(this.btnAccredit);
            this.pnlrgsq.Location = new System.Drawing.Point(-40, 156);
            this.pnlrgsq.Name = "pnlrgsq";
            this.pnlrgsq.Size = new System.Drawing.Size(582, 355);
            this.pnlrgsq.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlrgsq.TabIndex = 86;
            this.pnlrgsq.tbBackgroundImage = null;
            this.pnlrgsq.tbShowWatermark = false;
            this.pnlrgsq.tbSplit = "0,0,0,0";
            this.pnlrgsq.tbWatermark = null;
            this.pnlrgsq.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlrgsq.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            this.pnlrgsq.Visible = false;
            // 
            // label14
            // 
            this.label14.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label14.AutoSize = true;
            this.label14.Font = new System.Drawing.Font("宋体", 10F);
            this.label14.ForeColor = System.Drawing.Color.Red;
            this.label14.Location = new System.Drawing.Point(178, 171);
            this.label14.Margin = new System.Windows.Forms.Padding(3, 0, 0, 0);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(77, 14);
            this.label14.TabIndex = 10;
            this.label14.Text = "等待一分钟";
            this.label14.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // label13
            // 
            this.label13.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label13.AutoSize = true;
            this.label13.Font = new System.Drawing.Font("宋体", 10F);
            this.label13.ForeColor = System.Drawing.Color.Red;
            this.label13.Location = new System.Drawing.Point(90, 107);
            this.label13.Margin = new System.Windows.Forms.Padding(3, 0, 0, 0);
            this.label13.Name = "label13";
            this.label13.Size = new System.Drawing.Size(91, 14);
            this.label13.TabIndex = 9;
            this.label13.Text = "手机微信扫描";
            this.label13.TextAlign = System.Drawing.ContentAlignment.TopRight;
            // 
            // btnContact
            // 
            this.btnContact.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnContact.BackColor = System.Drawing.Color.Transparent;
            this.btnContact.BindingForm = null;
            this.btnContact.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnContact.Location = new System.Drawing.Point(452, 44);
            this.btnContact.Name = "btnContact";
            this.btnContact.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnContact.Selectable = true;
            this.btnContact.Size = new System.Drawing.Size(105, 23);
            this.btnContact.TabIndex = 8;
            this.btnContact.tbAdriftIconWhenHover = false;
            this.btnContact.tbAutoSize = false;
            this.btnContact.tbAutoSizeEx = false;
            this.btnContact.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnContact.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnContact.tbBadgeNumber = 0;
            this.btnContact.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnContact.tbDefaultText = "";
            this.btnContact.tbEnableEnter = true;
            this.btnContact.tbEndEllipsis = false;
            this.btnContact.tbIconHoldPlace = true;
            this.btnContact.tbIconImage = null;
            this.btnContact.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnContact.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnContact.tbIconMore = false;
            this.btnContact.tbIconMouseDown = null;
            this.btnContact.tbIconMouseHover = null;
            this.btnContact.tbIconMouseLeave = null;
            this.btnContact.tbIconPlaceText = 2;
            this.btnContact.tbIconReadOnly = null;
            this.btnContact.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnContact.tbImageMouseDown = null;
            this.btnContact.tbImageMouseHover = null;
            this.btnContact.tbImageMouseLeave = null;
            this.btnContact.tbProgressValue = 50;
            this.btnContact.tbReadOnly = false;
            this.btnContact.tbReadOnlyText = false;
            this.btnContact.tbShadow = false;
            this.btnContact.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnContact.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnContact.tbShowDot = false;
            this.btnContact.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnContact.tbShowMoreIconImg")));
            this.btnContact.tbShowNew = false;
            this.btnContact.tbShowProgress = false;
            this.btnContact.tbShowTip = true;
            this.btnContact.tbShowToolTipOnButton = false;
            this.btnContact.tbSplit = "3,3,3,3";
            this.btnContact.tbText = "联系客服";
            this.btnContact.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnContact.tbTextColor = System.Drawing.Color.White;
            this.btnContact.tbTextColorDisable = System.Drawing.Color.White;
            this.btnContact.tbTextColorDown = System.Drawing.Color.White;
            this.btnContact.tbTextColorHover = System.Drawing.Color.White;
            this.btnContact.tbTextMouseDownPlace = 0;
            this.btnContact.tbToolTip = "";
            this.btnContact.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnContact.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnContact.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnContact.tbUpToDownWhenCenter = false;
            this.btnContact.TimerCountdownNum = 3;
            this.btnContact.TimerInterval = 1;
            this.btnContact.VisibleEx = true;
            this.btnContact.Click += new System.EventHandler(this.btnContact_Click);
            // 
            // label8
            // 
            this.label8.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label8.AutoSize = true;
            this.label8.Font = new System.Drawing.Font("宋体", 10F);
            this.label8.ForeColor = System.Drawing.Color.Red;
            this.label8.Location = new System.Drawing.Point(74, 223);
            this.label8.Name = "label8";
            this.label8.Size = new System.Drawing.Size(371, 14);
            this.label8.TabIndex = 7;
            this.label8.Text = "* 授权二维码登入不涉及提现，不会危害您的帐号等安全。";
            // 
            // label7
            // 
            this.label7.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label7.AutoSize = true;
            this.label7.Font = new System.Drawing.Font("宋体", 10F);
            this.label7.Location = new System.Drawing.Point(90, 171);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(308, 28);
            this.label7.TabIndex = 7;
            this.label7.Text = "扫描后请务必等待一分钟 （等获取好友信息），\r\n后点击“已授权，继续”即可。";
            // 
            // label6
            // 
            this.label6.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label6.AutoSize = true;
            this.label6.Font = new System.Drawing.Font("宋体", 10F);
            this.label6.Location = new System.Drawing.Point(94, 107);
            this.label6.Name = "label6";
            this.label6.Size = new System.Drawing.Size(315, 28);
            this.label6.TabIndex = 7;
            this.label6.Text = "手机微信扫描客服提供的二维码授权登入\r\n（微信备份聊天记录查看需要手机微信协助授权）";
            // 
            // label5
            // 
            this.label5.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.label5.AutoSize = true;
            this.label5.Font = new System.Drawing.Font("宋体", 10F);
            this.label5.Location = new System.Drawing.Point(90, 44);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(301, 28);
            this.label5.TabIndex = 7;
            this.label5.Text = "点击“联系客服”授权，加入QQ群联系授权客服\r\n“a授权客服”进行授权。";
            // 
            // pictureBox6
            // 
            this.pictureBox6.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox6.Image = global::iWechatAssistant.Properties.Resources._2;
            this.pictureBox6.Location = new System.Drawing.Point(55, 107);
            this.pictureBox6.Name = "pictureBox6";
            this.pictureBox6.Size = new System.Drawing.Size(25, 25);
            this.pictureBox6.TabIndex = 6;
            this.pictureBox6.TabStop = false;
            // 
            // pictureBox5
            // 
            this.pictureBox5.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox5.Image = global::iWechatAssistant.Properties.Resources._3;
            this.pictureBox5.Location = new System.Drawing.Point(55, 170);
            this.pictureBox5.Name = "pictureBox5";
            this.pictureBox5.Size = new System.Drawing.Size(25, 25);
            this.pictureBox5.TabIndex = 5;
            this.pictureBox5.TabStop = false;
            // 
            // pictureBox4
            // 
            this.pictureBox4.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox4.Image = global::iWechatAssistant.Properties.Resources._1;
            this.pictureBox4.Location = new System.Drawing.Point(55, 44);
            this.pictureBox4.Name = "pictureBox4";
            this.pictureBox4.Size = new System.Drawing.Size(25, 25);
            this.pictureBox4.TabIndex = 4;
            this.pictureBox4.TabStop = false;
            // 
            // rtxtAccredit
            // 
            this.rtxtAccredit.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.rtxtAccredit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.rtxtAccredit.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.rtxtAccredit.Location = new System.Drawing.Point(492, 356);
            this.rtxtAccredit.Name = "rtxtAccredit";
            this.rtxtAccredit.ReadOnly = true;
            this.rtxtAccredit.Size = new System.Drawing.Size(87, 55);
            this.rtxtAccredit.TabIndex = 3;
            this.rtxtAccredit.Text = resources.GetString("rtxtAccredit.Text");
            this.rtxtAccredit.Visible = false;
            // 
            // btnAccredit
            // 
            this.btnAccredit.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnAccredit.BackColor = System.Drawing.Color.Transparent;
            this.btnAccredit.BindingForm = null;
            this.btnAccredit.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnAccredit.Location = new System.Drawing.Point(452, 171);
            this.btnAccredit.Name = "btnAccredit";
            this.btnAccredit.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnAccredit.Selectable = true;
            this.btnAccredit.Size = new System.Drawing.Size(105, 23);
            this.btnAccredit.TabIndex = 1;
            this.btnAccredit.tbAdriftIconWhenHover = false;
            this.btnAccredit.tbAutoSize = false;
            this.btnAccredit.tbAutoSizeEx = false;
            this.btnAccredit.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_white;
            this.btnAccredit.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnAccredit.tbBadgeNumber = 0;
            this.btnAccredit.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnAccredit.tbDefaultText = "";
            this.btnAccredit.tbEnableEnter = true;
            this.btnAccredit.tbEndEllipsis = false;
            this.btnAccredit.tbIconHoldPlace = true;
            this.btnAccredit.tbIconImage = null;
            this.btnAccredit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnAccredit.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnAccredit.tbIconMore = false;
            this.btnAccredit.tbIconMouseDown = null;
            this.btnAccredit.tbIconMouseHover = null;
            this.btnAccredit.tbIconMouseLeave = null;
            this.btnAccredit.tbIconPlaceText = 2;
            this.btnAccredit.tbIconReadOnly = null;
            this.btnAccredit.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnAccredit.tbImageMouseDown = null;
            this.btnAccredit.tbImageMouseHover = null;
            this.btnAccredit.tbImageMouseLeave = null;
            this.btnAccredit.tbProgressValue = 50;
            this.btnAccredit.tbReadOnly = false;
            this.btnAccredit.tbReadOnlyText = false;
            this.btnAccredit.tbShadow = false;
            this.btnAccredit.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnAccredit.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnAccredit.tbShowDot = false;
            this.btnAccredit.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnAccredit.tbShowMoreIconImg")));
            this.btnAccredit.tbShowNew = false;
            this.btnAccredit.tbShowProgress = false;
            this.btnAccredit.tbShowTip = true;
            this.btnAccredit.tbShowToolTipOnButton = false;
            this.btnAccredit.tbSplit = "3,3,3,3";
            this.btnAccredit.tbText = "已授权，继续";
            this.btnAccredit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnAccredit.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnAccredit.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnAccredit.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnAccredit.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnAccredit.tbTextMouseDownPlace = 0;
            this.btnAccredit.tbToolTip = "";
            this.btnAccredit.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnAccredit.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnAccredit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnAccredit.tbUpToDownWhenCenter = false;
            this.btnAccredit.TimerCountdownNum = 3;
            this.btnAccredit.TimerInterval = 1;
            this.btnAccredit.VisibleEx = true;
            this.btnAccredit.Click += new System.EventHandler(this.btnAccredit_Click);
            // 
            // pnlWelcome
            // 
            this.pnlWelcome.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.pnlWelcome.Controls.Add(this.pbRecommend);
            this.pnlWelcome.Controls.Add(this.tbbRecommend);
            this.pnlWelcome.Controls.Add(this.tbLabel3);
            this.pnlWelcome.Controls.Add(this.llblBackupCourse);
            this.pnlWelcome.Controls.Add(this.richTextBox3);
            this.pnlWelcome.Controls.Add(this.richTextBox2);
            this.pnlWelcome.Controls.Add(this.btnStart);
            this.pnlWelcome.Controls.Add(this.lblWelcone);
            this.pnlWelcome.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlWelcome.Location = new System.Drawing.Point(0, 0);
            this.pnlWelcome.Margin = new System.Windows.Forms.Padding(0);
            this.pnlWelcome.Name = "pnlWelcome";
            this.pnlWelcome.Size = new System.Drawing.Size(501, 638);
            this.pnlWelcome.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlWelcome.TabIndex = 9;
            this.pnlWelcome.tbBackgroundImage = null;
            this.pnlWelcome.tbShowWatermark = false;
            this.pnlWelcome.tbSplit = "0,0,0,0";
            this.pnlWelcome.tbWatermark = null;
            this.pnlWelcome.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlWelcome.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // pbRecommend
            // 
            this.pbRecommend.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pbRecommend.Image = global::iWechatAssistant.Properties.Resources.icon_yuyinhecheng;
            this.pbRecommend.Location = new System.Drawing.Point(28, 565);
            this.pbRecommend.Name = "pbRecommend";
            this.pbRecommend.Size = new System.Drawing.Size(15, 16);
            this.pbRecommend.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pbRecommend.TabIndex = 11;
            this.pbRecommend.TabStop = false;
            // 
            // tbbRecommend
            // 
            this.tbbRecommend.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.tbbRecommend.Cursor = System.Windows.Forms.Cursors.Default;
            this.tbbRecommend.Font = new System.Drawing.Font("宋体", 10F);
            this.tbbRecommend.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(81)))), ((int)(((byte)(81)))), ((int)(((byte)(81)))));
            this.tbbRecommend.Location = new System.Drawing.Point(49, 558);
            this.tbbRecommend.Name = "tbbRecommend";
            this.tbbRecommend.Size = new System.Drawing.Size(424, 25);
            this.tbbRecommend.TabIndex = 10;
            this.tbbRecommend.Text = "微信导出语音合并，请使用语音合并助手无损合并，免费试用中！";
            // 
            // tbLabel3
            // 
            this.tbLabel3.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.tbLabel3.AutoSize = true;
            this.tbLabel3.Font = new System.Drawing.Font("宋体", 10F);
            this.tbLabel3.ForeColor = System.Drawing.Color.Red;
            this.tbLabel3.Location = new System.Drawing.Point(7, 472);
            this.tbLabel3.Name = "tbLabel3";
            this.tbLabel3.Size = new System.Drawing.Size(623, 14);
            this.tbLabel3.TabIndex = 8;
            this.tbLabel3.tbAdriftWhenHover = false;
            this.tbLabel3.tbAutoEllipsis = false;
            this.tbLabel3.tbAutoSize = true;
            this.tbLabel3.tbHideImage = false;
            this.tbLabel3.tbIconImage = null;
            this.tbLabel3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.tbLabel3.tbIconPlaceText = 5;
            this.tbLabel3.tbShadow = false;
            this.tbLabel3.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.tbLabel3.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.tbLabel3.tbShowScrolling = false;
            this.tbLabel3.Text = "重要提醒：语音、视频、图片、聊天收发的文件需要在手机上下载查看后才能在助手上查看和导出。";
            // 
            // llblBackupCourse
            // 
            this.llblBackupCourse.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.llblBackupCourse.AutoSize = true;
            this.llblBackupCourse.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline;
            this.llblBackupCourse.Location = new System.Drawing.Point(365, 288);
            this.llblBackupCourse.Name = "llblBackupCourse";
            this.llblBackupCourse.Size = new System.Drawing.Size(125, 12);
            this.llblBackupCourse.TabIndex = 6;
            this.llblBackupCourse.TabStop = true;
            this.llblBackupCourse.Text = "如何备份，点此查看？";
            this.llblBackupCourse.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.llblBackupCourse_LinkClicked);
            // 
            // richTextBox3
            // 
            this.richTextBox3.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.richTextBox3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.richTextBox3.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.richTextBox3.Location = new System.Drawing.Point(10, 351);
            this.richTextBox3.Name = "richTextBox3";
            this.richTextBox3.ReadOnly = true;
            this.richTextBox3.Size = new System.Drawing.Size(480, 103);
            this.richTextBox3.TabIndex = 7;
            this.richTextBox3.Text = "授权后可以管理：\n1. 查看备份的微信记录。\n2. 导出备份的文字，语音，图片，视频,聊天记录中的文件，合并转发记录到电脑上。\n3. 导出记录成txt格式。\n4." +
    " 导出记录成excel格式。\n5. 导出记录成html网页格式。";
            // 
            // richTextBox2
            // 
            this.richTextBox2.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.richTextBox2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.richTextBox2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.richTextBox2.Font = new System.Drawing.Font("宋体", 11F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.richTextBox2.Location = new System.Drawing.Point(10, 169);
            this.richTextBox2.Name = "richTextBox2";
            this.richTextBox2.ReadOnly = true;
            this.richTextBox2.Size = new System.Drawing.Size(480, 116);
            this.richTextBox2.TabIndex = 7;
            this.richTextBox2.Text = "微信备份助手是全球第一款可以同时管理iPhone，Android手机微信备份的工具。\n\n您只需要通过电脑版微信先备份手机上的微信数据，就可以通过微信备份助手进行管" +
    "理。";
            // 
            // btnStart
            // 
            this.btnStart.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnStart.BackColor = System.Drawing.Color.Transparent;
            this.btnStart.BindingForm = null;
            this.btnStart.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnStart.Font = new System.Drawing.Font("宋体", 12F);
            this.btnStart.Location = new System.Drawing.Point(182, 502);
            this.btnStart.Name = "btnStart";
            this.btnStart.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnStart.Selectable = true;
            this.btnStart.Size = new System.Drawing.Size(141, 37);
            this.btnStart.TabIndex = 1;
            this.btnStart.tbAdriftIconWhenHover = false;
            this.btnStart.tbAutoSize = false;
            this.btnStart.tbAutoSizeEx = false;
            this.btnStart.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnStart.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnStart.tbBadgeNumber = 0;
            this.btnStart.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnStart.tbDefaultText = "";
            this.btnStart.tbEnableEnter = true;
            this.btnStart.tbEndEllipsis = false;
            this.btnStart.tbIconHoldPlace = true;
            this.btnStart.tbIconImage = null;
            this.btnStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnStart.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnStart.tbIconMore = false;
            this.btnStart.tbIconMouseDown = null;
            this.btnStart.tbIconMouseHover = null;
            this.btnStart.tbIconMouseLeave = null;
            this.btnStart.tbIconPlaceText = 2;
            this.btnStart.tbIconReadOnly = null;
            this.btnStart.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnStart.tbImageMouseDown = null;
            this.btnStart.tbImageMouseHover = null;
            this.btnStart.tbImageMouseLeave = null;
            this.btnStart.tbProgressValue = 50;
            this.btnStart.tbReadOnly = false;
            this.btnStart.tbReadOnlyText = false;
            this.btnStart.tbShadow = false;
            this.btnStart.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnStart.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnStart.tbShowDot = false;
            this.btnStart.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnStart.tbShowMoreIconImg")));
            this.btnStart.tbShowNew = false;
            this.btnStart.tbShowProgress = false;
            this.btnStart.tbShowTip = true;
            this.btnStart.tbShowToolTipOnButton = false;
            this.btnStart.tbSplit = "3,3,3,3";
            this.btnStart.tbText = "开始使用";
            this.btnStart.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnStart.tbTextColor = System.Drawing.Color.White;
            this.btnStart.tbTextColorDisable = System.Drawing.Color.White;
            this.btnStart.tbTextColorDown = System.Drawing.Color.White;
            this.btnStart.tbTextColorHover = System.Drawing.Color.White;
            this.btnStart.tbTextMouseDownPlace = 0;
            this.btnStart.tbToolTip = "";
            this.btnStart.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnStart.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnStart.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnStart.tbUpToDownWhenCenter = false;
            this.btnStart.TimerCountdownNum = 3;
            this.btnStart.TimerInterval = 1;
            this.btnStart.VisibleEx = true;
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            // 
            // lblWelcone
            // 
            this.lblWelcone.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblWelcone.AutoSize = true;
            this.lblWelcone.Font = new System.Drawing.Font("宋体", 26.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblWelcone.Location = new System.Drawing.Point(67, 100);
            this.lblWelcone.Name = "lblWelcone";
            this.lblWelcone.Size = new System.Drawing.Size(365, 35);
            this.lblWelcone.TabIndex = 0;
            this.lblWelcone.tbAdriftWhenHover = false;
            this.lblWelcone.tbAutoEllipsis = false;
            this.lblWelcone.tbAutoSize = true;
            this.lblWelcone.tbHideImage = false;
            this.lblWelcone.tbIconImage = null;
            this.lblWelcone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblWelcone.tbIconPlaceText = 5;
            this.lblWelcone.tbShadow = false;
            this.lblWelcone.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblWelcone.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.lblWelcone.tbShowScrolling = false;
            this.lblWelcone.Text = "欢迎使用微信备份助手";
            // 
            // pnlLoad
            // 
            this.pnlLoad.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.pnlLoad.Controls.Add(this.pbMsg);
            this.pnlLoad.Controls.Add(this.lblMsg);
            this.pnlLoad.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlLoad.Location = new System.Drawing.Point(1878, 0);
            this.pnlLoad.Margin = new System.Windows.Forms.Padding(0);
            this.pnlLoad.Name = "pnlLoad";
            this.pnlLoad.Size = new System.Drawing.Size(125, 638);
            this.pnlLoad.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlLoad.TabIndex = 12;
            this.pnlLoad.tbBackgroundImage = null;
            this.pnlLoad.tbShowWatermark = false;
            this.pnlLoad.tbSplit = "0,0,0,0";
            this.pnlLoad.tbWatermark = null;
            this.pnlLoad.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlLoad.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // pbMsg
            // 
            this.pbMsg.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pbMsg.Image = global::iWechatAssistant.Properties.Resources.gif_loading_24;
            this.pbMsg.Location = new System.Drawing.Point(-17, 307);
            this.pbMsg.Name = "pbMsg";
            this.pbMsg.Size = new System.Drawing.Size(24, 24);
            this.pbMsg.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pbMsg.TabIndex = 3;
            this.pbMsg.TabStop = false;
            // 
            // lblMsg
            // 
            this.lblMsg.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblMsg.AutoSize = true;
            this.lblMsg.Font = new System.Drawing.Font("宋体", 12F);
            this.lblMsg.ForeColor = System.Drawing.Color.Black;
            this.lblMsg.Location = new System.Drawing.Point(13, 311);
            this.lblMsg.Name = "lblMsg";
            this.lblMsg.Size = new System.Drawing.Size(96, 16);
            this.lblMsg.TabIndex = 0;
            this.lblMsg.Text = "正在加载...";
            this.lblMsg.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnlWechatList
            // 
            this.pnlWechatList.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.pnlWechatList.Controls.Add(this.tbPanel17);
            this.pnlWechatList.Controls.Add(this.pnlWechatAccounts);
            this.pnlWechatList.Controls.Add(this.tbPanel15);
            this.pnlWechatList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlWechatList.Location = new System.Drawing.Point(501, 0);
            this.pnlWechatList.Margin = new System.Windows.Forms.Padding(0);
            this.pnlWechatList.Name = "pnlWechatList";
            this.pnlWechatList.Size = new System.Drawing.Size(501, 638);
            this.pnlWechatList.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlWechatList.TabIndex = 8;
            this.pnlWechatList.tbBackgroundImage = null;
            this.pnlWechatList.tbShowWatermark = false;
            this.pnlWechatList.tbSplit = "0,0,0,0";
            this.pnlWechatList.tbWatermark = null;
            this.pnlWechatList.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlWechatList.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel17
            // 
            this.tbPanel17.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.tbPanel17.Controls.Add(this.tbButton1);
            this.tbPanel17.Controls.Add(this.btnWechatPath);
            this.tbPanel17.Controls.Add(this.btnRefreshAccount);
            this.tbPanel17.Controls.Add(this.llblPCPath);
            this.tbPanel17.Controls.Add(this.label2);
            this.tbPanel17.Controls.Add(this.txtWechatPath);
            this.tbPanel17.Controls.Add(this.lblWechatPath);
            this.tbPanel17.Cursor = System.Windows.Forms.Cursors.Default;
            this.tbPanel17.Dock = System.Windows.Forms.DockStyle.Top;
            this.tbPanel17.Location = new System.Drawing.Point(0, 41);
            this.tbPanel17.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel17.Name = "tbPanel17";
            this.tbPanel17.Size = new System.Drawing.Size(501, 61);
            this.tbPanel17.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel17.TabIndex = 87;
            this.tbPanel17.tbBackgroundImage = null;
            this.tbPanel17.tbShowWatermark = false;
            this.tbPanel17.tbSplit = "3,3,3,3";
            this.tbPanel17.tbWatermark = null;
            this.tbPanel17.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel17.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbButton1
            // 
            this.tbButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.tbButton1.BackColor = System.Drawing.Color.Transparent;
            this.tbButton1.BindingForm = null;
            this.tbButton1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.tbButton1.Location = new System.Drawing.Point(408, 29);
            this.tbButton1.Margin = new System.Windows.Forms.Padding(0);
            this.tbButton1.Name = "tbButton1";
            this.tbButton1.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.tbButton1.Selectable = true;
            this.tbButton1.Size = new System.Drawing.Size(57, 23);
            this.tbButton1.TabIndex = 87;
            this.tbButton1.tbAdriftIconWhenHover = false;
            this.tbButton1.tbAutoSize = false;
            this.tbButton1.tbAutoSizeEx = false;
            this.tbButton1.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.tbButton1.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.tbButton1.tbBadgeNumber = 0;
            this.tbButton1.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.tbButton1.tbDefaultText = "";
            this.tbButton1.tbEnableEnter = true;
            this.tbButton1.tbEndEllipsis = false;
            this.tbButton1.tbIconHoldPlace = true;
            this.tbButton1.tbIconImage = null;
            this.tbButton1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.tbButton1.tbIconImageState = iTong.Components.ImageState.OneState;
            this.tbButton1.tbIconMore = false;
            this.tbButton1.tbIconMouseDown = null;
            this.tbButton1.tbIconMouseHover = null;
            this.tbButton1.tbIconMouseLeave = null;
            this.tbButton1.tbIconPlaceText = 2;
            this.tbButton1.tbIconReadOnly = null;
            this.tbButton1.tbIconSize = new System.Drawing.Size(0, 0);
            this.tbButton1.tbImageMouseDown = null;
            this.tbButton1.tbImageMouseHover = null;
            this.tbButton1.tbImageMouseLeave = null;
            this.tbButton1.tbProgressValue = 50;
            this.tbButton1.tbReadOnly = false;
            this.tbButton1.tbReadOnlyText = false;
            this.tbButton1.tbShadow = false;
            this.tbButton1.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.tbButton1.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.tbButton1.tbShowDot = false;
            this.tbButton1.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("tbButton1.tbShowMoreIconImg")));
            this.tbButton1.tbShowNew = false;
            this.tbButton1.tbShowProgress = false;
            this.tbButton1.tbShowTip = true;
            this.tbButton1.tbShowToolTipOnButton = false;
            this.tbButton1.tbSplit = "3,3,3,3";
            this.tbButton1.tbText = "选择";
            this.tbButton1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.tbButton1.tbTextColor = System.Drawing.Color.White;
            this.tbButton1.tbTextColorDisable = System.Drawing.Color.White;
            this.tbButton1.tbTextColorDown = System.Drawing.Color.White;
            this.tbButton1.tbTextColorHover = System.Drawing.Color.White;
            this.tbButton1.tbTextMouseDownPlace = 0;
            this.tbButton1.tbToolTip = "";
            this.tbButton1.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.tbButton1.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.tbButton1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.tbButton1.tbUpToDownWhenCenter = false;
            this.tbButton1.TimerCountdownNum = 3;
            this.tbButton1.TimerInterval = 1;
            this.tbButton1.Visible = false;
            this.tbButton1.VisibleEx = true;
            this.tbButton1.Click += new System.EventHandler(this.tbButton1_Click);
            // 
            // btnWechatPath
            // 
            this.btnWechatPath.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnWechatPath.BackColor = System.Drawing.Color.Transparent;
            this.btnWechatPath.BindingForm = null;
            this.btnWechatPath.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnWechatPath.Location = new System.Drawing.Point(358, 6);
            this.btnWechatPath.Margin = new System.Windows.Forms.Padding(0);
            this.btnWechatPath.Name = "btnWechatPath";
            this.btnWechatPath.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnWechatPath.Selectable = true;
            this.btnWechatPath.Size = new System.Drawing.Size(57, 23);
            this.btnWechatPath.TabIndex = 0;
            this.btnWechatPath.tbAdriftIconWhenHover = false;
            this.btnWechatPath.tbAutoSize = false;
            this.btnWechatPath.tbAutoSizeEx = false;
            this.btnWechatPath.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnWechatPath.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnWechatPath.tbBadgeNumber = 0;
            this.btnWechatPath.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnWechatPath.tbDefaultText = "";
            this.btnWechatPath.tbEnableEnter = true;
            this.btnWechatPath.tbEndEllipsis = false;
            this.btnWechatPath.tbIconHoldPlace = true;
            this.btnWechatPath.tbIconImage = null;
            this.btnWechatPath.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnWechatPath.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnWechatPath.tbIconMore = false;
            this.btnWechatPath.tbIconMouseDown = null;
            this.btnWechatPath.tbIconMouseHover = null;
            this.btnWechatPath.tbIconMouseLeave = null;
            this.btnWechatPath.tbIconPlaceText = 2;
            this.btnWechatPath.tbIconReadOnly = null;
            this.btnWechatPath.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnWechatPath.tbImageMouseDown = null;
            this.btnWechatPath.tbImageMouseHover = null;
            this.btnWechatPath.tbImageMouseLeave = null;
            this.btnWechatPath.tbProgressValue = 50;
            this.btnWechatPath.tbReadOnly = false;
            this.btnWechatPath.tbReadOnlyText = false;
            this.btnWechatPath.tbShadow = false;
            this.btnWechatPath.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnWechatPath.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnWechatPath.tbShowDot = false;
            this.btnWechatPath.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnWechatPath.tbShowMoreIconImg")));
            this.btnWechatPath.tbShowNew = false;
            this.btnWechatPath.tbShowProgress = false;
            this.btnWechatPath.tbShowTip = true;
            this.btnWechatPath.tbShowToolTipOnButton = false;
            this.btnWechatPath.tbSplit = "3,3,3,3";
            this.btnWechatPath.tbText = "选择";
            this.btnWechatPath.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnWechatPath.tbTextColor = System.Drawing.Color.White;
            this.btnWechatPath.tbTextColorDisable = System.Drawing.Color.White;
            this.btnWechatPath.tbTextColorDown = System.Drawing.Color.White;
            this.btnWechatPath.tbTextColorHover = System.Drawing.Color.White;
            this.btnWechatPath.tbTextMouseDownPlace = 0;
            this.btnWechatPath.tbToolTip = "";
            this.btnWechatPath.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnWechatPath.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnWechatPath.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnWechatPath.tbUpToDownWhenCenter = false;
            this.btnWechatPath.TimerCountdownNum = 3;
            this.btnWechatPath.TimerInterval = 1;
            this.btnWechatPath.VisibleEx = true;
            this.btnWechatPath.Click += new System.EventHandler(this.btnWechatPath_Click);
            // 
            // btnRefreshAccount
            // 
            this.btnRefreshAccount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefreshAccount.BackColor = System.Drawing.Color.Transparent;
            this.btnRefreshAccount.BindingForm = null;
            this.btnRefreshAccount.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRefreshAccount.Location = new System.Drawing.Point(423, 6);
            this.btnRefreshAccount.Margin = new System.Windows.Forms.Padding(0);
            this.btnRefreshAccount.Name = "btnRefreshAccount";
            this.btnRefreshAccount.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnRefreshAccount.Selectable = true;
            this.btnRefreshAccount.Size = new System.Drawing.Size(58, 23);
            this.btnRefreshAccount.TabIndex = 82;
            this.btnRefreshAccount.tbAdriftIconWhenHover = false;
            this.btnRefreshAccount.tbAutoSize = false;
            this.btnRefreshAccount.tbAutoSizeEx = false;
            this.btnRefreshAccount.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnRefreshAccount.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnRefreshAccount.tbBadgeNumber = 0;
            this.btnRefreshAccount.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnRefreshAccount.tbDefaultText = "";
            this.btnRefreshAccount.tbEnableEnter = true;
            this.btnRefreshAccount.tbEndEllipsis = false;
            this.btnRefreshAccount.tbIconHoldPlace = true;
            this.btnRefreshAccount.tbIconImage = null;
            this.btnRefreshAccount.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnRefreshAccount.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnRefreshAccount.tbIconMore = false;
            this.btnRefreshAccount.tbIconMouseDown = null;
            this.btnRefreshAccount.tbIconMouseHover = null;
            this.btnRefreshAccount.tbIconMouseLeave = null;
            this.btnRefreshAccount.tbIconPlaceText = 2;
            this.btnRefreshAccount.tbIconReadOnly = null;
            this.btnRefreshAccount.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnRefreshAccount.tbImageMouseDown = null;
            this.btnRefreshAccount.tbImageMouseHover = null;
            this.btnRefreshAccount.tbImageMouseLeave = null;
            this.btnRefreshAccount.tbProgressValue = 50;
            this.btnRefreshAccount.tbReadOnly = false;
            this.btnRefreshAccount.tbReadOnlyText = false;
            this.btnRefreshAccount.tbShadow = false;
            this.btnRefreshAccount.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnRefreshAccount.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnRefreshAccount.tbShowDot = false;
            this.btnRefreshAccount.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnRefreshAccount.tbShowMoreIconImg")));
            this.btnRefreshAccount.tbShowNew = false;
            this.btnRefreshAccount.tbShowProgress = false;
            this.btnRefreshAccount.tbShowTip = true;
            this.btnRefreshAccount.tbShowToolTipOnButton = false;
            this.btnRefreshAccount.tbSplit = "3,3,3,3";
            this.btnRefreshAccount.tbText = "刷新";
            this.btnRefreshAccount.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnRefreshAccount.tbTextColor = System.Drawing.Color.White;
            this.btnRefreshAccount.tbTextColorDisable = System.Drawing.Color.White;
            this.btnRefreshAccount.tbTextColorDown = System.Drawing.Color.White;
            this.btnRefreshAccount.tbTextColorHover = System.Drawing.Color.White;
            this.btnRefreshAccount.tbTextMouseDownPlace = 0;
            this.btnRefreshAccount.tbToolTip = "";
            this.btnRefreshAccount.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnRefreshAccount.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnRefreshAccount.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnRefreshAccount.tbUpToDownWhenCenter = false;
            this.btnRefreshAccount.TimerCountdownNum = 3;
            this.btnRefreshAccount.TimerInterval = 1;
            this.btnRefreshAccount.VisibleEx = true;
            this.btnRefreshAccount.Click += new System.EventHandler(this.btnRefreshAccount_Click);
            // 
            // llblPCPath
            // 
            this.llblPCPath.ActiveLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(1)))), ((int)(((byte)(170)))), ((int)(((byte)(255)))));
            this.llblPCPath.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.llblPCPath.AutoSize = true;
            this.llblPCPath.BackColor = System.Drawing.Color.Transparent;
            this.llblPCPath.DisabledLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(1)))), ((int)(((byte)(170)))), ((int)(((byte)(255)))));
            this.llblPCPath.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline;
            this.llblPCPath.LinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(1)))), ((int)(((byte)(170)))), ((int)(((byte)(255)))));
            this.llblPCPath.Location = new System.Drawing.Point(342, 40);
            this.llblPCPath.Margin = new System.Windows.Forms.Padding(0);
            this.llblPCPath.Name = "llblPCPath";
            this.llblPCPath.Size = new System.Drawing.Size(53, 12);
            this.llblPCPath.TabIndex = 86;
            this.llblPCPath.TabStop = true;
            this.llblPCPath.Text = "点此获取";
            this.llblPCPath.VisitedLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(1)))), ((int)(((byte)(170)))), ((int)(((byte)(255)))));
            this.llblPCPath.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.llblPCPath_LinkClicked);
            // 
            // label2
            // 
            this.label2.Anchor = System.Windows.Forms.AnchorStyles.Bottom;
            this.label2.AutoSize = true;
            this.label2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.label2.Location = new System.Drawing.Point(70, 40);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(269, 12);
            this.label2.TabIndex = 85;
            this.label2.Text = "如果您不知道「微信文件默认保存位置」在哪，请";
            // 
            // txtWechatPath
            // 
            this.txtWechatPath.Cursor = System.Windows.Forms.Cursors.IBeam;
            this.txtWechatPath.Location = new System.Drawing.Point(190, 7);
            this.txtWechatPath.Margin = new System.Windows.Forms.Padding(0);
            this.txtWechatPath.Name = "txtWechatPath";
            this.txtWechatPath.Size = new System.Drawing.Size(160, 21);
            this.txtWechatPath.TabIndex = 1;
            // 
            // lblWechatPath
            // 
            this.lblWechatPath.AutoSize = true;
            this.lblWechatPath.Location = new System.Drawing.Point(19, 11);
            this.lblWechatPath.Name = "lblWechatPath";
            this.lblWechatPath.Size = new System.Drawing.Size(161, 12);
            this.lblWechatPath.TabIndex = 2;
            this.lblWechatPath.Text = "请选择微信文件默认保存位置";
            // 
            // pnlWechatAccounts
            // 
            this.pnlWechatAccounts.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnlWechatAccounts.BackColor = System.Drawing.Color.Silver;
            this.pnlWechatAccounts.Location = new System.Drawing.Point(20, 113);
            this.pnlWechatAccounts.Margin = new System.Windows.Forms.Padding(1);
            this.pnlWechatAccounts.Name = "pnlWechatAccounts";
            this.pnlWechatAccounts.Padding = new System.Windows.Forms.Padding(1);
            this.pnlWechatAccounts.Size = new System.Drawing.Size(460, 504);
            this.pnlWechatAccounts.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlWechatAccounts.TabIndex = 80;
            this.pnlWechatAccounts.tbBackgroundImage = null;
            this.pnlWechatAccounts.tbShowWatermark = false;
            this.pnlWechatAccounts.tbSplit = "0,0,0,0";
            this.pnlWechatAccounts.tbWatermark = null;
            this.pnlWechatAccounts.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlWechatAccounts.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel15
            // 
            this.tbPanel15.Controls.Add(this.btnGetBack);
            this.tbPanel15.Dock = System.Windows.Forms.DockStyle.Top;
            this.tbPanel15.Location = new System.Drawing.Point(0, 0);
            this.tbPanel15.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel15.Name = "tbPanel15";
            this.tbPanel15.Size = new System.Drawing.Size(501, 41);
            this.tbPanel15.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel15.TabIndex = 88;
            this.tbPanel15.tbBackgroundImage = null;
            this.tbPanel15.tbShowWatermark = false;
            this.tbPanel15.tbSplit = "0,0,0,0";
            this.tbPanel15.tbWatermark = null;
            this.tbPanel15.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel15.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // btnGetBack
            // 
            this.btnGetBack.BackColor = System.Drawing.Color.Transparent;
            this.btnGetBack.BindingForm = null;
            this.btnGetBack.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnGetBack.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnGetBack.Location = new System.Drawing.Point(10, 13);
            this.btnGetBack.Name = "btnGetBack";
            this.btnGetBack.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnGetBack.Selectable = true;
            this.btnGetBack.Size = new System.Drawing.Size(16, 14);
            this.btnGetBack.TabIndex = 85;
            this.btnGetBack.tbAdriftIconWhenHover = false;
            this.btnGetBack.tbAutoSize = false;
            this.btnGetBack.tbAutoSizeEx = true;
            this.btnGetBack.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_retreatex;
            this.btnGetBack.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnGetBack.tbBadgeNumber = 0;
            this.btnGetBack.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnGetBack.tbDefaultText = "";
            this.btnGetBack.tbEnableEnter = true;
            this.btnGetBack.tbEndEllipsis = false;
            this.btnGetBack.tbIconHoldPlace = true;
            this.btnGetBack.tbIconImage = null;
            this.btnGetBack.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnGetBack.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnGetBack.tbIconMore = false;
            this.btnGetBack.tbIconMouseDown = null;
            this.btnGetBack.tbIconMouseHover = null;
            this.btnGetBack.tbIconMouseLeave = null;
            this.btnGetBack.tbIconPlaceText = 2;
            this.btnGetBack.tbIconReadOnly = null;
            this.btnGetBack.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnGetBack.tbImageMouseDown = null;
            this.btnGetBack.tbImageMouseHover = null;
            this.btnGetBack.tbImageMouseLeave = null;
            this.btnGetBack.tbProgressValue = 50;
            this.btnGetBack.tbReadOnly = false;
            this.btnGetBack.tbReadOnlyText = false;
            this.btnGetBack.tbShadow = false;
            this.btnGetBack.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnGetBack.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnGetBack.tbShowDot = false;
            this.btnGetBack.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnGetBack.tbShowMoreIconImg")));
            this.btnGetBack.tbShowNew = false;
            this.btnGetBack.tbShowProgress = false;
            this.btnGetBack.tbShowTip = true;
            this.btnGetBack.tbShowToolTipOnButton = false;
            this.btnGetBack.tbSplit = "12,1,1,1";
            this.btnGetBack.tbText = "";
            this.btnGetBack.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnGetBack.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnGetBack.tbTextColorDisable = System.Drawing.Color.LightGray;
            this.btnGetBack.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnGetBack.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnGetBack.tbTextMouseDownPlace = 2;
            this.btnGetBack.tbToolTip = "";
            this.btnGetBack.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnGetBack.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btnGetBack.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnGetBack.tbUpToDownWhenCenter = false;
            this.btnGetBack.TimerCountdownNum = 3;
            this.btnGetBack.TimerInterval = 1;
            this.btnGetBack.VisibleEx = true;
            this.btnGetBack.Click += new System.EventHandler(this.btnGetBack_Click);
            // 
            // tbPanel12
            // 
            this.tbPanel12.Controls.Add(this.plnShowChat);
            this.tbPanel12.Controls.Add(this.pnlTop);
            this.tbPanel12.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tbPanel12.Location = new System.Drawing.Point(1002, 0);
            this.tbPanel12.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel12.Name = "tbPanel12";
            this.tbPanel12.Size = new System.Drawing.Size(876, 638);
            this.tbPanel12.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel12.TabIndex = 15;
            this.tbPanel12.tbBackgroundImage = null;
            this.tbPanel12.tbShowWatermark = false;
            this.tbPanel12.tbSplit = "0,0,0,0";
            this.tbPanel12.tbWatermark = null;
            this.tbPanel12.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel12.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // plnShowChat
            // 
            this.plnShowChat.BackColor = System.Drawing.Color.White;
            this.plnShowChat.Controls.Add(this.tbPanel5);
            this.plnShowChat.Controls.Add(this.tbPanel6);
            this.plnShowChat.Dock = System.Windows.Forms.DockStyle.Fill;
            this.plnShowChat.Location = new System.Drawing.Point(0, 41);
            this.plnShowChat.Margin = new System.Windows.Forms.Padding(0);
            this.plnShowChat.Name = "plnShowChat";
            this.plnShowChat.Size = new System.Drawing.Size(876, 597);
            this.plnShowChat.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.plnShowChat.TabIndex = 10;
            this.plnShowChat.tbBackgroundImage = null;
            this.plnShowChat.tbShowWatermark = false;
            this.plnShowChat.tbSplit = "0,0,0,0";
            this.plnShowChat.tbWatermark = null;
            this.plnShowChat.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.plnShowChat.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel5
            // 
            this.tbPanel5.BackColor = System.Drawing.Color.White;
            this.tbPanel5.Controls.Add(this.tbPanel3);
            this.tbPanel5.Controls.Add(this.pnlFriend);
            this.tbPanel5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tbPanel5.Location = new System.Drawing.Point(0, 0);
            this.tbPanel5.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel5.Name = "tbPanel5";
            this.tbPanel5.Size = new System.Drawing.Size(876, 517);
            this.tbPanel5.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel5.TabIndex = 14;
            this.tbPanel5.tbBackgroundImage = null;
            this.tbPanel5.tbShowWatermark = false;
            this.tbPanel5.tbSplit = "0,0,0,0";
            this.tbPanel5.tbWatermark = null;
            this.tbPanel5.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel5.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel3
            // 
            this.tbPanel3.BackColor = System.Drawing.Color.White;
            this.tbPanel3.Controls.Add(this.pnlPage);
            this.tbPanel3.Controls.Add(this.tbPanel4);
            this.tbPanel3.Controls.Add(this.lblSystemInfo);
            this.tbPanel3.Controls.Add(this.pnlTitle);
            this.tbPanel3.Controls.Add(this.pnlLoadChat);
            this.tbPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tbPanel3.Location = new System.Drawing.Point(280, 0);
            this.tbPanel3.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel3.Name = "tbPanel3";
            this.tbPanel3.Size = new System.Drawing.Size(596, 517);
            this.tbPanel3.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel3.TabIndex = 13;
            this.tbPanel3.tbBackgroundImage = null;
            this.tbPanel3.tbShowWatermark = false;
            this.tbPanel3.tbSplit = "0,0,0,0";
            this.tbPanel3.tbWatermark = null;
            this.tbPanel3.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel3.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // pnlPage
            // 
            this.pnlPage.BackColor = System.Drawing.Color.White;
            this.pnlPage.Controls.Add(this.tbpPage);
            this.pnlPage.Controls.Add(this.tbPanel18);
            this.pnlPage.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlPage.Location = new System.Drawing.Point(1, 485);
            this.pnlPage.Name = "pnlPage";
            this.pnlPage.Size = new System.Drawing.Size(595, 32);
            this.pnlPage.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlPage.TabIndex = 19;
            this.pnlPage.tbBackgroundImage = null;
            this.pnlPage.tbShowWatermark = false;
            this.pnlPage.tbSplit = "0,0,0,0";
            this.pnlPage.tbWatermark = null;
            this.pnlPage.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlPage.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbpPage
            // 
            this.tbpPage.CurrentPage = 0D;
            this.tbpPage.Cursor = System.Windows.Forms.Cursors.Default;
            this.tbpPage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tbpPage.Location = new System.Drawing.Point(0, 1);
            this.tbpPage.Name = "tbpPage";
            this.tbpPage.Size = new System.Drawing.Size(595, 31);
            this.tbpPage.TabIndex = 71;
            this.tbpPage.TotalPage = 0D;
            // 
            // tbPanel18
            // 
            this.tbPanel18.BackColor = System.Drawing.Color.Gainsboro;
            this.tbPanel18.Dock = System.Windows.Forms.DockStyle.Top;
            this.tbPanel18.Location = new System.Drawing.Point(0, 0);
            this.tbPanel18.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel18.Name = "tbPanel18";
            this.tbPanel18.Size = new System.Drawing.Size(595, 1);
            this.tbPanel18.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel18.TabIndex = 70;
            this.tbPanel18.tbBackgroundImage = null;
            this.tbPanel18.tbShowWatermark = false;
            this.tbPanel18.tbSplit = "0,0,0,0";
            this.tbPanel18.tbWatermark = null;
            this.tbPanel18.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel18.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel4
            // 
            this.tbPanel4.Dock = System.Windows.Forms.DockStyle.Left;
            this.tbPanel4.Location = new System.Drawing.Point(0, 41);
            this.tbPanel4.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel4.Name = "tbPanel4";
            this.tbPanel4.Size = new System.Drawing.Size(1, 476);
            this.tbPanel4.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel4.TabIndex = 0;
            this.tbPanel4.tbBackgroundImage = null;
            this.tbPanel4.tbShowWatermark = false;
            this.tbPanel4.tbSplit = "0,0,0,0";
            this.tbPanel4.tbWatermark = null;
            this.tbPanel4.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel4.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // lblSystemInfo
            // 
            this.lblSystemInfo.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblSystemInfo.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.lblSystemInfo.Font = new System.Drawing.Font("宋体", 14.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblSystemInfo.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.lblSystemInfo.Location = new System.Drawing.Point(175, 243);
            this.lblSystemInfo.Name = "lblSystemInfo";
            this.lblSystemInfo.Size = new System.Drawing.Size(266, 30);
            this.lblSystemInfo.TabIndex = 12;
            this.lblSystemInfo.tbAdriftWhenHover = false;
            this.lblSystemInfo.tbAutoEllipsis = false;
            this.lblSystemInfo.tbAutoSize = false;
            this.lblSystemInfo.tbHideImage = false;
            this.lblSystemInfo.tbIconImage = null;
            this.lblSystemInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblSystemInfo.tbIconPlaceText = 5;
            this.lblSystemInfo.tbShadow = false;
            this.lblSystemInfo.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblSystemInfo.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.lblSystemInfo.tbShowScrolling = false;
            this.lblSystemInfo.Text = "查看消息，请选中左侧联系人";
            this.lblSystemInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lblSystemInfo.Visible = false;
            // 
            // pnlTitle
            // 
            this.pnlTitle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.pnlTitle.Controls.Add(this.chkLock);
            this.pnlTitle.Controls.Add(this.dtpEnd);
            this.pnlTitle.Controls.Add(this.tbPanel19);
            this.pnlTitle.Controls.Add(this.TbPanel8);
            this.pnlTitle.Controls.Add(this.btnTimeSelect);
            this.pnlTitle.Controls.Add(this.lblStartTime);
            this.pnlTitle.Controls.Add(this.lblEndTime);
            this.pnlTitle.Controls.Add(this.btnOK);
            this.pnlTitle.Controls.Add(this.dtpEndTime);
            this.pnlTitle.Controls.Add(this.dtpStartTime);
            this.pnlTitle.Controls.Add(this.dtpStart);
            this.pnlTitle.Cursor = System.Windows.Forms.Cursors.Default;
            this.pnlTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTitle.Location = new System.Drawing.Point(0, 0);
            this.pnlTitle.Margin = new System.Windows.Forms.Padding(0);
            this.pnlTitle.Name = "pnlTitle";
            this.pnlTitle.Size = new System.Drawing.Size(596, 41);
            this.pnlTitle.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlTitle.TabIndex = 13;
            this.pnlTitle.tbBackgroundImage = null;
            this.pnlTitle.tbShowWatermark = false;
            this.pnlTitle.tbSplit = "3,3,3,3";
            this.pnlTitle.tbWatermark = null;
            this.pnlTitle.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlTitle.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            this.pnlTitle.Visible = false;
            // 
            // chkLock
            // 
            this.chkLock.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.chkLock.Appearance = System.Windows.Forms.Appearance.Button;
            this.chkLock.AutoSize = true;
            this.chkLock.BackColor = System.Drawing.Color.Transparent;
            this.chkLock.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkLock.Font = new System.Drawing.Font("Arial", 10F);
            this.chkLock.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.chkLock.ForeColor_Checked = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.chkLock.Location = new System.Drawing.Point(554, 9);
            this.chkLock.Name = "chkLock";
            this.chkLock.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.chkLock.Size = new System.Drawing.Size(99, 23);
            this.chkLock.TabIndex = 89;
            this.chkLock.tbAdriftIconWhenHover = false;
            this.chkLock.tbAutoSize = true;
            this.chkLock.tbAutoSizeEx = false;
            this.chkLock.tbIconChecked = ((System.Drawing.Image)(resources.GetObject("chkLock.tbIconChecked")));
            this.chkLock.tbIconCheckedMouseDown = null;
            this.chkLock.tbIconCheckedMouseHover = null;
            this.chkLock.tbIconCheckedMouseLeave = null;
            this.chkLock.tbIconCheckedState = iTong.Components.ImageState.FourState;
            this.chkLock.tbIconHoldPlace = true;
            this.chkLock.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.chkLock.tbIconIndeterminate = ((System.Drawing.Image)(resources.GetObject("chkLock.tbIconIndeterminate")));
            this.chkLock.tbIconIndeterminateMouseDown = null;
            this.chkLock.tbIconIndeterminateMouseHover = null;
            this.chkLock.tbIconIndeterminateMouseLeave = null;
            this.chkLock.tbIconIndeterminateState = iTong.Components.ImageState.FourState;
            this.chkLock.tbIconPlaceText = 1;
            this.chkLock.tbIconUnChecked = ((System.Drawing.Image)(resources.GetObject("chkLock.tbIconUnChecked")));
            this.chkLock.tbIconUnCheckedMouseDown = null;
            this.chkLock.tbIconUnCheckedMouseHover = null;
            this.chkLock.tbIconUnCheckedMouseLeave = null;
            this.chkLock.tbIconUnCheckedState = iTong.Components.ImageState.FourState;
            this.chkLock.tbImageBackground = null;
            this.chkLock.tbImageBackgroundState = iTong.Components.ImageState.TwoState;
            this.chkLock.tbImageCheckedMouseDown = null;
            this.chkLock.tbImageCheckedMouseHover = null;
            this.chkLock.tbImageCheckedMouseLeave = null;
            this.chkLock.tbImageUnCheckedMouseDown = null;
            this.chkLock.tbImageUnCheckedMouseHover = null;
            this.chkLock.tbImageUnCheckedMouseLeave = null;
            this.chkLock.tbReadOnly = false;
            this.chkLock.tbShadow = false;
            this.chkLock.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.chkLock.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.chkLock.tbSplit = "3,3,3,3";
            this.chkLock.tbToolTip = "";
            this.chkLock.Text = "锁定日期";
            this.chkLock.UseVisualStyleBackColor = false;
            this.chkLock.CheckedChanged += new System.EventHandler(this.chkLock_CheckedChanged);
            // 
            // dtpEnd
            // 
            this.dtpEnd.CalendarForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(102)))), ((int)(((byte)(102)))), ((int)(((byte)(102)))));
            this.dtpEnd.CalendarTitleForeColor = System.Drawing.Color.AliceBlue;
            this.dtpEnd.Checked = false;
            this.dtpEnd.Cursor = System.Windows.Forms.Cursors.Hand;
            this.dtpEnd.CustomFormat = "yyyy-MM-dd";
            this.dtpEnd.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpEnd.Location = new System.Drawing.Point(340, 10);
            this.dtpEnd.Name = "dtpEnd";
            this.dtpEnd.Size = new System.Drawing.Size(100, 21);
            this.dtpEnd.TabIndex = 16;
            this.dtpEnd.CloseUp += new System.EventHandler(this.dtpEnd_CloseUp);
            // 
            // tbPanel19
            // 
            this.tbPanel19.BackColor = System.Drawing.Color.Gainsboro;
            this.tbPanel19.Dock = System.Windows.Forms.DockStyle.Left;
            this.tbPanel19.Location = new System.Drawing.Point(0, 0);
            this.tbPanel19.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel19.Name = "tbPanel19";
            this.tbPanel19.Size = new System.Drawing.Size(1, 40);
            this.tbPanel19.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel19.TabIndex = 87;
            this.tbPanel19.tbBackgroundImage = null;
            this.tbPanel19.tbShowWatermark = false;
            this.tbPanel19.tbSplit = "0,0,0,0";
            this.tbPanel19.tbWatermark = null;
            this.tbPanel19.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel19.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // TbPanel8
            // 
            this.TbPanel8.BackColor = System.Drawing.Color.Gainsboro;
            this.TbPanel8.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.TbPanel8.Location = new System.Drawing.Point(0, 40);
            this.TbPanel8.Margin = new System.Windows.Forms.Padding(0);
            this.TbPanel8.Name = "TbPanel8";
            this.TbPanel8.Size = new System.Drawing.Size(596, 1);
            this.TbPanel8.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.TbPanel8.TabIndex = 69;
            this.TbPanel8.tbBackgroundImage = null;
            this.TbPanel8.tbShowWatermark = false;
            this.TbPanel8.tbSplit = "0,0,0,0";
            this.TbPanel8.tbWatermark = null;
            this.TbPanel8.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.TbPanel8.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // btnTimeSelect
            // 
            this.btnTimeSelect.BackColor = System.Drawing.Color.White;
            this.btnTimeSelect.BindingForm = null;
            this.btnTimeSelect.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnTimeSelect.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnTimeSelect.Location = new System.Drawing.Point(59, 9);
            this.btnTimeSelect.Margin = new System.Windows.Forms.Padding(0);
            this.btnTimeSelect.Name = "btnTimeSelect";
            this.btnTimeSelect.Padding = new System.Windows.Forms.Padding(10, 2, 10, 2);
            this.btnTimeSelect.Selectable = true;
            this.btnTimeSelect.Size = new System.Drawing.Size(99, 23);
            this.btnTimeSelect.TabIndex = 68;
            this.btnTimeSelect.tbAdriftIconWhenHover = false;
            this.btnTimeSelect.tbAutoSize = false;
            this.btnTimeSelect.tbAutoSizeEx = false;
            this.btnTimeSelect.tbBackgroundImage = ((System.Drawing.Image)(resources.GetObject("btnTimeSelect.tbBackgroundImage")));
            this.btnTimeSelect.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnTimeSelect.tbBadgeNumber = 0;
            this.btnTimeSelect.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnTimeSelect.tbDefaultText = "";
            this.btnTimeSelect.tbEnableEnter = true;
            this.btnTimeSelect.tbEndEllipsis = false;
            this.btnTimeSelect.tbIconHoldPlace = true;
            this.btnTimeSelect.tbIconImage = null;
            this.btnTimeSelect.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTimeSelect.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnTimeSelect.tbIconMore = true;
            this.btnTimeSelect.tbIconMouseDown = null;
            this.btnTimeSelect.tbIconMouseHover = null;
            this.btnTimeSelect.tbIconMouseLeave = null;
            this.btnTimeSelect.tbIconPlaceText = 2;
            this.btnTimeSelect.tbIconReadOnly = null;
            this.btnTimeSelect.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnTimeSelect.tbImageMouseDown = null;
            this.btnTimeSelect.tbImageMouseHover = null;
            this.btnTimeSelect.tbImageMouseLeave = null;
            this.btnTimeSelect.tbProgressValue = 50;
            this.btnTimeSelect.tbReadOnly = false;
            this.btnTimeSelect.tbReadOnlyText = false;
            this.btnTimeSelect.tbShadow = false;
            this.btnTimeSelect.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnTimeSelect.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnTimeSelect.tbShowDot = false;
            this.btnTimeSelect.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnTimeSelect.tbShowMoreIconImg")));
            this.btnTimeSelect.tbShowNew = false;
            this.btnTimeSelect.tbShowProgress = false;
            this.btnTimeSelect.tbShowTip = true;
            this.btnTimeSelect.tbShowToolTipOnButton = false;
            this.btnTimeSelect.tbSplit = "2,3,5,3";
            this.btnTimeSelect.tbText = "";
            this.btnTimeSelect.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTimeSelect.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnTimeSelect.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnTimeSelect.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnTimeSelect.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(88)))), ((int)(((byte)(171)))), ((int)(((byte)(216)))));
            this.btnTimeSelect.tbTextMouseDownPlace = 2;
            this.btnTimeSelect.tbToolTip = "";
            this.btnTimeSelect.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnTimeSelect.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnTimeSelect.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnTimeSelect.tbUpToDownWhenCenter = false;
            this.btnTimeSelect.TimerCountdownNum = 3;
            this.btnTimeSelect.TimerInterval = 1;
            this.btnTimeSelect.VisibleEx = true;
            this.btnTimeSelect.Click += new System.EventHandler(this.btnTimeSelect_Click);
            // 
            // lblStartTime
            // 
            this.lblStartTime.AutoSize = true;
            this.lblStartTime.BackColor = System.Drawing.Color.Transparent;
            this.lblStartTime.Font = new System.Drawing.Font("宋体", 9F);
            this.lblStartTime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.lblStartTime.Location = new System.Drawing.Point(19, 13);
            this.lblStartTime.Name = "lblStartTime";
            this.lblStartTime.Size = new System.Drawing.Size(35, 12);
            this.lblStartTime.TabIndex = 15;
            this.lblStartTime.Text = "日期:";
            this.lblStartTime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblEndTime
            // 
            this.lblEndTime.AutoSize = true;
            this.lblEndTime.BackColor = System.Drawing.Color.Transparent;
            this.lblEndTime.Font = new System.Drawing.Font("宋体", 9F);
            this.lblEndTime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.lblEndTime.Location = new System.Drawing.Point(322, 14);
            this.lblEndTime.Name = "lblEndTime";
            this.lblEndTime.Size = new System.Drawing.Size(17, 12);
            this.lblEndTime.TabIndex = 17;
            this.lblEndTime.Text = "～";
            this.lblEndTime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnOK
            // 
            this.btnOK.BackColor = System.Drawing.Color.Transparent;
            this.btnOK.BindingForm = null;
            this.btnOK.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnOK.Font = new System.Drawing.Font("宋体", 9F);
            this.btnOK.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnOK.Location = new System.Drawing.Point(504, 9);
            this.btnOK.Name = "btnOK";
            this.btnOK.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnOK.Selectable = true;
            this.btnOK.Size = new System.Drawing.Size(39, 21);
            this.btnOK.TabIndex = 18;
            this.btnOK.tbAdriftIconWhenHover = false;
            this.btnOK.tbAutoSize = false;
            this.btnOK.tbAutoSizeEx = true;
            this.btnOK.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnOK.tbBadgeNumber = 0;
            this.btnOK.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnOK.tbDefaultText = "";
            this.btnOK.tbEnableEnter = true;
            this.btnOK.tbEndEllipsis = false;
            this.btnOK.tbIconHoldPlace = true;
            this.btnOK.tbIconImage = null;
            this.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnOK.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnOK.tbIconMore = false;
            this.btnOK.tbIconMouseDown = null;
            this.btnOK.tbIconMouseHover = null;
            this.btnOK.tbIconMouseLeave = null;
            this.btnOK.tbIconPlaceText = 2;
            this.btnOK.tbIconReadOnly = null;
            this.btnOK.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnOK.tbImageMouseDown = null;
            this.btnOK.tbImageMouseHover = null;
            this.btnOK.tbImageMouseLeave = null;
            this.btnOK.tbProgressValue = 50;
            this.btnOK.tbReadOnly = false;
            this.btnOK.tbReadOnlyText = false;
            this.btnOK.tbShadow = false;
            this.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnOK.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnOK.tbShowDot = false;
            this.btnOK.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnOK.tbShowMoreIconImg")));
            this.btnOK.tbShowNew = false;
            this.btnOK.tbShowProgress = false;
            this.btnOK.tbShowTip = true;
            this.btnOK.tbShowToolTipOnButton = false;
            this.btnOK.tbSplit = "13,11,13,11";
            this.btnOK.tbText = "查找";
            this.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnOK.tbTextColor = System.Drawing.Color.White;
            this.btnOK.tbTextColorDisable = System.Drawing.Color.White;
            this.btnOK.tbTextColorDown = System.Drawing.Color.White;
            this.btnOK.tbTextColorHover = System.Drawing.Color.White;
            this.btnOK.tbTextMouseDownPlace = 2;
            this.btnOK.tbToolTip = "";
            this.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnOK.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnOK.tbUpToDownWhenCenter = false;
            this.btnOK.TimerCountdownNum = 3;
            this.btnOK.TimerInterval = 1;
            this.btnOK.VisibleEx = true;
            this.btnOK.Click += new System.EventHandler(this.btnOK_Click);
            // 
            // dtpEndTime
            // 
            this.dtpEndTime.Checked = false;
            this.dtpEndTime.Cursor = System.Windows.Forms.Cursors.Hand;
            this.dtpEndTime.CustomFormat = "HH:mm";
            this.dtpEndTime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpEndTime.Location = new System.Drawing.Point(441, 10);
            this.dtpEndTime.Name = "dtpEndTime";
            this.dtpEndTime.ShowUpDown = true;
            this.dtpEndTime.Size = new System.Drawing.Size(54, 21);
            this.dtpEndTime.TabIndex = 11;
            this.dtpEndTime.Value = new System.DateTime(2020, 9, 14, 13, 24, 39, 0);
            this.dtpEndTime.TextChanged += new System.EventHandler(this.dtpEndTime_TextChanged);
            // 
            // dtpStartTime
            // 
            this.dtpStartTime.Checked = false;
            this.dtpStartTime.Cursor = System.Windows.Forms.Cursors.Hand;
            this.dtpStartTime.CustomFormat = "HH:mm";
            this.dtpStartTime.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpStartTime.Location = new System.Drawing.Point(264, 11);
            this.dtpStartTime.Name = "dtpStartTime";
            this.dtpStartTime.ShowUpDown = true;
            this.dtpStartTime.Size = new System.Drawing.Size(54, 21);
            this.dtpStartTime.TabIndex = 11;
            this.dtpStartTime.Value = new System.DateTime(2020, 9, 14, 13, 3, 0, 0);
            this.dtpStartTime.TextChanged += new System.EventHandler(this.dtpStartTime_TextChanged);
            // 
            // dtpStart
            // 
            this.dtpStart.Checked = false;
            this.dtpStart.Cursor = System.Windows.Forms.Cursors.Hand;
            this.dtpStart.CustomFormat = "yyyy-MM-dd";
            this.dtpStart.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpStart.Location = new System.Drawing.Point(163, 10);
            this.dtpStart.Name = "dtpStart";
            this.dtpStart.Size = new System.Drawing.Size(100, 21);
            this.dtpStart.TabIndex = 11;
            this.dtpStart.CloseUp += new System.EventHandler(this.dtpStart_CloseUp);
            // 
            // pnlLoadChat
            // 
            this.pnlLoadChat.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnlLoadChat.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.pnlLoadChat.Controls.Add(this.pictureBox1);
            this.pnlLoadChat.Controls.Add(this.lblLoading);
            this.pnlLoadChat.Location = new System.Drawing.Point(22, 51);
            this.pnlLoadChat.Name = "pnlLoadChat";
            this.pnlLoadChat.Size = new System.Drawing.Size(549, 414);
            this.pnlLoadChat.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlLoadChat.TabIndex = 11;
            this.pnlLoadChat.tbBackgroundImage = null;
            this.pnlLoadChat.tbShowWatermark = false;
            this.pnlLoadChat.tbSplit = "0,0,0,0";
            this.pnlLoadChat.tbWatermark = null;
            this.pnlLoadChat.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlLoadChat.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            this.pnlLoadChat.Visible = false;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox1.Image = global::iWechatAssistant.Properties.Resources.gif_loading_24;
            this.pictureBox1.Location = new System.Drawing.Point(211, 195);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(24, 24);
            this.pictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pictureBox1.TabIndex = 2;
            this.pictureBox1.TabStop = false;
            // 
            // lblLoading
            // 
            this.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblLoading.AutoSize = true;
            this.lblLoading.Font = new System.Drawing.Font("宋体", 12F);
            this.lblLoading.ForeColor = System.Drawing.Color.Black;
            this.lblLoading.Location = new System.Drawing.Point(241, 199);
            this.lblLoading.Name = "lblLoading";
            this.lblLoading.Size = new System.Drawing.Size(96, 16);
            this.lblLoading.TabIndex = 1;
            this.lblLoading.Text = "正在加载...";
            // 
            // pnlFriend
            // 
            this.pnlFriend.BackColor = System.Drawing.Color.Gainsboro;
            this.pnlFriend.Controls.Add(this.tbPanel7);
            this.pnlFriend.Controls.Add(this.tbPanel1);
            this.pnlFriend.Dock = System.Windows.Forms.DockStyle.Left;
            this.pnlFriend.Location = new System.Drawing.Point(0, 0);
            this.pnlFriend.Margin = new System.Windows.Forms.Padding(0, 0, 1, 0);
            this.pnlFriend.Name = "pnlFriend";
            this.pnlFriend.Size = new System.Drawing.Size(280, 517);
            this.pnlFriend.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlFriend.TabIndex = 8;
            this.pnlFriend.tbBackgroundImage = null;
            this.pnlFriend.tbShowWatermark = false;
            this.pnlFriend.tbSplit = "0,0,0,0";
            this.pnlFriend.tbWatermark = null;
            this.pnlFriend.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlFriend.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel7
            // 
            this.tbPanel7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.tbPanel7.Controls.Add(this.tbPanel10);
            this.tbPanel7.Controls.Add(this.lblLinkmanMsg);
            this.tbPanel7.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.tbPanel7.Location = new System.Drawing.Point(0, 485);
            this.tbPanel7.Name = "tbPanel7";
            this.tbPanel7.Size = new System.Drawing.Size(280, 32);
            this.tbPanel7.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel7.TabIndex = 18;
            this.tbPanel7.tbBackgroundImage = null;
            this.tbPanel7.tbShowWatermark = false;
            this.tbPanel7.tbSplit = "0,0,0,0";
            this.tbPanel7.tbWatermark = null;
            this.tbPanel7.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel7.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel10
            // 
            this.tbPanel10.BackColor = System.Drawing.Color.Gainsboro;
            this.tbPanel10.Dock = System.Windows.Forms.DockStyle.Top;
            this.tbPanel10.Location = new System.Drawing.Point(0, 0);
            this.tbPanel10.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel10.Name = "tbPanel10";
            this.tbPanel10.Size = new System.Drawing.Size(280, 1);
            this.tbPanel10.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel10.TabIndex = 70;
            this.tbPanel10.tbBackgroundImage = null;
            this.tbPanel10.tbShowWatermark = false;
            this.tbPanel10.tbSplit = "0,0,0,0";
            this.tbPanel10.tbWatermark = null;
            this.tbPanel10.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel10.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // lblLinkmanMsg
            // 
            this.lblLinkmanMsg.Location = new System.Drawing.Point(5, 7);
            this.lblLinkmanMsg.Name = "lblLinkmanMsg";
            this.lblLinkmanMsg.Size = new System.Drawing.Size(261, 19);
            this.lblLinkmanMsg.TabIndex = 35;
            this.lblLinkmanMsg.tbAdriftWhenHover = false;
            this.lblLinkmanMsg.tbAutoEllipsis = false;
            this.lblLinkmanMsg.tbAutoSize = false;
            this.lblLinkmanMsg.tbHideImage = false;
            this.lblLinkmanMsg.tbIconImage = null;
            this.lblLinkmanMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblLinkmanMsg.tbIconPlaceText = 5;
            this.lblLinkmanMsg.tbShadow = false;
            this.lblLinkmanMsg.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblLinkmanMsg.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.lblLinkmanMsg.tbShowScrolling = false;
            this.lblLinkmanMsg.Text = "已选择 123 项，全部 123 项";
            this.lblLinkmanMsg.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tbPanel1
            // 
            this.tbPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.tbPanel1.Controls.Add(this.btnUpdateFriend);
            this.tbPanel1.Controls.Add(this.tbPanel9);
            this.tbPanel1.Controls.Add(this.cbxSelectAll);
            this.tbPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.tbPanel1.Location = new System.Drawing.Point(0, 0);
            this.tbPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel1.Name = "tbPanel1";
            this.tbPanel1.Size = new System.Drawing.Size(280, 41);
            this.tbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel1.TabIndex = 0;
            this.tbPanel1.tbBackgroundImage = null;
            this.tbPanel1.tbShowWatermark = false;
            this.tbPanel1.tbSplit = "0,0,0,0";
            this.tbPanel1.tbWatermark = null;
            this.tbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel1.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // btnUpdateFriend
            // 
            this.btnUpdateFriend.BackColor = System.Drawing.Color.Transparent;
            this.btnUpdateFriend.BindingForm = null;
            this.btnUpdateFriend.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnUpdateFriend.Font = new System.Drawing.Font("宋体", 9F);
            this.btnUpdateFriend.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.btnUpdateFriend.Location = new System.Drawing.Point(173, 9);
            this.btnUpdateFriend.Name = "btnUpdateFriend";
            this.btnUpdateFriend.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnUpdateFriend.Selectable = true;
            this.btnUpdateFriend.Size = new System.Drawing.Size(95, 21);
            this.btnUpdateFriend.TabIndex = 89;
            this.btnUpdateFriend.tbAdriftIconWhenHover = false;
            this.btnUpdateFriend.tbAutoSize = false;
            this.btnUpdateFriend.tbAutoSizeEx = false;
            this.btnUpdateFriend.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnUpdateFriend.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnUpdateFriend.tbBadgeNumber = 0;
            this.btnUpdateFriend.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnUpdateFriend.tbDefaultText = "";
            this.btnUpdateFriend.tbEnableEnter = true;
            this.btnUpdateFriend.tbEndEllipsis = false;
            this.btnUpdateFriend.tbIconHoldPlace = true;
            this.btnUpdateFriend.tbIconImage = null;
            this.btnUpdateFriend.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnUpdateFriend.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnUpdateFriend.tbIconMore = false;
            this.btnUpdateFriend.tbIconMouseDown = null;
            this.btnUpdateFriend.tbIconMouseHover = null;
            this.btnUpdateFriend.tbIconMouseLeave = null;
            this.btnUpdateFriend.tbIconPlaceText = 2;
            this.btnUpdateFriend.tbIconReadOnly = null;
            this.btnUpdateFriend.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnUpdateFriend.tbImageMouseDown = null;
            this.btnUpdateFriend.tbImageMouseHover = null;
            this.btnUpdateFriend.tbImageMouseLeave = null;
            this.btnUpdateFriend.tbProgressValue = 50;
            this.btnUpdateFriend.tbReadOnly = false;
            this.btnUpdateFriend.tbReadOnlyText = false;
            this.btnUpdateFriend.tbShadow = false;
            this.btnUpdateFriend.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnUpdateFriend.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnUpdateFriend.tbShowDot = false;
            this.btnUpdateFriend.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnUpdateFriend.tbShowMoreIconImg")));
            this.btnUpdateFriend.tbShowNew = false;
            this.btnUpdateFriend.tbShowProgress = false;
            this.btnUpdateFriend.tbShowTip = true;
            this.btnUpdateFriend.tbShowToolTipOnButton = false;
            this.btnUpdateFriend.tbSplit = "13,11,13,11";
            this.btnUpdateFriend.tbText = "更新好友信息";
            this.btnUpdateFriend.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnUpdateFriend.tbTextColor = System.Drawing.Color.White;
            this.btnUpdateFriend.tbTextColorDisable = System.Drawing.Color.White;
            this.btnUpdateFriend.tbTextColorDown = System.Drawing.Color.White;
            this.btnUpdateFriend.tbTextColorHover = System.Drawing.Color.White;
            this.btnUpdateFriend.tbTextMouseDownPlace = 2;
            this.btnUpdateFriend.tbToolTip = "好友头像、昵称、备注等信息不显示或者显示错误，点此试试。";
            this.btnUpdateFriend.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnUpdateFriend.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnUpdateFriend.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnUpdateFriend.tbUpToDownWhenCenter = false;
            this.btnUpdateFriend.TimerCountdownNum = 3;
            this.btnUpdateFriend.TimerInterval = 1;
            this.btnUpdateFriend.VisibleEx = true;
            this.btnUpdateFriend.Click += new System.EventHandler(this.btnUpdateFriend_Click);
            // 
            // tbPanel9
            // 
            this.tbPanel9.BackColor = System.Drawing.Color.Gainsboro;
            this.tbPanel9.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.tbPanel9.Location = new System.Drawing.Point(0, 40);
            this.tbPanel9.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel9.Name = "tbPanel9";
            this.tbPanel9.Size = new System.Drawing.Size(280, 1);
            this.tbPanel9.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel9.TabIndex = 70;
            this.tbPanel9.tbBackgroundImage = null;
            this.tbPanel9.tbShowWatermark = false;
            this.tbPanel9.tbSplit = "0,0,0,0";
            this.tbPanel9.tbWatermark = null;
            this.tbPanel9.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel9.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // cbxSelectAll
            // 
            this.cbxSelectAll.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.cbxSelectAll.Appearance = System.Windows.Forms.Appearance.Button;
            this.cbxSelectAll.AutoSize = true;
            this.cbxSelectAll.BackColor = System.Drawing.Color.Transparent;
            this.cbxSelectAll.Cursor = System.Windows.Forms.Cursors.Hand;
            this.cbxSelectAll.Font = new System.Drawing.Font("Arial", 10F);
            this.cbxSelectAll.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.cbxSelectAll.ForeColor_Checked = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(51)))), ((int)(((byte)(51)))));
            this.cbxSelectAll.Location = new System.Drawing.Point(10, 9);
            this.cbxSelectAll.Name = "cbxSelectAll";
            this.cbxSelectAll.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.cbxSelectAll.Size = new System.Drawing.Size(99, 23);
            this.cbxSelectAll.TabIndex = 88;
            this.cbxSelectAll.tbAdriftIconWhenHover = false;
            this.cbxSelectAll.tbAutoSize = true;
            this.cbxSelectAll.tbAutoSizeEx = false;
            this.cbxSelectAll.tbIconChecked = ((System.Drawing.Image)(resources.GetObject("cbxSelectAll.tbIconChecked")));
            this.cbxSelectAll.tbIconCheckedMouseDown = null;
            this.cbxSelectAll.tbIconCheckedMouseHover = null;
            this.cbxSelectAll.tbIconCheckedMouseLeave = null;
            this.cbxSelectAll.tbIconCheckedState = iTong.Components.ImageState.FourState;
            this.cbxSelectAll.tbIconHoldPlace = true;
            this.cbxSelectAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.cbxSelectAll.tbIconIndeterminate = ((System.Drawing.Image)(resources.GetObject("cbxSelectAll.tbIconIndeterminate")));
            this.cbxSelectAll.tbIconIndeterminateMouseDown = null;
            this.cbxSelectAll.tbIconIndeterminateMouseHover = null;
            this.cbxSelectAll.tbIconIndeterminateMouseLeave = null;
            this.cbxSelectAll.tbIconIndeterminateState = iTong.Components.ImageState.FourState;
            this.cbxSelectAll.tbIconPlaceText = 1;
            this.cbxSelectAll.tbIconUnChecked = ((System.Drawing.Image)(resources.GetObject("cbxSelectAll.tbIconUnChecked")));
            this.cbxSelectAll.tbIconUnCheckedMouseDown = null;
            this.cbxSelectAll.tbIconUnCheckedMouseHover = null;
            this.cbxSelectAll.tbIconUnCheckedMouseLeave = null;
            this.cbxSelectAll.tbIconUnCheckedState = iTong.Components.ImageState.FourState;
            this.cbxSelectAll.tbImageBackground = null;
            this.cbxSelectAll.tbImageBackgroundState = iTong.Components.ImageState.TwoState;
            this.cbxSelectAll.tbImageCheckedMouseDown = null;
            this.cbxSelectAll.tbImageCheckedMouseHover = null;
            this.cbxSelectAll.tbImageCheckedMouseLeave = null;
            this.cbxSelectAll.tbImageUnCheckedMouseDown = null;
            this.cbxSelectAll.tbImageUnCheckedMouseHover = null;
            this.cbxSelectAll.tbImageUnCheckedMouseLeave = null;
            this.cbxSelectAll.tbReadOnly = false;
            this.cbxSelectAll.tbShadow = false;
            this.cbxSelectAll.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.cbxSelectAll.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.cbxSelectAll.tbSplit = "3,3,3,3";
            this.cbxSelectAll.tbToolTip = "";
            this.cbxSelectAll.Text = "全选";
            this.cbxSelectAll.UseVisualStyleBackColor = false;
            this.cbxSelectAll.CheckedChanged += new System.EventHandler(this.cbxSelectAll_CheckedChanged);
            // 
            // tbPanel6
            // 
            this.tbPanel6.BackColor = System.Drawing.Color.White;
            this.tbPanel6.Controls.Add(this.tbPanel11);
            this.tbPanel6.Controls.Add(this.btnNewExport);
            this.tbPanel6.Controls.Add(this.btnExport);
            this.tbPanel6.Controls.Add(this.lblState);
            this.tbPanel6.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.tbPanel6.Location = new System.Drawing.Point(0, 517);
            this.tbPanel6.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel6.Name = "tbPanel6";
            this.tbPanel6.Size = new System.Drawing.Size(876, 80);
            this.tbPanel6.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel6.TabIndex = 15;
            this.tbPanel6.tbBackgroundImage = null;
            this.tbPanel6.tbShowWatermark = false;
            this.tbPanel6.tbSplit = "0,0,0,0";
            this.tbPanel6.tbWatermark = null;
            this.tbPanel6.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel6.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // tbPanel11
            // 
            this.tbPanel11.BackColor = System.Drawing.Color.Gainsboro;
            this.tbPanel11.Dock = System.Windows.Forms.DockStyle.Top;
            this.tbPanel11.Location = new System.Drawing.Point(0, 0);
            this.tbPanel11.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel11.Name = "tbPanel11";
            this.tbPanel11.Size = new System.Drawing.Size(876, 1);
            this.tbPanel11.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel11.TabIndex = 89;
            this.tbPanel11.tbBackgroundImage = null;
            this.tbPanel11.tbShowWatermark = false;
            this.tbPanel11.tbSplit = "0,0,0,0";
            this.tbPanel11.tbWatermark = null;
            this.tbPanel11.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel11.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // btnNewExport
            // 
            this.btnNewExport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNewExport.BackColor = System.Drawing.Color.Transparent;
            this.btnNewExport.BindingForm = null;
            this.btnNewExport.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnNewExport.Font = new System.Drawing.Font("Arial", 12F);
            this.btnNewExport.Location = new System.Drawing.Point(646, 21);
            this.btnNewExport.Margin = new System.Windows.Forms.Padding(0);
            this.btnNewExport.Name = "btnNewExport";
            this.btnNewExport.Padding = new System.Windows.Forms.Padding(6, 0, 10, 3);
            this.btnNewExport.Selectable = true;
            this.btnNewExport.Size = new System.Drawing.Size(30, 38);
            this.btnNewExport.TabIndex = 85;
            this.btnNewExport.tbAdriftIconWhenHover = false;
            this.btnNewExport.tbAutoSize = false;
            this.btnNewExport.tbAutoSizeEx = false;
            this.btnNewExport.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blueright;
            this.btnNewExport.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnNewExport.tbBadgeNumber = 0;
            this.btnNewExport.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnNewExport.tbDefaultText = "";
            this.btnNewExport.tbEnableEnter = true;
            this.btnNewExport.tbEndEllipsis = false;
            this.btnNewExport.tbIconHoldPlace = true;
            this.btnNewExport.tbIconImage = global::iWechatAssistant.Properties.Resources.btn_ddl_selecttype;
            this.btnNewExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnNewExport.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnNewExport.tbIconMore = false;
            this.btnNewExport.tbIconMouseDown = null;
            this.btnNewExport.tbIconMouseHover = null;
            this.btnNewExport.tbIconMouseLeave = null;
            this.btnNewExport.tbIconPlaceText = 3;
            this.btnNewExport.tbIconReadOnly = null;
            this.btnNewExport.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnNewExport.tbImageMouseDown = null;
            this.btnNewExport.tbImageMouseHover = null;
            this.btnNewExport.tbImageMouseLeave = null;
            this.btnNewExport.tbProgressValue = 50;
            this.btnNewExport.tbReadOnly = false;
            this.btnNewExport.tbReadOnlyText = false;
            this.btnNewExport.tbShadow = false;
            this.btnNewExport.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnNewExport.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnNewExport.tbShowDot = false;
            this.btnNewExport.tbShowMoreIconImg = null;
            this.btnNewExport.tbShowNew = false;
            this.btnNewExport.tbShowProgress = false;
            this.btnNewExport.tbShowTip = true;
            this.btnNewExport.tbShowToolTipOnButton = false;
            this.btnNewExport.tbSplit = "13,11,13,11";
            this.btnNewExport.tbText = "";
            this.btnNewExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnNewExport.tbTextColor = System.Drawing.Color.White;
            this.btnNewExport.tbTextColorDisable = System.Drawing.Color.White;
            this.btnNewExport.tbTextColorDown = System.Drawing.Color.White;
            this.btnNewExport.tbTextColorHover = System.Drawing.Color.White;
            this.btnNewExport.tbTextMouseDownPlace = 0;
            this.btnNewExport.tbToolTip = "";
            this.btnNewExport.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnNewExport.tbToolTipFont = new System.Drawing.Font("Arial", 12F);
            this.btnNewExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnNewExport.tbUpToDownWhenCenter = false;
            this.btnNewExport.TimerCountdownNum = 3;
            this.btnNewExport.TimerInterval = 1;
            this.btnNewExport.Visible = false;
            this.btnNewExport.VisibleEx = true;
            this.btnNewExport.Click += new System.EventHandler(this.btnNewExport_Click);
            // 
            // btnExport
            // 
            this.btnExport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExport.BackColor = System.Drawing.Color.Transparent;
            this.btnExport.BindingForm = null;
            this.btnExport.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnExport.Font = new System.Drawing.Font("宋体", 12F);
            this.btnExport.Location = new System.Drawing.Point(726, 21);
            this.btnExport.Margin = new System.Windows.Forms.Padding(0);
            this.btnExport.Name = "btnExport";
            this.btnExport.Padding = new System.Windows.Forms.Padding(10, 2, 10, 2);
            this.btnExport.Selectable = true;
            this.btnExport.Size = new System.Drawing.Size(138, 39);
            this.btnExport.TabIndex = 86;
            this.btnExport.tbAdriftIconWhenHover = false;
            this.btnExport.tbAutoSize = false;
            this.btnExport.tbAutoSizeEx = false;
            this.btnExport.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnExport.tbBadgeNumber = 0;
            this.btnExport.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnExport.tbDefaultText = "";
            this.btnExport.tbEnableEnter = true;
            this.btnExport.tbEndEllipsis = true;
            this.btnExport.tbIconHoldPlace = true;
            this.btnExport.tbIconImage = global::iWechatAssistant.Properties.Resources.btn_3_more;
            this.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnExport.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnExport.tbIconMore = false;
            this.btnExport.tbIconMouseDown = null;
            this.btnExport.tbIconMouseHover = null;
            this.btnExport.tbIconMouseLeave = null;
            this.btnExport.tbIconPlaceText = 3;
            this.btnExport.tbIconReadOnly = null;
            this.btnExport.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnExport.tbImageMouseDown = null;
            this.btnExport.tbImageMouseHover = null;
            this.btnExport.tbImageMouseLeave = null;
            this.btnExport.tbProgressValue = 50;
            this.btnExport.tbReadOnly = false;
            this.btnExport.tbReadOnlyText = false;
            this.btnExport.tbShadow = false;
            this.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnExport.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnExport.tbShowDot = false;
            this.btnExport.tbShowMoreIconImg = null;
            this.btnExport.tbShowNew = false;
            this.btnExport.tbShowProgress = false;
            this.btnExport.tbShowTip = true;
            this.btnExport.tbShowToolTipOnButton = false;
            this.btnExport.tbSplit = "13,11,13,11";
            this.btnExport.tbText = "导出到电脑";
            this.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnExport.tbTextColor = System.Drawing.Color.White;
            this.btnExport.tbTextColorDisable = System.Drawing.Color.White;
            this.btnExport.tbTextColorDown = System.Drawing.Color.White;
            this.btnExport.tbTextColorHover = System.Drawing.Color.White;
            this.btnExport.tbTextMouseDownPlace = 0;
            this.btnExport.tbToolTip = "";
            this.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnExport.tbToolTipFont = new System.Drawing.Font("宋体", 12F);
            this.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnExport.tbUpToDownWhenCenter = false;
            this.btnExport.TimerCountdownNum = 3;
            this.btnExport.TimerInterval = 1;
            this.btnExport.VisibleEx = true;
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // lblState
            // 
            this.lblState.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblState.BackColor = System.Drawing.Color.Transparent;
            this.lblState.Font = new System.Drawing.Font("宋体", 10F);
            this.lblState.ForeColor = System.Drawing.Color.Red;
            this.lblState.Location = new System.Drawing.Point(38, 21);
            this.lblState.Name = "lblState";
            this.lblState.Size = new System.Drawing.Size(861, 38);
            this.lblState.TabIndex = 0;
            this.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnlTop
            // 
            this.pnlTop.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.pnlTop.Controls.Add(this.llblExportPreview);
            this.pnlTop.Controls.Add(this.lblRegister);
            this.pnlTop.Controls.Add(this.picTimeScreen);
            this.pnlTop.Controls.Add(this.btnTimeScreen);
            this.pnlTop.Controls.Add(this.txtSearchChat);
            this.pnlTop.Controls.Add(this.tbPanel14);
            this.pnlTop.Controls.Add(this.btnSuperior);
            this.pnlTop.Controls.Add(this.btnWeCahtUsers);
            this.pnlTop.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTop.Location = new System.Drawing.Point(0, 0);
            this.pnlTop.Margin = new System.Windows.Forms.Padding(0);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Size = new System.Drawing.Size(876, 41);
            this.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlTop.TabIndex = 11;
            this.pnlTop.tbBackgroundImage = null;
            this.pnlTop.tbShowWatermark = false;
            this.pnlTop.tbSplit = "0,0,0,0";
            this.pnlTop.tbWatermark = null;
            this.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlTop.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // llblExportPreview
            // 
            this.llblExportPreview.ActiveLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(115)))), ((int)(((byte)(255)))));
            this.llblExportPreview.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.llblExportPreview.AutoSize = true;
            this.llblExportPreview.DisabledLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(115)))), ((int)(((byte)(255)))));
            this.llblExportPreview.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline;
            this.llblExportPreview.LinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(115)))), ((int)(((byte)(255)))));
            this.llblExportPreview.Location = new System.Drawing.Point(622, 13);
            this.llblExportPreview.Name = "llblExportPreview";
            this.llblExportPreview.Size = new System.Drawing.Size(53, 12);
            this.llblExportPreview.TabIndex = 105;
            this.llblExportPreview.TabStop = true;
            this.llblExportPreview.Text = "导出预览";
            this.llblExportPreview.VisitedLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(115)))), ((int)(((byte)(255)))));
            this.llblExportPreview.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.llblExportPreview_LinkClicked);
            // 
            // lblRegister
            // 
            this.lblRegister.BackColor = System.Drawing.Color.Transparent;
            this.lblRegister.Cursor = System.Windows.Forms.Cursors.Default;
            this.lblRegister.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(102)))), ((int)(((byte)(102)))), ((int)(((byte)(102)))));
            this.lblRegister.Location = new System.Drawing.Point(423, 12);
            this.lblRegister.Name = "lblRegister";
            this.lblRegister.Size = new System.Drawing.Size(279, 18);
            this.lblRegister.TabIndex = 104;
            this.lblRegister.Text = "试用版预览部分内容用\"*\"代替 立即购买";
            this.lblRegister.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // picTimeScreen
            // 
            this.picTimeScreen.Image = global::iWechatAssistant.Properties.Resources.TimeScreenUpDowm;
            this.picTimeScreen.Location = new System.Drawing.Point(394, 8);
            this.picTimeScreen.Margin = new System.Windows.Forms.Padding(0);
            this.picTimeScreen.Name = "picTimeScreen";
            this.picTimeScreen.Size = new System.Drawing.Size(9, 24);
            this.picTimeScreen.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.picTimeScreen.TabIndex = 102;
            this.picTimeScreen.TabStop = false;
            this.picTimeScreen.Click += new System.EventHandler(this.btnTimeScreen_Click);
            // 
            // btnTimeScreen
            // 
            this.btnTimeScreen.BackColor = System.Drawing.Color.Transparent;
            this.btnTimeScreen.BindingForm = null;
            this.btnTimeScreen.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnTimeScreen.Location = new System.Drawing.Point(300, 9);
            this.btnTimeScreen.Margin = new System.Windows.Forms.Padding(0);
            this.btnTimeScreen.Name = "btnTimeScreen";
            this.btnTimeScreen.Padding = new System.Windows.Forms.Padding(5, 2, 5, 0);
            this.btnTimeScreen.Selectable = true;
            this.btnTimeScreen.Size = new System.Drawing.Size(94, 23);
            this.btnTimeScreen.TabIndex = 103;
            this.btnTimeScreen.tbAdriftIconWhenHover = false;
            this.btnTimeScreen.tbAutoSize = false;
            this.btnTimeScreen.tbAutoSizeEx = false;
            this.btnTimeScreen.tbBackgroundImage = null;
            this.btnTimeScreen.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnTimeScreen.tbBadgeNumber = 0;
            this.btnTimeScreen.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnTimeScreen.tbDefaultText = "";
            this.btnTimeScreen.tbEnableEnter = true;
            this.btnTimeScreen.tbEndEllipsis = false;
            this.btnTimeScreen.tbIconHoldPlace = true;
            this.btnTimeScreen.tbIconImage = global::iWechatAssistant.Properties.Resources.btn_4_timescreen;
            this.btnTimeScreen.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTimeScreen.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnTimeScreen.tbIconMore = false;
            this.btnTimeScreen.tbIconMouseDown = null;
            this.btnTimeScreen.tbIconMouseHover = null;
            this.btnTimeScreen.tbIconMouseLeave = null;
            this.btnTimeScreen.tbIconPlaceText = 2;
            this.btnTimeScreen.tbIconReadOnly = null;
            this.btnTimeScreen.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnTimeScreen.tbImageMouseDown = null;
            this.btnTimeScreen.tbImageMouseHover = null;
            this.btnTimeScreen.tbImageMouseLeave = null;
            this.btnTimeScreen.tbProgressValue = 50;
            this.btnTimeScreen.tbReadOnly = false;
            this.btnTimeScreen.tbReadOnlyText = false;
            this.btnTimeScreen.tbShadow = false;
            this.btnTimeScreen.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnTimeScreen.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnTimeScreen.tbShowDot = false;
            this.btnTimeScreen.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnTimeScreen.tbShowMoreIconImg")));
            this.btnTimeScreen.tbShowNew = false;
            this.btnTimeScreen.tbShowProgress = false;
            this.btnTimeScreen.tbShowTip = true;
            this.btnTimeScreen.tbShowToolTipOnButton = false;
            this.btnTimeScreen.tbSplit = "0,0,0,0";
            this.btnTimeScreen.tbText = "按时间筛选";
            this.btnTimeScreen.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnTimeScreen.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnTimeScreen.tbTextColorDisable = System.Drawing.Color.LightGray;
            this.btnTimeScreen.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnTimeScreen.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnTimeScreen.tbTextMouseDownPlace = 2;
            this.btnTimeScreen.tbToolTip = "";
            this.btnTimeScreen.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnTimeScreen.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnTimeScreen.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnTimeScreen.tbUpToDownWhenCenter = false;
            this.btnTimeScreen.TimerCountdownNum = 3;
            this.btnTimeScreen.TimerInterval = 1;
            this.btnTimeScreen.VisibleEx = true;
            this.btnTimeScreen.Click += new System.EventHandler(this.btnTimeScreen_Click);
            // 
            // txtSearchChat
            // 
            this.txtSearchChat.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtSearchChat.BackColor = System.Drawing.Color.Transparent;
            this.txtSearchChat.Location = new System.Drawing.Point(694, 8);
            this.txtSearchChat.MaxLength = 32767;
            this.txtSearchChat.Name = "txtSearchChat";
            this.txtSearchChat.Padding = new System.Windows.Forms.Padding(5);
            this.txtSearchChat.ReadOnly = false;
            this.txtSearchChat.SearchText = "";
            this.txtSearchChat.SearchTipText = "按回车搜索记录";
            this.txtSearchChat.ShowClear = true;
            this.txtSearchChat.ShowClearAlways = false;
            this.txtSearchChat.ShowMore = false;
            this.txtSearchChat.ShowSearch = true;
            this.txtSearchChat.Size = new System.Drawing.Size(170, 23);
            this.txtSearchChat.TabIndex = 88;
            this.txtSearchChat.tbImageSearchState = iTong.Components.ImageState.ThreeState;
            this.txtSearchChat.Timer = true;
            this.txtSearchChat.TimerInterval = 0.5D;
            this.txtSearchChat.ClearClick += new System.EventHandler(this.txtSearchChat_ClearClick);
            this.txtSearchChat.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtSearchChat_KeyDown);
            // 
            // tbPanel14
            // 
            this.tbPanel14.BackColor = System.Drawing.Color.Gainsboro;
            this.tbPanel14.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.tbPanel14.Location = new System.Drawing.Point(0, 40);
            this.tbPanel14.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel14.Name = "tbPanel14";
            this.tbPanel14.Size = new System.Drawing.Size(876, 1);
            this.tbPanel14.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel14.TabIndex = 83;
            this.tbPanel14.tbBackgroundImage = null;
            this.tbPanel14.tbShowWatermark = false;
            this.tbPanel14.tbSplit = "0,0,0,0";
            this.tbPanel14.tbWatermark = null;
            this.tbPanel14.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel14.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // btnSuperior
            // 
            this.btnSuperior.BackColor = System.Drawing.Color.Transparent;
            this.btnSuperior.BindingForm = null;
            this.btnSuperior.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSuperior.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSuperior.Location = new System.Drawing.Point(10, 13);
            this.btnSuperior.Name = "btnSuperior";
            this.btnSuperior.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnSuperior.Selectable = true;
            this.btnSuperior.Size = new System.Drawing.Size(16, 14);
            this.btnSuperior.TabIndex = 82;
            this.btnSuperior.tbAdriftIconWhenHover = false;
            this.btnSuperior.tbAutoSize = false;
            this.btnSuperior.tbAutoSizeEx = true;
            this.btnSuperior.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_retreatex;
            this.btnSuperior.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnSuperior.tbBadgeNumber = 0;
            this.btnSuperior.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnSuperior.tbDefaultText = "";
            this.btnSuperior.tbEnableEnter = true;
            this.btnSuperior.tbEndEllipsis = false;
            this.btnSuperior.tbIconHoldPlace = true;
            this.btnSuperior.tbIconImage = null;
            this.btnSuperior.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSuperior.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnSuperior.tbIconMore = false;
            this.btnSuperior.tbIconMouseDown = null;
            this.btnSuperior.tbIconMouseHover = null;
            this.btnSuperior.tbIconMouseLeave = null;
            this.btnSuperior.tbIconPlaceText = 2;
            this.btnSuperior.tbIconReadOnly = null;
            this.btnSuperior.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnSuperior.tbImageMouseDown = null;
            this.btnSuperior.tbImageMouseHover = null;
            this.btnSuperior.tbImageMouseLeave = null;
            this.btnSuperior.tbProgressValue = 50;
            this.btnSuperior.tbReadOnly = false;
            this.btnSuperior.tbReadOnlyText = false;
            this.btnSuperior.tbShadow = false;
            this.btnSuperior.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnSuperior.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnSuperior.tbShowDot = false;
            this.btnSuperior.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnSuperior.tbShowMoreIconImg")));
            this.btnSuperior.tbShowNew = false;
            this.btnSuperior.tbShowProgress = false;
            this.btnSuperior.tbShowTip = true;
            this.btnSuperior.tbShowToolTipOnButton = false;
            this.btnSuperior.tbSplit = "12,1,1,1";
            this.btnSuperior.tbText = "";
            this.btnSuperior.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnSuperior.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnSuperior.tbTextColorDisable = System.Drawing.Color.LightGray;
            this.btnSuperior.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnSuperior.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnSuperior.tbTextMouseDownPlace = 2;
            this.btnSuperior.tbToolTip = "";
            this.btnSuperior.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnSuperior.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btnSuperior.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnSuperior.tbUpToDownWhenCenter = false;
            this.btnSuperior.TimerCountdownNum = 3;
            this.btnSuperior.TimerInterval = 1;
            this.btnSuperior.VisibleEx = true;
            this.btnSuperior.Click += new System.EventHandler(this.btnSuperior_Click);
            // 
            // btnWeCahtUsers
            // 
            this.btnWeCahtUsers.BackColor = System.Drawing.Color.White;
            this.btnWeCahtUsers.BindingForm = null;
            this.btnWeCahtUsers.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnWeCahtUsers.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnWeCahtUsers.Location = new System.Drawing.Point(40, 8);
            this.btnWeCahtUsers.Margin = new System.Windows.Forms.Padding(0);
            this.btnWeCahtUsers.Name = "btnWeCahtUsers";
            this.btnWeCahtUsers.Padding = new System.Windows.Forms.Padding(10, 2, 10, 2);
            this.btnWeCahtUsers.Selectable = true;
            this.btnWeCahtUsers.Size = new System.Drawing.Size(231, 24);
            this.btnWeCahtUsers.TabIndex = 71;
            this.btnWeCahtUsers.tbAdriftIconWhenHover = false;
            this.btnWeCahtUsers.tbAutoSize = false;
            this.btnWeCahtUsers.tbAutoSizeEx = false;
            this.btnWeCahtUsers.tbBackgroundImage = ((System.Drawing.Image)(resources.GetObject("btnWeCahtUsers.tbBackgroundImage")));
            this.btnWeCahtUsers.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnWeCahtUsers.tbBadgeNumber = 0;
            this.btnWeCahtUsers.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnWeCahtUsers.tbDefaultText = "";
            this.btnWeCahtUsers.tbEnableEnter = true;
            this.btnWeCahtUsers.tbEndEllipsis = false;
            this.btnWeCahtUsers.tbIconHoldPlace = true;
            this.btnWeCahtUsers.tbIconImage = null;
            this.btnWeCahtUsers.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnWeCahtUsers.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnWeCahtUsers.tbIconMore = true;
            this.btnWeCahtUsers.tbIconMouseDown = null;
            this.btnWeCahtUsers.tbIconMouseHover = null;
            this.btnWeCahtUsers.tbIconMouseLeave = null;
            this.btnWeCahtUsers.tbIconPlaceText = 2;
            this.btnWeCahtUsers.tbIconReadOnly = null;
            this.btnWeCahtUsers.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnWeCahtUsers.tbImageMouseDown = null;
            this.btnWeCahtUsers.tbImageMouseHover = null;
            this.btnWeCahtUsers.tbImageMouseLeave = null;
            this.btnWeCahtUsers.tbProgressValue = 50;
            this.btnWeCahtUsers.tbReadOnly = false;
            this.btnWeCahtUsers.tbReadOnlyText = false;
            this.btnWeCahtUsers.tbShadow = false;
            this.btnWeCahtUsers.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnWeCahtUsers.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnWeCahtUsers.tbShowDot = false;
            this.btnWeCahtUsers.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnWeCahtUsers.tbShowMoreIconImg")));
            this.btnWeCahtUsers.tbShowNew = false;
            this.btnWeCahtUsers.tbShowProgress = false;
            this.btnWeCahtUsers.tbShowTip = true;
            this.btnWeCahtUsers.tbShowToolTipOnButton = false;
            this.btnWeCahtUsers.tbSplit = "2,3,5,3";
            this.btnWeCahtUsers.tbText = "";
            this.btnWeCahtUsers.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnWeCahtUsers.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnWeCahtUsers.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnWeCahtUsers.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnWeCahtUsers.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(88)))), ((int)(((byte)(171)))), ((int)(((byte)(216)))));
            this.btnWeCahtUsers.tbTextMouseDownPlace = 2;
            this.btnWeCahtUsers.tbToolTip = "";
            this.btnWeCahtUsers.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnWeCahtUsers.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnWeCahtUsers.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnWeCahtUsers.tbUpToDownWhenCenter = false;
            this.btnWeCahtUsers.TimerCountdownNum = 3;
            this.btnWeCahtUsers.TimerInterval = 1;
            this.btnWeCahtUsers.VisibleEx = true;
            this.btnWeCahtUsers.Click += new System.EventHandler(this.btnWeCahtUsers_Click);
            // 
            // txtSearchFriend
            // 
            this.txtSearchFriend.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtSearchFriend.BackColor = System.Drawing.Color.Transparent;
            this.txtSearchFriend.Cursor = System.Windows.Forms.Cursors.Hand;
            this.txtSearchFriend.Location = new System.Drawing.Point(1643, 6);
            this.txtSearchFriend.MaxLength = 32767;
            this.txtSearchFriend.Name = "txtSearchFriend";
            this.txtSearchFriend.Padding = new System.Windows.Forms.Padding(5);
            this.txtSearchFriend.ReadOnly = false;
            this.txtSearchFriend.SearchText = "";
            this.txtSearchFriend.SearchTipText = "搜索";
            this.txtSearchFriend.ShowClear = true;
            this.txtSearchFriend.ShowClearAlways = false;
            this.txtSearchFriend.ShowMore = false;
            this.txtSearchFriend.ShowSearch = true;
            this.txtSearchFriend.Size = new System.Drawing.Size(189, 23);
            this.txtSearchFriend.TabIndex = 10;
            this.txtSearchFriend.tbImageSearchState = iTong.Components.ImageState.ThreeState;
            this.txtSearchFriend.Timer = true;
            this.txtSearchFriend.TimerInterval = 0.5D;
            this.txtSearchFriend.Visible = false;
            this.txtSearchFriend.TextChangedByTimer += new System.EventHandler(this.txtSearchFriend_TextChangedByTimer);
            // 
            // btnPCPath
            // 
            this.btnPCPath.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPCPath.BackColor = System.Drawing.Color.Transparent;
            this.btnPCPath.BindingForm = null;
            this.btnPCPath.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnPCPath.Location = new System.Drawing.Point(1463, 4);
            this.btnPCPath.Margin = new System.Windows.Forms.Padding(0);
            this.btnPCPath.Name = "btnPCPath";
            this.btnPCPath.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnPCPath.Selectable = true;
            this.btnPCPath.Size = new System.Drawing.Size(103, 23);
            this.btnPCPath.TabIndex = 84;
            this.btnPCPath.tbAdriftIconWhenHover = false;
            this.btnPCPath.tbAutoSize = false;
            this.btnPCPath.tbAutoSizeEx = false;
            this.btnPCPath.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnPCPath.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnPCPath.tbBadgeNumber = 0;
            this.btnPCPath.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnPCPath.tbDefaultText = "";
            this.btnPCPath.tbEnableEnter = true;
            this.btnPCPath.tbEndEllipsis = false;
            this.btnPCPath.tbIconHoldPlace = true;
            this.btnPCPath.tbIconImage = null;
            this.btnPCPath.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPCPath.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnPCPath.tbIconMore = false;
            this.btnPCPath.tbIconMouseDown = null;
            this.btnPCPath.tbIconMouseHover = null;
            this.btnPCPath.tbIconMouseLeave = null;
            this.btnPCPath.tbIconPlaceText = 2;
            this.btnPCPath.tbIconReadOnly = null;
            this.btnPCPath.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnPCPath.tbImageMouseDown = null;
            this.btnPCPath.tbImageMouseHover = null;
            this.btnPCPath.tbImageMouseLeave = null;
            this.btnPCPath.tbProgressValue = 50;
            this.btnPCPath.tbReadOnly = false;
            this.btnPCPath.tbReadOnlyText = false;
            this.btnPCPath.tbShadow = false;
            this.btnPCPath.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnPCPath.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnPCPath.tbShowDot = false;
            this.btnPCPath.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnPCPath.tbShowMoreIconImg")));
            this.btnPCPath.tbShowNew = false;
            this.btnPCPath.tbShowProgress = false;
            this.btnPCPath.tbShowTip = true;
            this.btnPCPath.tbShowToolTipOnButton = false;
            this.btnPCPath.tbSplit = "3,3,3,3";
            this.btnPCPath.tbText = "同步电脑微信路径";
            this.btnPCPath.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPCPath.tbTextColor = System.Drawing.Color.White;
            this.btnPCPath.tbTextColorDisable = System.Drawing.Color.White;
            this.btnPCPath.tbTextColorDown = System.Drawing.Color.White;
            this.btnPCPath.tbTextColorHover = System.Drawing.Color.White;
            this.btnPCPath.tbTextMouseDownPlace = 0;
            this.btnPCPath.tbToolTip = "";
            this.btnPCPath.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnPCPath.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnPCPath.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPCPath.tbUpToDownWhenCenter = false;
            this.btnPCPath.TimerCountdownNum = 3;
            this.btnPCPath.TimerInterval = 1;
            this.btnPCPath.Visible = false;
            this.btnPCPath.VisibleEx = true;
            this.btnPCPath.Click += new System.EventHandler(this.btnPCPath_Click);
            // 
            // lblCurrentAccount
            // 
            this.lblCurrentAccount.BackColor = System.Drawing.Color.Transparent;
            this.lblCurrentAccount.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Bold);
            this.lblCurrentAccount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(82)))), ((int)(((byte)(82)))), ((int)(((byte)(82)))));
            this.lblCurrentAccount.Location = new System.Drawing.Point(571, 4);
            this.lblCurrentAccount.Name = "lblCurrentAccount";
            this.lblCurrentAccount.Size = new System.Drawing.Size(57, 23);
            this.lblCurrentAccount.TabIndex = 72;
            this.lblCurrentAccount.tbAdriftWhenHover = false;
            this.lblCurrentAccount.tbAutoEllipsis = false;
            this.lblCurrentAccount.tbAutoSize = false;
            this.lblCurrentAccount.tbHideImage = false;
            this.lblCurrentAccount.tbIconImage = null;
            this.lblCurrentAccount.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblCurrentAccount.tbIconPlaceText = 5;
            this.lblCurrentAccount.tbShadow = false;
            this.lblCurrentAccount.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblCurrentAccount.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.lblCurrentAccount.tbShowScrolling = false;
            this.lblCurrentAccount.Text = "当前设备";
            this.lblCurrentAccount.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.lblCurrentAccount.Visible = false;
            // 
            // cmsExportNews
            // 
            this.cmsExportNews.AccessibleDescription = "153x126";
            this.cmsExportNews.DropShadowEnabled = false;
            this.cmsExportNews.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.cmsExportNews.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.按格式导出ToolStripMenuItem,
            this.按媒体导出ToolStripMenuItem,
            this.toolStripSeparator2,
            this.tsmiFriend,
            this.toolStripSeparator3,
            this.tsmiAll});
            this.cmsExportNews.Name = "munOperate";
            this.cmsExportNews.Size = new System.Drawing.Size(149, 104);
            this.cmsExportNews.tbBackColor = System.Drawing.Color.White;
            this.cmsExportNews.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.cmsExportNews.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.cmsExportNews.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // 按格式导出ToolStripMenuItem
            // 
            this.按格式导出ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiHtml,
            this.tsmiExcelEx,
            this.tsmiTxtEx});
            this.按格式导出ToolStripMenuItem.Name = "按格式导出ToolStripMenuItem";
            this.按格式导出ToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.按格式导出ToolStripMenuItem.Text = "按格式导出";
            // 
            // tsmiHtml
            // 
            this.tsmiHtml.Name = "tsmiHtml";
            this.tsmiHtml.Size = new System.Drawing.Size(165, 22);
            this.tsmiHtml.Text = "导出为Html文件";
            this.tsmiHtml.Click += new System.EventHandler(this.tsmiHtml_Click);
            // 
            // tsmiExcelEx
            // 
            this.tsmiExcelEx.Name = "tsmiExcelEx";
            this.tsmiExcelEx.Size = new System.Drawing.Size(165, 22);
            this.tsmiExcelEx.Text = "导出为Excel文件";
            this.tsmiExcelEx.Click += new System.EventHandler(this.tsmiExcelEx_Click);
            // 
            // tsmiTxtEx
            // 
            this.tsmiTxtEx.Name = "tsmiTxtEx";
            this.tsmiTxtEx.Size = new System.Drawing.Size(165, 22);
            this.tsmiTxtEx.Text = "导出为Txt文件";
            this.tsmiTxtEx.Click += new System.EventHandler(this.tsmiTxtEx_Click);
            // 
            // 按媒体导出ToolStripMenuItem
            // 
            this.按媒体导出ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiPicture,
            this.tsmiVoice,
            this.tsmiVideo});
            this.按媒体导出ToolStripMenuItem.Name = "按媒体导出ToolStripMenuItem";
            this.按媒体导出ToolStripMenuItem.Size = new System.Drawing.Size(148, 22);
            this.按媒体导出ToolStripMenuItem.Text = "按媒体导出";
            // 
            // tsmiPicture
            // 
            this.tsmiPicture.Name = "tsmiPicture";
            this.tsmiPicture.Size = new System.Drawing.Size(124, 22);
            this.tsmiPicture.Text = "导出图片";
            this.tsmiPicture.Click += new System.EventHandler(this.tsmiPicture_Click);
            // 
            // tsmiVoice
            // 
            this.tsmiVoice.Name = "tsmiVoice";
            this.tsmiVoice.Size = new System.Drawing.Size(124, 22);
            this.tsmiVoice.Text = "导出语音";
            this.tsmiVoice.Click += new System.EventHandler(this.tsmiVoice_Click);
            // 
            // tsmiVideo
            // 
            this.tsmiVideo.Name = "tsmiVideo";
            this.tsmiVideo.Size = new System.Drawing.Size(124, 22);
            this.tsmiVideo.Text = "导出视频";
            this.tsmiVideo.Click += new System.EventHandler(this.tsmiVideo_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(145, 6);
            // 
            // tsmiFriend
            // 
            this.tsmiFriend.Name = "tsmiFriend";
            this.tsmiFriend.Size = new System.Drawing.Size(148, 22);
            this.tsmiFriend.Text = "导出好友信息";
            this.tsmiFriend.Click += new System.EventHandler(this.tsmiFriend_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(145, 6);
            // 
            // tsmiAll
            // 
            this.tsmiAll.Name = "tsmiAll";
            this.tsmiAll.Size = new System.Drawing.Size(148, 22);
            this.tsmiAll.Text = "导出全部";
            this.tsmiAll.Click += new System.EventHandler(this.tsmiAll_Click);
            // 
            // bgwShowAccount
            // 
            this.bgwShowAccount.WorkerSupportsCancellation = true;
            this.bgwShowAccount.DoWork += new System.ComponentModel.DoWorkEventHandler(this.bgwShowAccount_DoWork);
            this.bgwShowAccount.RunWorkerCompleted += new System.ComponentModel.RunWorkerCompletedEventHandler(this.bgwShowAccount_RunWorkerCompleted);
            // 
            // tmrTimer
            // 
            this.tmrTimer.Interval = 1000;
            this.tmrTimer.Tick += new System.EventHandler(this.tmrTimer_Tick);
            // 
            // mNotifyIcon
            // 
            this.mNotifyIcon.ContextMenuStrip = this.mMenu;
            this.mNotifyIcon.Icon = ((System.Drawing.Icon)(resources.GetObject("mNotifyIcon.Icon")));
            this.mNotifyIcon.Text = "微信备份助手";
            this.mNotifyIcon.Visible = true;
            this.mNotifyIcon.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(this.mNotifyIcon_MouseDoubleClick);
            // 
            // mMenu
            // 
            this.mMenu.AccessibleDescription = "153x82";
            this.mMenu.DropShadowEnabled = false;
            this.mMenu.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.mMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tss4,
            this.tsmiCreateError,
            this.tss2,
            this.tsmiExist});
            this.mMenu.Name = "mMenu";
            this.mMenu.Size = new System.Drawing.Size(140, 60);
            this.mMenu.tbBackColor = System.Drawing.Color.White;
            this.mMenu.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.mMenu.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.mMenu.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tss4
            // 
            this.tss4.Name = "tss4";
            this.tss4.Size = new System.Drawing.Size(136, 6);
            // 
            // tsmiCreateError
            // 
            this.tsmiCreateError.Name = "tsmiCreateError";
            this.tsmiCreateError.Size = new System.Drawing.Size(139, 22);
            this.tsmiCreateError.Text = "错误日志(&E)";
            this.tsmiCreateError.Click += new System.EventHandler(this.tsmiCreateError_Click);
            // 
            // tss2
            // 
            this.tss2.Name = "tss2";
            this.tss2.Size = new System.Drawing.Size(136, 6);
            // 
            // tsmiExist
            // 
            this.tsmiExist.Name = "tsmiExist";
            this.tsmiExist.Size = new System.Drawing.Size(139, 22);
            this.tsmiExist.Text = "退出(&X)";
            this.tsmiExist.Click += new System.EventHandler(this.tsmiExist_Click);
            // 
            // tmrUpgrade
            // 
            this.tmrUpgrade.Interval = 500;
            this.tmrUpgrade.Tick += new System.EventHandler(this.tmrUpgrade_Tick);
            // 
            // munOperator
            // 
            this.munOperator.AccessibleDescription = "185x76";
            this.munOperator.DropShadowEnabled = false;
            this.munOperator.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.munOperator.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiOpenFolder,
            this.ToolStripSeparator1,
            this.tsmiOpen});
            this.munOperator.Name = "munSearch";
            this.munOperator.Size = new System.Drawing.Size(185, 54);
            this.munOperator.tbBackColor = System.Drawing.Color.White;
            this.munOperator.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.munOperator.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.munOperator.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiOpenFolder
            // 
            this.tsmiOpenFolder.Name = "tsmiOpenFolder";
            this.tsmiOpenFolder.Size = new System.Drawing.Size(184, 22);
            this.tsmiOpenFolder.Text = "打开文件所在的目录";
            this.tsmiOpenFolder.Click += new System.EventHandler(this.tsmiOpenFolder_Click);
            // 
            // ToolStripSeparator1
            // 
            this.ToolStripSeparator1.Name = "ToolStripSeparator1";
            this.ToolStripSeparator1.Size = new System.Drawing.Size(181, 6);
            // 
            // tsmiOpen
            // 
            this.tsmiOpen.Name = "tsmiOpen";
            this.tsmiOpen.Size = new System.Drawing.Size(184, 22);
            this.tsmiOpen.Text = "查看聊天记录";
            this.tsmiOpen.Click += new System.EventHandler(this.tsmiOpen_Click);
            // 
            // btn_normal
            // 
            this.btn_normal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_normal.BackColor = System.Drawing.Color.Transparent;
            this.btn_normal.BindingForm = null;
            this.btn_normal.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_normal.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_normal.Location = new System.Drawing.Point(2446, 6);
            this.btn_normal.Name = "btn_normal";
            this.btn_normal.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_normal.Selectable = true;
            this.btn_normal.Size = new System.Drawing.Size(24, 24);
            this.btn_normal.TabIndex = 48;
            this.btn_normal.tbAdriftIconWhenHover = false;
            this.btn_normal.tbAutoSize = false;
            this.btn_normal.tbAutoSizeEx = false;
            this.btn_normal.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_max;
            this.btn_normal.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_normal.tbBadgeNumber = 0;
            this.btn_normal.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_normal.tbDefaultText = "";
            this.btn_normal.tbEnableEnter = true;
            this.btn_normal.tbEndEllipsis = false;
            this.btn_normal.tbIconHoldPlace = true;
            this.btn_normal.tbIconImage = null;
            this.btn_normal.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btn_normal.tbIconMore = false;
            this.btn_normal.tbIconMouseDown = null;
            this.btn_normal.tbIconMouseHover = null;
            this.btn_normal.tbIconMouseLeave = null;
            this.btn_normal.tbIconPlaceText = 2;
            this.btn_normal.tbIconReadOnly = null;
            this.btn_normal.tbIconSize = new System.Drawing.Size(0, 0);
            this.btn_normal.tbImageMouseDown = null;
            this.btn_normal.tbImageMouseHover = null;
            this.btn_normal.tbImageMouseLeave = null;
            this.btn_normal.tbProgressValue = 50;
            this.btn_normal.tbReadOnly = false;
            this.btn_normal.tbReadOnlyText = false;
            this.btn_normal.tbShadow = false;
            this.btn_normal.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_normal.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_normal.tbShowDot = false;
            this.btn_normal.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_normal.tbShowMoreIconImg")));
            this.btn_normal.tbShowNew = false;
            this.btn_normal.tbShowProgress = false;
            this.btn_normal.tbShowTip = true;
            this.btn_normal.tbShowToolTipOnButton = false;
            this.btn_normal.tbSplit = "3,3,3,3";
            this.btn_normal.tbText = "";
            this.btn_normal.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.tbTextColor = System.Drawing.Color.White;
            this.btn_normal.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_normal.tbTextColorDown = System.Drawing.Color.White;
            this.btn_normal.tbTextColorHover = System.Drawing.Color.White;
            this.btn_normal.tbTextMouseDownPlace = 0;
            this.btn_normal.tbToolTip = "";
            this.btn_normal.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_normal.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_normal.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.tbUpToDownWhenCenter = false;
            this.btn_normal.TimerCountdownNum = 3;
            this.btn_normal.TimerInterval = 1;
            this.btn_normal.VisibleEx = true;
            // 
            // btn_close
            // 
            this.btn_close.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_close.BackColor = System.Drawing.Color.Transparent;
            this.btn_close.BindingForm = null;
            this.btn_close.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_close.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_close.Location = new System.Drawing.Point(2476, 5);
            this.btn_close.Name = "btn_close";
            this.btn_close.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_close.Selectable = true;
            this.btn_close.Size = new System.Drawing.Size(26, 26);
            this.btn_close.TabIndex = 47;
            this.btn_close.tbAdriftIconWhenHover = false;
            this.btn_close.tbAutoSize = true;
            this.btn_close.tbAutoSizeEx = false;
            this.btn_close.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_close;
            this.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbBadgeNumber = 0;
            this.btn_close.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_close.tbDefaultText = "";
            this.btn_close.tbEnableEnter = true;
            this.btn_close.tbEndEllipsis = false;
            this.btn_close.tbIconHoldPlace = true;
            this.btn_close.tbIconImage = null;
            this.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbIconMore = false;
            this.btn_close.tbIconMouseDown = null;
            this.btn_close.tbIconMouseHover = null;
            this.btn_close.tbIconMouseLeave = null;
            this.btn_close.tbIconPlaceText = 2;
            this.btn_close.tbIconReadOnly = null;
            this.btn_close.tbIconSize = new System.Drawing.Size(0, 0);
            this.btn_close.tbImageMouseDown = null;
            this.btn_close.tbImageMouseHover = null;
            this.btn_close.tbImageMouseLeave = null;
            this.btn_close.tbProgressValue = 50;
            this.btn_close.tbReadOnly = false;
            this.btn_close.tbReadOnlyText = false;
            this.btn_close.tbShadow = false;
            this.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_close.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_close.tbShowDot = false;
            this.btn_close.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_close.tbShowMoreIconImg")));
            this.btn_close.tbShowNew = false;
            this.btn_close.tbShowProgress = false;
            this.btn_close.tbShowTip = true;
            this.btn_close.tbShowToolTipOnButton = false;
            this.btn_close.tbSplit = "3,3,3,3";
            this.btn_close.tbText = "";
            this.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbTextColor = System.Drawing.Color.White;
            this.btn_close.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_close.tbTextColorDown = System.Drawing.Color.White;
            this.btn_close.tbTextColorHover = System.Drawing.Color.White;
            this.btn_close.tbTextMouseDownPlace = 0;
            this.btn_close.tbToolTip = "";
            this.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_close.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbUpToDownWhenCenter = false;
            this.btn_close.TimerCountdownNum = 3;
            this.btn_close.TimerInterval = 1;
            this.btn_close.VisibleEx = true;
            // 
            // btn_minimize
            // 
            this.btn_minimize.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_minimize.BackColor = System.Drawing.Color.Transparent;
            this.btn_minimize.BindingForm = null;
            this.btn_minimize.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_minimize.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_minimize.Location = new System.Drawing.Point(2414, 5);
            this.btn_minimize.Name = "btn_minimize";
            this.btn_minimize.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_minimize.Selectable = true;
            this.btn_minimize.Size = new System.Drawing.Size(26, 26);
            this.btn_minimize.TabIndex = 46;
            this.btn_minimize.tbAdriftIconWhenHover = false;
            this.btn_minimize.tbAutoSize = true;
            this.btn_minimize.tbAutoSizeEx = false;
            this.btn_minimize.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_min;
            this.btn_minimize.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_minimize.tbBadgeNumber = 0;
            this.btn_minimize.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_minimize.tbDefaultText = "";
            this.btn_minimize.tbEnableEnter = true;
            this.btn_minimize.tbEndEllipsis = false;
            this.btn_minimize.tbIconHoldPlace = true;
            this.btn_minimize.tbIconImage = null;
            this.btn_minimize.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btn_minimize.tbIconMore = false;
            this.btn_minimize.tbIconMouseDown = null;
            this.btn_minimize.tbIconMouseHover = null;
            this.btn_minimize.tbIconMouseLeave = null;
            this.btn_minimize.tbIconPlaceText = 2;
            this.btn_minimize.tbIconReadOnly = null;
            this.btn_minimize.tbIconSize = new System.Drawing.Size(0, 0);
            this.btn_minimize.tbImageMouseDown = null;
            this.btn_minimize.tbImageMouseHover = null;
            this.btn_minimize.tbImageMouseLeave = null;
            this.btn_minimize.tbProgressValue = 50;
            this.btn_minimize.tbReadOnly = false;
            this.btn_minimize.tbReadOnlyText = false;
            this.btn_minimize.tbShadow = false;
            this.btn_minimize.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_minimize.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_minimize.tbShowDot = false;
            this.btn_minimize.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_minimize.tbShowMoreIconImg")));
            this.btn_minimize.tbShowNew = false;
            this.btn_minimize.tbShowProgress = false;
            this.btn_minimize.tbShowTip = true;
            this.btn_minimize.tbShowToolTipOnButton = false;
            this.btn_minimize.tbSplit = "3,3,3,3";
            this.btn_minimize.tbText = "";
            this.btn_minimize.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.tbTextColor = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorDown = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorHover = System.Drawing.Color.White;
            this.btn_minimize.tbTextMouseDownPlace = 0;
            this.btn_minimize.tbToolTip = "";
            this.btn_minimize.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_minimize.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_minimize.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.tbUpToDownWhenCenter = false;
            this.btn_minimize.TimerCountdownNum = 3;
            this.btn_minimize.TimerInterval = 1;
            this.btn_minimize.VisibleEx = true;
            // 
            // btnToVideo
            // 
            this.btnToVideo.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnToVideo.BackColor = System.Drawing.Color.Transparent;
            this.btnToVideo.BindingForm = null;
            this.btnToVideo.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnToVideo.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnToVideo.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnToVideo.Location = new System.Drawing.Point(2223, 8);
            this.btnToVideo.Margin = new System.Windows.Forms.Padding(0);
            this.btnToVideo.Name = "btnToVideo";
            this.btnToVideo.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnToVideo.Selectable = true;
            this.btnToVideo.Size = new System.Drawing.Size(93, 23);
            this.btnToVideo.TabIndex = 79;
            this.btnToVideo.tbAdriftIconWhenHover = false;
            this.btnToVideo.tbAutoSize = false;
            this.btnToVideo.tbAutoSizeEx = false;
            this.btnToVideo.tbBackgroundImage = null;
            this.btnToVideo.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnToVideo.tbBadgeNumber = 0;
            this.btnToVideo.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnToVideo.tbDefaultText = "";
            this.btnToVideo.tbEnableEnter = true;
            this.btnToVideo.tbEndEllipsis = false;
            this.btnToVideo.tbIconHoldPlace = true;
            this.btnToVideo.tbIconImage = global::iWechatAssistant.Properties.Resources.btn_3_Video;
            this.btnToVideo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnToVideo.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnToVideo.tbIconMore = false;
            this.btnToVideo.tbIconMouseDown = null;
            this.btnToVideo.tbIconMouseHover = null;
            this.btnToVideo.tbIconMouseLeave = null;
            this.btnToVideo.tbIconPlaceText = 2;
            this.btnToVideo.tbIconReadOnly = null;
            this.btnToVideo.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnToVideo.tbImageMouseDown = null;
            this.btnToVideo.tbImageMouseHover = null;
            this.btnToVideo.tbImageMouseLeave = null;
            this.btnToVideo.tbProgressValue = 50;
            this.btnToVideo.tbReadOnly = false;
            this.btnToVideo.tbReadOnlyText = false;
            this.btnToVideo.tbShadow = false;
            this.btnToVideo.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnToVideo.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnToVideo.tbShowDot = false;
            this.btnToVideo.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnToVideo.tbShowMoreIconImg")));
            this.btnToVideo.tbShowNew = false;
            this.btnToVideo.tbShowProgress = false;
            this.btnToVideo.tbShowTip = true;
            this.btnToVideo.tbShowToolTipOnButton = false;
            this.btnToVideo.tbSplit = "0,0,0,0";
            this.btnToVideo.tbText = "视频教程";
            this.btnToVideo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnToVideo.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnToVideo.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnToVideo.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnToVideo.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnToVideo.tbTextMouseDownPlace = 2;
            this.btnToVideo.tbToolTip = "";
            this.btnToVideo.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnToVideo.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnToVideo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnToVideo.tbUpToDownWhenCenter = false;
            this.btnToVideo.TimerCountdownNum = 3;
            this.btnToVideo.TimerInterval = 1;
            this.btnToVideo.Visible = false;
            this.btnToVideo.VisibleEx = true;
            this.btnToVideo.Click += new System.EventHandler(this.btnToVideo_Click);
            // 
            // btnContactEx
            // 
            this.btnContactEx.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnContactEx.BackColor = System.Drawing.Color.Transparent;
            this.btnContactEx.BindingForm = null;
            this.btnContactEx.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnContactEx.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnContactEx.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.Location = new System.Drawing.Point(2320, 8);
            this.btnContactEx.Margin = new System.Windows.Forms.Padding(0);
            this.btnContactEx.Name = "btnContactEx";
            this.btnContactEx.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnContactEx.Selectable = true;
            this.btnContactEx.Size = new System.Drawing.Size(93, 23);
            this.btnContactEx.TabIndex = 80;
            this.btnContactEx.tbAdriftIconWhenHover = false;
            this.btnContactEx.tbAutoSize = false;
            this.btnContactEx.tbAutoSizeEx = false;
            this.btnContactEx.tbBackgroundImage = null;
            this.btnContactEx.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnContactEx.tbBadgeNumber = 0;
            this.btnContactEx.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnContactEx.tbDefaultText = "";
            this.btnContactEx.tbEnableEnter = true;
            this.btnContactEx.tbEndEllipsis = false;
            this.btnContactEx.tbIconHoldPlace = true;
            this.btnContactEx.tbIconImage = global::iWechatAssistant.Properties.Resources.btn_3_Contact;
            this.btnContactEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnContactEx.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnContactEx.tbIconMore = false;
            this.btnContactEx.tbIconMouseDown = null;
            this.btnContactEx.tbIconMouseHover = null;
            this.btnContactEx.tbIconMouseLeave = null;
            this.btnContactEx.tbIconPlaceText = 2;
            this.btnContactEx.tbIconReadOnly = null;
            this.btnContactEx.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnContactEx.tbImageMouseDown = null;
            this.btnContactEx.tbImageMouseHover = null;
            this.btnContactEx.tbImageMouseLeave = null;
            this.btnContactEx.tbProgressValue = 50;
            this.btnContactEx.tbReadOnly = false;
            this.btnContactEx.tbReadOnlyText = false;
            this.btnContactEx.tbShadow = false;
            this.btnContactEx.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnContactEx.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnContactEx.tbShowDot = false;
            this.btnContactEx.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnContactEx.tbShowMoreIconImg")));
            this.btnContactEx.tbShowNew = false;
            this.btnContactEx.tbShowProgress = false;
            this.btnContactEx.tbShowTip = true;
            this.btnContactEx.tbShowToolTipOnButton = false;
            this.btnContactEx.tbSplit = "0,0,0,0";
            this.btnContactEx.tbText = "联系客服";
            this.btnContactEx.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnContactEx.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.tbTextMouseDownPlace = 2;
            this.btnContactEx.tbToolTip = "";
            this.btnContactEx.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnContactEx.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnContactEx.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnContactEx.tbUpToDownWhenCenter = false;
            this.btnContactEx.TimerCountdownNum = 3;
            this.btnContactEx.TimerInterval = 1;
            this.btnContactEx.Visible = false;
            this.btnContactEx.VisibleEx = true;
            this.btnContactEx.Click += new System.EventHandler(this.btnContactEx_Click);
            // 
            // btnSetting
            // 
            this.btnSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSetting.BackColor = System.Drawing.Color.Transparent;
            this.btnSetting.BindingForm = null;
            this.btnSetting.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSetting.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSetting.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnSetting.Location = new System.Drawing.Point(2141, 8);
            this.btnSetting.Margin = new System.Windows.Forms.Padding(0);
            this.btnSetting.Name = "btnSetting";
            this.btnSetting.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnSetting.Selectable = true;
            this.btnSetting.Size = new System.Drawing.Size(67, 23);
            this.btnSetting.TabIndex = 79;
            this.btnSetting.tbAdriftIconWhenHover = false;
            this.btnSetting.tbAutoSize = false;
            this.btnSetting.tbAutoSizeEx = true;
            this.btnSetting.tbBackgroundImage = null;
            this.btnSetting.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnSetting.tbBadgeNumber = 0;
            this.btnSetting.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnSetting.tbDefaultText = "";
            this.btnSetting.tbEnableEnter = true;
            this.btnSetting.tbEndEllipsis = false;
            this.btnSetting.tbIconHoldPlace = true;
            this.btnSetting.tbIconImage = global::iWechatAssistant.Properties.Resources.btn_3_setting;
            this.btnSetting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSetting.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnSetting.tbIconMore = false;
            this.btnSetting.tbIconMouseDown = null;
            this.btnSetting.tbIconMouseHover = null;
            this.btnSetting.tbIconMouseLeave = null;
            this.btnSetting.tbIconPlaceText = 2;
            this.btnSetting.tbIconReadOnly = null;
            this.btnSetting.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnSetting.tbImageMouseDown = null;
            this.btnSetting.tbImageMouseHover = null;
            this.btnSetting.tbImageMouseLeave = null;
            this.btnSetting.tbProgressValue = 50;
            this.btnSetting.tbReadOnly = false;
            this.btnSetting.tbReadOnlyText = false;
            this.btnSetting.tbShadow = false;
            this.btnSetting.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnSetting.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnSetting.tbShowDot = false;
            this.btnSetting.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnSetting.tbShowMoreIconImg")));
            this.btnSetting.tbShowNew = false;
            this.btnSetting.tbShowProgress = false;
            this.btnSetting.tbShowTip = true;
            this.btnSetting.tbShowToolTipOnButton = false;
            this.btnSetting.tbSplit = "0,0,0,0";
            this.btnSetting.tbText = "设置";
            this.btnSetting.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnSetting.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnSetting.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnSetting.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnSetting.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnSetting.tbTextMouseDownPlace = 2;
            this.btnSetting.tbToolTip = "";
            this.btnSetting.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnSetting.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnSetting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnSetting.tbUpToDownWhenCenter = false;
            this.btnSetting.TimerCountdownNum = 3;
            this.btnSetting.TimerInterval = 1;
            this.btnSetting.VisibleEx = true;
            this.btnSetting.Click += new System.EventHandler(this.btnSetting_Click);
            // 
            // btnFeedback
            // 
            this.btnFeedback.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnFeedback.BackColor = System.Drawing.Color.Transparent;
            this.btnFeedback.BindingForm = null;
            this.btnFeedback.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnFeedback.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnFeedback.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnFeedback.Location = new System.Drawing.Point(2035, 8);
            this.btnFeedback.Margin = new System.Windows.Forms.Padding(0);
            this.btnFeedback.Name = "btnFeedback";
            this.btnFeedback.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnFeedback.Selectable = true;
            this.btnFeedback.Size = new System.Drawing.Size(93, 23);
            this.btnFeedback.TabIndex = 85;
            this.btnFeedback.tbAdriftIconWhenHover = false;
            this.btnFeedback.tbAutoSize = false;
            this.btnFeedback.tbAutoSizeEx = false;
            this.btnFeedback.tbBackgroundImage = null;
            this.btnFeedback.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnFeedback.tbBadgeNumber = 0;
            this.btnFeedback.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnFeedback.tbDefaultText = "";
            this.btnFeedback.tbEnableEnter = true;
            this.btnFeedback.tbEndEllipsis = false;
            this.btnFeedback.tbIconHoldPlace = true;
            this.btnFeedback.tbIconImage = global::iWechatAssistant.Properties.Resources.btn_feedback;
            this.btnFeedback.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnFeedback.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnFeedback.tbIconMore = false;
            this.btnFeedback.tbIconMouseDown = null;
            this.btnFeedback.tbIconMouseHover = null;
            this.btnFeedback.tbIconMouseLeave = null;
            this.btnFeedback.tbIconPlaceText = 2;
            this.btnFeedback.tbIconReadOnly = null;
            this.btnFeedback.tbIconSize = new System.Drawing.Size(0, 0);
            this.btnFeedback.tbImageMouseDown = null;
            this.btnFeedback.tbImageMouseHover = null;
            this.btnFeedback.tbImageMouseLeave = null;
            this.btnFeedback.tbProgressValue = 50;
            this.btnFeedback.tbReadOnly = false;
            this.btnFeedback.tbReadOnlyText = false;
            this.btnFeedback.tbShadow = false;
            this.btnFeedback.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnFeedback.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnFeedback.tbShowDot = false;
            this.btnFeedback.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnFeedback.tbShowMoreIconImg")));
            this.btnFeedback.tbShowNew = false;
            this.btnFeedback.tbShowProgress = false;
            this.btnFeedback.tbShowTip = true;
            this.btnFeedback.tbShowToolTipOnButton = false;
            this.btnFeedback.tbSplit = "0,0,0,0";
            this.btnFeedback.tbText = "问题反馈";
            this.btnFeedback.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnFeedback.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnFeedback.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnFeedback.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnFeedback.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnFeedback.tbTextMouseDownPlace = 2;
            this.btnFeedback.tbToolTip = "";
            this.btnFeedback.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnFeedback.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnFeedback.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnFeedback.tbUpToDownWhenCenter = false;
            this.btnFeedback.TimerCountdownNum = 3;
            this.btnFeedback.TimerInterval = 1;
            this.btnFeedback.VisibleEx = true;
            this.btnFeedback.Click += new System.EventHandler(this.btnFeedback_Click);
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(2507, 723);
            this.Controls.Add(this.btnFeedback);
            this.Controls.Add(this.txtSearchFriend);
            this.Controls.Add(this.btnPCPath);
            this.Controls.Add(this.btnContactEx);
            this.Controls.Add(this.lblCurrentAccount);
            this.Controls.Add(this.btnSetting);
            this.Controls.Add(this.btnToVideo);
            this.Controls.Add(this.btn_normal);
            this.Controls.Add(this.btn_close);
            this.Controls.Add(this.btn_minimize);
            this.Controls.Add(this.tblayoutMain);
            this.ForeColor = System.Drawing.SystemColors.ControlText;
            this.MinimumSize = new System.Drawing.Size(970, 650);
            this.Name = "MainForm";
            this.tbAutoSetFormSize = true;
            this.tbGuiBackground = global::iWechatAssistant.Properties.Resources.frm_bg_sub;
            this.tbShowTitleOnForm = true;
            this.tbSplit = "6,39,7,28";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.tbTitleForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.tbTitleLocation = new System.Drawing.Point(4, 10);
            this.Text = "微信备份助手";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.MainForm_FormClosing);
            this.tblayoutMain.ResumeLayout(false);
            this.tbPanel2.ResumeLayout(false);
            this.pnlAccredit.ResumeLayout(false);
            this.pnlAccredit.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox3)).EndInit();
            this.pnlzzsq.ResumeLayout(false);
            this.pnlzzsq.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox7)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pbArtificialAccredit)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox9)).EndInit();
            this.pnlrgsq.ResumeLayout(false);
            this.pnlrgsq.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox6)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox5)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox4)).EndInit();
            this.pnlWelcome.ResumeLayout(false);
            this.pnlWelcome.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pbRecommend)).EndInit();
            this.pnlLoad.ResumeLayout(false);
            this.pnlLoad.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pbMsg)).EndInit();
            this.pnlWechatList.ResumeLayout(false);
            this.tbPanel17.ResumeLayout(false);
            this.tbPanel17.PerformLayout();
            this.tbPanel15.ResumeLayout(false);
            this.tbPanel12.ResumeLayout(false);
            this.plnShowChat.ResumeLayout(false);
            this.tbPanel5.ResumeLayout(false);
            this.tbPanel3.ResumeLayout(false);
            this.pnlPage.ResumeLayout(false);
            this.pnlTitle.ResumeLayout(false);
            this.pnlTitle.PerformLayout();
            this.pnlLoadChat.ResumeLayout(false);
            this.pnlLoadChat.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.pnlFriend.ResumeLayout(false);
            this.tbPanel7.ResumeLayout(false);
            this.tbPanel1.ResumeLayout(false);
            this.tbPanel6.ResumeLayout(false);
            this.pnlTop.ResumeLayout(false);
            this.pnlTop.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picTimeScreen)).EndInit();
            this.cmsExportNews.ResumeLayout(false);
            this.mMenu.ResumeLayout(false);
            this.munOperator.ResumeLayout(false);
            this.ResumeLayout(false);

        }
        

        #endregion

        internal System.Windows.Forms.TableLayoutPanel tblayoutMain;
        internal iTong.Components.tbPanel plnShowChat;
        internal iTong.Components.tbPanel pnlFriend;
        internal iTong.Components.tbPanel pnlWelcome;
        internal iTong.Components.tbPanel pnlWechatList;
        private iTong.Components.tbButton btnStart;
        private iTong.Components.tbLabel lblWelcone;
        private System.Windows.Forms.Label lblWechatPath;
        private System.Windows.Forms.TextBox txtWechatPath;
        private iTong.Components.tbButton btnWechatPath;
        internal iTong.Components.tbPanel pnlWechatAccounts;
        private iTong.Components.tbButton btnRefreshAccount;
        private iTong.Components.tbPanel pnlTop;
        internal iTong.Components.tbButton btnWeCahtUsers;
        internal iTong.Components.tbLabel lblCurrentAccount;
        private iTong.Components.tbPanel pnlLoad;
        private System.Windows.Forms.Label lblMsg;
        internal iTong.Components.tbContextMenuStrip cmsExportNews;
        private System.Windows.Forms.Panel pnlAccredit;
        private System.Windows.Forms.RichTextBox rtxtAccredit;
        private iTong.Components.tbButton btnAccredit;
        private iTong.Components.tbPanel tbPanel1;
        internal iTong.Components.tbSearch txtSearchFriend;
        private iTong.Components.tbPanel pnlLoadChat;
        private System.Windows.Forms.PictureBox pictureBox1;
        private System.Windows.Forms.Label lblLoading;
        private System.Windows.Forms.Label lblState;
        private System.Windows.Forms.LinkLabel llblBackupCourse;
        private System.Windows.Forms.RichTextBox richTextBox3;
        private System.Windows.Forms.RichTextBox richTextBox2;
        private System.ComponentModel.BackgroundWorker bgwShowAccount;
        internal iTong.Components.tbLabel lblSystemInfo;
        private System.Windows.Forms.Timer tmrTimer;
        internal System.Windows.Forms.NotifyIcon mNotifyIcon;
        internal System.Windows.Forms.Timer tmrUpgrade;
        internal iTong.Components.tbContextMenuStrip munOperator;
        internal System.Windows.Forms.ToolStripMenuItem tsmiOpenFolder;
        internal System.Windows.Forms.ToolStripSeparator ToolStripSeparator1;
        internal System.Windows.Forms.ToolStripMenuItem tsmiOpen;
        private iTong.Components.tbPanel tbPanel3;
        private iTong.Components.tbPanel tbPanel4;
        internal iTong.Components.tbContextMenuStrip mMenu;
        internal System.Windows.Forms.ToolStripSeparator tss4;
        internal System.Windows.Forms.ToolStripMenuItem tsmiCreateError;
        internal System.Windows.Forms.ToolStripSeparator tss2;
        internal System.Windows.Forms.ToolStripMenuItem tsmiExist;
        private iTong.Components.tbButton btnPCPath;
        internal iTong.Components.tbPanel pnlTitle;
        internal iTong.Components.tbPanel TbPanel8;
        internal iTong.Components.tbButton btnTimeSelect;
        internal iTong.Components.tbButton btnOK;
        internal System.Windows.Forms.DateTimePicker dtpStart;
        internal System.Windows.Forms.Label lblStartTime;
        internal System.Windows.Forms.DateTimePicker dtpEnd;
        internal System.Windows.Forms.Label lblEndTime;
        private System.Windows.Forms.ToolStripMenuItem 按格式导出ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem tsmiHtml;
        private System.Windows.Forms.ToolStripMenuItem 按媒体导出ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem tsmiExcelEx;
        private System.Windows.Forms.ToolStripMenuItem tsmiTxtEx;
        private System.Windows.Forms.ToolStripMenuItem tsmiPicture;
        private System.Windows.Forms.ToolStripMenuItem tsmiVoice;
        private System.Windows.Forms.ToolStripMenuItem tsmiVideo;
        private System.Windows.Forms.ToolStripMenuItem tsmiAll;
        protected iTong.Components.tbButton btn_normal;
        internal iTong.Components.tbButton btn_close;
        internal iTong.Components.tbButton btn_minimize;
        private iTong.Components.tbPanel tbPanel5;
        private iTong.Components.tbPanel tbPanel6;
        internal iTong.Components.tbButton btnSuperior;
        internal iTong.Components.tbButton btnGetBack;
        internal iTong.Components.tbButton btnReturnAccredit;
        internal iTong.Components.tbButton btnNewExport;
        internal iTong.Components.tbButton btnExport;
        internal iTong.Components.tbCheckBox cbxSelectAll;
        internal iTong.Components.tbPanel tbPanel7;
        internal iTong.Components.tbLabel lblLinkmanMsg;
        internal iTong.Components.tbPanel tbPanel10;
        internal iTong.Components.tbPanel tbPanel9;
        internal iTong.Components.tbPanel tbPanel11;
        internal iTong.Components.tbButton btnToVideo;
        internal iTong.Components.tbButton btnContactEx;
        private iTong.Components.tbPanel tbPanel12;
        private iTong.Components.tbPanel tbPanel2;
        internal iTong.Components.tbPanel tbPanel13;
        internal iTong.Components.tbPanel tbPanel14;
        internal iTong.Components.tbPanel tbPanel17;
        internal iTong.Components.tbPanel tbPanel19;
        private System.Windows.Forms.PictureBox pbMsg;
        private System.Windows.Forms.LinkLabel llblPCPath;
        private System.Windows.Forms.Label label2;
        private iTong.Components.tbPanel tbPanel15;
        internal iTong.Components.tbButton btnSetting;
        private iTong.Components.tbLabel tbLabel3;
        internal iTong.Components.tbSearch txtSearchChat;
        internal System.Windows.Forms.PictureBox picTimeScreen;
        internal iTong.Components.tbButton btnTimeScreen;
        private System.Windows.Forms.ToolStripMenuItem tsmiFriend;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private iTong.Components.tbButton btnzzsq;
        private iTong.Components.tbPanel pnlzzsq;
        private iTong.Components.tbPanel pnlrgsq;
        private System.Windows.Forms.RichTextBox rtxtAccreditEx;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.PictureBox pictureBox3;
        private System.Windows.Forms.Label label8;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.Label label6;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.PictureBox pictureBox6;
        private System.Windows.Forms.PictureBox pictureBox5;
        private System.Windows.Forms.PictureBox pictureBox4;
        private iTong.Components.tbButton btnContact;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.Label lblArtificialAccredit;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.Label label12;
        private System.Windows.Forms.PictureBox pictureBox7;
        private System.Windows.Forms.PictureBox pbArtificialAccredit;
        private System.Windows.Forms.PictureBox pictureBox9;
        private iTong.Components.tbButton btnrgsq;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label13;
        private System.Windows.Forms.Label label15;
        internal iTong.Components.tbButton btnUpdateFriend;
        private tbBattery lblRegister;
        private System.Windows.Forms.LinkLabel llblExportPreview;
        private tbBattery lblRecommend;
        private System.Windows.Forms.PictureBox pbRecommend;
        private tbBattery tbbRecommend;
        internal iTong.Components.tbButton btnFeedback;
        internal iTong.Components.tbPanel pnlPage;
        private zComponents.tbPage tbpPage;
        internal iTong.Components.tbPanel tbPanel18;
        private iTong.Components.tbButton tbButton1;
        internal System.Windows.Forms.DateTimePicker dtpStartTime;
        internal System.Windows.Forms.DateTimePicker dtpEndTime;
        internal iTong.Components.tbCheckBox chkLock;
    }
}

