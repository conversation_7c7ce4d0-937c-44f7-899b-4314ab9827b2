﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading;

namespace iWechatAssistant
{
    // 用于读数据库
    public class WechatTalkNew
    {
        public static void SaveWechatTalk(Dictionary<string, WechatTalk> dictMasts, WechatTalk talk, WechatAccInfo waInfo,
                                          WechatMessageMast weMast = null)
        {
            try
            {
                WechatTalk t = talk.Clone();
                WechatMessageItem[] aryMsgItems = null;
                int nCount = 0;
                if (weMast != null)
                {
                    aryMsgItems = weMast.MessagegItemList.ToArray();
                    nCount = aryMsgItems.Length;
                    if (nCount > 0)
                    {
                        t.LastChatText = aryMsgItems[nCount - 1].MessageContent;
                        t.LastWechatMessageItem = aryMsgItems[nCount - 1];
                    }

                    weMast.MessagegItemList.Clear();
                }

                WechatTalk talkResult = null;
                if (dictMasts.ContainsKey(t.Md5))
                {
                    talkResult = dictMasts[t.Md5];

                    if (DateTime.Compare(talkResult.StartTime, t.StartTime) > 0)
                    {
                        talkResult.StartTime = t.StartTime;
                    }
                    if (DateTime.Compare(talkResult.EndTime, t.EndTime) < 0)
                    {
                        talkResult.EndTime = t.EndTime;
                        talkResult.LastChatText = t.LastChatText;
                    }
                    dictMasts.Remove(t.Md5);
                }

                if (talkResult == null)
                    dictMasts.Add(talk.Md5, t);
                else
                    dictMasts.Add(talk.Md5, talkResult);

                if (weMast != null)
                {
                    List<WechatMessageItem> listTemp = new List<WechatMessageItem>();
                    for (int i = nCount - 1; i >= 0; i--)
                    {
                        listTemp.Add(aryMsgItems[i]);
                    }

                    if (weMast != null)
                    {
                        WeChatTalkDB db = WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath);
                        if (db != null)
                        {
                            db.InsertTalk(t.Md5, listTemp);
                            db.Commit();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatTalkNew====>SaveWechatTalk()");
            }
        }

        public static WechatMessageMast LoadMessagesFromFile(byte[] accountPwds, int length,
                                                      string filePath, int offset)
        {
            WechatMessageMast wemast = null;
            try
            {
                byte[] aryBuffers = new byte[length];
                using (FileStream fs = new FileStream(filePath, FileMode.Open))
                {
                    fs.Seek(offset, SeekOrigin.Begin);
                    fs.Read(aryBuffers, 0, length);
                }
                //解密
                byte[] bufferUnDecrypt = AESFunctionHelper.AES_decrypt(aryBuffers, accountPwds);
                if (bufferUnDecrypt != null)
                {
                    //解析ProtoBuf数据
                    using (MemoryStream ms = new MemoryStream(bufferUnDecrypt))
                    {
                        //ms.Write(bufferUnDecrypt, 0, bufferUnDecrypt.Length);
                        //ms.Position = 0;

                        wemast = ProtoBuf.Serializer.Deserialize<WechatMessageMast>(ms);

                    }
                }

            }
            catch (Exception ex)
            {
                wemast = null;
                Common.LogException(ex.ToString(), "WechatTalkNew's LoadMessageFromFile");
            }

            return wemast;
        }

        public static List<WechatMessageItem> GetWechatMessages(WechatTalk talk, WechatAccInfo waInfo,
                                                                List<DateTime> listDateTimes, int startIndex)
        {
            List<WechatMessageItem> listMsgs = new List<WechatMessageItem>();
            Dictionary<string, WechatTalk> dictMessage = new Dictionary<string, WechatTalk>(StringComparer.InvariantCultureIgnoreCase);
            int nCount = 0;

            WechatMessageMast weMast = LoadMessagesFromFile(waInfo.PwdBuffer, talk.Length, talk.FilePath, talk.OffSet);
            SaveWechatTalk(dictMessage, talk, waInfo, weMast);

            nCount = weMast.MessagegItemCount;
            talk.LastChatText = dictMessage[talk.Md5].LastChatText;
            talk.LastWechatMessageItem = dictMessage[talk.Md5].LastWechatMessageItem;

            if (talk.Tag != null)
            {
                foreach (WechatTalk t in talk.Tag)
                {
                    weMast = LoadMessagesFromFile(waInfo.PwdBuffer, t.Length, t.FilePath, t.OffSet);
                    nCount += weMast.MessagegItemCount;
                    SaveWechatTalk(dictMessage, t, waInfo, weMast);
                }
            }

            talk.PageTotal = Math.Ceiling(Convert.ToDouble(nCount / talk.PageCount));
            listMsgs = WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).GetTalkInfo(talk.Md5, talk.NickName, listDateTimes,
                                                                                      talk.SearchText, startIndex, (int)talk.PageCount);

            return listMsgs;
        }
    }

    public class WechatTalk
    {
        private string mFilePath = string.Empty;
        private string mMd5 = string.Empty;
        private string mUserName = string.Empty;
        private string mNickName = string.Empty;
        private string mTalkId = string.Empty;
        private string mSearchText = string.Empty;
        private string mLastChatText = string.Empty;
        private int mOffSet = -1;
        private int mLength = -1;
        private double mPageCount = 20;
        private double mPageTotal = 0;
        private double mPageIndex = 0;
        private DateTime mStartTime = DateTime.MinValue;
        private DateTime mEndTime = DateTime.MinValue;
        private WechatMessageMast mMessageMast = null;
        private WechatMessageItem mLastWechatMessageItem = null;
        private List<WechatMessageItem> mMessageItems = new List<WechatMessageItem>();

        /// <summary>
        /// 每页条数
        /// </summary>
        public double PageCount
        {
            get { return this.mPageCount; }
            set { this.mPageCount = value; }
        }

        /// <summary>
        /// 总页数
        /// </summary>
        public double PageTotal
        {
            get { return this.mPageTotal; }
            set { this.mPageTotal = value; }
        }

        /// <summary>
        /// 当前页索引
        /// </summary>
        public double PageIndex
        {
            get { return mPageIndex; }
            set { mPageIndex = value; }
        }

        public string FilePath
        {
            get
            {
                return this.mFilePath;
            }
            set
            {
                this.mFilePath = value;
            }
        }

        public string Md5
        {
            get
            {
                return this.mMd5;
            }
            set
            {
                this.mMd5 = value;
            }
        }

        public string UserName
        {
            get
            {
                return this.mUserName;
            }
            set
            {
                this.mUserName = value;
            }
        }

        public string NickName
        {
            get
            {
                return this.mNickName;
            }
            set
            {
                this.mNickName = value;
            }
        }

        public string TalkId
        {
            get
            {
                return this.mTalkId;
            }
            set
            {
                this.mTalkId = value;
            }
        }

        public DateTime StartTime
        {
            get
            {
                return this.mStartTime;
            }
            set
            {
                this.mStartTime = value;
            }
        }

        public DateTime EndTime
        {
            get
            {
                return this.mEndTime;
            }
            set
            {
                this.mEndTime = value;
            }
        }

        public int OffSet
        {
            get
            {
                return this.mOffSet;
            }
            set
            {
                this.mOffSet = value;
            }
        }

        public List<WechatMessageItem> MessageItems
        {
            get
            {
                if (mMessageItems.Count == 0 && this.mMessageMast != null && this.mMessageMast.MessagegItemList != null)
                {
                    try
                    {
                        mPageTotal = Math.Ceiling(Convert.ToDouble(this.mMessageMast.MessagegItemList.Count / PageCount));
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "MessageItems_2");
                    }
                    return this.mMessageMast.MessagegItemList;
                }
                return mMessageItems;
            }
            set
            {
                this.mMessageItems = value;
                try
                {
                    mPageTotal = Math.Ceiling(Convert.ToDouble(this.mMessageItems.Count / PageCount));
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "MessageItems_1");
                }
            }
        }

        public string LastChatText
        {
            get
            {
                return this.mLastChatText;
            }
            set
            {
                this.mLastChatText = value;
            }
        }

        public WechatMessageItem LastWechatMessageItem
        {
            get
            {
                return this.mLastWechatMessageItem;
            }
            set
            {
                this.mLastWechatMessageItem = value;
            }
        }

        public string SearchText
        {
            get { return mSearchText; }
            set { mSearchText = value; }
        }

        public int Length { get; set; }
        public List<WechatTalk> Tag { get; set; }

        public WechatTalk Clone()
        {
            WechatTalk wTalk = new WechatTalk();
            wTalk.PageIndex = this.PageIndex;
            wTalk.EndTime = this.EndTime;
            wTalk.FilePath = this.FilePath;
            wTalk.LastChatText = this.LastChatText;
            wTalk.Md5 = this.Md5;
            wTalk.NickName = this.NickName;
            wTalk.OffSet = this.OffSet;
            wTalk.PageTotal = this.PageTotal;
            wTalk.PageCount = this.PageCount;
            wTalk.StartTime = this.StartTime;
            wTalk.TalkId = this.TalkId;
            wTalk.UserName = this.UserName;
            wTalk.Length = this.Length;
            wTalk.SearchText = this.SearchText;
            wTalk.MessageItems = this.MessageItems;

            return wTalk;
        }

        public bool LoadMessagesFromFile(byte[] accountPwds, int length, string wixId, bool isSaveDb, WeChatTalkDB db,string iniPath)
        {
            bool ret = true;
            try
            {
                byte[] aryBuffers = new byte[length];
                using (FileStream fs = new FileStream(this.FilePath, FileMode.Open, FileAccess.Read, FileShare.ReadWrite))
                {
                    fs.Seek(this.OffSet, SeekOrigin.Begin);
                    fs.Read(aryBuffers, 0, length);
                }

                //解密
                byte[] bufferUnDecrypt = AESFunctionHelper.AES_decrypt(aryBuffers, accountPwds);
                if (bufferUnDecrypt != null)
                {
                    //解析ProtoBuf数据
                    using (MemoryStream ms = new MemoryStream())
                    {
                        try
                        {
                            ms.Write(bufferUnDecrypt, 0, bufferUnDecrypt.Length);
                            ms.Position = 0;

                            var messageMast = ProtoBuf.Serializer.Deserialize<WechatMessageMast>(ms);

                            if (isSaveDb)
                            {
                                if (messageMast.MessagegItemList.Count > 0)
                                {
                                    LastChatText = messageMast.MessagegItemList[messageMast.MessagegItemList.Count - 1].MessageContent;
                                    LastWechatMessageItem = messageMast.MessagegItemList[messageMast.MessagegItemList.Count - 1];
                                }
                                long time = iTong.CoreModule.IniSetting.GetTalkMessageTimeSpan(wixId, UserName, Path.Combine(iniPath, "Wix.ini"));

                                List<WechatMessageItem> dbItems = new List<WechatMessageItem>();

                                foreach (WechatMessageItem item in messageMast.MessagegItemList)
                                {
                                    if (item.MessageDateTime > time)
                                    {
                                        dbItems.Add(item);
                                    }
                                }

                                if (dbItems.Count > 0)
                                    db.InsertTalk(Md5, dbItems);

                                return ret;
                            }

                            this.mMessageMast = messageMast;
                            if (this.MessageItems != null)
                            {
                                // Modified by Utmost on 2020.06.02

                                // 优化：以下代码仅需取一次属性值                                
                                WechatMessageItem[] aryMsgItems = MessageItems.ToArray();
                                int nCount = aryMsgItems.Length;
                                if (nCount > 0)
                                {
                                    LastChatText = aryMsgItems[nCount - 1].MessageContent;
                                    LastWechatMessageItem = aryMsgItems[nCount - 1];
                                }

                                // 以下代码需取三次属性值                                
                                //int nCount = MessageItems.Count;
                                //if (nCount > 0)
                                //{
                                //    this.LastChatText = this.MessageItems[nCount - 1].MessageContent;
                                //    this.LastWechatMessageItem = this.MessageItems[nCount - 1];
                                //}
                            }
                            // 输出protobuf转化的数据
                            this.LogMessageItems(ms);
                        }
                        catch (Exception ex2)
                        {
                            Common.LogException(ex2.ToString(), "WechatTalk_LoadMessageFromFile2");
                            ret = false;
                        }
                    }
                }
                aryBuffers = null;
                bufferUnDecrypt = null;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatTalk_LoadMessageFromFile");
                ret = false;
            }

            return ret;
        }

        private void LogMessageItems(MemoryStream ms)
        {
            if (!File.Exists(Path.Combine(Folder.AppFolder, "shuchu.dll")))
            {
                return;
            }

            string strDirectory = Path.Combine(Folder.AppFolder, "shuchu");
            if (!Directory.Exists(strDirectory))
            {
                Directory.CreateDirectory(strDirectory);
            }
            strDirectory = Path.Combine(strDirectory, Utility.ReplaceWinIllegalName(mUserName + " " + mNickName));
            if (!Directory.Exists(strDirectory))
            {
                Directory.CreateDirectory(strDirectory);
            }

            string strResultPath = Path.Combine(strDirectory, mTalkId.ToString());
            int i = 0;
            while (File.Exists(strResultPath))
            {
                i += 1;
                strResultPath = Path.Combine(strDirectory, mTalkId.ToString() + i.ToString());
            }

            FileStream fsA = new FileStream(strResultPath, FileMode.Create);
            fsA.Write(ms.GetBuffer(), 0, ms.GetBuffer().Length);
            fsA.Close();
        }

    }

    //public class WechatMessageItemTimeComparer : IComparer<WechatMessageItem>
    //{
    //    private SortType type = SortType.ASC;
    //    public WechatMessageItemTimeComparer(SortType tempType)
    //    {
    //        type = tempType;
    //    }

    //    public int Compare(WechatMessageItem x, WechatMessageItem y)
    //    {
    //        int result = 0;

    //        if (type == SortType.ASC)
    //        {
    //            result = x.MessageDT.CompareTo(y.MessageDT);
    //        }
    //        else
    //        {
    //            result = y.MessageDT.CompareTo(x.MessageDT);
    //        }

    //        return result;
    //    }

    //}
}
