﻿using iTong.CoreFoundation;
using iTong.CoreModule;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite3;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace iWechatAssistant
{
    public class WeChatTalkDB
    {
        private SQLiteConnection mConn = null;
        private string strNoVlaue = "NoValue";
        private bool mLoaded = false;
        private int mUnCommitCount = 0;
        private SQLiteTransaction mTran = null;
        private Queue<KeyValuePair<string, List<WechatMessageItem>>> mQueueMessages = new Queue<KeyValuePair<string, List<WechatMessageItem>>>();
        private Thread mThrInsertToDB = null;
        private bool mIsInserting = false;

        #region --- 属性 ---

        public bool Loaded
        {
            get { return this.mLoaded; }

            set { this.mLoaded = value; }
        }


        #endregion

        #region --- 单实例 ---

        private static Dictionary<string, WeChatTalkDB> lstInstance = new Dictionary<string, WeChatTalkDB>(StringComparer.InvariantCultureIgnoreCase);
        private static readonly object locker = new object();

        public static WeChatTalkDB GetInstance(string strDBFilePath)
        {
            WeChatTalkDB instance = null;
            lock (locker)
            {
                if (lstInstance.ContainsKey(strDBFilePath))
                    instance = lstInstance[strDBFilePath];
                else
                {
                    instance = new WeChatTalkDB(strDBFilePath);
                    lstInstance[strDBFilePath] = instance;
                }
            }
            return instance;
        }

        #endregion

        #region --- 构造函数 ---

        private WeChatTalkDB(string strDBFilePath)
        {
            this.Reload(strDBFilePath);
        }

        public void Dispose()
        {
            try
            {
                if (this.mConn != null && this.mConn.State == ConnectionState.Open)
                {
                    this.mConn.Close();
                }
            }
            catch
            { }
        }

        #endregion

        #region --- 初始化 ---

        public bool Reload(string strDBFilePath)
        {
            bool result = false;
            try
            {
                if (!File.Exists(strDBFilePath))
                {
                    this.CreateDB(strDBFilePath);
                }

                if (this.mConn != null && this.mConn.State == System.Data.ConnectionState.Open)
                {
                    this.mConn.Close();
                }

                this.mConn = SQLiteClass3.CreateConnectionFromFile(strDBFilePath);

                result = true;
            }
            catch
            { }

            return result;
        }

        private bool CreateDB(string path)
        {
            bool result = true;
            Folder.CheckFolder(Path.GetDirectoryName(path));
            SQLiteConnection conn = SQLiteClass3.CreateConnectionFromFile(path);
            try
            {
                result = result && this.CreateTable_DBInfo(conn);

                //result = result && this.CreateTable_Messages(conn);
                //result = result && this.CreateTable_MediaFiles(conn);
                result = result && this.CreateTable_AndroidVoices(conn);

                Insert_DBInfo_Version(new Version("1.0"), conn);
            }
            catch
            {
                Common.LogException("创建数据库失败，请重试！");
                result = false;
            }
            finally
            {
                conn.Close();
            }

            return result;
        }

        private bool CreateTable_DBInfo(SQLiteConnection conn)
        {
            string sql = @"
Drop Table IF Exists [DBInfo];
CREATE TABLE [DBInfo] ( 
[keystate] INTEGER,
[value] TEXT
);
";

            try
            {
                string errmsg = string.Empty;
                SQLiteClass3.ExecuteNoneQuery(sql, conn, null, null, ref errmsg);
                return string.IsNullOrEmpty(errmsg);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateTable_DBInfo");
                return false;
            }
        }

        public bool CreateTable_Messages(string md5, int index)
        {
            string sql = string.Format(@"Drop Table IF Exists [{0}];
                                            CREATE TABLE [{0}] ( 
                                            [ROWID] INTEGER PRIMARY KEY AUTOINCREMENT
                                            ,[TableMD5] TEXT
                                            ,[MessageType] INTEGER
                                            ,[MessageId] TEXT
                                            ,[SendId] TEXT
                                            ,[ReceiveId] TEXT
                                            ,[MessageContent] TEXT
                                            ,[Status] INTEGER
                                            ,[MessageDateTime] bigint
                                            ,[MediaCacheThumbPath] TEXT
                                            ,[MediaCacheBigPath] TEXT
                                            ,[MessageDT] DATETIME
                                            ,[SociaChatType] TEXT
                                            ,[MediaFileName] TEXT
                                            );                                          
                                         CREATE INDEX mtd{1} on [{0}] (MessageDateTime DESC);
                                            ", "Messages" + md5, index);
            List<string> MediaFileName;
            List<WechatMessageAndroidVoiceMsg> AndroidVoiceMsgList;
            try
            {
                sql = string.Format(sql, strNoVlaue);
                string errmsg = string.Empty;
                SQLiteClass3.ExecuteNoneQuery(sql, mConn, null, null, ref errmsg);
                return string.IsNullOrEmpty(errmsg);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateTable_Messages");
                return false;
            }
        }

        public bool CreateTable_MediaFiles(string md5)
        {
            string sql = string.Format(@"Drop Table IF Exists [{0}];
                                            CREATE TABLE [{0}] ( 
                                            [ROWID] INTEGER PRIMARY KEY AUTOINCREMENT  
                                            ,[TableMD5] TEXT                                          
                                            ,[MessageId] TEXT
                                            ,[MediaFileName] TEXT                                          
                                            );
                                            ", "MediaFiles" + md5);
            List<string> MediaFileName;
            List<WechatMessageAndroidVoiceMsg> AndroidVoiceMsgList;
            try
            {
                sql = string.Format(sql, strNoVlaue);
                string errmsg = string.Empty;
                SQLiteClass3.ExecuteNoneQuery(sql, mConn, null, null, ref errmsg);
                return string.IsNullOrEmpty(errmsg);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateTable_MediaFiles");
                return false;
            }
        }

        //private bool CreateTable_Messages(SQLiteConnection conn)
        //{
        //    string sql = string.Format(@"Drop Table IF Exists [{0}];
        //                                    CREATE TABLE [{0}] ( 
        //                                    [ROWID] INTEGER PRIMARY KEY AUTOINCREMENT
        //                                    ,[TableMD5] TEXT
        //                                    ,[MessageType] INTEGER
        //                                    ,[MessageId] TEXT
        //                                    ,[SendId] TEXT
        //                                    ,[ReceiveId] TEXT
        //                                    ,[MessageContent] TEXT
        //                                    ,[Status] INTEGER
        //                                    ,[MessageDateTime] bigint
        //                                    ,[MediaCacheThumbPath] TEXT
        //                                    ,[MediaCacheBigPath] TEXT
        //                                    ,[MessageDT] DATETIME
        //                                    ,[SociaChatType] TEXT
        //                                    );
        //                                    ", "Messages");
        //    List<string> MediaFileName;
        //    List<WechatMessageAndroidVoiceMsg> AndroidVoiceMsgList;
        //    try
        //    {
        //        sql = string.Format(sql, strNoVlaue);
        //        string errmsg = string.Empty;
        //        SQLiteClass3.ExecuteNoneQuery(sql, conn, null, null, ref errmsg);
        //        return string.IsNullOrEmpty(errmsg);
        //    }
        //    catch
        //    {
        //        return false;
        //    }
        //}

        //private bool CreateTable_MediaFiles(SQLiteConnection conn)
        //{
        //    string sql = string.Format(@"Drop Table IF Exists [{0}];
        //                                    CREATE TABLE [{0}] ( 
        //                                    [ROWID] INTEGER PRIMARY KEY AUTOINCREMENT  
        //                                    ,[TableMD5] TEXT                                          
        //                                    ,[MessageId] TEXT
        //                                    ,[MediaFileName] TEXT                                          
        //                                    );
        //                                    ", "MediaFiles");
        //    List<string> MediaFileName;
        //    List<WechatMessageAndroidVoiceMsg> AndroidVoiceMsgList;
        //    try
        //    {
        //        sql = string.Format(sql, strNoVlaue);
        //        string errmsg = string.Empty;
        //        SQLiteClass3.ExecuteNoneQuery(sql, conn, null, null, ref errmsg);
        //        return string.IsNullOrEmpty(errmsg);
        //    }
        //    catch
        //    {
        //        return false;
        //    }
        //}

        private bool CreateTable_AndroidVoices(SQLiteConnection conn)
        {
            string sql = string.Format(@"Drop Table IF Exists [{0}];
                                            CREATE TABLE [{0}] ( 
                                            [ROWID] INTEGER PRIMARY KEY AUTOINCREMENT
                                            ,[TableMD5] TEXT 
                                            ,[MessageId] TEXT
                                            ,[MessageLength] bigint
                                            ,[ArrayMessageContent] BLOB                                          
                                            );
                                            ", "AndroidVoices");
            List<string> MediaFileName;
            List<WechatMessageAndroidVoiceMsg> AndroidVoiceMsgList;
            try
            {
                sql = string.Format(sql, strNoVlaue);
                string errmsg = string.Empty;
                SQLiteClass3.ExecuteNoneQuery(sql, conn, null, null, ref errmsg);
                return string.IsNullOrEmpty(errmsg);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateTable_AndroidVoices");
                return false;
            }
        }

        private bool Insert_DBInfo_Version(Version ver, SQLiteConnection conn)
        {
            return this.Insert_DBInfo(DBInfoKey.key_Version, ver.ToString(2), conn);
        }

        private bool Insert_DBInfo(DBInfoKey key, string value, SQLiteConnection conn)
        {
            bool result = false;

            if (conn == null)
            {
                conn = mConn;
            }

            try
            {
                string err = string.Empty;
                string sql = "INSERT INTO [DBInfo](keystate,value) VALUES({0},'{1}')";
                string sqlPara = string.Format(sql, Convert.ToInt32(key.ToString("d")), value);

                SQLiteClass3.ExecuteNoneQuery(sqlPara, conn, null, null, ref err);
                result = err.Length == 0 ? true : false;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "Insert_DBInfo");
                return result;
            }

            return result;
        }

        #endregion

        #region --- Talke读写 ---

        public void Commit()
        {
            while (!Utility.CheckThreadIsStop(this.mThrInsertToDB) && this.mQueueMessages.Count >= 0 && this.mIsInserting == true)
            {
                Utility.WaitSeconds(0.2);
            }

            if (this.mUnCommitCount > 0)
            {
                this.mTran.Commit();
                this.mUnCommitCount = 0;
            }
        }

        public void InsertTalkInfo(string strTableMD5, List<WechatMessageItem> lstMessages)
        {
            KeyValuePair<string, List<WechatMessageItem>> talk = new KeyValuePair<string, List<WechatMessageItem>>(strTableMD5, lstMessages);
            this.mQueueMessages.Enqueue(talk);

            // 测试.......
            //InsertTalkInfoThread();

            if (this.mThrInsertToDB == null || Utility.CheckThreadIsStop(this.mThrInsertToDB))
            {
                this.mThrInsertToDB = new Thread(new ThreadStart(InsertTalkInfoThread));
                this.mThrInsertToDB.IsBackground = true;
                this.mThrInsertToDB.Start();
            }
        }

        public void InsertTalkInfoThread()
        {
            while (this.mQueueMessages.Count > 0)
            {
                KeyValuePair<string, List<WechatMessageItem>> talk = this.mQueueMessages.Dequeue();
                if (!string.IsNullOrWhiteSpace(talk.Key) && talk.Value != null)
                    InsertTalk(talk.Key, talk.Value);
            }
        }

        private int mIntMaidaCount = 0;


        public bool InsertTalk(string strTableMD5, List<WechatMessageItem> lstMessages)
        {
            bool isRelust = false;
            this.mIsInserting = true;
            try
            {
                try
                {
                    string errMsg = string.Empty;
                    string sqlMessages = @"INSERT INTO Messages" + strTableMD5 + @" (TableMD5, MessageType, MessageId, SendId, ReceiveId, MessageContent, Status, 
                                                                 MessageDateTime, MediaCacheThumbPath, MediaCacheBigPath, MessageDT, SociaChatType, MediaFileName) 
                                                       VALUES(@TableMD5, @MessageType, @MessageId, @SendId, @ReceiveId, @MessageContent, @Status,
                                                              @MessageDateTime, @MediaCacheThumbPath, @MediaCacheBigPath, @MessageDT, @SociaChatType, @MediaFileName);";

                    //string sqlMediaFiles = @"INSERT INTO MediaFiles" + strTableMD5 + @" (TableMD5,MessageId,MediaFileName) VALUES(@TableMD5,@MessageId,@MediaFileName);";
                    string sqlAndroidVoices = @"INSERT INTO AndroidVoices (TableMD5, MessageId, MessageLength, ArrayMessageContent) 
                                                            VALUES(@TableMD5, @MessageId, @MessageLength, @ArrayMessageContent);";
                    foreach (WechatMessageItem info in lstMessages)
                    {
                        if (this.mUnCommitCount <= 0)
                            this.mTran = (SQLiteTransaction)mConn.BeginTransaction();

                        this.mUnCommitCount++;
                        SQLiteParameter[] paras = new SQLiteParameter[13];
                        paras[0] = new SQLiteParameter("@TableMD5", strTableMD5);
                        paras[1] = new SQLiteParameter("@MessageType", info.MessageType);
                        paras[2] = new SQLiteParameter("@MessageId", info.MessageId);
                        paras[3] = new SQLiteParameter("@SendId", info.SendId);
                        paras[4] = new SQLiteParameter("@ReceiveId", info.ReceiveId);
                        paras[5] = new SQLiteParameter("@MessageContent", info.MessageContent);
                        paras[6] = new SQLiteParameter("@Status", info.Status);
                        paras[7] = new SQLiteParameter("@MessageDateTime", info.MessageDateTime);
                        paras[8] = new SQLiteParameter("@MediaCacheThumbPath", info.MediaCacheThumbPath);
                        paras[9] = new SQLiteParameter("@MediaCacheBigPath", info.MediaCacheBigPath);
                        paras[10] = new SQLiteParameter("@MessageDT", info.MessageDT.ToString("s"));
                        paras[11] = new SQLiteParameter("@SociaChatType", info.SociaChatType.ToString());
                        string mediaJson = "";

                        //if (info.MediaFileName != null && info.MediaFileName.Count > 0)
                        //{
                        //    foreach (string item in info.MediaFileName)
                        //    {
                        //        paras = new SQLiteParameter[3];
                        //        paras[0] = new SQLiteParameter("@TableMD5", strTableMD5);
                        //        paras[1] = new SQLiteParameter("@MessageId", info.MessageId);
                        //        paras[2] = new SQLiteParameter("@MediaFileName", item);
                        //        SQLiteClass3.ExecuteNoneQuery(sqlMediaFiles, mConn, paras, null, ref errMsg);
                        //        //this.mIntMaidaCount++;
                        //    }
                        //}
                        if (info.MediaFileName != null && info.MediaFileName.Count > 0)
                        {
                            mediaJson = string.Join("***", info.MediaFileName);
                        }

                        paras[12] = new SQLiteParameter("@MediaFileName", mediaJson);
                        SQLiteClass3.ExecuteNoneQuery(sqlMessages, mConn, paras, null, ref errMsg);

                        if (info.AndroidVoiceMsgList != null && info.AndroidVoiceMsgList.Count > 0 && info.AndroidVoiceMsgList.Any(f => f.ArrayMessageContent != null && f.MessageLength > 0))
                        {
                            foreach (WechatMessageAndroidVoiceMsg item in info.AndroidVoiceMsgList)
                            {
                                paras = new SQLiteParameter[4];
                                paras[0] = new SQLiteParameter("@TableMD5", strTableMD5);
                                paras[1] = new SQLiteParameter("@MessageId", info.MessageId);
                                paras[2] = new SQLiteParameter("@MessageLength", item.MessageLength);
                                paras[3] = new SQLiteParameter("@ArrayMessageContent", info.SendId);
                                SQLiteClass3.ExecuteNoneQuery(sqlAndroidVoices, mConn, paras, null, ref errMsg);
                                //this.mIntMaidaCount++;
                            }
                        }

                        if (this.mUnCommitCount >= 100000)
                        {
                            this.mTran.Commit();
                            this.mUnCommitCount = 0;
                        }
                    }

                    isRelust = errMsg.Length == 0 ? true : false;
                }
                catch (Exception ex3)
                {
                    Common.LogException(ex3.ToString(), "WeChatTalkDB_InsterTalkInfo_3");
                    Common.LogException("缓存数据失败", "WeChatTalkDB_InsterTalkInfo_4");
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_InsterTalkInfo");
            }
            //Common.Log("媒体文件数量:" + this.mIntMaidaCount);
            if (lstMessages != null)
                lstMessages.Clear();

            this.mIsInserting = false;
            return isRelust;
        }

        public int GetTalkInfoCount(string strTableName, List<DateTime> lstSearchTime, string strSearch)
        {
            int intRelust = 0;

            string strWhere = string.Empty;
            if (!string.IsNullOrEmpty(strSearch))
            {
                strWhere = string.Format(" AND MessageContent LIKE '%{0}%' ", strSearch);
            }

            if (lstSearchTime != null)
            {
                long longStart = WechatHeplerPC.ConvertPcTimeToWeixin(lstSearchTime[0]);
                long longEnd = WechatHeplerPC.ConvertPcTimeToWeixin(lstSearchTime[1]);
                strWhere += string.Format(" AND MessageDateTime > {0} AND MessageDateTime < {1} ", longStart, longEnd);
            }

            //string strSql = string.Format(@"SELECT  count(MessageId) as num 
            //                                    FROM Messages" + strTableName + @" 
            //                                    WHERE TableMD5='{0}' {1} 
            //                                    ORDER BY MessageDateTime DESC ", strTableName, strWhere);
            string strSql = string.Format(@"SELECT count(ROWID) as num 
                                                FROM Messages" + strTableName + @" 
                                                WHERE 1=1 {0} ", strWhere);
            DataTable dt = SQLiteClass3.ExecuteSQL(strSql, mConn);

            if (dt != null && dt.Rows.Count > 0)
                intRelust = Common.GetValue<int>(dt.Rows[0]["num"], 0);
            return intRelust;
        }

        public List<WechatMessageItem> GetTalkInfo(string strTableName, string nickName,
                                                   List<DateTime> lstSearchTime = null,
                                                   string strSearch = null, int intPage = 0, int intPageCount = 0,
                                                   EventHandler<ExportProgressEventArgs> handlerArgs = null, int index = 0, int indexTotal = 0)
        {
            List<WechatMessageItem> lstTemp = new List<WechatMessageItem>();
            try
            {
                string strWhere = string.Empty;
                if (!string.IsNullOrEmpty(strSearch))
                {
                    strWhere = string.Format(" AND MessageContent LIKE '%{0}%'", strSearch);
                }

                if (lstSearchTime != null)
                {
                    long longStart = WechatHeplerPC.ConvertPcTimeToWeixin(lstSearchTime[0]);
                    long longEnd = WechatHeplerPC.ConvertPcTimeToWeixin(lstSearchTime[1]);
                    strWhere += string.Format(" AND MessageDateTime > {0} AND MessageDateTime < {1} ", longStart, longEnd);
                }

                string strLimit = string.Empty;
                if (intPageCount > 0)
                {
                    strLimit = string.Format("limit {0},{1}", intPage, intPageCount);
                }

                //string strSql = string.Format(@"SELECT MessageType,MessageId,SendId,ReceiveId,MessageContent,Status,MessageDateTime, 
                //                                MediaCacheThumbPath,MediaCacheBigPath,MessageDT,SociaChatType FROM Messages" + strTableName + @" 
                //                                WHERE TableMD5='{0}' {1} ORDER BY MessageDateTime DESC {2}",
                //                                strTableName, strWhere, strLimit);
                string strSql = string.Format(@"SELECT MessageType,MessageId,SendId,ReceiveId,MessageContent,Status,MessageDateTime, 
                                                MediaCacheThumbPath,MediaCacheBigPath,MessageDT,SociaChatType,MediaFileName FROM Messages" + strTableName + @" 
                                                WHERE 1=1 {0} {1}",
                                                strWhere, strLimit);
                DataTable dt = SQLiteClass3.ExecuteSQL(strSql, mConn);
                lstTemp = this.ConvertTableToMessageItem(dt, strTableName, nickName, handlerArgs, index, indexTotal);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_GetTalkInfo");
            }
            return lstTemp;
        }

        public List<WechatMessageItem> GetTalkInfo(string strTableName, string strWhere)
        {
            List<WechatMessageItem> lstTemp = new List<WechatMessageItem>();
            try
            {
                string strSql = string.Format(@"SELECT MessageType,MessageId,SendId,ReceiveId,MessageContent,Status,MessageDateTime, 
                                                MediaCacheThumbPath,MediaCacheBigPath,MessageDT,SociaChatType,MediaFileName FROM Messages" + strTableName + @" 
                                                WHERE {0}",
                                                strWhere);

                DataTable dt = SQLiteClass3.ExecuteSQL(strSql, mConn);
                lstTemp = this.ConvertTableToMessageItem(dt, strTableName, "");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_GetTalkInfo");
            }
            return lstTemp;
        }

        public List<WechatMessageItem> GetTalkInfoByMessageId(string strTableName, string id)
        {
            List<WechatMessageItem> lstTemp = new List<WechatMessageItem>();
            try
            {
                string strSql = string.Format(@"SELECT MessageType,MessageId,SendId,ReceiveId,MessageContent,Status,MessageDateTime, 
                                                MediaCacheThumbPath,MediaCacheBigPath,MessageDT,SociaChatType,MediaFileName FROM Messages" + strTableName + @" 
                                                WHERE MessageId='{0}'",
                                                id);

                DataTable dt = SQLiteClass3.ExecuteSQL(strSql, mConn);
                lstTemp = this.ConvertTableToMessageItem(dt, strTableName, "");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_GetTalkInfo");
            }
            return lstTemp;
        }

        private List<WechatMessageItem>[] listMsgTables_ = null;
        private void GetMsgItemsFromDataRows(object objRows)
        {
            object[] aryRows = (object[])objRows;
            List<DataRow> listRows = aryRows[0] as List<DataRow>;
            int iThread = (int)aryRows[1];
            ManualResetEvent e = aryRows[2] as ManualResetEvent;
            string strTableName = aryRows[3].ToString();
            EventHandler<ExportProgressEventArgs> handlerArgs = aryRows[4] as EventHandler<ExportProgressEventArgs>;
            int threadCounts = (int)aryRows[5];
            int totalCounts = (int)aryRows[6];

            listMsgTables_[iThread] = new List<WechatMessageItem>();

            int nCount = listRows.Count;
            int iIndex = 0;
            foreach (DataRow dr in listRows)
            {
                if (handlerArgs != null)
                {
                    double dStep = ((double)(++iIndex) / (double)nCount) * 100.0;
                    handlerArgs(null, new ExportProgressEventArgs(ExportStatus.Exporting,
                                string.Format("正在准备从数据库中读取第({0}/{1})批数据(总记录数：{3}) ...... {2:0.00}%",
                                iThread + 1, threadCounts, dStep, totalCounts)));
                }

                try
                {
                    WechatMessageItem info = new WechatMessageItem();

                    info.MessageType = Common.GetValue<int>(dr["MessageType"], 0);
                    info.MessageId = Common.GetValue<string>(dr["MessageId"], string.Empty);
                    info.SendId = Common.GetValue<string>(dr["SendId"], string.Empty);
                    info.ReceiveId = Common.GetValue<string>(dr["ReceiveId"], string.Empty);
                    info.MessageContent = Common.GetValue<string>(dr["MessageContent"], string.Empty);
                    info.Status = Common.GetValue<int>(dr["Status"], 0);
                    info.MessageDateTime = Common.GetValue<long>(dr["MessageDateTime"], 0);
                    info.MediaCacheThumbPath = Common.GetValue<string>(dr["MediaCacheThumbPath"], string.Empty);
                    info.MediaCacheBigPath = Common.GetValue<string>(dr["MediaCacheBigPath"], string.Empty);
                    info.MessageDT = Common.GetValue<DateTime>(dr["MessageDT"], DateTime.Now);
                    info.SociaChatType = (SociaChatType)Enum.Parse(typeof(SociaChatType), Common.GetValue<string>(dr["SociaChatType"], string.Empty));
                    string mediaFileName = Common.GetValue<string>(dr["MediaFileName"], string.Empty);
                    if (!string.IsNullOrEmpty(mediaFileName))
                    {
                        info.MediaFileName = this.GetMediaFileName(Common.GetValue<string>(dr["MediaFileName"], string.Empty));
                    }

                    if (info.MessageType == 43 || info.MessageType == 62 || info.MessageType == 3 || info.MessageType == 34)
                    {
                        info.AndroidVoiceMsgList = this.GetAndroidVoice(info.MessageId, strTableName);
                        //info.MediaFileName = this.GetMediaFileName(Common.GetValue<string>(dr["MediaFileName"], string.Empty));
                    }
                    if (info.MessageType == 49)
                    {
                        try
                        {
                            string messageId = XmlLoad(info.MessageContent.Substring(info.MessageContent.IndexOf("<msg")));
                            if (!string.IsNullOrEmpty(messageId))
                            {
                                List<WechatMessageItem> msgs = this.GetTalkInfoByMessageId(strTableName, messageId);
                                if (msgs.Count > 0)
                                {
                                    info.SvrMsg = msgs[0];
                                }
                            }
                        }
                        catch (Exception ex)
                        {

                        }
                    }

                    if (!listMsgTables_[iThread].Contains(info))
                        listMsgTables_[iThread].Add(info);
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "GetMsgItemsFromDataRows");
                }
            }
            if (e != null)
                e.Set();
        }
        private List<WechatMessageItem> ConvertTableToMessageItem(DataTable dt, string strTableName, string nickName, EventHandler<ExportProgressEventArgs> handlerArgs = null, int index = 0, int indexTotal = 0)
        {
            List<WechatMessageItem> lstTemp = new List<WechatMessageItem>();

            try
            {
                if (dt != null && dt.Rows.Count > 0)
                {
                    const int MAXCOUNTS = 1000;
                    int nCounts = dt.Rows.Count;
                    bool bIsThread = nCounts > MAXCOUNTS;
                    bIsThread = false;
                    if (bIsThread)
                    {

                        DataRow[] aryRows = new DataRow[nCounts];
                        dt.Rows.CopyTo(aryRows, 0);
                        List<DataRow> listDataRows = aryRows.ToList<DataRow>();

                        int thread_counts = nCounts / MAXCOUNTS;
                        if (nCounts % MAXCOUNTS > 0)
                            thread_counts++;

                        listMsgTables_ = new List<WechatMessageItem>[thread_counts];

                        int nSpareCounts = 0;
                        //List<ManualResetEvent> listEvents = new List<ManualResetEvent>();
                        for (int iIndex = 0; iIndex < thread_counts; iIndex++)
                        {
                            //ManualResetEvent e = new ManualResetEvent(false);
                            //listEvents.Add(e);

                            if (iIndex == (thread_counts - 1))
                                nSpareCounts = nCounts % MAXCOUNTS;
                            else
                                nSpareCounts = MAXCOUNTS;

                            List<DataRow> listRows = listDataRows.GetRange(iIndex * MAXCOUNTS, nSpareCounts);
                            GetMsgItemsFromDataRows(new object[] { listRows, iIndex, null, strTableName, handlerArgs, thread_counts, nCounts });

                            //ThreadPool.QueueUserWorkItem(new WaitCallback(GetMsgItemsFromDataRows), 
                            //        new object[] { listRows, iIndex, e, strTableName, handlerArgs});
                        }
                        //WaitHandle.WaitAll(listEvents.ToArray());

                        foreach (List<WechatMessageItem> msgItems in listMsgTables_)
                        {
                            lstTemp.AddRange(msgItems);
                        }
                    }
                    else
                    {
                        decimal decIndex = 0;
                        decimal decCounts = (decimal)nCounts;
                        Common.Log("dt.Rows = "+ dt.Rows.Count, "WechatHeplerPC", true);
                        foreach (DataRow dr in dt.Rows)
                        {
                            try
                            {
                                //if (handlerArgs != null)
                                //{
                                //    decimal dStep = decimal.Round((++decIndex / decCounts) * 100.0M, 2);

                                //    if (index > 0 && indexTotal > 0)
                                //        handlerArgs(null, new ExportProgressEventArgs(ExportStatus.Exporting,
                                //                string.Format("正在准备从【{0}】数据库中读取第({3}/{4})批数据(总记录数：{1}) ...... {2:0.00}%", nickName, nCounts, dStep, index, indexTotal)));
                                //    else
                                //        handlerArgs(null, new ExportProgressEventArgs(ExportStatus.Exporting,
                                //                    string.Format("正在准备从【{0}】数据库中读取数据(总记录数：{1}) ...... {2:0.00}%", nickName, nCounts, dStep)));

                                //}
                                WechatMessageItem info = new WechatMessageItem();

                                info.MessageType = Common.GetValue<int>(dr["MessageType"], 0);
                                info.MessageId = Common.GetValue<string>(dr["MessageId"], string.Empty);
                                info.SendId = Common.GetValue<string>(dr["SendId"], string.Empty);
                                info.ReceiveId = Common.GetValue<string>(dr["ReceiveId"], string.Empty);
                                info.MessageContent = Common.GetValue<string>(dr["MessageContent"], string.Empty);
                                info.Status = Common.GetValue<int>(dr["Status"], 0);
                                info.MessageDateTime = Common.GetValue<long>(dr["MessageDateTime"], 0);
                                info.MediaCacheThumbPath = Common.GetValue<string>(dr["MediaCacheThumbPath"], string.Empty);
                                info.MediaCacheBigPath = Common.GetValue<string>(dr["MediaCacheBigPath"], string.Empty);
                                info.MessageDT = Common.GetValue<DateTime>(dr["MessageDT"], DateTime.Now);
                                info.SociaChatType = (SociaChatType)Enum.Parse(typeof(SociaChatType), Common.GetValue<string>(dr["SociaChatType"], string.Empty));
                                string mediaFileName = Common.GetValue<string>(dr["MediaFileName"], string.Empty);
                                if (!string.IsNullOrEmpty(mediaFileName))
                                {
                                    info.MediaFileName = this.GetMediaFileName(Common.GetValue<string>(dr["MediaFileName"], string.Empty));
                                }

                                if (info.MessageType == 43 || info.MessageType == 62 || info.MessageType == 3 || info.MessageType == 34)
                                {
                                    info.AndroidVoiceMsgList = this.GetAndroidVoice(info.MessageId, strTableName);
                                    //info.MediaFileName = this.GetMediaFileName(Common.GetValue<string>(dr["MediaFileName"], string.Empty));
                                    //info.MediaFileName = this.GetMediaFileName(info.MessageId, strTableName);
                                }
                                if (info.MessageType == 49)
                                {
                                    try
                                    {
                                        string messageId = XmlLoad(info.MessageContent.Substring(info.MessageContent.IndexOf("<msg")));
                                        if (!string.IsNullOrEmpty(messageId))
                                        {
                                            List<WechatMessageItem> msgs = this.GetTalkInfoByMessageId(strTableName, messageId);
                                            if (msgs.Count > 0)
                                            {
                                                info.SvrMsg = msgs[0];
                                            }
                                        }
                                    }
                                    catch (Exception ex)
                                    {

                                    }
                                }

                                if (!lstTemp.Contains(info))
                                    lstTemp.Add(info);

                                Common.Log(MyJson.SerializeToJsonString(info), "WechatHeplerPC", true);
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine(ex.ToString());
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_ConvertTableToMessageItem");
            }
            return lstTemp;
        }

        private string XmlLoad(string xml)
        {
            string messageId = string.Empty;
            try
            {
                string dirPath = Folder.GetTempFilePath();
                Folder.CheckFolder(dirPath);
                string fName = Path.Combine(dirPath, Guid.NewGuid().ToString("N")) + ".xml";

                File.WriteAllText(fName, xml);
                if (File.Exists(fName))
                {
                    System.Xml.XmlDocument doc = Common.XmlLoader(fName);
                    var elements = doc.GetElementsByTagName("svrid");
                    if (elements.Count > 0)
                    {
                        var element = elements[0];
                        messageId = element.InnerText;
                    }
                    File.Delete(fName);
                }
            }
            catch (Exception)
            {
                messageId = string.Empty;
            }
            return messageId;
        }

        private List<string> GetMediaFileName(string strMediaName)
        {
            List<string> lstRelust = new List<string>();
            try
            {

                if (!string.IsNullOrEmpty(strMediaName))
                {
                    lstRelust = strMediaName.Split(new string[] { "***" }, StringSplitOptions.RemoveEmptyEntries).ToList();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_GetMediaFileName");
            }
            return lstRelust;
        }

        //private List<string> GetMediaFileName(string strMsgId, string strMediaTableName)
        //{
        //    List<string> lstRelust = new List<string>();
        //    try
        //    {
        //        string strWhere = string.Format(" and MessageId='{0}'", strMsgId);
        //        string strSql = string.Format(@"Select MessageId,MediaFileName from MediaFiles" + strMediaTableName + @" where TableMD5='{0}' {1} ", strMediaTableName, strWhere);

        //        DataTable dt = SQLiteClass3.ExecuteSQL(strSql, mConn);
        //        if (dt != null && dt.Rows.Count > 0)
        //        {
        //            string strMediaFileName = string.Empty;
        //            foreach (DataRow dr in dt.Rows)
        //            {
        //                strMediaFileName = Common.GetValue<string>(dr["MediaFileName"], string.Empty);
        //                if (!string.IsNullOrEmpty(strMediaFileName) && !lstRelust.Contains(strMediaFileName))
        //                    lstRelust.Add(strMediaFileName);
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        Common.LogException(ex.ToString(), "WeChatTalkDB_GetMediaFileName");
        //    }
        //    return lstRelust;
        //}

        private List<WechatMessageAndroidVoiceMsg> GetAndroidVoice(string messageid, string strTableNameAndroidVoiceMsg)
        {
            List<WechatMessageAndroidVoiceMsg> lstRelust = new List<WechatMessageAndroidVoiceMsg>();
            try
            {
                string strSql = string.Format(@"Select MessageId,MessageLength,ArrayMessageContent from AndroidVoices where TableMD5='{0}' and MessageId='{1}' ",
                                              strTableNameAndroidVoiceMsg, messageid);

                DataTable dt = SQLiteClass3.ExecuteSQL(strSql, mConn);
                if (dt != null && dt.Rows.Count > 0)
                {
                    WechatMessageAndroidVoiceMsg info = null;
                    foreach (DataRow dr in dt.Rows)
                    {
                        info = new WechatMessageAndroidVoiceMsg();
                        info.MessageLength = Common.GetValue<long>(dr["MessageLength"], 0);

                        if (dr["ArrayMessageContent"].GetType() == typeof(byte[]))
                            info.ArrayMessageContent = Common.GetValue<byte[]>(dr["ArrayMessageContent"], null);

                        if (info.MessageLength > 0)//如果长度为0 就不需要保存
                            lstRelust.Add(info);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_GetAndroidVoice");
            }
            return lstRelust;
        }

        #endregion

        #region --- 查询聊天记录 ---

        public Dictionary<string, WechatTalk> SearchTalk(List<DateTime> lstSearchTime, string strSearch, Dictionary<string, WechatTalk> dictTalks)
        {
            Dictionary<string, WechatTalk> dictWechatTalk = new Dictionary<string, WechatTalk>(StringComparer.InvariantCultureIgnoreCase);
            try
            {
                if (dictTalks == null && dictTalks.Count == 0)
                    return dictWechatTalk;

                //名字有带关键字的记录            
                foreach (WechatTalk item in dictTalks.Values)
                {
                    if (item.NickName.ToLowerInvariant().Contains(strSearch) || item.UserName.ToLowerInvariant().Contains(strSearch))
                    {
                        if (!dictWechatTalk.ContainsKey(item.Md5))
                        {
                            item.SearchText = string.Empty;
                            dictWechatTalk.Add(item.Md5, item);
                        }
                    }
                }
                Application.DoEvents();

                Dictionary<string, WechatTalk> dictTemp = new Dictionary<string, WechatTalk>(StringComparer.InvariantCultureIgnoreCase);
                if (IniSetting.GetIsTalkSaveDB())
                {
                    dictTemp = this.SearchTalkFromDB(dictTalks, lstSearchTime, strSearch);
                }
                else
                {
                    dictTemp = this.SearchTalkFromCache(dictTalks, lstSearchTime, strSearch);
                }

                if (dictTemp.Count > 0)
                {
                    foreach (WechatTalk item in dictTemp.Values)
                    {
                        dictWechatTalk[item.Md5] = item;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_SearchTalk");
            }
            return dictWechatTalk;
        }

        private Dictionary<string, WechatTalk> SearchTalkFromDB(Dictionary<string, WechatTalk> dictTalks, List<DateTime> lstSearchTime, string strSearch)
        {
            Dictionary<string, WechatTalk> dictReturn = new Dictionary<string, WechatTalk>(StringComparer.InvariantCultureIgnoreCase);
            try
            {
                string strWhere = "";
                if (lstSearchTime != null && lstSearchTime.Count > 0)
                {
                    long longStart = WechatHeplerPC.ConvertPcTimeToWeixin(lstSearchTime[0]);
                    long longEnd = WechatHeplerPC.ConvertPcTimeToWeixin(lstSearchTime[1]);
                    strWhere = string.Format(" AND MessageContent LIKE '%{0}%' AND MessageDateTime > {1} AND MessageDateTime < {2} ", strSearch, longStart, longEnd);
                }
                else
                    strWhere = string.Format(" AND MessageContent LIKE '%{0}%' ", strSearch);

                foreach (var talk in dictTalks)
                {
                    //string strSQL = String.Format("SELECT DISTINCT TableMD5 FROM Messages" + talk.Value.Md5 + " WHERE 1=1 {0} ", strWhere);
                    string strSQL = String.Format("SELECT TableMD5 FROM Messages" + talk.Value.Md5 + " WHERE 1=1 {0} Limit 0,1", strWhere);
                    DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, mConn);

                    if (dt != null && dt.Rows.Count > 0)
                    {
                        foreach (DataRow dr in dt.Rows)
                        {
                            string strKey = Common.GetValue<string>(dr["TableMD5"], string.Empty);
                            if (strKey.Length <= 0)
                                continue;

                            if (dictTalks.ContainsKey(strKey))
                            {
                                dictReturn[strKey] = dictTalks[strKey];
                                dictTalks[strKey].SearchText = strSearch;
                            }
                        }
                    }
                }

                //string strSQL = String.Format("SELECT DISTINCT TableMD5 FROM Messages WHERE 1=1 {0} ", strWhere);
                //DataTable dt = SQLiteClass3.ExecuteSQL(strSQL, mConn);

                //if (dt != null && dt.Rows.Count > 0)
                //{
                //    foreach (DataRow dr in dt.Rows)
                //    {
                //        string strKey = Common.GetValue<string>(dr["TableMD5"], string.Empty);
                //        if (strKey.Length <= 0)
                //            continue;

                //        if (dictTalks.ContainsKey(strKey))
                //        {
                //            dictReturn[strKey] = dictTalks[strKey];
                //            dictTalks[strKey].SearchText = strSearch;
                //        }
                //    }
                //}
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_SearchTalkFromDB");
            }
            return dictReturn;
        }

        private Dictionary<string, WechatTalk> SearchTalkFromCache(Dictionary<string, WechatTalk> dictTalks, List<DateTime> lstSearchTime, string strSearch)
        {
            Dictionary<string, WechatTalk> dictReturn = new Dictionary<string, WechatTalk>(StringComparer.InvariantCultureIgnoreCase);
            try
            {
                long longStart = 0;
                long longEnd = 0;
                bool isByTime = false;
                if (lstSearchTime != null && lstSearchTime.Count == 2)
                {
                    isByTime = true;
                    longStart = WechatHeplerPC.ConvertPcTimeToWeixin(lstSearchTime[0]);
                    longEnd = WechatHeplerPC.ConvertPcTimeToWeixin(lstSearchTime[1]);
                }
                //List<WechatMessageItem> lstWMI = new List<WechatMessageItem>();
                //非数据库读取方式
                foreach (WechatTalk item in dictTalks.Values)
                {
                    List<WechatMessageItem> listWechats = null;
                    if (isByTime)
                    {
                        listWechats = item.MessageItems.FindAll(delegate (WechatMessageItem wmItem)
                        {
                            return wmItem.MessageContent.ToLowerInvariant().Contains(strSearch) &&
                                wmItem.MessageDateTime >= longStart &&
                                wmItem.MessageDateTime <= longEnd &&
                                wmItem.MessageType == 1;
                        });
                    }
                    else
                    {
                        listWechats = item.MessageItems.FindAll(delegate (WechatMessageItem wmItem)
                        {
                            return wmItem.MessageContent.ToLowerInvariant().Contains(strSearch) &&
                                wmItem.MessageType == 1;
                        });
                    }

                    if (listWechats != null && listWechats.Count > 0)
                    {
                        item.SearchText = strSearch;
                        dictReturn[item.Md5] = item;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WeChatTalkDB_SearchTalkFromCache");
            }
            return dictReturn;
        }

        #endregion
    }
}
