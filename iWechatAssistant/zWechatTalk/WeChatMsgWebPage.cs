﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Web;
using System.Xml;

namespace iWechatAssistant
{
    internal class WechatXmlNode
    {
        public string type { get; set; }
        public string title { get; set; }
        public string des { get; set; }
        public string url { get; set; }  
        public string wcpayInfo { get; set; }
        public string sourceDisplayName { get; set; }
        public string refermsg { get; set; }
        public string appname { get; set; }
        public string fileext { get; set; }
        public int fileLength { get; set; }
    }

    public class WeChatMsgWebPage
    {
        private WechatHeplerPC mWechatHeplerPC = null;
        private string mCurrentBackupDBPath = "";
        private string mCurrentBackupDBPassword = "";
        private string mCurrentBackupMediaFile = "";
        private WechatAccInfo mCurrentWAInfo = null;
        private bool mCurrentWeChatiOSDevice = true;

        public List<MergerForwardInfo> lstMergerForwardInfo = new List<MergerForwardInfo>();

        #region --- 单实例 ---

        private static WeChatMsgWebPage instance = null;
        private static readonly object locker = new object();

        public static WeChatMsgWebPage GetInstance(WechatHeplerPC cWechatHeplerPC, string cCurrentBackupDBPath, string cCurrentBackupDBPassword, string strBackupMediaFile, WechatAccInfo cWechatAccInfo, bool cCurrentWeChatiOSDevice)
        {
            if (instance == null)
            {
                lock (locker)
                {
                    if (instance == null)
                    {
                        instance = new WeChatMsgWebPage(cWechatHeplerPC, cCurrentBackupDBPath, cCurrentBackupDBPassword, strBackupMediaFile, cWechatAccInfo, cCurrentWeChatiOSDevice);
                    }
                }
            }

            return instance;
        }
        
        #endregion

        #region 测试...
        public WeChatMsgWebPage()
        {

        }

        public void TestMsg(string msg, string id)
        {
            WechatXmlNode wechatXml = new WechatXmlNode();
            MsgXML(msg, id, ref wechatXml);
        }
        #endregion

        #region --- 构造函数 ---

        private WeChatMsgWebPage(WechatHeplerPC cWechatHeplerPC, string cCurrentBackupDBPath, string cCurrentBackupDBPassword, 
                                 string strBackupMediaFile, WechatAccInfo cWechatAccInfo, bool cCurrentWeChatiOSDevice)
        {
            mWechatHeplerPC = cWechatHeplerPC;
            mCurrentBackupDBPath = cCurrentBackupDBPath;
            mCurrentBackupDBPassword = cCurrentBackupDBPassword;
            mCurrentBackupMediaFile = strBackupMediaFile;
            mCurrentWAInfo = cWechatAccInfo;
            mCurrentWeChatiOSDevice = cCurrentWeChatiOSDevice;
        }

        #endregion


        public string AnalysisNewWebpage(WechatMessageItem item, ref string strMsgForWeb, ref JsonArray arrHtml5ContentForWeb, 
                                         ref string strTypeForWeb, ref string strExportText, ref IMHtmlNodeType imtype, ref bool isLinkA)
        {
            string strFileName = "";
            try
            {
                item.SociaChatType = SociaChatType.webpage;
                strTypeForWeb = "3";
                imtype = IMHtmlNodeType.IMHtmlNode_Media;
                
                WechatXmlNode wechatXml = new WechatXmlNode();
                MsgXML(strMsgForWeb, item.MessageId,ref wechatXml);

                JsonObject dicWebPage = new JsonObject();
                strTypeForWeb = "0";
                arrHtml5ContentForWeb = null;
                switch (wechatXml.type)
                {
                    case "1":
                        strMsgForWeb = string.Format("【{0}】({1})", wechatXml.title, wechatXml.url);
                        strExportText = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", wechatXml.url, wechatXml.title);
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        break;

                    case "3":
                        strMsgForWeb = string.Format("【{0}({1}:{2})】({3})", 
                                    (string.IsNullOrWhiteSpace(wechatXml.appname) ? "音乐应用": wechatXml.appname),
                                    (string.IsNullOrWhiteSpace(wechatXml.title)? " " : wechatXml.title),
                                    (string.IsNullOrWhiteSpace(wechatXml.des)?" " : wechatXml.des),
                                    wechatXml.url);
                        
                        strExportText = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", wechatXml.url, 
                                                    string.IsNullOrWhiteSpace(wechatXml.title) ? wechatXml.des : wechatXml.title);;
                        isLinkA = true;
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        item.SociaChatType = SociaChatType.WebPageProgram;
                        break;
                    case "4":
                        isLinkA = true;
                        strMsgForWeb = string.Format("【{0}】({1})", string.IsNullOrWhiteSpace(wechatXml.title) ? wechatXml.des : wechatXml.title, wechatXml.url);
                        strExportText = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", wechatXml.url, 
                                                        string.IsNullOrWhiteSpace(wechatXml.title) ? wechatXml.des : wechatXml.title);
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        break;
                    case "5":
                        isLinkA = true;
                        strExportText = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", wechatXml.url,
                                                    string.IsNullOrWhiteSpace(wechatXml.title) ? wechatXml.des : wechatXml.title);
                        
                        arrHtml5ContentForWeb = new JsonArray();
                        strMsgForWeb = string.Format("【{0}】({1})", wechatXml.title, HttpUtility.UrlDecode(wechatXml.url));
                        
                        
                        arrHtml5ContentForWeb = null;
                        item.SociaChatType = SociaChatType.webpage;
                        strTypeForWeb = "0";
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        break;

                    case "6": // 发送文件附件
                        strTypeForWeb = "10";
                        if (wechatXml.fileext == "txt")
                            item.SociaChatType = SociaChatType.WebPageText;
                        else if (wechatXml.fileext.StartsWith("doc", StringComparison.InvariantCultureIgnoreCase))
                            item.SociaChatType = SociaChatType.WebPageDoc;
                        else if (wechatXml.fileext.StartsWith("xls", StringComparison.InvariantCultureIgnoreCase))
                            item.SociaChatType = SociaChatType.WebPageXls;
                        else if (wechatXml.fileext.StartsWith("ppt", StringComparison.InvariantCultureIgnoreCase))
                            item.SociaChatType = SociaChatType.WebPagePPT;
                        else if (wechatXml.fileext.StartsWith("pdf", StringComparison.InvariantCultureIgnoreCase))
                            item.SociaChatType = SociaChatType.WebPagePDF;                        
                        else
                        {
                            strTypeForWeb = "0";
                            item.SociaChatType = SociaChatType.WebPageApk;
                        }
                        
                        int intMapKey = 0;
                        mWechatHeplerPC.FileLength_ = wechatXml.fileLength;
                        string strFilePath = mWechatHeplerPC.GetMediaFilePath(item, mCurrentBackupDBPath, mCurrentBackupDBPassword, 
                                                                              mCurrentBackupMediaFile, mCurrentWAInfo.PwdBuffer, 
                                                                              true, mCurrentWeChatiOSDevice, out intMapKey);

                        string strSize = "此文件需先在手机上查看";
                        try
                        {
                            if (!string.IsNullOrEmpty(strFilePath) && File.Exists(strFilePath))
                            {
                                FileInfo finfo = new FileInfo(strFilePath);
                                if (wechatXml.fileLength % 2 != 0 && wechatXml.fileLength < 1024 && finfo.Length < 1024)
                                    strSize = Utility.FormatFileSize(wechatXml.fileLength);
                                else 
                                    strSize = Utility.FormatFileSize(finfo.Length);
                            }

                            arrHtml5ContentForWeb = new JsonArray();

                            dicWebPage.Clear();
                            dicWebPage.Add("title", wechatXml.title);
                            dicWebPage.Add("size", strSize);
                            arrHtml5ContentForWeb.Add(dicWebPage);
                        }
                        catch { }
                        strFileName = wechatXml.title;
                        if (item.SociaChatType == SociaChatType.WebPageApk)
                        {
                            arrHtml5ContentForWeb = null;

                            strMsgForWeb = string.Format("[文件：{0}；大小：{1}]，可在手机上查看", wechatXml.title, strSize);
                            strExportText = strMsgForWeb;
                        }
                        else 
                            strExportText = string.Format("[文件:{0}]", wechatXml.title);

                        break;

                    case "7": // 应用
                        strMsgForWeb = string.Format("【{0}】{1}", wechatXml.appname, wechatXml.title);
                        strExportText = strMsgForWeb;
                        isLinkA = false;
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        item.SociaChatType = SociaChatType.WebPageProgram;
                        break;
                    case "8":
                        strMsgForWeb = "【动画表情】";
                        strExportText = "【动画表情】";
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        break;

                    case "17":
                        strTypeForWeb = "4";
                        strMsgForWeb = wechatXml.title;
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        break;

                    case "19":
                    case "40":
                        strTypeForWeb = "9";
                        arrHtml5ContentForWeb = new JsonArray();
                        strMsgForWeb = wechatXml.title;
                        dicWebPage.Clear();
                        dicWebPage.Add("title", wechatXml.title);
                        dicWebPage.Add("des", wechatXml.des);
                        arrHtml5ContentForWeb.Add(dicWebPage);
                        strExportText = string.Format("【{0}】({1})", wechatXml.title, wechatXml.des);
                        item.SociaChatType = SociaChatType.WebPageGroup;
                        break;
                    case "24":
                        if (lstMergerForwardInfo.FindAll(ListFind).Count == 0)
                        {
                            strTypeForWeb = "0";
                            arrHtml5ContentForWeb = null;
                            strMsgForWeb = string.Format("提示：{0} {1}，可在手机上查看",
                                           (string.IsNullOrWhiteSpace(wechatXml.title) ? " " : wechatXml.title),
                                           (string.IsNullOrWhiteSpace(wechatXml.des) ? " " : wechatXml.des));

                            strExportText = strMsgForWeb;
                            imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        }
                        else
                        {
                            strTypeForWeb = "9";
                            arrHtml5ContentForWeb = new JsonArray();
                            strMsgForWeb = (string.IsNullOrWhiteSpace(wechatXml.title) ? wechatXml.des : wechatXml.title);
                            string tmpTitle = string.IsNullOrWhiteSpace(wechatXml.title) ? "消息" : wechatXml.title;
                            dicWebPage.Clear();
                            dicWebPage.Add("title", tmpTitle);
                            dicWebPage.Add("des", wechatXml.des);
                            arrHtml5ContentForWeb.Add(dicWebPage);
                            strExportText = string.Format("【{0}】({1})", tmpTitle, wechatXml.des);
                            item.SociaChatType = SociaChatType.WebPageGroup;
                        }
                        break;

                    case "33":
                        strTypeForWeb = "0";
                        arrHtml5ContentForWeb = null;
                        strMsgForWeb = string.Format("\"{0}\"小程序，可在手机上查看", wechatXml.sourceDisplayName);
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        item.SociaChatType = SociaChatType.WebPageProgram;
                        break;

                    case "36":
                        strTypeForWeb = "0";
                        arrHtml5ContentForWeb = null;
                        isLinkA = true;
                        // strMsgForWeb = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", strUrl, strTitle);
                        strMsgForWeb = string.Format("【{0}】({1})", wechatXml.title, wechatXml.url);
                        strExportText = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", wechatXml.url, wechatXml.title);
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        break;
                    case "46":  // 微视
                        strTypeForWeb = "0";
                        arrHtml5ContentForWeb = null;
                        strMsgForWeb = string.Format("【{0}:(微视应用)】({2})",wechatXml.title, wechatXml.url);
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        break;
                    case "51":
                        strTypeForWeb = "0";
                        arrHtml5ContentForWeb = null;
                        strMsgForWeb = "视频号小程序，可在手机上查看。";
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        item.SociaChatType = SociaChatType.WebPageVideo;
                        break;
                    case "53":  // 接龙
                        strMsgForWeb = wechatXml.title;
                        strExportText = strMsgForWeb;                        
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        item.SociaChatType = SociaChatType.WebPageView;
                        break;
                    case "54":
                        strMsgForWeb = string.Format("公众号：{0}({1})", wechatXml.sourceDisplayName, wechatXml.title);
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        item.SociaChatType = SociaChatType.WebPageOfficial;
                        break;
                    case "57":
                        strTypeForWeb = "0";
                        arrHtml5ContentForWeb = null;
                        //strMsgForWeb = string.Format("{0}：引用了\"{1}\"", wechatXml.title, wechatXml.refermsg);
                        strMsgForWeb = wechatXml.title;
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_Text;
                        item.SociaChatType = SociaChatType.WebPageRefer;
                        break;
                    case "62":
                        strTypeForWeb = "0";
                        arrHtml5ContentForWeb = null;
                        strMsgForWeb = "拍了拍";
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        item.SociaChatType = SociaChatType.SystemMessages;
                        break;
                    case "2000":
                        strTypeForWeb = "4";
                        arrHtml5ContentForWeb = null;
                        strMsgForWeb = string.Format("[{0}]{1}", wechatXml.title, wechatXml.des);
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        break;

                    case "2001": // wx红包
                        item.SociaChatType = SociaChatType.SystemMessages;
                        strTypeForWeb = "4";
                        strMsgForWeb = string.Format("收到【{0}】,可在手机上查看", wechatXml.title);
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        break;

                    case "2001-1":
                        item.SociaChatType = SociaChatType.SystemMessages;
                        strTypeForWeb = "4";
                        strMsgForWeb = string.Format("{0} {1}", wechatXml.title, wechatXml.des);
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        break;
                    case "2002":
                        item.SociaChatType = SociaChatType.SystemMessages;
                        strTypeForWeb = "4";
                        strMsgForWeb = string.Format("{0} {1}", wechatXml.title, wechatXml.des);
                        strExportText = strMsgForWeb;
                        imtype = IMHtmlNodeType.IMHtmlNode_SystemTip;
                        break;
                    default:
                        //Common.LogException(string.Format("【Type:{0}】{1}", wechatXml.type, strMsgForWeb), "暂未兼容格式");

                        Common.Log(string.Format("AnalysisNewWebpage() of WeChatMsgWebPage=====>未兼容格式类型：【{0}】====>{1}", wechatXml.type, strMsgForWeb));

                        strTypeForWeb = "0";
                        arrHtml5ContentForWeb = null;
                        strMsgForWeb = strMsgForWeb;// string.Format("暂未兼容格式，请联系软件提供商 {0}", strType);
                        strExportText = strMsgForWeb;
                        break;
                }
            }                
            catch (Exception ex)            
            {
                
                Common.LogException(ex.ToString(), "AnalysisNewWebpage");
            }
            return strFileName;
        }

        private void ParseAppMsg(string nodeName, string nodeValue, 
                                 string msgId, string strpath,
                                 ref WechatXmlNode wechatXml)
        {
            switch (nodeName.ToLowerInvariant())
            {
                case "title":
                    wechatXml.title = nodeValue;
                    break;
                case "des":
                    wechatXml.des = nodeValue;
                    break;
                case "type":
                    wechatXml.type = nodeValue;
                    break;
                case "recorditem":
                    if (!string.IsNullOrWhiteSpace(nodeValue))
                        AnalyzeRecorditem(msgId, strpath, nodeValue);
                    break;
            }
        }

        private bool MsgXML(string strMsgForWeb, string msgId, ref WechatXmlNode wechatXml)
        {
            bool rc = true;
            string strTitle = ""; 
            string strDes = ""; 
            string strType = ""; 
            string strUrl = "";
            string strpath = Folder.TempFolder;

            Folder.CheckFolder(Folder.TempFolder);
            
            try
            {
                if (string.IsNullOrWhiteSpace(strMsgForWeb)) return false;
                const string xmlVer = "<?xml version=\"1.0\"?>";
                if (strMsgForWeb.StartsWith(xmlVer))
                {
                    string tmp = strMsgForWeb.Remove(0, xmlVer.Length).Replace("\n", "").Replace("\r", "");
                    if (string.IsNullOrWhiteSpace(tmp)) return false;
                }

                strpath = Path.Combine(Folder.TempFolder, string.Format("xmltemp_{0}.xml", WechatHeplerPC.ParseWechatMsgId(msgId)));

                strMsgForWeb = strMsgForWeb.Replace("<<msg>", "<msg>");
                //strMsgForWeb = WechatHeplerPC.ReplaceHexadecimalSymbols(strMsgForWeb);

                string strxml = ""; // strMsgForWeb.Substring(strMsgForWeb.IndexOf("<msg>"));
                if (strMsgForWeb.Contains("<msg>"))
                    strxml = strMsgForWeb.Substring(strMsgForWeb.IndexOf("<msg>"));
                else if (strMsgForWeb.Contains("<sysmsg"))
                    strxml = strMsgForWeb.Substring(strMsgForWeb.IndexOf("<sysmsg"));
                else if (strMsgForWeb.Contains("<appmsg"))
                {
                    strxml = strMsgForWeb.Substring(strMsgForWeb.IndexOf("<appmsg"));
                    if (!strxml.EndsWith("</appmsg>"))
                    {
                        const string appmsg = "</appmsg>";
                        int pos = strxml.LastIndexOf(appmsg);
                        strxml = strxml.Substring(0, pos + appmsg.Length);
                    }
                }

                if (strxml.Contains(xmlVer) && !strxml.StartsWith(xmlVer))
                {
                    strxml = strxml.Replace(xmlVer, "");
                }
               
                if (!strxml.StartsWith(xmlVer))
                    strxml = "<?xml version=\"1.0\"?>\n" + strxml;
                                
                // Added by Utmost on 2020.06.03
                XmlDocument doc =  WechatHeplerPC.LoadXmlStrings(strxml);
                if (doc == null)
                {
                    using (StreamWriter ws = new StreamWriter(strpath, true))
                    {
                        ws.Write(strxml);
                    }
                    if (!File.Exists(strpath)) return false;
                    
                    doc = Common.XmlLoader(strpath);
                }

                if (doc == null) return false;
               
                XmlNode nodeMsgs = null;
                if (doc.ChildNodes.Count > 1)
                    nodeMsgs = doc.ChildNodes[1];
                else if (doc.ChildNodes.Count == 1)
                    nodeMsgs = doc.ChildNodes[0];
                else
                    nodeMsgs = doc;

                string strWcpayinfo = "";
                XmlNodeList nodeMsgLst = nodeMsgs.ChildNodes;
                for (int i = 0; i < nodeMsgLst.Count; i++)
                {
                    XmlNode nodeAppMsg = nodeMsgLst[i];
                    if (nodeAppMsg.Attributes.Count == 0)
                    {
                        ParseAppMsg(nodeAppMsg.Name, nodeAppMsg.InnerText, msgId, strpath, ref wechatXml);
                        continue;
                    }

                    XmlNodeList nodeAppMsgLst = nodeAppMsg.ChildNodes;

                    if (i == 0)
                    {                       
                        foreach (XmlNode xn in nodeAppMsgLst)
                        {
                            switch (xn.Name.ToLowerInvariant())
                            {
                                case "title":
                                    wechatXml.title = xn.InnerText;
                                    break;
                                case "des":
                                    wechatXml.des = xn.InnerText;
                                    break;
                                case "type":
                                    wechatXml.type = xn.InnerText;
                                    break;
                                case "url":
                                    wechatXml.url = HttpUtility.UrlDecode(xn.InnerText);
                                    break;
                                case "recorditem":
                                    if (!string.IsNullOrWhiteSpace(xn.InnerText))
                                        AnalyzeRecorditem(msgId, strpath, xn.InnerText);
                                    break;
                                case "wcpayinfo":
                                    strWcpayinfo = xn.InnerXml;
                                    wechatXml.wcpayInfo = xn.InnerText;
                                    break;
                                case "sourcedisplayname":
                                    wechatXml.sourceDisplayName = xn.InnerText;
                                    break;
                                case "refermsg":
                                    if (xn.HasChildNodes)
                                    {
                                        foreach (XmlNode referNode in xn.ChildNodes)
                                        {
                                            if (referNode.Name.ToLowerInvariant() == "displayname")
                                            {
                                                wechatXml.refermsg = referNode.InnerText;
                                            }
                                            else if (referNode.Name.ToLowerInvariant() == "content")
                                            {
                                                wechatXml.refermsg = string.Format("{0}：{1}", wechatXml.refermsg, referNode.InnerText);
                                            }
                                        }
                                    }
                                    break;
                                case "appinfo":
                                    if (xn.HasChildNodes)
                                    {
                                        foreach (XmlNode appNode in xn.ChildNodes)
                                        {
                                            if (appNode.Name.ToLowerInvariant() == "appname")
                                            {
                                                wechatXml.appname = appNode.InnerText;
                                                break;
                                            }
                                        }
                                    }
                                    break;
                                case "appattach":
                                    if (xn.HasChildNodes && wechatXml.type == "6")
                                    {
                                        foreach (XmlNode attachNode in xn.ChildNodes)
                                        {
                                            if (attachNode.Name.ToLowerInvariant() == "fileext")
                                                wechatXml.fileext = attachNode.InnerText.ToLowerInvariant();
                                            else if (attachNode.Name.ToLowerInvariant() == "totallen" && !string.IsNullOrWhiteSpace(attachNode.InnerText))
                                                wechatXml.fileLength = int.Parse(attachNode.InnerText.Trim());
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                    else
                    {
                        if (nodeAppMsg.Name.Equals("appinfo", StringComparison.InvariantCultureIgnoreCase))
                        {
                            foreach (XmlNode xn in nodeAppMsgLst)
                            {
                                if (xn.Name.Equals("appname", StringComparison.InvariantCultureIgnoreCase))
                                {
                                    wechatXml.appname = xn.InnerText;
                                }
                            }
                        }
                    }
                }

                if (strMsgForWeb.Contains("<sysmsg"))
                {
                    try
                    {
                        wechatXml.type = nodeMsgs.Attributes["type"].Value;                        
                    }
                    catch { }
                }

                if (wechatXml != null && wechatXml.type == "2001")
                {
                    AnalyzeWcpayinfo(strpath, ref doc, ref strWcpayinfo, ref strType, out strTitle, out strDes);
                    if (!string.IsNullOrWhiteSpace(strWcpayinfo))
                    {
                        wechatXml.wcpayInfo = strWcpayinfo;
                        wechatXml.type = strType;
                        wechatXml.title = strTitle;
                        wechatXml.des = strDes;
                    }
                }
                else if (wechatXml.type == "sysmsgtemplate")
                {
                }
            }
            catch (Exception ex)
            {
                string msg = string.Format("Exception:{0}, \r\n WebPagesMsg:{1}", ex.ToString(), strMsgForWeb);

                Common.LogException(msg, "MsgXML");

                rc = false;
            }
            finally
            {
                //用完就删除掉。
                //如果切换太快会出错。
                try
                {
                    if (File.Exists(strpath))
                        File.Delete(strpath);
                }
                catch { }

                // 回收
                GC.Collect();
            }

            return rc;
        }

        private void AnalyzeWcpayinfo(string strpath, ref XmlDocument doc, ref string strWcpayinfo, ref string strType, out string strTitle, out string strDes)
        {
            strTitle = ""; strDes = "";
            if (strWcpayinfo.Length > 0 && strTitle.Length == 0 && strDes.Length == 0)
            {
                strTitle = ExportHelper.GetWebTagFromReg(strWcpayinfo, "<receivertitle>(?<num>.*?)</receivertitle>", "num").Replace("<![CDATA[", "").Replace("]]>", "");
                strDes = ExportHelper.GetWebTagFromReg(strWcpayinfo, "<senderdes>(?<num>.*?)</senderdes>", "num").Replace("<![CDATA[", "").Replace("]]>", "");
                strType = "2001-1";
            }
        }

        private void AnalyzeRecorditem(string MessageId, string strpath, string recordItems)
        {
            if (!string.IsNullOrWhiteSpace(recordItems))
            {
                string strRecords = recordItems;
                if (!recordItems.StartsWith("<?xml version=\"1.0\"?>"))
                    strRecords = "<?xml version=\"1.0\"?>\n" + recordItems;

                // Added by Utmost on 2020.06.03
                XmlDocument doc =  WechatHeplerPC.LoadXmlStrings(strRecords);
                if (doc == null)
                {
                    if (File.Exists(strpath))
                        File.Delete(strpath);

                    using (StreamWriter file = new StreamWriter(strpath, true))
                    {
                        file.Write(strRecords);
                    }

                    doc = Common.XmlLoader(strpath);
                }

                XmlNode nXml = doc.ChildNodes[1];
                if (nXml == null) return;

                XmlNodeList nRecordinfoLst = nXml.ChildNodes;

                XmlNode listDatas = null;
                foreach (XmlNode item in nRecordinfoLst)
                {
                    if (item.Name.Equals("datalist", StringComparison.InvariantCultureIgnoreCase))
                    {
                        listDatas = item;
                        break;
                    }
                }
                if (listDatas == null) return;

                XmlNodeList dataXmls = listDatas.ChildNodes;
                MergerForwardInfo mfInfo = new MergerForwardInfo();
                foreach (XmlNode itemData in dataXmls)
                {
                    mfInfo = new MergerForwardInfo();
                    if (itemData.Attributes["datatype"] != null)
                        mfInfo.MsgType = itemData.Attributes["datatype"].Value;

                    mfInfo.MsgId = MessageId;
                    XmlNodeList dataNodes = itemData.ChildNodes;
                    foreach (XmlNode item in dataNodes)
                    {
                        try
                        {
                            switch (item.Name.ToLowerInvariant())
                            {
                                case "sourcename":
                                    mfInfo.Sourcename = item.InnerText;
                                    break;
                                case "sourcetime":
                                    mfInfo.Sourcetime = item.InnerText;
                                    break;
                                case "datadesc":
                                    mfInfo.Datadesc = item.InnerText;
                                    break;
                                case "srcmsglocalid":
                                    mfInfo.SrcMsgLocalid = item.InnerText;
                                    break;
                                case "srcmsgcreatetime":
                                    mfInfo.SrcMsgCreateTime = Common.GetLong(item.InnerText);
                                    break;
                                case "srcchatname":
                                    mfInfo.SrcChatname = item.InnerText;
                                    break;

                                case "thumbsourcepath":
                                    mfInfo.Thumbsourcepath = item.InnerText;
                                    break;

                                case "thumbsize":
                                    mfInfo.Thumbsize = item.InnerText;
                                    break;

                                case "datasourcepath":
                                    mfInfo.Datasourcepath = item.InnerText;
                                    break;

                                case "dataitemsource":
                                    XmlNodeList xnl = item.ChildNodes;
                                    if (xnl != null && xnl.Count > 0)
                                    {
                                        XmlNode xn = xnl[0];
                                        mfInfo.Realchatname = xn.InnerText;
                                    }

                                    break;

                                case "datatitle":
                                    mfInfo.Title = item.InnerText;
                                    break;

                                case "weburlitem":
                                    if (mfInfo.MsgType == "5")
                                    {
                                        XmlNodeList xnlweb = item.ChildNodes;
                                        foreach (XmlNode itemweb in xnlweb)
                                        {
                                            switch (itemweb.Name.ToLower())
                                            {
                                                case "title":
                                                    mfInfo.Title = itemweb.InnerText;
                                                    break;
                                                case "link":
                                                    mfInfo.Link = itemweb.InnerText;
                                                    break;
                                                case "desc":
                                                    mfInfo.Datadesc = itemweb.InnerText;
                                                    break;
                                                default:
                                                    break;
                                            }
                                        }
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }
                        catch { }
                    }
                    _MfInfo = mfInfo;
                    if (lstMergerForwardInfo.FindAll(ListFind).Count == 0)
                        lstMergerForwardInfo.Add(mfInfo);
                }
            }
        }

        private MergerForwardInfo _MfInfo;
        public bool ListFind(MergerForwardInfo info)
        {
            if (_MfInfo.MsgId == info.MsgId && _MfInfo.Sourcetime == info.Sourcetime &&
                _MfInfo.Datadesc == info.Datadesc && _MfInfo.Title == info.Title &&
                _MfInfo.Datasourcepath == info.Datasourcepath &&
                _MfInfo.Realchatname == info.Realchatname && _MfInfo.Sourcename == info.Sourcename &&
                _MfInfo.SrcMsgLocalid == info.SrcMsgLocalid)
                return true;
            else
                return false;
        }
    }
}
