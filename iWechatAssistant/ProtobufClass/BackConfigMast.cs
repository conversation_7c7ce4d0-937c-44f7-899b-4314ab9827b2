﻿using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Text;

namespace iWechatAssistant
{
    [ProtoContract]
    public class BackConfigInfo
    {
        [ProtoMember(1)]
        private string mWechatId;
        public string WechatId
        {
            get { return this.mWechatId; }
            set { this.mWechatId = value; }
        }

        [ProtoMember(2)]
        private string mDeviceIndntify;
        public string DeviceIndntify
        {
            get { return this.mDeviceIndntify; }
            set { this.mDeviceIndntify = value; }
        }

        [ProtoMember(3)]
        private long mBackupPrevTime;
        public long BackupPrevTime
        {
            get { return this.mBackupPrevTime; }
            set
            { this.mBackupPrevTime = value; }
        }

        [ProtoMember(4)]
        private long mBackupTime;
        public long BackupTime
        {
            get { return this.mBackupTime; }
            set
            { this.mBackupTime = value; }
        }

        [ProtoMember(5, DataFormat = DataFormat.Group)]
        private BackConfigItem mBackConfigList;
        public BackConfigItem BackConfigList
        {
            get { return this.mBackConfigList; }
            set { this.mBackConfigList = value; }
        }

        private string mStrBackupDBPath = "";

        public string BackupDBPath
        {
            get { return mStrBackupDBPath; }
            set { mStrBackupDBPath = value; }
        }

    }

    [ProtoContract]
    public class BackConfigItem
    {
        [ProtoMember(1)]
        private string mDeviceIndntify;
        public string DeviceIndntify
        {
            get { return this.mDeviceIndntify; }
            set { this.mDeviceIndntify = value; }
        }

        [ProtoMember(2)]
        private string mDeviceName;
        public string DeviceName
        {
            get { return this.mDeviceName; }
            set { this.mDeviceName = value; }
        }

        [ProtoMember(3)]
        private string mDeviceType;
        public string DeviceType
        {
            get { return this.mDeviceType; }
            set { this.mDeviceType = value; }
        }

        [ProtoMember(4)]
        private string mOSType;
        public string OSType
        {
            get { return this.mOSType; }
            set { this.mOSType = value; }
        }

        [ProtoMember(5)]
        private string mOSVersion;
        public string OSVersion
        {
            get { return this.mOSVersion; }
            set { this.mOSVersion = value; }
        }

    }
}
