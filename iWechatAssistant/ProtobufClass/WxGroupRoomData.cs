// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: wxGroupRoomData.pb
// </auto-generated>
#pragma warning disable 1591, 0612, 3021
#region Designer generated code

//using pb = global::Google.Protobuf;
//using pbc = global::Google.Protobuf.Collections;
//using pbr = global::Google.Protobuf.Reflection;
//using scg = global::System.Collections.Generic;
using Google.Protobuf.Reflection;
using Google.Protobuf.Collections;
using Google.Protobuf;

using System.Collections.Generic;
namespace GroupRoomData
{

    /// <summary>Holder for reflection information generated from wxGroupRoomData.pb</summary>
    public static partial class WxGroupRoomDataReflection
    {

        #region Descriptor
        /// <summary>File descriptor for wxGroupRoomData.pb</summary>
        public static FileDescriptor Descriptor
        {
            get { return descriptor; }
        }
        private static FileDescriptor descriptor;

        static WxGroupRoomDataReflection()
        {
            byte[] descriptorData = global::System.Convert.FromBase64String(
                string.Concat(
                  "ChJ3eEdyb3VwUm9vbURhdGEucGISDUdyb3VwUm9vbURhdGEiKgoIbWVtYmVy",
                  "X3QSDAoEd3hpZBgBIAEoDBIQCghtYXJrTmFtZRgCIAEoDCI6Cg9Hcm91cFJv",
                  "b21EYXRhX3QSJwoGbWVtYmVyGAEgAygLMhcuR3JvdXBSb29tRGF0YS5tZW1i",
                  "ZXJfdGIGcHJvdG8z"));
            descriptor = FileDescriptor.FromGeneratedCode(descriptorData,
                new FileDescriptor[] { },
                new GeneratedClrTypeInfo(null, new GeneratedClrTypeInfo[] {
            new GeneratedClrTypeInfo(typeof(global::GroupRoomData.member_t), global::GroupRoomData.member_t.Parser, new[]{ "Wxid", "MarkName" }, null, null, null),
            new GeneratedClrTypeInfo(typeof(global::GroupRoomData.GroupRoomData_t), global::GroupRoomData.GroupRoomData_t.Parser, new[]{ "Member" }, null, null, null)
          }));
        }
        #endregion

    }
    #region Messages
    public sealed partial class member_t :  IMessage<member_t>
    {
        private static readonly  MessageParser<member_t> _parser = new  MessageParser<member_t>(() => new member_t());
        private  UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static  MessageParser<member_t> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static MessageDescriptor Descriptor
        {
            get { return global::GroupRoomData.WxGroupRoomDataReflection.Descriptor.MessageTypes[0]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        MessageDescriptor  IMessage.Descriptor
        {
            get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public member_t()
        {
            OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public member_t(member_t other)
            : this()
        {
            wxid_ = other.wxid_;
            markName_ = other.markName_;
            _unknownFields =  UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public member_t Clone()
        {
            return new member_t(this);
        }

        /// <summary>Field number for the "wxid" field.</summary>
        public const int WxidFieldNumber = 1;
        private  ByteString wxid_ =  ByteString.Empty;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public  ByteString Wxid
        {
            get { return wxid_; }
            set
            {
                wxid_ =  ProtoPreconditions.CheckNotNull(value, "value");
            }
        }

        /// <summary>Field number for the "markName" field.</summary>
        public const int MarkNameFieldNumber = 2;
        private  ByteString markName_ =  ByteString.Empty;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public  ByteString MarkName
        {
            get { return markName_; }
            set
            {
                markName_ =  ProtoPreconditions.CheckNotNull(value, "value");
            }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override bool Equals(object other)
        {
            return Equals(other as member_t);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public bool Equals(member_t other)
        {
            if (ReferenceEquals(other, null))
            {
                return false;
            }
            if (ReferenceEquals(other, this))
            {
                return true;
            }
            if (Wxid != other.Wxid) return false;
            if (MarkName != other.MarkName) return false;
            return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override int GetHashCode()
        {
            int hash = 1;
            if (Wxid.Length != 0) hash ^= Wxid.GetHashCode();
            if (MarkName.Length != 0) hash ^= MarkName.GetHashCode();
            if (_unknownFields != null)
            {
                hash ^= _unknownFields.GetHashCode();
            }
            return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override string ToString()
        {
            return  JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void WriteTo( CodedOutputStream output)
        {
            if (Wxid.Length != 0)
            {
                output.WriteRawTag(10);
                output.WriteBytes(Wxid);
            }
            if (MarkName.Length != 0)
            {
                output.WriteRawTag(18);
                output.WriteBytes(MarkName);
            }
            if (_unknownFields != null)
            {
                _unknownFields.WriteTo(output);
            }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public int CalculateSize()
        {
            int size = 0;
            if (Wxid.Length != 0)
            {
                size += 1 +  CodedOutputStream.ComputeBytesSize(Wxid);
            }
            if (MarkName.Length != 0)
            {
                size += 1 +  CodedOutputStream.ComputeBytesSize(MarkName);
            }
            if (_unknownFields != null)
            {
                size += _unknownFields.CalculateSize();
            }
            return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(member_t other)
        {
            if (other == null)
            {
                return;
            }
            if (other.Wxid.Length != 0)
            {
                Wxid = other.Wxid;
            }
            if (other.MarkName.Length != 0)
            {
                MarkName = other.MarkName;
            }
            _unknownFields =  UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom( CodedInputStream input)
        {
            uint tag;
            while ((tag = input.ReadTag()) != 0)
            {
                switch (tag)
                {
                    default:
                        _unknownFields =  UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                        break;
                    case 10:
                        {
                            Wxid = input.ReadBytes();
                            break;
                        }
                    case 18:
                        {
                            MarkName = input.ReadBytes();
                            break;
                        }
                }
            }
        }

    }

    public sealed partial class GroupRoomData_t :  IMessage<GroupRoomData_t>
    {
        private static readonly  MessageParser<GroupRoomData_t> _parser = new  MessageParser<GroupRoomData_t>(() => new GroupRoomData_t());
        private  UnknownFieldSet _unknownFields;
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static  MessageParser<GroupRoomData_t> Parser { get { return _parser; } }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public static MessageDescriptor Descriptor
        {
            get { return global::GroupRoomData.WxGroupRoomDataReflection.Descriptor.MessageTypes[1]; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        MessageDescriptor  IMessage.Descriptor
        {
            get { return Descriptor; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public GroupRoomData_t()
        {
            OnConstruction();
        }

        partial void OnConstruction();

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public GroupRoomData_t(GroupRoomData_t other)
            : this()
        {
            member_ = other.member_.Clone();
            _unknownFields =  UnknownFieldSet.Clone(other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public GroupRoomData_t Clone()
        {
            return new GroupRoomData_t(this);
        }

        /// <summary>Field number for the "member" field.</summary>
        public const int MemberFieldNumber = 1;
        private static readonly  FieldCodec<global::GroupRoomData.member_t> _repeated_member_codec
            =  FieldCodec.ForMessage(10, global::GroupRoomData.member_t.Parser);
        private readonly  RepeatedField<global::GroupRoomData.member_t> member_ = new  RepeatedField<global::GroupRoomData.member_t>();
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public  RepeatedField<global::GroupRoomData.member_t> Member
        {
            get { return member_; }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override bool Equals(object other)
        {
            return Equals(other as GroupRoomData_t);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public bool Equals(GroupRoomData_t other)
        {
            if (ReferenceEquals(other, null))
            {
                return false;
            }
            if (ReferenceEquals(other, this))
            {
                return true;
            }
            if (!member_.Equals(other.member_)) return false;
            return Equals(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override int GetHashCode()
        {
            int hash = 1;
            hash ^= member_.GetHashCode();
            if (_unknownFields != null)
            {
                hash ^= _unknownFields.GetHashCode();
            }
            return hash;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public override string ToString()
        {
            return  JsonFormatter.ToDiagnosticString(this);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void WriteTo( CodedOutputStream output)
        {
            member_.WriteTo(output, _repeated_member_codec);
            if (_unknownFields != null)
            {
                _unknownFields.WriteTo(output);
            }
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public int CalculateSize()
        {
            int size = 0;
            size += member_.CalculateSize(_repeated_member_codec);
            if (_unknownFields != null)
            {
                size += _unknownFields.CalculateSize();
            }
            return size;
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom(GroupRoomData_t other)
        {
            if (other == null)
            {
                return;
            }
            member_.Add(other.member_);
            _unknownFields =  UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
        }

        [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
        public void MergeFrom( CodedInputStream input)
        {
            uint tag;
            while ((tag = input.ReadTag()) != 0)
            {
                switch (tag)
                {
                    default:
                        _unknownFields =  UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
                        break;
                    case 10:
                        {
                            member_.AddEntriesFrom(input, _repeated_member_codec);
                            break;
                        }
                }
            }
        }

    }

    #endregion

}

#endregion Designer generated code
