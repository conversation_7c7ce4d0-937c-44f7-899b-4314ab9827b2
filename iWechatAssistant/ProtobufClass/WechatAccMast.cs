﻿using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Text;

namespace iWechatAssistant
{
    [ProtoContract]
    class WechatAccMast
    {
        [ProtoMember(3, DataFormat = DataFormat.Group)]
        private List<WechatAccItem> mWechatAccList;
        public List<WechatAccItem> WechatAccList
        {
            get { return this.mWechatAccList; }
            set { this.mWechatAccList = value; }
        }
    }

    [ProtoContract]
    class WechatAccItem
    {
        [ProtoMember(1)]
        private int mWechatAccType;
        public int WechatAccType
        {
            get { return this.mWechatAccType; }
            set { this.mWechatAccType = value; }
        }

        [ProtoMember(2)]
        private string mWechatAccValue;
        public string WechatAccValue
        {
            get { return this.mWechatAccValue; }
            set { this.mWechatAccValue = value; }
        }

    }


    [ProtoContract]
    class WechatBackupFilesMast
    {
        [ProtoMember(3, DataFormat = DataFormat.Group)]
        private List<WechatBackupFilesItem> mList;
        public List<WechatBackupFilesItem> LstWCBFiles
        {
            get { return this.mList; }
            set { this.mList = value; }
        }
    }

    [ProtoContract]
    class WechatBackupFilesItem
    {
        [ProtoMember(1)]
        private int mWCType;
        public int WCType
        {
            get { return this.mWCType; }
            set { this.mWCType = value; }
        }

        [ProtoMember(2)]
        private string mWCValue;
        public string WCValue
        {
            get { return this.mWCValue; }
            set { this.mWCValue = value; }
        }

    }

}
