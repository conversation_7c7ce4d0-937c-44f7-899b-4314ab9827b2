﻿using ProtoBuf;
using System;
using System.Collections.Generic;
using System.Text;

namespace iWechatAssistant
{
    [ProtoContract]
    public class WechatMessageMast
    {
        [ProtoMember(1)]
        private int mMessagegItemCount;
        public int MessagegItemCount
        {
            get { return this.mMessagegItemCount; }
            set { this.mMessagegItemCount = value; }
        }

        // DataFormat.Group will not need to buffer the data in memory
        [ProtoMember(2, DataFormat = DataFormat.Group)]
        private List<WechatMessageItem> mMessagegItemList;
        public List<WechatMessageItem> MessagegItemList
        {
            get { return this.mMessagegItemList; }
            set { this.mMessagegItemList = value; }
        }
    }

    [ProtoContract]
    public class WechatMessageItem
    {
        [ProtoMember(1)]
        private int mMessageType;

        /// <summary>
        /// 消息类型
        /// </summary>
        public int MessageType
        {
            get { return this.mMessageType; }
            set { this.mMessageType = value; }
        }

        [ProtoMember(2)]
        private string mMessageId;
        /// <summary>
        /// 消息ID
        /// </summary>
        public string MessageId
        {
            get
            {
                mMessageId = this.TrimCode(this.mMessageId);
                if (string.IsNullOrEmpty(mMessageId))
                {
                    mMessageId = mMessageId2.ToString();
                }
                return mMessageId;
            }
            set { this.mMessageId = value; }
        }

        [ProtoMember(16)]
        private long mMessageId2;
        /// <summary>
        /// 消息才是世海认证的消息ID，目前先作为候补
        /// </summary>
        //public long MessageId2
        //{
        //    get { return this.mMessageId2; }
        //    set { this.mMessageId2 = value; }
        //}

        [ProtoMember(3)]
        private string mSendId;
        /// <summary>
        /// 发送方
        /// </summary>
        public string SendId
        {
            get { return this.TrimCode(this.mSendId); }
            set { this.mSendId = value; }
        }

        [ProtoMember(4)]
        private string mReceiveId;
        /// <summary>
        /// 接收方
        /// </summary>
        public string ReceiveId
        {
            get { return this.TrimCode(this.mReceiveId); }
            set { this.mReceiveId = value; }
        }

        [ProtoMember(5)]
        private string mMessageContent;
        /// <summary>
        /// 消息内容
        /// </summary>
        public string MessageContent
        {
            get { return this.TrimCode(this.mMessageContent); }
            set { this.mMessageContent = value; }
        }

        [ProtoMember(6)]
        private int mStatus;
        /// <summary>
        /// 消息状态
        /// </summary>
        public int Status
        {
            get { return this.mStatus; }
            set { this.mStatus = value; }
        }

        [ProtoMember(7)]
        private long mMessageDateTime;
        /// <summary>
        /// 消息时间
        /// </summary>
        public long MessageDateTime
        {
            get { return this.mMessageDateTime; }
            set { this.mMessageDateTime = value; }
        }

        private DateTime mMessageDT=DateTime.Now;
        /// <summary>
        /// 消息时间
        /// </summary>
        public DateTime MessageDT
        {
            get
            {
                try
                {
                    this.mMessageDT = WechatHeplerPC.ConvertWeixinToPcTime(mMessageDateTime);
                }
                catch { }
                return this.mMessageDT;
            }
            set { this.mMessageDT = value; }
        }

        // DataFormat.Group will not need to buffer the data in memory
        [ProtoMember(11, DataFormat = DataFormat.Group)]
        private List<string> mMediaFileName;
        /// <summary>
        /// 媒体名字
        /// </summary>
        public List<string> MediaFileName
        {
            get { return this.TrimCodeList(this.mMediaFileName); }
            set { this.mMediaFileName = value; }
        }

        // DataFormat.Group will not need to buffer the data in memory
        [ProtoMember(13, DataFormat = DataFormat.Group)]
        private List<WechatMessageAndroidVoiceMsg> mAndroidVoiceMsgList;
        /// <summary>
        /// 安卓媒体文件信息
        /// </summary>
        public List<WechatMessageAndroidVoiceMsg> AndroidVoiceMsgList
        {
            get { return this.mAndroidVoiceMsgList; }
            set { this.mAndroidVoiceMsgList = value; }
        }

        #region "--- 去掉文本前面特殊字符，临时处理的方式 ---"

        private List<string> TrimCodeList(List<string> lstValue)
        {
            List<string> lstReturn = null;
            if (lstValue != null && lstValue.Count > 0 && lstValue[0].StartsWith("\n"))
            {
                lstReturn = new List<string>();
                foreach (string item in lstValue)
                {
                    string strVal = this.TrimCode(item);
                    lstReturn.Add(strVal);
                }
            }
            else
            {
                return lstValue;
            }

            return lstReturn;
        }

        private string TrimCode(string strValue)
        {
            string strReturn = strValue;
            if (!string.IsNullOrEmpty(strValue) && strValue.StartsWith("\n"))
            {
                strReturn = strValue.Substring(2, strValue.Length - 2);
            }

            return strReturn;
        }

        #endregion

        private SociaChatType mSociaChatType = SociaChatType.Text;
        /// <summary>
        /// 扩展消息类型，可以区分不同的消息格式。
        /// </summary>
        public SociaChatType SociaChatType
        {
            get { return this.mSociaChatType; }
            set { this.mSociaChatType = value; }
        }

        private string mMediaCacheThumbPath = "";
        /// <summary>
        /// 媒体的缩略图路径
        /// </summary>
        public string MediaCacheThumbPath
        {
            get { return mMediaCacheThumbPath; }
            set { this.mMediaCacheThumbPath = value; }
        }

        private string mMediaCacheBigPath = "";
        /// <summary>
        /// 媒体的大图路径
        /// </summary>
        public string MediaCacheBigPath
        {
            get { return mMediaCacheBigPath; }
            set { this.mMediaCacheBigPath = value; }
        }

        private WechatMessageItem mSvrMsg;
        /// <summary>
        /// 引用的消息信息
        /// </summary>
        public WechatMessageItem SvrMsg
        {
            get { return this.mSvrMsg; }
            set { this.mSvrMsg = value; }
        }
    }

    [ProtoContract]
    public class WechatMessageAndroidVoiceMsg
    {
        [ProtoMember(1)]
        private long mMessageLength;
        /// <summary>
        /// 长度
        /// </summary>
        public long MessageLength
        {
            get { return this.mMessageLength; }
            set { this.mMessageLength = value; }
        }

        //[ProtoMember(2)]
        //private string mMessageContent;
        ///// <summary>
        ///// 内容
        ///// </summary>
        //public string MessageContent
        //{
        //    get { return this.mMessageContent; }
        //    set { this.mMessageContent = value; }
        //}

        // DataFormat.Group will not need to buffer the data in memory
        [ProtoMember(2,DataFormat = DataFormat.Group)]
        private byte[] mMessageContent;
        public byte[] ArrayMessageContent
        {
            get { return this.mMessageContent; }
            set { this.mMessageContent = value; }
        }
    }

    public enum SociaChatType
    {
        // 只是一个类型，方便记忆，从微信那边抄过来的
        Text = 1,
        Picture = 3,
        Voice = 34,
        NameCard = 42,
        Video = 43,
        Location = 48,
        webpage = 49,
        VideoConnected = 50,
        SystemMessages = 1000,
        SmallVideo = 62,
        SpecialExpression = 47,
        File = 4,


        WebPageText = 496,   // 文本附件
        WebPageDoc = 497,
        WebPageXls = 498,
        WebPagePPT = 499,
        WebPageProgram = 500,
        WebPageVideo = 501,
        WebPageGroup = 502,  // 群聊天记录
        WebPagePDF = 503 ,
        WebPageApk = 504,

        WebPageRefer = 507,
        WebPageView = 553, // 接龙
        WebPageOfficial = 554, // 公众号名片
    }

}
