﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using iTong.CoreFoundation;

namespace iWechatAssistant.zComponents
{
    public partial class tbPage : UserControl
    {

        public event EventHandler HomePageCallback;
        public event EventHandler PreviousPageCallback;
        public event EventHandler NextPageCallback;
        public event EventHandler TrailerPageCallback;
        public event EventHandler GotoPageCallback;

        private double _currentPage = 0;
        public double CurrentPage
        {
            get { return _currentPage; }
            set
            {
                _currentPage = value;
                this.SetPageInfo();
            }
        }

        private double _totalPage = 0;
        public double TotalPage
        {
            get { return _totalPage; }
            set
            {
                _totalPage = value;
                this.SetPageInfo();
            }
        }

        //public void IniGotoPage()
        //{
        //    this.txtPage.Text = "1";
        //}

        public tbPage()
        {
            InitializeComponent();
            this.Cursor = Cursors.Default;
            this.txtPage.TextChanged += txtPage_TextChanged;
        }

        private void btnHomePage_Click(object sender, EventArgs e)
        {
            this.CurrentPage = 1;
            this.SetPageInfo();
            if (HomePageCallback != null)
                HomePageCallback(sender, e);
        }

        private void btnPreviousPage_Click(object sender, EventArgs e)
        {
            this.CurrentPage--;
            this.SetPageInfo();
            if (PreviousPageCallback != null)
                PreviousPageCallback(sender, e);
        }

        private void btnNextPage_Click(object sender, EventArgs e)
        {
            this.CurrentPage++;
            this.SetPageInfo();
            if (NextPageCallback != null)
                NextPageCallback(sender, e);
        }

        private void btnTrailerPage_Click(object sender, EventArgs e)
        {
            this.CurrentPage = this._totalPage;
            this.SetPageInfo();
            if (TrailerPageCallback != null)
                TrailerPageCallback(sender, e);
        }

        private void InvokeOnUiThreadIfRequired(Control ctrl, Action action)
        {            
            if (ctrl.Disposing || ctrl.IsDisposed || !ctrl.IsHandleCreated)
            {
                return;
            }

            if (ctrl.InvokeRequired)
            {
                ctrl.BeginInvoke((Action)(() =>
                {
                    //No action
                    if (ctrl.Disposing || ctrl.IsDisposed || !ctrl.IsHandleCreated)
                    {
                        return;
                    }

                    action();
                }));
            }
            else
            {
                action.Invoke();
            }
        }

        public void Clear()
        {
            _currentPage = 0;
            _totalPage = 0;

            InvokeOnUiThreadIfRequired(lblPage, () => { lblPage.Text = "0/0页"; txtPage.Text = "0"; }); 
        }

        private void SetPageInfo()
        {
            if (this._currentPage <= 1)
            {
                this.btnHomePage.Enabled = false;
                this.btnPreviousPage.Enabled = false;
            }
            else
            {
                this.btnHomePage.Enabled = true;
                this.btnPreviousPage.Enabled = true;
            }

            if (this._currentPage >= this._totalPage)
            {
                this.btnNextPage.Enabled = false;
                this.btnTrailerPage.Enabled = false;
            }
            else
            {
                this.btnNextPage.Enabled = true;
                this.btnTrailerPage.Enabled = true;
            }

           //this.lblPage.Text = string.Format(@"{0}/{1}页", this._currentPage, this._totalPage);

            InvokeOnUiThreadIfRequired(lblPage, () =>
            {
                lblPage.Text = string.Format(@"{0}/{1}页", _currentPage, _totalPage);
                txtPage.Text = string.Format("{0}", (int)CurrentPage);
            });  
        }

        private void txtPage_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                this.btnGoPage_Click(null, null);
        }

        private void txtPage_TextChanged(object sender, EventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(this.txtPage.Text))
                {
                    int intPage = Convert.ToInt32(this.txtPage.Text);
                    if (intPage > this.TotalPage)
                    {
                        this.txtPage.Text = this.TotalPage.ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "txtPage_TextChanged");
            }

        }

        private void btnGoPage_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(this.txtPage.Text))
            {
                this.txtPage.Focus();
                return;
            }
            int intPage = Convert.ToInt32(this.txtPage.Text);
            if (intPage == 0)
                this.txtPage.Text = "1";

            this.CurrentPage = Convert.ToDouble(this.txtPage.Text);
            this.SetPageInfo();
            if (GotoPageCallback != null)
                GotoPageCallback(sender, e);
        }


    }
}
