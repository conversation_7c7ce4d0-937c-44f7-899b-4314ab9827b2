﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Security;
using System.Text;
using System.Threading.Tasks;
using Microsoft.VisualBasic;
using System.Windows.Forms;
using System.Drawing;
using iTong.CoreFoundation;

public class tbBattery : Label
{
    public event EventHandler TextClick;
    public event EventHandler SizeChange;

    public float mMeWidth = 0.0F;

    private ColorTextList _colorTextList = new ColorTextList();
    public ColorTextList ColorTextList
    {
        get
        {
            return _colorTextList;
        }
        set
        {
            _colorTextList = value;
        }
    }

    public tbBattery()
    {
        this.Cursor = Cursors.Default;
        ColorTextList.RaiseChanged -= ColorTextList_RaiseChanged;
        ColorTextList.RaiseChanged += ColorTextList_RaiseChanged;
    }
    protected override Size DefaultSize
    {
        get
        {
            return new Size(120, 18);
        }
    }
    protected override void OnPaint(PaintEventArgs e)
    {
        if (ColorTextList.Count == 0)
            base.OnPaint(e);

        Graphics g = e.Graphics;
        g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
        g.PageUnit = GraphicsUnit.Pixel;

        SizeF textSize;
        float fx = 0.0F;
        StringFormat drawFormat = new StringFormat();
        float singleY = 0.0F;

        drawFormat.Alignment = StringAlignment.Center;
        // drawFormat.LineAlignment = StringAlignment.Far
        try
        {
            foreach (ColorText var in ColorTextList)
            {
                using (SolidBrush drawBrush = new SolidBrush(var.mFontColor))
                {
                    // 文本的矩形区域大小
                    textSize = TextRenderer.MeasureText(g, var.mText, var.mFont, new Size(0, 0), TextFormatFlags.Bottom);
                    // g.DrawString(var.mText, var.mFont, drawBrush, Padding.Left + fx, Padding.Top, drawFormat);
                    singleY = var.mIsIntergar ? this.Height - Padding.Vertical - textSize.Height + 5 : this.Height - Padding.Vertical - textSize.Height;

                    RectangleF rect = new RectangleF(Padding.Left + fx, singleY, textSize.Width + 8, this.Height - Padding.Vertical);
                    if (var.CanClick && var.Rectangle != rect)
                        var.Rectangle = rect;
                    // g.DrawRectangle(New Pen(Color.Black), rect.X, rect.Y, textSize.Width, textSize.Height)
                    g.DrawString(var.mText, var.mFont, drawBrush, rect, drawFormat);
                    fx += (textSize.Width);
                }
            }
            mMeWidth = fx;
            if (SizeChange != null)
                SizeChange(this, e);
        }
        catch (Exception ex)
        {
            Common.LogException(ex.ToString(), "tbBattery_OnPaint");
        }
    }
    protected override void OnCreateControl()
    {
        base.OnCreateControl();
        if (this.ColorTextList.Count == 0)
            return;
    }
    private void SetSize()
    {
        Graphics g = Graphics.FromHwnd(base.Handle);
        SizeF sf = g.MeasureString(this.ColorTextList.ToString(), this.Font);
        this.Size = new Size(Convert.ToInt32(sf.Width), Convert.ToInt32(sf.Height));
        // 文本的矩形区域大小
        this.Text = this.ColorTextList.ToString();
    }
    private void ColorTextList_RaiseChanged(object sender, EventArgs e)
    {
        // SetSize();
        this.Invalidate();
    }

    protected override void OnMouseClick(MouseEventArgs e)
    {
        base.OnMouseClick(e);
        try
        {
            foreach (ColorText var in ColorTextList)
            {
                if (var.CanClick && var.Rectangle.Contains(e.Location))
                {
                    TextClick(var, e);
                    break;
                }
            }
        }
        catch (Exception ex)
        {
            Common.LogException(ex.ToString(), "tbBattery_OnMouseClick");
        }
    }

    protected override void OnMouseMove(MouseEventArgs e)
    {
        base.OnMouseMove(e);
        try
        {
            bool blnHover = false;
            foreach (ColorText var in ColorTextList)
            {
                if (var.CanClick && var.Rectangle.Contains(e.Location))
                {
                    blnHover = true;
                    break;
                }
            }
            if (blnHover)
                this.Cursor = Cursors.Hand;
            else
                this.Cursor = Cursors.Default;
        }
        catch (Exception ex)
        {
            Common.LogException(ex.ToString(), "tbBattery_OnMouseMove");
        }
    }
}

public class ColorText
{
    private Color _fontColor;
    public Color mFontColor
    {
        get
        {
            return _fontColor;
        }
        set
        {
            _fontColor = value;
        }
    }

    private int _index;
    public int mIndex
    {
        get
        {
            return _index;
        }
        set
        {
            _index = value;
        }
    }

    private string _text;
    public string mText
    {
        get
        {
            return _text;
        }
        set
        {
            _text = value;
        }
    }

    private Font _font;
    public Font mFont
    {
        get
        {
            return _font;
        }
        set
        {
            _font = value;
        }
    }

    private bool _isIntergar;
    public bool mIsIntergar
    {
        get
        {
            return _isIntergar;
        }
        set
        {
            _isIntergar = value;
        }
    }

    private RectangleF _rectangle = RectangleF.Empty;
    protected internal RectangleF Rectangle
    {
        get
        {
            return _rectangle;
        }
        set
        {
            _rectangle = value;
        }
    }

    private bool _CanClick = false;
    protected internal bool CanClick
    {
        get
        {
            return _CanClick;
        }
        set
        {
            _CanClick = value;
        }
    }
}

public class ColorTextList : List<ColorText>
{
    public event EventHandler RaiseChanged;

    public void AddItem(string text, int index, Color color, Font font, bool isIntergar, bool isCanClick = false)
    {
        ColorText ct = new ColorText();
        ct.mText = text;
        ct.mIndex = index;
        ct.mFontColor = color;
        ct.mFont = font;
        ct.mIsIntergar = isIntergar;
        ct.CanClick = isCanClick;
        this.Add(ct);

        RaiseChanged(this, null);
    }

    public void SetText(int index, string text, Color color)
    {
        this[index].mText = text;
        this[index].mFontColor = color;
        RaiseChanged(this, null);
    }

    public string[] ToStringArray()
    {
        string[] arr = new string[this.Count - 1 + 1];

        for (int i = 0; i <= this.Count - 1; i++)
            arr[i] = this[i].mText;
        return arr;
    }

    public new string ToString()
    {
        return string.Concat(ToStringArray());
    }
}
