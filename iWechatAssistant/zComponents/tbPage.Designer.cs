﻿namespace iWechatAssistant.zComponents
{
    partial class tbPage
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(tbPage));
            this.lblPage = new System.Windows.Forms.Label();
            this.btnHomePage = new iTong.Components.tbButton();
            this.btnPreviousPage = new iTong.Components.tbButton();
            this.btnNextPage = new iTong.Components.tbButton();
            this.btnTrailerPage = new iTong.Components.tbButton();
            this.txtPage = new iTong.Components.tbTextBox();
            this.btnGoPage = new iTong.Components.tbButton();
            this.tbLabel1 = new iTong.Components.tbLabel();
            this.tbLabel2 = new iTong.Components.tbLabel();
            this.SuspendLayout();
            // 
            // lblPage
            // 
            this.lblPage.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblPage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.lblPage.Location = new System.Drawing.Point(162, 6);
            this.lblPage.Name = "lblPage";
            this.lblPage.Size = new System.Drawing.Size(84, 24);
            this.lblPage.TabIndex = 2;
            this.lblPage.Text = "0000/0000页";
            this.lblPage.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnHomePage
            // 
            this.btnHomePage.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnHomePage.BackColor = System.Drawing.Color.Transparent;
            this.btnHomePage.BindingForm = null;
            this.btnHomePage.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnHomePage.Location = new System.Drawing.Point(100, 5);
            this.btnHomePage.Name = "btnHomePage";
            this.btnHomePage.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnHomePage.Selectable = true;
            this.btnHomePage.Size = new System.Drawing.Size(26, 27);
            this.btnHomePage.TabIndex = 3;
            this.btnHomePage.tbAdriftIconWhenHover = false;
            this.btnHomePage.tbAutoSize = false;
            this.btnHomePage.tbAutoSizeEx = false;
            this.btnHomePage.tbBackgroundImage = null;
            this.btnHomePage.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnHomePage.tbBadgeNumber = 0;
            this.btnHomePage.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnHomePage.tbEndEllipsis = false;
            this.btnHomePage.tbIconHoldPlace = true;
            this.btnHomePage.tbIconImage = global::iWechatAssistant.Properties.Resources.homepage;
            this.btnHomePage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnHomePage.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnHomePage.tbIconMore = false;
            this.btnHomePage.tbIconMouseDown = null;
            this.btnHomePage.tbIconMouseHover = null;
            this.btnHomePage.tbIconMouseLeave = null;
            this.btnHomePage.tbIconPlaceText = 2;
            this.btnHomePage.tbIconReadOnly = null;
            this.btnHomePage.tbImageMouseDown = null;
            this.btnHomePage.tbImageMouseHover = null;
            this.btnHomePage.tbImageMouseLeave = null;
            this.btnHomePage.tbProgressValue = 50;
            this.btnHomePage.tbReadOnly = false;
            this.btnHomePage.tbReadOnlyText = false;
            this.btnHomePage.tbShadow = false;
            this.btnHomePage.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnHomePage.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnHomePage.tbShowDot = false;
            this.btnHomePage.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnHomePage.tbShowMoreIconImg")));
            this.btnHomePage.tbShowNew = false;
            this.btnHomePage.tbShowProgress = false;
            this.btnHomePage.tbShowTip = true;
            this.btnHomePage.tbShowToolTipOnButton = false;
            this.btnHomePage.tbSplit = "3,3,3,3";
            this.btnHomePage.tbText = "";
            this.btnHomePage.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnHomePage.tbTextColor = System.Drawing.Color.White;
            this.btnHomePage.tbTextColorDisable = System.Drawing.Color.White;
            this.btnHomePage.tbTextColorDown = System.Drawing.Color.White;
            this.btnHomePage.tbTextColorHover = System.Drawing.Color.White;
            this.btnHomePage.tbTextMouseDownPlace = 0;
            this.btnHomePage.tbToolTip = "";
            this.btnHomePage.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnHomePage.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnHomePage.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnHomePage.VisibleEx = true;
            this.btnHomePage.Click += new System.EventHandler(this.btnHomePage_Click);
            // 
            // btnPreviousPage
            // 
            this.btnPreviousPage.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnPreviousPage.BackColor = System.Drawing.Color.Transparent;
            this.btnPreviousPage.BindingForm = null;
            this.btnPreviousPage.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnPreviousPage.Location = new System.Drawing.Point(132, 5);
            this.btnPreviousPage.Name = "btnPreviousPage";
            this.btnPreviousPage.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnPreviousPage.Selectable = true;
            this.btnPreviousPage.Size = new System.Drawing.Size(26, 27);
            this.btnPreviousPage.TabIndex = 3;
            this.btnPreviousPage.tbAdriftIconWhenHover = false;
            this.btnPreviousPage.tbAutoSize = false;
            this.btnPreviousPage.tbAutoSizeEx = false;
            this.btnPreviousPage.tbBackgroundImage = null;
            this.btnPreviousPage.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnPreviousPage.tbBadgeNumber = 0;
            this.btnPreviousPage.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnPreviousPage.tbEndEllipsis = false;
            this.btnPreviousPage.tbIconHoldPlace = true;
            this.btnPreviousPage.tbIconImage = global::iWechatAssistant.Properties.Resources.previouspage;
            this.btnPreviousPage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnPreviousPage.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnPreviousPage.tbIconMore = false;
            this.btnPreviousPage.tbIconMouseDown = null;
            this.btnPreviousPage.tbIconMouseHover = null;
            this.btnPreviousPage.tbIconMouseLeave = null;
            this.btnPreviousPage.tbIconPlaceText = 2;
            this.btnPreviousPage.tbIconReadOnly = null;
            this.btnPreviousPage.tbImageMouseDown = null;
            this.btnPreviousPage.tbImageMouseHover = null;
            this.btnPreviousPage.tbImageMouseLeave = null;
            this.btnPreviousPage.tbProgressValue = 50;
            this.btnPreviousPage.tbReadOnly = false;
            this.btnPreviousPage.tbReadOnlyText = false;
            this.btnPreviousPage.tbShadow = false;
            this.btnPreviousPage.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnPreviousPage.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnPreviousPage.tbShowDot = false;
            this.btnPreviousPage.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnPreviousPage.tbShowMoreIconImg")));
            this.btnPreviousPage.tbShowNew = false;
            this.btnPreviousPage.tbShowProgress = false;
            this.btnPreviousPage.tbShowTip = true;
            this.btnPreviousPage.tbShowToolTipOnButton = false;
            this.btnPreviousPage.tbSplit = "3,3,3,3";
            this.btnPreviousPage.tbText = "";
            this.btnPreviousPage.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPreviousPage.tbTextColor = System.Drawing.Color.White;
            this.btnPreviousPage.tbTextColorDisable = System.Drawing.Color.White;
            this.btnPreviousPage.tbTextColorDown = System.Drawing.Color.White;
            this.btnPreviousPage.tbTextColorHover = System.Drawing.Color.White;
            this.btnPreviousPage.tbTextMouseDownPlace = 0;
            this.btnPreviousPage.tbToolTip = "";
            this.btnPreviousPage.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnPreviousPage.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnPreviousPage.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPreviousPage.VisibleEx = true;
            this.btnPreviousPage.Click += new System.EventHandler(this.btnPreviousPage_Click);
            // 
            // btnNextPage
            // 
            this.btnNextPage.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnNextPage.BackColor = System.Drawing.Color.Transparent;
            this.btnNextPage.BindingForm = null;
            this.btnNextPage.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnNextPage.Location = new System.Drawing.Point(250, 5);
            this.btnNextPage.Name = "btnNextPage";
            this.btnNextPage.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnNextPage.Selectable = true;
            this.btnNextPage.Size = new System.Drawing.Size(26, 27);
            this.btnNextPage.TabIndex = 3;
            this.btnNextPage.tbAdriftIconWhenHover = false;
            this.btnNextPage.tbAutoSize = false;
            this.btnNextPage.tbAutoSizeEx = false;
            this.btnNextPage.tbBackgroundImage = null;
            this.btnNextPage.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnNextPage.tbBadgeNumber = 0;
            this.btnNextPage.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnNextPage.tbEndEllipsis = false;
            this.btnNextPage.tbIconHoldPlace = true;
            this.btnNextPage.tbIconImage = global::iWechatAssistant.Properties.Resources.nextpage;
            this.btnNextPage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnNextPage.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnNextPage.tbIconMore = false;
            this.btnNextPage.tbIconMouseDown = null;
            this.btnNextPage.tbIconMouseHover = null;
            this.btnNextPage.tbIconMouseLeave = null;
            this.btnNextPage.tbIconPlaceText = 2;
            this.btnNextPage.tbIconReadOnly = null;
            this.btnNextPage.tbImageMouseDown = null;
            this.btnNextPage.tbImageMouseHover = null;
            this.btnNextPage.tbImageMouseLeave = null;
            this.btnNextPage.tbProgressValue = 50;
            this.btnNextPage.tbReadOnly = false;
            this.btnNextPage.tbReadOnlyText = false;
            this.btnNextPage.tbShadow = false;
            this.btnNextPage.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnNextPage.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnNextPage.tbShowDot = false;
            this.btnNextPage.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnNextPage.tbShowMoreIconImg")));
            this.btnNextPage.tbShowNew = false;
            this.btnNextPage.tbShowProgress = false;
            this.btnNextPage.tbShowTip = true;
            this.btnNextPage.tbShowToolTipOnButton = false;
            this.btnNextPage.tbSplit = "3,3,3,3";
            this.btnNextPage.tbText = "";
            this.btnNextPage.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnNextPage.tbTextColor = System.Drawing.Color.White;
            this.btnNextPage.tbTextColorDisable = System.Drawing.Color.White;
            this.btnNextPage.tbTextColorDown = System.Drawing.Color.White;
            this.btnNextPage.tbTextColorHover = System.Drawing.Color.White;
            this.btnNextPage.tbTextMouseDownPlace = 0;
            this.btnNextPage.tbToolTip = "";
            this.btnNextPage.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnNextPage.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnNextPage.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnNextPage.VisibleEx = true;
            this.btnNextPage.Click += new System.EventHandler(this.btnNextPage_Click);
            // 
            // btnTrailerPage
            // 
            this.btnTrailerPage.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnTrailerPage.BackColor = System.Drawing.Color.Transparent;
            this.btnTrailerPage.BindingForm = null;
            this.btnTrailerPage.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnTrailerPage.Location = new System.Drawing.Point(282, 5);
            this.btnTrailerPage.Name = "btnTrailerPage";
            this.btnTrailerPage.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnTrailerPage.Selectable = true;
            this.btnTrailerPage.Size = new System.Drawing.Size(26, 27);
            this.btnTrailerPage.TabIndex = 3;
            this.btnTrailerPage.tbAdriftIconWhenHover = false;
            this.btnTrailerPage.tbAutoSize = false;
            this.btnTrailerPage.tbAutoSizeEx = false;
            this.btnTrailerPage.tbBackgroundImage = null;
            this.btnTrailerPage.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnTrailerPage.tbBadgeNumber = 0;
            this.btnTrailerPage.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnTrailerPage.tbEndEllipsis = false;
            this.btnTrailerPage.tbIconHoldPlace = true;
            this.btnTrailerPage.tbIconImage = global::iWechatAssistant.Properties.Resources.trailerpage;
            this.btnTrailerPage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnTrailerPage.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnTrailerPage.tbIconMore = false;
            this.btnTrailerPage.tbIconMouseDown = null;
            this.btnTrailerPage.tbIconMouseHover = null;
            this.btnTrailerPage.tbIconMouseLeave = null;
            this.btnTrailerPage.tbIconPlaceText = 2;
            this.btnTrailerPage.tbIconReadOnly = null;
            this.btnTrailerPage.tbImageMouseDown = null;
            this.btnTrailerPage.tbImageMouseHover = null;
            this.btnTrailerPage.tbImageMouseLeave = null;
            this.btnTrailerPage.tbProgressValue = 50;
            this.btnTrailerPage.tbReadOnly = false;
            this.btnTrailerPage.tbReadOnlyText = false;
            this.btnTrailerPage.tbShadow = false;
            this.btnTrailerPage.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnTrailerPage.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnTrailerPage.tbShowDot = false;
            this.btnTrailerPage.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnTrailerPage.tbShowMoreIconImg")));
            this.btnTrailerPage.tbShowNew = false;
            this.btnTrailerPage.tbShowProgress = false;
            this.btnTrailerPage.tbShowTip = true;
            this.btnTrailerPage.tbShowToolTipOnButton = false;
            this.btnTrailerPage.tbSplit = "3,3,3,3";
            this.btnTrailerPage.tbText = "";
            this.btnTrailerPage.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnTrailerPage.tbTextColor = System.Drawing.Color.White;
            this.btnTrailerPage.tbTextColorDisable = System.Drawing.Color.White;
            this.btnTrailerPage.tbTextColorDown = System.Drawing.Color.White;
            this.btnTrailerPage.tbTextColorHover = System.Drawing.Color.White;
            this.btnTrailerPage.tbTextMouseDownPlace = 0;
            this.btnTrailerPage.tbToolTip = "";
            this.btnTrailerPage.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnTrailerPage.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnTrailerPage.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnTrailerPage.VisibleEx = true;
            this.btnTrailerPage.Click += new System.EventHandler(this.btnTrailerPage_Click);
            // 
            // txtPage
            // 
            this.txtPage.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.txtPage.BackColor = System.Drawing.Color.White;
            this.txtPage.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(189)))), ((int)(((byte)(189)))));
            this.txtPage.BorderColorFocus = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(227)))), ((int)(((byte)(245)))));
            this.txtPage.BorderColorFocusIn = System.Drawing.Color.FromArgb(((int)(((byte)(63)))), ((int)(((byte)(131)))), ((int)(((byte)(209)))));
            this.txtPage.BorderStyle = iTong.Components.tbBorderStyle.None;
            this.txtPage.Font = new System.Drawing.Font("宋体", 8.5F);
            this.txtPage.ForeColor = System.Drawing.Color.Black;
            this.txtPage.ImeMode = System.Windows.Forms.ImeMode.On;
            this.txtPage.Location = new System.Drawing.Point(368, 8);
            this.txtPage.MaxLength = 32767;
            this.txtPage.Name = "txtPage";
            this.txtPage.Size = new System.Drawing.Size(36, 20);
            this.txtPage.TabIndex = 5;
            this.txtPage.Tag = null;
            this.txtPage.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown;
            this.txtPage.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal;
            this.txtPage.tbSelMark = true;
            this.txtPage.tbTextBind = "1";
            this.txtPage.Text = "1";
            this.txtPage.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txtPage.TextFormat = iTong.Components.tbTextFormat.Integer;
            this.txtPage.TextImeMode = System.Windows.Forms.ImeMode.On;
            this.txtPage.TextPadding = new System.Windows.Forms.Padding(3);
            this.txtPage.TextTip = "";
            this.txtPage.KeyDown += new iTong.Components.tbTextBase.KeyDownEventHandler(this.txtPage_KeyDown);
            // 
            // btnGoPage
            // 
            this.btnGoPage.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnGoPage.BackColor = System.Drawing.Color.Transparent;
            this.btnGoPage.BindingForm = null;
            this.btnGoPage.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnGoPage.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.btnGoPage.Location = new System.Drawing.Point(430, 6);
            this.btnGoPage.Name = "btnGoPage";
            this.btnGoPage.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnGoPage.Selectable = true;
            this.btnGoPage.Size = new System.Drawing.Size(46, 25);
            this.btnGoPage.TabIndex = 6;
            this.btnGoPage.tbAdriftIconWhenHover = false;
            this.btnGoPage.tbAutoSize = false;
            this.btnGoPage.tbAutoSizeEx = false;
            this.btnGoPage.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_white;
            this.btnGoPage.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnGoPage.tbBadgeNumber = 0;
            this.btnGoPage.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnGoPage.tbEndEllipsis = false;
            this.btnGoPage.tbIconHoldPlace = true;
            this.btnGoPage.tbIconImage = null;
            this.btnGoPage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnGoPage.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnGoPage.tbIconMore = false;
            this.btnGoPage.tbIconMouseDown = null;
            this.btnGoPage.tbIconMouseHover = null;
            this.btnGoPage.tbIconMouseLeave = null;
            this.btnGoPage.tbIconPlaceText = 2;
            this.btnGoPage.tbIconReadOnly = null;
            this.btnGoPage.tbImageMouseDown = null;
            this.btnGoPage.tbImageMouseHover = null;
            this.btnGoPage.tbImageMouseLeave = null;
            this.btnGoPage.tbProgressValue = 50;
            this.btnGoPage.tbReadOnly = false;
            this.btnGoPage.tbReadOnlyText = false;
            this.btnGoPage.tbShadow = false;
            this.btnGoPage.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnGoPage.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnGoPage.tbShowDot = false;
            this.btnGoPage.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnGoPage.tbShowMoreIconImg")));
            this.btnGoPage.tbShowNew = false;
            this.btnGoPage.tbShowProgress = false;
            this.btnGoPage.tbShowTip = true;
            this.btnGoPage.tbShowToolTipOnButton = false;
            this.btnGoPage.tbSplit = "3,3,3,3";
            this.btnGoPage.tbText = "跳转";
            this.btnGoPage.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnGoPage.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.btnGoPage.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.btnGoPage.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.btnGoPage.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.btnGoPage.tbTextMouseDownPlace = 0;
            this.btnGoPage.tbToolTip = "";
            this.btnGoPage.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnGoPage.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnGoPage.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnGoPage.VisibleEx = true;
            this.btnGoPage.Click += new System.EventHandler(this.btnGoPage_Click);
            // 
            // tbLabel1
            // 
            this.tbLabel1.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.tbLabel1.AutoSize = true;
            this.tbLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.tbLabel1.Location = new System.Drawing.Point(348, 10);
            this.tbLabel1.Name = "tbLabel1";
            this.tbLabel1.Size = new System.Drawing.Size(17, 12);
            this.tbLabel1.TabIndex = 7;
            this.tbLabel1.tbAdriftWhenHover = false;
            this.tbLabel1.tbAutoEllipsis = false;
            this.tbLabel1.tbAutoSize = true;
            this.tbLabel1.tbHideImage = false;
            this.tbLabel1.tbIconImage = null;
            this.tbLabel1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.tbLabel1.tbIconPlaceText = 5;
            this.tbLabel1.tbShadow = false;
            this.tbLabel1.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.tbLabel1.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.tbLabel1.tbShowScrolling = false;
            this.tbLabel1.Text = "第";
            // 
            // tbLabel2
            // 
            this.tbLabel2.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.tbLabel2.AutoSize = true;
            this.tbLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(153)))), ((int)(((byte)(153)))), ((int)(((byte)(153)))));
            this.tbLabel2.Location = new System.Drawing.Point(407, 10);
            this.tbLabel2.Name = "tbLabel2";
            this.tbLabel2.Size = new System.Drawing.Size(17, 12);
            this.tbLabel2.TabIndex = 7;
            this.tbLabel2.tbAdriftWhenHover = false;
            this.tbLabel2.tbAutoEllipsis = false;
            this.tbLabel2.tbAutoSize = true;
            this.tbLabel2.tbHideImage = false;
            this.tbLabel2.tbIconImage = null;
            this.tbLabel2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.tbLabel2.tbIconPlaceText = 5;
            this.tbLabel2.tbShadow = false;
            this.tbLabel2.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.tbLabel2.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.tbLabel2.tbShowScrolling = false;
            this.tbLabel2.Text = "页";
            // 
            // tbPage
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.tbLabel2);
            this.Controls.Add(this.tbLabel1);
            this.Controls.Add(this.btnGoPage);
            this.Controls.Add(this.txtPage);
            this.Controls.Add(this.btnTrailerPage);
            this.Controls.Add(this.btnPreviousPage);
            this.Controls.Add(this.btnNextPage);
            this.Controls.Add(this.btnHomePage);
            this.Controls.Add(this.lblPage);
            this.Name = "tbPage";
            this.Size = new System.Drawing.Size(577, 36);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Label lblPage;
        private iTong.Components.tbButton btnHomePage;
        private iTong.Components.tbButton btnPreviousPage;
        private iTong.Components.tbButton btnNextPage;
        private iTong.Components.tbButton btnTrailerPage;
        private iTong.Components.tbTextBox txtPage;
        private iTong.Components.tbButton btnGoPage;
        private iTong.Components.tbLabel tbLabel1;
        private iTong.Components.tbLabel tbLabel2;

    }
}
