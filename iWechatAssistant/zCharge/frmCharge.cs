﻿using iTong.Components;
using iTong.CoreFoundation;
using iTong.CoreModule;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

namespace iWechatAssistant
{
    public partial class frmCharge : tbBaseGuiForm
    {
        private WechatAccInfo mWAInfo = null;
        private WechatHeplerPC mWechatHeplerPC = null;

        private string mStrName = "";
        private string mStrAccount = "";

        public frmCharge(WechatAccInfo waInfo, WechatHeplerPC wHepler)
        {
            InitializeComponent();
            this.Icon = (Icon)global::iWechatAssistant.Properties.Resources.main.Clone();
            this.mWAInfo = waInfo;
            this.mWechatHeplerPC = wHepler;

            this.tbGuiBackground = (Image)global::iWechatAssistant.Properties.Resources.frm_bg_sub.Clone();
            this.btnBuyCDKEY.tbBackgroundImage = (Image)global::iWechatAssistant.Properties.Resources.btn_4_blue.Clone();
            this.btnActivate.tbBackgroundImage = (Image)global::iWechatAssistant.Properties.Resources.btn_4_blue.Clone();

            WechatHeplerPC.ActivateAccountHandler -= mWechatHeplerPC_ActivateAccountHandler;
            WechatHeplerPC.ActivateAccountHandler += mWechatHeplerPC_ActivateAccountHandler;


            string strName = waInfo.Name;
            mStrName = waInfo.Name;
            if (strName.Length > 4)
            {
                strName = strName.Substring(0, 4) + "...";
            }

            mStrAccount = waInfo.InnerAccount.Length > 0 ? waInfo.InnerAccount : waInfo.Account;

            if (waInfo.AState == ActivateState.Nonactivated)
            {
                this.lblTitle.Text = string.Format("当前帐号【{0}】昵称【{1}】\n\r尚未激活：购买激活后即可解锁功能。", mStrAccount, strName);
                this.Text = "激活";
                this.llblExportPreview.Visible = true;
                this.pbExportPreview.Visible = true;
            }
            else if (waInfo.AState == ActivateState.Overdue)
            {
                this.lblTitle.Text = string.Format("当前帐号【{0}】昵称【{1}】\n\r激活已过期：重新购买激活后即可解锁功能。", mStrAccount, strName);
                this.Text = "续期（已过期）";
                this.llblExportPreview.Visible = true;
                this.pbExportPreview.Visible = true;
            }
            else
            {
                this.lblTitle.Text = String.Format("当前帐号【{1}】昵称【{2}】\n\r状态正常，过期时间：{0}", waInfo.EndTime.ToString("yyyy-MM-dd"), mStrAccount, strName);
                this.Text = "续期（未过期）";
                this.llblExportPreview.Visible = false;
                this.pbExportPreview.Visible = false;
            }

            this.txtDescribe.Clear();
            this.txtDescribe.AppendText(Environment.NewLine);
            this.txtDescribe.AppendText("解锁后可以查看备份的信息，可以把数据导出到电脑（html，txt，excel）");
            this.txtDescribe.AppendText(Environment.NewLine);
            this.txtDescribe.AppendText(Environment.NewLine);
            this.txtDescribe.AppendText("点击下方的‘购买激活码’按钮即可在线购买激活码，购买成功几分钟后，您填写的邮箱将会收到含有激活码的邮件。");
            this.txtDescribe.AppendText(Environment.NewLine);
            this.txtDescribe.AppendText(Environment.NewLine);
            string strT = string.Format("非自助授权激活后需要联系客服对当前帐号【{0}】昵称【{1}】授权后才可以管理。", mStrAccount, waInfo.Name);
            this.txtDescribe.Select(this.txtDescribe.Text.Length, strT.Length);
            this.txtDescribe.SelectionColor = Color.Red;
            this.txtDescribe.AppendText(strT);
            this.txtDescribe.AppendText(Environment.NewLine);
            this.txtDescribe.ScrollToCaret();

        }

        private void mWechatHeplerPC_ActivateAccountHandler(object sender, ActivateAccountEventArgs e)
        {
            ActivateAccount(e.IsSucceed,e.TypeEx );
        }

        private delegate void ActivateAccountHandler(bool IsSucceed, int typeex);
        private void ActivateAccount(bool IsSucceed,int typeex)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new ActivateAccountHandler(ActivateAccount), IsSucceed, typeex);
                }
                else
                {

                    try
                    {
                        if (IsSucceed)
                        {
                            this.mWAInfo.TypeEx = typeex;
                            DialogResult = System.Windows.Forms.DialogResult.OK;
                            //this.Close();
                        }
                        else
                        {
                            this.pbLoading.Visible = false;
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "ActivateAccount");
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString());
            }
        }

        private void btnBuyCDKEY_Click(object sender, EventArgs e)
        {
            try
            {
                string strChannel = System.Web.HttpUtility.UrlEncode(Common.GetChannelID());
                string strUrlResult = ServerIniSetting.GetiWechatAssistantPurchase();
                //和web约定，现在的链接中用os来代表渠道号，s就可以去掉了
                if (!strUrlResult.Contains(String.Format("c_s={0}", strChannel)))
                {
                    if (strUrlResult.Contains("?"))
                        strUrlResult = String.Format("{0}&c_s={1}", strUrlResult, strChannel);
                    else
                        strUrlResult = String.Format("{0}?c_s={1}", strUrlResult, strChannel);
                }
                Common.OpenExplorer(strUrlResult);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "btnBuyCDKEY_Click");
            }
        }

        private void btnActivate_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(this.txtCDKEY.Text))
                {
                    MessageBox.Show(this, "请输入激活码。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.txtCDKEY.Focus();
                    return;
                }

                //确认绑定信息
                string strAccountMsg = string.Format("确认绑定当前帐号【{0}】昵称【{1}】到此电脑。\n\r提醒：一个激活码只能绑定一个微信。如果绑定错误， 需要重新购买（除高级版外）。", mStrAccount, mStrName);
                if (MessageBox.Show(this, strAccountMsg, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) != System.Windows.Forms.DialogResult.OK)
                    return;

                this.pbLoading.Visible = true;
                WechatHeplerPC.ActivateAccount(this.mWAInfo, this.txtCDKEY.Text, false);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "btnActivate_Click");
            }
        }

        private void frmCharge_FormClosing(object sender, FormClosingEventArgs e)
        {
            WechatHeplerPC.ActivateAccountHandler -= mWechatHeplerPC_ActivateAccountHandler;
        }

        private void llblExportPreview_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            ExportHelper.ExportPreview();
        }
    }
}
