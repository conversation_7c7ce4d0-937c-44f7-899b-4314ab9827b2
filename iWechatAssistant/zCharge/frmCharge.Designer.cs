﻿namespace iWechatAssistant
{
    partial class frmCharge
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmCharge));
            this.tbPanel1 = new iTong.Components.tbPanel();
            this.pbExportPreview = new System.Windows.Forms.PictureBox();
            this.llblExportPreview = new System.Windows.Forms.LinkLabel();
            this.lblTitle = new System.Windows.Forms.Label();
            this.txtDescribe = new System.Windows.Forms.RichTextBox();
            this.pbLoading = new System.Windows.Forms.PictureBox();
            this.txtCDKEY = new System.Windows.Forms.TextBox();
            this.btnBuyCDKEY = new iTong.Components.tbButton();
            this.btnActivate = new iTong.Components.tbButton();
            this.label1 = new System.Windows.Forms.Label();
            this.btn_close = new iTong.Components.tbButton();
            this.tbPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pbExportPreview)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.pbLoading)).BeginInit();
            this.SuspendLayout();
            // 
            // tbPanel1
            // 
            this.tbPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.tbPanel1.Controls.Add(this.pbExportPreview);
            this.tbPanel1.Controls.Add(this.llblExportPreview);
            this.tbPanel1.Controls.Add(this.lblTitle);
            this.tbPanel1.Controls.Add(this.txtDescribe);
            this.tbPanel1.Controls.Add(this.pbLoading);
            this.tbPanel1.Controls.Add(this.txtCDKEY);
            this.tbPanel1.Controls.Add(this.btnBuyCDKEY);
            this.tbPanel1.Controls.Add(this.btnActivate);
            this.tbPanel1.Controls.Add(this.label1);
            this.tbPanel1.Location = new System.Drawing.Point(1, 37);
            this.tbPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tbPanel1.Name = "tbPanel1";
            this.tbPanel1.Size = new System.Drawing.Size(538, 332);
            this.tbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.tbPanel1.TabIndex = 77;
            this.tbPanel1.tbBackgroundImage = null;
            this.tbPanel1.tbShowWatermark = false;
            this.tbPanel1.tbSplit = "0,0,0,0";
            this.tbPanel1.tbWatermark = null;
            this.tbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.tbPanel1.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // pbExportPreview
            // 
            this.pbExportPreview.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pbExportPreview.Image = global::iWechatAssistant.Properties.Resources.icon_export;
            this.pbExportPreview.Location = new System.Drawing.Point(104, 292);
            this.pbExportPreview.Name = "pbExportPreview";
            this.pbExportPreview.Size = new System.Drawing.Size(12, 12);
            this.pbExportPreview.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage;
            this.pbExportPreview.TabIndex = 107;
            this.pbExportPreview.TabStop = false;
            // 
            // llblExportPreview
            // 
            this.llblExportPreview.ActiveLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(115)))), ((int)(((byte)(255)))));
            this.llblExportPreview.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.llblExportPreview.AutoSize = true;
            this.llblExportPreview.DisabledLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(115)))), ((int)(((byte)(255)))));
            this.llblExportPreview.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline;
            this.llblExportPreview.LinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(115)))), ((int)(((byte)(255)))));
            this.llblExportPreview.Location = new System.Drawing.Point(45, 290);
            this.llblExportPreview.Name = "llblExportPreview";
            this.llblExportPreview.Size = new System.Drawing.Size(53, 12);
            this.llblExportPreview.TabIndex = 106;
            this.llblExportPreview.TabStop = true;
            this.llblExportPreview.Text = "导出预览";
            this.llblExportPreview.VisitedLinkColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(115)))), ((int)(((byte)(255)))));
            this.llblExportPreview.LinkClicked += new System.Windows.Forms.LinkLabelLinkClickedEventHandler(this.llblExportPreview_LinkClicked);
            // 
            // lblTitle
            // 
            this.lblTitle.AutoSize = true;
            this.lblTitle.BackColor = System.Drawing.Color.Transparent;
            this.lblTitle.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTitle.Location = new System.Drawing.Point(44, 22);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(76, 16);
            this.lblTitle.TabIndex = 83;
            this.lblTitle.Text = "激活码：";
            // 
            // txtDescribe
            // 
            this.txtDescribe.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtDescribe.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.txtDescribe.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.txtDescribe.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(102)))), ((int)(((byte)(102)))), ((int)(((byte)(102)))));
            this.txtDescribe.Location = new System.Drawing.Point(46, 70);
            this.txtDescribe.Margin = new System.Windows.Forms.Padding(0);
            this.txtDescribe.Name = "txtDescribe";
            this.txtDescribe.ReadOnly = true;
            this.txtDescribe.Size = new System.Drawing.Size(446, 151);
            this.txtDescribe.TabIndex = 82;
            this.txtDescribe.Text = "您当前是试用版：仅支持查看聊天记录，不支持导出功能，不支持查看图片大图，不支持查看删除找回数据，购买正式版即可解锁所有功能。\n";
            // 
            // pbLoading
            // 
            this.pbLoading.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.pbLoading.BackColor = System.Drawing.Color.Transparent;
            this.pbLoading.Image = global::iWechatAssistant.Properties.Resources.gif_loading_16;
            this.pbLoading.Location = new System.Drawing.Point(282, 290);
            this.pbLoading.Margin = new System.Windows.Forms.Padding(0);
            this.pbLoading.Name = "pbLoading";
            this.pbLoading.Size = new System.Drawing.Size(16, 16);
            this.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom;
            this.pbLoading.TabIndex = 81;
            this.pbLoading.TabStop = false;
            this.pbLoading.Visible = false;
            // 
            // txtCDKEY
            // 
            this.txtCDKEY.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCDKEY.Location = new System.Drawing.Point(46, 247);
            this.txtCDKEY.Name = "txtCDKEY";
            this.txtCDKEY.Size = new System.Drawing.Size(449, 21);
            this.txtCDKEY.TabIndex = 77;
            // 
            // btnBuyCDKEY
            // 
            this.btnBuyCDKEY.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBuyCDKEY.BackColor = System.Drawing.Color.Transparent;
            this.btnBuyCDKEY.BindingForm = null;
            this.btnBuyCDKEY.Location = new System.Drawing.Point(309, 287);
            this.btnBuyCDKEY.Name = "btnBuyCDKEY";
            this.btnBuyCDKEY.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnBuyCDKEY.Selectable = true;
            this.btnBuyCDKEY.Size = new System.Drawing.Size(87, 23);
            this.btnBuyCDKEY.TabIndex = 79;
            this.btnBuyCDKEY.tbAdriftIconWhenHover = false;
            this.btnBuyCDKEY.tbAutoSize = false;
            this.btnBuyCDKEY.tbAutoSizeEx = false;
            this.btnBuyCDKEY.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnBuyCDKEY.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnBuyCDKEY.tbBadgeNumber = 0;
            this.btnBuyCDKEY.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnBuyCDKEY.tbEndEllipsis = false;
            this.btnBuyCDKEY.tbIconHoldPlace = true;
            this.btnBuyCDKEY.tbIconImage = null;
            this.btnBuyCDKEY.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnBuyCDKEY.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnBuyCDKEY.tbIconMore = false;
            this.btnBuyCDKEY.tbIconMouseDown = null;
            this.btnBuyCDKEY.tbIconMouseHover = null;
            this.btnBuyCDKEY.tbIconMouseLeave = null;
            this.btnBuyCDKEY.tbIconPlaceText = 2;
            this.btnBuyCDKEY.tbIconReadOnly = null;
            this.btnBuyCDKEY.tbImageMouseDown = null;
            this.btnBuyCDKEY.tbImageMouseHover = null;
            this.btnBuyCDKEY.tbImageMouseLeave = null;
            this.btnBuyCDKEY.tbProgressValue = 50;
            this.btnBuyCDKEY.tbReadOnly = false;
            this.btnBuyCDKEY.tbReadOnlyText = false;
            this.btnBuyCDKEY.tbShadow = false;
            this.btnBuyCDKEY.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnBuyCDKEY.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnBuyCDKEY.tbShowDot = false;
            this.btnBuyCDKEY.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnBuyCDKEY.tbShowMoreIconImg")));
            this.btnBuyCDKEY.tbShowNew = false;
            this.btnBuyCDKEY.tbShowProgress = false;
            this.btnBuyCDKEY.tbShowTip = true;
            this.btnBuyCDKEY.tbShowToolTipOnButton = false;
            this.btnBuyCDKEY.tbSplit = "3,3,3,3";
            this.btnBuyCDKEY.tbText = "购买激活码";
            this.btnBuyCDKEY.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnBuyCDKEY.tbTextColor = System.Drawing.Color.White;
            this.btnBuyCDKEY.tbTextColorDisable = System.Drawing.Color.White;
            this.btnBuyCDKEY.tbTextColorDown = System.Drawing.Color.White;
            this.btnBuyCDKEY.tbTextColorHover = System.Drawing.Color.White;
            this.btnBuyCDKEY.tbTextMouseDownPlace = 0;
            this.btnBuyCDKEY.tbToolTip = "";
            this.btnBuyCDKEY.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnBuyCDKEY.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnBuyCDKEY.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnBuyCDKEY.VisibleEx = true;
            this.btnBuyCDKEY.Click += new System.EventHandler(this.btnBuyCDKEY_Click);
            // 
            // btnActivate
            // 
            this.btnActivate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnActivate.BackColor = System.Drawing.Color.Transparent;
            this.btnActivate.BindingForm = null;
            this.btnActivate.Location = new System.Drawing.Point(408, 287);
            this.btnActivate.Name = "btnActivate";
            this.btnActivate.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnActivate.Selectable = true;
            this.btnActivate.Size = new System.Drawing.Size(87, 23);
            this.btnActivate.TabIndex = 80;
            this.btnActivate.tbAdriftIconWhenHover = false;
            this.btnActivate.tbAutoSize = false;
            this.btnActivate.tbAutoSizeEx = false;
            this.btnActivate.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_4_blue;
            this.btnActivate.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnActivate.tbBadgeNumber = 0;
            this.btnActivate.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnActivate.tbEndEllipsis = false;
            this.btnActivate.tbIconHoldPlace = true;
            this.btnActivate.tbIconImage = null;
            this.btnActivate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnActivate.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnActivate.tbIconMore = false;
            this.btnActivate.tbIconMouseDown = null;
            this.btnActivate.tbIconMouseHover = null;
            this.btnActivate.tbIconMouseLeave = null;
            this.btnActivate.tbIconPlaceText = 2;
            this.btnActivate.tbIconReadOnly = null;
            this.btnActivate.tbImageMouseDown = null;
            this.btnActivate.tbImageMouseHover = null;
            this.btnActivate.tbImageMouseLeave = null;
            this.btnActivate.tbProgressValue = 50;
            this.btnActivate.tbReadOnly = false;
            this.btnActivate.tbReadOnlyText = false;
            this.btnActivate.tbShadow = false;
            this.btnActivate.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnActivate.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnActivate.tbShowDot = false;
            this.btnActivate.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnActivate.tbShowMoreIconImg")));
            this.btnActivate.tbShowNew = false;
            this.btnActivate.tbShowProgress = false;
            this.btnActivate.tbShowTip = true;
            this.btnActivate.tbShowToolTipOnButton = false;
            this.btnActivate.tbSplit = "3,3,3,3";
            this.btnActivate.tbText = "激活";
            this.btnActivate.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnActivate.tbTextColor = System.Drawing.Color.White;
            this.btnActivate.tbTextColorDisable = System.Drawing.Color.White;
            this.btnActivate.tbTextColorDown = System.Drawing.Color.White;
            this.btnActivate.tbTextColorHover = System.Drawing.Color.White;
            this.btnActivate.tbTextMouseDownPlace = 0;
            this.btnActivate.tbToolTip = "";
            this.btnActivate.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnActivate.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnActivate.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnActivate.VisibleEx = true;
            this.btnActivate.Click += new System.EventHandler(this.btnActivate_Click);
            // 
            // label1
            // 
            this.label1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.label1.AutoSize = true;
            this.label1.BackColor = System.Drawing.Color.Transparent;
            this.label1.Location = new System.Drawing.Point(44, 226);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 78;
            this.label1.Text = "激活码：";
            // 
            // btn_close
            // 
            this.btn_close.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_close.BackColor = System.Drawing.Color.Transparent;
            this.btn_close.BindingForm = null;
            this.btn_close.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_close.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_close.Location = new System.Drawing.Point(502, 5);
            this.btn_close.Name = "btn_close";
            this.btn_close.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_close.Selectable = true;
            this.btn_close.Size = new System.Drawing.Size(26, 26);
            this.btn_close.TabIndex = 78;
            this.btn_close.tbAdriftIconWhenHover = false;
            this.btn_close.tbAutoSize = true;
            this.btn_close.tbAutoSizeEx = false;
            this.btn_close.tbBackgroundImage = global::iWechatAssistant.Properties.Resources.btn_close;
            this.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbBadgeNumber = 0;
            this.btn_close.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_close.tbEndEllipsis = false;
            this.btn_close.tbIconHoldPlace = true;
            this.btn_close.tbIconImage = null;
            this.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbIconMore = false;
            this.btn_close.tbIconMouseDown = null;
            this.btn_close.tbIconMouseHover = null;
            this.btn_close.tbIconMouseLeave = null;
            this.btn_close.tbIconPlaceText = 2;
            this.btn_close.tbIconReadOnly = null;
            this.btn_close.tbImageMouseDown = null;
            this.btn_close.tbImageMouseHover = null;
            this.btn_close.tbImageMouseLeave = null;
            this.btn_close.tbProgressValue = 50;
            this.btn_close.tbReadOnly = false;
            this.btn_close.tbReadOnlyText = false;
            this.btn_close.tbShadow = false;
            this.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_close.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_close.tbShowDot = false;
            this.btn_close.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_close.tbShowMoreIconImg")));
            this.btn_close.tbShowNew = false;
            this.btn_close.tbShowProgress = false;
            this.btn_close.tbShowTip = true;
            this.btn_close.tbShowToolTipOnButton = false;
            this.btn_close.tbSplit = "3,3,3,3";
            this.btn_close.tbText = "";
            this.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbTextColor = System.Drawing.Color.White;
            this.btn_close.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_close.tbTextColorDown = System.Drawing.Color.White;
            this.btn_close.tbTextColorHover = System.Drawing.Color.White;
            this.btn_close.tbTextMouseDownPlace = 0;
            this.btn_close.tbToolTip = "";
            this.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_close.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.VisibleEx = true;
            // 
            // frmCharge
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(540, 370);
            this.Controls.Add(this.btn_close);
            this.Controls.Add(this.tbPanel1);
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(540, 370);
            this.MinimizeBox = false;
            this.MinimumSize = new System.Drawing.Size(540, 370);
            this.Name = "frmCharge";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.tbGuiBackground = global::iWechatAssistant.Properties.Resources.frm_bg_sub;
            this.tbShowTitleOnForm = true;
            this.tbSplit = "6,39,7,28";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.tbTitleForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(104)))), ((int)(((byte)(104)))));
            this.tbTitleLocation = new System.Drawing.Point(4, 10);
            this.Text = "激活";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.frmCharge_FormClosing);
            this.tbPanel1.ResumeLayout(false);
            this.tbPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pbExportPreview)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.pbLoading)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        private iTong.Components.tbPanel tbPanel1;
        private System.Windows.Forms.Label lblTitle;
        internal System.Windows.Forms.RichTextBox txtDescribe;
        internal System.Windows.Forms.PictureBox pbLoading;
        private System.Windows.Forms.TextBox txtCDKEY;
        private iTong.Components.tbButton btnBuyCDKEY;
        private iTong.Components.tbButton btnActivate;
        private System.Windows.Forms.Label label1;
        internal iTong.Components.tbButton btn_close;
        private System.Windows.Forms.LinkLabel llblExportPreview;
        private System.Windows.Forms.PictureBox pbExportPreview;

    }
}