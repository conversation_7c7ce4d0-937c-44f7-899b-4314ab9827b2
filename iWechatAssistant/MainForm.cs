﻿using Microsoft.VisualBasic;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Management;
using System.Text;
using System.Threading;
using System.Web;
using System.Windows.Forms;
using System.Linq;
using System.Xml;

//using Bamboo.ClientLib;
using iTong.CoreModule;
using iTong.Components;
using iTong.CoreFoundation;
using iTong.CoreCefSharp;
using GroupRoomData;
using Microsoft.Win32;
using System.Threading.Tasks;
using System.Runtime.InteropServices;

#if !NET46
using WebNavigatingEventArgs = iTong.CoreCefSharp.CefWebBrowserNavigatingEventArgs;
#endif

namespace iWechatAssistant
{
    public partial class MainForm : tbBaseGuiForm
    {
        private tbDataGridView dgvFriend = null;
        private tbDataGridView dgvAccount = null;
        private DataGridViewRow mCurrentDataGridViewRow = null;
        //private DataGridViewRow mCurrentAccountRow = null;
        private tbContextMenuStrip mMenuDevice = new tbContextMenuStrip();

        private Thread mTdGetChat = null;
        private Thread mTdLoadIcon = null;
        private Thread mTdWechatAccountFromPC = null;
        private Thread mTdSearchFriend = null;
        private Thread mTdCreateError;
        private Thread mTdLoadHeadIcon = null;
        private Thread mTdGetBackupInfoByComputer = null;

        private Thread m_threadDown = null;
        private BackgroundWorker m_bgwLoadFriends = null;

        // 缓存当前界面的信息
        //private WechatAccInfo mCurrentWAInfo = null;
        //private WechatTalk mCurrentWechatTalk = null;
        private IPluginPlayer mPlayer = null;
        private WechatHeplerPC mWechatHeplerPC = null;
        private ExportHelper mExportHelper = null;
        //private WechatTalk mScreenTalk = null;
        private WeChatMsgWebPage mWeChatMsgWebPage = null;

        //private List<WechatMessageItem> mLstCurrentShowWechatMessageItem = new List<WechatMessageItem>();
        //private Dictionary<string, WechatTalk> mDictWechatTalk = new Dictionary<string, WechatTalk>(StringComparer.InvariantCultureIgnoreCase);
        //private Dictionary<string, WechatTalk> mDictWechatTalkEx = new Dictionary<string, WechatTalk>();

        private Font mFontF = Common.CreateFont("宋体", 9, FontStyle.Regular);
        private Font mFontF10 = Common.CreateFont("宋体", 10, FontStyle.Regular);
        private Image mDefaultHeadIcon = Properties.Resources.weixin_icon_default;
        private Size mAppIconSize = new Size(44, 44);
        private Graphics mGraphic = null;

        private string mCurrentBackupDBPath = "";
        //private string mCurrentBackupDBPwd = "";
        private string mCurrentBackupMediaFile = "";
        private string mCurrentSelectPath = "";
        private string mWechatPath = "";
        private string mStrWechatRecovery = "微信恢复大师";
        private string mStrWechatManager = "微信管理大师";
        private string mStrVoiceMerge = "语音合并助手";
        private string mCurrentRecommend = "";
        private string mStrMsgId = "";
        private string mStrMsgDetailId = "";

        private bool mIsScreenByTime = false;
        private bool mIsSelectAllClick = false;
        private bool mIsAddRow = false;
        private bool mIsLoadHeadIconFinish = false;
        private bool mIsFirstRunAfterUpgrade = false;     //是否是升级后第一次运行
        private bool mIsFirstRecommend = true;
        private bool mIsShowSetRecommend = false;
        private bool mIsLoadBackupInfoByComputer = false;

        private bool mWeChatiOSDevice = true;
        private string mLoading = "加载中...";

        private CefWebBrowser wbsChat = null;
        private CefWebBrowser wbsChatDetail = null;

        private bool m_bIsInitialTime = false;

        private bool m_bIsDebug = false;

        private Point mLoadPicturePoint;

        private Point mLoadLblPoint;

        #region "---  初始化  ---"


        public MainForm()
        {
            InitializeComponent();

            Language = LanguageInterface.Instance();

            const string compiledMonth = "10";
            const string compiledDay = "23";
            const string compiledCount = "17";
            const string compiledCountPerMonth = "2";

            Common.Log(string.Format("MainForm (v{0}.{1}.{2}.{3}.{4}) to start main form on {5} system.",
                                    compiledMonth, compiledDay, compiledCount, compiledCountPerMonth,
                                    Common.GetSoftVersion(), OSHelper.OSType), true);
            try
            {
                m_bIsDebug = File.Exists(Path.Combine(Folder.AppFolder, "debug.dll"));
            }
            catch { }

            Application.ThreadException += OnThreadException;
            AppDomain.CurrentDomain.UnhandledException += UnhandledException;
            CheckCacheDbDirectory();
        }

        private void CheckCacheDbDirectory()
        {
            //判断临时数据库默认保存路径是否有配置 是否存在  如果不存在寻找一个剩余最大盘符
            string strDBFolder = IniSetting.GetWechatAssistantDBFolder();

            bool deleteCache = IniSetting.GetIsDeleteChache();

            if (string.IsNullOrEmpty(strDBFolder))
            {
                string strTemp = Path.Combine(Folder.MaxDrives(), "WechatAssistantDB");
                Folder.CheckFolder(strTemp);
                IniSetting.SetWechatAssistantDBFolder(strTemp);
            }
            else
            {
                try
                {
                    if (deleteCache)
                    {
                        Directory.Delete(strDBFolder, true);
                    }
                    else if (Directory.Exists(Path.Combine(strDBFolder, "cache")))
                    {
                        Directory.Delete(Path.Combine(strDBFolder, "cache"), true);
                    }
                }
                catch
                {

                }
                Folder.CheckFolder(strDBFolder);

                if (!Directory.Exists(strDBFolder))
                {
                    IniSetting.SetWechatAssistantDBFolder("");
                    CheckCacheDbDirectory();
                }
            }
        }

        //protected override void BeforeFormClose(ref bool pblnCancelClose)
        //{
        //    base.BeforeFormClose(ref pblnCancelClose);

        //}

        //protected override void OnClosed(EventArgs e)
        //{
        //    base.OnClosed(e);
        //    string strDBFolder = IniSetting.GetWechatAssistantDBFolder();
        //    if (!string.IsNullOrEmpty(strDBFolder) && Directory.Exists(strDBFolder))
        //        Directory.Delete(strDBFolder, true);
        //}

        protected override void InitControls()
        {
            base.InitControls();

            this.Size = new Size(980, 660);
            this.Icon = global::iWechatAssistant.Properties.Resources.main;

            this.SetViewStyle(MViewType.Welcome);

            this.InitWebBrowser();

            this.InitDataGridViewAccount();
            this.InitDataGridViewFriend();
            this.InitMusicPlayer();

            this.tbbRecommend.Visible = ServerIniSetting.RecommendVoiceMerger();
            this.pbRecommend.Visible = ServerIniSetting.RecommendVoiceMerger();
            this.lblRegister.TextClick += this.lblRegister_TextClick;
            this.lblRecommend.TextClick += lblRecommend_TextClick;
            this.tbbRecommend.TextClick += this.tbbRecommend_TextClick;

            this.tbpPage.HomePageCallback += tbpPage_HomePageCallback;
            this.tbpPage.PreviousPageCallback += tbpPage_PreviousPageCallback;
            this.tbpPage.NextPageCallback += tbpPage_NextPageCallback;
            this.tbpPage.TrailerPageCallback += tbpPage_TrailerPageCallback;
            this.tbpPage.GotoPageCallback += tbpPage_GotoPageCallback;

            this.txtWechatPath.Text = IniClass.GetIniSectionKey("Setting", "WeChatBackFolder", Folder.ConfigIniFile);
            if (string.IsNullOrEmpty(this.txtWechatPath.Text))
            {
                this.GetPCWeChatBackupPath();

                if (string.IsNullOrEmpty(this.txtWechatPath.Text))
                {
                    this.txtWechatPath.Text = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "WeChat Files");
                }
            }
            this.mWechatPath = this.txtWechatPath.Text;
            WechatHeplerPC.LoadServerSettingHandler += WechatHeplerPC_LoadServerSettingHandler;
            this.tmrTimer.Start();
        }

        //private void AdjustChatPosition(bool bVisible)
        //{
        //    //this.BeginInvoke(new MethodInvoker(delegate
        //    //{
        //    //tbPanel12.Size = new Size(tblayoutMain.Width, tblayoutMain.Height - (int)tblayoutMain.RowStyles[1].Height);
        //    //plnShowChat.Size = new Size(tbPanel12.Width, tbPanel12.Height - pnlTop.Height);
        //    //tbPanel5.Size = new Size(plnShowChat.Width, plnShowChat.Height - tbPanel6.Height);
        //    //tbPanel3.Size = new Size(tbPanel5.Width - pnlFriend.Width, pnlFriend.Height);

        //    //    if (bVisible)
        //    //    {
        //    //        wbsChat.Size = new Size(tbPanel3.Width, tbPanel3.Height - pnlPage.Height);
        //    //        wbsChatDetail.Size = new Size(tbPanel3.Width, tbPanel3.Height - pnlPage.Height);
        //    //    }
        //    //    else
        //    //    {
        //    //        wbsChat.Size = new Size(tbPanel3.Width, tbPanel3.Height);
        //    //        wbsChatDetail.Size = new Size(tbPanel3.Width, tbPanel3.Height);
        //    //    }
        //    //})); 
        //}
        private void InitWebBrowser()
        {
            string foldPath = Folder.LogFolder;
            Folder.CheckFolder(foldPath);

            if (!ChatWebPage.InitChatWebPage(false)) return;

            this.wbsChat = new CefWebBrowser(ChatWebPage.WebPagePath)
            {
                Name = "wbsChat",
                Visible = false,
                IsResized = true,
            };
            this.wbsChatDetail = new CefWebBrowser(ChatWebPage.WebPagePathDetail)
            {
                Name = "wbsChatDetail",
                Visible = false,
                IsResized = true,
            };

            this.tbPanel3.Controls.Add(this.wbsChat);
            this.tbPanel3.Controls.Add(this.wbsChatDetail);

            this.wbsChat.NewMessage += wbsChat_NewMessage;
            this.wbsChatDetail.NewMessage += wbsChatDetail_NewMessage;

            this.wbsChat.Navigating += wbsChat_Navigating;
            this.wbsChatDetail.Navigating += wbsChatDetail_Navigating;

            //this.wbsChat.LoadStateChanged += wbsChat_LoadStateChanged;
            //this.wbsChatDetail.LoadStateChanged += wbsChatDetail_LoadStateChanged;
        }

        protected override void SetInterface()
        {
            base.SetInterface();
            //  System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString()
            this.Text = String.Format("{0} v{1}", this.Language.GetString("Common.SoftwareName"), Common.GetSoftVersion()); //"微信备份助手"
            this.txtSearchChat.SearchTipText = "按回车搜索记录";

            this.rtxtAccredit.Clear();

            this.AppendText(this.rtxtAccredit, "此账号尚未授权。", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "(人工授权)", Color.Red);

            this.AppendText(this.rtxtAccredit, "电脑端备份的聊天记录查看是需要", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "手机微信协助", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "授权的，需要您微信扫描客服给的二维码登录一下即可开通。", Color.Black);

            this.AppendText(this.rtxtAccredit, "如果需要好友", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "昵称，备注，头像", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "等信息，必须在扫码后", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "等待一分钟", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "，你的手机上会显示MAC登入，", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "电脑端会下线。这些都", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "不要做任何操作", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "。等待一分钟后即可正常操作。", Color.Black);

            this.AppendText(this.rtxtAccredit, "您可以点击查看电脑登录的权限，", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "不涉及提现", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "，所以对您的账号", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "不会有安全问题", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "。", Color.Black);

            this.AppendText(this.rtxtAccredit, "请点击下方“", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "联系客服", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "“加入QQ群找到授权客服”", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "a授权客服", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "”进行授权，", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "等待一分钟,", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "后点击“", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "已授权，继续", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "”就可以查看数据了。", Color.Black);

            this.AppendText(this.rtxtAccredit, "（授权在线支持：", Color.Black, false);
            this.AppendText(this.rtxtAccredit, "周一至周五10：00-12：00 13：30-18：30  周六14：00-18：00 ", Color.Red, false);
            this.AppendText(this.rtxtAccredit, "周日休息，法定节假日另行通知）", Color.Black);

            this.rtxtAccreditEx.Clear();
            this.AppendText(this.rtxtAccreditEx, "此账号尚未授权。", Color.Black, false);
            this.AppendText(this.rtxtAccreditEx, "(自助授权)", Color.Red);

            this.AppendText(this.rtxtAccreditEx, "电脑端备份的聊天记录查看是需要", Color.Black, false);
            this.AppendText(this.rtxtAccreditEx, "微信PC(电脑)版", Color.Red, false);
            this.AppendText(this.rtxtAccreditEx, "协助授权的，需要您打开电脑上的微信登入您", Color.Black, false);
            this.AppendText(this.rtxtAccreditEx, "要查看的", Color.Red, false);
            this.AppendText(this.rtxtAccreditEx, "微信帐号。", Color.Black);

            this.AppendText(this.rtxtAccreditEx, "登入成功后请点击下方", Color.Black, false);
            this.AppendText(this.rtxtAccreditEx, "“自助授权”", Color.Red, false);
            this.AppendText(this.rtxtAccreditEx, "按钮，整个授权过程中", Color.Black, false);
            this.AppendText(this.rtxtAccreditEx, "请勿关闭微信PC(电脑)版，请勿退出当前登入帐号", Color.Red, false);
            this.AppendText(this.rtxtAccreditEx, "直到提示授权成功。", Color.Black);

            this.AppendText(this.rtxtAccreditEx, "如果多次尝试自助授权都失败，请点击下方", Color.Black, false);
            this.AppendText(this.rtxtAccreditEx, "“人工授权”", Color.Red, false);
            this.AppendText(this.rtxtAccreditEx, "按钮，联系客服（客服授权有上班时间，请先了解）进行授权。", Color.Black, false);

            this.tbbRecommend.ColorTextList.Clear();
            this.tbbRecommend.ColorTextList.AddItem("导出语音记录需合并，请使用", 0, System.Drawing.Color.FromArgb(81, 81, 81), mFontF10, false);
            this.tbbRecommend.ColorTextList.AddItem("语音合并助手", 1, System.Drawing.Color.FromArgb(52, 115, 255), mFontF10, false, true);
            this.tbbRecommend.ColorTextList.AddItem("无损合并，免费试用中...", 2, System.Drawing.Color.FromArgb(81, 81, 81), mFontF10, false);

            this.lblRegister.ColorTextList.Clear();
            Font fontF = Common.CreateFont("Arial", 9, FontStyle.Regular);
            this.lblRegister.ColorTextList.AddItem("试用版预览部分内容用「*」代替 ", 0, System.Drawing.Color.FromArgb(51, 51, 51), fontF, false);
            this.lblRegister.ColorTextList.AddItem("立即购买", 1, System.Drawing.Color.FromArgb(52, 115, 255), fontF, false, true);

            this.richTextBox2.Text = string.Format("{0}是全球第一款可以同时管理iPhone，Android手机微信备份的工具。\n\n您只需要通过电脑版微信先备份手机上的微信数据，就可以通过{0}进行管理。", Language.GetString("Common.SoftwareName"));
            this.lblWelcone.Text = "欢迎使用" + Language.GetString("Common.SoftwareName");
            this.mNotifyIcon.Text = Language.GetString("Common.SoftwareName");

        }

        private delegate void AppendTextHandler(RichTextBox rtx, string strText, Color c, bool isNewline);
        private void AppendText(RichTextBox rtx, string strText, Color c, bool isNewline = true)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new AppendTextHandler(AppendText), rtx, strText, c, isNewline);
            }
            else
            {
                rtx.Select(rtx.Text.Length, strText.Length);
                rtx.SelectionColor = c;
                rtx.AppendText(strText);
                if (isNewline)
                    rtx.AppendText("\n\r");
                rtx.ScrollToCaret();
            }
        }

        protected override void ResizeControlButton()
        {
            base.ResizeControlButton();
            try
            {
                btn_close.Location = new Point(this.Size.Width - this.btn_close.Size.Width - 16, GetTopControlCenter(this.btn_close));
                btn_normal.Location = new Point(this.btn_close.Location.X - this.btn_normal.Size.Width - 4, GetTopControlCenter(this.btn_normal));
                btn_minimize.Location = new Point(this.btn_normal.Location.X - this.btn_minimize.Size.Width - 4, GetTopControlCenter(this.btn_minimize));
                if (btnSetting.Visible)
                {
                    btnFeedback.Location = new Point(this.btn_minimize.Location.X - this.btnFeedback.Size.Width - 4, GetTopControlCenter(this.btnFeedback));
                    btnSetting.Location = new Point(this.btnFeedback.Location.X - this.btnSetting.Size.Width - 4, GetTopControlCenter(this.btnSetting));
                    btnContactEx.Location = new Point(this.btnSetting.Location.X - this.btnContactEx.Size.Width - 4, GetTopControlCenter(this.btnContactEx));
                    btnToVideo.Location = new Point(this.btnContactEx.Location.X - this.btnToVideo.Size.Width - 4, GetTopControlCenter(this.btnToVideo));
                }
                else
                {
                    btnFeedback.Location = new Point(this.btn_minimize.Location.X - this.btnSetting.Size.Width - 4, GetTopControlCenter(this.btnSetting));
                    btnContactEx.Location = new Point(this.btnFeedback.Location.X - this.btnContactEx.Size.Width - 4, GetTopControlCenter(this.btnContactEx));
                    btnToVideo.Location = new Point(this.btnContactEx.Location.X - this.btnToVideo.Size.Width - 4, GetTopControlCenter(this.btnToVideo));
                }
                if (this.wbsChat != null)
                {
                    this.wbsChat.Invalidate();
                    if (!wbsChat.IsResized)
                    {
                        wbsChat.IsResized = true;
                        wbsChat.Refresh();
                    }
                }
                if (this.wbsChatDetail != null)
                    this.wbsChatDetail.Invalidate();
            }
            catch { }
        }

        private int GetTopControlCenter(Control ctrl, int intHeight = 38)
        {
            int intResult = intHeight / 2;
            if (ctrl != null)
                intResult = intHeight / 2 - ctrl.Height / 2;

            //try
            //{
            //    intRelust = intHeight / 2 - con.Height / 2;
            //}
            //catch (Exception ex)
            //{
            //    Common.LogException(ex.ToString(), "GetTopControlCenter");
            //}
            return intResult;
        }

        private void GetPCWeChatBackupPath()
        {
            //获取微信PC 设置--通用设置--文件管理 的配置目录
            string strConfigFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), @"Tencent\WeChat\All Users\config\3ebffe94.ini");
            Common.Log(string.Format("同步PCWeChat备份路径=====>{0}", strConfigFilePath), m_bIsDebug);

            if (File.Exists(strConfigFilePath))
            {
                try
                {
                    using (StreamReader sr = new StreamReader(strConfigFilePath, Encoding.UTF8))
                    {
                        string strTemp = sr.ReadLine().Trim();
                        string strTempFolder = "";

                        if (!string.IsNullOrWhiteSpace(strTemp) ||
                            strTemp.Equals("MyDocument:", StringComparison.InvariantCultureIgnoreCase))
                        {
                            strTempFolder = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "WeChat Files");
                        }
                        else
                            strTempFolder = strTemp + @"\WeChat Files";

                        if (Directory.Exists(strTempFolder))
                        {
                            this.txtWechatPath.Text = strTempFolder;
                        }
                    }
                }
                catch (IOException ioe)
                {
                    Common.LogException(ioe.ToString(), "GetPCWeChatBackupPath");
                }
            }
        }

        private void InitDataGridViewAccount()
        {
            this.dgvAccount = new tbDataGridView();
            this.dgvAccount.SuspendLayout();
            this.dgvAccount.tbMouseHoverResponse = true;
            this.dgvAccount.RowTemplate.Height = 58;
            this.dgvAccount.ColumnHeadersVisible = true;
            this.dgvAccount.tbShowNoData = false;
            this.dgvAccount.MultiSelect = false;
            this.dgvAccount.ContextMenuStrip = this.munOperator;
            this.dgvAccount.Columns.Clear();
            this.dgvAccount.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, true, false,
                DataGridViewContentAlignment.MiddleCenter, true, DataGridViewTriState.False));

            //2、昵称（显示）
            tbDataGridViewTextBoxColumn colName = (tbDataGridViewTextBoxColumn)tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn),
                "colName", "昵称", 250, true, false, DataGridViewContentAlignment.MiddleLeft, true, DataGridViewTriState.False);
            //设置样式
            DataGridViewCellStyle cellStyle = new DataGridViewCellStyle();
            cellStyle.Font = Common.CreateFont(this.Font.Name, 9, FontStyle.Bold);
            cellStyle.ForeColor = Color.Black;
            colName.DefaultCellStyle = cellStyle;

            this.dgvAccount.Columns.Add(colName);
            //this.dgvAccount.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colType", "状态", 50, true, true, DataGridViewContentAlignment.MiddleLeft, true));
            this.dgvAccount.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewImageColumn), "colType", "授权", 50, true, false,
                DataGridViewContentAlignment.MiddleCenter, true, DataGridViewTriState.False));

            this.dgvAccount.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colFolderPath", "路径", 350, true, true,
                DataGridViewContentAlignment.MiddleLeft, true));

            this.dgvAccount.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colPCLimit", "套餐信息", 90, true, true,
                DataGridViewContentAlignment.MiddleLeft, true));

            this.dgvAccount.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewButtonColumn), "colButton", "", 90, true, true,
                DataGridViewContentAlignment.MiddleLeft, false));

            this.dgvAccount.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colOther", "", 5, false, true,
                DataGridViewContentAlignment.MiddleLeft, true));

            this.dgvAccount.ResumeLayout();

            dgvAccount.CellClick -= dgvAvvount_CellClick;
            dgvAccount.CellClick += dgvAvvount_CellClick;

            dgvAccount.RowPrePaint += dgvAccount_RowPrePaint;
            dgvAccount.CellMouseMove += dgvAccount_CellMouseMove;
            dgvAccount.MouseMove += dgvAccount_MouseMove;

            Utility.AddForm2Panel(this.dgvAccount, this.pnlWechatAccounts);
            this.dgvAccount.BringToFront();
        }

        private void InitDataGridViewFriend()
        {
            this.dgvFriend = new tbDataGridView();
            this.dgvFriend.SuspendLayout();

            this.dgvFriend.tbMouseHoverResponse = true;
            this.dgvFriend.RowTemplate.Height = 58;
            this.dgvFriend.ColumnHeadersVisible = false;
            this.dgvFriend.tbShowNoData = false;
            this.dgvFriend.MultiSelect = true;

            this.dgvFriend.Columns.Clear();
            this.dgvFriend.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, true, false,
                DataGridViewContentAlignment.MiddleCenter, true, DataGridViewTriState.False));

            //1、用户名（key）
            this.dgvFriend.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colBlank", "", 15, false, false,
                DataGridViewContentAlignment.MiddleLeft, true));

            //2、昵称（显示）
            tbDataGridViewTextBoxColumn colName = (tbDataGridViewTextBoxColumn)tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colName", "昵称", 155, true, false,
                DataGridViewContentAlignment.MiddleLeft, true, DataGridViewTriState.False);
            //设置样式
            DataGridViewCellStyle cellStyle = new DataGridViewCellStyle();
            cellStyle.Font = Common.CreateFont(this.Font.Name, 9, FontStyle.Bold);
            cellStyle.ForeColor = Color.Black;
            colName.DefaultCellStyle = cellStyle;

            this.dgvFriend.Columns.Add(colName);

            //3、时间
            this.dgvFriend.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colTime", "时间", 80, true, true,
                DataGridViewContentAlignment.MiddleRight, true, DataGridViewTriState.False));

            this.dgvFriend.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colOther", "", 5, false, true,
                DataGridViewContentAlignment.MiddleLeft, true));

            this.dgvFriend.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "colMD5", "", 5, false, true,
                DataGridViewContentAlignment.MiddleLeft, true));

            this.dgvFriend.ResumeLayout();

            this.dgvFriend.SelectionChanged -= dgvFriend_SelectionChanged;
            this.dgvFriend.SelectionChanged += dgvFriend_SelectionChanged;

            Utility.AddForm2Panel(this.dgvFriend, this.pnlFriend);
            this.dgvFriend.BringToFront();
        }

        private void InitMusicPlayer()
        {
            this.mPlayer = PluginPlayer.Instance();
            this.mPlayer.PlayMode = UserPlayMode.OrderPlay;

            if (this.mPlayer.GetUserPlaylist(this.Name) == null)
            {
                this.mPlayer.AddUserPlaylist(this.Name);
            }

            this.mPlayer.PlayStateChange += OnPlayStateChange;
        }

        private void InitExportHelper(string account)
        {
            if (this.mExportHelper != null)
            {
                this.mExportHelper.Dispose();
            }

            //WechatAccInfo waInfo = GetAcctInfo();
            //if (waInfo != null)
            this.mExportHelper = ExportHelper.GetInstance(account/*waInfo.Account*/, mWechatHeplerPC, mWeChatMsgWebPage);

            this.mExportHelper.ExportProgressEventHandler -= mExportHelper_ExportProgressEventHandler;
            this.mExportHelper.ExportProgressEventHandler += mExportHelper_ExportProgressEventHandler;
        }


        private void mExportHelper_ExportProgressEventHandler(object sender, ExportProgressEventArgs args)
        {
            ChangeStateText(args.Msg, args.PExportStatus);
        }

        private delegate void ChangeStateHandler(string Text, ExportStatus eStatus);
        private void ChangeStateText(string Text, ExportStatus eStatus)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new ChangeStateHandler(ChangeStateText), Text, eStatus);
                }
                else
                {
                    this.lblState.Text = Text;
                    if (eStatus == ExportStatus.Exporting)
                    {
                        this.btnNewExport.Enabled = false;
                        this.btnExport.Enabled = false;
                        this.btnWeCahtUsers.Enabled = false;
                        this.btnSuperior.Enabled = false;
                        //this.btnNewExport.Text = "导出中...";
                        this.btnSuperior.Enabled = false;
                    }
                    else
                    {
                        this.btnNewExport.Enabled = true;
                        this.btnExport.Enabled = true;
                        this.btnWeCahtUsers.Enabled = true;
                        this.btnSuperior.Enabled = true;
                        //this.btnNewExport.Text = "导出信息";
                        this.btnSuperior.Enabled = true;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ChangeStateText");
            }
        }

        private void OnPlayStateChange(object sender, MediaPlayStateChangedEventArgs e)
        {
            if (e.NewPlayState == WMPPlayState.wmppsStopped)
            {
                if (this.mPlayer.CurrentMedia != null)
                {
                    string strFilePlaying = this.mPlayer.CurrentMedia.sourceURL;
                    this.mPlayer.GetUserPlaylist(this.Name).Remove(strFilePlaying);
                    this.ShowAudioWebView(strFilePlaying, false);
                }
            }
        }

        private void ShowAudioWebView(string strWavFile, bool blnPlay)
        {
            if (string.IsNullOrEmpty(strWavFile))
            {
                return;
            }
            string strID = Path.GetFileNameWithoutExtension(strWavFile);

            ChatWebPage.ControlVoiceView(this.wbsChat, strID, blnPlay);
        }

        private void DownloadServerSetting()
        {
            Thread thd = new Thread(DoDownloadServerSetting);
            thd.IsBackground = true;
            thd.Start();
        }

        private void DoDownloadServerSetting()
        {
            try
            {
                string strTempPath = Folder.ConfigServerIniFile + "temp";
                string strContent = Utility.GetContentStringFromUrl("http://t.tongbu.com/iWechatAssistant/ServerSettings.aspx", Encoding.UTF8, 20000);

                using (StreamWriter sWriter = new StreamWriter(strTempPath, false, Encoding.Default))
                {
                    sWriter.Write(strContent);
                }
                if (File.Exists(strTempPath))
                {
                    try
                    {
                        File.Delete(Folder.ConfigServerIniFile);
                    }
                    catch { }
                    try
                    {
                        File.Move(strTempPath, Folder.ConfigServerIniFile);
                    }
                    catch { }
                }
            }
            catch (ThreadAbortException e)
            {
                System.Diagnostics.Debug.Write(e);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoDownloadServerSetting");
            }
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            try
            {
                string strVersion = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();
                if (!ServerIniSetting.IsWechatAssistantSupportVersion(strVersion))
                {
                    tbMessageBox.Show(this, string.Format(ServerIniSetting.IsWechatAssistantSupportHint(), strVersion), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.StartToCheckUpdate();
                    this.Close();
                }
            }
            catch { }
        }

        #endregion

        #region "---  窗体事件  ---"

        void dgvAccount_MouseMove(object sender, MouseEventArgs e)
        {
            this.Cursor = Cursors.Default;
        }

        private void dgvAccount_CellMouseMove(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.ColumnIndex == -1)
                return;

            if (e.ColumnIndex >= 0 && e.RowIndex == -1)
            {
                this.Cursor = Cursors.Hand;
                return;
            }

            try
            {
                DataGridViewCell cell = this.dgvAccount.Rows[e.RowIndex].Cells[e.ColumnIndex];
                //if (cell == null || cell.Value.ToString() == "" || cell.Value.ToString() == "0")
                //    return;
                if (cell == null || cell.Value == null || cell.Value.ToString().Equals("0"))
                    return;

                string headText = this.dgvAccount.Columns[e.ColumnIndex].HeaderText;
                //if (headText == "套餐信息" || headText == "")
                if (string.IsNullOrWhiteSpace(headText) ||
                    headText.Equals("套餐信息", StringComparison.InvariantCultureIgnoreCase))
                    this.Cursor = Cursors.Hand;
                else
                    this.Cursor = Cursors.Default;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dgvAccount_CellMouseMove");
            }
        }

        private void dgvAccount_RowPrePaint(object sender, DataGridViewRowPrePaintEventArgs e)
        {
            try
            {
                FontStyle newStyle = this.dgvAccount.DefaultCellStyle.Font.Style;
                newStyle |= FontStyle.Underline;
                Font font = new Font(this.dgvAccount.DefaultCellStyle.Font, newStyle);

                foreach (DataGridViewRow item in this.dgvAccount.Rows)
                {
                    try
                    {
                        //if (item.Cells["colPCLimit"].Value != null && item.Cells["colPCLimit"].Value.ToString() == this.mLoading)
                        //continue;
                        item.Cells["colPCLimit"].Style.Font = font;
                        item.Cells["colPCLimit"].Style.ForeColor = Color.FromArgb(54, 134, 246);
                    }
                    catch { }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dgvAccount_RowPrePaint");
            }
        }

        private void dgvFriend_SelectionChanged(object sender, EventArgs e)
        {
            if (this.mIsSelectAllClick)
                return;

            this.SetLinkmanMsg();

            if (!dgvFriend.tbCanResponse || dgvFriend.CurrentCell.ColumnIndex == 0 ||
                dgvFriend.SelectedRows == null || this.dgvFriend.SelectedRows.Count == 0)
            {
                return;
            }

            try
            {
                if (this.pnlTitle.Visible)
                    btnTimeScreen_Click(null, null);

                InvokeOnUiThreadIfRequired(this, () =>
                {
                    if (!m_IsLockDate)
                    {
                        this.btnTimeSelect.Text = "全部";
                        this.dtpStart.Value = Convert.ToDateTime("1970-01-02 00:00:01");
                        this.dtpEnd.Value = Convert.ToDateTime("2999-01-01 00:00:01");

                        this.dtpStart.MaxDate = dtpEnd.Value;
                        this.dtpEnd.MinDate = dtpStart.Value;
                    }

                    if (IniSetting.GetIsTalkSaveDB())
                    {
                        dgvFriend.Enabled = false;
                    }
                });

                this.mIsScreenByTime = false;

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dgvFriend_SelectionChanged");
            }

            try
            {
                try
                {
                    this.mWeChatMsgWebPage.lstMergerForwardInfo.Clear();
                }
                catch { }

                ChatWebPage.ClearChatOnWebPage(this.wbsChat);
                ChatWebPage.ClearChatOnWebPage(this.wbsChatDetail);

                DataGridViewRow row = this.dgvFriend.SelectedRows[0];
                if (row == null) return;

                this.mCurrentDataGridViewRow = row;

                this.SetLoadChat(true);

                WechatTalk wTalk = (WechatTalk)mCurrentDataGridViewRow.Tag;
                wTalk.PageIndex = 0;

                this.ShowSystemInfoOnPC(false, wTalk.NickName);
                this.tbpPage.Clear();

                if (m_IsLockDate)
                {
                    ScreenByTime(wTalk);
                    dgvFriend.Enabled = true;
                    return;
                }

                if (IniSetting.GetIsTalkSaveDB())
                {
                    //if (this.mCurrentWechatTalk != null)
                    //{
                    //    this.mCurrentWechatTalk.MessageItems.Clear();
                    //}

                    wTalk.MessageItems.Clear();
                }

                this.LoadChat(row);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dgvFriend_SelectionChanged_1");
            }
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            this.SetViewStyle(MViewType.WechatList);
            this.LoadWechatAccountFromPCThread();
        }

        private void btnRefreshAccount_Click(object sender, EventArgs e)
        {
            this.LoadWechatAccountFromPCThread();
        }

        private void btnSuperior_Click(object sender, EventArgs e)
        {
            frmSetting.WeChatDataLoadDBHandler -= frmSetting_WeChatDataLoadDBHandler;
            frmSetting.WeChatArtificialAccreditHandler -= frmSetting_WeChatArtificialAccreditHandler;

            m_IsLockDate = false;
            chkLock.Checked = false;

            this.mIsScreenByTime = false;
            this.mCurrentDataGridViewRow = null;

            this.txtSearchFriend.Text = string.Empty;
            this.SetViewStyle(MViewType.WechatList);
            this.txtSearchChat.Text = "";
        }

        private void btnGetBack_Click(object sender, EventArgs e)
        {
            this.SetViewStyle(MViewType.Welcome);
        }

        private WechatAccInfo GetAcctInfo()
        {
            if (dgvAccount != null &&
                dgvAccount.SelectedRows != null &&
                dgvAccount.SelectedRows.Count > 0)
            {
                return dgvAccount.SelectedRows[0].Tag as WechatAccInfo;
            }

            return null;
        }

        private void dgvAvvount_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || (e.ColumnIndex != 5 & e.ColumnIndex != 4))
                    return;

                this.SetLoadingMsg("解析备份数据，请耐心等候...");

                if (!this.mIsLoadBackupInfoByComputer)
                {
                    tbMessageBox.Show(this, "对不起，授权、套餐信息查询中，请稍候再试！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                if (e.ColumnIndex == 4)
                {
                    //跳转购买页
                    this.OpenURL(ServerIniSetting.GetiWechatAssistantPurchase());
                    return;
                }

                //this.txtSearchChat.SearchTipText = "按回车搜索记录";

                this.pnlzzsq.Visible = true;
                this.pnlrgsq.Visible = false;

                tbpPage.Clear();

                WechatAccInfo waInfo = dgvAccount.Rows[e.RowIndex].Tag as WechatAccInfo;
                if (waInfo == null) return;

                Common.Log(string.Format("当前查看====>>> Acount:{0}>>>Name:{1}>>>InnerAcount:{2}",
                            waInfo.Account, waInfo.Name, waInfo.InnerAccount), m_bIsDebug);


                InvokeOnUiThreadIfRequired(this, () =>
                {
                    txtSearchChat.Text = "";

                    pnlTitle.Visible = false;
                    picTimeScreen.Image = global::iWechatAssistant.Properties.Resources.TimeScreenUpDowm;

                    bool flag = (ServerIniSetting.IsMsgHide() && WechatHeplerPC.mAssistInfo.IsPay && waInfo.AState != ActivateState.Normal);
                    lblRegister.Visible = flag;
                    llblExportPreview.Visible = flag;
                });


                RunLoadFriendWorker(waInfo);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dgvAvvount_CellClick");
            }

            frmSetting.WeChatDataLoadDBHandler -= frmSetting_WeChatDataLoadDBHandler;
            frmSetting.WeChatDataLoadDBHandler += frmSetting_WeChatDataLoadDBHandler;

            frmSetting.WeChatArtificialAccreditHandler -= frmSetting_WeChatArtificialAccreditHandler;
            frmSetting.WeChatArtificialAccreditHandler += frmSetting_WeChatArtificialAccreditHandler;
        }


        private void btnWeCahtUsers_Click(object sender, EventArgs e)
        {
            try
            {
                mMenuDevice.Show(btnWeCahtUsers, new Point(0, btnWeCahtUsers.Height));
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "btnWeCahtUsers_Click");
            }
        }

        private void btnWechatPath_Click(object sender, EventArgs e)
        {
            this.DoGetWechatPath();
        }

        private void DoGetWechatPath()
        {

            Do_TryAgain:
            FolderBrowserDialog dialog = new FolderBrowserDialog();
            dialog.Description = "请选择微信备份文件默认保存位置";
            if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                if (string.IsNullOrEmpty(dialog.SelectedPath))
                {
                    tbMessageBox.Show(this, "请选择备份文件默认保存位置！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    goto Do_TryAgain;
                }

                this.txtWechatPath.Text = dialog.SelectedPath;
                this.LoadWechatAccountFromPCThread();
            }
        }

        private void btnNewExport_Click(object sender, EventArgs e)
        {
            if (this.dgvFriend.Rows.Count == 0)
            {
                //联系人为空。
                tbMessageBox.Show(this, "联系人为空。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            this.cmsExportNews.Show(this.btnNewExport, this.btnNewExport.Width - this.cmsExportNews.Width, this.btnNewExport.Height + 3);

        }

        private void tsmiExcelEx_Click(object sender, EventArgs e)
        {
            this.NewExport(ExportType.ExportToExcel);
        }

        private void tsmiTxtEx_Click(object sender, EventArgs e)
        {
            this.NewExport(ExportType.ExportToTxt);
        }

        private void tsmiHtml_Click(object sender, EventArgs e)
        {
            this.NewExport(ExportType.ExportToHtml);
        }

        private void tsmiPicture_Click(object sender, EventArgs e)
        {
            this.NewExport(ExportType.ExportToPicture);
        }

        private void tsmiVoice_Click(object sender, EventArgs e)
        {
            this.NewExport(ExportType.ExportToVoice);
        }

        private void tsmiVideo_Click(object sender, EventArgs e)
        {
            this.NewExport(ExportType.ExportToVideo);
        }

        private void tsmiAll_Click(object sender, EventArgs e)
        {
            this.NewExport(ExportType.ExportToAll);
        }

        private void btnExport_Click(object sender, EventArgs e)
        {
            //this.NewExport(ExportType.ExportToAll);
            if (this.dgvFriend.Rows.Count == 0)
            {
                //联系人为空。
                tbMessageBox.Show(this, "联系人为空。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            this.cmsExportNews.Show(this.btnExport, this.btnExport.Width - this.cmsExportNews.Width, this.btnExport.Height + 3);
        }

        private void btnAccredit_Click(object sender, EventArgs e)
        {
            LoadWechatAccountFromPCThread(true);
        }

        private void btnReturnAccredit_Click(object sender, EventArgs e)
        {
            if (!this.pnlrgsq.Visible)
                SetViewStyle(MViewType.WechatList);

            this.pnlrgsq.Visible = false;
            this.pnlzzsq.Visible = true;
        }

        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                InvokeOnUiThreadIfRequired(wbsChat, () => wbsChat.Dispose());
                InvokeOnUiThreadIfRequired(wbsChatDetail, () => wbsChatDetail.Dispose());

                if (m_threadDown != null && (m_threadDown.IsAlive || m_threadDown.ThreadState != ThreadState.Stopped))
                    m_threadDown.Abort();
            }
            catch (Exception ex)
            {
                Common.LogException(ex, "MainFrom_FromClosing");
            }

            try
            {
                if (this.mTdSearchFriend != null && this.mTdSearchFriend.ThreadState != ThreadState.Stopped)
                    this.mTdSearchFriend.Abort();

            }
            catch { }


            try
            {
                this.bgwShowAccount.CancelAsync();
            }
            catch { }

            // 清理缓存
            try
            {
                this.Visible = false;
                string strTmpForder = Folder.TempFolder;
                if (Directory.Exists(strTmpForder))
                {
                    Folder.ClearFolder(strTmpForder);
                }
            }
            catch { }
        }

        private void tsmiOpenFolder_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.dgvAccount.Rows.Count > 0 && this.dgvAccount.SelectedRows.Count > 0 && this.dgvAccount.SelectedRows[0].Tag != null)
                {
                    string path = ((WechatAccInfo)this.dgvAccount.SelectedRows[0].Tag).PathOnPC;
                    if (Directory.Exists(path))
                    {
                        try
                        {
                            Common.Log(string.Format("MainForm's tsmiOpenFolder>>>>>>>{0}", path), m_bIsDebug);
                            Common.OpenExplorer(path);
                        }
                        catch { }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tsmiOpenFolder_Click");
            }
        }

        private void tsmiOpen_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.dgvAccount.SelectedRows.Count > 0 && this.dgvAccount.SelectedRows[0].Tag != null)
                {
                    WechatAccInfo waInfo = (WechatAccInfo)this.dgvAccount.SelectedRows[0].Tag;
                    RunLoadFriendWorker(waInfo);
                    //this.ShowAccountThread(waInfo);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tsmiOpen_Click");
            }
        }

        private void tsmiExist_Click(object sender, EventArgs e)
        {
            try
            {
                Application.ThreadException -= OnThreadException;
                this.Close();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tsmiExist_Click");
            }
        }

        private void tsmiCreateError_Click(object sender, EventArgs e)
        {
            if (tbMessageBox.Show(this, "您确定要生成错误日志吗？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
                CreateErrorZip();
        }

        private void btnPCPath_Click(object sender, EventArgs e)
        {
            this.GetPCWeChatBackupPath();
            this.LoadWechatAccountFromPCThread();
        }

        private void btnContactEx_Click(object sender, EventArgs e)
        {
            frmService frm = new frmService();
            frm.ShowDialog();

            //Common.OpenExplorer(WechatHeplerPC.mAssistInfo.StrLinkQQ);
        }

        private void btnContact_Click(object sender, EventArgs e)
        {
            Common.OpenExplorer(WechatHeplerPC.mAssistInfo.StrLinkQQ);
        }

        private void btnToVideo_Click(object sender, EventArgs e)
        {
            //if (string.IsNullOrEmpty(WechatHeplerPC.mAssistInfo.StrVideoCourse))
            //    tbMessageBox.Show(this, "小编们正在努力制作中...", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //else
            //OpenURL(WechatHeplerPC.mAssistInfo.StrVideoCourse);

            Common.OpenExplorer(ServerIniSetting.GetiWechatAssistantTutorial());
        }

        private void lblRecommend_TextClick(object sender, EventArgs e)
        {
            ColorText ct = (ColorText)sender;
            if (ct.mText == this.mStrWechatRecovery)//恢复大师
                Common.OpenExplorer("http://rollback.wesafesoft.com/?c_s=Kpo%2bRnYW%2b8Q%3d&s=bfzstj");
            else if (ct.mText == this.mStrWechatManager)//管理大师
                Common.OpenExplorer("http://wechat.tongbu.com/?c_s=0r%2fhQCiqpUo%3d&s=bfzstj");
            else if (ct.mText == this.mStrVoiceMerge)//管理大师//合并助手
                Common.OpenExplorer(ServerIniSetting.RecommendVoiceMergerUrl());
        }

        private void txtSearchFriend_TextChangedByTimer(object sender, EventArgs e)
        {
            try
            {
                if (this.mTdSearchFriend != null && this.mTdSearchFriend.ThreadState != ThreadState.Stopped)
                    this.mTdSearchFriend.Abort();

            }
            catch { }
            this.ShowSystemInfoOnPC(true, "查看消息，请选中左侧联系人");
            ChatWebPage.ClearChatOnWebPage(this.wbsChat);
            this.mTdSearchFriend = new Thread(new ParameterizedThreadStart(DoSearchFriend));
            this.mTdSearchFriend.IsBackground = true;
            this.mTdSearchFriend.Start(this.txtSearchFriend.Text.Trim());
        }

        private void llblBackupCourse_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Common.OpenExplorer("http://news.tongbu.com/96019.html");
        }

        private void mNotifyIcon_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            foreach (Form frm in Application.OpenForms)
            {
                if (frm is MainForm)
                {
                    //当窗体最小化时，先SHOW再设置WindowState，这样会窗体显示出来
                    frm.Show();
                    if (frm.WindowState == FormWindowState.Minimized)
                        frm.WindowState = FormWindowState.Normal;
                    frm.Activate();
                    break;
                }
            }
        }

        private void dtpStart_CloseUp(object sender, EventArgs e)
        {
            this.dtpEnd.MinDate = this.dtpStart.Value;
        }

        private void dtpEnd_CloseUp(object sender, EventArgs e)
        {
            this.dtpStart.MaxDate = this.dtpEnd.Value;
        }

        void dtpStartTime_TextChanged(object sender, System.EventArgs e)
        {
            if (m_bIsInitialTime)
                this.dtpStartTime.Text = "00:00";
        }

        void dtpEndTime_TextChanged(object sender, System.EventArgs e)
        {
            if (m_bIsInitialTime)
                this.dtpEndTime.Text = "23:59";
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            if (mCurrentDataGridViewRow == null &&
                dgvFriend.SelectedRows != null &&
                dgvFriend.SelectedRows.Count > 0)
            {
                mCurrentDataGridViewRow = dgvFriend.SelectedRows[0];
            }

            if (this.mCurrentDataGridViewRow == null)
            {
                tbMessageBox.Show(this, "请选择要查看的联系人", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            try
            {
                this.mIsScreenByTime = true;
                this.SetLoadChat(true);

                ChatWebPage.ClearChatOnWebPage(this.wbsChat);
                ChatWebPage.ClearChatOnWebPage(this.wbsChatDetail);
                this.ShowWebDetail(false);

                WechatTalk wTalk = (WechatTalk)mCurrentDataGridViewRow.Tag;
                wTalk.PageIndex = 0;
                this.ScreenByTime(wTalk);

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "btnOK_Click");
            }

        }

        private bool m_IsLockDate = false;
        private void chkLock_CheckedChanged(object sender, EventArgs e)
        {
            m_IsLockDate = chkLock.Checked;
        }

        private void btnTimeSelect_Click(object sender, EventArgs e)
        {
            tbContextMenuStrip menu = new tbContextMenuStrip();
            ToolStripMenuItem menuInfoItem = null;

            List<string> lstTimes = new List<string>();
            lstTimes.Add("今天");
            lstTimes.Add("最近一周");
            lstTimes.Add("最近一个月");
            lstTimes.Add("最近三个月");
            lstTimes.Add("最近一年");
            lstTimes.Add("全部");
            foreach (string item in lstTimes)
            {
                menuInfoItem = (ToolStripMenuItem)menu.Items.Add(item, null);
                if (btnTimeSelect.Text == item)
                    menuInfoItem.Checked = true;
                else
                    menuInfoItem.Checked = false;
                menuInfoItem.Click += OnTimeMenuItemClick;
            }
            menu.Closed += OnTimeMenuClosed;
            menu.Show(btnTimeSelect, new Point(0, btnTimeSelect.Height));
        }

        private void OnTimeMenuItemClick(object sender, EventArgs e)
        {
            try
            {
                DateTime StartTime = DateTime.MinValue;
                DateTime nowTime = DateTime.Now;
                this.dtpEnd.Value = nowTime;
                this.dtpStart.MaxDate = this.dtpEnd.Value;
                ToolStripMenuItem item = (ToolStripMenuItem)sender;
                switch (item.Text)
                {
                    case "今天":
                        {
                            this.dtpStart.Value = DateTime.Now.Date;
                            break;
                        }

                    case "最近一周":
                        {
                            this.dtpStart.Value = nowTime.AddDays(-7);
                            break;
                        }

                    case "最近一个月":
                        {
                            this.dtpStart.Value = nowTime.AddMonths(-1);
                            break;
                        }

                    case "最近三个月":
                        {
                            this.dtpStart.Value = nowTime.AddMonths(-3);
                            break;
                        }

                    case "最近一年":
                        {
                            this.dtpStart.Value = nowTime.AddYears(-1);
                            break;
                        }

                    case "全部":
                        {
                            this.dtpStart.Value = System.Convert.ToDateTime("1970-01-01 00:00:01");
                            break;
                        }
                }
                this.btnTimeSelect.Text = item.Text;
                this.dtpEnd.MinDate = this.dtpStart.Value;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "OnTimeMenuItemClick");
            }
        }

        private void OnTimeMenuClosed(object sender, ToolStripDropDownClosedEventArgs e)
        {
            try
            {
                tbContextMenuStrip menu = (tbContextMenuStrip)sender;
                menu.Closed -= OnTimeMenuClosed;
            }
            catch { }
        }

        private void tmrTimer_Tick(object sender, EventArgs e)
        {
            _lngPassTime += 1;
            if (_lngPassTime == 1)
            {//检查是否有上次没有升级的升级包(_FirstRunFromUpgrade在验证升级成功会进行操作一定在放在一秒之前)
                this.CheckUpgradeFileExist();
                if (!this.mIsFirstRunAfterUpgrade)
                {
                    return;
                }

                try
                {
                    string strFileTag = Path.Combine(Application.StartupPath.TrimEnd('\\'), "OpenOK.dll");

                    if (!File.Exists(strFileTag))
                    {
                        FileStream fileStream = File.Create(strFileTag);
                        fileStream.Close();
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "OpenOKException");
                }

            }
            else if (_lngPassTime == 3)
            {
                //下载ServerSettings.ini
                this.DownloadServerSetting();
            }
            else if (_lngPassTime == 20)
            {
                StartToSendData();
            }
            else if (_lngPassTime == 2 || _lngPassTime % 3600 == 0)
            {
                //程序开启时已启动检测升级，然后每隔一小时进行检查软件升级
                StartToCheckUpdate();
            }


            if (_lngPassTime % 10 == 0 && this.mIsShowSetRecommend)
            {
                //如果开启了推荐合并语音 则10秒切换一次推荐
                if (ServerIniSetting.RecommendVoiceMerger())
                {
                    if (mIsFirstRecommend)
                    {
                        mIsFirstRecommend = false;
                        this.lblRecommend.ColorTextList.Clear();
                        this.lblRecommend.ColorTextList.AddItem("                                          微信导出语音合并，使用", 0, Color.FromArgb(128, 0, 0), mFontF, false);
                        this.lblRecommend.ColorTextList.AddItem(this.mStrVoiceMerge, 1, Color.FromArgb(52, 115, 255), mFontF, false, true);
                        this.lblRecommend.ColorTextList.AddItem("轻松合并，免费试用中...", 2, Color.FromArgb(128, 0, 0), mFontF, false);
                    }
                    else
                    {
                        mIsFirstRecommend = true;
                        //this.lblRecommend.ColorTextList.Clear();
                        //this.lblRecommend.ColorTextList.AddItem("                                        没找到聊天记录？可能被删除了！试试下载", 0, System.Drawing.Color.FromArgb(128, 0, 0), mFontF, false);
                        //this.lblRecommend.ColorTextList.AddItem(this.mCurrentRecommend, 1, System.Drawing.Color.FromArgb(52, 115, 255), mFontF, false, true);
                        //this.lblRecommend.ColorTextList.AddItem("进行恢复", 2, System.Drawing.Color.FromArgb(128, 0, 0), mFontF, false);
                    }
                }
            }

        }

        private void llblPCPath_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            this.GetPCWeChatBackupPath();
            this.LoadWechatAccountFromPCThread();
        }

        private void btnSetting_Click(object sender, EventArgs e)
        {
            frmSetting frm = new frmSetting();
            frm.ShowDialog(this);
        }

        private void btnSetting_VisibleChanged(object sender, EventArgs e)
        {
            if (this.btnSetting.Visible)
            {
                btnSetting.Location = new Point(this.btn_minimize.Location.X - this.btnSetting.Size.Width - 4, GetTopControlCenter(this.btnSetting));
                btnContactEx.Location = new Point(this.btnSetting.Location.X - this.btnContactEx.Size.Width - 4, GetTopControlCenter(this.btnContactEx));
                btnToVideo.Location = new Point(this.btnContactEx.Location.X - this.btnToVideo.Size.Width - 4, GetTopControlCenter(this.btnToVideo));
            }
            else
            {
                btnContactEx.Location = new Point(this.btn_minimize.Location.X - this.btnContactEx.Size.Width - 4, GetTopControlCenter(this.btnContactEx));
                btnToVideo.Location = new Point(this.btnContactEx.Location.X - this.btnToVideo.Size.Width - 4, GetTopControlCenter(this.btnToVideo));
            }
        }

        private void txtSearchChat_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode != Keys.Enter)
                return;
            //if (string.IsNullOrEmpty(txtSearchChat.Text)) return;

            //this.mDictWechatTalkEx.Clear();

            ChatWebPage.ClearChatOnWebPage(this.wbsChat);
            ChatWebPage.ClearChatOnWebPage(this.wbsChatDetail);
            //this.ShowWebDetail(false);

            this.ShowSystemInfoOnPC(false, "txtSearchChat_KeyDown to Log.");
            this.SetLoadChat(true);
            Utility.WaitSeconds(0.3);
            List<DateTime> lstDT = new List<DateTime>();
            if (pnlTitle.Visible)
            {
                //string dtmStart = dtpStart.Value.ToString("yyyy-MM-dd") + " " + dtpStartTime.Text;
                //DateTime dtStart = Convert.ToDateTime(dtmStart);

                //string dtmEnd = dtpEnd.Value.ToString("yyyy-MM-dd") + " " + dtpEndTime.Text;
                //DateTime dtEnd = Convert.ToDateTime(dtmEnd);

                //lstDT.Add(dtStart);
                //lstDT.Add(dtEnd);
                lstDT = FilterDateTime();
            }

            WechatAccInfo waInfo = GetAcctInfo();

            Dictionary<string, WechatTalk> dictWechats = null;
            if (waInfo != null)
            {
                dictWechats = WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).SearchTalk(lstDT,
                                                                                txtSearchChat.Text.ToLowerInvariant(),
                                                                                (Dictionary<string, WechatTalk>)dgvFriend.Tag);
            }
            if (dictWechats == null || dictWechats.Count == 0)
            {
                mCurrentDataGridViewRow = null;
                this.ShowSystemInfoOnPC(true, "搜索不到结果");
            }
            else
                this.ShowSystemInfoOnPC(true, "查看消息，请选中左侧联系人");

            this.AddDictToDataGridView(dictWechats, false);
            this.SetLoadChat(false);
            this.LoadHeadIcon();
        }

        private void txtSearchChat_ClearClick(object sender, EventArgs e)
        {
            //foreach (WechatTalk item in mDictWechatTalkEx.Values)
            //{
            //    item.SearchText = String.Empty;
            //}

            Dictionary<string, WechatTalk> dictFriends = (Dictionary<string, WechatTalk>)dgvFriend.Tag;
            if (dictFriends != null && dictFriends.Count > 0)
            {
                foreach (WechatTalk talk in dictFriends.Values)
                {
                    talk.SearchText = "";
                }
            }

            WechatTalk wTalk = (WechatTalk)wbsChat.Tag;
            if (wTalk != null)
                wTalk.SearchText = "";

            wbsChat.Tag = wTalk;

            ChatWebPage.ClearChatOnWebPage(wbsChat);
            ChatWebPage.ClearChatOnWebPage(wbsChatDetail);

            ShowSystemInfoOnPC(true, "查看消息，请选中左侧联系人");
            this.AddDictToDataGridView(/*mDictWechatTalk*/dictFriends, false);
            LoadHeadIcon();
        }

        private void btnTimeScreen_Click(object sender, EventArgs e)
        {
            try
            {
                if (!m_IsLockDate)
                    pnlTitle.Visible = !pnlTitle.Visible;// ? false : true;

                if (pnlTitle.Visible)
                    picTimeScreen.Image = global::iWechatAssistant.Properties.Resources.TimeScreenUp;
                else
                {
                    picTimeScreen.Image = global::iWechatAssistant.Properties.Resources.TimeScreenUpDowm;
                    if (mCurrentDataGridViewRow != null)
                        ShowWebDetail(false);
                }

                if (!m_IsLockDate)
                {
                    //dtpStart.Value = Convert.ToDateTime("1970-01-02 00:00:10");
                    dtpStart.Value = Convert.ToDateTime("1970-01-01 00:00:01");

                    m_bIsInitialTime = true;

                    dtpStartTime.Value = Convert.ToDateTime("00:00");
                    dtpStartTime.ResetText();

                    dtpEndTime.Value = Convert.ToDateTime("23:59");
                    dtpEndTime.ResetText();

                    m_bIsInitialTime = false;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "btnTimeScreen_Click");
            }
        }

        private void tsmiFriend_Click(object sender, EventArgs e)
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return;
            //1.付费 提示
            if (!this.PaidRemind(waInfo, false))
                return;

            InitExportHelper(waInfo.Account);

            //if (m_threadDown != null && (m_threadDown.IsAlive || m_threadDown.ThreadState != ThreadState.Stopped))
            //{
            //    tbMessageBox.Show(this, "正在加载好友头像等基本信息，请稍后继续......", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    return;
            //}

            try
            {
                this.mExportHelper.ExportFriendInfo(waInfo, this.btnWeCahtUsers.Text);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tsmiFriend_Click");
            }
        }

        private void btnzzsq_Click(object sender, EventArgs e)
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return;

            frmAccredit frm = new frmAccredit(waInfo, this.mCurrentSelectPath, this.mWechatPath);
            if (frm.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
            {
                waInfo.Password = frm.mStrBackupkey;
                //this.ShowAccountThread(this.mCurrentWAInfo);
                RunLoadFriendWorker(waInfo);

                //自助授权没有激活 将备份密钥 加密保存在本地 激活成功后 删除 超过7天数据删除
                if (WechatHeplerPC.mAssistInfo.IsPay && waInfo.AState != ActivateState.Normal)
                {
                    AccountIniSetting.SetBackupkey(waInfo.Account, waInfo.Password);

                    //没有激活 授权后 拷贝好友列表
                    if (File.Exists(frm.mStrContactPath))
                    {
                        string strAssistantPath = Path.Combine(waInfo.PathOnPC, "Assistant");
                        Folder.CheckFolder(strAssistantPath);
                        string strFriendPath = Path.Combine(strAssistantPath, "Friend.txt");
                        string strTempPath = strFriendPath + "Temp";
                        string strContent = File.ReadAllText(frm.mStrContactPath, Encoding.UTF8);
                        SaveFriendFile(strFriendPath, strTempPath, strContent);
                    }
                }
            }

        }

        private void btnrgsq_Click(object sender, EventArgs e)
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return;

            if (waInfo.AState != ActivateState.Normal && WechatHeplerPC.mAssistInfo.IsPay)
            {
                if (tbMessageBox.Show(this, "自助授权可先查看消息，在导出时付费激活。建议使用自助授权\n\r人工授权因为人力有限，需付费激活后联系客服授权。确定人工授权？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == System.Windows.Forms.DialogResult.Cancel)
                    return;

                frmCharge frm = new frmCharge(waInfo, this.mWechatHeplerPC);
                frm.BringToFront();
                frm.StartPosition = FormStartPosition.Manual;
                frm.Location = new Point(this.Left + (this.Width - frm.Width) / 2, this.Top + (this.Height - frm.Height) / 2);
                if (frm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                    return;
                this.ChangeRowType(waInfo);
            }
            this.pnlzzsq.Visible = false;
            this.pnlrgsq.Visible = true;
        }

        private void cbxSelectAll_CheckedChanged(object sender, EventArgs e)
        {
            if (mIsSelectAllClick)
                return;

            mIsSelectAllClick = true;
            foreach (DataGridViewRow item in dgvFriend.Rows)
            {
                if (item.Selected != cbxSelectAll.Checked)
                {
                    item.Selected = cbxSelectAll.Checked;
                    item.Selected = cbxSelectAll.Checked;
                }
            }
            mIsSelectAllClick = false;
            SetLinkmanMsg();
        }

        private void llblExportPreview_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            ExportHelper.ExportPreview();
        }

        private void tbbRecommend_TextClick(object sender, EventArgs e)
        {
            string strUrl = ServerIniSetting.RecommendVoiceMergerUrl();
            if (string.IsNullOrEmpty(strUrl))
                tbMessageBox.Show(this, "获取软件信息失败，请联系客服！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
            else
                Common.OpenExplorer(strUrl);
        }

        private void btnUpdateFriend_Click(object sender, EventArgs e)
        {


            if (IniSetting.GetUpdateFriends())
            {
                bool ss = false;
                tbMessageBox.Show(this, "如果出现好友头像或者昵称等其他信息与手机不一致请先在电脑微信上找到要查看的联系人查看他的个人信息，如果是群联系人就到对应群展开联系人去查看后，再执行该功能。", "提示",
                                                          MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1,
                                                          "不再提醒", ref ss, new string[] { "继续" });
                IniSetting.SetUpdateFriends(!ss);
            }
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return;

            frmAccredit frm = new frmAccredit(waInfo, this.mCurrentSelectPath, this.mWechatPath, 1);
            if (frm.ShowDialog(this) == System.Windows.Forms.DialogResult.OK)
            {
                if (IniSetting.GetUpdateFriendsed())
                {
                    bool ss = false;
                    tbMessageBox.Show(this, "更新好友成功后如果还是发现部分好友头像或者其他信息跟手机上不一致，请重启后再查看。", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1,
                                                          "不再提醒", ref ss, new string[] { "继续" });
                    IniSetting.SetUpdateFriendsed(!ss);
                }

                if (frm.mIsGetFriend)
                {
                    //try
                    //{
                    //    File.Delete(Path.Combine(waInfo.PathOnPC, @"Assistant\Friend.txt"));
                    //}
                    //catch { }

                    try
                    {
                        Directory.Delete(Path.Combine(Folder.CacheFolder, "HeadIcon"), true);
                    }
                    catch { }

                    //this.ShowAccountThread(this.mCurrentWAInfo);
                    RunLoadFriendWorker(waInfo);
                    try
                    {
                        if (WechatHeplerPC.mAssistInfo.IsPay && waInfo.AState != ActivateState.Normal && File.Exists(frm.mStrContactPath))
                        {
                            string strAssistantPath = Path.Combine(waInfo.PathOnPC, "Assistant");
                            Folder.CheckFolder(strAssistantPath);
                            string strFriendPath = Path.Combine(strAssistantPath, "Friend.txt");
                            string strTempPath = strFriendPath + "Temp";
                            string strContent = File.ReadAllText(frm.mStrContactPath, Encoding.UTF8);
                            SaveFriendFile(strFriendPath, strTempPath, strContent);
                        }
                    }
                    catch { }
                }
            }
        }

        private void lblRegister_TextClick(object sender, EventArgs e)
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo != null)
                this.PaidRemind(waInfo, false);
        }

        private void btnFeedback_Click(object sender, System.EventArgs e)
        {
            frmFeedback frm = new frmFeedback();
            frm.ShowDialog(this);
        }

        private void frmSetting_WeChatDataLoadDBHandler(object sender, EventArgs e)
        {
            mCurrentDataGridViewRow = null;

            this.txtSearchChat.SearchText = "";
            if (this.pnlTitle.Visible)
                btnTimeScreen_Click(null, null);

            OnWeChatUsersMenuItemClick(this.mCurrentWeChatUsersMenuItem, null);
        }

        private void frmSetting_WeChatArtificialAccreditHandler(object sender, EventArgs e)
        {
            try
            {
                bool isArtificialAccredit = IniSetting.GetWeChatArtificialAccredit();
                this.pbArtificialAccredit.Visible = isArtificialAccredit;
                this.lblArtificialAccredit.Visible = isArtificialAccredit;
                this.btnrgsq.Visible = isArtificialAccredit;
            }
            catch { }
        }

        #endregion

        #region "---  加载电脑上的微信账号  ---"

        private void LoadWechatAccountFromPCThread(bool isDefaultSelect = false)
        {
            string wechatPath = txtWechatPath.Text.Trim();
            if (string.IsNullOrWhiteSpace(wechatPath) ||
                !wechatPath.Contains("WeChat Files") ||
                !Directory.Exists(wechatPath))
            {
                wechatPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "WeChat Files");
                InvokeOnUiThreadIfRequired(this, () => txtWechatPath.Text = wechatPath);
            }

            if (string.IsNullOrEmpty(wechatPath))
            {
                this.DoGetWechatPath();
                return;
            }
            else if (!Directory.Exists(wechatPath))
            {
                tbMessageBox.Show(this, "文件夹不存在，请重新选择。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DoGetWechatPath();
                return;
            }


            try
            {
                WechatAccInfo waInfo = GetAcctInfo();
                if (waInfo != null &&
                    ServerIniSetting.IsMsgHide() &&
                    WechatHeplerPC.mAssistInfo.IsPay &&
                    waInfo.AState != ActivateState.Normal)
                {
                    this.lblRegister.Visible = true;
                    this.llblExportPreview.Visible = true;
                }
                else
                {
                    this.lblRegister.Visible = false;
                    this.llblExportPreview.Visible = false;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "LoadWechatAccountFromPCThread1");
            }

            IniClass.SetIniSectionKey("Setting", "WeChatBackFolder", this.txtWechatPath.Text, Folder.ConfigIniFile);

            this.SetViewStyle(MViewType.Loading);

            if (this.mTdWechatAccountFromPC != null && this.mTdWechatAccountFromPC.ThreadState != ThreadState.Stopped)
            {
                this.mTdWechatAccountFromPC.Abort();
                Utility.WaitSeconds(0.5);
            }
            this.mTdWechatAccountFromPC = new Thread(new ParameterizedThreadStart(DoLoadWechatAccountFromPC));
            this.mTdWechatAccountFromPC.IsBackground = true;
            this.mTdWechatAccountFromPC.Start(isDefaultSelect);
        }

        private void DoLoadWechatAccountFromPC(object obj)
        {
            this.SetLoadingMsg("解析备份数据，请耐心等待...");
            try
            {
                AccountIniSetting.DeleteBackupkey();
            }
            catch { }

            try
            {
                WechatHeplerPC.GetWechatAccountFromPCHandler -= WechatHeplerPC_GetWechatAccountFromPCHandler;
                WechatHeplerPC.GetWechatAccountFromPCHandler += WechatHeplerPC_GetWechatAccountFromPCHandler;

                this.mIsLoadBackupInfoByComputer = false;
                string weChatPath = this.txtWechatPath.Text;

                if (!this.txtWechatPath.Text.EndsWith("WeChat Files") && this.txtWechatPath.Text.Contains("WeChat Files"))
                {
                    weChatPath = weChatPath.Substring(0, weChatPath.IndexOf("WeChat Files") + "WeChat Files".Length);
                }

                //获取备份数据
                WechatHeplerPC.GetWechatAccountFromPC(weChatPath);
                this.AddAccountDataGridView(WechatHeplerPC.mLstWechatAccounts);
                this.LoadWechatIcon();
                if (!Convert.ToBoolean(obj))
                {
                    this.SetViewStyle(MViewType.WechatList);
                }
                else
                {
                    // 重新加载的时候， 直接加载特定的微信账号
                    foreach (WechatAccInfo item in WechatHeplerPC.mLstWechatAccounts)
                    {
                        if (item.PathOnPC.Equals(this.mCurrentSelectPath, StringComparison.InvariantCultureIgnoreCase))
                        {
                            //this.ShowAccountThread(item);
                            RunLoadFriendWorker(item);
                            break;
                        }
                    }
                    int rowIndex = 0;
                    foreach (DataGridViewRow item in dgvAccount.Rows)
                    {
                        if (((WechatAccInfo)item.Tag).PathOnPC == this.mCurrentSelectPath)
                        {
                            item.Selected = true;
                            dgvAccount.SetCurrentRow(rowIndex);
                        }
                        rowIndex++;
                    }

                }
            }
            catch (ThreadAbortException e)
            {
                System.Diagnostics.Debug.Write(e);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "LoadWechatAccountFromPCThread");
            }

            //获取备份激活状态
            if (this.mTdGetBackupInfoByComputer != null && this.mTdGetBackupInfoByComputer.ThreadState != ThreadState.Stopped)
                this.mTdGetBackupInfoByComputer.Abort();

            this.mTdGetBackupInfoByComputer = new Thread(new ThreadStart(DoGetBackupInfoByComputer));
            this.mTdGetBackupInfoByComputer.IsBackground = true;
            this.mTdGetBackupInfoByComputer.Start();
        }

        private void DoGetBackupInfoByComputer()
        {
            try
            {
                string strErrorMsg = "";

                string weChatPath = this.txtWechatPath.Text;

                if (!this.txtWechatPath.Text.EndsWith("WeChat Files") && this.txtWechatPath.Text.Contains("WeChat Files"))
                {
                    weChatPath = weChatPath.Substring(0, weChatPath.IndexOf("WeChat Files") + "WeChat Files".Length);
                }


                //获取备份激活状态
                WechatHeplerPC.GetBackupInfoByComputer(WechatHeplerPC.mLstWechatAccounts, weChatPath, ref strErrorMsg);

                if (!string.IsNullOrEmpty(strErrorMsg))
                    this.ShowErrorMessage(strErrorMsg);

                this.mIsLoadBackupInfoByComputer = true;

                foreach (WechatAccInfo item in WechatHeplerPC.mLstWechatAccounts)
                {
                    this.ChangeRowType(item, this.dgvAccount);
                }

            }
            catch (ThreadAbortException e)
            {
                System.Diagnostics.Debug.Write(e);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoGetBackupInfoByComputer");
            }
        }

        private void WechatHeplerPC_GetWechatAccountFromPCHandler(object sender, WechatAccountEventArgs e)
        {
            this.SetLoadingMsg(e.StrMessage);
        }

        private delegate void ShowErrorMessageHandler(string strErrorMsg);
        private void ShowErrorMessage(string strErrorMsg)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ShowErrorMessageHandler(ShowErrorMessage), strErrorMsg);
            }
            else
            {
                try
                {
                    tbMessageBox.Show(this, strErrorMsg, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "ShowErrorMessage");
                }
            }
        }

        private void LoadWechatIcon()
        {
            if (this.mTdLoadIcon != null && this.mTdLoadIcon.ThreadState != ThreadState.Stopped)
            {
                return;
            }

            this.mTdLoadIcon = new Thread(new ThreadStart(LoadWechatIconThread));
            this.mTdLoadIcon.IsBackground = true;
            this.mTdLoadIcon.Start();
        }

        private void LoadWechatIconThread()
        {
            try
            {
                foreach (DataGridViewRow item in dgvAccount.Rows)
                {
                    if (item == null || item.Tag == null || item.Tag.GetType() != typeof(WechatAccInfo))
                    {
                        continue;
                    }

                    WechatAccInfo info = (WechatAccInfo)item.Tag;
                    try
                    {
                        string strIconFile = Path.Combine(Folder.CacheFolder, string.Format(@"HeadIcon\{0}\{1}.pic", info.Account, info.Account));
                        if (!File.Exists(strIconFile))
                        {
                            if (!File.Exists(info.IconFilePath))
                            {
                                Utility.DownLoadFile(info.IconUrl, info.IconFilePath, 20000);
                            }

                            strIconFile = info.IconFilePath;
                        }

                        Image imgIcon = Utility.GetImageFormFile(strIconFile);
                        if (imgIcon != null)
                        {
                            tbDataGridViewTextBoxCell cellName = (tbDataGridViewTextBoxCell)item.Cells["colName"];
                            cellName.tbIcon = imgIcon;
                            info.Icon = imgIcon;
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "MainForm_LoadWechatIconThread");
                    }
                }
            }
            catch (ThreadAbortException e)
            {
                System.Diagnostics.Debug.Write(e);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "LoadWechatIconThread");
            }
        }

        private delegate List<tbDataGridViewRow> AddAccountDataGridViewHandler(List<WechatAccInfo> lstAccount);
        private List<tbDataGridViewRow> AddAccountDataGridView(List<WechatAccInfo> lstAccount)
        {
            List<tbDataGridViewRow> rowFindData = new List<tbDataGridViewRow>();

            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new AddAccountDataGridViewHandler(AddAccountDataGridView), new object[] { lstAccount });
                }
                else
                {
                    this.dgvAccount.SuspendLayout();
                    this.dgvAccount.Rows.Clear();
                    foreach (WechatAccInfo item in lstAccount)
                    {
                        try
                        {
                            tbDataGridViewRow row = new tbDataGridViewRow();
                            row.Height = 58;

                            string strName = item.Name;
                            strName = Utility.ReplaceWinIllegalName(strName);

                            row.CreateCells(this.dgvAccount, false, strName + "\r\n" + item.Account,
                                            global::iWechatAssistant.Properties.Resources.cell_question,
                                            item.PathOnPC, this.mLoading, "");

                            this.dgvAccount.Rows.Add(row);

                            tbDataGridViewTextBoxCell cellName = (tbDataGridViewTextBoxCell)row.Cells["colName"];
                            cellName.tbIconShadow = true;
                            cellName.tbIconSize = this.mAppIconSize;
                            cellName.tbIcon = this.mDefaultHeadIcon;

                            tbDataGridViewButtonCell cellButton = (tbDataGridViewButtonCell)row.Cells["colButton"];
                            cellButton.Value = "查看聊天信息";

                            if (File.Exists(item.IconFilePath))
                            {
                                Image imgIcon = Utility.GetImageFormFile(item.IconFilePath);

                                string strIconFolder = Path.Combine(Folder.CacheFolder, string.Format(@"HeadIcon\{0}", item.Account));
                                Folder.CheckFolder(strIconFolder);
                                string strIconPath = Path.Combine(strIconFolder, string.Format(@"{0}.pic", item.Account));
                                try
                                {
                                    File.Copy(item.IconFilePath, strIconPath, true);
                                }
                                catch { }


                                cellName.tbIcon = imgIcon;
                            }
                            row.Tag = item;
                        }
                        catch (Exception ex)
                        {
                            Common.LogException(ex.ToString(), "AddDictToDataGridView");
                        }
                    }
                    this.dgvAccount.ResumeLayout();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "AddAccountDataGridView_1");
            }
            return rowFindData;
        }


        #endregion

        #region "---  加载聊天记录列表  ---"

        private delegate List<tbDataGridViewRow> AddDictToDataGridViewHandler(Dictionary<string, WechatTalk> dict, bool isRefresh);
        private List<tbDataGridViewRow> AddDictToDataGridView(Dictionary<string, WechatTalk> dict, bool isRefresh)
        {
            List<tbDataGridViewRow> rowFindData = new List<tbDataGridViewRow>();
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new AddDictToDataGridViewHandler(AddDictToDataGridView), new object[] { dict, isRefresh });
                }
                else
                {
                    this.dgvFriend.SuspendLayout();
                    this.dgvFriend.Rows.Clear();

                    try
                    {
                        WechatTalk[] aryWechats = dict.Values.ToArray<WechatTalk>();
                        WechatAccInfo waInfo = GetAcctInfo();
                        if (waInfo == null || waInfo.DicWeChatFriendInfo == null) return null;
                        foreach (WechatTalk item in aryWechats)
                        {
                            string strName = "";
                            string strDescription = "";
                            string strLastTime = "";
                            string strTalkId = "";
                            try
                            {
                                if (waInfo.DicWeChatFriendInfo.ContainsKey(item.UserName))
                                {
                                    WeChatFriendInfo winfo = new WeChatFriendInfo();
                                    if (waInfo.DicWeChatFriendInfo.TryGetValue(item.UserName, out winfo))
                                    {
                                        //if (winfo.StrNickName.Length > 0 && item.NickName.Length == 0)
                                        //    item.NickName = winfo.StrNickName;
                                        if (!string.IsNullOrWhiteSpace(winfo.StrRemark))
                                            item.NickName = winfo.StrRemark;
                                        else if (!string.IsNullOrWhiteSpace(winfo.StrNickName))
                                            item.NickName = winfo.StrNickName;
                                    }
                                }
                            }
                            catch { }

                            if (string.IsNullOrWhiteSpace(strName))
                            {
                                strName = item.NickName;
                                if (string.IsNullOrWhiteSpace(strName))
                                {
                                    strName = item.UserName;
                                }

                                strName = Utility.ReplaceWinIllegalName(strName);
                            }

                            //string strMsg = this.OperateLastChatText(this.GetViewLastChat(item));
                            //if (ServerIniSetting.IsMsgHide() && WechatHeplerPC.mAssistInfo.IsPay && this.mCurrentWAInfo.AState != ActivateState.Normal)
                            //    strMsg = this.MsgHide(strMsg);

                            strDescription = strName + "\r\n";

                            if (strLastTime.Length == 0)
                            {
                                strLastTime = this.GetLastChatTime(item.EndTime) + "\r\n" + " ";
                            }
                            if (strTalkId.Length == 0)
                            {
                                strTalkId = item.TalkId;
                            }

                            tbDataGridViewRow row = new tbDataGridViewRow();
                            row.Height = 58;
                            row.CreateCells(this.dgvFriend, false, "", strDescription, strLastTime, false, strTalkId);
                            this.mIsAddRow = true;
                            this.dgvFriend.Rows.Add(row);
                            this.mIsAddRow = false;
                            tbDataGridViewTextBoxCell cellName = (tbDataGridViewTextBoxCell)row.Cells["colName"];
                            cellName.tbIconShadow = true;
                            cellName.tbIconSize = this.mAppIconSize;
                            cellName.tbIcon = this.mDefaultHeadIcon;
                            row.Tag = item;
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "AddDictToDataGridView");
                    }
                    this.SetLinkmanMsg();
                    this.dgvFriend.ResumeLayout();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "AddDictToDataGridView_1");
            }
            return rowFindData;
        }

        private string GetLastChatTime(DateTime LastChatTime)
        {
            string strTime = string.Empty;
            TimeSpan ts = DateTime.Now.Date.Subtract(LastChatTime.Date);
            //只需比较日期就好
            try
            {
                switch (ts.Days)
                {
                    case 0:
                        strTime = LastChatTime.ToString("HH:mm");
                        break;
                    case 1:
                        strTime = "昨天";
                        break;
                    default:
                        string strYear = LastChatTime.Year != DateTime.Now.Year ? LastChatTime.Year.ToString() : LastChatTime.Year.ToString().Substring(2, 2);
                        string strMonth = LastChatTime.Month < 10 ? "0" + LastChatTime.Month.ToString() : LastChatTime.Month.ToString();
                        string strDay = LastChatTime.Day < 10 ? "0" + LastChatTime.Day.ToString() : LastChatTime.Day.ToString();
                        strTime = string.Format("{0}-{1}-{2}", strYear, strMonth, strDay);
                        break;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetLastChatTime");
            }
            return strTime;
        }

        private string GetViewLastChat(WechatTalk item)
        {
            string strResult = item.LastChatText;
            string strNickNameEx = "";
            try
            {
                if (item.LastWechatMessageItem != null)
                {
                    string strTypeForWeb = "";
                    string strMsgForWeb = item.LastWechatMessageItem.MessageContent;
                    JsonArray arrHtml5ContentForWeb = null;

                    WechatAccInfo waInfo = GetAcctInfo();
                    if (waInfo == null) return "";

                    if (!WechatHeplerPC.IsReceivedMsg(item.LastWechatMessageItem, waInfo) && WechatHeplerPC.CheckIsChatRoom(item.UserName))
                    {
                        string strUsrName = "";
                        string strNickName = "";
                        string wxAccount = "";
                        string strPetName = "";
                        WechatHeplerPC.AnalyseGroup(waInfo.DicWeChatFriendInfo/*mCurrentWAInfo.DicWeChatFriendInfo*/,
                            ref strUsrName, ref wxAccount,
                            ref strNickName, ref strMsgForWeb, ref strPetName,
                            waInfo.LstWeChatGroupFriendInfo, item.UserName);
                    }

                    AnalyseMessageType(DataFilterType.OnlyListTitle, "", ref strTypeForWeb,
                                       ref strMsgForWeb, ref arrHtml5ContentForWeb, item.LastWechatMessageItem, ref strNickNameEx);

                    switch (item.LastWechatMessageItem.SociaChatType)
                    {
                        case SociaChatType.Text:
                            strResult = strMsgForWeb;
                            break;
                        case SociaChatType.Picture:
                            strResult = "[图片]";
                            break;
                        case SociaChatType.Voice:
                            strResult = "[语音]";
                            break;
                        case SociaChatType.NameCard:
                            strResult = "[名片]";
                            break;
                        case SociaChatType.Video:
                            strResult = "[视频]";
                            break;
                        case SociaChatType.Location:
                            strResult = "[位置]";
                            break;
                        case SociaChatType.webpage:
                            strResult = "[网页]";
                            break;
                        case SociaChatType.WebPageGroup:
                            strResult = "[聊天记录集]";
                            break;
                        case SociaChatType.WebPageText:
                            strResult = "[文本文件附件]";
                            break;
                        case SociaChatType.WebPageApk:
                            strResult = "[其他文件附件]";
                            break;
                        case SociaChatType.WebPageDoc:
                            strResult = "[Word文件附件]";
                            break;
                        case SociaChatType.WebPageXls:
                            strResult = "[Excel文件附件]";
                            break;
                        case SociaChatType.WebPagePPT:
                            strResult = "[PPT文件附件]";
                            break;
                        case SociaChatType.WebPagePDF:
                            strResult = "[pdf文件附件]";
                            break;
                        case SociaChatType.WebPageVideo:
                            strResult = "[视频号]";
                            break;
                        case SociaChatType.WebPageRefer:
                            strResult = "[引用]";
                            break;
                        case SociaChatType.WebPageProgram:
                            strResult = "[小程序]";
                            break;
                        case SociaChatType.WebPageOfficial:
                            strResult = "[公众号名片]";
                            break;
                        case SociaChatType.VideoConnected:
                            strResult = "[视频未接通]";
                            break;
                        case SociaChatType.SystemMessages:
                            strResult = "[系统消息]";
                            break;
                        case SociaChatType.SmallVideo:
                            strResult = "[视频]";
                            break;
                        case SociaChatType.SpecialExpression:
                            strResult = "[动画表情]";
                            break;
                        case SociaChatType.File:
                            strResult = "[文件]";
                            break;
                        default:
                            strResult = "[其他格式]";

                            Common.Log(string.Format("Type:{0}，Content:{1}",
                                        item.LastWechatMessageItem.MessageType,
                                        item.LastWechatMessageItem.MessageContent), m_bIsDebug);
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetViewLastChat");
            }
            return strResult;
        }

        private string OperateLastChatText(string strLastChatText)
        {
            if (strLastChatText.Length == 0)
            {
                return " ";
            }
            try
            {
                //截取
                if (this.mGraphic == null)
                {
                    this.mGraphic = this.CreateGraphics();
                }
                string strResult = strLastChatText;
                StringFormat strFormat = GuiHelper.CreateStringFormat(ContentAlignment.MiddleLeft, false);

                int charactersFitted = 0;
                int linesFilled = 0;
                //Common.Log("OSHelper.OSType:" + OSHelper.OSType.ToString());
                if (OSHelper.OSType != OSType.XP && OSHelper.OSType != OSType.XP_SP2 && OSHelper.OSType != OSType.WinXP64)
                {
                    this.mGraphic.MeasureString(strLastChatText, new Font("宋体", 9, FontStyle.Regular), new SizeF(100f, 15.2f), strFormat, out charactersFitted, out linesFilled);
                    if (charactersFitted != strLastChatText.Length && strLastChatText.Length > 3)
                    {
                        strResult = strLastChatText.Substring(0, (charactersFitted - 3 <= 0 ? 0 : charactersFitted - 3)) + "...";
                    }
                    if (charactersFitted >= 9 && strLastChatText.Length > 7)
                        strResult = strLastChatText.Substring(0, 7) + "...";
                }
                else
                {
                    if (strLastChatText.Length > 7)
                    {
                        strResult = strLastChatText.Substring(0, 7) + "...";
                    }
                }

                return strResult;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "iWechatAssistant_OperateLastChatText");
            }
            return strLastChatText;
        }

        #endregion

        #region "---  获取微信消息  ---"

        private void RunLoadFriendWorker(WechatAccInfo acctInfo)
        {
            SetViewStyle(MViewType.Loading);
            bool bIsBusy = false;

            if (m_bgwLoadFriends == null)
            {
                m_bgwLoadFriends = new BackgroundWorker();
                m_bgwLoadFriends.WorkerSupportsCancellation = true;
                m_bgwLoadFriends.DoWork += new DoWorkEventHandler(LoadingFriends_DoWork);
                m_bgwLoadFriends.RunWorkerCompleted += new RunWorkerCompletedEventHandler(LoadingFriends_RunWorkerCompleted);

                bIsBusy = m_bgwLoadFriends.IsBusy;
            }
            else
            {
                try
                {
                    if (m_bgwLoadFriends.IsBusy)
                    {
                        m_bgwLoadFriends.CancelAsync();
                        Utility.WaitSeconds(0.1);
                    }
                    while (m_bgwLoadFriends.IsBusy)
                    {
                        Utility.WaitSeconds(0.1);
                    }

                    bIsBusy = m_bgwLoadFriends.IsBusy;
                }
                catch (Exception ex)
                {
                    bIsBusy = true;

                    Common.LogException(ex.ToString(), "RunLoadFriendWorker");
                }
            }

            if (!bIsBusy)
                m_bgwLoadFriends.RunWorkerAsync(acctInfo);
        }

        private void LoadingFriends_DoWork(object sender, DoWorkEventArgs e)
        {
            // Added by Utmost 2020.05.29
            ChatWebPage.ClearChatOnWebPage(this.wbsChat);
            ChatWebPage.ClearChatOnWebPage(this.wbsChatDetail);
            this.ShowWebDetail(false);

            e.Result = LoadFriends((WechatAccInfo)e.Argument);

            GC.Collect();
        }

        private void LoadingFriends_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            ShowAccountThread((WechatAccInfo)e.Result);
        }

        public void ShowAccountThread(WechatAccInfo waInfo)
        {
            if (this.bgwShowAccount.IsBusy)
            {
                this.bgwShowAccount.CancelAsync();
                Utility.WaitSeconds(0.1);
            }

            // 解决有时候结束不了的问题
            while (this.bgwShowAccount.IsBusy)
            {
                Utility.WaitSeconds(0.1);
            }

            // Utmost on 2020.05.29
            //ChatWebPage.ClearChatOnWebPage(this.wbsChat);
            //ChatWebPage.ClearChatOnWebPage(this.wbsChatDetail);
            //this.ShowWebDetail(false);

            this.mIsLoadHeadIconFinish = false;

            if (waInfo != null && !this.bgwShowAccount.IsBusy)
            {
                this.bgwShowAccount.RunWorkerAsync(waInfo);
            }
        }

        private void bgwShowAccount_DoWork(object sender, DoWorkEventArgs e)
        {
            WechatAccInfo waInfo = (WechatAccInfo)e.Argument;
            int errCode = 0;

            try
            {
                this.SetLoadingMsg("解析备份数据，请耐心等候...");
                this.SetViewStyle(MViewType.Loading);

                //this.mCurrentWAInfo = waInfo;
                this.mCurrentSelectPath = waInfo.PathOnPC;

                //// 如果默认已经选择设备， 代表信息已经都有了，直接跳过去，去加载相关的设备信息
                //if (waInfo.SelectedDeviceUDID.Length == 0)
                //{
                ////1.付费 提示
                //if (!this.PaidRemind(waInfo))
                //    return;

                //2.判断是否授权
                if (string.IsNullOrEmpty(waInfo.Password))
                {
                    this.SetViewStyle(MViewType.Accredit);
                    return;
                }
                this.ChangeRowType(waInfo);

                //获取当前帐号 所有设备备份
                mWechatHeplerPC = WechatHeplerPC.GetInstance(waInfo.PathOnPC, waInfo.Password);
                mWechatHeplerPC.GetAccountBackupedDevice(waInfo);

                if (waInfo.LstWCABInfo == null || waInfo.LstWCABInfo.Count == 0)
                {
                    // 数据加载异常
                    string errMsg = string.Format("数据加载异常：路径{0} 可能包括特殊字符>>>>>>", waInfo.PathOnPC);
                    Common.Log(errMsg, "DoSWeHelpWork", true);

                    this.SetViewStyle(MViewType.WechatList);
                    errCode = -1;
                    e.Result = new object[] { errCode, null, null };

                    return;
                }
                //}

                this.SetLoadingMsg("加载好友列表，请耐心等候...");

                string deviceUDID = IniSetting.GetDeviceUDID(waInfo.Account, "DeviceUDID");
                if (!string.IsNullOrEmpty(deviceUDID))
                {
                    waInfo.SelectedDeviceUDID = deviceUDID;
                }
                else
                {
                    IniSetting.SetDeviceUDID(waInfo.Account, "DeviceUDID", waInfo.SelectedDeviceUDID);
                }


                //加载聊天好友
                WeChatAccBackupInfo info = waInfo.GetSelectedDevice();
                mCurrentBackupDBPath = info.BCConfigInfo.BackupDBPath;
                //mCurrentBackupDBPwd = waInfo.Password;
                mCurrentBackupMediaFile = info.MediaFilePath;
                waInfo.StrWeChatTalkDBPath = Path.Combine(IniSetting.GetWechatAssistantDBFolder(), Path.GetFileName(info.SavePath) + @"\WeChatTalkDB.db");//Path.GetDirectoryName(mCurrentBackupDBPath)

                //-----------------------------------------------------------------------------------------------------------------------
                bool occupied = false;
                foreach (FileInfo item in new DirectoryInfo(this.mCurrentBackupMediaFile).GetFiles())
                {
                    if (!item.Name.StartsWith("BAK_"))
                        continue;
                    if (IsOccupied(item.FullName))
                    {
                        occupied = true;
                        break;
                    }
                }
                if (occupied)
                {
                    System.Diagnostics.Process[] pros = System.Diagnostics.Process.GetProcessesByName("WeChat");
                    if (pros.ToList().Count > 0)
                    {
                        bool blnChecked = false;
                        //System.Diagnostics.Process pro = pros[0];
                        DialogResult dr = tbMessageBox.Show(this, "因电脑版微信占用着备份文件，导致微信备份助手读取数据失败！\n\r确认已完成备份,确定重启微信。", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Information);//, MessageBoxDefaultButton.Button1, "", ref blnChecked, new string[] { "重启", "取消" }
                        if (dr == DialogResult.Yes)
                        {
                            CloseWeChat();
                            //string wechatpath = pro.MainModule.FileName;
                            //pro.Kill();
                            //System.Diagnostics.Process.Start(wechatpath);
                            //Application.Restart();
                        }
                        //else if (dr == DialogResult.No)
                        //{
                        //    string wechatpath = pro.MainModule.FileName;
                        //    pro.Kill();
                        //    System.Diagnostics.Process.Start(wechatpath);
                        //    this.mCurrentSelectPath = waInfo.PathOnPC;
                        //    this.LoadWechatAccountFromPCThread();
                        //}

                        this.SetViewStyle(MViewType.WechatList);
                        return;
                    }
                }
                //-----------------------------------------------------------------------------------------------------------------------

                dgvFriend.Tag = mWechatHeplerPC.GetTalkList(this.mCurrentBackupDBPath, waInfo.Password, this.mCurrentBackupMediaFile, waInfo);
                e.Result = new object[] { errCode, waInfo, dgvFriend.Tag };

            }
            catch (Exception ex)
            {
                Common.LogException(MyJson.SerializeToJsonString(waInfo), "ShowAccount");
                Common.LogException(ex.ToString(), "ShowAccount");
                this.SetViewStyle(MViewType.WechatList);
            }

            try
            {
                this.mWechatHeplerPC.DailyLiving(waInfo.InnerAccount.Length > 0 ? waInfo.InnerAccount : waInfo.Account, waInfo.AState);
            }
            catch { }
        }


        private void CloseWeChat()
        {
            //RegistryKey installerProductsReg = Registry.LocalMachine.OpenSubKey(@"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\WeChat");

            //if (installerProductsReg != null)
            //{
            string cmdPath = string.Empty;

            if (WechatHelp.CheckIs64())
            {
                cmdPath = Path.Combine(Folder.AppFolder, "IncludeDlls", "CWehelp_x64", "tbCmd64.exe");
            }
            else
            {
                cmdPath = Path.Combine(Folder.AppFolder, "IncludeDlls", "CWehelp_x86", "tbCmd40.exe");
            }

            Common.CallCmdExe(new string[] { "ReStartWx" }, 0, pathExe: cmdPath);
            //}
        }


        private bool IsOccupied(string filepath)
        {
            FileStream stream = null;
            try
            {
                stream = new FileStream(filepath, FileMode.Open, FileAccess.Read, FileShare.Read);
                return false;
            }
            catch
            {
                return true;
            }
            finally
            {
                if (stream != null)
                    stream.Close();
            }
        }

        private bool PaidRemind(WechatAccInfo waInfo, bool returnhome = true)
        {
            bool isRelust = true;
            try
            {
                //是否提示付费
                if (WechatHeplerPC.mAssistInfo.IsPay)
                {
                    //Common.Log(string.Format("MainForm's PaidRemind()>>>>> IsPay:{0} AState:{1}", WechatHeplerPC.mAssistInfo.IsPay, waInfo.AState));

                    //1.判断是否激活
                    if (waInfo.AState != ActivateState.Normal)
                    {
                        frmCharge frm = new frmCharge(waInfo, this.mWechatHeplerPC);
                        frm.BringToFront();
                        frm.StartPosition = FormStartPosition.Manual;
                        frm.Location = new Point(this.Left + (this.Width - frm.Width) / 2, this.Top + (this.Height - frm.Height) / 2);
                        if (frm.ShowDialog() != System.Windows.Forms.DialogResult.OK)
                        {
                            if (returnhome)
                                this.SetViewStyle(MViewType.WechatList);
                            return false;
                        }
                        this.ChangeRowType(waInfo);
                        this.lblRegister.Visible = false;
                        this.llblExportPreview.Visible = false;
                        this.dgvFriend_SelectionChanged(null, null);
                    }
                }

                // 如果跳过付费步骤， 需要直接激活他的电脑
                if (!WechatHeplerPC.mAssistInfo.IsPay && waInfo.AState != ActivateState.Normal)
                {
                    //Common.Log("<<<  actvie11");

                    if (!WechatHeplerPC.ActiveComputeWithoutCode(waInfo))
                    {
                        tbMessageBox.Show(this, "请联系客服人员进行处理。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        if (returnhome)
                            this.SetViewStyle(MViewType.WechatList);
                        return false;
                    }
                    else
                    {
                        Common.Log("<<<  重新获取数据", m_bIsDebug);

                        // 重新获取数据
                        LoadWechatAccountFromPCThread(true);
                    }

                    return false;
                }
                else
                {
                    Common.Log(string.Format("MainForm's PaidRemind()>>>>> 账号信息：{0}, State：{1}", waInfo.Account, waInfo.AState), m_bIsDebug);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "PaidRemind");
            }
            return isRelust;
        }


        private WechatAccInfo LoadFriends(WechatAccInfo waInfo)
        {
            if (waInfo == null) return null;

            //var s2 = System.Diagnostics.Stopwatch.StartNew();

            Dictionary<string, WeChatFriendInfo> dictFriendInfos = new Dictionary<string, WeChatFriendInfo>(StringComparer.InvariantCultureIgnoreCase);
            List<WeChatGroupFriendInfo> listGroupInfos = new List<WeChatGroupFriendInfo>();

            string strAssistantPath = Path.Combine(waInfo.PathOnPC, "Assistant");
            Folder.CheckFolder(strAssistantPath);
            string strFriendPath = Path.Combine(strAssistantPath, "Friend.txt");
            try
            {
                string strTempPath = string.Format("{0}.tmp", strFriendPath);
                string strContent = "";

                if (!File.Exists(strFriendPath))
                {
                    Common.Log("friendPath unfind ", true);
                    JsonObject jObject = new JsonObject();
                    jObject.Add("wxid", waInfo.Account);
                    waInfo.FriendlistURL = HtmlHelper.GetFriendTxtUrl(JsonParser.SaveString(jObject));

                    if (waInfo.FriendlistURL == null)
                        Common.Log("waInfo.FriendlistURL is null", true);

                    if (waInfo.FriendlistURL.Length == 0)
                    {
                        jObject = new JsonObject();
                        jObject.Add("wxid", waInfo.InnerAccount);
                        waInfo.FriendlistURL = HtmlHelper.GetFriendTxtUrl(JsonParser.SaveString(jObject));
                    }

                    if (!string.IsNullOrEmpty(waInfo.FriendlistURL))
                    {
                        int intTry = 0;
                        strContent = Utility.GetContentStringFromUrl(waInfo.FriendlistURL, Encoding.UTF8, 20000);
                        while (string.IsNullOrEmpty(strContent) && intTry < 3)
                        {
                            Utility.WaitSeconds(0.5);
                            strContent = Utility.GetContentStringFromUrl(waInfo.FriendlistURL, Encoding.UTF8, 20000);
                            intTry += 1;
                        }
                        strContent = SaveFriendFile(strFriendPath, strTempPath, strContent);

                        if (string.IsNullOrEmpty(strContent))
                        {
                            Common.Log(string.Format("获取好友信息失败！=====>url:{0}", waInfo.FriendlistURL), m_bIsDebug);
                        }
                    }

                    Common.Log(waInfo.FriendlistURL, true);
                    strContent = Common.DecryptDES(strContent, "pw91!3#1", "19wp!3#1");
                }
                else
                {
                    //strContent = File.ReadAllText(strFriendPath, Encoding.UTF8);

                    // faster
                    //StringBuilder sb = new StringBuilder();

                    //using (StreamReader sr = new StreamReader(strFriendPath, Encoding.UTF8))
                    //{
                    //    string lines;
                    //    while ((lines = sr.ReadLine()) != null)
                    //        sb.Append(lines);
                    //}

                    string filepath = Common.DecryptDESToFile(strFriendPath, "pw91!3#1", "19wp!3#1");
                    strContent = File.ReadAllText(filepath, Encoding.UTF8);
                }

                //Common.Log(strContent, true);

                if (string.IsNullOrEmpty(strContent))
                {
                    //Common.Log(string.Format("访问：{0}  得到的数据为空！！！", waInfo.FriendlistURL));
                    Common.Log("好友数据为空！！！", m_bIsDebug);
                    goto Do_Exit;
                }

                JsonObject objJson = JsonParser.ParseString(strContent, false);
                // Modified by Utmost on 2020.06.12
                // data组数据：8113，group组数据：47
                // 优化前，运行7次平均时间：421.8143ms
                // 优化后，运行7次平均时间：372.5986ms

                if (objJson == null)
                {
                    Common.Log("objJson is null", true);
                    goto Do_Exit;
                }

                if (!objJson.ContainsKey("data"))
                {
                    Common.Log(string.Format("得到的数据：{0}  不包含：data", strContent), m_bIsDebug);
                }
                else
                {
                    try
                    {
                        JsonObject jsonData = objJson["data"] as JsonObject;
                        foreach (KeyValuePair<string, IJsonType> kv in jsonData.ToArray())
                        {
                            try
                            {
                                if (kv.Value is JsonNull)
                                    continue;

                                JsonObject item = (JsonObject)kv.Value;

                                WeChatFriendInfo friendInfo = new WeChatFriendInfo();

                                // 优化后
                                IJsonType paraValue = null;
                                if (item.TryGetValue("m_nsUsrName", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        friendInfo.StrWXID = (paraValue as JsonString).Value;
                                }

                                if (item.TryGetValue("nickname", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        friendInfo.StrNickName = (paraValue as JsonString).Value;
                                }

                                if (item.TryGetValue("m_nsRemark", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        friendInfo.StrRemark = (paraValue as JsonString).Value;

                                    if (string.IsNullOrWhiteSpace(friendInfo.StrRemark) &&
                                        friendInfo.StrWXID.StartsWith("gh_"))
                                    {
                                        friendInfo.StrRemark = "公众号";
                                    }
                                }

                                if (item.TryGetValue("m_nsAliasName", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        friendInfo.StrInnerAccount = (paraValue as JsonString).Value;
                                }

                                if (item.TryGetValue("m_nsHeadImgUrl", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        friendInfo.StrHeadImgUrl = (paraValue as JsonString).Value;
                                }


                                //if (friendInfo.StrWXID == "wxid_gg9xa0dvx62v22")
                                //{
                                //    Common.LogException("TS丢失头像" + friendInfo.StrHeadImgUrl);
                                //}

                                // 优化前
                                //if (item.ContainsKey("m_nsUsrName") && item["m_nsUsrName"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["m_nsUsrName"];
                                //    friendInfo.StrWXID = jPara.Value;
                                //}
                                //if (item.ContainsKey("nickname") && item["nickname"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["nickname"];
                                //    friendInfo.StrNickName = jPara.Value;
                                //}
                                //if (item.ContainsKey("m_nsRemark") && item["m_nsRemark"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["m_nsRemark"];
                                //    friendInfo.StrRemark = jPara.Value;
                                //}
                                //if (item.ContainsKey("m_nsAliasName") && item["m_nsAliasName"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["m_nsAliasName"];
                                //    friendInfo.StrInnerAccount = jPara.Value;
                                //}

                                //if (item.ContainsKey("m_nsHeadImgUrl") && item["m_nsHeadImgUrl"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["m_nsHeadImgUrl"];
                                //    friendInfo.StrHeadImgUrl = jPara.Value;
                                //}

                                if (!dictFriendInfos.ContainsKey(friendInfo.StrWXID))
                                    dictFriendInfos.Add(friendInfo.StrWXID, friendInfo);
                            }
                            catch (Exception ex)
                            {
                                Common.LogException(ex.ToString(), "LoadFriends-groupdata-data");
                            }
                        }
                    }
                    catch (Exception exe)
                    {
                        Common.LogException(exe.ToString(), "LoadFriends-groupdata-data-ex");
                    }
                }

                if (!objJson.ContainsKey("groupdata"))
                {
                    Common.Log(string.Format("得到的数据：{0}  不包含：groupdata", strContent), m_bIsDebug);
                }
                else
                {
                    JsonArray jsonGroupdata = objJson["groupdata"] as JsonArray;
                    if (jsonGroupdata.Count > 0)
                    {
                        foreach (JsonObject item in jsonGroupdata)
                        {
                            try
                            {
                                WeChatGroupFriendInfo groupInfo = new WeChatGroupFriendInfo();

                                // 优化后
                                IJsonType paraValue = null;
                                if (item.TryGetValue("ChatRoomName", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        groupInfo.StrChatRoomName = (paraValue as JsonString).Value;
                                }

                                if (item.TryGetValue("Reserved2", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        groupInfo.StrReserved2 = (paraValue as JsonString).Value;
                                }

                                if (item.TryGetValue("RoomData", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        groupInfo.LstRoomData = ConvertBuffToInfo((paraValue as JsonString).Value, dictFriendInfos);
                                }

                                if (item.TryGetValue("SelfDisplayName", out paraValue))
                                {
                                    if (paraValue.JsonTypeCode == JsonTypeCode.String)
                                        groupInfo.StrSelfDisplayName = (paraValue as JsonString).Value;
                                }

                                // 优化前
                                //if (item.ContainsKey("ChatRoomName") && item["ChatRoomName"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["ChatRoomName"];
                                //    groupInfo.StrChatRoomName = jPara.Value;
                                //}

                                //if (item.ContainsKey("Reserved2") && item["Reserved2"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["Reserved2"];
                                //    groupInfo.StrReserved2 = jPara.Value;
                                //}

                                //if (item.ContainsKey("RoomData") && item["RoomData"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["RoomData"];
                                //    groupInfo.LstRoomData = ConvertBuffToInfo(jPara.Value, dictFriendInfos);
                                //}

                                //if (item.ContainsKey("SelfDisplayName") && item["SelfDisplayName"] is JsonString)
                                //{
                                //    JsonString jPara = (JsonString)item["SelfDisplayName"];
                                //    groupInfo.StrSelfDisplayName = jPara.Value;
                                //}                            
                                if (!listGroupInfos.Contains(groupInfo) && groupInfo.LstRoomData.Count > 0)
                                {
                                    listGroupInfos.Add(groupInfo);
                                }
                            }

                            catch (Exception ex)
                            {
                                Common.LogException(JsonParser.SaveString(item), "LoadFriends-groupInfo LoadError");
                            }
                        }
                    }
                }

                objJson = null;
                strContent = null;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "LoadFriends-groupdata");
            }

            //s2.Stop();
            //System.Diagnostics.Debug.WriteLine("s2 Total: {0:0.00}ms", s2.Elapsed.TotalMilliseconds);

            Do_Exit:
            waInfo.FriendlistPCPath = strFriendPath;
            waInfo.DicWeChatFriendInfo = dictFriendInfos;
            waInfo.LstWeChatGroupFriendInfo = listGroupInfos;

            return waInfo;
        }

        private List<WeChatFriendInfo> ConvertBuffToInfo(string strBuff, Dictionary<string, WeChatFriendInfo> dicWeChatFriendInfo)
        {
            List<WeChatFriendInfo> lstInfo = new List<WeChatFriendInfo>();
            try
            {
                byte[] bytes = Convert.FromBase64String(strBuff);
                GroupRoomData_t grpb1 = GroupRoomData_t.Parser.ParseFrom(bytes);
                Google.Protobuf.Collections.RepeatedField<member_t> members1 = grpb1.Member;
                WeChatFriendInfo info = null;
                foreach (member_t item in members1)
                {
                    info = new WeChatFriendInfo();
                    info.StrWXID = item.Wxid.ToStringUtf8();
                    //info.StrNickName = item.MarkName.ToStringUtf8();
                    info.StrNickName = item.MarkName.ToStringUtf8();

                    //如果昵称为空 从好友列表里面查询
                    if (dicWeChatFriendInfo.ContainsKey(info.StrWXID) && string.IsNullOrEmpty(info.StrNickName))
                        info = dicWeChatFriendInfo[info.StrWXID];

                    if (!lstInfo.Contains(info) && info.StrNickName.Length > 0)
                        lstInfo.Add(info);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ConvertBuffToInfo");
            }
            return lstInfo;
        }

        private static string SaveFriendFile(string strFriendPath, string strTempPath, string strContent)
        {
            if (!string.IsNullOrEmpty(strContent))
            {
                //授权dll 直接加密 这边无需在加密 by cbh
                ////加密
                //strContent = Common.EncryptDES(strContent, "pw91!3#1", "19wp!3#1");
                using (StreamWriter sWriter = new StreamWriter(strTempPath, false, Encoding.UTF8))
                {
                    sWriter.Write(strContent);
                }

                if (File.Exists(strTempPath))
                {
                    try
                    {
                        File.Delete(strFriendPath);
                    }
                    catch { }
                    try
                    {
                        File.Move(strTempPath, strFriendPath);
                    }
                    catch { }
                }
            }
            return strContent;
        }

        private void bgwShowAccount_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            try
            {
                object[] arrObj = (object[])e.Result;
                if (arrObj == null || arrObj.Length < 2)
                {
                    return;
                }

                int errCode = (int)arrObj[0];

                if (errCode == -1)
                {
                    if (tbMessageBox.Show(this, "当前帐号尚未备份，点击‘确定’查看备份教程。\n备份后请刷新重试。", "提示",
                        MessageBoxButtons.OKCancel, MessageBoxIcon.Information) == DialogResult.OK)
                        Common.OpenExplorer("http://news.tongbu.com/96019.html");

                    return;
                }

                WechatAccInfo waInfo = (WechatAccInfo)arrObj[1];

                //加载设备
                this.CreateDeviceInfo2Form(waInfo);

                Dictionary<string, WechatTalk> dictTalks = (Dictionary<string, WechatTalk>)arrObj[2];
                AddDictToDataGridView(dictTalks, false);

                //加载聊天信息

                this.SetViewStyle(MViewType.ShowTalke);
                this.ShowSystemInfoOnPC(true, "查看消息，请选中左侧联系人", false);

                this.LoadHeadIcon();
                this.mWeChatMsgWebPage = WeChatMsgWebPage.GetInstance(mWechatHeplerPC, mCurrentBackupDBPath, waInfo.Password,
                                                                      mCurrentBackupMediaFile, waInfo, mWeChatiOSDevice);

                //// 默认选中一行的效果还没出来
                //if (dgvFriend.Rows.Count > 0)
                //{
                //    //this.dgvFriend.Rows[0].Selected = false;
                //    this.dgvFriend.Rows[0].Selected = true;
                //    this.dgvFriend.Rows[0].Selected = true;
                //}

                // 清空右侧页面的数据
                //ChatWebPage.ClearChatOnWebPage(this.wbsChat);
                //ChatWebPage.ClearChatOnWebPage(this.wbsChatDetail);
                //this.ShowWebDetail(false);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "bgwShowAccount_RunWorkerCompleted");
                this.SetViewStyle(MViewType.WechatList);
            }

            try
            {
                btnTimeSelect.Text = "全部";
                dtpStart.Value = Convert.ToDateTime("1970-01-02 00:00:01");
                dtpEnd.Value = Convert.ToDateTime("2999-01-01 00:00:01");

                dtpStart.MaxDate = dtpEnd.Value;
                dtpEnd.MinDate = dtpStart.Value;

                mIsScreenByTime = false;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dShowAccount_1");
            }
        }

        private delegate bool ShowDialogInvokeHandler(frmCharge frm);
        private bool ShowDialogInvoke(frmCharge frm)
        {
            bool isResult = false;

            if (frm.InvokeRequired)
            {
                isResult = (bool)frm.Invoke(new ShowDialogInvokeHandler(ShowDialogInvoke), frm);
            }
            else
            {
                isResult = (frm.ShowDialog() == System.Windows.Forms.DialogResult.OK);
            }

            return isResult;
        }

        //1、获取最新50条
        private void LoadChat(DataGridViewRow row, string strRender = "", string strMessageID = "", int intWebPage = 0)
        {
            if (row == null)
                return;

            WechatTalk wTalk = (WechatTalk)row.Tag;
            wTalk.PageIndex = intWebPage;

            ShowPageControl(true);

            this.LoadChat(wTalk, strRender, strMessageID);
        }

        private void LoadChat(WechatTalk wTalk, string strRender = "", string strMessageID = "")
        {
            try
            {
                if (this.mTdGetChat != null && this.mTdGetChat.ThreadState != ThreadState.Stopped)
                    this.mTdGetChat.Abort();
            }
            catch { }

            this.mTdGetChat = new Thread(new ParameterizedThreadStart(LoadChatInThread));
            this.mTdGetChat.IsBackground = true;
            this.mTdGetChat.Start(new Object[] { wTalk, strRender, strMessageID });
        }

        private void LoadChatInThread(object objTalk)
        {
            try
            {
                object[] arrobj = (object[])objTalk;
                WechatTalk wTalk = (WechatTalk)arrobj[0];
                string strRender = arrobj[1].ToString();
                string strMessageID = arrobj[2].ToString();
                //this.mCurrentWechatTalk = wTalk;

                wbsChat.Tag = wTalk;
                this.ShowChatOnWeb(wTalk, strRender, strMessageID);
                this.SetLoadChat(false);
                ShowPageControl(true);
            }
            catch (ThreadAbortException e)
            {
                System.Diagnostics.Debug.Write(e);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "LoadChatInThread");
            }
        }

        private delegate void SetLoadChatHanlder(bool isShow);
        private void SetLoadChat(bool isShow)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new SetLoadChatHanlder(SetLoadChat), new object[] { isShow });
                }
                else
                {
                    if (IniSetting.GetIsTalkSaveDB())
                    {
                        this.lblLoading.Text = "正在加载...（数据库读取方式，速度较慢）";
                        this.pictureBox1.Location = new Point((this.pnlLoadChat.Width - this.pictureBox1.Width - this.lblLoading.Width - 6) / 2, this.pictureBox1.Location.Y);
                        this.lblLoading.Location = new Point(this.pictureBox1.Location.X + this.pictureBox1.Width + 3, this.lblLoading.Location.Y);

                        //Common.Log("正在使用数据库读取方式，速度较慢。");
                    }
                    else
                    {
                        this.lblLoading.Text = "正在加载...";
                        this.pictureBox1.Location = new Point((this.pnlLoadChat.Width - this.pictureBox1.Width - this.lblLoading.Width - 6) / 2, this.pictureBox1.Location.Y);
                        this.lblLoading.Location = new Point(this.pictureBox1.Location.X + this.pictureBox1.Width + 3, this.lblLoading.Location.Y);
                    }

                    this.pnlLoadChat.Visible = isShow;
                    if (isShow)
                        this.pnlLoadChat.BringToFront();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SetLoadChat");
            }
        }

        private string GetChatJsonDetail(List<MergerForwardInfo> lstInfo)
        {
            JsonWriter writer = new JsonWriter();
            try
            {
                WechatAccInfo waInfo = GetAcctInfo();
                if (waInfo == null) return "";

                JsonArray ja = new JsonArray();
                foreach (MergerForwardInfo item in lstInfo)
                {
                    string strType = "0";
                    string strContent = item.Datadesc;
                    string strIconPath = WechatHeplerPC.GetHeadIconPathFromPC(item.Realchatname, waInfo);

                    JsonObject joItem = new JsonObject();
                    joItem.Add("nickname", this.ReplactSpecialChar(item.Sourcename));
                    joItem.Add("icon", this.ReplactSpecialChar(strIconPath));
                    joItem.Add("time", item.Sourcetime);

                    switch (item.MsgType)
                    {
                        case "1":
                            strType = "0";
                            strContent = item.Datadesc;
                            break;
                        case "2":
                            strType = "0";//"2";
                            strContent = "[这是张图片，暂未支持显示]";
                            break;
                        case "3":
                            strType = "0";//"2";
                            strContent = "[这是语音，暂未支持显示]";
                            break;
                        case "4":
                            strType = "0";//"5";
                            strContent = "[这是个视频，暂未支持显示]";
                            break;
                        case "5":
                            strType = "0";
                            if (item.Link.Length > 0)
                                strContent = string.Format("[{0}]({1})", item.Title, item.Link);
                            //strContent = string.Format("<a href=\"{0}\" target=\"_blank\">{1}</a>", item.Link, item.Title);
                            else
                                strContent = string.Format("{0}  {1}", item.Title, item.Datadesc);
                            break;
                        case "6":
                            strType = "0";//"2";
                            strContent = "[这是位置信息，暂未支持显示]";
                            break;
                        case "8":
                            strType = "0";
                            strContent = "[这是个文件，暂未支持显示]";
                            break;
                        case "19":
                            strType = "0";
                            strContent = "[小程序]" + item.Title;
                            break;
                        default:
                            strType = "0";
                            if (item.Title.Length == 0 && item.Datadesc.Length == 0)
                                strContent = "[暂未兼容]" + item.MsgType;
                            else
                                strContent = string.Format("[{0}]({1})", item.Title, item.Datadesc);

                            break;
                    }
                    joItem.Add("type", strType);
                    joItem.Add("content", this.ReplactSpecialChar(strContent));
                    joItem.Add("detailid", item.SrcMsgLocalid);
                    ja.Add(joItem);
                }

                string strStart = lstInfo[0].Sourcetime;
                string strEnd = lstInfo[lstInfo.Count - 1].Sourcetime;
                string strTime = string.Format("{0}~{1}", strStart, strEnd);
                JsonObject dictChat = new JsonObject();
                dictChat.Add("time", strTime);
                dictChat.Add("content", ja);
                dictChat.Write(writer);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetChatJsonDetail");
            }
            return writer.ToString();
        }

        //展示连贯的聊天记录（不展示搜索结果）
        private void ShowChatOnWeb(WechatTalk wTalk, string strRender = "", string strMessageID = "")
        {
            bool isSeach = wTalk.SearchText.Length > 0 ? true : false;
            string strJson = this.GetChatJson(wTalk, isSeach ? DataFilterType.Search : DataFilterType.Normal,
                                              wTalk.SearchText, strRender, strMessageID);

            this.ShowChatOnWebPage(strJson);

            this.SetPageInfo(wTalk);
        }

        private delegate void SetPageInfoHanlder(WechatTalk wTalk);
        private void SetPageInfo(WechatTalk wTalk)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetPageInfoHanlder(SetPageInfo), wTalk);
            }
            else
            {
                try
                {
                    tbpPage.TotalPage = wTalk.PageTotal;
                    tbpPage.CurrentPage = wTalk.PageIndex;

                    if (IniSetting.GetIsTalkSaveDB())
                    {
                        string strName = wTalk.NickName;
                        if (string.IsNullOrWhiteSpace(strName))
                        {
                            strName = Utility.ReplaceWinIllegalName(wTalk.UserName);
                        }
                        else
                        {
                            strName = Utility.ReplaceWinIllegalName(strName);
                        }

                        if (dgvFriend != null)
                        {
                            dgvFriend.Enabled = true;

                            if (dgvFriend.SelectedRows != null && wTalk != null)
                            {
                                string lastChatText = OperateLastChatText(GetViewLastChat(wTalk));
                                dgvFriend.SelectedRows[0].Cells["colName"].Value = string.Format("{0}\r\n{1}", strName, lastChatText);
                            }

                            if (m_exportType != ExportType.None)
                                NewExport(m_exportType);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "SetPageInfo");
                }
            }
        }

        private string GetChatJson(WechatTalk talk, DataFilterType dType = DataFilterType.Normal, string strSearchText = "", string strRender = "", string strMessageID = "")
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return "";

            JsonWriter writer = new JsonWriter();
            try
            {
                JsonArray arrChats = new JsonArray();

                string strFromForWeb = string.Empty;                //0：对方，1：自己
                string strTypeForWeb = string.Empty;                //0：文本，1：语音，2：图片，3：网页，4：系统消息
                string strIconPath = string.Empty;
                string strMD5 = string.Empty;
                string strUsrName = string.Empty;
                string wxAccount = "";
                //消息发送者
                string strNickName = string.Empty;
                string strMsgForWeb = string.Empty;
                string strTimeForWeb = string.Empty;
                string strPetName = string.Empty;
                DateTime startTime = DateTime.MinValue;

                //判断是否为群组
                bool isChatRoom = WechatHeplerPC.CheckIsChatRoom(talk.UserName);
                WebDataType wdType = WebDataType.Normal;
                List<WechatMessageItem> lstMessageItems = GetMessageListByPage(talk, strRender, strMessageID, ref wdType);

                string strNickNameEx = "";
                WechatMessageItem[] aryMsgItems = lstMessageItems.ToArray();

                List<string> lstItems = new List<string>();

                for (int i = aryMsgItems.Length - 1; i >= 0; i--)
                {
                    WechatMessageItem Item = aryMsgItems[i];

                    JsonObject dictChat = this.WechatMessageItemHandle(Item, isChatRoom, talk, waInfo, dType, strSearchText);

                    if (Item.SvrMsg != null)
                    {
                        JsonObject dictQuoteChat = this.WechatMessageItemHandle(Item.SvrMsg, isChatRoom, talk, waInfo, dType, strSearchText);
                        dictChat.Add("quote", dictQuoteChat);
                    }
                    arrChats.Add(dictChat);

                    //WechatMessageItem Item = aryMsgItems[i];

                    //strNickNameEx = "";
                    ////新闻欢迎数据可以不用显示
                    //if (talk.UserName == "newsapp" && Item.MessageType == 1)
                    //    continue;

                    ////1、来源与用户名
                    //strFromForWeb = "0";
                    //strUsrName = "";
                    //strNickName = "";
                    //strMsgForWeb = Item.MessageContent;

                    ////群组的信息里面包括联系人名称
                    //if (WechatHeplerPC.IsReceivedMsg(Item, waInfo))
                    //{
                    //    strFromForWeb = "1";

                    //    if (isChatRoom)
                    //        strNickName = WechatHeplerPC.GetChatRoomNickName(talk.UserName, waInfo.Account, waInfo.LstWeChatGroupFriendInfo);

                    //    if (strNickName.Length == 0)
                    //        strNickName = waInfo.Name;

                    //    strUsrName = Item.SendId;
                    //}
                    //else
                    //{
                    //    strFromForWeb = "0";

                    //    if (strNickName.Length == 0)
                    //        strNickName = talk.NickName;

                    //    //如果是群组，则需要取每一个人的用户名
                    //    if (isChatRoom)
                    //    {
                    //        WechatHeplerPC.AnalyseGroup(waInfo.DicWeChatFriendInfo,
                    //                                    ref strUsrName, ref wxAccount,
                    //                                    ref strNickName, ref strMsgForWeb, ref strPetName,
                    //                                    waInfo.LstWeChatGroupFriendInfo, talk.UserName);
                    //    }
                    //    else
                    //        strUsrName = Item.SendId;
                    //}
                    //if (!string.IsNullOrWhiteSpace(strUsrName))
                    //    strUsrName = WechatHeplerPC.ReplaceImproperCharacter(strUsrName);

                    ////2、昵称与头像
                    //strMD5 = talk.Md5;
                    //strIconPath = WechatHeplerPC.GetHeadIconPathFromPC(strUsrName, waInfo);
                    //if (!string.IsNullOrWhiteSpace(strIconPath))
                    //    strIconPath = ReplactSpecialChar(strIconPath);
                    //else
                    //{
                    //    //群组需要把成员的头像下载下来
                    //    if (!string.IsNullOrEmpty(strUsrName) && !lstItems.Contains(strUsrName))
                    //        lstItems.Add(strUsrName);
                    //}

                    ////3、消息类型与内容
                    //JsonArray arrHtml5ContentForWeb = default(JsonArray);
                    //AnalyseMessageType(dType, strSearchText, ref strTypeForWeb, ref strMsgForWeb, ref arrHtml5ContentForWeb, Item, ref strNickNameEx);

                    //if (strNickNameEx.Length > 0)
                    //{
                    //    bool isEmpty = false;
                    //    if (isChatRoom)
                    //    {
                    //        strNickName = WechatHeplerPC.GetChatRoomNickName(talk.UserName, strNickNameEx, waInfo.LstWeChatGroupFriendInfo);

                    //        if (strNickName.Length == 0)
                    //            isEmpty = true;
                    //    }
                    //    if (isEmpty)
                    //    {
                    //        foreach (WeChatFriendInfo wcfi in waInfo.DicWeChatFriendInfo.Values)
                    //        {
                    //            if (wcfi.StrWXID.Equals(strNickNameEx, StringComparison.InvariantCultureIgnoreCase))
                    //            {
                    //                strNickName = wcfi.StrNickName;
                    //                break;
                    //            }
                    //        }
                    //    }
                    //}

                    //if (strNickName.Length == 0)
                    //    strNickName = strUsrName;

                    //strNickName = ReplactSpecialChar(strNickName);

                    //JsonObject dictChat = new JsonObject();
                    //dictChat.Add("from", strFromForWeb);
                    //dictChat.Add("type", strTypeForWeb);
                    //dictChat.Add("user", strNickName);
                    //dictChat.Add("icon", strIconPath);
                    //Common.LogException(strNickName + ":" + strIconPath);
                    //if (arrHtml5ContentForWeb != null)
                    //    dictChat.Add("content", this.ReplactSpecialChar(arrHtml5ContentForWeb));
                    //else
                    //{
                    //    if (ServerIniSetting.IsMsgHide() && WechatHeplerPC.mAssistInfo.IsPay && waInfo.AState != ActivateState.Normal && strTypeForWeb == "0")
                    //        strMsgForWeb = this.MsgHide(strMsgForWeb);

                    //    dictChat.Add("content", this.ReplactSpecialChar(strMsgForWeb));
                    //}
                    //strTimeForWeb = string.Empty;
                    //if (startTime == DateTime.MinValue || WechatHeplerPC.ConvertWeixinToPcTime(Item.MessageDateTime).Subtract(startTime).TotalMinutes > 5)
                    //{
                    //    startTime = WechatHeplerPC.ConvertWeixinToPcTime(Item.MessageDateTime);
                    //    if (startTime.Year != DateTime.Now.Year)
                    //        strTimeForWeb = startTime.ToString("yy-MM-dd HH:mm:ss");
                    //    else
                    //        strTimeForWeb = startTime.ToString("MM-dd HH:mm:ss");
                    //}
                    ////取得要显示的图片的尺寸
                    //if (strTypeForWeb == "2" || strTypeForWeb == "5")
                    //{
                    //    this.GetPictureSize(strMsgForWeb, dictChat);
                    //}
                    //dictChat.Add("time", strTimeForWeb);
                    //dictChat.Add("friend", talk.UserName);
                    //if (!string.IsNullOrWhiteSpace(Item.MessageId))
                    //    dictChat.Add("id", Item.MessageId);
                    //else
                    //    dictChat.Add("id", "0");

                    //dictChat.Add("delete", 0); //标志是否删除 1：是  0：否  

                    //arrChats.Add(dictChat);
                }

                Thread threadDown = new Thread(new ParameterizedThreadStart(DownHeadIcon));
                threadDown.IsBackground = true;
                threadDown.Start(lstItems);

                //添加语言包
                JsonObject dictLanguage = new JsonObject();
                dictLanguage.Add("weixincopy", "复制");
                dictLanguage.Add("weixinexport", "导出");
                dictLanguage.Add("weixingetmore", "点击获取更多");
                dictLanguage.Add("weixingetcontext", "查看前后消息");

                JsonObject dictChats = new JsonObject();
                //dictChats.Add("type", wdType.GetHashCode().ToString());
                dictChats.Add("type", 0);
                dictChats.Add("top", strRender == "bottom" ? 1 : 0);
                dictChats.Add("data", arrChats);
                dictChats.Add("language", dictLanguage);

                //功能（"weixincopy","weixinexport"）
                JsonArray dicOption = new JsonArray();
                dicOption.Add("weixingetcontext");
                //dicOption.Add("weixincopy");
                dicOption.Add("weixinexport");
                dictChats.Add("msgOption", dicOption);

                dictChats.Write(writer);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetChatJson");
            }
            return writer.ToString();
        }

        public JsonObject WechatMessageItemHandle(WechatMessageItem item, bool isChatRoom, WechatTalk talk, WechatAccInfo waInfo, DataFilterType dType = DataFilterType.Normal, string strSearchText = "")
        {

            string strFromForWeb = string.Empty;                //0：对方，1：自己
            string strTypeForWeb = string.Empty;                //0：文本，1：语音，2：图片，3：网页，4：系统消息
            string strIconPath = string.Empty;
            string strMD5 = string.Empty;
            string strUsrName = string.Empty;
            string wxAccount = "";
            //消息发送者
            string strNickName = string.Empty;
            string strMsgForWeb = string.Empty;
            string strTimeForWeb = string.Empty;
            string strPetName = string.Empty;
            DateTime startTime = DateTime.MinValue;

            WebDataType wdType = WebDataType.Normal;

            string strNickNameEx = "";

            List<string> lstItems = new List<string>();

            WechatMessageItem Item = item;

            strNickNameEx = "";
            //新闻欢迎数据可以不用显示
            if (talk.UserName == "newsapp" && Item.MessageType == 1)
                return null;

            //1、来源与用户名
            strFromForWeb = "0";
            strUsrName = "";
            strNickName = "";
            strMsgForWeb = Item.MessageContent;

            //群组的信息里面包括联系人名称
            if (WechatHeplerPC.IsReceivedMsg(Item, waInfo))
            {
                strFromForWeb = "1";

                if (isChatRoom)
                    strNickName = WechatHeplerPC.GetChatRoomNickName(talk.UserName, waInfo.Account, waInfo.LstWeChatGroupFriendInfo);

                if (strNickName.Length == 0)
                    strNickName = waInfo.Name;

                strUsrName = Item.SendId;
            }
            else
            {
                strFromForWeb = "0";

                if (strNickName.Length == 0)
                    strNickName = talk.NickName;

                //如果是群组，则需要取每一个人的用户名
                if (isChatRoom)
                {
                    WechatHeplerPC.AnalyseGroup(waInfo.DicWeChatFriendInfo,
                                                ref strUsrName, ref wxAccount,
                                                ref strNickName, ref strMsgForWeb, ref strPetName,
                                                waInfo.LstWeChatGroupFriendInfo, talk.UserName);
                }
                else
                    strUsrName = Item.SendId;
            }
            if (!string.IsNullOrWhiteSpace(strUsrName))
                strUsrName = WechatHeplerPC.ReplaceImproperCharacter(strUsrName);

            //2、昵称与头像
            strMD5 = talk.Md5;
            strIconPath = WechatHeplerPC.GetHeadIconPathFromPC(strUsrName, waInfo);
            if (!string.IsNullOrWhiteSpace(strIconPath))
                strIconPath = ReplactSpecialChar(strIconPath);
            else
            {
                //群组需要把成员的头像下载下来
                if (!string.IsNullOrEmpty(strUsrName) && !lstItems.Contains(strUsrName))
                    lstItems.Add(strUsrName);
            }

            //3、消息类型与内容
            JsonArray arrHtml5ContentForWeb = default(JsonArray);
            AnalyseMessageType(dType, strSearchText, ref strTypeForWeb, ref strMsgForWeb, ref arrHtml5ContentForWeb, Item, ref strNickNameEx);

            if (strNickNameEx.Length > 0)
            {
                bool isEmpty = false;
                if (isChatRoom)
                {
                    strNickName = WechatHeplerPC.GetChatRoomNickName(talk.UserName, strNickNameEx, waInfo.LstWeChatGroupFriendInfo);

                    if (strNickName.Length == 0)
                        isEmpty = true;
                }
                if (isEmpty)
                {
                    foreach (WeChatFriendInfo wcfi in waInfo.DicWeChatFriendInfo.Values)
                    {
                        if (wcfi.StrWXID.Equals(strNickNameEx, StringComparison.InvariantCultureIgnoreCase))
                        {
                            strNickName = wcfi.StrNickName;
                            break;
                        }
                    }
                }
            }

            if (strNickName.Length == 0)
                strNickName = strUsrName;

            strNickName = ReplactSpecialChar(strNickName);

            JsonObject dictChat = new JsonObject();
            dictChat.Add("from", strFromForWeb);
            dictChat.Add("type", strTypeForWeb);
            dictChat.Add("user", strNickName);
            dictChat.Add("icon", strIconPath);
            Common.LogException(strNickName + ":" + strIconPath);
            if (arrHtml5ContentForWeb != null)
                dictChat.Add("content", this.ReplactSpecialChar(arrHtml5ContentForWeb));
            else
            {
                if (ServerIniSetting.IsMsgHide() && WechatHeplerPC.mAssistInfo.IsPay && waInfo.AState != ActivateState.Normal && strTypeForWeb == "0")
                    strMsgForWeb = this.MsgHide(strMsgForWeb);

                dictChat.Add("content", this.ReplactSpecialChar(strMsgForWeb));
            }
            strTimeForWeb = string.Empty;
            if (startTime == DateTime.MinValue || WechatHeplerPC.ConvertWeixinToPcTime(Item.MessageDateTime).Subtract(startTime).TotalMinutes > 5)
            {
                startTime = WechatHeplerPC.ConvertWeixinToPcTime(Item.MessageDateTime);
                if (startTime.Year != DateTime.Now.Year)
                    strTimeForWeb = startTime.ToString("yy-MM-dd HH:mm:ss");
                else
                    strTimeForWeb = startTime.ToString("MM-dd HH:mm:ss");
            }
            //取得要显示的图片的尺寸
            if (strTypeForWeb == "2" || strTypeForWeb == "5")
            {
                this.GetPictureSize(strMsgForWeb, dictChat);
            }
            dictChat.Add("time", strTimeForWeb);
            dictChat.Add("friend", talk.UserName);
            if (!string.IsNullOrWhiteSpace(Item.MessageId))
                dictChat.Add("id", Item.MessageId);
            else
                dictChat.Add("id", "0");

            dictChat.Add("delete", 0); //标志是否删除 1：是  0：否  

            return dictChat;

        }

        private void GetPictureSize(string strFilePath, JsonObject dictChat)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(strFilePath) || !File.Exists(strFilePath)) return;

                using (FileStream fs = new FileStream(strFilePath, FileMode.Open, FileAccess.Read))
                {
                    Image image = Image.FromStream(fs);
                    int intWidth = image.Width;
                    int intHeight = image.Height;
                    if (image.Height > 250)
                    {
                        intHeight = 250;
                        intWidth = 250 * image.Width / image.Height;
                    }
                    dictChat.Add("width", intWidth);
                    dictChat.Add("height", intHeight);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MainForm_GetPictureSize");
            }
        }

        private string ReplactSpecialChar(string strValue)
        {
            if (string.IsNullOrEmpty(strValue))
                return strValue;

            string strReturn = strValue.Replace("\\", "/").Replace("\n", "\\\\n").Replace("\r", "\\\\r").Replace("\"", "\\\"").Replace("'", "‘");
            return strReturn;
        }

        private JsonArray ReplactSpecialChar(JsonArray arr)
        {
            if (arr == null || arr.Count <= 0)
                return arr;

            foreach (JsonObject item in arr)
            {
                if (item != null)
                {
                    Dictionary<string, string> dictValue = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
                    foreach (var pair in item)
                    {
                        if (pair.Value != null && pair.Value.GetType() == typeof(JsonString))
                        {
                            dictValue.Add(pair.Key, pair.Value.ToString().Replace("\\", "/").Replace("\n", "\\\\n").Replace("\r", "\\\\r").Replace("\"", "\\\"").Replace("'", "‘"));
                        }
                    }
                    foreach (KeyValuePair<string, string> p in dictValue)
                    {
                        item[p.Key] = new JsonString(p.Value);
                    }
                }
            }
            return arr;
        }

        private List<WechatMessageItem> GetMessageListByPage(WechatTalk talk, string strRender, string strMessageID, ref WebDataType wdType)
        {
            List<WechatMessageItem> lstMessageItems = new List<WechatMessageItem>();

            if (string.IsNullOrEmpty(strMessageID))
            {
                wdType = WebDataType.Normal;
                talk.PageIndex = 1;
            }
            else
            {
                if (strRender.Equals("bottom", StringComparison.InvariantCultureIgnoreCase))
                {
                    wdType = WebDataType.GetNewsBottom;
                    if (talk.PageIndex == 0)
                        return lstMessageItems;
                }
                else
                    wdType = WebDataType.GetNews;
            }

            double intCurrentLoadTotalCount = talk.PageIndex * talk.PageCount;
            double intIndex = 0;

            var query = from oneItem in lstMessageItems select oneItem;

            //List<DateTime> lstDateTime = new List<DateTime>();
            //lstDateTime.Add(dtStart);
            //lstDateTime.Add(dtEnd);

            //从哪一行开始查找数据
            int intStartIndex = Convert.ToInt32((talk.PageIndex - 1) * talk.PageCount);
            if (IniSetting.GetIsTalkSaveDB())
            {
                List<DateTime> lstDateTime = FilterDateTime();
                WechatAccInfo waInfo = GetAcctInfo();

                WeChatTalkDB db = WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath);

                if (waInfo != null && db != null)
                {
                    int nCount = db.GetTalkInfoCount(talk.Md5, lstDateTime, talk.SearchText);
                    if (nCount == 0)
                    {
                        lstMessageItems = WechatTalkNew.GetWechatMessages(talk, waInfo, lstDateTime, intStartIndex);
                    }
                    else
                    {

                        talk.PageTotal = Math.Ceiling(Convert.ToDouble(nCount / talk.PageCount));
                        lstMessageItems = db.GetTalkInfo(talk.Md5, talk.NickName, lstDateTime, talk.SearchText, intStartIndex, (int)talk.PageCount);
                    }
                }
            }
            else
            {
                long longStart = WechatHeplerPC.ConvertPcTimeToWeixin(GetStartTime());
                long longEnd = WechatHeplerPC.ConvertPcTimeToWeixin(GetEndTime());

                List<WechatMessageItem> lstWMI = new List<WechatMessageItem>();
                string searchText = txtSearchChat.Text.Trim().ToLowerInvariant();
                //从内存中分页查询聊天记录
                if (string.IsNullOrEmpty(talk.SearchText))
                {
                    if (wdType == WebDataType.GetNews && talk.PageIndex > talk.PageTotal)
                        return lstMessageItems;

                    query = from oneItem in talk.MessageItems select oneItem;

                    if (this.mIsScreenByTime || m_IsLockDate)
                    {
                        lstWMI = talk.MessageItems.FindAll(delegate (WechatMessageItem wmItem)
                        {
                            return wmItem.MessageContent.ToLowerInvariant().Contains(searchText) &&
                                wmItem.MessageDateTime > longStart && wmItem.MessageDateTime < longEnd;
                        });

                        query = from oneItem in lstWMI select oneItem;
                    }
                }
                else
                {
                    if (this.pnlTitle.Visible)
                        lstWMI = talk.MessageItems.FindAll(delegate (WechatMessageItem wmItem)
                        {
                            return wmItem.MessageContent.ToLowerInvariant().Contains(searchText) &&
                                wmItem.MessageDateTime > longStart && wmItem.MessageDateTime < longEnd;
                        });
                    else
                        lstWMI = talk.MessageItems.FindAll(delegate (WechatMessageItem wmItem)
                        {
                            return wmItem.MessageContent.ToLowerInvariant().Contains(searchText);
                        });

                    query = from oneItem in lstWMI select oneItem;
                }

                List<WechatMessageItem> list = query.Skip(intStartIndex).Take((int)talk.PageCount).ToList();
                foreach (WechatMessageItem Item in list)
                {
                    lstMessageItems.Add(Item);
                }
                if (lstWMI.Count > 0)
                    talk.PageTotal = Math.Ceiling(Convert.ToDouble(lstWMI.Count / talk.PageCount));
            }

            return lstMessageItems;
        }


        private string MsgHide(string strMsgForWeb)
        {
            string strRelust = "";
            try
            {
                if (strMsgForWeb.Contains("^./expression"))
                    strMsgForWeb = strMsgForWeb.Substring(0, strMsgForWeb.IndexOf("^./expression"));

                double dTLength = (double)strMsgForWeb.Length;
                double dSLength = dTLength / 2;
                dSLength = Math.Floor(dSLength);

                double dCurrentIndex = 1;


                foreach (Char item in strMsgForWeb)
                {
                    if (dCurrentIndex <= dSLength)
                        strRelust = strRelust + item;
                    else
                        strRelust = strRelust + "*";

                    dCurrentIndex++;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MsgHide");
            }
            if (strRelust.Length == 0)
                strRelust = "*";

            return strRelust;
        }

        private void AnalyseMessageType(DataFilterType dType, string strSearchText, ref string strTypeForWeb, ref string strMsgForWeb,
            ref JsonArray arrHtml5ContentForWeb, WechatMessageItem item, ref string strNickNameEx)
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return;

            strTypeForWeb = "0";
            arrHtml5ContentForWeb = null;

            //微信： 1文本；3图片；34语音；42名片；43视频；47特殊表情；48地理位置；49网页；50视频未接通的消息；1000系统消息
            //web页 0文本，1语音，2图片，3网页
            int intMapKey = 0;
            switch (item.MessageType)
            {
                case 1:
                    // 处理文本
                    item.SociaChatType = SociaChatType.Text;

                    strTypeForWeb = "0";
                    //表情符号转换成表情图片
                    if (dType != DataFilterType.OnlyListTitle)
                        strMsgForWeb = this.OperateForExpression(strMsgForWeb);

                    //搜索加高亮效果（搜索只搜索文本类型），数据库搜索不区分大小写
                    if (dType == DataFilterType.Search)  //  && blnHighlight
                    {
                        if (!string.IsNullOrEmpty(strSearchText) && strMsgForWeb.Contains(strSearchText))
                        {
                            strMsgForWeb = strMsgForWeb.Replace(strSearchText, "~" + strSearchText + "~(background-color:#ffff00;color:#fffff;)");
                        }
                    }
                    break;

                case 3:
                    // 处理图片
                    item.SociaChatType = SociaChatType.Picture;
                    strTypeForWeb = "2";
                    //strMsgForWeb = this.GetChatImagePathFromPC(Item, true);     
                    strMsgForWeb = mWechatHeplerPC.GetMediaThumbFilePath(item, this.mCurrentBackupDBPath, waInfo.Password,
                        this.mCurrentBackupMediaFile, waInfo.PwdBuffer, this.mWeChatiOSDevice, out intMapKey);

                    break;

                case 34:
                    // 处理语音
                    item.SociaChatType = SociaChatType.Voice;
                    strTypeForWeb = "1";
                    mWechatHeplerPC.GetMediaThumbFilePath(item, this.mCurrentBackupDBPath, waInfo.Password,
                        this.mCurrentBackupMediaFile, waInfo.PwdBuffer, this.mWeChatiOSDevice, out intMapKey);

                    strMsgForWeb = WechatHeplerPC.GetChatVoiceLength(item.MessageContent, true).ToString();
                    break;

                case 62:
                    //  ?? 还不知道是啥？
                    strTypeForWeb = "2";
                    //strMsgForWeb = this.GetChatVideoPathFromPC(Item, true);       
                    strMsgForWeb = mWechatHeplerPC.GetMediaFilePath(item, this.mCurrentBackupDBPath, waInfo.Password,
                        this.mCurrentBackupMediaFile, waInfo.PwdBuffer, true, this.mWeChatiOSDevice, out intMapKey);

                    break;

                case 42:
                    // 处理名片
                    item.SociaChatType = SociaChatType.NameCard;
                    strMsgForWeb = "【名片】";
                    break;

                case 43:
                    // 处理视频
                    item.SociaChatType = SociaChatType.Video;
                    strMsgForWeb = "【视频】";
                    strTypeForWeb = "5";
                    strMsgForWeb = mWechatHeplerPC.GetMediaFilePath(item, this.mCurrentBackupDBPath, waInfo.Password, this.mCurrentBackupMediaFile,
                                                                    waInfo.PwdBuffer, true, this.mWeChatiOSDevice, out intMapKey);

                    //strMsgForWeb = this.GetChatVideoPathFromPC(Item, true);
                    break;

                case 47:
                    // 处理特殊表情
                    item.SociaChatType = SociaChatType.SpecialExpression;
                    strMsgForWeb = "【动画表情】";
                    break;

                case 48:
                    // 处理地理位置
                    strMsgForWeb = "【地理位置】";
                    item.SociaChatType = SociaChatType.Location;
                    break;

                case 49:
                    if (!Common.OldAnalysisWebpage())
                    {
                        string strExport = "";
                        IMHtmlNodeType imtype = IMHtmlNodeType.IMHtmlNode_Media;

                        bool isLinkA = false;
                        mWeChatMsgWebPage.AnalysisNewWebpage(item, ref strMsgForWeb, ref arrHtml5ContentForWeb, ref strTypeForWeb, ref strExport, ref imtype, ref isLinkA);

                        strMsgForWeb = this.OperateForExpression(strMsgForWeb);
                        //搜索加高亮效果（搜索只搜索文本类型），数据库搜索不区分大小写
                        if (dType == DataFilterType.Search)
                        {
                            if (!string.IsNullOrEmpty(strSearchText) && strMsgForWeb.Contains(strSearchText))
                            {
                                strMsgForWeb = strMsgForWeb.Replace(strSearchText, "~" + strSearchText + "~(background-color:#ffff00;color:#fffff;)");
                            }
                        }
                    }
                    else
                    {
                        // 处理网页
                        item.SociaChatType = SociaChatType.webpage;
                        strTypeForWeb = "3";
                        arrHtml5ContentForWeb = ExportHelper.GetWebPageJsonArray(strMsgForWeb);
                        //网页json
                        try
                        {
                            //兼容链接（只有title和url）
                            if (arrHtml5ContentForWeb == null || arrHtml5ContentForWeb.Count == 0)
                            {
                                JsonArray webLinkJsonArray = ExportHelper.GetWebLinkJsonArray(strMsgForWeb);

                                if (webLinkJsonArray != null && webLinkJsonArray.Count > 0)
                                {
                                    JsonObject dicWebPage = (JsonObject)webLinkJsonArray[0];
                                    string strTitle = ((JsonString)dicWebPage["title"]).Value;
                                    string strUrl = ((JsonString)dicWebPage["url"]).Value;
                                    strNickNameEx = ((JsonString)dicWebPage["fromusername"]).Value;

                                    if (!string.IsNullOrEmpty(strTitle) && !string.IsNullOrEmpty(strUrl))
                                    {
                                        strTypeForWeb = "0";
                                        arrHtml5ContentForWeb = null;
                                        strMsgForWeb = string.Format("【{1}】({0})", strUrl, strTitle);
                                    }
                                    //else if (strTitle.EndsWith (".xlsx"))
                                    //{
                                    //    item.SociaChatType = SociaChatType.File ;
                                    //    strTypeForWeb = "2";                                   
                                    //    strMsgForWeb = mWechatHeplerPC.GetMediaThumbFilePath(item, mCurrentBackupDBPath, mCurrentBackupDBPassword, mCurrentWAInfo.PwdBuffer);

                                    //}
                                    else
                                    {
                                        arrHtml5ContentForWeb = null;
                                        strTypeForWeb = "0";
                                        strMsgForWeb = "【动画表情】";
                                    }
                                }
                                else
                                {
                                    string strTemp = ExportHelper.GetWebTagFromReg(strMsgForWeb, "<title>(?<num>.*?)</title>", "num");

                                    if (strTemp.Length > 0)
                                    {

                                        if (strTemp.Contains("<![CDATA["))
                                            strTemp = strTemp.Replace("<![CDATA[", "");

                                        if (strTemp.Contains("]]>"))
                                            strTemp = strTemp.Replace("]]>", "");

                                        strTypeForWeb = "0";
                                        arrHtml5ContentForWeb = null;
                                        strMsgForWeb = strTemp;
                                    }

                                }
                                //搜索加高亮效果（搜索只搜索文本类型），数据库搜索不区分大小写
                                if (dType == DataFilterType.Search)
                                {
                                    if (!string.IsNullOrEmpty(strSearchText) && strMsgForWeb.Contains(strSearchText))
                                    {
                                        strMsgForWeb = strMsgForWeb.Replace(strSearchText, "~" + strSearchText + "~(background-color:#ffff00;color:#fffff;)");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Common.LogException(ex.ToString(), "GetWebLinkJsonArray2");
                        }
                    }
                    break;

                case 50:
                    // 处理视频未接通
                    item.SociaChatType = SociaChatType.VideoConnected;
                    strMsgForWeb = "[视频未接通]";
                    break;

                case 10000:
                    // 处理系统消息
                    item.SociaChatType = SociaChatType.SystemMessages;
                    strTypeForWeb = "4";
                    break;
                case 10002: // 邀请系统消息
                    item.SociaChatType = SociaChatType.SystemMessages;
                    strMsgForWeb = mWechatHeplerPC.ParseSysmsg(strMsgForWeb);
                    strTypeForWeb = "4";
                    break;
                default:
                    strMsgForWeb = item.MessageContent;
                    break;
            }
        }

        private void ShowChatOnWebPage(string strJson)
        {
            ChatWebPage.ShowChatOnWebPage(this.wbsChat, strJson);
            ChatWebPage.ShowMoreButton(this.wbsChat, false);
            ChatWebPage.ClearChatOnWebPage(this.wbsChatDetail);
            ShowWebDetail(false);
            this.ShowSystemInfoOnPC(false, "ShowChatOnWebPage to Log.");
        }

        private void ShowChatDetailOnWebPage(string strJson)
        {
            ChatWebPage.ShowChatOnWebPage(this.wbsChatDetail, strJson);
            ChatWebPage.ShowMoreButton(this.wbsChatDetail, false);
            ShowWebDetail(true);
        }

        //提取表情的正则方法
        private string OperateForExpression(string strContent)
        {
            string regular = "\\[(?<expression>.*?)\\]";
            DataTable dt = Utility.GetMatchStringByRegularExpressions(strContent, new string[1] { regular }, new string[1] { "expression" });

            if (dt != null && dt.Rows.Count > 0)
            {
                foreach (DataRow dr in dt.Rows)
                {
                    string strKey = this.FormatStringForWebPage(HttpUtility.UrlDecode(dr["expression"].ToString()));
                    strKey = "[" + strKey + "]";

                    string strExpressionPath = ExportHelper.GetExpressionPath(strKey);
                    //this.GetExpressionPath(strKey);

                    if (!string.IsNullOrEmpty(strExpressionPath))
                    {
                        strContent = strContent.Replace(strKey, strExpressionPath);
                    }
                }
            }

            return strContent;
        }

        private string FormatStringForWebPage(string strContent)
        {
            string value = strContent;

            if (!string.IsNullOrEmpty(value))
            {

                if (value.StartsWith("<![CDATA[") && value.EndsWith("]]>"))
                {
                    value = value.TrimStart(new char[] {
                '<',
                '!',
                '[',
                'C',
                'D',
                'A',
                'T',
                'A',
                '['
            });
                    value = value.TrimEnd(new char[] {
                ']',
                ']',
                '>'
            });

                }
            }

            return value;
        }

        //获取表情路径（相对路径）
        //private string GetExpressionPath(string strKey)
        //{
        //    string strName = string.Empty;

        //    switch (strKey)
        //    {
        //        case "[微笑]":
        //            strName = "Expression_1";
        //            break;
        //        case "[撇嘴]":
        //            strName = "Expression_2";
        //            break;
        //        case "[色]":
        //            strName = "Expression_3";
        //            break;
        //        case "[发呆]":
        //        case "[發呆]":
        //            strName = "Expression_4";
        //            break;
        //        case "[得意]":
        //            strName = "Expression_5";
        //            break;
        //        case "[流泪]":
        //        case "[流淚]":
        //            strName = "Expression_6";
        //            break;
        //        case "[害羞]":
        //            strName = "Expression_7";
        //            break;
        //        case "[闭嘴]":
        //        case "[閉嘴]":
        //            strName = "Expression_8";
        //            break;
        //        case "[睡]":
        //            strName = "Expression_9";
        //            break;
        //        case "[大哭]":
        //            strName = "Expression_10";
        //            break;
        //        case "[尴尬]":
        //        case "[尷尬]":
        //            strName = "Expression_11";
        //            break;
        //        case "[发怒]":
        //        case "[發怒]":
        //            strName = "Expression_12";
        //            break;
        //        case "[调皮]":
        //        case "[調皮]":
        //            strName = "Expression_13";
        //            break;
        //        case "[呲牙]":
        //            strName = "Expression_14";
        //            break;
        //        case "[惊讶]":
        //        case "[驚訝]":
        //            strName = "Expression_15";
        //            break;
        //        case "[难过]":
        //        case "[難過]":
        //            strName = "Expression_16";
        //            break;
        //        case "[酷]":
        //            strName = "Expression_17";
        //            break;
        //        case "[冷汗]":
        //            strName = "Expression_18";
        //            break;
        //        case "[抓狂]":
        //            strName = "Expression_19";
        //            break;
        //        case "[吐]":
        //            strName = "Expression_20";
        //            break;
        //        case "[偷笑]":
        //            strName = "Expression_21";
        //            break;
        //        case "[愉快]":
        //            strName = "Expression_22";
        //            break;
        //        case "[白眼]":
        //            strName = "Expression_23";
        //            break;
        //        case "[傲慢]":
        //            strName = "Expression_24";
        //            break;
        //        case "[饥饿]":
        //        case "[饑餓]":
        //            strName = "Expression_25";
        //            break;
        //        case "[困]":
        //            strName = "Expression_26";
        //            break;
        //        case "[惊恐]":
        //        case "[驚恐]":
        //            strName = "Expression_27";
        //            break;
        //        case "[流汗]":
        //            strName = "Expression_28";
        //            break;
        //        case "[憨笑]":
        //            strName = "Expression_29";
        //            break;
        //        case "[悠闲]":
        //        case "[悠閒]":
        //            strName = "Expression_30";
        //            break;
        //        case "[奋斗]":
        //        case "[奮鬥]":
        //            strName = "Expression_31";
        //            break;
        //        case "[咒骂]":
        //        case "[咒罵]":
        //            strName = "Expression_32";
        //            break;
        //        case "[疑问]":
        //            strName = "Expression_33";
        //            break;
        //        case "[嘘]":
        //            strName = "Expression_34";
        //            break;
        //        case "[晕]":
        //        case "[暈]":
        //            strName = "Expression_35";
        //            break;
        //        case "[疯了]":
        //        case "[瘋了]":
        //            strName = "Expression_36";
        //            break;
        //        case "[衰]":
        //            strName = "Expression_37";
        //            break;
        //        case "[骷髅]":
        //        case "[骷髏]":
        //            strName = "Expression_38";
        //            break;
        //        case "[敲打]":
        //            strName = "Expression_39";
        //            break;
        //        case "[再见]":
        //            strName = "Expression_40";
        //            break;
        //        case "[擦汗]":
        //            strName = "Expression_41";
        //            break;
        //        case "[抠鼻]":
        //        case "[摳鼻]":
        //            strName = "Expression_42";
        //            break;
        //        case "[鼓掌]":
        //            strName = "Expression_43";
        //            break;
        //        case "[糗大了]":
        //            strName = "Expression_44";
        //            break;
        //        case "[坏笑]":
        //        case "[壞笑]":
        //            strName = "Expression_45";
        //            break;
        //        case "[左哼哼]":
        //            strName = "Expression_46";
        //            break;
        //        case "[右哼哼]":
        //            strName = "Expression_47";
        //            break;
        //        case "[哈欠]":
        //            strName = "Expression_48";
        //            break;
        //        case "[鄙视]":
        //        case "[鄙視]":
        //            strName = "Expression_49";
        //            break;
        //        case "[委屈]":
        //            strName = "Expression_50";
        //            break;
        //        case "[快哭了]":
        //            strName = "Expression_51";
        //            break;
        //        case "[阴险]":
        //        case "[陰險]":
        //            strName = "Expression_52";
        //            break;
        //        case "[亲亲]":
        //        case "[親親]":
        //            strName = "Expression_53";
        //            break;
        //        case "[吓]":
        //        case "[嚇]":
        //            strName = "Expression_54";
        //            break;
        //        case "[可怜]":
        //        case "[可憐]":
        //            strName = "Expression_55";
        //            break;
        //        case "[菜刀]":
        //            strName = "Expression_56";
        //            break;
        //        case "[西瓜]":
        //            strName = "Expression_57";
        //            break;
        //        case "[啤酒]":
        //            strName = "Expression_58";
        //            break;
        //        case "[篮球]":
        //        case "[籃球]":
        //            strName = "Expression_59";
        //            break;
        //        case "[乒乓]":
        //            strName = "Expression_60";
        //            break;
        //        case "[咖啡]":
        //            strName = "Expression_61";
        //            break;
        //        case "[饭]":
        //        case "[飯]":
        //            strName = "Expression_62";
        //            break;
        //        case "[猪头]":
        //        case "[豬頭]":
        //            strName = "Expression_63";
        //            break;
        //        case "[玫瑰]":
        //            strName = "Expression_64";
        //            break;
        //        case "[凋谢]":
        //            strName = "Expression_65";
        //            break;
        //        case "[嘴唇]":
        //            strName = "Expression_66";
        //            break;
        //        case "[爱心]":
        //        case "[愛心]":
        //            strName = "Expression_67";
        //            break;
        //        case "[心碎]":
        //            strName = "Expression_68";
        //            break;
        //        case "[蛋糕]":
        //            strName = "Expression_69";
        //            break;
        //        case "[闪电]":
        //        case "[閃電]":
        //            strName = "Expression_70";
        //            break;
        //        case "[炸弹]":
        //        case "[炸彈]":
        //            strName = "Expression_71";
        //            break;
        //        case "[刀]":
        //            strName = "Expression_72";
        //            break;
        //        case "[足球]":
        //            strName = "Expression_73";
        //            break;
        //        case "[瓢虫]":
        //        case "[瓢蟲]":
        //            strName = "Expression_74";
        //            break;
        //        case "[便便]":
        //            strName = "Expression_75";
        //            break;
        //        case "[月亮]":
        //            strName = "Expression_76";
        //            break;
        //        case "[太阳]":
        //        case "[太陽]":
        //            strName = "Expression_77";
        //            break;
        //        case "[礼物]":
        //        case "[禮物]":
        //            strName = "Expression_78";
        //            break;
        //        case "[拥抱]":
        //        case "[擁抱]":
        //            strName = "Expression_79";
        //            break;
        //        case "[强]":
        //            strName = "Expression_80";
        //            break;
        //        case "[弱]":
        //            strName = "Expression_81";
        //            break;
        //        case "[握手]":
        //            strName = "Expression_82";
        //            break;
        //        case "[胜利]":
        //        case "[勝利]":
        //            strName = "Expression_83";
        //            break;
        //        case "[抱拳]":
        //            strName = "Expression_84";
        //            break;
        //        case "[勾引]":
        //            strName = "Expression_85";
        //            break;
        //        case "[拳头]":
        //        case "[拳頭]":
        //            strName = "Expression_86";
        //            break;
        //        case "[差劲]":
        //        case "[差勁]":
        //            strName = "Expression_87";
        //            break;
        //        case "[爱你]":
        //        case "[愛你]":
        //            strName = "Expression_88";
        //            break;
        //        case "[NO]":
        //            strName = "Expression_89";
        //            break;
        //        case "[OK]":
        //            strName = "Expression_90";
        //            break;
        //        case "[爱情]":
        //        case "[愛情]":
        //            strName = "Expression_91";
        //            break;
        //        case "[飞吻]":
        //        case "[飛吻]":
        //            strName = "Expression_92";
        //            break;
        //        case "[跳跳]":
        //            strName = "Expression_93";
        //            break;
        //        case "[发抖]":
        //        case "[發抖]":
        //            strName = "Expression_94";
        //            break;
        //        case "[怄火]":
        //        case "[慪火]":
        //            strName = "Expression_95";
        //            break;
        //        case "[转圈]":
        //        case "[轉圈]":
        //            strName = "Expression_96";
        //            break;
        //        case "[磕头]":
        //        case "[磕頭]":
        //            strName = "Expression_97";
        //            break;
        //        case "[回头]":
        //        case "[回頭]":
        //            strName = "Expression_98";
        //            break;
        //        case "[跳绳]":
        //        case "[跳繩]":
        //            strName = "Expression_99";
        //            break;
        //        case "[挥手]":
        //        case "[揮手]":
        //            strName = "Expression_100";
        //            break;
        //        case "[激动]":
        //        case "[激動]":
        //            strName = "Expression_101";
        //            break;
        //        case "[街舞]":
        //            strName = "Expression_102";
        //            break;
        //        case "[献吻]":
        //        case "[獻吻]":
        //            strName = "Expression_103";
        //            break;
        //        case "[左太极]":
        //        case "[左太極]":
        //            strName = "Expression_104";
        //            break;
        //        case "[右太极]":
        //        case "[右太極]":
        //            strName = "Expression_105";
        //            break;
        //    }

        //    string result = string.Empty;
        //    if (!string.IsNullOrEmpty(strName))
        //    {
        //        //result = String.Format("<img src=""./expression/{0}.png"" width=""21px"" height=""21px""/>", strName)
        //        result = string.Format("^./expression/{0}.png^(21,21)", strName);
        //    }
        //    //else
        //    //{                
        //    //    Common.Log(string.Format("{0} emoji does not exit! in GetExpressionPath() of MainForm", strKey));
        //    //}

        //    return result;
        //}

        //private JsonArray GetWebLinkJsonArray(string strContent)
        //{
        //    //string regular = "<title>(?<title>[\\w\\W]*?)</title>[\\w\\W]*?<url>(?<url>[\\w\\W]*?)</url>";
        //    string regular = "<title>(?<title>[\\w\\W]*?)</title>[\\w\\W]*?<url>(?<url>[\\w\\W]*?)</url>[\\w\\W]*?<fromusername>(?<fromusername>[\\w\\W]*?)</fromusername>";

        //    return this.GetWebPageJsonArray(strContent, regular);
        //}

        //private JsonArray GetWebPageJsonArray(string strContent)
        //{
        //    //.*?代表任意字符（不包括换行及特殊字符）
        //    //[\w\W]*?代表任意字符（包括换行及特殊字符）
        //    //Dim regular As String = "<title>(?<title>.*?)</title>[\w\W]*?<url>(?<url>.*?)</url>[\w\W]*?<pub_time>(?<pub_time>.*?)</pub_time>[\w\W]*?<cover>(?<cover>.*?)</cover>[\w\W]*?<digest>(?<digest>.*?)</digest>"
        //    string regular = "<title>(?<title>[\\w\\W]*?)</title>[\\w\\W]*?<url>(?<url>[\\w\\W]*?)</url>[\\w\\W]*?<pub_time>(?<pub_time>[\\w\\W]*?)</pub_time>[\\w\\W]*?<cover>(?<cover>[\\w\\W]*?)</cover>[\\w\\W]*?<digest>(?<digest>[\\w\\W]*?)</digest>";

        //    ////如果服务器有设置值则使用服务上的值
        //    //if (!string.IsNullOrEmpty(this.mRegular))
        //    //{
        //    //    regular = this.mRegular;
        //    //}

        //    return this.GetWebPageJsonArray(strContent, regular);
        //}

        ////提取网页内容的正则方法（需优化兼容更多网页）
        //private JsonArray GetWebPageJsonArray(string strContent, string regular)
        //{
        //    JsonArray arrWebPages = new JsonArray();
        //    JsonObject dicWebPage = default(JsonObject);

        //    DataTable dt = Utility.GetMatchStringByRegularExpressions(strContent, new string[1] { regular }, new string[7] { "title", "url", "pub_time", "cover", "digest", "type", "fromusername" });

        //    if (dt != null && dt.Rows.Count > 0)
        //    {
        //        foreach (DataRow dr in dt.Rows)
        //        {
        //            try
        //            {
        //                dicWebPage = new JsonObject();
        //                //标题
        //                string strTitle = this.FormatStringForWebPage(HttpUtility.UrlDecode(dr["title"].ToString()));
        //                dicWebPage.Add("title", strTitle);

        //                //网页url
        //                string strUrl = this.FormatStringForWebPage(HttpUtility.UrlDecode(dr["url"].ToString()));
        //                dicWebPage.Add("url", strUrl);

        //                string strfromusername = this.FormatStringForWebPage(HttpUtility.UrlDecode(dr["fromusername"].ToString()));
        //                dicWebPage.Add("fromusername", strfromusername);

        //                //发布时间
        //                string strPubTime = this.FormatStringForWebPage(HttpUtility.UrlDecode(dr["pub_time"].ToString()));
        //                DateTime PubTime = DateTime.MinValue;

        //                try
        //                {
        //                    if (!string.IsNullOrEmpty(strPubTime))
        //                    {
        //                        long longPubTime = long.Parse(strPubTime);
        //                        //PubTime = WeixinHelper.ConvertWeixinToPcTime(longPubTime);
        //                    }
        //                }
        //                catch { }

        //                if (PubTime == DateTime.MinValue)
        //                {
        //                    strPubTime = string.Empty;
        //                }
        //                else
        //                {
        //                    strPubTime = PubTime.ToString("MM-dd");
        //                }

        //                dicWebPage.Add("pub_time", strPubTime);

        //                //封面地址
        //                string strCover = this.FormatStringForWebPage(HttpUtility.UrlDecode(dr["cover"].ToString()));
        //                dicWebPage.Add("cover", strCover);

        //                //摘要
        //                string strDigest = this.FormatStringForWebPage(HttpUtility.UrlDecode(dr["digest"].ToString()));
        //                dicWebPage.Add("digest", strDigest);

        //                arrWebPages.Add(dicWebPage);
        //            }
        //            catch (Exception ex)
        //            {
        //                Common.LogException(ex.ToString(), "GetWebPageJsonArray");
        //            }
        //        }
        //    }
        //    else
        //    {
        //        //Common.LogException(strContent, "无法兼容的网页")
        //    }

        //    return arrWebPages;
        //}

        #endregion

        #region "--- 错误处理 ---"

        //************************************************************
        //**
        //** 名    称：OnThreadException
        //** 功能描述：处理主线程中未处理的异常
        //** 备    注：当主线程出现异常，并且没有try Catch，会捕获到异常，程序不会崩溃。
        //** 参    数：ByVal sender As Object
        //**           ByVal e As Threading.ThreadExceptionEventArgs
        //** 返 回 值：
        //** 全局变量：
        //** 调用模块：
        //** 版本历史：
        //**
        //************************************************************
        private void OnThreadException(object sender, ThreadExceptionEventArgs e)
        {
            string strException = e.Exception.ToString();
            Common.LogException(strException, "OnThreadException");
        }

        //************************************************************
        //**
        //** 名    称：UnhandledException
        //** 功能描述：处理非主线程中的未处理的异常
        //** 备    注：当非主线程出现异常，并且没有try Catch，会捕获到异常，程序直接崩溃。
        //** 参    数：ByVal sender As Object
        //**           ByVal e As System.UnhandledExceptionEventArgs
        //** 返 回 值：
        //** 全局变量：
        //** 调用模块：
        //** 版本历史：
        //**
        //************************************************************
        private void UnhandledException(object sender, System.UnhandledExceptionEventArgs e)
        {
            //Common.LogException(e.ExceptionObject.ToString(), "UnhandledException")

            string strException = e.ExceptionObject.ToString();
            Common.LogException(strException, "UnhandledException");

        }

        #endregion

        #region "--- 收集数据 ---"

        private bool mSendingData = false;
        private bool mCancelSendData = false;
        //当前收集的软件信息在文件中保存的id
        private string _UserIDInSoftInfo = "-1";
        private tbDeviceInfo _infoDevice = new tbDeviceInfo();
        private void StartToSendData()
        {
            try
            {
                //Common.Log("1.StartToSendData");
                ThreadPool.QueueUserWorkItem(new WaitCallback(DoSendData));
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "StartToSendData");
            }

        }

        private void DoSendData(object obj)
        {
            if (this.mSendingData)
            {
                return;
            }
            this.mSendingData = true;

            try
            {
                //Common.Log("1. 发送之前收集的软件信息");
                //1. 发送之前收集的软件信息
                this.SendSoftDataPrevious();

                //Common.Log("2. 收集当前的软件信息");
                //2. 收集当前的软件信息
                tbVersionInfo info = this.CollectSoftData();

                //Common.Log("3发送目前收集的软件信息");
                //3 发送目前收集的软件信息
                this.SendSoftDataNow(info);

                //Common.Log("3. 发送收集到的设备信息");
                //3. 发送收集到的设备信息
                //this.Start2SendDeviceData();

                //4. 发送已经安装的软件信息
                //tbDeviceCache.GetInstanse().SendInstalledSoft()

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoSendData_Main");
            }

            this.mSendingData = false;
            //'收集framwork版本号
            //ActionCollectHelper.FramworkVersion()
        }

        private tbVersionInfo CollectSoftData()
        {
            tbVersionInfo info = new tbVersionInfo();

            try
            {
                tbVersionInfoCache tbVersionCache = tbVersionInfoCache.GetInstanse();
                info.Key = Guid.NewGuid().ToString("N");
                //保存当前软件信息在缓存文件中的key
                this._UserIDInSoftInfo = info.Key;
                info.mac = Common.GetMacAddress();
                info.Version = Common.GetSoftVersion();
                info.osType = this.GetOsTypeRingtone();
                //Common.Log("ostype: " + info.osType);
                //  info.uid = PluginLogin.Instance.Uid;

                info.startTime = DateTime.Now;
                int Soft91Status = info.Soft91Status;
                int.TryParse(UtilityEx.GetSoftInstallStatus().ToString(), out Soft91Status);
                info.Soft91Status = Soft91Status;

                //Common.Log("State: " + Convert.ToInt32(info.Soft91Status).ToString());
                int runTimes = info.runTimes;
                string FunctionMapping = info.FunctionMapping;
                tbVersionCache.GetLastSoftUseState(ref runTimes, ref FunctionMapping);
                info.runTimes = runTimes;
                info.FunctionMapping = FunctionMapping;

                //tbVersionCache.AddCacheData(info)
                //tbVersionCache.Save()
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CollectSoftData");
            }

            return info;
        }

        private string GetOsTypeRingtone()
        {
            string OsTypeResult = "";
            try
            {
                string osBit = "未知";
                if (Folder.AppType == RunType.AirDroid)
                {
                    osBit = "unknow";
                }

                if (Common.IsOS_Of_64Bit())
                {
                    osBit = "64位";
                    if (Folder.AppType == RunType.AirDroid)
                    {
                        osBit = "64 bit";
                    }
                }
                else
                {
                    osBit = "32位";
                    if (Folder.AppType == RunType.AirDroid)
                    {
                        osBit = "32 bit";
                    }
                }

                string osType = "";
                try
                {
                    Microsoft.Win32.RegistryKey rk = Microsoft.Win32.Registry.LocalMachine.OpenSubKey("Software\\Microsoft\\Windows NT\\CurrentVersion");

                    object pname = rk.GetValue("ProductName");
                    if (pname != null)
                    {
                        osType = pname.ToString();
                    }
                }
                catch
                {
                }

                //获取总物理内存大小  
                long capacity = 0;
                using (ManagementClass cimobject1 = new ManagementClass("Win32_PhysicalMemory"))
                {
                    using (ManagementObjectCollection moc1 = cimobject1.GetInstances())
                    { 
                        foreach (ManagementObject mo1 in moc1)
                        {
                            capacity += long.Parse(mo1.Properties["Capacity"].Value.ToString());
                            mo1.Dispose();
                        }
                    }
                }

                OsTypeResult = (Convert.ToString(osBit + Convert.ToString("|")) + osType) + "|" + Utility.FormatFileSize(capacity);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetOsTypeRingtone");
            }
            return OsTypeResult;
        }

        private void SendSoftDataPrevious()
        {
            try
            {
                int n = 0;
                if (!NativeMethods.InternetGetConnectedState(out n, 0))
                {
                    return;
                }

                tbVersionInfoCache tbVersionCache = tbVersionInfoCache.GetInstanse();

                List<tbVersionInfo> lstVersionInfo = tbVersionCache.Datas;

                if (lstVersionInfo != null && lstVersionInfo.Count > 0)
                {
                    //发送软件数据
                    List<string> lstKeys = new List<string>();
                    DateTime uploadTime = tbVersionCache.GetUploadTime();
                    TimeSpan dt = DateTime.Now.Date - uploadTime.Date;


                    if (lstVersionInfo.Count > 100 || uploadTime == DateTime.MinValue || dt.Days > 0)
                    {
                        foreach (tbVersionInfo info in lstVersionInfo)
                        {
                            if (info.Key == tbVersionCache.UPLOADTIMEKEY || info.Key == tbVersionCache.SOFTUSESTATEKEY)
                            {
                                continue;
                            }

                            //传输今天以前收集的数据
                            info.SendZhushouData();

                            //当前数据显示程序的运行时间大于0分钟，或者当前数据显示的启动时间大于1天，则不再保存
                            if ((info.runTimes > 0 || (DateTime.Now.Date - info.startTime.Date).Days > 0))
                            {
                                lstKeys.Add(info.Key);
                            }

                            Application.DoEvents();
                        }

                        //删除发送成功的软件数据
                        foreach (string key in lstKeys)
                        {
                            tbVersionCache.RemoveCacheData(key);
                        }

                        //保存上传日期
                        if (lstKeys.Count > 0)
                        {
                            tbVersionCache.AddUploadTime(DateTime.Now);
                        }

                        //保存文件
                        tbVersionCache.Save();
                    }
                }

                if (lstVersionInfo != null)
                {
                    lstVersionInfo.Clear();
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SendSoftDataPrevious");
            }
        }

        private void SendSoftDataNow(tbVersionInfo info)
        {
            try
            {
                int n = 0;
                if (!NativeMethods.InternetGetConnectedState(out n, 0))
                {
                    return;
                }

                info.SendZhushouData();

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SendSoftDataNow");
            }
        }

        //导入判断网络是否连接的 .dll  
        //[DllImport("wininet.dll", EntryPoint = "InternetGetConnectedState")]
        ////判断网络状况的方法,返回值true为连接，false为未连接  
        //public extern static bool InternetGetConnectedState(out int conState, int reder);

        #endregion

        #region "--- 检测升级 ---"

        private string _FolderUpgrade = "";
        private string _strUpdatePackageFile = "";
        private DateTime _SoftStartTime = DateTime.MinValue;
        private string _strVersion = "";
        private double _lngPassTime = 0;
        private int _CheckUpgradeShowIcon = 1;

        private Thread _CheckEnviromentThread = null;
        private void CheckUpgradFile()
        {
            this._strUpdatePackageFile = Path.Combine(Folder.AppFolder, "Upgrade.dat");
            this._FolderUpgrade = Path.Combine(Path.GetTempPath(), String.Format("{0}Upgrade", Folder.AppType.ToString()));
        }

        private void CheckUpgradeFileExist()
        {
            string strPathUpgradeSuceed = Path.Combine(Folder.AppFolder, "UpgradeSuceed.dll");

            this.CheckUpgradFile();

            bool blnNewIncrease = true;

            if (File.Exists(this._strUpdatePackageFile))
            {
                //助手升级增量更新时，不需要删除tbUpgrade目录
                if (blnNewIncrease == false)
                {
                    try
                    {
                        //删除临时目录中的tbUpgrade目录,以免以前的升级程序失败后会遗留文件
                        if (Directory.Exists(this._FolderUpgrade))
                        {
                            Directory.Delete(this._FolderUpgrade, true);
                        }
                    }
                    catch { }
                }

                if (File.Exists(strPathUpgradeSuceed))
                {
                    this.mIsFirstRunAfterUpgrade = true;

                    //升级成功后清理升级产生的临时文件
                    try
                    {
                        File.Delete(strPathUpgradeSuceed);
                    }
                    catch { }

                    try
                    {
                        File.Delete(_strUpdatePackageFile);
                    }
                    catch { }

                }
                else
                {
                    if (!(Folder.LangType == LanguageType.en_US))
                    {
                        //助手升级增量更新时，每个下载的文件都会先比较md5，所以这边不需要再比较
                        if (blnNewIncrease == false)
                        {
                            //如果包不完整则删除
                            string strServerFileLength = IniClass.GetIniSectionKey("Setting", "UpdateFileLength", Folder.ConfigIniFile);
                            if (!this.CheckUpdateFileIsFull(this._strUpdatePackageFile, strServerFileLength))
                            {
                                try
                                {
                                    File.Delete(_strUpdatePackageFile);
                                }
                                catch { }
                                return;
                            }
                        }
                        else if (!Directory.Exists(this._FolderUpgrade))
                        {
                            return;
                        }

                        //升级文件已经下载完毕，但是用户没有立刻升级，所以启动的时候需要程序自动升级
                        LiveUpdateHelper.StartLiveUpdateExe(this._strUpdatePackageFile, this._FolderUpgrade, Application.ExecutablePath, "");
                    }
                }
            }
        }

        public void StartToCheckUpdate(bool blnUserCheckUpgrade = false, bool blnClick = false)
        {
            //新升级逻辑（国内版）
            if (Folder.LangType != LanguageType.vi_VN)
            {
                string strAppName = Language.GetString("Common.SoftwareName");// "微信备份助手";
                HelperExeManager.CheckUpdate(blnUserCheckUpgrade, strAppName, Application.ExecutablePath);
                return;
            }

        }

        private void tmrUpgrade_Tick(System.Object sender, System.EventArgs e)
        {
            if (Folder.LangType == LanguageType.en_US)
            {
                this.tmrUpgrade.Stop();
                return;
            }

            //If Me._CheckUpgrade Then
            if (this._CheckUpgradeShowIcon == 1)
            {
                this.mNotifyIcon.Icon = global::iWechatAssistant.Properties.Resources.main;
                this._CheckUpgradeShowIcon = 2;

            }
            else if (this._CheckUpgradeShowIcon == 2)
            {
                this.mNotifyIcon.Icon = global::iWechatAssistant.Properties.Resources.main;
                this._CheckUpgradeShowIcon = 3;

            }
            else if (this._CheckUpgradeShowIcon == 3)
            {
                this.mNotifyIcon.Icon = global::iWechatAssistant.Properties.Resources.main;
                this._CheckUpgradeShowIcon = 4;

            }
            else if (this._CheckUpgradeShowIcon == 4)
            {
                this.mNotifyIcon.Icon = global::iWechatAssistant.Properties.Resources.main;
                this._CheckUpgradeShowIcon = 1;
            }
        }

        private bool CheckUpdateFileIsFull(string strUpdateFile, string strServerFileLength)
        {
            bool blnFull = false;

            if (File.Exists(strUpdateFile))
            {
                try
                {
                    long lngFileLength = new FileInfo(strUpdateFile).Length;
                    if (string.Compare(lngFileLength.ToString(), strServerFileLength) == 0)
                    {
                        blnFull = true;
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "CheckUpdateFileIsFull");
                }
            }

            return blnFull;
        }

        #endregion

        #region "--- 生成错误日志 ---"

        private void CreateErrorZip()
        {
            if (mTdCreateError != null && mTdCreateError.ThreadState != ThreadState.Stopped)
                return;

            mTdCreateError = new Thread(new ThreadStart(DoCreateErrorZip));
            mTdCreateError.IsBackground = true;
            mTdCreateError.Start();
        }

        private void DoCreateErrorZip()
        {
            try
            {
                string tmpFile = Path.Combine(Folder.TempFolder, @"ErrorZip\" + Guid.NewGuid().ToString("N").Substring(0, 10));
                string tmpFileException = Path.Combine(tmpFile, "Exception");
                string tmpFileLog = Path.Combine(tmpFile, "Logs");
                string tmpFileComputerInfo = Path.Combine(tmpFile, "ComputerInfo");
                string tmpFlieAnalysis = Path.Combine(tmpFile, "Analysis");
                string tmpFlieAnalysisException64 = Path.Combine(tmpFlieAnalysis, "AnalysisException_x64");
                string tmpFlieAnalysisException86 = Path.Combine(tmpFlieAnalysis, "AnalysisException_x86");


                try
                {
                    Directory.Delete(tmpFile, true);
                }
                catch { }

                Folder.CheckFolder(tmpFileException);
                Folder.CheckFolder(tmpFileLog);
                Folder.CheckFolder(tmpFileComputerInfo);
                Folder.CheckFolder(tmpFlieAnalysisException64);
                Folder.CheckFolder(tmpFlieAnalysisException86);

                CreateMacInfo(tmpFileComputerInfo);

                string strSourcePath = Path.GetDirectoryName(Folder.AppFolder);
                GetErrorFile(Path.Combine(strSourcePath, "Exception"), tmpFileException);
                GetErrorFile(Path.Combine(strSourcePath, "Logs"), tmpFileLog);

                try
                {
                    string iniPath = Path.Combine(strSourcePath, "IncludeDlls", "CWehelp_x64", "iWechatAssistant.ini");

                    if (File.Exists(iniPath))
                        File.Copy(iniPath, Path.Combine(tmpFlieAnalysisException64, "iWechatAssistant_x64.ini"));

                    iniPath = Path.Combine(strSourcePath, "IncludeDlls", "CWehelp_x86", "iWechatAssistant.ini");
                    if (File.Exists(iniPath))
                        File.Copy(iniPath, Path.Combine(tmpFlieAnalysisException86, "iWechatAssistant_x86.ini"));

                    GetErrorFile(Path.Combine(strSourcePath, "IncludeDlls", "CWehelp_x64", "Exception"), tmpFlieAnalysisException64);
                    GetErrorFile(Path.Combine(strSourcePath, "IncludeDlls", "CWehelp_x86", "Exception"), tmpFlieAnalysisException86);
                }
                catch (Exception)
                {
                }

                //压缩
                string strZipFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), string.Format("LOG{0}.zip", DateTime.Now.ToString("yyyyMMddHHmmss")));
                Utility.PackFiles(strZipFilePath, tmpFile);
                Common.OpenExplorer(strZipFilePath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoCreateErrorZip");
            }
        }

        private static void CreateMacInfo(string strPath)
        {
            try
            {
                if (Directory.Exists(strPath))
                {
                    Directory.Delete(strPath, true);
                }

                Folder.CheckFolder(strPath);
                string strDESKey = "am58!2#0";
                string strFilePath = Path.Combine(strPath, string.Format("Computer at {0}.txt", DateTime.Now.ToString("yyyyMMdd")));
                string hardDiskId = Common.EncryptDES("DiskId=" + Common.GetHardDiskID2(), strDESKey, strDESKey);
                string cpuid = Common.EncryptDES("CpuId=" + Common.GetProcessorId(), strDESKey, strDESKey);
                string macAddress = Common.EncryptDES("WifiMac=" + Common.GetMacAddress(), strDESKey, strDESKey);
                string macid = Common.EncryptDES("MacId=" + Common.GetComputerID(), strDESKey, strDESKey);
                string allInfo = string.Format("{0}_@_{1}_@_{2}_@_{3}", hardDiskId, cpuid, macAddress, macid);
                if (File.Exists(strFilePath))
                {
                    File.Delete(strFilePath);
                }
                using (StreamWriter sw = new StreamWriter(strFilePath, true, Encoding.UTF8))
                {
                    sw.WriteLine(allInfo);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateMacInfo");
            }
        }

        private static void GetErrorFile(string sourcePath, string desPath, string strName = "", string strSuffix = "txt")
        {
            try
            {
                if (Directory.Exists(sourcePath) == false)
                {
                    return;
                }

                if (strName.Length == 0)
                {
                    strName = Path.GetFileNameWithoutExtension(sourcePath).TrimEnd('s');
                }

                //Dim strFileName As String = String.Format("{0}{1}.{2}", strName, "{0}", strSuffix)
                List<string> fileList = new List<string>();

                //只取今天之前7天的数据
                for (int index = 0; index <= 6; index++)
                {
                    fileList.Add((DateTime.Now.AddDays(-index)).ToString("yyyyMMdd"));
                }

                foreach (string Item in Directory.GetFiles(sourcePath))
                {
                    string strFileName = Path.GetFileName(Item);
                    foreach (string itemDate in fileList)
                    {
                        if (strFileName.Contains(itemDate))
                        {
                            try
                            {
                                File.Copy(Item, Path.Combine(desPath, strFileName));
                            }
                            catch (Exception ex)
                            {
                            }
                        }
                    }
                }

                fileList.Clear();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetErrorFile");
            }


        }

        #endregion

        #region "--- 加载头像 ---"

        private void LoadHeadIcon(List<tbDataGridViewRow> rowFindData = null)
        {
            Common.Log("加载头像", "LoadHeadIcon", m_bIsDebug);
            try
            {
                if (this.mTdLoadHeadIcon != null && this.mTdLoadHeadIcon.ThreadState != ThreadState.Stopped)
                {
                    this.mTdLoadHeadIcon.Abort();
                }
            }
            catch { }

            this.mTdLoadHeadIcon = new Thread(LoadHeadIconInThread);
            mTdLoadHeadIcon.IsBackground = true;
            mTdLoadHeadIcon.Start(rowFindData);
        }

        private void LoadHeadIconInThread(object listRows)
        {
            //Common.LogException("加载头像", "LoadHeadIconInThread");
            //下载好友列表文件 并解析
            //this.LoadFriends(this.mCurrentWAInfo);    
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null)
            {
                //Common.LogException("加载头像 return", "LoadHeadIconInThread");
                return;
            }
            try
            {
                if (File.Exists(waInfo.IconFilePath))
                {
                    //Image imgIcon = Utility.GetImageFormFile(waInfo.IconFilePath);

                    string strIconFolder = Path.Combine(Folder.CacheFolder, string.Format(@"HeadIcon\{0}", waInfo.Account));
                    Folder.CheckFolder(strIconFolder);
                    string strIconPath = Path.Combine(strIconFolder, string.Format(@"{0}.pic", waInfo.Account));
                    try
                    {
                        File.Copy(waInfo.IconFilePath, strIconPath, true);
                    }
                    catch { }
                }
            }
            catch { }

            //加载头像
            try
            {
                bool bAddedRow = false;
                List<tbDataGridViewRow> rowFindData = (List<tbDataGridViewRow>)listRows;

                if (rowFindData == null)
                {
                    rowFindData = new List<tbDataGridViewRow>();
                    bAddedRow = true;
                }

                //Common.LogException("缓存获取", "LoadHeadIconInThread");
                //先从缓存里面加载头像
                foreach (tbDataGridViewRow tRow in this.dgvFriend.Rows)
                {
                    this.UpdateHeadIconToDataGridView(tRow, true);

                    if (bAddedRow)
                    {
                        if (tRow.Tag == null || tRow.Tag.GetType() != typeof(WechatTalk))
                            continue;

                        rowFindData.Add(tRow);
                        //取出最后聊天记录
                        WechatTalk item = (WechatTalk)tRow.Tag;

                        string strMsg = this.OperateLastChatText(this.GetViewLastChat(item));
                        if (ServerIniSetting.IsMsgHide() && WechatHeplerPC.mAssistInfo.IsPay && waInfo.AState != ActivateState.Normal)
                            strMsg = this.MsgHide(strMsg);

                        item.LastChatText = strMsg;
                    }
                }
                Common.Log("下载", "LoadHeadIconInThread", m_bIsDebug);
                Dictionary<tbDataGridViewRow, List<string>> dicChat = new Dictionary<tbDataGridViewRow, List<string>>();
                foreach (tbDataGridViewRow row in rowFindData)
                {
                    WechatTalk wItem = (WechatTalk)row.Tag;
                    if (wItem == null)
                        continue;

                    //1、需要下载的头像列表
                    bool blnIsChat = false;
                    List<string> lstItems = new List<string>();

                    if (WechatHeplerPC.CheckIsChatRoom(wItem.UserName))
                    {
                        blnIsChat = true;

                        //群组需要把成员的头像都下载下来
                        WechatMessageItem[] aryMessageItems = wItem.MessageItems.ToArray();
                        List<string> userIds = new List<string>();
                        foreach (WechatMessageItem weMsg in aryMessageItems)
                        {
                            int len = weMsg.MessageContent.IndexOf(":");
                            if (len != -1)
                            {
                                string userId = weMsg.MessageContent.Substring(0, len);
                                if (userIds.Contains(userId))
                                {
                                    continue;
                                }
                                userIds.Add(userId);
                            }

                            string strUsrName = "";
                            string wxAccount = "";
                            string strNickName = "";
                            string strMsgForWeb = weMsg.MessageContent;
                            string strPetName = "";
                            // 提取群组中成员信息
                            WechatHeplerPC.AnalyseGroup(waInfo.DicWeChatFriendInfo,
                                                        ref strUsrName, ref wxAccount,
                                                        ref strNickName, ref strMsgForWeb, ref strPetName,
                                                        waInfo.LstWeChatGroupFriendInfo, wItem.UserName);

                            if (!string.IsNullOrEmpty(strUsrName) && !lstItems.Contains(strUsrName))
                                lstItems.Add(strUsrName);
                        }
                    }

                    if (!lstItems.Contains(wItem.UserName))
                    {
                        lstItems.Add(wItem.UserName);
                    }

                    if (blnIsChat)
                    {
                        //群组头像添加到队列，最后获取
                        dicChat.Add(row, lstItems);
                    }
                    else
                    {
                        //2、下载头像
                        this.DownHeadIcon(lstItems);

                        //3、更新界面
                        this.UpdateHeadIconToDataGridView(row);
                    }
                }

                try
                {
                    if (m_threadDown != null && (m_threadDown.IsAlive || m_threadDown.ThreadState != ThreadState.Stopped))
                    {
                        m_threadDown.Abort();
                    }
                }
                catch { }

                m_threadDown = new Thread(new ParameterizedThreadStart(DownWechatGroupIcon));
                m_threadDown.IsBackground = true;
                m_threadDown.Start(dicChat);

                // 删除所有临时文件
                WechatHeplerPC.DeleteXmlTempDir();
            }
            catch (ThreadAbortException e)
            {
                System.Diagnostics.Debug.Write(e);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "LoadHeadIconInThread");
            }
            //this.ChangeButtonRefreshEnable(true);

            mIsLoadHeadIconFinish = true;
        }

        private void DownWechatGroupIcon(object objChats)
        {
            Dictionary<tbDataGridViewRow, List<string>> dictChats = (Dictionary<tbDataGridViewRow, List<string>>)objChats;
            foreach (tbDataGridViewRow row in dictChats.Keys)
            {
                //2、下载头像
                this.DownHeadIcon(dictChats[row]);

                //3、更新界面
                this.UpdateHeadIconToDataGridView(row);
            }
            dictChats.Clear();
        }

        private bool HaveFriendColumn(string colname)
        {
            if (dgvFriend != null &&
                dgvFriend.Columns != null &&
                dgvFriend.Columns.Count > 0)
            {
                try
                {
                    return dgvFriend.Columns[colname].Name.Equals(colname, StringComparison.InvariantCultureIgnoreCase);
                }
                catch { }
            }
            return false;
        }

        private delegate void UpdateHeadIconToDataGridViewHandler(tbDataGridViewRow row, bool onlyicon);
        private void UpdateHeadIconToDataGridView(tbDataGridViewRow row, bool onlyicon = false)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new UpdateHeadIconToDataGridViewHandler(UpdateHeadIconToDataGridView), row, onlyicon);
                }
                else
                {
                    WechatAccInfo waInfo = GetAcctInfo();
                    if (row.Index < 0 || waInfo == null || !HaveFriendColumn("colName"))
                        return;

                    WechatTalk talk = (WechatTalk)row.Tag;
                    Image img = WechatHeplerPC.GetHeadIconFromPC(talk, waInfo);

                    tbDataGridViewTextBoxCell cellName = (tbDataGridViewTextBoxCell)row.Cells["colName"];
                    if (img != null)
                    {
                        cellName.tbIcon = img;
                        Application.DoEvents();
                    }
                    //else
                    //{                       
                    //    Common.Log(string.Format("WechatTalk:(UserName:{0}), icon is null.", talk.UserName));
                    //}

                    if (!onlyicon)
                    {
                        string strDescription = string.Empty;
                        string strName = string.Empty;
                        if (strName.Length == 0)
                        {
                            strName = talk.NickName;
                            if (strName.Length <= 0)
                            {
                                strName = talk.UserName;
                            }
                            strName = Utility.ReplaceWinIllegalName(strName);
                        }

                        strDescription = strName + "\r\n" + talk.LastChatText;
                        cellName.Value = strDescription;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(string.Format("colName:{0}  colMD5:{1}  ", row.Cells["colName"].Value, row.Cells["colMD5"].Value) + ex.ToString(), "UpdateHeadIconToDataGridView");
            }
        }

        //private Image GetHeadIconFromPC(WechatTalk item)
        //{
        //    Image img = null;
        //    WeChatFriendInfo info = null;
        //    if (this.mCurrentWAInfo.DicWeChatFriendInfo.ContainsKey(item.UserName))
        //    {
        //        info = this.mCurrentWAInfo.DicWeChatFriendInfo[item.UserName];
        //    }
        //    if (info == null)
        //        return img;

        //    string imgPath = string.Format("{0}\\HeadIcon\\{1}\\{2}.pic", Folder.CacheFolder, info.StrInnerAccount, info.StrWXID);

        //    if (File.Exists(imgPath))
        //    {
        //        img = Utility.GetImageFormFile(imgPath, true);
        //    }
        //    //如果图片无法使用就删除掉，下次进来还会重新加载
        //    if (img == null)
        //    {
        //        try
        //        {
        //            if (Common.IsTestMode() && File.Exists(imgPath))
        //            {
        //                File.Delete(imgPath);
        //            }
        //        }
        //        catch { }
        //    }
        //    return img;
        //}

        //private string GetHeadIconPathFromPC(string wxid)
        //{
        //    WeChatFriendInfo info = null;
        //    string imgPath = "";
        //    if (this.mCurrentWAInfo.DicWeChatFriendInfo.ContainsKey(wxid))
        //    {
        //        info = this.mCurrentWAInfo.DicWeChatFriendInfo[wxid];
        //    }

        //    if (info == null)
        //    {
        //        return imgPath;
        //    }

        //    string imgTempPath = string.Format("{0}\\HeadIcon\\{1}\\{2}.pic", Folder.CacheFolder, info.StrInnerAccount, info.StrWXID);

        //    if (File.Exists(imgTempPath))
        //    {
        //        imgPath = imgTempPath;
        //    }

        //    return imgPath;
        //}

        private void DownHeadIcon(object lstItems)
        {
            List<string> icons = lstItems as List<string>;
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null || icons.Count == 0) return;

            try
            {
                string strFileOnPC = "";
                string strFolderOnPC = "";

                foreach (string item in icons)
                {
                    if (waInfo.DicWeChatFriendInfo.ContainsKey(item))
                    {
                        WeChatFriendInfo info = waInfo.DicWeChatFriendInfo[item];
                        strFolderOnPC = Path.Combine(Folder.CacheFolder, string.Format(@"HeadIcon\{0}\", info.StrWXID));
                        Folder.CheckFolder(strFolderOnPC);

                        strFileOnPC = Path.Combine(strFolderOnPC, string.Format("{0}.pic", info.StrWXID));
                        if (!File.Exists(strFileOnPC))
                        {
                            string iconUrl = info.StrHeadImgUrl;

                            if (!string.IsNullOrEmpty(iconUrl))
                            {
                                Common.DownloadImage(iconUrl, 20000, strFileOnPC);
                            }
                        }

                        //if (!File.Exists(strFileOnPC))
                        //{
                        //    Common.Log(string.Format("DownHeadIcon()=====>{0} head icon is null >>> HeadImgUrl is {1}.", item, info.StrHeadImgUrl));
                        //}
                    }
                }
            }
            catch (ThreadAbortException e)
            {
                System.Diagnostics.Debug.Write(e);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DownHeadIcon");
            }
        }

        #endregion

        #region "--- WebBrowser处理方法 ---"

#if NET40
        private delegate void NewMessageHandler(CefWebBrowser sender, string message);

        private void wbsChat_NewMessage(CefWebBrowser sender, string message)
#else
        private delegate void NewMessageHandler(object sender, string message);

        private void wbsChat_NewMessage(object sender, string message)
#endif
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new NewMessageHandler(wbsChat_NewMessage), sender, message);
            }
            else
            {
                WechatTalk wTalk = wbsChat.Tag as WechatTalk;
                WechatAccInfo waInfo = GetAcctInfo();

                if (message == null || waInfo == null || wTalk == null)
                    return;

                string url = message;

                if (url.StartsWith("weixin://getnews", StringComparison.OrdinalIgnoreCase))
                {
                    this.GetNews(url);
                }
                else if (url.StartsWith("weixin://viewphoto", StringComparison.OrdinalIgnoreCase))
                {
                    if (!this.ShowMedia(url, false, wTalk))
                        return;
                }
                else if (url.StartsWith("weixin://copy", StringComparison.OrdinalIgnoreCase))
                {
                    this.CopyNews(url);
                }
                else if (url.StartsWith("weixin://exportphoto", StringComparison.OrdinalIgnoreCase))
                {
                    if (!this.ShowMedia(url, true, wTalk))
                        return;
                }
                else if (url.StartsWith("weixin://exportvoice", StringComparison.OrdinalIgnoreCase))
                {
                    if (!this.ShowMedia(url, true, wTalk))
                        return;
                }
                else if (url.StartsWith("weixin://playvoice", StringComparison.OrdinalIgnoreCase))
                {
                    if (ServerIniSetting.IsMsgHide() && !this.PaidRemind(waInfo, false))
                        return;
                    this.PlayVoice(url);
                }
                else if (url.StartsWith("weixin://playvideo", StringComparison.OrdinalIgnoreCase))
                {
                    if (!this.ShowMedia(url, false, wTalk))
                        return;
                }
                else if (url.StartsWith("weixin://exportvideo", StringComparison.OrdinalIgnoreCase))
                {
                    if (!this.ShowMedia(url, true, wTalk))
                        return;
                }
                else if (url.StartsWith("weixin://getcontextnews", StringComparison.OrdinalIgnoreCase))
                {
                }
                else if (url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase))
                {
                    this.Navigate(url);
                }
                else if (url.StartsWith("weixin://getdetail", StringComparison.OrdinalIgnoreCase))
                {
                    if (ServerIniSetting.IsMsgHide() && !this.PaidRemind(waInfo, false))
                        return;
                    this.ShowMergerForwardInfo(url);
                }
                else if (url.StartsWith("weixin://opendocument", StringComparison.OrdinalIgnoreCase))
                {
                    if (!this.ShowMedia(url, false, wTalk))
                        return;
                }
                else if (url.StartsWith("weixin://exportdocument", StringComparison.OrdinalIgnoreCase))
                {
                    if (!this.ShowMedia(url, true, wTalk))
                        return;
                }
            }
        }

#if NET40
        private void wbsChatDetail_NewMessage(CefWebBrowser sender, string message)
#else
        private void wbsChatDetail_NewMessage(object sender, string message)
#endif
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new NewMessageHandler(wbsChatDetail_NewMessage), sender, message);
            }
            else
            {
                if (message == null)
                    return;

                string url = message;

                if (url.StartsWith("weixin://comeback", StringComparison.OrdinalIgnoreCase))
                {
                    ShowWebDetail(false);
                }
                else if (url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase))
                {
                    this.Navigate(url);
                }
            }
        }

        private void wbsChatDetail_Navigating(object sender, WebNavigatingEventArgs e)
        {
            string url = e.Url.ToString();

            if (url.StartsWith("weixin://comeback", StringComparison.OrdinalIgnoreCase))
            {
                ShowWebDetail(false);
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase))
            {
                this.Navigate(url);
                e.Cancel = true;
            }
        }

        private void wbsChat_Navigating(object sender, WebNavigatingEventArgs e)
        {
            string url = e.Url.ToString();

            if (url.StartsWith("weixin://getnews", StringComparison.OrdinalIgnoreCase))
            {
                this.GetNews(url);
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://viewphoto", StringComparison.OrdinalIgnoreCase))
            {
                if (!this.ShowMedia(url, false))
                    return;
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://copy", StringComparison.OrdinalIgnoreCase))
            {
                this.CopyNews(url);
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://exportphoto", StringComparison.OrdinalIgnoreCase))
            {
                if (!this.ShowMedia(url, true))
                    return;
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://exportvoice", StringComparison.OrdinalIgnoreCase))
            {
                if (!this.ShowMedia(url, true))
                    return;
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://playvoice", StringComparison.OrdinalIgnoreCase))
            {
                WechatAccInfo waInfo = GetAcctInfo();
                if (waInfo == null) return;

                if (ServerIniSetting.IsMsgHide() && !this.PaidRemind(waInfo, false))
                    return;
                this.PlayVoice(url);
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://playvideo", StringComparison.OrdinalIgnoreCase))
            {
                if (!this.ShowMedia(url, false))
                    return;
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://exportvideo", StringComparison.OrdinalIgnoreCase))
            {
                if (!this.ShowMedia(url, true))
                    return;
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://getcontextnews", StringComparison.OrdinalIgnoreCase))
            {
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase))
            {
                this.Navigate(url);
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://getdetail", StringComparison.OrdinalIgnoreCase))
            {
                WechatAccInfo waInfo = GetAcctInfo();
                if (waInfo == null) return;
                if (ServerIniSetting.IsMsgHide() && !this.PaidRemind(waInfo, false))
                    return;
                this.ShowMergerForwardInfo(url);
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://opendocument", StringComparison.OrdinalIgnoreCase))
            {
                if (!this.ShowMedia(url, false))
                    return;
                e.Cancel = true;
            }
            else if (url.StartsWith("weixin://exportdocument", StringComparison.OrdinalIgnoreCase))
            {
                if (!this.ShowMedia(url, true))
                    return;
                e.Cancel = true;
            }
        }

        //private void wbsChat_LoadStateChanged(object sender, LoadingStateChangedEventArgs e)
        //{
        //wbsChat.Tag = e.IsLoading.ToString();
        //}

        //private void wbsChatDetail_LoadStateChanged(object sender, LoadingStateChangedEventArgs e)
        //{
        //wbsChatDetail.Tag = e.IsLoading.ToString();
        //}

        private void Navigate(string url)
        {
            string startText = "?url=";
            int startIndex = url.IndexOf(startText);
            string strUrl = url.Substring(startIndex + startText.Length).Trim();

            if (!string.IsNullOrEmpty(strUrl))
            {
                string strPrefix = "http://";
                string strPrefixEx = "https://";

                if (!strUrl.StartsWith(strPrefixEx) && !strUrl.StartsWith(strPrefix))
                {
                    strUrl = strPrefix + strUrl;
                }

                Common.OpenExplorer(strUrl);
            }
        }

        private void GetNews(string url)
        {
            string strUsrName = Utility.GetParamValueFromQuery("friend", url);
            string strID = Utility.GetParamValueFromQuery("id", url);
            string strRender = Utility.GetParamValueFromQuery("render", url);
            int intWebPage = 0;
            int.TryParse(Utility.GetParamValueFromQuery("page", url), out intWebPage);
            if (string.IsNullOrEmpty(strUsrName) || string.IsNullOrEmpty(strID))
            {
                return;
            }

            try
            {
                if (mCurrentDataGridViewRow != null)
                    this.LoadChat(mCurrentDataGridViewRow, strRender, strID, intWebPage);

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetNews");
            }
        }

        private bool ShowMedia(string url, bool isExport, WechatTalk wTalk = null)
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return false;
            bool isRelust = true;
            if (ServerIniSetting.IsMsgHide() && !this.PaidRemind(waInfo, false))
            {
                isRelust = false;
                goto GoExit;
            }

            WechatMessageItem chat = this.GetChatForWeb(url);
            if (chat == null)
            {
                tbMessageBox.Show(this, "定位不到该项目，无法展示内容。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                isRelust = false;
                goto GoExit;
            }
            int intMapKey = 0;
            string strPath = "";

            switch (chat.SociaChatType)
            {
                case SociaChatType.Picture:
                case SociaChatType.Voice:
                    strPath = mWechatHeplerPC.GetMediaFilePath(chat, mCurrentBackupDBPath, waInfo.Password, mCurrentBackupMediaFile,
                                                               waInfo.PwdBuffer, false, mWeChatiOSDevice, out intMapKey);

                    if (isExport)
                    {
                        SeparateExportMedia(chat.SociaChatType, strPath, chat);
                    }
                    else
                    {
                        //没有可供预览的大图
                        if (File.Exists(strPath))
                        {
                            strPath = WxgfDec(strPath);
                            //展示图片
                            tbImageViewForm showPicFrm = new tbImageViewForm();
                            showPicFrm.SrcPicture = Utility.GetImageFormFile(strPath);
                            showPicFrm.ViewType = ViewType.FromPC;
                            showPicFrm.ShowListView = false;
                            showPicFrm.HideButton = ToolBarButtonType.Main | ToolBarButtonType.Delete | ToolBarButtonType.WeiBo | ToolBarButtonType.Prev | ToolBarButtonType.Next;
                            showPicFrm.ShowDialog();
                        }
                        else
                            tbMessageBox.Show(this, "没有可供预览的大图。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    break;

                case SociaChatType.Video:
                    if (isExport)
                    {
                        strPath = mWechatHeplerPC.GetMediaFilePath(chat, mCurrentBackupDBPath, waInfo.Password, mCurrentBackupMediaFile,
                                                                   waInfo.PwdBuffer, false, mWeChatiOSDevice, out intMapKey);

                        SeparateExportMedia(chat.SociaChatType, strPath, chat);
                    }
                    else
                        tbMessageBox.Show(this, "视频消息，请导出查看。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    break;

                case SociaChatType.webpage:
                case SociaChatType.WebPageText:
                case SociaChatType.WebPageDoc:
                case SociaChatType.WebPageXls:
                case SociaChatType.WebPagePPT:
                case SociaChatType.WebPagePDF:
                    string strMsgForWeb = chat.MessageContent;
                    JsonArray arrHtml5ContentForWeb = null;
                    string strTypeForWeb = "";
                    string strExport = "";
                    IMHtmlNodeType imtype = IMHtmlNodeType.IMHtmlNode_Media;

                    bool isLinkA = false;
                    string strFileName = mWeChatMsgWebPage.AnalysisNewWebpage(chat, ref strMsgForWeb, ref arrHtml5ContentForWeb,
                                                                              ref strTypeForWeb, ref strExport, ref imtype, ref isLinkA);

                    if (isExport)
                    {
                        strPath = mWechatHeplerPC.GetMediaFilePath(chat, mCurrentBackupDBPath, waInfo.Password,
                                                                   mCurrentBackupMediaFile, waInfo.PwdBuffer,
                                                                   false, mWeChatiOSDevice, out intMapKey);

                        SeparateExportMedia(chat.SociaChatType, strPath, chat, strFileName);
                    }
                    else
                        tbMessageBox.Show(this, "当前消息，请导出查看。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    break;

                default:
                    tbMessageBox.Show(this, string.Format("定位不到该项目。({0})", (int)chat.SociaChatType), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    break;
            }
            GoExit:
            return isRelust;
        }

        private void SeparateExportMedia(SociaChatType sct, string strPath, WechatMessageItem chat, string webpageFileName = "")
        {
            try
            {
                WechatAccInfo waInfo = GetAcctInfo();
                WechatTalk wTalk = wbsChat.Tag as WechatTalk;

                if (!File.Exists(strPath) || waInfo == null || wTalk == null)
                {
                    tbMessageBox.Show(this, "找不到源文件。\n\r请确认：\n\r1.备份前图片、视频、语音、聊天收发的文件能正常在手机上查看；\n\r2.备份前图片、视频、语音、聊天收发的文件在手机上查看过。", "提示",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                FolderBrowserDialog fbdWeixin = new FolderBrowserDialog();
                if (!string.IsNullOrEmpty(IniSetting.GetWCAExportDefaultFolder()))
                    fbdWeixin.SelectedPath = IniSetting.GetWCAExportDefaultFolder();
                fbdWeixin.Description = "导出";
                fbdWeixin.ShowNewFolderButton = true;
                if (fbdWeixin.ShowDialog() != DialogResult.OK)
                    return;

                string strAccount = Utility.ReplaceWinIllegalName(waInfo.InnerAccount == "" ? waInfo.Account : waInfo.InnerAccount);
                string strFolderPath = Path.Combine(fbdWeixin.SelectedPath, "WX消息记录-" + strAccount);
                strFolderPath = Path.Combine(strFolderPath, DateTime.Now.ToString("yyyyMMddHHmm") + "-" + Utility.ReplaceWinIllegalNameToHtml(btnWeCahtUsers.Text));
                strFolderPath = Path.Combine(strFolderPath, Utility.ReplaceWinIllegalName(waInfo.Name));
                Folder.CheckFolder(strFolderPath);

                string strUsrName = "";
                string wxAccount = "";
                string strNickName = "";
                string strMsgForWeb = chat.MessageContent;
                string strPetName = "";
                //群组的信息里面包括联系人名称
                if (WechatHeplerPC.IsReceivedMsg(chat, waInfo))
                {
                    if (WechatHeplerPC.CheckIsChatRoom(wTalk.UserName))
                    {
                        strNickName = WechatHeplerPC.GetChatRoomNickName(wTalk.UserName, waInfo.Account,
                                                                         waInfo.LstWeChatGroupFriendInfo);
                    }

                    if (strNickName.Length == 0)
                        strNickName = waInfo.Name;

                    strUsrName = chat.SendId;
                }
                else
                {
                    if (strNickName.Length == 0)
                        strNickName = wTalk.NickName;

                    //如果是群组，则需要取每一个人的用户名
                    if (WechatHeplerPC.CheckIsChatRoom(wTalk.UserName))
                    {
                        WechatHeplerPC.AnalyseGroup(waInfo.DicWeChatFriendInfo,
                            ref strUsrName, ref wxAccount,
                            ref strNickName, ref strMsgForWeb, ref strPetName,
                            waInfo.LstWeChatGroupFriendInfo, wTalk.UserName);
                    }
                    else
                        strUsrName = chat.SendId;
                }

                string strFilePath = string.Format("{0}_{1}_{2}", chat.MessageDT.ToString("yyyyMMddHHmmss"), Path.GetFileName(strPath), strNickName);
                switch (sct)
                {
                    case SociaChatType.Picture:
                        strFolderPath = Path.Combine(strFolderPath, "图片");
                        Folder.CheckFolder(strFolderPath);
                        strFilePath = Path.Combine(strFolderPath, strFilePath + ".png");
                        break;
                    case SociaChatType.Voice:
                        strFolderPath = Path.Combine(strFolderPath, "语音");
                        Folder.CheckFolder(strFolderPath);
                        strFilePath = Path.Combine(strFolderPath, strFilePath + ".mp3");
                        strPath = WechatHeplerPC.GetChatMp3PathFromPC(strPath, chat.MessageContent);
                        break;
                    case SociaChatType.Video:
                        strFolderPath = Path.Combine(strFolderPath, "视频");
                        Folder.CheckFolder(strFolderPath);
                        strFilePath = Path.Combine(strFolderPath, strFilePath + ".mp4");
                        break;
                    case SociaChatType.WebPageText:
                        strFolderPath = Path.Combine(strFolderPath, "文本");
                        Folder.CheckFolder(strFolderPath);
                        strFilePath = Path.Combine(strFolderPath, strFilePath + "_" + webpageFileName);
                        break;
                    case SociaChatType.WebPageDoc:
                        strFolderPath = Path.Combine(strFolderPath, "Word");
                        Folder.CheckFolder(strFolderPath);
                        strFilePath = Path.Combine(strFolderPath, strFilePath + "_" + webpageFileName);
                        break;
                    case SociaChatType.WebPageXls:
                        strFolderPath = Path.Combine(strFolderPath, "Excel");
                        Folder.CheckFolder(strFolderPath);
                        strFilePath = Path.Combine(strFolderPath, strFilePath + "_" + webpageFileName);
                        break;
                    case SociaChatType.WebPagePPT:
                        strFolderPath = Path.Combine(strFolderPath, "ppt");
                        Folder.CheckFolder(strFolderPath);
                        strFilePath = Path.Combine(strFolderPath, strFilePath + "_" + webpageFileName);
                        break;
                    case SociaChatType.WebPagePDF:
                        strFolderPath = Path.Combine(strFolderPath, "pdf");
                        Folder.CheckFolder(strFolderPath);
                        strFilePath = Path.Combine(strFolderPath, strFilePath + "_" + webpageFileName);
                        break;
                    default:
                        if (webpageFileName.Length > 0)
                        {
                            strFolderPath = Path.Combine(strFolderPath, "附件");
                            Folder.CheckFolder(strFolderPath);
                            strFilePath = Path.Combine(strFolderPath, strFilePath + "_" + webpageFileName);
                        }
                        break;
                }

                try
                {
                    File.Copy(strPath, strFilePath);

                    if (sct == SociaChatType.Picture)
                        WxgfDec(strFilePath);
                }
                catch
                {
                }

                Common.OpenExplorer(strFolderPath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SeparateExportMedia");
            }
        }

        public string WxgfDec(string filepath)
        {
            string res = filepath;
            string dllPath = Environment.CurrentDirectory + "\\VoipEngine.dll";
            //1.初始化
            ExportHelper.decWxgfFunInit(dllPath, log_callback);

            if (File.Exists(filepath) && ExportHelper.isWxgf(filepath) == 1)
            {
                string outPath = Path.Combine(Path.GetDirectoryName(filepath), Path.GetFileName(filepath));
                int ret = ExportHelper.wxgfDec(filepath, outPath);
                if (ret == 0)
                    res = outPath;
            }
            return res;
        }

        public void log_callback(string log, int len)
        {

        }

        private WechatMessageItem GetChatForWeb(string url)
        {
            WechatMessageItem result = null;
            string strUsrName = Utility.GetParamValueFromQuery("friend", url);
            string strID = Utility.GetParamValueFromQuery("id", url);
            if (strID.Length == 0)
                strID = Utility.GetParamValueFromQuery("messageId", url);

            if (string.IsNullOrEmpty(strUsrName) || string.IsNullOrEmpty(strID))
            {
                return result;
            }

            WechatTalk wTalk = wbsChat.Tag as WechatTalk;
            if (wTalk == null) return null;

            if (IniSetting.GetIsTalkSaveDB())
            {
                WechatAccInfo waInfo = GetAcctInfo();

                if (waInfo == null) return null;

                //List<DateTime> lstDateTime = new List<DateTime>();
                //lstDateTime.Add(dtpStart.Value);
                //lstDateTime.Add(dtpEnd.Value);
                int intStartIndex = Convert.ToInt32((wTalk.PageIndex - 1) * wTalk.PageCount);

                string strWhere = string.Format(" MessageId='{0}' and (SendId='{1}' or ReceiveId='{1}') ", strID, strUsrName);
                List<WechatMessageItem> lstMessageItems = WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).GetTalkInfo(wTalk.Md5, strWhere);
                if (lstMessageItems.Count > 0)
                {
                    result = lstMessageItems[0];
                    string strTypeForWeb = "";
                    string strMsgForWeb = result.MessageContent;
                    JsonArray arrHtml5ContentForWeb = null;
                    string strNickNameEx = "";
                    this.AnalyseMessageType(DataFilterType.Normal, "", ref strTypeForWeb, ref strMsgForWeb, ref arrHtml5ContentForWeb, result, ref strNickNameEx);
                }
            }
            else
            {
                WechatMessageItem[] aryMessageItems = wTalk.MessageItems.ToArray();
                foreach (WechatMessageItem wItem in aryMessageItems)
                {
                    // 之前没有兼容自己发的语音， 尝试判断 recerviid， 其实我觉得只要判断 messageid 就可以，不知道会不会错
                    //if ((string.Compare(Item.SendId, strUsrName, true) == 0 || string.Compare(Item.ReceiveId, strUsrName, true) == 0) &&
                    //    string.Compare(Item.MessageId.ToString(), strID, true) == 0)
                    //{
                    //    result = Item;
                    //    break;
                    //}

                    if ((wItem.SendId.Equals(strUsrName, StringComparison.InvariantCultureIgnoreCase) ||
                        wItem.ReceiveId.Equals(strUsrName, StringComparison.InvariantCultureIgnoreCase)) &&
                        wItem.MessageId.Equals(strID))
                    {
                        result = wItem;
                        break;
                    }
                }
            }

            return result;
        }

        private void CopyNews(string url)
        {
            //if (this.CheckChargeOverdue)
            //{
            //    return;
            //}
            //WeChatChatInfo chat = this.GetChatForWeb(url);
            //if (chat != null)
            //{
            //    string strContent = this.GetChatMsg(chat, ChatMsgType.ShowOnWeb);

            //    try
            //    {
            //        Clipboard.SetText(strContent);

            //        this.ChangeStateText(this.Language.GetString("Welcome.Message.CopySucceed"));
            //    }
            //    catch
            //    {
            //        this.ChangeStateText(this.Language.GetString("Weixin.Message.CopyFail"));
            //        // "复制失败！"
            //    }
            //}
        }

        private void PlayVoice(string url)
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return;

            WechatMessageItem chat = this.GetChatForWeb(url);
            if (chat == null) return;

            //下载语音
            int intMapKey = 0;
            string strPath = mWechatHeplerPC.GetMediaFilePath(chat, this.mCurrentBackupDBPath, waInfo.Password,
                                                            this.mCurrentBackupMediaFile, waInfo.PwdBuffer,
                                                            true, this.mWeChatiOSDevice, out intMapKey);

            ////获取语音路径（wav格式）
            string audioPath = WechatHeplerPC.GetChatWavPathFromPC(strPath, chat.MessageContent);

            if (File.Exists(audioPath))
            {
                bool blnNeedPlay = true;

                if (this.mPlayer.PlayState == WMPPlayState.wmppsPlaying)
                {
                    string strFilePlaying = this.mPlayer.CurrentMedia.sourceURL;

                    //web页面暂停之前的
                    if (string.Compare(strFilePlaying, audioPath, true) == 0)
                    {
                        blnNeedPlay = false;
                        //如果正在播放，播放的是同一首歌，则暂停
                    }
                    else
                    {
                        blnNeedPlay = true;
                        //如果是新的语音，则播放新的语音
                    }

                    this.mPlayer.Pause();
                    this.ShowAudioWebView(strFilePlaying, false);
                }

                if (blnNeedPlay)
                {
                    //Debug.Print("播放")
                    this.mPlayer.GetUserPlaylist(this.Name).Clear();
                    this.mPlayer.GetUserPlaylist(this.Name).Add(audioPath);
                    //Me.mPlayer.GetUserPlaylist(Me.Name).SetItem(0, strWavFile)
                    this.mPlayer.CurrentUserPlaylist = this.mPlayer.GetUserPlaylist(this.Name);
                    this.mPlayer.Play();
                    this.ShowAudioWebView(chat.MessageId.ToString(), true);
                }
            }
            //else
            //{
            //    string strError = "<<<< 播放语音失败!" + audioPath;
            //    Common.Log(strError);
            //}
        }

        private void ShowMergerForwardInfo(string url)
        {
            string strUsrName = Utility.GetParamValueFromQuery("friend", url);
            this.mStrMsgId = Utility.GetParamValueFromQuery("messageId", url);
            this.mStrMsgDetailId = Utility.GetParamValueFromQuery("detailId", url);
            List<MergerForwardInfo> lstInfo = this.mWeChatMsgWebPage.lstMergerForwardInfo.FindAll(this.ListFind);
            string strJson = this.GetChatJsonDetail(lstInfo);
            ChatWebPage.ClearChatOnWebPage(this.wbsChatDetail);
            this.ShowChatDetailOnWebPage(strJson);
        }

        private bool ListFind(MergerForwardInfo info)
        {
            if (string.IsNullOrEmpty(this.mStrMsgDetailId))
                return info.MsgId.Equals(mStrMsgId) ? true : false;
            else
                return info.MsgId.Equals(mStrMsgId) && info.SrcMsgLocalid.Equals(this.mStrMsgDetailId) ? true : false;
        }

        #endregion

        #region "--- 消息序列化 ---"

        // 将消息序列化为二进制的方法  
        // < param name="model">要序列化的对象< /param>  
        public byte[] Serialize(WeChatFriendInfoModel model)
        {
            try
            {
                // 涉及格式转换，需要用到流，将二进制序列化到流中  
                using (MemoryStream ms = new MemoryStream())
                {
                    // 使用ProtoBuf工具的序列化方法  
                    ProtoBuf.Serializer.Serialize<WeChatFriendInfoModel>(ms, model);
                    // 定义二级制数组，保存序列化后的结果  
                    byte[] result = new byte[ms.Length - 1 + 1];
                    // 将流的位置设为0，起始点  
                    ms.Position = 0;
                    // 将流中的内容读取到二进制数组中  
                    ms.Read(result, 0, result.Length);
                    return result;
                }
            }
            catch (Exception ex)
            {
                Common.LogException("反序列化失败: " + ex.ToString(), "Serialize");
                return null;
            }
        }

        // 将收到的消息反序列化成对象  
        // < returns>The serialize.< /returns>  
        // < param name="msg">收到的消息.</param>  
        public WeChatFriendInfoModel DeSerialize(byte[] msg)
        {
            try
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    // 将消息写入流中  
                    ms.Write(msg, 0, msg.Length);
                    // 将流的位置归0  
                    ms.Position = 0;
                    // 使用工具反序列化对象  
                    WeChatFriendInfoModel result = ProtoBuf.Serializer.Deserialize<WeChatFriendInfoModel>(ms);
                    return result;
                }
            }
            catch (Exception ex)
            {
                Common.LogException("反序列化失败: " + ex.ToString(), "DeSerialize");
                return null/* TODO Change to default(_) if this is not a reference type */;
            }
        }

        #endregion

        #region "--- 私有方法 ---"

        private ExportType m_exportType = ExportType.None;

        private void NewExport(ExportType exportType)
        {
            //if (exportType != ExportType.ExportToAll && 
            //    exportType != ExportType.ExportToHtml && 
            //    exportType != ExportType.ExportToExcel && 
            //    exportType != ExportType.ExportToTxt)
            //{
            //    if (!this.PaidRemind(this.mCurrentWAInfo, false))
            //        return;
            //}

            //if (!ServerIniSetting.GetWCAPreviewExport())//预览导出开关
            //{
            //    //1.付费 提示
            //    if (!this.PaidRemind(this.mCurrentWAInfo, false))
            //        return;
            //}

            if (IniSetting.GetIsTalkSaveDB())
            {
                m_exportType = exportType;
                if (dgvFriend != null && !dgvFriend.Enabled)
                    return;
            }
            m_exportType = ExportType.None;
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return;

            //1.付费 提示
            if (!this.PaidRemind(waInfo, false))
                return;

            bool isPreview = (waInfo.AState != ActivateState.Normal);

            InitExportHelper(waInfo.Account);
            try
            {
                List<DataGridViewRow> lstRows = null;
                if (!CheckExport(ref lstRows))
                {
                    return;
                }

                Common.Log("MainForm's NewExport()>>>>>>>>Starting to export contents......", m_bIsDebug);

                if (IniSetting.GetWCAShow())
                {
                    StringBuilder sb = new StringBuilder();
                    sb.AppendLine("1.语音、图片、视频、聊天收发的文件需要在手机上查看后才能完整");
                    sb.AppendLine("备份至电脑，请确保需要导出的数据是在手机上查看过后才备份的。");
                    sb.AppendLine("2.如果大部分好友头像、昵称显示错误，请点击左上角【更新好友信息】。");
                    if ((exportType == ExportType.ExportToAll || exportType == ExportType.ExportToHtml) && !mIsLoadHeadIconFinish)
                    {
                        //如果是导出全部或者是导出html 需要判断下头像是不是加载完成
                        sb.AppendLine("3.好友头像尚未加载完成，当前导出为默认头像，如果需要导出完整");
                        sb.AppendLine("信息请稍后再试。");
                    }
                    bool isCheck = false;

                    DialogResult dr = tbMessageBox.Show(this, sb.ToString(), "提示",
                                                        MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1,
                                                        "不再提醒", ref isCheck, new string[] { "继续", "取消" });

                    IniSetting.SetWCAShow(!isCheck);
                    if (dr == DialogResult.Cancel)
                        return;
                }

                if (m_threadDown != null && (m_threadDown.IsAlive || m_threadDown.ThreadState != ThreadState.Stopped)) return;

                //List<DateTime>  lstTime = new List<DateTime>();
                //lstTime.Add(dtpStart.Value);
                //lstTime.Add(dtpEnd.Value);

                string account = Utility.ReplaceWinIllegalName(string.IsNullOrEmpty(waInfo.InnerAccount) ? waInfo.Account : waInfo.InnerAccount);
                this.mExportHelper.Export(lstRows,
                                        FilterDateTime(),
                                        account,
                                        btnWeCahtUsers.Text, exportType,
                                        new object[] { waInfo,
                                            mCurrentBackupDBPath,
                                            waInfo.Password,
                                            isPreview,
                                            mCurrentBackupMediaFile,
                                            mWeChatiOSDevice,
                                            ""//txtSearchChat.Text.Trim()                                             
                                        },
                                        waInfo.DicWeChatFriendInfo);

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "NewExport");
            }
        }

        private bool CheckExport(ref List<DataGridViewRow> lstRows)
        {
            bool isRelust = true;
            try
            {
                if (dgvFriend == null || dgvFriend.Rows.Count == 0)
                {
                    tbMessageBox.Show(this, "联系人为空。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }

                if (dgvFriend.SelectedRows.Count <= 0)
                {
                    tbMessageBox.Show(this, "请选择要导出的联系人", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
                lstRows = dgvFriend.SelectedRows;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CheckExport");
            }
            return isRelust;
        }

        private delegate void SetViewStyleHandler(MViewType style);
        private void SetViewStyle(MViewType style)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetViewStyleHandler(SetViewStyle), style);
            }
            else
            {
                try
                {
                    this.tblayoutMain.SuspendLayout();
                    switch (style)
                    {
                        case MViewType.Welcome:
                            if (this.tblayoutMain.ColumnStyles[0].Width != 100)
                            {
                                this.tblayoutMain.ColumnStyles[0].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[0].Width = 100;
                                this.tblayoutMain.ColumnStyles[1].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[1].Width = 0;
                                this.tblayoutMain.ColumnStyles[2].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[2].Width = 0;
                                this.tblayoutMain.ColumnStyles[3].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[3].Width = 0;
                                this.tblayoutMain.ColumnStyles[4].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[4].Width = 0;

                                this.pnlWelcome.Visible = true;
                                this.pnlWechatList.Visible = false;
                                this.pnlLoad.Visible = false;
                                this.pnlAccredit.Visible = false;

                                this.tbPanel12.Visible = false;
                                this.pnlTop.Visible = false;

                                this.tblayoutMain.RowStyles[1].SizeType = SizeType.Absolute;
                                this.tblayoutMain.RowStyles[1].Height = 34;

                                this.btnSetting.Visible = true;
                                this.SetRecommend(false);


                            }
                            break;

                        case MViewType.WechatList:
                            if (this.tblayoutMain.ColumnStyles[1].Width != 100)
                            {
                                this.tblayoutMain.ColumnStyles[0].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[0].Width = 0;
                                this.tblayoutMain.ColumnStyles[1].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[1].Width = 100;
                                this.tblayoutMain.ColumnStyles[2].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[2].Width = 0;
                                this.tblayoutMain.ColumnStyles[3].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[3].Width = 0;
                                this.tblayoutMain.ColumnStyles[4].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[4].Width = 0;

                                this.pnlWelcome.Visible = false;
                                this.pnlWechatList.Visible = true;
                                this.pnlLoad.Visible = false;
                                this.pnlAccredit.Visible = false;

                                this.tbPanel12.Visible = false;
                                this.pnlTop.Visible = false;

                                this.tblayoutMain.RowStyles[1].SizeType = SizeType.Absolute;
                                this.tblayoutMain.RowStyles[1].Height = 34;

                                this.btnSetting.Visible = true;
                                this.SetRecommend(false);
                                //this.txtWechatPath.Width = this.btnWechatPath.Left - this.txtWechatPath.Left - 10;
                            }
                            break;

                        case MViewType.ShowTalke:
                            if (this.tblayoutMain.ColumnStyles[2].Width != 100)
                            {
                                this.tblayoutMain.ColumnStyles[0].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[0].Width = 0;
                                this.tblayoutMain.ColumnStyles[1].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[1].Width = 0;
                                this.tblayoutMain.ColumnStyles[2].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[2].Width = 100;
                                this.tblayoutMain.ColumnStyles[3].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[3].Width = 0;
                                this.tblayoutMain.ColumnStyles[4].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[4].Width = 0;

                                this.pnlWelcome.Visible = false;
                                this.pnlWechatList.Visible = false;
                                this.pnlLoad.Visible = false;
                                this.pnlAccredit.Visible = false;

                                this.pnlTop.Visible = true;
                                this.tbPanel12.Visible = true;

                                this.tblayoutMain.RowStyles[1].SizeType = SizeType.Absolute;
                                this.tblayoutMain.RowStyles[1].Height = 34;
                                this.SetRecommend(true);
                            }
                            break;

                        case MViewType.Loading:
                            if (this.tblayoutMain.ColumnStyles[3].Width != 100)
                            {
                                this.tblayoutMain.ColumnStyles[0].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[0].Width = 0;
                                this.tblayoutMain.ColumnStyles[1].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[1].Width = 0;
                                this.tblayoutMain.ColumnStyles[2].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[2].Width = 0;
                                this.tblayoutMain.ColumnStyles[3].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[3].Width = 100;
                                this.tblayoutMain.ColumnStyles[4].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[4].Width = 0;

                                this.pnlWelcome.Visible = false;
                                this.pnlWechatList.Visible = false;
                                this.pnlLoad.Visible = true;
                                this.pnlAccredit.Visible = false;

                                this.tbPanel12.Visible = false;
                                this.pnlTop.Visible = false;

                                this.tblayoutMain.RowStyles[1].SizeType = SizeType.Absolute;
                                this.tblayoutMain.RowStyles[1].Height = 34;
                                this.btnSetting.Visible = true;
                                this.SetRecommend(false);
                            }
                            break;

                        case MViewType.Accredit:
                            if (this.tblayoutMain.ColumnStyles[4].Width != 100)
                            {
                                this.tblayoutMain.ColumnStyles[0].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[0].Width = 0;
                                this.tblayoutMain.ColumnStyles[1].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[1].Width = 0;
                                this.tblayoutMain.ColumnStyles[2].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[2].Width = 0;
                                this.tblayoutMain.ColumnStyles[3].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[3].Width = 0;
                                this.tblayoutMain.ColumnStyles[4].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[4].Width = 100;

                                this.pnlWelcome.Visible = false;
                                this.pnlWechatList.Visible = false;
                                this.pnlLoad.Visible = false;
                                this.pnlAccredit.Visible = true;

                                this.tbPanel12.Visible = false;
                                this.pnlTop.Visible = false;

                                this.tblayoutMain.RowStyles[1].SizeType = SizeType.Absolute;
                                this.tblayoutMain.RowStyles[1].Height = 34;
                                this.SetRecommend(false);

                                this.frmSetting_WeChatArtificialAccreditHandler(null, null);
                            }
                            break;
                    }
                    this.tblayoutMain.ResumeLayout(false);
                    this.tblayoutMain.PerformLayout();
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "SetViewStyle");
                }
                Application.DoEvents();

                if (pnlWechatList.Visible)
                {
                    InvokeOnUiThreadIfRequired(this, () =>
                    {
                        txtWechatPath.Width = btnWechatPath.Left - txtWechatPath.Left - 10;
                    });
                }
            }
        }

        private void SetRecommend(bool isShow)
        {
            mIsShowSetRecommend = isShow;

            try
            {
                lblRecommend.ColorTextList.Clear();
                lblRecommend.ColorTextList.AddItem("人工授权服务在线客服支持：周一至周五10：00-12：00  13：30-18：30   周六14：00-18：00 周日休息，法定节假日另行通知  ",
                                                    0, Color.FromArgb(128, 0, 0), mFontF, false);

                lblRecommend.TextAlign = ContentAlignment.TopCenter;
            }
            catch { }

            //try
            //{
            //    this.lblRecommend.TextClick -= lblRecommend_TextClick;
            //    this.lblRecommend.TextClick += lblRecommend_TextClick;
            //    this.lblRecommend.ColorTextList.Clear();
            //    this.lblRecommend.ColorTextList.AddItem("人工授权服务在线客服支持：周一至周五10：00-12：00  13：30-18：30   周六14：00-18：00 周日休息，法定节假日另行通知  ", 0, System.Drawing.Color.FromArgb(128, 0, 0), mFontF, false);

            //    if (mIsShowSetRecommend)
            //    {
            //        this.lblRecommend.ColorTextList.Clear();
            //        this.lblRecommend.ColorTextList.AddItem("                                        没找到聊天记录？可能被删除了！试试下载", 0, System.Drawing.Color.FromArgb(128, 0, 0), mFontF, false);
            //        this.lblRecommend.ColorTextList.AddItem(this.mCurrentRecommend, 1, System.Drawing.Color.FromArgb(52, 115, 255), mFontF, false, true);
            //        this.lblRecommend.ColorTextList.AddItem("进行恢复", 2, System.Drawing.Color.FromArgb(128, 0, 0), mFontF, false);
            //    }
            //    this.lblRecommend.TextAlign = ContentAlignment.TopCenter;
            //}
            //catch { }
        }

        private void CreateDeviceInfo2Form(WechatAccInfo waInfo)
        {
            try
            {
                ToolStripMenuItem menuInfoItem = null;
                BackConfigInfo info = null;

                //DateTime dtLast = DateTime.MinValue;

                this.mMenuDevice.Items.Clear();

                foreach (WeChatAccBackupInfo item in waInfo.LstWCABInfo)
                {
                    info = item.BCConfigInfo;
                    menuInfoItem = (ToolStripMenuItem)mMenuDevice.Items.Add(item.DeviceName, null);
                    menuInfoItem.Tag = item;// item.DeviceUDID;

                    //// 没赋值或者时间不一样，就给他赋值，为啥这样？ by zsh
                    //if (dtLast == DateTime.MinValue || DateTime.Compare(dtLast, WechatHeplerPC.ConvertWeixinToPcTime(info.BackupTime)) > 0)
                    //{
                    //    btnWeCahtUsers.Text = item.DeviceName;
                    //    btnWeCahtUsers.Tag = info;
                    //}
                    //dtLast = WechatHeplerPC.ConvertWeixinToPcTime(info.BackupTime);

                    if (waInfo.SelectedDeviceUDID.Length > 0 && waInfo.SelectedDeviceUDID == item.DeviceUDID)
                    {
                        this.btnWeCahtUsers.Text = item.DeviceName;
                        //this.btnWeCahtUsers.Tag = info;
                        this.mCurrentWeChatUsersMenuItem = menuInfoItem;
                        menuInfoItem.Checked = true;
                        string strOSType = item.BCConfigInfo.BackConfigList.OSType.ToString().ToLower();
                        if (strOSType == "android")
                        {
                            this.mCurrentRecommend = this.mStrWechatRecovery;
                            this.mWeChatiOSDevice = false;
                        }
                        else if (strOSType == "iphone os" || strOSType == "ios")
                        {
                            this.mCurrentRecommend = this.mStrWechatManager;
                            this.mWeChatiOSDevice = true;
                        }
                    }
                    else
                    {
                        menuInfoItem.Checked = false;
                    }

                    menuInfoItem.Click -= OnWeChatUsersMenuItemClick;
                    menuInfoItem.Click += OnWeChatUsersMenuItemClick;
                }

                //mMenuDevice.Closed -= OnWeChatUsersMenuClosed;
                //mMenuDevice.Closed += OnWeChatUsersMenuClosed;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateWeChatUsers");
            }
        }

        private object mCurrentWeChatUsersMenuItem = null;
        private void OnWeChatUsersMenuItemClick(object sender, EventArgs e)
        {
            WechatAccInfo waInfo = GetAcctInfo();
            if (waInfo == null) return;

            this.mCurrentWeChatUsersMenuItem = sender;
            this.txtSearchFriend.Text = string.Empty;
            //this.mCurrentWAInfo.SelectedDeviceUDID = ((ToolStripMenuItem)sender).Tag.ToString();
            WeChatAccBackupInfo wbinfo = (WeChatAccBackupInfo)((ToolStripMenuItem)sender).Tag;
            waInfo.SelectedDeviceUDID = wbinfo.DeviceUDID;
            IniSetting.SetDeviceUDID(waInfo.Account, "DeviceUDID", waInfo.SelectedDeviceUDID);
            //this.ShowAccountThread(this.mCurrentWAInfo);
            RunLoadFriendWorker(waInfo);
        }

        private delegate void DoSearchFriendHanlder(object strSearch);
        private void DoSearchFriend(object strSearch)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new DoSearchFriendHanlder(DoSearchFriend), new object[] { strSearch });
                }
                else
                {
                    Utility.WaitSeconds(0.3);
                    //速度太快，正在搜索的状态不明显
                    this.dgvFriend.ClearSelection();
                    this.SetRowVisible(strSearch.ToString());
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoSearchFriend");
            }
        }

        private void SetRowVisible(string strSearch)
        {
            strSearch = strSearch.ToLowerInvariant();
            int count = 0;

            foreach (DataGridViewRow Item in this.dgvFriend.Rows)
            {
                if (strSearch.Length == 0)
                {
                    Item.Visible = true;
                    count = count + 1;
                }
                else
                {
                    string strSoftName = (Item.Cells["colName"].Value + "").ToString().ToLower();
                    string strPinYin = PinYinClass.MakePinYin(strSoftName, PinYinOptions.Default).ToLower();
                    string strPinYinFirstChar = PinYinClass.MakePinYin(strSoftName, PinYinOptions.FirstCharacterOnly).ToLower();
                    if (strSoftName.Contains(strSearch) || strPinYin.Contains(strSearch) || strPinYinFirstChar.Contains(strSearch))
                    {
                        Item.Visible = true;
                        count = count + 1;
                    }
                    else
                    {
                        Item.Visible = false;
                    }
                }
            }

        }

        private delegate void LoadServerSettingHandler(object sender, EventArgs e);
        private void WechatHeplerPC_LoadServerSettingHandler(object sender, EventArgs e)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new LoadServerSettingHandler(WechatHeplerPC_LoadServerSettingHandler), new object[] { sender, e });
                }
                else
                {
                    if (!string.IsNullOrEmpty(WechatHeplerPC.mAssistInfo.StrVideoCourse))
                        this.btnToVideo.Visible = true;

                    if (!string.IsNullOrEmpty(WechatHeplerPC.mAssistInfo.StrLinkQQ))
                        this.btnContactEx.Visible = true;

                    if (WechatHeplerPC.mAssistInfo.IsVersionDisabled)
                    {
                        tbMessageBox.Show(this, "当前版本不可用，请去官网下载最新版本。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        this.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "WechatHeplerPC_LoadServerSettingHandler");
            }
        }

        private delegate void ShowSystemInfoOnPCHandler(bool blnVisible, string strText, bool isBackupFailure);
        private void ShowSystemInfoOnPC(bool blnVisible, string strText, bool isBackupFailure = false)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ShowSystemInfoOnPCHandler(ShowSystemInfoOnPC), blnVisible, strText, isBackupFailure);
            }
            else
            {
                if (isBackupFailure)
                {
                    this.lblSystemInfo.ForeColor = Color.FromArgb(5, 152, 242);
                    this.lblSystemInfo.Cursor = Cursors.Hand;
                }
                else
                {
                    this.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106);
                    this.lblSystemInfo.Cursor = Cursors.Default;
                }
                this.lblSystemInfo.Visible = blnVisible;
                this.lblSystemInfo.Text = strText;
                this.lblSystemInfo.BringToFront();

                // Added by Utmost20200327
                // 避免重新设置tbpanel3,cefwebbrowser控件大小 
                if (mCurrentDataGridViewRow == null)
                {
                    ShowPageControl(!blnVisible);
                }
            }
        }

        private delegate void ShowPageControlHandler(bool bVisible);
        private void ShowPageControl(bool bVisible)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ShowPageControlHandler(ShowPageControl), bVisible);
            }
            else
            {
                wbsChat.IsResized = !bVisible;

                pnlPage.Visible = bVisible;
                if (bVisible)
                    pnlPage.BringToFront();

            }
        }
        private void ScreenByTime(WechatTalk wTalk)
        {
            try
            {
                DateTime dtMessage = DateTime.Now;
                List<WechatMessageItem> listItems = new List<WechatMessageItem>();

                //WechatTalk tmpTalks = new WechatTalk();
                //tmpTalks.EndTime = wTalk.EndTime;
                //tmpTalks.FilePath = wTalk.FilePath;
                //tmpTalks.LastChatText = wTalk.LastChatText;
                //tmpTalks.Md5 = wTalk.Md5;
                //tmpTalks.NickName = wTalk.NickName;
                //tmpTalks.OffSet = wTalk.OffSet;
                //tmpTalks.PageTotal = wTalk.PageTotal;
                //tmpTalks.PageCount = wTalk.PageCount;
                //tmpTalks.StartTime = wTalk.StartTime;
                //tmpTalks.TalkId = wTalk.TalkId;
                //tmpTalks.UserName = wTalk.UserName;
                WechatTalk tmpTalks = wTalk.Clone();

                if (IniSetting.GetIsTalkSaveDB())
                {
                    WechatAccInfo waInfo = GetAcctInfo();
                    if (waInfo == null) return;

                    int intStartIndex = Convert.ToInt32((wTalk.PageIndex - 1) * wTalk.PageCount);
                    listItems = WeChatTalkDB.GetInstance(waInfo.StrWeChatTalkDBPath).GetTalkInfo(wTalk.Md5, wTalk.NickName, FilterDateTime(),
                                                                                                 txtSearchChat.Text, intStartIndex, (int)wTalk.PageCount);

                }
                else
                {
                    string dtmStart = dtpStart.Value.ToString("yyyy-MM-dd") + " " + dtpStartTime.Text;
                    DateTime dtStart = Convert.ToDateTime(dtmStart);

                    string dtmEnd = dtpEnd.Value.ToString("yyyy-MM-dd") + " " + dtpEndTime.Text;
                    DateTime dtEnd = Convert.ToDateTime(dtmEnd);

                    foreach (WechatMessageItem wItem in wTalk.MessageItems)
                    {
                        dtMessage = Convert.ToDateTime(wItem.MessageDT.ToString("yyyy-MM-dd HH:mm:ss"));
                        if (DateTime.Compare(dtStart, dtMessage) <= 0 && DateTime.Compare(dtEnd, dtMessage) >= 0)
                        {
                            listItems.Add(wItem);
                        }
                    }
                }
                tmpTalks.MessageItems = listItems;
                tmpTalks.PageIndex = 1;
                if (listItems.Count == 0)
                {
                    mCurrentDataGridViewRow = null;

                    this.SetLoadChat(false);
                    this.ShowSystemInfoOnPC(true, "搜索不到内容");
                }
                else
                {
                    tmpTalks.PageTotal = Math.Ceiling(Convert.ToDouble(listItems.Count / tmpTalks.PageCount));
                    this.LoadChat(tmpTalks);
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ScreenByTime");
            }
        }

        private void SetLinkmanMsg()
        {
            try
            {
                this.mIsSelectAllClick = true;
                if (!mIsAddRow && dgvFriend.Rows.Count > 0 && this.dgvFriend.SelectedRows != null && this.dgvFriend.SelectedRows.Count == this.dgvFriend.Rows.Count)
                    this.cbxSelectAll.Checked = true;
                else
                    this.cbxSelectAll.Checked = false;

                this.mIsSelectAllClick = false;

                this.lblLinkmanMsg.Text = String.Format("已选择 {0} 项，全部 {1} 项", this.dgvFriend.SelectedRows.Count, this.dgvFriend.Rows.Count);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SetLinkmanMsg");
            }
        }

        private delegate void ChangeRowTypeHandler(WechatAccInfo item, tbDataGridView dgvAcc);
        private void ChangeRowType(WechatAccInfo item, tbDataGridView dgvAcc = null)
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new ChangeRowTypeHandler(ChangeRowType), item, dgvAcc);
                }
                else
                {
                    tbDataGridViewImageCell colType = null;
                    tbDataGridViewCell colPCLimit = null;
                    if (dgvAcc != null)
                    {
                        foreach (DataGridViewRow row in dgvAcc.Rows)
                        {
                            colType = null;
                            colPCLimit = null;
                            if (row.Tag == null || row.Tag.GetType() != typeof(WechatAccInfo))
                                continue;

                            WechatAccInfo itemTemp = (WechatAccInfo)row.Tag;
                            colType = (tbDataGridViewImageCell)row.Cells["colType"];
                            colPCLimit = (tbDataGridViewCell)row.Cells["colPCLimit"];

                            this.SetRowType(colType, colPCLimit, itemTemp);
                        }
                    }
                    else
                    {
                        DataGridViewRow row = dgvAccount.SelectedRows[0];
                        colType = (tbDataGridViewImageCell)row.Cells["colType"];
                        colPCLimit = (tbDataGridViewCell)row.Cells["colPCLimit"];
                        this.SetRowType(colType, colPCLimit, item);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ChangeRowType");
            }
        }

        private void SetRowType(tbDataGridViewImageCell colType, tbDataGridViewCell colPCLimit, WechatAccInfo item)
        {
            try
            {
                colType.Value = this.GetActivateState(item);
                colPCLimit.Value = this.GetCombo(item);
                if (item.AState == ActivateState.Normal)
                    AccountIniSetting.DeleteBackupkey(item.Account);
            }
            catch { }
        }

        private object GetActivateState(WechatAccInfo item)
        {
            object img = global::iWechatAssistant.Properties.Resources.cell_false.Clone();
            try
            {
                if (item.Password.Length > 0)
                    img = global::iWechatAssistant.Properties.Resources.cell_true.Clone();
                else
                {
                    if (item.AState == ActivateState.Nonactivated)
                    {
                        string strKey = AccountIniSetting.GetBackupkey(item.Account);
                        if (strKey.Length > 0)
                        {
                            item.Password = strKey;
                            img = global::iWechatAssistant.Properties.Resources.cell_true.Clone();
                        }
                    }
                }
            }
            catch { }
            return img;
        }

        private string GetCombo(WechatAccInfo item)
        {
            string strReulst = "试用版";
            try
            {
                string strCombo = "试用版";
                if (item.TypeEx == 0)
                {
                    if (item.AState == ActivateState.Nonactivated)
                        strCombo = "试用版";
                    else
                        strCombo = "基础版";
                }
                else if (item.TypeEx == 1)
                    strCombo = "中级版";
                else if (item.TypeEx == 2)
                    strCombo = "高级版";

                string strOverdue = "";
                if (item.AState == ActivateState.Overdue)
                    strOverdue = "(已过期)";

                strReulst = string.Format("{0}{1}", strCombo, strOverdue);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetType");
            }
            return strReulst;
        }

        private DateTime GetStartTime()
        {
            string strStartTime = dtpStartTime.Text;
            if (string.IsNullOrEmpty(strStartTime))
                strStartTime = "00:00";
            string dtmStart = dtpStart.Value.ToString("yyyy-MM-dd") + " " + strStartTime;
            DateTime dtStart = Convert.ToDateTime(dtmStart);

            return dtStart;
        }

        private DateTime GetEndTime()
        {
            string strEndTime = dtpEndTime.Text;
            if (string.IsNullOrEmpty(strEndTime))
                strEndTime = "23:59";

            string dtmEnd = dtpEnd.Value.ToString("yyyy-MM-dd") + " " + strEndTime;
            DateTime dtEnd = Convert.ToDateTime(dtmEnd);

            return dtEnd;
        }

        private List<DateTime> FilterDateTime()
        {
            List<DateTime> listFilters = new List<DateTime>();

            listFilters.Add(GetStartTime());
            listFilters.Add(GetEndTime());

            return listFilters;
        }

        private delegate void SetLoadingMsgHandler(string strMsg);
        private void SetLoadingMsg(string strMsg)
        {
            try
            {
                if (this.InvokeRequired)
                    this.Invoke(new SetLoadingMsgHandler(SetLoadingMsg), strMsg);
                else
                {
                    this.lblMsg.Text = strMsg;

                    this.pbMsg.Location = new Point((this.pnlLoad.Width - this.pbMsg.Width - this.lblMsg.Width - 6) / 2, this.pbMsg.Location.Y);
                    this.lblMsg.Location = new Point(this.pbMsg.Location.X + this.pbMsg.Width + 3, this.lblMsg.Location.Y);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SetLoadingMsg");
            }
        }

        private void OpenURL(string strUrlResult)
        {
            string strChannel = System.Web.HttpUtility.UrlEncode(Common.GetChannelID());
            //和web约定，现在的链接中用os来代表渠道号，s就可以去掉了
            if (!strUrlResult.Contains(String.Format("c_s={0}", strChannel)))
            {
                if (strUrlResult.Contains("?"))
                    strUrlResult = String.Format("{0}&c_s={1}", strUrlResult, strChannel);
                else
                    strUrlResult = String.Format("{0}?c_s={1}", strUrlResult, strChannel);
            }
            Common.OpenExplorer(strUrlResult);
        }

        private void InvokeOnUiThreadIfRequired(Control control, Action action)
        {
            if (control.Disposing || control.IsDisposed || !control.IsHandleCreated)
            {
                return;
            }

            if (control.InvokeRequired)
            {
                control.BeginInvoke((Action)(() =>
                {
                    //No action
                    if (control.Disposing || control.IsDisposed || !control.IsHandleCreated)
                    {
                        return;
                    }

                    action();
                }));
            }
            else
            {
                action.Invoke();
            }
        }

        private delegate void ShowWebDetailHandler(bool isShow);
        private void ShowWebDetail(bool isShow)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ShowWebDetailHandler(ShowWebDetail), isShow);
            }
            else
            {
                try
                {
                    wbsChatDetail.Visible = isShow;
                    wbsChat.Visible = !isShow;
                    pnlPage.Visible = !isShow;

                    if (isShow)
                    {
                        wbsChatDetail.BringToFront();
                    }
                    else
                    {
                        wbsChat.IsResized = true;
                        wbsChat.BringToFront();
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "ShowWebDetail");
                }
            }
        }

        #endregion

        #region "--- 分页 ---"

        private void tbpPage_TrailerPageCallback(object sender, EventArgs e)
        {
            this.LoadPage("top");
        }

        private void tbpPage_NextPageCallback(object sender, EventArgs e)
        {
            this.LoadPage("top");
        }

        private void tbpPage_PreviousPageCallback(object sender, EventArgs e)
        {
            this.LoadPage("bottom");
        }

        private void tbpPage_HomePageCallback(object sender, EventArgs e)
        {
            this.LoadPage("bottom");
        }

        private void tbpPage_GotoPageCallback(object sender, EventArgs e)
        {
            this.LoadPage("top");
        }

        private void LoadPage(string strType)
        {
            this.SetLoadChat(true);
            this.ShowSystemInfoOnPC(false, "LoadPage to Log.");

            ChatWebPage.ClearChatOnWebPage(this.wbsChat);

            if (mCurrentDataGridViewRow != null)
                this.LoadChat(mCurrentDataGridViewRow, strType, "-1", Convert.ToInt32(this.tbpPage.CurrentPage));
        }

        #endregion


        //[DllImport("user32.dll", EntryPoint = "FindWindow", CharSet = CharSet.Auto)]
        //private extern static IntPtr FindWindow(string classname, string captionName);
        private void tbButton1_Click(object sender, EventArgs e)
        {
            IntPtr ip = NativeMethods.FindWindow("BackupWnd", null);
            if (ip == IntPtr.Zero)
            {
            }
            IntPtr ss = NativeMethods.FindWindow("WeChatMainWndForPC", null);
            if (ss == IntPtr.Zero)
            {
            }
        }

        private void lblRecommend_Click(object sender, EventArgs e)
        {
            if (lblRecommend.ColorTextList[0].mText.ToString().Contains("人工授权服务在线客服支持"))
            {
                frmService frm = new frmService();
                frm.ShowDialog();
            }
        }


    }

    public enum WebDataType
    {
        Normal = 0,         //普通展示
        GetNews = 1,        //查看更多消息
        Search = 2,         //搜索
        TimeLocate = 3,     //时间定位
        GetContextNews = 4,  //查看前后消息
        GetNewsBottom = 5    //向下滚动加载数据
    }

    public static class ChatWebPage
    {
        private static bool mHasInit = false;

        #region "---  属性  ---"

        private static string mWebPagePathDetail;
        public static string WebPagePathDetail
        {
            get { return mWebPagePathDetail; }
        }

        private static string mWebPagePath;
        public static string WebPagePath
        {
            get { return mWebPagePath; }
        }

        private static string mWebPagePopupPath;
        public static string WebPagePopupPath
        {
            get { return mWebPagePopupPath; }
        }

        #endregion

        #region "---  初始化  ---"

        //初始化网页，释放资源到本地（文件过多改为解压）
        public static bool InitChatWebPage(object objReload)
        {
            bool blnReload = (bool)objReload;
            if (mHasInit && !blnReload)
            {
                return true;
            }

            string folderHtml = Path.Combine(Folder.TempFolder, "ChatWebPage");
            try
            {
                Directory.Delete(folderHtml, true);
            }
            catch (Exception ex)
            {
            }

            Folder.CheckFolder(folderHtml);

            //解压网页
            string strFilePath = Path.Combine(folderHtml, "ChatWebPage.zip");
            ReleaseResource(strFilePath, iWechatAssistant.Properties.Resources.weixin_zip_ChatWebPage);

            if (Utility.unzip(strFilePath, folderHtml) == 0)
            {
                return false;
                //解压失败
            }

            //解压成功,聊天界面的地址
            mWebPagePath = Path.Combine(folderHtml, "wx\\index.html");
            mWebPagePathDetail = Path.Combine(folderHtml, "wx\\detail.html");
            //主页路径
            mWebPagePopupPath = Path.Combine(folderHtml, "push\\index.html");
            //主页路径
            //Push地址
            mHasInit = true;

            return true;
        }

        //Public Shared Sub InitChatWebPage(ByVal blnReload As Boolean)
        //    If mHasInit AndAlso Not blnReload Then
        //        Return
        //    End If

        //    Dim folderHtml As String = Path.Combine(Folder.TempFolder, "ChatWebPage\")
        //    Dim folderCss As String = Path.Combine(Folder.TempFolder, "ChatWebPage\css")
        //    Dim folderJs As String = Path.Combine(Folder.TempFolder, "ChatWebPage\js")
        //    Dim folderImages As String = Path.Combine(Folder.TempFolder, "ChatWebPage\images")
        //    'Dim folderImagesCloud As String = Path.Combine(Folder.CacheFolder, "ChatWebPage\web\images\cloud")

        //    Folder.CheckFolder(folderCss)
        //    Folder.CheckFolder(folderJs)
        //    Folder.CheckFolder(folderImages)
        //    'Folder.CheckFolder(folderImagesCloud)

        //    'If Directory.Exists(folderHtml) Then
        //    '    Try
        //    '        Directory.Delete(folderHtml, True)
        //    '    Catch
        //    '    End Try
        //    'End If

        //    Dim strFilePath As String = String.Empty

        //    strFilePath = Path.Combine(folderHtml, "index.html")
        //    ReleaseResource(strFilePath, My.Resources.weixin_html)
        //    mWebPagePath = strFilePath

        //    strFilePath = Path.Combine(folderCss, "main.css")
        //    ReleaseResource(strFilePath, My.Resources.weixin_css)

        //    strFilePath = Path.Combine(folderJs, "jquery1.7.min.js")
        //    ReleaseResource(strFilePath, My.Resources.weixin_js)

        //    strFilePath = Path.Combine(folderJs, "jquery.lazyload.js")
        //    ReleaseResource(strFilePath, My.Resources.weixin_js_lazyload)

        //    strFilePath = Path.Combine(folderImages, "cloud_img_body_white.png")
        //    ReleaseResource(strFilePath, My.Resources.weixin_png_body)

        //    strFilePath = Path.Combine(folderImages, "more.jpg")
        //    ReleaseResource(strFilePath, My.Resources.weixin_png_more)

        //    strFilePath = Path.Combine(folderImages, "icons.png")
        //    ReleaseResource(strFilePath, My.Resources.weixin_png_icons)

        //    strFilePath = Path.Combine(folderImages, "icons.gif")
        //    ReleaseResource(strFilePath, My.Resources.weixin_gif_icons)

        //    strFilePath = Path.Combine(folderImages, "voice_me.gif")
        //    ReleaseResource(strFilePath, My.Resources.weixin_gif_me)

        //    strFilePath = Path.Combine(folderImages, "voice_you.gif")
        //    ReleaseResource(strFilePath, My.Resources.weixin_gif_you)

        //    strFilePath = Path.Combine(folderImages, "default45x45.jpg")
        //    ReleaseResource(strFilePath, My.Resources.weixin_jpg_default45)

        //    strFilePath = Path.Combine(folderImages, "default365x185.jpg")
        //    ReleaseResource(strFilePath, My.Resources.weixin_jpg_default365)

        //    mHasInit = True
        //End Sub

        private static void ReleaseResource(string FilePath, byte[] Resource)
        {
            if (File.Exists(FilePath))
            {
                FileInfo fileInfo = new FileInfo(FilePath);

                if (fileInfo.Length != Resource.Length)
                {
                    try
                    {
                        File.Delete(FilePath);
                    }
                    catch
                    {
                    }
                }
            }

            if (!File.Exists(FilePath))
            {
                using (FileStream objWriter = new FileStream(FilePath, FileMode.OpenOrCreate, FileAccess.Write))
                {
                    objWriter.Write(Resource, 0, Resource.Length);
                }
            }
        }

        #endregion

        #region "---  js方法  ---"

        //展示聊天信息（新的数据，望下添加）
        //json格式
        //       from 信息来源 0：对方，1：自己
        //       type 信息分类 0：文字内容 ，1：语音，2：图片，3：网页
        //       user 用户名
        //       icon 用户头像地址
        //       content 内容
        //       time 聊天时间
        //举例
        //{"data":[{"from":"0","type":"0","user":"龙龙","icon":"C:\\Users\\<USER>