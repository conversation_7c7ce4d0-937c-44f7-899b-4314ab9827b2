﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Windows.Forms;

namespace iWechatAssistant
{
    static class Program
    {
        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [STAThread]
        static void Main()
        {
            //Application.EnableVisualStyles();
            //Application.SetCompatibleTextRenderingDefault(false);
            //Application.Run(new MainForm());

            // 保证单例
            bool createdNew; Mutex instance = new Mutex(true, "tb备份助手", out createdNew);
            if(createdNew)
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new MainForm());
                instance.ReleaseMutex();
            }
            else
            {
                Application.Exit();
            }
        }
    }
}
