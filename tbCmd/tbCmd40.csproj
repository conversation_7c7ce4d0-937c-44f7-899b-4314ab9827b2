﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2531A4CB-C43F-48AA-8532-1C4039675597}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>tbCmd40</RootNamespace>
    <AssemblyName>tbCmd40</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>TRACE;DEBUG;CMD;NET40;X64;</DefineConstants>
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>TRACE;DEBUG;CMD;NET40;X64;</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>TRACE;DEBUG;CMD;NET40;TONG;</DefineConstants>
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>TRACE;DEBUG;CMD;NET40;TONG;</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\AppUnion\zClass\DllImport.cs">
      <Link>Class\DllImport.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\WaveApi\API.cs">
      <Link>Class\API.cs</Link>
    </Compile>
    <Compile Include="..\CoreUtilCS\ClassDefine.cs">
      <Link>Class\ClassDefine.cs</Link>
    </Compile>
    <Compile Include="..\CoreUtilCS\RegistryKey64.cs">
      <Link>Class\RegistryKey64.cs</Link>
    </Compile>
    <Compile Include="Class\Common.cs" />
    <Compile Include="Class\CWeHelpAnalysis.cs" />
    <Compile Include="Class\Folder.cs" />
    <Compile Include="Class\Hash72.cs" />
    <Compile Include="Class\HashAB.cs" />
    <Compile Include="Class\IniClass.cs" />
    <Compile Include="Class\MemoryLibrary.cs" />
    <Compile Include="MemoryFile\MemoryLibraryEx.cs" />
    <Compile Include="MemoryFile\MemoryLibraryMember.cs" />
    <Compile Include="MemoryFile\MemoryLibraryStruct.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Library" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>