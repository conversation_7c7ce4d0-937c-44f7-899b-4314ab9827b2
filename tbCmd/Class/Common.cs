﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Security.Cryptography;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Management;
using System.Threading;

namespace iTong.CoreFoundation
{
    public class Utility
    {
        private static object mLocker = new object();

        private static bool mLogIsRunnig = false;
        private static Thread mThreadLog;
        private static Queue<string[]> mQueueLog = new Queue<string[]>();

        private static bool mCanLog = false;
        private static Dictionary<string, StringBuilder> mDictLog = new Dictionary<string, StringBuilder>();

        public static string LogPrefix { get; set; } = string.Empty;

        public static string[] GetOrSetLog(string[] arrPara, bool isClear)
        {
            string[] arrData = null;

            try
            {
                lock (mLocker)
                {
                    if (isClear)
                    {
                        mQueueLog.Clear();
                    }
                    else
                    {
                        if (arrPara != null && arrPara.Length > 0)
                        {
                            mQueueLog.Enqueue(arrPara);
                        }
                        else
                        {
                            if (mQueueLog.Count > 0)
                                arrData = mQueueLog.Dequeue();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }

            return arrData;
        }

        public static string LogData(string logDir)
        {
            string strLog = string.Empty;

            if (mDictLog.ContainsKey(logDir) && mDictLog[logDir] != null)
                strLog = mDictLog[logDir].ToString();

            return strLog;
        }

        public static void LogStart()
        {
            mCanLog = true;
        }

        public static void LogStop()
        {
            mCanLog = false;
        }

        public static void LogClear()
        {
            foreach (StringBuilder sb in mDictLog.Values)
            {
                sb.Length = 0;
            }
        }

        public static void LogClear(string dirLog)
        {
            if (!mDictLog.ContainsKey(dirLog))
                return;

            mDictLog[dirLog].Length = 0;
        }

        public static void LogExit()
        {
            GetOrSetLog(null, true);
            Utility.AbortThread(mThreadLog);
        }

        private static void WriteLog(object state)
        {
            try
            {
                int tryCount = 0;
                while (true)
                {
                    string[] arrPara = GetOrSetLog(null, false);
                    if (arrPara == null || arrPara.Length == 0)
                    {
                        tryCount++;

                        //连接10秒消息结束线程
                        if (tryCount > 1000)
                            break;

                        Thread.Sleep(10);
                        continue;
                    }

                    //有消息，重置计数器
                    tryCount = 0;

                    string strDir = Path.GetDirectoryName(arrPara[0]);
                    string strPath = arrPara[0];

                    Folder.CheckFolder(strDir);

                    //RS Host存在多进程，写日志容易出现文件相互占用
                    if (!string.IsNullOrEmpty(LogPrefix))
                    {
                        strPath = Path.Combine(strDir, string.Format("{0}_{1}", LogPrefix, Path.GetFileName(strPath)));
                    }

                    using (StreamWriter sw = new StreamWriter(strPath, true, Encoding.UTF8))
                        sw.WriteLine(arrPara[1]);

                    if (!mCanLog)
                        continue;

                    if (arrPara.Length == 3)
                    {
                        //异常错误输出
                        foreach (StringBuilder sb in mDictLog.Values)
                        {
                            if (sb.Length > 1 * 1024 * 1024)
                                sb.Length = 0;

                            sb.AppendLine(arrPara[1]);
                        }
                    }
                    else
                    {
                        if (!mDictLog.ContainsKey(strDir))
                            mDictLog[strDir] = new StringBuilder();

                        StringBuilder sb = mDictLog[strDir];

                        if (sb.Length > 1 * 1024 * 1024)
                            sb.Length = 0;

                        sb.AppendLine(arrPara[1]);
                    }
                }
            }
            catch
            { }
            finally
            {
                mLogIsRunnig = false;
            }
        }

        public static void CallWriteLog(string[] arrPara)
        {
            try
            {
                if (arrPara == null || arrPara.Length < 2)
                    return;

                GetOrSetLog(arrPara, false);

                if (mLogIsRunnig || Utility.CheckThreadIsAlive(mThreadLog))
                    return;

                mLogIsRunnig = true;

                mThreadLog = new Thread(new ParameterizedThreadStart(WriteLog));
                mThreadLog.Name = "Common.WriteLog";
                mThreadLog.IsBackground = true;
                mThreadLog.Start();
            }
            catch (Exception ex)
            {
                Utility.LogException(ex, "CallWriteLog");
            }
        }

        public static void LogException(string strText)
        {
            LogException(strText, "");
        }

        public static void LogException(string strPrint, string funcTitle)
        {
            try
            {
                if (string.IsNullOrEmpty(strPrint))
                    return;

                string txtPrint = string.Format("{0} {1}\r\n{2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.ffff"), funcTitle, strPrint);
                Console.WriteLine(txtPrint);

                if (Folder.AppType.ToString().Contains("AirDroid"))
                {
                    txtPrint = txtPrint.Replace("iTong.", Folder.AppType.ToString() + ".");

                    //if (!IsTestMode())
                    {
                        string strLowerText = txtPrint.ToLower();

                        //去除可能包含敏感信息的AirDroid日志
                        if (strLowerText.Contains("account") ||
                            strLowerText.Contains("token") ||
                            strLowerText.Contains("password") ||
                            strLowerText.Contains("pwd") ||
                            strLowerText.Contains("acct") ||
                            strLowerText.Contains("contact"))
                        {
                            string strIdentify = "?";
                            int iStart = strLowerText.IndexOf(strIdentify);
                            if (iStart < 0 || iStart > txtPrint.Length - 1)
                            {
                                return;
                            }

                            //如果是包含问号的， 则去掉参数，只保留问号前面的链接，方便追踪问题
                            txtPrint = txtPrint.Substring(0, iStart - 1);
                        }
                    }
                }

                string strFile = Path.Combine(Folder.ExceptionFolder, string.Format("Exception{0}.txt", System.DateTime.Now.ToString("yyyyMMdd")));

                CallWriteLog(new string[] { strFile, txtPrint + "\r\n", "" });
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        public static void LogException(Exception ex, string funcTitle = "")
        {
            if (ex == null)
                return;

            //if (ex is AggregateException)
            if (ex.GetType().Name.Contains("AggregateException"))
            {
                LogException(ex.ToString(), funcTitle);
                return;
            }

            StringBuilder sb = new StringBuilder(0xff);
            for (Exception inner = ex; inner != null; inner = inner.InnerException)
            {
                if (inner != ex)
                    sb.Append(Environment.NewLine).Append("===>");

                //if (inner is AggregateException)
                if (inner.GetType().Name.Contains("AggregateException"))
                {
                    sb.Append(inner);
                    break;
                }

                sb.Append(inner.GetType().FullName).Append(": ");
                sb.Append(inner.Message).Append(Environment.NewLine);
                sb.Append("StackTrace:");
                sb.Append(inner.StackTrace);
            }

            LogException(sb.ToString(), funcTitle);
        }

        private static object mLockerAbortThread = new object();
        private static Dictionary<int, DateTime> mDictAbortThreadHashCode = new Dictionary<int, DateTime>();

        public static bool CheckThreadIsAlive(Thread thd)
        {
            return !CheckThreadIsStop(thd);
        }

        public static bool CheckThreadIsStop(Thread thd)
        {
            bool blnResult = false;

            if (thd == null)
            {
                blnResult = true;
            }
            else
            {
                if ((thd.ThreadState & System.Threading.ThreadState.Stopped) == System.Threading.ThreadState.Stopped)
                {
                    blnResult = true;
                    goto DoExit;
                }

#if NET40 || MAC || IOS
                if ((thd.ThreadState & System.Threading.ThreadState.Aborted) == System.Threading.ThreadState.Aborted)
                {
                    blnResult = true;
                    goto DoExit;
                }

                if ((thd.ThreadState & System.Threading.ThreadState.AbortRequested) == System.Threading.ThreadState.AbortRequested)
                {
                    blnResult = true;
                    goto DoExit;
                }

                if ((thd.ThreadState & System.Threading.ThreadState.StopRequested) == System.Threading.ThreadState.StopRequested)
                {
                    blnResult = true;
                    goto DoExit;
                }
#endif

                if (thd.IsAlive)
                    blnResult = false;
            }

            DoExit:
            return blnResult;
        }

        public static void AbortThread(Thread thd)
        {
            lock (mLockerAbortThread)
            {
                if (CheckThreadIsStop(thd))
                    return;

                try
                {
                    int hashCode = thd.GetHashCode();
                    if (mDictAbortThreadHashCode.ContainsKey(hashCode) && DateTime.Now.Subtract(mDictAbortThreadHashCode[hashCode]).TotalSeconds < 30)
                        return;

                    mDictAbortThreadHashCode[hashCode] = DateTime.Now;

                    if (mDictAbortThreadHashCode.Count > 10)
                    {
                        List<int> listDel = new List<int>();
                        foreach (KeyValuePair<int, DateTime> item in mDictAbortThreadHashCode)
                        {
                            if (DateTime.Now.Subtract(mDictAbortThreadHashCode[hashCode]).TotalSeconds > 30)
                                listDel.Add(item.Key);
                        }

                        foreach (int item in listDel)
                        {
                            mDictAbortThreadHashCode.Remove(item);
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                }
            }

            try
            {
                if (thd != null)
                    thd.Abort();
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        public static string ReplaceWinIllegalNameToHtml(string strName)
        {
            if (string.IsNullOrEmpty(strName))
            {
                return string.Empty;
            }
            else
            {
                strName = strName.Replace("\\", "_")
                            .Replace("\"", "_")
                            .Replace("/", "_")
                            .Replace(":", "_")
                            .Replace("*", "_")
                            .Replace("?", "_")
                            .Replace("\"", "_")
                            .Replace("<", "_")
                            .Replace(">", "_")
                            .Replace("|", "_")
                            .Replace(Convert.ToChar(160).ToString(), "_")
                            .Replace("\t", "_")
                            .Replace("\n", "_")
                            .Replace("\r", "_")
                            .Replace("%", "_")
                            .Replace("#", "_");

                return strName;
            }
        }

        public static string ReplaceWinIllegalName(string strName)
        {
            return ReplaceWinIllegalName(strName, false);
        }

        //获取正确的Windows文件命名，替换文件名(不包含路径)中的非法字符为"_"
        public static string ReplaceWinIllegalName(string strName, bool blnReplaceDot)
        {
            if (string.IsNullOrEmpty(strName))
            {
                return string.Empty;
            }
            else
            {
#if MAC
				// Mac文件名含"、‘、?、/在html链接中失败
				// .字符开始的文件为隐藏文件,  "和',#,?字符开始的文件链接失败
                // ;字符在ie，safari浏览器链接失败
				strName = strName.Replace("/", "_")
								 .Replace(":", "_")
								 .Replace("\t", "_")
								 .Replace("\n", "_")
								 .Replace("\r", "_")
								 .Replace("\0", "_")
								 .Replace("\b", "_")
								 .Replace("©", "_")
								 .Replace("®", "_")
								 .Replace("™", "_")
								 .Replace("℠", "_")
								 .Replace("$", "_")
								 .Replace("\"", "_")
								 .Replace("\'", "_")
								 .Replace("#", "_")
								 .Replace("?", "_")
                                 .Replace(";", "_");

				if (blnReplaceDot)
					strName = strName.Replace(".", "_");

#else
                strName = strName.Replace("\\", "_")
                            .Replace("\"", "_")
                            .Replace("/", "_")
                            .Replace(":", "_")
                            .Replace("*", "_")
                            .Replace("?", "_")
                            .Replace("\"", "_")
                            .Replace("<", "_")
                            .Replace(">", "_")
                            .Replace("|", "_")
                            .Replace(Convert.ToChar(160).ToString(), "_")
                            .Replace("\t", "_")
                            .Replace("\n", "_")
                            .Replace("\r", "_")
                            .Replace("", "_");         //此处有不可字符，不可删除此替换
#endif

                return strName;
            }
        }

        // 获取正确的Windows路径，替换路径中的非法字符为"_"
        public static string ReplaceWinIllegalPath(string strPath)
        {
            if (string.IsNullOrEmpty(strPath))
            {
                return string.Empty;
            }

            string[] pathArr = strPath.Split(Path.DirectorySeparatorChar);

            for (int i = 1; i <= pathArr.Length - 1; i++)
            {
                pathArr[i] = ReplaceWinIllegalName(pathArr[i], false);
            }

            return string.Join(Path.DirectorySeparatorChar.ToString(), pathArr);
        }

        #region "--- IO操作 ---"

        public static void DeleteFile(string strFile)
        {
            try
            {
                if (File.Exists(strFile))
                    File.Delete(strFile);
            }
            catch (Exception ex)
            {
                LogException(ex.ToString(), "DeleteFile");
            }
        }

        public static void DeleteDirectory(string strDir)
        {
            try
            {
                if (Directory.Exists(strDir))
                    Directory.Delete(strDir, true);
            }
            catch (Exception ex)
            {
                LogException(ex.ToString(), "DeleteDirectory");
            }
        }

        /// <summary>
        /// 移动文件夹，作用于一些有删除权限的文件夹，没移动权限的文件夹的操作
        /// </summary>
        /// <param name="strDir"></param>
        /// <param name="destDir"></param>
        public static void MoveDirectory(string strDir, string destDir)
        {
            try
            {
                if (!Directory.Exists(strDir))
                    return;

                if (!Directory.Exists(destDir))
                    Folder.CheckFolder(destDir);

                //移动文件
                foreach (string filePath in Directory.GetFiles(strDir, "*.*", SearchOption.TopDirectoryOnly))
                {
                    string fileName = Path.GetFileName(filePath);
                    string destFilePath = Path.Combine(destDir, fileName);
                    try
                    {
                        File.Move(filePath, destFilePath);
                    }
                    catch (Exception ex)
                    {
                        LogException(ex.ToString(), "MoveDirectory");
                    }
                }

                //移动文件夹
                foreach (string dirPath in Directory.GetDirectories(strDir))
                {
                    string dirName = Path.GetFileName(dirPath);
                    string destDirPath = Path.Combine(destDir, dirName);

                    MoveDirectory(dirPath, destDirPath);
                }
            }
            catch (Exception ex)
            {
                LogException(ex.ToString(), "MoveDirectory");
            }
        }

        #endregion

        //通过版本字符串创建一个 Version 对象
        public static Version CreateVersion(string strVersion)
        {
            string strVer = "";
            foreach (char c in strVersion.Replace(",", ".").ToCharArray())
            {
                if (c >= 48 && c <= 57 || c == '.')
                {
                    strVer += c.ToString();
                }
            }
            if (strVer.Length == 0)
            {
                strVer = "1.0.0.0";
            }

            return new Version(strVer);
        }

        public static Version GetFileVersion(string strFile)
        {
            string strVer = string.Empty;

            if (File.Exists(strFile))
                strVer = FileVersionInfo.GetVersionInfo(strFile).FileVersion;

            return CreateVersion(strVer);
        }

        /// <summary>
        /// 获取文件的Hash值
        /// </summary>
        /// <param name="filePath">文件</param>
        /// <returns>Hash值</returns>
        public static string GetMd5Base64FromFile(string filePath)
        {
            string strMd5 = string.Empty;

            byte[] arrHash = GetMd5ArrayFormFile(filePath);
            if (arrHash != null)
                strMd5 = Convert.ToBase64String(arrHash);

            return strMd5;
        }

        /// <summary>
        /// 获取文件的Hash值
        /// </summary>
        /// <param name="filePath">文件</param>
        /// <returns>Hash值</returns>
        public static string GetMd5Base64(byte[] arrData)
        {
            string strMd5 = string.Empty;

            byte[] arrHash = GetMd5Array(arrData);
            if (arrHash != null)
                strMd5 = Convert.ToBase64String(arrHash);

            return strMd5;
        }

        public static string GetMd5HexFromFile(string filePath)
        {
            string strMd5 = string.Empty;

            byte[] arrHash = GetMd5ArrayFormFile(filePath);
            if (arrHash != null)
                strMd5 = ToHexString(arrHash);

            return strMd5;
        }

        public static byte[] GetMd5ArrayFormFile(string filePath)
        {
            byte[] arrHash = null;

            if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                goto DoExit;

#if !MAC && !IOS
            bool blnUseAPI = false;
            if (filePath.StartsWith(@"\\"))
                blnUseAPI = true;
#endif
            string strMd5 = string.Empty;

            try
            {
                using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read, FileShare.Read))
                {
                    System.Security.Cryptography.MD5CryptoServiceProvider md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
                    arrHash = md5.ComputeHash(fs);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetHashHex");
            }

            DoExit:
            return arrHash;
        }

        public static byte[] GetMd5Array(string text)
        {
            return GetMd5Array(Encoding.UTF8.GetBytes(text));
        }

        public static byte[] GetMd5Array(byte[] arrData)
        {
            byte[] arrHash = null;

            try
            {
                System.Security.Cryptography.MD5CryptoServiceProvider md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
                arrHash = md5.ComputeHash(arrData);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetMd5Array");
            }

            return arrHash;
        }

        public static string GetMd5Hex(string text)
        {
            return GetMd5Hex(Encoding.UTF8.GetBytes(text));
        }

        public static string GetMd5Hex(byte[] buffer)
        {
            return ToHexString(GetMd5Array(buffer));
        }

        public static byte[] GetSha1Array(string text)
        {
            return GetSha1Array(Encoding.UTF8.GetBytes(text));
        }

        public static byte[] GetSha1Array(byte[] buffer)
        {
            System.Security.Cryptography.SHA1CryptoServiceProvider md5 = new System.Security.Cryptography.SHA1CryptoServiceProvider();
            byte[] bytHashFile = md5.ComputeHash(buffer);
            md5.Clear();
            return bytHashFile;
        }

        public static string GetSha1HexFromFile(string filePath)
        {
            byte[] arrHash = GetSha1ArrayFromFile(filePath);

            if (arrHash == null)
                return string.Empty;
            else
                return ToHexString(arrHash);
        }

        public static byte[] GetSha1ArrayFromFile(string filePath)
        {
            byte[] arrHash = null;

            if (File.Exists(filePath))
            {
                FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                System.Security.Cryptography.SHA1CryptoServiceProvider hash = new System.Security.Cryptography.SHA1CryptoServiceProvider();
                arrHash = hash.ComputeHash(fs);
                fs.Close();
            }

            return arrHash;
        }

        public static string GetSha1Hex(string text)
        {
            return GetSha1Hex(Encoding.UTF8.GetBytes(text));
        }

        public static string GetSha1Hex(byte[] buffer)
        {
            return ToHexString(GetSha1Array(buffer));
        }

#if MAC || IOS || TOOLS || NET40
        public static byte[] GetSha256Array(string text)
        {
            return GetSha256Array(Encoding.UTF8.GetBytes(text));
        }

        public static byte[] GetSha256Array(byte[] buffer)
        {
            System.Security.Cryptography.SHA256CryptoServiceProvider md5 = new System.Security.Cryptography.SHA256CryptoServiceProvider();
            byte[] bytHashFile = md5.ComputeHash(buffer);
            md5.Clear();
            return bytHashFile;
        }

        public static string GetSha256HexFromFile(string filePath)
        {
            byte[] arrHash = GetSha256ArrayFromFile(filePath);

            if (arrHash == null)
                return string.Empty;
            else
                return ToHexString(arrHash);
        }

        public static byte[] GetSha256ArrayFromFile(string filePath)
        {
            byte[] arrHash = null;

            if (File.Exists(filePath))
            {
                FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read);
                System.Security.Cryptography.SHA256CryptoServiceProvider hash = new System.Security.Cryptography.SHA256CryptoServiceProvider();
                arrHash = hash.ComputeHash(fs);
                fs.Close();
            }

            return arrHash;
        }

        public static string GetSha256Hex(string text)
        {
            return GetSha256Hex(Encoding.UTF8.GetBytes(text));
        }

        public static string GetSha256Hex(byte[] buffer)
        {
            return ToHexString(GetSha256Array(buffer));
        }
#endif

        public static string GetHashString(string strContent)
        {
            System.Security.Cryptography.MD5CryptoServiceProvider md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();
            byte[] bytHashFile = md5.ComputeHash(System.Text.Encoding.UTF8.GetBytes(strContent));
            return ToHexString(bytHashFile);
        }

        public static string ToHexString(byte[] arrData)
        {
            StringBuilder sb = new StringBuilder();

            if (arrData != null)
            {
                foreach (byte byt in arrData)
                    sb.Append(Convert.ToString(byt, 16).PadLeft(2, '0'));
            }

            return sb.ToString();
        }

        public static string ToHexString(byte[] arrData, int index, int count)
        {
            StringBuilder sb = new StringBuilder();

            if (arrData != null && index > 0 && count > 0 && index + count < arrData.Length)
            {
                for (int intI = 0; intI < count; intI++)
                    sb.Append(Convert.ToString(arrData[index + intI], 16).PadLeft(2, '0'));
            }

            return sb.ToString();
        }

        public static byte[] FromHexString(string strData)
        {
            byte[] arrDate = null;

            if (!string.IsNullOrEmpty(strData))
            {
                int len = (int)Math.Floor(strData.Length / 2.0);
                arrDate = new byte[len];

                for (int index = 0; index < len; index++)
                {
                    arrDate[index] = Convert.ToByte(strData.Substring(index * 2, 2), 16);
                }
            }

            return arrDate;
        }

        public static void CallCmdExe(string[] arrArgs)
        {
            CallCmdExe(arrArgs, 0, string.Empty);
        }

        public static void CallCmdExe(string[] arrArgs, int waitSecond)
        {
            CallCmdExe(arrArgs, waitSecond, string.Empty);
        }

        public static void CallCmdExe(string[] arrArgs, int waitSecond, string pathExe)
        {
            CallCmdExe(arrArgs, waitSecond, pathExe, true, false);
        }

        public static void CallCmdExe(string[] arrArgs, int waitSecond, string pathExe, bool waitForExit)
        {
            CallCmdExe(arrArgs, waitSecond, pathExe, waitForExit, false);
        }

        public static void CallCmdExe(string[] arrArgs, int waitSecond, string pathExe, bool waitForExit, bool verb)
        {
            try
            {
                ProcessStartInfo pInfo = new ProcessStartInfo();

                if (string.IsNullOrEmpty(pathExe))
                    pathExe = Path.Combine(Folder.AppFolder, @"Codes\tbCmd.exe");

                if (!File.Exists(pathExe))
                    pathExe = Path.Combine(Folder.AppFolder, @"tbCmd40.exe");

                if (!File.Exists(pathExe))
                    pathExe = Path.Combine(Folder.AppFolder, @"tbCmd.exe");

                if (!File.Exists(pathExe))
                    pathExe = Path.Combine(Folder.AppFolder, @"Helper.exe");

                pInfo.FileName = pathExe;


                pInfo.ErrorDialog = false;
                pInfo.CreateNoWindow = true;
                pInfo.UseShellExecute = false;
                pInfo.RedirectStandardError = true;

                //请求管理员权限
                if (verb)
                {
                    pInfo.CreateNoWindow = false;
                    pInfo.Verb = "runas";
                }

                StringBuilder sb = new StringBuilder();
                foreach (string strArgs in arrArgs)
                    sb.Append(string.Format("\"{0}\" ", strArgs));
                pInfo.Arguments = sb.ToString().TrimEnd(' ');


                using (Process p = new Process())
                {
                    p.StartInfo = pInfo;
                    p.Start();

                    if (waitForExit)
                    {
                        if (waitSecond > 0)
                            p.WaitForExit(waitSecond * 1000);
                        else
                            p.WaitForExit();
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CallCmdExe");
            }
        }
    }

    public class Common : Utility
    {
        private static bool mVerIs30 = false;
        private static bool mPrintLog = false;

        static Common()
        {
            try
            {
                //File.Exists(Path.Combine(Folder.AppFolder, "3.dll")) ||
                mVerIs30 = System.Diagnostics.FileVersionInfo.GetVersionInfo(Folder.AppFile).FileMajorPart >= 3;
                mPrintLog = File.Exists(Path.Combine(Folder.AppFolder, "Test.dll")) || File.Exists(Path.Combine(Folder.AppFolder, "Debug.dll"));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.Print(ex.ToString());
            }
        }

        //记录日志
        public static void Log(string strLog)
        {
            Common.Log(strLog, string.Empty, false, string.Empty);
        }

        public static void Log(string strLog, bool blnAlwaysOutputLog)
        {
            Common.Log(strLog, string.Empty, blnAlwaysOutputLog, string.Empty);
        }

        public static void Log(string strLog, string strPreview)
        {
            Common.Log(strLog, strPreview, false, string.Empty);
        }

        public static void Log(string strLog, string strPreview, bool blnAlwaysOutputLog)
        {
            Common.Log(strLog, strPreview, blnAlwaysOutputLog, string.Empty, true);
        }

        public static void Log(string strLog, string strPreview, bool blnAlwaysOutputLog, bool printLogOnConsole)
        {
            Common.Log(strLog, strPreview, blnAlwaysOutputLog, string.Empty, printLogOnConsole);
        }

        public static void Log(string strLog, string strPreview, bool blnAlwaysOutputLog, string logDir)
        {
            Common.Log(strLog, strPreview, blnAlwaysOutputLog, logDir, true);
        }

        public static void Log(string strLog, string strPreview, bool blnAlwaysOutputLog, string logDir, bool printLogOnConsole)
        {
            Common.Log(strLog, strPreview, blnAlwaysOutputLog, logDir, printLogOnConsole, true);
        }

        public static void Log(string strLog, string strPreview, bool blnAlwaysOutputLog, string logDir, bool printLogOnConsole, bool appendDateTimeInFileName)
        {
            Common.Log(strLog, strPreview, blnAlwaysOutputLog, logDir, printLogOnConsole, appendDateTimeInFileName, true);
        }

        public static void Log(string strLog, string strPreview, bool blnAlwaysOutputLog, string logDir, bool printLogOnConsole, bool appendDateTimeInFileName, bool appendTime)
        {
            string txtPrint = appendTime ? string.Format("{0} {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.ffff"), strLog) : strLog;
            if (blnAlwaysOutputLog)
            {
                if (printLogOnConsole)
                    Console.WriteLine(txtPrint);

                if (!blnAlwaysOutputLog)
                    return;
            }

            //不打印日志
            if (!blnAlwaysOutputLog && !mPrintLog)
                return;

            //if (!string.IsNullOrEmpty(txtPrint) && Folder.AppType.ToString().Contains("AirDroid") && !IsFullLogMode())
            //{
            //    string strLowerText = txtPrint.ToLower();

            //    //去除可能包含敏感信息的AirDroid日志
            //    if (strLowerText.Contains("account") ||
            //        strLowerText.Contains("token") ||
            //        strLowerText.Contains("password") ||
            //        strLowerText.Contains("pwd") ||
            //        strLowerText.Contains("acct") ||
            //        strLowerText.Contains("contact"))
            //    {
            //        string strIdentify = "?";
            //        int iStart = strLowerText.IndexOf(strIdentify);
            //        if (iStart < 0 || iStart > txtPrint.Length - 2)
            //        {
            //            return;
            //        }

            //        //如果是包含问号的， 则去掉参数，只保留问号前面的链接，方便追踪问题
            //        txtPrint = txtPrint.Substring(0, iStart + 1);
            //    }
            //}

            if (string.IsNullOrEmpty(logDir))
                logDir = Folder.LogFolder;

            if (string.IsNullOrEmpty(strPreview))
                strPreview = "Log";

            string strFile = Path.Combine(logDir, strPreview + (appendDateTimeInFileName ? DateTime.Now.ToString("yyyyMMdd") : "")) + ".txt";

            Utility.CallWriteLog(new string[] { strFile, txtPrint });
        }

        public static void WriteLine(string msg)
        {
            WriteLine(msg, string.Empty, true);
        }

        public static void WriteLine(string msg, string fileName)
        {
            WriteLine(msg, fileName, true);
        }

        public static void WriteLine(string msg, string fileName, bool printStack)
        {
            try
            {
                string strDir = Folder.LogFolder;
                string strPrefix = LogPrefix;
                if (!string.IsNullOrEmpty(strPrefix))
                    strPrefix += "_";

                string strPath = Path.Combine(strDir, string.Format("{0}{1}{2}.txt", strPrefix, fileName, DateTime.Now.ToString("yyyyMMdd")));

                Folder.CheckFolder(strDir);

                if (printStack)
                {
                    StackTrace st = new StackTrace();

                    StringBuilder sbStack = new StringBuilder();
                    sbStack.AppendLine(msg);

                    for (int index = 0; index < st.FrameCount; index++)
                    {
                        StackFrame sf = st.GetFrame(index);
                        if (sf != null)
                        {
                            System.Reflection.MethodBase method = sf.GetMethod();
                            sbStack.AppendLine(method.DeclaringType.FullName + "." + method.Name);
                        }
                    }
                    msg = sbStack.ToString();
                }

                DateTime dt = DateTime.Now;
                string strPrint = string.Format("{0} {1}", dt.ToString("yyyy-MM-dd HH:mm:ss.ffff"), msg);

                using (StreamWriter sw = new StreamWriter(strPath, true, Encoding.UTF8))
                    sw.WriteLine(strPrint);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }
        }

        #region ---- IntPtr操作 ----

        public static IntPtr CreateIntPtr(IntPtr hSrc, long offset)
        {
            IntPtr hNew = IntPtr.Zero;

            try
            {
                if (Common.IsX64)
                {
                    hNew = new IntPtr(hSrc.ToInt64() + offset);
                }
                else
                {
                    hNew = new IntPtr(hSrc.ToInt32() + (int)offset);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateIntPtr");
            }

            return hNew;
        }

        public static IntPtr IntPtrNew(long lngValue)
        {
            IntPtr hNew = IntPtr.Zero;

            try
            {
                if (Common.IsX64)
                {
                    hNew = new IntPtr(lngValue);
                }
                else
                {
                    hNew = new IntPtr((int)lngValue);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "IntPtrNew");
            }

            return hNew;
        }

        public static IntPtr IntPtrAllocate()
        {
            return IntPtrAllocate(IntPtr.Size);
        }

        public static IntPtr IntPtrAllocate(int allocateSize)
        {
            IntPtr hNew = IntPtr.Zero;

            try
            {
                hNew = Marshal.AllocCoTaskMem(allocateSize);
                ClearMemory(hNew, allocateSize);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "IntPtrAllocate");
            }

            return hNew;
        }

        public static string IntPtrReadAsString(IntPtr hData, long lngSize, Encoding encoding)
        {
            string strText = string.Empty;

            byte[] arrData = IntPtrRead(hData, lngSize);
            if (arrData != null || arrData.Length > 0)
                strText = encoding.GetString(arrData);

            return strText;
        }


        public static string IntPtrReadAsHex(IntPtr hData, long lngSize)
        {
            string strText = string.Empty;

            byte[] arrData = IntPtrRead(hData, lngSize);
            if (arrData != null || arrData.Length > 0)
                strText = ToHexString(arrData);

            return strText;
        }

        public static byte[] IntPtrRead(IntPtr hData, long lngSize)
        {
            byte[] arrData = null;

            try
            {
                if (hData == IntPtr.Zero)
                    goto DoExit;

                if (lngSize > 0)
                {
                    arrData = new byte[lngSize];
                    Marshal.Copy(hData, arrData, 0, arrData.Length);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "IntPtrRead");
            }

            DoExit:
            return arrData;
        }

        public static byte[] IntPtrRead(IntPtr hData, IntPtr hDataSize)
        {
            byte[] arrData = null;

            try
            {
                if (hDataSize == IntPtr.Zero)
                    goto DoExit;

                long lngSize = 0;
                if (Common.IsX64)
                    lngSize = Marshal.ReadInt64(hDataSize);
                else
                    lngSize = Marshal.ReadInt32(hDataSize);

                arrData = IntPtrRead(hData, lngSize);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "IntPtrRead");
            }

            DoExit:
            return arrData;
        }

        public static void ClearMemory(IntPtr hData)
        {
            ClearMemory(hData, IntPtr.Size);
        }

        public static void ClearMemory(IntPtr hData, int dataLen)
        {
            byte[] arrZero = new byte[dataLen];

            for (int intI = 0; intI < dataLen; intI++)
                arrZero[intI] = 0;

            Marshal.Copy(arrZero, 0, hData, dataLen);
        }

        #endregion


#if !IOS && !MAC
        [DllImport("kernel32.dll", SetLastError = true, CallingConvention = CallingConvention.Winapi)]
        [return: MarshalAs(UnmanagedType.Bool)]
        private static extern bool IsWow64Process([In] IntPtr hProcess, [Out] out bool lpSystemInfo);
        private static bool InternalCheckIsWow64()
        {
            bool retVal = false;

            try
            {
                if ((Environment.OSVersion.Version.Major == 5 && Environment.OSVersion.Version.Minor >= 1) ||
                     Environment.OSVersion.Version.Major >= 6)
                {
                    Process curProcess = Process.GetCurrentProcess();
                    IsWow64Process(curProcess.Handle, out retVal);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "InternalCheckIsWow64");
            }

            return retVal;
        }
#endif

        public static bool IsX64
        {
            get { return IntPtr.Size == 8; }
        }

        /// <summary>
        /// 判断是否是64位操作系统
        /// </summary>
        /// <returns></returns>
        private static string _AddressWidth = string.Empty;
        public static bool IsOS_Of_64Bit()
        {
#if MAC || IOS
			return Common.IsX64;
#else
            try
            {
                if (Common.IsX64)
                    return true;

                if (string.IsNullOrEmpty(_AddressWidth))
                {
                    ConnectionOptions connection = new ConnectionOptions();
                    ManagementScope scope = new ManagementScope(@"\\localhost", connection);
                    ObjectQuery query = new ObjectQuery("select AddressWidth from Win32_Processor");
                    using (ManagementObjectSearcher seacher = new ManagementObjectSearcher(scope, query))
                    {
                        using (ManagementObjectCollection collInfo = seacher.Get())
                        {
                            foreach (ManagementObject obj in collInfo)
                            {
                                _AddressWidth = obj["AddressWidth"].ToString();
                                obj.Dispose();
                            }
                        }
                    }
                }

                if (_AddressWidth == "64")
                    return true;
                else
                    return false;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "IsOS_Of_64Bit");
                if (InternalCheckIsWow64())
                {
                    _AddressWidth = "64";
                    return true;
                }
                else
                {
                    _AddressWidth = "32";
                    return false;
                }
            }
#endif
        }

        public static string RunCmdExe(string pathExe, params string[] arrArgs)
        {
            string strLog = string.Empty;

            try
            {
                Process p = new Process();
                ProcessStartInfo pInfo = new ProcessStartInfo();
                pInfo.FileName = pathExe;


                pInfo.ErrorDialog = false;
                pInfo.CreateNoWindow = true;
                pInfo.UseShellExecute = false;
                pInfo.RedirectStandardError = true;
                pInfo.RedirectStandardOutput = true;

                StringBuilder sb = new StringBuilder();
                foreach (string strArgs in arrArgs)
                    sb.Append(string.Format("\"{0}\" ", strArgs));
                pInfo.Arguments = sb.ToString().TrimEnd(' ');

                p.StartInfo = pInfo;
                p.Start();

                strLog = p.StandardOutput.ReadToEnd();
            }
            catch (Exception ex)
            {
                strLog = ex.Message;
            }

            return strLog;
        }

        public static string GetNumber(string strValue)
        {
            return GetNumber(strValue, true);
        }

        public static string GetNumber(string strValue, bool includeDot)
        {
            return GetNumber(strValue, includeDot, false);
        }

        public static string GetNumber(string strValue, bool includeDot, bool includePlus)
        {
            if (string.IsNullOrEmpty(strValue))
                return string.Empty;

            string strReturn = string.Empty;
            foreach (char c in strValue)
            {
                //c>=0 && c<=9 || c==.
                if (c >= 48 && c <= 57 || includeDot && c == 46 || c == '-' || includePlus && c == '+')
                    strReturn += c.ToString();
            }

            return strReturn;
        }

        public static double GetDigit(string strValue)
        {
            double dblDigit = 0;

            string strDigit = GetNumber(strValue, true);
            double.TryParse(strDigit, out dblDigit);

            return dblDigit;
        }

        public static int GetInteger(string strValue)
        {
            int dblDigit = 0;

            string strDigit = GetNumber(strValue, false);
            int.TryParse(strDigit, out dblDigit);

            return dblDigit;
        }

        public static long GetLong(string strValue)
        {
            long dblDigit = 0;

            string strDigit = GetNumber(strValue, false);
            long.TryParse(strDigit, out dblDigit);

            return dblDigit;
        }

        public static T GetNumberFromHex<T>(string strValue)
        {
            return GetNumberFromHex<T>(strValue, 0);
        }

        public static T GetNumberFromHex<T>(string strValue, int indexStart)
        {
            T objValue = default(T);

            try
            {
                if (string.IsNullOrEmpty(strValue))
                    goto DoExit;

                int lenSize = Marshal.SizeOf(typeof(T)) * 2;

                if (indexStart + lenSize >= strValue.Length)
                    goto DoExit;

                Type t = typeof(T);
                string tmpValue = strValue.Substring(indexStart, lenSize);

                if (t == typeof(long))
                {
                    objValue = (T)Convert.ChangeType(Convert.ToInt64(tmpValue, 16), t);
                }
                else if (t == typeof(ulong))
                {
                    objValue = (T)Convert.ChangeType(Convert.ToUInt64(tmpValue, 16), t);
                }
                else if (t == typeof(int))
                {
                    objValue = (T)Convert.ChangeType(Convert.ToUInt32(tmpValue, 16), t);
                }
                else if (t == typeof(uint))
                {
                    objValue = (T)Convert.ChangeType(Convert.ToUInt32(tmpValue, 16), t);
                }
                else if (t == typeof(short))
                {
                    objValue = (T)Convert.ChangeType(Convert.ToUInt16(tmpValue, 16), t);
                }
                else if (t == typeof(ushort))
                {
                    objValue = (T)Convert.ChangeType(Convert.ToUInt16(tmpValue, 16), t);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetNumberFromHex");
            }

            DoExit:
            return objValue;
        }

        public static T GetValue<T>(object dbData, T defaltValue)
        {
            T value = defaltValue;

            try
            {
                if (dbData != null && !(dbData is DBNull))
                {
                    value = (T)Convert.ChangeType(dbData, typeof(T));
                }
                else
                {
                    value = defaltValue;
                }
            }
            catch
            { }

            return value;
        }

        public static T GetValue<T>(string strValue, T defaultValue)
        {
            T tValue = defaultValue;
            object objValue = null;

            Type type = typeof(T);
            if (type == typeof(string))
            {
                objValue = string.IsNullOrEmpty(strValue) ? Convert.ChangeType(defaultValue, type) : strValue;
            }
            else if (type == typeof(bool))
            {
                objValue = string.IsNullOrEmpty(strValue) ? Convert.ChangeType(defaultValue, type) : string.Compare(strValue, true.ToString(), StringComparison.OrdinalIgnoreCase) == 0;
            }
            else if (type == typeof(DateTime))
            {
                DateTime dt = DateTime.MinValue;
                if (DateTime.TryParse(strValue, out dt))
                    objValue = dt;
            }
            else if (type == typeof(float) || type == typeof(double) || type == typeof(decimal))
            {
                objValue = string.IsNullOrEmpty(strValue) ? Convert.ChangeType(defaultValue, type) : Common.GetDigit(strValue);
            }
            else if (type == typeof(System.Drawing.Size))
            {
                string[] arrSizeValue = strValue.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                if (arrSizeValue.Length == 2)
                    objValue = new System.Drawing.Size(Common.GetInteger(arrSizeValue[0]), Common.GetInteger(arrSizeValue[1]));
            }
            else
            {
                objValue = string.IsNullOrEmpty(strValue) ? Convert.ChangeType(defaultValue, type) : Common.GetLong(strValue);
            }

            if (objValue != null)
                tValue = (T)Convert.ChangeType(objValue, type);

            return tValue;
        }

        public static string RunExeAsPipe(string exePath, params string[] arrArgs)
        {
            string strLog = string.Empty;

            try
            {
                if (!File.Exists(exePath))
                    goto DoExit;

                SECURITY_ATTRIBUTES sa = new SECURITY_ATTRIBUTES();
                sa.nLength = Marshal.SizeOf(sa);
                sa.lpSecurityDescriptor = null;
                sa.bInheritHandle = true;

                IntPtr hWrite = new IntPtr();
                IntPtr hRead = new IntPtr();

                if (!API.CreatePipe(ref hRead, ref hWrite, sa, 0))
                {
                    Common.Log("CreatePipe err");
                    //MessageBox.Show("pipe err");
                    goto DoExit;
                }

                STARTUPINFO si = new STARTUPINFO();
                si.cb = Marshal.SizeOf(si);
                si.hStdError = hWrite;
                si.hStdOutput = hWrite;
                si.wShowWindow = API.SW_HIDE;
                si.dwFlags = API.STARTF_USESHOWWINDOW | API.STARTF_USESTDHANDLES;

                PROCESS_INFORMATION pi = new PROCESS_INFORMATION();

                //ffmpeg.dll -vn -i 1828.mp3 -ss 00:01:12 -t 00:01:42 -acodec libfaac -ac 2 -ab 128k tmp.mp4
                //string arguments = "\"{0}\" -vn -i \"{1}\" -ss {2} -t {3} \"{4}\"";
                //string command = string.Format(arguments, MediaHelper.ffmpeg, this.mInputFile, this.FormatDuration(this.mInfo.StartTime), this.FormatDuration(this.mInfo.EndTime - this.mInfo.StartTime), this.mOutputFile);
                StringBuilder sbArgs = new StringBuilder();
                sbArgs.AppendFormat("\"{0}\"", exePath);

                foreach (string strArgs in arrArgs)
                {
                    if (strArgs.Contains(" "))
                        sbArgs.AppendFormat(" \"{0}\"", strArgs);
                    else
                        sbArgs.AppendFormat(" {0}", strArgs);
                }

                string command = sbArgs.ToString();//@"C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe"

                if (!API.CreateProcess(null, command, null, null, true, API.NORMAL_PRIORITY_CLASS, null, null, ref si, ref pi))
                {
                    Common.Log("CreateProcess err");
                    //MessageBox.Show("create process err");
                    API.CloseHandle(hRead);
                    API.CloseHandle(hWrite);
                    goto DoExit;
                }

                API.CloseHandle(hWrite);

                byte[] buffer = new byte[4096];
                int bytesRead = 0;

                StringBuilder sb = new StringBuilder();
                while (API.ReadFile(hRead, buffer, buffer.Length, ref bytesRead, IntPtr.Zero))
                {
                    string strInfo = System.Text.Encoding.UTF8.GetString(buffer, 0, bytesRead);
                    sb.Append(strInfo);

                    System.Threading.Thread.Sleep(50);
                }
                API.CloseHandle(hRead);

                strLog = sb.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "RunExeAsPipe");
            }

            DoExit:
            return strLog;
        }

    }


}
