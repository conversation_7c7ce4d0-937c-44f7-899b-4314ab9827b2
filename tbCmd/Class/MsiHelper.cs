﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.ServiceProcess;
using System.Text;
using System.Threading.Tasks;

namespace iTong.Android
{
    public class Msi<PERSON>elper
    {
        /// <summary>
        /// Kill进程
        /// </summary>
        /// <param name="name"></param>
        public static void KillProces(string name)
        {
            try
            {
                Process[] processes = Process.GetProcessesByName(name);
                if (processes.Length > 0)
                {
                    foreach (Process process in processes)
                    {
                        process.Kill();
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(string.Format("API.GetLastError = {0} .Ex = {1}", API.GetLastError().ToString(), ex.ToString()));
            }
        }

        /// <summary>
        /// 停止服务
        /// </summary>
        /// <param name="name"></param>
        public static void StopService(string name)
        {

            try
            {
                using (ServiceController control = new ServiceController(name))
                {
                    if (control.Status == ServiceControllerStatus.Running)
                    {
                        control.Stop();
                    }
                }

            }
            catch (Exception ex)
            {
                Common.LogException(string.Format("API.GetLastError = {0} .Ex = {1}", API.GetLastError().ToString(), ex.ToString()));
            }
        }

        /// <summary>
        /// 删除服务
        /// </summary>
        /// <param name="name"></param>
        public static void DeleteService(string name)
        {
            try
            {
                string cmd = string.Format("sc delete \"{0}\"", name);
                API.ProcessStartUntilFinish(cmd, false, System.Text.Encoding.Default, 0);
            }
            catch (Exception ex)
            {
                Common.LogException(string.Format("API.GetLastError = {0} .Ex = {1}", API.GetLastError().ToString(), ex.ToString()));
            }
        }
    }
}
