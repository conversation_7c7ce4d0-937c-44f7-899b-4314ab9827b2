﻿ using System.Text;
 using System.Runtime.InteropServices;
 using System;
//using Microsoft.VisualBasic;

namespace iTong.CoreFoundation
{
    public class IniClass
    {
        [DllImport("kernel32", EntryPoint = "GetPrivateProfileSectionA", CharSet = CharSet.Ansi, SetLastError = true, ExactSpelling = true)]
        private static extern int GetPrivateProfileSection(string lpAppName, [MarshalAs(UnmanagedType.VBByRefStr)] ref string lpReturnedString, int nSize, string lpFileName);
        [DllImport("kernel32", EntryPoint = "GetPrivateProfileStringA", CharSet = CharSet.Ansi, SetLastError = true, ExactSpelling = true)]
        private static extern int GetPrivateProfileString(string lpApplicationName,string lpKeyName, string lpDefault, [MarshalAs(UnmanagedType.VBByRefStr)] ref string lpReturnedString, int nSize,string lpFileName);


        [DllImport("kernel32", EntryPoint = "WritePrivateProfileStringA", CharSet = CharSet.Ansi, SetLastError = true, ExactSpelling = true)]
        private static extern Int32 WritePrivateProfileString(string lpApplicationName, string lpKeyName, string lpString, string lpFileName);
        [DllImport("kernel32", EntryPoint = "WritePrivateProfileSectionA", CharSet = CharSet.Ansi, SetLastError = true, ExactSpelling = true)]
        private static extern Int32 WritePrivateProfileSection(string lpApplicationName, string lpAllKeyName, string lpFileName);

        //******************************************************************************************
        //**
        //** 模块名称: IniClass
        //** 作　　者: 熊俊
        //** 创建日期: 2005-3-10
        //** 功　　能: 对 Ini 文件进行操作
        //** 说　　明: 完整的对 Ini 文件进行操作的模块
        //**
        //******************************************************************************************

        //***************************************************
        //**
        //** Ini 文件结构说明：
        //**
        //** [Option] ------------------------> Section
        //** main = images \ logo.jpg --------> Key = KeyValue
        //**
        //***************************************************
        ///* 定义 API */
        //-------------------------------------------------------------
        //-------------------------------------------------------------
        //-------------------------------------------------------------
        //******************************************************************************************
        //**
        //** 名　　称: GetIniSectionKey
        //** 备　　注:
        //** 功　　能: 得到段中某健的健值
        //** 返 回 值: As String '健值（字符串）
        //** 参　　数:
        //** sSection As String ' 段名
        //** sKey As String ' 健名
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static string GetIniSectionKey(string sSection, string sKey, string sIniFileName)
        {

            Int32 lngLen = default(Int32);
            string sRet = new string('\0', 1024);
          
            lngLen = GetPrivateProfileString(sSection, sKey, "",ref sRet,1024, sIniFileName);

            return sRet.Substring(0, lngLen).Trim('\0');
        }

        //******************************************************************************************
        //**
        //** 名　　称: SetIniSectionKey
        //** 备　　注:
        //** 功　　能: 设置段中某健的健值
        //** 返 回 值: As Boolean '是否设置成功
        //** 参　　数:
        //** sSection As String ' 段名
        //** sKey As String ' 健名
        //** sValue As String ' 健值
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static bool SetIniSectionKey(string sSection, string sKey, string sValue, string sIniFileName)
        {

            Int32 lngLen = default(Int32);

            lngLen = WritePrivateProfileString(sSection, sKey, sValue, sIniFileName);

            return (lngLen == 0 ? false : true);
        }

        //******************************************************************************************
        //**
        //** 名　　称: GetIniSectionKeyCount
        //** 备　　注:
        //** 功　　能: 得到段中所有的健数
        //** 返 回 值: As Integer '得到段中所有的健数
        //** 参　　数:
        //** sSection As String ' 段名
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static int GetIniSectionKeyCount(string sSection, string sIniFileName)
        {

            Int32 lngLen = default(Int32);
            string sRet = new string('\0', 1024);
       
            lngLen = GetPrivateProfileString(sSection, null, "", ref sRet, 1024, sIniFileName);

            return sRet.Substring(0, lngLen).Split(new char[] { '\0' }, StringSplitOptions.RemoveEmptyEntries).Length;
        }

        //******************************************************************************************
        //**
        //** 名　　称: GetIniSectionAllKey
        //** 备　　注:
        //** 功　　能: 得到段中所有的健
        //** 返 回 值: As String '所有健字符串,每个健以 Chr(0) 为分隔
        //** 参　　数:
        //** sSection As String ' 段名
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static string[] GetIniSectionAllKey(string sSection, string sIniFileName)
        {
            return GetIniSectionAllKey(sSection, sIniFileName, 1024);
        }

        public static string[] GetIniSectionAllKey(string sSection, string sIniFileName,int bufferSize)
        {
            Int32 lngLen = default(Int32);

            if (bufferSize < 1024)
                bufferSize = 1024;

            string sRet = new string('\0', bufferSize);


            lngLen = GetPrivateProfileString(sSection, null, "", ref sRet, bufferSize, sIniFileName);

            return sRet.Substring(0, lngLen).Split(new char[] { '\0' }, StringSplitOptions.RemoveEmptyEntries);
        }

        /// <summary>
        /// 得到段中所有的健
        /// </summary>
        /// <param name="sSection"></param>
        /// <param name="sIniFileName"></param>
        /// <param name="count"></param>
        /// <returns></returns>
        public static string[] GetIniSectionAllKeyEx(string sSection, string sIniFileName, int count)
        {
            if (count > 1024 * 200) count = 1024 * 200;

            Int32 lngLen = default(Int32);
            string sRet = new string('\0', count);


            lngLen = GetPrivateProfileString(sSection, null, "", ref sRet, count, sIniFileName);

            return sRet.Substring(0, lngLen).Split(new char[] { '\0' }, StringSplitOptions.RemoveEmptyEntries);
        }

        //******************************************************************************************
        //**
        //** 名　　称: SetIniSectionAllKey
        //** 备　　注: 注意每个健以Chr(0)为分隔,Chr(0)结尾
        //** 功　　能: 写入整批数据到得到段中
        //** 返 回 值: 无
        //** 参　　数:
        //** sSection As String ' 段名
        //** sAllKey As String ' 所有数据
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static void SetIniSectionAllKey(string sSection, string sAllKey, string sIniFileName)
        {

            sAllKey += (sAllKey.EndsWith("\0",StringComparison.CurrentCultureIgnoreCase) ? "" : "\0");


            WritePrivateProfileSection(sSection, sAllKey, sIniFileName);
        }

        //******************************************************************************************
        //**
        //** 名　　称: GetIniSectionCount
        //** 备　　注:
        //** 功　　能: 得到所有段数
        //** 返 回 值: As Integer '所有的段数
        //** 参　　数:
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static int GetIniSectionCount(string sIniFileName)
        {
            Int32 lngLen = default(Int32);
            string sRet = new string('\0', 1024);

            lngLen = GetPrivateProfileString(null, null, "", ref sRet, 1024, sIniFileName);

            return sRet.Substring(0, lngLen).Split(new char[] { '\0' }, StringSplitOptions.RemoveEmptyEntries).Length;
        }

        //******************************************************************************************
        //**
        //** 名　　称: GetIniAllSection
        //** 备　　注: 注意每个段以Chr(0)为分隔
        //** 功　　能: 得到所有的段
        //** 返 回 值: As String '所有段字符串
        //** 参　　数:
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static string[] GetIniAllSection(string sIniFileName)
        {
            return GetIniAllSection(sIniFileName, 1024);
        }

        public static string[] GetIniAllSection(string sIniFileName,int bufferSize)
        {
            Int32 lngLen = default(Int32);
            if (bufferSize < 1024)
                bufferSize = 1024;

            string sRet = new string('\0', bufferSize);

            lngLen = GetPrivateProfileString(null, null, "", ref sRet, bufferSize, sIniFileName);

            return sRet.Substring(0, lngLen).Split(new char[] { '\0' }, StringSplitOptions.RemoveEmptyEntries);
        }
        //******************************************************************************************
        //**
        //** 名　　称: DeleteSection
        //** 备　　注:
        //** 功　　能: 删除段
        //** 返 回 值: 无
        //** 参　　数:
        //** sSection As String ' 段名
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static void DeleteSection(string sSection, string sIniFileName)
        {

            Int32 lngLen = default(Int32);


            lngLen = WritePrivateProfileString(sSection, null, null, sIniFileName);
        }

        //******************************************************************************************
        //**
        //** 名　　称: DeleteSectionKey
        //** 功　　能: 删除健值
        //** 备　　注: lpString 设置为 vbNullString 与 设置为 "" 有区别，
        //** 设置为 vbNullString 是删除此键
        //** 返 回 值: 无
        //** 参　　数:
        //** sSection As String ' 段名
        //** sKey As String ' 健名
        //** sIniFileName As String ' Ini文件名
        //**
        //******************************************************************************************
        public static void DeleteSectionKey(string sSection, string sKey, string sIniFileName)
        {

            Int32 lngLen = default(Int32);


            lngLen = WritePrivateProfileString(sSection, sKey, null, sIniFileName);
        }

    }
}

