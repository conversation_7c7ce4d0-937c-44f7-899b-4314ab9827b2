﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Diagnostics;
using System.Reflection;
using System.IO;
using System.Runtime.InteropServices;
using System.Security.Cryptography;

#if X64 && CMD
using iTong.CoreFoundation;
#endif

namespace iTong.CoreFoundation
{
    internal static class Hash72
    {
        [DllImport("Hash72.dll")]
        static extern IntPtr CalcDBHash(byte[] arrUDID, byte[] arrSha1);
                
        public static byte[] CalculateHash(byte[] arrSha1, string UDID)
        {
            byte[] arrHash = null;

            IntPtr ret = CalcDBHash(StringToByteArray(UDID), arrSha1);
            if (ret != IntPtr.Zero)
            {
                arrHash = new byte[46];
                Marshal.Copy(ret, arrHash, 0, 46);
            }
            else 
            {
                string msg = String.Format("Hash72 calculation failed.\r\n\r\nChanges have not been saved to your iPod.");
                Common.LogException(msg);
            }

            return arrHash;
        }
        
        private static byte[] StringToByteArray(string hex)
        {
            try
            {
                int NumberChars = hex.Length;
                byte[] bytes = new byte[NumberChars / 2];
                for (int i = 0; i < NumberChars; i += 2)
                    bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
                return bytes;
            }
            catch
            {
                return Encoding.ASCII.GetBytes(hex);
            }
        }
    }
}
