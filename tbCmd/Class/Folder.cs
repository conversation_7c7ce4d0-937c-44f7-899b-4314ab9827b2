﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Text;
using System.Xml;

#if X64
using tbCmd;
#endif

#if MAC || IOS
using Foundation;
#else
using System.Windows.Forms;
#endif


namespace iTong.CoreFoundation
{

    //此代表的数值用于HtmlHelper.cs里面回传给服务器的来源标志，只能添加，不要修改
    public enum RunType : long
    {
        SKPlayer = 501,

        Other = 0,
        Tongbu = 100,
        iClover = 101,
        JBHelper = 103,
        <PERSON><PERSON> = 104,
        ZJHelper = 105,
        AAHelper = 107,
        KKHelper = 108,
        WeixinEmoticon = 109,
        TongbuLite = 110,
        DuoBao = 111,
        SyncMobileDeviceHelper = 112,
        Tongbu_Abroad = 113,                        //同步助手海外收费版
        WechatMasterWin = 114,                      //win版微信管理大师
        iDataRecovery = 115,                        //数据找滚回来
        iWeChatMigration = 116,                     //微信数据迁移
        iWechatAssistant = 117,                     //微信备份助手
        iDataRecovery_Abroad = 118,                 //数据找滚回来海外
        iWechatRecoveryForAndroid = 119,            //微信恢复大师
        iVoiceMerge = 120,                          //语音合并助手
        iWechatPCTool = 121,                        //微信电脑版助手
        FlashGetCast = 1001,
        FlashGetCastToTV = 1005,
        FlashGetCastScreenToTV = 1006,
        FlashGetMirrorScreenToTV = 1007,


        AppHashServer = 201,
        AppBuySverver = 202,
        AppActiveMgr = 203,
        AppActiveServer = 204,
        AppActiveSelf = 205,
        TestApp = 206,
        TestSharp = 207,

        WechatMaster = 208,                           // 微信管理大师
        AltSigner = 209,                              //苹果个人帐号的签名工具
        BackupToAloneBackup = 301,

        zxKefu = 401,      //掌心客服
        tbToolkit = 402,

        Helper = 601,
        PkgInstaller = 602,
        Feedback = 603,
        AppSearch = 604,
        iRecoveryDevice = 605,
        tbMobileService = 606,
        tbFlashHelper = 607,
        tbRepair = 608,
        tbVipHelper = 609,
        tbWCRHelper = 610,


        AirDroid = 2001,
        AirDroidMirror = 2002,
        AirDroidBusiness = 2003,
        AirDroidBusinessDaemon = 2004,
        AirDroidCast = 2005,
        AirDroidRs = 2006,
        RemoteSupport = 2007,
        AirDroidParentalConnector = 2008,
        BizDaemon = 2009,

        PandaSpy = 23,
    }

    public enum LanguageType
    {
        zh_CN,      //国内版
        en_US,      //海外版
        vi_VN,      //越南版
        th_TH,      //泰国
        ringtone_en_US//铃声助手海外版 
    }

    public class Folder
    {
        private static string mAppFolder = string.Empty;
        private static string mDocFolder = string.Empty;
        private static string mFreeDiskFolder = string.Empty;
        private static string mLangName = string.Empty;
        private static string mUserDocFolder = string.Empty;
        private static string mDocumentFolderCommon = string.Empty;
        private static string mDocumentFolder = string.Empty;
        private static string mAppDataFolderCommon = string.Empty;
        private static string mAppDataFolder = string.Empty;
        private static string mFreeUserAppData = string.Empty;
        private static string mCacheFolder = string.Empty;
        private static string mAppName = string.Empty;

        private static bool mIsDebug = false;

#if IOS
        private static RunType mAppType = RunType.SKPlayer;
        private static string mCacheFolder = string.Empty;
        private static string mLibraryFolder = string.Empty;
#else
        private static RunType mAppType = RunType.Tongbu;
#endif

        private static bool mCreateDocSuccessed = true;   //在我的文档创建目录是否成功（默认为True，代表创建成功）
        private static bool mCreateTempSuccessed = true;  //在系统临时目录创建（默认为True，代表创建成功）

        #region "--- 初始化 ---"

        static Folder()
        {
            try
            {
                //A：“Application.StartupPath”:获取当前应用程序所在目录的路径，最后不包含“\”；
                //B：“Application.ExecutablePath ”:获取当前应用程序文件的路径，包含文件的名称；
                //C：“AppDomain.CurrentDomain.BaseDirectory”:获取当前应用程序所在目录的路径，最后包含“\”；
                //D：“System.Threading.Thread.GetDomain().BaseDirectory”:获取当前应用程序所在目录的路径，最后包含“\”；
                //E：“Environment.CurrentDirectory”:获取当前应用程序的路径，最后不包含“\”；
                //F：“Directory.GetCurrentDirectory”:获取当前应用程序的路径，最后不包含“\”；

                //foreach (System.Environment.SpecialFolder dirType in System.Enum.GetValues(typeof(System.Environment.SpecialFolder)))
                //{
                //    string strDir = Environment.GetFolderPath(dirType);
                //    Console.WriteLine(dirType.ToString() + ":\t" + strDir);
                //}

                //{ 

                //}

#if MAC
				mAppFolder = NSBundle.MainBundle.ResourcePath.Replace("Resources", "MonoBundle") + Path.DirectorySeparatorChar.ToString();

				mAppType = GetRunType();
				mLangName = GetLangName();

				//foreach (NSSearchPathDirectory dirType in System.Enum.GetValues(typeof(NSSearchPathDirectory)))
				//{
				//	string[] arrDir = NSSearchPath.GetDirectories(dirType, NSSearchPathDomain.User, true);
				//	foreach (string strDir in arrDir)
				//		Console.WriteLine(dirType.ToString() + ":\t" + strDir);
				//}

				//ApplicationDirectory:               /Users/<USER>/Applications
				//DemoApplicationDirectory:           /Users/<USER>/Applications / Demos
				//DeveloperApplicationDirectory:      /Users/<USER>/Developer / Applications
				//AdminApplicationDirectory:          /Users/<USER>/Applications / Utilities
				//LibraryDirectory:                   /Users/<USER>/Library
				//DeveloperDirectory:                 /Users/<USER>/Developer
				//DocumentationDirectory:             /Users/<USER>/Library / Documentation
				//DocumentDirectory:                  /Users/<USER>/Documents
				//AutosavedInformationDirectory:      /Users/<USER>/Library / Autosave Information
				//DesktopDirectory:					  /Users/<USER>/Desktop
				//CachesDirectory:                    /Users/<USER>/Library / Caches
				//ApplicationSupportDirectory:        /Users/<USER>/Library / Application 
				//SupportDownloadsDirectory:		  /Users/<USER>/Downloads
				//InputMethodsDirectory:              /Users/<USER>/Library / Input 
				//MethodsMoviesDirectory:			  /Users/<USER>/Movies
				//MusicDirectory:                     /Users/<USER>/Music
				//PicturesDirectory:                  /Users/<USER>/Pictures
				//SharedPublicDirectory:              /Users/<USER>/Public
				//PreferencePanesDirectory:           /Users/<USER>/Library / PreferencePanes
				//AllApplicationsDirectory:           /Users/<USER>/Applications
				//AllApplicationsDirectory:           /Users/<USER>/Applications / Utilities
				//AllApplicationsDirectory:           /Users/<USER>/Developer / Applications
				//AllApplicationsDirectory:           /Users/<USER>/Applications / Demos
				//AllLibrariesDirectory:              /Users/<USER>/Library
				//AllLibrariesDirectory:              /Users/<USER>/Developer
				//TrashDirectory:                     /Users/<USER>/.Trash

				mDocFolder = Path.Combine(NSSearchPath.GetDirectories(NSSearchPathDirectory.MusicDirectory, NSSearchPathDomain.User, true)[0], mAppType.ToString()) + Path.DirectorySeparatorChar.ToString(); ;
				mFreeDiskFolder = mDocFolder;
				mLogFolder = Path.Combine(mDocFolder, "Logs");
				mExceptionFolder = Path.Combine(mDocFolder, "Exception");
				mLogTestFolder = Path.Combine(mDocFolder, "LogTest");

#elif IOS
                mAppFolder = NSBundle.MainBundle.ResourcePath.Replace("Resources", "MonoBundle") + Path.DirectorySeparatorChar.ToString();
                mDocFolder = NSSearchPath.GetDirectories(NSSearchPathDirectory.DocumentDirectory, NSSearchPathDomain.User, true)[0] + Path.DirectorySeparatorChar.ToString();
                mCacheFolder = NSSearchPath.GetDirectories(NSSearchPathDirectory.CachesDirectory, NSSearchPathDomain.User, true)[0] + Path.DirectorySeparatorChar.ToString();
                mLibraryFolder = NSSearchPath.GetDirectories(NSSearchPathDirectory.LibraryDirectory, NSSearchPathDomain.User, true)[0] + Path.DirectorySeparatorChar.ToString();

                mLogFolder = Path.Combine(mDocFolder, "Logs") + Path.DirectorySeparatorChar.ToString();
                mExceptionFolder = Path.Combine(mDocFolder, "Exception") + Path.DirectorySeparatorChar.ToString();
#else

                mAppFolder = AppDomain.CurrentDomain.BaseDirectory.TrimEnd(Path.DirectorySeparatorChar) + Path.DirectorySeparatorChar.ToString();
                if (!Directory.Exists(mAppFolder))
                {
                    mAppFolder = Application.StartupPath.TrimEnd(Path.DirectorySeparatorChar) + Path.DirectorySeparatorChar.ToString();

                    if (!Directory.Exists(mAppFolder))
                        mAppFolder = Environment.CurrentDirectory.TrimEnd(Path.DirectorySeparatorChar) + Path.DirectorySeparatorChar.ToString();
                }

                mIsDebug = mAppFolder.TrimEnd(Path.DirectorySeparatorChar).EndsWith("Debug", StringComparison.OrdinalIgnoreCase);
                Init(GetRunType(), false);

#endif
            }
            catch (Exception ex)
            {
                Utility.LogException(ex.ToString(), "Folder.New");
            }

            Folder.CheckFolder();
        }

        public static bool IsNewApp
        {
#if NET46
            get { return true; }
#else
            get { return (int)(mAppType) >= 1000; }
#endif
        }

        public static void Init(RunType runType, bool checkFolder = true)
        {
            mFreeUserAppData = string.Empty;
            mDocFolder = string.Empty;
            mAppType = runType;
            mAppName = mAppType.ToString().Split('_')[0];
            mLangName = GetLangName();
#if !NET20
            mDocumentFolderCommon = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonDocuments), mAppName) + Path.DirectorySeparatorChar.ToString();
            mAppDataFolderCommon = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), mAppName) + Path.DirectorySeparatorChar.ToString();
#endif
            mDocumentFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), mAppName) + Path.DirectorySeparatorChar.ToString();
            
            if (IsNewApp)
            {
                mAppDataFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), mAppName) + Path.DirectorySeparatorChar.ToString();
                mCacheFolder = mAppDataFolder + "Cache" + Path.DirectorySeparatorChar.ToString();
            }
            else
            {
                mAppDataFolder = mAppFolder;
                mCacheFolder = mDocumentFolder + "Cache" + Path.DirectorySeparatorChar.ToString();
            }

            mLogFolder = mAppDataFolder + "Logs" + Path.DirectorySeparatorChar.ToString();
            mExceptionFolder = mAppDataFolder + "Exception" + Path.DirectorySeparatorChar.ToString();
            mLogTestFolder = mAppDataFolder + "LogTest" + Path.DirectorySeparatorChar.ToString();
            mFlashLogFolder = mAppDataFolder + "FlashLogs" + Path.DirectorySeparatorChar.ToString();

            string strPathOld = AppFolder + "Settings.ini";
            if (File.Exists(strPathOld) && !File.Exists(ConfigIniFile))
            {
                File.Move(strPathOld, ConfigIniFile);
            }

            if (checkFolder)
                Folder.CheckFolder();
        }

#endregion

#region "--- 其它属性与方法---"

        public static string ApplicationDataFolder
        {
            get { return mAppDataFolder; }
        }

        public static string AppDataFolderCommon
        {
            get { return mAppDataFolderCommon; }
        }

        public static string WindowsAppsFolder
        {
            get
            {
                string dirProgramFiles = System.Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles);
                string dirWindowsApps = Path.Combine(dirProgramFiles.Replace(" (x86)", ""), "WindowsApps");

                return dirWindowsApps;
            }
        }

        public static string ExecutablePath
        {
            get
            {
#if MAC || IOS
                return NSBundle.MainBundle.BundlePath;

#else
                return Application.ExecutablePath;
#endif
            }
        }

        public static RunType GetRunTypeFromExe(RunType defaultType)
        {
            RunType rType = defaultType;

            try
            {
                string strName = string.Empty;
#if MAC
                string strBundleName = NSBundle.MainBundle.InfoDictionary.ValueForKey(new NSString("CFBundleName")).ToString();
                if (strBundleName.Contains("Wechat"))
                    rType = RunType.WechatMaster;

                strName = strBundleName;
#else
                strName = Path.GetFileNameWithoutExtension(System.Windows.Forms.Application.ExecutablePath);

                bool blnFind = false;
                foreach (RunType type in System.Enum.GetValues(typeof(RunType)))
                {
                    if (string.Compare(strName, type.ToString(), true) == 0)
                    {
                        rType = type;
                        blnFind = true;
                        break;
                    }
                }

                if (blnFind && ((int)rType < 200 || (int)rType >= 1000))
                    goto DoExit;

                string strDir = Path.GetDirectoryName(Application.ExecutablePath);
                string[] arrPath = Directory.GetFiles(strDir, "*.exe");
                if (arrPath == null || arrPath.Length < 0)
                    goto DoExit;

                foreach (RunType type in Enum.GetValues(typeof(RunType)))
                {
                    //if (type == RunType.FlashGetCast)
                    //{
                    //}

                    if ((int)type < 200 || (int)type >= 1000)
                    {
                        foreach (string path in arrPath)
                        {
                            if (string.Compare(Path.GetFileNameWithoutExtension(path), type.ToString(), true) == 0)
                            {
                                rType = type;
                                goto DoExit; ;
                            }
                        }
                    }
                }

#endif
            }
            catch (Exception ex)
            {
                Utility.LogException(ex.ToString(), "Folder.GetRunTypeFromExe");
            }

        DoExit:
            return rType;
        }

        private static RunType GetRunType()
        {
#if IOS
            return RunType.SKPlayer;
#else
            RunType rType = GetRunTypeFromExe(RunType.Tongbu);

            return rType;
#endif
        }

        private static string GetLangName()
        {
            string strLangName = "";//"zh-CN";

            try
            {
#if MAC || IOS
                NSObject objLangName = NSBundle.MainBundle.InfoDictionary.ValueForKey(new NSString("LangName"));
                if (objLangName != null)
                    strLangName = objLangName.ToString();

#else

                string strConfig = Folder.AppFolder + string.Format("{0}.exe.config", Path.GetFileNameWithoutExtension(Application.ExecutablePath));
                if (!File.Exists(strConfig))
                    strConfig = Folder.AppFolder + string.Format("{0}.exe.config", AppName);

                if (!File.Exists(strConfig))
                    goto DoExit;

                XmlReaderSettings xmlSet = new XmlReaderSettings();
                xmlSet.XmlResolver = null;
                xmlSet.CheckCharacters = false;
                xmlSet.IgnoreComments = true;
                xmlSet.IgnoreWhitespace = true;
                xmlSet.ValidationType = ValidationType.None;

                XmlDocument xmlDoc = new XmlDocument();
                XmlReader xmlRead = XmlReader.Create(strConfig, xmlSet);
                xmlDoc.Load(xmlRead);
                xmlRead.Close();


                if (xmlDoc["configuration"] == null || xmlDoc["configuration"]["applicationSettings"] == null || xmlDoc["configuration"]["applicationSettings"]["iTong.My.MySettings"] == null)
                    goto DoExit;

                XmlNode nodeSetting = xmlDoc["configuration"]["applicationSettings"]["iTong.My.MySettings"];
                XmlNode nodeLangName = nodeSetting.ChildNodes[1];
                foreach (XmlNode item in nodeSetting.ChildNodes)
                {
                    if (item.Attributes.Count == 2 && item.Attributes["name"] != null && item.Attributes["name"].Value == "LangName")
                    {
                        strLangName = item.InnerText;
                        break;
                    }
                }

#endif
            }
            catch (Exception ex)
            {
                Utility.LogException(ex.ToString(), "Folder.GetLangName");
            }

        DoExit:
            if (string.IsNullOrEmpty(strLangName))
            {
                string dirLang = Path.Combine(mAppFolder, "Lang");
                if (Directory.Exists(dirLang))
                {
                    string[] arrFile = Directory.GetFiles(dirLang, "*.lang");
                    foreach (string strFile in arrFile)
                    {
                        strLangName = Path.GetFileNameWithoutExtension(strFile);
                        if (strLangName == "zh-CN")
                            break;
                    }
                }

                if (string.IsNullOrEmpty(strLangName))
                    strLangName = "zh-CN";
            }

            //判断是否为海外版本
            if (string.Compare(strLangName, "zh-CN", true) != 0)
            {
                switch (mAppType)
                {
                    case RunType.Tongbu: mAppType = RunType.Tongbu_Abroad; break;
                    case RunType.iDataRecovery: mAppType = RunType.iDataRecovery_Abroad; break;
                }
            }

            return strLangName;
        }

        public static string AppName
        {
            get
            {
                return mAppName;
            }
        }

        public static string LangName
        {
            get { return mLangName; }
        }

        //区域类型（国内版，海外版，越南版）
        //与语言包没有关系
        public static LanguageType LangType
        {
            get
            {
                LanguageType type = LanguageType.zh_CN;

                try
                {
                    type = (LanguageType)System.Enum.Parse(typeof(LanguageType), LangName.Replace("-", "_"));
                }
                catch (Exception ex)
                {
                    Utility.LogException(ex.ToString(), "Folder.LangType");
                }
                //Select Case LangName
                //    Case "en-US"
                //        type = LanguageType.en_US
                //    Case "vi-VN"
                //        type = LanguageType.vi_VN
                //    Case "th-VN"
                //        type = LanguageType.vi_VN
                //End Select
                return type;
            }
        }

        public static RunType AppType
        {
            get { return mAppType; }
        }

        //获取主程序的版本号（2.0.8.2）

        public static string GetMainSoftVersion()
        {
            string strVer = string.Empty;

            try
            {
#if MAC || IOS

                strVer = NSBundle.MainBundle.InfoDictionary.ValueForKey(new NSString("CFBundleShortVersionString")).ToString();

#else
                if (File.Exists(Folder.AppFile))
                {
                    strVer = System.Diagnostics.FileVersionInfo.GetVersionInfo(Folder.AppFile).FileVersion;
                }
#endif
            }
            catch (Exception ex)
            {
                Utility.LogException(ex.ToString(), "Folder.GetMainSoftVersion");
            }

            return strVer;
        }

        //获取当前程序是否是从PkgInstaller.exe运行的
        public static bool IsRunFromPkgInstaller()
        {
            return IsRunFrom("PkgInstaller");
        }

        //获取当前程序是否是从tbMobileService.exe运行的
        public static bool IsRunFromtbMobileService()
        {
            return IsRunFrom("tbMobileService");
        }

        public static bool IsRunFrom(string strExeName)
        {
#if MAC || IOS
            return false;
#else
            bool blnReturn = false;
            string strName = Path.GetFileNameWithoutExtension(System.Windows.Forms.Application.ExecutablePath);
            if (string.Compare(strName, strExeName, true) == 0)
                blnReturn = true;

            return blnReturn;
#endif
        }

#endregion

#region "--- 助手默认目录判断逻辑 ---"

        /// <summary>
        /// 程序通用数据存放目录，在 %AppData%\tongbu 下
        /// </summary>
        public static string DocumentFolder
        {
            get
            {
#if MAC || IOS
                //string strPath = Path.Combine(NSSearchPath.GetDirectories(NSSearchPathDirectory.ApplicationSupportDirectory, NSSearchPathDomain.User)[0], "WeixinHelper");

                //// 代码没签名的时候指向user下的Library，签名后指向应用沙盒中的Library
                ////				string strLibrary = NSSearchPath.GetDirectories (NSSearchPathDirectory.LibraryDirectory, NSSearchPathDomain.User) [0];
                ////				string strPath = Path.Combine (strLibrary, "Application Support/AirDroid");
                //CreateFolder(strPath);
                //return strPath;

                return mDocFolder;

#else
                if (IsNewApp)
                    return mAppDataFolder;
                else
                    return FreeDiskFolder;
#endif
            }
        }


        /// <summary>
        /// 程序文件存放目录，在 My Documents\tongbu 下，避免程序被卸载后一些用户自定义的配置以及备份文件被一并卸载而丢失。
        /// </summary>
        public static string DocumentFolderEx
        {
            get
            {
                if (string.IsNullOrEmpty(mDocFolder))
                {
                    if (IsNewApp)
                    {
                        mDocFolder = mDocumentFolder;
                    }
                    else
                    {
                        string strDoc = string.Empty;
                        if (mCreateDocSuccessed)
                        {
                            strDoc = System.Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
                        }
                        else
                        {
                            strDoc = Folder.AppFolder;
                        }

                        if ((long)mAppType >= 200 && (long)mAppType < 208)
                            mDocFolder = Path.Combine(strDoc, RunType.Tongbu.ToString());
                        else
                            mDocFolder = Path.Combine(strDoc, AppName);

                        string appName = IniClass.GetIniSectionKey("Setting", "AppName", Path.Combine(AppFolder, "Program.ini"));
                        if (!string.IsNullOrEmpty(appName))
                            mDocFolder = Path.Combine(strDoc, appName);
                    }

                    mDocFolder = PathFormat(mDocFolder);
                }

                return mDocFolder;
            }
        }

#if !IOS

        /// <summary>
        /// 取磁盘空间最大的盘为助手设置目录。因为有的用户C盘空间太小容易引起空间不足的问题
        /// </summary>
        /// <summary>
        /// 取磁盘空间最大的盘为助手设置目录。因为有的用户C盘空间太小容易引起空间不足的问题
        /// </summary>
        private static string FreeDiskFolder
        {
            get
            {
                if (!string.IsNullOrEmpty(mFreeDiskFolder))
                    goto DoExit;

                try
                {
                    string appName = mAppType.ToString();
                    if (appName.StartsWith("App") || appName.StartsWith("Test"))
                    {
                        mFreeDiskFolder = DocumentFolderEx;
                        goto DoExit;
                    }

                    //如果设置中存在默认目录就取设置中的。
                    string strPath = GetFreeDiskFolderSetting;

                    //如果用户手动在Setting修改路径 但是又没有创建对应的文件夹 系统会直接 获取最大盘符去创建文件夹 不会根据用户设置去创建 2015-07-24 by chenbihai
                    if (!string.IsNullOrEmpty(strPath) && !Directory.Exists(strPath))
                    {
                        CheckFolder(strPath);
                    }

                    if (strPath.Length > 0 && Directory.Exists(strPath))
                    {
                        mFreeDiskFolder = strPath;
                    }
                    else
                    {
                        //如果 我的文档\tongbu 下面有存在目录就取 我的文档\tongbu 的路径

                        //如果我的文档里面没有文件夹(证明用户是第一次用助手)就取空间最大的那个盘 如 E:\tongbu
                        string[] ayyPath = null;
                        if (Directory.Exists(DocumentFolderEx))
                        {
                            ayyPath = Directory.GetDirectories(DocumentFolderEx);
                        }

                        if (ayyPath != null && ayyPath.Length > 0)
                        {
                            mFreeDiskFolder = DocumentFolderEx;
                        }
                        else
                        {
                            mFreeDiskFolder = MaxDrives();
                        }
                    }
                }
                catch (Exception ex)
                {
                    //出错就用原来的逻辑
                    mFreeDiskFolder = DocumentFolderEx;

                    Utility.LogException(ex.ToString(), "Folder.FreeDiskFolder");
                }

            DoExit:
                return mFreeDiskFolder;
            }
        }

        public static string MaxDrives()
        {
            string strMaxDrives = string.Empty;
            try
            {
                string strDoc = string.Empty;
                string strDocNotC = string.Empty;

                long lngFreeSize = 0;
                long lngFreeSizeNotC = 0;
                long lngMax = 53687091200; //50G大小判断
                //1.助手第一次打开查找电脑中最大的非系统盘，如果有超过50G就先最大的非系统盘。
                //2.如果找到最大盘是系统盘，且其它盘小于50G就选择系统盘。
                foreach (DriveInfo Item in System.IO.DriveInfo.GetDrives())
                {
                    try
                    {
                        if (Item.DriveType == DriveType.Fixed && Item.DriveFormat != "HFS")
                        {
                            if (lngFreeSize < Item.TotalFreeSpace)
                            {
                                strDoc = Item.RootDirectory.ToString();
                                lngFreeSize = Item.TotalFreeSpace;
                            }
                            //找出最大的并且大于50G的非系统盘。
                            if (lngFreeSizeNotC < Item.TotalFreeSpace && Item.Name.ToLower() != "c:\\" && Item.TotalFreeSpace > lngMax)
                            {
                                strDocNotC = Item.RootDirectory.ToString();
                                lngFreeSizeNotC = Item.TotalFreeSpace;
                            }
                        }
                    }
                    catch { }
                }

                if (!string.IsNullOrEmpty(strDocNotC))
                {
                    strDoc = strDocNotC;
                    lngFreeSize = lngFreeSizeNotC;
                }

                strMaxDrives = Path.Combine(strDoc, AppName);

                strMaxDrives = PathFormat(strMaxDrives);

                SetFreeDiskFolderSetting(strMaxDrives);
            }
            catch (Exception ex)
            {
                Utility.LogException(ex.ToString(), "Folder.MaxDrives");
            }

            return strMaxDrives;
        }

        private static string GetFreeDiskFolderSetting
        {
            get
            {
                string val = IniClass.GetIniSectionKey("Setting", "DefaultPath", Folder.ConfigIniFile);
                if (string.IsNullOrEmpty(val))
                    val = string.Empty;
                else
                {
                    //判断盘符 是否存在
                    string[] drices = Directory.GetLogicalDrives();
                    string strDrive = Path.GetPathRoot(val);
                    bool isContain = false;
                    for (int i = 0; i < drices.Length; i++)
                    {
                        if (strDrive == drices[i])
                        {
                            isContain = true;
                            break;
                        }
                    }
                    if (!isContain)
                        val = MaxDrives();
                }
                return val;
            }
        }

        private static void SetFreeDiskFolderSetting(string strVal)
        {
            CheckFolder(Folder.DocumentFolderEx);

            IniClass.SetIniSectionKey("Setting", "DefaultPath", strVal, Folder.ConfigIniFile);
        }
#endif

#endregion

#region "--- 共用路径 ---"

        public static string iTunesPlugin
        {
#if TOOLS
            get { return AppFolder + "iTunes" + Path.DirectorySeparatorChar.ToString(); }
#else
            get { return DocumentFolder + "iTunes" + Path.DirectorySeparatorChar.ToString(); }
#endif

        }

        public static string SCInfo
        {
            get { return DocumentFolder + "SCInfo" + Path.DirectorySeparatorChar.ToString(); }
        }

        public static string tbTui
        {
            get { return DocumentFolder + "tbTui" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 获取应用程序启动的目录，以“\”结尾
        /// </summary> 
        public static string AppFolder
        {
            get { return mAppFolder; }
        }

        /// <summary>
        /// 获取应用程序启动的路径
        /// </summary> 
        public static string AppFile
        {
#if MAC || IOS
            get { return NSBundle.MainBundle.ExecutablePath; }
#else
            get { return AppFolder + AppName + ".exe"; }
#endif
        }

#if MAC || IOS

        public static string ResourceDllsFolder
        {
            get { return Path.Combine(ResourceFolder, "Dlls"); }
        }

        public static string ResourceFolder
        {
            get { return NSBundle.MainBundle.ResourcePath; }
        }

        public static string ApplicationSupportFolder
        {
            get
            {
                string strPath = Path.Combine(NSSearchPath.GetDirectories(NSSearchPathDirectory.ApplicationSupportDirectory, NSSearchPathDomain.User)[0], "Tongbu");

                // 代码没签名的时候指向user下的Library，签名后指向应用沙盒中的Library
                //				string strLibrary = NSSearchPath.GetDirectories (NSSearchPathDirectory.LibraryDirectory, NSSearchPathDomain.User) [0];
                //				string strPath = Path.Combine (strLibrary, "Application Support/AirDroid");
                CheckFolder(strPath);

                return strPath;
            }
        }

        public static string UpdateFolder
        {
            get
            {
                string strPath = Path.Combine(DocumentFolder, "Update");
                CheckFolder(strPath);

                return strPath;
            }
        }

#endif

        /// <summary>
        /// 程序插件目录
        /// </summary>
        public static string Plugins
        {
            get { return DocumentFolder + "Plugins" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 表情目录
        /// </summary>
        public static string Emoticon
        {
            get { return DocumentFolder + "Emoticon" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 程序deb插件目录
        /// </summary>
        public static string Deb
        {
            get { return DocumentFolder + "deb" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 系统临时文件夹下的 tongbu 目录，以“\”结尾
        /// </summary>
        public static string TempFolder
        {
            get
            {
                string strTempFolder = string.Empty;
#if IOS
                strTempFolder = mCacheFolder;
#else
                //如果系统目录创建失败则指到程序运行目录
                if (mCreateTempSuccessed)
                {
                    strTempFolder = Path.Combine(Path.GetTempPath(), AppName);
                }
                else
                {
                    strTempFolder = Path.Combine(mAppDataFolder, "Temp");
                }
#endif
                //strTempFolder = PathFormat(strTempFolder);

                return strTempFolder;
            }
        }

        /// <summary>
        /// 配置文件路径
        /// </summary>
        public static string ConfigIniFile
        {
            get { return (IsNewApp ? DocumentFolder : DocumentFolderEx) + "Settings.ini"; }
        }

        public static string AccountIniFile
        {
            get { return (IsNewApp ? DocumentFolder : DocumentFolderEx) + "Account.ini"; }
        }

        /// <summary>
        /// 备份文件大小
        /// </summary>
        public static string MobileSyncBackupIniFile
        {
            get { return Folder.DocumentFolder + "MobileSyncBackup.ini"; }
        }

        public static string AloneBackupWeChatIniFile
        {
            get { return Folder.DocumentFolder + "AloneBackupWeChat.ini"; }
        }

        public static string AloneBackupWeChatSpareIniFile
        {
            get { return Folder.DocumentFolder + "AloneBackupWeChatSpare.ini"; }
        }

        public static string AloneBackupWeChatVIPSpareIniFile
        {
            get { return Folder.DocumentFolder + "AloneBackupWeChatVIPSpare.ini"; }
        }

        public static string PremiumFeatureIniFile
        {
            get { return Folder.DocumentFolder + "PremiumFeature.ini"; }
        }

        public static string MigrationWeChatSpareIniFile
        {
            get { return Folder.DocumentFolder + "MigrationWeChatSpareIniFile.ini"; }
        }

        public static string GameAppTxtFile
        {
            get { return (IsNewApp ? DocumentFolder : DocumentFolderEx) + "GameAppFile.txt"; }
        }

        /// <summary>
        /// 从服务器获取到的iOS固件配置文件路径
        /// </summary>
        public static string ConfigFirmwarePlistServerFile
        {
            get { return DocumentFolder + "FirmwarePlist.plist"; }
        }

        /// <summary>
        /// 从服务器获取到的配置文件路径
        /// </summary>
        public static string ConfigServerIniFile
        {
            get { return DocumentFolder + "ServerSettings.ini"; }
        }

        /// <summary>
        /// 语言包目录
        /// </summary>
        public static string LangFolder
        {
            get
            {
#if MAC || IOS
                return Path.Combine(ResourceFolder, "Lang");
#else
                if (mAppType == RunType.AirDroid)
                {
                    return AppFolder + "AirLang" + Path.DirectorySeparatorChar.ToString();
                }

                return AppFolder + "Lang" + Path.DirectorySeparatorChar.ToString();
#endif
            }
        }


        /// <summary>
        /// IPA解压临时目录
        /// </summary>
        public static string PublicStaging
        {
            get { return TempFolder + "PublicStaging" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// IPA下载目录
        /// </summary>
        public static string DownloadFolder
        {
            get { return DocumentFolder + "PublicStaging" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        ///  缓存文件cache.web.dll
        /// </summary>
        public static string CrashLogPCInfo
        {
            //缓存收集的同步助手版本，使用时长等信息

            get { return CacheFolder + "cache.web.dll"; }
        }

        /// <summary>
        ///  缓存文件cache.info.dll
        /// </summary>
        public static string CrashLogDeviceInfo
        {
            //缓存收集的设备固件版本等信息
            get { return CacheFolder + "cache.info.dll"; }
        }

        /// <summary>
        ///  缓存文件cache.app.dll
        /// </summary>
        public static string CrashLogDeviceInstalledSoft
        {
            //缓存收集的设备上已经安装软件的相关信息
            get { return CacheFolder + "cache.app.dll"; }
        }

        /// <summary>
        ///  缓存文件cache.info.dll
        /// </summary>
        public static string CrashLogAndroidDeviceInfo
        {
            //缓存收集的设备固件版本等信息
            get { return CacheFolder + "cache.androidinfo.dll"; }
        }

        /// <summary>
        /// 音乐专辑缓存目录
        /// </summary>
        public static string AlbumThumbsFolder
        {
            get { return CacheFolder + "AlbumThumbs" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// LRC歌词存放目录
        /// </summary>
        public static string LyricsFolder
        {
            get { return DocumentFolder + "Lyrics" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 下载的音乐目录
        /// </summary>
        public static string MusicFolder
        {
            get { return DocumentFolder + "Music" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 下载的铃声目录
        /// </summary>
        public static string RingtoneFolder
        {
            get { return DocumentFolder + "Ringtone" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 铃声制作目录
        /// </summary>
        public static string M4rFolder
        {
            get { return DocumentFolder + "m4r" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 视频转化目录
        /// </summary>
        public static string Mp4Folder
        {
            get { return DocumentFolder + "mp4" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 下载的壁纸目录
        /// </summary>
        public static string WallPaperFolder
        {
            get { return DocumentFolder + "WallPaper" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 下载的视频目录
        /// </summary>
        public static string VideoFolder
        {
            get { return DocumentFolder + "Video" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// IPA新区下载目录
        /// </summary>
        public static string DownloadNewAreaFolder
        {
            get { return DownloadFolder + "NewArea" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 固件下载目录
        /// </summary>
        public static string FirmwareFolder
        {
            get { return DocumentFolder + "Firmware" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 备份目录
        /// </summary>
        public static string BackupFolder
        {
            get { return DocumentFolder + "Backup" + Path.DirectorySeparatorChar.ToString(); }
        }

        public static string DefaultExportFolder
        {
            get { return DocumentFolder + "Files" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 本地固件解析的缓存目录
        /// </summary>
        public static string CacheFirmwareFolder
        {
            get { return CacheFolder + "Firmware" + Path.DirectorySeparatorChar.ToString(); }
        }

        public static string CacheEmoticonFolder
        {
            get { return CacheFolder + "Emoticon" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        /// 本地APP的缓存目录
        /// </summary>
        public static string CacheAppInfo
        {
            get { return CacheFolder + "AppInfo" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <8.1.3>
        /// 用户安装8.1.3固件签名包的时候如果安装失败备份记录用
        /// <8.1.3>
        public static string BackupAppRecordFolder
        {
            get { return BackupFolder + "AppRecord" + Path.DirectorySeparatorChar.ToString(); }
        }

        /// <summary>
        ///  错误PNG
        /// </summary>
        public static string ErrorImage
        {
            get { return CacheFolder + "error.png"; }
        }

        /// <summary>
        ///  缓存文件cache.log.dll
        /// </summary>
        public static string CrashLogWebOperateInfo
        {
            //缓存收集web页操作行为的数据
            get { return CacheFolder + "cache.log.dll"; }
        }

        /// <summary>
        ///  缓存文件cache.log1.dll(操作时长)
        /// </summary>
        public static string CrashLogWebOperateInfo_TimeSpan
        {
            //缓存收集web页操作行为的数据
            get { return CacheFolder + "cache.log1.dll"; }
        }

        /// <summary>
        ///  缓存文件cache.log2.dll(tbMobileService)
        /// </summary>
        public static string CrashLogWebOperateInfo_tbMobileService
        {
            //缓存收集web页操作行为的数据
            get { return CacheFolder + "cache.log2.dll"; }
        }

        /// <summary>
        ///  缓存文件cache.log3.dll(Helper)
        /// </summary>
        public static string CrashLogWebOperateInfo_Helper
        {
            //缓存收集web页操作行为的数据
            get { return CacheFolder + "cache.log3.dll"; }
        }

        private static string m_mobileApplications = "";
        /// <summary>
        /// iPA目录
        /// </summary>
        public static string iTunesMobileApplications
        {
            get
            {
                string returnValue = m_mobileApplications;

                try
                {
                    //路径为空，或者不存在，那么就重新取一遍
                    if (returnValue.Length == 0 || !Directory.Exists(returnValue))
                    {
#if MAC
						returnValue = Path.Combine(NSSearchPath.GetDirectories(NSSearchPathDirectory.MusicDirectory,NSSearchPathDomain.User)[0],"iTunes/iTunes Media/Mobile Applications");
#else
                        returnValue = System.Environment.GetFolderPath(Environment.SpecialFolder.MyMusic).TrimEnd(System.IO.Path.DirectorySeparatorChar) + "\\iTunes\\";

                        //首先查找 iTunes Music Library.xml 文件
                        string libraryFile = returnValue + "iTunes Music Library.xml";

                        if (File.Exists(libraryFile))
                        {
                            string itunesMediaFolder = "";

#if !TONG && !X64
                            using (System.IO.StreamReader fileReader = new System.IO.StreamReader(libraryFile, Utility.CheckFileEncoding(libraryFile)))
                            {
                                string libraryXml = fileReader.ReadToEnd();

                                itunesMediaFolder = Utility.SearchXmlByKey(libraryXml, "Music Folder");
                                itunesMediaFolder = System.Web.HttpUtility.UrlDecode(itunesMediaFolder, Encoding.Default);
                                itunesMediaFolder = itunesMediaFolder.Replace("file://localhost/", "").Replace("/", Path.DirectorySeparatorChar.ToString()).TrimEnd(System.IO.Path.DirectorySeparatorChar) + "\\Mobile Applications";
                            }

                            if (itunesMediaFolder.Length > 0 && Directory.Exists(itunesMediaFolder))
                            {
                                m_mobileApplications = itunesMediaFolder;

                                return itunesMediaFolder;
                            }
#endif
                        }

                        //如果没有 iTunes Music Library.xml 的话，执行原来的判断逻辑
                        string[] aryFiles = new string[0];

                        if (Directory.Exists(returnValue))
                            aryFiles = Directory.GetDirectories(returnValue, "Mobile Applications", SearchOption.AllDirectories);

                        if (aryFiles.Length > 0)
                            returnValue = aryFiles[0];
                        else
                            returnValue = System.IO.Path.Combine(returnValue, "iTunes Music\\Mobile Applications");
#endif

                        m_mobileApplications = returnValue;
                    }
                }
                catch (Exception ex)
                {
                    Utility.LogException(ex.ToString(), "Folder.iTunesMobileApplications");
                }

                return returnValue;
            }
        }

        private static List<string> m_lstPackageFolder = new List<string>();
        /// <summary>
        /// pxl目录
        /// </summary>
        public static List<string> PxlPackageFolder
        {
            get
            {
#if MAC
				return new List<string>();
#else
                List<string> returnList = m_lstPackageFolder;
                if (returnList.Count == 0)
                {
                    string strFolder = (System.Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments) + "\\91 Mobile\\iPhone\\Download Packages").ToLower();
                    if (Directory.Exists(strFolder) && !returnList.Contains(strFolder))
                        returnList.Add(strFolder);

                    strFolder = (System.Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments) + "\\91 Mobile\\iPhone\\DownloadSavePath\\SoftwarePath").ToLower();
                    if (Directory.Exists(strFolder) && !returnList.Contains(strFolder))
                        returnList.Add(strFolder);

                    //=== 从注册表中获取91手机助手的Setting文件路径 ==============================================================================
                    string strPath91Setting = "";
                    try
                    {
                        using (Microsoft.Win32.RegistryKey hklm = Microsoft.Win32.Registry.LocalMachine)
                        {
                            using (Microsoft.Win32.RegistryKey key = hklm.OpenSubKey("SOFTWARE\\NetDragon\\PC Suite\\iPhone PC Suite"))
                            {
                                if (key != null)
                                    strPath91Setting = System.IO.Path.GetDirectoryName(Convert.ToString(key.GetValue("AppPath"))) + "\\Settings.ini";
                            }
                        }
                    }
                    catch
                    { }
                    //==================================================================================================================

                    //获取91手机助手Setting文件中记录的pxl的下载路径
                    string strAppSavePath = "";
                    if (strPath91Setting.Length > 0 && File.Exists(strPath91Setting))
                        strAppSavePath = IniClass.GetIniSectionKey("DownloadSetting", "SoftwarePath", strPath91Setting).ToLower().TrimEnd(System.IO.Path.DirectorySeparatorChar);


                    if (strAppSavePath.Length > 0 && Directory.Exists(strAppSavePath) && !returnList.Contains(strAppSavePath))
                        returnList.Add(strAppSavePath);
                }

                return returnList;
#endif
            }
        }

#if IOS
        /// <summary>
        /// 缓存文件目录
        /// </summary>
        public static string CacheFolder
        {
            get { return mCacheFolder; }
        }

        public static string LibraryFolder
        {
            get { return mLibraryFolder; }
        }
#else

        /// <summary>
        /// 缓存文件目录
        /// </summary>
        public static string CacheFolder
        {
            get { return DocumentFolder + "Cache" + Path.DirectorySeparatorChar.ToString(); }
        }


#endif

        /// <summary>
        /// 本地APP的缓存目录
        /// </summary>
        public static string CachePackagesFolder
        {
            get { return CacheFolder + "Packages" + Path.DirectorySeparatorChar.ToString(); }
        }

        private static string mLogFolder = "";
        /// <summary>
        /// 日志目录
        /// </summary>
        public static string LogFolder
        {
            get
            {
                return mLogFolder;
            }
        }

        private static string mExceptionFolder = "";
        /// <summary>
        /// 异常目录
        /// </summary>
        public static string ExceptionFolder
        {
            get
            {
                return mExceptionFolder;
            }
        }

        private static string mFlashLogFolder = "";
        /// <summary>
        /// 刷机日志目录
        /// </summary>
        public static string FlashLogFolder
        {
            get
            {
                return mFlashLogFolder;
            }
        }

        private static string mLogTestFolder = "";
        /// <summary>
        /// 日志目录
        /// </summary>
        public static string LogTestFolder
        {
            get
            {
                return mLogTestFolder;
            }
        }


#region "-  AirClient -"

        public static string CacheAppIconFolder
        {
            get { return CacheFolder + "AppIcon" + Path.DirectorySeparatorChar.ToString(); }
        }

        public static string CacheDeviceIconFolder
        {
            get { return CacheFolder + "DeviceIcon" + Path.DirectorySeparatorChar.ToString(); }
        }


        public static string CacheWebFolder
        {
            get { return CacheFolder + "Web" + Path.DirectorySeparatorChar.ToString(); }
        }

        public static string CacheContact
        {
            get { return CacheFolder + "Act" + Path.DirectorySeparatorChar.ToString(); }
        }

        public static string ScreenShotFolder
        {
            get { return DocumentFolder + "ScreenShot" + Path.DirectorySeparatorChar.ToString(); }
        }

#endregion

        private static string mExtractWeChatDB = "";
        /// <summary>
        /// 微信数据找回备份
        /// </summary>
        public static string ExtractWeChatDB
        {
            get
            {
                if (mExtractWeChatDB.Length > 0)
                {
                    return mExtractWeChatDB;
                }

                mExtractWeChatDB = Path.Combine(Folder.AppFolder, "ExtractWeChatDB");

                return mExtractWeChatDB;
            }
        }


        private static string mWeChatAud2Wav = "";
        /// <summary>
        /// 微信语音转换
        /// </summary>
        public static string WeChatAud2Wav
        {
            get
            {
                if (mWeChatAud2Wav.Length > 0)
                {
                    return mWeChatAud2Wav;
                }

                mWeChatAud2Wav = Path.Combine(Folder.AppFolder, "WeChatAud2Wav");

                return mWeChatAud2Wav;
            }
        }


        private static string mWeChatPayMigrationBackupsFolder = "";
        public static string WeChatPayMigrationBackupsFolder
        {
            get
            {
                if (mWeChatPayMigrationBackupsFolder.Length <= 0)
                    mWeChatPayMigrationBackupsFolder = Path.Combine(Folder.DocumentFolder, "WeChatPayMigration");
                return mWeChatPayMigrationBackupsFolder;
            }
        }

        private static string mQuickBackupFolder = "";
        public static string QuickBackupFolder
        {
            get
            {
                if (mQuickBackupFolder.Length <= 0)
                    mQuickBackupFolder = Path.Combine(Folder.DocumentFolder, "QuickBackup");
                return mQuickBackupFolder;
            }
        }

        private static string mAloneWeChatSpareBackupsFolder = "";
        public static string AloneWeChatSpareBackupsFolder
        {
            get
            {
                if (mAloneWeChatSpareBackupsFolder.Length <= 0)
                    mAloneWeChatSpareBackupsFolder = Path.Combine(Folder.QuickBackupFolder, "WeChatSpare");
                return mAloneWeChatSpareBackupsFolder;
            }
        }

        private static string mAloneWeChatVIPSpareBackupsFolder = "";
        public static string AloneWeChatVIPSpareBackupsFolder
        {
            get
            {
                if (mAloneWeChatVIPSpareBackupsFolder.Length <= 0)
                    mAloneWeChatVIPSpareBackupsFolder = Path.Combine(Folder.QuickBackupFolder, "WeChatVIPSpare");
                return mAloneWeChatVIPSpareBackupsFolder;
            }
        }

        private static string mAloneSMSBackupsFolder = "";
        public static string AloneSMSBackupsFolder
        {
            get
            {
                if (mAloneSMSBackupsFolder.Length <= 0)
                    mAloneSMSBackupsFolder = Path.Combine(Folder.QuickBackupFolder, "SMS");
                return mAloneSMSBackupsFolder;
            }
        }

        private static string mAloneWeChatBackupsFolder = "";
        public static string AloneWeChatBackupsFolder
        {
            get
            {
                if (mAloneWeChatBackupsFolder.Length <= 0)
                    mAloneWeChatBackupsFolder = Path.Combine(Folder.QuickBackupFolder, "WeChat");
                return mAloneWeChatBackupsFolder;
            }
        }

        // 使用iTunes备份数据的目录
        public static string MobileSyncBackupFolder
        {
            get
            {
                string result = "";

#if MAC
				string s = Environment.GetFolderPath(Environment.SpecialFolder.Personal).TrimEnd('/');
				result = string.Format("{0}/Library/Application Support/MobileSync/Backup/", s);
#else
                result = Path.Combine(Environment.GetFolderPath(
                                    Environment.SpecialFolder.ApplicationData).TrimEnd(new char[] { '\\' }),
                                  @"Apple Computer\MobileSync\Backup\");
#endif

                if (!string.IsNullOrEmpty(result) && !Directory.Exists(result))
                    Folder.CheckFolder(result);

                return result;
            }
        }

        /// iCloud backup folder ---Added by Utmost20171130
        /// 
        public static string iCloudBackupFolder
        {
            get
            {
                return Path.Combine(Folder.DocumentFolder, "iCloud-Backup") + Path.DirectorySeparatorChar.ToString();
            }
        }

#endregion

#region "--- 路径相关函数 ---"

        /// <summary>
        /// 检查需要使用的一些文件夹是否存在，不存在则自动创建
        /// </summary>
        /// <returns>是否创建成功</returns>

        public static void CheckFolder()
        {
            try
            {
                string strPath = Folder.TempFolder.Trim(System.IO.Path.DirectorySeparatorChar);
                if (File.Exists(strPath))
                {
                    try
                    {
                        FileInfo info = new FileInfo(strPath);
                        if (info.IsReadOnly == true)
                        {
                            info.IsReadOnly = false;
                        }
                        File.Delete(strPath);
                    }
                    catch (Exception ex)
                    {
                        Utility.LogException(ex.ToString(), "Folder.CheckFolder");
                    }
                }

                //1、创建临时目录
                if (!Folder.CheckFolder(Folder.TempFolder))
                {
                    mCreateTempSuccessed = false;

                    Folder.CheckFolder(Folder.TempFolder);
                }

                //2、在我的文档下创建目录
                bool blnRetry = false;

            DO_RETRY:
#if IOS
                Folder.CheckFolder(Folder.DownloadFolder, true);
                Folder.CheckFolder(Folder.PublicStaging, true);
#else

                Folder.CheckFolder(Folder.DocumentFolder, true);
                Folder.CheckFolder(Folder.ExceptionFolder, true);
                Folder.CheckFolder(Folder.LogFolder, true);

                if (mAppType == RunType.AirDroid)
                {
                    Folder.CheckFolder(Folder.CacheAppIconFolder, true);
                    Folder.CheckFolder(Folder.CacheDeviceIconFolder, true);
                    Folder.CheckFolder(Folder.CacheContact, true);
                    Folder.CheckFolder(Folder.ScreenShotFolder, true);
                    Folder.CheckFolder(Folder.CacheWebFolder, true);
                }
                else if (mAppType == RunType.Tongbu || mAppType == RunType.Tongbu_Abroad || mAppType == RunType.TongbuLite)
                {
                    Folder.CheckFolder(Folder.DownloadFolder, true);
                    Folder.CheckFolder(Folder.PublicStaging, true);
                    Folder.CheckFolder(Folder.CacheFolder, true);
                    Folder.CheckFolder(Folder.CachePackagesFolder, true);
                    Folder.CheckFolder(Folder.FirmwareFolder, true);
                    Folder.CheckFolder(Folder.CacheAppInfo, true);

                    Folder.CheckFolder(Folder.LyricsFolder, true);
                    Folder.CheckFolder(Folder.AlbumThumbsFolder, true);
                    Folder.CheckFolder(Folder.MusicFolder, true);
                    Folder.CheckFolder(Folder.RingtoneFolder, true);
                    Folder.CheckFolder(Folder.M4rFolder, true);
                    Folder.CheckFolder(Folder.Mp4Folder, true);
                    Folder.CheckFolder(Folder.WallPaperFolder, true);
                    Folder.CheckFolder(Folder.BackupFolder, true);
                    Folder.CheckFolder(Folder.VideoFolder, true);
                    Folder.CheckFolder(Folder.Plugins, true);
                    Folder.CheckFolder(Folder.Emoticon, true);
                    Folder.CheckFolder(Folder.FirmwareFolder, true);
                    Folder.CheckFolder(Folder.BackupAppRecordFolder, true);
                    Folder.CheckFolder(Folder.SCInfo);
                    Folder.CheckFolder(Folder.tbTui);
                    //Folder.CheckFolder(Folder.QuickBackupFolder, true);
                    //Folder.CheckFolder(Folder.AloneWeChatBackupsFolder, true);
                    //Folder.CheckFolder(Folder.AloneWeChatSpareBackupsFolder, true);
                    //Folder.CheckFolder(Folder.AloneWeChatVIPSpareBackupsFolder, true);
                    Folder.CheckFolder(Folder.DefaultExportFolder, true);
                    //Folder.CheckFolder(Folder.iTunesPlugin, true);
                }


                if (!mCreateDocSuccessed && !blnRetry)
                {
                    blnRetry = true;
                    goto DO_RETRY;
                }
#endif
            }
            catch (Exception ex2)
            {
                Utility.LogException(ex2.ToString(), "Folder.CheckFolder.2");
            }
        }

        public static bool CheckFolder(string path)
        {
            string errMsg = string.Empty;
            return CheckFolder(path, ref errMsg);
        }

        /// <summary>
        ///  检查需要使用的一些文件夹是否存在，不存在则自动创建
        /// </summary>
        /// <param name="path">检测路径</param>
        /// <returns>是否创建成功</returns>
        public static bool CheckFolder(string path, ref string errMsg)
        {
            try
            {
                string pathShare = Path.DirectorySeparatorChar.ToString() + Path.DirectorySeparatorChar.ToString();
                //兼容共享目录，以\\开头  add by zsh
                bool isStartWithShared = path.StartsWith(pathShare);

                //将传入的路径拆分开来，拆分之前保证所有的目录分隔符都是 “\”
                if (!Directory.Exists(path))
                {
                    string[] aryPath = PathFormat(path).Split(new char[] { Path.DirectorySeparatorChar }, StringSplitOptions.RemoveEmptyEntries);

                    //逐级检查每一级的目录是否存在
                    for (int intIndex = 1; intIndex <= aryPath.Length; intIndex++)
                    {
                        string pathParts = string.Join(Path.DirectorySeparatorChar.ToString(), aryPath, 0, intIndex);
                        if (isStartWithShared)
                        {
                            //如果是共享目录 第一级默认不创建 如 \\192.168.40.100\临时文件夹\用完删除\测试  第一级\\192.168.40.100 都会存在  by cbh
                            if (intIndex == 1)
                                continue;

                            pathParts = pathShare + pathParts;
                        }

#if MAC || IOS
						pathParts = "/" + pathParts;
#endif

                        if (!string.IsNullOrEmpty(pathParts) && !Directory.Exists(pathParts))
                        {
                            try
                            {
                                Directory.CreateDirectory(pathParts);
                            }
                            catch (Exception ex)
                            {
                                errMsg = "CheckFolder: " + pathParts + "\r\n" + ex.ToString();
                                Utility.LogException(path + "\r\n" + ex.ToString(), "Folder.CheckFolder.3");
                                return false;
                            }
                        }
                    }

                    return Directory.Exists(path);
                }
                else
                {
                    return true;
                }
            }
            catch (Exception ex)
            {
                errMsg = path + "\r\n" + ex.ToString();
                Utility.LogException(path + "\r\n" + ex.ToString(), "Folder.CheckFolder");
                return false;
            }
        }

        //在我的文档下创建目录可调用这个方法（如果有失败，则将此目录指向程序目录下）
        private static void CheckFolder(string path, bool blnCheckDoc)
        {
            if (!CheckFolder(path) && blnCheckDoc)
            {
                mCreateDocSuccessed = false;
            }
        }

        /// <summary> 
        /// 清除自动创建的临时目录 
        /// </summary> 
        /// <returns>是否清除成功</returns> 
        public static bool ClearFolder()
        {
            return ClearFolder(TempFolder);
        }

        public static bool ClearFolder(string path)
        {
            bool blnReuslt = true;

            if (!Directory.Exists(path))
                goto DoExit;

            foreach (string dir in Directory.GetDirectories(path))
            {
                try
                {
                    Directory.Delete(dir, true);
                }
                catch
                {
                    ClearFolder(dir);
                    blnReuslt = false;
                }
            }

            foreach (string file in Directory.GetFiles(path))
            {
                try
                {
                    File.Delete(file);
                }
                catch
                {
                    blnReuslt = false;
                }
            }

        DoExit:
            return blnReuslt;
        }

        /// <summary>
        /// 返回以 "\" 结束的路径
        /// </summary>
        /// <param name="srcPath">路径</param>
        /// <returns></returns>
        public static string PathFormat(string srcPath)
        {
#if MAC || IOS
            return srcPath.Replace("\\", "/").TrimEnd('/') + "/"; ;
#else
            return srcPath.Replace("/", "\\").TrimEnd(System.IO.Path.DirectorySeparatorChar) + "\\";
#endif
        }

        public static string GetTempFolder()
        {
            return GetTempFilePath("");
        }

        public static string GetTempFilePath()
        {
            return GetTempFilePath(".tmp");
        }

        public static string GetTempFilePath(string ext)
        {
            return Path.Combine(TempFolder, Guid.NewGuid().ToString("N").Substring(0, 8)) + ext;
        }

        public static string GetTempFilePathByName(string strName)
        {
            return Path.Combine(TempFolder, strName);
        }

        public static string GetAppIconPath(string identifier, string strVersion)
        {
            string strWinIllegalName = Utility.ReplaceWinIllegalName(identifier, false);
            string strIconPath = Path.Combine(Folder.CacheFolder, "Apps");

            if (!Directory.Exists(strIconPath))
                Directory.CreateDirectory(strIconPath);

            strIconPath = Path.Combine(strIconPath, strWinIllegalName);

            if (!Directory.Exists(strIconPath))
                Directory.CreateDirectory(strIconPath);

            if (strVersion.Length > 0)
                strIconPath = Path.Combine(strIconPath, Utility.ReplaceWinIllegalName(string.Format("{0}.png", strVersion)));
            else
                strIconPath = Path.Combine(strIconPath, Utility.ReplaceWinIllegalName(string.Format("{0}.png", strWinIllegalName)));

            return strIconPath;
        }

        public static string GetPathRoot(string strPath)
        {

#if MAC
			return TempFolder;
#else
            return Path.GetPathRoot(strPath);
#endif
        }

        public static string GetDesktopPath(string fileName)
        {
            return Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory), fileName);
        }

        public static void ClearOldDir(string strFolder)
        {
            ClearOldDir(strFolder, 30);
        }

        public static void ClearOldDir(string strFolder, int overDays)
        {
            if (!Directory.Exists(strFolder))
                return;

            foreach (string dir in Directory.GetDirectories(strFolder))
            {
                DirectoryInfo info = new DirectoryInfo(dir);
                if (DateTime.Now.Subtract(info.CreationTime).TotalDays > overDays)
                    Utility.DeleteDirectory(dir);
            }

            foreach (string file in Directory.GetFiles(strFolder))
            {
                FileInfo info = new FileInfo(file);
                if (DateTime.Now.Subtract(info.CreationTime).TotalDays > overDays)
                    Utility.DeleteFile(file);
            }
        }


        private static string mPersonSignatureFolder = "";
        /// <summary>
        /// 个人签名设置目录
        /// </summary>
        public static string PersonSignatureLogFolder
        {
            get
            {
                if (mPersonSignatureFolder.Length > 0)
                {
                    return mPersonSignatureFolder;
                }

                mPersonSignatureFolder = Path.Combine(Folder.AppFolder, "PersonSignature");

                if (Folder.AppType == RunType.ZJHelper)
                {
                    mPersonSignatureFolder = Path.Combine(Folder.DocumentFolder, "PersonSignature");
                }

                return mPersonSignatureFolder;
            }
        }

        public static string PersonSignatureConfigIniFile
        {
            get { return Path.Combine(PersonSignatureLogFolder, string.Format("Setting{0}.ini", DateTime.Now.ToString("yyyyMM"))); }
        }

#endregion
    }
}