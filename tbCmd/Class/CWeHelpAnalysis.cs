﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace iTong.CoreFoundation
{

    public class CWeHelpAnalysis
    {
        private static string iniPath = Path.Combine(Folder.AppFolder);

        private static string wxid;
        private static string nickname;

        public delegate void WXBackupKeyCB(string key, int keylen);
        public delegate void WXContactCB(IntPtr contact, int len);
        public delegate void WXLogCB(string log, int len);

        [DllImport(".\\CWeHelp.dll", EntryPoint = "StartWeHelpWork", CallingConvention = CallingConvention.Cdecl)]
        private static extern int StartWeHelpWork(IntPtr MicroMsgDBPath, int dbPathSize, string Offset, IntPtr ContactPath, int ContactPathSize,
                                        string UserName, //wxid
                                         string AliasName, //用户设置的微信号
                                         int OnlyContact,//0获取密钥和联系人，1(非0)为只获取联系人
                                         WXBackupKeyCB keyCB, WXContactCB ContactCB, WXLogCB LogCB);

        public static void Analysis(string _wxid, string _nickname, string dbpath, string offset, string mContactPath, string strUserName, string strAliasName, int intOnlyContact)
        {
            if (File.Exists(Path.Combine(iniPath, "iWechatAssistant.ini")))
                File.Delete(Path.Combine(iniPath, "iWechatAssistant.ini"));

            wxid = _wxid;
            nickname = _nickname;

            byte[] dbbuf = System.Text.Encoding.UTF8.GetBytes(dbpath);
            IntPtr dbPtr = Marshal.UnsafeAddrOfPinnedArrayElement(dbbuf, 0);
            byte[] contactbuf = System.Text.Encoding.UTF8.GetBytes(mContactPath);
            IntPtr contactPtr = Marshal.UnsafeAddrOfPinnedArrayElement(contactbuf, 0);

            int ret = StartWeHelpWork(dbPtr, dbbuf.Length, offset, contactPtr,
                                  contactbuf.Length, strUserName, strAliasName, intOnlyContact,
                                  backupkey_callback, contact_callback, log_callback);

            IniClass.SetIniSectionKey(wxid, "ret", ret.ToString(), Path.Combine(iniPath, "iWechatAssistant.ini"));
        }


        private static void contact_callback(IntPtr contact, int len)
        {
            Common.Log("Do--2");
            try
            {
                byte[] bt = new byte[len];
                Marshal.Copy(contact, bt, 0, len);
                IniClass.SetIniSectionKey(wxid, "mStrContactPath", System.Text.Encoding.UTF8.GetString(bt), Path.Combine(iniPath, "iWechatAssistant.ini"));
                Common.LogException("contact_callback", "contact_callback");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "contact_callback");
            }

        }

        private static void backupkey_callback(string key, int keylen)
        {
            Common.Log("Do--1");
            try
            {
                IniClass.SetIniSectionKey(wxid, "pwd", key, Path.Combine(iniPath, "iWechatAssistant.ini"));
                IniClass.SetIniSectionKey(wxid, "nickname", nickname, Path.Combine(iniPath, "iWechatAssistant.ini"));
                Common.LogException("backupkey_callback", "backupkey_callback");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "backupkey_callback");
            }
        }


        private static void log_callback(string log, int len)
        {
            Common.LogException(log, "log_callback");

            if (log.StartsWith("[Ver];"))
            {
                IniClass.SetIniSectionKey(wxid, "log", log, Path.Combine(iniPath, "iWechatAssistant.ini"));
            }
        }
    }
}
