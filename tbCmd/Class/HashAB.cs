﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.Web;
using System.IO;
using System.Security.Cryptography;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace iTong.CoreFoundation
{
    internal static class HashAB
    {
        public delegate void CalcHashABHandler(IntPtr result, IntPtr digest, IntPtr serial);
        public delegate void FinishHashHandler(IntPtr serial);

        private static IntPtr hFinishKey;
        private static IntPtr hUUID;
        private static IntPtr hSha1;
        private static IntPtr hHashAB;

        private static CalcHashABHandler CalcHashAB;

        static HashAB()
        {
            byte[] bufLibrary = global::tbCmd.Properties.Resources.Library;
            byte[] arrFinish = new byte[] { 0x70, 0x6B, 0x66, 0x1F, 0x9E, 0xD1, 0x5B, 0xA2, 0x78, 0x14, 0x7A, 0x76, 0x96, 0x62, 0x00 };

            hFinishKey = Marshal.AllocCoTaskMem(arrFinish.Length);
            hUUID = Marshal.AllocCoTaskMem(40);
            hSha1 = Marshal.AllocCoTaskMem(40);
            hHashAB = Marshal.AllocCoTaskMem(114);
            int library = MemoryLibrary.MemoryLoadLibrary(bufLibrary, false);

            int procFinish = MemoryLibrary.MemoryGetProcAddress(library, "Tongbu_FinishHash");
            FinishHashHandler FinishHash = (FinishHashHandler)Marshal.GetDelegateForFunctionPointer(new IntPtr(procFinish), typeof(FinishHashHandler));

            int procAscii = MemoryLibrary.MemoryGetProcAddress(library, "Tongbu_Ascii");
            CalcHashAB = (CalcHashABHandler)Marshal.GetDelegateForFunctionPointer(new IntPtr(procAscii), typeof(CalcHashABHandler));

            Marshal.Copy(arrFinish, 0, hFinishKey, arrFinish.Length);

            FinishHash(hFinishKey);
        }

        public static byte[] CalculateHash(byte[] sha1s, string UDID)
        {
            byte[] arrReturn = new byte[57];
            byte[] arrTemp = new byte[114];

            string strTemp = string.Empty;
            int index = 0;

            byte[] arrUUID = Encoding.ASCII.GetBytes(UDID);

            Marshal.Copy(arrUUID, 0, hUUID, arrUUID.Length);

            string strSha1 = string.Empty;
            for (int i = 0; i < sha1s.Length; i++)
                strSha1 += Convert.ToString(sha1s[i], 16).PadLeft(2, '0');

            byte[] arrSha1 = Encoding.ASCII.GetBytes(strSha1);

            Marshal.Copy(arrSha1, 0, hSha1, 40);

            CalcHashAB(hHashAB, hSha1, hUUID);
            Marshal.Copy(hHashAB, arrTemp, 0, arrTemp.Length);
            strTemp = Encoding.ASCII.GetString(arrTemp);
            for (int i = 0; i < strTemp.Length; i += 2)
            {
                arrReturn[index] = Convert.ToByte(strTemp.Substring(i, 2), 16);
                index++;
            }

            return arrReturn;
        }

    }
}