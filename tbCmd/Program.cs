﻿using System;
using System.Collections.Generic;
using System.Text;
using System.IO;
using System.Runtime.InteropServices;
using System.Diagnostics;
using Microsoft.Win32;

using iTong.CoreFoundation;

#if NET40 && CMD
using AirCmd;
#endif

#if NET46 || NET40
using System.Linq;
using System.Security.AccessControl;
using System.Security.Principal;
using System.Threading;

#if NET46
using iTong.Android;
using iTong.CoreModule;
#endif

#endif

namespace iTong.CoreFoundation
{
    class Program
    {
        #region QTConfig

        const string DllQTConfig = "QTConfig.dll";

        [DllImport(DllQTConfig, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool QT_EnableConfig(string uuid);

        #endregion

        [DllImport("Kernel32.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool SetDllDirectory(string lpPathName);

        [DllImport("tbChatWebPage.temp", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Aud2Wav(string armFile, string wavFile, string key);

        [DllImport("tbChatWebPageEx.temp", CallingConvention = CallingConvention.Cdecl)]
        public static extern int SilkToWav(string silkfilename, string wavfilename);

        public static void SetBluetoothInfo(string[] arrArgs)
        {
            if (arrArgs.Length < 2)
                return;
#if NET40
            try
            {
                DllHelper.DIH_Initialize();
                bool blnBLESupport = DllHelper.DIH_CheckPeripheralRoleSupport();
                bool blnIsOpenBluetooth = DllHelper.DIH_IsOpenBluetooth();

                string filePath = arrArgs[1];
                string dirPath = Path.GetDirectoryName(filePath);

                Folder.CheckFolder(dirPath);
                string text = string.Format("{0};{1}", blnBLESupport.ToString(), blnIsOpenBluetooth.ToString());

                File.WriteAllText(filePath, text);

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SetBluetoothInfo");
            }
#endif
        }

        public static string PostData(string strUrl, byte[] postBuffer, int timeout, string contentType)
        {
            string strResult = string.Empty;

            try
            {
                if (string.IsNullOrEmpty(strUrl))
                    goto DoExit;

                System.Net.HttpWebRequest webRequest = null;
                System.Net.HttpWebResponse webResponse = null;
                System.IO.Stream requestStream = null;
                System.IO.Stream responseStream = null;

                webRequest = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(strUrl);

                if (string.IsNullOrEmpty(contentType))
                    contentType = "application/x-www-form-urlencoded";

                webRequest.Method = "POST";
                webRequest.ContentType = contentType;
                //在向服务器发送大量的文本、包含非ASCII字符的文本或二进制数据时这种编码方式效率很低
                //webRequest.ContentType = "multipart/form-data"                '既可以发送文本数据，也支持二进制数据上载
                webRequest.KeepAlive = false;
                webRequest.ContentLength = postBuffer.Length;
                webRequest.Timeout = timeout;
                webRequest.AllowAutoRedirect = true;

                requestStream = webRequest.GetRequestStream();
                requestStream.Write(postBuffer, 0, postBuffer.Length);
                requestStream.Close();

                webResponse = (System.Net.HttpWebResponse)webRequest.GetResponse();
                responseStream = webResponse.GetResponseStream();

                int intCount = 0;
                List<byte> listByte = new List<byte>();
                do
                {
                    byte[] buffer = new byte[5012];
                    intCount = responseStream.Read(buffer, 0, buffer.Length);
                    for (int intI = 0; intI <= intCount - 1; intI++)
                    {
                        listByte.Add(buffer[intI]);
                    }

                    //System.Windows.Forms.Application.DoEvents()
                } while (intCount > 0);
                responseStream.Close();

                strResult = Encoding.UTF8.GetString(listByte.ToArray());
            }
            catch (Exception ex)
            {
                //Console.WriteLine(strUrl + "\r\n" + ex.ToString());
                Common.LogException(ex.ToString(), "Util_PostData");
            }

            DoExit:
            return strResult;
        }

        public static void CreateFile(string strFile)
        {
            try
            {
                DeleteFile(strFile);

                FileStream fs = File.Create(strFile);
                fs.Close();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateFile");
            }
        }

        public static void DeleteFile(string strFile)
        {
            try
            {
                if (File.Exists(strFile))
                    File.Delete(strFile);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DeleteFile");
            }
        }


        private static void SendData(string[] arrArgs)
        {
            string fileFlag = string.Empty;

            try
            {
                if (arrArgs.Length < 3)
                    return;

                string url = arrArgs[1];
                string dir = arrArgs[2];

                if (string.IsNullOrEmpty(url))
                    return;

                if (!Directory.Exists(dir))
                    return;

                fileFlag = Path.Combine(dir, "flag");
                if (File.Exists(fileFlag))
                {
                    //使用文件来记得发送程序是不是正在运行
                    FileInfo fileInfo = new FileInfo(fileFlag);
                    if (DateTime.Now.Subtract(fileInfo.CreationTime).TotalMinutes < 2)
                        return;
                }

                CreateFile(fileFlag);

                while (true)
                {
                    string[] arrFiles = Directory.GetFiles(dir, "*.sd", SearchOption.AllDirectories);
                    if (arrFiles.Length == 0)
                        break;

                    foreach (string strFile in arrFiles)
                    {
                        byte[] arrData = File.ReadAllBytes(strFile);
                        if (arrData.Length == 0)
                        {
                            File.Delete(strFile);
                            continue;
                        }

                        CreateFile(fileFlag);

                        string strLog = PostData(url, arrData, 30000, string.Empty);
                        if (strLog == "1")
                        {
                            DeleteFile(strFile);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SendData");
            }
            finally
            {
                DeleteFile(fileFlag);
            }
        }

        public static void SetEnableConfig(string[] arrArgs)
        {
            if (arrArgs.Length < 2)
                return;

            try
            {
                string uuid = arrArgs[1];
                bool blnResult = QT_EnableConfig(uuid);

                string filePath = arrArgs[2];
                string dirPath = Path.GetDirectoryName(filePath);

                Folder.CheckFolder(dirPath);

                File.WriteAllText(filePath, blnResult ? "1" : "");

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SetBluetoothInfo");
            }
        }

        public static void SendMessage(string title,int msg)
        {
#if NET46
            MyAPI.SendMessage(title, msg);
#endif
        }

#if NET40
        [MTAThread]
#else
        [STAThread]
#endif
        public static int Main(string[] arrArgs)
        {
            try
            {
                if (arrArgs == null || arrArgs.Length == 0)
                {
                    string BLSupport = Path.Combine(Folder.AppFolder, "BLSupport.txt");
                    if (File.Exists(BLSupport))
                    {
                        arrArgs = new string[] { "Bluetooth", BLSupport };
                    }
#if DEBUG
                    else
                    {
                        arrArgs = new string[] { "StartAppleMobileDeviceProcess" };
                    }
#else
                    else
                    {
                        return;
                    }
#endif
                }

                int iResult = 0;

                string strCmd = arrArgs[0];

                ///兼容msi kill程序问题
                if (strCmd.Contains("Msi"))
                {
                    string[] pars = arrArgs[0].Split('#');
                    strCmd = pars[0];
                    arrArgs = pars;
                }

                switch (strCmd)
                {
                    case "SilkToWav":
                        if (arrArgs.Length == 3)
                        {
                            SetDllDirectory(Path.Combine(Folder.CacheFolder, "RubbishClearWebPage"));
                            iResult = SilkToWav(arrArgs[1], arrArgs[2]);
                        }
                        break;

                    case "Aud2Wav":
                        if (arrArgs.Length == 4)
                        {
                            SetDllDirectory(Path.Combine(Folder.CacheFolder, "RubbishClearWebPage"));
                            iResult = Aud2Wav(arrArgs[1], arrArgs[2], arrArgs[3]);
                        }
                        break;

                    case "Hash72":
                    case "HashAB":
                        if (arrArgs.Length == 4)
                        {
                            string strSHA1 = arrArgs[1];
                            string strUDID = arrArgs[2];
                            string strDes = arrArgs[3];

                            byte[] arrHash = null;
                            if (strCmd == "HashAB")
                                arrHash = HashAB.CalculateHash(Common.FromHexString(strSHA1), strUDID);
                            else
                                arrHash = Hash72.CalculateHash(Common.FromHexString(strSHA1), strUDID);
                            File.WriteAllBytes(strDes, arrHash);
                        }
                        break;

                    case "Bluetooth":
                        SetBluetoothInfo(arrArgs);
                        break;

                    case "SendData":
                        SendData(arrArgs);
                        break;

                    case "EnableConfig":
                        SetEnableConfig(arrArgs);
                        break;



                    case "iWechatAssistant40":
#if !NET20
                        CWeHelpAnalysis.Analysis(arrArgs[1], arrArgs[2], arrArgs[3], arrArgs[4], arrArgs[5], arrArgs[6], arrArgs[7], int.Parse(arrArgs[8]));
#endif
                        break;

                    case "ReStartWx":

                        System.Diagnostics.Process[] pros = System.Diagnostics.Process.GetProcessesByName("WeChat");
#if !NET20
                        if (pros.Length > 0)
                        {
                            System.Diagnostics.Process pro = pros[0];
                            string wechatpath = pro.MainModule.FileName;
                            pro.Kill();
                            System.Diagnostics.Process.Start(wechatpath);
                        }
#endif
                        break;

#if NET46
                    case iTunesHelper.CMD_StartAppleMobileDeviceProcess:
                        iTunesHelper.StartAppleMobileDeviceProcess(arrArgs[1]);
                        break;

                    case iTunesHelper.CMD_StartAppleMobileDeviceService:
                        iTunesHelper.StartAppleMobileDeviceService();
                        break;

                    case iTunesHelper.CMD_SetAcl:
                        string iTunesFolder = arrArgs[1];

                        //iTunesHelper.BackupAcl(arrArgs[1], "iTunesOK.acl");
                        iTunesHelper.SetAcl(Folder.WindowsAppsFolder, "", false);
                        iTunesHelper.SetAcl(iTunesFolder, "iTunes", true);

                        iTunesHelper.SetRegistryForAppleMobileDeviceSupport(arrArgs[1]);
                        break;

                    case AppHelper.CMD_GetAppListFromWindowsApp:
                        {
                            string pathLog = arrArgs[1];
                            try
                            {
                                List<BizAppInfo> list = AppHelper.GetInstallAppListFromWindowsApps();

                                string strLog = MyJson.SerializeToJsonString(list);

                                File.WriteAllText(pathLog, strLog, Encoding.UTF8);
                            }
                            catch (Exception ex)
                            {
                                File.WriteAllText(Path.Combine(Path.GetDirectoryName(pathLog), AppHelper.CMD_GetAppListFromWindowsApp + "Error.txt"), ex.Message, Encoding.UTF8);
                                File.WriteAllText(pathLog, "", Encoding.UTF8);
                            }
                        }
                        break;

                    case AppHelper.CMD_GetAppxFromName:
                        {
                            string pathLog = arrArgs[1];
                            string name = arrArgs[2];
                            try
                            {
                                List<BizAppInfo> list = AppHelper.GetInstallAppListFromWindowsApps(name);

                                string strLog = MyJson.SerializeToJsonString(list);

                                File.WriteAllText(pathLog, strLog, Encoding.UTF8);
                            }
                            catch (Exception ex)
                            {
                                File.WriteAllText(pathLog, ex.Message, Encoding.UTF8);
                            }
                        }
                        break;

                    case AppHelper.CMD_AddAppx:
                        {
                            string pathLog = arrArgs[1];
                            string path = arrArgs[2];
                            try
                            {
                                string strLog = AppHelper.InstallAppX(path);

                                File.WriteAllText(pathLog, strLog, Encoding.UTF8);
                            }
                            catch (Exception ex)
                            {
                                File.WriteAllText(pathLog, ex.Message, Encoding.UTF8);
                            }
                        }
                        break;

                    case AppHelper.CMD_GetAppListFromCurrentUser:
                        {
                            string pathLog = arrArgs[1];
                            try
                            {
                                List<BizAppInfo> list = AppHelper.GetInstallAppListFromRegistryOfUninstall(RegistryHive.CurrentUser);

                                string strLog = MyJson.SerializeToJsonString(list);

                                File.WriteAllText(pathLog, strLog, Encoding.UTF8);
                            }
                            catch (Exception ex)
                            {
                                File.WriteAllText(pathLog, ex.Message, Encoding.UTF8);
                            }
                        }
                        break;

                    case AppHelper.CMD_GetAppAUMID:
                        {
                            string pathLog = arrArgs[1];
                            try
                            {
                                string strLog = AppHelper.GetAUMIDListHandle();

                                File.WriteAllText(pathLog, strLog, Encoding.UTF8);
                            }
                            catch (Exception ex)
                            {
                                File.WriteAllText(pathLog, ex.Message, Encoding.UTF8);
                            }
                        }
                        break;

                        //主要用于当前rs、daemon架构上server不能直接发送消息给界面的用法
                    case "SendMsg":
                        {
                            string pathLog = arrArgs[1];
                            if (arrArgs.Length < 4)
                                goto DoWriteFile;

                            string title = arrArgs[2].Replace("&_&", " ");
                            int msg = arrArgs[3].ToInt();

                            SendMessage(title, msg);

                            DoWriteFile:
                            File.WriteAllText(pathLog, pathLog, Encoding.UTF8);
                        }
                        break;

                    case "MsiKillExe":
                        {
                            string bizFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "BizDaemon");
                            string LogTmd40 = Path.Combine(bizFolder, "Logs", "tmd40_Operation.txt");
                            File.AppendAllText(LogTmd40, $"{DateTime.Now.ToString()} ---------- Start to Send Biz Win32 Message For Hide NotifyIcon In Tmd40 503 Lines\n");

                            string[] pars = arrArgs[1].Split(';');
                            SendMessage("AirDroid Biz Daemon-Windows MDM Solution", MyAPI.WM_HideNotifyIcon);

                            //Thread.Sleep(1000);

                            foreach (string par in pars)
                            {
                                string name = par.Replace("_", " ");

                                MsiHelper.KillProces(name);
                                MsiHelper.StopService(name);
                                MsiHelper.DeleteService(name);

                                //Utility.DeleteDirectory(Path.Combine(bizFolder, "Logs"));
                                //Utility.DeleteDirectory(Path.Combine(bizFolder, "DownloadApp"));
                                //Utility.DeleteDirectory(Path.Combine(bizFolder, "Cache"));
                                //Utility.DeleteFile(Path.Combine(bizFolder, "AppList.txt"));
                            }

                            string logFolder = Path.Combine(bizFolder, "Logs", "WebView2");
                            Utility.DeleteDirectory(logFolder);
                            Utility.DeleteFile(Path.Combine(bizFolder, "OpenOK.dll"));
                            Utility.DeleteFile(Path.Combine(bizFolder, "OneTime.ini"));
                        }
                        break;
                    case "Msi_MoveLogToTemp":
                        {
                            string[] pars = arrArgs[1].Split(';');
                            foreach (string par in pars)
                            {
                                string name = par.Replace("_", " ");
                                string logFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), name);
                                Utility.MoveDirectory(logFolder, Path.Combine(Path.GetTempPath(), name, "LogData"));
                                Utility.DeleteDirectory(logFolder);
                                string tempPath = Path.Combine(Path.GetTempPath(), name, "LogData");
                                Utility.DeleteFile(Path.Combine(tempPath, "OneTime.ini"));
                            }
                        }
                        break;
                    case "Msi_MoveLogToLogFolder":
                        {
                            string[] pars = arrArgs[1].Split(';');
                            bool autoStartApp = arrArgs[2].ToBool();

                            foreach (string par in pars)
                            {
                                string name = par.Replace("_", " ");
                                string logFolder = Path.Combine(Path.GetTempPath(), name, "LogData");
                                Utility.MoveDirectory(logFolder, Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), name));
                                Utility.DeleteDirectory(logFolder);
                                string tempPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), name);
                                Utility.DeleteFile(Path.Combine(tempPath, "OneTime.ini"));
                            }

                            string appPath = Path.Combine(Folder.AppFolder, "Launcher.exe");
                            if (File.Exists(appPath) && autoStartApp)
                                Process.Start(appPath);
                        }
                        break;
                    case "LockScreen":
                        {
                            MyAPI.LockWorkStation();
                        }
                        break;
                    case "SetRegistry":
                        {
                            try
                            {
                                string[] pars = arrArgs[1].Split(new string[] { "#_#" },StringSplitOptions.RemoveEmptyEntries);
                                if (pars.Length >= 4)
                                {
                                    MyRegistry.SetRegisterValueOnWOW6432Node(pars[0].Replace("@@", " "), pars[1].Replace("@@", " "), pars[2].Replace("@@", " "), (RegistryHive)int.Parse(pars[3]));
                                }
                            }
                            catch (Exception ex)
                            {
                                Common.LogException(ex);
                            }
                        }
                        break;
#endif
                        }

                if (iResult == 0)
                    Common.Log(string.Join("\r\n", arrArgs));
                else
                    Common.LogException(string.Join("\r\n", arrArgs));

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString() + "\r\n" + string.Join("\r\n", arrArgs), "tbCmd.Main");
            }
            return 1;
        }
    }
}
