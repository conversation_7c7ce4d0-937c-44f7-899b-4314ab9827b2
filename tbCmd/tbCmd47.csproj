﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{2531A4CB-C43F-48AA-8532-1C4039675597}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>tbCmd40</RootNamespace>
    <AssemblyName>tbCmd40</AssemblyName>
    <TargetFrameworkVersion>v4.7</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>TRACE;DEBUG;NET40;X64;TONG;NET46;TBCMD;</DefineConstants>
    <DebugType>full</DebugType>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>TRACE;DEBUG;NET40;X64;TONG;NET46;TBCMD;</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>TRACE;DEBUG;NET40;TONG;NET46;TBCMD;</DefineConstants>
    <DebugType>full</DebugType>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <DefineConstants>TRACE;DEBUG;NET40;TONG;NET46;TBCMD;</DefineConstants>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup />
  <PropertyGroup>
    <ApplicationManifest>app_47.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Management" />
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Web" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="..\AppUnion\zClass\DllImport.cs">
      <Link>Class\DllImport.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zClass\MyRegistry.cs">
      <Link>Class\MyRegistry.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zCommon\_ClassExtend.cs">
      <Link>Class\_ClassExtend.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zControl\MyAPI.cs">
      <Link>Class\MyAPI.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zServer\API\Info\InfoForBiz.cs">
      <Link>Class\InfoForBiz.cs</Link>
    </Compile>
    <Compile Include="..\AppUnion\zSystem\AppHelper.cs">
      <Link>Class\AppHelper.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\iTunesHelper.cs">
      <Link>Class\iTunesHelper.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\Disposable.cs">
      <Link>Json\Disposable.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonArray.cs">
      <Link>Json\IJsonArray.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonBoolean.cs">
      <Link>Json\IJsonBoolean.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonNull.cs">
      <Link>Json\IJsonNull.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonNumber.cs">
      <Link>Json\IJsonNumber.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonObject.cs">
      <Link>Json\IJsonObject.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonString.cs">
      <Link>Json\IJsonString.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonType.cs">
      <Link>Json\IJsonType.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonTypeFactory.cs">
      <Link>Json\IJsonTypeFactory.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IJsonWriter.cs">
      <Link>Json\IJsonWriter.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\IndentedJsonWriter.cs">
      <Link>Json\IndentedJsonWriter.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonArray.cs">
      <Link>Json\JsonArray.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonBoolean.cs">
      <Link>Json\JsonBoolean.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonNull.cs">
      <Link>Json\JsonNull.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonNumber.cs">
      <Link>Json\JsonNumber.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonObject.cs">
      <Link>Json\JsonObject.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonParser.cs">
      <Link>Json\JsonParser.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonString.cs">
      <Link>Json\JsonString.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonStructType.cs">
      <Link>Json\JsonStructType.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonTokenType.cs">
      <Link>Json\JsonTokenType.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonTypeCode.cs">
      <Link>Json\JsonTypeCode.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonTypeFactory.cs">
      <Link>Json\JsonTypeFactory.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonTypeSkeleton.cs">
      <Link>Json\JsonTypeSkeleton.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\Json\JsonWriter.cs">
      <Link>Json\JsonWriter.cs</Link>
    </Compile>
    <Compile Include="..\CoreMisc\WaveApi\API.cs">
      <Link>Class\API.cs</Link>
    </Compile>
    <Compile Include="..\CoreModule_iOS\zCommon\MyJson.cs">
      <Link>Class\MyJson.cs</Link>
    </Compile>
    <Compile Include="..\CoreUtilCS\ClassDefine.cs">
      <Link>Class\ClassDefine.cs</Link>
    </Compile>
    <Compile Include="..\CoreUtilCS\RegistryKey64.cs">
      <Link>Class\RegistryKey64.cs</Link>
    </Compile>
    <Compile Include="Class\AppEnum.cs" />
    <Compile Include="Class\Common.cs" />
    <Compile Include="Class\Folder.cs" />
    <Compile Include="Class\Hash72.cs" />
    <Compile Include="Class\HashAB.cs" />
    <Compile Include="Class\MemoryLibrary.cs" />
    <Compile Include="Class\IniClass.cs" />
    <Compile Include="Class\CWeHelpAnalysis.cs" />
    <Compile Include="Class\MsiHelper.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App_47.config" />
    <None Include="app_47.manifest" />
    <None Include="Resources\Library" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources1.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PreBuildEvent>
    </PreBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <PostBuildEvent>copy $(ProjectDir)App_47.config $(TargetDir)$(TargetFileName).config</PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>