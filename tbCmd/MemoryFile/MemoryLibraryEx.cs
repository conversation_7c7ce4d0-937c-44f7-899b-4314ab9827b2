﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;

using BYTE = System.Byte;
using WORD = System.UInt16;
using DWORD = System.UInt32;
using BOOL = System.Int32;
using SIZE_T =System.IntPtr;

namespace tbCmd40
{
    public unsafe partial class MemoryLibraryEx
    {
        const BOOL TRUE = 1;
        const BOOL FALSE = 0;

        public delegate void IMAGE_TLS_CALLBACK(IntPtr DllHandle, DWORD Reason, IntPtr Reserved);
        public delegate IntPtr CustomLoadLibraryFunc(byte* strData, IntPtr a);
        public delegate IntPtr CustomGetProcAddressFunc(IntPtr hBase, byte* name, IntPtr a);
        public delegate void CustomFreeLibraryFunc(IntPtr hBase, IntPtr a);
        public delegate BOOL DllEntryProc(IntPtr hinstDLL, DWORD fdwReason, IntPtr lpReserved);
        public delegate int ExeEntryProc();

        static uint IMAGE_SIZEOF_BASE_RELOCATION = (uint)Marshal.SizeOf(typeof(IMAGE_BASE_RELOCATION));
        static uint[, ,] ProtectionFlags = new uint[2, 2, 2]{
    {
        // not executable
        {PAGE_NOACCESS, PAGE_WRITECOPY},
        {PAGE_READONLY, PAGE_READWRITE},
    }, {
        // executable
        {PAGE_EXECUTE, PAGE_EXECUTE_WRITECOPY},
        {PAGE_EXECUTE_READ, PAGE_EXECUTE_READWRITE},
    },};

        #region DllImport
        #if NET40

        [DllImport("kernel32.dll",CallingConvention=CallingConvention.Winapi)]
        private static extern IntPtr VirtualAlloc(IntPtr lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect);
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect);
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern int VirtualFree(IntPtr lpAddress, SIZE_T dwSize, DWORD dwFreeType);
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern int VirtualProtect(IntPtr lpAddress, SIZE_T dwSize, uint flNewProtect, DWORD* lpflOldProtect);

        [DllImport("kernel32.dll", EntryPoint = "LoadLibraryA", CallingConvention = CallingConvention.Winapi)]
        private static extern IntPtr LoadLibrary(byte* lpLibFileName);
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, byte* lpProcName);
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern int FreeLibrary(IntPtr hLibModule);
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        internal static extern int GetLastError();

        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern IntPtr HeapAlloc(IntPtr hHeap, DWORD dwFlags, SIZE_T dwBytes);
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern IntPtr GetProcessHeap();
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern bool HeapFree(IntPtr hHeap, DWORD dwFlags, IntPtr lpMem);

        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        private static extern void GetNativeSystemInfo(SYSTEM_INFO* info);

        #else

        [DllImport("kernel32.dll")]
        private static extern IntPtr VirtualAlloc(IntPtr lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect);
        [DllImport("kernel32.dll")]
        private static extern IntPtr VirtualAllocEx(IntPtr hProcess, IntPtr lpAddress, SIZE_T dwSize, DWORD flAllocationType, DWORD flProtect);
        [DllImport("kernel32.dll")]
        private static extern int VirtualFree(IntPtr lpAddress, SIZE_T dwSize, DWORD dwFreeType);
        [DllImport("kernel32.dll")]
        private static extern int VirtualProtect(IntPtr lpAddress, SIZE_T dwSize, uint flNewProtect, DWORD* lpflOldProtect);

        [DllImport("kernel32.dll", EntryPoint = "LoadLibraryA")]
        private static extern IntPtr LoadLibrary(byte* lpLibFileName);
        [DllImport("kernel32.dll")]
        private static extern IntPtr GetProcAddress(IntPtr hModule, byte* lpProcName);
        [DllImport("kernel32.dll")]
        private static extern int FreeLibrary(IntPtr hLibModule);
        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Cdecl)]
        internal static extern int GetLastError();

        [DllImport("kernel32.dll")]
        private static extern IntPtr HeapAlloc(IntPtr hHeap, DWORD dwFlags, SIZE_T dwBytes);
        [DllImport("kernel32.dll")]
        private static extern IntPtr GetProcessHeap();
        [DllImport("kernel32.dll")]
        private static extern bool HeapFree(IntPtr hHeap, DWORD dwFlags, IntPtr lpMem);

        [DllImport("kernel32.dll")]
        private static extern void GetNativeSystemInfo(SYSTEM_INFO* info);

#endif


        static IntPtr _LoadLibrary(byte* filename, IntPtr userdata)
        {
            return LoadLibrary(filename);
        }

        static IntPtr _GetProcAddress(IntPtr module, byte* name, IntPtr userdata)
        {
            return GetProcAddress(module, name);
        }

        static void _FreeLibrary(IntPtr module, IntPtr userdata)
        {
            FreeLibrary(module);
        }

        #endregion

        #region 内存操作

        static ushort Endian(ushort num)
        {
            return (ushort)((num << 8) | (num >> 8));
        }

        static void memcpy(byte* dest, byte* src, long size)
        {
            for (uint i = 0; i < size; i++)
            {
                *(dest + i) = *(src + i);
            }
        }

        static void memset(byte* dest, byte c, long size)
        {
            for (uint i = 0; i < size; i++)
            {
                *dest = c;
            }
        }

        static byte* realloc(byte* bytes, uint newsize, uint oldsize)
        {

            byte* New = (byte*)Marshal.AllocHGlobal((int)newsize).ToPointer();
            if ((int)bytes != 0)
            {
                memcpy(New, bytes, oldsize);
                Marshal.FreeHGlobal(new IntPtr(bytes));
            }
            return New;
        }

        static bool stricmp(string str, byte* bytes)
        {
            int idx = 0;
            while (*bytes != 0 && idx < str.Length)
            {
                if (str[idx] != *(bytes + idx))
                {
                    return false;
                }
                idx++;
            }
            return true;
        }

        #endregion

        static uint HIWORD(IntPtr value)
        {
            if (IntPtr.Size == 8)
                return Convert.ToUInt32(((long)value >> 32) & 0xFFFFFFFF);
            else
                return Convert.ToUInt32(((uint)value >> 16) & 0xFFFF);
        }

        static uint LOWORD(IntPtr value)
        {
            if (IntPtr.Size == 8)
                return Convert.ToUInt32((long)value & 0xFFFFFFFF);
            else
                return Convert.ToUInt32((uint)value & 0xFFFF);
        }

         static IMAGE_SECTION_HEADER* IMAGE_FIRST_SECTION(IMAGE_NT_HEADERS* img)
        {
            return (IMAGE_SECTION_HEADER*)((long)img + 0x18 + img->FileHeader.SizeOfOptionalHeader);
        }

        static IMAGE_DATA_DIRECTORY* GET_HEADER_DICTIONARY(MEMORYMODULE* module, uint idx)
        {
            return (IMAGE_DATA_DIRECTORY*)(&module->headers->OptionalHeader.DataDirectory[idx]);
        }

        static IntPtr ALIGN_DOWN(IntPtr address, DWORD alignment)
        {
            return new IntPtr((long)address & ~(alignment - 1));
        }

        static bool IMAGE_SNAP_BY_ORDINAL(IntPtr Ordinal)
        {
            if (IntPtr.Size == 8)
                return ((ulong)Ordinal & 0x8000000000000000) != 0;
            else
                return ((long)Ordinal & 0x80000000) != 0;
        }

        static IntPtr IMAGE_ORDINAL(IntPtr Ordinal)
        {
            if (IntPtr.Size == 8)
                return new IntPtr((long)Ordinal & 0xffff);
            else
                return new IntPtr((long)Ordinal & 0xffff);
        }




        static BOOL CopySections(byte* data, IMAGE_NT_HEADERS* old_headers, MEMORYMODULE* module)
        {
            uint i;
            SIZE_T size;
            byte* codeBase = module->codeBase;
            byte* dest;
            IMAGE_SECTION_HEADER* section = IMAGE_FIRST_SECTION(module->headers);
            for (i = 0; i < module->headers->FileHeader.NumberOfSections; i++, section++)
            {
                if (section->SizeOfRawData == 0)
                {
                    // section doesn't contain data in the dll itself, but may define
                    // uninitialized data
                    size = (SIZE_T)old_headers->OptionalHeader.SectionAlignment;
                    if ((long)size > 0)
                    {
                        dest = (byte*)VirtualAlloc((IntPtr)((ulong)codeBase + section->VirtualAddress),
                            size,
                            MEM_COMMIT,
                            PAGE_READWRITE);

                        if ((long)dest == 0)
                            return FALSE;

                        // Always use position from file to support alignments smaller
                        // than page size.
                        dest = codeBase + section->VirtualAddress;
                        section->PhysicalAddress = (uint)dest;
                        memset(dest, 0, (long)size);
                    }

                    // section is empty
                    continue;
                }

                // commit memory block and copy data from dll
                dest = (byte*)VirtualAlloc((IntPtr)((ulong)codeBase + section->VirtualAddress),
                                    (SIZE_T)section->SizeOfRawData,
                                    MEM_COMMIT,
                                    PAGE_READWRITE);

                if ((long)dest == 0)
                    return FALSE;

                // Always use position from file to support alignments smaller
                // than page size.
                dest = codeBase + section->VirtualAddress;
                memcpy(dest, (byte*)((ulong)data + section->PointerToRawData), section->SizeOfRawData);
                section->PhysicalAddress = (uint)dest;
            }

            return TRUE;
        }

        static DWORD GetRealSectionSize(MEMORYMODULE* module, IMAGE_SECTION_HEADER* section)
        {
            DWORD size = section->SizeOfRawData;
            if (size == 0)
            {
                if ((section->Characteristics & IMAGE_SCN_CNT_INITIALIZED_DATA) != 0)
                {
                    size = module->headers->OptionalHeader.SizeOfInitializedData;
                }
                else if ((section->Characteristics & IMAGE_SCN_CNT_UNINITIALIZED_DATA) != 0)
                {
                    size = module->headers->OptionalHeader.SizeOfUninitializedData;
                }
            }
            return size;
        }

        static BOOL FinalizeSection(MEMORYMODULE* module, SECTIONFINALIZEDATA* sectionData)
        {
            DWORD protect, oldProtect;
            BOOL executable;
            BOOL readable;
            BOOL writeable;

            if (sectionData->size == 0)
            {
                return TRUE;
            }

            if ((sectionData->characteristics & IMAGE_SCN_MEM_DISCARDABLE) != 0)
            {
                // section is not needed any more and can safely be freed
                if (sectionData->address == sectionData->alignedAddress &&
                    ((sectionData->last) != 0 ||
                     module->headers->OptionalHeader.SectionAlignment == module->pageSize ||
                     (sectionData->size % module->pageSize) == 0)
                   )
                {
                    // Only allowed to decommit whole pages
                    VirtualFree(sectionData->address, (SIZE_T)sectionData->size, MEM_DECOMMIT);
                }
                return TRUE;
            }

            // determine protection flags based on characteristics
            executable = ((sectionData->characteristics & IMAGE_SCN_MEM_EXECUTE) != 0) ? TRUE : FALSE;
            readable = ((sectionData->characteristics & IMAGE_SCN_MEM_READ) != 0) ? TRUE : FALSE;
            writeable = ((sectionData->characteristics & IMAGE_SCN_MEM_WRITE) != 0) ? TRUE : FALSE;
            protect = ProtectionFlags[executable, readable, writeable];
            if ((sectionData->characteristics & IMAGE_SCN_MEM_NOT_CACHED) != 0)
            {
                protect |= PAGE_NOCACHE;
            }

            // change memory access flags
            if (VirtualProtect(sectionData->address, (SIZE_T)sectionData->size, protect, &oldProtect) == 0)
            {
                return FALSE;
            }

            return TRUE;
        }

        static BOOL FinalizeSections(MEMORYMODULE* module)
        {
            int i;
            IMAGE_SECTION_HEADER* section = IMAGE_FIRST_SECTION(module->headers);
            IntPtr imageOffset = (IntPtr.Size == 4 ? IntPtr.Zero : new IntPtr(Convert.ToInt64((ulong)module->headers->OptionalHeader.ImageBase & 0xffffffff00000000)));

            SECTIONFINALIZEDATA sectionData;
            sectionData.address = new IntPtr((long)section->PhysicalAddress | (long)imageOffset);
            sectionData.alignedAddress = ALIGN_DOWN(sectionData.address, module->pageSize);
            sectionData.size = GetRealSectionSize(module, section);
            sectionData.characteristics = section->Characteristics;
            sectionData.last = FALSE;
            section++;

            // loop through all sections and change access flags
            for (i = 1; i < module->headers->FileHeader.NumberOfSections; i++, section++)
            {
                IntPtr sectionAddress = new IntPtr((long)section->PhysicalAddress | (long)imageOffset);
                IntPtr alignedAddress = ALIGN_DOWN(sectionAddress, module->pageSize);
                DWORD sectionSize = GetRealSectionSize(module, section);
                // Combine access flags of all sections that share a page
                // TODO(fancycode): We currently share flags of a trailing large section
                //   with the page of a first small section. This should be optimized.
                if (sectionData.alignedAddress == alignedAddress || (long)sectionData.address + sectionData.size > (long)alignedAddress)
                {
                    // Section shares page with previous
                    if ((section->Characteristics & IMAGE_SCN_MEM_DISCARDABLE) == 0 || (sectionData.characteristics & IMAGE_SCN_MEM_DISCARDABLE) == 0)
                    {
                        sectionData.characteristics = (sectionData.characteristics | section->Characteristics) & ~IMAGE_SCN_MEM_DISCARDABLE;
                    }
                    else
                    {
                        sectionData.characteristics |= section->Characteristics;
                    }
                    sectionData.size = Convert.ToUInt32(((long)sectionAddress + sectionSize) - (long)sectionData.address);
                    continue;
                }

                if (FinalizeSection(module, &sectionData) == FALSE)
                {
                    return FALSE;
                }
                sectionData.address = sectionAddress;
                sectionData.alignedAddress = alignedAddress;
                sectionData.size = sectionSize;
                sectionData.characteristics = section->Characteristics;
            }
            sectionData.last = TRUE;
            if (FinalizeSection(module, &sectionData) == FALSE)
            {
                return FALSE;
            }

            return TRUE;
        }  
       
        static BOOL ExecuteTLS(MEMORYMODULE* module)
        {
            byte* codeBase = module->codeBase;
            IMAGE_TLS_DIRECTORY* tls;
            // PIMAGE_TLS_CALLBACK* callback;
            IMAGE_TLS_CALLBACK callback;

            IMAGE_DATA_DIRECTORY* directory = GET_HEADER_DICTIONARY(module, IMAGE_DIRECTORY_ENTRY_TLS);
            if (directory->VirtualAddress == 0)
            {
                return TRUE;
            }

            tls = (IMAGE_TLS_DIRECTORY*)(codeBase + directory->VirtualAddress);
            // callback = (PIMAGE_TLS_CALLBACK *) tls->AddressOfCallBacks;
            callback = Marshal.GetDelegateForFunctionPointer((IntPtr)tls->AddressOfCallBacks, typeof(IMAGE_TLS_CALLBACK)) as IMAGE_TLS_CALLBACK;
            if (callback != null)
            {
                //while (*callback)
                {
                    callback((IntPtr)codeBase, DLL_PROCESS_ATTACH, IntPtr.Zero);
                    throw new Exception("TODO:");
                    //callback++;
                }
            }
            return TRUE;
        }     
       
        static BOOL PerformBaseRelocation(MEMORYMODULE* module, SIZE_T delta)
        {
            byte* codeBase = module->codeBase;
            IMAGE_BASE_RELOCATION* relocation;

            IMAGE_DATA_DIRECTORY* directory = GET_HEADER_DICTIONARY(module, IMAGE_DIRECTORY_ENTRY_BASERELOC);
            if (directory->Size == 0)
            {
                return ((long)delta == 0 ? TRUE : FALSE);
            }

            relocation = (IMAGE_BASE_RELOCATION*)(codeBase + directory->VirtualAddress);
            for (; relocation->VirtualAddress > 0; )
            {
                DWORD i;
                byte* dest = codeBase + relocation->VirtualAddress;
                ushort* relInfo = (ushort*)((byte*)relocation + IMAGE_SIZEOF_BASE_RELOCATION);
                for (i = 0; i < ((relocation->SizeOfBlock - IMAGE_SIZEOF_BASE_RELOCATION) / 2); i++, relInfo++)
                {
                    DWORD* patchAddrHL;
                    long* patchAddr64;
                    int type, offset;

                    // the upper 4 bits define the type of relocation
                    type = *relInfo >> 12;
                    // the lower 12 bits define the offset
                    offset = *relInfo & 0xfff;

                    switch ((uint)type)
                    {
                        case IMAGE_REL_BASED_ABSOLUTE:
                            // skip relocation
                            break;

                        case IMAGE_REL_BASED_HIGHLOW:
                            // change complete 32 bit address
                            patchAddrHL = (DWORD*)(dest + offset);
                            *patchAddrHL += (DWORD)delta;
                            break;

                        case IMAGE_REL_BASED_DIR64:
                            patchAddr64 = (long*)(dest + offset);
                            *patchAddr64 += (long)delta;
                            break;

                        default:
                            //printf("Unknown relocation: %d\n", type);
                            break;
                    }
                }

                // advance to next relocation block
                relocation = (IMAGE_BASE_RELOCATION*)(((long)relocation) + (long)relocation->SizeOfBlock);
            }
            return TRUE;
        }

        static BOOL BuildImportTable(MEMORYMODULE* module)
        {
            byte* codeBase = module->codeBase;
            IMAGE_IMPORT_DESCRIPTOR* importDesc;
            BOOL result = TRUE;

            IMAGE_DATA_DIRECTORY* directory = GET_HEADER_DICTIONARY(module, IMAGE_DIRECTORY_ENTRY_IMPORT);
            if (directory->Size == 0)
            {
                return TRUE;
            }

            importDesc = (IMAGE_IMPORT_DESCRIPTOR*)(codeBase + directory->VirtualAddress);
            for (; importDesc->Name != 0; importDesc++)
            {
                IntPtr* thunkRef;
                IntPtr* funcRef;
                IntPtr* tmp;

                CustomLoadLibraryFunc delegateLoadLibrary = Marshal.GetDelegateForFunctionPointer((IntPtr)module->loadLibrary, typeof(CustomLoadLibraryFunc)) as CustomLoadLibraryFunc;
                IntPtr handle = delegateLoadLibrary((byte*)(codeBase + importDesc->Name), module->userdata);
                //IntPtr handle = module->loadLibrary((byte*)(codeBase + importDesc->Name), module->userdata);
                if (handle == IntPtr.Zero)
                {
                    //SetLastError(ERROR_MOD_NOT_FOUND);
                    result = FALSE;
                    break;
                }

                CustomFreeLibraryFunc delegateFreeLibrary = Marshal.GetDelegateForFunctionPointer((IntPtr)module->freeLibrary, typeof(CustomFreeLibraryFunc)) as CustomFreeLibraryFunc;

                tmp = (IntPtr*)realloc((byte*)module->modules, (uint)((module->numModules + 1) * IntPtr.Size), (uint)((module->numModules) * IntPtr.Size));
                //tmp = (HCUSTOMMODULE *) realloc(module->modules, (module->numModules+1)*(sizeof(HCUSTOMMODULE)));
                if (tmp == null)
                {
                    delegateFreeLibrary(handle, module->userdata);
                    //module->freeLibrary(handle, module->userdata);
                    //SetLastError(ERROR_OUTOFMEMORY);
                    result = FALSE;
                    break;
                }
                module->modules = tmp;

                module->modules[module->numModules++] = handle;
                //if (importDesc->OriginalFirstThunk)
                if ((importDesc->Characteristics) != 0)
                {
                    //thunkRef = (IntPtr*)(codeBase + importDesc->OriginalFirstThunk);
                    thunkRef = (IntPtr*)(codeBase + importDesc->Characteristics);
                    funcRef = (IntPtr*)(codeBase + importDesc->FirstThunk);
                }
                else
                {
                    // no hint table
                    thunkRef = (IntPtr*)(codeBase + importDesc->FirstThunk);
                    funcRef = (IntPtr*)(codeBase + importDesc->FirstThunk);
                }

                CustomGetProcAddressFunc delegateGetProcAddress = Marshal.GetDelegateForFunctionPointer((IntPtr)module->getProcAddress, typeof(CustomGetProcAddressFunc)) as CustomGetProcAddressFunc;
                for (; (*thunkRef) != IntPtr.Zero; thunkRef++, funcRef++)
                {
                    if (IMAGE_SNAP_BY_ORDINAL(*thunkRef))
                    {
                        //*funcRef = module->getProcAddress(handle, (byte*)IMAGE_ORDINAL(*thunkRef), module->userdata);
                        *funcRef = delegateGetProcAddress(handle, (byte*)IMAGE_ORDINAL(*thunkRef), module->userdata);
                    }
                    else
                    {
                        IMAGE_IMPORT_BY_NAME* thunkData = (IMAGE_IMPORT_BY_NAME*)(codeBase + (long)(*thunkRef));
                        //*funcRef = module->getProcAddress(handle, (byte*)&thunkData->Name, module->userdata);
                        *funcRef = delegateGetProcAddress(handle, (byte*)&thunkData->Name[0], module->userdata);
                    }
                    if (*funcRef == IntPtr.Zero)
                    {
                        result = FALSE;
                        break;
                    }
                }

                if (result == FALSE)
                {
                    delegateFreeLibrary(handle, module->userdata);
                    //module->freeLibrary(handle, module->userdata);
                    // SetLastError(ERROR_PROC_NOT_FOUND);
                    break;
                }
            }

            return result;
        }


        public static IntPtr MemoryLoadLibrary(byte[] arrData)
        {
            return MemoryLoadLibraryEx(arrData,
                                        new CustomLoadLibraryFunc(_LoadLibrary),
                                        new CustomGetProcAddressFunc(_GetProcAddress),
                                        new CustomFreeLibraryFunc(_FreeLibrary), IntPtr.Zero);
        }

        public static IntPtr MemoryLoadLibraryEx(byte[] arrData,
                                                    CustomLoadLibraryFunc loadLibrary,
                                                    CustomGetProcAddressFunc getProcAddress,
                                                    CustomFreeLibraryFunc freeLibrary,
                                                    IntPtr userdata)
        {
            //注意条件预编译WIN32是否加上
            //byte* data = (byte*)Marshal.UnsafeAddrOfPinnedArrayElement(arrData, 0);
            fixed (byte* data = arrData)
            {

                MEMORYMODULE* result;
                IMAGE_DOS_HEADER* dos_header;
                IMAGE_NT_HEADERS* old_header;
                byte* code, headers;
                SIZE_T locationDelta;
                SYSTEM_INFO sysInfo;

                dos_header = (IMAGE_DOS_HEADER*)data;
                if (dos_header->e_magic != IMAGE_DOS_SIGNATURE)
                {
                    //SetLastError(ERROR_BAD_EXE_FORMAT);
                    return IntPtr.Zero;
                }

                old_header = (IMAGE_NT_HEADERS*)(data + dos_header->e_lfanew);
                //old_header = (IMAGE_NT_HEADERS*)(&data[dos_header->e_lfanew]);
                if (old_header->Signature != IMAGE_NT_SIGNATURE)
                {
                    //SetLastError(ERROR_BAD_EXE_FORMAT);
                    return IntPtr.Zero;
                }

                if (IntPtr.Size == 8)
                {
                    if (old_header->FileHeader.Machine != IMAGE_FILE_MACHINE_AMD64)
                    {
                        //SetLastError(ERROR_BAD_EXE_FORMAT);
                        return IntPtr.Zero;
                    }
                }
                else
                {
                    if (old_header->FileHeader.Machine != IMAGE_FILE_MACHINE_I386)
                    {
                        //SetLastError(ERROR_BAD_EXE_FORMAT);
                        return IntPtr.Zero;
                    }
                }

                if ((old_header->OptionalHeader.SectionAlignment & 1) != 0)
                {
                    // Only support section alignments that are a multiple of 2
                    //SetLastError(ERROR_BAD_EXE_FORMAT);
                    return IntPtr.Zero;
                }

                // reserve memory for image of library
                // XXX: is it correct to commit the complete memory region at once?
                //      calling DllEntry raises an exception if we don't...
                code = (byte*)VirtualAlloc(old_header->OptionalHeader.ImageBase,
                                            (SIZE_T)old_header->OptionalHeader.SizeOfImage,
                                            MEM_RESERVE | MEM_COMMIT,
                                            PAGE_READWRITE);

                int errCode = GetLastError();
                int si = Marshal.SizeOf(typeof(IMAGE_NT_HEADERS));
                if ((long)code == FALSE)
                {
                    // try to allocate memory at arbitrary position
                    code = (byte*)VirtualAlloc(IntPtr.Zero,
                        (SIZE_T)old_header->OptionalHeader.SizeOfImage,
                        MEM_RESERVE | MEM_COMMIT,
                        PAGE_READWRITE);

                    errCode = GetLastError();

                    if ((long)code == FALSE)
                    {
                        //SetLastError(ERROR_OUTOFMEMORY);
                        return IntPtr.Zero;
                    }
                }

                result = (MEMORYMODULE*)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, (SIZE_T)sizeof(MEMORYMODULE));
                if ((long)result == FALSE)
                {
                    VirtualFree((IntPtr)code, (SIZE_T)0, MEM_RELEASE);
                    //SetLastError(ERROR_OUTOFMEMORY);
                    return IntPtr.Zero;
                }

                result->codeBase = code;
                result->isDLL = ((old_header->FileHeader.Characteristics & IMAGE_FILE_DLL) != 0) ? TRUE : FALSE; ;
                result->loadLibrary = Marshal.GetFunctionPointerForDelegate(loadLibrary);
                result->getProcAddress = Marshal.GetFunctionPointerForDelegate(getProcAddress);
                result->freeLibrary = Marshal.GetFunctionPointerForDelegate(freeLibrary);
                result->userdata = userdata;

                GetNativeSystemInfo(&sysInfo);
                result->pageSize = sysInfo.dwPageSize;

                // commit memory for headers
                headers = (byte*)VirtualAlloc((IntPtr)code,
                    (SIZE_T)old_header->OptionalHeader.SizeOfHeaders,
                    MEM_COMMIT,
                    PAGE_READWRITE);

                // copy PE header to code
                memcpy(headers, (byte*)dos_header, old_header->OptionalHeader.SizeOfHeaders);
                result->headers = (IMAGE_NT_HEADERS*)&headers[dos_header->e_lfanew];

                // update position
                result->headers->OptionalHeader.ImageBase = (IntPtr)code;

                // copy sections from DLL file block to new memory location
                if (CopySections(data, old_header, result) == FALSE)
                {
                    goto error;
                }

                // adjust base address of imported data
                locationDelta = (SIZE_T)((long)code - (long)old_header->OptionalHeader.ImageBase);
                if ((long)locationDelta != 0)
                {
                    result->isRelocated = PerformBaseRelocation(result, locationDelta);
                }
                else
                {
                    result->isRelocated = TRUE;
                }

                // load required dlls and adjust function table of imports
                if (BuildImportTable(result) == FALSE)
                {
                    goto error;
                }

                // mark memory pages depending on section headers and release
                // sections that are marked as "discardable"
                if (FinalizeSections(result) == FALSE)
                {
                    goto error;
                }

                // TLS callbacks are executed BEFORE the main loading
                if (ExecuteTLS(result) == FALSE)
                {
                    goto error;
                }

                // get entry point of loaded library
                if (result->headers->OptionalHeader.AddressOfEntryPoint != 0)
                {
                    if (result->isDLL != FALSE)
                    {
                        IntPtr hDllEntry = new IntPtr((long)code + (long)result->headers->OptionalHeader.AddressOfEntryPoint);
                        DllEntryProc DllEntry = (DllEntryProc)Marshal.GetDelegateForFunctionPointer(hDllEntry, typeof(DllEntryProc));
                        //DllEntryProc DllEntry = (DllEntryProc) (code + result->headers->OptionalHeader.AddressOfEntryPoint);

                        // notify library about attaching to process
                        BOOL successfull = DllEntry((IntPtr)code, DLL_PROCESS_ATTACH, IntPtr.Zero);
                        //BOOL successfull = (*DllEntry)((HINSTANCE)code, DLL_PROCESS_ATTACH, 0);
                        if (successfull == FALSE)
                        {
                            //SetLastError(ERROR_DLL_INIT_FAILED);
                            goto error;
                        }
                        result->initialized = TRUE;
                    }
                    else
                    {
                        IntPtr hExeEntry = new IntPtr((long)code + (long)result->headers->OptionalHeader.AddressOfEntryPoint);
                        result->exeEntry = hExeEntry;
                        //result->exeEntry = (ExeEntryProc)(code + result->headers->OptionalHeader.AddressOfEntryPoint);
                    }
                }
                else
                {
                    result->exeEntry = IntPtr.Zero;
                }

                return (IntPtr)result;

            error:
                // cleanup
                MemoryFreeLibrary((IntPtr)result);
                return IntPtr.Zero;
            }
        }



        public static IntPtr MemoryGetProcAddress(IntPtr module, string name)
        {
            byte* codeBase = ((MEMORYMODULE*)module)->codeBase;
            DWORD idx = 0;
            IMAGE_EXPORT_DIRECTORY* exports;
            IMAGE_DATA_DIRECTORY* directory = GET_HEADER_DICTIONARY((MEMORYMODULE*)module, IMAGE_DIRECTORY_ENTRY_EXPORT);
            if (directory->Size == 0)
            {
                // no export table found
                //SetLastError(ERROR_PROC_NOT_FOUND);
                return IntPtr.Zero;
            }

            exports = (IMAGE_EXPORT_DIRECTORY*)(codeBase + directory->VirtualAddress);
            if (exports->NumberOfNames == 0 || exports->NumberOfFunctions == 0)
            {
                // DLL doesn't export anything
                //SetLastError(ERROR_PROC_NOT_FOUND);
                return IntPtr.Zero;
            }
            IntPtr hName = Marshal.StringToCoTaskMemUni(name);
            //if (HIWORD(hName) == 0)
            //{
            //    // load function by ordinal value
            //    if (LOWORD(hName) < exports->Base)
            //    {
            //        //SetLastError(ERROR_PROC_NOT_FOUND);
            //        return IntPtr.Zero;
            //    }

            //    idx = (LOWORD(hName) - exports->Base);
            //}
            //else
            {
                // search function name in list of exported names
                DWORD i;
                DWORD* nameRef = (DWORD*)(codeBase + exports->AddressOfNames);
                WORD* ordinal = (WORD*)(codeBase + exports->AddressOfNameOrdinals);
                BOOL found = FALSE;
                for (i = 0; i < exports->NumberOfNames; i++, nameRef++, ordinal++)
                {
                    if (stricmp(name, (byte*)((long)codeBase + (long)(*nameRef))))
                    {
                        idx = *ordinal;
                        found = TRUE;
                        break;
                    }
                }

                if (found == FALSE)
                {
                    // exported symbol not found
                    //SetLastError(ERROR_PROC_NOT_FOUND);
                    return IntPtr.Zero;
                }
            }

            if (idx > exports->NumberOfFunctions)
            {
                // name <-> ordinal number don't match
                //SetLastError(ERROR_PROC_NOT_FOUND);
                return IntPtr.Zero;
            }

            // AddressOfFunctions contains the RVAs to the "real" functions
            return new IntPtr((long)codeBase + (long)(*(DWORD*)((long)codeBase + (long)exports->AddressOfFunctions + (idx * 4))));
        }

        public static void MemoryFreeLibrary(IntPtr mod)
        {
            if (mod == IntPtr.Zero)
            {
                return;
            }

            MEMORYMODULE* module = (MEMORYMODULE*)mod;
            if (module->initialized != 0)
            {
                // notify library about detaching from process
                IntPtr hDllEntry = new IntPtr((long)module->codeBase + (long)module->headers->OptionalHeader.AddressOfEntryPoint);
                DllEntryProc DllEntry = (DllEntryProc)Marshal.GetDelegateForFunctionPointer(hDllEntry, typeof(DllEntryProc));

                DllEntry((IntPtr)module->codeBase, DLL_PROCESS_DETACH, IntPtr.Zero);
                //DllEntryProc DllEntry = (DllEntryProc)(module->codeBase + module->headers->OptionalHeader.AddressOfEntryPoint);
                //(*DllEntry)((HINSTANCE)module->codeBase, DLL_PROCESS_DETACH, 0);
            }

            if ((IntPtr)module->modules != IntPtr.Zero)
            {
                // free previously opened libraries
                int i;
                for (i = 0; i < module->numModules; i++)
                {
                    if (module->modules[i] != IntPtr.Zero)
                    {
                        CustomFreeLibraryFunc delegateFreeLibrary = Marshal.GetDelegateForFunctionPointer(module->freeLibrary, typeof(CustomFreeLibraryFunc)) as CustomFreeLibraryFunc;
                        delegateFreeLibrary((IntPtr)module->modules[i], module->userdata);
                        //module->freeLibrary(module->modules[i], module->userdata);
                    }
                }

                Marshal.FreeHGlobal((IntPtr)module->modules);
                //free(module->modules);
            }

            if ((IntPtr)module->codeBase != IntPtr.Zero)
            {
                // release memory of library
                VirtualFree((IntPtr)module->codeBase, (SIZE_T)0, MEM_RELEASE);
            }

            HeapFree(GetProcessHeap(), 0, (IntPtr)module);
        }

        public static int MemoryCallEntryPoint(IntPtr mod)
        {
            MEMORYMODULE* module = (MEMORYMODULE*)mod;

            if ((IntPtr)module == IntPtr.Zero || module->isDLL != 0 || (IntPtr)module->exeEntry == IntPtr.Zero || module->isRelocated == 0)
            {
                return -1;
            }

            ExeEntryProc delegateExeEntry = Marshal.GetDelegateForFunctionPointer(module->exeEntry, typeof(ExeEntryProc)) as ExeEntryProc;
            return delegateExeEntry();
            //return module->exeEntry;
        }

        #region IMAGE_RESOURCE 暂时不用

        //[StructLayout(LayoutKind.Sequential, Pack = 1)]
        //public struct IMAGE_RESOURCE_DIRECTORY_ENTRY
        //{
        //    //union {
        //    //    struct {
        //    //        DWORD NameOffset:31;
        //    //        DWORD NameIsString:1;
        //    //    } DUMMYSTRUCTNAME;
        //    //    DWORD   Name;
        //    //    WORD    Id;
        //    //} DUMMYUNIONNAME;
        //    public DWORD DUMMYUNIONNAME;

        //    //union {
        //    //    DWORD   OffsetToData;
        //    //    struct {
        //    //        DWORD   OffsetToDirectory:31;
        //    //        DWORD   DataIsDirectory:1;
        //    //    } DUMMYSTRUCTNAME2;
        //    //} DUMMYUNIONNAME2;
        //    public DWORD DUMMYUNIONNAME2;
        //}

        //[StructLayout(LayoutKind.Sequential, Pack = 1)]
        //public struct IMAGE_RESOURCE_DIRECTORY
        //{
        //    public DWORD Characteristics;
        //    public DWORD TimeDateStamp;
        //    public WORD MajorVersion;
        //    public WORD MinorVersion;
        //    public WORD NumberOfNamedEntries;
        //    public WORD NumberOfIdEntries;
        //    //  IMAGE_RESOURCE_DIRECTORY_ENTRY DirectoryEntries[];
        //}

        //        static IMAGE_RESOURCE_DIRECTORY_ENTRY _MemorySearchResourceEntry(
        //    IntPtr root,
        //    IMAGE_RESOURCE_DIRECTORY resources,
        //    byte* key)
        //{
        //    IMAGE_RESOURCE_DIRECTORY_ENTRY* entries = (IMAGE_RESOURCE_DIRECTORY_ENTRY*) (resources + 1);
        //    IMAGE_RESOURCE_DIRECTORY_ENTRY* result = IntPtr.Zero;
        //    DWORD start;
        //    DWORD end;
        //    DWORD middle;

        //    if (!IS_INTRESOURCE(key) && key[0] == TEXT('#')) {
        //        // special case: resource id given as string
        //        TCHAR *endpos = NULL;
        //        long int tmpkey = (WORD) _tcstol((TCHAR *) &key[1], &endpos, 10);
        //        if (tmpkey <= 0xffff && lstrlen(endpos) == 0) {
        //            key = MAKEINTRESOURCE(tmpkey);
        //        }
        //    }

        //    // entries are stored as ordered list of named entries,
        //    // followed by an ordered list of id entries - we can do
        //    // a binary search to find faster...
        //    if (IS_INTRESOURCE(key)) {
        //        WORD check = (WORD) (uintptr_t) key;
        //        start = resources->NumberOfNamedEntries;
        //        end = start + resources->NumberOfIdEntries;

        //        while (end > start) {
        //            WORD entryName;
        //            middle = (start + end) >> 1;
        //            entryName = (WORD) entries[middle].Name;
        //            if (check < entryName) {
        //                end = (end != middle ? middle : middle-1);
        //            } else if (check > entryName) {
        //                start = (start != middle ? middle : middle+1);
        //            } else {
        //                result = &entries[middle];
        //                break;
        //            }
        //        }
        //    } else {
        //        LPCWSTR searchKey;
        //        size_t searchKeyLen = _tcslen(key);
        //#if defined(UNICODE)
        //        searchKey = key;
        //#else
        //        // Resource names are always stored using 16bit characters, need to
        //        // convert string we search for.
        //#define MAX_LOCAL_KEY_LENGTH 2048
        //        // In most cases resource names are short, so optimize for that by
        //        // using a pre-allocated array.
        //        wchar_t _searchKeySpace[MAX_LOCAL_KEY_LENGTH+1];
        //        LPWSTR _searchKey;
        //        if (searchKeyLen > MAX_LOCAL_KEY_LENGTH) {
        //            size_t _searchKeySize = (searchKeyLen + 1) * sizeof(wchar_t);
        //            _searchKey = (LPWSTR) malloc(_searchKeySize);
        //            if (_searchKey == NULL) {
        //                SetLastError(ERROR_OUTOFMEMORY);
        //                return NULL;
        //            }
        //        } else {
        //            _searchKey = &_searchKeySpace[0];
        //        }

        //        mbstowcs(_searchKey, key, searchKeyLen);
        //        _searchKey[searchKeyLen] = 0;
        //        searchKey = _searchKey;
        //#endif
        //        start = 0;
        //        end = resources->NumberOfNamedEntries;
        //        while (end > start) {
        //            int cmp;
        //            PIMAGE_RESOURCE_DIR_STRING_U resourceString;
        //            middle = (start + end) >> 1;
        //            resourceString = (PIMAGE_RESOURCE_DIR_STRING_U) (((char *) root) + (entries[middle].Name & 0x7FFFFFFF));
        //            cmp = wcsnicmp(searchKey, resourceString->NameString, resourceString->Length);
        //            if (cmp == 0) {
        //                // Handle partial match
        //                cmp = searchKeyLen - resourceString->Length;
        //            }
        //            if (cmp < 0) {
        //                end = (middle != end ? middle : middle-1);
        //            } else if (cmp > 0) {
        //                start = (middle != start ? middle : middle+1);
        //            } else {
        //                result = &entries[middle];
        //                break;
        //            }
        //        }
        //#if !defined(UNICODE)
        //        if (searchKeyLen > MAX_LOCAL_KEY_LENGTH) {
        //            free(_searchKey);
        //        }
        //#undef MAX_LOCAL_KEY_LENGTH
        //#endif
        //    }

        //    return result;
        //}

        //IntPtr MemoryFindResource(IntPtr module, byte* name, byte* type)
        //{
        //    return MemoryFindResourceEx(module, name, type, DEFAULT_LANGUAGE);
        //}

        #endregion

    }
}
