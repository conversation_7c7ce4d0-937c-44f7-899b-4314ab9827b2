﻿using System;
using System.Collections.Generic;
using System.Text;

namespace tbCmd40
{
    public unsafe partial class MemoryLibraryEx
    {

        const uint IMAGE_DOS_SIGNATURE = 0x5A4D;      // MZ
        const uint IMAGE_OS2_SIGNATURE = 0x454E;      // NE
        const uint IMAGE_OS2_SIGNATURE_LE = 0x454C;      // LE
        const uint IMAGE_VXD_SIGNATURE = 0x454C;      // LE
        const uint IMAGE_NT_SIGNATURE = 0x00004550;  // PE00

        const uint IMAGE_SIZEOF_FILE_HEADER = 20;

        const uint IMAGE_FILE_RELOCS_STRIPPED = 0x0001;  // Relocation info stripped from file.
        const uint IMAGE_FILE_EXECUTABLE_IMAGE = 0x0002;  // File is executable  (i.e. no unresolved externel references).
        const uint IMAGE_FILE_LINE_NUMS_STRIPPED = 0x0004;  // Line nunbers stripped from file.
        const uint IMAGE_FILE_LOCAL_SYMS_STRIPPED = 0x0008;  // Local symbols stripped from file.
        const uint IMAGE_FILE_AGGRESIVE_WS_TRIM = 0x0010;  // Agressively trim working set
        const uint IMAGE_FILE_LARGE_ADDRESS_AWARE = 0x0020;  // App can handle >2gb addresses
        const uint IMAGE_FILE_BYTES_REVERSED_LO = 0x0080;  // Bytes of machine word are reversed.
        const uint IMAGE_FILE_32BIT_MACHINE = 0x0100;  // 32 bit word machine.
        const uint IMAGE_FILE_DEBUG_STRIPPED = 0x0200;  // Debugging info stripped from file in .DBG file
        const uint IMAGE_FILE_REMOVABLE_RUN_FROM_SWAP = 0x0400;  // If Image is on removable media, copy and run from the swap file.
        const uint IMAGE_FILE_NET_RUN_FROM_SWAP = 0x0800;  // If Image is on Net, copy and run from the swap file.
        const uint IMAGE_FILE_SYSTEM = 0x1000;  // System File.
        const uint IMAGE_FILE_DLL = 0x2000;  // File is a DLL.
        const uint IMAGE_FILE_UP_SYSTEM_ONLY = 0x4000;  // File should only be run on a UP machine
        const uint IMAGE_FILE_BYTES_REVERSED_HI = 0x8000;  // Bytes of machine word are reversed.

        const uint IMAGE_FILE_MACHINE_UNKNOWN = 0;
        const uint IMAGE_FILE_MACHINE_I386 = 0x014c;  // Intel 386.
        const uint IMAGE_FILE_MACHINE_R3000 = 0x0162;  // MIPS little-endian, 0x160 big-endian
        const uint IMAGE_FILE_MACHINE_R4000 = 0x0166;  // MIPS little-endian
        const uint IMAGE_FILE_MACHINE_R10000 = 0x0168;  // MIPS little-endian
        const uint IMAGE_FILE_MACHINE_WCEMIPSV2 = 0x0169;  // MIPS little-endian WCE v2
        const uint IMAGE_FILE_MACHINE_ALPHA = 0x0184;  // Alpha_AXP
        const uint IMAGE_FILE_MACHINE_SH3 = 0x01a2;  // SH3 little-endian
        const uint IMAGE_FILE_MACHINE_SH3DSP = 0x01a3;
        const uint IMAGE_FILE_MACHINE_SH3E = 0x01a4;  // SH3E little-endian
        const uint IMAGE_FILE_MACHINE_SH4 = 0x01a6;  // SH4 little-endian
        const uint IMAGE_FILE_MACHINE_SH5 = 0x01a8;  // SH5
        const uint IMAGE_FILE_MACHINE_ARM = 0x01c0;  // ARM Little-Endian
        const uint IMAGE_FILE_MACHINE_THUMB = 0x01c2;
        const uint IMAGE_FILE_MACHINE_AM33 = 0x01d3;
        const uint IMAGE_FILE_MACHINE_POWERPC = 0x01F0; // IBM PowerPC Little-Endian
        const uint IMAGE_FILE_MACHINE_POWERPCFP = 0x01f1;
        const uint IMAGE_FILE_MACHINE_IA64 = 0x0200;  // Intel 64
        const uint IMAGE_FILE_MACHINE_MIPS16 = 0x0266;  // MIPS
        const uint IMAGE_FILE_MACHINE_ALPHA64 = 0x0284;  // ALPHA64
        const uint IMAGE_FILE_MACHINE_MIPSFPU = 0x0366;  // MIPS
        const uint IMAGE_FILE_MACHINE_MIPSFPU16 = 0x0466;  // MIPS
        const uint IMAGE_FILE_MACHINE_AXP64 = IMAGE_FILE_MACHINE_ALPHA64;
        const uint IMAGE_FILE_MACHINE_TRICORE = 0x0520;  // Infineon
        const uint IMAGE_FILE_MACHINE_CEF = 0x0CEF;
        const uint IMAGE_FILE_MACHINE_EBC = 0x0EBC;  // EFI Byte Code
        const uint IMAGE_FILE_MACHINE_AMD64 = 0x8664;  // AMD64 (K8)
        const uint IMAGE_FILE_MACHINE_M32R = 0x9041;  // M32R little-endian
        const uint IMAGE_FILE_MACHINE_CEE = 0xC0EE;

        const uint PAGE_NOACCESS = 0x01;
        const uint PAGE_READONLY = 0x02;
        const uint PAGE_READWRITE = 0x04;
        const uint PAGE_WRITECOPY = 0x08;
        const uint PAGE_EXECUTE = 0x10;
        const uint PAGE_EXECUTE_READ = 0x20;
        const uint PAGE_EXECUTE_READWRITE = 0x40;
        const uint PAGE_EXECUTE_WRITECOPY = 0x80;
        const uint PAGE_GUARD = 0x100;
        const uint PAGE_NOCACHE = 0x200;
        const uint PAGE_WRITECOMBINE = 0x400;
        const uint MEM_COMMIT = 0x1000;
        const uint MEM_RESERVE = 0x2000;
        const uint MEM_DECOMMIT = 0x4000;
        const uint MEM_RELEASE = 0x8000;
        const uint MEM_FREE = 0x10000;
        const uint MEM_PRIVATE = 0x20000;
        const uint MEM_MAPPED = 0x40000;
        const uint MEM_RESET = 0x80000;
        const uint MEM_TOP_DOWN = 0x100000;
        const uint MEM_WRITE_WATCH = 0x200000;
        const uint MEM_PHYSICAL = 0x400000;
        const uint MEM_LARGE_PAGES = 0x20000000;
        const uint MEM_4MB_PAGES = 0x80000000;

        const uint HEAP_NO_SERIALIZE = 0x00000001;
        const uint HEAP_GROWABLE = 0x00000002;
        const uint HEAP_GENERATE_EXCEPTIONS = 0x00000004;
        const uint HEAP_ZERO_MEMORY = 0x00000008;
        const uint HEAP_REALLOC_IN_PLACE_ONLY = 0x00000010;
        const uint HEAP_TAIL_CHECKING_ENABLED = 0x00000020;
        const uint HEAP_FREE_CHECKING_ENABLED = 0x00000040;
        const uint HEAP_DISABLE_COALESCE_ON_FREE = 0x00000080;
        const uint HEAP_CREATE_ALIGN_16 = 0x00010000;
        const uint HEAP_CREATE_ENABLE_TRACING = 0x00020000;
        const uint HEAP_CREATE_ENABLE_EXECUTE = 0x00040000;
        const uint HEAP_MAXIMUM_TAG = 0x0FFF;
        const uint HEAP_PSEUDO_TAG_FLAG = 0x8000;
        const uint HEAP_TAG_SHIFT = 18;
        
        const byte IMAGE_NUMBEROF_DIRECTORY_ENTRIES = 16;

        const uint IMAGE_SUBSYSTEM_UNKNOWN = 0;   // Unknown subsystem.
        const uint IMAGE_SUBSYSTEM_NATIVE = 1;   // Image doesn't require a subsystem.
        const uint IMAGE_SUBSYSTEM_WINDOWS_GUI = 2;   // Image runs in the Windows GUI subsystem.
        const uint IMAGE_SUBSYSTEM_WINDOWS_CUI = 3;  // Image runs in the Windows character subsystem.
        const uint IMAGE_SUBSYSTEM_OS2_CUI = 5;   // image runs in the OS/2 character subsystem.
        const uint IMAGE_SUBSYSTEM_POSIX_CUI = 7;  // image runs in the Posix character subsystem.
        const uint IMAGE_SUBSYSTEM_NATIVE_WINDOWS = 8; // image is a native Win9x driver.
        const uint IMAGE_SUBSYSTEM_WINDOWS_CE_GUI = 9; // Image runs in the Windows CE subsystem.
        const uint IMAGE_SUBSYSTEM_EFI_APPLICATION = 10; //
        const uint IMAGE_SUBSYSTEM_EFI_BOOT_SERVICE_DRIVER = 11;   //
        const uint IMAGE_SUBSYSTEM_EFI_RUNTIME_DRIVER = 12;//
        const uint IMAGE_SUBSYSTEM_EFI_ROM = 13;
        const uint IMAGE_SUBSYSTEM_XBOX = 14;
        const uint IMAGE_SUBSYSTEM_WINDOWS_BOOT_APPLICATION = 16;

        // DllCharacteristics Entries

        //      IMAGE_LIBRARY_PROCESS_INIT            0x0001     // Reserved.
        //      IMAGE_LIBRARY_PROCESS_TERM            0x0002     // Reserved.
        //      IMAGE_LIBRARY_THREAD_INIT             0x0004     // Reserved.
        //      IMAGE_LIBRARY_THREAD_TERM             0x0008     // Reserved.
        const uint IMAGE_DLLCHARACTERISTICS_DYNAMIC_BASE = 0x0040;    // DLL can move.
        const uint IMAGE_DLLCHARACTERISTICS_FORCE_INTEGRITY = 0x0080;     // Code Integrity Image
        const uint IMAGE_DLLCHARACTERISTICS_NX_COMPAT = 0x0100;     // Image is NX compatible
        const uint IMAGE_DLLCHARACTERISTICS_NO_ISOLATION = 0x0200;    // Image understands isolation and doesn't want it
        const uint IMAGE_DLLCHARACTERISTICS_NO_SEH = 0x0400;    // Image does not use SEH.  No SE handler may reside in this image
        const uint IMAGE_DLLCHARACTERISTICS_NO_BIND = 0x0800;    // Do not bind this image.
        //                                            0x1000     // Reserved.
        const uint IMAGE_DLLCHARACTERISTICS_WDM_DRIVER = 0x2000;     // Driver uses WDM model
        //                                            0x4000     // Reserved.
        const uint IMAGE_DLLCHARACTERISTICS_TERMINAL_SERVER_AWARE = 0x8000;

        // Directory Entries

        const uint IMAGE_DIRECTORY_ENTRY_EXPORT = 0;   // Export Directory
        const uint IMAGE_DIRECTORY_ENTRY_IMPORT = 1;   // Import Directory
        const uint IMAGE_DIRECTORY_ENTRY_RESOURCE = 2;   // Resource Directory
        const uint IMAGE_DIRECTORY_ENTRY_EXCEPTION = 3;   // Exception Directory
        const uint IMAGE_DIRECTORY_ENTRY_SECURITY = 4;   // Security Directory
        const uint IMAGE_DIRECTORY_ENTRY_BASERELOC = 5;   // Base Relocation Table
        const uint IMAGE_DIRECTORY_ENTRY_DEBUG = 6;   // Debug Directory
        //      IMAGE_DIRECTORY_ENTRY_COPYRIGHT       7   // (X86 usage)
        const uint IMAGE_DIRECTORY_ENTRY_ARCHITECTURE = 7;   // Architecture Specific Data
        const uint IMAGE_DIRECTORY_ENTRY_GLOBALPTR = 8;   // RVA of GP
        const uint IMAGE_DIRECTORY_ENTRY_TLS = 9;   // TLS Directory
        const uint IMAGE_DIRECTORY_ENTRY_LOAD_CONFIG = 10;   // Load Configuration Directory
        const uint IMAGE_DIRECTORY_ENTRY_BOUND_IMPORT = 11;   // Bound Import Directory in headers
        const uint IMAGE_DIRECTORY_ENTRY_IAT = 12;   // Import Address Table
        const uint IMAGE_DIRECTORY_ENTRY_DELAY_IMPORT = 13;   // Delay Load Import Descriptors
        const uint IMAGE_DIRECTORY_ENTRY_COM_DESCRIPTOR = 14;   // COM Runtime descriptor

        const int IMAGE_SIZEOF_SHORT_NAME = 8;
        const uint IMAGE_SCN_CNT_CODE = 0x00000020;  // Section contains code.
        const uint IMAGE_SCN_CNT_INITIALIZED_DATA = 0x00000040;  // Section contains initialized data.
        const uint IMAGE_SCN_CNT_UNINITIALIZED_DATA = 0x00000080; // Section contains uninitialized data.
        const uint IMAGE_SCN_LNK_NRELOC_OVFL = 0x01000000;  // Section contains extended relocations.
        const uint IMAGE_SCN_MEM_DISCARDABLE = 0x02000000;  // Section can be discarded.
        const uint IMAGE_SCN_MEM_NOT_CACHED = 0x04000000;   // Section is not cachable.
        const uint IMAGE_SCN_MEM_NOT_PAGED = 0x08000000;    // Section is not pageable.
        const uint IMAGE_SCN_MEM_SHARED = 0x10000000;       // Section is shareable.
        const uint IMAGE_SCN_MEM_EXECUTE = 0x20000000;      // Section is executable.
        const uint IMAGE_SCN_MEM_READ = 0x40000000;         // Section is readable.
        const uint IMAGE_SCN_MEM_WRITE = 0x80000000;        // Section is writeable.

        const uint IMAGE_REL_BASED_ABSOLUTE = 0;
        const uint IMAGE_REL_BASED_HIGH = 1;
        const uint IMAGE_REL_BASED_LOW = 2;
        const uint IMAGE_REL_BASED_HIGHLOW = 3;
        const uint IMAGE_REL_BASED_HIGHADJ = 4;
        const uint IMAGE_REL_BASED_MIPS_JMPADDR = 5;
        const uint IMAGE_REL_BASED_MIPS_JMPADDR16 = 9;
        const uint IMAGE_REL_BASED_IA64_IMM64 = 9;
        const uint IMAGE_REL_BASED_DIR64 = 10;

        const uint DLL_PROCESS_ATTACH = 1;
        const uint DLL_PROCESS_DETACH = 0;
    }
}
