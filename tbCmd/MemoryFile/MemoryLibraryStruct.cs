﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Runtime.InteropServices;

using BYTE = System.Byte;
using WORD = System.UInt16;
using DWORD = System.UInt32;
using BOOL = System.Int32;
using SIZE_T = System.IntPtr;

namespace tbCmd40
{
    public unsafe partial class MemoryLibraryEx
    {
         [StructLayout(LayoutKind.Sequential, Pack = 1)]
         struct IMAGE_DOS_HEADER                 // DOS .EXE header
         {
             public ushort e_magic;                     // Magic number
             public ushort e_cblp;                      // Bytes on last page of file
             public ushort e_cp;                        // Pages in file
             public ushort e_crlc;                      // Relocations
             public ushort e_cparhdr;                   // Size of header in paragraphs
             public ushort e_minalloc;                  // Minimum extra paragraphs needed
             public ushort e_maxalloc;                  // Maximum extra paragraphs needed
             public ushort e_ss;                        // Initial (relative) SS value
             public ushort e_sp;                        // Initial SP value
             public ushort e_csum;                      // Checksum
             public ushort e_ip;                        // Initial IP value
             public ushort e_cs;                        // Initial (relative) CS value
             public ushort e_lfarlc;                    // File address of relocation table
             public ushort e_ovno;                      // Overlay number
             public fixed ushort e_res[4];              // Reserved ushorts
             public ushort e_oemid;                     // OEM identifier (for e_oeminfo)
             public ushort e_oeminfo;                   // OEM information; e_oemid specific
             public fixed ushort e_res2[10];            // Reserved ushorts
             public uint e_lfanew;                      // File address of new exe header
         }


         [StructLayout(LayoutKind.Sequential, Pack = 1)]
         public struct IMAGE_NT_HEADERS
         {
             public uint Signature;
             public IMAGE_FILE_HEADER FileHeader;
             public IMAGE_OPTIONAL_HEADER OptionalHeader;
         }

         [StructLayout(LayoutKind.Sequential, Pack = 1)]
         public struct IMAGE_FILE_HEADER
         {
             public ushort Machine;
             public ushort NumberOfSections;
             public uint TimeDateStamp;
             public uint PointerToSymbolTable;
             public uint NumberOfSymbols;
             public ushort SizeOfOptionalHeader;
             public ushort Characteristics;
         }

         [StructLayout(LayoutKind.Sequential, Pack = 1)]
         public struct IMAGE_OPTIONAL_HEADER
         {
             //
             // Standard fields.
             //

             public WORD Magic;
             public BYTE MajorLinkerVersion;
             public BYTE MinorLinkerVersion;
             public DWORD SizeOfCode;
             public DWORD SizeOfInitializedData;
             public DWORD SizeOfUninitializedData;
             public DWORD AddressOfEntryPoint;
             public DWORD BaseOfCode;

#if WIN32
             public DWORD BaseOfData;
#endif
             //
             // NT additional fields.
             //

             public IntPtr ImageBase;
             public DWORD SectionAlignment;
             public DWORD FileAlignment;
             public WORD MajorOperatingSystemVersion;
             public WORD MinorOperatingSystemVersion;
             public WORD MajorImageVersion;
             public WORD MinorImageVersion;
             public WORD MajorSubsystemVersion;
             public WORD MinorSubsystemVersion;
             public DWORD Win32VersionValue;
             public DWORD SizeOfImage;
             public DWORD SizeOfHeaders;
             public DWORD CheckSum;
             public WORD Subsystem;
             public WORD DllCharacteristics;
             public IntPtr SizeOfStackReserve;
             public IntPtr SizeOfStackCommit;
             public IntPtr SizeOfHeapReserve;
             public IntPtr SizeOfHeapCommit;
             public DWORD LoaderFlags;
             public DWORD NumberOfRvaAndSizes;
             public fixed ulong DataDirectory[IMAGE_NUMBEROF_DIRECTORY_ENTRIES];
         }

         [StructLayout(LayoutKind.Sequential, Pack = 1)]
         struct IMAGE_DATA_DIRECTORY
         {
             public uint VirtualAddress;
             public uint Size;
         }

         [StructLayout(LayoutKind.Sequential, Pack = 1)]
         public struct MEMORYMODULE
         {
             public IMAGE_NT_HEADERS* headers;
             public byte* codeBase;
             public IntPtr* modules;
             public int numModules;
             public BOOL initialized;
             public BOOL isDLL;
             public BOOL isRelocated;
             public IntPtr loadLibrary;
             public IntPtr getProcAddress;
             public IntPtr freeLibrary;
             public IntPtr userdata;
             public IntPtr exeEntry;
             public DWORD pageSize;
         }

         [StructLayout(LayoutKind.Sequential, Pack = 1)]
         public struct SECTIONFINALIZEDATA
         {
             public IntPtr address;
             public IntPtr alignedAddress;
             public DWORD size;
             public DWORD characteristics;
             public BOOL last;
         }

        [StructLayout(LayoutKind.Sequential, Pack = 1)]
        struct IMAGE_TLS_DIRECTORY
        {
            public IntPtr StartAddressOfRawData;
            public IntPtr EndAddressOfRawData;
            public IntPtr AddressOfIndex;             // PDWORD
            public IntPtr AddressOfCallBacks;         // PIMAGE_TLS_CALLBACK *
            public DWORD SizeOfZeroFill;
            public DWORD Characteristics;
        }

        [StructLayout(LayoutKind.Sequential, Pack = 1)]
        struct IMAGE_BASE_RELOCATION
        {
            public DWORD VirtualAddress;
            public DWORD SizeOfBlock;
            //  WORD    TypeOffset[1];
        }

        [StructLayout(LayoutKind.Sequential, Pack = 1)]
        struct SYSTEM_INFO
        {
            //union {
            //    DWORD dwOemId;          // Obsolete field...do not use
            //    struct {
            //        WORD wProcessorArchitecture;
            //        WORD wReserved;
            //    } DUMMYSTRUCTNAME;
            //} DUMMYUNIONNAME;
            public DWORD DUMMYUNIONNAME;
            public DWORD dwPageSize;
            public IntPtr lpMinimumApplicationAddress;
            public IntPtr lpMaximumApplicationAddress;
            public IntPtr dwActiveProcessorMask;
            public DWORD dwNumberOfProcessors;
            public DWORD dwProcessorType;
            public DWORD dwAllocationGranularity;
            public WORD wProcessorLevel;
            public WORD wProcessorRevision;
        }

        [StructLayout(LayoutKind.Sequential, Pack = 1)]
        struct IMAGE_SECTION_HEADER
        {
            public fixed byte Name[IMAGE_SIZEOF_SHORT_NAME];
            public uint PhysicalAddress;
            public uint VirtualAddress;
            public uint SizeOfRawData;
            public uint PointerToRawData;
            public uint PointerToRelocations;
            public uint PointerToLinenumbers;
            public ushort NumberOfRelocations;
            public ushort NumberOfLinenumbers;
            public uint Characteristics;
        }


        [StructLayout(LayoutKind.Sequential, Pack = 1)]
        struct IMAGE_IMPORT_DESCRIPTOR
        {
            public uint Characteristics;            // 0 for terminating null import descriptor
            public uint TimeDateStamp;                  // 0 if not bound,
            // -1 if bound, and real date\time stamp
            //     in IMAGE_DIRECTORY_ENTRY_BOUND_IMPORT (new BIND)
            // O.W. date/time stamp of DLL bound to (Old BIND)

            public uint ForwarderChain;                 // -1 if no forwarders
            public uint Name;
            public uint FirstThunk;                     // RVA to IAT (if bound this IAT has actual addresses)
        }

        [StructLayout(LayoutKind.Sequential, Pack = 1)]
        struct IMAGE_IMPORT_BY_NAME
        {
            public ushort Hint;
            public fixed byte Name[1];
        }

        [StructLayout(LayoutKind.Sequential, Pack = 1)]
        struct IMAGE_EXPORT_DIRECTORY
        {
            public uint Characteristics;
            public uint TimeDateStamp;
            public ushort MajorVersion;
            public ushort MinorVersion;
            public uint Name;
            public uint Base;
            public uint NumberOfFunctions;
            public uint NumberOfNames;
            public uint AddressOfFunctions;     // RVA from base of image
            public uint AddressOfNames;         // RVA from base of image
            public uint AddressOfNameOrdinals;  // RVA from base of image
        }

    }
}
