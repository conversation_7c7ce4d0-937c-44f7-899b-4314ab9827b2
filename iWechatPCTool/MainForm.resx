﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btn_Setting.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEgAAAAYCAYAAABZY7uwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAFoSURBVFhH7Zc7DsIwDIY7du8NegVYWNlZ2Ni5AffoDdiYOQErOwMSAxMLE3co/lFAfcR1nCCB
        VEeyojjJ3/qr82iWWTECRsAIjIRAXdeTVBtCRdqLVPvpp0iFg/kGSMgyA2SA0vahv8ogWvMV2VCpNJua
        Zw/aCfrob23sSkB7QR/9rY1dE89rLAlwD9lrxTyApuQ7MEHAj/4UQAj+yOjD3zv1tDEBUO55CMRzrRhz
        is3If+oEgTb8vWuBMoMAYEl27uhfnD8dkMuiggQhinIlK7RwnA53D5qT5s3po0bbOzYCECCsGvp31/be
        mWLiei+10n2JMlaEC9r58cKAj5q9UEYCgubaQULNXihjY/vKPAEQoPT2nO6cBEBBt2xVoPRySEfJNqGi
        HkAP8kmGk/STUUpAkjb6t82MCo3lvawkOOgfLyAVzYDBAUtM/JlVZlDQsorOoICYVUMMkIDLABkg1Yqy
        wUbACGRP/vZMa3nTrwcAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btn_Setting.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABdSURBVChTY/j//z8DMZgoRSCDiFeo5ZQ8U9slDScG
        ycNMnGkcUDHTJLAKA4PEgYrgCkHWzzQPa5xpEdECxyA+TBG6G+GK0RVh88xMs9B6FJNgQYfN12A3oWOi
        gwcA1+QMdX3e8ZQAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btn_close.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEgAAAAYCAYAAABZY7uwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAJsSURBVFhH7ZchTwNBEEbrUIjaqrqaOlRFBQ5TQUKCQSBwaP4bDocjTVAkJIQgEAgSFAJxzCs7
        ZG9ur925awJJd5NJe9vb73befju3HQxKKwQKgUJgRwhUVXXQN9ahEu1F3/jTpegLh/EF0AaX/StAsmIj
        iWFqUtK/JzHxWDLhoCPpO2xx1kz6j+1vTkDnMv60ZduhfWF/y84nwHmUzzsLKcC5ls83iWmuqEkWOC8S
        DwlIwLmVeA8J/tYuByDgvEqQg4UEHPL6kLiMIeXmMogAyddqqZAiOPT3BfSMiIGkcOjfBiB0niTOAgiF
        Q393QJAMkFhh2n24xjk0VifbPUHPvsVwEZOnsdJc4xwa8E8kamMcDuKNBhRcSuM5uGoZroFfc49cL7Id
        pDfKIOqQQoI4DTiu+tMCiORjSJ/r4ADLCchCUv0knE6AQmJjGaxwvuT7zE36x5Ft5yAS0cmjz0on7+0A
        CG2Kcax/BYxUuPMSkX2Jm7CyTJ6Go0ZesZak59JPwaSpPtsBZzUgdQDENqU8xPpsO61JNVCunAIcrQmI
        MuFlV0iJhIGjemxbJq3bmQLegOQExNtL4VDTcI7WPPLBqd0AGThMdhy22zBacQprtpMMIODo5IHDRFkA
        zkWtkByAgKM6wNEzD4vAvGk8twYp20EycCKBMMRXcKLCrZDY1/NcUQMI61MsYzi6pRQS+iTW5RzEG4q6
        ib49EAIPSOjX6lFuLqv7ZPDUwjGQXMXaACJpPcClCjIOaxRrh4NwJJAa20j6+I1nN4q1C9C2b04Acv+7
        dwJy/7vfds4uvQJoA64CyOWncnMhsAsEvgFwiLCdNplIvQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btn_close.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABdSURBVChTY/j//z8DMZgoRSCDiFeo5ZQ8U9slDScG
        ycNMnGkcUDHTJLAKA4PEgYrgCkHWzzQPa5xpEdECxyA+TBG6G+GK0RVh88xMs9B6FJNgQYfN12A3oWOi
        gwcA1+QMdX3e8ZQAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btn_normal.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEgAAAAYCAYAAABZY7uwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAACtSURBVFhH7ZdBDoAgDAT5k+/w/8+pcsRol1ISIw4JJ+kaplsKpTAgAAEI/ISAmW3Z6aE6tffs
        fDUVWTg1HkDCZQD6EqDT0nJEavamxKT+NSbooB795tyK7Kf0qEcElwQkMmYTAHmdreao+T7gIK+zVf2c
        gwDkEKh4AQQgSsy5XXMGiadHHpBq9RO6mPpFtosp/fESi2y+Zy1vMUEJQADqKaTnNTgox49oCCxI4ACi
        XiXJJG3QswAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btn_normal.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABdSURBVChTY/j//z8DMZgoRSCDiFeo5ZQ8U9slDScG
        ycNMnGkcUDHTJLAKA4PEgYrgCkHWzzQPa5xpEdECxyA+TBG6G+GK0RVh88xMs9B6FJNgQYfN12A3oWOi
        gwcA1+QMdX3e8ZQAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btn_minimize.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEgAAAAYCAYAAABZY7uwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAACXSURBVFhH7ZfRCcAgDESzU+foTK7RBTpnGn8sFFpTTijYF8hfcuDzNGpGQAACEPgJAXdf1HxC
        Fdqrmp9uhQqn9gOo4zIAAUi7h3AQDsJBt6eAKdZ5RAAIQNo7cyoHxWJKZCZKFtsF0JYRj5pa174oL8b8
        ntSvde2Lkl2LASiNalzhVEdsHJZTCUBMMc1XOEjjRzcEJiRwAI5Qw5w2ZckOAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btn_minimize.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABdSURBVChTY/j//z8DMZgoRSCDiFeo5ZQ8U9slDScG
        ycNMnGkcUDHTJLAKA4PEgYrgCkHWzzQPa5xpEdECxyA+TBG6G+GK0RVh88xMs9B6FJNgQYfN12A3oWOi
        gwcA1+QMdX3e8ZQAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="mNotifyIcon.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="menuMain.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>132, 17</value>
  </metadata>
  <metadata name="tmrTimer.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>252, 19</value>
  </metadata>
  <data name="$this.tbGuiBackground" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADwAAACWCAYAAACCe+v6AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAGNSURBVHhe7duxbcNQEANQ75QFskIW10oKHCBVkI4Fxf8MuD2c+KhDXOT18fl1n/R9nfSw72f1
        wOvihAmPXXGVVmmVfvZfZt5h77B32Dv8qJ+Xjpaj5Wg5Wo5W8x1wpZt1ErsRTqTYPINws05iN8KJFJtn
        EG7WSexGOJFi8wzCzTqJ3QgnUmyeQbhZJ7Eb4USKzTMIN+skdiOcSLF5BuFmncRuhBMpNs8g3KyT2I1w
        IsXmGYSbdRK7EU6k2DyDcLNOYjfCiRSbZxBu1knsRjiRYvMMws06id0IJ1JsnkG4WSexG+FEis0zCDfr
        JHYjnEixeQbhZp3EboQTKTbPINysk9jtTOH7kM+7IT/Cp3w88Lo0YcJjCaj0GOifxyFMeCwBlR4DdbRU
        WqXHElDpMVBXWqVVeiwBlR4DdaVV+phKX9d1n/BV6WMqvf6gv8+n0uvShAmPJaDSY6B+Lam0So8loNJj
        oK60Sqv0WAIqPQbqSqu0So8loNJjoK60Sqv0WAIqPQb6/5VO/Of1U2Z8A/bWDi2iKYY9AAAAAElFTkSu
        QmCC
</value>
  </data>
</root>