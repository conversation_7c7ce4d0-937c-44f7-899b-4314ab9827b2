﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace iWechatPCTool
{
   public class LiveUpdateHelper
    {
        public static void StartLiveUpdateExe(string zipFile, string upgradeFolder, string softPath, string softVersion)
        {
            try
            {
                bool blnNewIncrease = true;
                //增量更新时没有app.zip的压缩包
                if (blnNewIncrease == false)
                {
                    if (Utility.unzip(zipFile, upgradeFolder) <= 0)
                    {
                        if (File.Exists(zipFile))
                        {
                            File.Delete(zipFile);
                        }

                        return;
                    }
                }

                string strLiveUpdate = "";
                foreach (string Item in System.IO.Directory.GetFiles(upgradeFolder, "*.exe", System.IO.SearchOption.TopDirectoryOnly))
                {
                    if (System.IO.Path.GetFileName(Item).ToLower() == "liveupdate.exe")
                    {
                        strLiveUpdate = Item;

                        break; // TODO: might not be correct. Was : Exit For
                    }
                }

                string strAppZip = System.IO.Path.Combine(upgradeFolder, "app.zip");

                //防止路径中有空格，所以需要加双引号

                string str = "\"" + softPath + "\" " + "\"" + strAppZip + "\" " + "\"" + softVersion + "\"";
                if (blnNewIncrease)
                {
                    str = "\"" + softPath + "\" " + "\"" + upgradeFolder + "\" " + "\"" + softVersion + "\" " + "\"" + "0" + "\" " + "\"" + "1" + "\" " + "\"" + "1" + "\"";
                }
                Common.Log(strLiveUpdate + " " + str);
                if (strLiveUpdate.Length > 0 && System.IO.File.Exists(strLiveUpdate))
                {
                    System.Diagnostics.Process.Start(strLiveUpdate, str);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "StartLiveUpdateExe");
            }
        }

    }
}
