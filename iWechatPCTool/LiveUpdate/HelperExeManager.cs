﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Diagnostics;
using System.IO;
using iTong.CoreFoundation;
using System.Drawing;

namespace iWechatPCTool
{
    /// <summary>
    /// Helper.exe的管理类
    /// </summary>
    /// <remarks>jll</remarks>
    public class HelperExeManager
    {
        private static string mHelperPath = Path.Combine(Folder.AppFolder, "Helper.exe");

        /// <summary>
        /// 重启主程序
        /// </summary>
        public static void RestartApp()
        {
            if (!File.Exists(mHelperPath))
            {
                return;
            }

            try
            {
                Process pro = new Process();
                pro.StartInfo.FileName = mHelperPath;
                pro.StartInfo.Arguments = "/s";
                pro.Start();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "HelperExeManager.RestartApp");
            }
        }

        public enum CheckUpdateAction
        {
            None = 0,
            CheckUpdateForX86,
            CheckUpdateForX64
        }

        public static void CheckUpdateApp2FitiTunes(bool blnUserCheckUpgrade, string strAppName, string strAppExecutablePath, CheckUpdateAction action)
        {
            if (!File.Exists(mHelperPath))
            {
                return;
            }

            try
            {
                bool isUpdateForV64 = false;
                string strUrlUpdate = HelperExeManager.GetUpdateUrl(ref isUpdateForV64, action);
                string strUpdateLogPath = HelperExeManager.GetUpdateLogUrl();

                string strArgs = "\"" + "/update" + "\" ";

                //装机是用静默升级
                if (Common.IsZJMode())
                {
                    strArgs = "\"" + "/silentupdate" + "\" ";
                }

                if (blnUserCheckUpgrade)
                {
                    //显示托盘，提示正在检查升级（或 已经是最新版本了）
                    strArgs = strArgs + "\"" + "/notify" + "\" ";
                }
                else
                {
                    //静默检查升级
                    strArgs = strArgs + "\"" + "/silent" + "\" ";
                }

                strArgs = strArgs + "\"" + strAppExecutablePath + "\" ";
                strArgs = strArgs + "\"" + strAppName + "\" ";

                //strUrlUpdate = String.Format(WebUrl.ServerAssitantUpdate, "2.0.9.0")      '测试
                strArgs = strArgs + "\"" + strUrlUpdate + "\" ";
                strArgs = strArgs + "\"" + strUpdateLogPath + "\" ";
                strArgs = strArgs + "\"" + (isUpdateForV64 ? "1" : "0") + "\" ";
                //是否升级到64位版本的助手
                strArgs = strArgs + "\"" + "0" + "\" ";
                //1代表自动升级，0代表主动升级
                strArgs = strArgs + "\"" + "0" + "\" ";
                strArgs = strArgs + "\"" + "1" + "\"";
                Common.Log(string.Format("{0} {1}", mHelperPath, strArgs));
                System.Diagnostics.Process pro = new System.Diagnostics.Process();
                pro.StartInfo.FileName = mHelperPath;
                pro.StartInfo.Arguments = strArgs;
                pro.Start();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "HelperExeManager.CheckUpdate");
            }
        }

        /// <summary>
        /// 检测升级
        /// </summary>
        /// <param name="blnUserCheckUpgrade">false 程序自动检测升级；true 手动检测升级</param>
        /// <param name="strAppName">主程序名称。设置的语言不同，名称不同。</param>
        public static void CheckUpdate(bool blnUserCheckUpgrade, string strAppName, string strAppExecutablePath)
        {
            CheckUpdateApp2FitiTunes(blnUserCheckUpgrade, strAppName, strAppExecutablePath, CheckUpdateAction.None);
        }

        private static string GetUpdateUrl(ref bool isUpdateForV64, CheckUpdateAction action)
        {
            bool blnNewIncrease = true;
            string strVer = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString(4);

            string strUrlUpdate = string.Format(WebUrl.ServerAssitantUpdate, strVer, GetVersionType(ref isUpdateForV64));
            if (blnNewIncrease)
            {
                strUrlUpdate = string.Format(WebUrl.ServerAssitantUpdateNew, strVer, GetVersionType(ref isUpdateForV64), System.Web.HttpUtility.UrlEncode(Common.GetChannelID()));
            }

            return strUrlUpdate;
        }

        private static string GetUpdateLogUrl()
        {
            string strUpdateLogPath = WebUrl.ServerZxkefuUpdateLog;
            return strUpdateLogPath;
        }

        private static string GetVersionX86Type()
        {
            string strVersionType = "iwechatpctool";
            return strVersionType;
        }

        private static string GetVersionType(ref bool isUpdateForV64)
        {
            string strVersionType = "iwechatpctool";

            return strVersionType;
        }

        /// <summary>
        /// 注册协议和浏览器插件
        /// </summary>
        public static void RegisterProtocol()
        {
            if (!File.Exists(mHelperPath))
            {
                return;
            }

            try
            {
                Process.Start(mHelperPath, "/r");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "HelperExeManager.RegisterProtocol");
            }
        }

        /// <summary>
        /// 以WinXP兼容模式运行主程序
        /// </summary>
        /// <param name="strExeName">主程序名称。</param>
        public static void RunAppInWinXP(string strExeName)
        {
            if (!File.Exists(mHelperPath))
            {
                return;
            }

            try
            {
                Process pro = new Process();
                pro.StartInfo.FileName = mHelperPath;
                pro.StartInfo.Arguments = string.Format("/o \"{0}\"", strExeName);
                pro.Start();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "HelperExeManager.RunAppInWinXP");
            }
        }

    }
}
