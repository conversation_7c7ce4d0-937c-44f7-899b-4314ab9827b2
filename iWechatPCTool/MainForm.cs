﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace iWechatPCTool
{
    public partial class MainForm : tbBaseGuiForm
    {
        private double _lngPassTime = 0;


        #region --- 初始化 ---

        public MainForm()
        {
            InitializeComponent();
        }

        protected override void SetInterface()
        {
            base.SetInterface();
            this.btn_close.tbToolTip = "关闭";
            this.btn_normal.tbToolTip = "最大化";
            this.btn_minimize.tbToolTip = "最小化";
            this.mNotifyIcon.Text = "iWechatPCTool";
        }

        protected override void InitControls()
        {
            base.InitControls();
        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            this.tmrTimer.Start();
        }

        #endregion

        #region --- 窗体事件 ---

        private void MainForm_Resize(object sender, EventArgs e)
        {
            if (this.btn_normal != null)
            {
                if (this.WindowState == FormWindowState.Maximized)
                {
                    this.btn_normal.tbToolTip = "还原";
                    this.btn_normal.tbBackgroundImage = Properties.Resources.btn_restore;
                }
                else if (this.WindowState == FormWindowState.Normal)
                {
                    this.btn_normal.tbToolTip = "最大化";
                    this.btn_normal.tbBackgroundImage = Properties.Resources.btn_max;
                }
            }
        }

        private void btn_Setting_Click(object sender, EventArgs e)
        {
            this.menuMain.Show(this.btn_Setting, new Point(0, this.btn_Setting.Height));
        }

        private void tmrTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                _lngPassTime += 1;
                if (!Common.IsVshostMode())
                {
                }

                //获取服务器数据
                if (this._lngPassTime == 40)
                {
                    //开始收集数据
                    //this.StartToSendData();
                }
                else if (this._lngPassTime == 30 || _lngPassTime % 3600 == 0)
                {
                    HelperExeManager.CheckUpdate(false, "iwechatpctool", Application.ExecutablePath);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MainForm_tmrTimer_Tick");
            }
        }

        private void mNotifyIcon_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            if (this.WindowState == FormWindowState.Minimized)
            {
                this.Show();
                this.WindowState = FormWindowState.Normal;
            }
            this.Activate();
        }

        #endregion
    }
}
