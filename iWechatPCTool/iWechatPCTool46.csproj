﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{223C23B1-43CC-40E2-B68E-68A029C128D2}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>iWechatPCTool</RootNamespace>
    <AssemblyName>iWechatPCTool</AssemblyName>
    <TargetFrameworkVersion>v4.6</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Release\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <PlatformTarget>x86</PlatformTarget>
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="LiveUpdate\HelperExeManager.cs" />
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <None Include="app.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Components\Components46.vbproj">
      <Project>{f9779807-fdba-4be9-9da2-3746bb24597b}</Project>
      <Name>Components46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc46.csproj">
      <Project>{596775d3-3eea-4125-bac2-934df77fbeba}</Project>
      <Name>CoreMisc46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses46.csproj">
      <Project>{a5844815-737d-486e-b7de-d57262f9e5f4}</Project>
      <Name>CoreReses46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag46.csproj">
      <Project>{7f12e495-c239-4b39-8156-aff1ad7a95b6}</Project>
      <Name>CoreTag46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS46.csproj">
      <Project>{c0b96f67-6997-454c-a762-470f156addf4}</Project>
      <Name>CoreUtilCS46</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil46.vbproj">
      <Project>{0c859628-5952-44d7-8c32-ab838ef37ede}</Project>
      <Name>CoreUtil46</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\ICSharpCode.SharpZLib46.csproj">
      <Project>{786969bf-d271-49a4-a98e-5affa29e2a55}</Project>
      <Name>ICSharpCode.SharpZLib46</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_close.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_max.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_min.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_restore.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\btn_setting.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\frm_bg_blank.png" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>