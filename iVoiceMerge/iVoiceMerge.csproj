﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{91BE7085-E95F-46DF-8BD8-36BDD0286D6B}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>iVoiceMerge</RootNamespace>
    <AssemblyName>iVoiceMerge</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>Resources\main.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Management" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="API.cs" />
    <Compile Include="frmMergeProgress.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMergeProgress.Designer.cs">
      <DependentUpon>frmMergeProgress.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="zDataGridView\tbDataGridViewEx.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDataGridView\tbDataGridViewMediaColumn.cs" />
    <Compile Include="zDataGridView\tbDataGridViewProgressColumn.cs" />
    <Compile Include="zDataGridView\tbMusicPlayer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="zDataGridView\tbMusicPlayer.Designer.cs">
      <DependentUpon>tbMusicPlayer.cs</DependentUpon>
    </Compile>
    <Compile Include="zLiveUpdate\LiveUpdateHelper.cs" />
    <EmbeddedResource Include="frmMergeProgress.resx">
      <DependentUpon>frmMergeProgress.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="zDataGridView\tbMusicPlayer.resx">
      <DependentUpon>tbMusicPlayer.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Lang\zh-cn.lang">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ProgressWaitting008.png" />
    <None Include="Resources\ProgressWaitting007.png" />
    <None Include="Resources\ProgressWaitting006.png" />
    <None Include="Resources\ProgressWaitting005.png" />
    <None Include="Resources\ProgressWaitting004.png" />
    <None Include="Resources\ProgressWaitting003.png" />
    <None Include="Resources\ProgressWaitting002.png" />
    <None Include="Resources\ProgressWaitting001.png" />
    <None Include="Resources\player_voice_bg.png" />
    <None Include="Resources\chk_4_true.png" />
    <None Include="Resources\btn_4_retreatex.png" />
    <None Include="Resources\btn_add_4.png" />
    <None Include="Resources\btn_delete_4.png" />
    <None Include="Resources\dgv_playing.png" />
    <None Include="Resources\dgv_4_play.png" />
    <None Include="Resources\dgv_4_stop.png" />
    <None Include="Resources\btn_4_blue.png" />
    <None Include="Resources\btn_4_moveup.png" />
    <None Include="Resources\btn_4_openfile.png" />
    <None Include="Resources\btn_4_movedown.png" />
    <None Include="Resources\btn_3_Contact.png" />
    <None Include="Resources\app_downloads_value.png" />
    <None Include="Resources\dgv_progress_value.png" />
    <None Include="Resources\app_progress_value.png" />
    <Content Include="Resources\frm_bg_sub1.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\btn_min.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\btn_max.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\btn_close.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\icon_lock.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\dgv_filenoexist.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\dgv_payment.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\dgv_33_loading.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\dgv_waiting.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\btn_4_white.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\app_btn_4_open.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\app_progress_background.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\btn_3_cancel.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\app_btn_4_ignore.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\btn_3_openfolder.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Resources\app_cell_fail.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="Resources\pic_muise.png" />
    <None Include="Resources\pic_sign.png" />
    <None Include="Resources\main.ico" />
    <None Include="Resources\player_bg.png" />
    <None Include="Resources\player_loop.png" />
    <None Include="Resources\player_next.png" />
    <None Include="Resources\player_order.png" />
    <None Include="Resources\player_pause.png" />
    <None Include="Resources\player_play.png" />
    <None Include="Resources\player_prev.png" />
    <None Include="Resources\player_random.png" />
    <None Include="Resources\player_single.png" />
    <None Include="Resources\player_time_bg.png" />
    <None Include="Resources\player_voice.png" />
    <Content Include="IncludeDlls\ffmpeg.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Components\Components40.vbproj">
      <Project>{f9779807-fdba-4be9-9da2-3746bb24597b}</Project>
      <Name>Components40</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc40.csproj">
      <Project>{596775d3-3eea-4125-bac2-934df77fbeba}</Project>
      <Name>CoreMisc40</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreModuleEx\CoreModuleCS40.csproj">
      <Project>{2435928b-bf66-4791-b976-aa373477fbfe}</Project>
      <Name>CoreModuleCS40</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses40.csproj">
      <Project>{a5844815-737d-486e-b7de-d57262f9e5f4}</Project>
      <Name>CoreReses40</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag40.csproj">
      <Project>{7f12e495-c239-4b39-8156-aff1ad7a95b6}</Project>
      <Name>CoreTag40</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS40.csproj">
      <Project>{c0b96f67-6997-454c-a762-470f156addf4}</Project>
      <Name>CoreUtilCS40</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil40.vbproj">
      <Project>{0c859628-5952-44d7-8c32-ab838ef37ede}</Project>
      <Name>CoreUtil40</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf40.csproj">
      <Project>{3fd45453-6069-4dd0-bbf0-17b25606e359}</Project>
      <Name>ProtoBuf40</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\ICSharpCode.SharpZLib40.csproj">
      <Project>{786969bf-d271-49a4-a98e-5affa29e2a55}</Project>
      <Name>ICSharpCode.SharpZLib40</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>