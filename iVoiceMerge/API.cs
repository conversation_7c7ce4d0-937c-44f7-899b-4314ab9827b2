﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace iVoiceMerge
{
    public class API
    {
#if MAC
        public static bool SetDllDirectory(string lpPathName)
		{
			return true;
		}

        public static IntPtr LoadLibrary(string lpFileName)
		{
			return Dlfcn.dlopen (lpFileName, 0);
		}

		public static IntPtr GetProcAddress(IntPtr hModule, string lpProcName)
		{
			return Dlfcn.GetIntPtr (hModule, lpProcName);
		}

		public static IntPtr GetProcAddressEx(IntPtr hModule, string lpProcName)
		{
			return Dlfcn.GetIndirect (hModule, lpProcName);
		}

        public static bool FreeLibrary(IntPtr hModule)
		{
			Dlfcn.dlclose (hModule);
			return true;
		}
#else
        [DllImport("Kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        public static extern bool SetDllDirectory(string lpPathName);

        [DllImport("Kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        public static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        public static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Winapi)]
        public static extern bool FreeLibrary(IntPtr hModule);

#endif

        // 根据函数名返回函数的 Delegate
        public static Delegate GetAddress(IntPtr dllModule, string functionName, Type t)
        {

            IntPtr addr = GetProcAddress(dllModule, functionName);
            if (addr == IntPtr.Zero)
                return null;
            else
                return Marshal.GetDelegateForFunctionPointer(addr, t);
        }

        // libstk.dll 函数 delegate 声明
        public delegate bool Open(string fileWavName);
        public delegate bool Close();
        public delegate bool Encode(string sourceFileWav, string destFileMp3);
        public delegate bool Decode(string sourceFileMp3, string destFileWav);
        public delegate bool Split(string fileWavOut, double begin, double end, double in_time, double out_time);
        public delegate double GetLValue(double secondTime);
        public delegate int GetDecodePercent();
        public delegate int GetPercent();

        #region PIPE 相关

        public const int SW_HIDE = 0;
        public const int SW_SHOWNORMAL = 1;
        public const int SW_NORMAL = 1;
        public const int SW_SHOWMINIMIZED = 2;
        public const int SW_SHOWMAXIMIZED = 3;
        public const int SW_MAXIMIZE = 3;
        public const int SW_SHOWNOACTIVATE = 4;
        public const int SW_SHOW = 5;
        public const int SW_MINIMIZE = 6;
        public const int SW_SHOWMINNOACTIVE = 7;
        public const int SW_SHOWNA = 8;
        public const int SW_RESTORE = 9;
        public const int SW_SHOWDEFAULT = 10;
        public const int SW_FORCEMINIMIZE = 11;
        public const int SW_MAX = 11;

        public const int NORMAL_PRIORITY_CLASS = 0x00000020;
        public const int CREATE_NO_WINDOW = 0x08000000;         // 64位架构调用CreateProcess不会显示窗体

        public const int STARTF_USESHOWWINDOW = 0x00000001;
        public const int STARTF_USESIZE = 0x00000002;
        public const int STARTF_USEPOSITION = 0x00000004;
        public const int STARTF_USECOUNTCHARS = 0x00000008;
        public const int STARTF_USEFILLATTRIBUTE = 0x00000010;
        public const int STARTF_RUNFULLSCREEN = 0x00000020;  // ignored for non-x86 platforms
        public const int STARTF_FORCEONFEEDBACK = 0x00000040;
        public const int STARTF_FORCEOFFFEEDBACK = 0x00000080;
        public const int STARTF_USESTDHANDLES = 0x00000100;

        [DllImport("Kernel32.dll")]
        public static extern bool CreateProcess(string lpApplicationName,
                                                string lpCommandLine,
                                                SECURITY_ATTRIBUTES lpProcessAttributes,
                                                SECURITY_ATTRIBUTES lpThreadAttributes,
                                                bool bInheritHandles,
                                                int dwCreationFlags,
                                                string lpEnvironment,
                                                string lpCurrentDirectory,
                                                ref STARTUPINFO lpStartupInfo,
                                                ref PROCESS_INFORMATION lpProcessInformation
                                                );


        [DllImport("Kernel32.dll")]
        public static extern bool CloseHandle(IntPtr hObject);

        [DllImport("kernel32.dll")]
        public static extern bool CreatePipe(ref IntPtr hReadPipe, ref IntPtr hWritePipe, SECURITY_ATTRIBUTES lpPipeAttributes, int nSize);

        [DllImport("kernel32.dll")]
        public static extern bool ReadFile(IntPtr hFile, byte[] lpBuffer, int nNumberOfBytesToRead, ref int lpNumberOfBytesRead, IntPtr lpOverlapped);

        [DllImport("kernel32.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int GetLastError();

        [DllImport("kernel32.dll", CharSet = CharSet.Ansi)]
        public static extern int lstrlenA(IntPtr ptr);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto)]
        public static extern int lstrlen(IntPtr ptr);

        [DllImport("kernel32.dll", CharSet = CharSet.Unicode)]
        public static extern int lstrlenW(IntPtr ptr);

        [DllImport("kernel32.dll", EntryPoint = "RtlMoveMemory", CharSet = CharSet.Unicode)]
        public static extern void CopyMemoryUni(StringBuilder pdst, IntPtr psrc, IntPtr sizetcb);

        #endregion

    }

    #region SECURITY_ATTRIBUTES

    [StructLayout(LayoutKind.Sequential)]
    public class SECURITY_ATTRIBUTES
    {
        public int nLength;
        public string lpSecurityDescriptor;
        public bool bInheritHandle;
    }

    #endregion

    #region STARTUPINFO

    [StructLayout(LayoutKind.Sequential)]
    public struct STARTUPINFO
    {
        public int cb;
        public IntPtr lpReserved;
        public IntPtr lpDesktop;
        public IntPtr lpTitle;
        public int dwX;
        public int dwY;
        public int dwXSize;
        public int dwYSize;
        public int dwXCountChars;
        public int dwYCountChars;
        public int dwFillAttribute;
        public int dwFlags;
        public short wShowWindow;
        public short cbReserved2;
        public IntPtr lpReserved2;
        public IntPtr hStdInput;
        public IntPtr hStdOutput;
        public IntPtr hStdError;
    }

    #endregion

    #region PROCESS_INFORMATION

    [StructLayout(LayoutKind.Sequential)]
    public struct PROCESS_INFORMATION
    {
        public IntPtr hProcess;
        public IntPtr hThread;
        public int dwProcessId;
        public int dwThreadId;

        //public static PROCESS_INFORMATION Empty = new PROCESS_INFORMATION();
        public bool IsEmpty
        {
            get
            {
                return (hProcess == IntPtr.Zero && hThread == IntPtr.Zero && dwProcessId == 0 && dwThreadId == 0);
            }
        }
    }

    #endregion
}
