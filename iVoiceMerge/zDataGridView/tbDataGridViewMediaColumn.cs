﻿using iTong.Components;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace iVoiceMerge
{
    public class tbDataGridViewMediaColumn : tbDataGridViewColumn
    {
        public tbDataGridViewMediaColumn()
        {
            this.CellTemplate = new tbDataGridViewMediaCell();
        }

        public override object Clone()
        {
            tbDataGridViewMediaColumn column = new tbDataGridViewMediaColumn();
            base.tbClone(column);

            return column;
        }
    }

    public class tbDataGridViewMediaCell : tbDataGridViewCell
    {
        private MouseStates mMouseState = MouseStates.MouseLeave;
        private CellButtonStyle mCellStatus = CellButtonStyle.None;

        private int mProgressValue = -1;



        public override object DefaultNewRowValue
        {
            get
            {
                return "tbDataGridViewMediaCell";
            }
        }

        public override Type ValueType
        {
            get
            {
                return typeof(System.Boolean);
            }
            set
            {
            }
        }

        public CellButtonStyle tbCellStatus
        {
            get
            {
                return this.mCellStatus;
            }
            set
            {
                this.mCellStatus = value;

                this.Invalidate();
            }
        }

        public int tbProgressValue
        {
            get
            {
                return this.mProgressValue;
            }
            set
            {
                if (value < -1)
                    value = -1;
                else if (value >= 100)
                {
                    value = 100;
                    this.mCellStatus = CellButtonStyle.None;
                }
                this.mProgressValue = value;
                this.Invalidate();
            }
        }



        private void InitTip()
        {
            if (this.mCellStatus == CellButtonStyle.FileNoExist)
            {
                //if (this.ToolTipText.Length == 0 && (tbDataGridViewMediaColumn)this.DataGridView.Columns[this.ColumnIndex].tbTextTip.Length > 0)
                //    this.ToolTipText = (tbDataGridViewMediaColumn)this.DataGridView.Columns[this.ColumnIndex].tbTextTip;
            }
            else if (this.ToolTipText.Length > 0)
                this.ToolTipText = string.Empty;
        }

        protected override void OnMouseDown(System.Windows.Forms.DataGridViewCellMouseEventArgs e)
        {
            base.OnMouseDown(e);

            if (e.Button != MouseButtons.Left)
                return;

            if (this.mMouseState != MouseStates.MouseDown)
            {
                this.mMouseState = MouseStates.MouseDown;
                this.Invalidate();
            }
        }

        protected override void OnMouseUp(System.Windows.Forms.DataGridViewCellMouseEventArgs e)
        {
            base.OnMouseUp(e);

            if (e.Button != MouseButtons.Left)
                return;

            if (this.mMouseState != MouseStates.MouseLeave)
            {
                if (this.mMouseState == MouseStates.MouseDown)
                    ((tbDataGridViewEx)this.DataGridView).OnCellButtonClick(new CellButtonEventArgs(this.mCellStatus, this));
                this.mMouseState = MouseStates.MouseLeave;
                this.Invalidate();
            }
        }

        protected override void OnMouseEnter(int rowIndex)
        {
            base.OnMouseEnter(rowIndex);

            if (this.mMouseState != MouseStates.MouseHover)
            {
                this.mMouseState = MouseStates.MouseHover;
                this.Invalidate();
            }
        }

        protected override void OnMouseLeave(int rowIndex)
        {
            base.OnMouseLeave(rowIndex);

            if (this.mMouseState != MouseStates.MouseLeave)
            {
                this.mMouseState = MouseStates.MouseLeave;
                this.Invalidate();
            }
        }

        protected override void Paint(System.Drawing.Graphics g, System.Drawing.Rectangle clipBounds, System.Drawing.Rectangle cellBounds, int rowIndex, System.Windows.Forms.DataGridViewElementStates elementState, object value, object formattedValue, string errorText, System.Windows.Forms.DataGridViewCellStyle cellStyle, System.Windows.Forms.DataGridViewAdvancedBorderStyle advancedBorderStyle, System.Windows.Forms.DataGridViewPaintParts paintParts)
        {
            base.Paint(g, clipBounds, cellBounds, rowIndex, elementState, value, formattedValue, errorText, cellStyle, advancedBorderStyle, paintParts);

            if (this.mCellStatus == CellButtonStyle.Progress)
            {
                if (this.mProgressValue == -1)
                {
                    Image imgWait = global::iVoiceMerge.Properties.Resources.dgv_waiting;
                    Rectangle rect = new Rectangle(cellBounds.Left + (cellBounds.Width - imgWait.Width) / 2, cellBounds.Top + (cellBounds.Height - imgWait.Height) / 2, imgWait.Width, imgWait.Height);
                    g.DrawImage(imgWait, rect, new Rectangle(0, 0, imgWait.Width, imgWait.Height), GraphicsUnit.Pixel);
                }
                else
                {
                    Color penColor = cellStyle.ForeColor;
                    if (this.Selected)
                        penColor = cellStyle.SelectionForeColor;

                    Image imgLoading = global::iVoiceMerge.Properties.Resources.dgv_33_loading;
                    int intHeight = imgLoading.Height / 33;
                    Size imgSize = new Size(14, 14);
                    Rectangle rect = new Rectangle(cellBounds.Left + (cellBounds.Width - imgSize.Width) / 2, cellBounds.Top + (cellBounds.Height - imgSize.Height) / 2, imgSize.Width, imgSize.Height);

                    int index = (int)Math.Floor((double)(16 * this.mProgressValue / 100));
                    Rectangle rectImage = new Rectangle(0, index * intHeight, imgLoading.Width, intHeight);

                    g.DrawImage(imgLoading, rect, rectImage, GraphicsUnit.Pixel);
                }

                return;
            }


            Image img = null/* TODO Change to default(_) if this is not a reference type */;
            ImageState imgState = ImageState.FourState;

            if (this.DataGridView.tbMouseHoverRowIndex == rowIndex)
            {
                switch (this.mCellStatus)
                {
                    case CellButtonStyle.Payment:
                        {
                            img = global::iVoiceMerge.Properties.Resources.dgv_payment;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.FileNoExist:
                        {
                            img = global::iVoiceMerge.Properties.Resources.dgv_filenoexist;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.BackupLock:
                        {
                            img = global::iVoiceMerge.Properties.Resources.icon_lock;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.BackupUnlock:
                        {
                            img = null/* TODO Change to default(_) if this is not a reference type */;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.Playing:
                        {
                            img = global::iVoiceMerge.Properties.Resources.dgv_4_stop;
                            break;
                        }

                    case CellButtonStyle.Paused:
                        {
                            img = global::iVoiceMerge.Properties.Resources.dgv_4_play;
                            break;
                        }

                    default:
                        {
                            if (System.Convert.ToBoolean(this.Value))
                                img = global::iVoiceMerge.Properties.Resources.dgv_4_play;
                            break;
                        }
                }
            }
            else
                switch (this.mCellStatus)
                {
                    case CellButtonStyle.Payment:
                        {
                            img = global::iVoiceMerge.Properties.Resources.dgv_payment;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.FileNoExist:
                        {
                            img = global::iVoiceMerge.Properties.Resources.dgv_filenoexist;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.BackupLock:
                        {
                            img = global::iVoiceMerge.Properties.Resources.icon_lock;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.BackupUnlock:
                        {
                            img = null/* TODO Change to default(_) if this is not a reference type */;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.Playing:
                        {
                            img = global::iVoiceMerge.Properties.Resources.dgv_playing;
                            imgState = ImageState.OneState;
                            break;
                        }

                    case CellButtonStyle.Paused:
                        {
                            img = global::iVoiceMerge.Properties.Resources.dgv_playing;
                            imgState = ImageState.OneState;
                            break;
                        }
                }


            if (img != null)
            {
                int intW = img.Width / System.Convert.ToInt32(imgState);
                Rectangle rectDraw = new Rectangle(cellBounds.Left + (cellBounds.Width - intW) / 2, cellBounds.Top + (cellBounds.Height - img.Height) / 2, intW, img.Height);

                GuiHelper.DrawImage(g, rectDraw, tbSplitStructure.Empty, img, true, this.mMouseState, imgState);
            }
        }
    }

}
