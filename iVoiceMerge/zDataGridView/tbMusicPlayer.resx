﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="cmsPlayStyle.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnPlay.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnPrev.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnNext.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnVoice.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnPlayStyle.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="proProgress.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABeSURBVChTtdExDoAwDEPRXqZqL98LMTF3QcABjP8e
        kFqV4W2xlShp2zuyNTtNg8iQzZISZcUOi4ZH0FEopD0amNEonDnzzf1L4fKTq616SqUQfJpNL4uGv5Ah
        WySlB5vwOLkNu2hqAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="proProgress.tbDotImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADYAAAASCAYAAAAQeC39AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAADK0lEQVRYR92W30uTURjHLTKtoJSSrIuS
        sAuFrroIZPf+D910VYMKvQkvvDF31UWQW7QWGII328BQXMspROCPMX8NcQq6aU6dw5/74bYGYjw93+OZ
        5DhuXryj0QMfOJzzPN/3fc5zzvO+JUT0X6Kyc8x55gJzkSmXYIw5rMEnnxWVDpxKLRbL/bGxsQ9erzcw
        MzNDAGOee2cyme7AR/qeZkWlg4XyoaGh51NTU+lwOEypVIoODw8FGG9sbBCvJZ1O5xP2LZMx2VYQnVAo
        RLFYjJLJpADj9fX1vDqYKOvv73+2tLQkgtLptHiJRCIhwBhzkUiE/H4/9fb2PkaMjM2Y5jqBQIC2t7cF
        SCQYDAqQKOY2NzdpcXFRqYPzWWowGOpGR0dTOzs7tL+/Lx68t7dHu7u7Aoyj0ahY29raIvaNNjc31yBW
        ahREB9VFQsvLy2IjFhYWBBhjDgliXaEjMrzicDjMcMSLYCfwUBVYgw92sq+v7w3HXpYamuvg5TNJwU9F
        Jjkkm6UjOksln+Ug7gPALuUCPhBzuVw+jq2QGprrrK2t0erqKq2srOQEPjieWTqibVYNDg4e4GEQOysD
        AwMRjr0hNYpNR3wTqr85nQfIWrUjKn7yEfjqcEDoJoNLq7lOprJnAUc2S+dI6EtPj39ubk55jlXMzs6S
        3Wbz/iWkuQ4SV91PFbhrWTpHpf9oNn/iznLcdfIxPDxMJqPxLceeOEJa6ninp4+7aT4mJyezdcRFq2hs
        bGyw2+2/JiYmaH5+Pifj4+Nks9niOp3uIcdekxoF0UEl8HnIBSqv0BGtES3ydmtr6ytus/gmkM/nU+J2
        u8lqtf5uaWl5wTG3ZGymTWuuw91RVDYejytBUqfoHH0QGbTJGr1e/7SrqyvMXYlGRkZEiQGODOa6u7vj
        TU1NL+HLYHdOfFiZguiguvjDwH3C3waS9Xg8uXSEIUNcuErmbm1t7SNDe/t7PrM/Pnd2hpmQsaPj++u2
        NmN9fX0D+0AEvogRuyOt2HSEZcSuMtXMPaaOeSDBGHMoN3ZGKcJWbDrCsICLd4mB4HWmSoIxBLCGcp8q
        wvaPdUpK/gBDZ1iYNLQZ7wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="proProgress.tbDownloadImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAA50lEQVQoU6XSsWrCYBSG4f8mYpLfJGYT
        vLQKDkETiIOlQiW0gvYOShchaBAHlyoitNAqVbTtEHQTOgmBXMDnOV5BSoZ3O98zHQFAaLXnou4MBtId
        nqQX4V/RhrdssEXYS1l647Nxt4AVrGA9fKH0uMkU3/KGt2yQVRF6PQrN9jtK3R/YvRh2/wD76ZgtvqUN
        b9kgaySk/5qYwR5WN84VG9KfpUL3l4lx/w2j85svMshKhVafj2RrC3m7yxcZbAmlOqmo3sdZa26QJzbY
        ur6NcjMtFpy3UG18/qnuCqq7zhjd0oa3bAAQF3M2d59nM1jzAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="proProgress.tbPlayImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAA50lEQVQoU6XSsWrCYBSG4f8mYpLfJGYT
        vLQKDkETiIOlQiW0gvYOShchaBAHlyoitNAqVbTtEHQTOgmBXMDnOV5BSoZ3O98zHQFAaLXnou4MBtId
        nqQX4V/RhrdssEXYS1l647Nxt4AVrGA9fKH0uMkU3/KGt2yQVRF6PQrN9jtK3R/YvRh2/wD76ZgtvqUN
        b9kgaySk/5qYwR5WN84VG9KfpUL3l4lx/w2j85svMshKhVafj2RrC3m7yxcZbAmlOqmo3sdZa26QJzbY
        ur6NcjMtFpy3UG18/qnuCqq7zhjd0oa3bAAQF3M2d59nM1jzAAAAAElFTkSuQmCC
</value>
  </data>
</root>