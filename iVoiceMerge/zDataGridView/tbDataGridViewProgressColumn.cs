﻿using iTong.Components;
using iTong.CoreFoundation;
using iTong.CoreModule;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace iVoiceMerge
{
    public partial class tbDataGridViewProgressColumn : tbDataGridViewProgressColumnBase
    {
        public tbDataGridViewProgressColumn()
        {
            this.CellTemplate = new tbDataGridViewProgressCellEx();
        }

        public override object Clone()
        {
            tbDataGridViewProgressColumn column = new tbDataGridViewProgressColumn();

            base.tbClone(column);

            return column;
        }
    }

    public class tbDataGridViewProgressCellEx : tbDataGridViewCell
    {
        private ProgressStyle mProgressStyle = ProgressStyle.None;
        private tbSplitStructure m_Split = new tbSplitStructure(5, 4, 6, 5);     // 背景图片的切割位置（Width1,Height1,Width2,Height2）
        private Padding m_Padding = new Padding(20, 0, 0, 0);
        private string mText = string.Empty;
        private bool mShowCancelButton = false;
        private Font mFont = null/* TODO Change to default(_) if this is not a reference type */;
        private Font mFontProgress = null/* TODO Change to default(_) if this is not a reference type */;

        // button style(三个按钮)
        private Rectangle mRectBackup = Rectangle.Empty;
        private Rectangle mRectUninstall = Rectangle.Empty;
        private Rectangle mRectDocument = Rectangle.Empty;
        private Rectangle mRectImport = Rectangle.Empty; // 导入按钮
        private int mButtonDiff = 10;
        // Private mButtonSize As New Size(global::iVoiceMerge.Properties.Resources.btn_4_backup.Width/4, global::iVoiceMerge.Properties.Resources.btn_4_backup.Height )
        private Image mImgBackup = global::iVoiceMerge.Properties.Resources.btn_4_white;
        private Image mImgUninstall = global::iVoiceMerge.Properties.Resources.btn_4_white;
        private Image mImgDocument = global::iVoiceMerge.Properties.Resources.app_btn_4_open;
        private ImageState mImgThirdImageState = ImageState.FourState;
        private Image mImgImport = global::iVoiceMerge.Properties.Resources.btn_4_white;
        private CellButtonStyle mCellButtonDown = CellButtonStyle.None;
        private CellButtonStyle mCellButtonHover = CellButtonStyle.None;
        private int mCellButtonEnable = (int)CellButtonStyle.Backup | (int)CellButtonStyle.Uninstall | (int)CellButtonStyle.Cancel | (int)CellButtonStyle.BackupFolder | (int)CellButtonStyle.Reinstall | (int)CellButtonStyle.Failed | (int)CellButtonStyle.OneButton | (int)CellButtonStyle.Ignore | (int)CellButtonStyle.Document;
        private string mTextBackup = "";
        private string mTextUnInstall = "";
        private string mTextReInstall = "";
        private string mTextImport = "";
        private bool mShowButtonText = true;  // 下载中心的按钮是图标，不需要绘文字。
        private bool mShowButtonFirst = true;  // 是否绘制第三个icon
        private bool mShowButtonSecond = true;  // 是否绘制第三个icon
        private bool mShowButtonThird = true;  // 是否绘制第三个icon

        private bool mShowButtonImport = false;  // 是否绘制导入

        private Image mIconFirst = null/* TODO Change to default(_) if this is not a reference type */;
        private Image mIconSecond = null/* TODO Change to default(_) if this is not a reference type */;

        private Image mIconImport = null/* TODO Change to default(_) if this is not a reference type */;

        // Progress Style
        private int mProgressValue = -1;
        private Rectangle mRectBackupFolder = Rectangle.Empty;
        private Image mImgProgressValue = global::iVoiceMerge.Properties.Resources.app_progress_value;
        private Image mImgProgressBackground = global::iVoiceMerge.Properties.Resources.app_progress_background;
        private Image mImgCancel = global::iVoiceMerge.Properties.Resources.btn_3_cancel;
        private ImageState mImgCancelState = ImageState.ThreeState;

        private bool mProgressMsgClick = false;  // 2015-07-30 by chenbihai 进度条 错误信息 添加是否可以点击

        // fail style
        private string mTextFail = "";
        private Font mFailedTextFont = null/* TODO Change to default(_) if this is not a reference type */;
        private Rectangle mRectFailed = Rectangle.Empty;

        // Waiting Style
        private System.Timers.Timer mTmrWaiting = null;
        private Image mImgProgressWaiting = null/* TODO Change to default(_) if this is not a reference type */;
        private Size mButtonSizeCommon = new Size(70, 24);
        private Size mButtonSizeMax = new Size(120, 24);

        // OneButton Style
        private Rectangle mRectOneButton = Rectangle.Empty;
        private Image mImgOneButton = global::iVoiceMerge.Properties.Resources.btn_4_white;
        // Private msOneButton As MouseStates = MouseStates.MouseLeave
        private Size mButtonSizeBackup = new Size(70, 24);
        private Size mButtonSizeUninstall = new Size(70, 24);
        private tbSplitStructure mOneButtonSplit = new tbSplitStructure(13, 11, 13, 11);
        private string mTextMore = "";
        private bool mShowIgnoreButton = false;
        private Image mImgIgnore = global::iVoiceMerge.Properties.Resources.app_btn_4_ignore;
        private Rectangle mRectIgnore = Rectangle.Empty;
        private Color mForColor = Color.Black;

        private int _ValueStep = 0;

        // BackupFolder Style
        private Image mImgBackupFolder = global::iVoiceMerge.Properties.Resources.btn_3_openfolder;
        private ImageState mBackupFolderState = ImageState.ThreeState;

        // ReInstall Style
        private Rectangle mRectReinstall = Rectangle.Empty;

        // Cancel Style
        private Rectangle mRectCancel = Rectangle.Empty;

        // DownloadProgress Style
        private Rectangle mRectSpeed = Rectangle.Empty;
        private string mSpeed = string.Empty;
        private string mTime = string.Empty;
        private string mToolTipText = string.Empty;
        private bool mblnCellMouseNone = false;                // 标记当前鼠标是不是在单元格上面。


        // text Style
        private string[] mLinesText = new string[] { "" };
        private string mSourceText = "";

        private bool mShowButtonFolder = false;  // text下是否绘制文件夹按钮


        public tbDataGridViewProgressCellEx()
        {
            this.ValueType = typeof(int);
        }


        public int ValueStep
        {
            get
            {
                return this._ValueStep;
            }
            set
            {
                this._ValueStep = value;
            }
        }

        public override object DefaultNewRowValue
        {
            get
            {
                return "tbDataGridViewProgressCellEx";
            }
        }

        public override Type ValueType
        {
            get
            {
                return typeof(System.String);
            }
            set
            {
            }
        }

        public ProgressStyle tbProgressStyle
        {
            get
            {
                return this.mProgressStyle;
            }
            set
            {
                if (this.mProgressStyle != value)
                {
                    if (value == ProgressStyle.UnsureProgress)
                    {
                        if (this.mTmrWaiting == null)
                        {
                            this.mTmrWaiting = new System.Timers.Timer();
                            {
                                var withBlock = this.mTmrWaiting;
                                withBlock.Interval = 100;
                                mTmrWaiting.Elapsed -= TimerWaiting_Tick;
                                mTmrWaiting.Elapsed += TimerWaiting_Tick;
                            }
                        }

                        // Noted by Utmost20140718
                        // Value为Object类型，下面操作需要Boxing 或 UnBoxing
                        // Me.Value = 1
                        // Me.mImgProgressWaiting = (GuiResource.GetWaitingImageFromValue(Me.Value))

                        this._ValueStep = 1;
                        int VStep = this.ValueStep;
                        this.mImgProgressWaiting = (GuiResource.GetWaitingImageFromValue(ref VStep));
                        this.ValueStep = VStep;
                        this.mTmrWaiting.Start();
                    }

                    this.mProgressStyle = value;
                    this.Invalidate();
                }
            }
        }

        public Padding tbPadding
        {
            get
            {
                return this.m_Padding;
            }
            set
            {
                this.m_Padding = value;
                this.Invalidate();
            }
        }

        public CellButtonStyle tbCellButtonEnable
        {
            get
            {
                return (CellButtonStyle)Enum.ToObject(typeof(CellButtonStyle), this.mCellButtonEnable);
            }
            set
            {
                this.mCellButtonEnable = (int)value;
                this.Invalidate();
            }
        }

        public string tbText
        {
            get
            {
                return this.mText;
            }
            set
            {
                if (this.mText != value)
                {
                    this.mText = value;
                    if (string.IsNullOrEmpty(value))
                        this.tbToolTipText = "";
                    this.Invalidate();
                }
            }
        }

        public Color tbForColor
        {
            get
            {
                return this.mForColor;
            }
            set
            {
                this.mForColor = value;
            }
        }

        public string tbTextBackup
        {
            get
            {
                return this.mTextBackup;
            }
            set
            {
                this.mTextBackup = value;

                if (this.DataGridView != null && !string.IsNullOrEmpty(this.mTextBackup))
                {
                    if (this.FormatButtonSize(this.mTextBackup, this.DataGridView.Font))
                        this.Invalidate();
                }
            }
        }

        private bool FormatButtonSize(string strText, Font f)
        {
            bool isChange = false;
            Size txtSize = TextRenderer.MeasureText(strText, f);
            if (txtSize.Width > this.mButtonSizeCommon.Width)
            {
                this.mButtonSizeBackup = new Size(txtSize.Width + 1, this.mButtonSizeBackup.Height);
                isChange = true;
            }

            if (this.mButtonSizeBackup.Width > this.mButtonSizeMax.Width)
            {
                this.mButtonSizeBackup = new Size(this.mButtonSizeMax.Width, this.mButtonSizeBackup.Height);
                isChange = true;
            }

            return isChange;
        }

        public string tbTextImport
        {
            get
            {
                return this.mTextImport;
            }
            set
            {
                this.mTextImport = value;

                if (this.DataGridView != null && !string.IsNullOrEmpty(this.mTextImport))
                {
                    if (this.FormatButtonSize(this.mTextImport, this.DataGridView.Font))
                        this.Invalidate();
                }
            }
        }

        public string tbTextUnInstall
        {
            get
            {
                return this.mTextUnInstall;
            }
            set
            {
                this.mTextUnInstall = value;

                if (this.DataGridView != null && !string.IsNullOrEmpty(this.mTextUnInstall))
                {
                    Size txtSize = TextRenderer.MeasureText(this.mTextUnInstall, this.DataGridView.Font);
                    if (txtSize.Width > this.mButtonSizeCommon.Width)
                    {
                        this.mButtonSizeUninstall = new Size(txtSize.Width + 1, this.mButtonSizeUninstall.Height);
                        this.Invalidate();
                    }
                }
            }
        }

        public string tbTextReInstall
        {
            get
            {
                return this.mTextReInstall;
            }
            set
            {
                this.mTextReInstall = value;

                if (this.DataGridView != null && !string.IsNullOrEmpty(this.mTextReInstall))
                {
                    if (this.FormatButtonSize(this.mTextReInstall, this.DataGridView.Font))
                        this.Invalidate();
                }
            }
        }

        public string tbTextFail
        {
            get
            {
                return this.mTextFail;
            }
            set
            {
                this.mTextFail = value;
            }
        }

        public string tbTextMore
        {
            get
            {
                return this.mTextMore;
            }
            set
            {
                this.mTextMore = value;
            }
        }

        public bool tbShowIgnoreButton
        {
            get
            {
                return this.mShowIgnoreButton;
            }
            set
            {
                this.mShowIgnoreButton = value;
            }
        }

        public Font tbFailFont
        {
            get
            {
                return this.mFailedTextFont;
            }
            set
            {
                this.mFailedTextFont = value;
                this.Invalidate();
            }
        }

        public int tbProgressValue
        {
            get
            {
                return this.mProgressValue;
            }
            set
            {
                this.mProgressValue = value;
                this.Invalidate();
            }
        }

        public Image tbProgressBackgroundImage
        {
            get
            {
                if (this.mImgProgressBackground == null)
                    this.mImgProgressBackground = ((tbDataGridViewProgressColumn)this.OwningColumn).tbProgressBackgroundImage;

                return this.mImgProgressBackground;
            }
            set
            {
                this.mImgProgressBackground = value;
                this.Invalidate();
            }
        }

        public Image tbProgressValueImage
        {
            get
            {
                if (this.mImgProgressValue == null)
                    this.mImgProgressValue = ((tbDataGridViewProgressColumn)this.OwningColumn).tbProgressValueImage;

                return this.mImgProgressValue;
            }
            set
            {
                this.mImgProgressValue = value;
                this.Invalidate();
            }
        }

        public Image tbProgressWaitingImage
        {
            get
            {
                if (this.mImgProgressWaiting == null)
                    this.mImgProgressWaiting = ((tbDataGridViewProgressColumn)this.OwningColumn).tbProgressWaitingImage;

                return this.mImgProgressWaiting;
            }
            set
            {
                this.mImgProgressWaiting = value;
                this.Invalidate();
            }
        }

        public bool tbShowCancelButton
        {
            get
            {
                return this.mShowCancelButton;
            }
            set
            {
                this.mShowCancelButton = value;
            }
        }

        public string tbSpeed
        {
            get
            {
                return this.mSpeed;
            }
            set
            {
                this.mSpeed = value;
            }
        }

        public string tbTime
        {
            get
            {
                return this.mTime;
            }
            set
            {
                this.mTime = value;
            }
        }

        public Size tbButtonSize
        {
            get
            {
                return this.mButtonSizeBackup;
            }
            set
            {
                this.mButtonSizeBackup = value;
                this.mButtonSizeUninstall = value;
            }
        }

        public bool tbShowButtonText
        {
            get
            {
                return this.mShowButtonText;
            }
            set
            {
                this.mShowButtonText = value;
            }
        }

        public string tbToolTipText
        {
            get
            {
                return this.mToolTipText;
            }
            set
            {
                this.mToolTipText = value;
            }
        }

        public Image tbOneButtonIcon
        {
            get
            {
                return this.mImgOneButton;
            }
            set
            {
                this.mImgOneButton = value;
            }
        }


        // 2015-07-30 by chenbihai 进度条 错误信息 添加是否可以点击
        public bool tbProgressMsgClick
        {
            get
            {
                return this.mProgressMsgClick;
            }
            set
            {
                this.mProgressMsgClick = value;
            }
        }


        public Image tbButtonImgFirst
        {
            get
            {
                return this.mImgBackup;
            }
            set
            {
                this.mImgBackup = value;
                this.Invalidate();
            }
        }

        public Image tbButtonImgSecond
        {
            get
            {
                return this.mImgUninstall;
            }
            set
            {
                this.mImgUninstall = value;
                this.Invalidate();
            }
        }

        public Image tbButtonImgThird
        {
            get
            {
                return this.mImgDocument;
            }
            set
            {
                this.mImgDocument = value;
                this.Invalidate();
            }
        }

        public ImageState tbButtonImgThirdImageState
        {
            get
            {
                return this.mImgThirdImageState;
            }
            set
            {
                this.mImgThirdImageState = value;
                this.Invalidate();
            }
        }


        public Image tbButtonImgImport
        {
            get
            {
                return this.mImgImport;
            }
            set
            {
                this.mImgImport = value;
                this.Invalidate();
            }
        }

        public bool tbShowButtonFirst
        {
            get
            {
                return this.mShowButtonFirst;
            }
            set
            {
                this.mShowButtonFirst = value;
            }
        }

        public bool tbShowButtonImport
        {
            get
            {
                return this.mShowButtonImport;
            }
            set
            {
                this.mShowButtonImport = value;
            }
        }

        public bool tbShowButtonSecond
        {
            get
            {
                return this.mShowButtonSecond;
            }
            set
            {
                this.mShowButtonSecond = value;
            }
        }

        public bool tbShowButtonThird
        {
            get
            {
                return this.mShowButtonThird;
            }
            set
            {
                this.mShowButtonThird = value;
            }
        }

        public Image tbButtonIconFirst
        {
            get
            {
                return this.mIconFirst;
            }
            set
            {
                this.mIconFirst = value;
                this.Invalidate();
            }
        }

        public Image tbButtonIconImport
        {
            get
            {
                return this.mIconImport;
            }
            set
            {
                this.mIconImport = value;
                this.Invalidate();
            }
        }



        public Image tbButtonIconSecond
        {
            get
            {
                return this.mIconSecond;
            }
            set
            {
                this.mIconSecond = value;
                this.Invalidate();
            }
        }

        public bool tbShowButtonFolder
        {
            get
            {
                return this.mShowButtonFolder;
            }
            set
            {
                this.mShowButtonFolder = value;
            }
        }




        protected override bool SetValue(int rowIndex, object value)
        {
            if (this.DataGridView != null && value != null && !string.IsNullOrEmpty(value.ToString()))
            {
                // Dim txtSize As Size = TextRenderer.MeasureText(value, Me.DataGridView.Font)
                // If txtSize.Width > Me.mButtonSizeCommon.Width Then
                // Me.mButtonSizeBackup = New Size(txtSize.Width + 1, Me.mButtonSizeBackup.Height)
                // End If

                if (this.FormatButtonSize(value.ToString(), this.DataGridView.Font))
                    this.Invalidate();
            }

            return base.SetValue(rowIndex, value);
        }

        protected override void Paint(System.Drawing.Graphics g, System.Drawing.Rectangle clipBounds, System.Drawing.Rectangle cellBounds, int rowIndex, System.Windows.Forms.DataGridViewElementStates elementState, object value, object formattedValue, string errorText, System.Windows.Forms.DataGridViewCellStyle cellStyle, System.Windows.Forms.DataGridViewAdvancedBorderStyle advancedBorderStyle, System.Windows.Forms.DataGridViewPaintParts paintParts)
        {
            base.Paint(g, clipBounds, cellBounds, rowIndex, elementState, value, formattedValue, errorText, cellStyle, advancedBorderStyle, paintParts);

            if (this.mFont == null)
                this.mFont = Common.CreateFont("Arial", cellStyle.Font.Size, cellStyle.Font.Style);

            if (this.mFailedTextFont == null)
            {
                float sngSize = cellStyle.Font.Size;
                if (this.mFailedTextFont == null)
                    this.mFailedTextFont = Common.CreateFont("Arial", sngSize, FontStyle.Underline);
            }

            Rectangle rect = new Rectangle(cellBounds.Left + this.m_Padding.Left, cellBounds.Top + this.m_Padding.Top, cellBounds.Width - this.m_Padding.Horizontal, cellBounds.Height - this.m_Padding.Vertical);

            switch (this.mProgressStyle)
            {
                case ProgressStyle.Button:
                    {
                        this.PaintButtonStyle(g, rect, cellStyle);
                        break;
                    }

                case ProgressStyle.Text:
                    {
                        this.PaintTextStyle(g, rect, cellStyle);
                        break;
                    }

                case ProgressStyle.Progress:
                    {
                        this.PaintProgressStyle(g, rect, cellStyle);
                        break;
                    }

                case ProgressStyle.DownloadProgress:
                    {
                        this.PaintDownloadProgressStyle(g, rect, cellStyle);
                        break;
                    }

                case ProgressStyle.UnsureProgress:
                    {
                        this.PaintUnsureProgressStyle(g, rect, cellStyle);
                        break;
                    }

                case ProgressStyle.Failed:
                case ProgressStyle.OneRetryButton:
                    {
                        this.PaintFailedStyle(g, rect, cellStyle);
                        break;
                    }

                case ProgressStyle.OneButton:
                    {
                        this.PaintOneButton(g, rect, cellStyle);
                        break;
                    }

                case ProgressStyle.BackupFolder:
                    {
                        this.PaintBackupFolder(g, rect, cellStyle);
                        break;
                    }

                case ProgressStyle.Reinstall:
                    {
                        this.PaintReinstall(g, rect, cellStyle);
                        break;
                    }
            }
        }

        private void PaintButtonStyle(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle)
        {
            // rect矩形是以dategreidview的左上角为坐标原点的。
            int iLeft = rect.Left;
            int iTop = rect.Top + (rect.Height - this.mButtonSizeBackup.Height) / 2;
            int iDiff = 3;

            this.mRectBackup = new Rectangle(iLeft, iTop, this.mButtonSizeBackup.Width, this.mButtonSizeBackup.Height);
            if (this.mShowButtonSecond)
                this.mRectUninstall = new Rectangle(this.mRectBackup.Right + this.mButtonDiff, iTop, this.mButtonSizeUninstall.Width, this.mButtonSizeUninstall.Height);

            if (this.mShowButtonImport)
                this.mRectImport = new Rectangle(this.mRectUninstall.Right + this.mButtonDiff, iTop, this.mButtonSizeUninstall.Width, this.mButtonSizeUninstall.Height);

            int intCount = 4;
            if (mImgThirdImageState == ImageState.ThreeState)
                intCount = 3;

            if (this.mShowButtonThird)
                this.mRectDocument = new Rectangle(rect.Left + rect.Width - mImgDocument.Width / intCount - 20, rect.Top + (rect.Height - this.mImgDocument.Height) / 2, this.mImgDocument.Width / intCount, this.mImgDocument.Height);



            // 按钮状态
            MouseStates msBackup = MouseStates.MouseLeave;
            MouseStates msUninstall = MouseStates.MouseLeave;
            MouseStates msDocument = MouseStates.MouseLeave;
            MouseStates msImport = MouseStates.MouseLeave;

            if (this.mCellButtonDown != CellButtonStyle.None)
            {
                switch (this.mCellButtonDown)
                {
                    case CellButtonStyle.Backup:
                        {
                            msBackup = MouseStates.MouseDown;
                            break;
                        }

                    case CellButtonStyle.Uninstall:
                        {
                            msUninstall = MouseStates.MouseDown;
                            break;
                        }

                    case CellButtonStyle.Document:
                        {
                            msDocument = MouseStates.MouseDown;
                            break;
                        }

                    case CellButtonStyle.Import:
                        {
                            msImport = MouseStates.MouseDown;
                            break;
                        }
                }
            }
            else if (this.mCellButtonHover != CellButtonStyle.None)
            {
                switch (this.mCellButtonHover)
                {
                    case CellButtonStyle.Backup:
                        {
                            msBackup = MouseStates.MouseHover;
                            break;
                        }

                    case CellButtonStyle.Uninstall:
                        {
                            msUninstall = MouseStates.MouseHover;
                            break;
                        }

                    case CellButtonStyle.Document:
                        {
                            msDocument = MouseStates.MouseHover;
                            break;
                        }

                    case CellButtonStyle.Import:
                        {
                            msImport = MouseStates.MouseHover;
                            break;
                        }
                }
            }

            // 第一个按钮(绘制背景)
            if (this.mShowButtonFirst)
                GuiHelper.DrawImage(g, this.mRectBackup, this.m_Split, this.mImgBackup, Convert.ToBoolean((this.mCellButtonEnable & (int)CellButtonStyle.Backup)) & this.Enabled, msBackup, ImageState.FourState);

            // 第二个按钮(绘制背景)
            if (this.mShowButtonSecond)
                // 绘制背景
                GuiHelper.DrawImage(g, this.mRectUninstall, this.m_Split, this.mImgUninstall, Convert.ToBoolean(this.mCellButtonEnable & (int)CellButtonStyle.Uninstall) & this.Enabled, msUninstall, ImageState.FourState);

            // 第三个按钮
            if (this.mShowButtonThird)
                GuiHelper.DrawImage(g, this.mRectDocument, this.m_Split, this.mImgDocument, Convert.ToBoolean(this.mCellButtonEnable & (int)CellButtonStyle.Document) & this.Enabled, msDocument, mImgThirdImageState);

            // 导入按钮
            if (this.mShowButtonImport)
                GuiHelper.DrawImage(g, this.mRectImport, this.m_Split, this.mImgImport, Convert.ToBoolean(this.mCellButtonEnable & (int)CellButtonStyle.Import) & this.Enabled, msImport, ImageState.FourState);

            // Icon和文字
            if (this.mShowButtonText)
            {
                // Me.PaintTextStyle(g, Me.mRectBackup, cellStyle, False, Me.mTextBackup, ContentAlignment.MiddleCenter, True)
                // Me.PaintTextStyle(g, Me.mRectUninstall, cellStyle, False, Me.mTextUnInstall, ContentAlignment.MiddleCenter, True)
                // 第一个按钮
                if (this.mShowButtonFirst)
                {
                    // 绘制Icon
                    Rectangle rectText = this.mRectBackup;
                    Rectangle rectIcon = Rectangle.Empty;
                    int X = 0;

                    if (this.mIconFirst != null)
                    {
                        Size size = TextRenderer.MeasureText(this.mTextBackup, this.mFont);
                        X = (this.mRectBackup.Width - size.Width - this.mIconFirst.Width - iDiff) / 2;
                        rectIcon = new Rectangle(this.mRectBackup.X + X, this.mRectBackup.Y + (this.mRectBackup.Height - this.mIconFirst.Height) / 2, this.mIconFirst.Width, this.mIconFirst.Height);
                        GuiHelper.DrawImage(g, rectIcon, new tbSplitStructure(0, 0, 0, 0), this.mIconFirst, true, MouseStates.MouseLeave, ImageState.OneState);
                    }

                    // 绘制文字
                    Color colorTextBackup = Color.Gray;
                    if (Convert.ToBoolean(this.mCellButtonEnable & (int)CellButtonStyle.Backup) & this.Enabled)
                        colorTextBackup = Color.Black;

                    ContentAlignment alignText = ContentAlignment.MiddleCenter;
                    if (this.mIconFirst != null)
                    {
                        rectText = new Rectangle(this.mRectBackup.X + rectIcon.Width + iDiff + X, this.mRectBackup.Y, this.mRectBackup.Width - rectIcon.Width - iDiff - X, this.mRectBackup.Height);
                        alignText = ContentAlignment.MiddleLeft;
                    }

                    GuiHelper.DrawText(g, this.mTextBackup, this.mFont, rectText, colorTextBackup, Color.Transparent, alignText);
                }

                // 绘制第二个按钮的文字
                if (this.mShowButtonSecond)
                {
                    Rectangle rectText = this.mRectUninstall;
                    Rectangle rectIcon = Rectangle.Empty;
                    int X = 0;

                    // 绘制Icon
                    if (this.mIconSecond != null)
                    {
                        Size size = TextRenderer.MeasureText(this.mTextUnInstall, this.mFont);
                        X = (this.mRectUninstall.Width - size.Width - this.mIconSecond.Width - iDiff) / 2;

                        rectIcon = new Rectangle(this.mRectUninstall.X + X, this.mRectUninstall.Y + (this.mRectUninstall.Height - this.mIconSecond.Height) / 2, this.mIconSecond.Width, this.mIconSecond.Height);
                        GuiHelper.DrawImage(g, rectIcon, new tbSplitStructure(0, 0, 0, 0), this.mIconSecond, true, MouseStates.MouseLeave, ImageState.OneState);
                    }

                    Color colorTextUninstall = Color.Gray;
                    if (Convert.ToBoolean(this.mCellButtonEnable & (int)CellButtonStyle.Uninstall) & this.Enabled)
                        colorTextUninstall = Color.Black;

                    // 绘制文字
                    ContentAlignment alignText = ContentAlignment.MiddleCenter;
                    if (this.mIconSecond != null)
                    {
                        rectText = new Rectangle(this.mRectUninstall.X + rectIcon.Width + iDiff + X, this.mRectUninstall.Y, this.mRectUninstall.Width - rectIcon.Width - iDiff - X, this.mRectUninstall.Height);
                        alignText = ContentAlignment.MiddleLeft;
                    }

                    GuiHelper.DrawText(g, this.mTextUnInstall, this.mFont, rectText, colorTextUninstall, Color.Transparent, alignText);
                }

                // 导入按钮
                if (this.mShowButtonImport)
                {
                    // 绘制Icon
                    Rectangle rectText = this.mRectImport;
                    Rectangle rectIcon = Rectangle.Empty;
                    int X = 0;

                    if (this.tbButtonIconImport != null)
                    {
                        Size size = TextRenderer.MeasureText(this.mTextImport, this.mFont);
                        X = (this.mRectImport.Width - size.Width - this.tbButtonIconImport.Width - iDiff) / 2;
                        rectIcon = new Rectangle(this.mRectImport.X + X, this.mRectImport.Y + (this.mRectImport.Height - this.tbButtonIconImport.Height) / 2, this.tbButtonIconImport.Width, this.tbButtonIconImport.Height);
                        GuiHelper.DrawImage(g, rectIcon, new tbSplitStructure(0, 0, 0, 0), this.tbButtonIconImport, true, MouseStates.MouseLeave, ImageState.OneState);
                    }

                    // 绘制文字
                    Color colorTextBackup = Color.Gray;
                    if (Convert.ToBoolean(this.mCellButtonEnable & (int)CellButtonStyle.Import) & this.Enabled)
                        colorTextBackup = Color.Black;

                    ContentAlignment alignText = ContentAlignment.MiddleCenter;
                    if (this.tbButtonIconImport != null)
                    {
                        rectText = new Rectangle(this.mRectImport.X + rectIcon.Width + iDiff + X, this.mRectImport.Y, this.mRectImport.Width - rectIcon.Width - iDiff - X, this.mRectImport.Height);
                        alignText = ContentAlignment.MiddleLeft;
                    }

                    GuiHelper.DrawText(g, this.mTextImport, this.mFont, rectText, colorTextBackup, Color.Transparent, alignText);
                }
            }
        }

        private void ZJPaintTextStyle(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle, bool isProgressStyle = false, string textDraw = "", ContentAlignment align = ContentAlignment.MiddleLeft, bool isButton = false)
        {
            if (textDraw.Length == 0)
                textDraw = this.mText;

            if (string.Compare(this.mSourceText, textDraw) != 0)
            {
                this.mSourceText = textDraw;
                this.mLinesText = this.mSourceText.Split(new string[] { "\n\r" }, StringSplitOptions.None);
            }

            int rectX = 0;
            int rectY = 0;
            int rectWidth = 0;
            int rectHeight = 0;

            rectX = rect.Left;
            rectY = rect.Y;
            rectWidth = rect.Width;
            rectHeight = rect.Height;

            Rectangle[] rectLines = new Rectangle[mLinesText.Length - 1 + 1];
            Size[] sizeLines = new Size[mLinesText.Length - 1 + 1];

            bool blnEndClips = false;
            int allTxtHeight = 0;

            sizeLines[0] = TextRenderer.MeasureText(mLinesText[0], this.mFont);
            allTxtHeight = sizeLines[0].Height;

            for (int index = 1; index <= mLinesText.Length - 1; index++)
            {
                sizeLines[index] = TextRenderer.MeasureText(mLinesText[index], this.mFont);
                allTxtHeight += sizeLines[index].Height;
            }

            int diffY = 6;
            int intY = rectY + (rectHeight - allTxtHeight - diffY * (sizeLines.Length - 1)) / 2;
            for (int index = 0; index <= sizeLines.Length - 1; index++)
            {
                if (rectWidth < sizeLines[index].Width)
                {
                    this.ToolTipText = textDraw;
                    blnEndClips = true;
                }

                rectLines[index] = new Rectangle(rectX, intY, rectWidth, sizeLines[index].Height);
                intY += sizeLines[index].Height + diffY;
            }

            if (this.ToolTipText.Length > 0 && !blnEndClips)
                this.ToolTipText = "";

            Color foreColor = cellStyle.ForeColor;
            if (this.Selected)
                foreColor = cellStyle.SelectionForeColor;
            if (!isButton)
            {
                if (this.mForColor != Color.Black)
                    foreColor = this.mForColor;
            }
            foreColor = isButton == true ? Color.Black : foreColor;

            TextFormatFlags textFlags = GuiHelper.CreateTextFormatFlags(align);

            // 绘制首行文字
            TextRenderer.DrawText(g, mLinesText[0], cellStyle.Font, rectLines[0], foreColor, Color.Transparent, textFlags);
            textFlags = GuiHelper.CreateTextFormatFlags(align, false, true, false);

            // 绘制其它行文字
            if (mLinesText.Length > 1)
            {
                for (int index = 1; index <= mLinesText.Length - 1; index++)
                    TextRenderer.DrawText(g, mLinesText[index], this.mFont, rectLines[index], foreColor, Color.Transparent, textFlags);
            }
            // ============================================================================================

            if (!isProgressStyle)
                // 取消按钮
                this.PaintCancelButton(g, rect);
        }

        private void PaintTextStyle(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle, bool isProgressStyle = false, string textDraw = "", ContentAlignment align = ContentAlignment.MiddleLeft, bool isButton = false)
        {

            /* TODO ERROR: Skipped IfDirectiveTrivia */
            /* TODO ERROR: Skipped DisabledTextTrivia */
            /* TODO ERROR: Skipped ElseDirectiveTrivia */
            if (textDraw.Length == 0)
                textDraw = this.mText;

            Size textSize = TextRenderer.MeasureText(textDraw, cellStyle.Font);
            Color tmpColor = Color.Black;

            if (this.Selected)
                tmpColor = cellStyle.SelectionForeColor;
            if (rect.Width < textSize.Width)
                this.mToolTipText = textDraw;

            GuiHelper.DrawText(g, textDraw, this.mFont, rect, isButton == true ? Color.Black : tmpColor, Color.Transparent, align, true, true, true, true);

            // 打开文件按钮
            if (this.mShowButtonFolder)
            {
                MouseStates msDocument = MouseStates.MouseLeave;
                if (this.mCellButtonDown != CellButtonStyle.None)
                {
                    switch (this.mCellButtonDown)
                    {
                        case CellButtonStyle.Document:
                            {
                                msDocument = MouseStates.MouseDown;
                                break;
                            }
                    }
                }
                else if (this.mCellButtonHover != CellButtonStyle.None)
                {
                    switch (this.mCellButtonHover)
                    {
                        case CellButtonStyle.Document:
                            {
                                msDocument = MouseStates.MouseHover;
                                break;
                            }
                    }
                }

                int intCount = 4;
                if (mImgThirdImageState == ImageState.ThreeState)
                    intCount = 3;
                this.mRectDocument = new Rectangle(rect.Left + rect.Width - mImgDocument.Width / intCount - 20, rect.Top + (rect.Height - this.mImgDocument.Height) / 2, this.mImgDocument.Width / intCount, this.mImgDocument.Height);

                GuiHelper.DrawImage(g, this.mRectDocument, this.m_Split, this.mImgDocument, Convert.ToBoolean(this.mCellButtonEnable & (int)CellButtonStyle.Document) & this.Enabled, msDocument, mImgThirdImageState);
            }

            if (!isProgressStyle)
                // 取消按钮
                this.PaintCancelButton(g, rect);
        }

        private void PaintProgressStyle(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle)
        {
            if (this.mProgressValue == -1)
                return;
            float percentage = (System.Convert.ToSingle(mProgressValue) / 100.0F);

            // 进度条背景色
            Rectangle rectBackground = new Rectangle(rect.X, rect.Y + (rect.Height - this.mImgProgressBackground.Height) / 2, this.mImgProgressBackground.Width, this.mImgProgressBackground.Height + 1);
            if (this.mImgProgressBackground.Width > rect.Width * 2 / 3)
                rectBackground.Width = rect.Width * 2 / 3;
            if (this.mImgProgressBackground.Height > rect.Height / 2)
                rectBackground.Height = rect.Height / 2;

            GuiHelper.DrawImage(g, rectBackground, this.m_Split, this.mImgProgressBackground);

            // 进度条前景色
            Rectangle rectValue = rectBackground;
            rectValue.Width = (int)(rectValue.Width * percentage);
            GuiHelper.DrawImage(g, rectValue, this.m_Split, this.mImgProgressValue);

            // 进度条的值
            // Dim rectProgress As New Rectangle(rectBackground.Location, New Size(rectBackground.Width, rectBackground.Height + 1))
            string strProgressValue = string.Format("{0}%", tbProgressValue);

            if (mFontProgress == null)
                mFontProgress = Common.CreateFont("Arial", 7, FontStyle.Regular);
            Color progressForeColor = tbProgressValue >= 60 ? Color.White : Color.Black;

            GuiHelper.DrawText(g, strProgressValue, mFontProgress, rectBackground, progressForeColor, Color.Transparent, ContentAlignment.MiddleCenter);

            // 底部的文字
            Rectangle rectText = new Rectangle(rect.X, rect.Y + rect.Height / 2, rect.Width, rect.Height / 2);
            this.PaintTextStyle(g, rectText, cellStyle, true);

            // 取消按钮
            this.PaintCancelButton(g, rect);
        }

        private void PaintDownloadProgressStyle(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle)
        {
            if (this.mProgressValue == -1)
                return;
            float percentage = (System.Convert.ToSingle(mProgressValue) / 100.0F);

            // 进度条背景色
            Rectangle rectBackground = new Rectangle(rect.X, rect.Y + (rect.Height - this.mButtonDiff) / 3, rect.Width - this.mButtonDiff * 2, this.mImgProgressBackground.Height + 1);

            if (this.mImgProgressBackground.Height > rect.Height / 2)
                rectBackground.Height = rect.Height / 2;

            GuiHelper.DrawImage(g, rectBackground, this.m_Split, this.mImgProgressBackground);

            // 进度条前景色
            if (percentage > 0)
            {
                Rectangle rectValue = rectBackground;
                rectValue.Width = (int)(rectValue.Width * percentage);
                GuiHelper.DrawImage(g, rectValue, this.m_Split, this.mImgProgressValue);
            }

            // 进度条的值
            // Dim rectProgress As New Rectangle(rect.X, rect.Y + (rect.Height - Me.mButtonDiff) / 3 + 1, rect.Width - Me.mButtonDiff * 2, Me.mImgProgressBackground.Height)
            string text = string.Format("{0}%", tbProgressValue.ToString());
            if (mFontProgress == null)
                mFontProgress = Common.CreateFont("Arial", 7, FontStyle.Regular);
            Color progressForeColor = tbProgressValue >= 60 ? Color.White : Color.Black;

            GuiHelper.DrawText(g, text, mFontProgress, rectBackground, progressForeColor, Color.Transparent, ContentAlignment.MiddleCenter);

            Rectangle rectSpeed = new Rectangle(rect.X, rect.Y + (rect.Height + this.mButtonDiff) / 3, rectBackground.Width / 5 * 3, rect.Height / 2);
            Rectangle rectTime = new Rectangle(rectSpeed.Right, rect.Y + (rect.Height + this.mButtonDiff) / 3, rectBackground.Width / 5 * 2, rect.Height / 2);

            if (string.IsNullOrEmpty(this.mTime))
                rectSpeed = new Rectangle(rect.X, rect.Y + (rect.Height + this.mButtonDiff) / 3, rectBackground.Width, rect.Height / 2);


            this.mRectSpeed = rectSpeed;
            // 速度
            this.PaintTextStyle(g, rectSpeed, cellStyle, true, this.mSpeed);

            // 剩余时间
            if (!string.IsNullOrEmpty(this.mTime))
                this.PaintTextStyle(g, rectTime, cellStyle, true, this.mTime, ContentAlignment.MiddleRight);
        }

        private void PaintFailedStyle(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle)
        {
            // 绘Icon
            // 2015-07-14 企业签名包 签名过期  重新获取URL
            Image imgIconFail = global::iVoiceMerge.Properties.Resources.app_cell_fail;

            Rectangle rectIconFail = new Rectangle(rect.X, rect.Y + (rect.Height - imgIconFail.Height) / 2, imgIconFail.Width, imgIconFail.Height);
            g.DrawImage(imgIconFail, rectIconFail, new Rectangle(0, 0, imgIconFail.Width, imgIconFail.Height), GraphicsUnit.Pixel);


            // 绘提示内容文字
            Size textSize = TextRenderer.MeasureText(this.mText, this.mFailedTextFont);
            Color tmpColor = Color.FromArgb(22, 138, 210);

            if (rect.Width < textSize.Width)
                this.mToolTipText = this.mText;

            // 重试
            this.mRectFailed = new Rectangle(rectIconFail.Right + this.mButtonDiff / 2, rect.Top + (rect.Height - textSize.Height) / 2, textSize.Width, textSize.Height);

            GuiHelper.DrawText(g, this.mText, this.mFailedTextFont, this.mRectFailed, tmpColor, Color.Transparent, ContentAlignment.MiddleLeft);

            // 绘Fail文字
            Size textFailSize = TextRenderer.MeasureText(this.mTextFail, this.mFont);
            Rectangle rectTextFail = new Rectangle(this.mRectFailed.Right + this.mButtonDiff / 2, rect.Top + (rect.Height - textFailSize.Height) / 2, textFailSize.Width, textFailSize.Height);

            this.PaintTextStyle(g, rectTextFail, cellStyle, false, this.mTextFail, ContentAlignment.MiddleLeft, false);

            // 当升级失败文字超过区域就用tooltip提示。
            if (rectTextFail.Right + rectTextFail.Width > rect.Right)
                this.ToolTipText = this.mTextFail;
        }

        private void PaintUnsureProgressStyle(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle)
        {
            // 绘进度图片
            Rectangle rectBackground = new Rectangle(rect.X, rect.Y + (rect.Height - this.mImgProgressBackground.Height) / 2, this.mImgProgressBackground.Width, this.mImgProgressBackground.Height);
            GuiHelper.DrawImage(g, rectBackground, this.m_Split, this.mImgProgressWaiting);

            // 绘底部的文字
            Rectangle rectText = new Rectangle(rect.X, rect.Y + rect.Height / 2, rect.Width, rect.Height / 2);
            this.PaintTextStyle(g, rectText, cellStyle, true);
        }

        private void PaintCancelButton(System.Drawing.Graphics g, System.Drawing.Rectangle rect)
        {
            if (this.mShowCancelButton)
            {
                MouseStates msCancel = MouseStates.MouseLeave;

                if (this.mCellButtonDown == CellButtonStyle.Cancel)
                    msCancel = MouseStates.MouseDown;
                else if (this.mCellButtonHover == CellButtonStyle.Cancel)
                    msCancel = MouseStates.MouseHover;

                this.mRectCancel = new Rectangle(rect.X + this.mImgProgressBackground.Width + this.mButtonDiff, rect.Top + (rect.Height - this.mImgCancel.Height) / 2 - 1, this.mImgCancel.Width / (int)this.mImgCancelState, this.mImgCancel.Height);

                GuiHelper.DrawImage(g, this.mRectCancel, this.m_Split, this.mImgCancel, this.Enabled, msCancel, this.mImgCancelState);
            }
        }

        private void PaintOneButton(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle)
        {
            // rect矩形是以dategreidview的左上角为坐标原点的。
            int iLeft = rect.Left;
            int iTop = rect.Top + (rect.Height - this.mButtonSizeBackup.Height) / 2;

            // 按钮状态
            MouseStates msOneButton = MouseStates.MouseLeave;
            MouseStates msIgnore = MouseStates.MouseLeave;

            if (this.mCellButtonDown != CellButtonStyle.None)
            {
                switch (this.mCellButtonDown)
                {
                    case CellButtonStyle.OneButton:
                        {
                            msOneButton = MouseStates.MouseDown;
                            break;
                        }

                    case CellButtonStyle.Ignore:
                        {
                            msIgnore = MouseStates.MouseDown;
                            break;
                        }
                }
            }
            else if (this.mCellButtonHover != CellButtonStyle.None)
            {
                switch (this.mCellButtonHover)
                {
                    case CellButtonStyle.OneButton:
                        {
                            msOneButton = MouseStates.MouseHover;
                            break;
                        }

                    case CellButtonStyle.Ignore:
                        {
                            msIgnore = MouseStates.MouseHover;
                            break;
                        }
                }
            }


            Size textSize = TextRenderer.MeasureText(this.mText, cellStyle.Font);
            Color tmpColor = Color.Black;
            if (this.mButtonSizeBackup.Width < textSize.Width)
            {
                if (textSize.Width < rect.Width - 5)
                    this.mButtonSizeBackup = new Size(textSize.Width, mButtonSizeBackup.Height);
                else
                    this.mToolTipText = this.mText;
            }

            this.mRectOneButton = new Rectangle(iLeft, iTop, this.mButtonSizeBackup.Width, this.mButtonSizeBackup.Height);
            GuiHelper.DrawImage(g, this.mRectOneButton, this.mOneButtonSplit, this.mImgOneButton, this.Enabled, msOneButton, ImageState.FourState);


            // 绘文字
            this.PaintTextStyle(g, this.mRectOneButton, cellStyle, false, this.mText, ContentAlignment.MiddleCenter, true);
            // TextRenderer.DrawText(g, Me.mText, Me.mFont, Me.mRectOneButton, tmpColor, Color.Transparent, textFlags)

            if (this.mTextMore.Length > 0)
            {
                Size textMoreSize = TextRenderer.MeasureText(this.mTextMore, cellStyle.Font);
                int restWidth = rect.Right - this.mRectOneButton.Right - this.mButtonDiff / 2;

                Rectangle rectTextMore = new Rectangle(this.mRectOneButton.Right + this.mButtonDiff / 2, rect.Top + (rect.Height - textMoreSize.Height) / 2, restWidth, textMoreSize.Height);
                // g.DrawRectangle(Pens.Red, rectTextMore)
                if (textMoreSize.Width > restWidth)
                    this.mToolTipText = this.mTextMore;

                // Me.PaintTextStyle(g, rectTextMore, cellStyle, False, Me.mTextMore)
                GuiHelper.DrawText(g, this.mTextMore, this.mFont, rectTextMore, this.tbForColor, Color.Transparent, ContentAlignment.MiddleLeft);
            }

            // 绘忽略的按钮
            if (this.mShowIgnoreButton)
            {
                this.mRectIgnore = new Rectangle(this.mRectOneButton.Right + this.mButtonDiff * 5, rect.Top + (rect.Height - this.mImgIgnore.Height) / 2, this.mImgIgnore.Width / 4, this.mImgIgnore.Height);

                GuiHelper.DrawImage(g, this.mRectIgnore, this.m_Split, this.mImgIgnore, this.Enabled, msIgnore, ImageState.FourState);
            }
        }

        private void PaintBackupFolder(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle)
        {
            // 绘文字
            Rectangle rectText = new Rectangle(rect.X, rect.Y, this.mImgProgressBackground.Width, rect.Height);

            Size textSize = TextRenderer.MeasureText(this.mText, cellStyle.Font);
            if (this.mImgProgressBackground.Width < textSize.Width)
                this.mToolTipText = this.mText;

            this.PaintTextStyle(g, rectText, cellStyle, false, this.mText, ContentAlignment.MiddleLeft, false);

            // 绘图标
            MouseStates msBackupFolder = MouseStates.MouseLeave;
            if (this.mCellButtonDown == CellButtonStyle.BackupFolder)
                msBackupFolder = MouseStates.MouseDown;
            else if (this.mCellButtonHover == CellButtonStyle.BackupFolder)
                msBackupFolder = MouseStates.MouseHover;

            this.mRectBackupFolder = new Rectangle(rectText.Right + this.mButtonDiff, rect.Top + (rect.Height - this.mImgBackupFolder.Height) / 2 - 1, this.mImgBackupFolder.Width / (int)this.mBackupFolderState, this.mImgBackupFolder.Height);

            GuiHelper.DrawImage(g, this.mRectBackupFolder, this.m_Split, this.mImgBackupFolder, this.Enabled, msBackupFolder, this.mBackupFolderState);
        }

        private void PaintReinstall(System.Drawing.Graphics g, System.Drawing.Rectangle rect, System.Windows.Forms.DataGridViewCellStyle cellStyle)
        {
            // 绘文字
            Size textSize = TextRenderer.MeasureText(this.mText, cellStyle.Font);
            Size textReinstallSize = TextRenderer.MeasureText(this.mTextReInstall, this.mFailedTextFont);

            Rectangle rectText = new Rectangle(rect.X, rect.Y, this.mButtonSizeBackup.Width, rect.Height);
            if (rectText.Width < textSize.Width)
                rectText.Width = textSize.Width;
            if (rectText.Width > rect.Width - this.mButtonDiff - textReinstallSize.Width)
                rectText.Width = rect.Width - this.mButtonDiff - textReinstallSize.Width;
            this.PaintTextStyle(g, rectText, cellStyle, false, this.mText, ContentAlignment.MiddleLeft, false);

            // 判断是否显示tooltip
            if (this.mButtonSizeBackup.Width < textSize.Width)
            {
                this.ToolTipText = this.mText;
                this.mToolTipText = this.mText;
            }

            // 绘ReInstall文字
            MouseStates msReinstall = MouseStates.MouseLeave;

            if (this.mCellButtonDown == CellButtonStyle.Reinstall)
                msReinstall = MouseStates.MouseDown;
            else if (this.mCellButtonHover == CellButtonStyle.Reinstall)
                msReinstall = MouseStates.MouseHover;

            this.mRectReinstall = new Rectangle(rectText.Right + this.mButtonDiff, rect.Top + (rect.Height - textReinstallSize.Height) / 2, textReinstallSize.Width, textReinstallSize.Height);

            GuiHelper.DrawText(g, this.mTextReInstall, this.mFont, this.mRectReinstall, Color.FromArgb(22, 138, 210), Color.Transparent, ContentAlignment.MiddleLeft);
        }

        protected override void OnMouseDown(System.Windows.Forms.DataGridViewCellMouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                if (this.Enabled)
                {
                    this.mCellButtonDown = this.GetCellButton(e.Location);
                    if (this.mCellButtonDown != CellButtonStyle.None)
                        this.Invalidate();
                }
            }

            base.OnMouseDown(e);
        }

        protected override void OnMouseUp(System.Windows.Forms.DataGridViewCellMouseEventArgs e)
        {
            base.OnMouseUp(e);

            if (this.Enabled)
            {
                CellButtonStyle tmpDownButton = this.GetCellButton(e.Location);
                ((tbDataGridViewEx)this.DataGridView).OnShowToolTip(new CellButtonEventArgs(CellButtonStyle.None, this));

                if (Convert.ToBoolean(this.mCellButtonEnable & (int)this.mCellButtonDown) && tmpDownButton == this.mCellButtonDown)
                {
                    switch (this.mCellButtonDown)
                    {
                        case CellButtonStyle.Cancel:
                            {
                                if (!this.tbShowCancelButton)
                                    goto DoExit;
                                break;
                            }
                    }

                    ((tbDataGridViewEx)this.DataGridView).OnCellButtonClick(new CellButtonEventArgs(this.mCellButtonDown, this));
                }

            DoExit:
                ;
                this.mCellButtonDown = CellButtonStyle.None;
            }
        }

        protected override void OnMouseMove(System.Windows.Forms.DataGridViewCellMouseEventArgs e)
        {
            // 如果是按钮灰掉就不用重绘
            if (this.Enabled)
            {
                CellButtonStyle tmpHoverButton = this.GetCellButton(e.Location);

                if (tmpHoverButton != this.mCellButtonHover)
                {
                    this.mCellButtonHover = tmpHoverButton;
                    ((tbDataGridViewEx)this.DataGridView).OnShowToolTip(new CellButtonEventArgs(this.mCellButtonHover, this));
                    this.ToolTipText = string.Empty;
                    this.Invalidate();
                    this.mblnCellMouseNone = false;
                }
                else if (this.mblnCellMouseNone == false && tmpHoverButton == CellButtonStyle.None)
                {
                    this.mblnCellMouseNone = true;
                    Rectangle rect = this.DataGridView.GetCellDisplayRectangle(this.ColumnIndex, this.RowIndex, true);
                    ((tbDataGridViewEx)this.DataGridView).OnShowToolTip(new CellButtonEventArgs(tmpHoverButton, this, false, rect));
                    this.Invalidate();
                }
                else if (tmpHoverButton == CellButtonStyle.ProgressSpeed)
                {
                    if (this.mCellButtonHover != tmpHoverButton)
                    {
                        this.mCellButtonHover = tmpHoverButton;
                        ((tbDataGridViewEx)this.DataGridView).OnShowToolTip(new CellButtonEventArgs(this.mCellButtonHover, this));
                    }
                }

                if (this.mCellButtonHover == CellButtonStyle.Reinstall || this.mCellButtonHover == CellButtonStyle.Failed || mProgressMsgClick)
                    this.DataGridView.Cursor = Cursors.Hand;
                else
                    this.DataGridView.Cursor = Cursors.Default;
            }

            base.OnMouseMove(e);
        }

        protected override void OnMouseLeave(int rowIndex)
        {
            // 如果是按钮灰掉就不用重绘
            if (this.Enabled)
            {
                if (mProgressMsgClick)
                    this.DataGridView.Cursor = Cursors.Default;

                if (this.mCellButtonHover != CellButtonStyle.None || this.mCellButtonDown != CellButtonStyle.None)
                {
                    this.mCellButtonDown = CellButtonStyle.None;
                    this.mCellButtonHover = CellButtonStyle.None;
                    ((tbDataGridViewEx)this.DataGridView).OnShowToolTip(new CellButtonEventArgs(CellButtonStyle.None, this));
                    this.tbToolTipText = string.Empty;
                    this.Invalidate();
                }
            }

            this.mblnCellMouseNone = false;

            base.OnMouseLeave(rowIndex);
        }

        private bool IsInOneButtonStyle()
        {
            if (this.Enabled && this.mProgressStyle == ProgressStyle.OneButton)
                return true;

            return false;
        }

        private CellButtonStyle GetCellButton(Point pos)
        {
            // pos是相对于cell的左上角。因此矩形区域要转化成相对于cell的坐标。
            Rectangle rectSrc = this.DataGridView.GetCellDisplayRectangle(this.ColumnIndex, this.RowIndex, false);
            Rectangle rectCell = new Rectangle(0, 0, rectSrc.Width, rectSrc.Height);
            Rectangle rect = new Rectangle(rectCell.Left + this.m_Padding.Left, rectCell.Top + this.m_Padding.Top, rectCell.Width - this.m_Padding.Horizontal, rectCell.Height - this.m_Padding.Vertical);
            pos.Offset(rectSrc.X, rectSrc.Y);
            return GetCellButton(rectSrc, pos);
        }

        private CellButtonStyle GetCellButton(Rectangle rect, Point pos)
        {
            CellButtonStyle CellButton = CellButtonStyle.None;

            switch (this.mProgressStyle)
            {
                case ProgressStyle.Button:
                    {
                        if (this.mRectBackup.Contains(pos))
                            CellButton = CellButtonStyle.Backup;
                        else if (this.mRectUninstall.Contains(pos))
                            CellButton = CellButtonStyle.Uninstall;
                        else if (this.mRectDocument.Contains(pos))
                            CellButton = CellButtonStyle.Document;
                        else if (this.mRectImport.Contains(pos))
                            CellButton = CellButtonStyle.Import;
                        break;
                    }

                case ProgressStyle.Progress:
                case ProgressStyle.Text:
                    {
                        if (this.mShowCancelButton && this.mRectCancel.Contains(pos))
                            CellButton = CellButtonStyle.Cancel;
                        if (this.mShowButtonThird && this.mRectDocument.Contains(pos))
                            CellButton = CellButtonStyle.Document;
                        if (this.mShowButtonFolder && this.mRectDocument.Contains(pos))
                            CellButton = CellButtonStyle.Document;
                        break;
                    }

                case ProgressStyle.BackupFolder:
                    {
                        if (this.mRectBackupFolder.Contains(pos))
                            CellButton = CellButtonStyle.BackupFolder;
                        break;
                    }

                case ProgressStyle.Reinstall:
                    {
                        if (this.mRectReinstall.Contains(pos))
                            CellButton = CellButtonStyle.Reinstall;
                        break;
                    }

                case ProgressStyle.Failed:
                    {
                        if (this.mRectFailed.Contains(pos))
                            CellButton = CellButtonStyle.Failed;
                        break;
                    }

                case ProgressStyle.OneButton:
                    {
                        if (this.mRectOneButton.Contains(pos))
                            CellButton = CellButtonStyle.OneButton;
                        else if (this.mShowIgnoreButton && this.mRectIgnore.Contains(pos))
                            CellButton = CellButtonStyle.Ignore;
                        break;
                    }

                case ProgressStyle.DownloadProgress:
                    {
                        if (this.mRectSpeed != Rectangle.Empty && this.mRectSpeed.Contains(pos))
                            CellButton = CellButtonStyle.ProgressSpeed;
                        break;
                    }

                case ProgressStyle.OneRetryButton:
                    {
                        if (this.mRectFailed.Contains(pos))
                            CellButton = CellButtonStyle.OneRetryButton;
                        else if (this.mShowIgnoreButton && this.mRectIgnore.Contains(pos))
                            CellButton = CellButtonStyle.Ignore;
                        break;
                    }
            }

            return CellButton;
        }

        private bool CheckPosInRect(Point pos)
        {
            // pos是相对于cell的左上角。因此矩形区域要转化成相对于cell的坐标。
            bool isContains = false;
            Rectangle rectSrc = this.DataGridView.GetCellDisplayRectangle(this.ColumnIndex, this.RowIndex, false);
            Rectangle rectCell = new Rectangle(0, 0, rectSrc.Width, rectSrc.Height);
            Rectangle rect = new Rectangle(rectCell.Left + this.m_Padding.Left, rectCell.Top + this.m_Padding.Top, rectCell.Width - this.m_Padding.Horizontal, rectCell.Height - this.m_Padding.Vertical);

            switch (this.mProgressStyle)
            {
                case ProgressStyle.OneButton:
                    {
                        int iLeft = rect.Left;
                        int iTop = rect.Top + (rect.Height - this.mButtonSizeBackup.Height) / 2;

                        Rectangle rectOneButton = new Rectangle(iLeft, iTop, this.mButtonSizeBackup.Width, this.mButtonSizeBackup.Height);
                        if (rectOneButton.Contains(pos))
                            isContains = true;
                        break;
                    }
            }

            return isContains;
        }



        private void TimerWaiting_Tick(object sender, System.Timers.ElapsedEventArgs e)
        {
            if (this.mProgressStyle != ProgressStyle.UnsureProgress)
            {
                this.mTmrWaiting.Enabled = false;

                return;
            }

            // Noted by Utmost20140718
            // Value为Object类型，下面操作需要Boxing 或 UnBoxing
            // Me.Value += 1
            // Me.mImgProgressWaiting = (GuiResource.GetWaitingImageFromValue(Me.Value))

            // ' Added by Utmost20140718
            this._ValueStep += 1;
            int vst = this.ValueStep;
            this.mImgProgressWaiting = (GuiResource.GetWaitingImageFromValue(ref vst));
            this.ValueStep = vst;
        }
    }

}
