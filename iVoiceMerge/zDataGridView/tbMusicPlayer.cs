﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using iTong.Components;
using System.Drawing.Drawing2D;
using iTong.CoreFoundation;
using System.Windows.Forms;

namespace iVoiceMerge
{
    public partial class tbMusicPlayer : System.Windows.Forms.Control
    {
        private PlaySource mPlaySource = PlaySource.Undefined;
        private UserPlayMode mPlayMode = UserPlayMode.Undefined;
        private PlayStatus mPlayStatus = PlayStatus.Ready;
        private bool mShowDown = true;

        private string mTitle = "青花瓷";
        private string mArtist = "周杰伦";

        public event EventHandler<PlayModeEventArgs> PlayModeChanged;
        public event EventHandler<ProgressEventArgs> PlayValueChanged;
        // Public Event VoiceValueChanged As EventHandler(Of ProgressEventArgs)

        public event EventHandler<ProgressEventArgs> VoiceValueChanged;

        public event EventHandler Play;
        public event EventHandler Pause;
        public event EventHandler Prev;
        public event EventHandler Next;


        public PlayStatus tbButtonState
        {
            get
            {
                return this.mPlayStatus;
            }
            set
            {
                this.mPlayStatus = value;

                if (this.mPlayStatus == PlayStatus.Play)
                {
                    this.btnPlay.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_pause;
                    this.btnPlay.tbBackgroundImageState = ImageState.FourState;
                }
                else
                {
                    this.btnPlay.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_play;
                    this.btnPlay.tbBackgroundImageState = ImageState.FourState;
                }
            }
        }

        public PlaySource tbPlaySource
        {
            get
            {
                return this.mPlaySource;
            }
            set
            {
                if (value != this.mPlaySource)
                {
                    this.mPlaySource = value;
                    this.tbPlayValue = 0;
                    this.tbPlayTime = "";
                    this.UpdatePlaySource();
                }
            }
        }

        public UserPlayMode tbPlayMode
        {
            get
            {
                return this.mPlayMode;
            }
            set
            {
                if (value != this.mPlayMode)
                {
                    this.mPlayMode = value;
                    this.UpdatePlayMode();
                }
            }
        }

        public string tbSingleCycleText
        {
            get
            {
                return this.tsmiSingle.Text;
            }
            set
            {
                this.tsmiSingle.Text = value;
            }
        }

        public string tbOrderPlayText
        {
            get
            {
                return this.tsmiOrder.Text;
            }
            set
            {
                this.tsmiOrder.Text = value;
            }
        }

        public string tbLoopPlayText
        {
            get
            {
                return this.tsmiLoop.Text;
            }
            set
            {
                this.tsmiLoop.Text = value;
            }
        }

        public string tbRandomPlayText
        {
            get
            {
                return this.tsmiRandom.Text;
            }
            set
            {
                this.tsmiRandom.Text = value;
            }
        }

        public string tbPlayTime
        {
            get
            {
                return this.lblTime.Text;
            }
            set
            {
                this.lblTime.Text = value;
                this.UpdateTitle();
            }
        }

        public string tbTitle
        {
            get
            {
                return this.mTitle;
            }
            set
            {
                this.mTitle = value;
                this.UpdateTitle();
            }
        }

        public string tbArtist
        {
            get
            {
                return this.mArtist;
            }
            set
            {
                this.mArtist = value;
                this.UpdateTitle();
            }
        }

        public int tbPlayMax
        {
            get
            {
                return this.proProgress.tbPlayMax;
            }
            set
            {
                this.proProgress.tbPlayMax = value;
            }
        }

        public int tbPlayMin
        {
            get
            {
                return this.proProgress.tbPlayMin;
            }
            set
            {
                this.proProgress.tbPlayMin = value;
            }
        }

        public int tbPlayValue
        {
            get
            {
                return (int)this.proProgress.tbPlayValue;
            }
            set
            {
                this.proProgress.tbPlayValue = value;
            }
        }

        public int tbDownMax
        {
            get
            {
                return this.proProgress.tbDownMax;
            }
            set
            {
                this.proProgress.tbDownMax = value;
            }
        }

        public int tbDownMin
        {
            get
            {
                return this.proProgress.tbDownMin;
            }
            set
            {
                this.proProgress.tbDownMin = value;
            }
        }

        public int tbDownValue
        {
            get
            {
                return this.proProgress.tbDownValue;
            }
            set
            {
                this.proProgress.tbDownValue = value;
            }
        }


        public tbMusicPlayer()
        {

            // 此调用是 Windows 窗体设计器所必需的。
            InitializeComponent();

            // 在 InitializeComponent() 调用之后添加任何初始化。

            this.SetStyle(ControlStyles.AllPaintingInWmPaint, true);
            this.SetStyle(ControlStyles.OptimizedDoubleBuffer, true);
            this.SetStyle(ControlStyles.SupportsTransparentBackColor, true);
            this.SetStyle(ControlStyles.ResizeRedraw, true);
            this.SetStyle(ControlStyles.Selectable, false);
            this.SetStyle(ControlStyles.UserPaint, true);
            this.SetStyle(ControlStyles.ContainerControl, false);

            this.UpdateStyles();
        }


        private void UpdateTitle()
        {
            string strText = this.mTitle;

            if (this.mArtist.Length > 0)
                strText = this.mArtist + " - " + strText;

            this.lblTitle.Text = strText;
        }

        private void UpdatePlaySource()
        {
            switch (this.mPlaySource)
            {
                case PlaySource.Local:
                    {
                        this.lblTitle.tbIconImage = null;

                        this.btnPlayStyle.Visible = true;
                        this.lblTime.Visible = true;
                        break;
                    }

                case PlaySource.Radio:
                    {
                        // Me.lblTitle.tbIconImage = global::iVoiceMerge.Properties.Resources.player_radio

                        this.btnPlayStyle.Visible = false;
                        this.lblTime.Visible = false;
                        this.proProgress.tbPlayValue = 0;
                        break;
                    }

                case PlaySource.Web:
                case PlaySource.Ringtone:
                    {
                        // Me.lblTitle.tbIconImage = global::iVoiceMerge.Properties.Resources.player_listen

                        this.btnPlayStyle.Visible = false;
                        this.lblTime.Visible = true;
                        break;
                    }
            }
        }

        private void UpdatePlayMode()
        {
            switch (this.mPlayMode)
            {
                case UserPlayMode.SingleCycle:
                    {
                        tsmiSingle_Click(this.tsmiSingle, new EventArgs());
                        break;
                    }

                case UserPlayMode.OrderPlay:
                    {
                        tsmiOrder_Click(this.tsmiOrder, new EventArgs());
                        break;
                    }

                case UserPlayMode.LoopPlay:
                    {
                        tsmiLoop_Click(this.tsmiLoop, new EventArgs());
                        break;
                    }

                case UserPlayMode.RandomPlay:
                    {
                        tsmiRandom_Click(this.tsmiRandom, new EventArgs());
                        break;
                    }
            }
        }


        private void btnPlay_Click(System.Object sender, System.EventArgs e)
        {
            if (this.mPlayStatus == PlayStatus.Ready)
            {
                this.tbButtonState = PlayStatus.Play;
                if (Play != null)
                    Play(this, new EventArgs());
            }
            else
            {
                this.tbButtonState = PlayStatus.Ready;
                if (Pause != null)
                    Pause(this, new EventArgs());
            }
        }

        private void btnPrev_Click(System.Object sender, System.EventArgs e)
        {
            if (Prev != null)
                Prev(this, new EventArgs());
        }

        private void btnNext_Click(System.Object sender, System.EventArgs e)
        {
            if (Next != null)
                Next(this, new EventArgs());
        }

        private void btnPlayStyle_Click(System.Object sender, System.EventArgs e)
        {
            this.cmsPlayStyle.Show(this.btnPlayStyle, 0, 0 - cmsPlayStyle.Height);
        }

        private void tsmiSingle_Click(System.Object sender, System.EventArgs e)
        {
            if (tsmiSingle.Checked)
                return;

            foreach (ToolStripMenuItem item in this.cmsPlayStyle.Items)
                item.Checked = false;
            tsmiSingle.Checked = true;
            this.btnPlayStyle.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_single;
            this.btnPlayStyle.tbBackgroundImageState = ImageState.FourState;
            this.mPlayMode = UserPlayMode.SingleCycle;
            if (PlayModeChanged != null)
                PlayModeChanged(this, new PlayModeEventArgs(ClickType.Player, this.mPlayMode));
        }

        private void tsmiOrder_Click(System.Object sender, System.EventArgs e)
        {
            if (tsmiOrder.Checked)
                return;

            foreach (ToolStripMenuItem item in this.cmsPlayStyle.Items)
                item.Checked = false;
            tsmiOrder.Checked = true;
            this.btnPlayStyle.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_order;
            this.btnPlayStyle.tbBackgroundImageState = ImageState.FourState;
            this.mPlayMode = UserPlayMode.OrderPlay;
            if (PlayModeChanged != null)
                PlayModeChanged(this, new PlayModeEventArgs(ClickType.Player, this.mPlayMode));
        }

        private void tsmiLoop_Click(System.Object sender, System.EventArgs e)
        {
            if (tsmiLoop.Checked)
                return;

            foreach (ToolStripMenuItem item in this.cmsPlayStyle.Items)
                item.Checked = false;
            tsmiLoop.Checked = true;
            this.btnPlayStyle.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_loop;
            this.btnPlayStyle.tbBackgroundImageState = ImageState.FourState;
            this.mPlayMode = UserPlayMode.LoopPlay;
            if (PlayModeChanged != null)
                PlayModeChanged(this, new PlayModeEventArgs(ClickType.Player, this.mPlayMode));
        }

        private void tsmiRandom_Click(System.Object sender, System.EventArgs e)
        {
            if (tsmiRandom.Checked)
                return;

            foreach (ToolStripMenuItem item in this.cmsPlayStyle.Items)
                item.Checked = false;
            tsmiRandom.Checked = true;
            this.btnPlayStyle.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_random;
            this.btnPlayStyle.tbBackgroundImageState = ImageState.FourState;
            this.mPlayMode = UserPlayMode.RandomPlay;
            if (PlayModeChanged != null)
                PlayModeChanged(this, new PlayModeEventArgs(ClickType.Player, this.mPlayMode));
        }

        private void proProgress_MouseLeave(System.Object sender, System.EventArgs e)
        {
            this.proProgress.tbShowDot = false;
        }

        private void proProgress_MouseEnter(System.Object sender, System.EventArgs e)
        {
            this.proProgress.tbShowDot = true;
        }

        private tbSplitStructure m_Split = new tbSplitStructure(3, 8, 3, 8);
        protected override void OnPaint(System.Windows.Forms.PaintEventArgs e)
        {
            base.OnPaint(e);

            e.Graphics.InterpolationMode = InterpolationMode.HighQualityBicubic;
            GuiHelper.DrawImage(e.Graphics, this.ClientRectangle, this.m_Split, global::iVoiceMerge.Properties.Resources.player_bg, this.Enabled, MouseStates.MouseLeave, ImageState.OneState);
        }


        private PNGControlForm mFormTime;
        private Label mLabel;

        private void proProgress_ProgressChanged(System.Object sender, ProgressEventArgs e)
        {
            if (mFormTime != null)
                this.mFormTime.Hide();

            if (this.tbButtonState == PlayStatus.Ready || this.mPlaySource == PlaySource.Undefined || this.mPlaySource == PlaySource.Radio)
            {
                e.Cancel = true;
                return;
            }
            if (PlayValueChanged != null)
                PlayValueChanged(this, e);
        }

        private void OnShown_FormTime(object sender, EventArgs e)
        {
            if (((Form)sender).Size != global::iVoiceMerge.Properties.Resources.player_time_bg.Size)
                ((Form)sender).Size = global::iVoiceMerge.Properties.Resources.player_time_bg.Size;
        }

        private void proProgress_ProgressChanging(System.Object sender, ProgressEventArgs e)
        {
            if (mFormTime == null)
            {
                mFormTime = new PNGControlForm();
                {
                    var withBlock = mFormTime;
                    withBlock.Size = new Size(70, 42);
                    withBlock.StartPosition = FormStartPosition.Manual;
                    withBlock.PNGForm.ShowInTaskbar = false;
                    withBlock.BackgroundImage = global::iVoiceMerge.Properties.Resources.player_time_bg;
                    withBlock.TopLevel = true;
                    withBlock.TransparencyKey = withBlock.BackColor;

                    mLabel = new Label();
                    mLabel.Size = new Size(50, 15);
                    mLabel.TextAlign = ContentAlignment.MiddleCenter;
                    mLabel.Font = Common.CreateFont("Arial", 9, FontStyle.Regular);
                    if (Common.VerIs30())
                    {
                        mLabel.ForeColor = Color.Black;
                        mLabel.BackColor = Color.White;
                    }
                    else
                    {
                        mLabel.ForeColor = Color.White;
                        mLabel.BackColor = Color.FromArgb(32, 32, 32);
                    }


                    withBlock.Controls.Add(mLabel);
                    mLabel.Location = new Point((withBlock.Width - mLabel.Width) / 2, 12);

                    withBlock.Shown += OnShown_FormTime;
                }
            }

            mLabel.Text = Utility.FormatDuration(e.ProgressValue);

            Point frmPos = new Point(e.Position.X - mFormTime.Width / 2, e.Position.Y - mFormTime.Height + 5);
            mFormTime.Location = frmPos;
            mFormTime.Show();
            mFormTime.Activate();
        }



        private System.Windows.Forms.Timer mTimerShow;
        private PNGControlForm mFormVoice;
        private tbControlBar mVoiceBar;

        private void proVoice_ProgressChanged(System.Object sender, ProgressEventArgs e)
        {
            // CoreFoundation.VolumeControl.SetVolume(e.ProgressValue)
            if (VoiceValueChanged != null)
                VoiceValueChanged(this, e);
        }

        private void OnShown_FormVoice(object sender, EventArgs e)
        {
            if (((Form)sender).Size != global::iVoiceMerge.Properties.Resources.player_voice_bg.Size)
                ((Form)sender).Size = global::iVoiceMerge.Properties.Resources.player_voice_bg.Size;
        }

        private void mTimerShow_Tick(System.Object sender, System.EventArgs e)
        {
            if (this.Visible && !this.CheckPoint(this.btnVoice) && !this.CheckPoint(mFormVoice))
            {
                this.mTimerShow.Stop();
                this.mFormVoice.Hide();
            }
        }

        // 检测鼠标位置
        private bool CheckPoint(System.Windows.Forms.Control c)
        {
            Rectangle rect = new Rectangle(c.PointToScreen(Point.Empty), c.Size);
            Point pos = System.Windows.Forms.Cursor.Position;

            return rect.Contains(pos);
        }

        private void btnVoice_Click(System.Object sender, System.EventArgs e)
        {
            if (mFormVoice == null)
            {
                mFormVoice = new PNGControlForm();
                {
                    var withBlock = mFormVoice;
                    mVoiceBar = new tbControlBar();
                    withBlock.Controls.Add(mVoiceBar);

                    withBlock.Size = new Size(50, 87);
                    withBlock.StartPosition = FormStartPosition.Manual;
                    withBlock.PNGForm.ShowInTaskbar = false;
                    withBlock.BackgroundImage = global::iVoiceMerge.Properties.Resources.player_voice_bg;
                    withBlock.TopLevel = true;
                    withBlock.TransparencyKey = withBlock.BackColor;
                    withBlock.CanbeMove = false;
                    withBlock.BackColor = Color.Blue;

                    mVoiceBar.Size = new Size(18, 54);
                    mVoiceBar.tbDirection = tbControlBar.tbBarDirection.Vertical;
                    mVoiceBar.tbSplit = "0,4,0,4";
                    mVoiceBar.Padding = new Padding(5, 0, 5, 0);
                    // mVoiceBar.tbDotImage = global::iVoiceMerge.Properties.Resources.player_progress_dot
                    // mVoiceBar.tbPlayImage = global::iVoiceMerge.Properties.Resources.player_progress_play_H
                    // mVoiceBar.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_progress_bg_H
                    if (Common.VerIs30())
                        mVoiceBar.BackColor = Color.White;
                    else
                        mVoiceBar.BackColor = Color.FromArgb(32, 32, 32);

                    mVoiceBar.Margin = new System.Windows.Forms.Padding(0);
                    mVoiceBar.tbShowDot = true;
                    mVoiceBar.Location = new Point((withBlock.Width - mVoiceBar.Width) / 2, 12);

                    mTimerShow = new System.Windows.Forms.Timer();
                    mTimerShow.Interval = 500;
                    mTimerShow.Tick += mTimerShow_Tick;



                    withBlock.Shown += OnShown_FormVoice;
                    mVoiceBar.ProgressChanged += proVoice_ProgressChanged;
                }
            }

            Point pos = this.PointToScreen(new Point(this.btnVoice.Left + this.btnVoice.Width / 2, this.btnVoice.Top));
            Point frmPos = new Point(pos.X - mFormVoice.Width / 2, pos.Y - mFormVoice.Height + 5);
            mFormVoice.Location = frmPos;
            mFormVoice.Show();

            mFormVoice.Activate();

            mTimerShow.Start();

            mVoiceBar.BringToFront();
        }
    }

    public class PlayModeEventArgs : ClickEventArgs
    {
        private UserPlayMode mPlayMode = UserPlayMode.OrderPlay;
        public UserPlayMode PlayMode
        {
            get
            {
                return this.mPlayMode;
            }
            set
            {
                this.mPlayMode = value;
            }
        }

        public PlayModeEventArgs(ClickType from)
            : base(from)
        {
        }

        public PlayModeEventArgs(ClickType from, UserPlayMode playMode)
            : this(from)
        {
            this.mPlayMode = playMode;
        }
    }

    public class CheckEventArgs : ClickEventArgs
    {
        private bool mCheck = false;
        public bool Check
        {
            get
            {
                return this.mCheck;
            }
            set
            {
                this.mCheck = value;
            }
        }

        public CheckEventArgs(ClickType from)
            : base(from)
        {
        }

        public CheckEventArgs(ClickType from, bool check)
            : this(from)
        {
            this.mCheck = check;
        }
    }

    public enum PlayStatus
    {
        Ready,
        Play
    }


}
