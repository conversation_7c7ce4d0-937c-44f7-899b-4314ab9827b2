﻿using iTong.Components;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iVoiceMerge
{
    public class tbDataGridViewEx : tbDataGridView
    {
        public event EventHandler<CellButtonEventArgs> CellButtonClick;
        public event EventHandler<CellButtonEventArgs> ShowToolTip;
        public event ShowCollectToolTipEventHandler ShowCollectToolTip;

        public delegate void ShowCollectToolTipEventHandler(int rowIndex, bool isShowTip);

        public event CellCollectButtonClickEventHandler CellCollectButtonClick;

        public delegate void CellCollectButtonClickEventHandler(int rowIndex);

        public void OnCellButtonClick(CellButtonEventArgs e)
        {
            if (CellButtonClick != null)
                CellButtonClick(this, e);
        }

        public void OnShowToolTip(CellButtonEventArgs e)
        {
            if (ShowToolTip != null)
                ShowToolTip(this, e);
        }

        public void OnShowCollectToolTip(int rowIndex, bool isShowTip)
        {
            if (ShowCollectToolTip != null)
                ShowCollectToolTip(rowIndex, isShowTip);
        }

        public void OnCollectCellButtonClick(int rowIndex)
        {
            if (CellCollectButtonClick != null)
                CellCollectButtonClick(rowIndex);
        }
    }

    public enum CellButtonStyle
    {
        None = 0,
        Backup = 1,// 备份
        Uninstall = 2,// 卸载
        CrakedInfo = 3, // 显示越狱状态ICON
        Cancel = 4,// 取消
        ProgressSpeed = 5,
        OneRetryButton = 6,// 2015-07-14 by chenbihai 企业签名包 签名过期 重新获取
        Payment = 7,           // 闪退赔付有列表添加钱的标记
        Failed = 8, // 失败
        Import = 9,
        ShowAppleID = 10,
        OneButton = 16,
        BackupFolder = 32,// 打开备份目录
        Reinstall = 64,// 重装
        Info = 128,
        Ignore = 256, // 忽略
        Playing = 512,
        Paused = 1024,
        FileNoExist = 2048,
        Edit = 4096,
        Progress = 8192,
        Document = 16384, // 文件共享
        BackupLock = 32768,
        BackupUnlock = 65536
    }

    public class CellButtonEventArgs : EventArgs
    {
        private CellButtonStyle mCellButton = CellButtonStyle.None;
        private tbDataGridViewCell mCell = null/* TODO Change to default(_) if this is not a reference type */;
        private bool mIsCollect = false;
        private Rectangle mBound = new Rectangle(0, 0, 0, 0);

        public tbDataGridViewCell Cell
        {
            get
            {
                return this.mCell;
            }
            set
            {
                this.mCell = value;
            }
        }

        public CellButtonStyle CellButton
        {
            get
            {
                return this.mCellButton;
            }
            set
            {
                this.mCellButton = value;
            }
        }

        public bool IsCollect
        {
            get
            {
                return this.mIsCollect;
            }
            set
            {
                this.mIsCollect = value;
            }
        }

        public Rectangle Bound
        {
            get
            {
                return this.mBound;
            }
            set
            {
                this.mBound = value;
            }
        }

        public CellButtonEventArgs(CellButtonStyle btn)
            : this(btn, null/* TODO Change to default(_) if this is not a reference type */)
        {
        }

        public CellButtonEventArgs(CellButtonStyle btn, tbDataGridViewCell cell)
        {
            this.mCell = cell;
            this.mCellButton = btn;
        }

        public CellButtonEventArgs(CellButtonStyle btn, tbDataGridViewCell cell, bool isCollect)
        {
            this.mCell = Cell;
            this.mCellButton = btn;
            this.mIsCollect = isCollect;
        }

        public CellButtonEventArgs(CellButtonStyle btn, tbDataGridViewCell cell, bool isCollect, Rectangle rec)
            : this(btn, cell, isCollect)
        {
            this.mBound = rec;
        }
    }

}
