﻿namespace iVoiceMerge
{
    partial class tbMusicPlayer
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(tbMusicPlayer));
            this.lblTime = new System.Windows.Forms.Label();
            this.cmsPlayStyle = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiSingle = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiOrder = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiLoop = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiRandom = new System.Windows.Forms.ToolStripMenuItem();
            this.lblTitle = new iTong.Components.tbLabel();
            this.btnPlay = new iTong.Components.tbButton();
            this.btnPrev = new iTong.Components.tbButton();
            this.btnNext = new iTong.Components.tbButton();
            this.btnVoice = new iTong.Components.tbButton();
            this.btnPlayStyle = new iTong.Components.tbButton();
            this.proProgress = new iTong.Components.tbControlBar();
            this.cmsPlayStyle.SuspendLayout();
            this.SuspendLayout();
            // 
            // lblTime
            // 
            this.lblTime.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lblTime.AutoEllipsis = true;
            this.lblTime.BackColor = System.Drawing.Color.Transparent;
            this.lblTime.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lblTime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(88)))), ((int)(((byte)(117)))), ((int)(((byte)(158)))));
            this.lblTime.Location = new System.Drawing.Point(580, 14);
            this.lblTime.Margin = new System.Windows.Forms.Padding(0);
            this.lblTime.Name = "lblTime";
            this.lblTime.Size = new System.Drawing.Size(83, 14);
            this.lblTime.TabIndex = 16;
            this.lblTime.Text = "13:26/15:26";
            this.lblTime.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmsPlayStyle
            // 
            this.cmsPlayStyle.AccessibleDescription = "153x114";
            this.cmsPlayStyle.DropShadowEnabled = false;
            this.cmsPlayStyle.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.cmsPlayStyle.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiSingle,
            this.tsmiOrder,
            this.tsmiLoop,
            this.tsmiRandom});
            this.cmsPlayStyle.Name = "mnuPlayStyle";
            this.cmsPlayStyle.Size = new System.Drawing.Size(125, 92);
            this.cmsPlayStyle.tbBackColor = System.Drawing.Color.White;
            this.cmsPlayStyle.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.cmsPlayStyle.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.cmsPlayStyle.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiSingle
            // 
            this.tsmiSingle.Name = "tsmiSingle";
            this.tsmiSingle.Size = new System.Drawing.Size(124, 22);
            this.tsmiSingle.Text = "单曲循环";
            this.tsmiSingle.Click += new System.EventHandler(this.tsmiSingle_Click);
            // 
            // tsmiOrder
            // 
            this.tsmiOrder.Name = "tsmiOrder";
            this.tsmiOrder.Size = new System.Drawing.Size(124, 22);
            this.tsmiOrder.Text = "顺序播放";
            this.tsmiOrder.Click += new System.EventHandler(this.tsmiOrder_Click);
            // 
            // tsmiLoop
            // 
            this.tsmiLoop.Name = "tsmiLoop";
            this.tsmiLoop.Size = new System.Drawing.Size(124, 22);
            this.tsmiLoop.Text = "列表循环";
            this.tsmiLoop.Click += new System.EventHandler(this.tsmiLoop_Click);
            // 
            // tsmiRandom
            // 
            this.tsmiRandom.Name = "tsmiRandom";
            this.tsmiRandom.Size = new System.Drawing.Size(124, 22);
            this.tsmiRandom.Text = "随机播放";
            this.tsmiRandom.Click += new System.EventHandler(this.tsmiRandom_Click);
            // 
            // lblTitle
            // 
            this.lblTitle.AutoEllipsis = true;
            this.lblTitle.BackColor = System.Drawing.Color.Transparent;
            this.lblTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(88)))), ((int)(((byte)(117)))), ((int)(((byte)(158)))));
            this.lblTitle.Location = new System.Drawing.Point(13, 13);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(192, 16);
            this.lblTitle.TabIndex = 17;
            this.lblTitle.tbAdriftWhenHover = false;
            this.lblTitle.tbAutoEllipsis = true;
            this.lblTitle.tbAutoSize = false;
            this.lblTitle.tbHideImage = false;
            this.lblTitle.tbIconImage = null;
            this.lblTitle.tbIconImageAlign = System.Drawing.ContentAlignment.TopLeft;
            this.lblTitle.tbIconPlaceText = 5;
            this.lblTitle.tbShadow = false;
            this.lblTitle.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblTitle.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.lblTitle.tbShowScrolling = true;
            this.lblTitle.Text = "你不知道的事 -- 王力宏";
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnPlay
            // 
            this.btnPlay.BackColor = System.Drawing.Color.Transparent;
            this.btnPlay.BindingForm = null;
            this.btnPlay.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnPlay.Location = new System.Drawing.Point(236, 12);
            this.btnPlay.Margin = new System.Windows.Forms.Padding(0);
            this.btnPlay.Name = "btnPlay";
            this.btnPlay.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btnPlay.Selectable = true;
            this.btnPlay.Size = new System.Drawing.Size(18, 18);
            this.btnPlay.TabIndex = 12;
            this.btnPlay.tbAdriftIconWhenHover = false;
            this.btnPlay.tbAutoSize = true;
            this.btnPlay.tbAutoSizeEx = false;
            this.btnPlay.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_play;
            this.btnPlay.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnPlay.tbBadgeNumber = 0;
            this.btnPlay.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnPlay.tbEndEllipsis = false;
            this.btnPlay.tbIconHoldPlace = true;
            this.btnPlay.tbIconImage = null;
            this.btnPlay.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPlay.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnPlay.tbIconMore = false;
            this.btnPlay.tbIconMouseDown = null;
            this.btnPlay.tbIconMouseHover = null;
            this.btnPlay.tbIconMouseLeave = null;
            this.btnPlay.tbIconPlaceText = 2;
            this.btnPlay.tbIconReadOnly = null;
            this.btnPlay.tbImageMouseDown = null;
            this.btnPlay.tbImageMouseHover = null;
            this.btnPlay.tbImageMouseLeave = null;
            this.btnPlay.tbProgressValue = 50;
            this.btnPlay.tbReadOnly = false;
            this.btnPlay.tbReadOnlyText = false;
            this.btnPlay.tbShadow = false;
            this.btnPlay.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnPlay.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnPlay.tbShowDot = false;
            this.btnPlay.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnPlay.tbShowMoreIconImg")));
            this.btnPlay.tbShowNew = false;
            this.btnPlay.tbShowProgress = false;
            this.btnPlay.tbShowTip = true;
            this.btnPlay.tbShowToolTipOnButton = false;
            this.btnPlay.tbSplit = "0,0,0,0";
            this.btnPlay.tbText = "";
            this.btnPlay.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPlay.tbTextColor = System.Drawing.Color.White;
            this.btnPlay.tbTextColorDisable = System.Drawing.Color.White;
            this.btnPlay.tbTextColorDown = System.Drawing.Color.White;
            this.btnPlay.tbTextColorHover = System.Drawing.Color.White;
            this.btnPlay.tbTextMouseDownPlace = 0;
            this.btnPlay.tbToolTip = "";
            this.btnPlay.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnPlay.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnPlay.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPlay.VisibleEx = true;
            this.btnPlay.Click += new System.EventHandler(this.btnPlay_Click);
            // 
            // btnPrev
            // 
            this.btnPrev.BackColor = System.Drawing.Color.Transparent;
            this.btnPrev.BindingForm = null;
            this.btnPrev.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnPrev.Location = new System.Drawing.Point(210, 14);
            this.btnPrev.Margin = new System.Windows.Forms.Padding(0);
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btnPrev.Selectable = true;
            this.btnPrev.Size = new System.Drawing.Size(14, 14);
            this.btnPrev.TabIndex = 13;
            this.btnPrev.tbAdriftIconWhenHover = false;
            this.btnPrev.tbAutoSize = true;
            this.btnPrev.tbAutoSizeEx = false;
            this.btnPrev.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_prev;
            this.btnPrev.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnPrev.tbBadgeNumber = 0;
            this.btnPrev.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnPrev.tbEndEllipsis = false;
            this.btnPrev.tbIconHoldPlace = true;
            this.btnPrev.tbIconImage = null;
            this.btnPrev.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPrev.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnPrev.tbIconMore = false;
            this.btnPrev.tbIconMouseDown = null;
            this.btnPrev.tbIconMouseHover = null;
            this.btnPrev.tbIconMouseLeave = null;
            this.btnPrev.tbIconPlaceText = 2;
            this.btnPrev.tbIconReadOnly = null;
            this.btnPrev.tbImageMouseDown = null;
            this.btnPrev.tbImageMouseHover = null;
            this.btnPrev.tbImageMouseLeave = null;
            this.btnPrev.tbProgressValue = 50;
            this.btnPrev.tbReadOnly = false;
            this.btnPrev.tbReadOnlyText = false;
            this.btnPrev.tbShadow = false;
            this.btnPrev.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnPrev.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnPrev.tbShowDot = false;
            this.btnPrev.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnPrev.tbShowMoreIconImg")));
            this.btnPrev.tbShowNew = false;
            this.btnPrev.tbShowProgress = false;
            this.btnPrev.tbShowTip = true;
            this.btnPrev.tbShowToolTipOnButton = false;
            this.btnPrev.tbSplit = "0,0,0,0";
            this.btnPrev.tbText = "";
            this.btnPrev.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPrev.tbTextColor = System.Drawing.Color.White;
            this.btnPrev.tbTextColorDisable = System.Drawing.Color.White;
            this.btnPrev.tbTextColorDown = System.Drawing.Color.White;
            this.btnPrev.tbTextColorHover = System.Drawing.Color.White;
            this.btnPrev.tbTextMouseDownPlace = 0;
            this.btnPrev.tbToolTip = "";
            this.btnPrev.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnPrev.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnPrev.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPrev.VisibleEx = true;
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // btnNext
            // 
            this.btnNext.BackColor = System.Drawing.Color.Transparent;
            this.btnNext.BindingForm = null;
            this.btnNext.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnNext.Location = new System.Drawing.Point(264, 14);
            this.btnNext.Margin = new System.Windows.Forms.Padding(0);
            this.btnNext.Name = "btnNext";
            this.btnNext.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btnNext.Selectable = true;
            this.btnNext.Size = new System.Drawing.Size(14, 14);
            this.btnNext.TabIndex = 14;
            this.btnNext.tbAdriftIconWhenHover = false;
            this.btnNext.tbAutoSize = true;
            this.btnNext.tbAutoSizeEx = false;
            this.btnNext.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_next;
            this.btnNext.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnNext.tbBadgeNumber = 0;
            this.btnNext.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnNext.tbEndEllipsis = false;
            this.btnNext.tbIconHoldPlace = true;
            this.btnNext.tbIconImage = null;
            this.btnNext.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnNext.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnNext.tbIconMore = false;
            this.btnNext.tbIconMouseDown = null;
            this.btnNext.tbIconMouseHover = null;
            this.btnNext.tbIconMouseLeave = null;
            this.btnNext.tbIconPlaceText = 2;
            this.btnNext.tbIconReadOnly = null;
            this.btnNext.tbImageMouseDown = null;
            this.btnNext.tbImageMouseHover = null;
            this.btnNext.tbImageMouseLeave = null;
            this.btnNext.tbProgressValue = 50;
            this.btnNext.tbReadOnly = false;
            this.btnNext.tbReadOnlyText = false;
            this.btnNext.tbShadow = false;
            this.btnNext.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnNext.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnNext.tbShowDot = false;
            this.btnNext.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnNext.tbShowMoreIconImg")));
            this.btnNext.tbShowNew = false;
            this.btnNext.tbShowProgress = false;
            this.btnNext.tbShowTip = true;
            this.btnNext.tbShowToolTipOnButton = false;
            this.btnNext.tbSplit = "0,0,0,0";
            this.btnNext.tbText = "";
            this.btnNext.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnNext.tbTextColor = System.Drawing.Color.White;
            this.btnNext.tbTextColorDisable = System.Drawing.Color.White;
            this.btnNext.tbTextColorDown = System.Drawing.Color.White;
            this.btnNext.tbTextColorHover = System.Drawing.Color.White;
            this.btnNext.tbTextMouseDownPlace = 0;
            this.btnNext.tbToolTip = "";
            this.btnNext.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnNext.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnNext.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnNext.VisibleEx = true;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnVoice
            // 
            this.btnVoice.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnVoice.BackColor = System.Drawing.Color.Transparent;
            this.btnVoice.BindingForm = null;
            this.btnVoice.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnVoice.Location = new System.Drawing.Point(673, 13);
            this.btnVoice.Name = "btnVoice";
            this.btnVoice.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btnVoice.Selectable = true;
            this.btnVoice.Size = new System.Drawing.Size(16, 16);
            this.btnVoice.TabIndex = 19;
            this.btnVoice.tbAdriftIconWhenHover = false;
            this.btnVoice.tbAutoSize = true;
            this.btnVoice.tbAutoSizeEx = false;
            this.btnVoice.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_voice;
            this.btnVoice.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnVoice.tbBadgeNumber = 0;
            this.btnVoice.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnVoice.tbEndEllipsis = false;
            this.btnVoice.tbIconHoldPlace = true;
            this.btnVoice.tbIconImage = null;
            this.btnVoice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnVoice.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnVoice.tbIconMore = false;
            this.btnVoice.tbIconMouseDown = null;
            this.btnVoice.tbIconMouseHover = null;
            this.btnVoice.tbIconMouseLeave = null;
            this.btnVoice.tbIconPlaceText = 2;
            this.btnVoice.tbIconReadOnly = null;
            this.btnVoice.tbImageMouseDown = null;
            this.btnVoice.tbImageMouseHover = null;
            this.btnVoice.tbImageMouseLeave = null;
            this.btnVoice.tbProgressValue = 50;
            this.btnVoice.tbReadOnly = false;
            this.btnVoice.tbReadOnlyText = false;
            this.btnVoice.tbShadow = false;
            this.btnVoice.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnVoice.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnVoice.tbShowDot = false;
            this.btnVoice.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnVoice.tbShowMoreIconImg")));
            this.btnVoice.tbShowNew = false;
            this.btnVoice.tbShowProgress = false;
            this.btnVoice.tbShowTip = true;
            this.btnVoice.tbShowToolTipOnButton = false;
            this.btnVoice.tbSplit = "3,3,3,3";
            this.btnVoice.tbText = "";
            this.btnVoice.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnVoice.tbTextColor = System.Drawing.Color.White;
            this.btnVoice.tbTextColorDisable = System.Drawing.Color.White;
            this.btnVoice.tbTextColorDown = System.Drawing.Color.White;
            this.btnVoice.tbTextColorHover = System.Drawing.Color.White;
            this.btnVoice.tbTextMouseDownPlace = 0;
            this.btnVoice.tbToolTip = "";
            this.btnVoice.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnVoice.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnVoice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnVoice.VisibleEx = true;
            this.btnVoice.Click += new System.EventHandler(this.btnVoice_Click);
            // 
            // btnPlayStyle
            // 
            this.btnPlayStyle.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPlayStyle.BackColor = System.Drawing.Color.Transparent;
            this.btnPlayStyle.BindingForm = null;
            this.btnPlayStyle.ContextMenuStrip = this.cmsPlayStyle;
            this.btnPlayStyle.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnPlayStyle.Location = new System.Drawing.Point(708, 13);
            this.btnPlayStyle.Name = "btnPlayStyle";
            this.btnPlayStyle.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btnPlayStyle.Selectable = true;
            this.btnPlayStyle.Size = new System.Drawing.Size(16, 16);
            this.btnPlayStyle.TabIndex = 15;
            this.btnPlayStyle.tbAdriftIconWhenHover = false;
            this.btnPlayStyle.tbAutoSize = true;
            this.btnPlayStyle.tbAutoSizeEx = false;
            this.btnPlayStyle.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.player_random;
            this.btnPlayStyle.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnPlayStyle.tbBadgeNumber = 0;
            this.btnPlayStyle.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnPlayStyle.tbEndEllipsis = false;
            this.btnPlayStyle.tbIconHoldPlace = true;
            this.btnPlayStyle.tbIconImage = null;
            this.btnPlayStyle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPlayStyle.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnPlayStyle.tbIconMore = false;
            this.btnPlayStyle.tbIconMouseDown = null;
            this.btnPlayStyle.tbIconMouseHover = null;
            this.btnPlayStyle.tbIconMouseLeave = null;
            this.btnPlayStyle.tbIconPlaceText = 2;
            this.btnPlayStyle.tbIconReadOnly = null;
            this.btnPlayStyle.tbImageMouseDown = null;
            this.btnPlayStyle.tbImageMouseHover = null;
            this.btnPlayStyle.tbImageMouseLeave = null;
            this.btnPlayStyle.tbProgressValue = 50;
            this.btnPlayStyle.tbReadOnly = false;
            this.btnPlayStyle.tbReadOnlyText = false;
            this.btnPlayStyle.tbShadow = false;
            this.btnPlayStyle.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnPlayStyle.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnPlayStyle.tbShowDot = false;
            this.btnPlayStyle.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnPlayStyle.tbShowMoreIconImg")));
            this.btnPlayStyle.tbShowNew = false;
            this.btnPlayStyle.tbShowProgress = false;
            this.btnPlayStyle.tbShowTip = true;
            this.btnPlayStyle.tbShowToolTipOnButton = false;
            this.btnPlayStyle.tbSplit = "3,3,3,3";
            this.btnPlayStyle.tbText = "";
            this.btnPlayStyle.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPlayStyle.tbTextColor = System.Drawing.Color.White;
            this.btnPlayStyle.tbTextColorDisable = System.Drawing.Color.White;
            this.btnPlayStyle.tbTextColorDown = System.Drawing.Color.White;
            this.btnPlayStyle.tbTextColorHover = System.Drawing.Color.White;
            this.btnPlayStyle.tbTextMouseDownPlace = 0;
            this.btnPlayStyle.tbToolTip = "";
            this.btnPlayStyle.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnPlayStyle.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnPlayStyle.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPlayStyle.VisibleEx = true;
            this.btnPlayStyle.Click += new System.EventHandler(this.btnPlayStyle_Click);
            // 
            // proProgress
            // 
            this.proProgress.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.proProgress.BackColor = System.Drawing.Color.Transparent;
            this.proProgress.Font = new System.Drawing.Font("宋体", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.proProgress.Location = new System.Drawing.Point(301, 12);
            this.proProgress.Margin = new System.Windows.Forms.Padding(0);
            this.proProgress.Name = "proProgress";
            this.proProgress.Padding = new System.Windows.Forms.Padding(0, 5, 0, 5);
            this.proProgress.Size = new System.Drawing.Size(260, 18);
            this.proProgress.TabIndex = 18;
            this.proProgress.tbBackgroundImage = ((System.Drawing.Image)(resources.GetObject("proProgress.tbBackgroundImage")));
            this.proProgress.tbCanDragValue = true;
            this.proProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal;
            this.proProgress.tbDotImage = ((System.Drawing.Image)(resources.GetObject("proProgress.tbDotImage")));
            this.proProgress.tbDotImageState = iTong.Components.ImageState.ThreeState;
            this.proProgress.tbDotMouseDown = null;
            this.proProgress.tbDotMouseHover = null;
            this.proProgress.tbDotMouseLeave = null;
            this.proProgress.tbDownloadImage = ((System.Drawing.Image)(resources.GetObject("proProgress.tbDownloadImage")));
            this.proProgress.tbDownMax = 100;
            this.proProgress.tbDownMin = 0;
            this.proProgress.tbDownValue = 0;
            this.proProgress.tbIsWaiting = false;
            this.proProgress.tbPlayImage = ((System.Drawing.Image)(resources.GetObject("proProgress.tbPlayImage")));
            this.proProgress.tbPlayMax = 100;
            this.proProgress.tbPlayMin = 0;
            this.proProgress.tbPlayValue = 0D;
            this.proProgress.tbShowDot = false;
            this.proProgress.tbShowText = false;
            this.proProgress.tbSplit = "4,0,4,0";
            this.proProgress.Text = "TbControlBar1";
            this.proProgress.ProgressChanged += new System.EventHandler<iTong.Components.ProgressEventArgs>(this.proProgress_ProgressChanged);
            this.proProgress.ProgressChanging += new System.EventHandler<iTong.Components.ProgressEventArgs>(this.proProgress_ProgressChanging);
            // 
            // tbMusicPlayer
            //             
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(243)))));
            this.Controls.Add(this.btnPlay);
            this.Controls.Add(this.btnPrev);
            this.Controls.Add(this.lblTime);
            this.Controls.Add(this.btnNext);
            this.Controls.Add(this.btnVoice);
            this.Controls.Add(this.btnPlayStyle);
            this.Controls.Add(this.lblTitle);
            this.Controls.Add(this.proProgress);
            this.ForeColor = System.Drawing.Color.White;
            this.Name = "tbMusicPlayer";
            this.Size = new System.Drawing.Size(737, 42);
            this.cmsPlayStyle.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        internal iTong.Components.tbButton btnPlay;
        internal iTong.Components.tbButton btnPrev;
        internal System.Windows.Forms.Label lblTime;
        internal iTong.Components.tbButton btnNext;
        internal iTong.Components.tbContextMenuStrip cmsPlayStyle;
        internal System.Windows.Forms.ToolStripMenuItem tsmiSingle;
        internal System.Windows.Forms.ToolStripMenuItem tsmiOrder;
        internal System.Windows.Forms.ToolStripMenuItem tsmiLoop;
        internal System.Windows.Forms.ToolStripMenuItem tsmiRandom;
        internal iTong.Components.tbButton btnVoice;
        internal iTong.Components.tbButton btnPlayStyle;
        internal iTong.Components.tbLabel lblTitle;
        internal iTong.Components.tbControlBar proProgress;
    }
}
