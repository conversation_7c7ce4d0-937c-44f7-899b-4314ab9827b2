﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using iTong.CoreFoundation;
using iTong.Components;
using iTong.CoreModule;
using System.Threading;
using System.Runtime.InteropServices;
using System.Management;

namespace iVoiceMerge
{
    public partial class MainForm : tbBaseGuiForm
    {
        private List<string> mLstCurrentMusic = new List<string>();
        private tbDataGridViewEx dgvMusic = new tbDataGridViewEx();
        protected IPluginPlayer mPlayer;
        private Thread mTdMerge;
        private Thread mTdAddMusic;
        private Thread mTdCreateError;

        #region "--- 初始化 ---"

        public MainForm()
        {
            InitializeComponent();

            this.Size = new Size(980, 660);

            this.Icon = global::iVoiceMerge.Properties.Resources.main;

            Application.ThreadException += OnThreadException;
            AppDomain.CurrentDomain.UnhandledException += UnhandledException;

            this.Text = String.Format("语音合并助手 v{0} Beta测试版", System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString());

            this.SetViewStyle(MViewType.Welcome);

            InitDataGridView();
            InitLocalPlayerService();
            try
            {
                tbPlayer.tbTitle = "音乐";
                tbPlayer.tbArtist = this.Text;
                tbPlayer.tbLoopPlayText = "循环播放";
                tbPlayer.tbOrderPlayText = "顺序播放";
                tbPlayer.tbRandomPlayText = "随机播放";
                tbPlayer.tbSingleCycleText = "单曲循环";
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmMP3");
            }

        }

        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            try
            {
                string strVersion = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version.ToString();
                if (!ServerIniSetting.IsVoiceSynthesisSupportVersion(strVersion))
                {
                    tbMessageBox.Show(this, string.Format(ServerIniSetting.IsVoiceSynthesisSupportHint(), strVersion), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.StartToCheckUpdate();
                    this.Close();
                }
            }
            catch { }
            tmrTimer.Start();
        }

        private void InitDataGridView()
        {
            try
            {
                this.dgvMusic.SuspendLayout();
                this.dgvMusic.tbMouseHoverResponse = true;
                this.dgvMusic.AllowDrop = true;
                this.dgvMusic.RowTemplate.Height = 32;
                this.dgvMusic.BackColor = System.Drawing.Color.FromArgb(244, 244, 244);
                this.dgvMusic.tbNoDataText = "请点击“添加”按钮或将音频文件拖拽至此";
                this.dgvMusic.tbNoDataImage = global::iVoiceMerge.Properties.Resources.pic_muise;

                this.dgvMusic.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, true, false, DataGridViewContentAlignment.MiddleCenter, true, DataGridViewTriState.False));
                this.dgvMusic.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "Title", "名称", 190));
                this.dgvMusic.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewMediaColumn), "colPlayStatus", "", 20, true, false, DataGridViewContentAlignment.MiddleCenter, true, DataGridViewTriState.False));
                this.dgvMusic.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "Duration", "时间", 70, true, false, DataGridViewContentAlignment.BottomRight, true, DataGridViewTriState.False));
                this.dgvMusic.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "Kbps", "比特率", 80, true, false, DataGridViewContentAlignment.BottomRight, true, DataGridViewTriState.False));
                this.dgvMusic.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewTextBoxColumn), "Artist", "路径", 385, true, false, DataGridViewContentAlignment.MiddleLeft, true, DataGridViewTriState.False));
                this.dgvMusic.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(typeof(tbDataGridViewProgressColumn), "colProgress", "", 160, true, false, DataGridViewContentAlignment.MiddleLeft, true, DataGridViewTriState.False));

                this.dgvMusic.SelectionChanged += dgvMusic_SelectionChanged;
                this.dgvMusic.CellButtonClick += dgvMusic_CellButtonClick;
                this.dgvMusic.Sorted += dgvMusic_Sorted;

                this.dgvMusic.DragEnter += dgvMusic_DragEnter;
                this.dgvMusic.DragDrop += dgvMusic_DragDrop;


                this.panel1.Controls.Add(this.dgvMusic);
                this.dgvMusic.ResumeLayout();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "InitDataGridView");
            }
        }

        private void InitLocalPlayerService()
        {
            try
            {
                this.mPlayer = PluginPlayer.Instance();
                this.mPlayer.PlayMode = UserPlayMode.LoopPlay;
                tbPlayer.tbPlayMode = this.mPlayer.PlayMode;

                if (this.mPlayer.GetUserPlaylist(this.Text) == null)
                    this.mPlayer.AddUserPlaylist(this.Text);

                this.mPlayer.PlayStartBefore += OnPlayStartBeforeInFuncMusic;
                this.mPlayer.CurrentMediaChange += OnLocalMediaChanged;
                this.mPlayer.CurrentPositionChange += OnLocalProgressValueChanged;
                this.mPlayer.PlayStateChange += OnLocalPlayStateChange;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "InitLocalPlayerService");
            }
            //this.tbPlayer.Visible = this.mPlayer.CanPlay;
        }

        #endregion

        #region "--- 窗体事件 ---"

        private void dgvMusic_DragDrop(object sender, DragEventArgs e)
        {
            try
            {
                string[] arrMusic = (string[])e.Data.GetData(DataFormats.FileDrop);
                AddMusic(arrMusic);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "dgvMusic_DragDrop");
            }
        }

        private void dgvMusic_DragEnter(object sender, DragEventArgs e)
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
                e.Effect = DragDropEffects.All;
            else
                e.Effect = DragDropEffects.None;
        }

        private void btnStart_Click(object sender, EventArgs e)
        {
            this.SetViewStyle(MViewType.ViewList);
        }

        private void btnSuperior_Click(object sender, EventArgs e)
        {
            this.SetViewStyle(MViewType.Welcome);
        }

        private void OnLocalProgressValueChanged(object sender, MediaPlayPositionChangedEventArgs e)
        {
            try
            {
                if (this.mPlayer.CurrentMedia != null && this.mPlayer.CurrentMedia.durationString != "00:00" && this.mPlayer.PlayState != WMPPlayState.wmppsStopped && this.mPlayer.PlayState != WMPPlayState.wmppsReady)
                {
                    tbPlayer.tbPlayMax = (int)this.mPlayer.CurrentMedia.duration;
                    tbPlayer.tbPlayMin = 0;
                    tbPlayer.tbPlayValue = (int)this.mPlayer.CurrentPosition;
                    if (this.mPlayer.CurrentPosition >= this.mPlayer.CurrentMedia.duration - 1.5)
                        tbPlayer.tbPlayValue = (int)this.mPlayer.CurrentMedia.duration;

                    tbPlayer.tbPlayTime = String.Format("{0}/{1}", this.mPlayer.CurrentPositionString, this.mPlayer.CurrentMedia.durationString);
                }
                else
                {
                    tbPlayer.tbPlayMax = 100;
                    tbPlayer.tbPlayMin = 0;
                    tbPlayer.tbPlayValue = 0;
                    tbPlayer.tbPlayTime = "";
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "OnLocalProgressValueChanged");
            }
        }

        private void OnLocalMediaChanged(object sender, MediaChangedEventArgs e)
        {
            try
            {
                MusicInfo info = this.mPlayer.CurrentMp3Info;

                if (info != null)
                {
                    tbPlayer.tbArtist = info.Artist;
                    tbPlayer.tbTitle = info.Title;
                }
                else
                {
                    if (this.mPlayer.CurrentMp3InfoList.Count > 0 && this.mPlayer.CurrentMediaIndex < this.mPlayer.CurrentMp3InfoList.Count)
                    {
                        info = this.mPlayer.CurrentMp3InfoList[this.mPlayer.CurrentMediaIndex];
                        if (info != null)
                        {
                            tbPlayer.tbArtist = info.Artist;
                            tbPlayer.tbTitle = info.Title;
                        }
                    }
                }
                tbPlayer.tbPlaySource = this.mPlayer.PlaySource;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "OnLocalMediaChanged");
            }
        }

        private void OnPlayStartBeforeInFuncMusic(object sender, MediaUrlEventArgs e)
        {
            try
            {
                //加载完后就把对象清空，所以需要重新赋值
                if (this.mPlayer.PlaySource == PlaySource.Local && File.Exists(e.PlayUrl))
                    this.mPlayer.CurrentMp3Info = new MusicInfo(e.PlayUrl);
                else
                    this.mPlayer.PlayNext();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "OnPlayStartBeforeInFuncMusic");
            }
        }

        private void dgvMusic_Sorted(object sender, EventArgs e)
        {
            this.ChangeRowState();
        }

        private void dgvMusic_SelectionChanged(object sender, EventArgs e)
        {
            this.ChangeHint();
        }


        private void dgvMusic_CellButtonClick(object sender, CellButtonEventArgs e)
        {
            DataGridViewRow row = this.dgvMusic.Rows[e.Cell.RowIndex];
            switch (e.CellButton)
            {
                case CellButtonStyle.Backup://上移
                    this.Displacement(-1, e.Cell.RowIndex);
                    break;

                case CellButtonStyle.Uninstall://下移
                    this.Displacement(1, e.Cell.RowIndex);
                    break;

                case CellButtonStyle.Document://目录
                    if (row.Tag != null && !string.IsNullOrEmpty(row.Tag.ToString()) && File.Exists(row.Tag.ToString()))
                        Common.OpenExplorerEx(row.Tag.ToString());
                    //this.mLstCurrentMusic.Remove(row.Tag.ToString());
                    //this.dgvMusic.Rows.Remove(row);
                    //this.mPlayer.GetUserPlaylist(this.Text).Remove(row.Tag.ToString());
                    //this.ChangeRowState();
                    break;

                case CellButtonStyle.None://播放
                    //if (this.mPlayer.CanPlay)
                    //this.PlayMusic(e);
                    //else
                    try
                    {
                        System.Diagnostics.Process.Start(row.Tag.ToString());
                    }
                    catch (Win32Exception ex)
                    {
                        if (ex.Message.ToString().Contains("系统找不到指定的文件"))
                            Common.OpenExplorerEx(row.Tag.ToString());
                    }
                    break;

                case CellButtonStyle.Paused:
                case CellButtonStyle.Playing://暂停
                    this.PlayMusic(e);
                    break;
                default:
                    break;
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            this.mAddMenu.Show(this.btnAdd, new Point(0, this.btnAdd.Bottom));
        }

        private void tsmiAddFile_Click(object sender, EventArgs e)
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Filter = "MP3文件|*.mp3";// "MP3(*.mp3)|*.mp3|WMA(*.wma)|*.wma|WAV(*.wav)|*.wav|M4A(*.m4a)|*.m4a|AAC(*.aac)|*.aac|FLAC(*.flac)|*.flac";
                    openFileDialog.Multiselect = true;
                    if (openFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                    {
                        string[] arrMusic = openFileDialog.FileNames;
                        AddMusic(arrMusic);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tsmiAddFile_Click");
            }
        }

        private void tsmiAddFolder_Click(object sender, EventArgs e)
        {
            try
            {
                using (FolderBrowserDialog folderBrowserDialog = new FolderBrowserDialog())
                {
                    folderBrowserDialog.Description = "选择音频文件所在的目录";
                    folderBrowserDialog.ShowNewFolderButton = true;
                    if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
                    {

                        if (Directory.Exists(folderBrowserDialog.SelectedPath))
                        {
                            List<string> lstFiles = new List<string>();
                            lstFiles.AddRange(FileSystem.GetFiles(folderBrowserDialog.SelectedPath, SearchOption.AllDirectories, "*.mp3"));

                            if (lstFiles.Count == 0)
                            {
                                tbMessageBox.Show(this, "您添加的文件夹中没有MP3格式的文件。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                return;
                            }
                            AddMusic(lstFiles.ToArray());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tsmiAddFolder_Click");
            }
        }


        private void btnDelete_Click(object sender, EventArgs e)
        {
            List<DataGridViewRow> lstDelete = this.dgvMusic.SelectedRows;
            if (lstDelete.Count == 0)
            {
                tbMessageBox.Show(this, "请选择要删除的文件！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            else if (lstDelete.Count > 0 && tbMessageBox.Show(this, "确定删除选定文件？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) != System.Windows.Forms.DialogResult.OK)
                return;

            foreach (DataGridViewRow item in lstDelete)
            {
                this.mLstCurrentMusic.Remove(item.Tag.ToString());
                this.dgvMusic.Rows.Remove(item);
                this.mPlayer.GetUserPlaylist(this.Text).Remove(item.Tag.ToString());
                this.ChangeRowState();
            }
            this.ChangeHint();
        }

        private void btnCreate_Click(object sender, EventArgs e)
        {
            try
            {
                List<DataGridViewRow> lstMusic = this.dgvMusic.SelectedRows;
                lstMusic.Sort(new DataGridViewRowIndexComparer(SortType.DESC));
                if (lstMusic.Count == 0)
                {
                    tbMessageBox.Show(this, "请选择需要合并的文件。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                else if (lstMusic.Count == 1)
                {
                    tbMessageBox.Show(this, "合并的文件最少需要2个及以上。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                else if (lstMusic.Count > 100)
                {
                    tbMessageBox.Show(this, "对不起，目前最多仅支持合并100个文件。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Title = "生成文件保存路径";
                sfd.Filter = "MP3文件|*.mp3";
                sfd.FileName = string.Format("语音合并{0}", DateTime.Now.ToString("yyyyMMddHHmmss"));
                if (sfd.ShowDialog() != DialogResult.OK)
                    return;
                frmMergeProgress.Show(this);

                string strSavePath = sfd.FileName;
                File.Delete(sfd.FileName);


                if (this.mTdMerge != null && this.mTdMerge.ThreadState != System.Threading.ThreadState.Stopped)
                    return;

                this.mTdMerge = new Thread(new ParameterizedThreadStart(DoMerge));
                this.mTdMerge.IsBackground = false;
                this.mTdMerge.SetApartmentState(ApartmentState.STA);
                this.mTdMerge.Start(new object[] { lstMusic, sfd.FileName });
                frmMergeProgress.SetProgress("开始合并请耐心等待", "", "", -1);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "btnCreate_Click");
            }

        }

        private void btnContactEx_Click(object sender, EventArgs e)
        {
            Common.OpenExplorer("https://jq.qq.com/?_wv=1027&k=5XVxPw2");
        }

        private void tsmiCreateError_Click(object sender, EventArgs e)
        {
            if (tbMessageBox.Show(this, "您确定要生成错误日志吗？", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
                this.CreateErrorZip();
        }

        private void tsmiExist_Click(object sender, EventArgs e)
        {
            try
            {
                Application.ThreadException -= OnThreadException;
                this.Close();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tsmiExist_Click");
            }
        }

        #endregion

        private void Displacement(int intMoveUp, int selectedRowIndex)
        {
            try
            {
                intMoveUp = selectedRowIndex + intMoveUp;
                DataGridViewRow newRow = this.dgvMusic.Rows[selectedRowIndex];
                this.dgvMusic.Rows.Remove(this.dgvMusic.Rows[selectedRowIndex]);
                this.dgvMusic.Rows.Insert(intMoveUp, newRow);
                this.dgvMusic.ClearSelection();
                this.dgvMusic.Rows[intMoveUp].Selected = true;
                this.ChangeRowState();
            }
            catch { }
        }

        private void ChangeRowState()
        {
            Playlist pl = this.mPlayer.GetUserPlaylist(this.Text);
            foreach (tbDataGridViewRow row in this.dgvMusic.Rows)
            {
                tbDataGridViewProgressCellEx cellProgressEx = (tbDataGridViewProgressCellEx)(row.Cells["colProgress"]);
                if (row.Index == 0 && row.Index == this.dgvMusic.Rows.Count - 1)
                    cellProgressEx.tbCellButtonEnable = CellButtonStyle.Document;
                else if (row.Index == 0)
                    cellProgressEx.tbCellButtonEnable = CellButtonStyle.Uninstall | CellButtonStyle.Document;
                else if (row.Index == this.dgvMusic.Rows.Count - 1)
                    cellProgressEx.tbCellButtonEnable = CellButtonStyle.Backup | CellButtonStyle.Document;
                else
                    cellProgressEx.tbCellButtonEnable = CellButtonStyle.Backup | CellButtonStyle.Uninstall | CellButtonStyle.Document;

                pl.SetItem(row.Index, row.Tag.ToString());
            }
        }

        private void AddMusic(string[] arrMusic)
        {
            if (arrMusic.Length == 0)
                return;
            if (this.mTdAddMusic != null && this.mTdAddMusic.ThreadState != System.Threading.ThreadState.Stopped)
                return;

            this.mTdAddMusic = new Thread(new ParameterizedThreadStart(DoAddMusic));
            this.mTdAddMusic.IsBackground = false;
            this.mTdAddMusic.SetApartmentState(ApartmentState.STA);
            this.mTdAddMusic.Start(arrMusic);
            frmMergeProgress.Show(this);
            frmMergeProgress.SetProgress("添加音频中，请耐心等待【目前仅支持MP3格式，非MP3直接过滤！】", "", "", -1);
            frmMergeProgress.DoCancelEventHandler -= frmMergeProgress_DoCancelEventHandler;
            frmMergeProgress.DoCancelEventHandler += frmMergeProgress_DoCancelEventHandler;
        }

        private void frmMergeProgress_DoCancelEventHandler(object sender, EventArgs e)
        {
            try
            {
                if (this.mTdAddMusic != null && this.mTdAddMusic.ThreadState != System.Threading.ThreadState.Stopped)
                {
                    this.mTdAddMusic.Abort();
                    this.mTdAddMusic = null;
                }
            }
            catch { }

            try
            {
                if (this.mTdMerge != null && this.mTdMerge.ThreadState != System.Threading.ThreadState.Stopped)
                {
                    this.mTdMerge.Abort();
                    this.mTdMerge = null;
                }
            }
            catch { }
        }

        private void DoAddMusic(object obj)
        {
            string strMsg = "";
            try
            {
                string[] arrMusic = (string[])obj;
                StringBuilder sbMsg = new StringBuilder();
                int intSucceed = 0;
                int intFailure = 0;
                decimal intIndex = 0;
                decimal intCount = Convert.ToDecimal(arrMusic.Length);

                foreach (string item in arrMusic)
                {
                    intIndex++;
                    decimal dProgress = intIndex / intCount;
                    int intProgress = (int)(Math.Round(dProgress, 2) * 100);
                    if (!item.ToLower().EndsWith(".mp3"))
                    {
                        sbMsg.AppendLine(string.Format("非MP3格式【{0}】", item));
                        intFailure++;
                        frmMergeProgress.SetProgress("添加音频中，请耐心等待", string.Format("非MP3格式【{0}】", item), "", intProgress);
                    }
                    //else if (!this.mLstCurrentMusic.Contains(item))
                    //{
                    frmMergeProgress.SetProgress("添加音频中，请耐心等待【目前仅支持MP3格式，非MP3直接过滤！】", item, "", intProgress);
                    AddRow(item);
                    intSucceed++;

                    //}
                    //else
                    //{
                    //    sbMsg.AppendLine(string.Format("已存在【{0}】", item));
                    //    intFailure++;
                    //}
                    this.ChangeRowState();
                    this.ChangeHint();
                }

                //if (intFailure > 0)
                //    strMsg = "您总共选择了【" + arrMusic.Length + "】个文件，成功添加【{0}】个 添加失败【{1}】个\n\r" + sbMsg.ToString();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoAddMusic");
            }
            frmMergeProgress.SetProgress("添加音频完成【目前仅支持MP3格式，非MP3直接过滤！】", strMsg, "", 100);
            frmMergeProgress.Close(this);
        }

        private delegate void ChangeHintHandler();
        private void ChangeHint()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new ChangeHintHandler(ChangeHint));
            }
            else
            {
                this.lblHint.Text = string.Format("总共：{0} 已选：{1}", this.mLstCurrentMusic.Count, this.dgvMusic.SelectedRows.Count);
            }
        }

        private delegate void AddRowHandler(string strPath);
        private void AddRow(string strPath)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new AddRowHandler(AddRow), strPath);
            }
            else
            {
                try
                {
                    tbDataGridViewRow row = new tbDataGridViewRow();
                    Mp3Info minfo = new Mp3Info(strPath);
                    row.Height = 40;

                    row.CreateCells(this.dgvMusic, false, Path.GetFileName(strPath), true, Utility.FormatDuration((long)minfo.MpegInfo.Duration), Utility.FormatMediaBps(minfo.BitRate), strPath, "");
                    this.dgvMusic.Rows.Add(row);
                    row.Tag = strPath;
                    this.mLstCurrentMusic.Add(strPath);

                    tbDataGridViewProgressCellEx cellProgressEx = (tbDataGridViewProgressCellEx)(row.Cells["colProgress"]);
                    cellProgressEx.tbProgressStyle = ProgressStyle.Button;

                    cellProgressEx.tbTextBackup = "上移";
                    cellProgressEx.tbTextUnInstall = "下移";
                    cellProgressEx.tbTextReInstall = "目录";

                    cellProgressEx.tbPadding = new Padding(40, 0, 0, 0);
                    cellProgressEx.tbProgressStyle = ProgressStyle.Button;
                    cellProgressEx.tbShowButtonText = false;
                    cellProgressEx.tbButtonSize = new Size(26, 26);

                    cellProgressEx.tbButtonImgFirst = global::iVoiceMerge.Properties.Resources.btn_4_moveup;
                    cellProgressEx.tbButtonImgSecond = global::iVoiceMerge.Properties.Resources.btn_4_movedown;
                    cellProgressEx.tbButtonImgThird = global::iVoiceMerge.Properties.Resources.btn_4_openfile;

                    cellProgressEx.tbShowButtonThird = true;
                    row.Tag = strPath;

                    if (this.mPlayer.CanPlay)
                        this.mPlayer.GetUserPlaylist(this.Text).Insert(row.Index, strPath);
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "AddRow");
                }
            }
        }

        private void DoMerge(object obj)
        {
            try
            {
                object[] arrObj = (object[])obj;

                List<DataGridViewRow> lstMusic = (List<DataGridViewRow>)arrObj[0];
                string strSavePath = arrObj[1].ToString();

                string stringbit = "";
                foreach (DataGridViewRow item in lstMusic)
                {
                    if (string.IsNullOrEmpty(stringbit))
                        stringbit = item.Cells["Kbps"].Value.ToString();
                    else if (stringbit != item.Cells["Kbps"].Value.ToString())
                    {
                        if (tbMessageBox.Show(this, "当前选中的文件存在比特率值不一样，有可能导致合并的文件缺失数据，是否继续？", "提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == System.Windows.Forms.DialogResult.Yes)
                            break;
                        else
                            goto Do_Exit;
                    }
                }

                string arguments = "-i concat:\"{0}\" -acodec copy \"{1}\" -y";
                string strTemp = "";
                //foreach (DataGridViewRow row in lstMusic)
                for (int i = lstMusic.Count - 1; i >= 0; i--)
                {
                    DataGridViewRow row = lstMusic[i];
                    if (row.Cells["Artist"].Value.ToString().Length > 0)
                        strTemp += row.Cells["Artist"].Value.ToString() + "|";
                }
                strTemp = strTemp.TrimEnd('|');
                string strExePath = Path.Combine(Application.StartupPath, @"IncludeDlls\ffmpeg.exe");
                arguments = string.Format(arguments, strTemp, strSavePath);

                Process p = new Process();
                p.StartInfo.FileName = strExePath;
                p.StartInfo.Arguments = arguments;
                p.StartInfo.UseShellExecute = false;
                p.StartInfo.RedirectStandardError = true;
                p.StartInfo.CreateNoWindow = true;
                p.ErrorDataReceived += new DataReceivedEventHandler(Output);
                p.Start();
                p.BeginErrorReadLine();
                p.WaitForExit();
                p.Close();
                p.Dispose();
                frmMergeProgress.SetProgress("合并完成", "", "", -1);
                Common.OpenExplorerEx(strSavePath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoMerge");
            }
        Do_Exit:
            frmMergeProgress.Close(this);
        }

        private void Output(object sendProcess, DataReceivedEventArgs output)
        {
            Process p = sendProcess as Process;
            if (p.HasExited && p.ExitCode == 1) // 在ffmpeg发生错误的时候才输出信息
            {
                Common.LogException(output.Data, "Output");
            }
        }

        #region "--- 播放器 ---"

        private void PlayMusic(int index, bool isReplay = true)
        {
            try
            {
                if (this.mPlayer != null && this.mPlayer.CurrentUserPlaylist != null)
                {
                    if (this.mPlayer.CurrentUserPlaylist.Name != this.Text)
                        this.mPlayer.CurrentUserPlaylist = this.mPlayer.GetUserPlaylist(this.Text);

                    string strUrl = this.mPlayer.CurrentUserPlaylist.Urls[index];

                    if (!CheckCanPlay(strUrl))
                    {
                        tbMessageBox.Show(this, string.Format("很抱歉，我们当前不支持播放 {0} 格式的音乐！", Path.GetExtension(strUrl).TrimStart('.').ToUpper()), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    this.mPlayer.PlaySource = PlaySource.Local;
                    if (isReplay)
                        this.mPlayer.Play(index);
                    else
                        this.mPlayer.Play();
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "PlayMusic");
            }
        }

        private bool CheckCanPlay(string strUrl)
        {
            bool blnResult = true;

            if (!strUrl.EndsWith(".mp3", StringComparison.OrdinalIgnoreCase) && System.Environment.OSVersion.Version < new Version("6.1"))
                blnResult = false;

            return blnResult;
        }

        private void PlayMusic(CellButtonEventArgs e)
        {
            try
            {
                if (e.Cell is tbDataGridViewMediaCell)
                {
                    tbDataGridViewMediaCell cell = (tbDataGridViewMediaCell)e.Cell;
                    if (cell.tbCellStatus == CellButtonStyle.Playing)
                    {
                        cell.tbCellStatus = CellButtonStyle.Paused;
                        this.mPlayer.Pause();
                    }
                    else if (cell.tbCellStatus == CellButtonStyle.FileNoExist)
                        // Me.mPlayer.Pause()
                        return;
                    else
                    {
                        bool isReplay = true;
                        if (cell.tbCellStatus == CellButtonStyle.Paused)
                            isReplay = false;
                        PlayMusic(cell.RowIndex, isReplay);
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "PlayMusic");
            }
        }

        private void OnLocalPlayStateChange(object sender, MediaPlayStateChangedEventArgs e)
        {
            try
            {
                if (e.NewPlayState == WMPPlayState.wmppsPlaying)
                {
                    tbPlayer.tbButtonState = PlayStatus.Play;
                    if (this.mPlayer.CurrentMp3Info != null)
                        SetPlayStatus(this.mPlayer.CurrentMp3Info.FilePath, CellButtonStyle.Playing);
                }
                else
                {
                    tbPlayer.tbButtonState = PlayStatus.Ready;
                    if (this.mPlayer.CurrentMp3Info != null)
                        SetPlayStatus(this.mPlayer.CurrentMp3Info.FilePath, CellButtonStyle.Paused);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "OnLocalPlayStateChange");
            }
        }

        public void SetPlayStatus(string strFile, CellButtonStyle state)
        {
            try
            {
                this.dgvMusic.SuspendLayout();
                foreach (DataGridViewRow row in this.dgvMusic.Rows)
                {
                    tbDataGridViewMediaCell cell = (tbDataGridViewMediaCell)row.Cells["colPlayStatus"];
                    if (row.Tag.ToString() == strFile)
                        cell.tbCellStatus = state;
                    else if (cell.tbCellStatus != CellButtonStyle.FileNoExist)
                        cell.tbCellStatus = CellButtonStyle.None;
                }
                this.dgvMusic.Invalidate();
                this.dgvMusic.ResumeLayout();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SetPlayStatus");
            }
        }

        private void tbPlayer_PlayModeChanged(System.Object sender, PlayModeEventArgs e)
        {
            if (this.mPlayer != null)
            {
                this.mPlayer.PlayMode = this.tbPlayer.tbPlayMode;
            }
        }

        private void tbPlayer_PlayValueChanged(System.Object sender, iTong.Components.ProgressEventArgs e)
        {
            if (CheckMediaCanPlay(true))
            {
                if (this.mPlayer.PlayState == WMPPlayState.wmppsBuffering || this.mPlayer.PlayState == WMPPlayState.wmppsPaused || this.mPlayer.PlayState == WMPPlayState.wmppsPlaying)
                {
                    this.mPlayer.CurrentPosition = e.ProgressValue;
                    Application.DoEvents();
                }
            }
        }

        private void tbPlayer_VoiceValueChanged(System.Object sender, iTong.Components.ProgressEventArgs e)
        {
            if (CheckMediaCanPlay(true))
                this.mPlayer.Settings.volume = e.ProgressValue;
        }

        private void tbPlayer_Prev(System.Object sender, System.EventArgs e)
        {
            if (CheckMediaCanPlay(true))
                this.mPlayer.Previous();
        }

        private void tbPlayer_Next(System.Object sender, System.EventArgs e)
        {
            if (CheckMediaCanPlay(true))
                this.mPlayer.Next();
        }

        private void tbPlayer_Play(System.Object sender, System.EventArgs e)
        {
            if (CheckMediaCanPlay(true))
            {
                string strMp3Path = "";
                if (this.mPlayer.CurrentMp3Info == null)
                {
                    DataGridViewRow row;
                    if (this.dgvMusic.SelectedRows.Count > 0)
                    {
                        row = this.dgvMusic.SelectedRows[0];
                        strMp3Path = row.Tag.ToString();
                        this.PlayMusic(row.Index, true);
                    }
                }
                else
                {
                    this.mPlayer.Play();
                    strMp3Path = this.mPlayer.CurrentMp3Info.FilePath;
                }

                if (this.mPlayer.PlayState != WMPPlayState.wmppsPlaying)
                    this.tbPlayer.tbButtonState = PlayStatus.Ready;
            }
        }

        private void tbPlayer_Pause(System.Object sender, System.EventArgs e)
        {
            if (CheckMediaCanPlay(true))
                this.mPlayer.Pause();
        }

        private bool CheckMediaCanPlay(bool isShow)
        {
            bool result = this.mPlayer.CanPlay;
            if (!result && isShow)
                tbMessageBox.Show(this, "很抱歉，音乐播放器启动失败！请确认您的操作系统已经安装了Windows Media Player。\r\n如果您的操作系统是Win7，可以进入 控制面板->程序和功能->打开或关闭Windows功能，将 媒体功能 选项打勾，\r\n然后重新打开程序。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);

            return result;
        }

        #endregion

        #region "--- 错误处理 ---"

        //************************************************************
        //**
        //** 名    称：OnThreadException
        //** 功能描述：处理主线程中未处理的异常
        //** 备    注：当主线程出现异常，并且没有try Catch，会捕获到异常，程序不会崩溃。
        //** 参    数：ByVal sender As Object
        //**           ByVal e As Threading.ThreadExceptionEventArgs
        //** 返 回 值：
        //** 全局变量：
        //** 调用模块：
        //** 版本历史：
        //**
        //************************************************************
        private void OnThreadException(object sender, ThreadExceptionEventArgs e)
        {
            string strException = e.Exception.ToString();
            Common.LogException(strException, "OnThreadException");
        }

        //************************************************************
        //**
        //** 名    称：UnhandledException
        //** 功能描述：处理非主线程中的未处理的异常
        //** 备    注：当非主线程出现异常，并且没有try Catch，会捕获到异常，程序直接崩溃。
        //** 参    数：ByVal sender As Object
        //**           ByVal e As System.UnhandledExceptionEventArgs
        //** 返 回 值：
        //** 全局变量：
        //** 调用模块：
        //** 版本历史：
        //**
        //************************************************************
        private void UnhandledException(object sender, System.UnhandledExceptionEventArgs e)
        {
            //Common.LogException(e.ExceptionObject.ToString(), "UnhandledException")

            string strException = e.ExceptionObject.ToString();
            Common.LogException(strException, "UnhandledException");

        }

        #endregion

        private delegate void SetViewStyleHandler(MViewType style);
        private void SetViewStyle(MViewType style)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new SetViewStyleHandler(SetViewStyle), style);
            }
            else
            {
                try
                {
                    this.tblayoutMain.SuspendLayout();
                    switch (style)
                    {
                        case MViewType.Welcome:
                            if (this.tblayoutMain.ColumnStyles[0].Width != 100)
                            {
                                this.tblayoutMain.ColumnStyles[0].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[0].Width = 100;
                                this.tblayoutMain.ColumnStyles[1].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[1].Width = 0;

                                this.pnlWelcome.Visible = true;
                                this.pnlViewList.Visible = false;
                            }
                            break;

                        case MViewType.ViewList:
                            if (this.tblayoutMain.ColumnStyles[1].Width != 100)
                            {
                                this.tblayoutMain.ColumnStyles[0].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[0].Width = 0;
                                this.tblayoutMain.ColumnStyles[1].SizeType = SizeType.Percent;
                                this.tblayoutMain.ColumnStyles[1].Width = 100;

                                this.pnlWelcome.Visible = false;
                                this.pnlViewList.Visible = true;

                            }
                            break;
                    }
                    this.tblayoutMain.ResumeLayout();
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "SetViewStyle");
                }
                Application.DoEvents();
            }
        }

        #region "--- 检测升级 ---"

        private void tmrTimer_Tick(object sender, EventArgs e)
        {
            _lngPassTime += 1;
            if (_lngPassTime == 1)
            {//检查是否有上次没有升级的升级包(_FirstRunFromUpgrade在验证升级成功会进行操作一定在放在一秒之前)
                this.CheckUpgradeFileExist();
                if (!this._FirstRunFromUpgrade)
                {
                    return;
                }

                try
                {
                    string strFileTag = Path.Combine(Application.StartupPath.TrimEnd('\\'), "OpenOK.dll");

                    if (!File.Exists(strFileTag))
                    {
                        FileStream fileStream = File.Create(strFileTag);
                        fileStream.Close();
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "OpenOKException");
                }

            }
            else if (_lngPassTime == 3)
            {
                //下载ServerSettings.ini
                DownloadServerSetting();
            }
            else if (_lngPassTime == 20)
            {
                StartToSendData();
            }
            else if (_lngPassTime == 2 || _lngPassTime % 3600 == 0)
            {
                //程序开启时已启动检测升级，然后每隔一小时进行检查软件升级
                StartToCheckUpdate();
            }

        }

        private void DownloadServerSetting()
        {
            Thread thd = new Thread(DoDownloadServerSetting);
            thd.IsBackground = true;
            thd.Start();


        }

        private void DoDownloadServerSetting()
        {
            try
            {
                string strTempPath = Folder.ConfigServerIniFile + "temp";
                string strContent = Utility.GetContentStringFromUrl("http://t.tongbu.com/iVoiceMerge/ServerSettings.aspx", Encoding.UTF8, 20000);

                using (StreamWriter sWriter = new StreamWriter(strTempPath, false, Encoding.Default))
                {
                    sWriter.Write(strContent);
                }
                if (File.Exists(strTempPath))
                {
                    try
                    {
                        File.Delete(Folder.ConfigServerIniFile);
                    }
                    catch { }
                    try
                    {
                        File.Move(strTempPath, Folder.ConfigServerIniFile);
                    }
                    catch { }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoDownloadServerSetting");
            }
        }



        private string _FolderUpgrade = "";
        private string _strUpdatePackageFile = "";
        private DateTime _SoftStartTime = DateTime.MinValue;
        private string _strVersion = "";
        private double _lngPassTime = 0;
        private bool _FirstRunFromUpgrade = false;     //是否是升级后第一次运行

        private Thread _CheckEnviromentThread = null;
        private void CheckUpgradFile()
        {
            this._strUpdatePackageFile = Path.Combine(Folder.AppFolder, "Upgrade.dat");
            this._FolderUpgrade = Path.Combine(Path.GetTempPath(), String.Format("{0}Upgrade", Folder.AppType.ToString()));
        }

        private void CheckUpgradeFileExist()
        {
            string strPathUpgradeSuceed = Path.Combine(Folder.AppFolder, "UpgradeSuceed.dll");

            this.CheckUpgradFile();

            bool blnNewIncrease = true;

            if (File.Exists(this._strUpdatePackageFile))
            {
                //助手升级增量更新时，不需要删除tbUpgrade目录
                if (blnNewIncrease == false)
                {
                    try
                    {
                        //删除临时目录中的tbUpgrade目录,以免以前的升级程序失败后会遗留文件
                        if (Directory.Exists(this._FolderUpgrade))
                        {
                            Directory.Delete(this._FolderUpgrade, true);
                        }
                    }
                    catch { }
                }

                if (File.Exists(strPathUpgradeSuceed))
                {
                    this._FirstRunFromUpgrade = true;

                    //升级成功后清理升级产生的临时文件
                    try
                    {
                        File.Delete(strPathUpgradeSuceed);
                    }
                    catch { }

                    try
                    {
                        File.Delete(_strUpdatePackageFile);
                    }
                    catch { }

                }
                else
                {
                    if (!(Folder.LangType == LanguageType.en_US))
                    {
                        //助手升级增量更新时，每个下载的文件都会先比较md5，所以这边不需要再比较
                        if (blnNewIncrease == false)
                        {
                            //如果包不完整则删除
                            string strServerFileLength = IniClass.GetIniSectionKey("Setting", "UpdateFileLength", Folder.ConfigIniFile);
                            if (!this.CheckUpdateFileIsFull(this._strUpdatePackageFile, strServerFileLength))
                            {
                                try
                                {
                                    File.Delete(_strUpdatePackageFile);
                                }
                                catch { }
                                return;
                            }
                        }
                        else if (!Directory.Exists(this._FolderUpgrade))
                        {
                            return;
                        }

                        //升级文件已经下载完毕，但是用户没有立刻升级，所以启动的时候需要程序自动升级
                        LiveUpdateHelper.StartLiveUpdateExe(this._strUpdatePackageFile, this._FolderUpgrade, Application.ExecutablePath, "");
                    }
                }
            }
        }

        public void StartToCheckUpdate(bool blnUserCheckUpgrade = false, bool blnClick = false)
        {
            //新升级逻辑（国内版）
            if (Folder.LangType != LanguageType.vi_VN)
            {
                string strAppName = "语音合并助手";
                HelperExeManager.CheckUpdate(blnUserCheckUpgrade, strAppName, Application.ExecutablePath);
                return;
            }

        }

        private int _CheckUpgradeShowIcon = 1;
        private void tmrUpgrade_Tick(System.Object sender, System.EventArgs e)
        {
            if (Folder.LangType == LanguageType.en_US)
            {
                this.tmrUpgrade.Stop();
                return;
            }

            //If Me._CheckUpgrade Then
            if (this._CheckUpgradeShowIcon == 1)
            {
                this.mNotifyIcon.Icon = global::iVoiceMerge.Properties.Resources.main;
                this._CheckUpgradeShowIcon = 2;

            }
            else if (this._CheckUpgradeShowIcon == 2)
            {
                this.mNotifyIcon.Icon = global::iVoiceMerge.Properties.Resources.main;
                this._CheckUpgradeShowIcon = 3;

            }
            else if (this._CheckUpgradeShowIcon == 3)
            {
                this.mNotifyIcon.Icon = global::iVoiceMerge.Properties.Resources.main;
                this._CheckUpgradeShowIcon = 4;

            }
            else if (this._CheckUpgradeShowIcon == 4)
            {
                this.mNotifyIcon.Icon = global::iVoiceMerge.Properties.Resources.main;
                this._CheckUpgradeShowIcon = 1;
            }
        }

        private bool CheckUpdateFileIsFull(string strUpdateFile, string strServerFileLength)
        {
            bool blnFull = false;

            if (File.Exists(strUpdateFile))
            {
                try
                {
                    long lngFileLength = new FileInfo(strUpdateFile).Length;
                    if (string.Compare(lngFileLength.ToString(), strServerFileLength) == 0)
                    {
                        blnFull = true;
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "CheckUpdateFileIsFull");
                }
            }

            return blnFull;
        }

        #endregion

        #region "--- 收集数据 ---"

        private bool mSendingData = false;
        private bool mCancelSendData = false;
        //当前收集的软件信息在文件中保存的id
        private string _UserIDInSoftInfo = "-1";
        private tbDeviceInfo _infoDevice = new tbDeviceInfo();
        private void StartToSendData()
        {
            try
            {
                Common.Log("1.StartToSendData");
                ThreadPool.QueueUserWorkItem(new WaitCallback(DoSendData));
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "StartToSendData");
            }

        }

        private void DoSendData(object obj)
        {
            if (this.mSendingData)
            {
                return;
            }
            this.mSendingData = true;

            try
            {
                Common.Log("1. 发送之前收集的软件信息");
                //1. 发送之前收集的软件信息
                this.SendSoftDataPrevious();

                Common.Log("2. 收集当前的软件信息");
                //2. 收集当前的软件信息
                tbVersionInfo info = this.CollectSoftData();

                Common.Log("3发送目前收集的软件信息");
                //3 发送目前收集的软件信息
                this.SendSoftDataNow(info);

                Common.Log("3. 发送收集到的设备信息");
                //3. 发送收集到的设备信息
                //this.Start2SendDeviceData();

                //4. 发送已经安装的软件信息
                //tbDeviceCache.GetInstanse().SendInstalledSoft()

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoSendData_Main");
            }

            this.mSendingData = false;
            //'收集framwork版本号
            //ActionCollectHelper.FramworkVersion()
        }

        private tbVersionInfo CollectSoftData()
        {
            tbVersionInfo info = new tbVersionInfo();

            try
            {
                tbVersionInfoCache tbVersionCache = tbVersionInfoCache.GetInstanse();
                info.Key = Guid.NewGuid().ToString("N");
                //保存当前软件信息在缓存文件中的key
                this._UserIDInSoftInfo = info.Key;
                info.mac = Common.GetMacAddress();
                info.Version = Common.GetSoftVersion();
                info.osType = this.GetOsTypeRingtone();
                Common.Log("ostype: " + info.osType);
                //  info.uid = PluginLogin.Instance.Uid;

                info.startTime = DateTime.Now;
                int Soft91Status = info.Soft91Status;
                int.TryParse(UtilityEx.GetSoftInstallStatus().ToString(), out Soft91Status);
                info.Soft91Status = Soft91Status;

                Common.Log("State: " + Convert.ToInt32(info.Soft91Status).ToString());
                int runTimes = info.runTimes;
                string FunctionMapping = info.FunctionMapping;
                tbVersionCache.GetLastSoftUseState(ref  runTimes, ref FunctionMapping);
                info.runTimes = runTimes;
                info.FunctionMapping = FunctionMapping;

                //tbVersionCache.AddCacheData(info)
                //tbVersionCache.Save()
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CollectSoftData");
            }

            return info;
        }

        private string GetOsTypeRingtone()
        {
            string OsTypeResult = "";
            try
            {
                string osBit = "未知";
                if (Folder.AppType == RunType.AirDroid)
                {
                    osBit = "unknow";
                }

                if (Common.IsOS_Of_64Bit())
                {
                    osBit = "64位";
                    if (Folder.AppType == RunType.AirDroid)
                    {
                        osBit = "64 bit";
                    }
                }
                else
                {
                    osBit = "32位";
                    if (Folder.AppType == RunType.AirDroid)
                    {
                        osBit = "32 bit";
                    }
                }

                string osType = "";
                try
                {
                    Microsoft.Win32.RegistryKey rk = Microsoft.Win32.Registry.LocalMachine.OpenSubKey("Software\\Microsoft\\Windows NT\\CurrentVersion");

                    object pname = rk.GetValue("ProductName");
                    if (pname != null)
                    {
                        osType = pname.ToString();
                    }
                }
                catch
                {
                }

                //获取总物理内存大小  
                long capacity = 0;
                ManagementClass cimobject1 = new ManagementClass("Win32_PhysicalMemory");
                ManagementObjectCollection moc1 = cimobject1.GetInstances();
                foreach (ManagementObject mo1 in moc1)
                {
                    capacity += long.Parse(mo1.Properties["Capacity"].Value.ToString());
                }
                moc1.Dispose();
                cimobject1.Dispose();

                OsTypeResult = (Convert.ToString(osBit + Convert.ToString("|")) + osType) + "|" + Utility.FormatFileSize(capacity);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetOsTypeRingtone");
            }
            return OsTypeResult;
        }

        private void SendSoftDataPrevious()
        {
            try
            {
                int n = 0;
                if (!InternetGetConnectedState(out n, 0))
                {
                    return;
                }

                tbVersionInfoCache tbVersionCache = tbVersionInfoCache.GetInstanse();

                List<tbVersionInfo> lstVersionInfo = tbVersionCache.Datas;

                if (lstVersionInfo != null && lstVersionInfo.Count > 0)
                {
                    //发送软件数据
                    List<string> lstKeys = new List<string>();
                    DateTime uploadTime = tbVersionCache.GetUploadTime();
                    TimeSpan dt = DateTime.Now.Date - uploadTime.Date;


                    if (lstVersionInfo.Count > 100 || uploadTime == DateTime.MinValue || dt.Days > 0)
                    {
                        foreach (tbVersionInfo info in lstVersionInfo)
                        {
                            if (info.Key == tbVersionCache.UPLOADTIMEKEY || info.Key == tbVersionCache.SOFTUSESTATEKEY)
                            {
                                continue;
                            }

                            //传输今天以前收集的数据
                            info.SendZhushouData();

                            //当前数据显示程序的运行时间大于0分钟，或者当前数据显示的启动时间大于1天，则不再保存
                            if ((info.runTimes > 0 || (DateTime.Now.Date - info.startTime.Date).Days > 0))
                            {
                                lstKeys.Add(info.Key);
                            }

                            Application.DoEvents();
                        }

                        //删除发送成功的软件数据
                        foreach (string key in lstKeys)
                        {
                            tbVersionCache.RemoveCacheData(key);
                        }

                        //保存上传日期
                        if (lstKeys.Count > 0)
                        {
                            tbVersionCache.AddUploadTime(DateTime.Now);
                        }

                        //保存文件
                        tbVersionCache.Save();
                    }
                }

                if (lstVersionInfo != null)
                {
                    lstVersionInfo.Clear();
                }

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SendSoftDataPrevious");
            }
        }

        private void SendSoftDataNow(tbVersionInfo info)
        {
            try
            {
                int n = 0;
                if (!InternetGetConnectedState(out n, 0))
                {
                    return;
                }

                info.SendZhushouData();

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "SendSoftDataNow");
            }
        }

        //导入判断网络是否连接的 .dll  
        [DllImport("wininet.dll", EntryPoint = "InternetGetConnectedState")]
        //判断网络状况的方法,返回值true为连接，false为未连接  
        public extern static bool InternetGetConnectedState(out int conState, int reder);

        #endregion

        #region "--- 生成错误日志 ---"

        private void CreateErrorZip()
        {
            if (mTdCreateError != null && mTdCreateError.ThreadState != System.Threading.ThreadState.Stopped)
                return;

            mTdCreateError = new Thread(new ThreadStart(DoCreateErrorZip));
            mTdCreateError.IsBackground = true;
            mTdCreateError.Start();
        }

        private void DoCreateErrorZip()
        {
            try
            {
                string tmpFile = Path.Combine(Folder.TempFolder, @"ErrorZip\" + Guid.NewGuid().ToString("N").Substring(0, 10));
                string tmpFileException = Path.Combine(tmpFile, "Exception");
                string tmpFileLog = Path.Combine(tmpFile, "Logs");
                string tmpFileComputerInfo = Path.Combine(tmpFile, "ComputerInfo");

                try
                {
                    Directory.Delete(tmpFile, true);
                }
                catch { }

                Folder.CheckFolder(tmpFileException);
                Folder.CheckFolder(tmpFileLog);
                Folder.CheckFolder(tmpFileComputerInfo);

                CreateMacInfo(tmpFileComputerInfo);
                string strSourcePath = Path.GetDirectoryName(Folder.ExceptionFolder);
                GetErrorFile(Path.Combine(strSourcePath, "Exception"), tmpFileException);
                GetErrorFile(Path.Combine(strSourcePath, "Logs"), tmpFileLog);
                //压缩
                string strZipFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), string.Format("LOG{0}.zip", DateTime.Now.ToString("yyyyMMddHHmmss")));
                Utility.PackFiles(strZipFilePath, tmpFile);
                Common.OpenExplorer(strZipFilePath);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "DoCreateErrorZip");
            }
        }

        private static void CreateMacInfo(string strPath)
        {
            try
            {
                if (Directory.Exists(strPath))
                {
                    Directory.Delete(strPath, true);
                }

                Folder.CheckFolder(strPath);
                string strDESKey = "am58!2#0";
                string strFilePath = Path.Combine(strPath, string.Format("Computer at {0}.txt", DateTime.Now.ToString("yyyyMMdd")));
                string hardDiskId = Common.EncryptDES("DiskId=" + Common.GetHardDiskID2(), strDESKey, strDESKey);
                string cpuid = Common.EncryptDES("CpuId=" + Common.GetProcessorId(), strDESKey, strDESKey);
                string macAddress = Common.EncryptDES("WifiMac=" + Common.GetMacAddress(), strDESKey, strDESKey);
                string macid = Common.EncryptDES("MacId=" + Common.GetComputerID(), strDESKey, strDESKey);
                string allInfo = string.Format("{0}_@_{1}_@_{2}_@_{3}", hardDiskId, cpuid, macAddress, macid);
                if (File.Exists(strFilePath))
                {
                    File.Delete(strFilePath);
                }
                using (StreamWriter sw = new StreamWriter(strFilePath, true, Encoding.UTF8))
                {
                    sw.WriteLine(allInfo);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "CreateMacInfo");
            }
        }

        private static void GetErrorFile(string sourcePath, string desPath, string strName = "", string strSuffix = "txt")
        {
            try
            {
                if (Directory.Exists(sourcePath) == false)
                {
                    return;
                }

                if (strName.Length == 0)
                {
                    strName = Path.GetFileNameWithoutExtension(sourcePath).TrimEnd('s');
                }

                //Dim strFileName As String = String.Format("{0}{1}.{2}", strName, "{0}", strSuffix)
                List<string> fileList = new List<string>();

                //只取今天之前7天的数据
                for (int index = 0; index <= 6; index++)
                {
                    fileList.Add((DateTime.Now.AddDays(-index)).ToString("yyyyMMdd"));
                }

                foreach (string Item in Directory.GetFiles(sourcePath))
                {
                    string strFileName = Path.GetFileName(Item);
                    foreach (string itemDate in fileList)
                    {
                        if (strFileName.Contains(itemDate))
                        {
                            try
                            {
                                File.Copy(Item, Path.Combine(desPath, strFileName));
                            }
                            catch (Exception ex)
                            {
                            }
                        }
                    }
                }

                fileList.Clear();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "GetErrorFile");
            }


        }

        #endregion

    }

    public enum MViewType
    {
        Welcome,
        ViewList
    }

    public class DataGridViewRowIndexComparer : IComparer<DataGridViewRow>
    {
        private SortType type = SortType.ASC;

        public DataGridViewRowIndexComparer(SortType tempType)
        {
            type = tempType;
        }

        public int Compare(DataGridViewRow x, DataGridViewRow y)
        {
            int result = 0;

            if (type == SortType.ASC)
                result = x.Index.CompareTo(y.Index);
            else
                result = y.Index.CompareTo(x.Index);

            return result;
        }
    }
}
