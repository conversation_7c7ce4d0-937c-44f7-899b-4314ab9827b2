﻿using iTong.Components;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace iVoiceMerge
{
    public partial class frmMergeProgress : tbBaseGuiForm
    {
        private static frmMergeProgress mProgress = null;
        public static bool Cancel = false;
        public static EventHandler<EventArgs> DoCancelEventHandler;
        #region "---  初始化  ---"

        public frmMergeProgress(MainForm owner)
        {
            InitializeComponent();
            this.Owner = owner;
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.Icon = Properties.Resources.main;
        }

        protected override void SetInterface()
        {
            base.SetInterface();
        }

        #endregion

        #region "---  Show Close  ---"

        private delegate void SetProgressHandler(string strDescription, string strProgress, string strTips, int intProgress);
        //设置进度和提示信息
        public static void SetProgress(string strDescription, string strProgress, string strTips, int intProgress)
        {
            if (mProgress == null || mProgress.IsDisposed)
                return;

            if (mProgress.InvokeRequired)
            {
                mProgress.Invoke(new SetProgressHandler(SetProgress), new object[] { strDescription, strProgress, strTips, intProgress });
            }
            else
            {
                mProgress.lblDescription.Text = strDescription;
                mProgress.lblDescription.Visible = true;
                mProgress.lblProgress.Text = strProgress;
                mProgress.lblProgress.Visible = true;
                mProgress.lblTips.Text = strTips;
                if (intProgress < 0)
                {
                    mProgress.pgpProgress.tbIsWaiting = true;
                }
                else
                {
                    if (intProgress > 100)
                    {
                        intProgress = 100;
                    }
                    mProgress.pgpProgress.tbPlayValue = intProgress;
                    mProgress.pgpProgress.tbIsWaiting = false;
                }
            }

        }

        public static void Show(MainForm frmMain)
        {
            if (mProgress == null || mProgress.IsDisposed)
                mProgress = new frmMergeProgress(frmMain);

            Thread thr = new Thread(new ParameterizedThreadStart(ShowCore));
            thr.IsBackground = true;
            thr.Start(new object[] { mProgress, frmMain });
        }

        private delegate void ShowCoreHandler(object obj);
        private static void ShowCore(object obj)
        {
            object[] arr = (object[])obj;
            frmMergeProgress frm = (frmMergeProgress)arr[0];
            MainForm frmMain = (MainForm)arr[1];
            if (frm.IsDisposed)
                return;

            if (frmMain.InvokeRequired)
            {
                frmMain.Invoke(new ShowCoreHandler(ShowCore), obj);
            }
            else
            {
                if (frm.Visible)
                    frm.Visible = false;

                Cancel = false;
                frm.ShowDialog(frmMain);
            }
        }

        public static void Close(MainForm frmMain)
        {
            CloseCore(mProgress, frmMain);
        }

        private delegate void CloseCoreHandler(frmMergeProgress frm, MainForm frmMain);
        private static void CloseCore(frmMergeProgress frm, MainForm frmMain)
        {
            if (frmMain.InvokeRequired)
            {
                frmMain.Invoke(new CloseCoreHandler(CloseCore), new object[] { frm, frmMain });
            }
            else
            {
                frm.DialogResult = DialogResult.OK;
                frm.Close();
            }
        }

        #endregion

        private void btnCancel_Click(object sender, EventArgs e)
        {
            if (tbMessageBox.Show(this, "确认取消当前操作?", "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question ) != DialogResult.OK)
                return;
            Cancel = true;
            if (DoCancelEventHandler != null)
                DoCancelEventHandler(this, new EventArgs());
            this.Close();
        }
    }
}
