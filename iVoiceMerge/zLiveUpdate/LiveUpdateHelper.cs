﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace iVoiceMerge
{
    public class LiveUpdateHelper
    {
        public static void StartLiveUpdateExe(string zipFile, string upgradeFolder, string softPath, string softVersion)
        {
            try
            {
                bool blnNewIncrease = true;
                //增量更新时没有app.zip的压缩包
                if (blnNewIncrease == false)
                {
                    if (Utility.unzip(zipFile, upgradeFolder) <= 0)
                    {
                        if (File.Exists(zipFile))
                        {
                            File.Delete(zipFile);
                        }

                        return;
                    }
                }

                string strLiveUpdate = "";
                foreach (string Item in System.IO.Directory.GetFiles(upgradeFolder, "*.exe", System.IO.SearchOption.TopDirectoryOnly))
                {
                    if (System.IO.Path.GetFileName(Item).ToLower() == "liveupdate.exe")
                    {
                        strLiveUpdate = Item;

                        break; // TODO: might not be correct. Was : Exit For
                    }
                }

                string strAppZip = System.IO.Path.Combine(upgradeFolder, "app.zip");

                //防止路径中有空格，所以需要加双引号

                string str = "\"" + softPath + "\" " + "\"" + strAppZip + "\" " + "\"" + softVersion + "\"";
                if (blnNewIncrease)
                {
                    str = "\"" + softPath + "\" " + "\"" + upgradeFolder + "\" " + "\"" + softVersion + "\" " + "\"" + "0" + "\" " + "\"" + "1" + "\" " + "\"" + "1" + "\"";
                }
                Common.Log(strLiveUpdate + " " + str);
                if (strLiveUpdate.Length > 0 && System.IO.File.Exists(strLiveUpdate))
                {
                    System.Diagnostics.Process.Start(strLiveUpdate, str);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "StartLiveUpdateExe");
            }
        }

        #region "--- 检测升级 ---"

        private static string _FolderUpgrade = "";
        private static string _strUpdatePackageFile = "";
        //是否是升级后第一次运行
        private static bool _FirstRunFromUpgrade = false;

        public static void CheckUpgradFile()
        {
            _strUpdatePackageFile = Path.Combine(Folder.AppFolder, "Upgrade.dat");
            _FolderUpgrade = Path.Combine(Path.GetTempPath(), "tbUpgrade");
            if (Folder.AppType == RunType.iVoiceMerge )
            {
                _FolderUpgrade = Path.Combine(Path.GetTempPath(), "iVoiceMergeUpgrade");
            }
            
        }

        public static void CheckUpgradeFileExist()
        {
            string strPathUpgradeSuceed = Path.Combine(Folder.AppFolder, "UpgradeSuceed.dll");

            CheckUpgradFile();

            bool blnNewIncrease = true;

            if (File.Exists(_strUpdatePackageFile))
            {
                //助手升级增量更新时，不需要删除tbUpgrade目录
                if (blnNewIncrease == false)
                {
                    try
                    {
                        //删除临时目录中的tbUpgrade目录,以免以前的升级程序失败后会遗留文件
                        if (Directory.Exists(_FolderUpgrade))
                        {
                            Directory.Delete(_FolderUpgrade, true);
                        }
                    }
                    catch
                    {
                    }
                }



                if (File.Exists(strPathUpgradeSuceed))
                {
                    _FirstRunFromUpgrade = true;

                    //升级成功后清理升级产生的临时文件
                    try
                    {
                        File.Delete(strPathUpgradeSuceed);
                    }
                    catch
                    {
                    }

                    try
                    {
                        File.Delete(_strUpdatePackageFile);
                    }
                    catch
                    {
                    }

                    if (!_FirstRunFromUpgrade)
                    {
                        return;
                    }

                    try
                    {
                        string strFileTag = Path.Combine(Application.StartupPath.TrimEnd('\\'), "OpenOK.dll");

                        if (!File.Exists(strFileTag))
                        {
                            FileStream fileStream = File.Create(strFileTag);
                            fileStream.Close();
                        }
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "LiveUpdateHelper.OpenOKException");
                    }

                }
                else
                {

                    if (!(Folder.LangType == LanguageType.en_US))
                    {
                        //助手升级增量更新时，每个下载的文件都会先比较md5，所以这边不需要再比较
                        if (blnNewIncrease == false)
                        {
                            //如果包不完整则删除
                            string strServerFileLength =  IniClass.GetIniSectionKey("Setting", "UpdateFileLength", Folder.ConfigIniFile);
                            if (!CheckUpdateFileIsFull(_strUpdatePackageFile, strServerFileLength))
                            {
                                try
                                {
                                    File.Delete(_strUpdatePackageFile);
                                }
                                catch
                                {
                                }

                                return;
                            }
                        }
                        else if (!Directory.Exists(_FolderUpgrade))
                        {
                            return;
                        }

                        //升级文件已经下载完毕，但是用户没有立刻升级，所以启动的时候需要程序自动升级
                        LiveUpdateHelper.StartLiveUpdateExe(_strUpdatePackageFile, _FolderUpgrade, Application.ExecutablePath, "");
                    }
                }
            }
        }

        public static bool CheckUpdateFileIsFull(string strUpdateFile, string strServerFileLength)
        {
            bool blnFull = false;

            if (File.Exists(strUpdateFile))
            {
                try
                {
                    long lngFileLength = new FileInfo(strUpdateFile).Length;
                    if (string.Compare(lngFileLength.ToString(), strServerFileLength) == 0)
                    {
                        blnFull = true;
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "CheckUpdateFileIsFull");
                }
            }

            return blnFull;
        }

        #endregion
    }
}
