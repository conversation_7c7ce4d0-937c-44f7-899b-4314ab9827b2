﻿namespace iVoiceMerge
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            this.panel1 = new System.Windows.Forms.Panel();
            this.tblayoutMain = new System.Windows.Forms.TableLayoutPanel();
            this.pnlViewList = new System.Windows.Forms.Panel();
            this.lblHint = new iTong.Components.tbLabel();
            this.btnDelete = new iTong.Components.tbButton();
            this.btnAdd = new iTong.Components.tbButton();
            this.btnSuperior = new iTong.Components.tbButton();
            this.btnCreate = new iTong.Components.tbButton();
            this.tbPlayer = new iVoiceMerge.tbMusicPlayer();
            this.pnlWelcome = new System.Windows.Forms.Panel();
            this.pictureBox1 = new System.Windows.Forms.PictureBox();
            this.tbLabel3 = new iTong.Components.tbLabel();
            this.richTextBox3 = new System.Windows.Forms.RichTextBox();
            this.richTextBox2 = new System.Windows.Forms.RichTextBox();
            this.btnStart = new iTong.Components.tbButton();
            this.lblWelcone = new iTong.Components.tbLabel();
            this.panel4 = new System.Windows.Forms.Panel();
            this.lblRecommend = new System.Windows.Forms.Label();
            this.btn_normal = new iTong.Components.tbButton();
            this.btn_close = new iTong.Components.tbButton();
            this.btn_minimize = new iTong.Components.tbButton();
            this.tmrUpgrade = new System.Windows.Forms.Timer(this.components);
            this.mNotifyIcon = new System.Windows.Forms.NotifyIcon(this.components);
            this.mMenu = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiCreateError = new System.Windows.Forms.ToolStripMenuItem();
            this.tss2 = new System.Windows.Forms.ToolStripSeparator();
            this.tsmiExist = new System.Windows.Forms.ToolStripMenuItem();
            this.tmrTimer = new System.Windows.Forms.Timer(this.components);
            this.btnContactEx = new iTong.Components.tbButton();
            this.mAddMenu = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiAddFile = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiAddFolder = new System.Windows.Forms.ToolStripMenuItem();
            this.tblayoutMain.SuspendLayout();
            this.pnlViewList.SuspendLayout();
            this.pnlWelcome.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).BeginInit();
            this.panel4.SuspendLayout();
            this.mMenu.SuspendLayout();
            this.mAddMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.BackColor = System.Drawing.Color.White;
            this.panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.panel1.ForeColor = System.Drawing.Color.Black;
            this.panel1.Location = new System.Drawing.Point(12, 42);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(877, 622);
            this.panel1.TabIndex = 3;
            // 
            // tblayoutMain
            // 
            this.tblayoutMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tblayoutMain.BackColor = System.Drawing.SystemColors.Control;
            this.tblayoutMain.ColumnCount = 2;
            this.tblayoutMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 40F));
            this.tblayoutMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 60F));
            this.tblayoutMain.Controls.Add(this.pnlViewList, 1, 0);
            this.tblayoutMain.Controls.Add(this.pnlWelcome, 0, 0);
            this.tblayoutMain.Controls.Add(this.panel4, 0, 1);
            this.tblayoutMain.Location = new System.Drawing.Point(1, 38);
            this.tblayoutMain.Name = "tblayoutMain";
            this.tblayoutMain.RowCount = 2;
            this.tblayoutMain.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tblayoutMain.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32F));
            this.tblayoutMain.Size = new System.Drawing.Size(1503, 769);
            this.tblayoutMain.TabIndex = 5;
            // 
            // pnlViewList
            // 
            this.pnlViewList.BackColor = System.Drawing.Color.White;
            this.pnlViewList.Controls.Add(this.lblHint);
            this.pnlViewList.Controls.Add(this.btnDelete);
            this.pnlViewList.Controls.Add(this.btnAdd);
            this.pnlViewList.Controls.Add(this.btnSuperior);
            this.pnlViewList.Controls.Add(this.btnCreate);
            this.pnlViewList.Controls.Add(this.panel1);
            this.pnlViewList.Controls.Add(this.tbPlayer);
            this.pnlViewList.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlViewList.Location = new System.Drawing.Point(601, 0);
            this.pnlViewList.Margin = new System.Windows.Forms.Padding(0);
            this.pnlViewList.Name = "pnlViewList";
            this.pnlViewList.Size = new System.Drawing.Size(902, 737);
            this.pnlViewList.TabIndex = 0;
            // 
            // lblHint
            // 
            this.lblHint.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lblHint.AutoSize = true;
            this.lblHint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(90)))), ((int)(((byte)(149)))), ((int)(((byte)(251)))));
            this.lblHint.Location = new System.Drawing.Point(14, 685);
            this.lblHint.Name = "lblHint";
            this.lblHint.Size = new System.Drawing.Size(53, 12);
            this.lblHint.TabIndex = 106;
            this.lblHint.tbAdriftWhenHover = false;
            this.lblHint.tbAutoEllipsis = false;
            this.lblHint.tbAutoSize = true;
            this.lblHint.tbHideImage = false;
            this.lblHint.tbIconImage = null;
            this.lblHint.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblHint.tbIconPlaceText = 5;
            this.lblHint.tbShadow = false;
            this.lblHint.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblHint.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.lblHint.tbShowScrolling = false;
            // 
            // btnDelete
            // 
            this.btnDelete.BackColor = System.Drawing.Color.Transparent;
            this.btnDelete.BindingForm = null;
            this.btnDelete.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDelete.Location = new System.Drawing.Point(115, 12);
            this.btnDelete.Margin = new System.Windows.Forms.Padding(0);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Padding = new System.Windows.Forms.Padding(5, 2, 5, 0);
            this.btnDelete.Selectable = true;
            this.btnDelete.Size = new System.Drawing.Size(67, 23);
            this.btnDelete.TabIndex = 105;
            this.btnDelete.tbAdriftIconWhenHover = false;
            this.btnDelete.tbAutoSize = false;
            this.btnDelete.tbAutoSizeEx = true;
            this.btnDelete.tbBackgroundImage = null;
            this.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnDelete.tbBadgeNumber = 0;
            this.btnDelete.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnDelete.tbEndEllipsis = false;
            this.btnDelete.tbIconHoldPlace = true;
            this.btnDelete.tbIconImage = global::iVoiceMerge.Properties.Resources.btn_delete_4;
            this.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnDelete.tbIconMore = false;
            this.btnDelete.tbIconMouseDown = null;
            this.btnDelete.tbIconMouseHover = null;
            this.btnDelete.tbIconMouseLeave = null;
            this.btnDelete.tbIconPlaceText = 2;
            this.btnDelete.tbIconReadOnly = null;
            this.btnDelete.tbImageMouseDown = null;
            this.btnDelete.tbImageMouseHover = null;
            this.btnDelete.tbImageMouseLeave = null;
            this.btnDelete.tbProgressValue = 50;
            this.btnDelete.tbReadOnly = false;
            this.btnDelete.tbReadOnlyText = false;
            this.btnDelete.tbShadow = false;
            this.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnDelete.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnDelete.tbShowDot = false;
            this.btnDelete.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnDelete.tbShowMoreIconImg")));
            this.btnDelete.tbShowNew = false;
            this.btnDelete.tbShowProgress = false;
            this.btnDelete.tbShowTip = true;
            this.btnDelete.tbShowToolTipOnButton = false;
            this.btnDelete.tbSplit = "0,0,0,0";
            this.btnDelete.tbText = "删除";
            this.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDelete.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnDelete.tbTextColorDisable = System.Drawing.Color.LightGray;
            this.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnDelete.tbTextMouseDownPlace = 2;
            this.btnDelete.tbToolTip = "";
            this.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnDelete.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDelete.VisibleEx = true;
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // btnAdd
            // 
            this.btnAdd.BackColor = System.Drawing.Color.Transparent;
            this.btnAdd.BindingForm = null;
            this.btnAdd.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnAdd.Location = new System.Drawing.Point(48, 12);
            this.btnAdd.Margin = new System.Windows.Forms.Padding(0);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Padding = new System.Windows.Forms.Padding(5, 2, 5, 0);
            this.btnAdd.Selectable = true;
            this.btnAdd.Size = new System.Drawing.Size(67, 23);
            this.btnAdd.TabIndex = 104;
            this.btnAdd.tbAdriftIconWhenHover = false;
            this.btnAdd.tbAutoSize = false;
            this.btnAdd.tbAutoSizeEx = true;
            this.btnAdd.tbBackgroundImage = null;
            this.btnAdd.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnAdd.tbBadgeNumber = 0;
            this.btnAdd.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnAdd.tbEndEllipsis = false;
            this.btnAdd.tbIconHoldPlace = true;
            this.btnAdd.tbIconImage = global::iVoiceMerge.Properties.Resources.btn_add_4;
            this.btnAdd.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnAdd.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnAdd.tbIconMore = false;
            this.btnAdd.tbIconMouseDown = null;
            this.btnAdd.tbIconMouseHover = null;
            this.btnAdd.tbIconMouseLeave = null;
            this.btnAdd.tbIconPlaceText = 2;
            this.btnAdd.tbIconReadOnly = null;
            this.btnAdd.tbImageMouseDown = null;
            this.btnAdd.tbImageMouseHover = null;
            this.btnAdd.tbImageMouseLeave = null;
            this.btnAdd.tbProgressValue = 50;
            this.btnAdd.tbReadOnly = false;
            this.btnAdd.tbReadOnlyText = false;
            this.btnAdd.tbShadow = false;
            this.btnAdd.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnAdd.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnAdd.tbShowDot = false;
            this.btnAdd.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnAdd.tbShowMoreIconImg")));
            this.btnAdd.tbShowNew = false;
            this.btnAdd.tbShowProgress = false;
            this.btnAdd.tbShowTip = true;
            this.btnAdd.tbShowToolTipOnButton = false;
            this.btnAdd.tbSplit = "0,0,0,0";
            this.btnAdd.tbText = "添加";
            this.btnAdd.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnAdd.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnAdd.tbTextColorDisable = System.Drawing.Color.LightGray;
            this.btnAdd.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnAdd.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnAdd.tbTextMouseDownPlace = 2;
            this.btnAdd.tbToolTip = "";
            this.btnAdd.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnAdd.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnAdd.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnAdd.VisibleEx = true;
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // btnSuperior
            // 
            this.btnSuperior.BackColor = System.Drawing.Color.Transparent;
            this.btnSuperior.BindingForm = null;
            this.btnSuperior.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSuperior.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSuperior.Location = new System.Drawing.Point(16, 15);
            this.btnSuperior.Name = "btnSuperior";
            this.btnSuperior.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnSuperior.Selectable = true;
            this.btnSuperior.Size = new System.Drawing.Size(16, 20);
            this.btnSuperior.TabIndex = 83;
            this.btnSuperior.tbAdriftIconWhenHover = false;
            this.btnSuperior.tbAutoSize = false;
            this.btnSuperior.tbAutoSizeEx = true;
            this.btnSuperior.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.btn_4_retreatex;
            this.btnSuperior.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnSuperior.tbBadgeNumber = 0;
            this.btnSuperior.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnSuperior.tbEndEllipsis = false;
            this.btnSuperior.tbIconHoldPlace = true;
            this.btnSuperior.tbIconImage = null;
            this.btnSuperior.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSuperior.tbIconImageState = iTong.Components.ImageState.FourState;
            this.btnSuperior.tbIconMore = false;
            this.btnSuperior.tbIconMouseDown = null;
            this.btnSuperior.tbIconMouseHover = null;
            this.btnSuperior.tbIconMouseLeave = null;
            this.btnSuperior.tbIconPlaceText = 2;
            this.btnSuperior.tbIconReadOnly = null;
            this.btnSuperior.tbImageMouseDown = null;
            this.btnSuperior.tbImageMouseHover = null;
            this.btnSuperior.tbImageMouseLeave = null;
            this.btnSuperior.tbProgressValue = 50;
            this.btnSuperior.tbReadOnly = false;
            this.btnSuperior.tbReadOnlyText = false;
            this.btnSuperior.tbShadow = false;
            this.btnSuperior.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnSuperior.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnSuperior.tbShowDot = false;
            this.btnSuperior.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnSuperior.tbShowMoreIconImg")));
            this.btnSuperior.tbShowNew = false;
            this.btnSuperior.tbShowProgress = false;
            this.btnSuperior.tbShowTip = true;
            this.btnSuperior.tbShowToolTipOnButton = false;
            this.btnSuperior.tbSplit = "12,1,1,1";
            this.btnSuperior.tbText = "";
            this.btnSuperior.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnSuperior.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.btnSuperior.tbTextColorDisable = System.Drawing.Color.LightGray;
            this.btnSuperior.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnSuperior.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(22)))), ((int)(((byte)(22)))));
            this.btnSuperior.tbTextMouseDownPlace = 2;
            this.btnSuperior.tbToolTip = "";
            this.btnSuperior.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnSuperior.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btnSuperior.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnSuperior.VisibleEx = true;
            this.btnSuperior.Click += new System.EventHandler(this.btnSuperior_Click);
            // 
            // btnCreate
            // 
            this.btnCreate.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCreate.BackColor = System.Drawing.Color.Transparent;
            this.btnCreate.BindingForm = null;
            this.btnCreate.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnCreate.Font = new System.Drawing.Font("宋体", 12F);
            this.btnCreate.Location = new System.Drawing.Point(738, 682);
            this.btnCreate.Name = "btnCreate";
            this.btnCreate.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnCreate.Selectable = true;
            this.btnCreate.Size = new System.Drawing.Size(152, 42);
            this.btnCreate.TabIndex = 11;
            this.btnCreate.tbAdriftIconWhenHover = false;
            this.btnCreate.tbAutoSize = false;
            this.btnCreate.tbAutoSizeEx = false;
            this.btnCreate.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.btn_4_blue;
            this.btnCreate.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnCreate.tbBadgeNumber = 0;
            this.btnCreate.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnCreate.tbEndEllipsis = false;
            this.btnCreate.tbIconHoldPlace = true;
            this.btnCreate.tbIconImage = null;
            this.btnCreate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCreate.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnCreate.tbIconMore = false;
            this.btnCreate.tbIconMouseDown = null;
            this.btnCreate.tbIconMouseHover = null;
            this.btnCreate.tbIconMouseLeave = null;
            this.btnCreate.tbIconPlaceText = 2;
            this.btnCreate.tbIconReadOnly = null;
            this.btnCreate.tbImageMouseDown = null;
            this.btnCreate.tbImageMouseHover = null;
            this.btnCreate.tbImageMouseLeave = null;
            this.btnCreate.tbProgressValue = 50;
            this.btnCreate.tbReadOnly = false;
            this.btnCreate.tbReadOnlyText = false;
            this.btnCreate.tbShadow = false;
            this.btnCreate.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnCreate.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnCreate.tbShowDot = false;
            this.btnCreate.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnCreate.tbShowMoreIconImg")));
            this.btnCreate.tbShowNew = false;
            this.btnCreate.tbShowProgress = false;
            this.btnCreate.tbShowTip = true;
            this.btnCreate.tbShowToolTipOnButton = false;
            this.btnCreate.tbSplit = "3,3,3,3";
            this.btnCreate.tbText = "合并生成";
            this.btnCreate.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCreate.tbTextColor = System.Drawing.Color.White;
            this.btnCreate.tbTextColorDisable = System.Drawing.Color.White;
            this.btnCreate.tbTextColorDown = System.Drawing.Color.White;
            this.btnCreate.tbTextColorHover = System.Drawing.Color.White;
            this.btnCreate.tbTextMouseDownPlace = 0;
            this.btnCreate.tbToolTip = "";
            this.btnCreate.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnCreate.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnCreate.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCreate.VisibleEx = true;
            this.btnCreate.Click += new System.EventHandler(this.btnCreate_Click);
            // 
            // tbPlayer
            // 
            this.tbPlayer.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tbPlayer.BackColor = System.Drawing.Color.Transparent;
            this.tbPlayer.ForeColor = System.Drawing.Color.White;
            this.tbPlayer.Location = new System.Drawing.Point(13, 682);
            this.tbPlayer.Name = "tbPlayer";
            this.tbPlayer.Size = new System.Drawing.Size(710, 42);
            this.tbPlayer.TabIndex = 4;
            this.tbPlayer.tbArtist = "周杰伦";
            this.tbPlayer.tbButtonState = iVoiceMerge.PlayStatus.Ready;
            this.tbPlayer.tbDownMax = 100;
            this.tbPlayer.tbDownMin = 0;
            this.tbPlayer.tbDownValue = 0;
            this.tbPlayer.tbLoopPlayText = "列表循环";
            this.tbPlayer.tbOrderPlayText = "顺序播放";
            this.tbPlayer.tbPlayMax = 100;
            this.tbPlayer.tbPlayMin = 0;
            this.tbPlayer.tbPlayMode = iTong.Components.UserPlayMode.Undefined;
            this.tbPlayer.tbPlaySource = iTong.Components.PlaySource.Undefined;
            this.tbPlayer.tbPlayTime = "13:26/15:26";
            this.tbPlayer.tbPlayValue = 0;
            this.tbPlayer.tbRandomPlayText = "随机播放";
            this.tbPlayer.tbSingleCycleText = "单曲循环";
            this.tbPlayer.tbTitle = "青花瓷";
            this.tbPlayer.Visible = false;
            this.tbPlayer.PlayModeChanged += new System.EventHandler<iVoiceMerge.PlayModeEventArgs>(this.tbPlayer_PlayModeChanged);
            this.tbPlayer.PlayValueChanged += new System.EventHandler<iTong.Components.ProgressEventArgs>(this.tbPlayer_PlayValueChanged);
            this.tbPlayer.VoiceValueChanged += new System.EventHandler<iTong.Components.ProgressEventArgs>(this.tbPlayer_VoiceValueChanged);
            this.tbPlayer.Play += new System.EventHandler(this.tbPlayer_Play);
            this.tbPlayer.Pause += new System.EventHandler(this.tbPlayer_Pause);
            this.tbPlayer.Prev += new System.EventHandler(this.tbPlayer_Prev);
            this.tbPlayer.Next += new System.EventHandler(this.tbPlayer_Next);
            // 
            // pnlWelcome
            // 
            this.pnlWelcome.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.pnlWelcome.Controls.Add(this.pictureBox1);
            this.pnlWelcome.Controls.Add(this.tbLabel3);
            this.pnlWelcome.Controls.Add(this.richTextBox3);
            this.pnlWelcome.Controls.Add(this.richTextBox2);
            this.pnlWelcome.Controls.Add(this.btnStart);
            this.pnlWelcome.Controls.Add(this.lblWelcone);
            this.pnlWelcome.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlWelcome.Location = new System.Drawing.Point(0, 0);
            this.pnlWelcome.Margin = new System.Windows.Forms.Padding(0);
            this.pnlWelcome.Name = "pnlWelcome";
            this.pnlWelcome.Size = new System.Drawing.Size(601, 737);
            this.pnlWelcome.TabIndex = 1;
            // 
            // pictureBox1
            // 
            this.pictureBox1.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.pictureBox1.Image = global::iVoiceMerge.Properties.Resources.pic_sign;
            this.pictureBox1.Location = new System.Drawing.Point(439, 153);
            this.pictureBox1.Name = "pictureBox1";
            this.pictureBox1.Size = new System.Drawing.Size(94, 26);
            this.pictureBox1.TabIndex = 0;
            this.pictureBox1.TabStop = false;
            // 
            // tbLabel3
            // 
            this.tbLabel3.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.tbLabel3.AutoSize = true;
            this.tbLabel3.Font = new System.Drawing.Font("宋体", 10F);
            this.tbLabel3.ForeColor = System.Drawing.Color.Red;
            this.tbLabel3.Location = new System.Drawing.Point(59, 514);
            this.tbLabel3.Name = "tbLabel3";
            this.tbLabel3.Size = new System.Drawing.Size(336, 14);
            this.tbLabel3.TabIndex = 13;
            this.tbLabel3.tbAdriftWhenHover = false;
            this.tbLabel3.tbAutoEllipsis = false;
            this.tbLabel3.tbAutoSize = true;
            this.tbLabel3.tbHideImage = false;
            this.tbLabel3.tbIconImage = null;
            this.tbLabel3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.tbLabel3.tbIconPlaceText = 5;
            this.tbLabel3.tbShadow = false;
            this.tbLabel3.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.tbLabel3.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.tbLabel3.tbShowScrolling = false;
            this.tbLabel3.Text = "目前仅支持MP3格式的文件，兼容更多格式敬请期待！";
            // 
            // richTextBox3
            // 
            this.richTextBox3.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.richTextBox3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.richTextBox3.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.richTextBox3.Location = new System.Drawing.Point(62, 372);
            this.richTextBox3.Name = "richTextBox3";
            this.richTextBox3.ReadOnly = true;
            this.richTextBox3.Size = new System.Drawing.Size(480, 124);
            this.richTextBox3.TabIndex = 11;
            this.richTextBox3.Text = "语音合并测试版可以：\n\n1.可以将微信备份助手导出的微信语音文件合并；\n\n2.可以将其他来源的音频文件合并；\n\n3.它现在限时免费中！";
            // 
            // richTextBox2
            // 
            this.richTextBox2.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.richTextBox2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(251)))), ((int)(((byte)(252)))));
            this.richTextBox2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.richTextBox2.Font = new System.Drawing.Font("宋体", 11F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.richTextBox2.Location = new System.Drawing.Point(62, 211);
            this.richTextBox2.Name = "richTextBox2";
            this.richTextBox2.ReadOnly = true;
            this.richTextBox2.Size = new System.Drawing.Size(480, 116);
            this.richTextBox2.TabIndex = 12;
            this.richTextBox2.Text = "语音合并助手是能将微信备份助手导出的语音进行合并的工具。同样它也支持其他来源的音频文件。\n\n您只需要把微信备份助手导出的音频文件或其他来源的音频文件，添加到语音合" +
    "并助手中（支持自定义语音顺序）就可以合并音频文件。";
            // 
            // btnStart
            // 
            this.btnStart.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.btnStart.BackColor = System.Drawing.Color.Transparent;
            this.btnStart.BindingForm = null;
            this.btnStart.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnStart.Font = new System.Drawing.Font("宋体", 12F);
            this.btnStart.Location = new System.Drawing.Point(230, 558);
            this.btnStart.Name = "btnStart";
            this.btnStart.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnStart.Selectable = true;
            this.btnStart.Size = new System.Drawing.Size(141, 37);
            this.btnStart.TabIndex = 10;
            this.btnStart.tbAdriftIconWhenHover = false;
            this.btnStart.tbAutoSize = false;
            this.btnStart.tbAutoSizeEx = false;
            this.btnStart.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.btn_4_blue;
            this.btnStart.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnStart.tbBadgeNumber = 0;
            this.btnStart.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnStart.tbEndEllipsis = false;
            this.btnStart.tbIconHoldPlace = true;
            this.btnStart.tbIconImage = null;
            this.btnStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnStart.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnStart.tbIconMore = false;
            this.btnStart.tbIconMouseDown = null;
            this.btnStart.tbIconMouseHover = null;
            this.btnStart.tbIconMouseLeave = null;
            this.btnStart.tbIconPlaceText = 2;
            this.btnStart.tbIconReadOnly = null;
            this.btnStart.tbImageMouseDown = null;
            this.btnStart.tbImageMouseHover = null;
            this.btnStart.tbImageMouseLeave = null;
            this.btnStart.tbProgressValue = 50;
            this.btnStart.tbReadOnly = false;
            this.btnStart.tbReadOnlyText = false;
            this.btnStart.tbShadow = false;
            this.btnStart.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnStart.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnStart.tbShowDot = false;
            this.btnStart.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnStart.tbShowMoreIconImg")));
            this.btnStart.tbShowNew = false;
            this.btnStart.tbShowProgress = false;
            this.btnStart.tbShowTip = true;
            this.btnStart.tbShowToolTipOnButton = false;
            this.btnStart.tbSplit = "3,3,3,3";
            this.btnStart.tbText = "开始使用";
            this.btnStart.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnStart.tbTextColor = System.Drawing.Color.White;
            this.btnStart.tbTextColorDisable = System.Drawing.Color.White;
            this.btnStart.tbTextColorDown = System.Drawing.Color.White;
            this.btnStart.tbTextColorHover = System.Drawing.Color.White;
            this.btnStart.tbTextMouseDownPlace = 0;
            this.btnStart.tbToolTip = "";
            this.btnStart.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnStart.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnStart.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnStart.VisibleEx = true;
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click);
            // 
            // lblWelcone
            // 
            this.lblWelcone.Anchor = System.Windows.Forms.AnchorStyles.None;
            this.lblWelcone.AutoSize = true;
            this.lblWelcone.Font = new System.Drawing.Font("宋体", 26.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblWelcone.Location = new System.Drawing.Point(68, 142);
            this.lblWelcone.Name = "lblWelcone";
            this.lblWelcone.Size = new System.Drawing.Size(365, 35);
            this.lblWelcone.TabIndex = 9;
            this.lblWelcone.tbAdriftWhenHover = false;
            this.lblWelcone.tbAutoEllipsis = false;
            this.lblWelcone.tbAutoSize = true;
            this.lblWelcone.tbHideImage = false;
            this.lblWelcone.tbIconImage = null;
            this.lblWelcone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.lblWelcone.tbIconPlaceText = 5;
            this.lblWelcone.tbShadow = false;
            this.lblWelcone.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.lblWelcone.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.lblWelcone.tbShowScrolling = false;
            this.lblWelcone.Text = "欢迎使用语音合并助手";
            this.lblWelcone.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            // 
            // panel4
            // 
            this.panel4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tblayoutMain.SetColumnSpan(this.panel4, 2);
            this.panel4.Controls.Add(this.lblRecommend);
            this.panel4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel4.Location = new System.Drawing.Point(0, 737);
            this.panel4.Margin = new System.Windows.Forms.Padding(0);
            this.panel4.Name = "panel4";
            this.panel4.Size = new System.Drawing.Size(1503, 32);
            this.panel4.TabIndex = 2;
            // 
            // lblRecommend
            // 
            this.lblRecommend.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblRecommend.ForeColor = System.Drawing.Color.Maroon;
            this.lblRecommend.Location = new System.Drawing.Point(11, 5);
            this.lblRecommend.Name = "lblRecommend";
            this.lblRecommend.Size = new System.Drawing.Size(1481, 23);
            this.lblRecommend.TabIndex = 0;
            this.lblRecommend.Text = "客服在线支持：周一至周五10：00-12：00  13：30-18：30   周六14：00-18：00 周日休息，法定节假日另行通知";
            this.lblRecommend.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btn_normal
            // 
            this.btn_normal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_normal.BackColor = System.Drawing.Color.Transparent;
            this.btn_normal.BindingForm = null;
            this.btn_normal.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_normal.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_normal.Location = new System.Drawing.Point(1448, 7);
            this.btn_normal.Name = "btn_normal";
            this.btn_normal.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_normal.Selectable = true;
            this.btn_normal.Size = new System.Drawing.Size(24, 24);
            this.btn_normal.TabIndex = 51;
            this.btn_normal.tbAdriftIconWhenHover = false;
            this.btn_normal.tbAutoSize = false;
            this.btn_normal.tbAutoSizeEx = false;
            this.btn_normal.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.btn_max;
            this.btn_normal.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_normal.tbBadgeNumber = 0;
            this.btn_normal.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_normal.tbEndEllipsis = false;
            this.btn_normal.tbIconHoldPlace = true;
            this.btn_normal.tbIconImage = null;
            this.btn_normal.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btn_normal.tbIconMore = false;
            this.btn_normal.tbIconMouseDown = null;
            this.btn_normal.tbIconMouseHover = null;
            this.btn_normal.tbIconMouseLeave = null;
            this.btn_normal.tbIconPlaceText = 2;
            this.btn_normal.tbIconReadOnly = null;
            this.btn_normal.tbImageMouseDown = null;
            this.btn_normal.tbImageMouseHover = null;
            this.btn_normal.tbImageMouseLeave = null;
            this.btn_normal.tbProgressValue = 50;
            this.btn_normal.tbReadOnly = false;
            this.btn_normal.tbReadOnlyText = false;
            this.btn_normal.tbShadow = false;
            this.btn_normal.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_normal.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_normal.tbShowDot = false;
            this.btn_normal.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_normal.tbShowMoreIconImg")));
            this.btn_normal.tbShowNew = false;
            this.btn_normal.tbShowProgress = false;
            this.btn_normal.tbShowTip = true;
            this.btn_normal.tbShowToolTipOnButton = false;
            this.btn_normal.tbSplit = "3,3,3,3";
            this.btn_normal.tbText = "";
            this.btn_normal.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.tbTextColor = System.Drawing.Color.White;
            this.btn_normal.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_normal.tbTextColorDown = System.Drawing.Color.White;
            this.btn_normal.tbTextColorHover = System.Drawing.Color.White;
            this.btn_normal.tbTextMouseDownPlace = 0;
            this.btn_normal.tbToolTip = "";
            this.btn_normal.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_normal.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_normal.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.VisibleEx = true;
            // 
            // btn_close
            // 
            this.btn_close.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_close.BackColor = System.Drawing.Color.Transparent;
            this.btn_close.BindingForm = null;
            this.btn_close.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_close.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_close.Location = new System.Drawing.Point(1478, 6);
            this.btn_close.Name = "btn_close";
            this.btn_close.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_close.Selectable = true;
            this.btn_close.Size = new System.Drawing.Size(26, 26);
            this.btn_close.TabIndex = 50;
            this.btn_close.tbAdriftIconWhenHover = false;
            this.btn_close.tbAutoSize = true;
            this.btn_close.tbAutoSizeEx = false;
            this.btn_close.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.btn_close;
            this.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbBadgeNumber = 0;
            this.btn_close.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_close.tbEndEllipsis = false;
            this.btn_close.tbIconHoldPlace = true;
            this.btn_close.tbIconImage = null;
            this.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbIconMore = false;
            this.btn_close.tbIconMouseDown = null;
            this.btn_close.tbIconMouseHover = null;
            this.btn_close.tbIconMouseLeave = null;
            this.btn_close.tbIconPlaceText = 2;
            this.btn_close.tbIconReadOnly = null;
            this.btn_close.tbImageMouseDown = null;
            this.btn_close.tbImageMouseHover = null;
            this.btn_close.tbImageMouseLeave = null;
            this.btn_close.tbProgressValue = 50;
            this.btn_close.tbReadOnly = false;
            this.btn_close.tbReadOnlyText = false;
            this.btn_close.tbShadow = false;
            this.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_close.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_close.tbShowDot = false;
            this.btn_close.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_close.tbShowMoreIconImg")));
            this.btn_close.tbShowNew = false;
            this.btn_close.tbShowProgress = false;
            this.btn_close.tbShowTip = true;
            this.btn_close.tbShowToolTipOnButton = false;
            this.btn_close.tbSplit = "3,3,3,3";
            this.btn_close.tbText = "";
            this.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbTextColor = System.Drawing.Color.White;
            this.btn_close.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_close.tbTextColorDown = System.Drawing.Color.White;
            this.btn_close.tbTextColorHover = System.Drawing.Color.White;
            this.btn_close.tbTextMouseDownPlace = 0;
            this.btn_close.tbToolTip = "";
            this.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_close.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.VisibleEx = true;
            // 
            // btn_minimize
            // 
            this.btn_minimize.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_minimize.BackColor = System.Drawing.Color.Transparent;
            this.btn_minimize.BindingForm = null;
            this.btn_minimize.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btn_minimize.Font = new System.Drawing.Font("Arial", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_minimize.Location = new System.Drawing.Point(1416, 6);
            this.btn_minimize.Name = "btn_minimize";
            this.btn_minimize.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_minimize.Selectable = true;
            this.btn_minimize.Size = new System.Drawing.Size(26, 26);
            this.btn_minimize.TabIndex = 49;
            this.btn_minimize.tbAdriftIconWhenHover = false;
            this.btn_minimize.tbAutoSize = true;
            this.btn_minimize.tbAutoSizeEx = false;
            this.btn_minimize.tbBackgroundImage = global::iVoiceMerge.Properties.Resources.btn_min;
            this.btn_minimize.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_minimize.tbBadgeNumber = 0;
            this.btn_minimize.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_minimize.tbEndEllipsis = false;
            this.btn_minimize.tbIconHoldPlace = true;
            this.btn_minimize.tbIconImage = null;
            this.btn_minimize.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btn_minimize.tbIconMore = false;
            this.btn_minimize.tbIconMouseDown = null;
            this.btn_minimize.tbIconMouseHover = null;
            this.btn_minimize.tbIconMouseLeave = null;
            this.btn_minimize.tbIconPlaceText = 2;
            this.btn_minimize.tbIconReadOnly = null;
            this.btn_minimize.tbImageMouseDown = null;
            this.btn_minimize.tbImageMouseHover = null;
            this.btn_minimize.tbImageMouseLeave = null;
            this.btn_minimize.tbProgressValue = 50;
            this.btn_minimize.tbReadOnly = false;
            this.btn_minimize.tbReadOnlyText = false;
            this.btn_minimize.tbShadow = false;
            this.btn_minimize.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_minimize.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_minimize.tbShowDot = false;
            this.btn_minimize.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_minimize.tbShowMoreIconImg")));
            this.btn_minimize.tbShowNew = false;
            this.btn_minimize.tbShowProgress = false;
            this.btn_minimize.tbShowTip = true;
            this.btn_minimize.tbShowToolTipOnButton = false;
            this.btn_minimize.tbSplit = "3,3,3,3";
            this.btn_minimize.tbText = "";
            this.btn_minimize.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.tbTextColor = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorDown = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorHover = System.Drawing.Color.White;
            this.btn_minimize.tbTextMouseDownPlace = 0;
            this.btn_minimize.tbToolTip = "";
            this.btn_minimize.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_minimize.tbToolTipFont = new System.Drawing.Font("Arial", 9F);
            this.btn_minimize.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.VisibleEx = true;
            // 
            // tmrUpgrade
            // 
            this.tmrUpgrade.Interval = 500;
            this.tmrUpgrade.Tick += new System.EventHandler(this.tmrUpgrade_Tick);
            // 
            // mNotifyIcon
            // 
            this.mNotifyIcon.ContextMenuStrip = this.mMenu;
            this.mNotifyIcon.Icon = ((System.Drawing.Icon)(resources.GetObject("mNotifyIcon.Icon")));
            this.mNotifyIcon.Text = "语音合并助手";
            this.mNotifyIcon.Visible = true;
            // 
            // mMenu
            // 
            this.mMenu.AccessibleDescription = "153x76";
            this.mMenu.DropShadowEnabled = false;
            this.mMenu.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.mMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiCreateError,
            this.tss2,
            this.tsmiExist});
            this.mMenu.Name = "mMenu";
            this.mMenu.Size = new System.Drawing.Size(140, 54);
            this.mMenu.tbBackColor = System.Drawing.Color.White;
            this.mMenu.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.mMenu.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.mMenu.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiCreateError
            // 
            this.tsmiCreateError.Name = "tsmiCreateError";
            this.tsmiCreateError.Size = new System.Drawing.Size(139, 22);
            this.tsmiCreateError.Text = "错误日志(&E)";
            this.tsmiCreateError.Click += new System.EventHandler(this.tsmiCreateError_Click);
            // 
            // tss2
            // 
            this.tss2.Name = "tss2";
            this.tss2.Size = new System.Drawing.Size(136, 6);
            // 
            // tsmiExist
            // 
            this.tsmiExist.Name = "tsmiExist";
            this.tsmiExist.Size = new System.Drawing.Size(139, 22);
            this.tsmiExist.Text = "退出(&X)";
            this.tsmiExist.Click += new System.EventHandler(this.tsmiExist_Click);
            // 
            // tmrTimer
            // 
            this.tmrTimer.Interval = 1000;
            this.tmrTimer.Tick += new System.EventHandler(this.tmrTimer_Tick);
            // 
            // btnContactEx
            // 
            this.btnContactEx.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnContactEx.BackColor = System.Drawing.Color.Transparent;
            this.btnContactEx.BindingForm = null;
            this.btnContactEx.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnContactEx.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnContactEx.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.Location = new System.Drawing.Point(1327, 2);
            this.btnContactEx.Margin = new System.Windows.Forms.Padding(0);
            this.btnContactEx.Name = "btnContactEx";
            this.btnContactEx.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnContactEx.Selectable = true;
            this.btnContactEx.Size = new System.Drawing.Size(91, 23);
            this.btnContactEx.TabIndex = 81;
            this.btnContactEx.tbAdriftIconWhenHover = false;
            this.btnContactEx.tbAutoSize = false;
            this.btnContactEx.tbAutoSizeEx = true;
            this.btnContactEx.tbBackgroundImage = null;
            this.btnContactEx.tbBackgroundImageState = iTong.Components.ImageState.OneState;
            this.btnContactEx.tbBadgeNumber = 0;
            this.btnContactEx.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnContactEx.tbEndEllipsis = false;
            this.btnContactEx.tbIconHoldPlace = true;
            this.btnContactEx.tbIconImage = global::iVoiceMerge.Properties.Resources.btn_3_Contact;
            this.btnContactEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnContactEx.tbIconImageState = iTong.Components.ImageState.ThreeState;
            this.btnContactEx.tbIconMore = false;
            this.btnContactEx.tbIconMouseDown = null;
            this.btnContactEx.tbIconMouseHover = null;
            this.btnContactEx.tbIconMouseLeave = null;
            this.btnContactEx.tbIconPlaceText = 2;
            this.btnContactEx.tbIconReadOnly = null;
            this.btnContactEx.tbImageMouseDown = null;
            this.btnContactEx.tbImageMouseHover = null;
            this.btnContactEx.tbImageMouseLeave = null;
            this.btnContactEx.tbProgressValue = 50;
            this.btnContactEx.tbReadOnly = false;
            this.btnContactEx.tbReadOnlyText = false;
            this.btnContactEx.tbShadow = false;
            this.btnContactEx.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnContactEx.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnContactEx.tbShowDot = false;
            this.btnContactEx.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnContactEx.tbShowMoreIconImg")));
            this.btnContactEx.tbShowNew = false;
            this.btnContactEx.tbShowProgress = false;
            this.btnContactEx.tbShowTip = true;
            this.btnContactEx.tbShowToolTipOnButton = false;
            this.btnContactEx.tbSplit = "0,0,0,0";
            this.btnContactEx.tbText = "联系客服";
            this.btnContactEx.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnContactEx.tbTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.tbTextColorDisable = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.tbTextColorDown = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.tbTextColorHover = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.btnContactEx.tbTextMouseDownPlace = 2;
            this.btnContactEx.tbToolTip = "";
            this.btnContactEx.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnContactEx.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnContactEx.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnContactEx.VisibleEx = true;
            this.btnContactEx.Click += new System.EventHandler(this.btnContactEx_Click);
            // 
            // mAddMenu
            // 
            this.mAddMenu.AccessibleDescription = "153x70";
            this.mAddMenu.DropShadowEnabled = false;
            this.mAddMenu.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.mAddMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiAddFile,
            this.tsmiAddFolder});
            this.mAddMenu.Name = "mMenu";
            this.mAddMenu.Size = new System.Drawing.Size(137, 48);
            this.mAddMenu.tbBackColor = System.Drawing.Color.White;
            this.mAddMenu.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.mAddMenu.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.mAddMenu.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiAddFile
            // 
            this.tsmiAddFile.Name = "tsmiAddFile";
            this.tsmiAddFile.Size = new System.Drawing.Size(136, 22);
            this.tsmiAddFile.Text = "添加文件";
            this.tsmiAddFile.Click += new System.EventHandler(this.tsmiAddFile_Click);
            // 
            // tsmiAddFolder
            // 
            this.tsmiAddFolder.Name = "tsmiAddFolder";
            this.tsmiAddFolder.Size = new System.Drawing.Size(136, 22);
            this.tsmiAddFolder.Text = "添加文件夹";
            this.tsmiAddFolder.Click += new System.EventHandler(this.tsmiAddFolder_Click);
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1505, 808);
            this.Controls.Add(this.btnContactEx);
            this.Controls.Add(this.btn_normal);
            this.Controls.Add(this.tblayoutMain);
            this.Controls.Add(this.btn_close);
            this.Controls.Add(this.btn_minimize);
            this.Name = "MainForm";
            this.tbAutoSetFormSize = true;
            this.tbGuiBackground = global::iVoiceMerge.Properties.Resources.frm_bg_sub;
            this.tbShowTitleOnForm = true;
            this.tbSplit = "6,39,7,28";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.tbTitleForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(106)))), ((int)(((byte)(106)))), ((int)(((byte)(106)))));
            this.tbTitleLocation = new System.Drawing.Point(4, 10);
            this.Text = "语音合并助手";
            this.tblayoutMain.ResumeLayout(false);
            this.pnlViewList.ResumeLayout(false);
            this.pnlWelcome.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBox1)).EndInit();
            this.panel4.ResumeLayout(false);
            this.mMenu.ResumeLayout(false);
            this.mAddMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private tbMusicPlayer tbPlayer;
        private System.Windows.Forms.TableLayoutPanel tblayoutMain;
        private System.Windows.Forms.Panel pnlViewList;
        private System.Windows.Forms.Panel pnlWelcome;
        private System.Windows.Forms.Panel panel4;
        private iTong.Components.tbLabel tbLabel3;
        private System.Windows.Forms.RichTextBox richTextBox3;
        private System.Windows.Forms.RichTextBox richTextBox2;
        private iTong.Components.tbButton btnStart;
        private iTong.Components.tbLabel lblWelcone;
        protected iTong.Components.tbButton btn_normal;
        internal iTong.Components.tbButton btn_close;
        internal iTong.Components.tbButton btn_minimize;
        private iTong.Components.tbButton btnCreate;
        internal iTong.Components.tbButton btnSuperior;
        internal iTong.Components.tbButton btnAdd;
        internal iTong.Components.tbButton btnDelete;
        private System.Windows.Forms.Label lblRecommend;
        internal System.Windows.Forms.Timer tmrUpgrade;
        internal System.Windows.Forms.NotifyIcon mNotifyIcon;
        private System.Windows.Forms.Timer tmrTimer;
        private System.Windows.Forms.PictureBox pictureBox1;
        internal iTong.Components.tbButton btnContactEx;
        internal iTong.Components.tbContextMenuStrip mMenu;
        internal System.Windows.Forms.ToolStripMenuItem tsmiCreateError;
        internal System.Windows.Forms.ToolStripSeparator tss2;
        internal System.Windows.Forms.ToolStripMenuItem tsmiExist;
        private iTong.Components.tbLabel lblHint;
        internal iTong.Components.tbContextMenuStrip mAddMenu;
        internal System.Windows.Forms.ToolStripMenuItem tsmiAddFile;
        internal System.Windows.Forms.ToolStripMenuItem tsmiAddFolder;
    }
}

