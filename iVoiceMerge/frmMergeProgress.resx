﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pgpProgress.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABeSURBVChTtdExDoAwDEPRXqZqL98LMTF3QcABjP8e
        kFqV4W2xlShp2zuyNTtNg8iQzZISZcUOi4ZH0FEopD0amNEonDnzzf1L4fKTq616SqUQfJpNL4uGv5Ah
        WySlB5vwOLkNu2hqAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="pgpProgress.tbDotImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADYAAAASCAYAAAAQeC39AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAANTSURBVFhHzZZJTFNBGMdL6UOQiLhgAi3QilhQZImU
        mJDoSZajYOJBEi+acNGLF2OIXDRENMYFtCZVSTDiEhIORiBAWNJS2tJiKwpqrQvBBQ1GlrYsfeP3Pd4z
        9PV1gYvvS/7pZN58v8438803I7GNutatuENVolXAZMGiQFKQDBQDimWFbezDb1HhAsMx7NiQHCHf1cIx
        7Ng1cfhBSaXxiTEPn7YdNJgddov93aL11QeCgvaCwWwfbnrUqoFxFI7lwziJgeMf1Jbk+O4B02N0/Db1
        m3i8i8RH08Tno6G9QL7+mAaok+7sHdRKE5Li+DAGKBLOv6Cityk2tvfomz9OTJF5t5cEs5k5NwEf8rxr
        oDEAJiIOWhRud32DrnzY4SRz8x7WJbjNzLqJxfGerqm7Xgz+mAZ4DsTGgdXZnprY0Wt8jdvN2RJs9229
        i1Q8MDHCNvZxhmnwosdgjt6akoCMkBzrT1LZ6mKE7fVwFpdpcrHvM9FobYywjX2cCXAkMpk8K01vGV3C
        HObsjsFFSrQGP+mGPrFfCZMeAyaHVyZXK5ARlAOBlLY4/XTv5S/2a+ScS/1fiPqGxU9X9RPsV0GOJEaW
        vi8bt52Gg8lZJewSPzDs42x52UdMI29p8FUjIxjnKOwSPzDs4yxSTtHdkYDAsI8zAY4kllLm5mPlWcUJ
        G9gSgIZGxmnwzQXGhmCccIFFygkXmABnBdQ/ZF9zKnbrrV7+hAI4Aqmo46ViJByhVLzCS0UeZ2Xrm1vb
        XWspHpNwWHUtbaP8FBLkhCgekXLCFQ8BDhxWRXZqyfHqs5CjZHYufHn9A+XVaBujiytOVIOvHBki5EB5
        TUrfTO0sOFB7TduLl91siLsD7wzM/5r6xnZKVaDxK9Pi4jCXGYVlksooLD9/ubHbaBun8V5wexaYaoMH
        E3N48vs0rMw4fa7uZheVsb8UfFLQl2WIiwMrAr/wLoPnCOSnitpVWJZXdqy2oemZq7Pf4jVax+jB4Td0
        R5/Zc+v+E+eew5UXGEjaXqXQU0gsHL9HML7PZIqsZBiooTI1R6jMolOgM6DToJNMH2w3rgx/MqsnJQZO
        AAwki96hSpCl5cgpZZ6aUuXnMFLm7saDCX+2CcbgdgdMhtP/51RJ/gJtCcnShGQPVgAAAABJRU5ErkJg
        gg==
</value>
  </data>
  <data name="pgpProgress.tbDownloadImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABfSURBVChTtdHBDYAwDEPRLlO1I7Ej6j7dAgEDGP97
        QGpVDu8WW4mStr0jW7PTNIgM2SwpUVbssGh4BB2FQtqjgRmNwpkz39y/FC4/udqqp1QKwafZ9LJo+AsZ
        skVSegDSI7ycpiMdKQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="pgpProgress.tbPlayImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAAICAYAAAD5nd/tAAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABfSURBVChTtdHBDYAwDEPRLlO1I7Ej6j7dAgEDGP97
        QGpVDu8WW4mStr0jW7PTNIgM2SwpUVbssGh4BB2FQtqjgRmNwpkz39y/FC4/udqqp1QKwafZ9LJo+AsZ
        skVSegDSI7ycpiMdKQAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btnCancel.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btn_close.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
</root>