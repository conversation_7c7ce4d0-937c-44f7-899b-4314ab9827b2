﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace tbToolkit
{
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    public delegate void DEBUG_ERRORHANDLE(int intPara);
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    public delegate void DEBUG_PRINTLOG(string strMsg);
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    public delegate void DEBUG_UPDATEDEBUGSTATUS(int intPara);
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    public delegate bool DEBUG_RUNSCRIPT();
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    public delegate void DEBUG_UPDATESTACKINFO(string strStack);
    [UnmanagedFunctionPointer(CallingConvention.Cdecl)]
    public delegate void DEBUG_UPDATEVAULESINFO(string strVaules);

    [StructLayout(LayoutKind.Sequential)]
    public struct CallBackFnction
    {
        public DEBUG_ERRORHANDLE debug_errorHandle;
        public DEBUG_PRINTLOG debug_printLog;
        public DEBUG_UPDATEDEBUGSTATUS debug_updateDebugStatus;
        public DEBUG_RUNSCRIPT debug_runScript;
        public DEBUG_UPDATESTACKINFO debug_updateStackInfo;
        public DEBUG_UPDATEVAULESINFO debug_updateVaulesInfo;
    }

    public class tbLua
    {
        private const string PathLuaBridge = "LuaBridge.dll";

        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern void SetCallBack(CallBackFnction address);

        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern int InitLuaBridge();

        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern void CleanLuaBridge();

        // 开始调试
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ide_startDebug(string strIP);

        // 停止调试
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ide_stopDebug();

        // 单步步过
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ide_over();

        // 单步步入
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ide_step();

        // 返回上一层
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ide_out();

        // 执行
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ide_run();

        // 获取变量值
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern string ide_serializeValue();

        // 设置断点
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern bool ide_breakpoint(string strFilePath,int intLint,bool blnState);

        // 删除所有断点
        [DllImport(PathLuaBridge, CallingConvention = CallingConvention.Cdecl)]
        public static extern void ide_delAllBreakpoints();

    }
}
