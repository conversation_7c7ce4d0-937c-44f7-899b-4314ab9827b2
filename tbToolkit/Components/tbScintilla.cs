﻿using ScintillaNET;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using tbToolkit;
using System.Windows.Forms;
using System.Text.RegularExpressions;
using iTong.CoreFoundation;

namespace tbToolkit
{
    public class tbScintilla: Scintilla
    {
        #region const

        /// <summary>
        /// the background color of the text area
        /// </summary>
        private const int BACK_COLOR = 0x2A211C;
        /// <summary>
        /// default text color of the text area
        /// </summary>
        private const int FORE_COLOR = 0xB7B7B7;

        private const int BREAKPOINT_MARGIN = 0;
        private const int BREAKPOINT_MARKER = 0;

        /// <summary>
        /// change this to whatever margin you want the line numbers to show in
        /// </summary>
        private const int NUMBER_MARGIN = 1;
        /// <summary>
        /// change this to whatever margin you want the bookmarks/breakpoints to show in
        /// </summary>
        private const int BOOKMARK_MARGIN = 2;
        private const int BOOKMARK_MARKER = 2;

        private const int BREAKPOINTLINE_MARGIN = 4;
        private const int BREAKPOINTLINE_MARKER = 4;

        private const int RUN_MARGIN = 5;
        private const int RUN_MARKER = 5;

        /// <summary>
        /// change this to whatever margin you want the code folding tree (+/-) to show in
        /// </summary>
        private const int FOLDING_MARGIN = 6;
        /// <summary>
        /// set this true to show circular buttons for code folding (the [+] and [-] buttons on the margin)
        /// </summary>
        private const bool CODEFOLDING_CIRCULAR = false;

        #endregion

        private List<string> mlstKeyWord = new List<string>();
        private List<string> mlstKeyWordTemp = new List<string>();
        public List<string> KeyWord
        {
            get
            {
                return this.mlstKeyWord;
            }
            set
            {
                this.mlstKeyWord = value;
            }
        }
        private string mFilePath = string.Empty;
        public string FilePath
        {
            get
            {
                return this.mFilePath;
            }
            set
            {
                this.mFilePath=value;
            }
        }
        private string mTextTemp = string.Empty;

        #region 初始化

        public tbScintilla()
        {
            this.WrapMode = WrapMode.None;
            this.IndentationGuides = IndentView.LookBoth;
            this.SetSelectionBackColor(true, IntToColor(0x114D9C));
            this.InitControls();
            this.ClearCmdKey(Keys.Control | Keys.S);
        }

        private void InitControls()
        {
            // NUMBER MARGIN
            this.InitNumberMargin();

            // BOOKMARK MARGIN
            this.InitBookMarkMargin();

            this.InitBreakPointMargin();

            this.InitBreakPointLineMargin();

            this.InitRunMargin();

            // CODE FOLDING MARGIN
            this.InitCodeFolding();

            // DRAG DROP
            this.InitDragDropFile();

            // CODE Font Color
            this.InitCodeColor();

            //调试模式选中行的颜色
            this.InitLineColor();
        }

        //选中行的效果
        private void InitLineColor()
        {
            this.CaretLineVisible = true;
            this.CaretLineBackColor = Color.White;
            this.CaretLineBackColorAlpha = 50;
        }

        #region 初始化 书签 断点 运行代码样式

        private void InitNumberMargin()
        {
            var nums = this.Margins[NUMBER_MARGIN];
            nums.Width = 36;
            nums.Type = MarginType.Number;
            nums.Sensitive = true;
            nums.Mask = 0;
        }

        private void InitBookMarkMargin()
        {
            var margin = this.Margins[BOOKMARK_MARGIN];
            margin.Width = 15;
            margin.Sensitive = true;
            margin.Type = MarginType.Symbol;
            margin.Mask = (1 << BOOKMARK_MARKER);
            //margin.Cursor = MarginCursor.Arrow;

            var marker = this.Markers[BOOKMARK_MARKER];
            marker.Symbol = MarkerSymbol.Bookmark;
            marker.SetBackColor(Color.DarkGray);
            marker.SetAlpha(100);
        }

        private void InitBreakPointMargin()
        {
            var marginLine = this.Margins[BREAKPOINT_MARGIN];
            marginLine.Width = 15;
            marginLine.Sensitive = true;
            marginLine.Type = MarginType.Symbol;
            marginLine.Mask = (1 << BREAKPOINT_MARGIN);

            var markerLine = this.Markers[BREAKPOINT_MARKER];
            markerLine.Symbol = MarkerSymbol.Circle;
            markerLine.SetBackColor(IntToColor(0xFF003B));
            markerLine.SetForeColor(IntToColor(0x000000));
            markerLine.SetAlpha(100);
        }

        private void InitBreakPointLineMargin()
        {
            var marginLine = this.Margins[BREAKPOINTLINE_MARGIN];
            marginLine.Width = 10;
            marginLine.Sensitive = true;
            marginLine.Type = MarginType.Symbol;
            marginLine.Mask = (1 << BREAKPOINTLINE_MARGIN);

            var markerLine = this.Markers[BREAKPOINTLINE_MARKER];
            markerLine.Symbol = MarkerSymbol.Circle;
            markerLine.SetBackColor(IntToColor(0xFF003B));
            markerLine.SetForeColor(IntToColor(0x000000));
            markerLine.SetAlpha(100);
        }

        private void InitRunMargin()
        {
            var marginLine = this.Margins[RUN_MARGIN];
            marginLine.Width = 10;
            marginLine.Sensitive = true;
            marginLine.Type = MarginType.Symbol;
            marginLine.Mask = (1 << RUN_MARGIN);

            var markerLine = this.Markers[RUN_MARKER];
            markerLine.Symbol = MarkerSymbol.Circle;
            markerLine.SetBackColor(IntToColor(0xFFF181));
            markerLine.SetForeColor(IntToColor(0x000000));
            markerLine.SetAlpha(100);
        }

        #endregion

        private void InitCodeFolding()
        {
            this.SetFoldMarginColor(true, IntToColor(BACK_COLOR));
            this.SetFoldMarginHighlightColor(true, IntToColor(BACK_COLOR));

            // Enable code folding
            this.SetProperty("fold", "1");
            this.SetProperty("fold.compact", "1");

            // Configure a margin to display folding symbols
            this.Margins[FOLDING_MARGIN].Type = MarginType.Symbol;
            this.Margins[FOLDING_MARGIN].Mask = Marker.MaskFolders;
            this.Margins[FOLDING_MARGIN].Sensitive = true;
            this.Margins[FOLDING_MARGIN].Width = 20;

            // Set colors for all folding markers
            for (int i = 25; i <= 31; i++)
            {
                this.Markers[i].SetForeColor(IntToColor(BACK_COLOR)); // styles for [+] and [-]
                this.Markers[i].SetBackColor(IntToColor(FORE_COLOR)); // styles for [+] and [-]
            }

            // Configure folding markers with respective symbols
            this.Markers[Marker.Folder].Symbol = CODEFOLDING_CIRCULAR ? MarkerSymbol.CirclePlus : MarkerSymbol.BoxPlus;
            this.Markers[Marker.FolderOpen].Symbol = CODEFOLDING_CIRCULAR ? MarkerSymbol.CircleMinus : MarkerSymbol.BoxMinus;
            this.Markers[Marker.FolderEnd].Symbol = CODEFOLDING_CIRCULAR ? MarkerSymbol.CirclePlusConnected : MarkerSymbol.BoxPlusConnected;
            this.Markers[Marker.FolderMidTail].Symbol = MarkerSymbol.TCorner;
            this.Markers[Marker.FolderOpenMid].Symbol = CODEFOLDING_CIRCULAR ? MarkerSymbol.CircleMinusConnected : MarkerSymbol.BoxMinusConnected;
            this.Markers[Marker.FolderSub].Symbol = MarkerSymbol.VLine;
            this.Markers[Marker.FolderTail].Symbol = MarkerSymbol.LCorner;

            // Enable automatic folding
            this.AutomaticFold = (AutomaticFold.Show | AutomaticFold.Click | AutomaticFold.Change);
        }

        private void InitDragDropFile()
        {
            this.AllowDrop = true;
            this.DragEnter += delegate (object sender, DragEventArgs e)
            {
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                    e.Effect = DragDropEffects.Copy;
                else
                    e.Effect = DragDropEffects.None;
            };
            this.DragDrop += delegate (object sender, DragEventArgs e)
            {
                // get file drop
                if (e.Data.GetDataPresent(DataFormats.FileDrop))
                {
                    Array a = (Array)e.Data.GetData(DataFormats.FileDrop);
                    if (a != null)
                    {

                        string path = a.GetValue(0).ToString();

                        this.LoadFile(path);
                    }
                }
            };
        }

        private void InitCodeColor()
        {
            //输入点光标颜色
            this.CaretForeColor = Color.White;
            //Configure the default style
            
            this.StyleResetDefault();

            this.Styles[Style.Default].Font = "Consolas";
            this.Styles[Style.Default].Size = 11;
            this.Styles[Style.Default].BackColor = IntToColor(0x212121);
            this.Styles[Style.Default].ForeColor = IntToColor(0xFFFFFF);

            this.StyleClearAll();

            this.Styles[Style.LineNumber].BackColor = IntToColor(BACK_COLOR);
            this.Styles[Style.LineNumber].ForeColor = IntToColor(FORE_COLOR);
            this.Styles[Style.IndentGuide].ForeColor = IntToColor(FORE_COLOR);
            this.Styles[Style.IndentGuide].BackColor = IntToColor(BACK_COLOR);

            if (this.Lexer == Lexer.Lua)
            {
                // Configure the Lua lexer styles
                this.Styles[Style.Lua.Identifier].ForeColor = IntToColor(0xD0DAE2);
                this.Styles[Style.Lua.Comment].ForeColor = IntToColor(0xBD758B);
                this.Styles[Style.Lua.CommentLine].ForeColor = IntToColor(0x868686);
                this.Styles[Style.Lua.CommentDoc].ForeColor = IntToColor(0x2FAE35);
                this.Styles[Style.Lua.Number].ForeColor = IntToColor(0xFFA500);
                this.Styles[Style.Lua.String].ForeColor = IntToColor(0xFFFF00);
                this.Styles[Style.Lua.Character].ForeColor = IntToColor(0xE95454);
                this.Styles[Style.Lua.Preprocessor].ForeColor = IntToColor(0x8AAFEE);
                this.Styles[Style.Lua.Operator].ForeColor = IntToColor(0xE0E0E0);

                this.Styles[Style.Lua.Word].ForeColor = IntToColor(0x93C763);
                this.Styles[Style.Lua.Word2].ForeColor = IntToColor(0xAAFFFF);
                this.Styles[Style.Lua.Word3].ForeColor = IntToColor(0xA082BD);
                this.Styles[Style.Lua.Word4].ForeColor = IntToColor(0x5F9EA0);
                this.Styles[Style.Lua.Word5].ForeColor = IntToColor(0x7FFF00);
                this.Styles[Style.Lua.Word6].ForeColor = IntToColor(0xD2691E);
                this.Styles[Style.Lua.Word7].ForeColor = IntToColor(0xFF7F50);
                this.Styles[Style.Lua.Word8].ForeColor = IntToColor(0x6495ED);
                this.Styles[Style.Lua.Default].ForeColor = IntToColor(0xF98906);
                this.Styles[Style.Lua.StringEol].ForeColor = IntToColor(0xF98906);
                this.Styles[Style.Lua.LiteralString].ForeColor = IntToColor(0xF98906);
                this.Styles[Style.Lua.Label].ForeColor = IntToColor(0xF98906);
            }
            else if (this.Lexer == Lexer.Json)
            {
                this.Styles[Style.Json.Default].ForeColor = IntToColor(0xF98906);
                this.Styles[Style.Json.Number].ForeColor = IntToColor(0xFFFF00);
                this.Styles[Style.Json.String].ForeColor = IntToColor(0xFFFF00);
                this.Styles[Style.Json.StringEol].ForeColor = IntToColor(0xF98906);
                this.Styles[Style.Json.PropertyName].ForeColor = IntToColor(0xAAFFFF);
                this.Styles[Style.Json.EscapeSequence].ForeColor = IntToColor(0xAAFFFF);
                this.Styles[Style.Json.LineComment].ForeColor = IntToColor(0xAAFFFF);
                this.Styles[Style.Json.BlockComment].ForeColor = IntToColor(0xBD758B);
                this.Styles[Style.Json.Operator].ForeColor = IntToColor(0xE0E0E0);
                this.Styles[Style.Json.Uri].ForeColor = IntToColor(0xAAFFFF);
                this.Styles[Style.Json.CompactIRI].ForeColor = IntToColor(0xAAFFFF);
                this.Styles[Style.Json.Keyword].ForeColor = IntToColor(0xAAFFFF);
                this.Styles[Style.Json.LdKeyword].ForeColor = IntToColor(0xAAFFFF);
                this.Styles[Style.Json.Error].ForeColor = IntToColor(0xA082BD);
            }
            else
            {
                // Configure the CPP (C#) lexer styles
                this.Styles[Style.Cpp.Identifier].ForeColor = IntToColor(0xD0DAE2);
                this.Styles[Style.Cpp.Comment].ForeColor = IntToColor(0xBD758B);
                this.Styles[Style.Cpp.CommentLine].ForeColor = IntToColor(0x40BF57);
                this.Styles[Style.Cpp.CommentDoc].ForeColor = IntToColor(0x2FAE35);
                this.Styles[Style.Cpp.Number].ForeColor = IntToColor(0xFFFF00);
                this.Styles[Style.Cpp.String].ForeColor = IntToColor(0xFFFF00);
                this.Styles[Style.Cpp.Character].ForeColor = IntToColor(0xE95454);
                this.Styles[Style.Cpp.Preprocessor].ForeColor = IntToColor(0x8AAFEE);
                this.Styles[Style.Cpp.Operator].ForeColor = IntToColor(0xE0E0E0);
                this.Styles[Style.Cpp.Regex].ForeColor = IntToColor(0xff00ff);
                this.Styles[Style.Cpp.CommentLineDoc].ForeColor = IntToColor(0x77A7DB);
                this.Styles[Style.Cpp.Word].ForeColor = IntToColor(0x48A8EE);
                this.Styles[Style.Cpp.Word2].ForeColor = IntToColor(0xF98906);
                this.Styles[Style.Cpp.CommentDocKeyword].ForeColor = IntToColor(0xB3D991);
                this.Styles[Style.Cpp.CommentDocKeywordError].ForeColor = IntToColor(0xFF0000);
                this.Styles[Style.Cpp.GlobalClass].ForeColor = IntToColor(0x48A8EE);
            }
            this.InitCodeFolding();
            this.InitKeywords();
        }

        private void InitKeywords()
        {
            this.mlstKeyWord.Clear();
            if (this.Lexer == Lexer.Lua)
            {
                this.SetKeywords(0, "and break do else elseif end for function nil if in local not or repeat return then until while true false");
                this.SetKeywords(1, "_ALERT _ERRORMESSAGE _G _INPUT _OUTPUT _PROMPT _STDERR _STDIN _STDOUT _VERSION string table mod call foreach string select print exp coroutine assert collectgarbage date error getfenv getmetatable next pcall select setfenv setmetatable time type xpcall abs acos asin atan atan2 ceil cos deg exp floor frexp ldexp log log10 max min mod rad random randomseed sin sqrt tan format gsub strbyte strchar strfind strlen strlower strrep strsub strupper tonumber tostring foreach foreachi getn ipairs  pairs sort tinsert tremove");
                this.SetKeywords(2, "+ - * / % ^ # ~ < > / = ( ) { } [ ] : ; , .");

                this.mlstKeyWord.AddRange("_ALERT _ERRORMESSAGE _G _INPUT _OUTPUT _PROMPT _STDERR _STDIN _STDOUT _VERSION string table mod call foreach string select print exp coroutine assert collectgarbage date error getfenv getmetatable next pcall select setfenv setmetatable time type xpcall abs acos asin atan atan2 ceil cos deg exp floor frexp ldexp log log10 max min mod rad random randomseed sin sqrt tan format gsub strbyte strchar strfind strlen strlower strrep strsub strupper tonumber tostring foreach foreachi getn ipairs  pairs sort tinsert tremove".Split(' '));
                this.mlstKeyWord.AddRange("and break do else elseif end for function nil if in local not or repeat return then until while true false".Split(' '));
                this.mlstKeyWord.Sort();
            }
            else if (this.Lexer == Lexer.Json)
            {
            }
            else
            {
                this.SetKeywords(0, "class extends implements import interface new case do while else if for in switch throw get set function var try catch finally while with default break continue delete return each const namespace package include use is as instanceof typeof author copy default deprecated eventType example exampleText exception haxe inheritDoc internal link mtasc mxmlc param private return see serial serialData serialField since throws usage version langversion playerversion productversion dynamic private public partial static intrinsic internal native override protected AS3 final super this arguments null Infinity NaN undefined true false abstract as base bool break by byte case catch char checked class const continue decimal default delegate do double descending explicit event extern else enum false finally fixed float for foreach from goto group if implicit in int interface internal into is lock long new null namespace object operator out override orderby params private protected public readonly ref return switch struct sbyte sealed short sizeof stackalloc static string select this throw true try typeof uint ulong unchecked unsafe ushort using var virtual volatile void while where yield");
                this.SetKeywords(1, "void Null ArgumentError arguments Array Boolean Class Date DefinitionError Error EvalError Function int Math Namespace Number Object RangeError ReferenceError RegExp SecurityError String SyntaxError TypeError uint XML XMLList Boolean Byte Char DateTime Decimal Double Int16 Int32 Int64 IntPtr SByte Single UInt16 UInt32 UInt64 UIntPtr Void Path File System Windows Forms ScintillaNET");

                this.mlstKeyWord.AddRange("class extends implements import interface new case do while else if for in switch throw get set function var try catch finally while with default break continue delete return each const namespace package include use is as instanceof typeof author copy default deprecated eventType example exampleText exception haxe inheritDoc internal link mtasc mxmlc param private return see serial serialData serialField since throws usage version langversion playerversion productversion dynamic private public partial static intrinsic internal native override protected AS3 final super this arguments null Infinity NaN undefined true false abstract as base bool break by byte case catch char checked class const continue decimal default delegate do double descending explicit event extern else enum false finally fixed float foreach from goto group if implicit in int interface internal into is lock long new null namespace object operator out override orderby params private protected public readonly ref return switch struct sbyte sealed short sizeof stackalloc static string select this throw true try typeof uint ulong unchecked unsafe ushort using var virtual volatile void while where yield".Split(' '));
                this.mlstKeyWord.AddRange("void Null ArgumentError arguments Array Boolean Class Date DefinitionError Error EvalError Function Math Namespace Number Object RangeError ReferenceError RegExp SecurityError String SyntaxError TypeError uint XML XMLList Boolean Byte Char DateTime Decimal Double Int16 Int32 Int64 IntPtr SByte Single UInt16 UInt32 UInt64 UIntPtr Void Path File System Windows Forms ScintillaNET".Split(' '));
                this.mlstKeyWord.Sort();
            }
            this.mlstKeyWordTemp.AddRange(this.mlstKeyWord);
        }

        private Color IntToColor(int rgb)
        {
            return Color.FromArgb(255, (byte)(rgb >> 16), (byte)(rgb >> 8), (byte)rgb);
        }

        #endregion

        #region Uppercase / Lowercase

        public void Lowercase()
        {
            // save the selection
            int start = this.SelectionStart;
            int end = this.SelectionEnd;

            // modify the selected text
            this.ReplaceSelection(this.GetTextRange(start, end - start).ToLower());

            // preserve the original selection
            this.SetSelection(start, end);
        }

        public void Uppercase()
        {
            // save the selection
            int start = this.SelectionStart;
            int end = this.SelectionEnd;

            // modify the selected text
            this.ReplaceSelection(this.GetTextRange(start, end - start).ToUpper());

            // preserve the original selection
            this.SetSelection(start, end);
        }

        #endregion

        #region Override Event

        protected override void OnBeforeDelete(BeforeModificationEventArgs e)
        {
            base.OnBeforeDelete(e);
        }

        protected override void OnDelete(ModificationEventArgs e)
        {
            base.OnDelete(e);
        }

        protected override void OnBeforeInsert(BeforeModificationEventArgs e)
        {
            base.OnBeforeInsert(e);
            if (this.Lexer == Lexer.Lua)
            {
                this.mTextTemp = this.Text;
            }
        }

        protected override void OnInsertCheck(InsertCheckEventArgs e)
        {
            base.OnInsertCheck(e);
        }

        protected override void OnCharAdded(CharAddedEventArgs e)
        {
            base.OnCharAdded(e);
            string strCurrentWord = this.GetWordFromPosition(this.CurrentPosition);
            if (string.IsNullOrEmpty(strCurrentWord))
                return;

            if (this.Lexer == Lexer.Lua)
            {
                this.mlstKeyWordTemp.Clear();
                this.mlstKeyWordTemp.AddRange((new Regex(@"\b\W+\b")).Split(this.mTextTemp).ToArray());
                this.mlstKeyWordTemp.AddRange(this.mlstKeyWord);
                this.mlstKeyWordTemp.Sort();
            }

            string strKey = "";
            string strContainKey = "";
            foreach (string item in this.mlstKeyWordTemp.Distinct<string>())
            {
                if (item.StartsWith("0") || item.StartsWith("1") || item.StartsWith("2") || item.StartsWith("3") || item.StartsWith("4") || item.StartsWith("5") || item.StartsWith("6") || item.StartsWith("7") || item.StartsWith("8") || item.StartsWith("9"))
                    continue;

                if (item.StartsWith(strCurrentWord))
                {
                    strKey += string.Format(" {0}", item);
                    continue;
                }

                if (item.Contains(strCurrentWord))
                    strContainKey += string.Format(" {0}", item);

            }

            //以当前字母开头的放上面，包含字母的放后面。
            strKey += strContainKey;
            if (string.IsNullOrEmpty(strKey.Trim()))
                return;

            int intStart = 0;
            if (strCurrentWord.Length > 1)
            {
                intStart = strCurrentWord.Length;
            }
            this.AutoCShow(intStart, strKey.TrimStart(' '));
        }

        protected override void OnAutoCCompleted(AutoCSelectionEventArgs e)
        {
            base.OnAutoCCompleted(e);
            if (e.ListCompletionMethod == ListCompletionMethod.Tab && e.Text == "try")
            {
                this.InsertText(this.CurrentPosition, "\r\n{}\r\ncatch{}");
            }
        }

        protected override void OnMarginClick(MarginClickEventArgs e)
        {
            base.OnMarginClick(e);
            if (e.Margin == BREAKPOINT_MARGIN)
            {
                this.SetBreadPoint(this.LineFromPosition(e.Position));
            }
            else if (e.Margin == BOOKMARK_MARGIN)
            {
                this.SetBookMark(this.LineFromPosition(e.Position));
            }
        }

        protected override void OnKeyDown(KeyEventArgs e)
        {
            base.OnKeyDown(e);
            if (e.KeyCode == Keys.S && e.Control)
            {
                this.SaveFile();
            }
        }

        #endregion

        #region File load create Save

        public void LoadFile(string strPath)
        {
            try
            {
                if (File.Exists(strPath))
                {
                    this.Lexer = Lexer.Cpp;
                    if (Path.GetExtension(strPath).ToLower() == ".lua")
                        this.Lexer = Lexer.Lua;
                    else if(Path.GetExtension(strPath).ToLower() == ".json")
                        this.Lexer = Lexer.Cpp;

                    this.Text = File.ReadAllText(strPath);
                    this.mFilePath = strPath;
                    this.InitCodeColor();
                }
            }
            catch (Exception ex)
            {
            }
        }

        public void CreateFile(string strPath)
        {
            try
            {
                File.Create(strPath);
                this.LoadFile(strPath);
            }
            catch (Exception ex)
            {
            }          
        }

        public void SaveFile()
        {
            try
            {
                File.WriteAllText(this.mFilePath,this.Text,Encoding.UTF8);
            }
            catch (Exception ex)
            {
            }
        }

        #endregion

        #region 书签 断点 运行代码高亮显示

        public void SetBreadPoint(int intLine)
        {
            if (intLine == -1)
                intLine = this.CurrentLine;
            // Do we have a marker for this line?
            const uint mask = (1 << BREAKPOINT_MARKER);
            var line = this.Lines[intLine];
            if ((line.MarkerGet() & mask) > 0)
            {
                // Remove existing breakpoint
                line.MarkerDelete(BREAKPOINT_MARKER);
                line.MarkerDelete(BREAKPOINTLINE_MARKER);
            }
            else
            {
                // Add breakpoint
                line.MarkerAdd(BREAKPOINT_MARKER);
                line.MarkerAdd(BREAKPOINTLINE_MARKER);
            }
        }

        public void SetBookMark(int intLine)
        {
            if (intLine == -1)
                intLine = this.CurrentLine;
            const uint mask = (1 << BOOKMARK_MARKER);
            var line = this.Lines[intLine];
            if ((line.MarkerGet() & mask) > 0)
            {
                // Remove existing bookmark
                line.MarkerDelete(BOOKMARK_MARKER);
            }
            else
            {
                // Add bookmark
                line.MarkerAdd(BOOKMARK_MARKER);
            }
        }

        public void SetRunLineLight(int intLine)
        {
            try
            {
                foreach (var item in this.Lines)
                {
                    item.MarkerDelete(RUN_MARGIN);
                }
                var lineA = this.Lines[intLine];
                lineA.MarkerAdd(RUN_MARGIN);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbScintilla_SetRunLineLight");
            }
        }

        #endregion

    }
}
