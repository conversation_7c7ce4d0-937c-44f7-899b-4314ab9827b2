﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using WebSocket4Net;

namespace tbToolkit
{
    public class tbLuaDavice : IDisposable
    {
        #region "对外事件"

        public EventHandler Connected;
        public EventHandler Disconnected;
        public EventHandler DeviceInfo;
        public EventHandler<tbLuaDaviceScreenArgs> DeviceImage;
        public EventHandler<tbLuaDaviceMessageArgs> DeviceMessage;

        #endregion

        private WebSocket mWebSocket = null;

        #region "属性"

        public string DeviceName
        {
            get;
            set;
        }

        public string IP
        {
            get;
            set;
        }

        public string Port
        {
            get;
            set;
        }

        public WebSocketState State
        {
            get
            {
                if (this.mWebSocket == null)
                    return WebSocketState.None;

                return this.mWebSocket.State;
            }
        }

        #endregion

        #region "Init"

        public tbLuaDavice(string strIP,string strPort)
        {
            this.IP = strIP;
            this.Port = strPort;
            this.InitDevice();
        }

        private void InitDevice()
        {
            try
            {
                string strUrl = string.Format("ws://{0}:{1}/{2}", this.IP, this.Port, "ws");
                List<KeyValuePair<string, string>> list = new List<KeyValuePair<string, string>>();
                this.mWebSocket = new WebSocket(strUrl, "", list, list, "", "http://www.tongbu.com", WebSocketVersion.Rfc6455);

                this.mWebSocket.Opened -= this.mWebSocket_Opened;
                this.mWebSocket.Opened += this.mWebSocket_Opened;

                this.mWebSocket.Closed -= this.mWebSocket_Closed;
                this.mWebSocket.Closed += this.mWebSocket_Closed;

                this.mWebSocket.Error -= this.mWebSocket_Error;
                this.mWebSocket.Error += this.mWebSocket_Error;

                this.mWebSocket.MessageReceived -= this.mWebSocket_MessageReceived;
                this.mWebSocket.MessageReceived += this.mWebSocket_MessageReceived;

                this.mWebSocket.DataReceived -= this.mWebSocket_DataReceived;
                this.mWebSocket.DataReceived += this.mWebSocket_DataReceived;

                this.mWebSocket.Open();

            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbLuaDevice_InitDevice");
            }
        }

        #endregion

        #region "Device"

        private void mWebSocket_DataReceived(object sender, DataReceivedEventArgs e)
        {
            try
            {
                if (e.Data == null)
                    return;
                
                string strContent= Encoding.Unicode.GetString(e.Data, 0, e.Data.Length);
                this.Response(strContent);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbLuaDevice_mWebSocket_DataReceived");
            }
        }

        private void mWebSocket_MessageReceived(object sender, MessageReceivedEventArgs e)
        {
            try
            {
                Common.Log("Lua Lan WebSocket MessageReceived: " + e.Message);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbLuaDevice_mWebSocket_MessageReceived");
            }
        }

        private void mWebSocket_Opened(object sender, EventArgs e)
        {
            Common.Log("Lua Lan WebSocket Opened");
            if (this.Connected != null)
                this.Connected(this, e);
        }

        private void mWebSocket_Closed(object sender, EventArgs e)
        {
            try
            {
                Common.Log("Lua Lan WebSocket Closed");
                if (this.Disconnected != null)
                    this.Disconnected(this, e);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbLuaDevice_mWebSocket_Closed");
            }
        }

        private void mWebSocket_Error(object sender, SuperSocket.ClientEngine.ErrorEventArgs e)
        {
            Common.LogException(e.Exception.ToString(), "tbLuaDevice_mWebSocket_Error");
        }

        #endregion

        #region "公共方法"

        /// <summary>
        /// tbToolkit使用数据流方式post数据
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <param name="urlStr">url</param>
        /// <returns>true——post数据成功</returns>
        public bool SendFile(string fileName, DataTransferEventHandler callBack,DataTransferEventArgs args)
        {
            string urlStr = string.Format("http://{0}:{1}/files",this.IP,this.Port);
            bool failture = false;   // 是否失败？
            if (File.Exists(fileName))
            {
                try
                {
                    int writenBytes = 0;

                    using (FileStream fs = new FileStream(fileName, FileMode.Open, FileAccess.Read, FileShare.Read))
                    {

                        long fLen = fs.Length;
                        if (callBack != null)
                        {
                            args.FileSize = fLen;
                        }

                        if (fLen > 0)
                        {
                            string strBoundary = string.Concat("---------------------", DateTime.Now.Ticks.ToString("x", System.Globalization.NumberFormatInfo.InvariantInfo));
                            string strHead = this.ConstructParame(fileName, strBoundary);
                            byte[] headBytes = Encoding.UTF8.GetBytes(strHead);

                            HttpWebRequest webReq = HttpWebRequest.Create(new Uri(urlStr)) as HttpWebRequest;

                            webReq.Timeout = 1000 * 1000;
                            webReq.ReadWriteTimeout = 1000 * 1000;
                            webReq.AllowWriteStreamBuffering = false;
                            webReq.KeepAlive = true;
                            webReq.Accept = "*/*";
                            webReq.Method = "POST";
                            webReq.ContentType = "multipart/form-data; boundary=" + strBoundary;
                            webReq.ContentLength = headBytes.Length + fLen;

                            try
                            {
                                using (Stream reqStream = webReq.GetRequestStream())
                                {
                                    //Write Header
                                    reqStream.Write(headBytes, 0, headBytes.Length);

                                    const int maxBytes = 2 * 1024 * 1024;
                                    byte[] buff = new byte[maxBytes];
                                    int readedBytes = 0;

                                    try
                                    {
                                        do
                                        {
                                            if (args.Cancel)
                                                break;

                                            readedBytes = fs.Read(buff, 0, buff.Length);

                                            reqStream.Write(buff, 0, readedBytes);
                                            reqStream.Flush();

                                            writenBytes += readedBytes;

                                            if (callBack != null)
                                            {
                                                args.LastTransSize = readedBytes;
                                                args.TransSize = (long)writenBytes;

                                                callBack(args);
                                            }

                                            Array.Clear(buff, 0, maxBytes);

                                        } while (readedBytes > 0);

                                        reqStream.Close();
                                    }
                                    catch
                                    {
                                        failture = true;
                                    }

                                }

                                if (!failture)
                                {
                                    // Read 或 Write数据时候，基础连接已经关闭: 接收时发生错误
                                    if (args.Cancel)
                                        failture = true;
                                    else
                                        failture = false;
                                }

                                fs.Close();

                            }
                            catch (IOException ex)
                            {
                                System.Diagnostics.Debug.Write(ex);
                                failture = true;
                            }
                            catch (Exception e)
                            {
                                System.Diagnostics.Debug.Write(e);
                                failture = true;
                            }

                            if (!failture && webReq != null)
                            {
                                try
                                {
                                    // 上传成功，返回数据
                                    HttpWebResponse webResp = (HttpWebResponse)webReq.GetResponse();
                                    Stream resStream = webResp.GetResponseStream();
                                    StreamReader sr = new StreamReader(resStream, Encoding.UTF8);
                                    string respContent = sr.ReadToEnd();
                                    sr.Close();
                                    resStream.Close();
                                    webResp.Close();

                                    failture = (string.IsNullOrEmpty(respContent) || respContent.ToLowerInvariant().Contains("err:") || respContent.Equals("[]"));
                                    if (failture)
                                        Common.LogException(respContent, "tbToolkit Post File Failure");
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.Write(ex);
                                    failture = true;
                                }
                            }
                        } // if (fLen > 0 
                    } // using FileStream

                } // end of try...
                catch (IOException ex)
                {
                    System.Diagnostics.Debug.Write(ex);
                    failture = true;
                } // end of try...catch (IOException ex)

            } // end of if (File.Exists(fileName))

            if (!failture)
            {
                if (callBack != null)
                {
                    args.Success = true;
                    callBack(args);
                }
            }

            return !failture;
        }

        public void Send(string strMessage)
        {
            this.Connect();
            this.mWebSocket.Send(strMessage);
        }

        public void Send(byte[] data, int offset, int length)
        {
            this.Connect();
            this.mWebSocket.Send(data, offset, length);
        }

        public void Send(IList<ArraySegment<byte>> segments)
        {
            this.Connect();
            this.mWebSocket.Send(segments);
        }

        public WebSocketState Connect()
        {               
            int intCount = 0;
            while (intCount<5)
            {
                intCount += 1;
                if (this.mWebSocket.State != WebSocketState.Open)
                {
                    this.mWebSocket.Open();
                    Utility.WaitSeconds(0.2);
                }
                else
                    break;
            }
            return this.mWebSocket.State;
        }

        public void Disconnect()
        {
            this.mWebSocket.Close();
        }

        #endregion

        #region "私有方法"

        /// <summary>
        /// tbToolkit传文件头信息
        /// </summary>
        /// <param name="filePath"></param>
        /// <param name="boundary"></param>
        /// <returns></returns>
        private string ConstructParame(string filePath, string boundary)
        {
            StringBuilder sb = new StringBuilder();
            try
            {
                string[] keys = new string[] { "client" };//这个参数是标识客户端用户
                string[] values = new string[] { Path.GetFileName(filePath) };//这个是参数对应的值
                for (int i = 0; i < keys.Length; i++)
                {
                    sb.Append("--");
                    sb.Append(boundary).Append("\r\n").AppendFormat("Content-Disposition: form-data; name=\"{0}\"", keys[i]).Append("\r\n\r\n");
                    sb.Append(System.Web.HttpUtility.UrlEncode(values[i])).Append("\r\n");
                }
                sb.Append("--").Append(boundary).Append("\r\n");
                sb.AppendFormat("Content-Disposition: form-data; name=\"file\"; filename=\"{0}\"", System.IO.Path.GetFileName(filePath)).Append("\r\n");
                sb.Append("Content-Type: application/octet-stream").Append("\r\n\r\n");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbLuaDevice_ConstructParame");
            }
            return sb.ToString();
        }

        private string ScriptRequest(string strFilePath,bool blnDebug)
        {
            string strReturn = string.Empty;

            JsonObject jObject = new JsonObject();
            jObject.Add("type", "Request");
            JsonObject jRequest = new JsonObject();
            JsonObject jScriptRequest = new JsonObject();
            jScriptRequest.Add("runImmediately", blnDebug ? 1 : 0);                     //1:接收完文件后立即执行, 0:不立即执行
            jScriptRequest.Add("debug", 1);                                             //1:调试状态, 0:非调试状态
            jScriptRequest.Add("filename", Path.GetFileName(strFilePath));              //文件名
            jScriptRequest.Add("fileSize", this.GetFileSize(strFilePath));              //脚本包大小(base64前原始大小)
            jScriptRequest.Add("fileContent", this.GetFileContents(strFilePath));       //脚本包base64编码

            jRequest.Add("RequestType", "ScriptRequest");
            jRequest.Add("ScriptRequest", jScriptRequest);
            jObject.Add("Request", jRequest);

            JsonWriter writer = new JsonWriter();
            jObject.Write(writer);

            strReturn = writer.ToString();
            return strReturn;
        }

        private long GetFileSize(string strFilePath)
        {
            long lngSize = 0;
            try
            {
                FileInfo info = new FileInfo(strFilePath);
                lngSize = info.Length;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbLuaDevice_GetFileSize");
            }
            return lngSize;
        }

        private string GetFileContents(string strFilePath)
        {
            string strContent = string.Empty;
            try
            {
                FileStream fSream = new FileStream(strFilePath, FileMode.Open);
                byte[] fileByte = new byte[fSream.Length];

                //调用read读取方法
                fSream.Read(fileByte, 0, fileByte.Length);
                strContent = Convert.ToBase64String(fileByte);
                fSream.Close();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "tbLuaDevice_GetFileContents");
            }
            return strContent;
        }

        private string LogRequest(bool blnDebug)
        {
            string strReturn = string.Empty;

            JsonObject jObject = new JsonObject();
            jObject.Add("type", "Request");
            JsonObject jRequest = new JsonObject();
            JsonObject jLogRequest = new JsonObject();
            jLogRequest.Add("level", 1);                            //debug:0, info:1, error:2
            jLogRequest.Add("msg", (blnDebug ? 1:0));               //1:调试状态, 0:非调试状态

            jRequest.Add("RequestType", "LogRequest");
            jRequest.Add("LogRequest", jLogRequest);
            jObject.Add("Request", jRequest);

            JsonWriter writer = new JsonWriter();
            jObject.Write(writer);

            strReturn = writer.ToString();
            return strReturn;
        }

        private string ScreenRequest()
        {
            string strReturn = string.Empty;

            JsonObject jObject = new JsonObject();
            jObject.Add("type", "Request");
            JsonObject jRequest = new JsonObject();
            jRequest.Add("RequestType", "ScreenRequest");
            jObject.Add("Request", jRequest);
            JsonWriter writer = new JsonWriter();
            jObject.Write(writer);

            strReturn = writer.ToString();
            return strReturn;
        }

        private string DeviceinfoRequest()
        {
            string strReturn = string.Empty;

            JsonObject jObject = new JsonObject();
            jObject.Add("type", "Request");
            JsonObject jRequest = new JsonObject();
            jRequest.Add("RequestType", "DeviceinfoRequest");
            jObject.Add("Request", jRequest);
            JsonWriter writer = new JsonWriter();
            jObject.Write(writer);
            strReturn = writer.ToString();
            return strReturn;
        }

        private string StopRequest()
        {
            string strReturn = string.Empty;

            JsonObject jObject = new JsonObject();
            jObject.Add("type", "Request");
            JsonObject jRequest = new JsonObject();
            jRequest.Add("RequestType", "StopRequest");
            jObject.Add("Request", jRequest);
            JsonWriter writer = new JsonWriter();
            jObject.Write(writer);
            strReturn = writer.ToString();
            return strReturn;
        }

        private void Response(string strContent)
        {
            JsonObject jObject = JsonParser.ParseString(strContent);

            if (jObject == null || !jObject.ContainsKey("type") || jObject["type"] is JsonNull)
                return;

            string strType = (jObject["type"] as JsonString).Value;
            if (strType != "Response")
                return;

            if (!jObject.ContainsKey("Response") || jObject["Response"] is JsonNull)
                return;

            JsonObject jResponse = (JsonObject)(jObject["Response"] as JsonObject);
            if (jResponse == null || !jResponse.ContainsKey("ResponseType") || jResponse["ResponseType"] is JsonNull)
                return;

            string strResponseType = (jResponse["ResponseType"] as JsonString).Value;
            if (strResponseType == "NormalResponse")
            {
                this.NormalResponse(jResponse);
            }
            else if (strResponseType == "ScreenResponse")
            {
                this.ScreenResponse(jResponse);
            }
            else if (strResponseType == "DeviceInfoResponse")
            {
                this.DeviceInfoResponse(jResponse);
            }
        }

        private void NormalResponse(JsonObject jResponse)
        {
            if (!jResponse.ContainsKey("NormalResponse") || jResponse["NormalResponse"] is JsonNull)
                return;

            JsonObject jNormalResponse = (JsonObject)(jResponse["NormalResponse"] as JsonObject);
            tbLuaDaviceMessageArgs args = new tbLuaDaviceMessageArgs();
            if (!jNormalResponse.ContainsKey("code") || jNormalResponse["code"] is JsonNull)
            {
                int intCode = (int)(jNormalResponse["code"] as JsonNumber).Value;
                args.Code = intCode;
            }
            if (!jNormalResponse.ContainsKey("msg") || jNormalResponse["msg"] is JsonNull)
            {
                string strMsg = (jNormalResponse["msg"] as JsonString).Value;
                args.Message = strMsg;
                if (this.DeviceMessage != null)
                    this.DeviceMessage(this, args);
            }
        }

        private void ScreenResponse(JsonObject jResponse)
        {
            if (!jResponse.ContainsKey("ScreenResponse") || jResponse["ScreenResponse"] is JsonNull)
                return;

            JsonObject jScreenResponse = (JsonObject)(jResponse["ScreenResponse"] as JsonObject);
            tbLuaDaviceScreenArgs args = new tbLuaDaviceScreenArgs();
            if (!jScreenResponse.ContainsKey("imgSize") || jScreenResponse["imgSize"] is JsonNull)
            {
                uint intSize = (uint)(jScreenResponse["imgSize"] as JsonNumber).Value;
                args.Size = intSize;
            }
            if (!jScreenResponse.ContainsKey("img") || jScreenResponse["img"] is JsonNull)
            {
                string strImage = (jScreenResponse["img"] as JsonString).Value;
                args.ScreenImage = this.ConvertImage(strImage);
                if (this.DeviceImage != null)
                    this.DeviceImage(this, args);
            }
        }

        private void DeviceInfoResponse(JsonObject jResponse)
        {
            if (!jResponse.ContainsKey("DeviceInfoResponse") || jResponse["DeviceInfoResponse"] is JsonNull)
                return;

            JsonObject jDeviceInfoResponse = (JsonObject)(jResponse["DeviceInfoResponse"] as JsonObject);

            if (!jDeviceInfoResponse.ContainsKey("deviceName") || jDeviceInfoResponse["deviceName"] is JsonNull)
            {
                string strDeviceName = (jDeviceInfoResponse["deviceName"] as JsonString).Value;
                this.DeviceName = strDeviceName;
                if (this.DeviceInfo != null)
                    this.DeviceInfo(this, new EventArgs());
            }
        }

        private Image ConvertImage(string strImageContent)
        {
            byte[] imageBytes = Convert.FromBase64String(strImageContent);
            MemoryStream memoryStream = new MemoryStream(imageBytes, 0, imageBytes.Length);
            memoryStream.Write(imageBytes, 0, imageBytes.Length);
            //转成图片
            Image image = Image.FromStream(memoryStream);
            return image;
        }

        #endregion

        #region "公共方法"

        public void SendFile(string strFilePath ,bool blnDebug)
        {
            string strJson=this.ScriptRequest(strFilePath, blnDebug);
            this.Send(strJson);
        }

        #endregion


        public void Dispose()
        {
            if (this.mWebSocket != null)
            {
                this.mWebSocket.Opened -= mWebSocket_Opened;
                this.mWebSocket.Closed -= mWebSocket_Closed;
                this.mWebSocket.Error -= mWebSocket_Error;
                this.mWebSocket.MessageReceived -= mWebSocket_MessageReceived;
                this.mWebSocket.DataReceived -= mWebSocket_DataReceived;
                try
                {
                    if (this.mWebSocket.State == WebSocketState.Open ||
                        this.mWebSocket.State == WebSocketState.Connecting)
                    {
                        this.mWebSocket.Close();
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "tbLuaDevice_Dispose");
                }
                finally
                {
                    this.mWebSocket = null;
                }
            }
        }
    }

    public class tbLuaDaviceScreenArgs:EventArgs
    {
        public uint Size
        {
            get;
            set;
        }

        public Image ScreenImage
        {
            get;
            set;
        }
    }

    public class tbLuaDaviceMessageArgs : EventArgs
    {
        public int Code
        {
            get;
            set;
        }

        public string Message
        {
            get;
            set;
        }
    }
}
