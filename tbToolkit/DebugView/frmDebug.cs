﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace tbToolkit
{
    public partial class frmDebug : tbBaseGuiForm
    {
        private frmCodeManage mCodeManage = null;
        private CallBackFnction mCallBackFun = new CallBackFnction();
        public CallBackFnction CallBackFun
        {
            get
            {
                return this.mCallBackFun;
            }
        }
        
        #region  初始化

        public frmDebug()
        {
            InitializeComponent();
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.mCallBackFun.debug_errorHandle += this.mCallBackFun_debug_errorHandle;
            this.mCallBackFun.debug_printLog += this.mCallBackFun_debug_printLog;
            this.mCallBackFun.debug_updateDebugStatus += this.mCallBackFun_debug_updateDebugStatus;
            this.mCallBackFun.debug_runScript += this.mCallBackFun_debug_runScript;
            this.mCallBackFun.debug_updateStackInfo += this.mCallBackFun_debug_updateStackInfo;
            this.mCallBackFun.debug_updateVaulesInfo += this.mCallBackFun_debug_updateVaulesInfo;

            this.mCodeManage = MainForm.GetMainForm().CodeManageForm;
        }

        protected override void SetInterface()
        {
            base.SetInterface();
        }

        #endregion

        #region Spliter

        private Point m_MousePoint = new Point(0, 0);
        private bool m_MouseDown = false;
        private int m_MinWidth = 100;

        private void pnlSplit_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.m_MousePoint = new Point(e.X, e.Y);
                this.m_MouseDown = true;
            }
        }

        private void pnlSplit_MouseMove(object sender, MouseEventArgs e)
        {
            if (this.m_MouseDown && e.Button == MouseButtons.Left)
            {
                int intChanged = e.X - this.m_MousePoint.X;
                int intWidth = (int)this.tlpMain.ColumnStyles[0].Width + intChanged;
                if (intWidth < m_MinWidth)
                {
                    intWidth = m_MinWidth;
                }
                else if (intWidth >= (this.tlpMain.ClientRectangle.Width - this.pnlSplit.ClientRectangle.Width - this.m_MinWidth - (int)this.tlpMain.ColumnStyles[2].Width))
                {
                    intWidth = (this.tlpMain.ClientRectangle.Width - this.pnlSplit.ClientRectangle.Width - this.m_MinWidth - (int)this.tlpMain.ColumnStyles[2].Width);
                }
                this.tlpMain.ColumnStyles[0].Width = intWidth;
            }
        }

        private void pnlSplie_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.m_MouseDown = false;
            }
        }

        #endregion

        #region ---Lua测试---

        private void mCallBackFun_debug_errorHandle(int intPara)
        {
            Common.LogException(intPara.ToString(), "lua debug_errorHandle");
            this.SetDebugInfo(intPara.ToString());
        }

        private void mCallBackFun_debug_printLog(string strMsg)
        {
            //tbLua.ide_delAllBreakpoints();
            tbLua.ide_breakpoint("main.lua", 5, true);
            Common.LogException(strMsg, "lua debug_printLog");
            this.SetDebugInfo(strMsg);
        }

        private void mCallBackFun_debug_updateDebugStatus(int intPara)
        {
            //Common.LogException(intPara.ToString(), "lua debug_updateDebugStatus");
            //this.SetDebugInfo(intPara.ToString());
        }

        private bool mCallBackFun_debug_runScript()
        {
            return true;
        }

        private void mCallBackFun_debug_updateStackInfo(string strStack)
        {
            Common.LogException(strStack, "lua debug_updateStackInfo");
            this.SetDebugInfo(strStack);

            string[] arr= strStack.Split( new char[] { '(',')'},StringSplitOptions.RemoveEmptyEntries);
            string strPath = string.Empty;
            string strLine = string.Empty;
            foreach (var item in arr)
            {
                if (item.StartsWith("defined in"))
                {
                    strPath= item.Replace("defined in", "");
                }
                if (item.StartsWith("main chunk at line"))
                {
                    strLine = item.Replace("main chunk at line","");
                }
            }
        }

        private void mCallBackFun_debug_updateVaulesInfo(string strVaules)
        {
            Common.LogException(strVaules, "lua debug_updateVaulesInfo");
            this.SetValue(strVaules);
        }

        #endregion

        #region  button事件

        private void btnOver_Click(object sender, EventArgs e)
        {
            try
            {
                tbLua.ide_over();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmDebug_btnOver_Click");
            }
        }

        private void btnInto_Click(object sender, EventArgs e)
        {
            try
            {
                tbLua.ide_step();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmDebug_btnInto_Click");
            }
        }

        private void btnOut_Click(object sender, EventArgs e)
        {
            try
            {
                tbLua.ide_out();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmDebug_btnOut_Click");
            }
        }

        private void btnDeletePoint_Click(object sender, EventArgs e)
        {
            try
            {
                tbLua.ide_delAllBreakpoints();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmDebug_btnDeletePoint_Click");
            }
        }

        private void btnContinue_Click(object sender, EventArgs e)
        {
            try
            {
                tbLua.ide_run();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmDebug_btnContinue_Click");
            }  
        }

        private void btnStop_Click(object sender, EventArgs e)
        {
            try
            {
                tbLua.ide_stopDebug();
                tbLua.CleanLuaBridge();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmDebug_btnStop_Click");
            }
        }

        #endregion

        public void SetDebugInfo(string strInfo)
        {
            string strMessage = string.Format("[{0}]{1}", DateTime.Now.ToString("HH:mm:ss"), strInfo);
            if (this.txtInfo.Text.Length > 0)
            {
                strMessage = "\r\n" + strMessage;
            }
            this.txtInfo.AppendText(strMessage);
            this.txtInfo.Focus();
            //设置光标的位置到文本尾 
            this.txtInfo.Select(this.txtInfo.TextLength, 0);
            //滚动到控件光标处 
            this.txtInfo.ScrollToCaret();
        }

        public void SetValue(string strInfo)
        {
            this.txtValue.Text= strInfo;
        }
    }
}
