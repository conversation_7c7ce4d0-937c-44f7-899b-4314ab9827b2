﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using ScintillaNET;

namespace tbToolkit
{
    internal class SearchManager
    {
        public static ScintillaNET.Scintilla mCodeTextArea;
        public static TextBox SearchBox;
        public static string LastSearch = "";
        public static int LastSearchIndex;

        public static void Find(bool next, bool incremental)
        {
            bool first = LastSearch != SearchBox.Text;

            LastSearch = SearchBox.Text;
            if (LastSearch.Length > 0)
            {
                if (next)
                {
                    // SEARCH FOR THE NEXT OCCURANCE
                    // Search the document at the last search index
                    mCodeTextArea.TargetStart = LastSearchIndex - 1;
                    mCodeTextArea.TargetEnd = LastSearchIndex + (LastSearch.Length + 1);
                    mCodeTextArea.SearchFlags = SearchFlags.None;

                    // Search, and if not found..
                    if (!incremental || mCodeTextArea.SearchInTarget(LastSearch) == -1)
                    {

                        // Search the document from the caret onwards
                        mCodeTextArea.TargetStart = mCodeTextArea.CurrentPosition;
                        mCodeTextArea.TargetEnd = mCodeTextArea.TextLength;
                        mCodeTextArea.SearchFlags = SearchFlags.None;

                        // Search, and if not found..
                        if (mCodeTextArea.SearchInTarget(LastSearch) == -1)
                        {
                            // Search again from top
                            mCodeTextArea.TargetStart = 0;
                            mCodeTextArea.TargetEnd = mCodeTextArea.TextLength;

                            // Search, and if not found..
                            if (mCodeTextArea.SearchInTarget(LastSearch) == -1)
                            {

                                // clear selection and exit
                                mCodeTextArea.ClearSelections();
                                return;
                            }
                        }

                    }
                }
                else
                {
                    // SEARCH FOR THE PREVIOUS OCCURANCE
                    // Search the document from the beginning to the caret
                    mCodeTextArea.TargetStart = 0;
                    mCodeTextArea.TargetEnd = mCodeTextArea.CurrentPosition;
                    mCodeTextArea.SearchFlags = SearchFlags.None;

                    // Search, and if not found..
                    if (mCodeTextArea.SearchInTarget(LastSearch) == -1)
                    {
                        // Search again from the caret onwards
                        mCodeTextArea.TargetStart = mCodeTextArea.CurrentPosition;
                        mCodeTextArea.TargetEnd = mCodeTextArea.TextLength;

                        // Search, and if not found..
                        if (mCodeTextArea.SearchInTarget(LastSearch) == -1)
                        {
                            // clear selection and exit
                            mCodeTextArea.ClearSelections();
                            return;
                        }
                    }
                }

                // Select the occurance
                LastSearchIndex = mCodeTextArea.TargetStart;
                mCodeTextArea.SetSelection(mCodeTextArea.TargetEnd, mCodeTextArea.TargetStart);
                mCodeTextArea.ScrollCaret();
            }
            SearchBox.Focus();
        }
    }
}
