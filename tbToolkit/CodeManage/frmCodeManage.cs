﻿using iTong.Components;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace tbToolkit
{
    public partial class frmCodeManage : tbBaseGuiForm
    {
        public frmCodeManage()
        {
            InitializeComponent();
        }

        protected override void InitControls()
        {
            base.InitControls();
        }

        protected override void SetInterface()
        {
            base.SetInterface();

        }

        private tbTabPanelPage CreatePage(FileInfo info)
        {
            string strText = Path.GetFileName(info.FullName);
            tbTabPanelPage page = new tbTabPanelPage(info.FullName);
            page.CanClose = true;
            page.Text = strText;
            page.ShowCloseButton = true;
            tbScintilla scintilla = new tbScintilla();
            page.PageFrom = scintilla;
            page.PageFrom.Tag = info;
            page.Name = info.FullName;
            scintilla.LoadFile(info.FullName);
            return page;
        }

        public void LoadFile(FileInfo info)
        {
            foreach (tbTabPanelPage item in this.tplMain.Pages)
            {
                if (item.Name == info.FullName)
                {
                    this.tplMain.SelectedPage=item;
                    return;
                }
            }
            tbTabPanelPage page = this.CreatePage(info);
            this.tplMain.Pages.Add(page);
            this.tplMain.SelectedPage = page;
        }

        public void CloseCurrentPage()
        {
            if (this.tplMain.SelectedPage != null)
            {
                this.tplMain.SelectedPage.Close();
            } 
        }

        public void ClosePageByPath(string strPath)
        {
            tbTabPanelPage panel = null;
            foreach (tbTabPanelPage item in this.tplMain.Pages)
            {
                if (item.Name == strPath)
                {
                    panel = item;
                    break;
                }
            }
            if (panel != null)
                panel.Close();
        }

        public void ClosePageByProject(ProjectObject obj)
        {
            List<tbTabPanelPage> lstPage = new List<tbTabPanelPage>();
            foreach (tbTabPanelPage item in this.tplMain.Pages)
            {
                if (item.Name.StartsWith(obj.ProjectPath.TrimEnd('\\') + "\\"))
                {
                    lstPage.Add(item);
                }
            }
            foreach (tbTabPanelPage item in lstPage)
            {
                item.Close();
            }
        }

        public tbScintilla SelectedPage()
        {
            if (this.tplMain.SelectedPage == null || this.tplMain.SelectedPage.PageFrom == null || this.tplMain.SelectedPage.PageFrom.GetType() != typeof(tbScintilla))
            {
                return null;
            }
            return (tbScintilla)this.tplMain.SelectedPage.PageFrom;
        }
    }
}
