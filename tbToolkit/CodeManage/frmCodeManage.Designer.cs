﻿namespace tbToolkit
{
    partial class frmCodeManage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmCodeManage));
            this.tplMain = new iTong.Components.tbTabPanel();
            this.SuspendLayout();
            // 
            // tplMain
            // 
            this.tplMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tplMain.Location = new System.Drawing.Point(0, 0);
            this.tplMain.Margin = new System.Windows.Forms.Padding(0);
            this.tplMain.Name = "tplMain";
            this.tplMain.SelectedIndex = -1;
            this.tplMain.SelectedPage = null;
            this.tplMain.ShowBar = true;
            this.tplMain.ShowProgress = false;
            this.tplMain.Size = new System.Drawing.Size(664, 498);
            this.tplMain.TabIndex = 0;
            this.tplMain.TabStop = true;
            this.tplMain.tbBackgroundImage = null;
            this.tplMain.tbBackgroundImageSplit = "0,0,0,0";
            this.tplMain.tbBarBackgroundImage = ((System.Drawing.Image)(resources.GetObject("tplMain.tbBarBackgroundImage")));
            this.tplMain.tbBarDeviceButtonStatus = iTong.Components.tbBarDeviceButtonStatus.IPhone;
            this.tplMain.tbBarHeight = 35;
            this.tplMain.tbBarOffset = new System.Drawing.Point(0, 0);
            this.tplMain.tbBarShadowColor = System.Drawing.Color.Black;
            this.tplMain.tbBarShadowOffset = new System.Drawing.Point(0, -1);
            this.tplMain.tbBarShowDeviceButtons = false;
            this.tplMain.tbBarShowWebButtons = false;
            this.tplMain.tbBarShowWebHomeButton = false;
            this.tplMain.tbBarWebButtonsHomeEnable = false;
            this.tplMain.tbBarWebButtonsNextEnable = false;
            this.tplMain.tbBarWebButtonsPreviousEnable = false;
            this.tplMain.tbBarWebButtonsReflashEnable = false;
            this.tplMain.tbBarWebButtonsRefreshStatus = iTong.Components.tbTabPanelBar.BarRefreshButtonStatus.Refresh;
            this.tplMain.tbPageBackgroundImageSplit = "2,3,2,3";
            this.tplMain.tbPageDisplayMode = iTong.Components.tbBarPageDisplayMode.Page;
            this.tplMain.tbPageHeight = 21;
            this.tplMain.tbPageMenuItemCloseText = "关闭";
            this.tplMain.tbPageNextButtonEnable = false;
            this.tplMain.tbPageOnCenter = false;
            this.tplMain.tbPagePrevButtonEnable = false;
            this.tplMain.tbPageSpace = 2;
            // 
            // frmCodeManage
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(36)))), ((int)(((byte)(37)))), ((int)(((byte)(41)))));
            this.ClientSize = new System.Drawing.Size(664, 498);
            this.Controls.Add(this.tplMain);
            this.Name = "frmCodeManage";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.Text = "frmCodeManage";
            this.ResumeLayout(false);

        }

        #endregion

        private iTong.Components.tbTabPanel tplMain;
    }
}