﻿namespace tbToolkit
{
    partial class MainForm
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
            this.tlpMain = new System.Windows.Forms.TableLayoutPanel();
            this.pnlBottom = new System.Windows.Forms.Panel();
            this.pnlCodeMamage = new System.Windows.Forms.Panel();
            this.pnlSplitBottom = new System.Windows.Forms.Panel();
            this.pnlSplitLeft = new System.Windows.Forms.Panel();
            this.pnlSplitRight = new System.Windows.Forms.Panel();
            this.pnlLeft = new System.Windows.Forms.Panel();
            this.pnlRight = new System.Windows.Forms.Panel();
            this.pnlTop = new iTong.Components.tbPanel();
            this.btnUIReview = new iTong.Components.tbButton();
            this.btnCodeReset = new iTong.Components.tbButton();
            this.btnCheckCode = new iTong.Components.tbButton();
            this.btnScreenshot = new iTong.Components.tbButton();
            this.btnDebug = new iTong.Components.tbButton();
            this.btnRun = new iTong.Components.tbButton();
            this.btn_close = new iTong.Components.tbButton();
            this.btn_normal = new iTong.Components.tbButton();
            this.btn_minimize = new iTong.Components.tbButton();
            this.btnFile = new iTong.Components.tbButton();
            this.btnEdit = new iTong.Components.tbButton();
            this.btnProject = new iTong.Components.tbButton();
            this.btnPublish = new iTong.Components.tbButton();
            this.btnHelper = new iTong.Components.tbButton();
            this.menuFile = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiNewFile = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiNewProject = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiSaveFile = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiSaveProject = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiImportProject = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCloseCurrentPage = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiSetWorkSpace = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiExit = new System.Windows.Forms.ToolStripMenuItem();
            this.menuEdit = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiCut = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCopy = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiPus = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiSelectAll = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCancel = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiRedo = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCommented = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiFold = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiSearch = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiReplace = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiSearchCode = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiBookMark = new System.Windows.Forms.ToolStripMenuItem();
            this.添加删除书签ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.查找上一个书签ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.查找下一个书签ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.文件内查找ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiWordWrap = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiTabSizeSetting = new System.Windows.Forms.ToolStripMenuItem();
            this.menuProject = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiUIPreview = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiRun = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiRunLocal = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiRunStop = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiConnectDevice = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiScreenshort = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCheckCode = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiTypeSetting = new System.Windows.Forms.ToolStripMenuItem();
            this.menuPublish = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiExportCode = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiDeveloper = new System.Windows.Forms.ToolStripMenuItem();
            this.menuHelper = new iTong.Components.tbContextMenuStrip(this.components);
            this.tsmiDeveloperManual = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiDownloadPhoneTool = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiFeedback = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiCheckUpdate = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiLang = new System.Windows.Forms.ToolStripMenuItem();
            this.tsmiAbout = new System.Windows.Forms.ToolStripMenuItem();
            this.tlpMain.SuspendLayout();
            this.pnlTop.SuspendLayout();
            this.menuFile.SuspendLayout();
            this.menuEdit.SuspendLayout();
            this.menuProject.SuspendLayout();
            this.menuPublish.SuspendLayout();
            this.menuHelper.SuspendLayout();
            this.SuspendLayout();
            // 
            // tlpMain
            // 
            this.tlpMain.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tlpMain.ColumnCount = 5;
            this.tlpMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 209F));
            this.tlpMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 2F));
            this.tlpMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tlpMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 2F));
            this.tlpMain.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 186F));
            this.tlpMain.Controls.Add(this.pnlBottom, 2, 2);
            this.tlpMain.Controls.Add(this.pnlCodeMamage, 2, 0);
            this.tlpMain.Controls.Add(this.pnlSplitBottom, 2, 1);
            this.tlpMain.Controls.Add(this.pnlSplitLeft, 1, 0);
            this.tlpMain.Controls.Add(this.pnlSplitRight, 3, 0);
            this.tlpMain.Controls.Add(this.pnlLeft, 0, 0);
            this.tlpMain.Controls.Add(this.pnlRight, 4, 0);
            this.tlpMain.Location = new System.Drawing.Point(1, 85);
            this.tlpMain.Name = "tlpMain";
            this.tlpMain.RowCount = 3;
            this.tlpMain.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tlpMain.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 2F));
            this.tlpMain.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 180F));
            this.tlpMain.Size = new System.Drawing.Size(1178, 694);
            this.tlpMain.TabIndex = 11;
            // 
            // pnlBottom
            // 
            this.pnlBottom.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(36)))), ((int)(((byte)(37)))), ((int)(((byte)(41)))));
            this.tlpMain.SetColumnSpan(this.pnlBottom, 3);
            this.pnlBottom.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlBottom.Location = new System.Drawing.Point(211, 514);
            this.pnlBottom.Margin = new System.Windows.Forms.Padding(0);
            this.pnlBottom.Name = "pnlBottom";
            this.pnlBottom.Size = new System.Drawing.Size(967, 180);
            this.pnlBottom.TabIndex = 4;
            // 
            // pnlCodeMamage
            // 
            this.pnlCodeMamage.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(36)))), ((int)(((byte)(37)))), ((int)(((byte)(41)))));
            this.pnlCodeMamage.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlCodeMamage.Location = new System.Drawing.Point(211, 0);
            this.pnlCodeMamage.Margin = new System.Windows.Forms.Padding(0);
            this.pnlCodeMamage.Name = "pnlCodeMamage";
            this.pnlCodeMamage.Size = new System.Drawing.Size(779, 512);
            this.pnlCodeMamage.TabIndex = 34;
            // 
            // pnlSplitBottom
            // 
            this.pnlSplitBottom.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(214)))), ((int)(((byte)(229)))), ((int)(((byte)(245)))));
            this.tlpMain.SetColumnSpan(this.pnlSplitBottom, 3);
            this.pnlSplitBottom.Cursor = System.Windows.Forms.Cursors.SizeNS;
            this.pnlSplitBottom.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlSplitBottom.Location = new System.Drawing.Point(211, 512);
            this.pnlSplitBottom.Margin = new System.Windows.Forms.Padding(0);
            this.pnlSplitBottom.Name = "pnlSplitBottom";
            this.pnlSplitBottom.Size = new System.Drawing.Size(967, 2);
            this.pnlSplitBottom.TabIndex = 0;
            this.pnlSplitBottom.MouseDown += new System.Windows.Forms.MouseEventHandler(this.pnlSplitBottom_MouseDown);
            this.pnlSplitBottom.MouseMove += new System.Windows.Forms.MouseEventHandler(this.pnlSplitBottom_MouseMove);
            this.pnlSplitBottom.MouseUp += new System.Windows.Forms.MouseEventHandler(this.pnlSplitBottom_MouseUp);
            // 
            // pnlSplitLeft
            // 
            this.pnlSplitLeft.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(214)))), ((int)(((byte)(229)))), ((int)(((byte)(245)))));
            this.pnlSplitLeft.Cursor = System.Windows.Forms.Cursors.SizeWE;
            this.pnlSplitLeft.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlSplitLeft.Location = new System.Drawing.Point(209, 0);
            this.pnlSplitLeft.Margin = new System.Windows.Forms.Padding(0);
            this.pnlSplitLeft.Name = "pnlSplitLeft";
            this.tlpMain.SetRowSpan(this.pnlSplitLeft, 3);
            this.pnlSplitLeft.Size = new System.Drawing.Size(2, 694);
            this.pnlSplitLeft.TabIndex = 1;
            this.pnlSplitLeft.MouseDown += new System.Windows.Forms.MouseEventHandler(this.pnlSplitLeft_MouseDown);
            this.pnlSplitLeft.MouseMove += new System.Windows.Forms.MouseEventHandler(this.pnlSplitLeft_MouseMove);
            this.pnlSplitLeft.MouseUp += new System.Windows.Forms.MouseEventHandler(this.pnlSplitLeft_MouseUp);
            // 
            // pnlSplitRight
            // 
            this.pnlSplitRight.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(214)))), ((int)(((byte)(229)))), ((int)(((byte)(245)))));
            this.pnlSplitRight.Cursor = System.Windows.Forms.Cursors.SizeWE;
            this.pnlSplitRight.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlSplitRight.Location = new System.Drawing.Point(990, 0);
            this.pnlSplitRight.Margin = new System.Windows.Forms.Padding(0);
            this.pnlSplitRight.Name = "pnlSplitRight";
            this.pnlSplitRight.Size = new System.Drawing.Size(2, 512);
            this.pnlSplitRight.TabIndex = 2;
            this.pnlSplitRight.MouseDown += new System.Windows.Forms.MouseEventHandler(this.pnlSplitRight_MouseDown);
            this.pnlSplitRight.MouseMove += new System.Windows.Forms.MouseEventHandler(this.pnlSplitRight_MouseMove);
            this.pnlSplitRight.MouseUp += new System.Windows.Forms.MouseEventHandler(this.pnlSplitRight_MouseUp);
            // 
            // pnlLeft
            // 
            this.pnlLeft.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(36)))), ((int)(((byte)(37)))), ((int)(((byte)(41)))));
            this.pnlLeft.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlLeft.Location = new System.Drawing.Point(0, 0);
            this.pnlLeft.Margin = new System.Windows.Forms.Padding(0);
            this.pnlLeft.Name = "pnlLeft";
            this.tlpMain.SetRowSpan(this.pnlLeft, 3);
            this.pnlLeft.Size = new System.Drawing.Size(209, 694);
            this.pnlLeft.TabIndex = 3;
            // 
            // pnlRight
            // 
            this.pnlRight.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(57)))), ((int)(((byte)(57)))), ((int)(((byte)(57)))));
            this.pnlRight.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlRight.Location = new System.Drawing.Point(992, 0);
            this.pnlRight.Margin = new System.Windows.Forms.Padding(0);
            this.pnlRight.Name = "pnlRight";
            this.pnlRight.Size = new System.Drawing.Size(186, 512);
            this.pnlRight.TabIndex = 4;
            // 
            // pnlTop
            // 
            this.pnlTop.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnlTop.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(54)))), ((int)(((byte)(54)))), ((int)(((byte)(54)))));
            this.pnlTop.Controls.Add(this.btnUIReview);
            this.pnlTop.Controls.Add(this.btnCodeReset);
            this.pnlTop.Controls.Add(this.btnCheckCode);
            this.pnlTop.Controls.Add(this.btnScreenshot);
            this.pnlTop.Controls.Add(this.btnDebug);
            this.pnlTop.Controls.Add(this.btnRun);
            this.pnlTop.Location = new System.Drawing.Point(1, 42);
            this.pnlTop.Name = "pnlTop";
            this.pnlTop.Size = new System.Drawing.Size(1178, 43);
            this.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlTop.TabIndex = 12;
            this.pnlTop.tbBackgroundImage = null;
            this.pnlTop.tbShowWatermark = false;
            this.pnlTop.tbSplit = "0,0,0,0";
            this.pnlTop.tbWatermark = null;
            this.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlTop.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // btnUIReview
            // 
            this.btnUIReview.BackColor = System.Drawing.Color.Transparent;
            this.btnUIReview.BindingForm = null;
            this.btnUIReview.Location = new System.Drawing.Point(520, 1);
            this.btnUIReview.Name = "btnUIReview";
            this.btnUIReview.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnUIReview.Selectable = true;
            this.btnUIReview.Size = new System.Drawing.Size(103, 41);
            this.btnUIReview.TabIndex = 33;
            this.btnUIReview.tbAdriftIconWhenHover = false;
            this.btnUIReview.tbAutoSize = false;
            this.btnUIReview.tbAutoSizeEx = false;
            this.btnUIReview.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_4_blank;
            this.btnUIReview.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnUIReview.tbBadgeNumber = 0;
            this.btnUIReview.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnUIReview.tbEndEllipsis = false;
            this.btnUIReview.tbIconHoldPlace = true;
            this.btnUIReview.tbIconImage = null;
            this.btnUIReview.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnUIReview.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnUIReview.tbIconMore = false;
            this.btnUIReview.tbIconMouseDown = null;
            this.btnUIReview.tbIconMouseHover = null;
            this.btnUIReview.tbIconMouseLeave = null;
            this.btnUIReview.tbIconPlaceText = 2;
            this.btnUIReview.tbIconReadOnly = null;
            this.btnUIReview.tbImageMouseDown = null;
            this.btnUIReview.tbImageMouseHover = null;
            this.btnUIReview.tbImageMouseLeave = null;
            this.btnUIReview.tbProgressValue = 50;
            this.btnUIReview.tbReadOnly = false;
            this.btnUIReview.tbReadOnlyText = false;
            this.btnUIReview.tbShadow = false;
            this.btnUIReview.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnUIReview.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnUIReview.tbShowDot = false;
            this.btnUIReview.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnUIReview.tbShowMoreIconImg")));
            this.btnUIReview.tbShowNew = false;
            this.btnUIReview.tbShowProgress = false;
            this.btnUIReview.tbShowTip = true;
            this.btnUIReview.tbShowToolTipOnButton = false;
            this.btnUIReview.tbSplit = "3,3,3,3";
            this.btnUIReview.tbText = "UI预览";
            this.btnUIReview.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnUIReview.tbTextColor = System.Drawing.Color.White;
            this.btnUIReview.tbTextColorDisable = System.Drawing.Color.White;
            this.btnUIReview.tbTextColorDown = System.Drawing.Color.White;
            this.btnUIReview.tbTextColorHover = System.Drawing.Color.White;
            this.btnUIReview.tbTextMouseDownPlace = 0;
            this.btnUIReview.tbToolTip = "";
            this.btnUIReview.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnUIReview.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnUIReview.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnUIReview.VisibleEx = true;
            // 
            // btnCodeReset
            // 
            this.btnCodeReset.BackColor = System.Drawing.Color.Transparent;
            this.btnCodeReset.BindingForm = null;
            this.btnCodeReset.Location = new System.Drawing.Point(416, 1);
            this.btnCodeReset.Name = "btnCodeReset";
            this.btnCodeReset.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnCodeReset.Selectable = true;
            this.btnCodeReset.Size = new System.Drawing.Size(103, 41);
            this.btnCodeReset.TabIndex = 32;
            this.btnCodeReset.tbAdriftIconWhenHover = false;
            this.btnCodeReset.tbAutoSize = false;
            this.btnCodeReset.tbAutoSizeEx = false;
            this.btnCodeReset.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_4_blank;
            this.btnCodeReset.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnCodeReset.tbBadgeNumber = 0;
            this.btnCodeReset.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnCodeReset.tbEndEllipsis = false;
            this.btnCodeReset.tbIconHoldPlace = true;
            this.btnCodeReset.tbIconImage = null;
            this.btnCodeReset.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCodeReset.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnCodeReset.tbIconMore = false;
            this.btnCodeReset.tbIconMouseDown = null;
            this.btnCodeReset.tbIconMouseHover = null;
            this.btnCodeReset.tbIconMouseLeave = null;
            this.btnCodeReset.tbIconPlaceText = 2;
            this.btnCodeReset.tbIconReadOnly = null;
            this.btnCodeReset.tbImageMouseDown = null;
            this.btnCodeReset.tbImageMouseHover = null;
            this.btnCodeReset.tbImageMouseLeave = null;
            this.btnCodeReset.tbProgressValue = 50;
            this.btnCodeReset.tbReadOnly = false;
            this.btnCodeReset.tbReadOnlyText = false;
            this.btnCodeReset.tbShadow = false;
            this.btnCodeReset.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnCodeReset.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnCodeReset.tbShowDot = false;
            this.btnCodeReset.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnCodeReset.tbShowMoreIconImg")));
            this.btnCodeReset.tbShowNew = false;
            this.btnCodeReset.tbShowProgress = false;
            this.btnCodeReset.tbShowTip = true;
            this.btnCodeReset.tbShowToolTipOnButton = false;
            this.btnCodeReset.tbSplit = "3,3,3,3";
            this.btnCodeReset.tbText = "代码排版";
            this.btnCodeReset.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCodeReset.tbTextColor = System.Drawing.Color.White;
            this.btnCodeReset.tbTextColorDisable = System.Drawing.Color.White;
            this.btnCodeReset.tbTextColorDown = System.Drawing.Color.White;
            this.btnCodeReset.tbTextColorHover = System.Drawing.Color.White;
            this.btnCodeReset.tbTextMouseDownPlace = 0;
            this.btnCodeReset.tbToolTip = "";
            this.btnCodeReset.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnCodeReset.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnCodeReset.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCodeReset.VisibleEx = true;
            // 
            // btnCheckCode
            // 
            this.btnCheckCode.BackColor = System.Drawing.Color.Transparent;
            this.btnCheckCode.BindingForm = null;
            this.btnCheckCode.Location = new System.Drawing.Point(312, 1);
            this.btnCheckCode.Name = "btnCheckCode";
            this.btnCheckCode.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnCheckCode.Selectable = true;
            this.btnCheckCode.Size = new System.Drawing.Size(103, 41);
            this.btnCheckCode.TabIndex = 31;
            this.btnCheckCode.tbAdriftIconWhenHover = false;
            this.btnCheckCode.tbAutoSize = false;
            this.btnCheckCode.tbAutoSizeEx = false;
            this.btnCheckCode.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_4_blank;
            this.btnCheckCode.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnCheckCode.tbBadgeNumber = 0;
            this.btnCheckCode.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnCheckCode.tbEndEllipsis = false;
            this.btnCheckCode.tbIconHoldPlace = true;
            this.btnCheckCode.tbIconImage = null;
            this.btnCheckCode.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCheckCode.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnCheckCode.tbIconMore = false;
            this.btnCheckCode.tbIconMouseDown = null;
            this.btnCheckCode.tbIconMouseHover = null;
            this.btnCheckCode.tbIconMouseLeave = null;
            this.btnCheckCode.tbIconPlaceText = 2;
            this.btnCheckCode.tbIconReadOnly = null;
            this.btnCheckCode.tbImageMouseDown = null;
            this.btnCheckCode.tbImageMouseHover = null;
            this.btnCheckCode.tbImageMouseLeave = null;
            this.btnCheckCode.tbProgressValue = 50;
            this.btnCheckCode.tbReadOnly = false;
            this.btnCheckCode.tbReadOnlyText = false;
            this.btnCheckCode.tbShadow = false;
            this.btnCheckCode.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnCheckCode.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnCheckCode.tbShowDot = false;
            this.btnCheckCode.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnCheckCode.tbShowMoreIconImg")));
            this.btnCheckCode.tbShowNew = false;
            this.btnCheckCode.tbShowProgress = false;
            this.btnCheckCode.tbShowTip = true;
            this.btnCheckCode.tbShowToolTipOnButton = false;
            this.btnCheckCode.tbSplit = "3,3,3,3";
            this.btnCheckCode.tbText = "语法检测";
            this.btnCheckCode.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCheckCode.tbTextColor = System.Drawing.Color.White;
            this.btnCheckCode.tbTextColorDisable = System.Drawing.Color.White;
            this.btnCheckCode.tbTextColorDown = System.Drawing.Color.White;
            this.btnCheckCode.tbTextColorHover = System.Drawing.Color.White;
            this.btnCheckCode.tbTextMouseDownPlace = 0;
            this.btnCheckCode.tbToolTip = "";
            this.btnCheckCode.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnCheckCode.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnCheckCode.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnCheckCode.VisibleEx = true;
            // 
            // btnScreenshot
            // 
            this.btnScreenshot.BackColor = System.Drawing.Color.Transparent;
            this.btnScreenshot.BindingForm = null;
            this.btnScreenshot.Location = new System.Drawing.Point(208, 1);
            this.btnScreenshot.Name = "btnScreenshot";
            this.btnScreenshot.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnScreenshot.Selectable = true;
            this.btnScreenshot.Size = new System.Drawing.Size(103, 41);
            this.btnScreenshot.TabIndex = 30;
            this.btnScreenshot.tbAdriftIconWhenHover = false;
            this.btnScreenshot.tbAutoSize = false;
            this.btnScreenshot.tbAutoSizeEx = false;
            this.btnScreenshot.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_4_blank;
            this.btnScreenshot.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnScreenshot.tbBadgeNumber = 0;
            this.btnScreenshot.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnScreenshot.tbEndEllipsis = false;
            this.btnScreenshot.tbIconHoldPlace = true;
            this.btnScreenshot.tbIconImage = null;
            this.btnScreenshot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnScreenshot.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnScreenshot.tbIconMore = false;
            this.btnScreenshot.tbIconMouseDown = null;
            this.btnScreenshot.tbIconMouseHover = null;
            this.btnScreenshot.tbIconMouseLeave = null;
            this.btnScreenshot.tbIconPlaceText = 2;
            this.btnScreenshot.tbIconReadOnly = null;
            this.btnScreenshot.tbImageMouseDown = null;
            this.btnScreenshot.tbImageMouseHover = null;
            this.btnScreenshot.tbImageMouseLeave = null;
            this.btnScreenshot.tbProgressValue = 50;
            this.btnScreenshot.tbReadOnly = false;
            this.btnScreenshot.tbReadOnlyText = false;
            this.btnScreenshot.tbShadow = false;
            this.btnScreenshot.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnScreenshot.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnScreenshot.tbShowDot = false;
            this.btnScreenshot.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnScreenshot.tbShowMoreIconImg")));
            this.btnScreenshot.tbShowNew = false;
            this.btnScreenshot.tbShowProgress = false;
            this.btnScreenshot.tbShowTip = true;
            this.btnScreenshot.tbShowToolTipOnButton = false;
            this.btnScreenshot.tbSplit = "3,3,3,3";
            this.btnScreenshot.tbText = "抓图";
            this.btnScreenshot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnScreenshot.tbTextColor = System.Drawing.Color.White;
            this.btnScreenshot.tbTextColorDisable = System.Drawing.Color.White;
            this.btnScreenshot.tbTextColorDown = System.Drawing.Color.White;
            this.btnScreenshot.tbTextColorHover = System.Drawing.Color.White;
            this.btnScreenshot.tbTextMouseDownPlace = 0;
            this.btnScreenshot.tbToolTip = "";
            this.btnScreenshot.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnScreenshot.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnScreenshot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnScreenshot.VisibleEx = true;
            // 
            // btnDebug
            // 
            this.btnDebug.BackColor = System.Drawing.Color.Transparent;
            this.btnDebug.BindingForm = null;
            this.btnDebug.Location = new System.Drawing.Point(104, 1);
            this.btnDebug.Name = "btnDebug";
            this.btnDebug.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnDebug.Selectable = true;
            this.btnDebug.Size = new System.Drawing.Size(103, 41);
            this.btnDebug.TabIndex = 29;
            this.btnDebug.tbAdriftIconWhenHover = false;
            this.btnDebug.tbAutoSize = false;
            this.btnDebug.tbAutoSizeEx = false;
            this.btnDebug.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_4_blank;
            this.btnDebug.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnDebug.tbBadgeNumber = 0;
            this.btnDebug.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnDebug.tbEndEllipsis = false;
            this.btnDebug.tbIconHoldPlace = true;
            this.btnDebug.tbIconImage = null;
            this.btnDebug.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDebug.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnDebug.tbIconMore = false;
            this.btnDebug.tbIconMouseDown = null;
            this.btnDebug.tbIconMouseHover = null;
            this.btnDebug.tbIconMouseLeave = null;
            this.btnDebug.tbIconPlaceText = 2;
            this.btnDebug.tbIconReadOnly = null;
            this.btnDebug.tbImageMouseDown = null;
            this.btnDebug.tbImageMouseHover = null;
            this.btnDebug.tbImageMouseLeave = null;
            this.btnDebug.tbProgressValue = 50;
            this.btnDebug.tbReadOnly = false;
            this.btnDebug.tbReadOnlyText = false;
            this.btnDebug.tbShadow = false;
            this.btnDebug.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnDebug.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnDebug.tbShowDot = false;
            this.btnDebug.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnDebug.tbShowMoreIconImg")));
            this.btnDebug.tbShowNew = false;
            this.btnDebug.tbShowProgress = false;
            this.btnDebug.tbShowTip = true;
            this.btnDebug.tbShowToolTipOnButton = false;
            this.btnDebug.tbSplit = "3,3,3,3";
            this.btnDebug.tbText = "调试";
            this.btnDebug.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDebug.tbTextColor = System.Drawing.Color.White;
            this.btnDebug.tbTextColorDisable = System.Drawing.Color.White;
            this.btnDebug.tbTextColorDown = System.Drawing.Color.White;
            this.btnDebug.tbTextColorHover = System.Drawing.Color.White;
            this.btnDebug.tbTextMouseDownPlace = 0;
            this.btnDebug.tbToolTip = "";
            this.btnDebug.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnDebug.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnDebug.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnDebug.VisibleEx = true;
            this.btnDebug.Click += new System.EventHandler(this.btnDebug_Click);
            // 
            // btnRun
            // 
            this.btnRun.BackColor = System.Drawing.Color.Transparent;
            this.btnRun.BindingForm = null;
            this.btnRun.Location = new System.Drawing.Point(0, 1);
            this.btnRun.Name = "btnRun";
            this.btnRun.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnRun.Selectable = true;
            this.btnRun.Size = new System.Drawing.Size(103, 41);
            this.btnRun.TabIndex = 28;
            this.btnRun.tbAdriftIconWhenHover = false;
            this.btnRun.tbAutoSize = false;
            this.btnRun.tbAutoSizeEx = false;
            this.btnRun.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_4_blank;
            this.btnRun.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnRun.tbBadgeNumber = 0;
            this.btnRun.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnRun.tbEndEllipsis = false;
            this.btnRun.tbIconHoldPlace = true;
            this.btnRun.tbIconImage = null;
            this.btnRun.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnRun.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnRun.tbIconMore = false;
            this.btnRun.tbIconMouseDown = null;
            this.btnRun.tbIconMouseHover = null;
            this.btnRun.tbIconMouseLeave = null;
            this.btnRun.tbIconPlaceText = 2;
            this.btnRun.tbIconReadOnly = null;
            this.btnRun.tbImageMouseDown = null;
            this.btnRun.tbImageMouseHover = null;
            this.btnRun.tbImageMouseLeave = null;
            this.btnRun.tbProgressValue = 50;
            this.btnRun.tbReadOnly = false;
            this.btnRun.tbReadOnlyText = false;
            this.btnRun.tbShadow = false;
            this.btnRun.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnRun.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnRun.tbShowDot = false;
            this.btnRun.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnRun.tbShowMoreIconImg")));
            this.btnRun.tbShowNew = false;
            this.btnRun.tbShowProgress = false;
            this.btnRun.tbShowTip = true;
            this.btnRun.tbShowToolTipOnButton = false;
            this.btnRun.tbSplit = "3,3,3,3";
            this.btnRun.tbText = "运行";
            this.btnRun.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnRun.tbTextColor = System.Drawing.Color.White;
            this.btnRun.tbTextColorDisable = System.Drawing.Color.White;
            this.btnRun.tbTextColorDown = System.Drawing.Color.White;
            this.btnRun.tbTextColorHover = System.Drawing.Color.White;
            this.btnRun.tbTextMouseDownPlace = 0;
            this.btnRun.tbToolTip = "";
            this.btnRun.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnRun.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnRun.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnRun.VisibleEx = true;
            this.btnRun.Click += new System.EventHandler(this.btnRun_Click);
            // 
            // btn_close
            // 
            this.btn_close.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_close.BackColor = System.Drawing.Color.Transparent;
            this.btn_close.BindingForm = null;
            this.btn_close.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_close.Location = new System.Drawing.Point(1153, 3);
            this.btn_close.Name = "btn_close";
            this.btn_close.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_close.Selectable = true;
            this.btn_close.Size = new System.Drawing.Size(24, 24);
            this.btn_close.TabIndex = 10;
            this.btn_close.tbAdriftIconWhenHover = false;
            this.btn_close.tbAutoSize = false;
            this.btn_close.tbAutoSizeEx = false;
            this.btn_close.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_close;
            this.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_close.tbBadgeNumber = 0;
            this.btn_close.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_close.tbEndEllipsis = false;
            this.btn_close.tbIconHoldPlace = true;
            this.btn_close.tbIconImage = null;
            this.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btn_close.tbIconMore = false;
            this.btn_close.tbIconMouseDown = null;
            this.btn_close.tbIconMouseHover = null;
            this.btn_close.tbIconMouseLeave = null;
            this.btn_close.tbIconPlaceText = 2;
            this.btn_close.tbIconReadOnly = null;
            this.btn_close.tbImageMouseDown = null;
            this.btn_close.tbImageMouseHover = null;
            this.btn_close.tbImageMouseLeave = null;
            this.btn_close.tbProgressValue = 50;
            this.btn_close.tbReadOnly = false;
            this.btn_close.tbReadOnlyText = false;
            this.btn_close.tbShadow = false;
            this.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_close.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_close.tbShowDot = false;
            this.btn_close.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_close.tbShowMoreIconImg")));
            this.btn_close.tbShowNew = false;
            this.btn_close.tbShowProgress = false;
            this.btn_close.tbShowTip = true;
            this.btn_close.tbShowToolTipOnButton = false;
            this.btn_close.tbSplit = "3,3,3,3";
            this.btn_close.tbText = "";
            this.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.tbTextColor = System.Drawing.Color.White;
            this.btn_close.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_close.tbTextColorDown = System.Drawing.Color.White;
            this.btn_close.tbTextColorHover = System.Drawing.Color.White;
            this.btn_close.tbTextMouseDownPlace = 0;
            this.btn_close.tbToolTip = "";
            this.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_close.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_close.VisibleEx = true;
            // 
            // btn_normal
            // 
            this.btn_normal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_normal.BackColor = System.Drawing.Color.Transparent;
            this.btn_normal.BindingForm = null;
            this.btn_normal.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_normal.Location = new System.Drawing.Point(1128, 3);
            this.btn_normal.Name = "btn_normal";
            this.btn_normal.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_normal.Selectable = true;
            this.btn_normal.Size = new System.Drawing.Size(24, 24);
            this.btn_normal.TabIndex = 9;
            this.btn_normal.tbAdriftIconWhenHover = false;
            this.btn_normal.tbAutoSize = false;
            this.btn_normal.tbAutoSizeEx = false;
            this.btn_normal.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_max;
            this.btn_normal.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_normal.tbBadgeNumber = 0;
            this.btn_normal.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_normal.tbEndEllipsis = false;
            this.btn_normal.tbIconHoldPlace = true;
            this.btn_normal.tbIconImage = null;
            this.btn_normal.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btn_normal.tbIconMore = false;
            this.btn_normal.tbIconMouseDown = null;
            this.btn_normal.tbIconMouseHover = null;
            this.btn_normal.tbIconMouseLeave = null;
            this.btn_normal.tbIconPlaceText = 2;
            this.btn_normal.tbIconReadOnly = null;
            this.btn_normal.tbImageMouseDown = null;
            this.btn_normal.tbImageMouseHover = null;
            this.btn_normal.tbImageMouseLeave = null;
            this.btn_normal.tbProgressValue = 50;
            this.btn_normal.tbReadOnly = false;
            this.btn_normal.tbReadOnlyText = false;
            this.btn_normal.tbShadow = false;
            this.btn_normal.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_normal.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_normal.tbShowDot = false;
            this.btn_normal.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_normal.tbShowMoreIconImg")));
            this.btn_normal.tbShowNew = false;
            this.btn_normal.tbShowProgress = false;
            this.btn_normal.tbShowTip = true;
            this.btn_normal.tbShowToolTipOnButton = false;
            this.btn_normal.tbSplit = "3,3,3,3";
            this.btn_normal.tbText = "";
            this.btn_normal.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.tbTextColor = System.Drawing.Color.White;
            this.btn_normal.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_normal.tbTextColorDown = System.Drawing.Color.White;
            this.btn_normal.tbTextColorHover = System.Drawing.Color.White;
            this.btn_normal.tbTextMouseDownPlace = 0;
            this.btn_normal.tbToolTip = "";
            this.btn_normal.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_normal.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btn_normal.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_normal.VisibleEx = true;
            // 
            // btn_minimize
            // 
            this.btn_minimize.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_minimize.BackColor = System.Drawing.Color.Transparent;
            this.btn_minimize.BindingForm = null;
            this.btn_minimize.Font = new System.Drawing.Font("宋体", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btn_minimize.Location = new System.Drawing.Point(1103, 3);
            this.btn_minimize.Name = "btn_minimize";
            this.btn_minimize.Padding = new System.Windows.Forms.Padding(0, 1, 0, 0);
            this.btn_minimize.Selectable = true;
            this.btn_minimize.Size = new System.Drawing.Size(24, 24);
            this.btn_minimize.TabIndex = 8;
            this.btn_minimize.tbAdriftIconWhenHover = false;
            this.btn_minimize.tbAutoSize = false;
            this.btn_minimize.tbAutoSizeEx = false;
            this.btn_minimize.tbBackgroundImage = global::tbToolkit.Properties.Resources.btn_min;
            this.btn_minimize.tbBackgroundImageState = iTong.Components.ImageState.ThreeState;
            this.btn_minimize.tbBadgeNumber = 0;
            this.btn_minimize.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btn_minimize.tbEndEllipsis = false;
            this.btn_minimize.tbIconHoldPlace = true;
            this.btn_minimize.tbIconImage = null;
            this.btn_minimize.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btn_minimize.tbIconMore = false;
            this.btn_minimize.tbIconMouseDown = null;
            this.btn_minimize.tbIconMouseHover = null;
            this.btn_minimize.tbIconMouseLeave = null;
            this.btn_minimize.tbIconPlaceText = 2;
            this.btn_minimize.tbIconReadOnly = null;
            this.btn_minimize.tbImageMouseDown = null;
            this.btn_minimize.tbImageMouseHover = null;
            this.btn_minimize.tbImageMouseLeave = null;
            this.btn_minimize.tbProgressValue = 50;
            this.btn_minimize.tbReadOnly = false;
            this.btn_minimize.tbReadOnlyText = false;
            this.btn_minimize.tbShadow = false;
            this.btn_minimize.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btn_minimize.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btn_minimize.tbShowDot = false;
            this.btn_minimize.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btn_minimize.tbShowMoreIconImg")));
            this.btn_minimize.tbShowNew = false;
            this.btn_minimize.tbShowProgress = false;
            this.btn_minimize.tbShowTip = true;
            this.btn_minimize.tbShowToolTipOnButton = false;
            this.btn_minimize.tbSplit = "3,3,3,3";
            this.btn_minimize.tbText = "";
            this.btn_minimize.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.tbTextColor = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorDisable = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorDown = System.Drawing.Color.White;
            this.btn_minimize.tbTextColorHover = System.Drawing.Color.White;
            this.btn_minimize.tbTextMouseDownPlace = 0;
            this.btn_minimize.tbToolTip = "";
            this.btn_minimize.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btn_minimize.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btn_minimize.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btn_minimize.VisibleEx = true;
            // 
            // btnFile
            // 
            this.btnFile.BackColor = System.Drawing.Color.Transparent;
            this.btnFile.BindingForm = null;
            this.btnFile.Location = new System.Drawing.Point(172, 10);
            this.btnFile.Name = "btnFile";
            this.btnFile.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnFile.Selectable = true;
            this.btnFile.Size = new System.Drawing.Size(76, 22);
            this.btnFile.TabIndex = 29;
            this.btnFile.tbAdriftIconWhenHover = false;
            this.btnFile.tbAutoSize = false;
            this.btnFile.tbAutoSizeEx = false;
            this.btnFile.tbBackgroundImage = null;
            this.btnFile.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnFile.tbBadgeNumber = 0;
            this.btnFile.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnFile.tbEndEllipsis = false;
            this.btnFile.tbIconHoldPlace = true;
            this.btnFile.tbIconImage = null;
            this.btnFile.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnFile.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnFile.tbIconMore = false;
            this.btnFile.tbIconMouseDown = null;
            this.btnFile.tbIconMouseHover = null;
            this.btnFile.tbIconMouseLeave = null;
            this.btnFile.tbIconPlaceText = 2;
            this.btnFile.tbIconReadOnly = null;
            this.btnFile.tbImageMouseDown = null;
            this.btnFile.tbImageMouseHover = null;
            this.btnFile.tbImageMouseLeave = null;
            this.btnFile.tbProgressValue = 50;
            this.btnFile.tbReadOnly = false;
            this.btnFile.tbReadOnlyText = false;
            this.btnFile.tbShadow = false;
            this.btnFile.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnFile.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnFile.tbShowDot = false;
            this.btnFile.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnFile.tbShowMoreIconImg")));
            this.btnFile.tbShowNew = false;
            this.btnFile.tbShowProgress = false;
            this.btnFile.tbShowTip = true;
            this.btnFile.tbShowToolTipOnButton = false;
            this.btnFile.tbSplit = "3,3,3,3";
            this.btnFile.tbText = "文件";
            this.btnFile.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnFile.tbTextColor = System.Drawing.Color.White;
            this.btnFile.tbTextColorDisable = System.Drawing.Color.White;
            this.btnFile.tbTextColorDown = System.Drawing.Color.White;
            this.btnFile.tbTextColorHover = System.Drawing.Color.White;
            this.btnFile.tbTextMouseDownPlace = 0;
            this.btnFile.tbToolTip = "";
            this.btnFile.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnFile.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnFile.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnFile.VisibleEx = true;
            this.btnFile.Click += new System.EventHandler(this.btnFile_Click);
            // 
            // btnEdit
            // 
            this.btnEdit.BackColor = System.Drawing.Color.Transparent;
            this.btnEdit.BindingForm = null;
            this.btnEdit.Location = new System.Drawing.Point(251, 10);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnEdit.Selectable = true;
            this.btnEdit.Size = new System.Drawing.Size(76, 22);
            this.btnEdit.TabIndex = 30;
            this.btnEdit.tbAdriftIconWhenHover = false;
            this.btnEdit.tbAutoSize = false;
            this.btnEdit.tbAutoSizeEx = false;
            this.btnEdit.tbBackgroundImage = null;
            this.btnEdit.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnEdit.tbBadgeNumber = 0;
            this.btnEdit.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnEdit.tbEndEllipsis = false;
            this.btnEdit.tbIconHoldPlace = true;
            this.btnEdit.tbIconImage = null;
            this.btnEdit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnEdit.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnEdit.tbIconMore = false;
            this.btnEdit.tbIconMouseDown = null;
            this.btnEdit.tbIconMouseHover = null;
            this.btnEdit.tbIconMouseLeave = null;
            this.btnEdit.tbIconPlaceText = 2;
            this.btnEdit.tbIconReadOnly = null;
            this.btnEdit.tbImageMouseDown = null;
            this.btnEdit.tbImageMouseHover = null;
            this.btnEdit.tbImageMouseLeave = null;
            this.btnEdit.tbProgressValue = 50;
            this.btnEdit.tbReadOnly = false;
            this.btnEdit.tbReadOnlyText = false;
            this.btnEdit.tbShadow = false;
            this.btnEdit.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnEdit.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnEdit.tbShowDot = false;
            this.btnEdit.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnEdit.tbShowMoreIconImg")));
            this.btnEdit.tbShowNew = false;
            this.btnEdit.tbShowProgress = false;
            this.btnEdit.tbShowTip = true;
            this.btnEdit.tbShowToolTipOnButton = false;
            this.btnEdit.tbSplit = "3,3,3,3";
            this.btnEdit.tbText = "编辑";
            this.btnEdit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnEdit.tbTextColor = System.Drawing.Color.White;
            this.btnEdit.tbTextColorDisable = System.Drawing.Color.White;
            this.btnEdit.tbTextColorDown = System.Drawing.Color.White;
            this.btnEdit.tbTextColorHover = System.Drawing.Color.White;
            this.btnEdit.tbTextMouseDownPlace = 0;
            this.btnEdit.tbToolTip = "";
            this.btnEdit.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnEdit.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnEdit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnEdit.VisibleEx = true;
            this.btnEdit.Click += new System.EventHandler(this.btnEdit_Click);
            // 
            // btnProject
            // 
            this.btnProject.BackColor = System.Drawing.Color.Transparent;
            this.btnProject.BindingForm = null;
            this.btnProject.Location = new System.Drawing.Point(330, 10);
            this.btnProject.Name = "btnProject";
            this.btnProject.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnProject.Selectable = true;
            this.btnProject.Size = new System.Drawing.Size(76, 22);
            this.btnProject.TabIndex = 31;
            this.btnProject.tbAdriftIconWhenHover = false;
            this.btnProject.tbAutoSize = false;
            this.btnProject.tbAutoSizeEx = false;
            this.btnProject.tbBackgroundImage = null;
            this.btnProject.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnProject.tbBadgeNumber = 0;
            this.btnProject.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnProject.tbEndEllipsis = false;
            this.btnProject.tbIconHoldPlace = true;
            this.btnProject.tbIconImage = null;
            this.btnProject.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnProject.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnProject.tbIconMore = false;
            this.btnProject.tbIconMouseDown = null;
            this.btnProject.tbIconMouseHover = null;
            this.btnProject.tbIconMouseLeave = null;
            this.btnProject.tbIconPlaceText = 2;
            this.btnProject.tbIconReadOnly = null;
            this.btnProject.tbImageMouseDown = null;
            this.btnProject.tbImageMouseHover = null;
            this.btnProject.tbImageMouseLeave = null;
            this.btnProject.tbProgressValue = 50;
            this.btnProject.tbReadOnly = false;
            this.btnProject.tbReadOnlyText = false;
            this.btnProject.tbShadow = false;
            this.btnProject.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnProject.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnProject.tbShowDot = false;
            this.btnProject.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnProject.tbShowMoreIconImg")));
            this.btnProject.tbShowNew = false;
            this.btnProject.tbShowProgress = false;
            this.btnProject.tbShowTip = true;
            this.btnProject.tbShowToolTipOnButton = false;
            this.btnProject.tbSplit = "3,3,3,3";
            this.btnProject.tbText = "项目";
            this.btnProject.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnProject.tbTextColor = System.Drawing.Color.White;
            this.btnProject.tbTextColorDisable = System.Drawing.Color.White;
            this.btnProject.tbTextColorDown = System.Drawing.Color.White;
            this.btnProject.tbTextColorHover = System.Drawing.Color.White;
            this.btnProject.tbTextMouseDownPlace = 0;
            this.btnProject.tbToolTip = "";
            this.btnProject.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnProject.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnProject.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnProject.VisibleEx = true;
            this.btnProject.Click += new System.EventHandler(this.btnProject_Click);
            // 
            // btnPublish
            // 
            this.btnPublish.BackColor = System.Drawing.Color.Transparent;
            this.btnPublish.BindingForm = null;
            this.btnPublish.Location = new System.Drawing.Point(408, 10);
            this.btnPublish.Name = "btnPublish";
            this.btnPublish.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnPublish.Selectable = true;
            this.btnPublish.Size = new System.Drawing.Size(76, 22);
            this.btnPublish.TabIndex = 32;
            this.btnPublish.tbAdriftIconWhenHover = false;
            this.btnPublish.tbAutoSize = false;
            this.btnPublish.tbAutoSizeEx = false;
            this.btnPublish.tbBackgroundImage = null;
            this.btnPublish.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnPublish.tbBadgeNumber = 0;
            this.btnPublish.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnPublish.tbEndEllipsis = false;
            this.btnPublish.tbIconHoldPlace = true;
            this.btnPublish.tbIconImage = null;
            this.btnPublish.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPublish.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnPublish.tbIconMore = false;
            this.btnPublish.tbIconMouseDown = null;
            this.btnPublish.tbIconMouseHover = null;
            this.btnPublish.tbIconMouseLeave = null;
            this.btnPublish.tbIconPlaceText = 2;
            this.btnPublish.tbIconReadOnly = null;
            this.btnPublish.tbImageMouseDown = null;
            this.btnPublish.tbImageMouseHover = null;
            this.btnPublish.tbImageMouseLeave = null;
            this.btnPublish.tbProgressValue = 50;
            this.btnPublish.tbReadOnly = false;
            this.btnPublish.tbReadOnlyText = false;
            this.btnPublish.tbShadow = false;
            this.btnPublish.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnPublish.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnPublish.tbShowDot = false;
            this.btnPublish.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnPublish.tbShowMoreIconImg")));
            this.btnPublish.tbShowNew = false;
            this.btnPublish.tbShowProgress = false;
            this.btnPublish.tbShowTip = true;
            this.btnPublish.tbShowToolTipOnButton = false;
            this.btnPublish.tbSplit = "3,3,3,3";
            this.btnPublish.tbText = "发布";
            this.btnPublish.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPublish.tbTextColor = System.Drawing.Color.White;
            this.btnPublish.tbTextColorDisable = System.Drawing.Color.White;
            this.btnPublish.tbTextColorDown = System.Drawing.Color.White;
            this.btnPublish.tbTextColorHover = System.Drawing.Color.White;
            this.btnPublish.tbTextMouseDownPlace = 0;
            this.btnPublish.tbToolTip = "";
            this.btnPublish.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnPublish.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnPublish.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnPublish.VisibleEx = true;
            this.btnPublish.Click += new System.EventHandler(this.btnPublish_Click);
            // 
            // btnHelper
            // 
            this.btnHelper.BackColor = System.Drawing.Color.Transparent;
            this.btnHelper.BindingForm = null;
            this.btnHelper.Location = new System.Drawing.Point(487, 10);
            this.btnHelper.Name = "btnHelper";
            this.btnHelper.Padding = new System.Windows.Forms.Padding(5, 2, 5, 2);
            this.btnHelper.Selectable = true;
            this.btnHelper.Size = new System.Drawing.Size(76, 22);
            this.btnHelper.TabIndex = 33;
            this.btnHelper.tbAdriftIconWhenHover = false;
            this.btnHelper.tbAutoSize = false;
            this.btnHelper.tbAutoSizeEx = false;
            this.btnHelper.tbBackgroundImage = null;
            this.btnHelper.tbBackgroundImageState = iTong.Components.ImageState.FourState;
            this.btnHelper.tbBadgeNumber = 0;
            this.btnHelper.tbBadgeNumberOffset = new System.Drawing.Point(0, 0);
            this.btnHelper.tbEndEllipsis = false;
            this.btnHelper.tbIconHoldPlace = true;
            this.btnHelper.tbIconImage = null;
            this.btnHelper.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnHelper.tbIconImageState = iTong.Components.ImageState.OneState;
            this.btnHelper.tbIconMore = false;
            this.btnHelper.tbIconMouseDown = null;
            this.btnHelper.tbIconMouseHover = null;
            this.btnHelper.tbIconMouseLeave = null;
            this.btnHelper.tbIconPlaceText = 2;
            this.btnHelper.tbIconReadOnly = null;
            this.btnHelper.tbImageMouseDown = null;
            this.btnHelper.tbImageMouseHover = null;
            this.btnHelper.tbImageMouseLeave = null;
            this.btnHelper.tbProgressValue = 50;
            this.btnHelper.tbReadOnly = false;
            this.btnHelper.tbReadOnlyText = false;
            this.btnHelper.tbShadow = false;
            this.btnHelper.tbShadowColor = System.Drawing.Color.FromArgb(((int)(((byte)(22)))), ((int)(((byte)(74)))), ((int)(((byte)(160)))));
            this.btnHelper.tbShadowOffset = new System.Drawing.Point(1, 1);
            this.btnHelper.tbShowDot = false;
            this.btnHelper.tbShowMoreIconImg = ((System.Drawing.Image)(resources.GetObject("btnHelper.tbShowMoreIconImg")));
            this.btnHelper.tbShowNew = false;
            this.btnHelper.tbShowProgress = false;
            this.btnHelper.tbShowTip = true;
            this.btnHelper.tbShowToolTipOnButton = false;
            this.btnHelper.tbSplit = "3,3,3,3";
            this.btnHelper.tbText = "帮助";
            this.btnHelper.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnHelper.tbTextColor = System.Drawing.Color.White;
            this.btnHelper.tbTextColorDisable = System.Drawing.Color.White;
            this.btnHelper.tbTextColorDown = System.Drawing.Color.White;
            this.btnHelper.tbTextColorHover = System.Drawing.Color.White;
            this.btnHelper.tbTextMouseDownPlace = 0;
            this.btnHelper.tbToolTip = "";
            this.btnHelper.tbToolTipColor = System.Drawing.Color.FromArgb(((int)(((byte)(122)))), ((int)(((byte)(117)))), ((int)(((byte)(117)))));
            this.btnHelper.tbToolTipFont = new System.Drawing.Font("宋体", 9F);
            this.btnHelper.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.btnHelper.VisibleEx = true;
            this.btnHelper.Click += new System.EventHandler(this.btnHelper_Click);
            // 
            // menuFile
            // 
            this.menuFile.AccessibleDescription = "161x202";
            this.menuFile.DropShadowEnabled = false;
            this.menuFile.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.menuFile.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiNewFile,
            this.tsmiNewProject,
            this.tsmiSaveFile,
            this.tsmiSaveProject,
            this.tsmiImportProject,
            this.tsmiCloseCurrentPage,
            this.tsmiSetWorkSpace,
            this.tsmiExit});
            this.menuFile.Name = "menuMain";
            this.menuFile.Size = new System.Drawing.Size(161, 180);
            this.menuFile.Tag = "";
            this.menuFile.tbBackColor = System.Drawing.Color.White;
            this.menuFile.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.menuFile.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.menuFile.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiNewFile
            // 
            this.tsmiNewFile.Name = "tsmiNewFile";
            this.tsmiNewFile.Size = new System.Drawing.Size(160, 22);
            this.tsmiNewFile.Text = "新建文件";
            this.tsmiNewFile.Click += new System.EventHandler(this.tsmiNewFile_Click);
            // 
            // tsmiNewProject
            // 
            this.tsmiNewProject.Name = "tsmiNewProject";
            this.tsmiNewProject.Size = new System.Drawing.Size(160, 22);
            this.tsmiNewProject.Text = "创建项目";
            this.tsmiNewProject.Click += new System.EventHandler(this.tsmiNewProject_Click);
            // 
            // tsmiSaveFile
            // 
            this.tsmiSaveFile.Name = "tsmiSaveFile";
            this.tsmiSaveFile.Size = new System.Drawing.Size(160, 22);
            this.tsmiSaveFile.Text = "保存文件";
            this.tsmiSaveFile.Click += new System.EventHandler(this.tsmiSaveFile_Click);
            // 
            // tsmiSaveProject
            // 
            this.tsmiSaveProject.Name = "tsmiSaveProject";
            this.tsmiSaveProject.Size = new System.Drawing.Size(160, 22);
            this.tsmiSaveProject.Text = "保存工程";
            this.tsmiSaveProject.Click += new System.EventHandler(this.tsmiSaveProject_Click);
            // 
            // tsmiImportProject
            // 
            this.tsmiImportProject.Name = "tsmiImportProject";
            this.tsmiImportProject.Size = new System.Drawing.Size(160, 22);
            this.tsmiImportProject.Text = "导入工程";
            this.tsmiImportProject.Click += new System.EventHandler(this.tsmiImportProject_Click);
            // 
            // tsmiCloseCurrentPage
            // 
            this.tsmiCloseCurrentPage.Name = "tsmiCloseCurrentPage";
            this.tsmiCloseCurrentPage.Size = new System.Drawing.Size(160, 22);
            this.tsmiCloseCurrentPage.Text = "关闭当前页面";
            this.tsmiCloseCurrentPage.Click += new System.EventHandler(this.tsmiCloseCurrentPage_Click);
            // 
            // tsmiSetWorkSpace
            // 
            this.tsmiSetWorkSpace.Name = "tsmiSetWorkSpace";
            this.tsmiSetWorkSpace.Size = new System.Drawing.Size(160, 22);
            this.tsmiSetWorkSpace.Text = "设置工作区路径";
            this.tsmiSetWorkSpace.Click += new System.EventHandler(this.tsmiSetWorkSpace_Click);
            // 
            // tsmiExit
            // 
            this.tsmiExit.Name = "tsmiExit";
            this.tsmiExit.Size = new System.Drawing.Size(160, 22);
            this.tsmiExit.Text = "退出";
            this.tsmiExit.Click += new System.EventHandler(this.tsmiExit_Click);
            // 
            // menuEdit
            // 
            this.menuEdit.AccessibleDescription = "161x334";
            this.menuEdit.DropShadowEnabled = false;
            this.menuEdit.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.menuEdit.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiCut,
            this.tsmiCopy,
            this.tsmiPus,
            this.tsmiSelectAll,
            this.tsmiCancel,
            this.tsmiRedo,
            this.tsmiCommented,
            this.tsmiFold,
            this.tsmiSearch,
            this.tsmiReplace,
            this.tsmiSearchCode,
            this.tsmiBookMark,
            this.tsmiWordWrap,
            this.tsmiTabSizeSetting});
            this.menuEdit.Name = "menuMain";
            this.menuEdit.Size = new System.Drawing.Size(161, 312);
            this.menuEdit.Tag = "";
            this.menuEdit.tbBackColor = System.Drawing.Color.White;
            this.menuEdit.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.menuEdit.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.menuEdit.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiCut
            // 
            this.tsmiCut.Name = "tsmiCut";
            this.tsmiCut.Size = new System.Drawing.Size(160, 22);
            this.tsmiCut.Text = "剪切";
            // 
            // tsmiCopy
            // 
            this.tsmiCopy.Name = "tsmiCopy";
            this.tsmiCopy.Size = new System.Drawing.Size(160, 22);
            this.tsmiCopy.Text = "复制";
            // 
            // tsmiPus
            // 
            this.tsmiPus.Name = "tsmiPus";
            this.tsmiPus.Size = new System.Drawing.Size(160, 22);
            this.tsmiPus.Text = "粘贴";
            // 
            // tsmiSelectAll
            // 
            this.tsmiSelectAll.Name = "tsmiSelectAll";
            this.tsmiSelectAll.Size = new System.Drawing.Size(160, 22);
            this.tsmiSelectAll.Text = "全选";
            // 
            // tsmiCancel
            // 
            this.tsmiCancel.Name = "tsmiCancel";
            this.tsmiCancel.Size = new System.Drawing.Size(160, 22);
            this.tsmiCancel.Text = "撤销";
            // 
            // tsmiRedo
            // 
            this.tsmiRedo.Name = "tsmiRedo";
            this.tsmiRedo.Size = new System.Drawing.Size(160, 22);
            this.tsmiRedo.Text = "重做";
            // 
            // tsmiCommented
            // 
            this.tsmiCommented.Name = "tsmiCommented";
            this.tsmiCommented.Size = new System.Drawing.Size(160, 22);
            this.tsmiCommented.Text = "注释、取消注释";
            // 
            // tsmiFold
            // 
            this.tsmiFold.Name = "tsmiFold";
            this.tsmiFold.Size = new System.Drawing.Size(160, 22);
            this.tsmiFold.Text = "折叠";
            // 
            // tsmiSearch
            // 
            this.tsmiSearch.Name = "tsmiSearch";
            this.tsmiSearch.Size = new System.Drawing.Size(160, 22);
            this.tsmiSearch.Text = "查找";
            // 
            // tsmiReplace
            // 
            this.tsmiReplace.Name = "tsmiReplace";
            this.tsmiReplace.Size = new System.Drawing.Size(160, 22);
            this.tsmiReplace.Text = "替换";
            // 
            // tsmiSearchCode
            // 
            this.tsmiSearchCode.Name = "tsmiSearchCode";
            this.tsmiSearchCode.Size = new System.Drawing.Size(160, 22);
            this.tsmiSearchCode.Text = "查找符号";
            // 
            // tsmiBookMark
            // 
            this.tsmiBookMark.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.添加删除书签ToolStripMenuItem,
            this.查找上一个书签ToolStripMenuItem,
            this.查找下一个书签ToolStripMenuItem,
            this.文件内查找ToolStripMenuItem});
            this.tsmiBookMark.Name = "tsmiBookMark";
            this.tsmiBookMark.Size = new System.Drawing.Size(160, 22);
            this.tsmiBookMark.Text = "书签";
            // 
            // 添加删除书签ToolStripMenuItem
            // 
            this.添加删除书签ToolStripMenuItem.Name = "添加删除书签ToolStripMenuItem";
            this.添加删除书签ToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.添加删除书签ToolStripMenuItem.Text = "添加、删除书签";
            // 
            // 查找上一个书签ToolStripMenuItem
            // 
            this.查找上一个书签ToolStripMenuItem.Name = "查找上一个书签ToolStripMenuItem";
            this.查找上一个书签ToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.查找上一个书签ToolStripMenuItem.Text = "查找上一个书签";
            // 
            // 查找下一个书签ToolStripMenuItem
            // 
            this.查找下一个书签ToolStripMenuItem.Name = "查找下一个书签ToolStripMenuItem";
            this.查找下一个书签ToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.查找下一个书签ToolStripMenuItem.Text = "查找下一个书签";
            // 
            // 文件内查找ToolStripMenuItem
            // 
            this.文件内查找ToolStripMenuItem.Name = "文件内查找ToolStripMenuItem";
            this.文件内查找ToolStripMenuItem.Size = new System.Drawing.Size(160, 22);
            this.文件内查找ToolStripMenuItem.Text = "文件内查找";
            // 
            // tsmiWordWrap
            // 
            this.tsmiWordWrap.Name = "tsmiWordWrap";
            this.tsmiWordWrap.Size = new System.Drawing.Size(160, 22);
            this.tsmiWordWrap.Text = "自动换行";
            // 
            // tsmiTabSizeSetting
            // 
            this.tsmiTabSizeSetting.Name = "tsmiTabSizeSetting";
            this.tsmiTabSizeSetting.Size = new System.Drawing.Size(160, 22);
            this.tsmiTabSizeSetting.Text = "Tab宽度设置";
            // 
            // menuProject
            // 
            this.menuProject.AccessibleDescription = "153x202";
            this.menuProject.DropShadowEnabled = false;
            this.menuProject.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.menuProject.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiUIPreview,
            this.tsmiRun,
            this.tsmiRunLocal,
            this.tsmiRunStop,
            this.tsmiConnectDevice,
            this.tsmiScreenshort,
            this.tsmiCheckCode,
            this.tsmiTypeSetting});
            this.menuProject.Name = "menuMain";
            this.menuProject.Size = new System.Drawing.Size(137, 180);
            this.menuProject.Tag = "";
            this.menuProject.tbBackColor = System.Drawing.Color.White;
            this.menuProject.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.menuProject.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.menuProject.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiUIPreview
            // 
            this.tsmiUIPreview.Name = "tsmiUIPreview";
            this.tsmiUIPreview.Size = new System.Drawing.Size(136, 22);
            this.tsmiUIPreview.Text = "UI预览";
            // 
            // tsmiRun
            // 
            this.tsmiRun.Name = "tsmiRun";
            this.tsmiRun.Size = new System.Drawing.Size(136, 22);
            this.tsmiRun.Text = "运行";
            // 
            // tsmiRunLocal
            // 
            this.tsmiRunLocal.Name = "tsmiRunLocal";
            this.tsmiRunLocal.Size = new System.Drawing.Size(136, 22);
            this.tsmiRunLocal.Text = "本地运行";
            // 
            // tsmiRunStop
            // 
            this.tsmiRunStop.Name = "tsmiRunStop";
            this.tsmiRunStop.Size = new System.Drawing.Size(136, 22);
            this.tsmiRunStop.Text = "停止运行";
            // 
            // tsmiConnectDevice
            // 
            this.tsmiConnectDevice.Name = "tsmiConnectDevice";
            this.tsmiConnectDevice.Size = new System.Drawing.Size(136, 22);
            this.tsmiConnectDevice.Text = "连接到设备";
            // 
            // tsmiScreenshort
            // 
            this.tsmiScreenshort.Name = "tsmiScreenshort";
            this.tsmiScreenshort.Size = new System.Drawing.Size(136, 22);
            this.tsmiScreenshort.Text = "抓图";
            // 
            // tsmiCheckCode
            // 
            this.tsmiCheckCode.Name = "tsmiCheckCode";
            this.tsmiCheckCode.Size = new System.Drawing.Size(136, 22);
            this.tsmiCheckCode.Text = "语法检测";
            // 
            // tsmiTypeSetting
            // 
            this.tsmiTypeSetting.Name = "tsmiTypeSetting";
            this.tsmiTypeSetting.Size = new System.Drawing.Size(136, 22);
            this.tsmiTypeSetting.Text = "代码排版";
            // 
            // menuPublish
            // 
            this.menuPublish.AccessibleDescription = "153x70";
            this.menuPublish.DropShadowEnabled = false;
            this.menuPublish.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.menuPublish.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiExportCode,
            this.tsmiDeveloper});
            this.menuPublish.Name = "menuMain";
            this.menuPublish.Size = new System.Drawing.Size(137, 48);
            this.menuPublish.Tag = "";
            this.menuPublish.tbBackColor = System.Drawing.Color.White;
            this.menuPublish.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.menuPublish.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.menuPublish.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiExportCode
            // 
            this.tsmiExportCode.Name = "tsmiExportCode";
            this.tsmiExportCode.Size = new System.Drawing.Size(136, 22);
            this.tsmiExportCode.Text = "导出脚本";
            // 
            // tsmiDeveloper
            // 
            this.tsmiDeveloper.Name = "tsmiDeveloper";
            this.tsmiDeveloper.Size = new System.Drawing.Size(136, 22);
            this.tsmiDeveloper.Text = "开发者平台";
            // 
            // menuHelper
            // 
            this.menuHelper.AccessibleDescription = "201x158";
            this.menuHelper.DropShadowEnabled = false;
            this.menuHelper.Font = new System.Drawing.Font("微软雅黑", 9F);
            this.menuHelper.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.tsmiDeveloperManual,
            this.tsmiDownloadPhoneTool,
            this.tsmiFeedback,
            this.tsmiCheckUpdate,
            this.tsmiLang,
            this.tsmiAbout});
            this.menuHelper.Name = "menuMain";
            this.menuHelper.Size = new System.Drawing.Size(201, 136);
            this.menuHelper.Tag = "";
            this.menuHelper.tbBackColor = System.Drawing.Color.White;
            this.menuHelper.tbBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(182)))), ((int)(((byte)(192)))), ((int)(((byte)(201)))));
            this.menuHelper.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(31)))), ((int)(((byte)(143)))), ((int)(((byte)(219)))));
            this.menuHelper.tbSelectedItemForeColor = System.Drawing.Color.White;
            // 
            // tsmiDeveloperManual
            // 
            this.tsmiDeveloperManual.Name = "tsmiDeveloperManual";
            this.tsmiDeveloperManual.Size = new System.Drawing.Size(200, 22);
            this.tsmiDeveloperManual.Text = "tbToolkit脚本开发手册";
            // 
            // tsmiDownloadPhoneTool
            // 
            this.tsmiDownloadPhoneTool.Name = "tsmiDownloadPhoneTool";
            this.tsmiDownloadPhoneTool.Size = new System.Drawing.Size(200, 22);
            this.tsmiDownloadPhoneTool.Text = "下载手机开发助手";
            // 
            // tsmiFeedback
            // 
            this.tsmiFeedback.Name = "tsmiFeedback";
            this.tsmiFeedback.Size = new System.Drawing.Size(200, 22);
            this.tsmiFeedback.Text = "用户反馈";
            // 
            // tsmiCheckUpdate
            // 
            this.tsmiCheckUpdate.Name = "tsmiCheckUpdate";
            this.tsmiCheckUpdate.Size = new System.Drawing.Size(200, 22);
            this.tsmiCheckUpdate.Text = "检查更新";
            // 
            // tsmiLang
            // 
            this.tsmiLang.Name = "tsmiLang";
            this.tsmiLang.Size = new System.Drawing.Size(200, 22);
            this.tsmiLang.Text = "环境语言切换";
            // 
            // tsmiAbout
            // 
            this.tsmiAbout.Name = "tsmiAbout";
            this.tsmiAbout.Size = new System.Drawing.Size(200, 22);
            this.tsmiAbout.Text = "关于";
            // 
            // MainForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(57)))), ((int)(((byte)(57)))), ((int)(((byte)(57)))));
            this.ClientSize = new System.Drawing.Size(1180, 780);
            this.Controls.Add(this.btnHelper);
            this.Controls.Add(this.btnPublish);
            this.Controls.Add(this.btnProject);
            this.Controls.Add(this.btnEdit);
            this.Controls.Add(this.btnFile);
            this.Controls.Add(this.tlpMain);
            this.Controls.Add(this.btn_close);
            this.Controls.Add(this.btn_normal);
            this.Controls.Add(this.btn_minimize);
            this.Controls.Add(this.pnlTop);
            this.MinimumSize = new System.Drawing.Size(700, 600);
            this.Name = "MainForm";
            this.tbSplit = "3,94,3,40";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.Text = "Form1";
            this.Resize += new System.EventHandler(this.MainForm_Resize);
            this.tlpMain.ResumeLayout(false);
            this.pnlTop.ResumeLayout(false);
            this.menuFile.ResumeLayout(false);
            this.menuEdit.ResumeLayout(false);
            this.menuProject.ResumeLayout(false);
            this.menuPublish.ResumeLayout(false);
            this.menuHelper.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        internal iTong.Components.tbButton btn_close;
        internal iTong.Components.tbButton btn_normal;
        internal iTong.Components.tbButton btn_minimize;
        private System.Windows.Forms.TableLayoutPanel tlpMain;
        private System.Windows.Forms.Panel pnlSplitBottom;
        private System.Windows.Forms.Panel pnlSplitLeft;
        private System.Windows.Forms.Panel pnlSplitRight;
        private System.Windows.Forms.Panel pnlLeft;
        private System.Windows.Forms.Panel pnlRight;
        private iTong.Components.tbPanel pnlTop;
        private iTong.Components.tbButton btnRun;
        private iTong.Components.tbButton btnDebug;
        private iTong.Components.tbButton btnUIReview;
        private iTong.Components.tbButton btnCodeReset;
        private iTong.Components.tbButton btnCheckCode;
        private iTong.Components.tbButton btnScreenshot;
        private iTong.Components.tbButton btnFile;
        private iTong.Components.tbButton btnEdit;
        private iTong.Components.tbButton btnProject;
        private iTong.Components.tbButton btnPublish;
        private iTong.Components.tbButton btnHelper;
        internal iTong.Components.tbContextMenuStrip menuFile;
        private System.Windows.Forms.ToolStripMenuItem tsmiNewFile;
        private System.Windows.Forms.ToolStripMenuItem tsmiNewProject;
        private System.Windows.Forms.ToolStripMenuItem tsmiSaveFile;
        private System.Windows.Forms.ToolStripMenuItem tsmiSaveProject;
        private System.Windows.Forms.ToolStripMenuItem tsmiImportProject;
        private System.Windows.Forms.ToolStripMenuItem tsmiCloseCurrentPage;
        private System.Windows.Forms.ToolStripMenuItem tsmiSetWorkSpace;
        private System.Windows.Forms.ToolStripMenuItem tsmiExit;
        internal iTong.Components.tbContextMenuStrip menuEdit;
        private System.Windows.Forms.ToolStripMenuItem tsmiCut;
        private System.Windows.Forms.ToolStripMenuItem tsmiCopy;
        private System.Windows.Forms.ToolStripMenuItem tsmiPus;
        private System.Windows.Forms.ToolStripMenuItem tsmiSelectAll;
        private System.Windows.Forms.ToolStripMenuItem tsmiCancel;
        private System.Windows.Forms.ToolStripMenuItem tsmiRedo;
        private System.Windows.Forms.ToolStripMenuItem tsmiCommented;
        private System.Windows.Forms.ToolStripMenuItem tsmiFold;
        private System.Windows.Forms.ToolStripMenuItem tsmiSearch;
        private System.Windows.Forms.ToolStripMenuItem tsmiReplace;
        private System.Windows.Forms.ToolStripMenuItem tsmiSearchCode;
        private System.Windows.Forms.ToolStripMenuItem tsmiBookMark;
        private System.Windows.Forms.ToolStripMenuItem 添加删除书签ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 查找上一个书签ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 查找下一个书签ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem 文件内查找ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem tsmiWordWrap;
        private System.Windows.Forms.ToolStripMenuItem tsmiTabSizeSetting;
        internal iTong.Components.tbContextMenuStrip menuProject;
        private System.Windows.Forms.ToolStripMenuItem tsmiUIPreview;
        private System.Windows.Forms.ToolStripMenuItem tsmiRun;
        private System.Windows.Forms.ToolStripMenuItem tsmiRunLocal;
        private System.Windows.Forms.ToolStripMenuItem tsmiRunStop;
        private System.Windows.Forms.ToolStripMenuItem tsmiConnectDevice;
        private System.Windows.Forms.ToolStripMenuItem tsmiScreenshort;
        private System.Windows.Forms.ToolStripMenuItem tsmiCheckCode;
        private System.Windows.Forms.ToolStripMenuItem tsmiTypeSetting;
        internal iTong.Components.tbContextMenuStrip menuPublish;
        internal iTong.Components.tbContextMenuStrip menuHelper;
        private System.Windows.Forms.ToolStripMenuItem tsmiExportCode;
        private System.Windows.Forms.ToolStripMenuItem tsmiDeveloper;
        private System.Windows.Forms.ToolStripMenuItem tsmiDeveloperManual;
        private System.Windows.Forms.ToolStripMenuItem tsmiDownloadPhoneTool;
        private System.Windows.Forms.ToolStripMenuItem tsmiFeedback;
        private System.Windows.Forms.ToolStripMenuItem tsmiCheckUpdate;
        private System.Windows.Forms.ToolStripMenuItem tsmiLang;
        private System.Windows.Forms.ToolStripMenuItem tsmiAbout;
        private System.Windows.Forms.Panel pnlCodeMamage;
        private System.Windows.Forms.Panel pnlBottom;
    }
}

