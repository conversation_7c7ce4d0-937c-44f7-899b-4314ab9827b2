local socket = require "socket.socket"
local mobdebug = require "mobdebug.mobdebug"

local function serialize(value, options) return mobdebug.line(value, options) end

-- src/util.lua
local function FixUTF8(s, repl)
  local p, len, invalid = 1, #s, {}
  while p <= len do
    if     s:find("^[%z\1-\127]", p) then p = p + 1
    elseif s:find("^[\194-\223][\128-\191]", p) then p = p + 2
    elseif s:find(       "^\224[\160-\191][\128-\191]", p)
        or s:find("^[\225-\236][\128-\191][\128-\191]", p)
        or s:find(       "^\237[\128-\159][\128-\191]", p)
        or s:find("^[\238-\239][\128-\191][\128-\191]", p) then p = p + 3
    elseif s:find(       "^\240[\144-\191][\128-\191][\128-\191]", p)
        or s:find("^[\241-\243][\128-\191][\128-\191][\128-\191]", p)
        or s:find(       "^\244[\128-\143][\128-\191][\128-\191]", p) then p = p + 4
    else
      if not repl then return end -- just signal invalid UTF8 string
      local repl = type(repl) == 'function' and repl(s:sub(p,p)) or repl
      s = s:sub(1, p-1)..repl..s:sub(p+1)
      table.insert(invalid, p)
      -- adjust position/length as the replacement may be longer than one char
      p = p + #repl
      len = len + #repl - 1
    end
  end
  return s, invalid
end
  
local function fixUTF8(...)
  local t = {...}
  -- convert to escaped decimal code as these can only appear in strings
  local function fix(s) return '\\'..string.byte(s) end
  for i = 1, #t do t[i] = FixUTF8(t[i], fix) end
  return unpack(t)
end

local function todeb(params) return params and " -- "..mobdebug.line(params, {comment = false}) or "" end

ide = {
  config = {
    debugger = {
      hostname = nil,
      numformat = "%.16g",
      port = 8172,
      maxdatalength = 256,
      maxdatanum = 128,
      maxdatalevel = 10,
      refuseonconflict = true,
    },
  },
  
  proto = {},
  
  debugger = {}, 
}

ide.proto.Debugger = {__index = {
  IsRunning = function(self) return self.running end,
  IsConnected = function(self) return self.client end,
  IsListening = function(self) return self.listening end,
  GetHostName = function(self) return self.hostname end,
  GetPortNumber = function(self) return self.portnumber end,
  GetDataOptions = function(self, options)
    local cfg = ide.config.debugger
    local params = {
      comment = false, nocode = true, numformat = cfg.numformat,
      maxlevel = cfg.maxdatalevel, maxnum = cfg.maxdatanum, maxlength = cfg.maxdatalength,
    }
    for k, v in pairs(options or {}) do params[k] = v end
    return params
  end,
}}

function ide:GetDebugger() return self.debugger end

function ide:SetDebugger(deb)
  self.debugger = deb
  return deb
end

local protodeb = setmetatable(ide:GetDebugger(), ide.proto.Debugger)

local debugger = protodeb
debugger.running = false -- true when the debuggee is running
debugger.listening = false -- true when the debugger is listening for a client
debugger.portnumber = ide.config.debugger.port or mobdebug.port -- the port # to use for debugging
debugger.hostname = ide.config.debugger.hostname or (function()
  local hostname = socket.dns.gethostname()
  return hostname and socket.dns.toip(hostname) and hostname or "localhost"
end)()

-- 调试器初始化
function debugger:init(initData)
  local o = {}
  -- merge known self and init values
  for k, v in pairs(initData or {}) do o[k] = v end
  return setmetatable(o, {__index = self})
end

-- 执行mobdebug.handle接口，该接口被改动，新增一个回调函数，用于查看执行状态
function debugger:handle(command, client, options)	
  local debugger = self
  options = options or {}

  debugger.running = true
  
  local notifyStatus = false;
  local _, _, cmd = string.find(command, "^([a-z]+)")
  if cmd == "run" or cmd == "step"
  or cmd == "over" or cmd == "out" then
	notifyStatus = true;
  end 

  if notifyStatus then lua_xx_debug_updateDebugStatus(1) end
  
  local file, line, err = mobdebug.handle(command, client or debugger.client, options, lua_xx_debug_checkWhenRunning)

  if notifyStatus then lua_xx_debug_updateDebugStatus(2) end
  
  debugger.running = false

  return file, line, err
end

-- 执行语句
function debugger:exec(command)
  local isExit = command == "exit" or command == "done"
  local debugger = self
  if debugger.client and not debugger.running or isExit then
	  local file, line, err = debugger:handle(command)
    if line == nil then
      debugger:teardown()
      return
    elseif not debugger.client then
      -- it is possible that while debugger.handle call was executing
        -- the debugging was terminated; simply return in this case.
      return
    else
      --lua_xx_debug_activateDocument(file, line)
    
      debugger:updateStack()
    end
  end
 end
 
 -- 结束调试器
function debugger:teardown()
  local debugger = self
  if debugger.client then
    debugger.client:close()
    debugger.client = nil
  end
  if debugger.server then
    debugger.server:close()
    debugger.server = nil
  end
  lua_xx_debug_updateDebugStatus(3)
end

-- 清除所有断点
function debugger:reSetBreakpoints()
  local debugger = self
  -- remove all breakpoints 
  debugger:handle("delallb")
  
  lua_xx_debug_reSetBreakpoints()
end

-- 混淆代码
function debugger:serializeValue(value)
  local debugger = self
  local params = debugger:GetDataOptions({maxlevel=false})
  local text = fixUTF8(serialize(value, params))
  return text
end 

-- 反馈堆栈信息
function debugger:stack(params) 
  return self:handle('stack' .. todeb(params)) 
end

-- {"bar1", "auto/test.lua", 8, 9, "Lua", "global", ".\\auto\\test.lua"}
-- {"bar", "auto/test.lua", 13, 16, "Lua", "global", ".\\auto\\test.lua"}
-- {nil, "auto/test.lua", 0, 21, "main", "", ".\\auto\\test.lua"}

-- [bar1] at line 9 (defined in test.lua:8)
-- [bar] at line 16 (defined in test.lua:13)
-- main chunk at line 21 (defined in test.lua)
-- 及时更新堆栈窗口信息
function debugger:updateStack()
  local debugger = self
  
  -- 已经连接且处于调试状态
  local canupdate = debugger.client and not debugger.running
  if canupdate then
  
    local stack, _, err = debugger:stack(debugger:GetDataOptions({maxlength=false}))

    if not stack or #stack == 0 then
      
      if err then -- report an error if any
        lua_xx_debug_errorHandle(3)
      end
      return
    end

    local params = debugger:GetDataOptions({maxlevel=false})
	
    local stackCall = {}
    local index = 1

    for _,frame in ipairs(stack) do
	
      while true do
        -- check if the stack includes expected structures
        if type(frame) ~= "table" or type(frame[1]) ~= "table" or #frame[1] < 7 then break end

        -- call = { source.name, source.source, source.linedefined,
        --   source.currentline, source.what, source.namewhat, source.short_src }
        local call = frame[1]

          -- format the function name to a readable user string
        local func = call[5] == "main" and "main chunk"
          or call[5] == "C" and (call[1] and "["..call[1].."]" or "C function")
          or call[5] == "tail" and "tail call"
          or (call[1] and "["..call[1].."]" or "anonymous function")

        -- format the function treeitem text string, including the function name
        local text = func ..
          (call[4] == -1 and '' or " at line "..call[4]) ..
          (call[5] ~= "main" and call[5] ~= "Lua" and ''
          or (call[3] > 0 and " (defined at "..call[7]..":"..call[3]..")"
                              or " (defined in "..call[7]..")"))
                  
        if string.find(text, "mobdebug.lua") then break end
     
        stackCall[index]  = {}
        
        stackCall[index].func = text
        stackCall[index].values = {}
        
        for name,val in pairs(type(frame[2]) == "table" and frame[2] or {}) do
          -- format the variable name, value as a single line and,
          -- if not a simple type, the string value.
          local value = val[1]
          
          stackCall[index].values[name]  = value
        end

        -- add the upvalues for this call stack level 
        for name,val in pairs(type(frame[3]) == "table" and frame[3] or {}) do
          -- format the variable name, value as a single line and,
          -- if not a simple type, the string value.
          local value = val[1]
            
          stackCall[index].values[name] = value
        end
        
        index  = index + 1
        
        break
      end	  
    end
	
    lua_xx_debug_updateStackInfo(stackCall)
  end
end

-- 设置/删除断点
function debugger:breakpoint(file, line, state)
  local debugger = self
  if debugger.running then
   return debugger:handle((state and "asetb " or "adelb ") .. file .. " " .. line)
  end
   return debugger:handle((state and "setb " or "delb ") .. file .. " " .. line)
end

-- 开始调试
function debugger:startDebug(ip)
  local debugger = self

  -- 监听指定ip指定端口
  local server, err = socket.bind(ip, debugger.portnumber)
  if not server then
    lua_xx_debug_errorHandle(1)
    return false
  end
  
  -- 启动本地lua调试
  local bResult = lua_xx_debug_runScript()
  
  -- 设置远程调试服务超时，并等待远程链接
  if bResult then
    server:settimeout(10)
    local client = server:accept()
    if not client then
        lua_xx_debug_printLog("debug server had accept false")
      return false
    end
	
    lua_xx_debug_printLog("debug server had accept ok")
	
    local options = debugger.options or {}
    if options.refuseonconflict == nil then options.refuseonconflict = ide.config.debugger.refuseonconflict end

    if debugger.server and options.refuseonconflict then
      client:close()
      lua_xx_debug_errorHandle(2)
      return
    end
  
    debugger = ide:SetDebugger(debugger:init({
        server = server,
        client = client,
        runtocursor = nil,
    }))
	
    debugger.listening = true

    -- 清理所有断点, 调试器刚加载的时候，读取文本编辑器所设置的断点，并设置断点
    debugger:reSetBreakpoints()
  
    lua_xx_debug_updateDebugStatus(0)

    lua_xx_debug_printLog("now send run command")
    debugger:exec("run")
  end
  
  return bResult
 end
 
 -- 分离调试
 function debugger:detach(cmd)
  local debugger = self
  if not debugger.client then return end
  debugger:exec(cmd or "done")
end

-- IDE 使用接口
-- 开始调试
function ide:startDebug(ip) return self:GetDebugger():startDebug(ip) end

-- 单步步过
function ide:over() return self:GetDebugger():exec("over") end

-- 从当前函数返回
function ide:out()  return self:GetDebugger():exec("out") end

-- 执行
function ide:run() return self:GetDebugger():exec("run") end

-- 单步步入
function ide:step() return self:GetDebugger():exec("step") end

-- 获取当前上下文环境和返回值
function ide:evaluate(exp) 
  local debugger = self:GetDebugger()
  local params = debugger:GetDataOptions({maxlevel = false})
  return self:GetDebugger():handle('eval ' .. exp .. todeb(params)) 
end

-- 当前环境下执行语句
function ide:execute(exp) 
  local debugger = self:GetDebugger()
  local params = debugger:GetDataOptions({maxlevel = false})
  return self:GetDebugger():handle('exec '.. exp .. todeb(params)) 
end

-- 返回堆栈信息
function ide:stack() 
  local debugger = self:GetDebugger()
  local params = debugger:GetDataOptions({maxlevel = false})
  return self:GetDebugger():handle('stack' .. todeb(params)) 
end

function ide:serializeValue(value)
	return self:GetDebugger():serializeValue(value)
end

-- 设置断点
function ide:breakpoint(file, line, state)
  self:GetDebugger():breakpoint(file, line, state)
end

-- 删除所有断点
function ide:DelAllbreakpoints()
  self:GetDebugger():handle("delallb")
end

-- 停止调试
function ide:stopDebug()
  self:GetDebugger():detach("exit")
end
