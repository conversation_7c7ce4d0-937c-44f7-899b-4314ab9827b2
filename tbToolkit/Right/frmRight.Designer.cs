﻿namespace tbToolkit
{
    partial class frmRight
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(frmRight));
            this.btnCommand = new iTong.Components.tbImageCheckBox();
            this.btnScriptFun = new iTong.Components.tbImageCheckBox();
            this.btnUI = new iTong.Components.tbImageCheckBox();
            this.btnDevice = new iTong.Components.tbImageCheckBox();
            this.btnBookMark = new iTong.Components.tbImageCheckBox();
            this.btnStake = new iTong.Components.tbImageCheckBox();
            this.pnlCommand = new iTong.Components.tbPanel();
            this.txtCommand = new iTong.Components.tbSearch();
            this.tvwCommand = new iTong.Components.tbTreeView();
            this.pnlScriptFun = new iTong.Components.tbPanel();
            this.txtScriptFun = new iTong.Components.tbSearch();
            this.pnlButton = new iTong.Components.tbPanel();
            this.pnlContent = new iTong.Components.tbPanel();
            this.pnlCommand.SuspendLayout();
            this.pnlScriptFun.SuspendLayout();
            this.pnlButton.SuspendLayout();
            this.pnlContent.SuspendLayout();
            this.SuspendLayout();
            // 
            // btnCommand
            // 
            this.btnCommand.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnCommand.Checked = false;
            this.btnCommand.Location = new System.Drawing.Point(0, 0);
            this.btnCommand.Name = "btnCommand";
            this.btnCommand.Size = new System.Drawing.Size(25, 84);
            this.btnCommand.TabIndex = 0;
            this.btnCommand.tbCheckedImage = null;
            this.btnCommand.tbSplit = "0,0,0,0";
            this.btnCommand.tbTwoStateImage = ((System.Drawing.Image)(resources.GetObject("btnCommand.tbTwoStateImage")));
            this.btnCommand.tbUnCheckImage = null;
            this.btnCommand.CheckedChanged += new System.EventHandler(this.btnCommand_CheckedChanged);
            // 
            // btnScriptFun
            // 
            this.btnScriptFun.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnScriptFun.Checked = false;
            this.btnScriptFun.Location = new System.Drawing.Point(0, 88);
            this.btnScriptFun.Name = "btnScriptFun";
            this.btnScriptFun.Size = new System.Drawing.Size(25, 84);
            this.btnScriptFun.TabIndex = 1;
            this.btnScriptFun.tbCheckedImage = null;
            this.btnScriptFun.tbSplit = "0,0,0,0";
            this.btnScriptFun.tbTwoStateImage = ((System.Drawing.Image)(resources.GetObject("btnScriptFun.tbTwoStateImage")));
            this.btnScriptFun.tbUnCheckImage = null;
            this.btnScriptFun.CheckedChanged += new System.EventHandler(this.btnCommand_CheckedChanged);
            // 
            // btnUI
            // 
            this.btnUI.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnUI.Checked = false;
            this.btnUI.Location = new System.Drawing.Point(0, 176);
            this.btnUI.Name = "btnUI";
            this.btnUI.Size = new System.Drawing.Size(25, 84);
            this.btnUI.TabIndex = 2;
            this.btnUI.tbCheckedImage = null;
            this.btnUI.tbSplit = "0,0,0,0";
            this.btnUI.tbTwoStateImage = ((System.Drawing.Image)(resources.GetObject("btnUI.tbTwoStateImage")));
            this.btnUI.tbUnCheckImage = null;
            this.btnUI.CheckedChanged += new System.EventHandler(this.btnCommand_CheckedChanged);
            // 
            // btnDevice
            // 
            this.btnDevice.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDevice.Checked = false;
            this.btnDevice.Location = new System.Drawing.Point(0, 264);
            this.btnDevice.Name = "btnDevice";
            this.btnDevice.Size = new System.Drawing.Size(25, 84);
            this.btnDevice.TabIndex = 3;
            this.btnDevice.tbCheckedImage = null;
            this.btnDevice.tbSplit = "0,0,0,0";
            this.btnDevice.tbTwoStateImage = ((System.Drawing.Image)(resources.GetObject("btnDevice.tbTwoStateImage")));
            this.btnDevice.tbUnCheckImage = null;
            this.btnDevice.CheckedChanged += new System.EventHandler(this.btnCommand_CheckedChanged);
            // 
            // btnBookMark
            // 
            this.btnBookMark.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnBookMark.Checked = false;
            this.btnBookMark.Location = new System.Drawing.Point(0, 352);
            this.btnBookMark.Name = "btnBookMark";
            this.btnBookMark.Size = new System.Drawing.Size(25, 84);
            this.btnBookMark.TabIndex = 4;
            this.btnBookMark.tbCheckedImage = null;
            this.btnBookMark.tbSplit = "0,0,0,0";
            this.btnBookMark.tbTwoStateImage = ((System.Drawing.Image)(resources.GetObject("btnBookMark.tbTwoStateImage")));
            this.btnBookMark.tbUnCheckImage = null;
            this.btnBookMark.CheckedChanged += new System.EventHandler(this.btnCommand_CheckedChanged);
            // 
            // btnStake
            // 
            this.btnStake.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnStake.Checked = false;
            this.btnStake.Location = new System.Drawing.Point(0, 440);
            this.btnStake.Name = "btnStake";
            this.btnStake.Size = new System.Drawing.Size(25, 84);
            this.btnStake.TabIndex = 5;
            this.btnStake.tbCheckedImage = null;
            this.btnStake.tbSplit = "0,0,0,0";
            this.btnStake.tbTwoStateImage = ((System.Drawing.Image)(resources.GetObject("btnStake.tbTwoStateImage")));
            this.btnStake.tbUnCheckImage = null;
            this.btnStake.CheckedChanged += new System.EventHandler(this.btnCommand_CheckedChanged);
            // 
            // pnlCommand
            // 
            this.pnlCommand.Controls.Add(this.txtCommand);
            this.pnlCommand.Controls.Add(this.tvwCommand);
            this.pnlCommand.Location = new System.Drawing.Point(9, 9);
            this.pnlCommand.Margin = new System.Windows.Forms.Padding(0);
            this.pnlCommand.Name = "pnlCommand";
            this.pnlCommand.Size = new System.Drawing.Size(213, 363);
            this.pnlCommand.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlCommand.TabIndex = 6;
            this.pnlCommand.tbBackgroundImage = null;
            this.pnlCommand.tbShowWatermark = false;
            this.pnlCommand.tbSplit = "0,0,0,0";
            this.pnlCommand.tbWatermark = null;
            this.pnlCommand.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlCommand.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // txtCommand
            // 
            this.txtCommand.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCommand.BackColor = System.Drawing.Color.Transparent;
            this.txtCommand.ForeColor = System.Drawing.SystemColors.WindowText;
            this.txtCommand.Location = new System.Drawing.Point(4, 5);
            this.txtCommand.MaxLength = 32767;
            this.txtCommand.Name = "txtCommand";
            this.txtCommand.Padding = new System.Windows.Forms.Padding(5);
            this.txtCommand.ReadOnly = false;
            this.txtCommand.SearchText = "";
            this.txtCommand.SearchTipText = "查找";
            this.txtCommand.ShowClear = false;
            this.txtCommand.ShowClearAlways = false;
            this.txtCommand.ShowMore = false;
            this.txtCommand.ShowSearch = true;
            this.txtCommand.Size = new System.Drawing.Size(204, 23);
            this.txtCommand.TabIndex = 0;
            this.txtCommand.tbImageSearchState = iTong.Components.ImageState.ThreeState;
            this.txtCommand.Timer = true;
            this.txtCommand.TimerInterval = 0.5D;
            this.txtCommand.SizeChanged += new System.EventHandler(this.txtCommand_SizeChanged);
            // 
            // tvwCommand
            // 
            this.tvwCommand.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tvwCommand.AutoScroll = true;
            this.tvwCommand.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(212)))), ((int)(((byte)(221)))), ((int)(((byte)(229)))));
            this.tvwCommand.ImageList = null;
            this.tvwCommand.Indent = 19;
            this.tvwCommand.ItemHeight = 28;
            this.tvwCommand.Location = new System.Drawing.Point(0, 32);
            this.tvwCommand.Margin = new System.Windows.Forms.Padding(0);
            this.tvwCommand.Name = "tvwCommand";
            this.tvwCommand.Padding = new System.Windows.Forms.Padding(0, 10, 0, 10);
            this.tvwCommand.PathSeparator = "\\";
            this.tvwCommand.SelectedNode = null;
            this.tvwCommand.Size = new System.Drawing.Size(213, 330);
            this.tvwCommand.TabIndex = 1;
            this.tvwCommand.TabStop = true;
            this.tvwCommand.tbBackgroundImage = ((System.Drawing.Image)(resources.GetObject("tvwCommand.tbBackgroundImage")));
            this.tvwCommand.tbNodeCountSelectedColor = System.Drawing.Color.Empty;
            this.tvwCommand.tbNodeDiff = 4;
            this.tvwCommand.tbNodeFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold);
            this.tvwCommand.tbNodeForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.tvwCommand.tbNodeLabelFont = new System.Drawing.Font("微软雅黑", 9F);
            this.tvwCommand.tbNodeLabelForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(119)))), ((int)(((byte)(143)))), ((int)(((byte)(178)))));
            this.tvwCommand.tbNodePadding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.tvwCommand.tbNodeSelectedBackground = ((System.Drawing.Image)(resources.GetObject("tvwCommand.tbNodeSelectedBackground")));
            this.tvwCommand.tbNodeShadowColor = System.Drawing.Color.Empty;
            this.tvwCommand.tbSplit = "1,27,3,3";
            this.tvwCommand.WrapContents = false;
            // 
            // pnlScriptFun
            // 
            this.pnlScriptFun.Controls.Add(this.txtScriptFun);
            this.pnlScriptFun.Location = new System.Drawing.Point(228, 9);
            this.pnlScriptFun.Margin = new System.Windows.Forms.Padding(0);
            this.pnlScriptFun.Name = "pnlScriptFun";
            this.pnlScriptFun.Size = new System.Drawing.Size(213, 363);
            this.pnlScriptFun.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlScriptFun.TabIndex = 7;
            this.pnlScriptFun.tbBackgroundImage = null;
            this.pnlScriptFun.tbShowWatermark = false;
            this.pnlScriptFun.tbSplit = "0,0,0,0";
            this.pnlScriptFun.tbWatermark = null;
            this.pnlScriptFun.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlScriptFun.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // txtScriptFun
            // 
            this.txtScriptFun.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtScriptFun.BackColor = System.Drawing.Color.Transparent;
            this.txtScriptFun.ForeColor = System.Drawing.SystemColors.WindowText;
            this.txtScriptFun.Location = new System.Drawing.Point(4, 5);
            this.txtScriptFun.MaxLength = 32767;
            this.txtScriptFun.Name = "txtScriptFun";
            this.txtScriptFun.Padding = new System.Windows.Forms.Padding(5);
            this.txtScriptFun.ReadOnly = false;
            this.txtScriptFun.SearchText = "";
            this.txtScriptFun.SearchTipText = "查找";
            this.txtScriptFun.ShowClear = false;
            this.txtScriptFun.ShowClearAlways = false;
            this.txtScriptFun.ShowMore = false;
            this.txtScriptFun.ShowSearch = true;
            this.txtScriptFun.Size = new System.Drawing.Size(204, 23);
            this.txtScriptFun.TabIndex = 0;
            this.txtScriptFun.tbImageSearchState = iTong.Components.ImageState.ThreeState;
            this.txtScriptFun.Timer = true;
            this.txtScriptFun.TimerInterval = 0.5D;
            this.txtScriptFun.SizeChanged += new System.EventHandler(this.txtCommand_SizeChanged);
            // 
            // pnlButton
            // 
            this.pnlButton.Controls.Add(this.btnCommand);
            this.pnlButton.Controls.Add(this.btnScriptFun);
            this.pnlButton.Controls.Add(this.btnUI);
            this.pnlButton.Controls.Add(this.btnStake);
            this.pnlButton.Controls.Add(this.btnDevice);
            this.pnlButton.Controls.Add(this.btnBookMark);
            this.pnlButton.Dock = System.Windows.Forms.DockStyle.Right;
            this.pnlButton.Location = new System.Drawing.Point(769, 0);
            this.pnlButton.Margin = new System.Windows.Forms.Padding(0);
            this.pnlButton.Name = "pnlButton";
            this.pnlButton.Size = new System.Drawing.Size(25, 662);
            this.pnlButton.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlButton.TabIndex = 8;
            this.pnlButton.tbBackgroundImage = null;
            this.pnlButton.tbShowWatermark = false;
            this.pnlButton.tbSplit = "0,0,0,0";
            this.pnlButton.tbWatermark = null;
            this.pnlButton.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlButton.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // pnlContent
            // 
            this.pnlContent.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnlContent.Controls.Add(this.pnlCommand);
            this.pnlContent.Controls.Add(this.pnlScriptFun);
            this.pnlContent.Location = new System.Drawing.Point(0, 0);
            this.pnlContent.Margin = new System.Windows.Forms.Padding(0);
            this.pnlContent.Name = "pnlContent";
            this.pnlContent.Size = new System.Drawing.Size(768, 662);
            this.pnlContent.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage;
            this.pnlContent.TabIndex = 8;
            this.pnlContent.tbBackgroundImage = null;
            this.pnlContent.tbShowWatermark = false;
            this.pnlContent.tbSplit = "0,0,0,0";
            this.pnlContent.tbWatermark = null;
            this.pnlContent.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft;
            this.pnlContent.tbWatermarkLocation = new System.Drawing.Point(0, 0);
            // 
            // frmRight
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(794, 662);
            this.Controls.Add(this.pnlContent);
            this.Controls.Add(this.pnlButton);
            this.Name = "frmRight";
            this.tbTitleBackColor = System.Drawing.Color.Transparent;
            this.Text = "frmRight";
            this.pnlCommand.ResumeLayout(false);
            this.pnlScriptFun.ResumeLayout(false);
            this.pnlButton.ResumeLayout(false);
            this.pnlContent.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private iTong.Components.tbImageCheckBox btnCommand;
        private iTong.Components.tbImageCheckBox btnScriptFun;
        private iTong.Components.tbImageCheckBox btnUI;
        private iTong.Components.tbImageCheckBox btnDevice;
        private iTong.Components.tbImageCheckBox btnBookMark;
        private iTong.Components.tbImageCheckBox btnStake;
        private iTong.Components.tbPanel pnlCommand;
        private iTong.Components.tbSearch txtCommand;
        private iTong.Components.tbTreeView tvwCommand;
        private iTong.Components.tbPanel pnlScriptFun;
        private iTong.Components.tbSearch txtScriptFun;
        private iTong.Components.tbPanel pnlButton;
        private iTong.Components.tbPanel pnlContent;
    }
}