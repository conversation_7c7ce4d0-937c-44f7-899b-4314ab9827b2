﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnCommand.tbTwoStateImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADQAAAANCAYAAADmDU2OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADIGlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20v
        eGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxu
        czpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1w
        OkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9
        InhtcC5paWQ6MkY0MzI2MzE0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiIHhtcE1NOkRvY3VtZW50SUQ9
        InhtcC5kaWQ6MkY0MzI2MzI0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiPiA8eG1wTU06RGVyaXZlZEZy
        b20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoyRjQzMjYyRjQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoyRjQzMjYzMDQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVu
        ZD0iciI/PsI5V6YAAANkSURBVEhLvZZdSJNRGMeFKGqVEXUXphJkJa5UKLEuysIkKEojUC+yxK4KCrwp
        gkq9MJIihnoxTFQqMxXSCGSJLstVm/OztmTzI+bU4dza8CN1e3r+a6/oejdXVAd+vM95znnP+/+/53DO
        CQlZXkK5uoORekGMnGghopC/jb9vefNB61vFL4SXl5fnWCwW5dzc3DhAjBzaGPRZVv62GYznp/y2voiu
        ri4ZDyhabhbJad2uFJJIU4PFxH2rJTFntgmmIdTspG0jDqo2OcjEULCo+gZJP2jyJ4+gnYePEH7GVsyC
        2+12Wa1W0mq11Nra6gExcgsLLjqamUuSvWdpfXxmkGTQxvh0W/iRi1KYGrK5o4xWt9VgddPvoO630Fut
        nlifR4uYPmiXy+XZbGgLTEXb7fbO4eFhamlpEQVtb9S9JIk5TRv2Zy3+KcRL62jwrYclXXrN6bW9467n
        naMuWgpmHQg5sbhO2UMWm5NW0mez2TrYyx4YipucnHSqVCpqb28XBW0m8xhJok96zCw1IhgQcr710APn
        p/iVyPav83bl4DwJQLxvLBhEXmiXN36kCauNVtLHhpzwAkPxOp3OiakMRFcPz1AAQ75GhXpowgUYkioM
        c/aX+u/UoPsJBPvGQk4whvb7tSr6rPsSUBt06/X6KXiBoViNRtPJpigQT+pfsaFTfpecvxkKT77cDEON
        +tn6mt5Zetw9Q1WdM1TJCMIRC3XfOP/pe3rX8SmgNuhWq9VYcrEwFFVUVJRjNBpdAwMDJIbBYKBD5656
        drilwleKNyVctOeV1qZiydX1zeyp0nxzVminqUwzTXJ1cBTUdlPxCw0ZjeLaoBfaCwsLsSnshKFNzEGF
        QiEbGRkhMa7lF5Nk9wne3TKCMrQ5MXsi8vgVReULZTKbicGmgA9l3XgozX/2oae0bdxZ3jFN4NEKFCvH
        6HpZM9W39Ylqg15ohwevF8+BuZ1JkslkObwWlWazeWJ0dNTW39+vqampyUtPT88pKSlJcTgccSxuX5BE
        c78IZrXPgYnT/hyTy9xiCpjbzB3v8y4/73ljIf+A6xVpaWkNTU1NQzwjU9AIrdAM7UwYs3j4r/EmDvPz
        GJPoBTFy6Iw+y8q/uCn4uS38kT64w9/DNWeXF8TI/XLtgbP/aAifC0rfDyiIHMs1luDjAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="btnScriptFun.tbTwoStateImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADQAAAANCAYAAADmDU2OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADIGlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20v
        eGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxu
        czpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1w
        OkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9
        InhtcC5paWQ6MkY0MzI2MzE0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiIHhtcE1NOkRvY3VtZW50SUQ9
        InhtcC5kaWQ6MkY0MzI2MzI0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiPiA8eG1wTU06RGVyaXZlZEZy
        b20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoyRjQzMjYyRjQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoyRjQzMjYzMDQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVu
        ZD0iciI/PsI5V6YAAANkSURBVEhLvZZdSJNRGMeFKGqVEXUXphJkJa5UKLEuysIkKEojUC+yxK4KCrwp
        gkq9MJIihnoxTFQqMxXSCGSJLstVm/OztmTzI+bU4dza8CN1e3r+a6/oejdXVAd+vM95znnP+/+/53DO
        CQlZXkK5uoORekGMnGghopC/jb9vefNB61vFL4SXl5fnWCwW5dzc3DhAjBzaGPRZVv62GYznp/y2voiu
        ri4ZDyhabhbJad2uFJJIU4PFxH2rJTFntgmmIdTspG0jDqo2OcjEULCo+gZJP2jyJ4+gnYePEH7GVsyC
        2+12Wa1W0mq11Nra6gExcgsLLjqamUuSvWdpfXxmkGTQxvh0W/iRi1KYGrK5o4xWt9VgddPvoO630Fut
        nlifR4uYPmiXy+XZbGgLTEXb7fbO4eFhamlpEQVtb9S9JIk5TRv2Zy3+KcRL62jwrYclXXrN6bW9467n
        naMuWgpmHQg5sbhO2UMWm5NW0mez2TrYyx4YipucnHSqVCpqb28XBW0m8xhJok96zCw1IhgQcr710APn
        p/iVyPav83bl4DwJQLxvLBhEXmiXN36kCauNVtLHhpzwAkPxOp3OiakMRFcPz1AAQ75GhXpowgUYkioM
        c/aX+u/UoPsJBPvGQk4whvb7tSr6rPsSUBt06/X6KXiBoViNRtPJpigQT+pfsaFTfpecvxkKT77cDEON
        +tn6mt5Zetw9Q1WdM1TJCMIRC3XfOP/pe3rX8SmgNuhWq9VYcrEwFFVUVJRjNBpdAwMDJIbBYKBD5656
        drilwleKNyVctOeV1qZiydX1zeyp0nxzVminqUwzTXJ1cBTUdlPxCw0ZjeLaoBfaCwsLsSnshKFNzEGF
        QiEbGRkhMa7lF5Nk9wne3TKCMrQ5MXsi8vgVReULZTKbicGmgA9l3XgozX/2oae0bdxZ3jFN4NEKFCvH
        6HpZM9W39Ylqg15ohwevF8+BuZ1JkslkObwWlWazeWJ0dNTW39+vqampyUtPT88pKSlJcTgccSxuX5BE
        c78IZrXPgYnT/hyTy9xiCpjbzB3v8y4/73ljIf+A6xVpaWkNTU1NQzwjU9AIrdAM7UwYs3j4r/EmDvPz
        GJPoBTFy6Iw+y8q/uCn4uS38kT64w9/DNWeXF8TI/XLtgbP/aAifC0rfDyiIHMs1luDjAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="btnUI.tbTwoStateImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADQAAAANCAYAAADmDU2OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADIGlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20v
        eGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxu
        czpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1w
        OkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9
        InhtcC5paWQ6MkY0MzI2MzE0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiIHhtcE1NOkRvY3VtZW50SUQ9
        InhtcC5kaWQ6MkY0MzI2MzI0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiPiA8eG1wTU06RGVyaXZlZEZy
        b20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoyRjQzMjYyRjQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoyRjQzMjYzMDQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVu
        ZD0iciI/PsI5V6YAAANkSURBVEhLvZZdSJNRGMeFKGqVEXUXphJkJa5UKLEuysIkKEojUC+yxK4KCrwp
        gkq9MJIihnoxTFQqMxXSCGSJLstVm/OztmTzI+bU4dza8CN1e3r+a6/oejdXVAd+vM95znnP+/+/53DO
        CQlZXkK5uoORekGMnGghopC/jb9vefNB61vFL4SXl5fnWCwW5dzc3DhAjBzaGPRZVv62GYznp/y2voiu
        ri4ZDyhabhbJad2uFJJIU4PFxH2rJTFntgmmIdTspG0jDqo2OcjEULCo+gZJP2jyJ4+gnYePEH7GVsyC
        2+12Wa1W0mq11Nra6gExcgsLLjqamUuSvWdpfXxmkGTQxvh0W/iRi1KYGrK5o4xWt9VgddPvoO630Fut
        nlifR4uYPmiXy+XZbGgLTEXb7fbO4eFhamlpEQVtb9S9JIk5TRv2Zy3+KcRL62jwrYclXXrN6bW9467n
        naMuWgpmHQg5sbhO2UMWm5NW0mez2TrYyx4YipucnHSqVCpqb28XBW0m8xhJok96zCw1IhgQcr710APn
        p/iVyPav83bl4DwJQLxvLBhEXmiXN36kCauNVtLHhpzwAkPxOp3OiakMRFcPz1AAQ75GhXpowgUYkioM
        c/aX+u/UoPsJBPvGQk4whvb7tSr6rPsSUBt06/X6KXiBoViNRtPJpigQT+pfsaFTfpecvxkKT77cDEON
        +tn6mt5Zetw9Q1WdM1TJCMIRC3XfOP/pe3rX8SmgNuhWq9VYcrEwFFVUVJRjNBpdAwMDJIbBYKBD5656
        drilwleKNyVctOeV1qZiydX1zeyp0nxzVminqUwzTXJ1cBTUdlPxCw0ZjeLaoBfaCwsLsSnshKFNzEGF
        QiEbGRkhMa7lF5Nk9wne3TKCMrQ5MXsi8vgVReULZTKbicGmgA9l3XgozX/2oae0bdxZ3jFN4NEKFCvH
        6HpZM9W39Ylqg15ohwevF8+BuZ1JkslkObwWlWazeWJ0dNTW39+vqampyUtPT88pKSlJcTgccSxuX5BE
        c78IZrXPgYnT/hyTy9xiCpjbzB3v8y4/73ljIf+A6xVpaWkNTU1NQzwjU9AIrdAM7UwYs3j4r/EmDvPz
        GJPoBTFy6Iw+y8q/uCn4uS38kT64w9/DNWeXF8TI/XLtgbP/aAifC0rfDyiIHMs1luDjAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="btnDevice.tbTwoStateImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADQAAAANCAYAAADmDU2OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADIGlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20v
        eGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxu
        czpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1w
        OkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9
        InhtcC5paWQ6MkY0MzI2MzE0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiIHhtcE1NOkRvY3VtZW50SUQ9
        InhtcC5kaWQ6MkY0MzI2MzI0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiPiA8eG1wTU06RGVyaXZlZEZy
        b20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoyRjQzMjYyRjQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoyRjQzMjYzMDQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVu
        ZD0iciI/PsI5V6YAAANkSURBVEhLvZZdSJNRGMeFKGqVEXUXphJkJa5UKLEuysIkKEojUC+yxK4KCrwp
        gkq9MJIihnoxTFQqMxXSCGSJLstVm/OztmTzI+bU4dza8CN1e3r+a6/oejdXVAd+vM95znnP+/+/53DO
        CQlZXkK5uoORekGMnGghopC/jb9vefNB61vFL4SXl5fnWCwW5dzc3DhAjBzaGPRZVv62GYznp/y2voiu
        ri4ZDyhabhbJad2uFJJIU4PFxH2rJTFntgmmIdTspG0jDqo2OcjEULCo+gZJP2jyJ4+gnYePEH7GVsyC
        2+12Wa1W0mq11Nra6gExcgsLLjqamUuSvWdpfXxmkGTQxvh0W/iRi1KYGrK5o4xWt9VgddPvoO630Fut
        nlifR4uYPmiXy+XZbGgLTEXb7fbO4eFhamlpEQVtb9S9JIk5TRv2Zy3+KcRL62jwrYclXXrN6bW9467n
        naMuWgpmHQg5sbhO2UMWm5NW0mez2TrYyx4YipucnHSqVCpqb28XBW0m8xhJok96zCw1IhgQcr710APn
        p/iVyPav83bl4DwJQLxvLBhEXmiXN36kCauNVtLHhpzwAkPxOp3OiakMRFcPz1AAQ75GhXpowgUYkioM
        c/aX+u/UoPsJBPvGQk4whvb7tSr6rPsSUBt06/X6KXiBoViNRtPJpigQT+pfsaFTfpecvxkKT77cDEON
        +tn6mt5Zetw9Q1WdM1TJCMIRC3XfOP/pe3rX8SmgNuhWq9VYcrEwFFVUVJRjNBpdAwMDJIbBYKBD5656
        drilwleKNyVctOeV1qZiydX1zeyp0nxzVminqUwzTXJ1cBTUdlPxCw0ZjeLaoBfaCwsLsSnshKFNzEGF
        QiEbGRkhMa7lF5Nk9wne3TKCMrQ5MXsi8vgVReULZTKbicGmgA9l3XgozX/2oae0bdxZ3jFN4NEKFCvH
        6HpZM9W39Ylqg15ohwevF8+BuZ1JkslkObwWlWazeWJ0dNTW39+vqampyUtPT88pKSlJcTgccSxuX5BE
        c78IZrXPgYnT/hyTy9xiCpjbzB3v8y4/73ljIf+A6xVpaWkNTU1NQzwjU9AIrdAM7UwYs3j4r/EmDvPz
        GJPoBTFy6Iw+y8q/uCn4uS38kT64w9/DNWeXF8TI/XLtgbP/aAifC0rfDyiIHMs1luDjAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="btnBookMark.tbTwoStateImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADQAAAANCAYAAADmDU2OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADIGlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20v
        eGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxu
        czpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1w
        OkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9
        InhtcC5paWQ6MkY0MzI2MzE0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiIHhtcE1NOkRvY3VtZW50SUQ9
        InhtcC5kaWQ6MkY0MzI2MzI0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiPiA8eG1wTU06RGVyaXZlZEZy
        b20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoyRjQzMjYyRjQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoyRjQzMjYzMDQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVu
        ZD0iciI/PsI5V6YAAANkSURBVEhLvZZdSJNRGMeFKGqVEXUXphJkJa5UKLEuysIkKEojUC+yxK4KCrwp
        gkq9MJIihnoxTFQqMxXSCGSJLstVm/OztmTzI+bU4dza8CN1e3r+a6/oejdXVAd+vM95znnP+/+/53DO
        CQlZXkK5uoORekGMnGghopC/jb9vefNB61vFL4SXl5fnWCwW5dzc3DhAjBzaGPRZVv62GYznp/y2voiu
        ri4ZDyhabhbJad2uFJJIU4PFxH2rJTFntgmmIdTspG0jDqo2OcjEULCo+gZJP2jyJ4+gnYePEH7GVsyC
        2+12Wa1W0mq11Nra6gExcgsLLjqamUuSvWdpfXxmkGTQxvh0W/iRi1KYGrK5o4xWt9VgddPvoO630Fut
        nlifR4uYPmiXy+XZbGgLTEXb7fbO4eFhamlpEQVtb9S9JIk5TRv2Zy3+KcRL62jwrYclXXrN6bW9467n
        naMuWgpmHQg5sbhO2UMWm5NW0mez2TrYyx4YipucnHSqVCpqb28XBW0m8xhJok96zCw1IhgQcr710APn
        p/iVyPav83bl4DwJQLxvLBhEXmiXN36kCauNVtLHhpzwAkPxOp3OiakMRFcPz1AAQ75GhXpowgUYkioM
        c/aX+u/UoPsJBPvGQk4whvb7tSr6rPsSUBt06/X6KXiBoViNRtPJpigQT+pfsaFTfpecvxkKT77cDEON
        +tn6mt5Zetw9Q1WdM1TJCMIRC3XfOP/pe3rX8SmgNuhWq9VYcrEwFFVUVJRjNBpdAwMDJIbBYKBD5656
        drilwleKNyVctOeV1qZiydX1zeyp0nxzVminqUwzTXJ1cBTUdlPxCw0ZjeLaoBfaCwsLsSnshKFNzEGF
        QiEbGRkhMa7lF5Nk9wne3TKCMrQ5MXsi8vgVReULZTKbicGmgA9l3XgozX/2oae0bdxZ3jFN4NEKFCvH
        6HpZM9W39Ylqg15ohwevF8+BuZ1JkslkObwWlWazeWJ0dNTW39+vqampyUtPT88pKSlJcTgccSxuX5BE
        c78IZrXPgYnT/hyTy9xiCpjbzB3v8y4/73ljIf+A6xVpaWkNTU1NQzwjU9AIrdAM7UwYs3j4r/EmDvPz
        GJPoBTFy6Iw+y8q/uCn4uS38kT64w9/DNWeXF8TI/XLtgbP/aAifC0rfDyiIHMs1luDjAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="btnStake.tbTwoStateImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADQAAAANCAYAAADmDU2OAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADIGlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4wLWMwNjAgNjEuMTM0Nzc3LCAyMDEwLzAyLzEyLTE3OjMyOjAwICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20v
        eGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxu
        czpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1w
        OkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M1IFdpbmRvd3MiIHhtcE1NOkluc3RhbmNlSUQ9
        InhtcC5paWQ6MkY0MzI2MzE0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiIHhtcE1NOkRvY3VtZW50SUQ9
        InhtcC5kaWQ6MkY0MzI2MzI0MUE5MTFFMDk3RkI5NDZBNUVGRjBDMzEiPiA8eG1wTU06RGVyaXZlZEZy
        b20gc3RSZWY6aW5zdGFuY2VJRD0ieG1wLmlpZDoyRjQzMjYyRjQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIgc3RSZWY6ZG9jdW1lbnRJRD0ieG1wLmRpZDoyRjQzMjYzMDQxQTkxMUUwOTdGQjk0NkE1RUZGMEMz
        MSIvPiA8L3JkZjpEZXNjcmlwdGlvbj4gPC9yZGY6UkRGPiA8L3g6eG1wbWV0YT4gPD94cGFja2V0IGVu
        ZD0iciI/PsI5V6YAAANkSURBVEhLvZZdSJNRGMeFKGqVEXUXphJkJa5UKLEuysIkKEojUC+yxK4KCrwp
        gkq9MJIihnoxTFQqMxXSCGSJLstVm/OztmTzI+bU4dza8CN1e3r+a6/oejdXVAd+vM95znnP+/+/53DO
        CQlZXkK5uoORekGMnGghopC/jb9vefNB61vFL4SXl5fnWCwW5dzc3DhAjBzaGPRZVv62GYznp/y2voiu
        ri4ZDyhabhbJad2uFJJIU4PFxH2rJTFntgmmIdTspG0jDqo2OcjEULCo+gZJP2jyJ4+gnYePEH7GVsyC
        2+12Wa1W0mq11Nra6gExcgsLLjqamUuSvWdpfXxmkGTQxvh0W/iRi1KYGrK5o4xWt9VgddPvoO630Fut
        nlifR4uYPmiXy+XZbGgLTEXb7fbO4eFhamlpEQVtb9S9JIk5TRv2Zy3+KcRL62jwrYclXXrN6bW9467n
        naMuWgpmHQg5sbhO2UMWm5NW0mez2TrYyx4YipucnHSqVCpqb28XBW0m8xhJok96zCw1IhgQcr710APn
        p/iVyPav83bl4DwJQLxvLBhEXmiXN36kCauNVtLHhpzwAkPxOp3OiakMRFcPz1AAQ75GhXpowgUYkioM
        c/aX+u/UoPsJBPvGQk4whvb7tSr6rPsSUBt06/X6KXiBoViNRtPJpigQT+pfsaFTfpecvxkKT77cDEON
        +tn6mt5Zetw9Q1WdM1TJCMIRC3XfOP/pe3rX8SmgNuhWq9VYcrEwFFVUVJRjNBpdAwMDJIbBYKBD5656
        drilwleKNyVctOeV1qZiydX1zeyp0nxzVminqUwzTXJ1cBTUdlPxCw0ZjeLaoBfaCwsLsSnshKFNzEGF
        QiEbGRkhMa7lF5Nk9wne3TKCMrQ5MXsi8vgVReULZTKbicGmgA9l3XgozX/2oae0bdxZ3jFN4NEKFCvH
        6HpZM9W39Ylqg15ohwevF8+BuZ1JkslkObwWlWazeWJ0dNTW39+vqampyUtPT88pKSlJcTgccSxuX5BE
        c78IZrXPgYnT/hyTy9xiCpjbzB3v8y4/73ljIf+A6xVpaWkNTU1NQzwjU9AIrdAM7UwYs3j4r/EmDvPz
        GJPoBTFy6Iw+y8q/uCn4uS38kT64w9/DNWeXF8TI/XLtgbP/aAifC0rfDyiIHMs1luDjAAAAAElFTkSu
        QmCC
</value>
  </data>
  <data name="tvwCommand.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAoCAIAAABINPzXAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADImlUWHRYTUw6Y29tLmFkb2Jl
        LnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQi
        Pz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENv
        cmUgNS4zLWMwMTEgNjYuMTQ1NjYxLCAyMDEyLzAyLzA2LTE0OjU2OjI3ICAgICAgICAiPiA8cmRmOlJE
        RiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8
        cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20v
        eGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxu
        czpzdFJlZj0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlUmVmIyIgeG1w
        OkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M2IChXaW5kb3dzKSIgeG1wTU06SW5zdGFuY2VJ
        RD0ieG1wLmlpZDoxNEUwQjZCNEJFQzgxMUUzQjY4NUQwMDYzMDY2RTcyNiIgeG1wTU06RG9jdW1lbnRJ
        RD0ieG1wLmRpZDoxNEUwQjZCNUJFQzgxMUUzQjY4NUQwMDYzMDY2RTcyNiI+IDx4bXBNTTpEZXJpdmVk
        RnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOjE0RTBCNkIyQkVDODExRTNCNjg1RDAwNjMwNjZF
        NzI2IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjE0RTBCNkIzQkVDODExRTNCNjg1RDAwNjMwNjZF
        NzI2Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQg
        ZW5kPSJyIj8+GkEmsgAAACNJREFUOE9j+PL1Ox7EMCqNLXxGgwVruhgNltFgQcouA5kcAMtXfj1RkW7F
        AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="tvwCommand.tbNodeSelectedBackground" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAABFSURBVEhLY1Dp/d0wEJhhICwF2TlqMd3iezSoR4OaZiEwmrhoFrToRfNoUI8GNc1CYDRx0Sxo
        R/PxaOIaTVw0C4GRl7gAAYejGugt9qkAAAAASUVORK5CYII=
</value>
  </data>
</root>