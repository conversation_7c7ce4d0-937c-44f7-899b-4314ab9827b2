﻿using iTong.Components;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace tbToolkit
{
    public partial class frmRight : tbBaseGuiForm
    {
        private MainForm mMainForm = null;

        #region  初始化
     
        public frmRight()
        {
            InitializeComponent();
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.mMainForm = MainForm.GetMainForm();
            this.InitPanel();
        }

        protected override void SetInterface()
        {
            base.SetInterface();
         
        }

        private void InitPanel()
        {
            this.btnCommand.Tag = this.pnlCommand;
            this.btnScriptFun.Tag = this.pnlScriptFun;
            this.btnUI.Tag = null;
            this.btnDevice.Tag = null;
            this.btnBookMark.Tag = null;
            this.btnStake.Tag = null;

            this.pnlCommand.Dock = DockStyle.Fill;
            this.pnlScriptFun.Dock = DockStyle.Fill;

            this.btnCommand.Checked = true;
        }

        #endregion

        private void btnCommand_CheckedChanged(object sender, EventArgs e)
        {
            if (sender == null)
                return;

            tbImageCheckBox cbx = (tbImageCheckBox)sender;

            //选中的节点就显示相应的panel
            if (cbx.Tag!= null && cbx.Tag.GetType() == typeof(tbPanel) && cbx.Checked)
            {
                tbPanel pnl = (tbPanel)cbx.Tag;
                pnl.BringToFront();
            }

            //把其它的checkbox设置成没选中
            foreach (Control item in this.pnlButton.Controls)
            {
                if (item != cbx && item.GetType() == typeof(tbImageCheckBox))
                {
                    ((tbImageCheckBox)item).Checked = false;
                }
            }

            //如果全部关闭就把右边收起来，
            //有选中就打开右边显示出来
            bool blnCheck = false;
            foreach (Control item in this.pnlButton.Controls)
            {
                if (((tbImageCheckBox)item).Checked)
                {
                    blnCheck = true;
                }
            }

            if (blnCheck)
                this.mMainForm.SetRightShow();
            else
                this.mMainForm.SetRightHide();
        }

        private void txtCommand_SizeChanged(object sender, EventArgs e)
        {
            tbSearch txt=(tbSearch)sender;
            txt.Width = txt.Parent.Width - 9;
        }

    }
}
