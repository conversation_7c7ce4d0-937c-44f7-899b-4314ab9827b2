﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{7D59A33B-81BD-48C1-BED7-6F013CF72914}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>tbToolkit</RootNamespace>
    <AssemblyName>tbToolkit</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Debug\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <PlatformTarget>x64</PlatformTarget>
    <OutputPath>..\bin\x64\Release\</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <OutputPath>..\bin\x86\Debug\</OutputPath>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\bin\x86\Release\</OutputPath>
    <PlatformTarget>x86</PlatformTarget>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ScintillaNET, Version=3.5.10.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Code\ScintillaNET.dll</HintPath>
    </Reference>
    <Reference Include="SuperSocket.ClientEngine40, Version=0.6.0.4, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\WebSocket4Net\ImportDll\SuperSocket.ClientEngine40.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CodeManage\frmCodeManage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CodeManage\frmCodeManage.Designer.cs">
      <DependentUpon>frmCodeManage.cs</DependentUpon>
    </Compile>
    <Compile Include="Components\tbLua.cs" />
    <Compile Include="Components\tbLuaDavice.cs" />
    <Compile Include="Components\tbScintilla.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="DebugView\frmDebug.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DebugView\frmDebug.Designer.cs">
      <DependentUpon>frmDebug.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Project\frmNewFile.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project\frmNewFile.Designer.cs">
      <DependentUpon>frmNewFile.cs</DependentUpon>
    </Compile>
    <Compile Include="Project\frmNewFolder.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project\frmNewFolder.Designer.cs">
      <DependentUpon>frmNewFolder.cs</DependentUpon>
    </Compile>
    <Compile Include="Project\frmNewProject.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project\frmNewProject.Designer.cs">
      <DependentUpon>frmNewProject.cs</DependentUpon>
    </Compile>
    <Compile Include="Project\frmProject.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Project\frmProject.Designer.cs">
      <DependentUpon>frmProject.cs</DependentUpon>
    </Compile>
    <Compile Include="Project\Keyword.cs" />
    <Compile Include="Project\ProjectFileIni.cs" />
    <Compile Include="Project\ProjectHelper.cs" />
    <Compile Include="Project\ProjectObject.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Right\frmRight.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Right\frmRight.Designer.cs">
      <DependentUpon>frmRight.cs</DependentUpon>
    </Compile>
    <Compile Include="Utils\HotKeyManager.cs" />
    <Compile Include="Utils\SearchManager.cs" />
    <EmbeddedResource Include="CodeManage\frmCodeManage.resx">
      <DependentUpon>frmCodeManage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DebugView\frmDebug.resx">
      <DependentUpon>frmDebug.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project\frmNewFile.resx">
      <DependentUpon>frmNewFile.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project\frmNewFolder.resx">
      <DependentUpon>frmNewFolder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project\frmNewProject.resx">
      <DependentUpon>frmNewProject.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Project\frmProject.resx">
      <DependentUpon>frmProject.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Right\frmRight.resx">
      <DependentUpon>frmRight.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="lualibs\client\cmd.cmd">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\ltn12.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\main.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\mime.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\core.dll.idb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\core.exp">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\core.ilk">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\core.lib">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\ftp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\headers.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\http.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\smtp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\tp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\socket\url.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\mobdebug.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\ftp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\headers.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\http.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\ltn12.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\mbox.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\mime.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\smtp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\socket.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\tp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\client\xxdebug\socket\url.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\debug.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\main.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\mobdebug\mobdebug.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\ftp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\headers.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\http.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\ltn12.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\mbox.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\mime.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\smtp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\socket.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\tp.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\socket\url.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="lualibs\xxdebug\mobdebug.lua">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Code\LuaBridge.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Code\ScintillaNET.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lualibs\client\lua51.exe">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lualibs\client\socket\core.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lualibs\client\socket\mime.pdb">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="lualibs\socket\来源luasocket-3.0.txt" />
    <None Include="Resources\file_file_small.png" />
    <None Include="Resources\file_folder_small.png" />
    <None Include="Resources\btn_4_blank.png" />
    <None Include="Resources\frm_bg_blank.png" />
    <None Include="Resources\btn_min.png" />
    <None Include="Resources\btn_max.png" />
    <None Include="Resources\btn_close.png" />
    <None Include="Resources\btn_restore.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Android\Android17.csproj">
      <Project>{8f52fde3-9023-4d42-8fca-3d813f2f41e3}</Project>
      <Name>Android17</Name>
    </ProjectReference>
    <ProjectReference Include="..\Components\Components17.vbproj">
      <Project>{6618a65b-8041-48dc-8765-e2c596dd4642}</Project>
      <Name>Components17</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreMisc\CoreMisc17.csproj">
      <Project>{14b0f2fd-cdc4-4173-9f96-780ee7faf989}</Project>
      <Name>CoreMisc17</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreResources\CoreReses17.csproj">
      <Project>{3211112c-cd0e-4c40-a0b1-77081fb5cc04}</Project>
      <Name>CoreReses17</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreTag\CoreTag17.csproj">
      <Project>{5aad17ba-e88c-495f-8a7f-075f4dbf88b0}</Project>
      <Name>CoreTag17</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtilCS\CoreUtilCS17.csproj">
      <Project>{d80bf913-8ebe-4d9b-bc0b-556d65a1df41}</Project>
      <Name>CoreUtilCS17</Name>
    </ProjectReference>
    <ProjectReference Include="..\CoreUtil\CoreUtil17.vbproj">
      <Project>{c7983172-adcf-4723-a227-1fbd45b4a6c3}</Project>
      <Name>CoreUtil17</Name>
    </ProjectReference>
    <ProjectReference Include="..\ProtoBuf\ProtoBuf17.csproj">
      <Project>{12bd1255-d61e-4230-a810-7e8401150347}</Project>
      <Name>ProtoBuf17</Name>
    </ProjectReference>
    <ProjectReference Include="..\SharpZLib\ICSharpCode.SharpZLib17.csproj">
      <Project>{14334b71-7f58-4e96-9dee-c970b1fd6e4c}</Project>
      <Name>ICSharpCode.SharpZLib17</Name>
    </ProjectReference>
    <ProjectReference Include="..\WebSocket4Net\WebSocket4Net.Net17.csproj">
      <Project>{2953af5b-001b-43a5-8d72-c94bbae2dbc4}</Project>
      <Name>WebSocket4Net.Net17</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>move "$(TargetDir)Code\*.*" "$(TargetDir)\"</PostBuildEvent>
  </PropertyGroup>
</Project>