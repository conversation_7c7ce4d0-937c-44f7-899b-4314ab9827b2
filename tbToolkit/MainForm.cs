﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace tbToolkit
{
    public partial class MainForm : tbBaseGuiForm
    {
        private frmProject mProject = new frmProject();
        private frmCodeManage mCodeManage = new frmCodeManage();
        private frmDebug mDebug = new frmDebug();
        private frmRight mRight = new frmRight();
        private tbLuaDavice mDevice = null;

        #region  主窗体对外属性

        public frmProject ProjectForm
        {
            get
            {
                return this.mProject;
            }
        }

        public frmCodeManage CodeManageForm
        {
            get
            {
                return this.mCodeManage;
            }
        }

        public frmDebug DebugForm
        {
            get
            {
                return this.mDebug;
            }
        }

        public frmRight RightForm
        {
            get
            {
                return this.mRight;
            }
        }

        public tbLuaDavice Device
        {
            get
            {
                return this.mDevice;
            }
        }

        #endregion

        #region  初始化

        public MainForm()
        {
            InitializeComponent();
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.Size = new Size(1230, 880);
            this.CanResize = true;
            Utility.AddForm2Panel(this.mProject, this.pnlLeft, true);
            Utility.AddForm2Panel(this.mCodeManage, this.pnlCodeMamage, true);
            Utility.AddForm2Panel(this.mDebug, this.pnlBottom, true);
            Utility.AddForm2Panel(this.mRight, this.pnlRight, true);

            this.mProject.OpenFile += this.mProject_OpenFile;
            this.mProject.DeleteProjectEvent += this.mProject_DeleteProject;
            this.mProject.DeleteProjectFile += this.mProject_DeleteProjectFile;

            tbLua.SetCallBack(this.mDebug.CallBackFun);
            this.InitHotkeys();
            this.mDevice = new tbLuaDavice("*************", "16667");
        }

        protected override void SetInterface()
        {
            base.SetInterface();
            this.btn_close.tbToolTip = "关闭";
            this.btn_normal.tbToolTip = "最大化";
            this.btn_minimize.tbToolTip = "最小化";
        }

        private void MainForm_Resize(object sender, EventArgs e)
        {
            if (this.btn_normal != null)
            {
                if (this.WindowState == FormWindowState.Maximized)
                {
                    this.btn_normal.tbToolTip = "还原";
                    this.btn_normal.tbBackgroundImage = Properties.Resources.btn_restore;
                }
                else if (this.WindowState == FormWindowState.Normal)
                {
                    this.btn_normal.tbToolTip = "最大化";
                    this.btn_normal.tbBackgroundImage = Properties.Resources.btn_max;
                }
            }
        }

        private void InitHotkeys()
        {
            HotKeyManager.AddHotKey(this, OpenSearch, Keys.F, true);
            HotKeyManager.AddHotKey(this, ZoomIn, Keys.Oemplus, true);
            HotKeyManager.AddHotKey(this, ZoomOut, Keys.OemMinus, true);
            HotKeyManager.AddHotKey(this, ZoomDefault, Keys.D0, true);
            HotKeyManager.AddHotKey(this, Save, Keys.S, true);
        }

        #endregion

        #region Spliter

        private Point m_MousePoint = new Point(0, 0);
        private bool m_MouseDown = false;
        private int m_MinWidth = 100;
        private float mintRightWidth = 100;

        private void pnlSplitLeft_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.m_MousePoint = new Point(e.X, e.Y);
                this.m_MouseDown = true;
            }
        }

        private void pnlSplitLeft_MouseMove(object sender, MouseEventArgs e)
        {
            if (this.m_MouseDown && e.Button == MouseButtons.Left)
            {
                int intChanged = e.X - this.m_MousePoint.X;
                int intWidth = (int)this.tlpMain.ColumnStyles[0].Width + intChanged;
                if (intWidth < m_MinWidth)
                {
                    intWidth = m_MinWidth;
                }
                else if (intWidth >= (this.tlpMain.ClientRectangle.Width - this.pnlSplitLeft.ClientRectangle.Width - this.m_MinWidth - (int)this.tlpMain.ColumnStyles[4].Width))
                {
                    intWidth = (this.tlpMain.ClientRectangle.Width - this.pnlSplitLeft.ClientRectangle.Width - this.m_MinWidth - (int)this.tlpMain.ColumnStyles[4].Width);
                }
                this.tlpMain.ColumnStyles[0].Width = intWidth;
            }
        }

        private void pnlSplitLeft_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.m_MouseDown = false;
            }
        }

        private void pnlSplitBottom_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.m_MousePoint = new Point(e.X, e.Y);
                this.m_MouseDown = true;
            }
        }

        private void pnlSplitBottom_MouseMove(object sender, MouseEventArgs e)
        {
            if (this.m_MouseDown && e.Button == MouseButtons.Left)
            {
                int intChanged = e.Y + this.m_MousePoint.Y;
                int intWidth = (int)this.tlpMain.RowStyles[2].Height - intChanged;
                if (intWidth < m_MinWidth)
                {
                    intWidth = m_MinWidth;
                }
                else if (intWidth >= (this.tlpMain.ClientRectangle.Height - this.pnlSplitBottom.ClientRectangle.Height - this.m_MinWidth))
                {
                    intWidth = (this.tlpMain.ClientRectangle.Height - this.pnlSplitBottom.ClientRectangle.Height - this.m_MinWidth);
                }
                this.tlpMain.RowStyles[2].Height = intWidth;
            }
        }

        private void pnlSplitBottom_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.m_MouseDown = false;
            }
        }

        private void pnlSplitRight_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.m_MousePoint = new Point(e.X, e.Y);
                this.m_MouseDown = true;
            }
        }

        private void pnlSplitRight_MouseMove(object sender, MouseEventArgs e)
        {
            if (this.tlpMain.ColumnStyles[4].Width <= 25)
                return;

            if (this.m_MouseDown && e.Button == MouseButtons.Left)
            {
                int intChanged = e.X + this.m_MousePoint.X;
                int intWidth = (int)this.tlpMain.ColumnStyles[4].Width - intChanged;
                if (intWidth < m_MinWidth)
                {
                    intWidth = m_MinWidth;
                }
                else if (intWidth >= (this.tlpMain.ClientRectangle.Width - this.pnlSplitRight.ClientRectangle.Width - this.m_MinWidth - (int)this.tlpMain.ColumnStyles[0].Width))
                {
                    intWidth = (this.tlpMain.ClientRectangle.Width - this.pnlSplitRight.ClientRectangle.Width - this.m_MinWidth - (int)this.tlpMain.ColumnStyles[0].Width);
                }
                this.tlpMain.ColumnStyles[4].Width = intWidth;
            }
        }

        private void pnlSplitRight_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                this.m_MouseDown = false;
            }
        }

        public void SetRightHide()
        {
            this.mintRightWidth = this.tlpMain.ColumnStyles[4].Width;
            this.tlpMain.ColumnStyles[4].Width = 25;
        }

        public void SetRightShow()
        {
            if (this.tlpMain.ColumnStyles[4].Width <= 25)
                this.tlpMain.ColumnStyles[4].Width = this.mintRightWidth;
        }

        #endregion

        #region btn事件

        private void btnRun_Click(object sender, EventArgs e)
        {
            //string strFilePath=this.mProject.PackageProject(false);
            //this.mDevice.SendFile(strFilePath, false);

            tbLua.InitLuaBridge();
            tbLua.ide_startDebug("127.0.0.1");
        }

        private void btnFile_Click(object sender, EventArgs e)
        {
            this.menuFile.Show(this.btnFile, new Point(0, this.btnFile.Height));
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            this.menuEdit.Show(this.btnEdit, new Point(0, this.btnEdit.Height));
        }

        private void btnProject_Click(object sender, EventArgs e)
        {
            this.menuProject.Show(this.btnProject, new Point(0, this.btnProject.Height));
        }

        private void btnPublish_Click(object sender, EventArgs e)
        {
            this.menuPublish.Show(this.btnPublish, new Point(0, this.btnPublish.Height));
        }

        private void btnHelper_Click(object sender, EventArgs e)
        {
            this.menuHelper.Show(this.btnHelper, new Point(0, this.btnHelper.Height));
            //DataTransferEventArgs eArgs = new DataTransferEventArgs("C:\\123.txt", "C:\\123.txt");
            //bool bln=iTong.Android.AndroidTcpInternet.PostRawStreamEx("C:\\123.txt", "http://*************:16667/files",null, eArgs);

            //if (this.mDevice == null)
            //    this.mDevice = new tbLuaDavice("*************", "16667");
            //this.mDevice.Send("123######");
        }

        private void btnDebug_Click(object sender, EventArgs e)
        {
            string strFilePath = this.mProject.PackageProject(true);
            this.mDevice.SendFile(strFilePath, false);
        }

        #endregion

        #region menu事件

        private void tsmiCloseCurrentPage_Click(object sender, EventArgs e)
        {
            this.mCodeManage.CloseCurrentPage();
        }

        private void tsmiExit_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void tsmiNewFile_Click(object sender, EventArgs e)
        {
            this.mProject.CreateFile();
        }

        private void tsmiSaveFile_Click(object sender, EventArgs e)
        {
            this.Save();
        }

        private void tsmiNewProject_Click(object sender, EventArgs e)
        {
            this.mProject.CreateProject();
        }

        private void tsmiSaveProject_Click(object sender, EventArgs e)
        {
            this.mProject.SaveProjject();
        }

        private void tsmiImportProject_Click(object sender, EventArgs e)
        {

        }

        private void tsmiSetWorkSpace_Click(object sender, EventArgs e)
        {

        }

        #endregion

        #region Project窗体事件

        private void mProject_OpenFile(object sender, TreeNodeMouseClickEventArgs e)
        {
            this.mCodeManage.LoadFile((FileInfo)e.Node.Tag);
        }

        private void mProject_DeleteProject(object sender, DeleteProjectEventArgs e)
        {
            this.mCodeManage.ClosePageByProject(e.ProjectObj);
        }

        private void mProject_DeleteProjectFile(object sender, DeleteProjectFileEventArgs e)
        {
            this.mCodeManage.ClosePageByPath(e.FilePath);
        }

        #endregion

        #region 取得主窗体

        public static MainForm GetMainForm()
        {
            MainForm frmMain = null;
            try
            {
                foreach (Form item in Application.OpenForms)
                {
                    if (item is MainForm)
                    {
                        frmMain = (MainForm)item;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "MainForm_GetMainForm");
            }
            return frmMain;
        }

        #endregion

        #region HotKey

        #region Indent / Outdent

        private void Indent()
        {
            // we use this hack to send "Shift+Tab" to scintilla, since there is no known API to indent,
            // although the indentation function exists. Pressing TAB with the editor focused confirms this.
            GenerateKeystrokes("{TAB}");
        }

        private void Outdent()
        {
            // we use this hack to send "Shift+Tab" to scintilla, since there is no known API to outdent,
            // although the indentation function exists. Pressing Shift+Tab with the editor focused confirms this.
            GenerateKeystrokes("+{TAB}");
        }

        private void GenerateKeystrokes(string keys)
        {
            if (this.mCodeManage.SelectedPage() == null)
                return;

            HotKeyManager.Enable = false;
            this.mCodeManage.SelectedPage().Focus();
            SendKeys.Send(keys);
            HotKeyManager.Enable = true;
        }

        #endregion

        #region Zoom

        private void ZoomIn()
        {
            if (this.mCodeManage.SelectedPage() == null)
                return;
            this.mCodeManage.SelectedPage().ZoomIn();
        }

        private void ZoomOut()
        {
            if (this.mCodeManage.SelectedPage() == null)
                return;
            this.mCodeManage.SelectedPage().ZoomOut();
        }

        private void ZoomDefault()
        {
            if (this.mCodeManage.SelectedPage() == null)
                return;
            this.mCodeManage.SelectedPage().Zoom = 0;
        }

        #endregion

        private void Save()
        {
            if (this.mCodeManage.SelectedPage() == null)
                return;
            this.mCodeManage.SelectedPage().SaveFile();
        }

        private void OpenSearch()
        {
            if (this.mCodeManage.SelectedPage() == null)
                return;
        }

        #endregion
       
    }
}
