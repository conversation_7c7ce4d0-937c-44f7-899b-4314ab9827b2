﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.NetworkInformation;
using System.Text;

namespace tbToolkit
{
    public class ProjectObject
    {
        //项目名称
        private string mName = string.Empty;
        public string Name
        {
            get
            {
                return this.mName;
            }
            set
            {
                this.mName = value;
            }
        }
        //后缀
        private string mExtension = "xsp";                  //生成的目标文件（传给手机端使用）
        private string mProjExtension = "xspproj";          //项目文件列表

        private Dictionary<string, object> mFileList = new Dictionary<string, object>();//用户添加的代码文件、资源文件列表
        public Dictionary<string, object> FileList
        {
            get
            {
                return this.mFileList;
            }
            set
            {
                this.mFileList = value;
            }
        }

        //关键字按文件区分，再按行区分
        private Dictionary<string, Dictionary<int, Keyword>> mKeywords = new Dictionary<string, Dictionary<int, Keyword>>();//代码中的关键字
        public Dictionary<string, Dictionary<int, Keyword>> Keywords
        {
            get
            {
                return this.mKeywords;
            }
            set
            {
                this.mKeywords = value;
            }
        }

        public string ProjectPath                           //系统自动生成的项目名称
        {
            get
            {
                return Path.Combine(Folder.tbToolkitWorkSpace, this.mName);
            }
        }
        public string ProjectDelllTag                       //有这个标记的项目是删除掉的项目不加载
        {
            get
            {
                return Path.Combine(this.ProjectPath, "tbdel");
            }
        }
        public string ProjectPathUI
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mExtension + "\\ui");
            }
        }
        public string ProjectPathSRC
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mExtension + "\\src");
            }
        }
        public string ProjectPathRES
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mExtension + "\\res");
            }
        }
        public string ProjectPathLIB
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mExtension + "\\lib");
            }
        }

        public string ManifestFile
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mExtension + "\\manifest.json");
            }
        }
        public string XSPFile
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mName + ".xsp");
            }
        }
        public string ProjectFile                           //项目文件列表存放的文件
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mName + "." + this.mProjExtension);
            }
        }
        public string TargetFile                            //生成的目标文件（传给手机端使用）
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mName + "." + this.mExtension);
            }
        }
        public string CodeParaFile                          //文件中的类参数列表保存文件
        {
            get
            {
                return Path.Combine(this.ProjectPath, this.mName);
            }
        }

        public ProjectObject()
        {

        }

        public ProjectObject(string strProjectName)
        {
            this.mName = strProjectName;
        }

        #region "--- 文件读取写 ---"

        public void CreateProject(string strProjectName)
        {
            try
            {
                this.mName = strProjectName;
                Folder.CheckFolder(this.ProjectPath);
                Folder.CheckFolder(this.ProjectPathUI);
                Folder.CheckFolder(this.ProjectPathSRC);
                Folder.CheckFolder(this.ProjectPathRES);
                Folder.CheckFolder(this.ProjectPathLIB);

                File.Create(this.ProjectFile).Close();
                File.Create(this.CodeParaFile).Close();
                File.Create(this.ManifestFile).Close();

                string strMain = Path.Combine(this.ProjectPathSRC, "main.lua");
                string strUI = Path.Combine(this.ProjectPathUI, "ui.json");
                File.Create(strMain).Close();
                File.Create(strUI).Close();
                this.mFileList.Add(strMain, new FileInfo(strMain));
                this.mFileList.Add(strUI, new FileInfo(strUI));

                this.CreateManifestFile("");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectObject_CreateProject");
            }
        }

        private void CreateManifestFile(string strHash)
        {
            this.CreateManifestFile(strHash, "main.lua", "100", "1.0.0");
        }

        private void CreateManifestFile(string strHash, string strMainLua, string strTengine, string strVersion)
        {
            string strValue = "{" + string.Format("\"bin_sha1\":\"{0}\",\"main\":\"{1}\",\"tengine\":{2},\"version\":\"{3}\"", strHash, strMainLua, strTengine, strVersion) + "}";
            this.SaveFile(this.ManifestFile, strValue);
        }

        public bool CheckProjectExist(string strProjectName)
        {
            bool blnExist = false;
            string path = Path.Combine(Folder.tbToolkitWorkSpace, strProjectName);
            if (Directory.Exists(path))
            {
                blnExist = true;
            }
            return blnExist;
        }

        public void SaveFile(string strFilePath, string strContents)
        {
            try
            {
                File.WriteAllText(strFilePath, strContents);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectObject_SaveFile");
            }
        }

        public bool ReNameFile(string strFilePath, string strNewName)
        {
            bool blnSucceed = false;
            try
            {
                string strPath = Path.GetDirectoryName(strFilePath);
                string strNewFilePath = Path.Combine(strPath, strNewName);
                if (this.mFileList.ContainsKey(strPath))
                {
                    FileInfo info = (FileInfo)this.mFileList[strPath];
                    info.MoveTo(strNewFilePath);
                    if (File.Exists(info.FullName))
                    {
                        blnSucceed = true;
                    }
                    this.mFileList.Remove(strPath);
                    this.mFileList.Add(info.FullName, info);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectObject_ReNameFile");
            }

            return blnSucceed;
        }

        public bool DeleteProject(bool blnDeleteFiles)
        {
            bool blnSucceed = false;
            try
            {
                //如果存在tbdll文件也是移除的项目，但是文件没删除掉。
                if (blnDeleteFiles)
                {
                    Directory.Delete(this.ProjectPath, true);
                }
                else
                {
                    File.Create(this.ProjectDelllTag).Close();
                }
                blnSucceed = true;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectObject_Delete");
            }

            return blnSucceed;
        }

        public bool ReNameProject(string strNewName)
        {
            return this.FolderRename(this.ProjectPath, Path.Combine(Folder.tbToolkitWorkSpace, strNewName));
        }

        public bool FolderRename(string strFolderPath, string strDesPath)
        {
            bool blnSucceed = false;
            DirectoryInfo directoryinfo = new DirectoryInfo(strFolderPath);

            //读取当前目录文件信息
            foreach (var item in directoryinfo.GetFiles())
            {
                string destPath = item.FullName.Replace(strFolderPath, strDesPath);
                if (File.Exists(item.FullName))
                {
                    //判断是否重名
                    if (!File.Exists(destPath))
                    {
                        item.MoveTo(destPath);
                    }
                }
            }

            return blnSucceed;
        }

        public bool IsTBProject()
        {
            bool blnSucceed = false;
            try
            {
                if (File.Exists(this.ProjectFile) && !File.Exists(this.ProjectDelllTag))
                {
                    blnSucceed = true;
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectObject_IsTBProject");
            }

            return blnSucceed;
        }

        public FileInfo CreateFile(string strFilePath)
        {
            FileInfo info = null; ;
            try
            {
                File.Create(strFilePath).Close();
                if (File.Exists(strFilePath))
                {
                    info = new FileInfo(strFilePath);
                    this.mFileList.Add(strFilePath, info);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectObject_CreateFile");
            }
            return info;
        }

        public DirectoryInfo CreateFolder(string strFolderPath)
        {
            DirectoryInfo info = null;
            try
            {
                Directory.CreateDirectory(strFolderPath);
                if (Directory.Exists(strFolderPath))
                {
                    info = new DirectoryInfo(strFolderPath);
                    this.mFileList.Add(strFolderPath, info);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectObject_CreateFolder");
            }
            return info;
        }

        public void DeleteFile(string strPath, bool isFile)
        {
            if (this.mFileList.ContainsKey(strPath))
            {
                this.mFileList.Remove(strPath);
                if (isFile)
                    return;
                //删除文件夹下的所有记录
                string strFolder = strPath.TrimEnd('\\') + "\\";
                List<string> list = new List<string>();
                foreach (string item in this.mFileList.Keys)
                {
                    if (item.StartsWith(strFolder))
                    {
                        list.Add(item);
                    }
                }
                foreach (string item in list)
                {
                    this.mFileList.Remove(item);
                }
            }
        }

        #endregion

        #region "--- 项目文件管理 ---"

        public void LoadProjectFiles()
        {
            this.LoadFileInfoFromIni();
            DirectoryInfo directoryinfo = new DirectoryInfo(Path.Combine(this.ProjectPath, this.mExtension));

            //读取当前目录文件信息
            foreach (DirectoryInfo item in directoryinfo.GetDirectories("*.*", SearchOption.AllDirectories))
            {
                if (this.mFileList.ContainsKey(item.FullName))
                    continue;

                if (!item.FullName.StartsWith(this.ProjectPathUI + "\\") &&
                    !item.FullName.StartsWith(this.ProjectPathSRC + "\\") &&
                    !item.FullName.StartsWith(this.ProjectPathRES + "\\") &&
                    !item.FullName.StartsWith(this.ProjectPathLIB + "\\"))
                    continue;

                this.mFileList.Add(item.FullName, item);
            }

            //读取当前目录文件夹信息
            foreach (FileInfo item in directoryinfo.GetFiles("*.*", SearchOption.AllDirectories))
            {
                if (this.mFileList.ContainsKey(item.FullName))
                    continue;

                if (!item.FullName.StartsWith(this.ProjectPathUI + "\\") &&
                    !item.FullName.StartsWith(this.ProjectPathSRC + "\\") &&
                    !item.FullName.StartsWith(this.ProjectPathRES + "\\") &&
                    !item.FullName.StartsWith(this.ProjectPathLIB + "\\"))
                    continue;

                this.mFileList.Add(item.FullName, item);
            }
        }

        private void LoadFileInfoFromIni()
        {
            List<string> lstFiles = ProjectFileIni.GetFiles(this);
            this.FileList.Clear();
            foreach (string item in lstFiles)
            {
                if (File.Exists(item))
                {
                    FileInfo info = new FileInfo(item);
                    this.FileList.Add(item, info);
                }
            }
        }

        public void SaveProjectFile()
        {
            ProjectFileIni.SetFilePath(this);
        }

        #endregion

        #region "--- 项目方法参数读取保存 ---"

        public void LoadProjectKeywords()
        {
            this.mKeywords.Clear();
            string[] sArr = File.ReadAllLines(this.CodeParaFile);
            foreach (string item in sArr)
            {
                string[] strParas = item.Split('	');
                if (strParas != null && strParas.Length == 4)
                {
                    try
                    {
                        Keyword kInfo = new Keyword();
                        kInfo.Value = strParas[0];
                        kInfo.Path = strParas[1];
                        kInfo.Line = Convert.ToInt32(strParas[2]);
                        kInfo.Type = strParas[3] == "e" ? KeywordType.Element : KeywordType.Function;
                        Dictionary<int, Keyword> dict = null;
                        if (this.mKeywords.ContainsKey(kInfo.Path))
                        {
                            dict = this.mKeywords[kInfo.Path];
                        }
                        else
                        {
                            dict = new Dictionary<int, Keyword>();
                        }
                        dict[kInfo.Line] = kInfo;
                        this.mKeywords[kInfo.Path] = dict;
                    }
                    catch (Exception ex)
                    {
                        Common.LogException(ex.ToString(), "ProjectObject_LoadProjectKeywords");
                    }
                }
            }
        }

        public void SaveProjectKeywords()
        {
            try
            {
                StringBuilder sb = new StringBuilder();
                foreach (Dictionary<int, Keyword> item in this.mKeywords.Values)
                {
                    if (item == null || item.Count <= 0)
                        continue;

                    foreach (Keyword keyItem in item.Values)
                    {
                        sb.AppendLine(string.Format("{0}	{1}	{2}	{3}", keyItem.Value, keyItem.Path, keyItem.Line, keyItem.Type == KeywordType.Element ? "e" : "f"));
                    }
                }
                File.WriteAllText(this.CodeParaFile, sb.ToString());
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectObject_SaveProjectKeywords");
            }
        }

        #endregion

        #region "---   创建xsp文件 ---"

        public string CreateXSPFile(bool blnDebug)
        {
            //清掉生成包的缓存文件
            if (File.Exists(this.XSPFile))
                File.Delete(this.XSPFile);

            string strPathTemp = Folder.GetTempFilePath();
            string strPathProject = Path.Combine(strPathTemp, this.mName);
            string strPathBin = Path.Combine(strPathProject, "bin");
            string strPathRes = Path.Combine(strPathProject, "res");
            string strPathUI = Path.Combine(strPathProject, "ui");
            Folder.CheckFolder(strPathProject);
            Folder.CheckFolder(strPathBin);
            Folder.CheckFolder(strPathRes);
            Folder.CheckFolder(strPathUI);
            try
            {
                //1.Copy代码源文件
                foreach (string strSrcFile in this.mFileList.Keys)
                {
                    if (strSrcFile.StartsWith(this.ProjectPathSRC) && File.Exists(strSrcFile))
                    {
                        string strDesFile = strSrcFile.Replace(this.ProjectPathSRC, strPathBin);
                        Folder.CheckFolder(Path.GetDirectoryName(strDesFile));
                        File.Copy(strSrcFile, strDesFile);
                    }
                    if (strSrcFile.StartsWith(this.ProjectPathRES) && File.Exists(strSrcFile))
                    {
                        string strDesFile = strSrcFile.Replace(this.ProjectPathRES, strPathRes);
                        Folder.CheckFolder(Path.GetDirectoryName(strDesFile));
                        File.Copy(strSrcFile, strDesFile);
                    }
                    if (strSrcFile.StartsWith(this.ProjectPathUI) && File.Exists(strSrcFile))
                    {
                        string strDesFile = strSrcFile.Replace(this.ProjectPathUI, strPathUI);
                        Folder.CheckFolder(Path.GetDirectoryName(strDesFile));
                        File.Copy(strSrcFile, strDesFile);
                    }   
                }
            
                //2.Copy Debug的相关文件
                if (blnDebug)
                {
                    string strLuaDeubgFilePath = Path.Combine(System.Environment.CurrentDirectory, "lualibs\\client\\xxdebug");
                    string strDecXXDebug = Path.Combine(strPathBin, "xxdebug");
                    foreach (string strSrcFile in Directory.GetFiles(strLuaDeubgFilePath, "*.*", SearchOption.AllDirectories))
                    {
                        string strDesFile = strSrcFile.Replace(strLuaDeubgFilePath, strDecXXDebug);
                        Folder.CheckFolder(Path.GetDirectoryName(strDesFile));
                        File.Copy(strSrcFile, strDesFile);
                    }
                    //创建xxdebug.lua文件
                    string strDebugFile = Path.Combine(strPathBin, "xxdebug.lua");
                    File.WriteAllText(strDebugFile, this.CreateDebugContent());
                }

                //3.创建bin.xsc文件
                string strBinZip = Path.Combine(strPathProject, "bin.xsc");
                Utility.PackFiles(strBinZip, strPathBin);
                Directory.Delete(strPathBin,true);
                this.CreateManifestFile(Common.GetMd5HexFromFile(strBinZip));
                File.Copy(this.ManifestFile, Path.Combine(strPathProject, "manifest.json"));

                //4.生成.xsp文件
                string strXspFile = Path.Combine(strPathTemp, this.mName+".xsp");
                Utility.PackFiles(strXspFile, strPathProject);

                //5.Copy到文件目录下
                File.Copy(strXspFile,this.XSPFile);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjextObject_CreateXSPFile");
            }


            return strPathProject;
        }

        private string CreateDebugContent()
        {
            StringBuilder sb = new StringBuilder();
            sb.AppendLine(string.Format("require(\"xxdebug.mobdebug\").start(\"{0}\", {1})", Common.GetIPAddress(), this.GetPort()));

            sb.AppendLine("require(\"main\")");
            return sb.ToString();
        }

        private string GetPort()
        {
            string strPortReturn = string.Empty;
            //获取本地计算机的网络连接和通信统计数据的信息            
            IPGlobalProperties ipGlobalProperties = IPGlobalProperties.GetIPGlobalProperties();

            //返回本地计算机上的所有Tcp监听程序            
            IPEndPoint[] ipsTCP = ipGlobalProperties.GetActiveTcpListeners();
            if (ipsTCP != null && ipsTCP.Length > 0)
            {
                strPortReturn = ipsTCP[0].Port.ToString();
            }
            return strPortReturn;
        }

        #endregion

    }
}
