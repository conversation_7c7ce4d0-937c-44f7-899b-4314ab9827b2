﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;

namespace tbToolkit
{
    public class ProjectHelper
    {

        private List<ProjectObject> mProjectList = new List<ProjectObject>();
        public List<ProjectObject> ProjectList
        {
            get
            {
                return this.mProjectList;
            }
        }

        private Thread threadDeleteProject = null;

        #region "--- 单例 ---"

        protected static object mLocker = new object();
        protected static ProjectHelper mProjectHelper;

        public ProjectHelper()
        {
            //加载文件WorkSpace文件夹下面的文件夹，取出项目列表
            DirectoryInfo directoryinfo = new DirectoryInfo(Folder.tbToolkitWorkSpace);
            foreach (DirectoryInfo item in directoryinfo.GetDirectories())
            {
                ProjectObject obj = new ProjectObject(item.Name);
                if (obj.IsTBProject())
                {
                    this.mProjectList.Add(obj);
                }
            }
        }

        public static ProjectHelper Instance()
        {
            try
            {
                if (mProjectHelper == null)
                {
                    lock (mLocker)
                    {
                        if (mProjectHelper == null)
                            mProjectHelper = new ProjectHelper();
                    }
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectHelper.Instance");
            }

            return mProjectHelper;
        }

        #endregion

        public ProjectObject CreateProject(string strName)
        {
            ProjectObject obj = new ProjectObject();
            obj.CreateProject(strName);
            this.mProjectList.Add(obj);
            return obj;
        }

        public void DeleteProject(ProjectObject proj, bool blnDeleteFiles)
        {
            if (this.mProjectList.Contains(proj))
                this.mProjectList.Remove(proj);

            this.DeleteProjectFiles(proj, blnDeleteFiles);
        }

        private void DeleteProjectFiles(ProjectObject proj, bool blnDeleteFiles)
        {
            if (Utility.CheckThreadIsAlive(this.threadDeleteProject))
                return;
            try
            {
                this.threadDeleteProject = new Thread (new ParameterizedThreadStart(DeleteProjectFilesThread));
                this.threadDeleteProject.IsBackground = true;
                this.threadDeleteProject.Start(new object[] { proj, blnDeleteFiles });
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectHelper_DeleteProjectFiles");
            }
        }

        private void DeleteProjectFilesThread(object obj)
        {
            try
            {
                object[] objArr = (object[])obj;
                ProjectObject proj = (ProjectObject)objArr[0];
                bool blnDeleteFiles = (bool)objArr[1];
                proj.DeleteProject(blnDeleteFiles);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "ProjectHelper_DeleteProjectFilesThread");
            }

        }
    }
}
