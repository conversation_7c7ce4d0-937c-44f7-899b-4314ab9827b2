﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace tbToolkit
{
    public partial class frmNewProject : tbBaseGuiForm
    {

        public string ProjectName
        {
            get;
            set;
        }

        public frmNewProject()
        {
            InitializeComponent();
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.txtProjectName.Text = this.GetProjectName();
        }

        protected override void SetInterface()
        {
            base.SetInterface();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                this.ProjectName = string.Empty;
                
                string strFilePath = Path.Combine(Folder.tbToolkitWorkSpace,this.txtProjectName.Text);
                if (Directory.Exists(strFilePath))
                {
                    tbMessageBox.Show(this, "已存在同名工程！", "消息");
                    return;
                }
                this.ProjectName = this.txtProjectName.Text;
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmNewProject_btnOK_Click");

            }
        }

        private string GetProjectName()
        {
            string strName = string.Empty;
            int intIndex = 0;
            while (true)
            {
                intIndex += 1;
                strName = string.Format("新建工程{0}", intIndex);
                string strFilePath = Path.Combine(Folder.tbToolkitWorkSpace, strName);
                if (Directory.Exists(strFilePath))
                {
                    continue;
                }
                
                break;
            }
           
            return strName;
        }
    }
}
