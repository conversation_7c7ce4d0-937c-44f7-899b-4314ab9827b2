﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace tbToolkit
{
    public partial class frmNewFile : tbBaseGuiForm
    {
        private string mFilePath = string.Empty;

        public string FileName
        {
            get;
            set;
        }

        public frmNewFile(string strFilePath)
        {
            InitializeComponent();
            this.mFilePath = strFilePath;
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.txtFileName.Text = "main";
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                this.FileName = string.Empty;
                string strEx = ".lua";
                if (this.btnJson.Checked)
                    strEx = ".json";
                string strFilePath = Path.Combine(this.mFilePath, this.txtFileName.Text + strEx);
                if (File.Exists(strFilePath))
                {
                    tbMessageBox.Show(this, "已存在同名文件！", "消息");
                    return;
                }
                this.FileName = this.txtFileName.Text + strEx;
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmNewFile_btnOK_Click");
               
            }
        }
    }
}
