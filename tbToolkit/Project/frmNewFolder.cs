﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace tbToolkit
{
    public partial class frmNewFolder : tbBaseGuiForm
    {
        private string mFilePath = string.Empty;
        public string FolderName
        {
            get;
            set;
        }

        public frmNewFolder(string strFilePath)
        {
            InitializeComponent();
            this.mFilePath = strFilePath;
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.txtFolderName.Text = this.GetFolderName();
        }
        protected override void SetInterface()
        {
            base.SetInterface();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                this.FolderName = string.Empty;
                string strFilePath = Path.Combine(this.mFilePath, this.txtFolderName.Text);
                if (Directory.Exists(strFilePath))
                {
                    tbMessageBox.Show(this, "已存在同名文件夹！", "消息");
                    return;
                }
                this.FolderName = this.txtFolderName.Text;
                this.DialogResult = DialogResult.OK;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmNewFolder_btnOK_Click");
            }
        }

        private string GetFolderName()
        {
            string strName = string.Empty;
            int intIndex = 0;
            while (true)
            {
                intIndex += 1;
                strName = string.Format("新建文件夹{0}", intIndex);
                string strFilePath = Path.Combine(this.mFilePath, strName);
                if (Directory.Exists(strFilePath))
                {
                    continue;
                }

                break;
            }

            return strName;
        }
    }
}
