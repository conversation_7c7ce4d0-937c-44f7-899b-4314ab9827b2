﻿using iTong.Components;
using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace tbToolkit
{
    public partial class frmProject : tbBaseGuiForm
    {
        public event EventHandler<TreeNodeMouseClickEventArgs> OpenFile;
        public event EventHandler<DeleteProjectFileEventArgs> DeleteProjectFile;
        public event EventHandler<DeleteProjectEventArgs> DeleteProjectEvent;

        private ProjectHelper mProjectHelper = null;
        private ProjectObject mProjectObject = null;
        private Thread threadLoadProject = null;
        private Thread threadLoadProjectFiles = null;

        #region ---初始化---

        public frmProject()
        {
            InitializeComponent();
        }

        protected override void InitControls()
        {
            base.InitControls();
            this.pnlProjectList.Dock = DockStyle.Fill;
            this.pnlFileList.Dock = DockStyle.Fill;
            this.pnlProjectList.BringToFront();
            this.InitImageList();
            this.LoadProject();
        }

        private void InitImageList()
        {
            this.imlFiles.Images.Clear();
            this.imlFiles.Images.Add("File", Properties.Resources.file_file_small);
            this.imlFiles.Images.Add("Folder", Properties.Resources.file_folder_small);
            this.tvwFileList.ImageList = this.imlFiles;
        }

        protected override void SetInterface()
        {
            base.SetInterface();

        }

        private void LoadProject()
        {
            if (Utility.CheckThreadIsAlive(this.threadLoadProject))
                return;
            try
            {
                this.threadLoadProject = new Thread(LoadProjectThread);
                this.threadLoadProject.IsBackground = true;
                this.threadLoadProject.Start();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmProject_LoadProject");
            }
        }

        private void LoadProjectThread()
        {
            try
            {
                this.mProjectHelper = ProjectHelper.Instance();
                foreach (ProjectObject item in this.mProjectHelper.ProjectList)
                {
                    this.AddProject(item);
                }
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmProject_LoadProjectThread");
            }
        }

        private delegate void AddProjectHandler(ProjectObject item);
        private void AddProject(ProjectObject item)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new AddProjectHandler(AddProject), item);
            }
            else
            {
                tbTreeNode node = this.tvwProjectList.Nodes.Add(new tbTreeNode(item.Name));
                node.TreeNodeDoubleClick += node_TreeNodeDoubleClick;
                node.Tag = item;
            }
        }

        #endregion

        #region ---窗体事件---

        private void node_TreeNodeDoubleClick(object sender, EventArgs e)
        {
            if (sender == null)
            {
                return;
            }
            try
            {
                ProjectObject item = (ProjectObject)((tbTreeNode)sender).Tag;
                this.ShowProject(item);
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmProject_node_TreeNodeDoubleClick");
            }
        }

        private void ShowProject(ProjectObject item)
        {
            this.mProjectObject = item;
            this.mProjectObject.LoadProjectFiles();
            this.lblFileList.Text = item.Name;
            this.pnlFileList.BringToFront();
            this.LoadProjctRoot(item);
        }

        private void LoadProjctRoot(ProjectObject item)
        {
            this.tvwFileList.Nodes.Clear();
            this.SetNodePara(item.ProjectPathSRC, new DirectoryInfo(item.ProjectPathSRC), "源码", "Folder");
            this.SetNodePara(item.ProjectPathUI, new DirectoryInfo(item.ProjectPathUI), "UI界面", "Folder");
            this.SetNodePara(item.ProjectPathRES, new DirectoryInfo(item.ProjectPathRES), "RES文件", "Folder");
            this.SetNodePara(item.ProjectPathLIB, new DirectoryInfo(item.ProjectPathLIB), "LIB", "Folder");

            this.LoadProjectFiles();
        }

        private void SetNodePara(string strName, object objTag, string strText, string strImgKey)
        {
            TreeNode node = this.tvwFileList.Nodes.Add(strText);
            node.Name = strName;
            node.Tag = objTag;
            node.ImageKey = strImgKey;
            node.SelectedImageKey = strImgKey;
            node.Nodes.Add("");
        }

        private void btnBack_Click(object sender, EventArgs e)
        {
            this.pnlProjectList.BringToFront();
            this.SaveProjject();
            this.mProjectObject = null;
        }

        private void tvwFileList_AfterExpand(object sender, TreeViewEventArgs e)
        {
            this.LoadClildNode(e.Node);
        }

        private void tsmiOpenFolder_Click(object sender, EventArgs e)
        {
            TreeNode node = tvwFileList.SelectedNode;
            if (node == null || node.Tag == null)
                return;

            string strOpenFolder = Path.GetDirectoryName(node.Name);
            Common.OpenExplorer(strOpenFolder);
        }

        private void tvwFileList_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node == null || e.Node.Tag == null || e.Node.Tag.GetType() != typeof(FileInfo))
            {
                return;
            }

            //双击文件的时候直接打开显示
            if (this.OpenFile != null)
            {
                this.OpenFile(sender, e);
            }
        }

        private void tsmiNewFile_Click(object sender, EventArgs e)
        {
            this.CreateFile();
        }

        private void tsmiNewFolder_Click(object sender, EventArgs e)
        {
            this.CreateFolder();
        }

        private void tsmiDelete_Click(object sender, EventArgs e)
        {
            this.DeleteFile();
        }

        private void tsmiDeleteProject_Click(object sender, EventArgs e)
        {
            this.DeleteProject();
        }

        private void tsmiNewProject_Click(object sender, EventArgs e)
        {
            this.CreateProject();
        }

        #endregion

        #region ---加载项目文件---

        private void LoadProjectFiles()
        {
            if (Utility.CheckThreadIsAlive(this.threadLoadProjectFiles))
                return;
            try
            {
                this.threadLoadProjectFiles = new Thread(LoadProjectAllFileThread);
                this.threadLoadProjectFiles.IsBackground = true;
                this.threadLoadProjectFiles.Start();
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmProject_LoadProjectFiles");
            }
        }

        private void LoadProjectAllFileThread()
        {
            foreach (TreeNode item in this.tvwFileList.Nodes)
            {
                try
                {
                    if (item.Nodes.Count == 1 && item.Nodes[0].Text == "" && item.Nodes[0].Tag == null)
                    {
                        this.LoadClildNode(item);
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "frmProject_LoadProjectAllFileThread");
                }
            }
        }

        private void LoadClildNode(TreeNode node)
        {
            node.Nodes.Clear();
            if (node.Tag == null)
                return;

            string strPath = string.Empty;
            if (node.Tag.GetType() == typeof(DirectoryInfo))
            {
                strPath = ((DirectoryInfo)node.Tag).FullName;
            }
            if (string.IsNullOrEmpty(strPath))
                return;

            List<object> lstFiles = new List<object>();
            foreach (string item in this.mProjectObject.FileList.Keys)
            {
                if (!item.StartsWith(strPath))
                {
                    continue;
                }
                string strFileName = item.Replace(strPath.TrimEnd('\\') + "\\", "");
                if (!strFileName.Contains("\\") && strFileName.Length > 0)
                {
                    lstFiles.Add(this.mProjectObject.FileList[item]);
                }
            }
            if (lstFiles.Count <= 0)
                return;

            this.tvwFileList.SuspendLayout();
            foreach (var item in lstFiles)
            {
                string strName = string.Empty;
                string strText = string.Empty;
                string strImgKey = string.Empty;
                if (item.GetType() == typeof(FileInfo))
                {
                    FileInfo info = (FileInfo)item;
                    strName = info.FullName;
                    strText = Path.GetFileName(strName);
                    strImgKey = "File";
                }
                else if (item.GetType() == typeof(DirectoryInfo))
                {
                    DirectoryInfo info = (DirectoryInfo)item;
                    strName = info.FullName;
                    strText = Path.GetFileNameWithoutExtension(strName);
                    strImgKey = "Folder";
                }
                this.AddChildNode(node, strName, item, strText, strImgKey);
            }
            this.tvwFileList.ResumeLayout();
        }

        private delegate void AddChildNodeHandler(TreeNode nodeParent, string strName, object objTag, string strText, string strImgKey);
        private void AddChildNode(TreeNode nodeParent, string strName, object objTag, string strText, string strImgKey)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new AddChildNodeHandler(AddChildNode), nodeParent, strName, objTag, strText, strImgKey);
            }
            else
            {
                TreeNode node = nodeParent.Nodes.Add(strText);
                node.Name = strName;
                node.Tag = objTag;
                node.ImageKey = strImgKey;
                node.SelectedImageKey = strImgKey;
                if (strImgKey == "Folder")
                    node.Nodes.Add("");
            }  
        }

        #endregion

        public void SaveProjject()
        {
            if (this.mProjectObject == null)
            {
                return;
            }
            this.mProjectObject.SaveProjectFile();
        }

        public void CreateFile()
        {
            try
            {
                if (this.mProjectObject == null || this.tvwFileList.SelectedNode == null || this.tvwFileList.SelectedNode.Tag == null)
                {
                    tbMessageBox.Show(MainForm.GetMainForm(), "请先选择需要创建文件的目录！", "消息");
                    return;
                }
                string strFilePath = string.Empty;
                TreeNode node = null;
                if (this.tvwFileList.SelectedNode.Tag.GetType() == typeof(FileInfo))
                {
                    FileInfo info = (FileInfo)this.tvwFileList.SelectedNode.Tag;
                    strFilePath = Path.GetDirectoryName(info.FullName);
                    node = this.tvwFileList.SelectedNode.Parent;
                }
                else if (this.tvwFileList.SelectedNode.Tag.GetType() == typeof(DirectoryInfo))
                {
                    DirectoryInfo info = (DirectoryInfo)this.tvwFileList.SelectedNode.Tag;
                    strFilePath = info.FullName;
                    node = this.tvwFileList.SelectedNode;
                }
                frmNewFile frm = new frmNewFile(strFilePath);
                frm.ShowDialog(MainForm.GetMainForm());
                if (string.IsNullOrEmpty(frm.FileName))
                    return;

                string strNewFile = Path.Combine(strFilePath, frm.FileName);
                this.mProjectObject.CreateFile(strNewFile);
                this.AddChildNode(node, strNewFile, this.mProjectObject.FileList[strNewFile], frm.FileName, "File");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmProject_CreateFile");
            }
        }

        public void CreateFolder()
        {
            try
            {
                string strFilePath = string.Empty;
                TreeNode node = null;
                if (this.tvwFileList.SelectedNode.Tag.GetType() == typeof(FileInfo))
                {
                    FileInfo info = (FileInfo)this.tvwFileList.SelectedNode.Tag;
                    strFilePath = Path.GetDirectoryName(info.FullName);
                    node = this.tvwFileList.SelectedNode.Parent;
                }
                else if (this.tvwFileList.SelectedNode.Tag.GetType() == typeof(DirectoryInfo))
                {
                    DirectoryInfo info = (DirectoryInfo)this.tvwFileList.SelectedNode.Tag;
                    strFilePath = info.FullName;
                    node = this.tvwFileList.SelectedNode;
                }
                frmNewFolder frm = new frmNewFolder(strFilePath);
                frm.ShowDialog(MainForm.GetMainForm());
                if (string.IsNullOrEmpty(frm.FolderName))
                    return;

                string strFolderFile = Path.Combine(strFilePath, frm.FolderName);
                this.mProjectObject.CreateFolder(strFolderFile);
                this.AddChildNode(node, strFolderFile, this.mProjectObject.FileList[strFolderFile], frm.FolderName, "Folder");
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmProject_CreateFolder");
            }
        }

        public void CreateProject()
        {
            frmNewProject frm = new frmNewProject();
            frm.ShowDialog(MainForm.GetMainForm());
            if (string.IsNullOrEmpty(frm.ProjectName))
                return;

            ProjectObject obj = this.mProjectHelper.CreateProject(frm.ProjectName);
            this.AddProject(obj);
            this.ShowProject(obj);
        }

        private void DeleteFile()
        {
            string strFilePath = string.Empty;
            bool isFile = false;
            string strMsg = "是否删除文件？";
            TreeNode node = null;
            if (this.tvwFileList.SelectedNode.Tag.GetType() == typeof(FileInfo))
            {
                FileInfo info = (FileInfo)this.tvwFileList.SelectedNode.Tag;
                strFilePath = info.FullName;
                node = this.tvwFileList.SelectedNode;
                isFile = true;
            }
            else if (this.tvwFileList.SelectedNode.Tag.GetType() == typeof(DirectoryInfo))
            {
                DirectoryInfo info = (DirectoryInfo)this.tvwFileList.SelectedNode.Tag;
                strFilePath = info.FullName;
                node = this.tvwFileList.SelectedNode;
                strMsg = "是否删除文件夹？";
            }
            if (tbMessageBox.Show(MainForm.GetMainForm(), strMsg, "消息", MessageBoxButtons.OKCancel) == DialogResult.OK)
            {
                try
                {
                    if (isFile)
                    {
                        File.Delete(strFilePath);
                    }
                    else
                    {
                        Directory.Delete(strFilePath, true);
                    }
                    this.mProjectObject.DeleteFile(strFilePath, isFile);
                    this.tvwFileList.Nodes.Remove(node);

                    if (this.DeleteProjectFile != null)
                    {
                        this.DeleteProjectFile(this, new DeleteProjectFileEventArgs(strFilePath));
                    }
                }
                catch (Exception ex)
                {
                    Common.LogException(ex.ToString(), "frmProject_tsmiDelete_Click");
                }
            }
        }

        private void DeleteProject()
        {
            if (this.tvwProjectList.SelectedNode == null)
            {
                tbMessageBox.Show(MainForm.GetMainForm(), "请选择要删除的项目！", "消息", MessageBoxButtons.OK);
                return;
            }

            bool blnCheck = false;
            if (tbMessageBox.Show(MainForm.GetMainForm(), "是否删除工程，并关闭打开的工程文件？", "消息", MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button2,"是否删除本地文件？",ref blnCheck) == DialogResult.OK)
            {
               tbTreeNode node = this.tvwProjectList.SelectedNode;
               this.mProjectHelper.DeleteProject((ProjectObject)node.Tag ,blnCheck);
               this.tvwProjectList.Nodes.Remove(node);
                if (this.DeleteProjectEvent != null)
                {
                    this.DeleteProjectEvent(this, new DeleteProjectEventArgs((ProjectObject)node.Tag));
                }
            }
        }

        public string PackageProject(bool blnDebug)
        {
            string strReturn = string.Empty;
            try
            {
                ProjectObject obj = this.mProjectObject;
                if (obj == null && this.tvwProjectList.SelectedNode == null)
                {
                    tbMessageBox.Show(MainForm.GetMainForm(), "请选择有交的工程！", "提示", MessageBoxButtons.OK);
                    return strReturn;
                }

                if (obj == null)
                {
                    obj = (ProjectObject)this.tvwProjectList.SelectedNode.Tag;
                    this.mProjectObject = obj;
                }
                obj.CreateXSPFile(blnDebug);
                strReturn = obj.XSPFile;
            }
            catch (Exception ex)
            {
                Common.LogException(ex.ToString(), "frmProject_PackageProject");
            }

            return strReturn;
        }

    }

  
    public class DeleteProjectEventArgs : EventArgs
    {
        private ProjectObject mProjectObj = null;
        public ProjectObject ProjectObj
        {
            get
            {
                return this.mProjectObj;

            }
        }

        public DeleteProjectEventArgs(ProjectObject pObject)
        {
            this.mProjectObj = pObject;
        }
    }

    public class DeleteProjectFileEventArgs : EventArgs
    {
        private string mFilePath = string.Empty;
        public string FilePath
        {
            get
            {
                return this.mFilePath;

            }
        }

        public DeleteProjectFileEventArgs(string pObject)
        {
            this.mFilePath = pObject;
        }
    }
}
