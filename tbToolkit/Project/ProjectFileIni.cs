﻿using iTong.CoreFoundation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;

namespace tbToolkit
{
    public class ProjectFileIni
    {
        private static int GetSize(ProjectObject obj, string strSession)
        {
            string strVer = IniClass.GetIniSectionKey(strSession, "size", obj.ProjectFile);
            int intReturn = 0;
            try
            {
                intReturn = Convert.ToInt32(strVer);
            }
            catch (Exception)
            {
            }
            return intReturn;
        }
        private static int GetSRCSize(ProjectObject obj)
        {
            
            return GetSize(obj, "SOURCE");
        }
        private static int GetUISize(ProjectObject obj)
        {

            return GetSize(obj, "UI");
        }
        private static int GetRESSize(ProjectObject obj)
        {

            return GetSize(obj, "RES");
        }
        private static int GetLIBSize(ProjectObject obj)
        {

            return GetSize(obj, "LIB");
        }

        private static List<string> GetFilePath(ProjectObject obj, string strSession, string key,int intSize)
        {
            List<string> lstReturn = new List<string>();
            for (int i = 1; i <= intSize; i++)
            {
                string strKey = string.Format("{0}\\{1}", i, key);
                string strVer = IniClass.GetIniSectionKey(strSession, strKey, obj.ProjectFile);
                lstReturn.Add(strVer);
            }

            return lstReturn;
        }
        private static List<string> GetSRCFilePath(ProjectObject obj)
        {
            int intCount = GetSRCSize(obj);
            return GetFilePath(obj, "SOURCE", "SOURCEFILE", intCount);
        }
        private static List<string> GetUIFilePath(ProjectObject obj)
        {
            int intCount = GetUISize(obj);
            return GetFilePath(obj, "UI", "UIFILE", intCount);
        }
        private static List<string> GetRESFilePath(ProjectObject obj)
        {
            int intCount = GetRESSize(obj);
            return GetFilePath(obj, "RES", "RESFILE", intCount);
        }
        private static List<string> GetLIBFilePath(ProjectObject obj)
        {
            int intCount = GetLIBSize(obj);
            return GetFilePath(obj, "LIB", "LIBFILE", intCount);
        }

        public static List<string> GetFiles(ProjectObject obj)
        {
            List<string> lstReturn = new List<string>();
            lstReturn.AddRange(GetSRCFilePath(obj));
            lstReturn.AddRange(GetUIFilePath(obj));
            lstReturn.AddRange(GetRESFilePath(obj));
            lstReturn.AddRange(GetLIBFilePath(obj));
            return lstReturn;
        }

        private static void SetFileList(ProjectObject obj, string strSession, string strKey, List<string> lstFile)
        {
            //Size
            IniClass.SetIniSectionKey(strSession, "size", lstFile.Count.ToString(), obj.ProjectFile);
            int intIndex = 0;
            foreach (string item in lstFile)
            {
                intIndex += 1;
                string strFileKey = string.Format("{0}\\{1}", intIndex, strKey);
                IniClass.SetIniSectionKey(strSession, strFileKey, item, obj.ProjectFile);
            }
        }
        private static void SetSRCFileList(ProjectObject obj, List<string> lstFile)
        {
            SetFileList(obj, "SOURCE", "SOURCEFILE", lstFile);
        }
        private static void SetUIFileList(ProjectObject obj, List<string> lstFile)
        {
            SetFileList(obj, "UI", "UIFILE", lstFile);
        }
        private static void SetRESFileList(ProjectObject obj, List<string> lstFile)
        {
            SetFileList(obj, "RES", "RESFILE", lstFile);
        }
        private static void SetLIBFileList(ProjectObject obj, List<string> lstFile)
        {
            SetFileList(obj, "LIB", "LIBFILE", lstFile);
        }

        public static void SetFilePath(ProjectObject obj)
        {
            List<string> lstSRC = new List<string>();
            List<string> lstUI = new List<string>();
            List<string> lstRES = new List<string>();
            List<string> lstLIB = new List<string>();
            foreach (object item in obj.FileList.Values)
            {
                if (item.GetType() != typeof(FileInfo))
                {
                    continue;
                }
                FileInfo info = (FileInfo)item;
                if (info.FullName.StartsWith(obj.ProjectPathSRC))
                {
                    lstSRC.Add(info.FullName);
                }
                if (info.FullName.StartsWith(obj.ProjectPathUI))
                {
                    lstUI.Add(info.FullName);
                }
                if (info.FullName.StartsWith(obj.ProjectPathRES))
                {
                    lstRES.Add(info.FullName);
                }
                if (info.FullName.StartsWith(obj.ProjectPathLIB))
                {
                    lstLIB.Add(info.FullName);
                }
            }
            SetSRCFileList(obj, lstSRC);
            SetUIFileList(obj, lstUI);
            SetRESFileList(obj, lstRES);
            SetLIBFileList(obj, lstLIB);
        }
    }
}
