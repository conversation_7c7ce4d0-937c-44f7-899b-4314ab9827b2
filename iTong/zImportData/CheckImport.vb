﻿



Public Class CheckImport

    Private Shared BackupFolder As String = String.Empty
    Private Shared Checked As Boolean = False

    ' Added by Utmost20140630
    ' 使用中间变量避免多次调用Folder.DocumentFolder属性
    Private Shared _DocFolder As String = Folder.DocumentFolder

    Shared Sub New()
        BackupFolder = _DocFolder + "Backup For 1x\"

        Folder.CheckFolder(BackupFolder)
    End Sub

    Public Shared Function CheckDB() As Boolean
        If Checked OrElse Folder.LangType = LanguageType.en_US Then
            Return False
        End If

        Dim strDB As String = String.Empty
        ''音乐-----------------------------------------
        'strDB = Folder.DocumentFolder & "tbMusic.db"
        'If f Then
        '   '---------------------------------------------

        strDB = _DocFolder + "tbMusic.db"
        If File.Exists(strDB) Then
            Return True
        End If

        strDB = _DocFolder + "tbDownload.db"
        If File.Exists(strDB) Then
            Return True
        End If

        strDB = _DocFolder + "tbLocalSoft.db"
        If File.Exists(strDB) Then
            Return True
        End If

        Checked = True
        Return False
    End Function

    Public Shared Sub Import()
        Checked = True

        Dim thd As New System.Threading.Thread(AddressOf ImportThread)
        thd.IsBackground = True
        thd.Name = "ImportThread"
        thd.Start()

        Application.DoEvents()
        Dim dt As Date = Now
        While thd.ThreadState <> Threading.ThreadState.Stopped AndAlso Now.Subtract(dt).TotalSeconds < 20
            System.Threading.Thread.Sleep(50)
            Application.DoEvents()
        End While
    End Sub

    Private Shared Sub ImportThread()
        Try
            CheckMusic()
            CheckDownload()
            CheckConfig()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ImportThread")
        End Try
    End Sub

    Private Shared Sub CheckMusic()
        Dim strDB As String = _DocFolder + "tbMusic.db"
        If Not File.Exists(strDB) Then
            Return
        End If

        Try
            Dim mgrMusic As MusicDB = MusicDB.Instance()
            Dim conn As System.Data.SQLite.SQLiteConnection = SQLiteClass.CreateConnectionFromFile(strDB)
            If conn Is Nothing Then
                Return
            End If

            Dim listMusicInfo As List(Of MusicInfo) = mgrMusic.GetAll_MusicInfo_Old(conn)
            Dim listFolder As List(Of FolderInfo) = mgrMusic.GetAll_MonitoredFolder_Old(conn)
            Dim dictFavorit As Dictionary(Of String, Boolean) = mgrMusic.Get_FavoritePath_Old(conn)

            conn.Close()

            mgrMusic.Insert_MusicInfo(listMusicInfo)
            mgrMusic.Insert_MonitoredFolder(listFolder)
            mgrMusic.Insert_FavoritePath_Old(dictFavorit)

            listMusicInfo.Clear()
            listFolder.Clear()
            dictFavorit.Clear()

            File.Move(strDB, BackupFolder & String.Format("tbMusic_{0}.db", Now.ToString("yyyyMMdd_HHmmss")))

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckMusic")
        End Try

    End Sub

    Private Shared Sub CheckDownload()
        Dim strDB As String = _DocFolder + "tbDownload.db"
        If Not File.Exists(strDB) Then
            Return
        End If

        Try
            Dim mgr As MultiThreadDownloadDB = MultiThreadDownloadDB.GetInstance()

#If NET40 Then
            Dim conn As System.Data.SQLite3.SQLiteConnection = SQLiteClass3.CreateConnectionFromFile(strDB)
            If conn Is Nothing Then
                Return
            End If
#Else
            Dim conn As System.Data.SQLite.SQLiteConnection = SQLiteClass.CreateConnectionFromFile(strDB)
            If conn Is Nothing Then
                Return
            End If
#End If

            Dim list As List(Of MultiThreadDownloadItemInfo) = mgr.GetAll_Task_Old(conn)
            conn.Close()

            mgr.Insert_Task(list)
            list.Clear()

            File.Move(strDB, BackupFolder & String.Format("tbDownload_{0}.db", Now.ToString("yyyyMMdd_HHmmss")))

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckDownload")
        End Try

    End Sub

    Private Shared Sub CheckConfig()
        MoveFile(Folder.DocumentFolder & "Regular.ini", BackupFolder & String.Format("Regular_{0}.ini", Now.ToString("yyyyMMdd_HHmmss")))
        MoveFile(Folder.DocumentFolder & "tbLocalSoft.db", BackupFolder & String.Format("tbLocalSoft_{0}.db", Now.ToString("yyyyMMdd_HHmmss")))

        Dim striPhone As String = Folder.WallPaperFolder & "iPhone"
        Dim striPhone4 As String = Folder.WallPaperFolder & "iPhone4"
        If Directory.Exists(striPhone4) Then
            For Each strFile As String In Directory.GetFiles(striPhone4)
                MoveFile(strFile, Path.Combine(striPhone, Path.GetFileName(strFile)))
            Next
            Directory.Delete(striPhone4, True)
        End If
    End Sub

    Private Shared Sub MoveFile(ByVal strSrc As String, ByVal strDes As String)
        Try
            If File.Exists(strSrc) Then
                File.Move(strSrc, strDes)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

End Class
