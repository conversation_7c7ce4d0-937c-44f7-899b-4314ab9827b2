﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSetting
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSetting))
        Me.btn_close = New tbButton
        Me.btniTunesFolder = New tbButton
        Me.lbliTunesFolder = New System.Windows.Forms.Label
        Me.btnDownloadFolder = New tbButton
        Me.lblDownloadFolder = New System.Windows.Forms.Label
        Me.btnCancel = New tbButton
        Me.btnOK = New tbButton
        Me.txtDownloadFolder = New tbTextBox
        Me.txtiTunesFolder = New tbTextBox
        Me.rdbExist = New tbRadioButton
        Me.rdbMini = New tbRadioButton
        Me.chkLinkIPAPackage = New tbCheckBox
        Me.chkRemember = New tbCheckBox
        Me.txtMusicFolder = New tbTextBox
        Me.btnMusicFolder = New tbButton
        Me.lblMusicFolder = New System.Windows.Forms.Label
        Me.txtRingtoneFolder = New tbTextBox
        Me.btnRingtoneFolder = New tbButton
        Me.lblRingtoneFolder = New System.Windows.Forms.Label
        Me.txtWallpaperFolder = New tbTextBox
        Me.btnWallpaperFolder = New tbButton
        Me.lblWallpaperFolder = New System.Windows.Forms.Label
        Me.lblApp = New System.Windows.Forms.Label
        Me.txtOtherFolder = New tbTextBox
        Me.btnOtherFolder = New tbButton
        Me.lblOtherFolder = New System.Windows.Forms.Label
        Me.btnSplitCommon = New tbButton
        Me.lblCommon = New System.Windows.Forms.Label
        Me.lblLanguage = New System.Windows.Forms.Label
        Me.cbxLanguage = New System.Windows.Forms.ComboBox
        Me.lblFolder = New System.Windows.Forms.Label
        Me.TbButton1 = New tbButton
        Me.chkAutoInstallAfterDownload = New tbCheckBox
        Me.chkAutoShowMsgAfterDownload = New tbCheckBox
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(501, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 8
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = My.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btniTunesFolder
        '
        Me.btniTunesFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btniTunesFolder.BackColor = System.Drawing.Color.Transparent
        Me.btniTunesFolder.BindingForm = Nothing
        Me.btniTunesFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btniTunesFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btniTunesFolder.Location = New System.Drawing.Point(438, 285)
        Me.btniTunesFolder.Name = "btniTunesFolder"
        Me.btniTunesFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btniTunesFolder.Selectable = True
        Me.btniTunesFolder.Size = New System.Drawing.Size(65, 23)
        Me.btniTunesFolder.TabIndex = 29
        Me.btniTunesFolder.tbAdriftIconWhenHover = False
        Me.btniTunesFolder.tbAutoSize = False
        Me.btniTunesFolder.tbAutoSizeEx = False
        Me.btniTunesFolder.tbBackgroundImage = My.Resources.btn_4_white
        Me.btniTunesFolder.tbBackgroundImageState = ImageState.FourState
        Me.btniTunesFolder.tbBadgeNumber = 0
        Me.btniTunesFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniTunesFolder.tbEndEllipsis = False
        Me.btniTunesFolder.tbIconHoldPlace = True
        Me.btniTunesFolder.tbIconImage = Nothing
        Me.btniTunesFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniTunesFolder.tbIconImageState = ImageState.OneState
        Me.btniTunesFolder.tbIconMore = False
        Me.btniTunesFolder.tbIconMouseDown = Nothing
        Me.btniTunesFolder.tbIconMouseHover = Nothing
        Me.btniTunesFolder.tbIconMouseLeave = Nothing
        Me.btniTunesFolder.tbIconPlaceText = 2
        Me.btniTunesFolder.tbIconReadOnly = Nothing
        Me.btniTunesFolder.tbImageMouseDown = Nothing
        Me.btniTunesFolder.tbImageMouseHover = Nothing
        Me.btniTunesFolder.tbImageMouseLeave = Nothing
        Me.btniTunesFolder.tbReadOnly = False
        Me.btniTunesFolder.tbReadOnlyText = False
        Me.btniTunesFolder.tbShadow = False
        Me.btniTunesFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btniTunesFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btniTunesFolder.tbShowNew = False
        Me.btniTunesFolder.tbShowToolTipOnButton = False
        Me.btniTunesFolder.tbSplit = "13,11,13,11"
        Me.btniTunesFolder.tbText = "更换"
        Me.btniTunesFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniTunesFolder.tbTextColor = System.Drawing.Color.Black
        Me.btniTunesFolder.tbTextColorDisable = System.Drawing.Color.Black
        Me.btniTunesFolder.tbTextColorDown = System.Drawing.Color.Black
        Me.btniTunesFolder.tbTextColorHover = System.Drawing.Color.Black
        Me.btniTunesFolder.tbTextMouseDownPlace = 0
        Me.btniTunesFolder.tbToolTip = ""
        Me.btniTunesFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniTunesFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniTunesFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniTunesFolder.VisibleEx = True
        '
        'lbliTunesFolder
        '
        Me.lbliTunesFolder.AutoSize = True
        Me.lbliTunesFolder.BackColor = System.Drawing.Color.Transparent
        Me.lbliTunesFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbliTunesFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lbliTunesFolder.Location = New System.Drawing.Point(43, 267)
        Me.lbliTunesFolder.Name = "lbliTunesFolder"
        Me.lbliTunesFolder.Size = New System.Drawing.Size(83, 12)
        Me.lbliTunesFolder.TabIndex = 28
        Me.lbliTunesFolder.Text = "iTunes Folder"
        '
        'btnDownloadFolder
        '
        Me.btnDownloadFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDownloadFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnDownloadFolder.BindingForm = Nothing
        Me.btnDownloadFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDownloadFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnDownloadFolder.Location = New System.Drawing.Point(438, 392)
        Me.btnDownloadFolder.Name = "btnDownloadFolder"
        Me.btnDownloadFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnDownloadFolder.Selectable = True
        Me.btnDownloadFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnDownloadFolder.TabIndex = 32
        Me.btnDownloadFolder.tbAdriftIconWhenHover = False
        Me.btnDownloadFolder.tbAutoSize = False
        Me.btnDownloadFolder.tbAutoSizeEx = False
        Me.btnDownloadFolder.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnDownloadFolder.tbBackgroundImageState = ImageState.FourState
        Me.btnDownloadFolder.tbBadgeNumber = 0
        Me.btnDownloadFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDownloadFolder.tbEndEllipsis = False
        Me.btnDownloadFolder.tbIconHoldPlace = True
        Me.btnDownloadFolder.tbIconImage = Nothing
        Me.btnDownloadFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.tbIconImageState = ImageState.OneState
        Me.btnDownloadFolder.tbIconMore = False
        Me.btnDownloadFolder.tbIconMouseDown = Nothing
        Me.btnDownloadFolder.tbIconMouseHover = Nothing
        Me.btnDownloadFolder.tbIconMouseLeave = Nothing
        Me.btnDownloadFolder.tbIconPlaceText = 2
        Me.btnDownloadFolder.tbIconReadOnly = Nothing
        Me.btnDownloadFolder.tbImageMouseDown = Nothing
        Me.btnDownloadFolder.tbImageMouseHover = Nothing
        Me.btnDownloadFolder.tbImageMouseLeave = Nothing
        Me.btnDownloadFolder.tbReadOnly = False
        Me.btnDownloadFolder.tbReadOnlyText = False
        Me.btnDownloadFolder.tbShadow = False
        Me.btnDownloadFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDownloadFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDownloadFolder.tbShowNew = False
        Me.btnDownloadFolder.tbShowToolTipOnButton = False
        Me.btnDownloadFolder.tbSplit = "13,11,13,11"
        Me.btnDownloadFolder.tbText = "更换"
        Me.btnDownloadFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.tbTextColor = System.Drawing.Color.Black
        Me.btnDownloadFolder.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnDownloadFolder.tbTextColorDown = System.Drawing.Color.Black
        Me.btnDownloadFolder.tbTextColorHover = System.Drawing.Color.Black
        Me.btnDownloadFolder.tbTextMouseDownPlace = 0
        Me.btnDownloadFolder.tbToolTip = ""
        Me.btnDownloadFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDownloadFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDownloadFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.VisibleEx = True
        '
        'lblDownloadFolder
        '
        Me.lblDownloadFolder.AutoSize = True
        Me.lblDownloadFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblDownloadFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDownloadFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblDownloadFolder.Location = New System.Drawing.Point(43, 374)
        Me.lblDownloadFolder.Name = "lblDownloadFolder"
        Me.lblDownloadFolder.Size = New System.Drawing.Size(95, 12)
        Me.lblDownloadFolder.TabIndex = 31
        Me.lblDownloadFolder.Text = "Download Folder"
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.Location = New System.Drawing.Point(438, 540)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(65, 23)
        Me.btnCancel.TabIndex = 40
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.Black
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(365, 540)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(65, 23)
        Me.btnOK.TabIndex = 39
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "保存"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'txtDownloadFolder
        '
        Me.txtDownloadFolder.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtDownloadFolder.BackColor = System.Drawing.Color.White
        Me.txtDownloadFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtDownloadFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtDownloadFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtDownloadFolder.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtDownloadFolder.ForeColor = System.Drawing.Color.Black
        Me.txtDownloadFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDownloadFolder.Location = New System.Drawing.Point(97, 393)
        Me.txtDownloadFolder.MaxLength = 256
        Me.txtDownloadFolder.Name = "txtDownloadFolder"
        Me.txtDownloadFolder.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtDownloadFolder.Size = New System.Drawing.Size(333, 21)
        Me.txtDownloadFolder.TabIndex = 44
        Me.txtDownloadFolder.Tag = Nothing
        Me.txtDownloadFolder.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtDownloadFolder.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtDownloadFolder.tbSelMark = True
        Me.txtDownloadFolder.tbTextBind = ""
        Me.txtDownloadFolder.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtDownloadFolder.TextFormat = tbTextFormat.Normal
        Me.txtDownloadFolder.TextTip = ""
        '
        'txtiTunesFolder
        '
        Me.txtiTunesFolder.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtiTunesFolder.BackColor = System.Drawing.Color.White
        Me.txtiTunesFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtiTunesFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtiTunesFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtiTunesFolder.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtiTunesFolder.ForeColor = System.Drawing.Color.Black
        Me.txtiTunesFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtiTunesFolder.Location = New System.Drawing.Point(43, 286)
        Me.txtiTunesFolder.MaxLength = 256
        Me.txtiTunesFolder.Name = "txtiTunesFolder"
        Me.txtiTunesFolder.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtiTunesFolder.Size = New System.Drawing.Size(387, 21)
        Me.txtiTunesFolder.TabIndex = 45
        Me.txtiTunesFolder.Tag = Nothing
        Me.txtiTunesFolder.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtiTunesFolder.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtiTunesFolder.tbSelMark = True
        Me.txtiTunesFolder.tbTextBind = ""
        Me.txtiTunesFolder.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtiTunesFolder.TextFormat = tbTextFormat.Normal
        Me.txtiTunesFolder.TextTip = ""
        '
        'rdbExist
        '
        Me.rdbExist.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.rdbExist.Appearance = System.Windows.Forms.Appearance.Button
        Me.rdbExist.BackColor = System.Drawing.Color.Transparent
        Me.rdbExist.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbExist.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbExist.Location = New System.Drawing.Point(314, 209)
        Me.rdbExist.Name = "rdbExist"
        Me.rdbExist.Size = New System.Drawing.Size(151, 22)
        Me.rdbExist.TabIndex = 47
        Me.rdbExist.TabStop = True
        Me.rdbExist.tbAdriftIconWhenHover = False
        Me.rdbExist.tbAutoSize = False
        Me.rdbExist.tbAutoSizeEx = False
        Me.rdbExist.tbBadgeNumber = 0
        Me.rdbExist.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.rdbExist.tbIconCheckedMouseDown = My.Resources.btn_checked
        Me.rdbExist.tbIconCheckedMouseHover = My.Resources.btn_checked
        Me.rdbExist.tbIconCheckedMouseLeave = My.Resources.btn_checked
        Me.rdbExist.tbIconHoldPlace = True
        Me.rdbExist.tbIconImage = Nothing
        Me.rdbExist.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.rdbExist.tbIconImageState = ImageState.OneState
        Me.rdbExist.tbIconMore = False
        Me.rdbExist.tbIconMoreImage = CType(resources.GetObject("rdbExist.tbIconMoreImage"), System.Drawing.Image)
        Me.rdbExist.tbIconPlaceText = 5
        Me.rdbExist.tbIconReadOnly = Nothing
        Me.rdbExist.tbIconUnCheckedMouseDown = My.Resources.btn_unchecked
        Me.rdbExist.tbIconUnCheckedMouseHover = My.Resources.btn_unchecked
        Me.rdbExist.tbIconUnCheckedMouseLeave = My.Resources.btn_unchecked
        Me.rdbExist.tbImageBackground = Nothing
        Me.rdbExist.tbImageBackgroundState = ImageState.TwoState
        Me.rdbExist.tbImageCheckedMouseDown = Nothing
        Me.rdbExist.tbImageCheckedMouseHover = Nothing
        Me.rdbExist.tbImageCheckedMouseLeave = Nothing
        Me.rdbExist.tbImageUnCheckedMouseDown = Nothing
        Me.rdbExist.tbImageUnCheckedMouseHover = Nothing
        Me.rdbExist.tbImageUnCheckedMouseLeave = Nothing
        Me.rdbExist.tbReadOnly = False
        Me.rdbExist.tbShadow = False
        Me.rdbExist.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.rdbExist.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.rdbExist.tbShowNew = False
        Me.rdbExist.tbSplit = "3,3,3,3"
        Me.rdbExist.tbTag = Nothing
        Me.rdbExist.tbToolTip = ""
        Me.rdbExist.Text = "退出程序"
        Me.rdbExist.UseVisualStyleBackColor = False
        '
        'rdbMini
        '
        Me.rdbMini.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.rdbMini.Appearance = System.Windows.Forms.Appearance.Button
        Me.rdbMini.BackColor = System.Drawing.Color.Transparent
        Me.rdbMini.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbMini.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbMini.Location = New System.Drawing.Point(75, 209)
        Me.rdbMini.Name = "rdbMini"
        Me.rdbMini.Size = New System.Drawing.Size(151, 22)
        Me.rdbMini.TabIndex = 48
        Me.rdbMini.TabStop = True
        Me.rdbMini.tbAdriftIconWhenHover = False
        Me.rdbMini.tbAutoSize = False
        Me.rdbMini.tbAutoSizeEx = False
        Me.rdbMini.tbBadgeNumber = 0
        Me.rdbMini.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.rdbMini.tbIconCheckedMouseDown = My.Resources.btn_checked
        Me.rdbMini.tbIconCheckedMouseHover = My.Resources.btn_checked
        Me.rdbMini.tbIconCheckedMouseLeave = My.Resources.btn_checked
        Me.rdbMini.tbIconHoldPlace = True
        Me.rdbMini.tbIconImage = Nothing
        Me.rdbMini.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.rdbMini.tbIconImageState = ImageState.OneState
        Me.rdbMini.tbIconMore = False
        Me.rdbMini.tbIconMoreImage = CType(resources.GetObject("rdbMini.tbIconMoreImage"), System.Drawing.Image)
        Me.rdbMini.tbIconPlaceText = 5
        Me.rdbMini.tbIconReadOnly = Nothing
        Me.rdbMini.tbIconUnCheckedMouseDown = My.Resources.btn_unchecked
        Me.rdbMini.tbIconUnCheckedMouseHover = My.Resources.btn_unchecked
        Me.rdbMini.tbIconUnCheckedMouseLeave = My.Resources.btn_unchecked
        Me.rdbMini.tbImageBackground = Nothing
        Me.rdbMini.tbImageBackgroundState = ImageState.TwoState
        Me.rdbMini.tbImageCheckedMouseDown = Nothing
        Me.rdbMini.tbImageCheckedMouseHover = Nothing
        Me.rdbMini.tbImageCheckedMouseLeave = Nothing
        Me.rdbMini.tbImageUnCheckedMouseDown = Nothing
        Me.rdbMini.tbImageUnCheckedMouseHover = Nothing
        Me.rdbMini.tbImageUnCheckedMouseLeave = Nothing
        Me.rdbMini.tbReadOnly = False
        Me.rdbMini.tbShadow = False
        Me.rdbMini.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.rdbMini.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.rdbMini.tbShowNew = False
        Me.rdbMini.tbSplit = "3,3,3,3"
        Me.rdbMini.tbTag = Nothing
        Me.rdbMini.tbToolTip = ""
        Me.rdbMini.Text = "最小化到托盘"
        Me.rdbMini.UseVisualStyleBackColor = False
        '
        'chkLinkIPAPackage
        '
        Me.chkLinkIPAPackage.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.chkLinkIPAPackage.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkLinkIPAPackage.BackColor = System.Drawing.Color.Transparent
        Me.chkLinkIPAPackage.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkLinkIPAPackage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkLinkIPAPackage.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkLinkIPAPackage.Location = New System.Drawing.Point(43, 103)
        Me.chkLinkIPAPackage.Name = "chkLinkIPAPackage"
        Me.chkLinkIPAPackage.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkLinkIPAPackage.Size = New System.Drawing.Size(158, 18)
        Me.chkLinkIPAPackage.TabIndex = 49
        Me.chkLinkIPAPackage.tbAdriftIconWhenHover = False
        Me.chkLinkIPAPackage.tbAutoSize = False
        Me.chkLinkIPAPackage.tbAutoSizeEx = True
        Me.chkLinkIPAPackage.tbIconChecked = CType(resources.GetObject("chkLinkIPAPackage.tbIconChecked"), System.Drawing.Image)
        Me.chkLinkIPAPackage.tbIconCheckedMouseDown = Nothing
        Me.chkLinkIPAPackage.tbIconCheckedMouseHover = Nothing
        Me.chkLinkIPAPackage.tbIconCheckedMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbIconCheckedState = ImageState.FourState
        Me.chkLinkIPAPackage.tbIconHoldPlace = True
        Me.chkLinkIPAPackage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkLinkIPAPackage.tbIconIndeterminate = CType(resources.GetObject("chkLinkIPAPackage.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkLinkIPAPackage.tbIconIndeterminateMouseDown = Nothing
        Me.chkLinkIPAPackage.tbIconIndeterminateMouseHover = Nothing
        Me.chkLinkIPAPackage.tbIconIndeterminateMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbIconIndeterminateState = ImageState.FourState
        Me.chkLinkIPAPackage.tbIconPlaceText = 1
        Me.chkLinkIPAPackage.tbIconUnChecked = CType(resources.GetObject("chkLinkIPAPackage.tbIconUnChecked"), System.Drawing.Image)
        Me.chkLinkIPAPackage.tbIconUnCheckedMouseDown = Nothing
        Me.chkLinkIPAPackage.tbIconUnCheckedMouseHover = Nothing
        Me.chkLinkIPAPackage.tbIconUnCheckedMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbIconUnCheckedState = ImageState.FourState
        Me.chkLinkIPAPackage.tbImageBackground = Nothing
        Me.chkLinkIPAPackage.tbImageBackgroundState = ImageState.TwoState
        Me.chkLinkIPAPackage.tbImageCheckedMouseDown = Nothing
        Me.chkLinkIPAPackage.tbImageCheckedMouseHover = Nothing
        Me.chkLinkIPAPackage.tbImageCheckedMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbImageUnCheckedMouseDown = Nothing
        Me.chkLinkIPAPackage.tbImageUnCheckedMouseHover = Nothing
        Me.chkLinkIPAPackage.tbImageUnCheckedMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbReadOnly = False
        Me.chkLinkIPAPackage.tbShadow = False
        Me.chkLinkIPAPackage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkLinkIPAPackage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkLinkIPAPackage.tbSplit = "3,3,3,3"
        Me.chkLinkIPAPackage.tbToolTip = ""
        Me.chkLinkIPAPackage.Text = "设为 ipa 默认打开方式"
        Me.chkLinkIPAPackage.UseVisualStyleBackColor = False
        '
        'chkRemember
        '
        Me.chkRemember.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.chkRemember.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkRemember.BackColor = System.Drawing.Color.Transparent
        Me.chkRemember.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkRemember.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkRemember.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkRemember.Location = New System.Drawing.Point(43, 184)
        Me.chkRemember.Name = "chkRemember"
        Me.chkRemember.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkRemember.Size = New System.Drawing.Size(116, 18)
        Me.chkRemember.TabIndex = 50
        Me.chkRemember.tbAdriftIconWhenHover = False
        Me.chkRemember.tbAutoSize = False
        Me.chkRemember.tbAutoSizeEx = True
        Me.chkRemember.tbIconChecked = CType(resources.GetObject("chkRemember.tbIconChecked"), System.Drawing.Image)
        Me.chkRemember.tbIconCheckedMouseDown = Nothing
        Me.chkRemember.tbIconCheckedMouseHover = Nothing
        Me.chkRemember.tbIconCheckedMouseLeave = Nothing
        Me.chkRemember.tbIconCheckedState = ImageState.FourState
        Me.chkRemember.tbIconHoldPlace = True
        Me.chkRemember.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkRemember.tbIconIndeterminate = CType(resources.GetObject("chkRemember.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkRemember.tbIconIndeterminateMouseDown = Nothing
        Me.chkRemember.tbIconIndeterminateMouseHover = Nothing
        Me.chkRemember.tbIconIndeterminateMouseLeave = Nothing
        Me.chkRemember.tbIconIndeterminateState = ImageState.FourState
        Me.chkRemember.tbIconPlaceText = 1
        Me.chkRemember.tbIconUnChecked = CType(resources.GetObject("chkRemember.tbIconUnChecked"), System.Drawing.Image)
        Me.chkRemember.tbIconUnCheckedMouseDown = Nothing
        Me.chkRemember.tbIconUnCheckedMouseHover = Nothing
        Me.chkRemember.tbIconUnCheckedMouseLeave = Nothing
        Me.chkRemember.tbIconUnCheckedState = ImageState.FourState
        Me.chkRemember.tbImageBackground = Nothing
        Me.chkRemember.tbImageBackgroundState = ImageState.TwoState
        Me.chkRemember.tbImageCheckedMouseDown = Nothing
        Me.chkRemember.tbImageCheckedMouseHover = Nothing
        Me.chkRemember.tbImageCheckedMouseLeave = Nothing
        Me.chkRemember.tbImageUnCheckedMouseDown = Nothing
        Me.chkRemember.tbImageUnCheckedMouseHover = Nothing
        Me.chkRemember.tbImageUnCheckedMouseLeave = Nothing
        Me.chkRemember.tbReadOnly = False
        Me.chkRemember.tbShadow = False
        Me.chkRemember.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkRemember.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkRemember.tbSplit = "3,3,3,3"
        Me.chkRemember.tbToolTip = ""
        Me.chkRemember.Text = "退出时不再提示"
        Me.chkRemember.UseVisualStyleBackColor = False
        '
        'txtMusicFolder
        '
        Me.txtMusicFolder.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtMusicFolder.BackColor = System.Drawing.Color.White
        Me.txtMusicFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtMusicFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtMusicFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtMusicFolder.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtMusicFolder.ForeColor = System.Drawing.Color.Black
        Me.txtMusicFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtMusicFolder.Location = New System.Drawing.Point(97, 427)
        Me.txtMusicFolder.MaxLength = 256
        Me.txtMusicFolder.Name = "txtMusicFolder"
        Me.txtMusicFolder.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtMusicFolder.Size = New System.Drawing.Size(333, 21)
        Me.txtMusicFolder.TabIndex = 53
        Me.txtMusicFolder.Tag = Nothing
        Me.txtMusicFolder.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtMusicFolder.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtMusicFolder.tbSelMark = True
        Me.txtMusicFolder.tbTextBind = ""
        Me.txtMusicFolder.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtMusicFolder.TextFormat = tbTextFormat.Normal
        Me.txtMusicFolder.TextTip = ""
        '
        'btnMusicFolder
        '
        Me.btnMusicFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnMusicFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnMusicFolder.BindingForm = Nothing
        Me.btnMusicFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnMusicFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnMusicFolder.Location = New System.Drawing.Point(438, 426)
        Me.btnMusicFolder.Name = "btnMusicFolder"
        Me.btnMusicFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnMusicFolder.Selectable = True
        Me.btnMusicFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnMusicFolder.TabIndex = 52
        Me.btnMusicFolder.tbAdriftIconWhenHover = False
        Me.btnMusicFolder.tbAutoSize = False
        Me.btnMusicFolder.tbAutoSizeEx = False
        Me.btnMusicFolder.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnMusicFolder.tbBackgroundImageState = ImageState.FourState
        Me.btnMusicFolder.tbBadgeNumber = 0
        Me.btnMusicFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnMusicFolder.tbEndEllipsis = False
        Me.btnMusicFolder.tbIconHoldPlace = True
        Me.btnMusicFolder.tbIconImage = Nothing
        Me.btnMusicFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMusicFolder.tbIconImageState = ImageState.OneState
        Me.btnMusicFolder.tbIconMore = False
        Me.btnMusicFolder.tbIconMouseDown = Nothing
        Me.btnMusicFolder.tbIconMouseHover = Nothing
        Me.btnMusicFolder.tbIconMouseLeave = Nothing
        Me.btnMusicFolder.tbIconPlaceText = 2
        Me.btnMusicFolder.tbIconReadOnly = Nothing
        Me.btnMusicFolder.tbImageMouseDown = Nothing
        Me.btnMusicFolder.tbImageMouseHover = Nothing
        Me.btnMusicFolder.tbImageMouseLeave = Nothing
        Me.btnMusicFolder.tbReadOnly = False
        Me.btnMusicFolder.tbReadOnlyText = False
        Me.btnMusicFolder.tbShadow = False
        Me.btnMusicFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnMusicFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnMusicFolder.tbShowNew = False
        Me.btnMusicFolder.tbShowToolTipOnButton = False
        Me.btnMusicFolder.tbSplit = "13,11,13,11"
        Me.btnMusicFolder.tbText = "更换"
        Me.btnMusicFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMusicFolder.tbTextColor = System.Drawing.Color.Black
        Me.btnMusicFolder.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnMusicFolder.tbTextColorDown = System.Drawing.Color.Black
        Me.btnMusicFolder.tbTextColorHover = System.Drawing.Color.Black
        Me.btnMusicFolder.tbTextMouseDownPlace = 0
        Me.btnMusicFolder.tbToolTip = ""
        Me.btnMusicFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnMusicFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnMusicFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMusicFolder.VisibleEx = True
        '
        'lblMusicFolder
        '
        Me.lblMusicFolder.AutoSize = True
        Me.lblMusicFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblMusicFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblMusicFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblMusicFolder.Location = New System.Drawing.Point(43, 431)
        Me.lblMusicFolder.Name = "lblMusicFolder"
        Me.lblMusicFolder.Size = New System.Drawing.Size(29, 12)
        Me.lblMusicFolder.TabIndex = 51
        Me.lblMusicFolder.Text = "音乐"
        '
        'txtRingtoneFolder
        '
        Me.txtRingtoneFolder.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtRingtoneFolder.BackColor = System.Drawing.Color.White
        Me.txtRingtoneFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtRingtoneFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtRingtoneFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtRingtoneFolder.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtRingtoneFolder.ForeColor = System.Drawing.Color.Black
        Me.txtRingtoneFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtRingtoneFolder.Location = New System.Drawing.Point(97, 461)
        Me.txtRingtoneFolder.MaxLength = 256
        Me.txtRingtoneFolder.Name = "txtRingtoneFolder"
        Me.txtRingtoneFolder.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtRingtoneFolder.Size = New System.Drawing.Size(333, 21)
        Me.txtRingtoneFolder.TabIndex = 56
        Me.txtRingtoneFolder.Tag = Nothing
        Me.txtRingtoneFolder.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtRingtoneFolder.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtRingtoneFolder.tbSelMark = True
        Me.txtRingtoneFolder.tbTextBind = ""
        Me.txtRingtoneFolder.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtRingtoneFolder.TextFormat = tbTextFormat.Normal
        Me.txtRingtoneFolder.TextTip = ""
        '
        'btnRingtoneFolder
        '
        Me.btnRingtoneFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRingtoneFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnRingtoneFolder.BindingForm = Nothing
        Me.btnRingtoneFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRingtoneFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRingtoneFolder.Location = New System.Drawing.Point(438, 460)
        Me.btnRingtoneFolder.Name = "btnRingtoneFolder"
        Me.btnRingtoneFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnRingtoneFolder.Selectable = True
        Me.btnRingtoneFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnRingtoneFolder.TabIndex = 55
        Me.btnRingtoneFolder.tbAdriftIconWhenHover = False
        Me.btnRingtoneFolder.tbAutoSize = False
        Me.btnRingtoneFolder.tbAutoSizeEx = False
        Me.btnRingtoneFolder.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnRingtoneFolder.tbBackgroundImageState = ImageState.FourState
        Me.btnRingtoneFolder.tbBadgeNumber = 0
        Me.btnRingtoneFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRingtoneFolder.tbEndEllipsis = False
        Me.btnRingtoneFolder.tbIconHoldPlace = True
        Me.btnRingtoneFolder.tbIconImage = Nothing
        Me.btnRingtoneFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRingtoneFolder.tbIconImageState = ImageState.OneState
        Me.btnRingtoneFolder.tbIconMore = False
        Me.btnRingtoneFolder.tbIconMouseDown = Nothing
        Me.btnRingtoneFolder.tbIconMouseHover = Nothing
        Me.btnRingtoneFolder.tbIconMouseLeave = Nothing
        Me.btnRingtoneFolder.tbIconPlaceText = 2
        Me.btnRingtoneFolder.tbIconReadOnly = Nothing
        Me.btnRingtoneFolder.tbImageMouseDown = Nothing
        Me.btnRingtoneFolder.tbImageMouseHover = Nothing
        Me.btnRingtoneFolder.tbImageMouseLeave = Nothing
        Me.btnRingtoneFolder.tbReadOnly = False
        Me.btnRingtoneFolder.tbReadOnlyText = False
        Me.btnRingtoneFolder.tbShadow = False
        Me.btnRingtoneFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRingtoneFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRingtoneFolder.tbShowNew = False
        Me.btnRingtoneFolder.tbShowToolTipOnButton = False
        Me.btnRingtoneFolder.tbSplit = "13,11,13,11"
        Me.btnRingtoneFolder.tbText = "更换"
        Me.btnRingtoneFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRingtoneFolder.tbTextColor = System.Drawing.Color.Black
        Me.btnRingtoneFolder.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnRingtoneFolder.tbTextColorDown = System.Drawing.Color.Black
        Me.btnRingtoneFolder.tbTextColorHover = System.Drawing.Color.Black
        Me.btnRingtoneFolder.tbTextMouseDownPlace = 0
        Me.btnRingtoneFolder.tbToolTip = ""
        Me.btnRingtoneFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRingtoneFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRingtoneFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRingtoneFolder.VisibleEx = True
        '
        'lblRingtoneFolder
        '
        Me.lblRingtoneFolder.AutoSize = True
        Me.lblRingtoneFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblRingtoneFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblRingtoneFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblRingtoneFolder.Location = New System.Drawing.Point(43, 465)
        Me.lblRingtoneFolder.Name = "lblRingtoneFolder"
        Me.lblRingtoneFolder.Size = New System.Drawing.Size(29, 12)
        Me.lblRingtoneFolder.TabIndex = 54
        Me.lblRingtoneFolder.Text = "铃声"
        '
        'txtWallpaperFolder
        '
        Me.txtWallpaperFolder.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtWallpaperFolder.BackColor = System.Drawing.Color.White
        Me.txtWallpaperFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtWallpaperFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtWallpaperFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtWallpaperFolder.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtWallpaperFolder.ForeColor = System.Drawing.Color.Black
        Me.txtWallpaperFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtWallpaperFolder.Location = New System.Drawing.Point(97, 495)
        Me.txtWallpaperFolder.MaxLength = 256
        Me.txtWallpaperFolder.Name = "txtWallpaperFolder"
        Me.txtWallpaperFolder.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtWallpaperFolder.Size = New System.Drawing.Size(333, 21)
        Me.txtWallpaperFolder.TabIndex = 59
        Me.txtWallpaperFolder.Tag = Nothing
        Me.txtWallpaperFolder.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtWallpaperFolder.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtWallpaperFolder.tbSelMark = True
        Me.txtWallpaperFolder.tbTextBind = ""
        Me.txtWallpaperFolder.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtWallpaperFolder.TextFormat = tbTextFormat.Normal
        Me.txtWallpaperFolder.TextTip = ""
        '
        'btnWallpaperFolder
        '
        Me.btnWallpaperFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnWallpaperFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnWallpaperFolder.BindingForm = Nothing
        Me.btnWallpaperFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnWallpaperFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnWallpaperFolder.Location = New System.Drawing.Point(438, 494)
        Me.btnWallpaperFolder.Name = "btnWallpaperFolder"
        Me.btnWallpaperFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnWallpaperFolder.Selectable = True
        Me.btnWallpaperFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnWallpaperFolder.TabIndex = 58
        Me.btnWallpaperFolder.tbAdriftIconWhenHover = False
        Me.btnWallpaperFolder.tbAutoSize = False
        Me.btnWallpaperFolder.tbAutoSizeEx = False
        Me.btnWallpaperFolder.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnWallpaperFolder.tbBackgroundImageState = ImageState.FourState
        Me.btnWallpaperFolder.tbBadgeNumber = 0
        Me.btnWallpaperFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnWallpaperFolder.tbEndEllipsis = False
        Me.btnWallpaperFolder.tbIconHoldPlace = True
        Me.btnWallpaperFolder.tbIconImage = Nothing
        Me.btnWallpaperFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWallpaperFolder.tbIconImageState = ImageState.OneState
        Me.btnWallpaperFolder.tbIconMore = False
        Me.btnWallpaperFolder.tbIconMouseDown = Nothing
        Me.btnWallpaperFolder.tbIconMouseHover = Nothing
        Me.btnWallpaperFolder.tbIconMouseLeave = Nothing
        Me.btnWallpaperFolder.tbIconPlaceText = 2
        Me.btnWallpaperFolder.tbIconReadOnly = Nothing
        Me.btnWallpaperFolder.tbImageMouseDown = Nothing
        Me.btnWallpaperFolder.tbImageMouseHover = Nothing
        Me.btnWallpaperFolder.tbImageMouseLeave = Nothing
        Me.btnWallpaperFolder.tbReadOnly = False
        Me.btnWallpaperFolder.tbReadOnlyText = False
        Me.btnWallpaperFolder.tbShadow = False
        Me.btnWallpaperFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnWallpaperFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnWallpaperFolder.tbShowNew = False
        Me.btnWallpaperFolder.tbShowToolTipOnButton = False
        Me.btnWallpaperFolder.tbSplit = "13,11,13,11"
        Me.btnWallpaperFolder.tbText = "更换"
        Me.btnWallpaperFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWallpaperFolder.tbTextColor = System.Drawing.Color.Black
        Me.btnWallpaperFolder.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnWallpaperFolder.tbTextColorDown = System.Drawing.Color.Black
        Me.btnWallpaperFolder.tbTextColorHover = System.Drawing.Color.Black
        Me.btnWallpaperFolder.tbTextMouseDownPlace = 0
        Me.btnWallpaperFolder.tbToolTip = ""
        Me.btnWallpaperFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnWallpaperFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWallpaperFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWallpaperFolder.VisibleEx = True
        '
        'lblWallpaperFolder
        '
        Me.lblWallpaperFolder.AutoSize = True
        Me.lblWallpaperFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblWallpaperFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWallpaperFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblWallpaperFolder.Location = New System.Drawing.Point(43, 499)
        Me.lblWallpaperFolder.Name = "lblWallpaperFolder"
        Me.lblWallpaperFolder.Size = New System.Drawing.Size(29, 12)
        Me.lblWallpaperFolder.TabIndex = 57
        Me.lblWallpaperFolder.Text = "壁纸"
        '
        'lblApp
        '
        Me.lblApp.AutoSize = True
        Me.lblApp.BackColor = System.Drawing.Color.Transparent
        Me.lblApp.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblApp.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblApp.Location = New System.Drawing.Point(43, 397)
        Me.lblApp.Name = "lblApp"
        Me.lblApp.Size = New System.Drawing.Size(29, 12)
        Me.lblApp.TabIndex = 60
        Me.lblApp.Text = "应用"
        '
        'txtOtherFolder
        '
        Me.txtOtherFolder.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtOtherFolder.BackColor = System.Drawing.Color.White
        Me.txtOtherFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtOtherFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtOtherFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtOtherFolder.BorderStyle = tbBorderStyle.FixedSingle
        Me.txtOtherFolder.ForeColor = System.Drawing.Color.Black
        Me.txtOtherFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtOtherFolder.Location = New System.Drawing.Point(43, 339)
        Me.txtOtherFolder.MaxLength = 256
        Me.txtOtherFolder.Name = "txtOtherFolder"
        Me.txtOtherFolder.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.txtOtherFolder.Size = New System.Drawing.Size(387, 21)
        Me.txtOtherFolder.TabIndex = 63
        Me.txtOtherFolder.Tag = Nothing
        Me.txtOtherFolder.tbAutoTab = tbTextBox.AutoTabType.EnterUpDown
        Me.txtOtherFolder.tbReadOnly = tbTextBox.ReadOnlyType.Normal
        Me.txtOtherFolder.tbSelMark = True
        Me.txtOtherFolder.tbTextBind = ""
        Me.txtOtherFolder.TextAlign = System.Windows.Forms.HorizontalAlignment.Left
        Me.txtOtherFolder.TextFormat = tbTextFormat.Normal
        Me.txtOtherFolder.TextTip = ""
        '
        'btnOtherFolder
        '
        Me.btnOtherFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOtherFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnOtherFolder.BindingForm = Nothing
        Me.btnOtherFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOtherFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnOtherFolder.Location = New System.Drawing.Point(438, 338)
        Me.btnOtherFolder.Name = "btnOtherFolder"
        Me.btnOtherFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOtherFolder.Selectable = True
        Me.btnOtherFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnOtherFolder.TabIndex = 62
        Me.btnOtherFolder.tbAdriftIconWhenHover = False
        Me.btnOtherFolder.tbAutoSize = False
        Me.btnOtherFolder.tbAutoSizeEx = False
        Me.btnOtherFolder.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnOtherFolder.tbBackgroundImageState = ImageState.FourState
        Me.btnOtherFolder.tbBadgeNumber = 0
        Me.btnOtherFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOtherFolder.tbEndEllipsis = False
        Me.btnOtherFolder.tbIconHoldPlace = True
        Me.btnOtherFolder.tbIconImage = Nothing
        Me.btnOtherFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOtherFolder.tbIconImageState = ImageState.OneState
        Me.btnOtherFolder.tbIconMore = False
        Me.btnOtherFolder.tbIconMouseDown = Nothing
        Me.btnOtherFolder.tbIconMouseHover = Nothing
        Me.btnOtherFolder.tbIconMouseLeave = Nothing
        Me.btnOtherFolder.tbIconPlaceText = 2
        Me.btnOtherFolder.tbIconReadOnly = Nothing
        Me.btnOtherFolder.tbImageMouseDown = Nothing
        Me.btnOtherFolder.tbImageMouseHover = Nothing
        Me.btnOtherFolder.tbImageMouseLeave = Nothing
        Me.btnOtherFolder.tbReadOnly = False
        Me.btnOtherFolder.tbReadOnlyText = False
        Me.btnOtherFolder.tbShadow = False
        Me.btnOtherFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnOtherFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOtherFolder.tbShowNew = False
        Me.btnOtherFolder.tbShowToolTipOnButton = False
        Me.btnOtherFolder.tbSplit = "13,11,13,11"
        Me.btnOtherFolder.tbText = "更换"
        Me.btnOtherFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOtherFolder.tbTextColor = System.Drawing.Color.Black
        Me.btnOtherFolder.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnOtherFolder.tbTextColorDown = System.Drawing.Color.Black
        Me.btnOtherFolder.tbTextColorHover = System.Drawing.Color.Black
        Me.btnOtherFolder.tbTextMouseDownPlace = 0
        Me.btnOtherFolder.tbToolTip = ""
        Me.btnOtherFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOtherFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOtherFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOtherFolder.VisibleEx = True
        '
        'lblOtherFolder
        '
        Me.lblOtherFolder.AutoSize = True
        Me.lblOtherFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblOtherFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblOtherFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblOtherFolder.Location = New System.Drawing.Point(43, 320)
        Me.lblOtherFolder.Name = "lblOtherFolder"
        Me.lblOtherFolder.Size = New System.Drawing.Size(179, 12)
        Me.lblOtherFolder.TabIndex = 61
        Me.lblOtherFolder.Text = "附加的其他软件（ipa）的目录："
        Me.lblOtherFolder.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'btnSplitCommon
        '
        Me.btnSplitCommon.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSplitCommon.BackColor = System.Drawing.Color.Transparent
        Me.btnSplitCommon.BindingForm = Nothing
        Me.btnSplitCommon.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSplitCommon.Location = New System.Drawing.Point(62, 57)
        Me.btnSplitCommon.Name = "btnSplitCommon"
        Me.btnSplitCommon.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSplitCommon.Selectable = True
        Me.btnSplitCommon.Size = New System.Drawing.Size(452, 2)
        Me.btnSplitCommon.TabIndex = 64
        Me.btnSplitCommon.tbAdriftIconWhenHover = False
        Me.btnSplitCommon.tbAutoSize = False
        Me.btnSplitCommon.tbAutoSizeEx = False
        Me.btnSplitCommon.tbBackgroundImage = My.Resources.welcome_btn_split
        Me.btnSplitCommon.tbBackgroundImageState = ImageState.OneState
        Me.btnSplitCommon.tbBadgeNumber = 0
        Me.btnSplitCommon.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSplitCommon.tbEndEllipsis = False
        Me.btnSplitCommon.tbIconHoldPlace = True
        Me.btnSplitCommon.tbIconImage = Nothing
        Me.btnSplitCommon.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplitCommon.tbIconImageState = ImageState.OneState
        Me.btnSplitCommon.tbIconMore = False
        Me.btnSplitCommon.tbIconMouseDown = Nothing
        Me.btnSplitCommon.tbIconMouseHover = Nothing
        Me.btnSplitCommon.tbIconMouseLeave = Nothing
        Me.btnSplitCommon.tbIconPlaceText = 2
        Me.btnSplitCommon.tbIconReadOnly = Nothing
        Me.btnSplitCommon.tbImageMouseDown = Nothing
        Me.btnSplitCommon.tbImageMouseHover = Nothing
        Me.btnSplitCommon.tbImageMouseLeave = Nothing
        Me.btnSplitCommon.tbReadOnly = False
        Me.btnSplitCommon.tbReadOnlyText = False
        Me.btnSplitCommon.tbShadow = False
        Me.btnSplitCommon.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSplitCommon.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSplitCommon.tbShowNew = False
        Me.btnSplitCommon.tbShowToolTipOnButton = False
        Me.btnSplitCommon.tbSplit = "35,0,35,0"
        Me.btnSplitCommon.tbText = ""
        Me.btnSplitCommon.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplitCommon.tbTextColor = System.Drawing.Color.White
        Me.btnSplitCommon.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSplitCommon.tbTextColorDown = System.Drawing.Color.White
        Me.btnSplitCommon.tbTextColorHover = System.Drawing.Color.White
        Me.btnSplitCommon.tbTextMouseDownPlace = 0
        Me.btnSplitCommon.tbToolTip = ""
        Me.btnSplitCommon.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSplitCommon.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSplitCommon.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplitCommon.VisibleEx = True
        '
        'lblCommon
        '
        Me.lblCommon.AutoSize = True
        Me.lblCommon.BackColor = System.Drawing.Color.Transparent
        Me.lblCommon.Font = New System.Drawing.Font("宋体", 9.5!, System.Drawing.FontStyle.Bold)
        Me.lblCommon.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblCommon.Location = New System.Drawing.Point(12, 52)
        Me.lblCommon.Name = "lblCommon"
        Me.lblCommon.Size = New System.Drawing.Size(35, 13)
        Me.lblCommon.TabIndex = 65
        Me.lblCommon.Text = "常用"
        '
        'lblLanguage
        '
        Me.lblLanguage.BackColor = System.Drawing.Color.Transparent
        Me.lblLanguage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblLanguage.Location = New System.Drawing.Point(48, 76)
        Me.lblLanguage.Name = "lblLanguage"
        Me.lblLanguage.Size = New System.Drawing.Size(119, 17)
        Me.lblLanguage.TabIndex = 67
        Me.lblLanguage.Text = "界面语言"
        Me.lblLanguage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cbxLanguage
        '
        Me.cbxLanguage.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cbxLanguage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxLanguage.FormattingEnabled = True
        Me.cbxLanguage.Location = New System.Drawing.Point(183, 74)
        Me.cbxLanguage.Name = "cbxLanguage"
        Me.cbxLanguage.Size = New System.Drawing.Size(121, 20)
        Me.cbxLanguage.TabIndex = 66
        '
        'lblFolder
        '
        Me.lblFolder.AutoSize = True
        Me.lblFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblFolder.Font = New System.Drawing.Font("宋体", 9.5!, System.Drawing.FontStyle.Bold)
        Me.lblFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblFolder.Location = New System.Drawing.Point(12, 245)
        Me.lblFolder.Name = "lblFolder"
        Me.lblFolder.Size = New System.Drawing.Size(35, 13)
        Me.lblFolder.TabIndex = 69
        Me.lblFolder.Text = "目录"
        '
        'TbButton1
        '
        Me.TbButton1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton1.BackColor = System.Drawing.Color.Transparent
        Me.TbButton1.BindingForm = Nothing
        Me.TbButton1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TbButton1.Location = New System.Drawing.Point(62, 250)
        Me.TbButton1.Name = "TbButton1"
        Me.TbButton1.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton1.Selectable = True
        Me.TbButton1.Size = New System.Drawing.Size(452, 2)
        Me.TbButton1.TabIndex = 68
        Me.TbButton1.tbAdriftIconWhenHover = False
        Me.TbButton1.tbAutoSize = False
        Me.TbButton1.tbAutoSizeEx = False
        Me.TbButton1.tbBackgroundImage = My.Resources.welcome_btn_split
        Me.TbButton1.tbBackgroundImageState = ImageState.OneState
        Me.TbButton1.tbBadgeNumber = 0
        Me.TbButton1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton1.tbEndEllipsis = False
        Me.TbButton1.tbIconHoldPlace = True
        Me.TbButton1.tbIconImage = Nothing
        Me.TbButton1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.tbIconImageState = ImageState.OneState
        Me.TbButton1.tbIconMore = False
        Me.TbButton1.tbIconMouseDown = Nothing
        Me.TbButton1.tbIconMouseHover = Nothing
        Me.TbButton1.tbIconMouseLeave = Nothing
        Me.TbButton1.tbIconPlaceText = 2
        Me.TbButton1.tbIconReadOnly = Nothing
        Me.TbButton1.tbImageMouseDown = Nothing
        Me.TbButton1.tbImageMouseHover = Nothing
        Me.TbButton1.tbImageMouseLeave = Nothing
        Me.TbButton1.tbReadOnly = False
        Me.TbButton1.tbReadOnlyText = False
        Me.TbButton1.tbShadow = False
        Me.TbButton1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton1.tbShowNew = False
        Me.TbButton1.tbShowToolTipOnButton = False
        Me.TbButton1.tbSplit = "35,0,35,0"
        Me.TbButton1.tbText = ""
        Me.TbButton1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.tbTextColor = System.Drawing.Color.White
        Me.TbButton1.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton1.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton1.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton1.tbTextMouseDownPlace = 0
        Me.TbButton1.tbToolTip = ""
        Me.TbButton1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.VisibleEx = True
        '
        'chkAutoInstallAfterDownload
        '
        Me.chkAutoInstallAfterDownload.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.chkAutoInstallAfterDownload.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkAutoInstallAfterDownload.BackColor = System.Drawing.Color.Transparent
        Me.chkAutoInstallAfterDownload.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkAutoInstallAfterDownload.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkAutoInstallAfterDownload.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkAutoInstallAfterDownload.Location = New System.Drawing.Point(43, 157)
        Me.chkAutoInstallAfterDownload.Name = "chkAutoInstallAfterDownload"
        Me.chkAutoInstallAfterDownload.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkAutoInstallAfterDownload.Size = New System.Drawing.Size(116, 18)
        Me.chkAutoInstallAfterDownload.TabIndex = 70
        Me.chkAutoInstallAfterDownload.tbAdriftIconWhenHover = False
        Me.chkAutoInstallAfterDownload.tbAutoSize = False
        Me.chkAutoInstallAfterDownload.tbAutoSizeEx = True
        Me.chkAutoInstallAfterDownload.tbIconChecked = CType(resources.GetObject("chkAutoInstallAfterDownload.tbIconChecked"), System.Drawing.Image)
        Me.chkAutoInstallAfterDownload.tbIconCheckedMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbIconCheckedMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbIconCheckedMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbIconCheckedState = ImageState.FourState
        Me.chkAutoInstallAfterDownload.tbIconHoldPlace = True
        Me.chkAutoInstallAfterDownload.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkAutoInstallAfterDownload.tbIconIndeterminate = CType(resources.GetObject("chkAutoInstallAfterDownload.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkAutoInstallAfterDownload.tbIconIndeterminateMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbIconIndeterminateMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbIconIndeterminateMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbIconIndeterminateState = ImageState.FourState
        Me.chkAutoInstallAfterDownload.tbIconPlaceText = 1
        Me.chkAutoInstallAfterDownload.tbIconUnChecked = CType(resources.GetObject("chkAutoInstallAfterDownload.tbIconUnChecked"), System.Drawing.Image)
        Me.chkAutoInstallAfterDownload.tbIconUnCheckedMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbIconUnCheckedMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbIconUnCheckedMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbIconUnCheckedState = ImageState.FourState
        Me.chkAutoInstallAfterDownload.tbImageBackground = Nothing
        Me.chkAutoInstallAfterDownload.tbImageBackgroundState = ImageState.TwoState
        Me.chkAutoInstallAfterDownload.tbImageCheckedMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbImageCheckedMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbImageCheckedMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbImageUnCheckedMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbImageUnCheckedMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbImageUnCheckedMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbReadOnly = False
        Me.chkAutoInstallAfterDownload.tbShadow = False
        Me.chkAutoInstallAfterDownload.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkAutoInstallAfterDownload.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkAutoInstallAfterDownload.tbSplit = "3,3,3,3"
        Me.chkAutoInstallAfterDownload.tbToolTip = ""
        Me.chkAutoInstallAfterDownload.Text = "下载后立即安装"
        Me.chkAutoInstallAfterDownload.UseVisualStyleBackColor = False
        '
        'chkAutoShowMsgAfterDownload
        '
        Me.chkAutoShowMsgAfterDownload.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.chkAutoShowMsgAfterDownload.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkAutoShowMsgAfterDownload.BackColor = System.Drawing.Color.Transparent
        Me.chkAutoShowMsgAfterDownload.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkAutoShowMsgAfterDownload.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkAutoShowMsgAfterDownload.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkAutoShowMsgAfterDownload.Location = New System.Drawing.Point(43, 130)
        Me.chkAutoShowMsgAfterDownload.Name = "chkAutoShowMsgAfterDownload"
        Me.chkAutoShowMsgAfterDownload.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkAutoShowMsgAfterDownload.Size = New System.Drawing.Size(116, 18)
        Me.chkAutoShowMsgAfterDownload.TabIndex = 71
        Me.chkAutoShowMsgAfterDownload.tbAdriftIconWhenHover = False
        Me.chkAutoShowMsgAfterDownload.tbAutoSize = False
        Me.chkAutoShowMsgAfterDownload.tbAutoSizeEx = True
        Me.chkAutoShowMsgAfterDownload.tbIconChecked = CType(resources.GetObject("chkAutoShowMsgAfterDownload.tbIconChecked"), System.Drawing.Image)
        Me.chkAutoShowMsgAfterDownload.tbIconCheckedMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconCheckedMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconCheckedMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconCheckedState = ImageState.FourState
        Me.chkAutoShowMsgAfterDownload.tbIconHoldPlace = True
        Me.chkAutoShowMsgAfterDownload.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminate = CType(resources.GetObject("chkAutoShowMsgAfterDownload.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminateMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminateMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminateMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminateState = ImageState.FourState
        Me.chkAutoShowMsgAfterDownload.tbIconPlaceText = 1
        Me.chkAutoShowMsgAfterDownload.tbIconUnChecked = CType(resources.GetObject("chkAutoShowMsgAfterDownload.tbIconUnChecked"), System.Drawing.Image)
        Me.chkAutoShowMsgAfterDownload.tbIconUnCheckedMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconUnCheckedMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconUnCheckedMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconUnCheckedState = ImageState.FourState
        Me.chkAutoShowMsgAfterDownload.tbImageBackground = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageBackgroundState = ImageState.TwoState
        Me.chkAutoShowMsgAfterDownload.tbImageCheckedMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageCheckedMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageCheckedMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageUnCheckedMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageUnCheckedMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageUnCheckedMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbReadOnly = False
        Me.chkAutoShowMsgAfterDownload.tbShadow = False
        Me.chkAutoShowMsgAfterDownload.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkAutoShowMsgAfterDownload.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkAutoShowMsgAfterDownload.tbSplit = "3,3,3,3"
        Me.chkAutoShowMsgAfterDownload.tbToolTip = ""
        Me.chkAutoShowMsgAfterDownload.Text = "下载后弹框提示"
        Me.chkAutoShowMsgAfterDownload.UseVisualStyleBackColor = False
        '
        'frmSetting
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(527, 585)
        Me.Controls.Add(Me.chkAutoShowMsgAfterDownload)
        Me.Controls.Add(Me.chkAutoInstallAfterDownload)
        Me.Controls.Add(Me.lblFolder)
        Me.Controls.Add(Me.TbButton1)
        Me.Controls.Add(Me.lblLanguage)
        Me.Controls.Add(Me.cbxLanguage)
        Me.Controls.Add(Me.lblCommon)
        Me.Controls.Add(Me.btnSplitCommon)
        Me.Controls.Add(Me.txtOtherFolder)
        Me.Controls.Add(Me.btnOtherFolder)
        Me.Controls.Add(Me.lblOtherFolder)
        Me.Controls.Add(Me.lblApp)
        Me.Controls.Add(Me.txtWallpaperFolder)
        Me.Controls.Add(Me.btnWallpaperFolder)
        Me.Controls.Add(Me.lblWallpaperFolder)
        Me.Controls.Add(Me.txtRingtoneFolder)
        Me.Controls.Add(Me.btnRingtoneFolder)
        Me.Controls.Add(Me.lblRingtoneFolder)
        Me.Controls.Add(Me.txtMusicFolder)
        Me.Controls.Add(Me.btnMusicFolder)
        Me.Controls.Add(Me.lblMusicFolder)
        Me.Controls.Add(Me.chkRemember)
        Me.Controls.Add(Me.chkLinkIPAPackage)
        Me.Controls.Add(Me.rdbMini)
        Me.Controls.Add(Me.rdbExist)
        Me.Controls.Add(Me.txtiTunesFolder)
        Me.Controls.Add(Me.txtDownloadFolder)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.btnDownloadFolder)
        Me.Controls.Add(Me.lblDownloadFolder)
        Me.Controls.Add(Me.btniTunesFolder)
        Me.Controls.Add(Me.lbliTunesFolder)
        Me.Controls.Add(Me.btn_close)
        Me.MinimumSize = New System.Drawing.Size(11, 40)
        Me.Name = "frmSetting"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,8"
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "Settings"
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btn_close As tbButton
    Friend WithEvents btniTunesFolder As tbButton
    Friend WithEvents lbliTunesFolder As System.Windows.Forms.Label
    Friend WithEvents btnDownloadFolder As tbButton
    Friend WithEvents lblDownloadFolder As System.Windows.Forms.Label
    Friend WithEvents btnCancel As tbButton
    Friend WithEvents btnOK As tbButton
    Friend WithEvents txtDownloadFolder As tbTextBox
    Friend WithEvents txtiTunesFolder As tbTextBox
    Friend WithEvents rdbExist As tbRadioButton
    Friend WithEvents rdbMini As tbRadioButton
    Friend WithEvents chkLinkIPAPackage As tbCheckBox
    Friend WithEvents chkRemember As tbCheckBox
    Friend WithEvents txtMusicFolder As tbTextBox
    Friend WithEvents btnMusicFolder As tbButton
    Friend WithEvents lblMusicFolder As System.Windows.Forms.Label
    Friend WithEvents txtRingtoneFolder As tbTextBox
    Friend WithEvents btnRingtoneFolder As tbButton
    Friend WithEvents lblRingtoneFolder As System.Windows.Forms.Label
    Friend WithEvents txtWallpaperFolder As tbTextBox
    Friend WithEvents btnWallpaperFolder As tbButton
    Friend WithEvents lblWallpaperFolder As System.Windows.Forms.Label
    Friend WithEvents lblApp As System.Windows.Forms.Label
    Friend WithEvents txtOtherFolder As tbTextBox
    Friend WithEvents btnOtherFolder As tbButton
    Friend WithEvents lblOtherFolder As System.Windows.Forms.Label
    Friend WithEvents btnSplitCommon As tbButton
    Friend WithEvents lblCommon As System.Windows.Forms.Label
    Friend WithEvents lblLanguage As System.Windows.Forms.Label
    Friend WithEvents cbxLanguage As System.Windows.Forms.ComboBox
    Friend WithEvents lblFolder As System.Windows.Forms.Label
    Friend WithEvents TbButton1 As tbButton
    Friend WithEvents chkAutoInstallAfterDownload As tbCheckBox
    Friend WithEvents chkAutoShowMsgAfterDownload As tbCheckBox
End Class
