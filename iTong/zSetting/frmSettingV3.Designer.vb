﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSettingV3
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSettingV3))
        Me.btn_close = New iTong.Components.tbButton()
        Me.btniTunesFolder = New iTong.Components.tbButton()
        Me.lbliTunesFolder = New System.Windows.Forms.Label()
        Me.btnDownloadFolder = New iTong.Components.tbButton()
        Me.lblDownloadFolder = New System.Windows.Forms.Label()
        Me.btnCancel = New iTong.Components.tbButton()
        Me.btnOK = New iTong.Components.tbButton()
        Me.txtDownloadFolder = New iTong.Components.tbTextBox()
        Me.txtiTunesFolder = New iTong.Components.tbTextBox()
        Me.rdbExist = New iTong.Components.tbRadioButton()
        Me.rdbMini = New iTong.Components.tbRadioButton()
        Me.chkLinkIPAPackage = New iTong.Components.tbCheckBox()
        Me.chkRemember = New iTong.Components.tbCheckBox()
        Me.txtMusicFolder = New iTong.Components.tbTextBox()
        Me.btnMusicFolder = New iTong.Components.tbButton()
        Me.lblMusicFolder = New System.Windows.Forms.Label()
        Me.txtRingtoneFolder = New iTong.Components.tbTextBox()
        Me.btnRingtoneFolder = New iTong.Components.tbButton()
        Me.lblRingtoneFolder = New System.Windows.Forms.Label()
        Me.txtWallpaperFolder = New iTong.Components.tbTextBox()
        Me.btnWallpaperFolder = New iTong.Components.tbButton()
        Me.lblWallpaperFolder = New System.Windows.Forms.Label()
        Me.lblApp = New System.Windows.Forms.Label()
        Me.txtOtherFolder = New iTong.Components.tbTextBox()
        Me.btnOtherFolder = New iTong.Components.tbButton()
        Me.lblOtherFolder = New System.Windows.Forms.Label()
        Me.lblLanguage = New System.Windows.Forms.Label()
        Me.cbxLanguage = New System.Windows.Forms.ComboBox()
        Me.chkAutoInstallAfterDownload = New iTong.Components.tbCheckBox()
        Me.chkAutoShowMsgAfterDownload = New iTong.Components.tbCheckBox()
        Me.pnlNormal = New iTong.Components.tbPanel()
        Me.cbxLog = New iTong.Components.tbCheckBox()
        Me.lblMaxDownload = New System.Windows.Forms.Label()
        Me.lblDownload = New System.Windows.Forms.Label()
        Me.numDownload = New System.Windows.Forms.NumericUpDown()
        Me.lblClose = New System.Windows.Forms.Label()
        Me.lblInstall = New System.Windows.Forms.Label()
        Me.chkIconShowUpdateNumber = New iTong.Components.tbCheckBox()
        Me.pnlFolder = New iTong.Components.tbPanel()
        Me.txtFirmware = New iTong.Components.tbTextBox()
        Me.btnFirmware = New iTong.Components.tbButton()
        Me.lblFirmware = New System.Windows.Forms.Label()
        Me.pnlLeft = New iTong.Components.tbPanel()
        Me.btnAndroidWeChat = New iTong.Components.tbRadioButton()
        Me.btnDervicePermission = New iTong.Components.tbRadioButton()
        Me.btnAppleID = New iTong.Components.tbRadioButton()
        Me.btnDNS = New iTong.Components.tbRadioButton()
        Me.btnNormal = New iTong.Components.tbRadioButton()
        Me.btnFolder = New iTong.Components.tbRadioButton()
        Me.pnlRight = New iTong.Components.tbPanel()
        Me.pnlDNS = New iTong.Components.tbPanel()
        Me.btnSetUserDNS = New iTong.Components.tbButton()
        Me.btnSetDefaultDNS = New iTong.Components.tbButton()
        Me.txtDNS2 = New iTong.Components.tbTextBox()
        Me.lblDNS2 = New System.Windows.Forms.Label()
        Me.txtDNS1 = New iTong.Components.tbTextBox()
        Me.lblDNS1 = New System.Windows.Forms.Label()
        Me.pnlAppleID = New iTong.Components.tbPanel()
        Me.chkLoginCode = New iTong.Components.tbCheckBox()
        Me.lblLoginApplids = New System.Windows.Forms.Label()
        Me.lblAppleIdMessage = New iTong.Components.tbButton()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.picPwd = New System.Windows.Forms.PictureBox()
        Me.picId = New System.Windows.Forms.PictureBox()
        Me.picCheck = New System.Windows.Forms.PictureBox()
        Me.txtPwd = New iTong.Components.tbTextBox()
        Me.txtId = New iTong.Components.tbTextBox()
        Me.pnlAppleIDGrid = New iTong.Components.tbPanel()
        Me.lblRegister = New iTong.Components.tbLabel()
        Me.btnLoginAppleId = New iTong.Components.tbButton()
        Me.lblApplePwd = New System.Windows.Forms.Label()
        Me.lblAppleID = New System.Windows.Forms.Label()
        Me.chkAgree = New iTong.Components.tbCheckBox()
        Me.lblClause = New iTong.Components.tbLabel()
        Me.pnlDervicePermission = New iTong.Components.tbPanel()
        Me.btnSplit = New iTong.Components.tbButton()
        Me.cbxPermitInstallTui = New iTong.Components.tbCheckBox()
        Me.cbxDervicePermission = New iTong.Components.tbCheckBox()
        Me.lblPermissionInstallTui = New System.Windows.Forms.Label()
        Me.lblDervicePermission = New System.Windows.Forms.Label()
        Me.lblPermissionIntallTuiMsg = New System.Windows.Forms.Label()
        Me.lblDervicePermissionMsg = New System.Windows.Forms.Label()
        Me.pnlAndroidWeChat = New iTong.Components.tbPanel()
        Me.Label2 = New System.Windows.Forms.Label()
        Me.cbxOldAndroidWeChat = New iTong.Components.tbCheckBox()
        Me.btnKnowiWechatAssistant = New iTong.Components.tbLabel()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.lblPhoto = New System.Windows.Forms.Label()
        Me.cbxPhoto = New iTong.Components.tbCheckBox()
        Me.lblPhotoDesc = New System.Windows.Forms.Label()
        Me.pnlNormal.SuspendLayout()
        CType(Me.numDownload, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlFolder.SuspendLayout()
        Me.pnlLeft.SuspendLayout()
        Me.pnlRight.SuspendLayout()
        Me.pnlDNS.SuspendLayout()
        Me.pnlAppleID.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picPwd, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picId, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picCheck, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDervicePermission.SuspendLayout()
        Me.pnlAndroidWeChat.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(1349, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 8
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btniTunesFolder
        '
        Me.btniTunesFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btniTunesFolder.BackColor = System.Drawing.Color.Transparent
        Me.btniTunesFolder.BindingForm = Nothing
        Me.btniTunesFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btniTunesFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btniTunesFolder.Location = New System.Drawing.Point(298, 46)
        Me.btniTunesFolder.Name = "btniTunesFolder"
        Me.btniTunesFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btniTunesFolder.Selectable = True
        Me.btniTunesFolder.Size = New System.Drawing.Size(65, 23)
        Me.btniTunesFolder.TabIndex = 29
        Me.btniTunesFolder.tbAdriftIconWhenHover = False
        Me.btniTunesFolder.tbAutoSize = False
        Me.btniTunesFolder.tbAutoSizeEx = False
        Me.btniTunesFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btniTunesFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btniTunesFolder.tbBadgeNumber = 0
        Me.btniTunesFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniTunesFolder.tbEndEllipsis = False
        Me.btniTunesFolder.tbIconHoldPlace = True
        Me.btniTunesFolder.tbIconImage = Nothing
        Me.btniTunesFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniTunesFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniTunesFolder.tbIconMore = False
        Me.btniTunesFolder.tbIconMouseDown = Nothing
        Me.btniTunesFolder.tbIconMouseHover = Nothing
        Me.btniTunesFolder.tbIconMouseLeave = Nothing
        Me.btniTunesFolder.tbIconPlaceText = 2
        Me.btniTunesFolder.tbIconReadOnly = Nothing
        Me.btniTunesFolder.tbImageMouseDown = Nothing
        Me.btniTunesFolder.tbImageMouseHover = Nothing
        Me.btniTunesFolder.tbImageMouseLeave = Nothing
        Me.btniTunesFolder.tbProgressValue = 50
        Me.btniTunesFolder.tbReadOnly = False
        Me.btniTunesFolder.tbReadOnlyText = False
        Me.btniTunesFolder.tbShadow = False
        Me.btniTunesFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btniTunesFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btniTunesFolder.tbShowDot = False
        Me.btniTunesFolder.tbShowMoreIconImg = CType(resources.GetObject("btniTunesFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btniTunesFolder.tbShowNew = False
        Me.btniTunesFolder.tbShowProgress = False
        Me.btniTunesFolder.tbShowTip = True
        Me.btniTunesFolder.tbShowToolTipOnButton = False
        Me.btniTunesFolder.tbSplit = "13,11,13,11"
        Me.btniTunesFolder.tbText = "更换"
        Me.btniTunesFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniTunesFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btniTunesFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btniTunesFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btniTunesFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btniTunesFolder.tbTextMouseDownPlace = 0
        Me.btniTunesFolder.tbToolTip = ""
        Me.btniTunesFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniTunesFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniTunesFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniTunesFolder.VisibleEx = True
        '
        'lbliTunesFolder
        '
        Me.lbliTunesFolder.AutoSize = True
        Me.lbliTunesFolder.BackColor = System.Drawing.Color.Transparent
        Me.lbliTunesFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbliTunesFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lbliTunesFolder.Location = New System.Drawing.Point(34, 28)
        Me.lbliTunesFolder.Name = "lbliTunesFolder"
        Me.lbliTunesFolder.Size = New System.Drawing.Size(83, 12)
        Me.lbliTunesFolder.TabIndex = 28
        Me.lbliTunesFolder.Text = "iTunes Folder"
        '
        'btnDownloadFolder
        '
        Me.btnDownloadFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDownloadFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnDownloadFolder.BindingForm = Nothing
        Me.btnDownloadFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDownloadFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnDownloadFolder.Location = New System.Drawing.Point(298, 190)
        Me.btnDownloadFolder.Name = "btnDownloadFolder"
        Me.btnDownloadFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnDownloadFolder.Selectable = True
        Me.btnDownloadFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnDownloadFolder.TabIndex = 32
        Me.btnDownloadFolder.tbAdriftIconWhenHover = False
        Me.btnDownloadFolder.tbAutoSize = False
        Me.btnDownloadFolder.tbAutoSizeEx = False
        Me.btnDownloadFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnDownloadFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDownloadFolder.tbBadgeNumber = 0
        Me.btnDownloadFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDownloadFolder.tbEndEllipsis = False
        Me.btnDownloadFolder.tbIconHoldPlace = True
        Me.btnDownloadFolder.tbIconImage = Nothing
        Me.btnDownloadFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDownloadFolder.tbIconMore = False
        Me.btnDownloadFolder.tbIconMouseDown = Nothing
        Me.btnDownloadFolder.tbIconMouseHover = Nothing
        Me.btnDownloadFolder.tbIconMouseLeave = Nothing
        Me.btnDownloadFolder.tbIconPlaceText = 2
        Me.btnDownloadFolder.tbIconReadOnly = Nothing
        Me.btnDownloadFolder.tbImageMouseDown = Nothing
        Me.btnDownloadFolder.tbImageMouseHover = Nothing
        Me.btnDownloadFolder.tbImageMouseLeave = Nothing
        Me.btnDownloadFolder.tbProgressValue = 50
        Me.btnDownloadFolder.tbReadOnly = False
        Me.btnDownloadFolder.tbReadOnlyText = False
        Me.btnDownloadFolder.tbShadow = False
        Me.btnDownloadFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDownloadFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDownloadFolder.tbShowDot = False
        Me.btnDownloadFolder.tbShowMoreIconImg = CType(resources.GetObject("btnDownloadFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDownloadFolder.tbShowNew = False
        Me.btnDownloadFolder.tbShowProgress = False
        Me.btnDownloadFolder.tbShowTip = True
        Me.btnDownloadFolder.tbShowToolTipOnButton = False
        Me.btnDownloadFolder.tbSplit = "13,11,13,11"
        Me.btnDownloadFolder.tbText = "更换"
        Me.btnDownloadFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDownloadFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDownloadFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDownloadFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnDownloadFolder.tbTextMouseDownPlace = 0
        Me.btnDownloadFolder.tbToolTip = ""
        Me.btnDownloadFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDownloadFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDownloadFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadFolder.VisibleEx = True
        '
        'lblDownloadFolder
        '
        Me.lblDownloadFolder.AutoSize = True
        Me.lblDownloadFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblDownloadFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDownloadFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblDownloadFolder.Location = New System.Drawing.Point(34, 172)
        Me.lblDownloadFolder.Name = "lblDownloadFolder"
        Me.lblDownloadFolder.Size = New System.Drawing.Size(95, 12)
        Me.lblDownloadFolder.TabIndex = 31
        Me.lblDownloadFolder.Text = "Download Folder"
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.Location = New System.Drawing.Point(1289, 809)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(65, 23)
        Me.btnCancel.TabIndex = 40
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbProgressValue = 50
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowDot = False
        Me.btnCancel.tbShowMoreIconImg = CType(resources.GetObject("btnCancel.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowProgress = False
        Me.btnCancel.tbShowTip = True
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(1215, 809)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(65, 23)
        Me.btnOK.TabIndex = 39
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "保存"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'txtDownloadFolder
        '
        Me.txtDownloadFolder.BackColor = System.Drawing.Color.White
        Me.txtDownloadFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtDownloadFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtDownloadFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtDownloadFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtDownloadFolder.ForeColor = System.Drawing.Color.Black
        Me.txtDownloadFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDownloadFolder.Location = New System.Drawing.Point(88, 191)
        Me.txtDownloadFolder.MaxLength = 256
        Me.txtDownloadFolder.Name = "txtDownloadFolder"
        Me.txtDownloadFolder.Size = New System.Drawing.Size(204, 21)
        Me.txtDownloadFolder.TabIndex = 44
        Me.txtDownloadFolder.Tag = Nothing
        Me.txtDownloadFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtDownloadFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtDownloadFolder.tbSelMark = True
        Me.txtDownloadFolder.tbTextBind = ""
        Me.txtDownloadFolder.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtDownloadFolder.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDownloadFolder.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtDownloadFolder.TextTip = ""
        '
        'txtiTunesFolder
        '
        Me.txtiTunesFolder.BackColor = System.Drawing.Color.White
        Me.txtiTunesFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtiTunesFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtiTunesFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtiTunesFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtiTunesFolder.ForeColor = System.Drawing.Color.Black
        Me.txtiTunesFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtiTunesFolder.Location = New System.Drawing.Point(34, 47)
        Me.txtiTunesFolder.MaxLength = 256
        Me.txtiTunesFolder.Name = "txtiTunesFolder"
        Me.txtiTunesFolder.Size = New System.Drawing.Size(258, 21)
        Me.txtiTunesFolder.TabIndex = 45
        Me.txtiTunesFolder.Tag = Nothing
        Me.txtiTunesFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtiTunesFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtiTunesFolder.tbSelMark = True
        Me.txtiTunesFolder.tbTextBind = ""
        Me.txtiTunesFolder.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtiTunesFolder.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtiTunesFolder.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtiTunesFolder.TextTip = ""
        '
        'rdbExist
        '
        Me.rdbExist.Appearance = System.Windows.Forms.Appearance.Button
        Me.rdbExist.BackColor = System.Drawing.Color.Transparent
        Me.rdbExist.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbExist.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbExist.Location = New System.Drawing.Point(212, 324)
        Me.rdbExist.Name = "rdbExist"
        Me.rdbExist.Size = New System.Drawing.Size(151, 22)
        Me.rdbExist.TabIndex = 47
        Me.rdbExist.TabStop = True
        Me.rdbExist.tbAdriftIconWhenHover = False
        Me.rdbExist.tbAutoSize = False
        Me.rdbExist.tbAutoSizeEx = False
        Me.rdbExist.tbBadgeNumber = 0
        Me.rdbExist.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.rdbExist.tbEnableToolTip = False
        Me.rdbExist.tbIconCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbExist.tbIconCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbExist.tbIconCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbExist.tbIconHoldPlace = True
        Me.rdbExist.tbIconImage = Nothing
        Me.rdbExist.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.rdbExist.tbIconImageState = iTong.Components.ImageState.OneState
        Me.rdbExist.tbIconMore = False
        Me.rdbExist.tbIconMoreImage = CType(resources.GetObject("rdbExist.tbIconMoreImage"), System.Drawing.Image)
        Me.rdbExist.tbIconPlaceText = 5
        Me.rdbExist.tbIconReadOnly = Nothing
        Me.rdbExist.tbIconReadOnlyLight = Nothing
        Me.rdbExist.tbIconUnCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbExist.tbIconUnCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbExist.tbIconUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbExist.tbImageBackground = Nothing
        Me.rdbExist.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.rdbExist.tbImageCheckedMouseDown = Nothing
        Me.rdbExist.tbImageCheckedMouseHover = Nothing
        Me.rdbExist.tbImageCheckedMouseLeave = Nothing
        Me.rdbExist.tbImageUnCheckedMouseDown = Nothing
        Me.rdbExist.tbImageUnCheckedMouseHover = Nothing
        Me.rdbExist.tbImageUnCheckedMouseLeave = Nothing
        Me.rdbExist.tbNoShowBadgeNumberImage = CType(resources.GetObject("rdbExist.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.rdbExist.tbReadOnly = False
        Me.rdbExist.tbShadow = False
        Me.rdbExist.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.rdbExist.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.rdbExist.tbShowBadgeNumber = True
        Me.rdbExist.tbShowBadgeNumberWhenSelection = True
        Me.rdbExist.tbShowNew = False
        Me.rdbExist.tbSplit = "3,3,3,3"
        Me.rdbExist.tbTag = Nothing
        Me.rdbExist.tbToolTip = ""
        Me.rdbExist.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.rdbExist.Text = "退出程序"
        Me.rdbExist.UseVisualStyleBackColor = False
        '
        'rdbMini
        '
        Me.rdbMini.Appearance = System.Windows.Forms.Appearance.Button
        Me.rdbMini.BackColor = System.Drawing.Color.Transparent
        Me.rdbMini.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbMini.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbMini.Location = New System.Drawing.Point(41, 324)
        Me.rdbMini.Name = "rdbMini"
        Me.rdbMini.Size = New System.Drawing.Size(151, 22)
        Me.rdbMini.TabIndex = 48
        Me.rdbMini.TabStop = True
        Me.rdbMini.tbAdriftIconWhenHover = False
        Me.rdbMini.tbAutoSize = False
        Me.rdbMini.tbAutoSizeEx = False
        Me.rdbMini.tbBadgeNumber = 0
        Me.rdbMini.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.rdbMini.tbEnableToolTip = False
        Me.rdbMini.tbIconCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbMini.tbIconCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbMini.tbIconCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_checked
        Me.rdbMini.tbIconHoldPlace = True
        Me.rdbMini.tbIconImage = Nothing
        Me.rdbMini.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.rdbMini.tbIconImageState = iTong.Components.ImageState.OneState
        Me.rdbMini.tbIconMore = False
        Me.rdbMini.tbIconMoreImage = CType(resources.GetObject("rdbMini.tbIconMoreImage"), System.Drawing.Image)
        Me.rdbMini.tbIconPlaceText = 5
        Me.rdbMini.tbIconReadOnly = Nothing
        Me.rdbMini.tbIconReadOnlyLight = Nothing
        Me.rdbMini.tbIconUnCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbMini.tbIconUnCheckedMouseHover = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbMini.tbIconUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_unchecked
        Me.rdbMini.tbImageBackground = Nothing
        Me.rdbMini.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.rdbMini.tbImageCheckedMouseDown = Nothing
        Me.rdbMini.tbImageCheckedMouseHover = Nothing
        Me.rdbMini.tbImageCheckedMouseLeave = Nothing
        Me.rdbMini.tbImageUnCheckedMouseDown = Nothing
        Me.rdbMini.tbImageUnCheckedMouseHover = Nothing
        Me.rdbMini.tbImageUnCheckedMouseLeave = Nothing
        Me.rdbMini.tbNoShowBadgeNumberImage = CType(resources.GetObject("rdbMini.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.rdbMini.tbReadOnly = False
        Me.rdbMini.tbShadow = False
        Me.rdbMini.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.rdbMini.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.rdbMini.tbShowBadgeNumber = True
        Me.rdbMini.tbShowBadgeNumberWhenSelection = True
        Me.rdbMini.tbShowNew = False
        Me.rdbMini.tbSplit = "3,3,3,3"
        Me.rdbMini.tbTag = Nothing
        Me.rdbMini.tbToolTip = ""
        Me.rdbMini.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.rdbMini.Text = "最小化到托盘"
        Me.rdbMini.UseVisualStyleBackColor = False
        '
        'chkLinkIPAPackage
        '
        Me.chkLinkIPAPackage.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkLinkIPAPackage.BackColor = System.Drawing.Color.Transparent
        Me.chkLinkIPAPackage.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkLinkIPAPackage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkLinkIPAPackage.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkLinkIPAPackage.Location = New System.Drawing.Point(34, 128)
        Me.chkLinkIPAPackage.Name = "chkLinkIPAPackage"
        Me.chkLinkIPAPackage.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkLinkIPAPackage.Size = New System.Drawing.Size(158, 18)
        Me.chkLinkIPAPackage.TabIndex = 49
        Me.chkLinkIPAPackage.tbAdriftIconWhenHover = False
        Me.chkLinkIPAPackage.tbAutoSize = False
        Me.chkLinkIPAPackage.tbAutoSizeEx = True
        Me.chkLinkIPAPackage.tbIconChecked = CType(resources.GetObject("chkLinkIPAPackage.tbIconChecked"), System.Drawing.Image)
        Me.chkLinkIPAPackage.tbIconCheckedMouseDown = Nothing
        Me.chkLinkIPAPackage.tbIconCheckedMouseHover = Nothing
        Me.chkLinkIPAPackage.tbIconCheckedMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkLinkIPAPackage.tbIconHoldPlace = True
        Me.chkLinkIPAPackage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkLinkIPAPackage.tbIconIndeterminate = CType(resources.GetObject("chkLinkIPAPackage.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkLinkIPAPackage.tbIconIndeterminateMouseDown = Nothing
        Me.chkLinkIPAPackage.tbIconIndeterminateMouseHover = Nothing
        Me.chkLinkIPAPackage.tbIconIndeterminateMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkLinkIPAPackage.tbIconPlaceText = 1
        Me.chkLinkIPAPackage.tbIconUnChecked = CType(resources.GetObject("chkLinkIPAPackage.tbIconUnChecked"), System.Drawing.Image)
        Me.chkLinkIPAPackage.tbIconUnCheckedMouseDown = Nothing
        Me.chkLinkIPAPackage.tbIconUnCheckedMouseHover = Nothing
        Me.chkLinkIPAPackage.tbIconUnCheckedMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkLinkIPAPackage.tbImageBackground = Nothing
        Me.chkLinkIPAPackage.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkLinkIPAPackage.tbImageCheckedMouseDown = Nothing
        Me.chkLinkIPAPackage.tbImageCheckedMouseHover = Nothing
        Me.chkLinkIPAPackage.tbImageCheckedMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbImageUnCheckedMouseDown = Nothing
        Me.chkLinkIPAPackage.tbImageUnCheckedMouseHover = Nothing
        Me.chkLinkIPAPackage.tbImageUnCheckedMouseLeave = Nothing
        Me.chkLinkIPAPackage.tbReadOnly = False
        Me.chkLinkIPAPackage.tbShadow = False
        Me.chkLinkIPAPackage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkLinkIPAPackage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkLinkIPAPackage.tbSplit = "3,3,3,3"
        Me.chkLinkIPAPackage.tbToolTip = ""
        Me.chkLinkIPAPackage.Text = "设为 ipa 默认打开方式"
        Me.chkLinkIPAPackage.UseVisualStyleBackColor = False
        '
        'chkRemember
        '
        Me.chkRemember.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkRemember.BackColor = System.Drawing.Color.Transparent
        Me.chkRemember.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkRemember.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkRemember.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkRemember.Location = New System.Drawing.Point(34, 294)
        Me.chkRemember.Name = "chkRemember"
        Me.chkRemember.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkRemember.Size = New System.Drawing.Size(116, 18)
        Me.chkRemember.TabIndex = 50
        Me.chkRemember.tbAdriftIconWhenHover = False
        Me.chkRemember.tbAutoSize = False
        Me.chkRemember.tbAutoSizeEx = True
        Me.chkRemember.tbIconChecked = CType(resources.GetObject("chkRemember.tbIconChecked"), System.Drawing.Image)
        Me.chkRemember.tbIconCheckedMouseDown = Nothing
        Me.chkRemember.tbIconCheckedMouseHover = Nothing
        Me.chkRemember.tbIconCheckedMouseLeave = Nothing
        Me.chkRemember.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkRemember.tbIconHoldPlace = True
        Me.chkRemember.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkRemember.tbIconIndeterminate = CType(resources.GetObject("chkRemember.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkRemember.tbIconIndeterminateMouseDown = Nothing
        Me.chkRemember.tbIconIndeterminateMouseHover = Nothing
        Me.chkRemember.tbIconIndeterminateMouseLeave = Nothing
        Me.chkRemember.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkRemember.tbIconPlaceText = 1
        Me.chkRemember.tbIconUnChecked = CType(resources.GetObject("chkRemember.tbIconUnChecked"), System.Drawing.Image)
        Me.chkRemember.tbIconUnCheckedMouseDown = Nothing
        Me.chkRemember.tbIconUnCheckedMouseHover = Nothing
        Me.chkRemember.tbIconUnCheckedMouseLeave = Nothing
        Me.chkRemember.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkRemember.tbImageBackground = Nothing
        Me.chkRemember.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkRemember.tbImageCheckedMouseDown = Nothing
        Me.chkRemember.tbImageCheckedMouseHover = Nothing
        Me.chkRemember.tbImageCheckedMouseLeave = Nothing
        Me.chkRemember.tbImageUnCheckedMouseDown = Nothing
        Me.chkRemember.tbImageUnCheckedMouseHover = Nothing
        Me.chkRemember.tbImageUnCheckedMouseLeave = Nothing
        Me.chkRemember.tbReadOnly = False
        Me.chkRemember.tbShadow = False
        Me.chkRemember.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkRemember.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkRemember.tbSplit = "3,3,3,3"
        Me.chkRemember.tbToolTip = ""
        Me.chkRemember.Text = "退出时不再提示"
        Me.chkRemember.UseVisualStyleBackColor = False
        '
        'txtMusicFolder
        '
        Me.txtMusicFolder.BackColor = System.Drawing.Color.White
        Me.txtMusicFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtMusicFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtMusicFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtMusicFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtMusicFolder.ForeColor = System.Drawing.Color.Black
        Me.txtMusicFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtMusicFolder.Location = New System.Drawing.Point(88, 225)
        Me.txtMusicFolder.MaxLength = 256
        Me.txtMusicFolder.Name = "txtMusicFolder"
        Me.txtMusicFolder.Size = New System.Drawing.Size(204, 21)
        Me.txtMusicFolder.TabIndex = 53
        Me.txtMusicFolder.Tag = Nothing
        Me.txtMusicFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtMusicFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtMusicFolder.tbSelMark = True
        Me.txtMusicFolder.tbTextBind = ""
        Me.txtMusicFolder.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtMusicFolder.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtMusicFolder.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtMusicFolder.TextTip = ""
        '
        'btnMusicFolder
        '
        Me.btnMusicFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnMusicFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnMusicFolder.BindingForm = Nothing
        Me.btnMusicFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnMusicFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnMusicFolder.Location = New System.Drawing.Point(298, 224)
        Me.btnMusicFolder.Name = "btnMusicFolder"
        Me.btnMusicFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnMusicFolder.Selectable = True
        Me.btnMusicFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnMusicFolder.TabIndex = 52
        Me.btnMusicFolder.tbAdriftIconWhenHover = False
        Me.btnMusicFolder.tbAutoSize = False
        Me.btnMusicFolder.tbAutoSizeEx = False
        Me.btnMusicFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnMusicFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnMusicFolder.tbBadgeNumber = 0
        Me.btnMusicFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnMusicFolder.tbEndEllipsis = False
        Me.btnMusicFolder.tbIconHoldPlace = True
        Me.btnMusicFolder.tbIconImage = Nothing
        Me.btnMusicFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMusicFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnMusicFolder.tbIconMore = False
        Me.btnMusicFolder.tbIconMouseDown = Nothing
        Me.btnMusicFolder.tbIconMouseHover = Nothing
        Me.btnMusicFolder.tbIconMouseLeave = Nothing
        Me.btnMusicFolder.tbIconPlaceText = 2
        Me.btnMusicFolder.tbIconReadOnly = Nothing
        Me.btnMusicFolder.tbImageMouseDown = Nothing
        Me.btnMusicFolder.tbImageMouseHover = Nothing
        Me.btnMusicFolder.tbImageMouseLeave = Nothing
        Me.btnMusicFolder.tbProgressValue = 50
        Me.btnMusicFolder.tbReadOnly = False
        Me.btnMusicFolder.tbReadOnlyText = False
        Me.btnMusicFolder.tbShadow = False
        Me.btnMusicFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnMusicFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnMusicFolder.tbShowDot = False
        Me.btnMusicFolder.tbShowMoreIconImg = CType(resources.GetObject("btnMusicFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnMusicFolder.tbShowNew = False
        Me.btnMusicFolder.tbShowProgress = False
        Me.btnMusicFolder.tbShowTip = True
        Me.btnMusicFolder.tbShowToolTipOnButton = False
        Me.btnMusicFolder.tbSplit = "13,11,13,11"
        Me.btnMusicFolder.tbText = "更换"
        Me.btnMusicFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMusicFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnMusicFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnMusicFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnMusicFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnMusicFolder.tbTextMouseDownPlace = 0
        Me.btnMusicFolder.tbToolTip = ""
        Me.btnMusicFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnMusicFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnMusicFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMusicFolder.VisibleEx = True
        '
        'lblMusicFolder
        '
        Me.lblMusicFolder.AutoSize = True
        Me.lblMusicFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblMusicFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblMusicFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblMusicFolder.Location = New System.Drawing.Point(34, 229)
        Me.lblMusicFolder.Name = "lblMusicFolder"
        Me.lblMusicFolder.Size = New System.Drawing.Size(29, 12)
        Me.lblMusicFolder.TabIndex = 51
        Me.lblMusicFolder.Text = "音乐"
        '
        'txtRingtoneFolder
        '
        Me.txtRingtoneFolder.BackColor = System.Drawing.Color.White
        Me.txtRingtoneFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtRingtoneFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtRingtoneFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtRingtoneFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtRingtoneFolder.ForeColor = System.Drawing.Color.Black
        Me.txtRingtoneFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtRingtoneFolder.Location = New System.Drawing.Point(88, 259)
        Me.txtRingtoneFolder.MaxLength = 256
        Me.txtRingtoneFolder.Name = "txtRingtoneFolder"
        Me.txtRingtoneFolder.Size = New System.Drawing.Size(204, 21)
        Me.txtRingtoneFolder.TabIndex = 56
        Me.txtRingtoneFolder.Tag = Nothing
        Me.txtRingtoneFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtRingtoneFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtRingtoneFolder.tbSelMark = True
        Me.txtRingtoneFolder.tbTextBind = ""
        Me.txtRingtoneFolder.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtRingtoneFolder.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtRingtoneFolder.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtRingtoneFolder.TextTip = ""
        '
        'btnRingtoneFolder
        '
        Me.btnRingtoneFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRingtoneFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnRingtoneFolder.BindingForm = Nothing
        Me.btnRingtoneFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRingtoneFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRingtoneFolder.Location = New System.Drawing.Point(298, 258)
        Me.btnRingtoneFolder.Name = "btnRingtoneFolder"
        Me.btnRingtoneFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnRingtoneFolder.Selectable = True
        Me.btnRingtoneFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnRingtoneFolder.TabIndex = 55
        Me.btnRingtoneFolder.tbAdriftIconWhenHover = False
        Me.btnRingtoneFolder.tbAutoSize = False
        Me.btnRingtoneFolder.tbAutoSizeEx = False
        Me.btnRingtoneFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnRingtoneFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRingtoneFolder.tbBadgeNumber = 0
        Me.btnRingtoneFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRingtoneFolder.tbEndEllipsis = False
        Me.btnRingtoneFolder.tbIconHoldPlace = True
        Me.btnRingtoneFolder.tbIconImage = Nothing
        Me.btnRingtoneFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRingtoneFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRingtoneFolder.tbIconMore = False
        Me.btnRingtoneFolder.tbIconMouseDown = Nothing
        Me.btnRingtoneFolder.tbIconMouseHover = Nothing
        Me.btnRingtoneFolder.tbIconMouseLeave = Nothing
        Me.btnRingtoneFolder.tbIconPlaceText = 2
        Me.btnRingtoneFolder.tbIconReadOnly = Nothing
        Me.btnRingtoneFolder.tbImageMouseDown = Nothing
        Me.btnRingtoneFolder.tbImageMouseHover = Nothing
        Me.btnRingtoneFolder.tbImageMouseLeave = Nothing
        Me.btnRingtoneFolder.tbProgressValue = 50
        Me.btnRingtoneFolder.tbReadOnly = False
        Me.btnRingtoneFolder.tbReadOnlyText = False
        Me.btnRingtoneFolder.tbShadow = False
        Me.btnRingtoneFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRingtoneFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRingtoneFolder.tbShowDot = False
        Me.btnRingtoneFolder.tbShowMoreIconImg = CType(resources.GetObject("btnRingtoneFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRingtoneFolder.tbShowNew = False
        Me.btnRingtoneFolder.tbShowProgress = False
        Me.btnRingtoneFolder.tbShowTip = True
        Me.btnRingtoneFolder.tbShowToolTipOnButton = False
        Me.btnRingtoneFolder.tbSplit = "13,11,13,11"
        Me.btnRingtoneFolder.tbText = "更换"
        Me.btnRingtoneFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRingtoneFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnRingtoneFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnRingtoneFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnRingtoneFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnRingtoneFolder.tbTextMouseDownPlace = 0
        Me.btnRingtoneFolder.tbToolTip = ""
        Me.btnRingtoneFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRingtoneFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRingtoneFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRingtoneFolder.VisibleEx = True
        '
        'lblRingtoneFolder
        '
        Me.lblRingtoneFolder.AutoSize = True
        Me.lblRingtoneFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblRingtoneFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblRingtoneFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblRingtoneFolder.Location = New System.Drawing.Point(34, 263)
        Me.lblRingtoneFolder.Name = "lblRingtoneFolder"
        Me.lblRingtoneFolder.Size = New System.Drawing.Size(29, 12)
        Me.lblRingtoneFolder.TabIndex = 54
        Me.lblRingtoneFolder.Text = "铃声"
        '
        'txtWallpaperFolder
        '
        Me.txtWallpaperFolder.BackColor = System.Drawing.Color.White
        Me.txtWallpaperFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtWallpaperFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtWallpaperFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtWallpaperFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtWallpaperFolder.ForeColor = System.Drawing.Color.Black
        Me.txtWallpaperFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtWallpaperFolder.Location = New System.Drawing.Point(88, 293)
        Me.txtWallpaperFolder.MaxLength = 256
        Me.txtWallpaperFolder.Name = "txtWallpaperFolder"
        Me.txtWallpaperFolder.Size = New System.Drawing.Size(204, 21)
        Me.txtWallpaperFolder.TabIndex = 59
        Me.txtWallpaperFolder.Tag = Nothing
        Me.txtWallpaperFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtWallpaperFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtWallpaperFolder.tbSelMark = True
        Me.txtWallpaperFolder.tbTextBind = ""
        Me.txtWallpaperFolder.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtWallpaperFolder.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtWallpaperFolder.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtWallpaperFolder.TextTip = ""
        '
        'btnWallpaperFolder
        '
        Me.btnWallpaperFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnWallpaperFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnWallpaperFolder.BindingForm = Nothing
        Me.btnWallpaperFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnWallpaperFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnWallpaperFolder.Location = New System.Drawing.Point(298, 292)
        Me.btnWallpaperFolder.Name = "btnWallpaperFolder"
        Me.btnWallpaperFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnWallpaperFolder.Selectable = True
        Me.btnWallpaperFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnWallpaperFolder.TabIndex = 58
        Me.btnWallpaperFolder.tbAdriftIconWhenHover = False
        Me.btnWallpaperFolder.tbAutoSize = False
        Me.btnWallpaperFolder.tbAutoSizeEx = False
        Me.btnWallpaperFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnWallpaperFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnWallpaperFolder.tbBadgeNumber = 0
        Me.btnWallpaperFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnWallpaperFolder.tbEndEllipsis = False
        Me.btnWallpaperFolder.tbIconHoldPlace = True
        Me.btnWallpaperFolder.tbIconImage = Nothing
        Me.btnWallpaperFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWallpaperFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnWallpaperFolder.tbIconMore = False
        Me.btnWallpaperFolder.tbIconMouseDown = Nothing
        Me.btnWallpaperFolder.tbIconMouseHover = Nothing
        Me.btnWallpaperFolder.tbIconMouseLeave = Nothing
        Me.btnWallpaperFolder.tbIconPlaceText = 2
        Me.btnWallpaperFolder.tbIconReadOnly = Nothing
        Me.btnWallpaperFolder.tbImageMouseDown = Nothing
        Me.btnWallpaperFolder.tbImageMouseHover = Nothing
        Me.btnWallpaperFolder.tbImageMouseLeave = Nothing
        Me.btnWallpaperFolder.tbProgressValue = 50
        Me.btnWallpaperFolder.tbReadOnly = False
        Me.btnWallpaperFolder.tbReadOnlyText = False
        Me.btnWallpaperFolder.tbShadow = False
        Me.btnWallpaperFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnWallpaperFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnWallpaperFolder.tbShowDot = False
        Me.btnWallpaperFolder.tbShowMoreIconImg = CType(resources.GetObject("btnWallpaperFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnWallpaperFolder.tbShowNew = False
        Me.btnWallpaperFolder.tbShowProgress = False
        Me.btnWallpaperFolder.tbShowTip = True
        Me.btnWallpaperFolder.tbShowToolTipOnButton = False
        Me.btnWallpaperFolder.tbSplit = "13,11,13,11"
        Me.btnWallpaperFolder.tbText = "更换"
        Me.btnWallpaperFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWallpaperFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnWallpaperFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnWallpaperFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnWallpaperFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnWallpaperFolder.tbTextMouseDownPlace = 0
        Me.btnWallpaperFolder.tbToolTip = ""
        Me.btnWallpaperFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnWallpaperFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWallpaperFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWallpaperFolder.VisibleEx = True
        '
        'lblWallpaperFolder
        '
        Me.lblWallpaperFolder.AutoSize = True
        Me.lblWallpaperFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblWallpaperFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWallpaperFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblWallpaperFolder.Location = New System.Drawing.Point(34, 297)
        Me.lblWallpaperFolder.Name = "lblWallpaperFolder"
        Me.lblWallpaperFolder.Size = New System.Drawing.Size(29, 12)
        Me.lblWallpaperFolder.TabIndex = 57
        Me.lblWallpaperFolder.Text = "壁纸"
        '
        'lblApp
        '
        Me.lblApp.AutoSize = True
        Me.lblApp.BackColor = System.Drawing.Color.Transparent
        Me.lblApp.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblApp.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblApp.Location = New System.Drawing.Point(34, 195)
        Me.lblApp.Name = "lblApp"
        Me.lblApp.Size = New System.Drawing.Size(29, 12)
        Me.lblApp.TabIndex = 60
        Me.lblApp.Text = "应用"
        '
        'txtOtherFolder
        '
        Me.txtOtherFolder.BackColor = System.Drawing.Color.White
        Me.txtOtherFolder.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtOtherFolder.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtOtherFolder.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtOtherFolder.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtOtherFolder.ForeColor = System.Drawing.Color.Black
        Me.txtOtherFolder.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtOtherFolder.Location = New System.Drawing.Point(34, 120)
        Me.txtOtherFolder.MaxLength = 256
        Me.txtOtherFolder.Name = "txtOtherFolder"
        Me.txtOtherFolder.Size = New System.Drawing.Size(258, 21)
        Me.txtOtherFolder.TabIndex = 63
        Me.txtOtherFolder.Tag = Nothing
        Me.txtOtherFolder.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtOtherFolder.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtOtherFolder.tbSelMark = True
        Me.txtOtherFolder.tbTextBind = ""
        Me.txtOtherFolder.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtOtherFolder.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtOtherFolder.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtOtherFolder.TextTip = ""
        '
        'btnOtherFolder
        '
        Me.btnOtherFolder.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOtherFolder.BackColor = System.Drawing.Color.Transparent
        Me.btnOtherFolder.BindingForm = Nothing
        Me.btnOtherFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOtherFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnOtherFolder.Location = New System.Drawing.Point(297, 119)
        Me.btnOtherFolder.Name = "btnOtherFolder"
        Me.btnOtherFolder.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOtherFolder.Selectable = True
        Me.btnOtherFolder.Size = New System.Drawing.Size(65, 23)
        Me.btnOtherFolder.TabIndex = 62
        Me.btnOtherFolder.tbAdriftIconWhenHover = False
        Me.btnOtherFolder.tbAutoSize = False
        Me.btnOtherFolder.tbAutoSizeEx = False
        Me.btnOtherFolder.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnOtherFolder.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOtherFolder.tbBadgeNumber = 0
        Me.btnOtherFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOtherFolder.tbEndEllipsis = False
        Me.btnOtherFolder.tbIconHoldPlace = True
        Me.btnOtherFolder.tbIconImage = Nothing
        Me.btnOtherFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOtherFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOtherFolder.tbIconMore = False
        Me.btnOtherFolder.tbIconMouseDown = Nothing
        Me.btnOtherFolder.tbIconMouseHover = Nothing
        Me.btnOtherFolder.tbIconMouseLeave = Nothing
        Me.btnOtherFolder.tbIconPlaceText = 2
        Me.btnOtherFolder.tbIconReadOnly = Nothing
        Me.btnOtherFolder.tbImageMouseDown = Nothing
        Me.btnOtherFolder.tbImageMouseHover = Nothing
        Me.btnOtherFolder.tbImageMouseLeave = Nothing
        Me.btnOtherFolder.tbProgressValue = 50
        Me.btnOtherFolder.tbReadOnly = False
        Me.btnOtherFolder.tbReadOnlyText = False
        Me.btnOtherFolder.tbShadow = False
        Me.btnOtherFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnOtherFolder.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOtherFolder.tbShowDot = False
        Me.btnOtherFolder.tbShowMoreIconImg = CType(resources.GetObject("btnOtherFolder.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOtherFolder.tbShowNew = False
        Me.btnOtherFolder.tbShowProgress = False
        Me.btnOtherFolder.tbShowTip = True
        Me.btnOtherFolder.tbShowToolTipOnButton = False
        Me.btnOtherFolder.tbSplit = "13,11,13,11"
        Me.btnOtherFolder.tbText = "更换"
        Me.btnOtherFolder.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOtherFolder.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOtherFolder.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOtherFolder.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOtherFolder.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnOtherFolder.tbTextMouseDownPlace = 0
        Me.btnOtherFolder.tbToolTip = ""
        Me.btnOtherFolder.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOtherFolder.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOtherFolder.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOtherFolder.VisibleEx = True
        '
        'lblOtherFolder
        '
        Me.lblOtherFolder.AutoSize = True
        Me.lblOtherFolder.BackColor = System.Drawing.Color.Transparent
        Me.lblOtherFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblOtherFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblOtherFolder.Location = New System.Drawing.Point(34, 101)
        Me.lblOtherFolder.Name = "lblOtherFolder"
        Me.lblOtherFolder.Size = New System.Drawing.Size(179, 12)
        Me.lblOtherFolder.TabIndex = 61
        Me.lblOtherFolder.Text = "附加的其他软件（ipa）的目录："
        Me.lblOtherFolder.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'lblLanguage
        '
        Me.lblLanguage.BackColor = System.Drawing.Color.Transparent
        Me.lblLanguage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblLanguage.Location = New System.Drawing.Point(34, 25)
        Me.lblLanguage.Name = "lblLanguage"
        Me.lblLanguage.Size = New System.Drawing.Size(119, 17)
        Me.lblLanguage.TabIndex = 67
        Me.lblLanguage.Text = "界面语言"
        Me.lblLanguage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cbxLanguage
        '
        Me.cbxLanguage.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cbxLanguage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxLanguage.FormattingEnabled = True
        Me.cbxLanguage.Location = New System.Drawing.Point(34, 55)
        Me.cbxLanguage.Name = "cbxLanguage"
        Me.cbxLanguage.Size = New System.Drawing.Size(226, 20)
        Me.cbxLanguage.TabIndex = 66
        '
        'chkAutoInstallAfterDownload
        '
        Me.chkAutoInstallAfterDownload.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkAutoInstallAfterDownload.BackColor = System.Drawing.Color.Transparent
        Me.chkAutoInstallAfterDownload.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkAutoInstallAfterDownload.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkAutoInstallAfterDownload.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkAutoInstallAfterDownload.Location = New System.Drawing.Point(34, 182)
        Me.chkAutoInstallAfterDownload.Name = "chkAutoInstallAfterDownload"
        Me.chkAutoInstallAfterDownload.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkAutoInstallAfterDownload.Size = New System.Drawing.Size(116, 18)
        Me.chkAutoInstallAfterDownload.TabIndex = 70
        Me.chkAutoInstallAfterDownload.tbAdriftIconWhenHover = False
        Me.chkAutoInstallAfterDownload.tbAutoSize = False
        Me.chkAutoInstallAfterDownload.tbAutoSizeEx = True
        Me.chkAutoInstallAfterDownload.tbIconChecked = CType(resources.GetObject("chkAutoInstallAfterDownload.tbIconChecked"), System.Drawing.Image)
        Me.chkAutoInstallAfterDownload.tbIconCheckedMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbIconCheckedMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbIconCheckedMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkAutoInstallAfterDownload.tbIconHoldPlace = True
        Me.chkAutoInstallAfterDownload.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkAutoInstallAfterDownload.tbIconIndeterminate = CType(resources.GetObject("chkAutoInstallAfterDownload.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkAutoInstallAfterDownload.tbIconIndeterminateMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbIconIndeterminateMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbIconIndeterminateMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkAutoInstallAfterDownload.tbIconPlaceText = 1
        Me.chkAutoInstallAfterDownload.tbIconUnChecked = CType(resources.GetObject("chkAutoInstallAfterDownload.tbIconUnChecked"), System.Drawing.Image)
        Me.chkAutoInstallAfterDownload.tbIconUnCheckedMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbIconUnCheckedMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbIconUnCheckedMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkAutoInstallAfterDownload.tbImageBackground = Nothing
        Me.chkAutoInstallAfterDownload.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkAutoInstallAfterDownload.tbImageCheckedMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbImageCheckedMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbImageCheckedMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbImageUnCheckedMouseDown = Nothing
        Me.chkAutoInstallAfterDownload.tbImageUnCheckedMouseHover = Nothing
        Me.chkAutoInstallAfterDownload.tbImageUnCheckedMouseLeave = Nothing
        Me.chkAutoInstallAfterDownload.tbReadOnly = False
        Me.chkAutoInstallAfterDownload.tbShadow = False
        Me.chkAutoInstallAfterDownload.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkAutoInstallAfterDownload.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkAutoInstallAfterDownload.tbSplit = "3,3,3,3"
        Me.chkAutoInstallAfterDownload.tbToolTip = ""
        Me.chkAutoInstallAfterDownload.Text = "下载后立即安装"
        Me.chkAutoInstallAfterDownload.UseVisualStyleBackColor = False
        '
        'chkAutoShowMsgAfterDownload
        '
        Me.chkAutoShowMsgAfterDownload.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkAutoShowMsgAfterDownload.BackColor = System.Drawing.Color.Transparent
        Me.chkAutoShowMsgAfterDownload.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkAutoShowMsgAfterDownload.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkAutoShowMsgAfterDownload.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkAutoShowMsgAfterDownload.Location = New System.Drawing.Point(34, 155)
        Me.chkAutoShowMsgAfterDownload.Name = "chkAutoShowMsgAfterDownload"
        Me.chkAutoShowMsgAfterDownload.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkAutoShowMsgAfterDownload.Size = New System.Drawing.Size(116, 18)
        Me.chkAutoShowMsgAfterDownload.TabIndex = 71
        Me.chkAutoShowMsgAfterDownload.tbAdriftIconWhenHover = False
        Me.chkAutoShowMsgAfterDownload.tbAutoSize = False
        Me.chkAutoShowMsgAfterDownload.tbAutoSizeEx = True
        Me.chkAutoShowMsgAfterDownload.tbIconChecked = CType(resources.GetObject("chkAutoShowMsgAfterDownload.tbIconChecked"), System.Drawing.Image)
        Me.chkAutoShowMsgAfterDownload.tbIconCheckedMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconCheckedMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconCheckedMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkAutoShowMsgAfterDownload.tbIconHoldPlace = True
        Me.chkAutoShowMsgAfterDownload.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminate = CType(resources.GetObject("chkAutoShowMsgAfterDownload.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminateMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminateMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminateMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkAutoShowMsgAfterDownload.tbIconPlaceText = 1
        Me.chkAutoShowMsgAfterDownload.tbIconUnChecked = CType(resources.GetObject("chkAutoShowMsgAfterDownload.tbIconUnChecked"), System.Drawing.Image)
        Me.chkAutoShowMsgAfterDownload.tbIconUnCheckedMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconUnCheckedMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconUnCheckedMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkAutoShowMsgAfterDownload.tbImageBackground = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkAutoShowMsgAfterDownload.tbImageCheckedMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageCheckedMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageCheckedMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageUnCheckedMouseDown = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageUnCheckedMouseHover = Nothing
        Me.chkAutoShowMsgAfterDownload.tbImageUnCheckedMouseLeave = Nothing
        Me.chkAutoShowMsgAfterDownload.tbReadOnly = False
        Me.chkAutoShowMsgAfterDownload.tbShadow = False
        Me.chkAutoShowMsgAfterDownload.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkAutoShowMsgAfterDownload.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkAutoShowMsgAfterDownload.tbSplit = "3,3,3,3"
        Me.chkAutoShowMsgAfterDownload.tbToolTip = ""
        Me.chkAutoShowMsgAfterDownload.Text = "下载后弹框提示"
        Me.chkAutoShowMsgAfterDownload.UseVisualStyleBackColor = False
        '
        'pnlNormal
        '
        Me.pnlNormal.BackColor = System.Drawing.Color.Transparent
        Me.pnlNormal.Controls.Add(Me.cbxLog)
        Me.pnlNormal.Controls.Add(Me.lblMaxDownload)
        Me.pnlNormal.Controls.Add(Me.lblDownload)
        Me.pnlNormal.Controls.Add(Me.numDownload)
        Me.pnlNormal.Controls.Add(Me.lblClose)
        Me.pnlNormal.Controls.Add(Me.lblInstall)
        Me.pnlNormal.Controls.Add(Me.cbxLanguage)
        Me.pnlNormal.Controls.Add(Me.chkAutoShowMsgAfterDownload)
        Me.pnlNormal.Controls.Add(Me.rdbExist)
        Me.pnlNormal.Controls.Add(Me.chkIconShowUpdateNumber)
        Me.pnlNormal.Controls.Add(Me.chkAutoInstallAfterDownload)
        Me.pnlNormal.Controls.Add(Me.rdbMini)
        Me.pnlNormal.Controls.Add(Me.chkLinkIPAPackage)
        Me.pnlNormal.Controls.Add(Me.chkRemember)
        Me.pnlNormal.Controls.Add(Me.lblLanguage)
        Me.pnlNormal.Location = New System.Drawing.Point(0, 1)
        Me.pnlNormal.Name = "pnlNormal"
        Me.pnlNormal.Size = New System.Drawing.Size(390, 380)
        Me.pnlNormal.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlNormal.TabIndex = 72
        Me.pnlNormal.tbBackgroundImage = Nothing
        Me.pnlNormal.tbShowWatermark = False
        Me.pnlNormal.tbSplit = "0,0,0,0"
        Me.pnlNormal.tbWatermark = Nothing
        Me.pnlNormal.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlNormal.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'cbxLog
        '
        Me.cbxLog.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxLog.BackColor = System.Drawing.Color.Transparent
        Me.cbxLog.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxLog.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxLog.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxLog.Location = New System.Drawing.Point(34, 355)
        Me.cbxLog.Name = "cbxLog"
        Me.cbxLog.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxLog.Size = New System.Drawing.Size(104, 18)
        Me.cbxLog.TabIndex = 77
        Me.cbxLog.tbAdriftIconWhenHover = False
        Me.cbxLog.tbAutoSize = False
        Me.cbxLog.tbAutoSizeEx = True
        Me.cbxLog.tbIconChecked = CType(resources.GetObject("cbxLog.tbIconChecked"), System.Drawing.Image)
        Me.cbxLog.tbIconCheckedMouseDown = Nothing
        Me.cbxLog.tbIconCheckedMouseHover = Nothing
        Me.cbxLog.tbIconCheckedMouseLeave = Nothing
        Me.cbxLog.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxLog.tbIconHoldPlace = True
        Me.cbxLog.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxLog.tbIconIndeterminate = CType(resources.GetObject("cbxLog.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxLog.tbIconIndeterminateMouseDown = Nothing
        Me.cbxLog.tbIconIndeterminateMouseHover = Nothing
        Me.cbxLog.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxLog.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxLog.tbIconPlaceText = 1
        Me.cbxLog.tbIconUnChecked = CType(resources.GetObject("cbxLog.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxLog.tbIconUnCheckedMouseDown = Nothing
        Me.cbxLog.tbIconUnCheckedMouseHover = Nothing
        Me.cbxLog.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxLog.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxLog.tbImageBackground = Nothing
        Me.cbxLog.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxLog.tbImageCheckedMouseDown = Nothing
        Me.cbxLog.tbImageCheckedMouseHover = Nothing
        Me.cbxLog.tbImageCheckedMouseLeave = Nothing
        Me.cbxLog.tbImageUnCheckedMouseDown = Nothing
        Me.cbxLog.tbImageUnCheckedMouseHover = Nothing
        Me.cbxLog.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxLog.tbReadOnly = False
        Me.cbxLog.tbShadow = False
        Me.cbxLog.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxLog.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxLog.tbSplit = "3,3,3,3"
        Me.cbxLog.tbToolTip = ""
        Me.cbxLog.Text = "输出详细日志"
        Me.cbxLog.UseVisualStyleBackColor = False
        '
        'lblMaxDownload
        '
        Me.lblMaxDownload.AutoSize = True
        Me.lblMaxDownload.BackColor = System.Drawing.Color.Transparent
        Me.lblMaxDownload.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblMaxDownload.Location = New System.Drawing.Point(210, 212)
        Me.lblMaxDownload.Name = "lblMaxDownload"
        Me.lblMaxDownload.Size = New System.Drawing.Size(77, 12)
        Me.lblMaxDownload.TabIndex = 76
        Me.lblMaxDownload.Text = "同时下载数量"
        Me.lblMaxDownload.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblDownload
        '
        Me.lblDownload.AutoSize = True
        Me.lblDownload.BackColor = System.Drawing.Color.Transparent
        Me.lblDownload.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblDownload.Location = New System.Drawing.Point(41, 212)
        Me.lblDownload.Name = "lblDownload"
        Me.lblDownload.Size = New System.Drawing.Size(77, 12)
        Me.lblDownload.TabIndex = 75
        Me.lblDownload.Text = "同时下载数量"
        Me.lblDownload.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'numDownload
        '
        Me.numDownload.Location = New System.Drawing.Point(151, 208)
        Me.numDownload.Minimum = New Decimal(New Integer() {1, 0, 0, 0})
        Me.numDownload.Name = "numDownload"
        Me.numDownload.Size = New System.Drawing.Size(49, 21)
        Me.numDownload.TabIndex = 74
        Me.numDownload.Value = New Decimal(New Integer() {1, 0, 0, 0})
        '
        'lblClose
        '
        Me.lblClose.BackColor = System.Drawing.Color.Transparent
        Me.lblClose.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblClose.Location = New System.Drawing.Point(39, 265)
        Me.lblClose.Name = "lblClose"
        Me.lblClose.Size = New System.Drawing.Size(119, 17)
        Me.lblClose.TabIndex = 73
        Me.lblClose.Text = "退出设置"
        Me.lblClose.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblInstall
        '
        Me.lblInstall.BackColor = System.Drawing.Color.Transparent
        Me.lblInstall.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblInstall.Location = New System.Drawing.Point(34, 100)
        Me.lblInstall.Name = "lblInstall"
        Me.lblInstall.Size = New System.Drawing.Size(119, 17)
        Me.lblInstall.TabIndex = 72
        Me.lblInstall.Text = "安装设置"
        Me.lblInstall.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'chkIconShowUpdateNumber
        '
        Me.chkIconShowUpdateNumber.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkIconShowUpdateNumber.BackColor = System.Drawing.Color.Transparent
        Me.chkIconShowUpdateNumber.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkIconShowUpdateNumber.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkIconShowUpdateNumber.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkIconShowUpdateNumber.Location = New System.Drawing.Point(34, 237)
        Me.chkIconShowUpdateNumber.Name = "chkIconShowUpdateNumber"
        Me.chkIconShowUpdateNumber.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkIconShowUpdateNumber.Size = New System.Drawing.Size(200, 18)
        Me.chkIconShowUpdateNumber.TabIndex = 70
        Me.chkIconShowUpdateNumber.tbAdriftIconWhenHover = False
        Me.chkIconShowUpdateNumber.tbAutoSize = False
        Me.chkIconShowUpdateNumber.tbAutoSizeEx = True
        Me.chkIconShowUpdateNumber.tbIconChecked = CType(resources.GetObject("chkIconShowUpdateNumber.tbIconChecked"), System.Drawing.Image)
        Me.chkIconShowUpdateNumber.tbIconCheckedMouseDown = Nothing
        Me.chkIconShowUpdateNumber.tbIconCheckedMouseHover = Nothing
        Me.chkIconShowUpdateNumber.tbIconCheckedMouseLeave = Nothing
        Me.chkIconShowUpdateNumber.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkIconShowUpdateNumber.tbIconHoldPlace = True
        Me.chkIconShowUpdateNumber.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkIconShowUpdateNumber.tbIconIndeterminate = CType(resources.GetObject("chkIconShowUpdateNumber.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkIconShowUpdateNumber.tbIconIndeterminateMouseDown = Nothing
        Me.chkIconShowUpdateNumber.tbIconIndeterminateMouseHover = Nothing
        Me.chkIconShowUpdateNumber.tbIconIndeterminateMouseLeave = Nothing
        Me.chkIconShowUpdateNumber.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkIconShowUpdateNumber.tbIconPlaceText = 1
        Me.chkIconShowUpdateNumber.tbIconUnChecked = CType(resources.GetObject("chkIconShowUpdateNumber.tbIconUnChecked"), System.Drawing.Image)
        Me.chkIconShowUpdateNumber.tbIconUnCheckedMouseDown = Nothing
        Me.chkIconShowUpdateNumber.tbIconUnCheckedMouseHover = Nothing
        Me.chkIconShowUpdateNumber.tbIconUnCheckedMouseLeave = Nothing
        Me.chkIconShowUpdateNumber.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkIconShowUpdateNumber.tbImageBackground = Nothing
        Me.chkIconShowUpdateNumber.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkIconShowUpdateNumber.tbImageCheckedMouseDown = Nothing
        Me.chkIconShowUpdateNumber.tbImageCheckedMouseHover = Nothing
        Me.chkIconShowUpdateNumber.tbImageCheckedMouseLeave = Nothing
        Me.chkIconShowUpdateNumber.tbImageUnCheckedMouseDown = Nothing
        Me.chkIconShowUpdateNumber.tbImageUnCheckedMouseHover = Nothing
        Me.chkIconShowUpdateNumber.tbImageUnCheckedMouseLeave = Nothing
        Me.chkIconShowUpdateNumber.tbReadOnly = False
        Me.chkIconShowUpdateNumber.tbShadow = False
        Me.chkIconShowUpdateNumber.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkIconShowUpdateNumber.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkIconShowUpdateNumber.tbSplit = "3,3,3,3"
        Me.chkIconShowUpdateNumber.tbToolTip = ""
        Me.chkIconShowUpdateNumber.Text = "在桌面图标上显示应用更新数量"
        Me.chkIconShowUpdateNumber.UseVisualStyleBackColor = False
        '
        'pnlFolder
        '
        Me.pnlFolder.BackColor = System.Drawing.Color.Transparent
        Me.pnlFolder.Controls.Add(Me.txtiTunesFolder)
        Me.pnlFolder.Controls.Add(Me.lbliTunesFolder)
        Me.pnlFolder.Controls.Add(Me.btniTunesFolder)
        Me.pnlFolder.Controls.Add(Me.lblDownloadFolder)
        Me.pnlFolder.Controls.Add(Me.btnDownloadFolder)
        Me.pnlFolder.Controls.Add(Me.txtDownloadFolder)
        Me.pnlFolder.Controls.Add(Me.txtOtherFolder)
        Me.pnlFolder.Controls.Add(Me.lblMusicFolder)
        Me.pnlFolder.Controls.Add(Me.btnOtherFolder)
        Me.pnlFolder.Controls.Add(Me.btnMusicFolder)
        Me.pnlFolder.Controls.Add(Me.lblOtherFolder)
        Me.pnlFolder.Controls.Add(Me.txtMusicFolder)
        Me.pnlFolder.Controls.Add(Me.lblApp)
        Me.pnlFolder.Controls.Add(Me.lblRingtoneFolder)
        Me.pnlFolder.Controls.Add(Me.txtFirmware)
        Me.pnlFolder.Controls.Add(Me.txtWallpaperFolder)
        Me.pnlFolder.Controls.Add(Me.btnRingtoneFolder)
        Me.pnlFolder.Controls.Add(Me.btnFirmware)
        Me.pnlFolder.Controls.Add(Me.btnWallpaperFolder)
        Me.pnlFolder.Controls.Add(Me.lblFirmware)
        Me.pnlFolder.Controls.Add(Me.txtRingtoneFolder)
        Me.pnlFolder.Controls.Add(Me.lblWallpaperFolder)
        Me.pnlFolder.Location = New System.Drawing.Point(0, 381)
        Me.pnlFolder.Name = "pnlFolder"
        Me.pnlFolder.Size = New System.Drawing.Size(390, 380)
        Me.pnlFolder.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFolder.TabIndex = 73
        Me.pnlFolder.tbBackgroundImage = Nothing
        Me.pnlFolder.tbShowWatermark = False
        Me.pnlFolder.tbSplit = "0,0,0,0"
        Me.pnlFolder.tbWatermark = Nothing
        Me.pnlFolder.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFolder.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'txtFirmware
        '
        Me.txtFirmware.BackColor = System.Drawing.Color.White
        Me.txtFirmware.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtFirmware.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtFirmware.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtFirmware.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtFirmware.ForeColor = System.Drawing.Color.Black
        Me.txtFirmware.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtFirmware.Location = New System.Drawing.Point(88, 327)
        Me.txtFirmware.MaxLength = 256
        Me.txtFirmware.Name = "txtFirmware"
        Me.txtFirmware.Size = New System.Drawing.Size(204, 21)
        Me.txtFirmware.TabIndex = 59
        Me.txtFirmware.Tag = Nothing
        Me.txtFirmware.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtFirmware.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtFirmware.tbSelMark = True
        Me.txtFirmware.tbTextBind = ""
        Me.txtFirmware.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtFirmware.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtFirmware.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtFirmware.TextTip = ""
        '
        'btnFirmware
        '
        Me.btnFirmware.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnFirmware.BackColor = System.Drawing.Color.Transparent
        Me.btnFirmware.BindingForm = Nothing
        Me.btnFirmware.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFirmware.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnFirmware.Location = New System.Drawing.Point(298, 326)
        Me.btnFirmware.Name = "btnFirmware"
        Me.btnFirmware.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnFirmware.Selectable = True
        Me.btnFirmware.Size = New System.Drawing.Size(65, 23)
        Me.btnFirmware.TabIndex = 58
        Me.btnFirmware.tbAdriftIconWhenHover = False
        Me.btnFirmware.tbAutoSize = False
        Me.btnFirmware.tbAutoSizeEx = False
        Me.btnFirmware.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnFirmware.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnFirmware.tbBadgeNumber = 0
        Me.btnFirmware.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFirmware.tbEndEllipsis = False
        Me.btnFirmware.tbIconHoldPlace = True
        Me.btnFirmware.tbIconImage = Nothing
        Me.btnFirmware.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFirmware.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnFirmware.tbIconMore = False
        Me.btnFirmware.tbIconMouseDown = Nothing
        Me.btnFirmware.tbIconMouseHover = Nothing
        Me.btnFirmware.tbIconMouseLeave = Nothing
        Me.btnFirmware.tbIconPlaceText = 2
        Me.btnFirmware.tbIconReadOnly = Nothing
        Me.btnFirmware.tbImageMouseDown = Nothing
        Me.btnFirmware.tbImageMouseHover = Nothing
        Me.btnFirmware.tbImageMouseLeave = Nothing
        Me.btnFirmware.tbProgressValue = 50
        Me.btnFirmware.tbReadOnly = False
        Me.btnFirmware.tbReadOnlyText = False
        Me.btnFirmware.tbShadow = False
        Me.btnFirmware.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnFirmware.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnFirmware.tbShowDot = False
        Me.btnFirmware.tbShowMoreIconImg = CType(resources.GetObject("btnFirmware.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFirmware.tbShowNew = False
        Me.btnFirmware.tbShowProgress = False
        Me.btnFirmware.tbShowTip = True
        Me.btnFirmware.tbShowToolTipOnButton = False
        Me.btnFirmware.tbSplit = "13,11,13,11"
        Me.btnFirmware.tbText = "更换"
        Me.btnFirmware.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFirmware.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnFirmware.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnFirmware.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnFirmware.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnFirmware.tbTextMouseDownPlace = 0
        Me.btnFirmware.tbToolTip = ""
        Me.btnFirmware.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFirmware.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFirmware.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFirmware.VisibleEx = True
        '
        'lblFirmware
        '
        Me.lblFirmware.AutoSize = True
        Me.lblFirmware.BackColor = System.Drawing.Color.Transparent
        Me.lblFirmware.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblFirmware.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblFirmware.Location = New System.Drawing.Point(34, 331)
        Me.lblFirmware.Name = "lblFirmware"
        Me.lblFirmware.Size = New System.Drawing.Size(29, 12)
        Me.lblFirmware.TabIndex = 57
        Me.lblFirmware.Text = "固件"
        '
        'pnlLeft
        '
        Me.pnlLeft.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.pnlLeft.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlLeft.Controls.Add(Me.btnAndroidWeChat)
        Me.pnlLeft.Controls.Add(Me.btnDervicePermission)
        Me.pnlLeft.Controls.Add(Me.btnAppleID)
        Me.pnlLeft.Controls.Add(Me.btnDNS)
        Me.pnlLeft.Controls.Add(Me.btnNormal)
        Me.pnlLeft.Controls.Add(Me.btnFolder)
        Me.pnlLeft.Location = New System.Drawing.Point(1, 32)
        Me.pnlLeft.Name = "pnlLeft"
        Me.pnlLeft.Size = New System.Drawing.Size(140, 763)
        Me.pnlLeft.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLeft.TabIndex = 74
        Me.pnlLeft.tbBackgroundImage = Nothing
        Me.pnlLeft.tbShowWatermark = False
        Me.pnlLeft.tbSplit = "0,0,0,0"
        Me.pnlLeft.tbWatermark = Nothing
        Me.pnlLeft.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLeft.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnAndroidWeChat
        '
        Me.btnAndroidWeChat.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnAndroidWeChat.ForeColor_Checked = System.Drawing.Color.Black
        Me.btnAndroidWeChat.Location = New System.Drawing.Point(0, 208)
        Me.btnAndroidWeChat.Name = "btnAndroidWeChat"
        Me.btnAndroidWeChat.Padding = New System.Windows.Forms.Padding(20, 0, 0, 0)
        Me.btnAndroidWeChat.Size = New System.Drawing.Size(140, 35)
        Me.btnAndroidWeChat.TabIndex = 79
        Me.btnAndroidWeChat.tbAdriftIconWhenHover = False
        Me.btnAndroidWeChat.tbAutoSize = False
        Me.btnAndroidWeChat.tbAutoSizeEx = False
        Me.btnAndroidWeChat.tbBadgeNumber = 0
        Me.btnAndroidWeChat.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAndroidWeChat.tbEnableToolTip = False
        Me.btnAndroidWeChat.tbIconCheckedMouseDown = Nothing
        Me.btnAndroidWeChat.tbIconCheckedMouseHover = Nothing
        Me.btnAndroidWeChat.tbIconCheckedMouseLeave = Nothing
        Me.btnAndroidWeChat.tbIconHoldPlace = True
        Me.btnAndroidWeChat.tbIconImage = Nothing
        Me.btnAndroidWeChat.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAndroidWeChat.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAndroidWeChat.tbIconMore = False
        Me.btnAndroidWeChat.tbIconMoreImage = CType(resources.GetObject("btnAndroidWeChat.tbIconMoreImage"), System.Drawing.Image)
        Me.btnAndroidWeChat.tbIconPlaceText = 5
        Me.btnAndroidWeChat.tbIconReadOnly = Nothing
        Me.btnAndroidWeChat.tbIconReadOnlyLight = Nothing
        Me.btnAndroidWeChat.tbIconUnCheckedMouseDown = Nothing
        Me.btnAndroidWeChat.tbIconUnCheckedMouseHover = Nothing
        Me.btnAndroidWeChat.tbIconUnCheckedMouseLeave = Nothing
        Me.btnAndroidWeChat.tbImageBackground = Nothing
        Me.btnAndroidWeChat.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnAndroidWeChat.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnAndroidWeChat.tbImageCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnAndroidWeChat.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnAndroidWeChat.tbImageUnCheckedMouseDown = Nothing
        Me.btnAndroidWeChat.tbImageUnCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_hover
        Me.btnAndroidWeChat.tbImageUnCheckedMouseLeave = Nothing
        Me.btnAndroidWeChat.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnAndroidWeChat.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnAndroidWeChat.tbReadOnly = False
        Me.btnAndroidWeChat.tbShadow = False
        Me.btnAndroidWeChat.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAndroidWeChat.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAndroidWeChat.tbShowBadgeNumber = True
        Me.btnAndroidWeChat.tbShowBadgeNumberWhenSelection = True
        Me.btnAndroidWeChat.tbShowNew = False
        Me.btnAndroidWeChat.tbSplit = "3,3,3,3"
        Me.btnAndroidWeChat.tbTag = Nothing
        Me.btnAndroidWeChat.tbToolTip = ""
        Me.btnAndroidWeChat.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnAndroidWeChat.Text = "旧版设置"
        Me.btnAndroidWeChat.UseVisualStyleBackColor = True
        '
        'btnDervicePermission
        '
        Me.btnDervicePermission.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnDervicePermission.ForeColor_Checked = System.Drawing.Color.Black
        Me.btnDervicePermission.Location = New System.Drawing.Point(0, 168)
        Me.btnDervicePermission.Name = "btnDervicePermission"
        Me.btnDervicePermission.Padding = New System.Windows.Forms.Padding(20, 0, 0, 0)
        Me.btnDervicePermission.Size = New System.Drawing.Size(140, 35)
        Me.btnDervicePermission.TabIndex = 78
        Me.btnDervicePermission.tbAdriftIconWhenHover = False
        Me.btnDervicePermission.tbAutoSize = False
        Me.btnDervicePermission.tbAutoSizeEx = False
        Me.btnDervicePermission.tbBadgeNumber = 0
        Me.btnDervicePermission.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDervicePermission.tbEnableToolTip = False
        Me.btnDervicePermission.tbIconCheckedMouseDown = Nothing
        Me.btnDervicePermission.tbIconCheckedMouseHover = Nothing
        Me.btnDervicePermission.tbIconCheckedMouseLeave = Nothing
        Me.btnDervicePermission.tbIconHoldPlace = True
        Me.btnDervicePermission.tbIconImage = Nothing
        Me.btnDervicePermission.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDervicePermission.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDervicePermission.tbIconMore = False
        Me.btnDervicePermission.tbIconMoreImage = CType(resources.GetObject("btnDervicePermission.tbIconMoreImage"), System.Drawing.Image)
        Me.btnDervicePermission.tbIconPlaceText = 5
        Me.btnDervicePermission.tbIconReadOnly = Nothing
        Me.btnDervicePermission.tbIconReadOnlyLight = Nothing
        Me.btnDervicePermission.tbIconUnCheckedMouseDown = Nothing
        Me.btnDervicePermission.tbIconUnCheckedMouseHover = Nothing
        Me.btnDervicePermission.tbIconUnCheckedMouseLeave = Nothing
        Me.btnDervicePermission.tbImageBackground = Nothing
        Me.btnDervicePermission.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnDervicePermission.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnDervicePermission.tbImageCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnDervicePermission.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnDervicePermission.tbImageUnCheckedMouseDown = Nothing
        Me.btnDervicePermission.tbImageUnCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_hover
        Me.btnDervicePermission.tbImageUnCheckedMouseLeave = Nothing
        Me.btnDervicePermission.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnDervicePermission.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnDervicePermission.tbReadOnly = False
        Me.btnDervicePermission.tbShadow = False
        Me.btnDervicePermission.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDervicePermission.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDervicePermission.tbShowBadgeNumber = True
        Me.btnDervicePermission.tbShowBadgeNumberWhenSelection = True
        Me.btnDervicePermission.tbShowNew = False
        Me.btnDervicePermission.tbSplit = "3,3,3,3"
        Me.btnDervicePermission.tbTag = Nothing
        Me.btnDervicePermission.tbToolTip = ""
        Me.btnDervicePermission.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnDervicePermission.Text = "设备权限管理"
        Me.btnDervicePermission.UseVisualStyleBackColor = True
        '
        'btnAppleID
        '
        Me.btnAppleID.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnAppleID.ForeColor_Checked = System.Drawing.Color.Black
        Me.btnAppleID.Location = New System.Drawing.Point(0, 128)
        Me.btnAppleID.Name = "btnAppleID"
        Me.btnAppleID.Padding = New System.Windows.Forms.Padding(20, 0, 0, 0)
        Me.btnAppleID.Size = New System.Drawing.Size(140, 35)
        Me.btnAppleID.TabIndex = 77
        Me.btnAppleID.tbAdriftIconWhenHover = False
        Me.btnAppleID.tbAutoSize = False
        Me.btnAppleID.tbAutoSizeEx = False
        Me.btnAppleID.tbBadgeNumber = 0
        Me.btnAppleID.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAppleID.tbEnableToolTip = False
        Me.btnAppleID.tbIconCheckedMouseDown = Nothing
        Me.btnAppleID.tbIconCheckedMouseHover = Nothing
        Me.btnAppleID.tbIconCheckedMouseLeave = Nothing
        Me.btnAppleID.tbIconHoldPlace = True
        Me.btnAppleID.tbIconImage = Nothing
        Me.btnAppleID.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAppleID.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAppleID.tbIconMore = False
        Me.btnAppleID.tbIconMoreImage = CType(resources.GetObject("btnAppleID.tbIconMoreImage"), System.Drawing.Image)
        Me.btnAppleID.tbIconPlaceText = 5
        Me.btnAppleID.tbIconReadOnly = Nothing
        Me.btnAppleID.tbIconReadOnlyLight = Nothing
        Me.btnAppleID.tbIconUnCheckedMouseDown = Nothing
        Me.btnAppleID.tbIconUnCheckedMouseHover = Nothing
        Me.btnAppleID.tbIconUnCheckedMouseLeave = Nothing
        Me.btnAppleID.tbImageBackground = Nothing
        Me.btnAppleID.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnAppleID.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnAppleID.tbImageCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnAppleID.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnAppleID.tbImageUnCheckedMouseDown = Nothing
        Me.btnAppleID.tbImageUnCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_hover
        Me.btnAppleID.tbImageUnCheckedMouseLeave = Nothing
        Me.btnAppleID.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnAppleID.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnAppleID.tbReadOnly = False
        Me.btnAppleID.tbShadow = False
        Me.btnAppleID.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAppleID.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAppleID.tbShowBadgeNumber = True
        Me.btnAppleID.tbShowBadgeNumberWhenSelection = True
        Me.btnAppleID.tbShowNew = False
        Me.btnAppleID.tbSplit = "3,3,3,3"
        Me.btnAppleID.tbTag = Nothing
        Me.btnAppleID.tbToolTip = ""
        Me.btnAppleID.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnAppleID.Text = "Apple ID登录"
        Me.btnAppleID.UseVisualStyleBackColor = True
        '
        'btnDNS
        '
        Me.btnDNS.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnDNS.ForeColor_Checked = System.Drawing.Color.Black
        Me.btnDNS.Location = New System.Drawing.Point(0, 88)
        Me.btnDNS.Name = "btnDNS"
        Me.btnDNS.Padding = New System.Windows.Forms.Padding(20, 0, 0, 0)
        Me.btnDNS.Size = New System.Drawing.Size(140, 35)
        Me.btnDNS.TabIndex = 76
        Me.btnDNS.tbAdriftIconWhenHover = False
        Me.btnDNS.tbAutoSize = False
        Me.btnDNS.tbAutoSizeEx = False
        Me.btnDNS.tbBadgeNumber = 0
        Me.btnDNS.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDNS.tbEnableToolTip = False
        Me.btnDNS.tbIconCheckedMouseDown = Nothing
        Me.btnDNS.tbIconCheckedMouseHover = Nothing
        Me.btnDNS.tbIconCheckedMouseLeave = Nothing
        Me.btnDNS.tbIconHoldPlace = True
        Me.btnDNS.tbIconImage = Nothing
        Me.btnDNS.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDNS.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDNS.tbIconMore = False
        Me.btnDNS.tbIconMoreImage = CType(resources.GetObject("btnDNS.tbIconMoreImage"), System.Drawing.Image)
        Me.btnDNS.tbIconPlaceText = 5
        Me.btnDNS.tbIconReadOnly = Nothing
        Me.btnDNS.tbIconReadOnlyLight = Nothing
        Me.btnDNS.tbIconUnCheckedMouseDown = Nothing
        Me.btnDNS.tbIconUnCheckedMouseHover = Nothing
        Me.btnDNS.tbIconUnCheckedMouseLeave = Nothing
        Me.btnDNS.tbImageBackground = Nothing
        Me.btnDNS.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnDNS.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnDNS.tbImageCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnDNS.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnDNS.tbImageUnCheckedMouseDown = Nothing
        Me.btnDNS.tbImageUnCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_hover
        Me.btnDNS.tbImageUnCheckedMouseLeave = Nothing
        Me.btnDNS.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnDNS.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnDNS.tbReadOnly = False
        Me.btnDNS.tbShadow = False
        Me.btnDNS.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDNS.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDNS.tbShowBadgeNumber = True
        Me.btnDNS.tbShowBadgeNumberWhenSelection = True
        Me.btnDNS.tbShowNew = False
        Me.btnDNS.tbSplit = "3,3,3,3"
        Me.btnDNS.tbTag = Nothing
        Me.btnDNS.tbToolTip = ""
        Me.btnDNS.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnDNS.Text = "DNS设置"
        Me.btnDNS.UseVisualStyleBackColor = True
        '
        'btnNormal
        '
        Me.btnNormal.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnNormal.Checked = True
        Me.btnNormal.ForeColor_Checked = System.Drawing.Color.Black
        Me.btnNormal.Location = New System.Drawing.Point(0, 12)
        Me.btnNormal.Name = "btnNormal"
        Me.btnNormal.Padding = New System.Windows.Forms.Padding(20, 0, 0, 0)
        Me.btnNormal.Size = New System.Drawing.Size(140, 35)
        Me.btnNormal.TabIndex = 74
        Me.btnNormal.TabStop = True
        Me.btnNormal.tbAdriftIconWhenHover = False
        Me.btnNormal.tbAutoSize = False
        Me.btnNormal.tbAutoSizeEx = False
        Me.btnNormal.tbBadgeNumber = 0
        Me.btnNormal.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnNormal.tbEnableToolTip = False
        Me.btnNormal.tbIconCheckedMouseDown = Nothing
        Me.btnNormal.tbIconCheckedMouseHover = Nothing
        Me.btnNormal.tbIconCheckedMouseLeave = Nothing
        Me.btnNormal.tbIconHoldPlace = True
        Me.btnNormal.tbIconImage = Nothing
        Me.btnNormal.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNormal.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnNormal.tbIconMore = False
        Me.btnNormal.tbIconMoreImage = CType(resources.GetObject("btnNormal.tbIconMoreImage"), System.Drawing.Image)
        Me.btnNormal.tbIconPlaceText = 5
        Me.btnNormal.tbIconReadOnly = Nothing
        Me.btnNormal.tbIconReadOnlyLight = Nothing
        Me.btnNormal.tbIconUnCheckedMouseDown = Nothing
        Me.btnNormal.tbIconUnCheckedMouseHover = Nothing
        Me.btnNormal.tbIconUnCheckedMouseLeave = Nothing
        Me.btnNormal.tbImageBackground = Nothing
        Me.btnNormal.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnNormal.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnNormal.tbImageCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnNormal.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnNormal.tbImageUnCheckedMouseDown = Nothing
        Me.btnNormal.tbImageUnCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_hover
        Me.btnNormal.tbImageUnCheckedMouseLeave = Nothing
        Me.btnNormal.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnNormal.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnNormal.tbReadOnly = False
        Me.btnNormal.tbShadow = False
        Me.btnNormal.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnNormal.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnNormal.tbShowBadgeNumber = True
        Me.btnNormal.tbShowBadgeNumberWhenSelection = True
        Me.btnNormal.tbShowNew = False
        Me.btnNormal.tbSplit = "3,3,3,3"
        Me.btnNormal.tbTag = Nothing
        Me.btnNormal.tbToolTip = ""
        Me.btnNormal.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnNormal.Text = "基本设置"
        Me.btnNormal.UseVisualStyleBackColor = True
        '
        'btnFolder
        '
        Me.btnFolder.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnFolder.ForeColor_Checked = System.Drawing.Color.Black
        Me.btnFolder.Location = New System.Drawing.Point(0, 49)
        Me.btnFolder.Name = "btnFolder"
        Me.btnFolder.Padding = New System.Windows.Forms.Padding(20, 0, 0, 0)
        Me.btnFolder.Size = New System.Drawing.Size(140, 35)
        Me.btnFolder.TabIndex = 75
        Me.btnFolder.tbAdriftIconWhenHover = False
        Me.btnFolder.tbAutoSize = False
        Me.btnFolder.tbAutoSizeEx = False
        Me.btnFolder.tbBadgeNumber = 0
        Me.btnFolder.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFolder.tbEnableToolTip = False
        Me.btnFolder.tbIconCheckedMouseDown = Nothing
        Me.btnFolder.tbIconCheckedMouseHover = Nothing
        Me.btnFolder.tbIconCheckedMouseLeave = Nothing
        Me.btnFolder.tbIconHoldPlace = True
        Me.btnFolder.tbIconImage = Nothing
        Me.btnFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFolder.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnFolder.tbIconMore = False
        Me.btnFolder.tbIconMoreImage = CType(resources.GetObject("btnFolder.tbIconMoreImage"), System.Drawing.Image)
        Me.btnFolder.tbIconPlaceText = 5
        Me.btnFolder.tbIconReadOnly = Nothing
        Me.btnFolder.tbIconReadOnlyLight = Nothing
        Me.btnFolder.tbIconUnCheckedMouseDown = Nothing
        Me.btnFolder.tbIconUnCheckedMouseHover = Nothing
        Me.btnFolder.tbIconUnCheckedMouseLeave = Nothing
        Me.btnFolder.tbImageBackground = Nothing
        Me.btnFolder.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnFolder.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnFolder.tbImageCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnFolder.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.settingtype_selected
        Me.btnFolder.tbImageUnCheckedMouseDown = Nothing
        Me.btnFolder.tbImageUnCheckedMouseHover = Global.iTong.My.Resources.Resources.settingtype_hover
        Me.btnFolder.tbImageUnCheckedMouseLeave = Nothing
        Me.btnFolder.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnFolder.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnFolder.tbReadOnly = False
        Me.btnFolder.tbShadow = False
        Me.btnFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFolder.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFolder.tbShowBadgeNumber = True
        Me.btnFolder.tbShowBadgeNumberWhenSelection = True
        Me.btnFolder.tbShowNew = False
        Me.btnFolder.tbSplit = "3,3,3,3"
        Me.btnFolder.tbTag = Nothing
        Me.btnFolder.tbToolTip = ""
        Me.btnFolder.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnFolder.Text = "目录设置"
        Me.btnFolder.UseVisualStyleBackColor = True
        '
        'pnlRight
        '
        Me.pnlRight.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlRight.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlRight.Controls.Add(Me.pnlNormal)
        Me.pnlRight.Controls.Add(Me.pnlFolder)
        Me.pnlRight.Controls.Add(Me.pnlDNS)
        Me.pnlRight.Controls.Add(Me.pnlAppleID)
        Me.pnlRight.Controls.Add(Me.pnlDervicePermission)
        Me.pnlRight.Controls.Add(Me.pnlAndroidWeChat)
        Me.pnlRight.Location = New System.Drawing.Point(141, 32)
        Me.pnlRight.Name = "pnlRight"
        Me.pnlRight.Size = New System.Drawing.Size(1232, 763)
        Me.pnlRight.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlRight.TabIndex = 75
        Me.pnlRight.tbBackgroundImage = Nothing
        Me.pnlRight.tbShowWatermark = False
        Me.pnlRight.tbSplit = "0,0,0,0"
        Me.pnlRight.tbWatermark = Nothing
        Me.pnlRight.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlRight.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlDNS
        '
        Me.pnlDNS.BackColor = System.Drawing.Color.Transparent
        Me.pnlDNS.Controls.Add(Me.btnSetUserDNS)
        Me.pnlDNS.Controls.Add(Me.btnSetDefaultDNS)
        Me.pnlDNS.Controls.Add(Me.txtDNS2)
        Me.pnlDNS.Controls.Add(Me.lblDNS2)
        Me.pnlDNS.Controls.Add(Me.txtDNS1)
        Me.pnlDNS.Controls.Add(Me.lblDNS1)
        Me.pnlDNS.Location = New System.Drawing.Point(391, 1)
        Me.pnlDNS.Name = "pnlDNS"
        Me.pnlDNS.Size = New System.Drawing.Size(390, 380)
        Me.pnlDNS.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDNS.TabIndex = 74
        Me.pnlDNS.tbBackgroundImage = Nothing
        Me.pnlDNS.tbShowWatermark = False
        Me.pnlDNS.tbSplit = "0,0,0,0"
        Me.pnlDNS.tbWatermark = Nothing
        Me.pnlDNS.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDNS.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnSetUserDNS
        '
        Me.btnSetUserDNS.BackColor = System.Drawing.Color.Transparent
        Me.btnSetUserDNS.BindingForm = Nothing
        Me.btnSetUserDNS.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSetUserDNS.Location = New System.Drawing.Point(243, 126)
        Me.btnSetUserDNS.Name = "btnSetUserDNS"
        Me.btnSetUserDNS.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnSetUserDNS.Selectable = True
        Me.btnSetUserDNS.Size = New System.Drawing.Size(103, 23)
        Me.btnSetUserDNS.TabIndex = 50
        Me.btnSetUserDNS.tbAdriftIconWhenHover = False
        Me.btnSetUserDNS.tbAutoSize = False
        Me.btnSetUserDNS.tbAutoSizeEx = False
        Me.btnSetUserDNS.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnSetUserDNS.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSetUserDNS.tbBadgeNumber = 0
        Me.btnSetUserDNS.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSetUserDNS.tbEndEllipsis = False
        Me.btnSetUserDNS.tbIconHoldPlace = True
        Me.btnSetUserDNS.tbIconImage = Nothing
        Me.btnSetUserDNS.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetUserDNS.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSetUserDNS.tbIconMore = False
        Me.btnSetUserDNS.tbIconMouseDown = Nothing
        Me.btnSetUserDNS.tbIconMouseHover = Nothing
        Me.btnSetUserDNS.tbIconMouseLeave = Nothing
        Me.btnSetUserDNS.tbIconPlaceText = 2
        Me.btnSetUserDNS.tbIconReadOnly = Nothing
        Me.btnSetUserDNS.tbImageMouseDown = Nothing
        Me.btnSetUserDNS.tbImageMouseHover = Nothing
        Me.btnSetUserDNS.tbImageMouseLeave = Nothing
        Me.btnSetUserDNS.tbProgressValue = 50
        Me.btnSetUserDNS.tbReadOnly = False
        Me.btnSetUserDNS.tbReadOnlyText = False
        Me.btnSetUserDNS.tbShadow = False
        Me.btnSetUserDNS.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnSetUserDNS.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnSetUserDNS.tbShowDot = False
        Me.btnSetUserDNS.tbShowMoreIconImg = CType(resources.GetObject("btnSetUserDNS.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSetUserDNS.tbShowNew = False
        Me.btnSetUserDNS.tbShowProgress = False
        Me.btnSetUserDNS.tbShowTip = True
        Me.btnSetUserDNS.tbShowToolTipOnButton = False
        Me.btnSetUserDNS.tbSplit = "13,11,13,11"
        Me.btnSetUserDNS.tbText = "恢复默认DNS"
        Me.btnSetUserDNS.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetUserDNS.tbTextColor = System.Drawing.Color.White
        Me.btnSetUserDNS.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSetUserDNS.tbTextColorDown = System.Drawing.Color.White
        Me.btnSetUserDNS.tbTextColorHover = System.Drawing.Color.White
        Me.btnSetUserDNS.tbTextMouseDownPlace = 0
        Me.btnSetUserDNS.tbToolTip = ""
        Me.btnSetUserDNS.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSetUserDNS.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSetUserDNS.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetUserDNS.VisibleEx = True
        '
        'btnSetDefaultDNS
        '
        Me.btnSetDefaultDNS.BackColor = System.Drawing.Color.Transparent
        Me.btnSetDefaultDNS.BindingForm = Nothing
        Me.btnSetDefaultDNS.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSetDefaultDNS.Location = New System.Drawing.Point(126, 126)
        Me.btnSetDefaultDNS.Name = "btnSetDefaultDNS"
        Me.btnSetDefaultDNS.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnSetDefaultDNS.Selectable = True
        Me.btnSetDefaultDNS.Size = New System.Drawing.Size(103, 23)
        Me.btnSetDefaultDNS.TabIndex = 49
        Me.btnSetDefaultDNS.tbAdriftIconWhenHover = False
        Me.btnSetDefaultDNS.tbAutoSize = False
        Me.btnSetDefaultDNS.tbAutoSizeEx = False
        Me.btnSetDefaultDNS.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnSetDefaultDNS.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSetDefaultDNS.tbBadgeNumber = 0
        Me.btnSetDefaultDNS.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSetDefaultDNS.tbEndEllipsis = False
        Me.btnSetDefaultDNS.tbIconHoldPlace = True
        Me.btnSetDefaultDNS.tbIconImage = Nothing
        Me.btnSetDefaultDNS.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetDefaultDNS.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSetDefaultDNS.tbIconMore = False
        Me.btnSetDefaultDNS.tbIconMouseDown = Nothing
        Me.btnSetDefaultDNS.tbIconMouseHover = Nothing
        Me.btnSetDefaultDNS.tbIconMouseLeave = Nothing
        Me.btnSetDefaultDNS.tbIconPlaceText = 2
        Me.btnSetDefaultDNS.tbIconReadOnly = Nothing
        Me.btnSetDefaultDNS.tbImageMouseDown = Nothing
        Me.btnSetDefaultDNS.tbImageMouseHover = Nothing
        Me.btnSetDefaultDNS.tbImageMouseLeave = Nothing
        Me.btnSetDefaultDNS.tbProgressValue = 50
        Me.btnSetDefaultDNS.tbReadOnly = False
        Me.btnSetDefaultDNS.tbReadOnlyText = False
        Me.btnSetDefaultDNS.tbShadow = False
        Me.btnSetDefaultDNS.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnSetDefaultDNS.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnSetDefaultDNS.tbShowDot = False
        Me.btnSetDefaultDNS.tbShowMoreIconImg = CType(resources.GetObject("btnSetDefaultDNS.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSetDefaultDNS.tbShowNew = False
        Me.btnSetDefaultDNS.tbShowProgress = False
        Me.btnSetDefaultDNS.tbShowTip = True
        Me.btnSetDefaultDNS.tbShowToolTipOnButton = False
        Me.btnSetDefaultDNS.tbSplit = "13,11,13,11"
        Me.btnSetDefaultDNS.tbText = "设置通用DNS"
        Me.btnSetDefaultDNS.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetDefaultDNS.tbTextColor = System.Drawing.Color.White
        Me.btnSetDefaultDNS.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSetDefaultDNS.tbTextColorDown = System.Drawing.Color.White
        Me.btnSetDefaultDNS.tbTextColorHover = System.Drawing.Color.White
        Me.btnSetDefaultDNS.tbTextMouseDownPlace = 0
        Me.btnSetDefaultDNS.tbToolTip = ""
        Me.btnSetDefaultDNS.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSetDefaultDNS.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSetDefaultDNS.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetDefaultDNS.VisibleEx = True
        '
        'txtDNS2
        '
        Me.txtDNS2.BackColor = System.Drawing.Color.Gainsboro
        Me.txtDNS2.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtDNS2.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtDNS2.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtDNS2.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtDNS2.ForeColor = System.Drawing.Color.Black
        Me.txtDNS2.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDNS2.Location = New System.Drawing.Point(126, 69)
        Me.txtDNS2.MaxLength = 256
        Me.txtDNS2.Name = "txtDNS2"
        Me.txtDNS2.ReadOnly = True
        Me.txtDNS2.Size = New System.Drawing.Size(221, 21)
        Me.txtDNS2.TabIndex = 48
        Me.txtDNS2.TabStop = False
        Me.txtDNS2.Tag = Nothing
        Me.txtDNS2.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtDNS2.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.[ReadOnly]
        Me.txtDNS2.tbSelMark = True
        Me.txtDNS2.tbTextBind = ""
        Me.txtDNS2.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtDNS2.TextImeMode = System.Windows.Forms.ImeMode.Disable
        Me.txtDNS2.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtDNS2.TextTip = ""
        '
        'lblDNS2
        '
        Me.lblDNS2.BackColor = System.Drawing.Color.Transparent
        Me.lblDNS2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDNS2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblDNS2.Location = New System.Drawing.Point(12, 69)
        Me.lblDNS2.Name = "lblDNS2"
        Me.lblDNS2.Size = New System.Drawing.Size(111, 21)
        Me.lblDNS2.TabIndex = 47
        Me.lblDNS2.Text = "备选DNS服务器"
        Me.lblDNS2.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'txtDNS1
        '
        Me.txtDNS1.BackColor = System.Drawing.Color.Gainsboro
        Me.txtDNS1.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtDNS1.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtDNS1.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtDNS1.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtDNS1.ForeColor = System.Drawing.Color.Black
        Me.txtDNS1.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDNS1.Location = New System.Drawing.Point(126, 28)
        Me.txtDNS1.MaxLength = 256
        Me.txtDNS1.Name = "txtDNS1"
        Me.txtDNS1.ReadOnly = True
        Me.txtDNS1.Size = New System.Drawing.Size(221, 21)
        Me.txtDNS1.TabIndex = 46
        Me.txtDNS1.TabStop = False
        Me.txtDNS1.Tag = Nothing
        Me.txtDNS1.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtDNS1.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.[ReadOnly]
        Me.txtDNS1.tbSelMark = True
        Me.txtDNS1.tbTextBind = ""
        Me.txtDNS1.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtDNS1.TextImeMode = System.Windows.Forms.ImeMode.Disable
        Me.txtDNS1.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtDNS1.TextTip = ""
        '
        'lblDNS1
        '
        Me.lblDNS1.BackColor = System.Drawing.Color.Transparent
        Me.lblDNS1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDNS1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblDNS1.Location = New System.Drawing.Point(12, 28)
        Me.lblDNS1.Name = "lblDNS1"
        Me.lblDNS1.Size = New System.Drawing.Size(111, 21)
        Me.lblDNS1.TabIndex = 29
        Me.lblDNS1.Text = "首选DNS服务器"
        Me.lblDNS1.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'pnlAppleID
        '
        Me.pnlAppleID.BackColor = System.Drawing.Color.Transparent
        Me.pnlAppleID.Controls.Add(Me.chkLoginCode)
        Me.pnlAppleID.Controls.Add(Me.lblLoginApplids)
        Me.pnlAppleID.Controls.Add(Me.lblAppleIdMessage)
        Me.pnlAppleID.Controls.Add(Me.pbLoading)
        Me.pnlAppleID.Controls.Add(Me.picPwd)
        Me.pnlAppleID.Controls.Add(Me.picId)
        Me.pnlAppleID.Controls.Add(Me.picCheck)
        Me.pnlAppleID.Controls.Add(Me.txtPwd)
        Me.pnlAppleID.Controls.Add(Me.txtId)
        Me.pnlAppleID.Controls.Add(Me.pnlAppleIDGrid)
        Me.pnlAppleID.Controls.Add(Me.lblRegister)
        Me.pnlAppleID.Controls.Add(Me.btnLoginAppleId)
        Me.pnlAppleID.Controls.Add(Me.lblApplePwd)
        Me.pnlAppleID.Controls.Add(Me.lblAppleID)
        Me.pnlAppleID.Controls.Add(Me.chkAgree)
        Me.pnlAppleID.Controls.Add(Me.lblClause)
        Me.pnlAppleID.Location = New System.Drawing.Point(391, 381)
        Me.pnlAppleID.Name = "pnlAppleID"
        Me.pnlAppleID.Size = New System.Drawing.Size(390, 380)
        Me.pnlAppleID.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlAppleID.TabIndex = 75
        Me.pnlAppleID.tbBackgroundImage = Nothing
        Me.pnlAppleID.tbShowWatermark = False
        Me.pnlAppleID.tbSplit = "0,0,0,0"
        Me.pnlAppleID.tbWatermark = Nothing
        Me.pnlAppleID.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlAppleID.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'chkLoginCode
        '
        Me.chkLoginCode.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.chkLoginCode.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkLoginCode.BackColor = System.Drawing.Color.Transparent
        Me.chkLoginCode.Checked = True
        Me.chkLoginCode.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkLoginCode.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkLoginCode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkLoginCode.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkLoginCode.Location = New System.Drawing.Point(99, 169)
        Me.chkLoginCode.Margin = New System.Windows.Forms.Padding(0)
        Me.chkLoginCode.Name = "chkLoginCode"
        Me.chkLoginCode.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkLoginCode.Size = New System.Drawing.Size(92, 23)
        Me.chkLoginCode.TabIndex = 75
        Me.chkLoginCode.tbAdriftIconWhenHover = True
        Me.chkLoginCode.tbAutoSize = False
        Me.chkLoginCode.tbAutoSizeEx = True
        Me.chkLoginCode.tbIconChecked = CType(resources.GetObject("chkLoginCode.tbIconChecked"), System.Drawing.Image)
        Me.chkLoginCode.tbIconCheckedMouseDown = Nothing
        Me.chkLoginCode.tbIconCheckedMouseHover = Nothing
        Me.chkLoginCode.tbIconCheckedMouseLeave = Nothing
        Me.chkLoginCode.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkLoginCode.tbIconHoldPlace = True
        Me.chkLoginCode.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkLoginCode.tbIconIndeterminate = CType(resources.GetObject("chkLoginCode.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkLoginCode.tbIconIndeterminateMouseDown = Nothing
        Me.chkLoginCode.tbIconIndeterminateMouseHover = Nothing
        Me.chkLoginCode.tbIconIndeterminateMouseLeave = Nothing
        Me.chkLoginCode.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkLoginCode.tbIconPlaceText = 1
        Me.chkLoginCode.tbIconUnChecked = CType(resources.GetObject("chkLoginCode.tbIconUnChecked"), System.Drawing.Image)
        Me.chkLoginCode.tbIconUnCheckedMouseDown = Nothing
        Me.chkLoginCode.tbIconUnCheckedMouseHover = Nothing
        Me.chkLoginCode.tbIconUnCheckedMouseLeave = Nothing
        Me.chkLoginCode.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkLoginCode.tbImageBackground = Nothing
        Me.chkLoginCode.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkLoginCode.tbImageCheckedMouseDown = Nothing
        Me.chkLoginCode.tbImageCheckedMouseHover = Nothing
        Me.chkLoginCode.tbImageCheckedMouseLeave = Nothing
        Me.chkLoginCode.tbImageUnCheckedMouseDown = Nothing
        Me.chkLoginCode.tbImageUnCheckedMouseHover = Nothing
        Me.chkLoginCode.tbImageUnCheckedMouseLeave = Nothing
        Me.chkLoginCode.tbReadOnly = False
        Me.chkLoginCode.tbShadow = False
        Me.chkLoginCode.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkLoginCode.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkLoginCode.tbSplit = "3,3,3,3"
        Me.chkLoginCode.tbToolTip = "33333"
        Me.chkLoginCode.Text = "开启验证码"
        Me.chkLoginCode.UseVisualStyleBackColor = False
        '
        'lblLoginApplids
        '
        Me.lblLoginApplids.AutoSize = True
        Me.lblLoginApplids.BackColor = System.Drawing.Color.Transparent
        Me.lblLoginApplids.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoginApplids.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblLoginApplids.Location = New System.Drawing.Point(8, 204)
        Me.lblLoginApplids.Name = "lblLoginApplids"
        Me.lblLoginApplids.Size = New System.Drawing.Size(101, 12)
        Me.lblLoginApplids.TabIndex = 74
        Me.lblLoginApplids.Text = "已登录的Apple ID"
        '
        'lblAppleIdMessage
        '
        Me.lblAppleIdMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblAppleIdMessage.BindingForm = Nothing
        Me.lblAppleIdMessage.Location = New System.Drawing.Point(102, 82)
        Me.lblAppleIdMessage.Margin = New System.Windows.Forms.Padding(0)
        Me.lblAppleIdMessage.Name = "lblAppleIdMessage"
        Me.lblAppleIdMessage.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.lblAppleIdMessage.Selectable = True
        Me.lblAppleIdMessage.Size = New System.Drawing.Size(269, 23)
        Me.lblAppleIdMessage.TabIndex = 73
        Me.lblAppleIdMessage.tbAdriftIconWhenHover = False
        Me.lblAppleIdMessage.tbAutoSize = False
        Me.lblAppleIdMessage.tbAutoSizeEx = False
        Me.lblAppleIdMessage.tbBackgroundImage = Nothing
        Me.lblAppleIdMessage.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.lblAppleIdMessage.tbBadgeNumber = 0
        Me.lblAppleIdMessage.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.lblAppleIdMessage.tbEndEllipsis = True
        Me.lblAppleIdMessage.tbIconHoldPlace = True
        Me.lblAppleIdMessage.tbIconImage = Nothing
        Me.lblAppleIdMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblAppleIdMessage.tbIconImageState = iTong.Components.ImageState.OneState
        Me.lblAppleIdMessage.tbIconMore = False
        Me.lblAppleIdMessage.tbIconMouseDown = Nothing
        Me.lblAppleIdMessage.tbIconMouseHover = Nothing
        Me.lblAppleIdMessage.tbIconMouseLeave = Nothing
        Me.lblAppleIdMessage.tbIconPlaceText = 0
        Me.lblAppleIdMessage.tbIconReadOnly = Nothing
        Me.lblAppleIdMessage.tbImageMouseDown = Nothing
        Me.lblAppleIdMessage.tbImageMouseHover = Nothing
        Me.lblAppleIdMessage.tbImageMouseLeave = Nothing
        Me.lblAppleIdMessage.tbProgressValue = 50
        Me.lblAppleIdMessage.tbReadOnly = False
        Me.lblAppleIdMessage.tbReadOnlyText = False
        Me.lblAppleIdMessage.tbShadow = False
        Me.lblAppleIdMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblAppleIdMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblAppleIdMessage.tbShowDot = False
        Me.lblAppleIdMessage.tbShowMoreIconImg = CType(resources.GetObject("lblAppleIdMessage.tbShowMoreIconImg"), System.Drawing.Image)
        Me.lblAppleIdMessage.tbShowNew = False
        Me.lblAppleIdMessage.tbShowProgress = False
        Me.lblAppleIdMessage.tbShowTip = True
        Me.lblAppleIdMessage.tbShowToolTipOnButton = False
        Me.lblAppleIdMessage.tbSplit = "3,3,3,3"
        Me.lblAppleIdMessage.tbText = ""
        Me.lblAppleIdMessage.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblAppleIdMessage.tbTextColor = System.Drawing.Color.White
        Me.lblAppleIdMessage.tbTextColorDisable = System.Drawing.Color.White
        Me.lblAppleIdMessage.tbTextColorDown = System.Drawing.Color.White
        Me.lblAppleIdMessage.tbTextColorHover = System.Drawing.Color.White
        Me.lblAppleIdMessage.tbTextMouseDownPlace = 0
        Me.lblAppleIdMessage.tbToolTip = ""
        Me.lblAppleIdMessage.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.lblAppleIdMessage.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.lblAppleIdMessage.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblAppleIdMessage.VisibleEx = True
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.BackColor = System.Drawing.Color.Transparent
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_16
        Me.pbLoading.Location = New System.Drawing.Point(288, 113)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(16, 16)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.pbLoading.TabIndex = 72
        Me.pbLoading.TabStop = False
        Me.pbLoading.Visible = False
        '
        'picPwd
        '
        Me.picPwd.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picPwd.Image = Global.iTong.My.Resources.Resources.dgv_filenoexist
        Me.picPwd.Location = New System.Drawing.Point(87, 64)
        Me.picPwd.Name = "picPwd"
        Me.picPwd.Size = New System.Drawing.Size(12, 12)
        Me.picPwd.TabIndex = 71
        Me.picPwd.TabStop = False
        Me.picPwd.Visible = False
        '
        'picId
        '
        Me.picId.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picId.Image = Global.iTong.My.Resources.Resources.dgv_filenoexist
        Me.picId.Location = New System.Drawing.Point(87, 32)
        Me.picId.Name = "picId"
        Me.picId.Size = New System.Drawing.Size(12, 12)
        Me.picId.TabIndex = 70
        Me.picId.TabStop = False
        Me.picId.Visible = False
        '
        'picCheck
        '
        Me.picCheck.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picCheck.Image = Global.iTong.My.Resources.Resources.dgv_filenoexist
        Me.picCheck.Location = New System.Drawing.Point(87, 147)
        Me.picCheck.Name = "picCheck"
        Me.picCheck.Size = New System.Drawing.Size(12, 12)
        Me.picCheck.TabIndex = 69
        Me.picCheck.TabStop = False
        Me.picCheck.Visible = False
        '
        'txtPwd
        '
        Me.txtPwd.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtPwd.BackColor = System.Drawing.Color.White
        Me.txtPwd.BorderColor = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtPwd.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtPwd.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtPwd.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtPwd.ForeColor = System.Drawing.Color.Black
        Me.txtPwd.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtPwd.Location = New System.Drawing.Point(102, 60)
        Me.txtPwd.Margin = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtPwd.MaxLength = 50
        Me.txtPwd.Name = "txtPwd"
        Me.txtPwd.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtPwd.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.txtPwd.Size = New System.Drawing.Size(181, 21)
        Me.txtPwd.TabIndex = 68
        Me.txtPwd.Tag = Nothing
        Me.txtPwd.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtPwd.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtPwd.tbSelMark = True
        Me.txtPwd.tbTextBind = ""
        Me.txtPwd.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtPwd.TextImeMode = System.Windows.Forms.ImeMode.Disable
        Me.txtPwd.TextPadding = New System.Windows.Forms.Padding(10, 3, 3, 3)
        Me.txtPwd.TextTip = ""
        '
        'txtId
        '
        Me.txtId.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtId.BackColor = System.Drawing.Color.White
        Me.txtId.BorderColor = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtId.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtId.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(21, Byte), Integer), CType(CType(151, Byte), Integer), CType(CType(217, Byte), Integer))
        Me.txtId.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtId.ForeColor = System.Drawing.Color.Black
        Me.txtId.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtId.Location = New System.Drawing.Point(102, 28)
        Me.txtId.Margin = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtId.MaxLength = 50
        Me.txtId.Name = "txtId"
        Me.txtId.Padding = New System.Windows.Forms.Padding(10, 0, 0, 0)
        Me.txtId.Size = New System.Drawing.Size(181, 21)
        Me.txtId.TabIndex = 67
        Me.txtId.Tag = Nothing
        Me.txtId.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtId.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtId.tbSelMark = True
        Me.txtId.tbTextBind = ""
        Me.txtId.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtId.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtId.TextPadding = New System.Windows.Forms.Padding(10, 3, 3, 3)
        Me.txtId.TextTip = ""
        '
        'pnlAppleIDGrid
        '
        Me.pnlAppleIDGrid.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlAppleIDGrid.BackColor = System.Drawing.Color.LightGray
        Me.pnlAppleIDGrid.Location = New System.Drawing.Point(5, 225)
        Me.pnlAppleIDGrid.Name = "pnlAppleIDGrid"
        Me.pnlAppleIDGrid.Padding = New System.Windows.Forms.Padding(1)
        Me.pnlAppleIDGrid.Size = New System.Drawing.Size(380, 150)
        Me.pnlAppleIDGrid.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlAppleIDGrid.TabIndex = 66
        Me.pnlAppleIDGrid.tbBackgroundImage = Nothing
        Me.pnlAppleIDGrid.tbShowWatermark = False
        Me.pnlAppleIDGrid.tbSplit = "0,0,0,0"
        Me.pnlAppleIDGrid.tbWatermark = Nothing
        Me.pnlAppleIDGrid.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlAppleIDGrid.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblRegister
        '
        Me.lblRegister.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblRegister.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblRegister.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblRegister.Location = New System.Drawing.Point(293, 25)
        Me.lblRegister.Name = "lblRegister"
        Me.lblRegister.Size = New System.Drawing.Size(85, 26)
        Me.lblRegister.TabIndex = 63
        Me.lblRegister.tbAdriftWhenHover = False
        Me.lblRegister.tbAutoEllipsis = False
        Me.lblRegister.tbAutoSize = False
        Me.lblRegister.tbHideImage = False
        Me.lblRegister.tbIconImage = Nothing
        Me.lblRegister.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblRegister.tbIconPlaceText = 5
        Me.lblRegister.tbShadow = False
        Me.lblRegister.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblRegister.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblRegister.tbShowScrolling = False
        Me.lblRegister.Text = "免费注册"
        Me.lblRegister.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnLoginAppleId
        '
        Me.btnLoginAppleId.BackColor = System.Drawing.Color.Transparent
        Me.btnLoginAppleId.BindingForm = Nothing
        Me.btnLoginAppleId.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnLoginAppleId.Location = New System.Drawing.Point(102, 108)
        Me.btnLoginAppleId.Name = "btnLoginAppleId"
        Me.btnLoginAppleId.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnLoginAppleId.Selectable = True
        Me.btnLoginAppleId.Size = New System.Drawing.Size(181, 27)
        Me.btnLoginAppleId.TabIndex = 49
        Me.btnLoginAppleId.tbAdriftIconWhenHover = False
        Me.btnLoginAppleId.tbAutoSize = False
        Me.btnLoginAppleId.tbAutoSizeEx = False
        Me.btnLoginAppleId.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnLoginAppleId.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnLoginAppleId.tbBadgeNumber = 0
        Me.btnLoginAppleId.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnLoginAppleId.tbEndEllipsis = False
        Me.btnLoginAppleId.tbIconHoldPlace = True
        Me.btnLoginAppleId.tbIconImage = Nothing
        Me.btnLoginAppleId.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLoginAppleId.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnLoginAppleId.tbIconMore = False
        Me.btnLoginAppleId.tbIconMouseDown = Nothing
        Me.btnLoginAppleId.tbIconMouseHover = Nothing
        Me.btnLoginAppleId.tbIconMouseLeave = Nothing
        Me.btnLoginAppleId.tbIconPlaceText = 2
        Me.btnLoginAppleId.tbIconReadOnly = Nothing
        Me.btnLoginAppleId.tbImageMouseDown = Nothing
        Me.btnLoginAppleId.tbImageMouseHover = Nothing
        Me.btnLoginAppleId.tbImageMouseLeave = Nothing
        Me.btnLoginAppleId.tbProgressValue = 50
        Me.btnLoginAppleId.tbReadOnly = False
        Me.btnLoginAppleId.tbReadOnlyText = False
        Me.btnLoginAppleId.tbShadow = False
        Me.btnLoginAppleId.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnLoginAppleId.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnLoginAppleId.tbShowDot = False
        Me.btnLoginAppleId.tbShowMoreIconImg = CType(resources.GetObject("btnLoginAppleId.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnLoginAppleId.tbShowNew = False
        Me.btnLoginAppleId.tbShowProgress = False
        Me.btnLoginAppleId.tbShowTip = True
        Me.btnLoginAppleId.tbShowToolTipOnButton = False
        Me.btnLoginAppleId.tbSplit = "13,11,13,11"
        Me.btnLoginAppleId.tbText = "登录Apple ID"
        Me.btnLoginAppleId.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLoginAppleId.tbTextColor = System.Drawing.Color.White
        Me.btnLoginAppleId.tbTextColorDisable = System.Drawing.Color.White
        Me.btnLoginAppleId.tbTextColorDown = System.Drawing.Color.White
        Me.btnLoginAppleId.tbTextColorHover = System.Drawing.Color.White
        Me.btnLoginAppleId.tbTextMouseDownPlace = 0
        Me.btnLoginAppleId.tbToolTip = ""
        Me.btnLoginAppleId.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnLoginAppleId.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnLoginAppleId.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLoginAppleId.VisibleEx = True
        '
        'lblApplePwd
        '
        Me.lblApplePwd.BackColor = System.Drawing.Color.Transparent
        Me.lblApplePwd.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblApplePwd.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblApplePwd.Location = New System.Drawing.Point(3, 60)
        Me.lblApplePwd.Name = "lblApplePwd"
        Me.lblApplePwd.Size = New System.Drawing.Size(81, 21)
        Me.lblApplePwd.TabIndex = 47
        Me.lblApplePwd.Text = "密码"
        Me.lblApplePwd.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblAppleID
        '
        Me.lblAppleID.BackColor = System.Drawing.Color.Transparent
        Me.lblAppleID.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblAppleID.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblAppleID.Location = New System.Drawing.Point(3, 28)
        Me.lblAppleID.Name = "lblAppleID"
        Me.lblAppleID.Size = New System.Drawing.Size(81, 21)
        Me.lblAppleID.TabIndex = 29
        Me.lblAppleID.Text = "Apple ID"
        Me.lblAppleID.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'chkAgree
        '
        Me.chkAgree.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.chkAgree.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkAgree.AutoSize = True
        Me.chkAgree.BackColor = System.Drawing.Color.Transparent
        Me.chkAgree.Checked = True
        Me.chkAgree.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkAgree.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkAgree.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkAgree.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkAgree.Location = New System.Drawing.Point(99, 140)
        Me.chkAgree.Margin = New System.Windows.Forms.Padding(0)
        Me.chkAgree.Name = "chkAgree"
        Me.chkAgree.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkAgree.Size = New System.Drawing.Size(116, 23)
        Me.chkAgree.TabIndex = 64
        Me.chkAgree.tbAdriftIconWhenHover = False
        Me.chkAgree.tbAutoSize = True
        Me.chkAgree.tbAutoSizeEx = True
        Me.chkAgree.tbIconChecked = CType(resources.GetObject("chkAgree.tbIconChecked"), System.Drawing.Image)
        Me.chkAgree.tbIconCheckedMouseDown = Nothing
        Me.chkAgree.tbIconCheckedMouseHover = Nothing
        Me.chkAgree.tbIconCheckedMouseLeave = Nothing
        Me.chkAgree.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkAgree.tbIconHoldPlace = True
        Me.chkAgree.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkAgree.tbIconIndeterminate = CType(resources.GetObject("chkAgree.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkAgree.tbIconIndeterminateMouseDown = Nothing
        Me.chkAgree.tbIconIndeterminateMouseHover = Nothing
        Me.chkAgree.tbIconIndeterminateMouseLeave = Nothing
        Me.chkAgree.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkAgree.tbIconPlaceText = 1
        Me.chkAgree.tbIconUnChecked = CType(resources.GetObject("chkAgree.tbIconUnChecked"), System.Drawing.Image)
        Me.chkAgree.tbIconUnCheckedMouseDown = Nothing
        Me.chkAgree.tbIconUnCheckedMouseHover = Nothing
        Me.chkAgree.tbIconUnCheckedMouseLeave = Nothing
        Me.chkAgree.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkAgree.tbImageBackground = Nothing
        Me.chkAgree.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkAgree.tbImageCheckedMouseDown = Nothing
        Me.chkAgree.tbImageCheckedMouseHover = Nothing
        Me.chkAgree.tbImageCheckedMouseLeave = Nothing
        Me.chkAgree.tbImageUnCheckedMouseDown = Nothing
        Me.chkAgree.tbImageUnCheckedMouseHover = Nothing
        Me.chkAgree.tbImageUnCheckedMouseLeave = Nothing
        Me.chkAgree.tbReadOnly = False
        Me.chkAgree.tbShadow = False
        Me.chkAgree.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkAgree.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkAgree.tbSplit = "3,3,3,3"
        Me.chkAgree.tbToolTip = "33333"
        Me.chkAgree.Text = "已经阅读并同意"
        Me.chkAgree.UseVisualStyleBackColor = False
        '
        'lblClause
        '
        Me.lblClause.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblClause.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblClause.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblClause.Location = New System.Drawing.Point(223, 144)
        Me.lblClause.Name = "lblClause"
        Me.lblClause.Size = New System.Drawing.Size(165, 17)
        Me.lblClause.TabIndex = 65
        Me.lblClause.tbAdriftWhenHover = False
        Me.lblClause.tbAutoEllipsis = False
        Me.lblClause.tbAutoSize = False
        Me.lblClause.tbHideImage = False
        Me.lblClause.tbIconImage = Nothing
        Me.lblClause.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblClause.tbIconPlaceText = 5
        Me.lblClause.tbShadow = False
        Me.lblClause.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblClause.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblClause.tbShowScrolling = False
        Me.lblClause.Text = "隐私条款"
        Me.lblClause.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlDervicePermission
        '
        Me.pnlDervicePermission.AutoSize = True
        Me.pnlDervicePermission.BackColor = System.Drawing.Color.Transparent
        Me.pnlDervicePermission.Controls.Add(Me.btnSplit)
        Me.pnlDervicePermission.Controls.Add(Me.cbxPermitInstallTui)
        Me.pnlDervicePermission.Controls.Add(Me.cbxDervicePermission)
        Me.pnlDervicePermission.Controls.Add(Me.lblPermissionInstallTui)
        Me.pnlDervicePermission.Controls.Add(Me.lblDervicePermission)
        Me.pnlDervicePermission.Controls.Add(Me.lblPermissionIntallTuiMsg)
        Me.pnlDervicePermission.Controls.Add(Me.lblDervicePermissionMsg)
        Me.pnlDervicePermission.Location = New System.Drawing.Point(783, 1)
        Me.pnlDervicePermission.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlDervicePermission.Name = "pnlDervicePermission"
        Me.pnlDervicePermission.Size = New System.Drawing.Size(390, 380)
        Me.pnlDervicePermission.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDervicePermission.TabIndex = 75
        Me.pnlDervicePermission.tbBackgroundImage = Nothing
        Me.pnlDervicePermission.tbShowWatermark = False
        Me.pnlDervicePermission.tbSplit = "0,0,0,0"
        Me.pnlDervicePermission.tbWatermark = Nothing
        Me.pnlDervicePermission.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDervicePermission.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnSplit
        '
        Me.btnSplit.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSplit.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.btnSplit.BindingForm = Nothing
        Me.btnSplit.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSplit.Location = New System.Drawing.Point(11, 172)
        Me.btnSplit.Name = "btnSplit"
        Me.btnSplit.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSplit.Selectable = True
        Me.btnSplit.Size = New System.Drawing.Size(368, 1)
        Me.btnSplit.TabIndex = 77
        Me.btnSplit.tbAdriftIconWhenHover = False
        Me.btnSplit.tbAutoSize = False
        Me.btnSplit.tbAutoSizeEx = False
        Me.btnSplit.tbBackgroundImage = Nothing
        Me.btnSplit.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSplit.tbBadgeNumber = 0
        Me.btnSplit.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSplit.tbEndEllipsis = False
        Me.btnSplit.tbIconHoldPlace = True
        Me.btnSplit.tbIconImage = Nothing
        Me.btnSplit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSplit.tbIconMore = False
        Me.btnSplit.tbIconMouseDown = Nothing
        Me.btnSplit.tbIconMouseHover = Nothing
        Me.btnSplit.tbIconMouseLeave = Nothing
        Me.btnSplit.tbIconPlaceText = 2
        Me.btnSplit.tbIconReadOnly = Nothing
        Me.btnSplit.tbImageMouseDown = Nothing
        Me.btnSplit.tbImageMouseHover = Nothing
        Me.btnSplit.tbImageMouseLeave = Nothing
        Me.btnSplit.tbProgressValue = 50
        Me.btnSplit.tbReadOnly = False
        Me.btnSplit.tbReadOnlyText = False
        Me.btnSplit.tbShadow = False
        Me.btnSplit.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSplit.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSplit.tbShowDot = False
        Me.btnSplit.tbShowMoreIconImg = Global.iTong.My.Resources.Resources.welcome_btn_split
        Me.btnSplit.tbShowNew = False
        Me.btnSplit.tbShowProgress = False
        Me.btnSplit.tbShowTip = True
        Me.btnSplit.tbShowToolTipOnButton = False
        Me.btnSplit.tbSplit = "35,0,35,0"
        Me.btnSplit.tbText = ""
        Me.btnSplit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.tbTextColor = System.Drawing.Color.White
        Me.btnSplit.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSplit.tbTextColorDown = System.Drawing.Color.White
        Me.btnSplit.tbTextColorHover = System.Drawing.Color.White
        Me.btnSplit.tbTextMouseDownPlace = 0
        Me.btnSplit.tbToolTip = ""
        Me.btnSplit.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSplit.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSplit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.VisibleEx = True
        '
        'cbxPermitInstallTui
        '
        Me.cbxPermitInstallTui.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxPermitInstallTui.BackColor = System.Drawing.Color.Transparent
        Me.cbxPermitInstallTui.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxPermitInstallTui.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxPermitInstallTui.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxPermitInstallTui.Location = New System.Drawing.Point(37, 288)
        Me.cbxPermitInstallTui.Name = "cbxPermitInstallTui"
        Me.cbxPermitInstallTui.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxPermitInstallTui.Size = New System.Drawing.Size(80, 18)
        Me.cbxPermitInstallTui.TabIndex = 76
        Me.cbxPermitInstallTui.tbAdriftIconWhenHover = False
        Me.cbxPermitInstallTui.tbAutoSize = False
        Me.cbxPermitInstallTui.tbAutoSizeEx = True
        Me.cbxPermitInstallTui.tbIconChecked = CType(resources.GetObject("cbxPermitInstallTui.tbIconChecked"), System.Drawing.Image)
        Me.cbxPermitInstallTui.tbIconCheckedMouseDown = Nothing
        Me.cbxPermitInstallTui.tbIconCheckedMouseHover = Nothing
        Me.cbxPermitInstallTui.tbIconCheckedMouseLeave = Nothing
        Me.cbxPermitInstallTui.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxPermitInstallTui.tbIconHoldPlace = True
        Me.cbxPermitInstallTui.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxPermitInstallTui.tbIconIndeterminate = CType(resources.GetObject("cbxPermitInstallTui.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxPermitInstallTui.tbIconIndeterminateMouseDown = Nothing
        Me.cbxPermitInstallTui.tbIconIndeterminateMouseHover = Nothing
        Me.cbxPermitInstallTui.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxPermitInstallTui.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxPermitInstallTui.tbIconPlaceText = 1
        Me.cbxPermitInstallTui.tbIconUnChecked = CType(resources.GetObject("cbxPermitInstallTui.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxPermitInstallTui.tbIconUnCheckedMouseDown = Nothing
        Me.cbxPermitInstallTui.tbIconUnCheckedMouseHover = Nothing
        Me.cbxPermitInstallTui.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxPermitInstallTui.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxPermitInstallTui.tbImageBackground = Nothing
        Me.cbxPermitInstallTui.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxPermitInstallTui.tbImageCheckedMouseDown = Nothing
        Me.cbxPermitInstallTui.tbImageCheckedMouseHover = Nothing
        Me.cbxPermitInstallTui.tbImageCheckedMouseLeave = Nothing
        Me.cbxPermitInstallTui.tbImageUnCheckedMouseDown = Nothing
        Me.cbxPermitInstallTui.tbImageUnCheckedMouseHover = Nothing
        Me.cbxPermitInstallTui.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxPermitInstallTui.tbReadOnly = False
        Me.cbxPermitInstallTui.tbShadow = False
        Me.cbxPermitInstallTui.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxPermitInstallTui.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxPermitInstallTui.tbSplit = "3,3,3,3"
        Me.cbxPermitInstallTui.tbToolTip = ""
        Me.cbxPermitInstallTui.Text = "推荐安装"
        Me.cbxPermitInstallTui.UseVisualStyleBackColor = False
        '
        'cbxDervicePermission
        '
        Me.cbxDervicePermission.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxDervicePermission.BackColor = System.Drawing.Color.Transparent
        Me.cbxDervicePermission.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxDervicePermission.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxDervicePermission.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxDervicePermission.Location = New System.Drawing.Point(35, 130)
        Me.cbxDervicePermission.Name = "cbxDervicePermission"
        Me.cbxDervicePermission.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxDervicePermission.Size = New System.Drawing.Size(146, 18)
        Me.cbxDervicePermission.TabIndex = 76
        Me.cbxDervicePermission.tbAdriftIconWhenHover = False
        Me.cbxDervicePermission.tbAutoSize = False
        Me.cbxDervicePermission.tbAutoSizeEx = True
        Me.cbxDervicePermission.tbIconChecked = CType(resources.GetObject("cbxDervicePermission.tbIconChecked"), System.Drawing.Image)
        Me.cbxDervicePermission.tbIconCheckedMouseDown = Nothing
        Me.cbxDervicePermission.tbIconCheckedMouseHover = Nothing
        Me.cbxDervicePermission.tbIconCheckedMouseLeave = Nothing
        Me.cbxDervicePermission.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxDervicePermission.tbIconHoldPlace = True
        Me.cbxDervicePermission.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxDervicePermission.tbIconIndeterminate = CType(resources.GetObject("cbxDervicePermission.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxDervicePermission.tbIconIndeterminateMouseDown = Nothing
        Me.cbxDervicePermission.tbIconIndeterminateMouseHover = Nothing
        Me.cbxDervicePermission.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxDervicePermission.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxDervicePermission.tbIconPlaceText = 1
        Me.cbxDervicePermission.tbIconUnChecked = CType(resources.GetObject("cbxDervicePermission.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxDervicePermission.tbIconUnCheckedMouseDown = Nothing
        Me.cbxDervicePermission.tbIconUnCheckedMouseHover = Nothing
        Me.cbxDervicePermission.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxDervicePermission.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxDervicePermission.tbImageBackground = Nothing
        Me.cbxDervicePermission.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxDervicePermission.tbImageCheckedMouseDown = Nothing
        Me.cbxDervicePermission.tbImageCheckedMouseHover = Nothing
        Me.cbxDervicePermission.tbImageCheckedMouseLeave = Nothing
        Me.cbxDervicePermission.tbImageUnCheckedMouseDown = Nothing
        Me.cbxDervicePermission.tbImageUnCheckedMouseHover = Nothing
        Me.cbxDervicePermission.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxDervicePermission.tbReadOnly = False
        Me.cbxDervicePermission.tbShadow = False
        Me.cbxDervicePermission.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxDervicePermission.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxDervicePermission.tbSplit = "3,3,3,3"
        Me.cbxDervicePermission.tbToolTip = ""
        Me.cbxDervicePermission.Text = "允许管理Android设备"
        Me.cbxDervicePermission.UseVisualStyleBackColor = False
        '
        'lblPermissionInstallTui
        '
        Me.lblPermissionInstallTui.BackColor = System.Drawing.Color.Transparent
        Me.lblPermissionInstallTui.Font = New System.Drawing.Font("Arial", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lblPermissionInstallTui.ForeColor = System.Drawing.Color.FromArgb(CType(CType(110, Byte), Integer), CType(CType(110, Byte), Integer), CType(CType(110, Byte), Integer))
        Me.lblPermissionInstallTui.Location = New System.Drawing.Point(24, 190)
        Me.lblPermissionInstallTui.Name = "lblPermissionInstallTui"
        Me.lblPermissionInstallTui.Size = New System.Drawing.Size(213, 22)
        Me.lblPermissionInstallTui.TabIndex = 75
        Me.lblPermissionInstallTui.Text = "安装管理"
        Me.lblPermissionInstallTui.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblDervicePermission
        '
        Me.lblDervicePermission.BackColor = System.Drawing.Color.Transparent
        Me.lblDervicePermission.Font = New System.Drawing.Font("Arial", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lblDervicePermission.ForeColor = System.Drawing.Color.FromArgb(CType(CType(110, Byte), Integer), CType(CType(110, Byte), Integer), CType(CType(110, Byte), Integer))
        Me.lblDervicePermission.Location = New System.Drawing.Point(24, 15)
        Me.lblDervicePermission.Name = "lblDervicePermission"
        Me.lblDervicePermission.Size = New System.Drawing.Size(197, 22)
        Me.lblDervicePermission.TabIndex = 75
        Me.lblDervicePermission.Text = "权限管理"
        Me.lblDervicePermission.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblPermissionIntallTuiMsg
        '
        Me.lblPermissionIntallTuiMsg.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblPermissionIntallTuiMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblPermissionIntallTuiMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(187, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(187, Byte), Integer))
        Me.lblPermissionIntallTuiMsg.Location = New System.Drawing.Point(37, 217)
        Me.lblPermissionIntallTuiMsg.Name = "lblPermissionIntallTuiMsg"
        Me.lblPermissionIntallTuiMsg.Size = New System.Drawing.Size(334, 60)
        Me.lblPermissionIntallTuiMsg.TabIndex = 74
        Me.lblPermissionIntallTuiMsg.Text = "设备连接成功后，为您安装最新版同步推，安装后可直接在移" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "动端上免费下载APP，还能大大提升对设备的管理能力。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.lblPermissionIntallTuiMsg.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblDervicePermissionMsg
        '
        Me.lblDervicePermissionMsg.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblDervicePermissionMsg.BackColor = System.Drawing.Color.Transparent
        Me.lblDervicePermissionMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(187, Byte), Integer), CType(CType(187, Byte), Integer), CType(CType(187, Byte), Integer))
        Me.lblDervicePermissionMsg.Location = New System.Drawing.Point(35, 42)
        Me.lblDervicePermissionMsg.Name = "lblDervicePermissionMsg"
        Me.lblDervicePermissionMsg.Size = New System.Drawing.Size(321, 75)
        Me.lblDervicePermissionMsg.TabIndex = 74
        Me.lblDervicePermissionMsg.Text = "同步助手同时支持iOS设备以及Android设备的管理，通过设备权限管理可自定义开启或关闭同步助手对于Android设备的管理权限。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.lblDervicePermissionMsg.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlAndroidWeChat
        '
        Me.pnlAndroidWeChat.AutoSize = True
        Me.pnlAndroidWeChat.BackColor = System.Drawing.Color.Transparent
        Me.pnlAndroidWeChat.Controls.Add(Me.lblPhoto)
        Me.pnlAndroidWeChat.Controls.Add(Me.cbxPhoto)
        Me.pnlAndroidWeChat.Controls.Add(Me.lblPhotoDesc)
        Me.pnlAndroidWeChat.Controls.Add(Me.Label2)
        Me.pnlAndroidWeChat.Controls.Add(Me.cbxOldAndroidWeChat)
        Me.pnlAndroidWeChat.Controls.Add(Me.btnKnowiWechatAssistant)
        Me.pnlAndroidWeChat.Controls.Add(Me.Label1)
        Me.pnlAndroidWeChat.Location = New System.Drawing.Point(783, 383)
        Me.pnlAndroidWeChat.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlAndroidWeChat.Name = "pnlAndroidWeChat"
        Me.pnlAndroidWeChat.Size = New System.Drawing.Size(390, 380)
        Me.pnlAndroidWeChat.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlAndroidWeChat.TabIndex = 76
        Me.pnlAndroidWeChat.tbBackgroundImage = Nothing
        Me.pnlAndroidWeChat.tbShowWatermark = False
        Me.pnlAndroidWeChat.tbSplit = "0,0,0,0"
        Me.pnlAndroidWeChat.tbWatermark = Nothing
        Me.pnlAndroidWeChat.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlAndroidWeChat.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'Label2
        '
        Me.Label2.BackColor = System.Drawing.Color.Transparent
        Me.Label2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Label2.Location = New System.Drawing.Point(25, 25)
        Me.Label2.Name = "Label2"
        Me.Label2.Size = New System.Drawing.Size(119, 17)
        Me.Label2.TabIndex = 77
        Me.Label2.Text = "Android微信管理"
        Me.Label2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cbxOldAndroidWeChat
        '
        Me.cbxOldAndroidWeChat.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.cbxOldAndroidWeChat.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxOldAndroidWeChat.BackColor = System.Drawing.Color.Transparent
        Me.cbxOldAndroidWeChat.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxOldAndroidWeChat.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxOldAndroidWeChat.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxOldAndroidWeChat.Location = New System.Drawing.Point(37, 134)
        Me.cbxOldAndroidWeChat.Margin = New System.Windows.Forms.Padding(0)
        Me.cbxOldAndroidWeChat.Name = "cbxOldAndroidWeChat"
        Me.cbxOldAndroidWeChat.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxOldAndroidWeChat.Size = New System.Drawing.Size(218, 23)
        Me.cbxOldAndroidWeChat.TabIndex = 76
        Me.cbxOldAndroidWeChat.tbAdriftIconWhenHover = True
        Me.cbxOldAndroidWeChat.tbAutoSize = False
        Me.cbxOldAndroidWeChat.tbAutoSizeEx = True
        Me.cbxOldAndroidWeChat.tbIconChecked = CType(resources.GetObject("cbxOldAndroidWeChat.tbIconChecked"), System.Drawing.Image)
        Me.cbxOldAndroidWeChat.tbIconCheckedMouseDown = Nothing
        Me.cbxOldAndroidWeChat.tbIconCheckedMouseHover = Nothing
        Me.cbxOldAndroidWeChat.tbIconCheckedMouseLeave = Nothing
        Me.cbxOldAndroidWeChat.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxOldAndroidWeChat.tbIconHoldPlace = True
        Me.cbxOldAndroidWeChat.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxOldAndroidWeChat.tbIconIndeterminate = CType(resources.GetObject("cbxOldAndroidWeChat.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxOldAndroidWeChat.tbIconIndeterminateMouseDown = Nothing
        Me.cbxOldAndroidWeChat.tbIconIndeterminateMouseHover = Nothing
        Me.cbxOldAndroidWeChat.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxOldAndroidWeChat.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxOldAndroidWeChat.tbIconPlaceText = 1
        Me.cbxOldAndroidWeChat.tbIconUnChecked = CType(resources.GetObject("cbxOldAndroidWeChat.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxOldAndroidWeChat.tbIconUnCheckedMouseDown = Nothing
        Me.cbxOldAndroidWeChat.tbIconUnCheckedMouseHover = Nothing
        Me.cbxOldAndroidWeChat.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxOldAndroidWeChat.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxOldAndroidWeChat.tbImageBackground = Nothing
        Me.cbxOldAndroidWeChat.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxOldAndroidWeChat.tbImageCheckedMouseDown = Nothing
        Me.cbxOldAndroidWeChat.tbImageCheckedMouseHover = Nothing
        Me.cbxOldAndroidWeChat.tbImageCheckedMouseLeave = Nothing
        Me.cbxOldAndroidWeChat.tbImageUnCheckedMouseDown = Nothing
        Me.cbxOldAndroidWeChat.tbImageUnCheckedMouseHover = Nothing
        Me.cbxOldAndroidWeChat.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxOldAndroidWeChat.tbReadOnly = False
        Me.cbxOldAndroidWeChat.tbShadow = False
        Me.cbxOldAndroidWeChat.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxOldAndroidWeChat.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxOldAndroidWeChat.tbSplit = "3,3,3,3"
        Me.cbxOldAndroidWeChat.tbToolTip = "33333"
        Me.cbxOldAndroidWeChat.Text = "显示旧管理（更多功能-微信媒体）"
        Me.cbxOldAndroidWeChat.UseVisualStyleBackColor = False
        '
        'btnKnowiWechatAssistant
        '
        Me.btnKnowiWechatAssistant.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnKnowiWechatAssistant.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnKnowiWechatAssistant.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnKnowiWechatAssistant.Location = New System.Drawing.Point(37, 92)
        Me.btnKnowiWechatAssistant.Name = "btnKnowiWechatAssistant"
        Me.btnKnowiWechatAssistant.Size = New System.Drawing.Size(106, 26)
        Me.btnKnowiWechatAssistant.TabIndex = 69
        Me.btnKnowiWechatAssistant.tbAdriftWhenHover = False
        Me.btnKnowiWechatAssistant.tbAutoEllipsis = False
        Me.btnKnowiWechatAssistant.tbAutoSize = False
        Me.btnKnowiWechatAssistant.tbHideImage = False
        Me.btnKnowiWechatAssistant.tbIconImage = Nothing
        Me.btnKnowiWechatAssistant.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnKnowiWechatAssistant.tbIconPlaceText = 5
        Me.btnKnowiWechatAssistant.tbShadow = False
        Me.btnKnowiWechatAssistant.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnKnowiWechatAssistant.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnKnowiWechatAssistant.tbShowScrolling = False
        Me.btnKnowiWechatAssistant.Text = "了解微信备份助手"
        Me.btnKnowiWechatAssistant.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'Label1
        '
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(37, 50)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(319, 37)
        Me.Label1.TabIndex = 68
        Me.Label1.Text = "同步助手支持对已经root的安卓设备管理微信的导出功能，因人力维护问题，现在用功能更强大的微信备份助手代替。"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblPhoto
        '
        Me.lblPhoto.BackColor = System.Drawing.Color.Transparent
        Me.lblPhoto.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblPhoto.Location = New System.Drawing.Point(25, 178)
        Me.lblPhoto.Name = "lblPhoto"
        Me.lblPhoto.Size = New System.Drawing.Size(119, 17)
        Me.lblPhoto.TabIndex = 80
        Me.lblPhoto.Text = "iOS照片管理"
        Me.lblPhoto.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblPhoto.Visible = False
        '
        'cbxPhoto
        '
        Me.cbxPhoto.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.cbxPhoto.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxPhoto.BackColor = System.Drawing.Color.Transparent
        Me.cbxPhoto.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxPhoto.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxPhoto.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxPhoto.Location = New System.Drawing.Point(37, 252)
        Me.cbxPhoto.Margin = New System.Windows.Forms.Padding(0)
        Me.cbxPhoto.Name = "cbxPhoto"
        Me.cbxPhoto.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxPhoto.Size = New System.Drawing.Size(224, 23)
        Me.cbxPhoto.TabIndex = 79
        Me.cbxPhoto.tbAdriftIconWhenHover = True
        Me.cbxPhoto.tbAutoSize = False
        Me.cbxPhoto.tbAutoSizeEx = True
        Me.cbxPhoto.tbIconChecked = CType(resources.GetObject("cbxPhoto.tbIconChecked"), System.Drawing.Image)
        Me.cbxPhoto.tbIconCheckedMouseDown = Nothing
        Me.cbxPhoto.tbIconCheckedMouseHover = Nothing
        Me.cbxPhoto.tbIconCheckedMouseLeave = Nothing
        Me.cbxPhoto.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxPhoto.tbIconHoldPlace = True
        Me.cbxPhoto.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxPhoto.tbIconIndeterminate = CType(resources.GetObject("cbxPhoto.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxPhoto.tbIconIndeterminateMouseDown = Nothing
        Me.cbxPhoto.tbIconIndeterminateMouseHover = Nothing
        Me.cbxPhoto.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxPhoto.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxPhoto.tbIconPlaceText = 1
        Me.cbxPhoto.tbIconUnChecked = CType(resources.GetObject("cbxPhoto.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxPhoto.tbIconUnCheckedMouseDown = Nothing
        Me.cbxPhoto.tbIconUnCheckedMouseHover = Nothing
        Me.cbxPhoto.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxPhoto.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxPhoto.tbImageBackground = Nothing
        Me.cbxPhoto.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxPhoto.tbImageCheckedMouseDown = Nothing
        Me.cbxPhoto.tbImageCheckedMouseHover = Nothing
        Me.cbxPhoto.tbImageCheckedMouseLeave = Nothing
        Me.cbxPhoto.tbImageUnCheckedMouseDown = Nothing
        Me.cbxPhoto.tbImageUnCheckedMouseHover = Nothing
        Me.cbxPhoto.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxPhoto.tbReadOnly = False
        Me.cbxPhoto.tbShadow = False
        Me.cbxPhoto.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxPhoto.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxPhoto.tbSplit = "3,3,3,3"
        Me.cbxPhoto.tbToolTip = "33333"
        Me.cbxPhoto.Text = "显示照片壁纸（功能位于顶部横栏）"
        Me.cbxPhoto.UseVisualStyleBackColor = False
        Me.cbxPhoto.Visible = False
        '
        'lblPhotoDesc
        '
        Me.lblPhotoDesc.BackColor = System.Drawing.Color.Transparent
        Me.lblPhotoDesc.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblPhotoDesc.Location = New System.Drawing.Point(37, 203)
        Me.lblPhotoDesc.Name = "lblPhotoDesc"
        Me.lblPhotoDesc.Size = New System.Drawing.Size(319, 37)
        Me.lblPhotoDesc.TabIndex = 78
        Me.lblPhotoDesc.Text = "由于部分iOS固件的不支持，我们将照片管理功能进行隐藏，如有需求可以勾选后显示"
        Me.lblPhotoDesc.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblPhotoDesc.Visible = False
        '
        'frmSettingV3
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(1375, 848)
        Me.Controls.Add(Me.pnlRight)
        Me.Controls.Add(Me.pnlLeft)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.btn_close)
        Me.MinimumSize = New System.Drawing.Size(11, 87)
        Me.Name = "frmSettingV3"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,55"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "Settings"
        Me.pnlNormal.ResumeLayout(False)
        Me.pnlNormal.PerformLayout()
        CType(Me.numDownload, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlFolder.ResumeLayout(False)
        Me.pnlFolder.PerformLayout()
        Me.pnlLeft.ResumeLayout(False)
        Me.pnlRight.ResumeLayout(False)
        Me.pnlRight.PerformLayout()
        Me.pnlDNS.ResumeLayout(False)
        Me.pnlAppleID.ResumeLayout(False)
        Me.pnlAppleID.PerformLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picPwd, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picId, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picCheck, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDervicePermission.ResumeLayout(False)
        Me.pnlAndroidWeChat.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As tbButton
    Friend WithEvents btniTunesFolder As tbButton
    Friend WithEvents lbliTunesFolder As System.Windows.Forms.Label
    Friend WithEvents btnDownloadFolder As tbButton
    Friend WithEvents lblDownloadFolder As System.Windows.Forms.Label
    Friend WithEvents btnCancel As tbButton
    Friend WithEvents btnOK As tbButton
    Friend WithEvents txtDownloadFolder As tbTextBox
    Friend WithEvents txtiTunesFolder As tbTextBox
    Friend WithEvents rdbExist As tbRadioButton
    Friend WithEvents rdbMini As tbRadioButton
    Friend WithEvents chkLinkIPAPackage As tbCheckBox
    Friend WithEvents chkRemember As tbCheckBox
    Friend WithEvents txtMusicFolder As tbTextBox
    Friend WithEvents btnMusicFolder As tbButton
    Friend WithEvents lblMusicFolder As System.Windows.Forms.Label
    Friend WithEvents txtRingtoneFolder As tbTextBox
    Friend WithEvents btnRingtoneFolder As tbButton
    Friend WithEvents lblRingtoneFolder As System.Windows.Forms.Label
    Friend WithEvents txtWallpaperFolder As tbTextBox
    Friend WithEvents btnWallpaperFolder As tbButton
    Friend WithEvents lblWallpaperFolder As System.Windows.Forms.Label
    Friend WithEvents lblApp As System.Windows.Forms.Label
    Friend WithEvents txtOtherFolder As tbTextBox
    Friend WithEvents btnOtherFolder As tbButton
    Friend WithEvents lblOtherFolder As System.Windows.Forms.Label
    Friend WithEvents lblLanguage As System.Windows.Forms.Label
    Friend WithEvents cbxLanguage As System.Windows.Forms.ComboBox
    Friend WithEvents chkAutoInstallAfterDownload As tbCheckBox
    Friend WithEvents chkAutoShowMsgAfterDownload As tbCheckBox
    Friend WithEvents pnlNormal As tbPanel
    Friend WithEvents pnlFolder As tbPanel
    Friend WithEvents pnlLeft As tbPanel
    Friend WithEvents pnlRight As tbPanel
    Friend WithEvents lblClose As System.Windows.Forms.Label
    Friend WithEvents lblInstall As System.Windows.Forms.Label
    Friend WithEvents btnNormal As tbRadioButton
    Friend WithEvents btnFolder As tbRadioButton
    Friend WithEvents numDownload As System.Windows.Forms.NumericUpDown
    Friend WithEvents lblDownload As System.Windows.Forms.Label
    Friend WithEvents lblMaxDownload As System.Windows.Forms.Label
    Friend WithEvents btnDNS As iTong.Components.tbRadioButton
    Friend WithEvents pnlDNS As iTong.Components.tbPanel
    Friend WithEvents btnSetUserDNS As iTong.Components.tbButton
    Friend WithEvents btnSetDefaultDNS As iTong.Components.tbButton
    Friend WithEvents txtDNS2 As iTong.Components.tbTextBox
    Friend WithEvents lblDNS2 As System.Windows.Forms.Label
    Friend WithEvents txtDNS1 As iTong.Components.tbTextBox
    Friend WithEvents lblDNS1 As System.Windows.Forms.Label
    Friend WithEvents btnAppleID As iTong.Components.tbRadioButton
    Friend WithEvents pnlAppleID As iTong.Components.tbPanel
    Friend WithEvents btnLoginAppleId As iTong.Components.tbButton
    Friend WithEvents lblApplePwd As System.Windows.Forms.Label
    Friend WithEvents lblAppleID As System.Windows.Forms.Label
    Friend WithEvents lblRegister As iTong.Components.tbLabel
    Friend WithEvents chkAgree As iTong.Components.tbCheckBox
    Friend WithEvents lblClause As iTong.Components.tbLabel
    Friend WithEvents pnlAppleIDGrid As iTong.Components.tbPanel
    Friend WithEvents txtPwd As iTong.Components.tbTextBox
    Friend WithEvents txtId As iTong.Components.tbTextBox
    Friend WithEvents picCheck As System.Windows.Forms.PictureBox
    Friend WithEvents picPwd As System.Windows.Forms.PictureBox
    Friend WithEvents picId As System.Windows.Forms.PictureBox
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents lblAppleIdMessage As iTong.Components.tbButton
    Friend WithEvents lblLoginApplids As System.Windows.Forms.Label
    Friend WithEvents btnDervicePermission As iTong.Components.tbRadioButton
    Friend WithEvents pnlDervicePermission As iTong.Components.tbPanel
    Friend WithEvents lblDervicePermissionMsg As System.Windows.Forms.Label
    Friend WithEvents lblDervicePermission As System.Windows.Forms.Label
    Friend WithEvents cbxDervicePermission As iTong.Components.tbCheckBox
    Friend WithEvents chkIconShowUpdateNumber As iTong.Components.tbCheckBox
    Friend WithEvents cbxPermitInstallTui As iTong.Components.tbCheckBox
    Friend WithEvents lblPermissionInstallTui As System.Windows.Forms.Label
    Friend WithEvents lblPermissionIntallTuiMsg As System.Windows.Forms.Label
    Friend WithEvents btnSplit As iTong.Components.tbButton
    Friend WithEvents txtFirmware As iTong.Components.tbTextBox
    Friend WithEvents btnFirmware As iTong.Components.tbButton
    Friend WithEvents lblFirmware As System.Windows.Forms.Label
    Friend WithEvents chkLoginCode As iTong.Components.tbCheckBox
    Friend WithEvents cbxLog As iTong.Components.tbCheckBox
    Friend WithEvents btnAndroidWeChat As iTong.Components.tbRadioButton
    Friend WithEvents pnlAndroidWeChat As iTong.Components.tbPanel
    Friend WithEvents Label2 As System.Windows.Forms.Label
    Friend WithEvents cbxOldAndroidWeChat As iTong.Components.tbCheckBox
    Friend WithEvents btnKnowiWechatAssistant As iTong.Components.tbLabel
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents lblPhoto As System.Windows.Forms.Label
    Friend WithEvents cbxPhoto As iTong.Components.tbCheckBox
    Friend WithEvents lblPhotoDesc As System.Windows.Forms.Label

End Class
