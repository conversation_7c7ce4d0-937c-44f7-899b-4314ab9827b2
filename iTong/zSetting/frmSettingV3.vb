﻿Imports System.Threading
Imports ProgressStyle = iTong.Components.ProgressStyle

Public Class frmSettingV3

    Private Enum FolderType
        iTunes
        Other
        App
        Music
        Ringtone
        Wallpaper
        Firmware
    End Enum

    Private mFolderiTunes As String = ""
    Private mFolderOther As String = ""
    Private mFolderDownload As String = ""
    Private mFolderMusic As String = ""
    Private mFolderRingtone As String = ""
    Private mFolderWallpaper As String = ""
    Private mFolderFirmware As String = ""
    Private mIsMini As Boolean = True
    Private mLstNewFolders As New List(Of String) '新增加的软件目录
    Private dgvAppleId As New tbDataGridViewEx
    Private chkAutoRun As New tbCheckBox

    'apple id登录
    Private mTdLogin As Thread = Nothing

#Region "--- 初始化 ---"

    Public Sub New(ByVal FunctionMappingKey As FunctionKey)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FilletRadius = 5
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong
        'Me.ShowInTaskbar = True
        'Me.mApplication = app

#If IS_ITONG_ZJ Then
        CreateAutoRunButton()
#End If

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitLang()

        Me.mFolderiTunes = IniSetting.GetiTunesFolder
        Me.txtiTunesFolder.Text = Me.mFolderiTunes

        Me.mFolderOther = IniSetting.GetOtherFolder
        Me.txtOtherFolder.Text = Me.mFolderOther

        Me.mFolderDownload = IniSetting.GetDownloadSoftFolder
        Me.txtDownloadFolder.Text = Me.mFolderDownload

        Me.mFolderMusic = IniSetting.GetDownloadMusicFolder
        Me.txtMusicFolder.Text = Me.mFolderMusic

        Me.mFolderRingtone = IniSetting.GetDownloadRingtoneFolder
        Me.txtRingtoneFolder.Text = Me.mFolderRingtone

        Me.mFolderWallpaper = IniSetting.GetDownloadWallpaperFolder
        Me.txtWallpaperFolder.Text = Me.mFolderWallpaper

        Me.mFolderFirmware = IniSetting.GetDownloadFirmwareFolder
        Me.txtFirmware.Text = Me.mFolderFirmware

        Dim intCountServer As Integer = ServerIniSetting.GetMaxDownloadCount
        Dim intCount As Integer = IniSetting.GetDownloadMaxCount
        Me.numDownload.Maximum = intCountServer
        If intCountServer < intCount OrElse intCount < 1 Then
            Me.numDownload.Value = intCountServer
        Else
            Me.numDownload.Value = intCount
        End If

        Me.chkRemember.Checked = IniSetting.GetRememberOnClose()

        Me.mIsMini = IniSetting.GetMinizedOnClose
        If Me.mIsMini Then
            Me.rdbMini.Checked = True
        Else
            Me.rdbExist.Checked = True
        End If

        Me.chkLinkIPAPackage.Checked = ProcFormHelper.Instance().CheckPkgInstallerRegisterExist()
        Me.chkAutoShowMsgAfterDownload.Checked = IniSetting.GetAutoShowMsgAfterDownload
        Me.chkAutoInstallAfterDownload.Checked = IniSetting.GetAutoInstallAfterDownload
        Me.chkIconShowUpdateNumber.Checked = IniSetting.GetIsIconShowUpdateNumber

        Me.cbxOldAndroidWeChat.Checked = IniSetting.GetOldAndroidWeChat

        Me.cbxDervicePermission.Checked = IniSetting.GetDervicePermission
        Me.cbxPermitInstallTui.Checked = IniSetting.GetPermissionInstallTui
        Me.cbxPhoto.Checked = IniSetting.GetPhotoShow()
        Me.InitLeftButton()

        Me.cbxLanguage.Font = Common.CreateFont("Arial", 9, FontStyle.Regular)

        Try
            Me.cbxLog.Checked = File.Exists(Path.Combine(Folder.AppFolder, "debug.dll"))
        Catch
        End Try

        Me.GetSystemDNS()
        Me.InitApleIdGrid()
        Me.GetAppleID()

        Me.chkLoginCode.Checked = IniSetting.GetBuyWithGSA()

#If IS_ITONG_ZJ Then
        Me.chkAutoRun.Checked = IniSetting.GetAutoRunState()
#End If
    End Sub

    Private Sub InitLeftButton()
        Me.Size = New Size(534, 467)
        Me.pnlNormal.Dock = DockStyle.Fill
        Me.pnlFolder.Dock = DockStyle.Fill
        Me.pnlDNS.Dock = DockStyle.Fill
        Me.pnlAppleID.Dock = DockStyle.Fill
        Me.pnlDervicePermission.Dock = DockStyle.Fill
        Me.pnlAndroidWeChat.Dock = DockStyle.Fill
        Me.btnNormal.Tag = Me.pnlNormal
        Me.btnFolder.Tag = Me.pnlFolder
        Me.btnDNS.Tag = Me.pnlDNS
        Me.btnAppleID.Tag = Me.pnlAppleID
        Me.btnDervicePermission.Tag = Me.pnlDervicePermission
        Me.btnAndroidWeChat.Tag = Me.pnlAndroidWeChat
        Me.btnNormal.Checked = True

        If Folder.LangType <> LanguageType.zh_CN Then
            Me.btnAppleID.Visible = False
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lbliTunesFolder.Text = Me.Language.GetString("Setting.Label.iTunesFolder")           '"iTunes Folder"
        Me.lblDownloadFolder.Text = Me.Language.GetString("Setting.Label.DownloadFolders")      '"下载目录" 
        Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")                              '"Cancel"
        Me.btnOK.Text = Me.Language.GetString("Common.Button.Save")                             '"Save"
        Me.btniTunesFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")     '"Change"
        Me.btnDownloadFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")   '"Change"
        Me.rdbMini.Text = Me.Language.GetString("Setting.RadioButton.MiniToTray")               '"Minimize to tray"
        Me.rdbExist.Text = Me.Language.GetString("Setting.RadioButton.ExitForm")                '"Exit iTong"
        Me.Text = Me.Language.GetString("Common.Button.Setting")
        Me.chkLinkIPAPackage.Text = Me.Language.GetString("Setting.Label.LinkIPAAndPKGPackage") '"关联 IPA 和 APK文件，可以直接双击文件来安装程序"      '"关联 IPA，可以直接双击 IPA 文件来安装程序"
        Me.chkRemember.Text = Me.Language.GetString("Setting.Label.NoPopExit")                  '"退出时不再提示"
        Me.lblApp.Text = Me.Language.GetString("App.TreeNode.DeviceApps")                       '"应用"
        Me.lblMusicFolder.Text = Me.Language.GetString("Main.Button.Music")                     '"音乐"
        Me.lblRingtoneFolder.Text = Me.Language.GetString("Media.Type.Ringtone")                '"铃声"
        Me.lblWallpaperFolder.Text = Me.Language.GetString("Photo.Label.WallPaper")             '"壁纸"
        Me.btnMusicFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")      '"Change"
        Me.btnRingtoneFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")   '"Change"
        Me.btnWallpaperFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")  '"Change"
        Me.btnFirmware.Text = Me.Language.GetString("Components.Login.Label.ChangePic")  '"Change"

        Me.lblFirmware.Text = Me.Language.GetString("Download.Label.Firmware")             '"固件"

        Me.lblOtherFolder.Text = Me.Language.GetString("Setting.Label.OtherFolder")             '"附加的其他软件（ipa）的目录"
        Me.btnOtherFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")      '"Change"
        Me.chkAutoShowMsgAfterDownload.Text = Me.Language.GetString("Setting.Label.AutoShowMsgAfterDownload") '"应用下载完成时弹窗提示"
        Me.chkAutoInstallAfterDownload.Text = Me.Language.GetString("Setting.Label.AutoInstallAfterDownload")   '"下载应用后立即安装"
        Me.chkIconShowUpdateNumber.Text = Me.Language.GetString("Setting.Label.PngShowUpdateNumber")      '在桌面图标上显示应用更新数量
        Me.lblLanguage.Text = Me.Language.GetString("Setting.Label.Language")                   '"界面语言"

        Me.btnNormal.Text = Me.Language.GetString("Setting.Label.SetupBase")                    '"基本设置"
        Me.btnFolder.Text = Me.Language.GetString("Setting.Label.SetupFolder")                  '"目录设置"
        Me.lblInstall.Text = Me.Language.GetString("Setting.Label.SetupInstall")                '"安装设置"
        Me.lblClose.Text = Me.Language.GetString("Setting.Label.SetupClose")                    '"退出设置"
        Me.lblDownload.Text = Me.Language.GetString("Setting.Label.DownloadCount")              '"同时下载数量"
        Me.lblMaxDownload.Text = String.Format(Me.Language.GetString("Setting.Label.MaxDownloadCount"), ServerIniSetting.GetMaxDownloadCount)   '"最大值为{0}"
        'Me.numDownload.Location = New Point(Me.lblDownload.Right + 3, Me.numDownload.Top)
        'Me.lblMaxDownload.Location = New Point(Me.numDownload.Right + 5, Me.lblMaxDownload.Top)

        Me.btnDNS.Text = Me.Language.GetString("Setting.Button.SetDNS")                         '"DNS设置"
        Me.lblDNS1.Text = Me.Language.GetString("Setting.Label.DefaultDNS")                     '"首选DNS服务器"
        Me.lblDNS2.Text = Me.Language.GetString("Setting.Label.BackupDNS")                      '"备选DNS服务器"
        Me.btnSetDefaultDNS.Text = Me.Language.GetString("Setting.Button.SetDefaultDNS")        '"设置通用DNS"
        Me.btnSetUserDNS.Text = Me.Language.GetString("Setting.Button.SetUserDNS")              '"恢复默认DNS"

        Me.btnAppleID.Text = Me.Language.GetString("App.Button.BindingAppleId")                 '"Apple ID登录"
        Me.lblApplePwd.Text = Me.Language.GetString("Common.Password")                          '"密码"
        Me.lblRegister.Text = Me.Language.GetString("App.Label.FreeRegistration")               '"免费注册"
        Me.btnLoginAppleId.Text = Me.Language.GetString("App.Button.BindingAppleId")            '"登录Apple ID"
        Me.chkAgree.Text = Me.Language.GetString("App.Label.ReadAndAgree")                      '"已经阅读并同意"
        Me.lblClause.Text = Me.Language.GetString("App.Label.Clause")                           '"隐私条款"
        Me.lblLoginApplids.Text = Me.Language.GetString("Setting.Label.LoginAccounts")          '"已登录的Apple ID"

        Me.btnDervicePermission.Text = Me.Language.GetString("Setting.Button.DervicePermission")                                     '设备权限管理
        Me.lblDervicePermission.Text = Me.Language.GetString("Setting.Label.DervicePermission")                                            '管理设置
        Me.cbxDervicePermission.Text = Me.Language.GetString("Setting.CheckBox.DervicePermission")                                 '允许管理Android设备
        Me.lblDervicePermissionMsg.Text = Me.Language.GetString("Setting.Label.DervicePermissionMsg")     '同步助手同时支持iOS设备以及Android设备的管理，通过设备权限管理可自定义开启或关闭同步助手对于Android设备的管理权限

        Me.cbxPermitInstallTui.Text = Me.Language.GetString("Setting.CheckBox.PermitInstallTui")    '推荐安装
        Me.lblPermissionIntallTuiMsg.Text = Me.Language.GetString("Setting.Label.PermissionInstallTuiMsg") '设备连接成功后，为您安装最新版同步推，安装后可直接在移动端上免费下载APP，还能大大提升对设备的管理能力。
        Me.lblPermissionInstallTui.Text = Me.Language.GetString("Setting.Label.PermissionInstallTui")     '安装管理

        Me.lblPhoto.Text = Me.Language.GetString("Setting.Label.Photo")                     '"iOS照片管理"
        Me.lblPhotoDesc.Text = Me.Language.GetString("Setting.Label.PhotoDesc")             '"由于部分iOS固件的不支持，我们将照片管理功能进行隐藏，如有需求可以勾选后重启助手显示"
        Me.cbxPhoto.Text = Me.Language.GetString("Setting.Button.Photo")                    '"显示照片壁纸（功能位于顶部横栏）"

        Me.chkLoginCode.Text = Me.Language.GetString("App.Label.OpenGSACode")               '"开启验证码"
        Me.SetFolderWidth()
    End Sub

    Private Function GetFolderPointX() As Integer
        Dim intReturn As Integer = 0
        intReturn = Me.lblApp.Right
        If intReturn < Me.lblMusicFolder.Right Then
            intReturn = Me.lblMusicFolder.Right
        End If

        If intReturn < Me.lblRingtoneFolder.Right Then
            intReturn = Me.lblRingtoneFolder.Right
        End If

        If intReturn < Me.lblWallpaperFolder.Right Then
            intReturn = Me.lblWallpaperFolder.Right
        End If

        If intReturn < Me.lblFirmware.Right Then
            intReturn = Me.lblFirmware.Right
        End If

        Return intReturn + 10
    End Function

    Private Sub SetFolderWidth()
        Me.txtDownloadFolder.Location = New Point(Me.GetFolderPointX(), Me.txtDownloadFolder.Top)
        Me.txtMusicFolder.Location = New Point(Me.GetFolderPointX(), Me.txtMusicFolder.Top)
        Me.txtRingtoneFolder.Location = New Point(Me.GetFolderPointX(), Me.txtRingtoneFolder.Top)
        Me.txtWallpaperFolder.Location = New Point(Me.GetFolderPointX(), Me.txtWallpaperFolder.Top)
        Me.txtFirmware.Location = New Point(Me.GetFolderPointX(), Me.txtFirmware.Top)

        Dim width As Integer = Me.txtOtherFolder.Right - Me.txtDownloadFolder.Left
        Me.txtDownloadFolder.Width = width
        Me.txtMusicFolder.Width = width
        Me.txtRingtoneFolder.Width = width
        Me.txtWallpaperFolder.Width = width
        Me.txtFirmware.Width = width
    End Sub

    '加载语言包信息
    Private Sub InitLang()
        Try
            Dim strCurrentLanguage As String = IniSetting.GetLanguage()
            If String.IsNullOrEmpty(strCurrentLanguage) Then
                strCurrentLanguage = Me.Language.CurrentLanguage.LangName
                '第一次打开的时候如果Setting.ini里面没有语言信息就写到文件里面去。
                IniSetting.SetLanguage(strCurrentLanguage)
            End If

            Me.cbxLanguage.Items.Clear()
            For Each info As LangInfo In Me.Language.Languages
                Me.cbxLanguage.Items.Add(info.LangDisplayName)
                If strCurrentLanguage = info.LangName Then
                    Me.cbxLanguage.Text = info.LangDisplayName
                End If
            Next

        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

    '保存语言名的信息
    Private Sub SaveLang()
        Try
            '读出当前语言种类和用户修改后语言种类
            Dim strCurrentLanguage As String = IniSetting.GetLanguage
            Dim strUserUpdateLanguage As String = ""
            For Each info As LangInfo In Me.Language.Languages
                If info.LangDisplayName = Me.cbxLanguage.Text Then
                    strUserUpdateLanguage = info.LangName
                    Exit For
                End If
            Next

            '读出 当前是否管理Android
            Dim strCurrentDervicePermission = IniSetting.GetDervicePermission

            Dim strCurrentOldAndroidWeChat = IniSetting.GetOldAndroidWeChat.ToString()

            '如果用户有修改语言信息就提示
            If strCurrentLanguage <> strUserUpdateLanguage OrElse strCurrentDervicePermission <> Me.cbxDervicePermission.Checked.ToString() OrElse strCurrentOldAndroidWeChat <> Me.cbxOldAndroidWeChat.Checked.ToString() Then
                IniSetting.SetDervicePermission(Me.cbxDervicePermission.Checked)
                IniSetting.SetLanguage(strUserUpdateLanguage)
                IniSetting.SetOldAndroidWeChat(Me.cbxOldAndroidWeChat.Checked)
                '"设置语言生效需要重启程序，是否立即执行重启？"
                '"提示"
                Dim result As DialogResult = tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.RestarForSetLang"), _
                                                             Me.Language.GetString("Common.Info"), _
                                                             MessageBoxButtons.YesNo, _
                                                             MessageBoxIcon.Question, _
                                                             MessageBoxDefaultButton.Button2)
                If result = Windows.Forms.DialogResult.Yes Then
                    Me.Hide()
                    Me.Close()
                    ProcForm.Instance.ExitApp()

                    '重启助手
                    HelperExeManager.RestartApp()
                End If
            End If
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try

    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnNormal_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNormal.CheckedChanged, btnFolder.CheckedChanged, btnDNS.CheckedChanged, btnAppleID.CheckedChanged, btnDervicePermission.CheckedChanged, btnAndroidWeChat.CheckedChanged
        Dim btn As tbRadioButton = sender
        If btn.Checked AndAlso btn IsNot Nothing AndAlso btn.Tag IsNot Nothing AndAlso TypeOf btn.Tag Is tbPanel Then
            CType(btn.Tag, tbPanel).BringToFront()
            btn.Checked = True
            If btn.Tag IsNot Nothing AndAlso btn.Tag Is Me.pnlDNS OrElse btn.Tag Is Me.pnlAppleID Then
                Me.btnOK.Enabled = False
                Me.btnCancel.Enabled = False
            Else
                Me.btnOK.Enabled = True
                Me.btnCancel.Enabled = True
            End If
        End If
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click

#If IS_ITONG_ZJ Then
        '1、开启自动启动
        Dim blnAutoRun As Boolean = Me.chkAutoRun.Checked

        If AutoRunHelper.SetAutoRun(blnAutoRun) Then
            IniSetting.SetAutoRunState(blnAutoRun)
        End If
#End If

        '设置退出时不再提示
        IniSetting.SetRememberOnClose(Me.chkRemember.Checked)
        If Me.rdbMini.Checked <> Me.mIsMini Then
            IniSetting.SetMinizedOnClose(Me.rdbMini.Checked)
        End If

        IniSetting.SetAutoShowMsgAfterDownload(Me.chkAutoShowMsgAfterDownload.Checked)
        IniSetting.SetAutoInstallAfterDownload(Me.chkAutoInstallAfterDownload.Checked)

#If IS_ITONG Then

        '设置在桌面图标上显示应用更新数量
        IniSetting.SetIsIconShowUpdateNumber(Me.chkIconShowUpdateNumber.Checked)
        '清空桌面数量
        If Me.chkIconShowUpdateNumber.Checked = False Then
            DesktopLink.ModifyShutcut()
        End If

        '设置是否允许自动安装同步推
        IniSetting.SetPermissionInstallTui(Me.cbxPhoto.Checked)
        IniSetting.SetPhotoShow(Me.cbxPhoto.Checked)
#End If

        If Me.chkLoginCode.Checked <> IniSetting.GetBuyWithGSA() Then
            IniSetting.SetBuyWithGSA(Me.chkLoginCode.Checked)

        End If

        '用户可以在参数设置里面设置是不是要关联 ipa 文件
        Dim strPkgInstallerFilePath As String = Folder.AppFolder & "PkgInstaller.exe"
        If ProcFormHelper.Instance().CheckPkgInstallerRegisterExist() <> Me.chkLinkIPAPackage.Checked AndAlso _
           File.Exists(strPkgInstallerFilePath) Then

            Try
                If Me.chkLinkIPAPackage.Checked = True Then
                    '设置关联
                    Process.Start(strPkgInstallerFilePath, "/r")
                Else
                    '取消关联
                    Process.Start(strPkgInstallerFilePath, "/u")
                End If

            Catch ex As Exception
            End Try
        End If
        IniSetting.SetOpenPkgInstaller(Me.chkLinkIPAPackage.Checked)

        '设置语言包 '设置 设备权限管理   （需要提示重启助手）
        Me.SaveLang()

        '设置目录
        Me.mLstNewFolders.Clear()
        Me.SaveFolder(Me.mFolderiTunes, Me.txtiTunesFolder.Text, FolderType.iTunes)
        Me.SaveFolder(Me.mFolderOther, Me.txtOtherFolder.Text, FolderType.Other)
        Me.SaveFolder(Me.mFolderDownload, Me.txtDownloadFolder.Text, FolderType.App)
        Me.SaveFolder(Me.mFolderMusic, Me.txtMusicFolder.Text, FolderType.Music)
        Me.SaveFolder(Me.mFolderRingtone, Me.txtRingtoneFolder.Text, FolderType.Ringtone)
        Me.SaveFolder(Me.mFolderWallpaper, Me.txtWallpaperFolder.Text, FolderType.Wallpaper)
        Me.SaveFolder(Me.mFolderFirmware, Me.txtFirmware.Text, FolderType.Firmware)

        IniSetting.SetDownloadMaxCount(Me.numDownload.Value)

        Try
            MultiThreadDownload.Instance().MaxDownloadCount = Me.numDownload.Value
        Catch ex As Exception
        End Try

        '软件目录（3个）。如果目录有更换，需要自动加载目录下的软件。
        Me.LoadAppFromNewFolders(Me.mLstNewFolders)

        Me.BringToFront()
        Me.Close()
    End Sub

    Private Sub btniTunesFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btniTunesFolder.Click
        Dim strFolder As String = Me.txtiTunesFolder.Text.Trim
        Me.txtiTunesFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnDownloadFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDownloadFolder.Click
        Dim strFolder As String = Me.txtDownloadFolder.Text.Trim
        Me.txtDownloadFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnMusicFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMusicFolder.Click
        Dim strFolder As String = Me.txtMusicFolder.Text.Trim
        Me.txtMusicFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnRingtoneFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRingtoneFolder.Click
        Dim strFolder As String = Me.txtRingtoneFolder.Text.Trim
        Me.txtRingtoneFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnWallpaperFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnWallpaperFolder.Click
        Dim strFolder As String = Me.txtWallpaperFolder.Text.Trim
        Me.txtWallpaperFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnFirmware_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFirmware.Click
        Dim strFolder As String = Me.txtFirmware.Text.Trim
        Me.txtFirmware.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnOtherFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOtherFolder.Click
        Dim strFolder As String = Me.txtOtherFolder.Text.Trim
        Me.txtOtherFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
        MyBase.OnClosed(e)
        If Me.Owner IsNot Nothing AndAlso TypeOf Me.Owner Is MainForm Then
            Me.Owner.BringToFront()
        End If
    End Sub

    Private Sub lblDownload_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblDownload.SizeChanged
        Me.numDownload.Location = New Point(Me.lblDownload.Right + 3, Me.numDownload.Top)
        Me.lblMaxDownload.Location = New Point(Me.numDownload.Right + 5, Me.lblMaxDownload.Top)
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function SelectFolder(ByVal strFolder As String) As String
        Dim folder As String = strFolder
        Dim dialog As FolderBrowserDialog = New FolderBrowserDialog()

        If Directory.Exists(strFolder) Then
            dialog.SelectedPath = strFolder
        End If

        If dialog.ShowDialog() = Windows.Forms.DialogResult.OK Then
            folder = dialog.SelectedPath
        End If

        Return folder
    End Function

    Private Sub GetFolderMusic(ByVal strMusicFolder As String)
        If Directory.Exists(strMusicFolder) Then
            Dim manager As MusicManage = MusicManage.Instance()      '用来添加音乐下载目录到监控目录中。
            Dim fInfo As FolderInfo = manager.MusicDB.Get_MonitoredFolder(strMusicFolder)

            If fInfo Is Nothing Then
                fInfo = FolderInfo.CreateOtherFolderInfo(strMusicFolder)
            End If
            fInfo.MonitorState = MonitorState.Monitor_True

            Dim lstFile As New List(Of String)
            Try
                For Each file As String In Directory.GetFiles(strMusicFolder.TrimEnd("\") & "\", "*.mp3", SearchOption.AllDirectories)
                    lstFile.Add(file)
                    Application.DoEvents()
                Next
            Catch ex As Exception

            End Try

            fInfo.SongList.AddRange(lstFile)
            manager.AddMusicFolder(fInfo)
        End If

    End Sub

    Private Function SaveFolder(ByVal strOldPath As String, ByVal strNewPath As String, ByVal type As FolderType) As String
        Dim strReturn As String = strNewPath.Trim().TrimEnd("\")
        strOldPath = strOldPath.Trim().TrimEnd("\")

        '"附加的其他软件目录"可以为空。除此之外的目录不能为空。
        If strReturn.Length > 0 OrElse _
           (type = FolderType.Other AndAlso String.Compare(strOldPath, strReturn, True) <> 0) Then

            If (strReturn.Length > 0 AndAlso String.Compare(strOldPath, strReturn, True) <> 0) OrElse _
                (type = FolderType.Other AndAlso String.Compare(strOldPath, strReturn, True) <> 0) Then

                Folder.CheckFolder(strReturn)

                Select Case type
                    Case FolderType.iTunes
                        IniSetting.SetiTunesFolder(strReturn)
                        Me.AddNewAppFolder(strReturn)

                    Case FolderType.Other
                        IniSetting.SetOtherFolder(strReturn)
                        Me.AddNewAppFolder(strReturn)

                    Case FolderType.App
                        IniSetting.SetDownloadSoftFolder(strReturn)
                        Me.AddNewAppFolder(strReturn)

                    Case FolderType.Music
                        IniSetting.SetDownloadMusicFolder(strReturn)
                        Me.GetFolderMusic(strReturn)

#If IS_ITONG Then

                    Case FolderType.Ringtone
                        IniSetting.SetDownloadRingtoneFolder(strReturn)
                        frmRingtoneLocal.FileWatcher.Path = strReturn

                    Case FolderType.Wallpaper
                        IniSetting.SetDownloadWallpaperFolder(strReturn)
                        frmWallpaperLocal.FileWatcher.Path = strReturn
#End If
                    Case FolderType.Firmware
                        IniSetting.SetDownloadFirmwareFolder(strReturn)

                End Select

                'Folder.CheckFolder(strReturn)
            End If
        End If
        Return strReturn
    End Function

    Private Sub AddNewAppFolder(ByVal strNewFolder As String)
        If strNewFolder.Length = 0 Then
            Return
        End If

        If String.Compare(strNewFolder, Me.mFolderiTunes, True) <> 0 AndAlso _
           String.Compare(strNewFolder, Me.mFolderOther, True) <> 0 AndAlso _
           String.Compare(strNewFolder, Me.mFolderDownload, True) <> 0 Then

            Me.mLstNewFolders.Add(strNewFolder)
        End If
    End Sub

    Private Sub LoadAppFromNewFolders(ByVal lstNewFolders As List(Of String))
        If lstNewFolders Is Nothing OrElse lstNewFolders.Count = 0 Then
            Return
        End If

        '添加新软件
        Dim appList As New List(Of String)
        For Each folder As String In lstNewFolders
            Try
                '有些目录没有权限读取，暂时Try Catch处理
                If System.IO.Directory.Exists(folder) Then
                    appList.AddRange(My.Computer.FileSystem.GetFiles(folder, FileIO.SearchOption.SearchAllSubDirectories, "*.ipa", "*.pxl"))
                End If
            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try
        Next

        ApplePackageReader.Instance().AddPackage(appList)
    End Sub

    Private Sub CreateAutoRunButton()
        Try

            Me.chkAutoRun.Appearance = System.Windows.Forms.Appearance.Button
            Me.chkAutoRun.BackColor = System.Drawing.Color.Transparent
            Me.chkAutoRun.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
            Me.chkAutoRun.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
            Me.chkAutoRun.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
            Me.chkAutoRun.Location = New System.Drawing.Point(34, 236)
            Me.chkAutoRun.Name = "chkAutoRun"
            Me.chkAutoRun.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
            Me.chkAutoRun.Size = New System.Drawing.Size(104, 18)
            Me.chkAutoRun.TabIndex = 77
            Me.chkAutoRun.tbAdriftIconWhenHover = False
            Me.chkAutoRun.tbAutoSize = False
            Me.chkAutoRun.tbAutoSizeEx = True
            Me.chkAutoRun.tbIconCheckedMouseDown = Nothing
            Me.chkAutoRun.tbIconCheckedMouseHover = Nothing
            Me.chkAutoRun.tbIconCheckedMouseLeave = Nothing
            Me.chkAutoRun.tbIconCheckedState = iTong.Components.ImageState.FourState
            Me.chkAutoRun.tbIconHoldPlace = True
            Me.chkAutoRun.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
            Me.chkAutoRun.tbIconIndeterminateMouseDown = Nothing
            Me.chkAutoRun.tbIconIndeterminateMouseHover = Nothing
            Me.chkAutoRun.tbIconIndeterminateMouseLeave = Nothing
            Me.chkAutoRun.tbIconIndeterminateState = iTong.Components.ImageState.FourState
            Me.chkAutoRun.tbIconPlaceText = 1
            Me.chkAutoRun.tbIconUnCheckedMouseDown = Nothing
            Me.chkAutoRun.tbIconUnCheckedMouseHover = Nothing
            Me.chkAutoRun.tbIconUnCheckedMouseLeave = Nothing
            Me.chkAutoRun.tbIconUnCheckedState = iTong.Components.ImageState.FourState
            Me.chkAutoRun.tbImageBackground = Nothing
            Me.chkAutoRun.tbImageBackgroundState = iTong.Components.ImageState.TwoState
            Me.chkAutoRun.tbImageCheckedMouseDown = Nothing
            Me.chkAutoRun.tbImageCheckedMouseHover = Nothing
            Me.chkAutoRun.tbImageCheckedMouseLeave = Nothing
            Me.chkAutoRun.tbImageUnCheckedMouseDown = Nothing
            Me.chkAutoRun.tbImageUnCheckedMouseHover = Nothing
            Me.chkAutoRun.tbImageUnCheckedMouseLeave = Nothing
            Me.chkAutoRun.tbReadOnly = False
            Me.chkAutoRun.tbShadow = False
            Me.chkAutoRun.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
            Me.chkAutoRun.tbShadowOffset = New System.Drawing.Point(1, 1)
            Me.chkAutoRun.tbSplit = "3,3,3,3"
            Me.chkAutoRun.tbToolTip = ""
            Me.chkAutoRun.Text = "开机自动启动"
            Me.chkAutoRun.UseVisualStyleBackColor = False
            Me.pnlNormal.Controls.Add(Me.chkAutoRun)
        Catch ex As Exception

        End Try

    End Sub

#End Region

#Region "--- DNS设置 ---"

    Private Sub GetSystemDNS()
        Dim strDNS() As String = Common.GetDNS()
        If strDNS.Length = 1 Then
            Me.txtDNS1.Text = strDNS(0)
        ElseIf strDNS.Length > 1 Then
            Me.txtDNS1.Text = strDNS(0)
            Me.txtDNS2.Text = strDNS(1)
        End If

        If Me.txtDNS1.Text = "***************" AndAlso Me.txtDNS2.Text = "*******" Then
            Me.btnSetDefaultDNS.Enabled = False
        End If

        Dim lstDNS As List(Of String) = IniSetting.GetUserDNS()
        If lstDNS.Count <= 0 Then
            Me.btnSetUserDNS.Enabled = False
        End If
    End Sub

    Private Sub btnSetDefaultDNS_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSetDefaultDNS.Click
        Me.SetDNS("***************", "*******", True)
    End Sub

    Private Sub btnSetUserDNS_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSetUserDNS.Click
        Try
            Dim lstDNS As List(Of String) = IniSetting.GetUserDNS()
            If lstDNS.Count <> 2 Then
                Return
            End If
            Me.SetDNS(lstDNS(0), lstDNS(1), False)
        Catch ex As Exception
        End Try
    End Sub

    Private Sub SetDNS(ByVal strDNS1 As String, ByVal strDNS2 As String, ByVal blnDefault As Boolean)
        Common.Log(String.Format("设置DNS C:{0}  {1}", strDNS1, strDNS2))
        If Common.SetDNS(strDNS1, strDNS2) Then
            If blnDefault Then
                Me.btnSetUserDNS.Enabled = True
                Me.btnSetDefaultDNS.Enabled = False
                IniSetting.SetUserDNS(Me.txtDNS1.Text, Me.txtDNS2.Text)
            Else
                Me.btnSetUserDNS.Enabled = False
                Me.btnSetDefaultDNS.Enabled = True
                IniSetting.SetUserDNS("", "")
            End If

            Me.txtDNS1.Text = strDNS1
            Me.txtDNS2.Text = strDNS2
            Common.GetDNS(True)     '保存完成后刷新DNS（助手取DNS有缓存）
        Else
            '"修改失败"
            tbMessageBox.Show(Me, Me.Language.GetString("Setting.Message.UpdateFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1)
        End If
    End Sub

#End Region

#Region "--- Apple ID设置 ---"

    Private Sub InitApleIdGrid()
        Me.dgvAppleId = New tbDataGridViewEx
        With Me.dgvAppleId
            .SuspendLayout()
            .BackgroundColor = Color.White
            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.ForeColor = Color.Black
            cellStyle.SelectionBackColor = Color.FromArgb(233, 233, 255)

            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", "", 150, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)

            Dim colProgress As tbDataGridViewProgressColumn = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", "", 210, True, False, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False)

            Dim colOther As tbDataGridViewTextBoxColumn = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True)

            colName.DefaultCellStyle = cellStyle
            colProgress.DefaultCellStyle = cellStyle
            colOther.DefaultCellStyle = cellStyle

            .tbMouseHoverResponse = True
            .RowTemplate.Height = 30
            .Columns.Clear()
            .Columns.Add(colName)
            .Columns.Add(colProgress)
            .Columns.Add(colOther)
            .tbShowNoData = False
            .MultiSelect = False
            .ColumnHeadersVisible = False
            .ResumeLayout()
        End With

        RemoveHandler dgvAppleId.CellButtonClick, AddressOf dgvAppleId_CellButtonClick
        AddHandler dgvAppleId.CellButtonClick, AddressOf dgvAppleId_CellButtonClick
        RemoveHandler dgvAppleId.SelectionChanged, AddressOf dgvAppleId_SelectionChanged
        AddHandler dgvAppleId.SelectionChanged, AddressOf dgvAppleId_SelectionChanged

        Me.pnlAppleIDGrid.Controls.Add(Me.dgvAppleId)
    End Sub

    Private Sub dgvAppleId_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvAppleId.Rows(e.Cell.RowIndex)
            Dim strId As String = ""
            If row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is String Then
                strId = row.Tag
            End If

            Select Case e.CellButton
                Case CellButtonStyle.Backup         '设置默认
                    If strId = IniSetting.GetDefaultUserAccount() Then
                        Return
                    End If
                    IniSetting.SetDefaultUserAccount(strId)

                    Me.SetDefaultAccount()
                Case CellButtonStyle.Uninstall      '退出登陆                   
                    IniSetting.RemoveUserAccount(strId)
                    Me.RemoveLogin(strId)
                    Me.dgvAppleId.Rows.Remove(row)
                    Me.SetDefaultAccount()
                Case CellButtonStyle.Uninstall
                Case CellButtonStyle.Cancel
                Case CellButtonStyle.BackupFolder
                Case CellButtonStyle.Reinstall
                Case CellButtonStyle.Document
            End Select

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSettingV3_dgvAppleId_CellButtonClick")
        End Try
    End Sub

    Private Sub RemoveLogin(ByVal strAppleId As String)
        Try
            Dim dictLogin As Dictionary(Of String, AppleAccountInfo) = AppleDownload.AccountDict()
            If dictLogin.ContainsKey(strAppleId) Then
                dictLogin.Remove(strAppleId)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSettingV3_RemoveLogin")
        End Try
    End Sub

    Private Sub dgvAppleId_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs)

    End Sub

    Private Sub lblRegister_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblRegister.Click
        '"http://news.tongbu.com/80551.html"
        Common.OpenExplorer(WebUrl.PageAppleIdRegister)
    End Sub

    Private Sub lblClause_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblClause.Click
        Dim strUrl As String = WebUrl.PageAppleIdClause       '"http://www.tongbu.com/privacy/"
        If Utility.IsPanda Then
            strUrl = WebUrl.PageAppleIdClausePanda           '"http://www.yuyue111.com/privacy/"
        End If
        Common.OpenExplorer(strUrl)
    End Sub

    Private Sub btnLoginAppleId_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLoginAppleId.Click
        If Me.chkLoginCode.Checked <> IniSetting.GetBuyWithGSA() Then
            IniSetting.SetBuyWithGSA(Me.chkLoginCode.Checked)
        End If
        Me.Login()
    End Sub

    Private Sub txtPwd_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtPwd.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.Login()
        End If
    End Sub

    Private Sub SetDefaultAccount()
        Dim row As tbDataGridViewRow = Nothing
        For Each Item As tbDataGridViewRow In Me.dgvAppleId.Rows
            Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
            Dim strId As String = ""
            If Item IsNot Nothing AndAlso Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is String Then
                strId = Item.Tag
            End If

            If strId = IniSetting.GetDefaultUserAccount() Then
                row = Item
                cellProgress.tbTextBackup = Me.Language.GetString("Setting.Button.DefaultAccount")                  '"默认账号"
                cellProgress.tbButtonImgFirst = Nothing
            Else
                cellProgress.tbTextBackup = Me.Language.GetString("Setting.Button.SetDefaultAccount")               '"设为默认"
                cellProgress.tbButtonImgFirst = cellProgress.tbButtonImgSecond
            End If
        Next
        If row IsNot Nothing Then
            Me.dgvAppleId.Rows.Remove(row)
            Me.dgvAppleId.Rows.Insert(0, row)
        End If
    End Sub

    Private Sub GetAppleID()
        Try
            Me.dgvAppleId.Rows.Clear()
            Dim dictUser As Dictionary(Of String, String) = IniSetting.GetUserAccounts(True)
            If dictUser Is Nothing OrElse dictUser.Count <= 0 Then
                Return
            End If

            For Each Item As String In dictUser.Keys
                Dim intRow As Integer = Me.dgvAppleId.Rows.Add(Item, "")
                Dim row As tbDataGridViewRow = Me.dgvAppleId.Rows(intRow)
                row.Tag = Item      '记录当前行的苹果id
                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                cellProgress.tbProgressStyle = ProgressStyle.Button
                cellProgress.tbShowButtonFirst = True
                cellProgress.tbShowButtonSecond = True
                cellProgress.tbShowButtonThird = False
                cellProgress.tbTextBackup = Me.Language.GetString("Setting.Button.SetDefaultAccount")               '"设为默认"
                cellProgress.tbTextUnInstall = Me.Language.GetString("Setting.RadioButton.ExitForm")                '"退出"

                If Item = IniSetting.GetDefaultUserAccount() Then
                    cellProgress.tbTextBackup = Me.Language.GetString("Setting.Button.DefaultAccount")              '"默认账号"
                    cellProgress.tbButtonImgFirst = Nothing
                    If row.Index <> 0 Then      '称到第一位
                        Me.dgvAppleId.Rows.Remove(row)
                        Me.dgvAppleId.Rows.Insert(0, row)
                        cellProgress.tbShowButtonText = True
                    End If
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSettingV3_GetAppleID")
        End Try
    End Sub

    Private Sub Login()
        Try
            If Not Me.CheckInput() Then
                Return
            End If

            If Me.mTdLogin IsNot Nothing AndAlso Me.mTdLogin.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            '登陆前清空先前登陆的记录
            Dim strAppleId As String = frmiTunesLoginV3.GetAppleIdFromTextBox(Me.txtId)
            'If CoreUpdateHelper.Instance().AccountDict().ContainsKey(strAppleId) Then
            '    CoreUpdateHelper.Instance().AccountDict().Remove(strAppleId)
            'End If

            AppleDownload.LogoutAppleId(strAppleId, True)

            Me.mTdLogin = New Thread(New ThreadStart(AddressOf DoLogin))
            Me.mTdLogin.IsBackground = True
            Me.mTdLogin.SetApartmentState(ApartmentState.STA)
            Me.mTdLogin.Start()

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSettingV3_Login")
        End Try
    End Sub

    Private Sub DoLogin()
        Try
            Me.SetViewInLogining(True)
            Dim state As SoftDownloadErrorState = CoreUpdateHelper.Instance().Login(frmiTunesLoginV3.GetAppleIdFromTextBox(Me.txtId), frmiTunesLoginV3.GetPasswordFromTextBox(Me.txtPwd))
            Me.SetLoginStatus(state)
            Me.SetViewInLogining(False)

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSettingV3_DoLogin")
        End Try
    End Sub

    Private Delegate Sub SetLoginStatusHandler(ByVal state As SoftDownloadErrorState)
    Private Sub SetLoginStatus(ByVal state As SoftDownloadErrorState)
        If Me.InvokeRequired Then
            Me.Invoke(New SetLoginStatusHandler(AddressOf SetLoginStatus), state)
        Else
            Dim strTxtAppleId As String = frmiTunesLoginV3.GetAppleIdFromTextBox(Me.txtId)
            Dim strPwd As String = frmiTunesLoginV3.GetPasswordFromTextBox(Me.txtPwd)
            If state = SoftDownloadErrorState.Succeed Then
                IniSetting.AddUserAccount(strTxtAppleId, strPwd)
            End If

            Select Case state
                Case SoftDownloadErrorState.Succeed
                    Me.SetLoginMessage(Me.Language.GetString("App.Message.BindingSucceed"), Color.Green)    '"绑定成功！"
                    Me.GetAppleID()

                Case SoftDownloadErrorState.LoginFailedWithAppleIdOrPasswordError
                    Me.SetLoginMessage(CoreUpdateHelper.GetErrorDescriptionByState(state, True), Color.Red)
                    Me.txtPwd.Focus()
                    Me.picPwd.Visible = True

                Case SoftDownloadErrorState.LoginFailedWithNetworkError, _
                     SoftDownloadErrorState.LoginFailedWithActionSignature, _
                     SoftDownloadErrorState.iTunesLowVersion, _
                     SoftDownloadErrorState.LoginFailedWithiTunesOffsetUnknown, _
                     SoftDownloadErrorState.LoginFailedWithAppleIdDisabled

                    Me.SetLoginMessage(CoreUpdateHelper.GetErrorDescriptionByState(state, True), Color.Red)

                Case Else
                    Me.SetLoginMessage(CoreUpdateHelper.GetErrorDescriptionByState(state, True), Color.Red)

            End Select
        End If
    End Sub

    Private Delegate Sub SetViewInLoginingHandler(ByVal isLogin As Boolean)
    Private Sub SetViewInLogining(ByVal isLogin As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewInLoginingHandler(AddressOf SetViewInLogining), isLogin)
        Else
            Me.txtId.Enabled = Not isLogin
            Me.txtPwd.Enabled = Not isLogin
            Me.btnLoginAppleId.Enabled = Not isLogin

            If isLogin Then
                '登陆的时候控件选中不可用状态会有蓝色底不好看，所在登陆的时候不选中
                Me.txtId.SelectionLength = 0
                Me.SetLoginMessage(Me.Language.GetString("App.Lable.Logging"), Color.FromArgb(107, 116, 123)) '"正在登录，请稍候 ..."

                Me.pbLoading.Visible = True
            Else
                Me.lblAppleIdMessage.ForeColor = Color.Red
                Me.pbLoading.Visible = False
            End If
        End If
    End Sub

    Private Function CheckInput() As Boolean
        Dim isSucceed As Boolean = False
        Me.picId.Visible = False
        Me.picPwd.Visible = False
        Me.picCheck.Visible = False

        If Not Common.NetworkIsAvailable Then
            Me.SetLoginMessage(Me.Language.GetString("Welcome.Label.NetDisable"), Color.Red)
            Return isSucceed
        End If

        If Me.txtId.Text.Trim.Length = 0 Then
            Me.txtId.Focus()
            Me.SetLoginMessage(Me.Language.GetString("App.Lable.EnterAccount"), Color.Red) '"请输入账号"
            Me.picId.Visible = True
            Return isSucceed
        End If

        If ServerIniSetting.GetCheckAppleIdIsEmail AndAlso Not Utility.CheckIsEmail(frmiTunesLoginV3.GetAppleIdFromTextBox(Me.txtId)) Then
            Me.txtId.Focus()
            Me.SetLoginMessage(CoreUpdateHelper.GetErrorDescriptionByState(SoftDownloadErrorState.LoginFailedWithAppleIdUnCorrect, True), Color.Red)
            Me.picId.Visible = True
            Return isSucceed
        End If

        If Me.txtPwd.Text.Trim.Length = 0 Then
            Me.txtPwd.Focus()
            Me.SetLoginMessage(Me.Language.GetString("App.Lable.EnterPassword"), Color.Red)    '"请输入密码"
            Me.picPwd.Visible = True
            Return isSucceed
        End If

        If Folder.LangType = LanguageType.zh_CN AndAlso Me.chkAgree.Checked = False Then
            Me.SetLoginMessage(Me.Language.GetString("App.Message.ReadAndAgree"), Color.Red)    '"请阅读并同意隐私条款。"
            Me.picCheck.Visible = True
            Return isSucceed
        End If

        isSucceed = True
        Return isSucceed
    End Function

    Private Sub SetLoginMessage(ByVal strText As String, ByVal color As Color)
        If Me.lblAppleIdMessage.tbTextColor <> color Then
            Me.lblAppleIdMessage.tbTextColor = color
        End If
        Me.lblAppleIdMessage.Text = strText
        Me.lblAppleIdMessage.tbToolTip = strText
        Me.lblAppleIdMessage.Refresh()
    End Sub

#End Region

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_sub
        End If
    End Sub

    Private Sub cbxLog_CheckedChanged(sender As Object, e As EventArgs) Handles cbxLog.CheckedChanged
        If cbxLog.Checked Then
            Try
                If Not File.Exists(Path.Combine(Folder.AppFolder, "debug.dll")) Then
                    File.Create(Path.Combine(Folder.AppFolder, "debug.dll")).Close()
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "cbxLog_CheckedChanged_Create")
            End Try
        Else
            Try
                If File.Exists(Path.Combine(Folder.AppFolder, "debug.dll")) Then
                    File.Delete(Path.Combine(Folder.AppFolder, "debug.dll"))
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "cbxLog_CheckedChanged_Delete")
            End Try
        End If
    End Sub

    Private Sub btnKnowiWechatAssistant_Click(sender As Object, e As EventArgs) Handles btnKnowiWechatAssistant.Click
        Common.OpenExplorer("https://at.umeng.com/SnyO1n?cid=6768")
    End Sub
End Class