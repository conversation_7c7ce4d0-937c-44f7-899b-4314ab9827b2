﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmVideoConvert
    Inherits frmDeviceBase


    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmVideoConvert))
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.btnSelectVideo = New iTong.Components.tbButton()
        Me.btnStart = New iTong.Components.tbButton()
        Me.btnStop = New iTong.Components.tbButton()
        Me.btnDelete = New iTong.Components.tbButton()
        Me.pnlMain = New System.Windows.Forms.Panel()
        Me.pnlImportVideo = New iTong.Components.tbPanel()
        Me.txtDescription = New iTong.Components.tbLabel()
        Me.picSelectVideo = New System.Windows.Forms.PictureBox()
        Me.btnImportVideo = New iTong.Components.tbButton()
        Me.btn_Visible = New iTong.Components.tbButton()
        Me.btnAddToDevice = New iTong.Components.tbButton()
        Me.btnOpenLocal = New iTong.Components.tbButton()
        Me.menuVideoConvert = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpenExplorer = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiStart = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiStop = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.lblMessage = New iTong.Components.tbLabel()
        Me.pnlTop.SuspendLayout()
        Me.pnlMain.SuspendLayout()
        Me.pnlImportVideo.SuspendLayout()
        CType(Me.picSelectVideo, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.menuVideoConvert.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(736, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(712, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(688, 0)
        '
        'pnlTop
        '
        Me.pnlTop.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.btnSelectVideo)
        Me.pnlTop.Controls.Add(Me.btnStart)
        Me.pnlTop.Controls.Add(Me.btnStop)
        Me.pnlTop.Controls.Add(Me.btnDelete)
        Me.pnlTop.Location = New System.Drawing.Point(1, 32)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(758, 32)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 23
        Me.pnlTop.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnSelectVideo
        '
        Me.btnSelectVideo.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSelectVideo.BackColor = System.Drawing.Color.Transparent
        Me.btnSelectVideo.BindingForm = Nothing
        Me.btnSelectVideo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSelectVideo.Location = New System.Drawing.Point(7, 5)
        Me.btnSelectVideo.Name = "btnSelectVideo"
        Me.btnSelectVideo.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnSelectVideo.Selectable = True
        Me.btnSelectVideo.Size = New System.Drawing.Size(71, 23)
        Me.btnSelectVideo.TabIndex = 44
        Me.btnSelectVideo.tbAdriftIconWhenHover = False
        Me.btnSelectVideo.tbAutoSize = False
        Me.btnSelectVideo.tbAutoSizeEx = True
        Me.btnSelectVideo.tbBackgroundImage = Nothing
        Me.btnSelectVideo.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSelectVideo.tbBadgeNumber = 0
        Me.btnSelectVideo.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSelectVideo.tbEndEllipsis = False
        Me.btnSelectVideo.tbIconHoldPlace = True
        Me.btnSelectVideo.tbIconImage = Global.iTong.My.Resources.Resources.btn_selectvideo_4
        Me.btnSelectVideo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSelectVideo.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnSelectVideo.tbIconMore = False
        Me.btnSelectVideo.tbIconMouseDown = Nothing
        Me.btnSelectVideo.tbIconMouseHover = Nothing
        Me.btnSelectVideo.tbIconMouseLeave = Nothing
        Me.btnSelectVideo.tbIconPlaceText = 2
        Me.btnSelectVideo.tbIconReadOnly = Nothing
        Me.btnSelectVideo.tbImageMouseDown = Nothing
        Me.btnSelectVideo.tbImageMouseHover = Nothing
        Me.btnSelectVideo.tbImageMouseLeave = Nothing
        Me.btnSelectVideo.tbProgressValue = 50
        Me.btnSelectVideo.tbReadOnly = False
        Me.btnSelectVideo.tbReadOnlyText = False
        Me.btnSelectVideo.tbShadow = False
        Me.btnSelectVideo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnSelectVideo.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnSelectVideo.tbShowDot = False
        Me.btnSelectVideo.tbShowMoreIconImg = CType(resources.GetObject("btnSelectVideo.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSelectVideo.tbShowNew = False
        Me.btnSelectVideo.tbShowProgress = False
        Me.btnSelectVideo.tbShowTip = True
        Me.btnSelectVideo.tbShowToolTipOnButton = False
        Me.btnSelectVideo.tbSplit = "13,11,13,11"
        Me.btnSelectVideo.tbText = "选择视频"
        Me.btnSelectVideo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectVideo.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnSelectVideo.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnSelectVideo.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnSelectVideo.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnSelectVideo.tbTextMouseDownPlace = 2
        Me.btnSelectVideo.tbToolTip = ""
        Me.btnSelectVideo.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSelectVideo.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSelectVideo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectVideo.VisibleEx = True
        '
        'btnStart
        '
        Me.btnStart.BackColor = System.Drawing.Color.Transparent
        Me.btnStart.BindingForm = Nothing
        Me.btnStart.Enabled = False
        Me.btnStart.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStart.Location = New System.Drawing.Point(104, 5)
        Me.btnStart.Name = "btnStart"
        Me.btnStart.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStart.Selectable = True
        Me.btnStart.Size = New System.Drawing.Size(57, 23)
        Me.btnStart.TabIndex = 17
        Me.btnStart.TabStop = False
        Me.btnStart.tbAdriftIconWhenHover = False
        Me.btnStart.tbAutoSize = False
        Me.btnStart.tbAutoSizeEx = True
        Me.btnStart.tbBackgroundImage = Nothing
        Me.btnStart.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStart.tbBadgeNumber = 0
        Me.btnStart.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStart.tbEndEllipsis = False
        Me.btnStart.tbIconHoldPlace = True
        Me.btnStart.tbIconImage = Global.iTong.My.Resources.Resources.btn_play_4
        Me.btnStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnStart.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnStart.tbIconMore = False
        Me.btnStart.tbIconMouseDown = Nothing
        Me.btnStart.tbIconMouseHover = Nothing
        Me.btnStart.tbIconMouseLeave = Nothing
        Me.btnStart.tbIconPlaceText = 2
        Me.btnStart.tbIconReadOnly = Nothing
        Me.btnStart.tbImageMouseDown = Nothing
        Me.btnStart.tbImageMouseHover = Nothing
        Me.btnStart.tbImageMouseLeave = Nothing
        Me.btnStart.tbProgressValue = 50
        Me.btnStart.tbReadOnly = False
        Me.btnStart.tbReadOnlyText = False
        Me.btnStart.tbShadow = False
        Me.btnStart.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStart.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStart.tbShowDot = False
        Me.btnStart.tbShowMoreIconImg = CType(resources.GetObject("btnStart.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStart.tbShowNew = False
        Me.btnStart.tbShowProgress = False
        Me.btnStart.tbShowTip = True
        Me.btnStart.tbShowToolTipOnButton = False
        Me.btnStart.tbSplit = "3,3,3,3"
        Me.btnStart.tbText = "开始"
        Me.btnStart.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnStart.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnStart.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnStart.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnStart.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnStart.tbTextMouseDownPlace = 2
        Me.btnStart.tbToolTip = ""
        Me.btnStart.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStart.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStart.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.VisibleEx = True
        '
        'btnStop
        '
        Me.btnStop.BackColor = System.Drawing.Color.Transparent
        Me.btnStop.BindingForm = Nothing
        Me.btnStop.Enabled = False
        Me.btnStop.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStop.Location = New System.Drawing.Point(169, 5)
        Me.btnStop.Name = "btnStop"
        Me.btnStop.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStop.Selectable = True
        Me.btnStop.Size = New System.Drawing.Size(57, 23)
        Me.btnStop.TabIndex = 17
        Me.btnStop.TabStop = False
        Me.btnStop.tbAdriftIconWhenHover = False
        Me.btnStop.tbAutoSize = False
        Me.btnStop.tbAutoSizeEx = True
        Me.btnStop.tbBackgroundImage = Nothing
        Me.btnStop.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStop.tbBadgeNumber = 0
        Me.btnStop.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStop.tbEndEllipsis = False
        Me.btnStop.tbIconHoldPlace = True
        Me.btnStop.tbIconImage = Global.iTong.My.Resources.Resources.btn_stop_4
        Me.btnStop.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnStop.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnStop.tbIconMore = False
        Me.btnStop.tbIconMouseDown = Nothing
        Me.btnStop.tbIconMouseHover = Nothing
        Me.btnStop.tbIconMouseLeave = Nothing
        Me.btnStop.tbIconPlaceText = 2
        Me.btnStop.tbIconReadOnly = Nothing
        Me.btnStop.tbImageMouseDown = Nothing
        Me.btnStop.tbImageMouseHover = Nothing
        Me.btnStop.tbImageMouseLeave = Nothing
        Me.btnStop.tbProgressValue = 50
        Me.btnStop.tbReadOnly = False
        Me.btnStop.tbReadOnlyText = False
        Me.btnStop.tbShadow = False
        Me.btnStop.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStop.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStop.tbShowDot = False
        Me.btnStop.tbShowMoreIconImg = CType(resources.GetObject("btnStop.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStop.tbShowNew = False
        Me.btnStop.tbShowProgress = False
        Me.btnStop.tbShowTip = True
        Me.btnStop.tbShowToolTipOnButton = False
        Me.btnStop.tbSplit = "3,3,3,3"
        Me.btnStop.tbText = "暂停"
        Me.btnStop.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnStop.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnStop.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnStop.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnStop.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnStop.tbTextMouseDownPlace = 2
        Me.btnStop.tbToolTip = ""
        Me.btnStop.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStop.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStop.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStop.VisibleEx = True
        '
        'btnDelete
        '
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Enabled = False
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(234, 5)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(57, 23)
        Me.btnDelete.TabIndex = 17
        Me.btnDelete.TabStop = False
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = True
        Me.btnDelete.tbBackgroundImage = Nothing
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "3,3,3,3"
        Me.btnDelete.tbText = "删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDelete.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDelete.tbTextMouseDownPlace = 2
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(236, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.pnlMain.Controls.Add(Me.pnlImportVideo)
        Me.pnlMain.Location = New System.Drawing.Point(1, 64)
        Me.pnlMain.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(758, 452)
        Me.pnlMain.TabIndex = 22
        '
        'pnlImportVideo
        '
        Me.pnlImportVideo.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlImportVideo.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlImportVideo.Controls.Add(Me.txtDescription)
        Me.pnlImportVideo.Controls.Add(Me.picSelectVideo)
        Me.pnlImportVideo.Controls.Add(Me.btnImportVideo)
        Me.pnlImportVideo.Location = New System.Drawing.Point(0, 0)
        Me.pnlImportVideo.Name = "pnlImportVideo"
        Me.pnlImportVideo.Size = New System.Drawing.Size(758, 452)
        Me.pnlImportVideo.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlImportVideo.TabIndex = 33
        Me.pnlImportVideo.tbBackgroundImage = Nothing
        Me.pnlImportVideo.tbShowWatermark = False
        Me.pnlImportVideo.tbSplit = "0,0,0,0"
        Me.pnlImportVideo.tbWatermark = Nothing
        Me.pnlImportVideo.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlImportVideo.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'txtDescription
        '
        Me.txtDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.txtDescription.Location = New System.Drawing.Point(105, 228)
        Me.txtDescription.Name = "txtDescription"
        Me.txtDescription.Size = New System.Drawing.Size(549, 23)
        Me.txtDescription.TabIndex = 2
        Me.txtDescription.tbAdriftWhenHover = False
        Me.txtDescription.tbAutoEllipsis = False
        Me.txtDescription.tbAutoSize = False
        Me.txtDescription.tbHideImage = False
        Me.txtDescription.tbIconImage = Nothing
        Me.txtDescription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.txtDescription.tbIconPlaceText = 5
        Me.txtDescription.tbShadow = False
        Me.txtDescription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.txtDescription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.txtDescription.tbShowScrolling = False
        Me.txtDescription.Text = "将 avi，flv，rmvb，wav，mov，mkv，f4v 格式的视频转换为 mp4。"
        Me.txtDescription.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picSelectVideo
        '
        Me.picSelectVideo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picSelectVideo.Image = Global.iTong.My.Resources.Resources.icon_video_100
        Me.picSelectVideo.Location = New System.Drawing.Point(279, 123)
        Me.picSelectVideo.Name = "picSelectVideo"
        Me.picSelectVideo.Size = New System.Drawing.Size(200, 103)
        Me.picSelectVideo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picSelectVideo.TabIndex = 1
        Me.picSelectVideo.TabStop = False
        '
        'btnImportVideo
        '
        Me.btnImportVideo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnImportVideo.BackColor = System.Drawing.Color.Transparent
        Me.btnImportVideo.BindingForm = Nothing
        Me.btnImportVideo.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImportVideo.Location = New System.Drawing.Point(281, 252)
        Me.btnImportVideo.Name = "btnImportVideo"
        Me.btnImportVideo.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImportVideo.Selectable = True
        Me.btnImportVideo.Size = New System.Drawing.Size(196, 39)
        Me.btnImportVideo.TabIndex = 0
        Me.btnImportVideo.tbAdriftIconWhenHover = False
        Me.btnImportVideo.tbAutoSize = False
        Me.btnImportVideo.tbAutoSizeEx = False
        Me.btnImportVideo.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnImportVideo.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnImportVideo.tbBadgeNumber = 0
        Me.btnImportVideo.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImportVideo.tbEndEllipsis = False
        Me.btnImportVideo.tbIconHoldPlace = True
        Me.btnImportVideo.tbIconImage = Nothing
        Me.btnImportVideo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportVideo.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnImportVideo.tbIconMore = False
        Me.btnImportVideo.tbIconMouseDown = Nothing
        Me.btnImportVideo.tbIconMouseHover = Nothing
        Me.btnImportVideo.tbIconMouseLeave = Nothing
        Me.btnImportVideo.tbIconPlaceText = 2
        Me.btnImportVideo.tbIconReadOnly = Nothing
        Me.btnImportVideo.tbImageMouseDown = Nothing
        Me.btnImportVideo.tbImageMouseHover = Nothing
        Me.btnImportVideo.tbImageMouseLeave = Nothing
        Me.btnImportVideo.tbProgressValue = 50
        Me.btnImportVideo.tbReadOnly = False
        Me.btnImportVideo.tbReadOnlyText = False
        Me.btnImportVideo.tbShadow = False
        Me.btnImportVideo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImportVideo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImportVideo.tbShowDot = False
        Me.btnImportVideo.tbShowMoreIconImg = CType(resources.GetObject("btnImportVideo.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImportVideo.tbShowNew = False
        Me.btnImportVideo.tbShowProgress = False
        Me.btnImportVideo.tbShowTip = True
        Me.btnImportVideo.tbShowToolTipOnButton = False
        Me.btnImportVideo.tbSplit = "3,3,3,3"
        Me.btnImportVideo.tbText = "请选择视频文件"
        Me.btnImportVideo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportVideo.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImportVideo.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImportVideo.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImportVideo.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnImportVideo.tbTextMouseDownPlace = 0
        Me.btnImportVideo.tbToolTip = ""
        Me.btnImportVideo.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImportVideo.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImportVideo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImportVideo.VisibleEx = True
        '
        'btn_Visible
        '
        Me.btn_Visible.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_Visible.BackColor = System.Drawing.Color.Transparent
        Me.btn_Visible.BindingForm = Nothing
        Me.btn_Visible.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_Visible.Location = New System.Drawing.Point(734, 3)
        Me.btn_Visible.Name = "btn_Visible"
        Me.btn_Visible.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_Visible.Selectable = True
        Me.btn_Visible.Size = New System.Drawing.Size(24, 24)
        Me.btn_Visible.TabIndex = 24
        Me.btn_Visible.tbAdriftIconWhenHover = False
        Me.btn_Visible.tbAutoSize = False
        Me.btn_Visible.tbAutoSizeEx = False
        Me.btn_Visible.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_Visible.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_Visible.tbBadgeNumber = 0
        Me.btn_Visible.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_Visible.tbEndEllipsis = False
        Me.btn_Visible.tbIconHoldPlace = True
        Me.btn_Visible.tbIconImage = Nothing
        Me.btn_Visible.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_Visible.tbIconMore = False
        Me.btn_Visible.tbIconMouseDown = Nothing
        Me.btn_Visible.tbIconMouseHover = Nothing
        Me.btn_Visible.tbIconMouseLeave = Nothing
        Me.btn_Visible.tbIconPlaceText = 2
        Me.btn_Visible.tbIconReadOnly = Nothing
        Me.btn_Visible.tbImageMouseDown = Nothing
        Me.btn_Visible.tbImageMouseHover = Nothing
        Me.btn_Visible.tbImageMouseLeave = Nothing
        Me.btn_Visible.tbProgressValue = 50
        Me.btn_Visible.tbReadOnly = False
        Me.btn_Visible.tbReadOnlyText = False
        Me.btn_Visible.tbShadow = False
        Me.btn_Visible.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_Visible.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_Visible.tbShowDot = False
        Me.btn_Visible.tbShowMoreIconImg = CType(resources.GetObject("btn_Visible.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_Visible.tbShowNew = False
        Me.btn_Visible.tbShowProgress = False
        Me.btn_Visible.tbShowTip = True
        Me.btn_Visible.tbShowToolTipOnButton = False
        Me.btn_Visible.tbSplit = "3,3,3,3"
        Me.btn_Visible.tbText = ""
        Me.btn_Visible.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.tbTextColor = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorDown = System.Drawing.Color.White
        Me.btn_Visible.tbTextColorHover = System.Drawing.Color.White
        Me.btn_Visible.tbTextMouseDownPlace = 0
        Me.btn_Visible.tbToolTip = ""
        Me.btn_Visible.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_Visible.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Visible.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Visible.VisibleEx = True
        '
        'btnAddToDevice
        '
        Me.btnAddToDevice.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAddToDevice.BackColor = System.Drawing.Color.Transparent
        Me.btnAddToDevice.BindingForm = Nothing
        Me.btnAddToDevice.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAddToDevice.Location = New System.Drawing.Point(643, 531)
        Me.btnAddToDevice.Name = "btnAddToDevice"
        Me.btnAddToDevice.Padding = New System.Windows.Forms.Padding(5, 1, 5, 0)
        Me.btnAddToDevice.Selectable = True
        Me.btnAddToDevice.Size = New System.Drawing.Size(93, 27)
        Me.btnAddToDevice.TabIndex = 41
        Me.btnAddToDevice.tbAdriftIconWhenHover = False
        Me.btnAddToDevice.tbAutoSize = False
        Me.btnAddToDevice.tbAutoSizeEx = True
        Me.btnAddToDevice.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnAddToDevice.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnAddToDevice.tbBadgeNumber = 0
        Me.btnAddToDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAddToDevice.tbEndEllipsis = False
        Me.btnAddToDevice.tbIconHoldPlace = True
        Me.btnAddToDevice.tbIconImage = Nothing
        Me.btnAddToDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddToDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAddToDevice.tbIconMore = False
        Me.btnAddToDevice.tbIconMouseDown = Nothing
        Me.btnAddToDevice.tbIconMouseHover = Nothing
        Me.btnAddToDevice.tbIconMouseLeave = Nothing
        Me.btnAddToDevice.tbIconPlaceText = 2
        Me.btnAddToDevice.tbIconReadOnly = Nothing
        Me.btnAddToDevice.tbImageMouseDown = Nothing
        Me.btnAddToDevice.tbImageMouseHover = Nothing
        Me.btnAddToDevice.tbImageMouseLeave = Nothing
        Me.btnAddToDevice.tbProgressValue = 50
        Me.btnAddToDevice.tbReadOnly = False
        Me.btnAddToDevice.tbReadOnlyText = False
        Me.btnAddToDevice.tbShadow = False
        Me.btnAddToDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnAddToDevice.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnAddToDevice.tbShowDot = False
        Me.btnAddToDevice.tbShowMoreIconImg = CType(resources.GetObject("btnAddToDevice.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAddToDevice.tbShowNew = False
        Me.btnAddToDevice.tbShowProgress = False
        Me.btnAddToDevice.tbShowTip = True
        Me.btnAddToDevice.tbShowToolTipOnButton = False
        Me.btnAddToDevice.tbSplit = "13,11,13,11"
        Me.btnAddToDevice.tbText = "Add to device"
        Me.btnAddToDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddToDevice.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnAddToDevice.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnAddToDevice.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnAddToDevice.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnAddToDevice.tbTextMouseDownPlace = 0
        Me.btnAddToDevice.tbToolTip = ""
        Me.btnAddToDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAddToDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAddToDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddToDevice.VisibleEx = True
        '
        'btnOpenLocal
        '
        Me.btnOpenLocal.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOpenLocal.BackColor = System.Drawing.Color.Transparent
        Me.btnOpenLocal.BindingForm = Nothing
        Me.btnOpenLocal.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOpenLocal.Location = New System.Drawing.Point(542, 531)
        Me.btnOpenLocal.Name = "btnOpenLocal"
        Me.btnOpenLocal.Padding = New System.Windows.Forms.Padding(5, 1, 5, 0)
        Me.btnOpenLocal.Selectable = True
        Me.btnOpenLocal.Size = New System.Drawing.Size(93, 27)
        Me.btnOpenLocal.TabIndex = 43
        Me.btnOpenLocal.tbAdriftIconWhenHover = False
        Me.btnOpenLocal.tbAutoSize = False
        Me.btnOpenLocal.tbAutoSizeEx = True
        Me.btnOpenLocal.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnOpenLocal.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOpenLocal.tbBadgeNumber = 0
        Me.btnOpenLocal.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpenLocal.tbEndEllipsis = False
        Me.btnOpenLocal.tbIconHoldPlace = True
        Me.btnOpenLocal.tbIconImage = Nothing
        Me.btnOpenLocal.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenLocal.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOpenLocal.tbIconMore = False
        Me.btnOpenLocal.tbIconMouseDown = Nothing
        Me.btnOpenLocal.tbIconMouseHover = Nothing
        Me.btnOpenLocal.tbIconMouseLeave = Nothing
        Me.btnOpenLocal.tbIconPlaceText = 2
        Me.btnOpenLocal.tbIconReadOnly = Nothing
        Me.btnOpenLocal.tbImageMouseDown = Nothing
        Me.btnOpenLocal.tbImageMouseHover = Nothing
        Me.btnOpenLocal.tbImageMouseLeave = Nothing
        Me.btnOpenLocal.tbProgressValue = 50
        Me.btnOpenLocal.tbReadOnly = False
        Me.btnOpenLocal.tbReadOnlyText = False
        Me.btnOpenLocal.tbShadow = False
        Me.btnOpenLocal.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOpenLocal.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOpenLocal.tbShowDot = False
        Me.btnOpenLocal.tbShowMoreIconImg = CType(resources.GetObject("btnOpenLocal.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOpenLocal.tbShowNew = False
        Me.btnOpenLocal.tbShowProgress = False
        Me.btnOpenLocal.tbShowTip = True
        Me.btnOpenLocal.tbShowToolTipOnButton = False
        Me.btnOpenLocal.tbSplit = "13,11,13,11"
        Me.btnOpenLocal.tbText = "Open location"
        Me.btnOpenLocal.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenLocal.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnOpenLocal.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnOpenLocal.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnOpenLocal.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnOpenLocal.tbTextMouseDownPlace = 0
        Me.btnOpenLocal.tbToolTip = ""
        Me.btnOpenLocal.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpenLocal.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpenLocal.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenLocal.VisibleEx = True
        '
        'menuVideoConvert
        '
        Me.menuVideoConvert.DropShadowEnabled = False
        Me.menuVideoConvert.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenExplorer, Me.tsmiStart, Me.tsmiStop, Me.tsmiDelete})
        Me.menuVideoConvert.Name = "menuInInstalGrid"
        Me.menuVideoConvert.Size = New System.Drawing.Size(137, 92)
        Me.menuVideoConvert.tbBackColor = System.Drawing.Color.White
        Me.menuVideoConvert.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuVideoConvert.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuVideoConvert.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenExplorer
        '
        Me.tsmiOpenExplorer.Name = "tsmiOpenExplorer"
        Me.tsmiOpenExplorer.Size = New System.Drawing.Size(136, 22)
        Me.tsmiOpenExplorer.Text = "打开文件夹"
        '
        'tsmiStart
        '
        Me.tsmiStart.Name = "tsmiStart"
        Me.tsmiStart.Size = New System.Drawing.Size(136, 22)
        Me.tsmiStart.Text = "开始"
        '
        'tsmiStop
        '
        Me.tsmiStop.Name = "tsmiStop"
        Me.tsmiStop.Size = New System.Drawing.Size(136, 22)
        Me.tsmiStop.Text = "暂停"
        '
        'tsmiDelete
        '
        Me.tsmiDelete.Name = "tsmiDelete"
        Me.tsmiDelete.Size = New System.Drawing.Size(136, 22)
        Me.tsmiDelete.Text = "删除"
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblMessage.AutoEllipsis = True
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblMessage.Location = New System.Drawing.Point(12, 534)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(498, 21)
        Me.lblMessage.TabIndex = 44
        Me.lblMessage.tbAdriftWhenHover = False
        Me.lblMessage.tbAutoEllipsis = True
        Me.lblMessage.tbAutoSize = False
        Me.lblMessage.tbHideImage = False
        Me.lblMessage.tbIconImage = Nothing
        Me.lblMessage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.tbIconPlaceText = 5
        Me.lblMessage.tbShadow = False
        Me.lblMessage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblMessage.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblMessage.tbShowScrolling = False
        Me.lblMessage.Text = "正在导入："""" 等N个文件"
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmVideoConvert
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(760, 570)
        Me.Controls.Add(Me.btnOpenLocal)
        Me.Controls.Add(Me.btnAddToDevice)
        Me.Controls.Add(Me.btn_Visible)
        Me.Controls.Add(Me.pnlTop)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.lblMessage)
        Me.FilletRadius = 5
        Me.MinimumSize = New System.Drawing.Size(16, 98)
        Me.Name = "frmVideoConvert"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbShowWatermark = True
        Me.tbSplit = "8,43,8,55"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "视频制作"
        Me.Controls.SetChildIndex(Me.lblMessage, 0)
        Me.Controls.SetChildIndex(Me.pnlMain, 0)
        Me.Controls.SetChildIndex(Me.pnlTop, 0)
        Me.Controls.SetChildIndex(Me.btn_Visible, 0)
        Me.Controls.SetChildIndex(Me.btnAddToDevice, 0)
        Me.Controls.SetChildIndex(Me.btnOpenLocal, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlTop.ResumeLayout(False)
        Me.pnlMain.ResumeLayout(False)
        Me.pnlImportVideo.ResumeLayout(False)
        CType(Me.picSelectVideo, System.ComponentModel.ISupportInitialize).EndInit()
        Me.menuVideoConvert.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlTop As tbPanel
    Friend WithEvents btnStart As tbButton
    Friend WithEvents btnStop As tbButton
    Friend WithEvents btnDelete As tbButton
    Friend WithEvents pnlMain As System.Windows.Forms.Panel
    Friend WithEvents btn_Visible As tbButton
    Friend WithEvents btnSelectVideo As tbButton
    Friend WithEvents btnAddToDevice As tbButton
    Friend WithEvents btnOpenLocal As tbButton
    Private WithEvents pnlImportVideo As tbPanel
    Private WithEvents picSelectVideo As System.Windows.Forms.PictureBox
    Private WithEvents btnImportVideo As tbButton
    Friend WithEvents menuVideoConvert As tbContextMenuStrip
    Friend WithEvents tsmiOpenExplorer As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiStart As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiStop As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDelete As System.Windows.Forms.ToolStripMenuItem
    Protected WithEvents lblMessage As tbLabel
    Friend WithEvents txtDescription As tbLabel
End Class
