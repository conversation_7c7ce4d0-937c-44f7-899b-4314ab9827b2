﻿Public Class AuthorityWarningForm
    '取得系统语言包
    Declare Function GetSystemDefaultLCID Lib "kernel32" () As Integer

    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub AuthorityWarningForm_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        Me.Icon = My.Resources.iTong
      
        Dim strTitle As String = "权限错误"
        Dim strDescription As String = "很抱歉，iClover无法以非管理员身份运行。 " & _
                                        vbCrLf & vbCrLf & vbCrLf & _
                                        "如果您正在使用的是 Win7 或者 Vista 系统，并且以普通管理员身份登录，请按照下面的步骤开启同步助手的管理员权限： " & _
                                        vbCrLf & vbCrLf & _
                                        "1.在桌面 同步助手 的快捷方式上点击鼠标右键，选择属性。 " & _
                                        vbCrLf & vbCrLf & _
                                        "2.选中""兼容性""选项卡，在""特权等级""中的""以管理员身份运行此程序""选项打勾。 " & _
                                        vbCrLf & vbCrLf & _
                                        "3.点击""确定""按钮，然后重新启动 同步助手。 "
        Dim strCloseButtonText As String = "关闭"
        Dim strLanguage As String = IniSetting.GetLanguage
        '如果ini中没有语言信息就取操作系统的语言信息
        If strLanguage.Length = 0 Then
            strLanguage = GetSystemLang()
        End If

        If Not Folder.LangType = LanguageType.en_US Then
            strDescription = strDescription.Replace("iClover", "同步助手")
        End If

        If strLanguage = "zh-CN" Then
            '简体中文
        ElseIf strLanguage = "zh-TW" Then
            '繁体中文
            strTitle = "權限錯誤"
            strDescription = "很抱歉，iClover無法以非管理員身份運行。 " & _
                                            vbCrLf & vbCrLf & vbCrLf & _
                                            "如果您正在使用的是 Win7 或者 Vista 系統，並且以普通管理員身份登入，請按照下面的步驟開啟同步助手的管理員許可權： " & _
                                            vbCrLf & vbCrLf & _
                                            "1.在桌面 同步助手 的快捷方式上點擊滑鼠右鍵，選擇屬性。 " & _
                                            vbCrLf & vbCrLf & _
                                            "2.選中""相容性""選項卡，在""特權等級""中的""以管理員身份運行此程式""選項打勾。 " & _
                                            vbCrLf & vbCrLf & _
                                            "3.點擊""確定""按鈕，然後重新開機 同步助手。"
            If Not Folder.LangType = LanguageType.en_US Then
                strDescription = strDescription.Replace("iClover", "同步助手")
            End If
            strCloseButtonText = "關閉"
        ElseIf strLanguage = "ja-JP" Then
            '日语
            strTitle = "権限エーラー"
            strDescription = "申し訳ありません。iCloverの利用には管理者権限で実行しなければなりません。" & _
                                            vbCrLf & vbCrLf & vbCrLf & _
                                            "一般権限でWin7またはVistaシステムを使う場合、下記によってiTongの管理者権限を取得してください： " & _
                                            vbCrLf & vbCrLf & _
                                            "1.デスクトップにあるiTongアイコンを右クリックして、「プロパティ」をクリックしてください。" & _
                                            vbCrLf & vbCrLf & _
                                            "2.「互換性」をクリックして、一番下の「特権レベル」――「管理者としてこのプログラムを実行する」をチェックしてください " & _
                                            vbCrLf & vbCrLf & _
                                            "3.「OK」をクリックして、iTongを再起動してください"
            strCloseButtonText = "閉じる"
        Else
            '英文
            strTitle = "Permission Error"
            strDescription = "Sorry, iClover cannot run as a non-administrator." & _
                                            vbCrLf & vbCrLf & vbCrLf & _
                                            "If you are under Win7 or Vista System and logging as a normal administrator, please follow the three steps to open the administrator permission of iTong:" & _
                                            vbCrLf & vbCrLf & _
                                            "1. Right click iTong shortcut icon and select ""Properties""" & _
                                            vbCrLf & vbCrLf & _
                                            "2. Go to ""Compatibility"", Check the ""Run this program as an administrator"" box under ""Privilege Level""." & _
                                            vbCrLf & vbCrLf & _
                                            "3. Click ""OK"" and then restart iTong."
            strCloseButtonText = "Close"
        End If

        

        Me.Text = strTitle
        Me.btnClose.Text = strCloseButtonText
        Me.lbl_Info.Text = strDescription
    End Sub

    '取得系统语言信息
    Private Function GetSystemLang() As String
        Dim strLanguage As String = ""
        Dim intLan As Integer = GetSystemDefaultLCID()
        Select Case intLan
            Case &H404
                strLanguage = "zh-TW"
            Case &H804
                strLanguage = "zh-CN"
            Case Else
                strLanguage = "en-US"
        End Select
        Return strLanguage
    End Function

End Class