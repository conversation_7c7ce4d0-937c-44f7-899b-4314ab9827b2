﻿Imports System.Threading

Public Class frmSettingSkin
    Private mApplication As IApplication = Nothing
    Private mthrLoadSkins As Thread

    Public Sub New(ByVal app As IApplication)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = app
        Me.Language = app.Language
        Me.Icon = My.Resources.iTong
        Me.CanResize = False
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.StartPosition = FormStartPosition.CenterParent

        Me.mthrLoadSkins = New Thread(AddressOf LoadSkins)
        Me.mthrLoadSkins.IsBackground = True
        Me.mthrLoadSkins.Start()
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Skin.Label.Title")             '"皮肤"
        Me.btnClose.Text = Me.Language.GetString("Common.Button.Close") '"关闭"
    End Sub

    Private Sub btnClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClose.Click
        Me.Close()
    End Sub

    Private Sub LoadSkins()
        Try
            '加载的时候先解压皮肤文件。
            CType(Me.mApplication, MainForm).UnZipSkin()
            Dim lstFiles As New List(Of String)
            lstFiles.Add("Default")

            If Directory.Exists(Skin.Instance.SkinFolder) Then
                Dim skinFiles() As String = Directory.GetFiles(Skin.Instance.SkinFolder, "*.zip", SearchOption.TopDirectoryOnly)
                For Each Item As String In skinFiles
                    lstFiles.Add(Item)
                Next

                Dim skinFolders() As String = Directory.GetDirectories(Skin.Instance.SkinFolder, "*_Files", SearchOption.TopDirectoryOnly)
                For Each Item As String In skinFolders
                    '这个是皮肤包的名字，可以不用翻译
                    Dim strFileName As String = Item.Replace("_Files", ".zip")
                    If lstFiles.Contains(strFileName) Then
                        Continue For
                    End If
                    lstFiles.Add(strFileName)
                Next
            End If

            Me.CreateIcons(lstFiles, Skin.Instance.GetSkinSetting())
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSettingSkin_LoadSkins")
        End Try
    End Sub

    Private Delegate Sub CreateIconsHandler(ByVal lstSkinFiles As List(Of String), ByVal strCurrentSkin As String)
    Private Sub CreateIcons(ByVal lstSkinFiles As List(Of String), ByVal strCurrentSkin As String)
        If Me.InvokeRequired Then
            Me.Invoke(New CreateIconsHandler(AddressOf CreateIcons), lstSkinFiles, strCurrentSkin)
        Else
            Me.flpContainer.tbSuspendLayout()
            For Each strFilePath As String In lstSkinFiles
                Dim strName As String = Path.GetFileNameWithoutExtension(strFilePath)
                Dim item As New tbRadioButton()
                With item
                    .Name = strName
                    .Size = New Size(154, 106)

                    .tbIconCheckedMouseDown = My.Resources.skin_select
                    .tbIconCheckedMouseHover = My.Resources.skin_select
                    .tbIconCheckedMouseLeave = My.Resources.skin_select
                    .tbIconUnCheckedMouseHover = My.Resources.skin_select
                    .tbIconUnCheckedMouseDown = My.Resources.skin_select

                    .tbImageBackgroundState = ImageState.OneState
                    .tbImageBackground = Me.GetSkinSettingImage(strName)
                    .tbIconImageAlign = ContentAlignment.TopRight
                    .tbIconHoldPlace = False
                   
                    .Font = Me.flpContainer.Font
                    .ForeColor = Color.White
                    .ForeColor_Checked = Color.White
                    .TextAlign = ContentAlignment.BottomLeft
                    If strName = "Default" Then
                        .Text = Me.Language.GetString("Skin.Label.Default")         '"默认"
                    Else
                        .Text = Me.GetSkinLang(strName)
                    End If

                    .Padding = New Padding(10, 0, 0, 10)
                    .Margin = New Padding(20, 20, 0, 8)
                End With
                Me.flpContainer.Controls.Add(item)

                If strCurrentSkin = strName Then
                    item.Checked = True
                End If
                AddHandler item.CheckedChanged, AddressOf chk_Checked
            Next
            flpContainer.tbResumeLayout()
        End If
    End Sub

    Private Function GetSkinLang(ByVal strKey As String) As String
        Dim strLang As String = strKey
        '这个是皮肤包的名字，可以不用翻译
        Select Case strKey
            Case "Skin_Blue"
                strLang = Me.Language.GetString("Skin.Label.Blue")
            Case "Skin_Purple"
                strLang = Me.Language.GetString("Skin.Label.Purple")
            Case "Skin_Red"
                strLang = Me.Language.GetString("Skin.Label.Red")
        End Select
        Return strLang
    End Function

    Private Sub chk_Checked(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim chk As tbRadioButton = sender
        If chk Is Nothing OrElse chk.Checked = False Then
            Return
        End If
        Skin.Instance.SetSkinSetting(chk.Name)
        CType(Me.mApplication, MainForm).ReloadSkin()
    End Sub

    Private Function GetSkinSettingImage(ByVal strSkinName As String) As Image
        Dim imgReturn As Image = My.Resources.skin_default_setting
        Try
            Dim strPath As String = Path.Combine(Skin.Instance.SkinFolder, strSkinName & "_Files\skin_setting.png")
            If File.Exists(strPath) Then
                imgReturn = Utility.GetImageFormFile(strPath)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSettingSkin_GetSkinSettingImage")
        End Try
        Return imgReturn
    End Function

    '教程显示
    Public Shared Sub ShowForm(ByVal app As IApplication)
        Dim frm As frmSettingSkin = Nothing
        For Each Item As Form In My.Application.OpenForms()
            If TypeOf Item Is frmSettingSkin Then
                frm = Item
                Exit For
            End If
        Next

        If frm IsNot Nothing Then
            frm.Activate()
            frm.WindowState = FormWindowState.Normal
            frm.BringToFront()
        Else
            frm = New frmSettingSkin(app)
            frm.Show(app)
        End If
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.btnClose.tbBackgroundImage = My.Resources.btn_4_blue
    End Sub

End Class