﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmAbout
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.btn_close = New tbButton
        Me.lblCopyright = New tbLabel
        Me.lblVersion = New tbLabel
        Me.lblHomeValue = New System.Windows.Forms.LinkLabel
        Me.lblSescription = New tbLabel
        Me.pnlBottom = New tbPanel
        Me.lblTongbu = New tbLabel
        Me.lblAttention = New tbLabel
        Me.pciQQweibo = New System.Windows.Forms.PictureBox
        Me.pciSinaweibo = New System.Windows.Forms.PictureBox
        Me.pnlBottom.SuspendLayout()
        CType(Me.pciQQweibo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pciSinaweibo, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(586, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 8
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = My.Resources.btn_close1
        Me.btn_close.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'lblCopyright
        '
        Me.lblCopyright.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblCopyright.BackColor = System.Drawing.Color.Transparent
        Me.lblCopyright.ForeColor = System.Drawing.Color.White
        Me.lblCopyright.Location = New System.Drawing.Point(92, 42)
        Me.lblCopyright.Name = "lblCopyright"
        Me.lblCopyright.Size = New System.Drawing.Size(429, 31)
        Me.lblCopyright.TabIndex = 42
        Me.lblCopyright.tbAdriftWhenHover = False
        Me.lblCopyright.tbAutoEllipsis = False
        Me.lblCopyright.tbAutoSize = False
        Me.lblCopyright.tbHideImage = False
        Me.lblCopyright.tbIconImage = Nothing
        Me.lblCopyright.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCopyright.tbIconPlaceText = 5
        Me.lblCopyright.tbShadow = False
        Me.lblCopyright.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCopyright.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCopyright.tbShowScrolling = False
        Me.lblCopyright.Text = "Copyright © 2013 TongBu.com, All rights reserved." & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "同步网络平台 版权所有" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.lblCopyright.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblVersion
        '
        Me.lblVersion.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblVersion.BackColor = System.Drawing.Color.Transparent
        Me.lblVersion.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblVersion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblVersion.Location = New System.Drawing.Point(67, 46)
        Me.lblVersion.Name = "lblVersion"
        Me.lblVersion.Size = New System.Drawing.Size(232, 27)
        Me.lblVersion.TabIndex = 45
        Me.lblVersion.tbAdriftWhenHover = False
        Me.lblVersion.tbAutoEllipsis = False
        Me.lblVersion.tbAutoSize = False
        Me.lblVersion.tbHideImage = False
        Me.lblVersion.tbIconImage = Nothing
        Me.lblVersion.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblVersion.tbIconPlaceText = 5
        Me.lblVersion.tbShadow = False
        Me.lblVersion.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblVersion.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblVersion.tbShowScrolling = False
        Me.lblVersion.Text = "同步助手 v1.0.0 beta"
        '
        'lblHomeValue
        '
        Me.lblHomeValue.BackColor = System.Drawing.Color.Transparent
        Me.lblHomeValue.LinkColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(121, Byte), Integer), CType(CType(174, Byte), Integer))
        Me.lblHomeValue.Location = New System.Drawing.Point(67, 227)
        Me.lblHomeValue.Name = "lblHomeValue"
        Me.lblHomeValue.Size = New System.Drawing.Size(295, 15)
        Me.lblHomeValue.TabIndex = 46
        Me.lblHomeValue.TabStop = True
        Me.lblHomeValue.Text = "http://www.tongbu.com"
        '
        'lblSescription
        '
        Me.lblSescription.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblSescription.BackColor = System.Drawing.Color.Transparent
        Me.lblSescription.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblSescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblSescription.Location = New System.Drawing.Point(67, 79)
        Me.lblSescription.Name = "lblSescription"
        Me.lblSescription.Size = New System.Drawing.Size(494, 110)
        Me.lblSescription.TabIndex = 49
        Me.lblSescription.tbAdriftWhenHover = False
        Me.lblSescription.tbAutoEllipsis = False
        Me.lblSescription.tbAutoSize = False
        Me.lblSescription.tbHideImage = False
        Me.lblSescription.tbIconImage = Nothing
        Me.lblSescription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSescription.tbIconPlaceText = 5
        Me.lblSescription.tbShadow = False
        Me.lblSescription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSescription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSescription.tbShowScrolling = False
        Me.lblSescription.Text = "同步助手是一款在电脑上使用的免费iOS设备管理软件，支持iPhone/iPad/iTouch，让管理" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "苹果设备变得更加简单。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "它不但是你的应用商店" & _
            "－－万千应用任你下载，还是您的苹果设备的最佳伴侣。"
        '
        'pnlBottom
        '
        Me.pnlBottom.Controls.Add(Me.lblTongbu)
        Me.pnlBottom.Controls.Add(Me.lblCopyright)
        Me.pnlBottom.Location = New System.Drawing.Point(0, 268)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(612, 82)
        Me.pnlBottom.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlBottom.TabIndex = 50
        Me.pnlBottom.tbBackgroundImage = My.Resources.pnl_bg_about_bottom
        Me.pnlBottom.tbShowWatermark = False
        Me.pnlBottom.tbSplit = "0,0,0,0"
        Me.pnlBottom.tbWatermark = Nothing
        Me.pnlBottom.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBottom.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblTongbu
        '
        Me.lblTongbu.AutoSize = True
        Me.lblTongbu.BackColor = System.Drawing.Color.Transparent
        Me.lblTongbu.ForeColor = System.Drawing.Color.White
        Me.lblTongbu.Location = New System.Drawing.Point(259, 22)
        Me.lblTongbu.Name = "lblTongbu"
        Me.lblTongbu.Size = New System.Drawing.Size(94, 13)
        Me.lblTongbu.TabIndex = 51
        Me.lblTongbu.tbAdriftWhenHover = False
        Me.lblTongbu.tbAutoEllipsis = False
        Me.lblTongbu.tbAutoSize = True
        Me.lblTongbu.tbHideImage = False
        Me.lblTongbu.tbIconImage = My.Resources.logo_13
        Me.lblTongbu.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTongbu.tbIconPlaceText = 5
        Me.lblTongbu.tbShadow = False
        Me.lblTongbu.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTongbu.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTongbu.tbShowScrolling = False
        Me.lblTongbu.Text = "同步网络平台"
        '
        'lblAttention
        '
        Me.lblAttention.AutoSize = True
        Me.lblAttention.BackColor = System.Drawing.Color.Transparent
        Me.lblAttention.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblAttention.Location = New System.Drawing.Point(67, 200)
        Me.lblAttention.Name = "lblAttention"
        Me.lblAttention.Size = New System.Drawing.Size(53, 12)
        Me.lblAttention.TabIndex = 51
        Me.lblAttention.tbAdriftWhenHover = False
        Me.lblAttention.tbAutoEllipsis = False
        Me.lblAttention.tbAutoSize = True
        Me.lblAttention.tbHideImage = False
        Me.lblAttention.tbIconImage = Nothing
        Me.lblAttention.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblAttention.tbIconPlaceText = 5
        Me.lblAttention.tbShadow = False
        Me.lblAttention.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblAttention.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblAttention.tbShowScrolling = False
        Me.lblAttention.Text = "关注我们"
        '
        'pciQQweibo
        '
        Me.pciQQweibo.BackColor = System.Drawing.Color.Transparent
        Me.pciQQweibo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.pciQQweibo.Image = My.Resources.icon_qqweibo
        Me.pciQQweibo.Location = New System.Drawing.Point(152, 199)
        Me.pciQQweibo.Name = "pciQQweibo"
        Me.pciQQweibo.Size = New System.Drawing.Size(15, 15)
        Me.pciQQweibo.TabIndex = 52
        Me.pciQQweibo.TabStop = False
        '
        'pciSinaweibo
        '
        Me.pciSinaweibo.BackColor = System.Drawing.Color.Transparent
        Me.pciSinaweibo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.pciSinaweibo.Image = My.Resources.icon_sinaweibo
        Me.pciSinaweibo.Location = New System.Drawing.Point(126, 199)
        Me.pciSinaweibo.Name = "pciSinaweibo"
        Me.pciSinaweibo.Size = New System.Drawing.Size(15, 15)
        Me.pciSinaweibo.TabIndex = 53
        Me.pciSinaweibo.TabStop = False
        '
        'frmAbout
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(612, 350)
        Me.Controls.Add(Me.pciSinaweibo)
        Me.Controls.Add(Me.pciQQweibo)
        Me.Controls.Add(Me.lblAttention)
        Me.Controls.Add(Me.pnlBottom)
        Me.Controls.Add(Me.lblSescription)
        Me.Controls.Add(Me.lblVersion)
        Me.Controls.Add(Me.lblHomeValue)
        Me.Controls.Add(Me.btn_close)
        Me.MinimumSize = New System.Drawing.Size(11, 40)
        Me.Name = "frmAbout"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbAutoSetFormSize = True
        Me.tbGuiBackground = My.Resources.pnl_bg_info
        Me.tbSplit = "5,32,6,8"
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "Settings"
        Me.pnlBottom.ResumeLayout(False)
        CType(Me.pciQQweibo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pciSinaweibo, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As tbButton
    Friend WithEvents lblCopyright As tbLabel
    Friend WithEvents lblVersion As tbLabel
    Friend WithEvents lblHomeValue As System.Windows.Forms.LinkLabel
    Friend WithEvents lblSescription As tbLabel
    Friend WithEvents pnlBottom As tbPanel
    Friend WithEvents lblTongbu As tbLabel
    Friend WithEvents lblAttention As tbLabel
    Friend WithEvents pciQQweibo As System.Windows.Forms.PictureBox
    Friend WithEvents pciSinaweibo As System.Windows.Forms.PictureBox
End Class
