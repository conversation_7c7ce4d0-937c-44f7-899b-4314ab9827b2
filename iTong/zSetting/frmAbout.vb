﻿Public Class frmAbout

    Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.FilletRadius = 5
        Me.tbGuiBackground = My.Resources.pnl_bg_info
        Me.tbSplit = "10,33,10,33"
        'Me.Icon = My.Resources.iTong
        'Me.tbShowTitleOnForm = True
        'Me.ShowInTaskbar = True
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Dim strVer As String = String.Empty
        If My.Application.Info.Version.MinorRevision = 0 Then
            strVer = My.Application.Info.Version.ToString(3)
        Else
            strVer = My.Application.Info.Version.ToString(4)
        End If
        Me.Text = Me.Language.GetString("Main.Menu.About")
        Me.lblSescription.Text = Me.Language.GetString("Main.Message.AboutDescription")
        Me.lblAttention.Text = Me.Language.GetString("Media.Label.AttentionMe")         '"关注我们"

        Me.pciSinaweibo.Location = New Point(Me.lblAttention.Right + 8, Me.pciSinaweibo.Top)
        Me.pciQQweibo.Location = New Point(Me.pciSinaweibo.Right + 10, Me.pciQQweibo.Top)

        Me.lblHomeValue.Text = WebUrl.PageAssitantHome
        Me.lblVersion.AutoSize = True
        If Folder.LangType = LanguageType.zh_CN Then
            Me.lblVersion.Text = String.Format("同步助手 v{0} beta", strVer)
            'Me.lblHomeValue.Text = "http://www.tongbu.com/zhushou"

            Me.lblTongbu.Text = "同步网络平台"
            Me.lblCopyright.Text = "Copyright © 2015 TongBu.com, All rights reserved." & vbCrLf & _
                                       "同步网络平台 版权所有"

            Me.pciSinaweibo.Image = My.Resources.icon_sinaweibo
            Me.pciQQweibo.Image = My.Resources.icon_qqweibo

#If IS_ITONG Then
        ElseIf Folder.LangType = LanguageType.vi_VN Then
            Me.lblVersion.Text = String.Format("Trợ thủ Đồng BỘ v{0} beta", strVer)
            'Me.lblHomeValue.Text = "http://www.dongbo.vn"

            Me.lblTongbu.Text = "Mạng Đồng BỘ"
            Me.lblCopyright.Text = "Copyright © 2015 dongbo.vn, All rights reserved."

            Me.pciSinaweibo.Image = My.Resources.icon_facebook
            Me.pciQQweibo.Image = My.Resources.icon_twitter

        ElseIf Folder.LangType = LanguageType.th_TH Then
            Me.lblVersion.Text = String.Format("AA assistant v{0} beta", strVer)
            'Me.lblHomeValue.Text = "http://www.appleAA.com/"

            Me.lblTongbu.Text = "appleAA Networks Ltd."
            Me.lblCopyright.Text = "Copyright © 2015 appleAA.com, All rights reserved."

            Me.pciSinaweibo.Image = My.Resources.icon_facebook
            Me.pciQQweibo.Image = My.Resources.icon_twitter
            Me.pciQQweibo.Visible = False

        Else
            Me.lblVersion.Text = String.Format("同步助手 v{0} beta", strVer)
            'Me.lblHomeValue.Text = "http://www.tongbu.com/zhushou"

            Me.lblTongbu.Text = "Xiamen Tongbu Networks Ltd."
            Me.lblCopyright.Text = "Copyright © 2015 iClover.com, All rights reserved."

            Me.pciSinaweibo.Image = My.Resources.icon_facebook
            Me.pciQQweibo.Image = My.Resources.icon_twitter
#End If

        End If

        lblTongbu.Location = New Point((Me.Width - lblTongbu.Width) \ 2, lblTongbu.Top)
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        'If Common.RunInChina Then
        '    Me.picLogo.Image = My.Resources.logo_cn
        'End If

        If Folder.LangType = LanguageType.vi_VN Then
            Me.pciQQweibo.Visible = False
        End If

    End Sub

    Private Sub lblLink_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblHomeValue.LinkClicked
        'Common.OpenExplorer("http://jp.tongbu.com")
        '"http://redirect.tongbu.com/?lan={0}"
        'Dim url As String = String.Format(WebUrl.HomePageUrl, Me.Language.CurrentLanguage.LangName, IIf(Common.RunInChina, "1", "2"))
        'If Common.RunInChina Then
        '    url = "http://www.tongbu.com/zhushou/"
        'End If
        Common.OpenExplorer(WebUrl.PageHome)
    End Sub

    Private Sub pciSinaweibo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pciSinaweibo.Click
        'Dim strUrl As String = WebUrl.PageAssistantWeibo

        'If Folder.LangType = LanguageType.zh_CN Then
        '    strUrl = "http://weibo.com/tongbuassistant"
        'ElseIf Folder.LangType = LanguageType.vi_VN Then
        '    strUrl = "https://www.facebook.com/pages/%C4%90%E1%BB%93ng-B%E1%BB%99/625446587487119"
        'Else
        '    strUrl = "https://www.facebook.com/pages/IClover/116533728554734"
        'End If

        Common.OpenExplorer(WebUrl.PageAssistantWeibo)
    End Sub

    Private Sub pciQQweibo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pciQQweibo.Click
        Dim strUrl As String = String.Empty
        If Folder.LangType = LanguageType.zh_CN Then
            strUrl = "http://t.qq.com/tongbuassistant"
        Else
            strUrl = "https://twitter.com/iCloverSupport"
        End If

        Common.OpenExplorer(strUrl)
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.tbGuiBackground = My.Resources.pnl_bg_info
        Me.pnlBottom.tbBackgroundImage = My.Resources.pnl_bg_about_bottom
        Me.btn_close.tbBackgroundImage = My.Resources.btn_close1

    End Sub
End Class