﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSettingSkin
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSettingSkin))
        Me.btn_close = New iTong.Components.tbButton()
        Me.btnClose = New iTong.Components.tbButton()
        Me.flpContainer = New iTong.tbFlowLayoutPanelEx()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(520, 1)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 9
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btnClose
        '
        Me.btnClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClose.BackColor = System.Drawing.Color.Transparent
        Me.btnClose.BindingForm = Nothing
        Me.btnClose.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClose.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnClose.Location = New System.Drawing.Point(436, 310)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnClose.Selectable = True
        Me.btnClose.Size = New System.Drawing.Size(84, 29)
        Me.btnClose.TabIndex = 41
        Me.btnClose.tbAdriftIconWhenHover = False
        Me.btnClose.tbAutoSize = False
        Me.btnClose.tbAutoSizeEx = False
        Me.btnClose.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnClose.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnClose.tbBadgeNumber = 0
        Me.btnClose.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClose.tbEndEllipsis = False
        Me.btnClose.tbIconHoldPlace = True
        Me.btnClose.tbIconImage = Nothing
        Me.btnClose.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClose.tbIconMore = False
        Me.btnClose.tbIconMouseDown = Nothing
        Me.btnClose.tbIconMouseHover = Nothing
        Me.btnClose.tbIconMouseLeave = Nothing
        Me.btnClose.tbIconPlaceText = 2
        Me.btnClose.tbIconReadOnly = Nothing
        Me.btnClose.tbImageMouseDown = Nothing
        Me.btnClose.tbImageMouseHover = Nothing
        Me.btnClose.tbImageMouseLeave = Nothing
        Me.btnClose.tbProgressValue = 50
        Me.btnClose.tbReadOnly = False
        Me.btnClose.tbReadOnlyText = False
        Me.btnClose.tbShadow = False
        Me.btnClose.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnClose.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnClose.tbShowDot = False
        Me.btnClose.tbShowMoreIconImg = CType(resources.GetObject("btnClose.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnClose.tbShowNew = False
        Me.btnClose.tbShowProgress = False
        Me.btnClose.tbShowTip = True
        Me.btnClose.tbShowToolTipOnButton = False
        Me.btnClose.tbSplit = "13,11,13,11"
        Me.btnClose.tbText = "关闭"
        Me.btnClose.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbTextColor = System.Drawing.Color.White
        Me.btnClose.tbTextColorDisable = System.Drawing.Color.White
        Me.btnClose.tbTextColorDown = System.Drawing.Color.White
        Me.btnClose.tbTextColorHover = System.Drawing.Color.White
        Me.btnClose.tbTextMouseDownPlace = 0
        Me.btnClose.tbToolTip = ""
        Me.btnClose.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClose.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClose.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.VisibleEx = True
        '
        'flpContainer
        '
        Me.flpContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.flpContainer.AutoScroll = True
        Me.flpContainer.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink
        Me.flpContainer.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.flpContainer.Location = New System.Drawing.Point(1, 31)
        Me.flpContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.flpContainer.Name = "flpContainer"
        Me.flpContainer.Size = New System.Drawing.Size(543, 266)
        Me.flpContainer.TabIndex = 42
        Me.flpContainer.tbGroupPadding = 0
        '
        'frmSettingSkin
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanResize = True
        Me.ClientSize = New System.Drawing.Size(545, 350)
        Me.Controls.Add(Me.flpContainer)
        Me.Controls.Add(Me.btnClose)
        Me.Controls.Add(Me.btn_close)
        Me.Name = "frmSettingSkin"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,70"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "皮肤"
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents btnClose As iTong.Components.tbButton
    Friend WithEvents flpContainer As iTong.tbFlowLayoutPanelEx
End Class
