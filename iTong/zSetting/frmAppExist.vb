﻿Public Class frmAppExist

    Private mApplication As IApplication = Nothing

    Public Sub New(ByVal application As IApplication)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.Icon = My.Resources.iTong

        Me.chkRemember.Checked = IniSetting.GetRememberOnClose()

        If IniSetting.GetMinizedOnClose() Then
            Me.rdbMini.Checked = True
        Else
            Me.rdbExist.Checked = True
        End If

        Me.FilletRadius = 5
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.gpbExist.Text = Me.Language.GetString("Setting.Group.CloseOrMiniForm")               '"您点击了关闭窗口，您想"
        Me.rdbExist.Text = Me.Language.GetString("Setting.Button.ExitApp")                      '"退出程序"
        Me.rdbMini.Text = Me.Language.GetString("Setting.Button.Mini")                          '"最小化到托盘"
        Me.btnOK.tbText = Me.Language.GetString("Common.OK")                                    '"确定"
        Me.btnCancel.tbText = Me.Language.GetString("Common.Cancel")                            '"取消"
        Me.chkRemember.Text = Me.Language.GetString("Setting.Button.RememberMyChoice")          '"记住我的选择"
        Me.Text = Me.Language.GetString("Common.Info")                                          '"提示"
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Me.DialogResult = Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub frmAppExist_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing
        If Me.DialogResult = Windows.Forms.DialogResult.OK Then
            IniSetting.SetRememberOnClose(Me.chkRemember.Checked)
        End If
    End Sub

    Public Shared Function CheckClosingFromSetting(ByVal app As IApplication) As CloseAction
        Dim actionResult As CloseAction = CloseAction.Cancel

        If IniSetting.GetRememberOnClose() Then
            actionResult = IIf(IniSetting.GetMinizedOnClose(), CloseAction.Minize, CloseAction.Close)
        Else
            Dim frmExist As New frmAppExist(app)
            If frmExist.ShowDialog() = Windows.Forms.DialogResult.OK Then
                actionResult = IIf(frmExist.rdbMini.Checked, CloseAction.Minize, CloseAction.Close)
                IniSetting.SetMinizedOnClose(frmExist.rdbMini.Checked)
            End If
        End If

        Return actionResult
    End Function

End Class

Public Enum CloseAction
    Close = 0
    Minize = 1
    Cancel = 2
End Enum
