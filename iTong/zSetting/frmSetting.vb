﻿Public Class frmSetting

    Private Enum FolderType
        iTunes
        Other
        App
        Music
        Ringtone
        Wallpaper
    End Enum

    Private mFolderiTunes As String = ""
    Private mFolderOther As String = ""
    Private mFolderDownload As String = ""
    Private mFolderMusic As String = ""
    Private mFolderRingtone As String = ""
    Private mFolderWallpaper As String = ""
    Private mIsMini As Boolean = True
    Private mLstNewFolders As New List(Of String) '新增加的软件目录

#Region "--- 初始化 ---"

    Public Sub New(ByVal FunctionMappingKey As FunctionKey)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FilletRadius = 5
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong
        'Me.ShowInTaskbar = True
        'Me.mApplication = app
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitLang()

        Me.mFolderiTunes = IniSetting.GetiTunesFolder
        Me.txtiTunesFolder.Text = Me.mFolderiTunes

        Me.mFolderOther = IniSetting.GetOtherFolder
        Me.txtOtherFolder.Text = Me.mFolderOther

        Me.mFolderDownload = IniSetting.GetDownloadSoftFolder
        Me.txtDownloadFolder.Text = Me.mFolderDownload

        Me.mFolderMusic = IniSetting.GetDownloadMusicFolder
        Me.txtMusicFolder.Text = Me.mFolderMusic

        Me.mFolderRingtone = IniSetting.GetDownloadRingtoneFolder
        Me.txtRingtoneFolder.Text = Me.mFolderRingtone

        Me.mFolderWallpaper = IniSetting.GetDownloadWallpaperFolder
        Me.txtWallpaperFolder.Text = Me.mFolderWallpaper

        Me.chkRemember.Checked = IniSetting.GetRememberOnClose()

        Me.mIsMini = IniSetting.GetMinizedOnClose
        If Me.mIsMini Then
            Me.rdbMini.Checked = True
        Else
            Me.rdbExist.Checked = True
        End If

        '从注册表中取得 ipa 文件是不是有关联
        'If Not IniSetting.GetOpenPkgInstaller() Then
        '    Me.chkLinkIPAPackage.Checked = False
        'Else
        '    Me.chkLinkIPAPackage.Checked = True
        'End If

        Me.chkLinkIPAPackage.Checked = ProcFormHelper.Instance().CheckPkgInstallerRegisterExist()
        Me.chkAutoShowMsgAfterDownload.Checked = IniSetting.GetAutoShowMsgAfterDownload
        Me.chkAutoInstallAfterDownload.Checked = IniSetting.GetAutoInstallAfterDownload

        If Folder.LangType = LanguageType.en_US Then
            Me.lblMusicFolder.Visible = False
            Me.txtMusicFolder.Visible = False
            Me.btnMusicFolder.Visible = False
            Me.lblRingtoneFolder.Visible = False
            Me.txtRingtoneFolder.Visible = False
            Me.btnRingtoneFolder.Visible = False
            Me.lblWallpaperFolder.Visible = False
            Me.txtWallpaperFolder.Visible = False
            Me.btnWallpaperFolder.Visible = False
            Me.Size = New Size(527, 475)
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lbliTunesFolder.Text = Me.Language.GetString("Setting.Label.iTunesFolder")           '"iTunes Folder"
        'Me.lbliTunesInfo.Text = Me.Language.GetString("Setting.Label.iTunesFolderDescription") '"Apps in this folder will be automatically updated into your Library."
        Me.lblDownloadFolder.Text = Me.Language.GetString("Setting.Label.DownloadFolders")      '"下载目录" 
        Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")                              '"Cancel"
        Me.btnOK.Text = Me.Language.GetString("Common.Button.Save")                             '"Save"
        Me.btniTunesFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")     '"Change"
        Me.btnDownloadFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")   '"Change"
        'Me.lblClose.Text = Me.Language.GetString("Setting.Label.CloseType")                    '"Click close button to"
        Me.rdbMini.Text = Me.Language.GetString("Setting.RadioButton.MiniToTray")               '"Minimize to tray"
        Me.rdbExist.Text = Me.Language.GetString("Setting.RadioButton.ExitForm")                '"Exit iTong"
        Me.Text = Me.Language.GetString("Common.Button.Setting")
        Me.chkLinkIPAPackage.Text = Me.Language.GetString("Setting.Label.LinkIPAPackage")       '"关联 IPA，可以直接双击 IPA 文件来安装程序"
        Me.chkRemember.Text = Me.Language.GetString("Setting.Label.NoPopExit")                  '"退出时不再提示"
        Me.lblApp.Text = Me.Language.GetString("App.TreeNode.DeviceApps")                       '"应用"
        Me.lblMusicFolder.Text = Me.Language.GetString("Main.Button.Music")                     '"音乐"
        Me.lblRingtoneFolder.Text = Me.Language.GetString("Media.Type.Ringtone")                '"铃声"
        Me.lblWallpaperFolder.Text = Me.Language.GetString("Photo.Label.WallPaper")             '"壁纸"
        Me.btnMusicFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")      '"Change"
        Me.btnRingtoneFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")   '"Change"
        Me.btnWallpaperFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")  '"Change"

        Me.lblOtherFolder.Text = Me.Language.GetString("Setting.Label.OtherFolder")             '"附加的其他软件（ipa）的目录"
        Me.btnOtherFolder.Text = Me.Language.GetString("Components.Login.Label.ChangePic")      '"Change"
        Me.lblCommon.Text = Me.Language.GetString("Setting.Label.Normal")                       '"常用"
        Me.lblFolder.Text = Me.Language.GetString("Setting.Label.Folder")                       '"目录"
        Me.chkAutoShowMsgAfterDownload.Text = Me.Language.GetString("Setting.Label.AutoShowMsgAfterDownload") '"应用下载完成时弹窗提示"
        Me.chkAutoInstallAfterDownload.Text = Me.Language.GetString("Setting.Label.AutoInstallAfterDownload")   '"下载应用后立即安装"
        Me.lblLanguage.Text = Me.Language.GetString("Setting.Label.Language")                   '"界面语言"

        Me.SetFolderWidth()
    End Sub

    Private Function GetFolderPointX() As Integer
        Dim intReturn As Integer = 0
        intReturn = Me.lblApp.Right
        If intReturn < Me.lblMusicFolder.Right Then
            intReturn = Me.lblMusicFolder.Right
        End If

        If intReturn < Me.lblRingtoneFolder.Right Then
            intReturn = Me.lblRingtoneFolder.Right
        End If

        If intReturn < Me.lblWallpaperFolder.Right Then
            intReturn = Me.lblWallpaperFolder.Right
        End If
        Return intReturn + 10
    End Function

    Private Sub SetFolderWidth()
        Me.txtDownloadFolder.Location = New Point(Me.GetFolderPointX(), Me.txtDownloadFolder.Top)
        Me.txtMusicFolder.Location = New Point(Me.GetFolderPointX(), Me.txtMusicFolder.Top)
        Me.txtRingtoneFolder.Location = New Point(Me.GetFolderPointX(), Me.txtRingtoneFolder.Top)
        Me.txtWallpaperFolder.Location = New Point(Me.GetFolderPointX(), Me.txtWallpaperFolder.Top)

        Dim width As Integer = Me.txtOtherFolder.Right - Me.txtDownloadFolder.Left
        Me.txtDownloadFolder.Width = width
        Me.txtMusicFolder.Width = width
        Me.txtRingtoneFolder.Width = width
        Me.txtWallpaperFolder.Width = width
    End Sub

    '加载语言包信息
    Private Sub InitLang()
        Try
            Dim strCurrentLanguage As String = IniSetting.GetLanguage()
            If String.IsNullOrEmpty(strCurrentLanguage) Then
                strCurrentLanguage = Me.Language.CurrentLanguage.LangName
                '第一次打开的时候如果Setting.ini里面没有语言信息就写到文件里面去。
                IniSetting.SetLanguage(strCurrentLanguage)
            End If

            Me.cbxLanguage.Items.Clear()
            For Each info As LangInfo In Me.Language.Languages
                Me.cbxLanguage.Items.Add(info.LangDisplayName)
                If strCurrentLanguage = info.LangName Then
                    Me.cbxLanguage.Text = info.LangDisplayName
                End If
            Next

        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

    '保存语言名的信息
    Private Sub SaveLang()
        Try
            '读出当前语言种类和用户修改后语言种类
            Dim strCurrentLanguage As String = IniSetting.GetLanguage
            Dim strUserUpdateLanguage As String = ""
            For Each info As LangInfo In Me.Language.Languages
                If info.LangDisplayName = Me.cbxLanguage.Text Then
                    strUserUpdateLanguage = info.LangName
                    Exit For
                End If
            Next
            '如果用户有修改语言信息就提示
            If strCurrentLanguage <> strUserUpdateLanguage Then
                IniSetting.SetLanguage(strUserUpdateLanguage)
                '"设置语言生效需要重启程序，是否立即执行重启？"
                '"提示"
                Dim result As DialogResult = tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.RestarForSetLang"), _
                                                             Me.Language.GetString("Common.Info"), _
                                                             MessageBoxButtons.YesNo, _
                                                             MessageBoxIcon.Question, _
                                                             MessageBoxDefaultButton.Button2)
                If result = Windows.Forms.DialogResult.Yes Then
                    Me.Hide()
                    Me.Close()
                    ProcForm.Instance.ExitApp()

                    '重启助手
                    HelperExeManager.RestartApp()
                End If
            End If
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try

    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click

        '设置退出时不再提示
        IniSetting.SetRememberOnClose(Me.chkRemember.Checked)
        If Me.rdbMini.Checked <> Me.mIsMini Then
            IniSetting.SetMinizedOnClose(Me.rdbMini.Checked)
        End If

        IniSetting.SetAutoShowMsgAfterDownload(Me.chkAutoShowMsgAfterDownload.Checked)
        IniSetting.SetAutoInstallAfterDownload(Me.chkAutoInstallAfterDownload.Checked)

        '用户可以在参数设置里面设置是不是要关联 ipa 文件
        Dim strPkgInstallerFilePath As String = Folder.AppFolder & "PkgInstaller.exe"
        If ProcFormHelper.Instance().CheckPkgInstallerRegisterExist() <> Me.chkLinkIPAPackage.Checked AndAlso _
           File.Exists(strPkgInstallerFilePath) Then

            Try
                If Me.chkLinkIPAPackage.Checked = True Then
                    '设置关联
                    Process.Start(strPkgInstallerFilePath, "/r")
                Else
                    '取消关联
                    Process.Start(strPkgInstallerFilePath, "/u")
                End If

            Catch ex As Exception
            End Try
        End If
        IniSetting.SetOpenPkgInstaller(Me.chkLinkIPAPackage.Checked)

        '设置语言包
        Me.SaveLang()

        '设置目录
        Me.mLstNewFolders.Clear()
        Me.SaveFolder(Me.mFolderiTunes, Me.txtiTunesFolder.Text, FolderType.iTunes)
        Me.SaveFolder(Me.mFolderOther, Me.txtOtherFolder.Text, FolderType.Other)
        Me.SaveFolder(Me.mFolderDownload, Me.txtDownloadFolder.Text, FolderType.App)
        Me.SaveFolder(Me.mFolderMusic, Me.txtMusicFolder.Text, FolderType.Music)
        Me.SaveFolder(Me.mFolderRingtone, Me.txtRingtoneFolder.Text, FolderType.Ringtone)
        Me.SaveFolder(Me.mFolderWallpaper, Me.txtWallpaperFolder.Text, FolderType.Wallpaper)

        '软件目录（3个）。如果目录有更换，需要自动加载目录下的软件。
        Me.LoadAppFromNewFolders(Me.mLstNewFolders)

        Me.BringToFront()
        Me.Close()
    End Sub

    Private Sub btniTunesFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btniTunesFolder.Click
        Dim strFolder As String = Me.txtiTunesFolder.Text.Trim
        Me.txtiTunesFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnDownloadFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDownloadFolder.Click
        Dim strFolder As String = Me.txtDownloadFolder.Text.Trim
        Me.txtDownloadFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnMusicFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnMusicFolder.Click
        Dim strFolder As String = Me.txtMusicFolder.Text.Trim
        Me.txtMusicFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnRingtoneFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRingtoneFolder.Click
        Dim strFolder As String = Me.txtRingtoneFolder.Text.Trim
        Me.txtRingtoneFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnWallpaperFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnWallpaperFolder.Click
        Dim strFolder As String = Me.txtWallpaperFolder.Text.Trim
        Me.txtWallpaperFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Private Sub btnOtherFolder_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOtherFolder.Click
        Dim strFolder As String = Me.txtOtherFolder.Text.Trim
        Me.txtOtherFolder.Text = Me.SelectFolder(strFolder)
    End Sub

    Protected Overrides Sub OnClosed(ByVal e As System.EventArgs)
        MyBase.OnClosed(e)
        If Me.Owner IsNot Nothing AndAlso TypeOf Me.Owner Is MainForm Then
            Me.Owner.BringToFront()
        End If
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function SelectFolder(ByVal strFolder As String) As String
        Dim folder As String = strFolder
        Dim dialog As FolderBrowserDialog = New FolderBrowserDialog()

        If Directory.Exists(strFolder) Then
            dialog.SelectedPath = strFolder
        End If

        If dialog.ShowDialog() = Windows.Forms.DialogResult.OK Then
            folder = dialog.SelectedPath
        End If

        Return folder
    End Function

    Private Sub GetFolderMusic(ByVal strMusicFolder As String)
        If Directory.Exists(strMusicFolder) Then
            Dim manager As MusicManage = MusicManage.Instance()      '用来添加音乐下载目录到监控目录中。
            Dim fInfo As FolderInfo = manager.MusicDB.Get_MonitoredFolder(strMusicFolder)

            If fInfo Is Nothing Then
                fInfo = FolderInfo.CreateOtherFolderInfo(strMusicFolder)
            End If
            fInfo.MonitorState = MonitorState.Monitor_True

            Dim lstFile As New List(Of String)
            Try
                For Each file As String In Directory.GetFiles(strMusicFolder.TrimEnd("\") & "\", "*.mp3", SearchOption.AllDirectories)
                    lstFile.Add(file)
                    Application.DoEvents()
                Next
            Catch ex As Exception

            End Try

            fInfo.SongList.AddRange(lstFile)
            manager.AddMusicFolder(fInfo)
        End If

    End Sub

    Private Function SaveFolder(ByVal strOldPath As String, ByVal strNewPath As String, ByVal type As FolderType) As String
        Dim strReturn As String = strNewPath.Trim().TrimEnd("\")
        strOldPath = strOldPath.Trim().TrimEnd("\")

        '"附加的其他软件目录"可以为空。除此之外的目录不能为空。
        If strReturn.Length > 0 OrElse _
           (type = FolderType.Other AndAlso String.Compare(strOldPath, strReturn, True) <> 0) Then

            If (strReturn.Length > 0 AndAlso String.Compare(strOldPath, strReturn, True) <> 0) OrElse _
                (type = FolderType.Other AndAlso String.Compare(strOldPath, strReturn, True) <> 0) Then

                Folder.CheckFolder(strReturn)

                Select Case type
                    Case FolderType.iTunes
                        IniSetting.SetiTunesFolder(strReturn)
                        Me.AddNewAppFolder(strReturn)

                    Case FolderType.Other
                        IniSetting.SetOtherFolder(strReturn)
                        Me.AddNewAppFolder(strReturn)

                    Case FolderType.App
                        IniSetting.SetDownloadSoftFolder(strReturn)
                        Me.AddNewAppFolder(strReturn)

                    Case FolderType.Music
                        IniSetting.SetDownloadMusicFolder(strReturn)
                        Me.GetFolderMusic(strReturn)

                    Case FolderType.Ringtone
                        IniSetting.SetDownloadRingtoneFolder(strReturn)
                        frmRingtoneLocal.FileWatcher.Path = strReturn

                    Case FolderType.Wallpaper
                        IniSetting.SetDownloadWallpaperFolder(strReturn)
                        frmWallpaperLocal.FileWatcher.Path = strReturn

                End Select

                'Folder.CheckFolder(strReturn)
            End If
        End If
        Return strReturn
    End Function

    Private Sub AddNewAppFolder(ByVal strNewFolder As String)
        If strNewFolder.Length = 0 Then
            Return
        End If

        If String.Compare(strNewFolder, Me.mFolderiTunes, True) <> 0 AndAlso _
           String.Compare(strNewFolder, Me.mFolderOther, True) <> 0 AndAlso _
           String.Compare(strNewFolder, Me.mFolderDownload, True) <> 0 Then

            Me.mLstNewFolders.Add(strNewFolder)
        End If
    End Sub

    Private Sub LoadAppFromNewFolders(ByVal lstNewFolders As List(Of String))
        If lstNewFolders Is Nothing OrElse lstNewFolders.Count = 0 Then
            Return
        End If

        '添加新软件
        Dim appList As New List(Of String)
        For Each folder As String In lstNewFolders
            Try
                '有些目录没有权限读取，暂时Try Catch处理
                If System.IO.Directory.Exists(folder) Then
                    appList.AddRange(My.Computer.FileSystem.GetFiles(folder, FileIO.SearchOption.SearchAllSubDirectories, "*.ipa", "*.pxl"))
                End If
            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try
        Next

        ApplePackageReader.Instance().AddPackage(appList)
    End Sub

#End Region

End Class