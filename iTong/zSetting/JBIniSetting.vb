﻿Imports System.Threading

Public Class JBIniSetting

    Private Shared mConfigPath As String = ""
    Private Shared mThreadServer As Thread

#Region "---  初始化 ---"

    Shared Sub New()
        mConfigPath = Path.Combine(Folder.DocumentFolder, "JailbreakInfo.ini")

        Try
            '如果安装目录下有这个文件就copy到目标目录下.
            Dim strPathOld As String = Path.Combine(Folder.AppFolder, "JailbreakInfo.ini")
            If File.Exists(strPathOld) Then
                If File.Exists(mConfigPath) Then
                    File.Delete(mConfigPath)
                End If
                System.IO.File.Move(strPathOld, mConfigPath)
            End If
        Catch ex As Exception
        End Try

        mThreadServer = New Thread(AddressOf DownloadConfigThread)

        With mThreadServer
            .IsBackground = True
            .Start()
        End With
    End Sub

    Public Shared Sub DownloadConfig()
        '执行代码移到new中
        Dim dt As Date = Now
        While mThreadServer IsNot Nothing AndAlso mThreadServer.ThreadState <> ThreadState.Stopped AndAlso Now.Subtract(dt).TotalSeconds < 10
            Utility.WaitSeconds(0.05)
        End While
    End Sub

    Private Shared Sub DownloadConfigThread()

        Try
            '纪龙龙
            '需要替换url
            '需要修改底层，允许定义下载路径
            Dim strPath As String = mConfigPath & ".tmp"
            If Common.DownloadImage("http://t.tongbu.com/tbzs20/JailbreakInfo.aspx", 20000, strPath) Then
                Dim strContent As String = String.Empty
                Using sReader As New StreamReader(strPath, Utility.CheckFileEncoding(strPath))
                    strContent = sReader.ReadToEnd()
                End Using

                If Not String.IsNullOrEmpty(strContent) Then
                    Using sWriter As New StreamWriter(strPath, False, Encoding.ASCII)
                        sWriter.Write(strContent)
                    End Using
                End If

                If File.Exists(mConfigPath) Then
                    File.Delete(mConfigPath)
                End If

                File.Move(strPath, mConfigPath)
            Else
                Common.LogException("JBIniSetting 下载失败", "DownloadConfigThread")
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadConfigThread")
        End Try

    End Sub

#End Region

#Region "--- 获取值 ---"

    '获取越狱工具文件名
    Public Shared Function GetFileName(ByVal ProductVersion As String, ByVal ProductType As String) As String
        Dim strKey As String = String.Format("FileName_{0}", ProductType)
        Dim strValue As String = IniClass.GetIniSectionKey(ProductVersion, strKey, mConfigPath)

        If String.IsNullOrEmpty(strValue) Then
            strValue = IniClass.GetIniSectionKey(ProductVersion, "FileName", mConfigPath)
        End If

        Return strValue
    End Function

    '获取越狱工具主程序名称
    Public Shared Function GetExeFileName(ByVal ProductVersion As String, ByVal ProductType As String) As String
        Dim strKey As String = String.Format("ExeFileName_{0}", ProductType)
        Dim strValue As String = IniClass.GetIniSectionKey(ProductVersion, strKey, mConfigPath)

        If String.IsNullOrEmpty(strValue) Then
            strValue = IniClass.GetIniSectionKey(ProductVersion, "ExeFileName", mConfigPath)
        End If

        Return strValue
    End Function

    '获取越狱工具下载url
    Public Shared Function GetFileUrl(ByVal ProductVersion As String, ByVal ProductType As String)
        Dim strKey As String = String.Format("FilePath_{0}", ProductType)
        Dim strValue As String = IniClass.GetIniSectionKey(ProductVersion, strKey, mConfigPath)

        If String.IsNullOrEmpty(strValue) Then
            strValue = IniClass.GetIniSectionKey(ProductVersion, "FilePath", mConfigPath)
        End If

        Return strValue
    End Function

#End Region

End Class
