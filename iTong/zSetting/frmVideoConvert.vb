﻿Imports System.Threading
Imports SharePodLib
Imports ProgressStyle = iTong.Components.ProgressStyle

Public Class frmVideoConvert
    Private mMediaMaster As MediaMaster = Nothing
    Friend WithEvents dgvTask As New tbDataGridViewEx
    Private mMusicDB As MusicDB = Nothing
    Private mthreadLoadFiles As Thread
    Private mIPod As IPod

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice)
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.tbAutoSetFormSize = True
        Me.FilletRadius = 5
        Me.Icon = My.Resources.iTong
        Me.CanResize = False

        Me.mApplication = application
        Me.Language = application.Language
        Me.mDevice = device
        Me.FunctionMappingKey = FunctionKey.VideoConvert
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.mMediaMaster = MediaMaster.Instance()
        Me.mMusicDB = MusicDB.Instance()

        RemoveHandler mMediaMaster.ConvertBegin, AddressOf ConvertBegin
        AddHandler mMediaMaster.ConvertBegin, AddressOf ConvertBegin

        RemoveHandler mMediaMaster.ConvertEnd, AddressOf ConvertEnd
        AddHandler mMediaMaster.ConvertEnd, AddressOf ConvertEnd

        RemoveHandler mMediaMaster.ConvertProgress, AddressOf ConvertProgress
        AddHandler mMediaMaster.ConvertProgress, AddressOf ConvertProgress

        If Me.mDevice Is Nothing Then
            Me.btnAddToDevice.Enabled = False
        End If

        Me.btn_close.Visible = False
        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False

        Me.tbGuiBackground = My.Resources.frm_bg_sub
        Me.tbSplit = "8,43,8,55"
        Me.InitDataGridView()
        Me.LoadExitFilesThread()
        Me.btnStart.Enabled = False
        Me.btnStop.Enabled = False
        Me.btnDelete.Enabled = False
        Me.CanResize = False

        MainForm.SetButtonStatus(Me.btnSelectVideo)
        MainForm.SetButtonStatus(Me.btnStart)
        MainForm.SetButtonStatus(Me.btnStop)
        MainForm.SetButtonStatus(Me.btnDelete)

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.btnSelectVideo.Text = Me.Language.GetString("Setting.Button.SelectVideo")        '"选择视频"
        Me.btnStart.Text = Me.Language.GetString("Download.Button.Start")                   '"开始"
        Me.btnStop.Text = Me.Language.GetString("Main.Button.Stop")                         '"停止"
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete")                          '"删除"
        Me.btnImportVideo.Text = Me.Language.GetString("Setting.Button.SelectVideo")        '"选择视频"
        Me.btnOpenLocal.Text = Me.Language.GetString("App.Button.ShowInFile")               '"Open location"
        Me.btnAddToDevice.Text = Me.Language.GetString("Common.Button.AddToDevice")         '"Add to device"
        Me.Text = Me.Language.GetString("Main.Button.VideoConvert")                         '"视频转换"
        Me.lblMessage.Text = ""
        Me.tsmiOpenExplorer.Text = Me.Language.GetString("App.Button.ShowInFile")               '"Open location"
        Me.tsmiStart.Text = Me.Language.GetString("Download.Button.Start")                   '"开始"
        Me.tsmiStop.Text = Me.Language.GetString("Main.Button.Stop")                         '"停止"
        Me.tsmiDelete.Text = Me.Language.GetString("Common.Delete")                          '"删除"
        Me.txtDescription.Text = Me.Language.GetString("Media.Message.VideoConvertDescription") '"将 avi，flv，rmvb，wav，mov，mkv，f4v 格式的视频转换为 mp4。"
    End Sub

    Private Sub InitDataGridView()
        With dgvTask
            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.Font = Common.CreateFont("Arial", 9, FontStyle.Bold)
            cellStyle.ForeColor = Color.Black

            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "ColName", Me.Language.GetString("Common.Label.Name"), _
                                                                                               200, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            colName.DefaultCellStyle = cellStyle

            '.ContextMenuStrip = Me.menuDownload
            Me.dgvTask.ContextMenuStrip = Me.menuVideoConvert

            .SuspendLayout()
            .RowTemplate.Height = 50
            .ScrollBars = System.Windows.Forms.ScrollBars.Both
            .Columns.Clear()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", _
                                                                    26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            .Columns.Add(colName)
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Duration", Me.Language.GetString("Music.Label.Duration"), _
                                                                    80, True, False, DataGridViewContentAlignment.MiddleRight))                 '"时间"

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colSize", Me.Language.GetString("Common.Columns.Size"), _
                                                                    80, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colFormat", Me.Language.GetString("Setting.Label.Format"), _
                                                                    80, True, False, DataGridViewContentAlignment.MiddleRight))                 '"格式"

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", Me.Language.GetString("Download.Label.Progress"), _
                                                                    175, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
            '"进度"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colControl", Me.Language.GetString("Download.Label.Operate"), _
                                                                    90, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))             '"操作"

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "FilePath", "", _
                                                                    100, False))                                                                '"路径"

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", _
                                                                    100.0!, True, True, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))

            Me.Controls.Add(dgvTask)
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")      '"这里没有内容"
            .tbNoDataImage = My.Resources.icon_videonodata_100
            .ResumeLayout()
        End With

        RemoveHandler dgvTask.CellButtonClick, AddressOf mdgvApps_CellButtonClick
        AddHandler dgvTask.CellButtonClick, AddressOf mdgvApps_CellButtonClick

        RemoveHandler dgvTask.SelectionChanged, AddressOf mdgvApps_SelectionChanged
        AddHandler dgvTask.SelectionChanged, AddressOf mdgvApps_SelectionChanged

        Utility.AddForm2Panel(Me.dgvTask, Me.pnlMain)
    End Sub

    Public Sub mdgvApps_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs)
        If Me.dgvTask.SelectedRows.Count > 0 Then
            Me.btnDelete.Enabled = True
            Dim blnStop As Boolean = False
            Dim blnStart As Boolean = False
            For Each Item As tbDataGridViewRow In Me.dgvTask.SelectedRows
                '设置button状态

                Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                If cellProgress.tbSpeed = Me.Language.GetString("Main.Button.Stop") Then
                    blnStart = True

                ElseIf cellProgress.tbSpeed = Me.Language.GetString("Download.Label.Waiting") Then
                    blnStop = True

                ElseIf cellProgress.tbSpeed = Me.Language.GetString("Media.Message.Finished") Then          '"完成"
                    blnStart = True

                Else
                    blnStop = True

                End If
            Next

            If Me.btnStart.Enabled <> blnStart Then
                Me.btnStart.Enabled = blnStart
            End If

            If Me.btnStop.Enabled <> blnStop Then
                Me.btnStop.Enabled = blnStop
            End If

            If Me.mDevice IsNot Nothing Then
                Me.btnAddToDevice.Enabled = True
            Else
                Me.btnAddToDevice.Enabled = False
            End If

        Else
            Me.btnDelete.Enabled = False
            Me.btnStart.Enabled = False
            Me.btnStop.Enabled = False
            Me.btnAddToDevice.Enabled = False
        End If
        
    End Sub

    Private Sub LoadExitFilesThread()
        Try
            If Me.mthreadLoadFiles IsNot Nothing AndAlso Me.mthreadLoadFiles.ThreadState <> ThreadState.Stopped Then
                Me.mthreadLoadFiles.Abort()
            End If
        Catch ex As Exception
        End Try

        Me.mthreadLoadFiles = New Thread(AddressOf LoadExitFiles)
        Me.mthreadLoadFiles.IsBackground = True
        Me.mthreadLoadFiles.Start()

    End Sub

    Private Sub LoadExitFiles()
        Try
            Dim dictFiles As New Dictionary(Of String, MediaConvertInfo)
            dictFiles = Me.mMusicDB.Get_MediaConvert()
            If dictFiles IsNot Nothing AndAlso dictFiles.Count > 0 Then

                For Each Item As String In dictFiles.Keys
                    Dim info As MediaConvertInfo = dictFiles(Item)

                    Dim task As MediaTask = Me.mMediaMaster.CreateMp4(Item, "VideoConvert")
                    If task IsNot Nothing Then
                        task.GetInfo()
                    Else
                        task = New MediaTask(Guid.NewGuid().ToString)
                        task.InputFile = Item
                        info.Status = MediaConvertStatus.UnFinished
                    End If
                    Dim row As tbDataGridViewRow = Me.AddTaskToDataGridView(info)
                    row.Tag = task
                Next

            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
        Me.btnAddToDevice.Enabled = True
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.btnAddToDevice.Enabled = False
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub mdgvApps_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Dim row As tbDataGridViewRow = Me.dgvTask.Rows(e.Cell.RowIndex)

        If e.CellButton = CellButtonStyle.OneButton Then
            If tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.AreYouSureDeleteTask"), _
                                                  Me.Language.GetString("Common.Delete"), _
                                                          MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                If row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is MediaTask Then
                    Dim task As MediaTask = row.Tag
                    Me.mMusicDB.Delete_MediaConvert(task.InputFile)
                    Me.mMediaMaster.Delete(task)
                End If
                Me.dgvTask.Rows.Remove(row)

            End If
        End If

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            '关闭窗体的时候同时取消正在转换的后台程序。

            For Each Item As tbDataGridViewRow In Me.dgvTask.Rows
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is MediaTask Then
                    Dim task As MediaTask = Item.Tag
                    Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                    If cellProgress.tbProgressValue < 100 Then
                        task.Cancel = True
                        task = Nothing
                    End If

                End If
            Next
        Catch ex As Exception
        End Try

        Try
            If Me.mthreadLoadFiles IsNot Nothing AndAlso Me.mthreadLoadFiles.ThreadState <> ThreadState.Stopped Then
                Me.mthreadLoadFiles.Abort()
            End If
        Catch ex As Exception
        End Try

    End Sub

    Private Sub btnSelectVideo_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnSelectVideo.Click, btnImportVideo.Click
        Dim openFileDlg As New OpenFileDialog()
        openFileDlg.Multiselect = True
        openFileDlg.Title = Me.Language.GetString("Setting.Button.SelectVideo")                                                                         '"选择视频"
        openFileDlg.Filter = Me.Language.GetString("Setting.Label.VideoFile") & " (*.avi;*.wav;*.rmvb;*.mkv;*.mov;*.flv;*.f4v)|*.avi;*.wav;*.rmvb;*.mkv;*.mov;*.flv;*.f4v"      '"视频文件"

        Dim lstFiles As New List(Of String)
        If openFileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
            For Each item As String In openFileDlg.FileNames
                Me.LoadVideoFile(item)
            Next

        End If

    End Sub

    Private Sub btnOpenLocal_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnOpenLocal.Click, tsmiOpenExplorer.Click
        If Me.dgvTask.SelectedRows.Count > 0 Then
            Dim strPath As String = Path.Combine(Folder.Mp4Folder, String.Format("{0}.mp4", Path.GetFileNameWithoutExtension(Me.dgvTask.SelectedRows(0).Cells("FilePath").Value)))
            If File.Exists(strPath) Then
                Common.OpenExplorer(strPath)
                Return
            End If
        End If

        Common.OpenExplorer(Folder.Mp4Folder)
    End Sub

    Private Sub btnDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDelete.Click, tsmiDelete.Click
        If Me.dgvTask.SelectedRows.Count <= 0 Then
            tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.SelectTaskToDelete"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        '"您确定删除任务吗？"
        If tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.AreYouSureDeleteTask"), _
                            Me.Language.GetString("Common.Delete"), _
                            MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then

            Dim lstRow As New List(Of tbDataGridViewRow)
            For Each Item As tbDataGridViewRow In Me.dgvTask.SelectedRows
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is MediaTask Then
                    lstRow.Add(Item)
                End If
            Next

            For Each Item As tbDataGridViewRow In lstRow
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is MediaTask Then
                    Dim task As MediaTask = Item.Tag
                    Me.mMusicDB.Delete_MediaConvert(task.InputFile)
                    Me.mMediaMaster.Delete(task)
                End If
                Me.dgvTask.Rows.Remove(Item)
            Next

            lstRow.Clear()
            lstRow = Nothing

        End If

    End Sub

    Private Sub btnStop_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStop.Click, tsmiStop.Click
        If Me.dgvTask.SelectedRows.Count <= 0 Then
            tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.SelectTaskToPause"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        For Each Item As tbDataGridViewRow In Me.dgvTask.SelectedRows
            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is MediaTask Then
                Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                If cellProgress.tbProgressValue < 100 Then
                    Me.mMediaMaster.Stop(Item.Tag)
                    cellProgress.tbSpeed = Me.Language.GetString("Main.Button.Stop")                         '"停止"
                    cellProgress.tbProgressStyle = ProgressStyle.Text
                    cellProgress.tbText = Me.Language.GetString("Main.Button.Stop")                         '"停止"
                    cellProgress.tbTime = ""

                    cellProgress.Invalidate()
                    Me.btnStart.Enabled = True
                End If

            End If
        Next
    End Sub

    Private Sub btnStart_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStart.Click, tsmiStart.Click
        If Me.dgvTask.SelectedRows.Count <= 0 Then
            tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.SelectDutyToStart"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        For Each Item As tbDataGridViewRow In Me.dgvTask.SelectedRows
            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is MediaTask Then
                Me.btnStop.Enabled = Me.CheckCanStartTask(Item.Tag, Item)
                If Me.btnStop.Enabled Then
                    Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                    cellProgress.tbProgressValue = 0
                    cellProgress.tbSpeed = Me.Language.GetString("Download.Label.Waiting")                  '"正在等待"
                    cellProgress.tbTime = ""
                    cellProgress.tbProgressStyle = ProgressStyle.DownloadProgress
                    cellProgress.tbText = ""

                    cellProgress.Invalidate()
                End If
            End If
        Next

    End Sub

    Private Function CheckCanStartTask(ByVal task As MediaTask, ByVal row As tbDataGridViewRow) As Boolean
        If File.Exists(Folder.Mp4Folder & Path.GetFileName(task.OutputFile)) Then
            Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
            '"文件 ""{0}"" 已经存在了，是否重新转换？"
            If tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Main.Message.FileExist"), Path.GetFileName(task.OutputFile)), Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.None, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
                cellProgress.tbSpeed = Me.Language.GetString("Media.Message.Finished")
                cellProgress.tbProgressValue = 100
                cellProgress.tbProgressStyle = ProgressStyle.Text
                cellProgress.tbText = Me.Language.GetString("Media.Message.Finished")
                Return False
            Else
                If cellProgress.tbProgressStyle <> ProgressStyle.DownloadProgress Then
                    cellProgress.tbProgressValue = 0
                    cellProgress.tbProgressStyle = ProgressStyle.DownloadProgress
                    cellProgress.tbText = ""
                    cellProgress.tbSpeed = Me.Language.GetString("Download.Label.Waiting")                  '"正在等待"
                End If
            End If
        End If

        task.OutputFile = Path.Combine(Folder.TempFolder(), Path.GetFileName(task.OutputFile))
        Me.mMediaMaster.Start(task)
        Return True
    End Function

    Private Sub btn_Visible_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btn_Visible.Click
        Me.Hide()
    End Sub

    Private Sub btnSelectVideo_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSelectVideo.SizeChanged, btnStart.SizeChanged, btnStop.SizeChanged, btnDelete.SizeChanged
        Me.btnStart.Location = New Point(Me.btnSelectVideo.Right + 10, Me.btnStart.Top)
        Me.btnStop.Location = New Point(Me.btnStart.Right + 10, Me.btnStop.Top)
        Me.btnDelete.Location = New Point(Me.btnStop.Right + 10, Me.btnDelete.Top)
    End Sub

    Private Sub btnAddToDevice_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddToDevice.Click
        If Me.mDevice IsNot Nothing Then

            Dim blnCannotSync As Boolean = False

            Dim lstVideo As New List(Of String)
            For Each Item As tbDataGridViewRow In Me.dgvTask.SelectedRows
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is MediaTask Then

                    Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                    If cellProgress.tbProgressValue < 100 OrElse cellProgress.tbProgressStyle = ProgressStyle.Failed Then
                        blnCannotSync = True
                        Exit For
                    End If

                    Dim task As MediaTask = Item.Tag
                    Dim strFilePath As String = Folder.Mp4Folder & Path.GetFileName(task.OutputFile)
                    If File.Exists(strFilePath) Then
                        lstVideo.Add(strFilePath)
                    End If
                End If
            Next

            If blnCannotSync Then
                '"您选择的视频还没有转换成功，无法添加到设备。请转换成功后再添加到设备。"
                tbMessageBox.Show(Me, Me.Language.GetString("Media.Message.CannotImportDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            If lstVideo.Count > 0 Then
                Me.pnlMain.Enabled = False
                Me.pnlTop.Enabled = False
                Me.btnAddToDevice.Enabled = False
                Me.btnOpenLocal.Enabled = False
                Me.lblMessage.Text = Me.Language.GetString("File.Label.UploadPrepare")          '正在准备上传...

                Me.mIPod = IPod.Instance(Me.mDevice, True)

                RemoveHandler mIPod.SyncBegin, AddressOf OnSyncBegin
                RemoveHandler mIPod.SyncEnd, AddressOf OnSyncEnd
                RemoveHandler mIPod.SyncProcess, AddressOf OnSyncProgress

                AddHandler mIPod.SyncBegin, AddressOf OnSyncBegin
                AddHandler mIPod.SyncEnd, AddressOf OnSyncEnd
                AddHandler mIPod.SyncProcess, AddressOf OnSyncProgress

                Me.mIPod.AddMedia(lstVideo.ToArray, Parsers.iTunesDB.MediaType.Video)
            End If

        End If
    End Sub

#End Region

    Private Sub LoadVideoFile(ByVal strFilePath As String)
        If Not File.Exists(strFilePath) Then
            Return
        End If

        If Me.mMediaMaster Is Nothing Then
            Me.mMediaMaster = MediaMaster.Instance
        End If
        '任务放在Tag里面。

        Dim row As tbDataGridViewRow = Me.AddTaskToDataGridView(strFilePath)

        If row Is Nothing Then
            Return
        End If

        'If CType(row.Cells("colProgress"), tbDataGridViewProgressCellEx).tbText = Me.Language.GetString("Media.Message.Finished") AndAlso Not Me.CheckCanStartTask(row.Tag, row) Then
        '    Return
        'End If

        Dim task As MediaTask = Me.mMediaMaster.CreateMp4(strFilePath, "VideoConvert")
        task.GetInfo()

        row.Cells("Duration").Value = Utility.FormatDuration(task.Info.Duration)
        row.Tag = task
        Me.pnlImportVideo.Visible = False
        Me.pnlImportVideo.SendToBack()

        '添加记录到资料库
        Dim strIdentify As String = ""
        If Me.mDevice IsNot Nothing Then
            strIdentify = Me.mDevice.Identifier
        End If

        Dim fileInfo As New FileInfo(task.InputFile)
        Dim longFileSize As Long = 0
        If fileInfo IsNot Nothing Then
            longFileSize = fileInfo.Length
        End If

        Dim info As New MediaConvertInfo()
        info.Name = task.Info.Title
        info.Duration = task.Info.Duration
        info.InputPath = task.InputFile
        info.OutputPath = task.OutputFile
        info.FileSize = longFileSize
        info.Format = "To MP4"
        info.Status = MediaConvertStatus.UnFinished

        If info.Name.Trim.Length <= 0 Then
            info.Name = Path.GetFileNameWithoutExtension(info.InputPath)
        End If

        Me.mMusicDB.Insert_MediaConvert(strIdentify, info)

        Me.CheckCanStartTask(task, row)
        'Me.mMediaMaster.Start(task)
    End Sub

    Private Delegate Function AddTaskToDataGridViewHandler(ByVal strFilePath As String) As tbDataGridViewRow
    Private Function AddTaskToDataGridView(ByVal strFilePath As String) As tbDataGridViewRow
        Dim row As tbDataGridViewRow = Nothing
        If Me.InvokeRequired Then
            row = Me.Invoke(New AddTaskToDataGridViewHandler(AddressOf AddTaskToDataGridView), strFilePath)
        Else
            If Not File.Exists(strFilePath) Then
                Return Nothing
            End If

            If Me.CheckTaskExist(strFilePath) Then
                For Each item As tbDataGridViewRow In Me.dgvTask.Rows
                    If item.Cells("FilePath").Value = strFilePath Then
                        Return item
                    End If
                Next
            End If

            Dim strFileName As String = Path.GetFileNameWithoutExtension(strFilePath)
            Dim info As New FileInfo(strFilePath)
            Dim strDuration As String = ""
            Dim strFileSize As String = ""

            If info IsNot Nothing Then
                strFileSize = Utility.FormatFileSize(info.Length)
            End If

            Dim strToFormat As String = "To MP4"
            Dim intRowIndex As Integer = 0
            Dim aryParameters() As Object = New Object() {False, strFileName, strDuration, strFileSize, strToFormat, 0, "", strFilePath}
            If Me.dgvTask.Rows.Count > 0 Then
                intRowIndex = Me.dgvTask.Rows.Count
            End If

            Me.dgvTask.Rows.Insert(intRowIndex, aryParameters)
            Dim imgControl As Image = My.Resources.dgv_4_download_start
            row = Me.dgvTask.Rows(intRowIndex)
            If row IsNot Nothing Then

                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                cellProgress.tbPadding = New Padding(10, 0, 0, 0)
                cellProgress.tbProgressStyle = ProgressStyle.DownloadProgress
                cellProgress.tbProgressValue = 0
                cellProgress.tbSpeed = Me.Language.GetString("Download.Label.Waiting")                  '"正在等待"
                cellProgress.tbTime = ""

                Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                cellControl.tbPadding = New Padding(10, 0, 0, 0)
                cellControl.tbProgressStyle = ProgressStyle.OneButton
                cellControl.tbShowButtonText = False
                cellControl.tbButtonSize = New Size(18, 18)
                cellControl.tbOneButtonIcon = My.Resources.dgv_4_delete
                cellControl.Tag = MediaConvertState.Waiting

            End If
            Me.pnlImportVideo.Visible = False
            Me.pnlImportVideo.SendToBack()
        End If

        Return row
    End Function

    Private Delegate Function AddTaskToDataGridViewInfoHandler(ByVal info As MediaConvertInfo) As tbDataGridViewRow
    Private Function AddTaskToDataGridView(ByVal info As MediaConvertInfo) As tbDataGridViewRow
        Dim row As tbDataGridViewRow = Nothing
        If Me.InvokeRequired Then
            row = Me.Invoke(New AddTaskToDataGridViewInfoHandler(AddressOf AddTaskToDataGridView), info)
        Else
            If Me.CheckTaskExist(info.InputPath) Then
                Return Nothing
            End If

            Dim intRowIndex As Integer = 0

            Dim aryParameters() As Object = New Object() {False, info.Name, Utility.FormatDuration(info.Duration), Utility.FormatFileSize(info.FileSize), info.Format, 0, "", info.InputPath}
            If Me.dgvTask.Rows.Count > 0 Then
                intRowIndex = Me.dgvTask.Rows.Count
            End If

            Me.dgvTask.Rows.Insert(intRowIndex, aryParameters)
            row = Me.dgvTask.Rows(intRowIndex)

            If row IsNot Nothing Then
                Dim cellProgress As tbDataGridViewProgressCellEx = Me.dgvTask.Rows(intRowIndex).Cells("colProgress")
                cellProgress.tbPadding = New Padding(10, 0, 0, 0)
                cellProgress.tbProgressStyle = ProgressStyle.DownloadProgress
                cellProgress.tbTime = ""

                If info.Status = MediaConvertStatus.Finished OrElse File.Exists(info.OutputPath) Then
                    cellProgress.tbProgressValue = 100
                    cellProgress.tbSpeed = Me.Language.GetString("Media.Message.Finished")                  '"完成"
                    cellProgress.tbProgressStyle = ProgressStyle.Text
                    cellProgress.tbText = Me.Language.GetString("Media.Message.Finished")                   '"完成"
                Else
                    cellProgress.tbProgressValue = 0
                    cellProgress.tbSpeed = Me.Language.GetString("Main.Button.Stop")                         '"停止"
                    cellProgress.tbProgressStyle = ProgressStyle.Text
                    cellProgress.tbText = Me.Language.GetString("Main.Button.Stop")                         '"停止"
                End If

                Dim cellControl As tbDataGridViewProgressCellEx = row.Cells("colControl")
                cellControl.tbPadding = New Padding(10, 0, 0, 0)
                cellControl.tbProgressStyle = ProgressStyle.OneButton
                cellControl.tbShowButtonText = False
                cellControl.tbButtonSize = New Size(18, 18)
                cellControl.tbOneButtonIcon = My.Resources.dgv_4_delete
                cellControl.Tag = MediaConvertState.Waiting

            End If

            Me.pnlImportVideo.Visible = False
            Me.pnlImportVideo.SendToBack()
        End If

        Return row
    End Function

    Private Function CheckTaskExist(ByVal strFilePath)
        Dim blnFileExist As Boolean = False
        For Each item As tbDataGridViewRow In Me.dgvTask.Rows
            If item.Cells("FilePath").Value = strFilePath Then
                blnFileExist = True
                Exit For
            End If

        Next
        Return blnFileExist
    End Function

#Region "--- 媒体转换进度 ---"

    Public Sub ConvertBegin(ByVal sender As Object, ByVal e As iTong.CoreFoundation.ConvertProgressArgs)
        For Each Item As tbDataGridViewRow In Me.dgvTask.Rows
            If e.MediaType = ConvertMediaType.Video AndAlso Item.Cells("FilePath").Value = e.FilePath Then
                Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                cellProgress.tbSpeed = Me.Language.GetString("Download.Label.Waiting")                  '"正在等待"

                Dim cellControl As tbDataGridViewProgressCellEx = Item.Cells("colControl")
                cellControl.Tag = MediaConvertState.Waiting

                'If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is MediaTask Then
                '    Me.mMusicDB.Update_MediaConvert(CType(Item.Tag, MediaTask).InputFile, MediaConvertStatus.UnFinished)
                'End If
                Return
            End If
        Next

    End Sub

    Public Sub ConvertEnd(ByVal sender As Object, ByVal e As iTong.CoreFoundation.ConvertProgressArgs)
        For Each Item As tbDataGridViewRow In Me.dgvTask.Rows
            If e.MediaType = ConvertMediaType.Video AndAlso Item.Cells("FilePath").Value = e.FilePath Then
                Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                cellProgress.tbProgressValue = 100
                cellProgress.tbSpeed = Me.Language.GetString("Media.Message.Finished")              '"完成"
                cellProgress.tbTime = ""
                cellProgress.tbProgressStyle = ProgressStyle.Text
                cellProgress.tbText = Me.Language.GetString("Media.Message.Finished")               '"完成"
                Dim filePath As String = ""
                Dim info As FileInfo = Nothing
                If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is MediaTask Then
                    Dim task As MediaTask = Item.Tag
                    Me.mMusicDB.Update_MediaConvert(task.InputFile, MediaConvertStatus.Finished)

                    Try
                        filePath = Folder.Mp4Folder & Path.GetFileName(task.OutputFile)
                        If File.Exists(filePath) Then
                            File.Delete(filePath)
                        End If
                        File.Move(task.OutputFile, filePath)
                        info = New FileInfo(filePath)
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "frmVideoConvert_ConvertEnd")
                    End Try
                End If

                If Not File.Exists(filePath) OrElse info Is Nothing OrElse info.Length <= 0 Then
                    cellProgress.tbProgressStyle = ProgressStyle.Failed
                    cellProgress.tbTextFail = Me.Language.GetString("Weixin.Lable.ConvertFailure")      '"视频转换失败！"
                    cellProgress.tbText = ""
                End If
            End If
        Next
    End Sub

    Public Sub ConvertProgress(ByVal sender As Object, ByVal e As iTong.CoreFoundation.ConvertProgressArgs)
        For Each Item As tbDataGridViewRow In Me.dgvTask.Rows
            If e.MediaType = ConvertMediaType.Video AndAlso Item.Cells("FilePath").Value = e.FilePath Then
                Dim cellProgress As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                cellProgress.tbProgressValue = e.ProgressValue
                If cellProgress.tbProgressStyle <> ProgressStyle.DownloadProgress Then
                    cellProgress.tbProgressStyle = ProgressStyle.DownloadProgress
                    cellProgress.tbText = ""
                End If

                cellProgress.tbSpeed = ""
                cellProgress.tbTime = ""
                cellProgress.tbSpeed = Utility.FormatDuration(e.TimeRemaining)

            End If
        Next
    End Sub

#End Region

#Region "--- 同步媒体进度 ---"

    Private Sub OnSyncProgress(ByVal sender As Object, ByVal e As SyncMediaEventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of SyncMediaEventArgs)(AddressOf OnSyncProgress), sender, e)
            Else
                Select Case e.Action
                    Case SyncAction.Prepair
                        Me.lblMessage.Text = Me.Language.GetString("File.Label.UploadPrepare")          '正在准备上传...

                    Case SyncAction.UpdateDatabase
                        Me.lblMessage.Text = Me.Language.GetString("Media.Message.UpdateDB")                 '"正在更新资料库..."

                    Case SyncAction.Transfer
                        '正在上传：{0}           {1}/{2}
                        Me.lblMessage.Text = String.Format(Me.Language.GetString("Photo.Message.UploadingPhoto"), e.Current.Title, Utility.FormatFileSize(e.TranSize), Utility.FormatFileSize(e.Current.FileSize.ByteCount))

                    Case SyncAction.Completed
                        If e.FailedList.Count > 0 Then
                            Dim strFileUploadFailure As String = Me.Language.GetString("Photo.Message.FileUploadFailure") & Me.Language.GetString("Common.Symbol.Colon")
                            For Each Item As String In e.FailedList
                                strFileUploadFailure &= Item
                            Next
                            tbMessageBox.Show(Me, strFileUploadFailure, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)

                        Else
                            Me.lblMessage.Text = Me.Language.GetString("RingtoneMaker.Message.ImportSucceed")              '导入成功！

                        End If


                End Select
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub OnSyncBegin(ByVal sender As Object, ByVal e As EventArgs)
        Me.pnlMain.Enabled = False
        Me.pnlTop.Enabled = False
        Me.btnAddToDevice.Enabled = False
        Me.btnOpenLocal.Enabled = False
    End Sub

    Private Sub OnSyncEnd(ByVal sender As Object, ByVal e As EventArgs)
        Me.pnlMain.Enabled = True
        Me.pnlTop.Enabled = True
        Me.btnAddToDevice.Enabled = True
        Me.btnOpenLocal.Enabled = True
        Utility.WaitSeconds(3)
        Me.lblMessage.Text = ""

        If Me.mIPod IsNot Nothing Then
            RemoveHandler mIPod.SyncBegin, AddressOf OnSyncBegin
            RemoveHandler mIPod.SyncEnd, AddressOf OnSyncEnd
            RemoveHandler mIPod.SyncProcess, AddressOf OnSyncProgress
        End If
    End Sub

#End Region

    Private Sub btnStart_EnabledChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStart.EnabledChanged
        Me.tsmiStart.Enabled = Me.btnStart.Enabled
    End Sub

    Private Sub btnStop_EnabledChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStop.EnabledChanged
        Me.tsmiStop.Enabled = Me.btnStop.Enabled
    End Sub

    Private Sub btnAddToDevice_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnAddToDevice.SizeChanged, btnOpenLocal.SizeChanged
        Me.btnAddToDevice.Location = New Point(Me.Width - Me.btnAddToDevice.Width - 20, Me.btnAddToDevice.Top)
        Me.btnOpenLocal.Location = New Point(Me.btnAddToDevice.Left - Me.btnOpenLocal.Width - 10, Me.btnOpenLocal.Top)
        Me.lblMessage.Size = New Size(Me.btnOpenLocal.Left - Me.lblMessage.Left - 5, Me.lblMessage.Height)
        Me.btnOpenLocal.BringToFront()

    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_sub
        End If
    End Sub

End Class
