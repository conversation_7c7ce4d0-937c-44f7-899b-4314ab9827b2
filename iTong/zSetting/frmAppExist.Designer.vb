﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmAppExist
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。


    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的


    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的


    '可以使用 Windows 窗体设计器修改它。


    '不要使用代码编辑器修改它。


    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmAppExist))
        Me.btn_close = New tbButton
        Me.pnlMain = New tbPanel
        Me.gpbExist = New System.Windows.Forms.GroupBox
        Me.rdbExist = New tbRadioButton
        Me.rdbMini = New tbRadioButton
        Me.chkRemember = New tbCheckBox
        Me.btnCancel = New tbButton
        Me.btnOK = New tbButton
        Me.pnlMain.SuspendLayout()
        Me.gpbExist.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(321, 0)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 9
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = My.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.Transparent
        Me.pnlMain.Controls.Add(Me.gpbExist)
        Me.pnlMain.Location = New System.Drawing.Point(1, 31)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(343, 122)
        Me.pnlMain.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlMain.TabIndex = 0
        Me.pnlMain.tbBackgroundImage = Nothing
        Me.pnlMain.tbShowWatermark = False
        Me.pnlMain.tbSplit = "8,22,8,9"
        Me.pnlMain.tbWatermark = Nothing
        Me.pnlMain.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlMain.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'gpbExist
        '
        Me.gpbExist.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.gpbExist.Controls.Add(Me.rdbExist)
        Me.gpbExist.Controls.Add(Me.rdbMini)
        Me.gpbExist.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.gpbExist.Location = New System.Drawing.Point(12, 13)
        Me.gpbExist.Name = "gpbExist"
        Me.gpbExist.Size = New System.Drawing.Size(320, 101)
        Me.gpbExist.TabIndex = 5
        Me.gpbExist.TabStop = False
        Me.gpbExist.Text = "您点击了关闭窗口，您想"
        '
        'rdbExist
        '
        Me.rdbExist.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.rdbExist.Appearance = System.Windows.Forms.Appearance.Button
        Me.rdbExist.BackColor = System.Drawing.Color.Transparent
        Me.rdbExist.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbExist.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbExist.Location = New System.Drawing.Point(36, 67)
        Me.rdbExist.Name = "rdbExist"
        Me.rdbExist.Size = New System.Drawing.Size(151, 22)
        Me.rdbExist.TabIndex = 50
        Me.rdbExist.TabStop = True
        Me.rdbExist.tbAdriftIconWhenHover = False
        Me.rdbExist.tbAutoSize = False
        Me.rdbExist.tbAutoSizeEx = False
        Me.rdbExist.tbBadgeNumber = 0
        Me.rdbExist.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.rdbExist.tbIconCheckedMouseDown = My.Resources.btn_checked
        Me.rdbExist.tbIconCheckedMouseHover = My.Resources.btn_checked
        Me.rdbExist.tbIconCheckedMouseLeave = My.Resources.btn_checked
        Me.rdbExist.tbIconHoldPlace = True
        Me.rdbExist.tbIconImage = Nothing
        Me.rdbExist.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.rdbExist.tbIconImageState = ImageState.OneState
        Me.rdbExist.tbIconMore = False
        Me.rdbExist.tbIconPlaceText = 5
        Me.rdbExist.tbIconReadOnly = Nothing
        Me.rdbExist.tbIconUnCheckedMouseDown = My.Resources.btn_unchecked
        Me.rdbExist.tbIconUnCheckedMouseHover = My.Resources.btn_unchecked
        Me.rdbExist.tbIconUnCheckedMouseLeave = My.Resources.btn_unchecked
        Me.rdbExist.tbImageBackground = Nothing
        Me.rdbExist.tbImageBackgroundState = ImageState.TwoState
        Me.rdbExist.tbImageCheckedMouseDown = Nothing
        Me.rdbExist.tbImageCheckedMouseHover = Nothing
        Me.rdbExist.tbImageCheckedMouseLeave = Nothing
        Me.rdbExist.tbImageUnCheckedMouseDown = Nothing
        Me.rdbExist.tbImageUnCheckedMouseHover = Nothing
        Me.rdbExist.tbImageUnCheckedMouseLeave = Nothing
        Me.rdbExist.tbReadOnly = False
        Me.rdbExist.tbShadow = False
        Me.rdbExist.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.rdbExist.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.rdbExist.tbShowNew = False
        Me.rdbExist.tbSplit = "3,3,3,3"
        Me.rdbExist.tbTag = Nothing
        Me.rdbExist.tbToolTip = ""
        Me.rdbExist.Text = "退出程序"
        Me.rdbExist.UseVisualStyleBackColor = False
        '
        'rdbMini
        '
        Me.rdbMini.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.rdbMini.Appearance = System.Windows.Forms.Appearance.Button
        Me.rdbMini.BackColor = System.Drawing.Color.Transparent
        Me.rdbMini.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbMini.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.rdbMini.Location = New System.Drawing.Point(36, 31)
        Me.rdbMini.Name = "rdbMini"
        Me.rdbMini.Size = New System.Drawing.Size(151, 22)
        Me.rdbMini.TabIndex = 49
        Me.rdbMini.TabStop = True
        Me.rdbMini.tbAdriftIconWhenHover = False
        Me.rdbMini.tbAutoSize = False
        Me.rdbMini.tbAutoSizeEx = False
        Me.rdbMini.tbBadgeNumber = 0
        Me.rdbMini.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.rdbMini.tbIconCheckedMouseDown = My.Resources.btn_checked
        Me.rdbMini.tbIconCheckedMouseHover = My.Resources.btn_checked
        Me.rdbMini.tbIconCheckedMouseLeave = My.Resources.btn_checked
        Me.rdbMini.tbIconHoldPlace = True
        Me.rdbMini.tbIconImage = Nothing
        Me.rdbMini.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.rdbMini.tbIconImageState = ImageState.OneState
        Me.rdbMini.tbIconMore = False
        Me.rdbMini.tbIconPlaceText = 5
        Me.rdbMini.tbIconReadOnly = Nothing
        Me.rdbMini.tbIconUnCheckedMouseDown = My.Resources.btn_unchecked
        Me.rdbMini.tbIconUnCheckedMouseHover = My.Resources.btn_unchecked
        Me.rdbMini.tbIconUnCheckedMouseLeave = My.Resources.btn_unchecked
        Me.rdbMini.tbImageBackground = Nothing
        Me.rdbMini.tbImageBackgroundState = ImageState.TwoState
        Me.rdbMini.tbImageCheckedMouseDown = Nothing
        Me.rdbMini.tbImageCheckedMouseHover = Nothing
        Me.rdbMini.tbImageCheckedMouseLeave = Nothing
        Me.rdbMini.tbImageUnCheckedMouseDown = Nothing
        Me.rdbMini.tbImageUnCheckedMouseHover = Nothing
        Me.rdbMini.tbImageUnCheckedMouseLeave = Nothing
        Me.rdbMini.tbReadOnly = False
        Me.rdbMini.tbShadow = False
        Me.rdbMini.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.rdbMini.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.rdbMini.tbShowNew = False
        Me.rdbMini.tbSplit = "3,3,3,3"
        Me.rdbMini.tbTag = Nothing
        Me.rdbMini.tbToolTip = ""
        Me.rdbMini.Text = "最小化到托盘"
        Me.rdbMini.UseVisualStyleBackColor = False
        '
        'chkRemember
        '
        Me.chkRemember.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.chkRemember.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkRemember.BackColor = System.Drawing.Color.Transparent
        Me.chkRemember.Checked = True
        Me.chkRemember.CheckState = System.Windows.Forms.CheckState.Checked
        Me.chkRemember.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkRemember.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkRemember.Location = New System.Drawing.Point(13, 156)
        Me.chkRemember.Name = "chkRemember"
        Me.chkRemember.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkRemember.Size = New System.Drawing.Size(104, 27)
        Me.chkRemember.TabIndex = 15
        Me.chkRemember.tbAdriftIconWhenHover = False
        Me.chkRemember.tbAutoSize = False
        Me.chkRemember.tbAutoSizeEx = True
        'Me.chkRemember.tbIconChecked = CType(resources.GetObject("chkRemember.tbIconChecked"), System.Drawing.Image)
        Me.chkRemember.tbIconCheckedMouseDown = Nothing
        Me.chkRemember.tbIconCheckedMouseHover = Nothing
        Me.chkRemember.tbIconCheckedMouseLeave = Nothing
        Me.chkRemember.tbIconCheckedState = ImageState.FourState
        Me.chkRemember.tbIconHoldPlace = True
        Me.chkRemember.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkRemember.tbIconPlaceText = 1
        'Me.chkRemember.tbIconUnChecked = CType(resources.GetObject("chkRemember.tbIconUnChecked"), System.Drawing.Image)
        Me.chkRemember.tbIconUnCheckedMouseDown = Nothing
        Me.chkRemember.tbIconUnCheckedMouseHover = Nothing
        Me.chkRemember.tbIconUnCheckedMouseLeave = Nothing
        Me.chkRemember.tbIconUnCheckedState = ImageState.FourState
        Me.chkRemember.tbImageBackground = Nothing
        Me.chkRemember.tbImageBackgroundState = ImageState.TwoState
        Me.chkRemember.tbImageCheckedMouseDown = Nothing
        Me.chkRemember.tbImageCheckedMouseHover = Nothing
        Me.chkRemember.tbImageCheckedMouseLeave = Nothing
        Me.chkRemember.tbImageUnCheckedMouseDown = Nothing
        Me.chkRemember.tbImageUnCheckedMouseHover = Nothing
        Me.chkRemember.tbImageUnCheckedMouseLeave = Nothing
        Me.chkRemember.tbReadOnly = False
        Me.chkRemember.tbShadow = False
        Me.chkRemember.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkRemember.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkRemember.tbSplit = "3,3,3,3"
        Me.chkRemember.tbToolTip = ""
        Me.chkRemember.Text = "记住我的选择"
        Me.chkRemember.UseVisualStyleBackColor = False
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.Location = New System.Drawing.Point(270, 158)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(65, 23)
        Me.btnCancel.TabIndex = 10
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.Black
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(193, 158)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(65, 23)
        Me.btnOK.TabIndex = 10
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "确定"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'frmAppExist
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.White
        Me.ClientSize = New System.Drawing.Size(345, 191)
        Me.Controls.Add(Me.chkRemember)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.btn_close)
        Me.FilletRadius = 3
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.MinimumSize = New System.Drawing.Size(40, 140)
        Me.Name = "frmAppExist"
        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbShowWatermark = True
        Me.tbSplit = "20,104,20,36"
        Me.tbTitleLocation = New System.Drawing.Point(4, 8)
        Me.Text = "提示"
        Me.pnlMain.ResumeLayout(False)
        Me.gpbExist.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As tbButton
    Friend WithEvents pnlMain As tbPanel
    Friend WithEvents btnOK As tbButton
    Friend WithEvents btnCancel As tbButton
    Friend WithEvents gpbExist As System.Windows.Forms.GroupBox
    Friend WithEvents chkRemember As tbCheckBox
    Friend WithEvents rdbMini As tbRadioButton
    Friend WithEvents rdbExist As tbRadioButton
End Class
