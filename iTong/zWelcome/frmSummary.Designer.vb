﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSummary
    Inherits frmDeviceBase

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSummary))
        Me.lblVersion = New iTong.Components.tbLabel()
        Me.btnDisconnect = New iTong.Components.tbButton()
        Me.lblDownloadProgress = New iTong.Components.tbLabel()
        Me.pnlChangeDeviceName = New iTong.Components.tbPanel()
        Me.txtNameValue = New iTong.Components.tbTextBox()
        Me.pbDevice = New System.Windows.Forms.PictureBox()
        Me.pnlScreenshot = New iTong.Components.tbPanel()
        Me.PictureBox4 = New System.Windows.Forms.PictureBox()
        Me.btnBatteryManager = New iTong.Components.tbButton()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.PictureBox3 = New System.Windows.Forms.PictureBox()
        Me.btnScreenshotSetUp = New iTong.Components.tbButton()
        Me.btnScreenshotPlay = New iTong.Components.tbButton()
        Me.btnShareTo = New iTong.Components.tbButton()
        Me.btnScreenshot = New iTong.Components.tbButton()
        Me.btnRename = New iTong.Components.tbButton()
        Me.tbCapacity = New iTong.Components.tbDeviceCapacity()
        Me.btnClear = New iTong.Components.tbButton()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.btnDetail = New iTong.Components.tbButton()
        Me.lblNameValue = New iTong.Components.tbLabel()
        Me.cmsSetting = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiScreenshot = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiScreenshotShell = New System.Windows.Forms.ToolStripMenuItem()
        Me.btnJump = New iTong.Components.tbButton()
        Me.btnClearTip = New iTong.Components.tbButton()
        Me.cmsWeibo = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiSina = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTecent = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiFacebook = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTwitter = New System.Windows.Forms.ToolStripMenuItem()
        Me.lblCharging = New System.Windows.Forms.Label()
        Me.picCharging = New System.Windows.Forms.PictureBox()
        Me.tmrRefresh = New System.Windows.Forms.Timer(Me.components)
        Me.pnlWeb = New iTong.Components.tbPanel()
        Me.pnlDeviceName = New iTong.Components.tbPanel()
        Me.btnCanceliOSUpgrade = New iTong.Components.tbButton()
        Me.btnGoJailbreakWeb = New iTong.Components.tbButton()
        Me.tmrTui = New System.Windows.Forms.Timer(Me.components)
        Me.btnYueYu = New iTong.Components.tbButton()
        Me.bgwStartJailbreak = New System.ComponentModel.BackgroundWorker()
        Me.pnlToolV3 = New iTong.Components.tbPanel()
        Me.btnQQ = New iTong.Components.tbButton()
        Me.btnH5Game = New iTong.Components.tbButton()
        Me.btnRepairPay = New iTong.Components.tbButton()
        Me.btnBinding = New iTong.Components.tbButton()
        Me.btnWeixin = New iTong.Components.tbButton()
        Me.btnBackupRestore = New iTong.Components.tbButton()
        Me.btnTuiToolV3 = New iTong.Components.tbButton()
        Me.btnRepairToolV3 = New iTong.Components.tbButton()
        Me.btnBindingTip = New iTong.Components.tbButton()
        Me.pnlDFUModeInfo = New iTong.Components.tbPanel()
        Me.btnCopyDeviceId = New iTong.Components.tbButton()
        Me.lblCopyInfo = New iTong.Components.tbLabel()
        Me.lblNote = New iTong.Components.tbLabel()
        Me.btnGotoFlashDevice = New iTong.Components.tbButton()
        Me.btnOutRecoverMode = New iTong.Components.tbButton()
        Me.lblDFUDescription2 = New iTong.Components.tbButton()
        Me.lblDFUDescription1 = New iTong.Components.tbButton()
        Me.lblDeviceIdValue = New iTong.Components.tbLabel()
        Me.lblDeviceId = New iTong.Components.tbLabel()
        Me.lblECIDValue = New iTong.Components.tbLabel()
        Me.lblProductTypeValue = New iTong.Components.tbLabel()
        Me.lblDeviceTypeValue = New iTong.Components.tbLabel()
        Me.lblECID = New iTong.Components.tbLabel()
        Me.lblProductType = New iTong.Components.tbLabel()
        Me.lblDeviceType = New iTong.Components.tbLabel()
        Me.lblDFUTitle = New iTong.Components.tbLabel()
        Me.lblConnectMode = New System.Windows.Forms.Label()
        Me.tmrShowCopyInfo = New System.Windows.Forms.Timer(Me.components)
        Me.pnlSummary = New iTong.Components.tbPanel()
        Me.lblBackup = New System.Windows.Forms.LinkLabel()
        Me.picSplit3 = New System.Windows.Forms.PictureBox()
        Me.picSplit2 = New System.Windows.Forms.PictureBox()
        Me.picSplit1 = New System.Windows.Forms.PictureBox()
        Me.btnDeviceInfo = New iTong.Components.tbButton()
        Me.btnEditDeviceName = New iTong.Components.tbButton()
        Me.lblDeviceName = New iTong.Components.tbLabel()
        Me.txtDeviceName = New iTong.Components.tbTextBox()
        Me.btnRingtone = New iTong.Components.tbButton()
        Me.btniOSDowngrade = New iTong.Components.tbButton()
        Me.btnWechat = New iTong.Components.tbButton()
        Me.pnlDeviceInfo = New iTong.Components.tbPanel()
        Me.btnMore = New iTong.Components.tbButton()
        Me.lblChargingDetail = New System.Windows.Forms.LinkLabel()
        Me.lblCloseiOSUpdate = New System.Windows.Forms.LinkLabel()
        Me.lblDeviceIdentifyValue = New iTong.Components.tbButton()
        Me.lblIMEIValue = New iTong.Components.tbButton()
        Me.lblSNValue = New iTong.Components.tbButton()
        Me.cbxCloseiTunesStart = New iTong.Components.tbCheckBox()
        Me.lblDeviceIdentify = New iTong.Components.tbLabel()
        Me.lblChargingPercentValue = New iTong.Components.tbLabel()
        Me.lblChargingPercent = New iTong.Components.tbLabel()
        Me.lblChargingTimesValue = New iTong.Components.tbLabel()
        Me.lblChargingTimes = New iTong.Components.tbLabel()
        Me.lblGuaranteeDateValue = New iTong.Components.tbLabel()
        Me.lblGuaranteeDate = New iTong.Components.tbLabel()
        Me.lblProductDateValue = New iTong.Components.tbLabel()
        Me.lblProductDate = New iTong.Components.tbLabel()
        Me.lblSaleAreaValue = New iTong.Components.tbLabel()
        Me.lblSaleArea = New iTong.Components.tbLabel()
        Me.lblIMEI = New iTong.Components.tbLabel()
        Me.lblSN = New iTong.Components.tbLabel()
        Me.lbliOSVersionValue = New iTong.Components.tbLabel()
        Me.lbliOSVersion = New iTong.Components.tbLabel()
        Me.lblDeviceColor = New iTong.Components.tbLabel()
        Me.btnAppeidBinding = New iTong.Components.tbButton()
        Me.btnInstallTui = New iTong.Components.tbButton()
        Me.btnAuthDevice = New iTong.Components.tbButton()
        Me.btnCloseDevice = New iTong.Components.tbButton()
        Me.btnRestarDevice = New iTong.Components.tbButton()
        Me.btnDeviceDesktop = New iTong.Components.tbButton()
        Me.btnScreensort1 = New iTong.Components.tbButton()
        Me.lblDownPluginProgress = New iTong.Components.tbLabel()
        Me.picDevice = New System.Windows.Forms.PictureBox()
        Me.tbDevCapacity = New iTong.Components.tbDeviceCapacity()
        Me.btnClearRabbish = New iTong.Components.tbButton()
        Me.picLoadingDeviceData = New System.Windows.Forms.PictureBox()
        Me.btnRefreshDeviceData = New iTong.Components.tbButton()
        Me.btnQQChat = New iTong.Components.tbButton()
        Me.btnZXKF = New iTong.Components.tbButton()
        Me.pnlChangeDeviceName.SuspendLayout()
        CType(Me.pbDevice, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlScreenshot.SuspendLayout()
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tbCapacity.SuspendLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.cmsSetting.SuspendLayout()
        Me.cmsWeibo.SuspendLayout()
        CType(Me.picCharging, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDeviceName.SuspendLayout()
        Me.pnlToolV3.SuspendLayout()
        Me.pnlDFUModeInfo.SuspendLayout()
        Me.pnlSummary.SuspendLayout()
        CType(Me.picSplit3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picSplit2, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picSplit1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDeviceInfo.SuspendLayout()
        CType(Me.picDevice, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tbDevCapacity.SuspendLayout()
        CType(Me.picLoadingDeviceData, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(1484, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(1460, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(1436, 0)
        '
        'lblVersion
        '
        Me.lblVersion.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblVersion.AutoSize = True
        Me.lblVersion.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblVersion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblVersion.Location = New System.Drawing.Point(4, 64)
        Me.lblVersion.Name = "lblVersion"
        Me.lblVersion.Size = New System.Drawing.Size(194, 24)
        Me.lblVersion.TabIndex = 17
        Me.lblVersion.tbAdriftWhenHover = False
        Me.lblVersion.tbAutoEllipsis = False
        Me.lblVersion.tbAutoSize = True
        Me.lblVersion.tbHideImage = False
        Me.lblVersion.tbIconImage = Nothing
        Me.lblVersion.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblVersion.tbIconPlaceText = 5
        Me.lblVersion.tbShadow = False
        Me.lblVersion.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblVersion.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblVersion.tbShowScrolling = False
        Me.lblVersion.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnDisconnect
        '
        Me.btnDisconnect.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnDisconnect.BackColor = System.Drawing.Color.Transparent
        Me.btnDisconnect.BindingForm = Nothing
        Me.btnDisconnect.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDisconnect.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDisconnect.Location = New System.Drawing.Point(244, 42)
        Me.btnDisconnect.Name = "btnDisconnect"
        Me.btnDisconnect.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDisconnect.Selectable = True
        Me.btnDisconnect.Size = New System.Drawing.Size(15, 15)
        Me.btnDisconnect.TabIndex = 16
        Me.btnDisconnect.TabStop = False
        Me.btnDisconnect.tbAdriftIconWhenHover = False
        Me.btnDisconnect.tbAutoSize = True
        Me.btnDisconnect.tbAutoSizeEx = False
        Me.btnDisconnect.tbBackgroundImage = Nothing
        Me.btnDisconnect.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnDisconnect.tbBadgeNumber = 0
        Me.btnDisconnect.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDisconnect.tbEndEllipsis = False
        Me.btnDisconnect.tbIconHoldPlace = True
        Me.btnDisconnect.tbIconImage = Nothing
        Me.btnDisconnect.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDisconnect.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDisconnect.tbIconMore = False
        Me.btnDisconnect.tbIconMouseDown = Nothing
        Me.btnDisconnect.tbIconMouseHover = Nothing
        Me.btnDisconnect.tbIconMouseLeave = Nothing
        Me.btnDisconnect.tbIconPlaceText = 2
        Me.btnDisconnect.tbIconReadOnly = Nothing
        Me.btnDisconnect.tbImageMouseDown = Nothing
        Me.btnDisconnect.tbImageMouseHover = Nothing
        Me.btnDisconnect.tbImageMouseLeave = Nothing
        Me.btnDisconnect.tbProgressValue = 50
        Me.btnDisconnect.tbReadOnly = False
        Me.btnDisconnect.tbReadOnlyText = False
        Me.btnDisconnect.tbShadow = False
        Me.btnDisconnect.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDisconnect.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDisconnect.tbShowDot = False
        Me.btnDisconnect.tbShowMoreIconImg = CType(resources.GetObject("btnDisconnect.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDisconnect.tbShowNew = False
        Me.btnDisconnect.tbShowProgress = False
        Me.btnDisconnect.tbShowTip = True
        Me.btnDisconnect.tbShowToolTipOnButton = False
        Me.btnDisconnect.tbSplit = "0,0,0,0"
        Me.btnDisconnect.tbText = ""
        Me.btnDisconnect.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDisconnect.tbTextColor = System.Drawing.Color.Black
        Me.btnDisconnect.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnDisconnect.tbTextColorDown = System.Drawing.Color.Black
        Me.btnDisconnect.tbTextColorHover = System.Drawing.Color.Black
        Me.btnDisconnect.tbTextMouseDownPlace = 0
        Me.btnDisconnect.tbToolTip = ""
        Me.btnDisconnect.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDisconnect.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDisconnect.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDisconnect.VisibleEx = True
        '
        'lblDownloadProgress
        '
        Me.lblDownloadProgress.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDownloadProgress.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDownloadProgress.ForeColor = System.Drawing.SystemColors.ControlDarkDark
        Me.lblDownloadProgress.Location = New System.Drawing.Point(437, 418)
        Me.lblDownloadProgress.Name = "lblDownloadProgress"
        Me.lblDownloadProgress.Size = New System.Drawing.Size(216, 17)
        Me.lblDownloadProgress.TabIndex = 6
        Me.lblDownloadProgress.tbAdriftWhenHover = False
        Me.lblDownloadProgress.tbAutoEllipsis = False
        Me.lblDownloadProgress.tbAutoSize = False
        Me.lblDownloadProgress.tbHideImage = False
        Me.lblDownloadProgress.tbIconImage = Nothing
        Me.lblDownloadProgress.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDownloadProgress.tbIconPlaceText = 5
        Me.lblDownloadProgress.tbShadow = False
        Me.lblDownloadProgress.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDownloadProgress.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDownloadProgress.tbShowScrolling = False
        Me.lblDownloadProgress.Text = "正在下载插件： 10%"
        Me.lblDownloadProgress.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDownloadProgress.Visible = False
        '
        'pnlChangeDeviceName
        '
        Me.pnlChangeDeviceName.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.pnlChangeDeviceName.Controls.Add(Me.txtNameValue)
        Me.pnlChangeDeviceName.Location = New System.Drawing.Point(1, 1)
        Me.pnlChangeDeviceName.Name = "pnlChangeDeviceName"
        Me.pnlChangeDeviceName.Size = New System.Drawing.Size(194, 34)
        Me.pnlChangeDeviceName.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlChangeDeviceName.TabIndex = 14
        Me.pnlChangeDeviceName.tbBackgroundImage = Nothing
        Me.pnlChangeDeviceName.tbShowWatermark = False
        Me.pnlChangeDeviceName.tbSplit = "0,0,0,0"
        Me.pnlChangeDeviceName.tbWatermark = Nothing
        Me.pnlChangeDeviceName.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlChangeDeviceName.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlChangeDeviceName.Visible = False
        '
        'txtNameValue
        '
        Me.txtNameValue.BackColor = System.Drawing.Color.White
        Me.txtNameValue.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtNameValue.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtNameValue.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtNameValue.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtNameValue.Dock = System.Windows.Forms.DockStyle.Top
        Me.txtNameValue.Font = New System.Drawing.Font("宋体", 15.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.txtNameValue.ForeColor = System.Drawing.Color.Black
        Me.txtNameValue.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNameValue.Location = New System.Drawing.Point(0, 0)
        Me.txtNameValue.Margin = New System.Windows.Forms.Padding(0)
        Me.txtNameValue.MaxLength = 50
        Me.txtNameValue.Name = "txtNameValue"
        Me.txtNameValue.Size = New System.Drawing.Size(194, 33)
        Me.txtNameValue.TabIndex = 0
        Me.txtNameValue.Tag = Nothing
        Me.txtNameValue.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtNameValue.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtNameValue.tbSelMark = True
        Me.txtNameValue.tbTextBind = "设备名称"
        Me.txtNameValue.Text = "设备名称"
        Me.txtNameValue.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtNameValue.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtNameValue.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtNameValue.TextTip = ""
        '
        'pbDevice
        '
        Me.pbDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbDevice.Location = New System.Drawing.Point(423, 57)
        Me.pbDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.pbDevice.Name = "pbDevice"
        Me.pbDevice.Size = New System.Drawing.Size(240, 321)
        Me.pbDevice.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.pbDevice.TabIndex = 0
        Me.pbDevice.TabStop = False
        '
        'pnlScreenshot
        '
        Me.pnlScreenshot.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlScreenshot.Controls.Add(Me.PictureBox4)
        Me.pnlScreenshot.Controls.Add(Me.btnBatteryManager)
        Me.pnlScreenshot.Controls.Add(Me.PictureBox1)
        Me.pnlScreenshot.Controls.Add(Me.PictureBox2)
        Me.pnlScreenshot.Controls.Add(Me.PictureBox3)
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshotSetUp)
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshotPlay)
        Me.pnlScreenshot.Controls.Add(Me.btnShareTo)
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshot)
        Me.pnlScreenshot.Location = New System.Drawing.Point(442, 387)
        Me.pnlScreenshot.Name = "pnlScreenshot"
        Me.pnlScreenshot.Size = New System.Drawing.Size(180, 24)
        Me.pnlScreenshot.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlScreenshot.TabIndex = 7
        Me.pnlScreenshot.tbBackgroundImage = Nothing
        Me.pnlScreenshot.tbShowWatermark = False
        Me.pnlScreenshot.tbSplit = "4,4,4,4"
        Me.pnlScreenshot.tbWatermark = Nothing
        Me.pnlScreenshot.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlScreenshot.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'PictureBox4
        '
        Me.PictureBox4.Location = New System.Drawing.Point(144, 1)
        Me.PictureBox4.Name = "PictureBox4"
        Me.PictureBox4.Size = New System.Drawing.Size(1, 24)
        Me.PictureBox4.TabIndex = 41
        Me.PictureBox4.TabStop = False
        '
        'btnBatteryManager
        '
        Me.btnBatteryManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBatteryManager.BackColor = System.Drawing.Color.Transparent
        Me.btnBatteryManager.BindingForm = Nothing
        Me.btnBatteryManager.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBatteryManager.Location = New System.Drawing.Point(117, 3)
        Me.btnBatteryManager.Name = "btnBatteryManager"
        Me.btnBatteryManager.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnBatteryManager.Selectable = True
        Me.btnBatteryManager.Size = New System.Drawing.Size(18, 18)
        Me.btnBatteryManager.TabIndex = 40
        Me.btnBatteryManager.TabStop = False
        Me.btnBatteryManager.tbAdriftIconWhenHover = False
        Me.btnBatteryManager.tbAutoSize = False
        Me.btnBatteryManager.tbAutoSizeEx = False
        Me.btnBatteryManager.tbBackgroundImage = Nothing
        Me.btnBatteryManager.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBatteryManager.tbBadgeNumber = 0
        Me.btnBatteryManager.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBatteryManager.tbEndEllipsis = False
        Me.btnBatteryManager.tbIconHoldPlace = True
        Me.btnBatteryManager.tbIconImage = Nothing
        Me.btnBatteryManager.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBatteryManager.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBatteryManager.tbIconMore = False
        Me.btnBatteryManager.tbIconMouseDown = Nothing
        Me.btnBatteryManager.tbIconMouseHover = Nothing
        Me.btnBatteryManager.tbIconMouseLeave = Nothing
        Me.btnBatteryManager.tbIconPlaceText = 2
        Me.btnBatteryManager.tbIconReadOnly = Nothing
        Me.btnBatteryManager.tbImageMouseDown = Nothing
        Me.btnBatteryManager.tbImageMouseHover = Nothing
        Me.btnBatteryManager.tbImageMouseLeave = Nothing
        Me.btnBatteryManager.tbProgressValue = 50
        Me.btnBatteryManager.tbReadOnly = False
        Me.btnBatteryManager.tbReadOnlyText = False
        Me.btnBatteryManager.tbShadow = False
        Me.btnBatteryManager.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnBatteryManager.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnBatteryManager.tbShowDot = False
        Me.btnBatteryManager.tbShowMoreIconImg = CType(resources.GetObject("btnBatteryManager.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBatteryManager.tbShowNew = False
        Me.btnBatteryManager.tbShowProgress = False
        Me.btnBatteryManager.tbShowTip = True
        Me.btnBatteryManager.tbShowToolTipOnButton = False
        Me.btnBatteryManager.tbSplit = "0,0,0,0"
        Me.btnBatteryManager.tbText = ""
        Me.btnBatteryManager.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBatteryManager.tbTextColor = System.Drawing.Color.Black
        Me.btnBatteryManager.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnBatteryManager.tbTextColorDown = System.Drawing.Color.Black
        Me.btnBatteryManager.tbTextColorHover = System.Drawing.Color.Black
        Me.btnBatteryManager.tbTextMouseDownPlace = 0
        Me.btnBatteryManager.tbToolTip = ""
        Me.btnBatteryManager.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBatteryManager.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBatteryManager.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBatteryManager.VisibleEx = True
        '
        'PictureBox1
        '
        Me.PictureBox1.Location = New System.Drawing.Point(36, 1)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(1, 24)
        Me.PictureBox1.TabIndex = 37
        Me.PictureBox1.TabStop = False
        '
        'PictureBox2
        '
        Me.PictureBox2.Location = New System.Drawing.Point(72, 1)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(1, 24)
        Me.PictureBox2.TabIndex = 38
        Me.PictureBox2.TabStop = False
        '
        'PictureBox3
        '
        Me.PictureBox3.Location = New System.Drawing.Point(108, 1)
        Me.PictureBox3.Name = "PictureBox3"
        Me.PictureBox3.Size = New System.Drawing.Size(1, 24)
        Me.PictureBox3.TabIndex = 39
        Me.PictureBox3.TabStop = False
        '
        'btnScreenshotSetUp
        '
        Me.btnScreenshotSetUp.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnScreenshotSetUp.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshotSetUp.BindingForm = Nothing
        Me.btnScreenshotSetUp.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshotSetUp.Location = New System.Drawing.Point(153, 3)
        Me.btnScreenshotSetUp.Name = "btnScreenshotSetUp"
        Me.btnScreenshotSetUp.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshotSetUp.Selectable = True
        Me.btnScreenshotSetUp.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshotSetUp.TabIndex = 3
        Me.btnScreenshotSetUp.TabStop = False
        Me.btnScreenshotSetUp.tbAdriftIconWhenHover = False
        Me.btnScreenshotSetUp.tbAutoSize = False
        Me.btnScreenshotSetUp.tbAutoSizeEx = False
        Me.btnScreenshotSetUp.tbBackgroundImage = Nothing
        Me.btnScreenshotSetUp.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnScreenshotSetUp.tbBadgeNumber = 0
        Me.btnScreenshotSetUp.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshotSetUp.tbEndEllipsis = False
        Me.btnScreenshotSetUp.tbIconHoldPlace = True
        Me.btnScreenshotSetUp.tbIconImage = Nothing
        Me.btnScreenshotSetUp.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshotSetUp.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshotSetUp.tbIconMore = False
        Me.btnScreenshotSetUp.tbIconMouseDown = Nothing
        Me.btnScreenshotSetUp.tbIconMouseHover = Nothing
        Me.btnScreenshotSetUp.tbIconMouseLeave = Nothing
        Me.btnScreenshotSetUp.tbIconPlaceText = 2
        Me.btnScreenshotSetUp.tbIconReadOnly = Nothing
        Me.btnScreenshotSetUp.tbImageMouseDown = Nothing
        Me.btnScreenshotSetUp.tbImageMouseHover = Nothing
        Me.btnScreenshotSetUp.tbImageMouseLeave = Nothing
        Me.btnScreenshotSetUp.tbProgressValue = 50
        Me.btnScreenshotSetUp.tbReadOnly = False
        Me.btnScreenshotSetUp.tbReadOnlyText = False
        Me.btnScreenshotSetUp.tbShadow = False
        Me.btnScreenshotSetUp.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshotSetUp.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshotSetUp.tbShowDot = False
        Me.btnScreenshotSetUp.tbShowMoreIconImg = CType(resources.GetObject("btnScreenshotSetUp.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnScreenshotSetUp.tbShowNew = False
        Me.btnScreenshotSetUp.tbShowProgress = False
        Me.btnScreenshotSetUp.tbShowTip = True
        Me.btnScreenshotSetUp.tbShowToolTipOnButton = False
        Me.btnScreenshotSetUp.tbSplit = "0,0,0,0"
        Me.btnScreenshotSetUp.tbText = ""
        Me.btnScreenshotSetUp.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotSetUp.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextMouseDownPlace = 0
        Me.btnScreenshotSetUp.tbToolTip = ""
        Me.btnScreenshotSetUp.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshotSetUp.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshotSetUp.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotSetUp.VisibleEx = True
        '
        'btnScreenshotPlay
        '
        Me.btnScreenshotPlay.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnScreenshotPlay.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshotPlay.BindingForm = Nothing
        Me.btnScreenshotPlay.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshotPlay.Location = New System.Drawing.Point(45, 3)
        Me.btnScreenshotPlay.Name = "btnScreenshotPlay"
        Me.btnScreenshotPlay.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshotPlay.Selectable = True
        Me.btnScreenshotPlay.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshotPlay.TabIndex = 2
        Me.btnScreenshotPlay.TabStop = False
        Me.btnScreenshotPlay.tbAdriftIconWhenHover = False
        Me.btnScreenshotPlay.tbAutoSize = False
        Me.btnScreenshotPlay.tbAutoSizeEx = False
        Me.btnScreenshotPlay.tbBackgroundImage = Nothing
        Me.btnScreenshotPlay.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnScreenshotPlay.tbBadgeNumber = 0
        Me.btnScreenshotPlay.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshotPlay.tbEndEllipsis = False
        Me.btnScreenshotPlay.tbIconHoldPlace = True
        Me.btnScreenshotPlay.tbIconImage = Nothing
        Me.btnScreenshotPlay.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshotPlay.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshotPlay.tbIconMore = False
        Me.btnScreenshotPlay.tbIconMouseDown = Nothing
        Me.btnScreenshotPlay.tbIconMouseHover = Nothing
        Me.btnScreenshotPlay.tbIconMouseLeave = Nothing
        Me.btnScreenshotPlay.tbIconPlaceText = 2
        Me.btnScreenshotPlay.tbIconReadOnly = Nothing
        Me.btnScreenshotPlay.tbImageMouseDown = Nothing
        Me.btnScreenshotPlay.tbImageMouseHover = Nothing
        Me.btnScreenshotPlay.tbImageMouseLeave = Nothing
        Me.btnScreenshotPlay.tbProgressValue = 50
        Me.btnScreenshotPlay.tbReadOnly = False
        Me.btnScreenshotPlay.tbReadOnlyText = False
        Me.btnScreenshotPlay.tbShadow = False
        Me.btnScreenshotPlay.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshotPlay.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshotPlay.tbShowDot = False
        Me.btnScreenshotPlay.tbShowMoreIconImg = CType(resources.GetObject("btnScreenshotPlay.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnScreenshotPlay.tbShowNew = False
        Me.btnScreenshotPlay.tbShowProgress = False
        Me.btnScreenshotPlay.tbShowTip = True
        Me.btnScreenshotPlay.tbShowToolTipOnButton = False
        Me.btnScreenshotPlay.tbSplit = "0,0,0,0"
        Me.btnScreenshotPlay.tbText = ""
        Me.btnScreenshotPlay.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotPlay.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshotPlay.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshotPlay.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshotPlay.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshotPlay.tbTextMouseDownPlace = 0
        Me.btnScreenshotPlay.tbToolTip = ""
        Me.btnScreenshotPlay.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshotPlay.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshotPlay.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotPlay.VisibleEx = True
        '
        'btnShareTo
        '
        Me.btnShareTo.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnShareTo.BackColor = System.Drawing.Color.Transparent
        Me.btnShareTo.BindingForm = Nothing
        Me.btnShareTo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnShareTo.Location = New System.Drawing.Point(81, 3)
        Me.btnShareTo.Name = "btnShareTo"
        Me.btnShareTo.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnShareTo.Selectable = True
        Me.btnShareTo.Size = New System.Drawing.Size(18, 18)
        Me.btnShareTo.TabIndex = 1
        Me.btnShareTo.TabStop = False
        Me.btnShareTo.tbAdriftIconWhenHover = False
        Me.btnShareTo.tbAutoSize = False
        Me.btnShareTo.tbAutoSizeEx = False
        Me.btnShareTo.tbBackgroundImage = Nothing
        Me.btnShareTo.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnShareTo.tbBadgeNumber = 0
        Me.btnShareTo.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnShareTo.tbEndEllipsis = False
        Me.btnShareTo.tbIconHoldPlace = True
        Me.btnShareTo.tbIconImage = Nothing
        Me.btnShareTo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnShareTo.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnShareTo.tbIconMore = False
        Me.btnShareTo.tbIconMouseDown = Nothing
        Me.btnShareTo.tbIconMouseHover = Nothing
        Me.btnShareTo.tbIconMouseLeave = Nothing
        Me.btnShareTo.tbIconPlaceText = 2
        Me.btnShareTo.tbIconReadOnly = Nothing
        Me.btnShareTo.tbImageMouseDown = Nothing
        Me.btnShareTo.tbImageMouseHover = Nothing
        Me.btnShareTo.tbImageMouseLeave = Nothing
        Me.btnShareTo.tbProgressValue = 50
        Me.btnShareTo.tbReadOnly = False
        Me.btnShareTo.tbReadOnlyText = False
        Me.btnShareTo.tbShadow = False
        Me.btnShareTo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnShareTo.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnShareTo.tbShowDot = False
        Me.btnShareTo.tbShowMoreIconImg = CType(resources.GetObject("btnShareTo.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnShareTo.tbShowNew = False
        Me.btnShareTo.tbShowProgress = False
        Me.btnShareTo.tbShowTip = True
        Me.btnShareTo.tbShowToolTipOnButton = False
        Me.btnShareTo.tbSplit = "0,0,0,0"
        Me.btnShareTo.tbText = ""
        Me.btnShareTo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnShareTo.tbTextColor = System.Drawing.Color.Black
        Me.btnShareTo.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnShareTo.tbTextColorDown = System.Drawing.Color.Black
        Me.btnShareTo.tbTextColorHover = System.Drawing.Color.Black
        Me.btnShareTo.tbTextMouseDownPlace = 0
        Me.btnShareTo.tbToolTip = ""
        Me.btnShareTo.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnShareTo.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnShareTo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnShareTo.VisibleEx = True
        '
        'btnScreenshot
        '
        Me.btnScreenshot.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnScreenshot.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshot.BindingForm = Nothing
        Me.btnScreenshot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshot.Location = New System.Drawing.Point(9, 3)
        Me.btnScreenshot.Name = "btnScreenshot"
        Me.btnScreenshot.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshot.Selectable = True
        Me.btnScreenshot.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshot.TabIndex = 0
        Me.btnScreenshot.TabStop = False
        Me.btnScreenshot.tbAdriftIconWhenHover = False
        Me.btnScreenshot.tbAutoSize = False
        Me.btnScreenshot.tbAutoSizeEx = False
        Me.btnScreenshot.tbBackgroundImage = Nothing
        Me.btnScreenshot.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnScreenshot.tbBadgeNumber = 0
        Me.btnScreenshot.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshot.tbEndEllipsis = False
        Me.btnScreenshot.tbIconHoldPlace = True
        Me.btnScreenshot.tbIconImage = Nothing
        Me.btnScreenshot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshot.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshot.tbIconMore = False
        Me.btnScreenshot.tbIconMouseDown = Nothing
        Me.btnScreenshot.tbIconMouseHover = Nothing
        Me.btnScreenshot.tbIconMouseLeave = Nothing
        Me.btnScreenshot.tbIconPlaceText = 2
        Me.btnScreenshot.tbIconReadOnly = Nothing
        Me.btnScreenshot.tbImageMouseDown = Nothing
        Me.btnScreenshot.tbImageMouseHover = Nothing
        Me.btnScreenshot.tbImageMouseLeave = Nothing
        Me.btnScreenshot.tbProgressValue = 50
        Me.btnScreenshot.tbReadOnly = False
        Me.btnScreenshot.tbReadOnlyText = False
        Me.btnScreenshot.tbShadow = False
        Me.btnScreenshot.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshot.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshot.tbShowDot = False
        Me.btnScreenshot.tbShowMoreIconImg = CType(resources.GetObject("btnScreenshot.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnScreenshot.tbShowNew = False
        Me.btnScreenshot.tbShowProgress = False
        Me.btnScreenshot.tbShowTip = True
        Me.btnScreenshot.tbShowToolTipOnButton = False
        Me.btnScreenshot.tbSplit = "0,0,0,0"
        Me.btnScreenshot.tbText = ""
        Me.btnScreenshot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshot.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextMouseDownPlace = 0
        Me.btnScreenshot.tbToolTip = ""
        Me.btnScreenshot.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshot.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshot.VisibleEx = True
        '
        'btnRename
        '
        Me.btnRename.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRename.BackColor = System.Drawing.Color.Transparent
        Me.btnRename.BindingForm = Nothing
        Me.btnRename.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnRename.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRename.Location = New System.Drawing.Point(201, 42)
        Me.btnRename.Name = "btnRename"
        Me.btnRename.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRename.Selectable = True
        Me.btnRename.Size = New System.Drawing.Size(15, 15)
        Me.btnRename.TabIndex = 15
        Me.btnRename.TabStop = False
        Me.btnRename.tbAdriftIconWhenHover = False
        Me.btnRename.tbAutoSize = True
        Me.btnRename.tbAutoSizeEx = False
        Me.btnRename.tbBackgroundImage = Nothing
        Me.btnRename.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnRename.tbBadgeNumber = 0
        Me.btnRename.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRename.tbEndEllipsis = False
        Me.btnRename.tbIconHoldPlace = True
        Me.btnRename.tbIconImage = Nothing
        Me.btnRename.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRename.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRename.tbIconMore = False
        Me.btnRename.tbIconMouseDown = Nothing
        Me.btnRename.tbIconMouseHover = Nothing
        Me.btnRename.tbIconMouseLeave = Nothing
        Me.btnRename.tbIconPlaceText = 2
        Me.btnRename.tbIconReadOnly = Nothing
        Me.btnRename.tbImageMouseDown = Nothing
        Me.btnRename.tbImageMouseHover = Nothing
        Me.btnRename.tbImageMouseLeave = Nothing
        Me.btnRename.tbProgressValue = 50
        Me.btnRename.tbReadOnly = False
        Me.btnRename.tbReadOnlyText = False
        Me.btnRename.tbShadow = False
        Me.btnRename.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRename.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRename.tbShowDot = False
        Me.btnRename.tbShowMoreIconImg = CType(resources.GetObject("btnRename.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRename.tbShowNew = False
        Me.btnRename.tbShowProgress = False
        Me.btnRename.tbShowTip = True
        Me.btnRename.tbShowToolTipOnButton = False
        Me.btnRename.tbSplit = "0,0,0,0"
        Me.btnRename.tbText = ""
        Me.btnRename.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRename.tbTextColor = System.Drawing.Color.Black
        Me.btnRename.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnRename.tbTextColorDown = System.Drawing.Color.Black
        Me.btnRename.tbTextColorHover = System.Drawing.Color.Black
        Me.btnRename.tbTextMouseDownPlace = 0
        Me.btnRename.tbToolTip = ""
        Me.btnRename.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRename.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRename.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRename.VisibleEx = True
        '
        'tbCapacity
        '
        Me.tbCapacity.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.tbCapacity.BackColor = System.Drawing.Color.Transparent
        Me.tbCapacity.BackgroundSplit = "3,3,3,3"
        Me.tbCapacity.BarHeight = 7
        Me.tbCapacity.Colon = ":"
        Me.tbCapacity.Controls.Add(Me.btnClear)
        Me.tbCapacity.Controls.Add(Me.picLoading)
        Me.tbCapacity.Controls.Add(Me.btnRefresh)
        Me.tbCapacity.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.tbCapacity.ForeColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(88, Byte), Integer))
        Me.tbCapacity.Location = New System.Drawing.Point(420, 474)
        Me.tbCapacity.Name = "tbCapacity"
        Me.tbCapacity.ShowSizeInfoTop = True
        Me.tbCapacity.ShowSizeOnTip = True
        Me.tbCapacity.ShowTotalAndAvailable = True
        Me.tbCapacity.ShowType = iTong.Components.tbDeviceCapacity.MobileShow.None
        Me.tbCapacity.Size = New System.Drawing.Size(965, 88)
        Me.tbCapacity.TabIndex = 0
        Me.tbCapacity.Text = "TbDeviceCapacity1"
        '
        'btnClear
        '
        Me.btnClear.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClear.BackColor = System.Drawing.Color.Transparent
        Me.btnClear.BindingForm = Nothing
        Me.btnClear.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnClear.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClear.Location = New System.Drawing.Point(920, 14)
        Me.btnClear.Name = "btnClear"
        Me.btnClear.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnClear.Selectable = True
        Me.btnClear.Size = New System.Drawing.Size(16, 16)
        Me.btnClear.TabIndex = 37
        Me.btnClear.TabStop = False
        Me.btnClear.tbAdriftIconWhenHover = False
        Me.btnClear.tbAutoSize = False
        Me.btnClear.tbAutoSizeEx = False
        Me.btnClear.tbBackgroundImage = Nothing
        Me.btnClear.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnClear.tbBadgeNumber = 0
        Me.btnClear.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClear.tbEndEllipsis = False
        Me.btnClear.tbIconHoldPlace = True
        Me.btnClear.tbIconImage = Nothing
        Me.btnClear.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnClear.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClear.tbIconMore = False
        Me.btnClear.tbIconMouseDown = Nothing
        Me.btnClear.tbIconMouseHover = Nothing
        Me.btnClear.tbIconMouseLeave = Nothing
        Me.btnClear.tbIconPlaceText = 2
        Me.btnClear.tbIconReadOnly = Nothing
        Me.btnClear.tbImageMouseDown = Nothing
        Me.btnClear.tbImageMouseHover = Nothing
        Me.btnClear.tbImageMouseLeave = Nothing
        Me.btnClear.tbProgressValue = 50
        Me.btnClear.tbReadOnly = False
        Me.btnClear.tbReadOnlyText = False
        Me.btnClear.tbShadow = False
        Me.btnClear.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnClear.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnClear.tbShowDot = False
        Me.btnClear.tbShowMoreIconImg = CType(resources.GetObject("btnClear.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnClear.tbShowNew = False
        Me.btnClear.tbShowProgress = False
        Me.btnClear.tbShowTip = True
        Me.btnClear.tbShowToolTipOnButton = False
        Me.btnClear.tbSplit = "0,0,0,0"
        Me.btnClear.tbText = ""
        Me.btnClear.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClear.tbTextColor = System.Drawing.Color.Black
        Me.btnClear.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnClear.tbTextColorDown = System.Drawing.Color.Black
        Me.btnClear.tbTextColorHover = System.Drawing.Color.Black
        Me.btnClear.tbTextMouseDownPlace = 0
        Me.btnClear.tbToolTip = ""
        Me.btnClear.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClear.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClear.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClear.VisibleEx = True
        '
        'picLoading
        '
        Me.picLoading.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.picLoading.Location = New System.Drawing.Point(890, 14)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(16, 16)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picLoading.TabIndex = 1
        Me.picLoading.TabStop = False
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(890, 14)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(16, 16)
        Me.btnRefresh.TabIndex = 0
        Me.btnRefresh.TabStop = False
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = True
        Me.btnRefresh.tbAutoSizeEx = False
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Nothing
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = ""
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.Black
        Me.btnRefresh.tbTextMouseDownPlace = 0
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'btnDetail
        '
        Me.btnDetail.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnDetail.BackColor = System.Drawing.Color.Transparent
        Me.btnDetail.BindingForm = Nothing
        Me.btnDetail.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDetail.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDetail.Location = New System.Drawing.Point(222, 42)
        Me.btnDetail.Name = "btnDetail"
        Me.btnDetail.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDetail.Selectable = True
        Me.btnDetail.Size = New System.Drawing.Size(15, 15)
        Me.btnDetail.TabIndex = 15
        Me.btnDetail.TabStop = False
        Me.btnDetail.tbAdriftIconWhenHover = False
        Me.btnDetail.tbAutoSize = True
        Me.btnDetail.tbAutoSizeEx = False
        Me.btnDetail.tbBackgroundImage = Nothing
        Me.btnDetail.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnDetail.tbBadgeNumber = 0
        Me.btnDetail.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDetail.tbEndEllipsis = False
        Me.btnDetail.tbIconHoldPlace = True
        Me.btnDetail.tbIconImage = Nothing
        Me.btnDetail.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDetail.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDetail.tbIconMore = False
        Me.btnDetail.tbIconMouseDown = Nothing
        Me.btnDetail.tbIconMouseHover = Nothing
        Me.btnDetail.tbIconMouseLeave = Nothing
        Me.btnDetail.tbIconPlaceText = 2
        Me.btnDetail.tbIconReadOnly = Nothing
        Me.btnDetail.tbImageMouseDown = Nothing
        Me.btnDetail.tbImageMouseHover = Nothing
        Me.btnDetail.tbImageMouseLeave = Nothing
        Me.btnDetail.tbProgressValue = 50
        Me.btnDetail.tbReadOnly = False
        Me.btnDetail.tbReadOnlyText = False
        Me.btnDetail.tbShadow = False
        Me.btnDetail.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDetail.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDetail.tbShowDot = False
        Me.btnDetail.tbShowMoreIconImg = CType(resources.GetObject("btnDetail.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDetail.tbShowNew = False
        Me.btnDetail.tbShowProgress = False
        Me.btnDetail.tbShowTip = True
        Me.btnDetail.tbShowToolTipOnButton = False
        Me.btnDetail.tbSplit = "0,0,0,0"
        Me.btnDetail.tbText = ""
        Me.btnDetail.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDetail.tbTextColor = System.Drawing.Color.Black
        Me.btnDetail.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnDetail.tbTextColorDown = System.Drawing.Color.Black
        Me.btnDetail.tbTextColorHover = System.Drawing.Color.Black
        Me.btnDetail.tbTextMouseDownPlace = 0
        Me.btnDetail.tbToolTip = ""
        Me.btnDetail.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDetail.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDetail.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDetail.VisibleEx = True
        '
        'lblNameValue
        '
        Me.lblNameValue.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblNameValue.AutoSize = True
        Me.lblNameValue.Font = New System.Drawing.Font("宋体", 15.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblNameValue.Location = New System.Drawing.Point(1, 37)
        Me.lblNameValue.Name = "lblNameValue"
        Me.lblNameValue.Size = New System.Drawing.Size(194, 30)
        Me.lblNameValue.TabIndex = 0
        Me.lblNameValue.tbAdriftWhenHover = False
        Me.lblNameValue.tbAutoEllipsis = False
        Me.lblNameValue.tbAutoSize = True
        Me.lblNameValue.tbHideImage = False
        Me.lblNameValue.tbIconImage = Nothing
        Me.lblNameValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNameValue.tbIconPlaceText = 5
        Me.lblNameValue.tbShadow = False
        Me.lblNameValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNameValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNameValue.tbShowScrolling = False
        Me.lblNameValue.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cmsSetting
        '
        Me.cmsSetting.AccessibleDescription = "153x70"
        Me.cmsSetting.DropShadowEnabled = False
        Me.cmsSetting.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsSetting.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiScreenshot, Me.tsmiScreenshotShell})
        Me.cmsSetting.Name = "munSearch"
        Me.cmsSetting.Size = New System.Drawing.Size(143, 48)
        Me.cmsSetting.tbBackColor = System.Drawing.Color.White
        Me.cmsSetting.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsSetting.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsSetting.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiScreenshot
        '
        Me.tsmiScreenshot.Name = "tsmiScreenshot"
        Me.tsmiScreenshot.Size = New System.Drawing.Size(142, 22)
        Me.tsmiScreenshot.Text = "截屏(无外壳)"
        '
        'tsmiScreenshotShell
        '
        Me.tsmiScreenshotShell.Name = "tsmiScreenshotShell"
        Me.tsmiScreenshotShell.Size = New System.Drawing.Size(142, 22)
        Me.tsmiScreenshotShell.Text = "截屏(带外壳)"
        '
        'btnJump
        '
        Me.btnJump.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnJump.BackColor = System.Drawing.Color.Transparent
        Me.btnJump.BindingForm = Nothing
        Me.btnJump.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnJump.Location = New System.Drawing.Point(138, 101)
        Me.btnJump.Name = "btnJump"
        Me.btnJump.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnJump.Selectable = True
        Me.btnJump.Size = New System.Drawing.Size(12, 12)
        Me.btnJump.TabIndex = 32
        Me.btnJump.tbAdriftIconWhenHover = False
        Me.btnJump.tbAutoSize = True
        Me.btnJump.tbAutoSizeEx = False
        Me.btnJump.tbBackgroundImage = Nothing
        Me.btnJump.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnJump.tbBadgeNumber = 0
        Me.btnJump.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnJump.tbEndEllipsis = False
        Me.btnJump.tbIconHoldPlace = True
        Me.btnJump.tbIconImage = Nothing
        Me.btnJump.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJump.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnJump.tbIconMore = False
        Me.btnJump.tbIconMouseDown = Nothing
        Me.btnJump.tbIconMouseHover = Nothing
        Me.btnJump.tbIconMouseLeave = Nothing
        Me.btnJump.tbIconPlaceText = 2
        Me.btnJump.tbIconReadOnly = Nothing
        Me.btnJump.tbImageMouseDown = Nothing
        Me.btnJump.tbImageMouseHover = Nothing
        Me.btnJump.tbImageMouseLeave = Nothing
        Me.btnJump.tbProgressValue = 50
        Me.btnJump.tbReadOnly = False
        Me.btnJump.tbReadOnlyText = False
        Me.btnJump.tbShadow = False
        Me.btnJump.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnJump.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnJump.tbShowDot = False
        Me.btnJump.tbShowMoreIconImg = CType(resources.GetObject("btnJump.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnJump.tbShowNew = False
        Me.btnJump.tbShowProgress = False
        Me.btnJump.tbShowTip = True
        Me.btnJump.tbShowToolTipOnButton = False
        Me.btnJump.tbSplit = "3,3,3,3"
        Me.btnJump.tbText = ""
        Me.btnJump.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJump.tbTextColor = System.Drawing.Color.White
        Me.btnJump.tbTextColorDisable = System.Drawing.Color.White
        Me.btnJump.tbTextColorDown = System.Drawing.Color.White
        Me.btnJump.tbTextColorHover = System.Drawing.Color.White
        Me.btnJump.tbTextMouseDownPlace = 0
        Me.btnJump.tbToolTip = ""
        Me.btnJump.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnJump.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnJump.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJump.VisibleEx = True
        '
        'btnClearTip
        '
        Me.btnClearTip.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnClearTip.BackColor = System.Drawing.Color.Transparent
        Me.btnClearTip.BindingForm = Nothing
        Me.btnClearTip.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClearTip.Location = New System.Drawing.Point(1034, 426)
        Me.btnClearTip.Name = "btnClearTip"
        Me.btnClearTip.Padding = New System.Windows.Forms.Padding(8, 10, 5, 14)
        Me.btnClearTip.Selectable = True
        Me.btnClearTip.Size = New System.Drawing.Size(150, 36)
        Me.btnClearTip.TabIndex = 37
        Me.btnClearTip.tbAdriftIconWhenHover = False
        Me.btnClearTip.tbAutoSize = False
        Me.btnClearTip.tbAutoSizeEx = True
        Me.btnClearTip.tbBackgroundImage = Nothing
        Me.btnClearTip.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnClearTip.tbBadgeNumber = 0
        Me.btnClearTip.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClearTip.tbEndEllipsis = False
        Me.btnClearTip.tbIconHoldPlace = True
        Me.btnClearTip.tbIconImage = Nothing
        Me.btnClearTip.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClearTip.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClearTip.tbIconMore = False
        Me.btnClearTip.tbIconMouseDown = Nothing
        Me.btnClearTip.tbIconMouseHover = Nothing
        Me.btnClearTip.tbIconMouseLeave = Nothing
        Me.btnClearTip.tbIconPlaceText = 2
        Me.btnClearTip.tbIconReadOnly = Nothing
        Me.btnClearTip.tbImageMouseDown = Nothing
        Me.btnClearTip.tbImageMouseHover = Nothing
        Me.btnClearTip.tbImageMouseLeave = Nothing
        Me.btnClearTip.tbProgressValue = 50
        Me.btnClearTip.tbReadOnly = False
        Me.btnClearTip.tbReadOnlyText = False
        Me.btnClearTip.tbShadow = False
        Me.btnClearTip.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnClearTip.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnClearTip.tbShowDot = False
        Me.btnClearTip.tbShowMoreIconImg = CType(resources.GetObject("btnClearTip.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnClearTip.tbShowNew = False
        Me.btnClearTip.tbShowProgress = False
        Me.btnClearTip.tbShowTip = True
        Me.btnClearTip.tbShowToolTipOnButton = False
        Me.btnClearTip.tbSplit = "33,14,61,13"
        Me.btnClearTip.tbText = "20天没有清理过垃圾了！"
        Me.btnClearTip.tbTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnClearTip.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
        Me.btnClearTip.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
        Me.btnClearTip.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
        Me.btnClearTip.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
        Me.btnClearTip.tbTextMouseDownPlace = 0
        Me.btnClearTip.tbToolTip = ""
        Me.btnClearTip.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClearTip.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClearTip.tbToolTipTextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.btnClearTip.VisibleEx = True
        '
        'cmsWeibo
        '
        Me.cmsWeibo.AccessibleDescription = "153x114"
        Me.cmsWeibo.DropShadowEnabled = False
        Me.cmsWeibo.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsWeibo.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiSina, Me.tsmiTecent, Me.tsmiFacebook, Me.tsmiTwitter})
        Me.cmsWeibo.Name = "munSearch"
        Me.cmsWeibo.Size = New System.Drawing.Size(119, 92)
        Me.cmsWeibo.tbBackColor = System.Drawing.Color.White
        Me.cmsWeibo.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsWeibo.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsWeibo.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiSina
        '
        Me.tsmiSina.Name = "tsmiSina"
        Me.tsmiSina.Size = New System.Drawing.Size(118, 22)
        Me.tsmiSina.Text = "新浪微博"
        '
        'tsmiTecent
        '
        Me.tsmiTecent.Name = "tsmiTecent"
        Me.tsmiTecent.Size = New System.Drawing.Size(118, 22)
        Me.tsmiTecent.Text = "腾讯微博"
        '
        'tsmiFacebook
        '
        Me.tsmiFacebook.Name = "tsmiFacebook"
        Me.tsmiFacebook.Size = New System.Drawing.Size(118, 22)
        Me.tsmiFacebook.Text = "Facebook"
        '
        'tsmiTwitter
        '
        Me.tsmiTwitter.Name = "tsmiTwitter"
        Me.tsmiTwitter.Size = New System.Drawing.Size(118, 22)
        Me.tsmiTwitter.Text = "Twitter"
        '
        'lblCharging
        '
        Me.lblCharging.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblCharging.AutoSize = True
        Me.lblCharging.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblCharging.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblCharging.Location = New System.Drawing.Point(35, 93)
        Me.lblCharging.Name = "lblCharging"
        Me.lblCharging.Size = New System.Drawing.Size(41, 12)
        Me.lblCharging.TabIndex = 40
        Me.lblCharging.Text = "      "
        Me.lblCharging.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picCharging
        '
        Me.picCharging.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.picCharging.Location = New System.Drawing.Point(6, 88)
        Me.picCharging.Margin = New System.Windows.Forms.Padding(0)
        Me.picCharging.Name = "picCharging"
        Me.picCharging.Size = New System.Drawing.Size(26, 26)
        Me.picCharging.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picCharging.TabIndex = 39
        Me.picCharging.TabStop = False
        '
        'tmrRefresh
        '
        Me.tmrRefresh.Interval = 90000
        '
        'pnlWeb
        '
        Me.pnlWeb.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlWeb.BackColor = System.Drawing.Color.Transparent
        Me.pnlWeb.Location = New System.Drawing.Point(700, 163)
        Me.pnlWeb.Name = "pnlWeb"
        Me.pnlWeb.Size = New System.Drawing.Size(402, 105)
        Me.pnlWeb.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlWeb.TabIndex = 43
        Me.pnlWeb.tbBackgroundImage = Nothing
        Me.pnlWeb.tbShowWatermark = False
        Me.pnlWeb.tbSplit = "0,0,0,0"
        Me.pnlWeb.tbWatermark = Nothing
        Me.pnlWeb.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlWeb.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlWeb.Visible = False
        '
        'pnlDeviceName
        '
        Me.pnlDeviceName.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlDeviceName.Controls.Add(Me.btnCanceliOSUpgrade)
        Me.pnlDeviceName.Controls.Add(Me.btnGoJailbreakWeb)
        Me.pnlDeviceName.Controls.Add(Me.pnlChangeDeviceName)
        Me.pnlDeviceName.Controls.Add(Me.lblNameValue)
        Me.pnlDeviceName.Controls.Add(Me.btnDetail)
        Me.pnlDeviceName.Controls.Add(Me.btnRename)
        Me.pnlDeviceName.Controls.Add(Me.btnDisconnect)
        Me.pnlDeviceName.Controls.Add(Me.lblVersion)
        Me.pnlDeviceName.Controls.Add(Me.btnJump)
        Me.pnlDeviceName.Controls.Add(Me.picCharging)
        Me.pnlDeviceName.Controls.Add(Me.lblCharging)
        Me.pnlDeviceName.Location = New System.Drawing.Point(700, 42)
        Me.pnlDeviceName.Name = "pnlDeviceName"
        Me.pnlDeviceName.Size = New System.Drawing.Size(806, 114)
        Me.pnlDeviceName.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDeviceName.TabIndex = 44
        Me.pnlDeviceName.tbBackgroundImage = Nothing
        Me.pnlDeviceName.tbShowWatermark = False
        Me.pnlDeviceName.tbSplit = "0,0,0,0"
        Me.pnlDeviceName.tbWatermark = Nothing
        Me.pnlDeviceName.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDeviceName.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnCanceliOSUpgrade
        '
        Me.btnCanceliOSUpgrade.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnCanceliOSUpgrade.BackColor = System.Drawing.Color.Transparent
        Me.btnCanceliOSUpgrade.BindingForm = Nothing
        Me.btnCanceliOSUpgrade.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnCanceliOSUpgrade.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCanceliOSUpgrade.Location = New System.Drawing.Point(223, 65)
        Me.btnCanceliOSUpgrade.Name = "btnCanceliOSUpgrade"
        Me.btnCanceliOSUpgrade.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCanceliOSUpgrade.Selectable = True
        Me.btnCanceliOSUpgrade.Size = New System.Drawing.Size(77, 21)
        Me.btnCanceliOSUpgrade.TabIndex = 51
        Me.btnCanceliOSUpgrade.tbAdriftIconWhenHover = False
        Me.btnCanceliOSUpgrade.tbAutoSize = False
        Me.btnCanceliOSUpgrade.tbAutoSizeEx = True
        Me.btnCanceliOSUpgrade.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnCanceliOSUpgrade.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCanceliOSUpgrade.tbBadgeNumber = 0
        Me.btnCanceliOSUpgrade.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCanceliOSUpgrade.tbEndEllipsis = False
        Me.btnCanceliOSUpgrade.tbIconHoldPlace = True
        Me.btnCanceliOSUpgrade.tbIconImage = Nothing
        Me.btnCanceliOSUpgrade.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCanceliOSUpgrade.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCanceliOSUpgrade.tbIconMore = False
        Me.btnCanceliOSUpgrade.tbIconMouseDown = Nothing
        Me.btnCanceliOSUpgrade.tbIconMouseHover = Nothing
        Me.btnCanceliOSUpgrade.tbIconMouseLeave = Nothing
        Me.btnCanceliOSUpgrade.tbIconPlaceText = 2
        Me.btnCanceliOSUpgrade.tbIconReadOnly = Nothing
        Me.btnCanceliOSUpgrade.tbImageMouseDown = Nothing
        Me.btnCanceliOSUpgrade.tbImageMouseHover = Nothing
        Me.btnCanceliOSUpgrade.tbImageMouseLeave = Nothing
        Me.btnCanceliOSUpgrade.tbProgressValue = 50
        Me.btnCanceliOSUpgrade.tbReadOnly = False
        Me.btnCanceliOSUpgrade.tbReadOnlyText = False
        Me.btnCanceliOSUpgrade.tbShadow = False
        Me.btnCanceliOSUpgrade.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCanceliOSUpgrade.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCanceliOSUpgrade.tbShowDot = False
        Me.btnCanceliOSUpgrade.tbShowMoreIconImg = CType(resources.GetObject("btnCanceliOSUpgrade.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCanceliOSUpgrade.tbShowNew = False
        Me.btnCanceliOSUpgrade.tbShowProgress = False
        Me.btnCanceliOSUpgrade.tbShowTip = True
        Me.btnCanceliOSUpgrade.tbShowToolTipOnButton = False
        Me.btnCanceliOSUpgrade.tbSplit = "13,11,13,11"
        Me.btnCanceliOSUpgrade.tbText = "关闭iOS更新"
        Me.btnCanceliOSUpgrade.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCanceliOSUpgrade.tbTextColor = System.Drawing.Color.White
        Me.btnCanceliOSUpgrade.tbTextColorDisable = System.Drawing.Color.White
        Me.btnCanceliOSUpgrade.tbTextColorDown = System.Drawing.Color.White
        Me.btnCanceliOSUpgrade.tbTextColorHover = System.Drawing.Color.White
        Me.btnCanceliOSUpgrade.tbTextMouseDownPlace = 0
        Me.btnCanceliOSUpgrade.tbToolTip = ""
        Me.btnCanceliOSUpgrade.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCanceliOSUpgrade.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCanceliOSUpgrade.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCanceliOSUpgrade.Visible = False
        Me.btnCanceliOSUpgrade.VisibleEx = True
        '
        'btnGoJailbreakWeb
        '
        Me.btnGoJailbreakWeb.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnGoJailbreakWeb.BackColor = System.Drawing.Color.Transparent
        Me.btnGoJailbreakWeb.BindingForm = Nothing
        Me.btnGoJailbreakWeb.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnGoJailbreakWeb.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnGoJailbreakWeb.Location = New System.Drawing.Point(205, 70)
        Me.btnGoJailbreakWeb.Margin = New System.Windows.Forms.Padding(0)
        Me.btnGoJailbreakWeb.Name = "btnGoJailbreakWeb"
        Me.btnGoJailbreakWeb.Padding = New System.Windows.Forms.Padding(0, 0, 5, 2)
        Me.btnGoJailbreakWeb.Selectable = True
        Me.btnGoJailbreakWeb.Size = New System.Drawing.Size(12, 12)
        Me.btnGoJailbreakWeb.TabIndex = 50
        Me.btnGoJailbreakWeb.tbAdriftIconWhenHover = False
        Me.btnGoJailbreakWeb.tbAutoSize = False
        Me.btnGoJailbreakWeb.tbAutoSizeEx = True
        Me.btnGoJailbreakWeb.tbBackgroundImage = Nothing
        Me.btnGoJailbreakWeb.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnGoJailbreakWeb.tbBadgeNumber = 0
        Me.btnGoJailbreakWeb.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGoJailbreakWeb.tbEndEllipsis = False
        Me.btnGoJailbreakWeb.tbIconHoldPlace = True
        Me.btnGoJailbreakWeb.tbIconImage = Global.iTong.My.Resources.Resources.btn_3_goto
        Me.btnGoJailbreakWeb.tbIconImageAlign = System.Drawing.ContentAlignment.TopLeft
        Me.btnGoJailbreakWeb.tbIconImageState = iTong.Components.ImageState.ThreeState
        Me.btnGoJailbreakWeb.tbIconMore = False
        Me.btnGoJailbreakWeb.tbIconMouseDown = Nothing
        Me.btnGoJailbreakWeb.tbIconMouseHover = Nothing
        Me.btnGoJailbreakWeb.tbIconMouseLeave = Nothing
        Me.btnGoJailbreakWeb.tbIconPlaceText = 0
        Me.btnGoJailbreakWeb.tbIconReadOnly = Nothing
        Me.btnGoJailbreakWeb.tbImageMouseDown = Nothing
        Me.btnGoJailbreakWeb.tbImageMouseHover = Nothing
        Me.btnGoJailbreakWeb.tbImageMouseLeave = Nothing
        Me.btnGoJailbreakWeb.tbProgressValue = 50
        Me.btnGoJailbreakWeb.tbReadOnly = False
        Me.btnGoJailbreakWeb.tbReadOnlyText = False
        Me.btnGoJailbreakWeb.tbShadow = False
        Me.btnGoJailbreakWeb.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGoJailbreakWeb.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGoJailbreakWeb.tbShowDot = False
        Me.btnGoJailbreakWeb.tbShowMoreIconImg = CType(resources.GetObject("btnGoJailbreakWeb.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGoJailbreakWeb.tbShowNew = False
        Me.btnGoJailbreakWeb.tbShowProgress = False
        Me.btnGoJailbreakWeb.tbShowTip = True
        Me.btnGoJailbreakWeb.tbShowToolTipOnButton = False
        Me.btnGoJailbreakWeb.tbSplit = "13,11,13,11"
        Me.btnGoJailbreakWeb.tbText = ""
        Me.btnGoJailbreakWeb.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGoJailbreakWeb.tbTextColor = System.Drawing.Color.CornflowerBlue
        Me.btnGoJailbreakWeb.tbTextColorDisable = System.Drawing.Color.CornflowerBlue
        Me.btnGoJailbreakWeb.tbTextColorDown = System.Drawing.Color.CornflowerBlue
        Me.btnGoJailbreakWeb.tbTextColorHover = System.Drawing.Color.CornflowerBlue
        Me.btnGoJailbreakWeb.tbTextMouseDownPlace = 0
        Me.btnGoJailbreakWeb.tbToolTip = ""
        Me.btnGoJailbreakWeb.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGoJailbreakWeb.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGoJailbreakWeb.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGoJailbreakWeb.Visible = False
        Me.btnGoJailbreakWeb.VisibleEx = True
        '
        'tmrTui
        '
        Me.tmrTui.Interval = 5000
        '
        'btnYueYu
        '
        Me.btnYueYu.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnYueYu.BackColor = System.Drawing.Color.Transparent
        Me.btnYueYu.BindingForm = Nothing
        Me.btnYueYu.Font = New System.Drawing.Font("宋体", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnYueYu.Location = New System.Drawing.Point(478, 436)
        Me.btnYueYu.Name = "btnYueYu"
        Me.btnYueYu.Padding = New System.Windows.Forms.Padding(20, 2, 20, 2)
        Me.btnYueYu.Selectable = True
        Me.btnYueYu.Size = New System.Drawing.Size(111, 36)
        Me.btnYueYu.TabIndex = 45
        Me.btnYueYu.tbAdriftIconWhenHover = False
        Me.btnYueYu.tbAutoSize = False
        Me.btnYueYu.tbAutoSizeEx = True
        Me.btnYueYu.tbBackgroundImage = Nothing
        Me.btnYueYu.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnYueYu.tbBadgeNumber = 0
        Me.btnYueYu.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnYueYu.tbEndEllipsis = False
        Me.btnYueYu.tbIconHoldPlace = True
        Me.btnYueYu.tbIconImage = Nothing
        Me.btnYueYu.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnYueYu.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnYueYu.tbIconMore = False
        Me.btnYueYu.tbIconMouseDown = Nothing
        Me.btnYueYu.tbIconMouseHover = Nothing
        Me.btnYueYu.tbIconMouseLeave = Nothing
        Me.btnYueYu.tbIconPlaceText = 0
        Me.btnYueYu.tbIconReadOnly = Nothing
        Me.btnYueYu.tbImageMouseDown = Nothing
        Me.btnYueYu.tbImageMouseHover = Nothing
        Me.btnYueYu.tbImageMouseLeave = Nothing
        Me.btnYueYu.tbProgressValue = 50
        Me.btnYueYu.tbReadOnly = False
        Me.btnYueYu.tbReadOnlyText = False
        Me.btnYueYu.tbShadow = False
        Me.btnYueYu.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnYueYu.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnYueYu.tbShowDot = False
        Me.btnYueYu.tbShowMoreIconImg = CType(resources.GetObject("btnYueYu.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnYueYu.tbShowNew = False
        Me.btnYueYu.tbShowProgress = False
        Me.btnYueYu.tbShowTip = True
        Me.btnYueYu.tbShowToolTipOnButton = False
        Me.btnYueYu.tbSplit = "13,11,13,11"
        Me.btnYueYu.tbText = "开始越狱"
        Me.btnYueYu.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnYueYu.tbTextColor = System.Drawing.Color.White
        Me.btnYueYu.tbTextColorDisable = System.Drawing.Color.White
        Me.btnYueYu.tbTextColorDown = System.Drawing.Color.White
        Me.btnYueYu.tbTextColorHover = System.Drawing.Color.White
        Me.btnYueYu.tbTextMouseDownPlace = 0
        Me.btnYueYu.tbToolTip = ""
        Me.btnYueYu.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnYueYu.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnYueYu.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnYueYu.VisibleEx = True
        '
        'bgwStartJailbreak
        '
        Me.bgwStartJailbreak.WorkerSupportsCancellation = True
        '
        'pnlToolV3
        '
        Me.pnlToolV3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlToolV3.Controls.Add(Me.btnQQ)
        Me.pnlToolV3.Controls.Add(Me.btnH5Game)
        Me.pnlToolV3.Controls.Add(Me.btnRepairPay)
        Me.pnlToolV3.Controls.Add(Me.btnBinding)
        Me.pnlToolV3.Controls.Add(Me.btnWeixin)
        Me.pnlToolV3.Controls.Add(Me.btnBackupRestore)
        Me.pnlToolV3.Controls.Add(Me.btnTuiToolV3)
        Me.pnlToolV3.Controls.Add(Me.btnRepairToolV3)
        Me.pnlToolV3.Location = New System.Drawing.Point(701, 273)
        Me.pnlToolV3.Name = "pnlToolV3"
        Me.pnlToolV3.Size = New System.Drawing.Size(729, 109)
        Me.pnlToolV3.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlToolV3.TabIndex = 46
        Me.pnlToolV3.tbBackgroundImage = Nothing
        Me.pnlToolV3.tbShowWatermark = False
        Me.pnlToolV3.tbSplit = "0,0,0,0"
        Me.pnlToolV3.tbWatermark = Nothing
        Me.pnlToolV3.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlToolV3.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlToolV3.Visible = False
        '
        'btnQQ
        '
        Me.btnQQ.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnQQ.BackColor = System.Drawing.Color.Transparent
        Me.btnQQ.BindingForm = Nothing
        Me.btnQQ.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnQQ.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnQQ.Location = New System.Drawing.Point(202, -7)
        Me.btnQQ.Name = "btnQQ"
        Me.btnQQ.Padding = New System.Windows.Forms.Padding(0, 10, 5, 2)
        Me.btnQQ.Selectable = True
        Me.btnQQ.Size = New System.Drawing.Size(107, 100)
        Me.btnQQ.TabIndex = 37
        Me.btnQQ.TabStop = False
        Me.btnQQ.tbAdriftIconWhenHover = False
        Me.btnQQ.tbAutoSize = False
        Me.btnQQ.tbAutoSizeEx = False
        Me.btnQQ.tbBackgroundImage = Nothing
        Me.btnQQ.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnQQ.tbBadgeNumber = 0
        Me.btnQQ.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnQQ.tbEndEllipsis = False
        Me.btnQQ.tbIconHoldPlace = True
        Me.btnQQ.tbIconImage = Nothing
        Me.btnQQ.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnQQ.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnQQ.tbIconMore = False
        Me.btnQQ.tbIconMouseDown = Nothing
        Me.btnQQ.tbIconMouseHover = Nothing
        Me.btnQQ.tbIconMouseLeave = Nothing
        Me.btnQQ.tbIconPlaceText = 5
        Me.btnQQ.tbIconReadOnly = Nothing
        Me.btnQQ.tbImageMouseDown = Nothing
        Me.btnQQ.tbImageMouseHover = Nothing
        Me.btnQQ.tbImageMouseLeave = Nothing
        Me.btnQQ.tbProgressValue = 50
        Me.btnQQ.tbReadOnly = False
        Me.btnQQ.tbReadOnlyText = False
        Me.btnQQ.tbShadow = False
        Me.btnQQ.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnQQ.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnQQ.tbShowDot = False
        Me.btnQQ.tbShowMoreIconImg = CType(resources.GetObject("btnQQ.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnQQ.tbShowNew = False
        Me.btnQQ.tbShowProgress = False
        Me.btnQQ.tbShowTip = True
        Me.btnQQ.tbShowToolTipOnButton = False
        Me.btnQQ.tbSplit = "0,0,0,0"
        Me.btnQQ.tbText = "QQ"
        Me.btnQQ.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnQQ.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.btnQQ.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnQQ.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnQQ.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnQQ.tbTextMouseDownPlace = 2
        Me.btnQQ.tbToolTip = ""
        Me.btnQQ.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(195, Byte), Integer))
        Me.btnQQ.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnQQ.tbToolTipTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnQQ.VisibleEx = True
        '
        'btnH5Game
        '
        Me.btnH5Game.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnH5Game.BackColor = System.Drawing.Color.Transparent
        Me.btnH5Game.BindingForm = Nothing
        Me.btnH5Game.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnH5Game.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnH5Game.Location = New System.Drawing.Point(625, -7)
        Me.btnH5Game.Name = "btnH5Game"
        Me.btnH5Game.Padding = New System.Windows.Forms.Padding(0, 10, 5, 2)
        Me.btnH5Game.Selectable = True
        Me.btnH5Game.Size = New System.Drawing.Size(101, 100)
        Me.btnH5Game.TabIndex = 36
        Me.btnH5Game.TabStop = False
        Me.btnH5Game.tbAdriftIconWhenHover = False
        Me.btnH5Game.tbAutoSize = False
        Me.btnH5Game.tbAutoSizeEx = False
        Me.btnH5Game.tbBackgroundImage = Nothing
        Me.btnH5Game.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnH5Game.tbBadgeNumber = 0
        Me.btnH5Game.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnH5Game.tbEndEllipsis = False
        Me.btnH5Game.tbIconHoldPlace = True
        Me.btnH5Game.tbIconImage = Nothing
        Me.btnH5Game.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnH5Game.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnH5Game.tbIconMore = False
        Me.btnH5Game.tbIconMouseDown = Nothing
        Me.btnH5Game.tbIconMouseHover = Nothing
        Me.btnH5Game.tbIconMouseLeave = Nothing
        Me.btnH5Game.tbIconPlaceText = 5
        Me.btnH5Game.tbIconReadOnly = Nothing
        Me.btnH5Game.tbImageMouseDown = Nothing
        Me.btnH5Game.tbImageMouseHover = Nothing
        Me.btnH5Game.tbImageMouseLeave = Nothing
        Me.btnH5Game.tbProgressValue = 50
        Me.btnH5Game.tbReadOnly = False
        Me.btnH5Game.tbReadOnlyText = False
        Me.btnH5Game.tbShadow = False
        Me.btnH5Game.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnH5Game.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnH5Game.tbShowDot = False
        Me.btnH5Game.tbShowMoreIconImg = CType(resources.GetObject("btnH5Game.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnH5Game.tbShowNew = False
        Me.btnH5Game.tbShowProgress = False
        Me.btnH5Game.tbShowTip = True
        Me.btnH5Game.tbShowToolTipOnButton = False
        Me.btnH5Game.tbSplit = "0,0,0,0"
        Me.btnH5Game.tbText = "小游戏"
        Me.btnH5Game.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnH5Game.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.btnH5Game.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnH5Game.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnH5Game.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnH5Game.tbTextMouseDownPlace = 2
        Me.btnH5Game.tbToolTip = ""
        Me.btnH5Game.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(128, Byte), Integer))
        Me.btnH5Game.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnH5Game.tbToolTipTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnH5Game.VisibleEx = True
        '
        'btnRepairPay
        '
        Me.btnRepairPay.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRepairPay.BackColor = System.Drawing.Color.Transparent
        Me.btnRepairPay.BindingForm = Nothing
        Me.btnRepairPay.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnRepairPay.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRepairPay.Location = New System.Drawing.Point(527, -7)
        Me.btnRepairPay.Name = "btnRepairPay"
        Me.btnRepairPay.Padding = New System.Windows.Forms.Padding(0, 10, 5, 2)
        Me.btnRepairPay.Selectable = True
        Me.btnRepairPay.Size = New System.Drawing.Size(101, 100)
        Me.btnRepairPay.TabIndex = 36
        Me.btnRepairPay.TabStop = False
        Me.btnRepairPay.tbAdriftIconWhenHover = False
        Me.btnRepairPay.tbAutoSize = False
        Me.btnRepairPay.tbAutoSizeEx = False
        Me.btnRepairPay.tbBackgroundImage = Nothing
        Me.btnRepairPay.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnRepairPay.tbBadgeNumber = 0
        Me.btnRepairPay.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRepairPay.tbEndEllipsis = False
        Me.btnRepairPay.tbIconHoldPlace = True
        Me.btnRepairPay.tbIconImage = Nothing
        Me.btnRepairPay.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnRepairPay.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRepairPay.tbIconMore = False
        Me.btnRepairPay.tbIconMouseDown = Nothing
        Me.btnRepairPay.tbIconMouseHover = Nothing
        Me.btnRepairPay.tbIconMouseLeave = Nothing
        Me.btnRepairPay.tbIconPlaceText = 5
        Me.btnRepairPay.tbIconReadOnly = Nothing
        Me.btnRepairPay.tbImageMouseDown = Nothing
        Me.btnRepairPay.tbImageMouseHover = Nothing
        Me.btnRepairPay.tbImageMouseLeave = Nothing
        Me.btnRepairPay.tbProgressValue = 50
        Me.btnRepairPay.tbReadOnly = False
        Me.btnRepairPay.tbReadOnlyText = False
        Me.btnRepairPay.tbShadow = False
        Me.btnRepairPay.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRepairPay.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRepairPay.tbShowDot = False
        Me.btnRepairPay.tbShowMoreIconImg = CType(resources.GetObject("btnRepairPay.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRepairPay.tbShowNew = False
        Me.btnRepairPay.tbShowProgress = False
        Me.btnRepairPay.tbShowTip = True
        Me.btnRepairPay.tbShowToolTipOnButton = False
        Me.btnRepairPay.tbSplit = "0,0,0,0"
        Me.btnRepairPay.tbText = "闪退赔付"
        Me.btnRepairPay.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepairPay.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.btnRepairPay.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRepairPay.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepairPay.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepairPay.tbTextMouseDownPlace = 2
        Me.btnRepairPay.tbToolTip = ""
        Me.btnRepairPay.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(128, Byte), Integer))
        Me.btnRepairPay.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRepairPay.tbToolTipTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnRepairPay.VisibleEx = True
        '
        'btnBinding
        '
        Me.btnBinding.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnBinding.BackColor = System.Drawing.Color.Transparent
        Me.btnBinding.BindingForm = Nothing
        Me.btnBinding.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnBinding.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBinding.Location = New System.Drawing.Point(419, -7)
        Me.btnBinding.Name = "btnBinding"
        Me.btnBinding.Padding = New System.Windows.Forms.Padding(0, 10, 5, 2)
        Me.btnBinding.Selectable = True
        Me.btnBinding.Size = New System.Drawing.Size(101, 100)
        Me.btnBinding.TabIndex = 35
        Me.btnBinding.TabStop = False
        Me.btnBinding.tbAdriftIconWhenHover = False
        Me.btnBinding.tbAutoSize = False
        Me.btnBinding.tbAutoSizeEx = False
        Me.btnBinding.tbBackgroundImage = Nothing
        Me.btnBinding.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnBinding.tbBadgeNumber = 0
        Me.btnBinding.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBinding.tbEndEllipsis = False
        Me.btnBinding.tbIconHoldPlace = True
        Me.btnBinding.tbIconImage = Nothing
        Me.btnBinding.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnBinding.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBinding.tbIconMore = False
        Me.btnBinding.tbIconMouseDown = Nothing
        Me.btnBinding.tbIconMouseHover = Nothing
        Me.btnBinding.tbIconMouseLeave = Nothing
        Me.btnBinding.tbIconPlaceText = 5
        Me.btnBinding.tbIconReadOnly = Nothing
        Me.btnBinding.tbImageMouseDown = Nothing
        Me.btnBinding.tbImageMouseHover = Nothing
        Me.btnBinding.tbImageMouseLeave = Nothing
        Me.btnBinding.tbProgressValue = 50
        Me.btnBinding.tbReadOnly = False
        Me.btnBinding.tbReadOnlyText = False
        Me.btnBinding.tbShadow = False
        Me.btnBinding.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnBinding.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnBinding.tbShowDot = False
        Me.btnBinding.tbShowMoreIconImg = CType(resources.GetObject("btnBinding.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBinding.tbShowNew = False
        Me.btnBinding.tbShowProgress = False
        Me.btnBinding.tbShowTip = True
        Me.btnBinding.tbShowToolTipOnButton = False
        Me.btnBinding.tbSplit = "0,0,0,0"
        Me.btnBinding.tbText = "绑定Apple ID"
        Me.btnBinding.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBinding.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.btnBinding.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnBinding.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBinding.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBinding.tbTextMouseDownPlace = 2
        Me.btnBinding.tbToolTip = ""
        Me.btnBinding.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(128, Byte), Integer))
        Me.btnBinding.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBinding.tbToolTipTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnBinding.VisibleEx = True
        '
        'btnWeixin
        '
        Me.btnWeixin.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnWeixin.BackColor = System.Drawing.Color.Transparent
        Me.btnWeixin.BindingForm = Nothing
        Me.btnWeixin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnWeixin.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWeixin.Location = New System.Drawing.Point(313, -7)
        Me.btnWeixin.Name = "btnWeixin"
        Me.btnWeixin.Padding = New System.Windows.Forms.Padding(0, 10, 5, 2)
        Me.btnWeixin.Selectable = True
        Me.btnWeixin.Size = New System.Drawing.Size(101, 100)
        Me.btnWeixin.TabIndex = 34
        Me.btnWeixin.TabStop = False
        Me.btnWeixin.tbAdriftIconWhenHover = False
        Me.btnWeixin.tbAutoSize = False
        Me.btnWeixin.tbAutoSizeEx = False
        Me.btnWeixin.tbBackgroundImage = Nothing
        Me.btnWeixin.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnWeixin.tbBadgeNumber = 0
        Me.btnWeixin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnWeixin.tbEndEllipsis = False
        Me.btnWeixin.tbIconHoldPlace = True
        Me.btnWeixin.tbIconImage = Nothing
        Me.btnWeixin.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnWeixin.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnWeixin.tbIconMore = False
        Me.btnWeixin.tbIconMouseDown = Nothing
        Me.btnWeixin.tbIconMouseHover = Nothing
        Me.btnWeixin.tbIconMouseLeave = Nothing
        Me.btnWeixin.tbIconPlaceText = 5
        Me.btnWeixin.tbIconReadOnly = Nothing
        Me.btnWeixin.tbImageMouseDown = Nothing
        Me.btnWeixin.tbImageMouseHover = Nothing
        Me.btnWeixin.tbImageMouseLeave = Nothing
        Me.btnWeixin.tbProgressValue = 50
        Me.btnWeixin.tbReadOnly = False
        Me.btnWeixin.tbReadOnlyText = False
        Me.btnWeixin.tbShadow = False
        Me.btnWeixin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnWeixin.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnWeixin.tbShowDot = False
        Me.btnWeixin.tbShowMoreIconImg = CType(resources.GetObject("btnWeixin.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnWeixin.tbShowNew = False
        Me.btnWeixin.tbShowProgress = False
        Me.btnWeixin.tbShowTip = True
        Me.btnWeixin.tbShowToolTipOnButton = False
        Me.btnWeixin.tbSplit = "0,0,0,0"
        Me.btnWeixin.tbText = "微信"
        Me.btnWeixin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWeixin.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.btnWeixin.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnWeixin.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnWeixin.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnWeixin.tbTextMouseDownPlace = 2
        Me.btnWeixin.tbToolTip = ""
        Me.btnWeixin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(195, Byte), Integer))
        Me.btnWeixin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWeixin.tbToolTipTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnWeixin.Visible = False
        Me.btnWeixin.VisibleEx = True
        '
        'btnBackupRestore
        '
        Me.btnBackupRestore.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnBackupRestore.BackColor = System.Drawing.Color.Transparent
        Me.btnBackupRestore.BindingForm = Nothing
        Me.btnBackupRestore.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnBackupRestore.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackupRestore.Location = New System.Drawing.Point(202, -7)
        Me.btnBackupRestore.Name = "btnBackupRestore"
        Me.btnBackupRestore.Padding = New System.Windows.Forms.Padding(0, 10, 5, 2)
        Me.btnBackupRestore.Selectable = True
        Me.btnBackupRestore.Size = New System.Drawing.Size(107, 100)
        Me.btnBackupRestore.TabIndex = 33
        Me.btnBackupRestore.TabStop = False
        Me.btnBackupRestore.tbAdriftIconWhenHover = False
        Me.btnBackupRestore.tbAutoSize = False
        Me.btnBackupRestore.tbAutoSizeEx = False
        Me.btnBackupRestore.tbBackgroundImage = Nothing
        Me.btnBackupRestore.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnBackupRestore.tbBadgeNumber = 0
        Me.btnBackupRestore.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackupRestore.tbEndEllipsis = False
        Me.btnBackupRestore.tbIconHoldPlace = True
        Me.btnBackupRestore.tbIconImage = Nothing
        Me.btnBackupRestore.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnBackupRestore.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBackupRestore.tbIconMore = False
        Me.btnBackupRestore.tbIconMouseDown = Nothing
        Me.btnBackupRestore.tbIconMouseHover = Nothing
        Me.btnBackupRestore.tbIconMouseLeave = Nothing
        Me.btnBackupRestore.tbIconPlaceText = 5
        Me.btnBackupRestore.tbIconReadOnly = Nothing
        Me.btnBackupRestore.tbImageMouseDown = Nothing
        Me.btnBackupRestore.tbImageMouseHover = Nothing
        Me.btnBackupRestore.tbImageMouseLeave = Nothing
        Me.btnBackupRestore.tbProgressValue = 50
        Me.btnBackupRestore.tbReadOnly = False
        Me.btnBackupRestore.tbReadOnlyText = False
        Me.btnBackupRestore.tbShadow = False
        Me.btnBackupRestore.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnBackupRestore.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnBackupRestore.tbShowDot = False
        Me.btnBackupRestore.tbShowMoreIconImg = CType(resources.GetObject("btnBackupRestore.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBackupRestore.tbShowNew = False
        Me.btnBackupRestore.tbShowProgress = False
        Me.btnBackupRestore.tbShowTip = True
        Me.btnBackupRestore.tbShowToolTipOnButton = False
        Me.btnBackupRestore.tbSplit = "0,0,0,0"
        Me.btnBackupRestore.tbText = "备份还原"
        Me.btnBackupRestore.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackupRestore.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.btnBackupRestore.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnBackupRestore.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackupRestore.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackupRestore.tbTextMouseDownPlace = 2
        Me.btnBackupRestore.tbToolTip = ""
        Me.btnBackupRestore.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(195, Byte), Integer))
        Me.btnBackupRestore.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackupRestore.tbToolTipTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnBackupRestore.VisibleEx = True
        '
        'btnTuiToolV3
        '
        Me.btnTuiToolV3.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnTuiToolV3.BackColor = System.Drawing.Color.Transparent
        Me.btnTuiToolV3.BindingForm = Nothing
        Me.btnTuiToolV3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnTuiToolV3.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTuiToolV3.Location = New System.Drawing.Point(105, -7)
        Me.btnTuiToolV3.Name = "btnTuiToolV3"
        Me.btnTuiToolV3.Padding = New System.Windows.Forms.Padding(0, 10, 5, 2)
        Me.btnTuiToolV3.Selectable = True
        Me.btnTuiToolV3.Size = New System.Drawing.Size(93, 100)
        Me.btnTuiToolV3.TabIndex = 32
        Me.btnTuiToolV3.TabStop = False
        Me.btnTuiToolV3.tbAdriftIconWhenHover = False
        Me.btnTuiToolV3.tbAutoSize = False
        Me.btnTuiToolV3.tbAutoSizeEx = False
        Me.btnTuiToolV3.tbBackgroundImage = Nothing
        Me.btnTuiToolV3.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnTuiToolV3.tbBadgeNumber = 0
        Me.btnTuiToolV3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTuiToolV3.tbEndEllipsis = False
        Me.btnTuiToolV3.tbIconHoldPlace = True
        Me.btnTuiToolV3.tbIconImage = Nothing
        Me.btnTuiToolV3.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnTuiToolV3.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnTuiToolV3.tbIconMore = False
        Me.btnTuiToolV3.tbIconMouseDown = Nothing
        Me.btnTuiToolV3.tbIconMouseHover = Nothing
        Me.btnTuiToolV3.tbIconMouseLeave = Nothing
        Me.btnTuiToolV3.tbIconPlaceText = 5
        Me.btnTuiToolV3.tbIconReadOnly = Nothing
        Me.btnTuiToolV3.tbImageMouseDown = Nothing
        Me.btnTuiToolV3.tbImageMouseHover = Nothing
        Me.btnTuiToolV3.tbImageMouseLeave = Nothing
        Me.btnTuiToolV3.tbProgressValue = 50
        Me.btnTuiToolV3.tbReadOnly = False
        Me.btnTuiToolV3.tbReadOnlyText = False
        Me.btnTuiToolV3.tbShadow = False
        Me.btnTuiToolV3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnTuiToolV3.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnTuiToolV3.tbShowDot = False
        Me.btnTuiToolV3.tbShowMoreIconImg = CType(resources.GetObject("btnTuiToolV3.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnTuiToolV3.tbShowNew = False
        Me.btnTuiToolV3.tbShowProgress = False
        Me.btnTuiToolV3.tbShowTip = True
        Me.btnTuiToolV3.tbShowToolTipOnButton = False
        Me.btnTuiToolV3.tbSplit = "0,0,0,0"
        Me.btnTuiToolV3.tbText = "同步推正版"
        Me.btnTuiToolV3.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTuiToolV3.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.btnTuiToolV3.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnTuiToolV3.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnTuiToolV3.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnTuiToolV3.tbTextMouseDownPlace = 2
        Me.btnTuiToolV3.tbToolTip = ""
        Me.btnTuiToolV3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(195, Byte), Integer))
        Me.btnTuiToolV3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTuiToolV3.tbToolTipTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnTuiToolV3.VisibleEx = True
        '
        'btnRepairToolV3
        '
        Me.btnRepairToolV3.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRepairToolV3.BackColor = System.Drawing.Color.Transparent
        Me.btnRepairToolV3.BindingForm = Nothing
        Me.btnRepairToolV3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnRepairToolV3.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRepairToolV3.Location = New System.Drawing.Point(1, -7)
        Me.btnRepairToolV3.Name = "btnRepairToolV3"
        Me.btnRepairToolV3.Padding = New System.Windows.Forms.Padding(0, 10, 5, 2)
        Me.btnRepairToolV3.Selectable = True
        Me.btnRepairToolV3.Size = New System.Drawing.Size(93, 100)
        Me.btnRepairToolV3.TabIndex = 31
        Me.btnRepairToolV3.TabStop = False
        Me.btnRepairToolV3.tbAdriftIconWhenHover = False
        Me.btnRepairToolV3.tbAutoSize = False
        Me.btnRepairToolV3.tbAutoSizeEx = False
        Me.btnRepairToolV3.tbBackgroundImage = Nothing
        Me.btnRepairToolV3.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnRepairToolV3.tbBadgeNumber = 0
        Me.btnRepairToolV3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRepairToolV3.tbEndEllipsis = False
        Me.btnRepairToolV3.tbIconHoldPlace = True
        Me.btnRepairToolV3.tbIconImage = Nothing
        Me.btnRepairToolV3.tbIconImageAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnRepairToolV3.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRepairToolV3.tbIconMore = False
        Me.btnRepairToolV3.tbIconMouseDown = Nothing
        Me.btnRepairToolV3.tbIconMouseHover = Nothing
        Me.btnRepairToolV3.tbIconMouseLeave = Nothing
        Me.btnRepairToolV3.tbIconPlaceText = 5
        Me.btnRepairToolV3.tbIconReadOnly = Nothing
        Me.btnRepairToolV3.tbImageMouseDown = Nothing
        Me.btnRepairToolV3.tbImageMouseHover = Nothing
        Me.btnRepairToolV3.tbImageMouseLeave = Nothing
        Me.btnRepairToolV3.tbProgressValue = 50
        Me.btnRepairToolV3.tbReadOnly = False
        Me.btnRepairToolV3.tbReadOnlyText = False
        Me.btnRepairToolV3.tbShadow = False
        Me.btnRepairToolV3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRepairToolV3.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRepairToolV3.tbShowDot = False
        Me.btnRepairToolV3.tbShowMoreIconImg = CType(resources.GetObject("btnRepairToolV3.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRepairToolV3.tbShowNew = False
        Me.btnRepairToolV3.tbShowProgress = False
        Me.btnRepairToolV3.tbShowTip = True
        Me.btnRepairToolV3.tbShowToolTipOnButton = True
        Me.btnRepairToolV3.tbSplit = "0,0,0,0"
        Me.btnRepairToolV3.tbText = "正版授权"
        Me.btnRepairToolV3.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepairToolV3.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer), CType(CType(20, Byte), Integer))
        Me.btnRepairToolV3.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRepairToolV3.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepairToolV3.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepairToolV3.tbTextMouseDownPlace = 2
        Me.btnRepairToolV3.tbToolTip = ""
        Me.btnRepairToolV3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(138, Byte), Integer), CType(CType(195, Byte), Integer))
        Me.btnRepairToolV3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRepairToolV3.tbToolTipTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnRepairToolV3.VisibleEx = True
        '
        'btnBindingTip
        '
        Me.btnBindingTip.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnBindingTip.BackColor = System.Drawing.Color.Transparent
        Me.btnBindingTip.BindingForm = Nothing
        Me.btnBindingTip.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBindingTip.Location = New System.Drawing.Point(944, 390)
        Me.btnBindingTip.Name = "btnBindingTip"
        Me.btnBindingTip.Padding = New System.Windows.Forms.Padding(10, 10, 5, 10)
        Me.btnBindingTip.Selectable = True
        Me.btnBindingTip.Size = New System.Drawing.Size(152, 32)
        Me.btnBindingTip.TabIndex = 49
        Me.btnBindingTip.tbAdriftIconWhenHover = False
        Me.btnBindingTip.tbAutoSize = False
        Me.btnBindingTip.tbAutoSizeEx = True
        Me.btnBindingTip.tbBackgroundImage = Nothing
        Me.btnBindingTip.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnBindingTip.tbBadgeNumber = 0
        Me.btnBindingTip.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBindingTip.tbEndEllipsis = False
        Me.btnBindingTip.tbIconHoldPlace = True
        Me.btnBindingTip.tbIconImage = Nothing
        Me.btnBindingTip.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBindingTip.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBindingTip.tbIconMore = False
        Me.btnBindingTip.tbIconMouseDown = Nothing
        Me.btnBindingTip.tbIconMouseHover = Nothing
        Me.btnBindingTip.tbIconMouseLeave = Nothing
        Me.btnBindingTip.tbIconPlaceText = 2
        Me.btnBindingTip.tbIconReadOnly = Nothing
        Me.btnBindingTip.tbImageMouseDown = Nothing
        Me.btnBindingTip.tbImageMouseHover = Nothing
        Me.btnBindingTip.tbImageMouseLeave = Nothing
        Me.btnBindingTip.tbProgressValue = 50
        Me.btnBindingTip.tbReadOnly = False
        Me.btnBindingTip.tbReadOnlyText = False
        Me.btnBindingTip.tbShadow = False
        Me.btnBindingTip.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBindingTip.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBindingTip.tbShowDot = False
        Me.btnBindingTip.tbShowMoreIconImg = CType(resources.GetObject("btnBindingTip.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBindingTip.tbShowNew = False
        Me.btnBindingTip.tbShowProgress = False
        Me.btnBindingTip.tbShowTip = True
        Me.btnBindingTip.tbShowToolTipOnButton = False
        Me.btnBindingTip.tbSplit = "8,5,5,5"
        Me.btnBindingTip.tbText = "绑定 Apple ID 告别闪退"
        Me.btnBindingTip.tbTextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.btnBindingTip.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
        Me.btnBindingTip.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
        Me.btnBindingTip.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
        Me.btnBindingTip.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
        Me.btnBindingTip.tbTextMouseDownPlace = 0
        Me.btnBindingTip.tbToolTip = ""
        Me.btnBindingTip.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBindingTip.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBindingTip.tbToolTipTextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.btnBindingTip.Visible = False
        Me.btnBindingTip.VisibleEx = True
        '
        'pnlDFUModeInfo
        '
        Me.pnlDFUModeInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlDFUModeInfo.BackColor = System.Drawing.Color.Transparent
        Me.pnlDFUModeInfo.Controls.Add(Me.btnCopyDeviceId)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblCopyInfo)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblNote)
        Me.pnlDFUModeInfo.Controls.Add(Me.btnGotoFlashDevice)
        Me.pnlDFUModeInfo.Controls.Add(Me.btnOutRecoverMode)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblDFUDescription2)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblDFUDescription1)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblDeviceIdValue)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblDeviceId)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblECIDValue)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblProductTypeValue)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblDeviceTypeValue)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblECID)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblProductType)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblDeviceType)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblDFUTitle)
        Me.pnlDFUModeInfo.Controls.Add(Me.lblConnectMode)
        Me.pnlDFUModeInfo.Location = New System.Drawing.Point(8, 48)
        Me.pnlDFUModeInfo.Name = "pnlDFUModeInfo"
        Me.pnlDFUModeInfo.Size = New System.Drawing.Size(412, 520)
        Me.pnlDFUModeInfo.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDFUModeInfo.TabIndex = 50
        Me.pnlDFUModeInfo.tbBackgroundImage = Nothing
        Me.pnlDFUModeInfo.tbShowWatermark = False
        Me.pnlDFUModeInfo.tbSplit = "0,0,0,0"
        Me.pnlDFUModeInfo.tbWatermark = Nothing
        Me.pnlDFUModeInfo.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDFUModeInfo.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlDFUModeInfo.Visible = False
        '
        'btnCopyDeviceId
        '
        Me.btnCopyDeviceId.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnCopyDeviceId.BackColor = System.Drawing.Color.Transparent
        Me.btnCopyDeviceId.BindingForm = Nothing
        Me.btnCopyDeviceId.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnCopyDeviceId.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCopyDeviceId.Location = New System.Drawing.Point(163, 286)
        Me.btnCopyDeviceId.Name = "btnCopyDeviceId"
        Me.btnCopyDeviceId.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCopyDeviceId.Selectable = True
        Me.btnCopyDeviceId.Size = New System.Drawing.Size(39, 17)
        Me.btnCopyDeviceId.TabIndex = 49
        Me.btnCopyDeviceId.tbAdriftIconWhenHover = False
        Me.btnCopyDeviceId.tbAutoSize = False
        Me.btnCopyDeviceId.tbAutoSizeEx = True
        Me.btnCopyDeviceId.tbBackgroundImage = Nothing
        Me.btnCopyDeviceId.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnCopyDeviceId.tbBadgeNumber = 0
        Me.btnCopyDeviceId.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopyDeviceId.tbEndEllipsis = False
        Me.btnCopyDeviceId.tbIconHoldPlace = True
        Me.btnCopyDeviceId.tbIconImage = Nothing
        Me.btnCopyDeviceId.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyDeviceId.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCopyDeviceId.tbIconMore = False
        Me.btnCopyDeviceId.tbIconMouseDown = Nothing
        Me.btnCopyDeviceId.tbIconMouseHover = Nothing
        Me.btnCopyDeviceId.tbIconMouseLeave = Nothing
        Me.btnCopyDeviceId.tbIconPlaceText = 2
        Me.btnCopyDeviceId.tbIconReadOnly = Nothing
        Me.btnCopyDeviceId.tbImageMouseDown = Nothing
        Me.btnCopyDeviceId.tbImageMouseHover = Nothing
        Me.btnCopyDeviceId.tbImageMouseLeave = Nothing
        Me.btnCopyDeviceId.tbProgressValue = 50
        Me.btnCopyDeviceId.tbReadOnly = False
        Me.btnCopyDeviceId.tbReadOnlyText = False
        Me.btnCopyDeviceId.tbShadow = False
        Me.btnCopyDeviceId.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCopyDeviceId.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCopyDeviceId.tbShowDot = False
        Me.btnCopyDeviceId.tbShowMoreIconImg = CType(resources.GetObject("btnCopyDeviceId.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCopyDeviceId.tbShowNew = False
        Me.btnCopyDeviceId.tbShowProgress = False
        Me.btnCopyDeviceId.tbShowTip = True
        Me.btnCopyDeviceId.tbShowToolTipOnButton = False
        Me.btnCopyDeviceId.tbSplit = "13,11,13,11"
        Me.btnCopyDeviceId.tbText = "复制"
        Me.btnCopyDeviceId.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyDeviceId.tbTextColor = System.Drawing.Color.CornflowerBlue
        Me.btnCopyDeviceId.tbTextColorDisable = System.Drawing.Color.CornflowerBlue
        Me.btnCopyDeviceId.tbTextColorDown = System.Drawing.Color.CornflowerBlue
        Me.btnCopyDeviceId.tbTextColorHover = System.Drawing.Color.CornflowerBlue
        Me.btnCopyDeviceId.tbTextMouseDownPlace = 0
        Me.btnCopyDeviceId.tbToolTip = ""
        Me.btnCopyDeviceId.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCopyDeviceId.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopyDeviceId.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyDeviceId.VisibleEx = True
        '
        'lblCopyInfo
        '
        Me.lblCopyInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblCopyInfo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblCopyInfo.ForeColor = System.Drawing.Color.Green
        Me.lblCopyInfo.Location = New System.Drawing.Point(208, 287)
        Me.lblCopyInfo.Name = "lblCopyInfo"
        Me.lblCopyInfo.Size = New System.Drawing.Size(107, 17)
        Me.lblCopyInfo.TabIndex = 48
        Me.lblCopyInfo.tbAdriftWhenHover = False
        Me.lblCopyInfo.tbAutoEllipsis = False
        Me.lblCopyInfo.tbAutoSize = False
        Me.lblCopyInfo.tbHideImage = False
        Me.lblCopyInfo.tbIconImage = Nothing
        Me.lblCopyInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCopyInfo.tbIconPlaceText = 5
        Me.lblCopyInfo.tbShadow = False
        Me.lblCopyInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCopyInfo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCopyInfo.tbShowScrolling = False
        Me.lblCopyInfo.Text = "复制成功！"
        Me.lblCopyInfo.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCopyInfo.Visible = False
        '
        'lblNote
        '
        Me.lblNote.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblNote.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblNote.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblNote.Location = New System.Drawing.Point(11, 416)
        Me.lblNote.Name = "lblNote"
        Me.lblNote.Size = New System.Drawing.Size(558, 29)
        Me.lblNote.TabIndex = 6
        Me.lblNote.tbAdriftWhenHover = False
        Me.lblNote.tbAutoEllipsis = False
        Me.lblNote.tbAutoSize = False
        Me.lblNote.tbHideImage = False
        Me.lblNote.tbIconImage = Global.iTong.My.Resources.Resources.app_cell_fail
        Me.lblNote.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNote.tbIconPlaceText = 5
        Me.lblNote.tbShadow = False
        Me.lblNote.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNote.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNote.tbShowScrolling = False
        Me.lblNote.Text = "提示：DFU模式下仍可进行刷机操作，如果手动退出DFU模式失败，则需要通过刷机来解决该模式！"
        Me.lblNote.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnGotoFlashDevice
        '
        Me.btnGotoFlashDevice.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnGotoFlashDevice.BackColor = System.Drawing.Color.Transparent
        Me.btnGotoFlashDevice.BindingForm = Nothing
        Me.btnGotoFlashDevice.Font = New System.Drawing.Font("宋体", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnGotoFlashDevice.Location = New System.Drawing.Point(163, 362)
        Me.btnGotoFlashDevice.Name = "btnGotoFlashDevice"
        Me.btnGotoFlashDevice.Padding = New System.Windows.Forms.Padding(20, 2, 20, 2)
        Me.btnGotoFlashDevice.Selectable = True
        Me.btnGotoFlashDevice.Size = New System.Drawing.Size(159, 36)
        Me.btnGotoFlashDevice.TabIndex = 47
        Me.btnGotoFlashDevice.tbAdriftIconWhenHover = False
        Me.btnGotoFlashDevice.tbAutoSize = False
        Me.btnGotoFlashDevice.tbAutoSizeEx = True
        Me.btnGotoFlashDevice.tbBackgroundImage = Nothing
        Me.btnGotoFlashDevice.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnGotoFlashDevice.tbBadgeNumber = 0
        Me.btnGotoFlashDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGotoFlashDevice.tbEndEllipsis = False
        Me.btnGotoFlashDevice.tbIconHoldPlace = True
        Me.btnGotoFlashDevice.tbIconImage = Nothing
        Me.btnGotoFlashDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnGotoFlashDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGotoFlashDevice.tbIconMore = False
        Me.btnGotoFlashDevice.tbIconMouseDown = Nothing
        Me.btnGotoFlashDevice.tbIconMouseHover = Nothing
        Me.btnGotoFlashDevice.tbIconMouseLeave = Nothing
        Me.btnGotoFlashDevice.tbIconPlaceText = 0
        Me.btnGotoFlashDevice.tbIconReadOnly = Nothing
        Me.btnGotoFlashDevice.tbImageMouseDown = Nothing
        Me.btnGotoFlashDevice.tbImageMouseHover = Nothing
        Me.btnGotoFlashDevice.tbImageMouseLeave = Nothing
        Me.btnGotoFlashDevice.tbProgressValue = 50
        Me.btnGotoFlashDevice.tbReadOnly = False
        Me.btnGotoFlashDevice.tbReadOnlyText = False
        Me.btnGotoFlashDevice.tbShadow = False
        Me.btnGotoFlashDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGotoFlashDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGotoFlashDevice.tbShowDot = False
        Me.btnGotoFlashDevice.tbShowMoreIconImg = CType(resources.GetObject("btnGotoFlashDevice.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGotoFlashDevice.tbShowNew = False
        Me.btnGotoFlashDevice.tbShowProgress = False
        Me.btnGotoFlashDevice.tbShowTip = True
        Me.btnGotoFlashDevice.tbShowToolTipOnButton = False
        Me.btnGotoFlashDevice.tbSplit = "13,11,13,11"
        Me.btnGotoFlashDevice.tbText = "跳转到刷机界面"
        Me.btnGotoFlashDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoFlashDevice.tbTextColor = System.Drawing.Color.White
        Me.btnGotoFlashDevice.tbTextColorDisable = System.Drawing.Color.White
        Me.btnGotoFlashDevice.tbTextColorDown = System.Drawing.Color.White
        Me.btnGotoFlashDevice.tbTextColorHover = System.Drawing.Color.White
        Me.btnGotoFlashDevice.tbTextMouseDownPlace = 0
        Me.btnGotoFlashDevice.tbToolTip = ""
        Me.btnGotoFlashDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGotoFlashDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGotoFlashDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoFlashDevice.VisibleEx = True
        '
        'btnOutRecoverMode
        '
        Me.btnOutRecoverMode.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnOutRecoverMode.BackColor = System.Drawing.Color.Transparent
        Me.btnOutRecoverMode.BindingForm = Nothing
        Me.btnOutRecoverMode.Font = New System.Drawing.Font("宋体", 11.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOutRecoverMode.Location = New System.Drawing.Point(11, 362)
        Me.btnOutRecoverMode.Name = "btnOutRecoverMode"
        Me.btnOutRecoverMode.Padding = New System.Windows.Forms.Padding(20, 2, 20, 2)
        Me.btnOutRecoverMode.Selectable = True
        Me.btnOutRecoverMode.Size = New System.Drawing.Size(143, 36)
        Me.btnOutRecoverMode.TabIndex = 46
        Me.btnOutRecoverMode.tbAdriftIconWhenHover = False
        Me.btnOutRecoverMode.tbAutoSize = False
        Me.btnOutRecoverMode.tbAutoSizeEx = True
        Me.btnOutRecoverMode.tbBackgroundImage = Nothing
        Me.btnOutRecoverMode.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOutRecoverMode.tbBadgeNumber = 0
        Me.btnOutRecoverMode.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOutRecoverMode.tbEndEllipsis = False
        Me.btnOutRecoverMode.tbIconHoldPlace = True
        Me.btnOutRecoverMode.tbIconImage = Nothing
        Me.btnOutRecoverMode.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOutRecoverMode.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOutRecoverMode.tbIconMore = False
        Me.btnOutRecoverMode.tbIconMouseDown = Nothing
        Me.btnOutRecoverMode.tbIconMouseHover = Nothing
        Me.btnOutRecoverMode.tbIconMouseLeave = Nothing
        Me.btnOutRecoverMode.tbIconPlaceText = 0
        Me.btnOutRecoverMode.tbIconReadOnly = Nothing
        Me.btnOutRecoverMode.tbImageMouseDown = Nothing
        Me.btnOutRecoverMode.tbImageMouseHover = Nothing
        Me.btnOutRecoverMode.tbImageMouseLeave = Nothing
        Me.btnOutRecoverMode.tbProgressValue = 50
        Me.btnOutRecoverMode.tbReadOnly = False
        Me.btnOutRecoverMode.tbReadOnlyText = False
        Me.btnOutRecoverMode.tbShadow = False
        Me.btnOutRecoverMode.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOutRecoverMode.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOutRecoverMode.tbShowDot = False
        Me.btnOutRecoverMode.tbShowMoreIconImg = CType(resources.GetObject("btnOutRecoverMode.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOutRecoverMode.tbShowNew = False
        Me.btnOutRecoverMode.tbShowProgress = False
        Me.btnOutRecoverMode.tbShowTip = True
        Me.btnOutRecoverMode.tbShowToolTipOnButton = False
        Me.btnOutRecoverMode.tbSplit = "13,11,13,11"
        Me.btnOutRecoverMode.tbText = "退出恢复模式"
        Me.btnOutRecoverMode.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOutRecoverMode.tbTextColor = System.Drawing.Color.White
        Me.btnOutRecoverMode.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOutRecoverMode.tbTextColorDown = System.Drawing.Color.White
        Me.btnOutRecoverMode.tbTextColorHover = System.Drawing.Color.White
        Me.btnOutRecoverMode.tbTextMouseDownPlace = 0
        Me.btnOutRecoverMode.tbToolTip = ""
        Me.btnOutRecoverMode.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOutRecoverMode.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOutRecoverMode.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOutRecoverMode.VisibleEx = True
        '
        'lblDFUDescription2
        '
        Me.lblDFUDescription2.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblDFUDescription2.BackColor = System.Drawing.Color.Transparent
        Me.lblDFUDescription2.BindingForm = Nothing
        Me.lblDFUDescription2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDFUDescription2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDFUDescription2.Location = New System.Drawing.Point(11, 149)
        Me.lblDFUDescription2.Name = "lblDFUDescription2"
        Me.lblDFUDescription2.Padding = New System.Windows.Forms.Padding(0, 0, 5, 0)
        Me.lblDFUDescription2.Selectable = True
        Me.lblDFUDescription2.Size = New System.Drawing.Size(322, 25)
        Me.lblDFUDescription2.TabIndex = 17
        Me.lblDFUDescription2.TabStop = False
        Me.lblDFUDescription2.tbAdriftIconWhenHover = False
        Me.lblDFUDescription2.tbAutoSize = True
        Me.lblDFUDescription2.tbAutoSizeEx = True
        Me.lblDFUDescription2.tbBackgroundImage = Nothing
        Me.lblDFUDescription2.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.lblDFUDescription2.tbBadgeNumber = 0
        Me.lblDFUDescription2.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.lblDFUDescription2.tbEndEllipsis = False
        Me.lblDFUDescription2.tbIconHoldPlace = True
        Me.lblDFUDescription2.tbIconImage = Nothing
        Me.lblDFUDescription2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDFUDescription2.tbIconImageState = iTong.Components.ImageState.OneState
        Me.lblDFUDescription2.tbIconMore = False
        Me.lblDFUDescription2.tbIconMouseDown = Nothing
        Me.lblDFUDescription2.tbIconMouseHover = Nothing
        Me.lblDFUDescription2.tbIconMouseLeave = Nothing
        Me.lblDFUDescription2.tbIconPlaceText = 2
        Me.lblDFUDescription2.tbIconReadOnly = Nothing
        Me.lblDFUDescription2.tbImageMouseDown = Nothing
        Me.lblDFUDescription2.tbImageMouseHover = Nothing
        Me.lblDFUDescription2.tbImageMouseLeave = Nothing
        Me.lblDFUDescription2.tbProgressValue = 50
        Me.lblDFUDescription2.tbReadOnly = False
        Me.lblDFUDescription2.tbReadOnlyText = False
        Me.lblDFUDescription2.tbShadow = False
        Me.lblDFUDescription2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.lblDFUDescription2.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.lblDFUDescription2.tbShowDot = False
        Me.lblDFUDescription2.tbShowMoreIconImg = CType(resources.GetObject("lblDFUDescription2.tbShowMoreIconImg"), System.Drawing.Image)
        Me.lblDFUDescription2.tbShowNew = False
        Me.lblDFUDescription2.tbShowProgress = False
        Me.lblDFUDescription2.tbShowTip = True
        Me.lblDFUDescription2.tbShowToolTipOnButton = False
        Me.lblDFUDescription2.tbSplit = "13,11,13,11"
        Me.lblDFUDescription2.tbText = "保持设备与电脑的连接状态，几秒后设备将退出恢复模式。"
        Me.lblDFUDescription2.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDFUDescription2.tbTextColor = System.Drawing.Color.Black
        Me.lblDFUDescription2.tbTextColorDisable = System.Drawing.Color.Black
        Me.lblDFUDescription2.tbTextColorDown = System.Drawing.Color.Black
        Me.lblDFUDescription2.tbTextColorHover = System.Drawing.Color.Black
        Me.lblDFUDescription2.tbTextMouseDownPlace = 0
        Me.lblDFUDescription2.tbToolTip = ""
        Me.lblDFUDescription2.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.lblDFUDescription2.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDFUDescription2.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDFUDescription2.VisibleEx = True
        '
        'lblDFUDescription1
        '
        Me.lblDFUDescription1.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblDFUDescription1.BackColor = System.Drawing.Color.Transparent
        Me.lblDFUDescription1.BindingForm = Nothing
        Me.lblDFUDescription1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDFUDescription1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDFUDescription1.Location = New System.Drawing.Point(11, 124)
        Me.lblDFUDescription1.Name = "lblDFUDescription1"
        Me.lblDFUDescription1.Padding = New System.Windows.Forms.Padding(0, 0, 5, 0)
        Me.lblDFUDescription1.Selectable = True
        Me.lblDFUDescription1.Size = New System.Drawing.Size(520, 25)
        Me.lblDFUDescription1.TabIndex = 16
        Me.lblDFUDescription1.TabStop = False
        Me.lblDFUDescription1.tbAdriftIconWhenHover = False
        Me.lblDFUDescription1.tbAutoSize = True
        Me.lblDFUDescription1.tbAutoSizeEx = True
        Me.lblDFUDescription1.tbBackgroundImage = Nothing
        Me.lblDFUDescription1.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.lblDFUDescription1.tbBadgeNumber = 0
        Me.lblDFUDescription1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.lblDFUDescription1.tbEndEllipsis = False
        Me.lblDFUDescription1.tbIconHoldPlace = True
        Me.lblDFUDescription1.tbIconImage = Nothing
        Me.lblDFUDescription1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDFUDescription1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.lblDFUDescription1.tbIconMore = False
        Me.lblDFUDescription1.tbIconMouseDown = Nothing
        Me.lblDFUDescription1.tbIconMouseHover = Nothing
        Me.lblDFUDescription1.tbIconMouseLeave = Nothing
        Me.lblDFUDescription1.tbIconPlaceText = 2
        Me.lblDFUDescription1.tbIconReadOnly = Nothing
        Me.lblDFUDescription1.tbImageMouseDown = Nothing
        Me.lblDFUDescription1.tbImageMouseHover = Nothing
        Me.lblDFUDescription1.tbImageMouseLeave = Nothing
        Me.lblDFUDescription1.tbProgressValue = 50
        Me.lblDFUDescription1.tbReadOnly = False
        Me.lblDFUDescription1.tbReadOnlyText = False
        Me.lblDFUDescription1.tbShadow = False
        Me.lblDFUDescription1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.lblDFUDescription1.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.lblDFUDescription1.tbShowDot = False
        Me.lblDFUDescription1.tbShowMoreIconImg = CType(resources.GetObject("lblDFUDescription1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.lblDFUDescription1.tbShowNew = False
        Me.lblDFUDescription1.tbShowProgress = False
        Me.lblDFUDescription1.tbShowTip = True
        Me.lblDFUDescription1.tbShowToolTipOnButton = False
        Me.lblDFUDescription1.tbSplit = "13,11,13,11"
        Me.lblDFUDescription1.tbText = "同时按住设备的""Home""键和""开机""键不松开，直到屏幕出现白色的苹果画面，即可退出DFU模式。"
        Me.lblDFUDescription1.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDFUDescription1.tbTextColor = System.Drawing.Color.Black
        Me.lblDFUDescription1.tbTextColorDisable = System.Drawing.Color.Black
        Me.lblDFUDescription1.tbTextColorDown = System.Drawing.Color.Black
        Me.lblDFUDescription1.tbTextColorHover = System.Drawing.Color.Black
        Me.lblDFUDescription1.tbTextMouseDownPlace = 0
        Me.lblDFUDescription1.tbToolTip = ""
        Me.lblDFUDescription1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.lblDFUDescription1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDFUDescription1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDFUDescription1.VisibleEx = True
        '
        'lblDeviceIdValue
        '
        Me.lblDeviceIdValue.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblDeviceIdValue.AutoSize = True
        Me.lblDeviceIdValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDeviceIdValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceIdValue.Location = New System.Drawing.Point(99, 291)
        Me.lblDeviceIdValue.Name = "lblDeviceIdValue"
        Me.lblDeviceIdValue.Size = New System.Drawing.Size(53, 12)
        Me.lblDeviceIdValue.TabIndex = 14
        Me.lblDeviceIdValue.tbAdriftWhenHover = False
        Me.lblDeviceIdValue.tbAutoEllipsis = False
        Me.lblDeviceIdValue.tbAutoSize = True
        Me.lblDeviceIdValue.tbHideImage = False
        Me.lblDeviceIdValue.tbIconImage = Nothing
        Me.lblDeviceIdValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceIdValue.tbIconPlaceText = 5
        Me.lblDeviceIdValue.tbShadow = False
        Me.lblDeviceIdValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceIdValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceIdValue.tbShowScrolling = False
        Me.lblDeviceIdValue.Text = "序列号："
        '
        'lblDeviceId
        '
        Me.lblDeviceId.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblDeviceId.AutoSize = True
        Me.lblDeviceId.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDeviceId.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblDeviceId.Location = New System.Drawing.Point(8, 291)
        Me.lblDeviceId.Name = "lblDeviceId"
        Me.lblDeviceId.Size = New System.Drawing.Size(53, 12)
        Me.lblDeviceId.TabIndex = 13
        Me.lblDeviceId.tbAdriftWhenHover = False
        Me.lblDeviceId.tbAutoEllipsis = False
        Me.lblDeviceId.tbAutoSize = True
        Me.lblDeviceId.tbHideImage = False
        Me.lblDeviceId.tbIconImage = Nothing
        Me.lblDeviceId.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceId.tbIconPlaceText = 5
        Me.lblDeviceId.tbShadow = False
        Me.lblDeviceId.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceId.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceId.tbShowScrolling = False
        Me.lblDeviceId.Text = "序列号："
        '
        'lblECIDValue
        '
        Me.lblECIDValue.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblECIDValue.AutoSize = True
        Me.lblECIDValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblECIDValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblECIDValue.Location = New System.Drawing.Point(99, 262)
        Me.lblECIDValue.Name = "lblECIDValue"
        Me.lblECIDValue.Size = New System.Drawing.Size(65, 12)
        Me.lblECIDValue.TabIndex = 12
        Me.lblECIDValue.tbAdriftWhenHover = False
        Me.lblECIDValue.tbAutoEllipsis = False
        Me.lblECIDValue.tbAutoSize = True
        Me.lblECIDValue.tbHideImage = False
        Me.lblECIDValue.tbIconImage = Nothing
        Me.lblECIDValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblECIDValue.tbIconPlaceText = 5
        Me.lblECIDValue.tbShadow = False
        Me.lblECIDValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblECIDValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblECIDValue.tbShowScrolling = False
        Me.lblECIDValue.Text = "设备ECID："
        '
        'lblProductTypeValue
        '
        Me.lblProductTypeValue.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblProductTypeValue.AutoSize = True
        Me.lblProductTypeValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblProductTypeValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblProductTypeValue.Location = New System.Drawing.Point(99, 233)
        Me.lblProductTypeValue.Name = "lblProductTypeValue"
        Me.lblProductTypeValue.Size = New System.Drawing.Size(65, 12)
        Me.lblProductTypeValue.TabIndex = 11
        Me.lblProductTypeValue.tbAdriftWhenHover = False
        Me.lblProductTypeValue.tbAutoEllipsis = False
        Me.lblProductTypeValue.tbAutoSize = True
        Me.lblProductTypeValue.tbHideImage = False
        Me.lblProductTypeValue.tbIconImage = Nothing
        Me.lblProductTypeValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblProductTypeValue.tbIconPlaceText = 5
        Me.lblProductTypeValue.tbShadow = False
        Me.lblProductTypeValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblProductTypeValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblProductTypeValue.tbShowScrolling = False
        Me.lblProductTypeValue.Text = "产品型号："
        '
        'lblDeviceTypeValue
        '
        Me.lblDeviceTypeValue.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblDeviceTypeValue.AutoSize = True
        Me.lblDeviceTypeValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDeviceTypeValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceTypeValue.Location = New System.Drawing.Point(99, 204)
        Me.lblDeviceTypeValue.Name = "lblDeviceTypeValue"
        Me.lblDeviceTypeValue.Size = New System.Drawing.Size(65, 12)
        Me.lblDeviceTypeValue.TabIndex = 10
        Me.lblDeviceTypeValue.tbAdriftWhenHover = False
        Me.lblDeviceTypeValue.tbAutoEllipsis = False
        Me.lblDeviceTypeValue.tbAutoSize = True
        Me.lblDeviceTypeValue.tbHideImage = False
        Me.lblDeviceTypeValue.tbIconImage = Nothing
        Me.lblDeviceTypeValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceTypeValue.tbIconPlaceText = 5
        Me.lblDeviceTypeValue.tbShadow = False
        Me.lblDeviceTypeValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceTypeValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceTypeValue.tbShowScrolling = False
        Me.lblDeviceTypeValue.Text = "设备型号："
        '
        'lblECID
        '
        Me.lblECID.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblECID.AutoSize = True
        Me.lblECID.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblECID.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblECID.Location = New System.Drawing.Point(8, 262)
        Me.lblECID.Name = "lblECID"
        Me.lblECID.Size = New System.Drawing.Size(65, 12)
        Me.lblECID.TabIndex = 5
        Me.lblECID.tbAdriftWhenHover = False
        Me.lblECID.tbAutoEllipsis = False
        Me.lblECID.tbAutoSize = True
        Me.lblECID.tbHideImage = False
        Me.lblECID.tbIconImage = Nothing
        Me.lblECID.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblECID.tbIconPlaceText = 5
        Me.lblECID.tbShadow = False
        Me.lblECID.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblECID.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblECID.tbShowScrolling = False
        Me.lblECID.Text = "设备ECID："
        '
        'lblProductType
        '
        Me.lblProductType.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblProductType.AutoSize = True
        Me.lblProductType.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblProductType.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblProductType.Location = New System.Drawing.Point(8, 233)
        Me.lblProductType.Name = "lblProductType"
        Me.lblProductType.Size = New System.Drawing.Size(65, 12)
        Me.lblProductType.TabIndex = 4
        Me.lblProductType.tbAdriftWhenHover = False
        Me.lblProductType.tbAutoEllipsis = False
        Me.lblProductType.tbAutoSize = True
        Me.lblProductType.tbHideImage = False
        Me.lblProductType.tbIconImage = Nothing
        Me.lblProductType.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblProductType.tbIconPlaceText = 5
        Me.lblProductType.tbShadow = False
        Me.lblProductType.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblProductType.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblProductType.tbShowScrolling = False
        Me.lblProductType.Text = "产品型号："
        '
        'lblDeviceType
        '
        Me.lblDeviceType.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblDeviceType.AutoSize = True
        Me.lblDeviceType.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDeviceType.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblDeviceType.Location = New System.Drawing.Point(8, 204)
        Me.lblDeviceType.Name = "lblDeviceType"
        Me.lblDeviceType.Size = New System.Drawing.Size(65, 12)
        Me.lblDeviceType.TabIndex = 3
        Me.lblDeviceType.tbAdriftWhenHover = False
        Me.lblDeviceType.tbAutoEllipsis = False
        Me.lblDeviceType.tbAutoSize = True
        Me.lblDeviceType.tbHideImage = False
        Me.lblDeviceType.tbIconImage = Nothing
        Me.lblDeviceType.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceType.tbIconPlaceText = 5
        Me.lblDeviceType.tbShadow = False
        Me.lblDeviceType.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceType.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceType.tbShowScrolling = False
        Me.lblDeviceType.Text = "设备型号："
        '
        'lblDFUTitle
        '
        Me.lblDFUTitle.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblDFUTitle.AutoSize = True
        Me.lblDFUTitle.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDFUTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDFUTitle.Location = New System.Drawing.Point(8, 95)
        Me.lblDFUTitle.Name = "lblDFUTitle"
        Me.lblDFUTitle.Size = New System.Drawing.Size(107, 12)
        Me.lblDFUTitle.TabIndex = 1
        Me.lblDFUTitle.tbAdriftWhenHover = False
        Me.lblDFUTitle.tbAutoEllipsis = False
        Me.lblDFUTitle.tbAutoSize = True
        Me.lblDFUTitle.tbHideImage = False
        Me.lblDFUTitle.tbIconImage = Nothing
        Me.lblDFUTitle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDFUTitle.tbIconPlaceText = 5
        Me.lblDFUTitle.tbShadow = False
        Me.lblDFUTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDFUTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDFUTitle.tbShowScrolling = False
        Me.lblDFUTitle.Text = "如何退出DFU模式："
        '
        'lblConnectMode
        '
        Me.lblConnectMode.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblConnectMode.AutoSize = True
        Me.lblConnectMode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(164, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.lblConnectMode.Location = New System.Drawing.Point(4, 37)
        Me.lblConnectMode.Name = "lblConnectMode"
        Me.lblConnectMode.Size = New System.Drawing.Size(168, 14)
        Me.lblConnectMode.TabIndex = 0
        Me.lblConnectMode.Text = "设备连接成功（DFU模式）"
        '
        'tmrShowCopyInfo
        '
        Me.tmrShowCopyInfo.Interval = 1000
        '
        'pnlSummary
        '
        Me.pnlSummary.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlSummary.Controls.Add(Me.lblBackup)
        Me.pnlSummary.Controls.Add(Me.btnClearTip)
        Me.pnlSummary.Controls.Add(Me.picSplit3)
        Me.pnlSummary.Controls.Add(Me.picSplit2)
        Me.pnlSummary.Controls.Add(Me.picSplit1)
        Me.pnlSummary.Controls.Add(Me.btnDeviceInfo)
        Me.pnlSummary.Controls.Add(Me.btnEditDeviceName)
        Me.pnlSummary.Controls.Add(Me.lblDeviceName)
        Me.pnlSummary.Controls.Add(Me.txtDeviceName)
        Me.pnlSummary.Controls.Add(Me.btnRingtone)
        Me.pnlSummary.Controls.Add(Me.btniOSDowngrade)
        Me.pnlSummary.Controls.Add(Me.btnWechat)
        Me.pnlSummary.Controls.Add(Me.pnlDeviceInfo)
        Me.pnlSummary.Controls.Add(Me.btnCloseDevice)
        Me.pnlSummary.Controls.Add(Me.btnRestarDevice)
        Me.pnlSummary.Controls.Add(Me.btnDeviceDesktop)
        Me.pnlSummary.Controls.Add(Me.btnScreensort1)
        Me.pnlSummary.Controls.Add(Me.lblDownPluginProgress)
        Me.pnlSummary.Controls.Add(Me.picDevice)
        Me.pnlSummary.Controls.Add(Me.tbDevCapacity)
        Me.pnlSummary.Controls.Add(Me.btnQQChat)
        Me.pnlSummary.Controls.Add(Me.btnZXKF)
        Me.pnlSummary.Location = New System.Drawing.Point(15, 30)
        Me.pnlSummary.Name = "pnlSummary"
        Me.pnlSummary.Size = New System.Drawing.Size(1481, 592)
        Me.pnlSummary.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlSummary.TabIndex = 51
        Me.pnlSummary.tbBackgroundImage = Nothing
        Me.pnlSummary.tbShowWatermark = False
        Me.pnlSummary.tbSplit = "0,0,0,0"
        Me.pnlSummary.tbWatermark = Nothing
        Me.pnlSummary.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlSummary.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblBackup
        '
        Me.lblBackup.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(190, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblBackup.AutoSize = True
        Me.lblBackup.BackColor = System.Drawing.Color.Transparent
        Me.lblBackup.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblBackup.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblBackup.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblBackup.Location = New System.Drawing.Point(1127, 404)
        Me.lblBackup.Name = "lblBackup"
        Me.lblBackup.Size = New System.Drawing.Size(53, 12)
        Me.lblBackup.TabIndex = 93
        Me.lblBackup.TabStop = True
        Me.lblBackup.Text = "备份还原"
        Me.lblBackup.Visible = False
        '
        'picSplit3
        '
        Me.picSplit3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picSplit3.Image = Global.iTong.My.Resources.Resources.summary_nav_bg
        Me.picSplit3.Location = New System.Drawing.Point(491, 507)
        Me.picSplit3.Name = "picSplit3"
        Me.picSplit3.Size = New System.Drawing.Size(1, 21)
        Me.picSplit3.TabIndex = 68
        Me.picSplit3.TabStop = False
        '
        'picSplit2
        '
        Me.picSplit2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picSplit2.Image = Global.iTong.My.Resources.Resources.summary_nav_bg
        Me.picSplit2.Location = New System.Drawing.Point(430, 507)
        Me.picSplit2.Name = "picSplit2"
        Me.picSplit2.Size = New System.Drawing.Size(1, 21)
        Me.picSplit2.TabIndex = 67
        Me.picSplit2.TabStop = False
        '
        'picSplit1
        '
        Me.picSplit1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picSplit1.Image = Global.iTong.My.Resources.Resources.summary_nav_bg
        Me.picSplit1.Location = New System.Drawing.Point(344, 507)
        Me.picSplit1.Name = "picSplit1"
        Me.picSplit1.Size = New System.Drawing.Size(1, 21)
        Me.picSplit1.TabIndex = 66
        Me.picSplit1.TabStop = False
        '
        'btnDeviceInfo
        '
        Me.btnDeviceInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnDeviceInfo.BackColor = System.Drawing.Color.Transparent
        Me.btnDeviceInfo.BindingForm = Nothing
        Me.btnDeviceInfo.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDeviceInfo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDeviceInfo.Location = New System.Drawing.Point(430, 35)
        Me.btnDeviceInfo.Name = "btnDeviceInfo"
        Me.btnDeviceInfo.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDeviceInfo.Selectable = True
        Me.btnDeviceInfo.Size = New System.Drawing.Size(18, 18)
        Me.btnDeviceInfo.TabIndex = 65
        Me.btnDeviceInfo.TabStop = False
        Me.btnDeviceInfo.tbAdriftIconWhenHover = False
        Me.btnDeviceInfo.tbAutoSize = True
        Me.btnDeviceInfo.tbAutoSizeEx = False
        Me.btnDeviceInfo.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_4_info
        Me.btnDeviceInfo.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDeviceInfo.tbBadgeNumber = 0
        Me.btnDeviceInfo.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDeviceInfo.tbEndEllipsis = False
        Me.btnDeviceInfo.tbIconHoldPlace = True
        Me.btnDeviceInfo.tbIconImage = Nothing
        Me.btnDeviceInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDeviceInfo.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDeviceInfo.tbIconMore = False
        Me.btnDeviceInfo.tbIconMouseDown = Nothing
        Me.btnDeviceInfo.tbIconMouseHover = Nothing
        Me.btnDeviceInfo.tbIconMouseLeave = Nothing
        Me.btnDeviceInfo.tbIconPlaceText = 2
        Me.btnDeviceInfo.tbIconReadOnly = Nothing
        Me.btnDeviceInfo.tbImageMouseDown = Nothing
        Me.btnDeviceInfo.tbImageMouseHover = Nothing
        Me.btnDeviceInfo.tbImageMouseLeave = Nothing
        Me.btnDeviceInfo.tbProgressValue = 50
        Me.btnDeviceInfo.tbReadOnly = False
        Me.btnDeviceInfo.tbReadOnlyText = False
        Me.btnDeviceInfo.tbShadow = False
        Me.btnDeviceInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnDeviceInfo.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnDeviceInfo.tbShowDot = False
        Me.btnDeviceInfo.tbShowMoreIconImg = CType(resources.GetObject("btnDeviceInfo.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDeviceInfo.tbShowNew = False
        Me.btnDeviceInfo.tbShowProgress = False
        Me.btnDeviceInfo.tbShowTip = True
        Me.btnDeviceInfo.tbShowToolTipOnButton = False
        Me.btnDeviceInfo.tbSplit = "0,0,0,0"
        Me.btnDeviceInfo.tbText = ""
        Me.btnDeviceInfo.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeviceInfo.tbTextColor = System.Drawing.Color.Black
        Me.btnDeviceInfo.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnDeviceInfo.tbTextColorDown = System.Drawing.Color.Black
        Me.btnDeviceInfo.tbTextColorHover = System.Drawing.Color.Black
        Me.btnDeviceInfo.tbTextMouseDownPlace = 0
        Me.btnDeviceInfo.tbToolTip = ""
        Me.btnDeviceInfo.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDeviceInfo.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDeviceInfo.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeviceInfo.Visible = False
        Me.btnDeviceInfo.VisibleEx = True
        '
        'btnEditDeviceName
        '
        Me.btnEditDeviceName.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnEditDeviceName.BackColor = System.Drawing.Color.Transparent
        Me.btnEditDeviceName.BindingForm = Nothing
        Me.btnEditDeviceName.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnEditDeviceName.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnEditDeviceName.Location = New System.Drawing.Point(408, 35)
        Me.btnEditDeviceName.Name = "btnEditDeviceName"
        Me.btnEditDeviceName.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnEditDeviceName.Selectable = True
        Me.btnEditDeviceName.Size = New System.Drawing.Size(16, 16)
        Me.btnEditDeviceName.TabIndex = 64
        Me.btnEditDeviceName.TabStop = False
        Me.btnEditDeviceName.tbAdriftIconWhenHover = False
        Me.btnEditDeviceName.tbAutoSize = True
        Me.btnEditDeviceName.tbAutoSizeEx = False
        Me.btnEditDeviceName.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_4_edit
        Me.btnEditDeviceName.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnEditDeviceName.tbBadgeNumber = 0
        Me.btnEditDeviceName.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnEditDeviceName.tbEndEllipsis = False
        Me.btnEditDeviceName.tbIconHoldPlace = True
        Me.btnEditDeviceName.tbIconImage = Nothing
        Me.btnEditDeviceName.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnEditDeviceName.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnEditDeviceName.tbIconMore = False
        Me.btnEditDeviceName.tbIconMouseDown = Nothing
        Me.btnEditDeviceName.tbIconMouseHover = Nothing
        Me.btnEditDeviceName.tbIconMouseLeave = Nothing
        Me.btnEditDeviceName.tbIconPlaceText = 2
        Me.btnEditDeviceName.tbIconReadOnly = Nothing
        Me.btnEditDeviceName.tbImageMouseDown = Nothing
        Me.btnEditDeviceName.tbImageMouseHover = Nothing
        Me.btnEditDeviceName.tbImageMouseLeave = Nothing
        Me.btnEditDeviceName.tbProgressValue = 50
        Me.btnEditDeviceName.tbReadOnly = False
        Me.btnEditDeviceName.tbReadOnlyText = False
        Me.btnEditDeviceName.tbShadow = False
        Me.btnEditDeviceName.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnEditDeviceName.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnEditDeviceName.tbShowDot = False
        Me.btnEditDeviceName.tbShowMoreIconImg = CType(resources.GetObject("btnEditDeviceName.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnEditDeviceName.tbShowNew = False
        Me.btnEditDeviceName.tbShowProgress = False
        Me.btnEditDeviceName.tbShowTip = True
        Me.btnEditDeviceName.tbShowToolTipOnButton = False
        Me.btnEditDeviceName.tbSplit = "0,0,0,0"
        Me.btnEditDeviceName.tbText = ""
        Me.btnEditDeviceName.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnEditDeviceName.tbTextColor = System.Drawing.Color.Black
        Me.btnEditDeviceName.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnEditDeviceName.tbTextColorDown = System.Drawing.Color.Black
        Me.btnEditDeviceName.tbTextColorHover = System.Drawing.Color.Black
        Me.btnEditDeviceName.tbTextMouseDownPlace = 0
        Me.btnEditDeviceName.tbToolTip = ""
        Me.btnEditDeviceName.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnEditDeviceName.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnEditDeviceName.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnEditDeviceName.VisibleEx = True
        '
        'lblDeviceName
        '
        Me.lblDeviceName.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDeviceName.AutoEllipsis = True
        Me.lblDeviceName.AutoSize = True
        Me.lblDeviceName.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblDeviceName.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceName.Location = New System.Drawing.Point(322, 28)
        Me.lblDeviceName.Name = "lblDeviceName"
        Me.lblDeviceName.Size = New System.Drawing.Size(72, 16)
        Me.lblDeviceName.TabIndex = 62
        Me.lblDeviceName.tbAdriftWhenHover = False
        Me.lblDeviceName.tbAutoEllipsis = True
        Me.lblDeviceName.tbAutoSize = True
        Me.lblDeviceName.tbHideImage = False
        Me.lblDeviceName.tbIconImage = Nothing
        Me.lblDeviceName.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceName.tbIconPlaceText = 5
        Me.lblDeviceName.tbShadow = False
        Me.lblDeviceName.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceName.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceName.tbShowScrolling = False
        Me.lblDeviceName.Text = "我的手机"
        Me.lblDeviceName.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'txtDeviceName
        '
        Me.txtDeviceName.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtDeviceName.BackColor = System.Drawing.Color.White
        Me.txtDeviceName.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtDeviceName.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtDeviceName.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtDeviceName.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtDeviceName.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.txtDeviceName.ForeColor = System.Drawing.Color.Black
        Me.txtDeviceName.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDeviceName.Location = New System.Drawing.Point(322, -8)
        Me.txtDeviceName.Margin = New System.Windows.Forms.Padding(0)
        Me.txtDeviceName.MaxLength = 50
        Me.txtDeviceName.MinimumSize = New System.Drawing.Size(60, 33)
        Me.txtDeviceName.Name = "txtDeviceName"
        Me.txtDeviceName.Size = New System.Drawing.Size(213, 33)
        Me.txtDeviceName.TabIndex = 63
        Me.txtDeviceName.Tag = Nothing
        Me.txtDeviceName.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtDeviceName.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtDeviceName.tbSelMark = True
        Me.txtDeviceName.tbTextBind = "设备名称"
        Me.txtDeviceName.Text = "设备名称"
        Me.txtDeviceName.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtDeviceName.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtDeviceName.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtDeviceName.TextTip = ""
        Me.txtDeviceName.Visible = False
        '
        'btnRingtone
        '
        Me.btnRingtone.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnRingtone.BackColor = System.Drawing.Color.Transparent
        Me.btnRingtone.BindingForm = Nothing
        Me.btnRingtone.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnRingtone.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.btnRingtone.Location = New System.Drawing.Point(889, 252)
        Me.btnRingtone.Name = "btnRingtone"
        Me.btnRingtone.Padding = New System.Windows.Forms.Padding(20, 18, 10, 20)
        Me.btnRingtone.Selectable = True
        Me.btnRingtone.Size = New System.Drawing.Size(310, 95)
        Me.btnRingtone.TabIndex = 59
        Me.btnRingtone.TabStop = False
        Me.btnRingtone.tbAdriftIconWhenHover = False
        Me.btnRingtone.tbAutoSize = False
        Me.btnRingtone.tbAutoSizeEx = False
        Me.btnRingtone.tbBackgroundImage = Nothing
        Me.btnRingtone.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnRingtone.tbBadgeNumber = 0
        Me.btnRingtone.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRingtone.tbEndEllipsis = False
        Me.btnRingtone.tbIconHoldPlace = True
        Me.btnRingtone.tbIconImage = Global.iTong.My.Resources.Resources.summary_ring_53
        Me.btnRingtone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRingtone.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRingtone.tbIconMore = False
        Me.btnRingtone.tbIconMouseDown = Nothing
        Me.btnRingtone.tbIconMouseHover = Nothing
        Me.btnRingtone.tbIconMouseLeave = Nothing
        Me.btnRingtone.tbIconPlaceText = 23
        Me.btnRingtone.tbIconReadOnly = Nothing
        Me.btnRingtone.tbImageMouseDown = Nothing
        Me.btnRingtone.tbImageMouseHover = Global.iTong.My.Resources.Resources.summary_btn_1_hover
        Me.btnRingtone.tbImageMouseLeave = Nothing
        Me.btnRingtone.tbProgressValue = 50
        Me.btnRingtone.tbReadOnly = False
        Me.btnRingtone.tbReadOnlyText = False
        Me.btnRingtone.tbShadow = False
        Me.btnRingtone.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRingtone.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRingtone.tbShowDot = False
        Me.btnRingtone.tbShowMoreIconImg = CType(resources.GetObject("btnRingtone.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRingtone.tbShowNew = False
        Me.btnRingtone.tbShowProgress = False
        Me.btnRingtone.tbShowTip = True
        Me.btnRingtone.tbShowToolTipOnButton = True
        Me.btnRingtone.tbSplit = "3,3,3,3"
        Me.btnRingtone.tbText = "铃声制作"
        Me.btnRingtone.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRingtone.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnRingtone.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRingtone.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRingtone.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRingtone.tbTextMouseDownPlace = 0
        Me.btnRingtone.tbToolTip = "DIY手机铃声，一键导入设备"
        Me.btnRingtone.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnRingtone.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRingtone.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRingtone.VisibleEx = True
        '
        'btniOSDowngrade
        '
        Me.btniOSDowngrade.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btniOSDowngrade.BackColor = System.Drawing.Color.Transparent
        Me.btniOSDowngrade.BindingForm = Nothing
        Me.btniOSDowngrade.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btniOSDowngrade.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.btniOSDowngrade.Location = New System.Drawing.Point(907, 353)
        Me.btniOSDowngrade.Name = "btniOSDowngrade"
        Me.btniOSDowngrade.Padding = New System.Windows.Forms.Padding(20, 18, 10, 20)
        Me.btniOSDowngrade.Selectable = True
        Me.btniOSDowngrade.Size = New System.Drawing.Size(310, 95)
        Me.btniOSDowngrade.TabIndex = 58
        Me.btniOSDowngrade.TabStop = False
        Me.btniOSDowngrade.tbAdriftIconWhenHover = False
        Me.btniOSDowngrade.tbAutoSize = False
        Me.btniOSDowngrade.tbAutoSizeEx = False
        Me.btniOSDowngrade.tbBackgroundImage = Nothing
        Me.btniOSDowngrade.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btniOSDowngrade.tbBadgeNumber = 0
        Me.btniOSDowngrade.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniOSDowngrade.tbEndEllipsis = False
        Me.btniOSDowngrade.tbIconHoldPlace = True
        Me.btniOSDowngrade.tbIconImage = Global.iTong.My.Resources.Resources.summary_brush_53
        Me.btniOSDowngrade.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniOSDowngrade.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniOSDowngrade.tbIconMore = False
        Me.btniOSDowngrade.tbIconMouseDown = Nothing
        Me.btniOSDowngrade.tbIconMouseHover = Nothing
        Me.btniOSDowngrade.tbIconMouseLeave = Nothing
        Me.btniOSDowngrade.tbIconPlaceText = 23
        Me.btniOSDowngrade.tbIconReadOnly = Nothing
        Me.btniOSDowngrade.tbImageMouseDown = Nothing
        Me.btniOSDowngrade.tbImageMouseHover = Global.iTong.My.Resources.Resources.summary_btn_1_hover
        Me.btniOSDowngrade.tbImageMouseLeave = Nothing
        Me.btniOSDowngrade.tbProgressValue = 50
        Me.btniOSDowngrade.tbReadOnly = False
        Me.btniOSDowngrade.tbReadOnlyText = False
        Me.btniOSDowngrade.tbShadow = False
        Me.btniOSDowngrade.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btniOSDowngrade.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btniOSDowngrade.tbShowDot = False
        Me.btniOSDowngrade.tbShowMoreIconImg = CType(resources.GetObject("btniOSDowngrade.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btniOSDowngrade.tbShowNew = False
        Me.btniOSDowngrade.tbShowProgress = False
        Me.btniOSDowngrade.tbShowTip = True
        Me.btniOSDowngrade.tbShowToolTipOnButton = True
        Me.btniOSDowngrade.tbSplit = "3,3,3,3"
        Me.btniOSDowngrade.tbText = "iOS系统升级降级"
        Me.btniOSDowngrade.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniOSDowngrade.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btniOSDowngrade.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btniOSDowngrade.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btniOSDowngrade.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btniOSDowngrade.tbTextMouseDownPlace = 0
        Me.btniOSDowngrade.tbToolTip = "请先备份好您的数据"
        Me.btniOSDowngrade.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniOSDowngrade.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniOSDowngrade.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniOSDowngrade.Visible = False
        Me.btniOSDowngrade.VisibleEx = True
        '
        'btnWechat
        '
        Me.btnWechat.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnWechat.BackColor = System.Drawing.Color.Transparent
        Me.btnWechat.BindingForm = Nothing
        Me.btnWechat.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnWechat.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.btnWechat.Location = New System.Drawing.Point(889, 52)
        Me.btnWechat.Name = "btnWechat"
        Me.btnWechat.Padding = New System.Windows.Forms.Padding(20, 10, 10, 10)
        Me.btnWechat.Selectable = True
        Me.btnWechat.Size = New System.Drawing.Size(310, 95)
        Me.btnWechat.TabIndex = 57
        Me.btnWechat.TabStop = False
        Me.btnWechat.tbAdriftIconWhenHover = False
        Me.btnWechat.tbAutoSize = False
        Me.btnWechat.tbAutoSizeEx = False
        Me.btnWechat.tbBackgroundImage = Nothing
        Me.btnWechat.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnWechat.tbBadgeNumber = 0
        Me.btnWechat.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnWechat.tbEndEllipsis = False
        Me.btnWechat.tbIconHoldPlace = True
        Me.btnWechat.tbIconImage = Global.iTong.My.Resources.Resources.summary_weixin_53
        Me.btnWechat.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnWechat.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnWechat.tbIconMore = False
        Me.btnWechat.tbIconMouseDown = Nothing
        Me.btnWechat.tbIconMouseHover = Nothing
        Me.btnWechat.tbIconMouseLeave = Nothing
        Me.btnWechat.tbIconPlaceText = 23
        Me.btnWechat.tbIconReadOnly = Nothing
        Me.btnWechat.tbImageMouseDown = Nothing
        Me.btnWechat.tbImageMouseHover = Global.iTong.My.Resources.Resources.summary_btn_1_hover
        Me.btnWechat.tbImageMouseLeave = Nothing
        Me.btnWechat.tbProgressValue = 50
        Me.btnWechat.tbReadOnly = False
        Me.btnWechat.tbReadOnlyText = False
        Me.btnWechat.tbShadow = False
        Me.btnWechat.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnWechat.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnWechat.tbShowDot = False
        Me.btnWechat.tbShowMoreIconImg = CType(resources.GetObject("btnWechat.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnWechat.tbShowNew = False
        Me.btnWechat.tbShowProgress = False
        Me.btnWechat.tbShowTip = True
        Me.btnWechat.tbShowToolTipOnButton = True
        Me.btnWechat.tbSplit = "3,3,3,3"
        Me.btnWechat.tbText = "微信聊天记录恢复"
        Me.btnWechat.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnWechat.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnWechat.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnWechat.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnWechat.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnWechat.tbTextMouseDownPlace = 0
        Me.btnWechat.tbToolTip = "两种模式：提供扫描设备恢复，微信单独备份恢复"
        Me.btnWechat.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnWechat.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWechat.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnWechat.VisibleEx = True
        '
        'pnlDeviceInfo
        '
        Me.pnlDeviceInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlDeviceInfo.Controls.Add(Me.btnMore)
        Me.pnlDeviceInfo.Controls.Add(Me.lblChargingDetail)
        Me.pnlDeviceInfo.Controls.Add(Me.lblCloseiOSUpdate)
        Me.pnlDeviceInfo.Controls.Add(Me.lblDeviceIdentifyValue)
        Me.pnlDeviceInfo.Controls.Add(Me.lblIMEIValue)
        Me.pnlDeviceInfo.Controls.Add(Me.lblSNValue)
        Me.pnlDeviceInfo.Controls.Add(Me.cbxCloseiTunesStart)
        Me.pnlDeviceInfo.Controls.Add(Me.lblDeviceIdentify)
        Me.pnlDeviceInfo.Controls.Add(Me.lblChargingPercentValue)
        Me.pnlDeviceInfo.Controls.Add(Me.lblChargingPercent)
        Me.pnlDeviceInfo.Controls.Add(Me.lblChargingTimesValue)
        Me.pnlDeviceInfo.Controls.Add(Me.lblChargingTimes)
        Me.pnlDeviceInfo.Controls.Add(Me.lblGuaranteeDateValue)
        Me.pnlDeviceInfo.Controls.Add(Me.lblGuaranteeDate)
        Me.pnlDeviceInfo.Controls.Add(Me.lblProductDateValue)
        Me.pnlDeviceInfo.Controls.Add(Me.lblProductDate)
        Me.pnlDeviceInfo.Controls.Add(Me.lblSaleAreaValue)
        Me.pnlDeviceInfo.Controls.Add(Me.lblSaleArea)
        Me.pnlDeviceInfo.Controls.Add(Me.lblIMEI)
        Me.pnlDeviceInfo.Controls.Add(Me.lblSN)
        Me.pnlDeviceInfo.Controls.Add(Me.lbliOSVersionValue)
        Me.pnlDeviceInfo.Controls.Add(Me.lbliOSVersion)
        Me.pnlDeviceInfo.Controls.Add(Me.lblDeviceColor)
        Me.pnlDeviceInfo.Controls.Add(Me.btnAppeidBinding)
        Me.pnlDeviceInfo.Controls.Add(Me.btnInstallTui)
        Me.pnlDeviceInfo.Controls.Add(Me.btnAuthDevice)
        Me.pnlDeviceInfo.Location = New System.Drawing.Point(580, 43)
        Me.pnlDeviceInfo.Name = "pnlDeviceInfo"
        Me.pnlDeviceInfo.Size = New System.Drawing.Size(294, 413)
        Me.pnlDeviceInfo.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDeviceInfo.TabIndex = 56
        Me.pnlDeviceInfo.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_info
        Me.pnlDeviceInfo.tbShowWatermark = False
        Me.pnlDeviceInfo.tbSplit = "10,10,10,10"
        Me.pnlDeviceInfo.tbWatermark = Nothing
        Me.pnlDeviceInfo.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDeviceInfo.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnMore
        '
        Me.btnMore.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnMore.BackColor = System.Drawing.Color.Transparent
        Me.btnMore.BindingForm = Nothing
        Me.btnMore.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnMore.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnMore.Location = New System.Drawing.Point(198, 336)
        Me.btnMore.Name = "btnMore"
        Me.btnMore.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnMore.Selectable = True
        Me.btnMore.Size = New System.Drawing.Size(74, 23)
        Me.btnMore.TabIndex = 93
        Me.btnMore.tbAdriftIconWhenHover = False
        Me.btnMore.tbAutoSize = False
        Me.btnMore.tbAutoSizeEx = False
        Me.btnMore.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnMore.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnMore.tbBadgeNumber = 0
        Me.btnMore.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnMore.tbEndEllipsis = False
        Me.btnMore.tbIconHoldPlace = True
        Me.btnMore.tbIconImage = Nothing
        Me.btnMore.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMore.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnMore.tbIconMore = False
        Me.btnMore.tbIconMouseDown = Nothing
        Me.btnMore.tbIconMouseHover = Nothing
        Me.btnMore.tbIconMouseLeave = Nothing
        Me.btnMore.tbIconPlaceText = 2
        Me.btnMore.tbIconReadOnly = Nothing
        Me.btnMore.tbImageMouseDown = Nothing
        Me.btnMore.tbImageMouseHover = Nothing
        Me.btnMore.tbImageMouseLeave = Nothing
        Me.btnMore.tbProgressValue = 50
        Me.btnMore.tbReadOnly = False
        Me.btnMore.tbReadOnlyText = False
        Me.btnMore.tbShadow = False
        Me.btnMore.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnMore.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnMore.tbShowDot = False
        Me.btnMore.tbShowMoreIconImg = CType(resources.GetObject("btnMore.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnMore.tbShowNew = False
        Me.btnMore.tbShowProgress = False
        Me.btnMore.tbShowTip = True
        Me.btnMore.tbShowToolTipOnButton = False
        Me.btnMore.tbSplit = "13,11,13,11"
        Me.btnMore.tbText = "取消"
        Me.btnMore.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMore.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnMore.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnMore.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnMore.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnMore.tbTextMouseDownPlace = 0
        Me.btnMore.tbToolTip = ""
        Me.btnMore.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnMore.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnMore.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMore.VisibleEx = True
        '
        'lblChargingDetail
        '
        Me.lblChargingDetail.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(190, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblChargingDetail.AutoSize = True
        Me.lblChargingDetail.BackColor = System.Drawing.Color.Transparent
        Me.lblChargingDetail.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblChargingDetail.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblChargingDetail.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblChargingDetail.Location = New System.Drawing.Point(222, 275)
        Me.lblChargingDetail.Name = "lblChargingDetail"
        Me.lblChargingDetail.Size = New System.Drawing.Size(53, 12)
        Me.lblChargingDetail.TabIndex = 92
        Me.lblChargingDetail.TabStop = True
        Me.lblChargingDetail.Text = "电池详情"
        '
        'lblCloseiOSUpdate
        '
        Me.lblCloseiOSUpdate.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(190, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblCloseiOSUpdate.AutoSize = True
        Me.lblCloseiOSUpdate.BackColor = System.Drawing.Color.Transparent
        Me.lblCloseiOSUpdate.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblCloseiOSUpdate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblCloseiOSUpdate.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblCloseiOSUpdate.Location = New System.Drawing.Point(206, 51)
        Me.lblCloseiOSUpdate.Name = "lblCloseiOSUpdate"
        Me.lblCloseiOSUpdate.Size = New System.Drawing.Size(71, 12)
        Me.lblCloseiOSUpdate.TabIndex = 91
        Me.lblCloseiOSUpdate.TabStop = True
        Me.lblCloseiOSUpdate.Text = "关闭iOS更新"
        '
        'lblDeviceIdentifyValue
        '
        Me.lblDeviceIdentifyValue.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDeviceIdentifyValue.BackColor = System.Drawing.Color.Transparent
        Me.lblDeviceIdentifyValue.BindingForm = Nothing
        Me.lblDeviceIdentifyValue.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.lblDeviceIdentifyValue.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDeviceIdentifyValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceIdentifyValue.Location = New System.Drawing.Point(120, 305)
        Me.lblDeviceIdentifyValue.Margin = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.lblDeviceIdentifyValue.Name = "lblDeviceIdentifyValue"
        Me.lblDeviceIdentifyValue.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.lblDeviceIdentifyValue.Selectable = True
        Me.lblDeviceIdentifyValue.Size = New System.Drawing.Size(163, 21)
        Me.lblDeviceIdentifyValue.TabIndex = 90
        Me.lblDeviceIdentifyValue.tbAdriftIconWhenHover = False
        Me.lblDeviceIdentifyValue.tbAutoSize = False
        Me.lblDeviceIdentifyValue.tbAutoSizeEx = False
        Me.lblDeviceIdentifyValue.tbBackgroundImage = Nothing
        Me.lblDeviceIdentifyValue.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.lblDeviceIdentifyValue.tbBadgeNumber = 0
        Me.lblDeviceIdentifyValue.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.lblDeviceIdentifyValue.tbEndEllipsis = True
        Me.lblDeviceIdentifyValue.tbIconHoldPlace = True
        Me.lblDeviceIdentifyValue.tbIconImage = Nothing
        Me.lblDeviceIdentifyValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDeviceIdentifyValue.tbIconImageState = iTong.Components.ImageState.OneState
        Me.lblDeviceIdentifyValue.tbIconMore = False
        Me.lblDeviceIdentifyValue.tbIconMouseDown = Nothing
        Me.lblDeviceIdentifyValue.tbIconMouseHover = Nothing
        Me.lblDeviceIdentifyValue.tbIconMouseLeave = Nothing
        Me.lblDeviceIdentifyValue.tbIconPlaceText = 2
        Me.lblDeviceIdentifyValue.tbIconReadOnly = Nothing
        Me.lblDeviceIdentifyValue.tbImageMouseDown = Nothing
        Me.lblDeviceIdentifyValue.tbImageMouseHover = Nothing
        Me.lblDeviceIdentifyValue.tbImageMouseLeave = Nothing
        Me.lblDeviceIdentifyValue.tbProgressValue = 50
        Me.lblDeviceIdentifyValue.tbReadOnly = False
        Me.lblDeviceIdentifyValue.tbReadOnlyText = False
        Me.lblDeviceIdentifyValue.tbShadow = False
        Me.lblDeviceIdentifyValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceIdentifyValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceIdentifyValue.tbShowDot = False
        Me.lblDeviceIdentifyValue.tbShowMoreIconImg = CType(resources.GetObject("lblDeviceIdentifyValue.tbShowMoreIconImg"), System.Drawing.Image)
        Me.lblDeviceIdentifyValue.tbShowNew = False
        Me.lblDeviceIdentifyValue.tbShowProgress = False
        Me.lblDeviceIdentifyValue.tbShowTip = True
        Me.lblDeviceIdentifyValue.tbShowToolTipOnButton = False
        Me.lblDeviceIdentifyValue.tbSplit = "13,11,13,11"
        Me.lblDeviceIdentifyValue.tbText = "2csef454215wefwef215gerge655er"
        Me.lblDeviceIdentifyValue.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceIdentifyValue.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceIdentifyValue.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceIdentifyValue.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceIdentifyValue.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblDeviceIdentifyValue.tbTextMouseDownPlace = 0
        Me.lblDeviceIdentifyValue.tbToolTip = ""
        Me.lblDeviceIdentifyValue.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.lblDeviceIdentifyValue.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDeviceIdentifyValue.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceIdentifyValue.VisibleEx = True
        '
        'lblIMEIValue
        '
        Me.lblIMEIValue.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblIMEIValue.BackColor = System.Drawing.Color.Transparent
        Me.lblIMEIValue.BindingForm = Nothing
        Me.lblIMEIValue.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.lblIMEIValue.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblIMEIValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblIMEIValue.Location = New System.Drawing.Point(120, 113)
        Me.lblIMEIValue.Margin = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.lblIMEIValue.Name = "lblIMEIValue"
        Me.lblIMEIValue.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.lblIMEIValue.Selectable = True
        Me.lblIMEIValue.Size = New System.Drawing.Size(99, 21)
        Me.lblIMEIValue.TabIndex = 89
        Me.lblIMEIValue.tbAdriftIconWhenHover = False
        Me.lblIMEIValue.tbAutoSize = True
        Me.lblIMEIValue.tbAutoSizeEx = True
        Me.lblIMEIValue.tbBackgroundImage = Nothing
        Me.lblIMEIValue.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.lblIMEIValue.tbBadgeNumber = 0
        Me.lblIMEIValue.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.lblIMEIValue.tbEndEllipsis = False
        Me.lblIMEIValue.tbIconHoldPlace = True
        Me.lblIMEIValue.tbIconImage = Nothing
        Me.lblIMEIValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblIMEIValue.tbIconImageState = iTong.Components.ImageState.OneState
        Me.lblIMEIValue.tbIconMore = False
        Me.lblIMEIValue.tbIconMouseDown = Nothing
        Me.lblIMEIValue.tbIconMouseHover = Nothing
        Me.lblIMEIValue.tbIconMouseLeave = Nothing
        Me.lblIMEIValue.tbIconPlaceText = 2
        Me.lblIMEIValue.tbIconReadOnly = Nothing
        Me.lblIMEIValue.tbImageMouseDown = Nothing
        Me.lblIMEIValue.tbImageMouseHover = Nothing
        Me.lblIMEIValue.tbImageMouseLeave = Nothing
        Me.lblIMEIValue.tbProgressValue = 50
        Me.lblIMEIValue.tbReadOnly = False
        Me.lblIMEIValue.tbReadOnlyText = False
        Me.lblIMEIValue.tbShadow = False
        Me.lblIMEIValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblIMEIValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblIMEIValue.tbShowDot = False
        Me.lblIMEIValue.tbShowMoreIconImg = CType(resources.GetObject("lblIMEIValue.tbShowMoreIconImg"), System.Drawing.Image)
        Me.lblIMEIValue.tbShowNew = False
        Me.lblIMEIValue.tbShowProgress = False
        Me.lblIMEIValue.tbShowTip = True
        Me.lblIMEIValue.tbShowToolTipOnButton = False
        Me.lblIMEIValue.tbSplit = "13,11,13,11"
        Me.lblIMEIValue.tbText = "54654546523112"
        Me.lblIMEIValue.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblIMEIValue.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblIMEIValue.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblIMEIValue.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblIMEIValue.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblIMEIValue.tbTextMouseDownPlace = 0
        Me.lblIMEIValue.tbToolTip = ""
        Me.lblIMEIValue.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.lblIMEIValue.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.lblIMEIValue.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblIMEIValue.VisibleEx = True
        '
        'lblSNValue
        '
        Me.lblSNValue.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSNValue.BackColor = System.Drawing.Color.Transparent
        Me.lblSNValue.BindingForm = Nothing
        Me.lblSNValue.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.lblSNValue.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblSNValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblSNValue.Location = New System.Drawing.Point(120, 81)
        Me.lblSNValue.Margin = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.lblSNValue.Name = "lblSNValue"
        Me.lblSNValue.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.lblSNValue.Selectable = True
        Me.lblSNValue.Size = New System.Drawing.Size(81, 21)
        Me.lblSNValue.TabIndex = 88
        Me.lblSNValue.tbAdriftIconWhenHover = False
        Me.lblSNValue.tbAutoSize = True
        Me.lblSNValue.tbAutoSizeEx = True
        Me.lblSNValue.tbBackgroundImage = Nothing
        Me.lblSNValue.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.lblSNValue.tbBadgeNumber = 0
        Me.lblSNValue.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.lblSNValue.tbEndEllipsis = False
        Me.lblSNValue.tbIconHoldPlace = True
        Me.lblSNValue.tbIconImage = Nothing
        Me.lblSNValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblSNValue.tbIconImageState = iTong.Components.ImageState.OneState
        Me.lblSNValue.tbIconMore = False
        Me.lblSNValue.tbIconMouseDown = Nothing
        Me.lblSNValue.tbIconMouseHover = Nothing
        Me.lblSNValue.tbIconMouseLeave = Nothing
        Me.lblSNValue.tbIconPlaceText = 2
        Me.lblSNValue.tbIconReadOnly = Nothing
        Me.lblSNValue.tbImageMouseDown = Nothing
        Me.lblSNValue.tbImageMouseHover = Nothing
        Me.lblSNValue.tbImageMouseLeave = Nothing
        Me.lblSNValue.tbProgressValue = 50
        Me.lblSNValue.tbReadOnly = False
        Me.lblSNValue.tbReadOnlyText = False
        Me.lblSNValue.tbShadow = False
        Me.lblSNValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSNValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSNValue.tbShowDot = False
        Me.lblSNValue.tbShowMoreIconImg = CType(resources.GetObject("lblSNValue.tbShowMoreIconImg"), System.Drawing.Image)
        Me.lblSNValue.tbShowNew = False
        Me.lblSNValue.tbShowProgress = False
        Me.lblSNValue.tbShowTip = True
        Me.lblSNValue.tbShowToolTipOnButton = False
        Me.lblSNValue.tbSplit = "13,11,13,11"
        Me.lblSNValue.tbText = "F554ASFDFEF"
        Me.lblSNValue.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblSNValue.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblSNValue.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblSNValue.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblSNValue.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblSNValue.tbTextMouseDownPlace = 0
        Me.lblSNValue.tbToolTip = ""
        Me.lblSNValue.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.lblSNValue.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.lblSNValue.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSNValue.VisibleEx = True
        '
        'cbxCloseiTunesStart
        '
        Me.cbxCloseiTunesStart.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxCloseiTunesStart.BackColor = System.Drawing.Color.Transparent
        Me.cbxCloseiTunesStart.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cbxCloseiTunesStart.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.cbxCloseiTunesStart.Location = New System.Drawing.Point(24, 335)
        Me.cbxCloseiTunesStart.Name = "cbxCloseiTunesStart"
        Me.cbxCloseiTunesStart.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxCloseiTunesStart.Size = New System.Drawing.Size(178, 24)
        Me.cbxCloseiTunesStart.TabIndex = 85
        Me.cbxCloseiTunesStart.tbAdriftIconWhenHover = False
        Me.cbxCloseiTunesStart.tbAutoSize = False
        Me.cbxCloseiTunesStart.tbAutoSizeEx = False
        Me.cbxCloseiTunesStart.tbIconChecked = CType(resources.GetObject("cbxCloseiTunesStart.tbIconChecked"), System.Drawing.Image)
        Me.cbxCloseiTunesStart.tbIconCheckedMouseDown = Nothing
        Me.cbxCloseiTunesStart.tbIconCheckedMouseHover = Nothing
        Me.cbxCloseiTunesStart.tbIconCheckedMouseLeave = Nothing
        Me.cbxCloseiTunesStart.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxCloseiTunesStart.tbIconHoldPlace = True
        Me.cbxCloseiTunesStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxCloseiTunesStart.tbIconIndeterminate = CType(resources.GetObject("cbxCloseiTunesStart.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxCloseiTunesStart.tbIconIndeterminateMouseDown = Nothing
        Me.cbxCloseiTunesStart.tbIconIndeterminateMouseHover = Nothing
        Me.cbxCloseiTunesStart.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxCloseiTunesStart.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxCloseiTunesStart.tbIconPlaceText = 1
        Me.cbxCloseiTunesStart.tbIconUnChecked = CType(resources.GetObject("cbxCloseiTunesStart.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxCloseiTunesStart.tbIconUnCheckedMouseDown = Nothing
        Me.cbxCloseiTunesStart.tbIconUnCheckedMouseHover = Nothing
        Me.cbxCloseiTunesStart.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxCloseiTunesStart.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxCloseiTunesStart.tbImageBackground = Nothing
        Me.cbxCloseiTunesStart.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxCloseiTunesStart.tbImageCheckedMouseDown = Nothing
        Me.cbxCloseiTunesStart.tbImageCheckedMouseHover = Nothing
        Me.cbxCloseiTunesStart.tbImageCheckedMouseLeave = Nothing
        Me.cbxCloseiTunesStart.tbImageUnCheckedMouseDown = Nothing
        Me.cbxCloseiTunesStart.tbImageUnCheckedMouseHover = Nothing
        Me.cbxCloseiTunesStart.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxCloseiTunesStart.tbReadOnly = False
        Me.cbxCloseiTunesStart.tbShadow = False
        Me.cbxCloseiTunesStart.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxCloseiTunesStart.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxCloseiTunesStart.tbSplit = "3,3,3,3"
        Me.cbxCloseiTunesStart.tbToolTip = ""
        Me.cbxCloseiTunesStart.Text = "阻止iTunes自启动"
        Me.cbxCloseiTunesStart.UseVisualStyleBackColor = False
        '
        'lblDeviceIdentify
        '
        Me.lblDeviceIdentify.AutoSize = True
        Me.lblDeviceIdentify.BackColor = System.Drawing.Color.Transparent
        Me.lblDeviceIdentify.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblDeviceIdentify.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblDeviceIdentify.Location = New System.Drawing.Point(24, 307)
        Me.lblDeviceIdentify.Name = "lblDeviceIdentify"
        Me.lblDeviceIdentify.Size = New System.Drawing.Size(53, 12)
        Me.lblDeviceIdentify.TabIndex = 83
        Me.lblDeviceIdentify.tbAdriftWhenHover = False
        Me.lblDeviceIdentify.tbAutoEllipsis = False
        Me.lblDeviceIdentify.tbAutoSize = True
        Me.lblDeviceIdentify.tbHideImage = False
        Me.lblDeviceIdentify.tbIconImage = Nothing
        Me.lblDeviceIdentify.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceIdentify.tbIconPlaceText = 5
        Me.lblDeviceIdentify.tbShadow = False
        Me.lblDeviceIdentify.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceIdentify.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceIdentify.tbShowScrolling = False
        Me.lblDeviceIdentify.Text = "设备标识"
        '
        'lblChargingPercentValue
        '
        Me.lblChargingPercentValue.AutoSize = True
        Me.lblChargingPercentValue.BackColor = System.Drawing.Color.Transparent
        Me.lblChargingPercentValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblChargingPercentValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblChargingPercentValue.Location = New System.Drawing.Point(125, 275)
        Me.lblChargingPercentValue.Name = "lblChargingPercentValue"
        Me.lblChargingPercentValue.Size = New System.Drawing.Size(23, 12)
        Me.lblChargingPercentValue.TabIndex = 82
        Me.lblChargingPercentValue.tbAdriftWhenHover = False
        Me.lblChargingPercentValue.tbAutoEllipsis = False
        Me.lblChargingPercentValue.tbAutoSize = True
        Me.lblChargingPercentValue.tbHideImage = False
        Me.lblChargingPercentValue.tbIconImage = Nothing
        Me.lblChargingPercentValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblChargingPercentValue.tbIconPlaceText = 5
        Me.lblChargingPercentValue.tbShadow = False
        Me.lblChargingPercentValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblChargingPercentValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblChargingPercentValue.tbShowScrolling = False
        Me.lblChargingPercentValue.Text = "90%"
        '
        'lblChargingPercent
        '
        Me.lblChargingPercent.AutoSize = True
        Me.lblChargingPercent.BackColor = System.Drawing.Color.Transparent
        Me.lblChargingPercent.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblChargingPercent.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblChargingPercent.Location = New System.Drawing.Point(24, 275)
        Me.lblChargingPercent.Name = "lblChargingPercent"
        Me.lblChargingPercent.Size = New System.Drawing.Size(53, 12)
        Me.lblChargingPercent.TabIndex = 81
        Me.lblChargingPercent.tbAdriftWhenHover = False
        Me.lblChargingPercent.tbAutoEllipsis = False
        Me.lblChargingPercent.tbAutoSize = True
        Me.lblChargingPercent.tbHideImage = False
        Me.lblChargingPercent.tbIconImage = Nothing
        Me.lblChargingPercent.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblChargingPercent.tbIconPlaceText = 5
        Me.lblChargingPercent.tbShadow = False
        Me.lblChargingPercent.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblChargingPercent.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblChargingPercent.tbShowScrolling = False
        Me.lblChargingPercent.Text = "电池寿命"
        '
        'lblChargingTimesValue
        '
        Me.lblChargingTimesValue.AutoSize = True
        Me.lblChargingTimesValue.BackColor = System.Drawing.Color.Transparent
        Me.lblChargingTimesValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblChargingTimesValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblChargingTimesValue.Location = New System.Drawing.Point(125, 243)
        Me.lblChargingTimesValue.Name = "lblChargingTimesValue"
        Me.lblChargingTimesValue.Size = New System.Drawing.Size(23, 12)
        Me.lblChargingTimesValue.TabIndex = 80
        Me.lblChargingTimesValue.tbAdriftWhenHover = False
        Me.lblChargingTimesValue.tbAutoEllipsis = False
        Me.lblChargingTimesValue.tbAutoSize = True
        Me.lblChargingTimesValue.tbHideImage = False
        Me.lblChargingTimesValue.tbIconImage = Nothing
        Me.lblChargingTimesValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblChargingTimesValue.tbIconPlaceText = 5
        Me.lblChargingTimesValue.tbShadow = False
        Me.lblChargingTimesValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblChargingTimesValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblChargingTimesValue.tbShowScrolling = False
        Me.lblChargingTimesValue.Text = "150"
        '
        'lblChargingTimes
        '
        Me.lblChargingTimes.AutoSize = True
        Me.lblChargingTimes.BackColor = System.Drawing.Color.Transparent
        Me.lblChargingTimes.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblChargingTimes.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblChargingTimes.Location = New System.Drawing.Point(24, 243)
        Me.lblChargingTimes.Name = "lblChargingTimes"
        Me.lblChargingTimes.Size = New System.Drawing.Size(53, 12)
        Me.lblChargingTimes.TabIndex = 79
        Me.lblChargingTimes.tbAdriftWhenHover = False
        Me.lblChargingTimes.tbAutoEllipsis = False
        Me.lblChargingTimes.tbAutoSize = True
        Me.lblChargingTimes.tbHideImage = False
        Me.lblChargingTimes.tbIconImage = Nothing
        Me.lblChargingTimes.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblChargingTimes.tbIconPlaceText = 5
        Me.lblChargingTimes.tbShadow = False
        Me.lblChargingTimes.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblChargingTimes.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblChargingTimes.tbShowScrolling = False
        Me.lblChargingTimes.Text = "充电次数"
        '
        'lblGuaranteeDateValue
        '
        Me.lblGuaranteeDateValue.AutoSize = True
        Me.lblGuaranteeDateValue.BackColor = System.Drawing.Color.Transparent
        Me.lblGuaranteeDateValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblGuaranteeDateValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblGuaranteeDateValue.Location = New System.Drawing.Point(125, 211)
        Me.lblGuaranteeDateValue.Name = "lblGuaranteeDateValue"
        Me.lblGuaranteeDateValue.Size = New System.Drawing.Size(53, 12)
        Me.lblGuaranteeDateValue.TabIndex = 78
        Me.lblGuaranteeDateValue.tbAdriftWhenHover = False
        Me.lblGuaranteeDateValue.tbAutoEllipsis = False
        Me.lblGuaranteeDateValue.tbAutoSize = True
        Me.lblGuaranteeDateValue.tbHideImage = False
        Me.lblGuaranteeDateValue.tbIconImage = Nothing
        Me.lblGuaranteeDateValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblGuaranteeDateValue.tbIconPlaceText = 5
        Me.lblGuaranteeDateValue.tbShadow = False
        Me.lblGuaranteeDateValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblGuaranteeDateValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblGuaranteeDateValue.tbShowScrolling = False
        Me.lblGuaranteeDateValue.Text = "2018.1.1"
        '
        'lblGuaranteeDate
        '
        Me.lblGuaranteeDate.AutoSize = True
        Me.lblGuaranteeDate.BackColor = System.Drawing.Color.Transparent
        Me.lblGuaranteeDate.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblGuaranteeDate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblGuaranteeDate.Location = New System.Drawing.Point(24, 211)
        Me.lblGuaranteeDate.Name = "lblGuaranteeDate"
        Me.lblGuaranteeDate.Size = New System.Drawing.Size(53, 12)
        Me.lblGuaranteeDate.TabIndex = 77
        Me.lblGuaranteeDate.tbAdriftWhenHover = False
        Me.lblGuaranteeDate.tbAutoEllipsis = False
        Me.lblGuaranteeDate.tbAutoSize = True
        Me.lblGuaranteeDate.tbHideImage = False
        Me.lblGuaranteeDate.tbIconImage = Nothing
        Me.lblGuaranteeDate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblGuaranteeDate.tbIconPlaceText = 5
        Me.lblGuaranteeDate.tbShadow = False
        Me.lblGuaranteeDate.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblGuaranteeDate.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblGuaranteeDate.tbShowScrolling = False
        Me.lblGuaranteeDate.Text = "保修到期"
        '
        'lblProductDateValue
        '
        Me.lblProductDateValue.AutoSize = True
        Me.lblProductDateValue.BackColor = System.Drawing.Color.Transparent
        Me.lblProductDateValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblProductDateValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblProductDateValue.Location = New System.Drawing.Point(125, 179)
        Me.lblProductDateValue.Name = "lblProductDateValue"
        Me.lblProductDateValue.Size = New System.Drawing.Size(53, 12)
        Me.lblProductDateValue.TabIndex = 76
        Me.lblProductDateValue.tbAdriftWhenHover = False
        Me.lblProductDateValue.tbAutoEllipsis = False
        Me.lblProductDateValue.tbAutoSize = True
        Me.lblProductDateValue.tbHideImage = False
        Me.lblProductDateValue.tbIconImage = Nothing
        Me.lblProductDateValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblProductDateValue.tbIconPlaceText = 5
        Me.lblProductDateValue.tbShadow = False
        Me.lblProductDateValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblProductDateValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblProductDateValue.tbShowScrolling = False
        Me.lblProductDateValue.Text = "2016.1.1"
        '
        'lblProductDate
        '
        Me.lblProductDate.AutoSize = True
        Me.lblProductDate.BackColor = System.Drawing.Color.Transparent
        Me.lblProductDate.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblProductDate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblProductDate.Location = New System.Drawing.Point(24, 179)
        Me.lblProductDate.Name = "lblProductDate"
        Me.lblProductDate.Size = New System.Drawing.Size(53, 12)
        Me.lblProductDate.TabIndex = 75
        Me.lblProductDate.tbAdriftWhenHover = False
        Me.lblProductDate.tbAutoEllipsis = False
        Me.lblProductDate.tbAutoSize = True
        Me.lblProductDate.tbHideImage = False
        Me.lblProductDate.tbIconImage = Nothing
        Me.lblProductDate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblProductDate.tbIconPlaceText = 5
        Me.lblProductDate.tbShadow = False
        Me.lblProductDate.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblProductDate.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblProductDate.tbShowScrolling = False
        Me.lblProductDate.Text = "生产日期"
        '
        'lblSaleAreaValue
        '
        Me.lblSaleAreaValue.AutoSize = True
        Me.lblSaleAreaValue.BackColor = System.Drawing.Color.Transparent
        Me.lblSaleAreaValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblSaleAreaValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblSaleAreaValue.Location = New System.Drawing.Point(125, 147)
        Me.lblSaleAreaValue.Name = "lblSaleAreaValue"
        Me.lblSaleAreaValue.Size = New System.Drawing.Size(41, 12)
        Me.lblSaleAreaValue.TabIndex = 74
        Me.lblSaleAreaValue.tbAdriftWhenHover = False
        Me.lblSaleAreaValue.tbAutoEllipsis = False
        Me.lblSaleAreaValue.tbAutoSize = True
        Me.lblSaleAreaValue.tbHideImage = False
        Me.lblSaleAreaValue.tbIconImage = Nothing
        Me.lblSaleAreaValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSaleAreaValue.tbIconPlaceText = 5
        Me.lblSaleAreaValue.tbShadow = False
        Me.lblSaleAreaValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSaleAreaValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSaleAreaValue.tbShowScrolling = False
        Me.lblSaleAreaValue.Text = "unknow"
        '
        'lblSaleArea
        '
        Me.lblSaleArea.AutoSize = True
        Me.lblSaleArea.BackColor = System.Drawing.Color.Transparent
        Me.lblSaleArea.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblSaleArea.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblSaleArea.Location = New System.Drawing.Point(24, 147)
        Me.lblSaleArea.Name = "lblSaleArea"
        Me.lblSaleArea.Size = New System.Drawing.Size(53, 12)
        Me.lblSaleArea.TabIndex = 73
        Me.lblSaleArea.tbAdriftWhenHover = False
        Me.lblSaleArea.tbAutoEllipsis = False
        Me.lblSaleArea.tbAutoSize = True
        Me.lblSaleArea.tbHideImage = False
        Me.lblSaleArea.tbIconImage = Nothing
        Me.lblSaleArea.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSaleArea.tbIconPlaceText = 5
        Me.lblSaleArea.tbShadow = False
        Me.lblSaleArea.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSaleArea.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSaleArea.tbShowScrolling = False
        Me.lblSaleArea.Text = "销售地区"
        '
        'lblIMEI
        '
        Me.lblIMEI.AutoSize = True
        Me.lblIMEI.BackColor = System.Drawing.Color.Transparent
        Me.lblIMEI.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblIMEI.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblIMEI.Location = New System.Drawing.Point(24, 115)
        Me.lblIMEI.Name = "lblIMEI"
        Me.lblIMEI.Size = New System.Drawing.Size(29, 12)
        Me.lblIMEI.TabIndex = 71
        Me.lblIMEI.tbAdriftWhenHover = False
        Me.lblIMEI.tbAutoEllipsis = False
        Me.lblIMEI.tbAutoSize = True
        Me.lblIMEI.tbHideImage = False
        Me.lblIMEI.tbIconImage = Nothing
        Me.lblIMEI.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblIMEI.tbIconPlaceText = 5
        Me.lblIMEI.tbShadow = False
        Me.lblIMEI.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblIMEI.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblIMEI.tbShowScrolling = False
        Me.lblIMEI.Text = "IMEI"
        '
        'lblSN
        '
        Me.lblSN.AutoSize = True
        Me.lblSN.BackColor = System.Drawing.Color.Transparent
        Me.lblSN.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblSN.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblSN.Location = New System.Drawing.Point(24, 83)
        Me.lblSN.Name = "lblSN"
        Me.lblSN.Size = New System.Drawing.Size(41, 12)
        Me.lblSN.TabIndex = 69
        Me.lblSN.tbAdriftWhenHover = False
        Me.lblSN.tbAutoEllipsis = False
        Me.lblSN.tbAutoSize = True
        Me.lblSN.tbHideImage = False
        Me.lblSN.tbIconImage = Nothing
        Me.lblSN.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSN.tbIconPlaceText = 5
        Me.lblSN.tbShadow = False
        Me.lblSN.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSN.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSN.tbShowScrolling = False
        Me.lblSN.Text = "序列号"
        '
        'lbliOSVersionValue
        '
        Me.lbliOSVersionValue.AutoSize = True
        Me.lbliOSVersionValue.BackColor = System.Drawing.Color.Transparent
        Me.lbliOSVersionValue.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lbliOSVersionValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lbliOSVersionValue.Location = New System.Drawing.Point(125, 51)
        Me.lbliOSVersionValue.Name = "lbliOSVersionValue"
        Me.lbliOSVersionValue.Size = New System.Drawing.Size(41, 12)
        Me.lbliOSVersionValue.TabIndex = 68
        Me.lbliOSVersionValue.tbAdriftWhenHover = False
        Me.lbliOSVersionValue.tbAutoEllipsis = False
        Me.lbliOSVersionValue.tbAutoSize = True
        Me.lbliOSVersionValue.tbHideImage = False
        Me.lbliOSVersionValue.tbIconImage = Nothing
        Me.lbliOSVersionValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lbliOSVersionValue.tbIconPlaceText = 5
        Me.lbliOSVersionValue.tbShadow = False
        Me.lbliOSVersionValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lbliOSVersionValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lbliOSVersionValue.tbShowScrolling = False
        Me.lbliOSVersionValue.Text = "10.3.3"
        '
        'lbliOSVersion
        '
        Me.lbliOSVersion.AutoSize = True
        Me.lbliOSVersion.BackColor = System.Drawing.Color.Transparent
        Me.lbliOSVersion.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lbliOSVersion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lbliOSVersion.Location = New System.Drawing.Point(24, 51)
        Me.lbliOSVersion.Name = "lbliOSVersion"
        Me.lbliOSVersion.Size = New System.Drawing.Size(53, 12)
        Me.lbliOSVersion.TabIndex = 67
        Me.lbliOSVersion.tbAdriftWhenHover = False
        Me.lbliOSVersion.tbAutoEllipsis = False
        Me.lbliOSVersion.tbAutoSize = True
        Me.lbliOSVersion.tbHideImage = False
        Me.lbliOSVersion.tbIconImage = Nothing
        Me.lbliOSVersion.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lbliOSVersion.tbIconPlaceText = 5
        Me.lbliOSVersion.tbShadow = False
        Me.lbliOSVersion.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lbliOSVersion.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lbliOSVersion.tbShowScrolling = False
        Me.lbliOSVersion.Text = "固件版本"
        '
        'lblDeviceColor
        '
        Me.lblDeviceColor.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDeviceColor.AutoEllipsis = True
        Me.lblDeviceColor.BackColor = System.Drawing.Color.Transparent
        Me.lblDeviceColor.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDeviceColor.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceColor.Location = New System.Drawing.Point(24, 10)
        Me.lblDeviceColor.Name = "lblDeviceColor"
        Me.lblDeviceColor.Size = New System.Drawing.Size(257, 28)
        Me.lblDeviceColor.TabIndex = 66
        Me.lblDeviceColor.tbAdriftWhenHover = False
        Me.lblDeviceColor.tbAutoEllipsis = True
        Me.lblDeviceColor.tbAutoSize = False
        Me.lblDeviceColor.tbHideImage = False
        Me.lblDeviceColor.tbIconImage = Nothing
        Me.lblDeviceColor.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceColor.tbIconPlaceText = 5
        Me.lblDeviceColor.tbShadow = False
        Me.lblDeviceColor.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceColor.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceColor.tbShowScrolling = False
        Me.lblDeviceColor.Text = "iPhone7,黑色，16G，未越狱"
        Me.lblDeviceColor.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnAppeidBinding
        '
        Me.btnAppeidBinding.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnAppeidBinding.BackColor = System.Drawing.Color.FromArgb(CType(CType(232, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAppeidBinding.BindingForm = Nothing
        Me.btnAppeidBinding.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnAppeidBinding.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAppeidBinding.Location = New System.Drawing.Point(191, 374)
        Me.btnAppeidBinding.Name = "btnAppeidBinding"
        Me.btnAppeidBinding.Padding = New System.Windows.Forms.Padding(3, 6, 3, 4)
        Me.btnAppeidBinding.Selectable = True
        Me.btnAppeidBinding.Size = New System.Drawing.Size(102, 38)
        Me.btnAppeidBinding.TabIndex = 65
        Me.btnAppeidBinding.tbAdriftIconWhenHover = False
        Me.btnAppeidBinding.tbAutoSize = False
        Me.btnAppeidBinding.tbAutoSizeEx = False
        Me.btnAppeidBinding.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_3_bg
        Me.btnAppeidBinding.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnAppeidBinding.tbBadgeNumber = 0
        Me.btnAppeidBinding.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAppeidBinding.tbEndEllipsis = False
        Me.btnAppeidBinding.tbIconHoldPlace = True
        Me.btnAppeidBinding.tbIconImage = Nothing
        Me.btnAppeidBinding.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAppeidBinding.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAppeidBinding.tbIconMore = False
        Me.btnAppeidBinding.tbIconMouseDown = Nothing
        Me.btnAppeidBinding.tbIconMouseHover = Nothing
        Me.btnAppeidBinding.tbIconMouseLeave = Nothing
        Me.btnAppeidBinding.tbIconPlaceText = 2
        Me.btnAppeidBinding.tbIconReadOnly = Nothing
        Me.btnAppeidBinding.tbImageMouseDown = Nothing
        Me.btnAppeidBinding.tbImageMouseHover = Nothing
        Me.btnAppeidBinding.tbImageMouseLeave = Nothing
        Me.btnAppeidBinding.tbProgressValue = 50
        Me.btnAppeidBinding.tbReadOnly = False
        Me.btnAppeidBinding.tbReadOnlyText = False
        Me.btnAppeidBinding.tbShadow = False
        Me.btnAppeidBinding.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAppeidBinding.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAppeidBinding.tbShowDot = False
        Me.btnAppeidBinding.tbShowMoreIconImg = CType(resources.GetObject("btnAppeidBinding.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAppeidBinding.tbShowNew = False
        Me.btnAppeidBinding.tbShowProgress = False
        Me.btnAppeidBinding.tbShowTip = True
        Me.btnAppeidBinding.tbShowToolTipOnButton = False
        Me.btnAppeidBinding.tbSplit = "3,3,3,3"
        Me.btnAppeidBinding.tbText = "绑定Apple ID"
        Me.btnAppeidBinding.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAppeidBinding.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAppeidBinding.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAppeidBinding.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAppeidBinding.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAppeidBinding.tbTextMouseDownPlace = 0
        Me.btnAppeidBinding.tbToolTip = ""
        Me.btnAppeidBinding.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAppeidBinding.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAppeidBinding.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAppeidBinding.VisibleEx = True
        '
        'btnInstallTui
        '
        Me.btnInstallTui.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnInstallTui.BackColor = System.Drawing.Color.FromArgb(CType(CType(232, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnInstallTui.BindingForm = Nothing
        Me.btnInstallTui.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnInstallTui.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnInstallTui.Location = New System.Drawing.Point(96, 374)
        Me.btnInstallTui.Name = "btnInstallTui"
        Me.btnInstallTui.Padding = New System.Windows.Forms.Padding(3, 6, 3, 4)
        Me.btnInstallTui.Selectable = True
        Me.btnInstallTui.Size = New System.Drawing.Size(92, 38)
        Me.btnInstallTui.TabIndex = 64
        Me.btnInstallTui.tbAdriftIconWhenHover = False
        Me.btnInstallTui.tbAutoSize = False
        Me.btnInstallTui.tbAutoSizeEx = False
        Me.btnInstallTui.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_3_bg
        Me.btnInstallTui.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnInstallTui.tbBadgeNumber = 0
        Me.btnInstallTui.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstallTui.tbEndEllipsis = False
        Me.btnInstallTui.tbIconHoldPlace = True
        Me.btnInstallTui.tbIconImage = Nothing
        Me.btnInstallTui.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstallTui.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnInstallTui.tbIconMore = False
        Me.btnInstallTui.tbIconMouseDown = Nothing
        Me.btnInstallTui.tbIconMouseHover = Nothing
        Me.btnInstallTui.tbIconMouseLeave = Nothing
        Me.btnInstallTui.tbIconPlaceText = 2
        Me.btnInstallTui.tbIconReadOnly = Nothing
        Me.btnInstallTui.tbImageMouseDown = Nothing
        Me.btnInstallTui.tbImageMouseHover = Nothing
        Me.btnInstallTui.tbImageMouseLeave = Nothing
        Me.btnInstallTui.tbProgressValue = 50
        Me.btnInstallTui.tbReadOnly = False
        Me.btnInstallTui.tbReadOnlyText = False
        Me.btnInstallTui.tbShadow = False
        Me.btnInstallTui.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInstallTui.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstallTui.tbShowDot = False
        Me.btnInstallTui.tbShowMoreIconImg = CType(resources.GetObject("btnInstallTui.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInstallTui.tbShowNew = False
        Me.btnInstallTui.tbShowProgress = False
        Me.btnInstallTui.tbShowTip = True
        Me.btnInstallTui.tbShowToolTipOnButton = False
        Me.btnInstallTui.tbSplit = "3,3,3,3"
        Me.btnInstallTui.tbText = "安装同步推"
        Me.btnInstallTui.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstallTui.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnInstallTui.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnInstallTui.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnInstallTui.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnInstallTui.tbTextMouseDownPlace = 0
        Me.btnInstallTui.tbToolTip = ""
        Me.btnInstallTui.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstallTui.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstallTui.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstallTui.VisibleEx = True
        '
        'btnAuthDevice
        '
        Me.btnAuthDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnAuthDevice.BackColor = System.Drawing.Color.FromArgb(CType(CType(232, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAuthDevice.BindingForm = Nothing
        Me.btnAuthDevice.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnAuthDevice.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAuthDevice.Location = New System.Drawing.Point(1, 374)
        Me.btnAuthDevice.Name = "btnAuthDevice"
        Me.btnAuthDevice.Padding = New System.Windows.Forms.Padding(3, 6, 3, 4)
        Me.btnAuthDevice.Selectable = True
        Me.btnAuthDevice.Size = New System.Drawing.Size(92, 38)
        Me.btnAuthDevice.TabIndex = 63
        Me.btnAuthDevice.tbAdriftIconWhenHover = False
        Me.btnAuthDevice.tbAutoSize = False
        Me.btnAuthDevice.tbAutoSizeEx = False
        Me.btnAuthDevice.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_3_bg
        Me.btnAuthDevice.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnAuthDevice.tbBadgeNumber = 0
        Me.btnAuthDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAuthDevice.tbEndEllipsis = False
        Me.btnAuthDevice.tbIconHoldPlace = True
        Me.btnAuthDevice.tbIconImage = Nothing
        Me.btnAuthDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAuthDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAuthDevice.tbIconMore = False
        Me.btnAuthDevice.tbIconMouseDown = Nothing
        Me.btnAuthDevice.tbIconMouseHover = Nothing
        Me.btnAuthDevice.tbIconMouseLeave = Nothing
        Me.btnAuthDevice.tbIconPlaceText = 2
        Me.btnAuthDevice.tbIconReadOnly = Nothing
        Me.btnAuthDevice.tbImageMouseDown = Nothing
        Me.btnAuthDevice.tbImageMouseHover = Nothing
        Me.btnAuthDevice.tbImageMouseLeave = Nothing
        Me.btnAuthDevice.tbProgressValue = 50
        Me.btnAuthDevice.tbReadOnly = False
        Me.btnAuthDevice.tbReadOnlyText = False
        Me.btnAuthDevice.tbShadow = False
        Me.btnAuthDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAuthDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAuthDevice.tbShowDot = False
        Me.btnAuthDevice.tbShowMoreIconImg = CType(resources.GetObject("btnAuthDevice.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAuthDevice.tbShowNew = False
        Me.btnAuthDevice.tbShowProgress = False
        Me.btnAuthDevice.tbShowTip = True
        Me.btnAuthDevice.tbShowToolTipOnButton = False
        Me.btnAuthDevice.tbSplit = "3,3,3,3"
        Me.btnAuthDevice.tbText = "正版授权"
        Me.btnAuthDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAuthDevice.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAuthDevice.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAuthDevice.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAuthDevice.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnAuthDevice.tbTextMouseDownPlace = 0
        Me.btnAuthDevice.tbToolTip = ""
        Me.btnAuthDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAuthDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAuthDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAuthDevice.VisibleEx = True
        '
        'btnCloseDevice
        '
        Me.btnCloseDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnCloseDevice.BackColor = System.Drawing.Color.Transparent
        Me.btnCloseDevice.BindingForm = Nothing
        Me.btnCloseDevice.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.btnCloseDevice.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCloseDevice.Location = New System.Drawing.Point(506, 507)
        Me.btnCloseDevice.Name = "btnCloseDevice"
        Me.btnCloseDevice.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnCloseDevice.Selectable = True
        Me.btnCloseDevice.Size = New System.Drawing.Size(35, 21)
        Me.btnCloseDevice.TabIndex = 55
        Me.btnCloseDevice.tbAdriftIconWhenHover = False
        Me.btnCloseDevice.tbAutoSize = False
        Me.btnCloseDevice.tbAutoSizeEx = True
        Me.btnCloseDevice.tbBackgroundImage = Nothing
        Me.btnCloseDevice.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCloseDevice.tbBadgeNumber = 0
        Me.btnCloseDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCloseDevice.tbEndEllipsis = False
        Me.btnCloseDevice.tbIconHoldPlace = True
        Me.btnCloseDevice.tbIconImage = Nothing
        Me.btnCloseDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCloseDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCloseDevice.tbIconMore = False
        Me.btnCloseDevice.tbIconMouseDown = Nothing
        Me.btnCloseDevice.tbIconMouseHover = Nothing
        Me.btnCloseDevice.tbIconMouseLeave = Nothing
        Me.btnCloseDevice.tbIconPlaceText = 2
        Me.btnCloseDevice.tbIconReadOnly = Nothing
        Me.btnCloseDevice.tbImageMouseDown = Nothing
        Me.btnCloseDevice.tbImageMouseHover = Nothing
        Me.btnCloseDevice.tbImageMouseLeave = Nothing
        Me.btnCloseDevice.tbProgressValue = 50
        Me.btnCloseDevice.tbReadOnly = False
        Me.btnCloseDevice.tbReadOnlyText = False
        Me.btnCloseDevice.tbShadow = False
        Me.btnCloseDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCloseDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCloseDevice.tbShowDot = False
        Me.btnCloseDevice.tbShowMoreIconImg = CType(resources.GetObject("btnCloseDevice.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCloseDevice.tbShowNew = False
        Me.btnCloseDevice.tbShowProgress = False
        Me.btnCloseDevice.tbShowTip = True
        Me.btnCloseDevice.tbShowToolTipOnButton = False
        Me.btnCloseDevice.tbSplit = "13,11,13,11"
        Me.btnCloseDevice.tbText = "关机"
        Me.btnCloseDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCloseDevice.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnCloseDevice.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnCloseDevice.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnCloseDevice.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnCloseDevice.tbTextMouseDownPlace = 0
        Me.btnCloseDevice.tbToolTip = ""
        Me.btnCloseDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCloseDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCloseDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCloseDevice.VisibleEx = True
        '
        'btnRestarDevice
        '
        Me.btnRestarDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnRestarDevice.BackColor = System.Drawing.Color.Transparent
        Me.btnRestarDevice.BindingForm = Nothing
        Me.btnRestarDevice.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.btnRestarDevice.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRestarDevice.Location = New System.Drawing.Point(444, 507)
        Me.btnRestarDevice.Name = "btnRestarDevice"
        Me.btnRestarDevice.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnRestarDevice.Selectable = True
        Me.btnRestarDevice.Size = New System.Drawing.Size(35, 21)
        Me.btnRestarDevice.TabIndex = 54
        Me.btnRestarDevice.tbAdriftIconWhenHover = False
        Me.btnRestarDevice.tbAutoSize = False
        Me.btnRestarDevice.tbAutoSizeEx = True
        Me.btnRestarDevice.tbBackgroundImage = Nothing
        Me.btnRestarDevice.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRestarDevice.tbBadgeNumber = 0
        Me.btnRestarDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRestarDevice.tbEndEllipsis = False
        Me.btnRestarDevice.tbIconHoldPlace = True
        Me.btnRestarDevice.tbIconImage = Nothing
        Me.btnRestarDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestarDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRestarDevice.tbIconMore = False
        Me.btnRestarDevice.tbIconMouseDown = Nothing
        Me.btnRestarDevice.tbIconMouseHover = Nothing
        Me.btnRestarDevice.tbIconMouseLeave = Nothing
        Me.btnRestarDevice.tbIconPlaceText = 2
        Me.btnRestarDevice.tbIconReadOnly = Nothing
        Me.btnRestarDevice.tbImageMouseDown = Nothing
        Me.btnRestarDevice.tbImageMouseHover = Nothing
        Me.btnRestarDevice.tbImageMouseLeave = Nothing
        Me.btnRestarDevice.tbProgressValue = 50
        Me.btnRestarDevice.tbReadOnly = False
        Me.btnRestarDevice.tbReadOnlyText = False
        Me.btnRestarDevice.tbShadow = False
        Me.btnRestarDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRestarDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRestarDevice.tbShowDot = False
        Me.btnRestarDevice.tbShowMoreIconImg = CType(resources.GetObject("btnRestarDevice.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRestarDevice.tbShowNew = False
        Me.btnRestarDevice.tbShowProgress = False
        Me.btnRestarDevice.tbShowTip = True
        Me.btnRestarDevice.tbShowToolTipOnButton = False
        Me.btnRestarDevice.tbSplit = "13,11,13,11"
        Me.btnRestarDevice.tbText = "重启"
        Me.btnRestarDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestarDevice.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnRestarDevice.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnRestarDevice.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnRestarDevice.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnRestarDevice.tbTextMouseDownPlace = 0
        Me.btnRestarDevice.tbToolTip = ""
        Me.btnRestarDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRestarDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRestarDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestarDevice.VisibleEx = True
        '
        'btnDeviceDesktop
        '
        Me.btnDeviceDesktop.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnDeviceDesktop.BackColor = System.Drawing.Color.Transparent
        Me.btnDeviceDesktop.BindingForm = Nothing
        Me.btnDeviceDesktop.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.btnDeviceDesktop.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDeviceDesktop.Location = New System.Drawing.Point(358, 507)
        Me.btnDeviceDesktop.Name = "btnDeviceDesktop"
        Me.btnDeviceDesktop.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnDeviceDesktop.Selectable = True
        Me.btnDeviceDesktop.Size = New System.Drawing.Size(59, 21)
        Me.btnDeviceDesktop.TabIndex = 53
        Me.btnDeviceDesktop.tbAdriftIconWhenHover = False
        Me.btnDeviceDesktop.tbAutoSize = False
        Me.btnDeviceDesktop.tbAutoSizeEx = True
        Me.btnDeviceDesktop.tbBackgroundImage = Nothing
        Me.btnDeviceDesktop.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDeviceDesktop.tbBadgeNumber = 0
        Me.btnDeviceDesktop.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDeviceDesktop.tbEndEllipsis = False
        Me.btnDeviceDesktop.tbIconHoldPlace = True
        Me.btnDeviceDesktop.tbIconImage = Nothing
        Me.btnDeviceDesktop.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeviceDesktop.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDeviceDesktop.tbIconMore = False
        Me.btnDeviceDesktop.tbIconMouseDown = Nothing
        Me.btnDeviceDesktop.tbIconMouseHover = Nothing
        Me.btnDeviceDesktop.tbIconMouseLeave = Nothing
        Me.btnDeviceDesktop.tbIconPlaceText = 2
        Me.btnDeviceDesktop.tbIconReadOnly = Nothing
        Me.btnDeviceDesktop.tbImageMouseDown = Nothing
        Me.btnDeviceDesktop.tbImageMouseHover = Nothing
        Me.btnDeviceDesktop.tbImageMouseLeave = Nothing
        Me.btnDeviceDesktop.tbProgressValue = 50
        Me.btnDeviceDesktop.tbReadOnly = False
        Me.btnDeviceDesktop.tbReadOnlyText = False
        Me.btnDeviceDesktop.tbShadow = False
        Me.btnDeviceDesktop.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDeviceDesktop.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDeviceDesktop.tbShowDot = False
        Me.btnDeviceDesktop.tbShowMoreIconImg = CType(resources.GetObject("btnDeviceDesktop.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDeviceDesktop.tbShowNew = False
        Me.btnDeviceDesktop.tbShowProgress = False
        Me.btnDeviceDesktop.tbShowTip = True
        Me.btnDeviceDesktop.tbShowToolTipOnButton = False
        Me.btnDeviceDesktop.tbSplit = "13,11,13,11"
        Me.btnDeviceDesktop.tbText = "实时桌面"
        Me.btnDeviceDesktop.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeviceDesktop.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnDeviceDesktop.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnDeviceDesktop.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnDeviceDesktop.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnDeviceDesktop.tbTextMouseDownPlace = 0
        Me.btnDeviceDesktop.tbToolTip = ""
        Me.btnDeviceDesktop.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDeviceDesktop.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDeviceDesktop.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeviceDesktop.VisibleEx = True
        '
        'btnScreensort1
        '
        Me.btnScreensort1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnScreensort1.BackColor = System.Drawing.Color.Transparent
        Me.btnScreensort1.BindingForm = Nothing
        Me.btnScreensort1.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.btnScreensort1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreensort1.Location = New System.Drawing.Point(296, 507)
        Me.btnScreensort1.Name = "btnScreensort1"
        Me.btnScreensort1.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btnScreensort1.Selectable = True
        Me.btnScreensort1.Size = New System.Drawing.Size(35, 21)
        Me.btnScreensort1.TabIndex = 52
        Me.btnScreensort1.tbAdriftIconWhenHover = False
        Me.btnScreensort1.tbAutoSize = False
        Me.btnScreensort1.tbAutoSizeEx = True
        Me.btnScreensort1.tbBackgroundImage = Nothing
        Me.btnScreensort1.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnScreensort1.tbBadgeNumber = 0
        Me.btnScreensort1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreensort1.tbEndEllipsis = False
        Me.btnScreensort1.tbIconHoldPlace = True
        Me.btnScreensort1.tbIconImage = Nothing
        Me.btnScreensort1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreensort1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreensort1.tbIconMore = False
        Me.btnScreensort1.tbIconMouseDown = Nothing
        Me.btnScreensort1.tbIconMouseHover = Nothing
        Me.btnScreensort1.tbIconMouseLeave = Nothing
        Me.btnScreensort1.tbIconPlaceText = 2
        Me.btnScreensort1.tbIconReadOnly = Nothing
        Me.btnScreensort1.tbImageMouseDown = Nothing
        Me.btnScreensort1.tbImageMouseHover = Nothing
        Me.btnScreensort1.tbImageMouseLeave = Nothing
        Me.btnScreensort1.tbProgressValue = 50
        Me.btnScreensort1.tbReadOnly = False
        Me.btnScreensort1.tbReadOnlyText = False
        Me.btnScreensort1.tbShadow = False
        Me.btnScreensort1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnScreensort1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnScreensort1.tbShowDot = False
        Me.btnScreensort1.tbShowMoreIconImg = CType(resources.GetObject("btnScreensort1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnScreensort1.tbShowNew = False
        Me.btnScreensort1.tbShowProgress = False
        Me.btnScreensort1.tbShowTip = True
        Me.btnScreensort1.tbShowToolTipOnButton = False
        Me.btnScreensort1.tbSplit = "13,11,13,11"
        Me.btnScreensort1.tbText = "截屏"
        Me.btnScreensort1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreensort1.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnScreensort1.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnScreensort1.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnScreensort1.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnScreensort1.tbTextMouseDownPlace = 0
        Me.btnScreensort1.tbToolTip = ""
        Me.btnScreensort1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreensort1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreensort1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreensort1.VisibleEx = True
        '
        'lblDownPluginProgress
        '
        Me.lblDownPluginProgress.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDownPluginProgress.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDownPluginProgress.ForeColor = System.Drawing.SystemColors.ControlDarkDark
        Me.lblDownPluginProgress.Location = New System.Drawing.Point(312, 539)
        Me.lblDownPluginProgress.Name = "lblDownPluginProgress"
        Me.lblDownPluginProgress.Size = New System.Drawing.Size(216, 17)
        Me.lblDownPluginProgress.TabIndex = 8
        Me.lblDownPluginProgress.tbAdriftWhenHover = False
        Me.lblDownPluginProgress.tbAutoEllipsis = False
        Me.lblDownPluginProgress.tbAutoSize = False
        Me.lblDownPluginProgress.tbHideImage = False
        Me.lblDownPluginProgress.tbIconImage = Nothing
        Me.lblDownPluginProgress.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDownPluginProgress.tbIconPlaceText = 5
        Me.lblDownPluginProgress.tbShadow = False
        Me.lblDownPluginProgress.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDownPluginProgress.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDownPluginProgress.tbShowScrolling = False
        Me.lblDownPluginProgress.Text = "正在下载插件： 10%"
        Me.lblDownPluginProgress.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDownPluginProgress.Visible = False
        '
        'picDevice
        '
        Me.picDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picDevice.Image = Global.iTong.My.Resources.Resources.device_default
        Me.picDevice.Location = New System.Drawing.Point(310, 70)
        Me.picDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.picDevice.Name = "picDevice"
        Me.picDevice.Size = New System.Drawing.Size(202, 412)
        Me.picDevice.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picDevice.TabIndex = 7
        Me.picDevice.TabStop = False
        '
        'tbDevCapacity
        '
        Me.tbDevCapacity.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.tbDevCapacity.BackColor = System.Drawing.Color.Transparent
        Me.tbDevCapacity.BackgroundSplit = "3,3,3,3"
        Me.tbDevCapacity.BarHeight = 6
        Me.tbDevCapacity.Colon = ":"
        Me.tbDevCapacity.Controls.Add(Me.btnClearRabbish)
        Me.tbDevCapacity.Controls.Add(Me.picLoadingDeviceData)
        Me.tbDevCapacity.Controls.Add(Me.btnRefreshDeviceData)
        Me.tbDevCapacity.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.tbDevCapacity.ForeColor = System.Drawing.Color.FromArgb(CType(CType(46, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(88, Byte), Integer))
        Me.tbDevCapacity.Location = New System.Drawing.Point(492, 459)
        Me.tbDevCapacity.Name = "tbDevCapacity"
        Me.tbDevCapacity.ShowSizeInfoTop = True
        Me.tbDevCapacity.ShowSizeOnTip = True
        Me.tbDevCapacity.ShowTotalAndAvailable = True
        Me.tbDevCapacity.ShowType = iTong.Components.tbDeviceCapacity.MobileShow.None
        Me.tbDevCapacity.Size = New System.Drawing.Size(712, 88)
        Me.tbDevCapacity.TabIndex = 61
        Me.tbDevCapacity.Text = "TbDeviceCapacity1"
        '
        'btnClearRabbish
        '
        Me.btnClearRabbish.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClearRabbish.BackColor = System.Drawing.Color.Transparent
        Me.btnClearRabbish.BindingForm = Nothing
        Me.btnClearRabbish.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnClearRabbish.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClearRabbish.Location = New System.Drawing.Point(667, 13)
        Me.btnClearRabbish.Name = "btnClearRabbish"
        Me.btnClearRabbish.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnClearRabbish.Selectable = True
        Me.btnClearRabbish.Size = New System.Drawing.Size(17, 17)
        Me.btnClearRabbish.TabIndex = 37
        Me.btnClearRabbish.TabStop = False
        Me.btnClearRabbish.tbAdriftIconWhenHover = False
        Me.btnClearRabbish.tbAutoSize = False
        Me.btnClearRabbish.tbAutoSizeEx = False
        Me.btnClearRabbish.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_4_clearrubbish
        Me.btnClearRabbish.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnClearRabbish.tbBadgeNumber = 0
        Me.btnClearRabbish.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClearRabbish.tbEndEllipsis = False
        Me.btnClearRabbish.tbIconHoldPlace = True
        Me.btnClearRabbish.tbIconImage = Nothing
        Me.btnClearRabbish.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnClearRabbish.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClearRabbish.tbIconMore = False
        Me.btnClearRabbish.tbIconMouseDown = Nothing
        Me.btnClearRabbish.tbIconMouseHover = Nothing
        Me.btnClearRabbish.tbIconMouseLeave = Nothing
        Me.btnClearRabbish.tbIconPlaceText = 2
        Me.btnClearRabbish.tbIconReadOnly = Nothing
        Me.btnClearRabbish.tbImageMouseDown = Nothing
        Me.btnClearRabbish.tbImageMouseHover = Nothing
        Me.btnClearRabbish.tbImageMouseLeave = Nothing
        Me.btnClearRabbish.tbProgressValue = 50
        Me.btnClearRabbish.tbReadOnly = False
        Me.btnClearRabbish.tbReadOnlyText = False
        Me.btnClearRabbish.tbShadow = False
        Me.btnClearRabbish.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnClearRabbish.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnClearRabbish.tbShowDot = False
        Me.btnClearRabbish.tbShowMoreIconImg = CType(resources.GetObject("btnClearRabbish.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnClearRabbish.tbShowNew = False
        Me.btnClearRabbish.tbShowProgress = False
        Me.btnClearRabbish.tbShowTip = True
        Me.btnClearRabbish.tbShowToolTipOnButton = False
        Me.btnClearRabbish.tbSplit = "0,0,0,0"
        Me.btnClearRabbish.tbText = ""
        Me.btnClearRabbish.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClearRabbish.tbTextColor = System.Drawing.Color.Black
        Me.btnClearRabbish.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnClearRabbish.tbTextColorDown = System.Drawing.Color.Black
        Me.btnClearRabbish.tbTextColorHover = System.Drawing.Color.Black
        Me.btnClearRabbish.tbTextMouseDownPlace = 0
        Me.btnClearRabbish.tbToolTip = ""
        Me.btnClearRabbish.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClearRabbish.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClearRabbish.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClearRabbish.VisibleEx = True
        '
        'picLoadingDeviceData
        '
        Me.picLoadingDeviceData.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.picLoadingDeviceData.Image = Global.iTong.My.Resources.Resources.gif_loading_summary_16
        Me.picLoadingDeviceData.Location = New System.Drawing.Point(637, 14)
        Me.picLoadingDeviceData.Name = "picLoadingDeviceData"
        Me.picLoadingDeviceData.Size = New System.Drawing.Size(16, 16)
        Me.picLoadingDeviceData.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picLoadingDeviceData.TabIndex = 1
        Me.picLoadingDeviceData.TabStop = False
        '
        'btnRefreshDeviceData
        '
        Me.btnRefreshDeviceData.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRefreshDeviceData.BackColor = System.Drawing.Color.Transparent
        Me.btnRefreshDeviceData.BindingForm = Nothing
        Me.btnRefreshDeviceData.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnRefreshDeviceData.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefreshDeviceData.Location = New System.Drawing.Point(637, 14)
        Me.btnRefreshDeviceData.Name = "btnRefreshDeviceData"
        Me.btnRefreshDeviceData.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefreshDeviceData.Selectable = True
        Me.btnRefreshDeviceData.Size = New System.Drawing.Size(17, 17)
        Me.btnRefreshDeviceData.TabIndex = 0
        Me.btnRefreshDeviceData.TabStop = False
        Me.btnRefreshDeviceData.tbAdriftIconWhenHover = False
        Me.btnRefreshDeviceData.tbAutoSize = True
        Me.btnRefreshDeviceData.tbAutoSizeEx = False
        Me.btnRefreshDeviceData.tbBackgroundImage = Global.iTong.My.Resources.Resources.summary_btn_4_refresh
        Me.btnRefreshDeviceData.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefreshDeviceData.tbBadgeNumber = 0
        Me.btnRefreshDeviceData.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefreshDeviceData.tbEndEllipsis = False
        Me.btnRefreshDeviceData.tbIconHoldPlace = True
        Me.btnRefreshDeviceData.tbIconImage = Nothing
        Me.btnRefreshDeviceData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefreshDeviceData.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRefreshDeviceData.tbIconMore = False
        Me.btnRefreshDeviceData.tbIconMouseDown = Nothing
        Me.btnRefreshDeviceData.tbIconMouseHover = Nothing
        Me.btnRefreshDeviceData.tbIconMouseLeave = Nothing
        Me.btnRefreshDeviceData.tbIconPlaceText = 2
        Me.btnRefreshDeviceData.tbIconReadOnly = Nothing
        Me.btnRefreshDeviceData.tbImageMouseDown = Nothing
        Me.btnRefreshDeviceData.tbImageMouseHover = Nothing
        Me.btnRefreshDeviceData.tbImageMouseLeave = Nothing
        Me.btnRefreshDeviceData.tbProgressValue = 50
        Me.btnRefreshDeviceData.tbReadOnly = False
        Me.btnRefreshDeviceData.tbReadOnlyText = False
        Me.btnRefreshDeviceData.tbShadow = False
        Me.btnRefreshDeviceData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnRefreshDeviceData.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRefreshDeviceData.tbShowDot = False
        Me.btnRefreshDeviceData.tbShowMoreIconImg = CType(resources.GetObject("btnRefreshDeviceData.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefreshDeviceData.tbShowNew = False
        Me.btnRefreshDeviceData.tbShowProgress = False
        Me.btnRefreshDeviceData.tbShowTip = True
        Me.btnRefreshDeviceData.tbShowToolTipOnButton = False
        Me.btnRefreshDeviceData.tbSplit = "0,0,0,0"
        Me.btnRefreshDeviceData.tbText = ""
        Me.btnRefreshDeviceData.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefreshDeviceData.tbTextColor = System.Drawing.Color.Black
        Me.btnRefreshDeviceData.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnRefreshDeviceData.tbTextColorDown = System.Drawing.Color.Black
        Me.btnRefreshDeviceData.tbTextColorHover = System.Drawing.Color.Black
        Me.btnRefreshDeviceData.tbTextMouseDownPlace = 0
        Me.btnRefreshDeviceData.tbToolTip = ""
        Me.btnRefreshDeviceData.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefreshDeviceData.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefreshDeviceData.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefreshDeviceData.VisibleEx = True
        '
        'btnQQChat
        '
        Me.btnQQChat.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnQQChat.BackColor = System.Drawing.Color.Transparent
        Me.btnQQChat.BindingForm = Nothing
        Me.btnQQChat.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnQQChat.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.btnQQChat.Location = New System.Drawing.Point(889, 152)
        Me.btnQQChat.Name = "btnQQChat"
        Me.btnQQChat.Padding = New System.Windows.Forms.Padding(20, 18, 10, 20)
        Me.btnQQChat.Selectable = True
        Me.btnQQChat.Size = New System.Drawing.Size(310, 95)
        Me.btnQQChat.TabIndex = 60
        Me.btnQQChat.TabStop = False
        Me.btnQQChat.tbAdriftIconWhenHover = False
        Me.btnQQChat.tbAutoSize = False
        Me.btnQQChat.tbAutoSizeEx = False
        Me.btnQQChat.tbBackgroundImage = Nothing
        Me.btnQQChat.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnQQChat.tbBadgeNumber = 0
        Me.btnQQChat.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnQQChat.tbEndEllipsis = False
        Me.btnQQChat.tbIconHoldPlace = True
        Me.btnQQChat.tbIconImage = Global.iTong.My.Resources.Resources.summary_qq_53
        Me.btnQQChat.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnQQChat.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnQQChat.tbIconMore = False
        Me.btnQQChat.tbIconMouseDown = Nothing
        Me.btnQQChat.tbIconMouseHover = Nothing
        Me.btnQQChat.tbIconMouseLeave = Nothing
        Me.btnQQChat.tbIconPlaceText = 23
        Me.btnQQChat.tbIconReadOnly = Nothing
        Me.btnQQChat.tbImageMouseDown = Nothing
        Me.btnQQChat.tbImageMouseHover = Global.iTong.My.Resources.Resources.summary_btn_1_hover
        Me.btnQQChat.tbImageMouseLeave = Nothing
        Me.btnQQChat.tbProgressValue = 50
        Me.btnQQChat.tbReadOnly = False
        Me.btnQQChat.tbReadOnlyText = False
        Me.btnQQChat.tbShadow = False
        Me.btnQQChat.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnQQChat.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnQQChat.tbShowDot = False
        Me.btnQQChat.tbShowMoreIconImg = CType(resources.GetObject("btnQQChat.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnQQChat.tbShowNew = False
        Me.btnQQChat.tbShowProgress = False
        Me.btnQQChat.tbShowTip = True
        Me.btnQQChat.tbShowToolTipOnButton = True
        Me.btnQQChat.tbSplit = "3,3,3,3"
        Me.btnQQChat.tbText = "QQ数据管理"
        Me.btnQQChat.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnQQChat.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnQQChat.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnQQChat.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnQQChat.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnQQChat.tbTextMouseDownPlace = 0
        Me.btnQQChat.tbToolTip = "新推出人工数据恢复"
        Me.btnQQChat.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnQQChat.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnQQChat.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnQQChat.VisibleEx = True
        '
        'btnZXKF
        '
        Me.btnZXKF.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnZXKF.BackColor = System.Drawing.Color.Transparent
        Me.btnZXKF.BindingForm = Nothing
        Me.btnZXKF.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnZXKF.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.btnZXKF.Location = New System.Drawing.Point(889, 352)
        Me.btnZXKF.Name = "btnZXKF"
        Me.btnZXKF.Padding = New System.Windows.Forms.Padding(20, 18, 10, 20)
        Me.btnZXKF.Selectable = True
        Me.btnZXKF.Size = New System.Drawing.Size(310, 95)
        Me.btnZXKF.TabIndex = 94
        Me.btnZXKF.TabStop = False
        Me.btnZXKF.tbAdriftIconWhenHover = False
        Me.btnZXKF.tbAutoSize = False
        Me.btnZXKF.tbAutoSizeEx = False
        Me.btnZXKF.tbBackgroundImage = Nothing
        Me.btnZXKF.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnZXKF.tbBadgeNumber = 0
        Me.btnZXKF.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnZXKF.tbEndEllipsis = False
        Me.btnZXKF.tbIconHoldPlace = True
        Me.btnZXKF.tbIconImage = Global.iTong.My.Resources.Resources.summary_zxkf_53
        Me.btnZXKF.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnZXKF.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnZXKF.tbIconMore = False
        Me.btnZXKF.tbIconMouseDown = Nothing
        Me.btnZXKF.tbIconMouseHover = Nothing
        Me.btnZXKF.tbIconMouseLeave = Nothing
        Me.btnZXKF.tbIconPlaceText = 23
        Me.btnZXKF.tbIconReadOnly = Nothing
        Me.btnZXKF.tbImageMouseDown = Nothing
        Me.btnZXKF.tbImageMouseHover = Global.iTong.My.Resources.Resources.summary_btn_1_hover
        Me.btnZXKF.tbImageMouseLeave = Nothing
        Me.btnZXKF.tbProgressValue = 50
        Me.btnZXKF.tbReadOnly = False
        Me.btnZXKF.tbReadOnlyText = False
        Me.btnZXKF.tbShadow = False
        Me.btnZXKF.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnZXKF.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnZXKF.tbShowDot = False
        Me.btnZXKF.tbShowMoreIconImg = CType(resources.GetObject("btnZXKF.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnZXKF.tbShowNew = False
        Me.btnZXKF.tbShowProgress = False
        Me.btnZXKF.tbShowTip = True
        Me.btnZXKF.tbShowToolTipOnButton = True
        Me.btnZXKF.tbSplit = "3,3,3,3"
        Me.btnZXKF.tbText = "掌心客服"
        Me.btnZXKF.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnZXKF.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnZXKF.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnZXKF.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnZXKF.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnZXKF.tbTextMouseDownPlace = 0
        Me.btnZXKF.tbToolTip = "微信客服精准营销方案"
        Me.btnZXKF.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnZXKF.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnZXKF.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnZXKF.Visible = False
        Me.btnZXKF.VisibleEx = True
        '
        'frmSummary
        '
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(1508, 623)
        Me.Controls.Add(Me.pnlSummary)
        Me.Controls.Add(Me.pnlDFUModeInfo)
        Me.Controls.Add(Me.btnBindingTip)
        Me.Controls.Add(Me.pnlToolV3)
        Me.Controls.Add(Me.btnYueYu)
        Me.Controls.Add(Me.pnlDeviceName)
        Me.Controls.Add(Me.pnlWeb)
        Me.Controls.Add(Me.lblDownloadProgress)
        Me.Controls.Add(Me.pbDevice)
        Me.Controls.Add(Me.tbCapacity)
        Me.Controls.Add(Me.pnlScreenshot)
        Me.Font = New System.Drawing.Font("宋体", 10.5!)
        Me.MinimumSize = New System.Drawing.Size(6, 6)
        Me.Name = "frmSummary"
        Me.tbShowIconOnForm = False
        Me.tbSplit = "3,3,3,3"
        Me.tbTitleBackColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(196, Byte), Integer))
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Controls.SetChildIndex(Me.pnlScreenshot, 0)
        Me.Controls.SetChildIndex(Me.tbCapacity, 0)
        Me.Controls.SetChildIndex(Me.pbDevice, 0)
        Me.Controls.SetChildIndex(Me.lblDownloadProgress, 0)
        Me.Controls.SetChildIndex(Me.pnlWeb, 0)
        Me.Controls.SetChildIndex(Me.pnlDeviceName, 0)
        Me.Controls.SetChildIndex(Me.btnYueYu, 0)
        Me.Controls.SetChildIndex(Me.pnlToolV3, 0)
        Me.Controls.SetChildIndex(Me.btnBindingTip, 0)
        Me.Controls.SetChildIndex(Me.pnlDFUModeInfo, 0)
        Me.Controls.SetChildIndex(Me.pnlSummary, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlChangeDeviceName.ResumeLayout(False)
        CType(Me.pbDevice, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlScreenshot.ResumeLayout(False)
        CType(Me.PictureBox4, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tbCapacity.ResumeLayout(False)
        Me.tbCapacity.PerformLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.cmsSetting.ResumeLayout(False)
        Me.cmsWeibo.ResumeLayout(False)
        CType(Me.picCharging, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDeviceName.ResumeLayout(False)
        Me.pnlDeviceName.PerformLayout()
        Me.pnlToolV3.ResumeLayout(False)
        Me.pnlDFUModeInfo.ResumeLayout(False)
        Me.pnlDFUModeInfo.PerformLayout()
        Me.pnlSummary.ResumeLayout(False)
        Me.pnlSummary.PerformLayout()
        CType(Me.picSplit3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picSplit2, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picSplit1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDeviceInfo.ResumeLayout(False)
        Me.pnlDeviceInfo.PerformLayout()
        CType(Me.picDevice, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tbDevCapacity.ResumeLayout(False)
        Me.tbDevCapacity.PerformLayout()
        CType(Me.picLoadingDeviceData, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pbDevice As System.Windows.Forms.PictureBox
    Friend WithEvents lblNameValue As tbLabel
    Friend WithEvents tbCapacity As tbDeviceCapacity
    Friend WithEvents btnRefresh As tbButton
    Friend WithEvents txtNameValue As tbTextBox
    Friend WithEvents pnlChangeDeviceName As tbPanel
    Friend WithEvents btnRename As tbButton
    Friend WithEvents pnlScreenshot As tbPanel
    Friend WithEvents btnScreenshot As tbButton
    Friend WithEvents btnScreenshotSetUp As tbButton
    Friend WithEvents btnScreenshotPlay As tbButton
    Friend WithEvents btnShareTo As tbButton
    Friend WithEvents cmsSetting As tbContextMenuStrip
    Friend WithEvents tsmiScreenshot As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiScreenshotShell As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lblDownloadProgress As tbLabel
    Friend WithEvents btnDetail As tbButton
    Friend WithEvents btnDisconnect As tbButton
    Friend WithEvents lblVersion As tbLabel
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents btnJump As tbButton
    Friend WithEvents btnClear As tbButton
    Friend WithEvents btnClearTip As tbButton
    Friend WithEvents cmsWeibo As tbContextMenuStrip
    Friend WithEvents tsmiSina As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTecent As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiFacebook As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTwitter As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lblCharging As System.Windows.Forms.Label
    Friend WithEvents picCharging As System.Windows.Forms.PictureBox
    Friend WithEvents tmrRefresh As System.Windows.Forms.Timer
    Friend WithEvents pnlWeb As tbPanel
    Friend WithEvents pnlDeviceName As tbPanel
    Friend WithEvents tmrTui As System.Windows.Forms.Timer
    Friend WithEvents btnYueYu As tbButton
    Friend WithEvents bgwStartJailbreak As System.ComponentModel.BackgroundWorker
    Friend WithEvents pnlToolV3 As tbPanel
    Friend WithEvents btnTuiToolV3 As tbButton
    Friend WithEvents btnRepairToolV3 As tbButton
    Friend WithEvents btnBackupRestore As tbButton
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox2 As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox3 As System.Windows.Forms.PictureBox
    Friend WithEvents btnWeixin As tbButton
    Friend WithEvents btnBinding As tbButton
    Friend WithEvents btnBindingTip As tbButton
    Friend WithEvents btnRepairPay As tbButton
    Friend WithEvents btnBatteryManager As iTong.Components.tbButton
    Friend WithEvents PictureBox4 As System.Windows.Forms.PictureBox
    Friend WithEvents pnlDFUModeInfo As iTong.Components.tbPanel
    Friend WithEvents lblConnectMode As System.Windows.Forms.Label
    Friend WithEvents lblECIDValue As iTong.Components.tbLabel
    Friend WithEvents lblProductTypeValue As iTong.Components.tbLabel
    Friend WithEvents lblDeviceTypeValue As iTong.Components.tbLabel
    Friend WithEvents lblECID As iTong.Components.tbLabel
    Friend WithEvents lblProductType As iTong.Components.tbLabel
    Friend WithEvents lblDeviceType As iTong.Components.tbLabel
    Friend WithEvents lblDFUTitle As iTong.Components.tbLabel
    Friend WithEvents lblNote As iTong.Components.tbLabel
    Friend WithEvents lblDeviceIdValue As iTong.Components.tbLabel
    Friend WithEvents lblDeviceId As iTong.Components.tbLabel
    Friend WithEvents lblDFUDescription1 As iTong.Components.tbButton
    Friend WithEvents lblDFUDescription2 As iTong.Components.tbButton
    Friend WithEvents btnGotoFlashDevice As iTong.Components.tbButton
    Friend WithEvents btnOutRecoverMode As iTong.Components.tbButton
    Friend WithEvents btnCopyDeviceId As iTong.Components.tbButton
    Friend WithEvents lblCopyInfo As iTong.Components.tbLabel
    Friend WithEvents tmrShowCopyInfo As System.Windows.Forms.Timer
    Friend WithEvents btnH5Game As iTong.Components.tbButton
    Friend WithEvents btnGoJailbreakWeb As iTong.Components.tbButton
    Friend WithEvents btnQQ As iTong.Components.tbButton
    Friend WithEvents btnCanceliOSUpgrade As iTong.Components.tbButton
    Friend WithEvents pnlSummary As iTong.Components.tbPanel
    Friend WithEvents pnlDeviceInfo As iTong.Components.tbPanel
    Friend WithEvents btnCloseDevice As iTong.Components.tbButton
    Friend WithEvents btnRestarDevice As iTong.Components.tbButton
    Friend WithEvents btnDeviceDesktop As iTong.Components.tbButton
    Friend WithEvents btnScreensort1 As iTong.Components.tbButton
    Friend WithEvents lblDownPluginProgress As iTong.Components.tbLabel
    Friend WithEvents picDevice As System.Windows.Forms.PictureBox
    Friend WithEvents btnQQChat As iTong.Components.tbButton
    Friend WithEvents btnRingtone As iTong.Components.tbButton
    Friend WithEvents btniOSDowngrade As iTong.Components.tbButton
    Friend WithEvents btnWechat As iTong.Components.tbButton
    Friend WithEvents btnInstallTui As iTong.Components.tbButton
    Friend WithEvents btnAuthDevice As iTong.Components.tbButton
    Friend WithEvents btnAppeidBinding As iTong.Components.tbButton
    Friend WithEvents tbDevCapacity As iTong.Components.tbDeviceCapacity
    Friend WithEvents btnClearRabbish As iTong.Components.tbButton
    Friend WithEvents picLoadingDeviceData As System.Windows.Forms.PictureBox
    Friend WithEvents btnRefreshDeviceData As iTong.Components.tbButton
    Friend WithEvents btnDeviceInfo As iTong.Components.tbButton
    Friend WithEvents btnEditDeviceName As iTong.Components.tbButton
    Friend WithEvents lblDeviceName As iTong.Components.tbLabel
    Friend WithEvents txtDeviceName As iTong.Components.tbTextBox
    Friend WithEvents cbxCloseiTunesStart As iTong.Components.tbCheckBox
    Friend WithEvents lblDeviceIdentify As iTong.Components.tbLabel
    Friend WithEvents lblChargingPercentValue As iTong.Components.tbLabel
    Friend WithEvents lblChargingPercent As iTong.Components.tbLabel
    Friend WithEvents lblChargingTimesValue As iTong.Components.tbLabel
    Friend WithEvents lblChargingTimes As iTong.Components.tbLabel
    Friend WithEvents lblGuaranteeDateValue As iTong.Components.tbLabel
    Friend WithEvents lblGuaranteeDate As iTong.Components.tbLabel
    Friend WithEvents lblProductDateValue As iTong.Components.tbLabel
    Friend WithEvents lblProductDate As iTong.Components.tbLabel
    Friend WithEvents lblSaleAreaValue As iTong.Components.tbLabel
    Friend WithEvents lblSaleArea As iTong.Components.tbLabel
    Friend WithEvents lblIMEI As iTong.Components.tbLabel
    Friend WithEvents lblSN As iTong.Components.tbLabel
    Friend WithEvents lbliOSVersionValue As iTong.Components.tbLabel
    Friend WithEvents lbliOSVersion As iTong.Components.tbLabel
    Friend WithEvents lblDeviceColor As iTong.Components.tbLabel
    Friend WithEvents picSplit1 As System.Windows.Forms.PictureBox
    Friend WithEvents picSplit3 As System.Windows.Forms.PictureBox
    Friend WithEvents picSplit2 As System.Windows.Forms.PictureBox
    Friend WithEvents lblDeviceIdentifyValue As iTong.Components.tbButton
    Friend WithEvents lblIMEIValue As iTong.Components.tbButton
    Friend WithEvents lblSNValue As iTong.Components.tbButton
    Friend WithEvents lblCloseiOSUpdate As System.Windows.Forms.LinkLabel
    Friend WithEvents lblChargingDetail As System.Windows.Forms.LinkLabel
    Friend WithEvents lblBackup As System.Windows.Forms.LinkLabel
    Friend WithEvents btnMore As iTong.Components.tbButton
    Friend WithEvents btnZXKF As iTong.Components.tbButton

End Class
