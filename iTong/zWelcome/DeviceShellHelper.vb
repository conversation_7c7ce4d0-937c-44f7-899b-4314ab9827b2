﻿'移到CoreModuleCS
Imports System.Threading

Public Class DeviceShellHelper

    Private Shared mDictInstances As New Dictionary(Of String, DeviceShellHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例

    Private mLanguage As LanguageInterface = Nothing
    Private mDevice As iPhoneDevice
    Private mTdDevriceShell As Thread
    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Private mTdGetShell As Thread

    Public Shared Event DevriceShellEventHandler As EventHandler(Of DeviceShellEventArgs)

    Private mStrShellFilePath As String

    Public ReadOnly Property ShellFilePath() As String
        Get
            Return mStrShellFilePath
        End Get
    End Property

    Private mImgShell As Image

    Public ReadOnly Property ImgShell() As Image
        Get
            Return mImgShell
        End Get
    End Property


#Region "--- 单例 ---"

    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New(ByVal iPhoneDeviceInstance As iPhoneDevice)
        mDevice = iPhoneDeviceInstance
    End Sub

    Public Shared Function GetInstance(ByVal device As iPhoneDevice) As DeviceShellHelper
        If mDictInstances.ContainsKey(device.Identifier) Then
            If mDictInstances(device.Identifier).mDevice.IsConnected = False Then
                mDictInstances.Remove(device.Identifier)
                mDictInstances.Add(device.Identifier, New DeviceShellHelper(device))
            End If
        Else
            SyncLock _lockGetInstance
                If Not mDictInstances.ContainsKey(device.Identifier) Then
                    mDictInstances.Add(device.Identifier, New DeviceShellHelper(device))
                End If
            End SyncLock
        End If

        Return mDictInstances.Item(device.Identifier)
    End Function

#End Region

#Region "--- 初始化 ---"

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
            End If
            Try
                If mTdGetShell IsNot Nothing AndAlso mTdGetShell.ThreadState <> ThreadState.Stopped Then
                    mTdGetShell.Abort()
                End If
            Catch
            End Try
            disposedValue = True
        End If
    End Sub

#End Region

#Region "--- 截图 ---"

    Public Sub Start2GetShell(ByVal strProductType As String)
        If mTdGetShell IsNot Nothing AndAlso mTdGetShell.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        mTdGetShell = New Thread(AddressOf GetShell)
        mTdGetShell.IsBackground = True
        mTdGetShell.Start(strProductType)

        '启动start的时候可能县城的状态来不及改变，会同时启动两个线程进去
        Utility.WaitSeconds(0.5)
    End Sub

    '取得外壳
    Private Sub GetShell(ByVal objProductType As Object)
        Dim strProductType As String = objProductType.ToString()
        Dim imgShell As Image = Nothing

        Try
            'Dim dInfo As tbDeviceInfo = tbDeviceCache.GetInstanse().GetLastDeviceInfo(Me.mDevice.SerialNumber)
            Dim iPhoneColor As PHBDeviceColorVariation = Me.mDevice.DeviceColor
            If iPhoneColor = PHBDeviceColorVariation.Default Then
                iPhoneColor = Me.mDevice.DeviceColor
            End If

            Dim strShellFileName As String = SummaryInfo.GetIconNameByDevice(Me.mDevice.ProductType, iPhoneColor)
            mStrShellFilePath = Folder.CacheFolder & "ShellEx\"

            Try
                Dim oldPath As String = Folder.CacheFolder & "Shell\"
                '把旧的文件夹删除掉
                If Directory.Exists(oldPath) Then
                    Directory.Delete(oldPath, True)
                End If
            Catch ex As Exception
            End Try

            Folder.CheckFolder(mStrShellFilePath)
            mStrShellFilePath = mStrShellFilePath & strShellFileName

            If Not File.Exists(mStrShellFilePath) AndAlso Common.NetworkIsAvailable() Then
                '下载图片
                Dim thd As New Thread(AddressOf GetShellThread)
                thd.IsBackground = True
                thd.Start(mStrShellFilePath)

                Dim dt As Date = Now
                Do
                    Utility.WaitSeconds(0.2)
                Loop While thd.ThreadState <> ThreadState.Stopped AndAlso New TimeSpan(Now.Ticks - dt.Ticks).TotalSeconds < 3
            End If

            If File.Exists(mStrShellFilePath) Then
                imgShell = Common.ImageFromFile(mStrShellFilePath)
            End If

            mImgShell = imgShell
            RaiseEvent DevriceShellEventHandler(Me, New DeviceShellEventArgs(Me.mDevice.SerialNumber))

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetShell")
        End Try

    End Sub

    Private Sub GetShellThread(ByVal state As Object)
        Try
            Dim strShellPath As String = state
            '"http://t.tongbu.com/shell/"
            Dim imgShell As Image = Common.DownloadImage(WebUrl.ResourceScreenShotShell & Path.GetFileName(strShellPath), 3000)
            If imgShell IsNot Nothing Then
                imgShell.Save(strShellPath, Imaging.ImageFormat.Png)
                imgShell.Dispose()
            End If
        Catch ex As Exception
        End Try
    End Sub

#End Region

End Class

Public Class DeviceShellEventArgs
    Inherits EventArgs

    Dim strSN As String

    Public Sub New(ByVal strsn As String)
        Me.strSN = strsn
    End Sub

    Public Property mStrSN() As String
        Get
            Return Me.strSN
        End Get
        Set(ByVal value As String)
            Me.strSN = value
        End Set
    End Property

End Class
