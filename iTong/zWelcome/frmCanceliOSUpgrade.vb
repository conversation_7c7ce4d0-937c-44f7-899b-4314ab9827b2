﻿Imports System.Threading

Public Class frmCanceliOSUpgrade

    Private mthrCanceliOSUpgrade As Thread

    Sub New(ByVal app As IApplication, dev As iPhoneDevice)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = app
        Me.mDevice = dev
        Me.Language = app.Language
        Me.Icon = My.Resources.iTong
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Common.Lable.CloseiOSUpgrade") '"关闭iOS更新"
        Me.lblTitle.Text = Me.Language.GetString("Common.Lable.CloseiOSUpgradeCourse") '"关闭iOS设备自动下载更新教程"
        Me.lblStep1.Text = Me.Language.GetString("Common.Lable.MasterCode") '"请保证手机处于解锁状态下"
        Me.btnLoadDescFile.Text = Me.Language.GetString("Common.Lable.LoadingUpgradeFile") '"点击加载描述文件。"
        Me.btnLoadDescFile.Location = New Point(Me.lblStep1.Right - 2, Me.btnLoadDescFile.Top)
        Me.lblStep2.Text = Me.Language.GetString("Common.Lable.InstallUpgradeRestart") '"加载成功后，在设备上安装该描述文件。" & vbCrLf & "并按提示重启设备。"
        Me.lblStep3.Text = Me.Language.GetString("Common.Lable.Feedback") '"桌面出现""Feedback""图标表示成功"
        Me.btnToDeleteFile.Text = Me.Language.GetString("Common.Lable.PromptUpdate") '"操作成功后仍提示更新？"
        Me.btnReopen.Text = Me.Language.GetString("Common.Lable.OpenUpdate") '"重新开启iOS更新"
        Me.btnISee.Text = Me.Language.GetString("Common.ISee") '"我知道了"
        Me.lblTitle.Font = Common.CreateFont("微软雅黑", 14.0!, FontStyle.Regular)
    End Sub

    Private Sub btnISee_Click(sender As Object, e As EventArgs) Handles btnISee.Click
        Me.Close()
    End Sub

    Private Sub btnReopen_Click(sender As Object, e As EventArgs) Handles btnReopen.Click
        '"http://news.tongbu.com/94489.html"
        Common.OpenExplorer(WebUrl.PageReOpeniOSUpgrade)
    End Sub

    Private Sub btnLoadDescFile_Click(sender As Object, e As EventArgs) Handles btnLoadDescFile.Click
        Try
            If Me.mthrCanceliOSUpgrade IsNot Nothing AndAlso Me.mthrCanceliOSUpgrade.ThreadState <> ThreadState.Stopped Then
                Me.mthrCanceliOSUpgrade.Abort()
            End If
            Me.mthrCanceliOSUpgrade = New Thread(AddressOf DoCanceliOSUpgrade)
            With Me.mthrCanceliOSUpgrade
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmCanceliOSUpgrade_btnLoadDescFile_Click")
        End Try
    End Sub

    Private Sub DoCanceliOSUpgrade()
        Try
            Me.mDevice.CanceliOSUpgrade()
            tbMessageBox.Show(Me, Me.Language.GetString("Common.Lable.PromptedInstallUpgrade"), Me.Language.GetString("Common.Lable.LoadUpgradeFile"), MessageBoxButtons.OK) '"加载描述文件成功，请到设备安装描述文件。" "加载描述文件"
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmCanceliOSUpgrade_DoCanceliOSUpgrade")
        End Try
    End Sub

    Private Sub btnToDeleteFile_Click(sender As Object, e As EventArgs) Handles btnToDeleteFile.Click
        '"http://news.tongbu.com/94504.html"
        Common.OpenExplorer(WebUrl.PageCanceliOSUpgrade)
    End Sub

End Class