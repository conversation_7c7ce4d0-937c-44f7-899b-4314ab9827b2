﻿Public Class frmNewDevice

    Private mDevice As iPhoneDevice
    Private tmrShow As System.Timers.Timer

    Public Sub New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()

        Me.StartPosition = FormStartPosition.Manual
        Me.PNGForm.ShowInTaskbar = False
        Me.ShowInTaskbar = False
        Me.CanbeMove = False
        Me.CanResize = False
        Me.FilletRadius = 5
        Me.PaddingEx = New Padding(15, 15, 16, 15)
        Me.BackColor = Color.FromArgb(6, 13, 18)
        Me.PNGForm.TopMost = True
        Me.TopMost = True

        Me.BackgroundImage = My.Resources.welcome_frm_bg_new

        tmrShow = New System.Timers.Timer()
        tmrShow.Interval = 10000

        AddHandler tmrShow.Elapsed, AddressOf tmrShow_Tick
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.btnOpen.tbText = Me.Language.GetString("Common.Button.Open")         '"Open"
    End Sub

    Private Sub btnOpen_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOpen.Click
        Me.tmrShow.Stop()
        Me.Hide()

        If mDevice IsNot Nothing AndAlso mDevice.IsConnected Then
            Dim frm As New MainForm(Me.mDevice)

            Dim frmOwner As Form = ProcForm.Instance().GetMainForm()
            If frmOwner IsNot Nothing Then
                frm.StartPosition = FormStartPosition.Manual
                frm.Location = New Point(frmOwner.Left + 135, frmOwner.Top + 25)
            End If

            frm.Show()
        End If
    End Sub

    Private Delegate Sub InitDataHandler(ByVal device As iPhoneDevice)
    Private Sub InitData(ByVal device As iPhoneDevice)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New InitDataHandler(AddressOf InitData), device)
            Else
                Try
                    Me.mDevice = device
                    If device.ConnectMode = ConnectMode.USB Then
                        Me.picIcon.Image = My.Resources.welcome_new_usb
                    Else
                        Me.picIcon.Image = My.Resources.welcome_new_wifi
                    End If

                    Dim strType As String = String.Empty
                    If device.ProductType.StartsWith("ipad", StringComparison.OrdinalIgnoreCase) Then
                        strType = "iPad"
                    ElseIf device.ProductType.StartsWith("iphone", StringComparison.OrdinalIgnoreCase) Then
                        strType = "iPhone"
                    Else
                        strType = "iTouch"
                    End If

                    Me.lblName.Text = String.Format("{0}'s {1}", device.DeviceName, strType)
                    Me.lblConnected.Text = Me.Language.GetString("Welcome.Label.Connected") '"Connected ..."
                    Me.btnOpen.tbText = Me.Language.GetString("Common.Button.Open")         '"Open"

                    Me.tmrShow.Start()
                    Me.TopMost = True
                    Me.Show()

                    'Debug.Print(Me.PNGForm.Location.ToString() & vbTab & Me.PNGForm.Size.ToString() & vbTab & Me.Visible.ToString())
                Catch ex As Exception
                End Try
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitData")
        End Try
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Dim scr As Screen = Screen.PrimaryScreen
        Me.Location = New Point(scr.WorkingArea.Right - Me.Width - 2, scr.WorkingArea.Bottom - Me.Height - 1)
    End Sub

    Private Shared mLocker As New Object()
    Private Shared mForm As frmNewDevice

    Private Sub tmrShow_Tick(ByVal sender As System.Object, ByVal e As System.Timers.ElapsedEventArgs)
        Me.HideForm()
    End Sub

    Public Sub HideForm()
        If Me.InvokeRequired Then
            Me.Invoke(New Threading.ThreadStart(AddressOf HideForm))
        Else
            Me.Hide()
        End If
    End Sub

#Region "--- 静态方法 ---"

    Public Shared Sub ShowDeviceInfoFrom(ByVal device As iPhoneDevice)
        If mForm Is Nothing Then
            SyncLock mLocker
                If mForm Is Nothing Then
                    mForm = New frmNewDevice()
                End If
            End SyncLock
        End If
        mForm.InitData(device)
    End Sub

    Public Shared Sub HideDeviceInfoFrom(ByVal device As iPhoneDevice)
        If mForm Is Nothing Then
            Return
        End If

        If mForm.mDevice IsNot Nothing AndAlso String.Compare(mForm.mDevice.Identifier, device.Identifier) = 0 Then
            If mForm.Visible Then
                mForm.HideForm()
            End If
        End If
    End Sub

#End Region

End Class
