﻿Imports System.Threading
Public Class frmHotApps

    Private mInfoDevice As New tbDeviceInfo
    Private mTdLoadApps As Thread = Nothing
    Private mFontTitle As Font = Nothing
    Private mFontNumbers As Font = Nothing
    Private mDictFileSharingPackageInfo As Dictionary(Of String, FileSharingPackageInfo)
    Private mItems As New Dictionary(Of String, AppItemRecom2HotApps)
    Private mAppItemRecom2HotAppsFont As Font = Nothing

    Private mAppSize As Long = 0
    Private mICount As Integer = 0
    Public Property ICount() As Integer
        Get
            Return Me.mICount
        End Get
        Set(ByVal value As Integer)
            If value >= 0 Then
                Me.mICount = value
                SetMsg()
            End If
        End Set
    End Property

    Private mIPhoneHotAppsHelper As iPhoneHotAppsHelper = iPhoneHotAppsHelper.GetInstance(Me.mDevice)

    Dim mLstInstalledPackage As New List(Of PackageInfo)
    Dim mLstUninstalledPackage As New List(Of PackageInfo)

    Private mApplePackageReader As ApplePackageReader = Nothing

#Region "----初始化、窗体闭关----"

    Public Sub New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
    End Sub

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice, ByVal infoDevice As tbDeviceInfo, ByVal dict As Dictionary(Of String, FileSharingPackageInfo))
        MyBase.New(application, device)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Me.Language = application.Language
        Me.mApplication = application
        Me.mDevice = device
        Me.mInfoDevice = infoDevice
        Me.FunctionMappingKey = FunctionKey.None

        Me.mDictFileSharingPackageInfo = dict

        Me.Icon = My.Resources.iTong
        Me.FilletRadius = 5
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.CanResize = False

        Me.btn_normal.Visible = False

        Me.mFontTitle = Common.CreateFont("宋体", 9.0F, FontStyle.Regular)
        Me.mFontNumbers = Common.CreateFont("宋体", 9.0F, FontStyle.Bold)
        Me.mAppItemRecom2HotAppsFont = Common.CreateFont("微软雅黑", 9.0F, FontStyle.Regular)
        RemoveHandler mIPhoneHotAppsHelper.OnekeyProgressEventHandler, AddressOf OnekeyProgress
        AddHandler mIPhoneHotAppsHelper.OnekeyProgressEventHandler, AddressOf OnekeyProgress
        ICount = 0

        Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
        Me.pnlLoading.BringToFront()
        btnInstall.Enabled = False

        Me.cbxUnShow.Checked = IIf(IniSetting.GetShowHotApps, False, True)

        Me.LoadApps()

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)

        Try
            If Me.mTdLoadApps IsNot Nothing AndAlso Me.mTdLoadApps.ThreadState <> ThreadState.Stopped Then
                Me.mTdLoadApps.Abort()
            End If
        Catch ex As Exception
        End Try

        'Try
        '    mIPhoneHotAppsHelper.Dispose()
        '    MyBase.BeforeFormClose(pblnCancelClose)
        'Catch ex As Exception
        '    Common.LogException(ex.ToString(), "frmHotApps_BeforeFormClose")
        'End Try
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As CoreFoundation.IDevice)
        MyBase.OnDisconnect(device)

        RemoveHandler mIPhoneHotAppsHelper.OnekeyProgressEventHandler, AddressOf OnekeyProgress
        Me.Close()
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)
        Try
            If CType(Me.mApplication, MainForm).Left < -2000 Then
                Me.Size = New Size(850, 642)
                Me.Location = New Point((Screen.PrimaryScreen.Bounds.Width - Me.Width) / 2, (Screen.PrimaryScreen.Bounds.Height - Me.Height) / 2)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmHotApps_OnShown")
        End Try
        Me.btn_minimize.Location = Me.btn_normal.Location
    End Sub

    Protected Overrides Sub OnSizeChanged(ByVal e As System.EventArgs)
        MyBase.OnSizeChanged(e)
        Me.btn_minimize.Location = Me.btn_normal.Location
    End Sub

#End Region

#Region "---- 窗体事件 ----"

    Private Sub btnInstall_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnInstall.Click
        Me.DoAllInstall()
    End Sub

    Private Sub cbxCheckAll_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cbxCheckAll.CheckedChanged
        For Each item As KeyValuePair(Of String, AppItemRecom2HotApps) In mItems
            If item.Value.Status <> AppIconStatus.None AndAlso item.Value.Status <> AppIconStatus.DownloadFailure AndAlso item.Value.Status <> AppIconStatus.Failure Then
                Continue For
            End If
            If item.Value.IsInstalled Then
                item.Value.IsChecked = False
            Else
                Dim pkgInfo As PackageInfo = CType(item.Value.Tag, PackageInfo)
                Dim isAdd As Boolean = True

                If (cbxCheckAll.Checked AndAlso Not item.Value.IsChecked) OrElse (Not cbxCheckAll.Checked AndAlso item.Value.IsChecked) Then
                    DoComputeCheckedInfo(item.Value, pkgInfo, cbxCheckAll.Checked)
                End If

                item.Value.IsChecked = cbxCheckAll.Checked
            End If
        Next
    End Sub

    Private Sub Item_CheckedClick(ByVal sender As System.Object, ByVal e As AppItemEventArgs)
        Try
            If sender IsNot Nothing AndAlso sender.tag IsNot Nothing Then
                Me.ComputeCheckedInfo(sender)

                Dim isAllCheck As Boolean = True
                For Each item As AppItemRecom2HotApps In Me.mItems.Values
                    If item.Status <> AppIconStatus.None OrElse item.Status <> AppIconStatus.DownloadFailure OrElse item.Status <> AppIconStatus.Failure Then
                        If Not item.IsChecked Then
                            isAllCheck = False
                            Exit For
                        End If
                    End If
                Next
                RemoveHandler cbxCheckAll.CheckedChanged, AddressOf cbxCheckAll_CheckedChanged
                Me.cbxCheckAll.Checked = isAllCheck
                AddHandler Me.cbxCheckAll.CheckedChanged, AddressOf cbxCheckAll_CheckedChanged

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Item_CheckedClick")
        End Try
    End Sub

    Private Sub cbxUnShow_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles cbxUnShow.CheckedChanged
        IniSetting.SetShowHotApps(IIf(cbxUnShow.Checked, False, True))
    End Sub

#End Region

#Region "---- 加载数据 ----"

    Private Sub LoadApps()
        If Me.mTdLoadApps IsNot Nothing AndAlso Me.mTdLoadApps.ThreadState <> Threading.ThreadState.Stopped Then
            Return
        End If

        Me.mTdLoadApps = New Thread(New Threading.ThreadStart(AddressOf DoLoadApps))
        Me.mTdLoadApps.IsBackground = True
        Me.mTdLoadApps.Start()
    End Sub

    Private Sub DoLoadApps()
        Try
            Me.mApplePackageReader = ApplePackageReader.Instance()
            mApplePackageReader.Load(Me.Name, False)

            '访问接口 获取数据
            Dim lstPackage As List(Of PackageInfo) = HtmlHelper.GetHotApps(Me.mDevice.IsIPad, Me.mDevice.Jailbreaked)

            '筛选 推荐App 当前设备 已安装与未安装的
            For Each item As PackageInfo In lstPackage
                If iPhoneDeviceHelper.CheckPackageIsInstalledUnCompareVersion(item, mDictFileSharingPackageInfo) Then
                    mLstInstalledPackage.Add(item)
                Else
                    mLstUninstalledPackage.Add(item)
                End If
            Next

            '先加载未安装
            If mLstUninstalledPackage.Count > 0 Then
                AddtbCheckButtonEx(mLstUninstalledPackage, False)
            End If

            '再加载已安装
            If mLstInstalledPackage.Count > 0 Then
                AddtbCheckButtonEx(mLstInstalledPackage, True)
            End If

            SetPnlLoad(False)

            Me.LoadIcon()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoLoadApps")
        End Try
    End Sub

    Private Sub LoadIcon()
        Try
            For Each Item As Control In Me.flpContainer.Controls
                If Item Is Nothing OrElse Not TypeOf Item Is AppItemRecom2HotApps Then
                    Continue For
                End If
                Dim AppItem As AppItemRecom2HotApps = Item

                Dim info As PackageInfo = AppItem.Tag
                Dim imgIcon As Image = My.Resources.app_icon_default.Clone

                Dim strIconPath As String = Path.Combine(Path.Combine(Folder.CacheFolder, "SummaryTemp"), "IHopApps" & Common.ReplaceWinIllegalName(info.AppIconUrl).Replace("_", ""))
                If Not File.Exists(strIconPath) Then
                    Utility.DownLoadFile(info.AppIconUrl, strIconPath)
                End If
                imgIcon = Utility.GetImageFormFile(strIconPath)

                If imgIcon IsNot Nothing Then
                    '委托主线程UI，执行完就不会占用
                    Me.UpdateRowIconToAppItemRecom2HotApps(AppItem, imgIcon)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadIcon")
        End Try
    End Sub

    Private Sub AddtbCheckButtonEx(ByVal lstPackage As List(Of PackageInfo), ByVal isIsInstalled As Boolean)
        Try
            For Each item As PackageInfo In lstPackage
                CreateItem(item, isIsInstalled)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddtbCheckButtonEx")
        End Try

    End Sub

    Private Delegate Sub CreateItemHandler(ByVal pkgInfo As PackageInfo, ByVal isIsInstalled As Boolean)
    Private Sub CreateItem(ByVal pkgInfo As PackageInfo, ByVal isIsInstalled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New CreateItemHandler(AddressOf CreateItem), pkgInfo, isIsInstalled)
        Else
            Try
                'Me.flpContainer.tbSuspendLayout()

                Dim item As New AppItemRecom2HotApps(pkgInfo.Name, Nothing, pkgInfo.Identifier, Nothing)
                item.Font = Me.mAppItemRecom2HotAppsFont
                item.Icon = Nothing  ' ItemRepair.pkgInfo.AppIconOriginal
                item.Tag = pkgInfo
                item.TipText = pkgInfo.Name
                item.IconRangeImage = My.Resources.summary_apprange
                item.IconCursor = Cursors.Default
                item.IsShowButton = False
                item.Status = AppIconStatus.None

                If isIsInstalled Then
                    '已安装
                    item.IsInstalled = True
                    item.IsChecked = False
                Else
                    '选择
                    item.IsChecked = cbxCheckAll.Checked
                    If cbxCheckAll.Checked Then
                        Me.mAppSize = Me.mAppSize + FormatSize(pkgInfo.Size)
                        Me.ICount = Me.ICount + 1
                    End If
                End If
                item.Tag = pkgInfo

                AddHandler item.CheckedClick, AddressOf Item_CheckedClick

                Me.flpContainer.Controls.Add(item)
                'RefleshAppItemIcon(item)

                If Not Me.mItems.ContainsKey(pkgInfo.Identifier) Then
                    Me.mItems.Add(pkgInfo.Identifier, item)
                End If

                'Me.flpContainer.tbResumeLayout()
                Application.DoEvents()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "CreateItem")
            End Try
        End If
    End Sub

    Private Delegate Sub SetPnlLoadHandler(ByVal isTop As Boolean)
    Private Sub SetPnlLoad(ByVal isTop As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetPnlLoadHandler(AddressOf SetPnlLoad), isTop)
        Else
            Try
                If isTop Then
                    Me.pnlLoading.BringToFront()
                    btnInstall.Enabled = False
                Else
                    Me.pnlLoading.SendToBack()
                    btnInstall.Enabled = True
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetPnlLoad")
            End Try

        End If
    End Sub

    Private Delegate Sub UpdateRowIconToAppItemRecom2HotAppsHandler(ByVal item As AppItemRecom2HotApps, ByVal appIcon As Image)
    Private Sub UpdateRowIconToAppItemRecom2HotApps(ByVal item As AppItemRecom2HotApps, ByVal appIcon As Image)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New UpdateRowIconToAppItemRecom2HotAppsHandler(AddressOf UpdateRowIconToAppItemRecom2HotApps), item, appIcon)
            Else
                If appIcon IsNot Nothing Then
                    item.Icon = appIcon.Clone()
                End If
                Application.DoEvents()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UpdateRowIconToAppItemRecom2HotApps")
        End Try
    End Sub

#End Region

#Region "---- 计算选中的信息 ----"

    Private Sub ComputeCheckedInfo(ByVal sender As System.Object)
        Dim item As AppItemRecom2HotApps = CType(sender, AppItemRecom2HotApps)
        Dim pkgInfo As PackageInfo = CType(item.Tag, PackageInfo)
        DoComputeCheckedInfo(item, pkgInfo, item.IsChecked)
    End Sub

    Private Sub DoComputeCheckedInfo(ByVal item As AppItemRecom2HotApps, ByVal pkgInfo As PackageInfo, ByVal isAdd As Boolean)
        If isAdd Then
            Me.mAppSize = Me.mAppSize + FormatSize(pkgInfo.Size)
            Me.ICount = Me.ICount + 1
        ElseIf Me.mICount >= 0 Then
            Me.mAppSize = Me.mAppSize - FormatSize(pkgInfo.Size)
            Me.ICount = Me.ICount - 1
        End If
    End Sub

    Private Function FormatSize(ByVal strSize As String) As Long
        Dim intReturn As Long = 0
        Try

            Dim strSizeEx As String = strSize.ToLower()
            If (strSizeEx.Contains("gb")) Then
                intReturn = CType(strSizeEx.Replace("gb", ""), Long) * 1073741824
            ElseIf (strSizeEx.Contains("mb")) Then
                intReturn = CType(strSizeEx.Replace("mb", ""), Long) * 1048576
            ElseIf (strSizeEx.Contains("kb")) Then
                intReturn = CType(strSizeEx.Replace("mb", ""), Long) * 1024
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "FormatSize")
        End Try
        Return intReturn
    End Function

    Private Sub SetMsg()
        Try
            Dim strAppSize As String = Utility.FormatFileSize(Me.mAppSize)
            Me.tbMsg.ColorTextList.Clear()
            Me.tbMsg.ColorTextList.AddItem("已选择", 0, System.Drawing.Color.FromArgb(68, 68, 68), mFontTitle, False)
            Me.tbMsg.ColorTextList.AddItem(mICount, 1, System.Drawing.Color.FromArgb(255, 131, 76), mFontNumbers, False)
            Me.tbMsg.ColorTextList.AddItem("款软件，共", 2, System.Drawing.Color.FromArgb(68, 68, 68), mFontTitle, False)
            Me.tbMsg.ColorTextList.AddItem(strAppSize, 3, System.Drawing.Color.FromArgb(255, 131, 76), mFontNumbers, False)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetMsg")
        End Try
    End Sub

#End Region

#Region "---- 一键安装 ----"

    Private Sub DoAllInstall()
        Try
            Dim pkgInfo As PackageInfo = Nothing
            Dim dicItem As New Dictionary(Of String, AppItemRecom2HotApps)
            For Each item As KeyValuePair(Of String, AppItemRecom2HotApps) In mItems
                If item.Value.IsChecked AndAlso (item.Value.Status = AppIconStatus.None OrElse item.Value.Status = AppIconStatus.DownloadFailure OrElse item.Value.Status = AppIconStatus.Failure) Then
                    dicItem.Add(item.Key, item.Value)
                End If
            Next
            If dicItem.Count <= 0 Then
                tbMessageBox.Show(Me, "请选择您要安装的应用？", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
                Return
            End If

            For Each item As KeyValuePair(Of String, AppItemRecom2HotApps) In dicItem
                item.Value.Status = AppIconStatus.Waiting
                item.Value.Text = "正在等待"
                Try
                    pkgInfo = CType(item.Value.Tag, PackageInfo)
                    If pkgInfo IsNot Nothing Then
                        ActionCollectHelper.HotAppsGetInformation(Me.mDevice, ModelKey.SummaryHotAppsDownloadAppOrInstall, ActionDataType.Click, FunctionSucceed.Succeed, pkgInfo.ItemId)
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoAllInstall_CType")
                End Try
            Next

            mIPhoneHotAppsHelper.OnekeyInstall(dicItem, Me.mApplication)

            Me.Close()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoAllInstall")
        End Try
    End Sub

    Private Sub OnekeyProgress(ByVal sender As Object, ByVal args As OnekeyInstallProgressEventArgs)

        If Not Me.mItems.ContainsKey(args.StrSku) Then
            Return
        End If
        Dim appItem As AppItemRecom2HotApps = Me.mItems.Item(args.StrSku)
        Dim appState As InstallState = args.InstState

        '进度反馈
        Me.SetIconStatus(appItem, appState, args.IntProgress)

    End Sub

    Private Delegate Sub SetIconStatusHandler(ByVal itemRecom As AppItemRecom2HotApps, ByVal status As AppIconStatus, ByVal intProgress As Integer)
    Private Sub SetIconStatus(ByVal itemRecom As AppItemRecom2HotApps, ByVal status As AppIconStatus, ByVal intProgress As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New SetIconStatusHandler(AddressOf SetIconStatus), itemRecom, status, intProgress)
        Else
            itemRecom.Status = status
            itemRecom.Progress = intProgress
            Select Case status
                Case AppIconStatus.None
                    itemRecom.Text = itemRecom.TipText
                    itemRecom.IsChecked = False

                Case AppIconStatus.WaitDownloading
                    itemRecom.Text = "等待下载"

                Case AppIconStatus.Downloading
                    itemRecom.Text = String.Format("下载{0}%", intProgress)

                Case AppIconStatus.DownloadFailure
                    itemRecom.Text = "下载失败"

                Case AppIconStatus.Waiting
                    itemRecom.Text = "等待安装"

                Case AppIconStatus.Transfering
                    itemRecom.Text = String.Format("上传{0}%", intProgress)

                Case AppIconStatus.Installing
                    itemRecom.Text = String.Format("安装{0}%", intProgress)

                Case AppIconStatus.Succeed
                    itemRecom.Text = "安装成功"
                    itemRecom.IsInstalled = True
                    itemRecom.IsChecked = False

                Case AppIconStatus.Failure
                    itemRecom.Text = "安装失败"

                Case AppIconStatus.Unavailable
                    itemRecom.Text = itemRecom.TipText


            End Select
        End If
    End Sub

#End Region

End Class