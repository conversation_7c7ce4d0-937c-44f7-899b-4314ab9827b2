﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmPreview
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmPreview))
        Me.pnlbg = New System.Windows.Forms.Panel()
        Me.pnlLoading = New iTong.Components.tbPanel()
        Me.lblLoading = New iTong.Components.tbLabel()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.lblMessage = New System.Windows.Forms.Label()
        Me.btnSaveToPC = New iTong.Components.tbButton()
        Me.btnCopyToClipboard = New iTong.Components.tbButton()
        Me.pbScreenshot = New System.Windows.Forms.PictureBox()
        Me.pnlScreenshot = New iTong.Components.tbPanel()
        Me.btnScreenshotSetUp = New iTong.Components.tbButton()
        Me.btnScreenshot = New iTong.Components.tbButton()
        Me.btn_close = New iTong.Components.tbButton()
        Me.picTopSplit = New System.Windows.Forms.PictureBox()
        Me.munSetUp = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiScreenshot = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiScreenshotShell = New System.Windows.Forms.ToolStripMenuItem()
        Me.lblState = New System.Windows.Forms.Label()
        Me.pnlbg.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.pbScreenshot, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlScreenshot.SuspendLayout()
        CType(Me.picTopSplit, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.munSetUp.SuspendLayout()
        Me.SuspendLayout()
        '
        'pnlbg
        '
        Me.pnlbg.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlbg.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink
        Me.pnlbg.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlbg.Controls.Add(Me.pnlLoading)
        Me.pnlbg.Controls.Add(Me.lblMessage)
        Me.pnlbg.Controls.Add(Me.btnSaveToPC)
        Me.pnlbg.Controls.Add(Me.btnCopyToClipboard)
        Me.pnlbg.Controls.Add(Me.pbScreenshot)
        Me.pnlbg.Controls.Add(Me.pnlScreenshot)
        Me.pnlbg.Location = New System.Drawing.Point(1, 30)
        Me.pnlbg.Name = "pnlbg"
        Me.pnlbg.Size = New System.Drawing.Size(468, 402)
        Me.pnlbg.TabIndex = 0
        '
        'pnlLoading
        '
        Me.pnlLoading.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(136, 119)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(200, 100)
        Me.pnlLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoading.TabIndex = 26
        Me.pnlLoading.tbBackgroundImage = Nothing
        Me.pnlLoading.tbShowWatermark = False
        Me.pnlLoading.tbSplit = "0,0,0,0"
        Me.pnlLoading.tbWatermark = Nothing
        Me.pnlLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(95, 34)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(101, 33)
        Me.lblLoading.TabIndex = 27
        Me.lblLoading.tbAdriftWhenHover = False
        Me.lblLoading.tbAutoEllipsis = False
        Me.lblLoading.tbAutoSize = False
        Me.lblLoading.tbHideImage = False
        Me.lblLoading.tbIconImage = Nothing
        Me.lblLoading.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLoading.tbIconPlaceText = 5
        Me.lblLoading.tbShadow = False
        Me.lblLoading.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLoading.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLoading.tbShowScrolling = False
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbLoading.Location = New System.Drawing.Point(3, 3)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(91, 95)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 26
        Me.pbLoading.TabStop = False
        '
        'lblMessage
        '
        Me.lblMessage.BackColor = System.Drawing.Color.Black
        Me.lblMessage.Font = New System.Drawing.Font("宋体", 9.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblMessage.ForeColor = System.Drawing.Color.White
        Me.lblMessage.Location = New System.Drawing.Point(146, 135)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(176, 82)
        Me.lblMessage.TabIndex = 23
        Me.lblMessage.Text = "截取图片均为设备实时界面。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "如遇黑屏，请解锁设备重新截屏。"
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.Visible = False
        '
        'btnSaveToPC
        '
        Me.btnSaveToPC.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSaveToPC.BackColor = System.Drawing.Color.Transparent
        Me.btnSaveToPC.BindingForm = Nothing
        Me.btnSaveToPC.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSaveToPC.Location = New System.Drawing.Point(263, 364)
        Me.btnSaveToPC.Name = "btnSaveToPC"
        Me.btnSaveToPC.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSaveToPC.Selectable = True
        Me.btnSaveToPC.Size = New System.Drawing.Size(70, 25)
        Me.btnSaveToPC.TabIndex = 22
        Me.btnSaveToPC.tbAdriftIconWhenHover = False
        Me.btnSaveToPC.tbAutoSize = False
        Me.btnSaveToPC.tbAutoSizeEx = False
        Me.btnSaveToPC.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnSaveToPC.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnSaveToPC.tbBadgeNumber = 0
        Me.btnSaveToPC.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSaveToPC.tbEndEllipsis = False
        Me.btnSaveToPC.tbIconHoldPlace = True
        Me.btnSaveToPC.tbIconImage = Nothing
        Me.btnSaveToPC.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSaveToPC.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSaveToPC.tbIconMore = False
        Me.btnSaveToPC.tbIconMouseDown = Nothing
        Me.btnSaveToPC.tbIconMouseHover = Nothing
        Me.btnSaveToPC.tbIconMouseLeave = Nothing
        Me.btnSaveToPC.tbIconPlaceText = 2
        Me.btnSaveToPC.tbIconReadOnly = Nothing
        Me.btnSaveToPC.tbImageMouseDown = Nothing
        Me.btnSaveToPC.tbImageMouseHover = Nothing
        Me.btnSaveToPC.tbImageMouseLeave = Nothing
        Me.btnSaveToPC.tbProgressValue = 50
        Me.btnSaveToPC.tbReadOnly = False
        Me.btnSaveToPC.tbReadOnlyText = False
        Me.btnSaveToPC.tbShadow = False
        Me.btnSaveToPC.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnSaveToPC.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnSaveToPC.tbShowDot = False
        Me.btnSaveToPC.tbShowMoreIconImg = CType(resources.GetObject("btnSaveToPC.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSaveToPC.tbShowNew = False
        Me.btnSaveToPC.tbShowProgress = False
        Me.btnSaveToPC.tbShowTip = True
        Me.btnSaveToPC.tbShowToolTipOnButton = False
        Me.btnSaveToPC.tbSplit = "13,11,13,11"
        Me.btnSaveToPC.tbText = "保存"
        Me.btnSaveToPC.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSaveToPC.tbTextColor = System.Drawing.Color.White
        Me.btnSaveToPC.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSaveToPC.tbTextColorDown = System.Drawing.Color.White
        Me.btnSaveToPC.tbTextColorHover = System.Drawing.Color.White
        Me.btnSaveToPC.tbTextMouseDownPlace = 0
        Me.btnSaveToPC.tbToolTip = ""
        Me.btnSaveToPC.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSaveToPC.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSaveToPC.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSaveToPC.VisibleEx = True
        '
        'btnCopyToClipboard
        '
        Me.btnCopyToClipboard.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCopyToClipboard.BackColor = System.Drawing.Color.Transparent
        Me.btnCopyToClipboard.BindingForm = Nothing
        Me.btnCopyToClipboard.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCopyToClipboard.Location = New System.Drawing.Point(136, 364)
        Me.btnCopyToClipboard.Name = "btnCopyToClipboard"
        Me.btnCopyToClipboard.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCopyToClipboard.Selectable = True
        Me.btnCopyToClipboard.Size = New System.Drawing.Size(70, 25)
        Me.btnCopyToClipboard.TabIndex = 21
        Me.btnCopyToClipboard.tbAdriftIconWhenHover = False
        Me.btnCopyToClipboard.tbAutoSize = False
        Me.btnCopyToClipboard.tbAutoSizeEx = False
        Me.btnCopyToClipboard.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnCopyToClipboard.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCopyToClipboard.tbBadgeNumber = 0
        Me.btnCopyToClipboard.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopyToClipboard.tbEndEllipsis = False
        Me.btnCopyToClipboard.tbIconHoldPlace = True
        Me.btnCopyToClipboard.tbIconImage = Nothing
        Me.btnCopyToClipboard.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyToClipboard.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCopyToClipboard.tbIconMore = False
        Me.btnCopyToClipboard.tbIconMouseDown = Nothing
        Me.btnCopyToClipboard.tbIconMouseHover = Nothing
        Me.btnCopyToClipboard.tbIconMouseLeave = Nothing
        Me.btnCopyToClipboard.tbIconPlaceText = 2
        Me.btnCopyToClipboard.tbIconReadOnly = Nothing
        Me.btnCopyToClipboard.tbImageMouseDown = Nothing
        Me.btnCopyToClipboard.tbImageMouseHover = Nothing
        Me.btnCopyToClipboard.tbImageMouseLeave = Nothing
        Me.btnCopyToClipboard.tbProgressValue = 50
        Me.btnCopyToClipboard.tbReadOnly = False
        Me.btnCopyToClipboard.tbReadOnlyText = False
        Me.btnCopyToClipboard.tbShadow = False
        Me.btnCopyToClipboard.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnCopyToClipboard.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCopyToClipboard.tbShowDot = False
        Me.btnCopyToClipboard.tbShowMoreIconImg = CType(resources.GetObject("btnCopyToClipboard.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCopyToClipboard.tbShowNew = False
        Me.btnCopyToClipboard.tbShowProgress = False
        Me.btnCopyToClipboard.tbShowTip = True
        Me.btnCopyToClipboard.tbShowToolTipOnButton = False
        Me.btnCopyToClipboard.tbSplit = "13,11,13,11"
        Me.btnCopyToClipboard.tbText = "复制"
        Me.btnCopyToClipboard.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopyToClipboard.tbTextColor = System.Drawing.Color.White
        Me.btnCopyToClipboard.tbTextColorDisable = System.Drawing.Color.White
        Me.btnCopyToClipboard.tbTextColorDown = System.Drawing.Color.White
        Me.btnCopyToClipboard.tbTextColorHover = System.Drawing.Color.White
        Me.btnCopyToClipboard.tbTextMouseDownPlace = 0
        Me.btnCopyToClipboard.tbToolTip = ""
        Me.btnCopyToClipboard.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCopyToClipboard.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopyToClipboard.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnCopyToClipboard.VisibleEx = True
        '
        'pbScreenshot
        '
        Me.pbScreenshot.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.pbScreenshot.Location = New System.Drawing.Point(4, 11)
        Me.pbScreenshot.Margin = New System.Windows.Forms.Padding(0)
        Me.pbScreenshot.Name = "pbScreenshot"
        Me.pbScreenshot.Size = New System.Drawing.Size(461, 304)
        Me.pbScreenshot.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.pbScreenshot.TabIndex = 9
        Me.pbScreenshot.TabStop = False
        '
        'pnlScreenshot
        '
        Me.pnlScreenshot.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshotSetUp)
        Me.pnlScreenshot.Controls.Add(Me.btnScreenshot)
        Me.pnlScreenshot.Location = New System.Drawing.Point(194, 323)
        Me.pnlScreenshot.Name = "pnlScreenshot"
        Me.pnlScreenshot.Size = New System.Drawing.Size(80, 28)
        Me.pnlScreenshot.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlScreenshot.TabIndex = 8
        Me.pnlScreenshot.tbBackgroundImage = Nothing
        Me.pnlScreenshot.tbShowWatermark = False
        Me.pnlScreenshot.tbSplit = "5,5,5,5"
        Me.pnlScreenshot.tbWatermark = Nothing
        Me.pnlScreenshot.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlScreenshot.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnScreenshotSetUp
        '
        Me.btnScreenshotSetUp.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnScreenshotSetUp.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshotSetUp.BindingForm = Nothing
        Me.btnScreenshotSetUp.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshotSetUp.Location = New System.Drawing.Point(47, 5)
        Me.btnScreenshotSetUp.Name = "btnScreenshotSetUp"
        Me.btnScreenshotSetUp.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshotSetUp.Selectable = True
        Me.btnScreenshotSetUp.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshotSetUp.TabIndex = 3
        Me.btnScreenshotSetUp.TabStop = False
        Me.btnScreenshotSetUp.tbAdriftIconWhenHover = False
        Me.btnScreenshotSetUp.tbAutoSize = False
        Me.btnScreenshotSetUp.tbAutoSizeEx = False
        Me.btnScreenshotSetUp.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_Setup
        Me.btnScreenshotSetUp.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnScreenshotSetUp.tbBadgeNumber = 0
        Me.btnScreenshotSetUp.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshotSetUp.tbEndEllipsis = False
        Me.btnScreenshotSetUp.tbIconHoldPlace = True
        Me.btnScreenshotSetUp.tbIconImage = Nothing
        Me.btnScreenshotSetUp.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshotSetUp.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshotSetUp.tbIconMore = False
        Me.btnScreenshotSetUp.tbIconMouseDown = Nothing
        Me.btnScreenshotSetUp.tbIconMouseHover = Nothing
        Me.btnScreenshotSetUp.tbIconMouseLeave = Nothing
        Me.btnScreenshotSetUp.tbIconPlaceText = 2
        Me.btnScreenshotSetUp.tbIconReadOnly = Nothing
        Me.btnScreenshotSetUp.tbImageMouseDown = Nothing
        Me.btnScreenshotSetUp.tbImageMouseHover = Nothing
        Me.btnScreenshotSetUp.tbImageMouseLeave = Nothing
        Me.btnScreenshotSetUp.tbProgressValue = 50
        Me.btnScreenshotSetUp.tbReadOnly = False
        Me.btnScreenshotSetUp.tbReadOnlyText = False
        Me.btnScreenshotSetUp.tbShadow = False
        Me.btnScreenshotSetUp.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshotSetUp.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshotSetUp.tbShowDot = False
        Me.btnScreenshotSetUp.tbShowMoreIconImg = CType(resources.GetObject("btnScreenshotSetUp.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnScreenshotSetUp.tbShowNew = False
        Me.btnScreenshotSetUp.tbShowProgress = False
        Me.btnScreenshotSetUp.tbShowTip = True
        Me.btnScreenshotSetUp.tbShowToolTipOnButton = False
        Me.btnScreenshotSetUp.tbSplit = "0,0,0,0"
        Me.btnScreenshotSetUp.tbText = ""
        Me.btnScreenshotSetUp.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotSetUp.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshotSetUp.tbTextMouseDownPlace = 0
        Me.btnScreenshotSetUp.tbToolTip = ""
        Me.btnScreenshotSetUp.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshotSetUp.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshotSetUp.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshotSetUp.VisibleEx = True
        '
        'btnScreenshot
        '
        Me.btnScreenshot.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnScreenshot.BackColor = System.Drawing.Color.Transparent
        Me.btnScreenshot.BindingForm = Nothing
        Me.btnScreenshot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnScreenshot.Location = New System.Drawing.Point(16, 5)
        Me.btnScreenshot.Name = "btnScreenshot"
        Me.btnScreenshot.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnScreenshot.Selectable = True
        Me.btnScreenshot.Size = New System.Drawing.Size(18, 18)
        Me.btnScreenshot.TabIndex = 0
        Me.btnScreenshot.TabStop = False
        Me.btnScreenshot.tbAdriftIconWhenHover = False
        Me.btnScreenshot.tbAutoSize = False
        Me.btnScreenshot.tbAutoSizeEx = False
        Me.btnScreenshot.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_refresh
        Me.btnScreenshot.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnScreenshot.tbBadgeNumber = 0
        Me.btnScreenshot.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnScreenshot.tbEndEllipsis = False
        Me.btnScreenshot.tbIconHoldPlace = True
        Me.btnScreenshot.tbIconImage = Nothing
        Me.btnScreenshot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnScreenshot.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnScreenshot.tbIconMore = False
        Me.btnScreenshot.tbIconMouseDown = Nothing
        Me.btnScreenshot.tbIconMouseHover = Nothing
        Me.btnScreenshot.tbIconMouseLeave = Nothing
        Me.btnScreenshot.tbIconPlaceText = 2
        Me.btnScreenshot.tbIconReadOnly = Nothing
        Me.btnScreenshot.tbImageMouseDown = Nothing
        Me.btnScreenshot.tbImageMouseHover = Nothing
        Me.btnScreenshot.tbImageMouseLeave = Nothing
        Me.btnScreenshot.tbProgressValue = 50
        Me.btnScreenshot.tbReadOnly = False
        Me.btnScreenshot.tbReadOnlyText = False
        Me.btnScreenshot.tbShadow = False
        Me.btnScreenshot.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnScreenshot.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnScreenshot.tbShowDot = False
        Me.btnScreenshot.tbShowMoreIconImg = CType(resources.GetObject("btnScreenshot.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnScreenshot.tbShowNew = False
        Me.btnScreenshot.tbShowProgress = False
        Me.btnScreenshot.tbShowTip = True
        Me.btnScreenshot.tbShowToolTipOnButton = False
        Me.btnScreenshot.tbSplit = "0,0,0,0"
        Me.btnScreenshot.tbText = ""
        Me.btnScreenshot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshot.tbTextColor = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorDown = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextColorHover = System.Drawing.Color.Black
        Me.btnScreenshot.tbTextMouseDownPlace = 0
        Me.btnScreenshot.tbToolTip = ""
        Me.btnScreenshot.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnScreenshot.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnScreenshot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnScreenshot.VisibleEx = True
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(445, 1)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 10
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'picTopSplit
        '
        Me.picTopSplit.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.picTopSplit.BackColor = System.Drawing.Color.Transparent
        Me.picTopSplit.Location = New System.Drawing.Point(435, 4)
        Me.picTopSplit.Name = "picTopSplit"
        Me.picTopSplit.Size = New System.Drawing.Size(2, 22)
        Me.picTopSplit.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picTopSplit.TabIndex = 25
        Me.picTopSplit.TabStop = False
        '
        'munSetUp
        '
        Me.munSetUp.DropShadowEnabled = False
        Me.munSetUp.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiScreenshot, Me.tsmiScreenshotShell})
        Me.munSetUp.Name = "munSearch"
        Me.munSetUp.Size = New System.Drawing.Size(145, 48)
        Me.munSetUp.tbBackColor = System.Drawing.Color.White
        Me.munSetUp.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munSetUp.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munSetUp.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiScreenshot
        '
        Me.tsmiScreenshot.Name = "tsmiScreenshot"
        Me.tsmiScreenshot.Size = New System.Drawing.Size(144, 22)
        Me.tsmiScreenshot.Text = "截屏(无外壳)"
        '
        'tsmiScreenshotShell
        '
        Me.tsmiScreenshotShell.Name = "tsmiScreenshotShell"
        Me.tsmiScreenshotShell.Size = New System.Drawing.Size(144, 22)
        Me.tsmiScreenshotShell.Text = "截屏(带外壳)"
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(14, 436)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(406, 21)
        Me.lblState.TabIndex = 26
        Me.lblState.Text = "提示信息"
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmPreview
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(470, 460)
        Me.Controls.Add(Me.lblState)
        Me.Controls.Add(Me.picTopSplit)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.pnlbg)
        Me.FilletRadius = 5
        Me.MinimumSize = New System.Drawing.Size(20, 66)
        Me.Name = "frmPreview"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_state
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,33"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.Text = "截屏"
        Me.pnlbg.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.pbScreenshot, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlScreenshot.ResumeLayout(False)
        CType(Me.picTopSplit, System.ComponentModel.ISupportInitialize).EndInit()
        Me.munSetUp.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents pnlbg As System.Windows.Forms.Panel
    Friend WithEvents btn_close As tbButton
    Friend WithEvents picTopSplit As System.Windows.Forms.PictureBox
    Friend WithEvents pnlScreenshot As tbPanel
    Friend WithEvents btnScreenshotSetUp As tbButton
    Friend WithEvents btnScreenshot As tbButton
    Friend WithEvents pbScreenshot As System.Windows.Forms.PictureBox
    Friend WithEvents btnCopyToClipboard As tbButton
    Friend WithEvents btnSaveToPC As tbButton
    Friend WithEvents tsmiScreenshot As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiScreenshotShell As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lblMessage As System.Windows.Forms.Label
    Friend WithEvents munSetUp As tbContextMenuStrip
    Friend WithEvents pnlLoading As tbPanel
    Friend WithEvents lblLoading As tbLabel
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents lblState As System.Windows.Forms.Label
End Class
