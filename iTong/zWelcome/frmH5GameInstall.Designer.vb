﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmH5GameInstall
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmH5GameInstall))
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.pnlInstallH5Game = New iTong.Components.tbPanel()
        Me.btnOk = New iTong.Components.tbButton()
        Me.lblStepThree = New System.Windows.Forms.Label()
        Me.btnStep3 = New iTong.Components.tbButton()
        Me.lblStepTwo = New System.Windows.Forms.Label()
        Me.btnStep2 = New iTong.Components.tbButton()
        Me.lblStepOne = New System.Windows.Forms.Label()
        Me.btnStep1 = New iTong.Components.tbButton()
        Me.picOperateStep = New iTong.Components.tbPictureBox()
        Me.lblInstallTip = New iTong.Components.tbLabel()
        Me.lblInstallH5Game = New iTong.Components.tbLabel()
        Me.btnInstall = New iTong.Components.tbButton()
        Me.pnlContainer.SuspendLayout()
        Me.pnlInstallH5Game.SuspendLayout()
        CType(Me.picOperateStep, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(813, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(789, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(765, 0)
        '
        'pnlContainer
        '
        Me.pnlContainer.Controls.Add(Me.pnlInstallH5Game)
        Me.pnlContainer.Controls.Add(Me.btnInstall)
        Me.pnlContainer.Location = New System.Drawing.Point(1, 31)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(835, 572)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 22
        Me.pnlContainer.tbBackgroundImage = Global.iTong.My.Resources.Resources.H5Game_install_step2
        Me.pnlContainer.tbShowWatermark = True
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Global.iTong.My.Resources.Resources.H5Game_install_step1
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlInstallH5Game
        '
        Me.pnlInstallH5Game.BackColor = System.Drawing.Color.FromArgb(CType(CType(244, Byte), Integer), CType(CType(245, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlInstallH5Game.Controls.Add(Me.btnOk)
        Me.pnlInstallH5Game.Controls.Add(Me.lblStepThree)
        Me.pnlInstallH5Game.Controls.Add(Me.btnStep3)
        Me.pnlInstallH5Game.Controls.Add(Me.lblStepTwo)
        Me.pnlInstallH5Game.Controls.Add(Me.btnStep2)
        Me.pnlInstallH5Game.Controls.Add(Me.lblStepOne)
        Me.pnlInstallH5Game.Controls.Add(Me.btnStep1)
        Me.pnlInstallH5Game.Controls.Add(Me.picOperateStep)
        Me.pnlInstallH5Game.Controls.Add(Me.lblInstallTip)
        Me.pnlInstallH5Game.Controls.Add(Me.lblInstallH5Game)
        Me.pnlInstallH5Game.Location = New System.Drawing.Point(0, 0)
        Me.pnlInstallH5Game.Name = "pnlInstallH5Game"
        Me.pnlInstallH5Game.Size = New System.Drawing.Size(136, 572)
        Me.pnlInstallH5Game.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlInstallH5Game.TabIndex = 56
        Me.pnlInstallH5Game.tbBackgroundImage = Global.iTong.My.Resources.Resources.H5Game_install_step2
        Me.pnlInstallH5Game.tbShowWatermark = False
        Me.pnlInstallH5Game.tbSplit = "0,0,0,0"
        Me.pnlInstallH5Game.tbWatermark = Nothing
        Me.pnlInstallH5Game.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlInstallH5Game.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlInstallH5Game.Visible = False
        '
        'btnOk
        '
        Me.btnOk.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnOk.BackColor = System.Drawing.Color.Transparent
        Me.btnOk.BindingForm = Nothing
        Me.btnOk.Font = New System.Drawing.Font("宋体", 16.0!)
        Me.btnOk.Location = New System.Drawing.Point(-12, 473)
        Me.btnOk.Name = "btnOk"
        Me.btnOk.Padding = New System.Windows.Forms.Padding(15, 2, 15, 2)
        Me.btnOk.Selectable = True
        Me.btnOk.Size = New System.Drawing.Size(161, 45)
        Me.btnOk.TabIndex = 121
        Me.btnOk.tbAdriftIconWhenHover = False
        Me.btnOk.tbAutoSize = False
        Me.btnOk.tbAutoSizeEx = False
        Me.btnOk.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue1
        Me.btnOk.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOk.tbBadgeNumber = 0
        Me.btnOk.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOk.tbEndEllipsis = False
        Me.btnOk.tbIconHoldPlace = True
        Me.btnOk.tbIconImage = Nothing
        Me.btnOk.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOk.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnOk.tbIconMore = False
        Me.btnOk.tbIconMouseDown = Nothing
        Me.btnOk.tbIconMouseHover = Nothing
        Me.btnOk.tbIconMouseLeave = Nothing
        Me.btnOk.tbIconPlaceText = 2
        Me.btnOk.tbIconReadOnly = Nothing
        Me.btnOk.tbImageMouseDown = Nothing
        Me.btnOk.tbImageMouseHover = Nothing
        Me.btnOk.tbImageMouseLeave = Nothing
        Me.btnOk.tbProgressValue = 50
        Me.btnOk.tbReadOnly = False
        Me.btnOk.tbReadOnlyText = False
        Me.btnOk.tbShadow = False
        Me.btnOk.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOk.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOk.tbShowDot = False
        Me.btnOk.tbShowMoreIconImg = CType(resources.GetObject("btnOk.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOk.tbShowNew = False
        Me.btnOk.tbShowProgress = False
        Me.btnOk.tbShowTip = True
        Me.btnOk.tbShowToolTipOnButton = False
        Me.btnOk.tbSplit = "15,15,15,15"
        Me.btnOk.tbText = "我知道了"
        Me.btnOk.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOk.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.btnOk.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.btnOk.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.btnOk.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.btnOk.tbTextMouseDownPlace = 0
        Me.btnOk.tbToolTip = ""
        Me.btnOk.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOk.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOk.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOk.VisibleEx = True
        '
        'lblStepThree
        '
        Me.lblStepThree.BackColor = System.Drawing.Color.Transparent
        Me.lblStepThree.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStepThree.ForeColor = System.Drawing.Color.White
        Me.lblStepThree.Location = New System.Drawing.Point(637, 406)
        Me.lblStepThree.Name = "lblStepThree"
        Me.lblStepThree.Size = New System.Drawing.Size(151, 51)
        Me.lblStepThree.TabIndex = 32
        Me.lblStepThree.Text = "即可在桌面查看使用"
        '
        'btnStep3
        '
        Me.btnStep3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep3.BackColor = System.Drawing.Color.Transparent
        Me.btnStep3.BindingForm = Nothing
        Me.btnStep3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep3.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep3.Location = New System.Drawing.Point(260, 405)
        Me.btnStep3.Name = "btnStep3"
        Me.btnStep3.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep3.Selectable = True
        Me.btnStep3.Size = New System.Drawing.Size(18, 17)
        Me.btnStep3.TabIndex = 33
        Me.btnStep3.tbAdriftIconWhenHover = False
        Me.btnStep3.tbAutoSize = False
        Me.btnStep3.tbAutoSizeEx = True
        Me.btnStep3.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep3
        Me.btnStep3.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep3.tbBadgeNumber = 0
        Me.btnStep3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep3.tbEndEllipsis = False
        Me.btnStep3.tbIconHoldPlace = True
        Me.btnStep3.tbIconImage = Nothing
        Me.btnStep3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep3.tbIconMore = False
        Me.btnStep3.tbIconMouseDown = Nothing
        Me.btnStep3.tbIconMouseHover = Nothing
        Me.btnStep3.tbIconMouseLeave = Nothing
        Me.btnStep3.tbIconPlaceText = 2
        Me.btnStep3.tbIconReadOnly = Nothing
        Me.btnStep3.tbImageMouseDown = Nothing
        Me.btnStep3.tbImageMouseHover = Nothing
        Me.btnStep3.tbImageMouseLeave = Nothing
        Me.btnStep3.tbProgressValue = 50
        Me.btnStep3.tbReadOnly = False
        Me.btnStep3.tbReadOnlyText = False
        Me.btnStep3.tbShadow = False
        Me.btnStep3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep3.tbShowDot = False
        Me.btnStep3.tbShowMoreIconImg = CType(resources.GetObject("btnStep3.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep3.tbShowNew = False
        Me.btnStep3.tbShowProgress = False
        Me.btnStep3.tbShowTip = True
        Me.btnStep3.tbShowToolTipOnButton = False
        Me.btnStep3.tbSplit = "0,0,0,0"
        Me.btnStep3.tbText = ""
        Me.btnStep3.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.tbTextColor = System.Drawing.Color.White
        Me.btnStep3.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep3.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep3.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep3.tbTextMouseDownPlace = 0
        Me.btnStep3.tbToolTip = ""
        Me.btnStep3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep3.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.VisibleEx = True
        '
        'lblStepTwo
        '
        Me.lblStepTwo.BackColor = System.Drawing.Color.Transparent
        Me.lblStepTwo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStepTwo.ForeColor = System.Drawing.Color.White
        Me.lblStepTwo.Location = New System.Drawing.Point(402, 406)
        Me.lblStepTwo.Name = "lblStepTwo"
        Me.lblStepTwo.Size = New System.Drawing.Size(124, 51)
        Me.lblStepTwo.TabIndex = 32
        Me.lblStepTwo.Text = "确定安装"
        '
        'btnStep2
        '
        Me.btnStep2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep2.BackColor = System.Drawing.Color.Transparent
        Me.btnStep2.BindingForm = Nothing
        Me.btnStep2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep2.Location = New System.Drawing.Point(27, 405)
        Me.btnStep2.Name = "btnStep2"
        Me.btnStep2.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep2.Selectable = True
        Me.btnStep2.Size = New System.Drawing.Size(18, 17)
        Me.btnStep2.TabIndex = 33
        Me.btnStep2.tbAdriftIconWhenHover = False
        Me.btnStep2.tbAutoSize = False
        Me.btnStep2.tbAutoSizeEx = True
        Me.btnStep2.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep2
        Me.btnStep2.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep2.tbBadgeNumber = 0
        Me.btnStep2.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep2.tbEndEllipsis = False
        Me.btnStep2.tbIconHoldPlace = True
        Me.btnStep2.tbIconImage = Nothing
        Me.btnStep2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep2.tbIconMore = False
        Me.btnStep2.tbIconMouseDown = Nothing
        Me.btnStep2.tbIconMouseHover = Nothing
        Me.btnStep2.tbIconMouseLeave = Nothing
        Me.btnStep2.tbIconPlaceText = 2
        Me.btnStep2.tbIconReadOnly = Nothing
        Me.btnStep2.tbImageMouseDown = Nothing
        Me.btnStep2.tbImageMouseHover = Nothing
        Me.btnStep2.tbImageMouseLeave = Nothing
        Me.btnStep2.tbProgressValue = 50
        Me.btnStep2.tbReadOnly = False
        Me.btnStep2.tbReadOnlyText = False
        Me.btnStep2.tbShadow = False
        Me.btnStep2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep2.tbShowDot = False
        Me.btnStep2.tbShowMoreIconImg = CType(resources.GetObject("btnStep2.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep2.tbShowNew = False
        Me.btnStep2.tbShowProgress = False
        Me.btnStep2.tbShowTip = True
        Me.btnStep2.tbShowToolTipOnButton = False
        Me.btnStep2.tbSplit = "0,0,0,0"
        Me.btnStep2.tbText = ""
        Me.btnStep2.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.tbTextColor = System.Drawing.Color.White
        Me.btnStep2.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep2.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep2.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep2.tbTextMouseDownPlace = 0
        Me.btnStep2.tbToolTip = ""
        Me.btnStep2.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep2.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep2.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.VisibleEx = True
        '
        'lblStepOne
        '
        Me.lblStepOne.BackColor = System.Drawing.Color.Transparent
        Me.lblStepOne.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStepOne.ForeColor = System.Drawing.Color.White
        Me.lblStepOne.Location = New System.Drawing.Point(131, 406)
        Me.lblStepOne.Name = "lblStepOne"
        Me.lblStepOne.Size = New System.Drawing.Size(140, 51)
        Me.lblStepOne.TabIndex = 32
        Me.lblStepOne.Text = "点击安装"
        '
        'btnStep1
        '
        Me.btnStep1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep1.BackColor = System.Drawing.Color.Transparent
        Me.btnStep1.BindingForm = Nothing
        Me.btnStep1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep1.Location = New System.Drawing.Point(-246, 405)
        Me.btnStep1.Name = "btnStep1"
        Me.btnStep1.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep1.Selectable = True
        Me.btnStep1.Size = New System.Drawing.Size(18, 17)
        Me.btnStep1.TabIndex = 33
        Me.btnStep1.tbAdriftIconWhenHover = False
        Me.btnStep1.tbAutoSize = False
        Me.btnStep1.tbAutoSizeEx = True
        Me.btnStep1.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep1
        Me.btnStep1.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep1.tbBadgeNumber = 0
        Me.btnStep1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep1.tbEndEllipsis = False
        Me.btnStep1.tbIconHoldPlace = True
        Me.btnStep1.tbIconImage = Nothing
        Me.btnStep1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep1.tbIconMore = False
        Me.btnStep1.tbIconMouseDown = Nothing
        Me.btnStep1.tbIconMouseHover = Nothing
        Me.btnStep1.tbIconMouseLeave = Nothing
        Me.btnStep1.tbIconPlaceText = 2
        Me.btnStep1.tbIconReadOnly = Nothing
        Me.btnStep1.tbImageMouseDown = Nothing
        Me.btnStep1.tbImageMouseHover = Nothing
        Me.btnStep1.tbImageMouseLeave = Nothing
        Me.btnStep1.tbProgressValue = 50
        Me.btnStep1.tbReadOnly = False
        Me.btnStep1.tbReadOnlyText = False
        Me.btnStep1.tbShadow = False
        Me.btnStep1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep1.tbShowDot = False
        Me.btnStep1.tbShowMoreIconImg = CType(resources.GetObject("btnStep1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep1.tbShowNew = False
        Me.btnStep1.tbShowProgress = False
        Me.btnStep1.tbShowTip = True
        Me.btnStep1.tbShowToolTipOnButton = False
        Me.btnStep1.tbSplit = "0,0,0,0"
        Me.btnStep1.tbText = ""
        Me.btnStep1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.tbTextColor = System.Drawing.Color.White
        Me.btnStep1.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep1.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep1.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep1.tbTextMouseDownPlace = 0
        Me.btnStep1.tbToolTip = ""
        Me.btnStep1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.VisibleEx = True
        '
        'picOperateStep
        '
        Me.picOperateStep.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picOperateStep.BackColor = System.Drawing.Color.Transparent
        Me.picOperateStep.Image = Global.iTong.My.Resources.Resources.h5game_install_detail
        Me.picOperateStep.Location = New System.Drawing.Point(-296, 140)
        Me.picOperateStep.Name = "picOperateStep"
        Me.picOperateStep.Padding = New System.Windows.Forms.Padding(0, 20, 0, 65)
        Me.picOperateStep.Size = New System.Drawing.Size(733, 253)
        Me.picOperateStep.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.picOperateStep.TabIndex = 3
        Me.picOperateStep.TabStop = False
        Me.picOperateStep.tbAutoSize = False
        Me.picOperateStep.tbBackgroundImage = Nothing
        Me.picOperateStep.tbSplit = "0,0,0,0"
        '
        'lblInstallTip
        '
        Me.lblInstallTip.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblInstallTip.AutoSize = True
        Me.lblInstallTip.BackColor = System.Drawing.Color.Transparent
        Me.lblInstallTip.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblInstallTip.ForeColor = System.Drawing.Color.White
        Me.lblInstallTip.Location = New System.Drawing.Point(-296, 99)
        Me.lblInstallTip.Name = "lblInstallTip"
        Me.lblInstallTip.Size = New System.Drawing.Size(473, 12)
        Me.lblInstallTip.TabIndex = 2
        Me.lblInstallTip.tbAdriftWhenHover = False
        Me.lblInstallTip.tbAutoEllipsis = False
        Me.lblInstallTip.tbAutoSize = True
        Me.lblInstallTip.tbHideImage = False
        Me.lblInstallTip.tbIconImage = Nothing
        Me.lblInstallTip.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblInstallTip.tbIconPlaceText = 5
        Me.lblInstallTip.tbShadow = False
        Me.lblInstallTip.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblInstallTip.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblInstallTip.tbShowScrolling = False
        Me.lblInstallTip.Text = "安装时，需要移动设备首先允许【小游戏安装描述文件】，仅用于小游戏，请放心安装。"
        '
        'lblInstallH5Game
        '
        Me.lblInstallH5Game.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblInstallH5Game.AutoSize = True
        Me.lblInstallH5Game.BackColor = System.Drawing.Color.Transparent
        Me.lblInstallH5Game.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblInstallH5Game.ForeColor = System.Drawing.Color.White
        Me.lblInstallH5Game.Location = New System.Drawing.Point(-296, 66)
        Me.lblInstallH5Game.Name = "lblInstallH5Game"
        Me.lblInstallH5Game.Size = New System.Drawing.Size(216, 16)
        Me.lblInstallH5Game.TabIndex = 1
        Me.lblInstallH5Game.tbAdriftWhenHover = False
        Me.lblInstallH5Game.tbAutoEllipsis = False
        Me.lblInstallH5Game.tbAutoSize = True
        Me.lblInstallH5Game.tbHideImage = False
        Me.lblInstallH5Game.tbIconImage = Nothing
        Me.lblInstallH5Game.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblInstallH5Game.tbIconPlaceText = 5
        Me.lblInstallH5Game.tbShadow = False
        Me.lblInstallH5Game.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblInstallH5Game.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblInstallH5Game.tbShowScrolling = False
        Me.lblInstallH5Game.Text = "正在为您安装同步助手游戏："
        '
        'btnInstall
        '
        Me.btnInstall.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnInstall.BackColor = System.Drawing.Color.Transparent
        Me.btnInstall.BindingForm = Nothing
        Me.btnInstall.Font = New System.Drawing.Font("宋体", 16.0!)
        Me.btnInstall.Location = New System.Drawing.Point(337, 473)
        Me.btnInstall.Name = "btnInstall"
        Me.btnInstall.Padding = New System.Windows.Forms.Padding(15, 2, 15, 2)
        Me.btnInstall.Selectable = True
        Me.btnInstall.Size = New System.Drawing.Size(161, 45)
        Me.btnInstall.TabIndex = 29
        Me.btnInstall.tbAdriftIconWhenHover = False
        Me.btnInstall.tbAutoSize = False
        Me.btnInstall.tbAutoSizeEx = False
        Me.btnInstall.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue1
        Me.btnInstall.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbBadgeNumber = 0
        Me.btnInstall.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstall.tbEndEllipsis = False
        Me.btnInstall.tbIconHoldPlace = True
        Me.btnInstall.tbIconImage = Nothing
        Me.btnInstall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnInstall.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbIconMore = False
        Me.btnInstall.tbIconMouseDown = Nothing
        Me.btnInstall.tbIconMouseHover = Nothing
        Me.btnInstall.tbIconMouseLeave = Nothing
        Me.btnInstall.tbIconPlaceText = 2
        Me.btnInstall.tbIconReadOnly = Nothing
        Me.btnInstall.tbImageMouseDown = Nothing
        Me.btnInstall.tbImageMouseHover = Nothing
        Me.btnInstall.tbImageMouseLeave = Nothing
        Me.btnInstall.tbProgressValue = 50
        Me.btnInstall.tbReadOnly = False
        Me.btnInstall.tbReadOnlyText = False
        Me.btnInstall.tbShadow = False
        Me.btnInstall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInstall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstall.tbShowDot = False
        Me.btnInstall.tbShowMoreIconImg = CType(resources.GetObject("btnInstall.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInstall.tbShowNew = False
        Me.btnInstall.tbShowProgress = False
        Me.btnInstall.tbShowTip = True
        Me.btnInstall.tbShowToolTipOnButton = False
        Me.btnInstall.tbSplit = "15,15,15,15"
        Me.btnInstall.tbText = "立即安装"
        Me.btnInstall.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.btnInstall.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.btnInstall.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.btnInstall.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(35, Byte), Integer))
        Me.btnInstall.tbTextMouseDownPlace = 0
        Me.btnInstall.tbToolTip = ""
        Me.btnInstall.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstall.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstall.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.VisibleEx = True
        '
        'frmH5GameInstall
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(837, 604)
        Me.Controls.Add(Me.pnlContainer)
        Me.Name = "frmH5GameInstall"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowIconOnForm = False
        Me.tbSplit = "5,32,6,8"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmH5Install"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlContainer, 0)
        Me.pnlContainer.ResumeLayout(False)
        Me.pnlInstallH5Game.ResumeLayout(False)
        CType(Me.picOperateStep, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlContainer As iTong.Components.tbPanel
    Friend WithEvents btnInstall As iTong.Components.tbButton
    Friend WithEvents pnlInstallH5Game As iTong.Components.tbPanel
    Friend WithEvents lblStepThree As System.Windows.Forms.Label
    Friend WithEvents btnStep3 As iTong.Components.tbButton
    Friend WithEvents lblStepTwo As System.Windows.Forms.Label
    Friend WithEvents btnStep2 As iTong.Components.tbButton
    Friend WithEvents lblStepOne As System.Windows.Forms.Label
    Friend WithEvents btnStep1 As iTong.Components.tbButton
    Friend WithEvents picOperateStep As iTong.Components.tbPictureBox
    Friend WithEvents lblInstallTip As iTong.Components.tbLabel
    Friend WithEvents lblInstallH5Game As iTong.Components.tbLabel
    Friend WithEvents btnOk As iTong.Components.tbButton
End Class
