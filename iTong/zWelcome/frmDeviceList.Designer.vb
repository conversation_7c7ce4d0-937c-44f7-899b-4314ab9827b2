﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDeviceList
    Inherits PNGControlForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Me.flpDeviceList = New tbFlowLayoutPanel
        Me.mTimerShow = New System.Windows.Forms.Timer(Me.components)
        Me.SuspendLayout()
        '
        'flpDeviceList
        '
        Me.flpDeviceList.BackColor = System.Drawing.Color.Black
        Me.flpDeviceList.Dock = System.Windows.Forms.DockStyle.Fill
        Me.flpDeviceList.FlowDirection = System.Windows.Forms.FlowDirection.TopDown
        Me.flpDeviceList.Location = New System.Drawing.Point(0, 0)
        Me.flpDeviceList.Margin = New System.Windows.Forms.Padding(0)
        Me.flpDeviceList.Name = "flpDeviceList"
        Me.flpDeviceList.Size = New System.Drawing.Size(200, 80)
        Me.flpDeviceList.TabIndex = 0
        '
        'mTimerShow
        '
        Me.mTimerShow.Interval = 200
        '
        'frmDeviceList
        '
        Me.ClientSize = New System.Drawing.Size(200, 80)
        Me.Controls.Add(Me.flpDeviceList)
        Me.Name = "frmDeviceList"
        Me.TransparencyKey = System.Drawing.SystemColors.Control
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents flpDeviceList As tbFlowLayoutPanel
    Friend WithEvents mTimerShow As System.Windows.Forms.Timer

End Class
