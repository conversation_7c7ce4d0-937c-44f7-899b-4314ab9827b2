﻿Public Class frmDetail

    Private dgvInfo As tbDataGridView
    Private mDataSource As DataTable
    Public frmSumm As frmSummary = Nothing

    Sub New(ByVal app As IApplication, ByVal dt As DataTable, ByVal frm As Object)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Me.Language = app.Language

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong
        Me.FilletRadius = 5
        Me.BackColor = Color.FromArgb(179, 178, 171)
        Me.Size = New Size(488, 540)


        Me.CanResize = False
        Me.ShowInTaskbar = False
        Me.tbShowTitleOnForm = False
        Me.btnCopy.tbAutoSizeEx = True
        Me.mDataSource = dt

        If TypeOf frm Is frmSummary Then
            Me.frmSumm = frm
            If dt.Rows.Count <= 15 Then
                Utility.WaitSeconds(0.2)
                Me.frmSumm.LoadPhoneInfo()
                Me.mDataSource = Me.frmSumm.mDataSource
            End If
        End If

        Me.Text = Me.Language.GetString("Welcome.Button.DeviceDetails")              '"Device Detail"
        Me.lblState.Visible = False
        If Common.VerIs30 Then
            Me.Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
        End If
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitAppsDataGridView()

        Me.InitContralV3()
    End Sub

    Private Sub InitContralV3()
        Try
            If Common.VerIs30 Then
                Me.tbGuiBackground = My.Resources.frm_bg_sub
                Me.tbSplit = "10,33,10,55"
                Me.tbShowTitleOnForm = True
                Me.tbTitleBackColor = Color.Transparent
                Me.tbTitleLocation = New Point(2, 8)
                Me.Text = Me.Language.GetString("Welcome.Button.DeviceDetails")
                Me.pnlMain.Size = New Size(Me.Width - 2, Me.pnlMain.Height)
                Me.pnlMain.Location = New Point(1, Me.pnlMain.Top)
                Me.dgvInfo.RowTemplate.Height = 35
                Me.dgvInfo.Columns("Key").DefaultCellStyle.BackColor = Color.FromArgb(236, 239, 244)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        'Me.btnCopy.Text = Me.Language.GetString("Common.Button.Copy")              '"Copy"
        Me.btnCopy.Text = Me.Language.GetString("Welcome.Button.CopyAll")           ' "复制全部" '"Copy"
        Me.tsmiCopyValue.Text = Me.Language.GetString("Welcome.Menu.CopyValue")     '"Copy Value"
        Me.tsmiCopyRow.Text = Me.Language.GetString("Welcome.Menu.CopyRow")         '"Copy Row"
        Me.lblState.Text = Me.Language.GetString("Welcome.Label.CopyToClip")        '"已成功复制到剪贴板！"
    End Sub

    Private Sub InitAppsDataGridView()
        Me.dgvInfo = New tbDataGridView
        With Me.dgvInfo
            .SuspendLayout()

            .ColumnHeadersVisible = False
            .RowHeadersVisible = False
            .MultiSelect = False

            Dim cellOtherStyle As New DataGridViewCellStyle()
            cellOtherStyle.BackColor = Color.White
            cellOtherStyle.Font = Common.CreateFont("Arial", 9.0F, FontStyle.Bold)
            cellOtherStyle.ForeColor = Color.Black ' Color.FromArgb(137, 137, 137)

            Dim colKey As tbDataGridViewColumn = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Key", "Key", 155, True, False, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False)
            colKey.DefaultCellStyle = cellOtherStyle

            .DefaultCellStyle.Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
            .DefaultCellStyle.ForeColor = Color.Black ' Color.FromArgb(64, 64, 64)
            .DefaultCellStyle.SelectionBackColor = Color.FromArgb(224, 241, 255)
            .DefaultCellStyle.SelectionForeColor = Color.FromArgb(106, 106, 106)
            Dim strValPadding As String = "   "
            If Common.VerIs30 Then
                .RowTemplate.Height = 30
            Else
                .RowTemplate.Height = 26
                strValPadding = ""
            End If
            Dim intWidth As Integer = 290
            If Common.VerIs30 Then
                intWidth = 300
            End If
            .Columns.Add(colKey)
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Value", "Value", intWidth, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "Other", "Other", 2, True, True, DataGridViewContentAlignment.MiddleLeft, True))
            .ContextMenuStrip = Me.cmsCopy

            For Each row As DataRow In Me.mDataSource.Rows
                .Rows.Add(strValPadding & row("Key") & "", strValPadding & row("Value") & "", "")
            Next

            If .SelectedRows.Count > 0 Then
                .SelectedRows(0).Selected = False
            End If

            .ResumeLayout()

        End With

        Me.pnlMain.Controls.Add(Me.dgvInfo)
    End Sub

    Private Sub btnCopy_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCopy.Click
        Try
            Dim sBuilder As New StringBuilder()
            For Each row As DataGridViewRow In Me.dgvInfo.Rows
                sBuilder.AppendLine(String.Format("{0}{1}", Common.StringFormat(row.Cells("Key").Value, 30, True, True, True), row.Cells("Value").Value))
            Next

            Clipboard.SetText(sBuilder.ToString())
            Me.lblState.Visible = True
        Catch ex As Exception
        End Try
    End Sub

    Private Sub tsmiCopyRow_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiCopyRow.Click
        Try
            If Me.dgvInfo.SelectedRows.Count > 0 Then
                Clipboard.SetText(String.Format("{0}{1}{2}", Me.dgvInfo.SelectedRows(0).Cells("Key").Value, vbTab, Me.dgvInfo.SelectedRows(0).Cells("Value").Value))
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub tsmiCopyValue_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiCopyValue.Click
        Try
            If Me.dgvInfo.SelectedRows.Count > 0 Then
                Clipboard.SetText(Me.dgvInfo.SelectedRows(0).Cells("Value").Value)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnCopy_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCopy.SizeChanged
        Me.btnCopy.Location = New Point(Me.Width - Me.btnCopy.Width - 16, Me.Height - Me.btnCopy.Height - 10)
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        '有的时候关闭这个窗体后主窗体会躲到别的窗体后面。
        If Me.Owner IsNot Nothing Then
            Me.Owner.BringToFront()
        End If
        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub
   
    'Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
    '    Me.frnSumm.LoadPhoneInfo()
    '    Me.mDataSource = Me.frnSumm.mDataSource
    '    With Me.dgvInfo
    '        .SuspendLayout()

    '        For Each row As DataRow In Me.mDataSource.Rows
    '            .Rows.Add(row("Key") & "", row("Value") & "", "")
    '        Next

    '        If .SelectedRows.Count > 0 Then
    '            .SelectedRows(0).Selected = False
    '        End If
    '    End With
    'End Sub

    Public Overrides Sub OverridSkin()
        'MyBase.OverridSkin()
        'If Me.tbGuiBackground IsNot Nothing Then
        '    Me.tbGuiBackground = My.Resources.frm_bg_sub
        'End If
        'Me.btn_close.tbBackgroundImage = My.Resources.btn_close1
    End Sub

End Class
