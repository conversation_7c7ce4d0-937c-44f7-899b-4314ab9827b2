﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmCanceliOSUpgrade
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmCanceliOSUpgrade))
        Me.lblStep1 = New iTong.Components.tbLabel()
        Me.lblStep2 = New iTong.Components.tbLabel()
        Me.lblTitle = New iTong.Components.tbLabel()
        Me.lblStep3 = New iTong.Components.tbLabel()
        Me.picStip1 = New iTong.Components.tbPictureBox()
        Me.picStip2 = New iTong.Components.tbPictureBox()
        Me.btnISee = New iTong.Components.tbButton()
        Me.btnReopen = New iTong.Components.tbButton()
        Me.btnLoadDescFile = New iTong.Components.tbButton()
        Me.btnToDeleteFile = New iTong.Components.tbButton()
        CType(Me.picStip1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picStip2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(546, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(522, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        Me.btn_normal.Visible = False
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(498, 0)
        Me.btn_minimize.Visible = False
        '
        'lblStep1
        '
        Me.lblStep1.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblStep1.AutoSize = True
        Me.lblStep1.BackColor = System.Drawing.Color.Transparent
        Me.lblStep1.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblStep1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStep1.Location = New System.Drawing.Point(40, 96)
        Me.lblStep1.Name = "lblStep1"
        Me.lblStep1.Size = New System.Drawing.Size(184, 17)
        Me.lblStep1.TabIndex = 22
        Me.lblStep1.tbAdriftWhenHover = False
        Me.lblStep1.tbAutoEllipsis = False
        Me.lblStep1.tbAutoSize = True
        Me.lblStep1.tbHideImage = False
        Me.lblStep1.tbIconImage = Global.iTong.My.Resources.Resources.tui_web_installStep1
        Me.lblStep1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblStep1.tbIconPlaceText = 5
        Me.lblStep1.tbShadow = False
        Me.lblStep1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblStep1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblStep1.tbShowScrolling = False
        Me.lblStep1.Text = "请保证手机处于解锁状态下，"
        Me.lblStep1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblStep2
        '
        Me.lblStep2.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblStep2.BackColor = System.Drawing.Color.Transparent
        Me.lblStep2.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblStep2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStep2.Location = New System.Drawing.Point(40, 344)
        Me.lblStep2.Name = "lblStep2"
        Me.lblStep2.Size = New System.Drawing.Size(239, 56)
        Me.lblStep2.TabIndex = 23
        Me.lblStep2.tbAdriftWhenHover = False
        Me.lblStep2.tbAutoEllipsis = False
        Me.lblStep2.tbAutoSize = False
        Me.lblStep2.tbHideImage = False
        Me.lblStep2.tbIconImage = Global.iTong.My.Resources.Resources.tui_web_installStep2
        Me.lblStep2.tbIconImageAlign = System.Drawing.ContentAlignment.TopLeft
        Me.lblStep2.tbIconPlaceText = 5
        Me.lblStep2.tbShadow = False
        Me.lblStep2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblStep2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblStep2.tbShowScrolling = False
        Me.lblStep2.Text = "安装成功后按提示重启设备。"
        '
        'lblTitle
        '
        Me.lblTitle.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblTitle.Location = New System.Drawing.Point(36, 43)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(498, 32)
        Me.lblTitle.TabIndex = 24
        Me.lblTitle.tbAdriftWhenHover = False
        Me.lblTitle.tbAutoEllipsis = False
        Me.lblTitle.tbAutoSize = False
        Me.lblTitle.tbHideImage = False
        Me.lblTitle.tbIconImage = Nothing
        Me.lblTitle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTitle.tbIconPlaceText = 5
        Me.lblTitle.tbShadow = False
        Me.lblTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTitle.tbShowScrolling = False
        Me.lblTitle.Text = "关闭iOS设备自动下载更新教程"
        Me.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblStep3
        '
        Me.lblStep3.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblStep3.BackColor = System.Drawing.Color.Transparent
        Me.lblStep3.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblStep3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStep3.Location = New System.Drawing.Point(300, 344)
        Me.lblStep3.Name = "lblStep3"
        Me.lblStep3.Size = New System.Drawing.Size(239, 35)
        Me.lblStep3.TabIndex = 25
        Me.lblStep3.tbAdriftWhenHover = False
        Me.lblStep3.tbAutoEllipsis = False
        Me.lblStep3.tbAutoSize = False
        Me.lblStep3.tbHideImage = False
        Me.lblStep3.tbIconImage = Global.iTong.My.Resources.Resources.tui_web_installStep3
        Me.lblStep3.tbIconImageAlign = System.Drawing.ContentAlignment.TopLeft
        Me.lblStep3.tbIconPlaceText = 5
        Me.lblStep3.tbShadow = False
        Me.lblStep3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblStep3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblStep3.tbShowScrolling = False
        Me.lblStep3.Text = "桌面出现""Feedback""图标表示成功" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'picStip1
        '
        Me.picStip1.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.picStip1.BackColor = System.Drawing.Color.Transparent
        Me.picStip1.Image = Global.iTong.My.Resources.Resources.summary_cancaliosupgrade_1
        Me.picStip1.Location = New System.Drawing.Point(64, 133)
        Me.picStip1.Name = "picStip1"
        Me.picStip1.Size = New System.Drawing.Size(203, 190)
        Me.picStip1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picStip1.TabIndex = 34
        Me.picStip1.TabStop = False
        Me.picStip1.tbAutoSize = False
        Me.picStip1.tbBackgroundImage = Nothing
        Me.picStip1.tbSplit = "0,0,0,0"
        '
        'picStip2
        '
        Me.picStip2.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.picStip2.BackColor = System.Drawing.Color.Transparent
        Me.picStip2.Image = Global.iTong.My.Resources.Resources.summary_cancaliosupgrade_2
        Me.picStip2.Location = New System.Drawing.Point(307, 133)
        Me.picStip2.Name = "picStip2"
        Me.picStip2.Size = New System.Drawing.Size(203, 190)
        Me.picStip2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picStip2.TabIndex = 35
        Me.picStip2.TabStop = False
        Me.picStip2.tbAutoSize = False
        Me.picStip2.tbBackgroundImage = Nothing
        Me.picStip2.tbSplit = "0,0,0,0"
        '
        'btnISee
        '
        Me.btnISee.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnISee.BackColor = System.Drawing.Color.Transparent
        Me.btnISee.BindingForm = Nothing
        Me.btnISee.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnISee.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnISee.Location = New System.Drawing.Point(226, 427)
        Me.btnISee.Name = "btnISee"
        Me.btnISee.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnISee.Selectable = True
        Me.btnISee.Size = New System.Drawing.Size(118, 34)
        Me.btnISee.TabIndex = 52
        Me.btnISee.tbAdriftIconWhenHover = False
        Me.btnISee.tbAutoSize = False
        Me.btnISee.tbAutoSizeEx = False
        Me.btnISee.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnISee.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnISee.tbBadgeNumber = 0
        Me.btnISee.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnISee.tbEndEllipsis = False
        Me.btnISee.tbIconHoldPlace = True
        Me.btnISee.tbIconImage = Nothing
        Me.btnISee.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnISee.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnISee.tbIconMore = False
        Me.btnISee.tbIconMouseDown = Nothing
        Me.btnISee.tbIconMouseHover = Nothing
        Me.btnISee.tbIconMouseLeave = Nothing
        Me.btnISee.tbIconPlaceText = 2
        Me.btnISee.tbIconReadOnly = Nothing
        Me.btnISee.tbImageMouseDown = Nothing
        Me.btnISee.tbImageMouseHover = Nothing
        Me.btnISee.tbImageMouseLeave = Nothing
        Me.btnISee.tbProgressValue = 50
        Me.btnISee.tbReadOnly = False
        Me.btnISee.tbReadOnlyText = False
        Me.btnISee.tbShadow = False
        Me.btnISee.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnISee.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnISee.tbShowDot = False
        Me.btnISee.tbShowMoreIconImg = CType(resources.GetObject("btnISee.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnISee.tbShowNew = False
        Me.btnISee.tbShowProgress = False
        Me.btnISee.tbShowTip = True
        Me.btnISee.tbShowToolTipOnButton = False
        Me.btnISee.tbSplit = "13,11,13,11"
        Me.btnISee.tbText = "我知道了"
        Me.btnISee.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnISee.tbTextColor = System.Drawing.Color.White
        Me.btnISee.tbTextColorDisable = System.Drawing.Color.White
        Me.btnISee.tbTextColorDown = System.Drawing.Color.White
        Me.btnISee.tbTextColorHover = System.Drawing.Color.White
        Me.btnISee.tbTextMouseDownPlace = 0
        Me.btnISee.tbToolTip = ""
        Me.btnISee.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnISee.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnISee.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnISee.VisibleEx = True
        '
        'btnReopen
        '
        Me.btnReopen.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnReopen.BackColor = System.Drawing.Color.Transparent
        Me.btnReopen.BindingForm = Nothing
        Me.btnReopen.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnReopen.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReopen.Location = New System.Drawing.Point(45, 436)
        Me.btnReopen.Name = "btnReopen"
        Me.btnReopen.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnReopen.Selectable = True
        Me.btnReopen.Size = New System.Drawing.Size(170, 17)
        Me.btnReopen.TabIndex = 53
        Me.btnReopen.tbAdriftIconWhenHover = False
        Me.btnReopen.tbAutoSize = False
        Me.btnReopen.tbAutoSizeEx = False
        Me.btnReopen.tbBackgroundImage = Nothing
        Me.btnReopen.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnReopen.tbBadgeNumber = 0
        Me.btnReopen.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReopen.tbEndEllipsis = False
        Me.btnReopen.tbIconHoldPlace = True
        Me.btnReopen.tbIconImage = Nothing
        Me.btnReopen.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReopen.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReopen.tbIconMore = False
        Me.btnReopen.tbIconMouseDown = Nothing
        Me.btnReopen.tbIconMouseHover = Nothing
        Me.btnReopen.tbIconMouseLeave = Nothing
        Me.btnReopen.tbIconPlaceText = 2
        Me.btnReopen.tbIconReadOnly = Nothing
        Me.btnReopen.tbImageMouseDown = Nothing
        Me.btnReopen.tbImageMouseHover = Nothing
        Me.btnReopen.tbImageMouseLeave = Nothing
        Me.btnReopen.tbProgressValue = 50
        Me.btnReopen.tbReadOnly = False
        Me.btnReopen.tbReadOnlyText = False
        Me.btnReopen.tbShadow = False
        Me.btnReopen.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnReopen.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnReopen.tbShowDot = False
        Me.btnReopen.tbShowMoreIconImg = CType(resources.GetObject("btnReopen.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReopen.tbShowNew = False
        Me.btnReopen.tbShowProgress = False
        Me.btnReopen.tbShowTip = True
        Me.btnReopen.tbShowToolTipOnButton = False
        Me.btnReopen.tbSplit = "13,11,13,11"
        Me.btnReopen.tbText = "重新开启iOS更新"
        Me.btnReopen.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnReopen.tbTextColor = System.Drawing.Color.CornflowerBlue
        Me.btnReopen.tbTextColorDisable = System.Drawing.Color.CornflowerBlue
        Me.btnReopen.tbTextColorDown = System.Drawing.Color.CornflowerBlue
        Me.btnReopen.tbTextColorHover = System.Drawing.Color.CornflowerBlue
        Me.btnReopen.tbTextMouseDownPlace = 0
        Me.btnReopen.tbToolTip = ""
        Me.btnReopen.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReopen.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReopen.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReopen.VisibleEx = True
        '
        'btnLoadDescFile
        '
        Me.btnLoadDescFile.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.btnLoadDescFile.BackColor = System.Drawing.Color.Transparent
        Me.btnLoadDescFile.BindingForm = Nothing
        Me.btnLoadDescFile.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnLoadDescFile.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnLoadDescFile.Location = New System.Drawing.Point(223, 96)
        Me.btnLoadDescFile.Name = "btnLoadDescFile"
        Me.btnLoadDescFile.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnLoadDescFile.Selectable = True
        Me.btnLoadDescFile.Size = New System.Drawing.Size(111, 17)
        Me.btnLoadDescFile.TabIndex = 54
        Me.btnLoadDescFile.tbAdriftIconWhenHover = False
        Me.btnLoadDescFile.tbAutoSize = False
        Me.btnLoadDescFile.tbAutoSizeEx = True
        Me.btnLoadDescFile.tbBackgroundImage = Nothing
        Me.btnLoadDescFile.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnLoadDescFile.tbBadgeNumber = 0
        Me.btnLoadDescFile.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnLoadDescFile.tbEndEllipsis = False
        Me.btnLoadDescFile.tbIconHoldPlace = True
        Me.btnLoadDescFile.tbIconImage = Nothing
        Me.btnLoadDescFile.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLoadDescFile.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnLoadDescFile.tbIconMore = False
        Me.btnLoadDescFile.tbIconMouseDown = Nothing
        Me.btnLoadDescFile.tbIconMouseHover = Nothing
        Me.btnLoadDescFile.tbIconMouseLeave = Nothing
        Me.btnLoadDescFile.tbIconPlaceText = 2
        Me.btnLoadDescFile.tbIconReadOnly = Nothing
        Me.btnLoadDescFile.tbImageMouseDown = Nothing
        Me.btnLoadDescFile.tbImageMouseHover = Nothing
        Me.btnLoadDescFile.tbImageMouseLeave = Nothing
        Me.btnLoadDescFile.tbProgressValue = 50
        Me.btnLoadDescFile.tbReadOnly = False
        Me.btnLoadDescFile.tbReadOnlyText = False
        Me.btnLoadDescFile.tbShadow = False
        Me.btnLoadDescFile.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnLoadDescFile.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnLoadDescFile.tbShowDot = False
        Me.btnLoadDescFile.tbShowMoreIconImg = CType(resources.GetObject("btnLoadDescFile.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnLoadDescFile.tbShowNew = False
        Me.btnLoadDescFile.tbShowProgress = False
        Me.btnLoadDescFile.tbShowTip = True
        Me.btnLoadDescFile.tbShowToolTipOnButton = False
        Me.btnLoadDescFile.tbSplit = "13,11,13,11"
        Me.btnLoadDescFile.tbText = "点击加载描述文件"
        Me.btnLoadDescFile.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLoadDescFile.tbTextColor = System.Drawing.Color.CornflowerBlue
        Me.btnLoadDescFile.tbTextColorDisable = System.Drawing.Color.CornflowerBlue
        Me.btnLoadDescFile.tbTextColorDown = System.Drawing.Color.CornflowerBlue
        Me.btnLoadDescFile.tbTextColorHover = System.Drawing.Color.CornflowerBlue
        Me.btnLoadDescFile.tbTextMouseDownPlace = 0
        Me.btnLoadDescFile.tbToolTip = ""
        Me.btnLoadDescFile.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnLoadDescFile.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnLoadDescFile.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLoadDescFile.VisibleEx = True
        '
        'btnToDeleteFile
        '
        Me.btnToDeleteFile.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.btnToDeleteFile.BackColor = System.Drawing.Color.Transparent
        Me.btnToDeleteFile.BindingForm = Nothing
        Me.btnToDeleteFile.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnToDeleteFile.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnToDeleteFile.Location = New System.Drawing.Point(316, 383)
        Me.btnToDeleteFile.Name = "btnToDeleteFile"
        Me.btnToDeleteFile.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnToDeleteFile.Selectable = True
        Me.btnToDeleteFile.Size = New System.Drawing.Size(147, 17)
        Me.btnToDeleteFile.TabIndex = 55
        Me.btnToDeleteFile.tbAdriftIconWhenHover = False
        Me.btnToDeleteFile.tbAutoSize = False
        Me.btnToDeleteFile.tbAutoSizeEx = True
        Me.btnToDeleteFile.tbBackgroundImage = Nothing
        Me.btnToDeleteFile.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnToDeleteFile.tbBadgeNumber = 0
        Me.btnToDeleteFile.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnToDeleteFile.tbEndEllipsis = False
        Me.btnToDeleteFile.tbIconHoldPlace = True
        Me.btnToDeleteFile.tbIconImage = Nothing
        Me.btnToDeleteFile.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnToDeleteFile.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnToDeleteFile.tbIconMore = False
        Me.btnToDeleteFile.tbIconMouseDown = Nothing
        Me.btnToDeleteFile.tbIconMouseHover = Nothing
        Me.btnToDeleteFile.tbIconMouseLeave = Nothing
        Me.btnToDeleteFile.tbIconPlaceText = 2
        Me.btnToDeleteFile.tbIconReadOnly = Nothing
        Me.btnToDeleteFile.tbImageMouseDown = Nothing
        Me.btnToDeleteFile.tbImageMouseHover = Nothing
        Me.btnToDeleteFile.tbImageMouseLeave = Nothing
        Me.btnToDeleteFile.tbProgressValue = 50
        Me.btnToDeleteFile.tbReadOnly = False
        Me.btnToDeleteFile.tbReadOnlyText = False
        Me.btnToDeleteFile.tbShadow = False
        Me.btnToDeleteFile.tbShadowColor = System.Drawing.Color.Red
        Me.btnToDeleteFile.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnToDeleteFile.tbShowDot = False
        Me.btnToDeleteFile.tbShowMoreIconImg = CType(resources.GetObject("btnToDeleteFile.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnToDeleteFile.tbShowNew = False
        Me.btnToDeleteFile.tbShowProgress = False
        Me.btnToDeleteFile.tbShowTip = True
        Me.btnToDeleteFile.tbShowToolTipOnButton = False
        Me.btnToDeleteFile.tbSplit = "13,11,13,11"
        Me.btnToDeleteFile.tbText = "操作成功后仍提示更新？"
        Me.btnToDeleteFile.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnToDeleteFile.tbTextColor = System.Drawing.Color.Red
        Me.btnToDeleteFile.tbTextColorDisable = System.Drawing.Color.Red
        Me.btnToDeleteFile.tbTextColorDown = System.Drawing.Color.Red
        Me.btnToDeleteFile.tbTextColorHover = System.Drawing.Color.Red
        Me.btnToDeleteFile.tbTextMouseDownPlace = 0
        Me.btnToDeleteFile.tbToolTip = ""
        Me.btnToDeleteFile.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnToDeleteFile.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnToDeleteFile.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnToDeleteFile.VisibleEx = True
        '
        'frmCanceliOSUpgrade
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(570, 484)
        Me.Controls.Add(Me.btnToDeleteFile)
        Me.Controls.Add(Me.btnLoadDescFile)
        Me.Controls.Add(Me.btnReopen)
        Me.Controls.Add(Me.btnISee)
        Me.Controls.Add(Me.picStip2)
        Me.Controls.Add(Me.picStip1)
        Me.Controls.Add(Me.lblStep3)
        Me.Controls.Add(Me.lblTitle)
        Me.Controls.Add(Me.lblStep2)
        Me.Controls.Add(Me.lblStep1)
        Me.Name = "frmCanceliOSUpgrade"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,50,10,10"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "关闭iOS更新"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.lblStep1, 0)
        Me.Controls.SetChildIndex(Me.lblStep2, 0)
        Me.Controls.SetChildIndex(Me.lblTitle, 0)
        Me.Controls.SetChildIndex(Me.lblStep3, 0)
        Me.Controls.SetChildIndex(Me.picStip1, 0)
        Me.Controls.SetChildIndex(Me.picStip2, 0)
        Me.Controls.SetChildIndex(Me.btnISee, 0)
        Me.Controls.SetChildIndex(Me.btnReopen, 0)
        Me.Controls.SetChildIndex(Me.btnLoadDescFile, 0)
        Me.Controls.SetChildIndex(Me.btnToDeleteFile, 0)
        CType(Me.picStip1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picStip2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents lblStep1 As iTong.Components.tbLabel
    Friend WithEvents lblStep2 As iTong.Components.tbLabel
    Friend WithEvents lblTitle As iTong.Components.tbLabel
    Friend WithEvents lblStep3 As iTong.Components.tbLabel
    Friend WithEvents picStip1 As iTong.Components.tbPictureBox
    Friend WithEvents picStip2 As iTong.Components.tbPictureBox
    Friend WithEvents btnISee As iTong.Components.tbButton
    Friend WithEvents btnReopen As iTong.Components.tbButton
    Friend WithEvents btnLoadDescFile As iTong.Components.tbButton
    Friend WithEvents btnToDeleteFile As iTong.Components.tbButton
End Class
