﻿Imports System.Threading
Imports System.IO

Public Class frmSummaryRecommend
    Private mlstBtn As List(Of tbRadioButton)
    Private mIntCount As Integer = 0
    Private mTmpPath As String = Path.Combine(Folder.CacheFolder, "SummaryTemp")
    Private mItems As New List(Of AppItemRecomEx)
    Private mIsReplaceMode As Boolean = False

    Public Sub New(ByVal application As IApplication, ByVal device As Object, ByVal intAppCount As Integer)
        MyBase.New()

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.btn_close.Visible = False
        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False
        Me.mApplication = application
        If TypeOf device Is iPhoneDevice Then
            Me.mDevice = device
            Me.lblTitle.Visible = False
            Me.Tag = DeviceType.iOS
        Else
            Me.mAndroid = device
            Me.Tag = DeviceType.Android
        End If

        If device Is Nothing Then
            Me.mIsReplaceMode = True
        End If

        Me.Language = Me.mApplication.Language
        Me.mIntCount = intAppCount
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.lblTitle.Font = Common.CreateFont("微软雅黑", 13.0!, FontStyle.Regular)
        'Me.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        'Me.txtSearch.Font = Me.Font
        Me.txtSearch.Size = New Size(166, Me.txtSearch.Height)
        Me.txtSearch.Location = New Point(Me.Width - Me.txtSearch.Width - 50, Me.txtSearch.Top)
        Me.lblNetDisable.Font = Common.CreateFont("微软雅黑", 13.0!, FontStyle.Regular)
        Me.LoadWebAppsList()
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lblLoading.Text = Me.Language.GetString("File.Label.Loading") 'loading

        'If Me.mIntCount < 10 Then
        '    Me.lblTitle.Text = "装机必备"
        'End If

        If Me.mAndroid IsNot Nothing Then
            Me.lblTitle.Text = Me.Language.GetString("Welcome.Label.Games")             '"小编推荐"
        End If
        Me.lblNetDisable.Text = Me.Language.GetString("Welcome.Label.NetDisable")       '"网络不可用"
        Me.txtSearch.SearchTipText = Me.Language.GetString("Common.Search")             '"搜索"
        Me.lblGetWebsite.Text = Me.Language.GetString("Welcome.Message.GoToAppSite")    '"去软件商店逛逛吧"
        Me.btnGotoSite.Location = New Point(Me.lblGetWebsite.Right + 3, Me.btnGotoSite.Top + 2)
    End Sub

    Private Sub LoadWebAppsList()
        If Common.NetworkIsAvailable = False AndAlso Not File.Exists(Me.GetContactFilePath) Then
            Me.SetErrorPanel()
            Return
        Else
            Me.pnlLoading.Dock = DockStyle.Fill
            Me.pnlLoading.BringToFront()
        End If

        '加载软件列表
        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf GetAppListFormServerThread))
    End Sub

    Private Delegate Sub SetErrorPanelHandler()
    Private Sub SetErrorPanel()
        If Me.InvokeRequired Then
            Me.Invoke(New SetErrorPanelHandler(AddressOf SetErrorPanel))
        Else
            Me.pnlNetDisable.BringToFront()
            Me.pnlNetDisable.Dock = DockStyle.Fill
            Me.pnlNetDisable.Visible = True
            Me.lblGetWebsite.Visible = False
            Me.btnGotoSite.Visible = False
            If Common.NetworkIsAvailable = False AndAlso Not File.Exists(Me.GetContactFilePath) Then
                Return
            End If
            Me.lblGetWebsite.Visible = True
            Me.btnGotoSite.Visible = True
            Me.lblNetDisable.Text = Me.Language.GetString("Welcome.Message.AppEmpty")               '"应用推荐去哪了"
            Me.lblNetDisable.Top -= 17
            Me.lblGetWebsite.Top -= 10
            Me.btnGotoSite.Top -= 10
        End If
    End Sub

    Private Sub GetAppListFormServerThread(ByVal obj As Object)
        Try
            Dim lst As New List(Of Object)
            Dim strType As String = "4"
            Dim strDevice As String = "1"
            Dim strBreak As String = "2"
            Dim strUrl As String = WebUrl.ServerTuiApple      'http://app.api.tongbu.com/app.html?t={0}&deviceid={1}&isbreak={2}&pageno=1&pagesize=36&token=FGHD77FW2s

            If Me.mDevice IsNot Nothing OrElse Me.mIsReplaceMode Then
                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.ProductType.ToLower.Contains("ipad") Then
                    strDevice = "2"
                End If

                '如果没有安装afc2也只给正版应用。
                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.Jailbreaked Then
                    strBreak = "1"
                End If

                strUrl = String.Format(strUrl, strType, strDevice, strBreak, 36)
                Me.ReadWebContactApple(strUrl)

            ElseIf Me.mAndroid IsNot Nothing Then
                strUrl = String.Format(WebUrl.ServerTuiAndroid, 1, 36)     ' "http://andtui.api.tongbu.com/v2/WebApi.ashx?ApksRecList&rectype=2&pageNo=1&pageSize={0}"
                Me.ReadWebContactAppleForAndroid(strUrl)
            End If

            Me.StartLoadAppIcon()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummaryRecommend_GetAppListFormServerThread")
        End Try
    End Sub

    Private Sub ReadWebContactApple(ByVal strUrl As String)
        'Dim strContact As String = Utility.GetContentStringFromUrl(strurl, System.Text.Encoding.UTF8, 10000)
        'If String.IsNullOrEmpty(strContact) Then
        '    strContact = Utility.GetContentStringFromUrl(strurl, System.Text.Encoding.UTF8)
        'End If
        Dim strContact As String = Me.GetContact(strUrl)
        Dim lstAppInfo As New List(Of PackageInfo)
        Dim objResult As JsonObject = JsonParser.ParseString(strContact)

        Dim objArr As JsonArray = Nothing
        Dim intCount As Integer = 0
        Dim isAbroad As Boolean = False
        Dim strDate As String = ""
        If objResult IsNot Nothing Then
            If objResult.ContainsKey("totalcount") Then
                Me.GetJsonValue(objResult, intCount, "totalcount")
            End If

            If objResult.ContainsKey("area") Then
                Dim strAbroad As String = ""
                Me.GetJsonValue(objResult, strAbroad, "area")
                isAbroad = (strAbroad = "1")
            End If

            If objResult.ContainsKey("data") AndAlso intCount > 0 Then
                objArr = CType(objResult("data"), JsonArray)
            End If
        End If

        If objArr Is Nothing Then
            Me.SetErrorPanel()
            Return
        End If

        Dim countPage As Integer = intCount / 12
        Dim index As Integer = 1
        If objArr.Count Mod 12 > 0 Then
            countPage += 1
        End If

        For Each Item As JsonObject In objArr
            Dim app As New PackageInfo
            'iPhone
            If Item.ContainsKey("appleid") Then
                Me.GetJsonValue(Item, app.AppleId, "appleid")
                app.ItemId = app.AppleId
            End If

            If Item.ContainsKey("name") Then
                Me.GetJsonValue(Item, app.Name, "name")
            End If

            If Item.ContainsKey("version") Then
                Me.GetJsonValue(Item, app.Version, "version")
            End If

            If Item.ContainsKey("sid") Then
                Me.GetJsonValue(Item, app.Tag, "sid")
                'app.Tag = Common.DecryptDES("eK2E8XHl_@_bty7W0Pad_@_QNO9q9/Bfv0xd".Replace("_@_", "+"), "55%g7z!@", "55%g7z!@")
            End If

            If Item.ContainsKey("iconurl") Then
                Me.GetJsonValue(Item, app.AppIconUrl, "iconurl")
            End If

            'Android
            If Item.ContainsKey("apkid") Then
                Me.GetJsonValue(Item, app.AppleId, "apkid")
                app.ItemId = app.AppleId
            End If

            If Item.ContainsKey("packagename") Then
                Me.GetJsonValue(Item, app.Identifier, "packagename")
            End If

            If Item.ContainsKey("name") Then
                Me.GetJsonValue(Item, app.Name, "name")
            End If

            If Item.ContainsKey("icon") Then
                Me.GetJsonValue(Item, app.AppIconUrl, "icon")
            End If

            If Item.ContainsKey("ver") Then
                Me.GetJsonValue(Item, app.Version, "ver")
            End If

            If Item.ContainsKey("note") Then
                Me.GetJsonValue(Item, app.Description, "note")
            End If


            If String.IsNullOrEmpty(app.Description) Then
                app.Description = "暂时没有小编评语"
            End If

            If Item.ContainsKey("size") Then
                Me.GetJsonValue(Item, app.Size, "size")
            End If

            app.IsAbroad = isAbroad

            lstAppInfo.Add(app)
            If lstAppInfo.Count = 12 Then
                lstAppInfo = Me.AddDuoBao(lstAppInfo)
                lstAppInfo = Me.AddDuoBaoSecond(lstAppInfo, index)
                Me.CreateIcons(lstAppInfo, countPage, index)
                lstAppInfo.Clear()
                index += 1
            End If
        Next

        If lstAppInfo.Count > 0 Then
            Me.CreateIcons(lstAppInfo, countPage, index)
            lstAppInfo.Clear()
        End If

        lstAppInfo = Nothing
    End Sub

#Region "一元进宝逻辑"

    '添加夺宝App到首页的列表中
    Private Function AddDuoBao(ByVal lstAppInfo As List(Of PackageInfo)) As List(Of PackageInfo)

        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.Jailbreaked OrElse Not ServerIniSetting.GetJinBaoShow Then
            Return lstAppInfo
        End If

        Dim info As PackageInfo = Me.GetAppInfoDuoBao(ServerIniSetting.GetJinBaoItemId(), ServerIniSetting.GetJinBaoName())
        If info IsNot Nothing Then
            lstAppInfo.Insert(0, info)
            If lstAppInfo.Count > 12 Then
                lstAppInfo.RemoveAt(12)
            End If
        End If
        Return lstAppInfo
    End Function

    Private Function AddDuoBaoSecond(ByVal lstAppInfo As List(Of PackageInfo), ByVal intPage As Integer) As List(Of PackageInfo)
        Try
            If intPage <> 1 OrElse Me.mDevice.Jailbreaked OrElse Not ServerIniSetting.GetJinBaoShowSecond Then
                Return lstAppInfo
            End If

            Dim info As PackageInfo = Me.GetAppInfoDuoBao(ServerIniSetting.GetJinBaoItemIdSecond(), "")
            If info IsNot Nothing Then
                lstAppInfo.Insert(1, info)
                If lstAppInfo.Count > 12 Then
                    lstAppInfo.RemoveAt(12)
                End If
            End If
        Catch 
        End Try
        Return lstAppInfo
    End Function

    Private Function GetAppInfoDuoBao(ByVal strItemid As String, ByVal strName As String) As PackageInfo
        Dim info As PackageInfo = HtmlHelper.GetAppInfoByItemId(strItemid)
        Try
            If info IsNot Nothing Then
                info.AppleId = info.ItemId
                Dim obj As CoreSignatureArgs = CoreUpdateHelper.Instance.GetSignatureOverdueData(info.ItemId, info.Identifier, "5008")
                info.Tag = Utility.GetParamValueFromQuery("sid", obj.SidURL, "&", True)
                If Not String.IsNullOrEmpty(strName) Then
                    info.Name = strName
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummaryRecommend_GetAppInfoDuoBao")
        End Try
        Return info
    End Function

#End Region

    Private Sub ReadWebContactAppleForAndroid(ByVal strUrl As String)
        'Dim strContact As String = Utility.GetContentStringFromUrl(strUrl, System.Text.Encoding.UTF8, 10000)
        'If String.IsNullOrEmpty(strContact) Then
        '    strContact = Utility.GetContentStringFromUrl(strUrl, System.Text.Encoding.UTF8)
        'End If
        Dim strContact As String = Me.GetContact(strUrl)
        Dim lstAppInfo As New List(Of PackageInfo)
        Dim objResult As JsonObject = JsonParser.ParseString(strContact)

        Dim objData As JsonObject = Nothing
        Dim intResult As Integer = 0
        Dim intCount As Integer = 0
        Dim strDate As String = ""
        Dim objArr As JsonArray = Nothing
        If objResult IsNot Nothing Then
            If objResult.ContainsKey("state") Then
                Me.GetJsonValue(objResult, intResult, "state")
            End If

            If objResult.ContainsKey("Data") Then
                objData = CType(objResult("Data"), JsonObject)
            End If
        End If

        If objData Is Nothing OrElse objData.Count <> 2 Then
            Me.SetErrorPanel()
            Return
        End If


        If objData IsNot Nothing Then
            If objData.ContainsKey("Total") Then
                Me.GetJsonValue(objData, intCount, "Total")
            End If
            If objData.ContainsKey("Items") AndAlso intCount > 0 Then
                objArr = CType(objData("Items"), JsonArray)
            End If
        End If

        intCount = objArr.Count
        Dim countPage As Integer = intCount / 12
        Dim index As Integer = 1

        If objArr Is Nothing OrElse objArr.Count <= 0 Then
            Return
        End If

        If objArr.Count Mod 12 > 0 Then
            countPage += 1
        End If

        For Each Item As JsonObject In objArr
            Dim app As New PackageInfo

            'Android
            If Item.ContainsKey("apkid") Then
                Me.GetJsonValue(Item, app.AppleId, "apkid")
                app.ItemId = app.AppleId
            End If

            If Item.ContainsKey("packageName") Then
                Me.GetJsonValue(Item, app.Identifier, "packageName")
            End If

            If Item.ContainsKey("title") Then
                Me.GetJsonValue(Item, app.Name, "title")
            End If

            If Item.ContainsKey("icons") Then
                Dim jsonIcon As JsonObject = Item("icons")
                If jsonIcon IsNot Nothing Then
                    If jsonIcon.ContainsKey("px78") Then
                        Me.GetJsonValue(jsonIcon, app.AppIconUrl, "px78")
                    End If

                    If app.AppIconUrl.Length <= 0 AndAlso jsonIcon.ContainsKey("px68") Then
                        Me.GetJsonValue(jsonIcon, app.AppIconUrl, "px68")
                    End If

                    If app.AppIconUrl.Length <= 0 AndAlso jsonIcon.ContainsKey("px100") Then
                        Me.GetJsonValue(jsonIcon, app.AppIconUrl, "px100")
                    End If

                    If app.AppIconUrl.Length <= 0 AndAlso jsonIcon.ContainsKey("px256") Then
                        Me.GetJsonValue(jsonIcon, app.AppIconUrl, "px256")
                    End If

                    If app.AppIconUrl.Length <= 0 AndAlso jsonIcon.ContainsKey("px48") Then
                        Me.GetJsonValue(jsonIcon, app.AppIconUrl, "px48")
                    End If

                    If app.AppIconUrl.Length <= 0 AndAlso jsonIcon.ContainsKey("px24") Then
                        Me.GetJsonValue(jsonIcon, app.AppIconUrl, "px24")
                    End If
                End If
               

            End If

            If Item.ContainsKey("latestApk") Then
                Dim jsonApk As JsonObject = Item("latestApk")
                If jsonApk IsNot Nothing AndAlso jsonApk.ContainsKey("versionName") Then
                    Me.GetJsonValue(jsonApk, app.Version, "versionName")
                End If
                If jsonApk.ContainsKey("bytes") Then
                    Me.GetJsonValue(jsonApk, app.Size, "bytes")
                    app.Size = Utility.FormatFileSize(app.Size)
                End If
            End If

            If Item.ContainsKey("editorComment") Then
                Me.GetJsonValue(Item, app.Description, "editorComment")
            End If


            If String.IsNullOrEmpty(app.Description) Then
                app.Description = "暂时没有小编评语"
            End If


            lstAppInfo.Add(app)
            If lstAppInfo.Count = 12 Then
                Me.CreateIcons(lstAppInfo, countPage, index)
                lstAppInfo.Clear()
                index += 1
            End If
        Next

        If lstAppInfo.Count > 0 Then
            Me.CreateIcons(lstAppInfo, countPage, index)
            lstAppInfo.Clear()
        End If

        lstAppInfo = Nothing
    End Sub

    Private Function GetContactFilePath() As String
        Folder.CheckFolder(Me.mTmpPath)

        Dim strReturn As String = Path.Combine(Me.mTmpPath, "iOS_WebApps.tmp")
        If Me.mAndroid IsNot Nothing Then
            strReturn = Path.Combine(Me.mTmpPath, "Android_WebApps.tmp")
        End If
        Return strReturn
    End Function

    Private Function GetContactFilePathByDate() As String
        Folder.CheckFolder(Me.mTmpPath)
        Dim strJailbreak As String = "Jail"
        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.Jailbreaked Then
            strJailbreak = "Jailbreak"
        End If
        Dim strReturn As String = Path.Combine(Me.mTmpPath, String.Format("iOS_WebApps_{0}_{1}.tmp", strJailbreak, DateTime.Now.ToString("yyyy_MM_dd")))
        If Me.mAndroid IsNot Nothing Then
            strReturn = Path.Combine(Me.mTmpPath, String.Format("Android_WebApps_{0}.tmp", DateTime.Now.ToString("yyyy_MM_dd")))
        End If
        Return strReturn
    End Function

    Private Sub ReloadContacrFromWeb(ByVal obj As Object)
        Try
            Dim strError As String = ""
            Dim strUrl As String = obj(0)
            Dim strContact As String = obj(1)
            'strContact = Utility.GetContentStringFromUrl(strUrl, System.Text.Encoding.UTF8, 10000, "", strError, False)
            strContact = Utility.GetContentStringFromUrl(strUrl, System.Text.Encoding.UTF8, 10000, "", strError)
            Me.WriteContactFormFile(Me.GetContactFilePathByDate, strContact)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSummaryRecom_ReloadContacrFromWeb")
        End Try
    End Sub

    '把最后一次的信息存在缓存里面，如果没有网络的时候从缓存里面读取信息。
    '带日期的缓存是一天存一个，当天如果存在就不先从缓存里面读取，再起一个线程下载最新的网页内容
    Private Function GetContact(ByVal strUrl As String) As String
        Dim strContact As String = Me.GetContactFormFile(Me.GetContactFilePathByDate)
        Dim objs() As Object = New Object() {strUrl, strContact}
        Dim strError As String = ""

        If String.IsNullOrEmpty(strContact) Then
            'strContact = Utility.GetContentStringFromUrl(strUrl, System.Text.Encoding.UTF8, 10000, "", strError, False)
            strContact = Utility.GetContentStringFromUrl(strUrl, System.Text.Encoding.UTF8, 10000, "", strError)
            Me.WriteContactFormFile(Me.GetContactFilePathByDate, strContact)
        Else
            '加载软件列表
            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ReloadContacrFromWeb), objs)
        End If

        If String.IsNullOrEmpty(strContact) Then
            'strContact = Utility.GetContentStringFromUrl(strUrl, System.Text.Encoding.UTF8, 10000, "", strError, False)
            strContact = Utility.GetContentStringFromUrl(strUrl, System.Text.Encoding.UTF8, 10000, "", strError)
            Me.WriteContactFormFile(Me.GetContactFilePathByDate, strContact)
        End If

        Dim strFilePath As String = Me.GetContactFilePath

        Try
            '1.取到的数据长度为空不写入缓存。
            '2.取到的数据有json格式但没有内容也不显示
            If String.IsNullOrEmpty(strContact) Then
                strContact = Me.GetContactFormFile(strFilePath)
            Else
                Me.WriteContactFormFile(strFilePath, strContact)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSummaryRecommend_GetContact")
        End Try
        Return strContact
    End Function

    Private Sub WriteContactFormFile(ByVal strFilePath As String, ByVal strContact As String)
        If String.IsNullOrEmpty(strContact) Then
            Return
        End If

        Try
            Using sw As New StreamWriter(strFilePath, False, System.Text.Encoding.UTF8)
                sw.Write(strContact)
            End Using
        Catch ex As Exception

        End Try

    End Sub

    Private Function GetContactFormFile(ByVal strFilePath As String) As String
        Dim strContachReturn As String = ""
        Try
            If File.Exists(strFilePath) Then
                Using sr As New StreamReader(strFilePath, True)
                    strContachReturn = sr.ReadToEnd
                End Using
            End If
        Catch ex As Exception
        End Try
        
        Return strContachReturn
    End Function

    Private Sub tmr_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            Dim isCheck As Boolean = False

            If Me.IsDisposed Then
                Return
            End If

            If Me.Bounds.Contains(Me.PointToClient(Control.MousePosition)) OrElse Me.mlstBtn Is Nothing OrElse Me.mlstBtn.Count = 0 Then
                Return
            End If

            For Each Item As tbRadioButton In Me.mlstBtn
                If Item.Checked Then
                    isCheck = True
                ElseIf isCheck = True Then
                    Item.Checked = True
                    Return
                End If
            Next

            If isCheck AndAlso Me.mlstBtn.Count > 0 Then
                Me.mlstBtn(0).Checked = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummaryRecommend_tmr_Tick")
        End Try
    End Sub

    Private Delegate Sub CreateIconsHandler(ByVal lstApp As List(Of PackageInfo), ByVal PnlCount As Integer, ByVal intIndex As Integer)
    Private Sub CreateIcons(ByVal lstApp As List(Of PackageInfo), ByVal PnlCount As Integer, ByVal intIndex As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New CreateIconsHandler(AddressOf CreateIcons), lstApp, PnlCount, intIndex)
        Else
            Try
                Dim flpContainer As tbFlowLayoutPanelEx = Me.CreateIconsPanel(String.Format("pnl_{0}", intIndex.ToString))
                flpContainer.tbSuspendLayout()
                For Each appInfo As PackageInfo In lstApp
                    Dim item As New AppItemRecomEx(appInfo.Name, String.Empty, appInfo.Identifier, Nothing)
                    item.Font = flpContainer.Font
                    item.Tag = appInfo
                    item.ShowHot = ServerIniSetting.GetCheckJinBaoItemId(appInfo.ItemId)
                    '是不是要显示同步屋
                    If item.ShowHot Then
                        item.TipText = item.Text
                        'If Me.ShowTongbu5Range() Then
                        '    item.ShowHot = False
                        '    item.ShowDiscount = True
                        '    item.Icon = My.Resources.icon_taobao.Clone()
                        '    item.TextHove = Me.Language.GetString("Common.Button.Open") '"打开"
                        '    item.ShowTipForm = True
                        '    item.TipText = "淘宝特价" & Chr(1) & " " & Chr(1) & "在这里可以领到内部优惠券哦～淘宝上的商品通通都有优惠券，先领券后下单，防剁手省钱小利器."
                        '    item.Text = "淘宝特价"
                        'End If
                    Else
                        item.Icon = Nothing
                        item.TipText = appInfo.Name & Chr(1) & String.Format("{0} | {1}", appInfo.Version, appInfo.Size) & Chr(1) & appInfo.Description
                        item.TextHove = Me.Language.GetString("App.Button.InstallApps") '"安装"
                        item.ShowTipForm = True
                    End If

                    AddHandler item.ButtonClickIcon, AddressOf Item_ButtonClickIcon
                    AddHandler item.ButtonClickText, AddressOf Item_ButtonClickText
                    flpContainer.Controls.Add(item)
                    Me.mItems.Add(item)
                Next
                flpContainer.tbResumeLayout()

                Dim btn As tbRadioButton = New tbRadioButton()
                Me.Controls.Add(btn)
                btn.Size = New Size(30, 15)
                btn.tbIconImageState = ImageState.OneState
                btn.tbIconCheckedMouseDown = My.Resources.summary_select
                btn.tbIconCheckedMouseHover = My.Resources.summary_select
                btn.tbIconCheckedMouseLeave = My.Resources.summary_select
                btn.tbIconUnCheckedMouseDown = My.Resources.summary_unselect
                btn.tbIconUnCheckedMouseHover = My.Resources.summary_unselect
                btn.tbIconUnCheckedMouseLeave = My.Resources.summary_unselect
                btn.Tag = flpContainer

                btn.Location = New Point((Me.Width - PnlCount * 30) / 2 + (intIndex - 1) * 30 - 15, Me.Height - 30)
                btn.Anchor = AnchorStyles.Bottom
                'btn.BringToFront()
                AddHandler btn.CheckedChanged, AddressOf btn_Checked
                AddHandler btn.MouseHover, AddressOf btn_MouseHover

                If intIndex = 1 Then
                    btn.Checked = True
                End If
                If Me.mlstBtn Is Nothing Then
                    Me.mlstBtn = New List(Of tbRadioButton)
                End If
                Me.mlstBtn.Add(btn)
            Catch ex As Exception
                Common.LogException(ex.ToString, "Summary_CreateIcons")
            End Try
        End If
    End Sub

    Private mintTongbu5Range As Integer = -1
    '同步屋显示的值为0-100
    Private Function ShowTongbu5Range() As Boolean
        Dim blnShow As Boolean = False
        Dim rdm As New Random()
        If Me.mintTongbu5Range < 0 Then
            Me.mintTongbu5Range = rdm.Next(0, 100)
        End If
        If ServerIniSetting.GetShowTongbu5 > mintTongbu5Range Then
            blnShow = True
        End If
        Return blnShow
    End Function

    Private Delegate Sub StartLoadIconHandler()
    Private Sub StartLoadAppIcon()
        If Me.InvokeRequired Then
            Me.Invoke(New StartLoadIconHandler(AddressOf StartLoadAppIcon))
        Else
            Dim tmr As New System.Windows.Forms.Timer
            AddHandler tmr.Tick, AddressOf tmr_Tick
            tmr.Interval = 5000
            tmr.Start()
            Me.pnlLoading.Visible = False
            '加载软件列表
            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf LoadAppIcon))
        End If
    End Sub

    Private Sub LoadAppIcon(ByVal obj As Object)
        Try
            Dim strKey As String = "Android"
            If Me.mDevice IsNot Nothing Then
                strKey = "iOS"
            End If

            Dim lstFilesDelete As New List(Of String)
            Dim lstFiles As New List(Of String)
            lstFiles.Add(Me.GetContactFilePath)
            lstFiles.Add(Me.GetContactFilePathByDate)

            For Each appItem As AppItemRecomEx In Me.mItems
                If appItem Is Nothing Then
                    Continue For
                End If

                Dim info As PackageInfo = appItem.Tag
                Dim imgPath As String = Me.mTmpPath

                Folder.CheckFolder(imgPath)
                imgPath = Path.Combine(imgPath, strKey & Common.ReplaceWinIllegalName(info.AppIconUrl).Replace("_", ""))
                lstFiles.Add(imgPath)

                If appItem.Icon Is Nothing AndAlso File.Exists(imgPath) Then
                    appItem.Icon = Utility.GetImageFormFile(imgPath, True)
                End If

                If Not File.Exists(imgPath) OrElse appItem.Icon Is Nothing Then
                    Common.DownloadImage(info.AppIconUrl, 10000, imgPath)
                    '如果还不存在就再下载一次，如果还下载不下来就不管了.
                    If Not File.Exists(imgPath) Then
                        Common.DownloadImage(info.AppIconUrl, 10000, imgPath)
                    End If
                End If

                If appItem.Icon Is Nothing AndAlso File.Exists(imgPath) Then
                    appItem.Icon = Utility.GetImageFormFile(imgPath, True)
                End If
                Application.DoEvents()
            Next

            '删除旧的数据文件
            For Each Item As String In Directory.GetFiles(Me.mTmpPath, "*.*", SearchOption.TopDirectoryOnly)
                If Path.GetFileName(Item).StartsWith(strKey) AndAlso Not lstFiles.Contains(Item) Then
                    lstFilesDelete.Add(Item)
                End If
            Next

            For Each Item As String In lstFilesDelete
                Try
                    File.Delete(Item)
                Catch ex As Exception
                End Try
            Next

            Try
                Dim oldTmpPath As String = Path.Combine(Folder.CacheFolder, "SummaryAppIconTemp")
                If Directory.Exists(oldTmpPath) Then
                    Directory.Delete(oldTmpPath, True)
                End If
            Catch ex As Exception
            End Try

        Catch ex As Exception
            Common.LogException("frmSummaryRecommend_LoadAppIcon:" & ex.ToString)
        End Try
    End Sub

    Private Function CreateIconsPanel(ByVal strName As String) As tbFlowLayoutPanelEx
        Dim flpContainer As New tbFlowLayoutPanelEx
        flpContainer.Name = strName
        Me.pnlIcons.Controls.Add(flpContainer)
        flpContainer.Dock = DockStyle.Fill
        flpContainer.HorizontalScroll.Visible = False
        flpContainer.VerticalScroll.Visible = False
        flpContainer.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Return flpContainer
    End Function

    '同步屋逻辑
    Private Function OpenTongbu5(ByVal item As AppItemRecomEx)
        Dim blnReturn As Boolean = False
        Try
            If item.ShowDiscount Then
                Common.OpenExplorer(WebUrl.PageTongbu5)
                blnReturn = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummaryRecommend_OpenTongbu5")
        End Try
        Return blnReturn
    End Function

    Private Sub Item_ButtonClickIcon(ByVal sender As System.Object, ByVal e As AppItemEventArgs)
        If sender IsNot Nothing AndAlso sender.tag IsNot Nothing AndAlso TypeOf sender.tag Is PackageInfo Then
            '同步屋逻辑
            If Me.OpenTongbu5(sender) Then
                Return
            End If
            Dim info As PackageInfo = sender.Tag
            If Me.mDevice IsNot Nothing Then
                frmSummaryRecommend.GotoAppDetail(Me.mApplication, Me.mDevice, info.AppleId)
                ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryEditorRecommendsInfo)
            ElseIf Me.mAndroid IsNot Nothing Then
                frmSummaryRecommend.GotoAppDetail(Me.mApplication, Me.mAndroid, info.AppleId)
            End If
        End If
    End Sub

    Private Sub Item_ButtonClickText(ByVal sender As System.Object, ByVal e As AppItemEventArgs)
        If sender IsNot Nothing AndAlso sender.tag IsNot Nothing AndAlso TypeOf sender.tag Is PackageInfo Then
           '同步屋逻辑
            If Me.OpenTongbu5(sender) Then
                Return
            End If

            Dim info As PackageInfo = sender.Tag
            If Me.mAndroid IsNot Nothing Then
                CoreUpdateHelper.Instance(Me.mAndroid)
                WebSiteHelper.Instance().DownloadAPK(mApplication, info.Identifier, info.AppIconUrl, info.Version, info.Name)
                Return
            End If
            ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryEditorRecommendsDown)
            Dim sid As String = ""
            If info.Tag IsNot Nothing Then
                sid = info.Tag.ToString
            End If

            Dim blnJaiblcak As Boolean = False

            '这个模块只有3.X才有不用兼容2.X逻辑
            Dim strDownloadUrl As String = "http://downs.tongbu.com/zbdowns/itunesdownload.aspx?model=1100&sid=" & sid

            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.InstallCydia Then
                strDownloadUrl = "http://d.tongbu.com/?sid=" & sid
                blnJaiblcak = True
            End If


            WebSiteHelper.Instance().Start2AnalyseDownloadUrlByDownloadCenter(mApplication, strDownloadUrl, info.Name, "", info.ItemId, _
                                                                        info.Version, False, info.AppIconUrl, Me.mDevice, blnJaiblcak, "", "", info.IsAbroad)

            '第二个icon点下载行为统计
            If info.ItemId = ServerIniSetting.GetJinBaoItemIdSecond Then
                ActionCollectHelper.OperateDuoBaoSecond(Me.mDevice, ActionDataType.Click, FunctionSucceed.Succeed, info.ItemId)
            End If
        End If
    End Sub

    Private Sub btn_Checked(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If sender IsNot Nothing AndAlso TypeOf sender Is tbRadioButton Then
            Dim btn As tbRadioButton = sender
            btn.Tag.BringToFront()
            'If Me.mlstBtn IsNot Nothing AndAlso Me.mlstBtn.Count > 0 Then
            '    For Each Item As tbRadioButton In mlstBtn
            '        Item.BringToFront()
            '    Next
            'End If
            'btn.BringToFront()
        End If
    End Sub

    Private Sub btn_MouseHover(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If sender IsNot Nothing AndAlso TypeOf sender Is tbRadioButton Then
            CType(sender, tbRadioButton).Checked = True
        End If
    End Sub

    Private Sub GetJsonValue(ByVal item As JsonObject, ByRef mValue As String, ByVal strPara As String)
        Try
            If item.ContainsKey(strPara) Then
                If TypeOf item(strPara) Is JsonNumber Then
                    Dim para As JsonNumber = CType(item(strPara), JsonNumber)
                    mValue = para.Value

                ElseIf TypeOf item(strPara) Is JsonString Then
                    Dim para As JsonString = CType(item(strPara), JsonString)
                    mValue = para.Value

                ElseIf TypeOf item(strPara) Is JsonBoolean Then
                    Dim para As JsonBoolean = CType(item(strPara), JsonBoolean)
                    mValue = para.Value

                End If

            End If
        Catch ex As Exception
            Debug.Print(String.Format("GetJsonValue:{0}", strPara))
        End Try
    End Sub

    Private Sub txtSearchFile_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtSearch.KeyDown
        Dim strSearch As String = Me.txtSearch.Text

        If e.KeyCode = Keys.Enter AndAlso Not String.IsNullOrEmpty(strSearch) Then  '  Me.txtSearch.Text.Length > 0
            Dim strNode As String = "AppShare"
            Dim jumpUrl As String = ""
            Dim strDeviceId As String = 1
            Dim stridentify As String = ""

            If Me.mDevice IsNot Nothing Then
                stridentify = Me.mDevice.Identifier
                Dim url As String = frmSite.Instance(Me.mApplication, Me.mDevice).GetUrlByNodeName("AppShare")
                If String.IsNullOrEmpty(url) Then
                    url = "http://v3.tongbu.com/share/#!"
                End If

                jumpUrl = url & "search&query={1}&deviceid={0}"

                If Me.mDevice.ProductType.ToLower.Contains("ipad") Then
                    strDeviceId = 2
                End If

                If Me.mDevice.InstallCydia OrElse Me.mDevice.Jailbreaked Then
                    strNode = "App"
                    jumpUrl = "http://cx.tongbu.com/s/v2?deviceid={0}&key={1}&bo=1"
                End If
                jumpUrl = String.Format(jumpUrl, strDeviceId, System.Web.HttpUtility.UrlEncode(strSearch))

            ElseIf Me.mAndroid IsNot Nothing Then
                strNode = "AppAndroid"
                stridentify = Me.mAndroid.DeviceID

                'jumpUrl = String.Format("http://v2.tongbu.com/android/search/?key={0}", System.Web.HttpUtility.UrlEncode(Me.txtSearch.Text))  '' 此为2.0版链接
                jumpUrl = String.Format("http://v3.tongbu.com/android/#!search&query={0}", System.Web.HttpUtility.UrlEncode(strSearch))

            End If

            Me.mApplication.GotoSite(ActionFuncType.App, strNode, stridentify, jumpUrl)
        End If
    End Sub

    Public Shared Sub GotoAppDetail(ByVal app As IApplication, ByVal dev As IDevice, ByVal appId As String)
        Dim jumpUrl As String = appId
        Dim strNode As String = "AppShare"
        Dim stridentify As String = ""

        If TypeOf dev Is iPhoneDevice Then
            Dim device As iPhoneDevice = dev
            stridentify = device.Identifier
            If device.Jailbreaked Then
                strNode = "App"
                jumpUrl = String.Format(WebUrl.PageApp, jumpUrl)
            End If
            'ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryEditorRecommendsInfo)
        ElseIf TypeOf dev Is AndroidDevice Then
            stridentify = dev.DeviceID
            strNode = "AppAndroid"

            If Common.VerIs30 Then
                jumpUrl = appId
            Else
                jumpUrl = String.Format(WebUrl.PageApkDownInfo, appId)
            End If
        End If

        app.GotoSite(ActionFuncType.App, strNode, stridentify, jumpUrl)
    End Sub

    Private Sub txtSearch_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearch.Resize
        Dim a As String = ""
        If Me.txtSearch.Width > 166 Then
            Return
        End If
    End Sub

    Private Sub lblGetWebsite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblGetWebsite.Click, btnGotoSite.Click
        '软件列表加载不出来的时候跳转到软件站
        Dim strApp As String = "App"
        Dim strId As String = ""
        If Me.mDevice IsNot Nothing Then
            If Me.mDevice.InstallCydia Then
                strApp = "App"
            Else
                strApp = "AppShare"
            End If
            strId = Me.mDevice.Identifier
        ElseIf Me.mAndroid IsNot Nothing Then
            strApp = "AppAndroid"
            strId = Me.mAndroid.DeviceID
        End If
        Me.mApplication.GotoSite(ActionFuncType.App, strApp, strId)
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.mlstBtn Is Nothing OrElse Me.mlstBtn.Count <= 0 Then
            Return
        End If

        For Each btn As tbRadioButton In Me.mlstBtn
            btn.tbIconCheckedMouseDown = My.Resources.summary_select
            btn.tbIconCheckedMouseHover = My.Resources.summary_select
            btn.tbIconCheckedMouseLeave = My.Resources.summary_select
            btn.tbIconUnCheckedMouseDown = My.Resources.summary_unselect
            btn.tbIconUnCheckedMouseHover = My.Resources.summary_unselect
            btn.tbIconUnCheckedMouseLeave = My.Resources.summary_unselect
        Next
    End Sub


    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

		If Me.mItems Is Nothing Then Exit Sub

        '窗体关闭的时候把资源注销掉，特别是图片资源
		Try

			For Each item As AppItemRecomEx In Me.mItems
				If item.Tag IsNot Nothing AndAlso TypeOf item.Tag Is PackageInfo Then
					CType(item.Tag, PackageInfo).Dispose()
				End If
				If item.Icon IsNot Nothing Then
					item.Icon.Dispose()
				End If
				item.Icon = Nothing
				item.Tag = Nothing
				item.Dispose()
			Next
			Me.mItems.Clear()
			Me.mItems = Nothing
		Catch ex As Exception
			Common.LogException(ex.ToString(), "frmSummaryRecommend_BeforeFormClose")
		End Try
    End Sub

End Class