﻿Imports System.Threading

Public Class frmWelcome

#If IS_ITONG_ZJ Then
    Private mFormSummarry As frmSummaryZJ
#Else
    Private mFormSummarry As frmSummary
#End If

    'Private mFormGuid As frmGuid
    Private mFormiTunes As frmiTunes
    Private mErrorDocumentText As String = String.Empty
    Private mDownManage As MultiThreadDownload

#Region "--- 初始化 ---"

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneDevice)
        Me.New(app, device, Nothing, Nothing)

    End Sub

    Public Sub New(ByVal app As IApplication, ByVal device As iPhoneRecoveryDevice, ByVal usbDev As UsbDevice)
        Me.New(app, device, device, usbDev)
    End Sub

    Public Sub New(ByVal app As IApplication, ByVal device As IDevice, ByVal usbDev As UsbDevice)  'iPhoneDevice
        Me.New(app, device, Nothing, usbDev)

    End Sub

    Public Sub New(ByVal app As IApplication, ByVal device As IDevice, ByVal devRecover As iPhoneRecoveryDevice, ByVal usbDev As UsbDevice)
        MyBase.New(app, device, usbDev)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FunctionMappingKey = FunctionKey.Welcome
        'Me.mDevice = device
        'Me.mDeviceRecovery = devRecover
        Me.mApplication = app
        Me.Language = app.Language

        Me.pbLoading.Visible = False
        Me.lblLoading.Visible = False
        Me.pbLoadingV3.Visible = True
        Me.pnlLoading.BackColor = Color.FromArgb(250, 251, 252)
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lblLoading.Text = Me.Language.GetString("Welcome.Message.Connecting") ' "正在准备连接设备..."
    End Sub

    Public Shared Function CheckiTunes() As Boolean
        Dim blnResult As Boolean = False

        If Common.CheckiTunesInstall() Then
            blnResult = True

            If iTunesHelper.iTunesUsePlugin AndAlso Not iTunesHelper.CheckPluginIsReady() Then
                AppleDownload.BuyFromSvr = True
            End If
        End If

        Return blnResult
    End Function


    Protected Overrides Sub InitControls()
        Me.mCanInitCheck = False

        MyBase.InitControls()

        '初始化的时候先置前
        Me.BringToFront()

        '当安装完整版本的iTunes才可以进入设备连接。
        If frmWelcome.CheckiTunes() Then
            Me.mCanInitCheck = True
            Me.CheckInit()
        Else
            Me.mFormiTunes = New frmiTunes(Me.mApplication, ProcForm.Instance, IniSetting.GetDownloadSoftFolder, ServerIniSetting.GetiTunesVersion(ServerIniSetting.GetiTunesUrl), ServerIniSetting.GetiTunesUrl)
            Utility.AddForm2Panel(Me.mFormiTunes, Me.pnlMain, True)
        End If

        If Not Folder.LangType = LanguageType.en_US Then
            Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
        End If

        Me.mDownManage = MultiThreadDownload.Instance()
    End Sub

    Public Overrides Sub OnConnecting()
        MyBase.OnConnecting()

        If frmWelcome.CheckiTunes() Then
            Me.pnlLoading.Visible = True
            Me.pnlLoading.BringToFront()
        End If
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
        '' Added by Utmost20140903
        '' 由弹窗程序进入助手的命令行参数
        ProcForm.ExecuteSiteByUsb()
        Me.pnlLoading.Visible = False
        Me.pnlLoading.SendToBack()
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        Try
            If Me.mFormSummarry IsNot Nothing AndAlso device IsNot Nothing Then
                Me.mFormSummarry.Name = Me.mFormSummarry.Name.Replace(device.DeviceID, "")
                Me.mFormSummarry.OnDisconnect(device)
            End If
        Catch ex As Exception
        End Try

        If Me.mFormSummarry IsNot Nothing AndAlso Not Me.mFormSummarry.IsDisposed Then
            Me.mFormSummarry.StopRefresh()
            Me.mFormSummarry.Close()
            Me.mFormSummarry = Nothing
        End If
    End Sub

    Public Overrides Sub OnRecoverConnect(ByVal device As iPhoneRecoveryDevice)
        MyBase.OnRecoverConnect(device)
        Me.CheckInit()
        Me.pnlLoading.Visible = False
        Me.pnlLoading.SendToBack()
    End Sub

    Public Overrides Sub OnRecoverDisconnect(ByVal device As iPhoneRecoveryDevice)
        MyBase.OnRecoverDisconnect(device)
        Me.mFormSummarry.Name = Me.mFormSummarry.Name.Replace(device.DeviceID, "")
        Me.CheckInit()
    End Sub

    'Public Sub ReCheckInit()
    '    Dim striTunesPath As String = Common.GetiTunesInstallPath().TrimEnd("\") & "\" & "iTunes.exe"
    '    If File.Exists(striTunesPath) Then
    '        '重新检测的时候把设备侦听打开。
    '        MobileDeviceManager.Instance().StartListen()
    '        Me.CheckInit()
    '    End If
    'End Sub

    Public Overrides Sub CheckInit()
        If Not frmWelcome.CheckiTunes() Then
            Return
        End If

        If Common.VerIs30() AndAlso Me.mUsbDev IsNot Nothing AndAlso Me.mUsbDev.MobileDevice Is Nothing Then
            'Me.OnConnecting()
            Return
        End If

        Try
            If Me.mDevice Is Nothing AndAlso Me.mDeviceRecovery Is Nothing AndAlso Me.mDeviceDFU Is Nothing Then
                'If Not Common.VerIs30 Then
                '    '设备断开后不显示此窗体 Noted by Utmost20140512
                '    If Me.mFormGuid Is Nothing Then
                '        Me.mFormGuid = New frmGuid(Me.mApplication)
                '        Utility.AddForm2Panel(Me.mFormGuid, Me.pnlMain, True)
                '    End If
                '    Me.mFormGuid.BringToFront()
                'End If

                If Me.mFormSummarry IsNot Nothing Then
                    Me.mFormSummarry.OnDisconnect(Me.mDevice)
                End If
            Else
                If Me.mFormSummarry Is Nothing Then
                    'Utility.WaitSeconds(0.01)
                    If Me.mDevice IsNot Nothing Then
#If IS_ITONG_ZJ Then
                        Me.mFormSummarry = New frmSummaryZJ(Me.mApplication, Me.mDevice)
#Else
                        Me.mFormSummarry = New frmSummary(Me.mApplication, Me.mDevice)
#End If
                        If Common.VerIs30() AndAlso Me.mCanInitCheck Then
                            Me.mFormSummarry.OnConnect(Me.mDevice)
                        End If

                    ElseIf Me.mDeviceRecovery IsNot Nothing Then
#If IS_ITONG_ZJ Then
                        Me.mFormSummarry = New frmSummaryZJ(Me.mApplication, Me.mDevice)
#Else
                        Me.mFormSummarry = New frmSummary(Me.mApplication, Me.mDevice)
#End If
                        If Me.mCanInitCheck Then
                            Me.mFormSummarry.OnRecoverConnect(Me.mDeviceRecovery)
                        End If
                    ElseIf Me.mDeviceDFU IsNot Nothing Then
#If IS_ITONG_ZJ Then
                        Me.mFormSummarry = New frmSummaryZJ(Me.mApplication, Me.mDevice)
#Else
                        Me.mFormSummarry = New frmSummary(Me.mApplication, Me.mDeviceDFU)
#End If
                        If Me.mCanInitCheck Then
                            Me.mFormSummarry.OnDFUConnect(Me.mDeviceDFU)
                        End If
                    End If
                    'Utility.WaitSeconds(0.01)
                    If Not Me.pnlMain.IsDisposed Then
                        '' 避免错误“System.ObjectDisposedException: 无法访问已释放的对象”
                        Utility.AddForm2Panel(Me.mFormSummarry, Me.pnlMain, True)
                    End If

                    If Me.mDevice IsNot Nothing Then
                        Me.mFormSummarry.Name = String.Format("{0}_{1}", Me.mDevice.DeviceID, Me.mFormSummarry.Name)
                    End If

                    Me.mFormSummarry.BringToFront()

#If IS_ITONG_ZJ Then

#Else
                    Me.mFormSummarry.InitSummaryRecommend()
#End If
                Else
                    If Me.mDevice IsNot Nothing Then
                        Me.mFormSummarry.OnConnect(Me.mDevice)
                    ElseIf Me.mDeviceRecovery IsNot Nothing Then
                        Me.mFormSummarry.OnRecoverConnect(Me.mDeviceRecovery)
                    ElseIf Me.mDeviceDFU IsNot Nothing Then
                        Me.mFormSummarry.OnDFUConnect(Me.mDeviceDFU)
                    End If
                End If
                Me.mFormSummarry.BringToFront()
                Utility.WaitSeconds(0.01)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckInit of frmWelcome")
        End Try
    End Sub

#End Region

End Class
