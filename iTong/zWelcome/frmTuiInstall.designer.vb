﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmTuiInstall
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。


    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的


    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的


    '可以使用 Windows 窗体设计器修改它。


    '不要使用代码编辑器修改它。


    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmTuiInstall))
        Me.btnInstall = New iTong.Components.tbButton()
        Me.pbarShowProgress = New iTong.Components.tbControlBar()
        Me.lblTuiProgress = New iTong.Components.tbLabel()
        Me.chkNoShow = New iTong.Components.tbCheckBox()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.pnlInstallTui = New iTong.Components.tbPanel()
        Me.btnOk = New iTong.Components.tbButton()
        Me.lblStepThree = New System.Windows.Forms.Label()
        Me.btnStep3 = New iTong.Components.tbButton()
        Me.lblStepTwo = New System.Windows.Forms.Label()
        Me.btnStep2 = New iTong.Components.tbButton()
        Me.lblStepOne = New System.Windows.Forms.Label()
        Me.btnStep1 = New iTong.Components.tbButton()
        Me.picOperateStep = New iTong.Components.tbPictureBox()
        Me.lblInstallTip = New iTong.Components.tbLabel()
        Me.lblInstallWebTui = New iTong.Components.tbLabel()
        Me.picWebTuiMoblie = New iTong.Components.tbPictureBox()
        Me.TbPanel1 = New iTong.Components.tbPanel()
        Me.btnBack = New iTong.Components.tbButton()
        Me.btnFinish = New iTong.Components.tbButton()
        Me.btnFeedback = New iTong.Components.tbButton()
        Me.pnlContainer.SuspendLayout()
        Me.pnlInstallTui.SuspendLayout()
        CType(Me.picOperateStep, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picWebTuiMoblie, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(813, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(789, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(765, 0)
        '
        'btnInstall
        '
        Me.btnInstall.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnInstall.BackColor = System.Drawing.Color.Transparent
        Me.btnInstall.BindingForm = Nothing
        Me.btnInstall.Font = New System.Drawing.Font("宋体", 15.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnInstall.Location = New System.Drawing.Point(490, 384)
        Me.btnInstall.Name = "btnInstall"
        Me.btnInstall.Padding = New System.Windows.Forms.Padding(15, 2, 15, 2)
        Me.btnInstall.Selectable = True
        Me.btnInstall.Size = New System.Drawing.Size(161, 60)
        Me.btnInstall.TabIndex = 28
        Me.btnInstall.tbAdriftIconWhenHover = False
        Me.btnInstall.tbAutoSize = False
        Me.btnInstall.tbAutoSizeEx = True
        Me.btnInstall.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue1
        Me.btnInstall.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbBadgeNumber = 0
        Me.btnInstall.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstall.tbEndEllipsis = False
        Me.btnInstall.tbIconHoldPlace = True
        Me.btnInstall.tbIconImage = Nothing
        Me.btnInstall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnInstall.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbIconMore = False
        Me.btnInstall.tbIconMouseDown = Nothing
        Me.btnInstall.tbIconMouseHover = Nothing
        Me.btnInstall.tbIconMouseLeave = Nothing
        Me.btnInstall.tbIconPlaceText = 2
        Me.btnInstall.tbIconReadOnly = Nothing
        Me.btnInstall.tbImageMouseDown = Nothing
        Me.btnInstall.tbImageMouseHover = Nothing
        Me.btnInstall.tbImageMouseLeave = Nothing
        Me.btnInstall.tbProgressValue = 50
        Me.btnInstall.tbReadOnly = False
        Me.btnInstall.tbReadOnlyText = False
        Me.btnInstall.tbShadow = False
        Me.btnInstall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInstall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstall.tbShowDot = False
        Me.btnInstall.tbShowMoreIconImg = CType(resources.GetObject("btnInstall.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInstall.tbShowNew = False
        Me.btnInstall.tbShowProgress = False
        Me.btnInstall.tbShowTip = True
        Me.btnInstall.tbShowToolTipOnButton = False
        Me.btnInstall.tbSplit = "15,15,15,15"
        Me.btnInstall.tbText = "立即安装"
        Me.btnInstall.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnInstall.tbTextColor = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDisable = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDown = System.Drawing.Color.White
        Me.btnInstall.tbTextColorHover = System.Drawing.Color.White
        Me.btnInstall.tbTextMouseDownPlace = 0
        Me.btnInstall.tbToolTip = ""
        Me.btnInstall.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstall.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstall.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.VisibleEx = True
        '
        'pbarShowProgress
        '
        Me.pbarShowProgress.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pbarShowProgress.BackColor = System.Drawing.Color.Transparent
        Me.pbarShowProgress.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.pbarShowProgress.Location = New System.Drawing.Point(515, 482)
        Me.pbarShowProgress.Name = "pbarShowProgress"
        Me.pbarShowProgress.Size = New System.Drawing.Size(110, 10)
        Me.pbarShowProgress.TabIndex = 29
        Me.pbarShowProgress.tbBackgroundImage = CType(resources.GetObject("pbarShowProgress.tbBackgroundImage"), System.Drawing.Image)
        Me.pbarShowProgress.tbCanDragValue = False
        Me.pbarShowProgress.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pbarShowProgress.tbDotImage = CType(resources.GetObject("pbarShowProgress.tbDotImage"), System.Drawing.Image)
        Me.pbarShowProgress.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pbarShowProgress.tbDotMouseDown = Nothing
        Me.pbarShowProgress.tbDotMouseHover = Nothing
        Me.pbarShowProgress.tbDotMouseLeave = Nothing
        Me.pbarShowProgress.tbDownloadImage = Global.iTong.My.Resources.Resources.app_progress_background
        Me.pbarShowProgress.tbDownMax = 100
        Me.pbarShowProgress.tbDownMin = 0
        Me.pbarShowProgress.tbDownValue = 0
        Me.pbarShowProgress.tbIsWaiting = False
        Me.pbarShowProgress.tbPlayImage = Global.iTong.My.Resources.Resources.app_progress_value
        Me.pbarShowProgress.tbPlayMax = 100
        Me.pbarShowProgress.tbPlayMin = 0
        Me.pbarShowProgress.tbPlayValue = 50
        Me.pbarShowProgress.tbShowDot = False
        Me.pbarShowProgress.tbShowText = True
        Me.pbarShowProgress.tbSplit = "4,0,4,0"
        Me.pbarShowProgress.Text = "TbControlBar1"
        Me.pbarShowProgress.Visible = False
        '
        'lblTuiProgress
        '
        Me.lblTuiProgress.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblTuiProgress.AutoEllipsis = True
        Me.lblTuiProgress.BackColor = System.Drawing.Color.Transparent
        Me.lblTuiProgress.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblTuiProgress.ForeColor = System.Drawing.Color.Black
        Me.lblTuiProgress.Location = New System.Drawing.Point(463, 453)
        Me.lblTuiProgress.Name = "lblTuiProgress"
        Me.lblTuiProgress.Size = New System.Drawing.Size(215, 21)
        Me.lblTuiProgress.TabIndex = 35
        Me.lblTuiProgress.tbAdriftWhenHover = False
        Me.lblTuiProgress.tbAutoEllipsis = True
        Me.lblTuiProgress.tbAutoSize = False
        Me.lblTuiProgress.tbHideImage = False
        Me.lblTuiProgress.tbIconImage = Nothing
        Me.lblTuiProgress.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTuiProgress.tbIconPlaceText = 5
        Me.lblTuiProgress.tbShadow = False
        Me.lblTuiProgress.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTuiProgress.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTuiProgress.tbShowScrolling = False
        Me.lblTuiProgress.Text = "正在下载手机版： 10%"
        Me.lblTuiProgress.TextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.lblTuiProgress.Visible = False
        '
        'chkNoShow
        '
        Me.chkNoShow.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.chkNoShow.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkNoShow.BackColor = System.Drawing.Color.Transparent
        Me.chkNoShow.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkNoShow.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkNoShow.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkNoShow.Location = New System.Drawing.Point(29, 535)
        Me.chkNoShow.Name = "chkNoShow"
        Me.chkNoShow.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkNoShow.Size = New System.Drawing.Size(80, 18)
        Me.chkNoShow.TabIndex = 52
        Me.chkNoShow.tbAdriftIconWhenHover = False
        Me.chkNoShow.tbAutoSize = False
        Me.chkNoShow.tbAutoSizeEx = True
        Me.chkNoShow.tbIconChecked = CType(resources.GetObject("chkNoShow.tbIconChecked"), System.Drawing.Image)
        Me.chkNoShow.tbIconCheckedMouseDown = Nothing
        Me.chkNoShow.tbIconCheckedMouseHover = Nothing
        Me.chkNoShow.tbIconCheckedMouseLeave = Nothing
        Me.chkNoShow.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbIconHoldPlace = True
        Me.chkNoShow.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkNoShow.tbIconIndeterminate = CType(resources.GetObject("chkNoShow.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkNoShow.tbIconIndeterminateMouseDown = Nothing
        Me.chkNoShow.tbIconIndeterminateMouseHover = Nothing
        Me.chkNoShow.tbIconIndeterminateMouseLeave = Nothing
        Me.chkNoShow.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbIconPlaceText = 1
        Me.chkNoShow.tbIconUnChecked = CType(resources.GetObject("chkNoShow.tbIconUnChecked"), System.Drawing.Image)
        Me.chkNoShow.tbIconUnCheckedMouseDown = Nothing
        Me.chkNoShow.tbIconUnCheckedMouseHover = Nothing
        Me.chkNoShow.tbIconUnCheckedMouseLeave = Nothing
        Me.chkNoShow.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbImageBackground = Nothing
        Me.chkNoShow.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkNoShow.tbImageCheckedMouseDown = Nothing
        Me.chkNoShow.tbImageCheckedMouseHover = Nothing
        Me.chkNoShow.tbImageCheckedMouseLeave = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseDown = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseHover = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseLeave = Nothing
        Me.chkNoShow.tbReadOnly = False
        Me.chkNoShow.tbShadow = False
        Me.chkNoShow.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkNoShow.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkNoShow.tbSplit = "3,3,3,3"
        Me.chkNoShow.tbToolTip = ""
        Me.chkNoShow.Text = "不再提醒"
        Me.chkNoShow.UseVisualStyleBackColor = False
        '
        'pnlContainer
        '
        Me.pnlContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContainer.BackgroundImage = Global.iTong.My.Resources.Resources.webclip_step1
        Me.pnlContainer.Controls.Add(Me.pnlInstallTui)
        Me.pnlContainer.Controls.Add(Me.btnBack)
        Me.pnlContainer.Controls.Add(Me.btnFinish)
        Me.pnlContainer.Controls.Add(Me.chkNoShow)
        Me.pnlContainer.Controls.Add(Me.btnInstall)
        Me.pnlContainer.Controls.Add(Me.lblTuiProgress)
        Me.pnlContainer.Controls.Add(Me.pbarShowProgress)
        Me.pnlContainer.Location = New System.Drawing.Point(1, 31)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(835, 572)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 53
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlInstallTui
        '
        Me.pnlInstallTui.BackColor = System.Drawing.Color.FromArgb(CType(CType(244, Byte), Integer), CType(CType(245, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlInstallTui.Controls.Add(Me.btnOk)
        Me.pnlInstallTui.Controls.Add(Me.lblStepThree)
        Me.pnlInstallTui.Controls.Add(Me.btnStep3)
        Me.pnlInstallTui.Controls.Add(Me.lblStepTwo)
        Me.pnlInstallTui.Controls.Add(Me.btnStep2)
        Me.pnlInstallTui.Controls.Add(Me.lblStepOne)
        Me.pnlInstallTui.Controls.Add(Me.btnStep1)
        Me.pnlInstallTui.Controls.Add(Me.picOperateStep)
        Me.pnlInstallTui.Controls.Add(Me.lblInstallTip)
        Me.pnlInstallTui.Controls.Add(Me.lblInstallWebTui)
        Me.pnlInstallTui.Controls.Add(Me.picWebTuiMoblie)
        Me.pnlInstallTui.Controls.Add(Me.TbPanel1)
        Me.pnlInstallTui.Location = New System.Drawing.Point(0, 0)
        Me.pnlInstallTui.Name = "pnlInstallTui"
        Me.pnlInstallTui.Size = New System.Drawing.Size(136, 572)
        Me.pnlInstallTui.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlInstallTui.TabIndex = 55
        Me.pnlInstallTui.tbBackgroundImage = Nothing
        Me.pnlInstallTui.tbShowWatermark = False
        Me.pnlInstallTui.tbSplit = "0,0,0,0"
        Me.pnlInstallTui.tbWatermark = Nothing
        Me.pnlInstallTui.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlInstallTui.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlInstallTui.Visible = False
        '
        'btnOk
        '
        Me.btnOk.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnOk.BackColor = System.Drawing.Color.Transparent
        Me.btnOk.BindingForm = Nothing
        Me.btnOk.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btnOk.ForeColor = System.Drawing.Color.White
        Me.btnOk.Location = New System.Drawing.Point(5, 488)
        Me.btnOk.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnOk.Name = "btnOk"
        Me.btnOk.Padding = New System.Windows.Forms.Padding(23, 2, 23, 2)
        Me.btnOk.Selectable = True
        Me.btnOk.Size = New System.Drawing.Size(113, 34)
        Me.btnOk.TabIndex = 119
        Me.btnOk.tbAdriftIconWhenHover = False
        Me.btnOk.tbAutoSize = False
        Me.btnOk.tbAutoSizeEx = True
        Me.btnOk.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOk.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOk.tbBadgeNumber = 0
        Me.btnOk.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOk.tbEndEllipsis = False
        Me.btnOk.tbIconHoldPlace = True
        Me.btnOk.tbIconImage = Nothing
        Me.btnOk.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOk.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOk.tbIconMore = False
        Me.btnOk.tbIconMouseDown = Nothing
        Me.btnOk.tbIconMouseHover = Nothing
        Me.btnOk.tbIconMouseLeave = Nothing
        Me.btnOk.tbIconPlaceText = 2
        Me.btnOk.tbIconReadOnly = Nothing
        Me.btnOk.tbImageMouseDown = Nothing
        Me.btnOk.tbImageMouseHover = Nothing
        Me.btnOk.tbImageMouseLeave = Nothing
        Me.btnOk.tbProgressValue = 50
        Me.btnOk.tbReadOnly = False
        Me.btnOk.tbReadOnlyText = False
        Me.btnOk.tbShadow = False
        Me.btnOk.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOk.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOk.tbShowDot = False
        Me.btnOk.tbShowMoreIconImg = CType(resources.GetObject("btnOk.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOk.tbShowNew = False
        Me.btnOk.tbShowProgress = False
        Me.btnOk.tbShowTip = True
        Me.btnOk.tbShowToolTipOnButton = False
        Me.btnOk.tbSplit = "13,11,13,11"
        Me.btnOk.tbText = "我知道了"
        Me.btnOk.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOk.tbTextColor = System.Drawing.Color.White
        Me.btnOk.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOk.tbTextColorDown = System.Drawing.Color.White
        Me.btnOk.tbTextColorHover = System.Drawing.Color.White
        Me.btnOk.tbTextMouseDownPlace = 0
        Me.btnOk.tbToolTip = ""
        Me.btnOk.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOk.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOk.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOk.VisibleEx = True
        '
        'lblStepThree
        '
        Me.lblStepThree.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblStepThree.BackColor = System.Drawing.Color.White
        Me.lblStepThree.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStepThree.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStepThree.Location = New System.Drawing.Point(217, 411)
        Me.lblStepThree.Name = "lblStepThree"
        Me.lblStepThree.Size = New System.Drawing.Size(182, 51)
        Me.lblStepThree.TabIndex = 32
        Me.lblStepThree.Text = "安装后，即可在桌面查看已安装的同步推"
        '
        'btnStep3
        '
        Me.btnStep3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep3.BackColor = System.Drawing.Color.Transparent
        Me.btnStep3.BindingForm = Nothing
        Me.btnStep3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep3.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep3.Location = New System.Drawing.Point(196, 411)
        Me.btnStep3.Name = "btnStep3"
        Me.btnStep3.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep3.Selectable = True
        Me.btnStep3.Size = New System.Drawing.Size(18, 17)
        Me.btnStep3.TabIndex = 33
        Me.btnStep3.tbAdriftIconWhenHover = False
        Me.btnStep3.tbAutoSize = False
        Me.btnStep3.tbAutoSizeEx = True
        Me.btnStep3.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep3
        Me.btnStep3.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep3.tbBadgeNumber = 0
        Me.btnStep3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep3.tbEndEllipsis = False
        Me.btnStep3.tbIconHoldPlace = True
        Me.btnStep3.tbIconImage = Nothing
        Me.btnStep3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep3.tbIconMore = False
        Me.btnStep3.tbIconMouseDown = Nothing
        Me.btnStep3.tbIconMouseHover = Nothing
        Me.btnStep3.tbIconMouseLeave = Nothing
        Me.btnStep3.tbIconPlaceText = 2
        Me.btnStep3.tbIconReadOnly = Nothing
        Me.btnStep3.tbImageMouseDown = Nothing
        Me.btnStep3.tbImageMouseHover = Nothing
        Me.btnStep3.tbImageMouseLeave = Nothing
        Me.btnStep3.tbProgressValue = 50
        Me.btnStep3.tbReadOnly = False
        Me.btnStep3.tbReadOnlyText = False
        Me.btnStep3.tbShadow = False
        Me.btnStep3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep3.tbShowDot = False
        Me.btnStep3.tbShowMoreIconImg = CType(resources.GetObject("btnStep3.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep3.tbShowNew = False
        Me.btnStep3.tbShowProgress = False
        Me.btnStep3.tbShowTip = True
        Me.btnStep3.tbShowToolTipOnButton = False
        Me.btnStep3.tbSplit = "0,0,0,0"
        Me.btnStep3.tbText = ""
        Me.btnStep3.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.tbTextColor = System.Drawing.Color.White
        Me.btnStep3.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep3.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep3.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep3.tbTextMouseDownPlace = 0
        Me.btnStep3.tbToolTip = ""
        Me.btnStep3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep3.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep3.VisibleEx = True
        '
        'lblStepTwo
        '
        Me.lblStepTwo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblStepTwo.BackColor = System.Drawing.Color.White
        Me.lblStepTwo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStepTwo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStepTwo.Location = New System.Drawing.Point(-14, 411)
        Me.lblStepTwo.Name = "lblStepTwo"
        Me.lblStepTwo.Size = New System.Drawing.Size(182, 51)
        Me.lblStepTwo.TabIndex = 32
        Me.lblStepTwo.Text = "点击确定【安装】"
        '
        'btnStep2
        '
        Me.btnStep2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep2.BackColor = System.Drawing.Color.Transparent
        Me.btnStep2.BindingForm = Nothing
        Me.btnStep2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep2.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep2.Location = New System.Drawing.Point(-34, 411)
        Me.btnStep2.Name = "btnStep2"
        Me.btnStep2.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep2.Selectable = True
        Me.btnStep2.Size = New System.Drawing.Size(18, 17)
        Me.btnStep2.TabIndex = 33
        Me.btnStep2.tbAdriftIconWhenHover = False
        Me.btnStep2.tbAutoSize = False
        Me.btnStep2.tbAutoSizeEx = True
        Me.btnStep2.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep2
        Me.btnStep2.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep2.tbBadgeNumber = 0
        Me.btnStep2.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep2.tbEndEllipsis = False
        Me.btnStep2.tbIconHoldPlace = True
        Me.btnStep2.tbIconImage = Nothing
        Me.btnStep2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep2.tbIconMore = False
        Me.btnStep2.tbIconMouseDown = Nothing
        Me.btnStep2.tbIconMouseHover = Nothing
        Me.btnStep2.tbIconMouseLeave = Nothing
        Me.btnStep2.tbIconPlaceText = 2
        Me.btnStep2.tbIconReadOnly = Nothing
        Me.btnStep2.tbImageMouseDown = Nothing
        Me.btnStep2.tbImageMouseHover = Nothing
        Me.btnStep2.tbImageMouseLeave = Nothing
        Me.btnStep2.tbProgressValue = 50
        Me.btnStep2.tbReadOnly = False
        Me.btnStep2.tbReadOnlyText = False
        Me.btnStep2.tbShadow = False
        Me.btnStep2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep2.tbShowDot = False
        Me.btnStep2.tbShowMoreIconImg = CType(resources.GetObject("btnStep2.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep2.tbShowNew = False
        Me.btnStep2.tbShowProgress = False
        Me.btnStep2.tbShowTip = True
        Me.btnStep2.tbShowToolTipOnButton = False
        Me.btnStep2.tbSplit = "0,0,0,0"
        Me.btnStep2.tbText = ""
        Me.btnStep2.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.tbTextColor = System.Drawing.Color.White
        Me.btnStep2.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep2.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep2.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep2.tbTextMouseDownPlace = 0
        Me.btnStep2.tbToolTip = ""
        Me.btnStep2.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep2.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep2.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep2.VisibleEx = True
        '
        'lblStepOne
        '
        Me.lblStepOne.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblStepOne.BackColor = System.Drawing.Color.White
        Me.lblStepOne.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStepOne.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStepOne.Location = New System.Drawing.Point(-245, 411)
        Me.lblStepOne.Name = "lblStepOne"
        Me.lblStepOne.Size = New System.Drawing.Size(182, 51)
        Me.lblStepOne.TabIndex = 32
        Me.lblStepOne.Text = "点击手机弹出的""安装描述文件""旁的【安装】"
        '
        'btnStep1
        '
        Me.btnStep1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStep1.BackColor = System.Drawing.Color.Transparent
        Me.btnStep1.BindingForm = Nothing
        Me.btnStep1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStep1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStep1.Location = New System.Drawing.Point(-264, 411)
        Me.btnStep1.Name = "btnStep1"
        Me.btnStep1.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStep1.Selectable = True
        Me.btnStep1.Size = New System.Drawing.Size(18, 17)
        Me.btnStep1.TabIndex = 33
        Me.btnStep1.tbAdriftIconWhenHover = False
        Me.btnStep1.tbAutoSize = False
        Me.btnStep1.tbAutoSizeEx = True
        Me.btnStep1.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_web_installStep1
        Me.btnStep1.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStep1.tbBadgeNumber = 0
        Me.btnStep1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStep1.tbEndEllipsis = False
        Me.btnStep1.tbIconHoldPlace = True
        Me.btnStep1.tbIconImage = Nothing
        Me.btnStep1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStep1.tbIconMore = False
        Me.btnStep1.tbIconMouseDown = Nothing
        Me.btnStep1.tbIconMouseHover = Nothing
        Me.btnStep1.tbIconMouseLeave = Nothing
        Me.btnStep1.tbIconPlaceText = 2
        Me.btnStep1.tbIconReadOnly = Nothing
        Me.btnStep1.tbImageMouseDown = Nothing
        Me.btnStep1.tbImageMouseHover = Nothing
        Me.btnStep1.tbImageMouseLeave = Nothing
        Me.btnStep1.tbProgressValue = 50
        Me.btnStep1.tbReadOnly = False
        Me.btnStep1.tbReadOnlyText = False
        Me.btnStep1.tbShadow = False
        Me.btnStep1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStep1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStep1.tbShowDot = False
        Me.btnStep1.tbShowMoreIconImg = CType(resources.GetObject("btnStep1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStep1.tbShowNew = False
        Me.btnStep1.tbShowProgress = False
        Me.btnStep1.tbShowTip = True
        Me.btnStep1.tbShowToolTipOnButton = False
        Me.btnStep1.tbSplit = "0,0,0,0"
        Me.btnStep1.tbText = ""
        Me.btnStep1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.tbTextColor = System.Drawing.Color.White
        Me.btnStep1.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStep1.tbTextColorDown = System.Drawing.Color.White
        Me.btnStep1.tbTextColorHover = System.Drawing.Color.White
        Me.btnStep1.tbTextMouseDownPlace = 0
        Me.btnStep1.tbToolTip = ""
        Me.btnStep1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStep1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStep1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStep1.VisibleEx = True
        '
        'picOperateStep
        '
        Me.picOperateStep.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picOperateStep.BackColor = System.Drawing.Color.White
        Me.picOperateStep.Image = Global.iTong.My.Resources.Resources.tui_web_InstallOperate
        Me.picOperateStep.Location = New System.Drawing.Point(-285, 144)
        Me.picOperateStep.Name = "picOperateStep"
        Me.picOperateStep.Padding = New System.Windows.Forms.Padding(0, 20, 0, 65)
        Me.picOperateStep.Size = New System.Drawing.Size(706, 320)
        Me.picOperateStep.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picOperateStep.TabIndex = 3
        Me.picOperateStep.TabStop = False
        Me.picOperateStep.tbAutoSize = False
        Me.picOperateStep.tbBackgroundImage = Nothing
        Me.picOperateStep.tbSplit = "0,0,0,0"
        '
        'lblInstallTip
        '
        Me.lblInstallTip.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblInstallTip.AutoSize = True
        Me.lblInstallTip.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblInstallTip.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblInstallTip.Location = New System.Drawing.Point(-192, 84)
        Me.lblInstallTip.Name = "lblInstallTip"
        Me.lblInstallTip.Size = New System.Drawing.Size(569, 12)
        Me.lblInstallTip.TabIndex = 2
        Me.lblInstallTip.tbAdriftWhenHover = False
        Me.lblInstallTip.tbAutoEllipsis = False
        Me.lblInstallTip.tbAutoSize = True
        Me.lblInstallTip.tbHideImage = False
        Me.lblInstallTip.tbIconImage = Nothing
        Me.lblInstallTip.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblInstallTip.tbIconPlaceText = 5
        Me.lblInstallTip.tbShadow = False
        Me.lblInstallTip.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblInstallTip.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblInstallTip.tbShowScrolling = False
        Me.lblInstallTip.Text = "为保证同步推网页版的顺利安装，需要你在手机端做如下操作（仅为获取设备端安装许可，请放心操作）。"
        '
        'lblInstallWebTui
        '
        Me.lblInstallWebTui.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblInstallWebTui.AutoSize = True
        Me.lblInstallWebTui.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblInstallWebTui.ForeColor = System.Drawing.Color.FromArgb(CType(CType(80, Byte), Integer), CType(CType(81, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblInstallWebTui.Location = New System.Drawing.Point(-192, 54)
        Me.lblInstallWebTui.Name = "lblInstallWebTui"
        Me.lblInstallWebTui.Size = New System.Drawing.Size(218, 22)
        Me.lblInstallWebTui.TabIndex = 1
        Me.lblInstallWebTui.tbAdriftWhenHover = False
        Me.lblInstallWebTui.tbAutoEllipsis = False
        Me.lblInstallWebTui.tbAutoSize = True
        Me.lblInstallWebTui.tbHideImage = False
        Me.lblInstallWebTui.tbIconImage = Nothing
        Me.lblInstallWebTui.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblInstallWebTui.tbIconPlaceText = 5
        Me.lblInstallWebTui.tbShadow = False
        Me.lblInstallWebTui.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblInstallWebTui.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblInstallWebTui.tbShowScrolling = False
        Me.lblInstallWebTui.Text = "正在为您安装同步推网页版："
        '
        'picWebTuiMoblie
        '
        Me.picWebTuiMoblie.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picWebTuiMoblie.Image = Global.iTong.My.Resources.Resources.tui_web_mobliePicture
        Me.picWebTuiMoblie.Location = New System.Drawing.Point(-246, 42)
        Me.picWebTuiMoblie.Name = "picWebTuiMoblie"
        Me.picWebTuiMoblie.Size = New System.Drawing.Size(39, 70)
        Me.picWebTuiMoblie.TabIndex = 0
        Me.picWebTuiMoblie.TabStop = False
        Me.picWebTuiMoblie.tbAutoSize = False
        Me.picWebTuiMoblie.tbBackgroundImage = Nothing
        Me.picWebTuiMoblie.tbSplit = "0,0,0,0"
        '
        'TbPanel1
        '
        Me.TbPanel1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.TbPanel1.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.TbPanel1.Location = New System.Drawing.Point(-286, 143)
        Me.TbPanel1.Name = "TbPanel1"
        Me.TbPanel1.Size = New System.Drawing.Size(708, 322)
        Me.TbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel1.TabIndex = 120
        Me.TbPanel1.tbBackgroundImage = Nothing
        Me.TbPanel1.tbShowWatermark = False
        Me.TbPanel1.tbSplit = "0,0,0,0"
        Me.TbPanel1.tbWatermark = Nothing
        Me.TbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnBack
        '
        Me.btnBack.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBack.BackColor = System.Drawing.Color.Transparent
        Me.btnBack.BindingForm = Nothing
        Me.btnBack.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBack.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnBack.Location = New System.Drawing.Point(636, 521)
        Me.btnBack.Name = "btnBack"
        Me.btnBack.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnBack.Selectable = True
        Me.btnBack.Size = New System.Drawing.Size(65, 23)
        Me.btnBack.TabIndex = 54
        Me.btnBack.tbAdriftIconWhenHover = False
        Me.btnBack.tbAutoSize = False
        Me.btnBack.tbAutoSizeEx = False
        Me.btnBack.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnBack.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBack.tbBadgeNumber = 0
        Me.btnBack.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBack.tbEndEllipsis = False
        Me.btnBack.tbIconHoldPlace = True
        Me.btnBack.tbIconImage = Nothing
        Me.btnBack.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBack.tbIconMore = False
        Me.btnBack.tbIconMouseDown = Nothing
        Me.btnBack.tbIconMouseHover = Nothing
        Me.btnBack.tbIconMouseLeave = Nothing
        Me.btnBack.tbIconPlaceText = 2
        Me.btnBack.tbIconReadOnly = Nothing
        Me.btnBack.tbImageMouseDown = Nothing
        Me.btnBack.tbImageMouseHover = Nothing
        Me.btnBack.tbImageMouseLeave = Nothing
        Me.btnBack.tbProgressValue = 50
        Me.btnBack.tbReadOnly = False
        Me.btnBack.tbReadOnlyText = False
        Me.btnBack.tbShadow = False
        Me.btnBack.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnBack.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnBack.tbShowDot = False
        Me.btnBack.tbShowMoreIconImg = CType(resources.GetObject("btnBack.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBack.tbShowNew = False
        Me.btnBack.tbShowProgress = False
        Me.btnBack.tbShowTip = True
        Me.btnBack.tbShowToolTipOnButton = False
        Me.btnBack.tbSplit = "13,11,13,11"
        Me.btnBack.tbText = "取消"
        Me.btnBack.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBack.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBack.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBack.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBack.tbTextMouseDownPlace = 0
        Me.btnBack.tbToolTip = ""
        Me.btnBack.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBack.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBack.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBack.Visible = False
        Me.btnBack.VisibleEx = True
        '
        'btnFinish
        '
        Me.btnFinish.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnFinish.BackColor = System.Drawing.Color.Transparent
        Me.btnFinish.BindingForm = Nothing
        Me.btnFinish.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFinish.Location = New System.Drawing.Point(723, 521)
        Me.btnFinish.Name = "btnFinish"
        Me.btnFinish.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnFinish.Selectable = True
        Me.btnFinish.Size = New System.Drawing.Size(65, 23)
        Me.btnFinish.TabIndex = 53
        Me.btnFinish.tbAdriftIconWhenHover = False
        Me.btnFinish.tbAutoSize = False
        Me.btnFinish.tbAutoSizeEx = False
        Me.btnFinish.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnFinish.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnFinish.tbBadgeNumber = 0
        Me.btnFinish.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFinish.tbEndEllipsis = False
        Me.btnFinish.tbIconHoldPlace = True
        Me.btnFinish.tbIconImage = Nothing
        Me.btnFinish.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFinish.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnFinish.tbIconMore = False
        Me.btnFinish.tbIconMouseDown = Nothing
        Me.btnFinish.tbIconMouseHover = Nothing
        Me.btnFinish.tbIconMouseLeave = Nothing
        Me.btnFinish.tbIconPlaceText = 2
        Me.btnFinish.tbIconReadOnly = Nothing
        Me.btnFinish.tbImageMouseDown = Nothing
        Me.btnFinish.tbImageMouseHover = Nothing
        Me.btnFinish.tbImageMouseLeave = Nothing
        Me.btnFinish.tbProgressValue = 50
        Me.btnFinish.tbReadOnly = False
        Me.btnFinish.tbReadOnlyText = False
        Me.btnFinish.tbShadow = False
        Me.btnFinish.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnFinish.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnFinish.tbShowDot = False
        Me.btnFinish.tbShowMoreIconImg = CType(resources.GetObject("btnFinish.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFinish.tbShowNew = False
        Me.btnFinish.tbShowProgress = False
        Me.btnFinish.tbShowTip = True
        Me.btnFinish.tbShowToolTipOnButton = False
        Me.btnFinish.tbSplit = "13,11,13,11"
        Me.btnFinish.tbText = "保存"
        Me.btnFinish.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFinish.tbTextColor = System.Drawing.Color.White
        Me.btnFinish.tbTextColorDisable = System.Drawing.Color.White
        Me.btnFinish.tbTextColorDown = System.Drawing.Color.White
        Me.btnFinish.tbTextColorHover = System.Drawing.Color.White
        Me.btnFinish.tbTextMouseDownPlace = 0
        Me.btnFinish.tbToolTip = ""
        Me.btnFinish.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFinish.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFinish.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFinish.Visible = False
        Me.btnFinish.VisibleEx = True
        '
        'btnFeedback
        '
        Me.btnFeedback.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnFeedback.BackColor = System.Drawing.Color.Transparent
        Me.btnFeedback.BindingForm = Nothing
        Me.btnFeedback.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnFeedback.Location = New System.Drawing.Point(662, 3)
        Me.btnFeedback.Margin = New System.Windows.Forms.Padding(0)
        Me.btnFeedback.Name = "btnFeedback"
        Me.btnFeedback.Padding = New System.Windows.Forms.Padding(4, 0, 0, 0)
        Me.btnFeedback.Selectable = True
        Me.btnFeedback.Size = New System.Drawing.Size(57, 21)
        Me.btnFeedback.TabIndex = 54
        Me.btnFeedback.tbAdriftIconWhenHover = False
        Me.btnFeedback.tbAutoSize = True
        Me.btnFeedback.tbAutoSizeEx = True
        Me.btnFeedback.tbBackgroundImage = Nothing
        Me.btnFeedback.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnFeedback.tbBadgeNumber = 0
        Me.btnFeedback.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFeedback.tbEndEllipsis = False
        Me.btnFeedback.tbIconHoldPlace = True
        Me.btnFeedback.tbIconImage = Nothing
        Me.btnFeedback.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnFeedback.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnFeedback.tbIconMore = False
        Me.btnFeedback.tbIconMouseDown = Nothing
        Me.btnFeedback.tbIconMouseHover = Nothing
        Me.btnFeedback.tbIconMouseLeave = Nothing
        Me.btnFeedback.tbIconPlaceText = 2
        Me.btnFeedback.tbIconReadOnly = Nothing
        Me.btnFeedback.tbImageMouseDown = Nothing
        Me.btnFeedback.tbImageMouseHover = Global.iTong.My.Resources.Resources.btn_1_transparent
        Me.btnFeedback.tbImageMouseLeave = Nothing
        Me.btnFeedback.tbProgressValue = 50
        Me.btnFeedback.tbReadOnly = False
        Me.btnFeedback.tbReadOnlyText = False
        Me.btnFeedback.tbShadow = False
        Me.btnFeedback.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFeedback.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFeedback.tbShowDot = False
        Me.btnFeedback.tbShowMoreIconImg = CType(resources.GetObject("btnFeedback.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFeedback.tbShowNew = False
        Me.btnFeedback.tbShowProgress = False
        Me.btnFeedback.tbShowTip = True
        Me.btnFeedback.tbShowToolTipOnButton = False
        Me.btnFeedback.tbSplit = "4,4,4,4"
        Me.btnFeedback.tbText = "意见反馈"
        Me.btnFeedback.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFeedback.tbTextColor = System.Drawing.Color.White
        Me.btnFeedback.tbTextColorDisable = System.Drawing.Color.White
        Me.btnFeedback.tbTextColorDown = System.Drawing.Color.White
        Me.btnFeedback.tbTextColorHover = System.Drawing.Color.White
        Me.btnFeedback.tbTextMouseDownPlace = 0
        Me.btnFeedback.tbToolTip = ""
        Me.btnFeedback.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFeedback.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFeedback.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFeedback.VisibleEx = True
        '
        'frmTuiInstall
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.White
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(837, 604)
        Me.Controls.Add(Me.pnlContainer)
        Me.Controls.Add(Me.btnFeedback)
        Me.MaximizeBox = False
        Me.MinimumSize = New System.Drawing.Size(11, 40)
        Me.Name = "frmTuiInstall"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowIconOnForm = False
        Me.tbSplit = "5,32,6,8"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "同步助手（手机版）"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.btnFeedback, 0)
        Me.Controls.SetChildIndex(Me.pnlContainer, 0)
        Me.pnlContainer.ResumeLayout(False)
        Me.pnlInstallTui.ResumeLayout(False)
        CType(Me.picOperateStep, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picWebTuiMoblie, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btnInstall As tbButton
    Friend WithEvents pbarShowProgress As tbControlBar
    Friend WithEvents lblTuiProgress As tbLabel
    Friend WithEvents chkNoShow As tbCheckBox
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents btnBack As tbButton
    Friend WithEvents btnFinish As tbButton
    Friend WithEvents btnFeedback As iTong.Components.tbButton
    Friend WithEvents pnlInstallTui As iTong.Components.tbPanel
    Friend WithEvents picOperateStep As iTong.Components.tbPictureBox
    Friend WithEvents lblInstallTip As iTong.Components.tbLabel
    Friend WithEvents lblInstallWebTui As iTong.Components.tbLabel
    Friend WithEvents picWebTuiMoblie As iTong.Components.tbPictureBox
    Friend WithEvents lblStepThree As System.Windows.Forms.Label
    Friend WithEvents btnStep3 As iTong.Components.tbButton
    Friend WithEvents lblStepTwo As System.Windows.Forms.Label
    Friend WithEvents btnStep2 As iTong.Components.tbButton
    Friend WithEvents lblStepOne As System.Windows.Forms.Label
    Friend WithEvents btnStep1 As iTong.Components.tbButton
    Friend WithEvents btnOk As iTong.Components.tbButton
    Friend WithEvents TbPanel1 As iTong.Components.tbPanel
End Class
