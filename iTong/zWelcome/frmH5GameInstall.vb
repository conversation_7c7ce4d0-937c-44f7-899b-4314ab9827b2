﻿Public Class frmH5GameInstall

#Region "--- 初始化/窗体事件 ---"
    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.mDevice = device
        Me.Icon = My.Resources.iTong
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("H5Game.Button.Games")               '小游戏
        Me.btnInstall.Text = Me.Language.GetString("Download.Button.Install") '"立即安装"

        Me.lblInstallH5Game.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.btnInstall.Font = Common.CreateFont("微软雅黑", 16.0!, FontStyle.Regular)
        Me.btnOk.Font = Common.CreateFont("微软雅黑", 16.0!, FontStyle.Regular)

        If Folder.LangType <> LanguageType.th_TH Then
            Me.btnInstall.Font = Common.CreateFont("微软雅黑", 16.0!, FontStyle.Regular)
            Me.btnInstall.tbTextColor = Color.FromArgb(255, 85, 35)
            Me.btnInstall.tbTextColorDisable = Color.FromArgb(255, 85, 35)
            Me.btnInstall.tbTextColorDown = Color.FromArgb(255, 85, 35)
            Me.btnInstall.tbTextColorHover = Color.FromArgb(255, 85, 35)
            Me.btnInstall.tbIconImage = Nothing
            Me.btnInstall.tbTextAlign = ContentAlignment.MiddleCenter
            Me.btnInstall.Padding = New Padding(30, 2, 30, 2)
            Me.btnInstall.Size = New Size(170, 47)
            Me.btnInstall.Location = New Point((Me.Width - Me.btnInstall.Width) \ 2, 470)
        End If

        Me.lblInstallH5Game.Text = Me.Language.GetString("H5Game.Label.InstallTitle") '正在为您安装同步助手游戏：
        Me.lblInstallTip.Text = Me.Language.GetString("H5Game.Message.InstallTip")    '安装时，需要移动设备首先允许【小游戏安装描述文件】，仅用于小游戏，请放心安装
        Me.lblStepOne.Text = Me.Language.GetString("H5Game.Message.ClickInstall")  '点击安装
        Me.lblStepTwo.Text = Me.Language.GetString("H5Game.Message.ConfirmInstall")  '确定安装
        Me.lblStepThree.Text = Me.Language.GetString("H5Game.Message.SeeAndUse") '即可在桌面查看使用
        Me.btnOk.Text = Me.Language.GetString("Common.ISee")   '我知道了
        Me.pnlInstallH5Game.Dock = DockStyle.Fill
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub

    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        MyBase.OnResize(e)

        Me.ResizeControlButton()
    End Sub

    Protected Overrides Sub OnActivated(ByVal e As System.EventArgs)
        MyBase.OnActivated(e)
        Me.ResizeControlButton()
    End Sub

#End Region

#Region "---  私有方法  ---"
    Private Sub ResizeControlButton()
        If Me.Owner IsNot Nothing AndAlso Me.Owner.WindowState <> FormWindowState.Maximized Then
            Me.StartPosition = FormStartPosition.Manual
            Me.Location = New Point(Me.Owner.Left + (Me.Owner.Width - Me.Width) \ 2, Me.Owner.Top + 30)
            Me.btn_minimize.Visible = False
            Me.btn_normal.Visible = False
        Else
            Me.btn_normal.Visible = False
            Me.btn_minimize.Location = New Point(Me.btn_close.Left - Me.btn_minimize.Width - 1, Me.btn_minimize.Top)
        End If
    End Sub
#End Region

#Region "--- 窗体事件 ---"
    Private Sub btnInstall_Click(sender As Object, e As EventArgs) Handles btnInstall.Click
        Try
            'WebSiteHelper.Instance.DownloadAppByIDEx(Me.mApplication, Me.mDevice, "1133438506", "", "小游戏")

            ' WebSiteHelper.Instance().Start2AnalyseDownloadUrlByDownloadCenter(Me.mApplication, ServerIniSetting.GetLemonUrl, "小游戏", "", ServerIniSetting.GetLemonItemId,"", False, "", Me.mDevice, False, "", "", False, False, True, Nothing, DownloadCenterSource.None)


            WebSiteHelper.Instance().DownloadIPA(Me.mDevice, ServerIniSetting.GetLemonUrl, "", "", "", "", "", False, Me.mDevice.Jailbreaked, "", ServerIniSetting.GetLemonSku)

            ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryH5GameInstall)

            'Me.pnlInstallH5Game.Visible = True
            'Me.pnlInstallH5Game.BringToFront()
            'ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryH5GameInstall)
            'Dim thd As New Threading.Thread(AddressOf InstallConfigurationProfile)
            'With thd
            '    .IsBackground = True
            '    .Start()
            'End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnInstall_Click")
        End Try
    End Sub

    'Private Sub InstallConfigurationProfile()
    '    Try
    '        Me.mDevice.InstallConfigurationProfile(My.Resources.h5_game)
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "InstallConfigurationProfile")
    '    End Try
    'End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        Me.Close()
    End Sub

#End Region

End Class