﻿Public Class frmDeviceList

    Private mParent As Control = Nothing
    Private mManager As MobileDeviceManager
    Private mApplication As IApplication = Nothing
    Private Shared mLockClick As New Object                      '这在打开的队列
    Private Shared mListOpening As New List(Of String)

    Public Sub New(ByVal app As IApplication, ByVal ctl As Control)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = app
        Me.Language = LanguageInterface.Instance()
        Me.mManager = MobileDeviceManager.Instance()

        Me.BackColor = Color.FromArgb(22, 22, 22)
        Me.FilletRadius = 5
        Me.mParent = ctl
        Me.PNGForm.ShowInTaskbar = False
        Me.CanbeMove = False
        Me.CanResize = False
        Me.PaddingEx = New Padding(19, 20, 19, 20)
        Me.tbAutoSize = False

        Me.InitDeviceList()
    End Sub

    Public Sub InitDeviceList()

        Dim intWidth As Integer = Me.flpDeviceList.Width
        Dim intHeight As Integer = 0

        For Each device As iPhoneDevice In Me.mManager.ConnectedDevices
            If Not device.IsConnected Then
                Continue For
            End If
            Dim btn As tbButton = Me.CreateItem(device, intWidth)
            intHeight += btn.Height + btn.Margin.Vertical

            Me.flpDeviceList.Controls.Add(btn)
        Next

        Me.Size = New Size(intWidth, intHeight)

        Dim imgBg As Image = Me.CreatePNG()
        'imgBg.Save(System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory) & "\device.png", Imaging.ImageFormat.Png)
        'Me.PNGForm.SetPngBackground(imgBg, 255)
        'imgBg.Dispose()
        Me.BackgroundImage = imgBg

    End Sub

    Private Function CreatePNG() As Image
        Dim img As Image = My.Resources.welcome_frm_bg
        Dim bmp As New Bitmap(Me.Width + Me.PaddingEx.Horizontal, Me.Height + Me.PaddingEx.Vertical)
        Dim g As Graphics = Graphics.FromImage(bmp)
        g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        g.Clear(Color.Transparent)

        GuiHelper.DrawImage(g, New Rectangle(0, 0, bmp.Width, bmp.Height), New tbSplitStructure(0, 20, 0, 20), img)

        g.Dispose()

        Return bmp
    End Function

    Private Function CreateItem(ByVal device As iPhoneDevice, ByVal intWidth As Integer)
        Dim btn As New tbButtonEx()
        With btn
            .Name = device.Identifier
            .tbText = device.DeviceName
            .Margin = New Padding(0, 0, 0, 0) ' New Padding(4, 4, 4, 4)
            .Size = New Size(intWidth - .Margin.Horizontal, 40)

            .tbTextColor = Color.White
            .BackColor = Me.flpDeviceList.BackColor
            .tbTextAlign = ContentAlignment.MiddleLeft
            .tbImageMouseHover = My.Resources.welcome_btn_bg_h
            .tbIconImageAlign = ContentAlignment.MiddleLeft
            .tbRingtIconPadding = New Padding(0, 3, 6, 0)
            .Padding = New System.Windows.Forms.Padding(5, 0, .tbRingtIconPadding.Right + My.Resources.welcome_icon_wifi.Width, 0)
            .tbEndEllipsis = True

            If CType(Me.mApplication, MainForm).mDevice Is device Then
                If device.ConnectMode = ConnectMode.USB Then
                    .tbRightIcon = My.Resources.welcome_icon_usb_h
                Else
                    .tbRightIcon = My.Resources.welcome_icon_wifi_h
                End If

                If device.ProductType.StartsWith("ipad", StringComparison.OrdinalIgnoreCase) Then
                    .tbIconImage = My.Resources.welcome_icon_ipad_h
                Else
                    .tbIconImage = My.Resources.welcome_icon_iphone_h
                End If
            Else
                If device.ConnectMode = ConnectMode.USB Then
                    .tbRightIcon = My.Resources.welcome_icon_usb
                Else
                    .tbRightIcon = My.Resources.welcome_icon_wifi
                End If

                If device.ProductType.StartsWith("ipad", StringComparison.OrdinalIgnoreCase) Then
                    .tbIconImage = My.Resources.welcome_icon_ipad
                Else
                    .tbIconImage = My.Resources.welcome_icon_iphone
                End If
            End If

            .Tag = device
        End With

        AddHandler btn.Click, AddressOf ItemClick

        Return btn
    End Function

    Private Sub ItemClick(ByVal sender As Object, ByVal e As EventArgs)
        Me.Close()

        ItemClick(Me.mApplication, sender)
    End Sub

    Private Shared Sub ItemClick(ByVal App As IApplication, ByVal sender As Object)
        SyncLock mLockClick
            If CType(App, MainForm).mDevice Is CType(sender, Control).Tag Then
                CType(App, MainForm).Activate()
            Else
                Try
                    Dim device As iPhoneDevice = CType(sender, Control).Tag
                    Dim frmExist As MainForm = Nothing
                    For Each frm As Form In Application.OpenForms
                        'Debug.Print(frm.Name & vbTab & frm.Text)
                        If TypeOf frm Is MainForm Then
                            Dim frmMain As MainForm = frm
                            If frmMain.mDevice IsNot Nothing AndAlso frmMain.mDevice.SerialNumber = device.SerialNumber Then
                                frmExist = frmMain
                                Exit For
                            End If
                        End If
                    Next

                    If frmExist Is Nothing Then
                        '如果包含说明正在创建
                        If mListOpening.Contains(device.Identifier) Then
                            Return
                        End If

                        '将正在创建窗体的设备，添加到列表。
                        If Not mListOpening.Contains(device.Identifier) Then
                            mListOpening.Add(device.Identifier)
                        End If

                        frmExist = New MainForm(device)

                        Dim frmOwner As Form = ProcForm.Instance().GetMainForm()
                        If frmOwner IsNot Nothing Then
                            frmExist.StartPosition = FormStartPosition.Manual
                            frmExist.Location = New Point(frmOwner.Left + 135, frmOwner.Top + 25)
                        End If

                        frmExist.Show()

                        '从正在创建的列表移除
                        If mListOpening.Contains(device.Identifier) Then
                            mListOpening.Remove(device.Identifier)
                        End If
                    Else
                        frmExist.Show()
                        '当窗体最小化时，先SHOW再设置WindowState，这样会窗体显示出来
                        If frmExist.WindowState = FormWindowState.Minimized Then
                            frmExist.WindowState = FormWindowState.Normal
                        End If
                        frmExist.Activate()
                    End If
                Catch ex As Exception
                End Try
            End If
        End SyncLock
    End Sub

    Private Sub mTimerShow_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles mTimerShow.Tick
        If Me.Visible AndAlso CheckPoint(Me) = False AndAlso CheckPoint(Me.mParent) = False Then
            Me.mTimerShow.Stop()
            Me.Close()
        End If
    End Sub

    '检测鼠标位置
    Private Function CheckPoint(ByVal c As Windows.Forms.Control) As Boolean
        Dim rect As New Rectangle(c.PointToScreen(New Point(0, 0)), c.Size)
        Dim pos As Point = Windows.Forms.Cursor.Position

        Return rect.Contains(pos)
    End Function

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.mTimerShow.Start()
    End Sub

End Class

Public Class tbButtonEx
    Inherits tbButton

    Private mRightIcon As Image
    Private mRingtIconPadding As Padding = Padding.Empty

    Public Property tbRingtIconPadding() As Padding
        Get
            Return Me.mRingtIconPadding
        End Get
        Set(ByVal value As Padding)
            Me.mRingtIconPadding = value
            Me.Invalidate()
        End Set
    End Property

    Public Property tbRightIcon() As Image
        Get
            Return Me.mRightIcon
        End Get
        Set(ByVal value As Image)
            Me.mRightIcon = value
            Me.Invalidate()
        End Set
    End Property

    Protected Overrides Sub OnPaint(ByVal e As System.Windows.Forms.PaintEventArgs)
        MyBase.OnPaint(e)

        Dim img As Image = Me.mRightIcon
        If img IsNot Nothing Then
            Dim rectDraw As New Rectangle(Me.Width - Me.mRingtIconPadding.Right - img.Width, (Me.Height - img.Height) \ 2, img.Width, img.Height)
            e.Graphics.DrawImage(img, rectDraw, New Rectangle(0, 0, img.Width, img.Height), GraphicsUnit.Pixel)
        End If
    End Sub

End Class
