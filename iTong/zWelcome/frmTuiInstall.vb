﻿Imports System.Threading
Public Class frmTuiInstall

    Private Enum InstallStatus
        Normal
        Finished
        ReInstall
    End Enum

    Private mSerp As Integer = 1
    Private mDownManage As MultiThreadDownload
    Private mInstallHelper As iPhoneInstallHelper = Nothing
    Private mUpdateHelper As CoreUpdateHelper = Nothing
    Private mJailbresk As Boolean = False
    Private mHideNoShowButton As Boolean = False
    Private mStatus As InstallStatus = InstallStatus.Normal
    Private mErrorDocumentText As String = String.Empty
    'Private mBrowser As tbWebBrowserEx = Nothing
    Private mThreadTopicDownload As Thread
    'Private mblnUserGuide As Boolean = False
    Private mDock As Boolean = False
    Private mthrDownBackPic As Thread
    Private mPicTui As Image = Nothing
    Private mAuthorizeByAppleID As AuthorizeByAppleID = Nothing

    '是否显示chkNoShow控件
    Public Property HideNoShowButton() As Boolean
        Get
            Return Me.mHideNoShowButton
        End Get
        Set(ByVal value As Boolean)
            Me.mHideNoShowButton = value
        End Set
    End Property

    '自动弹出此窗体为True，用户点击展示此窗体为False。
    Private mAutoShow As Boolean = True
    Public Property AutoShow() As Boolean
        Get
            Return mAutoShow
        End Get
        Set(ByVal value As Boolean)
            mAutoShow = value
        End Set
    End Property

#Region "--- 初始化/窗体事件 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language
        Me.mDevice = device
        Me.Icon = My.Resources.iTong
        'Me.mDock = blnDock
        'If Not blnDock Then
        '    Me.FilletRadius = 5
        'End If

        'Me.mblnUserGuide = blnUserGuide
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.lblInstallWebTui.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)

        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.BackColor = Color.White
        Me.tbShowTitleOnForm = True

        Me.mDownManage = MultiThreadDownload.Instance()

        Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)

        Me.mUpdateHelper = CoreUpdateHelper.Instance

        AddHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd
        AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
        AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
        AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall

        '安装deb的事件。
        AddHandler Me.mInstallHelper.InstallDebEventHandler, AddressOf mInstallHelper_OnInstallDeb
        AddHandler Me.mUpdateHelper.TaskInstalled, AddressOf mUpdateHelper_TaskInstalled

        If Me.mDevice IsNot Nothing AndAlso (Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia) Then
            '越狱版同步推
            Me.mJailbresk = True
            'Me.picTui.Image = My.Resources.webclip_step2
            Me.pnlContainer.BackgroundImage = My.Resources.webclip_step2
        Else

        End If

        Select Case Folder.LangType
            Case LanguageType.th_TH
                Me.btnInstall.Location = New Point(450, 478)
                Me.lblTuiProgress.Location = New Point(616, 490)
                Me.pbarShowProgress.Location = New Point(669, 519)
        End Select

        Me.chkNoShow.Visible = Not Me.mHideNoShowButton

        'If Me.mblnUserGuide Then
        '    Me.IniWebBrowserThread()
        'End If
        If Me.mDock Then
            Me.pnlContainer.Dock = DockStyle.Fill
        End If

        '收集浏览数据
        Dim sourceModel As ModelKey = ModelKey.None
        If Not Me.mAutoShow Then
            'sourceModel = ModelKey.ClickTui
        End If

        ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.Tui, ActionDataType.View)
        If Folder.LangType = LanguageType.zh_CN Then
            ' 下载背景图片
            Me.mthrDownBackPic = New Thread(AddressOf DownBackPic)
            Me.mthrDownBackPic.IsBackground = True
            Me.mthrDownBackPic.Start()
        End If

        Me.mAuthorizeByAppleID = AuthorizeByAppleID.Instance(Me.mDevice)
        AddHandler mAuthorizeByAppleID.CallBack, AddressOf OnTuiCallBack

        Me.pnlInstallTui.Dock = DockStyle.Fill
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

        Dim arg As New TuiEventArgs()
        arg.State = TuiState.CheckingInstall

        Me.OnTuiCallBack(Nothing, arg)
        Me.mAuthorizeByAppleID.CheckTuiState()
    End Sub

    Private Sub DownBackPic()
        Try
            Dim strPath As String = ServerIniSetting.GetTuiBackgroundPic(Me.mDevice)
            If strPath.Trim.Length <= 0 Then
                Return
            End If
            Dim strSavePath As String = Path.Combine(Folder.CacheFolder, Path.GetFileName(strPath))
            If Not File.Exists(strSavePath) Then
                Common.HttpDownload(strPath, strSavePath, 10000)
            End If

            If File.Exists(strSavePath) Then
                Me.SetBackgroundPic(strSavePath)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub SetBackgroundPicHandler(ByVal strImgPath As String)
    Private Sub SetBackgroundPic(ByVal strImgPath As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetBackgroundPicHandler(AddressOf SetBackgroundPic), strImgPath)
        Else
            Me.pnlContainer.BackgroundImage = Common.ImageFromFile(strImgPath)
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        'If Me.mblnUserGuide Then
        '    Me.Text = Me.Language.GetString("Welcome.Label.UserGuide")  '"新手引导"
        'Else
        '    Me.Text = Me.Language.GetString("File.Label.tbTui")         '"同步推"
        'End If
        Me.Text = Me.Language.GetString("File.Label.tbTui")         '"同步推"

        Me.chkNoShow.Text = Me.Language.GetString("App.Tip.Ignore")                 '"不再提醒"
        Me.btnInstall.Text = Me.Language.GetString("Download.Button.Install") '"立即安装"

        Me.btnInstall.Font = Common.CreateFont("微软雅黑", 18.0!, FontStyle.Bold)

        If Folder.LangType <> LanguageType.th_TH Then
            Me.btnInstall.Font = Common.CreateFont("微软雅黑", 16.0!, FontStyle.Regular)
            Me.btnInstall.tbTextColor = Color.FromArgb(255, 85, 35)
            Me.btnInstall.tbTextColorDisable = Color.FromArgb(255, 85, 35)
            Me.btnInstall.tbTextColorDown = Color.FromArgb(255, 85, 35)
            Me.btnInstall.tbTextColorHover = Color.FromArgb(255, 85, 35)
            Me.btnInstall.tbIconImage = Nothing
            Me.btnInstall.tbTextAlign = ContentAlignment.MiddleCenter
            Me.btnInstall.Padding = New Padding(30, 2, 30, 2)
            Me.btnInstall.Size = New Size(170, 47)
            Me.btnInstall.Location = New Point((Me.Width - Me.btnInstall.Width) \ 2, 470)
            Me.lblTuiProgress.Location = New Point((Me.Width - Me.lblTuiProgress.Width) \ 2, Me.btnInstall.Bottom + 3)
            Me.pbarShowProgress.Location = New Point((Me.Width - Me.pbarShowProgress.Width) \ 2, Me.lblTuiProgress.Bottom + 3)
            Me.btnInstall.tbSplit = "23, 21, 23, 24"
        End If
        Me.lblTuiProgress.Text = ""
        Me.pbarShowProgress.tbPlayValue = 0
        Me.btnBack.Text = Me.Language.GetString("Common.Button.Return")
        Me.btnFinish.Text = Me.Language.GetString("Media.Message.Finished")

        If ServerIniSetting.GetOpenH5 AndAlso AuthorizeHelper.GetInstance(Me.mDevice).CheckTuiMC() Then
            Me.btnInstall.Text = Me.Language.GetString("App.Button.Reinstall")
        End If

        Me.btnFeedback.Text = Me.Language.GetString("Main.Button.Feedback")
        Me.lblInstallWebTui.Text = Me.Language.GetString("WebTui.Label.InstallTitle") '正在为您安装同步推网页版
        Me.lblInstallTip.Text = Me.Language.GetString("WebTui.Message.InstallTip")    '为保证同步推网页版的顺利安装，需要你在手机端做如下操作（仅为获取设备端安装许可，请放心操作）
        Me.lblStepOne.Text = Me.Language.GetString("WebTui.Message.Step1")  '点击手机弹出的“安装描述文件”旁的【安装】
        Me.lblStepTwo.Text = Me.Language.GetString("WebTui.Message.Step2")  '点击确定【安装】
        Me.lblStepThree.Text = Me.Language.GetString("WebTui.Message.Step3") '安装后，即可在桌面查看已安装的同步推
        Me.btnOk.Text = Me.Language.GetString("Common.ISee")   '我知道了
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub

    Protected Overrides Sub OnResize(ByVal e As System.EventArgs)
        MyBase.OnResize(e)

        Me.ResizeControlButton()
    End Sub

    Private mInstallTui As Boolean = False
    Private Sub btnInstall_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnInstall.Click
        'Me.SetBtnCloseState(False)

        If Me.mStatus <> InstallStatus.Finished Then
            ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.TuiInstall, ActionDataType.Click)
        End If

        Me.InstallTui()
    End Sub

    Private Sub InstallTui()
        Try
            'If Me.mStatus = InstallStatus.Finished Then
            '    Me.Close()
            '    '如果用户没有点击同步推iOS9安装教程在安装完成后自动打开教程引导用户。
            '    '只有iOS9的设备才会有提示
            '    If Me.mDevice.VersionNumber >= 900 Then
            '        frmTuiInstallTutorial.ShowForm(Me.mApplication)
            '    End If
            '    Return
            'ElseIf Me.mStatus = InstallStatus.ReInstall Then
            '    'Me.iPhone.UninstallApplication(Common.GetTuiAppSKU(Me.mDevice.ProductType), "")
            '    Me.iPhone.UninstallApplication(Me.mDevice.GetTuiAppSKU(Me.mDevice.ProductType), "")
            'End If


            'Me.SetProgressVisable(True)

            'Dim blnJailbresk As Boolean = False
            'If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
            '    blnJailbresk = True
            'End If
            'If Me.mDevice.ProductType.ToLower().StartsWith("ipad") Then
            '    TuiInstallHelper.DownloadTbTuiHD(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, blnJailbresk)
            'Else
            '    TuiInstallHelper.DownloadTbTui(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, blnJailbresk)
            'End If

            'If ServerIniSetting.GetOpenH5 AndAlso Not blnJailbresk Then
            '    Me.mPicTui = Me.pnlContainer.BackgroundImage.Clone
            '    Me.pnlContainer.BackgroundImage = My.Resources.webclip_tuimc
            '    Me.btnInstall.Visible = False
            '    Me.btnBack.Visible = True
            '    Me.btnFinish.Visible = True
            '    Me.pbarShowProgress.Visible = False
            '    Me.lblTuiProgress.Visible = False
            'End If

            '收集下载数据
            'ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.Tui, ActionDataType.Download)

            If Me.mStatus = InstallStatus.Finished Then
                Me.Close()
                '如果用户没有点击同步推iOS9安装教程在安装完成后自动打开教程引导用户。
                '只有iOS9的设备才会有提示
                If Me.mDevice.VersionNumber >= 900 AndAlso Not IniSetting.GetNoShowTuiInstallTutorial() Then
                    frmTuiInstallTutorial.ShowForm(Me.mApplication)
                End If
            Else
                Me.SetProgressVisable(True)
                AuthorizeByAppleID.Instance(Me.mDevice).CheckTui()
                '收集下载数据
                ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.Tui, ActionDataType.Download)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnInstall_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnInstall.Resize
        Try
            If Folder.LangType = LanguageType.zh_CN Then
                'If Me.mDevice IsNot Nothing AndAlso (Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia) Then
                '    Me.lblTuiProgress.Location = New Point(Me.btnInstall.Right, Me.lblTuiProgress.Location.Y)
                '    Me.pbarShowProgress.Location = New Point(Me.lblTuiProgress.Left + (Me.lblTuiProgress.Width - Me.pbarShowProgress.Width) / 2, Me.pbarShowProgress.Location.Y)
                'Else
                '    Me.btnInstall.Location = New Point(Me.lblTuiProgress.Left - Me.btnInstall.Width, Me.btnInstall.Location.Y)
                'End If
                Me.btnInstall.Location = New Point((Me.Width - Me.btnInstall.Width) / 2, Me.btnInstall.Top)
            ElseIf Folder.LangType = LanguageType.vi_VN Then
                Me.btnInstall.Location = New Point(Me.lblTuiProgress.Left + Me.lblTuiProgress.Width / 2 - Me.btnInstall.Width / 2, Me.btnInstall.Location.Y)

            End If

        Catch
        End Try
    End Sub

    Private Sub OnTuiCallBack(ByVal sender As Object, ByVal e As TuiEventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of TuiEventArgs)(AddressOf OnTuiCallBack), sender, e)
            Else
                Select Case e.State
                    Case TuiState.CheckingInstall
                        Me.pbarShowProgress.tbIsWaiting = True
                        Me.lblTuiProgress.Text = Me.Language.GetString("Photo.Message.CheckingTuiIsInstall")
                        Me.SetProgressVisable(True)

                        Application.DoEvents()

                    Case TuiState.CheckingUpdate
                        Me.pbarShowProgress.tbIsWaiting = True
                        Me.lblTuiProgress.Text = Me.Language.GetString("Main.Message.CheckingUpgrade")
                        Me.SetProgressVisable(True)

                    Case TuiState.Logining
                        Me.pbarShowProgress.tbIsWaiting = True
                        Me.lblTuiProgress.Text = Me.Language.GetString("App.Lable.Logging")
                        Me.SetProgressVisable(True)

                    Case TuiState.Buying
                        Me.pbarShowProgress.tbIsWaiting = True
                        Me.lblTuiProgress.Text = Me.Language.GetString("App.Message.BuyingApp")
                        Me.SetProgressVisable(True)

                    Case TuiState.Combining
                        Me.pbarShowProgress.tbIsWaiting = True
                        Me.lblTuiProgress.Text = Me.Language.GetString("Download.Message.InOperation")
                        Me.SetProgressVisable(True)

                    Case TuiState.Installed
                        Me.btnInstall.tbText = Me.Language.GetString("MessageForm.Label.Reinstall")
                        Me.SetProgressVisable(False)

                    Case TuiState.Uninstalled
                        Me.btnInstall.tbText = Me.Language.GetString("App.Button.Install")
                        Me.SetProgressVisable(False)

                    Case TuiState.Installing
                        Me.pbarShowProgress.tbIsWaiting = True
                        Me.lblTuiProgress.Text = Me.Language.GetString("App.Cell.Installing")
                        Me.SetProgressVisable(True)

                    Case TuiState.InstallSucceed
                        'deb推安装成功反馈到界面状态
                        Me.mStatus = InstallStatus.Finished
                        Me.SetIntstallMessage(Me.Language.GetString("App.Cell.InstallSucceed"))
                        Me.SetProgressVisable(False)
                        Me.SetBtnInstallText(Me.Language.GetString("Media.Message.Finished")) '"完成"

                    Case TuiState.InstallH5
                        '底层安装H5页面的时候助手上层引导用户点确定。
                        Me.mStatus = InstallStatus.Finished
                        Me.SetIntstallMessage(Me.Language.GetString("App.Cell.InstallSucceed"))
                        Me.SetProgressVisable(False)
                        Me.SetBtnInstallText(Me.Language.GetString("Media.Message.Finished")) '"完成"
                        '提示完成后让用户点手机上的确定
                        Me.pnlInstallTui.Visible = True
                        Me.pnlInstallTui.BringToFront()
                        ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.TuiInstallH5, ActionDataType.Install, FunctionSucceed.Succeed, "")

                End Select
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnTuiCallBack")
        End Try
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            If mDownManage IsNot Nothing Then
                RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
                RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
                RemoveHandler Me.mUpdateHelper.TaskInstalled, AddressOf mUpdateHelper_TaskInstalled
            End If

            If Me.mAuthorizeByAppleID IsNot Nothing Then
                RemoveHandler Me.mAuthorizeByAppleID.CallBack, AddressOf OnTuiCallBack
            End If

            If Me.chkNoShow.Checked Then
                IniSetting.SetNoShowTuiJailberak(True)
                Me.SetTuiMarkInstall()
            End If
        Catch ex As Exception
        End Try

        Try
            If Me.mthrDownBackPic IsNot Nothing AndAlso Me.mthrDownBackPic.ThreadState <> ThreadState.Stopped Then
                Me.mthrDownBackPic.Abort()
            End If
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "---  同步推下载进度  ---"

    Public Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        '同步推如果授权失败后会走旧的逻辑下载，现在旧逻辑下载签名包服务器没给数据会一直卡在下载中的状态。
        '这种情况就直接提示下载失，提示用户重新安装。
        If item.ItemInfo.Type = ResourceType.pTui AndAlso String.IsNullOrEmpty(item.ItemInfo.SourceUrl) AndAlso String.IsNullOrEmpty(item.ItemInfo.Url) Then
            Me.mStatus = InstallStatus.Normal
            Me.pbarShowProgress.tbIsWaiting = False
            Me.SetIntstallMessage(Me.Language.GetString("PkgInstaller.Label.InstallFail"))
            Me.SetProgressVisable(True)
            Me.SetInstallButtonTest()
            Common.LogException("Url Empty", "Tui Download Failure")
        End If
    End Sub

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        If item.ItemInfo.Type <> ResourceType.pTui Then
            Return
        End If

        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of MultiThreadDownloadEventArgs)(AddressOf OnDownloading), sender, e)
        Else
            Me.SetProgressVisable(True)
            Me.lblTuiProgress.Text = Me.Language.GetString("App.Cell.Downloading") & " " & Me.Language.GetString("File.Label.tbTui")
            Me.pbarShowProgress.tbIsWaiting = False

            If Me.pbarShowProgress.tbPlayValue <> e.Progress Then
                Debug.Print(e.Progress.ToString() & "%")
            End If

            Me.pbarShowProgress.tbPlayValue = e.Progress
        End If
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        If item.ItemInfo.Type <> ResourceType.pTui Then
            Return
        End If

        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of MultiThreadDownloadCompletedEventArgs)(AddressOf OnDownloaded), sender, e)
        Else
            Me.pbarShowProgress.tbIsWaiting = False
            If e.Cancel = False Then
                If (e.ReceiveSize < e.ResourceSize AndAlso e.ResourceSize > 0) OrElse (e.ReceiveSize = 0 AndAlso e.ResourceSize = 0) Then
                    '下载失败
                    Dim strErrMsg As String = e.LastErrorMsg
                    If String.IsNullOrEmpty(strErrMsg) Then
                        strErrMsg = Me.Language.GetString("Tools.Payment.Message.DownloadFailure")
                    End If
                    '下载失败，安装H5
                    Me.SetIntstallMessage(strErrMsg)
                    Me.pbarShowProgress.tbPlayValue = 0
                    Me.SetBtnCloseState(True)
                    Me.SetInstallButtonTest()
                Else
                    '下载成功
                    Me.SetProgressVisable(True)
                    Me.lblTuiProgress.Text = Me.Language.GetString("App.Cell.Downloading") & " " & Me.Language.GetString("File.Label.tbTui")
                    Me.pbarShowProgress.tbPlayValue = 100
                End If
            Else
                '取消下载
                Me.btnInstall.tbText = Me.Language.GetString("App.Button.Install")
                Me.SetProgressVisable(False)
            End If

        End If
    End Sub

    Private Delegate Sub SetProgressVisableHandler(ByVal blnEnable As Boolean)
    Private Sub SetProgressVisable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetProgressVisableHandler(AddressOf SetProgressVisable), blnEnable)
        Else
            If Me.lblTuiProgress.Visible <> blnEnable OrElse Not Me.pbarShowProgress.Visible <> blnEnable Then
                'Me.lblTuiProgress.ForeColor = Color.DarkGray
                Me.lblTuiProgress.Visible = blnEnable
                Me.pbarShowProgress.Visible = blnEnable
                Me.btnInstall.Enabled = Not blnEnable
            End If
        End If
    End Sub

#End Region

#Region "---  同步推安装进度  ---"

    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        Try
            '兼容同步推新id逻辑
            If args.PackageInfo Is Nothing OrElse Not args.PackageInfo.IsTui Then
                Return
            End If

            Debug.Print(args.InstallState.ToString())

            Select Case args.InstallState
                Case InstallState.Nomal

                Case InstallState.Waiting
                    Me.SetIntstallMessage(Me.Language.GetString("App.Cell.WaitingInstall"))
                    Me.pbarShowProgress.tbIsWaiting = True

                Case InstallState.AuthBegin
                    Me.SetIntstallMessage(Me.Language.GetString("App.Message.Calculating"))
                    Me.pbarShowProgress.tbIsWaiting = True

                Case InstallState.Transfering
                    Me.SetIntstallMessage(Me.Language.GetString("File.Label.Uploading"))
                    Me.pbarShowProgress.tbIsWaiting = False
                    Me.pbarShowProgress.tbPlayValue = args.TransferProgress

                Case InstallState.Installing
                    Me.SetIntstallMessage(Me.Language.GetString("App.Cell.Installing"))
                    Me.pbarShowProgress.tbIsWaiting = False
                    Me.pbarShowProgress.tbPlayValue = args.TransferProgress

                Case InstallState.HaveNotInstall
                    Me.SetIntstallMessage(args.ErrorMsg)
                    Me.pbarShowProgress.tbIsWaiting = False
                    Me.pbarShowProgress.tbPlayValue = 0
                    Me.SetBtnCloseState(True)
                    Me.SetInstallButtonTest()

                Case InstallState.UnSucceed
                    Me.SetIntstallMessage(args.ErrorMsg)
                    Me.pbarShowProgress.tbIsWaiting = False
                    Me.pbarShowProgress.tbPlayValue = 0
                    Me.SetBtnCloseState(True)
                    Me.SetInstallButtonTest()
                Case InstallState.AlreadyInstall
                    Me.SetIntstallMessage(Me.Language.GetString("App.Cell.AlreadyInstall"))
                    Me.pbarShowProgress.tbIsWaiting = False
                    Me.pbarShowProgress.tbPlayValue = 100
                    Me.SetBtnCloseState(True)

                    Me.SetInstallButtonTest()
                    Me.mStatus = InstallStatus.ReInstall

                Case InstallState.Succeed
                    Me.SetIntstallMessage(Me.Language.GetString("App.Cell.InstallSucceed"))
                    Me.SetProgressVisable(False)
                    Me.mStatus = InstallStatus.Finished
                    Me.btnInstall.Enabled = True
                    Me.SetBtnInstallText(Me.Language.GetString("Media.Message.Finished")) '"完成"
                    Me.SetBtnCloseState(True)

                Case InstallState.AllInstallCompleted

                Case InstallState.BackupRecord
                    Me.SetIntstallMessage(Me.Language.GetString("App.Cell.BackupRecord"))   '"正在备份记录"
                    Me.pbarShowProgress.tbIsWaiting = False
                    Me.pbarShowProgress.tbPlayValue = args.TransferProgress

                Case InstallState.CancelInstall
                    Me.SetProgressVisable(False)
                    Me.SetInstallButtonTest()
                    Me.SetBtnInstallText(Me.Language.GetString("Download.Button.Install"))

            End Select
        Catch ex As Exception
        End Try

    End Sub

    Private Sub mInstallHelper_OnInstallDeb(ByVal sender As Object, ByVal args As InstallArgs)
        Try
            Select Case args.InstallState
                Case InstallState.Succeed
                    Me.SetToolTip(args.PackageInfo.Name, Me.Language.GetString("App.Cell.InstallSucceed"))
                Case InstallState.UnSucceed
                    Me.SetToolTip(args.PackageInfo.Name, Me.Language.GetString("PkgInstaller.Label.InstallFail"))
            End Select
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub SetToolTipHandler(ByVal strTaskName As String, ByVal strInfo As String)
    Private Sub SetToolTip(ByVal strTaskName As String, ByVal strInfo As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetToolTipHandler(AddressOf SetToolTip), strTaskName, strInfo)
        Else
            If Form.ActiveForm IsNot Nothing AndAlso TypeOf Form.ActiveForm Is frmTip Then
                Form.ActiveForm.Hide()
            End If

            If Form.ActiveForm Is Me Then
                frmTip.ShowTip(strTaskName, strInfo, Me)

            ElseIf Form.ActiveForm IsNot Nothing AndAlso TypeOf Form.ActiveForm Is tbImageViewForm Then
                Return

            ElseIf ProcForm.Instance().CheckMainFormIsOnlyOne() Then
                frmTip.ShowTip(strTaskName, strInfo, Nothing)
            End If
        End If
    End Sub

    Private Delegate Sub SetIntstallMessageHandler(ByVal msg As String)
    Private Sub SetIntstallMessage(ByVal msg As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetIntstallMessageHandler(AddressOf SetIntstallMessage), msg)
        Else
            Me.SetProgressVisable(True)
            Me.lblTuiProgress.Text = msg
            If msg.Contains("-402636802") Then
                '"请关闭设备上的访问限制。"
                tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.CloseAccessRestrictions"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, MessageBoxDefaultButton.Button1, My.Resources.webclip_note)
            End If
        End If
    End Sub

    Private Delegate Sub SetBtnInstallTextHandler(ByVal strText As String)
    Private Sub SetBtnInstallText(ByVal strText As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetBtnInstallTextHandler(AddressOf SetBtnInstallText), strText)
        Else
            Me.btnInstall.Text = strText
        End If
    End Sub

    Private Sub mUpdateHelper_TaskInstalled(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If item.ItemInfo.Type <> ResourceType.pTui Then
            Return
        End If

        Me.SetProgressVisable(True)
        Me.SetIntstallMessage(Me.Language.GetString("App.Cell.AlreadyInstall"))
        Me.pbarShowProgress.tbPlayValue = 100
        Me.SetInstallButtonTest()
        Me.mStatus = InstallStatus.ReInstall
        Me.SetBtnCloseState(True)
    End Sub

    Private Delegate Sub SetInstallButtonTestHandler()
    Private Sub SetInstallButtonTest()
        If Me.InvokeRequired Then
            Me.Invoke(New SetInstallButtonTestHandler(AddressOf SetInstallButtonTest))
        Else
            Me.btnInstall.Enabled = True
            Me.btnInstall.Text = Me.Language.GetString("App.Button.Reinstall")      '"重新安装"
        End If
    End Sub

#End Region

#Region "---  网页逻辑  ---"

    'Private Sub IniWebBrowserThread()
    '    'http://**************:8080/tang-userapp/guide/
    '    Dim strUrl As String = "http://v2.tongbu.com/app/guide/?bo={0}&producttype={1}&productversion={2}"
    '    'If Me.mDevice.CheckTuiAppExist() Then
    '    '    strUrl = "http://v2.tongbu.com/app/guide/down?bo={0}&producttype={1}&productversion={2}"
    '    'End If

    '    strUrl = String.Format(strUrl, IIf(Me.mDevice.Jailbreaked, "1", "0"), Me.mDevice.ProductType.ToLower(), Me.mDevice.ProductVersion)

    '    Me.mBrowser = Me.CreateWebSite(strUrl, False, Me.pnlContainer)
    '    Me.pnlContainer.BringToFront()
    '    Me.mBrowser.BringToFront()
    '    'Me.picTui.Visible = False

    '    Dim thr As New Threading.Thread(AddressOf IniWebBrowser)
    '    With thr
    '        .IsBackground = True
    '        .Start()
    '    End With
    'End Sub

    'Private Sub IniWebBrowser()
    '    Try
    '        Me.mBrowser.Navigate(Me.mBrowser.Name)
    '    Catch ex As Exception
    '    End Try
    'End Sub

    'Private Function CreateWebSite(ByVal strName As String, Optional ByVal showScrollBars As Boolean = True, Optional ByVal pnl As Control = Nothing) As tbWebBrowserEx
    '    Dim wb As New tbWebBrowserEx

    '    AddHandler wb.Navigating, AddressOf WebBrowser_Navigating
    '    AddHandler wb.NavigateError, AddressOf webDownloadPage_NavigateError
    '    AddHandler wb.NewWindow3, AddressOf webDownloadPage_NewWindow3

    '    If pnl IsNot Nothing Then
    '        pnl.Controls.Add(wb)
    '    End If

    '    wb.IsWebBrowserContextMenuEnabled = False
    '    wb.ScriptErrorsSuppressed = True
    '    wb.ScrollBarsEnabled = showScrollBars
    '    wb.ShowNavigateErrorPage = False
    '    wb.ShowLoadingWait = True

    '    wb.Name = strName
    '    wb.Size = New Size(pnl.Width + 4, pnl.Height + 4)
    '    wb.Location = New Point(-2, -2)
    '    wb.Dock = DockStyle.None
    '    wb.Anchor = AnchorStyles.Left Or AnchorStyles.Top Or AnchorStyles.Right Or AnchorStyles.Bottom

    '    '设置接入的设备是否越狱，并设置网页越狱属性.
    '    Dim isJailBreak As Boolean = False
    '    If Me.iPhone Is Nothing AndAlso IniSetting.GetJailbreaked Then
    '        isJailBreak = True

    '    ElseIf Me.iPhone IsNot Nothing AndAlso (Me.iPhone.Jailbreaked OrElse Me.iPhone.InstallCydia) Then
    '        isJailBreak = True

    '    End If
    '    wb.Jailbreaked = isJailBreak

    '    If Not Folder.LangType = LanguageType.en_US Then
    '        wb.LoadingGif = GuiResource.gif_loading_24
    '    End If

    '    Return wb
    'End Function

    'Private Sub WebBrowser_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs)
    '    Try
    '        Dim url As String = e.Url.ToString
    '        Debug.Print("weburl:" & url)

    '        If url.EndsWith(".ipa") Then
    '            Me.DownloadIPA(url)
    '            e.Cancel = True

    '        ElseIf String.IsNullOrEmpty(e.Url.Query) = False AndAlso e.Url.Query.IndexOf("actiontype=") >= 0 Then
    '            Me.DownloadApp(e.Url.OriginalString)
    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://UserGuid/Finished", StringComparison.OrdinalIgnoreCase) Then
    '            Me.Close()
    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://app/download", StringComparison.OrdinalIgnoreCase) Then
    '            'tongbu://app/download/itms?
    '            Dim strUrl As String = url.ToLower
    '            Dim info As New PackageInfo()
    '            info.ItemId = Utility.GetParamValueFromQuery("appleid", strUrl)
    '            info.Name = Utility.GetParamValueFromQuery("name", strUrl)
    '            info.Version = Utility.GetParamValueFromQuery("version", strUrl)

    '            CoreUpdateHelper.Instance().DownloadAppleSoft(info, False)

    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://app/guideapp", StringComparison.OrdinalIgnoreCase) Then
    '            'url = "tongbu://app/topicdownload?appleids=563443400,631177,469341118,524359189,522704697,512456155,488156323,506234146,431213733,431688122,504575083,512772066,480189064,479952446"
    '            Me.DownloadTopicApp(url)
    '            Dim strUrl As String = String.Format("http://v2.tongbu.com/app/guide/down?bo={0}&producttype={1}&productversion={2}", IIf(Me.mDevice.Jailbreaked, "1", "0"), Me.mDevice.ProductType.ToLower(), Me.mDevice.ProductVersion)
    '            Me.mBrowser.Navigate(strUrl)
    '            e.Cancel = True
    '        ElseIf url.StartsWith("tongbu://app/guidegame", StringComparison.OrdinalIgnoreCase) Then
    '            'url = "tongbu://app/topicdownload?appleids=563443400,631177,469341118,524359189,522704697,512456155,488156323,506234146,431213733,431688122,504575083,512772066,480189064,479952446"
    '            Me.DownloadTopicApp(url)
    '            Dim strTuiInstall As String = "1"
    '            If Me.mDevice.CheckTuiAppExist() Then
    '                strTuiInstall = "0"
    '            End If

    '            Dim strUrl As String = String.Format("http://v2.tongbu.com/app/guide/tui?bo={0}&producttype={1}&productversion={2}&tuiinstall={3}", IIf(Me.mDevice.Jailbreaked, "1", "0"), Me.mDevice.ProductType.ToLower(), Me.mDevice.ProductVersion, strTuiInstall)
    '            Me.mBrowser.Navigate(strUrl)
    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://userguid/Tui", StringComparison.OrdinalIgnoreCase) Then
    '            Dim istui As String = Utility.GetParamValueFromQuery("istui", url)
    '            If istui = "on" Then
    '                Me.InstallTui()
    '            End If

    '            Dim strUrl As String = String.Format("http://v2.tongbu.com/app/guide/over?bo={0}&producttype={1}&productversion={2}", IIf(Me.mDevice.Jailbreaked, "1", "0"), Me.mDevice.ProductType.ToLower(), Me.mDevice.ProductVersion)
    '            Me.mBrowser.Navigate(strUrl)
    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://wallpaper/download?", StringComparison.OrdinalIgnoreCase) Then
    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://ring/download?", StringComparison.OrdinalIgnoreCase) Then
    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://music/download/", StringComparison.OrdinalIgnoreCase) Then
    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://music/search?name=", StringComparison.OrdinalIgnoreCase) Then
    '            Dim strName As String = Utility.GetParamValueFromQuery("name", url)
    '            e.Cancel = True

    '        ElseIf url.ToLower().IndexOf("itms:") >= 0 Then
    '            '在 iTunes 打开页面(购买正版)，验证iTunes是不是安装，如果没有安装提示用户
    '            If Common.CheckiTunesInstall() Then
    '                Common.OpenExplorer(url)
    '            Else
    '                Try
    '                    CType(sender, tbWebBrowser).Document.InvokeScript("tip", New Object() {"noitunes"})
    '                Catch ex As Exception
    '                    Debug.Print(ex.ToString)
    '                End Try
    '            End If
    '            e.Cancel = True

    '        ElseIf url.StartsWith("tongbu://weibo/sina/send?msg=", StringComparison.OrdinalIgnoreCase) Then
    '            e.Cancel = True

    '        ElseIf url.StartsWith("ie://", StringComparison.OrdinalIgnoreCase) Then
    '            '在IE中直接显示 "ie://url" pc端会替换ie://为http://
    '            Common.OpenExplorer(url.Replace("ie://", "http://"))
    '            e.Cancel = True

    '        ElseIf e.Url.ToString().ToLower().Contains("itong://refresh") Then
    '            e.Cancel = True
    '            Dim wb As tbWebBrowser = CType(sender, tbWebBrowser)
    '            wb.Navigate(wb.Name)

    '        ElseIf e.Url.ToString().Contains("tongbu://jp/?") Then
    '            '跳转到软件下载页面.
    '            e.Cancel = True
    '            Dim jumpUrl As String = Utility.GetParamValueFromQuery("url", e.Url.ToString(), "&", False, True)
    '            jumpUrl = System.Web.HttpUtility.UrlDecode(jumpUrl)
    '            Dim nodeName As String = "app"
    '            If jumpUrl.ToLower.Contains("wallpaper") Then
    '                nodeName = "wallpaper"
    '            ElseIf jumpUrl.ToLower.Contains("ring") Then
    '                nodeName = "ringtone"
    '            ElseIf Me.mDevice.Jailbreaked Then
    '                nodeName = "app"
    '            Else
    '                nodeName = "appshare"
    '            End If

    '            Me.mApplication.GotoSite(nodeName, jumpUrl)
    '        ElseIf e.Url.ToString().Contains("istui=on") Then
    '            Me.TuiInstall()
    '        End If

    '    Catch ex As Exception
    '        Common.LogException(ex.ToString)
    '    End Try
    'End Sub

    'Private Sub TuiInstall()
    '    'Dim blnJailbresk As Boolean = False
    '    'If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
    '    '    blnJailbresk = True
    '    'End If
    '    'If Me.mDevice.ProductType.ToLower().StartsWith("ipad") Then
    '    '    TuiInstallHelper.DownloadTbTuiHD(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, blnJailbresk)
    '    'Else
    '    '    TuiInstallHelper.DownloadTbTui(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, blnJailbresk)
    '    'End If

    '    AuthorizeByAppleID.Instance(Me.mDevice).CheckTui()
    'End Sub

    'Private Sub webDownloadPage_NavigateError(ByVal sender As Object, ByVal e As WebBrowserNavigateErrorEventArgs)
    '    Dim wb As tbWebBrowser = CType(sender, tbWebBrowser)
    '    Dim strImageFile As String = Folder.CacheFolder & "welcome_gif_error.gif"
    '    If Not System.IO.File.Exists(strImageFile) Then
    '        My.Resources.welcome_gif_error.Save(strImageFile)
    '    End If

    '    If Not String.IsNullOrEmpty(e.Url) AndAlso _
    '       (wb.Url Is Nothing OrElse _
    '        wb.Url.ToString().StartsWith("about:blank", StringComparison.OrdinalIgnoreCase) OrElse _
    '        e.Url.ToString.Contains(New Uri(wb.Name).Host)) Then

    '        wb.Url = New Uri(e.Url)

    '        If String.IsNullOrEmpty(mErrorDocumentText) Then
    '            mErrorDocumentText = My.Resources.welcome_htm_error.Replace("error_icon.gif", strImageFile).Replace("#E9EDF3", "#F2F2F7")

    '            mErrorDocumentText = mErrorDocumentText.Replace("{0}", Me.Language.GetString("Welcome.Label.ConnFailed"))     '"Connection failed."
    '            '"Please try one of the following:{0}1. Check if your network is {0}working properly.{0}2.{1}Refresh{2} this page."
    '            Dim strPara0 As String = String.Format("<br/><div class=""{0}"">", Me.Language.CurrentLanguage.LangName)
    '            Dim strPara1 As String = String.Format("</div><div class=""{0}"">", Me.Language.CurrentLanguage.LangName)
    '            Dim strPara2 As String = "<a href=""iTong://Refresh"">"
    '            Dim strPara3 As String = "</a>"

    '            mErrorDocumentText = mErrorDocumentText.Replace("{1}", String.Format(Me.Language.GetString("Welcome.Message.HowToRefreshPage"), strPara0, strPara1, strPara2, strPara3))
    '        End If

    '        wb.DocumentText = mErrorDocumentText
    '    End If
    'End Sub

    'Private Sub webDownloadPage_NewWindow3(ByVal sender As tbWebBrowserEx, ByRef ppDisp As Object, ByRef Cancel As Boolean, ByVal dwFlags As UInteger, ByVal bstrUrlContext As String, ByVal bstrUrl As String)
    '    If Not String.IsNullOrEmpty(bstrUrl) Then
    '        Common.OpenExplorer(bstrUrl)
    '        Cancel = True
    '    End If
    'End Sub

    Private Sub DownloadIPA(ByVal url As String, _
                                                Optional ByVal strName As String = "", _
                                                Optional ByVal strVersion As String = "", _
                                                Optional ByVal strItemId As String = "", _
                                                Optional ByVal strIconUrl As String = "", _
                                                Optional ByVal strLoadID As String = "", _
                                                Optional ByVal isNeedToBackInfo As Boolean = False)
        Try
            Dim info As New MultiThreadDownloadItemInfo
            With info
                .IsSingleThread = False

                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.IPA
                .Class = ResourceClass.Software
                .Source = TaskSource.Tongbu
                .LoadId = strLoadID
                .Url = url
                .ItemId = strItemId
                .IconUrl = strIconUrl

                If isNeedToBackInfo Then
                    .UrlId = strLoadID
                End If

                If strName.Length > 0 Then
                    If strVersion.Length > 0 Then
                        strName &= String.Format("_v{0}", strVersion)
                    End If
                    If strName.ToLower().EndsWith(".ipa") = False Then
                        strName &= ".ipa"
                    End If
                End If
                If strName.Length = 0 Then
                    strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
                End If
                info.Name = System.Web.HttpUtility.UrlDecode(strName)

                If .Url.ToLower().Contains("tbtui") Then
                    .IsUpdate = True
                End If
                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
                    .Identifier = Me.mDevice.Identifier
                End If
            End With

            Me.mDownManage.NewTask(info)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DownloadIPA")
        End Try
    End Sub

#End Region

#Region "--- 专题应用下载 ---"

    'Private Sub DownloadTopicApp(ByVal url As String)
    '    Try
    '        If Me.mThreadTopicDownload IsNot Nothing AndAlso Me.mThreadTopicDownload.ThreadState <> ThreadState.Stopped Then
    '            Me.mThreadTopicDownload.Abort()
    '        End If
    '    Catch
    '    End Try

    '    Me.mThreadTopicDownload = New Thread(New ParameterizedThreadStart(AddressOf DownloadTopicAppInThread))
    '    With Me.mThreadTopicDownload
    '        .IsBackground = True
    '        .Start(url)
    '    End With
    'End Sub

    'Private Sub DownloadTopicAppInThread(ByVal objUrl As Object)
    '    Try
    '        Dim strUrl As String = CType(objUrl, String).Trim().ToLower()
    '        Dim strAppleIds As String = Utility.GetParamValueFromQuery("appleids", strUrl)

    '        If String.IsNullOrEmpty(strAppleIds) Then
    '            Return
    '        End If

    '        Dim arrId As String() = strAppleIds.Split(",")
    '        Dim strContent As String = String.Empty
    '        Dim strUrlForId As String = WebUrl.PageIpaDownInfo & String.Format("&bo={0}", IIf(Me.mDevice.Jailbreaked, "1", "0"))

    '        For Each Item As String In arrId
    '            If String.IsNullOrEmpty(Item) Then
    '                Exit For
    '            End If

    '            strContent = Utility.GetContentStringFromUrl(String.Format(strUrlForId, Item) & "&1", System.Text.Encoding.UTF8)

    '            If Not String.IsNullOrEmpty(Item) Then
    '                Me.DownloadApp(strContent)
    '            End If

    '        Next

    '    Catch
    '    End Try
    'End Sub

#End Region

#Region "---  私有方法  ---"

    '设置关闭按钮的状态
    Private Sub SetBtnCloseState(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetProgressVisableHandler(AddressOf SetBtnCloseState), blnEnable)
        Else
            Me.btn_close.Enabled = blnEnable
        End If
    End Sub

    Private Sub SetTuiMarkInstall()
        Try
            Dim strPath As String = ""
            For Each Item As String In Me.mDevice.GetDirectories("/var/mobile/Media/iTunes_Control/iTunes/")
                If Item.StartsWith("25312Mar") Then

                    strPath = Item
                    Exit For
                End If
            Next
            If strPath.Length > 0 Then
                Me.mDevice.DeleteDirectory("/var/mobile/Media/iTunes_Control/iTunes/" & strPath)
            End If

            Dim strValue As String = "/var/mobile/Media/iTunes_Control/iTunes/25312Mar" & DateTime.Now.ToString("yyyyMMdd")
            Me.mDevice.CreateDirectory(strValue)

        Catch ex As Exception
        End Try
    End Sub

    Private Sub ResizeControlButton()
        If Me.Owner IsNot Nothing AndAlso Me.Owner.WindowState <> FormWindowState.Maximized Then
            Me.StartPosition = FormStartPosition.Manual
            Me.Location = New Point(Me.Owner.Left + (Me.Owner.Width - Me.Width) \ 2, Me.Owner.Top + 30)
            Me.btn_minimize.Visible = False
            Me.btn_normal.Visible = False
            If Me.btnFeedback IsNot Nothing Then
                Me.btnFeedback.Visible = False
            End If
        Else
            Me.btn_normal.Visible = False
            Me.btn_minimize.Location = New Point(Me.btn_close.Left - Me.btn_minimize.Width - 1, Me.btn_minimize.Top)
            If Me.btnFeedback IsNot Nothing Then
                Me.btnFeedback.Location = New Point(Me.btn_minimize.Left - Me.btnFeedback.Width - 3, Me.btnFeedback.Top)
            End If
        End If
    End Sub

    Protected Overrides Sub OnActivated(ByVal e As System.EventArgs)
        MyBase.OnActivated(e)
        Me.ResizeControlButton()
    End Sub

#End Region

    Private Sub btnFinish_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFinish.Click
        Me.Close()
    End Sub

    Private Sub btnBack_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBack.Click
        Me.btnBack.Visible = False
        Me.btnFinish.Visible = False
        Me.btnInstall.Visible = True
        Me.btnInstall.Enabled = True
        If Me.mPicTui IsNot Nothing Then
            Me.pnlContainer.BackgroundImage = Me.mPicTui
        End If
    End Sub

    Private Sub btnFeedback_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnFeedback.Click
        Me.mAuthorizeByAppleID.PostKeError()
        tbSplashBox.ShowMessage(Me.Language.GetString("App.Message.Sended"), Me, 3)     '"已提交"
    End Sub

    Private Sub btnOk_Click(sender As Object, e As EventArgs) Handles btnOk.Click
        Me.Close()
    End Sub

End Class