﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDetail
    Inherits tbBaseGuiForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmDetail))
        Me.btn_close = New iTong.Components.tbButton()
        Me.pnlMain = New System.Windows.Forms.Panel()
        Me.btnCopy = New iTong.Components.tbButton()
        Me.cmsCopy = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiCopyValue = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiCopyRow = New System.Windows.Forms.ToolStripMenuItem()
        Me.lblState = New System.Windows.Forms.Label()
        Me.cmsCopy.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(594, 1)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 5
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'pnlMain
        '
        Me.pnlMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlMain.BackColor = System.Drawing.Color.White
        Me.pnlMain.Location = New System.Drawing.Point(10, 31)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(600, 536)
        Me.pnlMain.TabIndex = 6
        '
        'btnCopy
        '
        Me.btnCopy.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCopy.BackColor = System.Drawing.Color.Transparent
        Me.btnCopy.BindingForm = Nothing
        Me.btnCopy.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCopy.Location = New System.Drawing.Point(496, 582)
        Me.btnCopy.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnCopy.Name = "btnCopy"
        Me.btnCopy.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCopy.Selectable = True
        Me.btnCopy.Size = New System.Drawing.Size(95, 25)
        Me.btnCopy.TabIndex = 7
        Me.btnCopy.tbAdriftIconWhenHover = False
        Me.btnCopy.tbAutoSize = False
        Me.btnCopy.tbAutoSizeEx = False
        Me.btnCopy.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnCopy.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCopy.tbBadgeNumber = 0
        Me.btnCopy.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopy.tbEndEllipsis = False
        Me.btnCopy.tbIconHoldPlace = True
        Me.btnCopy.tbIconImage = Nothing
        Me.btnCopy.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopy.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCopy.tbIconMore = False
        Me.btnCopy.tbIconMouseDown = Nothing
        Me.btnCopy.tbIconMouseHover = Nothing
        Me.btnCopy.tbIconMouseLeave = Nothing
        Me.btnCopy.tbIconPlaceText = 2
        Me.btnCopy.tbIconReadOnly = Nothing
        Me.btnCopy.tbImageMouseDown = Nothing
        Me.btnCopy.tbImageMouseHover = Nothing
        Me.btnCopy.tbImageMouseLeave = Nothing
        Me.btnCopy.tbProgressValue = 50
        Me.btnCopy.tbReadOnly = False
        Me.btnCopy.tbReadOnlyText = False
        Me.btnCopy.tbShadow = False
        Me.btnCopy.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCopy.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCopy.tbShowDot = False
        Me.btnCopy.tbShowMoreIconImg = CType(resources.GetObject("btnCopy.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCopy.tbShowNew = False
        Me.btnCopy.tbShowProgress = False
        Me.btnCopy.tbShowTip = True
        Me.btnCopy.tbShowToolTipOnButton = False
        Me.btnCopy.tbSplit = "13,11,13,11"
        Me.btnCopy.tbText = "Copy"
        Me.btnCopy.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCopy.tbTextColor = System.Drawing.Color.Black
        Me.btnCopy.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnCopy.tbTextColorDown = System.Drawing.Color.Black
        Me.btnCopy.tbTextColorHover = System.Drawing.Color.Black
        Me.btnCopy.tbTextMouseDownPlace = 0
        Me.btnCopy.tbToolTip = ""
        Me.btnCopy.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCopy.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopy.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnCopy.VisibleEx = True
        '
        'cmsCopy
        '
        Me.cmsCopy.DropShadowEnabled = False
        Me.cmsCopy.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiCopyValue, Me.tsmiCopyRow})
        Me.cmsCopy.Name = "cmsCopy"
        Me.cmsCopy.Size = New System.Drawing.Size(143, 48)
        Me.cmsCopy.tbBackColor = System.Drawing.Color.White
        Me.cmsCopy.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsCopy.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsCopy.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiCopyValue
        '
        Me.tsmiCopyValue.Name = "tsmiCopyValue"
        Me.tsmiCopyValue.Size = New System.Drawing.Size(142, 22)
        Me.tsmiCopyValue.Text = "Copy Value"
        '
        'tsmiCopyRow
        '
        Me.tsmiCopyRow.Name = "tsmiCopyRow"
        Me.tsmiCopyRow.Size = New System.Drawing.Size(142, 22)
        Me.tsmiCopyRow.Text = "Copy Row"
        '
        'lblState
        '
        Me.lblState.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(131, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(176, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(14, 584)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(344, 21)
        Me.lblState.TabIndex = 8
        Me.lblState.Text = "已成功复制到剪贴板！"
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmDetail
        '
        Me.ClientSize = New System.Drawing.Size(620, 620)
        Me.Controls.Add(Me.lblState)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.btnCopy)
        Me.MinimumSize = New System.Drawing.Size(12, 12)
        Me.Name = "frmDetail"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.pnl_bg_info
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "6,6,6,6"
        Me.tbTitleBackColor = System.Drawing.Color.White
        Me.Text = "Detail Information"
        Me.cmsCopy.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As tbButton
    Friend WithEvents pnlMain As System.Windows.Forms.Panel
    Friend WithEvents btnCopy As tbButton
    Friend WithEvents cmsCopy As tbContextMenuStrip
    Friend WithEvents tsmiCopyRow As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiCopyValue As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lblState As System.Windows.Forms.Label

End Class
