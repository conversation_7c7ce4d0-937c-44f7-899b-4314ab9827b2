﻿'移到CoreModuleCS
Imports System.Threading
Imports System.Globalization

Public Class SaleInfoHelper

    Private mTdLoadPhoneSaleInfo As Thread

    Private mDevice As iPhoneDevice
    Private mLanguage As LanguageInterface = Nothing
    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Protected Shared mSaleInfoHelper As SaleInfoHelper
    Private Shared mDictInstances As New Dictionary(Of String, SaleInfoHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Private mPluginLogin As PluginLogin

    Private mIsReady As Boolean = False
    Private mSalesInfo As SalesInfoPara
    Private mSalesAreaValue As String
    Private mIsNewDevice As Boolean = False
    Private mIsFromAppleWeb As Boolean
    Private mIsWebbrowser As Boolean = False
    Private mDeviceProduce As String = ""
    Private mDateDeviceProduce As DateTime = DateTime.MinValue

    Private Shared mDictYears As Dictionary(Of String, String) = New Dictionary(Of String, String)
    Private Shared mDictWeeks As Dictionary(Of String, Integer) = New Dictionary(Of String, Integer)

    Public Shared Event LoadPhoneSaleInfoEventHandler As EventHandler(Of SaleInfoEventArgs)

#Region "--- 对外属性 ---"

    Public ReadOnly Property IsReady() As Boolean
        Get
            Return mIsReady
        End Get
    End Property

    Public ReadOnly Property SalesInfo() As SalesInfoPara
        Get
            Return mSalesInfo
        End Get
    End Property

    Public ReadOnly Property SalesAreaValue() As String
        Get
            Return mSalesAreaValue
        End Get
    End Property

    Public ReadOnly Property IsNewDevice()
        Get
            Return mIsNewDevice
        End Get
    End Property

    Public ReadOnly Property IsFromAppleWeb() As Boolean
        Get
            Return mIsFromAppleWeb
        End Get
    End Property

    Public Property IsWebbrowser() As Boolean
        Get
            Return mIsWebbrowser
        End Get
        Set(ByVal value As Boolean)
            mIsWebbrowser = value
        End Set
    End Property

    Public ReadOnly Property DeviceProduce() As String
        Get
            Return mDeviceProduce
        End Get
    End Property

    Public ReadOnly Property DateDeviceProduce() As DateTime
        Get
            If mDateDeviceProduce = Date.MinValue Then
                mDateDeviceProduce = GetProduceDate(mDevice.SerialNumber)
            End If
            Return mDateDeviceProduce
        End Get
    End Property

#End Region

#Region "--- 单例 ---"

    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New(ByVal iPhoneDeviceInstance As iPhoneDevice)
        mDevice = iPhoneDeviceInstance
        Me.mPluginLogin = PluginLogin.Instance()
        RemoveHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
        AddHandler mPluginLogin.LoginEvent, AddressOf PluginLogin_LoginEvent
    End Sub

    Public Shared Function GetInstance(ByVal device As iPhoneDevice) As SaleInfoHelper
        If mDictInstances.ContainsKey(device.Identifier) Then
            If mDictInstances(device.Identifier).mDevice.IsConnected = False Then
                mDictInstances.Remove(device.Identifier)
                mDictInstances.Add(device.Identifier, New SaleInfoHelper(device))
            End If
        Else
            SyncLock _lockGetInstance
                If Not mDictInstances.ContainsKey(device.Identifier) Then
                    mDictInstances.Add(device.Identifier, New SaleInfoHelper(device))
                End If
            End SyncLock
        End If

        Return mDictInstances.Item(device.Identifier)
    End Function

#End Region

#Region "--- 初始化 ---"

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
            End If

            Try
                If mTdLoadPhoneSaleInfo IsNot Nothing AndAlso mTdLoadPhoneSaleInfo.ThreadState <> ThreadState.Stopped Then
                    mTdLoadPhoneSaleInfo.Abort()
                End If
            Catch
            End Try
        End If
        disposedValue = True
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Public Sub Start2LoadPhoneSaleInfo(ByVal strRegionInfo As String)
        If mTdLoadPhoneSaleInfo IsNot Nothing AndAlso mTdLoadPhoneSaleInfo.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Dim objpara As Object() = New Object() {strRegionInfo}
        Me.mTdLoadPhoneSaleInfo = New Thread(AddressOf LoadPhoneSaleInfo)
        Me.mTdLoadPhoneSaleInfo.SetApartmentState(ApartmentState.STA)
        Me.mTdLoadPhoneSaleInfo.IsBackground = True
        Me.mTdLoadPhoneSaleInfo.Start(objpara)

        '启动start的时候可能县城的状态来不及改变，会同时启动两个线程进去
        Utility.WaitSeconds(0.5)
    End Sub

    Private Sub LoadPhoneSaleInfo(ByVal obj As Object)
        Try
            If mDevice Is Nothing Then
                GoTo Do_Exit
            End If

            Dim strRegionInfo As String = obj(0).ToString()
            Dim salesInfo As SalesInfoPara = Nothing
            Dim isFromAppleWeb As Boolean = False

            Try
                Common.Log("测试日志*-*-*-*解析序列号：" & Me.mDevice.SerialNumber)

                '获取设备的生产周期信息
                mDeviceProduce = GetProduceInfo(mDevice.SerialNumber)
                mDateDeviceProduce = GetProduceDate(mDevice.SerialNumber)

            Catch ex As Exception
                Common.LogException(ex.ToString(), "LoadPhoneSaleInfo0")
            End Try

            Try
                '从本地配置中读取
                salesInfo = IniSetting.GetSalesInfo(mDevice.Identifier)
                Common.Log("测试日志*-*-*-*从本地获取购买时间")

#If IS_ITONG Then
                '从同步服务器取
                If salesInfo Is Nothing AndAlso Folder.LangType = LanguageType.zh_CN Then
                    salesInfo = GetSalesInfoFromServer(mDevice)
                End If
#End If

                '2015-10-19 苹果获取购买时间有变化 所以目前先删除
                '从苹果服务器取 
                'If Not CheckSalesInfo(salesInfo) Then
                '    salesInfo = GetSaleInfoFromAppleWeb(mDevice)
                '    isFromAppleWeb = True
                '    Common.Log("测试日志*-*-*-*从苹果服务器获取购买时间")
                'End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "LoadPhoneSaleInfo1")
            End Try

            Me.mSalesInfo = salesInfo
            Me.mIsFromAppleWeb = isFromAppleWeb
            mSalesAreaValue = iPhoneDeviceHelper.GetSaleRegion(strRegionInfo)

            '判断是否是新机
            Me.JudgeIsNewDeviceEx()
            'Me.JudgeIsNewDevice(salesInfo)

            '保存数据到数据库 保存数据到本地配置
            Me.SaveSalesInfo(salesInfo, Me.mDevice, strRegionInfo, isFromAppleWeb)
            If mSalesInfo Is Nothing Then
                Common.Log("测试日志*-*-*-*SalesInfoPara is nothing")
            Else
                Common.Log("测试日志*-*-*-*SalesInfoPara isnot nothing")
            End If

Do_Exit:
            Try
#If IS_ITONG_ZJ Then
                '写数据到ios商店里面
                Utility.WaitSeconds(2)
                Dim iNewDevice As Integer = IIf(Me.mIsNewDevice, 1, 0)
                AppleStoreHelper.Instance().UploadIsNewDevice(mDevice, iNewDevice)
                Common.Log("测试日志*-*-*-*写数据到ios商店里面")
#End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "LoadPhoneSaleInfo5")
            End Try

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadPhoneSaleInfo6")
        End Try
    End Sub

    Private Function GetSalesInfoFromServer(ByVal device As iPhoneDevice) As SalesInfoPara
        Dim dicSalesInfo As SalesInfoPara = Nothing
        Try
            '1、post数据
            Dim jsonData As New JsonObject()
            jsonData.Add("SN", device.SerialNumber)
            jsonData.Add("UDID", device.Identifier)

            Dim strData As String = Common.EncryptDES(JsonParser.SaveString(jsonData), Common.RgbKeyString, Common.RgbKeyString)
            strData = String.Format("data={0}", System.Web.HttpUtility.UrlEncode(strData))

            '"http://**************/tbzs/api.aspx?type=2"
            Dim strUrl As String = String.Format(WebUrl.PostSalesInfo, "2")
            Dim strWebResult As String = Utility.PostData(strUrl, strData)

            If String.IsNullOrEmpty(strWebResult) Then
                GoTo DO_EXIT
            End If

            '2、分析服务器返回值
            Dim blnResult As Boolean = False
            Dim objJson As JsonObject = JsonParser.ParseString(strWebResult)

            If objJson.ContainsKey("state") Then
                If CType(objJson("state"), JsonString).Value = "1" Then
                    blnResult = True
                End If
            End If

            If Not blnResult Then
                Debug.Print("获取设备销售信息失败：" & strWebResult)
                GoTo DO_EXIT
            End If

            If objJson.ContainsKey("data") Then
                Dim dataJson As JsonObject = objJson("data")
                dicSalesInfo = New SalesInfoPara()

                If dataJson.ContainsKey("PURCH_COUNTRY") Then
                    dicSalesInfo.PurchCountry = CType(dataJson("PURCH_COUNTRY"), JsonString)
                End If

                If dataJson.ContainsKey("PURCHASE_DATE") Then
                    dicSalesInfo.PurchaseDate = CType(dataJson("PURCHASE_DATE"), JsonString)
                End If

                If dataJson.ContainsKey("COV_END_DATE") Then
                    dicSalesInfo.CovEndDate = CType(dataJson("COV_END_DATE"), JsonString)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSalesInfoFromServer")
        End Try

DO_EXIT:
        Return dicSalesInfo
    End Function

    Private Function CheckSalesInfo(ByVal info As SalesInfoPara) As Boolean
        Dim blnReturn As Boolean = True

        If info Is Nothing OrElse info.CovEndDate.Length = 0 Then
            blnReturn = False
        End If

        Return blnReturn
    End Function

    Private Function GetSaleInfoFromAppleWeb(ByVal device As iPhoneDevice) As SalesInfoPara
        Dim result As KeyValuePair(Of WarrantyType, Date) = SaleInfoHelper.GetExpireDate(device)
        Dim info As SalesInfoPara = CreateSalesInfoPara(result)

        Return info
    End Function

    Private Function GetPurchaseDate(ByVal CovEndDate As Date) As Date
        '购买日期 是由 过保日期减一年 但是考虑到有的是延长保修时间 所以减完后要看购买时间是不是大于当前时间 如果不是继续减一
        Dim datePurchase As Date = CovEndDate.AddYears(-1)
        While DateDiff(DateInterval.Day, Date.Today, datePurchase) > 0
            datePurchase = datePurchase.AddYears(-1)
        End While

        If DateDiff(DateInterval.Day, DateDeviceProduce, datePurchase) < 0 Then
            datePurchase = datePurchase.AddYears(1)
        End If

Do_Exit:
        Return datePurchase
    End Function

    Private Sub UploadSalesInfoToServer(ByVal dicSalesInfo As SalesInfoPara, ByVal strSalesArea As String, ByVal device As iPhoneDevice)
        Try
            Dim jsonData As New JsonObject()
            jsonData.Add("SN", device.SerialNumber)
            jsonData.Add("UDID", device.Identifier)
            jsonData.Add("PURCH_COUNTRY", strSalesArea)
            jsonData.Add("PURCHASE_DATE", dicSalesInfo.PurchaseDate)
            jsonData.Add("COV_END_DATE", dicSalesInfo.CovEndDate)

            Dim strData As String = Common.EncryptDES(JsonParser.SaveString(jsonData), Common.RgbKeyString, Common.RgbKeyString)
            strData = String.Format("data={0}", System.Web.HttpUtility.UrlEncode(strData))

            '"http://**************/tbzs/api.aspx?type=1"
            Dim strUrl As String = String.Format(WebUrl.PostSalesInfo, "1")
            Dim strWebResult As String = Utility.PostData(strUrl, strData)

            If String.IsNullOrEmpty(strWebResult) Then
                Return
            End If

            'If strWebResult.Length > 0 Then
            '    Debug.Print("提交设备销售信息失败，返回信息为空")
            '    Return
            'End If
            Dim blnResult As Boolean = False
            Dim objJson As JsonObject = JsonParser.ParseString(strWebResult)

            If objJson.ContainsKey("state") Then
                If CType(objJson("state"), JsonString).Value = "1" Then
                    blnResult = True
                End If
            End If

            If Not blnResult Then
                Debug.Print("提交设备销售信息失败：" & strWebResult)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UploadSalesInfoToServer")
        End Try
    End Sub

    Public Sub SaveSalesInfo(ByVal salesinfo As SalesInfoPara, ByVal device As iPhoneDevice, ByVal strRegionInfo As String, ByVal fromAppleWeb As Boolean)
        Try
            Me.mSalesInfo = salesinfo
            Me.mIsFromAppleWeb = IsFromAppleWeb
            mSalesAreaValue = iPhoneDeviceHelper.GetSaleRegion(strRegionInfo)

            '提交数据到数据库（只提交从苹果服务器获取到的数据，并且这些数据是有效的）
            If fromAppleWeb AndAlso Me.CheckSalesInfo(salesinfo) Then
                Me.UploadSalesInfoToServer(salesinfo, SalesAreaValue, device)
                Common.Log("测试日志*-*-*-*保存信息到数据库")
            End If

            '写到本地配置
            IniSetting.SetSalesInfo(device.Identifier, salesinfo)
            Common.Log("测试日志*-*-*-*保存信息到本地")

        Catch ex As Exception
            Common.LogException(ex.ToString(), "SaveSalesInfo")
        End Try
    End Sub

    Public Sub JudgeIsNewDeviceEx()
        Dim isNewDevice As Boolean = False

        '判断是否新机 根据设备类型 和充电次数

        Dim intChargeRecordCount As Integer = -1
        Try
            '获取设备充电次数
            Dim obj As Dictionary(Of Object, Object) = Nothing
            Dim intCount As Integer = 0

            While obj Is Nothing
                obj = Me.mDevice.GetIORegistryInfo()
                intCount += 1
                If obj IsNot Nothing Then
                    Exit While
                End If

                If intCount > 4 Then
                    GoTo Do_Next
                End If
                Utility.WaitSeconds(3)
            End While

            For Each item As KeyValuePair(Of Object, Object) In obj
                If item.Key.ToString().ToLower() = "cyclecount" Then
                    intChargeRecordCount = item.Value
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "JudgeIsNewDeviceEx")
        End Try

Do_Next:
        Dim strProductType As String = ServerIniSetting.GetZJNewDeviceProductType()
        Dim intChargeCount As Integer = ServerIniSetting.GetZJNewDeviceChargeCount()

        If strProductType.Contains(Me.mDevice.ProductType) AndAlso intChargeRecordCount <= intChargeCount AndAlso intChargeRecordCount >= 0 Then
            isNewDevice = True
        End If
        Me.mIsNewDevice = isNewDevice

        Me.mIsReady = True

        RaiseEvent LoadPhoneSaleInfoEventHandler(Me, New SaleInfoEventArgs(Me.mDevice.SerialNumber))


        '收集信息反馈
        ActionCollectHelper.ZJHelperGetInformation(Me.mDevice, _
                                                           ModelKey.ZJHelperChargeRecordCountJudgeNewDevice, _
                                                           ActionDataType.Search, _
                                                          IIf(Me.mIsNewDevice, FunctionSucceed.Succeed, FunctionSucceed.Failure), _
                                                           String.Format("SN:{0} UID:{1}  ChargeCount:{2};ProductType:{3};IsNewDevice:{4} ", Me.mDevice.SerialNumber, PluginLogin.Instance().Uid, intChargeRecordCount, Me.mDevice.ProductType, Me.mIsNewDevice))

    End Sub

    'Public Sub JudgeIsNewDevice(ByVal salesInfo As SalesInfoPara)
    '    Try
    '        Dim fsucceed As FunctionSucceed = FunctionSucceed.None
    '        Dim strContent As String = String.Empty

    '        If salesInfo IsNot Nothing Then
    '            '判断是否是新机  DateDiff (interval, Date1 , Date2[,firstweekofyear[,firstweekofyear]]) Date1 ,Date2：计算期间的两个日期表达式，若 >date1 较早，则两个日期之间的期间结果为正值；若 >date2 较早， 则结果为负值。
    '            Dim iNewDeviceTime As Integer = ServerIniSetting.GetZJNewDeviceTimeRange()
    '            'If Common.IsTestMode() Then
    '            '    intNewDeviceTime = IniSetting.GetTestNewDeviceTime()

    '            '    Common.Log(String.Format("-------苹果返回时间:{0} 时间周期：{1} ", mSalesInfo.PurchaseDate, intNewDeviceTime))
    '            'End If

    '            If Not String.IsNullOrEmpty(salesInfo.PurchaseDate) AndAlso _
    '               DateDiff(DateInterval.Day, Convert.ToDateTime(salesInfo.PurchaseDate), Date.Today) <= iNewDeviceTime Then
    '                Me.mIsNewDevice = True
    '            End If
    '            Common.Log("测试日志*-*-*-*判断是否是新机" & mIsNewDevice & mSalesInfo.PurchaseDate)
    '            fsucceed = IIf(mIsNewDevice, FunctionSucceed.Succeed, FunctionSucceed.Failure)
    '            strContent = mSalesInfo.PurchaseDate
    '        End If
    '        Dim strSN As String = ""
    '        If Me.mDevice IsNot Nothing Then
    '            strSN = Me.mDevice.SerialNumber
    '        End If
    '        ActionCollectHelper.ZJHelperGetInformation(Me.mDevice, ModelKey.ZJHelperNewDevice, ActionDataType.Search, fsucceed, String.Format("SN:{0} UID:{2} {1}", strSN, strContent, PluginLogin.Instance().Uid))

    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "LoadPhoneSaleInfo3")
    '    End Try

    '    Me.mIsReady = True

    '    Try
    '        Common.Log("测试日志*-*-*-*RaiseEvent1:" & Date.Now)
    '        RaiseEvent LoadPhoneSaleInfoEventHandler(Me, New SaleInfoEventArgs(Me.mDevice.SerialNumber))
    '        Common.Log("测试日志*-*-*-*RaiseEvent2:" & Date.Now)

    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "JudgeIsNewDevice")
    '    End Try
    'End Sub

    Public Function CreateSalesInfoPara(ByVal result As KeyValuePair(Of WarrantyType, Date)) As SalesInfoPara
        Dim dicSalesInfo As SalesInfoPara = New SalesInfoPara()
        Try
            Select Case result.Key
                Case WarrantyType.Active     '未过保
                    dicSalesInfo.CovEndDate = result.Value.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDay))                                         '过保日期
                    dicSalesInfo.PurchaseDate = GetPurchaseDate(result.Value).ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDay))    '购买日期

                Case WarrantyType.Expire     '已过保
                    dicSalesInfo.CovEndDate = "2000-01-01"       '代表已过期
                    dicSalesInfo.PurchaseDate = ""                       '购买日期

                Case WarrantyType.Unknow  '未知
                    dicSalesInfo = Nothing

            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateSalesInfoPara")
        End Try
        Return dicSalesInfo
    End Function

    Private Sub PluginLogin_LoginEvent(ByVal sender As Object, ByVal e As EventArgs)
        Try
#If IS_ITONG_ZJ Then
            '写数据到ios商店里面
            Dim iNewDevice As Integer = IIf(Me.mIsNewDevice, 1, 0)
            AppleStoreHelper.Instance().UploadIsNewDevice(mDevice, iNewDevice)
            Common.Log("测试日志*-*-*-*写数据到ios商店里面")
#End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "SaleInfo_PluginLogin_LoginEvent")
        End Try
    End Sub

#End Region

#Region "--- 获取设备生产周期 ---"

    Public Shared Function GetProduceInfo(ByVal strSN As String) As String

        Dim strResult As String = ""

        Try
            Dim iYear As Integer = 0
            Dim iWeek As Integer = 0
            GetProduceYearWeek(strSN, iYear, iWeek)

            If iYear > 0 AndAlso iWeek > 0 Then
                'strResult = String.Format("{0}第{1}周", iYear, iWeek)

                '第几周转化为具体的日期
                strResult = GetWeekDate(iYear, iWeek)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetProduceInfo")
        End Try

        Return strResult
    End Function

    Private Shared Function GetWeekDate(iYear As Integer, iWeek As Integer) As String
        Dim strResult As String = ""

        Try
            Dim iAllDays As Integer = (iWeek - 1) * 7

            Dim dtFirst As New Date(iYear, 1, 1)
            Dim firstDayOfWeek As Integer = dtFirst.DayOfWeek
            firstDayOfWeek = IIf(firstDayOfWeek = 0, 7, firstDayOfWeek)

            strResult = dtFirst.AddDays(iAllDays).ToString("yyyy-MM-dd")

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetWeekDate")
        End Try

        Return strResult
    End Function

    Private Shared Function GetProduceDate(ByVal strSN As String) As DateTime
        Dim iYear As Integer = 0
        Dim iWeek As Integer = 0
        GetProduceYearWeek(strSN, iYear, iWeek)

        Dim dateFirst As DateTime = DateTime.MinValue
        Dim dateLast As DateTime = DateTime.MinValue

        If iYear < 1700 OrElse iYear > 9999 Then
            GoTo Do_Exit
        End If
        If iWeek < 1 OrElse iWeek > 53 Then
            GoTo Do_Exit
        End If

        Dim startDay As DateTime = New DateTime(iYear, 1, 1) '该年第一天
        Dim endDay As DateTime = New DateTime(iYear + 1, 1, 1).AddMilliseconds(-1)
        Dim dayOfWeek As Integer = 0
        If Convert.ToInt32(startDay.DayOfWeek.ToString("d")) > 0 Then
            dayOfWeek = Convert.ToInt32(startDay.DayOfWeek.ToString("d")) '该年第一天为星期几
        End If
        If dayOfWeek = 7 Then
            dayOfWeek = 0
        End If

        If iWeek = 1 Then
            dateFirst = startDay
            If dayOfWeek = 6 Then
                dateLast = dateFirst
            Else
                dateLast = startDay.AddDays((7 - dayOfWeek))
            End If

        Else
            dateFirst = startDay.AddDays((8 - dayOfWeek) + (iWeek - 2) * 7) 'index周的起始日期
            dateLast = dateFirst.AddDays(6)
            If dateLast > endDay Then
                dateLast = endDay
            End If

        End If
Do_Exit:
        Return dateFirst
    End Function

    Private Shared Sub GetProduceYearWeek(ByVal strSN As String, ByRef iYear As Integer, ByRef iWeek As Integer)
        '1，序列号 第4位是生产年份   第5位是生产周
        iYear = 0
        iWeek = 0
        Dim strYearKey As String = strSN.Substring(3, 1)
        Dim strWeekKey As String = strSN.Substring(4, 1)
        Dim lstYear As List(Of String) = GetYear(strYearKey)

        If lstYear.Count > 0 Then
            Integer.TryParse(lstYear(0), iYear)
            iWeek = GetWeek(strWeekKey, lstYear(1))
        End If
    End Sub

    Private Shared Function GetYear(ByVal strKey As String) As List(Of String)
        If mDictYears.Count <= 0 Then
            mDictYears = GetYearDic()
        End If

        Dim strValue As String = ""
        Dim lstResult As New List(Of String)
        Try
            For Each item As KeyValuePair(Of String, String) In mDictYears
                If item.Key = strKey Then
                    strValue = item.Value
                    Exit For
                End If
            Next

            If strValue.Length <= 0 Then
                GoTo Do_Exit
            End If

            lstResult.Add(strValue.Substring(0, 4))

            If strValue.Contains("上") Then
                lstResult.Add("U")
            Else
                lstResult.Add("D")
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetYear")
        End Try
Do_Exit:
        Return lstResult
    End Function

    Private Shared Function GetWeek(ByVal strKey As String, ByVal strYear As String) As Integer
        If mDictWeeks.Count <= 0 Then
            mDictWeeks = GetWeekDic()
        End If

        Dim iResult As Integer = 0
        Try
            For Each item As KeyValuePair(Of String, Integer) In mDictWeeks
                If item.Key = strKey Then
                    If strYear = "D" Then
                        iResult = item.Value + 26
                    Else
                        iResult = item.Value
                    End If
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetWeek")
        End Try

        Return iResult
    End Function

    Private Shared Function GetYearDic() As Dictionary(Of String, String)
        Dim dicYears As New Dictionary(Of String, String)
        dicYears.Add("C", "2010上")
        dicYears.Add("D", "2010下")
        dicYears.Add("F", "2011上")
        dicYears.Add("G", "2011下")
        dicYears.Add("H", "2012上")
        dicYears.Add("J", "2012下")
        dicYears.Add("K", "2013上")
        dicYears.Add("L", "2013下")
        dicYears.Add("M", "2014上")
        dicYears.Add("N", "2014下")
        dicYears.Add("P", "2015上")
        dicYears.Add("Q", "2015下")
        dicYears.Add("R", "2016上")
        dicYears.Add("S", "2016下")
        dicYears.Add("T", "2017上")
        dicYears.Add("V", "2017下")
        dicYears.Add("W", "2018上")
        dicYears.Add("X", "2018下")
        dicYears.Add("Y", "2019上")
        dicYears.Add("Z", "2019下")
        Return dicYears
    End Function

    Private Shared Function GetWeekDic() As Dictionary(Of String, Integer)
        Dim dictWeeks As New Dictionary(Of String, Integer)
        dictWeeks.Add("1", 1)
        dictWeeks.Add("2", 2)
        dictWeeks.Add("3", 3)
        dictWeeks.Add("4", 4)
        dictWeeks.Add("5", 5)
        dictWeeks.Add("6", 6)
        dictWeeks.Add("7", 7)
        dictWeeks.Add("8", 8)
        dictWeeks.Add("9", 9)
        dictWeeks.Add("C", 10)
        dictWeeks.Add("D", 11)
        dictWeeks.Add("F", 12)
        dictWeeks.Add("G", 13)
        dictWeeks.Add("H", 14)
        dictWeeks.Add("J", 15)
        dictWeeks.Add("K", 16)
        dictWeeks.Add("L", 17)
        dictWeeks.Add("M", 18)
        dictWeeks.Add("N", 19)
        dictWeeks.Add("P", 20)
        dictWeeks.Add("Q", 21)
        dictWeeks.Add("R", 22)
        dictWeeks.Add("T", 23)
        dictWeeks.Add("V", 24)
        dictWeeks.Add("W", 25)
        dictWeeks.Add("X", 26)
        dictWeeks.Add("Y", 27)
        Return dictWeeks
    End Function

#End Region

#Region "--- 获取设备保修到期日期 ---"

    Public Shared Function GetExpireDate(ByVal device As iPhoneDevice) As KeyValuePair(Of WarrantyType, Date)
        Dim dateExpire As New KeyValuePair(Of WarrantyType, Date)(WarrantyType.Unknow, Date.MinValue)
        'strSerial = "F87LCE2YF196"

        Try
            Dim status As FunctionSucceed = FunctionSucceed.Succeed
            Dim strSerial As String = device.SerialNumber
            Dim strUrl As String = String.Format("https://selfsolve.apple.com/wcResults.do?sn={0}&cn=&locale=&caller=&num=0", strSerial)
            Dim strContent As String = ""
            Dim strPost As String = ""
            Dim iTryCount = 0

            '获取时间如果是 Unknow 就尝试 重新获取3次
            While dateExpire.Key = WarrantyType.Unknow AndAlso iTryCount < 3
                strContent = GetContentStringFromUrlWithAisi(strUrl)
                strPost = FormatContent(strContent)
                dateExpire = GetExpireDateByRegularExpressions(strContent)
                iTryCount = iTryCount + 1
                Common.Log(String.Format("测试日志*-*-*-*访问:{0}  SN:{1} intTryCount:{3} 返回结果：{2}  ", strUrl, strSerial, strPost, iTryCount))

                If dateExpire.Key = WarrantyType.Unknow AndAlso iTryCount < 3 Then
                    Utility.WaitSeconds(1)
                End If
            End While

            If dateExpire.Key = WarrantyType.Unknow Then
                status = FunctionSucceed.Failure
            ElseIf dateExpire.Key = WarrantyType.Expire Then
                status = FunctionSucceed.Overdue
            End If

            ActionCollectHelper.ZJHelperGetInformation(device, ModelKey.ZJHelperPTimeByPostDate, ActionDataType.Search, _
                                                                                                status, _
                                                                                                String.Format("SN:{0} UID:{2}  {1}", device.SerialNumber, strPost, PluginLogin.Instance().Uid))

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetExpireDate")
        End Try

        Return dateExpire
    End Function

    Private Shared Function GetContentStringFromUrlWithAisi(ByVal url As String) As String
        Dim blnRetry As Boolean = False
        Dim result As String = ""

DoRetry:
        Try
            Dim wbRequest As HttpWebRequest = DirectCast(HttpWebRequest.Create(url), HttpWebRequest)
            wbRequest.Timeout = 20000
            wbRequest.AllowAutoRedirect = True
            wbRequest.AllowWriteStreamBuffering = True
            wbRequest.KeepAlive = False
            wbRequest.UserAgent = "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 5.1)"
            wbRequest.Accept = "*/*"

            Dim response As WebResponse = wbRequest.GetResponse()
            Dim stream As Stream = response.GetResponseStream()
            Dim reader As New StreamReader(stream, Encoding.UTF8)
            Dim strOut As String = reader.ReadToEnd()
            reader.Close()
            stream.Close()
            result = System.Web.HttpUtility.HtmlDecode(strOut)

        Catch ex As Exception
            If Not blnRetry AndAlso (ex.Message.Contains("405") OrElse ex.Message.Contains("502") OrElse ex.Message.Contains("无法解析此远程名称")) Then
                blnRetry = True
                GoTo DoRetry
            End If

            Utility.LogException(url & vbCrLf & ex.ToString(), "GetContentStringFromUrl")
        End Try

        Return result
    End Function

    Public Shared Function FormatContent(ByVal strcontent As String) As String
        Dim result As String = strcontent
        If result Is Nothing Then
            Return result
        End If
        '将长度 1000改为 980 剩下20个给 拼序列号留位置
        Try
            Dim strStart As String = "displayHWSupportInfo"
            Dim iStart As Integer = strcontent.IndexOf(strStart)

            If iStart > 0 Then
                result = strcontent.Substring(iStart, IIf(strcontent.Length - iStart > 960, strcontent.Length - iStart, 960))
            ElseIf strcontent.Length > 960 Then
                result = strcontent.Substring(strcontent.Length - 960, 960)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "FormatContent")
        End Try

        Return result
    End Function

    '有三种结果
    '1、可获取保修到期日期
    '2、已过保
    '3、未知（网络问题、接口问题等获取不到）
    Public Shared Function GetExpireDateByRegularExpressions(ByVal strContent As String) As KeyValuePair(Of WarrantyType, Date)
        Dim type As WarrantyType = WarrantyType.Unknow
        Dim dateResult As Date = Date.MinValue

        Dim newDevice As String = "window.location.href ='/RegisterProduct.do?productRegister="
        If strContent.Contains(newDevice) Then
            type = WarrantyType.Active
            dateResult = Date.Now.AddYears(1)
            GoTo DO_EXIT
        End If

        Dim regular As String = "displayHWSupportInfo\((?<active>[\w\W]*?),\s?'[\w\W]*?,\s?'(?<date>[\w\W]*?)',\s?'"
        Dim dt As DataTable = Utility.GetMatchStringByRegularExpressions(strContent, New String(0) {regular}, New String(1) {"active", "date"})

        If dt Is Nothing OrElse dt.Rows.Count <= 0 Then
            GoTo DO_EXIT
        End If

        Try
            Dim dr As DataRow = dt.Rows(0)

            Dim strActive As String = dr("active").ToString().Trim().ToLower()
            Dim strDate As String = dr("date").ToString().Trim()

            Select Case strActive
                Case "false"
                    type = WarrantyType.Expire

                Case "true"
                    If strDate.Length = 0 Then
                        GoTo DO_EXIT
                    End If

                    '解析目标<br/>预计到期日：2015年11月13日 <br/>

                    Dim strDateValue As String = ""
                    Dim strStart As String = String.Empty
                    Dim strStartCn As String = "<br/>预计到期日："
                    Dim strStartEn As String = "<br/>Estimated Expiration Date:"
                    Dim strEnd As String = "<br/>"

                    Dim startIndex As Integer = -1
                    Dim startIndexCn As Integer = strDate.IndexOf(strStartCn)
                    Dim startIndexEn As Integer = strDate.IndexOf(strStartEn)

                    If startIndexCn > 0 Then
                        strStart = strStartCn
                        startIndex = startIndexCn
                    Else
                        strStart = strStartEn
                        startIndex = startIndexEn

                    End If

                    If startIndex < 0 Then
                        Common.Log("GetExpireDateByRegularExpressions1*-*-*-*" & strDateValue)
                        GoTo DO_EXIT
                    End If

                    strDateValue = strDate.Substring(startIndex + strStart.Length)
                    Dim endIndex As Integer = strDateValue.IndexOf(strEnd)
                    If endIndex < 0 Then
                        Common.Log("GetExpireDateByRegularExpressions2*-*-*-*" & strDateValue)
                        GoTo DO_EXIT
                    End If

                    Try
                        strDateValue = strDateValue.Substring(0, endIndex).Trim
                        dateResult = Convert.ToDateTime(strDateValue)
                        type = WarrantyType.Active
                    Catch ex As Exception
                        Common.LogException(strDateValue & vbCrLf & ex.ToString(), "GetExpireDateByRegularExpressions3")
                    End Try

            End Select

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetExpireDateByRegularExpressions4")
        End Try

DO_EXIT:
        Return New KeyValuePair(Of WarrantyType, Date)(type, dateResult)
    End Function

#End Region

End Class

Public Class SaleInfoEventArgs
    Inherits EventArgs

    Dim strSN As String

    Public Sub New(ByVal strsn As String)
        Me.strSN = strsn
    End Sub

    Public Property mStrSN() As String
        Get
            Return Me.strSN
        End Get
        Set(ByVal value As String)
            Me.strSN = value
        End Set
    End Property

End Class