﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmNewDevice
    Inherits PNGControlForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.btnOpen = New tbButton
        Me.picIcon = New System.Windows.Forms.PictureBox
        Me.lblName = New System.Windows.Forms.Label
        Me.lblConnected = New System.Windows.Forms.Label
        Me.pnlMain = New System.Windows.Forms.Panel
        CType(Me.picIcon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlMain.SuspendLayout()
        Me.SuspendLayout()
        '
        'btnOpen
        '
        Me.btnOpen.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOpen.BackColor = System.Drawing.Color.Transparent
        Me.btnOpen.BindingForm = Nothing
        Me.btnOpen.Location = New System.Drawing.Point(185, 14)
        Me.btnOpen.Name = "btnOpen"
        Me.btnOpen.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOpen.Selectable = True
        Me.btnOpen.Size = New System.Drawing.Size(57, 24)
        Me.btnOpen.TabIndex = 0
        Me.btnOpen.tbAdriftIconWhenHover = False
        Me.btnOpen.tbAutoSize = False
        Me.btnOpen.tbAutoSizeEx = False
        Me.btnOpen.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnOpen.tbBackgroundImageState = ImageState.FourState
        Me.btnOpen.tbBadgeNumber = 0
        Me.btnOpen.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpen.tbEndEllipsis = False
        Me.btnOpen.tbIconHoldPlace = True
        Me.btnOpen.tbIconImage = Nothing
        Me.btnOpen.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpen.tbIconImageState = ImageState.OneState
        Me.btnOpen.tbIconMore = False
        Me.btnOpen.tbIconMouseDown = Nothing
        Me.btnOpen.tbIconMouseHover = Nothing
        Me.btnOpen.tbIconMouseLeave = Nothing
        Me.btnOpen.tbIconPlaceText = 2
        Me.btnOpen.tbIconReadOnly = Nothing
        Me.btnOpen.tbImageMouseDown = Nothing
        Me.btnOpen.tbImageMouseHover = Nothing
        Me.btnOpen.tbImageMouseLeave = Nothing
        Me.btnOpen.tbReadOnly = False
        Me.btnOpen.tbReadOnlyText = False
        Me.btnOpen.tbShadow = False
        Me.btnOpen.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOpen.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOpen.tbShowNew = False
        Me.btnOpen.tbShowToolTipOnButton = False
        Me.btnOpen.tbSplit = "13,11,13,11"
        Me.btnOpen.tbText = "Open"
        Me.btnOpen.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpen.tbTextColor = System.Drawing.Color.White
        Me.btnOpen.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOpen.tbTextColorDown = System.Drawing.Color.White
        Me.btnOpen.tbTextColorHover = System.Drawing.Color.White
        Me.btnOpen.tbTextMouseDownPlace = 0
        Me.btnOpen.tbToolTip = ""
        Me.btnOpen.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpen.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpen.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picIcon
        '
        Me.picIcon.BackColor = System.Drawing.Color.Transparent
        Me.picIcon.Location = New System.Drawing.Point(2, -1)
        Me.picIcon.Name = "picIcon"
        Me.picIcon.Size = New System.Drawing.Size(56, 56)
        Me.picIcon.TabIndex = 1
        Me.picIcon.TabStop = False
        '
        'lblName
        '
        Me.lblName.AutoEllipsis = True
        Me.lblName.BackColor = System.Drawing.Color.Transparent
        Me.lblName.ForeColor = System.Drawing.Color.White
        Me.lblName.Location = New System.Drawing.Point(62, 11)
        Me.lblName.Name = "lblName"
        Me.lblName.Size = New System.Drawing.Size(117, 18)
        Me.lblName.TabIndex = 2
        Me.lblName.Text = "Jason's iPad"
        '
        'lblConnected
        '
        Me.lblConnected.AutoEllipsis = True
        Me.lblConnected.BackColor = System.Drawing.Color.Transparent
        Me.lblConnected.ForeColor = System.Drawing.Color.White
        Me.lblConnected.Location = New System.Drawing.Point(62, 29)
        Me.lblConnected.Name = "lblConnected"
        Me.lblConnected.Size = New System.Drawing.Size(117, 18)
        Me.lblConnected.TabIndex = 2
        Me.lblConnected.Text = "Connected ..." & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'pnlMain
        '
        Me.pnlMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(66, Byte), Integer), CType(CType(72, Byte), Integer), CType(CType(76, Byte), Integer))
        Me.pnlMain.Controls.Add(Me.picIcon)
        Me.pnlMain.Controls.Add(Me.btnOpen)
        Me.pnlMain.Controls.Add(Me.lblConnected)
        Me.pnlMain.Controls.Add(Me.lblName)
        Me.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlMain.ForeColor = System.Drawing.Color.FromArgb(CType(CType(66, Byte), Integer), CType(CType(72, Byte), Integer), CType(CType(76, Byte), Integer))
        Me.pnlMain.Location = New System.Drawing.Point(0, 0)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(249, 55)
        Me.pnlMain.TabIndex = 3
        '
        'frmNewDevice
        '
        Me.BackColor = System.Drawing.SystemColors.ActiveCaption
        Me.ClientSize = New System.Drawing.Size(249, 55)
        Me.Controls.Add(Me.pnlMain)
        Me.Name = "frmNewDevice"
        Me.TransparencyKey = System.Drawing.SystemColors.Control
        CType(Me.picIcon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlMain.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btnOpen As tbButton
    Friend WithEvents picIcon As System.Windows.Forms.PictureBox
    Friend WithEvents lblName As System.Windows.Forms.Label
    Friend WithEvents lblConnected As System.Windows.Forms.Label
    Friend WithEvents pnlMain As System.Windows.Forms.Panel

End Class
