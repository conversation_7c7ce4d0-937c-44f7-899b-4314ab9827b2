﻿
Imports System.Threading
Imports System.Net
Imports System.Text
Imports System.IO
Imports System.Runtime.InteropServices
Imports iTong.CoreFoundation

Public Class frmSummary

    Private _infoDevice As New tbDeviceInfo
    Private mIsRecoverMode As Boolean = False
    Private mIsDFUMode As Boolean = False
    Private mCapacityShow As DeviceCapacityShow = Nothing
    Private mDownManage As MultiThreadDownload
    Private mInstallHelper As iPhoneInstallHelper = Nothing

    Private mThreadSreenshot As Thread
    Private mActivatedDevice As Boolean = True          '记录当前设备有没有激活
    Private mFormDetail As frmDetail
    Private mbtnLastClick As tbButton = Nothing
    Private mblnCapacityAutoReload As Boolean = False
    Private Shared frmTuiIns As frmTuiInstall
    Public Shared DeviceFreeSize As Long = -1
    Public mDataSource As DataTable  '展示设备详细信息的数据源
    Private mHasLoad As Boolean = False

    Private mAutoTryInstallTui As Boolean = False   '当推安装失败时，自动尝试重新安装一次

    Private mIsShowHotApps As Boolean = True
    Private mblnLoadeed As Boolean = False
    Private mTickCount As Integer = 0
    Private mDiskSize As Integer = 0

    Private mThreadCheckTuiInstall As Thread

#If IS_ITONG Then
    Private mfrmH5GameInstall As frmH5GameInstall
    Private mfrmBatteryManager As frmBatteryManager = Nothing
    Private mfrmHotApps As frmHotApps = Nothing
    Private mIPhoneHotAppsHelper As iPhoneHotAppsHelper = Nothing
    Private mfrmCanceliOSUpgrade As frmCanceliOSUpgrade = Nothing
#End If
    Private miPhoneInstallGameHelper As iPhoneInstallGameHelper = Nothing
    Private mBatteryEfficiency As String = ""   '电池寿命
#Region "--- 初始化，关闭窗体 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As Object)
        MyBase.New()
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = Me.mApplication.Language

        Me.mIsRecoverMode = (TypeOf device Is iPhoneRecoveryDevice)
        Me.mIsDFUMode = (TypeOf device Is iPhoneDFUDevice)

        If Folder.LangType = LanguageType.zh_CN Then
            Me.btnYueYu.Visible = True
            'Me.btnClearTip.Top = 449
            Me.tbCapacity.Top = 476

            If ServerIniSetting.IsShowTBGuide() Then
                Me.btnQQ.Visible = True
                Me.btnBackupRestore.Visible = False
                Me.btnQQ.Location = Me.btnBackupRestore.Location
                Me.btnWeixin.Location = New Point(Me.btnQQ.Right + 2, Me.btnWeixin.Top)

                If IniSetting.GetShowQQNew() Then
                    Me.btnQQ.tbShowNew = True
                Else
                    Me.btnQQ.tbShowNew = False
                End If

            Else
                '以熊猫助手去掉同步推安装button
                Me.btnBackupRestore.Visible = True
                Me.btnQQ.Visible = False
                If Utility.IsPanda Then
                    Me.btnBackupRestore.Location = Me.btnTuiToolV3.Location
                    Me.btnTuiToolV3.Visible = False
                End If
                Me.btnWeixin.Location = New Point(Me.btnBackupRestore.Right + 2, Me.btnWeixin.Top)

            End If


            Me.btnBinding.Location = New Point(Me.btnWeixin.Right + 2, Me.btnBinding.Top)
            Me.btnRepairPay.Location = New Point(Me.btnBinding.Right + 2, Me.btnRepairPay.Top)
        Else
            Me.btnYueYu.Visible = False
        End If

        Me.InitJailbreakTool()

        If Common.VerIs30() Then
            Me.Tag = DeviceType.iOS
        End If
    End Sub

    '初始化时执行：初始化基本的界面。
    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.InitResources()

        Me.lblNameValue.MaximumSize = New Size(300, 30)
        Me.lblNameValue.AutoEllipsis = True

        Me.tbCapacity.Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
        Me.tbDevCapacity.Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
        Me.tbDevCapacity.ShowSizeInfoTop = False
        Me.txtNameValue.MaxLength = 61
        Me.CanbeMove = False

        If Folder.LangType = LanguageType.zh_CN Then
            Me.tsmiSina.Visible = True
            Me.tsmiTecent.Visible = True
            Me.tsmiFacebook.Visible = False
            Me.tsmiTwitter.Visible = False
            Me.btnWeixin.Visible = True

        ElseIf Folder.LangType = LanguageType.vi_VN OrElse Folder.LangType = LanguageType.th_TH Then
            Me.tsmiSina.Visible = False
            Me.tsmiTecent.Visible = False
            Me.tsmiFacebook.Visible = True
            Me.tsmiTwitter.Visible = False
            Me.btnWeixin.Visible = False

        Else
            Me.tsmiSina.Visible = False
            Me.tsmiTecent.Visible = False
            Me.tsmiFacebook.Visible = True
            Me.tsmiTwitter.Visible = True
            Me.btnWeixin.Visible = False

        End If

        If Utility.IsPanda Then
            Me.btnYueYu.Visible = False
        End If

        IniSetting.SetUserConnectionType(PopMsgDeviceType.iOS)

        If Me.mAndroid Is Nothing AndAlso IniSetting.GetShowFindWeChatDataNew() Then
            ' Me.btnWeixin.tbShowDot = True
            Me.btnWeixin.tbShowNew = True
        Else
            ' Me.btnWeixin.tbShowDot = False
            Me.btnWeixin.tbShowNew = False
        End If

        If Folder.AppType = RunType.Tongbu_Abroad Then
            Me.btnQQChat.Padding = New System.Windows.Forms.Padding(20, 10, 10, 20)
            If ServerIniSetting.GetShowiSafeFoneRecommend() Then
                btnQQChat.Visible = True
            Else
                btnQQChat.Visible = False
            End If
        End If

        Me.btnWechat.tbTextColorHover = Color.Black
        Me.btnQQChat.tbTextColorHover = Color.Black
        Me.btnRingtone.tbTextColorHover = Color.Black
    End Sub

    Private Sub InitResources()
        Me.btnScreenshotSetUp.tbBackgroundImage = My.Resources.btn_4_setting

#If IS_ITONG Then
        Me.btnBatteryManager.tbBackgroundImage = My.Resources.btn_4_battery
        Me.lblDFUDescription1.tbIconImage = GuiHelper.GetMouseStateImage(My.Resources.flash_first, 0, 2)
        Me.lblDFUDescription2.tbIconImage = GuiHelper.GetMouseStateImage(My.Resources.flash_second, 0, 2)
#End If
        Me.btnDisconnect.tbBackgroundImage = My.Resources.summary_btn_3_disconnect
        Me.pnlScreenshot.tbBackgroundImage = My.Resources.summary_nav_bg
        Me.PictureBox1.Image = My.Resources.summary_nav_split
        Me.PictureBox2.Image = My.Resources.summary_nav_split
        Me.PictureBox3.Image = My.Resources.summary_nav_split
        Me.PictureBox4.Image = My.Resources.summary_nav_split
        Me.btnScreenshotPlay.tbBackgroundImage = My.Resources.btn_4_fullScreen
        Me.btnShareTo.tbBackgroundImage = My.Resources.btn_4_sharing
        Me.btnScreenshot.tbBackgroundImage = My.Resources.btn_4_screenshot
        Me.btnRename.tbBackgroundImage = My.Resources.summary_btn_3_edit
        Me.btnClear.tbBackgroundImage = My.Resources.btn_4_clearrubbish
        Me.picLoading.Image = My.Resources.gif_loading_summary_16
        Me.btnRefresh.tbBackgroundImage = My.Resources.btn_4_refresh2
        Me.btnDetail.tbBackgroundImage = My.Resources.summary_btn_3_info
        Me.btnJump.tbBackgroundImage = My.Resources.btn_3_goto
        Me.btnClearTip.tbBackgroundImage = My.Resources.frm_bg_Tip_down
        Me.btnYueYu.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnYueYu.tbIconImage = My.Resources.summary_icon_jailbreak
        Me.btnRepairPay.tbIconImage = My.Resources.summary_repair_pay_v3
        Me.btnBinding.tbIconImage = My.Resources.summary_binding_apple_id
        Me.btnWeixin.tbIconImage = My.Resources.summary_weixin
        'Me.btnWeixin.tbShowNew = IniSetting.GetIsShowIosWeixinNew()  '是否显示new
        Me.btnBackupRestore.tbIconImage = My.Resources.summary_backup
        Me.btnTuiToolV3.tbIconImage = My.Resources.summary_tui_v3
        Me.btnQQ.tbIconImage = My.Resources.summary_qq
        Me.btnRepairToolV3.tbIconImage = My.Resources.summary_repair_v3
        Me.btnBindingTip.tbBackgroundImage = My.Resources.frm_bg_tip_binding
        Me.btnGotoFlashDevice.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnOutRecoverMode.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnH5Game.tbIconImage = My.Resources.summary_h5game

        Me.tsmiSina.Image = My.Resources.weibo_sina
        Me.tsmiTecent.Image = My.Resources.weibo_tecent
        Me.tsmiFacebook.Image = My.Resources.weibo_facebook
        Me.tsmiTwitter.Image = My.Resources.weibo_twitter
        Me.btnCanceliOSUpgrade.tbBackgroundImage = My.Resources.btn_4_blue
        Me.picLoadingDeviceData.Image = My.Resources.gif_loading_summary_16
    End Sub

#Region "--- 初始化3.0 ---"

    Protected Sub InitControlsV3()
        '隐藏2.0的Button
        If Common.VerIs30 AndAlso Not Me.mblnLoadeed Then
            Me.mblnLoadeed = True
            Me.BackColor = Color.FromArgb(250, 251, 252)

            '设备信息的控件位置调整
            Me.pnlDeviceName.Location = New Point(Me.pnlDeviceName.Left - 130, Me.pnlDeviceName.Top - 30)

            'web界面位置调整
            Me.pnlWeb.Visible = True
            Me.pnlWeb.Location = New Point(Me.pnlDeviceName.Left - 6, Me.pnlDeviceName.Bottom - 30)
            Me.pnlWeb.Size = New Size(650, 340)

            '3.0功能panel
            Me.pnlToolV3.Visible = True
            Me.pnlToolV3.Location = New Point(Me.pnlWeb.Left, Me.pnlWeb.Bottom)

            '设备图片
            Me.pbDevice.Location = New Point(Me.pnlDeviceName.Left - Me.pbDevice.Width - 30, Me.pbDevice.Top + 7)
            '设备图片下的功能操作
            Me.pnlScreenshot.Size = New Size(180, Me.pnlScreenshot.Height)
            Me.pnlScreenshot.Location = New Point(Me.pbDevice.Left + (Me.pbDevice.Width - Me.pnlScreenshot.Width) / 2, Me.pbDevice.Bottom + 25)
            'Me.SetScreenshotContralLocation()

            '越狱button
            Me.btnYueYu.Visible = True
            Me.btnYueYu.Size = New Size(Me.pnlScreenshot.Width, Me.btnYueYu.Height)
            Me.btnYueYu.Padding = New Padding(20, 2, 20, 2)
            Me.btnYueYu.Location = New Point(Me.pnlScreenshot.Left + (Me.pnlScreenshot.Width - Me.btnYueYu.Width) / 2, Me.pnlScreenshot.Bottom + 28)

            '容量显示控件位置和样式
            Me.tbCapacity.TotalCapacity.ShowOneLine = True
            Me.tbCapacity.Size = New Size(965, Me.tbCapacity.Height)
            Me.tbCapacity.Location = New Point(Me.pnlScreenshot.Left - 90, Me.pnlWeb.Bottom + 100)
            Me.tbDevCapacity.TotalCapacity.ShowOneLine = True

            '垃圾清理的tip
            Me.btnClearTip.Location = New Point(Me.tbDevCapacity.Right - Me.btnClearTip.Width + 11, Me.btnClearTip.Top)

            '下载插件显示下载进度的label
            Me.lblDownloadProgress.Location = New Point(Me.pbDevice.Left + (Me.pbDevice.Width - Me.lblDownloadProgress.Width) / 2, Me.pbDevice.Bottom + 50)
        End If

        Me.btnClearTip.BringToFront()
        Me.SetRepairPayment()
        Me.SetH5Game()
    End Sub

    Public Sub InitSummaryRecommend()
        'Application.DoEvents()
        'Dim frm As New frmSummaryRecommend(Me.mApplication, Me.mDevice, 0)
        'Utility.AddForm2Panel(frm, Me.pnlWeb, True)
    End Sub

    Private Sub SetScreenshotContralLocation()
        Dim intLength As Integer = Me.pnlScreenshot.Width / 5
        Dim intLoc As Integer = (intLength - Me.btnScreenshot.Width) / 2
        Me.btnScreenshot.Location = New Point(intLoc, Me.btnScreenshot.Top)
        Me.btnScreenshotPlay.Location = New Point(intLength + intLoc, Me.btnScreenshotPlay.Top)
        Me.btnShareTo.Location = New Point(intLength * 2 + intLoc, Me.btnShareTo.Top)

        Me.btnScreenshotSetUp.Location = New Point(intLength * 4 + intLoc, Me.btnScreenshotSetUp.Top)
        Me.btnBatteryManager.Location = New Point(intLength * 3 + intLoc, Me.btnBatteryManager.Top)
        Me.btnBatteryManager.tbShowDot = IniSetting.GetShowBatteryManagerNew()

        '分割符
        Me.PictureBox1.Location = New Point(intLength, Me.PictureBox1.Top)
        Me.PictureBox2.Location = New Point(intLength * 2, Me.PictureBox2.Top)
        Me.PictureBox3.Location = New Point(intLength * 3, Me.PictureBox3.Top)
        Me.PictureBox4.Location = New Point(intLength * 4, Me.PictureBox4.Top)
    End Sub

    Private Sub SetRepairPayment()
        Dim blniPhione As Boolean = True
        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.ProductType.ToLower.Contains("ipad") Then
            blniPhione = False
        End If

        If blniPhione AndAlso _
           Me.mDevice IsNot Nothing AndAlso _
           Folder.LangType = LanguageType.zh_CN AndAlso _
           Common.VerIs30 AndAlso _
           ServerIniSetting.GetShowRepairPayment AndAlso _
           Not Me.mDevice.Jailbreaked Then

            Me.btnRepairPay.Visible = True
            If IniSetting.GetShowRepairPaymentNew Then
                Me.btnRepairPay.tbShowNew = True
            Else
                Me.btnRepairPay.tbShowNew = False
            End If

        Else
            Me.btnRepairPay.Visible = False
        End If
    End Sub

    Private Sub SetH5Game()
        If Me.btnRepairPay.Visible = True Then
            Me.btnH5Game.Location = New Point(Me.btnRepairPay.Right + 2, Me.btnH5Game.Top)
        Else
            Me.btnH5Game.Location = New Point(Me.btnBinding.Right + 2, Me.btnH5Game.Top)
        End If

        If ServerIniSetting.GetShowH5Game Then
            Me.btnH5Game.Visible = True
            If IniSetting.GetShowH5GameNew() Then
                Me.btnH5Game.tbShowNew = True
            Else
                Me.btnH5Game.tbShowNew = False
            End If
        Else
            Me.btnH5Game.Visible = False
        End If
    End Sub

#End Region

    Private Sub IniInstallHelper()
        Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)
        RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
        AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
    End Sub

    Private Sub IniInstallGameHelper()
        Me.miPhoneInstallGameHelper = iPhoneInstallGameHelper.GetInstance(Me.mDevice)
        Me.miPhoneInstallGameHelper.StrongGame()
    End Sub

    '初始化时执行或更换设备时执行
    Public Overrides Sub CheckInit()
        MyBase.CheckInit()

        If Me.mHasLoad Then
            Return
        End If
        Me.mHasLoad = True

        Me.mAutoTryInstallTui = False
        Me.btnJump.Visible = False
        Me.lblDownloadProgress.Visible = False
        Me.lblDownloadProgress.Text = String.Empty
        Me.lblDownPluginProgress.Visible = False
        Me.lblDownPluginProgress.Text = String.Empty

        Me.picLoading.Visible = True
        Me.btnRefresh.Visible = False
        Me.picLoadingDeviceData.Visible = True
        Me.btnRefreshDeviceData.Visible = False
        Me.btnClearTip.Visible = False
        Me.lblNameValue.Visible = True
        Me.lblDeviceName.Visible = True
        Me.pnlChangeDeviceName.Visible = False

        If Folder.LangType = LanguageType.en_US Then
            Me.btnYueYu.Visible = False
        Else
            If Not mIsRecoverMode AndAlso Not mIsDFUMode Then
                Me.tmrTui.Start()
                Me.pnlSummary.Visible = True
            Else
                Me.pnlSummary.Visible = False
            End If

            If Folder.LangType = LanguageType.zh_CN Then
                Me.btnYueYu.Visible = True
            Else
                Me.btnYueYu.Visible = False
            End If
        End If

        If Not mIsRecoverMode AndAlso Not mIsDFUMode AndAlso Me.mDevice IsNot Nothing Then  '' Added by Utmost20141015   '' 避免拔掉设备后，程序延时还读取设备属性
            Me.InitCapacityShow()
            Me.mblnCapacityAutoReload = False
            Me.mCapacityShow.LoadCapacity()

            RemoveHandler mDevice.DeviceNameChanged, AddressOf OnDeviceNameChanged
            AddHandler mDevice.DeviceNameChanged, AddressOf OnDeviceNameChanged

            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf SetClearTipThread))
        End If

        Me.SetControls()

        If Folder.LangType = LanguageType.zh_CN Then
            Me.InstallAppSyncThread()
        End If

        Me.IniInstallHelper()

        mIsShowHotApps = True

#If IS_ITONG Then
        Me.mIPhoneHotAppsHelper = iPhoneHotAppsHelper.GetInstance(Me.mDevice)
#End If

        If Not Me.mIsRecoverMode AndAlso Me.mDevice IsNot Nothing Then
            If Not Me.tmrRefresh.Enabled Then
                '初始化电量信息
                Me.InitCharging()
            End If

            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ShowDeviceHomePageThread))
        End If

        If Me.tmrTui.Enabled = False Then
            Me.tmrTui.Start()
        End If

        '强装 游戏
        Me.IniInstallGameHelper()
    End Sub

    Private Sub ShowDeviceHomePageThread(ByVal objParam As Object)
        Try
            ActionCollectHelper.ShowDeviceHomePage(Me.mDevice)
            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.Identifier.Length > 0 Then
                AppDeviceDB.GetInstance.InsertDeviceInfo(Me.mDevice.Identifier)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "ShowDeviceHomePageThread")
        End Try
    End Sub

    Private Sub InitCapacityShow()

        If Me.mDevice IsNot Nothing Then  '' Added by Utmost20141015   '' 避免拔掉设备后，程序延时还读取设备属性

            If mCapacityShow IsNot Nothing Then
                RemoveHandler mCapacityShow.CapacityCallback, AddressOf OnCapacityCallback
                RemoveHandler mCapacityShow.CapacityLoadCompleted, AddressOf OnCapacityLoadCompleted
            End If

            mCapacityShow = DeviceCapacityShow.GetInstance(Me.mDevice)

            If mCapacityShow IsNot Nothing Then   '' Added by Utmost20141015   '' 避免拔掉设备后，程序延时还读取设备属性

                AddHandler mCapacityShow.CapacityCallback, AddressOf OnCapacityCallback
                AddHandler mCapacityShow.CapacityLoadCompleted, AddressOf OnCapacityLoadCompleted
            End If
        End If

    End Sub

    Private Sub InitCharging()
        If Me.mDevice IsNot Nothing Then
            If Me.RefreshBattery() < 100 Then
                Me.tmrRefresh.Start()
            Else
                Me.tmrRefresh.Stop()
            End If

        ElseIf Me.mDeviceRecovery IsNot Nothing Then
            Me.picCharging.Image = Nothing
            Me.lblCharging.Text = ""

        Else
            Me.picCharging.Image = Nothing
            Me.lblCharging.Text = ""

        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.btnDetail.tbToolTip = Me.Language.GetString("Welcome.Button.DeviceDetails")                              '"Device Details"
        Me.btnDisconnect.tbToolTip = Me.Language.GetString("Welcome.Button.DisconnectDevice")   '"断开设备连接"  'Me.Language.GetString("Welcome.Button.PopupDevice")                            '"Pop-up device"
        Me.btnRename.tbToolTip = Me.Language.GetString("Welcome.Button.ModifyDeviceName")                           '"Modify the device name"

        Me.btnRefresh.tbToolTip = Me.Language.GetString("Common.Button.Refresh")

        If Me.mIsRecoverMode Then
            Me.btnOutRecoverMode.tbText = Me.Language.GetString("Welcome.Button.RestoreModesOut")                   '"退出恢复模式"
        End If

        Me.tbCapacity.BeginUpdate()

        Me.tbCapacity.TotalCapacity.Text = Me.Language.GetString("Welcome.Label.TotalCapacity")                     '"容量"
        Me.tbCapacity.AudioCapacity.Text = Me.Language.GetString("Welcome.Label.AudioCapacity")                     '"音频"
        Me.tbCapacity.VedioCapacity.Text = Me.Language.GetString("Main.Button.Video")                               '"影片"
        Me.tbCapacity.CameraCapacity.Text = Me.Language.GetString("Main.Button.Camera")                             '"照片"
        Me.tbCapacity.AppCapacity.Text = Me.Language.GetString("Main.Button.App")                                   '"程序"
        Me.tbCapacity.EBookCapacity.Text = Me.Language.GetString("Main.Button.Book")                                '"图书"
        Me.tbCapacity.OtherCapacity.Text = Me.Language.GetString("Common.Label.Other")                              '"其它"
        Me.tbCapacity.AvailableCapacity.Text = Me.Language.GetString("Welcome.Label.AvailableCapacity")             '"可用空间"

        '截屏
        Me.tsmiScreenshot.Text = Me.Language.GetString("Welcome.Text.ScreenshotNoShell")                            '"截屏(无外壳)"
        Me.tsmiScreenshotShell.Text = Me.Language.GetString("Welcome.Text.ScreenshotHaveShell")                     '"截屏(带外壳)"

        Me.btnScreenshot.tbToolTip = Me.Language.GetString("Welcome.Button.Screenshot")                             '"屏幕截屏"
        Me.btnShareTo.tbToolTip = Me.Language.GetString("Welcome.Message.ShareScreenshortTo")                       '"分享截屏到"                   '"分享截屏到微博"
        Me.btnScreenshotPlay.tbToolTip = Me.Language.GetString("Welcome.Text.ScreenshotTitle")                      '"实时桌面"
        Me.btnScreenshotSetUp.tbToolTip = Me.Language.GetString("Common.Button.Setting")                            '"设置"
        Me.btnBatteryManager.tbToolTip = Me.Language.GetString("Tools.Button.BatteryManager")                       '电池管理

        'End If
        Me.btnClear.tbToolTip = Me.Language.GetString("Main.Button.ClearRubbish")                                   '"垃圾清理"

        Me.tsmiTecent.Text = Me.Language.GetString("WeiBo.Label.Tencent")                                           '"腾讯微博"
        Me.tsmiSina.Text = Me.Language.GetString("WeiBo.Label.Sina")                                                '"新浪微博"
        Me.tbCapacity.Colon = Me.Language.GetString("Common.Symbol.Colon")
        Me.tbCapacity.EndUpdate()

        Me.tbDevCapacity.BeginUpdate()
        Me.tbDevCapacity.TotalCapacity.Text = Me.Language.GetString("Welcome.Label.TotalCapacity")                     '"容量"
        Me.tbDevCapacity.AudioCapacity.Text = Me.Language.GetString("Welcome.Label.AudioCapacity")                     '"音频"
        Me.tbDevCapacity.VedioCapacity.Text = Me.Language.GetString("Main.Button.Video")                               '"影片"
        Me.tbDevCapacity.CameraCapacity.Text = Me.Language.GetString("Main.Button.Camera")                             '"照片"
        Me.tbDevCapacity.AppCapacity.Text = Me.Language.GetString("Main.Button.App")                                   '"程序"
        Me.tbDevCapacity.EBookCapacity.Text = Me.Language.GetString("Main.Button.Book")                                '"图书"
        Me.tbDevCapacity.OtherCapacity.Text = Me.Language.GetString("Common.Label.Other")                              '"其它"
        Me.tbDevCapacity.AvailableCapacity.Text = Me.Language.GetString("Welcome.Label.AvailableCapacity")             '"可用空间"
        Me.tbDevCapacity.Colon = Me.Language.GetString("Common.Symbol.Colon")
        Me.tbDevCapacity.EndUpdate()

        Me.btnYueYu.Text = Me.Language.GetString("Welcome.Button.StartJailbreak")                                   '手机估值
        Me.btnRepairToolV3.Text = Me.Language.GetString("Welcome.Button.Repair")
        Me.btnTuiToolV3.Text = Me.Language.GetString("File.Label.tbTui")
        Me.btnBackupRestore.Text = Me.Language.GetString("Backup.Text.Title")                                       '"备份还原

        Me.btnWeixin.Text = Me.Language.GetString("Weixin.Text.Title")                                              '微信
        Me.btnBinding.Text = Me.Language.GetString("App.Button.BindingAppleId")                                     '"绑定 Apple ID"
        Me.btnBindingTip.Text = Me.Language.GetString("App.Tip.BindingDescription")                                 '"绑定 Apple ID 告别闪退"
        Me.btnRepairPay.Text = Me.Language.GetString("Tools.Payment.Text.Payment")                                  '"闪退赔付"
        Me.btnH5Game.Text = Me.Language.GetString("H5Game.Button.Games")                                            '小游戏
        Me.lblConnectMode.Font = Common.CreateFont("微软雅黑", 20.0!, FontStyle.Regular)
        Me.lblDFUTitle.Font = Common.CreateFont("微软雅黑", 11.0!, FontStyle.Regular)

        Me.btnCanceliOSUpgrade.Text = Me.Language.GetString("Common.Lable.CloseiOSUpgrade")                     '"关闭iOS更新"

        Me.btnScreensort1.Text = Me.Language.GetString("Welcome.Button.Screenshot")                             '"截屏"
        Me.btnDeviceDesktop.Text = Me.Language.GetString("Welcome.Text.ScreenshotTitle")                        '"实时桌面"
        Me.btnRestarDevice.Text = Me.Language.GetString("Main.Button.Restar")                                   '"重启"
        Me.btnCloseDevice.Text = Me.Language.GetString("Main.Button.CloseDevice")                               '"关机"

        Me.lbliOSVersion.Text = Me.Language.GetString("Welcome.Label.FirmwareVersion") & Me.Language.GetString("Common.Symbol.Colon")                          '"固件版本"
        Me.lblSN.Text = Me.Language.GetString("Welcome.Label.SerialNumber") & Me.Language.GetString("Common.Symbol.Colon")                                     '"序列号"
        Me.lblIMEI.Text = "IMEI" & Me.Language.GetString("Common.Symbol.Colon")
        Me.lblSaleArea.Text = Me.Language.GetString("Welcome.Label.SalesArea") & Me.Language.GetString("Common.Symbol.Colon")                                '"销售地"
        Me.lblProductDate.Text = Me.Language.GetString("Welcome.Label.ProductionDate") & Me.Language.GetString("Common.Symbol.Colon")                         '"生产日期"
        Me.lblGuaranteeDate.Text = Me.Language.GetString("Welcome.Label.WarrantyDate") & Me.Language.GetString("Common.Symbol.Colon")                          '"过保日期"
        Me.lblChargingTimes.Text = Me.Language.GetString("Welcome.Label.ChargeTimes") & Me.Language.GetString("Common.Symbol.Colon")                          '"充电次数"
        Me.lblChargingPercent.Text = Me.Language.GetString("Welcome.Label.BatteryLife") & Me.Language.GetString("Common.Symbol.Colon")                        '"电池寿命"
        Me.lblDeviceIdentify.Text = Me.Language.GetString("Welcome.Label.UniqueDeviceId") & Me.Language.GetString("Common.Symbol.Colon")                       '"设备标识"

        Me.cbxCloseiTunesStart.Text = Me.Language.GetString("Welcome.Label.StopiTunesStart")                    '"阻止iTunes自启动"
        Me.lblBackup.Text = Me.Language.GetString("Backup.Text.Title")                                          '"备份还原

        Me.btnWechat.Text = Me.Language.GetString("Welcome.Label.WeChatLogs")                                   '"微信聊天记录恢复"
        Me.btnWechat.tbToolTip = Me.Language.GetString("Welcome.Label.WeChatLogsDesc")                          '"两种模式：提供扫描设备恢复，微信单独备份恢复"
        Me.btniOSDowngrade.Text = Me.Language.GetString("Welcome.Label.iOSUpdate")                              '"iOS系统升级降级"
        Me.btniOSDowngrade.tbToolTip = Me.Language.GetString("Welcome.Label.iOSUpdateDesc")                     '"请先备份好您的数据"
        Me.btnRingtone.Text = Me.Language.GetString("Music.Button.RingtoneMaker")                               '"铃声制作"
        Me.btnRingtone.tbToolTip = Me.Language.GetString("Welcome.Label.RingtoneMakerDesc")                     '"DIY手机铃声，一键导入设备"
        If Folder.AppType = RunType.Tongbu_Abroad AndAlso ServerIniSetting.GetShowiSafeFoneRecommend() Then
            Me.btnQQChat.Text = "iSafeFone D-Recovery for iOS"                                 '"iSafeFone D-Recovery for iOS
            Me.btnQQChat.tbToolTip = "Recover lost or deleted iOS data"                        '"Recover lost or deleted iOS data
        Else
            'If ServerIniSetting.GetShowiSafeFoneRecommendCN() Then
            '    Me.btnQQChat.tbIconImage = Global.iTong.My.Resources.Resources.summary_iSafeFone_53
            '    Me.btnQQChat.tbShowNew = ServerIniSetting.GetShowNewiSafeFoneRecommendCN()
            '    Me.btnQQChat.Text = ServerIniSetting.GetiSafeFoneRecommendCNText() '"数据滚回来"
            '    Me.btnQQChat.tbToolTip = ServerIniSetting.GetiSafeFoneRecommendCNToolTip()
            '    'Me.btnQQChat.Padding = New System.Windows.Forms.Padding(20, 10, 10, 10)
            'Else
            '    Me.btnQQChat.tbIconImage = Global.iTong.My.Resources.Resources.summary_qq_53
            '    Me.btnQQChat.Text = Me.Language.GetString("Welcome.Label.ManageQQData")                                 '"QQ数据管理"
            '    Me.btnQQChat.tbToolTip = Me.Language.GetString("Welcome.Label.ManageQQDataDesc")                        '"新推出人工数据恢复"
            'End If
            Me.btnQQChat.tbIconImage = Global.iTong.My.Resources.Resources.more_restore
            Me.btnQQChat.Text = Me.Language.GetString("Backup.Text.Title")
            Me.btnQQChat.tbToolTip = Me.Language.GetString("Backup.Label.DescriptionEx")
            RemoveHandler Me.btnQQChat.Click, AddressOf btnQQ_Click
            AddHandler Me.btnQQChat.Click, AddressOf btnBackupRestore_Click

        End If

        Me.btnZXKF.tbIconImage = Global.iTong.My.Resources.Resources.summary_zxkf_53
        Me.btnZXKF.tbShowNew = ServerIniSetting.GetShowNewZXFKCN()
        Me.btnZXKF.Text = "掌心客服"
        Me.btnZXKF.tbToolTip = ServerIniSetting.GetZXFKCNToolTip()
        'Me.btnZXKF.Padding = New System.Windows.Forms.Padding(20, 10, 10, 10)

        Me.lblCloseiOSUpdate.Text = Me.Language.GetString("Common.Lable.CloseiOSUpgrade")                       '"关闭iOS更新"
        Me.lblChargingDetail.Text = Me.Language.GetString("Welcome.Label.BatteryDetails")                             '"电池详情"

        Me.btnAuthDevice.Text = Me.Language.GetString("Welcome.Button.Repair")                                  '"正版授权"
        Me.btnInstallTui.Text = Me.Language.GetString("Photo.Message.InstalTui")                                '"安装同步推"
        Me.btnAppeidBinding.Text = Me.Language.GetString("App.Button.BindingAppleId")                           '"绑定 Apple ID"
        Me.btnClearRabbish.tbToolTip = Me.Language.GetString("Main.Button.ClearRubbish")                        '"垃圾清理"
        Me.InitScreensortLocation()
        Me.btnMore.Text = Me.Language.GetString("Main.Button.MoreInfo")                                         '"更多"
    End Sub

    Private Sub InitScreensortLocation()
        Dim intDiff As Integer = 4
        Dim intLength As Integer = Me.btnScreensort1.Width + Me.btnDeviceDesktop.Width + Me.btnRestarDevice.Width + Me.btnCloseDevice.Width + intDiff * 6
        Dim intLocation As Integer = Me.picDevice.Left + (Me.picDevice.Width - intLength) \ 2

        Dim intTop As Integer = Me.picDevice.Bottom + 23

        Me.btnScreensort1.Location = New Point(intLocation, intTop)
        Me.picSplit1.Location = New Point(Me.btnScreensort1.Right + intDiff, intTop)
        Me.btnDeviceDesktop.Location = New Point(Me.picSplit1.Right + intDiff, intTop)
        Me.picSplit2.Location = New Point(Me.btnDeviceDesktop.Right + intDiff, intTop)
        Me.btnRestarDevice.Location = New Point(Me.picSplit2.Right + intDiff, intTop)
        Me.picSplit3.Location = New Point(Me.btnRestarDevice.Right + intDiff, intTop)
        Me.btnCloseDevice.Location = New Point(Me.picSplit3.Right + intDiff, intTop)
        Me.lblDownPluginProgress.Location = New Point(Me.picDevice.Left + (Me.picDevice.Width - Me.lblDownPluginProgress.Width) \ 2, Me.btnCloseDevice.Bottom + 15)
    End Sub

    Protected Sub btnYueYu_SizeChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles btnYueYu.SizeChanged
        If btnYueYu IsNot Nothing AndAlso Me.pbDevice IsNot Nothing Then
            Me.btnYueYu.Left = Me.pbDevice.Left + (Me.pbDevice.Width - Me.btnYueYu.Width) \ 2
        End If
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        If Me.mIsRecoverMode OrElse Me.mIsDFUMode Then
            Me.mIsRecoverMode = False
            Me.mIsDFUMode = False
            Me.mHasLoad = False
        End If
        MyBase.OnConnect(device)
#If IS_ITONG Then
        MyWorldHelper.GetInstance(Me.mDevice, Me.mApplication)
#End If
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        Me.mHasLoad = False

        Try
            Me.tmrRefresh.Stop()
            Me.tmrTui.Stop()

            If Me.mfrmScreenshotPreview IsNot Nothing Then
                Me.mfrmScreenshotPreview.Close()
            End If
            If Me.frmImageView IsNot Nothing Then
                Me.frmImageView.Close()
            End If

            If Me.mFormDetail IsNot Nothing Then
                Me.mFormDetail.Close()
            End If
        Catch
        End Try

#If IS_ITONG Then
        MyWorldHelper.RemoveInstance(device)

        Try
            If Me.mfrmBatteryManager IsNot Nothing Then
                Me.mfrmBatteryManager.Close()
            End If
        Catch
        End Try

        Try
            If Me.mfrmHotApps IsNot Nothing Then
                Me.mfrmHotApps.Close()
            End If
        Catch
        End Try


        If Me.mIPhoneHotAppsHelper IsNot Nothing Then
            RemoveHandler Me.mIPhoneHotAppsHelper.LoadiPhoneHotAppsEventHandler, AddressOf OnLoadiPhoneHotApps
            Me.mIPhoneHotAppsHelper.Dispose()
            Me.mIPhoneHotAppsHelper = Nothing
        End If

#End If

        Try
            If Me.miPhoneInstallGameHelper IsNot Nothing Then
                Me.miPhoneInstallGameHelper.Dispose()
                Me.miPhoneInstallGameHelper = Nothing
            End If
        Catch
        End Try

        Try
            If Me.mThreadCheckTuiInstall IsNot Nothing AndAlso Me.mThreadCheckTuiInstall.ThreadState <> ThreadState.Stopped Then
                Me.mThreadCheckTuiInstall.Abort()
            End If
        Catch ex As Exception
        End Try

        Try
            If frmTuiIns IsNot Nothing Then
                frmTuiIns.Close()
                frmTuiIns = Nothing
            End If
        Catch ex As Exception
        End Try

        Try
            For Each ctl As Form In Application.OpenForms
                If TypeOf ctl Is frmTuiInstall Then
                    ctl.Close()
                    ctl = Nothing
                End If
            Next
        Catch ex As Exception
        End Try

        Try
            If Me.mThreadSreenshot IsNot Nothing AndAlso Me.mThreadSreenshot.ThreadState <> ThreadState.Stopped Then
                Me.mThreadSreenshot.Abort()
            End If
        Catch ex As Exception
        End Try

        '' Added by Utmost20141206
        If mDataSource IsNot Nothing Then
            mDataSource.Clear()
            mDataSource = Nothing
        End If

        If mCapacityShow IsNot Nothing Then
            mCapacityShow.Dispose()
            mCapacityShow = Nothing
        End If

        If mInstallHelper IsNot Nothing Then
            mInstallHelper.Dispose()
            mInstallHelper = Nothing
        End If

        If mFormDetail IsNot Nothing AndAlso Not mFormDetail.IsDisposed Then
            mFormDetail.Dispose()
            mFormDetail.Close()
            mFormDetail = Nothing
        End If
    End Sub

    Public Overrides Sub OnRecoverConnect(ByVal device As iPhoneRecoveryDevice)
        Me.mIsRecoverMode = True
        Me.mIsDFUMode = False
        MyBase.OnRecoverConnect(device)
        Me.SetControls()
    End Sub

    Public Overrides Sub OnDFUConnect(device As Device.iPhoneDFUDevice)
        Me.mIsRecoverMode = False
        Me.mIsDFUMode = True
        MyBase.OnDFUConnect(device)
        Me.SetControls()
    End Sub

    Private Sub SetControls()
        Me.pnlScreenshot.Visible = Not Me.mIsRecoverMode AndAlso Not Me.mIsDFUMode
        Me.tbCapacity.Visible = Not Me.mIsRecoverMode AndAlso Not Me.mIsDFUMode
        Me.btnRefresh.Visible = Not Me.mIsRecoverMode AndAlso Not Me.mIsDFUMode
        Me.btnDetail.Visible = Not Me.mIsRecoverMode AndAlso Not Me.mIsDFUMode
        Me.btnRename.Visible = Not Me.mIsRecoverMode AndAlso Not Me.mIsDFUMode
        Me.btnYueYu.Visible = Not Me.mIsRecoverMode AndAlso Not Me.mIsDFUMode
        Me.pnlDFUModeInfo.Visible = Me.mIsRecoverMode OrElse Me.mIsDFUMode

        Me.btnOutRecoverMode.Visible = Me.mIsRecoverMode
        Me.InitControlsV3()

        If Me.mIsRecoverMode OrElse Me.mIsDFUMode Then
            Me.pnlDFUModeInfo.Location = Me.pnlDeviceName.Location
            Me.pnlDFUModeInfo.Size = New Size(900, 520)
            Me.LoadPhoneInfoOnDFUMode()
            'Me.Start2LoadDeviceInfo(False)

            Me.LoadDeviceImage()
        Else
            Me.LoadPhoneInfo()

        End If
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        If Me.mIsRecoverMode Then
            Me.mDeviceRecovery = Nothing
        Else
            Me.OnDisconnect(Me.mDevice)
        End If

        '窗体关闭的时候把资源注销掉，特别是图片资源
        Try
            For Each item As Control In Me.pnlWeb.Controls
                If item IsNot Nothing AndAlso TypeOf item Is frmSummaryRecommend Then
                    CType(item, frmSummaryRecommend).Close()
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary_BeforeFormClose")
        End Try
    End Sub

    Private Sub OnDeviceNameChanged(ByVal sender As Object, ByVal e As EventArgs)
        Me.lblNameValue.Text = Me.mDevice.DeviceName
        Me.lblDeviceName.Text = Me.mDevice.DeviceName

        Dim strValue As String = Me.mDevice.DeviceName
        If Not String.IsNullOrEmpty(strValue) Then
            Me.mDataSource.Rows(0).Item(1) = strValue
            Me._infoDevice.DeviceName = strValue
        End If
    End Sub

    Public Sub StopRefresh()
        Me.tmrRefresh.Stop()
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub lblNameValue_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblNameValue.SizeChanged
        'Debug.Print(Me.lblNameValue.Text)
        Me.btnRename.Location = New Point(Me.lblNameValue.Right + 20, Me.lblNameValue.Top + (Me.lblNameValue.Height - btnRename.Height) \ 2)
        Me.btnDetail.Location = New Point(Me.btnRename.Right + 10, Me.btnRename.Top + (Me.btnRename.Height - btnDetail.Height) \ 2)
        Me.btnDisconnect.Location = New Point(Me.btnDetail.Right + 10, Me.btnDetail.Top + (Me.btnDetail.Height - btnDisconnect.Height) \ 2)
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click, btnRefreshDeviceData.Click
        Me.btnRefresh.Visible = False
        Me.picLoading.Visible = True
        Me.btnRefreshDeviceData.Visible = False
        Me.picLoadingDeviceData.Visible = True

        mCapacityShow = DeviceCapacityShow.GetInstance(Me.mDevice)
        If Me.mCapacityShow IsNot Nothing Then
            Me.mCapacityShow.LoadCapacity(True)
        End If

        Me.Start2CheckTuiInstall()

        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryRefreshCapacity)
    End Sub

    Private Sub btnDetail_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDetail.Click, btnDeviceInfo.Click, btnMore.Click
        If mFormDetail IsNot Nothing Then
            mFormDetail.Close()
            mFormDetail.Dispose()
        End If

        mFormDetail = New frmDetail(Me.mApplication, Me.mDataSource, Me)
        mFormDetail.StartPosition = FormStartPosition.CenterParent
        mFormDetail.Show(Me.mApplication)
        mFormDetail.frmSumm = Me

        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryDetail)
    End Sub

    Private Sub btnDisconnect_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnDisconnect.Click
        '"是否要断开连接？", _
        If tbMessageBox.Show(Me.mApplication,
                                                    Me.Language.GetString("Welcome.Message.Disconnect"),
                                                    Me.Language.GetString("Common.Info"),
                                                    MessageBoxButtons.YesNo,
                                                    MessageBoxIcon.Question,
                                                    MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.No Then
            Return
        End If

        If Me.mDevice IsNot Nothing Then
            MobileDeviceManager.Instance().OnRemoveDevice(Me.mDevice, 0)
        ElseIf Me.mDeviceRecovery IsNot Nothing Then
            MobileDeviceManager.Instance().OnRemoveDeviceRecovery(Me.mDeviceRecovery)
        End If

        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryBreakOff)
    End Sub

#If IS_ITONG Then

    Public Shared Sub RunApp(ByVal app As IApplication, ByVal dev As iPhoneDevice, ByVal type As Type, Optional ByVal para As Object = Nothing, Optional ByVal isShowDialog As Boolean = False, Optional ByVal ABType As AloneBackupType = AloneBackupType.None)
        Dim strModule As String = String.Empty
        If type Is GetType(frmWeixinEmoticon) OrElse type Is GetType(RingMakerForm) Then
            strModule = ",AppUnion"
        End If

        Dim frmType As Type = System.Type.GetType(type.ToString & strModule, True, True)
        'If frmType.Name.Contains("frmBackupSMS") AndAlso Me.mDevice.VersionNumber >= 800 AndAlso Not Me.mDevice.Jailbreaked AndAlso Not Me.mDevice.InstallCydia Then
        '    '"该功能暂不支持iOS8未越狱设备。"
        '    tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.UnSupportiOS8"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
        '    Return
        'End If

        If Not frmMore.CheckSupport(frmType.ToString, app, dev) Then
            Return
        End If

        Dim strName As String = dev.Identifier & type.FullName & IIf(ABType <> AloneBackupType.None, ABType.ToString(), "")
        Dim frm As Form = MainForm.ItemShowEx(frmType, strName, dev, app, para, False, isShowDialog, ABType)

        'If Folder.LangType = LanguageType.zh_CN Then
        '    '2015-07-08 by chenbihai 微信6.2.2弹窗提醒
        '    Dim frmTemp As frmWeixinEmoticon = CType(frm, frmWeixinEmoticon)
        '    RemoveHandler frmTemp.OnLoadFinished, AddressOf frmWeixin_OnLoadFinished
        '    AddHandler frmTemp.OnLoadFinished, AddressOf frmWeixin_OnLoadFinished
        'End If

        '名字要带设备ID不然拔掉的时候不会关闭。
        frm.Name = strName
    End Sub

    Private Sub frmWeixin_OnLoadFinished(ByVal sender As Object, ByVal args As WeixinEmoticonLoadArgs)
        If args.Status = WeixinState.Above6220 Then

            If Me.Tag IsNot Nothing AndAlso Me.Tag <> DeviceType.Unknown Then
                Dim mobileDevice As IDevice = Nothing

                If Me.Tag = DeviceType.Android Then
                    mobileDevice = Me.mAndroid
                ElseIf Me.Tag = DeviceType.iOS Then
                    mobileDevice = Me.mDevice
                End If
                frmWeixinEmoticonApp.ShowForm(Me.mApplication, mobileDevice, False)
            End If
        End If
    End Sub

    Private Sub btnWeixinEmo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnWeixin.Click, btnWechat.Click
        IniSetting.SetIsShowIosWeixinNew(False)
        RunApp(Me.mApplication, Me.mDevice, GetType(frmWeixinMain), Nothing, True, AloneBackupType.WeChat)
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.Weixin)
    End Sub

    Private Sub btnRepairPay_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRepairPay.Click
        Try
            IniSetting.SetShowRepairPaymentNew(False)
            Me.btnRepairPay.tbShowNew = False

            If Not Common.NetworkIsAvailable Then
                tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Welcome.Label.NetDisable"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
                Return
            End If

            For Each frm As Form In Application.OpenForms
                If frm IsNot Nothing AndAlso TypeOf frm Is frmRepairPayment AndAlso CType(frm, frmRepairPayment).mDevice Is Me.mDevice Then
                    frm.BringToFront()
                    frm.WindowState = FormWindowState.Normal
                    frm.Show()
                    Return
                End If
            Next

            Dim frmPaymenr As New frmRepairPayment(Me.mApplication, Me.mDevice)
            frmPaymenr.StartPosition = FormStartPosition.CenterParent
            frmPaymenr.Show(Me)
        Catch ex As Exception
            Common.LogException(ex.ToString, "btnRepairPay_Click")
        End Try
    End Sub

    Private Sub btnBackupRestore_Click(sender As Object, e As EventArgs) Handles btnBackupRestore.Click
        RunApp(Me.mApplication, Me.mDevice, GetType(frmBackupList), Nothing, True)
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.BackupRestore)
    End Sub

    Private Sub btnH5Game_Click(sender As Object, e As EventArgs) Handles btnH5Game.Click
        Try
            IniSetting.SetShowH5GameNew(False)
            Me.btnH5Game.tbShowNew = False
            Me.ShowFormH5GameInstall()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnH5Game_Click")
        End Try
    End Sub
#End If

    Private Sub txtNameValue_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles txtNameValue.Validating
        Me.ChangeDeviceName()
    End Sub

    Private Sub frmSummary_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Click
        Me.ChangeDeviceName()
        Me.ChangeDeviceNameEx()
    End Sub

    Private typePara() As Object = New Object() {ToolsType.Authorization}
    Private Sub btnRepair_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRepairToolV3.Click, btnAuthDevice.Click
        MainForm.ItemShow(GetType(frmTools), Me.mDevice, Me.mApplication, typePara)
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryAuthorization)
    End Sub

    Private Sub btnJump_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnJump.Click
        '"http://bbs.tongbu.com/thread-55451-1-1.html"
        Common.OpenExplorer(WebUrl.PageJailbreak)
    End Sub

    Private Sub lblVersion_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblVersion.SizeChanged
        Me.btnJump.Location = New Point(Me.lblVersion.Right + 15, Me.lblVersion.Top + (Me.lblVersion.Height - Me.btnJump.Height) \ 2)
        Dim intRight As Integer = Me.lblVersion.Right
        If Me.btnJump.Visible Then
            intRight = intRight + Me.btnJump.Width + 15
        ElseIf Me.btnGoJailbreakWeb.Visible Then
            intRight = intRight + Me.btnGoJailbreakWeb.Width + 15
        End If
        Me.btnCanceliOSUpgrade.Location = New Point(intRight + 15, Me.btnCanceliOSUpgrade.Top)
        Me.btnCanceliOSUpgrade.Visible = True
    End Sub

    Private Sub lblVersion_LocationChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblVersion.LocationChanged
        Me.btnJump.Location = New Point(Me.lblVersion.Right + 15, Me.lblVersion.Top + (Me.lblVersion.Height - Me.btnJump.Height) \ 2)
    End Sub

    Private Sub btnTui_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnTuiToolV3.Click, btnInstallTui.Click
        Me.ShowFormTuiInstall(False, True, Me.mDevice, Me.mApplication)
    End Sub

    Private Sub btnJailbreak_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        '"http://www.yueyuzhushou.com/"
        Common.OpenExplorer(WebUrl.ChinaYueYuZhuShou)
    End Sub

    Private Sub btnChangeDevice_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If MobileDeviceManager.Instance().ConnectedDevices.Count = 0 Then
            Return
        End If

        '如果设备状态都是没连接的就不显示－－－－－－－－－－
        Dim isConnection As Boolean = False
        For Each Item As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
            If Item IsNot Nothing AndAlso Item.IsConnected Then
                isConnection = True
                Exit For
            End If
        Next
        If Not isConnection Then
            Return
        End If
        '－－－－－－－－－－－－－－－－－－－－－－－－－－


#If IS_ITONG Then

        Dim rdb As tbButton = sender
        Dim frmMain As MainForm = Me.mApplication
        Dim posMoreToScreen As Point = frmMain.PointToScreen(Me.Location)

        Dim frmList As New frmDeviceList(Me.mApplication, sender)
        frmList.StartPosition = FormStartPosition.Manual
        frmList.Location = New Point(posMoreToScreen.X + rdb.Right - 110, posMoreToScreen.Y + rdb.Top + 122)
        frmList.Show()

#End If

    End Sub

    Private Sub btnClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClear.Click, btnClearRabbish.Click
#If IS_ITONG Then
        If Common.VerIs30 Then
            Dim strName As String = Me.mDevice.Identifier & GetType(frmClearRubbish).ToString()
            MainForm.ItemShowEx(GetType(frmClearRubbish), strName, Me.mDevice, Me.mApplication, sender.tag)
        Else
            MainForm.ItemShow(GetType(frmClearRubbish), Me.mDevice, Me.mApplication)
        End If
#End If

        Me.btnClearTip.Visible = False
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryGarbageClear)
    End Sub

    Private Sub btnClearTip_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClearTip.Click
        Me.btnClearTip.Visible = False
    End Sub

    Private Sub btnClearTip_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnClearTip.Resize
        Me.btnClearTip.Location = New Point(Me.tbDevCapacity.Right - Me.btnClearTip.Width + 11, Me.btnClearTip.Top)
    End Sub

    Private Sub btnYueYu_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnYueYu.Click
        Me.StartJailbreakTool()
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryJailbreak)

        '#If IS_ITONG Then
        '        Dim frmPrice As New frmDevicePrice(Me.mApplication)
        '        frmPrice.Show(Me)
        '        ActionCollectHelper.DevicePrice(Me.mDevice)
        '#End If
    End Sub

    Private Sub pnlDeviceName_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pnlDeviceName.Click
        Me.ChangeDeviceName()
    End Sub

    Private Sub btnBinding_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBinding.Click, btnAppeidBinding.Click
        Try
            For Each frm As Form In Application.OpenForms
                If Not (TypeOf frm Is frmiTunesLoginV3) Then
                    Continue For
                End If

                frm.BringToFront()
                Return
            Next

            Dim frmiTunes As New frmiTunesLoginV3(Me.mDevice, LoginMode.None)
            frmiTunes.StartPosition = FormStartPosition.CenterParent
            frmiTunes.Show(Me.mApplication)
            Me.btnBinding.tbShowNew = False
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmSummary_BindingClick")
        End Try

        ActionCollectHelper.AppleIDAccition(Me.mDevice, ModelKey.SummaryAppleId, ActionDataType.Click)
    End Sub

    Private Sub btnBinding_MouseMove(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles btnBinding.MouseMove
        If Not String.IsNullOrEmpty(IniSetting.GetDefaultUserAccount()) Then
            Return
        End If

        Me.btnBindingTip.Location = New Point(Me.pnlToolV3.Left + Me.btnBinding.Right - 20, Me.pnlToolV3.Top + 10)
        Me.btnBindingTip.BringToFront()
        Me.btnBindingTip.Visible = True
    End Sub

    Private Sub btnBinding_MouseLeave(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBinding.MouseLeave
        Me.btnBindingTip.Visible = False
    End Sub

    Private Sub btnGoJailbreakWeb_Click(sender As Object, e As EventArgs) Handles btnGoJailbreakWeb.Click
        Common.OpenExplorer("http://news.tongbu.com/71765.html")
    End Sub

#End Region

#Region "--- 获取设备信息 ---"

    Private Sub LoadPhoneInfoOnDFUMode()
        Me.pbDevice.Image = My.Resources.device_default
        Me.lblDeviceType.Text = Me.Language.GetString("Welcome.Label.DeviceType") & "："  '"设备型号："
        Me.lblProductType.Text = Me.Language.GetString("Welcome.Label.ProductType") & "：" '"产品型号："
        Me.lblECID.Text = Me.Language.GetString("Welcome.Label.ECID") & "："  '"设备ECID："
        Me.btnOutRecoverMode.Text = Me.Language.GetString("Welcome.Button.RestoreModesOut")      '"退出恢复模式"
        Me.btnGotoFlashDevice.Text = Me.Language.GetString("Welcome.Button.GotoFlashDevice") '"跳转到刷机界面"

        Me.btnCopyDeviceId.Text = Me.Language.GetString("Common.Button.Copy") '"复制"
        Me.lblCopyInfo.Text = Me.Language.GetString("Welcome.Message.CopySucceed") '"复制成功"

        Me.btnCopyDeviceId.Visible = False

        If Me.mIsRecoverMode Then
            Me.lblDeviceId.Text = Me.Language.GetString("Welcome.Label.SerialNumber") & "：" '"序列号："
            Me.lblDeviceIdValue.Text = Me.mDeviceRecovery.SerialNumber
            Me.lblDeviceTypeValue.Text = SummaryInfo.FormatProduct(Me.mDeviceRecovery.ProductType)
            Me.lblProductTypeValue.Text = Me.mDeviceRecovery.ProductType
            Me.lblECIDValue.Text = Me.mDeviceRecovery.ECID

            Me.lblConnectMode.Text = Me.Language.GetString("Welcome.Label.ConnectRecoverMode")     '"设备连接成功（恢复模式）"
            Me.lblDFUTitle.Text = Me.Language.GetString("Welcome.Label.HowtoExitRecoverMode")  '"如何退出恢复模式："
            Me.lblDFUDescription1.Text = Me.Language.GetString("Welcome.Message.ExitRecoverTip1") '"点击下方""退出恢复模式""按钮；"
            Me.lblDFUDescription2.Text = Me.Language.GetString("Welcome.Message.ExitRecoverTip2")  '"保持设备与电脑的连接状态，几秒后设备将退出恢复模式。"
            Me.lblNote.Text = Me.Language.GetString("Welcome.Message.ExitRecoverFailMethod") ' "提示：退出恢复模式后若又自动进入恢复模式，则需要通过刷机来解决该模式！"

            Me.btnOutRecoverMode.Location = New Point(Me.btnOutRecoverMode.Left, Me.lblDeviceId.Bottom + 60)
            Me.btnGotoFlashDevice.Location = New Point(Me.btnOutRecoverMode.Right + 22, Me.btnOutRecoverMode.Top)
            Me.lblNote.Location = New Point(Me.lblNote.Left, Me.btnOutRecoverMode.Bottom + 13)

            Me.btnCopyDeviceId.Visible = True

            Me.btnCopyDeviceId.Location = New Point(Me.lblDeviceIdValue.Right + Me.lblDeviceIdValue.Width, Me.lblDeviceIdValue.Location.Y)


        ElseIf Me.mIsDFUMode Then
            Me.lblDeviceId.Text = ""
            Me.lblDeviceIdValue.Text = ""
            Me.lblDeviceTypeValue.Text = SummaryInfo.FormatProduct(Me.mDeviceDFU.ProductType)
            Me.lblProductTypeValue.Text = Me.mDeviceDFU.ProductType
            Me.lblECIDValue.Text = Me.mDeviceDFU.ECID

            Me.lblConnectMode.Text = Me.Language.GetString("Welcome.Label.ConnectDFUMode")  '"设备连接成功（DFU模式）"
            Me.lblDFUTitle.Text = Me.Language.GetString("Welcome.Label.HowtoExitDFUMode")   '"如何退出DFU模式："
            Me.lblDFUDescription1.Text = Me.Language.GetString("Welcome.Message.ExitDFUTip1") ' "同时按住设备的""Home""键和""开机""键不松开；"
            Me.lblDFUDescription2.Text = Me.Language.GetString("Welcome.Message.ExitDFUTip2")   '"直到屏幕出现白色的苹果画面，即可退出DFU模式。"
            Me.lblNote.Text = Me.Language.GetString("Welcome.Message.ExitDFUFailMethod") '"提示：DFU模式下仍可进行刷机操作，如果手动退出DFU模式失败，则需要通过刷机来解决该模式！"

            Me.btnOutRecoverMode.Location = New Point(Me.btnOutRecoverMode.Left, Me.lblECID.Bottom + 60)
            Me.btnGotoFlashDevice.Location = Me.btnOutRecoverMode.Location
            Me.lblNote.Location = New Point(Me.lblNote.Left, Me.btnOutRecoverMode.Bottom + 13)

        End If


    End Sub

    Private Sub SwithViewInLoadDeviceInfo(ByVal obj As Object)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ParameterizedThreadStart(AddressOf SwithViewInLoadDeviceInfo), obj)
            Else
                Dim isLoading As Boolean = CType(obj, Object)
                Me.btnRename.Enabled = Not isLoading
                Me.btnDetail.Enabled = Not isLoading
                Me.btnDisconnect.Enabled = Not isLoading
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SwithViewInLoadDeviceInfo")
        End Try
    End Sub

    Private Sub LoadDeviceImage()
        Dim strProduct As String = String.Empty
        Dim iPhoneColor As PHBDeviceColorVariation = PHBDeviceColorVariation.Default
        Dim strDeviceColorBg As String = String.Empty
        If Me.mDevice IsNot Nothing Then
            Me.lblNameValue.Text = Me.mDevice.DeviceName
            Me.lblDeviceName.Text = Me.mDevice.DeviceName
            strProduct = Me.mDevice.ProductType
            iPhoneColor = Me.mDevice.DeviceColor
            Me.mDiskSize = 0
            Me.lblDeviceColor.Text = String.Format("{0} {1}", SummaryInfo.FormatProduct(Me.mDevice.ProductType), FormatDeviceColor(Me.mDevice, Me.mDevice.DeviceColor, Me.Language))
            Me.lbliOSVersionValue.Text = Me.GetValue(Me.mDevice.ProductVersion)
            Me.lblSNValue.Text = Me.GetValue(Me.mDevice.SerialNumber)
            Me.lblDeviceIdentifyValue.Text = Me.GetValue(Me.mDevice.Identifier)
            Me.SetDeviceInfoLoading()
            strDeviceColorBg = Me.mDevice.DeviceColorBg

        ElseIf Me.mDeviceDFU IsNot Nothing Then
            strProduct = Me.mDeviceDFU.ProductType
            iPhoneColor = PHBDeviceColorVariation.White '取不到颜色就用白色
        ElseIf Me.mDeviceRecovery IsNot Nothing Then
            strProduct = Me.mDeviceRecovery.ProductType
            iPhoneColor = PHBDeviceColorVariation.White '取不到颜色就用白色
        End If

        Me.GetDeviceImageFromLocal(strProduct, iPhoneColor, strDeviceColorBg)
        Me.pbDevice.Image = Me._infoDevice.DeviceImage
        Me.picDevice.Image = Me._infoDevice.DeviceImage
    End Sub

    Private Sub Start2LoadDeviceInfo(ByVal isOnlyLoadMainInfo As Boolean)
        Try
            Me.LoadDeviceImage()

            Me.lblVersion.Text = Me.Language.GetString("Welcome.Label.Loading") '"正在获取 ..."
            Me.SwithViewInLoadDeviceInfo(True)

            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf LoadDeviceInfo), isOnlyLoadMainInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Start2LoadDeviceInfo")
        End Try
    End Sub

    Private Sub LoadDeviceInfo(ByVal obj As Object)
        Dim isOnlyLoadMainInfo As Boolean = CType(obj, Boolean)
        Dim dt As Date = Now

        Try
            '连接手机的时候取一次UDID属性。
            If Me.mDevice IsNot Nothing Then
                Dim strUdid As String = Me.mDevice.UniqueDeviceID
            End If

            If isOnlyLoadMainInfo Then
                Me.SetMainInfo()
                GoTo DOEXIT
            End If

            Try
                If Me.mDevice IsNot Nothing Then
                    Me.mDevice.mb2_CheckEncrypt()
                End If
            Catch
            End Try

            Me.InitDataSource()
            Me.LoadDetailInfo()
            Me.WriteInfoToDevice(New Object() {False, False})
            Me.SetMainInfo()
            Me.LoadPhoneSaleInfo()
            Me.ShowDevieInfo()
            Try
                '获取设备是否有开启ITUNES备份加密 可能会获取失败  在这边先获取一次 用到的时候在获取  会加快速度
                If Me.mDevice IsNot Nothing Then
                    Dim intTryCount As Integer = 0
                    Dim isCheckEncrypt As Boolean = Me.mDevice.mb2_CheckEncrypt()
                    While Not isCheckEncrypt AndAlso intTryCount < 3
                        Utility.WaitSeconds(1)
                        If Me.mDevice IsNot Nothing Then
                            isCheckEncrypt = Me.mDevice.mb2_CheckEncrypt()
                        End If
                        intTryCount = intTryCount + 1
                    End While
                End If
            Catch ex As Exception
            End Try

            Debug.Print("收集手机信息1：" & Now.Subtract(dt).TotalMilliseconds)
            dt = Now

            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber >= 800 AndAlso AuthorizeByAppleID.Instance(Me.mDevice).CheckTuiInstall() Then
                Dim strIDFA As String = Me.mDevice.IDFA
                Dim intCount As Integer = 0

                '设备刚接入的时候，可能无法取得数据
                While String.IsNullOrEmpty(strIDFA) AndAlso intCount < 3
                    intCount += 1
                    Threading.Thread.Sleep(1000)
                    strIDFA = Me.mDevice.IDFA(True)
                End While

                Me._infoDevice.advertisingIdentifier = Me.mDevice.IDFA
                Me._infoDevice.identifierForVendor = Me.mDevice.IDFV(False)
                '取到的idfa缓存到setting里面，下载的时候如果不存在就取缓存。
                IniSetting.SetLastIDFA(strIDFA)
            ElseIf Me.mDeviceDFU IsNot Nothing Then
                Me._infoDevice.ECID = Me.mDeviceDFU.ECID
            ElseIf Me.mDeviceRecovery IsNot Nothing Then
                Me._infoDevice.ECID = Me.mDeviceRecovery.ECID
            End If

            '收集手机信息
            Me.CollectDevicData(Me._infoDevice)
            Debug.Print("收集手机信息2：" & Now.Subtract(dt).TotalMilliseconds)

            '缓存设备信息到内存
            If Me.mDevice IsNot Nothing Then
                ActionCollectHelper.UpdateDeviceCacheData(Me.mDevice)
                '手机外壳颜色收集
                ActionCollectHelper.GetDeviceHomePageDeviceColor(Me.mDevice, ModelKey.SummaryDetailDeviceColor, Me.mDevice.DeviceColorBg)

                '小于********* iPhone 底层直接提示助手不兼容， 上层只要判断12.3以上的版本还有这种问题即可，出现的概率会比较小。
                If Me.mDevice.VersionNumber >= 1000 AndAlso
                    Me.mDevice.ProductType.Length = 0 AndAlso
                    Me.mDevice.UniqueDeviceID.Length = 0 AndAlso
                    MainForm.mIsShowiTunesUpdate AndAlso
                    iTunesHelper.iTunesVersion >= New Version("*********") Then

                    Dim strMsg As String = Me.Language.GetString("Common.Info.UpdateiTunes") '"发现您的iTunes版本过低，导致助手的很多功能无法正常使用。" & vbCrLf & "建议您点击「确定」，查看教程升级您的iTunes到最新版本。"
                    tbMessageBox.Show(Me.mApplication, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
                    Common.OpenExplorer("http://news.tongbu.com/89562.html")
                    MainForm.mIsShowiTunesUpdate = False
                End If

                Dim strLog As String = String.Format(" Product Type:{0}{3} Version Number:{1}{3} Identify:{2}", Me.mDevice.ProductType, Me.mDevice.VersionNumber, Me.mDevice.UniqueDeviceID, vbCrLf)
                Common.LogException(strLog, "DeviceInfo:")
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "LoadDeviceInfo")
        End Try

        '同步推提示提示用户接助手升级同步推
        Me.DoTuiUpdateCollection()
DOEXIT:
        Me.SwithViewInLoadDeviceInfo(False)
    End Sub

    Private Function GetTuiIdentify(ByVal strSku As String)
        Dim strId As String = strSku
        Dim dict As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.Any)

        For Each Item As String In dict.Keys
            If Item.StartsWith(strId) Then
                strId = Item
                Exit For
            End If
        Next

        Return strId
    End Function

#Region "--- 同步推d.plist文件操作 ---"

    Private Sub WriteInfoToDeviceThread(ByVal blnIsKe As Boolean)
        Try
            Dim thr As New Thread(New ParameterizedThreadStart(AddressOf WriteInfoToDevice))
            With thr
                .IsBackground = True
                .Start(New Object() {blnIsKe, True})
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary_WriteInfoToDeviceThread")
        End Try
    End Sub

    Private Sub WriteInfoToDevice(ByVal paraObj As Object)
        Try
            Dim blnIsKe As Boolean = paraObj(0)
            Dim blnUpdateChannel As Boolean = paraObj(1)
            'udid现在全部设备都写
            'If Me.mDevice Is Nothing OrElse Me.mDevice.VersionNumber < 800 Then
            '    Return
            'End If

            '写入信息
            Dim strWritPathOnPhone As String = "/iTunes_Control/iTunes/Tongbu/d.plist"
            Me.mDevice.CheckFolderByAFC("/iTunes_Control/iTunes/Tongbu/")
            Dim strChannel As String = Me.GetChannel(blnIsKe)
            If Not blnUpdateChannel Then
                Me.mstrChannelFromDevice = Me.GetChannelFromDevice(strWritPathOnPhone)
            Else
                Me.mstrChannelFromDevice = strChannel
                Common.Log(String.Format("Tui_Channel : {0}", Me.mstrChannelFromDevice))
            End If

            Dim dict As New Dictionary(Of Object, Object)
            dict.Add("uniqueIdentifier", Me.mDevice.UniqueDeviceID)
            dict.Add("serialnumber", Me.mDevice.SerialNumber)
            dict.Add("macaddress", Me.mDevice.WiFiAddress)
            dict.Add("channel", Me.mstrChannelFromDevice)

            Dim strWritPathOnPC As String = Folder.GetTempFilePath()
            If iTong.Device.CoreFoundation.CreatePlist(dict, strWritPathOnPC) AndAlso Common.EncryptDESFile(strWritPathOnPC, strWritPathOnPC) Then
                Me.mDevice.CopyToPhoneByAFC(strWritPathOnPC, strWritPathOnPhone)
            End If

            '更新tongbu.plist文件到同步推目录里面。
            TongbuPlist.SetUniqueIdentifier(Me.mDevice, False)
            TongbuPlist.SetSerialNumber(Me.mDevice, False)
            TongbuPlist.SetMACAddress(Me.mDevice, False)
            TongbuPlist.SetTuiChannel(Me.mDevice, Me.mstrChannelFromDevice, True)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary_WriteInfoToDevice")
        End Try
    End Sub

    Private mstrChannelFromDevice As String = ""
    Private Function GetChannelFromDevice(ByVal strWritPathOnPhone As String) As String
        Dim strReturn As String = ""
        Dim strWritPathOnPC As String = Folder.GetTempFilePath()
        If String.IsNullOrEmpty(Me.mstrChannelFromDevice) AndAlso Me.mDevice.DownFromPhoneByAFC(strWritPathOnPhone, strWritPathOnPC) AndAlso Common.DecryptDESFile(strWritPathOnPC, strWritPathOnPC) Then
            Dim dict As New Dictionary(Of Object, Object)
            dict = iTong.Device.CoreFoundation.ReadPlist_managed(strWritPathOnPC)
            If dict IsNot Nothing Then
                If dict.ContainsKey("channel") Then
                    strReturn = dict("channel")
                End If
            End If
        Else
            strReturn = Me.mstrChannelFromDevice
        End If
        Return strReturn
    End Function

    Private Function GetChannel(ByVal blnIsKe As Boolean) As String
        Dim strReturn As String = ""
        Try
            If Me.mDevice.IsIPad Then
                strReturn = IniSetting.GetChanneliPad(blnIsKe)
            Else
                strReturn = IniSetting.GetChanneliPhone(blnIsKe)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary_GetChannel")
        End Try
        Return strReturn
    End Function


#End Region

    '首页展示设备信息
    Private Sub SetMainInfo()
        Try
            '首页展示设备信息
            If Me.mDevice IsNot Nothing Then
                With Me._infoDevice
                    .IsDeviceExist = True

                    '是否越狱
                    If Me.mDevice.Jailbreaked Then
                        .IsJailbreak = True
                        .IsAppSyncInstalled = TuiInstallHelper.CheckAppSyncInstall(Me.mDevice)
                    ElseIf Me.mDevice.InstallCydia Then
                        .IsCydiaInstalled = Me.mDevice.InstallCydia
                    Else
                        .IsJailbreak = False
                        .IsCydiaInstalled = False
                    End If

                    .DeviceName = Me.mDevice.DeviceName
                    .ProductVersion = Me.mDevice.ProductVersion
                    .ProductType = Me.mDevice.ProductType

                    'color ProductType 第一次取的时候可能会取不出来。取两次基本上都可以取成功的。
                    Dim iPhoneColor As PHBDeviceColorVariation = Me.mDevice.DeviceColor
                    If iPhoneColor = PHBDeviceColorVariation.Default Then
                        iPhoneColor = Me.mDevice.DeviceColor
                    End If
                    .DeviceColor = iPhoneColor
                    .DeviceColorBg = Me.mDevice.DeviceColorBg
                End With
            Else
                Me._infoDevice.IsDeviceExist = False
            End If

            '获取生产日期
            If Me._infoDevice.ProductDateString.Length = 0 Then
                If Me.mDevice IsNot Nothing Then
                    Me._infoDevice.ProductDateString = SaleInfoHelper.GetProduceInfo(Me.mDevice.SerialNumber)
                End If
            End If

            '获取设备图片
            Me.DeleteLocalErrorImage(Me._infoDevice.ProductType, Me._infoDevice.DeviceColor, Me._infoDevice.DeviceColorBg)
            Dim strFilePath As String = SummaryInfo.GetDeviceImage(Me._infoDevice.ProductType, Me._infoDevice.DeviceColor, Me._infoDevice.DeviceColorBg)
            Me.GetDeviceImageFromLocal(Me._infoDevice.ProductType, Me._infoDevice.DeviceColor, Me._infoDevice.DeviceColorBg)

            Me.SetMainInfoOnForm()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary.SetMainInfo")

        End Try
    End Sub

    Private Sub SetMainInfoOnForm()
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ThreadStart(AddressOf SetMainInfoOnForm))
        Else
            With Me._infoDevice
                Dim strJail As String = String.Empty
                If .IsDeviceExist Then
                    If .IsJailbreak Then
                        Me.btnGoJailbreakWeb.Visible = False
                        If .IsAppSyncInstalled Then
                            '"已越狱"
                            strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("Welcome.Label.InstalledAppSync"), "")  '"已安装AppSync"
                        Else
                            '"已越狱"
                            strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("Welcome.Label.UnInstallAppSync"), "")  '"未安装AppSync"
                            Me.btnJump.Visible = IIf(Not Folder.LangType = LanguageType.en_US, True, False)
                        End If
                    ElseIf .IsCydiaInstalled Then
                        '"已越狱"
                        strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("File.Label.NoInstallAfc2"), "")           '"未安装afc2补丁"
                        Me.btnGoJailbreakWeb.Visible = True
                    Else
                        strJail = Me.Language.GetString("Welcome.Label.UnJailbreaked")  '"未越狱"z
                    End If

                    Me.lblNameValue.Text = .DeviceName
                    Me.lblDeviceName.Text = .DeviceName
                    Me.lblVersion.Text = String.Format("{0}, {1}, {2}, {5}, {3}：{4}", .ProductVersion, SummaryInfo.FormatProduct(.ProductType), FormatDeviceColor(Me.mDevice, .DeviceColor, Me.Language), Me.Language.GetString("Welcome.Label.ProductionDate"), .ProductDateString, strJail)
                    Me.btnGoJailbreakWeb.Location = New Point(Me.lblVersion.Right + 1, Me.lblVersion.Top + (Me.lblVersion.Height - Me.btnGoJailbreakWeb.Height) / 2)
                Else
                    '概要信息
                    Me.lblNameValue.Text = _infoDevice.DeviceName
                    Me.lblDeviceName.Text = _infoDevice.DeviceName
                    Me.txtNameValue.Text = Me.lblNameValue.Text
                    Me.txtDeviceName.Text = Me.lblDeviceName.Text
                    Me.lblVersion.Text = String.Format("{0}, {1}", _infoDevice.ProductVersion, SummaryInfo.FormatProduct(_infoDevice.ProductType))
                End If

                If .DeviceImage IsNot Nothing Then
                    Me.pbDevice.Image = .DeviceImage
                    Me.picDevice.Image = .DeviceImage
                End If
            End With

            Application.DoEvents()
        End If
    End Sub

    Private Sub GetDeviceImageFromLocal(ByVal productType As String, ByVal iPhoneColor As PHBDeviceColorVariation, ByVal strColor As String)
        Dim strFilePath As String = SummaryInfo.GetIconPathByDevice(productType, iPhoneColor, strColor)

        '    ' 这边写在UI线程中， 会导致卡， 先去掉。add by zsh at 20160327
        'Try
        '    If File.Exists(strFilePath) = False AndAlso Common.NetworkIsAvailable() Then
        '        Folder.CheckFolder(Path.GetDirectoryName(strFilePath))
        '        Dim img As Image = Common.DownloadImage(WebUrl.ResourceScreenShotDevice & Path.GetFileName(strFilePath), 3000)
        '        If img IsNot Nothing Then
        '            Try
        '                If File.Exists(strFilePath) Then
        '                    File.Delete(strFilePath)
        '                End If
        '            Catch
        '            End Try

        '            img.Save(strFilePath, System.Drawing.Imaging.ImageFormat.Png)
        '            img.Dispose()
        '        End If

        '    End If
        'Catch ex As Exception
        '    Common.LogException(ex.ToString(), "GetDeviceImageFromLocal")
        'End Try

        If File.Exists(strFilePath) Then
            Me._infoDevice.DeviceImage = Common.ImageFromFile(strFilePath)
        Else
            Me._infoDevice.DeviceImage = My.Resources.device_default
        End If
    End Sub

    '加载设备所有信息
    Public Sub LoadPhoneInfo()
        Try
            With Me._infoDevice
                .Key = Me.mDevice.SerialNumber
                .startTime = Now
                .mac = Common.GetMacAddress()

                Dim pluginLogin As IPluginLogin = Me.GetService(GetType(IPluginLogin))
                If pluginLogin IsNot Nothing AndAlso String.IsNullOrEmpty(pluginLogin.Uid) = False Then
                    .uid = pluginLogin.Uid
                Else
                    .uid = 0
                End If
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString, "LoadPhoneInfo")
        End Try

        Me.Start2LoadDeviceInfo(False)
    End Sub

    '收集设备信息
    Private Sub CollectDevicData(ByVal info As tbDeviceInfo)
        Try

            If Folder.LangType = LanguageType.en_US Then
                Return
            End If

            Dim tbDeviceCache As tbDeviceInfoCache = tbDeviceInfoCache.GetInstanse()
            tbDeviceCache.AddDeviceCacheData(info)
            tbDeviceCache.Save()

            info = Nothing

            '发送设备数据,以便服务器能及时收到wifimac和serial的对应关系
            ProcForm.Instance().Start2SendDeviceData()

        Catch ex As Exception
            Common.LogException(ex.ToString, "CollectDevicData")
        End Try
    End Sub

#Region "--- 获取设备基本信息 ---"

    Private Sub LoadDetailInfo()
        Try
            If Me.mDevice Is Nothing Then
                Exit Sub
            End If

            With Me._infoDevice

                Me.mDevice.Connect()
                Me.mDevice.StartSession()

                '0 未知 1 越狱 2 未越狱
                If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
                    .JailbreakStatus = "1"
                Else
                    .JailbreakStatus = "2"
                End If

                Dim strValue As String = String.Empty

                strValue = Me.mDevice.DeviceName
                If Not String.IsNullOrEmpty(strValue) Then
                    .DeviceName = strValue
                End If

                strValue = Me.mDevice.ProductVersion
                If Not String.IsNullOrEmpty(strValue) Then
                    .ProductVersion = strValue
                End If

                strValue = Me.mDevice.ProductType
                If Not String.IsNullOrEmpty(strValue) Then
                    .ProductType = strValue
                End If

                strValue = Me.mDevice.DeviceColor
                If Not String.IsNullOrEmpty(strValue) Then
                    .DeviceColor = strValue
                End If
                .DeviceColorBg = Me.mDevice.DeviceColorBg

                Dim dicDeviceInfo As New Dictionary(Of Object, Object)
                Dim objDeviceInfo As Object = Me.mDevice.GetDeviceValue(CStr(Nothing), CStr(Nothing)) '获取设备的所有信息

                If objDeviceInfo Is Nothing Then
                    objDeviceInfo = Me.mDevice.GetDeviceValue(CStr(Nothing), CStr(Nothing))
                End If

                If objDeviceInfo IsNot Nothing AndAlso TypeOf (objDeviceInfo) Is Dictionary(Of Object, Object) Then
                    dicDeviceInfo = CType(objDeviceInfo, Dictionary(Of Object, Object))
                End If

                If objDeviceInfo IsNot Nothing Then
                    Debug.Print(iTong.Device.CoreFoundation.CreatePlistString(objDeviceInfo))
                End If

                '--- 获取刷机相关的数据 -------------------
                Try
                    Dim objImage4Supported As Object = Me.mDevice.GetDeviceValue(Nothing, DeviceInfoKey.Image4Supported)
                    Dim objECID As Object = Me.mDevice.GetDeviceValue(DeviceInfoKey.UniqueChipID)
                    Dim objAPNonce As Object = Me.mDevice.GetDeviceValue(Nothing, DeviceInfoKey.ApNonce)
                    Dim objSEPNonce As Object = Me.mDevice.GetDeviceValue(Nothing, DeviceInfoKey.SEPNonce)
                    Dim objFirmwarePreflightInfo As Dictionary(Of Object, Object) = Me.mDevice.GetDeviceValue(Nothing, DeviceInfoKey.FirmwarePreflightInfo)

                    Dim dictFlash As New Dictionary(Of Object, Object)
                    If objImage4Supported IsNot Nothing Then
                        dictFlash.Add(DeviceInfoKey.Image4Supported.ToString(), objImage4Supported)
                    End If
                    If objECID IsNot Nothing Then
                        dictFlash.Add(DeviceInfoKey.UniqueChipID.ToString(), objECID)
                    End If
                    If objAPNonce IsNot Nothing Then
                        dictFlash.Add(DeviceInfoKey.ApNonce.ToString(), objAPNonce)
                    End If
                    If objSEPNonce IsNot Nothing Then
                        dictFlash.Add(DeviceInfoKey.SEPNonce.ToString(), objSEPNonce)
                    End If
                    If objFirmwarePreflightInfo IsNot Nothing Then
                        dictFlash.Add(DeviceInfoKey.FirmwarePreflightInfo.ToString(), objFirmwarePreflightInfo)
                    End If

                    Dim arrFlash As Byte() = iTong.Device.CoreFoundation.CreatePlistBinaryData(dictFlash)
                    Dim strFlash As String = Convert.ToBase64String(arrFlash, 0, arrFlash.Length)
                    .FlashInfo = strFlash

                Catch ex As Exception
                    Common.LogException(ex.ToString())
                End Try
                '----------------------------------------------

                Me.mDevice.StopSession()
                Me.mDevice.Disconnect()

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.PhoneNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .PhoneNumber = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.SerialNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .SerialNumber = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.UniqueDeviceID)
                If Not String.IsNullOrEmpty(strValue) Then
                    .UniqueDeviceID = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.ModelNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .ModelNumber = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.RegionInfo)
                If Not String.IsNullOrEmpty(strValue) Then
                    .RegionInfo = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.CPUArchitecture)
                If Not String.IsNullOrEmpty(strValue) Then
                    .CPUArchitecture = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.TimeZoneOffsetFromUTC)
                If Not String.IsNullOrEmpty(strValue) Then
                    Dim intHour As Integer = Val(strValue) \ 3600
                    Dim strHour As String = String.Empty
                    If intHour > 0 Then
                        strHour = "+" & intHour.ToString()
                    Else
                        strHour = "-" & intHour.ToString()
                    End If
                    Dim strTempValue As String = String.Format("UTC/GMT {0} {1}", strHour, Me.Language.GetString("Music.Label.Hour"))
                    .TimeZoneOffsetFromUTC = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.Uses24HourClock)
                If Not String.IsNullOrEmpty(strValue) Then
                    .Uses24HourClock = strValue
                End If

                strValue = Me.mDevice.WiFiAddress
                If Not String.IsNullOrEmpty(strValue) Then
                    .WiFiAddress = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BluetoothAddress)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BluetoothAddress = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.InternationalMobileEquipmentIdentity)
                If Not String.IsNullOrEmpty(strValue) Then
                    .InternationalMobileEquipmentIdentity = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.IntegratedCircuitCardIdentity)
                If Not String.IsNullOrEmpty(strValue) Then
                    .IntegratedCircuitCardIdentity = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.InternationalMobileSubscriberIdentity)
                If Not String.IsNullOrEmpty(strValue) Then
                    .InternationalMobileSubscriberIdentity = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.ActivationState)
                If Not String.IsNullOrEmpty(strValue) Then
                    .ActivationState = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.PasswordProtected)
                If Not String.IsNullOrEmpty(strValue) Then
                    .PasswordProtected = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.MLBSerialNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .MLBSerialNumber = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandVersion)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BasebandVersion = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.FirmwareVersion)
                If Not String.IsNullOrEmpty(strValue) Then
                    .FirmwareVersion = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.HardwareModel)
                If Not String.IsNullOrEmpty(strValue) Then
                    .HardwareModel = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.UniqueChipID)
                If Not String.IsNullOrEmpty(strValue) Then
                    .UniqueChipID = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandGoldCertId)
                If String.IsNullOrEmpty(strValue) Then
                    strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandCertId)
                End If
                If Not String.IsNullOrEmpty(strValue) Then
                    .BasebandGoldCertId = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandSerialNumber)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BasebandSerialNumber = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BuildVersion)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BuildVersion = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.DeviceClass)
                If Not String.IsNullOrEmpty(strValue) Then
                    .DeviceClass = strValue
                End If

                strValue = Me.mDevice.GetDeviceValue("com.apple.itunesstored", "AppleID")
                If Not String.IsNullOrEmpty(strValue) Then
                    .AppleID = strValue
                End If

                strValue = Me.mDevice.GetDeviceValue("com.apple.international", "Keyboard")
                If Not String.IsNullOrEmpty(strValue) Then
                    .Keyboard = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandBootloaderVersion)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BasebandBootloaderVersion = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.HardwareModel)
                If Not String.IsNullOrEmpty(strValue) Then
                    .HardwareModel = strValue
                End If

                If dicDeviceInfo IsNot Nothing AndAlso dicDeviceInfo.ContainsKey("MobileEquipmentIdentifier") Then
                    .MobileEquipmentIdentifier = dicDeviceInfo("MobileEquipmentIdentifier")
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.HardwarePlatform)
                If Not String.IsNullOrEmpty(strValue) Then
                    .HardwarePlatform = strValue
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.BasebandStatus)
                If Not String.IsNullOrEmpty(strValue) Then
                    .BasebandStatus = strValue
                End If

                If dicDeviceInfo IsNot Nothing AndAlso dicDeviceInfo.ContainsKey("FusingStatus") Then
                    .FusingStatus = dicDeviceInfo("FusingStatus")
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.SIMStatus)
                If Not String.IsNullOrEmpty(strValue) Then
                    .SIMStatus = strValue
                End If

                If dicDeviceInfo IsNot Nothing AndAlso dicDeviceInfo.ContainsKey("SIMTrayStatus") Then
                    .SIMTrayStatus = dicDeviceInfo("SIMTrayStatus")
                End If

                If dicDeviceInfo IsNot Nothing AndAlso dicDeviceInfo.ContainsKey("kCTPostponementStatus") Then
                    .kCTPostponementStatus = dicDeviceInfo("kCTPostponementStatus")
                End If

                strValue = Me.GetDeviceValue(dicDeviceInfo, DeviceInfoKey.ActivationStateAcknowledged)
                If Not String.IsNullOrEmpty(strValue) Then
                    .ActivationStateAcknowledged = strValue
                End If

                If dicDeviceInfo IsNot Nothing AndAlso dicDeviceInfo.ContainsKey("BrickState") Then
                    .BrickState = dicDeviceInfo("BrickState")
                End If

                If dicDeviceInfo IsNot Nothing AndAlso dicDeviceInfo.ContainsKey("kCTPostponementInfoServiceProvisioningState") Then
                    .kCTPostponementInfoServiceProvisioningState = dicDeviceInfo("kCTPostponementInfoServiceProvisioningState")
                End If
            End With

            Me.LoadDetailInfoOnForm()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary.LoadDetailInfo")
        End Try
    End Sub

    Private Sub LoadDetailInfoOnForm()
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ThreadStart(AddressOf LoadDetailInfoOnForm))
        Else
            'Me.InitDataSource()

            With Me._infoDevice
                If Not String.IsNullOrEmpty(.DeviceName) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.DeviceName") & Me.Language.GetString("Common.Symbol.Colon"), .DeviceName)                '"Device Name:"
                End If

                If Not String.IsNullOrEmpty(.ProductVersion) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.ProductVersion") & Me.Language.GetString("Common.Symbol.Colon"), .ProductVersion)            '"Product Version:"
                End If

                If Not String.IsNullOrEmpty(.ProductType) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.ProductType") & Me.Language.GetString("Common.Symbol.Colon"), String.Format("{0}({1})", SummaryInfo.FormatProduct(.ProductType), .ProductType))    '"Product Type:"
                End If

                If Not String.IsNullOrEmpty(.DeviceColor) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.DeviceColor") & Me.Language.GetString("Common.Symbol.Colon"), FormatDeviceColor(Me.mDevice, .DeviceColor, Me.Language))               '"Device Color:"
                End If

                If Not String.IsNullOrEmpty(.PhoneNumber) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.PhoneNumber") & Me.Language.GetString("Common.Symbol.Colon"), .PhoneNumber)               '"Phone Number:"
                End If

                If Not String.IsNullOrEmpty(.SerialNumber) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.SerialNumber") & Me.Language.GetString("Common.Symbol.Colon"), .SerialNumber)              '"Serial Number:"
                End If

                If Not String.IsNullOrEmpty(.UniqueDeviceID) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.UniqueDeviceId") & Me.Language.GetString("Common.Symbol.Colon"), .UniqueDeviceID)            '"Unique Device ID:"

                    If String.Compare(.UniqueDeviceID, Me.mDevice.Identifier) <> 0 Then
                        Me.mDataSource.Rows.Add("UDID" & Me.Language.GetString("Common.Symbol.Colon"), Me.mDevice.Identifier)            '"Unique Device ID:"
                    End If
                End If

                If Not String.IsNullOrEmpty(.ModelNumber) OrElse Not String.IsNullOrEmpty(.RegionInfo) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.ModelNumber") & Me.Language.GetString("Common.Symbol.Colon"), .ModelNumber & .RegionInfo & "")               '"Model Number:"
                End If

                If Not String.IsNullOrEmpty(.CPUArchitecture) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.CPUArchitecture") & Me.Language.GetString("Common.Symbol.Colon"), .CPUArchitecture)           '"CPU Architecture:"
                End If

                If Not String.IsNullOrEmpty(.TimeZoneOffsetFromUTC) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.TimeZone") & Me.Language.GetString("Common.Symbol.Colon"), .TimeZoneOffsetFromUTC)                  '"Time Zone:"
                End If

                If Not String.IsNullOrEmpty(.TimeZoneOffsetFromUTC) Then
                    Dim intHour As Integer = Val(.TimeZoneOffsetFromUTC) \ 3600
                    Dim strHour As String = String.Empty
                    If intHour > 0 Then
                        strHour = "+" & intHour.ToString()
                    Else
                        strHour = "-" & intHour.ToString()
                    End If
                    Dim strTempValue As String = String.Format("UTC/GMT {0} {1}", strHour, Me.Language.GetString("Music.Label.Hour"))
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.TimeOffset") & Me.Language.GetString("Common.Symbol.Colon"), strTempValue)                '"Time Offset:"
                End If

                If Not String.IsNullOrEmpty(.Uses24HourClock) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.TwentyFourHourTime") & Me.Language.GetString("Common.Symbol.Colon"), .Uses24HourClock)        '"24-Hour Time:"
                End If

                If Not String.IsNullOrEmpty(.WiFiAddress) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.WiFiAddress") & Me.Language.GetString("Common.Symbol.Colon"), .WiFiAddress)               '"Wi-Fi Address:"
                End If

                If Not String.IsNullOrEmpty(.BluetoothAddress) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.Bluetooth") & Me.Language.GetString("Common.Symbol.Colon"), .BluetoothAddress)                 '"Bluetooth:"
                End If

                If Not String.IsNullOrEmpty(.InternationalMobileEquipmentIdentity) Then
                    Me.mDataSource.Rows.Add(String.Format("IMEI{0}", Me.Language.GetString("Common.Symbol.Colon")), .InternationalMobileEquipmentIdentity)
                End If

                If Not String.IsNullOrEmpty(.IntegratedCircuitCardIdentity) Then
                    Me.mDataSource.Rows.Add(String.Format("ICCID{0}", Me.Language.GetString("Common.Symbol.Colon")), .IntegratedCircuitCardIdentity)
                End If

                If Not String.IsNullOrEmpty(.InternationalMobileSubscriberIdentity) Then
                    Me.mDataSource.Rows.Add(String.Format("IMSI{0}", Me.Language.GetString("Common.Symbol.Colon")), .InternationalMobileSubscriberIdentity)
                End If

                If Not String.IsNullOrEmpty(.ActivationState) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.ActivationState") & Me.Language.GetString("Common.Symbol.Colon"), .ActivationState)           '"Activation State:"
                End If

                If Not String.IsNullOrEmpty(.PasswordProtected) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.PasswordProtected") & Me.Language.GetString("Common.Symbol.Colon"), .PasswordProtected)         '"Password Protected:"
                End If

                If Not String.IsNullOrEmpty(.MLBSerialNumber) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.MLBSerialNumber") & Me.Language.GetString("Common.Symbol.Colon"), .MLBSerialNumber)           '"MLB Serial Number:"
                End If

                If Not String.IsNullOrEmpty(.BasebandVersion) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.BasebandVersion") & Me.Language.GetString("Common.Symbol.Colon"), .BasebandVersion)           '"Baseband Version:"
                End If

                If Not String.IsNullOrEmpty(.FirmwareVersion) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.FirmwareVersion") & Me.Language.GetString("Common.Symbol.Colon"), .FirmwareVersion)           '"Firmware Version:"
                End If

                If Not String.IsNullOrEmpty(.HardwareModel) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.HardwareModel") & Me.Language.GetString("Common.Symbol.Colon"), .HardwareModel)             '"Hardware Model:"
                End If

                If Not String.IsNullOrEmpty(.UniqueChipID) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.UniqueChipId") & Me.Language.GetString("Common.Symbol.Colon"), .UniqueChipID)              '"Unique Chip ID:"
                End If

                If Not String.IsNullOrEmpty(.BasebandGoldCertId) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.BasebandGoldCertId") & Me.Language.GetString("Common.Symbol.Colon"), .BasebandGoldCertId)        '"Baseband Gold Cert Id:"
                End If

                If Not String.IsNullOrEmpty(.BasebandSerialNumber) Then
                    Me.mDataSource.Rows.Add(Me.Language.GetString("Welcome.Label.BasebandSerialNumber") & Me.Language.GetString("Common.Symbol.Colon"), .BasebandSerialNumber)      '"Baseband Serial Number:"
                End If

                If Not String.IsNullOrEmpty(.AppleID) Then
                    Dim strPhoneNumber As String = Me.Language.GetString("Welcome.Label.PhoneNumber") & Me.Language.GetString("Common.Symbol.Colon")
                    Dim startIndex As Integer = Me.GetIndexByNameFromDataTable(strPhoneNumber)

                    Dim row As DataRow = Me.CreateNewDataRow(Me.Language.GetString("Welcome.Label.AppleId") & Me.Language.GetString("Common.Symbol.Colon"), .AppleID) '"Apple ID:"
                    startIndex += 1
                    Me.mDataSource.Rows.InsertAt(row, startIndex)
                End If

                If Not String.IsNullOrEmpty(.Keyboard) Then
                    Dim strTwentyFourHourTime As String = Me.Language.GetString("Welcome.Label.TwentyFourHourTime") & Me.Language.GetString("Common.Symbol.Colon")
                    Dim startIndex As Integer = Me.GetIndexByNameFromDataTable(strTwentyFourHourTime)

                    Dim row As DataRow = Me.CreateNewDataRow(Me.Language.GetString("Welcome.Label.Keyboard") & Me.Language.GetString("Common.Symbol.Colon"), .Keyboard)   '"Keyboard:"
                    startIndex += 1
                    Me.mDataSource.Rows.InsertAt(row, startIndex)
                End If
            End With

            Application.DoEvents()
        End If
    End Sub

    Private Function GetDeviceValue(ByVal dicDeviceInfo As Dictionary(Of Object, Object), ByVal key As DeviceInfoKey) As String
        Return Me.GetDeviceValue(dicDeviceInfo, key.ToString())
    End Function

    Private Function GetDeviceValue(ByVal dicDeviceInfo As Dictionary(Of Object, Object), ByVal key As String) As String
        Dim strValue As String = String.Empty

        If dicDeviceInfo Is Nothing Then
            Return strValue
        End If

        If dicDeviceInfo.ContainsKey(key) Then
            Dim objValue As Object = dicDeviceInfo(key)

            If objValue IsNot Nothing Then
                If TypeOf objValue Is Byte() Then
                    strValue = Convert.ToBase64String(objValue)

                Else
                    strValue = objValue.ToString()
                End If
            End If
            'Else
            '    Debug.Print("设备信息不包括：" & key)
        End If

        If String.IsNullOrEmpty(strValue) Then
            Dim objValue As Object = Me.mDevice.GetDeviceValue(key)
            If objValue IsNot Nothing Then
                If TypeOf objValue Is Byte() Then
                    strValue = Convert.ToBase64String(objValue)
                Else
                    strValue = objValue.ToString()
                End If
            End If
        End If

        If String.IsNullOrEmpty(strValue) Then
            Debug.Print("设备信息不包括：" & key)
        End If

        Return strValue
    End Function

#End Region

#Region "--- 获取销售信息 ---"

    '请求获取销售信息
    Private Sub LoadPhoneSaleInfo()
        ''只有国内版助手需要展示销售信息
        'If Not Folder.LangType = LanguageType.zh_CN Then
        '    Return
        'End If

        If Me.mDevice Is Nothing Then
            Exit Sub
        End If

        Try
            With Me._infoDevice
                .Identifier = Me.mDevice.Identifier

                '添加销售地
                Dim strDeviceRegionKey As String = .RegionInfo
                Dim strSalesAreaValue As String = iPhoneDeviceHelper.GetSaleRegion(strDeviceRegionKey)
                If String.IsNullOrEmpty(strSalesAreaValue) Then
                    strSalesAreaValue = Me.Language.GetString("Common.Label.Unknow")          '"未知"
                End If
                .SalesAreaValue = strSalesAreaValue

                '从本地配置中读取
                Dim dicSalesInfo As SalesInfoPara = IniSetting.GetSalesInfo(Me.mDevice.Identifier)

                '从同步服务器取
                If dicSalesInfo Is Nothing AndAlso Folder.LangType = LanguageType.zh_CN Then
                    dicSalesInfo = Me.GetSalesInfoFromServer()
                End If

                '从苹果服务器取  2015-10-30 by chenbihai 苹果服务器获取购买时间 逻辑有变化 
                Dim isFromAppleWeb As Boolean = False
                'If dicSalesInfo Is Nothing OrElse Not Me.CheckSalesInfo(dicSalesInfo) Then
                '    dicSalesInfo = Me.GetSaleInfoFromAppleWeb()
                '    isFromAppleWeb = True
                'End If

                .SalesInfo = dicSalesInfo
                .IsFromAppleWeb = isFromAppleWeb

                Me.LoadPhoneSaleInfoOnForm()

                '提交数据到数据库（只提交从苹果服务器获取到的数据，并且这些数据是有效的）
                If Me._infoDevice.IsFromAppleWeb AndAlso Me.CheckSalesInfo(Me._infoDevice.SalesInfo) Then
                    Me.UploadSalesInfoToServer(Me._infoDevice.SalesInfo, Me._infoDevice.SalesAreaValue)
                End If
            End With
        Catch ex As Exception
            Debug.Print("GetSalesInfoError:" & ex.ToString)
        End Try

    End Sub

    '请求获取销售信息
    Private Sub LoadPhoneSaleInfoOnForm()
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ThreadStart(AddressOf LoadPhoneSaleInfoOnForm))
        Else
            Try
                With Me._infoDevice
                    '添加销售地
                    Dim strDeviceColorName As String = Me.Language.GetString("Welcome.Label.DeviceColor") & Me.Language.GetString("Common.Symbol.Colon")
                    Dim startIndex As Integer = Me.GetIndexByNameFromDataTable(strDeviceColorName)

                    Dim row As DataRow = Me.CreateNewDataRow(Me.Language.GetString("Welcome.Label.SalesArea") & Me.Language.GetString("Common.Symbol.Colon"), .SalesAreaValue)
                    startIndex += 1
                    Me.mDataSource.Rows.InsertAt(row, startIndex)

                    '展示到界面
                    If .SalesInfo IsNot Nothing Then
                        Dim strPurchDateName As String = Me.Language.GetString("Welcome.Label.PurchaseDate") & Me.Language.GetString("Common.Symbol.Colon")
                        Dim strCovEndDateName As String = Me.Language.GetString("Welcome.Label.WarrantyDate") & Me.Language.GetString("Common.Symbol.Colon")

                        '购买日期，没有值显示未知。
                        Dim strPurchaseDateValue As String = .SalesInfo.PurchaseDate
                        If String.IsNullOrEmpty(strPurchaseDateValue) Then
                            strPurchaseDateValue = Me.Language.GetString("Common.Label.Unknow")          '"未知"
                        End If

                        Dim rowPurchaseDate As DataRow = Me.CreateNewDataRow(strPurchDateName, strPurchaseDateValue)
                        startIndex += 1
                        Me.mDataSource.Rows.InsertAt(rowPurchaseDate, startIndex)

                        '过保日期
                        Dim strCovEndDateValue As String = .SalesInfo.CovEndDate
                        If strCovEndDateValue = "2000-01-01" Then
                            strCovEndDateValue = Me.Language.GetString("Welcome.Label.OutWarranty")
                        End If

                        If String.IsNullOrEmpty(strCovEndDateValue) Then
                            strCovEndDateValue = Me.Language.GetString("Common.Label.Unknow")          '"未知"
                        End If

                        Dim rowCovEndDate As DataRow = Me.CreateNewDataRow(strCovEndDateName, strCovEndDateValue)
                        startIndex += 1
                        Me.mDataSource.Rows.InsertAt(rowCovEndDate, startIndex)

                        '写到本地配置
                        IniSetting.SetSalesInfo(.Identifier, .SalesInfo)
                    End If
                End With

                Application.DoEvents()

            Catch ex As Exception
                Debug.Print("GetSalesInfoError:" & ex.ToString)
            End Try
        End If
    End Sub

    Private Function GetSaleInfoFromAppleWeb() As SalesInfoPara
        Dim dicSalesInfo As New SalesInfoPara()
        Dim result As KeyValuePair(Of WarrantyType, Date) = SaleInfoHelper.GetExpireDate(Me.mDevice)
        Try
            Select Case result.Key
                Case WarrantyType.Active     '未过保
                    dicSalesInfo.CovEndDate = result.Value.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDay))                                          '过保日期
                    dicSalesInfo.PurchaseDate = result.Value.AddYears(-1).AddDays(1).ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDay))  '购买日期

                Case WarrantyType.Expire     '已过保
                    dicSalesInfo.CovEndDate = "2000-01-01"       '代表已过期
                    dicSalesInfo.PurchaseDate = ""                       '购买日期

                Case WarrantyType.Unknow  '未知
                    dicSalesInfo = Nothing

            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString, "GetSaleInfoFromAppleWeb")
        End Try
        Return dicSalesInfo
    End Function

    Private Function CheckSalesInfo(ByVal info As SalesInfoPara) As Boolean
        Dim blnReturn As Boolean = True
        If info Is Nothing Then
            blnReturn = False
        Else
            If info.CovEndDate.Length = 0 Then
                blnReturn = False
            End If
        End If
        Return blnReturn
    End Function

    Private Function GetSaleInfoFromApple() As SalesInfoPara
        Dim salesInfo As SalesInfoPara = Nothing
        Try
            Dim strValue As String = Me.mDevice.SerialNumber
            Dim strUrl As String = "https://selfsolve.apple.com/warrantyChecker.do?sn={0}&cb=crossDomainAjax.successResponse"

            '请求苹果接口
            Dim strResult As String = Utility.GetContentStringFromUrl(String.Format(strUrl, strValue), System.Text.Encoding.UTF8, 40000)
            If strResult Is Nothing OrElse strResult.Length = 0 Then
                Return salesInfo
            End If

            '处理返回结果
            Dim strCrossDomain As String = "crossDomainAjax.successResponse("

            If strResult.StartsWith(strCrossDomain) Then
                strResult = strResult.Substring(strCrossDomain.Length, strResult.Length - strCrossDomain.Length)
            End If
            If strResult.EndsWith(")") Then
                strResult = strResult.TrimEnd(")")
            End If

            'json转换返回结果
            Dim objJson As JsonObject = JsonParser.ParseString(strResult)
            If objJson Is Nothing OrElse objJson.Count = 0 Then
                '"苹果服务器返回值为空。"
                Return salesInfo
            End If

            salesInfo = New SalesInfoPara()
            If objJson.ContainsKey("PURCHASE_DATE") Then
                salesInfo.PurchaseDate = CType(objJson.Item("PURCHASE_DATE"), JsonString).Value
            Else
                '取不到数据
                salesInfo.PurchaseDate = ""
            End If

            If objJson.ContainsKey("LAST_UNBRICK_DT") Then
                salesInfo.LastUnbrickDate = CType(objJson.Item("LAST_UNBRICK_DT"), JsonString).Value
            Else
                '取不到数据
                salesInfo.LastUnbrickDate = ""
            End If

            If objJson.ContainsKey("COV_END_DATE") Then
                salesInfo.CovEndDate = CType(objJson.Item("COV_END_DATE"), JsonString).Value
            Else
                '取不到数据
                salesInfo.CovEndDate = ""
            End If

            If objJson.ContainsKey("PURCH_COUNTRY") Then
                salesInfo.PurchCountry = CType(objJson.Item("PURCH_COUNTRY"), JsonString).Value
            Else
                '取不到数据
                salesInfo.PurchCountry = ""
            End If
        Catch ex As Exception
            Debug.Print(String.Format("从Apple官网取得销售地信息失败：{0}", ex.ToString()))
            salesInfo = Nothing
        End Try

        Return salesInfo
    End Function

    Private Sub UploadSalesInfoToServer(ByVal dicSalesInfo As SalesInfoPara, ByVal strSalesArea As String)
        Try
            Dim jsonData As New JsonObject()
            jsonData.Add("SN", Me.mDevice.SerialNumber)
            jsonData.Add("UDID", Me.mDevice.Identifier)
            jsonData.Add("PURCH_COUNTRY", strSalesArea)
            jsonData.Add("PURCHASE_DATE", dicSalesInfo.PurchaseDate)
            jsonData.Add("COV_END_DATE", dicSalesInfo.CovEndDate)

            Dim strData As String = Common.EncryptDES(JsonParser.SaveString(jsonData), Common.RgbKeyString, Common.RgbKeyString)
            strData = String.Format("data={0}", System.Web.HttpUtility.UrlEncode(strData))

            '"http://192.168.40.251/tbzs/api.aspx?type=1"
            Dim strUrl As String = String.Format(WebUrl.PostSalesInfo, "1")
            Dim strWebResult As String = Utility.PostData(strUrl, strData)

            If String.IsNullOrEmpty(strWebResult) Then
                Return
            End If

            'If strWebResult.Length > 0 Then
            '    Debug.Print("提交设备销售信息失败，返回信息为空")
            '    Return
            'End If
            Dim blnResult As Boolean = False
            Dim objJson As JsonObject = JsonParser.ParseString(strWebResult)

            If objJson.ContainsKey("state") Then
                If CType(objJson("state"), JsonString).Value = "1" Then
                    blnResult = True
                End If
            End If

            If Not blnResult Then
                Debug.Print("提交设备销售信息失败：" & strWebResult)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UploadSalesInfoToServer")
        End Try
    End Sub

    Private Function GetSalesInfoFromServer() As SalesInfoPara
        Dim dicSalesInfo As SalesInfoPara = Nothing
        Try
            '1、post数据
            Dim jsonData As New JsonObject()
            jsonData.Add("SN", Me.mDevice.SerialNumber)
            jsonData.Add("UDID", Me.mDevice.Identifier)

            Dim strData As String = Common.EncryptDES(JsonParser.SaveString(jsonData), Common.RgbKeyString, Common.RgbKeyString)
            strData = String.Format("data={0}", System.Web.HttpUtility.UrlEncode(strData))

            '"http://192.168.40.251/tbzs/api.aspx?type=2"
            Dim strUrl As String = String.Format(WebUrl.PostSalesInfo, "2")
            Dim strWebResult As String = Utility.PostData(strUrl, strData)

            If String.IsNullOrEmpty(strWebResult) Then
                GoTo DO_EXIT
            End If

            '2、分析服务器返回值
            Dim blnResult As Boolean = False
            Dim objJson As JsonObject = JsonParser.ParseString(strWebResult)

            If objJson.ContainsKey("state") Then
                If CType(objJson("state"), JsonString).Value = "1" Then
                    blnResult = True
                End If
            End If

            If Not blnResult Then
                Debug.Print("获取设备销售信息失败：" & strWebResult)
                GoTo DO_EXIT
            End If

            If objJson.ContainsKey("data") Then
                Dim dataJson As JsonObject = objJson("data")
                dicSalesInfo = New SalesInfoPara()

                If dataJson.ContainsKey("PURCH_COUNTRY") Then
                    dicSalesInfo.PurchCountry = CType(dataJson("PURCH_COUNTRY"), JsonString)
                End If

                If dataJson.ContainsKey("PURCHASE_DATE") Then
                    dicSalesInfo.PurchaseDate = CType(dataJson("PURCHASE_DATE"), JsonString)
                End If

                If dataJson.ContainsKey("COV_END_DATE") Then
                    dicSalesInfo.CovEndDate = CType(dataJson("COV_END_DATE"), JsonString)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSalesInfoFromServer")
        End Try

DO_EXIT:
        Return dicSalesInfo
    End Function

#End Region

#Region "--- 获取设备容量 ---"

    'OnCapacityCallback：每一项单独回调。（早期获取容量的方式）
    Private Sub OnCapacityCallback(ByVal sender As Object, ByVal e As DeviceCapacityArgs)
        '偶尔在拔插设备的时候会出现崩溃， 目前还未发现原因，暂时捕获异常处理
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of DeviceCapacityArgs)(AddressOf OnCapacityCallback), sender, e)
            Else
                Me.ShowCapacityForCallback(e)


            End If
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

    'OnCapacityLoadCompleted：回调一次，将结果都返回。（新的获取容量的方式）
    Private Sub OnCapacityLoadCompleted(ByVal sender As Object, ByVal e As DeviceCapacityArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of DeviceCapacityArgs)(AddressOf OnCapacityLoadCompleted), sender, e)
        Else
            If Me.mDevice IsNot Nothing Then

                Me.ShowCapacityForLoadCompleted(e)

                '容量加载不成功就等待0.5秒后再重新加载一次。
                If Me.mblnCapacityAutoReload = False AndAlso Not Me.CapacityLoadSucceed() Then
                    Utility.WaitSeconds(0.5)
                    Me.mCapacityShow.LoadCapacity()
                End If
                Me.mblnCapacityAutoReload = True

                Me.btnRefresh.Visible = True
                Me.picLoading.Visible = False
                Me.btnRefreshDeviceData.Visible = True
                Me.picLoadingDeviceData.Visible = False
            End If
        End If
    End Sub

    '容量出现负数的时候可以肯定读取失败。
    Private Function CapacityLoadSucceed() As Boolean
        Dim blnReturn As Boolean = True
        If Me.tbCapacity.AvailableCapacity.Capacity < 0 OrElse Me.tbCapacity.TotalCapacity.Capacity <= 0 OrElse
           Me.tbCapacity.AudioCapacity.Capacity < 0 OrElse Me.tbCapacity.VedioCapacity.Capacity < 0 OrElse
           Me.tbCapacity.CameraCapacity.Capacity < 0 OrElse Me.tbCapacity.AppCapacity.Capacity < 0 OrElse
           Me.tbCapacity.EBookCapacity.Capacity < 0 Then

            blnReturn = False
        End If

        Return blnReturn
    End Function

    Private Sub ShowCapacityForCallback(ByVal deviceCapacity As DeviceCapacityArgs)
        If deviceCapacity Is Nothing Then
            Return
        End If
        Dim lngDisk As Long = 0
        Me.tbCapacity.BeginUpdate()
        Me.tbDevCapacity.BeginUpdate()

        Try
            Dim list As New List(Of KeyValuePair(Of DeviceCapacityType, Long))
            For Each pair As KeyValuePair(Of DeviceCapacityType, Long) In deviceCapacity.ListCapacityType
                list.Add(pair)
            Next

            For Each pair As KeyValuePair(Of DeviceCapacityType, Long) In list
                'Debug.Print(pair.Key.ToString() & vbTab & Utility.FormatFileSize(pair.Value))
                Select Case pair.Key
                    Case DeviceCapacityType.TotalDiskCapacity
                        lngDisk = pair.Value
                    Case DeviceCapacityType.TotalDataCapacity
                        Me.tbCapacity.TotalCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.TotalCapacity.Capacity = pair.Value
                        '收集手机的总容量
                        ActionCollectHelper.GetDeviceHomePageDeviceColor(Me.mDevice, ModelKey.SummaryDetailDeviceSpace, Me.tbCapacity.TotalCapacity.CapacityText)

                    Case DeviceCapacityType.TotalDataAvailable
                        Me.tbCapacity.AvailableCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.AvailableCapacity.Capacity = pair.Value
                        frmSummary.DeviceFreeSize = pair.Value

                    Case DeviceCapacityType.AudioCapacity
                        Me.tbCapacity.AudioCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.AudioCapacity.Capacity = pair.Value

                    Case DeviceCapacityType.VideoCapacity
                        Me.tbCapacity.VedioCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.VedioCapacity.Capacity = pair.Value

                    Case DeviceCapacityType.CameraCapacity
                        Me.tbCapacity.CameraCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.CameraCapacity.Capacity = pair.Value
                        'If pair.Value >= 0 Then
                        '    Me.btnRefresh.Visible = True
                        '    Me.picLoading.Visible = False
                        'End If

                    Case DeviceCapacityType.MobileApplicationUsage
                        Me.tbCapacity.AppCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.AppCapacity.Capacity = pair.Value

                    Case DeviceCapacityType.EBookCapacity
                        Me.tbCapacity.EBookCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.EBookCapacity.Capacity = pair.Value

                        'Case DeviceCapacityType.OtherCapacity
                        '    Me.tbCapacity.OtherCapacity.Capacity = pair.Value

                End Select
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowCapacityForCallback")
        End Try

        Me.tbCapacity.EndUpdate()
        Me.tbDevCapacity.EndUpdate()

        Dim intSize As Integer = Math.Ceiling(lngDisk / (1000 * 1000 * 1000))
        If intSize > 0 Then
            Me.mDiskSize = intSize
            Me.ShowDevieInfoJail(Me.mDiskSize)
        End If
    End Sub

    Private Sub ShowCapacityForLoadCompleted(ByVal deviceCapacity As DeviceCapacityArgs)
        If deviceCapacity Is Nothing Then
            Return
        End If

        Me.tbCapacity.BeginUpdate()
        Me.tbDevCapacity.BeginUpdate()

        Me.ClearCapacity()

        Try
            Dim list As New List(Of KeyValuePair(Of DeviceCapacityType, Long))
            For Each pair As KeyValuePair(Of DeviceCapacityType, Long) In deviceCapacity.ListCapacityType
                list.Add(pair)
            Next

            Dim longAudioCapacity As Long = 0      '包括音乐、铃声、Podcast、有声读物、语音备忘录
            Dim longCameraCapacity As Long = 0      '包括照相机和图库
            Dim longVideoCapacity As Long = 0         '包括视频、音乐视频、TV Show、Podcast视频、iTunesU

            For Each pair As KeyValuePair(Of DeviceCapacityType, Long) In list
                'Debug.Print(pair.Key.ToString() & vbTab & Utility.FormatFileSize(pair.Value))
                Select Case pair.Key
                    Case DeviceCapacityType.TotalDataCapacity
                        Me.tbCapacity.TotalCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.TotalCapacity.Capacity = pair.Value

                    Case DeviceCapacityType.TotalDataAvailable
                        Me.tbCapacity.AvailableCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.AvailableCapacity.Capacity = pair.Value
                        frmSummary.DeviceFreeSize = pair.Value

                    Case DeviceCapacityType.AudioCapacity,
                            DeviceCapacityType.RingtoneCapacity,
                            DeviceCapacityType.PodcastCapacity,
                            DeviceCapacityType.AudiobookCapacity,
                            DeviceCapacityType.VoiceMemoCapacity

                        longAudioCapacity += pair.Value

                    Case DeviceCapacityType.VideoCapacity,
                            DeviceCapacityType.MusicVideoCapacity,
                            DeviceCapacityType.TVEpisodeCapacity,
                            DeviceCapacityType.VideoPodcastCapacity,
                            DeviceCapacityType.iTunesUVideoCapacity

                        longVideoCapacity += pair.Value

                    Case DeviceCapacityType.CameraCapacity,
                            DeviceCapacityType.PhotoCapacity

                        longCameraCapacity += pair.Value

                    Case DeviceCapacityType.MobileApplicationUsage
                        Me.tbCapacity.AppCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.AppCapacity.Capacity = pair.Value

                    Case DeviceCapacityType.EBookCapacity
                        Me.tbCapacity.EBookCapacity.Capacity = pair.Value
                        Me.tbDevCapacity.EBookCapacity.Capacity = pair.Value
                End Select
            Next

            Me.tbCapacity.AudioCapacity.Capacity = longAudioCapacity
            Me.tbCapacity.CameraCapacity.Capacity = longCameraCapacity
            Me.tbCapacity.VedioCapacity.Capacity = longVideoCapacity

            Me.tbDevCapacity.AudioCapacity.Capacity = longAudioCapacity
            Me.tbDevCapacity.CameraCapacity.Capacity = longCameraCapacity
            Me.tbDevCapacity.VedioCapacity.Capacity = longVideoCapacity

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowCapacityForLoadCompleted")
        End Try

        Me.tbCapacity.EndUpdate()
        Me.tbDevCapacity.EndUpdate()
    End Sub

    Private Sub ClearCapacity()
        Me.tbCapacity.TotalCapacity.Capacity = 0
        Me.tbCapacity.AudioCapacity.Capacity = 0
        Me.tbCapacity.VedioCapacity.Capacity = 0
        Me.tbCapacity.CameraCapacity.Capacity = 0
        Me.tbCapacity.AppCapacity.Capacity = 0
        Me.tbCapacity.EBookCapacity.Capacity = 0
        Me.tbCapacity.AvailableCapacity.Capacity = 0

        Me.tbDevCapacity.TotalCapacity.Capacity = 0
        Me.tbDevCapacity.AudioCapacity.Capacity = 0
        Me.tbDevCapacity.VedioCapacity.Capacity = 0
        Me.tbDevCapacity.CameraCapacity.Capacity = 0
        Me.tbDevCapacity.AppCapacity.Capacity = 0
        Me.tbDevCapacity.EBookCapacity.Capacity = 0
        Me.tbDevCapacity.AvailableCapacity.Capacity = 0
    End Sub

#End Region

#Region "--- 初始化DataTable ---"

    Private Sub InitDataSource()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf InitDataSource))
        Else
            If mDataSource Is Nothing Then
                mDataSource = New DataTable()
                With mDataSource
                    .Columns.Add("Key", GetType(String))
                    .Columns.Add("Value", GetType(String))
                End With
            Else
                mDataSource.Rows.Clear()
            End If
        End If
    End Sub

    '根据name获取其index
    Private Function GetIndexByNameFromDataTable(ByVal name As String) As Integer
        Dim startIndex As Integer = 0

        Try
            For index As Integer = 0 To Me.mDataSource.Rows.Count - 1
                If String.Compare(Me.mDataSource.Rows(index).Item(0).ToString(), name, True) = 0 Then
                    startIndex = index
                    Exit For
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetStartIndexFromDataTable")
        End Try

        Return startIndex
    End Function

    Private Function CreateNewDataRow(ByVal name As String, ByVal value As String) As DataRow
        Dim row As DataRow = Me.mDataSource.NewRow()
        row(0) = name
        row(1) = value

        Return row
    End Function

#End Region

#Region "--- 获取设备图片 ---"

    '删除本地错误的图片（之前服务器部署的有问题图片）
    Private Sub DeleteLocalErrorImage(ByVal strProduct As String, ByVal iPhoneColor As PHBDeviceColorVariation, ByVal strColor As String)
        Try
            Dim strImagePath As String = SummaryInfo.GetIconPathByDevice(strProduct, iPhoneColor, strColor)
            Dim strImageName As String = Path.GetFileName(strImagePath)
            Dim strHash As String = ""
            Dim blnDelete As Boolean = False

            If Not File.Exists(strImagePath) Then
                Return
            End If

            Select Case strImageName
                Case "iphone5s_black.png"
                    strHash = Common.GetMd5Base64FromFile(strImagePath)
                    If String.Compare(strHash, "Ptw5ugGdNyqJmympbURvFw==") = 0 Then
                        blnDelete = True
                    End If

                Case "iphone5s_white.png"
                    strHash = Common.GetMd5Base64FromFile(strImagePath)
                    If String.Compare(strHash, "URowrh04V4I/xX91OCzwPg==") = 0 Then
                        blnDelete = True
                    End If

                Case "iphone5s_gold.png"
                    strHash = Common.GetMd5Base64FromFile(strImagePath)
                    If String.Compare(strHash, "0ZgK6xsoW4YU0zajG/Uwsg==") = 0 Then
                        blnDelete = True
                    End If
            End Select

            If blnDelete Then
                File.Delete(strImagePath)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeleteLocalErrorImage")
        End Try
    End Sub

#End Region

#End Region

#Region "--- 修改设备名称 ---"

    Private Sub btnRename_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRename.Click
        Me.lblNameValue.Visible = False
        Me.btnRename.Visible = False
        Me.btnDetail.Visible = False
        Me.btnDisconnect.Visible = False

        Me.pnlChangeDeviceName.Visible = True
        Me.pnlChangeDeviceName.Size = New Size(Me.lblNameValue.Width + 30, Me.pnlChangeDeviceName.Height)
        Me.pnlChangeDeviceName.Location = New Point(Me.lblNameValue.Left + 2, Me.lblNameValue.Bottom - pnlChangeDeviceName.Height + 5)

        Me.txtNameValue.Enabled = True
        Me.txtNameValue.ReadOnly = False
        Me.txtNameValue.Text = ""
        Me.txtNameValue.Text = Me.lblNameValue.Text.ToString()
        Me.txtNameValue.Focus()
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryReName)
    End Sub

    Private Sub txtNameValue_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtNameValue.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.ChangeDeviceName()
        End If
    End Sub

    Private Sub ChangeDeviceName(Optional ByVal IsCancel As Boolean = False)
        If Not Me.txtNameValue.Visible Then
            Return
        End If

        Debug.Print(Me.txtNameValue.Text & vbTab & Me.txtNameValue.Text.Length)

        Dim strText As String = Me.mDevice.DeviceName
        If Not IsCancel Then
            strText = Me.txtNameValue.Text.Trim()
            If strText.Length > 0 AndAlso strText <> Me.lblNameValue.Text Then
                Me.mDevice.DeviceName = strText

                Dim frm As MainForm = Me.ParentForm.ParentForm
                frm.UpdateTopDeviceButtonText(Me.mDevice.DeviceID.ToLower(), strText)
            End If
        End If

        Me.lblNameValue.Visible = True
        Me.btnRename.Visible = True
        Me.btnDetail.Visible = True
        Me.btnDisconnect.Visible = True
        Me.pnlChangeDeviceName.Visible = False


        CType(Me.mApplication, MainForm).Text = Me.Language.GetString("Main.Text.Name") & String.Format(" - {0}", strText)
    End Sub

    Private Sub pnlChangeDeviceName_Validating(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles pnlChangeDeviceName.Validating
        If Not Me.pnlChangeDeviceName.Visible Then
            Return
        End If

        Me.ChangeDeviceName()
    End Sub

#End Region

#Region "--- 截屏插件处理事件 ---"

    Private mblnCloseImgView As Boolean = False
    Private frmImageView As tbImageViewForm = Nothing
    Private mThreadInstallPlugin As Thread = Nothing
    Private mClientType As ClientType = ClientType.Sina

    '截图
    Private mfrmScreenshotPreview As frmPreview = Nothing
    Private mThreadGetScreenshotForPreview As Thread

#Region "--- 视频播放 ---"

    Public Sub frmImageView_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs)
        Me.mblnCloseImgView = True
    End Sub

    Public Sub frmImageView_WeiboClick(ByVal sender As Object, ByVal e As ImageEventArgs)
        Me.ShareScreenshotToWeiBo(e.Tag, e.Image, Nothing)
    End Sub

    Public Sub frmImageView_LightDescClose(ByVal sender As Object, ByVal e As EventArgs)
        IniSetting.SetLightShowDescription(False)
    End Sub

    Private Sub GetScreenshotPlayThread()
        Try
            While True
                If Me.mblnCloseImgView = True Then
                    Exit While
                End If

                If Me.mDevice Is Nothing Then
                    Return
                End If

                Dim imgTemp As Image = Me.mDevice.GetScreenshotImage()
                If imgTemp IsNot Nothing Then
                    Me.SetImageForScreenshot(imgTemp)
                Else
                    Me.mDevice.CloseScreenshot()
                    Utility.WaitSeconds(1)
                End If
            End While
        Catch ex As Exception

        End Try
    End Sub

    '大屏时时播放设备上的图片
    Private Sub GetScreenshotPlay()

        If Not Me.mActivatedDevice Then
            '"您的设备还没有激活，请激活后再尝试使用此功能！"
            MessageBox.Show(Me.Language.GetString("Welcome.Message.DeviceUnactivated"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        '用启动服务的方式判断是否挂载截屏插件
        If Me.StartScreenshotServer() Then
            Me.SetMainFormStatusMessage("")
            '通览的时候先显示窗体再取图片
            If frmImageView IsNot Nothing AndAlso frmImageView.Visible Then
                frmImageView.WindowState = FormWindowState.Normal
                frmImageView.BringToFront()
                Return
            End If
            frmImageView = New tbImageViewForm()
            frmImageView.Icon = My.Resources.iTong

            RemoveHandler frmImageView.FormClosed, AddressOf frmImageView_FormClosed
            AddHandler frmImageView.FormClosed, AddressOf frmImageView_FormClosed
            RemoveHandler frmImageView.WeiboClick, AddressOf frmImageView_WeiboClick
            AddHandler frmImageView.WeiboClick, AddressOf frmImageView_WeiboClick
            RemoveHandler frmImageView.LightDescClose, AddressOf frmImageView_LightDescClose
            AddHandler frmImageView.LightDescClose, AddressOf frmImageView_LightDescClose


            frmImageView.ShowListView = False
            frmImageView.ShowPath = False
            frmImageView.ViewType = ViewType.Screenshot
            frmImageView.Text = Me.Language.GetString("Welcome.Text.ScreenshotTitle")                       '"实时桌面"
            frmImageView.ShowNormalButton = True
            'frmImageView.HideButton = ToolBarButtonType.WeiBo2
            frmImageView.ShowDescription = IniSetting.GetLightShowDescription
            frmImageView.Show()

            Me.mblnCloseImgView = False
            If Me.mThreadSreenshot IsNot Nothing AndAlso Me.mThreadSreenshot.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadSreenshot.Abort()
                Catch ex As Exception
                End Try
            End If

            Me.mThreadSreenshot = New Thread(AddressOf GetScreenshotPlayThread)
            With Me.mThreadSreenshot
                .IsBackground = True
                .Start()
            End With
        Else
            Me.DownloadScreenshotPlugin()

        End If
    End Sub

    Private Sub btnScreenshotPlay_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScreenshotPlay.Click, btnDeviceDesktop.Click
        Me.mbtnLastClick = sender
        Me.SetScreenshotEnable(False)
        Try
            Me.GetScreenshotPlay()
        Catch ex As Exception
            Common.LogException("Screenshot play Exception" & ex.ToString)
        End Try
        Me.SetScreenshotEnable(True)
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryScreenShort)
    End Sub

#End Region

#Region "--- 截图 ---"

    Private Sub GetShellThread(ByVal state As Object)
        Try
            Dim strShellPath As String = state(0)
            Dim strShells As String = state(1)

            '"http://t.tongbu.com/shell/"
            Dim strUrl As String = WebUrl.ResourceScreenShotShell
            If strShells = "Bangs" Then
                '下载留海的地址
                '"http://t.tongbu.com/bangs/"
                strUrl = WebUrl.ResourceScreenShotBangs
            End If

            Dim imgShell As Image = Common.DownloadImage(strUrl & Path.GetFileName(strShellPath), 3000)
            If imgShell IsNot Nothing Then
                imgShell.Save(strShellPath, Imaging.ImageFormat.Png)
                imgShell.Dispose()
            End If
        Catch ex As Exception
        End Try
    End Sub


    '取得外壳
    Private Function GetShell(ByVal strProductType As String, Optional ByVal strFolder As String = "ShellEx") As Image
        Dim imgShell As Image = Nothing

        Try
            'Dim dInfo As tbDeviceInfo = tbDeviceCache.GetInstanse().GetLastDeviceInfo(Me.mDevice.SerialNumber)
            Dim iPhoneColor As PHBDeviceColorVariation = Me.mDevice.DeviceColor
            If iPhoneColor = PHBDeviceColorVariation.Default Then
                iPhoneColor = Me.mDevice.DeviceColor
            End If

            Dim strShellFileName As String = SummaryInfo.GetIconNameByDevice(Me.mDevice.ProductType, iPhoneColor, Me.mDevice.DeviceColorBg)
            Dim strShellFilePath As String = Folder.CacheFolder & strFolder

            Folder.CheckFolder(strShellFilePath)
            strShellFilePath = Path.Combine(strShellFilePath, strShellFileName)

            If Not File.Exists(strShellFilePath) AndAlso Common.NetworkIsAvailable() Then
                '下载图片
                Dim thd As New Thread(AddressOf GetShellThread)
                thd.IsBackground = True
                thd.Start(New Object() {strShellFilePath, strFolder})

                Dim dt As Date = Now
                Do
                    Utility.WaitSeconds(0.2)
                Loop While thd.ThreadState <> ThreadState.Stopped AndAlso New TimeSpan(Now.Ticks - dt.Ticks).TotalSeconds < 3
            End If

            If File.Exists(strShellFilePath) Then
                imgShell = Common.ImageFromFile(strShellFilePath)
            End If

        Catch ex As Exception
        End Try

        Return imgShell
    End Function

    '取得带壳的iPhone图标
    Private Function GetScreenshotShell(ByVal imgSource As Image) As Image
        Dim imgShell As Image = imgSource

        If IniSetting.GetScreenshotShell() Then
            imgShell = Me.GetShell(Me.mDevice.ProductType)
            If imgShell Is Nothing Then
                Return imgSource
            End If

            ''iPad3就把大小缩放不iPad2的大小
            'If Me.mDevice.ProductType.ToLower.StartsWith("ipad3") Then
            '    imgSource = Utility.GetThumbnail(imgSource, New Size(imgSource.Width / 2, imgSource.Height / 2))
            'End If

            If imgSource.Width > imgSource.Height Then
                imgShell.RotateFlip(RotateFlipType.Rotate270FlipNone)
            End If

            Dim imgTemp As New Bitmap(imgShell.Width, imgShell.Height)

            Dim g As Graphics = Graphics.FromImage(imgTemp)
            '把背景填充白色
            g.Clear(Color.White)
            g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBilinear
            '画外框
            g.DrawImage(imgShell, New Rectangle(0, 0, imgShell.Width, imgShell.Height), New Rectangle(0, 0, imgShell.Width, imgShell.Height), GraphicsUnit.Pixel)
            '画图片
            Dim imgSize As Size = SummaryInfo.GetDeviceShellSize(Me.mDevice.ProductType, imgSource)
            g.DrawImage(imgSource, New Rectangle((imgShell.Width - imgSize.Width) \ 2, (imgShell.Height - imgSize.Height) \ 2, imgSize.Width, imgSize.Height), New Rectangle(0, 0, imgSource.Width, imgSource.Height), GraphicsUnit.Pixel)

            '画留海
            If SummaryInfo.CheckIsLiuHaiScreen(Me.mDevice.ProductType, imgSource.Size) Then
                Dim imgBangs As Image = Me.GetShell(Me.mDevice.ProductType, "Bangs")
                If imgBangs IsNot Nothing Then
                    If imgSource.Width > imgSource.Height Then
                        imgBangs.RotateFlip(RotateFlipType.Rotate270FlipNone)
                    End If
                    g.DrawImage(imgBangs, New Rectangle((imgShell.Width - imgSize.Width) \ 2, (imgShell.Height - imgSize.Height) \ 2, imgSize.Width, imgSize.Height), New Rectangle(0, 0, imgBangs.Width, imgBangs.Height), GraphicsUnit.Pixel)
                End If
            End If


            g.Dispose()
            imgShell = imgTemp

        End If

        Return imgShell
    End Function

    'iPhone6界面有两种模式，标准和放大 
    Private Function GetImageSize(ByVal imgSource As Image) As Size
        Dim sizeReturn As Size = imgSource.Size

        If Me.mDevice.ProductType.ToLower.StartsWith("iphone7,1") OrElse
           Me.mDevice.ProductType.ToLower.StartsWith("iphone8,2") OrElse
           Me.mDevice.ProductType.ToLower.StartsWith("iphone9,2") OrElse
           Me.mDevice.ProductType.ToLower.StartsWith("iphone9,3") OrElse
           Me.mDevice.ProductType.ToLower.StartsWith("iphone10,2") OrElse
           Me.mDevice.ProductType.ToLower.StartsWith("iphone10,5") Then
            'iphone6 +
            If sizeReturn.Width > sizeReturn.Height Then
                sizeReturn = New Size(2208, 1242)
            Else
                sizeReturn = New Size(1242, 2208)
            End If

        ElseIf Me.mDevice.ProductType.ToLower.StartsWith("iphone7,2") OrElse
               Me.mDevice.ProductType.ToLower.StartsWith("iphone8,1") OrElse
               Me.mDevice.ProductType.ToLower.StartsWith("iphone9,1") OrElse
               Me.mDevice.ProductType.ToLower.StartsWith("iphone9,3") OrElse
               Me.mDevice.ProductType.ToLower.StartsWith("iphone10,1") OrElse
               Me.mDevice.ProductType.ToLower.StartsWith("iphone10,4") Then
            'iPhone
            If sizeReturn.Width > sizeReturn.Height Then
                sizeReturn = New Size(1136, 640)
            Else
                sizeReturn = New Size(640, 1136)
            End If

        ElseIf Me.mDevice.ProductType.ToLower.StartsWith("iphone10,3") OrElse
                Me.mDevice.ProductType.ToLower.StartsWith("iphone10,6") OrElse
                Me.mDevice.ProductType.ToLower.StartsWith("iphone11,2") OrElse
                Me.mDevice.ProductType.ToLower.StartsWith("iphone11,4") OrElse
                Me.mDevice.ProductType.ToLower.StartsWith("iphone11,6") OrElse
                Me.mDevice.ProductType.ToLower.StartsWith("iphone11,8") Then

            'iPhoneX  iPhoneXs iPhoneXr iPhoneXs max
            If sizeReturn.Width > sizeReturn.Height Then
                sizeReturn = New Size(896, 414)
            Else
                sizeReturn = New Size(414, 896)
            End If

        ElseIf Me.mDevice.ProductType.ToLower.StartsWith("ipad6") Then
            'ipad6
            If sizeReturn.Width > sizeReturn.Height Then
                sizeReturn = New Size(2048, 1536)
            Else
                sizeReturn = New Size(1536, 2048)
            End If

        ElseIf Me.mDevice.ProductType.ToLower.StartsWith("ipad8") Then
            'iPad Pro
            If sizeReturn.Width > sizeReturn.Height Then
                sizeReturn = New Size(2388, 1668)
            Else
                sizeReturn = New Size(1668, 2388)
            End If

        End If
        Return sizeReturn
    End Function

    '截屏处理事件
    Private Sub GetScreenshotNormal()
        If Not Me.mActivatedDevice Then
            '您的设备还没有激活，请激活后再尝试使用此功能！
            MessageBox.Show(Me.Language.GetString("Welcome.Message.DeviceUnactivated"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        '用启动服务的方式判断是否挂载截屏插件
        If Me.StartScreenshotServer() Then
            Me.SetMainFormStatusMessage("")
            Me.mfrmScreenshotPreview = New frmPreview(Me.mApplication)

            RemoveHandler Me.mfrmScreenshotPreview.ReGetScreenshot, AddressOf ReGetScreenshot_Click
            AddHandler Me.mfrmScreenshotPreview.ReGetScreenshot, AddressOf ReGetScreenshot_Click

            '线程去取截屏图片，先显示窗体。
            If Me.mThreadGetScreenshotForPreview IsNot Nothing AndAlso Me.mThreadGetScreenshotForPreview.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadGetScreenshotForPreview.Abort()
                Catch ex As Exception
                End Try
            End If

            Me.mThreadGetScreenshotForPreview = New Threading.Thread(New Threading.ParameterizedThreadStart(AddressOf GetScreenshotThread))
            With Me.mThreadGetScreenshotForPreview
                .IsBackground = True
                .Start(Me.mfrmScreenshotPreview)
            End With

            Me.mfrmScreenshotPreview.ShowDialog(Me.mApplication)
            Me.mfrmScreenshotPreview = Nothing
            Me.Activate()

        Else
            Me.DownloadScreenshotPlugin()

        End If
    End Sub

    Private Sub GetScreenshotThread(ByVal frmObj As Object)
        Try
            If frmObj Is Nothing OrElse Not (TypeOf frmObj Is frmPreview) Then
                Return
            End If

            Dim frm As frmPreview = frmObj
            Dim img As Image = Nothing

            frm.SetLabelMessageStatus(False, False)
            frm.SetPanelLoading(True)
            img = Me.GetGetScreenshot()
            frm.SetPanelLoading(False)

            If img Is Nothing Then
                frm.SetPictureBoxImage(Nothing)
                Return
            End If

            Dim blnShowLabel As Boolean = Me.CheckPicBlack(img)

            '这个代码必须放在 “CheckPicBlack” 之后，因为判断是不是黑色的图片得在取得外壳之前判断。
            img = Me.GetScreenshotShell(img)
            frm.SetPictureBoxImage(img)

            If blnShowLabel Then
                Dim is_iPhone As Boolean = Not Me.mDevice.ProductType.ToLower().Contains("ipad")
                frm.SetLabelMessageStatus(True, is_iPhone)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub ReGetScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        If sender IsNot Nothing AndAlso TypeOf sender Is frmPreview Then
            'Me.GetScreenshotThread(sender)
            '线程去取截屏图片，先显示窗体。
            If Me.mThreadGetScreenshotForPreview IsNot Nothing AndAlso Me.mThreadGetScreenshotForPreview.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadGetScreenshotForPreview.Abort()
                Catch ex As Exception
                End Try
            End If

            Me.mThreadGetScreenshotForPreview = New Thread(New ParameterizedThreadStart(AddressOf GetScreenshotThread))
            With Me.mThreadGetScreenshotForPreview
                .IsBackground = True
                .Start(sender)
            End With

            'ElseIf sender IsNot Nothing Then
            '    Dim img As Image = Me.GetGetScreenshot()

            ''weiBo重新取得截屏图片的处理事件
            'Dim frmPreview As FrmTUpdate = sender
            'If Me.CheckPicBlack(img) = False Then
            '    frmPreview.SetMsgPnlStatus = False
            'Else
            '    frmPreview.SetMsgPnlStatus = True
            'End If
            'img = Me.GetScreenshotShell(img)
            'frmPreview.SetScreenshotContralStatus(True, Me.mDevice.ProductType)
            'Dim imgPath As String = Folder.TempFolder + Common.GenerateGuid + ".jpg"
            'img.Save(imgPath, Imaging.ImageFormat.Jpeg)
            'frmPreview.ReSetWeiBoImage(imgPath)

        End If

    End Sub

    Private Sub btnScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScreenshot.Click, btnScreensort1.Click
        Me.mbtnLastClick = sender
        Me.SetScreenshotEnable(False)
        Try
            Me.GetScreenshotNormal()
        Catch ex As Exception
            Common.LogException("Screenshot Exception" & ex.ToString)
        End Try
        Me.SetScreenshotEnable(True)

        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryScreen)
    End Sub

    '取得截屏图片，方法有处理是否要加壳
    Private Function GetGetScreenshot() As Image
        Dim img As Image = Me.mDevice.GetScreenshotImage()
        '有的时候一次会取不到图片，就暂停0.3秒再取一次
        If img Is Nothing Then
            Me.mDevice.CloseScreenshot()
            Utility.WaitSeconds(1)
            img = Me.mDevice.GetScreenshotImage()
        End If

        If img Is Nothing Then
            '"很抱歉，截屏失败请稍候再重试！"
            MessageBox.Show(Me.Language.GetString("Welcome.Message.GetScreenshotFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If

        Return img
    End Function

    '判断图片是不是黑色的
    Private Function CheckPicBlack(ByVal img As Image) As Boolean
        Dim blnReturn As Boolean = True

        If img Is Nothing Then
            Return blnReturn
        End If

        Dim bmpobj As New Bitmap(img)
        Dim intCenterX As Integer = bmpobj.Width / 2
        Dim intCenterY As Integer = bmpobj.Height / 2
        For index As Integer = 0 To bmpobj.Height - 1
            Dim colorPoint As Color = bmpobj.GetPixel(intCenterX, index)
            If colorPoint.A <> Color.Black.A OrElse colorPoint.R <> Color.Black.R OrElse colorPoint.G <> Color.Black.G OrElse colorPoint.B <> Color.Black.B Then
                blnReturn = False
                Return blnReturn
            End If
        Next

        For index As Integer = 0 To bmpobj.Width - 1
            Dim colorPoint As Color = bmpobj.GetPixel(index, intCenterY)
            If colorPoint.A <> Color.Black.A OrElse colorPoint.R <> Color.Black.R OrElse colorPoint.G <> Color.Black.G OrElse colorPoint.B <> Color.Black.B Then
                blnReturn = False
                Return blnReturn
            End If
        Next
        Return blnReturn
    End Function

#End Region

#Region "--- 设置 ---"

    '设置处理事件
    Private Sub btnScreenshotSetUp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScreenshotSetUp.Click
        If IniSetting.GetScreenshotShell() Then
            Me.tsmiScreenshotShell.Image = My.Resources.tsmi_checked
            Me.tsmiScreenshot.Image = Nothing
        Else
            Me.tsmiScreenshotShell.Image = Nothing
            Me.tsmiScreenshot.Image = My.Resources.tsmi_checked
        End If
        Me.cmsSetting.Show(Me.btnScreenshotSetUp, New Point(0, Me.btnScreenshotSetUp.Bottom + 1))
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummarySetShell)
    End Sub

    Private Sub tsmiScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiScreenshot.Click
        Me.tsmiScreenshotShell.Image = Nothing
        Me.tsmiScreenshot.Image = My.Resources.tsmi_checked
        IniSetting.SetScreenshotShell(False)
    End Sub

    Private Sub tsmiScreenshotShell_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiScreenshotShell.Click

        Me.tsmiScreenshotShell.Image = My.Resources.tsmi_checked
        Me.tsmiScreenshot.Image = Nothing
        IniSetting.SetScreenshotShell(True)
    End Sub

#End Region

    Private Delegate Sub SetImageForScreenshotHandler(ByVal img As Image)
    Private Sub SetImageForScreenshot(ByVal img As Image)
        If Me.InvokeRequired Then
            Me.BeginInvoke(New SetImageForScreenshotHandler(AddressOf SetImageForScreenshot), img)
        Else
            frmImageView.SrcPicture = img

        End If

    End Sub

    '下载插件
    Private Sub DownloadScreenshotPlugin()
        If Me.mDevice Is Nothing OrElse Not Me.mDevice.IsConnected Then
            Return
        End If
        '从配制文件里查找当前版本的文件是否存在，如果不存在就下载。
        Dim strFileName As String = ScreenshotSetting.GetFileNameForVersion(Me.mDevice.ProductVersion)
        If strFileName.Trim.Length <= 0 Then
            '"很抱歉，没有找到与当前固件匹配的苹果开发者插件！"
            tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Welcome.Message.CanNotFindPluginForDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
            Return
        End If

        Dim strScreenshorPluginPath As String = Folder.Plugins & strFileName
        If System.IO.File.Exists(strScreenshorPluginPath) = False Then
            '如果助手1.0目录里面有插件就到1.0那里copy过来用。
            Try
                Dim strPath As String = ""
                If Not Folder.LangType = LanguageType.en_US Then
                    strPath = strScreenshorPluginPath.Replace("Tongbu", "iClover")
                    If System.IO.File.Exists(strPath) Then
                        File.Copy(strPath, strScreenshorPluginPath)
                        Me.InstallScreenshotPluginThread()
                        Return
                    End If
                Else
                    strPath = strScreenshorPluginPath.Replace("iClover", "Tongbu")
                    If System.IO.File.Exists(strPath) Then
                        File.Copy(strPath, strScreenshorPluginPath)
                        Me.InstallScreenshotPluginThread()
                        Return
                    End If
                End If
            Catch ex As Exception
            End Try

            '判断插件下载逻辑
            If Me.CheckDownloading() Then
                '正在下载插件，请稍候！
                tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Welcome.Message.DownloadingPlugin"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
                Return

            Else
                '"使用此功能需要下载插件，点击确定进行下载！"
                If tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Welcome.Message.ShouldDownloadPlugin"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.None, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Cancel Then
                    Return
                End If

            End If

            RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            '下载:添加插件下载任务
            Me.ScreenshotPluginDownload(Me.mDevice.ProductVersion)
        Else
            Me.InstallScreenshotPluginThread()
        End If
    End Sub

    Public Sub ScreenshotPluginDownload(ByVal strVersion As String)
        Dim lstItem As List(Of MultiThreadDownloadItem) = Me.mDownManage.GetItems(ResourceClass.Plugins, ResourceType.pScreenShot, TaskState.All)
        Dim strUrl As String = ScreenshotSetting.GetScreenshotPluginDownloadUrl(strVersion)

        For Each Item As MultiThreadDownloadItem In lstItem
            If Item.ItemInfo.Url = strUrl Then

                Me.mDownManage.Start(Item)
                Return
            End If
        Next

        Dim info As New MultiThreadDownloadItemInfo
        If String.IsNullOrEmpty(strUrl.Trim) = False Then
            info.Url = strUrl
            info.Type = ResourceType.pScreenShot
            info.Class = ResourceClass.Plugins
            info.Name = Path.GetFileName(strUrl) '"Screenshot Plugin"
            info.SaveFolder = Folder.Plugins
            Me.mDownManage.NewTask(info)
        End If

    End Sub

    Private Function CheckDownloading() As Boolean
        Dim result As Boolean = False

        If Me.mDownManage Is Nothing Then
            Me.mDownManage = MultiThreadDownload.Instance
        End If

        Dim strUrl As String = ScreenshotSetting.GetScreenshotPluginDownloadUrl(Me.mDevice.ProductVersion)

        For Each item As MultiThreadDownloadItem In Me.mDownManage.DownloadingList
            If item.ItemInfo.Url = strUrl Then
                result = True
                '如果发现正在下载重新绑定下载的事件，不然重新插入拔就看不到状态了。
                RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
                AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
                RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                Exit For
            End If
        Next

        Return result
    End Function

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of MultiThreadDownloadEventArgs)(AddressOf OnDownloading), sender, e)
        Else
            Dim item As MultiThreadDownloadItem = sender
            If item.ItemInfo.Class = ResourceClass.Plugins Then
                Dim strText As String = String.Empty
                If item.ItemInfo.Type = ResourceType.pScreenShot Then
                    strText = String.Format(Me.Language.GetString("Welcome.Message.DownlaodingPlugin"), e.Progress.ToString().PadLeft(2, " "c))          '"正在下载插件：{0}%"
                ElseIf item.ItemInfo.Type = ResourceType.pEXE Then
                    strText = String.Format(Me.Language.GetString("Welcome.Message.DownloadingJailTool") & "：{0}%", e.Progress.ToString().PadLeft(2, " "c))
                End If
                If strText.Length > 0 Then
                    Me.lblDownloadProgress.Text = strText
                    Me.lblDownPluginProgress.Text = strText
                End If
                Me.lblDownloadProgress.Visible = True
                Me.lblDownPluginProgress.Visible = True
            End If

        End If
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of MultiThreadDownloadCompletedEventArgs)(AddressOf OnDownloaded), sender, e)
        Else
            Dim item As MultiThreadDownloadItem = sender
            If item.ItemInfo.Class = ResourceClass.Plugins Then
                If File.Exists(item.ItemInfo.FilePath) Then
                    RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
                    RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded

                    If item.ItemInfo.Type = ResourceType.pScreenShot Then
                        Me.lblDownloadProgress.Text = Me.Language.GetString("Welcome.Message.DownloadScreenshotPluginSuccessed") '"截屏插件下载成功！"
                        Me.lblDownPluginProgress.Text = Me.Language.GetString("Welcome.Message.DownloadScreenshotPluginSuccessed") '"截屏插件下载成功！"
                        Utility.WaitSeconds(0.5)

                        '安装插件
                        Me.InstallScreenshotPluginThread()
                    ElseIf item.ItemInfo.Type = ResourceType.pEXE Then
                        Me.btnYueYu.Enabled = True
                        Me.lblDownloadProgress.Text = "越狱工具下载成功！"
                        Utility.WaitSeconds(0.5)

                        '启动越狱工具
                        Me.StartJailbreakTool()
                    End If

                End If
            End If
        End If
    End Sub

    Private Delegate Sub SetMainFormStatusMessageHandler(ByVal strMessage As String)
    Private Sub SetMainFormStatusMessage(ByVal strMessage As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetMainFormStatusMessageHandler(AddressOf SetMainFormStatusMessage), strMessage)
        Else
            If String.IsNullOrEmpty(strMessage.Length) = False Then
                Me.lblDownloadProgress.Visible = True
                Me.lblDownPluginProgress.Visible = True
            Else
                Me.lblDownloadProgress.Visible = False
                Me.lblDownPluginProgress.Visible = False
            End If
            Me.lblDownloadProgress.Text = strMessage
            Me.lblDownPluginProgress.Text = strMessage
        End If
    End Sub

    '用线程去安装插件，就不会卡
    Private Sub InstallScreenshotPluginThread()
        If Me.mThreadInstallPlugin IsNot Nothing AndAlso Me.mThreadInstallPlugin.ThreadState <> ThreadState.Stopped Then
            Try
                Me.mThreadInstallPlugin.Abort()
            Catch ex As Exception
            End Try
        End If

        Me.mThreadInstallPlugin = New Thread(AddressOf InstallScreenshotPlugin)
        With Me.mThreadInstallPlugin
            .IsBackground = True
            .Start()
        End With
    End Sub

    '安装插件
    Private Sub InstallScreenshotPlugin()
        Try
            Me.SetScreenshotEnable(False)
            If Me.StartScreenshotServer() Then
                Me.SetScreenshotEnable(True)
                Return
            End If

            '下载后把文件解压出
            Dim strFileName As String = ScreenshotSetting.GetFileNameForVersion(Me.mDevice.ProductVersion)
            Dim strScreenshorPluginPath As String = Folder.Plugins & strFileName

            If Not File.Exists(strScreenshorPluginPath) Then
                Return
            End If

            Me.SetMainFormStatusMessage(Me.Language.GetString("Welcome.Message.MounterThePlugin")) ' '"正在挂载插件,请稍候..."

            Dim strPathOnPC As String = Path.Combine(Folder.TempFolder, Me.mDevice.Identifier.ToString()) & "\"

            '解压
            Dim lstFolders As New List(Of String)
            Dim listInfoFilters As New List(Of UnzipInfo)
            listInfoFilters.Add(New UnzipInfo("/DeveloperDiskImage.dmg.signature", False, False, False, True))
            listInfoFilters.Add(New UnzipInfo("/DeveloperDiskImage.dmg", False, False, False, True))

            If Utility.unzip(strScreenshorPluginPath, strPathOnPC, listInfoFilters, lstFolders, True, False, True, Nothing) <= 0 Then
                Common.LogException(String.Format("插件解压失败：{0}", strScreenshorPluginPath))
            End If

            'Utility.unzip(strScreenshorPluginPath, strPathOnPC, New String() {"DeveloperDiskImage.dmg.signature", "DeveloperDiskImage.dmg"}, False, False)

            Dim filePath As String = strPathOnPC & "DeveloperDiskImage.dmg"
            If Me.mDevice.VersionNumber >= 600 Then
                Try
                    Dim filePathTmp As String = strPathOnPC & "staging.dimage"
                    If File.Exists(filePathTmp) Then
                        File.Delete(filePathTmp)
                    End If
                    File.Move(filePath, filePathTmp)
                    filePath = filePathTmp
                Catch ex As Exception
                End Try
            End If

            Dim succ As Boolean = Me.mDevice.SendMobileImageMounter(filePath, strPathOnPC & "DeveloperDiskImage.dmg.signature")
            If succ Then
                Me.SetMainFormStatusMessage(Me.Language.GetString("Welcome.Message.MounterPluginSucceed"))      '"插件挂载成功！"
            Else
                Me.SetMainFormStatusMessage(Me.Language.GetString("Welcome.Message.MounterPluginFailed"))      '"插件挂载失败！"
            End If


            Me.SetScreenshotEnable(True)
            Me.ReDoclick()

            Utility.WaitSeconds(3)
            Me.SetMainFormStatusMessage("")
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub ReDoclickHandler()
    Private Sub ReDoclick()
        If Me.InvokeRequired Then
            Me.Invoke(New ReDoclickHandler(AddressOf ReDoclick))
        Else
            If Me.mbtnLastClick IsNot Nothing Then
                Me.SetScreenshotEnable(False)
                If Me.mbtnLastClick.Name = Me.btnScreenshot.Name OrElse Me.mbtnLastClick.Name = Me.btnScreensort1.Name Then
                    Me.GetScreenshotNormal()

                ElseIf Me.mbtnLastClick.Name = Me.btnShareTo.Name Then
                    Me.ShareScreenshotToWeiBo(Me.mClientType, Nothing, Me.mApplication)

                ElseIf Me.mbtnLastClick.Name = Me.btnScreenshotPlay.Name OrElse Me.mbtnLastClick.Name = Me.btnDeviceDesktop.Name Then
                    Me.GetScreenshotPlay()

                End If
                Me.SetScreenshotEnable(True)
                Me.mbtnLastClick = Nothing
            End If
        End If

    End Sub

    Private Delegate Sub SetScreenshotEnableHandler(ByVal blnEnable As Boolean)
    Private Sub SetScreenshotEnable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetScreenshotEnableHandler(AddressOf SetScreenshotEnable), blnEnable)
        Else
            Me.btnScreenshot.Enabled = blnEnable
            Me.btnShareTo.Enabled = blnEnable
            Me.btnScreenshotPlay.Enabled = blnEnable
            Me.btnScreenshotSetUp.Enabled = blnEnable
            Me.btnBatteryManager.Enabled = blnEnable
            Me.btnScreensort1.Enabled = blnEnable
            Me.btnDeviceDesktop.Enabled = blnEnable
            Application.DoEvents()
        End If

    End Sub

    Private Function StartScreenshotServer()
        '尝试启动截屏服务，如果启动不了再等0.5秒再启动一次
        Dim blnScreenshotServerStart As Boolean = False

        Try
            blnScreenshotServerStart = Me.mDevice.StartScreenshot()
            If Not blnScreenshotServerStart Then
                Utility.WaitSeconds(0.5)
                blnScreenshotServerStart = Me.mDevice.StartScreenshot()
            End If
        Catch
        End Try

        Return blnScreenshotServerStart
    End Function

#End Region

#Region "--- 发截屏分享 ---"

    Private Sub btnScreenshotWeiBo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnShareTo.Click
        Me.mbtnLastClick = sender
        Me.cmsWeibo.Show(sender, New Point(-5, CType(sender, Control).Height + 2))

        'Me.SetScreenshotEnable(False)
        'Me.ShareScreenshotToWeiBo(ClientType.Sina, Nothing, Me.mApplication)
        'Me.SetScreenshotEnable(True)

        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryScreenShare)
    End Sub

    Private mImgWeiBo As Image = Nothing
    Private Sub ShareScreenshotToWeiBo(ByVal clientType As ClientType, ByVal imgSrc As Image, ByVal app As IApplication)
        If Not Me.mActivatedDevice Then
            '您的设备还没有激活，请激活后再尝试使用此功能！
            MessageBox.Show(Me.Language.GetString("Welcome.Message.DeviceUnactivated"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Try
            Me.mClientType = clientType

            If imgSrc IsNot Nothing Then
                Me.mImgWeiBo = imgSrc
            Else
                Me.mImgWeiBo = Me.mDevice.GetScreenshotImage()
                If Me.mImgWeiBo Is Nothing Then
                    Me.mDevice.CloseScreenshot()
                    Utility.WaitSeconds(1)
                    Me.mImgWeiBo = Me.mDevice.GetScreenshotImage()
                End If
            End If

            If Me.mImgWeiBo IsNot Nothing Then
                Dim isblack As Boolean = Me.CheckPicBlack(Me.mImgWeiBo)
                '添加外壳
                Me.mImgWeiBo = Me.GetScreenshotShell(Me.mImgWeiBo)

                Dim strProductType As String = "iPhone"
                Dim strDeviceType As String = "iPhone"
                If Me.mDevice.ProductType.ToLower().StartsWith("ipad") Then
                    strDeviceType = "iPad"
                    strProductType = "iPad"
                ElseIf Me.mDevice.ProductType.ToLower().StartsWith("iphone") Then
                    strDeviceType = "iPhone"
                Else
                    strDeviceType = "iPod Touch"
                End If
                '"用#同步助手#分享我的#{0}#截屏："
                WeiboHelper.SendMessage(clientType, app, String.Format(Me.Language.GetString("Welcome.Menu.ScreenShortToWeibo"), strDeviceType), Me.mImgWeiBo, strProductType, New EventHandler(Of WeiboImageEventArgs)(AddressOf Weibo_ReloadImage), isblack)

            Else
                Me.DownloadScreenshotPlugin()

            End If

            If Me.cmsWeibo.Tag IsNot Nothing AndAlso TypeOf Me.cmsWeibo.Tag Is Image Then
                CType(Me.cmsWeibo.Tag, Image).Dispose()
                Me.cmsWeibo.Tag = Nothing
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShareScreenshotToWeiBo")
        End Try
    End Sub

    Private Sub Weibo_ReloadImage(ByVal sender As Object, ByVal e As WeiboImageEventArgs)
        Try
            Me.mImgWeiBo = Me.mDevice.GetScreenshotImage()
            If Me.mImgWeiBo Is Nothing Then
                Me.mDevice.CloseScreenshot()
                Utility.WaitSeconds(1)
                Me.mImgWeiBo = Me.mDevice.GetScreenshotImage()
            End If
            e.IsBlack = Me.CheckPicBlack(Me.mImgWeiBo)
            Me.mImgWeiBo = Me.GetScreenshotShell(Me.mImgWeiBo)

            If Me.mImgWeiBo IsNot Nothing Then
                e.Image = Me.mImgWeiBo.Clone
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Weibo_ReloadImage")
        End Try
    End Sub


#Region "--- 分享菜单 ---"

    Private Sub tsmiSina_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiSina.Click
        Me.ShareScreenshotToWeiBo(ClientType.Sina, Me.cmsWeibo.Tag, Me.mApplication)
    End Sub

    Private Sub tsmiTecent_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiTecent.Click
        Me.ShareScreenshotToWeiBo(ClientType.Tecent, Me.cmsWeibo.Tag, Me.mApplication)
    End Sub

    Private Sub tsmiFacebook_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiFacebook.Click
        Me.ShareScreenshotToWeiBo(ClientType.Facebook, Me.cmsWeibo.Tag, Me.mApplication)
    End Sub

    Private Sub tsmiTwitter_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiTwitter.Click
        Me.ShareScreenshotToWeiBo(ClientType.Twitter, Me.cmsWeibo.Tag, Me.mApplication)
    End Sub

    Private Sub tmrRefresh_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrRefresh.Tick
        Try
            Me.RefreshBattery()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "RefreshBattery")
        End Try
    End Sub

    '' 如果电量为100，则不刷新
    Public Function RefreshBattery() As Integer
        If Me.mDevice Is Nothing Then
            Me.tmrRefresh.Enabled = False
            Return -1
        End If

        Dim count As Integer = 12
        Dim index As Integer = 0

        Dim batteryCapacity As Integer = Me.mDevice.BatteryCurrentCapacity

        '获取失败，则重新获取一次。
        If batteryCapacity < 0 Then
            batteryCapacity = Me.mDevice.BatteryCurrentCapacity
        End If

        If batteryCapacity = 100 Then
            index = count - 1    '电量100的时候取最后一张图片
        Else
            index = Math.Floor(batteryCapacity / 100 * (count - 1))
        End If

        If batteryCapacity < 0 Then
            Debug.Print("电量值：" & batteryCapacity.ToString())
            Me.tmrRefresh.Interval = 30000

            Return -1
        Else
            If Me.tmrRefresh.Interval <> 90000 Then
                Me.tmrRefresh.Interval = 90000
            End If
        End If

        Dim strBattery As String = String.Format("{0}%", batteryCapacity.ToString().PadLeft(2, " "c))
        If Me.mDevice.BatteryIsCharging Then
            Me.picCharging.Image = GuiHelper.GetMouseStateImage(My.Resources.icon_battery, index, count)
            Me.lblCharging.Text = String.Format(Me.Language.GetString("Welcome.Label.Charging"), strBattery)   '"Charging {0}" 
        Else
            Me.picCharging.Image = GuiHelper.GetMouseStateImage(My.Resources.icon_battery, index, count)
            Me.lblCharging.Text = strBattery
        End If

        Return batteryCapacity
    End Function

#End Region

#End Region

#Region "--- 清理TiP展示 ---"

    Private Sub SetClearTipThread(ByVal obj As Object)
        Try
            TongbuPlist.GetUsedTongbu(Me.mDevice)
            Dim dateClear As DateTime = TongbuPlist.GetLastClearTime(Me.mDevice)

            While Not Me.IsHandleCreated
                Utility.WaitSeconds(0.5)
            End While
            Me.BeginInvoke(New SetClearTipHandler(AddressOf SetClearTip), dateClear)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetClearTipThread")
        End Try
    End Sub

    Private Delegate Sub SetClearTipHandler(ByVal dateClear As Date)
    Private Sub SetClearTip(ByVal dateClear As Date)
        Try
            '8.3及以上固件不提示垃圾清朝
            If Me.mDevice.VersionNumber >= 830 Then
                Return
            End If

            If dateClear = DateTime.MinValue Then
                Me.btnClearTip.Text = Me.Language.GetString("Welcome.Button.ClearRubbish")      '"现在可以清理垃圾啦！"
                Me.btnClearTip.Visible = True

            Else
                Dim ts1 As TimeSpan = New TimeSpan(Date.Now.Ticks)
                Dim ts2 As TimeSpan = New TimeSpan(dateClear.Ticks)
                Dim ts As TimeSpan = ts1.Subtract(ts2).Duration()
                Dim intValue As Integer = ts.Days

                If intValue > 7 Then
                    If intValue > 60 Then
                        intValue = 60
                    End If
                    Me.btnClearTip.Text = String.Format(Me.Language.GetString("Welcome.Button.ChearRubbishDays"), intValue)     '"{0}天没有清理过垃圾了！"
                    Me.btnClearTip.Visible = True
                Else
                    Me.btnClearTip.Visible = False
                End If
            End If
            Me.btnClearTip.Location = New Point(Me.tbDevCapacity.Right - Me.btnClearTip.Width + 11, Me.btnClearTip.Top)
        Catch
        End Try
    End Sub

#End Region

#Region "--- 检测是否安装同步推或正版 ---"

    Private Sub tmrTui_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrTui.Tick
        Me.tmrTui.Stop()
        If Me.mDevice Is Nothing Then
            Return
        End If
        '自动对同步推绑定帐号进行授权
        AuthorizeByAppleID.Instance(Me.mDevice).CheckAutoAuth()

        '检测同步推是否安装
        Me.Start2CheckTuiInstall()

        '检测右下角弹出广告窗
        'If Not Common.VerIs30() OrElse Not ServerIniSetting.GetIsUsingPush() Then
        '    ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf CheckPopThread))
        'End If
    End Sub


    Private Sub Start2CheckTuiInstall()
        If Folder.LangType = LanguageType.en_US OrElse
           (Me.mThreadCheckTuiInstall IsNot Nothing AndAlso Me.mThreadCheckTuiInstall.ThreadState <> Threading.ThreadState.Stopped) Then

            Return
        End If

        Me.mThreadCheckTuiInstall = New Threading.Thread(New Threading.ThreadStart(AddressOf DoCheckTuiInstall))
        Me.mThreadCheckTuiInstall.IsBackground = True
        Me.mThreadCheckTuiInstall.SetApartmentState(ApartmentState.STA) '线程中显示窗体需要设置
        Me.mThreadCheckTuiInstall.Start()
    End Sub

    Private Sub DoCheckTuiInstall()
        'Dim blnInsertTuiMC As Boolean = True
        Dim blnUnInstallTui As Boolean = False
        Dim blnAppCountTooLow As Boolean = False
        Dim installState As PackageInstallState = PackageInstallState.Installed

        Try

            If Me.mDevice IsNot Nothing Then
                If Me.mDevice.VersionNumber < 700 Then
                    '打开WiFiSync，支持设备上授权
                    Me.mDevice.OpenWiFiSync()
                End If

                'If Not Common.IsTestMode Then
                Me.InstallTui()
                'End If

                If Folder.AppType = RunType.Tongbu OrElse Folder.AppType = RunType.TongbuLite OrElse Folder.AppType = RunType.ZJHelper Then
                    BookmarkHelper.Instance(Me.mDevice).CheckTuiBookmark("同步推", "http://tui.tongbu.com/m/?s=tbzs30")
                    If ServerIniSetting.GetBookmarkTBGame Then
                        BookmarkHelper.Instance(Me.mDevice).CheckTuiBookmark("同步游戏", "http://game.tongbu.com/h5?channel=h5.zhushou.shuqian")
                    End If
                    'If ServerIniSetting.GetBookmarkXMGame Then
                    '    BookmarkHelper.Instance(Me.mDevice).CheckTuiBookmark("手机赚钱入口", "https://at.umeng.com/r8Demm")
                    'End If
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary_DoCheckTuiInstall")
        End Try

        Try
            Me.DoCollectApp()
        Catch ex As Exception

        End Try
    End Sub

    Private Sub InstallTui()
        Dim blnInstall As Boolean = False
        Dim state As PackageInstallState = AuthorizeByAppleID.Instance(Me.mDevice).CheckTuiInstallState()

        '收集设置不强装的电脑数量，用mac地址就可以知道有设置有没有设置的比例
        If Not IniSetting.GetPermissionInstallTui Then
            '用户去掉钩选安装同步推的时候才收集
            ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.TuiUnInstall, ActionDataType.Click, FunctionSucceed.Succeed)
            'Else
            '    ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.TuiUnInstall, ActionDataType.Click, FunctionSucceed.Failure)
        End If

        '1.如果用户设置不强装同步推就不强装
        If (state = PackageInstallState.Uninstalled OrElse state = PackageInstallState.Unknow) AndAlso Not IniSetting.GetPermissionInstallTui Then
            blnInstall = False
            GoTo DO_EXIT
        End If

        '如果是越狱设备安装了高版本的同步推要安装回低版的同步推。
        If state = PackageInstallState.InstallHigher AndAlso Me.mDevice.Jailbreaked Then
            state = PackageInstallState.InstallLower
        End If
        Select Case state
            Case PackageInstallState.InstallHigher, PackageInstallState.Installed
                '标记安装成功过了
                If TongbuPlist.GetTuiInstalling(Me.mDevice) Then
                    TongbuPlist.SetTuiInstalling(Me.mDevice, False)
                End If

            Case PackageInstallState.InstallLower
                '标记安装成功过了
                If TongbuPlist.GetTuiInstalling(Me.mDevice) Then
                    TongbuPlist.SetTuiInstalling(Me.mDevice, False)
                End If

                blnInstall = True
                GoTo DO_EXIT

            Case PackageInstallState.Uninstalled
                '用户没有安装过同步推，首次安装或者上次安装未完成则继续下载安装
                If Not TongbuPlist.GetUsedTongbu(Me.mDevice) OrElse TongbuPlist.GetTuiInstalling(Me.mDevice) Then
                    TongbuPlist.SetUsedTongbu(Me.mDevice, True)
                    TongbuPlist.SetShowTuiInstall(Me.mDevice, False)
                    TongbuPlist.SetTuiInstalling(Me.mDevice, True)

                    blnInstall = True
                    GoTo DO_EXIT
                Else
                    TongbuPlist.SetTuiInstalling(Me.mDevice, False)
                End If

                If TongbuPlist.GetShowTuiInstall(Me.mDevice) AndAlso Common.NetworkIsAvailable() Then
                    '熊猫推的弹窗去掉
                    If Utility.IsPanda() Then
                        Return
                    End If
                    TongbuPlist.SetShowTuiInstall(Me.mDevice)
                    '服务器启用强装同步推逻辑
                    If ServerIniSetting.GetForcedInstallTui() Then
                        blnInstall = True
                        GoTo DO_EXIT
                    Else
                        Me.BeginInvoke(New ShowFormTuiInstallDelegate(AddressOf ShowFormTuiInstall), True, False, Me.mDevice, Me.mApplication)
                    End If
                End If
        End Select

        'Return
DO_EXIT:
        If blnInstall Then
            AuthorizeByAppleID.Instance(Me.mDevice).CheckTui(ResourceClass.Plugins, False, False, False, Me.mAutoTryInstallTui)
            '收集强装开始事件。
            ActionCollectHelper.OperateTuiPage(Me.mDevice, ModelKey.None, ModelKey.TuiAutoInstall, ActionDataType.Click)
        End If

        '一键安装逻辑
#If IS_ITONG Then
        If Folder.LangType = LanguageType.zh_CN Then
            Try
                '检测需不需要弹出 装机必备 
                If Me.mIPhoneHotAppsHelper.IsReady = False Then
                    RemoveHandler Me.mIPhoneHotAppsHelper.LoadiPhoneHotAppsEventHandler, AddressOf OnLoadiPhoneHotApps
                    AddHandler Me.mIPhoneHotAppsHelper.LoadiPhoneHotAppsEventHandler, AddressOf OnLoadiPhoneHotApps
                    Me.mIPhoneHotAppsHelper.Start2LoadHotApps()
                Else
                    ShowHotApps()
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "DoCheckTuiInstall")
            End Try
        End If


        '如果装机必备没有关闭就不打开教程
        While Not Me.mIPhoneHotAppsHelper.IsReady OrElse (Me.mfrmHotApps IsNot Nothing AndAlso Me.mfrmHotApps.Visible AndAlso Not Me.mfrmHotApps.IsDisposed)
            Utility.WaitSeconds(1)
        End While

#End If

        '显示授权企业签名的提示
        If blnInstall Then
            Me.ShowTuiTutorial()
        End If

        '同步推H5 推安装收集
        If IconHelper.CheckTuiH5Installed(Me.mDevice, "同步推网页版") Then
            ActionCollectHelper.OperateTuiToPhone(Me.mDevice, ModelKey.TuiInstallH5ToPhone, ActionDataType.Install, FunctionSucceed.Succeed, "")
        End If

    End Sub

    '显示ios9信任签名教程
    Private Delegate Sub ShowTuiTutorialHandler()
    Private Sub ShowTuiTutorial()
        If Me.InvokeRequired Then
            Me.Invoke(New ShowTuiTutorialHandler(AddressOf ShowTuiTutorial))
        Else
            If Me.mDevice.VersionNumber >= 900 AndAlso Not TongbuPlist.GetShowedTuiTutorial(Me.mDevice) AndAlso Not IniSetting.GetNoShowTuiInstallTutorial() Then
                frmTuiInstallTutorial.ShowForm(Me.mApplication)
                TongbuPlist.SetShowedTuiTutorial(Me.mDevice, True)
            End If
            '品专业包的逻辑
            'frmTuiInstallTutorial.InstallTuiTutorial(Me.mDevice)
        End If
    End Sub

    Private Delegate Sub ShowFormTuiInstallDelegate(ByVal isAutoShow As Boolean, ByVal isHideNoShowButton As Boolean, ByVal device As iPhoneDevice, ByVal appli As IApplication)

    Public Shared Sub ShowFormTuiInstall(ByVal isAutoShow As Boolean, ByVal isHideNoShowButton As Boolean, ByVal device As iPhoneDevice, ByVal appli As IApplication)
        For Each ctl As Form In Application.OpenForms
            If TypeOf ctl Is frmTuiInstall AndAlso device.Identifier.Equals(ctl.Name, StringComparison.OrdinalIgnoreCase) Then

                frmTuiIns = ctl
                Exit For
            End If
        Next

        If frmTuiIns Is Nothing OrElse frmTuiIns.IsDisposed Then
            frmTuiIns = New frmTuiInstall(appli, device)
            frmTuiIns.AutoShow = isAutoShow
            frmTuiIns.HideNoShowButton = isHideNoShowButton

            frmTuiIns.StartPosition = FormStartPosition.Manual
            Dim frmMain As MainForm = appli
            frmTuiIns.Location = New Point(frmMain.Location.X + (frmMain.Width - frmTuiIns.Width) \ 2, frmMain.Location.Y + (frmMain.Height - frmTuiIns.Height) \ 2)
        End If
        frmTuiIns.Name = device.Identifier
        frmTuiIns.Show()
        frmTuiIns.Activate()

        If frmTuiIns.WindowState = FormWindowState.Minimized Then
            frmTuiIns.WindowState = FormWindowState.Normal
        End If

        ActionCollectHelper.ClickDeviceHomePageChild(device, ModelKey.Tui)
    End Sub

    Private Sub ShowFormH5GameInstall()
#If IS_ITONG Then
        Try
            For Each ct1 As Form In Application.OpenForms
                If TypeOf ct1 Is frmH5GameInstall AndAlso Me.mDevice.Identifier.Equals(ct1.Name, StringComparison.OrdinalIgnoreCase) Then
                    mfrmH5GameInstall = ct1
                    Exit For
                End If
            Next

            If mfrmH5GameInstall Is Nothing OrElse mfrmH5GameInstall.IsDisposed Then
                mfrmH5GameInstall = New frmH5GameInstall(Me.mApplication, Me.mDevice)
                mfrmH5GameInstall.StartPosition = FormStartPosition.Manual
                Dim frmMain As MainForm = Me.mApplication
                mfrmH5GameInstall.Location = New Point(frmMain.Location.X + (frmMain.Width - mfrmH5GameInstall.Width) \ 2, frmMain.Location.Y + (frmMain.Height - mfrmH5GameInstall.Height) \ 2)
            End If

            mfrmH5GameInstall.Name = Me.mDevice.Identifier
            mfrmH5GameInstall.Show()
            mfrmH5GameInstall.Activate()
            If mfrmH5GameInstall.WindowState = FormWindowState.Minimized Then
                mfrmH5GameInstall.WindowState = FormWindowState.Normal
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowFormH5GameInstall")
        End Try
#End If
    End Sub
#End Region

#Region "--- 同步推安装进度 ---"

    Private blnCancelInstall As Boolean = False
    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        Try
            Select Case args.InstallState
                Case InstallState.AllInstallCompleted
                    If Me.mDownManage Is Nothing Then
                        Me.mDownManage = MultiThreadDownload.Instance
                    End If
                    If Me.mInstallHelper.GetTaskCount <= 0 AndAlso
                       Me.mDownManage.GetItems(ResourceClass.Software, ResourceType.IPA, TaskState.Waiting).Count <= 0 Then
                        For Each Item As MultiThreadDownloadItem In Me.mDownManage.DownloadingList
                            If Item.ItemInfo.Type = ResourceType.IPA AndAlso Item.ItemInfo.Class = ResourceClass.Software Then
                                blnCancelInstall = False
                                Return
                            End If
                        Next
                        If blnCancelInstall Then

                            'Dim frmMain As MainForm = Common.CheckFormExist(GetType(MainForm))
                            'If frmMain IsNot Nothing Then
                            '    frmMain.SetToolTip(Me.mDevice.DeviceName, Me.Language.GetString("Welcome.Message.AllTaskFinished"))          '"所有软件已安装完成"
                            'End If

                            CType(Me.mApplication, MainForm).SetToolTip(Me.mDevice.DeviceName, Me.Language.GetString("Welcome.Message.AllTaskFinished"))          '"所有软件已安装完成"
                        End If
                        blnCancelInstall = False
                    End If

                Case InstallState.CancelInstall
                    blnCancelInstall = True

                Case InstallState.UnSucceed
                    If Me.mAutoTryInstallTui Then
                        Return
                    End If
                    Me.mAutoTryInstallTui = True

                    Me.Start2CheckTuiInstall()

                Case InstallState.Succeed
                    '如果同步推安装上了就把同步推安装状态设置成false ,下次进来就不会强装同步推了。
                    If args.PackageInfo IsNot Nothing AndAlso args.PackageInfo.TuiType <> tbTuiType.None Then
                        TongbuPlist.SetTuiInstalling(Me.mDevice, False)
                        Me.UpdateTuiChannel(args)
                    End If

            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "mInstallHelper_OnInstall")
        End Try
    End Sub

    Private Sub UpdateTuiChannel(ByVal args As InstallArgs)
        Dim key As ModelKey = CoreUpdateHelper.GetTuiModeKey(args.PackageInfo.PackagePath)
        Dim isKe As Boolean = False
        If key = ModelKey.TuiAutoInstallKe OrElse key = ModelKey.TuiInstallKe Then
            isKe = True
        End If
        Me.WriteInfoToDeviceThread(isKe)
    End Sub

#End Region

#Region "--- 自动安装AppSync逻辑 ---"

    Private Sub InstallAppSyncThread()
        'Dim thr As New Threading.Thread(AddressOf InstallAppSync)
        'With thr
        '    .IsBackground = True
        '    .Start()
        'End With
        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf InstallAppSync))
    End Sub

    Private Sub InstallAppSync(ByVal obj As Object)
        Try
            If Me.mDevice Is Nothing OrElse
               Not Me.mDevice.InstallCydia OrElse
               TuiInstallHelper.CheckAppSyncInstall(Me.mDevice) Then
                Return
            End If
            Folder.CheckFolder(Folder.Plugins)
            If Not File.Exists(ScreenshotSetting.GetAppSyncPath(Me.mDevice)) Then
                '下载插件appSync
                If Me.CheckDownloadingAppSync() Then
                    Return
                Else
                    RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                    AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                    Me.AppsyncPluginDownload(Me.mDevice.ProductVersion)
                End If
            End If

            Install(Me.mDevice)
        Catch ex As Exception
        End Try
    End Sub

    'Public Shared Function GetAppSyncPath(ByVal device As iPhoneDevice) As String
    '    Return Path.Combine(Folder.Plugins, Common.ReplaceWinIllegalName(ScreenshotSetting.GetAppSyncFileNameForVersion(device.ProductVersion))) & ".zip"
    'End Function

    Private Function CheckDownloadingAppSync() As Boolean
        Dim result As Boolean = False

        If Me.mDownManage Is Nothing Then
            Me.mDownManage = MultiThreadDownload.Instance
        End If

        If Me.mDevice IsNot Nothing Then '' Added by Utmost20141015 '' 避免拔掉设备后，程序延时还读取设备属性

            Dim strUrl As String = ScreenshotSetting.GetAppSyncDownloadUrl(Me.mDevice.ProductVersion)
            For Each item As MultiThreadDownloadItem In Me.mDownManage.DownloadingList
                If item.ItemInfo.Url = strUrl Then
                    result = True
                    '如果发现正在下载重新绑定下载的事件，不然重新插入拔就看不到状态了。
                    RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                    AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                    Exit For
                End If
            Next

        End If

        Return result
    End Function

    Public Sub AppsyncPluginDownload(ByVal strVersion As String)
        Dim lstItem As List(Of MultiThreadDownloadItem) = Me.mDownManage.GetItems(ResourceClass.Plugins, ResourceType.DEB, TaskState.All)
        Dim strUrl As String = ScreenshotSetting.GetAppSyncDownloadUrl(strVersion)
        Dim strName As String = ScreenshotSetting.GetAppSyncFileNameForVersion(strVersion)
        For Each Item As MultiThreadDownloadItem In lstItem
            If Item.ItemInfo.Url = strUrl Then
                Me.mDownManage.Start(Item)
                Return
            End If
        Next

        Dim info As New MultiThreadDownloadItemInfo
        If String.IsNullOrEmpty(strUrl.Trim) = False Then
            info.Url = strUrl
            info.Type = ResourceType.DEB
            info.Class = ResourceClass.Plugins
            info.Name = Common.ReplaceWinIllegalName(strName) & ".zip"
            info.SaveFolder = Folder.Plugins
            Me.mDownManage.NewTask(info)
        End If
    End Sub

    Public Shared Sub Install(ByVal device As iPhoneDevice)
        Try
            If Not device.InstallCydia OrElse TuiInstallHelper.CheckAppSyncInstall(device) Then
                Return
            End If

            Folder.CheckFolder(Folder.Plugins)
            Dim strAppSyncPath As String = ScreenshotSetting.GetAppSyncPath(device)
            If File.Exists(strAppSyncPath) Then
                Dim strPath As String = Path.Combine(Folder.TempFolder, "AppSync")
                Try
                    If Directory.Exists(strPath) Then
                        Directory.Delete(strPath, True)
                    End If
                Catch ex As Exception
                End Try

                Utility.unzip(strAppSyncPath, strPath)

                If Directory.Exists(strPath) Then
                    If Not device.StartTongbuFairy() Then
                        device.StartTongbuFairy()
                    End If

                    For Each Item As String In GetDebFiles(strPath)
                        If File.Exists(Item) Then
                            device.InstallDeb(Item, Nothing)
                        End If
                    Next
                End If
            End If
        Catch ex As Exception
            Common.LogException("Install AppSync Error:" & ex.ToString())
        End Try
    End Sub

    Public Shared Function GetDebFiles(ByVal strPath As String) As List(Of String)
        Dim lstReturn As New List(Of String)
        Dim strSetting As String = Path.Combine(strPath, "config.txt")
        Dim strSettingIni As String = Path.Combine(strPath, "config.ini")

        If Not File.Exists(strSetting) AndAlso Not File.Exists(strSettingIni) Then
            For Each Item As String In Directory.GetFiles(strPath, "*.*", SearchOption.AllDirectories)
                If Item.ToLower.EndsWith(".txt") Then
                    strSetting = Item
                ElseIf Item.ToLower.EndsWith(".int") Then
                    strSettingIni = Item
                End If
            Next
        End If

        Try
            If File.Exists(strSetting) Then
                Using fileReader As IO.StreamReader = New IO.StreamReader(strSetting)
                    Dim vals As String() = fileReader.ReadToEnd.Split(vbCrLf)
                    If vals IsNot Nothing AndAlso vals.Length > 0 Then
                        For Each Item As String In vals
                            If Item.Trim.StartsWith("Depends=") OrElse Item.Trim.StartsWith("Depends:") Then
                                For Each filePath As String In Item.Replace("Depends=", "").Replace("Depends:", "").Trim.Split(",")
                                    lstReturn.Add(Path.Combine(Path.GetDirectoryName(strSetting), filePath) & ".deb")
                                Next
                            ElseIf Not Item.Trim.StartsWith("Depends=") AndAlso Not Item.Trim.StartsWith("Depends:") AndAlso Not Item.Trim.StartsWith("Firmware=") AndAlso Not Item.Trim.StartsWith("Firmware:") Then
                                '有可能最后一行是有换行。
                                For Each filePath As String In Item.Replace("Depends=", "").Replace("Depends:", "").Trim.Split(",")
                                    lstReturn.Add(Path.Combine(Path.GetDirectoryName(strSetting), filePath) & ".deb")
                                Next
                            End If
                        Next
                        For Each Item As String In vals
                            If Item.Trim.StartsWith("Package=") OrElse Item.Trim.StartsWith("Package:") Then
                                Dim strItems As String() = Item.Trim.Replace("Package=", "").Replace("Package:", "").Split(",")
                                lstReturn.Add(Path.Combine(Path.GetDirectoryName(strSetting), Item.Trim.Replace("Package=", "").Replace("Package:", "") & ".deb"))
                            End If
                        Next
                    End If
                End Using
            ElseIf File.Exists(strSettingIni) Then
                '取得ini文件里面的deb文件列表。
                Dim strValue As String = IniClass.GetIniSectionKey("Deb", "Depends", strSettingIni)
                If strValue.Trim.Length > 0 Then
                    For Each Item As String In strValue.Split(",")
                        lstReturn.Add(Path.Combine(Path.GetDirectoryName(strSettingIni), Item) & ".deb")
                    Next
                End If

                strValue = IniClass.GetIniSectionKey("Deb", "Package", strSettingIni)
                If strValue.Trim.Length > 0 Then
                    lstReturn.Add(Path.Combine(Path.GetDirectoryName(strSettingIni), strValue) & ".deb")
                End If
            Else
                For Each Item As String In Directory.GetFiles(strPath)
                    If Item.Trim.EndsWith(".deb") Then
                        lstReturn.Add(Item.Trim)
                    End If
                Next
            End If
        Catch ex As Exception
        End Try

        Return lstReturn
    End Function

#End Region

#Region "--- 内部函数 ---"

    Private Function GetDeviceBackupPath() As String
        Dim pathOnPhone As String = "/ApplicationArchives/"

        If Not Me.mDevice.ExistsByAFC(pathOnPhone) Then
            Return String.Empty
        End If

        Return pathOnPhone
    End Function

    '判断是否有备份缓存
    Private Function CheckHasBackupCache() As Boolean
        Dim pathOnPhone As String = Me.GetDeviceBackupPath()

        If pathOnPhone.Length > 0 Then
            Return (Me.mDevice.GetFiles(pathOnPhone, True).Length > 0)
        End If

        Return False
    End Function

    '格式化激活状态
    Private Function FormatActivationState(ByVal str As String) As String
        Dim strResult As String = ""
        Me.mActivatedDevice = True
        Select Case str
            Case "Activated"
                strResult = Me.Language.GetString("Welcome.Label.Activated")    '"已激活"

            Case "WildcardActivated"
                strResult = Me.Language.GetString("Welcome.Label.Activated")    '"已激活"

            Case "FactoryActivated"
                strResult = Me.Language.GetString("Welcome.Label.Activated")    '"已激活"

            Case "Unactivated"
                strResult = Me.Language.GetString("Welcome.Label.Unactivated")  '"未激活"
                Me.mActivatedDevice = False '记录激活状态，截屏的时候如果没激活是无法使用的。

            Case Else
                strResult = str
        End Select

        Return strResult
    End Function

    '格式化颜色
    Public Shared Function FormatDeviceColor(ByVal dev As iPhoneDevice, ByVal deviceColor As PHBDeviceColorVariation, ByVal lang As LanguageInterface) As String
        Dim strResult As String = ""
        strResult = ServerIniSetting.GetDeviceColorName(dev.ProductType, dev.DeviceColorBg)
        If Not String.IsNullOrEmpty(strResult) Then
            Return strResult
        End If

        Select Case deviceColor
            Case PHBDeviceColorVariation.Black
                strResult = lang.GetString("Welcome.Label.Black") '"黑色"

            Case PHBDeviceColorVariation.Blue
                strResult = lang.GetString("Welcome.Label.Blue") '"蓝色"

            Case PHBDeviceColorVariation.Gold
                strResult = lang.GetString("Welcome.Label.Gold") '"金色"

            Case PHBDeviceColorVariation.Green
                strResult = lang.GetString("Welcome.Label.Green") '"绿色"

            Case PHBDeviceColorVariation.Pink
                strResult = lang.GetString("Welcome.Label.Pink") '"粉色"

            Case PHBDeviceColorVariation.Red
                strResult = lang.GetString("Welcome.Label.Red") '"红色"

            Case PHBDeviceColorVariation.Silver
                strResult = lang.GetString("Welcome.Label.Silver") '"银色"

            Case PHBDeviceColorVariation.White
                strResult = lang.GetString("Welcome.Label.White") '"白色"

            Case PHBDeviceColorVariation.Yellow
                strResult = lang.GetString("Welcome.Label.Yellow") '"黄色"

            Case PHBDeviceColorVariation.Rose
                strResult = lang.GetString("Welcome.Label.RoseGold") '"玫瑰金"

            Case PHBDeviceColorVariation.SpaceGray
                strResult = lang.GetString("Welcome.Label.SpaceGray") '"深空灰"

            Case PHBDeviceColorVariation.LightBlack
                strResult = lang.GetString("Welcome.Label.LightBlack")   '"亮黑色"

            Case PHBDeviceColorVariation.PacificBlue '"海蓝色"
                strResult = lang.GetString("Welcome.Label.SeaBlue")

            Case PHBDeviceColorVariation.Graphite '"石墨色"
                strResult = lang.GetString("Welcome.Label.Graphite")

            Case PHBDeviceColorVariation.DarkGreen
                strResult = lang.GetString("Welcome.Label.DarkGreen")    '"深绿色"

            Case PHBDeviceColorVariation.Purple
                strResult = lang.GetString("Welcome.Label.Purple")    '"紫色"

            Case PHBDeviceColorVariation.StarLight
                strResult = lang.GetString("Welcome.Label.StarLight")    '"星光色"

            Case PHBDeviceColorVariation.Midnight
                strResult = lang.GetString("Welcome.Label.Midnight")    '"午夜色"

            Case Else
                '从serverinisett.ini配制取颜色值
                strResult = ServerIniSetting.GetDeviceColor(dev.DeviceColorBg)
                If String.IsNullOrEmpty(strResult) Then
                    strResult = lang.GetString("Common.Label.Unknow") '"未知"
                End If
        End Select

        Return strResult
    End Function

    '通过序列号计算出第几周生产的
    Private Function ComputeWeekOfDeviceBySerial(ByVal strSerial As String) As String
        Dim strYear As String = ""
        '如果是iPhone4s就不进行计算
        '在恢复模式下　Me._iPhone　是为空，所以得加一个判断
        If String.IsNullOrEmpty(strSerial) = False AndAlso Me.mDevice IsNot Nothing Then

            '序列号类似880184BJ8M7，其中第三位表示年份，第四第五位表示周数
            Dim charArray As Char() = strSerial.ToCharArray()
            If charArray IsNot Nothing AndAlso charArray.Length > 5 Then
                If Char.IsDigit(charArray(2)) = False OrElse Char.IsDigit(charArray(3)) = False OrElse Char.IsDigit(charArray(4)) = False Then
                    Return ""
                End If

                Dim chYear As Char = charArray(2)
                Dim strWeek As String = charArray(3) & charArray(4)

                If chYear > "6" Then
                    strYear = "200" & chYear
                Else
                    strYear = "201" & chYear
                End If

                '"{0}年,{1}周,{2}月"
                strYear = String.Format(Me.Language.GetString("Welcome.Label.DeviceSerialOfWeek"), strYear, strWeek, ChangeWeekToDay(strYear, strWeek))

            End If
        End If
        If strYear.Length > 0 Then
            strYear = String.Format(" ({0})", strYear)
        End If
        Return strYear
    End Function

    '把第几周转换成月份格式(03月)
    Private Function ChangeWeekToDay(ByVal year As String, ByVal week As String) As String
        Dim strReturn As String = ""

        Try
            strReturn = DateAdd(DateInterval.WeekOfYear, Val(week), CDate(Val(year) & "-01-01")).ToString("MM")
        Catch ex As Exception
            Debug.Print(ex.ToString())
        End Try

        Return strReturn
    End Function

#End Region

#Region "--- 越狱工具下载及启动 ---"

    Private mJailbreakPath As String = String.Empty

    Private Sub InitJailbreakTool()
        Try
            Me.mJailbreakPath = Path.Combine(Folder.DocumentFolder, "Jailbreak")
            Folder.CheckFolder(Me.mJailbreakPath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitJailbreakTool")
        End Try
    End Sub

    Private Enum OperateType
        None
        OpenCourse
        OpenExe
        Download
    End Enum

#Region "--- 开始越狱 ---"

    Public Shared Function FormatProductForJailbreak(ByVal strProduct As String) As String
        Dim strResult As String = ""

        If String.IsNullOrEmpty(strProduct) Then
            Return strResult
        End If

        Select Case strProduct
            Case "iPad1,1"
                strResult = "iPad1"

            Case "iPad2,1"
                strResult = "iPad2"

            Case "iPad2,2"
                strResult = "iPad2"

            Case "iPad2,3"
                strResult = "iPad2"

            Case "iPad2,4"
                strResult = "iPad2"

            Case "iPad2,5"
                strResult = "iPadmini"

            Case "iPad2,6"
                strResult = "iPadmini"

            Case "iPad2,7"
                strResult = "iPadmini"

            Case "iPad3,1"
                strResult = "iPad3"

            Case "iPad3,2"
                strResult = "iPad3"

            Case "iPad3,3"
                strResult = "iPad3"

            Case "iPad3,4"
                strResult = "iPad4"

            Case "iPad3,5"
                strResult = "iPad4"

            Case "iPad3,6"
                strResult = "iPad4"

            Case "iPad4,1"
                strResult = "iPadAir"

            Case "iPad4,2"
                strResult = "iPadAir"

            Case "iPad4,4"
                strResult = "iPadmini2"

            Case "iPad4,5"
                strResult = "iPadmini2"

            Case "iPhone1,1"
                strResult = "iPhone2G"

            Case "iPhone1,2"
                strResult = "iPhone3G"

            Case "iPhone2,1"
                strResult = "iPhone3GS"

            Case "iPhone3,1"
                strResult = "iPhone4"

            Case "iPhone3,2"
                strResult = "iPhone4"

            Case "iPhone3,3"
                strResult = "iPhone4"

            Case "iPhone4,1"
                strResult = "iPhone4S"

            Case "iPhone5,1"
                strResult = "iPhone5"

            Case "iPhone5,2"
                strResult = "iPhone5"

            Case "iPhone5,3"
                strResult = "iPhone5C"

            Case "iPhone5,4"
                strResult = "iPhone5C"

            Case "iPhone6,1"
                strResult = "iPhone5S"

            Case "iPhone6,2"
                strResult = "iPhone5S"

            Case "iPhone7,1"
                strResult = "iPhone6Plus"

            Case "iPhone7,2"
                strResult = "iPhone6"

            Case "iPod1,1"
                strResult = "iPodTouch1G"

            Case "iPod2,1"
                strResult = "iPodTouch2G"

            Case "iPod3,1"
                strResult = "iPodTouch3G"

            Case "iPod4,1"
                strResult = "iPodTouch4"

            Case "iPod5,1"
                strResult = "iPodTouch5"

            Case Else
                strResult = strProduct

        End Select

        Return strResult
    End Function

    '开始越狱
    Public Sub StartJailbreakTool()
        Try
            If Me.bgwStartJailbreak.IsBusy Then
                Return
            End If

            Me.bgwStartJailbreak.RunWorkerAsync()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "StartJailbreakTool")
        End Try
    End Sub

    Private Sub bgwStartJailbreak_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwStartJailbreak.DoWork
        Try
            Dim type As OperateType = OperateType.None
            Me.SetDownloadProgressText(Me.Language.GetString("Welcome.Message.StartJailbreakTool"))     '"启动越狱工具..."

            If Me.mDevice Is Nothing Then
                GoTo DO_EXIT
            End If

            '1、从运行目录拷贝越狱工具
            Me.CopyFileFromAppFolder()

            JBIniSetting.DownloadConfig()

            Dim strProductVersion As String = Me.mDevice.ProductVersion
            Dim strProductType As String = frmSummary.FormatProductForJailbreak(Me.mDevice.ProductType)
            Dim strExeFileName As String = JBIniSetting.GetExeFileName(strProductVersion, strProductType)   'exe启动路径
            Dim strFileName As String = JBIniSetting.GetFileName(strProductVersion, strProductType)              '下载文件路径
            Dim strToolUrl As String = JBIniSetting.GetFileUrl(strProductVersion, strProductType)                      '下载地址

            If String.IsNullOrEmpty(strExeFileName) OrElse
               String.IsNullOrEmpty(strFileName) OrElse
               String.IsNullOrEmpty(strToolUrl) Then

                type = OperateType.OpenCourse
                GoTo DO_EXIT
            End If

            '2、检查越狱工具
            type = Me.CheckJailbreakTool(strExeFileName, strFileName)

            '3、下载越狱工具
            If type = OperateType.Download Then
                Me.SetButtonJailbreakEnabled(False)
                Me.DownloadJailbreakTool(strToolUrl, strFileName)
            End If

DO_EXIT:
            e.Result = type
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwStartJailbreak_DoWork")
        End Try
    End Sub

    Private Sub bgwStartJailbreak_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwStartJailbreak.RunWorkerCompleted
        Try
            Me.btnYueYu.Enabled = True
            Me.SetDownloadProgressText("")

            If e.Result Is Nothing Then
                Return
            End If

            Dim type As OperateType = CType(e.Result, OperateType)
            If type = OperateType.OpenCourse Then
                Me.OpenCourse()
            ElseIf type = OperateType.Download Then
                Me.btnYueYu.Enabled = False
                Me.SetDownloadProgressText(Me.Language.GetString("Welcome.Message.DownloadingJailTool"))            '"正在下载越狱工具..."
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwStartJailbreak_RunWorkerCompleted")
        End Try
    End Sub

    '检查越狱工具
    Private Function CheckJailbreakTool(ByVal strExeFileName As String, ByVal strFileName As String) As OperateType
        '参数一：exe启动路径；参数二：下载文件路径（如果下载文件的后缀为exe，则直接运行，否则需要解压）
        Dim type As OperateType = OperateType.None

        If String.IsNullOrEmpty(strExeFileName) OrElse String.IsNullOrEmpty(strFileName) Then
            type = OperateType.OpenCourse
            GoTo DO_EXIT
        End If

        Dim strExePath As String = String.Empty
        Dim strExtension As String = Path.GetExtension(strFileName).ToLower()

        '1、如果配置为exe文件
        If String.Compare(strExtension, ".exe") = 0 Then
            strExePath = Path.Combine(Me.mJailbreakPath, strFileName)
            If File.Exists(strExePath) Then
                Me.OpenJailbreakTool(strExePath)
                type = OperateType.OpenExe
            Else
                type = OperateType.Download
            End If

            GoTo DO_EXIT
        End If

        '2、如果配置为zip、rar文件
        Dim strExeFileFolder As String = Me.mJailbreakPath & "\" & Path.GetFileNameWithoutExtension(strFileName)
        strExePath = Me.GetExeFilePath(strExeFileFolder, strExeFileName)

        If Not String.IsNullOrEmpty(strExePath) AndAlso File.Exists(strExePath) Then
            Me.OpenJailbreakTool(strExePath)
            type = OperateType.OpenExe
        Else
            Dim strZipPath As String = Path.Combine(Me.mJailbreakPath, strFileName)
            If File.Exists(strZipPath) Then
                type = Me.UnzipJailbreakTool(strZipPath, strExeFileName)
            Else
                type = OperateType.Download
            End If
        End If

DO_EXIT:
        Return type
    End Function

    '解压zip文件
    Private Function UnzipJailbreakTool(ByVal strZipPath As String, ByVal strExeFileName As String) As OperateType
        Dim type As OperateType = OperateType.None

        Try
            If String.IsNullOrEmpty(strZipPath) OrElse String.IsNullOrEmpty(strExeFileName) Then
                type = OperateType.OpenCourse
                GoTo DO_EXIT
            End If

            Me.SetDownloadProgressText(Me.Language.GetString("Welcome.Message.UnzipJailbrealTool"))         '"解压越狱工具..."
            Me.SetButtonJailbreakEnabled(False)

            '解压目录
            Dim strExeFileFolder As String = Me.mJailbreakPath & "\" & Path.GetFileNameWithoutExtension(strZipPath)
            If Utility.unzip(strZipPath, strExeFileFolder) = 0 Then
                Common.LogException("文件路径：" & strZipPath, "解压失败")
                Try
                    File.Delete(strZipPath)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "删除无法解压的文件")
                End Try
            End If

            Me.SetDownloadProgressText("")
            Me.SetButtonJailbreakEnabled(True)

            Dim strExePath As String = Me.GetExeFilePath(strExeFileFolder, strExeFileName)
            If File.Exists(strExePath) Then
                Me.OpenJailbreakTool(strExePath)
                type = OperateType.OpenExe
            Else
                type = OperateType.Download
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "UnzipJailbreakTool")
        End Try

DO_EXIT:
        Return type
    End Function

#End Region

#Region "--- 结果处理 ---"

    '打开教程
    Private Sub OpenCourse()
        If Me.InvokeRequired Then
            Me.Invoke(New Threading.ThreadStart(AddressOf OpenCourse))
        Else
            'Me.mApplication.GotoItem(ActionFuncType.Jailbreak)
            Me.mApplication.GotoSite(ActionFuncType.Jailbreak, "Guide", "", "")
        End If
    End Sub

    '打开越狱工具
    Private Sub OpenJailbreakTool(ByVal strToolPath As String)
        Common.LogException("打开工具：" & strToolPath)

        If String.IsNullOrEmpty(strToolPath) OrElse Not File.Exists(strToolPath) Then
            Return
        End If

        Me.OpenCourse()

        Dim strAppName As String = Path.GetFileNameWithoutExtension(strToolPath)
        Dim arrProcess() As Process = Process.GetProcessesByName(strAppName)

        '1、如果不存在，则启动
        If arrProcess.Length = 0 Then
            Try
                Dim pro As New System.Diagnostics.Process
                pro.StartInfo.FileName = strToolPath
                pro.Start()
            Catch
            End Try

            Return
        End If

        '2、如果存在，则激活
        Try
            Dim process As Process = arrProcess(0)

            ShowWindow(process.MainWindowHandle, 9) '9，代表恢复
            SetActiveWindow(process.MainWindowHandle)
            SetForegroundWindow(process.MainWindowHandle)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetActiveWindow")
        End Try

    End Sub

    '下载越狱工具
    Private Sub DownloadJailbreakTool(ByVal strUrl As String, ByVal strFileName As String)
        Try
            If Me.mDownManage Is Nothing Then
                Me.mDownManage = MultiThreadDownload.Instance
            End If

            RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            AddHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
            RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
            AddHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded

            Dim lstItem As List(Of MultiThreadDownloadItem) = Me.mDownManage.GetItems(ResourceClass.Plugins, ResourceType.pEXE, TaskState.All)
            For Each Item As MultiThreadDownloadItem In lstItem
                If Item.ItemInfo.Url = strUrl Then
                    If Item.ItemInfo.Name = strFileName Then
                        Me.mDownManage.Start(Item)
                        Return
                    Else
                        Me.mDownManage.Delete(Item)
                        Exit For
                    End If
                End If
            Next

            Dim info As New MultiThreadDownloadItemInfo
            If String.IsNullOrEmpty(strUrl.Trim) = False Then

                '多线程下载
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))
                info.ListUrl.Add(New KeyValuePair(Of String, String)(strUrl, String.Empty))

                info.Class = ResourceClass.Plugins
                info.Type = ResourceType.pEXE
                info.Name = strFileName
                info.SaveFolder = Me.mJailbreakPath

                Me.mDownManage.NewTask(info)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadJailbreakTool")
        End Try
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Delegate Sub SetDownloadProgressTextHandler(ByVal strText As String)
    Private Sub SetDownloadProgressText(ByVal strText As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetDownloadProgressTextHandler(AddressOf SetDownloadProgressText), strText)
        Else
            Me.lblDownloadProgress.Visible = True
            Me.lblDownloadProgress.Text = strText
            Me.lblDownPluginProgress.Visible = True
            Me.lblDownPluginProgress.Text = strText
        End If
    End Sub

    Private Delegate Sub SetButtonJailbreakEnabledHandler(ByVal blnEnabled As Boolean)
    Private Sub SetButtonJailbreakEnabled(ByVal blnEnabled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetButtonJailbreakEnabledHandler(AddressOf SetButtonJailbreakEnabled), blnEnabled)
        Else
            Me.btnYueYu.Enabled = blnEnabled
        End If
    End Sub

    Private Function GetExeFilePath(ByVal strExeFileFolder As String, ByVal strExeFileName As String) As String
        Dim strExeFilePath As String = String.Empty
        If String.IsNullOrEmpty(strExeFileFolder) OrElse String.IsNullOrEmpty(strExeFileName) Then
            Return strExeFilePath
        End If

        If Not Directory.Exists(strExeFileFolder) Then
            Return strExeFilePath
        End If

        Dim appList As New List(Of String)
        appList.AddRange(My.Computer.FileSystem.GetFiles(strExeFileFolder, FileIO.SearchOption.SearchAllSubDirectories, "*.exe"))

        For Each Item As String In appList
            Dim strTempName As String = Path.GetFileName(Item)

            If String.Compare(strTempName, strExeFileName, True) = 0 Then
                strExeFilePath = Item
                Exit For
            End If
        Next

        Return strExeFilePath
    End Function

    Private Sub CopyFileFromAppFolder()
        Try
            Dim strToolPath As String = Path.Combine(Folder.AppFolder, "Jailbreak")

            If Not Directory.Exists(strToolPath) Then
                Return
            End If

            My.Computer.FileSystem.CopyDirectory(strToolPath, Me.mJailbreakPath, True)

            Directory.Delete(strToolPath, True)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CopyFileFromAppFolder")
        End Try
    End Sub

#End Region

#Region "--- 激活教程 ---"

    <DllImport("user32.dll", EntryPoint:="SendMessage")>
    Public Shared Function SendMessage(ByVal hWnd As IntPtr, ByVal wMsg As Integer, ByVal wParam As Integer, ByVal lParam As Integer) As Integer
    End Function

    <DllImport("user32.dll", CharSet:=CharSet.Auto, ExactSpelling:=True)>
    Public Shared Function GetForegroundWindow() As IntPtr
    End Function

    <DllImport("user32.dll")>
    Public Shared Function SetActiveWindow(ByVal hWnd As IntPtr) As IntPtr
    End Function

    <DllImport("user32.dll")>
    Public Shared Function SetForegroundWindow(ByVal hWnd As IntPtr) As IntPtr
    End Function

    <DllImport("user32.dll")>
    Public Shared Function ShowWindow(ByVal hWnd As IntPtr, ByVal nCmdShow As Integer) As IntPtr
    End Function

#End Region

#End Region

#Region "--- 电池管理 ---"

    Private Sub btnBatteryManager_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnBatteryManager.Click, lblChargingDetail.Click
#If IS_ITONG Then
        Try
            If Me.btnBatteryManager.tbShowDot Then
                IniSetting.SetShowBatteryManagerNew(False)
                Me.btnBatteryManager.tbShowDot = False
            End If

            For Each ctl As Form In Application.OpenForms
                If TypeOf ctl Is frmBatteryManager AndAlso Me.mDevice.Identifier.Equals(ctl.Name, StringComparison.OrdinalIgnoreCase) Then

                    mfrmBatteryManager = ctl
                    Exit For
                End If
            Next

            If mfrmBatteryManager Is Nothing OrElse mfrmBatteryManager.IsDisposed Then
                mfrmBatteryManager = New frmBatteryManager(Me.mApplication, Me.mDevice, Me._infoDevice, Me.mBatteryEfficiency)
                mfrmBatteryManager.StartPosition = FormStartPosition.CenterParent
            End If

            mfrmBatteryManager.Name = Me.mDevice.Identifier
            mfrmBatteryManager.Show(Me)
            mfrmBatteryManager.Activate()

            If mfrmBatteryManager.WindowState = FormWindowState.Minimized Then
                mfrmBatteryManager.WindowState = FormWindowState.Normal
            End If

            ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryBattery)

        Catch ex As Exception
            Common.LogException(ex.ToString, "btnBatteryManager_Click")
        End Try
#End If


#If IS_ITONG Then
        'If Utility.IsTestMode Then
        '    Try
        '        Dim frmfla As frmFlash = New frmFlash(Me.mApplication, Me.mDevice, Nothing)
        '        frmfla.Show(Me)

        'Dim frmfw As frmFirmware = New frmFirmware(Me.mApplication)
        '        frmfw.Show(Me)

        '    Catch ex As Exception
        '        Common.LogException(ex.ToString(), "btnBatteryManager_Click")
        '    End Try
        'End If
#End If

    End Sub

#End Region

#Region "--- 装机必备 ---"

#If IS_ITONG Then

    Private Sub OnLoadiPhoneHotApps(ByVal sender As Object, ByVal args As HotAppsEventArgs)
        Try
            If Me.mDevice Is Nothing OrElse Me.IsDisposed = True OrElse Me.IsHandleCreated = False Then
                Return
            End If

            If args.mStrSN = Me.mDevice.SerialNumber Then
                ShowHotApps()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnLoadiPhoneHotApps")
        End Try
    End Sub

    Private mLockerInvoke As New Object()
    Private Sub OnBeginInvoke(ByVal sender As Object, ByVal e As EventArgs)
        SyncLock mLockerInvoke
            Try
                For Each ctl As Form In Application.OpenForms
                    If TypeOf ctl Is frmHotApps AndAlso Me.mDevice.Identifier.Equals(ctl.Name, StringComparison.OrdinalIgnoreCase) Then
                        Me.mfrmHotApps = ctl
                        Exit For
                    End If
                Next

                If mfrmHotApps Is Nothing OrElse mfrmHotApps.IsDisposed Then
                    Me.mfrmHotApps = New frmHotApps(Me.mApplication, Me.mDevice, Me._infoDevice, Me.mIPhoneHotAppsHelper.DictFileSharingPackageInfo)
                    Me.mfrmHotApps.StartPosition = FormStartPosition.CenterParent

                End If

                Dim frmOwner As Form = ProcForm.Instance().GetMainForm()
                Utility.ShowFormInMiddleLocation(frmOwner, mfrmHotApps)

                Me.mfrmHotApps.Name = Me.mDevice.Identifier
                Me.mfrmHotApps.Show()

                Me.mIsShowHotApps = False

                If Me.mfrmHotApps.WindowState = FormWindowState.Minimized Then
                    Me.mfrmHotApps.WindowState = FormWindowState.Normal
                End If

                ActionCollectHelper.ShowSummaryHotApps(Me.mDevice, ModelKey.SummaryHotApps)
            Catch ex As Exception
                Debug.Write(ex)
            End Try

        End SyncLock
    End Sub

    Private Sub ShowHotApps()
        Try
            If Not My.Computer.Network.IsAvailable Then
                Return
            End If

            If Not mIsShowHotApps Then
                Return
            End If

            If FlashHelper.GetInstance(Me.mDevice).IsDoFlash Then
                Return
            End If

            If Me.mIPhoneHotAppsHelper.AppsCount > 10 OrElse Me.mIPhoneHotAppsHelper.AppsCount = -1 Then
                GoTo DOEXIT
            End If

            Me.BeginInvoke(New EventHandler(Of EventArgs)(AddressOf OnBeginInvoke), Nothing, Nothing)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowHotApps")
        End Try

DOEXIT:
        If Folder.LangType <> LanguageType.zh_CN OrElse Me.mDevice Is Nothing Then
            Return
        End If
        ''判断是否安装afc2
        'If Me.mDevice.Jailbreaked = False AndAlso Me.mDevice.InstallCydia Then
        '    If tbMessageBox.Show(Me.mApplication, LanguageInterface.Instance().GetString("WebSite.Message.InstallAFC2"), LanguageInterface.Instance().GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
        '        Common.OpenExplorer("http://news.tongbu.com/71765.html")
        '    End If
        '    Return
        'End If
    End Sub

#End If

#End Region

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.InitResources()
    End Sub

    Private Sub btnOutRecoverMode_Click(sender As Object, e As EventArgs) Handles btnOutRecoverMode.Click
        If Me.mIsRecoverMode Then
            If Me.mDeviceRecovery IsNot Nothing Then
                Me.mDeviceRecovery.ExitRecoveryMode()
            End If
        Else
            If Me.mDevice IsNot Nothing AndAlso
               Me.mDevice.IsConnected AndAlso
               MessageBox.Show(Me.Language.GetString("Welcome.Message.IfInRestoreMode"),
                                Me.Language.GetString("Welcome.Button.RestoreModesIn"),
                                MessageBoxButtons.YesNo, MessageBoxIcon.Question) = Windows.Forms.DialogResult.Yes Then
                '"此操作将会让您的手机进入到恢复模式！是否仍要继续？"
                '"进入恢复模式"
                Me.mDevice.EnterRecoveryMode()
                'Me.btn_EntryRecovery.Text = Me.Language.GetString("Welcome.Button.InRestoreingMode") '"正在进入恢复模式..."
                'Me.btn_EntryRecovery.Enabled = False
            End If
        End If
    End Sub

    Private Sub btnGotoFlashDevice_Click(sender As Object, e As EventArgs) Handles btnGotoFlashDevice.Click
        'Me.mApplication.GotoItem(ActionFuncType.Jailbreak)
        Me.mApplication.GotoSite(ActionFuncType.Jailbreak, "OnekeyFlash", "", "")
    End Sub

    Private Sub btnCopyECID_Click(sender As Object, e As EventArgs) Handles btnCopyDeviceId.Click
        Try
            Clipboard.SetDataObject(Me.lblDeviceIdValue.Text)

            If Not Me.tmrShowCopyInfo.Enabled Then
                Me.lblCopyInfo.Location = New Point(Me.btnCopyDeviceId.Location.X + Me.btnCopyDeviceId.Width, Me.btnCopyDeviceId.Location.Y)
                Me.lblCopyInfo.Visible = True
                Me.tmrShowCopyInfo.Start()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnCopyECID_Click")
        End Try
    End Sub

    Private Sub tmrShowCopyInfo_Tick(sender As Object, e As EventArgs) Handles tmrShowCopyInfo.Tick
        Me.mTickCount = Me.mTickCount + 1

        If Me.mTickCount = 3 Then
            Me.tmrShowCopyInfo.Stop()
            Me.ChangelblCopyInfoState(False)
            Me.mTickCount = 0
        End If
    End Sub
    Private Delegate Sub ChangelblCopyInfoStateHandler(ByVal blnVisible As Boolean)
    Private Sub ChangelblCopyInfoState(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New ChangelblCopyInfoStateHandler(AddressOf ChangelblCopyInfoState), blnVisible)
        Else
            Me.lblCopyInfo.Visible = blnVisible
        End If
    End Sub

    Private Sub DoTuiUpdateCollection()
        Try
            If Me.mDevice Is Nothing Then
                Return
            End If

            Dim tuiInfo As FileSharingPackageInfo = Nothing

            '获取已安装的IPA软件列表
            Dim dict As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            If dict.Count = 0 Then
                '如果加载失败则先暂停下再继续加载
                Utility.WaitSeconds(0.2)
                dict = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            End If
            If dict.Count = 0 Then
                Return
            End If
            For Each info As FileSharingPackageInfo In dict.Values
                If info.IsTui AndAlso info.CrackedInfo = CrakedInfo.Enterprise Then
                    tuiInfo = info
                    Exit For
                End If
            Next
            If tuiInfo Is Nothing Then
                Return
            End If

            Dim longVersionNumber As Long = 0

            If tuiInfo.Version.Length > 0 Then
                Dim arrVer As String() = tuiInfo.Version.Split(New Char() {"."c})
                If arrVer.Length > 0 Then
                    longVersionNumber = Convert.ToInt32(arrVer(0)) * 100
                End If
                If arrVer.Length > 1 Then
                    longVersionNumber += Convert.ToInt32(arrVer(1)) * 10
                End If
                If arrVer.Length > 2 Then
                    longVersionNumber += Convert.ToInt32(arrVer(2))
                End If
            End If

            If tuiInfo.CrackedInfo = CrakedInfo.Enterprise AndAlso
                ((tuiInfo.TuiType = tbTuiType.TuiHD AndAlso longVersionNumber > 302) OrElse (tuiInfo.TuiType = tbTuiType.Tui AndAlso longVersionNumber > 382)) Then

                Dim dtTime As DateTime = Me.mDevice.TuiEnterpriseShowDate(False)
                Dim tSpan As TimeSpan = Date.Now - dtTime
                If dtTime = DateTime.MinValue Then
                    ActionCollectHelper.OpenTui(Me.mDevice, ModelKey.TuiUnOpen, ActionDataType.View, FunctionSucceed.Succeed, "") '未打开
                ElseIf tSpan.Days < 3 Then
                    ActionCollectHelper.OpenTui(Me.mDevice, ModelKey.TuiOpenToTrid, ActionDataType.View, FunctionSucceed.Succeed, "") '三天内(当前算第一天)
                Else
                    ActionCollectHelper.OpenTui(Me.mDevice, ModelKey.TuiOpenToTrid, ActionDataType.View, FunctionSucceed.Failure, "") '三天以外
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmSummary_DoTuiUpdateCollection")
        End Try
    End Sub

    Private Sub DoCollectApp()
        Try
            '获取已安装的IPA软件列表
            Dim dict As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User, False)
            If dict.Count = 0 Then
                '如果加载失败则先暂停下再继续加载
                Utility.WaitSeconds(3)
                dict = Me.mDevice.InstalledApplications(ApplicationType.User, True)
            End If

            Dim isWhatsApp As Boolean = False
            Dim isLineApp As Boolean = False
            Dim isKikApp As Boolean = False
            For Each strIdentifier As String In dict.Keys
                If strIdentifier = "net.whatsapp.WhatsApp" Then
                    isWhatsApp = True
                End If

                If strIdentifier = "jp.naver.line" Then
                    isLineApp = True
                End If

                If strIdentifier = "com.kik.chat" Then
                    isKikApp = True
                End If
            Next

            Dim strMsg As String = String.Format("{0};{1};{2};{3}", Me.mDevice.Identifier, isWhatsApp, isLineApp, isKikApp)
            ActionCollectHelper.WeChatFindData(Me.mDevice, ModelKey.AppInstallWLK, ModelKey.AppInstallWLK, ActionDataType.Search, FunctionSucceed.Succeed, strMsg)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoCollectApp")
        End Try
    End Sub

#Region "--- 取得手机价格 ---"

    Private Function GetDevicePrice() As String
        Return HtmlHelper.GetDevicePrice("iOS", SummaryInfo.FormatProductNoDetail(Me.mDevice.ProductType))
    End Function

    Private Sub btnPrice_Click(sender As Object, e As EventArgs)
        '#If IS_ITONG Then
        '        Dim frmPrice As New frmDevicePrice(Me.mApplication)
        '        frmPrice.Show(Me)
        '        ActionCollectHelper.DevicePrice(Me.mDevice)
        '#End If
    End Sub

#End Region

#Region "关闭重启手机"

    Private Sub btnRestarDevice_Click(sender As Object, e As EventArgs) Handles btnRestarDevice.Click
        If tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.RestarDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            'Me.mDevice.Restart()
            Me.OperatorDevice("Restar")
        End If
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.Restar)
    End Sub

    Private Sub btnCloseDevice_Click(sender As Object, e As EventArgs) Handles btnCloseDevice.Click
        If tbMessageBox.Show(Me, Me.Language.GetString("Main.Message.ShurtDown"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
            'Me.mDevice.Shutdown()
            Me.OperatorDevice("ShurtDown")
        End If
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.ClosePhone)
    End Sub

    Private Sub OperatorDevice(ByVal strCommand As String)
        Dim thr As New Thread(New ParameterizedThreadStart(AddressOf OperatorDeviceThread))
        thr.IsBackground = True
        thr.Start(strCommand)
    End Sub

    Private Sub OperatorDeviceThread(ByVal strCommand As Object)
        If strCommand Is Nothing OrElse Not TypeOf strCommand Is String Then
            Return
        End If
        Select Case strCommand
            Case "Restar"
                Me.mDevice.Restart()

            Case "ShurtDown"
                Me.mDevice.Shutdown()

        End Select
    End Sub

#End Region

#Region "iTunes自启动"

    Private mThrOpeniTunes As Thread

    Private Sub OpeniTunes(ByVal blnSetValue As Boolean, ByVal blnLoadStatus As Boolean)
        Try
            If Me.mThrOpeniTunes IsNot Nothing AndAlso Me.mThrOpeniTunes.ThreadState <> ThreadState.Stopped Then
                Me.mThrOpeniTunes.Abort()
            End If

            Dim objPara() As Object = New Object() {blnSetValue, blnLoadStatus}

            Me.mThrOpeniTunes = New Thread(New ParameterizedThreadStart(AddressOf OpeniTunesThread))
            Me.mThrOpeniTunes.IsBackground = True
            Me.mThrOpeniTunes.Start(objPara)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OpeniTunes")
        End Try
    End Sub

    Private Sub OpeniTunesThread(ByVal obj As Object)
        Try
            Dim blnSetValue As Boolean = obj(0)
            Dim blnLoadStatus As Boolean = obj(1)

            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then

                If blnSetValue Then
                    Dim blnPrefs As Boolean = Me.mDevice.GetiTunesPrefs
                    Dim blnResult As Boolean = Me.mDevice.SetiTunesPrefs(Not blnPrefs)
                    Dim strMsg As String = ""

                    If blnPrefs Then
                        strMsg = ""
                        If Not blnResult Then
                            strMsg = Me.Language.GetString("Main.Message.CloseiTunesFailure")               '"iTunes 自启动关闭失败！"
                        End If
                    Else
                        If Not blnResult Then
                            strMsg = Me.Language.GetString("Main.Message.OpeniTunesFailure")                '"iTunes 自启动打开失败！"
                        End If
                    End If
                    Me.ShowMsg(strMsg)
                End If

                If blnSetValue OrElse blnLoadStatus Then
                    Me.SetiTunesOpenStatus()
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub ShowMsgHandler(ByVal strMsg As String)
    Private Sub ShowMsg(ByVal strMsg As String)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowMsgHandler(AddressOf ShowMsg), strMsg)
        Else
            If strMsg.Length > 0 Then
                tbSplashBox.ShowMessage(strMsg, Me, 1)
            End If
        End If

    End Sub

    Private Sub SetiTunesOpenStatus()
        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsConnected Then
            Dim blnPrefs As Boolean = Me.mDevice.GetiTunesPrefs
            Me.SetOpeniTunesStatus(blnPrefs)
        End If
    End Sub

    Private Delegate Sub SetOpeniTunesStatusHandler(ByVal blnPrefs As Boolean)
    Private Sub SetOpeniTunesStatus(ByVal blnPrefs As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetOpeniTunesStatusHandler(AddressOf SetOpeniTunesStatus), blnPrefs)
        Else
            Me.cbxCloseiTunesStart.Checked = Not blnPrefs
        End If
    End Sub

#End Region

#Region "显示设备信息到首页"

    Private Sub SetDeviceInfoLoading()
        Me.lblSaleAreaValue.Text = Me.Language.GetString("Common.Loading") & "..."
        Me.lblProductDateValue.Text = Me.Language.GetString("Common.Loading") & "..."
        Me.lblGuaranteeDateValue.Text = Me.Language.GetString("Common.Loading") & "..."
        Me.lblChargingTimesValue.Text = Me.Language.GetString("Common.Loading") & "..."
        Me.lblChargingPercentValue.Text = Me.Language.GetString("Common.Loading") & "..."
        Me.lblIMEIValue.Text = Me.Language.GetString("Common.Loading") & "..."
    End Sub

    Private Sub ShowDevieInfoJail(ByVal intDiskSize As Integer)
        Try
            Dim strJail As String = String.Empty
            With Me._infoDevice
                If .IsDeviceExist Then
                    If .IsJailbreak Then
                        If .IsAppSyncInstalled Then
                            '"已越狱"
                            strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("Welcome.Label.InstalledAppSync"), "")  '"已安装AppSync"
                        Else
                            '"已越狱"
                            strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("Welcome.Label.UnInstallAppSync"), "")  '"未安装AppSync"
                        End If
                    ElseIf .IsCydiaInstalled Then
                        '"已越狱"
                        strJail = Me.Language.GetString("File.Label.Jailbreaked") & IIf(Not Folder.LangType = LanguageType.en_US, ", " & Me.Language.GetString("File.Label.NoInstallAfc2"), "")           '"未安装afc2补丁"
                    Else
                        strJail = Me.Language.GetString("Welcome.Label.UnJailbreaked")  '"未越狱"
                    End If
                End If
            End With

            If String.IsNullOrEmpty(Me._infoDevice.DeviceColor) Then
                Me._infoDevice.DeviceColor = "-1"
            End If

            If intDiskSize > 0 Then
                Me.lblDeviceColor.Text = String.Format("{0} {1}G {2} {3}", SummaryInfo.FormatProduct(_infoDevice.ProductType), intDiskSize.ToString(), FormatDeviceColor(Me.mDevice, Me._infoDevice.DeviceColor, Me.Language), strJail)
            Else
                Me.lblDeviceColor.Text = String.Format("{0} {1} {2}", SummaryInfo.FormatProduct(_infoDevice.ProductType), FormatDeviceColor(Me.mDevice, Me._infoDevice.DeviceColor, Me.Language), strJail)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowDevieInfoJail")
        End Try
    End Sub

    Private Sub ShowDevieInfo()
        If Me.InvokeRequired Then
            Me.BeginInvoke(New ThreadStart(AddressOf ShowDevieInfo))
        Else
            Try
                Me.ShowDevieInfoJail(Me.mDiskSize)
                Me.lblIMEIValue.Text = Me.GetValue(Me._infoDevice.InternationalMobileEquipmentIdentity)
                Me.lblSaleAreaValue.Text = Me.GetValue(Me._infoDevice.SalesAreaValue)
                Me.lblProductDateValue.Text = Me.GetValue(Me._infoDevice.ProductDateString)
                Dim strCovEndDate As String = ""
                If Me._infoDevice.SalesInfo IsNot Nothing Then
                    strCovEndDate = Me._infoDevice.SalesInfo.CovEndDate
                End If
                Me.lblGuaranteeDateValue.Text = Me.GetValue(strCovEndDate)
                Me.OpeniTunes(False, True)
                Me.LoadBatteryInfo()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "ShowDevieInfo")
            End Try
        End If
    End Sub

    Private Function GetValue(ByVal strValue As String) As String
        Dim strReturn As String = Me.Language.GetString("Common.Label.Unknow")          '"未知"
        If strValue.Length > 0 Then
            strReturn = strValue
        End If
        Return strReturn
    End Function

#End Region

#Region "加载电池信息"

    Private mThrLoadBattery As Thread = Nothing

    Private Sub LoadBatteryInfo()
        Try
            If Me.mThrLoadBattery IsNot Nothing AndAlso Me.mThrLoadBattery.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadBattery.Abort()
            End If

            Me.mThrLoadBattery = New Thread(AddressOf LoadBatteryInfoThread)
            Me.mThrLoadBattery.IsBackground = True
            Me.mThrLoadBattery.Start()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadBatteryInfo")
        End Try
    End Sub

    Private Sub LoadBatteryInfoThread()
        Try
            Dim obj As Dictionary(Of Object, Object) = Nothing
            Dim intCount As Integer = 0

            While obj Is Nothing
                obj = Me.mDevice.GetIORegistryInfo()
                intCount += 1
                If obj IsNot Nothing Then
                    Exit While
                End If

                If intCount > 4 Then
                    Return
                End If
                Utility.WaitSeconds(3)
            End While

            Dim strChargeRecordCount As String = String.Empty

            For Each item As KeyValuePair(Of Object, Object) In obj
                If item.Key.ToString().ToLower() = "cyclecount" Then
                    strChargeRecordCount = item.Value
                    Exit For
                End If
            Next

            'Dim intBatteryCurrentCapacity As Integer = Me.mDevice.BatteryCurrentCapacity()
            Dim intBatteryCurrentCapacity As Integer = Me.LoadBatteryEfficiency()
            Me.mBatteryEfficiency = intBatteryCurrentCapacity
            Me.SetBatteryInfo(strChargeRecordCount, intBatteryCurrentCapacity)

            '如果取出来的值为负数就再取一次
            If intBatteryCurrentCapacity < 0 Then
                Dim intReadCount As Integer = 0
                While intBatteryCurrentCapacity < 0
                    intReadCount = intReadCount + 1
                    Utility.WaitSeconds(10)
                    intBatteryCurrentCapacity = Me.mDevice.BatteryCurrentCapacity()
                    Me.SetBatteryInfo(strChargeRecordCount, intBatteryCurrentCapacity)
                    '取两次，如果都失败就不处理了
                    If intReadCount > 2 Then
                        Exit While
                    End If
                End While
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadBatteryInfoThread")
        End Try
    End Sub

    Public Function LoadBatteryEfficiency()
        Dim strEfficiency As String = String.Empty
        Try
            Dim obj As Dictionary(Of Object, Object) = Nothing
            Dim intCount As Integer = 0

            While obj Is Nothing
                obj = Me.mDevice.GetIORegistryInfo()
                intCount += 1
                If obj IsNot Nothing Then
                    Exit While
                End If

                If intCount > 4 Then
                    Return strEfficiency
                End If
                Utility.WaitSeconds(3)
            End While

            Dim actualCapacity As Integer
            Dim intDesignCapacity As Integer
            For Each item As KeyValuePair(Of Object, Object) In obj
                If item.Key.ToString().ToLower() = "batterydata" Then
                    Dim dicBatteryData As Dictionary(Of Object, Object) = CType(item.Value, Dictionary(Of Object, Object))
                    For Each batteryItem As KeyValuePair(Of Object, Object) In dicBatteryData
                        If batteryItem.Key.ToString().ToLower() = "maxcapacity" Then
                            actualCapacity = batteryItem.Value

                        ElseIf batteryItem.Key.ToString().ToLower() = "designcapacity" Then
                            intDesignCapacity = batteryItem.Value

                        End If
                    Next
                End If
            Next

            If actualCapacity > 0 AndAlso intDesignCapacity > 0 Then
                Dim doubleEfficiency As Double = actualCapacity / intDesignCapacity
                If doubleEfficiency > 100 Then
                    doubleEfficiency = 99
                End If
                If actualCapacity > 0 AndAlso actualCapacity <= 100 Then
                    doubleEfficiency = actualCapacity
                End If
                strEfficiency = Format(doubleEfficiency, "0.00") * 100
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadBatteryEfficiency")
        End Try
        Return strEfficiency
    End Function

    Private Delegate Sub SetBatteryInfoHandler(ByVal strCount As String, ByVal strPercent As String)
    Private Sub SetBatteryInfo(ByVal strCount As String, ByVal strPercent As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetBatteryInfoHandler(AddressOf SetBatteryInfo), strCount, strPercent)
        Else
            If strPercent < 0 Then
                strPercent = 0
            End If
            If strPercent > 100 Then
                strPercent = 100
            End If
            Me.lblChargingTimesValue.Text = strCount
            Me.lblChargingPercentValue.Text = String.Format("{0}%", strPercent)
        End If
    End Sub

#End Region

#Region "控件事件"

    Private Sub btnQQ_Click(sender As Object, e As EventArgs) Handles btnQQ.Click, btnQQChat.Click
        Try
            If ServerIniSetting.GetShowiSafeFoneRecommendCN() Then
                IniSetting.SetiSafeFoneRecommendCNVersion()
                btnQQChat.tbShowNew = False
            End If
        Catch
        End Try

        Try
            Me.btnQQ.tbShowNew = False
            IniSetting.SetShowQQNew(False)

#If Not IS_ITONG_LITE AndAlso Not IS_ITONG_ZJ Then
            TBGuideHelper.OpenTBGuideByQQ(Me)
#End If

            ActionCollectHelper.OpenTBGuideByQQ(Me.mDevice, ModelKey.SummaryOpenTBGuideByQQ, ModelKey.SummaryOpenTBGuideByQQ, ActionDataType.Click, FunctionSucceed.None)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnQQ_Click")
        End Try
    End Sub

    Private Sub btnCanceliOSUpgrade_Click(sender As Object, e As EventArgs) Handles btnCanceliOSUpgrade.Click, lblCloseiOSUpdate.Click
#If IS_ITONG Then
        Try
            For Each ctl As Form In Application.OpenForms
                If TypeOf ctl Is frmCanceliOSUpgrade AndAlso Me.mDevice.Identifier.Equals(ctl.Name, StringComparison.OrdinalIgnoreCase) Then
                    mfrmCanceliOSUpgrade = ctl
                    Exit For

                End If
            Next

            If mfrmCanceliOSUpgrade Is Nothing OrElse mfrmCanceliOSUpgrade.IsDisposed Then
                mfrmCanceliOSUpgrade = New frmCanceliOSUpgrade(Me.mApplication, Me.mDevice)
                mfrmCanceliOSUpgrade.StartPosition = FormStartPosition.CenterParent
            End If

            If Me.mfrmCanceliOSUpgrade.Name <> Me.mDevice.Identifier Then
                mfrmCanceliOSUpgrade.Name = Me.mDevice.Identifier
                mfrmCanceliOSUpgrade.Show(Me)
                mfrmCanceliOSUpgrade.Activate()
            End If

            If mfrmCanceliOSUpgrade.WindowState = FormWindowState.Minimized Then
                mfrmCanceliOSUpgrade.WindowState = FormWindowState.Normal
            End If
            ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryiOSCancelUpgrad)
        Catch ex As Exception
            Common.LogException(ex.ToString, "btnCanceliOSUpgrade_Click")
        End Try
#End If
    End Sub

    Private Sub ChangeDeviceNameEx(Optional ByVal IsCancel As Boolean = False)
        If Not Me.txtDeviceName.Visible Then
            Return
        End If

        Debug.Print(Me.txtDeviceName.Text & vbTab & Me.txtDeviceName.Text.Length)

        Dim strText As String = Me.mDevice.DeviceName
        If Not IsCancel Then
            strText = Me.txtDeviceName.Text.Trim()
            If strText.Length > 0 AndAlso strText <> Me.lblDeviceName.Text Then
                Me.mDevice.DeviceName = strText

                Dim frm As MainForm = Me.ParentForm.ParentForm
                frm.UpdateTopDeviceButtonText(Me.mDevice.DeviceID.ToLower(), strText)
            End If
        End If

        Me.lblDeviceName.Visible = True
        Me.btnEditDeviceName.Visible = True
        Me.txtDeviceName.Visible = False
        CType(Me.mApplication, MainForm).Text = Me.Language.GetString("Main.Text.Name") & String.Format(" - {0}", strText)
    End Sub

    Private Sub btnEditDeviceName_Click(sender As Object, e As EventArgs) Handles btnEditDeviceName.Click
        Me.lblDeviceName.Visible = False
        Me.btnEditDeviceName.Visible = False

        Me.txtDeviceName.Visible = True
        Me.txtDeviceName.Size = New Size(Me.lblDeviceName.Width + 30, Me.txtDeviceName.Height)
        Me.txtDeviceName.Location = New Point(Me.lblDeviceName.Left + 2, Me.lblDeviceName.Bottom - Me.txtDeviceName.Height)

        Me.txtDeviceName.Enabled = True
        Me.txtDeviceName.ReadOnly = False
        Me.txtDeviceName.Text = ""
        Me.txtDeviceName.Text = Me.lblDeviceName.Text.ToString()
        Me.txtDeviceName.Focus()
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryReName)
    End Sub

    Private Sub txtDeviceName_KeyDown(sender As Object, e As KeyEventArgs) Handles txtDeviceName.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.ChangeDeviceNameEx()
        End If
    End Sub

    Private Sub txtDeviceName_Validating(sender As Object, e As System.ComponentModel.CancelEventArgs) Handles txtDeviceName.Validating
        Me.ChangeDeviceNameEx()
    End Sub

    Private Sub lblDeviceName_TextChanged(sender As Object, e As EventArgs) Handles lblDeviceName.TextChanged
        Dim strSize As SizeF = Me.CreateGraphics.MeasureString(Me.lblDeviceName.Text, Me.lblDeviceName.Font)
        Dim intWedit As Integer = 200

        If strSize.Width > intWedit Then
            Me.lblDeviceName.tbAutoSize = False
            Me.lblDeviceName.tbAutoEllipsis = True
            Me.lblDeviceName.Width = intWedit
        Else
            Me.lblDeviceName.tbAutoSize = True
            Me.lblDeviceName.tbAutoEllipsis = True
        End If
        Me.lblDeviceName.Height = 30
    End Sub

    Private Sub lblDeviceName_SizeChanged(sender As Object, e As EventArgs) Handles lblDeviceName.SizeChanged
        Me.SetDeviceNameLabelLocation()
    End Sub

    Private Sub SetDeviceNameLabelLocation()
        Me.lblDeviceName.Location = New Point(Me.picDevice.Left + (Me.picDevice.Width - Me.lblDeviceName.Width - 40) / 2, Me.lblDeviceName.Top)
        Me.btnEditDeviceName.Location = New Point(Me.lblDeviceName.Right + 5, Me.btnEditDeviceName.Top)
        Me.btnDeviceInfo.Location = New Point(Me.btnEditDeviceName.Right + 8, Me.btnDeviceInfo.Top)
    End Sub

    Private Sub pnlSummary_Click(sender As Object, e As EventArgs) Handles pnlSummary.Click
        Me.ChangeDeviceNameEx()
    End Sub

    Private Sub btnRingtone_Click(sender As Object, e As EventArgs) Handles btnRingtone.Click
        RunApp(Me.mApplication, Me.mDevice, GetType(RingMakerForm), Nothing, False, AloneBackupType.None)
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.RingMacker)
    End Sub

    Private Sub btnZXKF_Click(sender As Object, e As EventArgs) Handles btnZXKF.Click
        Try
            If ServerIniSetting.GetShowZXFKCN() Then
                IniSetting.SetZXFKCNVersion()
                btnZXKF.tbShowNew = False
            End If
        Catch
        End Try
        Common.OpenExplorer(ServerIniSetting.GetGuideZXFKUrlCN())
    End Sub

    Private Sub btniOSDowngrade_Click(sender As Object, e As EventArgs) Handles btniOSDowngrade.Click
        Me.mApplication.GotoSite(ActionFuncType.Jailbreak, "", "", "")
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.SummaryiOSDowngrade)
    End Sub

    Private Sub cbxCloseiTunesStart_Click(sender As Object, e As EventArgs) Handles cbxCloseiTunesStart.Click
        Me.OpeniTunes(True, True)
    End Sub

    Private Sub btnBackup_Click(sender As Object, e As EventArgs) Handles lblBackup.Click
        RunApp(Me.mApplication, Me.mDevice, GetType(frmBackupList), Nothing, True, AloneBackupType.None)
        ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.BackupRestore)
    End Sub

    Private Sub btnSNValue_Click(sender As Object, e As EventArgs) Handles lblSNValue.Click, lblIMEIValue.Click, lblDeviceIdentifyValue.Click
        Try
            If sender IsNot Nothing Then
                Dim strVal As String = CType(sender, tbButton).Text
                Clipboard.SetDataObject(strVal)
                Me.ShowMsg("成功复制到剪切板！")
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnSNValue_Click")
        End Try
    End Sub

    Private Sub picDevice_SizeChanged(sender As Object, e As EventArgs) Handles picDevice.SizeChanged
        Dim intLeft As Integer = Me.pnlDeviceInfo.Left - 68 - Me.picDevice.Width
        If Me.picDevice.Width > 202 Then
            intLeft = Me.pnlDeviceInfo.Left - 45 - Me.picDevice.Width
        End If
        Me.picDevice.Left = intLeft
        Me.SetDeviceNameLabelLocation()
        Me.InitScreensortLocation()
    End Sub

    Private Sub btnScreensort1_SizeChanged(sender As Object, e As EventArgs) Handles btnScreensort1.SizeChanged, btnDeviceDesktop.SizeChanged, btnRestarDevice.SizeChanged, btnCloseDevice.SizeChanged
        Me.InitScreensortLocation()
    End Sub

    Private Sub btnCharging_SizeChanged(sender As Object, e As EventArgs) Handles lblCloseiOSUpdate.SizeChanged, lblChargingDetail.SizeChanged, btnMore.SizeChanged
        Me.lblCloseiOSUpdate.Location = New Point(Me.pnlDeviceInfo.Width - Me.lblCloseiOSUpdate.Width - 20, Me.lblCloseiOSUpdate.Top)
        Me.lblChargingDetail.Location = New Point(Me.pnlDeviceInfo.Width - Me.lblChargingDetail.Width - 20, Me.lblChargingDetail.Top)
        Me.btnMore.Location = New Point(Me.pnlDeviceInfo.Width - Me.btnMore.Width - 22, Me.btnMore.Top)
    End Sub

#End Region

End Class