﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmTuiInstallTutorial
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmTuiInstallTutorial))
        Me.btn_close = New iTong.Components.tbButton()
        Me.lblSetp2 = New System.Windows.Forms.Label()
        Me.lblSetp1 = New System.Windows.Forms.Label()
        Me.lblSetp4 = New System.Windows.Forms.Label()
        Me.lblSetp3 = New System.Windows.Forms.Label()
        Me.lblSetpDescription1 = New System.Windows.Forms.Label()
        Me.lblSetpDescription2 = New System.Windows.Forms.Label()
        Me.lblSetpDescription3 = New System.Windows.Forms.Label()
        Me.lblSetpDescription4 = New System.Windows.Forms.Label()
        Me.picTuiTutorial = New System.Windows.Forms.PictureBox()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.lblClickMainForm = New System.Windows.Forms.Label()
        Me.btnOK = New iTong.Components.tbButton()
        Me.lblTitleDescription = New System.Windows.Forms.Label()
        Me.lblTitle = New System.Windows.Forms.Label()
        Me.chkNoShow = New iTong.Components.tbCheckBox()
        CType(Me.picTuiTutorial, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlContainer.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(805, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 11
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'lblSetp2
        '
        Me.lblSetp2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetp2.AutoSize = True
        Me.lblSetp2.BackColor = System.Drawing.Color.Transparent
        Me.lblSetp2.ForeColor = System.Drawing.Color.White
        Me.lblSetp2.Location = New System.Drawing.Point(318, 237)
        Me.lblSetp2.Name = "lblSetp2"
        Me.lblSetp2.Size = New System.Drawing.Size(41, 12)
        Me.lblSetp2.TabIndex = 79
        Me.lblSetp2.Text = "第二步"
        Me.lblSetp2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblSetp1
        '
        Me.lblSetp1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetp1.AutoSize = True
        Me.lblSetp1.BackColor = System.Drawing.Color.Transparent
        Me.lblSetp1.ForeColor = System.Drawing.Color.White
        Me.lblSetp1.Location = New System.Drawing.Point(318, 170)
        Me.lblSetp1.Name = "lblSetp1"
        Me.lblSetp1.Size = New System.Drawing.Size(41, 12)
        Me.lblSetp1.TabIndex = 78
        Me.lblSetp1.Text = "第一步"
        Me.lblSetp1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblSetp4
        '
        Me.lblSetp4.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetp4.AutoSize = True
        Me.lblSetp4.BackColor = System.Drawing.Color.Transparent
        Me.lblSetp4.ForeColor = System.Drawing.Color.White
        Me.lblSetp4.Location = New System.Drawing.Point(318, 371)
        Me.lblSetp4.Name = "lblSetp4"
        Me.lblSetp4.Size = New System.Drawing.Size(41, 12)
        Me.lblSetp4.TabIndex = 81
        Me.lblSetp4.Text = "第四步"
        Me.lblSetp4.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblSetp3
        '
        Me.lblSetp3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetp3.AutoSize = True
        Me.lblSetp3.BackColor = System.Drawing.Color.Transparent
        Me.lblSetp3.ForeColor = System.Drawing.Color.White
        Me.lblSetp3.Location = New System.Drawing.Point(318, 304)
        Me.lblSetp3.Name = "lblSetp3"
        Me.lblSetp3.Size = New System.Drawing.Size(41, 12)
        Me.lblSetp3.TabIndex = 80
        Me.lblSetp3.Text = "第三步"
        Me.lblSetp3.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblSetpDescription1
        '
        Me.lblSetpDescription1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetpDescription1.BackColor = System.Drawing.Color.Transparent
        Me.lblSetpDescription1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer))
        Me.lblSetpDescription1.Location = New System.Drawing.Point(389, 173)
        Me.lblSetpDescription1.Name = "lblSetpDescription1"
        Me.lblSetpDescription1.Size = New System.Drawing.Size(356, 48)
        Me.lblSetpDescription1.TabIndex = 82
        Me.lblSetpDescription1.Text = "打开已安装的应用时，出现以下情况，首先记住划线部分的内容；"
        '
        'lblSetpDescription2
        '
        Me.lblSetpDescription2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetpDescription2.BackColor = System.Drawing.Color.Transparent
        Me.lblSetpDescription2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer))
        Me.lblSetpDescription2.Location = New System.Drawing.Point(389, 240)
        Me.lblSetpDescription2.Name = "lblSetpDescription2"
        Me.lblSetpDescription2.Size = New System.Drawing.Size(356, 48)
        Me.lblSetpDescription2.TabIndex = 83
        Me.lblSetpDescription2.Text = "然后在设备中点击【设置】->【通用】，滑到最下方->【描述文件】；"
        '
        'lblSetpDescription3
        '
        Me.lblSetpDescription3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetpDescription3.BackColor = System.Drawing.Color.Transparent
        Me.lblSetpDescription3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer))
        Me.lblSetpDescription3.Location = New System.Drawing.Point(389, 307)
        Me.lblSetpDescription3.Name = "lblSetpDescription3"
        Me.lblSetpDescription3.Size = New System.Drawing.Size(356, 48)
        Me.lblSetpDescription3.TabIndex = 84
        Me.lblSetpDescription3.Text = "找到刚才记住的同名描述文件，点击进入；"
        '
        'lblSetpDescription4
        '
        Me.lblSetpDescription4.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSetpDescription4.AutoSize = True
        Me.lblSetpDescription4.BackColor = System.Drawing.Color.Transparent
        Me.lblSetpDescription4.ForeColor = System.Drawing.Color.FromArgb(CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer))
        Me.lblSetpDescription4.Location = New System.Drawing.Point(389, 374)
        Me.lblSetpDescription4.Name = "lblSetpDescription4"
        Me.lblSetpDescription4.Size = New System.Drawing.Size(125, 12)
        Me.lblSetpDescription4.TabIndex = 85
        Me.lblSetpDescription4.Text = "点击信任该描述文件。"
        '
        'picTuiTutorial
        '
        Me.picTuiTutorial.BackColor = System.Drawing.Color.Transparent
        Me.picTuiTutorial.Image = Global.iTong.My.Resources.Resources.gif_installtui
        Me.picTuiTutorial.Location = New System.Drawing.Point(70, 68)
        Me.picTuiTutorial.Name = "picTuiTutorial"
        Me.picTuiTutorial.Size = New System.Drawing.Size(200, 355)
        Me.picTuiTutorial.TabIndex = 86
        Me.picTuiTutorial.TabStop = False
        '
        'pnlContainer
        '
        Me.pnlContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContainer.BackColor = System.Drawing.Color.FromArgb(CType(CType(227, Byte), Integer), CType(CType(230, Byte), Integer), CType(CType(236, Byte), Integer))
        Me.pnlContainer.Controls.Add(Me.chkNoShow)
        Me.pnlContainer.Controls.Add(Me.lblClickMainForm)
        Me.pnlContainer.Controls.Add(Me.btnOK)
        Me.pnlContainer.Controls.Add(Me.lblTitleDescription)
        Me.pnlContainer.Controls.Add(Me.lblTitle)
        Me.pnlContainer.Controls.Add(Me.picTuiTutorial)
        Me.pnlContainer.Controls.Add(Me.lblSetp1)
        Me.pnlContainer.Controls.Add(Me.lblSetpDescription4)
        Me.pnlContainer.Controls.Add(Me.lblSetp2)
        Me.pnlContainer.Controls.Add(Me.lblSetpDescription3)
        Me.pnlContainer.Controls.Add(Me.lblSetp3)
        Me.pnlContainer.Controls.Add(Me.lblSetpDescription2)
        Me.pnlContainer.Controls.Add(Me.lblSetp4)
        Me.pnlContainer.Controls.Add(Me.lblSetpDescription1)
        Me.pnlContainer.Location = New System.Drawing.Point(1, 32)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(828, 567)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 87
        Me.pnlContainer.tbBackgroundImage = Global.iTong.My.Resources.Resources.tui_tutorial_bg
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblClickMainForm
        '
        Me.lblClickMainForm.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblClickMainForm.AutoSize = True
        Me.lblClickMainForm.BackColor = System.Drawing.Color.Transparent
        Me.lblClickMainForm.ForeColor = System.Drawing.Color.FromArgb(CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer))
        Me.lblClickMainForm.Location = New System.Drawing.Point(321, 423)
        Me.lblClickMainForm.Name = "lblClickMainForm"
        Me.lblClickMainForm.Size = New System.Drawing.Size(293, 12)
        Me.lblClickMainForm.TabIndex = 90
        Me.lblClickMainForm.Text = "点击助手顶部栏【iOS9必备技能】可再次查看此教程。"
        '
        'btnOK
        '
        Me.btnOK.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Location = New System.Drawing.Point(446, 474)
        Me.btnOK.Margin = New System.Windows.Forms.Padding(0)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(178, 47)
        Me.btnOK.TabIndex = 89
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 0
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.Transparent
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "10,10,10,10"
        Me.btnOK.tbText = "我知道了"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'lblTitleDescription
        '
        Me.lblTitleDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblTitleDescription.BackColor = System.Drawing.Color.Transparent
        Me.lblTitleDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer))
        Me.lblTitleDescription.Location = New System.Drawing.Point(318, 104)
        Me.lblTitleDescription.Name = "lblTitleDescription"
        Me.lblTitleDescription.Size = New System.Drawing.Size(427, 45)
        Me.lblTitleDescription.TabIndex = 88
        Me.lblTitleDescription.Text = "苹果对iOS9增加系统限制，如果遇到应用提示未信任，可根据教程解决。"
        '
        'lblTitle
        '
        Me.lblTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblTitle.ForeColor = System.Drawing.Color.White
        Me.lblTitle.Location = New System.Drawing.Point(318, 31)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(499, 63)
        Me.lblTitle.TabIndex = 87
        Me.lblTitle.Text = "iOS9安装软件后显示未信任怎么办？"
        Me.lblTitle.TextAlign = System.Drawing.ContentAlignment.BottomLeft
        '
        'chkNoShow
        '
        Me.chkNoShow.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.chkNoShow.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkNoShow.BackColor = System.Drawing.Color.Transparent
        Me.chkNoShow.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkNoShow.ForeColor = System.Drawing.Color.FromArgb(CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer), CType(CType(193, Byte), Integer))
        Me.chkNoShow.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkNoShow.Location = New System.Drawing.Point(323, 488)
        Me.chkNoShow.Name = "chkNoShow"
        Me.chkNoShow.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkNoShow.Size = New System.Drawing.Size(80, 18)
        Me.chkNoShow.TabIndex = 91
        Me.chkNoShow.tbAdriftIconWhenHover = False
        Me.chkNoShow.tbAutoSize = False
        Me.chkNoShow.tbAutoSizeEx = True
        Me.chkNoShow.tbIconChecked = CType(resources.GetObject("chkNoShow.tbIconChecked"), System.Drawing.Image)
        Me.chkNoShow.tbIconCheckedMouseDown = Nothing
        Me.chkNoShow.tbIconCheckedMouseHover = Nothing
        Me.chkNoShow.tbIconCheckedMouseLeave = Nothing
        Me.chkNoShow.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbIconHoldPlace = True
        Me.chkNoShow.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkNoShow.tbIconIndeterminate = CType(resources.GetObject("chkNoShow.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkNoShow.tbIconIndeterminateMouseDown = Nothing
        Me.chkNoShow.tbIconIndeterminateMouseHover = Nothing
        Me.chkNoShow.tbIconIndeterminateMouseLeave = Nothing
        Me.chkNoShow.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbIconPlaceText = 1
        Me.chkNoShow.tbIconUnChecked = CType(resources.GetObject("chkNoShow.tbIconUnChecked"), System.Drawing.Image)
        Me.chkNoShow.tbIconUnCheckedMouseDown = Nothing
        Me.chkNoShow.tbIconUnCheckedMouseHover = Nothing
        Me.chkNoShow.tbIconUnCheckedMouseLeave = Nothing
        Me.chkNoShow.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbImageBackground = Nothing
        Me.chkNoShow.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkNoShow.tbImageCheckedMouseDown = Nothing
        Me.chkNoShow.tbImageCheckedMouseHover = Nothing
        Me.chkNoShow.tbImageCheckedMouseLeave = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseDown = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseHover = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseLeave = Nothing
        Me.chkNoShow.tbReadOnly = False
        Me.chkNoShow.tbShadow = False
        Me.chkNoShow.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkNoShow.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkNoShow.tbSplit = "3,3,3,3"
        Me.chkNoShow.tbToolTip = ""
        Me.chkNoShow.Text = "不再提示"
        Me.chkNoShow.UseVisualStyleBackColor = False
        '
        'frmTuiInstallTutorial
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(830, 600)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.pnlContainer)
        Me.Name = "frmTuiInstallTutorial"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,33"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "iOS9必备技能"
        CType(Me.picTuiTutorial, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlContainer.ResumeLayout(False)
        Me.pnlContainer.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents lblSetp2 As System.Windows.Forms.Label
    Friend WithEvents lblSetp1 As System.Windows.Forms.Label
    Friend WithEvents lblSetp4 As System.Windows.Forms.Label
    Friend WithEvents lblSetp3 As System.Windows.Forms.Label
    Friend WithEvents lblSetpDescription1 As System.Windows.Forms.Label
    Friend WithEvents lblSetpDescription2 As System.Windows.Forms.Label
    Friend WithEvents lblSetpDescription3 As System.Windows.Forms.Label
    Friend WithEvents lblSetpDescription4 As System.Windows.Forms.Label
    Friend WithEvents picTuiTutorial As System.Windows.Forms.PictureBox
    Friend WithEvents pnlContainer As iTong.Components.tbPanel
    Friend WithEvents lblTitle As System.Windows.Forms.Label
    Friend WithEvents lblTitleDescription As System.Windows.Forms.Label
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents lblClickMainForm As System.Windows.Forms.Label
    Friend WithEvents chkNoShow As iTong.Components.tbCheckBox
End Class
