﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmHotApps
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmHotApps))
        Me.cbxCheckAll = New iTong.Components.tbCheckBox()
        Me.btnInstall = New iTong.Components.tbButton()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.flpContainer = New iTong.tbFlowLayoutPanelEx()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.cbxUnShow = New iTong.Components.tbCheckBox()
        Me.pnlLoading = New System.Windows.Forms.Panel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.tbMsg = New iTong.tbBattery()
        Me.Panel1.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(826, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(802, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(778, 0)
        '
        'cbxCheckAll
        '
        Me.cbxCheckAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.cbxCheckAll.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxCheckAll.BackColor = System.Drawing.Color.Transparent
        Me.cbxCheckAll.Checked = True
        Me.cbxCheckAll.CheckState = System.Windows.Forms.CheckState.Checked
        Me.cbxCheckAll.ForeColor = System.Drawing.Color.FromArgb(CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.cbxCheckAll.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.cbxCheckAll.Location = New System.Drawing.Point(22, 605)
        Me.cbxCheckAll.Name = "cbxCheckAll"
        Me.cbxCheckAll.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxCheckAll.Size = New System.Drawing.Size(106, 22)
        Me.cbxCheckAll.TabIndex = 22
        Me.cbxCheckAll.tbAdriftIconWhenHover = False
        Me.cbxCheckAll.tbAutoSize = False
        Me.cbxCheckAll.tbAutoSizeEx = False
        Me.cbxCheckAll.tbIconChecked = CType(resources.GetObject("cbxCheckAll.tbIconChecked"), System.Drawing.Image)
        Me.cbxCheckAll.tbIconCheckedMouseDown = Nothing
        Me.cbxCheckAll.tbIconCheckedMouseHover = Nothing
        Me.cbxCheckAll.tbIconCheckedMouseLeave = Nothing
        Me.cbxCheckAll.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxCheckAll.tbIconHoldPlace = True
        Me.cbxCheckAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxCheckAll.tbIconIndeterminate = CType(resources.GetObject("cbxCheckAll.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxCheckAll.tbIconIndeterminateMouseDown = Nothing
        Me.cbxCheckAll.tbIconIndeterminateMouseHover = Nothing
        Me.cbxCheckAll.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxCheckAll.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxCheckAll.tbIconPlaceText = 1
        Me.cbxCheckAll.tbIconUnChecked = CType(resources.GetObject("cbxCheckAll.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxCheckAll.tbIconUnCheckedMouseDown = Nothing
        Me.cbxCheckAll.tbIconUnCheckedMouseHover = Nothing
        Me.cbxCheckAll.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxCheckAll.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxCheckAll.tbImageBackground = Nothing
        Me.cbxCheckAll.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxCheckAll.tbImageCheckedMouseDown = Nothing
        Me.cbxCheckAll.tbImageCheckedMouseHover = Nothing
        Me.cbxCheckAll.tbImageCheckedMouseLeave = Nothing
        Me.cbxCheckAll.tbImageUnCheckedMouseDown = Nothing
        Me.cbxCheckAll.tbImageUnCheckedMouseHover = Nothing
        Me.cbxCheckAll.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxCheckAll.tbReadOnly = False
        Me.cbxCheckAll.tbShadow = False
        Me.cbxCheckAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxCheckAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxCheckAll.tbSplit = "3,3,3,3"
        Me.cbxCheckAll.tbToolTip = ""
        Me.cbxCheckAll.Text = "全选/全不选"
        Me.cbxCheckAll.UseVisualStyleBackColor = False
        '
        'btnInstall
        '
        Me.btnInstall.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnInstall.BackColor = System.Drawing.Color.Transparent
        Me.btnInstall.BindingForm = Nothing
        Me.btnInstall.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnInstall.Location = New System.Drawing.Point(710, 598)
        Me.btnInstall.Name = "btnInstall"
        Me.btnInstall.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnInstall.Selectable = True
        Me.btnInstall.Size = New System.Drawing.Size(128, 32)
        Me.btnInstall.TabIndex = 24
        Me.btnInstall.tbAdriftIconWhenHover = False
        Me.btnInstall.tbAutoSize = False
        Me.btnInstall.tbAutoSizeEx = False
        Me.btnInstall.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnInstall.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbBadgeNumber = 0
        Me.btnInstall.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstall.tbEndEllipsis = False
        Me.btnInstall.tbIconHoldPlace = True
        Me.btnInstall.tbIconImage = Nothing
        Me.btnInstall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnInstall.tbIconMore = False
        Me.btnInstall.tbIconMouseDown = Nothing
        Me.btnInstall.tbIconMouseHover = Nothing
        Me.btnInstall.tbIconMouseLeave = Nothing
        Me.btnInstall.tbIconPlaceText = 2
        Me.btnInstall.tbIconReadOnly = Nothing
        Me.btnInstall.tbImageMouseDown = Nothing
        Me.btnInstall.tbImageMouseHover = Nothing
        Me.btnInstall.tbImageMouseLeave = Nothing
        Me.btnInstall.tbProgressValue = 50
        Me.btnInstall.tbReadOnly = False
        Me.btnInstall.tbReadOnlyText = False
        Me.btnInstall.tbShadow = False
        Me.btnInstall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInstall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstall.tbShowDot = False
        Me.btnInstall.tbShowMoreIconImg = CType(resources.GetObject("btnInstall.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInstall.tbShowNew = False
        Me.btnInstall.tbShowProgress = False
        Me.btnInstall.tbShowTip = True
        Me.btnInstall.tbShowToolTipOnButton = False
        Me.btnInstall.tbSplit = "3,3,3,3"
        Me.btnInstall.tbText = "一键安装"
        Me.btnInstall.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbTextColor = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDisable = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDown = System.Drawing.Color.White
        Me.btnInstall.tbTextColorHover = System.Drawing.Color.White
        Me.btnInstall.tbTextMouseDownPlace = 0
        Me.btnInstall.tbToolTip = ""
        Me.btnInstall.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstall.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstall.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.VisibleEx = True
        '
        'Label1
        '
        Me.Label1.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.Label1.Font = New System.Drawing.Font("宋体", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(348, 10)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(153, 36)
        Me.Label1.TabIndex = 26
        Me.Label1.Text = "新手装机必备"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.Controls.Add(Me.flpContainer)
        Me.Panel1.Location = New System.Drawing.Point(0, 52)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(848, 486)
        Me.Panel1.TabIndex = 27
        '
        'flpContainer
        '
        Me.flpContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.flpContainer.AutoScroll = True
        Me.flpContainer.Location = New System.Drawing.Point(17, 0)
        Me.flpContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.flpContainer.Name = "flpContainer"
        Me.flpContainer.Size = New System.Drawing.Size(848, 486)
        Me.flpContainer.TabIndex = 25
        Me.flpContainer.tbGroupPadding = 0
        '
        'Panel2
        '
        Me.Panel2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.Panel2.Controls.Add(Me.Panel1)
        Me.Panel2.Controls.Add(Me.Label1)
        Me.Panel2.Location = New System.Drawing.Point(1, 31)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(848, 559)
        Me.Panel2.TabIndex = 28
        '
        'cbxUnShow
        '
        Me.cbxUnShow.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cbxUnShow.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxUnShow.BackColor = System.Drawing.Color.Transparent
        Me.cbxUnShow.ForeColor = System.Drawing.Color.FromArgb(CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.cbxUnShow.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer), CType(CType(68, Byte), Integer))
        Me.cbxUnShow.Location = New System.Drawing.Point(385, 608)
        Me.cbxUnShow.Name = "cbxUnShow"
        Me.cbxUnShow.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxUnShow.Size = New System.Drawing.Size(106, 22)
        Me.cbxUnShow.TabIndex = 29
        Me.cbxUnShow.tbAdriftIconWhenHover = False
        Me.cbxUnShow.tbAutoSize = False
        Me.cbxUnShow.tbAutoSizeEx = False
        Me.cbxUnShow.tbIconChecked = CType(resources.GetObject("cbxUnShow.tbIconChecked"), System.Drawing.Image)
        Me.cbxUnShow.tbIconCheckedMouseDown = Nothing
        Me.cbxUnShow.tbIconCheckedMouseHover = Nothing
        Me.cbxUnShow.tbIconCheckedMouseLeave = Nothing
        Me.cbxUnShow.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxUnShow.tbIconHoldPlace = True
        Me.cbxUnShow.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxUnShow.tbIconIndeterminate = CType(resources.GetObject("cbxUnShow.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxUnShow.tbIconIndeterminateMouseDown = Nothing
        Me.cbxUnShow.tbIconIndeterminateMouseHover = Nothing
        Me.cbxUnShow.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxUnShow.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxUnShow.tbIconPlaceText = 1
        Me.cbxUnShow.tbIconUnChecked = CType(resources.GetObject("cbxUnShow.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxUnShow.tbIconUnCheckedMouseDown = Nothing
        Me.cbxUnShow.tbIconUnCheckedMouseHover = Nothing
        Me.cbxUnShow.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxUnShow.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxUnShow.tbImageBackground = Nothing
        Me.cbxUnShow.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxUnShow.tbImageCheckedMouseDown = Nothing
        Me.cbxUnShow.tbImageCheckedMouseHover = Nothing
        Me.cbxUnShow.tbImageCheckedMouseLeave = Nothing
        Me.cbxUnShow.tbImageUnCheckedMouseDown = Nothing
        Me.cbxUnShow.tbImageUnCheckedMouseHover = Nothing
        Me.cbxUnShow.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxUnShow.tbReadOnly = False
        Me.cbxUnShow.tbShadow = False
        Me.cbxUnShow.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxUnShow.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxUnShow.tbSplit = "3,3,3,3"
        Me.cbxUnShow.tbToolTip = ""
        Me.cbxUnShow.Text = "不再提醒"
        Me.cbxUnShow.UseVisualStyleBackColor = False
        Me.cbxUnShow.Visible = False
        '
        'pnlLoading
        '
        Me.pnlLoading.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(1, 31)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(848, 559)
        Me.pnlLoading.TabIndex = 30
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(410, 257)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(120, 45)
        Me.lblLoading.TabIndex = 12
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbLoading.Location = New System.Drawing.Point(319, 232)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(91, 95)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 11
        Me.pbLoading.TabStop = False
        '
        'tbMsg
        '
        Me.tbMsg.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.tbMsg.BackColor = System.Drawing.Color.Transparent
        Me.tbMsg.Cursor = System.Windows.Forms.Cursors.Default
        Me.tbMsg.Location = New System.Drawing.Point(136, 603)
        Me.tbMsg.Name = "tbMsg"
        Me.tbMsg.Size = New System.Drawing.Size(328, 22)
        Me.tbMsg.TabIndex = 23
        Me.tbMsg.Text = "已选择24款软件，共1.02GB"
        '
        'frmHotApps
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(244, Byte), Integer), CType(CType(245, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(850, 642)
        Me.Controls.Add(Me.cbxCheckAll)
        Me.Controls.Add(Me.btnInstall)
        Me.Controls.Add(Me.cbxUnShow)
        Me.Controls.Add(Me.tbMsg)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.pnlLoading)
        Me.MaximizeBox = False
        Me.MaximumSize = New System.Drawing.Size(850, 642)
        Me.Name = "frmHotApps"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,5,54"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "装机必备"
        Me.Controls.SetChildIndex(Me.pnlLoading, 0)
        Me.Controls.SetChildIndex(Me.Panel2, 0)
        Me.Controls.SetChildIndex(Me.tbMsg, 0)
        Me.Controls.SetChildIndex(Me.cbxUnShow, 0)
        Me.Controls.SetChildIndex(Me.btnInstall, 0)
        Me.Controls.SetChildIndex(Me.cbxCheckAll, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Panel1.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents cbxCheckAll As iTong.Components.tbCheckBox
    Friend WithEvents tbMsg As iTong.tbBattery
    Friend WithEvents btnInstall As iTong.Components.tbButton
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents cbxUnShow As iTong.Components.tbCheckBox
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents flpContainer As iTong.tbFlowLayoutPanelEx
End Class
