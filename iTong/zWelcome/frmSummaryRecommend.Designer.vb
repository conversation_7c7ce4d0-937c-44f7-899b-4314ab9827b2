﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmSummaryRecommend
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmSummaryRecommend))
        Me.lblTitle = New iTong.Components.tbLabel()
        Me.pnlIcons = New iTong.Components.tbPanel()
        Me.pnlNetDisable = New System.Windows.Forms.Panel()
        Me.btnGotoSite = New iTong.Components.tbButton()
        Me.lblGetWebsite = New System.Windows.Forms.Label()
        Me.picError = New System.Windows.Forms.PictureBox()
        Me.lblNetDisable = New System.Windows.Forms.Label()
        Me.pnlLoading = New System.Windows.Forms.Panel()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.txtSearch = New iTong.Components.tbSearch()
        Me.pnlIcons.SuspendLayout()
        Me.pnlNetDisable.SuspendLayout()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(477, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(453, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(429, 0)
        '
        'lblTitle
        '
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 13.0!)
        Me.lblTitle.ForeColor = System.Drawing.SystemColors.ControlDarkDark
        Me.lblTitle.Location = New System.Drawing.Point(3, 25)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(194, 25)
        Me.lblTitle.TabIndex = 38
        Me.lblTitle.tbAdriftWhenHover = False
        Me.lblTitle.tbAutoEllipsis = False
        Me.lblTitle.tbAutoSize = False
        Me.lblTitle.tbHideImage = False
        Me.lblTitle.tbIconImage = Nothing
        Me.lblTitle.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTitle.tbIconPlaceText = 5
        Me.lblTitle.tbShadow = False
        Me.lblTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTitle.tbShowScrolling = False
        Me.lblTitle.Text = "编辑推荐"
        Me.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlIcons
        '
        Me.pnlIcons.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlIcons.Controls.Add(Me.pnlNetDisable)
        Me.pnlIcons.Controls.Add(Me.pnlLoading)
        Me.pnlIcons.Location = New System.Drawing.Point(-2, 50)
        Me.pnlIcons.Name = "pnlIcons"
        Me.pnlIcons.Size = New System.Drawing.Size(503, 277)
        Me.pnlIcons.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlIcons.TabIndex = 39
        Me.pnlIcons.tbBackgroundImage = Nothing
        Me.pnlIcons.tbShowWatermark = False
        Me.pnlIcons.tbSplit = "0,0,0,0"
        Me.pnlIcons.tbWatermark = Nothing
        Me.pnlIcons.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlIcons.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlNetDisable
        '
        Me.pnlNetDisable.BackColor = System.Drawing.Color.Transparent
        Me.pnlNetDisable.Controls.Add(Me.btnGotoSite)
        Me.pnlNetDisable.Controls.Add(Me.lblGetWebsite)
        Me.pnlNetDisable.Controls.Add(Me.picError)
        Me.pnlNetDisable.Controls.Add(Me.lblNetDisable)
        Me.pnlNetDisable.Location = New System.Drawing.Point(4, 60)
        Me.pnlNetDisable.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlNetDisable.Name = "pnlNetDisable"
        Me.pnlNetDisable.Size = New System.Drawing.Size(498, 112)
        Me.pnlNetDisable.TabIndex = 8
        Me.pnlNetDisable.Visible = False
        '
        'btnGotoSite
        '
        Me.btnGotoSite.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnGotoSite.BackColor = System.Drawing.Color.Transparent
        Me.btnGotoSite.BindingForm = Nothing
        Me.btnGotoSite.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnGotoSite.Location = New System.Drawing.Point(311, 73)
        Me.btnGotoSite.Name = "btnGotoSite"
        Me.btnGotoSite.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnGotoSite.Selectable = True
        Me.btnGotoSite.Size = New System.Drawing.Size(12, 12)
        Me.btnGotoSite.TabIndex = 38
        Me.btnGotoSite.tbAdriftIconWhenHover = False
        Me.btnGotoSite.tbAutoSize = False
        Me.btnGotoSite.tbAutoSizeEx = True
        Me.btnGotoSite.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_3_goto
        Me.btnGotoSite.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnGotoSite.tbBadgeNumber = 0
        Me.btnGotoSite.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnGotoSite.tbEndEllipsis = False
        Me.btnGotoSite.tbIconHoldPlace = True
        Me.btnGotoSite.tbIconImage = Nothing
        Me.btnGotoSite.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoSite.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnGotoSite.tbIconMore = False
        Me.btnGotoSite.tbIconMouseDown = Nothing
        Me.btnGotoSite.tbIconMouseHover = Nothing
        Me.btnGotoSite.tbIconMouseLeave = Nothing
        Me.btnGotoSite.tbIconPlaceText = 2
        Me.btnGotoSite.tbIconReadOnly = Nothing
        Me.btnGotoSite.tbImageMouseDown = Nothing
        Me.btnGotoSite.tbImageMouseHover = Nothing
        Me.btnGotoSite.tbImageMouseLeave = Nothing
        Me.btnGotoSite.tbProgressValue = 50
        Me.btnGotoSite.tbReadOnly = False
        Me.btnGotoSite.tbReadOnlyText = False
        Me.btnGotoSite.tbShadow = False
        Me.btnGotoSite.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnGotoSite.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnGotoSite.tbShowDot = False
        Me.btnGotoSite.tbShowMoreIconImg = CType(resources.GetObject("btnGotoSite.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnGotoSite.tbShowNew = False
        Me.btnGotoSite.tbShowProgress = False
        Me.btnGotoSite.tbShowTip = True
        Me.btnGotoSite.tbShowToolTipOnButton = False
        Me.btnGotoSite.tbSplit = "0,0,0,0"
        Me.btnGotoSite.tbText = ""
        Me.btnGotoSite.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoSite.tbTextColor = System.Drawing.Color.White
        Me.btnGotoSite.tbTextColorDisable = System.Drawing.Color.White
        Me.btnGotoSite.tbTextColorDown = System.Drawing.Color.White
        Me.btnGotoSite.tbTextColorHover = System.Drawing.Color.White
        Me.btnGotoSite.tbTextMouseDownPlace = 0
        Me.btnGotoSite.tbToolTip = ""
        Me.btnGotoSite.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnGotoSite.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnGotoSite.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnGotoSite.VisibleEx = True
        '
        'lblGetWebsite
        '
        Me.lblGetWebsite.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblGetWebsite.AutoSize = True
        Me.lblGetWebsite.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblGetWebsite.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblGetWebsite.Location = New System.Drawing.Point(230, 73)
        Me.lblGetWebsite.Name = "lblGetWebsite"
        Me.lblGetWebsite.Size = New System.Drawing.Size(97, 15)
        Me.lblGetWebsite.TabIndex = 37
        Me.lblGetWebsite.Text = "逛逛苹果商店"
        '
        'picError
        '
        Me.picError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picError.Image = Global.iTong.My.Resources.Resources.summary_error
        Me.picError.Location = New System.Drawing.Point(138, 13)
        Me.picError.Margin = New System.Windows.Forms.Padding(0)
        Me.picError.Name = "picError"
        Me.picError.Size = New System.Drawing.Size(87, 87)
        Me.picError.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picError.TabIndex = 11
        Me.picError.TabStop = False
        '
        'lblNetDisable
        '
        Me.lblNetDisable.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNetDisable.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblNetDisable.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblNetDisable.Location = New System.Drawing.Point(229, 36)
        Me.lblNetDisable.Name = "lblNetDisable"
        Me.lblNetDisable.Size = New System.Drawing.Size(266, 45)
        Me.lblNetDisable.TabIndex = 10
        Me.lblNetDisable.Text = "网络不可用"
        Me.lblNetDisable.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlLoading
        '
        Me.pnlLoading.BackColor = System.Drawing.Color.Transparent
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(0, 0)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(197, 54)
        Me.pnlLoading.TabIndex = 7
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(85, 5)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(120, 45)
        Me.lblLoading.TabIndex = 10
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_24
        Me.pbLoading.Location = New System.Drawing.Point(59, 17)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(24, 24)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.pbLoading.TabIndex = 0
        Me.pbLoading.TabStop = False
        '
        'txtSearch
        '
        Me.txtSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearch.BackColor = System.Drawing.Color.Transparent
        Me.txtSearch.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.txtSearch.Location = New System.Drawing.Point(271, 30)
        Me.txtSearch.MaxLength = 32767
        Me.txtSearch.Name = "txtSearch"
        Me.txtSearch.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearch.ReadOnly = False
        Me.txtSearch.SearchText = ""
        Me.txtSearch.SearchTipText = "搜索"
        Me.txtSearch.ShowClear = True
        Me.txtSearch.ShowClearAlways = False
        Me.txtSearch.ShowMore = False
        Me.txtSearch.ShowSearch = True
        Me.txtSearch.Size = New System.Drawing.Size(166, 23)
        Me.txtSearch.TabIndex = 40
        Me.txtSearch.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearch.Timer = True
        Me.txtSearch.TimerInterval = 0.5R
        '
        'frmSummaryRecommend
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(501, 361)
        Me.Controls.Add(Me.txtSearch)
        Me.Controls.Add(Me.lblTitle)
        Me.Controls.Add(Me.pnlIcons)
        Me.Name = "frmSummaryRecommend"
        Me.tbShowIconOnForm = False
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmSummaryRecommend"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlIcons, 0)
        Me.Controls.SetChildIndex(Me.lblTitle, 0)
        Me.Controls.SetChildIndex(Me.txtSearch, 0)
        Me.pnlIcons.ResumeLayout(False)
        Me.pnlNetDisable.ResumeLayout(False)
        Me.pnlNetDisable.PerformLayout()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents lblTitle As tbLabel
    Friend WithEvents pnlIcons As tbPanel
    Friend WithEvents pnlNetDisable As System.Windows.Forms.Panel
    Friend WithEvents picError As System.Windows.Forms.PictureBox
    Friend WithEvents lblNetDisable As System.Windows.Forms.Label
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Protected WithEvents txtSearch As tbSearch
    Friend WithEvents btnGotoSite As tbButton
    Friend WithEvents lblGetWebsite As System.Windows.Forms.Label
End Class
