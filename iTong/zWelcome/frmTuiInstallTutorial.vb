﻿Public Class frmTuiInstallTutorial

    Private mApplication As IApplication = Nothing

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.Icon = My.Resources.iTong
        Me.CanResize = False
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.lblTitle.Font = Common.CreateFont("微软雅黑", 17.0!, FontStyle.Bold)
        Me.lblTitleDescription.Font = Common.CreateFont("微软雅黑", 11.0!, FontStyle.Regular)

        Me.lblSetp1.Font = Common.CreateFont("微软雅黑", 13.0!, FontStyle.Regular)
        Me.lblSetp2.Font = Common.CreateFont("微软雅黑", 13.0!, FontStyle.Regular)
        Me.lblSetp3.Font = Common.CreateFont("微软雅黑", 13.0!, FontStyle.Regular)
        Me.lblSetp4.Font = Common.CreateFont("微软雅黑", 13.0!, FontStyle.Regular)

        Me.lblSetpDescription1.Font = Common.CreateFont("微软雅黑", 11.0!, FontStyle.Regular)
        Me.lblSetpDescription2.Font = Common.CreateFont("微软雅黑", 11.0!, FontStyle.Regular)
        Me.lblSetpDescription3.Font = Common.CreateFont("微软雅黑", 11.0!, FontStyle.Regular)
        Me.lblSetpDescription4.Font = Common.CreateFont("微软雅黑", 11.0!, FontStyle.Regular)
        Me.lblClickMainForm.Font = Common.CreateFont("微软雅黑", 11.0!, FontStyle.Regular)

        Me.btnOK.Font = Common.CreateFont("微软雅黑", 14.0!, FontStyle.Regular)

        Me.Text = Me.Language.GetString("App.InstallTutorial.Label.EssentialSkilliOS9")                 '"iOS9必备技能"
        Me.lblTitle.Text = Me.Language.GetString("App.InstallTutorial.Label.Title")                     '"iOS9安装软件后显示未信任怎么办？"
        Me.lblTitleDescription.Text = Me.Language.GetString("App.InstallTutorial.Label.TitleDesc")      '"苹果对iOS9增加系统限制，如果遇到应用提示未信任，可根据教程解决。"
        Me.lblSetp1.Text = Me.Language.GetString("Welcome.Label.StepOne") & Me.Language.GetString("Common.Symbol.Colon")            '"第一步:"
        Me.lblSetp2.Text = Me.Language.GetString("Welcome.Label.StepTwo") & Me.Language.GetString("Common.Symbol.Colon")            '"第二步:"
        Me.lblSetp3.Text = Me.Language.GetString("Welcome.Label.StepThree") & Me.Language.GetString("Common.Symbol.Colon")          '"第三步:"
        Me.lblSetp4.Text = Me.Language.GetString("Welcome.Label.StepFour") & Me.Language.GetString("Common.Symbol.Colon")           '"第四步:"
        Me.lblSetpDescription1.Text = Me.Language.GetString("App.InstallTutorial.Label.RememberLineDesc")                           '"打开已安装的应用时，出现以下情况，首先记住划线部分的内容；"
        Me.lblSetpDescription2.Text = Me.Language.GetString("App.InstallTutorial.Label.FoundDescFile")                              '"然后在设备中点击【设置】->【通用】，滑到最下方->【描述文件】；"
        Me.lblSetpDescription3.Text = Me.Language.GetString("App.InstallTutorial.Label.ClickInDescFile")                            '"找到刚才记住的同名描述文件，点击进入；"
        Me.lblSetpDescription4.Text = Me.Language.GetString("App.InstallTutorial.Label.ClickToTrust")                               '"点击信任该描述文件。"
        Me.lblClickMainForm.Text = Me.Language.GetString("App.InstallTutorial.Label.TopToSeeAgain")                                 '"点击助手顶部栏【iOS9必备技能】可再次查看此教程。"
        Me.btnOK.Text = Me.Language.GetString("Common.ISee")                        '"我知道了"

        Me.chkNoShow.Text = Me.Language.GetString("App.Button.IgnoreNumber")                        '"不再提醒"
    End Sub

    '教程显示
    Public Shared Sub ShowForm(ByVal app As IApplication)
        Dim frm As frmTuiInstallTutorial = Nothing
        For Each Item As Form In My.Application.OpenForms()
            If TypeOf Item Is frmTuiInstallTutorial Then
                frm = Item
            End If

            If app Is Nothing AndAlso TypeOf Item Is MainForm Then
                app = Item
            End If
            If frm IsNot Nothing AndAlso app IsNot Nothing Then
                Exit For
            End If
        Next

        If frm IsNot Nothing Then
            frm.Activate()
            frm.BringToFront()
        Else
            frm = New frmTuiInstallTutorial(app)
            frm.Show(app)
        End If
    End Sub

    '安装同步推信任企业签名文件教程
    Public Shared Sub InstallTuiTutorial(ByVal device As iPhoneDevice)
        If Folder.LangType <> LanguageType.zh_CN Then
            Return
        End If
        If device.VersionNumber >= 900 AndAlso Not AuthorizeHelper.GetInstance(device).CheckTuiTutorial() Then
            AuthorizeHelper.GetInstance(device).InstallTuiTutorial()
            ActionCollectHelper.OperateTuiPage(device, ModelKey.None, ModelKey.TuiInstallTutorial, ActionDataType.Install, FunctionSucceed.None, "")
        End If
    End Sub

#End Region

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Me.Close()
    End Sub

    Private Sub lblSetp1_SizeChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblSetp1.SizeChanged, lblSetp2.SizeChanged, lblSetp3.SizeChanged, lblSetp4.SizeChanged
        Dim intRight As Integer = Me.lblSetp1.Right
        If intRight < Me.lblSetp2.Right Then
            intRight = Me.lblSetp3.Right
        End If
        If intRight < Me.lblSetp3.Right Then
            intRight = Me.lblSetp3.Right
        End If
        If intRight < Me.lblSetp4.Right Then
            intRight = Me.lblSetp4.Right
        End If
        intRight = intRight + 5

        Me.lblSetpDescription1.Location = New Point(intRight, Me.lblSetpDescription1.Top)
        Me.lblSetpDescription2.Location = New Point(intRight, Me.lblSetpDescription2.Top)
        Me.lblSetpDescription3.Location = New Point(intRight, Me.lblSetpDescription3.Top)
        Me.lblSetpDescription4.Location = New Point(intRight, Me.lblSetpDescription4.Top)
    End Sub

    Private Sub chkNoShow_CheckedChanged(sender As Object, e As EventArgs) Handles chkNoShow.CheckedChanged
        IniSetting.SetNoShowTuiInstallTutorial(Me.chkNoShow.Checked)
    End Sub
End Class