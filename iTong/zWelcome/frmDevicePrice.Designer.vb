﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDevicePrice
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmDevicePrice))
        Me.webPrice = New iTong.Components.tbWebBrowserEx()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(876, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(852, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(828, 0)
        '
        'webPrice
        '
        Me.webPrice.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.webPrice.Client = ""
        Me.webPrice.DeviceList = Nothing
        Me.webPrice.IsConnected = False
        Me.webPrice.IsWebBrowserContextMenuEnabled = False
        Me.webPrice.Jailbreaked = False
        Me.webPrice.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.webPrice.LoadingFont = Nothing
        Me.webPrice.LoadingGif = CType(resources.GetObject("webPrice.LoadingGif"), System.Drawing.Image)
        Me.webPrice.Location = New System.Drawing.Point(2, 33)
        Me.webPrice.MinimumSize = New System.Drawing.Size(20, 20)
        Me.webPrice.Name = "webPrice"
        Me.webPrice.ScriptErrorsSuppressed = True
        Me.webPrice.ShowLoadingWait = False
        Me.webPrice.ShowNavigateErrorPage = True
        Me.webPrice.ShowProgress = True
        Me.webPrice.Size = New System.Drawing.Size(897, 666)
        Me.webPrice.SN = ""
        Me.webPrice.TabIndex = 22
        Me.webPrice.UserInfo = ""
        Me.webPrice.UserInfoEncode = ""
        '
        'frmDevicePrice
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(900, 700)
        Me.Controls.Add(Me.webPrice)
        Me.Name = "frmDevicePrice"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,8"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "设备回收"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.webPrice, 0)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents webPrice As iTong.Components.tbWebBrowserEx
End Class
