﻿Public Class frmCommonWel
    Dim _frmDebug As frmAndroidUSBDebugSimple = Nothing
    Dim mUsbDevice As UsbDevice = Nothing

    Public Sub New(ByVal app As IApplication, ByVal device As IDevice, ByVal dev As UsbDevice)
        Me.InitializeComponent()
        Me.CanbeMove = False
        Me.Language = LanguageInterface.Instance()
        Me.mUsbDevice = dev
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.lblWeChatHF.Visible = False
        Me.PictureBox1.Visible = False
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.lblDevNoConn.Text = Me.Language.GetString("Welcome.Label.DeviceNoConnected")                 '"设备未连接"
        Me.lblLinkMode.Text = Me.Language.GetString("Welcome.Label.LinkMode")                           '"支持iPhone/iPad 和 Android设备"
        Me.lklOpenDebug.Text = Me.Language.GetString("Welcome.Label.OpenDebug")                         '"安卓设备需要打开 调试模式，如何打开？"
        Me.lklUnrecognize.Text = Me.Language.GetString("Welcome.Label.ConnectedButUnfound")             '"已经连接，但是无法识别？"

        Me.lblNoConnected1.Text = String.Format(Me.Language.GetString("Welcome.Label.ContactMe"), Utility.TongbuContact)  ' '"如果连接过程中遇到问题，请联系客服QQ:{0}。"
        Me.lblOpenWechatAssisant.Text = Me.Language.GetString("Weixin.Message.AndroidExportWeixinWithoutCable")  '"安卓设备不用数据线导出微信信息到电脑，如何导出？"
        Me.lblWeChatHF.Text = Me.Language.GetString("Weixin.Message.AndroidRecoverWeixin")  '安卓设备微信记录误删，何如恢复？
        If ServerIniSetting.GetShowWechatBackupTool() Then
            lblOpenWechatAssisant.Visible = True
        End If
    End Sub

    Private Sub lklOpenDebug_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lklOpenDebug.LinkClicked

        If Me._frmDebug Is Nothing Then
            Me._frmDebug = New frmAndroidUSBDebugSimple(mUsbDevice)
            Utility.AddForm2Panel(Me._frmDebug, Me, False)

            AddHandler Me._frmDebug.OnCloseDebug, AddressOf OnNoDebugGuideClose
        End If

        _frmDebug.Visible = True
        _frmDebug.BringToFront()
    End Sub

    Private Sub OnNoDebugGuideClose(ByVal sender As Object, ByVal e As EventArgs)
        If Me._frmDebug IsNot Nothing Then
            _frmDebug.Visible = False
            _frmDebug.SendToBack()
        End If

    End Sub

    'Private Sub OnVisible(ByVal sender As Object, ByVal e As EventArgs) Handles Me.VisibleChanged
    '    If Me.Visible Then
    '        Me.OnNoDebugGuideClose(Nothing, Nothing)
    '    End If
    'End Sub

    Private Sub lklUnrecognize_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lklUnrecognize.LinkClicked
        Common.OpenExplorer(WebUrl.PageUnrecognizeDevice)  '"http://bbs.tongbu.com/thread-55460-1-1.html"
    End Sub

    Private Sub lblOpenWechatAssisant_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lblOpenWechatAssisant.LinkClicked
        Try
            Dim url As String = ServerIniSetting.GetUrlWechatBackupToolDeviceNotConnect()
            Common.OpenExplorer(url)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "lblOpenWechatAssisant_LinkClicked")
        End Try
    End Sub

    Private Sub lblWeChatHF_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lblWeChatHF.LinkClicked
        Common.OpenExplorer("http://rollback.wesafesoft.com/?c_s=L8MSjciE3oE%3d&s=zstj")
    End Sub

End Class