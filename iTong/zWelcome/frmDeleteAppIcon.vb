﻿Imports System.Threading

Public Class frmDeleteAppIcon

    Private mTDDelete As Thread = Nothing

#Region "----初始化、窗体闭关----"

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice)
        MyBase.New(application, device)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Me.Language = application.Language
        Me.tbAutoSetFormSize = True
        Me.mApplication = application
        Me.mDevice = device
        Me.Icon = My.Resources.iTong
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.CanResize = False

        Me.btn_normal.Visible = False
        Me.btn_minimize.Visible = False

    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As CoreFoundation.IDevice)
        MyBase.OnDisconnect(device)

        Me.Close()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            If Me.mTDDelete IsNot Nothing AndAlso Me.mTDDelete.ThreadState <> ThreadState.Stopped Then
                Me.mTDDelete.Abort()
            End If
        Catch
        End Try
    End Sub

#End Region

    Private Sub btnDelete_Click(sender As Object, e As EventArgs) Handles btnDelete.Click

        Try
            If Me.mTDDelete IsNot Nothing AndAlso Me.mTDDelete.ThreadState <> ThreadState.Stopped Then
                Me.mTDDelete.Abort()
            End If
        Catch
        End Try

        Me.mTDDelete = New Thread(AddressOf DoTDDelete)
        With Me.mTDDelete
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub DoTDDelete()

        Me.SetbtnDelete("检测、删除中...", False)
        Try
            Dim lstPackages As List(Of PackageInfo) = New List(Of PackageInfo)()

            Dim arrObj As Object() = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(Encoding.UTF8.GetBytes(Me.mDevice.GetIconStatePlist()))
            If arrObj IsNot Nothing AndAlso arrObj.Length > 0 Then
                For Each item As Object() In arrObj
                    If item IsNot Nothing AndAlso item.Length > 0 Then
                        For Each dictItem As Dictionary(Of Object, Object) In item
                            If dictItem.Values.Count = 5 AndAlso Not dictItem.ContainsKey("iconModDate") Then

                            ElseIf dictItem.Values.Count = 3 AndAlso dictItem.ContainsKey("listType") AndAlso dictItem.ContainsKey("iconLists") Then
                                Dim iObj As Object = dictItem("listType")
                                If (iObj = "folder") Then
                                    Dim arrIObj As Object() = dictItem("iconLists")(0)
                                    For Each dictIItem As Dictionary(Of Object, Object) In arrIObj
                                        If dictIItem.Values.Count = 5 AndAlso Not dictIItem.ContainsKey("iconModDate") Then

                                        ElseIf dictIItem.Values.Count = 2 Then
                                            '删除
                                            Dim info As PackageInfo = New PackageInfo()
                                            info.Identifier = dictIItem("displayIdentifier")
                                            info.PackageType = PackageType.iPA
                                            lstPackages.Add(info)
                                        End If
                                    Next
                                End If

                            ElseIf dictItem.Values.Count = 2 Then
                                '删除
                                Dim info As PackageInfo = New PackageInfo()
                                info.Identifier = dictItem("displayIdentifier")
                                info.PackageType = PackageType.iPA
                                lstPackages.Add(info)
                            End If
                        Next
                    End If
                Next
            End If
            'If lstPackages.Count > 0 Then
            iPhoneUninstallHelper.GetInstance(Me.mDevice).UninstallApplication(lstPackages)
            '    tbMessageBox.Show(String.Format("检测到{0}个废图标并尝试删除完成。", lstPackages.Count), "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            'Else
            '    tbMessageBox.Show("检测到0个废图标。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            'End If
            tbMessageBox.Show("删除废弃图标已完成。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.Close()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnDelete_Click")
        Finally
            Me.SetbtnDelete("立即删除", True)
        End Try
    End Sub

    Private Delegate Sub SetbtnDeleteHandler(ByVal strText As String, ByVal isEnabled As Boolean)
    Private Sub SetbtnDelete(ByVal strText As String, ByVal isEnabled As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetbtnDeleteHandler(AddressOf SetbtnDelete), strText, isEnabled)
            Else
                Me.btnDelete.Text = strText
                Me.btnDelete.Enabled = isEnabled
            End If
        Catch
        End Try
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub
End Class