﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmCommonWel
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.lblDevNoConn = New System.Windows.Forms.Label()
        Me.lblLinkMode = New System.Windows.Forms.Label()
        Me.lklUnrecognize = New System.Windows.Forms.LinkLabel()
        Me.lklOpenDebug = New System.Windows.Forms.LinkLabel()
        Me.picDefault = New System.Windows.Forms.PictureBox()
        Me.lblNoConnected1 = New iTong.Components.tbLabel()
        Me.lblOpenWechatAssisant = New System.Windows.Forms.LinkLabel()
        Me.lblWeChatHF = New System.Windows.Forms.LinkLabel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        CType(Me.picDefault, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblDevNoConn
        '
        Me.lblDevNoConn.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDevNoConn.AutoSize = True
        Me.lblDevNoConn.Font = New System.Drawing.Font("宋体", 21.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDevNoConn.Location = New System.Drawing.Point(143, 98)
        Me.lblDevNoConn.Name = "lblDevNoConn"
        Me.lblDevNoConn.Size = New System.Drawing.Size(158, 29)
        Me.lblDevNoConn.TabIndex = 0
        Me.lblDevNoConn.Text = "设备未连接"
        Me.lblDevNoConn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblLinkMode
        '
        Me.lblLinkMode.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLinkMode.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLinkMode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(126, Byte), Integer), CType(CType(126, Byte), Integer), CType(CType(126, Byte), Integer))
        Me.lblLinkMode.Location = New System.Drawing.Point(149, 147)
        Me.lblLinkMode.Name = "lblLinkMode"
        Me.lblLinkMode.Size = New System.Drawing.Size(544, 23)
        Me.lblLinkMode.TabIndex = 1
        Me.lblLinkMode.Text = "支持iPhone/iPad 和 Android设备"
        Me.lblLinkMode.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lklUnrecognize
        '
        Me.lklUnrecognize.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lklUnrecognize.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lklUnrecognize.LinkColor = System.Drawing.Color.Red
        Me.lklUnrecognize.Location = New System.Drawing.Point(149, 512)
        Me.lklUnrecognize.Name = "lklUnrecognize"
        Me.lklUnrecognize.Size = New System.Drawing.Size(235, 19)
        Me.lklUnrecognize.TabIndex = 2
        Me.lklUnrecognize.TabStop = True
        Me.lklUnrecognize.Text = "已经连接，但是无法识别？"
        Me.lklUnrecognize.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lklOpenDebug
        '
        Me.lklOpenDebug.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lklOpenDebug.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lklOpenDebug.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(133, Byte), Integer), CType(CType(218, Byte), Integer))
        Me.lklOpenDebug.Location = New System.Drawing.Point(149, 533)
        Me.lklOpenDebug.Name = "lklOpenDebug"
        Me.lklOpenDebug.Size = New System.Drawing.Size(602, 19)
        Me.lklOpenDebug.TabIndex = 3
        Me.lklOpenDebug.TabStop = True
        Me.lklOpenDebug.Text = "安卓设备需要打开 调试模式，如何打开？"
        Me.lklOpenDebug.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picDefault
        '
        Me.picDefault.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picDefault.BackColor = System.Drawing.Color.Transparent
        Me.picDefault.Image = Global.iTong.My.Resources.Resources.new_device_default
        Me.picDefault.Location = New System.Drawing.Point(179, 214)
        Me.picDefault.Name = "picDefault"
        Me.picDefault.Size = New System.Drawing.Size(630, 266)
        Me.picDefault.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picDefault.TabIndex = 6
        Me.picDefault.TabStop = False
        '
        'lblNoConnected1
        '
        Me.lblNoConnected1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNoConnected1.AutoSize = True
        Me.lblNoConnected1.BackColor = System.Drawing.Color.Transparent
        Me.lblNoConnected1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblNoConnected1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblNoConnected1.Location = New System.Drawing.Point(150, 599)
        Me.lblNoConnected1.Name = "lblNoConnected1"
        Me.lblNoConnected1.Size = New System.Drawing.Size(239, 12)
        Me.lblNoConnected1.TabIndex = 31
        Me.lblNoConnected1.tbAdriftWhenHover = False
        Me.lblNoConnected1.tbAutoEllipsis = False
        Me.lblNoConnected1.tbAutoSize = True
        Me.lblNoConnected1.tbHideImage = False
        Me.lblNoConnected1.tbIconImage = Nothing
        Me.lblNoConnected1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNoConnected1.tbIconPlaceText = 5
        Me.lblNoConnected1.tbShadow = False
        Me.lblNoConnected1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNoConnected1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNoConnected1.tbShowScrolling = False
        Me.lblNoConnected1.Text = "如果连接过程中遇到问题，请联系客服QQ:。"
        '
        'lblOpenWechatAssisant
        '
        Me.lblOpenWechatAssisant.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblOpenWechatAssisant.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lblOpenWechatAssisant.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(133, Byte), Integer), CType(CType(218, Byte), Integer))
        Me.lblOpenWechatAssisant.Location = New System.Drawing.Point(149, 554)
        Me.lblOpenWechatAssisant.Name = "lblOpenWechatAssisant"
        Me.lblOpenWechatAssisant.Size = New System.Drawing.Size(602, 19)
        Me.lblOpenWechatAssisant.TabIndex = 3
        Me.lblOpenWechatAssisant.TabStop = True
        Me.lblOpenWechatAssisant.Text = "安卓设备不用数据线导出微信信息到电脑，如何导出？"
        Me.lblOpenWechatAssisant.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblOpenWechatAssisant.Visible = False
        '
        'lblWeChatHF
        '
        Me.lblWeChatHF.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeChatHF.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lblWeChatHF.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(133, Byte), Integer), CType(CType(218, Byte), Integer))
        Me.lblWeChatHF.Location = New System.Drawing.Point(150, 575)
        Me.lblWeChatHF.Name = "lblWeChatHF"
        Me.lblWeChatHF.Size = New System.Drawing.Size(602, 19)
        Me.lblWeChatHF.TabIndex = 32
        Me.lblWeChatHF.TabStop = True
        Me.lblWeChatHF.Text = "安卓设备微信记录误删，何如恢复？"
        Me.lblWeChatHF.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.sub_new
        Me.PictureBox1.Location = New System.Drawing.Point(124, 574)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(20, 20)
        Me.PictureBox1.TabIndex = 33
        Me.PictureBox1.TabStop = False
        '
        'frmCommonWel
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(967, 633)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.lblWeChatHF)
        Me.Controls.Add(Me.lblNoConnected1)
        Me.Controls.Add(Me.lblOpenWechatAssisant)
        Me.Controls.Add(Me.lklOpenDebug)
        Me.Controls.Add(Me.lblDevNoConn)
        Me.Controls.Add(Me.picDefault)
        Me.Controls.Add(Me.lblLinkMode)
        Me.Controls.Add(Me.lklUnrecognize)
        Me.Name = "frmCommonWel"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.Text = "t "
        CType(Me.picDefault, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents lblDevNoConn As System.Windows.Forms.Label
    Friend WithEvents lblLinkMode As System.Windows.Forms.Label
    Friend WithEvents lklUnrecognize As System.Windows.Forms.LinkLabel
    Friend WithEvents lklOpenDebug As System.Windows.Forms.LinkLabel
    Friend WithEvents picDefault As System.Windows.Forms.PictureBox
    Friend WithEvents lblNoConnected1 As tbLabel
    Friend WithEvents lblOpenWechatAssisant As System.Windows.Forms.LinkLabel
    Friend WithEvents lblWeChatHF As System.Windows.Forms.LinkLabel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
End Class
