﻿Imports itong.Components


Public Class frmPreview
    Private mApplication As IApplication = Nothing

    Public Event ReGetScreenshot As EventHandler

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication)
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。


        Me.mApplication = application
        Me.Language = application.Language
        Me.Icon = My.Resources.iTong
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Welcome.Button.Screenshot")                                                '"屏幕截屏"
        Me.lblMessage.Text = Me.Language.GetString("Welcome.Message.UnlockDeviceIfBlackScreenshot")                 '"截取图片均为设备实时界面截图。如遇黑屏，请解锁设备重新截屏。"
        Me.btnCopyToClipboard.Text = Me.Language.GetString("Common.Button.Copy")                                    '"复制"
        Me.btnSaveToPC.Text = Me.Language.GetString("Common.Button.Save")                                           '"保存"
        Me.tsmiScreenshot.Text = Me.Language.GetString("Welcome.Text.ScreenshotNoShell")                            '"截屏(无外壳)"
        Me.tsmiScreenshotShell.Text = Me.Language.GetString("Welcome.Text.ScreenshotHaveShell")                     '"截屏(带外壳)"

        Me.btnScreenshot.tbToolTip = Me.Language.GetString("Common.Button.Refresh")                                 '"刷新"
        Me.btnScreenshotSetUp.tbToolTip = Me.Language.GetString("Common.Button.Setting")                            '"设置"
        Me.lblLoading.Text = Me.Language.GetString("App.Cell.Loading") 'loading
        Me.lblState.Text = ""
    End Sub


    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        If Not Folder.LangType = LanguageType.en_US Then
            Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
        End If
    End Sub

#End Region

#Region "--- 设置 ---"

    '设置处理事件
    Private Sub btnScreenshotSetUp_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScreenshotSetUp.Click
        If IniSetting.GetScreenshotShell() Then
            Me.tsmiScreenshotShell.Image = My.Resources.tsmi_checked
            Me.tsmiScreenshot.Image = Nothing
        Else
            Me.tsmiScreenshotShell.Image = Nothing
            Me.tsmiScreenshot.Image = My.Resources.tsmi_checked
        End If
        Me.munSetUp.Show(Me.btnScreenshotSetUp, New Point(0, Me.btnScreenshotSetUp.Bottom + 1))
    End Sub

    Private Sub tsmiScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiScreenshot.Click
        Me.tsmiScreenshotShell.Image = Nothing
        Me.tsmiScreenshot.Image = My.Resources.tsmi_checked
        IniSetting.SetScreenshotShell(False)
    End Sub

    Private Sub tsmiScreenshotShell_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiScreenshotShell.Click

        Me.tsmiScreenshotShell.Image = My.Resources.tsmi_checked
        Me.tsmiScreenshot.Image = Nothing
        IniSetting.SetScreenshotShell(True)
    End Sub

#End Region

#Region "--- 事件处理 ---"

    Private Sub btnScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnScreenshot.Click
        Me.SetContralStatus(False)
        Dim args As New EventArgs()
        RaiseEvent ReGetScreenshot(Me, args)
        Me.SetContralStatus(True)
    End Sub

    Private Sub btnCopyToClipboard_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCopyToClipboard.Click
        Try
            Clipboard.SetImage(Me.pbScreenshot.Image)
            Me.lblState.Text = Me.Language.GetString("Welcome.Message.CopySucceed")                 '"复制成功！"
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnSaveToPC_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSaveToPC.Click
        Me.SetContralStatus(False)
        Dim dlg As New SaveFileDialog()
        dlg.Filter = String.Format("{0}(*.png)|*.png", Me.Language.GetString("Common.OpenFileDialog.PicFile"))       '"图片文件"
        dlg.FileName = String.Format("{0}.png", DateTime.Now.ToString("yyyyMMdd_HHmmss"))
        If dlg.ShowDialog = Windows.Forms.DialogResult.OK Then
            Me.pbScreenshot.Image.Save(dlg.FileName, Imaging.ImageFormat.Png)
            Me.lblState.Text = Me.Language.GetString("File.Message.SaveSucceed")                '"保存成功."
        End If
        Me.SetContralStatus(True)
    End Sub

    Private Sub pbScreenshot_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles pbScreenshot.DoubleClick

        '' Modified by Utmost20150409
        '' 截屏的Bug
        Try
            Using showPicFrm As New tbImageViewForm()
                Dim tmpImage As Image = Me.pbScreenshot.Image

                With showPicFrm
                    .SrcPicture = tmpImage.Clone()
                    .ViewType = ViewType.FromPC
                    .ShowListView = False
                    .HideButton = ToolBarButtonType.Main Or ToolBarButtonType.Delete Or ToolBarButtonType.WeiBo
                    .ShowDialog()

                    .Close()
                End With

            End Using
        Catch ex As Exception

        End Try

    End Sub

#End Region

    Private Sub SetContralStatus(ByVal blnEnable As Boolean)
        Me.btnScreenshot.Enabled = blnEnable
        Me.btnScreenshotSetUp.Enabled = blnEnable
        Me.btnCopyToClipboard.Enabled = blnEnable
        Me.btnSaveToPC.Enabled = blnEnable
        Application.DoEvents()
    End Sub

    Private Delegate Sub SetLabelMessageStatusHandler(ByVal blnVisible As Boolean, ByVal is_iPhone As Boolean)
    Public Sub SetLabelMessageStatus(ByVal blnStatus As Boolean, ByVal is_iPhone As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetLabelMessageStatusHandler(AddressOf SetLabelMessageStatus), blnStatus, is_iPhone)
        Else
            Me.lblMessage.Visible = blnStatus
            If blnStatus Then
                Me.lblMessage.Padding = System.Windows.Forms.Padding.Empty '  New System.Windows.Forms.Padding(3, 0, 0, 0)
                If is_iPhone AndAlso IniSetting.GetScreenshotShell() Then
                    Me.lblMessage.Width = 120
                    Me.lblMessage.Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
                Else
                    Me.lblMessage.Width = 165
                    Me.lblMessage.Font = Common.CreateFont("Arial", 9.75, FontStyle.Regular)
                End If
                Me.lblMessage.Location = New Point((Me.Width - Me.lblMessage.Width) / 2, Me.lblMessage.Location.Y)
            End If
        End If
    End Sub

    Private Delegate Sub SetPictureBoxImageHandler(ByVal img As Image)
    Public Sub SetPictureBoxImage(ByVal img As Image)
        If Me.InvokeRequired Then
            Me.Invoke(New SetPictureBoxImageHandler(AddressOf SetPictureBoxImage), img)
        Else
            Me.pbScreenshot.SizeMode = PictureBoxSizeMode.Zoom
            Me.pbScreenshot.Image = img
        End If
    End Sub

    Private Delegate Sub SetPanelLoadingHandler(ByVal blnVisible As Boolean)
    Public Sub SetPanelLoading(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetPanelLoadingHandler(AddressOf SetPanelLoading), blnVisible)
        Else
            Try
                Me.pnlLoading.Visible = blnVisible

                Me.pbScreenshot.Visible = Not blnVisible
                Me.SetContralStatus(Not blnVisible)

                If blnVisible Then
                    Dim intLeft As Integer = (Me.pnlLoading.Width - Me.pbLoading.Width - Me.lblLoading.Width) \ 2
                    Me.pbLoading.Left = intLeft
                    Me.lblLoading.Location = New Point(Me.pbLoading.Right, Me.pbLoading.Top + (Me.pbLoading.Height - Me.lblLoading.Height) \ 2)
                    Me.pnlLoading.BringToFront()
                Else
                    Me.pnlLoading.SendToBack()
                End If
            Catch ex As Exception
            End Try
        End If
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        If Me.tbGuiBackground IsNot Nothing Then
            Me.tbGuiBackground = My.Resources.frm_bg_state
        End If
    End Sub

End Class
