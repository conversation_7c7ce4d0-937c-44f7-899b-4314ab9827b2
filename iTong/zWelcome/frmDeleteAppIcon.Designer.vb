﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDeleteAppIcon
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmDeleteAppIcon))
        Me.btnDelete = New iTong.Components.tbButton()
        Me.TbLabel1 = New iTong.Components.tbLabel()
        Me.TbLabel2 = New iTong.Components.tbLabel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.btnCancel = New iTong.Components.tbButton()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(529, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(505, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(481, 0)
        '
        'btnDelete
        '
        Me.btnDelete.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDelete.BackColor = System.Drawing.Color.Transparent
        Me.btnDelete.BindingForm = Nothing
        Me.btnDelete.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDelete.Location = New System.Drawing.Point(138, 240)
        Me.btnDelete.Name = "btnDelete"
        Me.btnDelete.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDelete.Selectable = True
        Me.btnDelete.Size = New System.Drawing.Size(128, 32)
        Me.btnDelete.TabIndex = 25
        Me.btnDelete.tbAdriftIconWhenHover = False
        Me.btnDelete.tbAutoSize = False
        Me.btnDelete.tbAutoSizeEx = False
        Me.btnDelete.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnDelete.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDelete.tbBadgeNumber = 0
        Me.btnDelete.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDelete.tbEndEllipsis = False
        Me.btnDelete.tbIconHoldPlace = True
        Me.btnDelete.tbIconImage = Nothing
        Me.btnDelete.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDelete.tbIconMore = False
        Me.btnDelete.tbIconMouseDown = Nothing
        Me.btnDelete.tbIconMouseHover = Nothing
        Me.btnDelete.tbIconMouseLeave = Nothing
        Me.btnDelete.tbIconPlaceText = 2
        Me.btnDelete.tbIconReadOnly = Nothing
        Me.btnDelete.tbImageMouseDown = Nothing
        Me.btnDelete.tbImageMouseHover = Nothing
        Me.btnDelete.tbImageMouseLeave = Nothing
        Me.btnDelete.tbProgressValue = 50
        Me.btnDelete.tbReadOnly = False
        Me.btnDelete.tbReadOnlyText = False
        Me.btnDelete.tbShadow = False
        Me.btnDelete.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDelete.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDelete.tbShowDot = False
        Me.btnDelete.tbShowMoreIconImg = CType(resources.GetObject("btnDelete.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDelete.tbShowNew = False
        Me.btnDelete.tbShowProgress = False
        Me.btnDelete.tbShowTip = True
        Me.btnDelete.tbShowToolTipOnButton = False
        Me.btnDelete.tbSplit = "3,3,3,3"
        Me.btnDelete.tbText = "立即删除"
        Me.btnDelete.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.tbTextColor = System.Drawing.Color.White
        Me.btnDelete.tbTextColorDisable = System.Drawing.Color.White
        Me.btnDelete.tbTextColorDown = System.Drawing.Color.White
        Me.btnDelete.tbTextColorHover = System.Drawing.Color.White
        Me.btnDelete.tbTextMouseDownPlace = 0
        Me.btnDelete.tbToolTip = ""
        Me.btnDelete.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDelete.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDelete.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDelete.VisibleEx = True
        '
        'TbLabel1
        '
        Me.TbLabel1.BackColor = System.Drawing.Color.Transparent
        Me.TbLabel1.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.TbLabel1.Location = New System.Drawing.Point(32, 56)
        Me.TbLabel1.Name = "TbLabel1"
        Me.TbLabel1.Size = New System.Drawing.Size(488, 47)
        Me.TbLabel1.TabIndex = 26
        Me.TbLabel1.tbAdriftWhenHover = False
        Me.TbLabel1.tbAutoEllipsis = False
        Me.TbLabel1.tbAutoSize = False
        Me.TbLabel1.tbHideImage = False
        Me.TbLabel1.tbIconImage = Nothing
        Me.TbLabel1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel1.tbIconPlaceText = 5
        Me.TbLabel1.tbShadow = False
        Me.TbLabel1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel1.tbShowScrolling = False
        Me.TbLabel1.Text = """删除废图标""功能用来删除设备桌面上无效的图标，但同时也会误删" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & """隐形APP""的文稿和数据，请谨慎使用！"
        Me.TbLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'TbLabel2
        '
        Me.TbLabel2.AutoSize = True
        Me.TbLabel2.BackColor = System.Drawing.Color.Transparent
        Me.TbLabel2.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.TbLabel2.ForeColor = System.Drawing.SystemColors.AppWorkspace
        Me.TbLabel2.Location = New System.Drawing.Point(73, 111)
        Me.TbLabel2.Margin = New System.Windows.Forms.Padding(3)
        Me.TbLabel2.Name = "TbLabel2"
        Me.TbLabel2.Size = New System.Drawing.Size(407, 24)
        Me.TbLabel2.TabIndex = 27
        Me.TbLabel2.tbAdriftWhenHover = False
        Me.TbLabel2.tbAutoEllipsis = False
        Me.TbLabel2.tbAutoSize = True
        Me.TbLabel2.tbHideImage = False
        Me.TbLabel2.tbIconImage = Nothing
        Me.TbLabel2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel2.tbIconPlaceText = 5
        Me.TbLabel2.tbShadow = False
        Me.TbLabel2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel2.tbShowScrolling = False
        Me.TbLabel2.Text = """隐形APP""是指：长期未使用而被系统自动卸载的APP,它们虽然被系统卸载，" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "但文稿和数据并未被删除，重装APP后，之前的文稿和数据会被恢复。"
        '
        'PictureBox1
        '
        Me.PictureBox1.BackColor = System.Drawing.Color.Transparent
        Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.clearapp
        Me.PictureBox1.Location = New System.Drawing.Point(179, 161)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(194, 48)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBox1.TabIndex = 28
        Me.PictureBox1.TabStop = False
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.Location = New System.Drawing.Point(287, 240)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(128, 32)
        Me.btnCancel.TabIndex = 25
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbProgressValue = 50
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCancel.tbShowDot = False
        Me.btnCancel.tbShowMoreIconImg = CType(resources.GetObject("btnCancel.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowProgress = False
        Me.btnCancel.tbShowTip = True
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "3,3,3,3"
        Me.btnCancel.tbText = "暂不删除"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.White
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.White
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.White
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.White
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'frmDeleteAppIcon
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(244, Byte), Integer), CType(CType(245, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(553, 303)
        Me.Controls.Add(Me.PictureBox1)
        Me.Controls.Add(Me.TbLabel2)
        Me.Controls.Add(Me.TbLabel1)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnDelete)
        Me.MaximizeBox = False
        Me.Name = "frmDeleteAppIcon"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,5,54"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "删除废图标"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.btnDelete, 0)
        Me.Controls.SetChildIndex(Me.btnCancel, 0)
        Me.Controls.SetChildIndex(Me.TbLabel1, 0)
        Me.Controls.SetChildIndex(Me.TbLabel2, 0)
        Me.Controls.SetChildIndex(Me.PictureBox1, 0)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnDelete As iTong.Components.tbButton
    Friend WithEvents TbLabel1 As iTong.Components.tbLabel
    Friend WithEvents TbLabel2 As iTong.Components.tbLabel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents btnCancel As iTong.Components.tbButton
End Class
