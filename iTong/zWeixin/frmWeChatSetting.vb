﻿Public Class frmWeChatSetting


    Public Shared Event WeChatSettingHandler(ByVal sender As Object, ByVal args As EventArgs) '单独备份进度回调
    Private mIsFirstExport As Boolean = False

#Region "--- 初始化 ---"

    Public Sub New(Optional ByVal isFirstExport As Boolean = False)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FilletRadius = 5
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong
        Me.mIsFirstExport = isFirstExport
        If Folder.AppType = RunType.iDataRecovery Then
            Me.tbTitleForeColor = Color.FromArgb(51, 51, 51)
        End If
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.chkLinkIsExcel.Checked = IniSetting.GetWeChatExportExcel()
        Me.cbxExportAudioConvert.Checked = IniSetting.GetIsAudioConvert()

        Me.cbxWCExportFormatExcel.Checked = IniSetting.GetWCExportFormatExcel()
        Me.cbxWCExportFormatHtml.Checked = IniSetting.GetWCExportFormatHtml()
        Me.cbxWCExportFormatTxt.Checked = IniSetting.GetWCExportFormatTxt()


        Me.cbxExportVoice.Checked = IniSetting.GetIsHtmlExportVoice()
        Me.cbxExportPicture.Checked = IniSetting.GetIsHtmlExportPicture()
        Me.cbxExportVideo.Checked = IniSetting.GetIsHtmlExportVideo()

        Me.cbxWeChatDataMigration.Checked = IniSetting.GetIsWeChatDataMigration()
        Me.cbxImportAloneBackup.Checked = IniSetting.GetIsWeChatImportAloneBackup()
        Me.cbxExcelCoding.Text = IniSetting.GetWeChatExportExcelCoding()

        Me.cbxWeChatIsDoFindDate.Checked = IniSetting.GetWeChatIsDoFindDate()

        Me.cbxWeChatExportSuccessOpenFolder.Checked = IniSetting.GetWeChatExportSuccessOpenFolder()

#If IS_WECHAT Then
        Me.cbxWeChatDataMigration.Visible = False
#Else
        Me.cbxWeChatDataMigration.Visible = True
#End If

        If Folder.AppType = RunType.iWeChatMigration Then
            Me.cbxImportAloneBackup.Visible = True
        Else
            Me.cbxImportAloneBackup.Visible = False
        End If

        If Folder.AppType = RunType.iDataRecovery_Abroad Then
            Me.tbGuiBackground = My.Resources.frm_bg_state1
            Me.btn_close.tbBackgroundImage = My.Resources.Resources.btn_close1
            Me.tbSplit = "3,35,3,60"
            Me.tbTitleForeColor = Color.FromArgb(51, 51, 51)
            Me.ForeColor = Color.Black
        End If

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.lblFirstExport.Visible = Me.mIsFirstExport
        Me.lblFirstExport.Text = Me.Language.GetString("Common.Button.FirstExport")

        Me.Text = Me.Language.GetString("Common.Button.Setting")
        Me.chkLinkIsExcel.Text = Me.Language.GetString("WeChat.Button.ExportExcel") '"「导出到电脑」同时导出表格式数据（Excel）"
        Me.cbxExportAudioConvert.Text = Me.Language.GetString("WeChat.Message.Convert") '"将导出的语音文件.aud格式转换成.mp3格式。"

        Me.cbxExportVoice.Text = Me.Language.GetString("Common.Button.Voice")     '"同时导出语音"
        Me.cbxExportPicture.Text = Me.Language.GetString("Common.Button.Picture")   '"同时导出图片"
        Me.cbxExportVideo.Text = Me.Language.GetString("Common.Button.Video")     '"同时导出小视频"

        Me.btnOK.Text = Me.Language.GetString("Common.Button.Save")
        Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")

        Me.lblExportSetting.Text = Me.Language.GetString("WeChat.Lable.ExportSetting")
        Me.lblExportExcel.Text = Me.Language.GetString("WeChat.Lable.ExportExcelSetting")

        Me.lblOther.Text = Me.Language.GetString("Common.Button.OtherSetting")     '其他设置
        Me.cbxWeChatDataMigration.Text = Me.Language.GetString("Common.Button.OpenDataMigration")  '"启用微信分身数据迁移"
        Me.cbxImportAloneBackup.Text = Me.Language.GetString("Weixin.Button.ImportAloneBackup") '导入单独备份

        Me.cbxWeChatIsDoFindDate.Text = Me.Language.GetString("Weixin.Button.AutoFindData") ' "自动查找删除的聊天记录"
        Me.cbxWeChatExportSuccessOpenFolder.Text = Me.Language.GetString("Weixin.Button.AutoOpenFolder") ' "导出成功后是否自动打开所在目录"

        Me.lblExport.Text = Me.Language.GetString("Weixin.Button.ExportComputerSetting")
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        If Not Me.cbxWCExportFormatHtml.Checked AndAlso Not Me.cbxWCExportFormatExcel.Checked AndAlso Not Me.cbxWCExportFormatTxt.Checked Then
            Me.lblFirstExport.Text = Me.Language.GetString("Common.Button.FirstExportEx")
            Me.lblFirstExport.Visible = True
            Return
        End If

        Try
            IniSetting.SetWeChatExportExcel(Me.chkLinkIsExcel.Checked)
            IniSetting.SetIsAudioConvert(Me.cbxExportAudioConvert.Checked)

            IniSetting.SetWCExportFormatExcel(Me.cbxWCExportFormatExcel.Checked)
            IniSetting.SetWCExportFormatHtml(Me.cbxWCExportFormatHtml.Checked)
            IniSetting.SetWCExportFormatTxt(Me.cbxWCExportFormatTxt.Checked)

            IniSetting.SetIsHtmlExportVoice(Me.cbxExportVoice.Checked)
            IniSetting.SetIsHtmlExportPicture(Me.cbxExportPicture.Checked)
            IniSetting.SetIsHtmlExportVideo(Me.cbxExportVideo.Checked)

            IniSetting.SetIsWeChatDataMigration(Me.cbxWeChatDataMigration.Checked)
            IniSetting.SetIsWeChatImportAloneBackup(Me.cbxImportAloneBackup.Checked)

            IniSetting.SetWeChatIsDoFindDate(Me.cbxWeChatIsDoFindDate.Checked)
            IniSetting.SetWeChatExportSuccessOpenFolder(Me.cbxWeChatExportSuccessOpenFolder.Checked)

            IniSetting.SetWeChatExportExcelCoding(Me.cbxExcelCoding.Text)

            RaiseEvent WeChatSettingHandler(Me, New EventArgs())
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnOK_Click")
        End Try

        Me.Close()
    End Sub

#End Region

End Class