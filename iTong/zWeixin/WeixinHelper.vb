﻿Imports System.Data.SQLite
'Imports System.Data.SQLite3
Imports System.Threading
Imports System.Windows.Forms
Imports System.Drawing
Imports System.Text
Imports System.IO
Imports System.Runtime.InteropServices
Imports iTong.CoreFoundation
Imports iTong.Device
Imports ProtoBuf
Imports System.Xml

Public Class WeixinHelper
    Enum FileType
        Amr
        Silk
    End Enum

    <DllImport("tbChatWebPage.temp", CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function Aud2Wav(ByVal armFile As String, ByVal wavFile As String, ByVal key As String) As Integer
    End Function

    Public Shared Function Aud2WavEx(ByVal armFile As String, ByVal wavFile As String, ByVal key As String, Optional ByVal strMessage As String = "") As Integer
        Dim type As FileType = WeixinHelper.CheckFileType(armFile, strMessage)
        Dim iReusult As Integer = 0

        If Common.IsX64 Then
            Dim list As New List(Of String)
            If type = FileType.Amr Then
                list.Add("Aud2Wav")
                list.Add(armFile)
                list.Add(wavFile)
                list.Add(key)
            Else
                list.Add("SilkToWav")
                list.Add(armFile)
                list.Add(wavFile)
            End If
            Common.CallCmdExe(list.ToArray())

        Else
            Select Case type
                Case FileType.Amr
                    iReusult = WeixinHelper.Aud2Wav(armFile, wavFile, key)

                Case FileType.Silk
                    iReusult = WeixinHelper.SilkToWav(armFile, wavFile)

            End Select

            '返回值:
            '-1  打开失败
            '-2	 转化失败
            '-3  读取失败
            '-4  格式错误
            '0:  成功
            '微信主意导出失败的时候输出错误类型后面可以追踪
            If iReusult < 0 Then
                Common.Log(String.Format("Audio Convert Failure {0}  {1}", iReusult, armFile))
            End If
        End If

        Return iReusult
    End Function

    Public Shared Function Wav2Mp3(ByVal armFile As String, ByVal wavFile As String, ByVal mp3File As String, ByVal key As String, Optional ByVal strMessage As String = "") As Integer
        Dim iReusult As Integer = 0
        Try
            iReusult = Aud2WavEx(armFile, wavFile, key, strMessage)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Wav2Mp3_1")
        End Try

        Try
            Dim strArguments As String = String.Format(" -V0 ""{0}"" ""{1}"" ", wavFile, mp3File)
            Dim strUndark As String = Path.Combine(Folder.AppFolder, "WeChat\lame.exe")

            Dim info As ProcessStartInfo = New ProcessStartInfo()
            info.FileName = strUndark
            info.CreateNoWindow = True
            info.UseShellExecute = False
            info.Arguments = strArguments

            Dim process As Process = New Process()
            process.StartInfo = info
            process.Start()
            process.WaitForExit()
            process.Close()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "Wav2Mp3_2")
        End Try
        Return iReusult
    End Function

    'Private Shared Sub RepairErrorFile(ByVal strFilePath As String)
    '    Dim strTmp As String = strFilePath & ".error"
    '    Try
    '        File.Move(strFilePath, strTmp)
    '    Catch ex As Exception

    '    End Try
    '    Dim byffer As Byte() = File.ReadAllBytes(strTmp)
    '    Using stream As System.IO.FileStream = New FileStream(strFilePath, FileMode.Create)
    '        stream.Write(byffer, 6, byffer.Length - 6)
    '    End Using
    '    Try
    '        File.Delete(strTmp)
    '    Catch ex As Exception

    '    End Try
    'End Sub

    Private Shared Function CheckFileType(ByVal strFile As String, ByVal strMessage As String) As FileType
        Dim type As FileType = FileType.Silk
        Dim byffer As Byte() = File.ReadAllBytes(strFile)

        If byffer.Length <= 0 Then
            Return type
        End If

        'Amr文件
        If byffer.Length > 13 AndAlso byffer(0) = &H23 AndAlso byffer(1) = &H21 AndAlso byffer(2) = &H41 AndAlso byffer(3) = &H4D AndAlso byffer(4) = &H52 AndAlso byffer(5) = &HA AndAlso _
           Not byffer(9) = &H53 AndAlso Not byffer(10) = &H49 AndAlso Not byffer(11) = &H4C AndAlso Not byffer(12) = &H4B Then
            type = FileType.Amr

            'Silk文件
        ElseIf byffer.Length > 7 AndAlso byffer(3) = &H53 AndAlso byffer(4) = &H49 AndAlso byffer(5) = &H4C AndAlso byffer(6) = &H4B Then
            type = FileType.Silk

            'ElseIf chat IsNot Nothing AndAlso Not String.IsNullOrEmpty(chat.StrMessage) AndAlso chat.StrMessage.Contains("voiceformat=""0""") Then
        ElseIf Not String.IsNullOrEmpty(strMessage) AndAlso strMessage.Contains("voiceformat=""0""") Then
            type = FileType.Amr
            '低版本的微信发消息给高版本的微信会出现语音无法转换的问题。
            WeixinHelper.UpdateAmrFile(strFile, byffer)

        End If
        Return type
    End Function

    Private Shared Function UpdateAmrFile(ByVal strFilePath As String, ByVal byffer As Byte()) As Boolean
        Dim blnReturn As Boolean = False
        Try
            If byffer.Length < 10 Then
                Common.LogException("File Error", "WeixinHelper_UpdateAmrFile")
                Return blnReturn
            End If
            Dim byfferNew(byffer.Length + 6) As Byte
            byfferNew(0) = &H23
            byfferNew(1) = &H21
            byfferNew(2) = &H41
            byfferNew(3) = &H4D
            byfferNew(4) = &H52
            byfferNew(5) = &HA
            Dim intIndex As Integer = 0
            For Each item As Byte In byffer
                byfferNew(intIndex + 6) = item
                intIndex += 1
            Next
            File.WriteAllBytes(strFilePath, byfferNew)
            blnReturn = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinHelper_UpdateAmrFile")
        End Try
        Return blnReturn
    End Function

    <DllImport("tbChatWebPageEx.temp", CallingConvention:=CallingConvention.Cdecl)> _
    Private Shared Function SilkToWav(ByVal silkfilename As String, ByVal wavfilename As String) As Integer
    End Function

    Public Shared Sub InitEnvironmentPath()
        Dim strPath As String = Environment.GetEnvironmentVariable("Path")
        Dim strNewPath As String = Path.Combine(Folder.CacheFolder, "RubbishClearWebPage")

        strPath = strPath.TrimEnd(";") & ";" & strNewPath

        Environment.SetEnvironmentVariable("Path", strPath, EnvironmentVariableTarget.Process)
    End Sub

    '释放aud.dll文件（aud转wav函数）
    Public Shared Sub ReleaseAudDll()
        Try
            Dim strFilePath As String = Path.Combine(Folder.CacheFolder, "RubbishClearWebPage")
            Folder.CheckFolder(strFilePath)
            Dim strFile As String = Path.Combine(strFilePath, "tbChatWebPage.temp")

            If File.Exists(strFile) Then
                Dim fileInfo As FileInfo = New FileInfo(strFile)

                If fileInfo.Length <> My.Resources.weixin_dll_aud.Length Then
                    Try
                        File.Delete(strFile)
                    Catch
                    End Try
                End If
            End If

            If Not File.Exists(strFile) Then
                Using objWriter As New FileStream(strFile, FileMode.OpenOrCreate, FileAccess.Write)
                    objWriter.Write(My.Resources.weixin_dll_aud, 0, My.Resources.weixin_dll_aud.Length)
                End Using
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinHelper_ReleaseAudDll")
        End Try
    End Sub

    '释放silk.dll文件（silk转wav函数）
    Public Shared Sub ReleaseSilkDll()
        Try
            Dim strFilePath As String = Path.Combine(Folder.CacheFolder, "RubbishClearWebPage")
            Folder.CheckFolder(strFilePath)
            Dim strFile As String = Path.Combine(strFilePath, "tbChatWebPageEx.temp")

            If File.Exists(strFile) Then
                Dim fileInfo As FileInfo = New FileInfo(strFile)

                If fileInfo.Length <> My.Resources.weixin_dll_silk.Length Then
                    Try
                        File.Delete(strFile)
                    Catch
                    End Try
                End If
            End If

            If Not File.Exists(strFile) Then
                Using objWriter As New FileStream(strFile, FileMode.OpenOrCreate, FileAccess.Write)
                    objWriter.Write(My.Resources.weixin_dll_silk, 0, My.Resources.weixin_dll_silk.Length)
                End Using
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinHelper_ReleaseSilkDll")
        End Try
    End Sub

    ' UTC 时间，Windows的时间是有时区的，要加上 UTC 和当前时区相差的小时数
    Private Shared mUTC2LocalTime As Integer = TimeZone.CurrentTimeZone.GetUtcOffset(DateTime.Now).Hours
    Public Shared Function ConvertWeixinToPcTime(ByVal timeMillis As Long) As DateTime
        If timeMillis = 0 Then
            Return System.DateTime.MinValue
        End If

        'Java中可以用System.currentTimeMillis() 获取当前时间的long形式，它的标示形式是从1970年1月1日起的到当前的毫秒的数。
        Dim timeTicks As Long = timeMillis * 10000000 + New DateTime(1970, 1, 1, 0, 0, 0).Ticks
        Dim deviceTime As New System.DateTime(timeTicks)

        Return deviceTime.AddHours(mUTC2LocalTime)
    End Function

    '搜索的时间范围，开始时间：即这天的最早时间，结束时间：即这天的最晚时间。
    Public Shared Function ConvertPcTimeToWeixin(ByVal time As DateTime) As Long
        Dim TempTime As DateTime = time.AddHours(-mUTC2LocalTime)

        Dim longTime As Long = TempTime.Ticks
        longTime = (longTime - New DateTime(1970, 1, 1, 0, 0, 0).Ticks) \ 10000000

        Return longTime
    End Function

End Class

'微信主要数据库（联系人和聊天记录）
Public Class WeixinMMDB
    Private ReadOnly mDBFile As String = String.Empty
    Private mConn As Object = Nothing

    Private mstrSKU As String = "com.tencent.xin"

    Private Shared mLstIllegalASCII As List(Of Char) = Nothing
    ''' <summary>
    ''' 不可见字符  http://ascii.911cha.com/
    ''' </summary>
    ''' <value></value>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared ReadOnly Property LstIllegalASCII As List(Of Char)
        Get
            If mLstIllegalASCII Is Nothing Then
                mLstIllegalASCII = New List(Of Char)
                mLstIllegalASCII.Add(Convert.ToChar(0))
                mLstIllegalASCII.Add(Convert.ToChar(1))
                mLstIllegalASCII.Add(Convert.ToChar(2))
                mLstIllegalASCII.Add(Convert.ToChar(3))
                mLstIllegalASCII.Add(Convert.ToChar(4))
                mLstIllegalASCII.Add(Convert.ToChar(5))
                mLstIllegalASCII.Add(Convert.ToChar(6))
                mLstIllegalASCII.Add(Convert.ToChar(7))
                mLstIllegalASCII.Add(Convert.ToChar(8))
                mLstIllegalASCII.Add(Convert.ToChar(9))
                mLstIllegalASCII.Add(Convert.ToChar(10))
                mLstIllegalASCII.Add(Convert.ToChar(11))
                mLstIllegalASCII.Add(Convert.ToChar(12))
                mLstIllegalASCII.Add(Convert.ToChar(13))
                mLstIllegalASCII.Add(Convert.ToChar(14))
                mLstIllegalASCII.Add(Convert.ToChar(15))
                mLstIllegalASCII.Add(Convert.ToChar(16))
                mLstIllegalASCII.Add(Convert.ToChar(17))
                mLstIllegalASCII.Add(Convert.ToChar(18))
                mLstIllegalASCII.Add(Convert.ToChar(19))
                mLstIllegalASCII.Add(Convert.ToChar(20))
                mLstIllegalASCII.Add(Convert.ToChar(21))
                mLstIllegalASCII.Add(Convert.ToChar(22))
                mLstIllegalASCII.Add(Convert.ToChar(23))
                mLstIllegalASCII.Add(Convert.ToChar(24))
                mLstIllegalASCII.Add(Convert.ToChar(25))
                mLstIllegalASCII.Add(Convert.ToChar(26))
                mLstIllegalASCII.Add(Convert.ToChar(27))
                mLstIllegalASCII.Add(Convert.ToChar(28))
                mLstIllegalASCII.Add(Convert.ToChar(29))
                mLstIllegalASCII.Add(Convert.ToChar(30))
                mLstIllegalASCII.Add(Convert.ToChar(31))
                mLstIllegalASCII.Add(Convert.ToChar(127))
            End If
            Return mLstIllegalASCII
        End Get
    End Property

#Region "--- 单实例 ---"

    Private Shared instance As WeixinMMDB = Nothing
    Private Shared ReadOnly locker As New Object()
    Private Shared lstInstance As New Dictionary(Of String, WeixinMMDB)

    Public Shared Function GetInstance(ByVal DBFile As String) As WeixinMMDB
        If lstInstance.ContainsKey(DBFile) AndAlso lstInstance(DBFile) IsNot Nothing AndAlso String.Compare(DBFile, lstInstance(DBFile).mDBFile, True) <> 0 Then
            lstInstance(DBFile).Dispose()
            lstInstance(DBFile) = Nothing
            lstInstance.Remove(DBFile)
        End If
        'If instance IsNot Nothing Then
        '    If String.Compare(DBFile, instance.mDBFile, True) <> 0 Then
        '        instance.Dispose()
        '        instance = Nothing
        '    End If
        'End If

        If instance Is Nothing Then
            SyncLock locker
                'If instance Is Nothing Then
                '    instance = New WeixinMMDB(DBFile)
                'End If
                If lstInstance.ContainsKey(DBFile) Then
                    lstInstance(DBFile) = New WeixinMMDB(DBFile)
                Else
                    lstInstance.Add(DBFile, New WeixinMMDB(DBFile))
                End If

            End SyncLock
        End If

        Return lstInstance(DBFile)
    End Function

#End Region

#Region "--- 构造函数 ---"

    Private Sub New(ByVal DBFile As String)
        mDBFile = DBFile
        Reload()
    End Sub

    Public Sub Dispose()
        Try
            Me.CloseDB()

            instance = Nothing
        Catch
        End Try
    End Sub

    Public ReadOnly Property DbFile() As String
        Get
            Return Me.mDBFile
        End Get
    End Property

#End Region

#Region "--- 加载数据库 ---"

    Public Function Reload() As Boolean
        Dim result As Boolean = False
        Try
            If mConn IsNot Nothing AndAlso mConn.State = System.Data.ConnectionState.Open Then
                mConn.Close()
            End If

            If Not File.Exists(mDBFile & "-shm") Then
                mConn = SQLiteClass.CreateConnectionFromFile(mDBFile)
            End If

            If mConn Is Nothing Then
                mConn = SQLiteClass3.CreateConnectionFromFile(mDBFile)
            End If

            result = True
            Me.CheckEmoticonPackage05()
        Catch ex As Exception
            Common.LogException(ex.ToString, "Reload")
        End Try

        Return result
    End Function

    Public Function CloseDB() As Boolean
        Dim result As Boolean = False
        Try
            If mConn IsNot Nothing AndAlso mConn.State = System.Data.ConnectionState.Open Then
                mConn.Close()
            End If
            result = True
        Catch
        End Try

        Return result
    End Function

#End Region

#Region "--- 获取朋友列表 ---"

    Public Function Get_Friend_Info() As Dictionary(Of String, WeChatFriendInfo)
        Dim dicInfos As New Dictionary(Of String, WeChatFriendInfo)()

        'string pstrSQL = @"Select *  from friend order by Last0ChatT desc";
        Dim pstrSQL As String = "select *  from friend,friend_ext where friend.[UsrName] =friend_ext.[UsrName]"
        'order by friend.[Last0ChatT] desc
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim info As New WeChatFriendInfo()
                    info.UsrName = Common.GetValue(Of String)(dr("UsrName"), String.Empty)
                    info.NickName = Common.GetValue(Of String)(dr("NickName"), String.Empty)
                    info.Md5 = Common.ToHexString(Common.GetMd5Array(info.UsrName))
                    For Each item As Char In LstIllegalASCII
                        info.NickName = info.NickName.Replace(item, "")
                    Next

                    info.IntSex = Common.GetValue(Of Integer)(dr("Sex"), 0)

                    info.NoteName = Common.GetValue(Of String)(dr("ConRemark"), String.Empty)
                    info.StrChatRoomMem = Common.GetValue(Of String)(dr("ConChatRoomMem"), String.Empty)
                    info.StrProfile = Common.GetValue(Of String)(dr("ConStrRes2"), String.Empty)

                    dicInfos(info.UsrName) = info
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Friend_Info")
                End Try
            Next
        End If

        Return dicInfos
    End Function

    Public Function Get_Friend_Info_New() As Dictionary(Of String, WeChatFriendInfo)
        Dim dicInfos As New Dictionary(Of String, WeChatFriendInfo)()
        'Dim pstrSQL As String = "select *  from Friend,QQContact where QQContact.UsrName = Friend.userName"
        Dim pstrSQL As String = "select *  from Friend"
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            Dim info As WeChatFriendInfo = Nothing
            For Each dr As DataRow In dt.Rows
                Try

                    'Dim strFile As String = "E:\新建文本文档.txt"
                    'Using writer As StreamWriter = New StreamWriter(strFile, True, Encoding.UTF8)
                    '    writer.WriteLine(dbContactHeadImage)
                    'End Using
                    Dim dbContactLocal As String = ""
                    If Not IsDBNull(dr("dbContactLocal")) Then
                        dbContactLocal = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactLocal")), String.Empty)
                    End If

                    Dim dbContactOther As String = ""
                    If Not IsDBNull(dr("dbContactOther")) Then
                        dbContactOther = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactOther")), String.Empty)
                    End If

                    'Dim dbContactRemark As String = ""
                    'Dim strRemark As String = ""
                    'If Not IsDBNull(dr("dbContactRemark")) Then
                    '    dbContactRemark = Me.GetStrContent(Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactRemark")), String.Empty), strRemark)
                    'End If

                    Dim model As WeChatFriendModel = Nothing
                    If Not IsDBNull(dr("dbContactRemark")) Then
                        model = GetWeChatFriendModel(dr("dbContactRemark"))
                    End If

                    'Dim dbContactHeadImage As String = ""
                    'If Not IsDBNull(dr("dbContactHeadImage")) Then
                    '    dbContactHeadImage = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactHeadImage")), String.Empty)
                    '    For Each item As Char In Me.LstIllegalASCII
                    '        dbContactHeadImage = dbContactHeadImage.Replace(item, "")
                    '    Next
                    '    dbContactHeadImage = dbContactHeadImage.Replace("�", "").Replace("""", "").Replace("|", "").Replace("~", "")
                    'End If

                    Dim HIModel As WeChatHeadLogoModel = Nothing
                    If Not IsDBNull(dr("dbContactHeadImage")) Then
                        HIModel = GetWeChatHeadLogoModel(dr("dbContactHeadImage"))
                    End If

                    Dim dbContactProfile As String = ""
                    If Not IsDBNull(dr("dbContactProfile")) Then
                        dbContactProfile = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactProfile")), String.Empty)
                        '    dbContactProfile = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactProfile")), String.Empty)
                        '    For Each item As Char In Me.LstIllegalASCII
                        '        dbContactProfile = dbContactProfile.Replace(item, "")
                        '    Next
                        '    dbContactProfile = dbContactProfile.Replace("�", ";")
                    End If

                    Dim dbContactSocial As String = ""
                    If Not IsDBNull(dr("dbContactSocial")) Then
                        dbContactSocial = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactSocial")), String.Empty)
                    End If

                    Dim dbContactChatRoom As String = ""
                    If Not IsDBNull(dr("dbContactChatRoom")) Then
                        dbContactChatRoom = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactChatRoom")), String.Empty)
                        For Each item As Char In LstIllegalASCII
                            dbContactChatRoom = dbContactChatRoom.Replace(item, "")
                        Next

                        If dbContactChatRoom.Contains("<RoomData>") AndAlso dbContactChatRoom.Contains("</RoomData>") Then
                            dbContactChatRoom = dbContactChatRoom.Replace("�", "")
                        Else
                            dbContactChatRoom = dbContactChatRoom.Replace("�", "").Replace(":", "").Replace("(", "").Replace("""", "")
                        End If

                    End If

                    Dim dbContactBrand As String = ""
                    If Not IsDBNull(dr("dbContactBrand")) Then
                        dbContactBrand = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactBrand")), String.Empty)
                    End If

                    info = New WeChatFriendInfo()
                    Dim strName As String = Common.GetValue(Of String)(dr("userName"), String.Empty)

                    info.UsrName = strName
                    info.Md5 = Common.ToHexString(Common.GetMd5Array(strName))
                    info.NickName = model.strName 'dbContactRemark
                    'info.Sex = Common.GetValue(Of Integer)(dr("Sex"), 0)
                    info.NoteName = IIf(model.strNickName = Nothing, model.strName, model.strNickName) 'strRemark
                    info.StrChatRoomMem = dbContactChatRoom
                    info.StrProfile = HIModel.HeadLogo123 ' dbContactHeadImage
                    info.SFType = Common.GetValue(Of Integer)(dr("Type"), 0)
                    dicInfos(info.UsrName) = info
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Friend_Info_New")
                End Try
            Next
        End If

        Return dicInfos
    End Function

    Private Function GetWeChatHeadLogoModel(ByVal byteHI As Byte()) As WeChatHeadLogoModel
        Try
            ' Dim byteCR As Byte() = Encoding.UTF8.GetBytes(strContactRemark)
            Using ms As New MemoryStream()
                '将消息写入流中  
                ms.Write(byteHI, 0, byteHI.Length)
                '将流的位置归0  
                ms.Position = 0
                '使用工具反序列化对象  
                Dim result As WeChatHeadLogoModel = ProtoBuf.Serializer.Deserialize(Of WeChatHeadLogoModel)(ms)
                Return result
            End Using
        Catch ex As Exception
            Common.LogException("反序列化失败: " + ex.ToString(), "GetWeChatHeadLogoModel")
            Return Nothing
        End Try
    End Function

    Private Function GetWeChatFriendModel(ByVal byteCR As Byte()) As WeChatFriendModel
        Try
            ' Dim byteCR As Byte() = Encoding.UTF8.GetBytes(strContactRemark)
            Using ms As New MemoryStream()
                '将消息写入流中  
                ms.Write(byteCR, 0, byteCR.Length)
                '将流的位置归0  
                ms.Position = 0
                '使用工具反序列化对象  
                Dim result As WeChatFriendModel = ProtoBuf.Serializer.Deserialize(Of WeChatFriendModel)(ms)
                Return result
            End Using
        Catch ex As Exception
            Common.LogException("反序列化失败: " + ex.ToString(), "GetWeChatFriendModel")
            Return Nothing
        End Try
    End Function

    Private Function GetStrContent(ByVal strC As String, ByRef strRemark As String) As String
        Dim strRelust As String = ""

        strC = strC.Replace(" ", "").Replace(vbCr, "").Replace(vbLf, "").Replace(vbCrLf, "")

        'If strC.Contains("乖") Then
        '    Dim s As String = ""

        '    Dim strFile As String = "E:\新建文本文档.txt"
        '    Using writer As StreamWriter = New StreamWriter(strFile, True, Encoding.UTF8)
        '        writer.WriteLine(strC)
        '    End Using

        'End If

        Dim iStart As Integer = 0

        Dim strStart As String = strC.Substring(0, 1)
        If Me.LstIllegalASCII.Contains(strStart) Then
            '开始就是 不可见字符
            iStart = 1
        Else
            '开始没有 不可见字符
            iStart = 0
        End If

        Dim iEnd As Integer = 0
        For i As Integer = 1 To strC.Length - 1
            If iStart = 1 AndAlso strC.Length <= 1 Then
                Exit For
            End If
            Dim temp As String = strC.Substring(i, 1)
            If Me.LstIllegalASCII.Contains(temp) Then
                iEnd = i
                Exit For
            End If
        Next

        If iEnd = 0 AndAlso iStart = 0 Then
            strRelust = strC
        ElseIf iEnd = 0 AndAlso iStart > 0 Then
            strRelust = strC.Substring(iStart)
        Else
            strRelust = strC.Substring(iStart, iEnd)
        End If

        '获取备注名称
        For Each item As Char In LstIllegalASCII
            strC = strC.Replace(item, "⑪")
        Next

        Dim arrStr As String() = strC.Replace("*", "").Split("⑪") '.Replace("""", "⑪")
        Dim lstStr As New List(Of String)

        For Each item As String In arrStr
            If item.Length > 0 Then
                lstStr.Add(item)
            End If
        Next

        If lstStr.Count >= 7 Then
            strRemark = lstStr(2)
            If Not strRemark.Contains("""") Then
                strRemark = lstStr(1)
            End If
        End If

        If lstStr.Count = 6 Then
            strRemark = lstStr(1)
            If Not strRemark.Contains("""") Then
                strRemark = lstStr(2)
            End If
        End If

        If lstStr.Count = 5 Then
            strRemark = lstStr(1)
            If strRemark = """" Then
                strRemark = ""
            End If
        End If

        If Not strRemark.Contains("""") Then
            strRemark = ""
            Common.Log("WeChatRemark 未兼容：" & strC)
        Else
            strRemark = strRemark.Substring(0, strRemark.IndexOf(""""))
        End If

        If strRemark.Length = 0 Then
            strRemark = strRelust
        End If

        If strRelust.Length > 0 Then
            For Each item As Char In LstIllegalASCII
                strRelust = strRelust.Replace(item, "")
            Next
        End If

        If strRemark.Length > 0 Then
            For Each item As Char In LstIllegalASCII
                strRemark = strRemark.Replace(item, "")
            Next
        End If

        Return strRelust
    End Function

    Public Function Get_GroupFriend_Info(ByVal strUsrName As String) As List(Of WeChatFriendInfo)
        Dim lstInfos As New List(Of WeChatFriendInfo)
        Try
            Dim pstrSQL As String = String.Format("select *  from Friend where userName = '{0}'", strUsrName)
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                Dim info As New WeChatFriendInfo
                For Each dr As DataRow In dt.Rows
                    Dim dbContactChatRoom As String = ""
                    If Not IsDBNull(dr("dbContactChatRoom")) Then
                        dbContactChatRoom = Common.GetValue(Of String)(Encoding.UTF8.GetString(dr("dbContactChatRoom")), String.Empty)
                        For Each item As Char In LstIllegalASCII
                            dbContactChatRoom = dbContactChatRoom.Replace(item, "")
                        Next
                        dbContactChatRoom = dbContactChatRoom.Replace("�", "") '.Replace(":", "").Replace("(", "").Replace("""", "")

                        Dim intStart As Integer = dbContactChatRoom.IndexOf("<RoomData>")
                        Dim intEnd As Integer = dbContactChatRoom.LastIndexOf("</RoomData>")

                        dbContactChatRoom = dbContactChatRoom.Substring(intStart, intEnd - intStart + "</RoomData>".Length)

                        Dim objXmlDoc As XmlDocument = New XmlDocument()
                        objXmlDoc.LoadXml(dbContactChatRoom)
                        Dim elemList As XmlNodeList = objXmlDoc.GetElementsByTagName("Member")
                        For i As Integer = 0 To elemList.Count - 1
                            info = New WeChatFriendInfo
                            Dim strUserName As String = elemList(i).Attributes("UserName").Value
                            Dim strDisplayName As String = ""
                            If elemList(i).Item("DisplayName") IsNot Nothing Then
                                strDisplayName = elemList(i).Item("DisplayName").InnerText
                            End If
                            info.WeChatNum = strUserName
                            info.GroupNoteName = strDisplayName
                            If strUserName.Length > 0 AndAlso Not lstInfos.Contains(info) Then
                                lstInfos.Add(info)
                            End If
                        Next
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Get_GroupFriend_Info")
        End Try
        Return lstInfos
    End Function

#End Region

#Region "--- 获取订阅号列表 ---"

    '微信5.X，session数据库没有保存正在会话的订阅号，4.x中是包含的。
    Public Function Get_Brand_List() As List(Of String)
        Dim lstBrand As New List(Of String)
        Dim pstrSQL As String = "SELECT UsrName,ConStrRes2 FROM friend_ext WHERE NOT (ConStrRes2 is null) and ConStrRes2 <>'' and NOT (ConStrRes2 LIKE '%<BrandHomepage/>%') AND NOT (ConStrRes2 LIKE '%<BrandHomepage></BrandHomepage>%') ;"
        'Dim pstrSQL As String = "SELECT a.[UsrName] , a.[NickName] , b.[ConStrRes2] FROM friend a LEFT JOIN friend_ext b ON a.[UsrName] = b.[UsrName] WHERE b.[ConStrRes2] not LIKE '%<chatstat>0</chatstat>%'"
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim strUsrName As String = Common.GetValue(Of String)(dr("UsrName"), String.Empty)
                    'Dim strNickName As String = Common.GetValue(Of String)(dr("NickName"), String.Empty)
                    Dim strBrief As String = Common.GetValue(Of String)(dr("ConStrRes2"), String.Empty)
                    Dim strA As String = "<BrandHomepage>"
                    Dim strB As String = "</BrandHomepage>"

                    'If String.Compare(strNickName, "互联网新鲜事") = 0 Then
                    '    Stop
                    'End If

                    If strBrief.Contains(strA) AndAlso strBrief.Contains(strB) Then
                        Dim startIndex As Integer = strBrief.IndexOf(strA)
                        Dim endIndex As Integer = strBrief.IndexOf(strB)

                        If (endIndex - startIndex) >= 15 Then
                            lstBrand.Add(strUsrName)
                            'Debug.Print("订阅号：" & strNickName)
                        End If

                    End If
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Brand_List")
                End Try
            Next
        End If

        Return lstBrand
    End Function

#End Region

#Region "--- 获取聊天信息 ---"


    Public Function Get_Chat_Info_Exist(ByVal usrName As String, ByVal lTime As Long, Optional ByVal md5 As String = "") As Boolean

        Dim lstInfos As New List(Of WeChatChatInfo)
        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        If md5.Length > 0 Then
            strMD5 = md5
        End If
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select  * from {0} where CreateTime={1}", strTableName, lTime)
        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)

        Return lstInfos.Count > 0

    End Function

    '获取消息
    Public Function Get_Chat_Info(ByVal usrName As String) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select  * from {0} order by CreateTime", strTableName)

        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)

        Return lstInfos
    End Function

    Public Function Get_Chat_Info_ID(ByVal usrName As String, ByVal strID As String) As WeChatChatInfo
        Dim info As WeChatChatInfo = Nothing

        Dim strTableName As String = String.Format("Chat_{0}", Common.ToHexString(Common.GetMd5Array(usrName)))
        Dim pstrSQL As String = String.Format("select  * from {0} where MesLocalID='{1}'", strTableName, strID)

        Dim lstInfos As List(Of WeChatChatInfo) = Me.ExecuteSQL(usrName, pstrSQL)
        If lstInfos.Count > 0 Then
            info = lstInfos(0)
        End If
        Return info
    End Function

    '获取消息 时间条件
    Public Function Get_Chat_Info(ByVal usrName As String, ByVal lstTime As List(Of DateTime)) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)
        Try
            Dim strWhere As String = ""
            If lstTime IsNot Nothing AndAlso lstTime.Count = 2 Then
                Dim longStartTime As Long = WeixinHelper.ConvertPcTimeToWeixin(lstTime(0).[Date])
                Dim longEndTime As Long = WeixinHelper.ConvertPcTimeToWeixin(lstTime(1).AddDays(1).[Date])
                strWhere = String.Format(" and CreateTime >= {0} and createtime <={1} ", longStartTime, longEndTime)

            End If

            Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
            Dim strTableName As String = String.Format("Chat_{0}", strMD5)
            Dim pstrSQL As String = String.Format("select  * from {0} where 1=1 {1} order by CreateTime", strTableName, strWhere)

            lstInfos = Me.ExecuteSQL(usrName, pstrSQL)
        Catch
        End Try
        Return lstInfos
    End Function

    '获取消息（根据索引）
    Public Function Get_Chat_Info(ByVal usrName As String, ByVal startIndex As Integer, ByVal count As Integer, Optional ByVal strWhere As String = "") As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select * from ( select  * from {0} where 1=1 {3} order by CreateTime desc limit {1},{2}) a order by a.CreateTime", strTableName, startIndex, count, strWhere)
        'string pstrSQL = string.Format(@"select * from {0} order by CreateTime desc limit {0},11", strTableName, startIndex);
        'string pstrSQL = string.Format(@"select * from {0} order by CreateTime asc ", strTableName, startIndex);

        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)

        Return lstInfos
    End Function

    '获取消息（根据文本）
    Public Function Get_Chat_Info(ByVal usrName As String, ByVal strSearchText As String) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select  * from {0} where Type = 1 and Message like '%{1}%' order by CreateTime", strTableName, strSearchText)

        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)
        lstInfos = Me.FilterWebChatType(lstInfos)

        Return lstInfos
    End Function

    Private Function Get_Chat_CreateTime_ByID(ByVal usrName As String, ByVal endID As Integer) As Long
        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)

        '先查出时间，然后根据时间进行分页
        Dim pstrSQL As String = String.Format("select CreateTime from {0} where MesLocalID = {1} ", strTableName, endID)
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
        Dim longCreateTime As Long = 0
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    longCreateTime = Common.GetValue(Of Long)(dr("CreateTime"), 0)
                    Exit For
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "Get_Chat_Info_Before")
                End Try
            Next
        End If
        If longCreateTime = 0 Then
            Common.LogException("查询时间出错", "Get_Chat_CreateTime_ByID")
        End If
        Return longCreateTime
    End Function


    Public Function Get_Chat_Info_NearbyNews(ByVal usrName As String, ByVal endID As Integer, ByVal count As Integer, ByVal strWhere As String) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)
        Try
            Dim longCreateTime As Long = Me.Get_Chat_CreateTime_ByID(usrName, endID)

            If longCreateTime = 0 Then
                Common.LogException("查询时间出错", "Get_Chat_Info_Before")
                Return lstInfos
            End If

            Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
            Dim strTableName As String = String.Format("Chat_{0}", strMD5)

            Dim newCount As Integer = count / 2
            Dim pstrSQL As String = ""

            pstrSQL = String.Format("select * from (select  * from {0} where CreateTime <={1} {3} order by CreateTime desc limit 0,{2})a order by  a.CreateTime", strTableName, longCreateTime, newCount, strWhere)
            Dim lstTemp1 As List(Of WeChatChatInfo) = Me.ExecuteSQL(usrName, pstrSQL)

            pstrSQL = String.Format("select * from (select  * from {0} where CreateTime >{1} {3} order by CreateTime limit 0,{2})a order by  a.CreateTime", strTableName, longCreateTime, newCount, strWhere)
            Dim lstTemp2 As List(Of WeChatChatInfo) = Me.ExecuteSQL(usrName, pstrSQL)

            For index As Integer = lstTemp2.Count - 1 To 0 Step -1
                lstInfos.Add(lstTemp2(index))
            Next

            For index As Integer = lstTemp1.Count - 1 To 0 Step -1
                lstInfos.Add(lstTemp1(index))
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "Get_Chat_Info_NearbyNews")
        End Try

        Return lstInfos
    End Function

    '获取（大于ID）查看上文消息
    Public Function Get_Chat_Info_Before(ByVal usrName As String, ByVal endID As Integer, ByVal count As Integer, ByVal strWhere As String) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)
        Dim longCreateTime As Long = Me.Get_Chat_CreateTime_ByID(usrName, endID)

        If longCreateTime = 0 Then
            Common.LogException("查询时间出错", "Get_Chat_Info_Before")
            Return lstInfos
        End If

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select * from (select  * from {0} where CreateTime <{1} {3} order by CreateTime desc limit 0,{2})a order by  a.CreateTime", strTableName, longCreateTime, count, strWhere)
        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)
        Return lstInfos
    End Function

    '获取（小于ID）查看下文消息
    Public Function Get_Chat_Info_After(ByVal usrName As String, ByVal endID As Integer, ByVal count As Integer, ByVal strWhere As String) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)
        Dim longCreateTime As Long = Me.Get_Chat_CreateTime_ByID(usrName, endID)

        If longCreateTime = 0 Then
            Common.LogException("查询时间出错", "Get_Chat_Info_Before")
            Return lstInfos
        End If

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select * from (select  * from {0} where CreateTime >{1} {3} order by CreateTime limit 0,{2})a order by  a.CreateTime", strTableName, longCreateTime, count, strWhere)
        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)
        Return lstInfos
    End Function

    '获取（大于ID）查看下文消息
    Public Function Get_Chat_Info_After(ByVal usrName As String, ByVal localID As Integer) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select  * from {0} where MesLocalID >= {1} order by CreateTime", strTableName, localID)

        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)

        Return lstInfos
    End Function

    '获取消息（根据起始时间）
    Public Function Get_Chat_Info(ByVal usrName As String, ByVal startTime As DateTime, ByVal endTime As DateTime) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)
        Try
            Dim longStartTime As Long = WeixinHelper.ConvertPcTimeToWeixin(startTime.[Date])
            Dim longEndTime As Long = WeixinHelper.ConvertPcTimeToWeixin(endTime.AddDays(1).[Date])

            Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
            Dim strTableName As String = String.Format("Chat_{0}", strMD5)
            Dim pstrSQL As String = String.Format("select * from {0} where createtime >= {1} and createtime <={2} ", strTableName, longStartTime, longEndTime)

            lstInfos = Me.ExecuteSQL(usrName, pstrSQL)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Get_Chat_Info")
        End Try
        Return lstInfos
    End Function

    '获取消息（根据开始时间）
    Public Function Get_Chat_Info(ByVal usrName As String, ByVal startTime As DateTime) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)

        Dim longStartTime As Long = WeixinHelper.ConvertPcTimeToWeixin(startTime.[Date])

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select * from {0} where createtime >= {1} ", strTableName, longStartTime)

        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)

        Return lstInfos
    End Function

    '获取消息（根据时间和文本）
    Public Function Get_Chat_Info(ByVal usrName As String, ByVal startTime As DateTime, ByVal endTime As DateTime, ByVal strSearchText As String) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)

        Dim longStartTime As Long = WeixinHelper.ConvertPcTimeToWeixin(startTime.[Date])
        Dim longEndTime As Long = WeixinHelper.ConvertPcTimeToWeixin(endTime.AddDays(1).[Date])

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("select * from {0} where createtime >= {1} and createtime<{2} and Type = 1 and Message like '%{3}%'", strTableName, longStartTime, longEndTime, strSearchText)

        lstInfos = Me.ExecuteSQL(usrName, pstrSQL)
        lstInfos = Me.FilterWebChatType(lstInfos)

        Return lstInfos
    End Function

    Private Function ExecuteSQL(ByVal usrName As String, ByVal pstrSQL As String) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)
        Try
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Try
                        Dim info As New WeChatChatInfo()
                        info.StrUsrName = usrName
                        info.IntID = Common.GetValue(Of Integer)(dr("MesLocalID"), 0)
                        info.StrMessage = Common.GetValue(Of String)(dr("Message"), String.Empty)
                        info.IntStatus = Common.GetValue(Of Integer)(dr("Status"), 0)
                        info.ImgStatus = Common.GetValue(Of Integer)(dr("ImgStatus"), 0)
                        info.SCType = Me.OperateSpecialChatType(Common.GetValue(Of Integer)(dr("Type"), 0), usrName, info.StrMessage)
                        info.IntDes = Common.GetValue(Of Integer)(dr("Des"), 0)

                        Dim longCreateTime As Long = Common.GetValue(Of Long)(dr("CreateTime"), 0)
                        info.IntCreateTime = longCreateTime
                        info.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)

                        lstInfos.Add(info)
                    Catch ex As Exception
                        System.Diagnostics.Debug.WriteLine(ex.ToString())
                        Common.LogException(ex.ToString(), "ExecuteSQL")
                    End Try
                Next
            End If
        Catch
        End Try
        Return lstInfos
    End Function

    'public ChatInfo Get_Chat_Info_LastChatTime(string UsrNameMD5)
    '{
    '    DateTime lastChatTime = DateTime.MinValue;

    '    //string strMD5 = Common.ToHexString(Common.GetHashArray(UsrName));
    '    string strTableName = string.Format(@"Chat_{0}", UsrNameMD5);

    '    string pstrSQL = string.Format(@"select CreateTime from {0} order by CreateTime desc limit 0,1 ", strTableName);

    '    DataTable dt = SQLiteClass.ExecuteSQL(pstrSQL, mConn);

    '    if (dt != null && dt.Rows.Count > 0)
    '    {
    '        foreach (DataRow dr in dt.Rows)
    '        {
    '            try
    '            {
    '                long longCreateTime = Common.GetValue<long>(dr["CreateTime"], 0);
    '                lastChatTime = this.ConverTime(longCreateTime);
    '            }
    '            catch (Exception ex)
    '            {
    '                System.Diagnostics.Debug.WriteLine(ex.ToString());
    '            }
    '        }
    '    }

    '    return lastChatTime;
    '}

#End Region

#Region "--- 修改聊天信息 ---"

    '更新数据库
    Public Function Update_Chat_Info(ByVal usrName As String, ByVal strOldText As String, ByVal strNewText As String) As Boolean
        Dim result As Boolean = False

        Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
        Dim strTableName As String = String.Format("Chat_{0}", strMD5)
        Dim pstrSQL As String = String.Format("update {0} set Message ='{1}' where Message like '%{2}%'", strTableName, strNewText, strOldText)

        Dim intUpdate As Integer = SQLiteClass.ExecuteNoneQuery(pstrSQL, mConn)

        If intUpdate > 0 Then
            result = True
        End If

        Return result
    End Function

    '更新数据库
    Public Function Update_Chat_Info_ByTableName(ByVal tableName As String, ByVal strOldText As String, ByVal strNewText As String) As Boolean
        Dim result As Boolean = False

        Dim pstrSQL As String = String.Format("update {0} set Message ='{1}' where Message like '%{2}%'", tableName, strNewText, strOldText)

        Dim intUpdate As Integer = SQLiteClass.ExecuteNoneQuery(pstrSQL, mConn)

        If intUpdate > 0 Then
            result = True
        End If

        Return result
    End Function

#End Region

#Region "--- 获取聊天的所有表 ---"

    Public Function Get_Chat_Table() As List(Of String)
        Dim lstTables As New List(Of String)()

        'Dim pstrSQL As String = "Select * From sqlite_master where type='table' and tbl_name like 'Chat_%'"
        Dim pstrSQL As String = "Select * From sqlite_master where type='table' and substr(tbl_name,1,5)='Chat_'"
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim strTableName As String = Common.GetValue(Of String)(dr("tbl_name"), String.Empty)

                    If Not String.IsNullOrEmpty(strTableName) Then
                        lstTables.Add(strTableName)
                    End If
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Chat_Table")
                End Try
            Next
        End If

        Return lstTables
    End Function

#End Region

#Region "--- 删除会话 ---"

    Public Function Delete_Chat_Table(ByVal fInfo As WeChatFriendInfo) As Boolean
        Dim blnResult As Boolean = False

        Dim sb As New StringBuilder()

        Dim strSQLLike As String = String.Format("Select type,name From sqlite_master where name like '%{0}%'", fInfo.Md5)
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(strSQLLike, mConn)
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim strName As String = Common.GetValue(Of String)(dr("name"), String.Empty)
                    Dim strType As String = Common.GetValue(Of String)(dr("type"), String.Empty)

                    Select Case strType
                        Case "index"
                            sb.AppendLine(String.Format("Drop Index IF Exists [{0}];", strName))

                        Case "table"
                            sb.AppendLine(String.Format("Drop Table IF Exists [{0}];", strName))

                    End Select

                Catch ex As Exception
                    Common.LogException(ex.ToString(), "Delete_Chat_Table")
                End Try
            Next
        End If

        'Drop Table IF Exists [Chat_c196266f837d14e0b693f961bee37b66];
        'Drop Table IF Exists [ChatExt2_c196266f837d14e0b693f961bee37b66];
        'Drop Index IF Exists [Chat_c196266f837d14e0b693f961bee37b66_Index];
        'Drop Index IF Exists [Chat_c196266f837d14e0b693f961bee37b66_Index2];
        'Drop Index IF Exists [Chat_c196266f837d14e0b693f961bee37b66_Index3];
        'Delete From [Friend] where UsrName='weixin';
        'Delete From [Friend_Ext] where UsrName='weixin';

        sb.AppendLine("Delete From [Friend] where UsrName='{0}';")
        sb.AppendLine("Delete From [Friend_Ext] where UsrName='{0}';")

        If SQLiteClass.CheckTableExist(mConn, "friend_meta") Then
            sb.AppendLine("Delete From [friend_meta] where username='{0}';")
        End If

        Dim pstrSQL As String = String.Format(sb.ToString(), fInfo.UsrName)

        Debug.Print(pstrSQL)

        Dim errMsg As String = String.Empty
        Dim intUpdate As Integer = SQLiteClass.ExecuteNoneQuery(pstrSQL, mConn, Nothing, Nothing, errMsg)
        If String.IsNullOrEmpty(errMsg) Then
            blnResult = True
        End If

        Return blnResult
    End Function

#End Region

#Region "--- 私有方法 ---"

    Private Function GetNoteName(ByVal text As String) As String
        Dim strNoteName As String = String.Empty

        If String.IsNullOrEmpty(text) Then
            Return strNoteName
        End If

        If text.Contains("<1>") AndAlso text.Contains("</1>") Then
            Dim startIndex As Integer = text.IndexOf("<1>")
            Dim lastIndex As Integer = text.IndexOf("</1>")
            strNoteName = text.Substring(startIndex + 3, lastIndex - startIndex - 3)
        End If
        Return strNoteName
    End Function

    '某些微信默认用户，数据库中存储的网页类型不对
    Private Function OperateSpecialChatType(ByVal strType As String, ByVal strUsrName As String, ByVal strMsg As String) As String
        Dim result As String = strType

        If String.Compare(strUsrName, "newsapp") = 0 OrElse _
           String.Compare(strUsrName, "blogapp") = 0 Then

            '（纪龙龙）判断是网页的方法，需要优化。有些网页的开始与结尾是<msg></msg>
            If strMsg.StartsWith("<mmreader>") AndAlso strMsg.EndsWith("</mmreader>") Then
                result = "49"
            Else
                'Debug.Print("不是网页：" & strMsg)
            End If

        End If

        Return result
    End Function

    '过滤网页类型的聊天记录
    Private Function FilterWebChatType(ByVal lstInfos As List(Of WeChatChatInfo)) As List(Of WeChatChatInfo)
        Dim lst As New List(Of WeChatChatInfo)

        For Each Item As WeChatChatInfo In lstInfos
            If Item.SCType = SociaChatType.webpage Then ' "49"
                Continue For
            End If

            lst.Add(Item)
        Next

        Return lst
    End Function

#End Region

#Region "--- 取得表情内容 ---"

    Public Function Get_Emoticons_Info(ByVal strMd5 As String) As Dictionary(Of String, Emoticon)
        Dim dicInfos As New Dictionary(Of String, Emoticon)()
        'EmoticonPackageRemoveTime=0 and
        Dim pstrSQL As String = "select *  from EmoticonPackage05 where EmoticonPackageInstallTime>100 and EmoticonPackageDLStatus=1"
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
        dicInfos.Add("custom_emoticon_pid", Me.GetCustomerEmoticon(strMd5))
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim info As New Emoticon(strMd5)
                    info.EmoticonPackageId = Common.GetValue(Of String)(dr("EmoticonPackageId"), String.Empty)
                    info.EmoticonPackageName = Common.GetValue(Of String)(dr("EmoticonPackageName"), String.Empty)
                    'info.EmoticonPackagePStatus = Common.GetValue(Of String)(dr("EmoticonPackagePStatus"), String.Empty)
                    'info.EmoticonPackageDLStatus = Common.GetValue(Of String)(dr("EmoticonPackageDLStatus"), String.Empty)
                    info.EmoticonPackageInstallTime = Common.GetValue(Of Integer)(dr("EmoticonPackageInstallTime"), 0)
                    info.EmoticonPackageRemoveTime = Common.GetValue(Of Integer)(dr("EmoticonPackageRemoveTime"), 0)
                    If info.EmoticonPackageRemoveTime > 0 AndAlso info.EmoticonPackageRemoveTime > info.EmoticonPackageInstallTime Then
                        Continue For
                    End If

                    dicInfos(info.EmoticonPackageId) = info
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Emoticons_Info")
                End Try
            Next
        End If

        Me.Get_EmoticonDetail_Info(dicInfos)

        Return dicInfos
    End Function

    Private Function GetCustomerEmoticon(ByVal strMd5 As String) As Emoticon
        Dim emReturn As New Emoticon(strMd5, "custom_emoticon_pid")
        emReturn.EmoticonPackageName = "我的收藏"
        Return emReturn
    End Function

    Private Sub Get_EmoticonDetail_Info(ByRef dicInfos As Dictionary(Of String, Emoticon))
        Dim pstrSQL As String = "select *  from Emoticon1 "
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim strId As String = Common.GetValue(Of String)(dr("ConStrRes2"), String.Empty)
                    If dicInfos.ContainsKey(strId) Then
                        Dim item As New EmoticonItem
                        item.ConStrRes2 = strId
                        item.MD5 = Common.GetValue(Of String)(dr("MD5"), String.Empty)
                        item.Type = Common.GetValue(Of Integer)(dr("Type"), 1)
                        dicInfos(strId).Items.Add(item)
                    ElseIf strId.Trim.Length = 0 Then
                        Dim item As New EmoticonItem
                        item.ConStrRes2 = strId
                        item.MD5 = Common.GetValue(Of String)(dr("MD5"), String.Empty)
                        item.Type = Common.GetValue(Of Integer)(dr("Type"), 1)
                        dicInfos("custom_emoticon_pid").Items.Add(item)
                    End If
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_EmoticonDetail_Info")
                End Try
            Next
        End If
    End Sub

    Public Function Check_EmoticonDetail_Info(ByVal info As EmoticonItem) As Boolean
        Dim blnCanDelete As Boolean = True
        Dim pstrSQL As String = String.Format("select *  from Emoticon1 where MD5='{0}'", info.MD5)
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
        If dt IsNot Nothing AndAlso dt.Rows.Count > 1 Then
            blnCanDelete = False
        End If
        Return blnCanDelete
    End Function

#End Region

#Region "--- 更新表情内容 ---"

    Public Function GetSQL_InsertEmoticon(ByVal group As Emoticon) As String
        Dim pstrSQL As String = ""

        Try
            Dim strCols As String = ""
            Dim strValues As String = ""

            strCols &= "EmoticonPackageId,"
            strValues &= String.Format("'{0}',", group.EmoticonPackageId)

            strCols &= "EmoticonPackageName,"
            strValues &= String.Format("'{0}',", group.EmoticonPackageName)

            strCols &= "EmoticonPackageIconUrl,"
            strValues &= String.Format("'{0}',", group.EmoticonPackageIconUrl)

            strCols &= "EmoticonPackagePannelUrl,"
            strValues &= String.Format("'{0}',", group.EmoticonPackagePannelUrl)

            strCols &= "EmoticonPackagePStatus,"
            strValues &= String.Format("{0},", group.EmoticonPackagePStatus)

            strCols &= "EmoticonPackageDLStatus,"
            strValues &= String.Format("{0},", group.EmoticonPackageDLStatus)

            strCols &= "EmoticonPackageInstallTime,"
            strValues &= String.Format("{0},", group.EmoticonPackageInstallTime)

            strCols &= "EmoticonPackageRemoveTime"
            strValues &= String.Format("{0}", group.EmoticonPackageRemoveTime)

            pstrSQL = String.Format("insert into emoticonpackage05({0}) values ({1});", strCols, strValues)

        Catch ex As Exception
        End Try

        Return pstrSQL
    End Function

    Public Function GetSQL_UpdateEmoticon(ByVal group As Emoticon) As String
        Return String.Format("update emoticonpackage05 set EmoticonPackageName=""{0}"" where EmoticonPackageId=""{1}"";", group.EmoticonPackageName, group.EmoticonPackageId)
    End Function

    Public Function GetSQL_DeleteEmoticon(ByVal group As Emoticon) As String
        Dim pstrSQL As String = ""

        pstrSQL &= String.Format("delete from emoticonpackage05  where EmoticonPackageId='{0}';", group.EmoticonPackageId) & vbCrLf
        pstrSQL &= String.Format("delete from Emoticon1  where ConIntRes2='{0}';", group.EmoticonPackageId)

        Return pstrSQL
    End Function

    Public Function GetSQL_InsertEmoticonItem(ByVal item As EmoticonItem, ByVal index As Integer) As String
        Dim pstrSQL As String = ""
        If item.EmoticonPackageId = "custom_emoticon_pid" Then
            '收藏夹的id要为空不然会导入不进去。
            pstrSQL &= String.Format("delete from Emoticon1  where MD5='{0}' and ConStrRes2='{1}';", item.MD5, "") & vbCrLf
            pstrSQL &= String.Format("insert into Emoticon1(MD5,Type,CreateTime,ConStrRes2) values ('{0}',{1},{2},'{3}');", item.MD5, item.Type, 0, "")
        Else
            '先删除再添加
            pstrSQL &= String.Format("delete from Emoticon1  where MD5='{0}' and ConStrRes2='{1}';", item.MD5, item.EmoticonPackageId) & vbCrLf
            pstrSQL &= String.Format("insert into Emoticon1(MD5,Type,CreateTime,ConStrRes2) values ('{0}',{1},{2},'{3}');", item.MD5, item.Type, 0, item.EmoticonPackageId)
        End If

        Return pstrSQL
    End Function

    Public Function GetSQL_DeleteEmoticonItem(ByVal item As EmoticonItem) As String
        Dim id As String = item.ConStrRes2
        If String.IsNullOrEmpty(id) Then
            id = item.EmoticonPackageId
        End If
        Dim strReturn As String = String.Format("delete from Emoticon1  where MD5='{0}' and ConStrRes2='{1}';", item.MD5, id)

        '如果id 为空的时候 where ConStrRes2='' 没办法删除记录。   custom_emoticon_pid收藏夹删除
        If String.IsNullOrEmpty(id) OrElse item.EmoticonPackageId = "custom_emoticon_pid" Then
            strReturn &= String.Format("delete from Emoticon1  where MD5='{0}' and ConStrRes2 is null;", item.MD5)
        End If

        Return strReturn
    End Function

    Public Function Update_EmoticonDB(ByVal pstrSQL As String) As String
        Dim errMsg As String = String.Empty

        Dim count As Integer = SQLiteClass.ExecuteNoneQuery(pstrSQL, mConn, Nothing, Nothing, errMsg)

        Return errMsg
    End Function

#End Region

#Region "--- 微信备份 ---"

    'Public Function BackUpWeixin(ByVal device As iPhoneDevice, ByVal strPath As String, ByVal talkHandler As TalkingStatusEventHandler, ByVal transferHandler As DataTransferEventHandler) As Boolean
    '    Dim isArchiveSucceed As Boolean = True
    '    Dim type As kArchiveType = kArchiveType.All

    '    Dim strFolderPath As String = device.BackupAppRecord(Me.mstrSKU, "", transferHandler)
    '    Dim strzipPath As String = strPath.Replace(".ipa", ".zip")
    '    If strFolderPath.Length > 0 Then
    '        Utility.PackFiles(strzipPath, strFolderPath)
    '    End If
    '    If File.Exists(strzipPath) Then
    '        isArchiveSucceed = True
    '        talkHandler(New TalkingStatusEventArgs(kApplicationStatus.None, 100, ""))
    '    Else
    '        isArchiveSucceed = False
    '        talkHandler(New TalkingStatusEventArgs(kApplicationStatus.Error, 100, ""))
    '    End If
    '    Try
    '        '完成后删除缓存里的文件。
    '        Directory.Delete(strFolderPath, True)
    '    Catch ex As Exception
    '    End Try
    '    Return isArchiveSucceed
    'End Function

    Public Function BackUpWeixin(ByVal device As iPhoneDevice, ByVal strPath As String, ByVal talkHandler As TalkingStatusEventHandler, ByVal transferHandler As DataTransferEventHandler) As Boolean
        Dim isArchiveSucceed As Boolean = True
        Dim strContainerPath As String = Path.Combine(Folder.BackupAppRecordFolder, device.SerialNumber)
        strContainerPath = Path.Combine(strContainerPath, String.Format("{0}_{1}", Me.mstrSKU, DateTime.Now.ToString("yyMMddHH")))

        '备份记录。
        Dim blnSucceed As Boolean = device.BackupAppRecord(strContainerPath, Me.mstrSKU, "", transferHandler)

        If Not blnSucceed OrElse Not Directory.Exists(strContainerPath) Then
            Dim args As New TalkingStatusEventArgs(kApplicationStatus.Error, 1, 1)
            talkHandler(args)
            Return False
        End If

        talkHandler(New TalkingStatusEventArgs(kApplicationStatus.ArchivingApplication, 100, ""))
        '把记录压缩成zip包
        Dim strFilePathZip As String = strContainerPath & ".zip"
        Utility.PackFiles(strFilePathZip, strContainerPath)

        If File.Exists(strFilePathZip) Then
            Try
                File.Move(strFilePathZip, strPath)
            Catch ex As Exception
            End Try
        End If

        If Not File.Exists(strPath) Then
            talkHandler(New TalkingStatusEventArgs(kApplicationStatus.Error, 100, ""))
            isArchiveSucceed = False
        Else
            talkHandler(New TalkingStatusEventArgs(kApplicationStatus.None, 100, ""))
        End If

        'Dim type As kArchiveType = kArchiveType.All
        'Dim strPathTemp As String = Path.Combine(Folder.TempFolder, String.Format("{0}.ipa", Guid.NewGuid.ToString("N")))
        'isArchiveSucceed = device.ArchiveApplication(Me.mstrSKU, type, False, strPathTemp, talkHandler, transferHandler)

        'If isArchiveSucceed Then
        '    isArchiveSucceed = Me.BackiOS8Document(device, strPathTemp, type, talkHandler)
        'End If

        'If isArchiveSucceed Then
        '    Try
        '        File.Move(strPathTemp, strPath)
        '    Catch ex As Exception
        '        Common.LogException(ex.ToString, "WeixinHelper_BackUpWeixin")
        '    End Try
        '    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.None, 100, ""))
        'Else
        '    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.Error, 100, ""))
        'End If
        Return isArchiveSucceed
    End Function

    Private Function BackiOS8Document(ByVal device As iPhoneDevice, ByVal strPathOnPC As String, ByVal type As kArchiveType, ByVal talkHandler As TalkingStatusEventHandler)
        Dim blnReturn As Boolean = True
        Try
            If type = kArchiveType.All AndAlso device.VersionNumber >= 800 Then
                Dim strPathTemp As String = Path.Combine(Folder.TempFolder, Guid.NewGuid.ToString)
                Dim strPathTempContainer As String = Path.Combine(strPathTemp, "Container")
                Dim Afchandler As System.IntPtr = IntPtr.Zero
                Afchandler = device.GetAfcByFileSharing(Me.mstrSKU, HouseArrestType.Document, True)
                Folder.CheckFolder(strPathTemp)
                Folder.CheckFolder(strPathTempContainer)

                If talkHandler IsNot Nothing Then
                    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.ExtractingPackage, 3, ""))
                End If

                blnReturn = device.DownFromPhone(Afchandler, "/Documents", Path.Combine(strPathTempContainer, "Documents"))
                If Not blnReturn Then
                    Return blnReturn
                End If
                If talkHandler IsNot Nothing Then
                    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.ExtractingPackage, 10, ""))
                End If

                blnReturn = device.DownFromPhone(Afchandler, "/Library", Path.Combine(strPathTempContainer, "Library"))
                If Not blnReturn Then
                    Return blnReturn
                End If
                If talkHandler IsNot Nothing Then
                    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.ExtractingPackage, 30, ""))
                End If

                blnReturn = device.DownFromPhone(Afchandler, "/tmp", Path.Combine(strPathTempContainer, "tmp"))
                If Not blnReturn Then
                    Return blnReturn
                End If
                If talkHandler IsNot Nothing Then
                    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.ExtractingPackage, 50, ""))
                End If

                '解压ipa包后再重新打包。
                Utility.unzip(strPathOnPC, strPathTemp)

                If talkHandler IsNot Nothing Then
                    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.ExtractingPackage, 80, ""))
                End If

                blnReturn = Utility.PackFiles(strPathOnPC, strPathTemp)

                If talkHandler IsNot Nothing Then
                    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.ExtractingPackage, 90, ""))
                End If

                If blnReturn Then
                    device.SaveAuthorizeIPAFromArchive(strPathOnPC)
                End If

                If talkHandler IsNot Nothing Then
                    talkHandler(New TalkingStatusEventArgs(kApplicationStatus.ExtractingPackage, 100, ""))
                End If
            End If
        Catch ex As Exception
            blnReturn = False
            Common.LogException(ex.ToString, "frmDeviceApp_BackiOS8Document")
        End Try
        Return blnReturn
    End Function

#End Region

#Region "--- 微信备份EmoticonPackage05 ---"

    Private Sub CheckEmoticonPackage05()
        Dim pstrSQL As String = "select *  from sqlite_master where tbl_name='EmoticonPackage05'"
        Try
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
            If dt.Rows.Count <= 0 Then
                pstrSQL = "CREATE TABLE EmoticonPackage05 " & vbCrLf & _
                            "( " & vbCrLf & _
                            "EmoticonPackageId TEXT NOT NULL PRIMARY KEY UNIQUE ON CONFLICT REPLACE ," & vbCrLf & _
                            "EmoticonPackageName TEXT ," & vbCrLf & _
                            "EmoticonPackageIconUrl TEXT , " & vbCrLf & _
                            "EmoticonPackagePannelUrl TEXT , " & vbCrLf & _
                            "EmoticonPackagePStatus INTEGER DEFAULT 0 , " & vbCrLf & _
                            "EmoticonPackageDLStatus INTEGER DEFAULT 0 , " & vbCrLf & _
                            "EmoticonPackageInstallTime INTEGER DEFAULT 0 , " & vbCrLf & _
                            "EmoticonPackageRemoveTime INTEGER DEFAULT 0 , " & vbCrLf & _
                            "ConIntRes1 INTEGER DEFAULT 0 , " & vbCrLf & _
                            "ConIntRes2 INTEGER DEFAULT 0 , " & vbCrLf & _
                            "ConIntRes3 INTEGER DEFAULT 0 , " & vbCrLf & _
                            "ConStrRes1 TEXT , " & vbCrLf & _
                            "ConStrRes2 TEXT , " & vbCrLf & _
                            "ConStrRes3 TEXT" & vbCrLf & _
                            ") ;"
                SQLiteClass.ExecuteSQL(pstrSQL, mConn)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString() & pstrSQL, "CheckEmoticonPackage05")
        End Try

        Try
            pstrSQL = "select *  from sqlite_master where tbl_name='Emoticon1'"
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
            If dt.Rows.Count <= 0 Then
                pstrSQL = "Create  TABLE Emoticon1(" & vbCrLf & _
                            "[MD5] TEXT" & vbCrLf & _
                            ",[Message] TEXT" & vbCrLf & _
                            ",[Type] INTEGER DEFAULT 0" & vbCrLf & _
                            ",[CreateTime] INTEGER DEFAULT 0" & vbCrLf & _
                            ",[Len] INTEGER DEFAULT 0" & vbCrLf & _
                            ",[Status] INTEGER DEFAULT 0" & vbCrLf & _
                            ",[Catalog] TEXT" & vbCrLf & _
                            ",[CatelogID] INTEGER DEFAULT 0" & vbCrLf & _
                            ",[Draft] TEXT" & vbCrLf & _
                            ",[ConIntRes1] INTEGER DEFAULT 0" & vbCrLf & _
                            ",[ConIntRes2] INTEGER DEFAULT 0" & vbCrLf & _
                            ",[ConIntRes3] INTEGER DEFAULT 0" & vbCrLf & _
                            ",[ConStrRes1] TEXT" & vbCrLf & _
                            ",[ConStrRes2] TEXT" & vbCrLf & _
                            ",[ConStrRes3] TEXT" & vbCrLf & _
                            "); CREATE INDEX Emoticon1_Index  ON Emoticon1(MD5);"

                SQLiteClass.ExecuteSQL(pstrSQL, mConn)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString() & pstrSQL, "CheckEmoticonPackage05")
        End Try
    End Sub

#End Region

#Region "--- 恢复找回的删除数据到手机 ---"

    Public Function CheckChatInDB(ByVal strName As String) As Boolean
        Dim isRelust As Boolean = False
        Try
            Dim strSql As String = String.Format(" Select * From sqlite_master where type='table' and tbl_name='Chat_{0}' ", strName)
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(strSql, mConn)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                isRelust = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckChatInDB")
        End Try
        Return isRelust
    End Function

    Public Function CreateChatTable(ByVal strName As String)
        Dim isRelust As Boolean = False
        Try
            Dim sbSql As StringBuilder = New StringBuilder()
            sbSql.AppendLine(String.Format(" CREATE TABLE Chat_{0}(TableVer integer default 1, MesLocalID integer primary key autoincrement, MesSvrID bigint default 0, CreateTime integer default 0, Message text, Status integer default 0, ImgStatus integer default 0, Type integer, Des integer);", strName))
            sbSql.AppendLine(String.Format("CREATE INDEX Chat_{0}_Index on Chat_{0}(MesSvrID);", strName))
            sbSql.AppendLine(String.Format("CREATE INDEX Chat_{0}_Index2 on Chat_{0}(CreateTime);", strName))
            sbSql.AppendLine(String.Format("CREATE INDEX Chat_{0}_Index3 on Chat_{0}(Status);", strName))
            Dim intUpdate As Integer = SQLiteClass.ExecuteNoneQuery(sbSql.ToString(), mConn)
            If intUpdate > 0 Then
                isRelust = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateChatTable")
        End Try
        Return isRelust
    End Function

    Public Function InsertSession(ByVal strUserName As String, ByVal IntData As Integer, ByVal intUnReadCount As Integer)
        Dim isRelust As Boolean = False
        Try
            Dim strSql As String = String.Format(" INSERT into SessionAbstract(usrname,createtime,unreadcount) values ('{0}',{1},{2}) ", strUserName, IntData, intUnReadCount)
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(strSql, mConn)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                isRelust = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InsertSession")
        End Try
        Return isRelust
    End Function

    Public Function UpdateSessionUnReadCount(ByVal strUserName As String, ByVal intUnReadCount As Integer) As Integer
        Dim iResult As Integer = 0
        Try
            Dim strSql As String = String.Format("update SessionAbstract set unreadcount={0} where UsrName='{1}'", intUnReadCount, strUserName)
            iResult = SQLiteClass.ExecuteNoneQuery(strSql, Me.mConn)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UpdateSessionUnReadCount")
        End Try
        Return iResult
    End Function
    Public Function InsertChatInfo(ByVal lstChatInfo As List(Of WeChatChatInfo), ByVal strName As String) As Boolean
        Dim isRelust As Boolean = False
        Try
            Dim intIndex As Integer = 1
            Dim lastChatInfo As WeChatChatInfo = Get_Last_Chat_Info(strName)
            If lastChatInfo IsNot Nothing Then
                intIndex = lastChatInfo.IntID + 1
            End If
            Dim intCount As Integer = 0
            'Dim intIndex As Integer = 1
            Dim strSql As String = "insert into Chat_{0}(TableVer,MesLocalID,MesSvrID,CreateTime,Message,Status,ImgStatus,Type,Des)values(1,{1},{2},{3},'{4}',{5},1,{6},{7});"
            Dim sbSql As New StringBuilder()
            For Each info As WeChatChatInfo In lstChatInfo
                sbSql.AppendLine(String.Format(strSql, strName, intIndex, intIndex, info.IntCreateTime, info.StrMessage, info.IntStatus, info.SCType, info.IntDes))
                intIndex = intIndex + 1
                intCount = intCount + 1
                If intCount > 200 Then
                    isRelust = SQLiteClass.ExecuteNoneQuery(sbSql.ToString(), Me.mConn)
                    sbSql.Remove(0, sbSql.Length)
                    intCount = 0
                End If
            Next
            isRelust = SQLiteClass.ExecuteNoneQuery(sbSql.ToString(), Me.mConn)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "InsertChatInfo")
        End Try
        Return isRelust
    End Function

    Public Function ClearChatInfo(ByVal strName As String) As Integer
        Dim iResult As Integer = 0
        Try
            Dim strSql As String = String.Format("delete from Chat_{0}", strName)
            iResult = SQLiteClass.ExecuteNoneQuery(strSql, Me.mConn)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ClearChatInfo")
        End Try
        Return iResult
    End Function

    Public Function Get_Last_Chat_Info(ByVal usrName As String) As WeChatChatInfo
        Dim chatInfo As WeChatChatInfo = Nothing
        Try
            'Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
            Dim strTableName As String = String.Format("Chat_{0}", usrName)
            Dim pstrSQL As String = String.Format("select * from {0} order by MesLocalID desc limit 0,1", strTableName)

            Dim lstInfos As List(Of WeChatChatInfo) = Me.ExecuteSQL(usrName, pstrSQL)
            If lstInfos IsNot Nothing AndAlso lstInfos.Count > 0 Then
                chatInfo = lstInfos(0)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Get_Last_Chat_Info")
        End Try
        Return chatInfo
    End Function
#End Region

    Function Search_Chat(ByVal dictAll As Dictionary(Of String, WeChatFriendInfo), ByVal strSearchText As String, ByVal lstDT As List(Of Long)) As Dictionary(Of String, WeChatFriendInfo)
        Dim dictReulst As New Dictionary(Of String, WeChatFriendInfo)
        Try
            Dim list As New List(Of WeChatFriendInfo)
            For Each item As WeChatFriendInfo In dictAll.Values
                If item.DTLastSendTime = DateTime.MinValue Then
                    Continue For
                End If
                If item.Md5.StartsWith(WeixinImportOrExportHelper.mStrUnknown) OrElse item Is Nothing Then 'OrElse item.DTLastSendTime = DateTime.MinValue
                    Continue For
                End If
                list.Add(item)
                '300个对话查一次
                If list.Count >= 300 Then
                    Me.SearchFriendChatExecuteSQL(list, strSearchText, lstDT, dictReulst)
                    list.Clear()
                End If
            Next

            If list.Count > 0 Then
                Me.SearchFriendChatExecuteSQL(list, strSearchText, lstDT, dictReulst)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "Search_Chat")
        End Try
        Return dictReulst
    End Function

    Private Sub SearchFriendChatExecuteSQL(ByVal list As List(Of WeChatFriendInfo), strSearchText As String, lstDT As List(Of Long), ByRef dictReulst As Dictionary(Of String, WeChatFriendInfo))
        Try
            Dim strSql As String = Me.SearchFriendChatSQL(list, strSearchText, lstDT)
            If strSql.Trim.Length <= 0 Then
                Return
            End If
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(strSql, mConn)
            Dim infoTemp As WeChatFriendInfo = Nothing
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Dim strKey As String = Common.GetValue(Of String)(dr("uin"), String.Empty)
                    If strKey.Length <= 0 Then
                        Continue For
                    End If
                    mStrKeyEndsWithSaurus = strKey
                    infoTemp = list.Find(AddressOf EndsWithSaurus)
                    'infoTemp = list.Find(Function(wcInfo As WeChatFriendInfo) wcInfo.Md5 = strKey)
                    If infoTemp IsNot Nothing Then
                        dictReulst.Add(infoTemp.UsrName, infoTemp)
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SearchFriendChatExecuteSQL——WeChat")
        End Try
    End Sub

    Private Shared mStrKeyEndsWithSaurus As String = ""
    Private Shared Function EndsWithSaurus(ByVal wcInfo As WeChatFriendInfo) As Boolean

        If wcInfo.Md5 = mStrKeyEndsWithSaurus Then
            Return True
        Else
            Return False
        End If
    End Function

    Private Function SearchFriendChatSQL(list As List(Of WeChatFriendInfo), ByVal strSearchText As String, lstDT As List(Of Long)) As String
        Dim strSql As String = ""
        Dim strTableName As String = ""
        Dim strWhere As String = ""
        If lstDT.Count = 2 Then
            strWhere = String.Format(" and Message like'%{0}%' and CreateTime >={1} and CreateTime <={2} ", strSearchText, lstDT(0), lstDT(1))
        Else
            strWhere = String.Format(" and Message like'%{0}%' ", strSearchText)
        End If

        For Each item As WeChatFriendInfo In list
            If item.DTLastSendTime = DateTime.MinValue Then
                Continue For
            End If
            Dim pstrSQL As String = ""
            strTableName = String.Format("Chat_{0}", item.Md5)
            pstrSQL = String.Format("select DISTINCT '{2}' as uin from {0} where 1=1 {1} ", strTableName, strWhere, item.Md5)
            If strSql.Length > 0 Then
                strSql = strSql & vbCrLf & "union" & vbCrLf & pstrSQL
            Else
                strSql = pstrSQL
            End If
        Next
        Return strSql
    End Function

End Class

'微信数据库（联系人的最近聊天时间）
Public Class WeixinSessionDB
    Private ReadOnly mDBFile As String = String.Empty
    Private mConn As SQLiteConnection = Nothing
    Private mConn3 As System.Data.SQLite3.SQLiteConnection = Nothing

#Region "--- 单实例 ---"

    Private Shared instance As WeixinSessionDB = Nothing
    Private Shared ReadOnly locker As New Object()

    Public Shared Function GetInstance(ByVal DBFile As String) As WeixinSessionDB
        If instance IsNot Nothing Then
            If String.Compare(DBFile, instance.mDBFile, True) <> 0 Then
                instance.Dispose()
                instance = Nothing
            End If
        End If

        If instance Is Nothing Then
            SyncLock locker
                If instance Is Nothing Then
                    instance = New WeixinSessionDB(DBFile)
                End If
            End SyncLock
        End If

        Return instance
    End Function

#End Region

#Region "--- 构造函数 ---"

    Private Sub New(ByVal DBFile As String)
        mDBFile = DBFile
        Reload()
    End Sub

    Public Sub Dispose()
        Try
            Me.CloseDB()

            instance = Nothing
        Catch
        End Try
    End Sub

    Public ReadOnly Property DbFile() As String
        Get
            Return Me.mDBFile
        End Get
    End Property

#End Region

#Region "--- 加载数据库 ---"

    Public Function Reload() As Boolean
        Dim result As Boolean = False
        Try
            If mConn IsNot Nothing AndAlso mConn.State = System.Data.ConnectionState.Open Then
                mConn.Close()
            End If

            If Not File.Exists(mDBFile & "-shm") Then
                mConn = SQLiteClass.CreateConnectionFromFile(mDBFile)
            End If

            If mConn Is Nothing Then
                If mConn3 IsNot Nothing AndAlso mConn3.State = System.Data.ConnectionState.Open Then
                    mConn3.Close()
                End If

                mConn3 = SQLiteClass3.CreateConnectionFromFile(mDBFile)
            End If

            result = True
        Catch
        End Try

        Return result
    End Function

    Public Function CloseDB() As Boolean
        Dim result As Boolean = False
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If

            If Me.mConn3 IsNot Nothing AndAlso Me.mConn3.State = ConnectionState.Open Then
                Me.mConn3.Close()
            End If

            result = True
        Catch
        End Try

        Return result
    End Function

#End Region

#Region "--- 获取数据 ---"

    Public Function Get_Session_Info() As Dictionary(Of String, DateTime)
        Dim dicInfos As New Dictionary(Of String, DateTime)()
        Dim isExistTable As Boolean = False

        If mConn3 IsNot Nothing Then
            isExistTable = SQLiteClass3.CheckTableExist(mConn3, "SessionAbstract")
        Else
            isExistTable = SQLiteClass.CheckTableExist(mConn, "SessionAbstract")
        End If
        If Not isExistTable Then
            Return dicInfos
        End If

        Dim pstrSQL As String = "select UsrName,CreateTime from SessionAbstract order by CreateTime desc"
        Dim dt As DataTable = Nothing
        If mConn3 IsNot Nothing Then
            dt = SQLiteClass3.ExecuteSQL(pstrSQL, mConn3)
        Else
            dt = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
        End If

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim strUsrName As String = Common.GetValue(Of String)(dr("UsrName"), String.Empty)
                    Dim CreateTime As DateTime = DateTime.MinValue
                    Dim longCreateTime As Long = Common.GetValue(Of Long)(dr("CreateTime"), 0)

                    If longCreateTime <> 0 Then
                        CreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)
                    End If

                    dicInfos(strUsrName) = CreateTime
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Session_Info")
                End Try
            Next
        End If

        Return dicInfos
    End Function

    Public Function Get_Session_AllInfo() As Dictionary(Of String, SessionInfo)
        Dim dicInfos As New Dictionary(Of String, SessionInfo)()
        Dim isExistTable As Boolean = False

        If mConn3 IsNot Nothing Then
            isExistTable = SQLiteClass3.CheckTableExist(mConn3, "SessionAbstract")
        Else
            isExistTable = SQLiteClass.CheckTableExist(mConn, "SessionAbstract")
        End If
        If Not isExistTable Then
            Return dicInfos
        End If

        Dim pstrSQL As String = "select UsrName,CreateTime,ConStrRes1 from SessionAbstract order by CreateTime desc"
        Dim dt As DataTable = Nothing
        If mConn3 IsNot Nothing Then
            dt = SQLiteClass3.ExecuteSQL(pstrSQL, mConn3)
        Else
            dt = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
        End If

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim Info As New SessionInfo()

                    Info.UsrName = Common.GetValue(Of String)(dr("UsrName"), String.Empty)

                    Dim CreateTime As DateTime = DateTime.MinValue
                    Dim longCreateTime As Long = Common.GetValue(Of Long)(dr("CreateTime"), 0)
                    If longCreateTime <> 0 Then
                        Info.CreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)
                    End If

                    Info.ConStrRes1 = Common.GetValue(Of String)(dr("ConStrRes1"), String.Empty)

                    dicInfos(Info.UsrName) = Info
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Session_Info")
                End Try
            Next
        End If

        Return dicInfos
    End Function

#End Region

#Region "--- 删除会话 ---"

    Public Function Get_Chat_File(ByVal fInfo As WeChatFriendInfo) As String
        Dim strFile As String = String.Empty

        Dim pstrSQL As String = String.Format("select ConStrRes1 from SessionAbstract where UsrName='{0}';", fInfo.UsrName)
        Dim dt As DataTable = Nothing
        If mConn3 IsNot Nothing Then
            dt = SQLiteClass3.ExecuteSQL(pstrSQL, mConn3)
        Else
            dt = SQLiteClass.ExecuteSQL(pstrSQL, mConn)
        End If

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            strFile = Common.GetValue(Of String)(dt.Rows(0)("ConStrRes1"), String.Empty)
        End If

        Return strFile
    End Function

    Public Function Delete_Chat_Table(ByVal fInfo As WeChatFriendInfo) As Boolean
        Dim blnResult As Boolean = False

        Dim pstrSQL As String = String.Format("Delete From [SessionAbstract] where UsrName='{0}';", fInfo.UsrName)
        Debug.Print(pstrSQL)

        Dim errMsg As String = String.Empty
        If mConn3 IsNot Nothing Then
            SQLiteClass3.ExecuteNoneQuery(pstrSQL, mConn3, Nothing, Nothing, errMsg)
        Else
            SQLiteClass.ExecuteNoneQuery(pstrSQL, mConn, Nothing, Nothing, errMsg)
        End If

        If String.IsNullOrEmpty(errMsg) Then
            blnResult = True
        End If

        Return blnResult
    End Function

#End Region

End Class

'微信数据库（朋友圈）
Public Class WeixinMomentDB
    Private ReadOnly mDBFile As String = String.Empty
    Private mConn As SQLiteConnection = Nothing

#Region "--- 单实例 ---"

    Private Shared instance As WeixinMomentDB = Nothing
    Private Shared ReadOnly locker As New Object()

    Public Shared Function GetInstance(ByVal DBFile As String) As WeixinMomentDB
        If instance IsNot Nothing Then
            If String.Compare(DBFile, instance.mDBFile, True) <> 0 Then
                instance.Dispose()
                instance = Nothing
            End If
        End If

        If instance Is Nothing Then
            SyncLock locker
                If instance Is Nothing Then
                    instance = New WeixinMomentDB(DBFile)
                End If
            End SyncLock
        End If

        Return instance
    End Function

#End Region

#Region "--- 构造函数 ---"

    Private Sub New(ByVal DBFile As String)
        mDBFile = DBFile
        Reload()
    End Sub

    Public Sub Dispose()
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If

            instance = Nothing
        Catch
        End Try
    End Sub

#End Region

#Region "--- 加载数据库 ---"

    Public Function Reload() As Boolean
        Dim result As Boolean = False
        Try
            If mConn IsNot Nothing AndAlso mConn.State = System.Data.ConnectionState.Open Then
                mConn.Close()
            End If

            mConn = SQLiteClass.CreateConnectionFromFile(mDBFile)

            result = True
        Catch
        End Try

        Return result
    End Function

#End Region

#Region "--- 获取数据 ---"

    Public Function Get_Timeline_Info() As Dictionary(Of String, MomentInfo)
        Dim dicInfos As New Dictionary(Of String, MomentInfo)()

        Dim pstrSQL As String = "select * from MyWC_Timeline01 order by MyWC_Timeline01.[Id] desc"
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim info As New MomentInfo()
                    info.ID = Common.GetValue(Of String)(dr("Id"), String.Empty)
                    info.UsrName = Common.GetValue(Of String)(dr("FromUser"), String.Empty)
                    info.Buffer = Common.GetValue(Of Byte())(dr("Buffer"), New Byte() {})

                    dicInfos(info.ID) = info
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Session_Info")
                End Try
            Next
        End If

        Return dicInfos
    End Function

#End Region

End Class

'微信数据库（我的收藏）
Public Class WeixinFavouriteDB
    Private ReadOnly mDBFile As String = String.Empty
    Private mConn As SQLiteConnection = Nothing

#Region "--- 单实例 ---"

    Private Shared instance As WeixinFavouriteDB = Nothing
    Private Shared ReadOnly locker As New Object()

    Public Shared Function GetInstance(ByVal DBFile As String) As WeixinFavouriteDB
        If instance IsNot Nothing Then
            If String.Compare(DBFile, instance.mDBFile, True) <> 0 Then
                instance.Dispose()
                instance = Nothing
            End If
        End If

        If instance Is Nothing Then
            SyncLock locker
                If instance Is Nothing Then
                    instance = New WeixinFavouriteDB(DBFile)
                End If
            End SyncLock
        End If

        Return instance
    End Function

#End Region

#Region "--- 构造函数 ---"

    Private Sub New(ByVal DBFile As String)
        mDBFile = DBFile
        Reload()
    End Sub

    Public Sub Dispose()
        Try
            If Me.mConn IsNot Nothing AndAlso Me.mConn.State = ConnectionState.Open Then
                Me.mConn.Close()
            End If

            instance = Nothing
        Catch
        End Try
    End Sub

#End Region

#Region "--- 加载数据库 ---"

    Public Function Reload() As Boolean
        Dim result As Boolean = False
        Try
            If mConn IsNot Nothing AndAlso mConn.State = System.Data.ConnectionState.Open Then
                mConn.Close()
            End If

            mConn = SQLiteClass.CreateConnectionFromFile(mDBFile)

            result = True
        Catch
        End Try

        Return result
    End Function

#End Region

#Region "--- 获取数据 ---"

    Public Function Get_FavoritesItem_Info() As Dictionary(Of String, FavouriteInfo)
        Dim dicInfos As New Dictionary(Of String, FavouriteInfo)()

        Dim pstrSQL As String = "select * from FavoritesItemTable order by favoritesitemtable.[Time] desc"
        Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    Dim info As New FavouriteInfo()
                    info.ID = Common.GetValue(Of String)(dr("LocalId"), String.Empty)
                    info.Type = Common.GetValue(Of Integer)(dr("Type"), 0)
                    Dim longCreateTime As Long = Common.GetValue(Of Long)(dr("Time"), 0)
                    info.FavouriteTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)

                    info.FromUsr = Common.GetValue(Of String)(dr("FromUsr"), String.Empty)
                    info.ToUsr = Common.GetValue(Of String)(dr("ToUsr"), String.Empty)
                    info.RealChatName = Common.GetValue(Of String)(dr("RealChatName"), String.Empty)
                    info.Xml = Common.GetValue(Of String)(dr("Xml"), String.Empty)

                    dicInfos(info.ID) = info
                Catch ex As Exception
                    System.Diagnostics.Debug.WriteLine(ex.ToString())
                    Common.LogException(ex.ToString(), "Get_Session_Info")
                End Try
            Next
        End If

        Return dicInfos
    End Function

#End Region

End Class

'联系人类
''Public Class FriendInfo

''    Private mUsrName As String = String.Empty
''    Public Property UsrName() As String
''        Get
''            Return mUsrName
''        End Get
''        Set(ByVal value As String)
''            mUsrName = value

''            mMd5 = Common.ToHexString(Common.GetMd5Array(value))
''            'Debug.Print(UsrName & vbTab & mMd5)
''        End Set
''    End Property

''    Private mMd5 As String = String.Empty
''    Public Property Md5() As String
''        Get
''            Return mMd5
''        End Get
''        Set(ByVal value As String)
''            mMd5 = value
''        End Set
''    End Property

''    '昵称
''    Private mNickName As String = String.Empty
''    Public Property NickName() As String
''        Get
''            Return mNickName
''        End Get
''        Set(ByVal value As String)
''            mNickName = value
''        End Set
''    End Property

''    '备注名
''    Private mNoteName As String = String.Empty
''    Public Property NoteName() As String
''        Get
''            Return mNoteName
''        End Get
''        Set(ByVal value As String)
''            mNoteName = value
''        End Set
''    End Property

''    Private mSex As Integer
''    Public Property Sex() As Integer
''        Get
''            Return mSex
''        End Get
''        Set(ByVal value As Integer)
''            mSex = value
''        End Set
''    End Property

''    '67星标朋友；1；2；3；4；5；6；7；9（没搞懂)
''    Private mType As Integer
''    Public Property Type() As Integer
''        Get
''            Return mType
''        End Get
''        Set(ByVal value As Integer)
''            mType = value
''        End Set
''    End Property

''    Private mChatRoomMem As String = String.Empty
''    Public Property ChatRoomMem() As String
''        Get
''            Return mChatRoomMem
''        End Get
''        Set(ByVal value As String)
''            mChatRoomMem = value
''        End Set
''    End Property

''    '个人简介
''    Private mProfile As String = String.Empty
''    Public Property Profile() As String
''        Get
''            Return mProfile
''        End Get
''        Set(ByVal value As String)
''            mProfile = value
''        End Set
''    End Property

''    Private mLastChatTime As DateTime = Date.MinValue
''    Public Property LastChatTime() As DateTime
''        Get
''            Return mLastChatTime
''        End Get
''        Set(ByVal value As DateTime)
''            mLastChatTime = value
''        End Set
''    End Property

''    Private mLastChatText As String = String.Empty
''    Public Property LastChatText() As String
''        Get
''            Return mLastChatText
''        End Get
''        Set(ByVal value As String)
''            mLastChatText = value
''        End Set
''    End Property

''    Private mIsFindDate As Boolean = False
''    Public Property IsFindDate() As Boolean
''        Get
''            Return mIsFindDate
''        End Get
''        Set(ByVal value As Boolean)
''            mIsFindDate = value
''        End Set
''    End Property

''    Private mWeChatNum As String = String.Empty
''    Public Property WeChatNum() As String
''        Get
''            Return mWeChatNum
''        End Get
''        Set(ByVal value As String)
''            mWeChatNum = value
''        End Set
''    End Property


''    Public Sub New()
''    End Sub

''    Public Sub New(ByVal strUsrName As String)
''        Me.UsrName = strUsrName
''        Me.mNickName = strUsrName
''    End Sub

''End Class

' ''聊天类
''Public Class ChatInfo
''    Private mID As Integer
''    Public Property ID() As Integer
''        Get
''            Return mID
''        End Get
''        Set(ByVal value As Integer)
''            mID = value
''        End Set
''    End Property

''    Private mUsrName As String
''    Public Property UsrName() As String
''        Get
''            Return mUsrName
''        End Get
''        Set(ByVal value As String)
''            mUsrName = value
''        End Set
''    End Property

''    Private mMessage As String = String.Empty
''    Public Property Message() As String
''        Get
''            Return mMessage
''        End Get
''        Set(ByVal value As String)
''            mMessage = value
''        End Set
''    End Property

''    '2发送；4接收；3未读
''    Private mStatus As Integer
''    Public Property Status() As Integer
''        Get
''            Return mStatus
''        End Get
''        Set(ByVal value As Integer)
''            mStatus = value
''        End Set
''    End Property

''    '1文本；2网页（包括图片）；7语音（3好像是未读的语音）
''    Private mImgStatus As Integer
''    Public Property ImgStatus() As Integer
''        Get
''            Return mImgStatus
''        End Get
''        Set(ByVal value As Integer)
''            mImgStatus = value
''        End Set
''    End Property

''    '1文本；3图片；34语音；42名片；43视频；48地理位置；49网页；50视频未接通的消息；1000系统消息
''    Private mType As Integer
''    Public Property Type() As Integer
''        Get
''            Return mType
''        End Get
''        Set(ByVal value As Integer)
''            mType = value
''        End Set
''    End Property

''    '0发送；1接收
''    Private mDes As Integer
''    Public Property Des() As Integer
''        Get
''            Return mDes
''        End Get
''        Set(ByVal value As Integer)
''            mDes = value
''        End Set
''    End Property

''    Private mCreateTime As DateTime
''    Public Property CreateTime() As DateTime
''        Get
''            Return mCreateTime
''        End Get
''        Set(ByVal value As DateTime)
''            mCreateTime = value
''        End Set
''    End Property

''    Private mICreateTime As Integer
''    Public Property ICreateTime() As Integer
''        Get
''            Return mICreateTime
''        End Get
''        Set(ByVal value As Integer)
''            mICreateTime = value
''        End Set
''    End Property

''    '是否删除
''    Private mIsDelete As Boolean = False
''    Public Property IsDelete() As Boolean
''        Get
''            Return mIsDelete
''        End Get
''        Set(ByVal value As Boolean)
''            mIsDelete = value
''        End Set
''    End Property

''End Class

'朋友圈类
Public Class MomentInfo
    'Moment ID
    Private mID As String = String.Empty
    Public Property ID() As String
        Get
            Return mID
        End Get
        Set(ByVal value As String)
            mID = value
        End Set
    End Property

    '用户名（WCDataItem）
    Private mUsrName As String
    Public Property UsrName() As String
        Get
            Return mUsrName
        End Get
        Set(ByVal value As String)
            mUsrName = value
        End Set
    End Property

    '昵称（WCDataItem）
    Private mNickName As String
    Public Property NickName() As String
        Get
            Return mNickName
        End Get
        Set(ByVal value As String)
            mNickName = value
        End Set
    End Property

    '文字（WCDataItem）
    Private mContentDesc As String = String.Empty
    Public Property ContentDesc() As String
        Get
            Return mContentDesc
        End Get
        Set(ByVal value As String)
            mContentDesc = value
        End Set
    End Property

    '城市（WCDataItem）
    Private mCity As String = String.Empty
    Public Property City() As String
        Get
            Return mCity
        End Get
        Set(ByVal value As String)
            mCity = value
        End Set
    End Property

    '创建时间（WCDataItem）
    Private mCreateTime As DateTime
    Public Property CreateTime() As DateTime
        Get
            Return mCreateTime
        End Get
        Set(ByVal value As DateTime)
            mCreateTime = value
        End Set
    End Property

    '标题（WCContentItem）
    Private mTitle As String = String.Empty
    Public Property Title() As String
        Get
            Return mTitle
        End Get
        Set(ByVal value As String)
            mTitle = value
        End Set
    End Property

    '摘要（WCContentItem）
    Private mDesc As String = String.Empty
    Public Property Desc() As String
        Get
            Return mDesc
        End Get
        Set(ByVal value As String)
            mDesc = value
        End Set
    End Property

    '跳转地址（WCContentItem）
    Private mLinkUrl As String = String.Empty
    Public Property LinkUrl() As String
        Get
            Return mLinkUrl
        End Get
        Set(ByVal value As String)
            mLinkUrl = value
        End Set
    End Property

    '内容类型（WCContentItem）
    '1、图片+文字（有或无）
    '2、文字
    '3、链接
    '4、歌曲
    Private mType As Integer
    Public Property Type() As Integer
        Get
            Return mType
        End Get
        Set(ByVal value As Integer)
            mType = value
        End Set
    End Property

    '多媒体列表（WCContentItem）
    Private mMediaList As New List(Of MediaItem)
    Public Property MediaList() As List(Of MediaItem)
        Get
            Return mMediaList
        End Get
        Set(ByVal value As List(Of MediaItem))
            mMediaList = value
        End Set
    End Property

    '软件名称（WCAppInfo）
    Private mAppName As String = String.Empty
    Public Property AppName() As String
        Get
            Return mAppName
        End Get
        Set(ByVal value As String)
            mAppName = value
        End Set
    End Property

    '软件ID（WCAppInfo）
    Private mAppID As String = String.Empty
    Public Property AppID() As String
        Get
            Return mAppID
        End Get
        Set(ByVal value As String)
            mAppID = value
        End Set
    End Property

    '数据库中的值。plist文件。
    Private mBuffer As Byte()
    Public Property Buffer() As Byte()
        Get
            Return mBuffer
        End Get
        Set(ByVal value As Byte())
            mBuffer = value
        End Set
    End Property

End Class

'WCMediaItem
Public Structure MediaItem
    Public ID As String                'ID
    Public DataUrl As String        '类型图片则为原图，类型为歌则为mp3，类型为链接则为大图
    Public PreviewUrls As String  '缩略图
    Public ImgSize As String        '图片大小
End Structure

'我的收藏类
Public Class FavouriteInfo

    Private mID As String = String.Empty
    Public Property ID() As String
        Get
            Return mID
        End Get
        Set(ByVal value As String)
            mID = value
        End Set
    End Property

    '内容类型
    '1、文字
    '2、图片
    '3、语音
    '4、视频
    '5、链接
    '6、地理位置
    Private mType As Integer
    Public Property Type() As Integer
        Get
            Return mType
        End Get
        Set(ByVal value As Integer)
            mType = value
        End Set
    End Property

    '谁发送的（如果是群，则是群用户名）
    Private mFromUsr As String
    Public Property FromUsr() As String
        Get
            Return mFromUsr
        End Get
        Set(ByVal value As String)
            mFromUsr = value
        End Set
    End Property

    '发给谁（可能为群，可能没有）
    Private mToUsr As String
    Public Property ToUsr() As String
        Get
            Return mToUsr
        End Get
        Set(ByVal value As String)
            mToUsr = value
        End Set
    End Property

    '当mFromUsr为群时，则表示谁发送的
    Private mRealChatName As String
    Public Property RealChatName() As String
        Get
            Return mRealChatName
        End Get
        Set(ByVal value As String)
            mRealChatName = value
        End Set
    End Property

    '收藏时间
    Private mFavouriteTime As DateTime
    Public Property FavouriteTime() As DateTime
        Get
            Return mFavouriteTime
        End Get
        Set(ByVal value As DateTime)
            mFavouriteTime = value
        End Set
    End Property

    '创建时间（文本、图片、语音、视频
    Private mCreateTime As DateTime
    Public Property CreateTime() As DateTime
        Get
            Return mCreateTime
        End Get
        Set(ByVal value As DateTime)
            mCreateTime = value
        End Set
    End Property

    Private mXml As String
    Public Property Xml() As String
        Get
            Return mXml
        End Get
        Set(ByVal value As String)
            mXml = value
        End Set
    End Property

    '文本内容
    Private mDesc As String
    Public Property Desc() As String
        Get
            Return mDesc
        End Get
        Set(ByVal value As String)
            mDesc = value
        End Set
    End Property

    '图片缩略图路径，视频缩略路径，链接插图
    Private mSourceThumbPath As String
    Public Property SourceThumbPath() As String
        Get
            Return mSourceThumbPath
        End Get
        Set(ByVal value As String)
            mSourceThumbPath = value
        End Set
    End Property

    '图片原图路径、语音路径、视频路径
    Private mSourceDataPath As String
    Public Property SourceDataPath() As String
        Get
            Return mSourceDataPath
        End Get
        Set(ByVal value As String)
            mSourceDataPath = value
        End Set
    End Property

    '语音时长（毫秒），视频时长（秒）
    Private mDuration As Integer
    Public Property Duration() As Integer
        Get
            Return mDuration
        End Get
        Set(ByVal value As Integer)
            mDuration = value
        End Set
    End Property

    '链接标题
    Private mPageTitle As String
    Public Property PageTitle() As String
        Get
            Return mPageTitle
        End Get
        Set(ByVal value As String)
            mPageTitle = value
        End Set
    End Property

    '链接摘要
    Private mPageDesc As String
    Public Property PageDesc() As String
        Get
            Return mPageDesc
        End Get
        Set(ByVal value As String)
            mPageDesc = value
        End Set
    End Property

    '链接link（跳转地址）
    Private mPageLink As String
    Public Property PageLink() As String
        Get
            Return mPageLink
        End Get
        Set(ByVal value As String)
            mPageLink = value
        End Set
    End Property

    '链接缩略图url
    Private mPageThumbUrl As String
    Public Property PageThumbUrl() As String
        Get
            Return mPageThumbUrl
        End Get
        Set(ByVal value As String)
            mPageThumbUrl = value
        End Set
    End Property

    '地理位置标签
    Private mLocationLabel As String
    Public Property LocationLabel() As String
        Get
            Return mLocationLabel
        End Get
        Set(ByVal value As String)
            mLocationLabel = value
        End Set
    End Property

End Class

'会话类
Public Class SessionInfo

    Private mUsrName As String
    Public Property UsrName() As String
        Get
            Return mUsrName
        End Get
        Set(ByVal value As String)
            mUsrName = value
        End Set
    End Property

    Private mCreateTime As DateTime
    Public Property CreateTime() As DateTime
        Get
            Return mCreateTime
        End Get
        Set(ByVal value As DateTime)
            mCreateTime = value
        End Set
    End Property

    Private mConStrRes1 As String = String.Empty
    Public Property ConStrRes1() As String
        Get
            Return mConStrRes1
        End Get
        Set(ByVal value As String)
            mConStrRes1 = value
        End Set
    End Property

End Class

'微信状态
Public Enum WeixinState
    Loading             '正在加载
    NoInstalled         '没有安装
    CanNotAccess        '有安装，但是无法访问
    NoAccount           '没有登录
    NoDB                '没有数据库
    SuperLimited        '被第三方应用限制超级用户权限
    DownloadDBFailed    '数据库下载失败
    Succeed
    UpdateGroup
    UpdateIcon
    WebSite
    Guide               '引导用户关闭后台
    Above6220           '6.2.2.0以上的版本
    PwdBackup           '8.3及以上设备加密备份
    BackupFailure       '备份文件失败（可能是锁屏状态下）
    BackupFailureClosePwd '备份文件有密码
    CancelEncrypted     '取消解密
    OutOfDiskSpace      '磁盘空间不足
End Enum

<ProtoContract> _
Public Class WeChatFriendModel
    '添加特性，表示该字段可以被序列化，1可以理解为下标  
    <ProtoMember(1)> _
    Public strName As String
    <ProtoMember(2)> _
    Public strWeChatID As String
    <ProtoMember(3)> _
    Public strNickName As String
End Class

<ProtoContract> _
Public Class WeChatHeadLogoModel
    '添加特性，表示该字段可以被序列化，1可以理解为下标  
    <ProtoMember(2)> _
    Public HeadLogo123 As String
    <ProtoMember(3)> _
    Public HeadLogo As String
End Class
