﻿Imports System.Threading
Imports System.Web
Imports System.Text.RegularExpressions
Imports System.Xml

#If CEFSHARP Then
Imports iTong.CoreCefSharp
#End If

Public Class frmWeixin


    Private mHelper As IMExportHelper
    Private mHelperBackup As BackupHelper = Nothing
    Private mBackupInfo As BackupInfo = Nothing
    Private mAppType As IMAppType = IMAppType.None
    Private mAppSKU As String = String.Empty

    Private dgvFriend As tbDataGridViewEx = Nothing

    Private mThreadLoadHeadIcon As Thread = Nothing
    Private mThreadLoadChat As Thread = Nothing
    Private mThreadNewSearch As Thread = Nothing
    Private mThreadIsInstallExcel As Thread = Nothing

    Private mAppIconSize As New Size(44, 44)
    Private mDefaultChatCount As Integer = 30
    Private mDefaultHeadIcon As Image = Nothing
    Private mPlayer As IPluginPlayer
    Private mGraphic As Graphics = Nothing

    Private mBlnInstallExcel As Boolean = True
    Private mThrBackup As Thread = Nothing
    Private mInstallHelper As iPhoneInstallHelper

    Private mFontText As Font = Nothing
    Private mFontNumber As Font = Nothing

    Private mShowTime As Integer = 0
    Private mShowSearchTime As Integer = 0

    Private tmrShowCount As System.Timers.Timer
    Private tmrShowSearchCount As System.Timers.Timer

    Private mIsFromWeixinMain As Boolean = False

    Private mBackupRestoreErrorType As BackupRestoreErrorType = BackupRestoreErrorType.None

    Private mFrmWeChatMain As frmWeixinMain = Nothing

    Private mChargeHelper As ChargeHelper = Nothing
    Private mSelectAllClick As Boolean = False

    Private mHasOpenDataRecoverySite As Boolean = False

    Private mIsFindDataing As Boolean = False

    Public Property IsFromWeixinMain() As Boolean
        Get
            Return Me.mIsFromWeixinMain
        End Get
        Set(ByVal value As Boolean)
            Me.mIsFromWeixinMain = value

            If value Then
                Me.tlpMain.Dock = DockStyle.Fill
            Else
                Me.tlpMain.Dock = DockStyle.None
            End If
        End Set
    End Property

    'Private mAloneBackupsHelper As AloneBackupsHelper = Nothing
    Private mIsLoadTemp As Boolean = False

    '重设备读取 是否 单独重新备份微信
    Private mIsFullBackup As Boolean = False

    Private mTDBtnBackup As Thread = Nothing
    Private mIsBtnBackupResult As Boolean = False
    Private mIsBtnBackupWaiting As Boolean = False

    Private mTDCopyBackup As Thread = Nothing
    Private mTDFindData As Thread = Nothing

    Private mABType As AloneBackupType = AloneBackupType.None

    Private mIsSelectChageUser As Boolean = False
    Private mIsInsertRow As Boolean = False


    Private mLastSelectFriendInfo As SocialFriendInfoBase = Nothing
    Private mLastLoadPara As IMLoadPara = Nothing
    Private mLastCreateTime As DateTime = DateTime.MinValue
    Private mLastCreateID As String = String.Empty

#If IS_RECOVERY Then
    Private mfrmDataRecovery As frmWelcomeDataRecovery = Nothing
#End If

    '界面Style
    Private Enum ViewStyle
        'Welcome
        Loading
        Home
        Description
        DescriptionPwdBackup
    End Enum


    Private mDBPwd As String = "" '加密数据的秘钥

    Private mIsDoWCDB As Boolean = False

    Private mShowFriendType As ShowFriendType = ShowFriendType.ShowAll

    Private mADIndex As Integer = 1

    Private mIsNewBackup As Boolean = False

#Region "---  初始化  ---"

    Public Sub New(ByVal application As IApplication, _
                   ByVal device As iPhoneDevice, _
                   ByVal backupInfo As BackupInfo, _
                   ByVal appType As IMAppType, _
                   Optional ByVal appSKU As String = "", _
                   Optional ByVal frmMain As Object = Nothing, _
                   Optional ByVal abType As AloneBackupType = AloneBackupType.None, _
                    Optional ByVal isNewBackup As Boolean = True)

        MyBase.New(application, device)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mIsNewBackup = isNewBackup

        Me.mApplication = application
        Me.Language = application.Language

        Me.FunctionMappingKey = FunctionKey.Weixin

        Me.tbAutoSetFormSize = True
        Me.mDefaultHeadIcon = My.Resources.weixin_icon_default.Clone

        Me.Size = New Size(970, 650)
        Me.Icon = My.Resources.iTong

        Me.wbsChat.WebBrowserShortcutsEnabled = True

        Me.mBackupInfo = backupInfo
        Me.mAppSKU = appSKU
        Me.mAppType = appType

        If frmMain IsNot Nothing AndAlso frmMain.GetType().FullName.Contains("frmWeixinMain") Then
            Me.mFrmWeChatMain = frmMain
        Else

#If IS_RECOVERY Then
        Me.mfrmDataRecovery = frmMain
#End If

        End If

        Me.mABType = abType

        Me.SetViewStyle(ViewStyle.Loading)
        Me.lblLoading.Text = Me.Language.GetString("Weixin.Lable.LoadData") '"正在获取微信数据"
        Me.lblLoading.Font = Common.CreateFont("微软雅黑", 9.5!, FontStyle.Regular)
    End Sub

    Private Sub InitHelper()
        Me.mHelperBackup = BackupHelper.Instance(Me.mDevice)
        AddHandler Me.mHelperBackup.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged
        AddHandler Me.mHelperBackup.DecryptInputPwd, AddressOf Me.OnDecryptInputPwd

        '微信分身 
        If Not Me.mIsSelectChageUser AndAlso Me.mABType = AloneBackupType.WeChatSpare Then

            Dim strBackupFolder As String = ""
            If Me.mBackupInfo IsNot Nothing Then
                strBackupFolder = Me.mBackupInfo.FolderPath
            End If

            Dim frmCWCSA As frmChoiceWeChatSpareApp = New frmChoiceWeChatSpareApp(Me.mApplication, Me.mDevice, False, strBackupFolder)
            If Not frmCWCSA.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
                If Me.mFrmWeChatMain IsNot Nothing Then
                    Me.mFrmWeChatMain.mIsFromBackupShow = True
                    Me.mFrmWeChatMain.SetViewStyle(WeixinMainViewStyle.LoadBackup)
                    Me.Close()
                    Return
                End If
            End If
            AloneBackupsHelper.GetQuickInfoByABType(Me.mABType, Me.mAppSKU, "", "")
        End If


        Dim para As New IMInstancePara()
        para.iPhone = Me.mDevice
        para.AppType = Me.mAppType
        para.AppSKU = Me.mAppSKU

        para.IsFullBackup = True
        If Me.mBackupInfo IsNot Nothing Then
            para.BackupFolder = Me.mBackupInfo.FolderPath
        End If
        para.BackupHelper = Me.mHelperBackup

        If Me.mFrmWeChatMain IsNot Nothing AndAlso Me.mFrmWeChatMain.mWCMCinfo IsNot Nothing Then
            para.AppSKU = Me.mFrmWeChatMain.mWCMCinfo.SrcSKU
        End If

        para.IsFullBackup = Me.mIsNewBackup

        Me.mHelper = IMExportHelper.Instance(para)
        Me.mHelper.BackupProgressChanged = New BackupRestoreHandler(AddressOf Me.OnBackupProgressChanged)
        AddHandler Me.mHelper.FindDataProgressChanged, AddressOf Me.OnFindDataProgressChanged
        AddHandler Me.mHelper.FindFriend, AddressOf Me.OnFindFriend
    End Sub

    Private Sub InitMusicPlayer()
        Me.mPlayer = PluginPlayer.Instance()
        Me.mPlayer.PlayMode = UserPlayMode.OrderPlay

        If Me.mPlayer.GetUserPlaylist(Me.Name) Is Nothing Then
            Me.mPlayer.AddUserPlaylist(Me.Name)
        End If

        AddHandler Me.mPlayer.PlayStateChange, AddressOf OnPlayStateChange
    End Sub

    Private Sub InitInstallHelper()
        If Me.mDevice Is Nothing Then
            Return
        End If

        Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)
        AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
    End Sub

    Private Sub InitTimer()
        Me.tmrShowCount = New System.Timers.Timer(1000)
        AddHandler Me.tmrShowCount.Elapsed, AddressOf tmrShowCount_Elapsed

        Me.tmrShowSearchCount = New System.Timers.Timer(1000)
        AddHandler Me.tmrShowSearchCount.Elapsed, AddressOf tmrShowSearchCount_Elapsed
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitHelper()
        Me.InitMusicPlayer()
        Me.InitDataGridView()
        Me.InitInstallHelper()
        Me.InitTimer()
        Me.InitChargeHelper()

        ChatWebPage.InitChatWebPage(False)
        Me.SetPanelCountVisible(False)
        Me.SetSearchPanelCountVisible(False)
        Me.SetPanelTimeFilterVisible(False)

        Me.LoadUsers()

        Me.tsmiSearchAll.Checked = True

        Me.btnWeCahtUsers.Enabled = False
        Me.btnWeCahtUsers.Text = Me.Language.GetString("Download.Label.Loading")


#If Not IS_RECOVERY Then
        '设置父窗体隐藏
        If Me.Owner IsNot Nothing Then
            Me.Owner.Visible = False
        End If
#End If

        Me.mFontText = Common.CreateFont("微软雅黑", 12.0F, FontStyle.Regular)
        Me.mFontNumber = Common.CreateFont("微软雅黑", 14.0F, FontStyle.Regular)

        Me.btnWeCahtUsers.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.btnTime.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)

        If Not Folder.LangType = LanguageType.en_US Then
            Common.SetImageLocation(Me.picLoading, GuiResource.gif_loading_24)
        End If

        MainForm.SetButtonStatus(Me.btnExportMedia)
        MainForm.SetButtonStatus(Me.btnFinalData)

        '找回数据 红点
        If IniSetting.GetShowFindWeChatDataNew() Then
            Me.btnFinalData.tbShowDot = True
        End If

        'Me.btnDataRecovery.Visible = ServerIniSetting.IsShowTBGuide()
        Me.tsmiRecoveryToPhone.Visible = ServerIniSetting.IsShowTBGuideWechatRecoveryDataToPhone()

        Dim strBackupFolder As String = ""
        If Me.mBackupInfo IsNot Nothing Then
            strBackupFolder = Me.mBackupInfo.FolderPath
        End If

        Me.pnlBackupTip.Visible = String.IsNullOrEmpty(strBackupFolder)
        If Me.pnlBackupTip.Visible OrElse m_bIsiCloud Then
            Me.lblFromBackupMsg.Visible = False
            Me.llblRefresh.Visible = False
        Else
            Dim CBInfo As New BackupInfo(strBackupFolder)
            Dim strName As String = CBInfo.DisplayName
            If strName.Length > 10 Then
                strName = strName.Substring(0, 8) & "..."
            End If

            Me.lblFromBackupMsg.Visible = True
            Me.lblFromBackupMsg.Text = String.Format(Me.Language.GetString("Common.Backup.DataExplain"), strName, CBInfo.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute))) '当前数据不是最新，是读取【{0}-{1}】的备份文件
            Me.llblRefresh.Visible = (Me.mFrmWeChatMain IsNot Nothing AndAlso Me.mFrmWeChatMain.mDevice IsNot Nothing)
        End If

        If IniSetting.GetWeChatRecoveryDataShowDot() Then
            Me.btnDataRecoveryToPhone.tbShowDot = True
        End If

        If Me.mABType = AloneBackupType.WeChatPayMigration Then
            Me.btnDataRecoveryToPhone.Visible = False
        End If

        'If Folder.AppType = RunType.Tongbu_Abroad Then
        '    Me.btnDataRecoveryToPhone.Visible = False
        '    Me.PictureBoxAD.Visible = False
        'End If
        Me.PictureBoxAD.Visible = False

        If IniSetting.GetADShowIndex() = 1 Then
            Me.PictureBoxAD.Image = Global.iTong.My.Resources.Resources.adqqbanner 'Global.iTong.My.Resources.Resources.adzxkf_banner
            IniSetting.SetADShowIndex(2)
            Me.mADIndex = 1
        Else
            Me.PictureBoxAD.Image = Global.iTong.My.Resources.Resources.adqqbanner
            IniSetting.SetADShowIndex(1)
            Me.mADIndex = 2
        End If

    End Sub


    Private Delegate Sub SetLblRegisterInfoHandler()

    Private Sub SetLblRegisterInfo()
        If Me.InvokeRequired Then
            Me.Invoke(New SetLblRegisterInfoHandler(AddressOf SetLblRegisterInfo))
        Else
            Try
                Dim fontF As Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
                Dim isRegisterVisible As Boolean = Not Me.mChargeHelper.CheckIsVIP()
                Me.lblRegister.Visible = isRegisterVisible

                Dim strIdentifier As String = ""

                If Me.mBackupInfo IsNot Nothing Then
                    strIdentifier = Me.mBackupInfo.Identifier
                ElseIf Me.mDevice IsNot Nothing Then
                    strIdentifier = Me.mDevice.Identifier
                End If

                If String.IsNullOrEmpty(strIdentifier) Then
                    Return
                End If

                Me.lblRegister.ColorTextList.Clear()

                If Not Me.mChargeHelper.CheckIsVIP() Then
                    Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("Weixin.Lable.RegisterWarn"), 0, System.Drawing.Color.FromArgb(51, 51, 51), fontF, False) '试用版预览部分内容用“*”代替
                ElseIf Not Me.mChargeHelper.CheckDeviceRegistered(strIdentifier) Then
                    Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("MainVip.Message.DeviceOverAmountEx"), 0, System.Drawing.Color.FromArgb(51, 51, 51), fontF, False) '试用版预览部分内容用“*”代替
                    Me.lblRegister.Visible = True
                End If
                Me.lblRegister.ColorTextList.AddItem(Me.Language.GetString("Weixin.Lable.BuyNow"), 1, System.Drawing.Color.FromArgb(0, 169, 255), fontF, False, True) '"立即购买"
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetLblRegisterInfo")
            End Try
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        If Me.mABType = AloneBackupType.WeChat Then
            Me.Text = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") '微信消息记录
        ElseIf Me.mABType = AloneBackupType.WeChatSpare Then
            Me.Text = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") & "-" & Me.Language.GetString("WeChat.Lable.Coexist") '"共存版"
        ElseIf Me.mABType = AloneBackupType.WeChatVIPSpare Then
            Me.Text = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") & "-VIP"
        End If

        '菜单
        Me.txtSearchChat.SearchTipText = Me.Language.GetString("Weixin.tbSearch.SearchTip") '"按回车搜索消息记录" 

        Me.lblStartTime.Text = Me.Language.GetString("Contact.Label.Date") '"日期"
        Me.lblEndTime.Text = "～" 'Me.Language.GetString("Weixin.Lable.EndTime") '"结束日期"

        Me.tsmiExcelEx.Text = Me.Language.GetString("Backup.Button.ExportToExcel") '"导出为Excel文件"
        Me.tsmiTxtEx.Text = Me.Language.GetString("Backup.Button.ExportToTxt") '"导出为Txt文件"
        Me.tsmiHtml.Text = Me.Language.GetString("Backup.Button.ExportToHtml") '"导出为Html文件"

        Me.tsmiGroup.Text = Me.Language.GetString("Backup.Button.ExportGroupFriend") '"选择群联系人导出"

        Me.tsmiRecoveryToComputer.Text = Me.Language.GetString("Button.ArtificialDataRecoveryToComputer") '人工数据恢复到电脑
        Me.tsmiRecoveryToPhone.Text = Me.Language.GetString("Button.ArtificialDataRecoveryToPhone")     '人工数据恢复到手机

        Me.btnFilter.Text = Me.Language.GetString("WeChat.Menu.ShowAll") '"显示全部"

        Me.lblOpenTutorial.Text = Me.Language.GetString("Weixin.Button.OpenTutorial") '备份失败？备份卡在某一处不动？戳我>>

        'loading页
        Me.lblLoading.Text = Me.Language.GetString("Weixin.Lable.LoadData") '"正在获取微信数据"
        Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Lable.NotInstall") '"尚未安装微信"
        If Me.mABType = AloneBackupType.WeChat Then
            Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Lable.Description") '"微信是一款手机通信软件，支持通过手机网络发送语音短信、视频、图片和文字，可以单聊及群聊，还能根据地理位置找到附近的人，带给朋友们全新的移动沟通体验。"
        ElseIf Me.mABType = AloneBackupType.WeChatSpare OrElse Me.mABType = AloneBackupType.WeChatVIPSpare Then
            Me.lblWeixinDescription.Text = Me.Language.GetString("WeChat.Message.CoexisNote") '"微信分身版（共存版，多开版） 可以在同一台手机安装多个微信，同时登入多个不同的微信帐号."
        End If

        Me.btnReCheck.Text = Me.Language.GetString("File.Button.CheckInstall") '"重新检查"
        Me.lblDownloadWeixin.Text = Me.Language.GetString("Weixin.Lable.GetWeixin") '"立即免费获取微信"
        'Me.lblSearchText.Location = New Point(Me.txtSearchChat.Left - 10 - Me.lblSearchText.Width, Me.lblSearchText.Top)

        Me.btnOK.Text = Me.Language.GetString("Common.OK")

        Me.tsmiPicEx.Text = Me.Language.GetString("Weixin.Lable.ExportPic")                               '"导出图片"
        Me.tsmiAudioEx.Text = Me.Language.GetString("Weixin.Lable.ExportAudio")                           '"导出语音"
        Me.tsmiVideoEx.Text = Me.Language.GetString("Weixin.Lable.ExportSmallVideo")                      '"导出小视频"
        Me.tsmiAll.Text = Me.Language.GetString("Contact.Menu.ExportAll")

        'Me.btnPreviousMenu.Text = Me.Language.GetString("Weixin.Button.Return") '返回上级

        Me.btnMoreTutorials.Text = Me.Language.GetString("MessageForm.Label.SeeMoreTutorial")           '"更多教程"

        Me.lblCancelPwdBackup.Font = Common.CreateFont("微软雅黑", 16.0!, FontStyle.Regular)
        Me.lblStep1Description.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.lblStep2Description.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.btnReCheckEx1.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)

        Me.lblCancelPwdBackup.Text = Me.Language.GetString("Weixin.Lable.LoadFailureByEncryption")              '"加载微信失败，请取消iTunes加密备份"
        Me.lblStep1Description.Text = Me.Language.GetString("Weixin.Lable.OpeniTunesSelectDevice")              '"打开iTunes,选择设备"
        Me.lblStep2Description.Text = Me.Language.GetString("Weixin.Lable.CancelDeviceEncryptionBackup")        '"取消该设备的备份加密"
        Me.btnReCheckEx1.Text = Me.Language.GetString("Weixin.Button.Continue")                                 '"已取消，继续"

        Me.btnFinalData.Text = Me.Language.GetString("Weixin.Button.RecoverMessages") '"找回聊天记录"

        'Me.lblCurrentAccount.Text = Me.Language.GetString("Weixin.Lable.CurrentAccount") '"当前帐号"

        'Me.lklblQQ.Text = Me.Language.GetString("Weixin.Lable.JoinQQ") '遇到问题？点此加入讨论群>>

        Dim strFindDataInfo As String = Me.Language.GetString("Weixin.Lable.ContactNames") '"左侧联系人昵称显示「红色」为有找到删除数据"

        Me.btnOpenTutorial.Text = Me.Language.GetString("Weixin.Lable.OpenTutorial")        '"打开教程"

        Me.Selection(strFindDataInfo, rtxtFindDataInfo, "「", "」", Color.Red)

        Try
            RichTextBox1.Clear()
            RichTextBox1.SelectionAlignment = HorizontalAlignment.Left
            RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint8"))
            RichTextBox1.AppendText(vbCrLf)
            RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint2"))
            RichTextBox1.AppendText(vbCrLf)
            RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint9"))
        Catch
        End Try


        Me.btnPreviousMenu.Visible = Me.mHelper.ParaCache.IsBackup
        If Me.mFrmWeChatMain Is Nothing Then
            Me.btnPreviousMenu.Visible = False
        End If

        RichTextBox2.AppendText(Me.Language.GetString("Weixin.Lable.Hint5")) '"此功能可【找回】已删除的微信聊天记录"
        RichTextBox2.AppendText(vbCrLf)
        RichTextBox2.AppendText(Me.Language.GetString("Weixin.Lable.Hint6")) '"包括找回：已经删除联系人的聊天记录"
        RichTextBox2.AppendText(vbCrLf)
        RichTextBox2.AppendText(Me.Language.GetString("Weixin.Lable.Hint7")) '"同时，支持非越狱设备的多账号聊天记录找回，无需登陆微信号，点击右上角账号框即可切换账号。"

        Me.lblFindData.Text = Me.Language.GetString("Weixin.Lable.FindDataing") ' "正在为您找回删除的聊天记录，请稍候..."
        Me.lblLoadData.Text = Me.Language.GetString("Weixin.Lable.LoadDataing") ' "数据加载中，请稍候..."

        Me.tsmiShowDelete.Text = Me.Language.GetString("Weixin.Lable.ShowOnlyDelete") '只显示删除找回
        Me.tsmiShowNoDelete.Text = Me.Language.GetString("Weixin.Lable.ShowUnDelete")
        Me.tsmiShowAll.Text = Me.Language.GetString("WeChat.Menu.ShowAll")
        Me.btnFilter.Text = Me.Language.GetString("WeChat.Menu.ShowAll")

        Me.btnTimeFilter.Text = Me.Language.GetString("Weixin.Lable.TimeFilter") '按时间筛选
        Me.btnExportSetting.Text = Me.Language.GetString("Weixin.Lable.ExportSetting") '导出设置
        Me.btnDataRecoveryToPhone.Text = Me.Language.GetString("Weixin.Lable.RestoreToPhone") '恢复到手机
        Me.btnExport.Text = Me.Language.GetString("Weixin.Lable.ExportToComputer") '导出到电脑
        Me.cbxSelectAll.Text = Me.Language.GetString("Common.SelectAll")
        Me.llblRefresh.Text = Me.Language.GetString("Weixin.Button.GetLatest")

        Me.tsmiFormat.Text = Me.Language.GetString("Weixin.Button.FormatExport")
        Me.tsmiMedia.Text = Me.Language.GetString("Weixin.Button.MediaExport")
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        If Me.Parent Is Nothing Then
            Me.Close()
        End If
    End Sub

    Private Sub InitDataGridView()
        Me.dgvFriend = New tbDataGridViewEx()
        With Me.dgvFriend
            .SuspendLayout()

            .tbMouseHoverResponse = True
            .RowTemplate.Height = 58
            .ColumnHeadersVisible = False
            .tbShowNoData = False
            .MultiSelect = True
            '.ContextMenuStrip=

            .Columns.Clear()
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            '1、用户名（key）
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colBlank", "", 15, False, False, DataGridViewContentAlignment.MiddleLeft, True))

            '2、昵称（显示）
            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Main.Button.Contact"), _
                                                                                                                                                                      165, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            '设置样式
            Dim cellStyle As New DataGridViewCellStyle()
            'cellStyle.Font = New Font("宋体", 9, FontStyle.Bold)
            cellStyle.Font = Common.CreateFont(Me.Font.Name, 9, FontStyle.Bold)
            cellStyle.ForeColor = Color.Black
            colName.DefaultCellStyle = cellStyle

            .Columns.Add(colName)

            '3、时间
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colTime", Me.Language.GetString("Music.Label.Duration"), _
                                                                                                          50, True, True, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, False, True, DataGridViewContentAlignment.MiddleLeft, True))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colMD5", "", 5, False, True, DataGridViewContentAlignment.MiddleLeft, True))

            .ResumeLayout()
        End With

        AddHandler dgvFriend.SelectionChanged, AddressOf dgvFriend_SelectionChanged

        Utility.AddForm2Panel(Me.dgvFriend, Me.pnlFriend)

        Me.dgvFriend.BringToFront()
    End Sub

    Private Sub dgvFriend_SelectionChanged(ByVal sender As Object, ByVal e As EventArgs)
        Try
            If Me.mSelectAllClick Then
                Return
            End If

            Dim currentRow As DataGridViewRow = Me.GetCurrentRow()
            If currentRow Is Nothing Then
                Return
            End If

            Dim friendInfo As SocialFriendInfoBase = currentRow.Tag
            If friendInfo Is Nothing Then
                Return
            End If

            If Me.mLastSelectFriendInfo IsNot friendInfo Then
                '显示Loading界面
                Me.SetLoading(True)

                '清空Web内容
                ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
            End If


            Me.LoadChat(FindDataType.Normal)
            Me.ShowFindDataResultPanel(False)
            Me.SetLinkmanMsg()
            Me.PictureBoxAD.Visible = False
        Catch ex As Exception
            Common.LogException(ex.ToString(), "dgvFriend_SelectionChanged")
        End Try
    End Sub

    Private Sub InitChargeHelper()
        Dim projectType As ChargeProjectType = ChargeProjectType.Assistant_Wechat_iOS

        If Folder.AppType = RunType.iWeChatMigration Then
            projectType = ChargeProjectType.Assistant_Wechat_iOS_DataMove

        ElseIf Folder.AppType = RunType.WechatMaster Then
            projectType = ChargeProjectType.WechatManage

        ElseIf Folder.AppType = RunType.iDataRecovery Then
            projectType = ChargeProjectType.iDataRecovery

        ElseIf Folder.AppType = RunType.iDataRecovery_Abroad Then
            projectType = ChargeProjectType.iDataRecovery_Abroad

        ElseIf Folder.AppType = RunType.Tongbu_Abroad Then
            projectType = ChargeProjectType.Assistant_Abroad

        End If

        Me.mChargeHelper = ChargeHelper.Instance(projectType)
        AddHandler Me.mChargeHelper.QueryCallback, AddressOf OnQueryCallback
        AddHandler Me.mChargeHelper.ActiveCallback, AddressOf OnActiveCallback

        If Me.mFrmWeChatMain Is Nothing AndAlso Me.mBackupInfo IsNot Nothing Then
            '从备份还原入口进来的数据没有做绑定手机的操作，进来后要重新绑定一次
            Me.mChargeHelper.SetDeviceRegistedLocal(Me.mBackupInfo.Identifier, Me.mBackupInfo.SerialNumber, Me.mBackupInfo.ProductType, Me.mBackupInfo.DeviceName)
        End If

        Me.mChargeHelper.Query()

        Me.SetLblRegisterInfo()
    End Sub

    Private Sub OnQueryCallback(sender As Object, e As ChargeResultArgs)
        SetLblRegisterInfo()
    End Sub
    Private mIsActiveCellback As Boolean = False
    Private Sub OnActiveCallback(sender As Object, e As ChargeResultArgs)
        '用户激活成功后自动注册当前设备
        Dim strIdentifier As String = ""
        If Me.mBackupInfo IsNot Nothing Then
            strIdentifier = Me.mBackupInfo.Identifier
        ElseIf Me.mDevice IsNot Nothing Then
            strIdentifier = Me.mDevice.Identifier
        End If

        If Me.mFrmWeChatMain Is Nothing AndAlso Not e.Code = ActiveCode.HasAuthed Then
            tbMessageBox.Show(Me, e.Msg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, e.MsgBoxIcon)
        End If

        'AndAlso Not Me.mChargeHelper.CheckDeviceRegistered(strIdentifier)
        If Me.mChargeHelper.CheckIsVIP() Then
            If Me.mBackupInfo IsNot Nothing Then
                Me.mChargeHelper.SetDeviceRegistedLocal(Me.mBackupInfo.Identifier, Me.mBackupInfo.SerialNumber, Me.mBackupInfo.ProductType, Me.mBackupInfo.DeviceName)
            ElseIf Me.mDevice IsNot Nothing Then
                Me.mChargeHelper.SetDeviceRegistedLocal(Me.mDevice)
            End If
        End If

        SetLblRegisterInfo()
        Me.mIsActiveCellback = True
        Me.dgvFriend_SelectionChanged(Nothing, Nothing)
        Me.mIsActiveCellback = False
    End Sub


    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            If Me.dgvFriend IsNot Nothing Then
                For Each item As tbDataGridViewRow In Me.dgvFriend.Rows
                    item.Tag = Nothing
                    item = Nothing
                Next
                Me.dgvFriend.Dispose()
                Me.dgvFriend = Nothing
            End If

            If Me.mInstallHelper IsNot Nothing Then
                Me.mInstallHelper.Dispose()
                Me.mInstallHelper = Nothing
            End If


            If Me.mHelperBackup IsNot Nothing Then
                RemoveHandler Me.mHelperBackup.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged
                RemoveHandler Me.mHelperBackup.DecryptInputPwd, AddressOf Me.OnDecryptInputPwd
            End If

            If Me.mHelper IsNot Nothing Then
                RemoveHandler Me.mHelper.FindDataProgressChanged, AddressOf Me.OnFindDataProgressChanged
                RemoveHandler Me.mHelper.FindFriend, AddressOf Me.OnFindFriend
                Me.mHelper.BackupProgressChanged = Nothing
                Me.mHelper.Dispose()
            End If

            If Me.mChargeHelper IsNot Nothing Then
                RemoveHandler Me.mChargeHelper.QueryCallback, AddressOf Me.OnQueryCallback
                RemoveHandler Me.mChargeHelper.ActiveCallback, AddressOf Me.OnActiveCallback
            End If

            If Me.tmrShowCount IsNot Nothing Then
                Me.tmrShowCount.Close()
                RemoveHandler Me.tmrShowCount.Elapsed, AddressOf tmrShowCount_Elapsed
            End If

            If tmrShowSearchCount IsNot Nothing Then
                Me.tmrShowSearchCount.Close()
                RemoveHandler Me.tmrShowSearchCount.Elapsed, AddressOf tmrShowSearchCount_Elapsed
            End If

            If Me.mPlayer IsNot Nothing Then
                RemoveHandler Me.mPlayer.PlayStateChange, AddressOf OnPlayStateChange
            End If

            If Me.mInstallHelper IsNot Nothing Then
                RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
            End If

            If Me.bgwLoadUsers IsNot Nothing Then
                If Me.bgwLoadUsers.IsBusy Then
                    Me.bgwLoadUsers.CancelAsync()
                End If

                RemoveHandler Me.bgwLoadUsers.DoWork, AddressOf bgwLoadUsers_DoWork
                RemoveHandler Me.bgwLoadUsers.RunWorkerCompleted, AddressOf bgwLoadUsers_RunWorkerCompleted
            End If

            If Me.mHelperBackup IsNot Nothing Then
                RemoveHandler Me.mHelperBackup.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged
                RemoveHandler Me.mHelperBackup.DecryptInputPwd, AddressOf Me.OnDecryptInputPwd

                Me.mHelperBackup.CancelBackup()
            End If

            Utility.AbortThread(Me.mThreadLoadHeadIcon)
            Utility.AbortThread(Me.mTDCopyBackup)
            Utility.AbortThread(Me.mTDFindData)
            Utility.AbortThread(Me.mThreadLoadChat)
            Utility.AbortThread(Me.mThreadNewSearch)
            Utility.AbortThread(Me.mThreadIsInstallExcel)
            Utility.AbortThread(Me.mThrBackup)


            If pblnCancelClose = False AndAlso Me.Owner IsNot Nothing AndAlso Me.Owner.Visible = False Then
                Me.Owner.Visible = True
            End If

            If Me.mFrmWeChatMain IsNot Nothing Then
                If Not String.IsNullOrEmpty(Me.mHelper.ParaCache.BackupFolderEncrypt) AndAlso Directory.Exists(Me.mHelper.ParaCache.BackupFolder) Then
                    IMExportHelper.DeleteEncryptedBackup(Me.mHelper.ParaCache.BackupFolder)
                End If
            End If

        Catch ex As Exception
        End Try
    End Sub

    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of InstallArgs)(AddressOf mInstallHelper_OnInstall), sender, args)
        Else
            Try
                '不是安装微信的就不显示进度到当前界面  
                If args.PackageInfo.Identifier <> Me.mHelper.ParaCache.AppSKU Then
                    Return
                End If

                Select Case args.InstallState
                    Case InstallState.Nomal

                    Case InstallState.Waiting
                        Me.lblState.Text = Me.Language.GetString("Download.Label.Waiting") '"Waiting Install"
                        Me.SetFrmWeChatMainlblState()

                    Case InstallState.AuthBegin
                        Me.lblState.Text = Me.Language.GetString("App.Message.Calculating")              '"正在计算" 
                        Me.SetFrmWeChatMainlblState()

                    Case InstallState.Transfering
                        Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("File.Label.Uploading"), args.TransferProgress) '"Uploading"
                        Me.SetFrmWeChatMainlblState()

                    Case InstallState.Installing
                        If args.InstallApplicationEventArgs IsNot Nothing Then
                            Select Case args.InstallApplicationEventArgs.InstallProgress
                                Case InstallapplicationProgress.OnUnzip, _
                                     InstallapplicationProgress.OnRestoreContainer
                                    Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("App.Cell.Restoring"), args.TransferProgress) '"Restoring"
                                    Me.SetFrmWeChatMainlblState()

                                Case InstallapplicationProgress.OnInstall
                                    Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("Backup.Message.RestoreMessage"), 0) '"Installing"
                                    Me.SetFrmWeChatMainlblState()

                            End Select
                        Else
                            Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("Backup.Message.RestoreMessage"), args.TransferProgress) '"Installing"
                            Me.SetFrmWeChatMainlblState()

                        End If
                        If args.TransferProgress = 100 Then
                            Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreSucceed")
                            Me.SetFrmWeChatMainlblState()
                        End If


                    Case InstallState.Succeed
                        Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreSucceed") '"安装成功"
                        Me.SetFrmWeChatMainlblState()
                    Case InstallState.HaveNotInstall
                        Me.lblState.Text = args.ErrorMsg
                        Me.SetFrmWeChatMainlblState()
                    Case InstallState.UnSucceed
                        Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreFailure")
                        Me.SetFrmWeChatMainlblState()
                    Case InstallState.CancelInstall


                    Case InstallState.AlreadyInstall


                    Case InstallState.AllInstallCompleted
                        'Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreSucceed")
                        Me.LoadUsers()
                    Case InstallState.BackupRecord
                        Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("App.Cell.BackupRecord"), args.TransferProgress) '"正在备份记录"
                        Me.SetFrmWeChatMainlblState()
                End Select
            Catch ex As Exception

            End Try
        End If
    End Sub

#End Region

#Region "---  窗体事件  ---"

    Private Sub btnWeCahtUsers_Click(sender As Object, e As EventArgs) Handles btnWeCahtUsers.Click
        Dim menu As New tbContextMenuStrip()

        Dim menuInfoItem As ToolStripMenuItem = Nothing

        For Each info As SocialUserInfo In Me.mHelper.ParaCache.ListUsers
            Dim tsmi As New ToolStripMenuItem()
            tsmi.Name = info.UserID
            tsmi.Text = info.UserName
            tsmi.Tag = info
            tsmi.Checked = IIf(Me.btnWeCahtUsers.Text = info.UserName, True, False)
            AddHandler tsmi.Click, AddressOf OnWechatUserMenu_Click

            menu.Items.Add(tsmi)
        Next

        AddHandler menu.Closed, AddressOf OnWechatUserMenu_Closed
        menu.Show(btnWeCahtUsers, New Point(0, btnWeCahtUsers.Height))
    End Sub

    Private Sub OnWechatUserMenu_Click(sender As Object, e As EventArgs)
        Dim tsmi As ToolStripMenuItem = sender
        If tsmi Is Nothing Then
            Return
        End If

        Dim user As SocialUserInfo = tsmi.Tag
        If user Is Nothing Then
            Return
        End If

        If user Is Me.btnWeCahtUsers.Tag Then
            Return
        End If

        Me.btnWeCahtUsers.Text = user.UserName
        Me.btnWeCahtUsers.Tag = user
        Me.SetPanelTimeFilterVisible(False)
        Me.ShowFindDataResultPanel(False)
        Me.LoadUsers()
        Me.SetPanelCountVisibleInvoke(False)
    End Sub

    Private Sub OnWechatUserMenu_Closed(sender As Object, e As ToolStripDropDownClosedEventArgs)
        Dim menu As tbContextMenuStrip = CType(sender, tbContextMenuStrip)
        RemoveHandler menu.Closed, AddressOf OnWechatUserMenu_Closed
    End Sub

    Private Sub txtSearchChat_SearchTypeClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchChat.SearchTypeClick
        Me.cmsSearch.Show(Me.txtSearchChat, New Point(10, Me.txtSearchChat.Height - 4))
    End Sub

    Private Sub txtSearchChat_MenuItemClick(sender As Object, e As EventArgs) Handles tsmiSearchAll.Click, tsmiSearchName.Click, tsmiSearchMsg.Click
        Dim tsmiItem As ToolStripMenuItem = sender

        Me.tsmiSearchAll.Checked = (tsmiItem.Name = Me.tsmiSearchAll.Name)
        Me.tsmiSearchName.Checked = (tsmiItem.Name = Me.tsmiSearchName.Name)
        Me.tsmiSearchMsg.Checked = (tsmiItem.Name = Me.tsmiSearchMsg.Name)
    End Sub

    Private Sub txtSearchChat_ClearClick(sender As Object, e As EventArgs) Handles txtSearchChat.ClearClick
        Me.NewSearch()
    End Sub

    Private Sub txtSearchChat_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtSearchChat.KeyDown
        If e.KeyCode = Keys.Back OrElse e.KeyCode = Keys.Delete Then
            If txtSearchChat.Text.Length - 1 <= 0 Then
                txtSearchChat.Text = String.Empty
                txtSearchChat_ClearClick(Nothing, Nothing)
            End If
        End If

        If e.KeyCode <> Keys.Enter Then
            Return
        End If

        Me.NewSearch()
    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.ResetShowAndFilterType()

        Me.LoadUsers(True)
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Me.NewSearch()
    End Sub

    Private Sub btnReCheck_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReCheck.Click, btnReCheckEx.Click, btnReCheckEx1.Click
        Me.LoadUsers(True)
    End Sub

    Private Sub btnNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.LoadUsers()
        IniSetting.SetShowWeixinWelcome()
    End Sub

    Private Sub lblDownloadWeixin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblDownloadWeixin.Click, btnDownloadWeixin.Click

#If IS_WECHAT Then
        Me.btnPreviousMenu_Click(sender, e)
#Else
        If Me.mABType = AloneBackupType.WeChat Then
            Dim strID As String = "414478124"
            WebSiteHelper.Instance.DownloadAppByIDEx(Me.mApplication, Me.mDevice, strID, "", "Weixin")
            Try
                If Me.mFrmWeChatMain IsNot Nothing Then
                    Me.mFrmWeChatMain.Close()
                End If
            Catch
            End Try

        ElseIf Me.mABType = AloneBackupType.WeChatSpare OrElse Me.mABType = AloneBackupType.WeChatVIPSpare Then
            Common.OpenExplorer("http://news.tongbu.com/93465.html")

        End If
        Try
            If Me.mFrmWeChatMain IsNot Nothing Then
                Me.mFrmWeChatMain.Close()
            Else
                Me.Close()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "lblDownloadWeixin_Click")
        End Try
#End If

    End Sub

    Private Sub lblDownloadWeixin_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblDownloadWeixin.Resize
        Me.btnDownloadWeixin.Location = New Point(Me.lblDownloadWeixin.Right + 2, Me.lblDownloadWeixin.Location.Y + 2)
    End Sub

    Private Sub btnRepair_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Dim frm As New frmWeixinRepair(Me.mApplication, Me.mDevice)
        frm.StartPosition = FormStartPosition.CenterParent
        frm.ShowDialog(Me)
    End Sub

    Private Sub btnExport_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs)
        'Me.btnRepair.Location = New Point(Me.btnExport.Right + 2, Me.btnRepair.Location.Y)
    End Sub

    Private Sub btnNewExport_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs)
        ' Me.btnRepair.Location = New Point(Me.btnNewExport.Right + 2, Me.btnRepair.Location.Y)
    End Sub

    Private Sub Label1_Click(sender As Object, e As EventArgs) Handles Label1.Click
        Common.JoinQQGroup(QQGroupNumber.Q587033524)
    End Sub

    Private Sub PictureBoxAD_MouseClick(sender As Object, e As MouseEventArgs) Handles PictureBoxAD.MouseClick
        Dim p As Point = e.Location
        If p.X > 590 AndAlso p.Y < 18 Then
            Me.PictureBoxAD.Visible = False
            IniSetting.SetCacheCloseADShow()
        Else
            If Me.mADIndex = 1 Then
                Common.OpenExplorer("https://at.umeng.com/9zqWTv?cid=480")
            ElseIf Me.mADIndex = 2 Then
                Common.OpenExplorer("https://at.umeng.com/05LLbu?cid=7030")
            End If
        End If

    End Sub

#End Region

#Region "---  与web交互  ---"

#If CEFSHARP Then
    Private Sub wbChat_Navigating(ByVal sender As System.Object, ByVal e As CefWebBrowserNavigatingEventArgs) Handles wbsChat.Navigating
#Else
    Private Sub wbChat_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs) Handles wbsChat.Navigating
#End If

        Dim url As String = e.Url.ToString
        'Debug.Print("微信weburl:" & url)

        If url.StartsWith("weixin://getnews", StringComparison.OrdinalIgnoreCase) Then
            '向前、向后获取消息
            Me.ExcuteOpration(WebOperation.GetMore, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://viewphoto", StringComparison.OrdinalIgnoreCase) Then
            '查看图片
            Me.ExcuteOpration(WebOperation.ViewPhoto, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://copy", StringComparison.OrdinalIgnoreCase) Then
            '复制文本
            Me.ExcuteOpration(WebOperation.CopyText, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportphoto", StringComparison.OrdinalIgnoreCase) Then
            '导出图片
            Me.ExcuteOpration(WebOperation.ExportPhoto, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportvoice", StringComparison.OrdinalIgnoreCase) Then
            '导出语音
            Me.ExcuteOpration(WebOperation.ExportAudio, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://playvoice", StringComparison.OrdinalIgnoreCase) Then
            '播放语音
            Me.ExcuteOpration(WebOperation.PlayAudio, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://getcontextnews", StringComparison.OrdinalIgnoreCase) Then
            '旧接口，已废弃
            Me.ExcuteOpration(WebOperation.ViewBeforeAndAfter, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase) Then
            '打开URL
            Me.ExcuteOpration(WebOperation.OpenUrl, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://nearbynewsreturn", StringComparison.OrdinalIgnoreCase) Then
            '在查看指定搜索消息的前后记录，返回搜索界面
            Me.ExcuteOpration(WebOperation.GoBack, url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://nearbynews", StringComparison.OrdinalIgnoreCase) Then
            '查看指定搜索消息的前后记录
            Me.ExcuteOpration(WebOperation.ViewBeforeAndAfter, url)
            e.Cancel = True

        End If
    End Sub

    Private Sub ExcuteOpration(ByVal op As WebOperation, ByVal strUrl As String)
        Try
            Dim isShow As Boolean = True
            If op = WebOperation.GetMore OrElse op = WebOperation.GoBack OrElse op = WebOperation.Normal Then
                isShow = False
            End If

            If isShow Then
                If Me.CheckChargeOverdue(isShow) OrElse String.IsNullOrEmpty(strUrl) Then
                    Return
                End If
            End If

            Select Case op
                Case WebOperation.OpenUrl
                    '打开RUL
                    Dim openUrl As String = Utility.GetParamValueFromQuery("url", strUrl)
                    If String.IsNullOrEmpty(openUrl) Then
                        openUrl = strUrl
                    End If
                    Common.OpenExplorer(openUrl)

                Case WebOperation.CopyText
                    '复制文本信息
                    Me.CopyText(strUrl)

                Case WebOperation.ExportAudio, WebOperation.ExportPhoto
                    '导出图片、视频、语音
                    Dim eType As WebOperation = ExportType.ExportOnlyPhoto

                    If op = WebOperation.ExportAudio Then
                        eType = ExportType.ExportOnlyAudio
                    End If

                    Me.ExportMeia(strUrl, eType)

                Case WebOperation.PlayAudio
                    '播放语音
                    Me.PlayAudio(strUrl)

                Case WebOperation.GoBack
                    '返回上次搜索内容
                    Me.GetMore(strUrl, FindDataType.GoBack)

                Case WebOperation.ViewBeforeAndAfter
                    '查看前后消息
                    Me.GetMore(strUrl, FindDataType.LoadCurrent)

                Case WebOperation.GetMore
                    '向前、向后获取更多消息
                    Me.GetMore(strUrl)

                Case WebOperation.ViewPhoto
                    '查看大图
                    Me.ViewPhoto(strUrl)

            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExcuteOpration")
        End Try
    End Sub

    Private Function FindCurrentWechatInfo(ByVal strUrl As String)
        Dim info As SociaChatBase = Nothing

        Dim strUserName As String = Utility.GetParamValueFromQuery("friend", strUrl)

        Dim strKey As String = Utility.GetParamValueFromQuery("id", strUrl) '2019/2/12 17:12:31<80
        Dim arr As String() = Nothing
        If strKey.Contains("<") Then
            arr = strKey.Split("<")
        End If

        Dim strID As String = strKey
        Dim strTime As String = strKey

        If arr.Length = 2 Then
            strID = arr(1)
            strTime = arr(0)
        End If

        Dim strRender As String = Utility.GetParamValueFromQuery("render", strUrl)

        info = Me.mHelper.GetMsgByMesLocalID(strUserName, strID)
        Return info
    End Function

    Private Sub GetMore(ByVal strUrl As String, Optional ByVal type As FindDataType = FindDataType.Normal)
        Dim strCreateTime As String = Utility.GetParamValueFromQuery("id", strUrl)
        Dim strRender As String = Utility.GetParamValueFromQuery("render", strUrl)
        Dim strCreateID As String = String.Empty

        If strCreateTime.Contains("<") Then
            Dim arrValue As String() = strCreateTime.Split("<"c)
            strCreateTime = arrValue(0)
            strCreateID = arrValue(1)
        End If

        Select Case strRender
            Case "bottom"
                type = FindDataType.LoadNext
                Exit Select
            Case "top"
                type = FindDataType.LoadPrev
                Exit Select
        End Select

        Dim dtCreateTime As DateTime = DateTime.MinValue
        Dim dtCreate As DateTime = DateTime.MinValue
        If DateTime.TryParse(strCreateTime, dtCreate) Then dtCreateTime = dtCreate

        If type = FindDataType.GoBack Then
            Me.mLastCreateTime = dtCreateTime
            Me.mLastCreateID = strCreateID
            Me.LoadChat(FindDataType.Search, dtCreateTime, strCreateID)
        Else
            Me.LoadChat(type, dtCreateTime, strCreateID)
        End If
    End Sub

    '展示原图
    Private Sub ViewPhoto(ByVal strUrl As String)
        Dim chat As SociaChatBase = Me.FindCurrentWechatInfo(strUrl)
        If chat Is Nothing Then
            Return
        End If

        '视频不作处理
        If chat.SCType = SociaChatType.Video OrElse chat.SCType = SociaChatType.SmallVideo Then
            Me.ChangeStateText(Me.Language.GetString("Weixin.Lable.SmallVideo"))         '"小视频"
            Return
        End If

        Dim currentRow As DataGridViewRow = Me.GetCurrentRow()
        If currentRow Is Nothing Then
            Return
        End If

        Dim friendInfo As SocialFriendInfoBase = currentRow.Tag

        Dim para As IMExportPara = Me.CheckExport(IMParaType.Load)
        If para Is Nothing Then
            Return
        End If

        para.ExportType = ExportType.ExportOnlyPhoto

        '下载图片
        Me.mHelper.DownloadWechatFiles(para, String.Empty, friendInfo.UsrName, chat)

        'If Not File.Exists(chat.FilePathOnPC) Then
        '    Return
        'End If

        '获取大图
        Dim imgPath As String = chat.FilePathOnPC

        '没有可供预览的大图
        If File.Exists(imgPath) Then
            '展示图片
            Dim showPicFrm As New tbImageViewForm()
            showPicFrm.SrcPicture = Utility.GetImageFormFile(imgPath)
            showPicFrm.Icon = My.Resources.iTong
            showPicFrm.ViewType = ViewType.FromPC
            showPicFrm.ShowListView = False
            showPicFrm.HideButton = ToolBarButtonType.Main Or ToolBarButtonType.Delete Or ToolBarButtonType.WeiBo Or ToolBarButtonType.Prev Or ToolBarButtonType.Next
            showPicFrm.ShowDialog()
        Else
            Me.ChangeStateText(Me.Language.GetString("Weixin.Message.NoLargeImage")) '"没有可供预览的大图"
        End If
    End Sub

    '播放语音
    Private Sub PlayAudio(ByVal strUrl As String)
        Dim chat As SociaChatBase = Me.FindCurrentWechatInfo(strUrl)
        If chat Is Nothing Then
            Return
        End If

        Dim currentRow As DataGridViewRow = Me.GetCurrentRow()
        If currentRow Is Nothing Then
            Return
        End If

        Dim friendInfo As SocialFriendInfoBase = currentRow.Tag

        Dim para As IMExportPara = Me.CheckExport(IMParaType.Load)
        para.ExportType = ExportType.ExportOnlyAudio

        '下载语音
        Me.mHelper.DownloadWechatFiles(para, String.Empty, friendInfo.UsrName, chat)

        If Not File.Exists(chat.FilePathOnPC) Then
            Return
        End If

        'win7下，低于一秒的不去播放
        If Environment.OSVersion.Version >= New Version("6.1") Then
            Dim singleLength As Single = Me.mHelper.GetAudioDuration(chat.StrMessage, False)  '获取时长的最大整数
            If singleLength <= 1.3 Then
                Return
            End If
        End If

        '获取语音路径（wav格式）
        Dim audioPath As String = chat.FilePathOnPC

        If File.Exists(audioPath) Then
            Dim blnNeedPlay As Boolean = True

            If Me.mPlayer.PlayState = WMPPlayState.wmppsPlaying Then
                Dim strFilePlaying As String = Me.mPlayer.CurrentMedia.sourceURL

                'web页面暂停之前的
                If String.Compare(strFilePlaying, audioPath, True) = 0 Then
                    blnNeedPlay = False         '如果正在播放，播放的是同一首歌，则暂停
                Else
                    blnNeedPlay = True          '如果是新的语音，则播放新的语音
                End If

                Me.mPlayer.Pause()
                Me.ShowAudioWebView(strFilePlaying, False)
            End If

            If blnNeedPlay Then
                'Debug.Print("播放")
                Me.mPlayer.GetUserPlaylist(Me.Name).Clear()
                Me.mPlayer.GetUserPlaylist(Me.Name).Add(audioPath)
                'Me.mPlayer.GetUserPlaylist(Me.Name).SetItem(0, strWavFile)
                Me.mPlayer.CurrentUserPlaylist = Me.mPlayer.GetUserPlaylist(Me.Name)
                Me.mPlayer.Play()
                Me.ShowAudioWebView(chat.IntID.ToString(), True)
                'Me.ShowAudioWebView(String.Format("{0}<{1}", chat.DTCreateTime.ToString(), chat.IntID), True)
            End If
        Else
            Dim strError As String = Me.Language.GetString("Weixin.Message.PlayFail") & " " & String.Format(Me.Language.GetString("Download.Message.ContactMe"), Utility.TongbuContact, Utility.TongbuTel)     '"如有疑问，请联系客服QQ：{0}    电话：{1}"
            Me.ChangeStateText(strError) '"播放语音失败！"
        End If
    End Sub


    '导出媒体文件
    Private Sub ExportMeia(ByVal strUrl As String, ByVal eType As ExportType)
        Dim chat As SociaChatBase = Me.FindCurrentWechatInfo(strUrl)
        If chat Is Nothing Then
            Return
        End If

        Dim currentRow As DataGridViewRow = Me.GetCurrentRow()
        If currentRow Is Nothing Then
            Return
        End If

        Dim friendInfo As SocialFriendInfoBase = currentRow.Tag

        Dim para As IMExportPara = Me.CheckExport()
        If para Is Nothing Then
            Return
        End If

        If chat.SCType = SociaChatType.Video OrElse chat.SCType = SociaChatType.SmallVideo Then
            para.ExportType = ExportType.ExportOnlyVideo
        Else
            para.ExportType = eType
        End If

        Me.mHelper.DownloadWechatFiles(para, chat.StrNickName, friendInfo.UsrName, chat)

        If File.Exists(chat.FilePathOnPC) Then
            Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(chat.FilePathOnPC))
            Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！

            Me.ChangeStateText(strText)
        Else
            If chat.SCType = SociaChatType.Video OrElse chat.SCType = SociaChatType.SmallVideo Then
                '"无法导出小视频，请到微信点击查看后再导出。"
                Me.ChangeStateText(Me.Language.GetString("Weixin.Message.CanotExportSmallVideo"))
                Return
            End If
        End If
    End Sub

    Private Sub CopyText(ByVal strUrl As String)
        Dim chat As SociaChatBase = Me.FindCurrentWechatInfo(strUrl)
        If chat Is Nothing Then
            Return
        End If

        Dim smsNode As iTong.CoreModule.IMHtmlNode = Me.mHelper.CreateHtmlNode(chat, "", False)

        Try
            'Clipboard.SetText(chat.MessageFormat)
            Clipboard.SetText(smsNode.SmsContent)
            Me.ChangeStateText(Me.Language.GetString("Welcome.Message.CopySucceed"))
        Catch
            Me.ChangeStateText(Me.Language.GetString("Weixin.Message.CopyFail")) ' "复制失败！"
        End Try
    End Sub

    '播放语音停止事件
    Private Sub OnPlayStateChange(ByVal sender As Object, ByVal e As MediaPlayStateChangedEventArgs)
        If e.NewPlayState = WMPPlayState.wmppsStopped Then
            If Me.mPlayer.CurrentMedia IsNot Nothing Then
                Dim strFilePlaying As String = Me.mPlayer.CurrentMedia.sourceURL
                Me.mPlayer.GetUserPlaylist(Me.Name).Remove(strFilePlaying)
                Me.ShowAudioWebView(strFilePlaying, False)
            End If
        End If
    End Sub

#End Region

#Region "---  界面展示  ---"

    Private Sub SetLinkmanMsg()
        Try
            Dim countVisible As Integer = 0
            Dim countSelect As Integer = 0

            For Each row As DataGridViewRow In Me.dgvFriend.Rows
                If row.Visible Then
                    countVisible += 1

                    If row.Selected Then
                        countSelect += 1
                    End If
                End If
            Next

            Me.mSelectAllClick = True
            Me.cbxSelectAll.Checked = (countSelect = countVisible)
            Me.mSelectAllClick = False

            Me.lblLinkmanMsg.Text = String.Format(Me.Language.GetString("WeChat.Lable.LinkmanMsg"), countSelect, countVisible) '已选择 {0} 项，全部 {1} 项
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetLinkmanMsg")
        End Try
    End Sub

    Private Function GetLastChatTime(ByVal LastChatTime As DateTime) As String
        Dim strTime As String = String.Empty
        Dim ts As TimeSpan = Now.Date.Subtract(LastChatTime.Date) '只需比较日期就好
        Try
            Select Case ts.Days
                Case 0
                    strTime = LastChatTime.ToString("HH:mm")
                Case 1
                    strTime = Me.Language.GetString("Note.Label.Yesterday") '"昨天"
                Case Else
                    Dim strYear As String = LastChatTime.Year.ToString().Substring(2, 2)
                    Dim strMonth As String = LastChatTime.Month.ToString()
                    Dim strDay As String = LastChatTime.Day.ToString()
                    strTime = String.Format("{0}-{1}-{2}", strYear, strMonth, strDay)
            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetLastChatTime")
        End Try
        Return strTime
    End Function

    Private Sub AloneBackupsHelper_AloneBackupBackupHandler(sender As Object, args As AloneBackupsEventArgs)
        If args.Statue = BackupRestoreErrorType.ConvertBackup Then
            Me.lblLoading.Text = Me.Language.GetString("Backup.Message.Backuping") '"正在进行整机备份转换为微信备份，请稍候..."
        Else
            Me.lblLoading.Text = String.Format("{0}{1}%", Me.Language.GetString("Weixin.Lable.LoadData"), args.Progress) '"正在获取微信数据"

            If Me.mIsBtnBackupWaiting Then
                Me.lblState.Text = String.Format("{0}{1}%", Me.Language.GetString("Backup.Message.BeinBackuping"), args.Progress) ' "正在备份中..."
                Me.SetFrmWeChatMainlblState()
            End If

        End If
        Me.mBackupRestoreErrorType = args.Statue
    End Sub

    Private Delegate Sub SetViewStyleHandler(ByVal style As ViewStyle)
    Private Sub SetViewStyle(ByVal style As ViewStyle)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewStyleHandler(AddressOf SetViewStyle), style)
        Else
            Me.tlpMain.SuspendLayout()
            Select Case style
                Case ViewStyle.DescriptionPwdBackup
                    If Me.tlpMain.ColumnStyles(0).Width <> 100 Then
                        Me.tlpMain.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(0).Width = 100
                        Me.tlpMain.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(1).Width = 0
                        Me.tlpMain.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(2).Width = 0
                        Me.tlpMain.ColumnStyles(3).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(3).Width = 0

                        Me.pnlDescription1.Visible = True
                        Me.pnlDescription.Visible = False
                        Me.pnlLoading.Visible = False
                    End If

                Case ViewStyle.Description
                    If Me.tlpMain.ColumnStyles(1).Width <> 100 Then
                        Me.tlpMain.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(0).Width = 0
                        Me.tlpMain.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(1).Width = 100
                        Me.tlpMain.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(2).Width = 0
                        Me.tlpMain.ColumnStyles(3).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(3).Width = 0

                        Me.pnlDescription1.Visible = False
                        Me.pnlDescription.Visible = True
                        Me.pnlLoading.Visible = False
                    End If

                Case ViewStyle.Loading
                    If Me.tlpMain.ColumnStyles(2).Width <> 100 Then
                        Me.tlpMain.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(0).Width = 0
                        Me.tlpMain.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(1).Width = 0
                        Me.tlpMain.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(2).Width = 100
                        Me.tlpMain.ColumnStyles(3).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(3).Width = 0

                        Me.pnlDescription1.Visible = False
                        Me.pnlDescription.Visible = False
                        Me.pnlLoading.Visible = True 'String.IsNullOrEmpty(Me.mHelper.ParaInstance.BackupFolder)
                    End If

                Case ViewStyle.Home
                    If Me.tlpMain.ColumnStyles(3).Width <> 100 Then
                        Me.tlpMain.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(0).Width = 0
                        Me.tlpMain.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(1).Width = 0
                        Me.tlpMain.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(2).Width = 0
                        Me.tlpMain.ColumnStyles(3).SizeType = SizeType.Percent
                        Me.tlpMain.ColumnStyles(3).Width = 100

                        Me.pnlDescription1.Visible = False
                        Me.pnlDescription.Visible = False
                        Me.pnlLoading.Visible = False
                    End If



            End Select
            Me.tlpMain.ResumeLayout()
            Application.DoEvents()
        End If
    End Sub

    Private Delegate Function AddDictToDataGridViewHandler(ByVal dict As Dictionary(Of String, SocialFriendInfoBase))
    Private Function AddDictToDataGridView(ByVal dict As Dictionary(Of String, SocialFriendInfoBase))
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New AddDictToDataGridViewHandler(AddressOf AddDictToDataGridView), dict)
            Else
                Me.dgvFriend.SuspendLayout()

                Me.dgvFriend.Rows.Clear()

                For Each item As SocialFriendInfoBase In dict.Values
                    If String.IsNullOrEmpty(item.StrLastSendMsg) Then   'item.DTLastSendTime = DateTime.MinValue AndAlso item.IsFindDate = False OrElse 
                        Continue For
                    End If

                    ''----过滤 公众号----
                    'If Not Me.mIsShowPublic Then
                    '    'If item.Type = 2 OrElse item.Type = 3 OrElse item.Type = 7 OrElse item.Type = 11 OrElse item.Type = 67 OrElse item.Type = 259 OrElse item.Type = 65539 Then
                    '    '    Continue For 
                    '    'End If
                    'End If
                    ''-------------------

                    Me.InsertDataGridRow(item)
                    'cellName.ShowWechatMessageLock = (item.IsFindDate AndAlso Not IniSetting.IsWeChatShare() AndAlso Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None)
                Next

                Me.dgvFriend.ResumeLayout()

                Me.SetLinkmanMsg()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddDictToDataGridView")
        End Try
    End Function

    Private Function InsertDataGridRow(ByVal item As SocialFriendInfoBase) As DataGridViewRow
        Dim row As DataGridViewRow = Nothing
        Try
            Dim strName As String = item.UsrName
            Dim strMD5 As String = Common.GetMd5Hex(strName)

            If Not String.IsNullOrEmpty(item.NoteName) Then
                strName = item.NoteName
            ElseIf Not String.IsNullOrEmpty(item.NickName) Then
                strName = item.NickName
            End If

            Dim aryParameters() As Object = New Object() {False, _
                                                           "", _
                                                           strName & vbCrLf & Me.OperateLastChatText(item.StrLastSendMsg), _
                                                           Me.GetLastChatTime(item.DTLastSendTime) & vbCrLf & " ", _
                                                           item.IsFindDate, _
                                                           strMD5}

            Dim rowIndex As Integer = Me.dgvFriend.Rows.Add(aryParameters)

            row = Me.dgvFriend.Rows(rowIndex)
            row.Tag = item

            Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")

            cellName.tbIconShadow = True
            cellName.tbIconSize = Me.mAppIconSize
            cellName.tbIcon = Me.mDefaultHeadIcon
            cellName.tbForeColor = IIf(item.IsFindDate, Color.FromArgb(255, 92, 92), Color.FromArgb(0, 0, 0))
            cellName.tbSelectionForeColor = cellName.tbForeColor

        Catch ex As Exception
            Common.LogException(ex.ToString(), "InsertDataGridRow")
        End Try
        Return row
    End Function

    '截取最后一条消息（展示在界面）
    Private Function OperateLastChatText(ByVal strLastChatText As String) As String
        If strLastChatText.Length = 0 Then
            Return " "
        End If

        Try
            '截取
            If Me.mGraphic Is Nothing Then
                Me.mGraphic = Me.CreateGraphics()
            End If

            Dim strResult As String = strLastChatText
            Dim strFormat As StringFormat = GuiHelper.CreateStringFormat(ContentAlignment.MiddleLeft, False)
            Dim charactersFitted As Integer
            Dim linesFilled As Integer
            Dim sizeCommment As SizeF = Me.mGraphic.MeasureString(strLastChatText, New Font("宋体", 9, FontStyle.Regular), New SizeF(100, 15.2), strFormat, charactersFitted, linesFilled)

            If charactersFitted <> strLastChatText.Length AndAlso strLastChatText.Length > 3 Then
                strResult = strLastChatText.Substring(0, IIf(charactersFitted - 3 <= 0, 0, charactersFitted - 3)) & "..."
            End If

            Return strResult
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OperateLastChatText")
            Return strLastChatText
        End Try
    End Function

    Private Delegate Sub ChangeStateHandler(ByVal Text As String)
    Private Sub ChangeStateText(ByVal Text As String)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ChangeStateHandler(AddressOf ChangeStateText), Text)
            Else
                '如果正在备份的时候就不刷新状态
                If mThrBackup Is Nothing OrElse mThrBackup.ThreadState = ThreadState.Stopped Then
                    Me.lblState.Text = Text
                    Me.SetFrmWeChatMainlblState()
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeStateText")
        End Try
    End Sub

    Private Delegate Sub ShowSystemInfoOnPCHandler(ByVal blnVisible As Boolean, ByVal strText As String, ByVal isBackupFailure As Boolean)
    Private Sub ShowSystemInfoOnPC(ByVal blnVisible As Boolean, ByVal strText As String, Optional ByVal isBackupFailure As Boolean = False)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowSystemInfoOnPCHandler(AddressOf ShowSystemInfoOnPC), blnVisible, strText, isBackupFailure)
        Else
            'Me.pnlInfo.Visible = blnVisible
            'Me.lblInfo.Text = strText
            RemoveHandler Me.lblSystemInfo.Click, AddressOf lblSystemInfo_Click
            If isBackupFailure Then
                Me.lblSystemInfo.ForeColor = Color.FromArgb(5, 152, 242)
                AddHandler Me.lblSystemInfo.Click, AddressOf lblSystemInfo_Click
                Me.lblSystemInfo.Cursor = Cursors.Hand
            Else
                Me.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106)
                Me.lblSystemInfo.Cursor = Cursors.Default
            End If
            Me.lblSystemInfo.Visible = blnVisible
            If Me.lblSystemInfo.Visible Then
                Me.lblSystemInfo.BringToFront()
            End If

            Me.lblSystemInfo.Text = strText

            'Me.PictureBoxAD.Visible = blnVisible

            If Me.PictureBoxAD.Visible Then
                Me.PictureBoxAD.Visible = Not IniSetting.GetCacheCloseADShow()
            End If

            If blnVisible Then
                Me.pnlLoadData.Visible = False
            End If
        End If
    End Sub

#End Region

#Region "---  LoadFriend  ---"

    Private Sub LoadUsers(Optional ByVal blnRefresh As Boolean = False)
        If Me.bgwLoadUsers.IsBusy Then
            Return
        End If

        Me.SetViewStyle(ViewStyle.Loading)
        Me.bgwLoadUsers.RunWorkerAsync(blnRefresh)
    End Sub

    Private Sub bgwLoadUsers_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwLoadUsers.DoWork
        Dim state As IMState = IMState.None

        Try
            Dim blnRefresh As Boolean = e.Argument

            If blnRefresh OrElse Not Me.mHelper.IsLoaded Then

                '初始化缓存变量等数据
                state = Me.mHelper.InitCache()
                If state <> IMState.Success Then
                    GoTo DO_EXIT
                End If

                '加载帐号列表
                If Me.mHelper.LoadUsers() Then
                    Me.SetCurrentWechatUser()
                Else
                    Me.SetCurrentWechatUser()
                    state = IMState.LoadUserFail
                    GoTo DO_EXIT
                End If
            End If

            If Me.mHelper.LoadFriends(Me.btnWeCahtUsers.Tag) Then
                state = IMState.Success
            Else
                state = IMState.LoadFriendFail
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadFriend_DoWork")
        End Try

DO_EXIT:
        e.Result = state
    End Sub

    Private Sub bgwLoadUsers_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwLoadUsers.RunWorkerCompleted
        Try
            Dim state As IMState = CType(e.Result, IMState)

            Me.SetLoading(False)

            If state <> IMState.Success Then
                Me.SetStateMsg(state)
                Return
            End If

            Me.SetViewStyle(ViewStyle.Home)

            '初始化WEB相关资源文件解压路径
            Me.wbsChat.Navigate(ChatWebPage.NewWebPagePath)

            Me.ShowFriend()

            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Lable.InfoToSelect")) '"查看消息，请选中左侧联系人"

            Me.StartLoadHeadIcon()

            '---------删除找回---------
            If IniSetting.GetShowWeChatIsDoFindDate() Then
                Dim isIgnoreNumber As Boolean = False
                If tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Button.AutoFindDataEx"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, Me.Language.GetString("App.Button.IgnoreNumber"), isIgnoreNumber, New String() {Me.Language.GetString("Weixin.Button.Recoverdata"), Me.Language.GetString("Weixin.Button.WantExport")}, Me.Language.GetString("Weixin.Button.Reinstall"), tbMessageBox.ToolTipType.Hint) = Windows.Forms.DialogResult.OK Then
                    IniSetting.SetWeChatIsDoFindDate(True)
                Else
                    IniSetting.SetWeChatIsDoFindDate(False)
                End If
                IniSetting.SetShowWeChatIsDoFindDate(isIgnoreNumber)
            End If

            If IniSetting.GetWeChatIsDoFindDate() Then
                Me.StartFindData()
            End If
            '--------------------------



        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadFriend_RunWorkerCompleted")
        End Try
    End Sub

    Private Sub ShowFriend()
        Dim dictFriend As New Dictionary(Of String, SocialFriendInfoBase)
        If Me.mAppType = IMAppType.WeChat Then
            For Each pair As KeyValuePair(Of String, WeChatFriendInfo) In Me.mHelper.ParaCache.DictFriendInfoWechat
                dictFriend(pair.Key) = pair.Value
            Next
        ElseIf Me.mAppType = IMAppType.QQ Then
            For Each pair As KeyValuePair(Of String, QQFriendInfo) In Me.mHelper.ParaCache.DictFriendInfoQQ
                dictFriend(pair.Key) = pair.Value
            Next
        End If

        Me.AddDictToDataGridView(dictFriend)
    End Sub

    Private Delegate Sub ShowStateInfoHandler(ByVal state As IMState)
    Private Sub SetStateMsg(ByVal state As IMState)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowStateInfoHandler(AddressOf SetStateMsg), state)
        Else
            Select Case state
                Case IMState.Loading
                    If Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber >= 830 AndAlso Not Me.mHelper.ParaCache.UIFileSharingEnabled Then
                        Me.lblLoadingDescription.Visible = True
                        Me.picNoLockScreen.Visible = True
                        Me.lblLoadingDescription.Text = Me.Language.GetString("Weixin.Lable.LoadDataDescription")           '"8.3及以上固件需要较长时间，请务必耐心等待"
                    Else
                        Me.lblLoadingDescription.Visible = False
                        Me.picNoLockScreen.Visible = False
                        Me.lblLoadingDescription.Text = ""
                    End If

                    Me.lblLoadingDescription.Location = New Point(Me.picLoading.Left, Me.lblLoading.Bottom + 3)
                    Me.lblLoading.Text = Me.Language.GetString("Weixin.Lable.LoadData") '"正在获取微信数据"
                    Me.SetViewStyle(ViewStyle.Loading)

                Case IMState.AppNoInstall
                    If Me.mABType = AloneBackupType.WeChat Then
                        Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Lable.Description") '"微信是一款手机通信软件，支持通过手机网络发送语音短信、视频、图片和文字，可以单聊及群聊，还能根据地理位置找到附近的人，带给朋友们全新的移动沟通体验。"
                    ElseIf Me.mABType = AloneBackupType.WeChatSpare OrElse Me.mABType = AloneBackupType.WeChatVIPSpare Then
                        Me.lblWeixinDescription.Text = Me.Language.GetString("WeChat.Message.CoexisNote") '"微信分身版（共存版，多开版） 可以在同一台手机安装多个微信，同时登入多个不同的微信帐号."
                    End If

                    If Me.mABType = AloneBackupType.WeChatSpare Then
                        Me.pnlTip.Visible = True
                    Else
                        Me.pnlTip.Visible = False
                    End If

                    Me.lblDownloadWeixin.Visible = True
#If IS_WECHAT Then
                    Me.lblDownloadWeixin.Text = Me.Language.GetString("Weixin.Button.Return") '返回上级
                    Me.btnDownloadWeixin.Visible = False
#Else
                    Me.lblDownloadWeixin.Text = Me.Language.GetString("Weixin.Lable.GetWeixin") '"立即免费获取微信"
                    Me.btnDownloadWeixin.Visible = True
#End If

                    Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Lable.NotInstall") '"尚未安装微信"
                    Me.picWeixinIcon.Visible = True
                    Me.picError.Visible = False
                    Me.btnReCheckEx.Visible = False
                    Me.btnMoreTutorials.Visible = False
                    Me.SetViewStyle(ViewStyle.Description)

                Case IMState.BackupDecryptFail
                    Me.lblWeixinDescription.Text = Me.Language.GetString("Backup.Message.ReadErrorUnlockPwd")    '"您使用了iTunes备份加密，请去掉密码后重试。"
                    Me.btnDownloadWeixin.Visible = False
                    Me.lblDownloadWeixin.Visible = False
                    Me.lblWeixinTitle.Text = Me.Language.GetString("App.Message.GetFailure") '"获取失败"
                    Me.picWeixinIcon.Visible = True
                    Me.picError.Visible = False
                    Me.btnReCheckEx.Visible = False
                    Me.btnMoreTutorials.Visible = False
                    Me.SetViewStyle(ViewStyle.Description)

                Case WeixinState.PwdBackup
                    Me.SetViewStyle(ViewStyle.DescriptionPwdBackup)

                Case IMState.NotLogin, IMState.LoadUserFail
                    Me.lblWeixinDescription.Text = Me.Language.GetString("App.Message.NotLogin") '"尚未登入微信,当前微信尚未登入过任何微信帐号。"
                    Me.btnDownloadWeixin.Visible = False
                    Me.lblDownloadWeixin.Visible = False
                    Me.lblWeixinTitle.Text = Me.Language.GetString("App.Message.GetFailure") '"获取失败"
                    Me.picWeixinIcon.Visible = True
                    Me.picError.Visible = False
                    Me.btnReCheckEx.Visible = False
                    Me.btnMoreTutorials.Visible = False
                    Me.SetViewStyle(ViewStyle.Description)

                Case Else
                    Dim sbMsg As New StringBuilder()
                    sbMsg.Append(Me.Language.GetString("Weixin.Lable.LoadDataFailed")) '"获取微信数据失败"

                    Dim strBackupFolder As String = ""
                    If Me.mBackupInfo IsNot Nothing Then
                        strBackupFolder = Me.mBackupInfo.FolderPath
                    End If

                    If strBackupFolder.Length > 0 Then
                        Dim strSdik As String = Path.GetPathRoot(strBackupFolder)
                        Dim intSdik As Long = Common.GetHardDiskSpace(strSdik)
                        If intSdik <= 100 Then
                            state = WeixinState.OutOfDiskSpace
                            sbMsg.AppendLine(strSdik & Me.Language.GetString("Common.Message.DiskSpaceNotEnough"))
                        End If
                    End If

                    Me.btnDownloadWeixin.Visible = False
                    Me.lblDownloadWeixin.Visible = False
                    Me.lblWeixinTitle.Text = sbMsg.ToString()
                    Me.picWeixinIcon.Visible = False
                    Me.btnReCheck.Visible = False
                    Me.btnMoreTutorials.Visible = False
                    'Me.lblWeixinDescription.AutoSize = True
                    Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Lable.iTunesBackup") '"请根据教程使用iTunes备份，在用助手查看。"
                    Me.picError.Visible = True
                    Me.btnOpenTutorial.Visible = True
                    Me.btnOpenTutorial.Location = New Point(Me.btnOpenTutorial.Left, Me.lblWeixinDescription.Top + 30)
                    Me.btnMoreTutorials.Location = New Point(Me.btnMoreTutorials.Left, Me.lblWeixinTitle.Top + 130)
                    Me.SetViewStyle(ViewStyle.Description)

                    If ServerIniSetting.GetShowWechatBackupTool Then
                        ''获取微信数据失败
                        ''可以试试【微信备份助手】，可以在不用数据线的情况下导出微信的消息。
                        ''是否需要了解？

                        Dim strMsg As String = String.Format("{1}{0}{2}{3}", _
                                                               vbCrLf, _
                                                               Me.Language.GetString("Weixin.Lable.LoadDataFailed"), _
                                                               Me.Language.GetString("WechatAssistant.Message.TryUseWechatAssistantEx"), _
                                                               Me.Language.GetString("WechatAssistant.Message.TryUseWechatAssistantEx1"))

                        If tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2, "", False, New String() {Me.Language.GetString("WechatAssistant.Button.Specific"), Me.Language.GetString("WechatAssistant.Button.iWechatAssistant")}) = Windows.Forms.DialogResult.Yes Then
                            Common.OpenExplorer("http://news.tongbu.com/92201.html")
                        Else
                            Common.OpenExplorer(ServerIniSetting.GetUrlWechatBackupTooliOSBackupFailed)
                        End If

                    End If

            End Select
        End If
    End Sub

    Public Shared Function SetPwdBackup(ByVal owner As Form, ByVal device As iPhoneDevice, ByVal lang As LanguageInterface) As Boolean
        Dim blnReturn As Boolean = True
DORetry:
        If device.mb2_CheckEncrypt() Then
            Dim strPwd As String = ""
            Dim res As DialogResult = tbInputBox.Show(owner, lang.GetString("Backup.Message.TypeInBackupPwd"), lang.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, MessageBoxDefaultButton.Button1, strPwd, True, My.Resources.backup_password) ', lang.GetString("Backup.Message.iTunesTutorial"), "http://news.tongbu.com/92502.html"“iTunes备份密码相关教程"’
            If res = Windows.Forms.DialogResult.Cancel Then
                blnReturn = False
            Else
                If Not device.mb2_ChangePW("", strPwd) Then
                    tbMessageBox.Show(owner, lang.GetString("Backup.Message.PwdIsError"), lang.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                    GoTo DORetry
                End If
            End If
        End If
        Return blnReturn
    End Function


    Private Delegate Sub SetlblStateHandler(ByVal strText As String)
    Private Sub SetlblState(ByVal strText As String)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetlblStateHandler(AddressOf SetlblState), strText)
            Else
                Me.lblState.Text = strText
            End If
        Catch
        End Try
    End Sub

    Private Sub OnDecryptProgressChanged(sender As Object, args As BackupDecryptArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupDecryptArgs)(AddressOf OnDecryptProgressChanged), sender, args)
        Else
            Me.lblState.Text = args.strMsg
        End If
    End Sub

    Private Sub OnDecryptInputPwd(sender As Object, e As BackupPwdArgs)
        Dim strTutorial As String = ""
        Dim strTutorialUrl As String = ""
        If ServerIniSetting.GetShowWechatBackupTool Then
            strTutorial = Me.Language.GetString("WechatAssistant.Message.ReadInAnotherWay")  '"忘记密码？换一个方式读取。"
            strTutorialUrl = ServerIniSetting.GetUrlWechatBackupTooliOSBackupFailed
        End If

        If tbInputBox.Show(Me, Me.Language.GetString("Backup.Message.TypeInBackupPwd"), _
                                      Me.Language.GetString("Common.Info"), _
                                      MessageBoxButtons.OK, _
                                      MessageBoxIcon.None, _
                                      MessageBoxDefaultButton.Button1, _
                                      e.Password, _
                                      True, _
                                      My.Resources.backup_password, _
                                      strTutorial, _
                                      strTutorialUrl) = Windows.Forms.DialogResult.OK Then ' , Me.Language.GetString("Backup.Message.iTunesTutorial"), "http://news.tongbu.com/92502.html""iTunes备份密码相关教程"’

        End If
    End Sub

    Private Sub SetCurrentWechatUser()
        If Me.InvokeRequired Then
            Me.Invoke(New ThreadStart(AddressOf Me.SetCurrentWechatUser))
        Else
            If Me.mHelper.ParaCache.ListUsers.Count = 0 Then
                Me.btnWeCahtUsers.Text = IIf(Me.mHelper.ParaInstance.AppType = IMAppType.WeChat, "未取到微信帐号", "未取到QQ帐号")
            Else
                Dim info As SocialUserInfo = Me.mHelper.ParaCache.ListUsers(0)
                Me.btnWeCahtUsers.Text = IIf(String.IsNullOrEmpty(info.UserName), info.UserID, info.UserName)
                Me.btnWeCahtUsers.Tag = info
                Me.btnWeCahtUsers.Enabled = True
            End If

        End If
    End Sub

#End Region

#Region "---  LoadChat  ---"


    Private Function GetCurrentRow() As DataGridViewRow
        Dim row As DataGridViewRow = Nothing

        If Me.dgvFriend.SelectedRows.Count > 0 Then
            row = Me.dgvFriend.SelectedRows(Me.dgvFriend.SelectedRows.Count - 1)
        End If

        Return row
    End Function

    Private Function GetSearchDataType() As SearchDataType
        Dim type As SearchDataType = SearchDataType.All

        If Me.tsmiSearchAll.Checked Then
            type = SearchDataType.All
        ElseIf Me.tsmiSearchName.Checked Then
            type = SearchDataType.Contact
        Else
            type = SearchDataType.Record
        End If

        Return type
    End Function

    '2、获取endID以前50条消息（endCreateTime为-1时，获取最新50条信息）

    Private Sub LoadChat(ByVal type As FindDataType)
        LoadChat(type, DateTime.MinValue, String.Empty)
    End Sub
    Private Sub LoadChat(ByVal type As FindDataType, dtCreate As DateTime, endCreateID As String)
        If Me.mIsInsertRow Then
            Return
        End If
        If Utility.CheckThreadIsAlive(Me.mThreadLoadChat) Then
            Return
        End If

        Dim currentRow As DataGridViewRow = Me.GetCurrentRow()
        If currentRow Is Nothing Then
            Return
        End If

        Dim friendInfo As SocialFriendInfoBase = currentRow.Tag
        If friendInfo Is Nothing Then
            Return
        End If

        If Me.mLastSelectFriendInfo Is friendInfo And type = FindDataType.Normal Then
            If Not Me.mIsActiveCellback Then
                Return
            End If
        End If

        If String.IsNullOrEmpty(Me.txtSearchChat.Text.Trim()) And type = FindDataType.Normal Then
            Me.SetPanelTimeFilterVisible(False)
        End If

        Dim para As IMLoadPara = Me.CheckExport(IMParaType.Load)
        If para Is Nothing Then
            Return
        End If

        Me.mLastSelectFriendInfo = friendInfo
        Me.mLastLoadPara = para

        para.CurrentFriend = friendInfo
        para.CurrentSearchText = friendInfo.SearchText  'Me.txtSearchChat.Text.Trim()
        para.CurrentFindType = type
        para.CurrentSearchType = Me.GetSearchDataType()

        para.CurrentCreateTime = dtCreate
        para.CurrentCreateID = endCreateID

        Me.mThreadLoadChat = New Thread(New ParameterizedThreadStart(AddressOf LoadChatInThread))
        Me.mThreadLoadChat.IsBackground = True
        Me.mThreadLoadChat.SetApartmentState(ApartmentState.STA)
        Me.mThreadLoadChat.Start(para)

        If friendInfo.IsSearch AndAlso friendInfo.SearchText.Length = 0 Then
            SearchShowHint(Me.Language.GetString("Weixin.Message.SearchHimt")) '"当前对话中不包含含有关键字的记录，已自动为您展示全部消息"’
        Else
            Me.SetPnlSearchHint(False)
        End If
        'SearchShowHint(Me.Language.GetString("Weixin.Message.SearchHimt")) '"当前对话中不包含含有关键字的记录，已自动为您展示全部消息"’
    End Sub

    Private Sub SearchShowHint(ByVal strHint As String)
        Me.rtxtSearchHint.Clear()
        Me.rtxtSearchHint.Text = strHint
        Me.SetPnlSearchHint(True)
    End Sub

    Private Sub SetPnlSearchHint(ByVal isShow As Boolean)
        Me.mShowSearchTime = 0
        If isShow Then
            Me.tmrShowSearchCount.Start()
        Else
            Me.tmrShowSearchCount.Close()
        End If
        Try
            Me.SetPnlSearchHintVisible(isShow)
        Catch ex As Exception
            Me.SetPnlSearchHintVisible(isShow)
            Common.LogException(ex.ToString(), "SetPnlSearchHint")
        End Try
    End Sub

    Private Delegate Sub SetPnlSearchHintVisibleHandler(ByVal blnVisible As Boolean)
    Private Sub SetPnlSearchHintVisible(ByVal blnVisible As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetPnlSearchHintVisibleHandler(AddressOf SetPnlSearchHintVisible), blnVisible)
            Else
                Me.pnlSearchHint.Visible = blnVisible
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetPnlSearchHintVisible")
        End Try
    End Sub

    Private Sub LoadChatInThread(ByVal objPara As Object)
        Try
            Dim para As IMLoadPara = objPara
            If para Is Nothing Then
                Return
            End If

            Dim listMsg As Object = Me.mHelper.LoadMsg(para)

            'For Each msg As SociaChatBase In listMsg
            '    If msg.IsDelete Then
            '        iCountDelete += 1
            '    End If
            'Next

            If para.CurrentDeleteChatCount > 0 Then
                Dim strText = String.Format(Me.Language.GetString("Weixin.Lable.CurrentFindRecords"), para.CurrentDeleteChatCount) '"红色标识为找回数据，当前联系人找回[{0}]条"

                If Me.mChargeHelper.CheckIsVIP() Then
                    If Not Me.mChargeHelper.CheckDeviceRegistered(Me.mBackupInfo.Identifier) Then
                        strText = String.Format("{0}", strText, "「激活码授权已超过正常许可上限部分内容用*号代替」")
                    End If
                Else
                    strText = String.Format("{0}", strText, "「试用版预览数据部分内容用*号代替，点击右上角‘激活’」")
                End If

                Dim colSelect As Color = Color.FromArgb(166, 55, 0)
                Me.Selection(strText, Me.rtxtPrompt, "[", "]", colSelect, HorizontalAlignment.Left)
                Me.SetPanelCountVisible(True)
            Else
                Me.SetPanelCountVisible(False)
            End If

            'Common.Log("1.赋给全局变量，复制当前页时使用:" & listMsg.Count, "赋给全局变量，复制当前页时使用")

            If para.CurrentFindType <> FindDataType.LoadPrev AndAlso para.CurrentFindType <> FindDataType.LoadNext Then
                ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
            End If

            Me.ShowChatOnWebPage(para.CurrentMsgJson)

            '赋给全局变量，复制当前页时使用。
            'Common.Log("2.赋给全局变量，复制当前页时使用:" & listMsg.Count, "赋给全局变量，复制当前页时使用")

            Me.SetLoading(False)

            If Me.pnlTimeFilter.Visible AndAlso listMsg.Count = 0 AndAlso para.CurrentFindType = FindDataType.Normal Then
                Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.NoSearchResult")) '您选择的时间内没有消息，请您重新选择
            Else
                Me.ShowSystemInfoOnPC(False, String.Empty)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadChatInThread")
        End Try
    End Sub


#End Region

#Region "---  web界面展示  ---"

    'web页聊天信息的展示
    Private Sub ShowChatOnWebPage(ByVal strJson As String)
        '清理PC页面信息
        Me.ShowSystemInfoOnPC(False, "")

        ChatWebPage.ShowChatOnWebPage(Me.wbsChat, strJson)
        ChatWebPage.ShowMoreButton(Me.wbsChat, False)
    End Sub

    'web页语音播放与暂停的控制
    Private Sub ShowAudioWebView(ByVal strWavFile As String, ByVal blnPlay As Boolean)
        If String.IsNullOrEmpty(strWavFile) Then
            Return
        End If

        Dim strID As String = ""
        Try
            strID = Path.GetFileNameWithoutExtension(strWavFile)
        Catch ex As Exception
            strID = strWavFile
        End Try

        If strID.Contains("_") Then
            Dim arr As String() = strID.Split("_")
            If arr.Length >= 2 Then
                strID = arr(1)
            End If
        End If

        ChatWebPage.ControlVoiceView(Me.wbsChat, strID, blnPlay)
    End Sub

#End Region

#Region "---  搜索信息记录  ---"

    Private Delegate Sub SetLoadingHandler(blnVisible As Boolean)
    Private Sub SetLoading(blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetLoadingHandler(AddressOf Me.SetLoading), blnVisible)
        Else
            If Me.mIsFindDataing Then
                Return
            End If

            Me.pnlFindDataResult.Visible = Not blnVisible
            Me.pnlLoadData.Visible = blnVisible

            If blnVisible Then
                Me.pnlLoadData.BringToFront()
            Else
                Me.pnlLoadData.SendToBack()
            End If
        End If
        'Me.ShowSystemInfoOnPC(blnVisible, Me.Language.GetString("Weixin.Lable.InfoToSelect")) '"查看消息，请选中左侧联系人"
    End Sub

    Private Delegate Sub SetRowsVisibleHandler(dictRows As Dictionary(Of DataGridViewRow, Boolean))
    Private Sub SetRowsVisible(dictRows As Dictionary(Of DataGridViewRow, Boolean))
        If Me.dgvFriend.InvokeRequired Then
            Me.dgvFriend.Invoke(New SetRowsVisibleHandler(AddressOf Me.SetRowsVisible), dictRows)
        Else
            Me.dgvFriend.SuspendLayout()

            For Each pair As KeyValuePair(Of DataGridViewRow, Boolean) In dictRows
                pair.Key.Visible = pair.Value
            Next

            Me.dgvFriend.ResumeLayout()

            Me.SetLinkmanMsg()
            Application.DoEvents()
        End If
    End Sub

    Private Sub NewSearch()

        Dim para As IMLoadPara = Me.CheckExport(IMParaType.Search)
        If para Is Nothing Then
            Return
        End If

        para.CurrentSearchText = Me.txtSearchChat.Text.Trim()

        If Me.tsmiSearchAll.Checked Then
            para.CurrentSearchType = SearchDataType.All

        ElseIf Me.tsmiSearchName.Checked Then
            para.CurrentSearchType = SearchDataType.Contact

        Else
            para.CurrentSearchType = SearchDataType.Record

        End If

        para.CurrentFriendType = Me.mShowFriendType

        Utility.AbortThread(Me.mThreadNewSearch)

        Me.mThreadNewSearch = New Thread(New ParameterizedThreadStart(AddressOf NewSearchThread))
        Me.mThreadNewSearch.IsBackground = True
        Me.mThreadNewSearch.SetApartmentState(ApartmentState.STA)
        Me.mThreadNewSearch.Start(para)
    End Sub

    Private Sub NewSearchThread(ByVal objPara As Object)
        Dim iCountShow As Integer = 0
        Dim dictRows As New Dictionary(Of DataGridViewRow, Boolean)

        Try
            Dim para As IMLoadPara = objPara
            If para Is Nothing Then
                Return
            End If

            '显示Loading界面
            Me.SetLoading(True)

            '清空Web内容
            ChatWebPage.ClearChatOnWebPage(Me.wbsChat)

            Dim listRows As New List(Of DataGridViewRow)

            For Each row As DataGridViewRow In Me.dgvFriend.Rows
                listRows.Add(row)
            Next

            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Lable.InfoToSelect")) '"查看消息，请选中左侧联系人"
            Me.ShowFindDataResultPanel(False)

            '从数据库中查找满足搜索关键字的好友列表
            Dim dictFriends As New Dictionary(Of String, SocialFriendInfoBase)

            If Me.mAppType = IMAppType.WeChat Then
                Dim dictFriendFilter As New Dictionary(Of String, WeChatFriendInfo)

                '过滤不存在的对话框Table的
                For Each pair As KeyValuePair(Of String, WeChatFriendInfo) In Me.mHelper.ParaCache.DictFriendInfoWechat
                    If Me.mHelper.CheckTableInWechatMMDB(pair.Key) Then
                        dictFriendFilter(pair.Key) = pair.Value
                    End If
                Next

                Dim dictTemp As Dictionary(Of String, WeChatFriendInfo) = Me.mHelper.ParaCache.DBWechat.SearchFriend(dictFriendFilter, para.CurrentSearchText, para.DateStart, para.DateEnd)
                For Each pair As KeyValuePair(Of String, WeChatFriendInfo) In dictTemp
                    dictFriends(pair.Key) = pair.Value
                Next

            ElseIf Me.mAppType = IMAppType.QQ Then
                Dim dictFriendFilter As New Dictionary(Of String, QQFriendInfo)

                '过滤不存在的对话框Table的
                For Each pair As KeyValuePair(Of String, QQFriendInfo) In Me.mHelper.ParaCache.DictFriendInfoQQ
                    If Me.mHelper.CheckTableInQQDB(pair.Key, pair.Value.IsGroup, pair.Value.IsDiscuss) Then
                        dictFriendFilter(pair.Key) = pair.Value
                    End If
                Next

                Dim dictTemp As Dictionary(Of String, QQFriendInfo) = Me.mHelper.ParaCache.DBQQ.SearchFriend(dictFriendFilter, para.CurrentSearchText, para.DateStart, para.DateEnd)
                For Each item As KeyValuePair(Of String, QQFriendInfo) In dictTemp
                    dictFriends(item.Key) = item.Value
                Next
            End If

            For Each row As DataGridViewRow In listRows
                Dim blnFindDelete As Boolean = False
                Dim blnRowVisible As Boolean = False

                '获取好友信息
                Dim friendInfo As SocialFriendInfoBase = row.Tag
                If friendInfo Is Nothing Then
                    GoTo DoNext
                End If

                If para.CurrentFriendType = ShowFriendType.ShowDelete Then
                    '只显示删除数据，跳过正常未删除数据的显示
                    If Not friendInfo.IsFindDate Then
                        GoTo DoNext
                    End If

                ElseIf para.CurrentFriendType = ShowFriendType.ShowNoDelete Then
                    '只显示正常未删除数据，跳过删除数据的显示
                    If friendInfo.IsFindDate Then
                        GoTo DoNext
                    End If

                End If

                '搜索关键字空，显示所有记录
                If String.IsNullOrEmpty(para.CurrentSearchText) Then
                    blnRowVisible = True
                    iCountShow += 1

                    GoTo DoNext
                End If

                '搜索全部或只搜索联系人
                If para.CurrentSearchType = SearchDataType.All OrElse para.CurrentSearchType = SearchDataType.Contact Then
                    Dim strSoftName = friendInfo.UsrName & friendInfo.NickName & friendInfo.NoteName
                    Dim strPinYin As String = PinYinClass.MakePinYin(strSoftName, PinYinOptions.Default).ToLower
                    Dim strPinYinFirstChar As String = PinYinClass.MakePinYin(strSoftName, PinYinOptions.FirstCharacterOnly).ToLower

                    If strSoftName.Contains(para.CurrentSearchText) OrElse strPinYin.Contains(para.CurrentSearchText) OrElse strPinYinFirstChar.Contains(para.CurrentSearchText) Then
                        blnRowVisible = True
                        iCountShow += 1

                        GoTo DoNext
                    End If
                End If

                '搜索搜索全部或者聊天记录
                If para.CurrentSearchType = SearchDataType.All OrElse para.CurrentSearchType = SearchDataType.Record Then
                    If dictFriends.ContainsKey(friendInfo.UsrName) Then
                        blnRowVisible = True
                        iCountShow += 1

                        GoTo DoNext
                    End If
                End If


                '从好友删除列表查找记录
                If Me.mAppType = IMAppType.WeChat Then
                    If Me.mHelper.ParaCache.DictFriendInfoWechat.ContainsKey(friendInfo.UsrName) AndAlso Me.mHelper.ParaCache.DictMessageDeleteWechat.ContainsKey(friendInfo.UsrName) Then
                        For Each msg As WeChatChatInfo In Me.mHelper.ParaCache.DictMessageDeleteWechat(friendInfo.UsrName)
                            If msg.StrMessage.Contains(para.CurrentSearchText) Then
                                blnRowVisible = True
                                iCountShow += 1
                                blnFindDelete = True
                                GoTo DoNext
                            End If
                        Next
                    End If

                ElseIf Me.mAppType = IMAppType.QQ Then
                    If Me.mHelper.ParaCache.DictFriendInfoQQ.ContainsKey(friendInfo.UsrName) AndAlso Me.mHelper.ParaCache.DictMessageDeleteQQ.ContainsKey(friendInfo.UsrName) Then
                        For Each msg As QQChatInfo In Me.mHelper.ParaCache.DictMessageDeleteQQ(friendInfo.UsrName)
                            If msg.StrMessage.Contains(para.CurrentSearchText) Then
                                blnRowVisible = True
                                iCountShow += 1

                                GoTo DoNext
                            End If
                        Next
                    End If
                End If

DoNext:
                dictRows(row) = blnRowVisible

                If (dictFriends.ContainsKey(friendInfo.UsrName) OrElse blnFindDelete) AndAlso para.CurrentSearchText.Length > 0 Then
                    friendInfo.SearchText = para.CurrentSearchText
                Else
                    friendInfo.SearchText = String.Empty
                End If

                If blnRowVisible AndAlso para.CurrentSearchText.Length > 0 Then
                    friendInfo.IsSearch = True
                Else
                    friendInfo.IsSearch = False
                End If
            Next

            Me.SetRowsVisible(dictRows)
            Me.SetLoading(False)

            If iCountShow > 0 Then
                Me.LoadChat(FindDataType.Search)
            Else
                Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.SearchNoResult")) '"搜索不到结果"
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "NewSearchThread")
        End Try
    End Sub

#End Region

#Region "---  过滤消息类型  ---"
    Private Sub ResetShowAndFilterType()
        Me.tsmiSearchAll.Checked = True
        Me.tsmiSearchMsg.Checked = False
        Me.tsmiSearchName.Checked = False

        Me.tsmiShowAll.Checked = True
        Me.tsmiShowDelete.Checked = False
        Me.tsmiShowNoDelete.Checked = False

        Me.btnFilter.Text = Me.Language.GetString("WeChat.Menu.ShowAll")
        Me.mShowFriendType = ShowFriendType.ShowAll
    End Sub

    Private Sub btnFilter_Click(sender As Object, e As EventArgs) Handles btnFilter.Click
        Select Case Me.mShowFriendType
            Case ShowFriendType.ShowAll
                Me.tsmiShowAll.Checked = True

                Me.tsmiShowDelete.Checked = False
                Me.tsmiShowNoDelete.Checked = False

            Case ShowFriendType.ShowDelete
                Me.tsmiShowDelete.Checked = True

                Me.tsmiShowAll.Checked = False
                Me.tsmiShowNoDelete.Checked = False

            Case ShowFriendType.ShowNoDelete
                Me.tsmiShowNoDelete.Checked = True

                Me.tsmiShowAll.Checked = False
                Me.tsmiShowDelete.Checked = False

        End Select
        Me.cmsFilter.Show(Me.btnFilter, 0, Me.btnFilter.Height + 3)
    End Sub

    '只显示已经删除记录
    Private Sub tsmiShowDelete_Click(sender As Object, e As EventArgs) Handles tsmiShowDelete.Click
        Me.btnFilter.Text = Me.Language.GetString("Weixin.Lable.ShowOnlyDelete")
        Me.LoadFilter(ShowFriendType.ShowDelete)
    End Sub

    '只显示正常未删除的记录
    Private Sub tsmiShowNoDelete_Click(sender As Object, e As EventArgs) Handles tsmiShowNoDelete.Click
        Me.btnFilter.Text = Me.Language.GetString("Weixin.Lable.ShowUnDelete")
        Me.LoadFilter(ShowFriendType.ShowNoDelete)
    End Sub

    '显示所有记录，包含已经删除记录
    Private Sub tsmiShowAll_Click(sender As Object, e As EventArgs) Handles tsmiShowAll.Click
        Me.btnFilter.Text = Me.Language.GetString("WeChat.Menu.ShowAll")
        Me.LoadFilter(ShowFriendType.ShowAll)
    End Sub

    '不显示公众号数据
    Private Sub tsmiShowNoPublic_Click(sender As Object, e As EventArgs) Handles tsmiShowNoPublic.Click
        Me.btnFilter.Text = "不显示公众号"
        Me.LoadFilter(ShowFriendType.ShowNoPublic)
    End Sub

    Private Sub LoadFilter(filterType As ShowFriendType)
        Try
            Me.mShowFriendType = filterType

            Me.NewSearch()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadFilter")
        End Try
    End Sub

#End Region

#Region "---  导出  ---"

    Private Sub ShowProgress(para As Object)
        Try
            Dim frmExport As New frmExportProgress(Me.mApplication, Me.mHelper, para)
            frmExport.ShowDialog(Me)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowExportProgress")
        End Try
    End Sub

#End Region

#Region "---  私有方法  ---"

    '检测是否安装Excel
    Private Sub CheckIsInstallExcel()
        Try
            If Me.mThreadIsInstallExcel IsNot Nothing AndAlso Me.mThreadIsInstallExcel.ThreadState <> ThreadState.Stopped Then
                Me.mThreadIsInstallExcel.Abort()
            End If
        Catch
        End Try

        Me.mThreadIsInstallExcel = New Thread(AddressOf CheckIsInstallExcelInThread)
        With Me.mThreadIsInstallExcel
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub CheckIsInstallExcelInThread()
        Try
            Dim xlApp As Object = CreateObject("Excel.Application")

            If xlApp Is Nothing Then
                Me.mBlnInstallExcel = False
            Else
                Me.mBlnInstallExcel = True
                xlApp.Quit()
            End If
        Catch
            Me.mBlnInstallExcel = False
        End Try
    End Sub

#End Region

#Region "---  找回数据  ---"
    Private Sub btnFinalData_Click(sender As Object, e As EventArgs) Handles btnFinalData.Click
        Me.StartFindData()
    End Sub

    Private Sub StartFindData()
        If Utility.CheckThreadIsAlive(Me.mTDFindData) Then
            Return
        End If

        Me.mLastSelectFriendInfo = Nothing
        Me.ShowSystemInfoOnPC(False, "")
        Me.ChangeStateText("")
        Me.PictureBoxAD.Visible = False
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        Me.SetPnlSearchHint(False)
        Me.SetPanelCountVisibleInvoke(False)

        Me.btnFinalData.tbShowDot = False
        Me.txtSearchChat.Text = String.Empty
        Me.txtSearchChat.SearchTipText = Me.Language.GetString("Weixin.tbSearch.SearchTip") '"按回车搜索消息记录" 

        ' Me.NewSearch()

        Me.ResetShowAndFilterType()

        ActionCollectHelper.WeChatFindData(Me.mDevice, ModelKey.Weixin, ModelKey.FindWeChatData, ActionDataType.Click, FunctionSucceed.Succeed)
        IniSetting.SetShowFindWeChatDataNew()

        Me.mTDFindData = New Thread(AddressOf FindDataThread)
        Me.mTDFindData.IsBackground = True
        Me.mTDFindData.Start()
    End Sub

    Private Sub FindDataThread()
        Try
            Me.mFindDataProgress = -1
            Me.SetProgress(0)
            Me.ShowFindData(True)
            Me.mHelper.FindData()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "FindDataThread")
        End Try
    End Sub

    Private Sub OnFindFriend(sender As Object, args As FindDataEventArgs)
        '打开的联系人要显示到界面
        If args.FriendInfo Is Nothing OrElse String.IsNullOrEmpty(args.FriendInfo.StrLastSendMsg) Then
            Return
        End If

        Try
            Me.mIsInsertRow = True
            Dim row As DataGridViewRow = Nothing
            For Each item As DataGridViewRow In Me.dgvFriend.Rows
                If item.Tag Is Nothing AndAlso Not TypeOf item.Tag Is SocialFriendInfoBase Then
                    Continue For
                End If
                Dim info As SocialFriendInfoBase = item.Tag
                If info.UsrName = args.FriendInfo.UsrName Then
                    row = item
                End If
            Next
            Me.dgvFriend.SuspendLayout()
            If row Is Nothing Then
                row = Me.InsertDataGridRow(args.FriendInfo)
            End If

            If row Is Nothing Then
                Return
            End If

            Me.dgvFriend.Rows.Remove(row)
            Me.dgvFriend.Rows.Insert(0, row)

            Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")

            cellName.tbIconShadow = True
            cellName.tbForeColor = IIf(args.FriendInfo.IsFindDate, Color.FromArgb(255, 92, 92), Color.FromArgb(0, 0, 0))
            cellName.tbSelectionForeColor = cellName.tbForeColor

            Me.dgvFriend.ResumeLayout()
            Me.mIsInsertRow = False
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWeixin_OnFindFriend")
            Me.mIsInsertRow = False
        End Try
    End Sub

    Private mFindDataProgress As Integer = -1
    Private Sub OnFindDataProgressChanged(sender As Object, args As FindDataEventArgs)
        Try
            Select Case args.Status
                Case FindDataStatus.Record

                Case FindDataStatus.Progress
                    If Me.mFindDataProgress <> args.Progress Then
                        Me.mFindDataProgress = args.Progress
                        Me.SetProgress(args.Progress)
                    End If

                Case FindDataStatus.ErrorM, FindDataStatus.Exception
                    Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Button.RecoveredNoData")) '"对不起！未找回任何删除数据。"
                    Me.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106)
                    Common.LogException(String.Format("ErrorMessage:{0} ErrorCode:{1}", args.Message, args.Progress), "<<<---FWCDHelper_WCRProgressEventHandler")
                    Me.ShowFindData(False)

                Case FindDataStatus.Abnormal
                    Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Button.RecoveredAbnormal")) ' "找回程序出现异常崩溃,请重试！"
                    Me.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106)
                    Me.ShowFindData(False)
                    Me.ShowFindDataResultPanel(False)

            End Select

            If args.IsFinish Then
                Me.ShowFriend()
                Me.FindResult()
                Me.StartLoadHeadIcon()
                Me.ShowFindData(False)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "FWCDHelper_WCRProgressEventHandler")
        End Try
    End Sub

    Private mProgressBackup As Double = 0
    Private Sub OnBackupProgressChanged(ByVal e As BackupRestoreEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New BackupRestoreHandler(AddressOf OnBackupProgressChanged), e)
        Else
            Dim sType As BackupRestoreErrorType = BackupRestoreErrorType.None

            If e.ErrorMsg.Length > 0 Then
                sType = BackupHelper.GetErrotType(BackupRestore.Backup, e)
            End If

            Dim strProgress As String = Convert.ToDecimal(e.Progress).ToString("0.00") 'CInt(e.Progress).ToString().PadLeft(2, " "c)
            If Common.GetDigit(strProgress) <= Me.mProgressBackup Then
                Return
            End If

            Me.mProgressBackup = Common.GetDigit(strProgress)

            If Me.mProgressBackup >= 100 AndAlso e.Completed Then
                Me.mBackupInfo = New BackupInfo(Me.mHelper.ParaCache.BackupFolder)
            End If

            Me.lblLoading.Text = String.Format("{0}{1}%", Me.Language.GetString("Weixin.Lable.LoadData"), Me.mProgressBackup) '"正在获取微信数据"

            Try
                If sType = BackupRestoreErrorType.DiskSpaceNotEnough Then
                    Dim strSdik As String = ""
                    If Not String.IsNullOrEmpty(Me.mHelper.ParaCache.BackupFolder) Then
                        strSdik = Path.GetPathRoot(Me.mHelper.ParaCache.BackupFolder)
                        If strSdik.EndsWith(":\") Then
                            strSdik = strSdik.Replace(":\", "")
                        End If
                        strSdik = strSdik & "盘 "
                    End If

                    Me.lblLoading.Text = strSdik & Me.Language.GetString("Common.Message.DiskSpaceNotEnough")
                    Me.picLoading.Visible = False
                    Common.Log("磁盘空间不足")
                End If
            Catch
            End Try

            If Me.mIsBtnBackupWaiting Then
                Me.lblState.Text = String.Format("{0}{1}%", Me.Language.GetString("Backup.Message.BeinBackuping"), Me.mProgressBackup) ' "正在备份中..."
                Me.SetFrmWeChatMainlblState()
            End If
        End If
    End Sub


    Private Sub StartLoadHeadIcon()

        If Not IniSetting.GetLoadWeChatHeadIcon() Then
            Return
        End If

        Utility.AbortThread(Me.mThreadLoadHeadIcon)

        Me.mThreadLoadHeadIcon = New Thread(New ParameterizedThreadStart(AddressOf Me.LoadHeadIconThread))
        Me.mThreadLoadHeadIcon.IsBackground = True
        Me.mThreadLoadHeadIcon.Start(Nothing)
    End Sub

    Private Sub LoadHeadIconThread(state As Object)
        Try
            For Each row As DataGridViewRow In Me.dgvFriend.Rows
                Dim friendInfo As SocialFriendInfoBase = row.Tag
                If friendInfo Is Nothing Then
                    Continue For
                End If

                If Me.mHelper.DownloadHeadIcon(friendInfo) Then
                    Me.UpdateHeadIconToDataGridView(row)
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadHeadIconThread")
        End Try
    End Sub

    Private Delegate Sub UpdateHeadIconToDataGridViewHandler(ByVal row As DataGridViewRow)
    Private Sub UpdateHeadIconToDataGridView(ByVal row As DataGridViewRow)
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateHeadIconToDataGridViewHandler(AddressOf UpdateHeadIconToDataGridView), row)
        Else
            Try
                If row.Index < 0 Then
                    Return
                End If

                Dim friendInfo As SocialFriendInfoBase = CType(row.Tag, SocialFriendInfoBase)
                Dim img As Image = Utility.GetImageFormFile(friendInfo.HeadIconOnPC)

                If img IsNot Nothing Then
                    Dim cellName As tbDataGridViewTextBoxCell = row.Cells("colName")
                    cellName.tbIcon = img

                    Application.DoEvents()
                End If
            Catch ex As Exception
                Common.LogException(String.Format("colName:{0}  colMD5:{1}  ", row.Cells("colName").Value, row.Cells("colMD5").Value) & ex.ToString(), "UpdateHeadIconToDataGridView")
            End Try
        End If
    End Sub

    Private Delegate Sub SetProgressHandler(ByVal strProgress As String)
    Private Sub SetProgress(ByVal strProgress As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetProgressHandler(AddressOf SetProgress), strProgress)
        Else
            Me.lblDoing.Text = String.Format("{0}%", strProgress)
            Application.DoEvents()
        End If
    End Sub


    Private Delegate Sub SetWeiXinProgressHandler(ByVal strProgress As String)
    Public Sub SetWeiXinProgress(ByVal strProgress As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetWeiXinProgressHandler(AddressOf SetWeiXinProgress), strProgress)
        Else
            Try
                Me.lblLoading.Text = String.Format("{0}%", strProgress)
                Application.DoEvents()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetWeiXinProgress")
            End Try
        End If
    End Sub

    Private Delegate Sub ShowFindDataHandler(ByVal blnVisible As Boolean)
    Private Sub ShowFindData(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowFindDataHandler(AddressOf ShowFindData), blnVisible)
        Else

            Me.mIsFindDataing = blnVisible

            Me.pnlFindData.Visible = blnVisible
            If Me.pnlFindData.Visible Then
                Me.pnlFindData.BringToFront()
            End If

            Me.btnExportMedia.Enabled = Not blnVisible
            Me.btnFinalData.Enabled = Not blnVisible
            Me.btnDataRecoveryToPhone.Enabled = Not blnVisible
            Me.btnWeCahtUsers.Enabled = Not blnVisible
            Me.txtSearchChat.ReadOnly = blnVisible
            Me.btnOK.Enabled = Not blnVisible
            Me.btnTime.Enabled = Not blnVisible
            Me.btnFilter.Enabled = Not blnVisible
            Me.dgvFriend.Enabled = Not blnVisible
            Me.btnTimeFilter.Enabled = Not blnVisible
            Me.picTimeFilter.Enabled = Not blnVisible
            Me.btnExportSetting.Enabled = Not blnVisible
            Me.btnExport.Enabled = Not blnVisible
            Me.llblRefresh.Enabled = Not blnVisible
            If Me.mFrmWeChatMain IsNot Nothing Then
                Me.mFrmWeChatMain.llblRefresh.Enabled = Not blnVisible
            End If
        End If
    End Sub

    Private Sub FindResult()
        If Me.mHelper.ParaCache.DictMessageDeleteWechat.Count = 0 Then
            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Button.RecoveredNoData")) '"对不起！未找回任何删除数据。"
            Me.lblSystemInfo.ForeColor = Color.FromArgb(106, 106, 106)
        Else
            Dim iCount As Integer = 0

            For Each temp As KeyValuePair(Of String, List(Of SociaChatBase)) In Me.mHelper.ParaCache.DictMessageDeleteWechat
                iCount = iCount + temp.Value.Count
            Next

            For Each temp As KeyValuePair(Of String, List(Of SociaChatBase)) In Me.mHelper.ParaCache.DictMessageDeleteQQ
                iCount = iCount + temp.Value.Count
            Next

            Dim strText As String = String.Format(Me.Language.GetString("Weixin.Lable.FindRecords"), String.Format("[{0}]", iCount)) '"共找回{0}条删除的数据"
            Dim colSelect As Color = Color.FromArgb(5, 152, 245)
            Me.Selection(strText, Me.rtxtFindDelCount, "[", "]", colSelect)
            Me.lblSystemInfo.Visible = False

            Me.ShowFindDataResultPanel(True)
        End If
    End Sub

    Private Delegate Sub SelectionHandler(ByVal strText As String, rtxt As RichTextBox, ByVal strTagState As String, ByVal strTagEnd As String, ByVal colSelect As Color, ByVal align As HorizontalAlignment)
    Private Sub Selection(ByVal strText As String, rtxt As RichTextBox, ByVal strTagState As String, ByVal strTagEnd As String, ByVal colSelect As Color, Optional ByVal align As HorizontalAlignment = HorizontalAlignment.Center)
        If Me.InvokeRequired Then
            Me.Invoke(New SelectionHandler(AddressOf Selection), strText, rtxt, strTagState, strTagEnd, colSelect, align)
        Else
            Try
                If strText.Length = 0 Then
                    Return
                End If
                Dim iStart As Integer = strText.IndexOf(strTagState)
                Dim iLength As Integer = strText.IndexOf(strTagEnd) - iStart - 1
                rtxt.Text = strText
                Try
                    rtxt.SelectionStart = iStart + 1
                    rtxt.SelectionLength = iLength
                    rtxt.SelectionColor = colSelect
                Catch ex As Exception
                End Try
                rtxt.SelectionAlignment = align
            Catch ex As Exception
                Common.LogException(ex.ToString(), "Selection")
            End Try
        End If
    End Sub

    Private Sub ShowFindDataResultPanel(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetControlVisibleHanlder(AddressOf ShowFindDataResultPanel), blnVisible)
        Else
            Me.pnlFindDataResult.Visible = blnVisible

            If Me.pnlFindDataResult.Visible Then
                Me.pnlFindDataResult.BringToFront()
            Else
                Me.pnlFindDataResult.SendToBack()
            End If
        End If
    End Sub

#End Region

#Region "--- Timer ShowCount ---"

    Private Delegate Sub SetControlVisibleHanlder(ByVal blnVisible As Boolean)

    Private Sub tmrShowSearchCount_Elapsed(sender As Object, e As Timers.ElapsedEventArgs)
        If Me.mShowSearchTime < 10 Then
            Me.mShowSearchTime = Me.mShowSearchTime + 1
            Return
        End If

        Me.SetSearchPanelCountVisible(False)
    End Sub

    Private Sub SetSearchPanelCountVisible(ByVal isShow As Boolean)
        Me.mShowSearchTime = 0
        If isShow Then
            Me.tmrShowSearchCount.Start()
        Else
            Me.tmrShowSearchCount.Close()
        End If
        Try
            Me.SetSearchPanelCountVisibleInvoke(isShow)
        Catch ex As Exception
            Me.SetSearchPanelCountVisibleInvoke(isShow)
            Common.LogException(ex.ToString(), "SetSearchPanelCount")
        End Try
    End Sub

    Private Sub SetSearchPanelCountVisibleInvoke(ByVal blnVisible As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetControlVisibleHanlder(AddressOf SetSearchPanelCountVisibleInvoke), blnVisible)
            Else
                Me.pnlSearchHint.Visible = blnVisible
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetSearchPanelCountInvoke")
        End Try
    End Sub

    Private Sub tmrShowCount_Elapsed(sender As Object, e As Timers.ElapsedEventArgs)
        If Me.mShowTime < 15 Then
            Me.mShowTime = Me.mShowTime + 1
            Return
        End If

        Me.SetPanelCountVisible(False)
    End Sub

    Private Sub SetPanelCountVisible(ByVal isShow As Boolean)
        Try
            If isShow Then
                Me.mShowTime = 0
                Me.tmrShowCount.Start()
            Else
                Me.tmrShowCount.Close()
            End If

            Me.SetPanelCountVisibleInvoke(isShow)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetPnlCount")
        End Try
    End Sub

    Private Sub SetPanelCountVisibleInvoke(ByVal blnVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetControlVisibleHanlder(AddressOf SetPanelCountVisibleInvoke), blnVisible)
        Else
            Me.pnlCount.Visible = blnVisible
        End If
    End Sub

#End Region

    Private Sub btnTime_Click(sender As Object, e As EventArgs) Handles btnTime.Click
        Dim menu As New tbContextMenuStrip()

        Dim lstTimes As List(Of String) = New List(Of String)
        lstTimes.Add(Me.Language.GetString("Download.Message.Today"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.Week"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.Month"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.ThreeMonth"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.Year"))
        lstTimes.Add(Me.Language.GetString("Weixin.Item.All"))

        For Each strTime As String In lstTimes
            Dim tsmi As New ToolStripMenuItem()
            tsmi.Text = strTime
            tsmi.Checked = (btnTime.Text = strTime)

            AddHandler tsmi.Click, AddressOf OnTimeMenuItemClick

            menu.Items.Add(tsmi)
        Next

        AddHandler menu.Closed, AddressOf OnTimeMenuClosed

        menu.Show(btnTime, New Point(0, btnTime.Height))
    End Sub

    Private Sub OnTimeMenuClosed(sender As Object, e As ToolStripDropDownClosedEventArgs)
        RemoveHandler CType(sender, tbContextMenuStrip).Closed, AddressOf OnTimeMenuClosed
    End Sub

    Private Sub OnTimeMenuItemClick(sender As Object, e As EventArgs)
        Dim item As ToolStripMenuItem = CType(sender, ToolStripMenuItem)
        Me.SetDateTime(item.Text)
    End Sub

    Private Sub SetDateTime(strText As String)
        Try
            Dim dtStart As DateTime = DateTime.Parse("1970-01-01")
            Dim dtEnd As DateTime = Date.Now.Date.AddDays(1).AddSeconds(-1)


            Select Case strText
                Case Me.Language.GetString("Download.Message.Today") '今天
                    dtStart = DateTime.Now.Date

                Case Me.Language.GetString("Weixin.Item.Week") '最近一周
                    dtStart = DateTime.Now.Date.AddDays(-6)

                Case Me.Language.GetString("Weixin.Item.Month") '最近一个月
                    dtStart = DateTime.Now.Date.AddMonths(-1)

                Case Me.Language.GetString("Weixin.Item.ThreeMonth") '最近三个月
                    dtStart = DateTime.Now.Date.AddMonths(-3)

                Case Me.Language.GetString("Weixin.Item.Year")  '最近一年
                    dtStart = DateTime.Now.Date.AddYears(-1)

                Case Me.Language.GetString("Weixin.Item.All")
                    dtEnd = DateTime.Parse("2999-01-01")
            End Select

            Me.btnTime.Text = strText

            'DateTimePicker中选择的最小日期和时间。默认值为 1/1/1753 00:00:00。
            'DateTimePicker中选择的最大日期和时间。默认值为 12/31/9998 23:59:59。
            '先重置最大日期和最小日期，否则会导致赋值失败
            Me.dtpStart.MaxDate = Date.Parse("9998-01-01")
            Me.dtpEnd.MinDate = Date.Parse("1753-01-01")

            Me.dtpStart.Value = dtStart
            Me.dtpEnd.Value = dtEnd

            Me.dtpStart.MaxDate = Me.dtpEnd.Value
            Me.dtpEnd.MinDate = Me.dtpStart.Value
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetDateTime")
        End Try
    End Sub

    Private Sub lblSystemInfo_Click(sender As Object, e As EventArgs)
#If IS_WECHAT Then
        If Folder.AppType = RunType.iWeChatMigration Then
            Common.OpenExplorer(WebUrl.iTunesBackupCourse)
        Else
            Common.OpenExplorer("http://wechat.tongbu.com/guide/1")
        End If
#Else
        Common.OpenExplorer(WebUrl.iTunesBackupCourse)
#End If
    End Sub

    Private Delegate Sub SetFrmWeChatMainlblStateHandler()
    Private Sub SetFrmWeChatMainlblState()
        If Me.InvokeRequired Then
            Me.Invoke(New SetFrmWeChatMainlblStateHandler(AddressOf SetFrmWeChatMainlblState))
        Else
            Try
                If Me.mFrmWeChatMain IsNot Nothing Then
                    Me.mFrmWeChatMain.StrlblState = Me.lblState.Text
                Else
                    Me.lblState.Visible = True
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetFrmWeChatMainlblState")
            End Try
        End If
    End Sub

    Private Delegate Sub SetBtnVisibleHandler(ByVal btn As tbButton, ByVal isVisible As Boolean)
    Private Sub SetBtnVisible(ByVal btn As tbButton, ByVal isVisible As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetBtnVisibleHandler(AddressOf SetBtnVisible), btn, isVisible)
        Else
            Try
                btn.Visible = isVisible
                'Me.SetBtnLocation()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetFrmWeChatMainlblState")
            End Try
        End If
    End Sub

    Private Sub btnPreviousMenu_Click(sender As Object, e As EventArgs) Handles btnPreviousMenu.Click, btnReturnView.Click
        If Me.mFrmWeChatMain IsNot Nothing Then
            Me.ResetShowAndFilterType()
            Me.mFrmWeChatMain.mIsFromBackupShow = True
            If Me.m_bIsiCloud Then
                Me.mFrmWeChatMain.SetViewStyle(WeixinMainViewStyle.iCloudBackup)
            Else
                Me.mFrmWeChatMain.SetViewStyle(WeixinMainViewStyle.LoadBackup)
            End If
            Me.Close()
        End If
    End Sub

    Private Sub btnOpenTutorial_Click(sender As Object, e As EventArgs) Handles btnOpenTutorial.Click

#If IS_WECHAT Then
        Common.OpenExplorer("http://wechat.tongbu.com/guide/1")
        Me.btnPreviousMenu_Click(sender, e)
#Else
        Common.OpenExplorer("http://news.tongbu.com/92201.html")
        Try
            If Me.mFrmWeChatMain IsNot Nothing Then
                Me.mFrmWeChatMain.Close()
            End If
        Catch
        End Try
        Me.Close()
#End If
    End Sub

    Private Sub lblOpenTutorial_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lblOpenTutorial.LinkClicked

#If IS_WECHAT Then
        If Folder.AppType = RunType.iWeChatMigration Then
            Common.OpenExplorer("http://news.tongbu.com/92201.html")
        Else
            Common.OpenExplorer("http://wechat.tongbu.com/guide/1")
        End If
#Else
        Common.OpenExplorer("http://news.tongbu.com/92201.html")
#End If
    End Sub

    Private Sub btnExportMedia_Click(sender As Object, e As EventArgs) Handles btnExportMedia.Click
        If Me.dgvFriend.Rows.Count = 0 Then
            '联系人为空。
            tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Me.cmsExportEx.Show(Me.btnExportMedia, Me.btnExportMedia.Width - Me.cmsExportEx.Width, Me.btnExportMedia.Height + 3)
    End Sub

    Private Sub tsmiPicEx_Click(sender As Object, e As EventArgs) Handles tsmiPicEx.Click
        Me.ExportMedia(ExportType.ExportOnlyPhoto)
    End Sub

    Private Sub tsmiAudioEx_Click(sender As Object, e As EventArgs) Handles tsmiAudioEx.Click
        Me.ExportMedia(ExportType.ExportOnlyAudio)
    End Sub

    Private Sub tsmiVideoEx_Click(sender As Object, e As EventArgs) Handles tsmiVideoEx.Click
        Me.ExportMedia(ExportType.ExportOnlyVideo)
    End Sub

    Private Sub tsmiAll_Click(sender As Object, e As EventArgs) Handles tsmiAll.Click
        Me.NewExport(ExportType.ExportToAll)
    End Sub

    Private Sub tsmiGroup_Click(sender As Object, e As EventArgs) Handles tsmiGroup.Click

        If Me.CheckChargeOverdue() Then
            Return
        End If

        Try
            If Me.dgvFriend Is Nothing OrElse Me.dgvFriend.SelectedRows.Count <= 0 Then
                tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Backup.Button.ExportGroupFriendTip"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            If Me.dgvFriend.SelectedRows.Count > 1 Then
                tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Backup.Button.ExportGroupFriendTip"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning) '请选择一个要导出的群
                Return
            End If

            Dim row As DataGridViewRow = Me.dgvFriend.SelectedRows(0)
            If row.Tag IsNot Nothing Then
                Dim info As SocialFriendInfoBase = CType(row.Tag, SocialFriendInfoBase)
                If Not info.IsGroup Then
                    tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Backup.Button.ExportGroupFriendTip"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning) '请选择一个要导出的群
                    Return
                End If

                Dim frm As frmExportGroup = New frmExportGroup(info, Me.mHelper)
                If frm.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
                    Me.ExportMedia(frm.mSelectExportType, frm.mSelectWXID)
                End If
            End If


        Catch ex As Exception
            Common.LogException(ex.ToString(), "tsmiGroup_Click")
        End Try
    End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        'If IniSetting.GetShowWCExportFormat() Then
        '    Dim frm As New frmWeChatSetting(True)
        '    frm.ShowDialog(Me)
        '    IniSetting.SetShowWCExportFormat(False)
        'End If
        'Me.NewExport(ExportType.ExportToSetting)

        If Me.dgvFriend.Rows.Count = 0 Then
            '联系人为空。
            tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Me.cmsExportEx.Show(Me.btnExportMedia, Me.btnExportMedia.Width - Me.cmsExportEx.Width, Me.btnExportMedia.Height + 3)
    End Sub

    Private Sub ExportMedia(ByVal strType As ExportType, Optional ByVal strGroup_Wxid As String = "")
        If Me.CheckChargeOverdue() Then
            Return
        End If

        Dim para As IMExportPara = Me.CheckExport()
        If para Is Nothing Then
            Return
        End If

        para.ExportType = strType
        para.UserNameOnlyExport = strGroup_Wxid

        Me.ShowProgress(para)

    End Sub

    Private Function CheckChargeOverdue(Optional ByVal isShow As Boolean = True) As Boolean
        Try
            If isShow Then
                Dim dev As ChargeDevice = Nothing
                If Me.mFrmWeChatMain IsNot Nothing Then
                    dev = Me.mFrmWeChatMain.mWCMCinfo
                End If
                Return Not Me.mChargeHelper.CheckIsChargeApp(Me, FunctionKey.None, 0, Me.mBackupInfo.Identifier, True, dev)
            Else
                Return Not Me.mChargeHelper.CheckDeviceRegistered(Me.mBackupInfo.Identifier)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckChargeOverdue")
        End Try
    End Function

    Private Function CheckExport(Optional ByVal type As IMParaType = IMParaType.Export, Optional ByVal dirSave As String = "") As Object
        Dim para As IMExportPara = Nothing

        Try
            Try
                If Me.dgvFriend.Rows.Count = 0 Then
                    '联系人为空。
                    tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                    GoTo DoExit
                End If

                If Me.dgvFriend Is Nothing OrElse Me.dgvFriend.SelectedRows.Count <= 0 Then
                    If type = IMParaType.Export OrElse type = IMParaType.Recovery Then
                        Dim strMsg As String = String.Empty
                        If type = IMParaType.Recovery Then
                            strMsg = Me.Language.GetString("Weixin.Message.SelectDeleteFriend")  '"请选择要恢复的联系人"
                        Else
                            strMsg = Me.Language.GetString("WeChat.Button.SelectContact") '"请选择要导出的联系人"
                        End If

                        tbMessageBox.Show(Me.mApplication, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    End If

                    If type <> IMParaType.Search Then
                        GoTo DoExit
                    End If
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "CheckExport_1")
            End Try

            If String.IsNullOrEmpty(dirSave) Then
                If type = IMParaType.Export Then
                    Dim fbd As New FolderBrowserDialog()

                    Dim dirLastSave As String = IniSetting.GetWeixinSaveFolder()
                    If Not String.IsNullOrEmpty(dirLastSave) AndAlso Directory.Exists(dirLastSave) Then
                        fbd.SelectedPath = dirLastSave
                    End If


                    If fbd.ShowDialog() = Windows.Forms.DialogResult.Cancel Then
                        GoTo DoExit
                    End If

                    dirSave = fbd.SelectedPath
                    IniSetting.SetWeixinSaveFolder(dirSave)
                Else
                    dirSave = Me.mHelper.ParaCache.TempFolder
                End If
            End If

            Try
                If type = IMParaType.Export Then
                    para = New IMExportPara()
                    para.AutoConvert = IniSetting.GetIsAudioConvert()
                Else
                    para = New IMLoadPara()
                End If

                para.IsVIP = Not Me.CheckChargeOverdue(False)
                para.SaveFolder = dirSave

                If type = IMParaType.Export Then
                    For Each row As tbDataGridViewRow In Me.dgvFriend.SelectedRows
                        Dim friendInfo As SocialFriendInfoBase = CType(row.Tag, SocialFriendInfoBase)

                        If type <> IMParaType.Recovery OrElse friendInfo.IsFindDate Then
                            para.ListExport.Add(friendInfo)
                        End If
                    Next
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "CheckExport_2")
            End Try

            If Me.pnlTimeFilter.Visible Then
                para.UseDateTime = True
                para.DateStart = Me.dtpStart.Value
                para.DateEnd = Me.dtpEnd.Value
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckExport")
        End Try

DoExit:
        Return para
    End Function

    Private Sub NewExport(ByVal eType As ExportType)

        If Me.CheckChargeOverdue() Then
            Return
        End If

        Dim para As IMExportPara = Me.CheckExport(IMParaType.Export)
        If para Is Nothing Then
            Return
        End If

        Dim isAudioConvert As Boolean = True

        Dim strPath As String = ""
        Dim lstType As New List(Of ExportType)
        If eType = ExportType.ExportToAll Then
            lstType.Add(ExportType.ExportToTxt)
            lstType.Add(ExportType.ExportToExcel)
            lstType.Add(ExportType.ExportToHtml)

        ElseIf eType = ExportType.ExportToSetting Then
            If IniSetting.GetWCExportFormatTxt() Then
                lstType.Add(ExportType.ExportToTxt)
            End If
            If IniSetting.GetWCExportFormatExcel() Then
                lstType.Add(ExportType.ExportToExcel)
            End If
            If IniSetting.GetWCExportFormatHtml() Then
                lstType.Add(ExportType.ExportToHtml)
            End If

        Else
            lstType.Add(eType)
        End If


        eType = ExportType.None
        For Each t As ExportType In lstType
            eType = eType Or t
        Next

        para.ExportType = eType

        Me.ShowProgress(para)

        'strPath = Me.mIMExportHelper.ExportWhatsAppAllInfo(lstRows, Me.mDictDelChatInfo, lstTime, Me.btnWeCahtUsers.Text, strDeviceName, lstType, obj, blnRecoveryData, Me.mBackupDeviceUDID)

        'If Not String.IsNullOrEmpty(strPath) Then
        '    ShowExportProgressNew(strPath)
        'End If
    End Sub

    Private Sub tsmiExcelEx_Click(sender As Object, e As EventArgs) Handles tsmiExcelEx.Click
        Me.NewExport(ExportType.ExportToExcel)
    End Sub

    Private Sub tsmiTxtEx_Click(sender As Object, e As EventArgs) Handles tsmiTxtEx.Click
        Me.NewExport(ExportType.ExportToTxt)
    End Sub

    Private Sub tsmiHtml_Click(sender As Object, e As EventArgs) Handles tsmiHtml.Click
        Me.NewExport(ExportType.ExportToHtml)
    End Sub

    Private Sub tsmiRecoveryToComputer_Click(sender As Object, e As EventArgs) Handles tsmiRecoveryToComputer.Click
        Try
            TBGuideHelper.OpenTBGuide(Me, TBGuideType.WeChat)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "tsmiRecoveryToComputer_Click")
        End Try
    End Sub

    Private Sub tsmiRecoveryToPhone_Click(sender As Object, e As EventArgs) Handles tsmiRecoveryToPhone.Click
        Try
            TBGuideHelper.OpenTBGuidWechatRecoveryDataToPhone(Me)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "tsmiRecoveryToPhone_Click")
        End Try
    End Sub

    Private Sub btnDataRecoveryToPhone_Click(sender As Object, e As EventArgs) Handles btnDataRecoveryToPhone.Click
        Try
            IniSetting.SetWeChatRecoveryDataShowDot(False)
            Me.btnDataRecoveryToPhone.tbShowDot = False

            If ServerIniSetting.GetShowDataRecoveryDeviceTool() Then
                '引导用工具恢复到设备
                Me.SetlblState("")

                '加密备份暂不支持
                If Directory.Exists(Me.mHelper.ParaCache.BackupFolderEncrypt) Then
                    '暂不支持加密备份的数据恢复到设备
                    tbMessageBox.Show(Me.Language.GetString("Weixin.Message.NotSupportEncryptBackRecoveryToDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If

                '8.3从设备读取
                If Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber < 830 Then
                    '8.3以下设备请先备份，从备份读取微信
                    tbMessageBox.Show(Me.Language.GetString("Weixin.Message.FirstBackupIn830"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If

                Dim drHelper As WechatRecoveryHelper = WechatRecoveryHelper.Instance()

                RemoveHandler drHelper.RecovertyProgressChanged, AddressOf drHelper_WeChatDataRecoveryToPhone
                AddHandler drHelper.RecovertyProgressChanged, AddressOf drHelper_WeChatDataRecoveryToPhone

                If Not File.Exists(Me.mHelper.ParaCache.PathDBOnPC) Then
                    'Media.Message.FileNoExist 文件不存在 
                    tbMessageBox.Show(String.Format("{0}:{1}", Me.Language.GetString("Media.Message.FileNoExist"), Me.mHelper.ParaCache.PathDBOnPC), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If

                If Not File.Exists(Me.mHelper.ParaCache.PathSessionDBOnPC) Then
                    tbMessageBox.Show(String.Format("{0}:{1}", Me.Language.GetString("Media.Message.FileNoExist"), Me.mHelper.ParaCache.PathSessionDBOnPC), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Return
                End If

                If Me.mHelper.ParaCache.DictMessageDeleteWechat.Count = 0 Then
                    '您要先找到删除的数据才可以恢复到手机。
                    tbMessageBox.Show(Me.Language.GetString("Weixin.Message.FirstFindDataThenRecoveryToDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If

                '检测助手是否有付费
                If Me.CheckChargeOverdue() Then
                    Return
                End If

                Dim lstRows As New List(Of DataGridViewRow)
                For Each row As tbDataGridViewRow In Me.dgvFriend.SelectedRows
                    Dim rowitem As WeChatFriendInfo = CType(row.Tag, WeChatFriendInfo)
                    If rowitem.IsFindDate Then
                        lstRows.Add(row)
                    End If
                Next

                If lstRows.Count = 0 Then
                    '请勾选找回的数据
                    tbMessageBox.Show(Me.Language.GetString("Weixin.Message.PleaseSelectFindData"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If

                Dim strWCUName As String = Me.btnWeCahtUsers.Text.Trim()
                Dim strExportFolder As String = Path.Combine(Folder.CacheFolder, "WeixinRecoveryToPhone")

                'Dim strPath As String = Path.Combine(strExportFolder, Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") & "-" & Utility.ReplaceWinIllegalName(strDeviceName)) '"微信消息记录"
                Dim strPath As String = Path.Combine(strExportFolder, Me.mHelper.ParaCache.BackupDeviceUDID)
                strPath = Path.Combine(strPath, Date.Now.ToString("yyyyMMddHHmm"))

                Dim strWCUNameUtf8 As String = Encoding.Default.GetString(Encoding.Default.GetBytes(strWCUName))
                'If Utility.ReplaceWinIllegalName(strWCUName).Length > 0 Then
                '    strPath = strPath & "_" & Utility.ReplaceWinIllegalName(strWCUName)
                'End If
                If Utility.ReplaceWinIllegalName(strWCUNameUtf8).Length > 0 Then
                    strPath = strPath & "_" & Utility.ReplaceWinIllegalName(strWCUNameUtf8)
                End If
                Dim strDbPwd As String = GetDBPwdFromServer()
                If String.IsNullOrEmpty(strDbPwd) Then
                    '获取数据库加密秘钥出错
                    tbMessageBox.Show(Me.Language.GetString("Weixin.Message.GetEncryptDBKeyError"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Return
                End If


                Dim url As String = String.Format("{0}?c_s={1}", WebUrl.PageiRecoveryDevicePurchase, System.Web.HttpUtility.UrlEncode(Common.GetChannelID))
                If Not Me.mChargeHelper.CheckDeviceRegistered(Me.mFrmWeChatMain.CurrentIdentify) Then
                    '"恢复到手机是单独的恢复工具处理的，比导出到电脑贵很多。" & vbCrLf & "建议您先使用同步助手恢复到电脑确认下是否是自己想要的数据。" & vbCrLf & "也可以看下为您打开的网址里面的视频。"
                    Dim tmpMsg As String = Me.Language.GetString("Weixin.Message.RecoveryToolIsOtherCharge") & vbCrLf & _
                                           Me.Language.GetString("Weixin.Message.SuggestYouExportComputerConfirmData") & vbCrLf & _
                                           Me.Language.GetString("Weixin.Message.LookUpVideoInWebsite")

                    tbMessageBox.Show(tmpMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)

                    If Not mHasOpenDataRecoverySite Then
                        Common.OpenExplorer(url)
                        mHasOpenDataRecoverySite = True
                    End If

                    If Not Me.mChargeHelper.CheckIsChargeApp(Me, FunctionKey.Weixin, 0, Me.mFrmWeChatMain.CurrentIdentify) Then
                        Return
                    End If
                End If

                'Me.btnDataRecoveryToPhone.Visible = False
                '"已经为您自动打开恢复手机工具的下载网址，" & vbCrLf & "里面有详细的教程教您怎么恢复数据到手机。" & vbCrLf & "是否立即导出工具需要的删除数据？"
                Dim strMsg As String = ""
                If Me.mChargeHelper.QueryResult.VipType = 15 Then
                    strMsg = Me.Language.GetString("Weixin.Message.OpenWebsiteRecoveryToDeviceEx") & vbCrLf '"您当前购买套餐已经包含恢复到手机的功能，" & vbCrLf & "请直接下载使用无需再次付费。" & vbCrLf
                End If

                strMsg = strMsg & Me.Language.GetString("Weixin.Message.OpenWebsiteRecoveryToDevice") & vbCrLf & Me.Language.GetString("Weixin.Message.HaveDetailTutorial") & vbCrLf & Me.Language.GetString("Weixin.Message.ExportFindDataNow")
                If mHasOpenDataRecoverySite Then
                    strMsg = Me.Language.GetString("Weixin.Message.ExportFindDataNow") '"是否立即导出工具需要的删除？"
                Else
                    Common.OpenExplorer(url)
                    mHasOpenDataRecoverySite = True
                End If

                If tbMessageBox.Show(strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Question) = Windows.Forms.DialogResult.Yes Then
                    Dim para As New WeChatRecoveryPara()
                    para.DictDelChatInfo = Me.mHelper.ParaCache.DictMessageDeleteWechat
                    'para.PathDB_MM = Me.mHelper.ParaCache.PathDBOnPC
                    para.PathDB_MM = Me.mHelper.FormatPath(Me.mHelper.ParaCache.PathDBOnPhone)
                    'para.PathDB_MM_Shm = Me.mHelper.ParaCache.PathDBOnPC & "-shm"
                    'para.PathDB_MM_Wal = Me.mHelper.ParaCache.PathDBOnPC & "-wal"

                    'para.PathDB_Session = Me.mHelper.ParaCache.PathSessionDBOnPC & ""
                    para.PathDB_Session = Me.mHelper.FormatPath(Me.mHelper.ParaCache.PathSessionDBOnPhone)
                    'para.PathDB_Session_Shm = Me.mHelper.ParaCache.PathSessionDBOnPC & "-shm"
                    'para.PathDB_Session_Wal = Me.mHelper.ParaCache.PathSessionDBOnPC & "-wal"

                    'para.PathDB_Contact = Me.mHelper.ParaCache.PathConatctDBOnPC
                    para.PathDB_Contact = Me.mHelper.FormatPath(Me.mHelper.ParaCache.PathConatctDBOnPhone)

                    Try
                        If Me.mDevice Is Nothing Then
                            Dim strBackupFolder As String = ""
                            If Me.mBackupInfo IsNot Nothing Then
                                strBackupFolder = Me.mBackupInfo.FolderPath
                            End If
                            Dim bInfo As New BackupInfo(strBackupFolder)
                            If bInfo IsNot Nothing Then
                                para.DeviceName = bInfo.DeviceName
                            End If
                        Else
                            para.DeviceName = Me.mDevice.DeviceName
                        End If
                    Catch
                    End Try

                    para.UserName = strWCUName
                    para.PathIniFolder = strPath
                    para.Password = strDbPwd

                    For Each row As DataGridViewRow In lstRows
                        para.ListExport.Add(row.Tag)
                    Next

                    Me.ShowProgress(para)
                Else
                    Return
                End If

            Else
                '推荐到淘宝
                TBGuideHelper.OpenTBGuidWechatRecoveryDataToPhone(Me)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "tsmiRecoveryToPhone_Click")
        End Try
    End Sub

    Private Sub ShowExportProgressDataRecovery(ByVal strPath As String)
        'Try
        '    Dim frmProgress As frmExportProgress = New frmExportProgress(Me.mApplication, False, strPath, True)
        '    RemoveHandler frmProgress.StopExportEventHandler, AddressOf frmProgress_StopExportEventHandler
        '    AddHandler frmProgress.StopExportEventHandler, AddressOf frmProgress_StopExportEventHandler
        '    frmProgress.ShowDialog(Me)
        'Catch ex As Exception
        '    Common.LogException(ex.ToString(), "ShowExportProgress")
        'End Try
    End Sub

    Private Sub drHelper_WeChatDataRecoveryToPhone(sender As Object, args As WeChatRecoveryEventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of WeChatRecoveryEventArgs)(AddressOf drHelper_WeChatDataRecoveryToPhone), sender, args)
            Else
                Select Case args.Status
                    'Case WeChatRecoveryStatus.Start
                    '    Me.lblState.Text = Me.Language.GetString("Weixin.Message.PrepareExport") '"准备导出"
                    '    Me.btnDataRecoveryToPhone.Enabled = False

                    'Case WeChatRecoveryStatus.Modify
                    '    Me.lblState.Text = Me.Language.GetString("Weixin.Message.Exporting") & args.Progress & "%"   '"导出中... "

                    Case WeChatRecoveryStatus.Failure
                        'Me.lblState.Text = Me.Language.GetString("Contact.Message.ExportFailure") '"导出失败"
                        'Me.lblState.Text = ""
                        Me.btnDataRecoveryToPhone.Enabled = True
                        Application.DoEvents()
                        If Directory.Exists(args.ExportPath) Then
                            Directory.Delete(args.ExportPath, True)
                        End If
                    Case WeChatRecoveryStatus.Succeed
                        'Me.lblState.Text = "导出成功"
                        Me.btnDataRecoveryToPhone.Enabled = True
                        'Me.SetFrmWeChatMainlblState()
                        'Application.DoEvents()
                        Common.OpenExplorer(args.ExportPath)
                        Try
                            If Me.mFrmWeChatMain IsNot Nothing Then
                                Me.mFrmWeChatMain.Close()
                            End If
                            Me.Close()
                        Catch ex As Exception
                            Common.LogException(ex.ToString(), "drHelper_WeChatDataRecoveryToPhone.Succeed")
                        End Try

                End Select

                Me.SetFrmWeChatMainlblState()
                'Me.lblState.BringToFront()
                Application.DoEvents()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "drHelper_WeChatDataRecoveryToPhone")
        End Try
    End Sub



    Private Sub lbl_SizeChanged(sender As Object, e As EventArgs) Handles btnFinalData.SizeChanged, picTimeFilter.SizeChanged, btnExportSetting.SizeChanged, lblRegister.SizeChanged
        Me.btnFinalData.Location = New Point(Me.btnWeCahtUsers.Right, Me.btnFinalData.Location.Y)
        Me.btnTimeFilter.Location = New Point(Me.btnFinalData.Right, Me.btnTimeFilter.Location.Y)
        Me.picTimeFilter.Location = New Point(Me.btnTimeFilter.Right, Me.picTimeFilter.Location.Y)
        Me.btnExportSetting.Location = New Point(Me.picTimeFilter.Right + 3, Me.btnExportSetting.Location.Y)
        Me.lblRegister.Location = New Point(Me.btnExportSetting.Right, Me.lblRegister.Location.Y)
    End Sub

    Private Sub btnTimeFilter_Click(sender As Object, e As EventArgs) Handles picTimeFilter.Click, btnTimeFilter.Click
        Me.SetPanelTimeFilterVisible(Not Me.pnlTimeFilter.Visible)
    End Sub

    Private Sub SetPanelTimeFilterVisible(ByVal blnVisible As Boolean)
        Me.pnlTimeFilter.Visible = blnVisible

        If blnVisible Then
            Me.picTimeFilter.Image = My.Resources.Resources.TimeScreenUp

            Me.SetDateTime(Me.Language.GetString("Weixin.Item.All"))
        Else
            Me.picTimeFilter.Image = My.Resources.Resources.TimeScreenUpDowm
        End If
    End Sub

    Private Sub dtpStart_CloseUp(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpStart.CloseUp
        Me.dtpEnd.MinDate = Me.dtpStart.Value
    End Sub

    Private Sub dtpEnd_CloseUp(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpEnd.CloseUp
        Me.dtpStart.MaxDate = Me.dtpEnd.Value
    End Sub


    Private Sub pnlContainer_SizeChanged(sender As Object, e As EventArgs) Handles pnlContainer.SizeChanged
        Try
            Me.lblSystemInfo.Location = New Point((Me.pnlContainer.Width - Me.lblSystemInfo.Width) / 2, (Me.pnlContainer.Height - Me.lblSystemInfo.Height) / 2)
            Me.pnlFindDataResult.Location = New Point((Me.pnlContainer.Width - Me.pnlFindDataResult.Width) / 2, (Me.pnlContainer.Height - Me.pnlFindDataResult.Height) / 2)
            Me.pnlFindData.Location = New Point((Me.pnlContainer.Width - Me.pnlFindData.Width) / 2, (Me.pnlContainer.Height - Me.pnlFindData.Height) / 2)
            Me.pnlLoadData.Location = New Point((Me.pnlContainer.Width - Me.pnlLoadData.Width) / 2, (Me.pnlContainer.Height - Me.pnlLoadData.Height) / 2)
        Catch ex As Exception

        End Try
    End Sub

    Private Sub btnExportSetting_Click(sender As Object, e As EventArgs) Handles btnExportSetting.Click
        Dim frm As New frmWeChatSetting()
        frm.ShowDialog(Me)
    End Sub

    Private Sub cbxSelectAll_CheckedChanged(sender As Object, e As EventArgs) Handles cbxSelectAll.Click
        If Me.mSelectAllClick Then
            Return
        End If

        Me.mSelectAllClick = True

        Me.dgvFriend.SuspendLayout()
        For Each item As DataGridViewRow In Me.dgvFriend.Rows
            If item.Selected <> Me.cbxSelectAll.Checked Then
                item.Selected = Me.cbxSelectAll.Checked

                '第一次全选 有可能会没生效
                If item.Selected <> Me.cbxSelectAll.Checked Then
                    item.Selected = Me.cbxSelectAll.Checked
                End If
            End If
        Next
        Me.dgvFriend.ResumeLayout()

        Me.mSelectAllClick = False

        Me.SetLinkmanMsg()
    End Sub

    Private Sub lblRegister_TextClick(sender As Object, e As EventArgs) Handles lblRegister.TextClick
        Me.mChargeHelper.ShowChargeForm(Me, Me.mBackupInfo.Identifier)
    End Sub

    Private Sub llblRefresh_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles llblRefresh.LinkClicked
        Dim cD As iPhoneDevice = Me.mDevice
        If Me.mFrmWeChatMain IsNot Nothing Then
            cD = Me.mFrmWeChatMain.mDevice
        End If
        If cD Is Nothing Then
            tbMessageBox.Show(Me, Me.Language.GetString("Welcome.Label.AccessDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
            Return
        End If
        Try
            If Me.mFrmWeChatMain IsNot Nothing Then
                Me.mFrmWeChatMain.llblRefresh_LinkClicked(Nothing, Nothing)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "llblRefresh_LinkClicked")
        End Try

    End Sub

    Private Sub lblLoading_SizeChanged(sender As Object, e As EventArgs) Handles lblLoading.SizeChanged
        Try
            Me.picLoading.Location = New Point((Me.pnlLoading.Width - Me.picLoading.Width - Me.lblLoading.Width) / 2, Me.picLoading.Location.Y)
            Me.lblLoading.Location = New Point(Me.picLoading.Right, Me.lblLoading.Location.Y)
        Catch
        End Try
    End Sub

    Private Sub lblR_SizeChanged(sender As Object, e As EventArgs) Handles llblRefresh.SizeChanged, lblFromBackupMsg.SizeChanged
        Try
            Me.llblRefresh.Location = New Point(Me.lblFromBackupMsg.Left + Me.lblFromBackupMsg.Size.Width, Me.lblFromBackupMsg.Location.Y)
        Catch
        End Try
    End Sub

#Region "--- 获取数据库加密秘钥 ---"
    Private Function GetDBPwdFromServer() As String
        Dim strResult As String = ""
        Try
            If Not String.IsNullOrEmpty(mDBPwd) Then
                Return mDBPwd
            End If
            Dim strUrl As String = "http://pc.api.tongbu.com/vip/v.html?t=131"
            Dim strContent As String = Utility.GetContentStringFromUrl(strUrl, Encoding.UTF8) 'AESFunctionHelper.PostDataToUrl(Me.GetComputerInfoJson(), strUrl)
            If String.IsNullOrEmpty(strContent) Then
                Common.LogException(String.Format("获取数据库秘钥错误,返回:{0}", strContent))
                Return strResult
            End If
            Common.LogException(String.Format("131接口返回:{0}", strContent))
            Dim strDeResult As String = strContent.Replace("_@_", "+")
            strDeResult = Common.DecryptDES(strDeResult, "auismp#6", "cd233!#9")
            Dim jObj As JsonObject = JsonParser.ParseString(strDeResult)
            If jObj Is Nothing Then
                Common.LogException("解析获取的加密数据库秘钥错误")
                Return strResult
            End If

            Dim dataObject As JsonObject = Nothing
            If jObj.ContainsKey("data") AndAlso TypeOf jObj("data") Is JsonObject Then
                dataObject = jObj("data")
            Else
                Common.LogException("解析获取的加密数据库秘钥错误")
                Return strResult
            End If

            If dataObject.ContainsKey("code") AndAlso TypeOf dataObject("code") Is JsonNumber AndAlso _
              dataObject.ContainsKey("msg") AndAlso TypeOf dataObject("msg") Is JsonString Then
                Dim jCode As JsonNumber = CType(dataObject("code"), JsonNumber)
                Dim jMsg As JsonString = CType(dataObject("msg"), JsonString)     '

                If jCode.Value = 1 Then
                    strResult = jMsg.ToString()
                End If
            Else
                Common.LogException("解析获取的加密数据库秘钥错误")
                Return strResult
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_LoadInfoFromServerThread")

        End Try
        mDBPwd = strResult
        Return strResult
    End Function

#End Region


    ''' 使用iCloud备份
    '#If IS_iCloud Then

    Private m_bIsiCloud As Boolean = False
    Public WriteOnly Property IsiCloud() As Boolean
        Set(value As Boolean)
            m_bIsiCloud = value
        End Set
    End Property

    Public Event DisplayNoDataHandler()

    '#End If


End Class