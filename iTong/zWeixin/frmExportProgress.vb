﻿Public Class frmExportProgress


    Private mExportHelper As IMExportHelper
    Private mPara As IMExportPara

    Private mRecoveryHelper As WechatRecoveryHelper
    Private mParaRecovery As WeChatRecoveryPara


    Public Sub New(ByVal app As IApplication, helper As Object, para As Object)

        ' 此调用是设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mApplication = app
        Me.Language = app.Language

        If helper IsNot Nothing AndAlso TypeOf helper Is IMExportHelper AndAlso TypeOf para Is IMExportPara Then
            Me.mPara = para

            Me.mExportHelper = helper
            AddHandler Me.mExportHelper.ExportProgressChanged, AddressOf Me.OnExportProgressChanged
        Else
            Me.mParaRecovery = para

            Me.mRecoveryHelper = WechatRecoveryHelper.Instance()
            AddHandler Me.mRecoveryHelper.RecovertyProgressChanged, AddressOf Me.OnRecoveryProgressChanged
        End If
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False
        Me.btn_close.Visible = False
        'Me.tbGuiBackground = Nothing
        Me.Icon = My.Resources.iTong
        Me.Text = Me.Language.GetString("WhatsApp.Label.ExportRecover") '导出到电脑

        If Folder.AppType = RunType.iDataRecovery_Abroad OrElse Folder.AppType = RunType.iDataRecovery Then
            Me.tbGuiBackground = My.Resources.frm_bg_state1
            Me.btnClose1.tbBackgroundImage = My.Resources.btn_close1
            Me.tbTitleForeColor = Color.FromArgb(102, 102, 102)
        Else
            Me.tbGuiBackground = My.Resources.frm_bg_state
            Me.btnClose1.tbBackgroundImage = My.Resources.btn_close
            Me.tbTitleForeColor = Color.White
            Me.Text = Me.Language.GetString("File.Button.DownloadToPC")
        End If
    End Sub

    Protected Overrides Sub OnShown(e As EventArgs)
        MyBase.OnShown(e)

        If Me.mExportHelper IsNot Nothing Then
            Me.mExportHelper.Start(Me.mPara)
        End If

        If Me.mRecoveryHelper IsNot Nothing Then
            Me.mRecoveryHelper.Start(Me.mParaRecovery)
        End If
    End Sub

    '微信导出进度
    Private Sub OnExportProgressChanged(sender As Object, args As ExportProgressEventArgs)
        Me.SetProgress(sender, args)
    End Sub

    Private Sub OnRecoveryProgressChanged(sender As Object, args As WeChatRecoveryEventArgs)
        Dim argsOutPut As New ExportProgressEventArgs(ExportStatus.Exporting, Me.Language.GetString("Weixin.Message.PrepareExport"), args.Progress, args.ExportPath)
        Select Case args.Status
            Case WeChatRecoveryStatus.Start
                argsOutPut.Message = Me.Language.GetString("Weixin.Message.PrepareExport") '"准备导出"
                argsOutPut.Status = ExportStatus.Exporting

            Case WeChatRecoveryStatus.Modify
                argsOutPut.Message = Me.Language.GetString("Weixin.Message.Exporting")
                argsOutPut.Status = ExportStatus.Exporting

            Case WeChatRecoveryStatus.Failure
                argsOutPut.Message = Me.Language.GetString("Contact.Message.ExportFailure") '"导出失败"
                argsOutPut.Status = ExportStatus.ExportFailure

            Case WeChatRecoveryStatus.Succeed
                argsOutPut.Message = Me.Language.GetString("Note.Message.ExportSucceed")
                argsOutPut.Status = ExportStatus.ExportSuccess

        End Select

        Me.SetProgress(sender, argsOutPut)
    End Sub
    Private Sub SetProgress(sender As Object, ByVal args As ExportProgressEventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of ExportProgressEventArgs)(AddressOf SetProgress), sender, args)
            Else

                If Not String.IsNullOrEmpty(args.Message) Then
                    Me.lblProgress.Text = args.Message
                    args.Message = String.Empty
                Else
                    Me.lblProgress.Text = String.Format(Me.Language.GetString("Weixin.Message.ExportWechat"), args.CurrentName & String.Format(" ({0}/{1})", args.CurrentIndex, args.TotalCount))
                End If

                Me.lblIntProgress.Text = args.Progress.ToString() + "%"
                Me.prgExport.tbPlayValue = args.Progress

                Application.DoEvents()

                If args.Status = ExportStatus.ExportSuccess Then
                    Me.Hide()

                    If Me.mExportHelper IsNot Nothing Then
                        If IniSetting.GetShowWeChatExportSuccess() Then
                            Dim isIgnoreNumber As Boolean = False
                            If tbMessageBox.Show(Me.mApplication, _
                                                 Me.Language.GetString("Note.Message.ExportSucceed") & vbCrLf & Me.Language.GetString("Common.Info.SystemRelatedEx"), _
                                                 Me.Language.GetString("Common.Info"), _
                                                 MessageBoxButtons.OKCancel, _
                                                 MessageBoxIcon.Warning, _
                                                 MessageBoxDefaultButton.Button1, _
                                                 Me.Language.GetString("App.Button.IgnoreNumber"), _
                                                 isIgnoreNumber, _
                                                 New String() {Me.Language.GetString("App.Button.ShowInFile"), Me.Language.GetString("Common.OK")}) = Windows.Forms.DialogResult.OK Then
                                IniSetting.SetWeChatExportSuccessOpenFolder(True)
                            Else
                                IniSetting.SetWeChatExportSuccessOpenFolder(False)
                            End If
                            IniSetting.SetShowWeChatExportSuccess(Not isIgnoreNumber)
                        End If

                        If IniSetting.GetWeChatExportSuccessOpenFolder() Then
                            Common.OpenExplorer(Me.mPara.ExportFolderApp)
                        End If
                    End If

                    Me.Close()
                ElseIf args.Status = ExportStatus.ExportFailure Then
                    Me.Hide()

                    tbMessageBox.Show(Me.mApplication, Me.Language.GetString("Contact.Message.ExportFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1)
                    Me.Close()
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetProgress")
        End Try
    End Sub

    Private Sub btnClose1_Click(sender As Object, e As EventArgs) Handles btnClose1.Click
        Try
            '正在导出备份，您确定要取消吗?
            If tbMessageBox.Show(Me.mApplication, _
                                Me.Language.GetString("WhatsApp.Message.CancelExport"), _
                                 Me.Language.GetString("Common.Info"), _
                                 MessageBoxButtons.OKCancel, _
                                  MessageBoxIcon.Warning) = Windows.Forms.DialogResult.OK Then

                Try
                    If Me.mExportHelper IsNot Nothing Then
                        Me.mExportHelper.Stop()
                    End If
                Catch
                End Try

                Try
                    If Me.mRecoveryHelper IsNot Nothing Then
                        Me.mRecoveryHelper.Stop()
                    End If
                Catch
                End Try

                Me.Close()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnClose1_Click")
        End Try
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            If Me.mExportHelper IsNot Nothing Then
                RemoveHandler Me.mExportHelper.ExportProgressChanged, AddressOf Me.OnExportProgressChanged
            End If
        Catch
        End Try

        Try
            If Me.mRecoveryHelper IsNot Nothing Then
                RemoveHandler Me.mRecoveryHelper.RecovertyProgressChanged, AddressOf Me.OnRecoveryProgressChanged
            End If
        Catch
        End Try
    End Sub

End Class