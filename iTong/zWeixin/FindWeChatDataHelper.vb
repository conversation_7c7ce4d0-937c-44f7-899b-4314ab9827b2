﻿Imports System.Runtime.InteropServices
Imports ProtoBuf
Imports System.Web
Imports System.Net

Public Class FindWeChatDataHelper
    Implements IDisposable

    Private mLanguage As LanguageInterface = Nothing
    Private Shared mStrWCRHelperPath As String = Path.Combine(Folder.AppFolder, "tbWCRHelper.exe")
    Private Const LOWVERSION As String = "1.0.4.2"  '找回模块最小的dll，可以防止刷机dll不符合导致的问题
    Private mIsCheckVersion As Boolean = False

    Public Event WCRProgressEventHandler(ByVal sender As Object, ByVal args As FindWeChatDataEventArgs)

    Public mDictDelChatInfo As New Dictionary(Of String, List(Of WeChatChatInfo))          '被删除的聊天信息
    Public mDictDelFriendInfo As Dictionary(Of String, WeChatFriendInfo) = New Dictionary(Of String, WeChatFriendInfo) '被删除的聊天信息 对应的联系人
    Public mDictFriendInfo As Dictionary(Of String, WeChatFriendInfo)                     '所有联系人表（UserName,FriendInfo）

    Const WM_COPYDATA As Integer = &H4A
    Const SC_RESTORE As Int32 = &HF122

    Friend WithEvents mFindDataTimer As System.Timers.Timer = New System.Timers.Timer()


#Region "--- 单例 ---"

    Private Shared _lockGetInstance As New Object

    Public Sub New(ByVal strDBMMOnPC As String)
        Me.mLanguage = LanguageInterface.Instance()
    End Sub

    Private Shared mHelper As FindWeChatDataHelper = Nothing

    Public Shared Function GetInstance(ByVal strDBMMOnPC As String) As FindWeChatDataHelper
        If mHelper Is Nothing Then
            mHelper = New FindWeChatDataHelper(strDBMMOnPC)
        End If
        Return mHelper
    End Function


#End Region

#Region "--- 释放 ---"
    Private disposedValue As Boolean ' 检测冗余的调用

    ' IDisposable
    Protected Overridable Sub Dispose(disposing As Boolean)
        If Not Me.disposedValue Then
            If disposing Then
                ' TODO:  释放托管状态(托管对象)。
            End If

            ' TODO:  释放非托管资源(非托管对象)并重写下面的 Finalize()。
            ' TODO:  将大型字段设置为 null。
        End If
        Me.disposedValue = True
    End Sub

    ' TODO:  仅当上面的 Dispose(ByVal disposing As Boolean)具有释放非托管资源的代码时重写 Finalize()。
    'Protected Overrides Sub Finalize()
    '    ' 不要更改此代码。    请将清理代码放入上面的 Dispose(ByVal disposing As Boolean)中。
    '    Dispose(False)
    '    MyBase.Finalize()
    'End Sub

    ' Visual Basic 添加此代码是为了正确实现可处置模式。
    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。    请将清理代码放入上面的 Dispose (disposing As Boolean)中。
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub
#End Region

    Private mWeChatDB As WeixinMMDB = Nothing
    Private mWeChatDBPath As String = ""

    Public Function WCRFindData(ByVal strPath As String, ByVal strMD5 As String, ByVal dictFriendInfo As Dictionary(Of String, WeChatFriendInfo), ByVal dictFindSha1 As Dictionary(Of String, String)) As Boolean
        Me.mDictFriendInfo = dictFriendInfo
        Me.mDictDelChatInfo = New Dictionary(Of String, List(Of WeChatChatInfo))
        Me.mDictDelFriendInfo = New Dictionary(Of String, WeChatFriendInfo)
        Dim blnResule As Boolean = False
        Common.KillProcess("tbWCRHelper")

        If mWeChatDBPath <> strPath AndAlso mWeChatDB Is Nothing Then
            mWeChatDB = WeixinMMDB.GetInstance(strPath)
            mWeChatDBPath = strPath
        End If

        Try
            '----------兼容 \session\data----------
            Dim lstFindSessionDataFile As New List(Of String)
            Dim strKey As String = String.Format("{0}/session/data", strMD5)
            Dim strTemp As String = Folder.GetTempFilePath
            Folder.CheckFolder(strTemp)
            Dim strFindFilePath As String = ""
            Dim strTempPath As String = ""
            For Each item As KeyValuePair(Of String, String) In dictFindSha1
                Try
                    If item.Key.Contains(strKey) AndAlso Not item.Key.Contains("_@") Then
                        strFindFilePath = dictFindSha1(item.Key)
                        If File.Exists(strFindFilePath) Then
                            strTempPath = Path.Combine(strTemp, Path.GetFileName(strFindFilePath))
                            File.Copy(strFindFilePath, strTempPath)

                            Using fsRead As New FileStream(strFindFilePath, FileMode.Open)
                                Dim result As SessionDataModel = ProtoBuf.Serializer.Deserialize(Of SessionDataModel)(fsRead)
                                If result IsNot Nothing AndAlso result.SDIM IsNot Nothing Then

                                    Dim cInfo As WeChatChatInfo = New WeChatChatInfo()
                                    cInfo.IntID = result.SDIM.intId
                                    cInfo.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(result.SDIM.longDate)
                                    cInfo.IntCreateTime = result.SDIM.longDate

                                    cInfo.StrUsrName = Me.GetFindName(Common.ToHexString(Common.GetMd5Array(result.SDIM.strWXID)))
                                    Dim strMsg As String = result.SDIM.strContent

                                    If result.SDIM.strContent.Contains("Mesage:") Then
                                        strMsg = result.SDIM.strContent.ToString().Substring(result.SDIM.strContent.ToString().IndexOf("Mesage:")).TrimStart(New Char() {"M"c, "e"c, "s"c, "s"c, "a"c, "g"c, "e"c, ":"c})
                                    End If

                                    For Each iascii As Char In WeixinMMDB.LstIllegalASCII
                                        strMsg = strMsg.Replace(iascii, "")
                                    Next
                                    If strMsg.Length = 0 Then
                                        Continue For
                                    End If
                                    cInfo.StrMessage = HttpUtility.HtmlEncode(strMsg)
                                    cInfo.IsDelete = True
                                    cInfo.IntDes = result.SDIM.intDsc

                                    If mWeChatDB.Get_Chat_Info_Exist(cInfo.StrUsrName, cInfo.IntCreateTime) Then
                                        Continue For
                                    End If

                                    Me.AddDelChatInfo(Common.ToHexString(Common.GetMd5Array(result.SDIM.strWXID)), cInfo)
                                    Me.AddDelFriendInfo(Common.ToHexString(Common.GetMd5Array(result.SDIM.strWXID)), cInfo.StrUsrName)

                                End If
                            End Using
                        End If
                    End If
                Catch
                End Try
            Next
            '------------------------------
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WCRFindData")
        End Try

        Try
            If CheckWCRHelperDll() Then
                Try
                    mFindDataTimer.Interval = 5000
                    mFindDataTimer.Start()
                Catch
                End Try

                Dim strSend As String = String.Format("-i ""{0}"" --freespace -m {1}", strPath, strMD5)
                Me.DoSendMessage(strSend)
                blnResule = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WCRFindData")
        End Try

        Return blnResule
    End Function

    Private Function CheckWCRHelperDll() As Boolean

        Dim blnResult As Boolean = True
        Dim arg As FindWeChatDataEventArgs = New FindWeChatDataEventArgs(WCRType.Exception, 501, Me.mLanguage.GetString("WeChat.Message.LoseProgram")) '"数据找回程序丢失，请重装同步助手！"
        If Not File.Exists(mStrWCRHelperPath) Then
            blnResult = False
            GoTo DoExit
        End If

        Try
            Dim strWeChatHelperdll = Path.Combine(Folder.AppFolder, "WeChat\WeChatHelper.dll")
            Dim strVersion As String = FileVersionInfo.GetVersionInfo(strWeChatHelperdll).FileVersion

            Try
                If IniSetting.GetWeChatHelperdll() AndAlso ServerIniSetting.GetWeChatHelperdll(My.Application.Info.Version.ToString(4)) Then  '如果今天已经下载过就不在下载
                    '检查是否需要更新刷机dll
                    Dim strUrl As String = ServerIniSetting.GetWeChatHelperURL() '"http://qd.tongbu.com/qd/tbzsqd/plugin/WeChatHelper.dll"
                    Dim strTemp As String = strWeChatHelperdll & ".temp"
                    If Utility.DownLoadFile(strUrl, strTemp) Then
                        Try
                            File.Delete(strWeChatHelperdll)
                        Catch
                        End Try
                        Try
                            File.Move(strTemp, strWeChatHelperdll)
                        Catch
                        End Try
                        IniSetting.SetWeChatHelperdll(Date.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                    End If
                End If
            Catch
            End Try
            '-----------------------


            '校验刷机tblibidevicerestore.dll是否存在以及他的版本号是否正确
            If Not mIsCheckVersion Then
                mIsCheckVersion = True

                If Not File.Exists(strWeChatHelperdll) Then
                    blnResult = False
                    GoTo DoExit
                End If

                strVersion = FileVersionInfo.GetVersionInfo(strWeChatHelperdll).FileVersion
                Dim ver As New Version(strVersion)
                Dim verLow As New Version(LOWVERSION)
                If ver < verLow Then
                    blnResult = False
                    GoTo DoExit
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckTBFlashHerper")
        End Try

DoExit:
        If Not blnResult Then
            WCRProgress(arg)
        End If
        Return blnResult
    End Function

    Private Sub WCRProgress(arg As FindWeChatDataEventArgs)
        Try
            If arg.WType = WCRType.Abnormal OrElse arg.WType = WCRType.ErrorM OrElse arg.WType = WCRType.Exception OrElse arg.IntProgress >= 100 Then
                mFindDataTimer.Stop()
            End If

            RaiseEvent WCRProgressEventHandler(Me, arg)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "RestoreProgress")
        End Try
    End Sub

    Private Sub Timer_Tick(ByVal sender As System.Object, ByVal e As System.Timers.ElapsedEventArgs) Handles mFindDataTimer.Elapsed
        Try
            If Not Common.ProcessExist("tbWCRHelper") Then
                mFindDataTimer.Stop()
                Dim args As FindWeChatDataEventArgs = New FindWeChatDataEventArgs(WCRType.Abnormal, 0, "")
                Me.WCRProgress(args)
            End If
        Catch ex As Exception

        End Try
    End Sub

#Region "--- 发送消息 ---"

    Private Sub DoSendMessage(strSend As String)
        Dim iHandle As Integer = GetIHandle()
        Try
            Dim isOpen As Boolean = False
            If iHandle = 0 Then
                If File.Exists(mStrWCRHelperPath) Then
                    'Dim pro As New System.Diagnostics.Process
                    'pro.StartInfo.FileName = mStrWCRHelperPath
                    'pro.StartInfo.Arguments = strSend
                    'pro.Start()

                    System.Diagnostics.Process.Start(mStrWCRHelperPath, strSend)

                    isOpen = True
                End If
            End If

            If iHandle = 0 Then
                Dim intCount As Integer = 0
                While intCount < 10
                    iHandle = GetIHandle()
                    If iHandle > 0 Then
                        Exit While
                    End If
                    intCount = intCount + 1
                    Utility.WaitSeconds(1)
                End While
            End If

            If iHandle > 0 Then
                If isOpen Then
                    If Me.SendiCloverProcFormHandle() Then
                        isOpen = False
                    End If
                End If

                Dim sarr As Byte() = System.Text.Encoding.[Default].GetBytes(strSend)
                Dim len As Integer = sarr.Length
                Dim cds As COPYDATASTRUCT
                cds.dwData = CType(100, IntPtr)
                cds.lpData = strSend
                cds.cbData = len + 1
                SendMessage(iHandle, WM_COPYDATA, SC_RESTORE, cds)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoSendMessage")
        End Try
    End Sub

    <DllImport("User32.dll", EntryPoint:="SendMessage")> _
    Private Shared Function SendMessage(hWnd As Integer, Msg As Integer, wParam As Integer, ByRef lParam As COPYDATASTRUCT) As Integer
    End Function

    <DllImport("User32.dll", EntryPoint:="FindWindow")> _
    Private Shared Function FindWindow(lpClassName As String, lpWindowName As String) As Integer
    End Function

    Private Function GetIHandle() As Integer
        Dim iHandle As Integer = 0
        Try
            iHandle = FindWindow(Nothing, "WeChatHelperMain")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetIHandle")
        End Try
        Return iHandle
    End Function

    Private Function SendiCloverProcFormHandle() As Boolean
        Dim isResult As Boolean = False
        Try
            Dim ProcFormHandle As IntPtr = IntPtr.Zero
#If IS_WECHAT Then
            For Each item As Form In Application.OpenForms
                If TypeOf item Is MainFormDataRecovery Then
                    ProcFormHandle = item.Handle
                    Exit For
                End If
            Next

            ProcFormHandle = FindWindow(Nothing, MainFormDataRecovery.Instance().mStrMFDRText)

#Else
            For Each item As Form In Application.OpenForms
                If TypeOf item Is ProcForm Then
                    ProcFormHandle = item.Handle
                    Exit For
                End If
            Next
#End If



            Me.DoSendMessage("iCloverProcFormHandle" & ProcFormHandle.ToString())
            isResult = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SendiCloverProcFormHandle")
        End Try
        Return isResult
    End Function

#End Region

#Region "--- 接收消息 ---"

    Public Sub WCRDataProgress(lpData As String)
        Try
            Dim arrData As String() = lpData.Split("﹁")
            Dim wType As WCRType = GetStatusFromString(arrData(0))
            If wType = WCRType.None Then
                Return
            End If

            Dim intCodeOrProgress As Integer = 0
            Dim strContent As String = ""
            Dim isFinish As Boolean = False

            Select Case wType
                Case WCRType.ErrorM, WCRType.Exception
                    intCodeOrProgress = arrData(1)
                    strContent = arrData(2)

                Case WCRType.Progress
                    intCodeOrProgress = arrData(1)
                    If intCodeOrProgress >= 100 Then
                        isFinish = True
                    End If

                Case WCRType.Record
                    Dim strTemp As String = lpData.Replace(String.Format("{0}﹁{1}﹁", wType.ToString(), arrData(1)), "")
                    'Dim bTemp As Byte() = Encoding.Default.GetBytes(strTemp)
                    'strTemp = Encoding.UTF8().GetString(bTemp)
                    Me.WCRDataRecord(strTemp)
            End Select
            If wType <> WCRType.Record Then
                Dim args As New FindWeChatDataEventArgs(wType, intCodeOrProgress, strContent)
                If isFinish Then
                    args = New FindWeChatDataEventArgs(wType, intCodeOrProgress, strContent, Me.mDictDelChatInfo, Me.mDictDelFriendInfo, Me.mDictFriendInfo)
                End If
                Me.WCRProgress(args)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "WCRDataProgress")
        End Try
    End Sub

    Private Sub WCRDataRecord(ByVal strContent As String)
        Try
            Dim strKey As String = ""
            If strContent.StartsWith("Chat") OrElse strContent.StartsWith("noName") Then
                Dim arrItem As String() = strContent.Split(";")
                If arrItem(0).StartsWith("noName") Then
                    strKey = arrItem(0).Replace("noName", WeixinImportOrExportHelper.mStrUnknown)
                Else
                    strKey = arrItem(0).Replace("Chat_", "")
                End If

                Dim cInfo As WeChatChatInfo = New WeChatChatInfo()
                If arrItem(8) > 0 Then
                    cInfo.IntID = arrItem(8)
                Else
                    cInfo.IntID = arrItem(1)
                End If

                cInfo.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(arrItem(3))
                cInfo.IntCreateTime = arrItem(3)
                cInfo.IntStatus = arrItem(4)
                cInfo.ImgStatus = arrItem(5)
                cInfo.SCType = arrItem(6)
                cInfo.IntDes = arrItem(7)

                cInfo.StrUsrName = Me.GetFindName(strKey)
                Dim strMsg As String = strContent.ToString().Substring(strContent.ToString().IndexOf("Mesage:")).TrimStart(New Char() {"M"c, "e"c, "s"c, "s"c, "a"c, "g"c, "e"c, ":"c})
                For Each item As Char In WeixinMMDB.LstIllegalASCII
                    strMsg = strMsg.Replace(item, "")
                Next
                If strMsg.Length = 0 Then
                    Return
                End If
                cInfo.StrMessage = HttpUtility.HtmlEncode(strMsg)
                cInfo.IsDelete = True

                If Not mWeChatDB.Get_Chat_Info_Exist(cInfo.StrUsrName, cInfo.IntCreateTime, strKey) Then
                    Me.AddDelChatInfo(strKey, cInfo)
                    Me.AddDelFriendInfo(strKey, cInfo.StrUsrName)
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WCRDataRecord")
        End Try
    End Sub

    Private Function GetFindName(strMd5 As String) As String
        Dim strUserName As String = strMd5
        Dim isContain As Boolean = False
        Try
            If strUserName.StartsWith(WeixinImportOrExportHelper.mStrUnknown) Then
                GoTo Do_Exit
            End If
            For Each item As KeyValuePair(Of String, WeChatFriendInfo) In Me.mDictFriendInfo
                If item.Value.Md5 = strMd5 Then
                    strUserName = item.Value.UsrName
                    isContain = True
                    Continue For
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "FindWeChatDataHelper_GetFindName")
        End Try
        If Not isContain Then
            strUserName = WeixinImportOrExportHelper.mStrUnknown & "_" & strUserName
        End If
Do_Exit:
        Return strUserName
    End Function

    Public Shared Function GetStatusFromString(strStatus As String) As WCRType
        Dim status As WCRType = WCRType.None
        Try
            status = DirectCast([Enum].Parse(GetType(WCRType), strStatus, True), WCRType)
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
        Return status
    End Function

#End Region

    Private Sub AddDelChatInfo(strKey As String, cInfo As WeChatChatInfo)
        Try
            Dim lstWCCInfo As List(Of WeChatChatInfo) = New List(Of WeChatChatInfo)
            If Me.mDictDelChatInfo.ContainsKey(strKey) Then
                Dim LstTempWCCInfo As List(Of WeChatChatInfo) = Me.mDictDelChatInfo(strKey)
                LstTempWCCInfo.Add(cInfo)
                lstWCCInfo = LstTempWCCInfo
                Me.mDictDelChatInfo.Remove(strKey)
            Else
                lstWCCInfo.Add(cInfo)
            End If
            Me.mDictDelChatInfo.Add(strKey, lstWCCInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddDelChatInfo")
        End Try
    End Sub

    Private Sub AddDelFriendInfo(strKey As String, strUserName As String)
        Try
            If Not Me.mDictDelFriendInfo.ContainsKey(strUserName) Then
                Dim fInfo As WeChatFriendInfo = Me.GetFriendInfo(strKey)
                If strUserName.StartsWith(WeixinImportOrExportHelper.mStrUnknown) OrElse fInfo Is Nothing Then
                    fInfo = New WeChatFriendInfo()
                    fInfo.UsrName = strUserName
                    fInfo.NickName = strUserName
                    fInfo.NoteName = strUserName
                    fInfo.Md5 = strKey
                End If
                fInfo.IsFindDate = True
                If Me.mDictDelChatInfo.ContainsKey(fInfo.Md5) Then
                    fInfo.DTLastSendTime = Me.mDictDelChatInfo(fInfo.Md5)(0).DTCreateTime
                    'If strUserName.StartsWith(WeixinImportOrExportHelper.mStrUnknown) Then
                    '    fInfo.StrLastSendMsg = Me.mDictDelChatInfo(fInfo.Md5)(0).StrMessage
                    'End If
                Else
                    fInfo.DTLastSendTime = DateTime.Now
                End If

                Me.mDictDelFriendInfo.Add(strUserName, fInfo)
                If Not Me.mDictFriendInfo.ContainsKey(strUserName) Then
                    Me.mDictFriendInfo.Add(strUserName, fInfo)
                Else
                    Dim wcfInfo As WeChatFriendInfo = Me.mDictFriendInfo(strUserName)
                    Me.mDictFriendInfo.Remove(strUserName)
                    wcfInfo.IsFindDate = True
                    Me.mDictFriendInfo.Add(strUserName, wcfInfo)
                End If

                Me.WCRProgress(New FindWeChatDataEventArgs(fInfo, WCRType.Record))
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddDelFriendInfo")
        End Try
    End Sub

    Private Function GetFriendInfo(ByVal strKey As String) As WeChatFriendInfo
        Dim fInfo As WeChatFriendInfo = Nothing
        For Each item As WeChatFriendInfo In Me.mDictFriendInfo.Values
            If item.Md5 = strKey Then
                fInfo = New WeChatFriendInfo()
                fInfo.StrChatRoomMem = item.StrChatRoomMem
                fInfo.IsFindDate = item.IsFindDate
                fInfo.StrLastSendMsg = item.StrLastSendMsg
                fInfo.DTLastSendTime = item.DTLastSendTime
                fInfo.Md5 = item.Md5
                fInfo.NickName = item.NickName
                fInfo.NoteName = item.NoteName
                fInfo.StrProfile = item.StrProfile
                fInfo.IntSex = item.IntSex
                fInfo.SFType = item.SFType
                fInfo.UsrName = item.UsrName
                Exit For
            End If
        Next
        Return fInfo
    End Function

End Class

Public Class FindWeChatDataEventArgs
    Inherits EventArgs

    Dim mIntProgress As Integer = 0
    Public Property IntProgress() As Integer
        Get
            Return Me.mIntProgress
        End Get
        Set(ByVal value As Integer)
            Me.mIntProgress = value
        End Set
    End Property

    Dim mWType As WCRType = WCRType.None
    Public Property WType() As WCRType
        Get
            Return Me.mWType
        End Get
        Set(ByVal value As WCRType)
            Me.mWType = value
        End Set
    End Property

    Dim mStrMsg As String = ""
    Public Property StrMsg() As String
        Get
            Return Me.mStrMsg
        End Get
        Set(ByVal value As String)
            Me.mStrMsg = value
        End Set
    End Property

    Dim mWCFInfo As WeChatFriendInfo = Nothing
    Public Property WCFInfo() As WeChatFriendInfo
        Get
            Return Me.mWCFInfo
        End Get
        Set(ByVal value As WeChatFriendInfo)
            Me.mWCFInfo = value
        End Set
    End Property

    Dim mDictDelChatInfo As New Dictionary(Of String, List(Of WeChatChatInfo))          '被删除的聊天信息
    Public Property DictDelChatInfo() As Dictionary(Of String, List(Of WeChatChatInfo))
        Get
            Return Me.mDictDelChatInfo
        End Get
        Set(ByVal value As Dictionary(Of String, List(Of WeChatChatInfo)))
            Me.mDictDelChatInfo = value
        End Set
    End Property

    Dim mDictDelFriendInfo As New Dictionary(Of String, WeChatFriendInfo) '被删除的聊天信息 对应的联系人
    Public Property DictDelFriendInfo() As Dictionary(Of String, WeChatFriendInfo)
        Get
            Return Me.mDictDelFriendInfo
        End Get
        Set(ByVal value As Dictionary(Of String, WeChatFriendInfo))
            Me.mDictDelFriendInfo = value
        End Set
    End Property

    Dim mDictFriendInfo As New Dictionary(Of String, WeChatFriendInfo)                     '所有联系人表（UserName,FriendInfo）
    Public Property DictFriendInfo() As Dictionary(Of String, WeChatFriendInfo)
        Get
            Return Me.mDictFriendInfo
        End Get
        Set(ByVal value As Dictionary(Of String, WeChatFriendInfo))
            Me.mDictFriendInfo = value
        End Set
    End Property

    Public Sub New(ByVal wType As WCRType, ByVal intProgress As Integer, ByVal strMsg As String)
        Me.mIntProgress = intProgress
        Me.mWType = wType
        Me.mStrMsg = strMsg
    End Sub

    Public Sub New(WInfo As WeChatFriendInfo, ByVal wType As WCRType)
        Me.mWCFInfo = WInfo
        Me.mWType = wType
    End Sub
    Public Sub New(ByVal wType As WCRType, ByVal intProgress As Integer, ByVal strMsg As String, dicDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), dicDelFriendInfo As Dictionary(Of String, WeChatFriendInfo), dicFriendInfo As Dictionary(Of String, WeChatFriendInfo))
        Me.mIntProgress = intProgress
        Me.mWType = wType
        Me.mStrMsg = strMsg

        Me.mDictDelChatInfo = dicDelChatInfo
        Me.mDictDelFriendInfo = dicDelFriendInfo
        Me.mDictFriendInfo = dicFriendInfo
    End Sub
End Class

Public Enum WCRType
    None = 0
    Exception = 1
    Progress = 2
    Record = 3
    ErrorM = 4
    Abnormal = 5
End Enum

<ProtoContract> _
Public Class SessionDataModel
    '添加特性，表示该字段可以被序列化，1可以理解为下标  
    <ProtoMember(2)> _
    Public SDIM As SessionDataInfoModel
End Class

<ProtoContract> _
Public Class SessionDataInfoModel
    '添加特性，表示该字段可以被序列化，1可以理解为下标  
    <ProtoMember(2)> _
    Public intId As Integer
    <ProtoMember(5)> _
    Public intDsc As Integer
    <ProtoMember(7)> _
    Public longDate As Long
    <ProtoMember(8)> _
    Public strWXID As String
    <ProtoMember(9)> _
    Public strWXAccount As String
    <ProtoMember(10)> _
    Public strNickName As String
    <ProtoMember(11)> _
    Public strContent As String
End Class
