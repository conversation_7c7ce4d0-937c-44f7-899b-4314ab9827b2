﻿Imports System.Threading
Imports System.Collections
Imports System.IO
Imports System.Windows.Forms
Imports System.Drawing
Imports iTong.Components
Imports iTong.CoreFoundation
Imports iTong.Device

Public Class frmWeixinRepair

    '未完成

    '微信3.X，无法登录暂不兼容阿拉伯字符

    Private mWechatMMDB As WechatMMDB = Nothing                                 '主数据库（联系人和聊天记录）
    Private mWechatSessionDB As WechatSessionDB = Nothing                       '会话表（最后一次联系的时间）

    Private Const SKU As String = "com.tencent.xin"
    Private mBlnWeixin5X As Boolean = False                                                 '微信版本是不是5.X
    Private mAfcHandler As System.IntPtr = IntPtr.Zero
    Private mPathWeixinOnPC_Cache As String = String.Empty                      '保存头像
    Private mPathWeixinOnPC_Temp As String = String.Empty                       '保存数据库、图片、语音等（涉及到隐私，程序关闭则会删除）
    Private mPathWeixinOnPhone As String = String.Empty
    Private mUsrNameMyself As String = String.Empty
    Private mMD5Myself As String = String.Empty
    Private mThreadRepairWeixin As Thread = Nothing

    Private Enum RepairStatus
        Start
        Failure
        Finished
    End Enum

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice)
        MyBase.New(application, device)

        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mApplication = application
        Me.Language = application.Language
        Me.FunctionMappingKey = FunctionKey.Weixin
        Me.tbAutoSetFormSize = True
        Me.FilletRadius = 5

        Me.Size = New Size(430, 260)
        Me.Icon = My.Resources.iTong
        Me.btnStart.Tag = RepairStatus.Start

        Me.btn_normal.Visible = False
        Me.btn_minimize.Visible = False

        Me.lblMessage.Text = String.Empty
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        '如果选中的是设备结点，则要切换到本地所有软件结点


        '关闭窗体
        Me.Close()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            If Me.bgwLoadDB.IsBusy() Then
                Me.bgwLoadDB.CancelAsync()
            End If
        Catch
        End Try

        Try
            If Me.mThreadRepairWeixin IsNot Nothing AndAlso Me.mThreadRepairWeixin.ThreadState <> ThreadState.Stopped Then
                Me.mThreadRepairWeixin.Abort()
            End If
        Catch
        End Try

        Me.DisposeWeixinDB()
    End Sub

    Private Sub DisposeWeixinDB()
        If Me.mWechatMMDB IsNot Nothing Then
            Me.mWechatMMDB.Dispose()
        End If

        If Me.mWechatSessionDB IsNot Nothing Then
            Me.mWechatSessionDB.Dispose()
        End If
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnStart_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStart.Click
        If Me.btnStart.Tag Is Nothing Then
            Me.Close()
        End If

        Dim type As RepairStatus = Me.btnStart.Tag
        Select Case type
            Case RepairStatus.Start
                Me.LoadDB()

            Case RepairStatus.Failure
                Me.LoadDB()

            Case RepairStatus.Finished
                Me.Close()
        End Select

    End Sub

#End Region

#Region "--- 下载数据库 ---"

    Private Sub LoadDB()
        Try
            Me.btnStart.Enabled = False
            Me.btn_close.Enabled = False

            If Me.bgwLoadDB.IsBusy() Then
                Me.bgwLoadDB.CancelAsync()
            End If

            Me.bgwLoadDB.RunWorkerAsync()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadDB")
        End Try
    End Sub

    Private Sub bgwLoadDB_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwLoadDB.DoWork
        Me.ShowStateText(Me.Language.GetString("Weixin.Lable.LoadData"))  '"正在获取微信数据"
        Dim state As WeixinState = WeixinState.Succeed

        Try

            Me.DisposeWeixinDB()

            '1、判断微信是否安装

            Me.mAfcHandler = Me.mDevice.GetAfcByFileSharing(SKU, HouseArrestType.None, True)

            If Me.mAfcHandler = System.IntPtr.Zero Then
                state = WeixinState.NoInstalled
                GoTo DO_EXIT
            End If

            '2、获取微信是否为5.X
            Me.mBlnWeixin5X = Me.IsWeixin5X(Me.mAfcHandler)

            '3、获取登录用户名
            Dim strPathWeixinOnPC As String = String.Format("{0}Weixin", Folder.CacheFolder)
            Folder.CheckFolder(strPathWeixinOnPC)
            Dim strLocalInfoOnPhone As String = "/Documents/LocalInfo.lst"
            Dim strLocalInfoOnPC As String = Path.Combine(strPathWeixinOnPC, "LocalInfo.lst")

            If Me.mDevice.Exists(Me.mAfcHandler, strLocalInfoOnPhone) AndAlso Me.mDevice.DownFromPhone(Me.mAfcHandler, strLocalInfoOnPhone, strLocalInfoOnPC) Then
                Dim strContent As String = iTong.Device.CoreFoundation.ReadPlist(strLocalInfoOnPC)
                strContent = strContent.Replace("$", "")

                Dim arrBytes As Byte() = System.Text.Encoding.Default.GetBytes(strContent)
                Dim dict As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(arrBytes)

                Dim arrObjects As Object() = dict("objects")
                Me.mUsrNameMyself = arrObjects(2).ToString()
            End If

            If String.IsNullOrEmpty(Me.mUsrNameMyself) Then
                state = WeixinState.NoAccount
                GoTo DO_EXIT
            End If

            '4、判断是否有登录
            Me.mMD5Myself = Common.ToHexString(Common.GetMd5Array(mUsrNameMyself))
            Me.mPathWeixinOnPhone = String.Format("/Documents/{0}", Me.mMD5Myself)

            If Not Me.mDevice.Exists(Me.mAfcHandler, Me.mPathWeixinOnPhone) Then
                state = WeixinState.NoAccount
                GoTo DO_EXIT
            End If

            '5、下载MM
            If Me.DownloadMMDB(state) Then
                '下载session
                Me.DownloadSessionDB(state)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadFriend_DoWork")
            state = WeixinState.NoDB
        End Try

DO_EXIT:
        e.Result = state
    End Sub

    Private Sub bgwLoadDB_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwLoadDB.RunWorkerCompleted
        Dim state As WeixinState = CType(e.Result, WeixinState)

        '数据库下载失败

        If state <> WeixinState.Succeed Then
            Me.ShowLoadDBStateText(state)
            Return
        End If

        '数据库下载成功

        Me.RepairWeixin()
    End Sub

    '下载MM.splite数据库

    Private Function DownloadMMDB(ByRef state As WeixinState) As Boolean
        Dim blnResult As Boolean = False

        Try
            Dim strDBMMOnPhone As String = String.Format("{0}/DB/MM.sqlite", Me.mPathWeixinOnPhone)
            Me.mPathWeixinOnPC_Temp = String.Format("{0}\Weixin\{1}\{2}", Folder.TempFolder, Me.mDevice.Identifier, Me.mMD5Myself)
            Me.mPathWeixinOnPC_Cache = String.Format("{0}\Weixin\{1}\{2}", Folder.CacheFolder, Me.mDevice.Identifier, Me.mMD5Myself)

            Dim strDirDBOnPC As String = String.Format("{0}\DB", Me.mPathWeixinOnPC_Temp)
            Folder.CheckFolder(strDirDBOnPC)
            Dim strDBMMOnPC As String = Path.Combine(strDirDBOnPC, "MM.sqlite")

            If Not Me.mDevice.Exists(Me.mAfcHandler, strDBMMOnPhone) Then
                state = WeixinState.NoAccount
            Else
                '1、删除旧数据库DB
                Dim dateDBPhone As String = Me.GetDBTimeOnPhone(strDBMMOnPhone)
                If File.Exists(strDBMMOnPC) Then
                    Dim dateDBPC As String = IniSetting.GetMMDate()

                    If String.Compare(dateDBPC, dateDBPhone, True) <> 0 Then
                        Try
                            File.Delete(strDBMMOnPC)
                        Catch
                        End Try
                    End If
                End If

                '2、重新下载数据库DB
                If Not File.Exists(strDBMMOnPC) Then
                    If Me.mDevice.DownFromPhone(Me.mAfcHandler, strDBMMOnPhone, strDBMMOnPC) Then
                        IniSetting.SetMMDate(dateDBPhone)
                    Else
                        state = WeixinState.DownloadDBFailed
                    End If
                End If

                If File.Exists(strDBMMOnPC) Then
                    Me.mWechatMMDB = WechatMMDB.Instance(strDBMMOnPC)
                    blnResult = True
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadMMDB")
            state = WeixinState.NoDB
        End Try

        Return blnResult
    End Function

    '下载session.db 数据库

    Private Function DownloadSessionDB(ByRef state As WeixinState) As Boolean
        Dim blnResult As Boolean = False

        Try
            Dim strDBSessionOnPhone As String = String.Format("{0}/session/session.db", Me.mPathWeixinOnPhone)
            Dim strDirDBSessionOnPC As String = String.Format("{0}\session", Me.mPathWeixinOnPC_Temp)
            Folder.CheckFolder(strDirDBSessionOnPC)
            Dim strDBSessionOnPC As String = Path.Combine(strDirDBSessionOnPC, "session.db")
            Dim dateDBPhone As String = Me.GetDBTimeOnPhone(strDBSessionOnPhone)

            '1、删除本地旧数据库

            If File.Exists(strDBSessionOnPC) Then
                Dim dateDBPC As String = IniSetting.GetSessionDate()

                If String.Compare(dateDBPC, dateDBPhone, True) <> 0 Then
                    Try
                        File.Delete(strDBSessionOnPC)
                    Catch
                    End Try
                End If
            End If

            '2、下载数据库
            If Not File.Exists(strDBSessionOnPC) Then
                If Me.mDevice.DownFromPhone(Me.mAfcHandler, strDBSessionOnPhone, strDBSessionOnPC) Then
                    IniSetting.SetSessionDate(dateDBPhone)
                Else
                    state = WeixinState.DownloadDBFailed
                End If
            End If

            If File.Exists(strDBSessionOnPC) Then
                blnResult = True
                Me.mWechatSessionDB = WechatSessionDB.Instance(strDBSessionOnPC)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadSessionDB")
            state = WeixinState.NoDB
        End Try

        Return blnResult
    End Function

#End Region

#Region "--- 修复 ---"

    Private Sub RepairWeixin()
        Try
            If Me.mThreadRepairWeixin IsNot Nothing AndAlso Me.mThreadRepairWeixin.ThreadState <> ThreadState.Stopped Then
                Me.mThreadRepairWeixin.Abort()
            End If
        Catch
        End Try

        Me.mThreadRepairWeixin = New Thread(AddressOf RepairWeixinInThread)
        With Me.mThreadRepairWeixin
            .IsBackground = True
            .Start()
        End With
    End Sub

    Private Sub RepairWeixinInThread()
        Try
            Me.ShowStateText("正在查找阿拉伯字符串")

            '1、替换特殊字符

            Dim lstTableName As List(Of String) = Me.UpdateProblemTables()
            If lstTableName.Count = 0 Then
                Me.ShowResult(False, "没有找到阿拉伯字符串")
                Return
            End If

            Me.ShowStateText("正在修复")

            '2、上传修改过的MM.sqlite数据库

            If Not Me.UploadMMDatabase() Then
                Me.ShowResult(False, "修复失败")
                Common.LogException("上传数据库1失败", "微信闪退修复")
                Return
            End If

            '3、删除session中对应的Data文件
            If Me.DeleteDataFiles(lstTableName) Then
                Me.ShowResult(True, "修复成功,请尝试重新打开微信")
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "RepairWeixinInThread")
        End Try
    End Sub

    '修改包含阿拉伯字符的chat表

    '并返回这些chat的表名

    Private Function UpdateProblemTables() As List(Of String)
        Dim lstTableName As New List(Of String)

        Try
            Dim lstSpecialStrings As List(Of String) = Me.GetSpecialStrings()
            Dim strReplaceString As String = "阿拉伯字符串"
            Dim lstAllChatTable As List(Of String) = Me.mWechatMMDB.Get_Chat_Table()

            For Each Item As String In lstAllChatTable
                For Each strSpecialString As String In lstSpecialStrings
                    Dim blnUpdateResult As String = Me.mWechatMMDB.Update_Chat_Info_ByTableName(Item, strSpecialString, strReplaceString)

                    If blnUpdateResult AndAlso Not lstTableName.Contains(Item) Then
                        lstTableName.Add(Item)
                    End If
                Next
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetProblemTables")
        End Try

        Return lstTableName
    End Function

    '上传修改过的MM数据库到手机
    Private Function UploadMMDatabase() As Boolean
        Dim blnCopyResult As Boolean = False

        Try
            Dim strDBMMOnPhone As String = String.Format("{0}/DB/MM.sqlite", Me.mPathWeixinOnPhone)
            Dim strDirDBOnPC As String = String.Format("{0}\DB", Me.mPathWeixinOnPC_Temp)
            Dim strDBMMOnPC As String = Path.Combine(strDirDBOnPC, "MM.sqlite")

            If File.Exists(strDBMMOnPC) Then
                Dim strDBTemp As String = Path.Combine(strDirDBOnPC, "MM1.sqlite")
                File.Copy(strDBMMOnPC, strDBTemp, True)

                If File.Exists(strDBTemp) Then
                    blnCopyResult = Me.mDevice.CopyToPhone(Me.mAfcHandler, strDBTemp, strDBMMOnPhone)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UploadProblemTables")
        End Try

        Return blnCopyResult
    End Function

    '删除session中对应的data文件
    Private Function DeleteDataFiles(ByVal lstTableName As List(Of String)) As Boolean
        Dim result As Boolean = True

        Try
            If Me.mBlnWeixin5X Then
                '微信5.0
                Dim lstDeleteFiles As New List(Of String)  '需要删除的文件列表
                Dim strDirDBSessionOnPC As String = String.Format("{0}\session", Me.mPathWeixinOnPC_Temp)
                Dim strDBSessionOnPC As String = Path.Combine(strDirDBSessionOnPC, "session.db")

                If Not File.Exists(strDBSessionOnPC) Then
                    Me.ShowResult(False, "修复失败")
                    Common.LogException("数据库2不存在", "微信闪退修复")
                    result = False
                    Return result
                End If

                If Me.mWechatSessionDB Is Nothing Then
                    Me.mWechatSessionDB = WechatSessionDB.Instance(strDBSessionOnPC)
                End If

                Dim dict As Dictionary(Of String, SessionInfo) = Me.mWechatSessionDB.Get_Session_AllInfo()

                '获取需要删除的lstDeleteFiles列表
                lstDeleteFiles = Me.GetDataFiles(lstTableName, dict)

                '4、开始删除（ConStrRes1一般还包含一个副本）
                For Each Item As String In lstDeleteFiles
                    If String.IsNullOrEmpty(Item) Then
                        Continue For
                    End If

                    Dim strSessionDataFile As String = Me.mPathWeixinOnPhone & Item
                    Dim strSessionDataFolder As String = Me.mDevice.GetDirectoryName(strSessionDataFile)
                    Dim strSessionDataName As String = Me.mDevice.GetFileName(strSessionDataFile)
                    Dim arrSessionDataFiles As String() = Me.mDevice.GetFiles(Me.mAfcHandler, strSessionDataFolder)

                    For Each strSessionDataTempName As String In arrSessionDataFiles
                        If strSessionDataTempName.Contains(strSessionDataName) Then
                            strSessionDataFile = strSessionDataFolder & "/" & strSessionDataTempName

                            Me.mDevice.DeleteFile(Me.mAfcHandler, strSessionDataFile)
                        End If
                    Next
                Next
            Else
                '微信4.0
                Dim strSessionDataFolder As String = Me.mPathWeixinOnPhone & "/session/data"

                If Me.mDevice.Exists(Me.mAfcHandler, strSessionDataFolder) Then
                    Me.mDevice.DeleteDirectory(Me.mAfcHandler, strSessionDataFolder, True)
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeleteDataFiles")
            result = False
        End Try

        Return result
    End Function

    '兼容了QQ离线消息
    Private Function GetDataFiles(ByVal lstTableName As List(Of String), ByVal dict As Dictionary(Of String, SessionInfo)) As List(Of String)
        Dim lstDataFiles As New List(Of String)  '需要删除的文件列表

        If lstTableName Is Nothing OrElse dict Is Nothing Then
            Return lstDataFiles
        End If

        '原理：

        'lstTableName中的表有存在在dict中，则删除其对应的data文件；

        '如果没有存在，可能是qq离线消息用户（暂时这样处理）。


        Try
            Dim dicSessionChats As New Dictionary(Of String, String)             'key:UsrName对应的Chat表 value:ConStrRes1
            For Each strUsrName As String In dict.Keys
                Dim strMD5 As String = Me.GetFriendMD5(strUsrName)
                Dim strTableName As String = String.Format("Chat_{0}", strMD5)
                Dim info As SessionInfo = dict(strUsrName)
                dicSessionChats.Add(strTableName, info.ConStrRes1)
            Next

            Dim blnHasSepcialTable As Boolean = False '有问题的table，是否都对应session中的用户
            For Each Item As String In lstTableName
                If dicSessionChats.ContainsKey(Item) Then
                    lstDataFiles.Add(dicSessionChats(Item))
                Else
                    blnHasSepcialTable = True
                End If
            Next

            If blnHasSepcialTable Then
                '添加QQ离线消息对应的Data文件
                Dim strUsrName As String = "qmessage"
                If dict.ContainsKey(strUsrName) Then
                    Dim info As SessionInfo = dict(strUsrName)
                    lstDataFiles.Add(info.ConStrRes1)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetDataFiles")
        End Try

        Return lstDataFiles
    End Function

#End Region

#Region "--- 界面变化 ---"

    Private Delegate Sub ShowStateTextHandler(ByVal strText As String)
    Private Sub ShowStateText(ByVal strText As String)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowStateTextHandler(AddressOf ShowStateText), strText)
        Else
            Me.lblMessage.ForeColor = Color.Black
            Me.lblMessage.Text = strText
        End If
    End Sub

    '失败的情况

    Private Sub ShowLoadDBStateText(ByVal state As WeixinState)
        Dim strText As String = String.Empty

        Select Case state
            Case WeixinState.NoInstalled
                strText = Me.Language.GetString("Weixin.Lable.NotInstall") '"尚未安装微信"

            Case WeixinState.NoAccount
                strText = Me.Language.GetString("Weixin.Lable.NotLogin") '"尚未登录微信"

            Case WeixinState.NoDB
                strText = Me.Language.GetString("Weixin.Lable.LoadDataFailed") '"获取微信数据失败"

            Case WeixinState.DownloadDBFailed
                strText = Me.Language.GetString("Weixin.Lable.LoadDataFailed") ' "获取微信数据失败"
        End Select

        If Not String.IsNullOrEmpty(strText) Then
            Me.ShowResult(False, strText)
        End If
    End Sub

    Private Delegate Sub ShowResultHandler(ByVal result As Boolean, ByVal strText As String)
    Private Sub ShowResult(ByVal result As Boolean, ByVal strText As String)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowResultHandler(AddressOf ShowResult), result, strText)
        Else
            If result Then
                Me.lblMessage.Text = strText
                Me.lblMessage.ForeColor = Color.Green
                Me.btnStart.Text = Me.Language.GetString("Media.Message.Finished")                  '"完成"
                Me.btnStart.Tag = RepairStatus.Finished

            Else
                Me.lblMessage.Text = strText & "，如有问题请加入客服QQ群：291311056"
                Me.lblMessage.ForeColor = Color.Red
                Me.btnStart.Text = Me.Language.GetString("Common.Retry")                            '"重试"
                Me.btnStart.Tag = RepairStatus.Failure
            End If

            Me.btnStart.Enabled = True
            Me.btn_close.Enabled = True
        End If
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function GetFriendMD5(ByVal usrName As String) As String
        Dim md5 As String = String.Empty

        If String.IsNullOrEmpty(usrName) Then
            Return md5
        End If

        Try
            md5 = Common.ToHexString(Common.GetMd5Array(usrName))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetFriendMD5")
        End Try

        Return md5
    End Function

    '判断微信是否为5.X
    Private Function IsWeixin5X(ByVal AfcHandler As System.IntPtr) As Boolean
        Dim blnWeixin5X As Boolean = False

        Try
            Dim files As String() = Me.mDevice.GetFiles(AfcHandler, "/", True)
            Dim strAppPathOnPhone As String = String.Empty

            For Each Item As String In files
                If Item.EndsWith(".app") Then
                    strAppPathOnPhone = Item
                    Exit For
                End If
            Next

            If String.IsNullOrEmpty(strAppPathOnPhone) Then
                Return blnWeixin5X
            End If

            Dim strInfoOnPhone As String = "/" & strAppPathOnPhone & "/Info.plist"
            Dim strPathWeixinOnPC As String = String.Format("{0}Weixin", Folder.CacheFolder)
            Folder.CheckFolder(strPathWeixinOnPC)
            Dim strInfoOnPC As String = Path.Combine(strPathWeixinOnPC, Guid.NewGuid.ToString("N") & ".plist")

            If Me.mDevice.Exists(Me.mAfcHandler, strInfoOnPhone) AndAlso Me.mDevice.DownFromPhone(Me.mAfcHandler, strInfoOnPhone, strInfoOnPC) Then
                Dim dict As Dictionary(Of Object, Object) = CType(iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(strInfoOnPC), Dictionary(Of Object, Object))
                If dict IsNot Nothing Then
                    If dict.ContainsKey("CFBundleVersion") Then
                        Dim strVersion As String = CStr(dict.Item("CFBundleVersion"))
                        If Common.CompareVer(strVersion, "*******", 4) >= 0 Then
                            blnWeixin5X = True
                        End If
                    End If
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "IsWeixin5X")
        End Try

        Return blnWeixin5X
    End Function

    Private Function GetDBTimeOnPhone(ByVal strDBOnPhone As String)
        Dim dateDBPhone As String = String.Empty
        Dim iPhoneFileInfo As iPhoneFileInfo = Me.mDevice.GetFileInfo(Me.mAfcHandler, strDBOnPhone)

        If iPhoneFileInfo IsNot Nothing Then
            dateDBPhone = iPhoneFileInfo.st_mtime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))
        End If

        Return dateDBPhone
    End Function

#End Region

#Region "--- 配置文件 ---"

    'Private Function GetSpecialStrings() As List(Of String)
    '    Dim lstValues As New List(Of String)
    '    Dim strIniFile As String = Path.Combine(Folder.AppFolder, "Weixin.plist")

    '    If Not File.Exists(strIniFile) Then
    '        Return lstValues
    '    End If

    '    Try
    '        Dim dictResult As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(strIniFile)

    '        If dictResult IsNot Nothing AndAlso dictResult.ContainsKey("SpecialStrings") Then
    '            Dim arrResults As Object() = dictResult("SpecialStrings")

    '            If arrResults IsNot Nothing Then
    '                For Each Item As String In arrResults
    '                    lstValues.Add(Item)
    '                Next
    '            End If

    '        End If

    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "GetSpecialStrings")
    '    End Try

    '    Return lstValues
    'End Function

    'Private Sub SetSpecialStrings(ByVal lstValues As List(Of String))
    '    If lstValues IsNot Nothing AndAlso lstValues.Count = 0 Then
    '        Return
    '    End If

    '    Try
    '        Dim dict As New Dictionary(Of Object, Object)
    '        Dim arrValues As New ArrayList

    '        For Each Item As String In lstValues
    '            arrValues.Add(Item)
    '        Next

    '        dict.Add("SpecialStrings", arrValues)

    '        Dim strIniFile As String = Path.Combine(Folder.AppFolder, "Weixin.plist")
    '        iTong.Device.CoreFoundation.WritePlist(dict, strIniFile)

    '        dict.Clear()
    '        dict = Nothing

    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "SetSpecialStrings")
    '    End Try
    'End Sub

    Private Function GetSpecialStrings() As List(Of String)
        Dim lstSpecialStrings As New List(Of String)

        Try
            Dim strOldTextAll As String = "سمَـَّوُوُحخ ̷̴̐خ ̷̴̐خ ̷̴̐خ امارتيخ ̷̴̐خ"
            Dim strOldText As String = "خ ̷̴̐"
            lstSpecialStrings.Add(strOldText)

            '与上面的不一样

            Dim strOldText1All As String = "سمَـَّوُوُحخ ̷̴̐خ ̷̴̐خ ̷̴̐خ امارتيخ ̷̴̐خ"
            Dim strOldText1 As String = "خ ̷̴̐"
            lstSpecialStrings.Add(strOldText1)

            Dim lstValues As List(Of String) = Me.GetSpecialStringsFromIni()
            For Each Item As String In lstValues
                If String.IsNullOrEmpty(Item) Then
                    Continue For
                End If

                If Not lstSpecialStrings.Contains(Item) Then
                    lstSpecialStrings.Add(Item)
                End If
            Next
        Catch
        End Try

        Return lstSpecialStrings
    End Function

    Private Function GetSpecialStringsFromIni() As List(Of String)
        Dim lstValues As New List(Of String)
        Dim strValues As String = ServerIniSetting.GetWeixinSpecialStrings()
        Dim arrValues As String() = strValues.Split(New Char() {";"c}, StringSplitOptions.RemoveEmptyEntries)

        If arrValues Is Nothing OrElse arrValues.Length <= 0 Then
            Return lstValues
        End If

        For Each Item As String In arrValues
            Dim strTemp As String = Me.ToStringFormBase64(Item)

            If Not String.IsNullOrEmpty(strTemp) Then
                lstValues.Add(strTemp)
            End If
        Next

        Return lstValues
    End Function

    Private Function ToBase64FromString(ByVal strValue As String) As String
        Dim result As String = ""
        If String.IsNullOrEmpty(strValue) Then
            Return result
        End If

        Try
            Dim arrBytes As Byte() = System.Text.Encoding.UTF8.GetBytes(strValue)
            result = System.Convert.ToBase64String(arrBytes)
        Catch
        End Try

        Return result
    End Function

    Private Function ToStringFormBase64(ByVal strValue As String) As String
        Dim result As String = ""
        If String.IsNullOrEmpty(strValue) Then
            Return result
        End If

        Try
            Dim arrBytes As Byte() = System.Convert.FromBase64String(strValue)
            result = System.Text.Encoding.UTF8.GetString(arrBytes)
        Catch
        End Try
        
        Return result
    End Function

#End Region

End Class

