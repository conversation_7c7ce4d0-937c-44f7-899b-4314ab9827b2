﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmExportGroup
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmExportGroup))
        Me.btn_close = New iTong.Components.tbButton()
        Me.btnCancel = New iTong.Components.tbButton()
        Me.pnlContent = New iTong.Components.tbPanel()
        Me.txtSearchChat = New iTong.Components.tbSearch()
        Me.btnExport = New iTong.Components.tbButton()
        Me.cmsExportEx = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiPicEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAudioEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiVideoEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlLoading = New iTong.Components.tbPanel()
        Me.lblLoadData = New iTong.Components.tbLabel()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.lblSearchNoResult = New iTong.Components.tbLabel()
        Me.bgwLoad = New System.ComponentModel.BackgroundWorker()
        Me.cmsExportEx.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(574, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 10
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.Location = New System.Drawing.Point(326, 458)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(123, 33)
        Me.btnCancel.TabIndex = 44
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbProgressValue = 50
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowDot = False
        Me.btnCancel.tbShowMoreIconImg = CType(resources.GetObject("btnCancel.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowProgress = False
        Me.btnCancel.tbShowTip = True
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'pnlContent
        '
        Me.pnlContent.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContent.BackColor = System.Drawing.Color.Transparent
        Me.pnlContent.Location = New System.Drawing.Point(12, 80)
        Me.pnlContent.Name = "pnlContent"
        Me.pnlContent.Size = New System.Drawing.Size(576, 358)
        Me.pnlContent.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContent.TabIndex = 45
        Me.pnlContent.tbBackgroundImage = Nothing
        Me.pnlContent.tbShowWatermark = False
        Me.pnlContent.tbSplit = "0,0,0,0"
        Me.pnlContent.tbWatermark = Nothing
        Me.pnlContent.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContent.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'txtSearchChat
        '
        Me.txtSearchChat.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearchChat.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchChat.ForeColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.txtSearchChat.Location = New System.Drawing.Point(12, 51)
        Me.txtSearchChat.MaxLength = 32767
        Me.txtSearchChat.Name = "txtSearchChat"
        Me.txtSearchChat.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchChat.ReadOnly = False
        Me.txtSearchChat.SearchText = ""
        Me.txtSearchChat.SearchTipText = "Search"
        Me.txtSearchChat.ShowClear = True
        Me.txtSearchChat.ShowClearAlways = False
        Me.txtSearchChat.ShowMore = False
        Me.txtSearchChat.ShowSearch = True
        Me.txtSearchChat.Size = New System.Drawing.Size(576, 23)
        Me.txtSearchChat.TabIndex = 46
        Me.txtSearchChat.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchChat.Timer = True
        Me.txtSearchChat.TimerInterval = 0.5R
        '
        'btnExport
        '
        Me.btnExport.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnExport.Location = New System.Drawing.Point(465, 458)
        Me.btnExport.Margin = New System.Windows.Forms.Padding(0)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(123, 33)
        Me.btnExport.TabIndex = 85
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = False
        Me.btnExport.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = True
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Nothing
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnExport.tbIconMore = False
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 3
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbProgressValue = 50
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowDot = False
        Me.btnExport.tbShowMoreIconImg = Nothing
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowProgress = False
        Me.btnExport.tbShowTip = True
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "导出到电脑"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.White
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.White
        Me.btnExport.tbTextColorDown = System.Drawing.Color.White
        Me.btnExport.tbTextColorHover = System.Drawing.Color.White
        Me.btnExport.tbTextMouseDownPlace = 0
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("宋体", 12.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.VisibleEx = True
        '
        'cmsExportEx
        '
        Me.cmsExportEx.AccessibleDescription = "153x92"
        Me.cmsExportEx.DropShadowEnabled = False
        Me.cmsExportEx.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsExportEx.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiPicEx, Me.tsmiAudioEx, Me.tsmiVideoEx})
        Me.cmsExportEx.Name = "munOperate"
        Me.cmsExportEx.Size = New System.Drawing.Size(125, 70)
        Me.cmsExportEx.tbBackColor = System.Drawing.Color.White
        Me.cmsExportEx.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsExportEx.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsExportEx.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiPicEx
        '
        Me.tsmiPicEx.Name = "tsmiPicEx"
        Me.tsmiPicEx.Size = New System.Drawing.Size(124, 22)
        Me.tsmiPicEx.Text = "导出图片"
        '
        'tsmiAudioEx
        '
        Me.tsmiAudioEx.Name = "tsmiAudioEx"
        Me.tsmiAudioEx.Size = New System.Drawing.Size(124, 22)
        Me.tsmiAudioEx.Text = "导出语音"
        '
        'tsmiVideoEx
        '
        Me.tsmiVideoEx.Name = "tsmiVideoEx"
        Me.tsmiVideoEx.Size = New System.Drawing.Size(124, 22)
        Me.tsmiVideoEx.Text = "导出视频"
        '
        'pnlLoading
        '
        Me.pnlLoading.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoading.BackColor = System.Drawing.Color.Transparent
        Me.pnlLoading.Controls.Add(Me.lblLoadData)
        Me.pnlLoading.Controls.Add(Me.PictureBox2)
        Me.pnlLoading.Location = New System.Drawing.Point(12, 80)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(576, 358)
        Me.pnlLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoading.TabIndex = 86
        Me.pnlLoading.tbBackgroundImage = Nothing
        Me.pnlLoading.tbShowWatermark = False
        Me.pnlLoading.tbSplit = "0,0,0,0"
        Me.pnlLoading.tbWatermark = Nothing
        Me.pnlLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoadData
        '
        Me.lblLoadData.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblLoadData.BackColor = System.Drawing.Color.Transparent
        Me.lblLoadData.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblLoadData.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblLoadData.Location = New System.Drawing.Point(11, 195)
        Me.lblLoadData.Name = "lblLoadData"
        Me.lblLoadData.Size = New System.Drawing.Size(554, 21)
        Me.lblLoadData.TabIndex = 35
        Me.lblLoadData.tbAdriftWhenHover = False
        Me.lblLoadData.tbAutoEllipsis = False
        Me.lblLoadData.tbAutoSize = False
        Me.lblLoadData.tbHideImage = False
        Me.lblLoadData.tbIconImage = Nothing
        Me.lblLoadData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLoadData.tbIconPlaceText = 5
        Me.lblLoadData.tbShadow = False
        Me.lblLoadData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLoadData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLoadData.tbShowScrolling = False
        Me.lblLoadData.Text = "数据加载中，请稍候..."
        Me.lblLoadData.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox2
        '
        Me.PictureBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.PictureBox2.Image = Global.iTong.My.Resources.Resources.gif_loading_32
        Me.PictureBox2.Location = New System.Drawing.Point(272, 143)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(32, 32)
        Me.PictureBox2.TabIndex = 34
        Me.PictureBox2.TabStop = False
        '
        'lblSearchNoResult
        '
        Me.lblSearchNoResult.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSearchNoResult.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblSearchNoResult.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblSearchNoResult.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblSearchNoResult.Location = New System.Drawing.Point(23, 240)
        Me.lblSearchNoResult.Name = "lblSearchNoResult"
        Me.lblSearchNoResult.Size = New System.Drawing.Size(554, 21)
        Me.lblSearchNoResult.TabIndex = 87
        Me.lblSearchNoResult.tbAdriftWhenHover = False
        Me.lblSearchNoResult.tbAutoEllipsis = False
        Me.lblSearchNoResult.tbAutoSize = False
        Me.lblSearchNoResult.tbHideImage = False
        Me.lblSearchNoResult.tbIconImage = Nothing
        Me.lblSearchNoResult.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSearchNoResult.tbIconPlaceText = 5
        Me.lblSearchNoResult.tbShadow = False
        Me.lblSearchNoResult.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSearchNoResult.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSearchNoResult.tbShowScrolling = False
        Me.lblSearchNoResult.Text = "搜索不到结果"
        Me.lblSearchNoResult.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bgwLoad
        '
        Me.bgwLoad.WorkerSupportsCancellation = True
        '
        'frmExportGroup
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(600, 500)
        Me.Controls.Add(Me.lblSearchNoResult)
        Me.Controls.Add(Me.pnlLoading)
        Me.Controls.Add(Me.btnExport)
        Me.Controls.Add(Me.txtSearchChat)
        Me.Controls.Add(Me.pnlContent)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btn_close)
        Me.Name = "frmExportGroup"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,55"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "frmExportGroup"
        Me.cmsExportEx.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents btnCancel As iTong.Components.tbButton
    Friend WithEvents pnlContent As iTong.Components.tbPanel
    Friend WithEvents txtSearchChat As iTong.Components.tbSearch
    Friend WithEvents btnExport As iTong.Components.tbButton
    Friend WithEvents cmsExportEx As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiPicEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAudioEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiVideoEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlLoading As iTong.Components.tbPanel
    Friend WithEvents lblLoadData As iTong.Components.tbLabel
    Friend WithEvents PictureBox2 As System.Windows.Forms.PictureBox
    Friend WithEvents lblSearchNoResult As iTong.Components.tbLabel
    Friend WithEvents bgwLoad As System.ComponentModel.BackgroundWorker
End Class
