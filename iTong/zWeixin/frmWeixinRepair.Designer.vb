﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeixinRepair
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeixinRepair))
        Me.btnClose = New iTong.Components.tbButton
        Me.lblDiscription = New iTong.Components.tbLabel
        Me.btnStart = New iTong.Components.tbButton
        Me.pbImage = New System.Windows.Forms.PictureBox
        Me.lblTitle = New iTong.Components.tbLabel
        Me.lblMessage = New System.Windows.Forms.Label
        Me.bgwLoadDB = New System.ComponentModel.BackgroundWorker
        CType(Me.pbImage, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(406, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(382, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(358, 0)
        '
        'btnClose
        '
        Me.btnClose.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClose.BackColor = System.Drawing.Color.Transparent
        Me.btnClose.BindingForm = Nothing
        Me.btnClose.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClose.Location = New System.Drawing.Point(404, 3)
        Me.btnClose.Name = "btnClose"
        Me.btnClose.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnClose.Selectable = True
        Me.btnClose.Size = New System.Drawing.Size(24, 24)
        Me.btnClose.TabIndex = 38
        Me.btnClose.TabStop = False
        Me.btnClose.tbAdriftIconWhenHover = False
        Me.btnClose.tbAutoSize = True
        Me.btnClose.tbAutoSizeEx = False
        Me.btnClose.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btnClose.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnClose.tbBadgeNumber = 0
        Me.btnClose.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClose.tbEndEllipsis = False
        Me.btnClose.tbIconHoldPlace = True
        Me.btnClose.tbIconImage = Nothing
        Me.btnClose.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClose.tbIconMore = False
        Me.btnClose.tbIconMouseDown = Nothing
        Me.btnClose.tbIconMouseHover = Nothing
        Me.btnClose.tbIconMouseLeave = Nothing
        Me.btnClose.tbIconPlaceText = 2
        Me.btnClose.tbIconReadOnly = Nothing
        Me.btnClose.tbImageMouseDown = Nothing
        Me.btnClose.tbImageMouseHover = Nothing
        Me.btnClose.tbImageMouseLeave = Nothing
        Me.btnClose.tbReadOnly = False
        Me.btnClose.tbReadOnlyText = False
        Me.btnClose.tbShadow = False
        Me.btnClose.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnClose.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnClose.tbShowNew = False
        Me.btnClose.tbShowToolTipOnButton = False
        Me.btnClose.tbSplit = "3,3,3,3"
        Me.btnClose.tbText = ""
        Me.btnClose.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose.tbTextColor = System.Drawing.Color.White
        Me.btnClose.tbTextColorDisable = System.Drawing.Color.White
        Me.btnClose.tbTextColorDown = System.Drawing.Color.White
        Me.btnClose.tbTextColorHover = System.Drawing.Color.White
        Me.btnClose.tbTextMouseDownPlace = 0
        Me.btnClose.tbToolTip = ""
        Me.btnClose.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClose.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClose.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblDiscription
        '
        Me.lblDiscription.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblDiscription.BackColor = System.Drawing.Color.Transparent
        Me.lblDiscription.Location = New System.Drawing.Point(107, 78)
        Me.lblDiscription.Name = "lblDiscription"
        Me.lblDiscription.Size = New System.Drawing.Size(306, 87)
        Me.lblDiscription.TabIndex = 39
        Me.lblDiscription.tbAdriftWhenHover = False
        Me.lblDiscription.tbAutoEllipsis = False
        Me.lblDiscription.tbAutoSize = False
        Me.lblDiscription.tbHideImage = False
        Me.lblDiscription.tbIconImage = Nothing
        Me.lblDiscription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblDiscription.tbIconPlaceText = 5
        Me.lblDiscription.tbShadow = False
        Me.lblDiscription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDiscription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDiscription.tbShowScrolling = False
        Me.lblDiscription.Text = "如果您的设备因为有人恶作剧给你发了一段特殊的" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "阿拉伯字符串，导致微信打开就闪退，那么您可以" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "尝试用此功能进行修复。"
        Me.lblDiscription.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnStart
        '
        Me.btnStart.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnStart.BackColor = System.Drawing.Color.Transparent
        Me.btnStart.BindingForm = Nothing
        Me.btnStart.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStart.Location = New System.Drawing.Point(338, 220)
        Me.btnStart.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnStart.Name = "btnStart"
        Me.btnStart.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnStart.Selectable = True
        Me.btnStart.Size = New System.Drawing.Size(74, 23)
        Me.btnStart.TabIndex = 41
        Me.btnStart.tbAdriftIconWhenHover = False
        Me.btnStart.tbAutoSize = False
        Me.btnStart.tbAutoSizeEx = False
        Me.btnStart.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnStart.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnStart.tbBadgeNumber = 0
        Me.btnStart.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStart.tbEndEllipsis = False
        Me.btnStart.tbIconHoldPlace = True
        Me.btnStart.tbIconImage = Nothing
        Me.btnStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStart.tbIconMore = False
        Me.btnStart.tbIconMouseDown = Nothing
        Me.btnStart.tbIconMouseHover = Nothing
        Me.btnStart.tbIconMouseLeave = Nothing
        Me.btnStart.tbIconPlaceText = 2
        Me.btnStart.tbIconReadOnly = Nothing
        Me.btnStart.tbImageMouseDown = Nothing
        Me.btnStart.tbImageMouseHover = Nothing
        Me.btnStart.tbImageMouseLeave = Nothing
        Me.btnStart.tbReadOnly = False
        Me.btnStart.tbReadOnlyText = False
        Me.btnStart.tbShadow = False
        Me.btnStart.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStart.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStart.tbShowNew = False
        Me.btnStart.tbShowToolTipOnButton = False
        Me.btnStart.tbSplit = "13,11,13,11"
        Me.btnStart.tbText = "修复"
        Me.btnStart.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStart.tbTextColor = System.Drawing.Color.White
        Me.btnStart.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStart.tbTextColorDown = System.Drawing.Color.White
        Me.btnStart.tbTextColorHover = System.Drawing.Color.White
        Me.btnStart.tbTextMouseDownPlace = 0
        Me.btnStart.tbToolTip = ""
        Me.btnStart.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStart.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStart.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'pbImage
        '
        Me.pbImage.BackColor = System.Drawing.Color.Transparent
        Me.pbImage.Image = Global.iTong.My.Resources.Resources.more_weixinrepair
        Me.pbImage.Location = New System.Drawing.Point(24, 88)
        Me.pbImage.Name = "pbImage"
        Me.pbImage.Size = New System.Drawing.Size(58, 58)
        Me.pbImage.TabIndex = 43
        Me.pbImage.TabStop = False
        '
        'lblTitle
        '
        Me.lblTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblTitle.AutoEllipsis = True
        Me.lblTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblTitle.Location = New System.Drawing.Point(107, 49)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(305, 25)
        Me.lblTitle.TabIndex = 44
        Me.lblTitle.tbAdriftWhenHover = False
        Me.lblTitle.tbAutoEllipsis = True
        Me.lblTitle.tbAutoSize = False
        Me.lblTitle.tbHideImage = False
        Me.lblTitle.tbIconImage = Nothing
        Me.lblTitle.tbIconImageAlign = System.Drawing.ContentAlignment.TopLeft
        Me.lblTitle.tbIconPlaceText = 5
        Me.lblTitle.tbShadow = False
        Me.lblTitle.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTitle.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTitle.tbShowScrolling = False
        Me.lblTitle.Text = "微信闪退修复"
        '
        'lblMessage
        '
        Me.lblMessage.BackColor = System.Drawing.Color.Transparent
        Me.lblMessage.Location = New System.Drawing.Point(107, 170)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(306, 30)
        Me.lblMessage.TabIndex = 45
        Me.lblMessage.Text = "Description"
        '
        'bgwLoadDB
        '
        Me.bgwLoadDB.WorkerSupportsCancellation = True
        '
        'frmWeixinRepair
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(430, 260)
        Me.Controls.Add(Me.lblMessage)
        Me.Controls.Add(Me.lblTitle)
        Me.Controls.Add(Me.pbImage)
        Me.Controls.Add(Me.btnStart)
        Me.Controls.Add(Me.lblDiscription)
        Me.Controls.Add(Me.btnClose)
        Me.MinimumSize = New System.Drawing.Size(10, 85)
        Me.Name = "frmWeixinRepair"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,31,5,54"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.Text = "微信闪退修复"
        Me.Controls.SetChildIndex(Me.btnClose, 0)
        Me.Controls.SetChildIndex(Me.lblDiscription, 0)
        Me.Controls.SetChildIndex(Me.btnStart, 0)
        Me.Controls.SetChildIndex(Me.pbImage, 0)
        Me.Controls.SetChildIndex(Me.lblTitle, 0)
        Me.Controls.SetChildIndex(Me.lblMessage, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        CType(Me.pbImage, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btnClose As iTong.Components.tbButton
    Friend WithEvents lblDiscription As iTong.Components.tbLabel
    Friend WithEvents btnStart As iTong.Components.tbButton
    Friend WithEvents pbImage As System.Windows.Forms.PictureBox
    Friend WithEvents lblTitle As iTong.Components.tbLabel
    Friend WithEvents lblMessage As System.Windows.Forms.Label
    Friend WithEvents bgwLoadDB As System.ComponentModel.BackgroundWorker
End Class
