﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeixinEmoticonApp
    Inherits frmDeviceBase


    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeixinEmoticonApp))
        Me.btnOK = New iTong.Components.tbButton()
        Me.picError = New System.Windows.Forms.PictureBox()
        Me.lblWeixinDescription = New System.Windows.Forms.Label()
        Me.pnlBottom = New iTong.Components.tbPanel()
        Me.lblWeixinupdate = New System.Windows.Forms.Label()
        Me.lblWeixinTitle = New System.Windows.Forms.Label()
        Me.picEmoticon = New System.Windows.Forms.PictureBox()
        Me.pnlIOSBackground = New iTong.Components.tbPanel()
        Me.pnlAndroidBackground = New iTong.Components.tbPanel()
        Me.btnOKAndroid = New iTong.Components.tbButton()
        Me.PictureBoxAndroid = New System.Windows.Forms.PictureBox()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlBottom.SuspendLayout()
        CType(Me.picEmoticon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlIOSBackground.SuspendLayout()
        Me.pnlAndroidBackground.SuspendLayout()
        CType(Me.PictureBoxAndroid, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(814, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(790, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(766, 0)
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(697, 32)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(102, 36)
        Me.btnOK.TabIndex = 41
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_welcome_connect
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "立即下载"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'picError
        '
        Me.picError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picError.Image = Global.iTong.My.Resources.Resources.weixin_error
        Me.picError.Location = New System.Drawing.Point(234, 195)
        Me.picError.Name = "picError"
        Me.picError.Size = New System.Drawing.Size(70, 69)
        Me.picError.TabIndex = 42
        Me.picError.TabStop = False
        '
        'lblWeixinDescription
        '
        Me.lblWeixinDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinDescription.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWeixinDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblWeixinDescription.Location = New System.Drawing.Point(324, 197)
        Me.lblWeixinDescription.Name = "lblWeixinDescription"
        Me.lblWeixinDescription.Size = New System.Drawing.Size(373, 119)
        Me.lblWeixinDescription.TabIndex = 43
        Me.lblWeixinDescription.Text = "由于苹果8.3及以上固件改变了文件目录结构，" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "无法读取数据。推荐下载微表情大全，" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "直接在手机上发送表情。"
        '
        'pnlBottom
        '
        Me.pnlBottom.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(250, Byte), Integer), CType(CType(250, Byte), Integer))
        Me.pnlBottom.Controls.Add(Me.btnOK)
        Me.pnlBottom.Controls.Add(Me.lblWeixinupdate)
        Me.pnlBottom.Controls.Add(Me.lblWeixinTitle)
        Me.pnlBottom.Controls.Add(Me.picEmoticon)
        Me.pnlBottom.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlBottom.Location = New System.Drawing.Point(0, 474)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(836, 101)
        Me.pnlBottom.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlBottom.TabIndex = 44
        Me.pnlBottom.tbBackgroundImage = Global.iTong.My.Resources.Resources.weixin_bg_bottom
        Me.pnlBottom.tbShowWatermark = False
        Me.pnlBottom.tbSplit = "2,2,2,2"
        Me.pnlBottom.tbWatermark = Nothing
        Me.pnlBottom.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBottom.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblWeixinupdate
        '
        Me.lblWeixinupdate.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinupdate.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWeixinupdate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblWeixinupdate.Location = New System.Drawing.Point(322, 54)
        Me.lblWeixinupdate.Name = "lblWeixinupdate"
        Me.lblWeixinupdate.Size = New System.Drawing.Size(235, 23)
        Me.lblWeixinupdate.TabIndex = 45
        Me.lblWeixinupdate.Text = "大量萌贱表情，每周更新！"
        Me.lblWeixinupdate.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblWeixinTitle
        '
        Me.lblWeixinTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinTitle.AutoSize = True
        Me.lblWeixinTitle.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblWeixinTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblWeixinTitle.Location = New System.Drawing.Point(322, 27)
        Me.lblWeixinTitle.Name = "lblWeixinTitle"
        Me.lblWeixinTitle.Size = New System.Drawing.Size(76, 16)
        Me.lblWeixinTitle.TabIndex = 44
        Me.lblWeixinTitle.Text = "微信表情"
        Me.lblWeixinTitle.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picEmoticon
        '
        Me.picEmoticon.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picEmoticon.Image = Global.iTong.My.Resources.Resources.weixin_emoticon
        Me.picEmoticon.Location = New System.Drawing.Point(236, 23)
        Me.picEmoticon.Name = "picEmoticon"
        Me.picEmoticon.Size = New System.Drawing.Size(55, 55)
        Me.picEmoticon.TabIndex = 43
        Me.picEmoticon.TabStop = False
        '
        'pnlIOSBackground
        '
        Me.pnlIOSBackground.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlIOSBackground.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlIOSBackground.Controls.Add(Me.pnlBottom)
        Me.pnlIOSBackground.Controls.Add(Me.lblWeixinDescription)
        Me.pnlIOSBackground.Controls.Add(Me.picError)
        Me.pnlIOSBackground.Location = New System.Drawing.Point(1, 26)
        Me.pnlIOSBackground.Margin = New System.Windows.Forms.Padding(1, 0, 1, 1)
        Me.pnlIOSBackground.Name = "pnlIOSBackground"
        Me.pnlIOSBackground.Size = New System.Drawing.Size(836, 575)
        Me.pnlIOSBackground.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlIOSBackground.TabIndex = 45
        Me.pnlIOSBackground.tbBackgroundImage = Nothing
        Me.pnlIOSBackground.tbShowWatermark = False
        Me.pnlIOSBackground.tbSplit = "0,0,0,0"
        Me.pnlIOSBackground.tbWatermark = Nothing
        Me.pnlIOSBackground.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlIOSBackground.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlAndroidBackground
        '
        Me.pnlAndroidBackground.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlAndroidBackground.Controls.Add(Me.btnOKAndroid)
        Me.pnlAndroidBackground.Controls.Add(Me.PictureBoxAndroid)
        Me.pnlAndroidBackground.Location = New System.Drawing.Point(1, 26)
        Me.pnlAndroidBackground.Margin = New System.Windows.Forms.Padding(1, 0, 1, 1)
        Me.pnlAndroidBackground.Name = "pnlAndroidBackground"
        Me.pnlAndroidBackground.Size = New System.Drawing.Size(836, 575)
        Me.pnlAndroidBackground.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlAndroidBackground.TabIndex = 46
        Me.pnlAndroidBackground.tbBackgroundImage = Nothing
        Me.pnlAndroidBackground.tbShowWatermark = False
        Me.pnlAndroidBackground.tbSplit = "0,0,0,0"
        Me.pnlAndroidBackground.tbWatermark = Nothing
        Me.pnlAndroidBackground.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlAndroidBackground.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnOKAndroid
        '
        Me.btnOKAndroid.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOKAndroid.BackColor = System.Drawing.Color.Transparent
        Me.btnOKAndroid.BindingForm = Nothing
        Me.btnOKAndroid.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnOKAndroid.Location = New System.Drawing.Point(352, 451)
        Me.btnOKAndroid.Name = "btnOKAndroid"
        Me.btnOKAndroid.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOKAndroid.Selectable = True
        Me.btnOKAndroid.Size = New System.Drawing.Size(135, 47)
        Me.btnOKAndroid.TabIndex = 48
        Me.btnOKAndroid.tbAdriftIconWhenHover = False
        Me.btnOKAndroid.tbAutoSize = False
        Me.btnOKAndroid.tbAutoSizeEx = False
        Me.btnOKAndroid.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_Emoticon_Download
        Me.btnOKAndroid.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOKAndroid.tbBadgeNumber = 0
        Me.btnOKAndroid.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOKAndroid.tbEndEllipsis = False
        Me.btnOKAndroid.tbIconHoldPlace = True
        Me.btnOKAndroid.tbIconImage = Nothing
        Me.btnOKAndroid.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOKAndroid.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOKAndroid.tbIconMore = False
        Me.btnOKAndroid.tbIconMouseDown = Nothing
        Me.btnOKAndroid.tbIconMouseHover = Nothing
        Me.btnOKAndroid.tbIconMouseLeave = Nothing
        Me.btnOKAndroid.tbIconPlaceText = 2
        Me.btnOKAndroid.tbIconReadOnly = Nothing
        Me.btnOKAndroid.tbImageMouseDown = Nothing
        Me.btnOKAndroid.tbImageMouseHover = Nothing
        Me.btnOKAndroid.tbImageMouseLeave = Nothing
        Me.btnOKAndroid.tbProgressValue = 50
        Me.btnOKAndroid.tbReadOnly = False
        Me.btnOKAndroid.tbReadOnlyText = False
        Me.btnOKAndroid.tbShadow = False
        Me.btnOKAndroid.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnOKAndroid.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOKAndroid.tbShowDot = False
        Me.btnOKAndroid.tbShowMoreIconImg = CType(resources.GetObject("btnOKAndroid.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOKAndroid.tbShowNew = False
        Me.btnOKAndroid.tbShowProgress = False
        Me.btnOKAndroid.tbShowTip = True
        Me.btnOKAndroid.tbShowToolTipOnButton = False
        Me.btnOKAndroid.tbSplit = "13,11,13,11"
        Me.btnOKAndroid.tbText = "立即下载"
        Me.btnOKAndroid.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOKAndroid.tbTextColor = System.Drawing.Color.White
        Me.btnOKAndroid.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOKAndroid.tbTextColorDown = System.Drawing.Color.White
        Me.btnOKAndroid.tbTextColorHover = System.Drawing.Color.White
        Me.btnOKAndroid.tbTextMouseDownPlace = 0
        Me.btnOKAndroid.tbToolTip = ""
        Me.btnOKAndroid.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOKAndroid.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOKAndroid.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOKAndroid.VisibleEx = True
        '
        'PictureBoxAndroid
        '
        Me.PictureBoxAndroid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PictureBoxAndroid.Image = Global.iTong.My.Resources.Resources.bg_Emoticon_Android
        Me.PictureBoxAndroid.Location = New System.Drawing.Point(0, 0)
        Me.PictureBoxAndroid.Margin = New System.Windows.Forms.Padding(0)
        Me.PictureBoxAndroid.Name = "PictureBoxAndroid"
        Me.PictureBoxAndroid.Size = New System.Drawing.Size(836, 575)
        Me.PictureBoxAndroid.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBoxAndroid.TabIndex = 47
        Me.PictureBoxAndroid.TabStop = False
        '
        'frmWeixinEmoticonApp
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(838, 602)
        Me.Controls.Add(Me.pnlAndroidBackground)
        Me.Controls.Add(Me.pnlIOSBackground)
        Me.MaximizeBox = False
        Me.MaximumSize = New System.Drawing.Size(838, 602)
        Me.Name = "frmWeixinEmoticonApp"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "微信表情"
        Me.Controls.SetChildIndex(Me.pnlIOSBackground, 0)
        Me.Controls.SetChildIndex(Me.pnlAndroidBackground, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        CType(Me.picError, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlBottom.ResumeLayout(False)
        Me.pnlBottom.PerformLayout()
        CType(Me.picEmoticon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlIOSBackground.ResumeLayout(False)
        Me.pnlAndroidBackground.ResumeLayout(False)
        Me.pnlAndroidBackground.PerformLayout()
        CType(Me.PictureBoxAndroid, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btnOK As tbButton
    Friend WithEvents picError As System.Windows.Forms.PictureBox
    Friend WithEvents lblWeixinDescription As System.Windows.Forms.Label
    Friend WithEvents pnlBottom As tbPanel
    Friend WithEvents picEmoticon As System.Windows.Forms.PictureBox
    Friend WithEvents lblWeixinupdate As System.Windows.Forms.Label
    Friend WithEvents lblWeixinTitle As System.Windows.Forms.Label
    Friend WithEvents pnlIOSBackground As tbPanel
    Friend WithEvents pnlAndroidBackground As iTong.Components.tbPanel
    Friend WithEvents PictureBoxAndroid As System.Windows.Forms.PictureBox
    Friend WithEvents btnOKAndroid As iTong.Components.tbButton
End Class
