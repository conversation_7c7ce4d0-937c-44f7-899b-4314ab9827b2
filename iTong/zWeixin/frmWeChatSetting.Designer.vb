﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeChatSetting
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeChatSetting))
        Me.btn_close = New iTong.Components.tbButton()
        Me.btnCancel = New iTong.Components.tbButton()
        Me.btnOK = New iTong.Components.tbButton()
        Me.chkLinkIsExcel = New iTong.Components.tbCheckBox()
        Me.lblExportSetting = New System.Windows.Forms.Label()
        Me.cbxExportAudioConvert = New iTong.Components.tbCheckBox()
        Me.cbxExportVoice = New iTong.Components.tbCheckBox()
        Me.cbxExportPicture = New iTong.Components.tbCheckBox()
        Me.cbxExportVideo = New iTong.Components.tbCheckBox()
        Me.lblOther = New System.Windows.Forms.Label()
        Me.lblExportExcel = New System.Windows.Forms.Label()
        Me.cbxExcelCoding = New System.Windows.Forms.ComboBox()
        Me.cbxWeChatDataMigration = New iTong.Components.tbCheckBox()
        Me.cbxImportAloneBackup = New iTong.Components.tbCheckBox()
        Me.cbxWeChatIsDoFindDate = New iTong.Components.tbCheckBox()
        Me.cbxWCExportFormatTxt = New iTong.Components.tbCheckBox()
        Me.lblExport = New System.Windows.Forms.Label()
        Me.cbxWCExportFormatExcel = New iTong.Components.tbCheckBox()
        Me.cbxWCExportFormatHtml = New iTong.Components.tbCheckBox()
        Me.cbxWeChatExportSuccessOpenFolder = New iTong.Components.tbCheckBox()
        Me.lblFirstExport = New System.Windows.Forms.Label()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(654, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 9
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.Location = New System.Drawing.Point(604, 451)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(65, 23)
        Me.btnCancel.TabIndex = 42
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbProgressValue = 50
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowDot = False
        Me.btnCancel.tbShowMoreIconImg = CType(resources.GetObject("btnCancel.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowProgress = False
        Me.btnCancel.tbShowTip = True
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(530, 451)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(65, 23)
        Me.btnOK.TabIndex = 41
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "保存"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'chkLinkIsExcel
        '
        Me.chkLinkIsExcel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.chkLinkIsExcel.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkLinkIsExcel.BackColor = System.Drawing.Color.Transparent
        Me.chkLinkIsExcel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkLinkIsExcel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkLinkIsExcel.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkLinkIsExcel.Location = New System.Drawing.Point(33, 451)
        Me.chkLinkIsExcel.Name = "chkLinkIsExcel"
        Me.chkLinkIsExcel.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkLinkIsExcel.Size = New System.Drawing.Size(266, 18)
        Me.chkLinkIsExcel.TabIndex = 50
        Me.chkLinkIsExcel.tbAdriftIconWhenHover = False
        Me.chkLinkIsExcel.tbAutoSize = False
        Me.chkLinkIsExcel.tbAutoSizeEx = True
        Me.chkLinkIsExcel.tbIconChecked = CType(resources.GetObject("chkLinkIsExcel.tbIconChecked"), System.Drawing.Image)
        Me.chkLinkIsExcel.tbIconCheckedMouseDown = Nothing
        Me.chkLinkIsExcel.tbIconCheckedMouseHover = Nothing
        Me.chkLinkIsExcel.tbIconCheckedMouseLeave = Nothing
        Me.chkLinkIsExcel.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkLinkIsExcel.tbIconHoldPlace = True
        Me.chkLinkIsExcel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkLinkIsExcel.tbIconIndeterminate = CType(resources.GetObject("chkLinkIsExcel.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkLinkIsExcel.tbIconIndeterminateMouseDown = Nothing
        Me.chkLinkIsExcel.tbIconIndeterminateMouseHover = Nothing
        Me.chkLinkIsExcel.tbIconIndeterminateMouseLeave = Nothing
        Me.chkLinkIsExcel.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkLinkIsExcel.tbIconPlaceText = 1
        Me.chkLinkIsExcel.tbIconUnChecked = CType(resources.GetObject("chkLinkIsExcel.tbIconUnChecked"), System.Drawing.Image)
        Me.chkLinkIsExcel.tbIconUnCheckedMouseDown = Nothing
        Me.chkLinkIsExcel.tbIconUnCheckedMouseHover = Nothing
        Me.chkLinkIsExcel.tbIconUnCheckedMouseLeave = Nothing
        Me.chkLinkIsExcel.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkLinkIsExcel.tbImageBackground = Nothing
        Me.chkLinkIsExcel.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkLinkIsExcel.tbImageCheckedMouseDown = Nothing
        Me.chkLinkIsExcel.tbImageCheckedMouseHover = Nothing
        Me.chkLinkIsExcel.tbImageCheckedMouseLeave = Nothing
        Me.chkLinkIsExcel.tbImageUnCheckedMouseDown = Nothing
        Me.chkLinkIsExcel.tbImageUnCheckedMouseHover = Nothing
        Me.chkLinkIsExcel.tbImageUnCheckedMouseLeave = Nothing
        Me.chkLinkIsExcel.tbReadOnly = False
        Me.chkLinkIsExcel.tbShadow = False
        Me.chkLinkIsExcel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkLinkIsExcel.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkLinkIsExcel.tbSplit = "3,3,3,3"
        Me.chkLinkIsExcel.tbToolTip = ""
        Me.chkLinkIsExcel.Text = """导出到电脑""同时导出表格式数据（Excel）"
        Me.chkLinkIsExcel.UseVisualStyleBackColor = False
        Me.chkLinkIsExcel.Visible = False
        '
        'lblExportSetting
        '
        Me.lblExportSetting.BackColor = System.Drawing.Color.Transparent
        Me.lblExportSetting.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblExportSetting.Location = New System.Drawing.Point(50, 58)
        Me.lblExportSetting.Name = "lblExportSetting"
        Me.lblExportSetting.Size = New System.Drawing.Size(239, 17)
        Me.lblExportSetting.TabIndex = 68
        Me.lblExportSetting.Text = "微信导出Html设置"
        Me.lblExportSetting.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cbxExportAudioConvert
        '
        Me.cbxExportAudioConvert.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxExportAudioConvert.BackColor = System.Drawing.Color.Transparent
        Me.cbxExportAudioConvert.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxExportAudioConvert.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxExportAudioConvert.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxExportAudioConvert.Location = New System.Drawing.Point(70, 84)
        Me.cbxExportAudioConvert.Name = "cbxExportAudioConvert"
        Me.cbxExportAudioConvert.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxExportAudioConvert.Size = New System.Drawing.Size(260, 18)
        Me.cbxExportAudioConvert.TabIndex = 69
        Me.cbxExportAudioConvert.tbAdriftIconWhenHover = False
        Me.cbxExportAudioConvert.tbAutoSize = False
        Me.cbxExportAudioConvert.tbAutoSizeEx = True
        Me.cbxExportAudioConvert.tbIconChecked = CType(resources.GetObject("cbxExportAudioConvert.tbIconChecked"), System.Drawing.Image)
        Me.cbxExportAudioConvert.tbIconCheckedMouseDown = Nothing
        Me.cbxExportAudioConvert.tbIconCheckedMouseHover = Nothing
        Me.cbxExportAudioConvert.tbIconCheckedMouseLeave = Nothing
        Me.cbxExportAudioConvert.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxExportAudioConvert.tbIconHoldPlace = True
        Me.cbxExportAudioConvert.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxExportAudioConvert.tbIconIndeterminate = CType(resources.GetObject("cbxExportAudioConvert.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxExportAudioConvert.tbIconIndeterminateMouseDown = Nothing
        Me.cbxExportAudioConvert.tbIconIndeterminateMouseHover = Nothing
        Me.cbxExportAudioConvert.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxExportAudioConvert.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxExportAudioConvert.tbIconPlaceText = 1
        Me.cbxExportAudioConvert.tbIconUnChecked = CType(resources.GetObject("cbxExportAudioConvert.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxExportAudioConvert.tbIconUnCheckedMouseDown = Nothing
        Me.cbxExportAudioConvert.tbIconUnCheckedMouseHover = Nothing
        Me.cbxExportAudioConvert.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxExportAudioConvert.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxExportAudioConvert.tbImageBackground = Nothing
        Me.cbxExportAudioConvert.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxExportAudioConvert.tbImageCheckedMouseDown = Nothing
        Me.cbxExportAudioConvert.tbImageCheckedMouseHover = Nothing
        Me.cbxExportAudioConvert.tbImageCheckedMouseLeave = Nothing
        Me.cbxExportAudioConvert.tbImageUnCheckedMouseDown = Nothing
        Me.cbxExportAudioConvert.tbImageUnCheckedMouseHover = Nothing
        Me.cbxExportAudioConvert.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxExportAudioConvert.tbReadOnly = False
        Me.cbxExportAudioConvert.tbShadow = False
        Me.cbxExportAudioConvert.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxExportAudioConvert.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxExportAudioConvert.tbSplit = "3,3,3,3"
        Me.cbxExportAudioConvert.tbToolTip = ""
        Me.cbxExportAudioConvert.Text = "将导出的语音文件.aud格式转换成.wav格式"
        Me.cbxExportAudioConvert.UseVisualStyleBackColor = False
        '
        'cbxExportVoice
        '
        Me.cbxExportVoice.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxExportVoice.BackColor = System.Drawing.Color.Transparent
        Me.cbxExportVoice.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxExportVoice.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxExportVoice.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxExportVoice.Location = New System.Drawing.Point(70, 114)
        Me.cbxExportVoice.Name = "cbxExportVoice"
        Me.cbxExportVoice.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxExportVoice.Size = New System.Drawing.Size(104, 18)
        Me.cbxExportVoice.TabIndex = 70
        Me.cbxExportVoice.tbAdriftIconWhenHover = False
        Me.cbxExportVoice.tbAutoSize = False
        Me.cbxExportVoice.tbAutoSizeEx = True
        Me.cbxExportVoice.tbIconChecked = CType(resources.GetObject("cbxExportVoice.tbIconChecked"), System.Drawing.Image)
        Me.cbxExportVoice.tbIconCheckedMouseDown = Nothing
        Me.cbxExportVoice.tbIconCheckedMouseHover = Nothing
        Me.cbxExportVoice.tbIconCheckedMouseLeave = Nothing
        Me.cbxExportVoice.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxExportVoice.tbIconHoldPlace = True
        Me.cbxExportVoice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxExportVoice.tbIconIndeterminate = CType(resources.GetObject("cbxExportVoice.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxExportVoice.tbIconIndeterminateMouseDown = Nothing
        Me.cbxExportVoice.tbIconIndeterminateMouseHover = Nothing
        Me.cbxExportVoice.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxExportVoice.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxExportVoice.tbIconPlaceText = 1
        Me.cbxExportVoice.tbIconUnChecked = CType(resources.GetObject("cbxExportVoice.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxExportVoice.tbIconUnCheckedMouseDown = Nothing
        Me.cbxExportVoice.tbIconUnCheckedMouseHover = Nothing
        Me.cbxExportVoice.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxExportVoice.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxExportVoice.tbImageBackground = Nothing
        Me.cbxExportVoice.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxExportVoice.tbImageCheckedMouseDown = Nothing
        Me.cbxExportVoice.tbImageCheckedMouseHover = Nothing
        Me.cbxExportVoice.tbImageCheckedMouseLeave = Nothing
        Me.cbxExportVoice.tbImageUnCheckedMouseDown = Nothing
        Me.cbxExportVoice.tbImageUnCheckedMouseHover = Nothing
        Me.cbxExportVoice.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxExportVoice.tbReadOnly = False
        Me.cbxExportVoice.tbShadow = False
        Me.cbxExportVoice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxExportVoice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxExportVoice.tbSplit = "3,3,3,3"
        Me.cbxExportVoice.tbToolTip = ""
        Me.cbxExportVoice.Text = "同时导出语音"
        Me.cbxExportVoice.UseVisualStyleBackColor = False
        '
        'cbxExportPicture
        '
        Me.cbxExportPicture.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxExportPicture.BackColor = System.Drawing.Color.Transparent
        Me.cbxExportPicture.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxExportPicture.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxExportPicture.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxExportPicture.Location = New System.Drawing.Point(70, 144)
        Me.cbxExportPicture.Name = "cbxExportPicture"
        Me.cbxExportPicture.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxExportPicture.Size = New System.Drawing.Size(104, 18)
        Me.cbxExportPicture.TabIndex = 71
        Me.cbxExportPicture.tbAdriftIconWhenHover = False
        Me.cbxExportPicture.tbAutoSize = False
        Me.cbxExportPicture.tbAutoSizeEx = True
        Me.cbxExportPicture.tbIconChecked = CType(resources.GetObject("cbxExportPicture.tbIconChecked"), System.Drawing.Image)
        Me.cbxExportPicture.tbIconCheckedMouseDown = Nothing
        Me.cbxExportPicture.tbIconCheckedMouseHover = Nothing
        Me.cbxExportPicture.tbIconCheckedMouseLeave = Nothing
        Me.cbxExportPicture.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxExportPicture.tbIconHoldPlace = True
        Me.cbxExportPicture.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxExportPicture.tbIconIndeterminate = CType(resources.GetObject("cbxExportPicture.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxExportPicture.tbIconIndeterminateMouseDown = Nothing
        Me.cbxExportPicture.tbIconIndeterminateMouseHover = Nothing
        Me.cbxExportPicture.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxExportPicture.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxExportPicture.tbIconPlaceText = 1
        Me.cbxExportPicture.tbIconUnChecked = CType(resources.GetObject("cbxExportPicture.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxExportPicture.tbIconUnCheckedMouseDown = Nothing
        Me.cbxExportPicture.tbIconUnCheckedMouseHover = Nothing
        Me.cbxExportPicture.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxExportPicture.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxExportPicture.tbImageBackground = Nothing
        Me.cbxExportPicture.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxExportPicture.tbImageCheckedMouseDown = Nothing
        Me.cbxExportPicture.tbImageCheckedMouseHover = Nothing
        Me.cbxExportPicture.tbImageCheckedMouseLeave = Nothing
        Me.cbxExportPicture.tbImageUnCheckedMouseDown = Nothing
        Me.cbxExportPicture.tbImageUnCheckedMouseHover = Nothing
        Me.cbxExportPicture.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxExportPicture.tbReadOnly = False
        Me.cbxExportPicture.tbShadow = False
        Me.cbxExportPicture.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxExportPicture.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxExportPicture.tbSplit = "3,3,3,3"
        Me.cbxExportPicture.tbToolTip = ""
        Me.cbxExportPicture.Text = "同时导出图片"
        Me.cbxExportPicture.UseVisualStyleBackColor = False
        '
        'cbxExportVideo
        '
        Me.cbxExportVideo.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxExportVideo.BackColor = System.Drawing.Color.Transparent
        Me.cbxExportVideo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxExportVideo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxExportVideo.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxExportVideo.Location = New System.Drawing.Point(70, 174)
        Me.cbxExportVideo.Name = "cbxExportVideo"
        Me.cbxExportVideo.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxExportVideo.Size = New System.Drawing.Size(116, 18)
        Me.cbxExportVideo.TabIndex = 72
        Me.cbxExportVideo.tbAdriftIconWhenHover = False
        Me.cbxExportVideo.tbAutoSize = False
        Me.cbxExportVideo.tbAutoSizeEx = True
        Me.cbxExportVideo.tbIconChecked = CType(resources.GetObject("cbxExportVideo.tbIconChecked"), System.Drawing.Image)
        Me.cbxExportVideo.tbIconCheckedMouseDown = Nothing
        Me.cbxExportVideo.tbIconCheckedMouseHover = Nothing
        Me.cbxExportVideo.tbIconCheckedMouseLeave = Nothing
        Me.cbxExportVideo.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxExportVideo.tbIconHoldPlace = True
        Me.cbxExportVideo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxExportVideo.tbIconIndeterminate = CType(resources.GetObject("cbxExportVideo.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxExportVideo.tbIconIndeterminateMouseDown = Nothing
        Me.cbxExportVideo.tbIconIndeterminateMouseHover = Nothing
        Me.cbxExportVideo.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxExportVideo.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxExportVideo.tbIconPlaceText = 1
        Me.cbxExportVideo.tbIconUnChecked = CType(resources.GetObject("cbxExportVideo.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxExportVideo.tbIconUnCheckedMouseDown = Nothing
        Me.cbxExportVideo.tbIconUnCheckedMouseHover = Nothing
        Me.cbxExportVideo.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxExportVideo.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxExportVideo.tbImageBackground = Nothing
        Me.cbxExportVideo.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxExportVideo.tbImageCheckedMouseDown = Nothing
        Me.cbxExportVideo.tbImageCheckedMouseHover = Nothing
        Me.cbxExportVideo.tbImageCheckedMouseLeave = Nothing
        Me.cbxExportVideo.tbImageUnCheckedMouseDown = Nothing
        Me.cbxExportVideo.tbImageUnCheckedMouseHover = Nothing
        Me.cbxExportVideo.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxExportVideo.tbReadOnly = False
        Me.cbxExportVideo.tbShadow = False
        Me.cbxExportVideo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxExportVideo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxExportVideo.tbSplit = "3,3,3,3"
        Me.cbxExportVideo.tbToolTip = ""
        Me.cbxExportVideo.Text = "同时导出小视频"
        Me.cbxExportVideo.UseVisualStyleBackColor = False
        '
        'lblOther
        '
        Me.lblOther.BackColor = System.Drawing.Color.Transparent
        Me.lblOther.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblOther.Location = New System.Drawing.Point(50, 282)
        Me.lblOther.Name = "lblOther"
        Me.lblOther.Size = New System.Drawing.Size(239, 17)
        Me.lblOther.TabIndex = 74
        Me.lblOther.Text = "其他设置"
        Me.lblOther.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblExportExcel
        '
        Me.lblExportExcel.BackColor = System.Drawing.Color.Transparent
        Me.lblExportExcel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblExportExcel.Location = New System.Drawing.Point(50, 216)
        Me.lblExportExcel.Name = "lblExportExcel"
        Me.lblExportExcel.Size = New System.Drawing.Size(239, 17)
        Me.lblExportExcel.TabIndex = 75
        Me.lblExportExcel.Text = "微信导出Excel设置"
        Me.lblExportExcel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cbxExcelCoding
        '
        Me.cbxExcelCoding.FormattingEnabled = True
        Me.cbxExcelCoding.Items.AddRange(New Object() {"utf8", "gb2312", "gbk", "unicode"})
        Me.cbxExcelCoding.Location = New System.Drawing.Point(70, 240)
        Me.cbxExcelCoding.Name = "cbxExcelCoding"
        Me.cbxExcelCoding.Size = New System.Drawing.Size(260, 20)
        Me.cbxExcelCoding.TabIndex = 76
        '
        'cbxWeChatDataMigration
        '
        Me.cbxWeChatDataMigration.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxWeChatDataMigration.BackColor = System.Drawing.Color.Transparent
        Me.cbxWeChatDataMigration.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxWeChatDataMigration.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxWeChatDataMigration.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxWeChatDataMigration.Location = New System.Drawing.Point(70, 306)
        Me.cbxWeChatDataMigration.Name = "cbxWeChatDataMigration"
        Me.cbxWeChatDataMigration.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxWeChatDataMigration.Size = New System.Drawing.Size(152, 18)
        Me.cbxWeChatDataMigration.TabIndex = 73
        Me.cbxWeChatDataMigration.tbAdriftIconWhenHover = False
        Me.cbxWeChatDataMigration.tbAutoSize = False
        Me.cbxWeChatDataMigration.tbAutoSizeEx = True
        Me.cbxWeChatDataMigration.tbIconChecked = CType(resources.GetObject("cbxWeChatDataMigration.tbIconChecked"), System.Drawing.Image)
        Me.cbxWeChatDataMigration.tbIconCheckedMouseDown = Nothing
        Me.cbxWeChatDataMigration.tbIconCheckedMouseHover = Nothing
        Me.cbxWeChatDataMigration.tbIconCheckedMouseLeave = Nothing
        Me.cbxWeChatDataMigration.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWeChatDataMigration.tbIconHoldPlace = True
        Me.cbxWeChatDataMigration.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxWeChatDataMigration.tbIconIndeterminate = CType(resources.GetObject("cbxWeChatDataMigration.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxWeChatDataMigration.tbIconIndeterminateMouseDown = Nothing
        Me.cbxWeChatDataMigration.tbIconIndeterminateMouseHover = Nothing
        Me.cbxWeChatDataMigration.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxWeChatDataMigration.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxWeChatDataMigration.tbIconPlaceText = 1
        Me.cbxWeChatDataMigration.tbIconUnChecked = CType(resources.GetObject("cbxWeChatDataMigration.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxWeChatDataMigration.tbIconUnCheckedMouseDown = Nothing
        Me.cbxWeChatDataMigration.tbIconUnCheckedMouseHover = Nothing
        Me.cbxWeChatDataMigration.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxWeChatDataMigration.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWeChatDataMigration.tbImageBackground = Nothing
        Me.cbxWeChatDataMigration.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxWeChatDataMigration.tbImageCheckedMouseDown = Nothing
        Me.cbxWeChatDataMigration.tbImageCheckedMouseHover = Nothing
        Me.cbxWeChatDataMigration.tbImageCheckedMouseLeave = Nothing
        Me.cbxWeChatDataMigration.tbImageUnCheckedMouseDown = Nothing
        Me.cbxWeChatDataMigration.tbImageUnCheckedMouseHover = Nothing
        Me.cbxWeChatDataMigration.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxWeChatDataMigration.tbReadOnly = False
        Me.cbxWeChatDataMigration.tbShadow = False
        Me.cbxWeChatDataMigration.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxWeChatDataMigration.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxWeChatDataMigration.tbSplit = "3,3,3,3"
        Me.cbxWeChatDataMigration.tbToolTip = ""
        Me.cbxWeChatDataMigration.Text = "启用微信分身数据迁移"
        Me.cbxWeChatDataMigration.UseVisualStyleBackColor = False
        '
        'cbxImportAloneBackup
        '
        Me.cbxImportAloneBackup.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxImportAloneBackup.BackColor = System.Drawing.Color.Transparent
        Me.cbxImportAloneBackup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxImportAloneBackup.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxImportAloneBackup.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxImportAloneBackup.Location = New System.Drawing.Point(70, 306)
        Me.cbxImportAloneBackup.Name = "cbxImportAloneBackup"
        Me.cbxImportAloneBackup.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxImportAloneBackup.Size = New System.Drawing.Size(104, 18)
        Me.cbxImportAloneBackup.TabIndex = 77
        Me.cbxImportAloneBackup.tbAdriftIconWhenHover = False
        Me.cbxImportAloneBackup.tbAutoSize = False
        Me.cbxImportAloneBackup.tbAutoSizeEx = True
        Me.cbxImportAloneBackup.tbIconChecked = CType(resources.GetObject("cbxImportAloneBackup.tbIconChecked"), System.Drawing.Image)
        Me.cbxImportAloneBackup.tbIconCheckedMouseDown = Nothing
        Me.cbxImportAloneBackup.tbIconCheckedMouseHover = Nothing
        Me.cbxImportAloneBackup.tbIconCheckedMouseLeave = Nothing
        Me.cbxImportAloneBackup.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxImportAloneBackup.tbIconHoldPlace = True
        Me.cbxImportAloneBackup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxImportAloneBackup.tbIconIndeterminate = CType(resources.GetObject("cbxImportAloneBackup.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxImportAloneBackup.tbIconIndeterminateMouseDown = Nothing
        Me.cbxImportAloneBackup.tbIconIndeterminateMouseHover = Nothing
        Me.cbxImportAloneBackup.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxImportAloneBackup.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxImportAloneBackup.tbIconPlaceText = 1
        Me.cbxImportAloneBackup.tbIconUnChecked = CType(resources.GetObject("cbxImportAloneBackup.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxImportAloneBackup.tbIconUnCheckedMouseDown = Nothing
        Me.cbxImportAloneBackup.tbIconUnCheckedMouseHover = Nothing
        Me.cbxImportAloneBackup.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxImportAloneBackup.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxImportAloneBackup.tbImageBackground = Nothing
        Me.cbxImportAloneBackup.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxImportAloneBackup.tbImageCheckedMouseDown = Nothing
        Me.cbxImportAloneBackup.tbImageCheckedMouseHover = Nothing
        Me.cbxImportAloneBackup.tbImageCheckedMouseLeave = Nothing
        Me.cbxImportAloneBackup.tbImageUnCheckedMouseDown = Nothing
        Me.cbxImportAloneBackup.tbImageUnCheckedMouseHover = Nothing
        Me.cbxImportAloneBackup.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxImportAloneBackup.tbReadOnly = False
        Me.cbxImportAloneBackup.tbShadow = False
        Me.cbxImportAloneBackup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxImportAloneBackup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxImportAloneBackup.tbSplit = "3,3,3,3"
        Me.cbxImportAloneBackup.tbToolTip = ""
        Me.cbxImportAloneBackup.Text = "导入单独备份"
        Me.cbxImportAloneBackup.UseVisualStyleBackColor = False
        '
        'cbxWeChatIsDoFindDate
        '
        Me.cbxWeChatIsDoFindDate.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxWeChatIsDoFindDate.BackColor = System.Drawing.Color.Transparent
        Me.cbxWeChatIsDoFindDate.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxWeChatIsDoFindDate.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxWeChatIsDoFindDate.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxWeChatIsDoFindDate.Location = New System.Drawing.Point(70, 330)
        Me.cbxWeChatIsDoFindDate.Name = "cbxWeChatIsDoFindDate"
        Me.cbxWeChatIsDoFindDate.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxWeChatIsDoFindDate.Size = New System.Drawing.Size(164, 18)
        Me.cbxWeChatIsDoFindDate.TabIndex = 78
        Me.cbxWeChatIsDoFindDate.tbAdriftIconWhenHover = False
        Me.cbxWeChatIsDoFindDate.tbAutoSize = False
        Me.cbxWeChatIsDoFindDate.tbAutoSizeEx = True
        Me.cbxWeChatIsDoFindDate.tbIconChecked = CType(resources.GetObject("cbxWeChatIsDoFindDate.tbIconChecked"), System.Drawing.Image)
        Me.cbxWeChatIsDoFindDate.tbIconCheckedMouseDown = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconCheckedMouseHover = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconCheckedMouseLeave = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWeChatIsDoFindDate.tbIconHoldPlace = True
        Me.cbxWeChatIsDoFindDate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxWeChatIsDoFindDate.tbIconIndeterminate = CType(resources.GetObject("cbxWeChatIsDoFindDate.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxWeChatIsDoFindDate.tbIconIndeterminateMouseDown = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconIndeterminateMouseHover = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxWeChatIsDoFindDate.tbIconPlaceText = 1
        Me.cbxWeChatIsDoFindDate.tbIconUnChecked = CType(resources.GetObject("cbxWeChatIsDoFindDate.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxWeChatIsDoFindDate.tbIconUnCheckedMouseDown = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconUnCheckedMouseHover = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxWeChatIsDoFindDate.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWeChatIsDoFindDate.tbImageBackground = Nothing
        Me.cbxWeChatIsDoFindDate.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxWeChatIsDoFindDate.tbImageCheckedMouseDown = Nothing
        Me.cbxWeChatIsDoFindDate.tbImageCheckedMouseHover = Nothing
        Me.cbxWeChatIsDoFindDate.tbImageCheckedMouseLeave = Nothing
        Me.cbxWeChatIsDoFindDate.tbImageUnCheckedMouseDown = Nothing
        Me.cbxWeChatIsDoFindDate.tbImageUnCheckedMouseHover = Nothing
        Me.cbxWeChatIsDoFindDate.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxWeChatIsDoFindDate.tbReadOnly = False
        Me.cbxWeChatIsDoFindDate.tbShadow = False
        Me.cbxWeChatIsDoFindDate.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxWeChatIsDoFindDate.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxWeChatIsDoFindDate.tbSplit = "3,3,3,3"
        Me.cbxWeChatIsDoFindDate.tbToolTip = ""
        Me.cbxWeChatIsDoFindDate.Text = "自动查找删除的聊天记录"
        Me.cbxWeChatIsDoFindDate.UseVisualStyleBackColor = False
        '
        'cbxWCExportFormatTxt
        '
        Me.cbxWCExportFormatTxt.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxWCExportFormatTxt.BackColor = System.Drawing.Color.Transparent
        Me.cbxWCExportFormatTxt.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxWCExportFormatTxt.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxWCExportFormatTxt.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxWCExportFormatTxt.Location = New System.Drawing.Point(632, 227)
        Me.cbxWCExportFormatTxt.Name = "cbxWCExportFormatTxt"
        Me.cbxWCExportFormatTxt.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxWCExportFormatTxt.Size = New System.Drawing.Size(50, 18)
        Me.cbxWCExportFormatTxt.TabIndex = 80
        Me.cbxWCExportFormatTxt.tbAdriftIconWhenHover = False
        Me.cbxWCExportFormatTxt.tbAutoSize = False
        Me.cbxWCExportFormatTxt.tbAutoSizeEx = True
        Me.cbxWCExportFormatTxt.tbIconChecked = CType(resources.GetObject("cbxWCExportFormatTxt.tbIconChecked"), System.Drawing.Image)
        Me.cbxWCExportFormatTxt.tbIconCheckedMouseDown = Nothing
        Me.cbxWCExportFormatTxt.tbIconCheckedMouseHover = Nothing
        Me.cbxWCExportFormatTxt.tbIconCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatTxt.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatTxt.tbIconHoldPlace = True
        Me.cbxWCExportFormatTxt.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxWCExportFormatTxt.tbIconIndeterminate = CType(resources.GetObject("cbxWCExportFormatTxt.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxWCExportFormatTxt.tbIconIndeterminateMouseDown = Nothing
        Me.cbxWCExportFormatTxt.tbIconIndeterminateMouseHover = Nothing
        Me.cbxWCExportFormatTxt.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxWCExportFormatTxt.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatTxt.tbIconPlaceText = 1
        Me.cbxWCExportFormatTxt.tbIconUnChecked = CType(resources.GetObject("cbxWCExportFormatTxt.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxWCExportFormatTxt.tbIconUnCheckedMouseDown = Nothing
        Me.cbxWCExportFormatTxt.tbIconUnCheckedMouseHover = Nothing
        Me.cbxWCExportFormatTxt.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatTxt.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatTxt.tbImageBackground = Nothing
        Me.cbxWCExportFormatTxt.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxWCExportFormatTxt.tbImageCheckedMouseDown = Nothing
        Me.cbxWCExportFormatTxt.tbImageCheckedMouseHover = Nothing
        Me.cbxWCExportFormatTxt.tbImageCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatTxt.tbImageUnCheckedMouseDown = Nothing
        Me.cbxWCExportFormatTxt.tbImageUnCheckedMouseHover = Nothing
        Me.cbxWCExportFormatTxt.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatTxt.tbReadOnly = False
        Me.cbxWCExportFormatTxt.tbShadow = False
        Me.cbxWCExportFormatTxt.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxWCExportFormatTxt.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxWCExportFormatTxt.tbSplit = "3,3,3,3"
        Me.cbxWCExportFormatTxt.tbToolTip = ""
        Me.cbxWCExportFormatTxt.Text = "txt"
        Me.cbxWCExportFormatTxt.UseVisualStyleBackColor = False
        Me.cbxWCExportFormatTxt.Visible = False
        '
        'lblExport
        '
        Me.lblExport.BackColor = System.Drawing.Color.Transparent
        Me.lblExport.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lblExport.Location = New System.Drawing.Point(430, 203)
        Me.lblExport.Name = "lblExport"
        Me.lblExport.Size = New System.Drawing.Size(239, 17)
        Me.lblExport.TabIndex = 79
        Me.lblExport.Text = "导出到电脑格式设置"
        Me.lblExport.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblExport.Visible = False
        '
        'cbxWCExportFormatExcel
        '
        Me.cbxWCExportFormatExcel.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxWCExportFormatExcel.BackColor = System.Drawing.Color.Transparent
        Me.cbxWCExportFormatExcel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxWCExportFormatExcel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxWCExportFormatExcel.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxWCExportFormatExcel.Location = New System.Drawing.Point(538, 227)
        Me.cbxWCExportFormatExcel.Name = "cbxWCExportFormatExcel"
        Me.cbxWCExportFormatExcel.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxWCExportFormatExcel.Size = New System.Drawing.Size(62, 18)
        Me.cbxWCExportFormatExcel.TabIndex = 81
        Me.cbxWCExportFormatExcel.tbAdriftIconWhenHover = False
        Me.cbxWCExportFormatExcel.tbAutoSize = False
        Me.cbxWCExportFormatExcel.tbAutoSizeEx = True
        Me.cbxWCExportFormatExcel.tbIconChecked = CType(resources.GetObject("cbxWCExportFormatExcel.tbIconChecked"), System.Drawing.Image)
        Me.cbxWCExportFormatExcel.tbIconCheckedMouseDown = Nothing
        Me.cbxWCExportFormatExcel.tbIconCheckedMouseHover = Nothing
        Me.cbxWCExportFormatExcel.tbIconCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatExcel.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatExcel.tbIconHoldPlace = True
        Me.cbxWCExportFormatExcel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxWCExportFormatExcel.tbIconIndeterminate = CType(resources.GetObject("cbxWCExportFormatExcel.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxWCExportFormatExcel.tbIconIndeterminateMouseDown = Nothing
        Me.cbxWCExportFormatExcel.tbIconIndeterminateMouseHover = Nothing
        Me.cbxWCExportFormatExcel.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxWCExportFormatExcel.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatExcel.tbIconPlaceText = 1
        Me.cbxWCExportFormatExcel.tbIconUnChecked = CType(resources.GetObject("cbxWCExportFormatExcel.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxWCExportFormatExcel.tbIconUnCheckedMouseDown = Nothing
        Me.cbxWCExportFormatExcel.tbIconUnCheckedMouseHover = Nothing
        Me.cbxWCExportFormatExcel.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatExcel.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatExcel.tbImageBackground = Nothing
        Me.cbxWCExportFormatExcel.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxWCExportFormatExcel.tbImageCheckedMouseDown = Nothing
        Me.cbxWCExportFormatExcel.tbImageCheckedMouseHover = Nothing
        Me.cbxWCExportFormatExcel.tbImageCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatExcel.tbImageUnCheckedMouseDown = Nothing
        Me.cbxWCExportFormatExcel.tbImageUnCheckedMouseHover = Nothing
        Me.cbxWCExportFormatExcel.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatExcel.tbReadOnly = False
        Me.cbxWCExportFormatExcel.tbShadow = False
        Me.cbxWCExportFormatExcel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxWCExportFormatExcel.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxWCExportFormatExcel.tbSplit = "3,3,3,3"
        Me.cbxWCExportFormatExcel.tbToolTip = ""
        Me.cbxWCExportFormatExcel.Text = "excel"
        Me.cbxWCExportFormatExcel.UseVisualStyleBackColor = False
        Me.cbxWCExportFormatExcel.Visible = False
        '
        'cbxWCExportFormatHtml
        '
        Me.cbxWCExportFormatHtml.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxWCExportFormatHtml.BackColor = System.Drawing.Color.Transparent
        Me.cbxWCExportFormatHtml.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxWCExportFormatHtml.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxWCExportFormatHtml.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxWCExportFormatHtml.Location = New System.Drawing.Point(450, 227)
        Me.cbxWCExportFormatHtml.Name = "cbxWCExportFormatHtml"
        Me.cbxWCExportFormatHtml.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxWCExportFormatHtml.Size = New System.Drawing.Size(56, 18)
        Me.cbxWCExportFormatHtml.TabIndex = 82
        Me.cbxWCExportFormatHtml.tbAdriftIconWhenHover = False
        Me.cbxWCExportFormatHtml.tbAutoSize = False
        Me.cbxWCExportFormatHtml.tbAutoSizeEx = True
        Me.cbxWCExportFormatHtml.tbIconChecked = CType(resources.GetObject("cbxWCExportFormatHtml.tbIconChecked"), System.Drawing.Image)
        Me.cbxWCExportFormatHtml.tbIconCheckedMouseDown = Nothing
        Me.cbxWCExportFormatHtml.tbIconCheckedMouseHover = Nothing
        Me.cbxWCExportFormatHtml.tbIconCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatHtml.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatHtml.tbIconHoldPlace = True
        Me.cbxWCExportFormatHtml.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxWCExportFormatHtml.tbIconIndeterminate = CType(resources.GetObject("cbxWCExportFormatHtml.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxWCExportFormatHtml.tbIconIndeterminateMouseDown = Nothing
        Me.cbxWCExportFormatHtml.tbIconIndeterminateMouseHover = Nothing
        Me.cbxWCExportFormatHtml.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxWCExportFormatHtml.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatHtml.tbIconPlaceText = 1
        Me.cbxWCExportFormatHtml.tbIconUnChecked = CType(resources.GetObject("cbxWCExportFormatHtml.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxWCExportFormatHtml.tbIconUnCheckedMouseDown = Nothing
        Me.cbxWCExportFormatHtml.tbIconUnCheckedMouseHover = Nothing
        Me.cbxWCExportFormatHtml.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatHtml.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWCExportFormatHtml.tbImageBackground = Nothing
        Me.cbxWCExportFormatHtml.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxWCExportFormatHtml.tbImageCheckedMouseDown = Nothing
        Me.cbxWCExportFormatHtml.tbImageCheckedMouseHover = Nothing
        Me.cbxWCExportFormatHtml.tbImageCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatHtml.tbImageUnCheckedMouseDown = Nothing
        Me.cbxWCExportFormatHtml.tbImageUnCheckedMouseHover = Nothing
        Me.cbxWCExportFormatHtml.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxWCExportFormatHtml.tbReadOnly = False
        Me.cbxWCExportFormatHtml.tbShadow = False
        Me.cbxWCExportFormatHtml.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxWCExportFormatHtml.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxWCExportFormatHtml.tbSplit = "3,3,3,3"
        Me.cbxWCExportFormatHtml.tbToolTip = ""
        Me.cbxWCExportFormatHtml.Text = "html"
        Me.cbxWCExportFormatHtml.UseVisualStyleBackColor = False
        Me.cbxWCExportFormatHtml.Visible = False
        '
        'cbxWeChatExportSuccessOpenFolder
        '
        Me.cbxWeChatExportSuccessOpenFolder.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxWeChatExportSuccessOpenFolder.BackColor = System.Drawing.Color.Transparent
        Me.cbxWeChatExportSuccessOpenFolder.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxWeChatExportSuccessOpenFolder.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.cbxWeChatExportSuccessOpenFolder.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.cbxWeChatExportSuccessOpenFolder.Location = New System.Drawing.Point(70, 354)
        Me.cbxWeChatExportSuccessOpenFolder.Name = "cbxWeChatExportSuccessOpenFolder"
        Me.cbxWeChatExportSuccessOpenFolder.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxWeChatExportSuccessOpenFolder.Size = New System.Drawing.Size(212, 18)
        Me.cbxWeChatExportSuccessOpenFolder.TabIndex = 83
        Me.cbxWeChatExportSuccessOpenFolder.tbAdriftIconWhenHover = False
        Me.cbxWeChatExportSuccessOpenFolder.tbAutoSize = False
        Me.cbxWeChatExportSuccessOpenFolder.tbAutoSizeEx = True
        Me.cbxWeChatExportSuccessOpenFolder.tbIconChecked = CType(resources.GetObject("cbxWeChatExportSuccessOpenFolder.tbIconChecked"), System.Drawing.Image)
        Me.cbxWeChatExportSuccessOpenFolder.tbIconCheckedMouseDown = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconCheckedMouseHover = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconCheckedMouseLeave = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWeChatExportSuccessOpenFolder.tbIconHoldPlace = True
        Me.cbxWeChatExportSuccessOpenFolder.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxWeChatExportSuccessOpenFolder.tbIconIndeterminate = CType(resources.GetObject("cbxWeChatExportSuccessOpenFolder.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxWeChatExportSuccessOpenFolder.tbIconIndeterminateMouseDown = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconIndeterminateMouseHover = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxWeChatExportSuccessOpenFolder.tbIconPlaceText = 1
        Me.cbxWeChatExportSuccessOpenFolder.tbIconUnChecked = CType(resources.GetObject("cbxWeChatExportSuccessOpenFolder.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxWeChatExportSuccessOpenFolder.tbIconUnCheckedMouseDown = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconUnCheckedMouseHover = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxWeChatExportSuccessOpenFolder.tbImageBackground = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxWeChatExportSuccessOpenFolder.tbImageCheckedMouseDown = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbImageCheckedMouseHover = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbImageCheckedMouseLeave = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbImageUnCheckedMouseDown = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbImageUnCheckedMouseHover = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxWeChatExportSuccessOpenFolder.tbReadOnly = False
        Me.cbxWeChatExportSuccessOpenFolder.tbShadow = False
        Me.cbxWeChatExportSuccessOpenFolder.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxWeChatExportSuccessOpenFolder.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxWeChatExportSuccessOpenFolder.tbSplit = "3,3,3,3"
        Me.cbxWeChatExportSuccessOpenFolder.tbToolTip = ""
        Me.cbxWeChatExportSuccessOpenFolder.Text = "导出成功后是否自动打开所在目录"
        Me.cbxWeChatExportSuccessOpenFolder.UseVisualStyleBackColor = False
        '
        'lblFirstExport
        '
        Me.lblFirstExport.BackColor = System.Drawing.Color.Transparent
        Me.lblFirstExport.ForeColor = System.Drawing.Color.Red
        Me.lblFirstExport.Location = New System.Drawing.Point(433, 259)
        Me.lblFirstExport.Name = "lblFirstExport"
        Me.lblFirstExport.Size = New System.Drawing.Size(341, 17)
        Me.lblFirstExport.TabIndex = 84
        Me.lblFirstExport.Text = "第一次导出请选择导出格式"
        Me.lblFirstExport.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFirstExport.Visible = False
        '
        'frmWeChatSetting
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(680, 486)
        Me.Controls.Add(Me.lblFirstExport)
        Me.Controls.Add(Me.cbxWeChatExportSuccessOpenFolder)
        Me.Controls.Add(Me.cbxWCExportFormatHtml)
        Me.Controls.Add(Me.cbxWCExportFormatExcel)
        Me.Controls.Add(Me.cbxWCExportFormatTxt)
        Me.Controls.Add(Me.lblExport)
        Me.Controls.Add(Me.cbxWeChatIsDoFindDate)
        Me.Controls.Add(Me.cbxImportAloneBackup)
        Me.Controls.Add(Me.cbxExcelCoding)
        Me.Controls.Add(Me.lblExportExcel)
        Me.Controls.Add(Me.lblOther)
        Me.Controls.Add(Me.cbxWeChatDataMigration)
        Me.Controls.Add(Me.cbxExportVideo)
        Me.Controls.Add(Me.cbxExportPicture)
        Me.Controls.Add(Me.cbxExportVoice)
        Me.Controls.Add(Me.cbxExportAudioConvert)
        Me.Controls.Add(Me.lblExportSetting)
        Me.Controls.Add(Me.chkLinkIsExcel)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.btn_close)
        Me.MinimumSize = New System.Drawing.Size(11, 87)
        Me.Name = "frmWeChatSetting"
        Me.ShowInTaskbar = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,55"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 7)
        Me.Text = "WeChatSetting"
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents btnCancel As iTong.Components.tbButton
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents chkLinkIsExcel As iTong.Components.tbCheckBox
    Friend WithEvents lblExportSetting As System.Windows.Forms.Label
    Friend WithEvents cbxExportAudioConvert As iTong.Components.tbCheckBox
    Friend WithEvents cbxExportVoice As iTong.Components.tbCheckBox
    Friend WithEvents cbxExportPicture As iTong.Components.tbCheckBox
    Friend WithEvents cbxExportVideo As iTong.Components.tbCheckBox
    Friend WithEvents lblOther As System.Windows.Forms.Label
    Friend WithEvents lblExportExcel As System.Windows.Forms.Label
    Friend WithEvents cbxExcelCoding As System.Windows.Forms.ComboBox
    Friend WithEvents cbxWeChatDataMigration As iTong.Components.tbCheckBox
    Friend WithEvents cbxImportAloneBackup As iTong.Components.tbCheckBox
    Friend WithEvents cbxWeChatIsDoFindDate As iTong.Components.tbCheckBox
    Friend WithEvents cbxWCExportFormatTxt As iTong.Components.tbCheckBox
    Friend WithEvents lblExport As System.Windows.Forms.Label
    Friend WithEvents cbxWCExportFormatExcel As iTong.Components.tbCheckBox
    Friend WithEvents cbxWCExportFormatHtml As iTong.Components.tbCheckBox
    Friend WithEvents cbxWeChatExportSuccessOpenFolder As iTong.Components.tbCheckBox
    Friend WithEvents lblFirstExport As System.Windows.Forms.Label
End Class
