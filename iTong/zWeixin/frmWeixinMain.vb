﻿Imports System.Threading
Imports ProgressStyle = iTong.Components.ProgressStyle

'#If IS_iCloud Then
''''''''''''''''''''''''''''''''''''''''''''''''
'' 引入iCloud所涉及的空间
Imports iCloudRescue
Imports iCloudRescue.iCloud
Imports iCloudRescue.Util
Imports iCloudRescue.iCloudBackup
Imports iCloudRescue.Datas

'#End If

Public Class frmWeixinMain

#Region "--- 属性 ---"

    Private mDgvBackupList As New tbDataGridViewEx
    Private mDgvCharge As New tbDataGridViewEx
    Private mChargeHelper As ChargeHelper = Nothing
    Private mBackupHelper As BackupHelper = Nothing
    Private mCurrentRestoreRow As tbDataGridViewRow = Nothing
    Private mCurrentAgainFailedRow As tbDataGridViewRow = Nothing
    Private mTDGetDeviceImageFromLocal As Thread = Nothing
    Private mTDLoadBackUpList As Thread = Nothing
    'Private mAloneBackupsHelper As AloneBackupsHelper = Nothing
    Private mAloneBackupList As New List(Of BackupInfo)
    Private mIsLoadAloneBackUp As Boolean = True
    Public mIsCurrentDeviceBeAloneBackup As Boolean = False

    Public Property StrlblState() As String
        Get
            Return Me.lblState.Text
        End Get
        Set(ByVal value As String)
            Me.lblState.Text = value
            If value.Length > 0 Then
                lblState.Visible = True
            Else
                lblState.Visible = False
            End If
            Me.lblState.ForeColor = Color.FromArgb(82, 82, 82)
        End Set
    End Property

    Private mCurrentSelectBackupInfo As BackupInfo = Nothing
    Private mShowTime As Integer = 0
    Public mIsFromBackupShow As Boolean = True


    Private mAloneBackupIniFailure As New List(Of String)

    Private mABType As AloneBackupType = AloneBackupType.None
    Private mStrSku As String = ""
    Private mStrAloneBackupIniFile As String = ""

    Private miTunesBackupEncrypt As Boolean = False

    Private mBackupListDeleteHelper As BackupListDeleteHelper = Nothing

    Private frmWX As frmWeixin = Nothing
    Private mDoDecryptCancel As Boolean = False

    Private mCurrentWeixinMainViewStyle As WeixinMainViewStyle = WeixinMainViewStyle.LoadBackup

    Private mTDImportBackup As Thread = Nothing
    Private mTDImportAloneBackup As Thread = Nothing

    Public Enum BackupFolderType
        Full
        Quick
        Failure
    End Enum

    Private mTDDeleteTemp As Thread = Nothing
    Private mIsImportAloneBackup As Boolean = False

    Public ReadOnly Property CurrentIdentify() As String
        Get
            Dim strIdentify As String = String.Empty
            If Me.mCurrentSelectBackupInfo IsNot Nothing Then
                strIdentify = Me.mCurrentSelectBackupInfo.Identifier
            ElseIf Me.mDevice IsNot Nothing Then
                strIdentify = Me.mDevice.Identifier
            End If
            Return strIdentify
        End Get
    End Property

    Private mfrmBackupPreviewBackupInfo As BackupInfo = Nothing
#End Region

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice, ABType As AloneBackupType)
        Me.New(application, device, Nothing, False, ABType)

    End Sub

    Public Sub New(application As IApplication, device As iPhoneDevice, listRecord As List(Of MBFileRecord), blnIsEncrypted As Boolean, ABType As AloneBackupType, Optional ByVal BackObject As BackupInfo = Nothing)
        MyBase.New(application, device)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.FunctionMappingKey = FunctionKey.Weixin
        Me.tbAutoSetFormSize = True

        Me.Size = New Size(970, 650)
        Me.Icon = My.Resources.iTong

        Me.mABType = ABType

        Me.mfrmBackupPreviewBackupInfo = BackObject
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.SetStartLoadDevice(False, Me.Language.GetString("Common.Backup.DetectionAloneBackup"))
        Me.InitGridView()
        Me.InitChargeGridView()
        Me.InitBackupHelper()
        Me.IniBackupListDeleteHelper()
        Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
        '#If IS_iCloud Then
        Me.InitiCloudBackup()
        '#End If

        'Me.InitAloneBackupsHelper()
#If IS_WECHAT Then
        Me.picDefault.Image = iTong.My.Resources.Resources.new_device_default_ios
#Else
        Me.picDefault.Image = iTong.My.Resources.Resources.new_device_default
#End If
        'LoadBackUpList()

        If Me.mABType = AloneBackupType.WeChatSpare Then
            Me.pnlTip.Visible = True
        Else
            Me.pnlTip.Visible = False
        End If

        AloneBackupsHelper.GetQuickInfoByABType(Me.mABType, Me.mStrSku, "", "")
        If Folder.AppType = RunType.Tongbu_Abroad Then
            Me.btnRepair.Visible = False
        End If

        If Me.mABType = AloneBackupType.WeChatSpare Then
            Me.btnWeChatDataMigration.Visible = IniSetting.GetIsWeChatDataMigration()
        Else
            Me.btnWeChatDataMigration.Visible = False
        End If

        RemoveHandler frmWeChatSetting.WeChatSettingHandler, AddressOf frmWeChatSetting_WeChatSettingHandler
        AddHandler frmWeChatSetting.WeChatSettingHandler, AddressOf frmWeChatSetting_WeChatSettingHandler


        If Folder.AppType = RunType.iWeChatMigration Then
            Me.btnChangeSKU.Visible = True
            Me.btnDevice.tbIconImage = iTong.My.Resources.Resources.wechat_device_return
        End If
        If Folder.AppType = RunType.iWeChatMigration Then
            Me.mIsImportAloneBackup = IniSetting.GetIsWeChatImportAloneBackup()
        End If

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.tsmSetting.Text = Me.Language.GetString("Common.Button.Setting") & "(&O)"                '参数设置(&O)
        Me.btnStartLoadDevice.Text = Me.Language.GetString("Download.Button.Start") '"开始"
        Me.btnBackupStart.Text = Me.Language.GetString("Weixin.Button.AloneBackup") '“备份最新数据”
        Me.InitChargeHelper()

        Me.lblDevNoConn.Text = Me.Language.GetString("Welcome.Label.AccessDevice")
#If IS_WECHAT Then
        Me.SetMeText()

        Me.btnDevice_3.Text = Me.Language.GetString("Weixin.Button.BackupAttainFD") '通过扫描设备获取信息
        Me.btnDevice.Text = Me.Language.GetString("Weixin.Button.BackupAttainFD") '通过设备扫描恢复
        Me.btnDeviceEx.Text = Me.Language.GetString("Weixin.Button.BackupAttainFD") '通过设备扫描恢复
        Me.btnDeviceEx.tbToolTip = Me.Language.GetString("Weixin.Button.DeviceAttainExplainEx") '可恢复苹果设备的微信数据

        Me.btnBackup_3.Text = Me.Language.GetString("Weixin.Button.DeviceAttainFD") '微信单独备份还原
        Me.btnBackupEx.Text = Me.Language.GetString("Weixin.Button.DeviceAttainFD") '通过iTunes备份恢复
        Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.DeviceAttainFD") '通过iTunes备份恢复
        Me.btnBackup.tbToolTip = Me.Language.GetString("Weixin.Button.BackupAttainExplainEx") '通过iTunes备份文件进行恢复，完美支持恢复微信数据
#Else
        If Me.mABType = AloneBackupType.WeChat Then
            Me.Text = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") '微信消息记录
        ElseIf Me.mABType = AloneBackupType.WeChatSpare Then
            Me.Text = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") & "-" & Me.Language.GetString("WeChat.Lable.Coexist") '"共存版"
        ElseIf Me.mABType = AloneBackupType.WeChatVIPSpare Then
            Me.Text = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") & "-VIP"
        End If

        Me.btnDevice_3.Text = Me.Language.GetString("Weixin.Button.DeviceAttain") '通过扫描设备获取信息
        Me.btnDevice.Text = Me.Language.GetString("Weixin.Button.DeviceAttain") '通过扫描设备获取信息
        Me.btnDeviceEx.Text = Me.Language.GetString("Weixin.Button.DeviceAttain") '通过扫描设备获取信息
        Me.btnDeviceEx.tbToolTip = Me.Language.GetString("Weixin.Button.DeviceAttainExplain") '通过对设备微信安装目录的扫描，获取包括文本、图片、视频等数据。展示出来并可进行相应的管理。

        Me.btnBackup_3.Text = Me.Language.GetString("Weixin.Button.BackupAttain") '微信单独备份还原
        Me.btnBackupEx.Text = Me.Language.GetString("Weixin.Button.BackupAttain") '微信单独备份还原
        Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.BackupAttain") '微信单独备份还原
        Me.btnBackup.tbToolTip = Me.Language.GetString("Weixin.Button.BackupAttainExplain") '使用iTunes机制对微信进行整体信息备份，成功后可从备份文件中查看和管理信息。
#End If

        '--------------icloud----------------
        Me.btniCloud_1.Text = Me.Language.GetStringTrim("iCloud.Restore.Backup")
        Me.btniCloud_2.Text = Me.Language.GetStringTrim("iCloud.Restore.Backup")
        Me.btniCloud.Text = Me.Language.GetStringTrim("iCloud.Restore.Backup")
        Me.btniCloud.tbToolTip = Me.Language.GetStringTrim("iCloud.BackupWechat.Message")

        Me.btniCloudCancel.Text = Me.Language.GetStringTrim("iCloud.CancelDownload")
        Me.btniCloudForget.Text = Me.Language.GetStringTrim("iCloud.Forgot")
        Me.btniCloudView.Text = Me.Language.GetStringTrim("iCloud.View")
        Me.btniCloudPrivate.Text = Me.Language.GetStringTrim("iCloud.Private")
        Me.btniCloudLogout.Text = Me.Language.GetStringTrim("Login.Label.LogOff")

        Me.lbliCloudLoading.Text = Me.Language.GetStringTrim("iCloud.Get.BackupLists")
        Me.lbliCloudView.Text = Me.Language.GetStringTrim("Weixin.Button.BackupTitle")
        Me.lbliCloudTip_1.Text = Me.Language.GetStringTrim("iCloud.Tip_1.Message")
        Me.lbliCloudTip_2.Text = Me.Language.GetStringTrim("iCloud.Tip_2.Message")
        Me.btniCloudReturn.Text = Me.Language.GetStringTrim("Common.Button.Return")
        Me.lbliCloudNodata.Text = Me.Language.GetStringTrim("iCloud.NoData.Message")

        Dim fontF As Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.lbliCloudWechat.ColorTextList.Clear()
        Me.lbliCloudWechat.ColorTextList.AddItem(Me.Language.GetStringTrim("iCloud.YourUsing.Message"), 0, System.Drawing.Color.FromArgb(51, 51, 51), fontF, False)
        Me.lbliCloudWechat.ColorTextList.AddItem(Me.Language.GetString("iCloud.Wechat"), 1, System.Drawing.Color.FromArgb(0, 169, 255), fontF, False, True)
        Me.lbliCloudWechat.ColorTextList.AddItem(Me.Language.GetStringTrim("iCloud.iTunes.Message"), 2, System.Drawing.Color.FromArgb(51, 51, 51), fontF, False)

        Me.lbliCloudAcct.Text = Me.Language.GetStringTrim("iCloud.Account.Message")

        Me.lblIncompatible.Text = Me.Language.GetStringTrim("iCloud.Account.Incompatible")
        Me.lblCompatibility.Text = Me.Language.GetStringTrim("iCloud.Account.Compatibility")

        '------------------------------------

        Me.lblTitle.Text = Me.Language.GetString("Weixin.Button.BackupTitle") '请选择要查看的微信信息备份文件
        ' Me.lblOpenTutorial.Text = Me.Language.GetString("Weixin.Button.OpenTutorial") '备份失败？备份卡在某一处不动？戳我>>
        Me.lblStorageSpace.Text = Me.Language.GetString("Weixin.Button.BackupStorage") ' "提示：备份提示存储空间不足时删除无用的备份，可以扩大可用容量。"
        Me.lblPrompt.Text = Me.Language.GetString("Weixin.Button.BackupPrompt") '1、从备份文件读取微信信息能有效提高获取速度，首次备份所需时间会比较长   2、通过备份文件只能查看截止备份时间的数据，查看新数据，请快速备份


        Me.tsmiAllbackup.Text = Me.Language.GetString("Weixin.Button.ImportAllBackup") '导入整机备份
        Me.tsmiAloneBackup.Text = Me.Language.GetString("Weixin.Button.ImportAloneBackup") '导入单独备份
        Me.btnDeleteTemp.Text = Me.Language.GetString("Weixin.Button.ClearCache") '清理缓存

        Me.btnImport.Text = Me.Language.GetString("File.Button.UploadToDevice") '导入
        Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh") '刷新
        Me.btnRepair.Text = Me.Language.GetString("Tools.Button.Repair") '修复

        Me.btnWeChatDataMigration.Text = Me.Language.GetString("Common.Button.DataMigration") '数据迁移
        Me.btnChangeSKU.Text = String.Format("VIP{0}", Me.Language.GetString("Common.Button.DataMigration")) '数据迁移

        Me.btnRefresh.Location = New Point(Me.btnImport.Location.X - Me.btnRefresh.Width - 4, 28)
        Me.btnRepair.Location = New Point(Me.btnRefresh.Location.X - Me.btnRepair.Width - 4, 28)

        Me.tsmiDelete.Text = Me.Language.GetString("Common.Delete")
        Me.tsmiOpen.Text = Me.Language.GetString("Photo.Menu.View")                     '"查看"
        Me.tsmiOpenFolder.Text = Me.Language.GetString("Common.Button.OpenLocalFolder")

        Me.llblRefresh.Text = Me.Language.GetString("Weixin.Button.GetLatest") & ">>" '获取最新

        If Me.mDevice IsNot Nothing Then
            Me.lblNameValue.Text = Me.mDevice.DeviceName
        Else
            Me.btnBackupStart.Enabled = False
        End If

        Me.lblBackupPwdMsg.Text = Me.Language.GetString("Common.Backup.UnEncryptionPrompt") '"设备未设置备份密码，备份的文件不会被加密，在恢复到设备时无需输入密码，且备份的文件可以查看其内容。"

        Me.llblDeletePwd.Text = Me.Language.GetString("Common.DeleteBackupPwdEx") '"删除备份密码
        Me.btnQQ.Text = Me.Language.GetString("Weixin.Lable.Problem") '"遇到问题？"

        Me.btnQQ.Location = New Point(Me.btn_Setting.Location.X - Me.btnQQ.Width - 4, Me.btnQQ.Location.Y)

        If Me.mABType = AloneBackupType.WeChat Then
            Me.btnWeChat.Text = Me.Language.GetString("Weixin.Lable.ManagementWeChatCoexist") '"管理共存版"
            Me.btnWeChat.Visible = ServerIniSetting.IsShowMoreWeChatCoexist()
        ElseIf Me.mABType = AloneBackupType.WeChatSpare OrElse Me.mABType = AloneBackupType.WeChatVIPSpare Then
            Me.btnWeChat.Text = Me.Language.GetString("Weixin.Lable.StableVersion") '"我想要稳定版"
            Me.btnWeChat.Visible = ServerIniSetting.IsShowWeChatCoexist()
            Me.TbLabel1.Visible = ServerIniSetting.IsShowWeChatCoexist()
        End If


        If Folder.AppType = RunType.iWeChatMigration Then
            Me.btnWeChat.Visible = False
        End If

        Me.lblStartLoadDevice.Text = Me.Language.GetString("Common.Backup.DetectionAloneBackup") '"正在检测当前连接设备是否有微信备份，请耐心等候..."

        Me.btnBuy.Visible = True
        Me.SetBuyButtonStatus()

        If Folder.AppType = RunType.iWeChatMigration Then
            Me.SetViewStyle(WeixinMainViewStyle.MigrationHome)
            Me.btnDevice.Text = "返回首页"
        Else
            Dim ret As Integer = IniSetting.GetWeChatDefaultShowBackup()
            If ret = 1 Then
                Me.SetViewStyle(WeixinMainViewStyle.LoadBackup)
            ElseIf ret = 0 Then
                Me.SetViewStyle(WeixinMainViewStyle.LoadDevice)
            Else
                Me.SetViewStyle(WeixinMainViewStyle.iCloudBackup)
            End If
        End If

        Me.GetDeviceImageFromLocal()

    End Sub

    Protected Overrides Sub OnShown(e As System.EventArgs)
        MyBase.OnShown(e)

        If Me.mfrmBackupPreviewBackupInfo IsNot Nothing Then
            Me.ShowDetail(Me.mfrmBackupPreviewBackupInfo)
        End If
    End Sub

    Protected Overrides Sub OnFormClosed(ByVal e As System.Windows.Forms.FormClosedEventArgs)
        MyBase.OnFormClosed(e)

        Try
            Utility.AbortThread(Me.mTDGetDeviceImageFromLocal)
            Utility.AbortThread(Me.mTDImportBackup)
            Utility.AbortThread(Me.mTDImportAloneBackup)
            Utility.AbortThread(Me.mTDDeleteTemp)
            Utility.AbortThread(Me.mTDLoadBackUpList)
            Utility.AbortThread(Me.mthrRepair)
            Utility.AbortThread(Me.mTDImportBackup)

            RemoveHandler frmWeChatSetting.WeChatSettingHandler, AddressOf frmWeChatSetting_WeChatSettingHandler

            If Me.mBackupHelper IsNot Nothing Then
                RemoveHandler Me.mBackupHelper.BackupProgressChanged, AddressOf Me.OnBackupProgressChanged
                RemoveHandler Me.mBackupHelper.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged
                RemoveHandler Me.mBackupHelper.MigrationProgressChanged, AddressOf Me.OnMigrationProgressChanged
                Me.mBackupHelper.Cancel()
            End If

            If Me.mChargeHelper IsNot Nothing Then
                RemoveHandler Me.mChargeHelper.QueryCallback, AddressOf Me.OnQueryCallback
                RemoveHandler Me.mChargeHelper.ActiveCallback, AddressOf Me.OnActiveCallback
            End If

            iPhoneDevice.DoDecryptCancel()

            Me.mDoDecryptCancel = True

            If frmWX IsNot Nothing Then
                frmWX.Close()
            End If


#If IS_WECHAT Then
        Try
            MainFormDataRecovery.Instance().ExitApp()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWeixinMain.OnFormClosed")
        End Try
#End If

            '#If IS_iCloud Then
            Me.CleariCloud()
            '#End If

        Catch ex As Exception
        End Try
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
        Me.InitBackupHelper()
        Me.GetDeviceImageFromLocal()
        Me.SetRowCanRestore()
        Me.SetMeText()
        If Not Me.mCurrentWeixinMainViewStyle = WeixinMainViewStyle.Content Then
            If Folder.AppType <> RunType.iWeChatMigration Then
                If IniSetting.GetWeChatDefaultShowBackup() Then
                    Me.SetViewStyle(WeixinMainViewStyle.LoadBackup)
                Else
                    Me.SetViewStyle(WeixinMainViewStyle.LoadDevice)
                End If
            End If

            Me.pnlDeviceUnConnect.Visible = False
            Me.pnlDeviceConnect.Visible = True
            Me.btnBackupStart.Enabled = True
        End If


    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.SetRowCanRestore()
        Me.SetMeText()
#If IS_WECHAT Then
        If Not Me.mCurrentWeixinMainViewStyle = WeixinMainViewStyle.Content Then
            Me.pnlDeviceUnConnect.Visible = True
            Me.pnlDeviceConnect.Visible = False
        End If
        Me.btnBackupStart.Enabled = False
#Else
        'Me.Close()
#End If
    End Sub

    Private Sub InitGridView()
        Me.mDgvBackupList = New tbDataGridViewEx
        With Me.mDgvBackupList
            .SuspendLayout()
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 38
            .AllowDrop = True
            .MultiSelect = True
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .ShowCellToolTips = True
            .Columns.Clear()

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Common.Label.Name"), _
                                                                   145, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewMediaColumn), "colStatus", Me.Language.GetString("Backup.Label.Encypt"), _
                                                                   48, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colDateTime", Me.Language.GetString("App.Button.TimeSort"), _
                                                                   115, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"时间"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colSize", Me.Language.GetString("Common.Columns.Size"), _
                                                                   80, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"Size"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colVersion", Me.Language.GetString("App.Column.Version"), _
                                                                   60, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"当前版本"
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colDeviceType", Me.Language.GetString("App.Button.DeviceType"), _
                                                                   100, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False)) '"设备类型"           
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", "", 245, True, True, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, False, True, DataGridViewContentAlignment.MiddleLeft, True))
            .ContextMenuStrip = Me.munOperator
            .GroupRowTemplateArray = Me.GetGroupTemplate()
            .ResumeLayout()
        End With

        RemoveHandler mDgvBackupList.CellDoubleClick, AddressOf mDgvBackupList_CellDoubleClick
        AddHandler mDgvBackupList.CellDoubleClick, AddressOf mDgvBackupList_CellDoubleClick

        RemoveHandler mDgvBackupList.CellButtonClick, AddressOf mDgvBackupList_CellButtonClick
        AddHandler mDgvBackupList.CellButtonClick, AddressOf mDgvBackupList_CellButtonClick

        RemoveHandler mDgvBackupList.SelectionChanged, AddressOf mDgvBackupList_SelectionChanged
        AddHandler mDgvBackupList.SelectionChanged, AddressOf mDgvBackupList_SelectionChanged

        RemoveHandler mDgvBackupList.ShowToolTip, AddressOf mDgvBackupList_ShowToolTip
        AddHandler mDgvBackupList.ShowToolTip, AddressOf mDgvBackupList_ShowToolTip

        Me.pnlGridBackList.Controls.Add(Me.mDgvBackupList)

        Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.FailureBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"损坏备份"
        Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.SeparateBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"单独备份"
        Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.FullBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"整机备份"

    End Sub


    Private Sub InitChargeHelper()
        Dim projectType As ChargeProjectType = ChargeProjectType.Assistant_Wechat_iOS

        If Folder.AppType = RunType.iWeChatMigration Then
            projectType = ChargeProjectType.Assistant_Wechat_iOS_DataMove

        ElseIf Folder.AppType = RunType.WechatMaster Then
            projectType = ChargeProjectType.WechatManage

        ElseIf Folder.AppType = RunType.iDataRecovery Then
            projectType = ChargeProjectType.iDataRecovery

        ElseIf Folder.AppType = RunType.iDataRecovery_Abroad Then
            projectType = ChargeProjectType.iDataRecovery_Abroad

        ElseIf Folder.AppType = RunType.Tongbu_Abroad Then
            projectType = ChargeProjectType.Assistant_Abroad

        End If

        Me.mChargeHelper = ChargeHelper.Instance(projectType)
        AddHandler Me.mChargeHelper.QueryCallback, AddressOf OnQueryCallback
        AddHandler Me.mChargeHelper.ActiveCallback, AddressOf OnActiveCallback

        Me.mChargeHelper.Query()
    End Sub

    Private Function GetGroupTemplate() As Object()
        Return New Object() {"", _
                            True, _
                            "", _
                            "", _
                            "", _
                            "", _
                            "", _
                            "", _
                            ""}
    End Function

    Private Sub InitBackupHelper()
        Try
            Me.mBackupHelper = BackupHelper.Instance(Me.mDevice)

            AddHandler Me.mBackupHelper.BackupProgressChanged, AddressOf Me.OnBackupProgressChanged
            AddHandler Me.mBackupHelper.DecryptProgressChanged, AddressOf Me.OnDecryptProgressChanged
            AddHandler Me.mBackupHelper.MigrationProgressChanged, AddressOf OnMigrationProgressChanged
            AddHandler Me.mBackupHelper.LoadBackupList, AddressOf OnLoadBackupList

            Me.mBackupHelper.GetFullBackupList(False)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitBackupHelper")
        End Try
    End Sub

    Private Sub IniBackupListDeleteHelper()
        Try
            Me.mBackupListDeleteHelper = BackupListDeleteHelper.GetInstance()
            RemoveHandler Me.mBackupListDeleteHelper.BackupListDeleteEventHandler, AddressOf BackupListDeleteHelper_BackupListDeleteEventHandler
            AddHandler Me.mBackupListDeleteHelper.BackupListDeleteEventHandler, AddressOf BackupListDeleteHelper_BackupListDeleteEventHandler
        Catch ex As Exception
            Common.LogException(ex.ToString(), "IniBackupListDeleteHelper")
        End Try
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnBackup_Click(sender As Object, e As EventArgs) Handles btnBackup.Click, btnBackupEx.Click, btnBackup_3.Click
        Me.SetViewStyle(WeixinMainViewStyle.LoadBackup)
    End Sub

    Private Sub btnDevice_Click(sender As Object, e As EventArgs) Handles btnDevice.Click, btnDeviceEx.Click, btnDevice_3.Click
        If Folder.AppType = RunType.iWeChatMigration Then
            Me.SetViewStyle(WeixinMainViewStyle.MigrationHome)
            Me.mWCMCinfo = Nothing
            SetMeText()
        Else
            Me.SetViewStyle(WeixinMainViewStyle.LoadDevice)
        End If
    End Sub


    Public Sub mDgvBackupList_CellDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs)
        Try
            '双击查看还原列不显示详细信息
            If e.RowIndex < 0 OrElse e.ColumnIndex >= 7 Then
                Return
            End If

            If Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing Then
                Me.ShowDetail(Me.mDgvBackupList.SelectedRows(0).Tag)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "mDgvBackupList_CellDoubleClick")
        End Try
    End Sub

    Private Sub mDgvBackupList_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Dim row As DataGridViewRow = Me.mDgvBackupList.Rows(e.Cell.RowIndex)
        If row.Tag Is Nothing Then
            Return
        End If
        Dim info As BackupInfo = row.Tag
        Select Case e.CellButton
            Case CellButtonStyle.Backup
                Me.ShowDetail(info)

            Case CellButtonStyle.Uninstall
                If Me.mDevice Is Nothing Then
                    Return
                End If
                'If ServerIniSetting.GetWechatAllRestore() AndAlso Me.mDevice.ProductType.StartsWith("iPhone11,", StringComparison.InvariantCultureIgnoreCase) Then
                '    '"由于苹果的限制iPhoneXs、iPhoneXs (Max)、iPhoneXr无法单独还原微信，您可以到""更多功能->备份还原""中进行整机还原。" & vbCrLf & "注意：整机还原将会覆盖设备上所有数据。"
                '    If tbMessageBox.Show(Me.Language.GetString("WeChat.Message.RestoreDevice"), Me.Language.GetString("Weixin.Lable.Message"), MessageBoxButtons.OKCancel) = Windows.Forms.DialogResult.OK Then
                '        frmSummary.RunApp(Me.mApplication, Me.mDevice, GetType(frmBackupList), Nothing, True, AloneBackupType.None)
                '    End If
                '    Return
                'End If
                Me.OpenBackupRestoreForm(BackupRestore.Restore, info)

            Case CellButtonStyle.Import
                Me.mCurrentRestoreRow = row
                Me.DeleteBackup(row)

        End Select
    End Sub

    Public Sub mDgvBackupList_SelectionChanged(sender As Object, e As System.EventArgs)
        If Common.IsChangeSku() AndAlso Me.mABType = AloneBackupType.WeChatVIPSpare Then
            Me.btnChangeSKU.Visible = True
        Else
            Dim blnCanRepair = False
            For Each item As tbDataGridViewRow In Me.mDgvBackupList.SelectedRows
                If Me.CheckRowCanRepair(item) Then
                    blnCanRepair = True
                End If
            Next
        End If
    End Sub

    Private intRowIndex As Integer = -1
    Private Sub mDgvBackupList_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Dim row As DataGridViewRow = Me.mDgvBackupList.Rows(e.Cell.RowIndex)
        Dim pos As Point = Windows.Forms.Cursor.Position
        Dim secPos As Point = Me.PointToClient(pos)
        Dim newPos As New Point(secPos.X + 10, secPos.Y + 10)
        Dim cellProgressEx As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        Try
            Select Case e.CellButton
                Case CellButtonStyle.Uninstall
                    Dim info As BackupInfo = CType(row.Tag, BackupInfo)

                    If cellProgressEx.tbCellButtonEnable = CellButtonStyle.CrakedInfo Then
                        Return
                    End If
                    If Me.mDevice IsNot Nothing Then
                        Me.tipDgvCell.Show(String.Format(Me.Language.GetString("BackupList.Column.HintVersion"), info.ProductVersion, Me.mDevice.ProductVersion), Me, newPos) '备份固件版本：{0} 无法还原到您设备固件版本：{1}
                    Else
                        Me.tipDgvCell.Show(Me.Language.GetString("Welcome.Label.AccessDevice"), Me, newPos) '"请连接设备"
                    End If

                Case CellButtonStyle.None
                    Me.tipDgvCell.Hide(Me)

            End Select
        Catch ex As Exception
        End Try

        If TypeOf e.Cell Is tbDataGridViewProgressCellEx AndAlso e.CellButton = CellButtonStyle.OneButton Then

        ElseIf TypeOf e.Cell Is tbDataGridViewTextBoxCellEx Then
            Try
                If e.Cell.RowIndex <> intRowIndex AndAlso Me.munOperator.Visible = False AndAlso row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is BackupInfo Then
                    intRowIndex = e.Cell.RowIndex
                    Dim bakObject As BackupInfo = row.Tag
                    Dim strTip As New StringBuilder

                    strTip.AppendLine(Me.Language.GetString("Common.Label.Name") & Me.Language.GetString("Common.Symbol.Colon") & " " & bakObject.DisplayName)        '"名称："
                    strTip.AppendLine(Me.Language.GetString("App.Button.TimeSort") & Me.Language.GetString("Common.Symbol.Colon") & " " & bakObject.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute)))   '"时间："
                    strTip.AppendLine(Me.Language.GetString("Common.Columns.Size") & Me.Language.GetString("Common.Symbol.Colon") & " " & Utility.FormatFileSize(bakObject.Size)) '"大小："
                    strTip.AppendLine(Me.Language.GetString("App.Column.Version") & Me.Language.GetString("Common.Symbol.Colon") & " " & String.Format("{0}({1})", bakObject.ProductVersion, bakObject.BuildVersion)) '"版本："
                    strTip.AppendLine(Me.Language.GetString("Backup.Label.Key") & Me.Language.GetString("Common.Symbol.Colon") & " " & bakObject.Identifier)      '"标识符："
                    strTip.AppendLine(Me.Language.GetString("Welcome.Label.SerialNumber") & Me.Language.GetString("Common.Symbol.Colon") & " " & bakObject.SerialNumber)      '"序列号："
                    Dim strFolderPath As String = bakObject.FolderPath
                    If strFolderPath.Length > 70 Then
                        Dim intIndex As Integer = strFolderPath.Substring(0, 70).LastIndexOf("\")
                        strFolderPath = strFolderPath.Substring(0, intIndex) & vbCrLf & strFolderPath.Substring(intIndex, strFolderPath.Length - intIndex)
                    End If
                    strTip.AppendLine(Me.Language.GetString("Components.ImageView.Label.Location") & Me.Language.GetString("Common.Symbol.Colon") & " " & strFolderPath)   '"路径："

                    Me.tipDgvCell.Show(strTip.ToString(), Me, newPos)
                Else
                    Me.tipDgvCell.Hide(Me)
                End If
            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub DeleteBackup(row As DataGridViewRow)

        '"您确定删除所选的备份项目以及备份数据？"
        If tbMessageBox.Show(Me, Me.Language.GetString("Backup.Message.DeleteBackupFiles"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) <> Windows.Forms.DialogResult.OK Then
            Return
        End If

        Dim lstRows As New List(Of tbDataGridViewRow)
        lstRows.Add(row)
        Me.mBackupListDeleteHelper.DeleteByRows(lstRows)

    End Sub

    Protected Function CheckOpenByAfc2() As Boolean
        Return Me.mDevice IsNot Nothing AndAlso Me.mDevice.Jailbreaked AndAlso Me.mDevice.VersionNumber >= 830
    End Function

    Private Sub btnStartLoadDevice_Click(sender As Object, e As EventArgs) Handles btnStartLoadDevice.Click
        Try
            Dim isNeedBackup As Boolean = True
            Dim isNewBackup As Boolean = True

            '判断是否需要走备份逻辑读取
            If (Me.mDevice IsNot Nothing AndAlso Me.mDevice.Jailbreaked AndAlso Me.mDevice.VersionNumber >= 830) OrElse Me.mDevice.CheckUIFileSharingEnabled(Me.mStrSku) OrElse Me.mABType <> AloneBackupType.WeChat Then
                isNeedBackup = False
            End If

            If Not isNeedBackup Then
                Me.LoadWeChat(False, isNewBackup)
                Me.mCurrentSelectBackupInfo = Nothing
                Return
            End If

            '如果需要 从备份  判断当前设备是否是第一次备份
            Dim isFirst As Boolean = Not BackupHelper.ExistBackup(Me.mDevice.Identifier)

            If Not isFirst Then

                Dim strMsg As String = Me.Language.GetString("Common.Backup.Prompt") '"发现您的设备在电脑上已经有备份过微信。快速备份：在原有备份的基础上，新增修改或删除，时间较短。（原有备份会被覆盖）新备份：重新完整的备份微信所有数据，需要的时间长，文件所占空间大。"

                Dim isNoArgenShow As Boolean = False
                'If IniSetting.GetShowStartLoadDeviceHint() Then

                Dim dr As DialogResult = tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNoCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button3, "", False, New String() {Me.Language.GetString("Common.Backup.DeltaBackup"), Me.Language.GetString("Common.Backup.NewBackup"), Me.Language.GetString("WeChat.Button.CheckBackup")}) '"查看备份"

                If dr = Windows.Forms.DialogResult.Yes Then
                    Dim lst As New List(Of String)
                    lst.Add(Me.Language.GetString("Backup.Button.BackupIncremental"))
                    Dim frm As tbRemind = New Components.tbRemind(Me.mApplication, lst)
                    If frm.ShowDialog(Me) <> Windows.Forms.DialogResult.OK Then
                        Return
                    End If
                    isNewBackup = False
                ElseIf dr = Windows.Forms.DialogResult.No Then
                    isNewBackup = True
                Else
                    Me.SetViewStyle(WeixinMainViewStyle.LoadBackup)
                    Return
                End If

            End If

            Me.mCurrentSelectBackupInfo = Nothing
            Me.LoadWeChat(False, isNewBackup)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnStartLoadDevice_Click")
        End Try
    End Sub

    Private Sub btnBackupStart_Click(sender As Object, e As EventArgs) Handles btnBackupStart.Click
        If Me.mDevice Is Nothing Then
            tbMessageBox.Show(Me, Me.Language.GetString("Welcome.Label.AccessDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
            Return
        End If

        Dim autoOpen As Boolean = True
        'If Me.miTunesBackupEncrypt AndAlso Common.IOSStrVersionToIntVersion(Me.mDevice.ProductVersion) >= 933 Then
        '    autoOpen = False
        'End If

        Me.OpenBackupRestoreForm(BackupRestore.Backup, Nothing, autoOpen)
    End Sub

    Private Sub LinkLabel1_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs)
        If Folder.AppType = RunType.iDataRecovery OrElse Folder.AppType = RunType.iDataRecovery_Abroad Then
            Common.OpenExplorer("http://rollback.tongbu.com/guide")
        Else
            Common.OpenExplorer(WebUrl.iTunesBackupCourse)
        End If

    End Sub

    Private Sub tsmiOpenFolder_Click(sender As Object, e As EventArgs) Handles tsmiOpenFolder.Click
        If Me.mDgvBackupList.Rows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing Then
            Dim path As String = CType(Me.mDgvBackupList.SelectedRows(0).Tag, BackupInfo).FolderPath & ""
            If Directory.Exists(path) Then
                Try
                    Common.OpenExplorer(path)
                Catch ex As Exception
                End Try
            End If
        End If
    End Sub

    Private Sub tsmiOpen_Click(sender As Object, e As EventArgs) Handles tsmiOpen.Click
        If Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing Then
            Me.ShowDetail(Me.mDgvBackupList.SelectedRows(0).Tag)
        End If
    End Sub

    Private Sub tsmiDelete_Click(sender As Object, e As EventArgs) Handles tsmiDelete.Click
        If Me.mDgvBackupList.SelectedRows.Count > 0 AndAlso Me.mDgvBackupList.SelectedRows(0).Tag IsNot Nothing Then
            Me.DeleteBackup(Me.mDgvBackupList.SelectedRows(0))
        End If
    End Sub

    Private Sub llblDeletePwd_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles llblDeletePwd.LinkClicked
        If BackupListDeleteHelper.GetInstance().DeleteiTunesBackupPassword(Me.Language, Me.mDevice) Then
            SetBackupPwd(False)
        End If
    End Sub

    Private Sub btn_Setting_Click(sender As Object, e As EventArgs) Handles btn_Setting.Click
        'Me.menuMain.Show(Me.btn_Setting, New Point(0, Me.btn_Setting.Height))
        Dim frm As New frmWeChatSetting()
        frm.ShowDialog(Me)
    End Sub

    Private Sub tsmSetting_Click(sender As Object, e As EventArgs) Handles tsmSetting.Click
        Dim frm As New frmWeChatSetting()
        frm.ShowDialog(Me)
    End Sub

    Private Sub btnQQ_Click(sender As Object, e As EventArgs) Handles btnQQ.Click
        If Folder.AppType = RunType.iWeChatMigration Then
            Common.JoinQQGroup(QQGroupNumber.Q172251247)
            Return

        ElseIf Me.mChargeHelper.ProjectType = ChargeProjectType.Assistant_Wechat_iOS Then
            'Common.JoinQQGroup(QQGroupNumber.Q659942151)
            Common.OpenExplorer(ServerIniSetting.GetiTongWeChatAssistantQQGroup())
            Return
        End If

#If IS_WECHAT Then
        Common.JoinQQ(QQNumber.Q2411775764)
#Else
        Common.JoinQQGroup(QQGroupNumber.Q474680156)
#End If

    End Sub

    Private Sub TbLabel1_Click(sender As Object, e As EventArgs) Handles TbLabel1.Click
        Common.JoinQQGroup(QQGroupNumber.Q587033524)
    End Sub

    Private Sub btnWeChatDataMigration_Click(sender As Object, e As EventArgs) Handles btnWeChatDataMigration.Click
        Try
            If Me.mDevice Is Nothing Then
                tbMessageBox.Show(Me, "尚未连接设备，请先连接设备。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
                Return
            End If

            Dim bInfo As BackupInfo = Nothing
            If Me.mDgvBackupList.SelectedRows.Count > 0 Then
                For Each item As DataGridViewRow In Me.mDgvBackupList.SelectedRows
                    If item Is Nothing OrElse item.Tag Is Nothing OrElse Not TypeOf item.Tag Is BackupInfo Then
                        Continue For
                    End If
                    bInfo = item.Tag
                    Exit For
                Next
            End If
            If bInfo Is Nothing Then
                tbMessageBox.Show(Me, "请选择要迁移的备份数据。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
                Return
            End If

Do_RetryCheckiCloud:
            'icloud 提示
            Dim frmCC As frmCheckiCloud = Nothing
            If Me.mDeviceMode = DeviceMode.NomalMode Then
                '判断 icloud 查找我的iphone是否打开
                Dim objValue As Object = Me.mDevice.GetDeviceValue("com.apple.fmip", "IsAssociated")
                Dim blnValue As Boolean = CType(objValue, Boolean)
                If blnValue Then
                    frmCC = New frmCheckiCloud(iCloudType.FindMyIphoneClose)
                End If
            End If

            If frmCC IsNot Nothing Then
                If frmCC.ShowDialog <> Windows.Forms.DialogResult.OK Then
                    Return
                End If
                GoTo Do_RetryCheckiCloud
            End If

            If tbInputBox.Show(Me, "请根据教程双击Home退出所有的APP。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, "", False, My.Resources.backup_exitapp, "", "", "", False) <> Windows.Forms.DialogResult.OK Then
                Return
            End If

            'If tbMessageBox.Show(Me, String.Format("请先确认：{0}1、设备 设置--iCloud--查找我的iPhone 已经关闭；{0}2、设备 双击Home键退出所有的APP。", vbCrLf), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel) = Windows.Forms.DialogResult.Cancel Then
            '    Return
            'End If

            Me.btnWeChatDataMigration.Enabled = False

            Dim isFullBackup As Boolean = True
            If BackupHelper.LstQuickBackupList.Count > 0 AndAlso BackupHelper.LstQuickBackupList.Contains(bInfo) Then
                isFullBackup = False
            End If
            Common.LogException(bInfo.FolderPath, "****WeChatSpare****")
            Me.mBackupHelper.DataMigration("", "", bInfo.FolderPath, isFullBackup)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnChangeSKU_Click")
        End Try
    End Sub

    Private Sub btnBuy_Click(sender As Object, e As EventArgs) Handles btnBuy.Click
        Me.mChargeHelper.CheckIsChargeApp(Me, FunctionKey.None, 0, "", True, Me.mWCMCinfo, True)
    End Sub

    Private Sub tsmiAllbackup_Click(sender As Object, e As EventArgs) Handles tsmiAllbackup.Click
        ImportBackup()
    End Sub

    Private Sub tsmiAloneBackup_Click(sender As Object, e As EventArgs) Handles tsmiAloneBackup.Click
        If Me.mTDImportAloneBackup IsNot Nothing AndAlso Me.mTDImportAloneBackup.ThreadState <> ThreadState.Stopped Then
            Return
        End If
        Dim folderDialog As New FolderBrowserDialog()
        Dim folderPath As String = ""
        If folderDialog.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
            folderPath = folderDialog.SelectedPath
        End If

        Me.mTDImportAloneBackup = New Thread(AddressOf DoImportAloneBackup)
        Me.mTDImportAloneBackup.IsBackground = True
        Me.mTDImportAloneBackup.Start(folderPath)
    End Sub

    Private Sub DoImportAloneBackup(ByVal obj As Object)
        Try
            SetImportPlan("导入中...", False)
            Dim lstPath As New List(Of String)
            If BackupHelper.CheckPathIsBackupPath(obj.ToString()) Then

                Me.LoadAloneBackInfo(obj.ToString())
                lstPath.Add(obj.ToString())
            Else
                '如果当前目录下面有备份的目录也导入进来
                Dim arrDir As String() = Directory.GetDirectories(obj.ToString())
                If arrDir.Length > 0 Then
                    For Each folder As String In arrDir
                        If BackupHelper.CheckPathIsBackupPath(folder) Then
                            Me.LoadAloneBackInfo(folder)
                            lstPath.Add(folder)
                        End If
                        Application.DoEvents()
                    Next
                End If
            End If

            If lstPath.Count <= 0 Then
                tbMessageBox.Show(Me, "您导入的文件夹不可用。文件缺失不是一个完整的备份。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoImportAloneBackup")
        End Try
        Try
            Me.LoadSize()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoImportAloneBackup_1")
        End Try
        SetImportPlan(Me.Language.GetString("File.Button.UploadToDevice"), True)
    End Sub

    Private Delegate Sub SetImportPlanHandler(ByVal strPlan As String, ByVal isEnabled As Boolean)
    Private Sub SetImportPlan(ByVal strPlan As String, ByVal isEnabled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetImportPlanHandler(AddressOf SetImportPlan), strPlan, isEnabled)
        Else
            Try
                Me.btnImport.Enabled = isEnabled
                Me.btnImport.Text = strPlan
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetImportPlan")
            End Try
        End If
    End Sub

    Private Sub btnImport_Click(sender As Object, e As EventArgs) Handles btnImport.Click

        If Me.mIsImportAloneBackup Then
            Me.cmsImport.Show(Me.btnImport, 0, Me.btnImport.Height + 3)
        Else
            ImportBackup()
        End If

    End Sub

    Private Sub ImportBackup()
        If Me.mTDImportBackup IsNot Nothing AndAlso Me.mTDImportBackup.ThreadState <> ThreadState.Stopped Then
            Return
        End If
        Dim folderDialog As New FolderBrowserDialog()
        Dim folderPath As String = ""
        If folderDialog.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
            folderPath = folderDialog.SelectedPath
        End If

        Me.mTDImportBackup = New Thread(AddressOf DoImportBackup)
        Me.mTDImportBackup.IsBackground = True
        Me.mTDImportBackup.Start(folderPath)
    End Sub

    Private Sub DoImportBackup(ByVal obj As Object)
        Try
            SetImportPlan("导入中...", False)
            Dim lstPath As New List(Of String)
            If BackupHelper.CheckPathIsBackupPath(obj.ToString()) Then
                Me.LoadBackInfo(obj.ToString())
                lstPath.Add(obj.ToString())
            Else
                '如果当前目录下面有备份的目录也导入进来
                Dim arrDir As String() = Directory.GetDirectories(obj.ToString())
                If arrDir.Length > 0 Then
                    For Each folder As String In arrDir
                        If BackupHelper.CheckPathIsBackupPath(folder) Then
                            Me.LoadBackInfo(folder)
                            lstPath.Add(folder)
                        End If
                        Application.DoEvents()
                    Next
                End If
            End If

            If lstPath.Count <= 0 Then
                '"您导入的文件夹不可用。可以尝试在""查找备份失败记录""中查看或联系客服QQ:{0}"
                Dim strMsg As String = String.Format(Me.Language.GetString("Backup.Message.LoadFolderError"), Utility.TongbuContact)
                tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
            End If
            '用户添加的目录记录到xml文件里面。
            XmlHelper.InsertXMForBackupPath(lstPath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoImportBackup")
        End Try
        Try
            Me.LoadSize()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoImportBackup_1")
        End Try
        SetImportPlan(Me.Language.GetString("File.Button.UploadToDevice"), True)
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        Me.btnRefresh.Enabled = False
        Me.mBackupHelper.GetFullBackupList(True)
    End Sub

    Private Sub btnDeleteTemp_Click(sender As Object, e As EventArgs) Handles btnDeleteTemp.Click
        If Me.mTDDeleteTemp IsNot Nothing AndAlso Me.mTDDeleteTemp.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTDDeleteTemp = New Thread(AddressOf DoDeleteTemp)
        Me.mTDDeleteTemp.IsBackground = True
        Me.mTDDeleteTemp.Start()
    End Sub

    Private Sub lbliCloudWechat_TextClick(sender As Object, e As EventArgs) Handles lbliCloudWechat.TextClick
        Common.OpenExplorer("http://news.tongbu.com/95256.html")
    End Sub

    Private Sub lbliCloudAcct_SizeChanged(sender As Object, e As EventArgs) Handles lbliCloudAcct.SizeChanged
        lbliCloudAcct.Location = New Point(btniCloudLogout.Location.X - lbliCloudAcct.Size.Width, lbliCloudAcct.Location.Y)
    End Sub

#End Region

#Region "--- 备份获取最新 ---"

    Public Sub OpenBackupRestoreForm(ByVal type As BackupRestore, ByVal info As BackupInfo, Optional isAutoClose As Boolean = True)
        Try

            Dim strName As String = "frmBacbup"
            If type = BackupRestore.Restore Then
                strName = "frmRestore"
            End If

            Dim frm As frmBackupCreate = Nothing
            For Each ctl As Form In Application.OpenForms
                If TypeOf ctl Is Form AndAlso ctl.GetType().FullName = GetType(frmBackupCreate).ToString Then
                    If iPhone IsNot Nothing And TypeOf ctl Is frmDeviceBase AndAlso (Not TypeOf ctl Is frmAndroidBase) AndAlso CType(ctl, frmDeviceBase).iPhone IsNot iPhone Then
                        Continue For
                    End If
                    frm = ctl
                    Exit For
                End If
            Next

            If frm Is Nothing Then
                frm = New frmBackupCreate(Me.mApplication, Me.mDevice, type, Nothing)
                frm.Name = strName
            Else
                frm.Activate()
            End If
            frm.mAutoClose = isAutoClose

            Dim isFullbackup As Boolean = True
            If BackupHelper.LstQuickBackupList.Count > 0 AndAlso BackupHelper.LstQuickBackupList.Contains(info) Then
                isFullbackup = False
            End If

            If info IsNot Nothing AndAlso type = BackupRestore.Restore Then
                Dim strPwdForDecrypt As String = ""
                If Not Me.CheckIsEncrypted(info, False, True, strPwdForDecrypt) Then
                    Return
                End If
                Dim strUnbackDir As String = ""
                If info.IsEncrypted AndAlso Directory.Exists(Path.Combine(info.FolderPath, iPhoneDevice.Unback_Dir)) Then
                    strUnbackDir = Path.Combine(info.FolderPath, iPhoneDevice.Unback_Dir)
                End If

                If Me.mABType = AloneBackupType.WeChatSpare Then
                    Dim frmCWCSA As frmChoiceWeChatSpareApp = New frmChoiceWeChatSpareApp(Me.mApplication, Me.mDevice, False, info.FolderPath)
                    If frmCWCSA.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
                        Me.mStrSku = ServerIniSetting.GetWeChatSpareSKU()
                    Else
                        Return
                    End If
                ElseIf Me.mABType = AloneBackupType.WeChatPayMigration Then
                    Me.mStrSku = Me.mWCMCinfo.SrcSKU
                End If

                CType(frm, frmBackupCreate).StartRestore(info.FolderPath, Me, isFullbackup, Me.mStrSku, strUnbackDir, strPwdForDecrypt)
            Else
                frm.ShowDialog(Me)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OpenBackupRestoreForm")
        End Try
    End Sub

#End Region

#Region "--- 添加行 ---"

    Private Delegate Sub AddRowHandler(ByVal intIndex As Integer, ByVal bakObject As BackupInfo, ByVal isExistQuick As Boolean, ByVal FolderType As BackupFolderType)
    Public Sub AddRow(ByVal intIndex As Integer, ByVal bakObject As BackupInfo, ByVal isExistQuick As Boolean, ByVal FolderType As BackupFolderType)
        If Me.InvokeRequired Then
            Me.Invoke(New AddRowHandler(AddressOf AddRow), intIndex, bakObject, isExistQuick, FolderType)
        Else
            Try
                Dim strSize As String = Me.Language.GetString("App.Cell.Loading") '"正在加载..." 'Utility.FormatFileSize(bakObject.Size)
                Dim strDeviceName As String = bakObject.DeviceName
                If FolderType = BackupFolderType.Failure Then
                    strSize = Me.Language.GetString("Common.Label.Unknow")
                    strDeviceName = Path.GetFileName(bakObject.FolderPath)
                End If
                Dim row As tbDataGridViewRow = Nothing

                Dim aryParameters As Object() = New Object() {strDeviceName, _
                                                                   True, _
                                                                   bakObject.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute)), _
                                                                   strSize, _
                                                                   bakObject.ProductVersion, _
                                                                   bakObject.ProductType, _
                                                                   "", "", ""}

                row = Me.SetDataGridViewGroup(FolderType, aryParameters, intIndex)

                row.Tag = bakObject
                Dim cellProgressEx As tbDataGridViewProgressCellEx = row.Cells("colProgress")

                cellProgressEx.tbProgressStyle = ProgressStyle.Button
                cellProgressEx.tbButtonSize = New Size(60, 24)
                cellProgressEx.tbShowButtonThird = False

                cellProgressEx.tbShowButtonFirst = True
                cellProgressEx.tbTextBackup = Me.Language.GetString("Photo.Menu.View")                  '"查看"
                cellProgressEx.tbShowButtonSecond = True
                cellProgressEx.tbTextUnInstall = Me.Language.GetString("Weixin.Button.SeparateReduction") ' "单独还原"

                If Not frmBackupCreate.CheckCanRestore(Me.mDevice, row.Tag) Then
                    cellProgressEx.tbCellButtonEnable = cellProgressEx.tbCellButtonEnable And Not CellButtonStyle.Uninstall
                Else
                    cellProgressEx.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall
                End If

                If FolderType = BackupFolderType.Failure Then
                    cellProgressEx.tbCellButtonEnable = cellProgressEx.tbCellButtonEnable And Not CellButtonStyle.Uninstall
                End If

                Dim isShowDelete As Boolean = True
                'If FolderType = BackupFolderType.Full Then
                '    isShowDelete = False
                'End If

                cellProgressEx.tbShowButtonImport = isShowDelete
                cellProgressEx.tbTextImport = Me.Language.GetString("Common.Delete")                    '"删除"

                Dim celStatus As tbDataGridViewMediaCell = row.Cells("colStatus")
                If mbdb.CheckIsEncrypted(bakObject.FolderPath) Then
                    celStatus.tbCellStatus = CellButtonStyle.BackupLock
                    celStatus.ToolTipText = Me.Language.GetString("Backup.Label.EncryptionBackup")          '"加密备份"
                Else
                    celStatus.tbCellStatus = CellButtonStyle.BackupUnlock
                End If
            Catch ex As Exception
                Common.LogException("Add List Error" & ex.ToString, "AddRow")
            End Try
        End If
    End Sub

    Private Function SetDataGridViewGroup(ByVal BackupFolder As BackupFolderType, ByVal aryParameters As Object(), ByVal intIndex As Integer) As tbDataGridViewRow
        Dim row As New tbDataGridViewRow

        Dim strGroupText As String = ""
        Select Case BackupFolder
            Case BackupFolderType.Full
                strGroupText = Me.Language.GetString("Weixin.Button.FullBackup") '"整机备份"
            Case BackupFolderType.Quick
                strGroupText = Me.Language.GetString("Weixin.Button.SeparateBackup") '"单独备份"
            Case BackupFolderType.Failure
                strGroupText = Me.Language.GetString("Weixin.Button.FailureBackup") '"损坏备份"
        End Select

        row = Me.mDgvBackupList.AddGroupItem(strGroupText, aryParameters, False, "", intIndex)
        'row = Me.mDgvBackupList.AddGroupItem(strGroupText, aryParameters, "")
        row.Group.ShowIconMore = True

        Return row
    End Function

    Public Function UpdateRowFileSize(ByVal strFilePath As String) As Long
        Dim size As ULong = 0
        If strFilePath.Length <= 0 Then
            Return size
        End If

        For Each Item As tbDataGridViewRow In Me.mDgvBackupList.Rows
            If Item.Tag IsNot Nothing AndAlso TypeOf Item.Tag Is BackupInfo Then
                Dim obj As BackupInfo = Item.Tag
                If String.Compare(obj.FolderPath, strFilePath, True) = 0 Then
                    size = BackupHelper.GetFileSize(strFilePath, True)
                    obj.Size = size
                    Dim bInfo As New BackupInfo(strFilePath)

                    Item.Cells("colDateTime").Value = bInfo.BackupDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinute))
                    Item.Cells("colSize").Value = Utility.FormatFileSize(obj.Size)

                    Dim celStatus As tbDataGridViewMediaCell = Item.Cells("colStatus")
                    If mbdb.CheckIsEncrypted(bInfo.FolderPath) Then
                        celStatus.tbCellStatus = CellButtonStyle.BackupLock
                        celStatus.ToolTipText = Me.Language.GetString("Backup.Label.EncryptionBackup")          '"加密备份"
                    Else
                        celStatus.tbCellStatus = CellButtonStyle.BackupUnlock
                    End If
                    Exit For
                End If
            End If
        Next

        Return size
    End Function

    Private Delegate Sub SetRowCanRestoreHandler()
    Private Sub SetRowCanRestore()
        If Me.InvokeRequired Then
            Me.Invoke(New SetRowCanRestoreHandler(AddressOf SetRowCanRestore))
        Else
            For Each Item As tbDataGridViewRow In Me.mDgvBackupList.Rows
                Dim cellProgressEx As tbDataGridViewProgressCellEx = Item.Cells("colProgress")
                If Not frmBackupCreate.CheckCanRestore(Me.mDevice, Item.Tag) Then
                    cellProgressEx.tbCellButtonEnable = cellProgressEx.tbCellButtonEnable And Not CellButtonStyle.Uninstall
                Else
                    cellProgressEx.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall
                End If
            Next
        End If
    End Sub

#End Region

#Region "--- 加载设备图片 ---"

    Private Sub GetDeviceImageFromLocal()
        Try
            If Me.mDevice Is Nothing Then
                Return
            End If

            If Me.mTDGetDeviceImageFromLocal IsNot Nothing AndAlso Me.mTDGetDeviceImageFromLocal.ThreadState <> ThreadState.Stopped Then
                Return
            End If
            Me.mTDGetDeviceImageFromLocal = New Thread(AddressOf DoGetDeviceImageFromLocal)
            Me.mTDGetDeviceImageFromLocal.IsBackground = True
            Me.mTDGetDeviceImageFromLocal.Start()
            Utility.WaitSeconds(0.5)
        Catch ex As Exception
            Common.LogException(ex.ToString, "GetDeviceImageFromLocal")
        End Try
    End Sub

    Private Sub DoGetDeviceImageFromLocal()
        Try
            Dim strFilePath As String = ""
#If IS_WECHAT Then
            strFilePath = GetDeviceImage(Me.mDevice.ProductType, Me.mDevice.DeviceColor, Me.mDevice.DeviceColorBg)
#Else
            strFilePath = SummaryInfo.GetIconPathByDevice(Me.mDevice.ProductType, Me.mDevice.DeviceColor, Me.mDevice.DeviceColorBg)
#End If
            If File.Exists(strFilePath) Then
                Me.pbDevice.Image = Common.ImageFromFile(strFilePath)
            Else
                Me.pbDevice.Image = My.Resources.device_default
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoGetDeviceImageFromLocal")
        End Try
        Try
            SetLoadDeviceInfo()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoGetDeviceImageFromLocal_SetLoadDeviceInfo")
        End Try

        Try
            Dim intTryCount = 0
            Dim isCheckEncrypt As Boolean = False
            If Me.mDevice IsNot Nothing Then
                isCheckEncrypt = Me.mDevice.mb2_CheckEncrypt()
                While Not isCheckEncrypt AndAlso intTryCount < 15
                    Utility.WaitSeconds(1)
                    isCheckEncrypt = Me.mDevice.mb2_CheckEncrypt()
                    intTryCount = intTryCount + 1
                End While
            End If
            SetBackupPwd(isCheckEncrypt)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoGetDeviceImageFromLocal_SetBackupPwd")
        End Try

    End Sub

    Private Function GetDeviceImage(strProduct As String, iPhoneColor As PHBDeviceColorVariation, strColor As String) As String
        Dim imgPath As String = SummaryInfo.GetIconPathByDevice(strProduct, iPhoneColor, strColor)
        Try
            If Not File.Exists(imgPath) AndAlso Common.NetworkIsAvailable() Then
                Folder.CheckFolder(Path.GetDirectoryName(imgPath))
                Common.DownloadImage(WebUrl.ResourceScreenShotDevice & Path.GetFileName(imgPath), 30000, imgPath)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetDeviceImage")
        End Try
        Return imgPath
    End Function

    Private Delegate Sub SetLoadDeviceInfoHandler()
    Public Sub SetLoadDeviceInfo()
        If Me.InvokeRequired Then
            Me.Invoke(New SetLoadDeviceInfoHandler(AddressOf SetLoadDeviceInfo))
        Else
            Try
                Me.mDevice.Connect()
                Me.mDevice.StartSession()

                If Me.mDevice Is Nothing Then
                    Return
                End If
                Me.lblNameValue.Text = Me.mDevice.DeviceName
                Me.lblDeviceType.Text = String.Format("{0}：  {1}({1})", Me.Language.GetString("Welcome.Label.ProductType"), Me.mDevice.ProductType, SummaryInfo.FormatProduct(Me.mDevice.ProductType))
                Me.lblDericeType.Text = String.Format("{0}：  {1}", Me.Language.GetString("Welcome.Label.ProductVersion"), Me.mDevice.ProductVersion)
                Me.lblFirmwareVersion.Text = String.Format("{0}：  {1}", Me.Language.GetString("Welcome.Label.DeviceColor"), frmSummary.FormatDeviceColor(Me.mDevice, Me.mDevice.DeviceColor, Me.Language))

                Me.mDevice.StopSession()
                Me.mDevice.Disconnect()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetLoadDeviceInfo")
            End Try
        End If
    End Sub

    Private Delegate Sub SetBackupPwdHandler(isCheckEncrypt As Boolean)
    Public Sub SetBackupPwd(isCheckEncrypt As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetBackupPwdHandler(AddressOf SetBackupPwd), isCheckEncrypt)
        Else
            Try
                Me.llblDeletePwd.Visible = isCheckEncrypt
                Me.miTunesBackupEncrypt = isCheckEncrypt

                Dim strEncrypt As String = Me.Language.GetString("Common.Backup.EncryptionPrompt") '"此设备已设置备份密码，备份的文件将会加密，在恢复到设备时需要输入密码，且加密的备份文件不能查看其内容。"
                Dim strUnEncrypt As String = Me.Language.GetString("Common.Backup.UnEncryptionPrompt") '"设备未设置备份密码，备份的文件不会被加密，在恢复到设备时无需输入密码，且备份的文件可以查看其内容。"

                Me.lblBackupPwdMsg.Text = IIf(isCheckEncrypt, strEncrypt, strUnEncrypt)
                Me.lblBackupPwdMsg.ForeColor = IIf(isCheckEncrypt, Color.FromArgb(255, 68, 68), Color.FromArgb(102, 102, 102))

            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetBackupPwd")
            End Try
        End If
    End Sub

#End Region

#Region "--- 展示微信 ---"

    Private Delegate Sub SetViewStyleHandler(ByVal style As WeixinMainViewStyle, ByVal isJump As Boolean, ByVal isFromBackup As Boolean)
    Public Sub SetViewStyle(ByVal style As WeixinMainViewStyle, Optional ByVal isJump As Boolean = False, Optional ByVal isFromBackup As Boolean = False)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewStyleHandler(AddressOf SetViewStyle), style, isJump, isFromBackup)
        Else
            Try
                Me.mCurrentWeixinMainViewStyle = style
                Me.tblayoutMain.SuspendLayout()
                Select Case style
                    Case WeixinMainViewStyle.LoadDevice
                        If Me.tblayoutMain.ColumnStyles(0).Width <> 100 Then
                            Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(0).Width = 100
                            Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(1).Width = 0
                            Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(2).Width = 0
                            Me.tblayoutMain.ColumnStyles(3).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(3).Width = 0
                            Me.tblayoutMain.ColumnStyles(4).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(4).Width = 0

#If IS_WECHAT Then
                            Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.DeviceAttainFD") '通过iTunes备份恢复
#Else
                            Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.BackupAttain") '微信单独备份还原
#End If

                            If Me.mDevice Is Nothing Then
                                Me.pnlDeviceUnConnect.Visible = True
                                Me.pnlDeviceConnect.Visible = False
                            Else
                                Me.pnlDeviceUnConnect.Visible = False
                                Me.pnlDeviceConnect.Visible = True
                            End If

                            Me.pnlDevice.Visible = True
                            Me.pnlHeadDevice.Visible = True

                            Me.pnlBackup.Visible = False
                            Me.pnlHeadBackup.Visible = False

                            Me.pnlDescribe.Visible = False

                            Me.pnlContent.Visible = False
                            IniSetting.SetWeChatDefaultShowBackup(0)

                            Me.pnliCloudHead.Visible = False
                            Me.pnliCloudDetail.Visible = False

                            Me.tblayoutMain.RowStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.RowStyles(2).Height = 0
                            Me.tblayoutMain.Size = New Size(Me.tblayoutMain.Width, Height - 60)
                        End If

                    Case WeixinMainViewStyle.LoadBackup
                        If Me.tblayoutMain.ColumnStyles(1).Width <> 100 Then
                            Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(1).Width = 100
                            Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(0).Width = 0
                            Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(2).Width = 0
                            Me.tblayoutMain.ColumnStyles(3).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(3).Width = 0
                            Me.tblayoutMain.ColumnStyles(4).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(4).Width = 0


                            If Me.mABType = AloneBackupType.WeChatSpare OrElse Me.mABType = AloneBackupType.WeChatVIPSpare Then
#If IS_WECHAT Then
                                Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.DeviceAttainFD") '通过iTunes备份恢复
#Else
                                Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.BackupAttain") '微信单独备份还原
#End If

                            ElseIf Me.mABType = AloneBackupType.WeChat Then

#If IS_WECHAT Then
                                Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.DeviceAttainFD") '通过iTunes备份恢复
#Else
                                Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.BackupAttainEx") '"微信单独备份还原(仅支持官方版本微信)"
#End If
                            End If

                            Me.pnlDevice.Visible = False
                            Me.pnlHeadDevice.Visible = False

                            Me.pnlBackup.Visible = True
                            Me.pnlHeadBackup.Visible = True

                            Me.pnlDescribe.Visible = False

                            Me.pnlContent.Visible = False
                            IniSetting.SetWeChatDefaultShowBackup(1)

                            Me.pnliCloudHead.Visible = False
                            Me.pnliCloudDetail.Visible = False

                            'If isJump Then
                            '    Me.LoadAloneBackUpList(Me.mAloneBackupsHelper.GetBackupInfosByABType(Me.mABType, False))
                            'End If

                            Me.tblayoutMain.RowStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.RowStyles(2).Height = 0

                            If Folder.AppType = RunType.iWeChatMigration Then
                                Me.btnBuy.Visible = False
                            End If
                            Me.tblayoutMain.Size = New Size(Me.tblayoutMain.Width, Height - 60)
                        End If

                    Case WeixinMainViewStyle.Content
                        If Me.tblayoutMain.ColumnStyles(2).Width <> 100 Then
                            Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(2).Width = 100
                            Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(0).Width = 0
                            Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(1).Width = 0
                            Me.tblayoutMain.ColumnStyles(3).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(3).Width = 0
                            Me.tblayoutMain.ColumnStyles(4).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(4).Width = 0

                            Me.pnlDevice.Visible = False
                            Me.pnlHeadDevice.Visible = False
                            Me.pnlBackup.Visible = False
                            Me.pnlHeadBackup.Visible = False
                            Me.pnlDescribe.Visible = False

                            Me.pnliCloudHead.Visible = False
                            Me.pnliCloudDetail.Visible = False

                            If Folder.AppType = RunType.iWeChatMigration Then
                                Me.btnBuy.Visible = True
                                SetBuyButtonStatus()
                            End If


                            Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.BackupAttain") '微信单独备份还原
                            Me.pnlContent.Visible = True

                            Me.tblayoutMain.RowStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.RowStyles(2).Height = 0
                            'Try
                            '    If Me.mCurrentSelectBackupInfo Is Nothing Then
                            '        Me.tblayoutMain.RowStyles(2).SizeType = SizeType.Percent
                            '        Me.tblayoutMain.RowStyles(2).Height = 0
                            '    Else
                            '        ' Dim ts As TimeSpan = Date.Now - Me.mCurrentSelectBackupInfo.LastBackupDate                 ts.TotalHours > 1 AndAlso

                            '        If isFromBackup AndAlso Me.mIsFromBackupShow Then
                            '            Me.tblayoutMain.RowStyles(2).SizeType = SizeType.Absolute
                            '            Me.tblayoutMain.RowStyles(2).Height = 24
                            '        Else
                            '            Me.tblayoutMain.RowStyles(2).SizeType = SizeType.Percent
                            '            Me.tblayoutMain.RowStyles(2).Height = 0
                            '        End If
                            '    End If
                            'Catch ex As Exception
                            '    Common.LogException(ex.ToString(), "SetViewStyle_1")
                            'End Try

                            Me.tblayoutMain.Size = New Size(Me.tblayoutMain.Width, Me.Height - 33)


                        End If

                    Case WeixinMainViewStyle.MigrationHome
                        If Me.tblayoutMain.ColumnStyles(3).Width <> 100 Then
                            Me.tblayoutMain.ColumnStyles(3).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(3).Width = 100
                            Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(0).Width = 0
                            Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(1).Width = 0
                            Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(2).Width = 0
                            Me.tblayoutMain.ColumnStyles(4).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(4).Width = 0

                            Me.pnlDevice.Visible = False
                            Me.pnlHeadDevice.Visible = False
                            Me.pnlBackup.Visible = False
                            Me.pnlHeadBackup.Visible = False
                            Me.pnlContent.Visible = False
                            Me.pnlDescribe.Visible = True
                            Me.pnliCloudHead.Visible = False
                            Me.pnliCloudDetail.Visible = False

                            If Folder.AppType = RunType.iWeChatMigration Then
                                Me.btnBuy.Visible = False
                            End If

                            Me.btnBackup.Text = Me.Language.GetString("Weixin.Button.BackupAttain") '微信单独备份还原
                            Me.tblayoutMain.Size = New Size(Me.tblayoutMain.Width, Me.Height - 60)
                        End If
                    Case WeixinMainViewStyle.iCloudBackup    '' iCloud备份恢复
                        With Me.tblayoutMain
                            If .ColumnStyles(4).Width <> 100 Then
                                .ColumnStyles(0).SizeType = SizeType.Percent
                                .ColumnStyles(0).Width = 0
                                .ColumnStyles(1).SizeType = SizeType.Percent
                                .ColumnStyles(1).Width = 0
                                .ColumnStyles(2).SizeType = SizeType.Percent
                                .ColumnStyles(2).Width = 0
                                .ColumnStyles(3).SizeType = SizeType.Percent
                                .ColumnStyles(3).Width = 0
                                .ColumnStyles(4).SizeType = SizeType.Percent
                                .ColumnStyles(4).Width = 100

                                .RowStyles(2).SizeType = SizeType.Percent
                                .RowStyles(2).Height = 0
                                .Size = New Size(.Width, Height - 60)
                            End If
                        End With

                        Me.pnlDevice.Visible = False
                        Me.pnlHeadDevice.Visible = False

                        Me.pnlBackup.Visible = False
                        Me.pnlHeadBackup.Visible = False

                        Me.pnlDescribe.Visible = False

                        Me.pnlContent.Visible = False

                        IniSetting.SetWeChatDefaultShowBackup(2)
                        Me.pnliCloudHead.Visible = True
                        Me.pnliCloudDetail.Visible = True
                        '暂不支持
                        Me.lblIncompatible.Text = Me.Language.GetString("WeChat.Label.Main.NotOpenedFunc")
                        Me.lblCompatibility.Text = String.Empty
                        Me.pnlDeveloping.Visible = True
                        Me.pnlDeveloping.BringToFront()
                        Me.pnlDeveloping.Dock = DockStyle.Fill
                        Exit Select
                        '#If IS_iCloud Then
                        If OSHelper.OSType = OSType.XP_SP2 OrElse OSHelper.OSType = OSType.XP OrElse OSHelper.OSType = OSType.WinXP64 Then
                            Me.pnlDeveloping.Visible = True
                            Me.pnlDeveloping.BringToFront()
                            Me.pnlDeveloping.Dock = DockStyle.Fill
                        Else
                            Me.pnlDeveloping.Visible = False
                            Me.ShowiCloudPanel()
                        End If
                        '#End If

                End Select
                Me.tblayoutMain.ResumeLayout()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetViewStyle")
            End Try
            Application.DoEvents()
        End If
    End Sub

    Public Sub ShowDetail(ByVal backupObj As BackupInfo, Optional ByVal isAgainDecrypt As Boolean = False)

        If Not Me.CheckIsAdd(backupObj) Then
            Return
        End If

        Dim strEncryptedBackup As String = ""
        Try
            If backupObj.Identifier.Length = 0 Then
                Common.OpenExplorer(backupObj.FolderPath)
                Return
            End If

            If Not Me.CheckIsEncrypted(backupObj, False, isAgainDecrypt) Then
                Return
            End If
            If backupObj.IsEncrypted AndAlso Directory.Exists(Path.Combine(backupObj.FolderPath, iPhoneDevice.Unback_Dir)) Then
                backupObj = New BackupInfo(Path.Combine(backupObj.FolderPath, iPhoneDevice.Unback_Dir))
                strEncryptedBackup = backupObj.FolderPath
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowDetail")
        End Try
DO_EXIT:
        Me.mCurrentSelectBackupInfo = backupObj
        Me.LoadWeChat(True, False, strEncryptedBackup)

    End Sub


    Public Function CheckIsEncrypted(ByVal backupObj As BackupInfo, Optional ByRef isCancel As Boolean = False, Optional ByVal isAgainDecrypt As Boolean = False, Optional ByRef strPwdForDecrypt As String = "") As Boolean
        Dim isRelust As Boolean = True
        Dim deviceForDecrypt As iPhoneDevice = Nothing


        Try
            '1、判断是否需要解密
            If Not mbdb.CheckIsEncrypted(backupObj.FolderPath) Then
                GoTo DO_EXIT
            End If

            ' 2、"该备份被加密了，需要连接设备并输入正确密码后才能解密。请插入设备！"
            'Dim mgrDevice As MobileDeviceManager = MobileDeviceManager.Instance()
            'If mgrDevice.ConnectedDevices.Count = 0 Then
            '    tbMessageBox.Show(Me, Me.Language.GetString("Backup.Message.IsEncryptFile"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            '    isRelust = False
            '    GoTo DO_EXIT
            'End If

            '3、"无法解密。当前接入的设备固件版本号，低于备份时的版本号{0}。"
            '2017-02-07把这个判断去掉，现在的解密方式不需要，先去掉这个判断逻辑测试看看解密后读取会不会有问题
            'deviceForDecrypt = BackupInfo.GetHighProductVersionDevice(backupObj.ProductVersion)
            'If deviceForDecrypt Is Nothing Then
            '    tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Backup.Message.LowProductVersion"), backupObj.ProductVersion), _
            '                                     Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            '    isRelust = False
            '    GoTo DO_EXIT
            'End If

            '4、是否已成功解密
            If iPhoneDevice.mb2_Check_Decrypted(backupObj.FolderPath, isAgainDecrypt) Then
                GoTo DO_EXIT
            End If

            '"您曾经在iTunes中备份加密，请输入设置的备份密码。"
DO_CONTINUE:
            Dim strTutorial As String = ""
            Dim strTutorialUrl As String = ""
            If ServerIniSetting.GetShowWechatBackupTool Then
                strTutorial = Me.Language.GetString("WechatAssistant.Message.ReadInAnotherWay")  '"忘记密码？换一个方式读取。"
                strTutorialUrl = ServerIniSetting.GetUrlWechatBackupTooliOSBackupFailed
            End If

            If tbInputBox.Show(Me, Me.Language.GetString("Backup.Message.TypeInBackupPwd"), _
                                        Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.None, _
                                        MessageBoxDefaultButton.Button1, strPwdForDecrypt, True, My.Resources.backup_password, strTutorial, strTutorialUrl) = Windows.Forms.DialogResult.OK Then ' , Me.Language.GetString("Backup.Message.iTunesTutorial"), "http://news.tongbu.com/92502.html""iTunes备份密码相关教程"’
                '密码不能为空
                If String.IsNullOrEmpty(strPwdForDecrypt) Then
                    GoTo DO_CONTINUE
                End If
                If Not Me.mBackupHelper.LoadRecords(backupObj.FolderPath, backupObj.Identifier, strPwdForDecrypt) Then
                    isRelust = False
                    If Not Me.mDoDecryptCancel Then
                        GoTo DO_CONTINUE
                    End If
                Else
                    isRelust = True
                End If
                If Me.mDoDecryptCancel Then
                    isRelust = False
                End If
            Else
                isRelust = False
                isCancel = True
                GoTo DO_EXIT
            End If
DO_EXIT:
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeChatMain_CheckIsEncrypted")
        End Try
        Me.lblState.Text = ""
        Me.lblState.Visible = False
        Return isRelust
    End Function
    Private Sub LoadWeChat(ByVal isBackup As Boolean, ByVal isNewBackup As Boolean, Optional ByVal strEncryptedBackup As String = "")
        Try
            Dim strIdentify As String = ""
            If isBackup Then
                Me.mChargeHelper.SetDeviceRegistedLocal(Me.mCurrentSelectBackupInfo.Identifier, Me.mCurrentSelectBackupInfo.SerialNumber, Me.mCurrentSelectBackupInfo.ProductType, Me.mCurrentSelectBackupInfo.DeviceName)
                strIdentify = Me.mCurrentSelectBackupInfo.Identifier

            Else
                Me.mChargeHelper.SetDeviceRegistedLocal(Me.mDevice)

                If Me.mDevice IsNot Nothing Then
                    strIdentify = Me.mDevice.Identifier
                End If
            End If

            Me.mChargeHelper.PrintRegisterLog(String.Format("DeviceRegistedLocal:id1:{0}", strIdentify))

            If isBackup Then
                frmWX = New frmWeixin(Me.mApplication, Nothing, Me.mCurrentSelectBackupInfo, IMAppType.WeChat, Me.mStrSku, Me, Me.mABType)
                SetLoadFromBackup()
            Else
#If Not IS_RECOVERY Then
                frmWX = New frmWeixin(Me.mApplication, Me.mDevice, Nothing, IMAppType.WeChat, Me.mStrSku, Me, Me.mABType, isNewBackup)
#End If
            End If

            frmWX.IsFromWeixinMain = True

            Utility.AddForm2Panel(frmWX, Me.pnlContent, True)
            Me.SetViewStyle(WeixinMainViewStyle.Content, False, isBackup)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadWeChat")
        End Try
    End Sub

    Private Delegate Sub SetLoadFromBackupHandler()
    Public Sub SetLoadFromBackup()
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetLoadFromBackupHandler(AddressOf SetLoadFromBackup))
            Else
                Me.lblFromBackupMsg.Text = String.Format(Me.Language.GetString("Common.Backup.DataExplain"), Me.mCurrentSelectBackupInfo.DisplayName, Me.mCurrentSelectBackupInfo.BackupDate) '当前数据不是最新，是读取【{0}-{1}】的备份文件

                Dim iFBLX As Integer = (Me.Width - Me.lblFromBackupMsg.Width - Me.llblRefresh.Width) / 2
                Me.lblFromBackupMsg.Location = New Point(iFBLX, Me.lblFromBackupMsg.Location.Y)
                Me.llblRefresh.Location = New Point(Me.lblFromBackupMsg.Left + Me.lblFromBackupMsg.Size.Width, Me.lblFromBackupMsg.Location.Y)

                If Me.mDevice Is Nothing Then
                    Me.llblRefresh.Visible = False
                Else
                    Me.llblRefresh.Visible = True
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "SetLoadFromBackup")
        End Try
    End Sub

    Public Sub llblRefresh_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles llblRefresh.LinkClicked
        If Me.mDevice Is Nothing Then
            tbMessageBox.Show(Me, Me.Language.GetString("Welcome.Label.AccessDevice"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
            Return
        End If

        If Me.mDevice.Identifier <> Me.mCurrentSelectBackupInfo.Identifier Then
            tbMessageBox.Show(Me, Me.Language.GetString("Common.Backup.DataExplainEx"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning) '"当前连接设备与备份文件的设备不一致，无法获取最新。"
            Return
        End If

        Me.mIsFromBackupShow = False
        SetViewStyle(WeixinMainViewStyle.LoadBackup)
        btnBackupStart_Click(Nothing, Nothing)
    End Sub

#End Region

#Region "--- 备份还原委托事件 ---"

    Public Sub OnBackupProgressChanged(ByVal sender As Object, ByVal args As BackupArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupArgs)(AddressOf OnBackupProgressChanged), sender, args)
        Else
            Try
                If Me.mCurrentRestoreRow Is Nothing Then
                    For Each item As tbDataGridViewRow In mDgvBackupList.Rows
                        Dim bakInfo As BackupInfo = item.Tag
                        If bakInfo Is Nothing Then
                            Continue For
                        End If
                        If bakInfo.FolderPath = args.FolderPath Then
                            mCurrentRestoreRow = item
                            Exit For
                        End If
                    Next
                End If
                If args.Status = BackupStatus.Succeed AndAlso args.BackupType = BackupRestore.Backup Then
                    Dim bakObject As BackupInfo = BackupHelper.CheckBackObjExist(args.FolderPath, 0)
                    If bakObject IsNot Nothing Then
                        If args.IsNewBackup Then
                            XmlHelper.InsertXMForBackupPath(args.FolderPath)
                            Dim intIndex As Integer = 0
                            If BackupHelper.LstQuickBackupList.Count > 0 Then
                                intIndex = 1
                            End If

                            Me.AddRow(intIndex, bakObject, BackupHelper.LstQuickBackupList.Count > 0, BackupFolderType.Full)
                        End If
                        '-----------备份后打开----------
                        If args.IsOpenWeChat Then
                            Me.ShowDetail(bakObject, True)
                            Me.BringToFront()
                        End If
                        bakObject.Size = Me.UpdateRowFileSize(args.FolderPath)
                    End If
                End If

                If Me.mCurrentRestoreRow Is Nothing OrElse Me.mCurrentRestoreRow.Index < 0 Then
                    Return
                End If

                Dim cellProgressEx As tbDataGridViewProgressCellEx = Me.mCurrentRestoreRow.Cells("colProgress")

                If args.Status = BackupStatus.BackupOrRestore OrElse args.Status = BackupStatus.ReadyRestore Then
                    cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                    If args.BackupType = BackupRestore.Restore Then
                        If args.IsConvert Then
                            cellProgressEx.tbText = Me.Language.GetString("Weixin.Message.RestoreData") '"正在准备还原数据"
                        Else
                            cellProgressEx.tbText = Me.Language.GetString("Backup.Message.RestoreMessage") '"正在还原...(请不要断开连接)"
                        End If
                    End If

                    cellProgressEx.tbShowCancelButton = True
                    If args.Progress = 100 Then
                        cellProgressEx.tbProgressValue = 99
                    Else
                        cellProgressEx.tbProgressValue = args.Progress
                        '  cellProgressEx.tbText = ""
                    End If

                ElseIf args.Status = BackupStatus.Succeed Then
                    cellProgressEx.tbProgressStyle = ProgressStyle.Text
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = IIf(args.BackupType = BackupRestore.Backup, Me.Language.GetString("App.Cell.BackupSucceed"), Me.Language.GetString("Backup.Message.RestoreSucceed"))
                    Me.mCurrentRestoreRow = Nothing

                    If args.BackupType = BackupRestore.Restore Then
                        If Folder.AppType = RunType.iDataRecovery OrElse Folder.AppType = RunType.iDataRecovery_Abroad Then
                            Common.OpenExplorer("http://rollback.tongbu.com/guide/1")
                        Else
                            Common.OpenExplorer("http://news.tongbu.com/93324.html")
                        End If

                    End If

                ElseIf args.Status = BackupStatus.None Then
                    cellProgressEx.tbProgressStyle = ProgressStyle.Text
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = ""
                    Me.SetRestoreBtnEnable()
                    Me.mCurrentRestoreRow = Nothing

                Else
                    cellProgressEx.tbProgressStyle = ProgressStyle.Failed
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = ""
                    cellProgressEx.tbTextFail = args.ErrorMsg
                    Me.mCurrentRestoreRow = Nothing

                End If

                If args.Cancel Then
                    cellProgressEx.tbProgressStyle = ProgressStyle.Button
                    cellProgressEx.tbButtonSize = New Size(60, 24)
                    cellProgressEx.tbShowButtonThird = False

                    cellProgressEx.tbShowButtonFirst = True
                    cellProgressEx.tbTextBackup = Me.Language.GetString("Photo.Menu.View")                  '"查看"
                    cellProgressEx.tbShowButtonSecond = True
                    cellProgressEx.tbTextUnInstall = Me.Language.GetString("Weixin.Button.SeparateReduction") '"单独还原"
                    cellProgressEx.tbShowButtonImport = CheckQuickBackup(args.FolderPath)
                    cellProgressEx.tbTextImport = Me.Language.GetString("Common.Delete")                    '"删除"

                    Me.mCurrentRestoreRow = Nothing
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "mBackupHelper_BackupHandler")
            End Try

        End If
    End Sub

    Private Sub OnLoadBackupList(sender As Object, args As EventArgs)
        LoadBackUpList()
    End Sub

    Private Function CheckQuickBackup(ByVal strPath As String) As Boolean
        Dim isRelust As Boolean = False
        For Each bInfo As BackupInfo In BackupHelper.LstQuickBackupList
            If bInfo.FolderPath = strPath Then
                isRelust = True
                Exit For
            End If
        Next
        Return isRelust
    End Function

    Private Sub SetRestoreBtnEnable()
        If Me.mCurrentRestoreRow Is Nothing Then
            Return
        End If
        Dim cellProgressEx As tbDataGridViewProgressCellEx = Me.mCurrentRestoreRow.Cells("colProgress")
        cellProgressEx.tbProgressStyle = ProgressStyle.Button
    End Sub

    Private Sub OnDecryptProgressChanged(sender As Object, args As BackupDecryptArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New EventHandler(Of BackupDecryptArgs)(AddressOf OnDecryptProgressChanged), sender, args)
        Else
            Try
                Application.DoEvents()
                Dim drow As tbDataGridViewRow = Nothing
                For Each item As tbDataGridViewRow In mDgvBackupList.Rows
                    Dim bakInfo As BackupInfo = item.Tag
                    If bakInfo Is Nothing Then
                        Continue For
                    End If
                    If bakInfo.FolderPath = args.strPath Then
                        drow = item
                        Exit For
                    End If
                Next
                If drow IsNot Nothing Then
                    Dim cellProgressEx As tbDataGridViewProgressCellEx = drow.Cells("colProgress")
                    If Not args.isFinish Then
                        cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                        cellProgressEx.tbText = args.strMsg
                        cellProgressEx.tbShowCancelButton = True
                        cellProgressEx.tbProgressValue = args.intProgress
                    Else
                        If Not frmBackupCreate.CheckCanRestore(Me.mDevice, drow.Tag) Then
                            cellProgressEx.tbCellButtonEnable = cellProgressEx.tbCellButtonEnable And Not CellButtonStyle.Uninstall
                        Else
                            cellProgressEx.tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall
                        End If
                        cellProgressEx.tbProgressStyle = ProgressStyle.Button
                        cellProgressEx.tbShowButtonThird = False
                        cellProgressEx.tbShowButtonFirst = True
                        cellProgressEx.tbTextBackup = Me.Language.GetString("Photo.Menu.View")                  '"查看"
                        cellProgressEx.tbShowButtonSecond = True
                        cellProgressEx.tbTextUnInstall = Me.Language.GetString("Weixin.Button.SeparateReduction") ' "单独还原"
                    End If
                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "mBackupHelper_BackupDecipherHandler")
            End Try

            If args.isDecryptedFailure Then
                Me.lblState.ForeColor = Color.Red
            Else
                Me.lblState.ForeColor = Color.FromArgb(82, 82, 82)
            End If
            args.strMsg = Me.Language.GetString("Backup.Message.Decrypteding")
            Me.lblState.Text = args.strMsg & String.Format("{0}%", args.intProgress)
            lblState.Visible = True
            If Me.frmWX IsNot Nothing Then
                Me.frmWX.SetWeiXinProgress(args.strMsg & String.Format("{0}", args.intProgress))
            End If
        End If
    End Sub


#End Region

#Region "--- 从设备扫描 开始 按钮的状态控制 ---"

    Private Delegate Sub SetStartLoadDeviceHandler(ByVal btnStartLoadDeviceEnabled As Boolean, ByVal lblStartLoadDeviceText As String)
    Public Sub SetStartLoadDevice(ByVal btnStartLoadDeviceEnabled As Boolean, Optional ByVal lblStartLoadDeviceText As String = "")
        If Me.InvokeRequired Then
            Me.Invoke(New SetStartLoadDeviceHandler(AddressOf SetStartLoadDevice), btnStartLoadDeviceEnabled, lblStartLoadDeviceText)
        Else
            Me.btnRefresh.Enabled = True

            Me.btnStartLoadDevice.Enabled = btnStartLoadDeviceEnabled
            Me.lblStartLoadDevice.Visible = Not btnStartLoadDeviceEnabled
            Me.lblStartLoadDevice.Text = lblStartLoadDeviceText
        End If
    End Sub

#End Region

#Region "--- 修复有问题的微信备份 ---"

    Private mthrRepair As Thread

    Private Sub btnRepair_Click(sender As Object, e As EventArgs) Handles btnRepair.Click
        Me.DoRepair()
    End Sub

    Private Sub DoRepair()
        Try
            Me.mthrRepair = New Thread(AddressOf DoRepairThread)
            With Me.mthrRepair
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWeixinMain_DoRepair")
        End Try
    End Sub

    Private Sub DoRepairThread()
        For Each item As tbDataGridViewRow In Me.mDgvBackupList.SelectedRows
            Try
                'If Not Me.CheckRowCanRepair(item) Then
                '    Continue For
                'End If
                Me.SetRepairMessage(item, String.Format(Me.Language.GetString("Tools.Message.Repairing"), 0))
                Dim info As BackupInfo = item.Tag
                '重新取得微信备份的数据库
                Me.RestoreDB(info)
                '修复状态文件Status.plist
                If Not Me.RestoreStatus(info) Then
                    Me.SetRepairMessage(item, Me.Language.GetString("Tools.Message.RepairFailure"))
                    Continue For
                End If

                '检察备份文件是不是有文件丢失。
                Dim lstFileRecord As List(Of MBFileRecord) = mbdb.Reload(info.FolderPath)
                Dim lstKey As List(Of String) = New List(Of String)()
                If lstFileRecord IsNot Nothing AndAlso lstFileRecord.Count > 0 Then
                    Dim intIndex As Integer = 0
                    For Each itemRecord As MBFileRecord In lstFileRecord
                        If Not File.Exists(itemRecord.PathOnPC) Then
                            itemRecord.LoadInfoFromPlist()
                        End If
                        If Not File.Exists(itemRecord.PathOnPC) AndAlso (itemRecord.Mode = 33188 OrElse itemRecord.Mode = 33216) Then
                            lstKey.Add(itemRecord.fileID)
                            Common.LogException(itemRecord.fileID, "备份文件修复")
                        End If
                        intIndex += 1
                        Me.SetRepairMessage(item, String.Format(Me.Language.GetString("Tools.Message.RepairingProgess"), intIndex * 100 \ lstFileRecord.Count))
                    Next
                End If
                If lstKey.Count <= 0 Then
                    Me.SetRepairMessage(item, Me.Language.GetString("Tools.Button.RepairSucceed"))
                    Continue For
                End If

                If Val(info.ProductVersion) >= 10 Then
                    mbdb.WriteDBWeixin(info.FolderPath, lstKey)
                Else
                    mbdb.Write(info.FolderPath, lstKey)
                End If

                Me.SetRepairMessage(item, Me.Language.GetString("Tools.Button.RepairSucceed"))
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmWeixinMain_DoRepairThread")
                Me.SetRepairMessage(item, Me.Language.GetString("Tools.Message.RepairFailure"))
            End Try
        Next
    End Sub

    Private Function RestoreStatus(ByVal info As BackupInfo) As Boolean
        Dim succeed As Boolean = True
        Try
            '修复状态文件Status.plist
            Dim strStatusPath As String = Path.Combine(info.FolderPath, "Status.plist")
            If File.Exists(strStatusPath) Then
                Dim dict As New Dictionary(Of Object, Object)
                dict = Device.CoreFoundation.ReadPlist_managed(strStatusPath)
                If dict.ContainsKey("SnapshotState") AndAlso dict("SnapshotState").ToString().ToLower() <> "finished" Then
                    File.Copy(strStatusPath, strStatusPath & "_back_repair_" & DateTime.Now.ToString("yyyyMMddHHmmss"))
                    Common.LogException(dict("SnapshotState").ToString().ToLower(), "备份文件修复")
                    dict("SnapshotState") = "finished"
                    Device.CoreFoundation.WritePlist(dict, strStatusPath)
                End If
            End If
        Catch ex As Exception
            succeed = False
            Common.LogException(ex.ToString(), "frmWeixinMain_RestoreStatus")
        End Try
        Return succeed
    End Function

    Private Function RestoreDB(ByVal info As BackupInfo) As Boolean
        '把微信记录的文件盖掉默认备份文件。
        Dim strWeixinDBBackupFile As String = Path.Combine(info.FolderPath, "Manifest.db_weixin")
        Dim strWeixinDBFile As String = Path.Combine(info.FolderPath, "Manifest.db")
        Dim strWeixinMBDBBackupFile As String = Path.Combine(info.FolderPath, "Manifest.mbdb_weixin")
        Dim strWeixinMBDBFile As String = Path.Combine(info.FolderPath, "Manifest.mbdb")

        If Not File.Exists(strWeixinDBBackupFile) AndAlso Not File.Exists(strWeixinMBDBBackupFile) Then
            '不是微信备份不做处理
            Return False
        End If

        If File.Exists(strWeixinDBBackupFile) Then
            Try
                Dim strBack As String = strWeixinDBFile & "_back_repair_" & DateTime.Now.ToString("yyyyMMddHHmmss")
                File.Copy(strWeixinDBFile, strBack)
                File.Delete(strWeixinDBFile)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmWeixinMain_DoRepairThread_backup")
            End Try
            File.Copy(strWeixinDBBackupFile, strWeixinDBFile)
        End If

        If File.Exists(strWeixinMBDBBackupFile) Then
            Try
                Dim strBack As String = strWeixinMBDBFile & "_back_repair_" & DateTime.Now.ToString("yyyyMMddHHmmss")
                File.Copy(strWeixinMBDBFile, strBack)
                File.Delete(strWeixinMBDBFile)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmWeixinMain_DoRepairThread_backup")
            End Try
            File.Copy(strWeixinMBDBBackupFile, strWeixinMBDBFile)
        End If
        Return True
    End Function

    Private Function CheckRowCanRepair(ByVal row As tbDataGridViewRow)
        Dim blnReturn As Boolean = True
        Try
            If row Is Nothing OrElse row.Tag Is Nothing OrElse Not TypeOf row.Tag Is BackupInfo Then
                blnReturn = False
            Else
                Dim info As BackupInfo = row.Tag
                Dim strWeixinDBBackupFile As String = Path.Combine(info.FolderPath, "Manifest.db_weixin")
                Dim strWeixinMBDBBackupFile As String = Path.Combine(info.FolderPath, "Manifest.mbdb_weixin")
                If Not File.Exists(strWeixinDBBackupFile) AndAlso Not File.Exists(strWeixinMBDBBackupFile) Then
                    blnReturn = False
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmWeixinMain_CheckRowCanRepair")
        End Try
        Return blnReturn
    End Function

    Private Delegate Sub SetRepairMessageHandler(ByVal row As tbDataGridViewRow, ByVal strMessage As String)
    Private Sub SetRepairMessage(ByVal row As tbDataGridViewRow, ByVal strMessage As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetRepairMessageHandler(AddressOf SetRepairMessage), row, strMessage)
        Else
            Dim cellProgressEx As tbDataGridViewProgressCellEx = row.Cells("colProgress")
            cellProgressEx.Enabled = True
            cellProgressEx.tbShowCancelButton = False
            cellProgressEx.tbProgressStyle = ProgressStyle.Text
            cellProgressEx.tbText = strMessage
            cellProgressEx.tbTextFail = strMessage
        End If
    End Sub

#End Region

#Region "--- 加载备份数据 ---"

    Private Sub LoadBackUpList()
        Try
            If Me.mTDLoadBackUpList IsNot Nothing AndAlso Me.mTDLoadBackUpList.ThreadState <> ThreadState.Stopped Then
                Return
            End If
            DgvClear()
            Me.mTDLoadBackUpList = New Thread(AddressOf DoLoadBackUpList)
            Me.mTDLoadBackUpList.IsBackground = True
            Me.mTDLoadBackUpList.Start()
            Utility.WaitSeconds(0.5)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadBackUpList")
        End Try
    End Sub

    Private Delegate Sub DgvClearHandler()
    Private Sub DgvClear()
        If Me.InvokeRequired Then
            Me.Invoke(New DgvClearHandler(AddressOf DgvClear))
        Else
            Try
                Me.mDgvBackupList.DictGroupKeys.Clear()
                Me.mDgvBackupList.Rows.Clear()
                Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.FailureBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"损坏备份"
                Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.SeparateBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"单独备份"
                Me.mDgvBackupList.AddGroup(Me.Language.GetString("Weixin.Button.FullBackup"), Me.mDgvBackupList.GroupRowTemplateArray, "") '"整机备份"
            Catch ex As Exception
                Common.LogException(ex.ToString(), "MainForm_SetBuyButtonStatus")
            End Try
        End If
    End Sub


    Private Sub DoLoadBackUpList()
        Me.SetStartLoadDevice(False, Me.Language.GetString("Common.Backup.DetectionAloneBackup"))
        Try
            'If Not BackupHelper.IsLoadFullBackupList Then
            '    Dim frm As frmWait = New frmWait(Me.mApplication, Me.Language.GetString("WeiBo.Message.Loading"), WaitState.FullBackupList) '正在获取信息...
            '    frm.ShowDialog(Me)
            'End If
            Dim intIndex As Integer = 0

            Dim lstQuickBackup As List(Of BackupInfo) = Me.GetQuickBackupLst()

            For Each bInfo As BackupInfo In BackupHelper.LstFullBackupList
                intIndex += 1
                Me.AddRow(intIndex, bInfo, lstQuickBackup.Count > 0, BackupFolderType.Full)
            Next

            If lstQuickBackup.Count > 0 Then
                intIndex += 1
            End If
            For Each bInfo As BackupInfo In lstQuickBackup
                intIndex += 1
                Me.AddRow(intIndex, bInfo, lstQuickBackup.Count > 0, BackupFolderType.Quick)
            Next

            If BackupHelper.LstFailureBackupList.Count > 0 Then
                intIndex += 1
            End If
            For Each bInfo As BackupInfo In BackupHelper.LstFailureBackupList
                intIndex += 1
                Me.AddRow(intIndex, bInfo, BackupHelper.LstFailureBackupList.Count > 0, BackupFolderType.Failure)
            Next

            '如果统计大小的时候出错再进行统计一次
            If Not Me.LoadSize() Then
                Me.LoadSize()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
        Me.SetStartLoadDevice(True, "")
    End Sub

    Private Function GetQuickBackupLst() As List(Of BackupInfo)
        Dim lstTemp As New List(Of BackupInfo)
        Try
            For Each item As BackupInfo In BackupHelper.LstQuickBackupList
                If item.IntType = 0 AndAlso Me.mABType = AloneBackupType.WeChat Then
                    lstTemp.Add(item)
                ElseIf item.IntType = 1 AndAlso Me.mABType = AloneBackupType.WeChatSpare Then
                    lstTemp.Add(item)
                ElseIf item.IntType = 2 AndAlso Me.mABType = AloneBackupType.WeChatVIPSpare Then
                    lstTemp.Add(item)
                ElseIf Me.mABType = AloneBackupType.WeChatPayMigration Then
                    lstTemp.Add(item)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetQuickBackupLst")
        End Try
        Return lstTemp
    End Function

    Public Function LoadSize() As Boolean
        Dim blnRetuern As Boolean = True
        Try
            Dim blnFull As Boolean = DoLoadSize(BackupHelper.LstFullBackupList)
            Dim blnQuick As Boolean = DoLoadSize(BackupHelper.LstQuickBackupList)

            If Not blnFull OrElse Not blnQuick Then
                blnRetuern = False
            End If

        Catch ex As Exception
            Common.Log("Load Size Error:" & ex.ToString())
            blnRetuern = False
        End Try
        Return blnRetuern
    End Function

    Private Function DoLoadSize(ByVal lstTemp As List(Of BackupInfo)) As Boolean
        Dim blnRetuern As Boolean = True
        Try
            For Each Item As BackupInfo In lstTemp
                Dim longSize As ULong = BackupHelper.GetFileSize(Item.FolderPath, False)
                For Each row As tbDataGridViewRow In Me.mDgvBackupList.Rows
                    If row.Tag IsNot Nothing AndAlso TypeOf row.Tag Is BackupInfo AndAlso CType(row.Tag, BackupInfo).FolderPath = Item.FolderPath Then
                        row.Cells("colSize").Value = Utility.FormatFileSize(longSize)
                        Item.Size = longSize
                    End If
                Next
            Next
        Catch ex As Exception
            Common.Log("Load Size Error__1:" & ex.ToString())
            blnRetuern = False
        End Try
        Return blnRetuern
    End Function

#End Region

#Region "--- 删除 ---"
    Private Sub BackupListDeleteHelper_BackupListDeleteEventHandler(sender As Object, e As BackupListDeleteEventArgs)
        Try
            Dim cRow As tbDataGridViewRow = Nothing
            For Each item As tbDataGridViewRow In Me.mDgvBackupList.Rows
                Dim bakInfo As BackupInfo = item.Tag
                If bakInfo Is Nothing Then
                    Continue For
                End If
                If bakInfo.FolderPath = e.StrFolderPath Then
                    cRow = item
                    Exit For
                End If
            Next
            If cRow Is Nothing AndAlso e.DelStatus = DeleteStatus.DeleteFinish Then

                Return
            End If

            Dim cellProgressEx As tbDataGridViewProgressCellEx = cRow.Cells("colProgress")

            Select Case e.DelStatus

                Case DeleteStatus.WaitDelete
                    cellProgressEx.tbProgressStyle = ProgressStyle.Text
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = Me.Language.GetString("WeChat.Button.WaitingDelete") '"等待删除"
                    SetDgvBackupList(False)

                Case DeleteStatus.Deleteing
                    cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                    cellProgressEx.tbText = Me.Language.GetString("WeChat.Button.Deleting") '"正在删除"
                    cellProgressEx.tbShowCancelButton = True
                    If e.IntProgress = 100 Then
                        cellProgressEx.tbProgressValue = 99
                    Else
                        cellProgressEx.tbProgressValue = e.IntProgress
                    End If
                    SetDgvBackupList(False)

                Case DeleteStatus.Success
                    Try
                        XmlHelper.DeleteXMLForBackupPath(e.StrFolderPath)
                        Me.mDgvBackupList.RemoveGroupItem(cRow)
                        Dim info As BackupInfo = BackupHelper.CheckBackObjExist(e.StrFolderPath, 0)
                        If info IsNot Nothing Then
                            BackupHelper.LstFullBackupList.Remove(info)
                        Else
                            Try
                                For Each item As BackupInfo In BackupHelper.LstQuickBackupList
                                    If item.FolderPath = e.StrFolderPath Then
                                        BackupHelper.LstQuickBackupList.Remove(item)
                                        Exit For
                                    End If
                                Next
                            Catch
                            End Try
                        End If
                    Catch
                    End Try

                    SetDgvBackupList(True)

                Case DeleteStatus.Failure
                    cellProgressEx.tbProgressStyle = ProgressStyle.Text
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = Me.Language.GetString("Backup.Message.DeleteFailure") '删除失败

                    SetDgvBackupList(True)

            End Select

        Catch ex As Exception
            Common.LogException(ex.ToString(), "BackupListDeleteHelper_BackupListDeleteEventHandler")
        End Try
    End Sub

    Private Delegate Sub SetDgvBackupListHandler(ByVal isEnabled As Boolean)
    Private Sub SetDgvBackupList(ByVal isEnabled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetDgvBackupListHandler(AddressOf SetDgvBackupList), isEnabled)
        Else
            Me.mDgvBackupList.Enabled = isEnabled
        End If
    End Sub

#End Region

#Region "--- 数据迁移 ---"

    Private Sub btnChangeSKU_Click(sender As Object, e As EventArgs) Handles btnChangeSKU.Click
        Try
            If Me.mDevice Is Nothing Then
                tbMessageBox.Show(Me, "尚未连接设备，请先连接设备。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
                Return
            End If

            Dim bInfo As BackupInfo = Nothing
            If Me.mDgvBackupList.SelectedRows.Count > 0 Then
                For Each item As DataGridViewRow In Me.mDgvBackupList.SelectedRows
                    If item Is Nothing OrElse item.Tag Is Nothing OrElse Not TypeOf item.Tag Is BackupInfo Then
                        Continue For
                    End If
                    bInfo = item.Tag
                    Exit For
                Next
            End If

            If bInfo Is Nothing Then
                tbMessageBox.Show(Me, "请选择要迁移的备份数据。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
                Return
            End If

            Try
                If Not frmBackupCreate.CheckCanRestore(Me.mDevice, bInfo) Then
                    If tbMessageBox.Show(Me, String.Format("备份固件版本：{0} 无法迁移到您设备固件版本：{1}，请加QQ群反馈。", bInfo.ProductVersion, Me.mDevice.ProductVersion), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, "", False, New String() {"加QQ群"}) = Windows.Forms.DialogResult.OK Then
                        Common.OpenExplorer("https://jq.qq.com/?_wv=1027&k=4EpKHLE")
                    End If
                    Return
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "btnChangeSKU_Click")
            End Try

            '-------------------------------
            If Folder.AppType = RunType.iWeChatMigration Then

                If Not CheckIsAdd(bInfo) Then
                    Return
                End If

                If Not Me.GetChargeWeChatMigrationCharge() Then
                    Return
                End If
            End If

            '-------------------------    
Do_Retry:
            If Me.mWCMCinfo.DesUDID <> Me.mDevice.Identifier Then
                If tbMessageBox.Show(Me, String.Format("当前链接的设备【{0}】与配置的新设备【{1}】信息不符。{2}请连接配置的新设备后继续迁移，如果是新设备请添加新设备。", Me.mDevice.DeviceName, Me.mWCMCinfo.DesName, vbCrLf), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, "", False, New String() {"已连接，继续"}) <> Windows.Forms.DialogResult.OK Then
                    Return
                Else
                    GoTo Do_Retry
                End If
            End If

            If tbMessageBox.Show(Me, "请先卸载重装「新微信」，「新微信」数据会丢失，重装后不要打开不要使用。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, "", False, New String() {"已卸载重装，继续"}) <> Windows.Forms.DialogResult.OK Then
                Return
            End If

Do_RetryCheckiCloud:
            'icloud 提示
            Dim frmCC As frmCheckiCloud = Nothing
            If Me.mDeviceMode = DeviceMode.NomalMode Then
                '判断 icloud 查找我的iphone是否打开
                Dim objValue As Object = Me.mDevice.GetDeviceValue("com.apple.fmip", "IsAssociated")
                Dim blnValue As Boolean = CType(objValue, Boolean)
                If blnValue Then
                    frmCC = New frmCheckiCloud(iCloudType.FindMyIphoneClose)
                End If
            End If

            If frmCC IsNot Nothing Then
                If frmCC.ShowDialog <> Windows.Forms.DialogResult.OK Then
                    Return
                End If
                GoTo Do_RetryCheckiCloud
            End If

            If tbInputBox.Show(Me, "请根据教程双击Home退出所有的APP。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, "", False, My.Resources.backup_exitapp, "", "", "", False) <> Windows.Forms.DialogResult.OK Then
                Return
            End If

            If Me.mDevice Is Nothing Then
                If tbMessageBox.Show(Me, "请连接迁移的新设备（新设备和闪退设备可以是同一台）。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, "", False, New String() {"已连接，继续"}) <> Windows.Forms.DialogResult.OK Then
                    Return
                Else
                    GoTo Do_Retry
                End If
            End If

            '-------------------------

            Dim isFullBackup As Boolean = True
            If BackupHelper.LstQuickBackupList.Count > 0 AndAlso BackupHelper.LstQuickBackupList.Contains(bInfo) Then
                isFullBackup = False
            End If
            PremiumFeatureHelper.GetInstance(Me.mDevice.Identifier).GetWeChatVIPSpareInfo()

            Dim oldSku As String = Me.mStrSku
            Dim newSku As String = PremiumFeatureHelper.GetInstance(Me.mDevice.Identifier).CPremiumFeatureInfo.StrNewSku
            If Folder.AppType = RunType.iWeChatMigration Then
                oldSku = Me.mWCMCinfo.SrcSKU
                newSku = Me.mWCMCinfo.DesSKU
            End If

            Me.mBackupHelper.DataMigration(oldSku, newSku, bInfo.FolderPath, isFullBackup)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnChangeSKU_Click")
        End Try
    End Sub

    Private Sub OnMigrationProgressChanged(sender As Object, args As DataMigrationArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(Of DataMigrationArgs)(AddressOf Me.OnMigrationProgressChanged), sender, args)
                Return
            End If

            Dim CRow As tbDataGridViewRow = Nothing
            For Each item As tbDataGridViewRow In mDgvBackupList.Rows
                Dim bakInfo As BackupInfo = item.Tag
                If bakInfo Is Nothing Then
                    Continue For
                End If
                If bakInfo.FolderPath = args.FolderPath Then
                    CRow = item
                    Exit For
                End If
            Next

            If CRow Is Nothing OrElse CRow.Index < 0 Then
                Return
            End If

            Dim cellProgressEx As tbDataGridViewProgressCellEx = CRow.Cells("colProgress")
            Select Case args.Status
                Case DataMigrationStatus.Start
                    cellProgressEx.tbProgressStyle = ProgressStyle.Text
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = "准备迁移"
                    'Me.btnChangeSKU.Enabled = False
                    SetbtnChangeSKU(False)

                Case DataMigrationStatus.DetectionSku
                    cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                    cellProgressEx.tbShowCancelButton = False
                    If args.Progress = 100 Then
                        cellProgressEx.tbProgressValue = 99
                    Else
                        cellProgressEx.tbProgressValue = args.Progress
                        cellProgressEx.tbText = "正在解析SKU"
                    End If

                Case DataMigrationStatus.Convert
                    cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                    cellProgressEx.tbShowCancelButton = False
                    If args.Progress = 100 Then
                        cellProgressEx.tbProgressValue = 99
                    Else
                        cellProgressEx.tbProgressValue = args.Progress
                        cellProgressEx.tbText = "正在准备数据"
                    End If

                Case DataMigrationStatus.Modify
                    cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                    cellProgressEx.tbShowCancelButton = False
                    If args.Progress = 100 Then
                        cellProgressEx.tbProgressValue = 99
                    Else
                        cellProgressEx.tbProgressValue = args.Progress
                        cellProgressEx.tbText = "正在转换数据"
                    End If

                Case DataMigrationStatus.Restore
                    cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                    cellProgressEx.tbShowCancelButton = False
                    If args.Progress = 100 Then
                        cellProgressEx.tbProgressValue = 99
                    Else
                        cellProgressEx.tbProgressValue = args.Progress
                        cellProgressEx.tbText = "正在迁移数据"
                    End If

                Case DataMigrationStatus.FailureSku
                    cellProgressEx.tbProgressStyle = ProgressStyle.Failed
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = "存在多个分身版数据，请联系客服"
                    cellProgressEx.tbTextFail = args.ErrorMsg
                    'Me.btnChangeSKU.Enabled = True
                    SetbtnChangeSKU(True)
                    Me.btnWeChatDataMigration.Enabled = True

                Case DataMigrationStatus.Failure
                    cellProgressEx.tbProgressStyle = ProgressStyle.Failed
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = "数据迁移失败"
                    cellProgressEx.tbTextFail = args.ErrorMsg
                    'Me.btnChangeSKU.Enabled = True
                    SetbtnChangeSKU(True)
                    Me.btnWeChatDataMigration.Enabled = True

                Case DataMigrationStatus.Succeed
                    cellProgressEx.tbProgressStyle = ProgressStyle.Text
                    cellProgressEx.tbShowCancelButton = False
                    cellProgressEx.tbText = "数据迁移成功"
                    'Me.btnChangeSKU.Enabled = True
                    SetbtnChangeSKU(True)
                    Me.btnWeChatDataMigration.Enabled = True

            End Select

        Catch ex As Exception
            Common.LogException(ex.ToString(), "mBackupHelper_DataMigrationHandler")
        End Try
    End Sub

#End Region


    Private Delegate Sub SetbtnChangeSKUHandler(ByVal isEnabled As Boolean)
    Private Sub SetbtnChangeSKU(ByVal isEnabled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetbtnChangeSKUHandler(AddressOf SetbtnChangeSKU), isEnabled)
        Else
            Me.btnChangeSKU.Enabled = isEnabled
        End If
    End Sub

    Private Delegate Sub SetBuyButtonStatusHandler()
    Private Sub SetBuyButtonStatus()
        If Me.InvokeRequired Then
            Me.Invoke(New SetBuyButtonStatusHandler(AddressOf SetBuyButtonStatus))
        Else
            Try
                If Me.mChargeHelper.CheckIsVIP() Then
                    Me.btnBuy.BackColor = Color.Transparent
                    Me.btnBuy.tbIconImage = My.Resources.btn_buy_3
                    Me.btnBuy.Text = Me.Language.GetString("MainVip.Message.Renewal")   '"续期"
                    Me.btnBuy.Padding = New Padding(0, 3, 1, 0)
                Else
                    Me.btnBuy.BackColor = Color.Transparent
                    Me.btnBuy.tbIconImage = My.Resources.btn_active_3
                    Me.btnBuy.Text = Me.Language.GetString("MainVip.Button.Register") '"激活" 'Me.Language.GetString("MainVip.Message.UpgradeAtOnce")    '"马上升级"
                    Me.btnBuy.Padding = New Padding(0, 3, 1, 0) 'New Padding(1, 3, 1, 0)
                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "MainForm_SetBuyButtonStatus")
            End Try
        End If
    End Sub

    Private Delegate Sub SetMeTextHandler()
    Private Sub SetMeText()
        If Me.InvokeRequired Then
            Me.Invoke(New SetMeTextHandler(AddressOf SetMeText))
        Else
            Try

                If Folder.AppType = RunType.Tongbu_Abroad Then
                    Return
                End If

                Dim strType As String = String.Empty

                If Me.mChargeHelper.CheckIsVIP() Then
                    strType = "正式版"
                ElseIf Me.mChargeHelper.QueryResult.Status = ActiveStatus.OverDue Then
                    strType = "已过期"
                Else
                    strType = "试用版"
                End If

                Dim strBit As String = IIf(Common.IsX64, "64bit", "32bit")
                Dim strVer As String = String.Empty
                If My.Application.Info.Version.MinorRevision = 0 Then
                    strVer = My.Application.Info.Version.ToString(3)
                Else
                    strVer = My.Application.Info.Version.ToString(4)
                End If

                Dim strVersion As String = String.Format("v{0}_{1}", strVer, strBit)

                If Folder.AppType = RunType.iDataRecovery OrElse Folder.AppType = RunType.iDataRecovery_Abroad Then
                    Me.Text = String.Format("数据滚回来 {0}（{1}）", strVersion, strType)

                ElseIf Folder.AppType = RunType.iWeChatMigration Then
                    Dim strDeviceName As String = ""
                    If Me.mDevice IsNot Nothing Then
                        strDeviceName = String.Format("【当前连接设备：{0}】", Me.mDevice.DeviceName)
                    End If
                    If strType.Length > 0 Then
                        Me.Text = String.Format("微信闪退迁移专家 {0}（{1}） {2}", strVersion, strType, strDeviceName)
                    Else
                        Me.Text = String.Format("微信闪退迁移专家 {0} {1}", strVersion, strDeviceName)
                    End If

                ElseIf Me.mChargeHelper.ProjectType <> ChargeProjectType.Assistant_Wechat_iOS Then
                    Me.Text = String.Format("微信管理大师 {0}（{1}）", strVersion, strType)

                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetMeText")
            End Try
        End If
    End Sub

    Private Sub OnActiveCallback(sender As Object, e As ChargeResultArgs)
        Me.SetBuyButtonStatus()
        Me.SetMeText()

        Dim strIdentify As String = ""

        '如果用户注册成功后就进行设备注册。不然当前的设备没办法使用
        If Me.mCurrentSelectBackupInfo IsNot Nothing Then
            Me.mChargeHelper.SetDeviceRegistedLocal(Me.mCurrentSelectBackupInfo.Identifier, Me.mCurrentSelectBackupInfo.SerialNumber, Me.mCurrentSelectBackupInfo.ProductType, Me.mCurrentSelectBackupInfo.DeviceName)
            strIdentify = Me.mCurrentSelectBackupInfo.Identifier

        ElseIf Me.mDevice IsNot Nothing Then
            Me.mChargeHelper.SetDeviceRegistedLocal(Me.mDevice)

            If Me.mDevice IsNot Nothing Then
                strIdentify = Me.mDevice.Identifier
            End If
        End If

        '激活失败提示用户错误信息
        'If Not e.Code = ActiveCode.Success Then
        If Not e.Code = ActiveCode.HasAuthed Then
            tbMessageBox.Show(Me, e.Msg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, e.MsgBoxIcon)
        End If

        Me.mChargeHelper.PrintRegisterLog(String.Format("DeviceRegistedLocal:id:{0}", strIdentify))

        Me.SetMeText()
        Me.SetBuyButtonStatus()
    End Sub

    Private Sub frmWeChatSetting_WeChatSettingHandler(sender As Object, args As EventArgs)
        If Me.mABType = AloneBackupType.WeChatSpare Then
            Me.btnWeChatDataMigration.Visible = IniSetting.GetIsWeChatDataMigration()
        Else
            Me.btnWeChatDataMigration.Visible = False
        End If

        Me.mIsImportAloneBackup = IniSetting.GetIsWeChatImportAloneBackup()
    End Sub

    Public Function LoadBackInfo(ByVal strPath As String) As BackupInfo
        Dim bakObject As BackupInfo = New BackupInfo(strPath)
        Try
            Dim intIndex As Integer = 0
            Dim isExist As Boolean = False
            bakObject = Me.mBackupHelper.AddLstFullBackupList(strPath, intIndex, isExist)
            If Not isExist Then
                Dim intIndexE As Integer = 0
                If BackupHelper.LstQuickBackupList.Count > 0 Then
                    intIndexE = 1
                End If

                Me.AddRow(intIndexE, bakObject, BackupHelper.LstQuickBackupList.Count > 0, BackupFolderType.Full)
            End If

            Me.SetRowCanRestore()
        Catch ex As Exception
            Common.LogException(ex.ToString, "rmBackupList.LoadBackInfo")
        End Try
        Return bakObject
    End Function

    Public Function LoadAloneBackInfo(ByVal strPath As String) As BackupInfo
        Dim bakObject As BackupInfo = New BackupInfo(strPath)
        Try
            Dim intIndex As Integer = 0
            Dim isExist As Boolean = False
            bakObject = Me.mBackupHelper.AddLstAloneBackupList(strPath, intIndex, isExist)

            If Not Me.mBackupHelper.CheckAloneBackup(bakObject) Then
                tbMessageBox.Show(Me, String.Format("文件【{0}】{1}不是单独备份。", bakObject.FolderPath, vbCrLf), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return Nothing
            End If

            If bakObject IsNot Nothing Then
                Dim strQuickPath As String = Path.Combine(Folder.AloneWeChatBackupsFolder, Path.GetFileNameWithoutExtension(strPath))
                Dim intQuickIndex As Integer = 0
                Dim strTemp As String = strQuickPath
                While Directory.Exists(strTemp)
                    intQuickIndex += 1
                    strTemp = strQuickPath & "_" & intQuickIndex
                End While

                Folder.CheckFolder(strTemp)

                Try
                    My.Computer.FileSystem.CopyDirectory(strPath, strQuickPath)
                Catch
                End Try
            End If
            If Not isExist Then
                Dim intIndexE As Integer = 0
                If BackupHelper.LstQuickBackupList.Count > 0 Then
                    intIndexE = 1
                End If

                Me.AddRow(intIndexE, bakObject, BackupHelper.LstQuickBackupList.Count > 0, BackupFolderType.Quick)
            End If

            Me.SetRowCanRestore()
        Catch ex As Exception
            Common.LogException(ex.ToString, "rmBackupList.LoadBackInfo")
        End Try
        Return bakObject
    End Function

#Region "--- 付费迁移工具 ---"

    Private Sub InitChargeGridView()
        Me.mDgvCharge = New tbDataGridViewEx
        With Me.mDgvCharge
            .SuspendLayout()
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 38
            .AllowDrop = True
            .MultiSelect = True
            .tbNoDataText = "您尚未添加任何闪退微信，请点击「添加闪退微信」进行添加。"
            .ShowCellToolTips = True
            .Columns.Clear()

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colOldName", "闪退设备", _
                                                                   130, True, False, DataGridViewContentAlignment.MiddleCenter, False, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colOldSKU", "闪退微信SKU", _
                                                                   200, True, False, DataGridViewContentAlignment.MiddleCenter, False, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colNewName", "新设备", _
                                                                   130, True, False, DataGridViewContentAlignment.MiddleCenter, False, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colNewSKU", "新微信SKU", _
                                                                   200, True, False, DataGridViewContentAlignment.MiddleCenter, False, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colendtime", "过期日期", _
                                                                   85, True, False, DataGridViewContentAlignment.MiddleCenter, False, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", "", _
                                                                   50, True, True, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", _
                                                                   5, False, True, DataGridViewContentAlignment.MiddleLeft, True))
            .ResumeLayout()
        End With

        RemoveHandler Me.mDgvCharge.CellButtonClick, AddressOf mDgvCharge_CellButtonClick
        AddHandler Me.mDgvCharge.CellButtonClick, AddressOf mDgvCharge_CellButtonClick

        Me.pnlcharge.Controls.Add(Me.mDgvCharge)

    End Sub

    Private Sub btnAdd_Click(sender As Object, e As EventArgs) Handles btnAdd.Click
        Me.AddDevice()
    End Sub

    Private Function AddDevice() As Boolean
        Dim isRelust As Boolean = False
        If Me.mDevice Is Nothing Then
            tbMessageBox.Show(Me, "尚未连接设备，请连接要查看微信的设备后重试。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            GoTo Do_Exit
        End If
        Dim frm As frmIconManage = New frmIconManage(Me.mApplication, Me.mDevice, True)
        If frm.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
            Me.mWCMCinfo = New ChargeDevice()
            Me.mWCMCinfo.SrcSKU = frm.StrChooseSKU
            Me.mWCMCinfo.SrcUDID = Me.mDevice.Identifier
            Me.mWCMCinfo.SrcName = Me.mDevice.DeviceName

            Me.SetViewStyle(WeixinMainViewStyle.LoadBackup)
            isRelust = True
            SetMeText()
        End If
Do_Exit:
        Return isRelust
    End Function

    Private Delegate Sub AddChargeRowHandler(ByVal lstWMC As List(Of ChargeDevice))
    Public Sub AddChargeRow(ByVal lstWMC As List(Of ChargeDevice))
        If Me.InvokeRequired Then
            Me.Invoke(New AddChargeRowHandler(AddressOf AddChargeRow), lstWMC)
        Else
            Try
                Me.mDgvCharge.Rows.Clear()
                For Each item As ChargeDevice In lstWMC
                    Dim row As New tbDataGridViewRow
                    row.Height = 58

                    row.CreateCells(Me.mDgvCharge, _
                                           String.Format("{0} {1}", item.SrcName, item.SrcUDID), _
                                            item.SrcSKU, _
                                            String.Format("{0} {1}", item.DesName, item.DesUDID), _
                                            item.DesSKU, _
                                            item.ExpiryDate.ToString("yyyy-MM-dd"))

                    row.Tag = item
                    Me.mDgvCharge.Rows.Add(row)

                    Dim cellProgressEx As tbDataGridViewProgressCellEx = row.Cells("colProgress")

                    cellProgressEx.tbProgressStyle = ProgressStyle.Button
                    cellProgressEx.tbButtonSize = New Size(60, 24)
                    cellProgressEx.tbShowButtonThird = False
                    cellProgressEx.tbShowButtonSecond = False

                    cellProgressEx.tbShowButtonFirst = True
                    cellProgressEx.tbTextBackup = Me.Language.GetString("Photo.Menu.View")                  '"查看"
                Next
                Me.mDgvCharge.ResumeLayout()
            Catch ex As Exception
                Common.LogException("Add List Error" & ex.ToString, "AddRow")
            End Try
        End If
    End Sub

    Private Sub mDgvCharge_CellButtonClick(sender As Object, e As CellButtonEventArgs)
        Dim row As DataGridViewRow = Me.mDgvCharge.Rows(e.Cell.RowIndex)
        If row.Tag Is Nothing Then
            Return
        End If
        Dim info As ChargeDevice = row.Tag

        Select Case e.CellButton
            Case CellButtonStyle.Backup
                Me.mWCMCinfo = info
                Me.SetViewStyle(WeixinMainViewStyle.LoadBackup)
                SetMeText()
        End Select
    End Sub

    Public mWCMCinfo As ChargeDevice = Nothing
    Private Function GetChargeWeChatMigrationCharge() As Boolean
        Dim isRelust As Boolean = False

        Try
            If Me.mChargeHelper.CheckIsChargeApp(Me, FunctionKey.None, 0, "", True, mWCMCinfo) <> Windows.Forms.DialogResult.OK Then
                GoTo Do_Exit
            End If

            Dim isNotSet As Boolean = False

            If Me.mWCMCinfo.DesSKU.Length = 0 OrElse Me.mWCMCinfo.DesUDID.Length = 0 Then
                isNotSet = True
            End If

            If isNotSet Then
                If tbMessageBox.Show(Me, String.Format("请连接要迁移的新设备，连接成功后继续操作。{0}如果新设备是当前连接设备则直接继续。", vbCrLf), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2, "", False, New String() {"已连接，继续"}) <> Windows.Forms.DialogResult.OK Then
                    GoTo Do_Exit
                End If

                Dim frm As frmIconManage = New frmIconManage(Me.mApplication, Me.mDevice, True)
                If frm.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then


                    If tbMessageBox.Show(Me, String.Format("请确认信息:{0}从闪退设备:{1}{0}SKU:{2}{0}数据迁移至{0}新设备:{3}{0}SKU:{4}{0}确认无误后继续。", vbCrLf, Me.mWCMCinfo.SrcName, Me.mWCMCinfo.SrcSKU, Me.mDevice.DeviceName, frm.StrChooseSKU), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) = Windows.Forms.DialogResult.OK Then
                        Me.mWCMCinfo.DesSKU = frm.StrChooseSKU
                        Me.mWCMCinfo.DesUDID = Me.mDevice.Identifier
                        Me.mWCMCinfo.DesName = Me.mDevice.DeviceName
                        Me.mChargeHelper.PostPara.ChargeDevice = Me.mWCMCinfo

                        Me.mChargeHelper.Active()

                        If Not Me.mChargeHelper.CheckIsVIP() AndAlso Not String.IsNullOrEmpty(Me.mChargeHelper.QueryResult.Msg) Then
                            tbMessageBox.Show(Me, Me.mChargeHelper.QueryResult.Msg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                            GoTo Do_Exit
                        End If

                        isRelust = True

                    End If

                Else
                    GoTo Do_Exit
                End If
            Else
                If tbMessageBox.Show(Me, String.Format("请确认信息:{0}从设备:{1}{0}SKU:{2}{0}数据迁移至{0}设备:{3}{0}SKU:{4}{0}确认无误后继续。", vbCrLf, Me.mWCMCinfo.SrcName, Me.mWCMCinfo.SrcSKU, Me.mWCMCinfo.DesName, Me.mWCMCinfo.DesSKU), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) = Windows.Forms.DialogResult.OK Then
                    isRelust = True
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetChargeWeChatMigrationCharge")
        End Try
Do_Exit:
        Return isRelust
    End Function

    Private Function CheckIsAdd(ByVal backupObj As BackupInfo) As Boolean
        Dim isRelust As Boolean = True
        Try
            If Folder.AppType = RunType.iWeChatMigration Then
                If Me.mWCMCinfo IsNot Nothing AndAlso Me.mWCMCinfo.SrcUDID <> backupObj.Identifier Then
Do_Again:
                    If tbMessageBox.Show(Me, String.Format("该设备【{0}】尚未添加,请先连接该设备，然后选择要操作的APP。", backupObj.DeviceName), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button3, "", False, New String() {"已连接，继续"}) = Windows.Forms.DialogResult.OK Then

                        If Me.mDevice Is Nothing Then
                            If tbMessageBox.Show(Me, "尚未连接设备，请连接设备后继续。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button3, "", False, New String() {"已连接，继续"}) <> Windows.Forms.DialogResult.OK Then
                                isRelust = False
                                GoTo Do_Exit
                            Else
                                GoTo Do_Again
                            End If
                        End If

                        If Me.mDevice.Identifier <> backupObj.Identifier Then
                            GoTo Do_Again
                        End If

                        If Not Me.AddDevice() Then
                            isRelust = False
                        End If
                    Else
                        isRelust = False
                    End If
                End If
            End If
        Catch ex As Exception

        End Try
Do_Exit:
        Return isRelust
    End Function

    Private Sub OnQueryCallback(sender As Object, e As ChargeResultArgs)
        Try
            Me.SetBuyButtonStatus()
            AddChargeRow(Me.mChargeHelper.QueryResult.ChargeDevices)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_WeChatMigrationChargeHandler")
        End Try
    End Sub

    Private Sub DoDeleteTemp()
        SetDeleteTemp(Me.Language.GetString("Weixin.Button.PlanClearCache"), False) '"准备清理"
        Try
            Dim strMigrateTempBackup As String = String.Format("{0}{1}", IniSetting.StrMigrateTempFolder(), "MigrateBackup")
            If Directory.Exists(strMigrateTempBackup) Then
                Try
                    Common.LogException(String.Format("{1}{0}", strMigrateTempBackup, Me.Language.GetString("Weixin.Button.ClearCache")), "DoDeleteTemp")
                    SetDeleteTemp(Me.Language.GetString("Weixin.Button.Clearing"), False) '"清理中..."
                    Directory.Delete(strMigrateTempBackup, True)
                Catch
                End Try
                SetDeleteTemp(Me.Language.GetString("Weixin.Button.ClearSuccess"), False) '"清理成功"
            End If
        Catch ex As Exception
            SetDeleteTemp(Me.Language.GetString("Weixin.Button.ClearFailure"), False) '"清理失败"
            Common.LogException(ex.ToString(), "DoDeleteTemp")
        End Try
        Utility.WaitSeconds(3)
        SetDeleteTemp(Me.Language.GetString("Weixin.Button.ClearCache"), True)
    End Sub

    Private Delegate Sub SetDeleteTempHandler(ByVal strText As String, ByVal isEnabled As Boolean)
    Public Sub SetDeleteTemp(ByVal strText As String, ByVal isEnabled As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetDeleteTempHandler(AddressOf SetDeleteTemp), strText, isEnabled)
        Else
            Try
                Me.btnDeleteTemp.Text = strText
                Me.btnDeleteTemp.Enabled = isEnabled
            Catch ex As Exception
                Common.LogException(ex.ToString, "SetDeleteTemp")
            End Try
        End If
    End Sub

    Private Sub btnRefreshEx_Click(sender As Object, e As EventArgs) Handles btnRefreshEx.Click
        Me.mChargeHelper.Query(True)
    End Sub

#End Region

    Private Sub btnOpenVIP_Click(sender As Object, e As EventArgs) Handles btnOpenVIP.Click
        If Me.mChargeHelper.ProjectType = ChargeProjectType.Assistant_Wechat_iOS_DataMove Then
            Common.OpenExplorer("http://zs.tongbu.com/dvip/vip.html")
        Else
            Common.OpenExplorer("http://zs.tongbu.com/vip.html")
        End If

    End Sub

    Private Sub btn_SizeChanged(sender As Object, e As EventArgs) Handles btnQQ.SizeChanged, btnBuy.SizeChanged, btnOpenVIP.SizeChanged
        If btn_minimize IsNot Nothing Then
            If Me.btnWeChat.Visible Then
                Me.btnWeChat.Location = New Point(btn_minimize.Left - Me.btnWeChat.Size.Width, Me.btnWeChat.Location.Y)
            End If

            If Me.btnQQ.Visible Then
                If Me.btnWeChat.Visible Then
                    Me.btnQQ.Location = New Point(btnWeChat.Left - Me.btnQQ.Size.Width, Me.btnQQ.Location.Y)
                Else
                    Me.btnQQ.Location = New Point(btn_Setting.Left - Me.btnQQ.Size.Width, Me.btnQQ.Location.Y)
                End If
            End If
        End If

        If Me.btnBuy.Visible Then
            If Me.btnQQ.Visible Then
                Me.btnBuy.Location = New Point(btnQQ.Left - Me.btnBuy.Size.Width - 5, Me.btnBuy.Location.Y)
            ElseIf Me.btnWeChat.Visible Then
                Me.btnBuy.Location = New Point(btnWeChat.Left - Me.btnBuy.Size.Width - 5, Me.btnBuy.Location.Y)
            Else
                Me.btnBuy.Location = New Point(btn_Setting.Left - Me.btnBuy.Size.Width - 5, Me.btnBuy.Location.Y)
            End If
        End If

        If Me.btnOpenVIP.Visible Then
            If Me.btnBuy.Visible Then
                Me.btnOpenVIP.Location = New Point(btnBuy.Left - Me.btnOpenVIP.Size.Width - 5, Me.btnOpenVIP.Location.Y)
            ElseIf Me.btnQQ.Visible Then
                Me.btnOpenVIP.Location = New Point(btnQQ.Left - Me.btnOpenVIP.Size.Width - 5, Me.btnOpenVIP.Location.Y)
            ElseIf Me.btnWeChat.Visible Then
                Me.btnOpenVIP.Location = New Point(btnWeChat.Left - Me.btnOpenVIP.Size.Width - 5, Me.btnOpenVIP.Location.Y)
            Else
                Me.btnOpenVIP.Location = New Point(btn_Setting.Left - Me.btnOpenVIP.Size.Width - 5, Me.btnOpenVIP.Location.Y)
            End If

        End If

    End Sub

    Private Sub btnWeChat_Click(sender As Object, e As EventArgs) Handles btnWeChat.Click
#If IS_WECHAT Then
        If (tbMessageBox.Show(Me, ServerIniSetting.GetWeChatCoexistHint, Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK) Then
            Common.JoinQQGroup(QQGroupNumber.Q587033524)
        End If
#Else
        If Me.mABType = AloneBackupType.WeChat Then
            If tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.JumpRemind"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = Windows.Forms.DialogResult.OK Then '"确定关闭当前界面，跳转到微信共存版管理？"
                Try
                    Me.Hide()
                    Me.Dispose()
                    Me.Close()
                Catch
                End Try
                MainForm.OpenWeChatSpare(GetType(frmWeixinMain), Me.mApplication, Me.mDevice)
            End If

        ElseIf Me.mABType = AloneBackupType.WeChatSpare OrElse Me.mABType = AloneBackupType.WeChatVIPSpare Then
            If (tbMessageBox.Show(Me, ServerIniSetting.GetWeChatCoexistHint, Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK) Then
                Common.JoinQQGroup(QQGroupNumber.Q587033524)
            End If
            IniSetting.SetShowWeChatCoexist()
        End If
#End If
    End Sub

    '''' 处理iCloud系列函数

    '#If IS_iCloud Then
    '' 以下为使用iCloud所涉及的变量定义——Added by Utmost20171124
    Private Enum iCloudDisplayStatus
        kInitialStatus = 0                  '' 初始状态
        kLoginingStatus = 1                 '' 开始登录状态
        kGetiCloudStatus = 2                '' 登录成功状态，开始获取备份列表
        kNodataStatus = 3                   '' 没有微信数据状态
        kShowGridviewStatus = 4             '' 显示所有备份数据
    End Enum

    Private m_enmCurrentStatus As iCloudDisplayStatus = iCloudDisplayStatus.kInitialStatus

    Private m_iCloudGridView As tbDataGridViewEx = Nothing
    Private m_currentRow As Integer = -1
    Private m_iCloudBackup As iCloudBackup = Nothing
    Private m_bLogining As Boolean = False


    Private Sub InitiCloudBackup()
        Me.m_iCloudGridView = New tbDataGridViewEx()
        With Me.m_iCloudGridView
            .SuspendLayout()
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 38
            .AllowDrop = False
            .MultiSelect = False
            .ShowCellToolTips = True
            ''.tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .Columns.Clear()

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Common.Label.Name"), _
                                                                  230, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colDateTime", Me.Language.GetString("App.Button.TimeSort"), _
                                                                   115, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"时间"

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colSize", Me.Language.GetString("Common.Columns.Size"), _
                                                                   100, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"Size"

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colVersion", Me.Language.GetString("App.Column.Version"), _
                                                                   80, True, False, DataGridViewContentAlignment.MiddleRight, False, DataGridViewTriState.False)) '"当前版本"

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colDeviceType", Me.Language.GetString("App.Button.DeviceType"), _
                                                                   120, True, False, DataGridViewContentAlignment.MiddleLeft, False, DataGridViewTriState.False)) '"设备类型"  

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", "", 120, True, True, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False))
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, False, True, DataGridViewContentAlignment.MiddleLeft, True))


            RemoveHandler .CellDoubleClick, AddressOf iCloudGridView_CellDoubleClick
            AddHandler .CellDoubleClick, AddressOf iCloudGridView_CellDoubleClick

            RemoveHandler .CellButtonClick, AddressOf iCloudGridView_CellButtonClick
            AddHandler .CellButtonClick, AddressOf iCloudGridView_CellButtonClick

            RemoveHandler .ShowToolTip, AddressOf iCloudGridView_ShowToolTip
            AddHandler .ShowToolTip, AddressOf iCloudGridView_ShowToolTip

            .ResumeLayout()
        End With

        Me.pnliCloudGrid.Controls.Add(Me.m_iCloudGridView)

        RemoveHandler iCloudException.PatchException, AddressOf iCloudException_PatchException
        AddHandler iCloudException.PatchException, AddressOf iCloudException_PatchException


        Me.txtAppleId.TextTip = Me.Language.GetStringTrim("App.Message.EnterCorrectAppleId")
        Me.txtPwd.TextTip = Me.Language.GetStringTrim("App.Lable.EnterPassword")
        Me.txtPwd.Text = ""
        Me.txtAppleId.Text = ""

        Me.lbliCloudErrorServer.Visible = False

        Folder.CheckFolder(Folder.iCloudBackupFolder)
    End Sub

    Private Sub ShowDeleteiCloudBackup(ByVal bIsDeleted As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf ShowDeleteiCloudBackup), bIsDeleted)
        Else

            m_iCloudGridView.Enabled = Not bIsDeleted
            Dim cellProgressEx As tbDataGridViewProgressCellEx = m_iCloudGridView.Rows(m_currentRow).Cells("colProgress")

            If bIsDeleted Then
                cellProgressEx.tbProgressStyle = ProgressStyle.Progress
                cellProgressEx.tbText = Me.Language.GetString("WeChat.Button.Deleting") '"正在删除"
            Else
                cellProgressEx.tbProgressStyle = ProgressStyle.Button
                cellProgressEx.tbTextBackup = Me.Language.GetStringTrim("Site.Label.Download")
                cellProgressEx.tbShowButtonSecond = False
                cellProgressEx.Tag = "Download"
            End If

        End If

    End Sub
    Private Sub DeleteiCloudBackupFiles(ByVal row As DataGridViewRow)
        If row.Tag Is Nothing Then
            Return
        End If
        If tbMessageBox.Show(Me, Me.Language.GetString("iCloud.DeleteBackup.Message"), _
                             Me.Language.GetString("Common.Info"), _
                             MessageBoxButtons.OKCancel, _
                             MessageBoxIcon.Question, _
                             MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then

            Me.ShowDeleteiCloudBackup(True)
            Application.DoEvents()

            If TypeOf (row.Tag) Is Object() Then
                Dim aryTags As Object() = CType(row.Tag, Object())
                Dim bkpItem As DeviceBackup = CType(aryTags(0), DeviceBackup)
                Dim icloudBackup As New BackupItems()

                Dim icloudFolder As String = Path.Combine(Folder.iCloudBackupFolder, bkpItem.AppleId)
                icloudFolder = Path.Combine(icloudFolder, String.Format("{0}{1}", bkpItem.SerialNumber, _
                                         CustomUtils.GetDateTimeFromTimeStamp2001(bkpItem.CreateTime).ToString(iCloudDateTime.y4MdHms)))

                RemoveHandler icloudBackup.DeleteDownloadBackupHandler, AddressOf icloudBackup_DeleteDownloadBackupHandler
                AddHandler icloudBackup.DeleteDownloadBackupHandler, AddressOf icloudBackup_DeleteDownloadBackupHandler


                icloudBackup.DeleteDownloadediCloudBackupItem(bkpItem, Nothing, icloudFolder)

            ElseIf TypeOf (row.Tag) Is iCloudCopy Then
                Dim bkpItem As iCloudCopy = CType(row.Tag, iCloudCopy)
                Dim icloudBackup As New BackupItems()
                Dim icloudFolder As String = Path.Combine(Folder.iCloudBackupFolder, bkpItem.AppleId)

                icloudFolder = Path.Combine(icloudFolder, String.Format("{0}{1}", bkpItem.SerialNumber, _
                                         CustomUtils.GetDateTimeFromTimeStamp1970(bkpItem.LastModified).ToString(iCloudDateTime.y4MdHms)))

                RemoveHandler icloudBackup.DeleteDownloadBackupHandler, AddressOf icloudBackup_DeleteDownloadBackupHandler
                AddHandler icloudBackup.DeleteDownloadBackupHandler, AddressOf icloudBackup_DeleteDownloadBackupHandler

                icloudBackup.DeleteDownloadediCloudBackupItem(Nothing, bkpItem, Path.Combine(Folder.iCloudBackupFolder, icloudFolder))
            End If
        End If
    End Sub

    Private Sub DownloadiCloudFiles(ByVal row As DataGridViewRow)

        If row.Tag Is Nothing Then
            Return
        End If

        Me.ShowiCloudGridView(False)
        Me.ShowiCloudDownLoadiCloud(True)
        Me.ShowiCloudDownStatus(True)
        Application.DoEvents()


        Dim dictDownItems As New Dictionary(Of iCloudDataItems, String)(1)
        If String.IsNullOrEmpty(Me.mStrSku) Then
            Me.mStrSku = "com.tencent.xin"
        End If
        dictDownItems.Add(iCloudDataItems.weixin, Me.mStrSku)

        If TypeOf (row.Tag) Is Object() Then
            Dim aryTags As Object() = CType(row.Tag, Object())
            Dim downloadItems As New BackupItems(CType(aryTags(0), DeviceBackup), Folder.iCloudBackupFolder)

            RemoveHandler downloadItems.DownloadErrorEvent, AddressOf downloadItems_DownloadErrorEvent
            AddHandler downloadItems.DownloadErrorEvent, AddressOf downloadItems_DownloadErrorEvent

            RemoveHandler downloadItems.DownloadProgressEvent, AddressOf downloadItems_DownloadProgressEvent
            AddHandler downloadItems.DownloadProgressEvent, AddressOf downloadItems_DownloadProgressEvent

            RemoveHandler downloadItems.AssetDownloadHandler, AddressOf downloadItems_AssetDownloadHandler
            AddHandler downloadItems.AssetDownloadHandler, AddressOf downloadItems_AssetDownloadHandler

            RemoveHandler downloadItems.AssetsRequestEvent, AddressOf downloadItems_AssetsRequestEvent
            AddHandler downloadItems.AssetsRequestEvent, AddressOf downloadItems_AssetsRequestEvent

            downloadItems.DownItemLists = dictDownItems
            downloadItems.DownloadingDeviceBackupItems(CType(aryTags(1), Backup))

        ElseIf TypeOf (row.Tag) Is iCloudCopy Then
            Dim downloadItems As New BackupItems(CType(row.Tag, iCloudCopy), m_iCloudBackup.ListiCloudCopies, Folder.iCloudBackupFolder)
            RemoveHandler downloadItems.DownloadErrorEvent, AddressOf downloadItems_DownloadErrorEvent
            AddHandler downloadItems.DownloadErrorEvent, AddressOf downloadItems_DownloadErrorEvent

            RemoveHandler downloadItems.DownloadProgressEvent, AddressOf downloadItems_DownloadProgressEvent
            AddHandler downloadItems.DownloadProgressEvent, AddressOf downloadItems_DownloadProgressEvent

            downloadItems.DownItemLists = dictDownItems
            downloadItems.DownloadingiCloudCopyItems()
        End If
    End Sub

    Private Sub ViewiCloudFiles(ByVal row As DataGridViewRow)
        Dim downloadPath As String = ""
        If row IsNot Nothing Then

            If row.Tag Is Nothing Then
                Return
            End If

            If TypeOf (row.Tag) Is Object() Then
                Dim aryTags As Object() = CType(row.Tag, Object())
                Dim bkpItem As DeviceBackup = CType(aryTags(0), DeviceBackup)
                downloadPath = String.Format("{0}{1}", bkpItem.SerialNumber, _
                                             CustomUtils.GetDateTimeFromTimeStamp2001(bkpItem.CreateTime).ToString(iCloudDateTime.y4MdHms))

                downloadPath = Path.Combine(Path.Combine(Folder.iCloudBackupFolder, bkpItem.AppleId), downloadPath)

            ElseIf TypeOf (row.Tag) Is iCloudCopy Then
                Dim bkpItem As iCloudCopy = CType(row.Tag, iCloudCopy)
                downloadPath = String.Format("{0}{1}", bkpItem.SerialNumber, _
                                            CustomUtils.GetDateTimeFromTimeStamp1970(bkpItem.LastModified).ToString(iCloudDateTime.y4MdHms))
                downloadPath = Path.Combine(Path.Combine(Folder.iCloudBackupFolder, bkpItem.AppleId), downloadPath)
            End If
        Else
            downloadPath = "E:\Tongbu\iCloud-Backup\<EMAIL>\C39V5M39JCM0201712111458"
        End If

        If downloadPath.Length > 0 AndAlso Directory.Exists(downloadPath) Then
            Dim fmbdb As String = Path.Combine(downloadPath, "Manifest.mbdb")
            Dim fdb As String = Path.Combine(downloadPath, "Manifest.db")
            If File.Exists(fmbdb) OrElse File.Exists(fdb) Then

                Dim bkpInfo As BackupInfo = BackupHelper.CheckBackObjExist(downloadPath)
                If bkpInfo Is Nothing Then
                    bkpInfo = New BackupInfo(downloadPath)
                End If

                Me.LoadiCloudWechat(bkpInfo, downloadPath)
            Else
                Me.ShowNoiCloudFiles()
            End If
        End If
    End Sub


    Private Sub downloadItems_DownloadErrorEvent(ByVal err As DownloadingEnumValues)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of DownloadingEnumValues)(AddressOf downloadItems_DownloadErrorEvent), err)
        Else

            If err = DownloadingEnumValues.kCancelDownloading Then
                Me.ShowiCloudPanel()
            ElseIf err = DownloadingEnumValues.kHasNoneiCloudFiles Then
                Me.ShowNoiCloudFiles()
            End If
        End If

    End Sub

    Private Sub ShowiCloudDownloadPress(ByVal tips As String, Optional ByVal percentMsg As String = "", Optional ByVal stepValue As String = "")
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of String, String, String)(AddressOf ShowiCloudDownloadPress), tips, percentMsg, stepValue)
        Else
            If stepValue.Length > 0 Then
                Me.lblPercentage.Text = stepValue
            Else
                Me.lblPercentage.Text = ""
            End If
            Dim strTips As String = tips
            If percentMsg.Length > 0 Then
                strTips = strTips & vbCrLf & percentMsg
            End If

            Me.lbliCloudWaitingMsg.Text = strTips
        End If
    End Sub

    Private Sub downloadItems_DownloadProgressEvent(ByVal state As DownloadingEnumValues, ByVal downloadingSize As Long, ByVal totalSize As Long)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of DownloadingEnumValues, Long, Long)(AddressOf downloadItems_DownloadProgressEvent), state, downloadingSize, totalSize)
        Else
            m_enmCurrentStatus = iCloudDisplayStatus.kGetiCloudStatus
            Select Case state
                Case DownloadingEnumValues.kReadyData
                    Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.GetFiles.Message"))
                    Application.DoEvents()
                Case DownloadingEnumValues.kInsertDBCache
                    Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.InsertDB.Message"))
                    Application.DoEvents()
                Case DownloadingEnumValues.kDownloading
                    If downloadingSize = 0L Then
                        Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.DownloadFiles.Message"))
                    Else
                        Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.DownloadFiles.Message"), _
                                                   String.Format(Me.Language.GetStringTrim("iCloud.DownloadFilesPercent.Message"), _
                                                                 CustomUtils.FormatFileSize(downloadingSize, 2), _
                                                                 CustomUtils.FormatFileSize(totalSize, 2)), _
                                                   CustomUtils.FormatPercent(downloadingSize, totalSize, 2))

                    End If
                    Application.DoEvents()
                Case DownloadingEnumValues.kDownloadCompleted, DownloadingEnumValues.kCompletedDatabase
                    '''下载完成
                    ''' 
                    m_enmCurrentStatus = iCloudDisplayStatus.kShowGridviewStatus

                    Me.ShowiCloudDownStatus(False)
                    Me.ShowiCloudGridView(True)
                    Application.DoEvents()

                    Me.DisplayCellProgressEx(m_iCloudGridView.Rows(m_currentRow), True)
                    Me.WriteDownloadItems(m_iCloudGridView.Rows(m_currentRow))

                    Me.ViewiCloudFiles(m_iCloudGridView.Rows(m_currentRow))

                Case DownloadingEnumValues.kGetiCloudFiles

                    If downloadingSize = 0L Then
                        Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.GetFiles.Message"))
                    Else
                        Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.GetFiles.Message"), _
                                                   String.Format(Me.Language.GetStringTrim("iCloud.DownloadFilesPercent.Message"), _
                                                                 CustomUtils.FormatFileSize(downloadingSize, 2), _
                                                                 CustomUtils.FormatFileSize(totalSize, 2)), _
                                                    CustomUtils.FormatPercent(downloadingSize, totalSize, 2))

                    End If
                    Application.DoEvents()
                Case DownloadingEnumValues.kGetiCloudFileProto
                    Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.GetFiles.Message"), _
                                       String.Format(" ({0}/{1})", downloadingSize, totalSize), _
                                       CustomUtils.FormatPercent(downloadingSize, totalSize))
                    Application.DoEvents()
            End Select
        End If
    End Sub

    Private Sub downloadItems_AssetDownloadHandler(ByVal downloadingSize As Long, ByVal totalSize As Long)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Long, Long)(AddressOf downloadItems_AssetDownloadHandler), downloadingSize, totalSize)
        Else

            Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.DownloadFiles.Message"), _
                                       String.Format(Me.Language.GetStringTrim("iCloud.DownloadFilesPercent.Message"), _
                                                     CustomUtils.FormatFileSize(downloadingSize, 2), _
                                                     CustomUtils.FormatFileSize(totalSize, 2)), _
                                       CustomUtils.FormatPercent(downloadingSize, totalSize, 2))
        End If
    End Sub

    Private Sub downloadItems_AssetsRequestEvent(ByVal fileCount As Integer, ByVal totalCount As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Integer, Integer)(AddressOf downloadItems_AssetsRequestEvent), fileCount, totalCount)
        Else

            Me.ShowiCloudDownloadPress(Me.Language.GetStringTrim("iCloud.GetFiles.Message"), _
                                       String.Format(" ({0}/{1})", fileCount, totalCount), _
                                       CustomUtils.FormatPercent(fileCount, totalCount))
        End If

    End Sub

    Private Sub icloudBackup_DeleteDownloadBackupHandler(ByVal fileCount As Integer, ByVal totalCount As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Integer, Integer)(AddressOf icloudBackup_DeleteDownloadBackupHandler), fileCount, totalCount)
        Else
            If fileCount < totalCount Then

                Dim cellProgressEx As tbDataGridViewProgressCellEx = m_iCloudGridView.Rows(m_currentRow).Cells("colProgress")
                cellProgressEx.tbProgressValue = CustomUtils.StepPercent(fileCount, totalCount)

            Else
                Me.ShowDeleteiCloudBackup(False)
                Me.WriteDownloadItems(m_iCloudGridView.Rows(m_currentRow), True)
            End If

        End If
    End Sub

    Private Sub iCloudGridView_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Object, CellButtonEventArgs)(AddressOf iCloudGridView_CellButtonClick), sender, e)
        Else
            ManagerFactory.Instance.IsCancel = False
            m_currentRow = e.Cell.RowIndex

            Dim row As DataGridViewRow = Me.m_iCloudGridView.Rows(m_currentRow)

            If e.CellButton = CellButtonStyle.Backup Then  '' 下载或查看

                Dim cellProgressEx As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                If cellProgressEx IsNot Nothing Then
                    Dim strKey As String = cellProgressEx.Tag.ToString()

                    If CustomUtils.EqualCase(strKey, "Download") Then
                        Me.DownloadiCloudFiles(row)
                    ElseIf CustomUtils.EqualCase(strKey, "View") Then
                        Me.ViewiCloudFiles(row)
                    End If

                End If

            ElseIf e.CellButton = CellButtonStyle.Uninstall Then  '' 删除
                Me.DeleteiCloudBackupFiles(row)
            End If

        End If

    End Sub

    Private Sub iCloudGridView_CellDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs)

    End Sub
    Private Sub iCloudGridView_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)

    End Sub


    Private Sub iCloudException_PatchException(ByVal code As ExceptionCode, ByVal message As String, ByVal modename As String)
        Common.LogException(String.Format("Exception Code={0}, Exception Message={1}", code, message), modename)
    End Sub

    Private Sub iCloudBackup_DownloadingiCloudBackupEvent(ByVal item As iCloudCopy)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of iCloudCopy)(AddressOf iCloudBackup_DownloadingiCloudBackupEvent), item)
        Else
            Me.AddResults(Nothing, Nothing, item)
        End If
    End Sub

    Private Sub iCloudBackup_DownloadiCloudBackupCompletedEvent(ByVal bIsCompleted As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf iCloudBackup_DownloadiCloudBackupCompletedEvent), New Object() {bIsCompleted})
        Else
            Me.ShowiCloudWaiting(False)
        End If
    End Sub

    Private Sub ShowiCloudPanel()
        If m_enmCurrentStatus = iCloudDisplayStatus.kInitialStatus Then
            ShowiCloudLoginPanel(True)
            ShowiCloudGridView(False)
            ShowiCloudDownStatus(False)
        Else
            ShowiCloudLoginPanel(False)
            ShowiCloudDownStatus(True)

            ShowiCloudGridView((m_enmCurrentStatus = iCloudDisplayStatus.kGetiCloudStatus) Or _
                               (m_enmCurrentStatus = iCloudDisplayStatus.kShowGridviewStatus) Or _
                               (m_enmCurrentStatus = iCloudDisplayStatus.kLoginingStatus))

        End If

        ShowiCloudDownLoadiCloud(False)
        ShowiCloudWaiting(m_enmCurrentStatus = iCloudDisplayStatus.kGetiCloudStatus)
        ShowiCloudNoData(m_enmCurrentStatus = iCloudDisplayStatus.kNodataStatus)

        Application.DoEvents()
    End Sub

    Private Sub ShowiCloudLoginPanel(ByVal bIsShowed As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf ShowiCloudLoginPanel), New Object() {bIsShowed})
        Else
            Me.pnliCloudLogin.Visible = bIsShowed
            Me.pnliCloudLogin.BringToFront()
            Me.pnliCloudLogin.Dock = DockStyle.Fill
        End If
    End Sub
    Private Sub ShowNoiCloudFiles()
        '' 没有微信数据库
        m_enmCurrentStatus = iCloudDisplayStatus.kNodataStatus
        Me.ShowiCloudGridView(False)
        Me.ShowiCloudDownStatus(False)
        Me.ShowiCloudNoData(True)
        Application.DoEvents()
    End Sub
    Private Sub ShowiCloudGridView(ByVal bIsShowed As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf ShowiCloudGridView), New Object() {bIsShowed})
        Else
            If bIsShowed Then
                Me.pnliCloudGrid.BringToFront()
            Else
                Me.pnliCloudGrid.SendToBack()
            End If

            Me.pnliCloudGrid.Visible = bIsShowed
            Me.btniCloudLogout.Visible = bIsShowed
            Me.lbliCloudAcct.Visible = bIsShowed
            Me.lbliCloudView.Visible = bIsShowed
        End If
    End Sub
    Private Sub ShowiCloudDownStatus(ByVal bIsShowed As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf ShowiCloudDownStatus), New Object() {bIsShowed})
        Else
            Me.pnliCloudDownStatus.Visible = bIsShowed
            If bIsShowed Then
                Me.pnliCloudDownStatus.Dock = DockStyle.Fill
                Me.pnliCloudDownStatus.BringToFront()
            Else
                Me.pnliCloudDownStatus.SendToBack()
                lblPercentage.Text = ""
                Me.lbliCloudWaitingMsg.Text = ""
            End If
        End If
    End Sub

    Private Sub ShowiCloudDownLoadiCloud(ByVal bIsShowed As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf ShowiCloudDownLoadiCloud), New Object() {bIsShowed})
        Else
            Me.pnlDownLoadiCloud.Visible = bIsShowed
        End If
    End Sub

    Private Sub ShowiCloudNoData(ByVal bIsShowed As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf ShowiCloudNoData), New Object() {bIsShowed})
        Else
            Me.pnliCloudNodata.Visible = bIsShowed
            If bIsShowed Then
                Me.pnliCloudNodata.BringToFront()
                Me.pnliCloudNodata.Dock = DockStyle.Fill
            Else
                Me.pnliCloudNodata.SendToBack()
            End If
        End If
    End Sub
    Private Sub ShowiCloudWaiting(ByVal bIsShowed As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf ShowiCloudWaiting), New Object() {bIsShowed})
        Else
            If bIsShowed Then
                Me.pnliCloudWaiting.BringToFront()
            Else
                Me.pnliCloudWaiting.SendToBack()
            End If

            Me.pnliCloudWaiting.Visible = bIsShowed
            Me.lbliCloudView.Visible = Not bIsShowed
            Me.lbliCloudAcct.Visible = Not bIsShowed
            Me.btniCloudLogout.Visible = Not bIsShowed
        End If

    End Sub
    Private Sub ShowiCloudAppleId(ByVal appleId As String)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of String)(AddressOf ShowiCloudAppleId), New Object() {appleId})
        Else
            Me.lbliCloudAcct.Text = Me.Language.GetStringTrim("iCloud.Account.Message") & appleId
        End If
    End Sub

    Private Sub CleariCloud(Optional ByVal bIsCleared As Boolean = True)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf CleariCloud), New Object() {bIsCleared})
        Else

            If m_iCloudBackup IsNot Nothing Then
                If m_iCloudBackup.ListiCloudCopies IsNot Nothing Then
                    m_iCloudBackup.ListiCloudCopies.Clear()
                End If
                m_iCloudBackup.LoginInfo.iCloudAccountInfos = Nothing
                m_iCloudBackup.LoginInfo = Nothing
                m_iCloudBackup = Nothing
            End If

            Me.lbliCloudAcct.Text = Me.Language.GetStringTrim("iCloud.Account.Message")
            If m_iCloudGridView IsNot Nothing Then
                m_iCloudGridView.Rows.Clear()
            End If

            Me.lbliCloudAcct.Text = Me.Language.GetStringTrim("iCloud.Account.Message")
            Me.txtPwd.Text = ""
            Me.txtPwd.PasswordChar = ""
            Me.btniCloudLogin.Text = Me.Language.GetStringTrim("iCloud.Login")

            ManagerFactory.Instance.IsCancel = True
            Utility.WaitSeconds(0.01)
        End If
    End Sub
    Private Sub ShowiCloudFailture(ByVal bIsShowed As Boolean, ByVal errmsg As String)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean, String)(AddressOf ShowiCloudFailture), New Object() {bIsShowed, errmsg})
        Else
            lbliCloudfailture.Visible = bIsShowed
            lbliCloudfailture.Text = errmsg
        End If
    End Sub

    Private Sub ShowiCloudLoginText(ByVal bIsLogin As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of Boolean)(AddressOf ShowiCloudLoginText), New Object() {bIsLogin})
        Else
            If bIsLogin Then
                txtAppleId.Focus()
                btniCloudLogin.Text = Me.Language.GetStringTrim("iCloud.Login")
            Else
                btniCloudLogin.Text = Me.Language.GetStringTrim("App.Lable.Logging")
            End If
            m_bLogining = Not bIsLogin
        End If
    End Sub

    Private Sub InitialiCloudBackup()
        m_iCloudBackup = New iCloudBackup()

        RemoveHandler m_iCloudBackup.DownloadingiCloudBackupEvent, AddressOf iCloudBackup_DownloadingiCloudBackupEvent
        AddHandler m_iCloudBackup.DownloadingiCloudBackupEvent, AddressOf iCloudBackup_DownloadingiCloudBackupEvent

        RemoveHandler m_iCloudBackup.DownloadiCloudBackupCompletedEvent, AddressOf iCloudBackup_DownloadiCloudBackupCompletedEvent
        AddHandler m_iCloudBackup.DownloadiCloudBackupCompletedEvent, AddressOf iCloudBackup_DownloadiCloudBackupCompletedEvent
    End Sub

    Private Sub btniCloud_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btniCloud.Click, btniCloud_1.Click, btniCloud_2.Click
        Me.SetViewStyle(WeixinMainViewStyle.iCloudBackup)
        ActionCollectHelper.WeChatFindData(Me.mDevice, ModelKey.Weixin, ModelKey.WexiniCloud, ActionDataType.Click, FunctionSucceed.Succeed)
    End Sub

    Private Sub btniCloudLogin_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btniCloudLogin.Click
        If m_bLogining Then
            Return
        End If

        Dim strAppleId As String = Common.ChangeStringFullMode2HalfMode(txtAppleId.Text.Trim())
        Dim strPwd As String = Common.ChangeStringFullMode2HalfMode(txtPwd.Text.Trim())

        ''' 测试..............
        'ViewiCloudFiles(Nothing)

        'Return

        ManagerFactory.Instance.IsCancel = False
        If Not String.IsNullOrEmpty(strAppleId) AndAlso Not String.IsNullOrEmpty(strPwd) Then

            m_enmCurrentStatus = iCloudDisplayStatus.kLoginingStatus

            Me.InitialiCloudBackup()
            Me.ShowiCloudLoginText(False)
            Me.ShowiCloudFailture(False, "")

            Application.DoEvents()

            Dim icloudLogin As New iCloudRemoteLogin()

            RemoveHandler icloudLogin.RemoteLogin, AddressOf icloudLogin_RemoteLogin
            AddHandler icloudLogin.RemoteLogin, AddressOf icloudLogin_RemoteLogin

            icloudLogin.LoginiCloud(strAppleId, strPwd, False)
        Else
            Me.ShowiCloudLoginText(True)
        End If
    End Sub


    Private Sub btniCloudCancel_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btniCloudCancel.Click
        If tbMessageBox.Show(Me, Me.Language.GetStringTrim("iCloud.CancelDownload.Message"), _
                             Me.Language.GetStringTrim("Common.Info"), _
                             MessageBoxButtons.OKCancel, _
                             MessageBoxIcon.Question, _
                             MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then

            ManagerFactory.Instance.IsCancel = True
            Me.ShowiCloudPanel()
            Me.ShowiCloudLoginText(True)
        End If
    End Sub

    Private Sub btniCloudLogout_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btniCloudLogout.Click
        If tbMessageBox.Show(Me, Me.Language.GetStringTrim("iCloud.Logout.Message"), _
                             Me.Language.GetStringTrim("Common.Info"), _
                             MessageBoxButtons.OKCancel, _
                             MessageBoxIcon.Question, _
                             MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then

            m_enmCurrentStatus = iCloudDisplayStatus.kInitialStatus

            Me.ShowiCloudPanel()
            Me.ShowiCloudLoginText(True)
            Me.CleariCloud()
        End If
    End Sub

    Private Sub btniCloudReturn_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btniCloudReturn.Click
        m_enmCurrentStatus = iCloudDisplayStatus.kShowGridviewStatus

        Me.ShowiCloudDownStatus(True)
        Me.ShowiCloudGridView(True)
        Me.ShowiCloudNoData(False)
        Me.ShowiCloudDownLoadiCloud(False)

    End Sub
    Private Sub btniCloudForget_Click(ByVal sender As Object, ByVal e As EventArgs) Handles btniCloudForget.Click
        Common.OpenExplorer("https://iforgot.apple.com/password/verify/appleid")
    End Sub

    Private Sub btniCloudWechat_Click(ByVal sender As Object, ByVal e As EventArgs)
        Common.OpenExplorer("http://news.tongbu.com/95256.html")
    End Sub

    Private Sub pbShowPwd_Click(sender As Object, e As EventArgs) Handles pbShowPwd.Click
        If m_bLogining Then
            pbShowPwd.Image = My.Resources.btn_closedeyenew
            m_bLogining = False
            If String.IsNullOrEmpty(txtPwd.Text) = False Then
                txtPwd.PasswordChar = "*"
            End If

        Else
            pbShowPwd.Image = My.Resources.btn_opendeyenew
            m_bLogining = True
            txtPwd.PasswordChar = ""
        End If
    End Sub

    Private Sub txtPwd_GotFocus(ByVal sender As Object, ByVal e As EventArgs) Handles txtPwd.GotFocus
        If Not m_bLogining Then
            txtPwd.PasswordChar = "*"
        End If
    End Sub

    Private Sub txtPwd_LostFocus(ByVal sender As Object, ByVal e As System.EventArgs) Handles txtPwd.LostFocus
        If Not m_bLogining AndAlso txtPwd.Text.Length = 0 Then
            txtPwd.PasswordChar = ""
        End If
    End Sub

    Private Sub icloudLogin_RemoteLogin(ByVal status As ExceptionCode, ByVal icloudInfo As iCloudLoginInfo)
        Dim errmsg As String = ""
        If status = ExceptionCode.kSuccess AndAlso icloudInfo IsNot Nothing Then

            m_enmCurrentStatus = iCloudDisplayStatus.kGetiCloudStatus

            Me.ShowiCloudPanel()
            Me.ShowiCloudWaiting(True)
            Me.ShowiCloudFailture(False, errmsg)
            Me.ShowiCloudAppleId(icloudInfo.AppleId)

            RemoveHandler icloudInfo.DownloadingDeviceBackupEvent, AddressOf LoginInfo_DownloadingDeviceBackupEvent
            AddHandler icloudInfo.DownloadingDeviceBackupEvent, AddressOf LoginInfo_DownloadingDeviceBackupEvent

            Dim thd As New Thread(New ParameterizedThreadStart(AddressOf BackupedDevices))
            thd.Start(New Object() {icloudInfo.GetiCloudBackup(), icloudInfo})
        Else

            m_enmCurrentStatus = iCloudDisplayStatus.kInitialStatus

            Me.ShowiCloudPanel()
            Me.ShowiCloudLoginText(True)

            Select Case status
                Case ExceptionCode.kNotNetwork
                    errmsg = "连接苹果服务器失败！"
                Case ExceptionCode.kConflictError
                    errmsg = "身份验证失败！因为您在iCloud上开启了两步验证或双重验证功能，请关掉它，再试一次。"
                Case ExceptionCode.kUnauthorizedError
                    errmsg = "您的苹果账号或者密码错误，请确认之后再尝试。"
                Case ExceptionCode.kTimeoutError
                    errmsg = "远程服务器返回错误。"
            End Select

            Me.ShowiCloudFailture(True, errmsg)
        End If

    End Sub

    Private Sub BackupedDevices(ByVal status As Object)
        If status IsNot Nothing Then

            Dim aryResults As Object() = CType(status, Object())
            If aryResults.Length = 2 Then
                'Dim dictDevices As Dictionary(Of Object, Object) = CType(aryResults(0), Dictionary(Of Object, Object))

                m_iCloudBackup.LoginInfo = New iCloudLoginInfo()
                m_iCloudBackup.LoginInfo.Clone(CType(aryResults(1), iCloudLoginInfo))

                ''调用另外方法读取iCloud备份数据
                m_iCloudBackup.GetDeviceBackupList()
            End If
        End If
    End Sub

    Private Sub AddResults(ByVal deviceItem As DeviceBackup, ByVal bkp As Backup, ByVal icloudItem As iCloudCopy)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of DeviceBackup, Backup, iCloudCopy)(AddressOf AddResults), deviceItem, bkp, icloudItem)
        Else
            Dim aryResults As Object() = Nothing
            Dim viewRow As New tbDataGridViewRow()

            Dim serial_backupdate As String = ""

            If deviceItem IsNot Nothing AndAlso bkp IsNot Nothing Then
                aryResults = New Object() {deviceItem.DeviceName, _
                                       deviceItem.BackupDateTime, _
                                       deviceItem.BackupSizeString, _
                                       deviceItem.ProductVersion, _
                                       deviceItem.ProductName, _
                                       ""}
                serial_backupdate = String.Format("{0}{1}", deviceItem.SerialNumber, _
                                                  CustomUtils.GetDateTimeFromTimeStamp2001(deviceItem.CreateTime).ToString(iCloudDateTime.y4MdHms))

            ElseIf icloudItem IsNot Nothing Then
                aryResults = New Object() {icloudItem.DeviceName, _
                                           icloudItem.BackupDateTime, _
                                           icloudItem.BackupSizeString, _
                                           icloudItem.ProductVersion, _
                                           icloudItem.ProductName, _
                                         ""}

                serial_backupdate = String.Format("{0}{1}", icloudItem.SerialNumber, _
                                                  CustomUtils.GetDateTimeFromTimeStamp1970(icloudItem.LastModified).ToString(iCloudDateTime.y4MdHms))
            End If

            If aryResults IsNot Nothing Then
                With m_iCloudGridView
                    .SuspendLayout()

                    viewRow.Height = 58
                    viewRow.CreateCells(m_iCloudGridView, aryResults)
                    If deviceItem IsNot Nothing AndAlso bkp IsNot Nothing Then
                        viewRow.Tag = New Object() {deviceItem, bkp}
                    ElseIf icloudItem IsNot Nothing Then
                        viewRow.Tag = icloudItem
                    End If

                    .Rows.Add(viewRow)
                    .ResumeLayout()
                End With

                Dim cellProgressEx As tbDataGridViewProgressCellEx = viewRow.Cells("colProgress")
                With cellProgressEx
                    .Enabled = True
                    .tbProgressStyle = ProgressStyle.Button
                    cellProgressEx.Tag = "Download"
                    .tbShowCancelButton = False
                    .tbShowButtonFirst = True
                    .tbShowButtonSecond = False
                    .tbShowButtonThird = False
                    .tbCellButtonEnable = CellButtonStyle.Backup Or CellButtonStyle.Uninstall
                End With

                Me.DisplayCellProgressEx(viewRow, HasDownloadItem(serial_backupdate))
            End If

            Me.lbliCloudErrorServer.Visible = (aryResults Is Nothing)
        End If
    End Sub

    Private Function HasDownloadItem(ByVal serial_backupdate As String)
        Dim fName As String = Path.Combine(Folder.iCloudBackupFolder, "icloud.plist")
        If File.Exists(fName) Then
            Dim objTemp As Object = iTong.Device.CoreFoundation.ReadPlist_managed(fName)
            If objTemp IsNot Nothing Then
                Dim dictTemps As Dictionary(Of Object, Object) = CType(objTemp, Dictionary(Of Object, Object))
                If dictTemps.ContainsKey(serial_backupdate) Then
                    Dim dictiClouds As Dictionary(Of Object, Object) = CType(dictTemps(serial_backupdate), Dictionary(Of Object, Object))

                    Dim strDir As String = dictiClouds("BackupFolder").ToString()
                    Dim bIsDeleted As Boolean = CType(dictiClouds("IsDeleted"), Boolean)
                    If strDir.Length > 0 AndAlso Directory.Exists(strDir) AndAlso Not bIsDeleted Then
                        Return True
                    End If
                End If
            End If

        End If

        Return False
    End Function

    Private Function GetiCloudsPlist(ByVal appleId As String, _
                                     ByVal bkpfolder As String, _
                                     ByVal bkpdate As String, _
                                     ByVal serialNumber As String, _
                                     ByVal bIsDeleted As Boolean) As Dictionary(Of Object, Object)
        Dim dictTemps As New Dictionary(Of Object, Object)
        dictTemps.Add("AppleId", appleId)
        dictTemps.Add("BackupFolder", bkpfolder)
        dictTemps.Add("BackupDate", bkpdate)
        dictTemps.Add("SerialNumber", serialNumber)
        dictTemps.Add("IsDeleted", bIsDeleted)


        Return dictTemps
    End Function
    Private Sub WriteDownloadItems(ByVal row As DataGridViewRow, Optional ByVal bIsDeleted As Boolean = False)
        If row Is Nothing OrElse row.Tag Is Nothing Then
            Return
        End If

        Dim fName As String = Path.Combine(Folder.iCloudBackupFolder, "icloud.plist")
        Dim dictiClouds As Dictionary(Of Object, Object) = Nothing

        If File.Exists(fName) Then
            Dim objTemp As Object = iTong.Device.CoreFoundation.ReadPlist_managed(fName)
            If objTemp IsNot Nothing Then
                dictiClouds = CType(objTemp, Dictionary(Of Object, Object))
            End If

            CustomUtils.DeleteFile(fName)
        End If

        Dim bkpfolder As String = ""
        Dim appleId As String = ""
        Dim serialNumber As String = ""
        Dim bkpdate As String = ""
        Dim serial_backupdate As String = ""

        If TypeOf (row.Tag) Is Object() Then
            Dim aryTags As Object() = CType(row.Tag, Object())
            Dim bkpItem As DeviceBackup = CType(aryTags(0), DeviceBackup)

            serial_backupdate = String.Format("{0}{1}", bkpItem.SerialNumber, _
                                              CustomUtils.GetDateTimeFromTimeStamp2001(bkpItem.CreateTime).ToString(iCloudDateTime.y4MdHms))

            bkpfolder = Path.Combine(Path.Combine(Folder.iCloudBackupFolder, bkpItem.AppleId), serial_backupdate)
            bkpdate = bkpItem.BackupDateTime
            appleId = bkpItem.AppleId
            serialNumber = bkpItem.SerialNumber

        ElseIf TypeOf (row.Tag) Is iCloudCopy Then
            Dim bkpItem As iCloudCopy = CType(row.Tag, iCloudCopy)
            serial_backupdate = String.Format("{0}{1}", bkpItem.SerialNumber, _
                                              CustomUtils.GetDateTimeFromTimeStamp1970(bkpItem.LastModified).ToString(iCloudDateTime.y4MdHms))
            bkpfolder = Path.Combine(Path.Combine(Folder.iCloudBackupFolder, bkpItem.AppleId), serial_backupdate)
            bkpdate = bkpItem.BackupDateTime
            appleId = bkpItem.AppleId
            serialNumber = bkpItem.SerialNumber

        End If

        If serialNumber.Length > 0 Then
            If dictiClouds Is Nothing Then
                dictiClouds = New Dictionary(Of Object, Object)

                dictiClouds.Add(serial_backupdate, _
                                GetiCloudsPlist(appleId, bkpfolder, bkpdate, serialNumber, bIsDeleted))
            Else
                Dim bIsNotHave As Boolean = False
                For i As Integer = 0 To dictiClouds.Count
                    If Not dictiClouds.ContainsKey(serial_backupdate) Then
                        bIsNotHave = True
                        Exit For
                    End If
                Next

                If bIsNotHave Then
                    dictiClouds.Add(serial_backupdate, _
                                    GetiCloudsPlist(appleId, bkpfolder, bkpdate, serialNumber, bIsDeleted))
                ElseIf bIsDeleted Then
                    CType(dictiClouds(serial_backupdate), Dictionary(Of Object, Object))("IsDeleted") = bIsDeleted
                End If

            End If

            iTong.Device.CoreFoundation.WritePlist(dictiClouds, fName)
        End If

    End Sub

    Private Sub DisplayCellProgressEx(ByVal selectedRow As tbDataGridViewRow, Optional ByVal bIsDeleted As Boolean = False)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of tbDataGridViewRow, Boolean)(AddressOf DisplayCellProgressEx), selectedRow, bIsDeleted)
        Else
            If selectedRow IsNot Nothing Then

                Dim cellProgressEx As tbDataGridViewProgressCellEx = selectedRow.Cells("colProgress")
                If bIsDeleted Then
                    cellProgressEx.tbTextBackup = Me.Language.GetString("Photo.Menu.View")                  '"查看"
                    cellProgressEx.tbShowButtonSecond = True
                    cellProgressEx.tbTextUnInstall = Me.Language.GetString("Common.Delete") ' "删除"
                    cellProgressEx.Tag = "View"
                Else
                    cellProgressEx.tbTextBackup = Me.Language.GetStringTrim("Site.Label.Download")
                    cellProgressEx.tbShowButtonSecond = False
                    cellProgressEx.Tag = "Download"
                End If
            End If
        End If
    End Sub
    Private Sub LoginInfo_DownloadingDeviceBackupEvent(ByVal item As DeviceBackup, ByVal bkp As Backup)
        If Me.InvokeRequired Then
            Me.Invoke(New MethodUtil(Of DeviceBackup, Backup)(AddressOf LoginInfo_DownloadingDeviceBackupEvent), New Object() {item, bkp})
        Else
            m_enmCurrentStatus = iCloudDisplayStatus.kGetiCloudStatus
            Me.AddResults(item, bkp, Nothing)
        End If
    End Sub

    Private Sub LoadiCloudWechat(ByVal bkpInfo As BackupInfo, ByVal encryptedBackupPath As String)
        Me.mCurrentSelectBackupInfo = bkpInfo
        SetLoadFromBackup()

        Me.mChargeHelper.SetDeviceRegistedLocal(bkpInfo.Identifier, bkpInfo.SerialNumber, bkpInfo.ProductType, bkpInfo.DeviceName)

        Dim frm As New frmWeixin(Me.mApplication, Nothing, bkpInfo, IMAppType.WeChat, "", Me, Me.mABType)
        RemoveHandler frm.DisplayNoDataHandler, AddressOf DisplayNoDataHandler
        AddHandler frm.DisplayNoDataHandler, AddressOf DisplayNoDataHandler

        frm.Name = "iCloudWechat"
        frm.IsFromWeixinMain = True
        frm.IsiCloud = True
        Utility.AddForm2Panel(frm, Me.pnlContent, True)
        Me.SetViewStyle(WeixinMainViewStyle.Content, False, True)
    End Sub

    Private Sub DisplayNoDataHandler()
        Me.SetViewStyle(WeixinMainViewStyle.iCloudBackup)
        m_enmCurrentStatus = iCloudDisplayStatus.kNodataStatus

        Me.ShowiCloudGridView(False)
        Me.ShowiCloudDownLoadiCloud(False)
        Me.ShowiCloudNoData(True)

        If Me.pnlContent.Controls.Count > 0 Then
            Dim bIsWeixin As Boolean = False
            Dim i As Integer = 0
            For i = 0 To Me.pnlContent.Controls.Count
                If TypeOf (Me.pnlContent.Controls(i)) Is frmWeixin Then
                    CType(Me.pnlContent.Controls(i), frmWeixin).Close()

                    Exit For
                End If
            Next
        End If
    End Sub
    '#End If


    Private Sub lbl_SizeChanged(sender As Object, e As EventArgs) Handles lbliCloudTip_2.SizeChanged, lbliCloudTip_1.SizeChanged, btniCloudView.SizeChanged, btniCloudPrivate.SizeChanged

        lbliCloudTip_1.Location = New Point((btniCloudView.Size.Width + lbliCloudTip_1.Size.Width) / 2, lbliCloudTip_1.Location.Y)

        btniCloudView.Location = New Point(Me.lbliCloudTip_1.Left + Me.lbliCloudTip_1.Size.Width, Me.lbliCloudTip_1.Location.Y)
        btniCloudPrivate.Location = New Point(Me.lbliCloudTip_2.Left + Me.lbliCloudTip_2.Size.Width, Me.lbliCloudTip_2.Location.Y)
    End Sub

    Private Sub lbliCloudWechat_SizeChange(sender As Object, e As EventArgs) Handles lbliCloudWechat.SizeChange
        lbliCloudWechat.Location = New Point((pnliCloudNodata.Size.Width - lbliCloudWechat.Size.Width) / 2, lbliCloudWechat.Location.Y)
    End Sub

    Private Sub btniCloudPrivate_Click(sender As Object, e As EventArgs) Handles btniCloudPrivate.Click

    End Sub

    Private Sub btniCloudView_Click(sender As Object, e As EventArgs) Handles btniCloudView.Click

    End Sub
End Class



