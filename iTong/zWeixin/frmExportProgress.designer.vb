﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmExportProgress
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmExportProgress))
        Me.lblProgress = New System.Windows.Forms.Label()
        Me.prgExport = New iTong.Components.tbControlBar()
        Me.lblIntProgress = New System.Windows.Forms.Label()
        Me.btnClose1 = New iTong.Components.tbButton()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(378, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(354, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(330, 0)
        '
        'lblProgress
        '
        Me.lblProgress.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblProgress.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblProgress.Location = New System.Drawing.Point(30, 73)
        Me.lblProgress.Name = "lblProgress"
        Me.lblProgress.Size = New System.Drawing.Size(280, 20)
        Me.lblProgress.TabIndex = 0
        Me.lblProgress.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'prgExport
        '
        Me.prgExport.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.prgExport.BackColor = System.Drawing.Color.Transparent
        Me.prgExport.Font = New System.Drawing.Font("Microsoft Sans Serif", 8.0!)
        Me.prgExport.Location = New System.Drawing.Point(30, 104)
        Me.prgExport.Name = "prgExport"
        Me.prgExport.Size = New System.Drawing.Size(348, 8)
        Me.prgExport.TabIndex = 46
        Me.prgExport.tbBackgroundImage = CType(resources.GetObject("prgExport.tbBackgroundImage"), System.Drawing.Image)
        Me.prgExport.tbCanDragValue = False
        Me.prgExport.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.prgExport.tbDotImage = CType(resources.GetObject("prgExport.tbDotImage"), System.Drawing.Image)
        Me.prgExport.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.prgExport.tbDotMouseDown = Nothing
        Me.prgExport.tbDotMouseHover = Nothing
        Me.prgExport.tbDotMouseLeave = Nothing
        Me.prgExport.tbDownloadImage = Nothing
        Me.prgExport.tbDownMax = 100
        Me.prgExport.tbDownMin = 0
        Me.prgExport.tbDownValue = 0
        Me.prgExport.tbIsWaiting = False
        Me.prgExport.tbPlayImage = Global.iTong.My.Resources.Resources.app_progress_value_export
        Me.prgExport.tbPlayMax = 100
        Me.prgExport.tbPlayMin = 0
        Me.prgExport.tbPlayValue = 0
        Me.prgExport.tbShowDot = False
        Me.prgExport.tbShowText = False
        Me.prgExport.tbSplit = "7,0,7,0"
        Me.prgExport.Text = "TbControlBar1"
        '
        'lblIntProgress
        '
        Me.lblIntProgress.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblIntProgress.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblIntProgress.Location = New System.Drawing.Point(318, 73)
        Me.lblIntProgress.Name = "lblIntProgress"
        Me.lblIntProgress.Size = New System.Drawing.Size(56, 20)
        Me.lblIntProgress.TabIndex = 0
        Me.lblIntProgress.Text = "0%"
        Me.lblIntProgress.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'btnClose1
        '
        Me.btnClose1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnClose1.BackColor = System.Drawing.Color.Transparent
        Me.btnClose1.BindingForm = Nothing
        Me.btnClose1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnClose1.Location = New System.Drawing.Point(378, 0)
        Me.btnClose1.Name = "btnClose1"
        Me.btnClose1.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnClose1.Selectable = True
        Me.btnClose1.Size = New System.Drawing.Size(24, 24)
        Me.btnClose1.TabIndex = 47
        Me.btnClose1.tbAdriftIconWhenHover = False
        Me.btnClose1.tbAutoSize = False
        Me.btnClose1.tbAutoSizeEx = False
        Me.btnClose1.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close1
        Me.btnClose1.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnClose1.tbBadgeNumber = 0
        Me.btnClose1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnClose1.tbEndEllipsis = False
        Me.btnClose1.tbIconHoldPlace = True
        Me.btnClose1.tbIconImage = Nothing
        Me.btnClose1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnClose1.tbIconMore = False
        Me.btnClose1.tbIconMouseDown = Nothing
        Me.btnClose1.tbIconMouseHover = Nothing
        Me.btnClose1.tbIconMouseLeave = Nothing
        Me.btnClose1.tbIconPlaceText = 2
        Me.btnClose1.tbIconReadOnly = Nothing
        Me.btnClose1.tbImageMouseDown = Nothing
        Me.btnClose1.tbImageMouseHover = Nothing
        Me.btnClose1.tbImageMouseLeave = Nothing
        Me.btnClose1.tbProgressValue = 50
        Me.btnClose1.tbReadOnly = False
        Me.btnClose1.tbReadOnlyText = False
        Me.btnClose1.tbShadow = False
        Me.btnClose1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnClose1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnClose1.tbShowDot = False
        Me.btnClose1.tbShowMoreIconImg = CType(resources.GetObject("btnClose1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnClose1.tbShowNew = False
        Me.btnClose1.tbShowProgress = False
        Me.btnClose1.tbShowTip = True
        Me.btnClose1.tbShowToolTipOnButton = False
        Me.btnClose1.tbSplit = "3,3,3,3"
        Me.btnClose1.tbText = ""
        Me.btnClose1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose1.tbTextColor = System.Drawing.Color.White
        Me.btnClose1.tbTextColorDisable = System.Drawing.Color.White
        Me.btnClose1.tbTextColorDown = System.Drawing.Color.White
        Me.btnClose1.tbTextColorHover = System.Drawing.Color.White
        Me.btnClose1.tbTextMouseDownPlace = 0
        Me.btnClose1.tbToolTip = ""
        Me.btnClose1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnClose1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnClose1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnClose1.VisibleEx = True
        '
        'frmExportProgress
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.White
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(402, 177)
        Me.Controls.Add(Me.btnClose1)
        Me.Controls.Add(Me.prgExport)
        Me.Controls.Add(Me.lblIntProgress)
        Me.Controls.Add(Me.lblProgress)
        Me.MinimizeBox = False
        Me.Name = "frmExportProgress"
        Me.ShowIcon = False
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_state1
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "3,35,3,60"
        Me.tbTitleForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "Export"
        Me.Controls.SetChildIndex(Me.lblProgress, 0)
        Me.Controls.SetChildIndex(Me.lblIntProgress, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.prgExport, 0)
        Me.Controls.SetChildIndex(Me.btnClose1, 0)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents lblProgress As System.Windows.Forms.Label
    Friend WithEvents prgExport As iTong.Components.tbControlBar
    Friend WithEvents lblIntProgress As System.Windows.Forms.Label
    Friend WithEvents btnClose1 As iTong.Components.tbButton
End Class
