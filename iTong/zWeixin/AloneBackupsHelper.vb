﻿Imports System.Threading

Public Class AloneBackupsHelper
    Implements IDisposable

    Private mLanguage As LanguageInterface = Nothing
    Private mBackupXml As String = Path.Combine(Folder.BackupFolder, "BackupPathList.xml")
    Private mLockLoadAloneBackup As New Object()
    Private mQueueLoadAloneBackup As New Queue(Of LoadAloneBackupInfo)()
    Private mIsLoadAloneBackup As Boolean = False
    Private mTdLoadAloneBackup As Thread = Nothing
    Private mDictBackupConverting As New Dictionary(Of AloneBackupType, List(Of String))
    Private mDictBackupConverted As New Dictionary(Of AloneBackupType, List(Of String))
    Private mListBackup2AloneBackup As New List(Of Backup2AloneBackupInfo)()
    Private mLastType As AloneBackupType = AloneBackupType.None         '记住最后操作的行为，是微信还是微信分身版，还是qq等

    Public mDictAloneBackupList As New Dictionary(Of AloneBackupType, List(Of BackupInfo))
    Private mIsBackup2AloneBackup As Boolean = False

    Public Event AloneBackupHandler(ByVal sender As Object, ByVal args As AloneBackupsEventArgs) '单独备份进度回调
    Public Event CheckAloneBackupsHandler(ByVal sender As Object, ByVal args As CheckAloneBackupEventArgs) '按钮是否可以启用的进度回调
    Public Event LoadAloneBackupHandler(ByVal sender As Object, ByVal args As LoadAloneBackupEventArgs) '转换检测前的错误回调

    Private mCurrentAloneBackupABType As AloneBackupType = AloneBackupType.None
    Private mCurrentBackup2AloneBackupABType As AloneBackupType = AloneBackupType.None
    Private mCurrentBackup2AloneBackupFolder As String = ""

    Private mDictBackup2AloneBackupSuccessCount As New Dictionary(Of AloneBackupType, Integer)
    Private mDictBackup2AloneBackupFailureCount As New Dictionary(Of AloneBackupType, Integer)

    Private mIsIniData As Boolean = False
    Private mAllABTypeTask As New List(Of AloneBackupType)
    Private mDictAllBackupConverting As New Dictionary(Of AloneBackupType, List(Of String))
    Private mDictAllBackup2AloneBackup As New Dictionary(Of AloneBackupType, List(Of Backup2AloneBackupInfo))

#Region "--- 单例 ---"
    Private Shared mDictInstances As New Dictionary(Of String, AloneBackupsHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New()
        Me.mLanguage = LanguageInterface.Instance()

        Me.mAllABTypeTask.Clear()
        Me.mAllABTypeTask.Add(AloneBackupType.WeChat)
        Me.mAllABTypeTask.Add(AloneBackupType.WeChatSpare)
        Me.mAllABTypeTask.Add(AloneBackupType.WeChatVIPSpare)

    End Sub

    Private Shared mHelper As AloneBackupsHelper = Nothing

    Public Shared Function GetInstance() As AloneBackupsHelper
        Dim helper As AloneBackupsHelper = Nothing
        Dim blnRetry As Boolean = False

        SyncLock _lockGetInstance
DO_RETRY:
            Try
                If Not mDictInstances.ContainsKey(NoDevcieIdentifier) Then
                    mDictInstances.Add(NoDevcieIdentifier, New AloneBackupsHelper())
                End If
                helper = mDictInstances.Item(NoDevcieIdentifier)
            Catch ex As Exception
                If Not blnRetry Then
                    blnRetry = True
                    GoTo DO_RETRY
                End If
            End Try
        End SyncLock
        Return helper
    End Function

#End Region

#Region "--- 释放 ---"

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
                mDictInstances.Clear()

                Try
                    If Me.mTdLoadAloneBackup IsNot Nothing AndAlso Me.mTdLoadAloneBackup.ThreadState <> ThreadState.Stopped Then
                        Me.mTdLoadAloneBackup.Abort()
                        Me.mTdLoadAloneBackup = Nothing
                    End If
                Catch
                End Try
                Try
                    Me.mQueueLoadAloneBackup.Clear()
                Catch
                End Try
            End If
        End If
        disposedValue = True
    End Sub

#End Region

#Region "--- 对外方法 ---"

    '单独备份
    Public  Function AloneBackup(ByVal strBackupCache As String, ByVal abType As AloneBackupType, ByVal CurrentDevice As iPhoneDevice) As Boolean
        Dim isResult As Boolean = False
        Try
            While QueryConvert(CurrentDevice.Identifier, abType)
                RaiseEvent AloneBackupHandler(Me, New AloneBackupsEventArgs(0, BackupRestoreErrorType.ConvertBackup, strBackupCache, String.Empty, abType))
                Utility.WaitSeconds(0.5)
            End While

            Dim strSKU As String = ""
            GetQuickInfoByABType(abType, strSKU, "", "")

            If strBackupCache.Length = 0 Then
                'strBackupCache = GetBackupFolder(abType, CurrentDevice.Identifier)
            End If

            Me.mIntProgress = 0
            Dim callback As New BackupRestoreHandler(AddressOf mb2_Backup_BackupRestore)
            Me.mCurrentAloneBackupABType = abType
            isResult = CurrentDevice.mb2_Backup_DB(strBackupCache, callback, BackupType.Backup_Weixin, strSKU)
            Me.mCurrentAloneBackupABType = AloneBackupType.None
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AloneBackup_DoBackup")
        End Try
        Return isResult
    End Function

    ' 加载单独备份还原、整机备份转单独备份    
    Public Sub LoadAloneBackup(ByVal abType As AloneBackupType, ByVal strIdentifier As String)
        Dim isAddLast As Boolean = False
        If Me.mIsLoadAloneBackup Then
            If Me.mLastType <> abType Then
                Me.mLastType = abType
                isAddLast = True
            End If
            Me.QueueLoadAloneBackup(New LoadAloneBackupInfo(abType, strIdentifier, isAddLast))
            Return
        End If
        Me.mLastType = abType
        Me.QueueLoadAloneBackup(New LoadAloneBackupInfo(abType, strIdentifier, isAddLast))

        Try
            If Me.mTdLoadAloneBackup IsNot Nothing AndAlso Me.mTdLoadAloneBackup.ThreadState <> ThreadState.Stopped Then
                Return
            End If
            Me.mIsLoadAloneBackup = True
            Me.mTdLoadAloneBackup = New Thread(AddressOf DoLoadAloneBackup)
            Me.mTdLoadAloneBackup.IsBackground = True
            Me.mTdLoadAloneBackup.Start()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadAloneBackup")
        End Try
    End Sub

    '判断是否在转换队列中
    Public Function ExistInAloneBackupConverting(strIdentifier As String, abType As AloneBackupType, Optional isEvent As Boolean = True, Optional isInWaitConvert As Boolean = False) As Boolean
        Dim isRelust As Boolean = False
        If Not Me.mIsIniData Then
            GoTo Do_Exit
        End If
        Try
            '判断是否在本地微信单独列表里面   只判断是否在待转换列表这边不需要执行 （isInWaitConvert=true）
            If Not isInWaitConvert AndAlso Me.mDictAloneBackupList.ContainsKey(abType) Then
                Dim lstTemp As List(Of BackupInfo) = Me.mDictAloneBackupList(abType)
                For Each info As BackupInfo In lstTemp
                    If info.Identifier = strIdentifier Then
                        isRelust = True
                        Exit For
                    End If
                Next
            End If

            '判断是否在已经转换过的列表中
            If Not isRelust AndAlso Me.mDictAllBackupConverting.ContainsKey(abType) Then
                Dim lstTemp As List(Of String) = Me.mDictAllBackupConverting(abType)
                For Each item As String In lstTemp
                    If item.Contains(strIdentifier) Then
                        isRelust = True
                        Exit For
                    End If
                Next
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExistInAloneBackupConverting")
        End Try
        If isEvent Then
            RaiseEvent CheckAloneBackupsHandler(Me, New CheckAloneBackupEventArgs(True, isRelust, abType))
        End If
Do_Exit:
        Return isRelust
    End Function

    '获取一个新的备份路径

    '    Public Shared Function GetBackupFolder(abType As AloneBackupType, strIdf As String, Optional ByVal isDefault As Boolean = True, Optional ByVal isNewBackup As Boolean = False, Optional ByVal isAddTemp As Boolean = False) As String

    '        Dim strBackupFolder As String = ""
    '        GetQuickInfoByABType(abType, "", strBackupFolder, "")

    '        Dim strFolder As String = Path.Combine(strBackupFolder, strIdf)
    '        If Not isDefault Then
    '            strFolder = ""
    '        End If

    '        Try
    '            Dim arrDirectoryInfo As DirectoryInfo() = New DirectoryInfo(strBackupFolder).GetDirectories()
    '            If arrDirectoryInfo.Length = 0 Then
    '                GoTo Do_Exit
    '            End If

    '            Dim isExist As Boolean = False
    '            For Each dInfo As DirectoryInfo In arrDirectoryInfo
    '                If dInfo.Name.Contains(strIdf) Then
    '                    isExist = True
    '                    Exit For
    '                End If
    '            Next

    '            If Not isAddTemp AndAlso Not isExist Then
    '                '如果从未备份的
    '                strBackupFolder = ""
    '                GoTo Do_Exit
    '            End If

    '            If isNewBackup Then
    '                Dim strTempFolder As String = Path.Combine(strBackupFolder, strIdf)
    '                Dim intIndex As Integer = 0
    '                While True
    '                    If Directory.Exists(strTempFolder) Then
    '                        intIndex += 1
    '                        strTempFolder = Path.Combine(strBackupFolder, strIdf & String.Format("({0})", intIndex))
    '                    Else
    '                        Exit While
    '                    End If
    '                End While
    '                strFolder = strTempFolder

    '            Else
    '                Dim dtTime As DateTime = DateTime.MinValue
    '                For Each item As DirectoryInfo In arrDirectoryInfo
    '                    If item.Name.Contains(strIdf) Then
    '                        Dim strStatusPath As String = Path.Combine(item.FullName, "Status.Plist")
    '                        If Not File.Exists(strStatusPath) Then
    '                            Continue For
    '                        End If
    '                        If DateTime.Compare(dtTime, New FileInfo(strStatusPath).LastWriteTime) > 0 Then
    '                            Continue For
    '                        End If
    '                        dtTime = New FileInfo(strStatusPath).LastWriteTime
    '                        strFolder = item.FullName
    '                    End If
    '                Next
    '                strFolder = arrDirectoryInfo(0).FullName
    '            End If
    '        Catch ex As Exception
    '            Common.LogException(ex.ToString(), "GetBackupFolder")
    '        End Try
    'Do_Exit:
    '        Try
    '            If strFolder.Length > 0 Then
    '                Folder.CheckFolder(strFolder)
    '            End If
    '        Catch ex As Exception
    '            Common.LogException(ex.ToString(), "GetBackupFolder_1")
    '        End Try

    '        Return strFolder
    '    End Function

    '根据类型获取 SKU 单独备份保存的目录 以及配置ini文件路径
    Public Shared Sub GetQuickInfoByABType(ByVal abType As AloneBackupType, ByRef strSku As String, ByRef strBackupFolder As String, ByRef strAloneBackupIniFile As String, Optional ByVal strMigrationSku As String = "")
        Try
            Select Case abType
                Case AloneBackupType.WeChat
                    strSku = "com.tencent.xin"
                    strBackupFolder = Folder.AloneWeChatBackupsFolder
                    strAloneBackupIniFile = Folder.AloneBackupWeChatIniFile

                Case AloneBackupType.WeChatSpare
                    strSku = ServerIniSetting.GetWeChatSpareSKU
                    strBackupFolder = Folder.AloneWeChatSpareBackupsFolder
                    strAloneBackupIniFile = Folder.AloneBackupWeChatSpareIniFile

                Case AloneBackupType.WeChatVIPSpare
                    strSku = PremiumFeatureHelper.GetInstance.CPremiumFeatureInfo.LstSku(0)
                    strBackupFolder = Folder.AloneWeChatVIPSpareBackupsFolder
                    strAloneBackupIniFile = Folder.AloneBackupWeChatVIPSpareIniFile

                Case AloneBackupType.WeChatPayMigration
                    strSku = strMigrationSku
                    strBackupFolder = Folder.WeChatPayMigrationBackupsFolder
                    strAloneBackupIniFile = Folder.WeChatPayMigrationBackupsFolder


            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSKUAndBackupFolderByABType")
        End Try
        Try
            If strBackupFolder.Length > 0 Then
                Folder.CheckFolder(strBackupFolder)
            End If
        Catch ex As Exception

        End Try
    End Sub

    Public Function GetBackupInfosByABType(ByVal abType As AloneBackupType, Optional ByVal isEvent As Boolean = True) As List(Of BackupInfo)
        Dim lstTemp As New List(Of BackupInfo)
        Try
            If Not mIsIniData Then
                GoTo Do_Exit
            End If
            Try
                If Me.mDictAloneBackupList.ContainsKey(abType) Then
                    lstTemp = Me.mDictAloneBackupList(abType)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "GetBackupInfosByABType")
            End Try

            If isEvent Then
                Dim eArgs As New LoadAloneBackupEventArgs(LoadAloneBackupType.AloneBackupList)
                eArgs.ABType = abType
                eArgs.LstAloneBackupList = lstTemp
                RaiseEvent LoadAloneBackupHandler(Me, eArgs)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetBackupInfosByABType_1")
        End Try

Do_Exit:
        Return lstTemp
    End Function

#End Region

#Region "--- 单独备份 ---"

    Private Function QueryConvert(strIdentifier As String, abType As AloneBackupType) As Boolean
        Dim isResult As Boolean = False
        '判断是否在转换列表
        Dim isConvert As Boolean = Me.ExistInAloneBackupConverting(strIdentifier, abType, False, True)
        '如果在转换列表 判断是否转换完成
        If isConvert Then
            Dim isConverted As Boolean = False
            If Me.mDictBackupConverted.ContainsKey(abType) Then
                For Each item As String In Me.mDictBackupConverted(abType)
                    If item.Contains(strIdentifier) Then
                        isConverted = True
                        Exit For
                    End If
                Next
            End If
            If Not isConverted Then
                isResult = True
            End If
        End If
        Return isResult
    End Function

    Private mIntProgress As Integer = 0
    Private Sub mb2_Backup_BackupRestore(ByVal e As BackupRestoreEventArgs)
        Dim sType As BackupRestoreErrorType = BackupRestoreErrorType.None
        If e.ErrorMsg.Length > 0 Then
            Dim Args As New BackupRestoreEventArgs
            Args.ErrorMsg = e.ErrorMsg
            sType = BackupHelper.GetErrotType(BackupRestore.Backup, Args)
        End If
        Dim strProgress = CInt(e.Progress).ToString().PadLeft(2, " "c)
        If CInt(strProgress) < mIntProgress Then
            Return
        End If
        mIntProgress = CInt(strProgress)

        Dim strErrMsg As String = BackupHelper.GetErrorMessage(BackupRestore.Backup, sType)
        RaiseEvent AloneBackupHandler(Me, New AloneBackupsEventArgs(CInt(strProgress), sType, "", strErrMsg, Me.mCurrentAloneBackupABType))
    End Sub

#End Region

#Region "--- 加载单独备份还原、整机备份转单独备份 ---"

    Private Sub DoLoadAloneBackup()
        Try
            Dim strSku As String = ""
            Dim strBackupFolder As String = ""
            Dim strAloneBackupIniFile As String = ""

            Dim LABinfo As LoadAloneBackupInfo = Nothing
            Dim abType As AloneBackupType = AloneBackupType.None
            Dim strIdentifier As String = ""
            Dim isAddLast As Boolean = False

            '----------准备所有枚举的数据----------
            Me.mDictAloneBackupList.Clear()
            Me.mDictAllBackupConverting.Clear()
            Me.mDictAllBackup2AloneBackup.Clear()
            Me.mIsIniData = False
            For Each ABTypeTask As AloneBackupType In Me.mAllABTypeTask
                GetQuickInfoByABType(ABTypeTask, strSku, strBackupFolder, strAloneBackupIniFile)

                'a.已经转换的微信单独备份列表
                Me.GetAloneBackupList(ABTypeTask, strBackupFolder, strAloneBackupIniFile)

                'b.通过整机备份列表得到需要转换的列表
                Me.GetAllBackup2AloneBackup(ABTypeTask, strSku, strBackupFolder, strAloneBackupIniFile, strIdentifier)
            Next
            Me.mIsIniData = True
            '-----------------------------------

DoRetry:
            While Me.mQueueLoadAloneBackup.Count > 0
                Try
                    LABinfo = Me.QueueLoadAloneBackup(Nothing)
                    abType = LABinfo.ABType
                    strIdentifier = LABinfo.StrIdentifier
                    isAddLast = LABinfo.IsAddLast
                    GetQuickInfoByABType(abType, strSku, strBackupFolder, strAloneBackupIniFile)

                    'a.获取当前状态已经转换的微信单独备份列表
                    Me.GetBackupInfosByABType(abType)

                    'b.返回当前设备是否有存在 单独备份
                    Me.ExistInAloneBackupConverting(strIdentifier, abType, True)

                    'c.添加任务到执行转换列表
                    Me.AddBackup2AloneBackupByAllBackup2AloneBackup(abType, isAddLast)

                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoLoadAloneBackup_WhileQueueLoadAloneBackup")
                End Try
            End While

            '处理整机转单独, 从后面开始处理，才能移除处理过的信息
            If Me.mListBackup2AloneBackup.Count > 0 Then
                For index As Integer = Me.mListBackup2AloneBackup.Count - 1 To 0 Step -1
                    Try
                        Me.DoBackup2AloneBackup(Me.mListBackup2AloneBackup(index))
                        Me.mListBackup2AloneBackup.RemoveAt(index)

                        If Me.mQueueLoadAloneBackup.Count > 0 Then
                            GoTo DoRetry
                        End If
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "DoLoadAloneBackup_DoLoadAloneBackup")
                    End Try
                Next
                Dim eArgs As New LoadAloneBackupEventArgs(LoadAloneBackupType.Convert)
                eArgs.ABType = abType
                eArgs.BABStatus = Backup2AloneBackupStatus.DoEnd
                eArgs.IntSuccessCount = Me.SuccessOrFailureCount(abType, Me.mDictBackup2AloneBackupSuccessCount)
                eArgs.IntFailureCount = Me.SuccessOrFailureCount(abType, Me.mDictBackup2AloneBackupFailureCount)
                eArgs.IntTotalCount = Me.GetBackupConvertTotal(abType)
                RaiseEvent LoadAloneBackupHandler(Me, eArgs)

                Me.mDictBackup2AloneBackupSuccessCount.Clear()
                Me.mDictBackup2AloneBackupFailureCount.Clear()

            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoLoadAloneBackup")
        End Try
        Me.mIsLoadAloneBackup = False
    End Sub

    Private Function QueueLoadAloneBackup(ByVal item As LoadAloneBackupInfo) As LoadAloneBackupInfo
        Dim info As LoadAloneBackupInfo = Nothing
        SyncLock Me.mLockLoadAloneBackup
            If item Is Nothing Then
                info = Me.mQueueLoadAloneBackup.Dequeue()
            Else
                Me.mQueueLoadAloneBackup.Enqueue(item)
            End If
        End SyncLock
        Return info
    End Function

    Public Sub DoBackup2AloneBackup(BABInfo As Backup2AloneBackupInfo)
        Dim isReslue As Boolean = False
        Dim eArgs As New LoadAloneBackupEventArgs(LoadAloneBackupType.None)
        Try
            Dim info As BackupInfo = BABInfo.BInfo
            Dim abType As AloneBackupType = BABInfo.ABType

            Dim strBackupFolder As String = ""
            Dim strAloneBackupIniFile As String = ""
            Dim strSku As String = ""
            GetQuickInfoByABType(abType, strSku, strBackupFolder, strAloneBackupIniFile)

            Dim strBackupWeChatFolder As String = Path.Combine(strBackupFolder, info.Identifier)
            Dim iIndex As Integer = 0

            While True
                If Directory.Exists(strBackupWeChatFolder) Then
                    iIndex += 1
                    strBackupWeChatFolder = Path.Combine(strBackupFolder, info.Identifier & String.Format("({0})", iIndex))
                Else
                    Exit While
                End If
            End While

            Dim isFailureRetry As Boolean = False
            If IniClass.GetIniSectionKey("AloneBackupWeChat_Failure", info.FolderPath.ToLower(), strAloneBackupIniFile).Length > 0 Then
                isFailureRetry = True
            End If

            eArgs = New LoadAloneBackupEventArgs(LoadAloneBackupType.Convert)
            eArgs.ABType = abType
            eArgs.StrFolderPath = info.FolderPath
            eArgs.BABStatus = Backup2AloneBackupStatus.Start
            eArgs.IntSuccessCount = Me.SuccessOrFailureCount(abType, Me.mDictBackup2AloneBackupSuccessCount)
            eArgs.IntFailureCount = Me.SuccessOrFailureCount(abType, Me.mDictBackup2AloneBackupFailureCount)
            eArgs.IntTotalCount = Me.GetBackupConvertTotal(abType)
            RaiseEvent LoadAloneBackupHandler(Me, eArgs)

            Dim callback As New BackupRestoreHandler(AddressOf mb2_CopyBackupWeixinFiles)
            Dim statueReslue As CopyAppBackupStatus = CopyAppBackupStatus.None
            Me.mCurrentBackup2AloneBackupABType = abType
            Me.mCurrentBackup2AloneBackupFolder = info.FolderPath

            Common.LogException(String.Format("FolderPath:{0}  BackupWeChatFolder:{1}  strSku:{2}  ABType:{3}", info.FolderPath, strBackupWeChatFolder, strSku, abType), "转换日志")

            statueReslue = iPhoneDevice.mb2_CopyBackupFilesByIdentify(info.FolderPath, strBackupWeChatFolder, callback, ServerIniSetting.GetCopyAloneWeChatInterval(), info.ProductVersion, strSku)
            Me.mCurrentBackup2AloneBackupFolder = ""
            Me.mCurrentBackup2AloneBackupABType = AloneBackupType.None

            If statueReslue = CopyAppBackupStatus.Succeed Then
                isReslue = True
            End If

            '处理结果
            If isReslue Then
                Me.SuccessOrFailureCount(abType, Me.mDictBackup2AloneBackupSuccessCount, True)
                IniClass.SetIniSectionKey("AloneBackupWeChat", info.FolderPath.ToLower(), strBackupWeChatFolder.ToLower(), strAloneBackupIniFile)

                If isFailureRetry Then
                    IniClass.DeleteSectionKey("AloneBackupWeChat_Failure", info.FolderPath.ToLower(), strAloneBackupIniFile)
                End If

            Else
                Try
                    Directory.Delete(strBackupWeChatFolder, True)
                Catch
                End Try
                Me.SuccessOrFailureCount(abType, Me.mDictBackup2AloneBackupFailureCount, True)

                If Not isFailureRetry Then
                    IniClass.SetIniSectionKey("AloneBackupWeChat_Failure", info.FolderPath.ToLower(), statueReslue, strAloneBackupIniFile)
                End If

            End If

            If Not isFailureRetry Then
                eArgs = New LoadAloneBackupEventArgs(LoadAloneBackupType.Convert)
                eArgs.ABType = abType
                eArgs.StrFolderPath = IIf(isReslue, strBackupWeChatFolder, info.FolderPath)
                eArgs.BABStatus = IIf(isReslue, Backup2AloneBackupStatus.Success, Backup2AloneBackupStatus.Failure)
                eArgs.CBStatus = statueReslue
                eArgs.IntSuccessCount = Me.SuccessOrFailureCount(abType, Me.mDictBackup2AloneBackupSuccessCount)
                eArgs.IntFailureCount = Me.SuccessOrFailureCount(abType, Me.mDictBackup2AloneBackupFailureCount)
                eArgs.IntTotalCount = Me.GetBackupConvertTotal(abType)
                RaiseEvent LoadAloneBackupHandler(Me, eArgs)
            Else
                eArgs = New LoadAloneBackupEventArgs(LoadAloneBackupType.Convert)
                eArgs.ABType = abType
                eArgs.StrFolderPath = IIf(isReslue, strBackupWeChatFolder, info.FolderPath)
                eArgs.BABStatus = IIf(isReslue, Backup2AloneBackupStatus.RetrySuccess, Backup2AloneBackupStatus.RetryFailure)
                eArgs.CBStatus = statueReslue
                eArgs.IntTotalCount = 0
                eArgs.IntFailureCount = 1
                eArgs.IntSuccessCount = 0
                eArgs.FailureRetry = True
                RaiseEvent LoadAloneBackupHandler(Me, eArgs)
            End If

            Me.AddBackupConverted(abType, info.FolderPath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoBackup2AloneBackup")
        End Try
    End Sub

    Private Sub AddBackupConverted(ByVal abType As AloneBackupType, ByVal strFolderPath As String)
        Try
            Dim lstTemp As New List(Of String)
            If Me.mDictBackupConverted.ContainsKey(abType) Then
                lstTemp = Me.mDictBackupConverted(abType)
                If Not lstTemp.Contains(strFolderPath) Then
                    lstTemp.Add(strFolderPath)
                End If
                Me.mDictBackupConverted(abType) = lstTemp

            Else
                lstTemp.Add(strFolderPath)
                Me.mDictBackupConverted.Add(abType, lstTemp)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddBackupConverted")
        End Try
    End Sub

    Private Function SuccessOrFailureCount(ByVal abType As AloneBackupType, ByVal dictTemp As Dictionary(Of AloneBackupType, Integer), Optional ByVal isAdd As Boolean = False) As Integer
        Dim intRelust As Integer = 0
        Try
            If isAdd Then
                If dictTemp.ContainsKey(abType) Then
                    Dim intTemp As Integer = dictTemp(abType)
                    dictTemp.Remove(abType)
                    dictTemp.Add(abType, intTemp + 1)
                Else
                    dictTemp.Add(abType, 1)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSuccessOrFailureCount_Set")
        End Try

        Try
            If dictTemp.ContainsKey(abType) Then
                intRelust = dictTemp(abType)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSuccessOrFailureCount_Get")
        End Try
        Return intRelust
    End Function

    Private Function GetBackupConvertTotal(ByVal abType As AloneBackupType) As Integer
        Dim intRelust As Integer = 0
        Try
            If Me.mDictBackupConverting.ContainsKey(abType) Then
                intRelust = Me.mDictBackupConverting(abType).Count
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetBackupConvertTotal")
        End Try
        Return intRelust
    End Function

    Private Sub GetAllBackup2AloneBackup(abType As AloneBackupType, strSku As String, strBackupFolder As String, strAloneBackupIniFile As String, strIdentifier As String)
        Dim listTemp As New List(Of String)
        Try
            '加载ITUNES备份目录里面的文件
            For Each Item As String In Directory.GetDirectories(BackupHelper.BackupFolderEx)
                If Not listTemp.Contains(Item) Then
                    listTemp.Insert(Me.GetInserIndex(Item, listTemp, strIdentifier), Item)
                End If
            Next

            '加载xml文件里面的记录
            Dim lstXML As List(Of String) = XmlHelper.LoadBackUpListFromXML()
            If lstXML IsNot Nothing AndAlso lstXML.Count > 0 Then
                For Each Item As String In lstXML
                    If Not listTemp.Contains(Item) Then
                        listTemp.Insert(Me.GetInserIndex(Item, listTemp, strIdentifier), Item)
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "GetBackup2AloneBackup_LoadDirectory")
        End Try

        Try
            Dim lstBackupConverting As New List(Of String)
            Dim lstBackup2AloneBackupInfo As New List(Of Backup2AloneBackupInfo)
            For Each strFolder As String In listTemp

                If Not Directory.Exists(strFolder) Then
                    Common.LogException("文件夹不存在:" & strFolder, "GetBackup2AloneBackup")
                    Continue For
                End If

                '已经转过无需在转
                If IniClass.GetIniSectionKey("AloneBackupWeChat", strFolder.ToLower(), strAloneBackupIniFile).Length > 0 Then
                    Continue For
                End If

                '转过后删除 也无需在转
                If IniClass.GetIniSectionKey("AloneBackupWeChat_Del", strFolder.ToLower(), strAloneBackupIniFile).Length > 0 Then
                    Common.LogException("转换过，被用户删除，无需转换:" & strFolder, "GetBackup2AloneBackup")
                    Continue For
                End If

                '转过后失败 也无需在转
                If IniClass.GetIniSectionKey("AloneBackupWeChat_Failure", strFolder.ToLower(), strAloneBackupIniFile).Length > 0 Then
                    Continue For
                End If

                '整机备份没有微信相关
                If iPhoneDevice.CheckBackupIncludeWeixin(strFolder, strSku) <> WeixinInstalled.Installed Then
                    IniClass.SetIniSectionKey("AloneBackupWeChat_Failure", strFolder.ToLower(), CopyAppBackupStatus.UninstalledWeChat, strAloneBackupIniFile)
                    Dim eArgs As New LoadAloneBackupEventArgs(LoadAloneBackupType.LoadiTunes)
                    eArgs.ABType = abType
                    eArgs.StrFolderPath = strFolder.ToLower()
                    RaiseEvent LoadAloneBackupHandler(Me, eArgs)
                    Continue For
                End If

                Dim info As BackupInfo = New BackupInfo(strFolder)
                If info.Identifier.Length = 0 Then
                    IniClass.SetIniSectionKey("AloneBackupWeChat_Failure", strFolder.ToLower(), CopyAppBackupStatus.UninstalledWeChat, strAloneBackupIniFile)
                    Dim eArgs As New LoadAloneBackupEventArgs(LoadAloneBackupType.LoadiTunes)
                    eArgs.ABType = abType
                    eArgs.StrFolderPath = strFolder.ToLower()
                    RaiseEvent LoadAloneBackupHandler(Me, eArgs)
                    Continue For
                End If

                '添加到待转换列表
                If info.Identifier.Length > 0 AndAlso Not lstBackupConverting.Contains(info.Identifier) Then
                    lstBackupConverting.Add(info.Identifier)
                End If
                Dim babInfo As New Backup2AloneBackupInfo(abType, info)
                lstBackup2AloneBackupInfo.Add(babInfo)
            Next

            '添加到总的待转换列表
            Me.AddAllDictBackupConverting(abType, lstBackupConverting)
            Me.AddDictAllBackup2AloneBackup(abType, lstBackup2AloneBackupInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetBackup2AloneBackup_Add")
        End Try
    End Sub

    Private Sub AddDictAllBackup2AloneBackup(ByVal abType As AloneBackupType, ByVal lstBackup2AloneBackupInfo As List(Of Backup2AloneBackupInfo))
        Try
            If Me.mDictAllBackup2AloneBackup.ContainsKey(abType) Then
                Dim lstTemp As List(Of Backup2AloneBackupInfo) = Me.mDictAllBackup2AloneBackup(abType)
                For Each info As Backup2AloneBackupInfo In lstBackup2AloneBackupInfo
                    If info.ABType = abType AndAlso Not lstTemp.Contains(info) Then
                        lstTemp.Add(info)
                    End If
                Next
                Me.mDictAllBackup2AloneBackup(abType) = lstTemp
            Else
                Me.mDictAllBackup2AloneBackup.Add(abType, lstBackup2AloneBackupInfo)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddDictAllBackup2AloneBackup")
        End Try
    End Sub

    Private Sub AddAllDictBackupConverting(abType As AloneBackupType, lstBackupConverting As List(Of String))
        Try
            If Not Me.mDictAllBackupConverting.ContainsKey(abType) Then
                Me.mDictAllBackupConverting.Add(abType, lstBackupConverting)
            Else
                Dim lstTemp As List(Of String) = Me.mDictAllBackupConverting(abType)
                For Each strItem As String In lstBackupConverting
                    If Not lstTemp.Contains(strItem) Then
                        lstTemp.Add(strItem)
                    End If
                Next
                Me.mDictAllBackupConverting(abType) = lstTemp
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddAllDictBackupConverting")
        End Try
    End Sub

    Private Sub AddBackup2AloneBackupByAllBackup2AloneBackup(ByVal abType As AloneBackupType, ByVal isAddLast As Boolean)
        Try
            Dim lstTemp As List(Of Backup2AloneBackupInfo) = Nothing
            If Me.mDictAllBackup2AloneBackup.ContainsKey(abType) Then
                lstTemp = Me.mDictAllBackup2AloneBackup(abType)
            End If
            If lstTemp Is Nothing OrElse lstTemp.Count = 0 Then
                Return
            End If
            Dim lstTemping As List(Of String) = Nothing
            If Me.mDictBackupConverting.ContainsKey(abType) Then
                lstTemping = Me.mDictBackupConverting(abType)
                Me.mDictBackupConverting.Remove(abType)
            Else
                lstTemping = New List(Of String)
            End If

            For Each info As Backup2AloneBackupInfo In lstTemp
                If Not lstTemping.Contains(info.BInfo.FolderPath) Then
                    If isAddLast Then
                        Me.mListBackup2AloneBackup.Add(info)
                    Else
                        Me.mListBackup2AloneBackup.Insert(0, (info))
                    End If
                    lstTemping.Add(info.BInfo.FolderPath)
                End If
            Next

            Me.mDictBackupConverting.Add(abType, lstTemping)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddBackup2AloneBackupByAllBackup2AloneBackup")
        End Try
    End Sub

    Private Sub GetAloneBackupList(ByVal abType As AloneBackupType, ByVal strBackupFolder As String, ByVal strAloneBackupIniFile As String)
        Dim lstLoadAloneBackupList As New List(Of BackupInfo)
        Try
            Dim arrItem As String() = Directory.GetDirectories(strBackupFolder)
            Dim info As BackupInfo = Nothing
            If arrItem IsNot Nothing Then
                For Each Item As String In arrItem
                    info = Me.AddAloneBackInfo(Item, strAloneBackupIniFile)
                    If info IsNot Nothing AndAlso Not lstLoadAloneBackupList.Contains(info) Then
                        lstLoadAloneBackupList.Add(info)
                    End If
                Next
            End If
            Me.AddDictAloneBackup(abType, lstLoadAloneBackupList)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetAloneBackupList")
        End Try
    End Sub

    Private Sub AddDictAloneBackup(ByVal abType As AloneBackupType, ByVal lstLoadAloneBackupList As List(Of BackupInfo))
        Try
            If Me.mDictAloneBackupList.ContainsKey(abType) Then
                Dim lstTemp As List(Of BackupInfo) = Me.mDictAloneBackupList(abType)
                For Each info As BackupInfo In lstLoadAloneBackupList
                    If Not lstTemp.Contains(info) Then
                        lstTemp.Add(info)
                    End If
                Next
                Me.mDictAloneBackupList(abType) = lstTemp
            Else
                Me.mDictAloneBackupList.Add(abType, lstLoadAloneBackupList)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddDictAloneBackup")
        End Try
    End Sub

    Public Function AddAloneBackInfo(ByVal strPath As String, ByVal strAloneBackupIniFile As String) As BackupInfo
        Try
            Dim bakObject As BackupInfo = New BackupInfo(strPath)
            If Me.CheckBackEnable(bakObject) Then
                Return bakObject
            Else
                '解析不出来的当成是垃圾删除
                'Try
                '    If Not Me.mIsBackup2AloneBackup Then '如果在转换中则不删除 有可能是转换中的文件                       
                '        If Directory.Exists(strPath) Then
                '            Try
                '                For Each strFolder As String In Directory.GetDirectories(strPath, "*", SearchOption.AllDirectories)
                '                    For Each strFile As String In Directory.GetFiles(strFolder)
                '                        Try
                '                            File.Delete(strFile)
                '                        Catch
                '                        End Try
                '                        Application.DoEvents()
                '                    Next
                '                    Try
                '                        Directory.Delete(strFolder, True)
                '                    Catch
                '                    End Try
                '                    Application.DoEvents()
                '                Next

                '                For Each strFile As String In Directory.GetFiles(strPath)
                '                    Try
                '                        File.Delete(strFile)
                '                    Catch
                '                    End Try
                '                    Application.DoEvents()
                '                Next
                '                Directory.Delete(strPath)
                '            Catch ex As Exception
                '                Common.LogException(ex.ToString(), "AddAloneBackInfo_Delete")
                '            End Try
                '        End If

                '        Dim strSPath As String = ""
                '        If IsConverted(strPath.ToLower(), strAloneBackupIniFile, strSPath) Then
                '            IniClass.SetIniSectionKey("AddAloneBackInfo_Del", strSPath.ToLower(), strPath.ToLower(), strAloneBackupIniFile)
                '        End If
                '    End If
                'Catch
                'End Try
                Return Nothing
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "AddAloneBackInfo")
        End Try
        Return Nothing
    End Function

    Public Function IsConverted(ByVal strPath As String, ByVal strAloneBackupIniFile As String, ByRef strSFolder As String) As Boolean
        Dim isReulse As Boolean = False
        Try
            '获取 AloneBackupWeChat.ini 里面的所有转换
            Dim arrAllAloneBackupWeChat As String() = IniClass.GetIniSectionAllKey("AloneBackupWeChat", strAloneBackupIniFile)
            Dim dicAllAloneBackupWeChat As New Dictionary(Of String, String)
            Dim strValue As String = ""
            For Each strItem As String In arrAllAloneBackupWeChat
                strValue = IniClass.GetIniSectionKey("AloneBackupWeChat", strItem, strAloneBackupIniFile).ToLower()
                If strValue.Length > 0 AndAlso Not dicAllAloneBackupWeChat.ContainsKey(strValue) Then
                    dicAllAloneBackupWeChat.Add(strValue, strItem)
                End If
            Next

            If dicAllAloneBackupWeChat.ContainsKey(strPath.ToLower()) Then
                isReulse = True
                strSFolder = dicAllAloneBackupWeChat(strPath)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "IsConverted")
        End Try
        Return isReulse
    End Function

    Private Function CheckBackEnable(ByVal obj As BackupInfo) As Boolean
        Dim blnReturn As Boolean = True
        Try
            If obj.BuildVersion.Length = 0 AndAlso obj.DeviceName.Length = 0 AndAlso obj.DisplayName.Length = 0 AndAlso obj.ProductType.Length = 0 AndAlso obj.ProductVersion.Length = 0 AndAlso obj.SerialNumber.Length = 0 Then
                blnReturn = False
            End If
        Catch ex As Exception
            blnReturn = False
            Common.LogException(ex.ToString(), "CheckBackEnable")
        End Try
        Return blnReturn
    End Function

    Private Function GetInserIndex(ByVal strPath As String, ByVal listTemp As List(Of String), ByVal strIdentifier As String) As Integer
        Dim intIndex As Integer = listTemp.Count
        Try
            If listTemp.Count > 0 AndAlso strPath.Contains(strIdentifier) Then
                intIndex = 0
                For Each Item As String In listTemp
                    If Not Item.Contains(strIdentifier) Then
                        Continue For
                    End If
                    If Not File.Exists(Path.Combine(Item, "Status.Plist")) OrElse Not File.Exists(Path.Combine(strPath, "Status.Plist")) Then
                        Continue For
                    End If
                    If New FileInfo(Path.Combine(Item, "Status.Plist")).LastWriteTime <= New FileInfo(Path.Combine(strPath, "Status.Plist")).LastWriteTime Then
                        Exit For
                    End If
                    intIndex += 1
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "GetIndexInList")
        End Try
        Return intIndex
    End Function

    Private Sub mb2_CopyBackupWeixinFiles(ByVal e As BackupRestoreEventArgs)
        Dim eArgs As New LoadAloneBackupEventArgs(LoadAloneBackupType.Convert)
        eArgs.ABType = Me.mCurrentBackup2AloneBackupABType
        eArgs.BABStatus = Backup2AloneBackupStatus.Aloneing
        eArgs.IntProgress = CInt(e.Progress).ToString().PadLeft(2, " "c)
        eArgs.ErrorMsg = e.ErrorMsg
        eArgs.StrFolderPath = Me.mCurrentBackup2AloneBackupFolder
        eArgs.IntSuccessCount = Me.SuccessOrFailureCount(Me.mCurrentBackup2AloneBackupABType, Me.mDictBackup2AloneBackupSuccessCount)
        eArgs.IntFailureCount = Me.SuccessOrFailureCount(Me.mCurrentBackup2AloneBackupABType, Me.mDictBackup2AloneBackupFailureCount)
        eArgs.IntTotalCount = Me.GetBackupConvertTotal(Me.mCurrentBackup2AloneBackupABType)
        RaiseEvent LoadAloneBackupHandler(Me, eArgs)
    End Sub

#End Region

End Class

Public Class Backup2AloneBackupInfo

    Private _abType As AloneBackupType = AloneBackupType.WeChat
    Public Property ABType() As AloneBackupType
        Get
            Return Me._abType
        End Get
        Set(ByVal value As AloneBackupType)
            Me._abType = value
        End Set
    End Property

    Private _bInfo As BackupInfo = Nothing
    Public Property BInfo() As BackupInfo
        Get
            Return Me._bInfo
        End Get
        Set(ByVal value As BackupInfo)
            Me._bInfo = value
        End Set
    End Property

    Public Sub New(ByVal abType As AloneBackupType, ByVal bInfo As BackupInfo)
        Me._abType = abType
        Me._bInfo = bInfo
    End Sub

    Public Sub New()

    End Sub

End Class

Public Class LoadAloneBackupInfo

    Dim _ABType As AloneBackupType = AloneBackupType.None
    Public Property ABType() As AloneBackupType
        Get
            Return Me._ABType
        End Get
        Set(ByVal value As AloneBackupType)
            Me._ABType = value
        End Set
    End Property

    Dim _StrIdentifier As String = ""
    Public Property StrIdentifier() As String
        Get
            Return Me._StrIdentifier
        End Get
        Set(ByVal value As String)
            Me._StrIdentifier = value
        End Set
    End Property

    Dim _IsAddLast As Boolean = False
    Public Property IsAddLast() As Boolean
        Get
            Return Me._IsAddLast
        End Get
        Set(ByVal value As Boolean)
            Me._IsAddLast = value
        End Set
    End Property

    Public Sub New(ByVal abType As AloneBackupType, ByVal strI As String, ByVal isLast As Boolean)
        Me._ABType = abType
        Me._StrIdentifier = strI
        Me._IsAddLast = isLast
    End Sub

    Public Sub New()

    End Sub
End Class

Public Class AloneBackupsEventArgs
    Inherits EventArgs

    Private mintProgress As Integer = 0
    Private mstrPath As String = ""
    Private mStatus As BackupRestoreErrorType = BackupRestoreErrorType.None
    Private mstrErrorMsg As String = String.Empty
    Private mAloneBackupType As AloneBackupType = AloneBackupType.WeChat

    Public Sub New(intPro As Integer, btype As BackupRestoreErrorType, sPath As String, ByVal strErrMsg As String, abType As AloneBackupType)
        Me.mintProgress = intPro
        Me.mStatus = btype
        Me.mstrPath = sPath
        Me.mstrErrorMsg = strErrMsg
        Me.mAloneBackupType = abType
    End Sub

    Public Property Progress() As Integer
        Get
            Return Me.mintProgress
        End Get
        Set(ByVal value As Integer)
            Me.mintProgress = value
        End Set
    End Property

    Public Property Statue() As BackupRestoreErrorType
        Get
            Return Me.mStatus
        End Get
        Set(ByVal value As BackupRestoreErrorType)
            Me.mStatus = value
        End Set
    End Property

    Public Property StrFolderPath() As String
        Get
            Return Me.mstrPath
        End Get
        Set(ByVal value As String)
            Me.mstrPath = value
        End Set
    End Property

    Public Property ErrorMsg() As String
        Get
            Return Me.mstrErrorMsg
        End Get
        Set(ByVal value As String)
            Me.mstrErrorMsg = value
        End Set
    End Property

    Public Property SAloneBackupType() As AloneBackupType
        Get
            Return Me.mAloneBackupType
        End Get
        Set(ByVal value As AloneBackupType)
            Me.mAloneBackupType = value
        End Set
    End Property

End Class

Public Class CheckAloneBackupEventArgs
    Inherits EventArgs

    Dim blnFinish As Boolean = False
    Dim blnContain As Boolean = False
    Private mAloneBackupType As AloneBackupType = AloneBackupType.WeChat

    Public Property SAloneBackupType() As AloneBackupType
        Get
            Return Me.mAloneBackupType
        End Get
        Set(ByVal value As AloneBackupType)
            Me.mAloneBackupType = value
        End Set
    End Property
    Public Property IsFinish() As Boolean
        Get
            Return Me.blnFinish
        End Get
        Set(ByVal value As Boolean)
            Me.blnFinish = value
        End Set
    End Property

    Public Property IsContain() As Boolean
        Get
            Return Me.blnContain
        End Get
        Set(ByVal value As Boolean)
            Me.blnContain = value
        End Set
    End Property

    Public Sub New(ByVal isFin As Boolean, ByVal isCon As Boolean, abType As AloneBackupType)
        blnFinish = isFin
        blnContain = isCon
        mAloneBackupType = abType
    End Sub

End Class

Public Enum Backup2AloneBackupStatus
    None = 0
    Start = 1
    Success = 2
    Failure = 3
    DoEnd = 4
    Aloneing = 5
    RetrySuccess = 6
    RetryFailure = 7
End Enum

Public Enum WeixinMainViewStyle
    LoadBackup
    LoadDevice
    Content
    MigrationHome
    iCloudBackup
End Enum

Public Enum LoadAloneBackupType
    None
    Convert
    LoadiTunes
    AloneBackupList
End Enum

Public Class LoadAloneBackupEventArgs
    Inherits EventArgs

    Public Sub New(ByVal lab As LoadAloneBackupType)
        Me._LABType = lab
    End Sub

    Dim _LABType As LoadAloneBackupType = LoadAloneBackupType.None
    Public Property LABType() As LoadAloneBackupType
        Get
            Return Me._LABType
        End Get
        Set(ByVal value As LoadAloneBackupType)
            Me._LABType = value
        End Set
    End Property

    Dim _abType As AloneBackupType = AloneBackupType.None
    Public Property ABType() As AloneBackupType
        Get
            Return Me._abType
        End Get
        Set(ByVal value As AloneBackupType)
            Me._abType = value
        End Set
    End Property

    Dim _StrFolderPath As String = ""
    Public Property StrFolderPath() As String
        Get
            Return Me._StrFolderPath
        End Get
        Set(ByVal value As String)
            Me._StrFolderPath = value
        End Set
    End Property

    Dim _BABStatus As Backup2AloneBackupStatus = Backup2AloneBackupStatus.None
    Public Property BABStatus() As Backup2AloneBackupStatus
        Get
            Return Me._BABStatus
        End Get
        Set(ByVal value As Backup2AloneBackupStatus)
            Me._BABStatus = value
        End Set
    End Property

    Dim _IntProgress As Integer = 0
    Public Property IntProgress() As Integer
        Get
            Return Me._IntProgress
        End Get
        Set(ByVal value As Integer)
            Me._IntProgress = value
        End Set
    End Property

    Dim _IntSuccessCount As Integer = 0
    Public Property IntSuccessCount() As Integer
        Get
            Return Me._IntSuccessCount
        End Get
        Set(ByVal value As Integer)
            Me._IntSuccessCount = value
        End Set
    End Property

    Dim _IntFailureCount As Integer = 0
    Public Property IntFailureCount() As Integer
        Get
            Return Me._IntFailureCount
        End Get
        Set(ByVal value As Integer)
            Me._IntFailureCount = value
        End Set
    End Property

    Dim _IntTotalCount As Integer = 0
    Public Property IntTotalCount() As Integer
        Get
            Return Me._IntTotalCount
        End Get
        Set(ByVal value As Integer)
            Me._IntTotalCount = value
        End Set
    End Property

    Dim _CBStatus As CopyAppBackupStatus = CopyAppBackupStatus.None
    Public Property CBStatus() As CopyAppBackupStatus
        Get
            Return Me._CBStatus
        End Get
        Set(ByVal value As CopyAppBackupStatus)
            Me._CBStatus = value
        End Set
    End Property

    Dim _LstAloneBackupList As New List(Of BackupInfo)
    Public Property LstAloneBackupList() As List(Of BackupInfo)
        Get
            Return Me._LstAloneBackupList
        End Get
        Set(ByVal value As List(Of BackupInfo))
            Me._LstAloneBackupList = value
        End Set
    End Property

    Dim _ErrorMsg As String = ""
    Public Property ErrorMsg() As String
        Get
            Return Me._ErrorMsg
        End Get
        Set(ByVal value As String)
            Me._ErrorMsg = value
        End Set
    End Property

    Dim _FailureRetry As Boolean = False
    Public Property FailureRetry() As Boolean
        Get
            Return Me._FailureRetry
        End Get
        Set(ByVal value As Boolean)
            Me._FailureRetry = value
        End Set
    End Property


End Class
