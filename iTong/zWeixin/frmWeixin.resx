<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btn_normal.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEgAAAAYCAYAAABZY7uwAAAABGdBTUEAALGPC/xhBQAAAT1JREFUWEft
        mLENg0AMRRkgZSqGoEmbLnOkTZcm22SIVOxBwQoMQr6DjU5wh30CBSnxk75Owv8s+4sipHAcx/kn+r4v
        oQo6ZYj8JbdYhP1X6JYh8p+4xX5gCApnunyOFkNiTywAq/YNCQPkvjlTVdwqCuq5b85UV25l4gAdDSKf
        CQwwLts0zbmu64sm8oX3uFUU1Mdlu667t2370ES+8B63MnHEhacm8g12HfjHRWl5nC9N7MsOiJbHqcI+
        DyjF6oDoTCmom4B/FhCdKUk9vMetoqA+C4jOlKROfhG3MqEFsFlA4XPRlgGFz0UekAc0B3U1AJEH9OWA
        ogrqJuCPBpCS1MN73CoK6tEAUpI6+UXcysQnAE3kG+w68M8C0rQ2II01Afkv6W+DAX7qW2xzMIB/zWtg
        CP8/yHGcgaJ4AxPgunYMhw4oAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnTimeFilter.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnPreviousMenu.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnExportSetting.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnFinalData.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnWeCahtUsers.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAATCAYAAACQjC21AAAABGdBTUEAALGPC/xhBQAAAEpJREFUOE/t
        1LEJACAMRFE3S+vWqV3EIkvEfHCDsxHy4UBSvNLh7lZbtRSHYYA7IlINAwvwnvSwGtRqUK9BvX/A5//h
        5IEurgyfB4e2rfC0KKiDAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="cbxSelectAll.tbIconChecked" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0w0hl6ky4wgPQuIB0EURhmBhjKAMMMTWyIqEBEEREBRZCggAGjoUis
        iGIhKKhgD0gQUGIwiqioZEbWSnx5ee/l5ffHvd/aZ+9z99l7n7UuACRPHy4vBZYCIJkn4Ad6ONNXhUfQ
        sf0ABniAAaYAMFnpqb5B7sFAJC83F3q6yAn8i94MAUj8vmXo6U+ng/9P0qxUvgAAyF/E5mxOOkvE+SJO
        yhSkiu0zIqbGJIoZRomZL0pQxHJijlvkpZ99FtlRzOxkHlvE4pxT2clsMfeIeHuGkCNixEfEBRlcTqaI
        b4tYM0mYzBXxW3FsMoeZDgCKJLYLOKx4EZuImMQPDnQR8XIAcKS4LzjmCxZwsgTiQ7mkpGbzuXHxArou
        S49uam3NoHtyMpM4AoGhP5OVyOSz6S4pyalMXjYAi2f+LBlxbemiIluaWltaGpoZmX5RqP+6+Dcl7u0i
        vQr43DOI1veH7a/8UuoAYMyKarPrD1vMfgA6tgIgd/8Pm+YhACRFfWu/8cV5aOJ5iRcIUm2MjTMzM424
        HJaRuKC/6386/A198T0j8Xa/l4fuyollCpMEdHHdWClJKUI+PT2VyeLQDf88xP848K/zWBrIieXwOTxR
        RKhoyri8OFG7eWyugJvCo3N5/6mJ/zDsT1qca5Eo9Z8ANcoISN2gAuTnPoCiEAESeVDc9d/75oMPBeKb
        F6Y6sTj3nwX9+65wifiRzo37HOcSGExnCfkZi2viawnQgAAkARXIAxWgAXSBITADVsAWOAI3sAL4gWAQ
        DtYCFogHyYAPMkEu2AwKQBHYBfaCSlAD6kEjaAEnQAc4DS6Ay+A6uAnugAdgBIyD52AGvAHzEARhITJE
        geQhVUgLMoDMIAZkD7lBPlAgFA5FQ3EQDxJCudAWqAgqhSqhWqgR+hY6BV2ArkID0D1oFJqCfoXewwhM
        gqmwMqwNG8MM2An2hoPhNXAcnAbnwPnwTrgCroOPwe3wBfg6fAcegZ/DswhAiAgNUUMMEQbigvghEUgs
        wkc2IIVIOVKHtCBdSC9yCxlBppF3KAyKgqKjDFG2KE9UCIqFSkNtQBWjKlFHUe2oHtQt1ChqBvUJTUYr
        oQ3QNmgv9Cp0HDoTXYAuRzeg29CX0HfQ4+g3GAyGhtHBWGE8MeGYBMw6TDHmAKYVcx4zgBnDzGKxWHms
        AdYO64dlYgXYAux+7DHsOewgdhz7FkfEqeLMcO64CBwPl4crxzXhzuIGcRO4ebwUXgtvg/fDs/HZ+BJ8
        Pb4LfwM/jp8nSBN0CHaEYEICYTOhgtBCuER4SHhFJBLVidbEACKXuIlYQTxOvEIcJb4jyZD0SS6kSJKQ
        tJN0hHSedI/0ikwma5MdyRFkAXknuZF8kfyY/FaCImEk4SXBltgoUSXRLjEo8UISL6kl6SS5VjJHslzy
        pOQNyWkpvJS2lIsUU2qDVJXUKalhqVlpirSptJ90snSxdJP0VelJGayMtoybDFsmX+awzEWZMQpC0aC4
        UFiULZR6yiXKOBVD1aF6UROoRdRvqP3UGVkZ2WWyobJZslWyZ2RHaAhNm+ZFS6KV0E7QhmjvlygvcVrC
        WbJjScuSwSVzcopyjnIcuUK5Vrk7cu/l6fJu8onyu+U75B8poBT0FQIUMhUOKlxSmFakKtoqshQLFU8o
        3leClfSVApXWKR1W6lOaVVZR9lBOVd6vfFF5WoWm4qiSoFKmclZlSpWiaq/KVS1TPaf6jC5Ld6In0Svo
        PfQZNSU1TzWhWq1av9q8uo56iHqeeqv6Iw2CBkMjVqNMo1tjRlNV01czV7NZ874WXouhFa+1T6tXa05b
        RztMe5t2h/akjpyOl06OTrPOQ12yroNumm6d7m09jB5DL1HvgN5NfVjfQj9ev0r/hgFsYGnANThgMLAU
        vdR6KW9p3dJhQ5Khk2GGYbPhqBHNyMcoz6jD6IWxpnGE8W7jXuNPJhYmSSb1Jg9MZUxXmOaZdpn+aqZv
        xjKrMrttTjZ3N99o3mn+cpnBMs6yg8vuWlAsfC22WXRbfLS0suRbtlhOWWlaRVtVWw0zqAx/RjHjijXa
        2tl6o/Vp63c2ljYCmxM2v9ga2ibaNtlOLtdZzllev3zMTt2OaVdrN2JPt4+2P2Q/4qDmwHSoc3jiqOHI
        dmxwnHDSc0pwOub0wtnEme/c5jznYuOy3uW8K+Lq4Vro2u8m4xbiVun22F3dPc692X3Gw8Jjncd5T7Sn
        t+duz2EvZS+WV6PXzAqrFetX9HiTvIO8K72f+Oj78H26fGHfFb57fB+u1FrJW9nhB/y8/Pb4PfLX8U/z
        /z4AE+AfUBXwNNA0MDewN4gSFBXUFPQm2Dm4JPhBiG6IMKQ7VDI0MrQxdC7MNaw0bGSV8ar1q66HK4Rz
        wzsjsBGhEQ0Rs6vdVu9dPR5pEVkQObRGZ03WmqtrFdYmrT0TJRnFjDoZjY4Oi26K/sD0Y9YxZ2O8Yqpj
        ZlgurH2s52xHdhl7imPHKeVMxNrFlsZOxtnF7YmbineIL4+f5rpwK7kvEzwTahLmEv0SjyQuJIUltSbj
        kqOTT/FkeIm8nhSVlKyUgVSD1ILUkTSbtL1pM3xvfkM6lL4mvVNAFf1M9Ql1hVuFoxn2GVUZbzNDM09m
        SWfxsvqy9bN3ZE/kuOd8vQ61jrWuO1ctd3Pu6Hqn9bUboA0xG7o3amzM3zi+yWPT0c2EzYmbf8gzySvN
        e70lbEtXvnL+pvyxrR5bmwskCvgFw9tst9VsR23nbu/fYb5j/45PhezCa0UmReVFH4pZxde+Mv2q4quF
        nbE7+0ssSw7uwuzi7Rra7bD7aKl0aU7p2B7fPe1l9LLCstd7o/ZeLV9WXrOPsE+4b6TCp6Jzv+b+Xfs/
        VMZX3qlyrmqtVqreUT13gH1g8KDjwZYa5ZqimveHuIfu1nrUttdp15UfxhzOOPy0PrS+92vG140NCg1F
        DR+P8I6MHA082tNo1djYpNRU0gw3C5unjkUeu/mN6zedLYYtta201qLj4Ljw+LNvo78dOuF9ovsk42TL
        d1rfVbdR2grbofbs9pmO+I6RzvDOgVMrTnV32Xa1fW/0/ZHTaqerzsieKTlLOJt/duFczrnZ86nnpy/E
        XRjrjup+cHHVxds9AT39l7wvXbnsfvlir1PvuSt2V05ftbl66hrjWsd1y+vtfRZ9bT9Y/NDWb9nffsPq
        RudN65tdA8sHzg46DF645Xrr8m2v29fvrLwzMBQydHc4cnjkLvvu5L2key/vZ9yff7DpIfph4SOpR+WP
        lR7X/aj3Y+uI5ciZUdfRvidBTx6Mscae/5T+04fx/Kfkp+UTqhONk2aTp6fcp24+W/1s/Hnq8/npgp+l
        f65+ofviu18cf+mbWTUz/pL/cuHX4lfyr468Xva6e9Z/9vGb5Dfzc4Vv5d8efcd41/s+7P3EfOYH7IeK
        j3ofuz55f3q4kLyw8Bv3hPP74uYdwgAAAAlwSFlzAAALDAAACwwBP0AiyAAAAlBJREFUWEftl01vEkEc
        h/dbGE/aaLx49+hVP4KJ8eB3MI0mVRONF2O8qHswxjS9Wt8rpkpqAV9qKy7QytotCQVJqZRS2Nr2+HP+
        ExZ2Z2dghqsenmxmJg/LPMxCsL7lXJuBESDPOvfkp83ACJBnnXhQsBkYAfIsr7xhMzACNgCLAuDP3oEx
        3QgUQLo+jG4ECiBdH0Y3AgWQrg+DvP8BTAKU6j7O3i/ixkyFj00DiL5pANE3DdBsdZBML8FZXuVjowAZ
        r4WTN/OYeLnemzMJIPNNAsh8kwC1egOvZjPI5vvvVTtA8kcTY9ccXJxaw25oXjeAytcNoPJ1A1Rrm3ie
        SOHj13xkXivAe3bzIxPfcfruCrbae5E1nQCDfJ0Ag3ydABXa/Jt5zH5YQMeP+rEAtxJVPMr0X6xQ7eD4
        dQdHrzpw1tu9+QAxgKkvBjD1xQC5FQ/uWv8R+b3Vwou3Kfbpz2Ozsd2bD4gFGH9WxqHLWVyY9JCvtHHq
        9jIfP0zLC4sBTH0xgKkvBlhyXEy/nkNmIcc3n0h+5mPX60cJEwuw4+/jzL0iv+nhK1l+Pf/Yi4kBYgBT
        Xwxg6osB/N19JFOLfNNPZ+b4Nf3FiXkBsQCEW+vgGDt2dHM6et6GH5HCiAEIE18MQJj4YgCi0dzhx542
        T0e/uR1/dAKkAYjJT3X+Bu68+xWZF5EFIHR9WQBC15cFIFZLFR6gUCxF5kWUAein5tJ0GS12JMPzIqoA
        ur4qgK6vCkAsOkX2SES/9UWUAXRRBdBFFUCXQQF0CAf4h/8NwvoL1mPs+hTyHSEAAAAASUVORK5CYII=
</value>
  </data>
  <data name="cbxSelectAll.tbIconIndeterminate" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0w0hl6ky4wgPQuIB0EURhmBhjKAMMMTWyIqEBEEREBRZCggAGjoUis
        iGIhKKhgD0gQUGIwiqioZEbWSnx5ee/l5ffHvd/aZ+9z99l7n7UuACRPHy4vBZYCIJkn4Ad6ONNXhUfQ
        sf0ABniAAaYAMFnpqb5B7sFAJC83F3q6yAn8i94MAUj8vmXo6U+ng/9P0qxUvgAAyF/E5mxOOkvE+SJO
        yhSkiu0zIqbGJIoZRomZL0pQxHJijlvkpZ99FtlRzOxkHlvE4pxT2clsMfeIeHuGkCNixEfEBRlcTqaI
        b4tYM0mYzBXxW3FsMoeZDgCKJLYLOKx4EZuImMQPDnQR8XIAcKS4LzjmCxZwsgTiQ7mkpGbzuXHxArou
        S49uam3NoHtyMpM4AoGhP5OVyOSz6S4pyalMXjYAi2f+LBlxbemiIluaWltaGpoZmX5RqP+6+Dcl7u0i
        vQr43DOI1veH7a/8UuoAYMyKarPrD1vMfgA6tgIgd/8Pm+YhACRFfWu/8cV5aOJ5iRcIUm2MjTMzM424
        HJaRuKC/6386/A198T0j8Xa/l4fuyollCpMEdHHdWClJKUI+PT2VyeLQDf88xP848K/zWBrIieXwOTxR
        RKhoyri8OFG7eWyugJvCo3N5/6mJ/zDsT1qca5Eo9Z8ANcoISN2gAuTnPoCiEAESeVDc9d/75oMPBeKb
        F6Y6sTj3nwX9+65wifiRzo37HOcSGExnCfkZi2viawnQgAAkARXIAxWgAXSBITADVsAWOAI3sAL4gWAQ
        DtYCFogHyYAPMkEu2AwKQBHYBfaCSlAD6kEjaAEnQAc4DS6Ay+A6uAnugAdgBIyD52AGvAHzEARhITJE
        geQhVUgLMoDMIAZkD7lBPlAgFA5FQ3EQDxJCudAWqAgqhSqhWqgR+hY6BV2ArkID0D1oFJqCfoXewwhM
        gqmwMqwNG8MM2An2hoPhNXAcnAbnwPnwTrgCroOPwe3wBfg6fAcegZ/DswhAiAgNUUMMEQbigvghEUgs
        wkc2IIVIOVKHtCBdSC9yCxlBppF3KAyKgqKjDFG2KE9UCIqFSkNtQBWjKlFHUe2oHtQt1ChqBvUJTUYr
        oQ3QNmgv9Cp0HDoTXYAuRzeg29CX0HfQ4+g3GAyGhtHBWGE8MeGYBMw6TDHmAKYVcx4zgBnDzGKxWHms
        AdYO64dlYgXYAux+7DHsOewgdhz7FkfEqeLMcO64CBwPl4crxzXhzuIGcRO4ebwUXgtvg/fDs/HZ+BJ8
        Pb4LfwM/jp8nSBN0CHaEYEICYTOhgtBCuER4SHhFJBLVidbEACKXuIlYQTxOvEIcJb4jyZD0SS6kSJKQ
        tJN0hHSedI/0ikwma5MdyRFkAXknuZF8kfyY/FaCImEk4SXBltgoUSXRLjEo8UISL6kl6SS5VjJHslzy
        pOQNyWkpvJS2lIsUU2qDVJXUKalhqVlpirSptJ90snSxdJP0VelJGayMtoybDFsmX+awzEWZMQpC0aC4
        UFiULZR6yiXKOBVD1aF6UROoRdRvqP3UGVkZ2WWyobJZslWyZ2RHaAhNm+ZFS6KV0E7QhmjvlygvcVrC
        WbJjScuSwSVzcopyjnIcuUK5Vrk7cu/l6fJu8onyu+U75B8poBT0FQIUMhUOKlxSmFakKtoqshQLFU8o
        3leClfSVApXWKR1W6lOaVVZR9lBOVd6vfFF5WoWm4qiSoFKmclZlSpWiaq/KVS1TPaf6jC5Ld6In0Svo
        PfQZNSU1TzWhWq1av9q8uo56iHqeeqv6Iw2CBkMjVqNMo1tjRlNV01czV7NZ874WXouhFa+1T6tXa05b
        RztMe5t2h/akjpyOl06OTrPOQ12yroNumm6d7m09jB5DL1HvgN5NfVjfQj9ev0r/hgFsYGnANThgMLAU
        vdR6KW9p3dJhQ5Khk2GGYbPhqBHNyMcoz6jD6IWxpnGE8W7jXuNPJhYmSSb1Jg9MZUxXmOaZdpn+aqZv
        xjKrMrttTjZ3N99o3mn+cpnBMs6yg8vuWlAsfC22WXRbfLS0suRbtlhOWWlaRVtVWw0zqAx/RjHjijXa
        2tl6o/Vp63c2ljYCmxM2v9ga2ibaNtlOLtdZzllev3zMTt2OaVdrN2JPt4+2P2Q/4qDmwHSoc3jiqOHI
        dmxwnHDSc0pwOub0wtnEme/c5jznYuOy3uW8K+Lq4Vro2u8m4xbiVun22F3dPc692X3Gw8Jjncd5T7Sn
        t+duz2EvZS+WV6PXzAqrFetX9HiTvIO8K72f+Oj78H26fGHfFb57fB+u1FrJW9nhB/y8/Pb4PfLX8U/z
        /z4AE+AfUBXwNNA0MDewN4gSFBXUFPQm2Dm4JPhBiG6IMKQ7VDI0MrQxdC7MNaw0bGSV8ar1q66HK4Rz
        wzsjsBGhEQ0Rs6vdVu9dPR5pEVkQObRGZ03WmqtrFdYmrT0TJRnFjDoZjY4Oi26K/sD0Y9YxZ2O8Yqpj
        ZlgurH2s52xHdhl7imPHKeVMxNrFlsZOxtnF7YmbineIL4+f5rpwK7kvEzwTahLmEv0SjyQuJIUltSbj
        kqOTT/FkeIm8nhSVlKyUgVSD1ILUkTSbtL1pM3xvfkM6lL4mvVNAFf1M9Ql1hVuFoxn2GVUZbzNDM09m
        SWfxsvqy9bN3ZE/kuOd8vQ61jrWuO1ctd3Pu6Hqn9bUboA0xG7o3amzM3zi+yWPT0c2EzYmbf8gzySvN
        e70lbEtXvnL+pvyxrR5bmwskCvgFw9tst9VsR23nbu/fYb5j/45PhezCa0UmReVFH4pZxde+Mv2q4quF
        nbE7+0ssSw7uwuzi7Rra7bD7aKl0aU7p2B7fPe1l9LLCstd7o/ZeLV9WXrOPsE+4b6TCp6Jzv+b+Xfs/
        VMZX3qlyrmqtVqreUT13gH1g8KDjwZYa5ZqimveHuIfu1nrUttdp15UfxhzOOPy0PrS+92vG140NCg1F
        DR+P8I6MHA082tNo1djYpNRU0gw3C5unjkUeu/mN6zedLYYtta201qLj4Ljw+LNvo78dOuF9ovsk42TL
        d1rfVbdR2grbofbs9pmO+I6RzvDOgVMrTnV32Xa1fW/0/ZHTaqerzsieKTlLOJt/duFczrnZ86nnpy/E
        XRjrjup+cHHVxds9AT39l7wvXbnsfvlir1PvuSt2V05ftbl66hrjWsd1y+vtfRZ9bT9Y/NDWb9nffsPq
        RudN65tdA8sHzg46DF645Xrr8m2v29fvrLwzMBQydHc4cnjkLvvu5L2key/vZ9yff7DpIfph4SOpR+WP
        lR7X/aj3Y+uI5ciZUdfRvidBTx6Mscae/5T+04fx/Kfkp+UTqhONk2aTp6fcp24+W/1s/Hnq8/npgp+l
        f65+ofviu18cf+mbWTUz/pL/cuHX4lfyr468Xva6e9Z/9vGb5Dfzc4Vv5d8efcd41/s+7P3EfOYH7IeK
        j3ofuz55f3q4kLyw8Bv3hPP74uYdwgAAAAlwSFlzAAALDAAACwwBP0AiyAAAAKdJREFUWEft17ENQjEM
        hGFvwQbM9EZhEbwEr6NnApAQzUM0dEwAojSxlEgQnWQ5LVf8Vc7N10WO50VLNpDfybS7askG8jtZby9a
        soH8Tm73h5ZsIDUzcQB7vt7pKoIDwPeoiuAA8D2qIjgAfI/yOwL0AKvNKaxtEQDa97UtAkD7vrZFAPP+
        ENa2BCAAAQhAAAIQgAA/AJkQQCYEkAkBZCLAF8Af/wZNPjDYw2uzW610AAAAAElFTkSuQmCC
</value>
  </data>
  <data name="cbxSelectAll.tbIconUnChecked" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAEAAAAAPCAYAAABUZ8lnAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAACH
        DwAAjA8AAP1SAACBQAAAfXkAAOmLAAA85QAAGcxzPIV3AAAKOWlDQ1BQaG90b3Nob3AgSUNDIHByb2Zp
        bGUAAEjHnZZ3VFTXFofPvXd6oc0w0hl6ky4wgPQuIB0EURhmBhjKAMMMTWyIqEBEEREBRZCggAGjoUis
        iGIhKKhgD0gQUGIwiqioZEbWSnx5ee/l5ffHvd/aZ+9z99l7n7UuACRPHy4vBZYCIJkn4Ad6ONNXhUfQ
        sf0ABniAAaYAMFnpqb5B7sFAJC83F3q6yAn8i94MAUj8vmXo6U+ng/9P0qxUvgAAyF/E5mxOOkvE+SJO
        yhSkiu0zIqbGJIoZRomZL0pQxHJijlvkpZ99FtlRzOxkHlvE4pxT2clsMfeIeHuGkCNixEfEBRlcTqaI
        b4tYM0mYzBXxW3FsMoeZDgCKJLYLOKx4EZuImMQPDnQR8XIAcKS4LzjmCxZwsgTiQ7mkpGbzuXHxArou
        S49uam3NoHtyMpM4AoGhP5OVyOSz6S4pyalMXjYAi2f+LBlxbemiIluaWltaGpoZmX5RqP+6+Dcl7u0i
        vQr43DOI1veH7a/8UuoAYMyKarPrD1vMfgA6tgIgd/8Pm+YhACRFfWu/8cV5aOJ5iRcIUm2MjTMzM424
        HJaRuKC/6386/A198T0j8Xa/l4fuyollCpMEdHHdWClJKUI+PT2VyeLQDf88xP848K/zWBrIieXwOTxR
        RKhoyri8OFG7eWyugJvCo3N5/6mJ/zDsT1qca5Eo9Z8ANcoISN2gAuTnPoCiEAESeVDc9d/75oMPBeKb
        F6Y6sTj3nwX9+65wifiRzo37HOcSGExnCfkZi2viawnQgAAkARXIAxWgAXSBITADVsAWOAI3sAL4gWAQ
        DtYCFogHyYAPMkEu2AwKQBHYBfaCSlAD6kEjaAEnQAc4DS6Ay+A6uAnugAdgBIyD52AGvAHzEARhITJE
        geQhVUgLMoDMIAZkD7lBPlAgFA5FQ3EQDxJCudAWqAgqhSqhWqgR+hY6BV2ArkID0D1oFJqCfoXewwhM
        gqmwMqwNG8MM2An2hoPhNXAcnAbnwPnwTrgCroOPwe3wBfg6fAcegZ/DswhAiAgNUUMMEQbigvghEUgs
        wkc2IIVIOVKHtCBdSC9yCxlBppF3KAyKgqKjDFG2KE9UCIqFSkNtQBWjKlFHUe2oHtQt1ChqBvUJTUYr
        oQ3QNmgv9Cp0HDoTXYAuRzeg29CX0HfQ4+g3GAyGhtHBWGE8MeGYBMw6TDHmAKYVcx4zgBnDzGKxWHms
        AdYO64dlYgXYAux+7DHsOewgdhz7FkfEqeLMcO64CBwPl4crxzXhzuIGcRO4ebwUXgtvg/fDs/HZ+BJ8
        Pb4LfwM/jp8nSBN0CHaEYEICYTOhgtBCuER4SHhFJBLVidbEACKXuIlYQTxOvEIcJb4jyZD0SS6kSJKQ
        tJN0hHSedI/0ikwma5MdyRFkAXknuZF8kfyY/FaCImEk4SXBltgoUSXRLjEo8UISL6kl6SS5VjJHslzy
        pOQNyWkpvJS2lIsUU2qDVJXUKalhqVlpirSptJ90snSxdJP0VelJGayMtoybDFsmX+awzEWZMQpC0aC4
        UFiULZR6yiXKOBVD1aF6UROoRdRvqP3UGVkZ2WWyobJZslWyZ2RHaAhNm+ZFS6KV0E7QhmjvlygvcVrC
        WbJjScuSwSVzcopyjnIcuUK5Vrk7cu/l6fJu8onyu+U75B8poBT0FQIUMhUOKlxSmFakKtoqshQLFU8o
        3leClfSVApXWKR1W6lOaVVZR9lBOVd6vfFF5WoWm4qiSoFKmclZlSpWiaq/KVS1TPaf6jC5Ld6In0Svo
        PfQZNSU1TzWhWq1av9q8uo56iHqeeqv6Iw2CBkMjVqNMo1tjRlNV01czV7NZ874WXouhFa+1T6tXa05b
        RztMe5t2h/akjpyOl06OTrPOQ12yroNumm6d7m09jB5DL1HvgN5NfVjfQj9ev0r/hgFsYGnANThgMLAU
        vdR6KW9p3dJhQ5Khk2GGYbPhqBHNyMcoz6jD6IWxpnGE8W7jXuNPJhYmSSb1Jg9MZUxXmOaZdpn+aqZv
        xjKrMrttTjZ3N99o3mn+cpnBMs6yg8vuWlAsfC22WXRbfLS0suRbtlhOWWlaRVtVWw0zqAx/RjHjijXa
        2tl6o/Vp63c2ljYCmxM2v9ga2ibaNtlOLtdZzllev3zMTt2OaVdrN2JPt4+2P2Q/4qDmwHSoc3jiqOHI
        dmxwnHDSc0pwOub0wtnEme/c5jznYuOy3uW8K+Lq4Vro2u8m4xbiVun22F3dPc692X3Gw8Jjncd5T7Sn
        t+duz2EvZS+WV6PXzAqrFetX9HiTvIO8K72f+Oj78H26fGHfFb57fB+u1FrJW9nhB/y8/Pb4PfLX8U/z
        /z4AE+AfUBXwNNA0MDewN4gSFBXUFPQm2Dm4JPhBiG6IMKQ7VDI0MrQxdC7MNaw0bGSV8ar1q66HK4Rz
        wzsjsBGhEQ0Rs6vdVu9dPR5pEVkQObRGZ03WmqtrFdYmrT0TJRnFjDoZjY4Oi26K/sD0Y9YxZ2O8Yqpj
        ZlgurH2s52xHdhl7imPHKeVMxNrFlsZOxtnF7YmbineIL4+f5rpwK7kvEzwTahLmEv0SjyQuJIUltSbj
        kqOTT/FkeIm8nhSVlKyUgVSD1ILUkTSbtL1pM3xvfkM6lL4mvVNAFf1M9Ql1hVuFoxn2GVUZbzNDM09m
        SWfxsvqy9bN3ZE/kuOd8vQ61jrWuO1ctd3Pu6Hqn9bUboA0xG7o3amzM3zi+yWPT0c2EzYmbf8gzySvN
        e70lbEtXvnL+pvyxrR5bmwskCvgFw9tst9VsR23nbu/fYb5j/45PhezCa0UmReVFH4pZxde+Mv2q4quF
        nbE7+0ssSw7uwuzi7Rra7bD7aKl0aU7p2B7fPe1l9LLCstd7o/ZeLV9WXrOPsE+4b6TCp6Jzv+b+Xfs/
        VMZX3qlyrmqtVqreUT13gH1g8KDjwZYa5ZqimveHuIfu1nrUttdp15UfxhzOOPy0PrS+92vG140NCg1F
        DR+P8I6MHA082tNo1djYpNRU0gw3C5unjkUeu/mN6zedLYYtta201qLj4Ljw+LNvo78dOuF9ovsk42TL
        d1rfVbdR2grbofbs9pmO+I6RzvDOgVMrTnV32Xa1fW/0/ZHTaqerzsieKTlLOJt/duFczrnZ86nnpy/E
        XRjrjup+cHHVxds9AT39l7wvXbnsfvlir1PvuSt2V05ftbl66hrjWsd1y+vtfRZ9bT9Y/NDWb9nffsPq
        RudN65tdA8sHzg46DF645Xrr8m2v29fvrLwzMBQydHc4cnjkLvvu5L2key/vZ9yff7DpIfph4SOpR+WP
        lR7X/aj3Y+uI5ciZUdfRvidBTx6Mscae/5T+04fx/Kfkp+UTqhONk2aTp6fcp24+W/1s/Hnq8/npgp+l
        f65+ofviu18cf+mbWTUz/pL/cuHX4lfyr468Xva6e9Z/9vGb5Dfzc4Vv5d8efcd41/s+7P3EfOYH7IeK
        j3ofuz55f3q4kLyw8Bv3hPP74uYdwgAAAAlwSFlzAAALDAAACwwBP0AiyAAAAHxJREFUWEft1yEOhUAM
        hOGehjO9i9FLcJUnCAaCwXECCHJok5VNGiphxK+6Yz638h9ntVDId/IbFrVQyHfS9ZNaKOQ7WbddLRRS
        AOIAOM7rcQ3BAcJ7VkNwgPCe1RAcILxn+Y4ABCAAAQhAAAIQIHyQRQACEOA1AB/+DUJuSbRxiJ0Y19kA
        AAAASUVORK5CYII=
</value>
  </data>
  <data name="btnTime.tbBackgroundImage" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABQAAAATCAYAAACQjC21AAAABGdBTUEAALGPC/xhBQAAAEpJREFUOE/t
        1LEJACAMRFE3S+vWqV3EIkvEfHCDsxHy4UBSvNLh7lZbtRSHYYA7IlINAwvwnvSwGtRqUK9BvX/A5//h
        5IEurgyfB4e2rfC0KKiDAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="btnOK.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="wbsChat.LoadingGif" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        R0lGODlhWgBeAPcAAPf37/L09+/37+/35vfv9/fv7/fv5u/v9+/v7/fv3vfv1vfvzubv9+/u5ubv5u/v
        zubv3t7v997v3t7v1uPr7/fm5vfm3t7vzt7vxffmxdbvzvfmzvfmvf/mpdbvvebm5vfmtd7m9/fmrebm
        3ufmz/fmpffe3tbm99bm7+bmvd7m1uDmxd7mzs7m9/fe1vfezu/f3s7m79bnxdbmzu/e0sXm9//epf/e
        nNbmvf/ejP/elNbg5ebe0/fend7e5tbms//ehP/ee87mvc7mtffelPfejObew87mrffehO/enPfWzvfe
        e87e7+Hetd7b3vfWxc7lpe/W0sXmru/XxcXe98Xmpd7cve/WvcXe78XmnObW1b3e787inNbdtb3e98Xl
        lLXe9+bWvd7W1ujWtb3mjP/WY9bW1vfOxbXe7//Wa97YrdbelPfWctbbo8XZ5s7W4P/WWvfOvf/WUu/O
        xffOr/fWWsXepc7W1u/Ove/OssXX3t7WoeXOxb7enL3ejL3W4ubOvd7OzrXW5r3ehObOrbXW79HO063W
        973Wxa3W7rXejMXaiP/OSrXehP/OQs7WjPfFtbXee/bFrbXec9bOraXW7/fOSq3ee8HO4urFvcPO1K3e
        a63ec5zW7+nDtd7Fw6Xea7XO373Ova3O4qXeY/e9reHFnKXO7/e9pc7FxaXO4t7Btfe9nMXFzpzO78XF
        xa3WY5TO75TO5ua9nLXF1qXWUozO79a9vaXWWve1nITO76nF3pzWUve1lOa6lJ3WSqXF1pzWQua1peq1
        nJTWQpjF4JTWOrW9zpTWMYzF74TF74zF4fetjIzWIYzWKfethITF5nvF75y9znvF3nPF796tnPalhPel
        e++me4TOGXO973O95uSljGu95mO974S11vecc3S11mO15veUa1q171q15veUY++Uc1K15kq17/eMWlmt
        3kqt5kqt3kKt5jqt5veEUveESkKl3v97Sv97Ov97QjGl5iml5v9zMf5zOvdzMf9rKf9rIf9jIf9jGf9a
        ECH/C05FVFNDQVBFMi4wAwEAAAAh+QQEAgD/ACwAAAAAWgBeAAAIlQADCBxIsKDBgwgTKlzIsKHDhxAj
        SpxIsaLFixgzatzIsaPHjyBDihxJsqTJkyhTqlzJsqXLlzBjypxJs6bNmzhz6tzJs6fPn0CDCh1KtKjR
        o0iTKl3KtKnTp1CjSp1KtarVq1izat3KtavXr2DDih1LtqzZs2jTql3Ltq3bt3Djyp1Lt67du3jz6t3L
        t6/fv4ADLw0IACH5BAQCAP8ALA8AJgAJAAcAAAgpAAMEaGCggcCDChImRCCwgUKFDA08VBgAwcSEBgJI
        vCgQAYCPAAQECAgAIfkEBAIA/wAsCQAjABAACwAACE4AAwQQsICDQQ4JBCpUCACEiIcQFyxUmAGiRREc
        Jgq4eDHDQgMcL0oUCDKkRQMCAZi0CEKAwIorH44U4DAmCAApYV7koACnRgNAgU4MEBAAIfkEBAIA/wAs
        AwAfABYADwAACGoAAwgc2CCBgYEIEyJsAKJHkYdFRBxUmNAAEYgYi2SgONBixowlEHB0+BGkyIQJSpbs
        cXJgBpUlQSR8CfPjRpc1SzbAmRNkx542B5YAipHISQQkiRaRKRDB0J49QCRQqEBERiIiMmSYmjAgACH5
        BAQCAP8ALAMAHwAWAA8AAAhiAAMIHEiwoMGDCBMqXHhwARE2bJAsYChwQR1HGDOyKbBQQcaPGC0pUMgG
        JEiRCAuYNInS4IKVLDkWfAkT5BKDKmuCJGIQiU6QIAom+PmxjMyBIH5aQsIB4YIlINn06NGUYEAAIfkE
        BAIA/wAsAwAfABYAEAAACJYAAwgcWKCggIEIEyI0sCGDwwwLEiicmOChRYgFJg6s+LDEkjJl2JTIIHGi
        gY5yHKlcWYbkRAUORaycqZKRCAUKH5ahSdNmSYEFHvLk6XOjwxJDiWY4GOBkBqRJaaZZIBCB0Kg0l5Rc
        4JANVppEEAQImoHD15l1qAbgSOQrIzYiMhp9moZmGSRIShjQWACmxQUKDDBNGBAAIfkEBAIA/wAsAAAe
        AB4AEgAACNEAAwgUCCDBgoMHCwxcyLDhwAQdbkiceIODQocYBS6gyPFGhwQZHW7s2JFDyIUFSN4AkgYO
        nDJAKp4UyKEjEEaOcuqEo8PkyY45dArNyShHhoYHkgZI0BHO0KFFFSxkQJXBgZETdTx9ykiHgYFVqWKV
        CGQr1w4AAhwIy2DsSrNP4ZhcWzWAAY5a4Q5Ns4DugYERKZbROzRIg78LmVIMSlinHBEOM3AMQphRmRwL
        LjasSRGIU6Fw0qQBwkFB2pAKAqvksADkzMQcQMiWncH06wABAQAh+QQEAgD/ACwYACIACAAJAAAIKAAD
        HIhAkGCAAAULMkCYkCCDhg4hMnjYMADFggcvHjgYoAGDjRwDBAQAIfkEBAIA/wAsGAAfAAoADAAACD4A
        AwQ4cKLFiRAMDggk2KJhwxMKIzicGCJAwYkOGWCcqHGjwY4bGYCcGCHAyIYMBI4MoVBlQ4QCYzpIGLNm
        QAAh+QQEAgD/ACwYABwADQAPAAAITwADCDxwooXBFgwEKmRQo6HDGgkVPpwYMcLEhy0Etrj4MMABjg8Z
        MATZMIBFkhlPgkyo8mLFlQpbNmxxQGGAExgj2gwxU6fNAB9O+PwZICAAIfkEBAIA/wAsGAASABQAGQAA
        CI0AAwgcGOBEDTQI0dRgQLBhgBaVOkmcKLGGQ4FbKGqUiOZAwxobQx4iyCCkSYsYTYas5LGkypAnHr4M
        aRHNzI1oAtzEGWGnRjQnfFLcAlKoRINGO7G0aTQmU58on95EGUCqykoxB1oVyZDgVoqVWlz8qrSGx7E0
        u14MkHFipYVrCRZ9ezbuwB1b1NolGBAAIfkEBAIA/wAsGAAIABwAIwAACMkAAwgcSJDBCTAIEW6JQLCh
        Q4ERKkWbSJGiri0PHTLoVLFjxVgMMkLU5bEkRYYPI5hcORFlQZIsV7oUCCYmy1gFbcZsMXCLzpsDbf1k
        GZLBUJYnAvg8ahJMAI5MSzqFGbWj06pSVWLt2OLE1o4Man6daCuA2LFUzI6NpktgrLUo335N63YuQblV
        6Q7EyzRpQ74/QT4EbBNjRsIrK4UUidhjLL8iAzSm+Djy3ZW2IFuu67ETz81/O1bSDHqgRLZbFpd26CbR
        zNUOAwIAIfkEBAIA/wAsGAAIABwAJAAACM0AAwgcSLCgwYMIEypcyLChw4cBWlSyRZGiFwYQA1RKd6+j
        x472qGFkeILcx5MeyZ1Y2MIeypf33LVIeMIlzJfpRhqkdvMmtYMnevZcWZCnUJg/C9o8itJeQTRMb+oM
        EC0qTC8ETVpFiXXg1pddI35FOVOgl7EfnQ60hdZjUoFs294rK7Bq22gFtY5NZ1DvVnt0B/qNCvjg4KMy
        ER7uSW5qXsKuFi5GqZLhZI/u0Di87K4SxMXubGUMMLiz480e02kePXBZyMCsA6iJfTAgACH5BAQCAP8A
        LAAAAAABAAEAAAgEAAMEBAAh+QQEAgD/ACwXAAYAHwAmAAAI/wADCBxIUOABBggTFlzIcOABCidiSJwo
        8QSDhg0fUty48SJGhxE5ipQY4sBHBiNTSkRhkuEBlTBZLjyAYmOhWNFy5qyEZeSJhREoxmJ3r6jRova8
        9eTo0WDNGFvQHZ1qFN0Wjj8HBo1RyB7Vr/feFWI6MOIWr2C/sls6MYRBid7SpvXGUWDQLXLlXqVoMmLc
        vGDpUrwoES1gqvY6vnR1OC3bGAhjaGsMttLgoFIpU7U88UBQzV85x0ARIEQh0FTHkgxwohLqo4knXjwR
        7bVRwaMF0rZdVDVk3ZNta5tIoWxm1OzaEjxxXLM91TKNo36+uiBz0GIlFl8YojlgdD1RNFUtGMF7WnvK
        SLZkWP6w1dHj2Zuf+s5VDIsfBzKYX/RdrNER5EfQfl+9E819DKwnYAAHeOdfCxQouKBACxzHTicRTthQ
        ENPY080WEmpYkBxdhCjiQgEBACH5BAQCAP8ALBcAAQAjADAAAAj/AAMIHEiQYAgUCBOGKMiwIcMQWBJJ
        nEgRy0KHGAOEKESxY8dCFzMOPBDRo8mJIEUKZMDxpEuJWESyfEkz0RaMB1rWfBmzYcmdNE8wZAAU6IGC
        W4ru7LlSqdGBPynGitatatVkp4DGGOgxmrt7YMOCtVcuK81CAkNQdMVOrNuw7FzVFPgzlr23eO+9i0WT
        QYCWru7mxevO7Em/E8sNHlzuJQOiiVwtXiz38AmJiifnbXwYhUTBmt/ac8kgorLQgw1TRBtRHOq8yUzG
        TNr29dvYHhdKtI0X92qBiWLxfsu3IwrgyYaLHf2RazflYTlTDJnoOfR7xSce5+oauriOTIHXpx7uDjzD
        RONt28ueaHtB9MPXT3e4JX3ovSj9OsRifzI7s4UIlRF/qNmzzURYHCUSgZrFBZN+KqHQn1vvKCPRFiGp
        FICEg70TzYMaFhTChO90k0ghMUAY4kAM2OdhIhat6NAD47mjTIwyYhREN2QlsgMCBQSJQI4NyYEICh80
        oOSSDQxJJEELFBAAk1Q+2dABVC5p5ZVZSrklQwcUwKSCXzKEAAJkfhkQACH5BAQCAP8ALBkAKQAPAAoA
        AAhNAO8JdAfNjY8GCBsUOBAggL1yiXzAmEhxogUEAXZU3EjRQkMLHDkWCIAAZEiKFRoWOImyIUmTIUe6
        LBnSo8uGNCtevHmzQYUKCBkGCAgAIfkEBAIA/wAsGgAoABMADgAACG4A7wm89y5aojdOKiis0OBAgIcB
        BLqDpkeLkosYL76oALHcKDFRMorEaOFhyJEoLxYI4CJlShMBELxwifIhApojETxsgDPjyp09L0IEirPk
        0AA8Xb7QeTRmy5EvfjYNcKDCTI0WmE4dWqCrw6EBAQAh+QQEAgD/ACwaACcAFwARAAAIigDvCRz4rlui
        Qkw+NFjYoECAhxAH3nsXLZShQE/OaNx45gkMiAEEuoOmhw/HkxyVHHhYbpSYOShjblQZAKbMmxo/ZsR5
        88mBAjt5xqwQoIJQmS4eGj168gVEF0w5On0aVWNSkDCqIgD50ALTq1yLCn2yNezDAkp6OjSLNejGF2vZ
        cq0Ao66FlSADAgAh+QQEAgD/ACwZACUAKgAcAAAI8wDvCRxI8N47ZYkSbUFhQckZSZJ6SZw48UyFABgz
        ZixI8F20RIJaeYpIsaTJXpIsaNzI8V23RH9unZw58wyClQU9jkoljKbPk6husrznTpmmVT+TnoQkNIC9
        crs+5VJK1eSZjGaGVd1qsgFGBFO5iu0VJ2OFsGO3esVoIS3XshlduK2KauWZuVTXZoyDN6lKjQhQ9fX5
        F7DgwScLazyLuOvKjA3QNs71eLHkwXArY2Tc2IVmjQ0O9637GbTouZ5LA4aEV5Lqyk/c5rr4+rGFy0pn
        19aM4C5d2rs1NyDpE9WTpsE/VzhzeqKk1MmDI7BAnfrrgAAh+QQEAgD/ACwZACIAOwAoAAAI/wDvCRxI
        sOA9dqcSJSp0IoDDhwhcSJLErKLFi8wkPdzI8aHBj/fsbVOYCMsBhy7OSLo2rqXLlzBd9jrTsSPIguxc
        KcTCQCIrcDGDCnV5LU5NjzfvvVOmcIuYn0OjSrVm4ejNd9EUugHETKpXqeBo2jT4rtvCVMO+qvUqdmNB
        rIneCGO5tm7Utg4HulPmZpU1u4Cluthor1yiT70CK456baOZYUAXSxaq0aGFyZiDgkPw8Ezmzy4rO4wD
        +vPmjaxKZza6satqydY4Ivj7ejHnjRUi1wY8mKMF3bvXit6oJLjd4Rs9G1eLHPXyr82dP48aHSLt6UGV
        HN14HfvLCtu5e3R/2Ti8+PHjSpnn6Np77/UOESTGXh6+9OfV10t6ftp+R+XBseZfRy4AV1psAx4122vg
        gJfgdqWUBk5VD4anBF2TXUNhheEhsJ9kzNzG4XoWtFcXOPmNeJQLJkp1DV4qDlhBKd3F1EscIsY4YgVx
        TOTje/AFBAAh+QQEAgD/ACwYACIAPAAoAAAI/wDZ3RtIsKDBe+xOJUpU6ESAhxAjSpxIsWIAgQcz2tu2
        MBGWAxZDiqyIMWNBdq4WYmEAUYkka+DiyZw5U52SkSFLmnynbOGWEA1YmYv3r6jRo0j/7WPVAOdEnQbf
        RVv4Z5i6fkmzav3Xj5XTiFAHvuvGMNW5fVvTao3X9CtUqYkMcUOrtm7WfG1xlnSnTBO3fHYDa833tZu9
        cqP+Cl6sFZxTUdXgMZ6sNQ7OAuYoa0Ya7yvgzaAt42zwGTRldV8b0DU9Oe9IJVhZMxbtFLbsxda+Poxz
        W3Bu3QFY9bbbGXjw4WrNGX+YGbnW38utOc/aazlE6dOP3rT+sFf2ovu4R2EUnt2xeIisYiM3cT5iHPW3
        lbePqGQ1637s50dsQFR2df0TgcOafABO9N5m5rhWoEQCTpbggiEp0Z9d/fwHoUhxqKNWPpIoeGFIJrAC
        E03xWEPbhyimqOKKLLbo4oswOhUQACH5BAQCAP8ALAAAAAABAAEAAAgEAAMEBAAh+QQEAgD/ACwYACAA
        OgAqAAAI/wDZ3RtIsKBBgvZiJVqYKESAhw8PFJhIkeIBiBgzYhR4sCPBdwoXFmIgsUEFCyZSqlyZsgIC
        jTADcPR4kN2phYJ2nGTJs6cJCw0uxnw4k+ZAe9sK/dHjBIbPpz2BDpVplCA7VXrMiHGaElIucOPqiR07
        Fh4kqC5jFu34rtgdrTBeMFNX75/du3jz/tvH7IVPCy81ri24DpgZM4GYwfOnt7Hjf/6YPQ288eC7cIbN
        rBq377Fnx/X89iyQsWi7b7QMfcLW+bPrxvhEs7QglOjA05pW43vN2zE+nxUwbmsnLRU2fb2TOx7nkzIl
        bvCUS3eMKirEAuama89br/n13dvDV//naQGjCfDhp8PzHtFF6/TSZa8MDhEBJMbwlY+fndE+/vy8geOT
        Rg2gAmBvAo6mUQXMHPhadwpmdIAFDTromTo+kabRhOpY+FiCPGmoUQEmgONhY9ZkOBSJJp6I11k91QZT
        BSZY46Jd+zw11UM0Vngicz3Rt2OP/zl4hoo7BjChCagUmR+GPsk41ZKQvAefP0dalyRES75QV34psrdl
        RCiZMA58UGo5JpdlNrmdOvKxRNmaENGY0pnSwfmUkHRiZKcJkHz5mj9h/iVlnw8hUCaT0XmGTy5x8jQn
        ohgputIZzIBFVj3g7AeViJRu+CdUpP4EaqgwWVrqnoeiumEDi64fmhJgrq55AKyyWnBqrWMiUMFO5FWw
        K6+ISlRRq1MFBAAh+QQEAgD/ACwTAB8ARgAvAAAI/wADCAyQiN29gwgTKkxoL1aih4lQDBxYoIGFixgz
        VmgwsaPHjwUXilTYEGKiEAILVHChJI7LlzBjnrFQ4KPNiVsMjhz5zuHDQh9YxhxKNKaSCjdtYtG5UyG7
        U4kE6QlUtKpVl0+QJp24tCnDbYLu8LlK9qqSmjYzDOzq9R67UIHmxGTFbJw6enjz5p3HqizMM1o95lAg
        EAXTke2AyXUJ6Ro8ev8iS55M+d++a5D8urTwkZEOAwEM7wz3ivG1ef4qq179z981zXE4d3TEqAOAEIcP
        2vv2ySUrdftYC19NL7PfwAIdOYLDgcFhedJclgIXfLh11fiMkz3DcaByR2ke6P+EDmg6vuvoV+PT/GTi
        d0dBuq07Bgnc+fT4VavTLDvAezmSnJbfgKv14tcZBCTHSBk5LEACPARGSBk9/AkEBAcKAADBBTTcJ6GE
        BpaF4EQCXGBiHB5+OOA8FQ40gYkXrABJdSoOqJ1ZAzkAo4lGsJJajfmFWBZaO8I4hY9A4jeOZkhtWOSJ
        vSSZ3pJ+uRDAkzCSEMc1Ul5HoV9KDIAljBZs2eVw8Gh2hpNjOuBCHBCeuRqVfo0J4wQBtDSOnKqBA5ud
        MAZAwBNx7MnnZH1pBqiJKZ0RBziHRrYPbHEsegFFLnF56H6aKcEmlhBMVEGmP55ZCmxKlGinR2XG0Uup
        SabJCRtnCLz4pAAfvRkHKzTW6M+psAX2aag36QoJZED6CdsZWyX1wkvq1CgrbFY2axMBLbn0qoTw3OhX
        d9Z+hC1M0Q7YLaVxtBduUtn6hux1/iiLLnLrekTAszD1Ms9w+DDjbaf1NqsrTKVcY5de9IwjJLouoRVw
        UgMzPOvD1o4qsWYvUBxuA45ebFaCGls7rsdFKQFyyOG2SjJM1aJcbwHtegyYyxpXQKjET7jgMM0UVxCz
        iP3x7HIBQl31QgUnC610ABZlZMFGDwcEACH5BAQCAP8ALA8ALAAVAB4AAAjlAB0JFMioTI4FKeD9W8iw
        oSM4adIA4aAAAIQsY/A13HiDw4IEAUIKOJKlSh6NGxcCCMkyAIYsMLlI2pfyX8uQDmDqbMPKX8qbAUjq
        hLmn58abF4YOzdPrKMucSnVyyXOtYcshUYe2iVN1IcuLWYeOiaPQZkisYXUeeRJnnFkBaZViYDsupIe4
        Q6s0OBMnAFy8QzEU6DsBcF4BFQLgMDz0QkjGQ48EAAsZpoCklWFOwJz5AufKnjPDvEC5MoQAVTJXCfnZ
        sAeRQg1XEfA0teHTLSHETosb6N2wR3oD9evBNkwcwkMGBAAh+QQEAgD/ACwLACwAHQArAAAI/wAdCRw4
        kFGZHAuawPvHsKFDhwQdwUmTBggHBQA8RAqD76FHhjdChuSwIEGAkw4iTSKTp+NHhyBAZMB4smaALJNy
        coG072VDm0ADXMhJtA0rfz7/Ba2JYBBRomOO+lx68sdTooPi9Jq6FMLVp4viXHtJ9cvXp23Eflzq4ezV
        MXEWPgyKQKVbopGuxBk3FyiUu1e/PNnrEKgDwF9/nIkD7qdNs4ifRmoQR63Sml4jX81SofI1fzZxar46
        wULlXjUPj75KJoCLyjX/rr6KwXWck3VnXx108sVJDLq/1iZwEnJwoq1xH/864ffyq1BOin6eM9JJp9SJ
        TkCQ/emPCd2JZlMBH37SF/Lhz5c3rzp8dLvdPQSQ3R2C0PC8r3evfXIo9fw1TXecfTYhQMZxkfAHlIHB
        KcjWapE0RxVmxt0FhQMTBnUBFPBhdWGGGUIwwYgTEDhhQAAh+QQEAgD/ACwLACwAHQArAAAI/wAdCRw4
        kFGZHAvWwPvHsKFDhwQdwUmTBggHBQC+OFOD76FHhjdChuSwIEGAkwOcZQuWp+NHhyBAZMB4smaATdly
        woK072VDm0ADeMhJ9BErfz7/BbXJiyjRPUd9Lj1ZxSlRZHl6SV2a0irRWnGuvZwayavTRWE/Lr1g1uqe
        OAsfLm3a9uqVOOPkAh1a12mkJ3gdBqXblyiJM3HA/bTJtzBRXgXipFVqk5Rjqx4qSL7mzyaEy1aDBbAg
        uZdNMqAxB3Ah2abK1I9Psq7ZGHbOCydf1Cxrm2ikkwRqIut9Fehn4kQhMEZOtMpp5jnJPIcuvSZq6jar
        Yq/JFrpzm8OZK0KfTpxX0K7EPSz9QnzT1Ju2gw14Dx+0aPonL7zu+xt/zQG8mcULbv4BNUAVm/CiIC9k
        EFjggxBGKOGEFFZo4YVLBQQAIfkEBAIA/wAsAAAAAAEAAQAACAQAAwQEACH5BAQCAP8ALAoAKwAgACwA
        AAj/AB0JHEhwYB1C1+b9W8iwocOCBRmVKZLCDzyHGB3CKQgnTRogHBRAaNSsjb6MKG+oXMlhQYIAARBo
        aJbNmJqTKB2C2JlBAQCYQCWQykYUFyF+ORsCXQrUQRWiUAfN8pd0IdOlCCb8ggq1zdSqV4OS4QoVmRpf
        YMM6mEkWai0r2JKGzbqpLddIcHOGlTDELlkuVi5mvOpAq1+uyLpYMTd4qYAJTw9z3WQlzDiMTAtvlcwV
        h5Ur4Bw6hsyZ7C8VVuJcU9p0Qq3SZKuwCKOa6r+lE3DAJmtsAgs8cXpRbR1pd+wJRuLEYbUv6ASaxqH+
        mjBhinJIMLNGjg51yAQJL5TD1yxclzvUTdQbKIkDkzoy82WpQyCgJGZu+J2pCxhPGj9RMtQ5AJMEExTn
        XzaRyNdegQciSN0E9jF4YILUIZDVBGNN+KCF1PV1IIAVXjjBe/7hsKGIBsI33YYBPMgWfFU8OMF+BFLX
        CHykyAhhAIU9OFR0xmggIwQwPSbjj7D1piMCQEGg4xDQSYaejhJgpeMEGpRn1y/eXcnkUj3qqAEZpPxi
        5i+RdHmlgFfVeOWbb1YZVgBuwmmnnHPyaOedeVq5p45s9glmnW9C8KWgVyHgAKESOHBoWAEBACH5BAQC
        AP8ALAgAKwAkADMAAAj/AB0JHEiwYJ081+b9W8iwocOFQQpKZFQmx49G8B5qdHgDCJyCcNKkAcJBwQRO
        zfro28jyhsuXLjksSBAgAAIpzbIZa4OPpUYQQIFmUACgplEZvLIp5aVmn0+HRqNKleBHqdVLpvw9ZSi1
        KwIhxKxa7TNL69auUmVcEmvVmRRfW/+hNSoBJ1urv4ZgOzv3K667YjnpfTo3gIw+gNn66ZKR5VwJQ8Im
        biuliznHXoVUnSwW1xAr4zai1RCZM9sqQ8KA09jVwZDNpq0SGzIEz7WHXWUM+RVb8ZAuca6ZXSjVdZXe
        bI3RDhOn1/C0Q0Ah9z0kThxWTuUadT0k53TZtIHH/4FET3tN3bC/K+1De4r1OOqMIqD9V71Vz0NkKHlv
        lPQQZ/a1RdsQEOwXh1FCDHFcgFahNoQQAexXEwS0pRfgJQM6QMALNc1Am3QMKgXKgDNENSCIIY44IAI1
        cRddiCIOOIQENU0w4FowqkibDOcNiBiMGMpYk4z/wehghvPJiKJ9sxEpgYu02RWgH0QOMQGUtHESIC9V
        WoklbUl9Z4wUXV7Z5RBh9qbcmRMEcOYQfXjHGX5n0pjgmVLUBxgx7L05hAOG+TmEFJfwQsyhxIDSp6A1
        QSboo2fyOCSklMoIQX+VVgqhfHdmKuilUdnoqZ+SQjdql5uipdupMgrBYl+rsi3qamF0yfoqrTV95Wmb
        uHYlQad+CgFqr2ghMAGwAwqhAaDE9oqAA9BCeyuuAQEAIfkEBAIA/wAsAAAAAAEAAQAACAQAAwQEACH5
        BAQCAP8ALAAAAQBaAF0AAAj/AAMIHEiwoMGDCA8wiBDihMMTFBgcQEixosWLGDEeiHAihsePIFGEmJix
        pMmTFA9QQAGypcsIKGPK1NjRpU2QI2fq3BmAwc2fIUnyHJrRJ9CjMU4IJcr04AGWSI+iWNq0aoCaUY+G
        sMrVaFakVLnyhPr16AmxTL2WPRoWbUyyIAu5ikWXbqUtWSm43XnApStl0QILFqwMC9Kze2eqxQJ4sOPA
        ygohTTwzxMctjR9rlgyUAeW3HhlrHh2Y803Pn0329eiKNGllQGGmLmkUi2vXlX7Kno0xAuvbr3+i5n2R
        gsfMwB/jtTmceMWOtpOPNt3SOcaOlaSPzu0SsfXnMWJp/9fMvWXz7wePj39c/uNU9BU9rn9M3eNu+Om3
        zHdsOCT++IXsR1hL7/2HUAzZCRhNfecZSBCCCiZjHnEFJKDAhQokgEB8re1XGEgNJpbABhmUaGIGCxRw
        oHjzfegRCucVgEADFdRo440VNIBAWxgpcOKPJSaQHovjuZjURAUUUIEFJjTp5JNQNllBATxSlACQWCpg
        0AlESpdMfzGgQGOUZJb55JQZFYAlByUQgQQSRZSAYkFcjudKmDv4AIOZfPZZwYYWLQBkCW8W+mYRICxA
        UJ3JKZMICk5osWeflPLZgEVAgmDopkiIoKVAIXRJmis7RArDpGdAwgozzIDj6quwsv+iRKVn8qjmj0Vw
        uqkIQgYQgaiPyaKHFlrw8EQcrFgD67LMvsrMGbQ2aUFbBvzIga6cimCAr8AGBo0qdxA7Ry7XNGvuub24
        EO20BlV7oqbYbsoBAgwAC80u4WohyTPn9nvuNU+sa9CtJsIbb6FFZIBAh4EtE4oZWnjSi78U/xswrRUY
        ROKJB29KhAIJOiwGHrlUbPK/s9KqIkFXnkhox4X2MAq4JJd78s3NMiNwQRub6CbMSCRhBCrK4mx0s5BE
        C+hA7prIQa7x9iDCAixUMYXNR2ftqjU7E9S0iSIQIbbYJYAAggIGCOBAFpEMIYnWcIOTNK1LD1SAoFhm
        gPbKASD/IEMkgK8ASdxZc40xQgYkoLjiBvBNkAR+AB6JHySgQvjRF1da5UUODCE54F+QUPLlN0uitEwC
        TNDI54AfITrpJ+t8eEwQVMG65DiQwC/sFMteqQUxOTDD7ZL7sQINu/N+ru+VopR65MQD3gcLNBStfM7R
        moASBEJE//kRLFx9Pfanl4SA6t5/LgMLSmA9vqu5ZO/4RRN4nn7xLJCghPXvz02rSQ5A3/1wxwIe7O99
        rlJXtMxXvwF+rhEToJ4Lkqe8XmQPeBmBwASg50DAVUEFLHDBBMcHrWhlDCPn+1sHP3cBFZDABC6YGO+e
        kT0TXAojGmTbCiVXhQmogAZNYgXv/0pYPoucbwI7ZN0EfOgkSbhPa6ioIQYvosHuJVFyQ1jiCJz0BP4d
        zYI1PKFFUjcBHV5xckucQAOc5AIhZi1+NTTB5gYSwA2eUXIzWKIDCvCkM1DQZFGMoxgtksY7YjGNAkAA
        lCThxeURsYbzQ8gRcWBIwGUhjQ7oW5Qg0apzsUKBcTTBICuiwQlQspJ+SOMEBIIAJkHJBWdAhSxn+chQ
        sgsjaTylIVOZRkAVwJWhzF7dKnJEU1YSjWmEwEAOAMxgUuqGGKnjBIZXyUuqsiAVcCalRmmRUi7xmEdQ
        5QSG2UptlombFpGAKr9QyTyqMpMFOUA2zekkCwwTl6q03xl5qd1KZR6knOa85UnEaccz4oCg/nRKA5qZ
        vT/NhKC63OEXCDqBhCZkoQ2N5EAJqs8O+uECFLUoMWm0pHM2YI4ZoWj9VufAj6pUpBlBQJJmitKTqJOi
        F+Cg96ypUng6SCDepKgQdPo5PxxUpXr8KR2RmsYZCKEKUK3CEdzJ1HEqlZVVzSpTrzqQm2r1q0nlagCk
        CVatCkCsAvFqWZnqU7EWc60qlQBaCUJWuIrznmINql2XiFe01hWufZ0rAtRa1sDOVSB/rSoEznpYIyYW
        oYZtrEEQ4ADCVtQBjJWsZnUSEAAh+QQEAgD/ACwAAAEAWgBcAAAIlAADCBxIsKDBgwgTKlzIsKHDhxAj
        SpxIsaLFixgzatzIsaPHjyBDihxJsqTJkyhTqlzJsqXLlzBjypxJs6bNmzhz6tzJs6fPn0CDCh1KtKjR
        o0iTKl3KtKnTp1CjSp1KtarVq1izat3KtavXr2DDih1LtqzZs2jTql3Ltq3bt3Djyp1Lt67du3jz6t3L
        t6/fv4AxBgQAOw==
</value>
  </data>
  <data name="btnReCheckEx1.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnReturnView.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnOpenTutorial.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnMoreTutorials.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnReCheckEx.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnDownloadWeixin.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnReCheck.tbShowMoreIconImg" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAABGdBTUEAALGPC/xhBQAAABl0RVh0U29m
        dHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAABjSURBVChTY/j//z9RGKsgNoxVEBtm0HJKnqntkoYT
        g+TBCoHETOOAipkmgVUYGCQOkocpBCs2D2ucaRHRAscgPkwRskIUxeiKQBhZIQjPNAutx1AEwigcKMZQ
        BMIYAtjxfwYA1+QMde5yN5MAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="bgwLoadUsers.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>132, 17</value>
  </metadata>
  <metadata name="cmsExportEx.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>275, 17</value>
  </metadata>
  <metadata name="cmsRecoveryData.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>401, 17</value>
  </metadata>
  <metadata name="cmsFilter.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>558, 17</value>
  </metadata>
  <metadata name="cmsSearch.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>64</value>
  </metadata>
</root>