﻿Imports System.Threading
Imports System.Web
Imports System.Text.RegularExpressions
Imports System.ComponentModel
Imports System.Drawing.Imaging
Imports iTong.Android

Public Class frmAndroidWeixin

    Private mHelper As IMExportHelper
    Private mWechatHelper As AndroidWeixin = Nothing

    Private mDgvFriend As tbDataGridViewEx = Nothing
    Private mAppIconSize As New Size(44, 44)
    Private mDefaultChatCount As Integer = 50
    Private mDefaultHeadIcon As Image = Nothing
    Private mGraphic As Graphics = Nothing
    Private mPlayer As IPluginPlayer

    ' 微信下载地址
    Private mWeixinUrl As String = "http://andtui.api.tongbu.com/PcApi.ashx?ApkDownUrl&packageName=com.tencent.mm"
    Private mWechatTempFolderOnPC As String = String.Empty       '' 微信数据库在电脑上的临时目录
    Private mWechatPictureFolderOnPC As String = String.Empty    '' 所有微信图片在电脑上的缓冲目录

    Private mDictChatMsgs As New Dictionary(Of String, Chatmessage)     '' 当前web页展示的所有聊天信息（Chatmessage）
    'Private mDictWeixins As New Dictionary(Of String, DataType)
    Private mCurrentSelectUserName As String = String.Empty          '' 当前选定的微信联系人
    Private mCurrentAndroidDeviceName As String = String.Empty

    Private mLoadState As WeixinAndroidState = WeixinAndroidState.Unknow

    Private WithEvents bgwLoadWechat As New BackgroundWorker()

    Private mDTLoadDataStart As Date = Date.MinValue    '开始加载数据

    Private mKingRootHelper As KingRootHelper

    Private m_bInstalledExcel As Boolean = False

	Private mTdLoadAccount As Thread

	Private _bIsSearch As Boolean = False

    Private m_bIsInitial As Boolean = False
#Region "-- 私有枚举值 ---"

    ''导出类型
    'Private Enum ExportType
    '	CurrentPage				'备份当前页
    '	CurrentFriend			'备份当前联系人
    '	AllFriend				'备份全部联系人
    '	CopyToClipboard			'复制当前页
    'End Enum

	'消息展示在哪里
	Private Enum ChatMsgType
		ShowOnList				'展示在联系人列表上      
		ShowOnWeb				'展示在web页面上
		ShowOnTxtFile
		ShowOnExcelFile
	End Enum

	'界面Style
	Private Enum ViewStyle
		Welcome
		Loading
		Home
		Description
		DescriptionPwdBackup
		NoneContents
	End Enum

	'聊天数据的来源
	Private Enum DataType
		Normal = 0				'普通展示
		GetNews = 1				'查看更多消息
		Search = 2				'搜索
		TimeLocate = 3			'时间定位
		GetContextNews = 4		'查看前后消息
	End Enum

#End Region

#Region "--- 初始化 ---"

	Public Sub New(ByVal application As IApplication, ByVal device As AndroidDevice)

		' 此调用是 Windows 窗体设计器所必需的。
		InitializeComponent()

		' 在 InitializeComponent() 调用之后添加任何初始化。
		Me.FunctionMappingKey = FunctionKey.AndroidWeixin

		Me.mApplication = application
		Me.Language = application.Language
		Me.tbAutoSetFormSize = True
		Me.Size = New Size(870, 630)
		Me.Icon = My.Resources.iTong
		Me.mDefaultHeadIcon = My.Resources.weixin_icon_default.Clone

		Me.wbsChat.WebBrowserShortcutsEnabled = True

		Me.mAndroid = device
		Me.mCurrentAndroidDeviceName = mAndroid.DeviceName

		'Me.bgwLoadWechat.WorkerReportsProgress = True
		Me.mKingRootHelper = KingRootHelper.Instance()
        AddHandler Me.mKingRootHelper.InstallKingRootEvent, AddressOf OnInstallKingRoot

	End Sub


	Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Dim para As New IMInstancePara()
        para.AppType = IMAppType.AndroidWeChat

        Me.mHelper = IMExportHelper.Instance(para)

		Me.btnFavourite.Visible = False
		Me.btnMoment.Visible = False

		Me.btnRefresh.Enabled = False
		Me.txtSearchFriend.Size = New Size(220, 23)
		Me.dtpStart.Value = Now
		Me.dtpStart.MaxDate = Now

		Me.dtpEnd.Value = Now
		Me.dtpEnd.MinDate = Now

		Me.cmbTime.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
		Me.cmbFriend.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)

		Me.cmbTime.Size = New Size(Me.cmbTime.Width, 25)
		Me.cmbFriend.Size = New Size(Me.cmbFriend.Width, 25)

		'"当前联系人","全部"
		Me.cmbFriend.Items.Clear()
		Me.cmbFriend.Items.AddRange(New Object() {Me.Language.GetString("Weixin.Item.CurrentFriend"), _
												  Me.Language.GetString("Weixin.Item.All")})
		Me.cmbFriend.SelectedIndex = 0

		'"最近一周", "最近一个月", "最近三个月", "最近一年", "全部"
		Me.cmbTime.Items.Clear()
		Me.cmbTime.Items.AddRange(New Object() {Me.Language.GetString("Weixin.Item.Week"), _
												Me.Language.GetString("Weixin.Item.Month"), _
												Me.Language.GetString("Weixin.Item.ThreeMonth"), _
												Me.Language.GetString("Weixin.Item.Year"), _
												Me.Language.GetString("Weixin.Item.All")})

		Me.cmbTime.SelectedIndex = 0
		Me.tpnlContent.Location = New Point(1, 32)
		Me.pnlWelcome.Location = New Point(1, 32)
		Me.pnlWelcome.Size = New Size(868, 571)
		Me.pnlLoading.Location = New Point(1, 32)
		Me.pnlLoading.Size = New Size(868, 571)
		Me.pnlDescription.Location = New Point(1, 32)
		Me.pnlDescription.Size = New Size(868, 571)


		Me.pnlNoneContents.Location = New Point(1, 32)
		Me.pnlNoneContents.Size = New Size(868, 571)
		Me.pnlNoneContents.SendToBack()

		Me.InitDataGridView()
        Me.InitMusicPlayer()

        Me.bgwShowChatMsg.WorkerSupportsCancellation = True
        Me.bgwLoadChatMsgs.WorkerSupportsCancellation = True
        Me.bgwLoadWechat.WorkerSupportsCancellation = True


        'ChatWebPage.InitChatWebPage(False)
        While String.IsNullOrEmpty(ChatWebPage.WebPagePath) OrElse Not File.Exists(ChatWebPage.WebPagePath)
            ChatWebPage.InitChatWebPage(True)
        End While

        If Not Folder.LangType = LanguageType.en_US Then
            Common.SetImageLocation(Me.picLoading, GuiResource.gif_loading_24)
            Common.SetImageLocation(Me.picLoading_1, GuiResource.gif_loading_24)
        End If

        MainForm.SetButtonStatus(Me.btnExport)
        MainForm.SetButtonStatus(Me.btnRepair)

        Me.btnBackup.Visible = False
        Me.btnRestore.Visible = False

        'If IniSetting.GetShowWeixinWelcome() Then
        '	Me.SetViewStyle(ViewStyle.Welcome)
        'Else
        '	'' 加载微信记录
        '	Me.LoadWechatData()
        '	Me.CheckIsInstallExcel()
        'End If
    End Sub

    Protected Overrides Sub OnShown(e As System.EventArgs)
        MyBase.OnShown(e)

        If IniSetting.GetShowWeixinWelcome() Then
            Me.SetViewStyle(ViewStyle.Welcome)
        Else
            '' 加载微信记录
            Me.LoadWechatData()
            Me.CheckIsInstallExcel()
        End If
    End Sub
    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") '微信消息记录

        '菜单
        Me.txtSearchFriend.SearchTipText = Me.Language.GetString("Common.Search") '"搜索"
        Me.btnExport.Text = Me.Language.GetString("Media.Button.Export") '"导出"
        'Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh") '"刷新"
        Me.btnRepair.Text = Me.Language.GetString("App.Button.Repair")   '修复闪退
        Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh")                 '"刷新"
        Me.lblSearchRange.Text = Me.Language.GetString("Weixin.Lable.SearchRange") & Me.Language.GetString("Common.Symbol.Colon")  '"查找范围："
        'Me.lblSearchText.Text = Me.Language.GetString("Weixin.Lable.SearchContent") & Me.Language.GetString("Common.Symbol.Colon") '"查找内容："
        Me.txtSearchChat.SearchTipText = Me.Language.GetString("Weixin.tbSearch.SearchTip") '"按回车搜索消息记录" 

        Me.lblStartTime.Text = Me.Language.GetString("Weixin.Lable.StartTime") '"开始日期"
        Me.lblEndTime.Text = Me.Language.GetString("Weixin.Lable.EndTime") '"结束日期"
        Me.tsmiExcel.Text = Me.Language.GetString("Backup.Button.ExportToExcel") '"导出为Excel文件"
        Me.tsmiExcelCurrentFriend.Text = Me.Language.GetString("Weixin.Item.ExportSelected") '"导出选中联系人的消息记录"
        Me.tsmiExcelAll.Text = Me.Language.GetString("Weixin.Item.ExportAll") '"导出全部消息记录"
        Me.tsmiTxt.Text = Me.Language.GetString("Backup.Button.ExportToTxt") '"导出为Txt文件"
        Me.tsmiTxtCurrentFriend.Text = Me.Language.GetString("Weixin.Item.ExportSelected") '"导出选中联系人的消息记录"
        Me.tsmiTxtAll.Text = Me.Language.GetString("Weixin.Item.ExportAll") '"导出全部消息记录"
        Me.btnCopy.Text = Me.Language.GetString("Weixin.Button.CopyAllNews") '"复制全部消息"
        Me.btnNext.Text = Me.Language.GetString("Tools.Carrier.Button.Next") '下一步

        '欢迎页
        Me.lblWelcome.Text = Me.Language.GetString("Weixin.Lable.LookAllNews") '"查看微信消息记录"
        Me.lblWelcome1.Text = Me.Language.GetString("Weixin.Lable.SupportFuncion") '"·支持搜索和导出消息记录；"
        Me.lblWelcome2.Text = Me.Language.GetString("Weixin.Lable.SupportVersion") '"·支持微信4.0以上版本。"

        'loading页
        Me.lblLoading.Text = Me.Language.GetString("Weixin.Lable.LoadData") '"正在获取微信数据"
        Me.lblLoading_1.Text = Me.Language.GetString("Weixin.Lable.LoadData")
        Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Lable.NotInstall") '"尚未安装微信"
        Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Lable.DescriptionGetWeixin") '"请您点击“立即免费获取微信”后，再继续使用该功能。"
        Me.lblDownloadWeixin.Text = Me.Language.GetString("Weixin.Lable.GetWeixin") '"立即免费获取微信"
        'Me.lblSearchText.Location = New Point(Me.txtSearchChat.Left - 10 - Me.lblSearchText.Width, Me.lblSearchText.Top)
        Me.lblSearchRange.Location = New Point(Me.cmbFriend.Left - 3 - Me.lblSearchRange.Width, Me.lblSearchRange.Top)
        Me.btnCopy.Location = New Point(Me.pnlState.Right - Me.pnlState.Left - Me.btnCopy.Width - 3, Me.btnCopy.Top)
        Me.btnOK.Text = Me.Language.GetString("Common.OK")


        Me.tsmiPic.Text = Me.Language.GetString("Weixin.Lable.ExportPic")                               '"导出图片"
        Me.tsmiPicCurrentFriend.Text = Me.Language.GetString("Weixin.Lable.ExportPicSelectContact")     '"导出选中联系人的图片"
        Me.tsmiPicAll.Text = Me.Language.GetString("Weixin.Lable.ExportPicAll")                         '"导出全部图片"

        Me.tsmiAudio.Text = Me.Language.GetString("Weixin.Lable.ExportAudio")                           '"导出语音"
        Me.tsmiAudioCurrentFriend.Text = Me.Language.GetString("Weixin.Lable.ExportAudioSelectContact") '"导出选中联系人的语音"
        Me.tsmiAudioAll.Text = Me.Language.GetString("Weixin.Lable.ExportAudioAll")                     '"导出全部语音"

        Me.tsmiVideo.Text = Me.Language.GetString("Weixin.Lable.ExportSmallVideo")                      '"导出小视频"
        Me.tsmiVideoCurrentFriend.Text = Me.Language.GetString("Weixin.Lable.ExportSmallVideoSelectContact") '"导出选中联系人的小视频"
        Me.tsmiVideoAll.Text = Me.Language.GetString("Weixin.Lable.ExportSmallVideoAll")                '"导出全部小视频"

        Me.lblCancelPwdBackup.Font = Common.CreateFont("微软雅黑", 16.0!, FontStyle.Regular)

        Me.tsmiDelete.Text = Me.Language.GetString("Common.Delete")
        Me.tsmiSave.Text = Me.Language.GetString("Common.Button.Save")

        'If Me.mIsWeixinBackup Then
        '    Me.btnRepair.Visible = False
        '    Me.btnBackup.tbIconImage = My.Resources.btn_delete_4
        '    Me.btnRestore.tbIconImage = My.Resources.btn_save_4
        '    Me.btnBackup.Text = Me.Language.GetString("Common.Delete")
        '    Me.btnRestore.Text = Me.Language.GetString("Common.Button.Save")
        'Else
        '    Me.btnBackup.Text = Me.Language.GetString("Main.Button.Backup")
        '    Me.btnRestore.Text = Me.Language.GetString("Common.Button.Restore")
        'End If

        Me.btnBackup.Location = New Point(Me.btnExport.Right + 2, Me.btnBackup.Top)
        Me.btnRestore.Location = New Point(Me.btnBackup.Right + 2, Me.btnRestore.Top)

        'Me.btnStartRoot.Text = Me.Language.GetString("Weixin.Button.StartRoot")	 '开始root
        'Me.lblDiscriptionDetail.Text = Me.Language.GetString("Weixin.Message.KingRootDescription")  '  KingRoot是一款获取手机Root权限的工具，为用户提供简单 快捷的一键Root方案, Root过程安全稳定, 无需用户干预, 获 取权限后, 更可以管理敏感的隐私权限
        Me.lblAbout.Text = String.Format(Me.Language.GetString("Download.Message.ContactMe"), Utility.TongbuContact, Utility.TongbuTel)     '"如有疑问，请联系客服QQ：{0}    电话：{1}"
        'If ServerIniSetting.GetShowWechatBackupTool() Then
        '    Me.btnStepWechatAssistant.tbBackgroundImage = My.Resources.btn_weixinNotRoot_step1
        '    Me.btnStepWechatMedia.tbBackgroundImage = My.Resources.btn_weixinNotRoot_step2
        '    Me.btnStepRoot.tbBackgroundImage = My.Resources.btn_weixinNotRoot_step3
        'Else
        '    Me.btnStepWechatMedia.tbBackgroundImage = My.Resources.btn_weixinNotRoot_step1
        '    Me.btnStepRoot.tbBackgroundImage = My.Resources.btn_weixinNotRoot_step2
        'End If
    End Sub

    Private Sub InitDataGridView()
        Me.mDgvFriend = New tbDataGridViewEx()
        With Me.mDgvFriend
            .SuspendLayout()

            .tbMouseHoverResponse = True
            .RowTemplate.Height = 58
            .ColumnHeadersVisible = False
            .tbShowNoData = False
            .MultiSelect = False

            .Columns.Clear()

            '1、用户名（key）
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colBlank", "", 15, True, False, DataGridViewContentAlignment.MiddleLeft, True))

            '2、昵称（显示）
            Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "ColName", Me.Language.GetString("Common.Label.Name"), _
                                                                                                      165, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)
            '设置样式
            Dim cellStyle As New DataGridViewCellStyle()
            cellStyle.Font = Common.CreateFont(Me.Font.Name, 9, FontStyle.Bold)
            cellStyle.ForeColor = Color.Black
            colName.DefaultCellStyle = cellStyle

            .Columns.Add(colName)

            '3、时间
            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "ColTime", Me.Language.GetString("Common.Label.Name"), _
                                                                   50, True, True, DataGridViewContentAlignment.MiddleRight, True, DataGridViewTriState.False))
            .ResumeLayout()
        End With

        RemoveHandler mDgvFriend.SelectionChanged, AddressOf dgvFriend_SelectionChanged
        AddHandler mDgvFriend.SelectionChanged, AddressOf dgvFriend_SelectionChanged

        'AddHandler m_dgvFriend.RowStateChanged, AddressOf dgvFriend_RowStateChanged

        RemoveHandler mDgvFriend.MouseDown, AddressOf dgvFriend_MouseDown
        AddHandler mDgvFriend.MouseDown, AddressOf dgvFriend_MouseDown

        RemoveHandler mDgvFriend.MouseHover, AddressOf m_dgvFriend_MouseHover
        AddHandler mDgvFriend.MouseHover, AddressOf m_dgvFriend_MouseHover

        Utility.AddForm2Panel(Me.mDgvFriend, Me.pnlFriend)
    End Sub

    Private Sub dgvFriend_SelectionChanged(ByVal sender As Object, ByVal e As EventArgs)
        Dim lstRows As List(Of DataGridViewRow) = Me.mDgvFriend.SelectedRows()
        If lstRows.Count = 0 OrElse lstRows(0) Is Nothing Then
            ' 记录未加载完成
            Return
        End If

        Dim item As Rconversation = CType(lstRows(0).Tag, Rconversation)
        If item Is Nothing OrElse _
            (Not String.IsNullOrEmpty(mCurrentSelectUserName) AndAlso mCurrentSelectUserName.Equals(item.username)) Then
            Return
        End If

        mDgvFriend.Enabled = False
        'Me.ActiveControl = Me.wbsChat

        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        Me.ChangeStateText("")
        Me.mDictChatMsgs.Clear()

        Me.LoadChatMessage(lstRows(0))
    End Sub

    Private Sub dgvFriend_MouseDown(ByVal sender As Object, ByVal e As MouseEventArgs)
        If e.Button = Windows.Forms.MouseButtons.Right Then
            Me.cmsRepair.Show(Me.mDgvFriend, e.Location)
        End If
    End Sub

    Private Sub m_dgvFriend_MouseHover(ByVal sender As Object, ByVal e As EventArgs)
        If Me.ActiveControl IsNot Nothing AndAlso _
           String.Compare(Me.ActiveControl.Name, Me.mDgvFriend.Name, True) <> 0 Then

            '控制鼠标在左侧树节点移动的时候可以直接拖拽滚动条
            Me.ActiveControl = Me.mDgvFriend
        End If
    End Sub

    Private Sub InitMusicPlayer()
        Me.mPlayer = PluginPlayer.Instance()
        Me.mPlayer.PlayMode = UserPlayMode.OrderPlay

        If Me.mPlayer.GetUserPlaylist(Me.Name) Is Nothing Then
            Me.mPlayer.AddUserPlaylist(Me.Name)
        End If

        AddHandler Me.mPlayer.PlayStateChange, AddressOf OnPlayStateChange
    End Sub

    '播放语音停止事件
    Private Sub OnPlayStateChange(ByVal sender As Object, ByVal e As MediaPlayStateChangedEventArgs)
        If e.NewPlayState = WMPPlayState.wmppsStopped Then
            If Me.mPlayer.CurrentMedia IsNot Nothing Then
                Dim strFilePlaying As String = Me.mPlayer.CurrentMedia.sourceURL
                Me.mPlayer.GetUserPlaylist(Me.Name).Remove(strFilePlaying)
                Me.ShowAudioWebView(strFilePlaying, False)
            End If
        End If
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        '关闭窗体
        Me.Close()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        Try
            RemoveHandler mDgvFriend.SelectionChanged, AddressOf dgvFriend_SelectionChanged

            Try
                If Me.mTdLoadAccount IsNot Nothing AndAlso Me.mTdLoadAccount.ThreadState <> ThreadState.Stopped Then
                    Me.mTdLoadAccount.Abort()
                End If
            Catch
            End Try

            If Me.bgwLoadWechat.IsBusy() Then
                Me.bgwLoadWechat.CancelAsync()
            End If

            If Me.bgwLoadChatMsgs.IsBusy() Then
                Me.bgwLoadChatMsgs.CancelAsync()
            End If

            RemoveHandler Me.mPlayer.PlayStateChange, AddressOf OnPlayStateChange

            Try
                If Not String.IsNullOrEmpty(Me.mWechatTempFolderOnPC) AndAlso Directory.Exists(mWechatTempFolderOnPC) Then
                    Dim sTmp As String = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
                    Dim aryFolders As String() = sTmp.Split("\\")
                    sTmp = aryFolders(0) + "\" + aryFolders(1) + "\Temp"

                    If Directory.Exists(sTmp) Then
                        Folder.ClearFolder(sTmp)
                    End If

                    Folder.ClearFolder(mWechatTempFolderOnPC)
                End If
            Catch
            End Try

        Catch

        End Try

        If mWechatHelper IsNot Nothing Then
            Me.mWechatHelper.DisposeWechat()
            RemoveHandler Me.mWechatHelper.LoadDataCallBack, AddressOf OnLoadDataCallBack
        End If

        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub

#End Region

#Region "--- 数据加载、获取联系人 ---"

    Private Sub LoadWechatData(Optional ByVal blnRefresh As Boolean = False)
        Me.ShowStateInfo(WeixinAndroidState.Loading)
        Me.ChangeTitleText(Me.Language.GetString("Weixin.Message.WelcomeTitle")) '"欢迎使用微信消息记录管理器"
        Me.ChangeStateText("")
        Me.ChangeButtonRefreshEnable(False)

        'Me.mDictWeixins.Clear()
        Me.mCurrentSelectUserName = String.Empty
        If Me.bgwLoadWechat.IsBusy() Then
            Me.bgwLoadWechat.CancelAsync()
        End If

        Me.bgwLoadWechat.RunWorkerAsync(blnRefresh)

    End Sub

    Private Sub bgwLoadWechat_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwLoadWechat.DoWork
        Dim result As WeixinAndroidState = WeixinAndroidState.Unknow

        Try
            Me.mDTLoadDataStart = Date.Now

            Dim blnRefresh As Boolean = CType(e.Argument, Boolean)
            If blnRefresh AndAlso mAndroid IsNot Nothing Then
                Me.mAndroid.Connect(mAndroid.DeviceID)
            End If

            Me.mWechatHelper = New AndroidWeixin(Me.mAndroid)

            RemoveHandler Me.mWechatHelper.LoadDataCallBack, AddressOf OnLoadDataCallBack
            AddHandler Me.mWechatHelper.LoadDataCallBack, AddressOf OnLoadDataCallBack

            RemoveHandler Me.mWechatHelper.DownloadedCallBack, AddressOf OnDownloadedCallBack
            AddHandler Me.mWechatHelper.DownloadedCallBack, AddressOf OnDownloadedCallBack

            'Common.Log("weixinload: 1")
            result = Me.mWechatHelper.LoadData(Me.mWechatTempFolderOnPC, Me.mWechatPictureFolderOnPC)
            'Common.Log("weixinload: 2")

            '通过服务器获取 UIN
            Me.TdLoadAccount()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadWechat_DoWork of frmAndroidWeixin")
        End Try

        'If result = WeixinAndroidState.Succeed Then
        '    Me.DownloadWechatIcon(Nothing)
        'End If

        '回传信息
        Dim strError As String = Me.GetStateDesByState(result)
        Me.UploadData2Server(New Object() {result, ModelKey.AndroidWeixinLoad, strError})

        e.Result = result
        mLoadState = result

    End Sub

    Private Sub bgwLoadWechat_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwLoadWechat.RunWorkerCompleted
        Try
            If e.Result Is Nothing OrElse String.IsNullOrEmpty(ChatWebPage.WebPagePath) OrElse wbsChat Is Nothing OrElse wbsChat.IsDisposed Then Exit Sub

            Dim state As WeixinAndroidState = CType(e.Result, WeixinAndroidState)
            'Common.Log("weixinload: 3")
            If state <> WeixinAndroidState.Succeed Then
                '' 显示当前状态
                'Common.Log("weixinload: 4")
                Me.ShowStateInfo(state)
                'Common.Log("weixinload: 5")
                Return
            End If

            If mWechatHelper Is Nothing OrElse mWechatHelper.DictRConversations Is Nothing Then
                Me.lblNoneWeixin.Text = String.Format(Me.Language.GetString("Weixin.Message.LoadFailNotRecord"), mWechatHelper.selfuser.weixinno)         '微信号 {0} 没有聊天记录。
                Me.SetViewStyle(ViewStyle.NoneContents)
                Return
            End If

            'While Not File.Exists(ChatWebPage.WebPagePath)
            '	'Common.Log("weixinload: 6")
            '	ChatWebPage.InitChatWebPage(True)
            '	'Common.Log("weixinload: 7")
            'End While

            If String.IsNullOrEmpty(ChatWebPage.WebPagePath) Then Exit Sub

            Me.wbsChat.Navigate(ChatWebPage.WebPagePath)

            'Me.wbsChat.Navigate("http://local.tongbu.com/wx")

            '         Common.Log("weixinload: 8")
            Me.SetViewStyle(ViewStyle.Home)
            '         Common.Log("weixinload: 9")
            '         Common.Log("weixinload: 10")
            Me.AddWechatToDataGridView()

            '         Common.Log("weixinload: 11")

            '更新头像
            UpdateAllHeadIcon(Nothing)


            'Common.Log("weixinload: 12")
            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Lable.InfoToSelect")) '"查看消息，请选中左侧联系人"
            'Common.Log("weixinload: 13")
            Me.ChangeTitleText(Me.Language.GetString("Weixin.Message.WelcomeTitle")) '"欢迎使用微信消息记录管理器"
            'Common.Log("weixinload: 14")
            Me.ChangeButtonRefreshEnable(True)
            'Common.Log("weixinload: 15")

        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadWechat_RunWorkerCompleted of frmAndroidWeixin")
        End Try

        'Common.LogException(Now.Subtract(Me.mDTLoadDataStart).TotalSeconds, "LoadWeixin")
    End Sub

    Private Sub OnLoadDataCallBack(sender As Object, e As iTong.Android.AndroidWeixinEventArgs)
        Try
            If e.State = WeixinAndroidState.ReadDBDataFailed Then
                ''' 读取微信数据库失败，可能是微信一直在接收信息
                ''' 提示在设备端先暂时关闭微信
                tbMessageBox.Show(Me, "请在设备端先暂时关闭微信，然后重新获取", _
                                  Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
            Else
                Me.lblLoading.Text = String.Format("{0} {1}%", Me.GetStateDesByState(e.State), e.Progress.ToString())   '"正在获取微信数据" 
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnLoadDataCallBack")
        End Try
    End Sub

    Private Sub OnDownloadedCallBack(ByVal sender As Object, ByVal e As EventArgs)
        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf Me.DownloadWechatIcon), Nothing)
    End Sub

    Private Function GetStateDesByState(state As WeixinAndroidState) As String
        Dim result As String = ""

        Select Case state
            Case WeixinAndroidState.Unknow
                result = Me.Language.GetString("Login.Message.UnknowError") ' "未知错误"

            Case WeixinAndroidState.Succeed
                result = ""

            Case WeixinAndroidState.Loading, WeixinAndroidState.CheckWeixinInstall, WeixinAndroidState.CheckRoot, _
                WeixinAndroidState.Downloading, WeixinAndroidState.AnalysingDB, WeixinAndroidState.AnalysingFailedRetry
                result = Me.Language.GetString("Weixin.Lable.LoadData") '"正在获取微信数据"

            Case WeixinAndroidState.Installed


            Case WeixinAndroidState.NoInstalled
                result = Me.Language.GetString("Weixin.Lable.NotInstall") '"尚未安装微信"

            Case WeixinAndroidState.NoRoot
                result = Me.Language.GetString("Weixin.Lable.NotRootDescription")    '设备尚未Root,无法读取微信配置信息

            Case WeixinAndroidState.NoLogin
                result = Me.Language.GetString("Weixin.Lable.NotLogin") '"尚未登录微信"

            Case WeixinAndroidState.NoRoot
                result = Me.Language.GetString("Weixin.Lable.NotRootDescription")    '设备尚未Root,无法读取微信配置信息

            Case WeixinAndroidState.SuperLimited
                result = Me.Language.GetString("Weixin.Message.NotGetWeixinError")   '暂未读取到微信的相关信息，可能是第三方权限限制或Root

            Case WeixinAndroidState.DownloadDBFailed
                result = String.Format(Me.Language.GetString("Weixin.Message.DownloadDbFail"), mAndroid.DeviceName) '下载 {0} 设备上微信数据库失败！

            Case WeixinAndroidState.NoDB, WeixinAndroidState.ReadDBDataFailed
                result = String.Format(Me.Language.GetString("Weixin.Label.DecryptFail"), mAndroid.DeviceName)       '解密 {0} 设备上微信数据库失败
        End Select

        Return result
    End Function

    Private Function CompressImage(ByVal img As Image) As Image
        Dim wid As Integer = Me.mDefaultHeadIcon.Width \ 3
        Dim hei As Integer = Me.mDefaultHeadIcon.Height \ 3

        Dim result As New Bitmap(wid, hei)

        If img IsNot Nothing Then
            Dim g As Graphics = Graphics.FromImage(result)

            g.FillRectangle(New SolidBrush(Color.White), New Rectangle(0, 0, wid, hei))
            g.DrawImageUnscaledAndClipped(img, New Rectangle(0, 0, wid, hei))
            g.Dispose()
        End If


        Return result
    End Function
    Private Function MergerImage(ByVal aryBmpStrs As String()) As Image

        Dim result As Bitmap = Nothing
        Try

            Dim len As Integer = aryBmpStrs.Length
            If len = 0 Then Return Nothing

            Dim aryImages(len - 1) As Image
            Dim i As Integer = 0
            Try
                For Each s As String In aryBmpStrs
                    If String.IsNullOrEmpty(s) Then Continue For

                    If s.Equals("Default") Then
                        aryImages(i) = CompressImage(Me.mDefaultHeadIcon)
                    Else

                        If File.Exists(s) Then
                            aryImages(i) = CompressImage(Image.FromFile(s))
                        End If

                    End If

                    i += 1
                Next
            Catch ex As Exception
                Common.LogException(ex.ToString(), "1.MergerImage of frmAndroidWeixin")
            End Try


            Dim wid As Integer = Me.mDefaultHeadIcon.Width
            Dim hei As Integer = Me.mDefaultHeadIcon.Height

            result = New Bitmap(wid, hei)
            Dim g As Graphics = Graphics.FromImage(result)

            g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic
            g.FillRectangle(New SolidBrush(Color.White), New Rectangle(0, 0, wid, hei))

            Dim imgX As Integer = wid \ 3
            Dim imgY As Integer = hei \ 3

            For i = 0 To len - 1
                If aryImages IsNot Nothing AndAlso aryImages(i) IsNot Nothing Then
                    g.DrawImageUnscaledAndClipped(aryImages(i), New Rectangle((i Mod 3) * imgX, (i \ 3) * imgY, imgX, imgY))
                End If
            Next

            g.Dispose()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "2.MergerImage of frmAndroidWeixin")

            result = Nothing
        End Try

        Return result
    End Function
    Private Sub UpdateAllHeadIcon(ByVal obj As Object)
        Try
            Dim strHeaderFolder As String = Path.Combine(mWechatPictureFolderOnPC, "mmhead")
            Folder.CheckFolder(strHeaderFolder)

            For Each row As tbDataGridViewRow In Me.mDgvFriend.Rows
                Dim item As Rconversation = CType(row.Tag, Rconversation)
                If item Is Nothing Then
                    Continue For
                End If

                Dim strUserName As String = item.username
                If mWechatHelper.IsChatRoom(strUserName) Then
                    '' 是群组继续
                    'Continue For

                    Dim strFName As String = Path.Combine(strHeaderFolder, strUserName)
                    If Not File.Exists(strFName) Then
                        '' 合并群中头像

                        Dim members As String = IIf(mWechatHelper.DictChatrooms.ContainsKey(strUserName), mWechatHelper.DictChatrooms(strUserName).memberlist, "")

                        If members.Length > 0 Then
                            Dim aryBmps(8) As String

                            Dim aryMembers As String() = members.Split(New String() {";"}, StringSplitOptions.RemoveEmptyEntries)
                            Dim i As Integer = 0

                            For Each mm As String In aryMembers
                                Dim strMmName As String = Path.Combine(strHeaderFolder, mm)

                                If File.Exists(strMmName) Then
                                    aryBmps(i) = strMmName
                                Else
                                    aryBmps(i) = "Default"
                                End If

                                i += 1

                                If i >= 9 Then Exit For
                            Next

                            If aryBmps IsNot Nothing AndAlso aryBmps.Length > 0 Then

                                Dim img As Image = MergerImage(aryBmps)
                                If img IsNot Nothing Then
                                    img.Save(strFName)
                                End If

                            End If

                        End If

                    End If
                End If

                '不存在,并且不是群组则去下载
                'Dim strImagePath As String = Path.Combine(strHeaderFolder, strUserName)
                'If Not File.Exists(strImagePath) AndAlso mWechatHelper.DictHeadimgs.ContainsKey(strUserName) Then ''AndAlso Not Me.mWechatHelper.IsChatRoom(strUserName)
                '    Dim header As HeaderImage = mWechatHelper.DictHeadimgs(strUserName)
                '    If Not String.IsNullOrEmpty(header.Img132Url) Then
                '        Common.DownloadImage(header.Img132Url, 20000, strImagePath)
                '    End If
                'End If

                '更新头像到界面
                UpdateHeadIconToDataGridView(row, strUserName)
            Next


        Catch ex As Exception
            Common.LogException(ex.ToString(), "UpdateAllHeadIcon of frmAndroidWeixin")
        End Try
    End Sub

    '''' 下载微信的相关头像——Added by Utmost20160217
    Private Sub DownloadWechatIcon(ByVal objParam As Object)

        If mWechatHelper.DictRConversations Is Nothing Then Return

        Try
            Dim strHeaderFolder As String = Path.Combine(mWechatPictureFolderOnPC, "mmhead")
            Folder.CheckFolder(strHeaderFolder)

            For Each item As Rconversation In mWechatHelper.DictRConversations.Values
                Dim usrName As String = item.username
                If usrName.StartsWith("gh_", StringComparison.OrdinalIgnoreCase) OrElse mWechatHelper.IsChatRoom(usrName) Then
                    ''' 先屏蔽订阅号
                    Continue For
                End If

                Dim strImagePath As String = Path.Combine(strHeaderFolder, usrName)
                Try
                    If Not File.Exists(strImagePath) AndAlso mWechatHelper.DictHeadimgs.ContainsKey(usrName) Then
                        Dim header As HeaderImage = mWechatHelper.DictHeadimgs(usrName)
                        If Not String.IsNullOrEmpty(header.Img132Url) Then
                            Common.DownloadImage(header.Img132Url, 20000, strImagePath)
                        End If
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "1.DownloadWechatIcon of frmAndroidWeixin")
                End Try

            Next


            ''' 下载微信登录的帐号头像——20160126
            Dim strImageName As String = Path.Combine(strHeaderFolder, mWechatHelper.selfuser.weixinname)
            If Not File.Exists(strImageName) AndAlso Not String.IsNullOrEmpty(mWechatHelper.selfuser.HeadIconAddr) Then
                Common.DownloadImage(mWechatHelper.selfuser.HeadIconAddr, 20000, strImageName)
            End If

            ''' 下载帐号不在最近会话中的头像
            ''' 
            If mWechatHelper.DictHeadimgs.Count > 0 Then

                For Each usrKey As String In mWechatHelper.DictHeadimgs.Keys
                    strImageName = Path.Combine(strHeaderFolder, usrKey)
                    If Not File.Exists(strImageName) Then
                        Dim header As HeaderImage = mWechatHelper.DictHeadimgs(usrKey)
                        If Not String.IsNullOrEmpty(header.Img132Url) Then
                            Common.DownloadImage(header.Img132Url, 20000, strImageName)
                        End If
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadWechatIcon of frmAndroidWeixin")
        End Try
    End Sub

    Private Sub UploadData2Server(ByVal objParam As Object)
        Try
            Dim state As WeixinAndroidState = CType(objParam(0), WeixinAndroidState)
            Dim mdl As ModelKey = CType(objParam(1), ModelKey)
            Dim errMsg As String = CType(objParam(2), String)
            Dim fsc As FunctionSucceed = FunctionSucceed.Succeed

            If state <> WeixinAndroidState.Succeed Then
                fsc = FunctionSucceed.Failure
            End If

            ActionCollectHelper.OperateAndroidWeixin(Me.mAndroid, mdl, mdl, ActionDataType.View, fsc, errMsg)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "UploadData2Server of frmAndroidWeixin")
        End Try
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnExport_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Click
        If Me.mDgvFriend.Rows.Count = 0 Then
            '联系人为空。
            tbMessageBox.Show(Me, Me.Language.GetString("Contact.Message.ContactEmpty"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Me.SetMenuEnable()
        Me.cmsExport.Show(Me.btnExport, 0, Me.btnExport.Height + 3)
    End Sub

    Private Sub btnExport_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnExport.Resize
        Me.btnRepair.Location = New Point(Me.btnExport.Right + 2, Me.btnRepair.Location.Y)
        Me.btnRefresh.Location = New Point(Me.btnExport.Right + 2, Me.btnRefresh.Top)
    End Sub

    Private Sub btnRepair_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRepair.Click
        Dim frm As New frmWeixinRepair(Me.mApplication, Me.mDevice)
        frm.StartPosition = FormStartPosition.CenterParent
        frm.ShowDialog(Me)
    End Sub

    Private Sub cmbFriend_Resize(ByVal sender As Object, ByVal e As System.EventArgs) Handles cmbFriend.Resize, cmbTime.Resize
        Try
            Me.cmbFriend.Location = New Point(Me.cmbFriend.Left, (Me.cmbFriend.Parent.Height - Me.cmbFriend.Height) / 2)
            Me.cmbTime.Location = New Point(Me.cmbTime.Left, (Me.cmbTime.Parent.Height - Me.cmbTime.Height) / 2)
        Catch ex As Exception
        End Try
    End Sub

    Private Sub txtSearchChat_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtSearchChat.KeyDown
        If e.KeyCode <> Keys.Enter Then
            Return
        End If

        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)

        Dim strSearchText As String = Me.txtSearchChat.Text.Trim()
        'If strSearchText.Length = 0 Then
        '    Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.NoSearchContent"))   '"搜索内容不能为空"
        '    Return
        'End If

        '开始搜索
        '1、区域范围
        Dim blnSearchAllFriends As Boolean = False
        If Me.cmbFriend.SelectedIndex = 1 Then
            blnSearchAllFriends = True
        End If

        '2、时间范围
        Dim StartTime As DateTime = DateTime.MinValue
        Dim EndTime As DateTime = Now
        Dim blnSearchAllTime As Boolean = True

        If Me.cmbTime.SelectedIndex <> 4 Then
            blnSearchAllTime = False
            Dim intDays As Integer = 0
            Select Case Me.cmbTime.SelectedIndex
                Case 0
                    intDays = 7
                Case 1
                    '最近一个月
                    intDays = 30
                Case 2
                    '最近三个月
                    intDays = 30 * 3
                Case 3
                    '最近一年
                    intDays = 30 * 12
            End Select

            StartTime = EndTime.AddDays(-intDays)
            'EndTime = EndTime.AddMonths(1)
        End If

        _bIsSearch = True
        '3、执行搜索
        If blnSearchAllTime Then
            Me.StartToSearchChat(strSearchText, blnSearchAllFriends)
        Else
            Me.StartToSearchChat(strSearchText, blnSearchAllFriends, StartTime, EndTime)
        End If

    End Sub

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        If mDictChatMsgs IsNot Nothing Then
            Me.mDictChatMsgs.Clear()
        End If

        Me.mWechatHelper.DisposeWechat()

        '' 重新连接一下
        If Me.mAndroid IsNot Nothing Then
            Me.LoadWechatData(True)
        Else
            Me.lblWeixinDescription.Height = 18
            Me.btnDownloadWeixin.Visible = False
            Me.lblDownloadWeixin.Visible = False
            Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Message.DeviceDisconnect")    '安卓设备可能断开
            Me.lblWeixinDescription.Text = String.Format("{0} " & Me.Language.GetString("Weixin.Message.DeviceDisconnect"), mCurrentAndroidDeviceName)
            Me.picWeixinIcon.Visible = False
            Me.picError.Visible = True
            Me.lblReCheckEx.Visible = True
            Me.SetViewStyle(ViewStyle.Description)
        End If
    End Sub

    Private Sub txtSearchFriend_TextChangedByTimer(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtSearchFriend.TextChangedByTimer
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
        Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Lable.InfoToSelect")) '"查看消息，请选中左侧联系人"
        Me.ChangeTitleText(Me.Language.GetString("Weixin.Message.WelcomeTitle")) '"欢迎使用微信消息记录管理器"
        Me.StartToSearchFriend(Me.txtSearchFriend.Text.Trim())

    End Sub

    Private Sub dtpStart_CloseUp(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpStart.CloseUp
        Me.dtpEnd.MinDate = Me.dtpStart.Value
    End Sub

    Private Sub dtpEnd_CloseUp(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles dtpEnd.CloseUp
        Me.dtpStart.MaxDate = Me.dtpEnd.Value
    End Sub

    Private Sub dtpEnd_Resize(ByVal sender As Object, ByVal e As System.EventArgs) Handles dtpEnd.Resize, dtpStart.Resize
        Try
            Me.dtpStart.Location = New Point(Me.dtpStart.Left, (Me.dtpStart.Parent.Height - Me.dtpStart.Height) / 2)
            Me.dtpEnd.Location = New Point(Me.dtpEnd.Left, (Me.dtpEnd.Parent.Height - Me.dtpEnd.Height) / 2)
        Catch ex As Exception
        End Try
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        ChatWebPage.ClearChatOnWebPage(Me.wbsChat)

        Dim startTime As DateTime = Me.dtpStart.Value
        Dim endTime As DateTime = Me.dtpEnd.Value

        Me.GetChatMsgForTimeLocation(startTime, endTime)
    End Sub

    Private Sub btnCopy_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCopy.Click
        Me.Export(True, ExportType.CopyToClipboard, True)
    End Sub

    Private Sub btnReCheckEx_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReCheckNone.Click, lblReCheckEx.Click
        Me.LoadWechatData(True)
    End Sub

    Private Sub tsmiTxtCurrentFriend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiTxtCurrentFriend.Click
        Me.Export(True, ExportType.CurrentFriend, True)
    End Sub

    Private Sub tsmiTxtAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiTxtAll.Click
        Me.Export(True, ExportType.AllFriend, True)
    End Sub

    Private Sub tsmiExcelCurrentFriend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExcelCurrentFriend.Click
        Me.Export(False, ExportType.CurrentFriend, False)
    End Sub

    Private Sub tsmiTxtCurrentFriendTime_Click(sender As Object, e As EventArgs) Handles tsmiTxtCurrentFriendTime.Click
        Me.Export(True, ExportType.CurrentFriendByTime, True)
    End Sub

    Private Sub tsmiExcelCurrentFriendTime_Click(sender As Object, e As EventArgs) Handles tsmiExcelCurrentFriendTime.Click
        Me.Export(False, ExportType.CurrentFriendByTime, False)
    End Sub

    Private Sub tsmiExcelAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExcelAll.Click
        Me.Export(False, ExportType.AllFriend, False)
    End Sub

    Private Sub tsmiPicCurrentFriend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiPicCurrentFriend.Click
        Dim nickName As String = tsmiPicCurrentFriend.Tag
        Me.ExportPhotoByUserId(True, nickName)
    End Sub

    Private Sub tsmiPicAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiPicAll.Click
        Me.ExportPhotoByUserId(False)
    End Sub

    Private Sub tsmiAudioCurrentFriend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiAudioCurrentFriend.Click
        Dim nickname As String = tsmiAudioCurrentFriend.Tag
        Me.ExportAudioByUserId(True, nickname)
    End Sub

    Private Sub tsmiAudioAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiAudioAll.Click
        Me.ExportAudioByUserId(False)
    End Sub

    Private Sub tsmiVideoCurrentFriend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiVideoCurrentFriend.Click
        Dim nickname As String = tsmiAudioCurrentFriend.Tag
        Me.ExportVideoByUserId(True)
    End Sub

    Private Sub tsmiVideoAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiVideoAll.Click
        Me.ExportVideoByUserId(False)
    End Sub

    Private Sub tsmiDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiDelete.Click
        Me.DoDelete()
    End Sub

    Private Sub tsmiSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiSave.Click
        Dim blnChecked As Boolean = False
        Dim digResult As DialogResult = tbMessageBox.Show(Me, _
                                                         Me.Language.GetString("Main.Message.MakesureCloseWeixin"), _
                                                         Me.Language.GetString("Common.Info"), _
                                                         MessageBoxButtons.YesNo, _
                                                         MessageBoxIcon.Information, _
                                                         MessageBoxDefaultButton.Button2, _
                                                         Me.Language.GetString("Weixin.Lable.AlreadyCloseWeixin"), _
                                                         blnChecked, _
                                                         Me.Language.GetString("Common.Message.DeleteFiles"), _
                                                         True)

        If digResult <> Windows.Forms.DialogResult.Yes OrElse Not blnChecked Then
            Return
        End If

    End Sub

    Private Sub lblShowRootCourse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblShowRootCourse.Click, btnShowRootCourse.Click

        If mLoadState = WeixinAndroidState.SuperLimited OrElse mLoadState = WeixinAndroidState.NoRoot Then
            Common.OpenExplorer("http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415746&page=1&extra=#pid1366432")
        End If

    End Sub

    Private Sub lblDownloadWeixin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblDownloadWeixin.Click, btnDownloadWeixin.Click
        If mLoadState = WeixinAndroidState.NoRoot Then
            '' 打开root教程
            Common.OpenExplorer("http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415746&page=1&extra=#pid1366432")
        ElseIf mLoadState = WeixinAndroidState.SuperLimited Then
            Common.OpenExplorer("http://news.tongbu.com/87279.html")
        Else
            Try

                ''' 下载微信
                Dim strContent As String = Utility.GetContentStringFromUrl(mWeixinUrl, Encoding.UTF8)
                Dim objJson As JsonObject = JsonParser.ParseString(strContent)

                If objJson IsNot Nothing AndAlso objJson.ContainsKey("Data") Then

                    Dim objDatas As JsonObject = objJson("Data")

                    If objDatas IsNot Nothing AndAlso objDatas.ContainsKey("lurl") Then
                        Dim strJson As JsonString = CType(objDatas("lurl"), JsonString)
                        Dim urlStr As String = strJson.Value

                        Dim strName As String = Utility.GetParamValueFromQuery("appname", urlStr, "&", True)
                        Dim strVersion As String = Utility.GetParamValueFromQuery("ver", urlStr, "&", True)
                        Dim weixinName As String = Utility.GetParamValueFromQuery("packagename", urlStr, "&", True)
                        Dim strIconUrl As String = Utility.GetParamValueFromQuery("icon", urlStr, "&", True)
                        'Dim strUrl As String = AndroidApp.GetApkDownloadingUrl(weixinName, "z")

                        WebSiteHelper.Instance.DownloadAPK(Me.mApplication, weixinName, strIconUrl, strVersion, strName)
                    End If

                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "btnDownloadWeixin_Click of frmAndroidWeixin")
            End Try


            Me.Close()
        End If

    End Sub

    Private Sub lblDownloadWeixin_Resize(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles lblDownloadWeixin.Resize
        'Me.btnDownloadWeixin.Location = New Point(Me.lblDownloadWeixin.Right + 2, Me.lblDownloadWeixin.Location.Y + 2)
    End Sub

    Private Sub btnNext_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnNext.Click
        Me.LoadWechatData()
        IniSetting.SetShowWeixinWelcome()
    End Sub

    Private Sub btnMoreTutorials_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Common.OpenExplorer("http://news.tongbu.com/82572.html")
    End Sub

    Private Sub SetMenuEnable()
        If Me.mDgvFriend.SelectedRows.Count = 0 Then
            Me.tsmiTxtCurrentFriend.Enabled = False
            Me.tsmiTxtCurrentFriend.Text = Me.Language.GetString("Weixin.Item.ExportSelected")              '"导出选中联系人的消息记录"

            Me.tsmiExcelCurrentFriend.Enabled = False
            Me.tsmiExcelCurrentFriend.Text = Me.Language.GetString("Weixin.Item.ExportSelected")            '"导出选中联系人的消息记录"

            Me.tsmiTxtCurrentFriendTime.Enabled = False
            Me.tsmiTxtCurrentFriendTime.Text = Me.Language.GetString("Weixin.Item.ExportSelectedCurrent") '"导出选中联系人当前浏览时段消息记录"

            Me.tsmiExcelCurrentFriendTime.Enabled = False
            Me.tsmiExcelCurrentFriendTime.Text = Me.Language.GetString("Weixin.Item.ExportSelectedCurrent") '"导出选中联系人当前浏览时段消息记录"

            Me.tsmiPicCurrentFriend.Enabled = False
            Me.tsmiPicCurrentFriend.Text = Me.Language.GetString("Weixin.Lable.ExportPicSelectContact")     '"导出选中联系人的图片"

            Me.tsmiAudioCurrentFriend.Enabled = False
            Me.tsmiAudioCurrentFriend.Text = Me.Language.GetString("Weixin.Lable.ExportAudioSelectContact") '"导出选中联系人的语音"

            Me.tsmiVideoCurrentFriend.Enabled = False
            Me.tsmiVideoCurrentFriend.Text = Me.Language.GetString("Weixin.Lable.ExportSmallVideoSelectContact") '"导出选中联系人的小视频"


        Else

            Dim usrName As String = String.Empty
            Dim msgCount As Integer = 0

            Dim strNickName As String = GetSelectedNickName(usrName, msgCount)

            Me.tsmiTxtCurrentFriend.Enabled = True
            Me.tsmiTxtCurrentFriend.Text = String.Format(Me.Language.GetString("Backup.Button.ExportSelectedRecord"), strNickName) '导出 "{0} 的记录"
            tsmiTxtCurrentFriend.Tag = strNickName

            Me.tsmiExcelCurrentFriend.Enabled = True
            Me.tsmiExcelCurrentFriend.Text = String.Format(Me.Language.GetString("Backup.Button.ExportSelectedRecord"), strNickName) '导出 "{0} 的记录"
            tsmiExcelCurrentFriend.Tag = strNickName

            Me.tsmiTxtCurrentFriendTime.Enabled = True
            Me.tsmiTxtCurrentFriendTime.Text = String.Format(Me.Language.GetString("Backup.Button.ExportSelectedRecordCurrent"), strNickName)
            Me.tsmiTxtCurrentFriendTime.Tag = strNickName

            Me.tsmiExcelCurrentFriendTime.Enabled = True
            Me.tsmiExcelCurrentFriendTime.Text = String.Format(Me.Language.GetString("Backup.Button.ExportSelectedRecordCurrent"), strNickName)
            Me.tsmiExcelCurrentFriendTime.Tag = strNickName

            Me.tsmiPicCurrentFriend.Enabled = True
            Me.tsmiPicCurrentFriend.Text = String.Format(Me.Language.GetString("Weixin.Lable.ExportPicCurrentContact"), strNickName) '导出 "{0} 的图片"
            tsmiPicCurrentFriend.Tag = strNickName

            Me.tsmiAudioCurrentFriend.Enabled = True
            Me.tsmiAudioCurrentFriend.Text = String.Format(Me.Language.GetString("Weixin.Lable.ExportAudioCurrentContact"), strNickName) '导出 "{0} 的语音"
            tsmiAudioCurrentFriend.Tag = strNickName

            Me.tsmiVideoCurrentFriend.Enabled = True
            Me.tsmiVideoCurrentFriend.Text = String.Format(Me.Language.GetString("Weixin.Lable.ExportSmallVideoCurrentContact"), strNickName) '"导出 ""{0} 的小视频"""
            tsmiVideoCurrentFriend.Tag = strNickName
        End If

    End Sub

    Private Sub btnStartRoot_Click(sender As Object, e As EventArgs)
        If KingRootHelper.Instance.CheckIsNeedDownloadKingRoot() = False Then
            Return
        End If

        mKingRootHelper.DownloadZip()   '下载安装KingRoot
    End Sub

    Private Sub AdjustShowStateInfoPos(ByVal state As WeixinAndroidState)
        If state = WeixinAndroidState.SuperLimited OrElse state = WeixinAndroidState.NoRoot Then
            If ServerIniSetting.GetShowWechatBackupTool() Then

                Me.picWeixinIcon.Location = New Point(Me.pnlDescription.Left + 145, Me.pnlDescription.Top + 103)
                Me.lblNoRootMedthod.Location = New Point(Me.lblNoRootMedthod.Left, Me.picWeixinIcon.Bottom + 37)


                Me.pnlWechatAssiant.Location = New Point(Me.pnlMethod1.Left, Me.lblNoRootMedthod.Bottom + 9)
                Me.pnlMethod1.Location = New Point(Me.pnlMethod1.Left, Me.pnlWechatAssiant.Bottom + 3)
                Me.pnlWechatAssiant.Visible = True
                'Me.pnlMethod2.Location = New Point(Me.pnlMethod2.Left, Me.pnlMethod1.Bottom + 9)
            Else
                Me.pnlWechatAssiant.Visible = False
                Me.picWeixinIcon.Location = New Point(Me.pnlDescription.Left + 145, Me.pnlDescription.Top + 113)
                Me.lblNoRootMedthod.Location = New Point(Me.lblNoRootMedthod.Left, Me.picWeixinIcon.Bottom + 37)
                Me.pnlMethod1.Location = New Drawing.Point(Me.pnlMethod1.Left, Me.lblNoRootMedthod.Bottom + 9)
                'Me.picWeixinIcon.Location = New Point(Me.pnlDescription.Left + 107, Me.pnlDescription.Top + 99)
                'Me.pnlMethod1.Location = New Point(Me.pnlMethod1.Left, Me.picWeixinIcon.Bottom + 47)
                'Me.pnlMethod2.Location = New Point(Me.pnlMethod2.Left, Me.pnlMethod1.Bottom + 9)
            End If
            Me.lblNoRootMedthod.Visible = True
            'Me.picWeixinIcon.Location = New Point(Me.pnlDescription.Left + 167, Me.pnlDescription.Top + 19)
            'Me.picError.Location = New Point(Me.picWeixinIcon.Left + 27, Me.picWeixinIcon.Top + 22)
            'Me.lblWeixinTitle.Location = New Point(Me.pnlDescription.Left + 255, Me.picWeixinIcon.Top + 38)
            Me.lblWeixinTitle.Location = New Point(Me.picWeixinIcon.Right + 15, Me.picWeixinIcon.Top + 38)
            Me.lblTryRoot.Location = New Point(Me.lblWeixinTitle.Left, Me.lblWeixinTitle.Bottom + 10)
            Me.lblHowtoRoot.Location = New Point(Me.lblTryRoot.Right + 5, Me.lblWeixinTitle.Bottom + 10)
            Me.lblCacelLimit.Location = New Point(Me.lblHowtoRoot.Right + 5, Me.lblWeixinTitle.Bottom + 10)

            Me.lblWeixinDescription.Location = New Point(Me.lblWeixinTitle.Left, Me.lblWeixinTitle.Bottom + 17)
            Me.lblReCheckEx.Location = New Point(Me.lblWeixinDescription.Left, Me.lblWeixinDescription.Bottom + 8)
            Me.lblDownloadWeixin.Location = New Point(Me.lblReCheckEx.Right + 30, Me.lblWeixinDescription.Bottom + 8)
            Me.btnDownloadWeixin.Location = New Point(Me.lblDownloadWeixin.Right, Me.lblWeixinDescription.Bottom + 10)
            'Me.pnlWechatAssiant.Location = New Drawing.Point(Me.pnlWechatAssiant.Left, Me.picWeixinIcon.Bottom + 37)
            'Me.pnlMethod1.Location = New Point(Me.pnlMethod1.Left, Me.pnlWechatAssiant.Bottom + 9)
            'Me.pnlMethod2.Location = New Point(Me.pnlMethod2.Left, Me.pnlMethod1.Bottom + 9)
            Me.lblAbout.Location = New Point(Me.pnlMethod2.Left, Me.pnlMethod2.Bottom)
            Me.lblAbout.TextAlign = ContentAlignment.MiddleCenter

        Else
            Me.picWeixinIcon.Location = New Point(Me.pnlDescription.Left + 220, Me.pnlDescription.Top + 210)
            Me.picError.Location = New Point(Me.picWeixinIcon.Left + 27, Me.picWeixinIcon.Top + 22)
            Me.lblWeixinTitle.Location = New Point(Me.picWeixinIcon.Right + 33, Me.picWeixinIcon.Top)
            Me.lblWeixinDescription.Location = New Point(Me.lblWeixinTitle.Left, Me.lblWeixinTitle.Bottom + 17)
            Me.lblReCheckEx.Location = New Point(Me.lblWeixinDescription.Left, Me.lblWeixinDescription.Bottom + 8)
            Me.lblDownloadWeixin.Location = New Point(Me.lblReCheckEx.Right + 30, Me.lblWeixinDescription.Bottom + 8)
            Me.btnDownloadWeixin.Location = New Point(Me.lblDownloadWeixin.Right, Me.lblWeixinDescription.Bottom + 12)
            ' Me.lblAbout.Location = New Point(Me.pnlDescription.Left + 80, Me.picWeixinIcon.Bottom)
            Me.lblAbout.Location = New Point(Me.lblReCheckEx.Left, Me.lblReCheckEx.Bottom)
            Me.lblAbout.TextAlign = ContentAlignment.MiddleLeft
            'Me.lblAbout.Visible = True
        End If

    End Sub
#End Region

#Region "--- 与web交互 ---"

    Private Sub wbChat_Navigating(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserNavigatingEventArgs) Handles wbsChat.Navigating
        Dim url As String = e.Url.ToString

        If url.StartsWith("weixin://getnews", StringComparison.OrdinalIgnoreCase) Then
            Me.GetNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://viewphoto", StringComparison.OrdinalIgnoreCase) Then
            Me.ShowPhoto(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://copy", StringComparison.OrdinalIgnoreCase) Then
            Me.CopyNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportphoto", StringComparison.OrdinalIgnoreCase) Then
            Me.ExportPhoto(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://exportvoice", StringComparison.OrdinalIgnoreCase) Then
            Me.ExportVoice(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://playvoice", StringComparison.OrdinalIgnoreCase) Then
            Me.PlayVoice(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://getcontextnews", StringComparison.OrdinalIgnoreCase) Then
            Me.GetContextNews(url)
            e.Cancel = True

        ElseIf url.StartsWith("weixin://navigate", StringComparison.OrdinalIgnoreCase) Then
            Me.Navigate(url)
            e.Cancel = True

        End If
    End Sub

    Private Function GetChatMsgForWebPage(ByVal url As String) As Chatmessage
        Dim chatMsg As Chatmessage = Nothing
        Dim usrName As String = Utility.GetParamValueFromQuery("friend", url)
        Dim strId As String = Utility.GetParamValueFromQuery("id", url)

        If Not String.IsNullOrEmpty(usrName) AndAlso Not String.IsNullOrEmpty(strId) Then

            For Each item As Chatmessage In Me.mDictChatMsgs.Values

                If usrName.Equals(item.talker, StringComparison.OrdinalIgnoreCase) AndAlso _
                   strId.Equals(item.msgId) Then

                    chatMsg = item
                    Exit For

                End If
            Next
        End If

        Return chatMsg
    End Function

    '查看更多消息
    Private Sub GetNews(ByVal url As String)
        Dim chat As Chatmessage = Me.GetChatMsgForWebPage(url)

        If chat IsNot Nothing Then
            ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
            Application.DoEvents()

            Dim usrKey As String = chat.talker
            Dim dictMsgs As Dictionary(Of String, Chatmessage) = Me.mWechatHelper.GetContentChatMessage(chat, 50, False)
            If dictMsgs IsNot Nothing AndAlso dictMsgs.Count > 0 Then

                For Each strKey As String In dictMsgs.Keys
                    If Not mDictChatMsgs.ContainsKey(strKey) Then
                        mDictChatMsgs.Add(strKey, dictMsgs(strKey))
                    End If
                Next
            End If

            Me.mWechatHelper.SortDictChatMessage(mDictChatMsgs, True)
            'mDictWeixins(usrKey) = DataType.GetContextNews

            Dim lstUsrKeys As New List(Of String)
            lstUsrKeys.Add(usrKey)

            Dim dictChatMsgs As New Dictionary(Of String, Dictionary(Of String, Chatmessage))
            dictChatMsgs.Add(usrKey, mDictChatMsgs)

            Me.ShowChatMsgOnWeb(lstUsrKeys, DataType.GetContextNews, dictChatMsgs)
        End If

    End Sub

    '复制文本
    Private Sub CopyNews(ByVal url As String)
        Dim chat As Chatmessage = Me.GetChatMsgForWebPage(url)
        If chat IsNot Nothing Then
            Dim strContent As String = Me.GetChatMsg(chat)

            Try

                Clipboard.SetText(strContent)

                If Not String.IsNullOrEmpty(chat.imgPath) Then
                    Dim img As Image = Utility.GetImageFormFile(chat.imgPath)
                    If img IsNot Nothing Then
                        Clipboard.SetImage(img)
                    End If
                End If


                Me.ChangeStateText(Me.Language.GetString("Welcome.Message.CopySucceed"))
            Catch
                Me.ChangeStateText(Me.Language.GetString("Weixin.Message.CopyFail")) ' "复制失败！"
            End Try
        End If
    End Sub

    '展示原图
    Private Sub ShowPhoto(ByVal url As String)
        Dim chat As Chatmessage = Me.GetChatMsgForWebPage(url)

        If chat IsNot Nothing Then
            If chat.msgtype = 62 Then
                Me.ChangeStateText(Me.Language.GetString("Weixin.Lable.SmallVideo"))        '"小视频"
                Return
            End If

            '获取大图
            Dim imgPath As String = Me.GetChatImagePathFromLocal(chat, True)
            '没有可供预览的大图
            If File.Exists(imgPath) Then
                '展示图片
                Dim showPicFrm As New tbImageViewForm()
                showPicFrm.SrcPicture = Utility.GetImageFormFile(imgPath)
                showPicFrm.Icon = My.Resources.iTong
                showPicFrm.ViewType = ViewType.FromPC
                showPicFrm.ShowListView = False
                showPicFrm.HideButton = ToolBarButtonType.Main Or ToolBarButtonType.Delete Or ToolBarButtonType.WeiBo Or ToolBarButtonType.Prev Or ToolBarButtonType.Next
                showPicFrm.ShowDialog()
            Else
                Me.ChangeStateText(Me.Language.GetString("Weixin.Message.NoLargeImage")) '"没有可供预览的大图"
            End If
        End If
    End Sub

    Private Sub ExportPhoto(ByVal url As String)
        Dim chat As Chatmessage = Me.GetChatMsgForWebPage(url)
        If chat.msgtype = 62 OrElse chat.msgtype = 43 Then
            Me.DoExportVideo(chat)
        Else
            Me.DoExportPhoto(chat)
        End If
    End Sub

    Private Sub DoExportPhoto(ByVal chat As Chatmessage)
        If chat IsNot Nothing Then
            '获取图片路径
            Dim imgPath As String = chat.imgPath
            Dim ext As String = chat.fileext

            Try

                imgPath = Me.GetChatImagePathFromLocal(chat, True)

                If String.IsNullOrEmpty(imgPath) OrElse Not File.Exists(imgPath) Then
                    imgPath = Me.GetChatImagePathFromLocal(chat, False)
                End If

                If Not String.IsNullOrEmpty(imgPath) Then
                    ext = Path.GetExtension(imgPath)
                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "DoExportPhoto of frmAndroidWeixin")
            End Try

            Dim errMsg As String = String.Empty

            If String.IsNullOrEmpty(imgPath) OrElse Not File.Exists(imgPath) Then
                '"无法导出图片"

                errMsg = Me.Language.GetString("Weixin.Message.CanotExportPicture")     '无法导出图片，可能图片不存在或错误
                Me.ChangeStateText(errMsg)

                ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
                                             New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, errMsg})

                Return
            End If

            Dim fileDlg As SaveFileDialog = New SaveFileDialog()

            If Not String.IsNullOrEmpty(ext) Then
                ext = String.Format(" (*{0})|*{0}", ext)
            Else
                ext = " (*.png)|*.png"
            End If

            fileDlg.InitialDirectory = IniSetting.GetWeixinSaveFolder
            fileDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") + ext
            fileDlg.Title = Me.Language.GetString("Media.Button.Export")        '导出
            fileDlg.FileName = chat.createTime.ToString("yyyy-MM-dd HH-mm-ss")

            If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
                Dim strFilePath As String = fileDlg.FileName
                IniSetting.SetWeixinSaveFolder(Path.GetDirectoryName(strFilePath))

                Try

                    '拷贝到指定的目录
                    If File.Exists(imgPath) Then
                        File.Copy(imgPath, strFilePath, True)

                        Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(strFilePath))
                        Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！

                        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
                                                     New Object() {WeixinAndroidState.Succeed, ModelKey.AndroidWeixinExport, strText})

                        Me.ChangeStateText(strText)
                    End If

                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoExportPhoto of frmAndroidWeixin")
                End Try

            End If
        End If
    End Sub

    Private Sub DoExportVideo(ByVal chat As Chatmessage)
        If chat IsNot Nothing Then
            Dim fileDlg As SaveFileDialog = New SaveFileDialog()

            fileDlg.InitialDirectory = IniSetting.GetWeixinSaveFolder
            fileDlg.Filter = "声音文件 (*.mp4)|*.mp4"   '' Me.Language.GetString("Common.OpenFileDialog.PicFile")
            fileDlg.Title = Me.Language.GetString("Media.Button.Export")        '导出
            fileDlg.FileName = chat.createTime.ToString("yyyy-MM-dd HH-mm-ss")

            If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
                Dim strFilePath As String = fileDlg.FileName
                IniSetting.SetWeixinSaveFolder(Path.GetDirectoryName(strFilePath))

                Try
                    Dim imgPath As String = Me.GetChatVideoPathFromLocal(chat)

                    If String.IsNullOrEmpty(imgPath) OrElse Not File.Exists(imgPath) Then
                        '"无法导出小视频，请到微信点击查看后再导出。"
                        Me.ChangeStateText(Me.Language.GetString("Weixin.Message.CanotExportSmallVideo"))

                        Dim errMsg As String = String.Format("{1} ({0})", imgPath, Me.Language.GetString("Weixin.Message.CanotExportSmallVideo"))

                        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
                                                     New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, errMsg})

                        Return
                    End If

                    '拷贝到指定的目录
                    If File.Exists(imgPath) Then
                        File.Copy(imgPath, strFilePath)

                        Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(strFilePath))
                        Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！

                        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
                                                     New Object() {WeixinAndroidState.Succeed, ModelKey.AndroidWeixinExport, strText})

                        Me.ChangeStateText(strText)
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoExportVideo of frmAndroidWeixin")
                End Try
            End If
        End If
    End Sub

    '播放语音
    Private Sub PlayVoice(ByVal url As String)
        Dim chat As Chatmessage = Me.GetChatMsgForWebPage(url)

        If chat Is Nothing Then
            Return
        End If


        '获取语音路径（wav格式）
        Dim audioPath As String = Me.GetChatWavPathFromLocal(chat)
        Try
            If File.Exists(audioPath) Then
                Dim blnNeedPlay As Boolean = True

                If Me.mPlayer.PlayState = WMPPlayState.wmppsPlaying Then
                    Dim strFilePlaying As String = Me.mPlayer.CurrentMedia.sourceURL

                    'web页面暂停之前的
                    If String.Compare(strFilePlaying, audioPath, True) = 0 Then
                        blnNeedPlay = False         '如果正在播放，播放的是同一首歌，则暂停
                    Else
                        blnNeedPlay = True          '如果是新的语音，则播放新的语音
                    End If

                    Me.mPlayer.Pause()
                    Me.ShowAudioWebView(strFilePlaying, False)
                End If

                If blnNeedPlay Then
                    Me.mPlayer.GetUserPlaylist(Me.Name).Clear()
                    Me.mPlayer.GetUserPlaylist(Me.Name).Add(audioPath)
                    Me.mPlayer.CurrentUserPlaylist = Me.mPlayer.GetUserPlaylist(Me.Name)

                    Me.mPlayer.Play()
                    Me.ShowAudioWebView(chat.msgId, True)
                End If
            Else
                Me.ChangeStateText(Me.Language.GetString("Weixin.Message.PlayFail")) '"播放语音失败！"
            End If
        Catch ex As Exception
            Debug.WriteLine(ex)

            Me.ChangeStateText(Me.Language.GetString("Weixin.Message.PlayFail")) '"播放语音失败！"
        End Try

    End Sub

    '导出语音
    Private Sub ExportVoice(ByVal url As String)
        Dim chat As Chatmessage = Me.GetChatMsgForWebPage(url)

        If chat IsNot Nothing Then
            Dim fileDlg As SaveFileDialog = New SaveFileDialog()
            'fileDlg.Filter = Me.Language.GetString("Weixin.OpenFileDialog.AmrFile") & " (*.amr)|*.amr"  '语音文件
            fileDlg.Filter = Me.Language.GetString("Weixin.OpenFileDialog.AmrFile") & " (*.wav)|*.wav"  '语音文件

            fileDlg.Title = Me.Language.GetString("Media.Button.Export")        '导出
            fileDlg.FileName = chat.createTime.ToString("yyyy-MM-dd HH-mm-ss")

            If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
                Dim strFilePath As String = fileDlg.FileName

                Dim dictMsg As New Dictionary(Of String, Chatmessage)
                dictMsg.Add(chat.msgId, chat)
                '下载语音
                Me.GetChatImageVideoEmojiVoiceFromPhone(dictMsg)

                '获取语音路径（wav格式）
                Dim audioPath As String = Me.GetChatWavPathFromLocal(chat)

                '拷贝到指定的目录

                If File.Exists(audioPath) Then
                    File.Copy(audioPath, strFilePath)

                    Dim strFileName As String = String.Format("""{0}"" ", Path.GetFileName(strFilePath))
                    Dim strText As String = strFileName & Me.Language.GetString("Note.Message.ExportSucceed")   '导出成功！
                    ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
                                                 New Object() {WeixinAndroidState.Succeed, ModelKey.AndroidWeixinExport, strText})

                    Me.ChangeStateText(strText)
                Else
                    ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
                                                 New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, "获取语音路径（wav格式）失败"})
                End If
            End If
        End If

    End Sub

    '查看前后消息
    Private Sub GetContextNews(ByVal url As String)
        Dim chat As Chatmessage = Me.GetChatMsgForWebPage(url)
        If chat IsNot Nothing Then
            ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
            Application.DoEvents()

            Dim usrKey As String = chat.talker

            Dim dictMsgs As Dictionary(Of String, Chatmessage) = Me.mWechatHelper.GetContentChatMessage(chat, 50, True)
            If dictMsgs IsNot Nothing AndAlso dictMsgs.Count > 0 Then
                Dim lstUsrKeys As New List(Of String)
                lstUsrKeys.Add(usrKey)
                Dim dictChatMsgs As New Dictionary(Of String, Dictionary(Of String, Chatmessage))
                dictChatMsgs.Add(usrKey, dictMsgs)
                Me.ShowChatMsgOnWeb(lstUsrKeys, DataType.GetContextNews, dictChatMsgs)
            End If
        End If
    End Sub

    '跳转
    Private Sub Navigate(ByVal url As String)
        Dim startText As String = "?url="
        Dim startIndex As Integer = url.IndexOf(startText)
        Dim strUrl As String = url.Substring(startIndex + startText.Length).Trim()

        If Not String.IsNullOrEmpty(strUrl) Then
            Dim strPrefix As String = "http://"
            If Not strUrl.StartsWith(strPrefix) Then
                strUrl = strPrefix & strUrl
            End If

            Common.OpenExplorer(strUrl)
        End If
    End Sub

#End Region

#Region "--- 界面展示 ---"

    Private Function GetChatShowingMessage(ByVal chat As Rconversation) As String
        Dim strMsg As String = String.Empty
        Dim strDigest As String = chat.digest

        Select Case chat.msgtype
            Case 3
                strMsg = "[" & Me.Language.GetString("Main.Button.Photo") & "]" '"[图片]"
            Case 34
                strMsg = "[" & Me.Language.GetString("Weixin.Item.Audio") & "]" '"[语音]"
            Case 42
                If String.IsNullOrEmpty(strDigest) Then
                    strMsg = "[" & Me.Language.GetString("Weixin.Item.BusinessCard") & "]" '"[名片]"
                Else
                    strMsg = strDigest
                End If
            Case 43
                strMsg = "[" & Me.Language.GetString("Media.Type.Video") & "]" '"[视频]"
            Case 47
                strMsg = "[" & Me.Language.GetString("Weixin.Item.Expression") & "]" ' "[动画表情]"
                If strDigest.Contains(":") Then
                    Dim aryTemps As String() = strDigest.Split(New String() {":"}, StringSplitOptions.RemoveEmptyEntries)
                    strMsg = aryTemps(0) + ": " + strMsg
                End If

            Case 48
                strMsg = "[" & Me.Language.GetString("Weixin.Item.Location") & "]" '"[地理位置]"
            Case 49
                If strDigest.StartsWith("[File] ") Then
                    strMsg = strDigest.Replace("[File] ", "[" + Me.Language.GetString("Common.File") + "]")
                Else
                    strMsg = strDigest
                End If
            Case 50
                strMsg = "[" & Me.Language.GetString("Weixin.Item.Video") & "]" '"[视频聊天]"
            Case 62
                strMsg = "[" & Me.Language.GetString("Weixin.Lable.SmallVideo") & "]" '"[视频聊天]"
            Case 10000
                strMsg = Me.Language.GetString("Weixin.Item.SystemInfo")    '"系统消息"
            Case Else
                strMsg = chat.digest
        End Select

        Return strMsg
    End Function

    Private Delegate Sub SetChatViewStyleHandler(ByVal IsView As Boolean)
    Private Sub SetChatViewStyle(ByVal IsView As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetChatViewStyleHandler(AddressOf SetChatViewStyle), IsView)
        Else
            Me.pnlLoading_1.Visible = IsView
            If IsView Then
                Me.pnlLoading_1.BringToFront()
            Else
                Me.pnlLoading_1.SendToBack()
            End If

        End If

    End Sub

    Private Delegate Sub SetViewStyleHandler(ByVal style As ViewStyle)
    Private Sub SetViewStyle(ByVal style As ViewStyle)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewStyleHandler(AddressOf SetViewStyle), style)
        Else
            Select Case style
                Case ViewStyle.Home
                    Me.tpnlContent.BringToFront()

                Case ViewStyle.Loading
                    Me.pnlLoading.BringToFront()

                Case ViewStyle.Welcome
                    Me.pnlWelcome.BringToFront()

                Case ViewStyle.Description
                    Me.pnlDescription.BringToFront()

                Case ViewStyle.NoneContents
                    Me.pnlNoneContents.BringToFront()
            End Select

            Application.DoEvents()
        End If
    End Sub

    Private Delegate Sub ShowStateInfoHandler(ByVal state As WeixinAndroidState)
    ''' 显示当前运行状态
    Private Sub ShowStateInfo(ByVal state As WeixinAndroidState)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowStateInfoHandler(AddressOf ShowStateInfo), state)
        Else

            Me.pnlNoneContents.SendToBack()

            If state = WeixinAndroidState.SuperLimited Then

                Me.lblShowRootCourse.Visible = False
                Me.lblWeixinDescription_1.Visible = False
                Me.btnShowRootCourse.Visible = False

            Else
                Me.lblShowRootCourse.Visible = False
                Me.lblWeixinDescription_1.Visible = False
                Me.btnShowRootCourse.Visible = False
            End If

            Select Case state
                Case WeixinAndroidState.Loading

                    Me.lblLoadingDescription.Visible = False
                    Me.picNoLockScreen.Visible = False
                    Me.lblLoadingDescription.Text = ""

                    Me.lblLoadingDescription.Location = New Point(Me.picLoading.Left, Me.lblLoading.Bottom + 3)
                    Me.lblLoading.Text = Me.GetStateDesByState(state) '"正在获取微信数据"
                    Me.SetViewStyle(ViewStyle.Loading)

                Case WeixinAndroidState.NoInstalled
                    'Me.lblWeixinDescription.Height = 70
                    Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Lable.DescriptionGetWeixin") '"请您点击“立即免费获取微信”后，再继续使用该功能。"                    
                    Me.lblWeixinTitle.Text = Me.GetStateDesByState(state)   '"尚未安装微信"
                    Me.lblDownloadWeixin.Text = Me.Language.GetString("Weixin.Lable.GetWeixin") '"立即免费获取微信"
                    Me.btnDownloadWeixin.Visible = True
                    Me.lblDownloadWeixin.Visible = True
                    Me.picWeixinIcon.Visible = True
                    Me.picError.Visible = False
                    Me.lblReCheckEx.Text = Me.Language.GetString("File.Button.CheckInstall")  '重新检查
                    Me.lblReCheckEx.Visible = True
                    AdjustShowStateInfoPos(state)
                    Me.SetViewStyle(ViewStyle.Description)

                Case WeixinAndroidState.NoLogin
                    ' Me.lblWeixinDescription.Height = 70
                    Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Label.NotLoginTryMethod") '"请您在设备上登入微信后，点击“重新检查”                    
                    Me.btnDownloadWeixin.Visible = False
                    Me.lblDownloadWeixin.Visible = False
                    Me.lblWeixinTitle.Text = Me.GetStateDesByState(state) '"尚未登录微信"
                    Me.picWeixinIcon.Visible = True
                    Me.picError.Visible = False
                    Me.lblReCheckEx.Text = Me.Language.GetString("File.Button.CheckInstall")  '重新检查
                    Me.lblReCheckEx.Visible = True
                    AdjustShowStateInfoPos(state)
                    Me.SetViewStyle(ViewStyle.Description)

                Case WeixinAndroidState.NoRoot, WeixinAndroidState.SuperLimited

                    Me.picWeixinIcon.Image = My.Resources.Weixin_notRootOrLimit_New
                    Me.picWeixinIcon.Visible = True
                    Me.picError.Visible = False
                    Me.lblWeixinDescription.Visible = False
                    Me.lblReCheckEx.Visible = False
                    Me.lblDownloadWeixin.Visible = False
                    Me.btnDownloadWeixin.Visible = False
                    Me.lblWeixinDescription.Height = 18

                    Me.lblWechatAssistant.Text = Me.Language.GetString("Weixin.Text.WechatAssistant")  '微信备份助手
                    Me.lblDescWechatAssistant.Text = Me.Language.GetString("Weixin.Message.ExportWechatWithoutCable")  '无需数据线，免root导出微信文字，图片，小视频，语音
                    Me.btnOpenWechatAssisant.Text = Me.Language.GetString("Weixin.Button.Viewimmdediately") '立即查看

                    Me.lblWechatMediaManage.Text = Me.Language.GetString("WeixinMedia.Text.Manage")  '微信媒体管理
                    Me.btnManage.Text = Me.Language.GetString("Weixin.Button.Manage.Now")            '立即管理
                    Me.lblHowtoRoot.Text = Me.Language.GetString("Weixin.Message.HowToRoot")         '如何ROOT

                    'Me.lblWeixinTitle.Location = New Point(Me.picWeixinIcon.Right, Me.lblWeixinTitle.Top)
                    Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Message.NotGetWeixinError")    ' "暂未读取到微信信息，可能是第三方权限限制或Root不完善导致"
                    Me.pnlMethod1.Visible = True

                    Me.lblWechatTxtManage.Text = Me.Language.GetString("Weixin.ChatText.Manage")   '微信聊天文本管理
                    Me.lblDescRootAgain.Text = Me.Language.GetString("Weixin.Message.ChatTextMethod1")   '需要ROOT，才能导出文本信息
                    'Me.lblDescRootAgain2.Text = Me.Language.GetString("Weixin.Message.ChatTextMethod2") '若您的设备已ROOT，优先尝试取消设备中第三方应用限制访问权限后重试

                    'Me.lblDescCacelLimit.Text = Me.Language.GetString("Weixin.Message.NotGetWeixinErrorMethod1")  '如果设备未Root请先进行Root,已Root设备优先尝试取消设备中第三方 应用限制访问权限后重试
                    Me.lblDescCacelLimit.Text = Me.Language.GetString("WeixinMedia.Text.DescripWechatMedia")    '免ROOT批量导出/删除微信图片，小视频，语音，文件（不支持管理文本消息）
                    Me.lblCacelLimit.Text = Me.Language.GetString("Weixin.Button.CancelLimit")  '如何取消限制
                    'Me.lblDiscriptionDetail.Text = Me.Language.GetString("Weixin.Message.KingRootDescription")

                    'Me.pnlMethod2.Visible = True
                    Me.lblNoRootMedthod.Text = Me.Language.GetString("Weixin.Message.TryUseNoRootMethod")  '您也可以尝试一下免ROOT方式
                    Me.lblNoRootMedthod.Visible = True
                    Me.lblTryRoot.Text = Me.Language.GetString("Weixin.Message.TryRootCacelLimit") '您可尝试ROOT，已ROOT设备尝试取消第三方权限限制
                    Me.lblTryRoot.Visible = True
                    Me.lblHowtoRoot.Visible = True
                    Me.lblCacelLimit.Visible = True
                    'Me.lblDescRootAgain.Text = Me.Language.GetString("Weixin.Message.NotGetWeixinErrorMethod1")  '您也可以尝试ROOT（支持文本消息／媒体管理），已ROOT设备优先尝试取消设备中第三方应用显示访问权。
                    'Me.lblAbout.Visible = True
                    'Me.lblTipRootFail.Text = Me.Language.GetString("Weixin.Message.RootNoEffect")	 '*使用KingRoot后仍然无法获取？
                    'Me.lblOtherRootTool.Text = Me.Language.GetString("Weixin.Button.TryMoreRootTool")	 '尝试更多Root工具 >>
                    AdjustShowStateInfoPos(state)
                    Me.SetViewStyle(ViewStyle.Description)
                Case WeixinAndroidState.DownloadDBFailed, WeixinAndroidState.NoDB, WeixinAndroidState.ReadDBDataFailed
                    Me.lblWeixinDescription.Height = 18
                    Me.btnDownloadWeixin.Visible = False
                    Me.lblDownloadWeixin.Visible = False
                    Me.lblWeixinTitle.Text = Me.Language.GetString("Weixin.Lable.LoadDataFailed") '"获取微信数据失败"
                    Me.picWeixinIcon.Visible = False
                    Me.picError.Visible = True
                    Me.lblReCheckEx.Text = Me.Language.GetString("File.Button.CheckInstall")  '重新检查
                    Me.lblReCheckEx.Visible = True
                    'Me.lblReCheckEx.Location = New Point(Me.lblReCheckEx.Left, Me.lblWeixinTitle.Top + 130)

                    Me.lblWeixinDescription.Text = String.Format(Me.GetStateDesByState(state), Me.mAndroid.DeviceName)
                    'If state = WeixinAndroidState.DownloadDBFailed Then
                    '    Me.lblWeixinDescription.Text = String.Format(Me.Language.GetString("Weixin.Message.DownloadDbFail"), mAndroid.DeviceName)   '下载 {0} 设备上微信数据库失败！
                    'ElseIf state = WeixinAndroidState.NoDB Then
                    '    Me.lblWeixinDescription.Text = String.Format(Me.Language.GetString("Weixin.Label.DecryptFail"), mAndroid.DeviceName)       '解密 {0} 设备上微信数据库失败
                    'End If
                    AdjustShowStateInfoPos(state)
                    Me.SetViewStyle(ViewStyle.Description)
            End Select
        End If
    End Sub

    ' 添加聊天记录到网格内
    Private Sub AddWechatToDataGridView()
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New Threading.ThreadStart(AddressOf AddWechatToDataGridView))
            Else

                Dim fnt As Font = New Font("宋体", 9, FontStyle.Regular)
                Dim layoutSize As SizeF = New SizeF(100, 15.2)
                Dim strFormat As StringFormat = GuiHelper.CreateStringFormat(ContentAlignment.MiddleLeft, False)

                With Me.mDgvFriend
                    .SuspendLayout()
                    .Rows.Clear()

                    For Each item As Rconversation In mWechatHelper.DictRConversations.Values

                        If item.username.StartsWith("gh_", StringComparison.OrdinalIgnoreCase) Then
                            ''' 先屏蔽订阅号
                            Continue For
                        End If

                        Dim row As New tbDataGridViewRow()

                        row.Height = 58

                        Dim strText As String
                        Dim strChatTime As String

                        If String.IsNullOrEmpty(item.nickname) Then
                            strText = item.aliasname
                        Else
                            strText = item.nickname
                        End If

                        Dim strMsg As String = item.digest

                        strMsg = Me.GetChatShowingMessage(item)

                        strText = strText & vbCrLf & Me.OperateLastChatText(strMsg, fnt, layoutSize, strFormat)
                        strChatTime = Me.GetLastChatTime(item.talktime) + vbCrLf + " "

                        row.CreateCells(mDgvFriend, "", strText, strChatTime)

                        .Rows.Add(row)

                        Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                        cellName.tbIconShadow = True
                        cellName.tbIconSize = Me.mAppIconSize
                        cellName.tbIcon = Me.mDefaultHeadIcon

                        row.Tag = item

                    Next
                    .ResumeLayout()
                End With
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddWechatToDataGridView of frmAndroidWeixin")
        End Try


    End Sub

    '截取最后一条消息（展示在界面）
    Private Function OperateLastChatText(ByVal strLastChatText As String, ByVal fnt As Font, ByVal layoutSize As SizeF, ByVal strFormat As StringFormat) As String
        If strLastChatText.Length = 0 Then
            Return " "
        End If

        Try
            If Me.mGraphic Is Nothing Then
                Me.mGraphic = Me.CreateGraphics()
            End If

            Dim strResult As String = strLastChatText
            Dim charactersFitted As Integer = 0
            Dim linesFilled As Integer = 0
            Dim sizeCommment As SizeF = Me.mGraphic.MeasureString(strLastChatText, fnt, _
                                                                   layoutSize, strFormat, charactersFitted, linesFilled)

            If charactersFitted <> strLastChatText.Length AndAlso strLastChatText.Length > 2 AndAlso charactersFitted >= 2 Then
                strResult = strLastChatText.Substring(0, charactersFitted - 2) & "..."
            End If

            Return strResult
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OperateLastChatText of frmAndroidWeixin")
            Return strLastChatText
        End Try
    End Function

    Private Delegate Sub ChangeStateHandler(ByVal strText As String)
    Private Sub ChangeStateText(ByVal strText As String)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ChangeStateHandler(AddressOf ChangeStateText), strText)
            Else
                Me.lblState.Text = strText
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeStateText of frmAndroidWeixin")
        End Try
    End Sub

    Private Delegate Sub ChangeTitleTextHandler(ByVal Text As String)
    Private Sub ChangeTitleText(ByVal Text As String)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ChangeTitleTextHandler(AddressOf ChangeTitleText), Text)
            Else
                Me.lblTitle.Text = Text
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeTitleText of frmAndroidWeixin")
        End Try
    End Sub

    Private Delegate Sub ChangeButtonRefreshEnableHandler(ByVal enable As Boolean)
    Private Sub ChangeButtonRefreshEnable(ByVal enable As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New ChangeButtonRefreshEnableHandler(AddressOf ChangeButtonRefreshEnable), enable)
            Else
                Me.btnRefresh.Enabled = enable
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeButtonRefreshEnable of frmAndroidWeixin")
        End Try
    End Sub

    Private Delegate Sub ShowSystemInfoOnPCHandler(ByVal blnVisible As Boolean, ByVal strText As String)
    Private Sub ShowSystemInfoOnPC(ByVal blnVisible As Boolean, ByVal strText As String)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowSystemInfoOnPCHandler(AddressOf ShowSystemInfoOnPC), blnVisible, strText)
        Else
            Me.lblSystemInfo.Visible = blnVisible
            Me.lblSystemInfo.Text = strText
        End If
    End Sub

    '显示安装KingRoot进度
    Private Delegate Sub OnInstallKingRootHandler(ByVal sender As Object, ByVal e As InstallKingRootProgress)
    Private Sub OnInstallKingRoot(ByVal sender As Object, ByVal e As InstallKingRootProgress)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New OnInstallKingRootHandler(AddressOf OnInstallKingRoot), New Object() {sender, e})
            Else

                'If Me.btnKingRoot Is Nothing Then
                '	Return
                'End If

                'If e.Status = InstallKingRootProgress.InstallKingRootStatus.Downloading Then
                '	Me.btnStartRoot.Visible = False
                '	Me.pbarShowProgress.Visible = True
                '	Me.pbarShowProgress.tbPlayValue = e.Value
                '	Me.pbarShowProgress.Location = Me.btnStartRoot.Location
                '	Me.lblInstallTip.Location = New Point(Me.pbarShowProgress.Left + 15, Me.pbarShowProgress.Bottom)
                '	Me.lblInstallTip.Text = Me.Language.GetString("App.Cell.Installing")	'正在安装
                '	Me.lblInstallTip.Visible = True
                '	Me.lblRestartInstall.Visible = False
                'ElseIf e.Status = InstallKingRootProgress.InstallKingRootStatus.DownloadFailed Then
                '	Me.lblInstallTip.Location = New Point(Me.pbarShowProgress.Left + 15, Me.pbarShowProgress.Bottom)
                '	Me.lblInstallTip.Text = Me.Language.GetString("Tools.Payment.Message.DownloadFailure")	'下载失败
                '	Me.lblInstallTip.Visible = True
                '	Me.lblRestartInstall.Visible = True
                '	Me.lblRestartInstall.Location = New Point(lblInstallTip.Right + 1, lblInstallTip.Top)
                '	Me.lblRestartInstall.Text = Me.Language.GetString("App.Button.Reinstall")
                'ElseIf e.Status = InstallKingRootProgress.InstallKingRootStatus.ErrorUnzip Then
                '	Me.lblInstallTip.Location = New Point(Me.pbarShowProgress.Left + 15, Me.pbarShowProgress.Bottom)
                '	Me.lblInstallTip.Text = Me.Language.GetString("PkgInstaller.Label.InstallFail")	 '安装失败
                '	Me.lblInstallTip.Visible = True
                '	Me.lblRestartInstall.Visible = True
                '	Me.lblRestartInstall.Location = New Point(lblInstallTip.Right + 1, lblInstallTip.Top)
                '	Me.lblRestartInstall.Text = Me.Language.GetString("App.Button.Reinstall")  '重装
                'ElseIf e.Status = InstallKingRootProgress.InstallKingRootStatus.Succeed Then
                '	Me.btnStartRoot.Text = Me.Language.GetString("Weixin.Button.StartRoot")
                '	Me.btnStartRoot.Visible = True
                '	Me.pbarShowProgress.Visible = False
                '	Me.lblRestartInstall.Visible = False
                '	Me.lblInstallTip.Location = New Point(pbarShowProgress.Left + 15, Me.btnStartRoot.Bottom)
                '	Me.lblInstallTip.Text = Me.Language.GetString("App.Cell.InstallSucceed") '安装成功
                '	Me.lblInstallTip.Visible = True
                '	Me.lblTipRootFail.Visible = True
                '	Me.lblTipRootFail.Text = Me.Language.GetString("Weixin.Message.RootNoEffect")	'*使用KingRoot后仍然无法获取？
                '	lblOtherRootTool.Visible = True
                '	Me.lblOtherRootTool.Text = Me.Language.GetString("Weixin.Button.TryMoreRootTool")  '尝试更多Root工具 >>
                'ElseIf e.Status = InstallKingRootProgress.InstallKingRootStatus.TaskDelete OrElse e.Status = InstallKingRootProgress.InstallKingRootStatus.TaskStop Then
                '	Me.btnStartRoot.Text = Me.Language.GetString("Weixin.Button.StartRoot")
                '	Me.btnStartRoot.Visible = True
                '	Me.pbarShowProgress.Visible = False
                '	Me.lblRestartInstall.Visible = False
                '	Me.lblInstallTip.Visible = False
                'End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnInstallKingRoot of frmAndroidWeixin")
        End Try

    End Sub

#End Region

#Region "--- 检查有效性函数 ---"

    Private Function CheckIsContainUrl(ByVal strContent As String) As String
        Dim result As String = strContent

        Try
            '(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&amp;:/~\+#]*[\w\-\@?^=%&amp;/~\+#])?
            '用正则表达式识别URL超链接
            Dim re As New Regex("((http|ftp|https)://)?(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,4})*(/[a-zA-Z0-9\&%_\./-~-]*)?", RegexOptions.IgnoreCase Or RegexOptions.Compiled)
            Dim matches As MatchCollection = re.Matches(strContent)

            For Each m As Match In matches
                result = result.Replace(m.Value, String.Format("<span class=""title left""><a href=""weixin://navigate?url={0}""><font><u>{1}</u></font></a></span>", m.Value, m.Value))
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckIsContainUrl of frmAndroidWeixin")
            result = strContent
        End Try

        Return result
    End Function

#End Region

#Region "--- web界面展示 ---"

    Private Function GetChatMsgJson(ByVal usrname As String, ByVal dty As DataType, ByVal dictMsgs As Dictionary(Of String, Chatmessage)) As String
        Dim aryChats As New JsonArray
        Dim dictChats As JsonObject
        Dim strFromForWeb As String = String.Empty   '0：对方，1：自己
        Dim strTypeForWeb As String = String.Empty    '0：文本，1：语音，2：图片，3：网页，4：系统消息
        Dim strNickName As String = String.Empty
        Dim iconPath As String = String.Empty
        Dim strMsgForWeb As String = String.Empty
        Dim strTimeForWeb As String = String.Empty

        For Each chatItem As Chatmessage In dictMsgs.Values

            strFromForWeb = String.Format("{0}", chatItem.isSend)
            strNickName = chatItem.showname

            If chatItem.msgtype <> 10000 Then
                iconPath = GetHeadIconPathFromLocal(chatItem.wxsenderID)
            End If

            strTypeForWeb = "0"
            strMsgForWeb = String.Empty

            If dty = DataType.Search AndAlso _
              Not (chatItem.msgtype = 1 OrElse chatItem.msgtype = 436207665 OrElse chatItem.msgtype = 10000) Then
                Continue For
            End If

            Select Case chatItem.msgtype
                Case 1, 436207665, 318767153
                    strTypeForWeb = "0"
                    If chatItem.msgtype = 318767153 Then
                        strMsgForWeb = "安全登录提醒"
                    Else
                        strMsgForWeb = chatItem.content
                    End If

                Case 3
                    strTypeForWeb = "2"
                    strMsgForWeb = Me.GetChatImagePathFromLocal(chatItem)
                Case 34
                    strTypeForWeb = "1"
                    strMsgForWeb = String.Format("{0}", chatItem.voicelength)
                Case 42
                    '"[名片]"
                    strMsgForWeb = "[" + Me.Language.GetString("Weixin.Item.BusinessCard") + "]" + chatItem.content
                Case 47
                    strTypeForWeb = "2"

                    ''''''''''''''''''''''''''''''''''''''''''''''''
                    ''' Added by Utmost20160125
                    Dim strExt As String = String.Empty
                    Dim strImgName As String = String.Empty
                    Dim strChatContent As String = chatItem.content

                    If strChatContent.StartsWith("http://", StringComparison.OrdinalIgnoreCase) OrElse _
                       strChatContent.StartsWith("http*#*//", StringComparison.OrdinalIgnoreCase) Then
                        ''' 从服务器下载表情图片
                        Dim strImagePath As String = Path.Combine(mWechatPictureFolderOnPC, "image")
                        Dim npos As Integer = chatItem.imgPath.LastIndexOf("/")
                        strImgName = chatItem.imgPath.Substring(npos + 1)
                        strImgName = Path.Combine(strImagePath, strImgName)

                        If File.Exists(strImgName) Then
                            strExt = GetImageFileExt(strImgName)
                        End If
                    End If

                    If String.IsNullOrEmpty(strExt) AndAlso Not String.IsNullOrEmpty(strImgName) Then
                        ''从服务器下载表情图片
                        If File.Exists(strImgName) Then
                            Try
                                File.Delete(strImgName)
                            Catch ex As Exception

                            End Try
                        End If

                        If strChatContent.StartsWith("http*#*//", StringComparison.OrdinalIgnoreCase) Then
                            strChatContent = strChatContent.Replace("http*#*//", "http://")
                        End If
                        Common.DownloadImage(strChatContent, 20000, strImgName)
                    End If
                    ''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''''
                    strMsgForWeb = Me.GetChatImagePathFromLocal(chatItem)
                Case 48
                    '"[地理位置]"
                    strMsgForWeb = "[" & Me.Language.GetString("Weixin.Item.Location") & "]" + chatItem.content

                Case 49
                    If chatItem.ContentJson IsNot Nothing Then
                        strTypeForWeb = "3"
                    Else
                        strTypeForWeb = "0"
                        If String.IsNullOrEmpty(chatItem.content) Then
                            strMsgForWeb = "[" + Me.Language.GetString("Weixin.Item.Expression") + "]"  '"[动画表情]"
                        ElseIf chatItem.content.StartsWith("[File] ") Then
                            strMsgForWeb = chatItem.content.Replace("[File] ", "[" + Me.Language.GetString("Common.File") + "]")
                        Else
                            strMsgForWeb = chatItem.content
                        End If
                    End If
                Case 50
                    strMsgForWeb = "[" + Me.Language.GetString("Weixin.Item.Video") + "]" '"[视频未接通]"
                Case 43, 62
                    strTypeForWeb = "2"
                    strMsgForWeb = Me.GetChatImagePathFromLocal(chatItem)
                Case 10000
                    strTypeForWeb = "4"
                    strMsgForWeb = chatItem.content
            End Select

            dictChats = New JsonObject()
            dictChats.Add("from", strFromForWeb)
            dictChats.Add("type", strTypeForWeb)
            dictChats.Add("user", strNickName)
            dictChats.Add("icon", iconPath)

            If chatItem.ContentJson IsNot Nothing Then
                dictChats.Add("content", chatItem.ContentJson)
            Else
                dictChats.Add("content", strMsgForWeb)
            End If

            strTimeForWeb = chatItem.createTime.ToString("MM-dd HH:mm:ss")

            dictChats.Add("time", strTimeForWeb)
            dictChats.Add("friend", usrname)
            dictChats.Add("id", chatItem.msgId)

            aryChats.Add(dictChats)
        Next

        '''''添加语言包
        Dim dictLanguage As New JsonObject
        dictLanguage.Add("weixincopy", Language.GetString("Common.Button.Copy"))
        dictLanguage.Add("weixinexport", Language.GetString("Media.Button.Export"))
        dictLanguage.Add("weixingetmore", Language.GetString("Weixin.Button.GetMoreNews"))
        dictLanguage.Add("weixingetcontext", Language.GetString("Weixin.Button.GetContextNews"))

        Dim writer As New JsonWriter()
        dictChats = New JsonObject()

        dictChats.Add("type", dty.GetHashCode().ToString())
        dictChats.Add("data", aryChats)
        dictChats.Add("language", dictLanguage)
        dictChats.Write(writer)

        Return writer.ToString()
    End Function

    Private WithEvents bgwShowChatMsg As New BackgroundWorker()
    Private Sub ShowChatMsgOnWebThread(ByVal objParams As Object)
        Try
            Dim aryParams As Object() = CType(objParams, Object())
            Dim lstUsrKeys As List(Of String) = CType(aryParams(0), List(Of String))
            Dim dty As DataType = CType(aryParams(1), DataType)
            Dim dictMsgs As Dictionary(Of String, Dictionary(Of String, Chatmessage)) = CType(aryParams(2), Dictionary(Of String, Dictionary(Of String, Chatmessage)))

            If Me.bgwShowChatMsg.IsBusy() Then
                Me.bgwShowChatMsg.CancelAsync()
            End If

            Dim usrname As String = lstUsrKeys(0)
            'If Not mDictWeixins.ContainsKey(usrname) OrElse _
            '   dty = DataType.GetContextNews OrElse _
            '   lstUsrKeys.Count > 1 OrElse _
            '   dty = DataType.Search Then
            Me.SetChatViewStyle(True)
            'End If

            Me.bgwShowChatMsg.RunWorkerAsync(New Object() {lstUsrKeys, dty, dictMsgs})
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowChatMsgOnWebThread  of frmAndroidWeixin")
        End Try
    End Sub

    Private Sub ShowChatMsg_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwShowChatMsg.DoWork

        Try

            Dim dictMsgs As Dictionary(Of String, Dictionary(Of String, Chatmessage)) = CType(e.Argument(2), Dictionary(Of String, Dictionary(Of String, Chatmessage)))
            Dim lstUsrKeys As List(Of String) = CType(e.Argument(0), List(Of String))
            Dim dty As DataType = CType(e.Argument(1), DataType)

            Dim aryUsrKeys() As String = lstUsrKeys.ToArray()
            For Each usrKey As String In aryUsrKeys
                Dim dictMsg As Dictionary(Of String, Chatmessage) = dictMsgs(usrKey)

                If dictMsg.Count = 0 Then
                    lstUsrKeys.Remove(usrKey)
                    dictMsgs.Remove(usrKey)
                ElseIf dty <> DataType.Search Then
                    Me.GetChatImageVideoEmojiVoiceFromPhone(dictMsg)
                End If
            Next

            e.Result = New Object() {lstUsrKeys, dty, dictMsgs}
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowChatMsg_DoWork  of frmAndroidWeixin")
        End Try
    End Sub

    Private Sub ShowChatMsg_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwShowChatMsg.RunWorkerCompleted
        Try

            Dim dictMsgs As Dictionary(Of String, Dictionary(Of String, Chatmessage)) = CType(e.Result(2), Dictionary(Of String, Dictionary(Of String, Chatmessage)))
            Dim lstUsrKeys As List(Of String) = CType(e.Result(0), List(Of String))
            Dim dty As DataType = CType(e.Result(1), DataType)

            Dim usrname As String = lstUsrKeys(0)

            Dim count As Integer = dictMsgs(usrname).Count
            Dim fVisibled As Boolean = True       '' 显示查询更多按钮...

            Dim msgCount As Integer = Me.mWechatHelper.MsgsCount(usrname)
            fVisibled = (msgCount >= count)

            Dim strJson As String = String.Empty
            Dim strShowName As String = Me.Language.GetString("Weixin.Message.ComeFrom")
            Dim strNickName As String

            If lstUsrKeys.Count = 1 Then

                strJson = GetChatMsgJson(usrname, dty, dictMsgs(usrname))
                strNickName = Me.mWechatHelper.GetNickName(usrname)

                If _bIsSearch Then
                    Me.ShowSystemInfoOnWeb(strShowName + strNickName)    '"以下来自"
                End If

                Me.ShowChatOnWebPage(strJson)

                _bIsSearch = False
            Else

                For Each sKeys As String In lstUsrKeys
                    Dim dictTmps As Dictionary(Of String, Chatmessage) = dictMsgs(sKeys)
                    strNickName = Me.mWechatHelper.GetNickName(sKeys)

                    strJson = GetChatMsgJson(sKeys, dty, dictTmps)

                    'ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ShowAllSearchChatOnWebPageThread), New Object() {strShowName, strJson})

                    Me.ShowSystemInfoOnWeb(strShowName + strNickName)   '"以下来自"
                    Utility.WaitSeconds(0.1)
                    Me.ShowChatOnWebPage(strJson)

                Next

            End If


            Select Case dty
                Case DataType.Normal, DataType.GetNews
                    If count < mDefaultChatCount Then
                        ChatWebPage.ShowMoreButton(Me.wbsChat, False)
                    Else
                        ChatWebPage.ShowMoreButton(Me.wbsChat, True)
                    End If

                    If dty = DataType.GetNews Then
                        If mDictChatMsgs Is Nothing Then
                            mDictChatMsgs = New Dictionary(Of String, Chatmessage)
                        End If

                        For Each msgId As String In mDictChatMsgs.Keys
                            dictMsgs(usrname)(msgId) = mDictChatMsgs(msgId)
                        Next
                    End If
                Case DataType.TimeLocate
                    ChatWebPage.ShowMoreButton(Me.wbsChat, fVisibled)
                Case DataType.GetContextNews
                    ChatWebPage.ShowMoreButton(Me.wbsChat, fVisibled)
            End Select

            For Each strKey As String In dictMsgs(usrname).Keys
                If Not mDictChatMsgs.ContainsKey(strKey) Then
                    mDictChatMsgs.Add(strKey, dictMsgs(usrname)(strKey))
                End If
            Next

            'If Not mDictWeixins.ContainsKey(usrname) OrElse dty = DataType.GetContextNews OrElse dty = DataType.Search Then
            Me.SetChatViewStyle(False)
            'End If

            'If dty <> DataType.GetContextNews Then
            '    mDictWeixins(usrname) = dty
            'End If


        Catch ex As Exception
            Common.LogException(ex.ToString, "ShowChatMsg_RunWorkerCompleted of frmAndroidWeixin")
        End Try

        mDgvFriend.Enabled = True
    End Sub

    ''' 显示指定用户微信号的消息
    Private Sub ShowChatMsgOnWeb(ByVal lstUsrKeys As List(Of String), ByVal dty As DataType, ByVal dictMsgs As Dictionary(Of String, Dictionary(Of String, Chatmessage)))

        If Me.bgwShowChatMsg.IsBusy() Then
            Me.bgwShowChatMsg.CancelAsync()
        End If

        If Not bgwShowChatMsg.IsBusy() Then
            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ShowChatMsgOnWebThread), New Object() {lstUsrKeys, dty, dictMsgs})
        End If

    End Sub

    'web页聊天信息的展示
    Private Sub ShowChatOnWebPage(ByVal strJson As String)
        '    '清理PC页面信息

        Me.ShowSystemInfoOnPC(False, "")

        ChatWebPage.ShowChatOnWebPage(Me.wbsChat, strJson)

        Application.DoEvents()

        ChatWebPage.ShowMoreButton(Me.wbsChat, False)

    End Sub


    Private Sub ShowAllSearchChatOnWebPageThread(ByVal objParams As Object)
        Dim strShowName As String = objParams(0).ToString()
        Dim strJson As String = objParams(1).ToString()

        Me.ShowSystemInfoOnWeb(strShowName) '"以下来自"
        Utility.WaitSeconds(0.1)
        Me.ShowChatOnWebPage(strJson)
    End Sub

    'web页语音播放与暂停的控制
    Private Sub ShowAudioWebView(ByVal strWavFile As String, ByVal blnPlay As Boolean)
        If String.IsNullOrEmpty(strWavFile) Then
            Return
        End If

        Dim strID As String = Path.GetFileNameWithoutExtension(strWavFile)

        ChatWebPage.ControlVoiceView(Me.wbsChat, strID, blnPlay)
    End Sub

    Private Sub ShowSystemInfoOnWeb(ByVal strText As String, Optional ByRef sJsonValue As String = "")
        Dim dicChat As JsonObject = New JsonObject

        dicChat.Add("from", "")
        dicChat.Add("type", "4")
        dicChat.Add("user", "")
        dicChat.Add("icon", "")
        dicChat.Add("content", strText)
        dicChat.Add("time", "")

        Dim arrChats As New JsonArray
        arrChats.Add(dicChat)

        Dim writer As New JsonWriter()
        Dim dicChats As New JsonObject

        dicChats.Add("data", arrChats)
        dicChats.Write(writer)

        Dim strJson As String = writer.ToString()

        If String.IsNullOrEmpty(sJsonValue) Then
            Me.ShowChatOnWebPage(strJson)
        Else
            sJsonValue = strJson
        End If

    End Sub

#End Region

#Region "--- 头像 ---"

    '从本地获取头像文件路径名称
    Private Function GetHeadIconPathFromLocal(ByVal strUserName As String) As String
        Dim result As String = String.Empty

        Dim strHeaderFolder As String = Path.Combine(mWechatPictureFolderOnPC, "mmhead")
        Dim strImagePath As String = Path.Combine(strHeaderFolder, strUserName)
        If File.Exists(strImagePath) Then
            result = strImagePath
            GoTo DoExit
        End If

        Dim strDefaultImagePath As String = Path.Combine(strHeaderFolder, "default_usr.png")
        If Not File.Exists(strDefaultImagePath) Then
            My.Resources.weixin_icon_default.Save(strDefaultImagePath)
            result = strDefaultImagePath
            GoTo DoExit
        End If

DoExit:
        Return result
    End Function

    ' 从本地获取头像
    Private Function GetHeadIconFromLocal(ByVal strUserName As String) As Image
        Dim strHeaderFolder As String = Path.Combine(mWechatPictureFolderOnPC, "mmhead")
        Dim strImagePath As String = Path.Combine(strHeaderFolder, strUserName)
        Dim img As Image = Nothing

        If File.Exists(strImagePath) Then
            img = Utility.GetImageFormFile(strImagePath)
        End If

        If img Is Nothing Then
            img = Me.mDefaultHeadIcon
        End If

        Return img
    End Function

    Private Delegate Sub UpdateHeadIconToDataGridViewHandler(ByVal row As tbDataGridViewRow, ByVal strUserName As String)
    ' 更新网格内联系人头像
    Private Sub UpdateHeadIconToDataGridView(ByVal row As tbDataGridViewRow, ByVal strUserName As String)
        Try
            If Me.InvokeRequired Then
                Me.BeginInvoke(New UpdateHeadIconToDataGridViewHandler(AddressOf UpdateHeadIconToDataGridView), row, strUserName)
            Else
                Dim img As Image = Me.GetHeadIconFromLocal(strUserName)
                If img Is Nothing Then
                    img = Me.mDefaultHeadIcon
                End If

                Dim cellName As tbDataGridViewTextBoxCell = row.Cells("colName")
                cellName.tbIcon = img
                cellName.Tag = strUserName
                Application.DoEvents()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "UpdateHeadIconToDataGridView of frmAndroidWeixin")
        End Try
    End Sub

#End Region

#Region "--- 图片、语音、视频等文件 ---"

    '获取wav格式语音路径
    Private Function GetChatWavPathFromLocal(ByVal chatItem As Chatmessage) As String
        Dim strImagePath As String = Path.Combine(Me.mWechatTempFolderOnPC, "image")
        Folder.CheckFolder(strImagePath)

        Dim strChatPath As String = chatItem.imgPath
        Dim strWaveName As String = String.Empty

        If Not String.IsNullOrEmpty(strChatPath) Then
            Dim npos As Integer = strChatPath.LastIndexOf("/")

            Common.LogException(strChatPath, "GetChatWavPathFromLocal of frmAndroidWeixin")

            If npos > 0 Then
                Dim strTmp As String = strChatPath.Substring(npos + 1)

                '''' 语音播放与停止需要数字ID
                strWaveName = Path.Combine(strImagePath, chatItem.msgId) + ".wav"
                Dim errMsg As String = String.Empty

                Dim strAmrName As String = Path.Combine(Path.Combine(Me.mWechatPictureFolderOnPC, "image"), strTmp) + ".amr"
                If Not File.Exists(strWaveName) AndAlso File.Exists(strAmrName) Then
                    Try
                        WechatHelper.Aud2WavEx(strAmrName, strWaveName, "2013042508181124", "")
                    Catch ex As Exception
                        errMsg = String.Format(Me.Language.GetString("Weixin.Message.ConvertFail"), strChatPath)   'arm格式转为wav格式失败 {0}

                        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
                                                     New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, errMsg})
                    End Try

                End If
            End If

        End If

        Return strWaveName
    End Function

    '''获取指定聊天类型、聊天的图片路径的本地已下载的图片等文件、路径名
    Private Function GetChatImagePathFromLocal(ByVal chatItem As Chatmessage, Optional ByVal largeflag As Boolean = False) As String
        Dim picPath As String = Path.Combine(mWechatPictureFolderOnPC, "image")
        Try

            Dim strImagePath As String = Path.Combine(mWechatPictureFolderOnPC, "image")
            Dim npos As Integer = chatItem.imgPath.LastIndexOf("/")
            Dim strImgName As String = chatItem.imgPath.Substring(npos + 1)

            picPath = Path.Combine(strImagePath, strImgName)
            strImgName = Path.Combine(strImagePath, strImgName)

            If Not String.IsNullOrEmpty(strImgName) Then

                Dim fext As String = chatItem.fileext
                Dim picOnDevice As String

                If chatItem.msgtype = 62 OrElse chatItem.msgtype = 43 Then
                    fext = ".jpg"
                    picPath += fext
                End If

                If Not largeflag Then
                    '' 获取缩略图
                    picPath = Me.GetChatImageFileName(strImgName, picPath, fext)

                    If String.IsNullOrEmpty(picPath) Then
                        picPath = Path.Combine(strImagePath, strImgName) + "_thumb"
                        picPath = Me.GetChatImageFileName(strImgName + "_thumb", picPath, fext)
                    End If

                    If String.IsNullOrEmpty(picPath) Then
                        '' 获取缩略图——Added by Utmost20160226
                        picPath = Path.Combine(strImagePath, strImgName) + "_cover"
                        picPath = Me.GetChatImageFileName(strImgName + "_cover", picPath, fext)
                    End If

                    If String.IsNullOrEmpty(picPath) AndAlso Not String.IsNullOrEmpty(chatItem.BigImgPath) Then
                        ''' 使用大图
                        picPath = Path.Combine(strImagePath, chatItem.BigImgPath)
                        picPath = Me.GetChatImageFileName(strImgName, picPath, fext)
                    End If
                Else

                    If Not String.IsNullOrEmpty(chatItem.BigImgPath) AndAlso Not String.IsNullOrEmpty(chatItem.imgPath) Then
                        strImgName = chatItem.BigImgPath
                        picPath = Path.Combine(strImagePath, strImgName)
                        picOnDevice = LinuxPath.GetDirectoryName(chatItem.imgPath)

                        If strImgName.EndsWith("_bak_iphone", StringComparison.OrdinalIgnoreCase) OrElse _
                           strImgName.EndsWith("_bak_ipad", StringComparison.OrdinalIgnoreCase) Then
                            ''' 由iPhone导入的微信图片
                            npos = picOnDevice.IndexOf("/image2")
                            picOnDevice = picOnDevice.Substring(0, npos + 1)

                            picOnDevice = String.Format("{0}image2/{1}/{2}", picOnDevice, strImgName.Substring(0, 2), strImgName.Substring(2, 2))

                        End If


                        If Not File.Exists(picPath) Then
                            picOnDevice = LinuxPath.Combine(picOnDevice, strImgName)

                            Dim bRet As Boolean = mAndroid.DownFromPhone(picOnDevice, picPath)

                            If Not bRet Then

                                '' 如果原来读取表情位置错误则采用新的位置读取——Added by Utmost20160201
                                If Not String.IsNullOrEmpty(mWechatHelper.EmojiOldPath) AndAlso _
                                   Not String.IsNullOrEmpty(mWechatHelper.EmojiSDPath) AndAlso _
                                   Not mWechatHelper.EmojiOldPath.Equals(mWechatHelper.EmojiSDPath) Then
                                    picOnDevice = picOnDevice.Replace(mWechatHelper.EmojiOldPath, mWechatHelper.EmojiSDPath)
                                    bRet = mAndroid.DownFromPhone(picOnDevice, picPath)
                                End If

                                If Not bRet Then

                                    If mAndroid.HasSDCard AndAlso mAndroid.HasExtSDCard Then
                                        ''' 华为c8813 4.1.1有两张SD卡，需循环读取两张卡数据
                                        picOnDevice = picOnDevice.Replace(mAndroid.SDPath, mAndroid.ExtSDPath)
                                        bRet = mAndroid.DownFromPhone(picOnDevice, picPath)

                                        If Not bRet Then
                                            Dim picDirOnDevice As String = LinuxPath.GetDirectoryName(chatItem.imgPath)

                                            npos = picDirOnDevice.IndexOf("/image2")
                                            picDirOnDevice = picDirOnDevice.Substring(0, npos + 1)

                                            picOnDevice = String.Format("{0}image2/{1}/{2}/{3}", picDirOnDevice, strImgName.Substring(0, 2), strImgName.Substring(2, 2), strImgName)

                                            bRet = mAndroid.DownFromPhone(picOnDevice, picPath)

                                            If Not bRet Then
                                                ''' 华为c8813 4.1.1有两张SD卡，需循环读取两张卡数据
                                                picOnDevice = picOnDevice.Replace(mAndroid.SDPath, mAndroid.ExtSDPath)
                                                mAndroid.DownFromPhone(picOnDevice, picPath)
                                            End If

                                        End If
                                    Else
                                        ''' 米pad设备有iPhone导入的微信数据
                                        npos = picOnDevice.LastIndexOf("image2/")
                                        picOnDevice = picOnDevice.Substring(0, npos + 7)

                                        Dim strTmp As String = String.Format("{0}{1}/{2}/{3}", picOnDevice, strImgName.Substring(0, 2), strImgName.Substring(2, 2), strImgName)
                                        bRet = mAndroid.DownFromPhone(strTmp, picPath)
                                    End If

                                    If Not File.Exists(picPath) Then
                                        Dim fName As String = LinuxPath.GetFileName(chatItem.imgPath)
                                        picPath = Path.Combine(strImagePath, fName)
                                    End If
                                End If
                            End If
                        End If


                        Return picPath
                    End If

                    fext = GetImageFileExt(strImgName)

                    If String.IsNullOrEmpty(fext) Then
                        picPath = String.Empty

                        '' 使用缩略图——Added by Utmost20160226
                        If String.IsNullOrEmpty(picPath) Then
                            picPath = Path.Combine(strImagePath, strImgName) + "_thumb"
                            picPath = Me.GetChatImageFileName(strImgName + "_thumb", picPath, fext)
                        End If

                        If String.IsNullOrEmpty(picPath) Then

                            picPath = Path.Combine(strImagePath, strImgName) + "_cover"
                            picPath = Me.GetChatImageFileName(strImgName + "_cover", picPath, fext)
                        End If

                    ElseIf Not fext.Equals(".gif") Then
                        '' 下载大的图片
                        Dim thimgName As String = chatItem.imgPath.Replace("th_", "")
                        npos = thimgName.LastIndexOf("/")

                        Dim strTh As String = thimgName.Substring(npos + 1)
                        picPath = Path.Combine(strImagePath, strTh) + fext
                        picOnDevice = thimgName + fext

                        If Not File.Exists(picPath) Then
                            mAndroid.DownFromPhone(picOnDevice, picPath)

                            If Not File.Exists(picPath) Then
                                picOnDevice = thimgName + ".temp" + fext
                                mAndroid.DownFromPhone(picOnDevice, picPath)
                            End If
                        End If

                        If Not File.Exists(picPath) AndAlso Me.mAndroid.HasExtSDCard() Then
                            '' ZTE U956手机，数据保存在第2张SD卡
                            picOnDevice = thimgName + fext
                            picOnDevice = picOnDevice.Replace(mAndroid.SDPath, mAndroid.ExtSDPath)

                            mAndroid.DownFromPhone(picOnDevice, picPath)
                            If Not File.Exists(picPath) Then
                                picOnDevice = thimgName + ".temp" + fext
                                mAndroid.DownFromPhone(picOnDevice, picPath)
                            End If
                        End If


                        If Not File.Exists(picPath) Then
                            ''' 没有大图，导出缩略图
                            picOnDevice = chatItem.imgPath
                            mAndroid.DownFromPhone(picOnDevice, picPath)
                        End If

                        If Not File.Exists(picPath) AndAlso Me.mAndroid.HasExtSDCard() Then
                            picOnDevice = picOnDevice.Replace(mAndroid.SDPath, mAndroid.ExtSDPath)
                            mAndroid.DownFromPhone(picOnDevice, picPath)
                        End If
                    End If
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetChatImagePathFromLocal  of frmAndroidWeixin")
        End Try

        Return picPath
    End Function

    Private Function GetChatVideoPathFromLocal(ByVal chatItem As Chatmessage) As String
        Dim strImagePath As String = Path.Combine(mWechatPictureFolderOnPC, "image")
        Dim npos As Integer = chatItem.imgPath.LastIndexOf("/")
        Dim strImgName As String = chatItem.imgPath.Substring(npos + 1)
        Dim picPath As String = String.Empty
        Dim picOnDevice As String = chatItem.imgPath
        Dim bVideo As Boolean = False

        If Not String.IsNullOrEmpty(strImgName) Then
            picPath = Path.Combine(strImagePath, strImgName)
            picPath += ".mp4"
        End If

        Try
            If Not File.Exists(picPath) Then
                bVideo = mAndroid.DownFromPhone(picOnDevice, picPath)
            End If

            If Not bVideo Then  '''Not File.Exists(picPath)
                Dim picTemp As String = picOnDevice + ".mp4"
                bVideo = mAndroid.DownFromPhone(picTemp, picPath)
            End If

            If Not bVideo Then '''Not File.Exists(picPath)
                ''' SD卡用没有文件，则直接读取手机上的——Added by Utmost20160425
                Dim picTemp As String = picOnDevice + ".mp4"
                Dim strSdpath As String = ""
                If mAndroid.HasSDCard Then
                    strSdpath = mAndroid.SDPath + "/tencent"
                ElseIf mAndroid.HasExtSDCard Then
                    strSdpath = mAndroid.ExtSDPath + "/tencent"
                End If

                If strSdpath.Length > 0 Then
                    picTemp = picTemp.Replace(strSdpath, "/data/data/com.tencent.mm")
                    bVideo = mAndroid.DownFromPhone(picTemp, picPath)
                End If
            End If

            If Not bVideo AndAlso mAndroid.HasSDCard AndAlso mAndroid.HasExtSDCard Then
                ''' 第一张SD卡无法获取，则从第二张SD卡获取
                ''' 华为C8813就需要从第二张SD卡中获取
                picOnDevice = picOnDevice.Replace(mAndroid.SDPath, mAndroid.ExtSDPath)
                mAndroid.DownFromPhone(picOnDevice, picPath)

                If Not File.Exists(picPath) Then
                    Dim picTemp As String = picOnDevice + ".mp4"
                    mAndroid.DownFromPhone(picTemp, picPath)
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetChatVideoPathFromLocal  of frmAndroidWeixin")
        End Try

        Return picPath
    End Function



    Private Function GetImageFileExt(ByVal strImgName As String) As String
        Dim fext As String = ""

        If Not File.Exists(strImgName) Then
            fext = Path.GetExtension(strImgName)
        Else
            Try
                Using fm As FileStream = New FileStream(strImgName, FileMode.Open)
                    Dim aryBytes As Byte() = New Byte(2) {}
                    fm.Read(aryBytes, 0, 2)

                    If aryBytes(0) = &HFF AndAlso aryBytes(1) = &HD8 Then
                        fext = ".jpg"
                    ElseIf aryBytes(0) = &H89 AndAlso aryBytes(1) = &H50 Then
                        fext = ".png"
                    ElseIf aryBytes(0) = &H47 AndAlso aryBytes(1) = &H49 Then
                        fext = ".gif"
                    ElseIf aryBytes(0) = &H42 AndAlso aryBytes(1) = &H4D Then
                        fext = ".bmp"
                    Else
                        'fext = Path.GetExtension(strImgName)
                        'If (String.IsNullOrEmpty(fext)) Then
                        '    fext = ".jpg"
                        'End If
                        fext = ""
                    End If

                    fm.Close()
                End Using

            Catch ex As Exception

            End Try
        End If


        Return fext
    End Function

    Private Function GetChatImageFileName(ByVal imgmd5 As String, ByVal strImgName As String, ByRef fext As String) As String

        Dim strPicPath As String = ""
        '' 判断图片文件格式
        If File.Exists(strImgName) Then

            strPicPath = Path.Combine(Me.mWechatTempFolderOnPC, "image")
            Folder.CheckFolder(strPicPath)

            strPicPath = Path.Combine(strPicPath, imgmd5) + GetImageFileExt(strImgName)

            If Not File.Exists(strPicPath) Then
                Try
                    File.Copy(strImgName, strPicPath)
                Catch ex As Exception

                End Try
            End If

            fext = Me.GetImageFileExt(strImgName)
            If String.IsNullOrEmpty(fext) Then
                strPicPath = ""
            End If
        End If

        Return strPicPath
    End Function

    Private Function GetLargeImage(ByVal chatPath As String, ByVal bigImgPath As String, ByVal strImgName As String) As Boolean
        '' 直接读取大图
        Dim strBig As String = bigImgPath
        Dim npos As Integer = chatPath.IndexOf("/image2/")
        Dim bChat As Boolean = False

        If npos > 0 Then
            strBig = chatPath.Substring(0, npos + 8) + strBig.Substring(0, 2) + "/" + strBig.Substring(2, 2) + "/" + strBig
            Dim listEntries As New List(Of AndroidFileEntry)

            If mAndroid.GetFileInfo(strBig, listEntries) IsNot Nothing Then
                bChat = mAndroid.DownFromPhone(strBig, strImgName)
            End If
        End If

        Return bChat
    End Function
    ''' 从手机上下载指定用户的聊天图片、视频、声音、表情
    Private Sub GetChatImageVideoEmojiVoiceFromPhone(ByVal dictMsgs As Dictionary(Of String, Chatmessage))
        If dictMsgs IsNot Nothing AndAlso dictMsgs.Count > 0 Then

            Dim strImagePath As String = Path.Combine(mWechatPictureFolderOnPC, "image")
            Folder.CheckFolder(strImagePath)

            Dim strImgName As String = String.Empty
            Dim strMp4Name As String = String.Empty
            Dim imgmd5 As String = String.Empty

            For Each chatItem As Chatmessage In dictMsgs.Values
                Dim strChatPath As String = chatItem.imgPath

                If Not String.IsNullOrEmpty(strChatPath) Then
                    Try
                        Dim npos As Integer = strChatPath.LastIndexOf("/")
                        If npos <= 0 Then

                            If Not strChatPath.Contains("ChatWebPage\wx\images\") Then
                                strImgName = Path.Combine(Folder.TempFolder, "ChatWebPage\wx\images\" & strChatPath)
                                chatItem.imgPath = strImgName
                            Else
                                strImgName = strChatPath
                            End If

                        Else
                            imgmd5 = strChatPath.Substring(npos + 1)
                            strImgName = imgmd5
                        End If

                        strImgName = GetValidFilePathName(strImgName)

                        Select Case chatItem.msgtype
                            Case 62, 43
                                Dim tmpName As String = strImgName
                                ''' 小视频
                                strImgName = Path.Combine(strImagePath, tmpName) + ".jpg"
                                strMp4Name = Path.Combine(strImagePath, tmpName) + ".mp4"
                                strChatPath = chatItem.imgPath + ".jpg"
                            Case 34
                                strImgName = Path.Combine(strImagePath, strImgName) + chatItem.fileext
                                strChatPath = chatItem.imgPath + chatItem.fileext
                            Case Else

                                If npos > 0 Then
                                    strImgName = Path.Combine(strImagePath, strImgName)
                                End If

                        End Select

                        Dim bChat As Boolean = False
                        Dim infor As AndroidFileEntry = Nothing
                        Dim listEntries As New List(Of AndroidFileEntry)

                        If Not File.Exists(strImgName) Then

                            If strChatPath.StartsWith("http://", StringComparison.OrdinalIgnoreCase) Then
                                ''' 从服务器上下载图片
                                Common.DownloadImage(strChatPath, 20000, strImgName)
                            Else

                                infor = mAndroid.GetFileInfo(strChatPath, listEntries)

                                If infor Is Nothing Then
                                    ''' 如果没有缩略图，则下载大图——Added by Utmost20160218
                                    If Not String.IsNullOrEmpty(chatItem.BigImgPath) Then
                                        bChat = GetLargeImage(strChatPath, chatItem.BigImgPath, strImgName)
                                    End If

                                    If Not bChat Then
                                        ''' 没有任何图，从/data/data/com.tencent.mm目录下再读取——Added by Utmost20160229
                                        Dim strSdpath As String = ""
                                        If mAndroid.HasSDCard Then
                                            strSdpath = mAndroid.SDPath + "/tencent"
                                        ElseIf mAndroid.HasExtSDCard Then
                                            strSdpath = mAndroid.ExtSDPath + "/tencent"
                                        End If

                                        If strSdpath.Length > 0 Then
                                            strChatPath = strChatPath.Replace(strSdpath, "/data/data/com.tencent.mm")
                                            bChat = mAndroid.DownFromPhone(strChatPath, strImgName)
                                        End If
                                    End If

                                    Continue For
                                End If


                                bChat = mAndroid.DownFromPhone(infor, strImgName, False)

                                If Not bChat AndAlso _
                                   Not mWechatHelper.EmojiOldPath.Equals(mWechatHelper.EmojiSDPath, StringComparison.OrdinalIgnoreCase) AndAlso _
                                   Not String.IsNullOrEmpty(mWechatHelper.EmojiSDPath) Then
                                    strChatPath = strChatPath.Replace(mWechatHelper.EmojiOldPath, mWechatHelper.EmojiSDPath)
                                    bChat = mAndroid.DownFromPhone(strChatPath, strImgName)
                                End If

                                If Not bChat AndAlso Not File.Exists(strImgName) AndAlso Not String.IsNullOrEmpty(mAndroid.ExtSDPath) Then
                                    '' Huawei C8813手机的微信图片地址在外部SD卡
                                    strChatPath = strChatPath.Replace(mAndroid.SDPath, mAndroid.ExtSDPath)

                                    infor = mAndroid.GetFileInfo(strChatPath, listEntries)
                                    If infor IsNot Nothing Then
                                        bChat = mAndroid.DownFromPhone(infor, strImgName, False)
                                    ElseIf Not String.IsNullOrEmpty(chatItem.BigImgPath) Then
                                        bChat = GetLargeImage(strChatPath, chatItem.BigImgPath, strImgName)
                                    End If

                                End If

                                If chatItem.msgtype = 47 Then

                                    Dim strThumbName As String = strImgName + "_thumb"
                                    If Not File.Exists(strThumbName) Then

                                        ''' 判断是否存在缩略图
                                        Dim strImgOnDevice As String = strChatPath + "_thumb"
                                        infor = mAndroid.GetFileInfo(strImgOnDevice, listEntries)
                                        bChat = False

                                        If infor IsNot Nothing Then
                                            bChat = mAndroid.DownFromPhone(infor, strThumbName, False)
                                        Else

                                            ''' 判断是否存在缩略图——Added by Utmost20160226
                                            strImgOnDevice = strChatPath + "_cover"
                                            strThumbName = strImgName + "_cover"
                                            bChat = mAndroid.DownFromPhone(strImgOnDevice, strThumbName)

                                        End If

                                        If Not bChat AndAlso _
                                           Not mWechatHelper.EmojiOldPath.Equals(mWechatHelper.EmojiSDPath, StringComparison.OrdinalIgnoreCase) AndAlso _
                                           Not String.IsNullOrEmpty(mWechatHelper.EmojiSDPath) Then

                                            strImgOnDevice = strImgOnDevice.Replace(mWechatHelper.EmojiOldPath, mWechatHelper.EmojiSDPath)
                                            bChat = mAndroid.DownFromPhone(strImgOnDevice, strThumbName)

                                        End If

                                    End If
                                End If
                            End If


                            If chatItem.msgtype = 34 Then Continue For

                            If chatItem.msgtype = 62 OrElse chatItem.msgtype = 43 Then
                                Dim strMp4Tmp As String

                                If infor IsNot Nothing Then
                                    strMp4Tmp = Path.Combine(LinuxPath.GetPathWithoutFile(infor.FullPath), LinuxPath.GetFileNameWithoutExtension(infor.FullPath))
                                Else
                                    strMp4Tmp = chatItem.imgPath
                                End If

                                strMp4Tmp += ".mp4"

                                bChat = mAndroid.DownFromPhone(strMp4Tmp, strMp4Name)

                                If Not bChat AndAlso _
                                   Not mWechatHelper.EmojiOldPath.Equals(mWechatHelper.EmojiSDPath, StringComparison.OrdinalIgnoreCase) AndAlso _
                                   Not String.IsNullOrEmpty(mWechatHelper.EmojiSDPath) Then

                                    strMp4Tmp = strMp4Tmp.Replace(mWechatHelper.EmojiOldPath, mWechatHelper.EmojiSDPath)
                                    bChat = mAndroid.DownFromPhone(strMp4Tmp, strMp4Name)
                                End If

                                If Not bChat AndAlso Not File.Exists(strImgName) AndAlso Not String.IsNullOrEmpty(mAndroid.ExtSDPath) Then
                                    '' Huawei C8813手机的微信图片地址在外部SD卡
                                    strChatPath = strChatPath.Replace(mAndroid.SDPath, mAndroid.ExtSDPath)
                                    bChat = mAndroid.DownFromPhone(strChatPath, strImgName)
                                End If
                            End If
                        End If

                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "GetChatImageVideoEmojiVoiceFromPhone  of frmAndroidWeixin")
                    End Try
                    'Else
                    '    '
                    '    Dim strContent As String = chatItem.content
                    '    Dim re As New Regex("[\u4e00-\u9fa5]", RegexOptions.Compiled)
                    '    Dim m As Match = re.Match(strContent)


                    '    If strContent.Contains("^./expression/") AndAlso Not m.Success Then

                    '        Try
                    '            Dim aryContents As String() = strContent.Split(New String() {"^(21,21)"}, StringSplitOptions.RemoveEmptyEntries)
                    '            If aryContents IsNot Nothing AndAlso aryContents.Length > 0 Then
                    '                Dim pos As Integer = aryContents(0).IndexOf("^./expression/")
                    '                Dim strTmp As String = aryContents(0).Substring(pos)

                    '                chatItem.imgPath = Folder.TempFolder + "ChatWebPage\wx\expression\" + strTmp.Replace("^./expression/", "")
                    '            End If
                    '        Catch ex As Exception

                    '        End Try
                    '    End If
                End If
            Next

        End If
    End Sub

    ' 时间定位（根据起始时间，获取到现在的消息）
    Private Sub GetChatMsgForTimeLocation(ByVal startTime As DateTime, ByVal endTime As DateTime)
        If Me.mDgvFriend.SelectedRows IsNot Nothing AndAlso Me.mDgvFriend.SelectedRows.Count > 0 Then
            Try
                Dim userName As String = String.Empty
                Dim msgCount As Integer = 0
                Me.GetSelectedNickName(userName, msgCount)

                If Not String.IsNullOrEmpty(userName) Then
                    Dim dictChatMsg As New Dictionary(Of String, Chatmessage)

                    dictChatMsg = Me.mWechatHelper.GetChatMessages(userName, String.Empty, startTime, endTime)

                    If dictChatMsg IsNot Nothing AndAlso dictChatMsg.Count > 0 Then
                        Dim lstUsrKeys As New List(Of String)
                        lstUsrKeys.Add(userName)
                        Dim dictTmpMsgs As New Dictionary(Of String, Dictionary(Of String, Chatmessage))
                        dictTmpMsgs.Add(userName, dictChatMsg)
                        Me.ShowChatMsgOnWeb(lstUsrKeys, DataType.TimeLocate, dictTmpMsgs)
                    Else
                        Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.NoSearchResult")) '您选择的时间内没有消息，请您重新选择
                    End If
                Else
                    Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.InfoSelectFriend"))
                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "GetChatMsgForTimeLocation of frmAndroidWeixin")
            End Try
        Else
            Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.InfoSelectFriend")) '"请选中联系人再查询！"
        End If
    End Sub

    ''' 详细显示当前微信记录
    Private Sub LoadChatMessage(ByVal row As tbDataGridViewRow)
        Dim item As Rconversation = CType(row.Tag, Rconversation)
        Me.ChangeTitleText(item.nickname)
        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf LoadChatMessageWork), New Object() {item.username, 0, 50})

        Me.mCurrentSelectUserName = item.username
    End Sub

    Private WithEvents bgwLoadChatMsgs As New BackgroundWorker()

    Private Sub LoadChatMessageWork(ByVal objParams As Object)
        Try
            Dim aryParams As Object() = CType(objParams, Object())
            Dim usrname As String = aryParams(0).ToString()
            Dim startIndex As Integer = CType(aryParams(1), Integer)
            Dim nCount As Integer = CType(aryParams(2), Integer)

            LoadChatMessage(usrname, startIndex, nCount)
        Catch ex As Exception
            Common.LogException(ex.ToString, "LoadChatMessageWork of frmAndroidWeixin")
        End Try
    End Sub

    Private Sub LoadChatMessage(ByVal usrname As String, ByVal startIndex As Integer, Optional ByVal Count As Integer = 50)
        If Me.bgwLoadChatMsgs.IsBusy() Then
            Me.bgwLoadChatMsgs.CancelAsync()
        End If

        'If Not mDictWeixins.ContainsKey(usrname) Then
        Me.SetChatViewStyle(True)
        'End If

        Me.bgwLoadChatMsgs.RunWorkerAsync(New Object() {usrname, startIndex, Count})
    End Sub

    Private Sub LoadChatMsgs_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwLoadChatMsgs.DoWork
        Try
            Dim dictMsgs As Dictionary(Of String, Chatmessage) = Me.LoadChatMessageThread(New Object() {e.Argument(0), e.Argument(1), e.Argument(2)})
            e.Result = New Object() {dictMsgs, e.Argument(0), e.Argument(1)}

        Catch ex As Exception
            Common.LogException(ex.ToString, "LoadChatMsgs_DoWork of frmAndroidWeixin")
        End Try
    End Sub

    Private Sub LoadChatMsgs_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwLoadChatMsgs.RunWorkerCompleted
        Dim dictMsgs As Dictionary(Of String, Chatmessage) = CType(e.Result(0), Dictionary(Of String, Chatmessage))
        Dim usrname As String = e.Result(1).ToString()

        If dictMsgs IsNot Nothing AndAlso dictMsgs.Count > 0 Then
            Try

                Dim dty As DataType = DataType.Normal
                Dim startIndex As Integer = CType(e.Result(2), Integer)
                If startIndex > 0 Then
                    dty = DataType.GetNews
                End If

                Dim lstUsrKeys As New List(Of String)
                lstUsrKeys.Add(usrname)

                Dim dictChatMsgs As New Dictionary(Of String, Dictionary(Of String, Chatmessage))
                dictChatMsgs.Add(usrname, dictMsgs)
                Me.ShowChatMsgOnWeb(lstUsrKeys, dty, dictChatMsgs)

            Catch ex As Exception
                Common.LogException(ex.ToString, "LoadChatMsgs_RunWorkerCompleted of frmAndroidWeixin")
            End Try
        Else
            mDgvFriend.Enabled = True
        End If

        'If Not mDictWeixins.ContainsKey(usrname) Then
        Me.SetChatViewStyle(False)
        'End If
    End Sub

    Private Function LoadChatMessageThread(ByVal objParams As Object) As Dictionary(Of String, Chatmessage)
        Dim dictMsgs As Dictionary(Of String, Chatmessage) = New Dictionary(Of String, Chatmessage)
        If Me.mWechatHelper IsNot Nothing AndAlso objParams IsNot Nothing Then

            Try
                Dim aryParams As Object() = CType(objParams, Object())
                Dim usrname As String = aryParams(0).ToString()
                Dim startIndex As Integer = CType(aryParams(1), Integer)
                Dim Count As Integer = CType(aryParams(2), Integer)

                dictMsgs = mWechatHelper.GetChatMessages(usrname, startIndex - 1, Count)

            Catch ex As Exception
                Common.LogException(ex.ToString(), "LoadChatMessageThread of frmAndroidWeixin")
            End Try
        End If

        Return dictMsgs
    End Function

#End Region

#Region "--- 搜索联系人、信息记录 ---"

	Private Sub SetRowVisible(ByVal strSearch As String)
		strSearch = strSearch.ToLower()
		Dim count As Integer = 0

		For Each row As DataGridViewRow In Me.mDgvFriend.Rows
			If strSearch.Length = 0 Then
				row.Visible = True
				count = count + 1
			Else
				Dim strSoftName As String = (row.Cells("colName").Value & "").ToString().ToLower()
				Dim strPinYin As String = PinYinClass.MakePinYin(strSoftName, PinYinOptions.Default).ToLower
				Dim strPinYinFirstChar As String = PinYinClass.MakePinYin(strSoftName, PinYinOptions.FirstCharacterOnly).ToLower

				If strSoftName.Contains(strSearch) OrElse _
				   strPinYin.Contains(strSearch) OrElse _
				   strPinYinFirstChar.Contains(strSearch) Then

					row.Visible = True
					count = count + 1
				Else
					row.Visible = False
				End If
			End If
		Next

	End Sub

	Private Sub StartToSearchFriend(ByVal friendName As String)
		Try
			ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf StartToSearchFriendThread), friendName)
		Catch ex As Exception

		End Try
	End Sub

	Private Delegate Sub StartToSearchFriendInThreadHanlder(ByVal strSearch As Object)
	Private Sub StartToSearchFriendThread(ByVal params As Object)
		Try
			If Me.InvokeRequired Then
				Me.Invoke(New StartToSearchFriendInThreadHanlder(AddressOf StartToSearchFriendThread), params)
			Else
				Try
					Utility.WaitSeconds(0.3)

					Me.mDgvFriend.ClearSelection()
					Me.SetRowVisible(params.ToString())

				Catch ex As Exception
					Common.LogException(ex.ToString(), "StartToSearchFriendInThread of frmAndroidWeixin")
				End Try
			End If
		Catch ex As Exception
		End Try

	End Sub

	Private Sub ShowSearchChatOnWeb(ByVal lstUsrKeys As List(Of String), ByVal startTime As DateTime, ByVal endTime As DateTime, ByVal searchText As String, Optional ByVal blnSearchAll As Boolean = False)

		If Not blnSearchAll Then
			Return
		End If

		''' 搜索全部
		Dim dictMsgs As New Dictionary(Of String, Dictionary(Of String, Chatmessage))
		Dim aryUsrKeys() As String = lstUsrKeys.ToArray()
		For Each usrKey As String In aryUsrKeys
			Dim dictTmpMsgs As Dictionary(Of String, Chatmessage) = mWechatHelper.GetChatMessages(usrKey, searchText, startTime, endTime)

			If dictTmpMsgs.Count = 0 Then
				lstUsrKeys.Remove(usrKey)
			Else
				dictMsgs.Add(usrKey, dictTmpMsgs)
			End If

		Next

		If dictMsgs.Count = 0 Then
			Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.SearchNoResult"))	'"搜索不到结果"

			Return
		End If

		Me.ShowChatMsgOnWeb(lstUsrKeys, DataType.Search, dictMsgs)

	End Sub

	Private Sub ShowSearchChatOnWeb(ByVal usrKey As String, ByVal startTime As DateTime, ByVal endTime As DateTime, ByVal searchText As String, Optional ByVal blnSearchAll As Boolean = False)

		Dim dictChatMsgs As Dictionary(Of String, Chatmessage) = Me.mWechatHelper.GetChatMessages(usrKey, searchText, startTime, endTime)

		If dictChatMsgs Is Nothing OrElse dictChatMsgs.Count = 0 Then
			If Not blnSearchAll Then
				Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.SearchNoResult"))	'"搜索不到结果"
			End If

			Return
		End If

		Dim lstUsrKeys As New List(Of String)
		If Not blnSearchAll AndAlso Not String.IsNullOrEmpty(usrKey) Then
			Me.mDictChatMsgs = dictChatMsgs

			lstUsrKeys.Add(usrKey)
			Dim dictTempMsgs As New Dictionary(Of String, Dictionary(Of String, Chatmessage))
			dictTempMsgs.Add(usrKey, dictChatMsgs)
			Me.ShowChatMsgOnWeb(lstUsrKeys, DataType.Search, dictTempMsgs)
			'Else
			'    If Me.m_dictChatMsgs Is Nothing Then
			'        Me.m_dictChatMsgs = New Dictionary(Of String, Chatmessage)
			'    End If

			'    For Each sKey As String In dictChatMsgs.Keys
			'        If Not m_dictChatMsgs.ContainsKey(sKey) Then
			'            m_dictChatMsgs.Add(sKey, dictChatMsgs(sKey))
			'        End If

			'        If Not lstUsrKeys.Contains(sKey) Then
			'            lstUsrKeys.Add(sKey)
			'        End If

			'    Next

			'    Me.ShowChatMsgOnWeb(lstUsrKeys, DataType.Search, dictChatMsgs)
		End If

	End Sub

	Private Sub SearchByTimeAndText(ByVal startTime As DateTime, ByVal endTime As DateTime, ByVal strSearchText As String, ByVal blnSearchAll As Boolean)
		If Not blnSearchAll Then
			If Me.mDgvFriend.SelectedRows IsNot Nothing AndAlso Me.mDgvFriend.SelectedRows.Count > 0 Then
				Dim userName As String = String.Empty
				Dim msgCount As Integer = 0
				Dim nickName As String = Me.GetSelectedNickName(userName, msgCount)

				Me.ChangeStateText(nickName & " " & Me.Language.GetString("Weixin.Message.Query")) ''正在查询...
				Me.ChangeTitleText(nickName)

				Me.ShowSearchChatOnWeb(userName, startTime, endTime, strSearchText, blnSearchAll)

				Me.ChangeStateText(nickName & " " & Me.Language.GetString("Weixin.Message.QueryCompleted"))	'查询完成！
			Else
				Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.InfoSelectFriend")) '"请选中联系人再查询"
				Me.ChangeTitleText(Me.Language.GetString("Weixin.Message.WelcomeTitle")) '"欢迎使用微信消息记录管理器"
			End If
		Else
			Me.ChangeStateText(Me.Language.GetString("Weixin.Message.AllFriend") & " " & Me.Language.GetString("Weixin.Message.Query"))	'全部联系人 正在查询...
			If Me.mDictChatMsgs Is Nothing Then
				Me.mDictChatMsgs = New Dictionary(Of String, Chatmessage)
			Else
				Me.mDictChatMsgs.Clear()
			End If

			Dim lstUsrKeys As New List(Of String)
			For i As Integer = mDgvFriend.RowCount - 1 To 0 Step -1
				Dim row As DataGridViewRow = Me.mDgvFriend.Rows(i)
				Dim item As Rconversation = CType(row.Tag, Rconversation)

				If item IsNot Nothing Then
					'Me.ShowSearchChatOnWeb(item.username, startTime, endTime, strSearchText, blnSearchAll)
					lstUsrKeys.Add(item.username)
				End If
			Next

			If lstUsrKeys.Count = 0 Then
				Me.ShowSystemInfoOnPC(True, Me.Language.GetString("Weixin.Message.SearchNoResult"))	'"搜索不到结果"
			End If

			Me.ShowSearchChatOnWeb(lstUsrKeys, startTime, endTime, strSearchText, blnSearchAll)

			Me.ChangeTitleText(Me.Language.GetString("Weixin.Item.All"))
			Me.ChangeStateText(Me.Language.GetString("Weixin.Message.AllFriend") & " " & Me.Language.GetString("Weixin.Message.QueryCompleted")) '全部联系人 查询完成
		End If
	End Sub

	'1、搜索全部时间内的消息
	Private Sub StartToSearchChat(ByVal strSearchText As String, ByVal blnSearchAll As Boolean)
		Me.StartToSearchChat(strSearchText, blnSearchAll, DateTime.MinValue, DateTime.MinValue)
	End Sub

	'2、根据关键字、范围、时间搜索
	Private Sub StartToSearchChat(ByVal strSearchText As String, ByVal blnSearchAll As Boolean, ByVal startTime As DateTime, ByVal endTime As DateTime)

		Dim dictValues As New Dictionary(Of String, Object)
		dictValues.Add("StartTime", startTime)
		dictValues.Add("EndTime", endTime)
		dictValues.Add("SearchText", strSearchText)
		dictValues.Add("SearchAll", blnSearchAll)

		Dim thd As Thread = New Thread(New ParameterizedThreadStart(AddressOf StartToSearchChatInThread))
		With thd
			.IsBackground = True
			.Start(dictValues)
		End With
	End Sub

	Private Sub StartToSearchChatInThread(ByVal dictValues As Object)
		Try
			Dim startTime As DateTime = CType(dictValues("StartTime"), DateTime)
			Dim endTime As DateTime = CType(dictValues("EndTime"), DateTime)
			Dim strSearch As String = CType(dictValues("SearchText"), String)
			Dim blnSearchAll As Boolean = CType(dictValues("SearchAll"), Boolean)

			ChatWebPage.ClearChatOnWebPage(Me.wbsChat)
			Application.DoEvents()

			Me.SearchByTimeAndText(startTime, endTime, strSearch, blnSearchAll)

		Catch ex As Exception
			Common.LogException(ex.ToString(), "StartToSearchChatInThread of frmAndroidWeixin")
		End Try
	End Sub

#End Region

#Region "--- 导出Excel、Text ---"

    Private Sub Export(ByVal blnExportToTxt As Boolean, ByVal type As ExportType, ByVal isTxt As Boolean)
        Try
            Dim lstRows As List(Of DataGridViewRow) = New List(Of DataGridViewRow)

            If type = ExportType.AllFriend Then
                For Each row As DataGridViewRow In Me.mDgvFriend.Rows
                    lstRows.Add(row)
                Next
            ElseIf type = ExportType.CurrentFriend OrElse type = ExportType.CurrentFriendByTime Then
                lstRows.Add(Me.mDgvFriend.SelectedRows(0))
            End If

            Dim userName As String = String.Empty
            Dim msgCount As Integer = 0
            Dim strNickName As String = GetSelectedNickName(userName, msgCount)

            Dim lstCurrentChatInfo As New List(Of WeChatChatInfo)

            For Each info As Chatmessage In Me.mDictChatMsgs.Values
                lstCurrentChatInfo.Add(GenerateChatmessage2ChatInfo(info))
            Next

            Dim lstTime As New List(Of DateTime)
            If type = ExportType.CurrentFriendByTime Then
                lstTime.Add(Me.dtpStart.Value)
                lstTime.Add(Me.dtpEnd.Value)
            End If

            'Me.mWeixinImportOrExportHelper.Export(blnExportToTxt, strNickName, type, lstCurrentChatInfo, lstRows, Nothing, lstTime)

            Dim eType As ExportType = ExportType.ExportToTxt
            If isTxt = False Then
                eType = ExportType.ExportToExcel
            End If

            Dim para As New IMExportPara()
            For Each row As DataGridViewRow In lstRows
                para.ListExport.Add(row.Tag)
            Next

            para.DateStart = Me.dtpStart.Value
            para.DateEnd = Me.dtpEnd.Value
            para.ExportType = eType

            Me.mHelper.Start(para)

            ' Me.mWeixinImportOrExportHelper.ExportHtml(lstRows, Nothing, lstTime, strNickName, Me.mAndroid.DeviceName, eType, Nothing, False, Me.mAndroid.DeviceID)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "Export of frmAndroidWeixin")
        End Try

    End Sub

    Private Function GenerateChatmessage2ChatInfo(info As Chatmessage) As WeChatChatInfo
        Dim cInfo As WeChatChatInfo = New WeChatChatInfo()
        Try
            cInfo.DTCreateTime = info.createTime
            cInfo.IntID = info.msgId

            cInfo.StrMessage = AndroidWeixin.EmojiReplaceText(info.content)

            cInfo.IntStatus = IIf(info.isSend = 0, 4, 2) ' 2发送；4接收；3未读       0：联系人发给我或群中 1：我发给联系人或群中
            cInfo.SCType = info.msgtype
            cInfo.StrUsrName = info.wxsenderID
        Catch ex As Exception

        End Try

        Return cInfo
    End Function

	Private Sub WeixinImportOrExportHelper_ExportProgressEventHandler(ByVal sender As Object, ByVal e As ExportProgressEventArgs)
        Me.ChangeStateText(e.Message)
	End Sub


	'	Private Sub Export(ByVal blnExportToTxt As Boolean, ByVal type As ExportType)

	'		Dim strPath As String = String.Empty

	'		'如果导出全部，则选择一个目录即可
	'		If type = ExportType.AllFriend Then
	'			Dim fbdWeixin As New FolderBrowserDialog

	'			fbdWeixin.Description = Me.Language.GetString("Media.Button.Export") '导出
	'			fbdWeixin.ShowNewFolderButton = True


	'			If fbdWeixin.ShowDialog() = Windows.Forms.DialogResult.OK Then
	'				strPath = fbdWeixin.SelectedPath	 '目录
	'			Else
	'				Return
	'			End If

	'		ElseIf type = ExportType.CurrentPage OrElse type = ExportType.CurrentFriend Then
	'			Dim sfdWeixin As New SaveFileDialog
	'			sfdWeixin.Title = Me.Language.GetString("Media.Button.Export") '导出
	'			sfdWeixin.Filter = Me.Language.GetString("Note.Label.TxtFile") & String.Format(" (*.{0})|*.{0}", IIf(blnExportToTxt, "txt", "xls"))		   '文本文档 

	'			Dim strFileName As String = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") '"微信消息记录"

	'			'' 选择昵称
	'			Dim userName As String = String.Empty
	'			Dim msgCount As Integer = 0
	'			Dim strNickName As String = Me.GetSelectedNickName(userName, msgCount)

	'			If Not String.IsNullOrEmpty(strNickName) Then
	'				strFileName = String.Format("{0}--{1}", Common.ReplaceWinIllegalName(strNickName), strFileName)
	'			End If

	'			sfdWeixin.FileName = strFileName

	'			If sfdWeixin.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
	'				strPath = sfdWeixin.FileName	'文件路径
	'			Else
	'				Return
	'			End If
	'		End If


	'		'Dim thd As Thread = New Thread(New ParameterizedThreadStart(AddressOf ExportInThread))
	'		'thd.IsBackground = True

	'		'thd.Start(New Object() {blnExportToTxt, type, strPath})

	'		Try
	'			ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ExportInThread), New Object() {blnExportToTxt, type, strPath})
	'		Catch ex As Exception

	'		End Try

	'	End Sub

	'	Private Sub ExportInThread(ByVal objPara As Object)
	'		Try
	'			If objPara Is Nothing Then
	'				Return
	'			End If

	'			Dim blnExportToTxt As Boolean = objPara(0)
	'			Dim type As ExportType = objPara(1)
	'			Dim strFilePath As String = objPara(2)

	'			Select Case type
	'				Case ExportType.CopyToClipboard
	'					If Me.mDictChatMsgs Is Nothing OrElse Me.mDictChatMsgs.Count = 0 Then
	'						Me.ChangeStateText(Me.Language.GetString("Weixin.Message.NotChsooseAccount"))
	'					Else
	'						Me.ChangeStateText(Me.Language.GetString("Weixin.Message.Copying"))	'"正在复制..."
	'						Me.ExportToTxtFile(Me.mDictChatMsgs, "", True)
	'					End If
	'				Case ExportType.CurrentPage
	'					Me.ChangeStateText(String.Format(Me.Language.GetString("Weixin.Message.ExportMsg"), ""))	'"正在导出..."
	'					Dim result As Boolean = Me.ExportToFile(Me.mDictChatMsgs, strFilePath, blnExportToTxt)

	'					If result Then
	'						Me.ChangeStateText(Me.Language.GetString("Note.Message.ExportSucceed"))	   '"导出成功！"

	'						If File.Exists(strFilePath) Then
	'							Common.OpenExplorer(strFilePath)
	'						End If
	'					Else
	'						Me.ChangeStateText(Me.Language.GetString("Contact.Message.ExportFailure"))	  '"导出失败！"
	'					End If

	'				Case ExportType.CurrentFriend
	'					Dim strUsrName As String = String.Empty
	'					Dim msgCount As Integer = 0
	'					Dim strNickName As String = GetSelectedNickName(strUsrName, msgCount)


	'					Me.ChangeStateText(String.Format(Me.Language.GetString("Weixin.Message.ExportMsg"), """" + strNickName + """"))	'"正在导出 ""{0}""...."

	'					Dim dictChatMsg As Dictionary(Of String, Chatmessage) = Me.mWechatHelper.GetChatMessages(strUsrName)
	'					Dim result As Boolean = Me.ExportToFile(dictChatMsg, strFilePath, blnExportToTxt)

	'					If result Then
	'						Me.ChangeStateText(String.Format("""{0}"" ", strNickName) & Me.Language.GetString("Note.Message.ExportSucceed"))

	'						If File.Exists(strFilePath) Then
	'							Common.OpenExplorer(strFilePath)
	'						End If
	'					Else
	'						Me.ChangeStateText(String.Format("""{0}"" ", strNickName) & Me.Language.GetString("Contact.Message.ExportFailure"))	   '"导出失败！"
	'					End If

	'				Case ExportType.AllFriend

	'					For Each row As DataGridViewRow In Me.mDgvFriend.Rows
	'						Dim item As Rconversation = CType(row.Tag, Rconversation)
	'						If item Is Nothing Then Continue For

	'						Dim strNickName As String = item.nickname
	'						Dim strFile As String = String.Empty

	'						Me.ChangeStateText(String.Format(Me.Language.GetString("Weixin.Message.ExportMsg"), """" & strNickName & """"))	 '"正在导出 ""{0}""...."

	'						Dim dictChatMsg As Dictionary(Of String, Chatmessage) = Me.mWechatHelper.GetChatMessages(item.username)

	'						Dim strFileName As String = Me.Language.GetString("Weixin.Lable.WeixinMsgRecord") '"微信消息记录"
	'                        If Not String.IsNullOrEmpty(strNickName) Then
	'                            strFileName = String.Format("{0}--{1}", Common.ReplaceWinIllegalName(strNickName), strFileName)
	'                        End If

	'						If blnExportToTxt Then
	'							strFile = Path.Combine(strFilePath, strFileName & ".txt")
	'						Else
	'							strFile = Path.Combine(strFilePath, strFileName & ".xls")
	'						End If

	'						Me.ExportToFile(dictChatMsg, strFile, blnExportToTxt)

	'					Next

	'					Me.ChangeStateText(Me.Language.GetString("Note.Message.ExportSucceed"))	 '"导出成功！"
	'					Common.OpenExplorer(strFilePath)
	'			End Select
	'		Catch ex As Exception
	'			Common.LogException(ex.ToString(), "ExportInThread of frmAndroidWeixin")
	'		End Try
	'	End Sub

	'	Private Function ExportToFile(ByVal dictChatMsgs As Dictionary(Of String, Chatmessage), ByVal strFilePath As String, ByVal blnExportToTxt As Boolean) As Boolean
	'		Dim result As Boolean = False
	'		Dim strMsg As String = ""
	'		Dim wxState As WeixinAndroidState = WeixinAndroidState.Succeed

	'		If blnExportToTxt Then
	'			'导出为txt文件
	'			result = Me.ExportToTxtFile(dictChatMsgs, strFilePath, False)
	'			strMsg = "导出为txt文件"
	'		Else
	'			'导出为excel文件
	'            result = Me.ExportToExcelFile(dictChatMsgs, strFilePath)
	'			strMsg = "导出为excel文件"
	'		End If

	'		If Not result Then
	'			wxState = WeixinAndroidState.DownloadDBFailed
	'        End If

	'		ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), New Object() {wxState, ModelKey.AndroidWeixinExport, strMsg})

	'		Return result
	'	End Function

	'	'导出为txt文件
	'	Private Function ExportToTxtFile(ByVal dictChatMsgs As Dictionary(Of String, Chatmessage), ByVal strFilePath As String, ByVal blnCopyToClipboard As Boolean) As Boolean
	'		Dim result As Boolean = False
	'		If dictChatMsgs Is Nothing OrElse dictChatMsgs.Count = 0 Then
	'			Return result
	'		End If

	'		Try
	'			Dim strLines As New StringBuilder()
	'			If Not blnCopyToClipboard Then
	'				'"导出时间"  "微信消息记录"
	'				strLines.AppendLine(String.Format("{0}     {1}:{2}", _
	'									Me.Language.GetString("Weixin.Lable.WeixinMsgRecord"), _
	'									Me.Language.GetString("Backup.Label.ExportDate"), _
	'									DateTime.Now.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))))

	'				strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")
	'			End If

	'			'"时间"   "联系人"  "状态"  "类型"  "消息"
	'			''
	'            strLines.AppendLine(String.Format("{0}{1}{2}{3}{4}{5}", _
	'                                         Common.StringFormat("ID", 30, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("App.Button.TimeSort"), 25, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("Main.Button.Contact"), 25, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("File.Label.Status"), 20, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("Common.Label.ColumnType"), 20, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("Weixin.Lable.Message"), 2000, False, False, True, False)))

	'			strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")

	'			For Each chat As Chatmessage In dictChatMsgs.Values

	'                Dim strCreateTime As String = Common.StringFormat(chat.createTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), _
	'                                                                  25, False, True, True, False)
	'                Dim strShowName As String = chat.showname
	'                Dim strID As String = Common.StringFormat(chat.wxsenderID, 30, False, True, True, False)

	'				If chat.msgtype <> 10000 AndAlso String.IsNullOrEmpty(strShowName) Then
	'					strShowName = chat.wxsenderID
	'				End If

	'				strShowName = Common.StringFormat(strShowName, 25, False, True, True, False)

	'                Dim strReceive As String = Common.StringFormat(IIf(chat.isSend = 0, Me.Language.GetString("Backup.Label.Receive"), _
	'                                                               Me.Language.GetString("Backup.Label.Send")), 20, False, True, True, False)
	'				Dim strMsgType As String = Common.StringFormat(Me.GetChatType(chat.msgtype), 20, False, True, True, False)

	'				If Not String.IsNullOrEmpty(chat.content) Then

	'                    Try
	'                        Dim strContent As String = chat.content.Replace(vbCr, " ").Replace(vbLf, " ")
	'                        strcontent = Common.StringFormat(strcontent, 2000, False, False, True, False)

	'                        strLines.AppendLine(String.Format("{0}{1}{2}{3}{4}{5}", _
	'                                                    strID, _
	'                                                    strCreateTime, _
	'                                                    strShowName, _
	'                                                    strReceive, _
	'                                                    strMsgType, _
	'                                                    strContent))

	'                    Catch ex As Exception
	'                        Debug.Write(ex)
	'                    End Try
	'				Else
	'					Dim strContent As String = GetChatType(chat.msgtype)

	'                    strLines.AppendLine(String.Format("{0}{1}{2}{3}{4}{5}", _
	'                                                strID, _
	'                                                strCreateTime, _
	'                                                strShowName, _
	'                                                strReceive, _
	'                                                strMsgType, _
	'                                                Common.StringFormat(strContent, 2000, False, False, True, False)))
	'				End If

	'			Next


	'			strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")

	'			If blnCopyToClipboard Then
	'				CopyToClipboard(strLines.ToString())
	'				result = True
	'			Else
	'				File.WriteAllText(strFilePath, strLines.ToString())
	'				result = True
	'			End If

	'		Catch ex As Exception
	'			Common.LogException(ex.ToString(), "ExportToTxtFile of frmAndroidWeixin")

	'			result = False
	'		End Try


	'		Return result
	'	End Function

	Private Sub CheckIsInstallExcel()
		Dim thd As New Thread(AddressOf CheckIsInstallExcelInThread)
		thd.IsBackground = True
		thd.Start()
	End Sub

	Private Sub CheckIsInstallExcelInThread()
		Try
			Dim xlApp As Object = CreateObject("Excel.Application")

			If xlApp Is Nothing Then
				Me.m_bInstalledExcel = False
			Else
				Me.m_bInstalledExcel = True
				xlApp.Quit()
			End If
		Catch
			Me.m_bInstalledExcel = False
		End Try
	End Sub

	'    '导出Excel时，如果字符串以==或= =开始，导出会异常。
	'    Private Function FormatExcelCellText(ByVal strText As String) As String
	'        Dim result As String = strText
	'        Dim strTextTemp As String = strText

	'        If String.IsNullOrEmpty(strText) OrElse Not strTextTemp.StartsWith("=") Then
	'            GoTo DO_EIXT
	'        End If

	'        strTextTemp = strText.Replace(" ", "")

	'        If Not strTextTemp.StartsWith("==") Then
	'            GoTo DO_EIXT
	'        End If

	'        result = "'" & strText

	'DO_EIXT:
	'        Return result
	'    End Function

	'    Private Function ExportToExcelFile(ByVal dictChatMsgs As Dictionary(Of String, Chatmessage), ByVal strFilePath As String) As Boolean
	'        If dictChatMsgs Is Nothing Then
	'            Return False
	'        End If

	'        If Not m_bInstalledExcel Then
	'            Return Me.ExportToCSVFile(dictChatMsgs, strFilePath, False)
	'        End If


	'        Dim xlApp As Object = Nothing
	'        Dim xlSheet As Object = Nothing

	'        '创建Excel对象
	'        Try
	'            xlApp = CreateObject("Excel.Application")

	'            If xlApp IsNot Nothing Then
	'                Dim xlBook As Object = xlApp.Workbooks.Add
	'                xlSheet = xlBook.Worksheets(1)
	'            End If
	'        Catch ex As Exception
	'            Common.LogException("CreateExcel", ex.ToString())
	'            Return False
	'        End Try

	'        Try
	'            With xlSheet
	'                '1、设置标题
	'                .Cells(1, 1) = "ID"                   '时间
	'                .Cells(1, 2) = Me.Language.GetString("App.Button.TimeSort")                   '时间
	'                .Cells(1, 3) = Me.Language.GetString("Main.Button.Contact")     '联系人
	'                .Cells(1, 4) = Me.Language.GetString("File.Label.Status")                 '状态
	'                .Cells(1, 5) = Me.Language.GetString("Common.Label.ColumnType")                   '类型
	'                .Cells(1, 6) = Me.Language.GetString("Weixin.Lable.Message") '"消息"                '消息

	'                With .Range(.Cells(1, 1), .Cells(1, 6))
	'                    .Font.Size = 12
	'                    .Interior.ColorIndex = 45           '背景色为浅橙色
	'                    .Font.Bold = True                      '字体加粗
	'                    .Borders.LineStyle = 1
	'                End With

	'                .Rows(1).RowHeight = 25               '行高

	'                '2、设置第二列（时间列）
	'                With .Columns(2)
	'                    .HorizontalAlignment = -4152        '水平居右
	'                    .VerticalAlignment = -4108          '垂直居中
	'                End With

	'                '3、设置列宽
	'                .Columns(1).ColumnWidth = 30
	'                .Columns(2).ColumnWidth = 18
	'                .Columns(3).ColumnWidth = 18
	'                .Columns(4).ColumnWidth = 15
	'                .Columns(5).ColumnWidth = 15
	'                .Columns(6).ColumnWidth = 100

	'                '4、输出内容
	'                Dim rowIndex As Integer = 1

	'                For Each chat As Chatmessage In dictChatMsgs.Values

	'                    rowIndex += 1

	'                    Dim strCreateTime As String = Common.StringFormat(chat.createTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), 25, False, True, True, False)
	'                    Dim strShowName As String = chat.showname
	'                    If chat.msgtype <> 10000 AndAlso String.IsNullOrEmpty(strShowName) Then
	'                        strShowName = chat.wxsenderID
	'                    End If

	'                    strShowName = Common.StringFormat(strShowName, 25, False, True, True, False)

	'                    Dim strReceive As String = Common.StringFormat(IIf(chat.isSend = 0, Me.Language.GetString("Backup.Label.Receive"), _
	'                                                                   Me.Language.GetString("Backup.Label.Send")), 20, False, True, True, False)

	'                    Dim strMsgType As String = Common.StringFormat(Me.GetChatType(chat.msgtype), 20, False, True, True, False)
	'                    Dim strContent As String = chat.content

	'                    .Cells(rowIndex, 1) = chat.wxsenderID
	'                    .Cells(rowIndex, 2) = strCreateTime
	'                    .Cells(rowIndex, 3) = Me.FormatExcelCellText(strShowName)
	'                    .Cells(rowIndex, 4) = strReceive
	'                    .Cells(rowIndex, 5) = strMsgType
	'                    .Cells(rowIndex, 6) = Me.FormatExcelCellText(strContent)
	'                Next

	'                '5、设置单元格式
	'                With .Range(.Cells(2, 2), .Cells(rowIndex, 2))
	'                    .NumberFormatLocal = Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)  '日期格式
	'                End With
	'                'With .Range(.Cells(2, 1), .Cells(rowIndex + Me.mDictChatInfo.Count, 5))
	'                '    .NumberFormatLocal = "@"                               '文本格式
	'                'End With

	'                '6、设置样式（除标题栏）
	'                With .Range(.Cells(2, 1), .Cells(rowIndex, 6))
	'                    .Borders.LineStyle = 1           '表格边框样式
	'                    .Font.Size = 10                     '表格内容的字体为 10 号字
	'                End With

	'            End With

	'            If xlSheet IsNot Nothing Then
	'                xlSheet.SaveAs(strFilePath)
	'            End If

	'            If xlApp IsNot Nothing Then
	'                xlApp.Quit()
	'            End If

	'            Return True
	'        Catch ex As Exception
	'            If xlApp IsNot Nothing Then
	'                xlApp.Quit()
	'            End If

	'        End Try

	'        Return False
	'    End Function

	'	Private Function ExportToCSVFile(ByVal dictChatMsgs As Dictionary(Of String, Chatmessage), ByVal strFilePath As String, ByVal blnCopyToClipboard As Boolean) As Boolean
	'		Dim result As Boolean = False
	'		If dictChatMsgs Is Nothing Then
	'			Return result
	'		End If

	'		Try
	'			Dim strLines As New StringBuilder()

	'			If Not blnCopyToClipboard Then
	'				'"导出时间"  "微信消息记录"
	'				strLines.AppendLine(String.Format("{0}     {1}:{2},,,,", _
	'									Me.Language.GetString("Weixin.Lable.WeixinMsgRecord"), _
	'									Me.Language.GetString("Backup.Label.ExportDate"), _
	'									DateTime.Now.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))))
	'				'strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－,,,,")
	'			End If

	'			'"时间"   "联系人"  "状态"  "类型"  "消息"
	'			''
	'            strLines.AppendLine(String.Format("{0},{1},{2},{3},{4},{5}", _
	'                                         Common.StringFormat("ID", 30, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("App.Button.TimeSort"), 25, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("Main.Button.Contact"), 25, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("File.Label.Status"), 20, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("Common.Label.ColumnType"), 20, False, True, True, False), _
	'                                         Common.StringFormat(Me.Language.GetString("Weixin.Lable.Message"), 2000, False, False, True, False)))
	'			'strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")

	'			Dim strContent As String = ""

	'			For Each chat As Chatmessage In dictChatMsgs.Values

	'				Dim strCreateTime As String = Common.StringFormat(chat.createTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), 25, False, True, True, False)
	'				Dim strShowName As String = chat.showname
	'                Dim strID As String = Common.StringFormat(chat.wxsenderID, 30, False, True, True, False)

	'                If chat.msgtype <> 10000 AndAlso String.IsNullOrEmpty(strShowName) Then
	'                    strShowName = chat.wxsenderID
	'                End If

	'				strShowName = Common.StringFormat(strShowName, 25, False, True, True, False)

	'				Dim strReceive As String = Common.StringFormat(IIf(chat.isSend = 0, Me.Language.GetString("Backup.Label.Receive"), Me.Language.GetString("Backup.Label.Send")), 20, False, True, True, False)
	'				Dim strMsgType As String = Common.StringFormat(Me.GetChatType(chat.msgtype), 20, False, True, True, False)


	'				If Not String.IsNullOrEmpty(chat.content) Then

	'					Try
	'						strContent = chat.content
	'                        strContent = Common.StringFormat(strContent.Replace(",", "，"), 2000, False, False, True, False)
	'						strContent = strContent.Replace(vbCr, " ").Replace(vbLf, " ")

	'						''' excel把,当作分割符
	'                        strLines.AppendLine(String.Format("{0}{1}{2}{3}{4}{5}", _
	'                                                        strID, _
	'                                                        strCreateTime, _
	'                                                        strShowName, _
	'                                                        strReceive, _
	'                                                        strMsgType, _
	'                                                        strContent))

	'					Catch ex As Exception
	'						Debug.Write(ex)
	'					End Try
	'				Else
	'					strContent = Common.StringFormat(GetChatType(chat.msgtype), 2000, False, False, True, False)

	'                    strLines.AppendLine(String.Format("{0}{1}{2}{3}{4}{5}", _
	'                                        strID, _
	'                                        strCreateTime, _
	'                                        strShowName, _
	'                                        strReceive, _
	'                                        strMsgType, _
	'                                        strContent))
	'				End If

	'			Next

	'			'strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－,,,,")

	'			If blnCopyToClipboard Then
	'				CopyToClipboard(strLines.ToString())
	'				result = True
	'			Else
	'				Dim csvFName As String = strFilePath.Replace(".xls", ".csv")
	'				File.WriteAllText(csvFName, strLines.ToString, Encoding.Default)

	'				If File.Exists(csvFName) Then
	'					Try
	'						File.Copy(csvFName, strFilePath, True)
	'						File.Delete(csvFName)
	'					Catch ex As Exception
	'					End Try

	'				End If
	'				result = True

	'			End If

	'		Catch ex As Exception
	'			Common.LogException(ex.ToString(), "ExportToTxtFile")

	'			result = False
	'		End Try

	'		Return result
	'	End Function

	'	'复制页面全部消息
	'	Private Delegate Sub CopyToClipboardHandler(ByVal strContent As String)
	'	Private Sub CopyToClipboard(ByVal strContent As String)
	'		Try
	'			If Me.InvokeRequired Then
	'				Me.Invoke(New CopyToClipboardHandler(AddressOf CopyToClipboard), strContent)
	'			Else
	'				Try
	'					Clipboard.SetText(strContent.TrimEnd())
	'					Me.ChangeStateText(Me.Language.GetString("Welcome.Message.CopySucceed")) '复制成功
	'				Catch
	'					Me.ChangeStateText(Me.Language.GetString("Weixin.Message.CopyFail")) '"复制失败！"
	'				End Try
	'			End If
	'		Catch ex As Exception
	'			Common.LogException(ex.ToString(), "CopyToClipboard")

	'			Me.ChangeStateText(Me.Language.GetString("Weixin.Message.CopyFail")) '"复制失败！"
	'		End Try
	'	End Sub

#End Region

#Region "--- 私有方法 ---"

	Private Function GetSelectedNickName(ByRef usrName As String, ByRef msgCount As Integer) As String
		Dim strNickName As String = String.Empty

		If mDgvFriend IsNot Nothing AndAlso mDgvFriend.SelectedRows IsNot Nothing AndAlso mDgvFriend.SelectedRows.Count > 0 Then
			Dim row As DataGridViewRow = Me.mDgvFriend.SelectedRows(0)
			Dim item As Rconversation = CType(row.Tag, Rconversation)

			If item IsNot Nothing Then
				strNickName = item.nickname
				usrName = item.username
				msgCount = item.msgcount
			End If
		End If

		Return strNickName
	End Function

	Private Function GetChatMsg(ByVal chat As Chatmessage) As String
		Dim strText As String = String.Empty

		Select Case chat.msgtype
			Case 318767153
				strText = "[安全登录提醒]"
			Case 1, 10000
				'strText = Me.Language.GetString("Weixin.Item.Text") '"文本"
				strText = chat.content
			Case 3
				strText = "[" + Me.Language.GetString("Main.Button.Photo") + "]" '"图片"
			Case 34
				strText = "[" + Me.Language.GetString("Weixin.Item.Audio") + "]" '"语音"
			Case 42
				strText = "[" + Me.Language.GetString("Weixin.Item.BusinessCard") + "]" + chat.content '"名片"
			Case 43
				strText = "[" + Me.Language.GetString("Media.Type.Video") + "]"	'"视频"
			Case 47
				strText = "[" + Me.Language.GetString("Weixin.Item.Expression") + "]" '"动画表情"
			Case 48
				strText = "[" + Me.Language.GetString("Weixin.Item.Location") + "]" + chat.content '"地理位置"
			Case 49
				strText = chat.content
				If String.IsNullOrEmpty(strText) Then
					strText = "[" + Me.Language.GetString("Weixin.Item.WebPage") + "]" '"网页"
				ElseIf strText.StartsWith("[File] ") Then
					strText = strText.Replace("[File] ", "[" + Me.Language.GetString("Common.File") + "]")
				End If

			Case 50
				strText = "[" + Me.Language.GetString("Weixin.Item.Video") + "]" '"视频聊天"
			Case 62
				strText = "[" + Me.Language.GetString("Weixin.Lable.SmallVideo") + "]" '"小视频"
		End Select

		Return strText
	End Function

	Private Function GetChatType(ByVal msgtype As Integer) As String
		Dim strText As String = String.Empty

		Select Case msgtype
			Case 1
				strText = Me.Language.GetString("Weixin.Item.Text")	'"文本"
			Case 3
				strText = Me.Language.GetString("Main.Button.Photo") '"图片"
			Case 34
				strText = Me.Language.GetString("Weixin.Item.Audio") '"语音"
			Case 42
				strText = Me.Language.GetString("Weixin.Item.BusinessCard")	'"名片"
			Case 43
				strText = Me.Language.GetString("Media.Type.Video")	'"视频"
			Case 47
				strText = Me.Language.GetString("Weixin.Item.Expression") '"动画表情"
			Case 48
				strText = Me.Language.GetString("Weixin.Item.Location")	'"地理位置"
			Case 49
				strText = Me.Language.GetString("Weixin.Item.WebPage") '"网页"
			Case 50
				strText = Me.Language.GetString("Weixin.Item.Video") '"视频聊天"
			Case 62
				strText = Me.Language.GetString("Weixin.Lable.SmallVideo") '"小视频"
			Case 10000
				strText = Me.Language.GetString("Weixin.Item.SystemInfo") '"系统消息"
		End Select

		Return strText
	End Function

	Private Function GetLastChatTime(ByVal LastChatTime As DateTime) As String
		Dim strTime As String = String.Empty
		Dim ts As TimeSpan = Now.Date.Subtract(LastChatTime.Date) '只需比较日期就好

		Select Case ts.Days
			Case 0
				strTime = LastChatTime.ToString("HH:mm")
			Case 1
				strTime = Me.Language.GetString("Note.Label.Yesterday")	'"昨天"
			Case Else
				Dim strYear As String = LastChatTime.Year.ToString().Substring(2, 2)
				Dim strMonth As String = LastChatTime.Month.ToString()
				Dim strDay As String = LastChatTime.Day.ToString()

				strTime = String.Format("{0}-{1}-{2}", strYear, strMonth, strDay)
		End Select

		Return strTime
	End Function

#End Region

#Region "--- 特殊兼容方法 ---"

	'从手机上无法获取微信默认用户的头像。
	Private Function ReleaseSpecialHeadIcon(ByVal strUsrName As String, ByVal strFileOnPC As String) As Boolean
		Dim result As Boolean = True

		Dim img As Image = Nothing
		Select Case strUsrName
			Case "qqmail"			   'QQ邮箱提醒
				img = My.Resources.weixin_icon_qqmail
			Case "medianote"		'语音记事本
				img = My.Resources.weixin_icon_medianote
			Case "newsapp"			 '腾讯新闻
				img = My.Resources.weixin_icon_newsapp
			Case "blogapp"			  '微博阅读
				img = My.Resources.weixin_icon_blogapp
			Case "masssendapp"	  '群发助手
				img = My.Resources.weixin_icon_masssendapp
			Case "qqsync"			   '通讯录安全助手
				img = My.Resources.weixin_icon_qqsync
			Case Else
				result = False
		End Select

		If img IsNot Nothing AndAlso _
		   Not String.IsNullOrEmpty(strFileOnPC) AndAlso _
		   Not File.Exists(strFileOnPC) Then

			img.Save(strFileOnPC)
		End If

		Return result
	End Function

#End Region

#Region "--- 备份、还原、删除、保存 ---"

	Private Sub DoDelete()
		If Me.mDgvFriend.SelectedRows.Count = 0 Then
			Return
		End If

		Dim row As DataGridViewRow = Me.mDgvFriend.SelectedRows(0)

	End Sub

	Private Delegate Sub OnTalkingStatusEventHandler(ByVal e As iTong.Device.TalkingStatusEventArgs)
	Private Sub OnTalkingStatusEvent(ByVal e As iTong.Device.TalkingStatusEventArgs)
		If Me.InvokeRequired Then
			Me.Invoke(New OnTalkingStatusEventHandler(AddressOf OnTalkingStatusEvent), e)
		Else
			Select Case e.State
				Case kApplicationStatus.ArchivingApplication
					'正在备份
					Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("App.Cell.Archiving"), e.PercentComplete) '"Archiving"

				Case kApplicationStatus.Error
					'备份失败

					Me.lblState.Text = Me.Language.GetString("App.Cell.BackupFail")	'"备份失败,请尝试解锁屏幕或重启设备。"


					Me.btnBackup.Enabled = True


				Case kApplicationStatus.ExtractingPackage
					Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("Common.Loading"), e.PercentComplete) '"Loading"

				Case kApplicationStatus.None
					Me.lblState.Text = Me.Language.GetString("App.Cell.BackupSucceed")
					Me.btnBackup.Enabled = True


			End Select
			Application.DoEvents()

		End If
	End Sub

	Private Delegate Sub OnDataTransferEventHandler(ByVal e As DataTransferEventArgs)
	Private Sub OnDataTransferEvent(ByVal e As DataTransferEventArgs)
		If Me.InvokeRequired Then
			Me.Invoke(New OnDataTransferEventHandler(AddressOf OnDataTransferEvent), e)
		Else
			Dim intPercent As Integer = IIf(e.FileSize = 0, 0, e.TransSize * 100 / e.FileSize)
			If Not e.Cancel Then
				'正在下载备份文件. {0}%
				Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("WeiBo.Message.DownloadingBackupFile"), intPercent)	' '"Downloading"
			End If
			If e.Cancel Then
				Me.btnBackup.Enabled = True
				Me.btnRestore.Enabled = True
			End If
			Application.DoEvents()

		End If
	End Sub

	Private Sub cmsRestore_Opening(ByVal sender As System.Object, ByVal e As System.ComponentModel.CancelEventArgs) Handles cmsRestore.Opening

		Try

		Catch ex As Exception
			Common.LogException(ex.ToString, "frmWeixin_cmsRestore_Opening")
		End Try
	End Sub

	Private Sub OnMenuClick(ByVal sender As System.Object, ByVal e As System.EventArgs)
		Dim menu As ToolStripMenuItem = sender
		If menu.Tag IsNot Nothing AndAlso TypeOf menu.Tag Is String Then
			Me.DoRestore(menu.Tag)
		End If
	End Sub

	Private Sub DoRestore(ByVal strPath As String)
		'If Not File.Exists(strPath) Then
		'    tbMessageBox.Show(Me, Me.Language.GetString("Media.Message.FileNoExist"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK)
		'    Return
		'End If
		'Me.btnBackup.Enabled = False
		'Me.btnRestore.Enabled = False

		'Dim thr As New Thread(New ParameterizedThreadStart(AddressOf DoRestoreThread))
		'With thr
		'    .IsBackground = True
		'    .Start(strPath)
		'End With
	End Sub

	Private Delegate Sub mInstallHelper_OnInstallHandler(ByVal sender As Object, ByVal args As InstallArgs)
	Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
		If Me.InvokeRequired Then
			Me.Invoke(New mInstallHelper_OnInstallHandler(AddressOf mInstallHelper_OnInstall), sender, args)
		Else
			Try
				Select Case args.InstallState
					Case InstallState.Nomal

					Case InstallState.Waiting
						Me.lblState.Text = Me.Language.GetString("Download.Label.Waiting") '"Waiting Install"

					Case InstallState.AuthBegin
						Me.lblState.Text = Me.Language.GetString("App.Message.Calculating")				 '"正在计算" 

					Case InstallState.Transfering
						Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("File.Label.Uploading"), args.TransferProgress) '"Uploading"

					Case InstallState.Installing
						If args.InstallApplicationEventArgs IsNot Nothing Then
							Select Case args.InstallApplicationEventArgs.InstallProgress
								Case InstallapplicationProgress.OnUnzip, _
									 InstallapplicationProgress.OnRestoreContainer
									Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("App.Cell.Restoring"), args.TransferProgress) '"Restoring"

								Case InstallapplicationProgress.OnInstall
									Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("Backup.Message.RestoreMessage"), 0)	'"Installing"

							End Select
						Else
							Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("Backup.Message.RestoreMessage"), args.TransferProgress)	'"Installing"

						End If
						If args.TransferProgress = 100 Then
							Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreSucceed")
							Me.btnBackup.Enabled = True
							Me.btnRestore.Enabled = True
						End If


					Case InstallState.Succeed
						Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreSucceed") '"安装成功"

					Case InstallState.HaveNotInstall
						Me.lblState.Text = args.ErrorMsg

					Case InstallState.UnSucceed
						Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreFailure")

					Case InstallState.CancelInstall


					Case InstallState.AlreadyInstall


					Case InstallState.AllInstallCompleted
						Me.btnBackup.Enabled = True
						Me.btnRestore.Enabled = True

						Me.LoadWechatData()
					Case InstallState.BackupRecord
						Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("App.Cell.BackupRecord"), args.TransferProgress)	'"正在备份记录"

				End Select
			Catch ex As Exception
				Common.LogException(ex.ToString(), "mInstallHelper_OnInstall of frmAndroidWeixin")
			End Try
		End If
	End Sub

	Private Function GetBackupPackageFolder() As String
		Dim strReturn As String = Path.Combine(Folder.BackupFolder, "Wechat")
		strReturn = Path.Combine(strReturn, Me.mDevice.Identifier)
		Folder.CheckFolder(strReturn)
		Return strReturn
	End Function

	Private Function GetBackupPackagePath() As String
		Dim strReturn As String = Me.GetBackupPackageFolder()
		strReturn = Path.Combine(strReturn, String.Format("{0}.zip", DateTime.Now.ToString("yyyy-MM-dd HH_mm_ss")))
		Return strReturn
	End Function

	Private Sub OnRestoreEvent(ByVal e As DataTransferEventArgs)
		If Me.InvokeRequired Then
			Me.Invoke(New OnDataTransferEventHandler(AddressOf OnRestoreEvent), e)
		Else
			Dim intPercent As Integer = IIf(e.FileSize = 0, 0, e.TransSize * 100 / e.FileSize)
			If Not e.Cancel Then
				'正在下载备份文件. {0}%
				Me.lblState.Text = String.Format("{0} {1}%", Me.Language.GetString("Backup.Message.RestoreMessage"), intPercent) '"Installing"
			End If
			If e.Cancel OrElse intPercent >= 100 Then
				Me.btnBackup.Enabled = True
				Me.btnRestore.Enabled = True
			End If

			If intPercent >= 100 Then
				Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreSucceed")
			ElseIf e.Cancel Then
				Me.lblState.Text = Me.Language.GetString("Backup.Message.RestoreFailure")
			End If
			Application.DoEvents()
		End If
	End Sub

#End Region

#Region "--- 导出视频、声音、图片 ---"

	''' 下载聊天图片、声音、视频
	Private Function DownloadChatMsgsFromPhone(ByVal strChatPath As String, _
											   ByVal ext As String, _
											   ByVal msgtype As Integer, _
											   ByVal imagePath As String) As String
		Dim strImgName As String = String.Empty

		If Not String.IsNullOrEmpty(strChatPath) Then

			Try
				Dim npos As Integer = strChatPath.LastIndexOf("/")
				If npos <= 0 Then

					If Not strChatPath.Contains("ChatWebPage\wx\images\") Then
                        strImgName = Path.Combine(Folder.TempFolder, "ChatWebPage\wx\images\" & strChatPath)
					Else
						strImgName = strChatPath
					End If
				Else
					strImgName = strChatPath.Substring(npos + 1)
				End If

				Select Case msgtype
					Case 34
						strImgName = Path.Combine(imagePath, strImgName) + ext
						strChatPath += ext
					Case 43, 62
						strImgName = strChatPath.Substring(npos + 1)
						strImgName = Path.Combine(imagePath, strImgName) + ".mp4"
						strChatPath += ".mp4"
					Case Else
						If npos > 0 Then
							strImgName = Path.Combine(imagePath, strImgName)
						End If
				End Select

				If Not File.Exists(strImgName) Then
					'If mAndroid.FileEntriesDictionaries IsNot Nothing Then
					'	mAndroid.FileEntriesDictionaries.Clear()
					'End If

					Dim bChat As Boolean = mAndroid.DownFromPhone(strChatPath, strImgName)
					If Not bChat AndAlso Not File.Exists(strImgName) AndAlso Not String.IsNullOrEmpty(mAndroid.ExtSDPath) Then
						'' Huawei C8813手机的微信图片地址在外部SD卡
						strChatPath = strChatPath.Replace(mAndroid.SDPath, mAndroid.ExtSDPath)
						mAndroid.DownFromPhone(strChatPath, strImgName)
					End If

				End If

				Dim strThumbName As String = strImgName + "_thumb"
				If Not File.Exists(strThumbName) Then

					''' 判断是否存在缩略图
					Dim strImgOnDevice As String = strChatPath + "_thumb"
					'If mAndroid.FileEntriesDictionaries IsNot Nothing Then
					'	mAndroid.FileEntriesDictionaries.Clear()
					'End If
					mAndroid.DownFromPhone(strImgOnDevice, strThumbName)
				End If
			Catch ex As Exception
				Common.LogException(ex.ToString(), "DownloadChatMsgsFromPhone of frmAndroidWeixin")
			End Try
		End If

		Return strImgName
	End Function

	Private Function GetValidFilePathName(ByVal fName As String) As String
		Dim strFName As String = fName
		Try
			Dim strTmpPath As String = Path.GetDirectoryName(fName)
			''' 处理文件名中包含的特殊字符
			''' 
			strFName = Common.ReplaceWinIllegalName(Path.GetFileName(fName))

			strFName = Path.Combine(strTmpPath, strFName)
		Catch
		End Try

		Return strFName
	End Function

	Private Sub ExportAudioByUserId(ByVal isSelect As Boolean, Optional ByVal nickName As String = "")
		Dim fileDlg As New FolderBrowserDialog()

		If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
			Try
				ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ExportAudioByUserIdThread), New Object() {fileDlg.SelectedPath, isSelect, nickName})
			Catch ex As Exception

			End Try

		End If
	End Sub

	Private Sub ExportAudioByUserIdThread(ByVal params As Object)
		If params Is Nothing Then Return

		Dim strFilePath As String = params(0)
		Dim isSelect As Boolean = params(1)
		Dim nickName As String = params(2)

		Dim intCount As Integer = 0
		Dim nNoneCount As Integer = 0

		Dim dictChatMsg As New Dictionary(Of String, Chatmessage)

		If isSelect Then
			Dim usrName As String = String.Empty
			Dim msgCount As Integer = 0

			GetSelectedNickName(usrName, msgCount)

			dictChatMsg = Me.mWechatHelper.GetChatMessages(usrName)
		Else
			For Each row As DataGridViewRow In Me.mDgvFriend.Rows
				If row.Tag Is Nothing OrElse Not TypeOf row.Tag Is Rconversation Then
					Continue For
				End If

				Dim item As Rconversation = CType(row.Tag, Rconversation)
				If item Is Nothing Then Continue For

				Dim dictChatTmp As Dictionary(Of String, Chatmessage) = Me.mWechatHelper.GetChatMessages(item.username)

				Try
					For Each sKey As String In dictChatTmp.Keys
						If Not dictChatMsg.ContainsKey(sKey) Then
							Dim tmpChat As Chatmessage = dictChatTmp(sKey)
							If tmpChat.msgtype = 34 Then
								dictChatMsg.Add(sKey, dictChatTmp(sKey))
							End If
						End If
					Next
				Catch ex As Exception
					Common.LogException(ex.ToString(), "ExportAudioByUserIdThread of frmAndroidWeixin")
				End Try

			Next
		End If

		Me.ChangeStateText(String.Empty)
		For Each chat As Chatmessage In dictChatMsg.Values
			If Not (chat.msgtype = 34) Then
				Continue For
			End If

			'If String.IsNullOrEmpty(nickName) Then
			'    nickName = chat.showname
			'End If

			Dim strName As String = String.Format("{0}_{1}_{2}_{3}", chat.createTime.ToString("yyyy-MM-dd HH-mm-ss"), chat.showname, chat.msgId, Me.Language.GetString("Weixin.Item.Audio"))   '语音

			'' 文件名可能含有非法字符
			strName = GetValidFilePathName(strName)
			

			Dim strImagePath As String = Path.Combine(mWechatPictureFolderOnPC, "image")
			Dim strSavePath As String = Path.Combine(strFilePath, strName)

			Dim strImgName As String = String.Empty
			Try
				strImgName = Me.DownloadChatMsgsFromPhone(chat.imgPath, chat.fileext, chat.msgtype, strImagePath)
				Dim strWaveName As String = Me.GetChatWavPathFromLocal(chat)

				If File.Exists(strWaveName) Then
					strSavePath += ".wav"
					File.Copy(strWaveName, strSavePath, True)

					intCount += 1

					Me.ChangeStateText(String.Format(Me.Language.GetString("Weixin.Message.ExportingVoice"), intCount))		'正在导出第 {0} 个语音
				Else
					nNoneCount += 1
				End If
			Catch ex As Exception
				Common.LogException(ex.ToString(), "ExportAudioByUserIdThread of frmAndroidWeixin")
			End Try
		Next

		Dim strMsg As String = ""

		If intCount = 0 Then

			strMsg = Me.Language.GetString("Weixin.Message.NotVoice")

			ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
										 New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, strMsg})

			Me.ChangeStateText(strMsg)	  '无可导出的语音
		Else

			If nNoneCount <> 0 Then
				strMsg = String.Format(Me.Language.GetString("Weixin.Message.CannotExportVoice"), nNoneCount)  ''有{0}个语音不能导出

				ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
											 New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, strMsg})

				strMsg += "，"
			End If

			Dim strSuccess As String = String.Format(Me.Language.GetString("Weixin.Message.ExportedAudioCount"), intCount)	'"共导出{0}个语音"
			ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
										 New Object() {WeixinAndroidState.Succeed, ModelKey.AndroidWeixinExport, strSuccess})

			strMsg += strSuccess
			Me.ChangeStateText(strMsg)
		End If

	End Sub

	Private Sub ExportPhotoByUserIdThread(ByVal params As Object)

		If params Is Nothing Then Return

		Dim strFilePath As String = params(0)
		Dim isSelect As Boolean = params(1)
		Dim nickName As String = params(2)

		Dim intCount As Integer = 0
		Dim nNoneCount As Integer = 0

		Dim dictChatMsg As New Dictionary(Of String, Chatmessage)

		If isSelect Then
			Dim usrName As String = String.Empty
			Dim msgCount As Integer = 0

			GetSelectedNickName(usrName, msgCount)

			dictChatMsg = Me.mWechatHelper.GetChatMessages(usrName)
		Else
			For Each rowItem As DataGridViewRow In Me.mDgvFriend.Rows
				If rowItem.Tag Is Nothing OrElse Not TypeOf rowItem.Tag Is Rconversation Then
					Continue For
				End If

				Dim itemTmp As Rconversation = CType(rowItem.Tag, Rconversation)
				If itemTmp Is Nothing Then Continue For

				Dim dictChatTmp As Dictionary(Of String, Chatmessage) = Me.mWechatHelper.GetChatMessages(itemTmp.username)

				For Each sKey As String In dictChatTmp.Keys
					If Not dictChatMsg.ContainsKey(sKey) Then
						Dim tmpChat As Chatmessage = dictChatTmp(sKey)
						If tmpChat.msgtype = 3 OrElse tmpChat.msgtype = 47 Then
							dictChatMsg.Add(sKey, dictChatTmp(sKey))
						End If
					End If
				Next
			Next
		End If

		Me.ChangeStateText(String.Empty)

		For Each chat As Chatmessage In dictChatMsg.Values
			If Not (chat.msgtype = 3 OrElse chat.msgtype = 47) Then
				Continue For
			End If

			'If String.IsNullOrEmpty(nickName) Then
			'    nickName = chat.showname
			'End If


			Dim strName As String = String.Format("{0}_{1}_{2}_{3}", chat.createTime.ToString("yyyy-MM-dd HH-mm-ss"), chat.showname, chat.msgId, Me.Language.GetString("Main.Button.Photo"))


			'' 文件名可能含有非法字符
			strName = GetValidFilePathName(strName)

			Dim strSavePath As String = Path.Combine(strFilePath, strName)
			Dim strImgName As String = chat.imgPath
			Dim ext As String = chat.fileext

			Try
				strImgName = Me.GetChatImagePathFromLocal(chat, True)


				If Not String.IsNullOrEmpty(strImgName) AndAlso File.Exists(strImgName) AndAlso Not String.IsNullOrEmpty(ext) Then
					strSavePath += ext
					File.Copy(strImgName, strSavePath, True)
					intCount += 1
					Me.ChangeStateText(String.Format(Me.Language.GetString("Weixin.Message.ExportingPicture"), intCount))	 '正在导出第 {0} 张图片
				Else
					nNoneCount += 1
				End If

			Catch ex As Exception
				Common.LogException(ex.ToString(), "ExportPhotoByUserIdThread of frmAndroidWeixin")
			End Try
		Next

		Dim strMsg As String = ""

		If intCount = 0 Then
			strMsg = Me.Language.GetString("Weixin.Message.NotPicture")
			Me.ChangeStateText(strMsg)	   '无可导出的图片

			ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
										 New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, strMsg})
		Else

			If nNoneCount <> 0 Then
				strMsg = String.Format(Me.Language.GetString("Weixin.Message.CannotExportPicture"), nNoneCount)	   '有{0} 个图片不能导出
				ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
											 New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, strMsg})
				strMsg += "，"
			End If

			Dim strSuccess As String = String.Format(Me.Language.GetString("Weixin.Message.ExportedPhotoCount"), intCount)	  '共导出{0}张图片
			ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
										 New Object() {WeixinAndroidState.Succeed, ModelKey.AndroidWeixinExport, strSuccess})

			strMsg += strSuccess
			Me.ChangeStateText(strMsg)	   '"共导出{0}个图片"
		End If

	End Sub

	Private Sub ExportPhotoByUserId(ByVal isSelect As Boolean, Optional ByVal nickName As String = "")
		Dim fileDlg As New FolderBrowserDialog()

		If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
			Try
				ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ExportPhotoByUserIdThread), New Object() {fileDlg.SelectedPath, isSelect, nickName})
			Catch ex As Exception
				Common.LogException(ex.ToString(), "ExportPhotoByUserId of frmAndroidWeixin")
			End Try

		End If
	End Sub

	Private Sub ExportVideoByUserId(ByVal isSelect As Boolean, Optional ByVal nickName As String = "")
		Dim fileDlg As New FolderBrowserDialog()
		If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
			Try
				ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ExportVideoByUserIdThread), New Object() {fileDlg.SelectedPath, isSelect, nickName})
			Catch ex As Exception
				Common.LogException(ex.ToString(), "ExportVideoByUserId of frmAndroidWeixin")
			End Try
		End If
	End Sub

	Private Sub ExportVideoByUserIdThread(ByVal params As Object)
		If params Is Nothing Then Return

		Dim strFilePath As String = params(0)
		Dim isSelect As Boolean = params(1)
		Dim nickName As String = params(2)
		Dim intCount As Integer = 0
		Dim nNoneCount As Integer = 0
		Dim dictChatMsg As New Dictionary(Of String, Chatmessage)

		If isSelect Then
			Dim usrName As String = String.Empty
			Dim msgCount As Integer = 0

			GetSelectedNickName(usrName, msgCount)

			dictChatMsg = Me.mWechatHelper.GetChatMessages(usrName)
		Else
			For Each row As DataGridViewRow In Me.mDgvFriend.Rows
				If row.Tag Is Nothing OrElse Not TypeOf row.Tag Is Rconversation Then
					Continue For
				End If

				Dim item As Rconversation = CType(row.Tag, Rconversation)
				If item Is Nothing Then Continue For

				Dim dictChatTmp As Dictionary(Of String, Chatmessage) = Me.mWechatHelper.GetChatMessages(item.username)
				Try
					For Each sKey As String In dictChatTmp.Keys
						If Not dictChatMsg.ContainsKey(sKey) Then
							Dim tmpChat As Chatmessage = dictChatTmp(sKey)
							If tmpChat.msgtype = 62 OrElse tmpChat.msgtype = 43 Then
								dictChatMsg.Add(sKey, dictChatTmp(sKey))
							End If
						End If
					Next
				Catch ex As Exception
					Common.LogException(ex.ToString(), "ExportVideoByUserIdThread of frmAndroidWeixin")
				End Try

			Next
		End If

		Me.ChangeStateText(String.Empty)

		For Each chat As Chatmessage In dictChatMsg.Values
			If Not (chat.msgtype = 62 OrElse chat.msgtype = 43) Then
				Continue For
			End If

			'If String.IsNullOrEmpty(nickName) Then
			'    nickName = chat.showname
			'End If

			Dim strName As String = String.Format("{0}_{1}_{2}_{3}", chat.createTime.ToString("yyyy-MM-dd HH-mm-ss"), chat.showname, chat.msgId, Me.Language.GetString("Weixin.Lable.SmallVideo"))

			'' 文件名可能含有非法字符
			strName = GetValidFilePathName(strName)

			Dim strSavePath As String = Path.Combine(strFilePath, strName)
			Dim chatImgPath As String = chat.imgPath
			Dim strImgName As String = String.Empty
			Dim strImagePath As String = Path.Combine(mWechatPictureFolderOnPC, "image")

			Try
				strImgName = Me.DownloadChatMsgsFromPhone(chat.imgPath, chat.fileext, chat.msgtype, strImagePath)


				If Not String.IsNullOrEmpty(strImgName) AndAlso File.Exists(strImgName) Then
					strSavePath += ".mp4"
					File.Copy(strImgName, strSavePath, True)

					intCount += 1

					Me.ChangeStateText(String.Format(Me.Language.GetString("Weixin.Message.ExportingVideo"), intCount))	   '正在导出第{0}个视频
				Else
					nNoneCount += 1
				End If

			Catch ex As Exception
				Common.LogException(ex.ToString(), "ExportVideoByUserIdThread of frmAndroidWeixin")
			End Try
		Next

		Dim strMsg As String = ""

		If intCount = 0 Then

			strMsg = Me.Language.GetString("Weixin.Message.NotVideo")
			Me.ChangeStateText(strMsg)		'无可导出的视频

			ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
										 New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, strMsg})
		Else

			If nNoneCount <> 0 Then
				strMsg = String.Format(Me.Language.GetString("Weixin.Message.CannotExportVideo"), nNoneCount)  '有{0} 个视频不能导出
				ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
											 New Object() {WeixinAndroidState.DownloadDBFailed, ModelKey.AndroidWeixinExport, strMsg})

				strMsg += "，"
			End If

			Dim strSuccess As String = String.Format(Me.Language.GetString("Weixin.Message.ExportedVideoCount"), intCount)		'共导出{0}个小视频

			ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf UploadData2Server), _
										 New Object() {WeixinAndroidState.Succeed, ModelKey.AndroidWeixinExport, strSuccess})

			strMsg += strSuccess
			Me.ChangeStateText(strMsg)		   '"共导出{0}个小视频"
		End If


	End Sub

#End Region


    Private Sub lblOtherRootTool_Click(sender As Object, e As EventArgs)
        Common.OpenExplorer("http://news.tongbu.com/78360.html")
    End Sub

    Private Sub lblCacelLimit_Click(sender As Object, e As EventArgs) Handles lblCacelLimit.Click
        Common.OpenExplorer("http://news.tongbu.com/87279.html")
    End Sub

    Private Sub lblHowtoRoot_Click(sender As Object, e As EventArgs) Handles lblHowtoRoot.Click
        Common.OpenExplorer("http://news.tongbu.com/78360.html")
    End Sub

	Private Sub frmAndroidWeixin_FormClosed(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosedEventArgs) Handles Me.FormClosed
		If mWechatHelper IsNot Nothing Then
			Me.mWechatHelper.ClearTempDirectory()
		End If
	End Sub

	Private mDictAccount As New Dictionary(Of String, String)

	Public Sub TdLoadAccount()
		If Me.mTdLoadAccount IsNot Nothing AndAlso Me.mTdLoadAccount.ThreadState <> ThreadState.Stopped Then
			Return
		End If

		Me.mTdLoadAccount = New Thread(AddressOf LoadAccount)
		Me.mTdLoadAccount.IsBackground = True
		Me.mTdLoadAccount.Start()

	End Sub

	Private Sub LoadAccount()
		Try
			If Me.mWechatPictureFolderOnPC.Length = 0 OrElse Not Directory.Exists(Me.mWechatPictureFolderOnPC) Then
				Return
			End If

			Me.mDictAccount.Clear()

			Dim arrAllKey As String() = IniClass.GetIniSectionAllKey("WeChatUin", Folder.ConfigIniFile)
			If arrAllKey.Length > 0 Then
				For Each strKey As String In arrAllKey
                    Dim strValue As String = Common.DecryptDES(IniClass.GetIniSectionKey("WeChatUin", strKey, Folder.ConfigIniFile), IMExportHelper.UinSecretKey, IMExportHelper.UinSecretKey)
					If Me.mDictAccount.ContainsKey(strKey) = False Then
						Me.mDictAccount.Add(strKey, strValue)
					End If
				Next
			End If

			Dim lstKeys As New List(Of String)
			Dim strDirectory As String = Path.GetDirectoryName(Me.mWechatPictureFolderOnPC)

			Dim strDirInfo As New DirectoryInfo(strDirectory)
			Dim jArr As New JsonArray
			For Each dicInfo As DirectoryInfo In strDirInfo.GetDirectories()
				If dicInfo.Name.Length >= 32 AndAlso Not dicInfo.Name.Contains("00000000000000000000000000000000") AndAlso Not Me.mDictAccount.ContainsKey(dicInfo.Name) Then
					jArr.Add(dicInfo.Name)
					lstKeys.Add(dicInfo.Name)
				End If
			Next
			If jArr.Count = 0 Then
				Return
			End If

            Try
                Dim jObject As New JsonObject
                jObject.Add("Vs", jArr)
                Dim strJson As String = JsonParser.SaveString(jObject)
                Dim dictSUin As Dictionary(Of String, String) = HtmlHelper.WeixinUIN(strJson, lstKeys)

                If dictSUin.Count > 0 Then
                    For Each item As KeyValuePair(Of String, String) In dictSUin
                        IniClass.SetIniSectionKey("WeChatUin", item.Key, Common.EncryptDES(item.Value, IMExportHelper.UinSecretKey, IMExportHelper.UinSecretKey), Folder.ConfigIniFile)
                        If Not Me.mDictAccount.ContainsKey(item.Key) Then
                            Me.mDictAccount.Add(item.Key, item.Value)
                        End If
                    Next

                    Dim isResult As Boolean = False
                    Dim strUin As String = ""
                    If dictSUin.ContainsKey(Me.mWechatHelper._UinEncryption) Then
                        strUin = dictSUin(Me.mWechatHelper._UinEncryption)
                        If strUin = Me.mWechatHelper._Uin Then
                            isResult = True
                        End If
                    End If

                    ActionCollectHelper.OperateAndroidWeixin(Me.mAndroid, ModelKey.AndroidWeixinUin, ModelKey.AndroidWeixinUin, ActionDataType.View, _
                                                             IIf(isResult, FunctionSucceed.Succeed, FunctionSucceed.Failure), _
                                                             String.Format("{0}  ServerUin:{1};Uin:{2}", Me.mWechatHelper._UinEncryption, strUin, Me.mWechatHelper._Uin))
                Else
                    ActionCollectHelper.OperateAndroidWeixin(Me.mAndroid, ModelKey.AndroidWeixinUin, ModelKey.AndroidWeixinUin, ActionDataType.View, _
                                                             FunctionSucceed.None, _
                                                             "服务器返回空数据")

                End If
            Catch ex As Exception

            End Try


		Catch ex As Exception
			Common.LogException(ex.ToString(), "LoadAccount of frmAndroidWeixin")
		End Try
	End Sub

    Private Sub btnManage_Click(sender As Object, e As EventArgs) Handles btnManage.Click
        Try
#If IS_ITONG Then
            IniSetting.SetIsShowAndroidWeixinMediaNew(False)
            Me.RunApp(GetType(frmAndroidWechatMedia))
#End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnManage_Click")
        End Try
    End Sub

    Public Sub RunApp(ByVal type As Type, Optional ByVal para As Object = Nothing)
#If IS_ITONG Then

        If mAndroid Is Nothing Then
            Return
        End If

        Try
            Dim frmType As Type = System.Type.GetType(type.ToString, True, True)

            ''' Android设备的无需判断iOS设备
            'If Not frmMore.CheckSupport(frmType.ToString, Me.mApplication, Me.mDevice) Then
            '	Return
            'End If

            Dim strName As String = Me.mAndroid.DeviceID & type.FullName
            Dim frm As Form = MainForm.ItemShowEx(frmType, strName, Me.mAndroid, Me.mApplication, para)
            '名字要带设备ID不然拔掉的时候不会关闭。
            frm.Name = strName
        Catch ex As Exception
            Debug.Write(ex)
        End Try

#End If
    End Sub


    Private Sub btnOpenWechatAssisant_Click(sender As Object, e As EventArgs) Handles btnOpenWechatAssisant.Click
        Try
            Dim url As String = ServerIniSetting.GetUrlWechatBackupToolAndroidRoot()
            Common.OpenExplorer(url)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnOpenWechatAssisant_Click")
        End Try
    End Sub
End Class

#Region "下载QQ微信相关的"

''' 下载QQ微信备份需要的vc++ 2005环境
'Private Sub DownloadVCredistX86()
'    Dim urlStr As String = String.Empty
'    Try
'        Dim strTemp As String = Common.ToHexString(Common.EncryptDES(Encoding.UTF8.GetBytes("VC2005RedistX86"), Common.RgbKey, Common.RgbKey))
'        Dim x86Url As String = String.Format("http://driver.tongbu.com/Index.aspx?hid={0}", strTemp)

'        Dim encUrl As String = WebHelper.GetString(x86Url, 15)
'        If Not String.IsNullOrEmpty(encUrl) Then
'            Dim itemInfor As New MultiThreadDownloadItemInfo()
'            With itemInfor
'                .IsSingleThread = False
'                .SaveFolder = Folder.TempFolder
'                .Type = ResourceType.pEXE
'                .Class = ResourceClass.Other
'                .Source = TaskSource.Tongbu
'                .Url = encUrl
'                .ItemId = "VC2005RedistX86"
'                .Name = "vcredist2005"
'            End With

'            Dim downItem As MultiThreadDownloadItemInfo = Me.mDownManager.AppleSoftExist(itemInfor)
'            If downItem.Status <> TaskState.Completed Then
'                Me.mDownManager.NewTask(itemInfor)
'            Else
'                Me.UpdateButtonState(True, False)
'            End If
'        End If
'    Catch ex As Exception
'        Me.UpdateButtonState(True, False)
'    End Try

'End Sub

'''' 下载QQ微信备份
'Private Sub DownloadWechatBackup()
'    ''' WechatBackup2005为QQ微信备份程序放服务器上的标识        
'    Dim urlStr As String = String.Empty

'    Try
'        Dim strTemp As String = Common.ToHexString(Common.EncryptDES(Encoding.UTF8.GetBytes("WechatBackup2005"), Common.RgbKey, Common.RgbKey))
'        Dim wechatUrl As String = String.Format("http://driver.tongbu.com/Index.aspx?hid={0}", strTemp)


'        Dim encUrl As String = WebHelper.GetString(wechatUrl, 15)
'        If Not String.IsNullOrEmpty(encUrl) Then
'            Dim itemInfor As New MultiThreadDownloadItemInfo()
'            With itemInfor
'                .IsSingleThread = False
'                .SaveFolder = Folder.TempFolder
'                .Type = ResourceType.pEXE
'                .Class = ResourceClass.Other
'                .Source = TaskSource.Tongbu
'                .Url = encUrl
'                .ItemId = "QqWechatBackup2005"
'                .Name = "WechatBackup2005"
'            End With

'            Dim downItem As MultiThreadDownloadItemInfo = Me.mDownManager.AppleSoftExist(itemInfor)
'            If downItem.Status <> TaskState.Completed Then
'                Me.mDownManager.NewTask(itemInfor)
'            Else
'                Me.UpdateButtonState(True, False)
'            End If
'        End If
'    Catch ex As Exception
'        Me.UpdateButtonState(True, False)
'    End Try

'End Sub


#End Region

#Region "使用exe文件备份微信"

'    ''' 启动微信备份
'    Private Sub StartAdbWeixin(ByVal state As Object)
'        Try
'            If Me.mAndroid IsNot Nothing Then
'                Dim strWeixin As String = Path.Combine(Folder.AppFolder, "zAndroidWeixin\iAdbWeixin.exe")
'                Dim strDeviceId As String = Me.mAndroid.DeviceID

'                Dim strArgs As String = String.Format("{0}|{1}|{2}|{3}|{4}|{5}", _
'                                                      Folder.AppFolder, Folder.BackupFolder, Folder.ExceptionFolder, _
'                                                      strDeviceId, My.Application.Info.Version.ToString(4), Me.Handle.ToInt64())

'                ProcessHelper.Start(strWeixin, strArgs)
'            End If
'        Catch ex As Exception

'        End Try

'    End Sub


'    Private Function CheckWechatBackup() As Boolean
'        Dim retVal As Boolean = True
'        Dim strQq As String = Path.Combine(Folder.AppFolder, "zWechatBackup")

'        If Not Directory.Exists(strQq) Then

'            '' 下载QQ微信备份还原的exe
'            Me.DownloadWechatBackup()

'            retVal = False
'        End If

'        Return retVal
'    End Function

'    Private Function CheckRedistributable2005() As Boolean

'        Dim retVal As Boolean = True

'        Me.lblWeixinConnect.Text = "正在设置微信备份还原需要的环境，请稍候..."
'        Me.UpdateButtonState(False, True)
'        Me.pgbProgress.Visible = False

'        If Not Me.HasRedistributable2005() Then

'            '' 下载安装QQ微信备份环境
'            Me.DownloadVCredistX86()

'            retVal = False
'        End If

'        Return retVal
'    End Function

'    Private Function HasRedistributable2005() As Boolean
'        Dim bHas2005 As Boolean = False

'        Try
'            ''HKEY_LOCAL_MACHINE\SOFTWARE\Wow6432Node\Microsoft\Windows\CurrentVersion\Uninstall\{710f4c1c-cc18-4c49-8cbf-51240c89a1a2}
'            Dim Redistributable2005RegKey As String = "\Microsoft\Windows\CurrentVersion\Uninstall\{710f4c1c-cc18-4c49-8cbf-51240c89a1a2}"
'            Dim uninstallKey As String
'            If Common.IsOS_Of_64Bit Then
'                uninstallKey = String.Format("SOFTWARE\Wow6432Node{0}", Redistributable2005RegKey)
'                Dim regKey64 As RegistryKey64 = RegistryKey64.OpenKey(RegistryHive.LocalMachine, uninstallKey, True, RegistryKey64.RegWow64Options.KEY_WOW64_64KEY)
'                bHas2005 = (regKey64 IsNot Nothing)
'                regKey64.Close()
'            Else
'                uninstallKey = String.Format("SOFTWARE{0}", Redistributable2005RegKey)
'                Dim regKey32 As RegistryKey = Registry.LocalMachine.OpenSubKey(uninstallKey, True)
'                bHas2005 = (regKey32 IsNot Nothing)
'                regKey32.Close()
'            End If

'        Catch ex As Exception

'        End Try

'        Return bHas2005
'    End Function

'    ''' 安装vc++ 2005环境
'    Private Sub InstallVCRedistX86(ByVal state As Object)
'        Try
'            Dim srcZip As String = Path.Combine(Folder.TempFolder, "vcredist2005.zip")

'            If Utility.unzip(srcZip, Folder.TempFolder) > -1 Then
'                Dim strX86 As String = Path.Combine(Folder.TempFolder, "vcredist_x86.exe")
'                If File.Exists(strX86) Then
'                    ProcessHelper.Start(strX86, "/q")
'                End If
'            End If

'        Catch ex As Exception

'        End Try
'    End Sub

'    Private Sub InstallQqWechatBackup(ByVal state As Object)
'        Try
'            Dim srcZip As String = Path.Combine(Folder.TempFolder, "WechatBackup2005.zip")
'            Dim backupPath As String = Path.Combine(Folder.AppFolder, "zWechatBackup")
'            If Not Directory.Exists(backupPath) Then
'                Directory.CreateDirectory(backupPath)
'            End If

'            If Utility.unzip(srcZip, backupPath) > -1 Then
'                m_bIsNotQq = False  ''' 触发调用qq微信备份

'                Me.HasWeixinInstalling()
'            End If
'        Catch ex As Exception

'        End Try
'    End Sub
#End Region