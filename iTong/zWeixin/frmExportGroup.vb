﻿Imports System.Threading

Public Class frmExportGroup

    Private mHelper As IMExportHelper
    Private mCurrentFriendInfo As WeChatFriendInfo
    Private mCurrentFriendsInChatRoom As List(Of WeChatFriendInfo)

    Private dgvFriend As tbDataGridViewEx = Nothing
    Private mDefaultHeadIcon As Image = My.Resources.weixin_icon_default.Clone


    Public mSelectWXID As String = ""
    Public mSelectExportType As ExportType = ExportType.None

#Region "--- 初始化 ---"

    Public Sub New(ByVal info As WeChatFriendInfo, helper As IMExportHelper)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FilletRadius = 5
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong

        Me.mCurrentFriendInfo = info
        Me.mHelper = helper
    End Sub

    Protected Overrides Sub OnShown(e As EventArgs)
        MyBase.OnShown(e)

        If Me.bgwLoad.IsBusy() Then
            Me.bgwLoad.CancelAsync()
        End If

        Me.bgwLoad.RunWorkerAsync()
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitDataGridView()

        Me.SetSearchNoResult(False)

        Me.SetLoading(True)
    End Sub

    Private Sub bgwLoad_DoWork(sender As Object, e As System.ComponentModel.DoWorkEventArgs) Handles bgwLoad.DoWork
        Try
            Me.mCurrentFriendsInChatRoom = Me.mHelper.LoadWechatFriendsInChatRoom(Me.mCurrentFriendInfo)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoad_DoWork")
        End Try
    End Sub

    Private Sub bgwLoad_RunWorkerCompleted(sender As Object, e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwLoad.RunWorkerCompleted
        Try

            Me.SetLoading(True)

            Me.AddGridView(Me.mCurrentFriendsInChatRoom)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoad_RunWorkerCompleted")
        End Try
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.btnExport.Text = Me.Language.GetString("Weixin.Lable.ExportToComputer") '导出到电脑
        Me.tsmiPicEx.Text = Me.Language.GetString("Weixin.Lable.ExportPic")                               '"导出图片"
        Me.tsmiAudioEx.Text = Me.Language.GetString("Weixin.Lable.ExportAudio")                           '"导出语音"
        Me.tsmiVideoEx.Text = Me.Language.GetString("Weixin.Lable.ExportSmallVideo")                      '"导出小视频"
        Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")
        Me.txtSearchChat.SearchTipText = Me.Language.GetString("File.Label.Search")
        Me.Text = Me.Language.GetString("Backup.Button.ExportGroupFriend") '"选择群联系人导出"
        Me.lblLoadData.Text = Me.Language.GetString("Weixin.Lable.LoadDataing") ' "数据加载中，请稍候..."
        Me.lblSearchNoResult.Text = Me.Language.GetString("Weixin.Message.SearchNoResult")

    End Sub

    Private Sub InitDataGridView()
        Me.dgvFriend = New tbDataGridViewEx
        With Me.dgvFriend
            .SuspendLayout()

            .tbMouseHoverResponse = True
            .RowTemplate.Height = 58
            .ColumnHeadersVisible = True
            .tbShowNoData = False
            .MultiSelect = False
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .Columns.Clear()

            '.Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Main.Button.Contact"), 205, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colNickname", Me.Language.GetString("Components.Login.Label.Nickname"), 165, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colID", Me.Language.GetString("Weixin.Lable.WeChatNumber"), 185, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))

            .ResumeLayout()
        End With


        Utility.AddForm2Panel(Me.dgvFriend, Me.pnlContent)
        Me.dgvFriend.BringToFront()
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        If Me.bgwLoad.IsBusy Then
            Me.bgwLoad.CancelAsync()
        End If
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub txtSearchChat_TextChangedByTimer(sender As Object, e As EventArgs) Handles txtSearchChat.TextChangedByTimer
        Try
            Dim strSearchText As String = txtSearchChat.Text
            Dim lst As List(Of WeChatFriendInfo) = Me.mCurrentFriendsInChatRoom.FindAll(AddressOf EndsWithSaurus)
            If lst.Count = 0 Then
                Me.SetSearchNoResult(True)
            Else
                Me.SetSearchNoResult(False)
            End If
            Me.AddGridView(lst)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "txtSearchChat_TextChangedByTimer")
        End Try
    End Sub

    Private Sub btnExport_Click(sender As Object, e As EventArgs) Handles btnExport.Click
        If Me.dgvFriend.SelectedRows.Count = 0 Then
            '联系人为空。
            tbMessageBox.Show(Me, Me.Language.GetString("WeChat.Button.SelectContact"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If

        Me.cmsExportEx.Show(Me.btnExport, Me.btnExport.Width - Me.cmsExportEx.Width, Me.btnExport.Height + 3)
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub tsmiPicEx_Click(sender As Object, e As EventArgs) Handles tsmiPicEx.Click
        Me.ExportMedia(ExportType.ExportOnlyPhoto)
    End Sub

    Private Sub tsmiAudioEx_Click(sender As Object, e As EventArgs) Handles tsmiAudioEx.Click
        Me.ExportMedia(ExportType.ExportOnlyAudio)
    End Sub

    Private Sub tsmiVideoEx_Click(sender As Object, e As EventArgs) Handles tsmiVideoEx.Click
        Me.ExportMedia(ExportType.ExportOnlyVideo)
    End Sub


#End Region

    Private Delegate Sub SetSearchNoResultHandler(ByVal isVisible As Boolean)
    Private Sub SetSearchNoResult(ByVal isVisible As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetSearchNoResultHandler(AddressOf SetSearchNoResult), isVisible)
            Else
                Me.lblSearchNoResult.Visible = isVisible
                If isVisible Then
                    Me.lblSearchNoResult.BringToFront()
                End If
            End If
        Catch
        End Try
    End Sub

    Private Delegate Sub SetLoadingHandler(ByVal isVisible As Boolean)
    Private Sub SetLoading(ByVal isVisible As Boolean)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetLoadingHandler(AddressOf SetLoading), isVisible)
            Else
                Me.pnlLoading.Visible = isVisible
                If isVisible Then
                    Me.pnlLoading.BringToFront()
                End If

                Me.txtSearchChat.Enabled = Not isVisible
                Me.btnCancel.Enabled = Not isVisible
                Me.btnExport.Enabled = Not isVisible
            End If
        Catch
        End Try
    End Sub

    Private Delegate Sub AddGridViewHandler(ByVal lstTemp As List(Of WeChatFriendInfo))
    Private Sub AddGridView(ByVal lstTemp As List(Of WeChatFriendInfo))
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New AddGridViewHandler(AddressOf AddGridView), New Object() {lstTemp})
            Else
                Me.dgvFriend.SuspendLayout()
                Me.dgvFriend.Rows.Clear()
                For Each item As WeChatFriendInfo In lstTemp
                    Try
                        Dim row As New tbDataGridViewRow()
                        row.Height = 58

                        row.CreateCells(Me.dgvFriend, _
                                        item.GroupNoteName, _
                                        IIf(item.NoteName.Length > 0, item.NoteName, item.UsrName), _
                                        item.WeChatNum) 'False, _
                        Me.dgvFriend.Rows.Add(row)

                        Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                        cellName.tbIconShadow = True

                        cellName.tbIconSize = New Size(44, 44)

                        Dim img As Image = Me.GetHeadIconFromPC(item)
                        cellName.tbIcon = img

                        row.Tag = item
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "AddDictToDataGridView")
                    End Try
                Next

                Me.dgvFriend.ResumeLayout()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
        Me.SetLoading(False)
    End Sub

    Private Function GetHeadIconFromPC(ByVal item As WeChatFriendInfo) As Image
        Dim img As Image = Nothing
        Dim imgPath As String = String.Format("{0}\Photo\{1}.jpg", Me.mHelper.ParaCache.CacheFolder, item.Md5)

        If File.Exists(imgPath) Then
            img = Utility.GetImageFormFile(imgPath, True)
        End If

        '如果图片无法使用就删除掉，下次进来还会重新加载
        If img Is Nothing Then
            Try
                If Common.IsTestMode() AndAlso File.Exists(imgPath) Then
                    File.Delete(imgPath)
                End If
            Catch ex As Exception
            End Try

            img = Me.mDefaultHeadIcon.Clone()
        End If
        Return img
    End Function

    Private Function EndsWithSaurus(ByVal wcInfo As WeChatFriendInfo) As Boolean
        If wcInfo.UsrName.ToLower().Contains(txtSearchChat.Text) OrElse wcInfo.WeChatNum.ToLower().Contains(txtSearchChat.Text) OrElse wcInfo.NoteName.ToLower().Contains(txtSearchChat.Text) OrElse wcInfo.GroupNoteName.ToLower().Contains(txtSearchChat.Text) Then
            Return True
        Else
            Return False
        End If
    End Function

    Private Sub ExportMedia(ByVal eType As ExportType)
        Try
            Dim row As DataGridViewRow = Me.dgvFriend.SelectedRows(0)
            Dim info As WeChatFriendInfo = CType(row.Tag, WeChatFriendInfo)
            Me.mSelectWXID = info.WeChatNum
            Me.mSelectExportType = eType
            DialogResult = Windows.Forms.DialogResult.OK
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExportMedia")
        End Try
    End Sub


End Class