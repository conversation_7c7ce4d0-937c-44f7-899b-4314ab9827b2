﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeixin
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    '<System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeixin))
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.btnTimeFilter = New iTong.Components.tbButton()
        Me.picTimeFilter = New System.Windows.Forms.PictureBox()
        Me.btnPreviousMenu = New iTong.Components.tbButton()
        Me.btnExportSetting = New iTong.Components.tbButton()
        Me.txtSearchChat = New iTong.Components.tbSearch()
        Me.btnFinalData = New iTong.Components.tbButton()
        Me.btnWeCahtUsers = New iTong.Components.tbButton()
        Me.lblRegister = New iTong.tbBattery()
        Me.pnlSearchFriend = New iTong.Components.tbPanel()
        Me.btnFilter = New iTong.Components.tbButton()
        Me.cbxSelectAll = New iTong.Components.tbCheckBox()
        Me.pnlTimeFilter = New iTong.Components.tbPanel()
        Me.TbPanel8 = New iTong.Components.tbPanel()
        Me.btnTime = New iTong.Components.tbButton()
        Me.btnOK = New iTong.Components.tbButton()
        Me.dtpStart = New System.Windows.Forms.DateTimePicker()
        Me.lblStartTime = New System.Windows.Forms.Label()
        Me.dtpEnd = New System.Windows.Forms.DateTimePicker()
        Me.lblEndTime = New System.Windows.Forms.Label()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.PictureBoxAD = New System.Windows.Forms.PictureBox()
        Me.pnlSearchHint = New System.Windows.Forms.Panel()
        Me.rtxtSearchHint = New System.Windows.Forms.RichTextBox()
        Me.pnlCount = New System.Windows.Forms.Panel()
        Me.rtxtPrompt = New System.Windows.Forms.RichTextBox()
        Me.lblSystemInfo = New iTong.Components.tbLabel()
        Me.pnlLoadData = New iTong.Components.tbPanel()
        Me.lblLoadData = New iTong.Components.tbLabel()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.wbsChat = New iTong.Components.tbWebBrowserEx()
        Me.pnlFindDataResult = New System.Windows.Forms.Panel()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.rtxtFindDataInfo = New System.Windows.Forms.RichTextBox()
        Me.rtxtFindDelCount = New System.Windows.Forms.RichTextBox()
        Me.pnlFindData = New iTong.Components.tbPanel()
        Me.lblFindData = New iTong.Components.tbLabel()
        Me.lblDoing = New iTong.Components.tbLabel()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.RichTextBox2 = New System.Windows.Forms.RichTextBox()
        Me.pnlFriend = New iTong.Components.tbPanel()
        Me.TbPanel1 = New iTong.Components.tbPanel()
        Me.lblLinkmanMsg = New iTong.Components.tbLabel()
        Me.pnlLoading = New iTong.Components.tbPanel()
        Me.pnlBackupTip = New System.Windows.Forms.Panel()
        Me.lblOpenTutorial = New System.Windows.Forms.LinkLabel()
        Me.lblLoadingDescription = New System.Windows.Forms.Label()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.picNoLockScreen = New System.Windows.Forms.PictureBox()
        Me.pnlDescription = New iTong.Components.tbPanel()
        Me.btnReturnView = New iTong.Components.tbButton()
        Me.pnlTip = New iTong.Components.tbPanel()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.btnOpenTutorial = New iTong.Components.tbButton()
        Me.btnMoreTutorials = New iTong.Components.tbButton()
        Me.btnReCheckEx = New iTong.Components.tbButton()
        Me.picError = New System.Windows.Forms.PictureBox()
        Me.lblWeixinTitle = New System.Windows.Forms.Label()
        Me.btnDownloadWeixin = New iTong.Components.tbButton()
        Me.lblWeixinDescription = New System.Windows.Forms.Label()
        Me.btnReCheck = New iTong.Components.tbButton()
        Me.lblDownloadWeixin = New System.Windows.Forms.Label()
        Me.picWeixinIcon = New System.Windows.Forms.PictureBox()
        Me.bgwLoadUsers = New System.ComponentModel.BackgroundWorker()
        Me.lblState = New iTong.Components.tbLabel()
        Me.pnlDescription1 = New iTong.Components.tbPanel()
        Me.picClosePwd1 = New System.Windows.Forms.PictureBox()
        Me.lblCancelPwdBackup = New iTong.Components.tbLabel()
        Me.lblStep2Description = New System.Windows.Forms.Label()
        Me.btnReCheckEx1 = New iTong.Components.tbButton()
        Me.lblStep1Description = New System.Windows.Forms.Label()
        Me.picClosePwd = New System.Windows.Forms.PictureBox()
        Me.cmsExportEx = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiFormat = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExcelEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTxtEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiHtml = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiMedia = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiPicEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAudioEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiVideoEx = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator2 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiGroup = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator1 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.cmsRecoveryData = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiRecoveryToComputer = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiRecoveryToPhone = New System.Windows.Forms.ToolStripMenuItem()
        Me.cmsFilter = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiShowDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShowNoDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShowAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiShowNoPublic = New System.Windows.Forms.ToolStripMenuItem()
        Me.tlpMain = New System.Windows.Forms.TableLayoutPanel()
        Me.TbPanel2 = New iTong.Components.tbPanel()
        Me.TbPanel4 = New iTong.Components.tbPanel()
        Me.pnlBottom = New iTong.Components.tbPanel()
        Me.llblRefresh = New System.Windows.Forms.LinkLabel()
        Me.lblFromBackupMsg = New iTong.Components.tbLabel()
        Me.btnExportMedia = New iTong.Components.tbButton()
        Me.btnDataRecoveryToPhone = New iTong.Components.tbButton()
        Me.btnExport = New iTong.Components.tbButton()
        Me.pnlContent = New iTong.Components.tbPanel()
        Me.pnlLeft = New iTong.Components.tbPanel()
        Me.TbPanel7 = New iTong.Components.tbPanel()
        Me.cmsSearch = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiSearchAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiSearchName = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiSearchMsg = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlTop.SuspendLayout()
        CType(Me.picTimeFilter, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlSearchFriend.SuspendLayout()
        Me.pnlTimeFilter.SuspendLayout()
        Me.pnlContainer.SuspendLayout()
        CType(Me.PictureBoxAD, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlSearchHint.SuspendLayout()
        Me.pnlCount.SuspendLayout()
        Me.pnlLoadData.SuspendLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlFindDataResult.SuspendLayout()
        Me.pnlFindData.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlFriend.SuspendLayout()
        Me.TbPanel1.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        Me.pnlBackupTip.SuspendLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picNoLockScreen, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDescription.SuspendLayout()
        Me.pnlTip.SuspendLayout()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picWeixinIcon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDescription1.SuspendLayout()
        CType(Me.picClosePwd1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picClosePwd, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.cmsExportEx.SuspendLayout()
        Me.cmsRecoveryData.SuspendLayout()
        Me.cmsFilter.SuspendLayout()
        Me.tlpMain.SuspendLayout()
        Me.pnlBottom.SuspendLayout()
        Me.pnlContent.SuspendLayout()
        Me.pnlLeft.SuspendLayout()
        Me.cmsSearch.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(2488, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(2464, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(2440, 0)
        '
        'pnlTop
        '
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlTop.Controls.Add(Me.btnTimeFilter)
        Me.pnlTop.Controls.Add(Me.picTimeFilter)
        Me.pnlTop.Controls.Add(Me.btnPreviousMenu)
        Me.pnlTop.Controls.Add(Me.btnExportSetting)
        Me.pnlTop.Controls.Add(Me.txtSearchChat)
        Me.pnlTop.Controls.Add(Me.btnFinalData)
        Me.pnlTop.Controls.Add(Me.btnWeCahtUsers)
        Me.pnlTop.Controls.Add(Me.lblRegister)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTop.Location = New System.Drawing.Point(1500, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(1010, 40)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 4
        Me.pnlTop.tbBackgroundImage = Nothing
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnTimeFilter
        '
        Me.btnTimeFilter.BackColor = System.Drawing.Color.Transparent
        Me.btnTimeFilter.BindingForm = Nothing
        Me.btnTimeFilter.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnTimeFilter.Location = New System.Drawing.Point(261, 9)
        Me.btnTimeFilter.Margin = New System.Windows.Forms.Padding(0)
        Me.btnTimeFilter.Name = "btnTimeFilter"
        Me.btnTimeFilter.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnTimeFilter.Selectable = True
        Me.btnTimeFilter.Size = New System.Drawing.Size(93, 23)
        Me.btnTimeFilter.TabIndex = 98
        Me.btnTimeFilter.tbAdriftIconWhenHover = False
        Me.btnTimeFilter.tbAutoSize = False
        Me.btnTimeFilter.tbAutoSizeEx = True
        Me.btnTimeFilter.tbBackgroundImage = Nothing
        Me.btnTimeFilter.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnTimeFilter.tbBadgeNumber = 0
        Me.btnTimeFilter.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTimeFilter.tbEndEllipsis = False
        Me.btnTimeFilter.tbIconHoldPlace = True
        Me.btnTimeFilter.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_timescreen
        Me.btnTimeFilter.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnTimeFilter.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnTimeFilter.tbIconMore = False
        Me.btnTimeFilter.tbIconMouseDown = Nothing
        Me.btnTimeFilter.tbIconMouseHover = Nothing
        Me.btnTimeFilter.tbIconMouseLeave = Nothing
        Me.btnTimeFilter.tbIconPlaceText = 2
        Me.btnTimeFilter.tbIconReadOnly = Nothing
        Me.btnTimeFilter.tbImageMouseDown = Nothing
        Me.btnTimeFilter.tbImageMouseHover = Nothing
        Me.btnTimeFilter.tbImageMouseLeave = Nothing
        Me.btnTimeFilter.tbProgressValue = 50
        Me.btnTimeFilter.tbReadOnly = False
        Me.btnTimeFilter.tbReadOnlyText = False
        Me.btnTimeFilter.tbShadow = False
        Me.btnTimeFilter.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnTimeFilter.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnTimeFilter.tbShowDot = False
        Me.btnTimeFilter.tbShowMoreIconImg = CType(resources.GetObject("btnTimeFilter.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnTimeFilter.tbShowNew = False
        Me.btnTimeFilter.tbShowProgress = False
        Me.btnTimeFilter.tbShowTip = True
        Me.btnTimeFilter.tbShowToolTipOnButton = False
        Me.btnTimeFilter.tbSplit = "0,0,0,0"
        Me.btnTimeFilter.tbText = "按时间筛选"
        Me.btnTimeFilter.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTimeFilter.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnTimeFilter.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnTimeFilter.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnTimeFilter.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnTimeFilter.tbTextMouseDownPlace = 2
        Me.btnTimeFilter.tbToolTip = ""
        Me.btnTimeFilter.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnTimeFilter.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTimeFilter.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTimeFilter.VisibleEx = True
        '
        'picTimeFilter
        '
        Me.picTimeFilter.Image = Global.iTong.My.Resources.Resources.TimeScreenUpDowm
        Me.picTimeFilter.Location = New System.Drawing.Point(354, 8)
        Me.picTimeFilter.Margin = New System.Windows.Forms.Padding(0)
        Me.picTimeFilter.Name = "picTimeFilter"
        Me.picTimeFilter.Size = New System.Drawing.Size(9, 24)
        Me.picTimeFilter.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.picTimeFilter.TabIndex = 97
        Me.picTimeFilter.TabStop = False
        '
        'btnPreviousMenu
        '
        Me.btnPreviousMenu.BackColor = System.Drawing.Color.Transparent
        Me.btnPreviousMenu.BindingForm = Nothing
        Me.btnPreviousMenu.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnPreviousMenu.Location = New System.Drawing.Point(10, 13)
        Me.btnPreviousMenu.Name = "btnPreviousMenu"
        Me.btnPreviousMenu.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnPreviousMenu.Selectable = True
        Me.btnPreviousMenu.Size = New System.Drawing.Size(16, 14)
        Me.btnPreviousMenu.TabIndex = 81
        Me.btnPreviousMenu.tbAdriftIconWhenHover = False
        Me.btnPreviousMenu.tbAutoSize = False
        Me.btnPreviousMenu.tbAutoSizeEx = True
        Me.btnPreviousMenu.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_retreatex
        Me.btnPreviousMenu.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnPreviousMenu.tbBadgeNumber = 0
        Me.btnPreviousMenu.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnPreviousMenu.tbEndEllipsis = False
        Me.btnPreviousMenu.tbIconHoldPlace = True
        Me.btnPreviousMenu.tbIconImage = Nothing
        Me.btnPreviousMenu.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnPreviousMenu.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnPreviousMenu.tbIconMore = False
        Me.btnPreviousMenu.tbIconMouseDown = Nothing
        Me.btnPreviousMenu.tbIconMouseHover = Nothing
        Me.btnPreviousMenu.tbIconMouseLeave = Nothing
        Me.btnPreviousMenu.tbIconPlaceText = 2
        Me.btnPreviousMenu.tbIconReadOnly = Nothing
        Me.btnPreviousMenu.tbImageMouseDown = Nothing
        Me.btnPreviousMenu.tbImageMouseHover = Nothing
        Me.btnPreviousMenu.tbImageMouseLeave = Nothing
        Me.btnPreviousMenu.tbProgressValue = 50
        Me.btnPreviousMenu.tbReadOnly = False
        Me.btnPreviousMenu.tbReadOnlyText = False
        Me.btnPreviousMenu.tbShadow = False
        Me.btnPreviousMenu.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnPreviousMenu.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnPreviousMenu.tbShowDot = False
        Me.btnPreviousMenu.tbShowMoreIconImg = CType(resources.GetObject("btnPreviousMenu.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnPreviousMenu.tbShowNew = False
        Me.btnPreviousMenu.tbShowProgress = False
        Me.btnPreviousMenu.tbShowTip = True
        Me.btnPreviousMenu.tbShowToolTipOnButton = False
        Me.btnPreviousMenu.tbSplit = "12,1,1,1"
        Me.btnPreviousMenu.tbText = ""
        Me.btnPreviousMenu.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnPreviousMenu.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnPreviousMenu.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnPreviousMenu.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnPreviousMenu.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnPreviousMenu.tbTextMouseDownPlace = 2
        Me.btnPreviousMenu.tbToolTip = ""
        Me.btnPreviousMenu.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnPreviousMenu.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnPreviousMenu.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnPreviousMenu.VisibleEx = True
        '
        'btnExportSetting
        '
        Me.btnExportSetting.BackColor = System.Drawing.Color.Transparent
        Me.btnExportSetting.BindingForm = Nothing
        Me.btnExportSetting.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExportSetting.Location = New System.Drawing.Point(367, 9)
        Me.btnExportSetting.Margin = New System.Windows.Forms.Padding(0)
        Me.btnExportSetting.Name = "btnExportSetting"
        Me.btnExportSetting.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExportSetting.Selectable = True
        Me.btnExportSetting.Size = New System.Drawing.Size(81, 23)
        Me.btnExportSetting.TabIndex = 78
        Me.btnExportSetting.tbAdriftIconWhenHover = False
        Me.btnExportSetting.tbAutoSize = False
        Me.btnExportSetting.tbAutoSizeEx = True
        Me.btnExportSetting.tbBackgroundImage = Nothing
        Me.btnExportSetting.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnExportSetting.tbBadgeNumber = 0
        Me.btnExportSetting.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExportSetting.tbEndEllipsis = False
        Me.btnExportSetting.tbIconHoldPlace = True
        Me.btnExportSetting.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_exportsetting
        Me.btnExportSetting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExportSetting.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExportSetting.tbIconMore = False
        Me.btnExportSetting.tbIconMouseDown = Nothing
        Me.btnExportSetting.tbIconMouseHover = Nothing
        Me.btnExportSetting.tbIconMouseLeave = Nothing
        Me.btnExportSetting.tbIconPlaceText = 2
        Me.btnExportSetting.tbIconReadOnly = Nothing
        Me.btnExportSetting.tbImageMouseDown = Nothing
        Me.btnExportSetting.tbImageMouseHover = Nothing
        Me.btnExportSetting.tbImageMouseLeave = Nothing
        Me.btnExportSetting.tbProgressValue = 50
        Me.btnExportSetting.tbReadOnly = False
        Me.btnExportSetting.tbReadOnlyText = False
        Me.btnExportSetting.tbShadow = False
        Me.btnExportSetting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExportSetting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExportSetting.tbShowDot = False
        Me.btnExportSetting.tbShowMoreIconImg = CType(resources.GetObject("btnExportSetting.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExportSetting.tbShowNew = False
        Me.btnExportSetting.tbShowProgress = False
        Me.btnExportSetting.tbShowTip = True
        Me.btnExportSetting.tbShowToolTipOnButton = False
        Me.btnExportSetting.tbSplit = "0,0,0,0"
        Me.btnExportSetting.tbText = "导出设置"
        Me.btnExportSetting.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportSetting.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnExportSetting.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnExportSetting.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnExportSetting.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnExportSetting.tbTextMouseDownPlace = 2
        Me.btnExportSetting.tbToolTip = ""
        Me.btnExportSetting.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExportSetting.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExportSetting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportSetting.VisibleEx = True
        '
        'txtSearchChat
        '
        Me.txtSearchChat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearchChat.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchChat.ForeColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.txtSearchChat.Location = New System.Drawing.Point(843, 9)
        Me.txtSearchChat.MaxLength = 32767
        Me.txtSearchChat.Name = "txtSearchChat"
        Me.txtSearchChat.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchChat.ReadOnly = False
        Me.txtSearchChat.SearchText = ""
        Me.txtSearchChat.SearchTipText = "Search"
        Me.txtSearchChat.ShowClear = True
        Me.txtSearchChat.ShowClearAlways = False
        Me.txtSearchChat.ShowMore = True
        Me.txtSearchChat.ShowSearch = True
        Me.txtSearchChat.Size = New System.Drawing.Size(156, 23)
        Me.txtSearchChat.TabIndex = 9
        Me.txtSearchChat.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchChat.Timer = True
        Me.txtSearchChat.TimerInterval = 0.5R
        '
        'btnFinalData
        '
        Me.btnFinalData.BackColor = System.Drawing.Color.Transparent
        Me.btnFinalData.BindingForm = Nothing
        Me.btnFinalData.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFinalData.Location = New System.Drawing.Point(180, 9)
        Me.btnFinalData.Margin = New System.Windows.Forms.Padding(0)
        Me.btnFinalData.Name = "btnFinalData"
        Me.btnFinalData.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnFinalData.Selectable = True
        Me.btnFinalData.Size = New System.Drawing.Size(81, 23)
        Me.btnFinalData.TabIndex = 69
        Me.btnFinalData.tbAdriftIconWhenHover = False
        Me.btnFinalData.tbAutoSize = False
        Me.btnFinalData.tbAutoSizeEx = True
        Me.btnFinalData.tbBackgroundImage = Nothing
        Me.btnFinalData.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnFinalData.tbBadgeNumber = 0
        Me.btnFinalData.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFinalData.tbEndEllipsis = False
        Me.btnFinalData.tbIconHoldPlace = True
        Me.btnFinalData.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_finddata
        Me.btnFinalData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnFinalData.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnFinalData.tbIconMore = False
        Me.btnFinalData.tbIconMouseDown = Nothing
        Me.btnFinalData.tbIconMouseHover = Nothing
        Me.btnFinalData.tbIconMouseLeave = Nothing
        Me.btnFinalData.tbIconPlaceText = 2
        Me.btnFinalData.tbIconReadOnly = Nothing
        Me.btnFinalData.tbImageMouseDown = Nothing
        Me.btnFinalData.tbImageMouseHover = Nothing
        Me.btnFinalData.tbImageMouseLeave = Nothing
        Me.btnFinalData.tbProgressValue = 50
        Me.btnFinalData.tbReadOnly = False
        Me.btnFinalData.tbReadOnlyText = False
        Me.btnFinalData.tbShadow = False
        Me.btnFinalData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFinalData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFinalData.tbShowDot = False
        Me.btnFinalData.tbShowMoreIconImg = CType(resources.GetObject("btnFinalData.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFinalData.tbShowNew = False
        Me.btnFinalData.tbShowProgress = False
        Me.btnFinalData.tbShowTip = True
        Me.btnFinalData.tbShowToolTipOnButton = False
        Me.btnFinalData.tbSplit = "0,0,0,0"
        Me.btnFinalData.tbText = "找回数据"
        Me.btnFinalData.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFinalData.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnFinalData.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnFinalData.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(23, Byte), Integer), CType(CType(120, Byte), Integer), CType(CType(205, Byte), Integer))
        Me.btnFinalData.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnFinalData.tbTextMouseDownPlace = 2
        Me.btnFinalData.tbToolTip = ""
        Me.btnFinalData.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFinalData.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFinalData.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFinalData.VisibleEx = True
        '
        'btnWeCahtUsers
        '
        Me.btnWeCahtUsers.BackColor = System.Drawing.Color.White
        Me.btnWeCahtUsers.BindingForm = Nothing
        Me.btnWeCahtUsers.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnWeCahtUsers.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.btnWeCahtUsers.Location = New System.Drawing.Point(41, 8)
        Me.btnWeCahtUsers.Margin = New System.Windows.Forms.Padding(0)
        Me.btnWeCahtUsers.Name = "btnWeCahtUsers"
        Me.btnWeCahtUsers.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnWeCahtUsers.Selectable = True
        Me.btnWeCahtUsers.Size = New System.Drawing.Size(130, 24)
        Me.btnWeCahtUsers.TabIndex = 0
        Me.btnWeCahtUsers.tbAdriftIconWhenHover = False
        Me.btnWeCahtUsers.tbAutoSize = False
        Me.btnWeCahtUsers.tbAutoSizeEx = False
        Me.btnWeCahtUsers.tbBackgroundImage = CType(resources.GetObject("btnWeCahtUsers.tbBackgroundImage"), System.Drawing.Image)
        Me.btnWeCahtUsers.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnWeCahtUsers.tbBadgeNumber = 0
        Me.btnWeCahtUsers.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnWeCahtUsers.tbEndEllipsis = False
        Me.btnWeCahtUsers.tbIconHoldPlace = True
        Me.btnWeCahtUsers.tbIconImage = Nothing
        Me.btnWeCahtUsers.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnWeCahtUsers.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnWeCahtUsers.tbIconMore = True
        Me.btnWeCahtUsers.tbIconMouseDown = Nothing
        Me.btnWeCahtUsers.tbIconMouseHover = Nothing
        Me.btnWeCahtUsers.tbIconMouseLeave = Nothing
        Me.btnWeCahtUsers.tbIconPlaceText = 2
        Me.btnWeCahtUsers.tbIconReadOnly = Nothing
        Me.btnWeCahtUsers.tbImageMouseDown = Nothing
        Me.btnWeCahtUsers.tbImageMouseHover = Nothing
        Me.btnWeCahtUsers.tbImageMouseLeave = Nothing
        Me.btnWeCahtUsers.tbProgressValue = 50
        Me.btnWeCahtUsers.tbReadOnly = False
        Me.btnWeCahtUsers.tbReadOnlyText = False
        Me.btnWeCahtUsers.tbShadow = False
        Me.btnWeCahtUsers.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnWeCahtUsers.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnWeCahtUsers.tbShowDot = False
        Me.btnWeCahtUsers.tbShowMoreIconImg = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnWeCahtUsers.tbShowNew = False
        Me.btnWeCahtUsers.tbShowProgress = False
        Me.btnWeCahtUsers.tbShowTip = True
        Me.btnWeCahtUsers.tbShowToolTipOnButton = False
        Me.btnWeCahtUsers.tbSplit = "2,3,5,3"
        Me.btnWeCahtUsers.tbText = ""
        Me.btnWeCahtUsers.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnWeCahtUsers.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnWeCahtUsers.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnWeCahtUsers.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnWeCahtUsers.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnWeCahtUsers.tbTextMouseDownPlace = 2
        Me.btnWeCahtUsers.tbToolTip = ""
        Me.btnWeCahtUsers.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnWeCahtUsers.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWeCahtUsers.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWeCahtUsers.VisibleEx = True
        '
        'lblRegister
        '
        Me.lblRegister.BackColor = System.Drawing.Color.Transparent
        Me.lblRegister.Cursor = System.Windows.Forms.Cursors.Default
        Me.lblRegister.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.lblRegister.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblRegister.Location = New System.Drawing.Point(450, 10)
        Me.lblRegister.Name = "lblRegister"
        Me.lblRegister.Size = New System.Drawing.Size(356, 19)
        Me.lblRegister.TabIndex = 96
        Me.lblRegister.Text = "试用版预览部分内容用""*""代替 立即购买"
        Me.lblRegister.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlSearchFriend
        '
        Me.pnlSearchFriend.BackColor = System.Drawing.SystemColors.Control
        Me.pnlSearchFriend.Controls.Add(Me.btnFilter)
        Me.pnlSearchFriend.Controls.Add(Me.cbxSelectAll)
        Me.pnlSearchFriend.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlSearchFriend.Location = New System.Drawing.Point(0, 0)
        Me.pnlSearchFriend.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlSearchFriend.Name = "pnlSearchFriend"
        Me.pnlSearchFriend.Size = New System.Drawing.Size(281, 40)
        Me.pnlSearchFriend.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlSearchFriend.TabIndex = 32
        Me.pnlSearchFriend.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlSearchFriend.tbShowWatermark = False
        Me.pnlSearchFriend.tbSplit = "3,3,3,3"
        Me.pnlSearchFriend.tbWatermark = Nothing
        Me.pnlSearchFriend.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlSearchFriend.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnFilter
        '
        Me.btnFilter.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnFilter.BackColor = System.Drawing.Color.Transparent
        Me.btnFilter.BindingForm = Nothing
        Me.btnFilter.Font = New System.Drawing.Font("Arial", 9.0!)
        Me.btnFilter.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.btnFilter.Location = New System.Drawing.Point(115, 9)
        Me.btnFilter.Name = "btnFilter"
        Me.btnFilter.Padding = New System.Windows.Forms.Padding(15, 3, 15, 3)
        Me.btnFilter.Selectable = True
        Me.btnFilter.Size = New System.Drawing.Size(156, 23)
        Me.btnFilter.TabIndex = 0
        Me.btnFilter.tbAdriftIconWhenHover = False
        Me.btnFilter.tbAutoSize = False
        Me.btnFilter.tbAutoSizeEx = False
        Me.btnFilter.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnFilter.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnFilter.tbBadgeNumber = 0
        Me.btnFilter.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFilter.tbEndEllipsis = False
        Me.btnFilter.tbIconHoldPlace = True
        Me.btnFilter.tbIconImage = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnFilter.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnFilter.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnFilter.tbIconMore = False
        Me.btnFilter.tbIconMouseDown = Nothing
        Me.btnFilter.tbIconMouseHover = Nothing
        Me.btnFilter.tbIconMouseLeave = Nothing
        Me.btnFilter.tbIconPlaceText = 5
        Me.btnFilter.tbIconReadOnly = Nothing
        Me.btnFilter.tbImageMouseDown = Nothing
        Me.btnFilter.tbImageMouseHover = Nothing
        Me.btnFilter.tbImageMouseLeave = Nothing
        Me.btnFilter.tbProgressValue = 50
        Me.btnFilter.tbReadOnly = False
        Me.btnFilter.tbReadOnlyText = False
        Me.btnFilter.tbShadow = False
        Me.btnFilter.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFilter.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFilter.tbShowDot = False
        Me.btnFilter.tbShowMoreIconImg = Nothing
        Me.btnFilter.tbShowNew = False
        Me.btnFilter.tbShowProgress = False
        Me.btnFilter.tbShowTip = True
        Me.btnFilter.tbShowToolTipOnButton = False
        Me.btnFilter.tbSplit = "13,11,13,11"
        Me.btnFilter.tbText = "显示所有"
        Me.btnFilter.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFilter.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFilter.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFilter.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFilter.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFilter.tbTextMouseDownPlace = 0
        Me.btnFilter.tbToolTip = ""
        Me.btnFilter.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFilter.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnFilter.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFilter.VisibleEx = True
        '
        'cbxSelectAll
        '
        Me.cbxSelectAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.cbxSelectAll.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxSelectAll.AutoSize = True
        Me.cbxSelectAll.BackColor = System.Drawing.Color.Transparent
        Me.cbxSelectAll.Font = New System.Drawing.Font("Arial", 10.0!)
        Me.cbxSelectAll.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.cbxSelectAll.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.cbxSelectAll.Location = New System.Drawing.Point(10, 9)
        Me.cbxSelectAll.Name = "cbxSelectAll"
        Me.cbxSelectAll.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxSelectAll.Size = New System.Drawing.Size(99, 23)
        Me.cbxSelectAll.TabIndex = 87
        Me.cbxSelectAll.tbAdriftIconWhenHover = False
        Me.cbxSelectAll.tbAutoSize = True
        Me.cbxSelectAll.tbAutoSizeEx = False
        Me.cbxSelectAll.tbIconChecked = CType(resources.GetObject("cbxSelectAll.tbIconChecked"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbIconCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbIconCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbIconHoldPlace = True
        Me.cbxSelectAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxSelectAll.tbIconIndeterminate = CType(resources.GetObject("cbxSelectAll.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconIndeterminateMouseDown = Nothing
        Me.cbxSelectAll.tbIconIndeterminateMouseHover = Nothing
        Me.cbxSelectAll.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxSelectAll.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbIconPlaceText = 1
        Me.cbxSelectAll.tbIconUnChecked = CType(resources.GetObject("cbxSelectAll.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxSelectAll.tbIconUnCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbIconUnCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxSelectAll.tbImageBackground = Nothing
        Me.cbxSelectAll.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxSelectAll.tbImageCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbImageCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbImageCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseDown = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseHover = Nothing
        Me.cbxSelectAll.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxSelectAll.tbReadOnly = False
        Me.cbxSelectAll.tbShadow = False
        Me.cbxSelectAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxSelectAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxSelectAll.tbSplit = "3,3,3,3"
        Me.cbxSelectAll.tbToolTip = ""
        Me.cbxSelectAll.Text = "全选"
        Me.cbxSelectAll.UseVisualStyleBackColor = False
        '
        'pnlTimeFilter
        '
        Me.pnlTimeFilter.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlTimeFilter.Controls.Add(Me.TbPanel8)
        Me.pnlTimeFilter.Controls.Add(Me.btnTime)
        Me.pnlTimeFilter.Controls.Add(Me.btnOK)
        Me.pnlTimeFilter.Controls.Add(Me.dtpStart)
        Me.pnlTimeFilter.Controls.Add(Me.lblStartTime)
        Me.pnlTimeFilter.Controls.Add(Me.dtpEnd)
        Me.pnlTimeFilter.Controls.Add(Me.lblEndTime)
        Me.pnlTimeFilter.Cursor = System.Windows.Forms.Cursors.Default
        Me.pnlTimeFilter.Dock = System.Windows.Forms.DockStyle.Top
        Me.pnlTimeFilter.Location = New System.Drawing.Point(282, 0)
        Me.pnlTimeFilter.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTimeFilter.Name = "pnlTimeFilter"
        Me.pnlTimeFilter.Size = New System.Drawing.Size(728, 40)
        Me.pnlTimeFilter.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTimeFilter.TabIndex = 8
        Me.pnlTimeFilter.tbBackgroundImage = Nothing
        Me.pnlTimeFilter.tbShowWatermark = False
        Me.pnlTimeFilter.tbSplit = "3,3,3,3"
        Me.pnlTimeFilter.tbWatermark = Nothing
        Me.pnlTimeFilter.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTimeFilter.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlTimeFilter.Visible = False
        '
        'TbPanel8
        '
        Me.TbPanel8.BackColor = System.Drawing.Color.Gainsboro
        Me.TbPanel8.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.TbPanel8.Location = New System.Drawing.Point(0, 39)
        Me.TbPanel8.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel8.Name = "TbPanel8"
        Me.TbPanel8.Size = New System.Drawing.Size(728, 1)
        Me.TbPanel8.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel8.TabIndex = 69
        Me.TbPanel8.tbBackgroundImage = Nothing
        Me.TbPanel8.tbShowWatermark = False
        Me.TbPanel8.tbSplit = "0,0,0,0"
        Me.TbPanel8.tbWatermark = Nothing
        Me.TbPanel8.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel8.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnTime
        '
        Me.btnTime.BackColor = System.Drawing.Color.White
        Me.btnTime.BindingForm = Nothing
        Me.btnTime.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnTime.Location = New System.Drawing.Point(78, 9)
        Me.btnTime.Margin = New System.Windows.Forms.Padding(0)
        Me.btnTime.Name = "btnTime"
        Me.btnTime.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnTime.Selectable = True
        Me.btnTime.Size = New System.Drawing.Size(99, 23)
        Me.btnTime.TabIndex = 68
        Me.btnTime.tbAdriftIconWhenHover = False
        Me.btnTime.tbAutoSize = False
        Me.btnTime.tbAutoSizeEx = False
        Me.btnTime.tbBackgroundImage = CType(resources.GetObject("btnTime.tbBackgroundImage"), System.Drawing.Image)
        Me.btnTime.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnTime.tbBadgeNumber = 0
        Me.btnTime.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTime.tbEndEllipsis = False
        Me.btnTime.tbIconHoldPlace = True
        Me.btnTime.tbIconImage = Nothing
        Me.btnTime.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnTime.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnTime.tbIconMore = True
        Me.btnTime.tbIconMouseDown = Nothing
        Me.btnTime.tbIconMouseHover = Nothing
        Me.btnTime.tbIconMouseLeave = Nothing
        Me.btnTime.tbIconPlaceText = 2
        Me.btnTime.tbIconReadOnly = Nothing
        Me.btnTime.tbImageMouseDown = Nothing
        Me.btnTime.tbImageMouseHover = Nothing
        Me.btnTime.tbImageMouseLeave = Nothing
        Me.btnTime.tbProgressValue = 50
        Me.btnTime.tbReadOnly = False
        Me.btnTime.tbReadOnlyText = False
        Me.btnTime.tbShadow = False
        Me.btnTime.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnTime.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnTime.tbShowDot = False
        Me.btnTime.tbShowMoreIconImg = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnTime.tbShowNew = False
        Me.btnTime.tbShowProgress = False
        Me.btnTime.tbShowTip = True
        Me.btnTime.tbShowToolTipOnButton = False
        Me.btnTime.tbSplit = "2,3,5,3"
        Me.btnTime.tbText = ""
        Me.btnTime.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnTime.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnTime.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnTime.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnTime.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnTime.tbTextMouseDownPlace = 2
        Me.btnTime.tbToolTip = ""
        Me.btnTime.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnTime.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTime.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTime.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnOK.Location = New System.Drawing.Point(409, 10)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(39, 21)
        Me.btnOK.TabIndex = 18
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = True
        Me.btnOK.tbBackgroundImage = Nothing
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "确定"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOK.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnOK.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnOK.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnOK.tbTextMouseDownPlace = 2
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOK.VisibleEx = True
        '
        'dtpStart
        '
        Me.dtpStart.Checked = False
        Me.dtpStart.CustomFormat = "yyyy-MM-dd"
        Me.dtpStart.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.dtpStart.Location = New System.Drawing.Point(182, 10)
        Me.dtpStart.Name = "dtpStart"
        Me.dtpStart.Size = New System.Drawing.Size(100, 21)
        Me.dtpStart.TabIndex = 11
        '
        'lblStartTime
        '
        Me.lblStartTime.AutoSize = True
        Me.lblStartTime.BackColor = System.Drawing.Color.Transparent
        Me.lblStartTime.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblStartTime.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStartTime.Location = New System.Drawing.Point(38, 13)
        Me.lblStartTime.Name = "lblStartTime"
        Me.lblStartTime.Size = New System.Drawing.Size(35, 12)
        Me.lblStartTime.TabIndex = 15
        Me.lblStartTime.Text = "日期:"
        Me.lblStartTime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'dtpEnd
        '
        Me.dtpEnd.CalendarForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.dtpEnd.CalendarTitleForeColor = System.Drawing.Color.AliceBlue
        Me.dtpEnd.Checked = False
        Me.dtpEnd.CustomFormat = "yyyy-MM-dd"
        Me.dtpEnd.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.dtpEnd.Location = New System.Drawing.Point(301, 10)
        Me.dtpEnd.Name = "dtpEnd"
        Me.dtpEnd.Size = New System.Drawing.Size(100, 21)
        Me.dtpEnd.TabIndex = 16
        '
        'lblEndTime
        '
        Me.lblEndTime.AutoSize = True
        Me.lblEndTime.BackColor = System.Drawing.Color.Transparent
        Me.lblEndTime.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblEndTime.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblEndTime.Location = New System.Drawing.Point(284, 14)
        Me.lblEndTime.Name = "lblEndTime"
        Me.lblEndTime.Size = New System.Drawing.Size(17, 12)
        Me.lblEndTime.TabIndex = 17
        Me.lblEndTime.Text = "～"
        Me.lblEndTime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlContainer
        '
        Me.pnlContainer.BackColor = System.Drawing.SystemColors.Control
        Me.pnlContainer.Controls.Add(Me.PictureBoxAD)
        Me.pnlContainer.Controls.Add(Me.pnlSearchHint)
        Me.pnlContainer.Controls.Add(Me.pnlCount)
        Me.pnlContainer.Controls.Add(Me.lblSystemInfo)
        Me.pnlContainer.Controls.Add(Me.pnlLoadData)
        Me.pnlContainer.Controls.Add(Me.wbsChat)
        Me.pnlContainer.Controls.Add(Me.pnlFindDataResult)
        Me.pnlContainer.Controls.Add(Me.pnlFindData)
        Me.pnlContainer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContainer.Location = New System.Drawing.Point(282, 40)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(728, 456)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 3
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'PictureBoxAD
        '
        Me.PictureBoxAD.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.PictureBoxAD.Location = New System.Drawing.Point(56, 254)
        Me.PictureBoxAD.Name = "PictureBoxAD"
        Me.PictureBoxAD.Size = New System.Drawing.Size(617, 153)
        Me.PictureBoxAD.TabIndex = 10
        Me.PictureBoxAD.TabStop = False
        '
        'pnlSearchHint
        '
        Me.pnlSearchHint.BackColor = System.Drawing.Color.FromArgb(CType(CType(198, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.pnlSearchHint.Controls.Add(Me.rtxtSearchHint)
        Me.pnlSearchHint.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlSearchHint.Location = New System.Drawing.Point(0, 408)
        Me.pnlSearchHint.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlSearchHint.Name = "pnlSearchHint"
        Me.pnlSearchHint.Size = New System.Drawing.Size(728, 24)
        Me.pnlSearchHint.TabIndex = 9
        '
        'rtxtSearchHint
        '
        Me.rtxtSearchHint.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rtxtSearchHint.BackColor = System.Drawing.Color.FromArgb(CType(CType(198, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.rtxtSearchHint.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxtSearchHint.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.rtxtSearchHint.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.rtxtSearchHint.Location = New System.Drawing.Point(4, 5)
        Me.rtxtSearchHint.Multiline = False
        Me.rtxtSearchHint.Name = "rtxtSearchHint"
        Me.rtxtSearchHint.ReadOnly = True
        Me.rtxtSearchHint.ShowSelectionMargin = True
        Me.rtxtSearchHint.Size = New System.Drawing.Size(720, 14)
        Me.rtxtSearchHint.TabIndex = 12
        Me.rtxtSearchHint.Text = "当前对话中不包含含有关键字的记录，已自动为您展示全部消息"
        '
        'pnlCount
        '
        Me.pnlCount.BackColor = System.Drawing.Color.FromArgb(CType(CType(208, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.pnlCount.Controls.Add(Me.rtxtPrompt)
        Me.pnlCount.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlCount.Location = New System.Drawing.Point(0, 432)
        Me.pnlCount.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlCount.Name = "pnlCount"
        Me.pnlCount.Size = New System.Drawing.Size(728, 24)
        Me.pnlCount.TabIndex = 6
        '
        'rtxtPrompt
        '
        Me.rtxtPrompt.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rtxtPrompt.BackColor = System.Drawing.Color.FromArgb(CType(CType(208, Byte), Integer), CType(CType(239, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.rtxtPrompt.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxtPrompt.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.rtxtPrompt.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.rtxtPrompt.Location = New System.Drawing.Point(4, 5)
        Me.rtxtPrompt.Multiline = False
        Me.rtxtPrompt.Name = "rtxtPrompt"
        Me.rtxtPrompt.ReadOnly = True
        Me.rtxtPrompt.ShowSelectionMargin = True
        Me.rtxtPrompt.Size = New System.Drawing.Size(720, 14)
        Me.rtxtPrompt.TabIndex = 12
        Me.rtxtPrompt.Text = "红色标识为找回数据，当前联系人找回18条"
        '
        'lblSystemInfo
        '
        Me.lblSystemInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSystemInfo.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblSystemInfo.Font = New System.Drawing.Font("宋体", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblSystemInfo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblSystemInfo.Location = New System.Drawing.Point(93, 213)
        Me.lblSystemInfo.Name = "lblSystemInfo"
        Me.lblSystemInfo.Size = New System.Drawing.Size(542, 30)
        Me.lblSystemInfo.TabIndex = 3
        Me.lblSystemInfo.tbAdriftWhenHover = False
        Me.lblSystemInfo.tbAutoEllipsis = False
        Me.lblSystemInfo.tbAutoSize = False
        Me.lblSystemInfo.tbHideImage = False
        Me.lblSystemInfo.tbIconImage = Nothing
        Me.lblSystemInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSystemInfo.tbIconPlaceText = 5
        Me.lblSystemInfo.tbShadow = False
        Me.lblSystemInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSystemInfo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSystemInfo.tbShowScrolling = False
        Me.lblSystemInfo.Text = "查看消息，请选中左侧联系人"
        Me.lblSystemInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblSystemInfo.Visible = False
        '
        'pnlLoadData
        '
        Me.pnlLoadData.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoadData.Controls.Add(Me.lblLoadData)
        Me.pnlLoadData.Controls.Add(Me.PictureBox2)
        Me.pnlLoadData.Location = New System.Drawing.Point(202, 84)
        Me.pnlLoadData.Name = "pnlLoadData"
        Me.pnlLoadData.Size = New System.Drawing.Size(564, 288)
        Me.pnlLoadData.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoadData.TabIndex = 8
        Me.pnlLoadData.tbBackgroundImage = Nothing
        Me.pnlLoadData.tbShowWatermark = False
        Me.pnlLoadData.tbSplit = "0,0,0,0"
        Me.pnlLoadData.tbWatermark = Nothing
        Me.pnlLoadData.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoadData.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlLoadData.Visible = False
        '
        'lblLoadData
        '
        Me.lblLoadData.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblLoadData.BackColor = System.Drawing.Color.Transparent
        Me.lblLoadData.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblLoadData.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblLoadData.Location = New System.Drawing.Point(5, 160)
        Me.lblLoadData.Name = "lblLoadData"
        Me.lblLoadData.Size = New System.Drawing.Size(554, 21)
        Me.lblLoadData.TabIndex = 33
        Me.lblLoadData.tbAdriftWhenHover = False
        Me.lblLoadData.tbAutoEllipsis = False
        Me.lblLoadData.tbAutoSize = False
        Me.lblLoadData.tbHideImage = False
        Me.lblLoadData.tbIconImage = Nothing
        Me.lblLoadData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLoadData.tbIconPlaceText = 5
        Me.lblLoadData.tbShadow = False
        Me.lblLoadData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLoadData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLoadData.tbShowScrolling = False
        Me.lblLoadData.Text = "数据加载中，请稍候..."
        Me.lblLoadData.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox2
        '
        Me.PictureBox2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.PictureBox2.Image = Global.iTong.My.Resources.Resources.gif_loading_32
        Me.PictureBox2.Location = New System.Drawing.Point(266, 108)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(32, 32)
        Me.PictureBox2.TabIndex = 31
        Me.PictureBox2.TabStop = False
        '
        'wbsChat
        '
        Me.wbsChat.AllowWebBrowserDrop = False
        Me.wbsChat.Client = ""
        Me.wbsChat.DeviceList = Nothing
        Me.wbsChat.Dock = System.Windows.Forms.DockStyle.Fill
        Me.wbsChat.IsConnected = False
        Me.wbsChat.IsWebBrowserContextMenuEnabled = False
        Me.wbsChat.Jailbreaked = False
        Me.wbsChat.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.wbsChat.LoadingFont = Nothing
        Me.wbsChat.LoadingGif = CType(resources.GetObject("wbsChat.LoadingGif"), System.Drawing.Image)
        Me.wbsChat.Location = New System.Drawing.Point(0, 0)
        Me.wbsChat.MinimumSize = New System.Drawing.Size(20, 20)
        Me.wbsChat.Name = "wbsChat"
        Me.wbsChat.ScriptErrorsSuppressed = True
        Me.wbsChat.ShowLoadingWait = False
        Me.wbsChat.ShowNavigateErrorPage = True
        Me.wbsChat.ShowProgress = True
        Me.wbsChat.Size = New System.Drawing.Size(728, 456)
        Me.wbsChat.SN = ""
        Me.wbsChat.TabIndex = 0
        Me.wbsChat.UserInfo = ""
        Me.wbsChat.UserInfoEncode = ""
        '
        'pnlFindDataResult
        '
        Me.pnlFindDataResult.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFindDataResult.Controls.Add(Me.RichTextBox1)
        Me.pnlFindDataResult.Controls.Add(Me.rtxtFindDataInfo)
        Me.pnlFindDataResult.Controls.Add(Me.rtxtFindDelCount)
        Me.pnlFindDataResult.Location = New System.Drawing.Point(136, 46)
        Me.pnlFindDataResult.Name = "pnlFindDataResult"
        Me.pnlFindDataResult.Size = New System.Drawing.Size(564, 288)
        Me.pnlFindDataResult.TabIndex = 5
        Me.pnlFindDataResult.Visible = False
        '
        'RichTextBox1
        '
        Me.RichTextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.RichTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.RichTextBox1.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.RichTextBox1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(209, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(74, Byte), Integer))
        Me.RichTextBox1.Location = New System.Drawing.Point(113, 148)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.ReadOnly = True
        Me.RichTextBox1.Size = New System.Drawing.Size(448, 106)
        Me.RichTextBox1.TabIndex = 30
        Me.RichTextBox1.Text = ""
        '
        'rtxtFindDataInfo
        '
        Me.rtxtFindDataInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.rtxtFindDataInfo.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.rtxtFindDataInfo.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxtFindDataInfo.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.rtxtFindDataInfo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.rtxtFindDataInfo.Location = New System.Drawing.Point(3, 115)
        Me.rtxtFindDataInfo.Multiline = False
        Me.rtxtFindDataInfo.Name = "rtxtFindDataInfo"
        Me.rtxtFindDataInfo.ReadOnly = True
        Me.rtxtFindDataInfo.ShowSelectionMargin = True
        Me.rtxtFindDataInfo.Size = New System.Drawing.Size(558, 21)
        Me.rtxtFindDataInfo.TabIndex = 12
        Me.rtxtFindDataInfo.Text = "左侧联系人昵称显示红色为有找到删除数据"
        '
        'rtxtFindDelCount
        '
        Me.rtxtFindDelCount.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.rtxtFindDelCount.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.rtxtFindDelCount.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxtFindDelCount.Font = New System.Drawing.Font("宋体", 16.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.rtxtFindDelCount.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.rtxtFindDelCount.Location = New System.Drawing.Point(2, 74)
        Me.rtxtFindDelCount.Multiline = False
        Me.rtxtFindDelCount.Name = "rtxtFindDelCount"
        Me.rtxtFindDelCount.ReadOnly = True
        Me.rtxtFindDelCount.ShowSelectionMargin = True
        Me.rtxtFindDelCount.Size = New System.Drawing.Size(559, 30)
        Me.rtxtFindDelCount.TabIndex = 11
        Me.rtxtFindDelCount.Text = "共找回8条删除的数据"
        '
        'pnlFindData
        '
        Me.pnlFindData.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFindData.Controls.Add(Me.lblFindData)
        Me.pnlFindData.Controls.Add(Me.lblDoing)
        Me.pnlFindData.Controls.Add(Me.PictureBox1)
        Me.pnlFindData.Controls.Add(Me.RichTextBox2)
        Me.pnlFindData.Location = New System.Drawing.Point(202, 84)
        Me.pnlFindData.Name = "pnlFindData"
        Me.pnlFindData.Size = New System.Drawing.Size(564, 288)
        Me.pnlFindData.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFindData.TabIndex = 7
        Me.pnlFindData.tbBackgroundImage = Nothing
        Me.pnlFindData.tbShowWatermark = False
        Me.pnlFindData.tbSplit = "0,0,0,0"
        Me.pnlFindData.tbWatermark = Nothing
        Me.pnlFindData.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFindData.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlFindData.Visible = False
        '
        'lblFindData
        '
        Me.lblFindData.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblFindData.BackColor = System.Drawing.Color.Transparent
        Me.lblFindData.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblFindData.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFindData.Location = New System.Drawing.Point(4, 57)
        Me.lblFindData.Name = "lblFindData"
        Me.lblFindData.Size = New System.Drawing.Size(556, 21)
        Me.lblFindData.TabIndex = 33
        Me.lblFindData.tbAdriftWhenHover = False
        Me.lblFindData.tbAutoEllipsis = False
        Me.lblFindData.tbAutoSize = False
        Me.lblFindData.tbHideImage = False
        Me.lblFindData.tbIconImage = Nothing
        Me.lblFindData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFindData.tbIconPlaceText = 5
        Me.lblFindData.tbShadow = False
        Me.lblFindData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFindData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFindData.tbShowScrolling = False
        Me.lblFindData.Text = "正在为您找回删除的聊天记录，请稍候..."
        Me.lblFindData.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblDoing
        '
        Me.lblDoing.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblDoing.BackColor = System.Drawing.Color.Transparent
        Me.lblDoing.Font = New System.Drawing.Font("宋体", 18.0!)
        Me.lblDoing.ForeColor = System.Drawing.Color.FromArgb(CType(CType(36, Byte), Integer), CType(CType(142, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblDoing.Location = New System.Drawing.Point(247, 149)
        Me.lblDoing.Name = "lblDoing"
        Me.lblDoing.Size = New System.Drawing.Size(70, 32)
        Me.lblDoing.TabIndex = 30
        Me.lblDoing.tbAdriftWhenHover = False
        Me.lblDoing.tbAutoEllipsis = False
        Me.lblDoing.tbAutoSize = False
        Me.lblDoing.tbHideImage = False
        Me.lblDoing.tbIconImage = Nothing
        Me.lblDoing.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDoing.tbIconPlaceText = 5
        Me.lblDoing.tbShadow = False
        Me.lblDoing.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDoing.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDoing.tbShowScrolling = False
        Me.lblDoing.Text = "数据找回中，请耐心等候 ..."
        Me.lblDoing.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.gif_loading_132
        Me.PictureBox1.Location = New System.Drawing.Point(216, 99)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(132, 132)
        Me.PictureBox1.TabIndex = 31
        Me.PictureBox1.TabStop = False
        '
        'RichTextBox2
        '
        Me.RichTextBox2.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.RichTextBox2.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox2.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.RichTextBox2.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.RichTextBox2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.RichTextBox2.Location = New System.Drawing.Point(43, 71)
        Me.RichTextBox2.Name = "RichTextBox2"
        Me.RichTextBox2.ReadOnly = True
        Me.RichTextBox2.Size = New System.Drawing.Size(478, 117)
        Me.RichTextBox2.TabIndex = 32
        Me.RichTextBox2.Text = ""
        Me.RichTextBox2.Visible = False
        '
        'pnlFriend
        '
        Me.pnlFriend.BackColor = System.Drawing.SystemColors.Control
        Me.pnlFriend.Controls.Add(Me.TbPanel1)
        Me.pnlFriend.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlFriend.Location = New System.Drawing.Point(0, 40)
        Me.pnlFriend.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFriend.Name = "pnlFriend"
        Me.pnlFriend.Size = New System.Drawing.Size(281, 456)
        Me.pnlFriend.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFriend.TabIndex = 5
        Me.pnlFriend.tbBackgroundImage = Nothing
        Me.pnlFriend.tbShowWatermark = False
        Me.pnlFriend.tbSplit = "0,0,0,0"
        Me.pnlFriend.tbWatermark = Nothing
        Me.pnlFriend.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFriend.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel1
        '
        Me.TbPanel1.Controls.Add(Me.lblLinkmanMsg)
        Me.TbPanel1.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.TbPanel1.Location = New System.Drawing.Point(0, 424)
        Me.TbPanel1.Name = "TbPanel1"
        Me.TbPanel1.Size = New System.Drawing.Size(281, 32)
        Me.TbPanel1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel1.TabIndex = 17
        Me.TbPanel1.tbBackgroundImage = Nothing
        Me.TbPanel1.tbShowWatermark = False
        Me.TbPanel1.tbSplit = "0,0,0,0"
        Me.TbPanel1.tbWatermark = Nothing
        Me.TbPanel1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLinkmanMsg
        '
        Me.lblLinkmanMsg.Location = New System.Drawing.Point(4, 7)
        Me.lblLinkmanMsg.Name = "lblLinkmanMsg"
        Me.lblLinkmanMsg.Size = New System.Drawing.Size(268, 19)
        Me.lblLinkmanMsg.TabIndex = 35
        Me.lblLinkmanMsg.tbAdriftWhenHover = False
        Me.lblLinkmanMsg.tbAutoEllipsis = False
        Me.lblLinkmanMsg.tbAutoSize = False
        Me.lblLinkmanMsg.tbHideImage = False
        Me.lblLinkmanMsg.tbIconImage = Nothing
        Me.lblLinkmanMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLinkmanMsg.tbIconPlaceText = 5
        Me.lblLinkmanMsg.tbShadow = False
        Me.lblLinkmanMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblLinkmanMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblLinkmanMsg.tbShowScrolling = False
        Me.lblLinkmanMsg.Text = "已选择 123 项，全部 123 项"
        Me.lblLinkmanMsg.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlLoading
        '
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.pnlBackupTip)
        Me.pnlLoading.Controls.Add(Me.lblLoadingDescription)
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.picLoading)
        Me.pnlLoading.Controls.Add(Me.picNoLockScreen)
        Me.pnlLoading.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlLoading.Location = New System.Drawing.Point(1000, 0)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.tlpMain.SetRowSpan(Me.pnlLoading, 5)
        Me.pnlLoading.Size = New System.Drawing.Size(500, 618)
        Me.pnlLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoading.TabIndex = 7
        Me.pnlLoading.tbBackgroundImage = Nothing
        Me.pnlLoading.tbShowWatermark = False
        Me.pnlLoading.tbSplit = "0,0,0,0"
        Me.pnlLoading.tbWatermark = Nothing
        Me.pnlLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlBackupTip
        '
        Me.pnlBackupTip.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(131, Byte), Integer))
        Me.pnlBackupTip.Controls.Add(Me.lblOpenTutorial)
        Me.pnlBackupTip.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlBackupTip.Location = New System.Drawing.Point(0, 594)
        Me.pnlBackupTip.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBackupTip.Name = "pnlBackupTip"
        Me.pnlBackupTip.Size = New System.Drawing.Size(500, 24)
        Me.pnlBackupTip.TabIndex = 34
        '
        'lblOpenTutorial
        '
        Me.lblOpenTutorial.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblOpenTutorial.AutoSize = True
        Me.lblOpenTutorial.BackColor = System.Drawing.Color.Transparent
        Me.lblOpenTutorial.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblOpenTutorial.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblOpenTutorial.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblOpenTutorial.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblOpenTutorial.LinkColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblOpenTutorial.Location = New System.Drawing.Point(3, 4)
        Me.lblOpenTutorial.Name = "lblOpenTutorial"
        Me.lblOpenTutorial.Size = New System.Drawing.Size(221, 12)
        Me.lblOpenTutorial.TabIndex = 37
        Me.lblOpenTutorial.TabStop = True
        Me.lblOpenTutorial.Text = "备份失败？备份卡在某一处不动？戳我>>"
        Me.lblOpenTutorial.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.lblOpenTutorial.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        '
        'lblLoadingDescription
        '
        Me.lblLoadingDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoadingDescription.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoadingDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer))
        Me.lblLoadingDescription.Location = New System.Drawing.Point(150, 340)
        Me.lblLoadingDescription.Name = "lblLoadingDescription"
        Me.lblLoadingDescription.Size = New System.Drawing.Size(409, 26)
        Me.lblLoadingDescription.TabIndex = 32
        Me.lblLoadingDescription.Text = "8.3及以上固件需要较长时间，请务必耐心等待"
        Me.lblLoadingDescription.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLoadingDescription.Visible = False
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.AutoSize = True
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(149, 301)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(0, 16)
        Me.lblLoading.TabIndex = 12
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picLoading
        '
        Me.picLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.picLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.picLoading.Location = New System.Drawing.Point(112, 297)
        Me.picLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(24, 24)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picLoading.TabIndex = 11
        Me.picLoading.TabStop = False
        '
        'picNoLockScreen
        '
        Me.picNoLockScreen.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picNoLockScreen.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.picNoLockScreen.Image = Global.iTong.My.Resources.Resources.Weixin_notRootOrLimit_New
        Me.picNoLockScreen.Location = New System.Drawing.Point(22, 252)
        Me.picNoLockScreen.Margin = New System.Windows.Forms.Padding(0)
        Me.picNoLockScreen.Name = "picNoLockScreen"
        Me.picNoLockScreen.Size = New System.Drawing.Size(114, 114)
        Me.picNoLockScreen.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picNoLockScreen.TabIndex = 33
        Me.picNoLockScreen.TabStop = False
        Me.picNoLockScreen.Visible = False
        '
        'pnlDescription
        '
        Me.pnlDescription.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlDescription.Controls.Add(Me.btnReturnView)
        Me.pnlDescription.Controls.Add(Me.pnlTip)
        Me.pnlDescription.Controls.Add(Me.btnOpenTutorial)
        Me.pnlDescription.Controls.Add(Me.btnMoreTutorials)
        Me.pnlDescription.Controls.Add(Me.btnReCheckEx)
        Me.pnlDescription.Controls.Add(Me.picError)
        Me.pnlDescription.Controls.Add(Me.lblWeixinTitle)
        Me.pnlDescription.Controls.Add(Me.btnDownloadWeixin)
        Me.pnlDescription.Controls.Add(Me.lblWeixinDescription)
        Me.pnlDescription.Controls.Add(Me.btnReCheck)
        Me.pnlDescription.Controls.Add(Me.lblDownloadWeixin)
        Me.pnlDescription.Controls.Add(Me.picWeixinIcon)
        Me.pnlDescription.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlDescription.Location = New System.Drawing.Point(500, 0)
        Me.pnlDescription.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlDescription.Name = "pnlDescription"
        Me.tlpMain.SetRowSpan(Me.pnlDescription, 5)
        Me.pnlDescription.Size = New System.Drawing.Size(500, 618)
        Me.pnlDescription.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDescription.TabIndex = 33
        Me.pnlDescription.tbBackgroundImage = Nothing
        Me.pnlDescription.tbShowWatermark = False
        Me.pnlDescription.tbSplit = "0,0,0,0"
        Me.pnlDescription.tbWatermark = Nothing
        Me.pnlDescription.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDescription.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnReturnView
        '
        Me.btnReturnView.BackColor = System.Drawing.Color.Transparent
        Me.btnReturnView.BindingForm = Nothing
        Me.btnReturnView.Font = New System.Drawing.Font("Arial", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReturnView.Location = New System.Drawing.Point(9, 9)
        Me.btnReturnView.Name = "btnReturnView"
        Me.btnReturnView.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnReturnView.Selectable = True
        Me.btnReturnView.Size = New System.Drawing.Size(16, 14)
        Me.btnReturnView.TabIndex = 82
        Me.btnReturnView.tbAdriftIconWhenHover = False
        Me.btnReturnView.tbAutoSize = False
        Me.btnReturnView.tbAutoSizeEx = True
        Me.btnReturnView.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_retreatex
        Me.btnReturnView.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnReturnView.tbBadgeNumber = 0
        Me.btnReturnView.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReturnView.tbEndEllipsis = False
        Me.btnReturnView.tbIconHoldPlace = True
        Me.btnReturnView.tbIconImage = Nothing
        Me.btnReturnView.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnReturnView.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnReturnView.tbIconMore = False
        Me.btnReturnView.tbIconMouseDown = Nothing
        Me.btnReturnView.tbIconMouseHover = Nothing
        Me.btnReturnView.tbIconMouseLeave = Nothing
        Me.btnReturnView.tbIconPlaceText = 2
        Me.btnReturnView.tbIconReadOnly = Nothing
        Me.btnReturnView.tbImageMouseDown = Nothing
        Me.btnReturnView.tbImageMouseHover = Nothing
        Me.btnReturnView.tbImageMouseLeave = Nothing
        Me.btnReturnView.tbProgressValue = 50
        Me.btnReturnView.tbReadOnly = False
        Me.btnReturnView.tbReadOnlyText = False
        Me.btnReturnView.tbShadow = False
        Me.btnReturnView.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnReturnView.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnReturnView.tbShowDot = False
        Me.btnReturnView.tbShowMoreIconImg = CType(resources.GetObject("btnReturnView.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReturnView.tbShowNew = False
        Me.btnReturnView.tbShowProgress = False
        Me.btnReturnView.tbShowTip = True
        Me.btnReturnView.tbShowToolTipOnButton = False
        Me.btnReturnView.tbSplit = "12,1,1,1"
        Me.btnReturnView.tbText = ""
        Me.btnReturnView.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReturnView.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnReturnView.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnReturnView.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnReturnView.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnReturnView.tbTextMouseDownPlace = 2
        Me.btnReturnView.tbToolTip = ""
        Me.btnReturnView.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReturnView.tbToolTipFont = New System.Drawing.Font("Arial", 9.0!)
        Me.btnReturnView.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReturnView.VisibleEx = True
        '
        'pnlTip
        '
        Me.pnlTip.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlTip.Controls.Add(Me.Label1)
        Me.pnlTip.Location = New System.Drawing.Point(-60, 388)
        Me.pnlTip.Name = "pnlTip"
        Me.pnlTip.Size = New System.Drawing.Size(621, 27)
        Me.pnlTip.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTip.TabIndex = 43
        Me.pnlTip.tbBackgroundImage = Nothing
        Me.pnlTip.tbShowWatermark = False
        Me.pnlTip.tbSplit = "0,0,0,0"
        Me.pnlTip.tbWatermark = Nothing
        Me.pnlTip.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTip.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlTip.Visible = False
        '
        'Label1
        '
        Me.Label1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.Label1.AutoSize = True
        Me.Label1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.Red
        Me.Label1.Location = New System.Drawing.Point(11, 7)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(599, 12)
        Me.Label1.TabIndex = 29
        Me.Label1.Text = "如果你安装了非同步助手渠道购买的分身版，想管理点击此处加入QQ群（587033524），联系管理员或者群主咨询" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnOpenTutorial
        '
        Me.btnOpenTutorial.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnOpenTutorial.BackColor = System.Drawing.Color.Transparent
        Me.btnOpenTutorial.BindingForm = Nothing
        Me.btnOpenTutorial.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOpenTutorial.Location = New System.Drawing.Point(138, 275)
        Me.btnOpenTutorial.Name = "btnOpenTutorial"
        Me.btnOpenTutorial.Padding = New System.Windows.Forms.Padding(10, 1, 10, 0)
        Me.btnOpenTutorial.Selectable = True
        Me.btnOpenTutorial.Size = New System.Drawing.Size(73, 28)
        Me.btnOpenTutorial.TabIndex = 42
        Me.btnOpenTutorial.tbAdriftIconWhenHover = False
        Me.btnOpenTutorial.tbAutoSize = True
        Me.btnOpenTutorial.tbAutoSizeEx = True
        Me.btnOpenTutorial.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOpenTutorial.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOpenTutorial.tbBadgeNumber = 0
        Me.btnOpenTutorial.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpenTutorial.tbEndEllipsis = False
        Me.btnOpenTutorial.tbIconHoldPlace = True
        Me.btnOpenTutorial.tbIconImage = Nothing
        Me.btnOpenTutorial.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenTutorial.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOpenTutorial.tbIconMore = False
        Me.btnOpenTutorial.tbIconMouseDown = Nothing
        Me.btnOpenTutorial.tbIconMouseHover = Nothing
        Me.btnOpenTutorial.tbIconMouseLeave = Nothing
        Me.btnOpenTutorial.tbIconPlaceText = 2
        Me.btnOpenTutorial.tbIconReadOnly = Nothing
        Me.btnOpenTutorial.tbImageMouseDown = Nothing
        Me.btnOpenTutorial.tbImageMouseHover = Nothing
        Me.btnOpenTutorial.tbImageMouseLeave = Nothing
        Me.btnOpenTutorial.tbProgressValue = 50
        Me.btnOpenTutorial.tbReadOnly = False
        Me.btnOpenTutorial.tbReadOnlyText = False
        Me.btnOpenTutorial.tbShadow = False
        Me.btnOpenTutorial.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOpenTutorial.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOpenTutorial.tbShowDot = False
        Me.btnOpenTutorial.tbShowMoreIconImg = CType(resources.GetObject("btnOpenTutorial.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOpenTutorial.tbShowNew = False
        Me.btnOpenTutorial.tbShowProgress = False
        Me.btnOpenTutorial.tbShowTip = True
        Me.btnOpenTutorial.tbShowToolTipOnButton = False
        Me.btnOpenTutorial.tbSplit = "13,11,13,11"
        Me.btnOpenTutorial.tbText = "打开教程"
        Me.btnOpenTutorial.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenTutorial.tbTextColor = System.Drawing.Color.White
        Me.btnOpenTutorial.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOpenTutorial.tbTextColorDown = System.Drawing.Color.White
        Me.btnOpenTutorial.tbTextColorHover = System.Drawing.Color.White
        Me.btnOpenTutorial.tbTextMouseDownPlace = 0
        Me.btnOpenTutorial.tbToolTip = ""
        Me.btnOpenTutorial.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpenTutorial.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpenTutorial.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenTutorial.Visible = False
        Me.btnOpenTutorial.VisibleEx = True
        '
        'btnMoreTutorials
        '
        Me.btnMoreTutorials.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnMoreTutorials.BackColor = System.Drawing.Color.Transparent
        Me.btnMoreTutorials.BindingForm = Nothing
        Me.btnMoreTutorials.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnMoreTutorials.Location = New System.Drawing.Point(221, 338)
        Me.btnMoreTutorials.Name = "btnMoreTutorials"
        Me.btnMoreTutorials.Padding = New System.Windows.Forms.Padding(10, 1, 10, 0)
        Me.btnMoreTutorials.Selectable = True
        Me.btnMoreTutorials.Size = New System.Drawing.Size(73, 27)
        Me.btnMoreTutorials.TabIndex = 41
        Me.btnMoreTutorials.tbAdriftIconWhenHover = False
        Me.btnMoreTutorials.tbAutoSize = True
        Me.btnMoreTutorials.tbAutoSizeEx = True
        Me.btnMoreTutorials.tbBackgroundImage = Nothing
        Me.btnMoreTutorials.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnMoreTutorials.tbBadgeNumber = 0
        Me.btnMoreTutorials.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnMoreTutorials.tbEndEllipsis = False
        Me.btnMoreTutorials.tbIconHoldPlace = True
        Me.btnMoreTutorials.tbIconImage = Nothing
        Me.btnMoreTutorials.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMoreTutorials.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnMoreTutorials.tbIconMore = False
        Me.btnMoreTutorials.tbIconMouseDown = Nothing
        Me.btnMoreTutorials.tbIconMouseHover = Nothing
        Me.btnMoreTutorials.tbIconMouseLeave = Nothing
        Me.btnMoreTutorials.tbIconPlaceText = 2
        Me.btnMoreTutorials.tbIconReadOnly = Nothing
        Me.btnMoreTutorials.tbImageMouseDown = Nothing
        Me.btnMoreTutorials.tbImageMouseHover = Nothing
        Me.btnMoreTutorials.tbImageMouseLeave = Nothing
        Me.btnMoreTutorials.tbProgressValue = 50
        Me.btnMoreTutorials.tbReadOnly = False
        Me.btnMoreTutorials.tbReadOnlyText = False
        Me.btnMoreTutorials.tbShadow = False
        Me.btnMoreTutorials.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnMoreTutorials.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnMoreTutorials.tbShowDot = False
        Me.btnMoreTutorials.tbShowMoreIconImg = CType(resources.GetObject("btnMoreTutorials.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnMoreTutorials.tbShowNew = False
        Me.btnMoreTutorials.tbShowProgress = False
        Me.btnMoreTutorials.tbShowTip = True
        Me.btnMoreTutorials.tbShowToolTipOnButton = False
        Me.btnMoreTutorials.tbSplit = "13,11,13,11"
        Me.btnMoreTutorials.tbText = "更多教程"
        Me.btnMoreTutorials.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMoreTutorials.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnMoreTutorials.tbTextColorDisable = System.Drawing.Color.White
        Me.btnMoreTutorials.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnMoreTutorials.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(200, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnMoreTutorials.tbTextMouseDownPlace = 0
        Me.btnMoreTutorials.tbToolTip = ""
        Me.btnMoreTutorials.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnMoreTutorials.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnMoreTutorials.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMoreTutorials.VisibleEx = True
        '
        'btnReCheckEx
        '
        Me.btnReCheckEx.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnReCheckEx.BackColor = System.Drawing.Color.Transparent
        Me.btnReCheckEx.BindingForm = Nothing
        Me.btnReCheckEx.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReCheckEx.Location = New System.Drawing.Point(140, 338)
        Me.btnReCheckEx.Name = "btnReCheckEx"
        Me.btnReCheckEx.Padding = New System.Windows.Forms.Padding(10, 1, 10, 0)
        Me.btnReCheckEx.Selectable = True
        Me.btnReCheckEx.Size = New System.Drawing.Size(73, 28)
        Me.btnReCheckEx.TabIndex = 40
        Me.btnReCheckEx.tbAdriftIconWhenHover = False
        Me.btnReCheckEx.tbAutoSize = True
        Me.btnReCheckEx.tbAutoSizeEx = True
        Me.btnReCheckEx.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnReCheckEx.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnReCheckEx.tbBadgeNumber = 0
        Me.btnReCheckEx.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReCheckEx.tbEndEllipsis = False
        Me.btnReCheckEx.tbIconHoldPlace = True
        Me.btnReCheckEx.tbIconImage = Nothing
        Me.btnReCheckEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReCheckEx.tbIconMore = False
        Me.btnReCheckEx.tbIconMouseDown = Nothing
        Me.btnReCheckEx.tbIconMouseHover = Nothing
        Me.btnReCheckEx.tbIconMouseLeave = Nothing
        Me.btnReCheckEx.tbIconPlaceText = 2
        Me.btnReCheckEx.tbIconReadOnly = Nothing
        Me.btnReCheckEx.tbImageMouseDown = Nothing
        Me.btnReCheckEx.tbImageMouseHover = Nothing
        Me.btnReCheckEx.tbImageMouseLeave = Nothing
        Me.btnReCheckEx.tbProgressValue = 50
        Me.btnReCheckEx.tbReadOnly = False
        Me.btnReCheckEx.tbReadOnlyText = False
        Me.btnReCheckEx.tbShadow = False
        Me.btnReCheckEx.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnReCheckEx.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnReCheckEx.tbShowDot = False
        Me.btnReCheckEx.tbShowMoreIconImg = CType(resources.GetObject("btnReCheckEx.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReCheckEx.tbShowNew = False
        Me.btnReCheckEx.tbShowProgress = False
        Me.btnReCheckEx.tbShowTip = True
        Me.btnReCheckEx.tbShowToolTipOnButton = False
        Me.btnReCheckEx.tbSplit = "13,11,13,11"
        Me.btnReCheckEx.tbText = "重新检查"
        Me.btnReCheckEx.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx.tbTextColor = System.Drawing.Color.White
        Me.btnReCheckEx.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReCheckEx.tbTextColorDown = System.Drawing.Color.White
        Me.btnReCheckEx.tbTextColorHover = System.Drawing.Color.White
        Me.btnReCheckEx.tbTextMouseDownPlace = 0
        Me.btnReCheckEx.tbToolTip = ""
        Me.btnReCheckEx.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReCheckEx.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReCheckEx.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx.Visible = False
        Me.btnReCheckEx.VisibleEx = True
        '
        'picError
        '
        Me.picError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picError.Image = Global.iTong.My.Resources.Resources.weixin_error
        Me.picError.Location = New System.Drawing.Point(38, 230)
        Me.picError.Name = "picError"
        Me.picError.Size = New System.Drawing.Size(70, 69)
        Me.picError.TabIndex = 32
        Me.picError.TabStop = False
        Me.picError.Visible = False
        '
        'lblWeixinTitle
        '
        Me.lblWeixinTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinTitle.AutoSize = True
        Me.lblWeixinTitle.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblWeixinTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblWeixinTitle.Location = New System.Drawing.Point(134, 209)
        Me.lblWeixinTitle.Name = "lblWeixinTitle"
        Me.lblWeixinTitle.Size = New System.Drawing.Size(119, 16)
        Me.lblWeixinTitle.TabIndex = 27
        Me.lblWeixinTitle.Text = "尚未安装 微信"
        '
        'btnDownloadWeixin
        '
        Me.btnDownloadWeixin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnDownloadWeixin.BackColor = System.Drawing.Color.Transparent
        Me.btnDownloadWeixin.BindingForm = Nothing
        Me.btnDownloadWeixin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDownloadWeixin.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDownloadWeixin.Location = New System.Drawing.Point(342, 347)
        Me.btnDownloadWeixin.Name = "btnDownloadWeixin"
        Me.btnDownloadWeixin.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDownloadWeixin.Selectable = True
        Me.btnDownloadWeixin.Size = New System.Drawing.Size(12, 12)
        Me.btnDownloadWeixin.TabIndex = 31
        Me.btnDownloadWeixin.tbAdriftIconWhenHover = False
        Me.btnDownloadWeixin.tbAutoSize = False
        Me.btnDownloadWeixin.tbAutoSizeEx = True
        Me.btnDownloadWeixin.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_3_goto
        Me.btnDownloadWeixin.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnDownloadWeixin.tbBadgeNumber = 0
        Me.btnDownloadWeixin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDownloadWeixin.tbEndEllipsis = False
        Me.btnDownloadWeixin.tbIconHoldPlace = True
        Me.btnDownloadWeixin.tbIconImage = Nothing
        Me.btnDownloadWeixin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDownloadWeixin.tbIconMore = False
        Me.btnDownloadWeixin.tbIconMouseDown = Nothing
        Me.btnDownloadWeixin.tbIconMouseHover = Nothing
        Me.btnDownloadWeixin.tbIconMouseLeave = Nothing
        Me.btnDownloadWeixin.tbIconPlaceText = 2
        Me.btnDownloadWeixin.tbIconReadOnly = Nothing
        Me.btnDownloadWeixin.tbImageMouseDown = Nothing
        Me.btnDownloadWeixin.tbImageMouseHover = Nothing
        Me.btnDownloadWeixin.tbImageMouseLeave = Nothing
        Me.btnDownloadWeixin.tbProgressValue = 50
        Me.btnDownloadWeixin.tbReadOnly = False
        Me.btnDownloadWeixin.tbReadOnlyText = False
        Me.btnDownloadWeixin.tbShadow = False
        Me.btnDownloadWeixin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDownloadWeixin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDownloadWeixin.tbShowDot = False
        Me.btnDownloadWeixin.tbShowMoreIconImg = CType(resources.GetObject("btnDownloadWeixin.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDownloadWeixin.tbShowNew = False
        Me.btnDownloadWeixin.tbShowProgress = False
        Me.btnDownloadWeixin.tbShowTip = True
        Me.btnDownloadWeixin.tbShowToolTipOnButton = False
        Me.btnDownloadWeixin.tbSplit = "0,0,0,0"
        Me.btnDownloadWeixin.tbText = ""
        Me.btnDownloadWeixin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.tbTextColor = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorDisable = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorDown = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorHover = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextMouseDownPlace = 0
        Me.btnDownloadWeixin.tbToolTip = ""
        Me.btnDownloadWeixin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDownloadWeixin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDownloadWeixin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.VisibleEx = True
        '
        'lblWeixinDescription
        '
        Me.lblWeixinDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinDescription.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWeixinDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer))
        Me.lblWeixinDescription.Location = New System.Drawing.Point(134, 244)
        Me.lblWeixinDescription.Name = "lblWeixinDescription"
        Me.lblWeixinDescription.Size = New System.Drawing.Size(353, 83)
        Me.lblWeixinDescription.TabIndex = 28
        Me.lblWeixinDescription.Text = "微信是一款手机通信软件，支持通过手机网络发送语音短信、" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "视频、图片和文字，可以单聊及群聊，还能根据地理位置找到" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "附近的人，带给朋友们全新的移动沟通体验" & _
    "。"
        '
        'btnReCheck
        '
        Me.btnReCheck.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnReCheck.BackColor = System.Drawing.Color.Transparent
        Me.btnReCheck.BindingForm = Nothing
        Me.btnReCheck.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReCheck.Location = New System.Drawing.Point(131, 342)
        Me.btnReCheck.Name = "btnReCheck"
        Me.btnReCheck.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnReCheck.Selectable = True
        Me.btnReCheck.Size = New System.Drawing.Size(63, 22)
        Me.btnReCheck.TabIndex = 30
        Me.btnReCheck.tbAdriftIconWhenHover = False
        Me.btnReCheck.tbAutoSize = True
        Me.btnReCheck.tbAutoSizeEx = True
        Me.btnReCheck.tbBackgroundImage = Nothing
        Me.btnReCheck.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnReCheck.tbBadgeNumber = 0
        Me.btnReCheck.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReCheck.tbEndEllipsis = False
        Me.btnReCheck.tbIconHoldPlace = True
        Me.btnReCheck.tbIconImage = Nothing
        Me.btnReCheck.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheck.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReCheck.tbIconMore = False
        Me.btnReCheck.tbIconMouseDown = Nothing
        Me.btnReCheck.tbIconMouseHover = Nothing
        Me.btnReCheck.tbIconMouseLeave = Nothing
        Me.btnReCheck.tbIconPlaceText = 2
        Me.btnReCheck.tbIconReadOnly = Nothing
        Me.btnReCheck.tbImageMouseDown = Nothing
        Me.btnReCheck.tbImageMouseHover = Nothing
        Me.btnReCheck.tbImageMouseLeave = Nothing
        Me.btnReCheck.tbProgressValue = 50
        Me.btnReCheck.tbReadOnly = False
        Me.btnReCheck.tbReadOnlyText = False
        Me.btnReCheck.tbShadow = False
        Me.btnReCheck.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnReCheck.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnReCheck.tbShowDot = False
        Me.btnReCheck.tbShowMoreIconImg = CType(resources.GetObject("btnReCheck.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReCheck.tbShowNew = False
        Me.btnReCheck.tbShowProgress = False
        Me.btnReCheck.tbShowTip = True
        Me.btnReCheck.tbShowToolTipOnButton = False
        Me.btnReCheck.tbSplit = "13,11,13,11"
        Me.btnReCheck.tbText = "重新检查"
        Me.btnReCheck.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheck.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnReCheck.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReCheck.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.btnReCheck.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(200, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btnReCheck.tbTextMouseDownPlace = 0
        Me.btnReCheck.tbToolTip = ""
        Me.btnReCheck.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReCheck.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReCheck.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheck.VisibleEx = True
        '
        'lblDownloadWeixin
        '
        Me.lblDownloadWeixin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDownloadWeixin.AutoSize = True
        Me.lblDownloadWeixin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblDownloadWeixin.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblDownloadWeixin.Location = New System.Drawing.Point(234, 344)
        Me.lblDownloadWeixin.Name = "lblDownloadWeixin"
        Me.lblDownloadWeixin.Size = New System.Drawing.Size(101, 12)
        Me.lblDownloadWeixin.TabIndex = 29
        Me.lblDownloadWeixin.Text = "立即免费获取微信"
        '
        'picWeixinIcon
        '
        Me.picWeixinIcon.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picWeixinIcon.Image = Global.iTong.My.Resources.Resources.file_weixin_large
        Me.picWeixinIcon.Location = New System.Drawing.Point(-6, 228)
        Me.picWeixinIcon.Name = "picWeixinIcon"
        Me.picWeixinIcon.Size = New System.Drawing.Size(114, 114)
        Me.picWeixinIcon.TabIndex = 26
        Me.picWeixinIcon.TabStop = False
        '
        'bgwLoadUsers
        '
        Me.bgwLoadUsers.WorkerSupportsCancellation = True
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(3, 60)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(1003, 18)
        Me.lblState.TabIndex = 29
        Me.lblState.tbAdriftWhenHover = False
        Me.lblState.tbAutoEllipsis = False
        Me.lblState.tbAutoSize = False
        Me.lblState.tbHideImage = False
        Me.lblState.tbIconImage = Nothing
        Me.lblState.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblState.tbIconPlaceText = 5
        Me.lblState.tbShadow = False
        Me.lblState.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblState.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblState.tbShowScrolling = False
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblState.Visible = False
        '
        'pnlDescription1
        '
        Me.pnlDescription1.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlDescription1.Controls.Add(Me.picClosePwd1)
        Me.pnlDescription1.Controls.Add(Me.lblCancelPwdBackup)
        Me.pnlDescription1.Controls.Add(Me.lblStep2Description)
        Me.pnlDescription1.Controls.Add(Me.btnReCheckEx1)
        Me.pnlDescription1.Controls.Add(Me.lblStep1Description)
        Me.pnlDescription1.Controls.Add(Me.picClosePwd)
        Me.pnlDescription1.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlDescription1.Location = New System.Drawing.Point(0, 0)
        Me.pnlDescription1.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlDescription1.Name = "pnlDescription1"
        Me.tlpMain.SetRowSpan(Me.pnlDescription1, 5)
        Me.pnlDescription1.Size = New System.Drawing.Size(500, 618)
        Me.pnlDescription1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDescription1.TabIndex = 34
        Me.pnlDescription1.tbBackgroundImage = Nothing
        Me.pnlDescription1.tbShowWatermark = False
        Me.pnlDescription1.tbSplit = "0,0,0,0"
        Me.pnlDescription1.tbWatermark = Nothing
        Me.pnlDescription1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDescription1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'picClosePwd1
        '
        Me.picClosePwd1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picClosePwd1.Image = Global.iTong.My.Resources.Resources.weixin_close_pwd2
        Me.picClosePwd1.Location = New System.Drawing.Point(-56, 163)
        Me.picClosePwd1.Name = "picClosePwd1"
        Me.picClosePwd1.Size = New System.Drawing.Size(300, 231)
        Me.picClosePwd1.TabIndex = 43
        Me.picClosePwd1.TabStop = False
        '
        'lblCancelPwdBackup
        '
        Me.lblCancelPwdBackup.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblCancelPwdBackup.Location = New System.Drawing.Point(-403, 92)
        Me.lblCancelPwdBackup.Name = "lblCancelPwdBackup"
        Me.lblCancelPwdBackup.Size = New System.Drawing.Size(710, 54)
        Me.lblCancelPwdBackup.TabIndex = 63
        Me.lblCancelPwdBackup.tbAdriftWhenHover = False
        Me.lblCancelPwdBackup.tbAutoEllipsis = False
        Me.lblCancelPwdBackup.tbAutoSize = False
        Me.lblCancelPwdBackup.tbHideImage = False
        Me.lblCancelPwdBackup.tbIconImage = Global.iTong.My.Resources.Resources.icon_attention1
        Me.lblCancelPwdBackup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCancelPwdBackup.tbIconPlaceText = 5
        Me.lblCancelPwdBackup.tbShadow = False
        Me.lblCancelPwdBackup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCancelPwdBackup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCancelPwdBackup.tbShowScrolling = False
        Me.lblCancelPwdBackup.Text = "TbLabel1"
        Me.lblCancelPwdBackup.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblStep2Description
        '
        Me.lblStep2Description.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblStep2Description.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStep2Description.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblStep2Description.Location = New System.Drawing.Point(-67, 405)
        Me.lblStep2Description.Name = "lblStep2Description"
        Me.lblStep2Description.Size = New System.Drawing.Size(322, 24)
        Me.lblStep2Description.TabIndex = 45
        Me.lblStep2Description.Text = "取消该设备的备份加密"
        Me.lblStep2Description.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnReCheckEx1
        '
        Me.btnReCheckEx1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnReCheckEx1.BackColor = System.Drawing.Color.Transparent
        Me.btnReCheckEx1.BindingForm = Nothing
        Me.btnReCheckEx1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReCheckEx1.Location = New System.Drawing.Point(-187, 466)
        Me.btnReCheckEx1.Name = "btnReCheckEx1"
        Me.btnReCheckEx1.Padding = New System.Windows.Forms.Padding(10, 1, 10, 0)
        Me.btnReCheckEx1.Selectable = True
        Me.btnReCheckEx1.Size = New System.Drawing.Size(215, 47)
        Me.btnReCheckEx1.TabIndex = 40
        Me.btnReCheckEx1.tbAdriftIconWhenHover = False
        Me.btnReCheckEx1.tbAutoSize = False
        Me.btnReCheckEx1.tbAutoSizeEx = False
        Me.btnReCheckEx1.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnReCheckEx1.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnReCheckEx1.tbBadgeNumber = 0
        Me.btnReCheckEx1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReCheckEx1.tbEndEllipsis = False
        Me.btnReCheckEx1.tbIconHoldPlace = True
        Me.btnReCheckEx1.tbIconImage = Nothing
        Me.btnReCheckEx1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReCheckEx1.tbIconMore = False
        Me.btnReCheckEx1.tbIconMouseDown = Nothing
        Me.btnReCheckEx1.tbIconMouseHover = Nothing
        Me.btnReCheckEx1.tbIconMouseLeave = Nothing
        Me.btnReCheckEx1.tbIconPlaceText = 2
        Me.btnReCheckEx1.tbIconReadOnly = Nothing
        Me.btnReCheckEx1.tbImageMouseDown = Nothing
        Me.btnReCheckEx1.tbImageMouseHover = Nothing
        Me.btnReCheckEx1.tbImageMouseLeave = Nothing
        Me.btnReCheckEx1.tbProgressValue = 50
        Me.btnReCheckEx1.tbReadOnly = False
        Me.btnReCheckEx1.tbReadOnlyText = False
        Me.btnReCheckEx1.tbShadow = False
        Me.btnReCheckEx1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnReCheckEx1.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnReCheckEx1.tbShowDot = False
        Me.btnReCheckEx1.tbShowMoreIconImg = CType(resources.GetObject("btnReCheckEx1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReCheckEx1.tbShowNew = False
        Me.btnReCheckEx1.tbShowProgress = False
        Me.btnReCheckEx1.tbShowTip = True
        Me.btnReCheckEx1.tbShowToolTipOnButton = False
        Me.btnReCheckEx1.tbSplit = "13,11,13,11"
        Me.btnReCheckEx1.tbText = "重新检查"
        Me.btnReCheckEx1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx1.tbTextColor = System.Drawing.Color.White
        Me.btnReCheckEx1.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReCheckEx1.tbTextColorDown = System.Drawing.Color.White
        Me.btnReCheckEx1.tbTextColorHover = System.Drawing.Color.White
        Me.btnReCheckEx1.tbTextMouseDownPlace = 0
        Me.btnReCheckEx1.tbToolTip = ""
        Me.btnReCheckEx1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReCheckEx1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReCheckEx1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckEx1.VisibleEx = True
        '
        'lblStep1Description
        '
        Me.lblStep1Description.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblStep1Description.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStep1Description.ForeColor = System.Drawing.Color.FromArgb(CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer), CType(CType(61, Byte), Integer))
        Me.lblStep1Description.Location = New System.Drawing.Point(-414, 405)
        Me.lblStep1Description.Name = "lblStep1Description"
        Me.lblStep1Description.Size = New System.Drawing.Size(322, 24)
        Me.lblStep1Description.TabIndex = 28
        Me.lblStep1Description.Text = "打开iTunes,选择设备"
        Me.lblStep1Description.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picClosePwd
        '
        Me.picClosePwd.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picClosePwd.Image = Global.iTong.My.Resources.Resources.weixin_close_pwd1
        Me.picClosePwd.Location = New System.Drawing.Point(-403, 163)
        Me.picClosePwd.Name = "picClosePwd"
        Me.picClosePwd.Size = New System.Drawing.Size(300, 231)
        Me.picClosePwd.TabIndex = 33
        Me.picClosePwd.TabStop = False
        '
        'cmsExportEx
        '
        Me.cmsExportEx.AccessibleDescription = "173x126"
        Me.cmsExportEx.DropShadowEnabled = False
        Me.cmsExportEx.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsExportEx.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiFormat, Me.tsmiMedia, Me.ToolStripSeparator2, Me.tsmiGroup, Me.ToolStripSeparator1, Me.tsmiAll})
        Me.cmsExportEx.Name = "munOperate"
        Me.cmsExportEx.Size = New System.Drawing.Size(173, 104)
        Me.cmsExportEx.tbBackColor = System.Drawing.Color.White
        Me.cmsExportEx.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsExportEx.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsExportEx.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiFormat
        '
        Me.tsmiFormat.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExcelEx, Me.tsmiTxtEx, Me.tsmiHtml})
        Me.tsmiFormat.Name = "tsmiFormat"
        Me.tsmiFormat.Size = New System.Drawing.Size(172, 22)
        Me.tsmiFormat.Text = "按格式导出"
        '
        'tsmiExcelEx
        '
        Me.tsmiExcelEx.Name = "tsmiExcelEx"
        Me.tsmiExcelEx.Size = New System.Drawing.Size(165, 22)
        Me.tsmiExcelEx.Text = "导出为Excel文件"
        '
        'tsmiTxtEx
        '
        Me.tsmiTxtEx.Name = "tsmiTxtEx"
        Me.tsmiTxtEx.Size = New System.Drawing.Size(165, 22)
        Me.tsmiTxtEx.Text = "导出为Txt文件"
        '
        'tsmiHtml
        '
        Me.tsmiHtml.Name = "tsmiHtml"
        Me.tsmiHtml.Size = New System.Drawing.Size(165, 22)
        Me.tsmiHtml.Text = "导出为Html文件"
        '
        'tsmiMedia
        '
        Me.tsmiMedia.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiPicEx, Me.tsmiAudioEx, Me.tsmiVideoEx})
        Me.tsmiMedia.Name = "tsmiMedia"
        Me.tsmiMedia.Size = New System.Drawing.Size(172, 22)
        Me.tsmiMedia.Text = "按媒体导出"
        '
        'tsmiPicEx
        '
        Me.tsmiPicEx.Name = "tsmiPicEx"
        Me.tsmiPicEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiPicEx.Text = "导出图片"
        '
        'tsmiAudioEx
        '
        Me.tsmiAudioEx.Name = "tsmiAudioEx"
        Me.tsmiAudioEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiAudioEx.Text = "导出语音"
        '
        'tsmiVideoEx
        '
        Me.tsmiVideoEx.Name = "tsmiVideoEx"
        Me.tsmiVideoEx.Size = New System.Drawing.Size(136, 22)
        Me.tsmiVideoEx.Text = "导出小视频"
        '
        'ToolStripSeparator2
        '
        Me.ToolStripSeparator2.Name = "ToolStripSeparator2"
        Me.ToolStripSeparator2.Size = New System.Drawing.Size(169, 6)
        '
        'tsmiGroup
        '
        Me.tsmiGroup.Name = "tsmiGroup"
        Me.tsmiGroup.Size = New System.Drawing.Size(172, 22)
        Me.tsmiGroup.Text = "选择群联系人导出"
        '
        'ToolStripSeparator1
        '
        Me.ToolStripSeparator1.Name = "ToolStripSeparator1"
        Me.ToolStripSeparator1.Size = New System.Drawing.Size(169, 6)
        '
        'tsmiAll
        '
        Me.tsmiAll.Name = "tsmiAll"
        Me.tsmiAll.Size = New System.Drawing.Size(172, 22)
        Me.tsmiAll.Text = "导出全部"
        '
        'cmsRecoveryData
        '
        Me.cmsRecoveryData.AccessibleDescription = "185x70"
        Me.cmsRecoveryData.DropShadowEnabled = False
        Me.cmsRecoveryData.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsRecoveryData.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiRecoveryToComputer, Me.tsmiRecoveryToPhone})
        Me.cmsRecoveryData.Name = "munOperate"
        Me.cmsRecoveryData.Size = New System.Drawing.Size(185, 48)
        Me.cmsRecoveryData.tbBackColor = System.Drawing.Color.White
        Me.cmsRecoveryData.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsRecoveryData.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsRecoveryData.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiRecoveryToComputer
        '
        Me.tsmiRecoveryToComputer.Name = "tsmiRecoveryToComputer"
        Me.tsmiRecoveryToComputer.Size = New System.Drawing.Size(184, 22)
        Me.tsmiRecoveryToComputer.Text = "人工数据恢复到电脑"
        '
        'tsmiRecoveryToPhone
        '
        Me.tsmiRecoveryToPhone.Name = "tsmiRecoveryToPhone"
        Me.tsmiRecoveryToPhone.Size = New System.Drawing.Size(184, 22)
        Me.tsmiRecoveryToPhone.Text = "人工数据恢复到手机"
        '
        'cmsFilter
        '
        Me.cmsFilter.AccessibleDescription = "161x92"
        Me.cmsFilter.DropShadowEnabled = False
        Me.cmsFilter.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsFilter.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiShowDelete, Me.tsmiShowNoDelete, Me.tsmiShowAll, Me.tsmiShowNoPublic})
        Me.cmsFilter.Name = "munOperate"
        Me.cmsFilter.Size = New System.Drawing.Size(161, 92)
        Me.cmsFilter.tbBackColor = System.Drawing.Color.White
        Me.cmsFilter.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsFilter.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsFilter.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiShowDelete
        '
        Me.tsmiShowDelete.Name = "tsmiShowDelete"
        Me.tsmiShowDelete.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowDelete.Text = "只显示删除找回"
        '
        'tsmiShowNoDelete
        '
        Me.tsmiShowNoDelete.Name = "tsmiShowNoDelete"
        Me.tsmiShowNoDelete.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowNoDelete.Text = "只显示未删除"
        '
        'tsmiShowAll
        '
        Me.tsmiShowAll.Name = "tsmiShowAll"
        Me.tsmiShowAll.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowAll.Text = "显示全部"
        '
        'tsmiShowNoPublic
        '
        Me.tsmiShowNoPublic.Name = "tsmiShowNoPublic"
        Me.tsmiShowNoPublic.Size = New System.Drawing.Size(160, 22)
        Me.tsmiShowNoPublic.Text = "不显示公众号"
        Me.tsmiShowNoPublic.Visible = False
        '
        'tlpMain
        '
        Me.tlpMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tlpMain.BackColor = System.Drawing.Color.Transparent
        Me.tlpMain.ColumnCount = 4
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 500.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 500.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 500.0!))
        Me.tlpMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.Controls.Add(Me.pnlTop, 3, 0)
        Me.tlpMain.Controls.Add(Me.pnlDescription1, 0, 0)
        Me.tlpMain.Controls.Add(Me.pnlDescription, 1, 0)
        Me.tlpMain.Controls.Add(Me.pnlLoading, 2, 0)
        Me.tlpMain.Controls.Add(Me.TbPanel2, 3, 1)
        Me.tlpMain.Controls.Add(Me.TbPanel4, 3, 3)
        Me.tlpMain.Controls.Add(Me.pnlBottom, 3, 4)
        Me.tlpMain.Controls.Add(Me.pnlContent, 3, 2)
        Me.tlpMain.Location = New System.Drawing.Point(1, 31)
        Me.tlpMain.Margin = New System.Windows.Forms.Padding(0)
        Me.tlpMain.Name = "tlpMain"
        Me.tlpMain.RowCount = 5
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tlpMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 80.0!))
        Me.tlpMain.Size = New System.Drawing.Size(2510, 618)
        Me.tlpMain.TabIndex = 36
        '
        'TbPanel2
        '
        Me.TbPanel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(229, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(229, Byte), Integer))
        Me.TbPanel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TbPanel2.Location = New System.Drawing.Point(1500, 40)
        Me.TbPanel2.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel2.Name = "TbPanel2"
        Me.TbPanel2.Size = New System.Drawing.Size(1010, 1)
        Me.TbPanel2.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel2.TabIndex = 35
        Me.TbPanel2.tbBackgroundImage = Nothing
        Me.TbPanel2.tbShowWatermark = False
        Me.TbPanel2.tbSplit = "0,0,0,0"
        Me.TbPanel2.tbWatermark = Nothing
        Me.TbPanel2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel2.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel4
        '
        Me.TbPanel4.BackColor = System.Drawing.Color.FromArgb(CType(CType(229, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(229, Byte), Integer))
        Me.TbPanel4.Dock = System.Windows.Forms.DockStyle.Fill
        Me.TbPanel4.Location = New System.Drawing.Point(1500, 537)
        Me.TbPanel4.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel4.Name = "TbPanel4"
        Me.TbPanel4.Size = New System.Drawing.Size(1010, 1)
        Me.TbPanel4.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel4.TabIndex = 37
        Me.TbPanel4.tbBackgroundImage = Nothing
        Me.TbPanel4.tbShowWatermark = False
        Me.TbPanel4.tbSplit = "0,0,0,0"
        Me.TbPanel4.tbWatermark = Nothing
        Me.TbPanel4.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel4.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlBottom
        '
        Me.pnlBottom.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlBottom.Controls.Add(Me.llblRefresh)
        Me.pnlBottom.Controls.Add(Me.lblFromBackupMsg)
        Me.pnlBottom.Controls.Add(Me.btnExportMedia)
        Me.pnlBottom.Controls.Add(Me.btnDataRecoveryToPhone)
        Me.pnlBottom.Controls.Add(Me.btnExport)
        Me.pnlBottom.Controls.Add(Me.lblState)
        Me.pnlBottom.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlBottom.Location = New System.Drawing.Point(1500, 538)
        Me.pnlBottom.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(1010, 80)
        Me.pnlBottom.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlBottom.TabIndex = 38
        Me.pnlBottom.tbBackgroundImage = Nothing
        Me.pnlBottom.tbShowWatermark = False
        Me.pnlBottom.tbSplit = "0,0,0,0"
        Me.pnlBottom.tbWatermark = Nothing
        Me.pnlBottom.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBottom.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'llblRefresh
        '
        Me.llblRefresh.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.llblRefresh.AutoSize = True
        Me.llblRefresh.BackColor = System.Drawing.Color.Transparent
        Me.llblRefresh.Cursor = System.Windows.Forms.Cursors.Hand
        Me.llblRefresh.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.llblRefresh.ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.llblRefresh.LinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.Location = New System.Drawing.Point(67, 21)
        Me.llblRefresh.Name = "llblRefresh"
        Me.llblRefresh.Size = New System.Drawing.Size(65, 12)
        Me.llblRefresh.TabIndex = 41
        Me.llblRefresh.TabStop = True
        Me.llblRefresh.Text = "获取最新>>"
        Me.llblRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.llblRefresh.Visible = False
        Me.llblRefresh.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        '
        'lblFromBackupMsg
        '
        Me.lblFromBackupMsg.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.lblFromBackupMsg.AutoSize = True
        Me.lblFromBackupMsg.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.lblFromBackupMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFromBackupMsg.Location = New System.Drawing.Point(8, 21)
        Me.lblFromBackupMsg.Name = "lblFromBackupMsg"
        Me.lblFromBackupMsg.Size = New System.Drawing.Size(53, 12)
        Me.lblFromBackupMsg.TabIndex = 40
        Me.lblFromBackupMsg.tbAdriftWhenHover = False
        Me.lblFromBackupMsg.tbAutoEllipsis = False
        Me.lblFromBackupMsg.tbAutoSize = True
        Me.lblFromBackupMsg.tbHideImage = False
        Me.lblFromBackupMsg.tbIconImage = Nothing
        Me.lblFromBackupMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFromBackupMsg.tbIconPlaceText = 5
        Me.lblFromBackupMsg.tbShadow = False
        Me.lblFromBackupMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFromBackupMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFromBackupMsg.tbShowScrolling = False
        Me.lblFromBackupMsg.Text = "TbLabel1"
        Me.lblFromBackupMsg.Visible = False
        '
        'btnExportMedia
        '
        Me.btnExportMedia.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnExportMedia.BackColor = System.Drawing.Color.Transparent
        Me.btnExportMedia.BindingForm = Nothing
        Me.btnExportMedia.Font = New System.Drawing.Font("Arial", 12.0!)
        Me.btnExportMedia.Location = New System.Drawing.Point(963, 17)
        Me.btnExportMedia.Margin = New System.Windows.Forms.Padding(0)
        Me.btnExportMedia.Name = "btnExportMedia"
        Me.btnExportMedia.Padding = New System.Windows.Forms.Padding(6, 0, 10, 3)
        Me.btnExportMedia.Selectable = True
        Me.btnExportMedia.Size = New System.Drawing.Size(30, 38)
        Me.btnExportMedia.TabIndex = 83
        Me.btnExportMedia.tbAdriftIconWhenHover = False
        Me.btnExportMedia.tbAutoSize = False
        Me.btnExportMedia.tbAutoSizeEx = False
        Me.btnExportMedia.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blueright
        Me.btnExportMedia.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExportMedia.tbBadgeNumber = 0
        Me.btnExportMedia.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExportMedia.tbEndEllipsis = False
        Me.btnExportMedia.tbIconHoldPlace = True
        Me.btnExportMedia.tbIconImage = Global.iTong.My.Resources.Resources.btn_ddl_selecttype
        Me.btnExportMedia.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnExportMedia.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnExportMedia.tbIconMore = False
        Me.btnExportMedia.tbIconMouseDown = Nothing
        Me.btnExportMedia.tbIconMouseHover = Nothing
        Me.btnExportMedia.tbIconMouseLeave = Nothing
        Me.btnExportMedia.tbIconPlaceText = 3
        Me.btnExportMedia.tbIconReadOnly = Nothing
        Me.btnExportMedia.tbImageMouseDown = Nothing
        Me.btnExportMedia.tbImageMouseHover = Nothing
        Me.btnExportMedia.tbImageMouseLeave = Nothing
        Me.btnExportMedia.tbProgressValue = 50
        Me.btnExportMedia.tbReadOnly = False
        Me.btnExportMedia.tbReadOnlyText = False
        Me.btnExportMedia.tbShadow = False
        Me.btnExportMedia.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExportMedia.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExportMedia.tbShowDot = False
        Me.btnExportMedia.tbShowMoreIconImg = Nothing
        Me.btnExportMedia.tbShowNew = False
        Me.btnExportMedia.tbShowProgress = False
        Me.btnExportMedia.tbShowTip = True
        Me.btnExportMedia.tbShowToolTipOnButton = False
        Me.btnExportMedia.tbSplit = "13,11,13,11"
        Me.btnExportMedia.tbText = ""
        Me.btnExportMedia.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportMedia.tbTextColor = System.Drawing.Color.White
        Me.btnExportMedia.tbTextColorDisable = System.Drawing.Color.White
        Me.btnExportMedia.tbTextColorDown = System.Drawing.Color.White
        Me.btnExportMedia.tbTextColorHover = System.Drawing.Color.White
        Me.btnExportMedia.tbTextMouseDownPlace = 0
        Me.btnExportMedia.tbToolTip = ""
        Me.btnExportMedia.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExportMedia.tbToolTipFont = New System.Drawing.Font("Arial", 12.0!)
        Me.btnExportMedia.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExportMedia.Visible = False
        Me.btnExportMedia.VisibleEx = True
        '
        'btnDataRecoveryToPhone
        '
        Me.btnDataRecoveryToPhone.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDataRecoveryToPhone.BackColor = System.Drawing.Color.Transparent
        Me.btnDataRecoveryToPhone.BindingForm = Nothing
        Me.btnDataRecoveryToPhone.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnDataRecoveryToPhone.Location = New System.Drawing.Point(653, 17)
        Me.btnDataRecoveryToPhone.Name = "btnDataRecoveryToPhone"
        Me.btnDataRecoveryToPhone.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnDataRecoveryToPhone.Selectable = True
        Me.btnDataRecoveryToPhone.Size = New System.Drawing.Size(150, 38)
        Me.btnDataRecoveryToPhone.TabIndex = 78
        Me.btnDataRecoveryToPhone.tbAdriftIconWhenHover = False
        Me.btnDataRecoveryToPhone.tbAutoSize = False
        Me.btnDataRecoveryToPhone.tbAutoSizeEx = False
        Me.btnDataRecoveryToPhone.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnDataRecoveryToPhone.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnDataRecoveryToPhone.tbBadgeNumber = 0
        Me.btnDataRecoveryToPhone.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDataRecoveryToPhone.tbEndEllipsis = False
        Me.btnDataRecoveryToPhone.tbIconHoldPlace = True
        Me.btnDataRecoveryToPhone.tbIconImage = Nothing
        Me.btnDataRecoveryToPhone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecoveryToPhone.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDataRecoveryToPhone.tbIconMore = False
        Me.btnDataRecoveryToPhone.tbIconMouseDown = Nothing
        Me.btnDataRecoveryToPhone.tbIconMouseHover = Nothing
        Me.btnDataRecoveryToPhone.tbIconMouseLeave = Nothing
        Me.btnDataRecoveryToPhone.tbIconPlaceText = 2
        Me.btnDataRecoveryToPhone.tbIconReadOnly = Nothing
        Me.btnDataRecoveryToPhone.tbImageMouseDown = Nothing
        Me.btnDataRecoveryToPhone.tbImageMouseHover = Nothing
        Me.btnDataRecoveryToPhone.tbImageMouseLeave = Nothing
        Me.btnDataRecoveryToPhone.tbProgressValue = 50
        Me.btnDataRecoveryToPhone.tbReadOnly = False
        Me.btnDataRecoveryToPhone.tbReadOnlyText = False
        Me.btnDataRecoveryToPhone.tbShadow = False
        Me.btnDataRecoveryToPhone.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDataRecoveryToPhone.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDataRecoveryToPhone.tbShowDot = False
        Me.btnDataRecoveryToPhone.tbShowMoreIconImg = Nothing
        Me.btnDataRecoveryToPhone.tbShowNew = False
        Me.btnDataRecoveryToPhone.tbShowProgress = False
        Me.btnDataRecoveryToPhone.tbShowTip = True
        Me.btnDataRecoveryToPhone.tbShowToolTipOnButton = False
        Me.btnDataRecoveryToPhone.tbSplit = "13,11,13,11"
        Me.btnDataRecoveryToPhone.tbText = "恢复到手机"
        Me.btnDataRecoveryToPhone.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecoveryToPhone.tbTextColor = System.Drawing.Color.White
        Me.btnDataRecoveryToPhone.tbTextColorDisable = System.Drawing.Color.White
        Me.btnDataRecoveryToPhone.tbTextColorDown = System.Drawing.Color.White
        Me.btnDataRecoveryToPhone.tbTextColorHover = System.Drawing.Color.White
        Me.btnDataRecoveryToPhone.tbTextMouseDownPlace = 0
        Me.btnDataRecoveryToPhone.tbToolTip = ""
        Me.btnDataRecoveryToPhone.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDataRecoveryToPhone.tbToolTipFont = New System.Drawing.Font("宋体", 12.0!)
        Me.btnDataRecoveryToPhone.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDataRecoveryToPhone.VisibleEx = True
        '
        'btnExport
        '
        Me.btnExport.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnExport.Location = New System.Drawing.Point(813, 17)
        Me.btnExport.Margin = New System.Windows.Forms.Padding(0)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(150, 38)
        Me.btnExport.TabIndex = 84
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = False
        Me.btnExport.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = True
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Nothing
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnExport.tbIconMore = False
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 3
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbProgressValue = 50
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowDot = False
        Me.btnExport.tbShowMoreIconImg = Nothing
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowProgress = False
        Me.btnExport.tbShowTip = True
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "导出到电脑"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.White
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.White
        Me.btnExport.tbTextColorDown = System.Drawing.Color.White
        Me.btnExport.tbTextColorHover = System.Drawing.Color.White
        Me.btnExport.tbTextMouseDownPlace = 0
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("宋体", 12.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.VisibleEx = True
        '
        'pnlContent
        '
        Me.pnlContent.Controls.Add(Me.pnlContainer)
        Me.pnlContent.Controls.Add(Me.pnlTimeFilter)
        Me.pnlContent.Controls.Add(Me.pnlLeft)
        Me.pnlContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContent.Location = New System.Drawing.Point(1500, 41)
        Me.pnlContent.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContent.Name = "pnlContent"
        Me.pnlContent.Size = New System.Drawing.Size(1010, 496)
        Me.pnlContent.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContent.TabIndex = 39
        Me.pnlContent.tbBackgroundImage = Nothing
        Me.pnlContent.tbShowWatermark = False
        Me.pnlContent.tbSplit = "0,0,0,0"
        Me.pnlContent.tbWatermark = Nothing
        Me.pnlContent.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContent.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlLeft
        '
        Me.pnlLeft.Controls.Add(Me.pnlFriend)
        Me.pnlLeft.Controls.Add(Me.pnlSearchFriend)
        Me.pnlLeft.Controls.Add(Me.TbPanel7)
        Me.pnlLeft.Dock = System.Windows.Forms.DockStyle.Left
        Me.pnlLeft.Location = New System.Drawing.Point(0, 0)
        Me.pnlLeft.Name = "pnlLeft"
        Me.pnlLeft.Size = New System.Drawing.Size(282, 496)
        Me.pnlLeft.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLeft.TabIndex = 0
        Me.pnlLeft.tbBackgroundImage = Nothing
        Me.pnlLeft.tbShowWatermark = False
        Me.pnlLeft.tbSplit = "0,0,0,0"
        Me.pnlLeft.tbWatermark = Nothing
        Me.pnlLeft.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLeft.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbPanel7
        '
        Me.TbPanel7.BackColor = System.Drawing.Color.FromArgb(CType(CType(229, Byte), Integer), CType(CType(229, Byte), Integer), CType(CType(229, Byte), Integer))
        Me.TbPanel7.Dock = System.Windows.Forms.DockStyle.Right
        Me.TbPanel7.Location = New System.Drawing.Point(281, 0)
        Me.TbPanel7.Margin = New System.Windows.Forms.Padding(0)
        Me.TbPanel7.Name = "TbPanel7"
        Me.TbPanel7.Size = New System.Drawing.Size(1, 496)
        Me.TbPanel7.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.TbPanel7.TabIndex = 33
        Me.TbPanel7.tbBackgroundImage = Nothing
        Me.TbPanel7.tbShowWatermark = False
        Me.TbPanel7.tbSplit = "0,0,0,0"
        Me.TbPanel7.tbWatermark = Nothing
        Me.TbPanel7.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.TbPanel7.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'cmsSearch
        '
        Me.cmsSearch.AccessibleDescription = "161x92"
        Me.cmsSearch.DropShadowEnabled = False
        Me.cmsSearch.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsSearch.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiSearchAll, Me.tsmiSearchName, Me.tsmiSearchMsg})
        Me.cmsSearch.Name = "munSearch"
        Me.cmsSearch.Size = New System.Drawing.Size(161, 70)
        Me.cmsSearch.tbBackColor = System.Drawing.Color.White
        Me.cmsSearch.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsSearch.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsSearch.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiSearchAll
        '
        Me.tsmiSearchAll.CheckOnClick = True
        Me.tsmiSearchAll.Name = "tsmiSearchAll"
        Me.tsmiSearchAll.Size = New System.Drawing.Size(160, 22)
        Me.tsmiSearchAll.Text = "搜索全部"
        '
        'tsmiSearchName
        '
        Me.tsmiSearchName.Name = "tsmiSearchName"
        Me.tsmiSearchName.Size = New System.Drawing.Size(160, 22)
        Me.tsmiSearchName.Text = "只搜索联系人"
        '
        'tsmiSearchMsg
        '
        Me.tsmiSearchMsg.Name = "tsmiSearchMsg"
        Me.tsmiSearchMsg.Size = New System.Drawing.Size(160, 22)
        Me.tsmiSearchMsg.Text = "只搜索聊天记录"
        '
        'frmWeixin
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(2512, 650)
        Me.Controls.Add(Me.tlpMain)
        Me.Name = "frmWeixin"
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "微信消息记录"
        Me.Controls.SetChildIndex(Me.tlpMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlTop.ResumeLayout(False)
        CType(Me.picTimeFilter, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlSearchFriend.ResumeLayout(False)
        Me.pnlTimeFilter.ResumeLayout(False)
        Me.pnlTimeFilter.PerformLayout()
        Me.pnlContainer.ResumeLayout(False)
        CType(Me.PictureBoxAD, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlSearchHint.ResumeLayout(False)
        Me.pnlCount.ResumeLayout(False)
        Me.pnlLoadData.ResumeLayout(False)
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlFindDataResult.ResumeLayout(False)
        Me.pnlFindData.ResumeLayout(False)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlFriend.ResumeLayout(False)
        Me.TbPanel1.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        Me.pnlLoading.PerformLayout()
        Me.pnlBackupTip.ResumeLayout(False)
        Me.pnlBackupTip.PerformLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picNoLockScreen, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDescription.ResumeLayout(False)
        Me.pnlDescription.PerformLayout()
        Me.pnlTip.ResumeLayout(False)
        Me.pnlTip.PerformLayout()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picWeixinIcon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDescription1.ResumeLayout(False)
        CType(Me.picClosePwd1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picClosePwd, System.ComponentModel.ISupportInitialize).EndInit()
        Me.cmsExportEx.ResumeLayout(False)
        Me.cmsRecoveryData.ResumeLayout(False)
        Me.cmsFilter.ResumeLayout(False)
        Me.tlpMain.ResumeLayout(False)
        Me.pnlBottom.ResumeLayout(False)
        Me.pnlBottom.PerformLayout()
        Me.pnlContent.ResumeLayout(False)
        Me.pnlLeft.ResumeLayout(False)
        Me.cmsSearch.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents pnlFriend As tbPanel
    Friend WithEvents pnlLoading As tbPanel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents bgwLoadUsers As System.ComponentModel.BackgroundWorker
    Friend WithEvents wbsChat As tbWebBrowserEx
    Friend WithEvents pnlTimeFilter As tbPanel
    Friend WithEvents dtpStart As System.Windows.Forms.DateTimePicker
    Friend WithEvents lblStartTime As System.Windows.Forms.Label
    Friend WithEvents btnDownloadWeixin As tbButton
    Friend WithEvents btnReCheck As tbButton
    Friend WithEvents lblDownloadWeixin As System.Windows.Forms.Label
    Friend WithEvents lblWeixinDescription As System.Windows.Forms.Label
    Friend WithEvents lblWeixinTitle As System.Windows.Forms.Label
    Friend WithEvents picWeixinIcon As System.Windows.Forms.PictureBox
    Friend WithEvents lblState As tbLabel
    Friend WithEvents lblSystemInfo As tbLabel
    Friend WithEvents pnlSearchFriend As tbPanel
    Friend WithEvents pnlTop As tbPanel
    Friend WithEvents txtSearchChat As tbSearch
    Friend WithEvents btnOK As tbButton
    Friend WithEvents lblEndTime As System.Windows.Forms.Label
    Friend WithEvents dtpEnd As System.Windows.Forms.DateTimePicker
    Friend WithEvents lblLoadingDescription As System.Windows.Forms.Label
    Friend WithEvents pnlDescription As tbPanel
    Friend WithEvents picError As System.Windows.Forms.PictureBox
    Friend WithEvents btnReCheckEx As tbButton
    Friend WithEvents picNoLockScreen As System.Windows.Forms.PictureBox
    Friend WithEvents btnMoreTutorials As tbButton
    Friend WithEvents pnlDescription1 As iTong.Components.tbPanel
    Friend WithEvents btnReCheckEx1 As iTong.Components.tbButton
    Friend WithEvents picClosePwd As System.Windows.Forms.PictureBox
    Friend WithEvents picClosePwd1 As System.Windows.Forms.PictureBox
    Friend WithEvents lblStep2Description As System.Windows.Forms.Label
    Friend WithEvents lblStep1Description As System.Windows.Forms.Label
    Friend WithEvents lblCancelPwdBackup As iTong.Components.tbLabel
    Friend WithEvents btnTime As iTong.Components.tbButton
    Friend WithEvents btnFinalData As iTong.Components.tbButton
    Friend WithEvents pnlFindDataResult As System.Windows.Forms.Panel
    Friend WithEvents pnlCount As System.Windows.Forms.Panel
    Friend WithEvents rtxtFindDelCount As System.Windows.Forms.RichTextBox
    Friend WithEvents rtxtPrompt As System.Windows.Forms.RichTextBox
    Friend WithEvents rtxtFindDataInfo As System.Windows.Forms.RichTextBox
    Friend WithEvents RichTextBox1 As System.Windows.Forms.RichTextBox
    Friend WithEvents pnlBackupTip As System.Windows.Forms.Panel
    Friend WithEvents lblOpenTutorial As System.Windows.Forms.LinkLabel
    Friend WithEvents btnOpenTutorial As iTong.Components.tbButton
    Friend WithEvents cmsExportEx As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiPicEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAudioEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiVideoEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlTip As iTong.Components.tbPanel
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents tsmiExcelEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTxtEx As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiHtml As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnWeCahtUsers As iTong.Components.tbButton
    Friend WithEvents cmsRecoveryData As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiRecoveryToComputer As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiRecoveryToPhone As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents TbPanel1 As iTong.Components.tbPanel
    Friend WithEvents lblLinkmanMsg As iTong.Components.tbLabel
    Friend WithEvents cmsFilter As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiShowDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShowNoPublic As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlFindData As iTong.Components.tbPanel
    Friend WithEvents RichTextBox2 As System.Windows.Forms.RichTextBox
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents lblDoing As iTong.Components.tbLabel
    Friend WithEvents tlpMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TbPanel2 As iTong.Components.tbPanel
    Friend WithEvents TbPanel4 As iTong.Components.tbPanel
    Friend WithEvents pnlBottom As iTong.Components.tbPanel
    Friend WithEvents pnlContent As iTong.Components.tbPanel
    Friend WithEvents pnlLeft As iTong.Components.tbPanel
    Friend WithEvents TbPanel7 As iTong.Components.tbPanel
    Friend WithEvents btnDataRecoveryToPhone As iTong.Components.tbButton
    Friend WithEvents btnExportMedia As iTong.Components.tbButton
    Friend WithEvents btnExport As iTong.Components.tbButton
    Friend WithEvents btnExportSetting As iTong.Components.tbButton
    Friend WithEvents lblRegister As iTong.tbBattery
    Friend WithEvents cbxSelectAll As iTong.Components.tbCheckBox
    Friend WithEvents btnFilter As iTong.Components.tbButton
    Friend WithEvents btnPreviousMenu As iTong.Components.tbButton
    Friend WithEvents TbPanel8 As iTong.Components.tbPanel
    Friend WithEvents tsmiAll As System.Windows.Forms.ToolStripMenuItem
    Public WithEvents llblRefresh As System.Windows.Forms.LinkLabel
    Friend WithEvents lblFromBackupMsg As iTong.Components.tbLabel
    Friend WithEvents lblFindData As iTong.Components.tbLabel
    Friend WithEvents pnlLoadData As iTong.Components.tbPanel
    Friend WithEvents lblLoadData As iTong.Components.tbLabel
    Friend WithEvents PictureBox2 As System.Windows.Forms.PictureBox
    Friend WithEvents picTimeFilter As System.Windows.Forms.PictureBox
    Friend WithEvents btnTimeFilter As iTong.Components.tbButton
    Friend WithEvents tsmiFormat As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiMedia As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripSeparator2 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiShowNoDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiShowAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlSearchHint As System.Windows.Forms.Panel
    Friend WithEvents rtxtSearchHint As System.Windows.Forms.RichTextBox
    Friend WithEvents cmsSearch As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiSearchAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiSearchName As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiSearchMsg As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiGroup As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripSeparator1 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents btnReturnView As iTong.Components.tbButton
    Friend WithEvents PictureBoxAD As System.Windows.Forms.PictureBox
End Class
