﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeixinEmoticonNotify
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeixinEmoticonNotify))
        Me.lblDescription = New iTong.Components.tbLabel()
        Me.chkNoShow = New iTong.Components.tbCheckBox()
        Me.btnOK = New iTong.Components.tbButton()
        Me.pic3 = New System.Windows.Forms.PictureBox()
        Me.pnlBack = New iTong.Components.tbPanel()
        Me.lblCloseWeixin = New iTong.Components.tbLabel()
        Me.lblClickHome = New iTong.Components.tbLabel()
        Me.lblStep2 = New iTong.Components.tbLabel()
        Me.lblSetp1 = New iTong.Components.tbLabel()
        CType(Me.pic3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlBack.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(395, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(371, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(347, 0)
        '
        'lblDescription
        '
        Me.lblDescription.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblDescription.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.lblDescription.Location = New System.Drawing.Point(2, 40)
        Me.lblDescription.Name = "lblDescription"
        Me.lblDescription.Size = New System.Drawing.Size(415, 44)
        Me.lblDescription.TabIndex = 26
        Me.lblDescription.tbAdriftWhenHover = False
        Me.lblDescription.tbAutoEllipsis = False
        Me.lblDescription.tbAutoSize = False
        Me.lblDescription.tbHideImage = False
        Me.lblDescription.tbIconImage = Nothing
        Me.lblDescription.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDescription.tbIconPlaceText = 5
        Me.lblDescription.tbShadow = False
        Me.lblDescription.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDescription.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDescription.tbShowScrolling = False
        Me.lblDescription.Text = "记得要从后台退出微信，再导入表情哦！"
        Me.lblDescription.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'chkNoShow
        '
        Me.chkNoShow.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.chkNoShow.Appearance = System.Windows.Forms.Appearance.Button
        Me.chkNoShow.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.chkNoShow.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.chkNoShow.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.chkNoShow.ForeColor_Checked = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.chkNoShow.Location = New System.Drawing.Point(26, 457)
        Me.chkNoShow.Name = "chkNoShow"
        Me.chkNoShow.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.chkNoShow.Size = New System.Drawing.Size(80, 18)
        Me.chkNoShow.TabIndex = 72
        Me.chkNoShow.tbAdriftIconWhenHover = False
        Me.chkNoShow.tbAutoSize = False
        Me.chkNoShow.tbAutoSizeEx = True
        Me.chkNoShow.tbIconChecked = CType(resources.GetObject("chkNoShow.tbIconChecked"), System.Drawing.Image)
        Me.chkNoShow.tbIconCheckedMouseDown = Nothing
        Me.chkNoShow.tbIconCheckedMouseHover = Nothing
        Me.chkNoShow.tbIconCheckedMouseLeave = Nothing
        Me.chkNoShow.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbIconHoldPlace = True
        Me.chkNoShow.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.chkNoShow.tbIconIndeterminate = CType(resources.GetObject("chkNoShow.tbIconIndeterminate"), System.Drawing.Image)
        Me.chkNoShow.tbIconIndeterminateMouseDown = Nothing
        Me.chkNoShow.tbIconIndeterminateMouseHover = Nothing
        Me.chkNoShow.tbIconIndeterminateMouseLeave = Nothing
        Me.chkNoShow.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbIconPlaceText = 1
        Me.chkNoShow.tbIconUnChecked = CType(resources.GetObject("chkNoShow.tbIconUnChecked"), System.Drawing.Image)
        Me.chkNoShow.tbIconUnCheckedMouseDown = Nothing
        Me.chkNoShow.tbIconUnCheckedMouseHover = Nothing
        Me.chkNoShow.tbIconUnCheckedMouseLeave = Nothing
        Me.chkNoShow.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.chkNoShow.tbImageBackground = Nothing
        Me.chkNoShow.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.chkNoShow.tbImageCheckedMouseDown = Nothing
        Me.chkNoShow.tbImageCheckedMouseHover = Nothing
        Me.chkNoShow.tbImageCheckedMouseLeave = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseDown = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseHover = Nothing
        Me.chkNoShow.tbImageUnCheckedMouseLeave = Nothing
        Me.chkNoShow.tbReadOnly = False
        Me.chkNoShow.tbShadow = False
        Me.chkNoShow.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.chkNoShow.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.chkNoShow.tbSplit = "3,3,3,3"
        Me.chkNoShow.tbToolTip = ""
        Me.chkNoShow.Text = "不再提示"
        Me.chkNoShow.UseVisualStyleBackColor = False
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(301, 452)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(95, 28)
        Me.btnOK.TabIndex = 73
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "知道了"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'pic3
        '
        Me.pic3.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.pic3.Image = Global.iTong.My.Resources.Resources.weixin_close3
        Me.pic3.Location = New System.Drawing.Point(51, 95)
        Me.pic3.Name = "pic3"
        Me.pic3.Size = New System.Drawing.Size(157, 330)
        Me.pic3.TabIndex = 74
        Me.pic3.TabStop = False
        '
        'pnlBack
        '
        Me.pnlBack.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlBack.Controls.Add(Me.lblCloseWeixin)
        Me.pnlBack.Controls.Add(Me.lblClickHome)
        Me.pnlBack.Controls.Add(Me.lblStep2)
        Me.pnlBack.Controls.Add(Me.lblSetp1)
        Me.pnlBack.Location = New System.Drawing.Point(1, 32)
        Me.pnlBack.Name = "pnlBack"
        Me.pnlBack.Size = New System.Drawing.Size(417, 468)
        Me.pnlBack.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlBack.TabIndex = 76
        Me.pnlBack.tbBackgroundImage = Nothing
        Me.pnlBack.tbShowWatermark = False
        Me.pnlBack.tbSplit = "0,0,0,0"
        Me.pnlBack.tbWatermark = Nothing
        Me.pnlBack.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlBack.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblCloseWeixin
        '
        Me.lblCloseWeixin.AutoSize = True
        Me.lblCloseWeixin.ForeColor = System.Drawing.Color.FromArgb(CType(CType(38, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(211, Byte), Integer))
        Me.lblCloseWeixin.Location = New System.Drawing.Point(241, 284)
        Me.lblCloseWeixin.Name = "lblCloseWeixin"
        Me.lblCloseWeixin.Size = New System.Drawing.Size(77, 12)
        Me.lblCloseWeixin.TabIndex = 1
        Me.lblCloseWeixin.tbAdriftWhenHover = False
        Me.lblCloseWeixin.tbAutoEllipsis = False
        Me.lblCloseWeixin.tbAutoSize = True
        Me.lblCloseWeixin.tbHideImage = False
        Me.lblCloseWeixin.tbIconImage = Nothing
        Me.lblCloseWeixin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCloseWeixin.tbIconPlaceText = 5
        Me.lblCloseWeixin.tbShadow = False
        Me.lblCloseWeixin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCloseWeixin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCloseWeixin.tbShowScrolling = False
        Me.lblCloseWeixin.Text = "关闭微信后台"
        '
        'lblClickHome
        '
        Me.lblClickHome.AutoSize = True
        Me.lblClickHome.ForeColor = System.Drawing.Color.FromArgb(CType(CType(38, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(211, Byte), Integer))
        Me.lblClickHome.Location = New System.Drawing.Point(241, 210)
        Me.lblClickHome.Name = "lblClickHome"
        Me.lblClickHome.Size = New System.Drawing.Size(59, 12)
        Me.lblClickHome.TabIndex = 0
        Me.lblClickHome.tbAdriftWhenHover = False
        Me.lblClickHome.tbAutoEllipsis = False
        Me.lblClickHome.tbAutoSize = True
        Me.lblClickHome.tbHideImage = False
        Me.lblClickHome.tbIconImage = Nothing
        Me.lblClickHome.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblClickHome.tbIconPlaceText = 5
        Me.lblClickHome.tbShadow = False
        Me.lblClickHome.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblClickHome.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblClickHome.tbShowScrolling = False
        Me.lblClickHome.Text = "双击ome键"
        '
        'lblStep2
        '
        Me.lblStep2.AutoSize = True
        Me.lblStep2.Location = New System.Drawing.Point(241, 254)
        Me.lblStep2.Name = "lblStep2"
        Me.lblStep2.Size = New System.Drawing.Size(41, 12)
        Me.lblStep2.TabIndex = 1
        Me.lblStep2.tbAdriftWhenHover = False
        Me.lblStep2.tbAutoEllipsis = False
        Me.lblStep2.tbAutoSize = True
        Me.lblStep2.tbHideImage = False
        Me.lblStep2.tbIconImage = Nothing
        Me.lblStep2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblStep2.tbIconPlaceText = 5
        Me.lblStep2.tbShadow = False
        Me.lblStep2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblStep2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblStep2.tbShowScrolling = False
        Me.lblStep2.Text = "第二步"
        '
        'lblSetp1
        '
        Me.lblSetp1.AutoSize = True
        Me.lblSetp1.Location = New System.Drawing.Point(241, 180)
        Me.lblSetp1.Name = "lblSetp1"
        Me.lblSetp1.Size = New System.Drawing.Size(41, 12)
        Me.lblSetp1.TabIndex = 0
        Me.lblSetp1.tbAdriftWhenHover = False
        Me.lblSetp1.tbAutoEllipsis = False
        Me.lblSetp1.tbAutoSize = True
        Me.lblSetp1.tbHideImage = False
        Me.lblSetp1.tbIconImage = Nothing
        Me.lblSetp1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSetp1.tbIconPlaceText = 5
        Me.lblSetp1.tbShadow = False
        Me.lblSetp1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSetp1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSetp1.tbShowScrolling = False
        Me.lblSetp1.Text = "第一步"
        '
        'frmWeixinEmoticonNotify
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(419, 501)
        Me.Controls.Add(Me.pic3)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.chkNoShow)
        Me.Controls.Add(Me.lblDescription)
        Me.Controls.Add(Me.pnlBack)
        Me.Name = "frmWeixinEmoticonNotify"
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "提示"
        Me.Controls.SetChildIndex(Me.pnlBack, 0)
        Me.Controls.SetChildIndex(Me.lblDescription, 0)
        Me.Controls.SetChildIndex(Me.chkNoShow, 0)
        Me.Controls.SetChildIndex(Me.btnOK, 0)
        Me.Controls.SetChildIndex(Me.pic3, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        CType(Me.pic3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlBack.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents lblDescription As iTong.Components.tbLabel
    Friend WithEvents chkNoShow As iTong.Components.tbCheckBox
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents pic3 As System.Windows.Forms.PictureBox
    Friend WithEvents pnlBack As iTong.Components.tbPanel
    Friend WithEvents lblSetp1 As iTong.Components.tbLabel
    Friend WithEvents lblStep2 As iTong.Components.tbLabel
    Friend WithEvents lblCloseWeixin As iTong.Components.tbLabel
    Friend WithEvents lblClickHome As iTong.Components.tbLabel
End Class
