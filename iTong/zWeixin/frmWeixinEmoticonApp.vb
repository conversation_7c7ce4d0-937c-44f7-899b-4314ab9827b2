﻿Imports System.Threading
Public Class frmWeixinEmoticonApp

    Private mCheckFireware As Boolean = True  '目前有两种，一种校验固件大于8.3，一种是校验微信在6.2.2.0以上
    Private mIsAndroid As Boolean = True
    Private mAndroidEmotionUrl As String = "http://andtui.api.tongbu.com/PcApi.ashx?ApkDownUrl&packageName=com.kk.biaoqing"
    '微信表情大全可以在服务器上配制
    '[Weixin]
    'WeixinEmoticonName   应用名称
    'WeixinEmoticonItemId 应用itemid
    'WeixinEmoticonIcon   Icon有url

    Public Sub New(ByVal application As IApplication, ByVal device As IDevice, Optional ByVal CheckFireware As Boolean = True)
        MyBase.New(application, device)
        ' 此调用是 Windows 窗体设计器所必需的。

        Me.mIsAndroid = (TypeOf device Is AndroidDevice)

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.FunctionMappingKey = FunctionKey.Weixin
        Me.tbAutoSetFormSize = True
        Me.Icon = My.Resources.iTong

        Me.mCheckFireware = CheckFireware

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.btn_normal.Visible = False
        Me.btn_minimize.Visible = False

        Me.SetViewStyle()

        '加载Icon
        ThreadPool.QueueUserWorkItem(AddressOf UpdateWeixinEmoIcon)

        Me.PictureBoxAndroid.Image = My.Resources.bg_Emoticon_Android
    End Sub

    Private Sub SetViewStyle()
        'If Me.mIsAndroid Then
        Me.pnlAndroidBackground.Visible = True
        Me.pnlIOSBackground.Visible = False
        Me.Size = New Size(838, 602)
        'Else
        'Me.pnlAndroidBackground.Visible = False
        'Me.pnlIOSBackground.Visible = True
        'Me.Size = New Size(570, 395)
        'End If
        Dim mKey As ModelKey = IIf(Me.mIsAndroid, ModelKey.AndroidWeixinEmoticon, ModelKey.WeixinEmoticon)
        ActionCollectHelper.WeixinEmoticon(IIf(Me.mIsAndroid, Me.mAndroid, Me.mDevice), mKey, mKey, ActionDataType.View, FunctionSucceed.Succeed)
    End Sub
    Private Sub UpdateWeixinEmoIcon(ByVal state As Object)
        Dim url As String = ServerIniSetting.GetWeixinEmoticonIcon()
        If String.IsNullOrEmpty(url) Then
            Return
        End If
        Try
            Dim strPathWeixinOnPC As String = Path.Combine(Folder.CacheFolder, "Weixin")
            Folder.CheckFolder(strPathWeixinOnPC)
            Dim strFilePath As String = Path.Combine(strPathWeixinOnPC, Path.GetFileName(url))
            If Not File.Exists(strFilePath) Then
                Common.DownloadImage(url, 20000, strFilePath)
            End If

            If Not File.Exists(strFilePath) Then
                Return
            End If

            Dim img As Image = Utility.GetImageFormFile(strFilePath)
            Me.SetAppIcon(img)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmWeixinEmoticonApp_UpdateWeixinEmoIcon")
        End Try
    End Sub

    Private Delegate Sub SetAppIconHandler(ByVal img As Image)
    Private Sub SetAppIcon(ByVal img As Image)
        If Me.InvokeRequired Then
            Me.Invoke(New SetAppIconHandler(AddressOf SetAppIcon), img)
        Else
            Me.picEmoticon.Image = img
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.Text = Me.Language.GetString("Weixin.Lable.Emoticon")                        '"微信表情"
        Me.btnOK.Text = Me.Language.GetString("Weixin.Button.DownloadImmediately")      '"立即下载"
        Me.btnOKAndroid.Text = Me.Language.GetString("Weixin.Button.DownloadImmediately")      '"立即下载"

        Me.lblWeixinTitle.Text = ServerIniSetting.GetWeixinEmoticonNameCode() '"微表情大全"     '不需要翻译， 推荐的是软件
        Me.lblWeixinupdate.Text = Me.Language.GetString("Weixin.Lable.EmoticonUpdate")  '"大量萌贱表情，每周更新！"

        If Me.mCheckFireware Then
            Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Lable.EmoticonDescription")
            '"由于苹果8.3及以上固件改变了文件目录结构，" & vbCrLf & vbCrLf & _
            '"无法读取数据。推荐下载微表情大全，" & vbCrLf & vbCrLf & _
            '"直接在手机上发送表情。"
        Else
            Me.lblWeixinDescription.Text = Me.Language.GetString("Weixin.Lable.WXEmoticonDescription")
            '由于微信6.2.2以上版本改变了数据库，\r\n\r\n
            '无法读取数据。推荐下载【微表情大全】，\r\n\r\n
            '直接在手机上发送表情。
        End If
    End Sub

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        '932363848
        '下载包的文件名，可以不用翻译

        Me.Close()
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As CoreFoundation.IDevice)
        MyBase.OnDisconnect(device)

        Me.Close()
    End Sub

    Public Shared Sub ShowForm(ByVal app As IApplication, ByVal dev As IDevice, Optional ByVal CheckFireware As Boolean = True)
        Dim result As Boolean = True

        For Each frm As Form In Application.OpenForms
            If frm IsNot Nothing AndAlso TypeOf frm Is frmWeixinEmoticonApp Then
                frm.BringToFront()
                Return
            End If
        Next

        Dim frmApp As New frmWeixinEmoticonApp(app, dev, CheckFireware)
        frmApp.ShowDialog(app)
    End Sub

    Private Sub btnOKAndroid_Click(sender As Object, e As EventArgs) Handles btnOKAndroid.Click
        Dim mKey As ModelKey = ModelKey.WeixinEmoticon
        If Me.mIsAndroid Then
            mKey = ModelKey.AndroidWeixinEmoticon
            Dim strContent As String = Utility.GetContentStringFromUrl(mAndroidEmotionUrl, Encoding.UTF8)
            Dim objJson As JsonObject = JsonParser.ParseString(strContent)
            If objJson IsNot Nothing AndAlso objJson.ContainsKey("Data") Then
                Dim objDatas As JsonObject = objJson("Data")
                If objDatas IsNot Nothing AndAlso objDatas.ContainsKey("lurl") Then
                    Dim strJson As JsonString = CType(objDatas("lurl"), JsonString)
                    Dim urlStr As String = strJson.Value
                    Dim strName As String = Utility.GetParamValueFromQuery("appname", urlStr, "&", True)
                    Dim strVersion As String = Utility.GetParamValueFromQuery("ver", urlStr, "&", True)
                    Dim strNameEx As String = Utility.GetParamValueFromQuery("packagename", urlStr, "&", True)
                    Dim strIconUrl As String = Utility.GetParamValueFromQuery("icon", urlStr, "&", True)
                    If Me.mAndroid IsNot Nothing Then
                        CoreUpdateHelper.Instance(Me.mAndroid)
                    End If
                    WebSiteHelper.Instance.DownloadAPK(Me.mApplication, strNameEx, strIconUrl, strVersion, strName)
                End If
            End If
        Else
            mKey = ModelKey.WeixinEmoticon
            WebSiteHelper.Instance.DownloadAppByIDEx(Me.mApplication, Me.mDevice, ServerIniSetting.GetWeixinEmoticonItemId(), "", ServerIniSetting.GetWeixinEmoticonNameCode())
        End If
        ActionCollectHelper.WeixinEmoticon(IIf(Me.mIsAndroid, Me.mAndroid, Me.mDevice), mKey, mKey, ActionDataType.Download, FunctionSucceed.Succeed)
        Me.Close()
    End Sub
End Class