﻿Imports System.Threading
Imports System.IO
Imports System.Runtime.InteropServices

Public Class frmFindWeChatData
    Private mApplication As IApplication = Nothing
    Private mTDFindWeChatData As Thread = Nothing

    Public mDictDelChatInfo As New Dictionary(Of String, List(Of WeChatChatInfo))          '被删除的聊天信息
    Public mDictDelFriendInfo As Dictionary(Of String, WeChatFriendInfo) = New Dictionary(Of String, WeChatFriendInfo) '被删除的聊天信息 对应的联系人
    Public mDictFriendInfo As Dictionary(Of String, WeChatFriendInfo)                     '所有联系人表（UserName,FriendInfo）
    Private mStrDBMMOnPC As String = ""
    Private mDevice As IDevice = Nothing
    Private mStrMD5Myself As String = ""

    Private mWechatMMDB As WechatMMDB = Nothing
    Private mUserName As String = ""

    Private mChargeHelper As ChargeHelper = Nothing

    Public Sub New(ByVal app As IApplication, ByVal device As IDevice, dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), dictDelFriendInfo As Dictionary(Of String, WeChatFriendInfo), dictFriendInfo As Dictionary(Of String, WeChatFriendInfo), strDBMMOnPC As String, strMD5Myself As String, WechatMMDB As WechatMMDB, strUserName As String)
        InitializeComponent()

        Me.mDevice = device
        Me.mApplication = app
        Me.Language = app.Language
        Me.TopMost = True
        Me.FilletRadius = 3
        Me.Icon = My.Resources.iTong
        Me.tbShowTitleOnForm = True
        Me.ShowInTaskbar = True
        Me.tbTitleLocation = New Point(Me.tbTitleLocation.X, Me.tbTitleLocation.Y + 5)

        Me.CanbeMove = True
        Me.CanResize = False

        Me.StartPosition = FormStartPosition.CenterScreen

        Me.mDictDelChatInfo = dictDelChatInfo
        Me.mDictDelFriendInfo = dictDelFriendInfo
        Me.mDictFriendInfo = dictFriendInfo
        Me.mStrDBMMOnPC = strDBMMOnPC
        Me.mStrMD5Myself = strMD5Myself
        Me.mWechatMMDB = WechatMMDB
        Me.mUserName = strUserName

        If Me.Language.CurrentLanguage.LangName = "en-US" Then
            Me.Size = New Size(576, 326)
            Me.RichTextBox1.Size = New Size(478, 133)
            Me.picLoading.Location = New Point(51, 266)
            Me.lblDoing.Location = New Point(86, 267)
            Me.btnOK.Location = New Point(399, 258)
        End If

    End Sub
    Protected Overrides Sub SetInterface()
        Me.InitChargeHelper()

        Common.SetImageLocation(Me.picLoading, GuiResource.gif_loading_24)

        Me.picLoading.Visible = False
        Me.lblDoing.Visible = False

        Me.Text = Me.Language.GetString("Weixin.Lable.Recoverdata") '"找回数据"

        Me.lblHint.Text = Me.Language.GetString("Common.Info")

        RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint5")) '"此功能可【找回】已删除的微信聊天记录"
        RichTextBox1.AppendText(vbCrLf)
        RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint6")) '"包括找回：已经删除联系人的聊天记录"
        RichTextBox1.AppendText(vbCrLf)
        RichTextBox1.AppendText(Me.Language.GetString("Weixin.Lable.Hint7")) '"同时，支持非越狱设备的多账号聊天记录找回，无需登陆微信号，点击右上角账号框即可切换账号。"

        'Me.lblDoing.Text = Me.Language.GetString("Weixin.Lable.Finding")
        Me.lblDoing.Text = String.Format("{0} 0%", Me.Language.GetString("Weixin.Lable.Finding")) '"数据找回中，请耐心等候 ..."
        Me.btnOK.Text = Me.Language.GetString("Weixin.Button.Recoverdata") '"开始找回"

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        Try
            If Me.mTDFindWeChatData IsNot Nothing AndAlso Me.mTDFindWeChatData.ThreadState <> ThreadState.Stopped Then
                Me.mTDFindWeChatData.Abort()
            End If
        Catch
        End Try

        Try
            Me.KillProcess("WeChatHelper")
        Catch
        End Try
    End Sub


    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Try
            Me.btnOK.Enabled = True
        Catch ex As Exception

        End Try
        Try
            Try
                If Me.mTDFindWeChatData IsNot Nothing AndAlso Me.mTDFindWeChatData.ThreadState <> ThreadState.Stopped Then
                    Me.mTDFindWeChatData.Abort()
                End If
            Catch
            End Try

            Me.picLoading.Visible = True
            Me.lblDoing.Visible = True

            Me.btnOK.Enabled = True

            Me.mTDFindWeChatData = New Thread(AddressOf DoFindWeChatData)
            Me.mTDFindWeChatData.IsBackground = True
            Me.mTDFindWeChatData.Start()

        Catch ex As Exception

        End Try
        ActionCollectHelper.WeChatFindData(Me.mDevice, ModelKey.Weixin, ModelKey.FindWeChat, ActionDataType.Click, FunctionSucceed.None)
    End Sub

    <DllImport("User32.dll", EntryPoint:="FindWindow")> _
    Private Shared Function FindWindow(lpClassName As String, lpWindowName As String) As Integer
    End Function

    Private Function GetIHandle() As Integer
        Dim iHandle As Integer = 0
        Try
            iHandle = FindWindow(Nothing, Me.Language.GetString("Weixin.Lable.Recoverdata"))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetIHandle")
        End Try
        Return iHandle
    End Function

    Private Sub DoFindWeChatData()
        Try
            If Me.mWechatMMDB IsNot Nothing Then
                Me.mWechatMMDB.Dispose()
                Utility.WaitSeconds(1)
                Me.mWechatMMDB = WechatMMDB.Instance(Me.mStrDBMMOnPC)
            End If

            If Common.IsExtractWeChatDB() Then
                Folder.CheckFolder(Folder.ExtractWeChatDB)
                Try
                    Dim strSavePath As String = Path.Combine(Folder.ExtractWeChatDB, Date.Now().ToString("yyyyMMddHHmmss") & Me.mUserName & "_MM.sqlite")
                    File.Copy(Me.mStrDBMMOnPC, strSavePath, True)
                Catch
                End Try
                Try
                    Dim strSavePath As String = Path.Combine(Folder.ExtractWeChatDB, Date.Now().ToString("yyyyMMddHHmmss") & Me.mUserName & "_WCDB_Contact.sqlite")
                    File.Copy(Path.Combine(Path.GetDirectoryName(Me.mStrDBMMOnPC), "WCDB_Contact.sqlite"), strSavePath, True)
                Catch
                End Try
                Try
                    Dim strSavePath As String = Path.Combine(Folder.ExtractWeChatDB, Date.Now().ToString("yyyyMMddHHmmss") & Me.mUserName & "_fts_message.db")
                    File.Copy(Path.Combine(Path.GetDirectoryName(Me.mStrDBMMOnPC), "fts_message.db"), strSavePath, True)
                Catch
                End Try
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoFindWeChatData_1")
        End Try

        Me.DoFindWeChatDataByExe()


        Try
            If Me.mDictDelChatInfo.Count > 0 Then
                ActionCollectHelper.WeChatFindData(Me.mDevice, ModelKey.Weixin, ModelKey.FindWeChat, ActionDataType.Click, FunctionSucceed.Succeed)
            Else
                ActionCollectHelper.WeChatFindData(Me.mDevice, ModelKey.Weixin, ModelKey.FindWeChat, ActionDataType.Click, FunctionSucceed.Failure)
            End If
        Catch ex As Exception
        End Try
        Me.DialogResult = Windows.Forms.DialogResult.OK
    End Sub

    Private Sub DoFindWeChatDataByExe()
        Try
            Me.mDictDelChatInfo.Clear()
            Me.mDictDelFriendInfo.Clear()

            Dim strArguments As String = String.Format(" -i ""{0}"" --freespace -m {1} -w {2}", Me.mStrDBMMOnPC, Me.mStrMD5Myself, Me.GetIHandle())

            Dim strFreeEXEPath As String = ""
            Dim strPayEXEPath As String = ""
            Try
                For Each fInfo As FileInfo In New System.IO.DirectoryInfo(Path.Combine(Folder.AppFolder, "WeChat")).GetFiles("*.exe", SearchOption.AllDirectories)
                    If Path.GetFileName(fInfo.FullName).StartsWith("WeChatHelper") Then
                        Dim fvInfo As FileVersionInfo = FileVersionInfo.GetVersionInfo(fInfo.FullName)
                        If fvInfo.FileVersion = "1.0.2.9" Then
                            strFreeEXEPath = fInfo.FullName
                        Else
                            strPayEXEPath = fInfo.FullName
                        End If
                    End If
                Next
            Catch
            End Try

            Dim strUndark As String = ""
            If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None Then
                strUndark = strPayEXEPath
            Else
                strUndark = strFreeEXEPath
            End If

            If strUndark.Length = 0 OrElse Not File.Exists(strUndark) Then
                tbMessageBox.Show(Me, Me.Language.GetString("Backup.Message.LostProgram"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1) ' "找回聊天记录程序文件缺失，请重装软件或联系客服。"
                Me.Close()
                Return
            End If

            Dim info As ProcessStartInfo = New ProcessStartInfo()
            info.FileName = strUndark
            info.CreateNoWindow = True
            info.UseShellExecute = False
            info.Arguments = strArguments

            Dim process As Process = New Process()
            process.StartInfo = info
            process.Start()
            process.WaitForExit()
            process.Close()

            Dim strResultPath As String = Path.Combine(Path.GetDirectoryName(Me.mStrDBMMOnPC), "Record.txt")
            Dim dictLine As Dictionary(Of String, List(Of StringBuilder)) = New Dictionary(Of String, List(Of StringBuilder))
            Dim sb As StringBuilder = New StringBuilder()

            Dim line As String = ""
            Dim strKey As String = ""

            Dim intWaitDoEvent As Integer = 0

            Using sr As New StreamReader(strResultPath, Encoding.UTF8)
                While (InlineAssign(line, sr.ReadLine())) IsNot Nothing
                    If intWaitDoEvent >= 10000 Then
                        intWaitDoEvent = 0
                        Application.DoEvents()
                    End If

                    If line.StartsWith("Chat") OrElse line.StartsWith("noName") Then
                        Dim arrItem As String() = line.Split(";")
                        If arrItem(0).StartsWith("noName") Then

                            If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None Then
                                strKey = arrItem(0)
                            Else
                                strKey = IMExportHelper.UnKnownContact
                                line = line.Replace(arrItem(0), "noName")
                            End If

                        Else
                            strKey = arrItem(0).Replace("Chat_", "")
                        End If
                    End If

                    If line = String.Format("{0}{1}{0}", Convert.ToChar(1), Convert.ToChar(2)) Then
                        Me.AddItem(dictLine, strKey, sb)
                    Else
                        line = PinYinClass.CheckMojibake(line)
                        sb.AppendLine(line)
                    End If
                    intWaitDoEvent = intWaitDoEvent + 1
                End While
            End Using

            Me.AddItem(dictLine, strKey, sb)

            Dim lstInfo As List(Of WeChatChatInfo)
            Dim cInfo As WeChatChatInfo
            intWaitDoEvent = 0
            For Each sKey As String In dictLine.Keys
                lstInfo = New List(Of WeChatChatInfo)
                For Each sbItem As StringBuilder In dictLine(sKey)
                    If intWaitDoEvent >= 10000 Then
                        intWaitDoEvent = 0
                        Application.DoEvents()
                    End If
                    If sbItem.ToString().Length = 0 Then
                        Continue For
                    End If
                    Dim strTemp As String = sbItem.ToString()

                    If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None Then
                        If sKey.StartsWith("noName") Then
                            sKey = sKey.Replace("noName", IMExportHelper.UnKnownContact)
                            strTemp = sKey & sbItem.ToString()
                        End If
                    Else
                        If sKey.StartsWith(IMExportHelper.UnKnownContact) Then
                            sKey = IMExportHelper.UnKnownContact
                            strTemp = sKey & sbItem.ToString()
                        End If
                    End If

                    'table:TableVer:MesSvrID:CreateTime:Status:ImStatus:Type:Des:Mesage:
                    Dim arrItem As String() = strTemp.Split(";")
                    If arrItem.Length < 9 Then
                        Continue For
                    End If
                    Try
                        cInfo = New WeChatChatInfo()
                        cInfo.IntID = arrItem(1)
                        cInfo.DTCreateTime = WechatHelper.ConvertWeixinToPcTime(arrItem(3)) ' DateTimeHelper.ToDateTimeFromSeconds(arrItem(3))
                        cInfo.IntCreateTime = arrItem(3)
                        cInfo.IntStatus = arrItem(4)
                        cInfo.ImgStatus = arrItem(5)
                        cInfo.SCType = arrItem(6)
                        cInfo.IntDes = arrItem(7)
                        'cInfo.UsrName = sKey
                        cInfo.StrUsrName = Me.GetFindName(sKey)
                        Dim strMsg As String = sbItem.ToString().Substring(sbItem.ToString().IndexOf("Mesage:")).Replace("Mesage:", "")

                        For Each item As Char In WechatMMDB.LstIllegalASCII
                            strMsg = strMsg.Replace(item, "")
                        Next
                        If strMsg.Length = 0 Then
                            Continue For
                        End If
                        cInfo.StrMessage = strMsg

                        cInfo.IsDelete = True
                        lstInfo.Add(cInfo)
                    Catch ex As Exception
                        Common.LogException(strTemp & "   " & ex.ToString(), "DoFindWeChatData_2")
                    End Try
                    intWaitDoEvent = intWaitDoEvent + 1
                Next
                If lstInfo.Count > 0 Then
                    Me.mDictDelChatInfo.Add(sKey, lstInfo)
                End If
            Next

            intWaitDoEvent = 0
            For Each strFKey As String In Me.mDictFriendInfo.Keys
                If intWaitDoEvent >= 10000 Then
                    intWaitDoEvent = 0
                    Application.DoEvents()
                End If
                Dim fInfo As WeChatFriendInfo = Me.mDictFriendInfo(strFKey)

                If Me.mDictDelChatInfo.ContainsKey(fInfo.Md5) Then
                    If Not mDictDelFriendInfo.ContainsKey(strFKey) Then
                        fInfo.DTLastSendTime = Me.mDictDelChatInfo(fInfo.Md5)(0).DTCreateTime
                        fInfo.IsFindDate = True
                        mDictDelFriendInfo.Add(strFKey, fInfo)
                    End If
                End If
                intWaitDoEvent = intWaitDoEvent + 1
            Next

            If Me.mChargeHelper.mChargeInfo.PayVersion <> PayVersions.None Then
                AddDictFriendInfo(intWaitDoEvent)
            Else
                If Me.mDictDelChatInfo.ContainsKey(IMExportHelper.UnKnownContact) Then
                    Dim tempInfo As WeChatFriendInfo = New WeChatFriendInfo()
                    tempInfo.UsrName = IMExportHelper.UnKnownContact
                    tempInfo.NickName = IMExportHelper.UnKnownContact
                    tempInfo.NoteName = IMExportHelper.UnKnownContact
                    tempInfo.Md5 = Common.ToHexString(Common.GetMd5Array(tempInfo.UsrName))
                    tempInfo.DTLastSendTime = DateTime.Now
                    tempInfo.IsFindDate = True

                    If mDictDelFriendInfo.ContainsKey(IMExportHelper.UnKnownContact) = False Then
                        mDictDelFriendInfo.Add(IMExportHelper.UnKnownContact, tempInfo)
                    End If

                    If Me.mDictFriendInfo.ContainsKey(IMExportHelper.UnKnownContact) = False Then
                        Me.mDictFriendInfo.Add(IMExportHelper.UnKnownContact, tempInfo)
                    End If
                End If

                Dim uFriend As WeChatFriendInfo = New WeChatFriendInfo()
                uFriend.UsrName = IMExportHelper.UnKnownContact
                uFriend.NickName = IMExportHelper.UnKnownContact
                uFriend.NoteName = IMExportHelper.UnKnownContact
                uFriend.Md5 = Common.ToHexString(Common.GetMd5Array(uFriend.UsrName))
                uFriend.DTLastSendTime = DateTime.Now
                uFriend.IsFindDate = True
                For Each str As String In Me.mDictDelChatInfo.Keys
                    Dim iInfo As WeChatFriendInfo = GetFriendInfo(str)
                    If str = IMExportHelper.UnKnownContact OrElse iInfo IsNot Nothing Then
                        Continue For
                    End If

                    If mDictDelFriendInfo.ContainsKey(IMExportHelper.UnKnownContact) = False Then
                        mDictDelFriendInfo.Add(IMExportHelper.UnKnownContact, uFriend)
                    End If

                    If Me.mDictFriendInfo.ContainsKey(IMExportHelper.UnKnownContact) = False Then
                        Me.mDictFriendInfo.Add(IMExportHelper.UnKnownContact, uFriend)
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoFindWeChatDataByExe")
        End Try
    End Sub

    Private Sub AddDictFriendInfo(ByRef intWaitDoEvent As Integer)
        Dim uFriend As WeChatFriendInfo = Nothing
        intWaitDoEvent = 0
        For Each str As String In Me.mDictDelChatInfo.Keys
            If intWaitDoEvent >= 10000 Then
                intWaitDoEvent = 0
                Application.DoEvents()
            End If
            Dim iInfo As WeChatFriendInfo = GetFriendInfo(str)
            If str = IMExportHelper.UnKnownContact OrElse iInfo IsNot Nothing Then
                Continue For
            End If

            Dim strName As String = str
            If Not str.StartsWith(IMExportHelper.UnKnownContact) Then
                strName = IMExportHelper.UnKnownContact & "_" & strName
            End If
            uFriend = New WeChatFriendInfo()
            uFriend.DTLastSendTime = DateTime.Now
            uFriend.IsFindDate = True
            uFriend.UsrName = strName
            uFriend.NickName = strName
            uFriend.NoteName = strName
            uFriend.Md5 = Common.ToHexString(Common.GetMd5Array(strName))

            If mDictDelFriendInfo.ContainsKey(strName) = False Then
                mDictDelFriendInfo.Add(strName, uFriend)
            End If

            If Me.mDictFriendInfo.ContainsKey(strName) = False Then
                Me.mDictFriendInfo.Add(strName, uFriend)
            End If
            intWaitDoEvent = intWaitDoEvent + 1
        Next
    End Sub

    Private Delegate Sub SetProgressHandler(ByVal strProgress As String)
    Private Sub SetProgress(ByVal strProgress As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetProgressHandler(AddressOf SetProgress), strProgress)
        Else
            Try
                Me.lblDoing.Text = String.Format("{0} {1}%", Me.Language.GetString("Weixin.Lable.Finding"), strProgress)
                Application.DoEvents()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetProgress")
            End Try
        End If
    End Sub

    Private Function GetFriendInfo(ByVal strKey As String) As WeChatFriendInfo
        Dim fInfo As WeChatFriendInfo = Nothing
        For Each item As WeChatFriendInfo In Me.mDictFriendInfo.Values
            If item.Md5 = strKey Then
                fInfo = New WeChatFriendInfo()
                fInfo.StrChatRoomMem = item.StrChatRoomMem
                fInfo.IsFindDate = item.IsFindDate
                fInfo.StrLastSendMsg = item.StrLastSendMsg
                fInfo.DTLastSendTime = item.DTLastSendTime
                fInfo.Md5 = item.Md5
                fInfo.NickName = item.NickName
                fInfo.NoteName = item.NoteName
                fInfo.StrProfile = item.StrProfile
                fInfo.IntSex = item.IntSex
                fInfo.SFType = item.SFType
                fInfo.UsrName = item.UsrName
                Exit For
            End If
        Next
        Return fInfo
    End Function

    Private Function InlineAssign(ByRef line As String, strReadLine As String) As Object
        line = strReadLine
        Return line
    End Function

    Private Sub AddItem(ByRef dictLine As Dictionary(Of String, List(Of StringBuilder)), ByVal strKey As String, ByRef sb As StringBuilder)
        Try
            If strKey.Length = 0 Then
                strKey = IMExportHelper.UnKnownContact
            End If
            Dim lst As List(Of StringBuilder) = New List(Of StringBuilder)
            If dictLine.ContainsKey(strKey) Then
                lst = dictLine(strKey)
                lst.Add(sb)
                dictLine(strKey) = lst
            Else
                lst.Add(sb)
                dictLine.Add(strKey, lst)
            End If
            sb = New StringBuilder()

        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddItem")
        End Try
    End Sub

    Private Function GetFindName(strMd5 As String) As String
        Dim strUserName As String = strMd5
        Try
            For Each item As KeyValuePair(Of String, WeChatFriendInfo) In Me.mDictFriendInfo
                If item.Value.Md5 = strMd5 Then
                    strUserName = item.Value.UsrName
                    Continue For
                End If
            Next

        Catch ex As Exception

        End Try
        Return strUserName
    End Function

    Protected Overrides Sub WndProc(ByRef m As Message)
        Const WM_COPYDATA As Integer = &H4A

        If m.Msg = WM_COPYDATA AndAlso CInt(m.WParam) = 122 Then
            Select Case m.Msg
                Case WM_COPYDATA
                    Try
                        Dim mystr As New COPYDATASTRUCT()
                        Dim mytype As Type = mystr.[GetType]()
                        mystr = DirectCast(m.GetLParam(mytype), COPYDATASTRUCT)
                        Dim iProgress As Integer = mystr.dwData.ToInt32()
                        Me.SetProgress(iProgress)
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "WndProc_WeChatFindData")
                    End Try
                    Exit Select
                Case Else
                    MyBase.DefWndProc(m)
                    Exit Select
            End Select
        End If

        MyBase.WndProc(m)

    End Sub

    Private Sub KillProcess(ByVal appName As String)
        Try
            Dim strName As String = Path.GetFileNameWithoutExtension(appName)
            Dim tryCount As Integer = 0

            Do
                tryCount += 1
                If tryCount >= 10 Then
                    Exit Do
                End If

                Dim arrProcess() As Process = Process.GetProcessesByName(appName)
                If arrProcess Is Nothing OrElse arrProcess.Length = 0 Then
                    Exit Do
                End If

                For Each pro As Process In arrProcess
                    Try
                        pro.Kill()
                        Utility.WaitSeconds(0.5)
                    Catch ex1 As Exception
                        Common.LogException(ex1.ToString())
                    End Try
                Next
            Loop
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
    End Sub

    Private Sub InitChargeHelper()
        If Me.mChargeHelper Is Nothing Then
#If IS_ITONG Then
            Me.mChargeHelper = ChargeHelper.Instance(PayVersions.iTongWeChatAssistant)
#Else
            Me.mChargeHelper = ChargeHelper.Instance()
#End If
        End If
    End Sub



    


End Class