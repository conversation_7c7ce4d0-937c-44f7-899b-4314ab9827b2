﻿Imports System.Threading

Public Class frmChoiceWeChatSpareApp

    Private mApplication As IApplication
    Private mDevice As iPhoneDevice

    Private mTdLoadWeChatSpareApp As Thread = Nothing
    Private mTbGroup As tbGroupPanel = Nothing
    Private mIsFromDevice As Boolean = False
    Private mStrBackupPath As String = ""

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As iPhoneDevice, ByVal isFromDevice As Boolean, ByVal strBackupPath As String)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mApplication = application
        Me.mDevice = device
        Me.FilletRadius = 5
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong
        Me.mIsFromDevice = isFromDevice
        Me.mStrBackupPath = strBackupPath
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.mTbGroup = Me.CreateGroup("", "WeChatSpareApp")
        Me.LoadWeChatSpareApp()
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = "选择要操作的共存版"
    End Sub

#End Region

    Private Sub LoadWeChatSpareApp()
        Try
            If Me.mTdLoadWeChatSpareApp IsNot Nothing AndAlso Me.mTdLoadWeChatSpareApp.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mTdLoadWeChatSpareApp = New Thread(AddressOf DoLoadWeChatSpareApp)
            Me.mTdLoadWeChatSpareApp.IsBackground = True
            Me.mTdLoadWeChatSpareApp.Start()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadWeChatSpareApp")
        End Try
    End Sub

    Private Sub DoLoadWeChatSpareApp(ByVal obj As Object)
        Try
            Dim dictWeChatSku As New Dictionary(Of String, String)
            If Me.mIsFromDevice Then
                '从设备获取
                dictWeChatSku = BackupHelper.GetSkuByDevice(Me.mDevice, ScreenSkuState.All)
            Else
                '从备份获取
                dictWeChatSku = BackupHelper.GetSkuByBackup(Me.mStrBackupPath, ScreenSkuState.All)
            End If

            If dictWeChatSku.Count = 0 Then
                Me.DialogResult = Windows.Forms.DialogResult.OK
                Return
            End If
            Dim isOne As Boolean = False
            If dictWeChatSku.Count = 1 Then
                isOne = True
            End If

            For Each item As KeyValuePair(Of String, String) In dictWeChatSku
                If isOne Then
                    ServerIniSetting.SetWeChatSpareSKU(item.Key)
                    Me.DialogResult = Windows.Forms.DialogResult.OK
                    Return
                End If
                Me.HCreateItem(item.Key, item.Key)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoLoadWeChatSpareApp")
        End Try
    End Sub

    Private Delegate Sub CreateItemHandler(ByVal strName As String, ByVal strSku As String)
    Public Sub HCreateItem(ByVal strName As String, ByVal strSku As String)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New CreateItemHandler(AddressOf HCreateItem), strName, strSku)
            Else
                Dim fntText As Font = Common.CreateFont("Arial", 9, FontStyle.Regular)
                Dim btnItem As tbButton = Me.CreateItemEx(Me.GetWeChatName(strName), fntText, _
                                                 My.Resources.more_weixincoexist, My.Resources.more_weixincoexist, _
                                                 New Type() {GetType(String), GetType(tbSplashBox)}, _
                                                 New Object() {"OpenWeChat"}, strSku)
                ' btnItem.tbToolTip = strSku
                Me.mTbGroup.Controls.Add(btnItem)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "HCreateItem")
        End Try
    End Sub

    Private Function GetWeChatName(ByVal strSku As String) As String
        Dim strName As String = strSku.Replace("com.tencent.xin.mjuruangong.", "").ToLower()
        Try
            If strName = "realtimemessage.test" Then
                strName = "实时(体验)"
            ElseIf strName = "realtimemessage" Then
                strName = "实时"
            ElseIf strName = "doubleopen.test" Then
                strName = "多开(体验)"
            ElseIf strName = "doubleopen" Then
                strName = "多开"
            ElseIf strName = "trans.test" Then
                strName = "转发(体验)"
            ElseIf strName = "trans" Then
                strName = "转发"
            ElseIf strName = "fansmgr.test" Then
                strName = "加粉(体验)"
            ElseIf strName = "fansmgr" Then
                strName = "加粉"
            ElseIf strName = "master.test" Then
                strName = "达人(体验)"
            ElseIf strName = "master" Then
                strName = "达人"
            ElseIf strName = "calculator.test" Then
                strName = "隐私(体验)"
            ElseIf strName = "calculator" Then
                strName = "隐私"
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetWeChatName")
        End Try
        Return strName
    End Function

    Private Function CreateGroup(ByVal strGroupName As String, Optional ByVal groupTag As String = "") As tbGroupPanel
        Dim group As New tbGroupPanel
        With group
            .Tag = groupTag
            .GroupName = strGroupName
            .GroupNameOffset = New Point(16, 20)
            .Padding = New Padding(15, 40, 15, 12)
            .Margin = System.Windows.Forms.Padding.Empty
            .Size = New Size(Me.flpMain.Width, 135)
            .ForeColor = Color.FromArgb(49, 101, 160)
            .Font = Common.CreateFont("Arial", 12, FontStyle.Bold)
            .BackColor = Color.FromArgb(250, 251, 252)
        End With
        Me.flpMain.Controls.Add(group)
        Return group
    End Function
    Private Function CreateItemEx(ByVal strText As String, ByVal fntText As Font, _
                                  ByVal imgIcon As Image, ByVal imgIconReadonly As Image, _
                                  ByVal arrType() As Type, Optional ByVal args() As Object = Nothing, _
                                  Optional ByVal tagDesc As String = "") As tbButton
        Dim btn As New tbButton

        With btn
            .AccessibleDescription = tagDesc
            .BackColor = Color.Transparent
            .Margin = New Padding(2, 2, 2, 2)
            .Padding = New Padding(0, 5, 2, 5)
            .BackColor = Color.Transparent
            .Size = New Size(85, 85)
            .tbAutoSize = False
            .tbIconHoldPlace = True
            .tbIconPlaceText = 10
            .tbIconMore = False
            .tbShadow = False
            .tbShowToolTipOnButton = False
            .tbTextAlign = ContentAlignment.BottomCenter
            .tbTextColor = Color.Black
            .tbIconImageAlign = ContentAlignment.TopCenter
            .tbImageMouseDown = Nothing
            .tbImageMouseHover = Nothing
            .tbImageMouseLeave = Nothing
            .tbBackgroundImage = My.Resources.more_btn_3_bg
            .tbBackgroundImageState = ImageState.ThreeState
            .MinimumSize = New Size(85, 85)
            .MaximumSize = New Size(150, 85)

            .tbIconImage = imgIcon
            .tbIconReadOnly = imgIconReadonly
            If imgIconReadonly IsNot Nothing Then
                .tbReadOnly = True
                .tbReadOnlyText = True
            End If

            .tbSplit = "6,6,6,6"

            Dim strTypes As String = String.Empty
            For Each t As Type In arrType
                strTypes &= t.FullName & "&"
            Next
            strTypes = strTypes.Trim("&"c)
            .Name = strTypes

            .tbText = strText
            .Font = fntText

            If args IsNot Nothing Then
                .Tag = args
            End If
        End With

        AddHandler btn.Click, AddressOf ToolItem_ClickEx
        AddHandler btn.MouseLeave, AddressOf ToolItem_MouseLeave
        Return btn
    End Function
    Private Sub ToolItem_MouseLeave(sender As Object, e As EventArgs)
        frmTipInfo.HideTip(sender)
    End Sub

    Private Sub ToolItem_ClickEx(sender As Object, e As EventArgs)
        Dim item As tbButton = sender
        Dim strSku As String = item.AccessibleDescription
        If strSku.Length > 0 Then
            ServerIniSetting.SetWeChatSpareSKU(strSku)
        End If
        Me.DialogResult = Windows.Forms.DialogResult.OK

    End Sub

End Class