﻿Imports System.Web
Imports System.Threading
Imports System.Text.RegularExpressions


Public Class WeixinImportOrExportHelper
    Implements IDisposable

#Region "--- 属性 字段 ---"
    Private mLanguage As LanguageInterface = Nothing
    Private mRegular As String = String.Empty
    Private mDevice As IDevice = Nothing
    Private mUsrNameMyself As String = String.Empty
    Private mDictFriendInfo As Dictionary(Of String, WeChatFriendInfo)                     '所有联系人表（UserName,FriendInfo）
    Private mDictQQFriendInfo As Dictionary(Of String, QQFriendInfo)                     '所有联系人表（UserName,FriendInfo）
    Private mBlnInstallExcel As Boolean = True                                               '是否安装Excel
    Private mWeixinMMDB As WeixinMMDB = Nothing                                 '主数据库（联系人和聊天记录）
    Private mQQDB As QQDB = Nothing
    Private mAndroidWeixin As AndroidWeixin = Nothing

    Private mLstContentLayout As List(Of ContentLayout) = New List(Of ContentLayout)

    Public Event ExportProgressEventHandler(ByVal sender As Object, ByVal args As ExportProgressEventArgs)
    Public Event ExportProgressNewEventHandler(ByVal sender As Object, ByVal args As ExportProgressEventArgs)
    Private mThreadExport As Thread = Nothing
    Private mThreadIsInstallExcel As Thread = Nothing

    Public Shared ReadOnly mStrUnknown As String = "未知联系人"
    Public Shared ReadOnly mStrUnknownEx As String = "未知联系人_1" '有联系人 但是没有在微信通讯录里面

    Public Shared ReadOnly mUinSecretKey As String = "ac68!3#1"

    Private mPathContainer As String = String.Empty
    Private mUIFileSharingEnabled As Boolean = True
    Private mAfcHandler As System.IntPtr = IntPtr.Zero
    Private mIsWeixinBackup As Boolean = False
    Private mIsWeixinAloneBackup As Boolean = False
    Private mWeChatSKU As String = "com.tencent.xin"
    Private mDictSha1 As New Dictionary(Of String, String)
    Private mWeixinBackupCache As String = String.Empty
    Private mPathWeixinOnPhone As String = String.Empty
    Private mPathWeixinOnPC_Temp As String = String.Empty
    Private mPathWhatsAppOnPC_Temp As String = String.Empty
    Protected mListDir As New List(Of String)
    Protected mLstMBFileRecord As List(Of MBFileRecord)

    Private mThreadExportHtml As Thread = Nothing
    Private mThreadExportMedia As Thread = Nothing
    Private mThreadExportContact As Thread = Nothing
    Private mThreadExportCallHistory As Thread = Nothing

    Private mIntCurrentIndex As Integer = 0
    Private mIntTotal As Integer = 0
    Private mStrNickName As String = ""
    Private mBackupDeviceUdid As String = ""

    Private mExportSocialType As ExportSocialType

    Private mChargeHelper As ChargeHelper = Nothing
    Private mWhatsAppHelper As WhatsAppHelper = Nothing
    Public mCurrentQQNum As String = String.Empty
#If IS_RECOVERY Then
    Private m_enabled As Boolean = False
    Private xlApp As Object = Nothing
    Private xlBook As Object = Nothing
    Private xlSheet As Object = Nothing
#End If

#End Region

#Region "--- 单例 ---"
    Private Shared mDictInstances As New Dictionary(Of String, WeixinImportOrExportHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New(ByVal device As IDevice, strUsrNameMyself As String, ByVal dictFriendInfo As Object, objWx As Object)
        mLanguage = LanguageInterface.Instance()
        mDevice = device
        '获取解析网页的正则
        mRegular = ServerIniSetting.GetRegularForWeixin()

        mUsrNameMyself = strUsrNameMyself
        If TypeOf dictFriendInfo Is Dictionary(Of String, WeChatFriendInfo) Then
            mDictFriendInfo = dictFriendInfo
        ElseIf TypeOf dictFriendInfo Is Dictionary(Of String, QQFriendInfo) Then
            mDictQQFriendInfo = dictFriendInfo
        End If

        If TypeOf objWx Is WeixinMMDB Then
            mWeixinMMDB = objWx
            mExportSocialType = ExportSocialType.WeChat
        ElseIf TypeOf objWx Is QQDB Then
            mQQDB = objWx
            mExportSocialType = ExportSocialType.QQ
        ElseIf TypeOf objWx Is AndroidWeixin Then
            mAndroidWeixin = objWx
            mExportSocialType = ExportSocialType.AndroidWeChat

        ElseIf TypeOf objWx Is WhatsAppHelper Then
            Me.mWhatsAppHelper = objWx
            mExportSocialType = ExportSocialType.WhatsApp

        End If
        Me.InitChargeHelper()
        'Me.CheckIsInstallExcel()

        'Try
        '    xlApp = CreateObject("Excel.Application")
        '    xlBook = xlApp.Workbooks.Add
        '    xlSheet = xlBook.Worksheets(1)
        '    m_enabled = True
        'Catch
        '    m_enabled = False
        'End Try
    End Sub

    Private Shared mHelper As WeixinImportOrExportHelper = Nothing

    ''' <summary>
    ''' Modified by Utmost20160530
    ''' </summary>
    ''' <param name="device">Android/iOS设备</param>
    ''' <param name="strUsrNameMyself"></param>
    ''' <param name="dictFriendInfo"></param>
    ''' <param name="objWx"></param>
    ''' <returns></returns>
    ''' <remarks></remarks>
    Public Shared Function GetInstance(ByVal device As IDevice, strUsrNameMyself As String, ByVal dictFriendInfo As Object, objWx As Object) As WeixinImportOrExportHelper
        Dim helper As WeixinImportOrExportHelper = Nothing
        Dim blnRetry As Boolean = False

        SyncLock _lockGetInstance
DO_RETRY:
            Try
                If device Is Nothing Then
                    If Not mDictInstances.ContainsKey(NoDevcieIdentifier) Then
                        mDictInstances.Add(NoDevcieIdentifier, New WeixinImportOrExportHelper(device, strUsrNameMyself, dictFriendInfo, objWx))
                    End If
                    helper = mDictInstances.Item(NoDevcieIdentifier)

                Else

                    If mDictInstances.ContainsKey(device.DeviceID) Then  ''device.Identifier
                        Dim tmpHelper As WeixinImportOrExportHelper = mDictInstances(device.DeviceID)
                        If tmpHelper.mDevice.IsConnected = False AndAlso device.IsConnected Then
                            mDictInstances.Remove(device.DeviceID)
                            mDictInstances.Add(device.DeviceID, New WeixinImportOrExportHelper(device, strUsrNameMyself, dictFriendInfo, objWx))

                            '释放资源
                            tmpHelper.Dispose()
                        End If

                    Else
                        If Not mDictInstances.ContainsKey(device.DeviceID) Then
                            mDictInstances.Add(device.DeviceID, New WeixinImportOrExportHelper(device, strUsrNameMyself, dictFriendInfo, objWx))
                        End If

                    End If

                    helper = mDictInstances.Item(device.DeviceID)
                End If

            Catch ex As Exception
                If Not blnRetry Then
                    blnRetry = True
                    GoTo DO_RETRY
                End If
            End Try
        End SyncLock

        Return helper

    End Function

    Private Sub InitChargeHelper()
        If Me.mChargeHelper Is Nothing Then
#If IS_ITONG Then
            Me.mChargeHelper = ChargeHelper.Instance(PayVersions.iTongWeChatAssistant)
#Else
            Me.mChargeHelper = ChargeHelper.Instance()
#End If
        End If
    End Sub

#End Region

#Region "--- 释放 ---"

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
                Me.mDictFriendInfo = Nothing
                mDictInstances.Clear()

                Try
                    If Me.mThreadExportHtml IsNot Nothing AndAlso Me.mThreadExportHtml.ThreadState <> ThreadState.Stopped Then
                        Me.mThreadExportHtml.Abort()
                    End If
                Catch
                End Try

                Try
                    If Me.mThreadExportContact IsNot Nothing AndAlso Me.mThreadExportContact.ThreadState <> ThreadState.Stopped Then
                        Me.mThreadExportContact.Abort()
                    End If
                Catch
                End Try

                Try
                    If Me.mThreadExportCallHistory IsNot Nothing AndAlso Me.mThreadExportCallHistory.ThreadState <> ThreadState.Stopped Then
                        Me.mThreadExportCallHistory.Abort()
                    End If
                Catch
                End Try

                Try
                    If Me.mThreadExportMedia IsNot Nothing AndAlso Me.mThreadExportMedia.ThreadState <> ThreadState.Stopped Then
                        Me.mThreadExportMedia.Abort()
                    End If
                Catch
                End Try

            End If
        End If
        disposedValue = True
    End Sub

#End Region

#Region "--- 导出 ---"

    Private Function GetUnknownEx(dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo))) As List(Of String)
        Dim lstTemp As New List(Of String)
        Try
            For Each strKey As String In dictDelChatInfo.Keys
                If strKey = WeixinImportOrExportHelper.mStrUnknown Then
                    Continue For
                End If

                If Not Me.IsContain(strKey) Then
                    lstTemp.Add(strKey)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetUnknownEx")
        End Try
        Return lstTemp
    End Function

    Private Function IsContain(strKey As String) As Boolean
        Dim isResult As Boolean = False
        For Each item As WeChatFriendInfo In Me.mDictFriendInfo.Values
            If item.Md5 = strKey Then
                isResult = True
                Exit For
            End If
        Next
        Return isResult
    End Function
    Private Function ExportToFile(ByVal lstChatInfo As List(Of SociaChatBase), ByVal strFilePath As String, ByVal blnExportToTxt As Boolean) As Boolean
        Dim result As Boolean = False

        If blnExportToTxt Then
            '导出为txt文件
            result = Me.ExportToTxtFile(lstChatInfo, strFilePath, False)
        Else
            '导出为excel文件
            result = Me.ExportToExcelFileEx(lstChatInfo, strFilePath)
        End If

        Return result
    End Function
    Private Function ExportToTxtFile(ByVal lstChatInfo As List(Of SociaChatBase), ByVal strFilePath As String, ByVal blnCopyToClipboard As Boolean) As Boolean
        Dim result As Boolean = False
        Try
            If lstChatInfo Is Nothing Then
                Return result
            End If

            Dim lstHead As List(Of ContentLayout) = New List(Of ContentLayout)
            lstHead.Add(Me.GetContentLayout("Len30", "ID"))
            lstHead.Add(Me.GetContentLayout("Len25", Me.mLanguage.GetString("App.Button.TimeSort")))
            lstHead.Add(Me.GetContentLayout("Len25", Me.mLanguage.GetString("Main.Button.Contact")))
            If Me.mExportSocialType <> ExportSocialType.WhatsApp Then
                If Me.mExportSocialType = ExportSocialType.QQ Then
                    lstHead.Add(Me.GetContentLayout("Len25", "QQ Id")) '"微信号"
                Else
                    lstHead.Add(Me.GetContentLayout("Len25", Me.mLanguage.GetString("Weixin.Lable.WeChatNumber"))) '"微信号"
                End If
            End If
            lstHead.Add(Me.GetContentLayout("Len20", Me.mLanguage.GetString("File.Label.Status")))
            lstHead.Add(Me.GetContentLayout("Len20", Me.mLanguage.GetString("Common.Label.ColumnType")))
            lstHead.Add(Me.GetContentLayout("Len2000", Me.mLanguage.GetString("Weixin.Lable.Message")))

            Dim dicVlues As Dictionary(Of Integer, List(Of ContentLayout)) = New Dictionary(Of Integer, List(Of ContentLayout))
            Dim iCount As Integer = 0
            Dim lstInfo As List(Of ContentLayout) = Nothing

            Dim intCProgress As Integer = 0

            For Each Item As SociaChatBase In lstChatInfo
                lstInfo = New List(Of ContentLayout)

                Dim strNickName = ""
                Dim strUsrNameToChat As String = ""
                If Me.mExportSocialType <> ExportSocialType.AndroidWeChat Then
                    strNickName = Me.GetNickName(Item, strUsrNameToChat)
                    If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                        strNickName = Me.GetNickName(Item, strUsrNameToChat)
                        strNickName = Item.StrNickName
                    ElseIf Me.mExportSocialType = ExportSocialType.QQ Then
                        If strNickName = Me.mUsrNameMyself Then
                            strNickName = Me.mLanguage.GetString("Weixin.Text.Me")
                        End If
                        If String.IsNullOrEmpty(strNickName) Then
                            strNickName = Me.GetNickName(Item.StrUsrName)
                        End If
                    End If
                Else
                    strNickName = Me.mAndroidWeixin.GetMemoName(Item.StrUsrName)
                    If String.IsNullOrEmpty(strNickName) Then
                        strNickName = Me.mAndroidWeixin.GetNickName(Item.StrUsrName)
                    End If
                End If


                Dim isChatRoom As Boolean = JudgeIsChatRoom(Item.StrUsrName)

                If isChatRoom Then
                    Dim MD5 As String = Me.GetFriendMD5(strUsrNameToChat)
                    Dim lstGroupFriendInfo As New List(Of WeChatFriendInfo)
                    WeixinImportOrExportHelper.GetGroupFriend(Path.Combine(Path.GetDirectoryName(Me.mWeixinMMDB.DbFile), "WCDB_Contact.sqlite"), Item.StrUsrName, Nothing, Me.mDictFriendInfo, lstGroupFriendInfo)
                    If lstGroupFriendInfo.Count > 0 Then
                        For Each wfi As WeChatFriendInfo In lstGroupFriendInfo
                            If wfi.Md5 = MD5 Then
                                strNickName = IIf(wfi.GroupNoteName.Length > 0, wfi.GroupNoteName, IIf(wfi.NickName.Length > 0, wfi.NickName, wfi.NoteName))
                                Exit For
                            End If
                        Next
                    End If
                End If

                'Dim strWeChatID As String = Item.UsrName
                'If IsChatRoom(Item.UsrName) Then
                '    strWeChatID = strUsrNameToChat
                'End If

                Dim wxid As String = Item.StrUsrName
                Dim strWeChatID As String = Item.StrUsrName

                If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                    isChatRoom = Item.IsWhatsAppChatRoom
                End If

                If isChatRoom Then
                    strWeChatID = strUsrNameToChat
                End If
                If Not Me.IsReceivedMsg(Item) Then
                    wxid = strUsrNameToChat
                    strWeChatID = strUsrNameToChat
                    strNickName = Me.mLanguage.GetString("Weixin.Text.Me")
                End If

                Dim strMsg As String = GetChatMsg(Item, ChatMsgType.ShowOnTxtFile, isChatRoom)
                lstInfo.Add(Me.GetContentLayout("Len30", wxid))
                lstInfo.Add(Me.GetContentLayout("Len25", Item.DTCreateTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))))
                lstInfo.Add(Me.GetContentLayout("Len25", strNickName))
                If Me.mExportSocialType <> ExportSocialType.WhatsApp Then
                    lstInfo.Add(Me.GetContentLayout("Len25", strWeChatID))
                End If
                If Me.mExportSocialType <> ExportSocialType.AndroidWeChat Then
                    lstInfo.Add(Me.GetContentLayout("Len20", IIf(IsReceivedMsg(Item), mLanguage.GetString("Backup.Label.Receive"), mLanguage.GetString("Backup.Label.Send"))))
                Else
                    lstInfo.Add(Me.GetContentLayout("Len20", IIf(Item.IntStatus = 4, mLanguage.GetString("Backup.Label.Receive"), mLanguage.GetString("Backup.Label.Send"))))
                End If
                lstInfo.Add(Me.GetContentLayout("Len20", GetChatType(Item)))
                lstInfo.Add(Me.GetContentLayout("Len2000", strMsg))

                dicVlues.Add(iCount, lstInfo)
                iCount = iCount + 1

                Me.DoRaiseNewProgressEvent(iCount, lstChatInfo.Count, "TXT")
            Next


            result = Me.ExportToTxtFile(lstHead, dicVlues, strFilePath, blnCopyToClipboard)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExportToTxtFile")
        End Try

        Return result
    End Function
    Private Function ExportToTxtFile(lstHead As List(Of ContentLayout), _
                                     ByVal dicVlues As Dictionary(Of Integer, List(Of ContentLayout)), ByVal strFilePath As String, ByVal blnCopyToClipboard As Boolean) As Boolean

        Dim result As Boolean = False

        Try
            Dim strLines As New StringBuilder
            If Not blnCopyToClipboard Then
                '"导出时间"  "微信消息记录"
                strLines.AppendLine(String.Format("{0}     {1}:{2}", mLanguage.GetString("Weixin.Lable.WeixinMsgRecord"), mLanguage.GetString("Backup.Label.ExportDate"), DateTime.Now.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))))
                strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")
            End If

            Dim strHead As String = ""
            For Each item As ContentLayout In lstHead
                strHead = strHead & Common.StringFormat(item.CLValue, item.CLLength, item.CLIsDoc, item.CLIsPad, item.CLPadRight, item.CLIsCut)
            Next
            strLines.AppendLine(strHead)
            strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")

            For Each iIndex As Integer In dicVlues.Keys
                Dim strValue As String = ""
                For Each info As ContentLayout In dicVlues(iIndex)
                    strValue = strValue & Common.StringFormat(info.CLValue, info.CLLength, info.CLIsDoc, info.CLIsPad, info.CLPadRight, info.CLIsCut)
                Next
                strLines.AppendLine(strValue)
            Next
            strLines.AppendLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")

            If blnCopyToClipboard Then
                CopyToClipboard(strLines.ToString())
                result = True
            Else
                File.WriteAllText(strFilePath, strLines.ToString(), System.Text.Encoding.UTF8) '导出韩文乱码
                result = True
            End If


        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExportToTxtFile")
        End Try
        Return result

    End Function
    Private Function ExportToExcelFileEx(ByVal lstChatInfo As List(Of SociaChatBase), ByVal strFilePath As String) As Boolean
        Dim result As Boolean = False
        If lstChatInfo Is Nothing Then
            Return False
        End If

        Dim strTempFilePath As String = strFilePath & ".Temp"
        Try
            If File.Exists(strTempFilePath) Then
                File.Delete(strTempFilePath)
            End If
        Catch
        End Try
        Dim strFileName As String = Path.GetFileNameWithoutExtension(strFilePath)

        Try
            '创建头部
            CreateExcelHead(strTempFilePath)

            '写入内容
            Dim strLineTd As String = "<td class=xl24  style='border-bottom:.5pt solid black;border-top:none;' x:str>{0}</td>"
            Dim strLineTr As String = "<tr style='mso-height-source:userset;height:25.00pt'>{0}</tr>"
            Dim strExcelContacts As New StringBuilder
            Dim strLine As StringBuilder = New StringBuilder()
            Dim intCount As Integer = 0
            Dim intExcelIndex As Integer = 1

            Dim intCProgress As Integer = 0

            For Each item As SociaChatBase In lstChatInfo
                strLine.Remove(0, strLine.Length)

                Dim strNickName = ""
                Dim strUsrNameToChat As String = ""
                If Me.mExportSocialType = ExportSocialType.QQ Then
                    strNickName = Me.GetNickName(item, strUsrNameToChat)
                    If strNickName = Me.mUsrNameMyself Then
                        strNickName = Me.mLanguage.GetString("Weixin.Text.Me")
                    End If
                    If String.IsNullOrEmpty(strNickName) Then
                        strNickName = Me.GetNickName(item.StrUsrName)
                    End If
                ElseIf Me.mExportSocialType = ExportSocialType.WeChat Then
                    strNickName = Me.GetNickName(item, strUsrNameToChat)
                ElseIf Me.mExportSocialType = ExportSocialType.WhatsApp Then
                    strNickName = Me.GetNickName(item, strUsrNameToChat)
                    strNickName = item.StrNickName
                Else
                    strNickName = Me.mAndroidWeixin.GetNickName(item.StrUsrName)
                End If

                Dim isChatRoom As Boolean = JudgeIsChatRoom(item.StrUsrName)

                If isChatRoom Then
                    Dim MD5 As String = Me.GetFriendMD5(strUsrNameToChat)
                    Dim lstGroupFriendInfo As New List(Of WeChatFriendInfo)
                    WeixinImportOrExportHelper.GetGroupFriend(Path.Combine(Path.GetDirectoryName(Me.mWeixinMMDB.DbFile), "WCDB_Contact.sqlite"), item.StrUsrName, Nothing, Me.mDictFriendInfo, lstGroupFriendInfo)
                    If lstGroupFriendInfo.Count > 0 Then
                        For Each wfi As WeChatFriendInfo In lstGroupFriendInfo
                            If wfi.Md5 = MD5 Then
                                strNickName = IIf(wfi.GroupNoteName.Length > 0, wfi.GroupNoteName, IIf(wfi.NickName.Length > 0, wfi.NickName, wfi.NoteName))
                                Exit For
                            End If
                        Next
                    End If
                End If

                Dim strMsg As String = Me.GetChatMsg(item, ChatMsgType.ShowOnExcelFile, isChatRoom)
                Dim strIsResiveOrSend As String = ""

                'If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                '    strIsResiveOrSend = IIf(item.IntDes = 1, Me.mLanguage.GetString("Backup.Label.Receive"), Me.mLanguage.GetString("Backup.Label.Send"))           '"接收" "发送"
                If Me.mExportSocialType <> ExportSocialType.AndroidWeChat Then
                    strIsResiveOrSend = IIf(Me.IsReceivedMsg(item), Me.mLanguage.GetString("Backup.Label.Receive"), Me.mLanguage.GetString("Backup.Label.Send"))    '"接收" "发送"
                Else
                    strIsResiveOrSend = IIf(item.IntStatus = 4, Me.mLanguage.GetString("Backup.Label.Receive"), Me.mLanguage.GetString("Backup.Label.Send"))           '"接收" "发送"
                End If

                Dim wxid As String = item.StrUsrName
                Dim strWeChatID As String = item.StrUsrName

                If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                    isChatRoom = item.IsWhatsAppChatRoom
                End If

                If isChatRoom Then
                    strWeChatID = strUsrNameToChat
                End If

                If Not Me.IsReceivedMsg(item) Then
                    wxid = strUsrNameToChat
                    strWeChatID = strUsrNameToChat
                    strNickName = Me.mLanguage.GetString("Weixin.Text.Me")
                End If


                strLine.Append(String.Format(strLineTd, wxid))
                strLine.Append(String.Format(strLineTd, item.DTCreateTime.ToString("yyyy-MM-dd HH:mm:ss")))
                strLine.Append(String.Format(strLineTd, Me.FormatExcelCellText(strNickName)))
                If Me.mExportSocialType <> ExportSocialType.WhatsApp Then
                    strLine.Append(String.Format(strLineTd, strWeChatID))
                End If
                strLine.Append(String.Format(strLineTd, strIsResiveOrSend))
                strLine.Append(String.Format(strLineTd, Me.GetChatType(item)))
                Dim strMessage As String = HttpUtility.HtmlEncode(Me.GetChatMsg(item, ChatMsgType.ShowOnExcelFile, isChatRoom))
                strLine.Append(String.Format(strLineTd, Me.FormatExcelCellText(strMessage)))
                'strLine.Append(String.Format(strLineTd, Me.FormatExcelCellText(strMsg)))

                strExcelContacts.AppendLine(String.Format(strLineTr, strLine.ToString()))

                If strExcelContacts.Length > 1000 Then
                    Me.WriteText(strTempFilePath, strExcelContacts.ToString())
                    strExcelContacts.Remove(0, strExcelContacts.Length)
                End If

                intCount = intCount + 1

                '如果一次内容写入的行数 超过65500 则需要在创建一个excel
                If intCount >= 65500 Then
                    If strExcelContacts.Length > 0 Then
                        Me.WriteText(strTempFilePath, strExcelContacts.ToString())
                        strExcelContacts.Remove(0, strExcelContacts.Length)
                    End If

                    '创建execl 尾部
                    CreateExcelFoot(strTempFilePath)
                    '保存 Excel
                    SaveCreateExce(strTempFilePath, strFilePath)

                    Dim strFileNewName As String = String.Format("{0}_{1}.xls", strFileName, intExcelIndex)
                    strFilePath = Path.Combine(Path.GetDirectoryName(strFilePath), strFileNewName)
                    strTempFilePath = strFilePath & ".Temp"
                    Try
                        If File.Exists(strTempFilePath) Then
                            File.Delete(strTempFilePath)
                        End If
                    Catch
                    End Try

                    '创建头部
                    CreateExcelHead(strTempFilePath)

                    intCount = 0
                    intExcelIndex = 1
                End If

                Me.DoRaiseNewProgressEvent(intExcelIndex, lstChatInfo.Count, "EXCEL")
                intExcelIndex = intExcelIndex + 1
            Next

            If strExcelContacts.Length > 0 Then
                Me.WriteText(strTempFilePath, strExcelContacts.ToString())
            End If

            '创建execl 尾部
            CreateExcelFoot(strTempFilePath)

            '保存 Excel
            SaveCreateExce(strTempFilePath, strFilePath)

            result = File.Exists(strFilePath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinImportOrExportHelper_ExportToExcelFileEx")
        End Try
        Return result
    End Function

    Private Sub CreateExcelHead(strTempFilePath As String)
        Dim strExcelContacts As New StringBuilder
        strExcelContacts.AppendLine("<html xmlns:v=""urn:schemas-microsoft-com:vml""")
        strExcelContacts.AppendLine("xmlns:o=""urn:schemas-microsoft-com:office:office""")
        strExcelContacts.AppendLine("xmlns:x=""urn:schemas-microsoft-com:office:excel""")
        strExcelContacts.AppendLine("xmlns=""http://www.w3.org/TR/REC-html40"">")

        Dim strExcelHeader As String = My.Resources.excel_header.Replace("<meta http-equiv=Content-Type content=""text/html; charset=utf8"">", String.Format("<meta http-equiv=Content-Type content=""text/html; charset={0}"">", IniSetting.GetWeChatExportExcelCoding))

        strExcelContacts.AppendLine(strExcelHeader)
        strExcelContacts.AppendLine("<body link=blue vlink=purple><table x:str border=0 cellpadding=0 cellspacing=0 width=1296 style='border-collapse:collapse;table-layout:fixed;width:972pt'>")

        Dim strTitleTr As String = "<tr style='mso-height-source:userset;height:30.00pt'>{0}</tr>"
        Dim strTitleTd As String = "<td rowspan=2 class=xl300  style='border-bottom:.5pt solid black;border-top:none;' >{0}</td>"
        Dim strLineTr As String = "<tr style='mso-height-source:userset;height:25.00pt'>{0}</tr>"

        Dim strTitle As New StringBuilder
        strTitle.Append(String.Format(strTitleTd, "ID"))
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("App.Button.TimeSort")))       '时间
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Main.Button.Contact")))       '联系人
        If Me.mExportSocialType <> ExportSocialType.WhatsApp Then
            If Me.mExportSocialType = ExportSocialType.QQ Then
                strTitle.Append(String.Format(strTitleTd, "QQ Id")) '"微信号"
            Else
                strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Weixin.Lable.WeChatNumber"))) '"微信号"
            End If
        End If
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("File.Label.Status")))       '状态
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Common.Label.ColumnType")))   '类型
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Weixin.Lable.Message")))     '消息
        strExcelContacts.AppendLine(String.Format(strTitleTr, strTitle))
        strExcelContacts.AppendLine(String.Format(strLineTr, ""))

        Me.WriteText(strTempFilePath, strExcelContacts.ToString())
    End Sub

    Private Sub CreateExcelFoot(strTempFilePath As String)
        Me.WriteText(strTempFilePath, "</table></body></html>")
    End Sub

    Private Sub SaveCreateExce(strTempFilePath As String, strFilePath As String)
        Try
            If File.Exists(strFilePath) Then
                File.Delete(strFilePath)
            End If
        Catch
        End Try
        Try
            File.Move(strTempFilePath, strFilePath)
        Catch
        End Try
    End Sub

    Private Sub WriteText(strTempFilePath As String, strExcelContacts As String)
        Try
            Dim myByte As Byte() = System.Text.Encoding.Default.GetBytes(strExcelContacts) '导出韩文乱码
            Using fsWrite As New FileStream(strTempFilePath, FileMode.Append)
                fsWrite.Write(myByte, 0, myByte.Length)
            End Using
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WriteText")
        End Try
    End Sub

#End Region

#Region "--- 格式 ---"
    Private Function GetContentLayout(ByVal strName As String, Optional ByVal strValue As String = "") As ContentLayout
        Dim info As ContentLayout = New ContentLayout()

        If Me.mLstContentLayout.Count = 0 Then
            Me.CreateContentLayout()
        End If

        For Each item As ContentLayout In Me.mLstContentLayout
            If item.CLName = strName Then
                info.CLIsCut = item.CLIsCut
                info.CLIsDoc = item.CLIsDoc
                info.CLIsPad = item.CLIsPad
                info.CLLength = item.CLLength
                info.CLName = item.CLName
                info.CLPadRight = item.CLPadRight
                Exit For
            End If
        Next

        info.CLValue = strValue

        Return info
    End Function

    Private Sub CreateContentLayout()
        Me.mLstContentLayout = New List(Of ContentLayout)

        Dim info As ContentLayout = New ContentLayout()
        info.CLName = "Len30"
        info.CLLength = 30
        info.CLIsDoc = False
        info.CLIsPad = True
        info.CLPadRight = True
        info.CLIsCut = False
        Me.mLstContentLayout.Add(info)

        info = New ContentLayout()
        info.CLName = "Len25"
        info.CLLength = 25
        info.CLIsDoc = False
        info.CLIsPad = True
        info.CLPadRight = True
        info.CLIsCut = False
        Me.mLstContentLayout.Add(info)


        info = New ContentLayout()
        info.CLName = "Len20"
        info.CLLength = 20
        info.CLIsDoc = False
        info.CLIsPad = True
        info.CLPadRight = True
        info.CLIsCut = False
        Me.mLstContentLayout.Add(info)

        info = New ContentLayout()
        info.CLName = "Len2000"
        info.CLLength = 2000
        info.CLIsDoc = False
        info.CLIsPad = False
        info.CLPadRight = True
        info.CLIsCut = False
        Me.mLstContentLayout.Add(info)
    End Sub

#End Region

#Region "--- 私有方法 ---"
    Private Sub CopyToClipboard(ByVal strContent As String)
        Try
            Clipboard.SetText(strContent.TrimEnd())
            RaiseEvent ExportProgressEventHandler(Me, New ExportProgressEventArgs(ExportStatus.CopySuccess, Me.mLanguage.GetString("Welcome.Message.CopySucceed"))) '复制成功
        Catch
            RaiseEvent ExportProgressEventHandler(Me, New ExportProgressEventArgs(ExportStatus.CopyFailure, Me.mLanguage.GetString("Weixin.Message.CopyFail"))) '"复制失败！"
        End Try
    End Sub

    Private Function GetChatMsg(ByVal Chat As SociaChatBase, ByVal type As ChatMsgType, ByVal isChatRoom As Boolean) As String
        Dim strText As String = String.Empty

        Select Case Chat.SCType
            Case SociaChatType.Text  '"1"
                strText = GetChatMsgForText(Chat, type)
            Case SociaChatType.Picture  '"3"
                strText = "[" & mLanguage.GetString("Main.Button.Photo") & "]" '"[图片]"
            Case SociaChatType.Voice '"34"
                strText = "[" & mLanguage.GetString("Weixin.Item.Audio") & "]" '"[语音]"
            Case SociaChatType.NameCard '"42"
                strText = "[" & mLanguage.GetString("Weixin.Item.BusinessCard") & "]" '"[名片]"
                strText += Chat.StrMessage
            Case SociaChatType.Video '"43"
                strText = "[" & mLanguage.GetString("Media.Type.Video") & "]" '"[视频]"
            Case SociaChatType.SpecialExpression  '"47"
                strText = "[" & mLanguage.GetString("Weixin.Item.Expression") & "]" ' "[动画表情]"
            Case SociaChatType.Location '"48"
                strText = "[" & mLanguage.GetString("Weixin.Item.Location") & "]" '"[地理位置]"
                strText += Chat.StrMessage
            Case SociaChatType.webpage '"49"
                strText = GetChatMsgForWebPage(Chat, type)
            Case SociaChatType.VideoConnected ' "50"
                strText = "[" & mLanguage.GetString("Weixin.Item.Video") & "]" '"[视频聊天]"
            Case SociaChatType.SmallVideo '"62"
                strText = "[" & mLanguage.GetString("Weixin.Lable.SmallVideo") & "]" '"[视频聊天]"
            Case SociaChatType.SystemMessages '"10000"
            Case SociaChatType.SystemMessagesEx '"10000"
                strText = Chat.StrMessage
            Case SociaChatType.SystemMessagesNew
                Dim strMsg As String = Chat.StrMessage
                If Me.IsReceivedMsg(Chat) AndAlso isChatRoom Then
                    Dim values As String() = strMsg.Split(New Char() {vbCr, vbLf})
                    If values.Length > 1 Then
                        Dim strMsgTemp As String = String.Empty
                        For index As Integer = 1 To values.Length - 1
                            If String.IsNullOrEmpty(strMsgTemp) Then
                                strMsgTemp = values(index)
                            Else
                                strMsgTemp = strMsgTemp & vbCrLf & values(index)
                            End If
                        Next
                        If strMsgTemp.Length > 0 Then
                            strMsg = strMsgTemp
                        End If
                    End If
                End If

                strText = frmWeixin.GetSystemMessagesNew(strMsg)
            Case SociaChatType.Linkman      '系统消息
                strText = Chat.StrMessage
            Case SociaChatType.File
                Dim strFileName() As String = Chat.StrMessage.Split("|")
                If strFileName IsNot Nothing AndAlso strFileName.Length > 0 Then
                    strText = String.Format("[{0}]", mLanguage.GetString("WeChat.Lable.File")) & strFileName(0)
                Else
                    strText = String.Format("[{0}]", mLanguage.GetString("WeChat.Lable.File")) & Chat.StrMessage
                End If
            Case SociaChatType.SystemMessagesNewEx
                Dim jObj As JsonObject = JsonParser.ParseString(Chat.StrMessage)
                If jObj IsNot Nothing Then
                    If jObj.ContainsKey("msgContent") Then
                        strText = CType(jObj("msgContent"), JsonString).Value
                    End If
                End If
        End Select

        Return strText
    End Function

    '当类型为文本时，获取其内容
    Private Function GetChatMsgForText(ByVal chat As SociaChatBase, ByVal type As ChatMsgType) As String
        Dim strText As String = chat.StrMessage

        Dim isChatRoom As Boolean = JudgeIsChatRoom(chat.StrUsrName)
        If Me.mExportSocialType = ExportSocialType.WhatsApp Then
            isChatRoom = chat.IsWhatsAppChatRoom
        End If

        Select Case type
            Case ChatMsgType.ShowOnList
                If isChatRoom Then
                    Dim arr As String() = chat.StrMessage.Split(New Char() {vbCr, vbLf})

                    If arr.Length > 1 Then
                        Dim strNickName As String = GetNickName(arr(0).TrimEnd(":"))
                        Dim strMsg As String = arr(1)

                        strText = String.Format("{0}:{1}", strNickName, strMsg)
                    End If

                Else
                    Dim arr As String() = chat.StrMessage.Split(New Char() {vbCr, vbLf})
                    strText = arr(0)
                End If

            Case ChatMsgType.ShowOnTxtFile, ChatMsgType.ShowOnExcelFile
                If isChatRoom Then
                    Dim arr As String() = chat.StrMessage.Split(New Char() {vbCr, vbLf})

                    If arr.Length > 1 Then
                        Dim strMsgTemp As String = String.Empty
                        '第一个值为用户名，不显示
                        For index As Integer = 1 To arr.Length - 1
                            strMsgTemp = strMsgTemp & " " & arr(index)
                        Next

                        strText = strMsgTemp.TrimStart(" ")
                    End If

                    If strText.Length = 0 Then
                        strText = chat.StrMessage
                    End If

                Else
                    Dim arr As String() = chat.StrMessage.Split(New Char() {vbCr, vbLf})

                    If arr.Length > 1 Then
                        Dim strMsgTemp As String = String.Empty
                        For index As Integer = 0 To arr.Length - 1
                            strMsgTemp = strMsgTemp & " " & arr(index)
                        Next

                        strText = strMsgTemp.TrimStart(" ")
                    End If
                End If

            Case ChatMsgType.ShowOnWeb
                If isChatRoom Then
                    Dim arr As String() = chat.StrMessage.Split(New Char() {vbCr, vbLf})

                    If arr.Length > 1 Then
                        Dim strMsgTemp As String = String.Empty
                        '第一个值为用户名，不显示
                        For index As Integer = 1 To arr.Length - 1
                            strMsgTemp = strMsgTemp & vbCrLf & arr(index)
                        Next

                        strText = strMsgTemp.TrimStart(vbCrLf)
                    End If

                Else
                    Dim arr As String() = chat.StrMessage.Split(New Char() {vbCr, vbLf})

                    If arr.Length > 1 Then
                        Dim strMsgTemp As String = String.Empty
                        For index As Integer = 0 To arr.Length - 1
                            strMsgTemp = strMsgTemp & vbCrLf & arr(index)
                        Next

                        strText = strMsgTemp.TrimStart(vbCrLf)
                    End If
                End If

        End Select

        Return strText
    End Function

    Private Function JudgeIsChatRoom(ByVal usrName As String) As Boolean
        Dim result As Boolean = False
        Try
            If Me.mExportSocialType <> ExportSocialType.AndroidWeChat Then
                Dim item As WeChatFriendInfo = Nothing

                If mDictFriendInfo IsNot Nothing Then
                    If mDictFriendInfo.ContainsKey(usrName) Then
                        item = mDictFriendInfo(usrName)
                    End If
                End If

                If item IsNot Nothing AndAlso item.StrChatRoomMem.TrimEnd().Length > 0 Then
                    result = True
                End If
            Else
                result = Me.mAndroidWeixin.IsChatRoom(usrName)
            End If
        Catch ex As Exception

        End Try
        Return result
    End Function

    Private Function GetNickName(ByVal UsrName As String) As String
        Dim strNickName As String = String.Empty
        If Me.mExportSocialType <> ExportSocialType.AndroidWeChat Then
            If mDictFriendInfo IsNot Nothing Then
                If mDictFriendInfo.ContainsKey(UsrName) Then
                    Dim item As SocialFriendInfoBase = mDictFriendInfo(UsrName)

                    strNickName = IIf(item.NoteName.Length > 0, item.NoteName, item.NickName)
                End If
            ElseIf Me.mDictQQFriendInfo IsNot Nothing Then
                If mDictQQFriendInfo.ContainsKey(UsrName) Then
                    Dim item As SocialFriendInfoBase = mDictQQFriendInfo(UsrName)
                    strNickName = IIf(item.NoteName.Length > 0, item.NoteName, item.NickName)
                End If
            End If

            If String.IsNullOrEmpty(strNickName) Then
                strNickName = UsrName
            End If
        Else

            strNickName = Me.mAndroidWeixin.GetNickName(UsrName)
        End If

        Return strNickName
    End Function

    Private Function GetWeChatNum(ByVal UsrName As String) As String
        Dim strWeChatNum As String = String.Empty
        If Me.mExportSocialType <> ExportSocialType.AndroidWeChat Then
            If mDictFriendInfo IsNot Nothing Then
                If mDictFriendInfo.ContainsKey(UsrName) Then
                    Dim item As WeChatFriendInfo = mDictFriendInfo(UsrName)
                    strWeChatNum = item.WeChatNum
                End If
            End If

            If String.IsNullOrEmpty(strWeChatNum) Then
                strWeChatNum = UsrName
            End If
        End If
        If strWeChatNum.Length = 0 Then
            strWeChatNum = UsrName
        End If

        Return strWeChatNum
    End Function

    Private Function GetNickName(ByVal chat As SociaChatBase, Optional ByRef strUsrNameToChat As String = "") As String
        Dim strUsrName As String = String.Empty
        Dim blnChatRoom As Boolean = False
        Try
            If Not IsReceivedMsg(chat) Then
                '发送的消息
                strUsrName = mUsrNameMyself
            Else
                '接收的消息

                If chat.StrUsrName = mStrUnknown Then
                    blnChatRoom = False
                Else
                    blnChatRoom = JudgeIsChatRoom(chat.StrUsrName)
                    If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                        blnChatRoom = chat.IsWhatsAppChatRoom
                    End If
                End If

                If Not blnChatRoom Then
                    strUsrName = chat.StrUsrName
                Else

                    If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                        strUsrName = chat.StrNickName

                    Else
                        Select Case chat.SCType
                            Case SociaChatType.Video, SociaChatType.SmallVideo '"43", "62" '<msg><videomsg clientmsgid="34" playlength="220" length="14883363" type="44" status="0" fromusername="yan868440" aeskey="6f9dd20a9a8f4663b4580cc59cbcbcbc" cdnvideourl="304b0201000444304202010002046be4fc8002032dd14d0204a9fc4e7d020458467ab60420323630303732323436354063686174726f6f6d34365f313438313031333934310201000201000400" cdnthumburl="304b0201000444304202010002046be4fc8002032dd14d0204a9fc4e7d020458467ab60420323630303732323436354063686174726f6f6d34365f313438313031333934310201000201000400" cdnthumblength="3531" cdnthumbwidth="224" cdnthumbheight="398" cdnthumbaeskey="6f9dd20a9a8f4663b4580cc59cbcbcbc" encryver="1" /></msg>
                                Try
                                    If strUsrName.Length = 0 Then
                                        Dim strKey As String = "fromusername="""
                                        Dim iStart As Integer = chat.StrMessage.IndexOf(strKey)
                                        iStart = iStart + strKey.Length
                                        If iStart > 0 AndAlso iStart < chat.StrMessage.Length - 1 Then
                                            Dim iEnd As Integer = chat.StrMessage.IndexOf("""", iStart)
                                            If (iEnd > iStart) Then
                                                strUsrName = chat.StrMessage.Substring(iStart, iEnd - iStart)
                                            End If
                                        End If
                                    End If
                                Catch ex As Exception
                                End Try

                            Case SociaChatType.SystemMessages '"10000"
                                strUsrName = "10000"

                            Case Else '"1", "3", "34", "42", "47", "49", "48", "50"
                                Dim values As String() = chat.StrMessage.Split(New Char() {vbCr, vbLf})
                                If values.Length > 1 Then
                                    If values(0).Contains(":") Then
                                        strUsrName = values(0).TrimEnd(":")
                                    End If
                                End If

                        End Select
                    End If
                End If

            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetNickName")
        End Try
        strUsrNameToChat = strUsrName
        If Me.mExportSocialType <> ExportSocialType.AndroidWeChat AndAlso Not blnChatRoom Then
            strUsrNameToChat = GetWeChatNum(mUsrNameMyself)
        End If
        Dim strNickName As String = GetNickName(strUsrName)
        Return strNickName
    End Function

    '消息的状态是否为接收
    Private Function IsReceivedMsg(ByVal chat As SociaChatBase) As Boolean
        Dim blnReceive As Boolean = True

        If Me.mExportSocialType = ExportSocialType.WhatsApp Then
            blnReceive = IIf(chat.IntDes = 1, False, True)

        ElseIf Me.mExportSocialType = ExportSocialType.QQ Then
            blnReceive = IIf(chat.IntDes = 1, False, True)
            If String.Compare(chat.StrUsrName, Me.mUsrNameMyself) = 0 Then
                blnReceive = False
            End If

        Else
            If String.Compare(chat.StrUsrName, Me.mUsrNameMyself) = 0 OrElse String.Compare(chat.IntDes, "0") = 0 Then
                blnReceive = False
            End If

        End If
        Return blnReceive
    End Function

    '当类型为网页时，获取其内容
    Private Function GetChatMsgForWebPage(ByVal chat As SociaChatBase, ByVal type As ChatMsgType) As String
        Dim strText As String = String.Empty

        If type = ChatMsgType.ShowOnList Then
            strText = "[" & mLanguage.GetString("Weixin.Item.WebPage") & "]" '"[网页]"
        Else
            Dim strMsgForWeb As String = chat.StrMessage
            Dim arrHtml5ContentForWeb As JsonArray = GetWebPageJsonArray(strMsgForWeb) '网页json

            '兼容链接（只有title和url）
            If arrHtml5ContentForWeb Is Nothing OrElse arrHtml5ContentForWeb.Count = 0 Then
                arrHtml5ContentForWeb = GetWebLinkJsonArray(strMsgForWeb)
            End If

            If arrHtml5ContentForWeb IsNot Nothing AndAlso arrHtml5ContentForWeb.Count > 0 Then
                For Each Item As JsonObject In arrHtml5ContentForWeb
                    Try
                        Dim strTitle As String = CType(Item("title"), JsonString).Value
                        Dim strUrl As String = CType(Item("url"), JsonString).Value
                        If Not String.IsNullOrEmpty(strTitle) AndAlso Not String.IsNullOrEmpty(strUrl) Then
                            Dim strSeparator As String = vbCrLf
                            If type = ChatMsgType.ShowOnTxtFile Then
                                strSeparator = strSeparator & GetSpaces(90)
                            End If

                            If String.IsNullOrEmpty(strText) Then
                                strText = strTitle & strSeparator & strUrl
                            Else
                                strText = strText & strSeparator & strTitle & strSeparator & strUrl
                            End If
                        Else
                            Dim strType As String = ""
                            Dim strTemp As String = frmWeixin.GetSystemMessagesNew(strMsgForWeb, strType)
                            If strType <> "" Then
                                strText = strTemp
                            Else
                                strText = "[" & mLanguage.GetString("Weixin.Item.Expression") & "]"  '"[动画表情]"
                            End If
                        End If
                    Catch
                    End Try
                Next
            Else
                Dim strMsg As String = chat.StrMessage
                Dim isChatRoom As Boolean = Me.JudgeIsChatRoom(chat.StrUsrName)
                If Me.IsReceivedMsg(chat) AndAlso isChatRoom Then
                    Dim values As String() = strMsg.Split(New Char() {vbCr, vbLf})
                    If values.Length > 1 Then
                        Dim strMsgTemp As String = String.Empty
                        For index As Integer = 1 To values.Length - 1
                            If String.IsNullOrEmpty(strMsgTemp) Then
                                strMsgTemp = values(index)
                            Else
                                strMsgTemp = strMsgTemp & vbCrLf & values(index)
                            End If
                        Next
                        If strMsgTemp.Length > 0 Then
                            strMsg = strMsgTemp
                        End If
                    End If
                End If

                Dim strType As String = ""
                Dim strTemp As String = frmWeixin.GetSystemMessagesNew(strMsg, strType)
                If strType <> "" Then
                    strText = strTemp
                End If
            End If
        End If

        If String.IsNullOrEmpty(strText) Then
            strText = chat.StrMessage
        End If

        Return strText
    End Function

    '获取链接的正则方法
    Private Function GetWebLinkJsonArray(ByVal strContent As String) As JsonArray
        'Dim regular As String = "<title>(?<title>[\w\W]*?)</title>[\w\W]*?<url>(?<url>[\w\W]*?)</url>"
        Dim regular As String = "<title>(?<title>[\w\W]*?)</title>[\w\W]*?<des>(?<des>[\w\W]*?)</des>[\w\W]*?<url>(?<url>[\w\W]*?)</url>"
        Return GetWebPageJsonArray(strContent, regular)
    End Function

    Private Function GetWebPageJsonArray(ByVal strContent As String) As JsonArray
        '.*?代表任意字符（不包括换行及特殊字符）
        '[\w\W]*?代表任意字符（包括换行及特殊字符）
        'Dim regular As String = "<title>(?<title>.*?)</title>[\w\W]*?<url>(?<url>.*?)</url>[\w\W]*?<pub_time>(?<pub_time>.*?)</pub_time>[\w\W]*?<cover>(?<cover>.*?)</cover>[\w\W]*?<digest>(?<digest>.*?)</digest>"
        Dim regular As String = "<title>(?<title>[\w\W]*?)</title>[\w\W]*?<url>(?<url>[\w\W]*?)</url>[\w\W]*?<pub_time>(?<pub_time>[\w\W]*?)</pub_time>[\w\W]*?<cover>(?<cover>[\w\W]*?)</cover>[\w\W]*?<digest>(?<digest>[\w\W]*?)</digest>"

        '如果服务器有设置值则使用服务上的值
        If Not String.IsNullOrEmpty(mRegular) Then
            regular = mRegular
        End If

        Return GetWebPageJsonArray(strContent, regular)
    End Function

    '提取网页内容的正则方法（需优化兼容更多网页）
    Private Function GetWebPageJsonArray(ByVal strContent As String, ByVal regular As String) As JsonArray
        Dim arrWebPages As New JsonArray
        Dim dicWebPage As JsonObject

        Dim dt As DataTable = Utility.GetMatchStringByRegularExpressions(strContent, New String(0) {regular}, New String(5) {"title", "des", "url", "pub_time", "cover", "digest"})

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Try
                    dicWebPage = New JsonObject()
                    '标题
                    Dim strTitle As String = FormatStringForWebPage(HttpUtility.UrlDecode(dr("title").ToString()))
                    dicWebPage.Add("title", strTitle)

                    '网页url
                    Dim strUrl As String = FormatStringForWebPage(HttpUtility.UrlDecode(dr("url").ToString()))
                    dicWebPage.Add("url", strUrl)

                    '发布时间
                    Dim strPubTime As String = FormatStringForWebPage(HttpUtility.UrlDecode(dr("pub_time").ToString()))
                    Dim PubTime As DateTime = DateTime.MinValue

                    Try
                        If Not String.IsNullOrEmpty(strPubTime) Then
                            Dim longPubTime As Long = Long.Parse(strPubTime)
                            PubTime = WeixinHelper.ConvertWeixinToPcTime(longPubTime)
                        End If
                    Catch
                    End Try

                    If PubTime = DateTime.MinValue Then
                        strPubTime = String.Empty
                    Else
                        strPubTime = PubTime.ToString("MM-dd")
                    End If

                    dicWebPage.Add("pub_time", strPubTime)

                    '封面地址
                    Dim strCover As String = FormatStringForWebPage(HttpUtility.UrlDecode(dr("cover").ToString()))
                    dicWebPage.Add("cover", strCover)

                    '摘要
                    Dim strDigest As String = FormatStringForWebPage(HttpUtility.UrlDecode(dr("digest").ToString()))
                    dicWebPage.Add("digest", strDigest)

                    Dim strDes As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("des").ToString()))
                    dicWebPage.Add("des", strDes)

                    arrWebPages.Add(dicWebPage)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "GetWebPageJsonArray")
                End Try
            Next
        Else
            'Common.LogException(strContent, "无法兼容的网页")
        End If

        Return arrWebPages
    End Function

    Private Function FormatStringForWebPage(ByVal strContent As String)
        Dim value As String = strContent

        If Not String.IsNullOrEmpty(value) Then
            If value.StartsWith("<![CDATA[") AndAlso value.EndsWith("]]>") Then

                value = value.TrimStart(New Char() {"<"c, "!"c, "["c, "C"c, "D"c, "A"c, "T"c, "A"c, "["c})
                value = value.TrimEnd(New Char() {"]"c, "]"c, ">"c})

            End If
        End If

        Return value
    End Function

    Private Function GetSpaces(ByVal count As String) As String
        Dim strSpaces As String = ""

        For index As Integer = 1 To count
            strSpaces = strSpaces & " "
        Next

        Return strSpaces
    End Function

    Private Function GetChatType(ByVal Chat As SociaChatBase) As String
        Dim strText As String = String.Empty

        Select Case Chat.SCType
            Case SociaChatType.Text '"1"
                strText = mLanguage.GetString("Weixin.Item.Text") '"文本"
            Case SociaChatType.Picture '"3"
                strText = mLanguage.GetString("Main.Button.Photo") '"图片"
            Case SociaChatType.Voice '"34"
                strText = mLanguage.GetString("Weixin.Item.Audio") '"语音"
            Case SociaChatType.NameCard '"42"
                strText = mLanguage.GetString("Weixin.Item.BusinessCard") '"名片"
            Case SociaChatType.Video '"43"
                strText = mLanguage.GetString("Media.Type.Video") '"视频"
            Case SociaChatType.SpecialExpression '"47"
                strText = mLanguage.GetString("Weixin.Item.Expression") '"动画表情"
            Case SociaChatType.Location '"48"
                strText = mLanguage.GetString("Weixin.Item.Location") '"地理位置"
            Case SociaChatType.webpage '"49"
                strText = mLanguage.GetString("Weixin.Item.WebPage") '"网页"
            Case SociaChatType.VideoConnected '"50"
                strText = mLanguage.GetString("Weixin.Item.Video") '"视频聊天"
            Case SociaChatType.SmallVideo '"62"
                strText = mLanguage.GetString("Weixin.Lable.SmallVideo") '"小视频"
            Case SociaChatType.SystemMessages '"10000"
                strText = mLanguage.GetString("Weixin.Item.SystemInfo") '"系统消息"
            Case SociaChatType.Linkman
                strText = mLanguage.GetString("Weixin.Item.SystemInfo") '"系统消息"
            Case SociaChatType.StartChat
                strText = mLanguage.GetString("Weixin.Item.SystemInfo") '"系统消息"
            Case SociaChatType.File
                strText = mLanguage.GetString("WeChat.Lable.File")  '文件
        End Select

        Return strText
    End Function

    '检测是否安装Excel
    'Private Sub CheckIsInstallExcel()
    '    Try
    '        If Me.mThreadIsInstallExcel IsNot Nothing AndAlso Me.mThreadIsInstallExcel.ThreadState <> ThreadState.Stopped Then
    '            Me.mThreadIsInstallExcel.Abort()
    '        End If
    '    Catch
    '    End Try

    '    Me.mThreadIsInstallExcel = New Thread(AddressOf CheckIsInstallExcelInThread)
    '    With Me.mThreadIsInstallExcel
    '        .IsBackground = True
    '        .Start()
    '    End With
    'End Sub

    'Private Sub CheckIsInstallExcelInThread()
    '    Try
    '        Dim xlApp As Object = CreateObject("Excel.Application")

    '        If xlApp Is Nothing Then
    '            Me.mBlnInstallExcel = False
    '        Else
    '            Me.mBlnInstallExcel = True
    '            xlApp.Quit()
    '        End If
    '    Catch
    '        Me.mBlnInstallExcel = False
    '    End Try
    'End Sub

    '导出Excel时，如果字符串以==或= =开始，导出会异常。
    Private Function FormatExcelCellText(ByVal strText As String) As String
        Dim result As String = strText
        Dim strTextTemp As String = strText

        If String.IsNullOrEmpty(strText) OrElse Not strTextTemp.StartsWith("=") Then
            GoTo DO_EIXT
        End If

        strTextTemp = strText.Replace(" ", "")

        If Not strTextTemp.StartsWith("==") Then
            GoTo DO_EIXT
        End If

        result = "'" & strText

DO_EIXT:
        Return result
    End Function

#End Region

#Region "--- 导出媒体文件 ---"
    'Public Sub ExportMedia(lstRows As List(Of DataGridViewRow), lstTime As List(Of Date), strDeviceName As String, strType As String, strWeCahtUsers As String, obj As Object())
    Public Function ExportMedia(lstRows As List(Of DataGridViewRow), lstTime As List(Of Date), strDeviceName As String, strType As String, strWeCahtUsers As String, obj As Object(), ByVal dicDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), Optional ByVal strGroup_Wxid As String = "") As String
        Return ExportMedia(lstRows, lstTime, strDeviceName, strType, strWeCahtUsers, obj, "", Nothing, dicDelChatInfo, strGroup_Wxid)
    End Function

    Public Function ExportMedia(lstRows As List(Of DataGridViewRow), lstTime As List(Of Date), strDeviceName As String, strType As String, strWeCahtUsers As String, obj As Object(), ByVal strQQNum As String, ByVal BFHelper As BackupFolderHelper, ByVal dicDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), Optional ByVal strGroup_Wxid As String = "") As String
        If Me.mThreadExportHtml IsNot Nothing AndAlso Me.mThreadExportHtml.ThreadState <> ThreadState.Stopped Then
            Try
                Me.mThreadExportHtml.Abort()
            Catch
            End Try
        End If

        Dim strPath As String = ""
        If Not Me.SetParameter(strPath, lstTime, strDeviceName, strWeCahtUsers, obj, DRAppName.Defaults, strGroup_Wxid) Then
            Return strPath
        End If

        Dim objPara(9) As Object
        objPara(0) = strPath
        objPara(1) = lstRows
        objPara(2) = lstTime
        objPara(3) = strDeviceName
        objPara(4) = strType
        objPara(5) = strQQNum
        objPara(6) = BFHelper
        objPara(7) = strGroup_Wxid
        objPara(8) = dicDelChatInfo

        Me.mThreadExportHtml = New Thread(New ParameterizedThreadStart(AddressOf DoExportMedia))
        With Me.mThreadExportHtml
            .IsBackground = True
            .SetApartmentState(ApartmentState.STA)
            .Start(objPara)
        End With
        Return strPath
    End Function

    Private Sub DoExportMedia(ByVal objPara As Object)
        Try
            Utility.WaitSeconds(0.5)
            Dim strFilePath As String = objPara(0).ToString()
            Dim lstRows As List(Of DataGridViewRow) = CType(objPara(1), List(Of DataGridViewRow))
            Dim lstTime As List(Of DateTime) = CType(objPara(2), List(Of DateTime))
            Dim strWeChatUserName As String = objPara(3).ToString()
            Dim strType As String = objPara(4).ToString()
            Dim strQQNum As String = objPara(5).ToString()
            Dim BFHelper As BackupFolderHelper = CType(objPara(6), BackupFolderHelper)
            Dim strGroup_Wxid As String = objPara(7).ToString()
            Dim dicDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)) = objPara(8)


            Dim fInfo As SocialFriendInfoBase = Nothing
            Dim wAppInfo As WhatsAppSession = Nothing
            Dim strNickName As String = ""
            Dim intCurrentIndex As Integer = 0
            Dim intProgress As Integer = 0

            Dim WCFInfo As WeChatFriendInfo = Nothing

            Dim strSavePath As String = Path.Combine(strFilePath, Me.mLanguage.GetString("Weixin.Lable.MediaFile")) '"媒体文件"           
            Folder.CheckFolder(strSavePath)
            For Each Row As DataGridViewRow In lstRows
                Try
                    If TypeOf Row.Tag Is WhatsAppSession Then
                        wAppInfo = CType(Row.Tag, WhatsAppSession)
                        fInfo = ChangeFriendInfoToFriendInfoBase(Nothing, Nothing, wAppInfo, mExportSocialType)
                    Else
                        fInfo = CType(Row.Tag, SocialFriendInfoBase)
                    End If
                    strNickName = Me.GetNickName(fInfo.UsrName)
                    If mExportSocialType = ExportSocialType.WhatsApp Then
                        strNickName = wAppInfo.ChatName
                    End If
                    strNickName = Utility.ReplaceWinIllegalName(strNickName).Trim()

                    intCurrentIndex = intCurrentIndex + 1
                    mLstCurrentExportInfo = New List(Of String)
                    mLstCurrentExportInfo.Add(strNickName)
                    mLstCurrentExportInfo.Add(intCurrentIndex.ToString())
                    mLstCurrentExportInfo.Add(lstRows.Count.ToString())
                    mLstCurrentExportInfo.Add(strFilePath)

                    Dim strAttachmentPath As String = Path.Combine(strSavePath, strNickName)
                    Dim lstFriendInfo As New List(Of SocialFriendInfoBase)
                    lstFriendInfo.Add(fInfo)

                    WCFInfo = CType(Row.Tag, WeChatFriendInfo)
                    Dim obj As Object() = New Object() {lstFriendInfo, strAttachmentPath, lstTime, "Html", dicDelChatInfo, WCFInfo}

                    Dim lstInfos As New List(Of QQChatInfo)
                    If strQQNum.Length > 0 Then
                        Dim qInfo As QQFriendInfo = CType(Row.Tag, QQFriendInfo)
                        lstInfos = mQQDB.Get_Chat_Info_ByTime_All(qInfo.StrQQNum, lstTime, qInfo.IsGroup, qInfo.IsDiscuss)
                    End If

                    Select Case strType
                        Case "Picture"
                            '导出图片
                            Me.DoExportPhotoByUserId(obj, strQQNum, BFHelper, lstInfos, strGroup_Wxid)

                        Case "Audio"
                            '导出语言
                            Me.DoExportAudioByUserId(obj, True, strQQNum, BFHelper, lstInfos, strGroup_Wxid)

                        Case "Video"
                            '导出小视频
                            Me.DoExportVideoByUserId(obj, strQQNum, BFHelper, lstInfos, strGroup_Wxid)

                    End Select
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoExportMedia1")
                End Try
            Next
            RaiseEvent ExportProgressEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportSuccess, Me.mLanguage.GetString("Note.Message.ExportSucceed"))) '导出成功
            RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportSuccess, Me.mLanguage.GetString("Note.Message.ExportSucceed")))
            'Common.OpenExplorer(strSavePath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportMedia")
            RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportFailure, Me.mLanguage.GetString("Contact.Message.ExportFailure"), 100, "")) '导出失败
        End Try
    End Sub

#End Region

#Region "--- 导出HTML ---"

    Public Sub SetFields(obj As Object())
        Try
            If obj IsNot Nothing Then
                Me.mPathContainer = obj(0)
                Me.mUIFileSharingEnabled = obj(1)
                Me.mAfcHandler = obj(2)
                Me.mIsWeixinBackup = obj(3)
                Me.mIsWeixinAloneBackup = obj(4)
                Me.mWeChatSKU = obj(5)
                Me.mDictSha1 = obj(6)
                Me.mWeixinBackupCache = obj(7)
                Me.mPathWeixinOnPhone = obj(8)
                Me.mPathWeixinOnPC_Temp = obj(9)
                Me.mListDir = obj(10)
                Me.mLstMBFileRecord = obj(11)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetObj")
        End Try
    End Sub

    Private Function SetParameter(ByRef strPath As String, lstT As List(Of Date), strDName As String, strWCUName As String, obj As Object(), Optional ByVal app As DRAppName = DRAppName.Defaults, Optional ByVal strGroupWxid As String = "")
        Dim isRelust As Boolean = True
        Try
            SetFields(obj)

            '获取导出的路径

            Dim fbdWeixin As New FolderBrowserDialog
            fbdWeixin.Description = Me.mLanguage.GetString("Media.Button.Export") '导出
            fbdWeixin.ShowNewFolderButton = True
            fbdWeixin.SelectedPath = IniSetting.GetWeChatExportPath
            If fbdWeixin.ShowDialog() <> Windows.Forms.DialogResult.OK Then
                Return False
            End If

            IniSetting.SetWeChatExportPath(fbdWeixin.SelectedPath)

            Dim strAppName As String = Me.mLanguage.GetString("Weixin.Lable.WeixinMsgRecord")
            Select Case app
                Case DRAppName.Contact
                    strAppName = Me.mLanguage.GetString("Recovery.Group.Contact")
                Case DRAppName.History
                    strAppName = Me.mLanguage.GetString("Recovery.Group.CallHistory")
            End Select

            strPath = Path.Combine(fbdWeixin.SelectedPath, Utility.ReplaceWinIllegalName(strDName) & "\" & strAppName) '"微信消息记录"
            strPath = Path.Combine(strPath, Date.Now.ToString("yyyyMMddHHmm"))

            If Utility.ReplaceWinIllegalName(strWCUName).Length > 0 Then
                strPath = strPath & "-" & Utility.ReplaceWinIllegalName(strWCUName)
            End If

            'If strGroupWxid.Length > 0 Then
            '    strPath = Path.Combine(strPath, Utility.ReplaceWinIllegalName(Me.GetNickName(strGroupWxid)))
            'End If

            Folder.CheckFolder(strPath)

            If lstT IsNot Nothing AndAlso lstT.Count >= 2 Then
                Dim dtStart As DateTime = CType(lstT(0).ToString("yyyy-MM-dd 00:00:00"), DateTime)
                Dim dtEnd As DateTime = CType(lstT(1).ToString("yyyy-MM-dd 23:59:59"), DateTime)
                lstT.Clear()
                lstT.Add(dtStart)
                lstT.Add(dtEnd)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetParameter")
        End Try
        Return isRelust
    End Function

    Public Function ExportHtml(lstR As List(Of DataGridViewRow), dictDCInfo As Dictionary(Of String, List(Of WeChatChatInfo)), lstT As List(Of Date), strWCUName As String, strDName As String, eType As ExportType, obj As Object(), Optional ByVal blnRecoveryData As Boolean = False, Optional ByVal deviceUdid As String = "")
        Dim strPath As String = ""
        Try
            If Not Me.SetParameter(strPath, lstT, strDName, strWCUName, obj) Then
                Return strPath
            End If

            Dim objPara(8) As Object
            objPara(0) = strPath
            objPara(1) = lstR
            objPara(2) = dictDCInfo
            objPara(3) = lstT
            objPara(4) = strWCUName
            objPara(5) = eType
            objPara(6) = blnRecoveryData
            objPara(7) = True
            Me.mBackupDeviceUdid = deviceUdid

            If Me.mThreadExportHtml IsNot Nothing AndAlso Me.mThreadExportHtml.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadExportHtml.Abort()
                Catch
                End Try
            End If

            Me.mThreadExportHtml = New Thread(New ParameterizedThreadStart(AddressOf DoExportHtml))
            With Me.mThreadExportHtml
                .IsBackground = True
                .SetApartmentState(ApartmentState.STA)
                .Start(objPara)
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExportHtml")
        End Try
        Return strPath
    End Function

    Private mLstCurrentExportInfo As List(Of String) = New List(Of String)
    Private Sub DoExportHtml(ByVal objPara As Object)
        Try
            Utility.WaitSeconds(0.5)

            Dim strFilePath As String = objPara(0).ToString()
            Dim lstRows As List(Of DataGridViewRow) = CType(objPara(1), List(Of DataGridViewRow))
            Dim dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)) = Nothing
            Dim dictDelQQChatInfo As Dictionary(Of String, List(Of QQChatInfo)) = Nothing
            If objPara(2) IsNot Nothing Then
                If TypeOf objPara(2) Is Dictionary(Of String, List(Of WeChatChatInfo)) Then
                    dictDelChatInfo = IIf(objPara(2) Is Nothing, Nothing, CType(objPara(2), Dictionary(Of String, List(Of WeChatChatInfo))))
                ElseIf TypeOf objPara(2) Is Dictionary(Of String, List(Of QQChatInfo)) Then
                    dictDelQQChatInfo = IIf(objPara(2) Is Nothing, Nothing, CType(objPara(2), Dictionary(Of String, List(Of QQChatInfo))))
                End If
            End If

            Dim lstTime As List(Of DateTime) = CType(objPara(3), List(Of DateTime))
            Dim strWeChatUserName As String = objPara(4).ToString()
            Dim eType As ExportType = CType(objPara(5), ExportType)
            Dim blnRecoveryData As Boolean = CType(objPara(6), Boolean)

            Dim isShow As Boolean = CType(objPara(7), Boolean)

            Dim fInfo As SocialFriendInfoBase = Nothing
            Dim qInfo As QQFriendInfo = Nothing
            Dim wInfo As WeChatFriendInfo = Nothing
            Dim wAppInfo As WhatsAppSession = Nothing

            Dim lstInfos As List(Of SociaChatBase) = Nothing
            Dim lstWInfos As List(Of WeChatChatInfo) = Nothing
            Dim lstQInfos As List(Of QQChatInfo) = Nothing
            Dim lstWAppInfos As List(Of WhatsAppMessage) = Nothing

            'Dim deleteInfos As New List(Of WeChatChatInfo)
            Dim strNickName As String = ""
            Dim lstHtmlUserInfo As New List(Of ExportHtmlUserInfo)
            Dim lstExportHtmlMainInfo As New List(Of ExportHtmlMainInfo)
            Dim strDataPath As String = ""

            Dim strHtmlPath As String = Path.Combine(strFilePath, Me.mLanguage.GetString("Weixin.Lable.WebFormat")) '"网页格式"
            Dim strTxtPath As String = Path.Combine(strFilePath, Me.mLanguage.GetString("Weixin.Lable.TextFormat")) '"文本格式"
            Dim strExcelPath As String = Path.Combine(strFilePath, Me.mLanguage.GetString("Weixin.Lable.ExcelFormat")) '"表格格式"

            Dim strContentPath As String = Path.Combine(strHtmlPath, "content")

            Dim intCurrentIndex As Integer = 0
            Dim intTotal As Integer = lstRows.Count

            If eType = ExportType.ExportToHtml Then
                Folder.CheckFolder(strContentPath)
                Try
                    '解压Html资源
                    Dim strFileZipPath As String = Path.Combine(Folder.TempFolder, "weixin_zip_exporthtml.zip")
                    ReleaseResource(strFileZipPath, My.Resources.weixin_zip_exporthtml)

                    If Utility.unzip(strFileZipPath, strHtmlPath) = 0 Then
                        Common.LogException("微信导出Html 资源文件解压失败", "微信导出Html 资源文件解压失败")
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoExportHtml_1")
                End Try
            End If

            '----------------------------------------------------------------
            Dim strEPEAMsg As String = ""
            For Each Row As DataGridViewRow In lstRows
                Try
                    fInfo = Nothing
                    qInfo = Nothing
                    wInfo = Nothing
                    wAppInfo = Nothing

                    lstInfos = Nothing
                    lstWInfos = Nothing
                    lstQInfos = Nothing
                    lstWAppInfos = Nothing

                    strNickName = ""
                    strEPEAMsg = ""

                    If mExportSocialType <> ExportSocialType.AndroidWeChat Then
                        Dim strUsrName As String = ""
                        Dim isWeChat As Boolean = False
                        Dim isQQ As Boolean = False
                        If TypeOf Row.Tag Is WeChatFriendInfo Then
                            isWeChat = True
                            wInfo = CType(Row.Tag, WeChatFriendInfo)
                            lstWInfos = Me.mWeixinMMDB.Get_Chat_Info(wInfo.UsrName, lstTime)
                            strUsrName = wInfo.UsrName

                        ElseIf TypeOf Row.Tag Is QQFriendInfo Then
                            isQQ = True
                            qInfo = CType(Row.Tag, QQFriendInfo)
                            lstQInfos = Me.mQQDB.Get_Chat_Info_ByTime_All(qInfo.StrQQNum, lstTime, qInfo.IsGroup, qInfo.IsDiscuss)
                            strUsrName = qInfo.StrQQNum

                        ElseIf TypeOf Row.Tag Is WhatsAppSession Then
                            wAppInfo = CType(Row.Tag, WhatsAppSession)
                            lstWAppInfos = Me.mWhatsAppHelper.GetMessage(wAppInfo.SessionIdZPK)
                            strUsrName = wAppInfo.ContactJid

                        End If

                        strNickName = Me.GetNickName(strUsrName)
                        If mExportSocialType = ExportSocialType.WhatsApp Then
                            strNickName = wAppInfo.ChatName
                        ElseIf Me.mExportSocialType = ExportSocialType.QQ Then
                            strNickName = qInfo.NoteName
                            If String.IsNullOrEmpty(strNickName) Then
                                strNickName = qInfo.NickName
                            End If
                        End If
                        intCurrentIndex = intCurrentIndex + 1

                        mLstCurrentExportInfo = New List(Of String)
                        mLstCurrentExportInfo.Add(strNickName)
                        mLstCurrentExportInfo.Add(intCurrentIndex.ToString())
                        mLstCurrentExportInfo.Add(intTotal.ToString())
                        mLstCurrentExportInfo.Add(strFilePath)

                        Dim strFolder As String = Utility.ReplaceWinIllegalName(strNickName)
                        strFolder = strFolder.Replace("#", "_").Replace("&", "_").Replace("+", "_").Replace(".", "_")

                        Dim strFolderEx As String = Utility.ReplaceWinIllegalName(strUsrName)
                        strFolderEx = strFolderEx.Replace("#", "_").Replace("&", "_").Replace("+", "_").Replace(".", "_")

                        '-------------------------删除恢复的数据-------------------------
                        GetDeleteDate(isWeChat, isQQ, lstTime, dictDelChatInfo, dictDelQQChatInfo, wInfo, qInfo, lstWInfos, lstQInfos)
                        '--------------------------------------------------------------------

                        Dim strAttachmentName As String = strFolder
                        If Not strUsrName.Contains("_") AndAlso strAttachmentName.Replace("_", "").Length = 0 Then
                            strAttachmentName = strFolderEx
                        End If
                        Dim strAttachment As String = "attachment\" & strAttachmentName

                        '-----------------------------------------------
                        fInfo = ChangeFriendInfoToFriendInfoBase(qInfo, wInfo, wAppInfo, mExportSocialType)
                        lstInfos = ChangeChatsToChatBase(lstQInfos, lstWInfos, lstWAppInfos, mExportSocialType, fInfo.IsGroup)
                        '-----------------------------------------------

                        If Utility.IsHtmlJson() Then
                            '------------------------导出 聊天数据 Json 数据------------------------
                            strDataPath = Path.Combine(strHtmlPath, "data")
                            Me.ExportToHtmlEx(lstInfos, strDataPath, fInfo, strNickName, lstHtmlUserInfo, strAttachment)
                        End If

                        If eType = ExportType.ExportToHtml Then
                            Dim isAudioConvert As Boolean = IniSetting.GetIsAudioConvert()

                            '--------导出Html格式--------
                            Dim strAttachmentPath As String = Path.Combine(strHtmlPath, strAttachment)
                            Dim lstFriendInfo As New List(Of SocialFriendInfoBase)
                            lstFriendInfo.Add(fInfo)
                            Dim obj As Object() = New Object() {lstFriendInfo, strAttachmentPath, lstTime, "Html", dictDelChatInfo, wInfo}

                            If IniSetting.GetIsHtmlExportPicture() Then
                                If mExportSocialType = ExportSocialType.QQ Then
                                    '导出图片
                                    Me.DoExportPhotoByUserId(obj, Me.mCurrentQQNum, Nothing, lstQInfos)
                                Else
                                    '导出图片
                                    Me.DoExportPhotoByUserId(obj)
                                End If
                            End If

                            If IniSetting.GetIsHtmlExportVoice() Then
                                If mExportSocialType = ExportSocialType.QQ Then
                                    '导出语言
                                    Me.DoExportAudioByUserId(obj, isAudioConvert, Me.mCurrentQQNum, Nothing, lstQInfos)
                                Else
                                    '导出语言
                                    Me.DoExportAudioByUserId(obj, isAudioConvert)
                                End If

                            End If

                            If IniSetting.GetIsHtmlExportVideo() Then
                                If mExportSocialType = ExportSocialType.QQ Then
                                    '导出小视频
                                    Me.DoExportVideoByUserId(obj, Me.mCurrentQQNum, Nothing, lstQInfos)
                                Else
                                    '导出小视频
                                    Me.DoExportVideoByUserId(obj)
                                End If
                            End If

                            Dim strHtmlFileName As String = strFolder & ".html"
                            Dim strHtmlFilePath As String = Path.Combine(strContentPath, strHtmlFileName)
                            Me.DoExportHtmlInfo(lstInfos, strHtmlFilePath, fInfo, strNickName, "../" & strAttachment.Replace("\", "/"), lstExportHtmlMainInfo, strUsrName, isAudioConvert)

                        ElseIf eType = ExportType.ExportToTxt Then
                            '--------导出文本格式 导出为txt--------
                            If Not Directory.Exists(strTxtPath) Then
                                Folder.CheckFolder(strTxtPath)
                            End If

                            Dim strTxtFileName As String = strFolder & ".txt"
                            Dim strTxtFilePath As String = Path.Combine(strTxtPath, strTxtFileName)
                            Me.ExportToFile(lstInfos, strTxtFilePath, True)

                        ElseIf eType = ExportType.ExportToExcel Then
                            '--------导出Excel格式--------
                            If Not Directory.Exists(strExcelPath) Then
                                Folder.CheckFolder(strExcelPath)
                            End If

                            Dim strExeclFileName As String = strFolder & ".xls"
                            Dim strExeclFilePath As String = Path.Combine(strExcelPath, strExeclFileName)
                            Me.ExportToFile(lstInfos, strExeclFilePath, False)
                        End If

                    Else
                        If TypeOf Row.Tag Is Rconversation Then

                            Dim aInfo As Rconversation = CType(Row.Tag, Rconversation)
                            wInfo = GenerateRconversation2FriendInfo(aInfo)

                            Dim dictChatMsg As New Dictionary(Of String, Chatmessage)
                            If lstTime.Count > 0 Then
                                dictChatMsg = Me.mAndroidWeixin.GetChatMessages(wInfo.UsrName, "", lstTime(0), lstTime(1))
                            Else
                                dictChatMsg = Me.mAndroidWeixin.GetChatMessages(wInfo.UsrName)
                            End If

                            lstInfos = New List(Of SociaChatBase)
                            Dim cInfo As SociaChatBase = Nothing
                            For Each info As Chatmessage In dictChatMsg.Values
                                cInfo = GenerateChatmessage2ChatInfo(info)
                                lstInfos.Add(cInfo)
                            Next

                            strNickName = Me.mAndroidWeixin.GetMemoName(wInfo.UsrName)
                            If String.IsNullOrEmpty(strNickName) Then
                                strNickName = Me.GetNickName(wInfo.UsrName)
                            End If
                            Dim isTxt As Boolean = True
                            Dim strSuffix As String = ".txt"
                            If eType = ExportType.ExportToExcel Then
                                isTxt = False
                                strSuffix = ".xls"
                            End If

                            If Not Directory.Exists(strTxtPath) Then
                                Folder.CheckFolder(strTxtPath)
                            End If

                            Dim strFolder As String = Utility.ReplaceWinIllegalName(strNickName)
                            strFolder = strFolder.Replace("#", "_").Replace("&", "_").Replace("+", "_").Replace(".", "_")
                            Dim strTxtFileName As String = strFolder & strSuffix
                            Dim strTxtFilePath As String = Path.Combine(strTxtPath, strTxtFileName)
                            Me.ExportToFile(lstInfos, strTxtFilePath, isTxt)
                        End If
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoExportHtml_2")
                End Try
            Next

            If Utility.IsHtmlJson() Then
                '------------------------导出 聊天数据 Json 数据------------------------
                If lstHtmlUserInfo.Count > 0 Then
                    Dim ehuInfo As ExportHtmlUserInfo
                    For Each aFInfo As WeChatFriendInfo In mDictFriendInfo.Values
                        If CheckExistExportHtmlUserInfo(aFInfo.UsrName, lstHtmlUserInfo) Then
                            Continue For
                        End If
                        ehuInfo = New ExportHtmlUserInfo
                        ehuInfo.userName = aFInfo.UsrName
                        ehuInfo.nickName = aFInfo.NickName
                        ehuInfo.type = aFInfo.IntType
                        ehuInfo.msgTotal = 0
                        lstHtmlUserInfo.Add(ehuInfo)
                    Next

                    Me.GetChatUser(lstHtmlUserInfo, strDataPath, strWeChatUserName)
                End If
            End If

            '------------------------导出 MainHtml------------------------
            If eType = ExportType.ExportToHtml AndAlso lstExportHtmlMainInfo.Count > 0 Then
                Dim strMainHtml As String = Path.Combine(strHtmlPath, String.Format("{0}.html", Me.mLanguage.GetString("Weixin.Lable.AllChats"))) '"全部聊天记录.html
                lstExportHtmlMainInfo.Sort(New CreateExportHtmlMainInfoTimeComparer(SortType.DESC))
                IMHtmlNodeHelper.CreateMainHtml("", strMainHtml, lstExportHtmlMainInfo)
            End If

            If eType = ExportType.ExportToExcel AndAlso blnRecoveryData Then
                CopyMMDBAndSessionDBFile(strExcelPath)
            End If

            If isShow Then
                RaiseEvent ExportProgressEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportSuccess, Me.mLanguage.GetString("Note.Message.ExportSucceed"))) '导出成功
                RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportSuccess, Me.mLanguage.GetString("Note.Message.ExportSucceed"), 100, ""))
                'Common.OpenExplorer(strFilePath)
            End If

            If mExportSocialType = ExportSocialType.AndroidWeChat Then
                Common.OpenExplorer(strFilePath)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportHtml")
            RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportFailure, Me.mLanguage.GetString("Contact.Message.ExportFailure"), 100, "")) '导出失败
        End Try

    End Sub

    Private Sub GetDeleteDate(ByVal isWeChat As Boolean, ByVal isQQ As Boolean, ByVal lstTime As List(Of DateTime), _
                              ByVal dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), ByVal dictDelQQChatInfo As Dictionary(Of String, List(Of QQChatInfo)), _
                              ByVal wInfo As WeChatFriendInfo, ByVal qInfo As QQFriendInfo, _
                              ByRef lstWInfos As List(Of WeChatChatInfo), ByRef lstQInfos As List(Of QQChatInfo))
        If isWeChat Then
            Try
                Dim deleteInfos As List(Of WeChatChatInfo) = Me.GetDelectChatInfos(dictDelChatInfo, wInfo)
                If deleteInfos IsNot Nothing AndAlso deleteInfos.Count > 0 Then
                    For Each cInfo As WeChatChatInfo In deleteInfos
                        If lstTime.Count = 2 Then
                            '时间过滤
                            If cInfo.IntCreateTime >= WeixinHelper.ConvertPcTimeToWeixin(lstTime(0)) AndAlso cInfo.IntCreateTime <= WeixinHelper.ConvertPcTimeToWeixin(lstTime(1)) Then
                                lstWInfos.Add(cInfo)
                            End If
                        Else
                            '无时间过滤
                            lstWInfos.Add(cInfo)
                        End If
                    Next
                End If
                lstWInfos.Sort(New CreateTimeComparer(SortType.ASC))
            Catch ex As Exception
                Common.LogException(ex.ToString(), "DoExportHtml_GetDelectChatInfos")
            End Try
        ElseIf isQQ Then
            Try
                Dim delQQInfos As New List(Of QQChatInfo)
                delQQInfos = Me.GetDelQQChatInfos(dictDelQQChatInfo, qInfo)
                If delQQInfos IsNot Nothing AndAlso delQQInfos.Count > 0 Then
                    For Each cInfo As QQChatInfo In delQQInfos
                        'lstQInfos.Add(cInfo)
                        If lstTime.Count = 2 Then
                            '时间过滤
                            If cInfo.IntCreateTime >= WeixinHelper.ConvertPcTimeToWeixin(lstTime(0)) AndAlso cInfo.IntCreateTime <= WeixinHelper.ConvertPcTimeToWeixin(lstTime(1)) Then
                                lstQInfos.Add(cInfo)
                            End If
                        Else
                            '无时间过滤
                            lstQInfos.Add(cInfo)
                        End If
                    Next
                End If
                lstQInfos.Sort(New QQCreateTimeComparer(SortType.ASC))
            Catch ex As Exception
                Common.LogException(ex.ToString(), "DoExportHtml_GetDeleteDate")
            End Try
        End If
    End Sub

    Public Function ExportWhatsAppAllInfo(lstR As List(Of DataGridViewRow), dictDCInfo As Object, lstT As List(Of Date), strWCUName As String, strDName As String, lstType As List(Of ExportType), obj As Object(), Optional ByVal blnRecoveryData As Boolean = False, Optional ByVal deviceUdid As String = "") As String
        Dim strPath As String = ""
        Try
            If Not Me.SetParameter(strPath, lstT, strDName, strWCUName, obj) Then
                Return strPath
            End If
            Me.mBackupDeviceUdid = deviceUdid

            Dim iIndex As Integer = 0
            Dim objParaAll(lstType.Count) As Object
            For Each item As ExportType In lstType
                Dim objPara(8) As Object
                objPara(0) = strPath
                objPara(1) = lstR
                objPara(2) = dictDCInfo
                objPara(3) = lstT
                objPara(4) = strWCUName
                objPara(5) = item
                objPara(6) = blnRecoveryData
                objPara(7) = IIf(iIndex = lstType.Count - 1, True, False)
                objParaAll(iIndex) = objPara
                iIndex += 1
            Next

            If Me.mThreadExportHtml IsNot Nothing AndAlso Me.mThreadExportHtml.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadExportHtml.Abort()
                Catch
                End Try
            End If

            Me.mThreadExportHtml = New Thread(New ParameterizedThreadStart(AddressOf DoExportWhatsAppAllInfo))
            With Me.mThreadExportHtml
                .IsBackground = True
                .SetApartmentState(ApartmentState.STA)
                .Start(objParaAll)
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExportHtml")
        End Try
        Return strPath
    End Function

    Private Sub DoExportWhatsAppAllInfo(ByVal objPara As Object)
        Try
            Dim arrObj As Object() = CType(objPara, Object())

            For Each item As Object() In arrObj
                If item IsNot Nothing Then
                    Me.DoExportHtml(item)
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportWhatsAppAllInfo")
        End Try
    End Sub

    Private Function GenerateRconversation2FriendInfo(info As Rconversation) As WeChatFriendInfo
        Dim iInfo As WeChatFriendInfo = New WeChatFriendInfo()
        iInfo.UsrName = info.username
        Return iInfo
    End Function

    Private Function GenerateChatmessage2ChatInfo(info As Chatmessage) As SociaChatBase
        Dim cInfo As SociaChatBase = New SociaChatBase()
        Try
            cInfo.DTCreateTime = info.createTime
            cInfo.IntID = info.msgId
            cInfo.StrMessage = AndroidWeixin.EmojiReplaceText(info.content)
            cInfo.IntStatus = IIf(info.isSend = 0, 4, 2) ' 2发送；4接收；3未读       0：联系人发给我或群中 1：我发给联系人或群中
            cInfo.SCType = info.msgtype
            cInfo.StrUsrName = info.wxsenderID

        Catch ex As Exception

        End Try
        Return cInfo
    End Function

    Private Sub CopyMMDBAndSessionDBFile(ByVal dirOutPut As String)
        Try

            Dim strMMdb As String = FormatPath(String.Format("{0}/DB/MM.sqlite", Me.mPathWeixinOnPhone))
            Dim strSessiondb As String = FormatPath(String.Format("{0}/session/session.db", Me.mPathWeixinOnPhone))
            Dim strWCDBContact As String = FormatPath(String.Format("{0}/DB/WCDB_Contact.sqlite", Me.mPathWeixinOnPhone)) '微信 6.3.23 联系人获取不到 兼容

            If Not File.Exists(strMMdb) OrElse Not File.Exists(strSessiondb) Then
                MessageBox.Show("数据库文件不存在", "导出错误", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If
            '手机恢复数据文件，MM.sqlite, session.db
            'Dim strFolder As String = Path.Combine(Folder.DocumentFolder, "WechatRecoveryPhone")
            'Dim strRecoveryDataFolder As String = Path.Combine(strFolder, DateTime.Now.ToString("yyyyMMddHHmmss"))
            'Folder.CheckFolder(strRecoveryDataFolder)
            Dim strDesMMPath As String = Path.Combine(dirOutPut, Path.GetFileName(strMMdb))
            Dim strDesSessionPath As String = Path.Combine(dirOutPut, Path.GetFileName(strSessiondb))
            Dim strDesWCDBContact As String = Path.Combine(dirOutPut, Path.GetFileName(strWCDBContact))
            File.Copy(strMMdb, strDesMMPath, True)
            File.Copy(strSessiondb, strDesSessionPath, True)
            '生成记录的ini文件
            Dim strIniFile As String = Path.Combine(dirOutPut, "Settings.ini")
            If File.Exists(strWCDBContact) Then
                File.Copy(strWCDBContact, strDesWCDBContact, True)
                IniClass.SetIniSectionKey("Setting", "WCDBContact", strWCDBContact, strIniFile)
            End If
            IniClass.SetIniSectionKey("Setting", "MMPath", strMMdb, strIniFile)
            IniClass.SetIniSectionKey("Setting", "SessionPath", strSessiondb, strIniFile)
            Dim strUdid As String = ""
            If String.IsNullOrEmpty(Me.mBackupDeviceUdid) = False Then
                strUdid = Me.mBackupDeviceUdid
            End If
            IniClass.SetIniSectionKey("Setting", "UDID", strUdid, strIniFile)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CopyMMDBAndSessionDBFile")
        End Try
    End Sub
    'Private Sub DoRaiseEvent(intP As Integer, Optional strNName As String = "", Optional intCIndex As Integer = 0, Optional intTotal As Integer = 0, Optional isIni As Boolean = False)

    '    If intP < Me.mIntCurrentIndex AndAlso intP > 100 Then
    '        Return
    '    End If

    '    If isIni Then
    '        Me.mIntCurrentIndex = intCIndex
    '        Me.mIntTotal = intTotal
    '        Me.mStrNickName = strNName
    '    End If
    '    Dim strEPEAMsg As String = String.Format(Me.mLanguage.GetString("Weixin.Message.ExportMsg"), """" & Me.mStrNickName & """") & String.Format(" {0}% ({1}/{2})", intP, Me.mIntCurrentIndex, Me.mIntTotal)
    '    RaiseEvent ExportProgressEventHandler(Me, New ExportProgressEventArgs(ExportStatus.Exporting, strEPEAMsg)) '正在导出 
    'End Sub

    Private mCurrentProgress As Double = 0
    Private Sub DoRaiseNewProgressEvent(ByVal intIndex As Double, ByVal intCount As Double, Optional ByVal strType As String = "")
        If (mLstCurrentExportInfo Is Nothing OrElse mLstCurrentExportInfo.Count < 4) Then
            Return
        End If

        If intIndex = 0 AndAlso intCount = 0 Then
            Return
        End If
        Dim intProgress As Double = Math.Round((intIndex / intCount) * 100)
        'If (mCurrentProgress < intProgress) Then

        'End If
        mCurrentProgress = intProgress
        'Dim strMsg As String = String.Format("正在导出【{0}】{3} {1}/{2}", mLstCurrentExportInfo(0), mLstCurrentExportInfo(1), mLstCurrentExportInfo(2), strType + " " + intProgress.ToString() + "%")
        Dim strExport As String = String.Format(Me.mLanguage.GetString("Weixin.Message.ExportMsg"), "【" & mLstCurrentExportInfo(0) & "】")
        Dim strMsg As String = String.Format("{0} {3} {1}/{2}", strExport, mLstCurrentExportInfo(1), mLstCurrentExportInfo(2), strType + " ")
        RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.Exporting, strMsg, intProgress, mLstCurrentExportInfo(3)))


    End Sub

    Private Function CheckExistExportHtmlUserInfo(strUserName As String, lstHtmlUserInfo As List(Of ExportHtmlUserInfo)) As Boolean
        Dim isRelust As Boolean = False
        Try
            For Each item As ExportHtmlUserInfo In lstHtmlUserInfo
                If item.userName = strUserName Then
                    isRelust = True
                    Exit For
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckExistExportHtmlUserInfo")
        End Try
        Return isRelust
    End Function

    Private Shared Sub ReleaseResource(ByVal FilePath As String, ByVal Resource As Byte())
        Try
            If File.Exists(FilePath) Then
                Dim fileInfo As FileInfo = New FileInfo(FilePath)

                If fileInfo.Length <> Resource.Length Then
                    Try
                        File.Delete(FilePath)
                    Catch
                    End Try
                End If
            End If

            If Not File.Exists(FilePath) Then
                Using objWriter As New FileStream(FilePath, FileMode.OpenOrCreate, FileAccess.Write)
                    objWriter.Write(Resource, 0, Resource.Length)
                End Using
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ReleaseResource")
        End Try
    End Sub

    Private Function ChangeFriendInfoToFriendInfoBase(qInfo As QQFriendInfo, wInfo As WeChatFriendInfo, wAppInfo As WhatsAppSession, type As ExportSocialType) As SocialFriendInfoBase
        Dim info As New SocialFriendInfoBase
        Try
            If type = ExportSocialType.WeChat Then
                info.DTLastSendTime = wInfo.DTLastSendTime
                info.IntSex = wInfo.IntSex
                info.IsFindDate = wInfo.IsFindDate
                info.IsGroup = wInfo.IsGroup
                info.NickName = wInfo.NickName
                info.NoteName = wInfo.NoteName
                info.SFType = wInfo.SFType
                info.StrChatRoomMem = wInfo.StrChatRoomMem
                info.StrID = wInfo.StrID
                info.StrLastSendMsg = wInfo.StrLastSendMsg
                info.StrProfile = wInfo.StrProfile
                info.UsrName = wInfo.UsrName
            ElseIf type = ExportSocialType.QQ Then
                info.DTLastSendTime = qInfo.DTLastSendTime
                info.IntSex = qInfo.IntSex
                info.IsFindDate = qInfo.IsFindDate
                info.IsGroup = qInfo.IsGroup
                info.IsDiscuss = qInfo.IsDiscuss
                info.NickName = qInfo.NickName
                info.NoteName = qInfo.NoteName
                info.SFType = qInfo.SFType
                info.StrChatRoomMem = qInfo.StrChatRoomMem
                info.StrID = qInfo.StrID
                info.StrLastSendMsg = qInfo.StrLastSendMsg
                info.StrProfile = qInfo.StrProfile
                info.UsrName = qInfo.StrQQNum
            ElseIf type = ExportSocialType.WhatsApp Then
                info.DTLastSendTime = wAppInfo.LastMessageDate
                info.IntSex = 1
                info.IsFindDate = False
                info.IsGroup = IIf(wAppInfo.GroupInfoId > 0, True, False)
                info.NickName = wAppInfo.ChatName
                info.NoteName = wAppInfo.ChatName
                info.SFType = SocialFriendType.None
                info.StrChatRoomMem = wAppInfo.GroupInfoId
                info.StrID = wAppInfo.SessionIdZPK
                Dim msg As WhatsAppMessage = Me.mWhatsAppHelper.GetMessage(wAppInfo.SessionIdZPK, wAppInfo.LastMsgId)
                If msg IsNot Nothing Then
                    info.StrLastSendMsg = msg.Text
                End If
                info.StrProfile = String.Empty
                info.UsrName = wAppInfo.ContactJid
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeFriendInfoToFriendInfoBase")
        End Try
        Return info
    End Function

    Private Function ChangeChatsToChatBase(lstQInfos As List(Of QQChatInfo), lstWInfos As List(Of WeChatChatInfo), lstWAppInfos As List(Of WhatsAppMessage), type As ExportSocialType, isGroup As Boolean) As List(Of SociaChatBase)
        Dim lstChat As New List(Of SociaChatBase)
        Try
            If type = ExportSocialType.WeChat Then
                For Each item As WeChatChatInfo In lstWInfos
                    lstChat.Add(ChangeChatToChatBase(Nothing, item, Nothing, type, isGroup))
                Next
            ElseIf type = ExportSocialType.QQ Then
                For Each item As QQChatInfo In lstQInfos
                    lstChat.Add(ChangeChatToChatBase(item, Nothing, Nothing, type, isGroup))
                Next
            ElseIf type = ExportSocialType.WhatsApp Then
                For Each item As WhatsAppMessage In lstWAppInfos
                    lstChat.Add(ChangeChatToChatBase(Nothing, Nothing, item, type, isGroup))
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeChatToChatBase")
        End Try
        Return lstChat
    End Function

    Private Function ChangeChatToChatBase(qChat As QQChatInfo, wChat As WeChatChatInfo, wAppChat As WhatsAppMessage, type As ExportSocialType, isGroup As Boolean) As SociaChatBase
        Dim chat As New SociaChatBase
        Try
            If type = ExportSocialType.WeChat Then
                chat.DTCreateTime = wChat.DTCreateTime
                chat.ImgStatus = wChat.ImgStatus
                chat.IntCreateTime = wChat.IntCreateTime
                chat.IntDes = wChat.IntDes
                chat.IntID = wChat.IntID
                chat.IntStatus = wChat.IntStatus
                chat.IsDelete = wChat.IsDelete
                chat.SCType = wChat.SCType
                chat.StrMessage = wChat.StrMessage
                chat.StrPicUrl = wChat.StrPicUrl
                chat.StrSendUin = wChat.StrSendUin
                chat.StrUsrName = wChat.StrUsrName
            ElseIf type = ExportSocialType.QQ Then
                chat.DTCreateTime = qChat.DTCreateTime
                chat.ImgStatus = qChat.ImgStatus
                chat.IntCreateTime = qChat.IntCreateTime
                chat.IntDes = qChat.IntDes
                chat.IntID = qChat.IntID
                chat.IntStatus = qChat.IntStatus
                chat.IsDelete = qChat.IsDelete
                chat.SCType = qChat.SCType
                chat.StrMessage = qChat.StrMessage
                chat.StrPicUrl = qChat.StrPicUrl
                chat.StrSendUin = qChat.StrSendUin
                chat.StrUsrName = qChat.StrSendUin
                chat.StrNickName = qChat.StrNickName

            ElseIf type = ExportSocialType.WhatsApp Then
                chat.DTCreateTime = wAppChat.MessageDate
                chat.ImgStatus = 0
                chat.IntCreateTime = 0
                chat.IntID = 0
                chat.IntStatus = 0
                chat.IsDelete = False
                chat.SCType = wAppChat.MessageType
                chat.IntDes = wAppChat.IsFromMe
                chat.IsWhatsAppChatRoom = isGroup
                If wAppChat.IsFromMe = 1 Then
                    'chat.IntDes = 0
                    chat.StrUsrName = Me.mUsrNameMyself
                    chat.StrNickName = Me.mLanguage.GetString("Weixin.Text.Me")
                ElseIf wAppChat.Member IsNot Nothing Then
                    'chat.IntDes = 1
                    chat.StrUsrName = wAppChat.Member.MemberJid
                    chat.StrNickName = wAppChat.Member.PushName
                ElseIf wAppChat.IsFromMe = 0 Then
                    'chat.IntDes = 1
                    chat.StrUsrName = wAppChat.FromJID
                    chat.StrNickName = wAppChat.ChatSession.ContactNames
                End If
                If chat.SCType = SociaChatType.Linkman OrElse chat.SCType = SociaChatType.StartChat Then
                    chat.StrUsrName = "WhatsApp"
                    chat.StrNickName = "WhatsApp"
                End If
                chat.StrMessage = wAppChat.Text
                If wAppChat.MessageType = SociaChatType.Voice Then
                    chat.StrMessage = "voicelength=""" & wAppChat.Medias(0).Duration * 1000 & """"
                End If
                chat.StrPicUrl = String.Empty
                chat.StrSendUin = wAppChat.ToJID
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChangeFriendInfoToFriendInfoBase")
        End Try
        Return chat
    End Function


#End Region

#Region "--- 导出Html文件 ---"
    Private Sub DoExportHtmlInfo(lstInfos As List(Of SociaChatBase), strDPath As String, fInfo As SocialFriendInfoBase, strNName As String, strAttachment As String, lstEHlMInfo As List(Of ExportHtmlMainInfo), strUName As String, ByVal isAudioConvert As Boolean)

        Try
            Dim strFromForWeb As String = String.Empty   '0：对方，1：自己
            Dim strTypeForWeb As String = String.Empty ' '0：文本，1：语音，2：图片，3：网页，4：系统消息
            Dim iconPath As String = String.Empty
            Dim MD5 As String = String.Empty
            Dim strUsrName As String = String.Empty         '消息发送者
            Dim strMsgForWeb As String = String.Empty
            Dim arrHtml5ContentForWeb As JsonArray
            Dim isLinkA As Boolean = False
            Dim strTimeForWeb As String = String.Empty
            Dim startTime As DateTime = DateTime.MinValue
            Dim isChatRoom As Boolean = Me.JudgeIsChatRoom(fInfo.UsrName)     '判断是否为群组
            If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                isChatRoom = fInfo.IsGroup
            End If

            If Me.mExportSocialType = ExportSocialType.QQ Then
                If fInfo.IsGroup OrElse fInfo.IsDiscuss Then
                    isChatRoom = True
                End If
            End If

            Dim lstIMHtmlNode As New List(Of IMHtmlNode)
            Dim mInfo As ExportHtmlMainInfo

            Dim intIndex As Integer = 0
            Dim intCProgress As Integer = 0
            Dim temp As Integer = 0
            Dim strUsrNameToChat As String = ""

            '倒叙
            For Each Item As SociaChatBase In lstInfos
                isLinkA = False
                intIndex += 1
                Me.DoRaiseNewProgressEvent(intIndex, lstInfos.Count, "HTML")
                '新闻欢迎数据可以不用显示
                If fInfo.UsrName = "newsapp" AndAlso Item.SCType = SociaChatType.Text Then ' = 1 
                    Continue For
                End If

                '1、来源与用户名
                strFromForWeb = "0"
                strUsrName = ""
                '  strNickName = String.Empty
                strMsgForWeb = Item.StrMessage  '群组的信息里面包括联系人名称

                If Not Me.IsReceivedMsg(Item) Then
                    strFromForWeb = "1"
                    strUsrName = Me.mUsrNameMyself
                    iconPath = Me.GetHeadIcon(mUsrNameMyself)

                Else
                    strFromForWeb = "0"
                    '如果是群组，则需要取每一个人的用户名
                    If isChatRoom Then
                        Dim values As String() = strMsgForWeb.Split(New Char() {vbCr, vbLf})

                        If strMsgForWeb.Contains("videomsg") Then
                            Try
                                If values.Length > 2 Then
                                    Dim strVideomsg As String = values(2)
                                    If strVideomsg.Contains("fromusername=") Then
                                        Dim strTemp As String = strVideomsg.Substring(strVideomsg.IndexOf("fromusername="), strVideomsg.Length - strVideomsg.IndexOf("fromusername="))
                                        strTemp = strTemp.Substring(strTemp.IndexOf("""") + 1)
                                        strUsrName = strTemp.Substring(0, strTemp.IndexOf(""""))
                                    End If
                                End If
                            Catch
                            End Try
                        End If

                        If values.Length > 1 Then
                            If strUsrName.Length = 0 AndAlso values(0).EndsWith(":") Then
                                strUsrName = values(0).TrimEnd(":")
                            Else
                                Common.LogException(values(0), "微信导出用户昵称")
                            End If

                            Dim strMsgTemp As String = String.Empty
                            For index As Integer = 1 To values.Length - 1
                                If String.IsNullOrEmpty(strMsgTemp) Then
                                    strMsgTemp = values(index)
                                Else
                                    strMsgTemp = strMsgTemp & vbCrLf & values(index)
                                End If
                            Next

                            If strMsgTemp.Length > 0 Then
                                strMsgForWeb = strMsgTemp
                            End If
                        End If

                        If strUsrName.Length = 0 AndAlso values.Length = 1 Then
                            Try
                                Dim strVideomsg As String = values(0)
                                If strVideomsg.Contains("fromusername=") Then
                                    Dim strTemp As String = strVideomsg.Substring(strVideomsg.IndexOf("fromusername="), strVideomsg.Length - strVideomsg.IndexOf("fromusername="))
                                    strTemp = strTemp.Substring(strTemp.IndexOf("""") + 1)
                                    strUsrName = strTemp.Substring(0, strTemp.IndexOf(""""))

                                ElseIf strMsgForWeb.Contains("<fromusername>") Then
                                    Dim strTemp As String = strMsgForWeb.Substring(strMsgForWeb.IndexOf("<fromusername>"), strMsgForWeb.Length - strMsgForWeb.IndexOf("<fromusername>"))
                                    strTemp = strTemp.Substring(strTemp.IndexOf("<fromusername>") + "<fromusername>".Length)
                                    strUsrName = strTemp.Substring(0, strTemp.IndexOf("</fromusername>"))
                                    If strUsrName.StartsWith("<![CDATA[") Then
                                        strUsrName = strUsrName.Replace("<![CDATA[", "").Replace("]]>", "")
                                    End If
                                End If
                            Catch
                            End Try
                            If strUsrName.Length = 0 AndAlso values.Length = 1 AndAlso Not (Item.SCType = SociaChatType.SystemMessages OrElse Item.SCType = SociaChatType.SystemMessagesEx OrElse Item.SCType = SociaChatType.SystemMessagesNew OrElse Item.SCType = SociaChatType.SystemMessagesNewEx) Then
                                Try
                                    If Not Item.IsDelete AndAlso Not values(0).Contains("<type>2001</type>") Then
                                        Dim arrTemp As String() = values(0).Split(":")
                                        strUsrName = arrTemp(0)
                                        If strUsrName.EndsWith("@chatroom") Then
                                            strUsrName = ""
                                        Else
                                            Dim strMsgTemp As String = String.Empty
                                            For index As Integer = 1 To arrTemp.Length - 1
                                                If String.IsNullOrEmpty(strMsgTemp) Then
                                                    strMsgTemp = arrTemp(index)
                                                Else
                                                    strMsgTemp = strMsgTemp & vbCrLf & arrTemp(index)
                                                End If
                                            Next

                                            If strMsgTemp.Length > 0 Then
                                                strMsgForWeb = strMsgTemp
                                            End If
                                        End If
                                    End If
                                Catch
                                End Try
                            End If
                        End If

                        If Me.mExportSocialType = ExportSocialType.QQ Then
                            strUsrName = Item.StrSendUin
                        End If
                        iconPath = Me.GetHeadIcon(strUsrName)
                    Else
                        iconPath = Me.GetHeadIcon(fInfo.UsrName)
                        strUsrName = Item.StrUsrName
                    End If
                End If

                '2、昵称与头像
                If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                    Dim strWhatsAppUser As String = "Photo."
                    If Me.IsReceivedMsg(Item) Then
                        strWhatsAppUser = Item.StrUsrName
                    End If
                    Dim strIconPathOnPhone As String = Me.mWhatsAppHelper.GetIconPath(strWhatsAppUser)
                    If strIconPathOnPhone.Length > 0 Then
                        iconPath = Me.mWhatsAppHelper.GetImageByJID(strIconPathOnPhone)
                    Else
                        iconPath = Me.mWhatsAppHelper.GetImageByJID(strWhatsAppUser)
                    End If
                    
                End If

                If String.IsNullOrEmpty(iconPath) Then
                    iconPath = "../assets/images/default.jpg"
                End If

                MD5 = Me.GetFriendMD5(strUsrName)

                Dim smsNode As New IMHtmlNode
                smsNode.IsItemMe = IIf(strFromForWeb = "0", False, True)
                smsNode.UserID = Item.IntID
                smsNode.IsDeleted = Item.IsDelete
                smsNode.HeaderIconUrl = iconPath

                If strUsrName <> Me.mUsrNameMyself Then
                    smsNode.NickName = Me.GetNickName(strUsrName)
                    If isChatRoom Then
                        Dim lstGroupFriendInfo As New List(Of WeChatFriendInfo)
                        WeixinImportOrExportHelper.GetGroupFriend(Path.Combine(Path.GetDirectoryName(Me.mWeixinMMDB.DbFile), "WCDB_Contact.sqlite"), strUName, Nothing, Me.mDictFriendInfo, lstGroupFriendInfo)
                        If lstGroupFriendInfo.Count > 0 Then
                            For Each wfi As WeChatFriendInfo In lstGroupFriendInfo
                                If wfi.Md5 = MD5 Then
                                    smsNode.NickName = IIf(wfi.GroupNoteName.Length > 0, wfi.GroupNoteName, IIf(wfi.NickName.Length > 0, wfi.NickName, wfi.NoteName))
                                    If smsNode.NickName.Length = 0 Then
                                        smsNode.NickName = Me.GetNickName(strUsrName)
                                    End If
                                    Exit For
                                End If
                            Next
                        End If
                    End If
                Else
                    smsNode.NickName = Me.GetNickName(Item, strUsrNameToChat)
                End If

                If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                    smsNode.NickName = Item.StrNickName
                End If
                smsNode.UserName = smsNode.NickName

                Dim strWeChatID As String = Item.StrUsrName
                If isChatRoom Then
                    strWeChatID = strUsrNameToChat
                End If
                If isChatRoom Then
                    smsNode.WeChatID = strWeChatID
                Else
                    smsNode.WeChatID = smsNode.NickName
                End If

                smsNode.IsChatRoom = isChatRoom

                '3、消息类型与内容
                '微信： 1文本；3图片；34语音；42名片；43视频；47特殊表情；48地理位置；49网页；50视频未接通的消息；1000系统消息              
                arrHtml5ContentForWeb = Nothing
                strTypeForWeb = "0"

                Select Case Item.SCType
                    Case SociaChatType.Text '"1"
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Text

                        strTypeForWeb = "0"

                        '检查是否有url
                        Dim strCheckIsContainUrl As String = strMsgForWeb
                        Dim blnHighlight As Boolean = True
                        If String.Compare(strCheckIsContainUrl, strMsgForWeb) <> 0 Then
                            blnHighlight = False
                            strMsgForWeb = strCheckIsContainUrl
                        End If

                        '表情符号转换成表情图片
                        strMsgForWeb = Me.OperateForExpression(strMsgForWeb, True)

                    Case SociaChatType.Voice '"34"
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Voice
                        Dim strFileName As String = String.Format("{0}_{1}", Item.StrUsrName, Item.DTCreateTime.ToString("yyyyMMdd_HHmmss"))
                        Dim strSuffix As String = "aud"
                        If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                            strSuffix = "opus"
                        End If

                        'If isAudioConvert Then
                        '    strSuffix = "wav"
                        'End If
                        If isAudioConvert Then
                            strSuffix = "mp3"
                        End If

                        If Me.mExportSocialType = ExportSocialType.WhatsApp Then

                            strFileName = String.Format("{0}_{1}", Item.StrSendUin, Item.DTCreateTime.ToString("yyyyMMdd_HHmmss"))
                        End If

                        'smsNode.AttachPath = strAttachment & "/" & String.Format("{0}.{1}", strFileName, strSuffix)
                        smsNode.AttachPath = strAttachment & "/" & GetMediaFileName(Item, "." & strSuffix)

                        strTypeForWeb = "1"
                        strMsgForWeb = Me.GetChatVoiceLength(Item.StrMessage, True).ToString()

                    Case SociaChatType.Picture '"3"
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Image
                        Dim strImagePath As String = "" ' strAttachment & "/" & String.Format("{0}.png", Item.DTCreateTime.ToString("yyyyMMdd_HHmmss"))
                        'smsNode.ThumbnailPath = strAttachment & "/" & String.Format("{0}.png", Item.DTCreateTime.ToString("yyyyMMdd_HHmmss"))

                        strImagePath = strAttachment & "/" & GetMediaFileName(Item, ".png")
                        smsNode.ThumbnailPath = strAttachment & "/" & GetMediaFileName(Item, ".png")

                        smsNode.AttachPath = strImagePath
                        'If Not File.Exists(strImagePath) Then
                        '    smsNode.ThumbnailPath = "../assets/images/default_photo.png"
                        'End If

                        strTypeForWeb = "2"

                    Case SociaChatType.SmallVideo '"62"
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Image
                        'smsNode.AttachPath = strAttachment & "/" & String.Format("{0}.mp4", Item.DTCreateTime.ToString("yyyyMMdd_HHmmss"))
                        smsNode.AttachPath = strAttachment & "/" & GetMediaFileName(Item, ".mp4")
                        smsNode.ThumbnailPath = "../assets/images/default_video.png"

                        strTypeForWeb = "2"

                    Case SociaChatType.NameCard '"42"
                        strMsgForWeb = "[" & Me.mLanguage.GetString("Weixin.Item.BusinessCard") & "]" '"[名片]"

                    Case "43"
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Image
                        'smsNode.AttachPath = strAttachment & "/" & String.Format("{0}.mp4", Item.DTCreateTime.ToString("yyyyMMdd_HHmmss"))
                        smsNode.AttachPath = strAttachment & "/" & GetMediaFileName(Item, ".mp4")
                        smsNode.ThumbnailPath = "../assets/images/default_video.png" 'Path.Combine(strAttachment, String.Format("{0}.mp4", Item.CreateTime.ToString("yyyyMMdd_HHmmss")))

                        strTypeForWeb = "2"


                    Case SociaChatType.SpecialExpression '"47"
                        strMsgForWeb = "[" & Me.mLanguage.GetString("Weixin.Item.Expression") & "]"  '"[动画表情]"

                    Case SociaChatType.Location '"48"
                        strMsgForWeb = "[" & Me.mLanguage.GetString("Weixin.Item.Location") & "]" '"[地理位置]"

                    Case SociaChatType.webpage '"49"
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_Media
                        strTypeForWeb = "3"

                        arrHtml5ContentForWeb = Me.GetWebPageJsonArray(strMsgForWeb) '网页json
                        If Me.mExportSocialType = ExportSocialType.QQ Then
                            arrHtml5ContentForWeb = Nothing
                        End If
                        Try
                            '兼容链接（只有title和url）
                            If arrHtml5ContentForWeb Is Nothing OrElse arrHtml5ContentForWeb.Count = 0 Then
                                Dim webLinkJsonArray As JsonArray = Me.GetWebLinkJsonArray(strMsgForWeb)

                                If webLinkJsonArray IsNot Nothing AndAlso webLinkJsonArray.Count > 0 Then
                                    Dim dicWebPage As JsonObject = webLinkJsonArray(0)
                                    Dim strTitle As String = CType(dicWebPage("title"), JsonString).Value
                                    Dim strUrl As String = CType(dicWebPage("url"), JsonString).Value
                                    Dim strDes As String = CType(dicWebPage("des"), JsonString).Value

                                    If Not String.IsNullOrEmpty(strTitle) AndAlso Not String.IsNullOrEmpty(strUrl) Then
                                        strTypeForWeb = "0"
                                        arrHtml5ContentForWeb = Nothing

                                        'strMsgForWeb = String.Format("[{1}]({0})", strUrl, strTitle)
                                        isLinkA = True
                                        strMsgForWeb = String.Format("<a href=""{0}"" target=""_blank"">{1}</span></a>", strUrl, strTitle & IIf(strDes.Length = 0, "", vbCrLf & strDes))

                                    Else
                                        strTypeForWeb = "0"

                                        Dim strType As String = ""
                                        Dim strTemp As String = frmWeixin.GetSystemMessagesNew(strMsgForWeb, strType)
                                        If strType <> "" Then
                                            strMsgForWeb = strTemp
                                        Else
                                            strMsgForWeb = "[" & Me.mLanguage.GetString("Weixin.Item.Expression") & "]"  '"[动画表情]"
                                        End If

                                    End If
                                End If
                            End If
                        Catch ex As Exception
                            Common.LogException(ex.ToString(), "GetWebLinkJsonArray2")
                        End Try

                        Try
                            If strTypeForWeb = "3" AndAlso arrHtml5ContentForWeb.Count = 0 Then
                                Dim strType As String = ""
                                Dim strTemp As String = frmWeixin.GetSystemMessagesNew(strMsgForWeb, strType)
                                If strType <> "" Then
                                    arrHtml5ContentForWeb = Nothing
                                    strTypeForWeb = "0"
                                    strMsgForWeb = strTemp
                                End If
                            End If
                        Catch
                        End Try

                    Case SociaChatType.VideoConnected ' "50"
                        strMsgForWeb = "[" & Me.mLanguage.GetString("Weixin.Item.Video") & "]" '"[视频未接通]"

                    Case SociaChatType.SystemMessages, SociaChatType.SystemMessagesEx ' "10000"
                        strTypeForWeb = "4"
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_SystemTip

                    Case SociaChatType.Linkman
                        strTypeForWeb = "4"
                        strMsgForWeb = Item.StrMessage
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_SystemTip

                    Case SociaChatType.StartChat
                        strTypeForWeb = "4"
                        strMsgForWeb = Item.StrMessage
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_SystemTip

                    Case SociaChatType.SystemMessagesNew
                        strTypeForWeb = "4"
                        Dim strMsg As String = Item.StrMessage
                        If Me.IsReceivedMsg(Item) AndAlso isChatRoom Then
                            Dim values As String() = strMsg.Split(New Char() {vbCr, vbLf})
                            If values.Length > 1 Then
                                Dim strMsgTemp As String = String.Empty
                                For index As Integer = 1 To values.Length - 1
                                    If String.IsNullOrEmpty(strMsgTemp) Then
                                        strMsgTemp = values(index)
                                    Else
                                        strMsgTemp = strMsgTemp & vbCrLf & values(index)
                                    End If
                                Next
                                If strMsgTemp.Length > 0 Then
                                    strMsg = strMsgTemp
                                End If
                            End If
                        End If

                        strMsgForWeb = frmWeixin.GetSystemMessagesNew(strMsg)
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_SystemTip

                    Case SociaChatType.SystemMessagesNewEx
                        strTypeForWeb = "4"
                        Dim jObj As JsonObject = JsonParser.ParseString(strMsgForWeb)
                        If jObj IsNot Nothing Then
                            If jObj.ContainsKey("msgContent") Then
                                strMsgForWeb = CType(jObj("msgContent"), JsonString).Value
                            End If
                        End If
                        smsNode.NType = IMHtmlNodeType.IMHtmlNode_SystemTip

                End Select

                If arrHtml5ContentForWeb IsNot Nothing Then
                    smsNode.ArrContentForWeb = arrHtml5ContentForWeb
                Else
                    If isLinkA Then
                        smsNode.SmsContent = strMsgForWeb
                    Else
                        smsNode.SmsContent = MakeText(strMsgForWeb, True)
                    End If
                End If

                If mExportSocialType = ExportSocialType.WhatsApp OrElse mExportSocialType = ExportSocialType.QQ Then
                    smsNode.SmsTime = Item.DTCreateTime.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))
                Else
                    smsNode.SmsTime = Item.DTCreateTime.ToString("yyyy-MM-dd hh:mm")
                End If

                lstIMHtmlNode.Add(smsNode)

                If intIndex = lstInfos.Count Then
                    Try
                        mInfo = New ExportHtmlMainInfo
                        mInfo.id = lstEHlMInfo.Count + 1
                        mInfo.LastMessage = smsNode.SmsContent
                        mInfo.LastMessageTime = Item.DTCreateTime
                        mInfo.UserName = strNName
                        mInfo.IconImage = Me.GetHeadIcon(strUName, fInfo.IsGroup, fInfo.IsDiscuss)
                        If Me.mExportSocialType = ExportSocialType.WhatsApp AndAlso Not String.IsNullOrEmpty(mInfo.IconImage) AndAlso File.Exists(mInfo.IconImage) Then
                            '把联系头像拷贝出来
                            Try
                                Dim strFolderIconImgOnExport As String = Path.Combine(Path.GetDirectoryName(strDPath), "icon")
                                Dim strPathIconImgOnExport As String = Path.Combine(strFolderIconImgOnExport, Path.GetFileNameWithoutExtension(mInfo.IconImage))
                                If Not File.Exists(strPathIconImgOnExport) Then
                                    Folder.CheckFolder(strFolderIconImgOnExport)
                                    File.Copy(mInfo.IconImage, strPathIconImgOnExport)
                                End If
                                mInfo.IconImage = "./content/icon/" & Path.GetFileName(strPathIconImgOnExport).Replace("\", "/")
                            Catch ex As Exception
                                Common.LogException(ex.ToString(), "DoExportHtmlInfo_AddMainInfo_CopyHeadIcon")
                            End Try

                        End If
                        If String.IsNullOrEmpty(mInfo.IconImage) Then
                            mInfo.IconImage = "./assets/images/default.jpg"
                        End If
                        mInfo.StrUrl = Path.Combine("content", Path.GetFileName(strDPath))
                        lstEHlMInfo.Add(mInfo)
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "DoExportHtmlInfo_AddMainInfo")
                    End Try

                End If
            Next
            IMHtmlNodeHelper.CreateHtml(strNName, strDPath, lstIMHtmlNode)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportHtmlInfo")
        End Try
    End Sub

    Private Function MakeText(strMsgForWeb As String, Optional ByVal isExport As Boolean = False)
        Try

            Dim UrlRegex As Regex
            Dim matches As MatchCollection

            '处理url形式超链
            'var reg_url = /((https?:\/\/)?[a-zA-Z0-9][a-zA-Z0-9\?;=\+_\-&\/\.#]*\.(com|cn|net|org|info)\/?[a-zA-Z0-9\?;=\+_\-&\/\.#]*)/g;
            'var link_url = text.replace(reg_url, '<a href="weixin://navigate?url=$1">$1</a>');
            Try
                UrlRegex = New Regex("((https?:\/\/)?[a-zA-Z0-9][a-zA-Z0-9\?;=\+_\-&\/\.#]*\.(com|cn|net|org|info)\/?[a-zA-Z0-9\?;=\+_\-&\/\.#]*)", RegexOptions.IgnoreCase Or RegexOptions.Compiled)
                matches = UrlRegex.Matches(strMsgForWeb)
                For Each match As Match In matches
                    strMsgForWeb = strMsgForWeb.Replace(match.Value, String.Format("<a href=""weixin://navigate?url={0}"">{0}</a>", match.Value))
                Next
            Catch ex As Exception

            End Try

            '处理文本超链
            '[链接](http://www.weixin.com/) 转化成 <a href="http://www.weixin.com/">链接</a>
            'var reg_text = /\[(.*?)\]\(<a\shref=".*">(.*)<\/a>\)/g;
            'var link_text = link_url.replace(reg_text, '<a href="weixin://navigate?url=$2">$1</a>');
            Try
                UrlRegex = New Regex("[(.*?)\]\(<a\shref="".*"">(.*)<\/a>\)", RegexOptions.IgnoreCase Or RegexOptions.Compiled)
                matches = UrlRegex.Matches(strMsgForWeb)
                For Each match As Match In matches
                    strMsgForWeb = strMsgForWeb.Replace(match.Value, String.Format("<a href=""weixin://navigate?url={0}"">{0}</a>", match.Value))
                Next
            Catch ex As Exception

            End Try

            '高亮处理
            '~高亮文字~(background:#000;) 转化成 <span style="background:#000;">高亮文字</span>
            'var reg_bg = /~(.*?)~\((([_*\-a-zA-Z]+:\s*#[0-9a-zA-Z]{3,6};?\s*)+)\)/g;
            'var bg_text = link_text.replace(reg_bg, '<span style="$2">$1</span>');
            Try
                UrlRegex = New Regex("~(.*?)~\((([_*\-a-zA-Z]+:\s*#[0-9a-zA-Z]{3,6};?\s*)+)\)", RegexOptions.IgnoreCase Or RegexOptions.Compiled)
                matches = UrlRegex.Matches(strMsgForWeb)
                For Each match As Match In matches
                    strMsgForWeb = strMsgForWeb.Replace(match.Value, String.Format("<span style=""{0}"">{0}</span>", match.Value))
                Next
            Catch ex As Exception

            End Try

            '处理表情图片
            '^(biaoqong.jpg)^(21,21) 转化成 <img src="biaoqing.jpg" width="21px" height="21px"/>
            'var reg_img = /\^(.*?\.[a-zA-Z0-1]+)\^\((\d+),\s*(\d+)\)/g;
            'var img_text = bg_text.replace(reg_img, '<img src="$1" width="$2px" height="$3px"/>');
            Try
                UrlRegex = New Regex("\^(.*?\.[a-zA-Z0-1]+)\^\((\d+),\s*(\d+)\)", RegexOptions.IgnoreCase Or RegexOptions.Compiled)
                matches = UrlRegex.Matches(strMsgForWeb)
                For Each match As Match In matches
                    strMsgForWeb = strMsgForWeb.Replace(match.Value, String.Format("<img src=""{0}"" width=""21px"" height=""21px""/>", match.Value.Replace("^", "").Replace("(21,21)", "")))
                Next
            Catch ex As Exception

            End Try

        Catch ex As Exception
            Common.LogException(ex.ToString(), "MakeText")
        End Try
        Return strMsgForWeb
    End Function

    Private Function OperateForExpression(ByVal strContent As String, Optional ByVal isExport As Boolean = False) As String
        Dim regular As String = "\[(?<expression>.*?)\]"
        Dim dt As DataTable = Utility.GetMatchStringByRegularExpressions(strContent, New String(0) {regular}, New String(0) {"expression"})

        If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
            For Each dr As DataRow In dt.Rows
                Dim strKey As String = Me.FormatStringForWebPage(HttpUtility.UrlDecode(dr("expression").ToString()))
                strKey = "[" & strKey & "]"

                Dim strExpressionPath As String = Me.GetExpressionPath(strKey, isExport)

                If Not String.IsNullOrEmpty(strExpressionPath) Then
                    strContent = strContent.Replace(strKey, strExpressionPath)
                End If
            Next
        End If

        Return strContent
    End Function

    Private Function GetExpressionPath(ByVal strKey As String, Optional ByVal isExport As Boolean = False) As String
        Dim strName As String = String.Empty

        Select Case strKey
            Case "[微笑]"
                strName = "Expression_1"
            Case "[撇嘴]"
                strName = "Expression_2"
            Case "[色]"
                strName = "Expression_3"
            Case "[发呆]", "[發呆]"
                strName = "Expression_4"
            Case "[得意]"
                strName = "Expression_5"
            Case "[流泪]", "[流淚]"
                strName = "Expression_6"
            Case "[害羞]"
                strName = "Expression_7"
            Case "[闭嘴]", "[閉嘴]"
                strName = "Expression_8"
            Case "[睡]"
                strName = "Expression_9"
            Case "[大哭]"
                strName = "Expression_10"
            Case "[尴尬]", "[尷尬]"
                strName = "Expression_11"
            Case "[发怒]", "[發怒]"
                strName = "Expression_12"
            Case "[调皮]", "[調皮]"
                strName = "Expression_13"
            Case "[呲牙]"
                strName = "Expression_14"
            Case "[惊讶]", "[驚訝]"
                strName = "Expression_15"
            Case "[难过]", "[難過]"
                strName = "Expression_16"
            Case "[酷]"
                strName = "Expression_17"
            Case "[冷汗]"
                strName = "Expression_18"
            Case "[抓狂]"
                strName = "Expression_19"
            Case "[吐]"
                strName = "Expression_20"
            Case "[偷笑]"
                strName = "Expression_21"
            Case "[愉快]"
                strName = "Expression_22"
            Case "[白眼]"
                strName = "Expression_23"
            Case "[傲慢]"
                strName = "Expression_24"
            Case "[饥饿]", "[饑餓]"
                strName = "Expression_25"
            Case "[困]"
                strName = "Expression_26"
            Case "[惊恐]", "[驚恐]"
                strName = "Expression_27"
            Case "[流汗]"
                strName = "Expression_28"
            Case "[憨笑]"
                strName = "Expression_29"
            Case "[悠闲]", "[悠閒]"
                strName = "Expression_30"
            Case "[奋斗]", "[奮鬥]"
                strName = "Expression_31"
            Case "[咒骂]", "[咒罵]"
                strName = "Expression_32"
            Case "[疑问]"
                strName = "Expression_33"
            Case "[嘘]"
                strName = "Expression_34"
            Case "[晕]", "[暈]"
                strName = "Expression_35"
            Case "[疯了]", "[瘋了]"
                strName = "Expression_36"
            Case "[衰]"
                strName = "Expression_37"
            Case "[骷髅]", "[骷髏]"
                strName = "Expression_38"
            Case "[敲打]"
                strName = "Expression_39"
            Case "[再见]"
                strName = "Expression_40"
            Case "[擦汗]"
                strName = "Expression_41"
            Case "[抠鼻]", "[摳鼻]"
                strName = "Expression_42"
            Case "[鼓掌]"
                strName = "Expression_43"
            Case "[糗大了]"
                strName = "Expression_44"
            Case "[坏笑]", "[壞笑]"
                strName = "Expression_45"
            Case "[左哼哼]"
                strName = "Expression_46"
            Case "[右哼哼]"
                strName = "Expression_47"
            Case "[哈欠]"
                strName = "Expression_48"
            Case "[鄙视]", "[鄙視]"
                strName = "Expression_49"
            Case "[委屈]"
                strName = "Expression_50"
            Case "[快哭了]"
                strName = "Expression_51"
            Case "[阴险]", "[陰險]"
                strName = "Expression_52"
            Case "[亲亲]", "[親親]"
                strName = "Expression_53"
            Case "[吓]", "[嚇]"
                strName = "Expression_54"
            Case "[可怜]", "[可憐]"
                strName = "Expression_55"
            Case "[菜刀]"
                strName = "Expression_56"
            Case "[西瓜]"
                strName = "Expression_57"
            Case "[啤酒]"
                strName = "Expression_58"
            Case "[篮球]", "[籃球]"
                strName = "Expression_59"
            Case "[乒乓]"
                strName = "Expression_60"
            Case "[咖啡]"
                strName = "Expression_61"
            Case "[饭]", "[飯]"
                strName = "Expression_62"
            Case "[猪头]", "[豬頭]"
                strName = "Expression_63"
            Case "[玫瑰]"
                strName = "Expression_64"
            Case "[凋谢]"
                strName = "Expression_65"
            Case "[嘴唇]"
                strName = "Expression_66"
            Case "[爱心]", "[愛心]"
                strName = "Expression_67"
            Case "[心碎]"
                strName = "Expression_68"
            Case "[蛋糕]"
                strName = "Expression_69"
            Case "[闪电]", "[閃電]"
                strName = "Expression_70"
            Case "[炸弹]", "[炸彈]"
                strName = "Expression_71"
            Case "[刀]"
                strName = "Expression_72"
            Case "[足球]"
                strName = "Expression_73"
            Case "[瓢虫]", "[瓢蟲]"
                strName = "Expression_74"
            Case "[便便]"
                strName = "Expression_75"
            Case "[月亮]"
                strName = "Expression_76"
            Case "[太阳]", "[太陽]"
                strName = "Expression_77"
            Case "[礼物]", "[禮物]"
                strName = "Expression_78"
            Case "[拥抱]", "[擁抱]"
                strName = "Expression_79"
            Case "[强]"
                strName = "Expression_80"
            Case "[弱]"
                strName = "Expression_81"
            Case "[握手]"
                strName = "Expression_82"
            Case "[胜利]", "[勝利]"
                strName = "Expression_83"
            Case "[抱拳]"
                strName = "Expression_84"
            Case "[勾引]"
                strName = "Expression_85"
            Case "[拳头]", "[拳頭]"
                strName = "Expression_86"
            Case "[差劲]", "[差勁]"
                strName = "Expression_87"
            Case "[爱你]", "[愛你]"
                strName = "Expression_88"
            Case "[NO]"
                strName = "Expression_89"
            Case "[OK]"
                strName = "Expression_90"
            Case "[爱情]", "[愛情]"
                strName = "Expression_91"
            Case "[飞吻]", "[飛吻]"
                strName = "Expression_92"
            Case "[跳跳]"
                strName = "Expression_93"
            Case "[发抖]", "[發抖]"
                strName = "Expression_94"
            Case "[怄火]", "[慪火]"
                strName = "Expression_95"
            Case "[转圈]", "[轉圈]"
                strName = "Expression_96"
            Case "[磕头]", "[磕頭]"
                strName = "Expression_97"
            Case "[回头]", "[回頭]"
                strName = "Expression_98"
            Case "[跳绳]", "[跳繩]"
                strName = "Expression_99"
            Case "[挥手]", "[揮手]"
                strName = "Expression_100"
            Case "[激动]", "[激動]"
                strName = "Expression_101"
            Case "[街舞]"
                strName = "Expression_102"
            Case "[献吻]", "[獻吻]"
                strName = "Expression_103"
            Case "[左太极]", "[左太極]"
                strName = "Expression_104"
            Case "[右太极]", "[右太極]"
                strName = "Expression_105"
        End Select

        Dim result As String = String.Empty
        If Not String.IsNullOrEmpty(strName) Then
            If isExport Then
                result = String.Format("^../assets/expression/{0}.png^(21,21)", strName)
            Else
                result = String.Format("^./expression/{0}.png^(21,21)", strName)
            End If
        Else
            Common.Log(strKey & "---GetExpressionPath")
        End If

        Return result
    End Function

    Private Function GetChatVoiceLength(ByVal strContent As String, ByVal blnRound As Boolean) As Single
        Dim singleLength As Single = 0
        Dim strContent1 As String = strContent
        Dim strKey As String = "voicelength="""

        If strContent1.Length > 0 AndAlso strContent1.Contains(strKey) Then
            Dim startIndex As Integer = strContent1.IndexOf(strKey)
            strContent1 = strContent.Substring(startIndex + strKey.Length)
            startIndex = strContent1.IndexOf("""")
            strContent1 = strContent1.Substring(0, startIndex)

            Try
                singleLength = Single.Parse(strContent1)
            Catch
            End Try

            singleLength = singleLength / 1000

            If blnRound Then
                singleLength = System.Math.Floor(singleLength + 0.5)          '四舍五入
            End If
        End If

        Return singleLength
    End Function

#End Region

#Region "--- 导出 聊天数据 Json 数据---"

    Private Function GetDelectChatInfos(dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), fItem As WeChatFriendInfo) As List(Of WeChatChatInfo)

        Dim deleteInfos As New List(Of WeChatChatInfo)
        Try
            If dictDelChatInfo IsNot Nothing AndAlso dictDelChatInfo.Count > 0 AndAlso (dictDelChatInfo.ContainsKey(fItem.Md5) OrElse dictDelChatInfo.ContainsKey(fItem.NickName)) Then

                If dictDelChatInfo.ContainsKey(fItem.NickName) Then
                    deleteInfos = dictDelChatInfo(fItem.NickName)
                ElseIf dictDelChatInfo.ContainsKey(fItem.Md5) Then
                    deleteInfos = dictDelChatInfo(fItem.Md5)
                End If

                'If fItem.NickName.Contains(mStrUnknown) Then
                '    deleteInfos = dictDelChatInfo(fItem.NickName)
                'Else
                '    deleteInfos = dictDelChatInfo(fItem.Md5)
                'End If
            End If
            If fItem.NickName = WeixinImportOrExportHelper.mStrUnknownEx Then
                Dim lstTemp As List(Of String) = Me.GetUnknownEx(dictDelChatInfo)
                For Each strKey As String In lstTemp
                    Dim lstChatInfoTemp As List(Of WeChatChatInfo) = New List(Of WeChatChatInfo)
                    If dictDelChatInfo.ContainsKey(strKey) Then
                        lstChatInfoTemp = dictDelChatInfo(strKey)
                        For Each itemv As SociaChatBase In lstChatInfoTemp
                            deleteInfos.Add(itemv)
                        Next
                    End If
                Next
            End If
            If deleteInfos.Count = 0 AndAlso fItem.NickName.StartsWith(WeixinImportOrExportHelper.mStrUnknown & "_") Then
                Dim strTempName As String = fItem.NickName.Replace(WeixinImportOrExportHelper.mStrUnknown & "_", "")
                If dictDelChatInfo.ContainsKey(strTempName) Then
                    deleteInfos = dictDelChatInfo(strTempName)
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetDelectChatInfos")
        End Try
        Return deleteInfos
    End Function

    Private Function GetDelQQChatInfos(dictDelChatInfo As Dictionary(Of String, List(Of QQChatInfo)), fItem As QQFriendInfo) As List(Of QQChatInfo)

        Dim deleteInfos As New List(Of QQChatInfo)
        Try
            If dictDelChatInfo IsNot Nothing AndAlso dictDelChatInfo.Count > 0 AndAlso (dictDelChatInfo.ContainsKey(fItem.StrQQNum) OrElse dictDelChatInfo.ContainsKey(fItem.NickName)) Then

                If dictDelChatInfo.ContainsKey(fItem.NickName) Then
                    deleteInfos = dictDelChatInfo(fItem.NickName)
                ElseIf dictDelChatInfo.ContainsKey(fItem.StrQQNum) Then
                    deleteInfos = dictDelChatInfo(fItem.StrQQNum)
                End If

                'If fItem.NickName.Contains(mStrUnknown) Then
                '    deleteInfos = dictDelChatInfo(fItem.NickName)
                'Else
                '    deleteInfos = dictDelChatInfo(fItem.Md5)
                'End If
            End If
            'If fItem.NickName = WeixinImportOrExportHelper.mStrUnknownEx Then
            '    Dim lstTemp As List(Of String) = Me.GetUnknownEx(dictDelChatInfo)
            '    For Each strKey As String In lstTemp
            '        Dim lstChatInfoTemp As List(Of WeChatChatInfo) = New List(Of WeChatChatInfo)
            '        If dictDelChatInfo.ContainsKey(strKey) Then
            '            lstChatInfoTemp = dictDelChatInfo(strKey)
            '            For Each itemv As SociaChatBase In lstChatInfoTemp
            '                deleteInfos.Add(itemv)
            '            Next
            '        End If
            '    Next
            'End If
            If deleteInfos.Count = 0 AndAlso fItem.NickName.StartsWith(WeixinImportOrExportHelper.mStrUnknown & "_") Then
                Dim strTempName As String = fItem.NickName.Replace(WeixinImportOrExportHelper.mStrUnknown & "_", "")
                If dictDelChatInfo.ContainsKey(strTempName) Then
                    deleteInfos = dictDelChatInfo(strTempName)
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetDelectChatInfos")
        End Try
        Return deleteInfos
    End Function

    Private Sub ExportToHtmlEx(lstInfos As List(Of SociaChatBase), strFilePath As String, Item As SocialFriendInfoBase, strNickName As String, ByRef lstEHUserInfo As List(Of ExportHtmlUserInfo), strAttachment As String)
        Try
            Folder.CheckFolder(strFilePath)

            Dim strNum As String = ""
            Dim intType As Int32 = 0
            If TypeOf Item Is WeChatFriendInfo Then
                Dim wInfo As WeChatFriendInfo = CType(Item, WeChatFriendInfo)
                strNum = wInfo.Md5
                intType = wInfo.IntType
            Else
                Dim qInfo As QQFriendInfo = CType(Item, QQFriendInfo)
                strNum = qInfo.StrQQNum
            End If

            Me.ExportToHtml(lstInfos, strFilePath, strNum, strAttachment)

            Dim info As New ExportHtmlUserInfo
            info.userName = Item.UsrName
            info.nickName = strNickName
            info.type = intType
            info.msgTotal = lstInfos.Count
            lstEHUserInfo.Add(info)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExportToHtmlEx")
        End Try
    End Sub
    Private Sub ExportToHtml(lstInfos As List(Of SociaChatBase), strFilePath As String, str As String, strAttachment As String)
        Try
            Dim intPages As Integer = 1
            Dim intRowNumber As Integer = 0
            Dim dict As New Dictionary(Of String, String)
            Dim strKey As String = ""
            Dim JAData As New JsonArray
            Dim intFrom As Integer = 0 '0：对方，1：自己
            Dim intType As Integer = 0
            For Each cInfo As SociaChatBase In lstInfos
                Dim JAItem As New JsonArray
                ' JAItem.Add(cInfo.ID)
                JAItem.Add(cInfo.IntID)
                JAItem.Add(GetMessage(cInfo, strAttachment))

                If cInfo.SCType = SociaChatType.webpage Then ' = 49
                    Dim arrHtml5ContentForWeb As JsonArray = GetWebPageJsonArray(cInfo.StrMessage) '网页json
                    If arrHtml5ContentForWeb IsNot Nothing AndAlso arrHtml5ContentForWeb.Count Then
                        intType = 49 * 10
                    Else
                        intType = 49
                    End If
                Else
                    intType = 49
                End If

                JAItem.Add(intType)
                ' JAItem.Add(cInfo.CreateTime)
                JAItem.Add(WeixinHelper.ConvertPcTimeToWeixin(cInfo.DTCreateTime))

                If Not Me.IsReceivedMsg(cInfo) Then
                    intFrom = 1
                Else
                    intFrom = 0
                End If
                JAItem.Add(intFrom)
                JAItem.Add(cInfo.IntDes)
                JAItem.Add(IIf(cInfo.IsDelete, 1, 0)) '标志是否删除 1：是  0：否  
                JAData.Add(JAItem)

                If JAData.Count >= 100 Then
                    strKey = String.Format("msg_{0}_{1}.json", str, intPages)
                    If Not dict.ContainsKey(strKey) Then
                        dict.Add(strKey, GetPageContent(JAData, str, intPages))
                        JAData = New JsonArray
                        intPages = intPages + 1
                    End If
                End If
            Next

            strKey = String.Format("msg_{0}_{1}.json", str, intPages)
            If Not dict.ContainsKey(strKey) Then
                dict.Add(strKey, GetPageContent(JAData, str, intPages))
            End If

            For Each item As KeyValuePair(Of String, String) In dict
                Using writer As New StreamWriter(Path.Combine(strFilePath, item.Key), True, Encoding.UTF8)
                    writer.WriteLine(item.Value)
                    writer.Flush()
                    writer.Close()
                End Using
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExportToHtml")
        End Try
    End Sub

    Public Function GetMessage(cInfo As SociaChatBase, strAttachment As String) As String
        Dim strRelust As String = cInfo.StrMessage
        Try
            '微信： 1文本；3图片；34语音；42名片；43视频；47特殊表情；48地理位置；49网页；50视频未接通的消息；1000系统消息
            Select Case cInfo.SCType
                Case SociaChatType.Picture '"3" '图片
                    strRelust = Path.Combine(strAttachment, String.Format("{0}.png", cInfo.DTCreateTime.ToString("yyyyMMdd_HHmmss")))

                Case SociaChatType.Voice '"34" '语音
                    'strRelust = Path.Combine(strAttachment, String.Format("{0}.wav", cInfo.DTCreateTime.ToString("yyyyMMdd_HHmmss")))
                    strRelust = Path.Combine(strAttachment, String.Format("{0}.mp3", cInfo.DTCreateTime.ToString("yyyyMMdd_HHmmss")))

                Case SociaChatType.SmallVideo '"62" '小视频
                    strRelust = Path.Combine(strAttachment, String.Format("{0}.mp4", cInfo.DTCreateTime.ToString("yyyyMMdd_HHmmss")))

                Case SociaChatType.Video '"43" '视频
                    strRelust = Path.Combine(strAttachment, String.Format("{0}.mp4", cInfo.DTCreateTime.ToString("yyyyMMdd_HHmmss")))

            End Select

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetMessage")
        End Try
        Return strRelust
    End Function

    Private Sub GetChatUser(lstInfo As List(Of ExportHtmlUserInfo), strFilePath As String, strUserName As String)
        Try
            Dim strJson As String = ""
            Dim JOItem As JsonObject
            Dim writerJ As New iTong.CoreFoundation.JsonWriter()
            Dim JO As New JsonObject
            JO.Add("total", lstInfo.Count)
            JO.Add("version", WeixinHelper.ConvertPcTimeToWeixin(DateTime.Now))

            Dim JAData As New JsonArray
            For Each info As ExportHtmlUserInfo In lstInfo
                JOItem = New JsonObject
                JOItem.Add("id", info.id)
                JOItem.Add("userName", info.userName)
                JOItem.Add("nickName", info.nickName)
                JOItem.Add("type", info.type)
                JOItem.Add("headIcon", GetHeadIcon(info.userName))
                JOItem.Add("msgTotal", info.msgTotal)
                JAData.Add(JOItem)
            Next
            JO.Add("data", JAData)

            Dim JOOwner As New JsonObject
            JOOwner.Add("userName", strUserName)
            JOOwner.Add("headIcon", GetHeadIconUrl(Me.mUsrNameMyself))
            JO.Add("owner", JOOwner)

            JO.Write(writerJ)
            strJson = writerJ.ToString()

            strJson = String.Format("zs_chat_user({0})", strJson)
            strFilePath = Path.Combine(strFilePath, "user.json")
            Using writer As New StreamWriter(strFilePath, True, Encoding.UTF8)
                writer.WriteLine(strJson)
                writer.Flush()
                writer.Close()
            End Using
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetChatUser")
        End Try
    End Sub

    Private Function GetHeadIconUrl(strUserName As String) As String
        Dim iconUrl As String = ""
        Try
            Dim info As WeChatFriendInfo = Me.mDictFriendInfo(strUserName)
            If Not String.IsNullOrEmpty(info.StrProfile) Then
                iconUrl = Me.SearchXmlByKey(info.StrProfile, "HeadImgUrl")

                Dim isWeCaht6323 As Boolean = False
                If iconUrl.Length = 0 AndAlso info.StrProfile.Contains("http") Then
                    isWeCaht6323 = True
                    iconUrl = info.StrProfile.Substring(info.StrProfile.IndexOf("http"), info.StrProfile.LastIndexOf("http"))
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetHeadIconUrl")
        End Try
        Return iconUrl
    End Function

    Private Function GetHeadIcon(strUsrName As String, Optional ByVal isQQGroup As Boolean = False, Optional ByVal isDiscuss As Boolean = False) As String
        Dim strRelust As String = ""
        Try
            If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                Dim strName As String = Me.mWhatsAppHelper.GetIconPath(strUsrName)
                If strName.Length > 0 Then
                    strRelust = Me.mWhatsAppHelper.GetImageByJID(strName)
                Else
                    strRelust = Me.mWhatsAppHelper.GetImageByJID(strUsrName)
                End If

            ElseIf Me.mExportSocialType = ExportSocialType.QQ Then
                If isQQGroup Then
                    strRelust = String.Format("http://p.qlogo.cn/gh/{0}/{0}/100", strUsrName)
                ElseIf isDiscuss Then
                    strRelust = String.Format("http://q1.qlogo.cn/g?b=qq&nk={0}&s=100", 0)
                Else
                    strRelust = String.Format("http://q1.qlogo.cn/g?b=qq&nk={0}&s=100", strUsrName)
                End If

            Else
                If Me.mDictFriendInfo.ContainsKey(strUsrName) Then
                    Dim info As WeChatFriendInfo = Me.mDictFriendInfo(strUsrName)
                    If info.StrProfile IsNot Nothing Then
                        strRelust = info.StrProfile
                        If info.StrProfile.Contains("HeadImgUrl") Then
                            strRelust = Me.SearchXmlByKey(strRelust, "HeadImgUrl")
                        End If
                    End If
                End If

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetHeadIcon")
        End Try
        Return strRelust
    End Function

    Private Function SearchXmlByKey(ByVal text As String, ByVal key As String) As String
        Dim strReturn As String = ""
        Dim intStart As Integer = 0
        Dim intEnd As Integer = 0

        Dim strStart As String = String.Format("<{0}>", key)
        Dim strEnd As String = String.Format("</{0}>", key)

        intStart = InStr(text, strStart)

        If intStart > 0 Then
            intStart += strStart.Length
            intEnd = InStr(intStart, text, strEnd)

            If intEnd > intStart Then
                strReturn = Mid(text, intStart, intEnd - intStart)
            End If
        End If

        Return strReturn.Trim
    End Function

    Private Function GetPageContent(JAData As JsonArray, str As String, intPage As Integer) As String
        Dim strRelust As String = ""
        Try
            Dim strJson As String = ""
            Dim writer As New iTong.CoreFoundation.JsonWriter()
            Dim JO As New JsonObject
            JO.Add("total", JAData.Count)

            Dim JOFormat As New JsonObject

            JOFormat.Add("svrId", "int")
            JOFormat.Add("message", "string")
            JOFormat.Add("type", "int")
            JOFormat.Add("creatTime", "int")
            JOFormat.Add("From", "int")
            JOFormat.Add("Des", "int")
            JOFormat.Add("isDelete", "int")
            JO.Add("format", JOFormat)

            JO.Add("data", JAData)

            JO.Write(writer)
            strJson = writer.ToString()

            strRelust = String.Format("zs_chat_msg_{0}_{1}({2})", str, intPage, strJson)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetPageContent")
        End Try
        Return strRelust
    End Function

#End Region

#Region "--- 导出图片 ---"

    Private Sub DoExportPhotoByUserId(ByVal obj As Object, Optional ByVal strQQNum As String = "", Optional ByVal BFHelper As BackupFolderHelper = Nothing, Optional ByVal lstInfos As List(Of QQChatInfo) = Nothing, Optional ByVal strGroup_Wxid As String = "")
        Try
            Dim lstRowsFriendIfno As List(Of SocialFriendInfoBase) = obj(0)
            Dim strFilePath As String = obj(1)
            Dim lstTime As List(Of DateTime) = obj(2)
            Dim isHtml As Boolean = False
            Try
                If obj(3) IsNot Nothing Then
                    isHtml = True
                End If
            Catch
            End Try
            Dim dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)) = obj(4)
            Dim wInfo As WeChatFriendInfo = obj(5)

            Dim intCount As Integer = 0
            For Each Item As SocialFriendInfoBase In lstRowsFriendIfno
                '生成后的文件移动到指定目录下
                Dim strName As String = Utility.ReplaceWinIllegalName(Item.NickName).Trim
                If strName.Trim.Length <= 0 Then
                    strName = Utility.ReplaceWinIllegalName(Item.NoteName).Trim
                End If
                If strName.Trim.Length <= 0 Then
                    strName = Utility.ReplaceWinIllegalName(Item.UsrName).Trim
                End If
                strName = String.Format("{0}_{1}", strName, Me.mLanguage.GetString("Main.Button.Photo"))
                Dim strSavePath As String = Path.Combine(strFilePath, strName)
                If isHtml Then
                    strSavePath = strFilePath
                End If
                If strGroup_Wxid.Length > 0 Then
                    Dim strNickName As String = ""
                    If Me.JudgeIsChatRoom(Item.UsrName) Then
                        Dim MD5 As String = Me.GetFriendMD5(strGroup_Wxid)
                        Dim lstGroupFriendInfo As New List(Of WeChatFriendInfo)
                        WeixinImportOrExportHelper.GetGroupFriend(Path.Combine(Path.GetDirectoryName(Me.mWeixinMMDB.DbFile), "WCDB_Contact.sqlite"), Item.UsrName, Nothing, Me.mDictFriendInfo, lstGroupFriendInfo)
                        If lstGroupFriendInfo.Count > 0 Then
                            For Each wfi As WeChatFriendInfo In lstGroupFriendInfo
                                If wfi.Md5 = MD5 Then
                                    strNickName = IIf(wfi.GroupNoteName.Length > 0, wfi.GroupNoteName, IIf(wfi.NickName.Length > 0, wfi.NickName, wfi.NoteName))
                                    Exit For
                                End If
                            Next
                        End If
                    End If
                    If strNickName.Length = 0 Then
                        strNickName = Me.GetNickName(strGroup_Wxid)
                    End If
                    strSavePath = Path.Combine(strSavePath, Utility.ReplaceWinIllegalName(strNickName))
                End If
                If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                    intCount += Me.DownloadWhatsAppMedia(Item.StrID, SociaChatType.Picture, strSavePath, False)
                Else
                    If strQQNum.Length = 0 Then
                        intCount += Me.DownloadPhotoFromPhone(Item.UsrName, strSavePath, lstTime, dictDelChatInfo, wInfo, strGroup_Wxid)
                    Else
                        DownloadChatMediaFromPhone(lstInfos, False, QQMediaType.Picture, strQQNum, BFHelper, strSavePath, True)
                    End If
                End If
            Next
            'Me.ChangeStateText(String.Format(Me.mLanguage.GetString("Weixin.Message.ExportedPhotoCount"), intCount))     '"共导出{0}个图片"
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportPhotoByUserId")
        End Try

    End Sub

    Private Function DownloadWhatsAppMedia(ByVal intID As Integer, ByVal type As SociaChatType, ByVal strSPath As String, isAudioConvert As Boolean) As Integer
        Dim intCount As Integer = 0
        Dim lstMessage As List(Of WhatsAppMessage) = Me.mWhatsAppHelper.GetMessage(intID)
        Dim strMessage As String = String.Format(Me.mLanguage.GetString("Weixin.Message.ExportMsg"), Me.mLanguage.GetString("Common.Symbol.Colon") & Path.GetFileName(strSPath))

        Dim intCProgress As Integer = 0
        Dim intIndex As Integer = 0
        Dim strFile As String = "{0}.png"
        Select Case type
            Case SociaChatType.Picture
                strFile = "{0}.png"
            Case SociaChatType.Video
                strFile = "{0}.mp4"
            Case SociaChatType.Voice
                strFile = "{0}.opus"
        End Select

        For Each item As WhatsAppMessage In lstMessage
            Try
                intIndex = intIndex + 1
                Me.DoRaiseNewProgressEvent(intIndex, lstMessage.Count, "Media")
                If item.MessageType <> type Then
                    Continue For
                End If
                Folder.CheckFolder(strSPath)

                If type = SociaChatType.Voice Then
                    'Dim strFilePathOnPC As String = String.Format("{0}/Audio/{1}", String.Format("{0}WhatsApp", Folder.CacheFolder), )
                    'Dim strFolderTempOnPC As String = String.Format("{0}WhatsApp", Folder.CacheFolder)
                    If String.IsNullOrEmpty(Me.mPathWhatsAppOnPC_Temp) OrElse Not File.Exists(mPathWhatsAppOnPC_Temp) Then
                        Me.mPathWhatsAppOnPC_Temp = Folder.GetTempFilePath()
                        Folder.CheckFolder(Me.mPathWhatsAppOnPC_Temp)
                    End If
                    Dim strFileName As String = String.Format(strFile, item.ToJID & "_" & item.MessageDate.ToString("yyyyMMdd_HHmmss"))
                    Dim strFileOpusOnPc As String = Path.Combine(Path.Combine(mPathWhatsAppOnPC_Temp, Path.GetFileName(strSPath)), strFileName)
                    Folder.CheckFolder(Path.GetDirectoryName(strFileOpusOnPc))
                    Dim strFileInBackup = Me.mWhatsAppHelper.GetMediaPath(item)
                    If File.Exists(strFileInBackup) Then
                        File.Copy(strFileInBackup, strFileOpusOnPc, True)
                    End If

                    '下载失败
                    If Not File.Exists(strFileOpusOnPc) Then
                        Continue For
                    End If

                    Dim strOpusPath As String = Path.Combine(strSPath, Path.GetFileName(strFileOpusOnPc))
                    Dim strWavPath As String = Path.Combine(strSPath, Path.GetFileNameWithoutExtension(strFileOpusOnPc) & ".mp3")
                    'Dim strWavPath As String = Path.Combine(strSPath, Path.GetFileNameWithoutExtension(strFileOpusOnPc) & ".wav")
                    If isAudioConvert Then
                        If Not File.Exists(strWavPath) Then
                            MediaMaster.ConvertWhatsappMedia(strFileOpusOnPc, strWavPath)
                        End If

                        If File.Exists(strWavPath) Then
                            intCount += 1
                        End If
                    Else
                        File.Copy(strFileOpusOnPc, strOpusPath, True)
                        If File.Exists(strOpusPath) Then
                            intCount += 1
                        End If
                    End If

                Else
                    Folder.CheckFolder(strSPath)
                    Dim strFilePhotoOnPC As String = Path.Combine(strSPath, String.Format(strFile, item.MessageDate.ToString("yyyyMMdd_HHmmss")))
                    Dim strFileInBackup = Me.mWhatsAppHelper.GetMediaPath(item)
                    If item.Medias IsNot Nothing AndAlso item.Medias.Count > 0 Then
                        Try

                            If File.Exists(strFileInBackup) Then
                                File.Copy(strFileInBackup, strFilePhotoOnPC)
                            End If
                            intCount += 1
                        Catch ex As Exception
                            Common.LogException(ex.ToString(), "WeixinImportOrExportHelper_DownloadWhatsAppMedia1")
                        End Try
                    End If
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "WeixinImportOrExportHelper_DownloadWhatsAppMedia")
            End Try
        Next
        Return intCount
    End Function

    Private Function DownloadPhotoFromPhone(ByVal UName As String, ByVal strSPath As String, ByVal lstT As List(Of DateTime), ByVal dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), ByVal wInfo As WeChatFriendInfo, Optional ByVal strGroup_Wxid As String = "") As Integer
        '下载语音
        Dim strPathWeixinOnPhone = String.Format("/Documents/{0}", Common.ToHexString(Common.GetMd5Array(Me.mUsrNameMyself)))
        Dim strMD5 As String = Me.GetFriendMD5(UName)
        Dim strPathPhotoOnPhone As String = String.Format("{0}/Img/{1}", strPathWeixinOnPhone, strMD5)
        Dim strFilePhotoOnPhone As String = String.Empty
        Dim strFilePhotoOnPhoneTmp As String = String.Empty
        Dim strFilePhotoOnPC As String = String.Empty
        Dim intCount As Integer = 0
        Dim temp As Integer = 0

        Dim strMessage As String = String.Format(Me.mLanguage.GetString("Weixin.Message.ExportMsg"), Me.mLanguage.GetString("Common.Symbol.Colon") & Path.GetFileName(strSPath))
        'Me.ChangeStateText(strMessage & String.Format("{0}%", 0))
        Dim lstInfo As New List(Of WeChatChatInfo)
        If lstT.Count = 0 Then
            lstInfo = Me.mWeixinMMDB.Get_Chat_Info(UName)
        Else
            lstInfo = Me.mWeixinMMDB.Get_Chat_Info(UName, lstT(0), lstT(1))
        End If

        '-------------------------删除恢复的数据-------------------------
        GetDeleteDate(True, False, lstT, dictDelChatInfo, Nothing, wInfo, Nothing, lstInfo, Nothing)
        '--------------------------------------------------------------------

        If lstInfo.Count > 0 Then

            Dim intCProgress As Integer = 0

            Dim intIndex As Integer = 0
            For Each Item As SociaChatBase In lstInfo
                intIndex += 1
                Me.DoRaiseNewProgressEvent(intIndex, lstInfo.Count, Me.mLanguage.GetString("Main.Button.Camera"))
                Dim intPersend As Integer = intIndex * 100 / lstInfo.Count
                'Me.ChangeStateText(strMessage & String.Format("{0}%", intPersend))

                If Item.SCType <> SociaChatType.Picture Then ' 3
                    Continue For
                End If

                If strGroup_Wxid.Length > 0 Then
                    Dim isDo As Boolean = False

                    If Not IsReceivedMsgWCInfo(Item) Then
                        If strGroup_Wxid = mUsrNameMyself Then
                            isDo = True
                        End If
                    End If

                    If Item.StrMessage.Contains(strGroup_Wxid) Then
                        isDo = True
                    End If

                    If Not isDo Then
                        Continue For
                    End If
                End If

                Folder.CheckFolder(strSPath)
                'strFilePhotoOnPC = Path.Combine(strSPath, String.Format("{0}.png", Item.DTCreateTime.ToString("yyyyMMdd_HHmmss")))
                strFilePhotoOnPC = Path.Combine(strSPath, GetMediaFileName(Item, ".png"))
                strFilePhotoOnPhone = String.Format("{0}/{1}.pic", strPathPhotoOnPhone, Item.IntID)
                strFilePhotoOnPhoneTmp = String.Format("{0}/{1}.pic_thum", strPathPhotoOnPhone, Item.IntID)

                Me.DownFromPhone(strFilePhotoOnPhone, strFilePhotoOnPC)

                If Not File.Exists(strFilePhotoOnPC) Then
                    Me.DownFromPhone(strFilePhotoOnPhoneTmp, strFilePhotoOnPC)
                End If
                If File.Exists(strFilePhotoOnPC) Then
                    intCount += 1
                End If
            Next
        End If
        Return intCount
    End Function

    Protected Function DownFromPhone(ByVal strFileOnPhone As String, ByVal strFileOnPC As String) As Boolean
        Dim blnResult As Boolean = False

        If File.Exists(strFileOnPC) Then
            Dim infoPhone As iPhoneFileInfo = Nothing
            Dim infoPC As FileInfo = New FileInfo(strFileOnPC)

            Me.IsDirectory(strFileOnPhone, infoPhone)
            If infoPhone IsNot Nothing Then
                If infoPC.Length = infoPhone.st_size Then
                    blnResult = True
                Else
                    blnResult = Me.DownFromPhoneEx(strFileOnPhone, strFileOnPC)
                End If
            End If
        Else
            blnResult = Me.DownFromPhoneEx(strFileOnPhone, strFileOnPC)
        End If

        Return blnResult
    End Function

    Protected Function DownFromPhoneEx(ByVal strFileOnPhone As String, ByVal strFileOnPC As String) As Boolean
        If Me.CheckOpenByAfc2() Then
            Return CType(Me.mDevice, iPhoneDevice).DownFromPhone(CType(Me.mDevice, iPhoneDevice).DefaultAfcHandle, Me.mPathContainer & strFileOnPhone, strFileOnPC)

        ElseIf Me.mUIFileSharingEnabled Then
            'Common.LogException(String.Format("1.Download weixin files {0}  {1}   {2}", Me.mAfcHandler, strFileOnPhone, strFileOnPC))
            Return CType(Me.mDevice, iPhoneDevice).DownFromPhone(Me.mAfcHandler, strFileOnPhone, strFileOnPC)
        Else
            Dim blnResult As Boolean = False

            Try
                Dim strPath As String = FormatPath(strFileOnPhone)
                'Common.LogException(String.Format("2.Download weixin files {0}  {1}   {2}", strPath, strFileOnPhone, strFileOnPC))
                If File.Exists(strPath) Then
                    File.Copy(strPath, strFileOnPC, True)
                    blnResult = True
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "frmWeixin.DownFromPhoneEx")
            End Try

            Return blnResult
        End If
    End Function

    Private Function GetMediaFileName(ByVal info As SociaChatBase, ByVal strSuffix As String) As String
        Dim strFileName As String = ""
        Dim strDT As String = ""
        Dim strID As String = ""
        Dim strNane As String = ""
        Try
            strDT = info.DTCreateTime.ToString("yyyyMMddHHmmss")
            strID = info.IntID.ToString()

            If Not Me.IsReceivedMsg(info) Then
                strNane = Me.mUsrNameMyself
            Else

                Dim isChatRoom As Boolean = Me.JudgeIsChatRoom(info.StrUsrName)
                If isChatRoom Then
                    Dim values As String() = info.StrMessage.Split(New Char() {vbCr, vbLf})

                    If info.StrMessage.Contains("videomsg") Then
                        Try
                            If values.Length > 2 Then
                                Dim strVideomsg As String = values(2)
                                If strVideomsg.Contains("fromusername=") Then
                                    Dim strTemp As String = strVideomsg.Substring(strVideomsg.IndexOf("fromusername="), strVideomsg.Length - strVideomsg.IndexOf("fromusername="))
                                    strTemp = strTemp.Substring(strTemp.IndexOf("""") + 1)
                                    strNane = strTemp.Substring(0, strTemp.IndexOf(""""))
                                End If
                            ElseIf values.Length = 1 Then
                                Dim strVideomsg As String = values(0)
                                If strVideomsg.Contains("fromusername=") Then
                                    Dim strTemp As String = strVideomsg.Substring(strVideomsg.IndexOf("fromusername="), strVideomsg.Length - strVideomsg.IndexOf("fromusername="))
                                    strTemp = strTemp.Substring(strTemp.IndexOf("""") + 1)
                                    strNane = strTemp.Substring(0, strTemp.IndexOf(""""))
                                End If
                            End If
                        Catch
                        End Try
                    End If
                    If values.Length > 1 Then
                        If strNane.Length = 0 AndAlso values(0).EndsWith(":") Then
                            strNane = values(0).TrimEnd(":")
                        End If
                    End If
                Else
                    strNane = info.StrUsrName
                End If
            End If
            Dim strNewName As String = ""
            If Me.JudgeIsChatRoom(info.StrUsrName) Then
                Dim MD5 As String = Me.GetFriendMD5(strNane)
                Dim lstGroupFriendInfo As New List(Of WeChatFriendInfo)
                WeixinImportOrExportHelper.GetGroupFriend(Path.Combine(Path.GetDirectoryName(Me.mWeixinMMDB.DbFile), "WCDB_Contact.sqlite"), info.StrUsrName, Nothing, Me.mDictFriendInfo, lstGroupFriendInfo)
                If lstGroupFriendInfo.Count > 0 Then
                    For Each wfi As WeChatFriendInfo In lstGroupFriendInfo
                        If wfi.Md5 = MD5 Then
                            strNewName = IIf(wfi.GroupNoteName.Length > 0, wfi.GroupNoteName, IIf(wfi.NickName.Length > 0, wfi.NickName, wfi.NoteName))
                            Exit For
                        End If
                    Next
                End If
            End If
            If strNewName.Length = 0 Then
                strNane = GetNickName(strNane)
            Else
                strNane = strNewName
            End If
            strNane = strNane.Replace("-", "").Replace("/", "").Replace("\", "").Replace(":", "").Replace("*", "").Replace("?", "").Replace("""", "").Replace("<", "").Replace(">", "").Replace("|", "")

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetMediaFileName")
        Finally
            strFileName = String.Format("{0}_{1}_{2}{3}", strDT, strID, strNane, strSuffix)
        End Try
        Return strFileName
    End Function

    Protected Overridable Function FormatPath(ByVal strPath As String) As String
        If Me.mIsWeixinBackup OrElse Me.mIsWeixinAloneBackup Then
            Dim strNewPath As String = String.Format("/var/mobile/Applications/{1}/{0}", strPath.Trim("/"c), mWeChatSKU)
            If Me.mDictSha1.ContainsKey(strNewPath) Then
                Return Me.mDictSha1(strNewPath)
            Else
                Return ""
            End If
        Else
            Return mWeixinBackupCache & strPath.Replace("/", "\")
        End If
    End Function

    Protected Function IsDirectory(ByVal strPath As String, ByRef e As iPhoneFileInfo) As Boolean
        If Me.CheckOpenByAfc2() Then
            Return CType(Me.mDevice, iPhoneDevice).IsDirectory(CType(Me.mDevice, iPhoneDevice).DefaultAfcHandle, Me.mPathContainer & strPath, e)

        ElseIf Me.mUIFileSharingEnabled Then
            Return CType(Me.mDevice, iPhoneDevice).IsDirectory(Me.mAfcHandler, strPath, e)

        Else
            Dim strPathNew As String = FormatPath(strPath)
            If File.Exists(strPathNew) Then
                e = New iPhoneFileInfo(New FileInfo(strPathNew))
                Return True

            ElseIf Directory.Exists(strPathNew) Then
                e = New iPhoneFileInfo(New DirectoryInfo(strPathNew))
                Return True
            Else
                e = Nothing
                Return False
            End If
        End If
    End Function

    Protected Function CheckOpenByAfc2() As Boolean
        Return Me.mDevice IsNot Nothing AndAlso CType(Me.mDevice, iPhoneDevice).Jailbreaked AndAlso CType(Me.mDevice, iPhoneDevice).VersionNumber >= 830
    End Function

    Private Function GetFriendMD5(ByVal usrName As String) As String
        Dim md5 As String = String.Empty

        If String.IsNullOrEmpty(usrName) Then
            Return md5
        End If

        If Me.mDictFriendInfo IsNot Nothing Then
            If Me.mDictFriendInfo.ContainsKey(usrName) Then
                Dim item As WeChatFriendInfo = Me.mDictFriendInfo(usrName)
                md5 = item.Md5
            End If
        End If

        If String.IsNullOrEmpty(md5) Then
            md5 = Common.GetHashString(usrName)
        End If

        Return md5
    End Function

#End Region

#Region "--- 导出语音 ---"

    Private Sub DoExportAudioByUserId(ByVal obj As Object, ByVal isAudioConvert As Boolean, Optional ByVal strQQNum As String = "", Optional ByVal BFHelper As BackupFolderHelper = Nothing, Optional ByVal lstInfos As List(Of QQChatInfo) = Nothing, Optional ByVal strGroup_Wxid As String = "")
        Try
            Dim lstRowsFriendIfno As List(Of SocialFriendInfoBase) = obj(0)
            Dim strFilePath As String = obj(1)
            Dim lstTime As List(Of DateTime) = obj(2)
            Dim isHtml As Boolean = False
            Try
                If obj(3) IsNot Nothing Then
                    isHtml = True
                End If
            Catch
            End Try
            Dim dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)) = obj(4)
            Dim wInfo As WeChatFriendInfo = obj(5)

            Dim intErrCount As Integer = 0
            Dim intCount As Integer = 0
            For Each Item As SocialFriendInfoBase In lstRowsFriendIfno
                '生成后的文件移动到指定目录下
                Dim strName As String = Utility.ReplaceWinIllegalName(Item.NickName).Trim
                If strName.Trim.Length <= 0 Then
                    strName = Utility.ReplaceWinIllegalName(Item.NoteName).Trim
                End If
                If strName.Trim.Length <= 0 Then
                    strName = Utility.ReplaceWinIllegalName(Item.UsrName).Trim
                End If
                strName = String.Format("{0}_{1}", strName, Me.mLanguage.GetString("Weixin.Item.Audio"))
                Dim strSavePath As String = Path.Combine(strFilePath, strName)
                If isHtml Then
                    strSavePath = strFilePath
                End If
                If strGroup_Wxid.Length > 0 Then
                    strSavePath = Path.Combine(strSavePath, Utility.ReplaceWinIllegalName(Me.GetNickName(strGroup_Wxid)))
                End If
                If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                    intCount += Me.DownloadWhatsAppMedia(Item.StrID, SociaChatType.Voice, strSavePath, isAudioConvert)
                Else
                    If strQQNum.Length = 0 Then
                        intCount += Me.DownloadAudioFromPhone(Item.UsrName, strSavePath, intErrCount, lstTime, isAudioConvert, dictDelChatInfo, wInfo, strGroup_Wxid)
                    Else
                        DownloadChatMediaFromPhone(lstInfos, False, QQMediaType.Audio, strQQNum, BFHelper, strSavePath, True)
                    End If
                End If
            Next
            Dim strMsg As String = String.Format(Me.mLanguage.GetString("Weixin.Message.ExportedAudioCount"), intCount)
            If intErrCount > 0 Then
                strMsg = strMsg & String.Format(" {0}:{1} {2}", Me.mLanguage.GetString("Download.Label.Failed"), intErrCount, String.Format(Me.mLanguage.GetString("Download.Message.ContactMe"), Utility.TongbuContact, Utility.TongbuTel))
            End If
            'Me.ChangeStateText(strMsg)     '"共导出{0}个语音"

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportAudioByUserId")
        End Try
    End Sub

    Private Function DownloadAudioFromPhone(UName As String, strSPath As String, ByRef intECount As Integer, lstT As List(Of DateTime), isAudioConvert As Boolean, ByVal dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), ByVal wInfo As WeChatFriendInfo, Optional ByVal strGroup_Wxid As String = "") As Integer
        '下载语音
        Dim strMD5 As String = Me.GetFriendMD5(UName)
        Dim strPathAudioOnPhone As String = String.Format("{0}/Audio/{1}", Me.mPathWeixinOnPhone, strMD5)
        Dim strFileAudioOnPhone As String = String.Empty
        Dim strPathAudioOnPC As String = String.Format("{0}\Audio\{1}", Me.mPathWeixinOnPC_Temp, strMD5)
        Dim strFileAudioOnPC As String = String.Empty

        Dim strMessage As String = String.Format(Me.mLanguage.GetString("Weixin.Message.ExportMsg"), Me.mLanguage.GetString("Common.Symbol.Colon") & Path.GetFileName(strSPath))
        'Me.ChangeStateText(strMessage & String.Format("{0}%", 0))
        Dim intCount As Integer = 0

        Dim lstInfo As New List(Of WeChatChatInfo)
        If lstT Is Nothing OrElse lstT.Count = 0 Then
            lstInfo = Me.mWeixinMMDB.Get_Chat_Info(UName)
        Else
            lstInfo = Me.mWeixinMMDB.Get_Chat_Info(UName, lstT(0), lstT(1))
        End If

        '-------------------------删除恢复的数据-------------------------
        GetDeleteDate(True, False, lstT, dictDelChatInfo, Nothing, wInfo, Nothing, lstInfo, Nothing)
        '--------------------------------------------------------------------

        If lstInfo.Count = 0 Then
            GoTo DoExit
        End If

        Try
            If Directory.Exists(Folder.WeChatAud2Wav) Then
                Directory.Delete(Folder.WeChatAud2Wav, True)
            End If
        Catch
        End Try

        Dim intIndex As Integer = 0

        Dim intCProgress As Integer = 0
        Dim temp As Integer = 0

        Dim intDoEvents As Integer = 0

        For Each Item As SociaChatBase In lstInfo
            intIndex += 1
            Me.DoRaiseNewProgressEvent(intIndex, lstInfo.Count, Me.mLanguage.GetString("Weixin.Item.Audio"))
            Dim intPersend As Integer = intIndex * 100 / lstInfo.Count
            'Me.ChangeStateText(strMessage & String.Format("{0}%", intPersend))

            If Item.SCType <> SociaChatType.Voice Then ' <> 34              
                Continue For
            End If

            If strGroup_Wxid.Length > 0 Then
                Dim isDo As Boolean = False

                If Not IsReceivedMsgWCInfo(Item) Then
                    If strGroup_Wxid = mUsrNameMyself Then
                        isDo = True
                    End If
                End If

                If Item.StrMessage.Contains(strGroup_Wxid) Then
                    isDo = True
                End If

                If Not isDo Then
                    Continue For
                End If
            End If

            Folder.CheckFolder(strSPath)
            Dim strFileName As String = String.Format("{0}_{1}", Item.StrUsrName, Item.DTCreateTime.ToString("yyyyMMdd_HHmmss"))
            strFileName = GetMediaFileName(Item, "")

            strFileAudioOnPhone = String.Format("{0}/{1}.aud", strPathAudioOnPhone, Item.IntID)

            '不用转换格式
            'If Not isAudioConvert Then
            '    Dim strSavePath As String = Path.Combine(strSPath, strFileName & ".aud")
            '    Me.DownloadAudioFromPhone(strFileAudioOnPhone, strSavePath, intECount, lstT, False, dictDelChatInfo, wInfo)
            '    Continue For
            'End If

            '处理转换格式

            Folder.CheckFolder(strPathAudioOnPC)
            strFileAudioOnPC = String.Format("{0}\{1}.amr", strPathAudioOnPC, Item.IntID)
            Dim isDownload2Phone As Boolean = False
            If Not File.Exists(strFileAudioOnPC) Then
                '下载语音到本地
                If Me.DownloadAudioFromPhoneEx(strFileAudioOnPhone, strFileAudioOnPC, Nothing, Nothing, False) Then
                    isDownload2Phone = True
                Else
                    isDownload2Phone = False
                End If
            End If

            '如果从Phone 到本地下载失败 后面的操作就没有必要做
            If Not File.Exists(strFileAudioOnPC) Then
                Dim strErrMsga As String = String.Format("{0} {1}", strFileAudioOnPhone, strFileAudioOnPC)
                Common.LogException(strErrMsga, " 从Phone 到本地下载失败 可能是目标文件不存在")
                Continue For
            End If

            Dim strWavPath As String = Path.Combine(strPathAudioOnPC, Path.GetFileNameWithoutExtension(strFileAudioOnPC) & ".wav")
            Dim strMp3Path As String = Path.Combine(strPathAudioOnPC, Path.GetFileNameWithoutExtension(strFileAudioOnPC) & ".mp3")

            If isAudioConvert Then
                Try
                    If Not File.Exists(strMp3Path) Then
                        WeixinHelper.Wav2Mp3(strFileAudioOnPC, strWavPath, strMp3Path, "2013042508181124", Item.StrMessage)
                    End If
                Catch ex As Exception
                    Dim strErrMsga As String = String.Format("{0} {1} {2}", ex.ToString(), strFileAudioOnPC, Item.StrMessage)
                    Common.LogException(strErrMsga, "转换格式出错")

                    Try
                        '格式转换错误 判断是否要收集错误的数据 以便分析错误
                        Folder.CheckFolder(Folder.WeChatAud2Wav)
                        Dim strTempPath As String = Path.Combine(Folder.WeChatAud2Wav, Path.GetFileName(strFileAudioOnPhone))
                        Me.DownloadAudioFromPhone(strFileAudioOnPhone, strTempPath, intECount, lstT, False, dictDelChatInfo, wInfo)
                        Common.LogException(String.Format("源文件:{0},备份到:{1}", strFileAudioOnPhone, strTempPath), "转换格式出错--导出失败源文件")
                    Catch ex1 As Exception
                        Common.LogException(ex1.ToString(), "转换格式出错")
                    End Try
                End Try
                Try
                    If File.Exists(strMp3Path) Then
                        Dim strSaveFileName As String = Path.Combine(strSPath, String.Format("{0}.mp3", strFileName)) 'Item.CreateTime.ToString("yyyyMMdd_HHmmss")
                        File.Copy(strMp3Path, strSaveFileName, True)
                        intCount += 1
                    Else
                        intECount += 1
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DownloadAudioFromPhone.Copy")
                End Try

            Else
                If File.Exists(strFileAudioOnPC) Then
                    Dim strAudPath As String = Path.Combine(strSPath, String.Format("{0}.aud", strFileName))
                    File.Copy(strFileAudioOnPC, strAudPath, True)
                    intCount += 1
                Else
                    intECount += 1
                End If

            End If

            If intDoEvents = 30 Then
                intDoEvents = 0
                Application.DoEvents()
            End If
        Next
DoExit:
        Application.DoEvents()
        Return intCount
    End Function

    Private intSize As Integer = 0
    Private mDataTransfer_Buffer_Size As Integer = (2 * 1024 * 1024) - 1
    Private Function DownloadAudioFromPhoneEx(ByVal fileOnPhone As String, ByVal fileOnPC As String, ByVal progressCallback As DataTransferEventHandler, ByRef succeedListFile As List(Of String), ByRef blnCancel As Boolean) As Boolean
        Dim isTryAgain As Boolean = True
Do_TryAgain:
        Dim buffer As Byte() = New Byte(mDataTransfer_Buffer_Size) {}
        Dim blnReturn As Boolean = True
        Dim strErrMsg As String = ""

        If System.IO.File.Exists(fileOnPC) Then
            Try
                System.IO.File.Delete(fileOnPC)
            Catch ex As Exception
                Return False
            End Try
        End If

        If Not Me.Exists(fileOnPhone) Then
            Return False
        End If

        '取得来源文件的文件名和路径
        Dim sSourcePath As String = Strings.Left(fileOnPhone, Strings.InStrRev(fileOnPhone, "/", -1, CompareMethod.Binary))
        Dim sSourceFile As String = Strings.Mid(fileOnPhone, (Strings.InStrRev(fileOnPhone, "/", -1, CompareMethod.Binary) + 1))
        Dim info As iPhoneFileInfo = Nothing

        If fileOnPC.EndsWith("\") Then
            fileOnPC = fileOnPC & Utility.ReplaceWinIllegalName(sSourceFile)
        End If

        '如果下载的是文件
        If Not Me.IsDirectory(fileOnPhone, info) Then
            If info Is Nothing Then
                GoTo DO_EXIT
            End If

            Dim e As New DataTransferEventArgs(fileOnPhone, fileOnPC)

            Try

                If info.LinkTarget.Length > 0 Then
                    If info.LinkTarget.StartsWith("/") Then
                        fileOnPhone = info.LinkTarget
                    Else
                        fileOnPhone = Utility.GetDirectoryName(fileOnPhone) & "/" & info.LinkTarget
                    End If
                End If

                Using stream As System.IO.FileStream = New FileStream(fileOnPC, FileMode.Create)
                    Dim hFile As Object = FileOpen(fileOnPhone)
                    Dim iRead As Integer = FileRead(hFile, buffer, 0, buffer.Length)

                    Dim dblTimer As Double = DateAndTime.Timer
                    Dim lngBuffer As Long = 0
                    Dim intSpeed As Integer = 0

                    'Dim bufferHeader As Byte() = New Byte() {&H23, &H21, &H41, &H4D, &H52, &HA}
                    'stream.Write(bufferHeader, 0, 6)

                    e.FileSize = Me.FileSize(fileOnPhone)
                    '判断是不是silk文件,如果是aud文件就把头写进去，如果是silk文件就不写头 
                    If buffer.Length > 7 AndAlso Not buffer(3) = &H53 AndAlso Not buffer(4) = &H49 AndAlso Not buffer(5) = &H4C AndAlso Not buffer(6) = &H4B Then
                        Dim bufferHeader As Byte() = New Byte() {&H23, &H21, &H41, &H4D, &H52, &HA}
                        stream.Write(bufferHeader, 0, 6)
                    End If

                    Try
                        Do While iRead > 0
                            If progressCallback IsNot Nothing Then
                                '=== 计算传输速度、更新状态 =========================================
                                lngBuffer += iRead
                                Dim dblTimePast As Double = DateAndTime.Timer - dblTimer
                                If dblTimePast >= 1 Then
                                    intSpeed = CInt(lngBuffer / dblTimePast)
                                    dblTimer = DateAndTime.Timer
                                    lngBuffer = 0
                                End If

                                e.Speed = intSpeed
                                e.LastTransSize = iRead
                                '==========================================================

                                progressCallback(e)

                                '用户是否放弃传输
                                If e.Cancel Then
                                    blnCancel = True
                                    blnReturn = False
                                    Exit Do
                                End If
                            End If

                            stream.Write(buffer, 0, iRead)
                            e.TransSize += iRead

                            iRead = FileRead(hFile, buffer, 0, buffer.Length)
                        Loop
                        intSize = intSize + buffer.Length
                        FileClose(hFile)
                        e.LastTransSize = 0
                        e.TransSize = e.FileSize

                    Catch ex As Exception
                        If hFile <> IntPtr.Zero Then
                            FileClose(hFile)
                        End If

                        blnReturn = False
                    End Try
                End Using

                If e.Cancel Then
                    Try
                        If File.Exists(fileOnPC) Then
                            File.Delete(fileOnPC)
                        End If
                    Catch ex As Exception
                    End Try
                End If

            Catch ex As Exception
                Try
                    If ex.Message.Contains("AFCFileRefOpen failed with error") AndAlso isTryAgain Then
                        Me.mAfcHandler = CType(Me.mDevice, iPhoneDevice).GetAfcByFileSharing(mWeChatSKU, HouseArrestType.Document, True)
                        isTryAgain = False
                        GoTo Do_TryAgain
                    End If

                    '当下载失败时，如果下载的文件大小为0，则直接删除空文件
                    If File.Exists(fileOnPC) AndAlso New FileInfo(fileOnPC).Length = 0 Then
                        File.Delete(fileOnPC)
                    End If
                Catch exIO As Exception
                End Try
                strErrMsg = ex.Message

                blnReturn = False
            End Try

            If progressCallback IsNot Nothing Then
                progressCallback(e)
            End If

            If blnReturn AndAlso succeedListFile IsNot Nothing Then
                '添加已经下载成功的文件列表

                succeedListFile.Add(fileOnPC)
            End If

        Else
            If info IsNot Nothing AndAlso Not info.isDirectory Then
                Dim strFiles As String = Me.FormatPath(fileOnPhone)
                If File.Exists(strFiles) Then
                    Me.DownFromPhoneEx(fileOnPhone, fileOnPC)
                End If
            Else
                '如果下载的是目录
                If Not System.IO.Directory.Exists(fileOnPC) Then
                    System.IO.Directory.CreateDirectory(fileOnPC)
                End If
            End If

            '添加要下载的目录
            If succeedListFile IsNot Nothing Then
                succeedListFile.Add(fileOnPC)
            End If

            Dim strOnComputer As String = (fileOnPC & "\")
            For Each strfile As String In Me.GetFiles(fileOnPhone, True)
                If Not Me.DownloadAudioFromPhoneEx(fileOnPhone.TrimEnd("/"c) & "/" & strfile, strOnComputer, progressCallback, succeedListFile, blnCancel) Then
                    blnReturn = False
                End If

                If blnCancel Then
                    GoTo DO_EXIT
                End If
            Next

        End If

DO_EXIT:
        buffer = Nothing
        '全部传送成功才算成功，如果有任意一个或多个文件传送失败，那么就会返回 False
        Return blnReturn
    End Function
    Protected Function GetFiles(ByVal strPath As String, ByVal includeDirectory As Boolean) As String()
        If Me.CheckOpenByAfc2() Then
            Return CType(Me.mDevice, iPhoneDevice).GetFiles(CType(Me.mDevice, iPhoneDevice).DefaultAfcHandle, Me.mPathContainer & strPath, includeDirectory)

        ElseIf Me.mUIFileSharingEnabled Then
            Return CType(Me.mDevice, iPhoneDevice).GetFiles(Me.mAfcHandler, strPath, includeDirectory)

        ElseIf Me.mIsWeixinBackup OrElse Me.mIsWeixinAloneBackup Then
            Return Me.GetFilesByBackup(strPath)

        Else
            Dim list As New List(Of String)

            Dim strPathNew As String = FormatPath(strPath)
            If Directory.Exists(strPathNew) Then
                For Each strFile As String In Directory.GetFiles(strPathNew)
                    list.Add(Path.GetFileName(strFile))
                Next
                For Each strDir As String In Directory.GetDirectories(strPathNew)
                    list.Add(Path.GetFileName(strDir))
                Next
            End If

            Return list.ToArray()
        End If
    End Function

    Private Function GetFilesByBackup(ByVal strPath As String) As String()
        Dim lstItems As New List(Of String)

        'Dim strDir As String = String.Format("/var/mobile/Applications/com.tencent.xin/{0}/", strPath.Trim("/"c))
        Dim strDir As String = String.Format("/var/mobile/Applications/{1}/{0}/", strPath.Trim("/"c), mWeChatSKU)
        For Each item As MBFileRecord In Me.mLstMBFileRecord
            If item.PathOnPhone Is Nothing Then
                Continue For
            End If
            '/var/mobile/ 与 /var/mobile
            If Not item.PathOnPhone.StartsWith(strDir) OrElse item.PathOnPhone.TrimEnd("/").Length <= strDir.TrimEnd("/").Length Then
                Continue For
            End If

            If Me.CheckInFolder(strDir, item.PathOnPhone) Then
                '文件夹mode:16877 16895 16872 16832
                If Me.IsFolder(item) Then
                    Dim strNewDir As String = item.PathOnPhone.TrimEnd("/"c) & "/"
                    If lstItems.Contains(strNewDir) Then
                        Continue For
                    End If

                    lstItems.Add(Utility.GetFileName(strNewDir))
                Else
                    lstItems.Add(Utility.GetFileName(item.PathOnPhone))
                End If
            End If
        Next

        Return lstItems.ToArray()
    End Function

    Private Function IsFolder(ByVal record As MBFileRecord)
        Dim blnIsFolder As Boolean = False
        If (record.Mode = 16877 OrElse record.Mode = 16895 OrElse record.Mode = 16895 OrElse record.Mode = 16872 OrElse record.Mode = 16832 OrElse record.Mode = 16841) AndAlso record.FileLength = 0 Then
            blnIsFolder = True
        End If
        Return blnIsFolder
    End Function

    Private Function CheckInFolder(ByVal dirPath As String, ByVal subPath As String) As Boolean
        Dim isSubPath As String = False
        Dim strNewPath As String = subPath.TrimEnd("/").Replace(dirPath.TrimEnd("/") & "/", "")
        If Not strNewPath.Contains("/") Then
            isSubPath = True
        End If
        Return isSubPath
    End Function

    Protected Function FileSize(ByVal strPath As String) As Long
        If Me.CheckOpenByAfc2() Then
            Return CType(Me.mDevice, iPhoneDevice).FileSize(CType(Me.mDevice, iPhoneDevice).DefaultAfcHandle, Me.mPathContainer & strPath)

        ElseIf Me.mUIFileSharingEnabled Then
            Return CType(Me.mDevice, iPhoneDevice).FileSize(Me.mAfcHandler, strPath)

        Else
            Dim strPathNew As String = FormatPath(strPath)
            If File.Exists(strPathNew) Then
                Return New FileInfo(strPath).Length
            End If
        End If
    End Function

    Protected Function FileRead(ByVal hFile As Object, ByVal arrData As Byte(), ByVal index As Integer, ByVal count As Integer) As Integer
        If TypeOf hFile Is iPhoneFile Then
            Return CType(hFile, iPhoneFile).Read(arrData, index, count)
        Else
            Return CType(hFile, FileStream).Read(arrData, index, count)
        End If
    End Function

    Protected Function FileOpen(ByVal strFile As String) As Object
        If Me.CheckOpenByAfc2() Then
            Return iPhoneFile.OpenRead(Me.mDevice, CType(Me.mDevice, iPhoneDevice).DefaultAfcHandle, Me.mPathContainer & strFile)
        ElseIf Me.mUIFileSharingEnabled Then
            Return iPhoneFile.OpenRead(Me.mDevice, Me.mAfcHandler, strFile)
        Else
            Return New FileStream(FormatPath(strFile), FileMode.OpenOrCreate, FileAccess.ReadWrite, FileShare.ReadWrite)
        End If
    End Function

    Protected Function Exists(ByVal strPath As String) As Boolean
        If Me.CheckOpenByAfc2() Then
            Return CType(Me.mDevice, iPhoneDevice).Exists(CType(Me.mDevice, iPhoneDevice).DefaultAfcHandle, Me.mPathContainer & strPath)

        ElseIf Me.mUIFileSharingEnabled Then
            Return CType(Me.mDevice, iPhoneDevice).Exists(Me.mAfcHandler, strPath)

        Else
            Dim strPathNew As String = FormatPath(strPath)
            If File.Exists(strPathNew) Then
                Return True
            ElseIf Directory.Exists(strPathNew) Then
                Return True
            Else
                'Dim strNewPath As String = String.Format("/var/mobile/Applications/com.tencent.xin/{0}/", strPath.Trim("/"c))
                Dim strNewPath As String = String.Format("/var/mobile/Applications/{1}/{0}/", strPath.Trim("/"c), mWeChatSKU)
                If Me.mListDir.Contains(strNewPath) Then
                    Return True
                Else
                    Return False
                End If
            End If
        End If
    End Function

#End Region

#Region "--- 导出小视频 ---"

    Private Sub DoExportVideoByUserId(ByVal obj As Object, Optional ByVal strQQNum As String = "", Optional ByVal BFHelper As BackupFolderHelper = Nothing, Optional ByVal lstInfos As List(Of QQChatInfo) = Nothing, Optional ByVal strGroup_Wxid As String = "")
        Try
            Dim intCount As Integer = 0
            Dim strFilePath As String = obj(1)
            Dim lstRowsFriendIfno As List(Of SocialFriendInfoBase) = obj(0)
            Dim lstTime As List(Of DateTime) = obj(2)
            Dim isHtml As Boolean = False
            Try
                If obj(3) IsNot Nothing Then
                    isHtml = True
                End If
            Catch
            End Try
            Dim dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)) = obj(4)
            Dim wInfo As WeChatFriendInfo = obj(5)

            For Each Item As SocialFriendInfoBase In lstRowsFriendIfno
                '生成后的文件移动到指定目录下
                Dim strName As String = Utility.ReplaceWinIllegalName(Item.NickName).Trim
                If strName.Trim.Length <= 0 Then
                    strName = Utility.ReplaceWinIllegalName(Item.NoteName).Trim
                End If
                If strName.Trim.Length <= 0 Then
                    strName = Utility.ReplaceWinIllegalName(Item.UsrName).Trim
                End If
                strName = String.Format("{0}_{1}", strName, Me.mLanguage.GetString("Weixin.Lable.SmallVideo"))
                Dim strSavePath As String = Path.Combine(strFilePath, strName)
                If isHtml Then
                    strSavePath = strFilePath
                End If

                If strGroup_Wxid.Length > 0 Then
                    strSavePath = Path.Combine(strSavePath, Utility.ReplaceWinIllegalName(Me.GetNickName(strGroup_Wxid)))
                End If

                If Me.mExportSocialType = ExportSocialType.WhatsApp Then
                    intCount += Me.DownloadWhatsAppMedia(Item.StrID, SociaChatType.Video, strSavePath, False)
                Else
                    If strQQNum.Length = 0 Then
                        intCount += Me.DownloadVideoFromPhone(Item.UsrName, strSavePath, lstTime, dictDelChatInfo, wInfo, strGroup_Wxid)
                    Else
                        DownloadChatMediaFromPhone(lstInfos, False, QQMediaType.Video, strQQNum, BFHelper, strSavePath, True)
                    End If
                End If
            Next
            'Me.ChangeStateText(String.Format(Me.mLanguage.GetString("Weixin.Message.ExportedVideoCount"), intCount))         '"共导出{0}个小视频"
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportVideoByUserId")
        End Try

    End Sub

    Private Function IsReceivedMsgWCInfo(ByVal chat As WeChatChatInfo) As Boolean
        Dim blnReceive As Boolean = True

        If String.Compare(chat.StrUsrName, Me.mUsrNameMyself) = 0 OrElse String.Compare(chat.IntDes, "0") = 0 Then
            blnReceive = False
        End If

        Return blnReceive
    End Function

    Private Function DownloadVideoFromPhone(UsrName As String, strSavePath As String, lstTime As List(Of DateTime), dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), wInfo As WeChatFriendInfo, Optional ByVal strGroup_Wxid As String = "") As Integer
        '下载视频
        Dim strMD5 As String = Me.GetFriendMD5(UsrName)
        Dim strPathVideoOnPhone As String = String.Format("{0}/Video/{1}", Me.mPathWeixinOnPhone, strMD5)
        Dim strFileVideoOnPhone As String = String.Empty
        Dim strFileVideoOnPC As String = String.Empty
        Dim intCount As Integer = 0

        Dim strMessage As String = String.Format(Me.mLanguage.GetString("Weixin.Message.ExportMsg"), Me.mLanguage.GetString("Common.Symbol.Colon") & Path.GetFileName(strSavePath))
        'Me.ChangeStateText(strMessage & String.Format("{0}%", 0))

        Dim lstInfo As New List(Of WeChatChatInfo)
        If lstTime.Count = 0 Then
            lstInfo = Me.mWeixinMMDB.Get_Chat_Info(UsrName)
        Else
            lstInfo = Me.mWeixinMMDB.Get_Chat_Info(UsrName, lstTime(0), lstTime(1))
        End If

        '-------------------------删除恢复的数据-------------------------
        GetDeleteDate(True, False, lstTime, dictDelChatInfo, Nothing, wInfo, Nothing, lstInfo, Nothing)
        '--------------------------------------------------------------------

        If lstInfo.Count > 0 Then

            Dim intIndex As Integer = 0
            Dim intCProgress As Integer = 0
            Dim temp As Integer = 0

            For Each Item As SociaChatBase In lstInfo
                intIndex += 1
                Me.DoRaiseNewProgressEvent(intIndex, lstInfo.Count, Me.mLanguage.GetString("Main.Button.Movie"))
                'Me.ChangeStateText(strMessage & String.Format("{0}%", intPersend))
                If Item.SCType <> SociaChatType.SmallVideo AndAlso Item.SCType <> SociaChatType.Video Then '62   43
                    Continue For
                End If

                If strGroup_Wxid.Length > 0 Then
                    Dim isDo As Boolean = False

                    If Not IsReceivedMsgWCInfo(Item) Then
                        If strGroup_Wxid = mUsrNameMyself Then
                            isDo = True
                        End If
                    End If

                    If Item.StrMessage.Contains(strGroup_Wxid) Then
                        isDo = True
                    End If

                    If Not isDo Then
                        Continue For
                    End If
                End If

                Folder.CheckFolder(strSavePath)
                ' strFileVideoOnPC = Path.Combine(strSavePath, String.Format("{0}.mp4", Item.DTCreateTime.ToString("yyyyMMdd_HHmmss")))
                strFileVideoOnPC = Path.Combine(strSavePath, GetMediaFileName(Item, ".mp4"))
                strFileVideoOnPhone = String.Format("{0}/{1}.mp4", strPathVideoOnPhone, Item.IntID)

                Me.DownFromPhone(strFileVideoOnPhone, strFileVideoOnPC)
                If File.Exists(strFileVideoOnPC) Then
                    intCount += 1
                End If
            Next
        End If
        Return intCount
    End Function

#End Region

#Region "--- QQ媒体导出 ---"

    Private Function GetQQFileDevicePath(ByVal strUserNum As String, ByVal mediaType As QQMediaType, ByVal isThum As Boolean, ByVal isOriginal As Boolean)
        Dim strPathOnPhone As String = ""
        Select Case mediaType
            Case QQMediaType.Picture
                If isThum Then
                    strPathOnPhone = String.Format("/Documents/{0}/image_thumbnail/", strUserNum)
                ElseIf isOriginal Then
                    strPathOnPhone = String.Format("/Documents/{0}/image_original/", strUserNum)
                Else
                    strPathOnPhone = String.Format("/Documents/{0}/image/", strUserNum)
                End If
            Case QQMediaType.Video
                strPathOnPhone = String.Format("/Documents/{0}/ShortVideo/", strUserNum)

            Case QQMediaType.Audio
                strPathOnPhone = String.Format("/Documents/{0}/Audio/", strUserNum)

        End Select
        Return strPathOnPhone
    End Function

    Private Function GetQQFilePCPath(ByVal strUserNum As String, ByVal strPathQQOnPC_Temp As String, ByVal mediaType As QQMediaType)
        Dim strMType As String = "Img"
        Select Case mediaType
            Case QQMediaType.Picture
                strMType = "Img"

            Case QQMediaType.Video
                strMType = "Video"

            Case QQMediaType.Audio
                strMType = "Audio"

        End Select

        Dim strPathOnPC As String = String.Format("{0}\{1}\{2}", strPathQQOnPC_Temp, strMType, strUserNum)
        Folder.CheckFolder(strPathOnPC)
        Return strPathOnPC
    End Function

    Public Sub DownloadChatMediaFromPhone(ByVal lstInfos As List(Of QQChatInfo), ByVal blnThum As Boolean, ByVal mType As QQMediaType, ByVal strUserNum As String, ByVal iBackupFolderHelper As BackupFolderHelper, ByVal strPathQQOnPC_Temp As String, Optional ByVal IsPng As Boolean = False)
        Try
            Dim strPathOnPhone As String = ""
            Dim strPathOnPC As String = Me.GetQQFilePCPath(strUserNum, strPathQQOnPC_Temp, mType)

            Dim strFileName As String = ""
            Dim strFileOnPhone As String = ""
            Dim strFileOnPC As String = ""
            Dim intIndex As Integer = 0
            For Each item As SociaChatBase In lstInfos
                intIndex += 1
                Me.DoRaiseNewProgressEvent(intIndex, lstInfos.Count, Me.mLanguage.GetString("Main.Group.Media"))

                If item.SCType = SociaChatType.Picture OrElse item.SCType = SociaChatType.SmallVideo OrElse item.SCType = SociaChatType.Video OrElse item.SCType = SociaChatType.Voice Then

                    strFileName = GetKeyWords(item.StrPicUrl, blnThum, mType) 'GetKeyWords(IIf(mType = MediaType.Video, item.PicUrl, item.Message), blnThum, mType)

                    If mType = QQMediaType.Audio Then
                        strFileOnPC = Path.Combine(strPathOnPC, strFileName)
                    End If
                    If mType <> QQMediaType.Audio Then
                        If IsPng Then
                            strFileOnPC = Path.Combine(strPathOnPC, Path.GetFileName(strFileName))
                        Else
                            strFileOnPC = Path.Combine(strPathOnPC, Path.GetFileNameWithoutExtension(strFileName) & ".pic_thum")
                        End If
                    Else
                        strFileOnPC = Path.Combine(strPathOnPC, strFileName)
                    End If

                    If mType = QQMediaType.Video AndAlso Not blnThum Then
                        strFileOnPC = Path.Combine(strPathOnPC, Path.GetFileNameWithoutExtension(strFileName) & ".mp4")
                    End If

                    strPathOnPhone = Me.GetQQFileDevicePath(strUserNum, mType, blnThum, Not blnThum)
                    strFileOnPhone = String.Format("{0}{1}", strPathOnPhone, strFileName)

                    Dim strPathNew As String = FormatPath(strFileOnPhone)

                    If strPathNew.Length = 0 AndAlso item.SCType = SociaChatType.Picture Then
                        strPathOnPhone = Me.GetQQFileDevicePath(strUserNum, mType, False, False)
                        strFileOnPhone = String.Format("{0}{1}", strPathOnPhone, strFileName)
                        strPathNew = FormatPath(strFileOnPhone)
                    End If

                    If mType = QQMediaType.Audio Then
                        'Dim strAmr As String = Path.Combine(Path.GetDirectoryName(strFileOnPC), String.Format("{0}_{1}.wav", item.DTCreateTime.ToString("yyyyMMdd_HHmmss"), item.IntID))
                        Dim strAmr As String = Path.Combine(Path.GetDirectoryName(strFileOnPC), String.Format("{0}_{1}.mp3", item.DTCreateTime.ToString("yyyyMMdd_HHmmss"), item.IntID))
                        Me.ConvertQQAudio(iBackupFolderHelper, strUserNum, strFileOnPhone, strPathNew, strAmr, item)
                    Else
                        If iBackupFolderHelper Is Nothing Then
                            strFileOnPC = Path.Combine(strPathQQOnPC_Temp, String.Format("{0}{1}", item.DTCreateTime.ToString("yyyyMMdd_HHmmss"), Path.GetExtension(strFileOnPC)))
                            Me.DownFromPhoneEx(strFileOnPhone, strFileOnPC)
                        Else
                            iBackupFolderHelper.DownFromPhone(strFileOnPhone, strFileOnPC, strPathNew)
                        End If
                    End If
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownloadChatImageFromPhone")
        End Try
    End Sub

    Private Sub ConvertQQAudio(ByVal iBackupFolderHelper As BackupFolderHelper, ByVal strUserNum As String, ByVal strFileOnPhone As String, ByVal strPathOnBackupPath As String, ByVal strFileOnPC As String, ByVal item As SociaChatBase)
        Dim strPathTmp As String = Path.Combine(Folder.TempFolder, strUserNum)
        Folder.CheckFolder(strPathTmp)
        Dim strFileName As String = Path.GetFileName(strFileOnPhone)
        Dim strFilePathTmp As String = Path.Combine(strPathTmp, strFileName)
        iBackupFolderHelper.DownFromPhone(strFileOnPhone, strFilePathTmp, strPathOnBackupPath)

        Try
            Dim strAmr As String = Path.Combine(Path.GetDirectoryName(strFilePathTmp), Path.GetFileNameWithoutExtension(strFilePathTmp) & ".wav")
            Dim strMp3 As String = Path.Combine(Path.GetDirectoryName(strFilePathTmp), Path.GetFileNameWithoutExtension(strFilePathTmp) & ".mp3")
            If File.Exists(strAmr) Then
                File.Delete(strAmr)
            End If
            If File.Exists(strMp3) Then
                File.Delete(strMp3)
            End If
            'WeixinHelper.Aud2WavEx(strFilePathTmp, strAmr, "2013042508181124", item.StrMessage)
            WeixinHelper.Wav2Mp3(strFilePathTmp, strAmr, strMp3, "2013042508181124", item.StrMessage)
            'If File.Exists(strAmr) Then
            '    File.Copy(strAmr, strFileOnPC)
            '    File.Delete(strFilePathTmp)
            'End If
            If File.Exists(strMp3) Then
                File.Copy(strMp3, strFileOnPC)
                File.Delete(strFilePathTmp)
            End If
        Catch ex As Exception
            Dim strErrMsga As String = String.Format("{0} {1} {2}", ex.ToString(), strFileOnPhone, item.StrMessage)
            Common.LogException(strErrMsga, "转换格式出错")
        End Try
    End Sub

    Public Function GetKeyWords(ByVal strValue As String, ByVal blnThum As Boolean, ByVal mType As QQMediaType) As String
        Dim relust As String = strValue
        Try
            Dim strMD5 As String = ""
            Dim objJson As JsonObject = JsonParser.ParseString(strValue)
            Dim strSuffix As String = ""
            Select Case mType
                Case QQMediaType.Picture
                    strSuffix = "png"

                    If objJson.ContainsKey("md5") Then
                        strMD5 = CType(objJson("md5"), JsonString)
                    End If

                Case QQMediaType.Video
                    If blnThum Then
                        strSuffix = "png"
                        'If objJson.ContainsKey("thumbMD5") Then
                        '    strMD5 = CType(objJson("thumbMD5"), JsonString)
                        'End If
                    Else
                        strSuffix = "mp4"

                    End If
                    If objJson.ContainsKey("videoMD5") Then
                        strMD5 = CType(objJson("videoMD5"), JsonString)
                    End If

                Case QQMediaType.Audio
                    strSuffix = "amr"
                    If objJson.ContainsKey("fileName") Then
                        strMD5 = CType(objJson("fileName"), JsonString)
                    End If

                    If String.IsNullOrEmpty(strMD5) AndAlso objJson.ContainsKey("fileId") Then
                        strMD5 = CType(objJson("fileId"), JsonString)
                    End If
            End Select
            relust = String.Format("{0}.{1}", strMD5, strSuffix)


        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetKeyWords")
        End Try
        Return relust
    End Function

#End Region


    Private Shared mFindGroupFriend As WeChatFriendInfo
    Private Shared mGroupFriendwxmmdb As WeixinMMDB = Nothing
    Private Shared mStrGroupFriendWCDBOnPC As String = ""

    Public Shared Sub GetGroupFriend(ByVal strWCDBOnPC As String, ByVal strUsrName As String, ByVal lstNotGroupFriend As List(Of WeChatFriendInfo), ByVal dictFriendInfo As Dictionary(Of String, WeChatFriendInfo), ByRef lstGroupFriendInfo As List(Of WeChatFriendInfo))
        Try
            If mStrGroupFriendWCDBOnPC.Length = 0 OrElse mStrGroupFriendWCDBOnPC <> strWCDBOnPC Then
                mStrGroupFriendWCDBOnPC = strWCDBOnPC
                mGroupFriendwxmmdb = WeixinMMDB.GetInstance(strWCDBOnPC)
            End If

            lstGroupFriendInfo = mGroupFriendwxmmdb.Get_GroupFriend_Info(strUsrName)
            If lstGroupFriendInfo.Count > 0 Then
                For Each item As WeChatFriendInfo In lstGroupFriendInfo
                    If dictFriendInfo.ContainsKey(item.WeChatNum) Then
                        Dim info As WeChatFriendInfo = dictFriendInfo(item.WeChatNum)
                        item.NoteName = info.NoteName
                        item.UsrName = info.NickName
                        item.Md5 = info.Md5
                    End If
                Next
            End If

            If lstNotGroupFriend IsNot Nothing AndAlso lstNotGroupFriend.Count > 0 Then
                For Each item As WeChatFriendInfo In lstNotGroupFriend
                    mFindGroupFriend = item
                    If lstGroupFriendInfo.Find(AddressOf ExistsFunc) Is Nothing Then
                        lstGroupFriendInfo.Add(item)
                    End If
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetGroupFriend")
        End Try
    End Sub

    Private Shared Function ExistsFunc(w As WeChatFriendInfo) As Boolean
        Return w.WeChatNum = mFindGroupFriend.WeChatNum AndAlso w.Md5 = mFindGroupFriend.Md5 'AndAlso w.UsrName = mFindGroupFriend.UsrName
    End Function

#If IS_RECOVERY Then

#Region "--- 手机联系人导出 ---"

    Public Function ExportContact(ByVal listContact As List(Of DataGridViewRow), ByVal strDName As String, ByVal tp As ExportDRType, Optional ByVal strGroup As String = "", Optional ByVal app As DRAppName = DRAppName.Defaults) As String
        Dim strPath As String = ""
        If Not Me.SetParameter(strPath, Nothing, strDName, "", Nothing, app) Then
            Return strPath
        End If

        Dim objPara(4) As Object
        objPara(0) = listContact
        objPara(1) = strGroup
        objPara(2) = strPath
        objPara(3) = tp

        If Me.mThreadExportContact IsNot Nothing AndAlso Me.mThreadExportContact.ThreadState <> ThreadState.Stopped Then
            Try
                Me.mThreadExportContact.Abort()
            Catch
            End Try
        End If

        Me.mThreadExportContact = New Thread(New ParameterizedThreadStart(AddressOf DoExportContact))
        With Me.mThreadExportContact
            .IsBackground = True
            .SetApartmentState(ApartmentState.STA)
            .Start(objPara)
        End With
        Return strPath
    End Function

    Public Function ChangeTypeTovCard(ByVal type As LabelType) As vLabelType
        Dim t As vLabelType = vLabelType.NotSpecified

        Select Case type
            Case LabelType.Anniversary
                t = vLabelType.Anniversary
            Case LabelType.Home
                t = vLabelType.Home
            Case LabelType.HomeFax
                t = vLabelType.Home Or vLabelType.Fax
            Case LabelType.HomePage
                t = vLabelType.Home Or vLabelType.Pref
            Case LabelType.iPhone, LabelType.Other
                t = vLabelType.Other
            Case LabelType.Main
                t = vLabelType.Main
            Case LabelType.Mobile
                t = vLabelType.Cell
            Case LabelType.Pager
                t = vLabelType.Pager
            Case LabelType.Work
                t = vLabelType.Work
            Case LabelType.WorkFax
                t = vLabelType.Work Or vLabelType.Fax
            Case LabelType.Parent
                t = vLabelType.Parent
            Case LabelType.Brother
                t = vLabelType.Brother
            Case LabelType.Sister
                t = vLabelType.Sister
            Case LabelType.Mother
                t = vLabelType.Mother
            Case LabelType.Father
                t = vLabelType.Father
            Case LabelType.Child
                t = vLabelType.Child
            Case LabelType.Friend
                t = vLabelType.Friend
            Case LabelType.Spouse
                t = vLabelType.Spouse
            Case LabelType.Partner
                t = vLabelType.Partner
            Case LabelType.Manager
                t = vLabelType.Manager

            Case Else
                t = vLabelType.Custom

        End Select

        Return t
    End Function

    Private Function GetFilePath(ByVal strSaveFolder As String, ByVal strName As String) As String
        Dim strPath As String = String.Empty

        Dim index As Integer = 0
        While True
            Dim tmpName As String = String.Empty

            If index > 0 Then
                tmpName = String.Format("{0}({1}){2}", System.IO.Path.GetFileNameWithoutExtension(strName), index, System.IO.Path.GetExtension(strName))
            Else
                tmpName = strName
            End If

            strPath = System.IO.Path.Combine(strSaveFolder, tmpName)
            If Not System.IO.File.Exists(strPath) Then
                Exit While
            End If

            index += 1
        End While

        Return strPath
    End Function

    Public Function ChangeIMType(ByVal type As vIMType) As IMType
        Dim t As IMType = IMType.AIM

        Select Case type
            Case vIMType.AIM
                t = IMType.AIM
            Case vIMType.Facebook
                t = IMType.Facebook
            Case vIMType.GaduGadu
                t = IMType.GaduGadu
            Case vIMType.GoogleTalk
                t = IMType.GoogleTalk
            Case vIMType.ICQ
                t = IMType.ICQ
            Case vIMType.Jabber
                t = IMType.Jabber
            Case vIMType.MSN
                t = IMType.MSN
            Case vIMType.QQ
                t = IMType.QQ
            Case vIMType.Skype
                t = IMType.Skype
            Case vIMType.Yahoo
                t = IMType.Yahoo
            Case Else
                t = IMType.Custom

        End Select

        Return t
    End Function

    Public Function ChangeIMType(ByVal type As IMType) As vIMType
        Dim t As vIMType = vIMType.AIM

        Select Case type
            Case IMType.AIM
                t = vIMType.AIM
            Case IMType.Facebook
                t = vIMType.Facebook
            Case IMType.GaduGadu
                t = vIMType.GaduGadu
            Case IMType.GoogleTalk
                t = vIMType.GoogleTalk
            Case IMType.ICQ
                t = vIMType.ICQ
            Case IMType.Jabber
                t = vIMType.Jabber
            Case IMType.MSN
                t = vIMType.MSN
            Case IMType.QQ
                t = vIMType.QQ
            Case IMType.Skype
                t = vIMType.Skype
            Case IMType.Yahoo
                t = vIMType.Yahoo
            Case Else
                t = vIMType.Custom

        End Select

        Return t
    End Function

    Private Sub DoExportContact(ByVal obj As Object)
        Try
            Utility.WaitSeconds(1)

            Dim arrObj As Object() = CType(obj, Object())

            Dim listContact As List(Of DataGridViewRow) = CType(arrObj(0), List(Of DataGridViewRow))
            Dim strGroup As String = arrObj(1).ToString()
            Dim strSavePath As String = arrObj(2).ToString()
            Dim strVCFPath As String = Path.Combine(strSavePath, Me.mLanguage.GetString("Contact.Menu.ExportTovCard"))
            Dim strCSVPath As String = Path.Combine(strSavePath, Me.mLanguage.GetString("Weixin.Lable.ExcelFormat"))

            Dim isDoVCF As Boolean = False
            Dim isDoCSV As Boolean = False

            Dim tp As ExportDRType = CType(arrObj(3), ExportDRType)
            If tp = ExportDRType.All Then
                Folder.CheckFolder(strVCFPath)
                Folder.CheckFolder(strCSVPath)
                isDoVCF = True
                isDoCSV = True

            ElseIf tp = ExportDRType.vCard Then
                Folder.CheckFolder(strVCFPath)
                isDoVCF = True

            ElseIf tp = ExportDRType.CSV Then
                Folder.CheckFolder(strCSVPath)
                isDoCSV = True

            End If

            If isDoVCF Then
                DoVCF(listContact, strVCFPath, strGroup, strSavePath)
            End If

            If isDoCSV Then
                DoCSV(listContact, strCSVPath, strSavePath)
            End If

            RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportSuccess, Me.mLanguage.GetString("Note.Message.ExportSucceed")))
            RaiseEvent ExportProgressEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportSuccess, Me.mLanguage.GetString("Note.Message.ExportSucceed"))) '导出成功

        Catch ex As Exception
            RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportFailure, Me.mLanguage.GetString("Contact.Message.ExportFailure"), 100, "")) '导出失败
        End Try
    End Sub

    Private Sub DoVCF(ByVal listContact As List(Of DataGridViewRow), ByVal strVCFPath As String, ByVal strGroup As String, ByVal strSavePath As String)
        Try
            Dim iCount As Integer = 0
            For Each row As DataGridViewRow In listContact
                iCount = iCount + 1
                Dim cInfo As Contact = Nothing
                If row.Tag IsNot Nothing Then
                    cInfo = CType(row.Tag, Contact)
                End If

                mLstCurrentExportInfo = New List(Of String)
                mLstCurrentExportInfo.Add(cInfo.LastName & cInfo.FirstName)
                mLstCurrentExportInfo.Add(iCount.ToString())
                mLstCurrentExportInfo.Add(listContact.Count.ToString())
                mLstCurrentExportInfo.Add(strSavePath)

                Try
                    Dim vcf As New vCard()

                    vcf.Category = strGroup

                    vcf.Items.Add(New vName(cInfo.LastName, cInfo.FirstName, cInfo.MiddleName, cInfo.Prefix, cInfo.Suffix))
                    vcf.FullName = String.Format("{0} {1} {2} {3} {4}", cInfo.Prefix, cInfo.LastName, cInfo.Suffix, cInfo.MiddleName, cInfo.FirstName)
                    If cInfo.NickName.Length > 0 Then
                        vcf.NickNames.Add(cInfo.NickName)
                    End If
                    If cInfo.FirstNamePinyin.Length > 0 Then
                        vcf.Items.Add(vItemName.PHONETIC_FIRST_NAME, String.Empty, cInfo.FirstNamePinyin, True, New Dictionary(Of String, String))
                    End If
                    If cInfo.FirstNamePinyin.Length > 0 Then
                        vcf.Items.Add(vItemName.PHONETIC_LAST_NAME, String.Empty, cInfo.LastNamePinyin, True, New Dictionary(Of String, String))
                    End If
                    If cInfo.CompanyName.Length > 0 OrElse cInfo.Department.Length > 0 Then
                        vcf.Items.Add(New vOrganization(cInfo.CompanyName, cInfo.Department))
                    End If
                    If cInfo.JobTitle.Length > 0 Then
                        vcf.Title = cInfo.JobTitle
                    End If

                    For Each item As ContactItem In cInfo.Emails
                        vcf.Emails.Add(item.Value, ChangeTypeTovCard(item.LabelType), item.Label)
                    Next
                    For Each item As ContactItem In cInfo.PhoneNumbers
                        vcf.PhoneNumbers.Add(item.Value, ChangeTypeTovCard(item.LabelType), item.Label)
                    Next
                    For Each item As AddressItem In cInfo.Addresses
                        vcf.Addresses.Add(ChangeTypeTovCard(item.LabelType), item.Label, String.Empty, String.Empty, item.Street, item.City, item.Province, item.PostalCode, item.Country, item.CountryCode)
                    Next
                    For Each item As ContactItem In cInfo.URLs
                        vcf.Urls.Add(item.Value, ChangeTypeTovCard(item.LabelType), item.Label)
                    Next
                    If cInfo.Birthday <> Date.MinValue Then
                        vcf.Birthday = cInfo.Birthday
                    End If
                    vcf.Photo = cInfo.Photo
                    For Each item As ContactItem In cInfo.Dates
                        vcf.Dates.Add(item.Value, ChangeTypeTovCard(item.LabelType), item.Label)
                    Next
                    For Each item As IMItem In cInfo.IMs
                        vcf.IMs.Add(item.Value, ChangeIMType(item.IMType), ChangeTypeTovCard(item.LabelType), item.Label, item.IMCustom)
                    Next
                    For Each item As ContactItem In cInfo.Relateds
                        vcf.Relateds.Add(item.Value, ChangeTypeTovCard(item.LabelType), item.Label)
                    Next

                    If cInfo.Notes.Length > 0 Then
                        vcf.Note = New vNote(cInfo.Notes, vcf)
                    End If

                    Dim strPath As String = GetFilePath(strVCFPath, Common.ReplaceWinIllegalName(vcf.FullName).Trim() & ".vcf")
                    vcf.ToFile(strPath)
                    Me.DoRaiseNewProgressEvent(iCount, listContact.Count, "vCard")
                Catch ex As Exception
                End Try
            Next

        Catch ex As Exception
        End Try
    End Sub

    Private Sub DoCSV(listContact As List(Of DataGridViewRow), strCSVPath As String, ByVal strSavePath As String)

        Dim iCount As Integer = 0
        Dim sw As StreamWriter = New StreamWriter(GetFilePath(strCSVPath, Me.mLanguage.GetString("Recovery.Group.Contact") & ".csv"), True, Encoding.UTF8)
        sw.WriteLine("姓名,手机号码,住宅号码,工作号码,iPhone号码,主要号码,住宅传真,工作传真,传呼,其他号码,家庭邮箱,工作邮箱,其他邮箱,公司,主页,地址,备注")
        Try
            'For Each row As DataGridViewRow In listContact
            For index As Integer = listContact.Count - 1 To 0 Step -1
                Dim row As DataGridViewRow = listContact(index)
                iCount = iCount + 1
                Dim cInfo As Contact = Nothing
                If row.Tag IsNot Nothing Then
                    cInfo = CType(row.Tag, Contact)
                End If

                mLstCurrentExportInfo = New List(Of String)
                mLstCurrentExportInfo.Add(cInfo.LastName & cInfo.FirstName)
                mLstCurrentExportInfo.Add(iCount.ToString())
                mLstCurrentExportInfo.Add(listContact.Count.ToString())
                mLstCurrentExportInfo.Add(strSavePath)

                Try
                    Dim strFullName As String = String.Format("{0} {1} {2} {3} {4}", cInfo.Prefix, cInfo.LastName, cInfo.Suffix, cInfo.MiddleName, cInfo.FirstName)

                    Dim strHomeE As String = ""
                    Dim strWorkE As String = ""
                    Dim strOtherE As String = ""
                    For Each item As ContactItem In cInfo.Emails
                        Select Case item.LabelType
                            Case LabelType.Work
                                strWorkE = item.Value & "  " & strWorkE
                            Case LabelType.Home
                                strHomeE = item.Value & "  " & strHomeE
                            Case LabelType.Other
                                strOtherE = item.Value & "  " & strOtherE
                        End Select
                    Next

                    Dim strNotes As String = cInfo.Notes

                    Dim strMobile As String = ""
                    Dim strHome As String = ""
                    Dim strWork As String = ""
                    Dim striPhone As String = ""
                    Dim strMain As String = ""
                    Dim strHomeFax As String = ""
                    Dim strWorkFax As String = ""
                    Dim strPager As String = ""
                    Dim strOther As String = ""

                    For Each item As ContactItem In cInfo.PhoneNumbers
                        Select Case item.LabelType
                            Case LabelType.Home
                                strHome = item.Value & "  " & strHome
                            Case LabelType.Work
                                strWork = item.Value & "  " & strWork
                            Case LabelType.iPhone
                                striPhone = item.Value & "  " & striPhone
                            Case LabelType.Mobile
                                strMobile = item.Value & "  " & strMobile
                            Case LabelType.Main
                                strMain = item.Value & "  " & strMain
                            Case LabelType.HomeFax
                                strHomeFax = item.Value & "  " & strHomeFax
                            Case LabelType.WorkFax
                                strWorkFax = item.Value & "  " & strWorkFax
                            Case LabelType.Pager
                                strPager = item.Value & "  " & strPager
                            Case LabelType.Other
                                strOther = item.Value & "  " & strOther
                        End Select
                    Next

                    If strMobile.Length = 0 AndAlso strHome.Length = 0 AndAlso strWork.Length = 0 AndAlso striPhone.Length = 0 AndAlso strMain.Length = 0 AndAlso strHomeFax.Length = 0 AndAlso strWorkFax.Length = 0 AndAlso strPager.Length = 0 Then
                        strMobile = strOther
                    End If

                    Dim strAddresses As String = ""
                    For Each item As AddressItem In cInfo.Addresses
                        strAddresses = strAddresses & item.Country & item.Province & item.City & item.Street & ";"
                    Next

                    Dim strURLs As String = ""
                    For Each item As ContactItem In cInfo.URLs
                        strURLs = strURLs & item.Value & ";"
                    Next

                    Dim strCompanyName As String = cInfo.CompanyName

                    strFullName = strFullName.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strMobile = strMobile.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strHome = strHome.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strWork = strWork.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    striPhone = striPhone.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strMain = strMain.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strHomeE = strHomeE.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strWorkE = strWorkE.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strOtherE = strOtherE.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strURLs = strURLs.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strCompanyName = strCompanyName.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strAddresses = strAddresses.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")
                    strNotes = strNotes.Replace(vbCrLf, "").Replace(vbCr, "").Replace(vbLf, "")

                    sw.WriteLine(String.Format("{0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13},{14},{15},{16}", strFullName, strMobile, strHome, strWork, striPhone, strMain, strHomeFax, strWorkFax, strPager, strOther, strHomeE, strWorkE, strOtherE, strCompanyName, strURLs, strAddresses, strNotes))

                    Me.DoRaiseNewProgressEvent(iCount, listContact.Count, "CSV")
                Catch

                End Try
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoCSV")
        Finally
            sw.Close()
        End Try
    End Sub

#End Region

#Region "---通话记录导出 ---"

    Public Function ExportCallHistory(ByVal listItem As List(Of String), callHistory As List(Of CallHistory), dCallHistory As List(Of CallHistory), ByVal strDName As String, ByVal tp As ExportDRType, Optional ByVal app As DRAppName = DRAppName.Defaults) As String
        Dim strPath As String = ""
        If Not Me.SetParameter(strPath, Nothing, strDName, "", Nothing, app) Then
            Return strPath
        End If

        Dim objPara(5) As Object
        objPara(0) = listItem
        objPara(1) = strPath
        objPara(2) = tp
        objPara(3) = callHistory
        objPara(4) = dCallHistory

        If Me.mThreadExportCallHistory IsNot Nothing AndAlso Me.mThreadExportCallHistory.ThreadState <> ThreadState.Stopped Then
            Try
                Me.mThreadExportCallHistory.Abort()
            Catch
            End Try
        End If

        Me.mThreadExportCallHistory = New Thread(New ParameterizedThreadStart(AddressOf DoExportCallHistory))
        With Me.mThreadExportCallHistory
            .IsBackground = True
            .SetApartmentState(ApartmentState.STA)
            .Start(objPara)
        End With
        Return strPath
    End Function

    Private Sub DoExportCallHistory(ByVal obj As Object)
        Try
            Utility.WaitSeconds(1)

            Dim arrObj As Object() = CType(obj, Object())

            Dim lstItem As List(Of String) = CType(arrObj(0), List(Of String))
            Dim strSavePath As String = arrObj(1).ToString()
            Dim tp As ExportDRType = CType(arrObj(2), ExportDRType)
            Dim callHistory As List(Of CallHistory) = CType(arrObj(3), List(Of CallHistory))
            Dim dCallHistory As List(Of CallHistory) = CType(arrObj(4), List(Of CallHistory))
            dCallHistory.Sort(New CallHistoryComparer(SortType.DESC))

            Dim DoCallHistory As New List(Of CallHistory)

            Dim strTxtPath As String = Path.Combine(strSavePath, Me.mLanguage.GetString("Weixin.Lable.TextFormat"))
            Dim strExcelPath As String = Path.Combine(strSavePath, Me.mLanguage.GetString("Weixin.Lable.ExcelFormat"))

            Dim isDoTxt As Boolean = False
            Dim isDoExcel As Boolean = False

            If tp = ExportDRType.All Then
                Folder.CheckFolder(strTxtPath)
                Folder.CheckFolder(strExcelPath)
                isDoTxt = True
                isDoExcel = True

            ElseIf tp = ExportDRType.Text Then
                Folder.CheckFolder(strTxtPath)
                isDoTxt = True

            ElseIf tp = ExportDRType.Excel Then
                Folder.CheckFolder(strExcelPath)
                isDoExcel = True

            End If

            Dim isAll As Boolean = False
            Dim isAccept As Boolean = False
            Dim isUnAccept As Boolean = False
            Dim isOut As Boolean = False

            If lstItem.Contains("All") Then
                isAll = True
            End If
            If lstItem.Contains("Accept") Then
                isAccept = True
            End If
            If lstItem.Contains("UnAccept") Then
                isUnAccept = True
            End If
            If lstItem.Contains("Out") Then
                isOut = True
            End If

            Dim strAllPath As String = Me.mLanguage.GetString("Backup.Label.CallHistoryAll")
            Dim strAcceptPath As String = Me.mLanguage.GetString("Backup.Label.CallHistoryAccept")
            Dim strUnAcceptPath As String = Me.mLanguage.GetString("Backup.Label.CallHistoryUnAccept")
            Dim strOutPath As String = Me.mLanguage.GetString("Backup.Label.CallHistoryOut")

            If isAll Then
                DoCallHistory.Clear()
                For Each historyItem As CallHistory In dCallHistory
                    DoCallHistory.Add(historyItem)
                Next

                For Each historyItem As CallHistory In callHistory
                    DoCallHistory.Add(historyItem)
                Next

                If isDoTxt Then
                    DoCHText(DoCallHistory, Path.Combine(strTxtPath, strAllPath & ".txt"))
                End If

                If isDoExcel Then
                    DoCHExcel(DoCallHistory, Path.Combine(strExcelPath, strAllPath & ".xlsx"))
                End If
            End If

            If isAccept Then
                DoCallHistory.Clear()
                For Each historyItem As CallHistory In dCallHistory
                    If historyItem.Duration > 0 AndAlso historyItem.CallInOut = CallInOut.CallIn Then
                        DoCallHistory.Add(historyItem)
                    End If
                Next

                For Each historyItem As CallHistory In callHistory
                    If historyItem.Duration > 0 AndAlso historyItem.CallInOut = CallInOut.CallIn Then
                        DoCallHistory.Add(historyItem)
                    End If
                Next

                If isDoTxt Then
                    DoCHText(DoCallHistory, Path.Combine(strTxtPath, strAcceptPath & ".txt"))
                End If

                If isDoExcel Then
                    DoCHExcel(DoCallHistory, Path.Combine(strExcelPath, strAcceptPath & ".xlsx"))
                End If
            End If

            If isUnAccept Then
                DoCallHistory.Clear()
                For Each historyItem As CallHistory In dCallHistory
                    If historyItem.Duration <= 0 AndAlso historyItem.CallInOut = CallInOut.CallIn Then
                        DoCallHistory.Add(historyItem)
                    End If
                Next

                For Each historyItem As CallHistory In callHistory
                    If historyItem.Duration <= 0 AndAlso historyItem.CallInOut = CallInOut.CallIn Then
                        DoCallHistory.Add(historyItem)
                    End If
                Next

                If isDoTxt Then
                    DoCHText(DoCallHistory, Path.Combine(strTxtPath, strUnAcceptPath & ".txt"))
                End If

                If isDoExcel Then
                    DoCHExcel(DoCallHistory, Path.Combine(strExcelPath, strUnAcceptPath & ".xlsx"))
                End If
            End If

            If isOut Then
                DoCallHistory.Clear()
                For Each historyItem As CallHistory In dCallHistory
                    If historyItem.CallInOut = CallInOut.CallOut Then
                        DoCallHistory.Add(historyItem)
                    End If
                Next

                For Each historyItem As CallHistory In callHistory
                    If historyItem.CallInOut = CallInOut.CallOut Then
                        DoCallHistory.Add(historyItem)
                    End If
                Next

                If isDoTxt Then
                    DoCHText(DoCallHistory, Path.Combine(strTxtPath, strOutPath & ".txt"))
                End If

                If isDoExcel Then
                    DoCHExcel(DoCallHistory, Path.Combine(strExcelPath, strOutPath & ".xlsx"))
                End If
            End If

            RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportSuccess, Me.mLanguage.GetString("Note.Message.ExportSucceed")))
            RaiseEvent ExportProgressEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportSuccess, Me.mLanguage.GetString("Note.Message.ExportSucceed"))) '导出成功

        Catch ex As Exception
            RaiseEvent ExportProgressNewEventHandler(Me, New ExportProgressEventArgs(ExportStatus.ExportFailure, Me.mLanguage.GetString("Contact.Message.ExportFailure"), 100, "")) '导出失败
        End Try
    End Sub

    Public Function DoCHExcel(ByVal lstCallHistory As List(Of CallHistory), ByVal strOutputFile As String) As Boolean
        Dim result As Boolean = False
        Try

            Dim strTempFilePath As String = strOutputFile & "tmp"
            '创建头部
            CreateCHExcelHead(strTempFilePath)

            '写入内容
            Dim strLineTd As String = "<td class=xl24  style='border-bottom:.5pt solid black;border-top:none;' x:str>{0}</td>"
            Dim strLineTr As String = "<tr style='mso-height-source:userset;height:25.00pt'>{0}</tr>"
            Dim strExcelContacts As New StringBuilder
            Dim strLine As StringBuilder = New StringBuilder()
            Dim intExcelIndex As Integer = 1
            Dim intCProgress As Integer = 0

            For Each item As CallHistory In lstCallHistory
                strLine.Remove(0, strLine.Length)

                strLine.Append(String.Format(strLineTd, item.Name))
                strLine.Append(String.Format(strLineTd, item.PhoneNumber))
                strLine.Append(String.Format(strLineTd, item.CreateDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))))
                strLine.Append(String.Format(strLineTd, Common.FormatTime(item.Duration)))
                strLine.Append(String.Format(strLineTd, Me.GetCallType(item)))

                strExcelContacts.AppendLine(String.Format(strLineTr, strLine.ToString()))

                If strExcelContacts.Length > 1000 Then
                    Me.WriteText(strTempFilePath, strExcelContacts.ToString())
                    strExcelContacts.Remove(0, strExcelContacts.Length)
                End If

                Me.DoRaiseNewProgressEvent(intExcelIndex, lstCallHistory.Count, "EXCEL")
                intExcelIndex = intExcelIndex + 1
            Next

            If strExcelContacts.Length > 0 Then
                Me.WriteText(strTempFilePath, strExcelContacts.ToString())
            End If

            '创建execl 尾部
            CreateExcelFoot(strTempFilePath)

            '保存 Excel
            SaveCreateExce(strTempFilePath, strOutputFile)

        Catch ex As Exception

        End Try
        result = File.Exists(strOutputFile)

    End Function

    Private Sub CreateCHExcelHead(strTempFilePath As String)
        Dim strExcelContacts As New StringBuilder
        strExcelContacts.AppendLine("<html xmlns:v=""urn:schemas-microsoft-com:vml""")
        strExcelContacts.AppendLine("xmlns:o=""urn:schemas-microsoft-com:office:office""")
        strExcelContacts.AppendLine("xmlns:x=""urn:schemas-microsoft-com:office:excel""")
        strExcelContacts.AppendLine("xmlns=""http://www.w3.org/TR/REC-html40"">")

        Dim strExcelHeader As String = My.Resources.excel_header.Replace("<meta http-equiv=Content-Type content=""text/html; charset=utf8"">", String.Format("<meta http-equiv=Content-Type content=""text/html; charset={0}"">", IniSetting.GetWeChatExportExcelCoding))

        strExcelContacts.AppendLine(strExcelHeader)
        strExcelContacts.AppendLine("<body link=blue vlink=purple><table x:str border=0 cellpadding=0 cellspacing=0 width=1296 style='border-collapse:collapse;table-layout:fixed;width:972pt'>")

        Dim strTitleTr As String = "<tr style='mso-height-source:userset;height:30.00pt'>{0}</tr>"
        Dim strTitleTd As String = "<td rowspan=2 class=xl300  style='border-bottom:.5pt solid black;border-top:none;' >{0}</td>"
        Dim strLineTr As String = "<tr style='mso-height-source:userset;height:25.00pt'>{0}</tr>"

        Dim strTitle As New StringBuilder
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Main.Button.Contact")))
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Welcome.Label.PhoneNumber")))
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Backup.Label.CallTime")))
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Backup.Label.CallDuration")))
        strTitle.Append(String.Format(strTitleTd, Me.mLanguage.GetString("Common.Label.ColumnType")))
        strExcelContacts.AppendLine(String.Format(strTitleTr, strTitle))
        strExcelContacts.AppendLine(String.Format(strLineTr, ""))

        Me.WriteText(strTempFilePath, strExcelContacts.ToString())
    End Sub

    Public Function DoCHExcelOld(ByVal lstCallHistory As List(Of CallHistory), ByVal strOutputFile As String) As Boolean
        If Not m_enabled OrElse lstCallHistory Is Nothing OrElse lstCallHistory.Count <= 0 Then
            Return False
        End If

        Try
            xlBook = xlApp.Workbooks.Add
            xlSheet = xlBook.Worksheets(1)
            With xlSheet
                '***************************************************************************************
                .Cells(1, 1) = Me.mLanguage.GetString("App.Button.TimeSort")
                .Cells(1, 2) = Me.mLanguage.GetString("Main.Button.Contact")
                .Cells(1, 3) = Me.mLanguage.GetString("Welcome.Label.PhoneNumber")
                .Cells(1, 4) = Me.mLanguage.GetString("Common.Label.ColumnType")
                .Cells(1, 5) = Me.mLanguage.GetString("Backup.Label.CallDuration")

                '=== 输出详细内容 ======================================================== 
                Dim rowIndex As Integer = 2

                '设置表格中各列的类型
                With .Range(.Cells(2, 1), .Cells(rowIndex + lstCallHistory.Count, 5))
                    .NumberFormatLocal = "@"                '文本格式
                End With

                With .Range(.Cells(2, 1), .Cells(rowIndex + lstCallHistory.Count, 1))
                    .NumberFormatLocal = Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)  '日期格式
                End With

                For Each item As CallHistory In lstCallHistory
                    Application.DoEvents()
                    .Cells(rowIndex, 1) = item.CreateDate
                    .Cells(rowIndex, 2) = item.Name & ""
                    .Cells(rowIndex, 3) = item.PhoneNumber & ""
                    .Cells(rowIndex, 4) = Me.GetCallType(item) & ""
                    .Cells(rowIndex, 5) = Common.FormatTime(item.Duration) & ""
                    rowIndex += 1
                Next

                '========================================================================= 
                .Columns(1).ColumnWidth = 18
                .Columns(2).ColumnWidth = 18
                .Columns(3).ColumnWidth = 18
                .Columns(4).ColumnWidth = 15
                .Columns(5).ColumnWidth = 50

                '***** 格式设定 ******************************************************************************

                '标题
                .Rows(1).RowHeight = 25                 '行高

                '标题栏


                With .Range(.Cells(1, 1), .Cells(1, 5))
                    .Font.Size = 12
                    .Interior.ColorIndex = 45           '设置标题背景色为 浅橙色


                    .Font.Bold = True                   '标题字体加粗
                End With

                '表格格式（含标题栏）
                With .Range(.Cells(1, 1), .Cells(rowIndex, 5))
                    .Borders.LineStyle = 1              '设表格边框样式


                    .Font.Size = 10                     '设置表格内容的字体为 10 号字
                End With

                With .Range(.Cells(1, 1), .Cells(rowIndex, 1))
                    .HorizontalAlignment = -4152        '水平居右
                    .VerticalAlignment = -4108          '垂直居中
                End With
            End With

            If Not (xlSheet Is Nothing) Then
                xlSheet.SaveAs(strOutputFile)           '文件另存为

                'Me.xlBook.Close()
                'xlApp.Visible = True                    '显示导出后的 Excel 内容
                'xlApp.Quit()                           '退出 Excel
            End If

            'If xlApp IsNot Nothing Then
            '    xlApp.Quit()
            'End If

            Return True
        Catch ex As Exception
            If xlApp IsNot Nothing Then
                xlApp.Quit()
            End If
            Common.LogException(ex.ToString())

            Return False
        End Try
    End Function

    Public Function DoCHText(ByVal lstCallHistory As List(Of CallHistory), ByVal strOutputFile As String) As Boolean
        If lstCallHistory Is Nothing OrElse lstCallHistory.Count <= 0 Then
            Return False
        End If

        Try
            Using sw As New System.IO.StreamWriter(strOutputFile, True, System.Text.Encoding.UTF8)
                '"通话记录"   "导出时间"
                sw.WriteLine(String.Format("{0}     {1}:{2}", Me.mLanguage.GetString("Main.Button.CallHistory"), Me.mLanguage.GetString("Backup.Label.ExportDate"), DateTime.Now.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond))))
                sw.WriteLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")
                '"时间"   "联络人"   "电话号码"  "类型"  "通话时长"
                sw.WriteLine(String.Format("{0}{1}{2}{3}{4}", _
                                                    Common.StringFormat(Me.mLanguage.GetString("Main.Button.Contact"), 20, False, True, True, False), _
                                                    Common.StringFormat(Me.mLanguage.GetString("Welcome.Label.PhoneNumber"), 20, False, True, True, False), _
                                                    Common.StringFormat(Me.mLanguage.GetString("Backup.Label.CallTime"), 20, False, True, True, False), _
                                                    Common.StringFormat(Me.mLanguage.GetString("Backup.Label.CallDuration"), 20, False, True, True, False), _
                                                    Common.StringFormat(Me.mLanguage.GetString("Common.Label.ColumnType"), 20, False, True, True, False)))
                sw.WriteLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")
                Dim intExcelIndex As Integer = 1
                For Each Item As CallHistory In lstCallHistory
                    Application.DoEvents()
                    sw.WriteLine(String.Format("{0}{1}{2}{3}{4}", _
                                                    Common.StringFormat(Item.Name, 20, False, True, True, False), _
                                                    Common.StringFormat(Item.PhoneNumber, 20, False, True, True, False), _
                                                    Common.StringFormat(Item.CreateDate.ToString(Common.GetDateTimeFormat(DateTimeFormat.YearMonthDayHourMinuteSecond)), 20, False, True, True, False), _
                                                    Common.StringFormat(Common.FormatTime(Item.Duration), 20, False, True, True, False), _
                                                    Common.StringFormat(Me.GetCallType(Item), 20, False, True, True, False)))
                    intExcelIndex += 1
                    Me.DoRaiseNewProgressEvent(intExcelIndex, lstCallHistory.Count, "TEXT")
                Next
                sw.WriteLine("－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－－")
            End Using
            Return True
        Catch ex As Exception
            Return False
        End Try
    End Function

    Private Function GetCallType(ByVal item As CallHistory) As String
        Dim strText As String = ""

        If item.Duration > 0 AndAlso item.CallInOut = CallInOut.CallIn Then
            'Accept
            strText = Me.mLanguage.GetString("Backup.Label.CallHistoryAccept")           '"已接电话"

        ElseIf item.Duration <= 0 AndAlso item.CallInOut = CallInOut.CallIn Then
            'UnAccept
            strText = Me.mLanguage.GetString("Backup.Label.CallHistoryUnAccept")         '"未接电话"

        Else
            'Out
            strText = Me.mLanguage.GetString("Backup.Label.CallHistoryOut")              '"已拨电话"

        End If
        Return strText
    End Function

#End Region


#End If

    Public Sub StopExport()
        Try
            If Me.mThreadExportHtml IsNot Nothing AndAlso Me.mThreadExportHtml.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mThreadExportHtml.Abort()
                Catch
                End Try
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "StopExport")
        End Try
    End Sub


End Class

Public Class ExportProgressEventArgs
    Inherits EventArgs

    Dim mExportStatus As ExportStatus = 0
    Dim mMsg As String = ""
    Dim mIntleProgress As Double = 0

    Dim mStrSaveFolder As String = ""
    Public Property StrSaveFolder() As String
        Get
            Return Me.mStrSaveFolder
        End Get
        Set(ByVal value As String)
            Me.mStrSaveFolder = value
        End Set
    End Property

    Public Property PExportStatus() As ExportStatus
        Get
            Return Me.mExportStatus
        End Get
        Set(ByVal value As ExportStatus)
            Me.mExportStatus = value
        End Set
    End Property

    Public Property Msg() As String
        Get
            Return Me.mMsg
        End Get
        Set(ByVal value As String)
            Me.mMsg = value
        End Set
    End Property

    Public Property intProgress() As Integer
        Get
            Return Me.mIntleProgress
        End Get
        Set(value As Integer)
            Me.mIntleProgress = value
        End Set
    End Property

    Public Sub New(ByVal statu As ExportStatus, ByVal strMsg As String)
        Me.mExportStatus = statu
        Me.mMsg = strMsg
    End Sub

    Public Sub New(ByVal statu As ExportStatus, ByVal strMsg As String, ByVal intProgress As Integer, ByVal strSFolder As String)
        Me.mExportStatus = statu
        Me.mMsg = strMsg
        Me.mIntleProgress = intProgress
        Me.mStrSaveFolder = strSFolder
    End Sub
End Class

Public Class WhatsAppStartExportEventArgs
    Inherits EventArgs

    Dim mExportStatus As ExportStatus = 0

    Public Property PExportStatus() As ExportStatus
        Get
            Return Me.mExportStatus
        End Get
        Set(ByVal value As ExportStatus)
            Me.mExportStatus = value
        End Set
    End Property


    Dim mDirExportOnPC As String = ""
    Public Property PEDirExportOnPC() As String
        Get
            Return Me.mDirExportOnPC
        End Get
        Set(value As String)
            Me.mDirExportOnPC = value
        End Set
    End Property
    Public Sub New(ByVal status As ExportStatus, ByVal dir As String)
        Me.mExportStatus = status
        Me.mDirExportOnPC = dir
    End Sub
End Class

Public Enum ExportStatus
    Copying             '正在复制
    CopySuccess         '复制成功
    CopyFailure         '复制失败
    Exporting           '正在导出
    ExportSuccess       '导出成功
    ExportFailure       '导出失败
End Enum

'导出类型
'Public Enum WeixinExportType
'    CurrentPage             '备份当前页
'    CurrentFriend           '备份当前联系人
'    AllFriend               '备份全部联系人
'    CopyToClipboard         '复制当前页
'    CurrentFriendByTime     '备份当前联系人浏览时间段内
'End Enum

'消息展示在哪里
Public Enum ChatMsgType
    ShowOnList              '展示在联系人列表上      
    ShowOnWeb               '展示在web页面上
    ShowOnTxtFile
    ShowOnExcelFile
End Enum

Public Class ContentLayout

    Dim strName As String = ""
    Dim length As Integer = 0
    Dim isDoc As Boolean = False
    Dim isPad As Boolean = False
    Dim padRight As Boolean = False
    Dim isCut As Boolean = False

    Dim strValue As String = ""

    Dim iColumnIndex As Integer = 1
    Dim iColumnWidth As Integer = 0


    Public Property CLName() As String
        Get
            Return Me.strName
        End Get
        Set(ByVal value As String)
            Me.strName = value
        End Set
    End Property

    Public Property CLLength() As Integer
        Get
            Return Me.length
        End Get
        Set(ByVal value As Integer)
            Me.length = value
        End Set
    End Property

    Public Property CLIsDoc() As Boolean
        Get
            Return Me.isDoc
        End Get
        Set(ByVal value As Boolean)
            Me.isDoc = value
        End Set
    End Property

    Public Property CLIsPad() As Boolean
        Get
            Return Me.isPad
        End Get
        Set(ByVal value As Boolean)
            Me.isPad = value
        End Set
    End Property

    Public Property CLPadRight() As Boolean
        Get
            Return Me.padRight
        End Get
        Set(ByVal value As Boolean)
            Me.padRight = value
        End Set
    End Property

    Public Property CLIsCut() As Boolean
        Get
            Return Me.isCut
        End Get
        Set(ByVal value As Boolean)
            Me.isCut = value
        End Set
    End Property

    Public Property CLValue() As String
        Get
            Return Me.strValue
        End Get
        Set(ByVal value As String)
            Me.strValue = value
        End Set
    End Property

    Public Property CLColumnIndex() As Integer
        Get
            Return Me.iColumnIndex
        End Get
        Set(ByVal value As Integer)
            Me.iColumnIndex = value
        End Set
    End Property

    Public Property CLColumnWidth() As Integer
        Get
            Return Me.iColumnWidth
        End Get
        Set(ByVal value As Integer)
            Me.iColumnWidth = value
        End Set
    End Property

End Class

Public Class ExportHtmlUserInfo
    Dim _id As Integer = 0
    Dim _userName As String = ""
    Dim _type As Integer = 0
    Dim _msgTotal As Integer = 0
    Dim _nickName As String = ""

    Public Property id() As Integer
        Get
            Return Me._id
        End Get
        Set(ByVal value As Integer)
            Me._id = value
        End Set
    End Property

    Public Property userName() As String
        Get
            Return Me._userName
        End Get
        Set(ByVal value As String)
            Me._userName = value
        End Set
    End Property

    Public Property type() As Integer
        Get
            Return Me._type
        End Get
        Set(ByVal value As Integer)
            Me._type = value
        End Set
    End Property

    Public Property msgTotal() As Integer
        Get
            Return Me._msgTotal
        End Get
        Set(ByVal value As Integer)
            Me._msgTotal = value
        End Set
    End Property

    Public Property nickName() As String
        Get
            Return Me._nickName
        End Get
        Set(ByVal value As String)
            Me._nickName = value
        End Set
    End Property

End Class

Public Class CreateIDComparer
    Implements IComparer(Of ExportHtmlMainInfo)

    Private type As SortType = SortType.ASC

    Public Sub New(ByVal tempType As SortType)
        type = tempType
    End Sub

    Public Function Compare(ByVal x As ExportHtmlMainInfo, ByVal y As ExportHtmlMainInfo) As Integer Implements System.Collections.Generic.IComparer(Of ExportHtmlMainInfo).Compare
        Dim result As Integer = 0

        If type = SortType.ASC Then
            result = x.id.CompareTo(y.id)
        Else
            result = y.id.CompareTo(x.id)
        End If

        Return result
    End Function

End Class

Public Class CreateExportHtmlMainInfoTimeComparer
    Implements IComparer(Of ExportHtmlMainInfo)

    Private type As SortType = SortType.ASC

    Public Sub New(ByVal tempType As SortType)
        type = tempType
    End Sub

    Public Function Compare(ByVal x As ExportHtmlMainInfo, ByVal y As ExportHtmlMainInfo) As Integer Implements System.Collections.Generic.IComparer(Of ExportHtmlMainInfo).Compare
        Dim result As Integer = 0

        If type = SortType.ASC Then
            result = x.LastMessageTime.CompareTo(y.LastMessageTime)
        Else
            result = y.LastMessageTime.CompareTo(x.LastMessageTime)
        End If

        Return result
    End Function

End Class

Public Enum ExportSocialType
    WeChat
    AndroidWeChat
    QQ
    WhatsApp
End Enum

Public Class ExportHtmlMainInfo
    Dim _id As Integer = 0
    Dim _strUrl As String = ""
    Dim _userName As String = ""
    Dim _iconImage As String = ""
    Dim _lastMessage As String = ""
    Dim _lastMessageTime As DateTime

    Public Property id() As Integer
        Get
            Return Me._id
        End Get
        Set(ByVal value As Integer)
            Me._id = value
        End Set
    End Property

    Public Property StrUrl() As String
        Get
            Return Me._strUrl
        End Get
        Set(ByVal value As String)
            Me._strUrl = value
        End Set
    End Property

    Public Property UserName() As String
        Get
            Return Me._userName
        End Get
        Set(ByVal value As String)
            Me._userName = value
        End Set
    End Property

    Public Property IconImage() As String
        Get
            Return Me._iconImage
        End Get
        Set(ByVal value As String)
            Me._iconImage = value
        End Set
    End Property

    Public Property LastMessage() As String
        Get
            Return Me._lastMessage
        End Get
        Set(ByVal value As String)
            Me._lastMessage = value
        End Set
    End Property

    Public Property LastMessageTime() As DateTime
        Get
            Return Me._lastMessageTime
        End Get
        Set(ByVal value As DateTime)
            Me._lastMessageTime = value
        End Set
    End Property

End Class

Public Enum QQMediaType
    Picture
    Audio
    Video
End Enum

Public Enum DRAppName
    Defaults
    WhatsApp
    Contact
    MessageCall
    History
    Note
    Reminder
    Safari
    Calendar
End Enum