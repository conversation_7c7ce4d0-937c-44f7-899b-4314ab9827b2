﻿Imports iTong.CoreFoundation
Imports System.Collections
Imports System.Collections.Generic
Imports System.Data
Imports System.Diagnostics
Imports System.IO
Imports System.Text

Public Class IMHtmlNodeHelper

    Public Shared Function CreateHtml(strTitle As String, strFilePath As String, lstIMHtmlNode As List(Of IMHtmlNode)) As Object
        Dim isRelust As Boolean = True
        Try
            '创建头部
            CreateHtmlHeader(strTitle, strFilePath)

            '创建内容
            CreateHtmlContent(strFilePath, lstIMHtmlNode)

            '创建尾部
            CreateHtmlEnd(strFilePath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtml")
        End Try
        Return isRelust
    End Function

    Private Shared Function CreateHtmlContent(strFilePath As String, lstIMHtmlNode As List(Of IMHtmlNode)) As Object
        Dim isRelust As Boolean = True
        Try
            Dim strContent As String = ""
            Dim strTime As String = ""
            Dim isShowTime As Boolean = False

            For Each info As IMHtmlNode In lstIMHtmlNode
                If strTime.Length = 0 OrElse strTime <> info.SmsTime Then
                    isShowTime = True
                    strTime = info.SmsTime
                Else
                    isShowTime = False
                End If

                If info.NType <> IMHtmlNodeType.IMHtmlNode_Image AndAlso info.NType <> IMHtmlNodeType.IMHtmlNode_Media AndAlso info.NType <> IMHtmlNodeType.IMHtmlNode_Video AndAlso info.SmsContent.Length = 0 Then
                    Continue For
                End If

                Select Case info.NType
                    Case IMHtmlNodeType.IMHtmlNode_Text
                        strContent = CreateHtmlContentText(strFilePath, info, isShowTime)
                        Exit Select

                    Case IMHtmlNodeType.IMHtmlNode_SystemTip
                        strContent = CreateHtmlContentSystemTip(strFilePath, info, isShowTime)
                        Exit Select

                    Case IMHtmlNodeType.IMHtmlNode_Media
                        If info.ArrContentForWeb Is Nothing Then
                            strContent = CreateHtmlContentText(strFilePath, info, isShowTime)
                        Else
                            strContent = CreateHtmlContentMedia(strFilePath, info, isShowTime)
                        End If
                        Exit Select

                    Case IMHtmlNodeType.IMHtmlNode_Image
                        strContent = CreateHtmlContentImage(strFilePath, info, isShowTime)
                        Exit Select

                    Case IMHtmlNodeType.IMHtmlNode_Voice
                        strContent = CreateHtmlContentVoice(strFilePath, info, isShowTime)
                        Exit Select

                    Case IMHtmlNodeType.IMHtmlNode_Video
                        strContent = CreateHtmlContentVideo(strFilePath, info, isShowTime)
                        Exit Select
                End Select

                WriteText(strFilePath, strContent)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtmlContent")
        End Try
        Return isRelust
    End Function

    Private Shared Function CreateHtmlContentText(strFilePath As String, node As IMHtmlNode, isShowTime As Boolean) As String
        Dim strRelust As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""chatItem {0}"">", (IIf(node.IsItemMe, "me", "you"))))
            If isShowTime Then
                strContent.AppendLine(String.Format("<div class=""time""><span class=""timeText"">{0}</span></div>", node.SmsTime))
            End If
            strContent.AppendLine("<div class=""clear""></div>")
            strContent.AppendLine("<div class=""chatItemContent"">")
            strContent.AppendLine(String.Format("<img class=""avatar"" src=""{0}"" title=""{1}"">", node.HeaderIconUrl, node.WeChatID))
            strContent.AppendLine(String.Format("<div class=""cloud cloudText {0}"" cloudfriend=""{1}"" cloudid=""{2}"">", (IIf(node.IsDeleted, "deleted", "")), node.UserName, node.UserID))
            If node.IsChatRoom AndAlso Not node.IsItemMe Then
                strContent.AppendLine(String.Format("<div class=""nickName"">{0}</div>", node.NickName))
            End If
            strContent.AppendLine("<div class=""cloudPannel"">")
            strContent.AppendLine("<div class=""cloudBody"">")
            strContent.AppendLine("<div class=""cloudContent"">")
            strContent.AppendLine(String.Format("<pre style=""white-space:pre-wrap;*white-space:pre;*word-wrap:break-word;"">{0}</pre>", node.SmsContent))
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("<div class=""cloudArrow""></div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strRelust = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtmlContentText")
        End Try
        Return strRelust
    End Function

    Private Shared Function CreateHtmlContentTextEx(strFilePath As String, node As IMHtmlNode, isShowTime As Boolean) As String
        Dim strRelust As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""chatItem {0}"">", (IIf(node.IsItemMe, "me", "you"))))
            If isShowTime Then
                strContent.AppendLine(String.Format("<div class=""time""><span class=""timeText"">{0}</span></div>", node.SmsTime))
            End If
            strContent.AppendLine("<div class=""clear""></div>")
            strContent.AppendLine("<div class=""chatItemContent"">")
            strContent.AppendLine(String.Format("<img class=""avatar"" src=""{0}"" title=""{1}"">", node.HeaderIconUrl, node.WeChatID))
            strContent.AppendLine(String.Format("<div class=""cloud cloudText {0}"" cloudfriend=""{1}"" cloudid=""{2}"">", (IIf(node.IsDeleted, "deleted", "")), node.UserName, node.UserID))
            If node.IsChatRoom AndAlso Not node.IsItemMe Then
                strContent.AppendLine(String.Format("<div class=""nickName"">{0}</div>", node.NickName))
            End If
            strContent.AppendLine("<div class=""cloudPannel"">")
            strContent.AppendLine("<div class=""cloudBody"">")
            strContent.AppendLine("<div class=""cloudContent"">")
            strContent.AppendLine(String.Format("<pre style=""white-space:pre-wrap;*white-space:pre;*word-wrap:break-word;"">{0}</pre>", node.SmsContent))
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("<div class=""cloudArrow""></div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strRelust = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtmlContentText")
        End Try
        Return strRelust
    End Function

    Private Shared Function CreateHtmlContentSystemTip(strFilePath As String, node As IMHtmlNode, isShowTime As Boolean) As String
        Dim strRelust As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""chatItem {0}"">", (IIf(node.IsItemMe, "me", "you"))))
            If isShowTime Then
                strContent.AppendLine(String.Format("<div class=""time""><span class=""timeText"">{0}</span></div>", node.SmsTime))
            End If
            strContent.AppendLine("<div class=""clear""></div>")
            strContent.AppendLine(String.Format("<div class=""systemTip""><span>{0}</span></div>", node.SmsContent))
            strContent.AppendLine("</div>")
            strRelust = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtmlContentSystemTip")
        End Try
        Return strRelust
    End Function

    Private Shared Function CreateHtmlContentMedia(strFilePath As String, node As IMHtmlNode, isShowTime As Boolean) As String
        Dim strRelust As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""chatItem {0}"">", (IIf(node.IsItemMe, "me", "you"))))
            If isShowTime Then
                strContent.AppendLine(String.Format("<div class=""time""><span class=""timeText"">{0}</span></div>", node.SmsTime))
            End If
            If node.IsChatRoom AndAlso Not node.IsItemMe Then
                strContent.AppendLine(String.Format("<div class=""nickName"">{0}</div>", node.NickName))
            End If
            strContent.AppendLine("<div class=""clear""></div>")
            Dim sMediaNode As String = CloudMedia(node)
            strContent.AppendLine(sMediaNode)
            strContent.AppendLine("</div>")
            strRelust = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtmlContentMedia")
        End Try
        Return strRelust
    End Function

    Private Shared Function CloudMedia(node As IMHtmlNode) As String
        Dim strDiv As String = ""
        Try
            Dim arrJson As JsonArray = node.ArrContentForWeb
            'TryCast(CType(node.SmsContent, Object), JsonArray)
            Dim strContent As New StringBuilder()

            Dim wsNodeUrl As String = ""
            Dim wsNodeTitle As String = ""
            Dim wsNodePubtime As String = ""
            Dim wsNodeCover As String = ""
            Dim wsNodeDigest As String = ""

            If arrJson Is Nothing OrElse arrJson.Count = 0 Then
                GoTo Do_Exit
            End If

            If arrJson.Count > 1 Then
                strContent.AppendLine(String.Format("<div class=""media {0}"">", (IIf(node.IsDeleted, "deleted", ""))))
                strContent.AppendLine("<div class=""mediaPanel"">")
                Dim intIndex As Integer = 0
                For Each item As JsonObject In arrJson
                    wsNodeUrl = DirectCast(item("url"), JsonString).Value
                    wsNodeTitle = DirectCast(item("title"), JsonString).Value
                    wsNodePubtime = DirectCast(item("pub_time"), JsonString).Value
                    wsNodeCover = DirectCast(item("cover"), JsonString).Value
                    wsNodeDigest = DirectCast(item("digest"), JsonString).Value

                    If intIndex < 1 Then
                        strContent.AppendLine("<div class=""mediaImg"">")
                        strContent.AppendLine("<div class=""mediaImgPanel"">")
                        strContent.AppendLine(String.Format("<a href=""{0}"" target=""_blank""><img class=""lazy"" src=""{1}"" data-original=""{2}"" /></a>", wsNodeUrl, wsNodeCover, wsNodeCover))
                        strContent.AppendLine("</div>")
                        strContent.AppendLine("<div class=""mediaImgFooter"">")
                        strContent.AppendLine(String.Format("<p class=""mesgTitleTitle left""><a  title=""{0}"" href=""{1}"">{2}</a></p>", wsNodeTitle, wsNodeUrl, wsNodeTitle))
                        strContent.AppendLine("<div class=""clr""></div>")
                        strContent.AppendLine("</div>")
                        strContent.AppendLine("</div>")
                    Else
                        If intIndex = 1 Then
                            strContent.AppendLine("<div class=""mediaContent"">")
                        End If

                        strContent.AppendLine("<div class=""mediaMesg"">")
                        strContent.AppendLine("<span class=""mediaMesgDot""></span>")
                        strContent.AppendLine(String.Format("<div class=""mediaMesgTitle left""><p class=""left""><a href=""{0}"" target=""_blank"">{1}</a></p></div>", wsNodeUrl, wsNodeTitle))
                        strContent.AppendLine("<div class=""mediaMesgIcon right"">")
                        strContent.AppendLine(String.Format("<a href=""{0}"" target=""_blank""><img class=""lazy"" src=""{1}"" data-original=""{2}""></a>", wsNodeUrl, wsNodeCover, wsNodeCover))
                        strContent.AppendLine("</div>")
                        strContent.AppendLine("<div class=""clr""></div>")
                        strContent.AppendLine("</div>")

                        If (intIndex = arrJson.Count - 1) Then
                            strContent.AppendLine("</div>")
                        End If
                    End If

                    intIndex = intIndex + 1
                Next

                strContent.AppendLine("</div>")


                strContent.AppendLine("</div>")
            Else
                Dim item As JsonObject = DirectCast(arrJson(0), JsonObject)

                wsNodeUrl = DirectCast(item("url"), JsonString).Value
                wsNodeTitle = DirectCast(item("title"), JsonString).Value

                If item.ContainsKey("pub_time") Then
                    wsNodePubtime = DirectCast(item("pub_time"), JsonString).Value
                End If

                If item.ContainsKey("cover") Then
                    wsNodeCover = DirectCast(item("cover"), JsonString).Value
                End If

                If item.ContainsKey("digest") Then
                    wsNodeDigest = DirectCast(item("digest"), JsonString).Value
                End If

                strContent.AppendLine(String.Format("<div class=""media mediaFullText {0}"">", (IIf(node.IsDeleted, "deleted", ""))))

                strContent.AppendLine("<div class=""mediaPanel""><div class=""mediaHead"">")
                strContent.AppendLine(String.Format("<div class=""title left""><a href=""{0}"" target=""_blank"">{1}</a></div>", wsNodeUrl, wsNodeTitle))
                strContent.AppendLine("<div class=""clr""></div></div>")

                If wsNodeCover.Length <> 0 Then
                    strContent.AppendLine(String.Format("<div class=""mediaImg""><a href=""{0}"" target=""_blank""><img class=""lazy"" src=""{1}"" data-original=""{2}""></a></div>", wsNodeUrl, wsNodeCover, wsNodeCover))
                End If

                strContent.AppendLine(String.Format("<div class=""mediaContent mediaContentP""><p>{0}</p></div>", wsNodeDigest))
                strContent.AppendLine("<div class=""mediaFooter"">")
                strContent.AppendLine(String.Format("<a href=""{0}"" target=""_blank""><span class=""mesgText left"">查看全文</span></a>", wsNodeUrl))
                strContent.AppendLine("<div class=""clr""></div>")
                strContent.AppendLine("</div>")

                strContent.AppendLine("</div>")

                strContent.AppendLine("</div>")
            End If
            strDiv = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CloudMedia")
        End Try
Do_Exit:
        Return strDiv
    End Function

    Private Shared Function CreateHtmlContentImage(strFilePath As String, node As IMHtmlNode, isShowTime As Boolean) As String
        Dim strRelust As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""chatItem {0}"">", (IIf(node.IsItemMe, "me", "you"))))
            If isShowTime Then
                strContent.AppendLine(String.Format("<div class=""time""><span class=""timeText"">{0}</span></div>", node.SmsTime))
            End If
            strContent.AppendLine("<div class=""clear""></div>")
            strContent.AppendLine("<div class=""chatItemContent"">")
            strContent.AppendLine(String.Format("<img class=""avatar"" src=""{0}"" title=""{1}"">", node.HeaderIconUrl, node.WeChatID))
            Dim sImageNode As String = CloudImage(node)
            strContent.AppendLine(sImageNode)
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strRelust = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtmlContentImage")
        End Try
        Return strRelust
    End Function

    Private Shared Function CloudImage(node As IMHtmlNode) As String
        Dim strDiv As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""cloud cloudImg {0}"">", (IIf(node.IsDeleted, "deleted", ""))))
            If node.IsChatRoom AndAlso Not node.IsItemMe Then
                strContent.AppendLine(String.Format("<div class=""nickName"">{0}</div>", node.NickName))
            End If
            strContent.AppendLine("<div class=""cloudPannel"">")
            strContent.AppendLine("<div class=""cloudBody"">")
            strContent.AppendLine("<div class=""cloudContent"">")
            strContent.AppendLine(String.Format("<a href=""{0}"" target=""_blank""><img class=""zoomIn imageBorder"" src=""{1}"" /></a>", node.AttachPath, node.ThumbnailPath))
            strContent.AppendLine("</div>")
            strContent.AppendLine("<div class=""cloudArrow""></div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")

            strDiv = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CloudImage")
        End Try
        Return strDiv
    End Function

    Private Shared Function CreateHtmlContentVideo(strFilePath As String, node As IMHtmlNode, isShowTime As Boolean) As String
        Dim strRelust As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""chatItem {0}"">", (IIf(node.IsItemMe, "me", "you"))))
            strContent.AppendLine("<div class=""chatItemContent"">")
            If isShowTime Then
                strContent.AppendLine(String.Format("<div class=""time""><span class=""timeText"">{0}</span></div>", node.SmsTime))
            End If
            strContent.AppendLine("<div class=""clear""></div>")
            strContent.AppendLine(String.Format("<img class=""avatar"" src=""{0}"" title=""{1}"">", node.HeaderIconUrl, node.WeChatID))
            Dim sImageNode As String = CloudVideo(node)
            strContent.AppendLine(sImageNode)
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strRelust = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtmlContentImage")
        End Try
        Return strRelust
    End Function

    Private Shared Function CloudVideo(node As IMHtmlNode) As String
        Dim strDiv As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""cloud cloudVedio{0}"">", (IIf(node.IsDeleted, " deleted", ""))))
            If node.IsChatRoom AndAlso Not node.IsItemMe Then
                strContent.AppendLine(String.Format("<div class=""nickName"">{0}</div>", node.NickName))
            End If
            strContent.AppendLine("<div class=""cloudPannel"">")
            strContent.AppendLine("<div class=""cloudBody"">")
            strContent.AppendLine("<div class=""cloudContent"">")
            strContent.AppendLine(String.Format("<a href=""{1}""id=""{0}"" target=""_blank"">", node.UserID, node.AttachPath))
            strContent.AppendLine(String.Format("<img class=""zoomIn imageBorder thumb"" src=""{0}"">", node.ThumbnailPath))
            strContent.AppendLine("<img class=""play"" src=""../assets/images/play.png"">")
            strContent.AppendLine("</a>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("<div class=""cloudArrow ""></div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")

            strDiv = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CloudVoice")
        End Try
        Return strDiv
    End Function

    Private Shared Function CreateHtmlContentVoice(strFilePath As String, node As IMHtmlNode, isShowTime As Boolean) As String
        Dim strRelust As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""chatItem {0}"">", (IIf(node.IsItemMe, "me", "you"))))
            If isShowTime Then
                strContent.AppendLine(String.Format("<div class=""time""><span class=""timeText"">{0}</span></div>", node.SmsTime))
            End If
            strContent.AppendLine("<div class=""clear""></div>")
            strContent.AppendLine("<div class=""chatItemContent"">")
            strContent.AppendLine(String.Format("<img class=""avatar"" src=""{0}"" title=""{1}"">", node.HeaderIconUrl, node.WeChatID))
            Dim sVoiceNode As String = CloudVoice(node)
            strContent.AppendLine(sVoiceNode)
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strRelust = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtmlContentVoice")
        End Try
        Return strRelust
    End Function

    Private Shared Function CloudVoice(node As IMHtmlNode) As String
        Dim strDiv As String = ""
        Try
            Dim strContent As New StringBuilder()
            strContent.AppendLine(String.Format("<div class=""cloud cloudVoice {0}"">", (IIf(node.IsDeleted, "deleted", ""))))
            If node.IsChatRoom AndAlso Not node.IsItemMe Then
                strContent.AppendLine(String.Format("<div class=""nickName"">{0}</div>", node.NickName))
            End If
            strContent.AppendLine("<div class=node""cloudPannel""node>")
            strContent.AppendLine(String.Format("<div class=""sendStatus""><span class=""second"" >{0}</span></div>", node.SmsContent))
            strContent.AppendLine("<div class=""cloudBody"">")
            strContent.AppendLine("<div class=""cloudContent"">")
            strContent.AppendLine(String.Format("<a id=""icoVoice_{0}"" target=""_blank"" class=""icoVoice stop"" href=""{1}""><span></span></a>", node.UserID, node.AttachPath))
            strContent.AppendLine("</div>")
            strContent.AppendLine("<div class=""cloudArrow""></div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")
            strContent.AppendLine("</div>")

            strDiv = strContent.ToString()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CloudVoice")
        End Try
        Return strDiv
    End Function

    Private Shared Function CreateHtmlHeader(strTitle As String, strFilePath As String) As Object
        Dim isRelust As Boolean = True
        Try
            Dim sbContent As New StringBuilder()
            sbContent.AppendLine("<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"">")
            sbContent.AppendLine("<html xmlns=""http://www.w3.org/1999/xhtml"">")
            sbContent.AppendLine("<head>")
            sbContent.AppendLine("<meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8"">")
            sbContent.AppendLine("<meta http-equiv=""Content-Language"" content=""zh-cn"">")
            sbContent.AppendLine(String.Format("<title>{0}</title>", strTitle))
            sbContent.AppendLine("<script type=""text/javascript"">")
            sbContent.AppendLine("window.onload=function(){")
            sbContent.AppendLine("document.body.scrollTop = 1000000;")
            sbContent.AppendLine("}")
            sbContent.AppendLine("</script>")
            sbContent.AppendLine("<link rel=""stylesheet"" type=""text/css"" href=""../assets/css/main.css?2014070501"">")
            sbContent.AppendLine("</head>")
            sbContent.AppendLine("<body>")
            sbContent.AppendLine("<div id=""chat_chatmsglist"" class=""chatContent"">")

            WriteText(strFilePath, sbContent.ToString())
        Catch ex As Exception
            isRelust = False
            Common.LogException(ex.ToString(), "CreateHtmlHeader")
        End Try
        Return isRelust
    End Function

    Private Shared Function CreateHtmlEnd(strFilePath As String) As Object
        Dim isRelust As Boolean = True
        Try
            Dim sbContent As New StringBuilder()
            sbContent.AppendLine("</div>")
            sbContent.AppendLine("</body>")
            sbContent.AppendLine("</html>")

            WriteText(strFilePath, sbContent.ToString())
        Catch ex As Exception
            isRelust = False
            Common.LogException(ex.ToString(), "CreateHtmlEnd")
        End Try
        Return isRelust
    End Function

    Private Shared Sub WriteText(strTempFilePath As String, strExcelContacts As String)
        Try
            'Dim myByte As Byte() = System.Text.Encoding.Default.GetBytes(strExcelContacts) '导出韩文乱码
            'Using fsWrite As New FileStream(strTempFilePath, FileMode.Append)
            '    fsWrite.Write(myByte, 0, myByte.Length)
            'End Using
            Using writer As New StreamWriter(strTempFilePath, True, Encoding.UTF8)
                writer.WriteLine(strExcelContacts)
                writer.Flush()
                writer.Close()
            End Using
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WriteText")
        End Try
    End Sub

    Public Shared Function CreateMainHtml(strName As String, strPath As String, lstExportHtmlMainInfo As List(Of ExportHtmlMainInfo)) As Boolean
        Dim isRelust As Boolean = True
        Try
            '创建头部
            CreateMainHtmlHeader(strName, strPath)

            '创建内容
            CreateMainHtmlContent(strPath, lstExportHtmlMainInfo)

            '创建尾部
            CreateMainHtmlEnd(strPath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateHtml")
        End Try
        Return isRelust
    End Function

    Private Shared Function CreateMainHtmlHeader(strName As String, strPath As String) As Boolean
        Dim isRelust As Boolean = True
        Try
            Dim sbContent As New StringBuilder()
            sbContent.AppendLine("<!DOCTYPE html PUBLIC ""-//W3C//DTD XHTML 1.0 Transitional//EN"" ""http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"">")
            sbContent.AppendLine("<html xmlns=""http://www.w3.org/1999/xhtml"">")
            sbContent.AppendLine("<head>")
            sbContent.AppendLine("<meta http-equiv=""Content-Type"" content=""text/html; charset=utf-8"">")
            sbContent.AppendLine("<meta http-equiv=""Content-Language"" content=""zh-cn"">")
            sbContent.AppendLine(String.Format("<title>{0}</title>", strName))

            sbContent.AppendLine("<link href=""./assets/css/reset.css"" rel=""stylesheet""/>")
            sbContent.AppendLine("<link href=""./assets/css/style.css"" rel=""stylesheet""/>")
            sbContent.AppendLine("<script type=""text/javascript"" src=""./assets/js/jquery.1.8.3.min.js""></script>")

            sbContent.AppendLine("<script type=""text/javascript"">")
            sbContent.AppendLine("function changeIframe() {")
            sbContent.AppendLine("var path = $(this).attr('path')")
            sbContent.AppendLine("$(this).addClass('is-active').siblings().removeClass(""is-active"")")
            sbContent.AppendLine("$('.main--right iframe').attr('src', path)")
            sbContent.AppendLine("}")
            sbContent.AppendLine("$(document).ready(function() {")
            sbContent.AppendLine("if(window.ActiveXObject || ""ActiveXObject"" in window) {")
            sbContent.AppendLine("document.getElementById('browsehappy').style.display = 'block'")
            sbContent.AppendLine("} else {")
            sbContent.AppendLine("document.getElementById('browsehappy').style.display = 'none'")
            sbContent.AppendLine("}")
            sbContent.AppendLine("$('.nav__item').bind('click',changeIframe)")
            sbContent.AppendLine("});")
            sbContent.AppendLine("</script>")

            sbContent.AppendLine("</head>")
            sbContent.AppendLine("<body>")
            sbContent.AppendLine("<div class=""wrap"">")
            sbContent.AppendLine("  <p id=""browsehappy"" class=""browsehappy"">为了获得更好的浏览体验，推荐您使用最新<a href=""http://www.google.cn/chrome/browser/desktop/index.html"" target=""_blank"">Chrome浏览器、</a><a href=""http://www.firefox.com.cn/download/"" target=""_blank"">Firefox浏览器、</a>或者<a href=""http://ie.sogou.com/"" target=""_blank"">搜狗浏览器</a>打开本聊天记录HTML文件</p>")

            WriteText(strPath, sbContent.ToString())
        Catch ex As Exception
            isRelust = False
            Common.LogException(ex.ToString(), "CreateMainHtmlHeader")
        End Try
        Return isRelust
    End Function

    Private Shared Function CreateMainHtmlContent(strPath As String, lstExportHtmlMainInfo As List(Of ExportHtmlMainInfo)) As Boolean
        Dim isRelust As Boolean = True
        Try
            Dim strContent As New StringBuilder()

            '创建左边
            strContent.AppendLine("<div class=""main--left"" >")
            strContent.AppendLine("<ul class=""nav__box"">")
            For Each info As ExportHtmlMainInfo In lstExportHtmlMainInfo
                Try
                    strContent.AppendLine(String.Format("<li class=""{0}"" path=""{1}"">", (IIf(info.id Mod 2 = 1, "nav__item", "nav__item even")), info.StrUrl))
                    strContent.AppendLine(String.Format("<div class=""icon""><img src=""{0}"" /></div>", info.IconImage))
                    strContent.AppendLine("<div class=""content"">")
                    strContent.AppendLine(String.Format("<p>{0}</p>", info.UserName))
                    'strContent.AppendLine(String.Format("<p class=""summary"">{0}</p>", info.LastMessage))
                    strContent.AppendLine("</div>")
                    strContent.AppendLine("<div class=""time"">")
                    strContent.AppendLine(String.Format("<p>{0}</p>", GetLastChatTime(info.LastMessageTime)))
                    strContent.AppendLine("</div>")
                    strContent.AppendLine("</li>")

                    If strContent.Length >= 1000 Then
                        WriteText(strPath, strContent.ToString())
                        strContent.Remove(0, strContent.Length)
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "CreateMainHtmlContent_1")

                End Try
            Next
            strContent.AppendLine("</ul>")
            strContent.AppendLine("</div>")

            '创建右边
            strContent.AppendLine("<div class=""main--right"">")
            strContent.AppendLine(String.Format("<iframe src=""{0}"" frameborder=""no""></iframe>", lstExportHtmlMainInfo(0).StrUrl))
            strContent.AppendLine("</div>")


            WriteText(strPath, strContent.ToString())
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateMainHtmlContent")
        End Try
        Return isRelust
    End Function

    Private Shared Function GetLastChatTime(LastChatTime As DateTime) As String
        Dim strTime As String = String.Empty
        Dim ts As TimeSpan = DateTime.Now.[Date].Subtract(LastChatTime.[Date])
        '只需比较日期就好

        Select Case ts.Days
            Case 0
                strTime = LastChatTime.ToString("HH:mm")

                Exit Select
            Case 1
                strTime = "昨天"

                Exit Select
            Case Else
                strTime = LastChatTime.ToString("yy-MM-dd")

                Exit Select
        End Select

        Return strTime
    End Function

    Private Shared Function CreateMainHtmlEnd(strPath As String) As Boolean
        Dim isRelust As Boolean = True
        Try
            Dim sbContent As New StringBuilder()
            sbContent.AppendLine("</div>")
            sbContent.AppendLine("</body>")
            sbContent.AppendLine("</html>")

            WriteText(strPath, sbContent.ToString())
        Catch ex As Exception
            isRelust = False
            Common.LogException(ex.ToString(), "CreateMainHtmlEnd")
        End Try
        Return isRelust

    End Function

End Class

Public Enum IMHtmlNodeType
    IMHtmlNode_Text
    IMHtmlNode_Image
    IMHtmlNode_Voice
    IMHtmlNode_Media
    IMHtmlNode_Video
    IMHtmlNode_SystemTip
End Enum

Public Class IMHtmlNode

    Private mType As IMHtmlNodeType
    Private mDeleted As Boolean
    Private mItemMe As Boolean
    Private mUserName As String = ""
    Private mUserID As String = ""
    Private mNickName As String = ""
    Private mHeaderIconUrl As String = ""
    Private mSmsTime As String = ""
    Private mSmsContent As String = ""
    Private mThumbnailPath As String = ""
    Private mAttachPath As String = ""

    Private mWeChatID As String = ""

    Private mIsChatRoom As Boolean = False

    Private mArrContentForWeb As JsonArray
    Public Property NType() As IMHtmlNodeType
        Get
            Return Me.mType
        End Get
        Set(value As IMHtmlNodeType)
            Me.mType = value
        End Set
    End Property

    Public Property IsDeleted() As Boolean
        Get
            Return Me.mDeleted
        End Get
        Set(value As Boolean)
            Me.mDeleted = value
        End Set
    End Property

    Public Property IsItemMe() As Boolean
        Get
            Return Me.mItemMe
        End Get
        Set(value As Boolean)
            Me.mItemMe = value
        End Set
    End Property

    Public Property UserName() As String
        Get
            Return Me.mUserName
        End Get
        Set(value As String)
            Me.mUserName = value
        End Set
    End Property

    Public Property UserID() As String
        Get
            Return Me.mUserID
        End Get
        Set(value As String)
            Me.mUserID = value
        End Set
    End Property

    Public Property NickName() As String
        Get
            Return Me.mNickName
        End Get
        Set(value As String)
            Me.mNickName = value
        End Set
    End Property

    Public Property WeChatID() As String
        Get
            Return Me.mWeChatID
        End Get
        Set(value As String)
            Me.mWeChatID = value
        End Set
    End Property

    Public Property HeaderIconUrl() As String
        Get
            Return Me.mHeaderIconUrl
        End Get
        Set(value As String)
            Me.mHeaderIconUrl = value
        End Set
    End Property

    Public Property SmsTime() As String
        Get
            Return Me.mSmsTime
        End Get
        Set(value As String)
            Me.mSmsTime = value
        End Set
    End Property

    Public Property SmsContent() As String
        Get
            Return Me.mSmsContent
        End Get
        Set(value As String)
            Me.mSmsContent = value
        End Set
    End Property

    Public Property ThumbnailPath() As String
        Get
            Return Me.mThumbnailPath
        End Get
        Set(value As String)
            Me.mThumbnailPath = value
        End Set
    End Property

    Public Property AttachPath() As String
        Get
            Return Me.mAttachPath
        End Get
        Set(value As String)
            Me.mAttachPath = value
        End Set
    End Property

    Public Property ArrContentForWeb() As JsonArray
        Get
            Return Me.mArrContentForWeb
        End Get
        Set(value As JsonArray)
            Me.mArrContentForWeb = value
        End Set
    End Property

    Public Property IsChatRoom() As Boolean
        Get
            Return Me.mIsChatRoom
        End Get
        Set(value As Boolean)
            Me.mIsChatRoom = value
        End Set
    End Property

End Class
