﻿Imports System.Threading

Public Class WeixinEmotionWebHelper

    Private mType As WeixinEmoWebType = WeixinEmoWebType.News                                 '表情编号
    Private mListViewEmotcion As tbListViewEx                   '微信表情包的ListView
    Private mListViewEmotcionIcon As tbListViewEx               '微信表情图片ListView
    Private mTotalEmoticon As Integer = 0
    Private mPageSize As Integer = 40
    Private mIsLoadRolling As Boolean = False                   '要不要加载广告轮播
    Private mRollings As List(Of WeixinEmotionWebRolling)       '广告对像
    Private mIconSize As New Size(130, 130)
    Private mRollingSize As New Size(636, 128)

    Private mThreadLoadEmotion As Thread
    Public Event OnLoadEmotionWeb(ByVal sender As Object, ByVal args As WeixinEmoticonArgs)
    Public Event OnLoadIconImage(ByVal sender As Object, ByVal item As ListViewItem)
    Public Event OnLoadRolling(ByVal sender As Object)

#Region "--- 属性 ---"

    Public Property Type() As WeixinEmoWebType
        Get
            Return Me.mType
        End Get
        Set(value As WeixinEmoWebType)
            Me.mType = value
        End Set
    End Property

    Public ReadOnly Property TotalPage() As Integer
        Get
            Dim intPageCount As Integer = Me.mTotalEmoticon \ Me.mPageSize

            If Me.mTotalEmoticon Mod Me.mPageSize > 0 Then
                intPageCount += 1
            End If
            Return intPageCount
        End Get
    End Property

    Public ReadOnly Property CurrentPage() As Integer
        Get
            Dim intIndex As Integer = 0
            If Me.mListViewEmotcion IsNot Nothing AndAlso Me.mListViewEmotcion.Items.Count > 0 Then
                intIndex = Me.mListViewEmotcion.Items.Count / Me.mPageSize
            End If
            Return intIndex
        End Get
    End Property

    Public Property TotalEmoticon() As Integer
        Get
            Return Me.mTotalEmoticon
        End Get
        Set(value As Integer)
            Me.mTotalEmoticon = value
        End Set
    End Property

    Public Property ListViewEmoticon() As tbListViewEx
        Get
            Return Me.mListViewEmotcion
        End Get
        Set(value As tbListViewEx)
            Me.mListViewEmotcion = value
        End Set
    End Property

    Public Property ListViewEmoticonIcon() As tbListViewEx
        Get
            Return Me.mListViewEmotcionIcon
        End Get
        Set(value As tbListViewEx)
            Me.mListViewEmotcionIcon = value
        End Set
    End Property

    Public Property IsLoadRolling() As Boolean
        Get
            Return Me.mIsLoadRolling
        End Get
        Set(value As Boolean)
            Me.mIsLoadRolling = value
        End Set
    End Property

    Public Property Rollings() As List(Of WeixinEmotionWebRolling)
        Get
            Return Me.mRollings
        End Get
        Set(value As List(Of WeixinEmotionWebRolling))
            Me.mRollings = value
        End Set
    End Property

#End Region

#Region "--- 初始化 ---"

#Region "--- 单实例 ---"

    Private Shared mInstance As New Dictionary(Of WeixinEmoWebType, WeixinEmotionWebHelper)           '按站点名字生成相应的实例
    Private Shared ReadOnly locker As New Object

    Public Shared Function Instance(ByVal type As WeixinEmoWebType) As WeixinEmotionWebHelper
        SyncLock locker
            If Not mInstance.ContainsKey(type) Then
                mInstance.Add(type, New WeixinEmotionWebHelper(type))
            End If
        End SyncLock
        Return mInstance(type)
    End Function

#End Region

    Sub New(ByVal type As WeixinEmoWebType)
        Me.mType = type
    End Sub

#End Region

#Region "--- 轮播广告 ---"

    '加载广告轮播内容
    Public Sub LoadRolling()
        If Not Me.mIsLoadRolling OrElse Me.mRollings IsNot Nothing Then
            Return
        End If
        Try
            Dim strResult As String = HtmlHelper.PostWeixinEmoData("Rolling", Me.GetRollingPostJson())
            If Not String.IsNullOrEmpty(strResult) Then
                Me.mRollings = Me.ReadRollingJson(strResult)
            End If

            RaiseEvent OnLoadRolling(Me)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_LoadRolling")
        End Try
    End Sub

    Private Function GetRollingPostJson() As String
        Dim dicChat As New JsonObject()
        dicChat.Add("Version", Common.GetSoftVersion)

        Dim writer As New JsonWriter()
        dicChat.Write(writer)
        Return writer.ToString()
    End Function

    Private Function ReadRollingJson(ByVal strJson As String) As List(Of WeixinEmotionWebRolling)
        Dim lstRolling As New List(Of WeixinEmotionWebRolling)
        Try
            Dim jObj As JsonObject = JsonParser.ParseString(strJson)
            If Not jObj.ContainsKey("Data") OrElse TypeOf jObj("Data") Is JsonNull Then
                Return lstRolling
            End If

            Dim jData As JsonObject = jObj("Data")
            If Not jData.ContainsKey("Items") OrElse TypeOf jData("Items") Is JsonNull Then
                Return lstRolling
            End If

            Dim jItems As JsonArray = jData("Items")
            For Each item As JsonObject In jItems
                Dim roll As New WeixinEmotionWebRolling()
                If item.ContainsKey("Id") AndAlso Not TypeOf item("Id") Is JsonNull Then
                    roll.Id = CType(item("Id"), JsonNumber).Value
                End If

                If item.ContainsKey("Image") AndAlso Not TypeOf item("Image") Is JsonNull Then
                    roll.ImageURL = CType(item("Image"), JsonString).Value
                End If

                If item.ContainsKey("Uri") AndAlso Not TypeOf item("Uri") Is JsonNull Then
                    roll.Uri = CType(item("Uri"), JsonString).Value
                End If
                roll.Image = Me.GetItemImage(roll.ImageURL, Me.mRollingSize)
                lstRolling.Add(roll)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_ReadRollingJson")
        End Try
        Return lstRolling
    End Function

#End Region

#Region "--- 加载表情包列表 ---"

    Public Sub FirstLoadPacks()
        If Me.ListViewEmoticon IsNot Nothing AndAlso Me.ListViewEmoticon.Items.Count > 0 Then
            Return
        End If
        Me.LoadPacks()
    End Sub

    Public Sub LoadPacks(Optional ByVal blnRefresh As Boolean = False)
        Try
            '刷新
            If blnRefresh AndAlso Me.ListViewEmoticon IsNot Nothing Then
                Me.ListViewEmoticon.Items.Clear()
            End If

            If Me.mThreadLoadEmotion IsNot Nothing AndAlso Me.mThreadLoadEmotion.ThreadState <> ThreadState.Stopped Then
                Me.mThreadLoadEmotion.Abort()
            End If

            Me.mThreadLoadEmotion = New Thread(AddressOf LoadPacksThread)
            With Me.mThreadLoadEmotion
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_LoadPacks")
        End Try
    End Sub

    Private Sub LoadPacksThread()
        Try
            Dim args As New WeixinEmoticonArgs
            args.Type = WeixinEmoticonLoadType.EmoticonPack
            args.Status = EmoticonStatus.Process
            '开始加载
            RaiseEvent OnLoadEmotionWeb(Me, args)

            '加载广告
            Me.LoadRolling()

            Dim strResult As String = HtmlHelper.PostWeixinEmoData("Packs", Me.GetPacksPostJson)
            If Not String.IsNullOrEmpty(strResult) Then
                args.LstEmoticon = Me.ReadPacksJson(strResult)
                args.Status = EmoticonStatus.Succeed
                args.Current = Me.CurrentPage
            Else
                args.Status = EmoticonStatus.Failure
            End If
            '加载完成
            RaiseEvent OnLoadEmotionWeb(Me, args)
            Me.LoadItemImage(Me.mListViewEmotcion)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_LoadPacksThread")
        End Try
    End Sub

    Private Function GetPacksPostJson() As String
        Dim dicChat As New JsonObject()
        Dim intId As Integer = 0
        Select Case Me.mType
            Case WeixinEmoWebType.News
                intId = -1
            Case WeixinEmoWebType.Hot
                intId = -2
            Case WeixinEmoWebType.Selected
                intId = -3
        End Select

        dicChat.Add("category", intId)
        dicChat.Add("pageIndex", Me.CurrentPage)
        dicChat.Add("pageSize", mPageSize)

        Dim writer As New JsonWriter()
        dicChat.Write(writer)
        Return writer.ToString()
    End Function

    Private Function ReadPacksJson(ByVal strJson As String) As List(Of Emoticon)
        Dim lstEmo As New List(Of Emoticon)
        Try
            Dim jObj As JsonObject = JsonParser.ParseString(strJson)
            If Not jObj.ContainsKey("Data") OrElse TypeOf jObj("Data") Is JsonNull Then
                Return lstEmo
            End If

            Dim jData As JsonObject = jObj("Data")
            If Not jData.ContainsKey("Items") OrElse TypeOf jData("Items") Is JsonNull Then
                Return lstEmo
            End If

            If jData.ContainsKey("Total") AndAlso Not TypeOf jData("Total") Is JsonNull Then
                Me.mTotalEmoticon = CType(jData("Total"), JsonNumber).Value
            End If

            Dim jItems As JsonArray = jData("Items")
            For Each item As JsonObject In jItems
                Dim emo As New Emoticon("")
                If item.ContainsKey("Id") AndAlso Not TypeOf item("Id") Is JsonNull Then
                    emo.EmoticonPackageId = CType(item("Id"), JsonNumber).Value
                End If

                If item.ContainsKey("Title") AndAlso Not TypeOf item("Title") Is JsonNull Then
                    emo.EmoticonPackageName = CType(item("Title"), JsonString).Value
                End If

                If item.ContainsKey("Thumb") AndAlso Not TypeOf item("Thumb") Is JsonNull Then
                    emo.EmoticonPackageIconUrl = CType(item("Thumb"), JsonString).Value
                End If

                If item.ContainsKey("Url") AndAlso Not TypeOf item("Url") Is JsonNull Then
                    emo.EmoticonPackageUrl = CType(item("Url"), JsonString).Value
                End If
                emo.MD5Myself = Common.ToHexString(Common.GetMd5Array(emo.EmoticonPackageIconUrl))
                emo.Icon = Nothing
                lstEmo.Add(emo)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_ReadPacksJson")
        End Try
        Return lstEmo
    End Function

#End Region

#Region "--- 加载表情包内容 ---"

    Public Sub LoadPackPics(ByVal strPackId As String)
        Try
            Dim thr As New Thread(New ParameterizedThreadStart(AddressOf LoadPackPicsThread))
            With thr
                .IsBackground = True
                .Start(strPackId)
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_LoadPackPics")
        End Try
    End Sub

    Public Sub LoadPackPicsThread(ByVal parObj As Object)
        Try
            Dim args As New WeixinEmoticonArgs
            args.Type = WeixinEmoticonLoadType.EmoticonPic
            args.Status = EmoticonStatus.Process
            '开始加载
            RaiseEvent OnLoadEmotionWeb(Me, args)

            Dim strPackId As String = parObj
            Dim strResult As String = HtmlHelper.PostWeixinEmoData("Emotions", Me.GetPackPicsPostJson(strPackId))
            If Not String.IsNullOrEmpty(strResult) Then
                args.LstEmoticonItem = Me.ReadPacksPicJson(strResult)
                args.Status = EmoticonStatus.Succeed
                args.Total = args.LstEmoticonItem.Count
            Else
                args.Status = EmoticonStatus.Failure
            End If

            '加载完成
            RaiseEvent OnLoadEmotionWeb(Me, args)
            Me.LoadItemImage(Me.mListViewEmotcionIcon)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_LoadPackPicsThread")
        End Try
    End Sub

    Private Function GetPackPicsPostJson(ByVal strPackId As String) As String
        Dim dicChat As New JsonObject()
        dicChat.Add("PackId", strPackId)

        Dim writer As New JsonWriter()
        dicChat.Write(writer)
        Return writer.ToString()
    End Function

    Private Function ReadPacksPicJson(ByVal strJson As String) As List(Of EmoticonItem)
        Dim lstEmoItem As New List(Of EmoticonItem)
        Try
            Dim jObj As JsonObject = JsonParser.ParseString(strJson)
            If Not jObj.ContainsKey("Data") OrElse TypeOf jObj("Data") Is JsonNull Then
                Return lstEmoItem
            End If
            Dim jData As JsonObject = jObj("Data")
            If Not jData.ContainsKey("Items") OrElse TypeOf jData("Items") Is JsonNull Then
                Return lstEmoItem
            End If

            Dim jItems As JsonArray = jData("Items")
            For Each item As JsonObject In jItems
                Dim emoItem As New EmoticonItem()
                If item.ContainsKey("Id") AndAlso Not TypeOf item("Id") Is JsonNull Then
                    emoItem.EmoticonPackageId = CType(item("Id"), JsonNumber).Value
                End If

                If item.ContainsKey("Thumb") AndAlso Not TypeOf item("Thumb") Is JsonNull Then
                    emoItem.IconUrl = CType(item("Thumb"), JsonString).Value
                End If
                emoItem.MD5 = Common.ToHexString(Common.GetMd5Array(emoItem.IconUrl))
                lstEmoItem.Add(emoItem)
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_ReadPacksPicJson")
        End Try
        Return lstEmoItem
    End Function

#End Region

#Region "--- 加载缩略图 ---"

    Public Sub LoadItemImage(ByVal lvw As tbListViewEx)
        Try
            Dim thr As New Thread(New ParameterizedThreadStart(AddressOf LoadItemImageThread))
            With thr
                .IsBackground = True
                .Start(lvw)
            End With
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_LoadItemImage")
        End Try
    End Sub

    Public Sub LoadItemImageThread(ByVal paraObj As Object)
        Try
            Dim lvw As tbListViewEx = paraObj
            For Each item As ListViewItem In lvw.Items
                If item IsNot Nothing AndAlso item.Tag IsNot Nothing AndAlso (TypeOf item.Tag Is Emoticon OrElse TypeOf item.Tag Is EmoticonItem) Then
                    If TypeOf item.Tag Is Emoticon Then
                        Dim emo As Emoticon = item.Tag
                        If emo.Icon IsNot Nothing Then
                            Continue For
                        End If
                        emo.Icon = Me.GetItemImage(emo.EmoticonPackageIconUrl, Me.mIconSize)

                    Else
                        Dim emoItem As EmoticonItem = item.Tag
                        If emoItem.Icon IsNot Nothing Then
                            Continue For
                        End If
                        emoItem.Icon = Me.GetItemImage(emoItem.IconUrl, Me.mIconSize)

                    End If
                    RaiseEvent OnLoadIconImage(Me, item)
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_LoadItemImageThread")
        End Try
    End Sub

    Private Function GetItemImage(ByVal strImageUrl As String, ByVal imgSize As Size) As Image
        Dim img As Image = Nothing
        Try
            Dim strImgPath As String = Path.Combine(Folder.CacheEmoticonFolder, "Web")
            Folder.CheckFolder(strImgPath)
            strImgPath = Path.Combine(strImgPath, Common.ToHexString(Common.GetMd5Array(strImageUrl)))
            If Not File.Exists(strImgPath) Then
                Utility.DownLoadFile(strImageUrl, strImgPath)
            End If

            If File.Exists(strImgPath) Then
                img = Utility.GetImageFormFile(strImgPath)
                img = Utility.GetThumbnail(img, imgSize)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmotionWebHelper_GetItemImage")
        End Try

        Return img
    End Function

#End Region

End Class

Public Class WeixinEmotionWebRolling
    Public Id As Integer = 0
    Public ImageURL As String = String.Empty
    Public Image As Image = Nothing
    Public Uri As String = String.Empty
    Public Show As Boolean = False              '是否显示出来
End Class

Public Enum WeixinEmoWebType
    News
    Hot
    Selected
End Enum
