﻿Imports System.Threading
Imports System.Windows.Forms
Imports System.Drawing
Imports System.Text
Imports System.IO
Imports iTong.Components
Imports iTong.Device
Imports iTong.CoreFoundation


Public Class WeixinEmoticonHepler
    Implements IDisposable

    Private mDevice As iPhoneDevice = Nothing
    Private mstrSKU As String = "com.tencent.xin"
    Private mMD5Myself As String = String.Empty
    Private mUsrNameMyself As String = String.Empty
    Private mAfcHandler As System.IntPtr = IntPtr.Zero

    Private mPathWechatOnPhone As String = String.Empty
    Private mPathWechatOnPC_Temp As String = String.Empty                       '保存数据库（涉及到隐私，程序关闭则会删除）
    Private mPathWechatOnPC_MMDB As String = String.Empty
    Private mPathWechatOnPhone_MMDB As String = String.Empty
    Private mEmoticons As Dictionary(Of String, Emoticon) = Nothing

    Private mInsertToFavorite As Boolean = False
    Private mWechatMMDB As WechatMMDB = Nothing
    Private mBlnWeixin5X As Boolean = False
    Private mGroupIconSize As New Size(115, 115)

    Private mQueueOp As New Queue(Of Object)
    Private mSQLBuilder As New StringBuilder()
    Private mDBInfo As iPhoneFileInfo = Nothing
    Private mThreadLoad As Thread = Nothing
    Private mThreadSync As Thread = Nothing
    Private mThreadRestore As Thread = Nothing

    Public Event OnLoadFinished(ByVal sender As Object, ByVal args As WeixinEmoticonLoadArgs)
    Public Event OnSync As EventHandler(Of WeixinEmoticonArgs)
    Public Event OnRestore As EventHandler


#Region "--- 属性 ---"

    Public ReadOnly Property SKU() As String
        Get
            Return Me.mstrSKU
        End Get
    End Property

    Public ReadOnly Property MD5Myself() As String
        Get
            Return Me.mMD5Myself
        End Get
    End Property

    Public ReadOnly Property AfcHandler() As System.IntPtr
        Get
            Return Me.mAfcHandler
        End Get
    End Property

    Public Property Emoticons() As Dictionary(Of String, Emoticon)
        Get
            Return Me.mEmoticons
        End Get
        Set(ByVal value As Dictionary(Of String, Emoticon))
            Me.mEmoticons = value
        End Set
    End Property

    Public ReadOnly Property BackDBPath() As String
        Get
            Dim strWeiPath As String = Path.Combine(Folder.CacheFolder, "WeixinEmoticon")
            Dim strPath As String = Path.Combine(strWeiPath, Me.mMD5Myself)
            Return strPath
        End Get
    End Property

#End Region

#Region "--- 单实例 ---"

    Private Shared mInstance As New Dictionary(Of String, WeixinEmoticonHepler)
    Private Shared ReadOnly locker As New Object

    Public Shared Function Instance(ByVal device As iPhoneDevice) As WeixinEmoticonHepler
        SyncLock locker
            If Not mInstance.ContainsKey(device.Identifier) Then
                mInstance.Add(device.Identifier, New WeixinEmoticonHepler())
                mInstance(device.Identifier).mDevice = device
            Else
                mInstance(device.Identifier).mDevice = device
            End If
        End SyncLock
        Return mInstance(device.Identifier)
    End Function

#End Region

    '加载数据库
    Public Sub Reload()
        Try
            If Me.mThreadLoad IsNot Nothing AndAlso Me.mThreadLoad.ThreadState <> ThreadState.Stopped Then
                Me.mThreadLoad.Abort()
            End If
        Catch ex As Exception
        End Try

        Me.mThreadLoad = New Thread(AddressOf ReloadThread)
        Me.mThreadLoad.IsBackground = True
        Me.mThreadLoad.SetApartmentState(ApartmentState.STA)
        Me.mThreadLoad.Start()
    End Sub

    Private Sub ReloadThread()
        Dim state As WeixinState = WeixinState.Succeed
        Try
            Me.mSQLBuilder.Length = 0
            Me.mQueueOp.Clear()

            '1、判断微信是否安装
            Dim dictApp As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.User)
            If Not dictApp.ContainsKey(SKU) Then
                state = WeixinState.NoInstalled
                GoTo DO_EXIT
            End If

            Me.mAfcHandler = Me.mDevice.GetAfcByFileSharing(SKU, HouseArrestType.Document, True)
            If Me.mAfcHandler = System.IntPtr.Zero Then
                Common.LogException("微信目录访问不到", "微信表情加载数据")
                state = WeixinState.CanNotAccess
                GoTo DO_EXIT
            End If

            ''1、判断微信是否安装
            'Me.mAfcHandler = Me.mDevice.GetAfcByFileSharing(Me.mstrSKU, HouseArrestType.Document, True)
            'If Me.mAfcHandler = System.IntPtr.Zero Then
            '    state = WeixinState.NoInstalled
            '    GoTo DO_EXIT
            'End If

            '2、获取微信是否为5.X
            'Dim strVer As String = Me.GetWeivinVersion(Me.mAfcHandler)
            Dim info As FileSharingPackageInfo = dictApp(SKU)
            If info.CFBundleVersion.Length > 0 AndAlso Common.CompareVer(info.CFBundleVersion, "5.0.0.0", 4) < 0 Then
                Me.mBlnWeixin5X = False
            Else
                Me.mBlnWeixin5X = True
            End If

            '判断微信版本是否大于6.2.2.0
            If info.CFBundleVersion.Length > 0 AndAlso Common.CompareVer(info.CFBundleVersion, "6.2.2.0", 4) >= 0 Then
                Me.mInsertToFavorite = True
                'state = WeixinState.Above6220
                'GoTo DO_EXIT
            End If

            '3、获取登录用户名
            Dim strPathWeixinOnPC As String = String.Format("{0}Weixin", Folder.CacheFolder)
            Folder.CheckFolder(strPathWeixinOnPC)
            Dim strLocalInfoOnPhone As String = "/Documents/LocalInfo.lst"
            Dim strLocalInfoOnPC As String = Path.Combine(strPathWeixinOnPC, "LocalInfo.lst")

            If Me.mDevice.Exists(Me.mAfcHandler, strLocalInfoOnPhone) AndAlso Me.mDevice.DownFromPhone(Me.mAfcHandler, strLocalInfoOnPhone, strLocalInfoOnPC) Then
                Dim strContent As String = iTong.Device.CoreFoundation.ReadPlist(strLocalInfoOnPC)
                strContent = strContent.Replace("$", "")

                Dim arrBytes As Byte() = System.Text.Encoding.Default.GetBytes(strContent)
                Dim dict As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(arrBytes)
                'plist文件 里面如果有汉字会读不出来，重新写到文件 里面再读就正常了。
                If dict Is Nothing Then
                    Try
                        Using sr As New StreamWriter(strLocalInfoOnPC)
                            sr.Write(strContent)
                        End Using
                        dict = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(strLocalInfoOnPC)
                    Catch ex As Exception
                        Common.LogException(ex.ToString, "WeixinEmoticonHelper_ReSavePlist")
                    End Try
                End If
                Dim arrObjects As Object() = dict("objects")
                Me.mUsrNameMyself = arrObjects(2).ToString()
            End If

            If String.IsNullOrEmpty(Me.mUsrNameMyself) Then
                state = WeixinState.NoAccount
                GoTo DO_EXIT
            End If

            '4、判断是否有登录
            Me.mMD5Myself = Common.ToHexString(Common.GetMd5Array(mUsrNameMyself))
            Me.mPathWechatOnPhone = String.Format("/Documents/{0}", Me.mMD5Myself)
            Me.mPathWechatOnPC_Temp = Path.Combine(Folder.TempFolder, Me.mMD5Myself)

            If Not Me.mDevice.Exists(Me.mAfcHandler, Me.mPathWechatOnPhone) Then
                state = WeixinState.NoAccount
                GoTo DO_EXIT
            End If

            '5、下载数据库，读取用户列表
            Me.mPathWechatOnPhone_MMDB = String.Format("{0}/DB/MM.sqlite", Me.mPathWechatOnPhone)
            Dim strDirDBOnPC As String = String.Format("{0}\DB", Me.mPathWechatOnPC_Temp)

            Folder.CheckFolder(strDirDBOnPC)
            Me.mPathWechatOnPC_MMDB = Path.Combine(strDirDBOnPC, "MM.sqlite")

            If Me.mDevice.IsDirectory(Me.mAfcHandler, Me.mPathWechatOnPhone_MMDB, Me.mDBInfo) OrElse Me.mDBInfo Is Nothing Then
                state = WeixinState.NoAccount
            Else
                Dim dateDBPhone As String = Me.GetMMDBKey(Me.mDBInfo)

                '1、删除旧数据库
                If File.Exists(Me.mPathWechatOnPC_MMDB) Then
                    Dim dateDBPC As String = IniSetting.GetMMDate()
                    If String.Compare(dateDBPC, dateDBPhone, True) <> 0 Then
                        Try
                            If Me.mWechatMMDB IsNot Nothing Then
                                Me.mWechatMMDB.CloseDB()
                            End If

                            File.Delete(Me.mPathWechatOnPC_MMDB)
                        Catch
                        End Try
                    End If
                End If

                '2、重新下载数据库
                If Not File.Exists(Me.mPathWechatOnPC_MMDB) Then
                    If Me.mDevice.DownFromPhone(Me.mAfcHandler, Me.mPathWechatOnPhone_MMDB, Me.mPathWechatOnPC_MMDB) Then
                        '下载数据库的另外两个文件
                        If Me.mDevice.Exists(Me.mAfcHandler, Me.mPathWechatOnPhone_MMDB & "-shm") Then
                            Me.mDevice.DownFromPhone(Me.mAfcHandler, Me.mPathWechatOnPhone_MMDB & "-shm", Me.mPathWechatOnPC_MMDB & "-shm")
                        End If
                        If Me.mDevice.Exists(Me.mAfcHandler, Me.mPathWechatOnPhone_MMDB & "-wal") Then
                            Me.mDevice.DownFromPhone(Me.mAfcHandler, Me.mPathWechatOnPhone_MMDB & "-wal", Me.mPathWechatOnPC_MMDB & "-wal")
                        End If

                        Me.BackDB(Me.mPathWechatOnPC_MMDB, Me.mMD5Myself)
                        IniSetting.SetMMDate(dateDBPhone)
                    Else
                        state = WeixinState.DownloadDBFailed
                        GoTo DO_EXIT
                    End If
                End If

                '3、读取信息
                Me.mWechatMMDB = WechatMMDB.Instance(Me.mPathWechatOnPC_MMDB)
                Me.mWechatMMDB.Reload()
                Me.mEmoticons = Me.mWechatMMDB.Get_Emoticons_Info(Me.mMD5Myself)

                For Each group As Emoticon In Me.mEmoticons.Values
                    group.Icon = Me.CreateGroupIconShow(group)
                Next
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwLoadFriend_DoWork")
            state = WeixinState.NoDB
        End Try

DO_EXIT:
        Dim arg As New WeixinEmoticonLoadArgs
        arg.Is622 = Me.mInsertToFavorite
        arg.Status = state
        RaiseEvent OnLoadFinished(Me, arg)
    End Sub

    Private Function GetMMDBKey(ByVal info As iPhoneFileInfo) As String
        Return info.st_mtime.ToString("yyyy-MM-dd HH:mm:ss") & info.st_size.ToString()
    End Function

    Private Sub BackDB(ByVal strDBPath As String, ByVal strPathName As String)
        Try
            If File.Exists(strDBPath) Then
                Dim strDir = Path.Combine(Folder.CacheFolder, "WeixinEmoticon\" & strPathName)
                Dim strFilePath As String = Path.Combine(strDir, DateTime.Now.ToString("yyyy_MM_dd") & ".tmp")
                If File.Exists(strFilePath) Then
                    Return
                End If

                Folder.CheckFolder(strDir)
                Try
                    File.Copy(strDBPath, strFilePath)
                    '如果数据库另外两个文件存在就一起备份
                    If File.Exists(strDBPath & "-shm") Then
                        File.Copy(strDBPath & "-shm", strFilePath & "-shm")
                    End If

                    If File.Exists(strDBPath & "-wal") Then
                        File.Copy(strDBPath & "-wal", strFilePath & "-wal")
                    End If
                Catch ex As Exception
                End Try
            End If
        Catch ex As Exception
        End Try
    End Sub

    Public Sub RestoreDB(ByVal strFilePath As String)
        Try
            If Me.mThreadRestore IsNot Nothing AndAlso Me.mThreadRestore.ThreadState <> ThreadState.Stopped Then
                Me.mThreadRestore.Abort()
            End If
        Catch ex As Exception
        End Try

        Me.mThreadRestore = New Thread(New ParameterizedThreadStart(AddressOf RestoreDBThread))
        Me.mThreadRestore.IsBackground = True
        Me.mThreadRestore.Start(strFilePath)
    End Sub

    Private Sub RestoreDBThread(ByVal strObj As Object)
        Try
            Dim strFilePath As String = ""
            If strObj IsNot Nothing AndAlso TypeOf strObj Is String Then
                strFilePath = strObj
            End If

            'Dim strDir = Path.Combine(Folder.CacheFolder, "WeixinEmoticon\" & Me.mMD5Myself)
            'Dim strFilePath As String = Path.Combine(strDir, DateTime.Now.ToString("yyyy_MM_dd") & ".tmp")

            'If Not File.Exists(strFilePath) Then
            '    Dim arrFiles() As String = System.IO.Directory.GetFiles(strDir, "*.tmp", SearchOption.TopDirectoryOnly)
            '    Dim strMaxName As String = String.Empty

            '    For Each strTmp As String In arrFiles
            '        Dim strName As String = Path.GetFileNameWithoutExtension(strTmp)
            '        If String.Compare(strMaxName, strName) < 0 Then
            '            strMaxName = strName
            '        End If
            '    Next

            '    If Not String.IsNullOrEmpty(strMaxName) Then
            '        strFilePath = Path.Combine(strDir, strMaxName & ".tmp")
            '    End If
            'End If

            If File.Exists(strFilePath) Then
                Me.mWechatMMDB.CloseDB()
                Me.CopyDBFile(strFilePath, "")
                Me.CopyDBFile(strFilePath, "-shm")
                Me.CopyDBFile(strFilePath, "-wal")
                Me.mWechatMMDB.Reload()
            End If

        Catch ex As Exception
        Finally
            RaiseEvent OnRestore(Me, EventArgs.Empty)
        End Try
    End Sub

    Private Sub CopyDBFile(ByVal strFilePath As String, ByVal strFileExit As String)
        If File.Exists(strFilePath & strFileExit) Then
            Me.mDevice.CopyToPhone(Me.mAfcHandler, strFilePath & strFileExit, Me.mPathWechatOnPhone_MMDB & strFileExit)
            File.Copy(strFilePath & strFileExit, Me.mPathWechatOnPC_MMDB & strFileExit, True)
        End If
    End Sub

    '取得微信版本号
    Private Function GetWeivinVersion(ByVal AfcHandler As System.IntPtr) As String
        Dim strVer As String = String.Empty

        Try
            Dim files As String() = Me.mDevice.GetFiles(AfcHandler, "/", True)
            Dim strAppPathOnPhone As String = String.Empty

            For Each Item As String In files
                If Item.EndsWith(".app") Then
                    strAppPathOnPhone = Item
                    Exit For
                End If
            Next

            If String.IsNullOrEmpty(strAppPathOnPhone) Then
                Return strVer
            End If

            Dim strInfoOnPhone As String = "/" & strAppPathOnPhone & "/Info.plist"
            Dim strPathWeixinOnPC As String = String.Format("{0}Weixin", Folder.CacheFolder)
            Folder.CheckFolder(strPathWeixinOnPC)
            Dim strInfoOnPC As String = Path.Combine(strPathWeixinOnPC, Guid.NewGuid.ToString("N") & ".plist")

            If Me.mDevice.Exists(Me.mAfcHandler, strInfoOnPhone) AndAlso Me.mDevice.DownFromPhone(Me.mAfcHandler, strInfoOnPhone, strInfoOnPC) Then
                Dim dict As Dictionary(Of Object, Object) = CType(iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(strInfoOnPC), Dictionary(Of Object, Object))
                If dict IsNot Nothing Then
                    If dict.ContainsKey("CFBundleVersion") Then
                        strVer = CStr(dict.Item("CFBundleVersion"))
                        'If Common.CompareVer(strVersion, "5.0.0.0", 4) < 0 Then
                        '    blnWeixin5X = False
                        'End If
                    End If
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "IsWeixin5X")
        End Try

        Return strVer
    End Function

    Private Function IsWeixin5X(ByVal AfcHandler As System.IntPtr) As Boolean
        Dim blnWeixin5X As Boolean = True

        Try
            Dim files As String() = Me.mDevice.GetFiles(AfcHandler, "/", True)
            Dim strAppPathOnPhone As String = String.Empty

            For Each Item As String In files
                If Item.EndsWith(".app") Then
                    strAppPathOnPhone = Item
                    Exit For
                End If
            Next

            If String.IsNullOrEmpty(strAppPathOnPhone) Then
                Return blnWeixin5X
            End If

            Dim strInfoOnPhone As String = "/" & strAppPathOnPhone & "/Info.plist"
            Dim strPathWeixinOnPC As String = String.Format("{0}Weixin", Folder.CacheFolder)
            Folder.CheckFolder(strPathWeixinOnPC)
            Dim strInfoOnPC As String = Path.Combine(strPathWeixinOnPC, Guid.NewGuid.ToString("N") & ".plist")

            If Me.mDevice.Exists(Me.mAfcHandler, strInfoOnPhone) AndAlso Me.mDevice.DownFromPhone(Me.mAfcHandler, strInfoOnPhone, strInfoOnPC) Then
                Dim dict As Dictionary(Of Object, Object) = CType(iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(strInfoOnPC), Dictionary(Of Object, Object))
                If dict IsNot Nothing Then
                    If dict.ContainsKey("CFBundleVersion") Then
                        Dim strVersion As String = CStr(dict.Item("CFBundleVersion"))
                        If Common.CompareVer(strVersion, "5.0.0.0", 4) < 0 Then
                            blnWeixin5X = False
                        End If
                    End If
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "IsWeixin5X")
        End Try

        Return blnWeixin5X
    End Function

    '关闭数据库
    Public Sub Close()
        Try
            If Me.mThreadLoad IsNot Nothing AndAlso Me.mThreadLoad.ThreadState <> ThreadState.Stopped Then
                Me.mThreadLoad.Abort()
            End If
        Catch ex As Exception
        End Try

        Try
            If mThreadSync IsNot Nothing AndAlso mThreadSync.ThreadState <> ThreadState.Stopped Then
                mThreadSync.Abort()
            End If
        Catch ex As Exception
        End Try

        Try
            If Me.mThreadRestore IsNot Nothing AndAlso mThreadRestore.ThreadState <> ThreadState.Stopped Then
                mThreadRestore.Abort()
            End If
        Catch ex As Exception
        End Try


        Try
            If Me.mWechatMMDB IsNot Nothing Then
                Me.mWechatMMDB.CloseDB()
            End If
        Catch ex As Exception
        End Try

        Try
            mInstance.Remove(Me.mDevice.Identifier)
        Catch ex As Exception
        End Try
    End Sub

    Public Function CreateGroup(ByVal strName As String, ByVal strThumbUrl As String) As Emoticon
        Dim group As Emoticon = Nothing

        If mInsertToFavorite Then
            If Me.mEmoticons.ContainsKey("custom_emoticon_pid") Then
                group = Me.mEmoticons("custom_emoticon_pid")
                group.EmotionOp = EmotionOp.Update
                Me.mQueueOp.Enqueue(group)
            Else
                group = New Emoticon(Me.MD5Myself, "custom_emoticon_pid")
                group.EmoticonPackageName = "我的收藏"
                group.EmotionOp = EmotionOp.Insert
                group.EmoticonPackageInstallTime = WechatHelper.ConvertPcTimeToWeixin(Now)

                Me.mQueueOp.Enqueue(group)

                Me.mEmoticons(group.EmoticonPackageId) = group
            End If

            If group.Items.Count > 120 Then
                For Each item As Emoticon In Me.mEmoticons.Values
                    If item.EmoticonPackageId = group.EmoticonPackageId OrElse item.Items.Count > 120 Then
                        Continue For
                    End If

                    group = item
                    Exit For
                Next
            End If
            '6.2.2添加包含的group
            If Not group.ContainGroup.Contains(strName) Then
                group.ContainGroup.Add(strName)
            End If
        Else
            group = New Emoticon(Me.MD5Myself)

            group.EmoticonPackageName = strName
            group.EmoticonPackageInstallTime = WechatHelper.ConvertPcTimeToWeixin(Now)
            group.EmotionOp = EmotionOp.Insert
            If strThumbUrl.Trim.Length > 0 Then
                group.EmoticonPackageIconUrl = strThumbUrl
                group.EmoticonPackagePannelUrl = strThumbUrl
            End If

            Me.mQueueOp.Enqueue(group)
            Me.mEmoticons(group.EmoticonPackageId) = group
        End If

        Return group
    End Function

    Public Function CreateGroupIconShow(ByVal group As Emoticon) As Image
        Dim img As Image = Nothing

        Try
            Dim list As New List(Of String)

            For Each Item As EmoticonItem In group.Items
                If File.Exists(Item.PicOnPCTemp) Then
                    list.Add(Item.PicOnPCTemp)

                ElseIf File.Exists(Item.PicThumbOnPC) OrElse Me.mDevice.DownFromPhone(Me.mAfcHandler, Item.PicThumbOnPhone, Item.PicThumbOnPC) OrElse Me.mDevice.DownFromPhone(Me.mAfcHandler, Item.PicThumbOnPhoneEx, Item.PicThumbOnPC) Then
                    list.Add(Item.PicThumbOnPC)

                ElseIf File.Exists(Item.PicOnPC) OrElse Me.mDevice.DownFromPhone(Me.mAfcHandler, Item.PicOnPhone, Item.PicOnPC) OrElse Me.mDevice.DownFromPhone(Me.mAfcHandler, Item.PicOnPhoneEx, Item.PicOnPC) Then
                    list.Add(Item.PicOnPC)

                End If

                If list.Count = 4 Then
                    Exit For
                End If
            Next

            'If Me.EmoticonPackageName.Contains("gxf") Then
            '    Stop
            'End If

            img = Me.DrawGroupIcon(list)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Emoticon_GreateIconImage")
        End Try

        Return img
    End Function

    Private Function DrawGroupIcon(ByVal listFiles As List(Of String)) As Image
        Dim mSize As New Size(140, 140)
        Dim bmp As New Bitmap(mSize.Width, mSize.Height)

        Dim g As Graphics = Graphics.FromImage(bmp)
        g.InterpolationMode = Drawing2D.InterpolationMode.HighQualityBicubic
        g.SmoothingMode = Drawing2D.SmoothingMode.AntiAlias
        g.Clear(Color.FromArgb(255, 255, 255, 255))

        For index As Integer = 1 To 4
            Dim img As Image = Nothing
            If listFiles IsNot Nothing AndAlso listFiles.Count > 0 AndAlso index - 1 < listFiles.Count Then
                img = Utility.GetImageFormFile(listFiles(index - 1))
            End If

            Dim diff As Integer = 10
            Dim rect As New Rectangle(diff, diff, (bmp.Width - diff * 3) / 2, (bmp.Height - diff * 3) / 2)
            If index = 2 Then
                rect.X = (bmp.Width - diff * 3) / 2 + diff * 2
                rect.Y = diff
            ElseIf index = 3 Then
                rect.X = diff
                rect.Y = (bmp.Height - diff * 3) / 2 + diff * 2
            ElseIf index = 4 Then
                rect.X = (bmp.Width - diff * 3) / 2 + diff * 2
                rect.Y = (bmp.Height - diff * 3) / 2 + diff * 2
            End If

            If img IsNot Nothing Then
                Dim intX As Integer = 0
                Dim intY As Integer = 0
                Dim intW As Integer = 0
                Dim intH As Integer = 0

                If rect.Width / rect.Height < img.Width / img.Height Then
                    intW = img.Width
                    intH = img.Width / rect.Width * rect.Height
                    intY = (img.Height - intH) \ 2
                Else
                    intH = img.Height
                    intW = img.Height / rect.Height * rect.Width
                    intX = (img.Width - intW) \ 2
                End If

                g.DrawImage(img, rect, New Rectangle(intX, intY, intW, intH), GraphicsUnit.Pixel)
                img.Dispose()
            End If

            If listFiles Is Nothing OrElse listFiles.Count = 0 Then
                g.FillRectangle(GuiHelper.GetCacheBrush(Color.FromArgb(237, 237, 237)), rect)
            End If
        Next

        Return bmp
    End Function

    Public Function AddItems(ByVal group As Emoticon, ByVal arrFiles As String(), ByVal strDefaultPath As String) As List(Of EmoticonItem)
        Dim list As New List(Of EmoticonItem)

        Try
            If group.EmotionOp = EmotionOp.Insert AndAlso String.IsNullOrEmpty(group.DefaultIconPath) Then
                If arrFiles IsNot Nothing AndAlso arrFiles.Length > 0 Then
                    group.DefaultIconPath = strDefaultPath
                End If
                Me.mQueueOp.Enqueue(group)
            ElseIf group.EmotionOp = EmotionOp.None Then
                group.EmotionOp = EmotionOp.Update
                Me.mQueueOp.Enqueue(group)
            End If


            For Each strFile As String In arrFiles
                Dim item As New EmoticonItem()

                item.EmoticonPackageId = group.EmoticonPackageId
                item.ConStrRes2 = group.EmoticonPackageId
                item.EmotionOp = EmotionOp.Insert
                item.MD5 = Common.GetMd5HexFromFile(strFile)
                item.Type = IIf(strFile.EndsWith(".gif"), 2, 1)
                item.PicOnPCTemp = strFile

                Try
                    Dim imgs As Image = Utility.GetImageFormFile(strFile)
                    If imgs.RawFormat.Equals(System.Drawing.Imaging.ImageFormat.Gif) Then
                        item.Type = 2
                    Else
                        item.Type = 1
                    End If

                Catch ex As Exception
                End Try

                Dim img As Image = Utility.GetImageFormFile(strFile)
                If img Is Nothing Then
                    Continue For
                End If

                item.Icon = Utility.GetThumbnail(img, Me.mGroupIconSize)
                If item.Icon IsNot Nothing Then
                    item.Icon.Save(item.PicThumbOnPC, System.Drawing.Imaging.ImageFormat.Png)
                End If

                img.Dispose()

                list.Add(item)
                group.Items.Add(item)
                Me.mQueueOp.Enqueue(item)
            Next

            group.Icon = Me.CreateGroupIconShow(group)

        Catch ex As Exception
            Common.LogException(ex.ToString, "WeixinEmoticonHepler.AddItems")
        End Try

        Return list
    End Function

    Private Sub CreateGroupIcon(ByVal group As Emoticon)
        Try
            If Not File.Exists(group.DefaultIconPath) Then
                Return
            End If
            Dim img As Image = Nothing
            If group.Items.Count > 0 Then
                Dim tmpImage As Image = Nothing
                Dim item As EmoticonItem = group.Items(group.Items.Count - 1)

                'If File.Exists(item.PicOnPCTemp) Then
                '    tmpImage = Utility.GetImageFormFile(item.PicOnPCTemp)
                'ElseIf File.Exists(item.PicOnPC) OrElse Me.mDevice.DownFromPhone(item.PicThumbOnPhone, item.PicOnPC) Then
                '    tmpImage = Utility.GetImageFormFile(item.PicOnPC)
                'End If
                If File.Exists(group.DefaultIconPath) Then
                    tmpImage = Utility.GetImageFormFile(group.DefaultIconPath)
                End If

                If tmpImage IsNot Nothing Then
                    img = Utility.GetThumbnail(tmpImage, New Size(50, 50))
                    tmpImage.Dispose()
                End If
            End If

            If img Is Nothing Then
                img = My.Resources.icon_emotion
            End If

            img.Save(group.IconPathForWeiXin, Imaging.ImageFormat.Png)
            Me.mDevice.CopyToPhone(Me.mAfcHandler, group.IconPathForWeiXin, group.EmoticonPackageThumbPath)

        Catch ex As Exception
            Common.LogException(ex.ToString, "WeixinEmoticonHepler.CreateGroupIcon")
        End Try
    End Sub

    Public Sub UpdateGroups(ByVal group As Emoticon)
        If group.EmotionOp <> EmotionOp.Insert Then
            group.EmotionOp = EmotionOp.Update
        End If
        Me.mQueueOp.Enqueue(group)
    End Sub

    Public Sub DeleteGroups(ByVal listGroups As List(Of Emoticon))
        For Each group As Emoticon In listGroups
            group.EmotionOp = EmotionOp.Delete
            Me.mQueueOp.Enqueue(group)

            For Each item As EmoticonItem In group.Items
                item.EmotionOp = EmotionOp.Delete
                Me.mQueueOp.Enqueue(item)
            Next
        Next
    End Sub

    Public Sub DeleteItems(ByVal group As Emoticon, ByVal listItems As List(Of EmoticonItem))
        Try
            For Each item As EmoticonItem In listItems
                item.EmotionOp = EmotionOp.Delete
                Me.mQueueOp.Enqueue(item)

                If group.Items.Contains(item) Then
                    group.Items.Remove(item)
                End If
            Next

            group.Icon = Me.CreateGroupIconShow(group)

            If group.EmotionOp = EmotionOp.None Then
                group.EmotionOp = EmotionOp.Update
                Me.mQueueOp.Enqueue(group)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmoticonHepler.DeleteItems")
        End Try
    End Sub


    Private Sub WriteLength(ByVal ms As System.IO.Stream, ByVal len As Long)
        While True
            Dim bytValue As Byte = len And &H7F
            len = (len >> 7)

            If len = 0 Then
                ms.WriteByte(bytValue)
                Exit While
            Else
                ms.WriteByte(bytValue + &H80)
            End If
        End While
    End Sub

    Private Sub WriteItemStr(ByVal ms As System.IO.Stream, ByVal bytItemFlag As Byte, ByVal strData As String)
        WriteItemArr(ms, bytItemFlag, Encoding.UTF8.GetBytes(strData))
    End Sub

    Private Sub WriteItemArr(ByVal ms As System.IO.Stream, ByVal bytItemFlag As Byte, ByVal arrData() As Byte)
        ms.WriteByte(bytItemFlag)
        WriteLength(ms, arrData.Length)
        ms.Write(arrData, 0, arrData.Length)
    End Sub

    Private Sub WriteItemBln(ByVal ms As System.IO.Stream, ByVal bytItemFlag As Byte, ByVal blnValue As Boolean)
        WriteItemLng(ms, bytItemFlag, Math.Abs(CInt(blnValue)))
    End Sub

    Private Sub WriteItemInt(ByVal ms As System.IO.Stream, ByVal bytItemFlag As Byte, ByVal intValue As Integer)
        WriteItemLng(ms, bytItemFlag, intValue)
    End Sub

    Private Sub WriteItemLng(ByVal ms As System.IO.Stream, ByVal bytItemFlag As Byte, ByVal lngValue As Long)
        ms.WriteByte(bytItemFlag)
        WriteLength(ms, lngValue)
    End Sub

    Private Sub WriteData(ByVal ms As System.IO.Stream, ByVal strData As String)
        WriteData(ms, Encoding.UTF8.GetBytes(strData))
    End Sub

    Private Sub WriteData(ByVal ms As System.IO.Stream, ByVal arrData() As Byte)
        WriteLength(ms, arrData.Length)
        ms.Write(arrData, 0, arrData.Length)
    End Sub

    Private Sub UpdateStoreEmotionProductIdListDat()
        Dim strPathOnPhone As String = String.Format("/Library/WechatPrivate/{0}/emoticon_package/EmoticonBackup/Data/StoreEmotionProductIdList.dat", mMD5Myself)
        Dim strPathOnPC As String = IIf(Common.IsVshostMode(), Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory), "StoreEmotionProductIdList.bin"), Folder.GetTempFilePath())

        Dim list As New List(Of Emoticon)
        For Each group As Emoticon In Me.mEmoticons.Values
            If group.EmotionOp = EmotionOp.Delete OrElse group.EmoticonPackageId = "custom_emoticon_pid" Then
                Continue For
            End If

            list.Add(group)
        Next
        list.Sort(New EmoticonComparer())

        Dim ms As New System.IO.MemoryStream()
        For Each group As Emoticon In list
            WriteData(ms, group.EmoticonPackageId)
        Next

        Using fs As New FileStream(strPathOnPC, FileMode.Create)
            WriteItemArr(fs, &HA, ms.ToArray())
        End Using

        Me.mDevice.CopyToPhone(Me.mAfcHandler, strPathOnPC, strPathOnPhone)
    End Sub

    Private Sub UpdateEmoticonDesc(ByVal group As Emoticon)
        Dim pkgID_MD5 As String = Common.ToHexString(Common.GetMd5Array(group.EmoticonPackageId))
        Dim strPathOnPhone As String = String.Format("/Library/WechatPrivate/{0}/emoticon_package/emoticon_desc/{1}", mMD5Myself, pkgID_MD5)
        Dim strPathOnPC As String = IIf(Common.IsVshostMode(), Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.DesktopDirectory), "emoticon_desc.bin"), Folder.GetTempFilePath())

        Dim dictLang As New Dictionary(Of String, String)
        dictLang.Add("default", "")
        dictLang.Add("zh_cn", "")
        dictLang.Add("zh_hk", "")
        dictLang.Add("zh_tw", "")

        Dim msItemList As New System.IO.MemoryStream()
        For Each item As EmoticonItem In group.Items

            Dim msLang As New MemoryStream()
            For Each pair As KeyValuePair(Of String, String) In dictLang
                Dim msLangItem As New MemoryStream()
                WriteItemArr(msLangItem, &HA, Encoding.UTF8.GetBytes(pair.Key))
                WriteItemArr(msLangItem, &H12, Encoding.UTF8.GetBytes(pair.Value))

                WriteData(msLang, msLangItem.ToArray())
                msLangItem.Close()
            Next

            Dim msItem As New MemoryStream()
            WriteItemStr(msItem, &HA, item.MD5)
            WriteItemArr(msItem, &H12, msLang.ToArray())
            msLang.Close()

            WriteData(msItemList, msItem.ToArray())
        Next

        'Debug.Print(WechatHelper.ConvertWeixinToPcTime(1421550238))

        Using fs As New FileStream(strPathOnPC, FileMode.Create)
            WriteItemArr(fs, &HA, msItemList.ToArray())
            WriteItemStr(fs, &H12, group.EmoticonPackageId)
            WriteItemLng(fs, &H18, WechatHelper.ConvertPcTimeToWeixin(Now))
            WriteItemBln(fs, &H20, True)

            msItemList.Close()
        End Using


        Me.mDevice.CopyToPhone(Me.mAfcHandler, strPathOnPC, strPathOnPhone)
    End Sub

    Public Sub SyncData()
        Try
            If mThreadSync IsNot Nothing AndAlso mThreadSync.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            mThreadSync = New Thread(AddressOf SyncDataThread)
            mThreadSync.IsBackground = True
            mThreadSync.Start()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeixinEmoticonHepler.SyncData")
        End Try
    End Sub

    Private Sub SyncDataThread()
        Dim args As New WeixinEmoticonArgs()
        Dim dict As New Dictionary(Of String, Emoticon)

        args.Device = Me.mDevice
REDO:
        Me.mSQLBuilder.Length = 0
        Try
            args.Current = 0
            args.Total = Me.mQueueOp.Count + 3
            args.Status = EmoticonStatus.Process
            RaiseEvent OnSync(Me, args)

            Me.mAfcHandler = Me.mDevice.GetAfcByFileSharing(Me.mstrSKU, HouseArrestType.Document, True)
            If mAfcHandler = IntPtr.Zero Then
                GoTo DO_EXIT
            End If


            While Me.mQueueOp.Count > 0
                If args.Current >= args.Total - 3 Then
                    args.Total = Me.mQueueOp.Count + 3
                    args.Current = 0
                End If

                Dim objItem As Object = Me.mQueueOp.Dequeue()

                args.Current += 1
                RaiseEvent OnSync(Me, args)

                If TypeOf objItem Is Emoticon Then
                    Dim group As Emoticon = objItem

                    Select Case group.EmotionOp
                        Case EmotionOp.Insert
                            Me.mSQLBuilder.AppendLine(Me.mWechatMMDB.GetSQL_InsertEmoticon(group))
                            Me.CreateGroupIcon(group)
                            args.LstEmoticon.Add(group)
                            'Debug.Print("ASDF:" & args.LstEmoticon.Count & group.EmoticonPackageName)
                            dict(group.EmoticonPackageId) = group

                        Case EmotionOp.Update
                            Me.CreateGroupIcon(group)
                            dict(group.EmoticonPackageId) = group
                            If mInsertToFavorite Then
                                args.LstEmoticon.Add(group)
                            Else
                                Me.mSQLBuilder.AppendLine(Me.mWechatMMDB.GetSQL_UpdateEmoticon(group))
                            End If

                        Case EmotionOp.Delete
                            Me.mSQLBuilder.AppendLine(Me.mWechatMMDB.GetSQL_DeleteEmoticon(group))
                            Me.mDevice.DeleteFile(Me.mAfcHandler, group.EmoticonPackageThumbPath)

                    End Select

                ElseIf TypeOf objItem Is EmoticonItem Then
                    Dim item As EmoticonItem = objItem
                    Select Case item.EmotionOp
                        Case EmotionOp.Insert
                            Me.mSQLBuilder.AppendLine(Me.mWechatMMDB.GetSQL_InsertEmoticonItem(item, args.Current))
                            If Me.mDevice.Exists(Me.mAfcHandler, "/Library/WechatPrivate/emoticon1") Then
                                Me.mDevice.CopyToPhone(Me.mAfcHandler, item.PicOnPCTemp, item.PicOnPhone)
                                Me.mDevice.CopyToPhone(Me.mAfcHandler, item.PicThumbOnPC, item.PicThumbOnPhone)
                            Else
                                Me.mDevice.CopyToPhone(Me.mAfcHandler, item.PicOnPCTemp, item.PicOnPhoneEx)
                                Me.mDevice.CopyToPhone(Me.mAfcHandler, item.PicThumbOnPC, item.PicThumbOnPhoneEx)
                            End If

                        Case EmotionOp.Delete
                            Me.mSQLBuilder.AppendLine(Me.mWechatMMDB.GetSQL_DeleteEmoticonItem(item))
                            '如果用户导入两个相同的包就不删除文件 。
                            If Me.mWechatMMDB.Check_EmoticonDetail_Info(item) Then
                                If Me.mDevice.Exists(Me.mAfcHandler, "/Library/WechatPrivate/emoticon1") Then
                                    Me.mDevice.DeleteFile(Me.mAfcHandler, item.PicOnPhone)
                                    Me.mDevice.DeleteFile(Me.mAfcHandler, item.PicThumbOnPhone)
                                Else
                                    Me.mDevice.DeleteFile(Me.mAfcHandler, item.PicOnPhoneEx)
                                    Me.mDevice.DeleteFile(Me.mAfcHandler, item.PicThumbOnPhoneEx)
                                End If
                            End If

                    End Select
                End If
            End While

            args.Current += 1
            RaiseEvent OnSync(Me, args)


            Dim tryCount As Integer = 0
DO_UPDATE:
            Debug.Print(Me.mSQLBuilder.ToString())
            Dim errMsg As String = Me.mWechatMMDB.Update_EmoticonDB(Me.mSQLBuilder.ToString())
            If Not String.IsNullOrEmpty(errMsg) Then
                Common.LogException(errMsg & vbCrLf & Me.mSQLBuilder.ToString(), "WeixinEmoticonHepler.SyncDataThread")
                GoTo DO_EXIT
            End If


            '比较数据库是发生变化
            Dim info As iPhoneFileInfo = Nothing
            If Me.mDevice.IsDirectory(Me.mAfcHandler, Me.mPathWechatOnPhone_MMDB, info) OrElse info Is Nothing Then
                Common.LogException("无法获取到微信数据", "WeixinEmoticonHepler.SyncDataThread")
                GoTo DO_EXIT

            ElseIf info.st_size <> Me.mDBInfo.st_size OrElse info.st_mtime <> Me.mDBInfo.st_mtime Then
                tryCount += 1
                If tryCount > 5 Then
                    Common.LogException("更新数据不成功文件没有变化", "WeixinEmoticonHepler.SyncDataThread")
                    GoTo DO_EXIT
                End If

                Me.mDBInfo = info
                IniSetting.SetMMDate(Me.GetMMDBKey(Me.mDBInfo))

                Me.mWechatMMDB.CloseDB()
                If Not Me.mDevice.DownFromPhone(Me.mAfcHandler, Me.mPathWechatOnPhone_MMDB, Me.mPathWechatOnPC_MMDB) Then
                    Common.LogException("下载微信数据库失败", "WeixinEmoticonHepler.SyncDataThread")
                    GoTo DO_EXIT
                End If

                If Me.mWechatMMDB.Reload() Then
                    GoTo DO_UPDATE
                Else
                    Common.LogException("重新加载数据库失败", "WeixinEmoticonHepler.SyncDataThread")
                    GoTo DO_EXIT
                End If
            End If

            args.Current = args.Total
            RaiseEvent OnSync(Me, args)

            Dim isSucceed As Boolean = False
            Dim iTryUpload As Integer = 0

            '强制重新上传几次，以便保证成功
            While Not isSucceed AndAlso iTryUpload < 3
                Try
                    Me.mWechatMMDB.CloseDB()
                    isSucceed = Me.mDevice.CopyToPhone(Me.mAfcHandler, Me.mPathWechatOnPC_MMDB, Me.mPathWechatOnPhone_MMDB)

                Catch ex As Exception
                    Common.LogException(ex.ToString(), "WeixinEmoticonHelper.SyncDataThread")
                End Try

                If Not isSucceed AndAlso iTryUpload < 3 Then
                    iTryUpload += 1
                    Utility.WaitSeconds(0.5)
                End If
            End While

            'Me.UpdateStoreEmotionProductIdListDat()
            'For Each group As Emoticon In dict.Values
            '    Me.UpdateEmoticonDesc(group)
            'Next

            Me.mWechatMMDB.Reload()
            args.Current = args.Total

        Catch ex As Exception
            args.Status = EmoticonStatus.Failure
            Common.LogException(ex.ToString(), "WeixinEmoticonHelper.SyncDataThread")
        End Try

DO_EXIT:

        Try
            If args.Current = args.Total Then
                Me.mSQLBuilder.Length = 0
                'Me.mQueueOp.Clear()

                args.Status = EmoticonStatus.Succeed
            Else
                args.Status = EmoticonStatus.Failure
            End If
            RaiseEvent OnSync(Me, args)
        Catch ex As Exception
        End Try

        If Me.mQueueOp.Count > 0 Then
            GoTo REDO
        End If

    End Sub

#Region "----IDisposable Support----"

    Private disposedValue As Boolean = False        ' 检测冗余的调用
    ' IDisposable
    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not Me.disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
            End If

            ' TODO: 释放共享的非托管资源
        End If
        Me.disposedValue = True
    End Sub

    ' Visual Basic 添加此代码是为了正确实现可处置模式。
    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。
        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub
#End Region

End Class

Public Class WeixinEmoticonLoadArgs
    Inherits EventArgs
    Public Status As WeixinState = WeixinState.Loading
    Public Is622 As Boolean = False
End Class

Public Class WeixinEmoticonArgs
    Inherits EventArgs

    Public Status As EmoticonStatus = EmoticonStatus.None
    Public Type As WeixinEmoticonLoadType = WeixinEmoticonLoadType.EmoticonPack
    Friend Total As Integer = 0
    Friend Current As Integer = 0

    Public ReadOnly Property Progress() As Integer
        Get
            If Total > 0 Then
                Return Current * 100 / Total
            Else
                Return 100
            End If
        End Get
    End Property
    Public Device As iPhoneDevice = Nothing
    Public LstEmoticon As New List(Of Emoticon)
    Public LstEmoticonItem As New List(Of EmoticonItem)
End Class

Public Enum EmoticonStatus
    None
    Process
    Succeed
    Failure
End Enum

Public Enum WeixinEmoticonLoadType
    EmoticonPack
    EmoticonPic
End Enum