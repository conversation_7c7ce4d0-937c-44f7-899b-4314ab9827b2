﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmFindWeChatData
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmFindWeChatData))
        Me.lblHint = New iTong.Components.tbLabel()
        Me.btn_close = New iTong.Components.tbButton()
        Me.btnOK = New iTong.Components.tbButton()
        Me.lblDoing = New iTong.Components.tbLabel()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'lblHint
        '
        Me.lblHint.AutoSize = True
        Me.lblHint.BackColor = System.Drawing.Color.Transparent
        Me.lblHint.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblHint.Location = New System.Drawing.Point(48, 61)
        Me.lblHint.Name = "lblHint"
        Me.lblHint.Size = New System.Drawing.Size(59, 16)
        Me.lblHint.TabIndex = 2
        Me.lblHint.tbAdriftWhenHover = False
        Me.lblHint.tbAutoEllipsis = False
        Me.lblHint.tbAutoSize = True
        Me.lblHint.tbHideImage = False
        Me.lblHint.tbIconImage = Nothing
        Me.lblHint.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblHint.tbIconPlaceText = 5
        Me.lblHint.tbShadow = False
        Me.lblHint.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblHint.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblHint.tbShowScrolling = False
        Me.lblHint.Text = "提示："
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(550, 2)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 24
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnOK.Location = New System.Drawing.Point(399, 211)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(128, 38)
        Me.btnOK.TabIndex = 26
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "开始找回"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'lblDoing
        '
        Me.lblDoing.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblDoing.BackColor = System.Drawing.Color.Transparent
        Me.lblDoing.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDoing.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDoing.Location = New System.Drawing.Point(86, 220)
        Me.lblDoing.Name = "lblDoing"
        Me.lblDoing.Size = New System.Drawing.Size(289, 21)
        Me.lblDoing.TabIndex = 27
        Me.lblDoing.tbAdriftWhenHover = False
        Me.lblDoing.tbAutoEllipsis = False
        Me.lblDoing.tbAutoSize = False
        Me.lblDoing.tbHideImage = False
        Me.lblDoing.tbIconImage = Nothing
        Me.lblDoing.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDoing.tbIconPlaceText = 5
        Me.lblDoing.tbShadow = False
        Me.lblDoing.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDoing.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDoing.tbShowScrolling = False
        Me.lblDoing.Text = "数据找回中，请耐心等候 ..."
        Me.lblDoing.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picLoading
        '
        Me.picLoading.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.picLoading.Location = New System.Drawing.Point(51, 219)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(24, 24)
        Me.picLoading.TabIndex = 28
        Me.picLoading.TabStop = False
        '
        'RichTextBox1
        '
        Me.RichTextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.RichTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1.Cursor = System.Windows.Forms.Cursors.Arrow
        Me.RichTextBox1.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.RichTextBox1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.RichTextBox1.Location = New System.Drawing.Point(51, 89)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.ReadOnly = True
        Me.RichTextBox1.Size = New System.Drawing.Size(478, 107)
        Me.RichTextBox1.TabIndex = 29
        Me.RichTextBox1.Text = ""
        '
        'frmFindWeChatData
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(576, 279)
        Me.Controls.Add(Me.RichTextBox1)
        Me.Controls.Add(Me.picLoading)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.lblHint)
        Me.Controls.Add(Me.lblDoing)
        Me.Name = "frmFindWeChatData"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,8"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "找回数据"
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents lblHint As iTong.Components.tbLabel
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents lblDoing As iTong.Components.tbLabel
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents RichTextBox1 As System.Windows.Forms.RichTextBox
End Class
