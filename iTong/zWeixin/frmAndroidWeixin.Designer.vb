﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmAndroidWeixin
    Inherits frmAndroidBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    '<System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmAndroidWeixin))
        Me.tpnlContent = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlTop = New iTong.Components.tbPanel()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.btnRestore = New iTong.Components.tbButton()
        Me.btnBackup = New iTong.Components.tbButton()
        Me.cmbTime = New System.Windows.Forms.ComboBox()
        Me.cmbFriend = New System.Windows.Forms.ComboBox()
        Me.lblSearchRange = New System.Windows.Forms.Label()
        Me.txtSearchChat = New iTong.Components.tbSearch()
        Me.btnFavourite = New iTong.Components.tbButton()
        Me.btnMoment = New iTong.Components.tbButton()
        Me.btnRepair = New iTong.Components.tbButton()
        Me.btnExport = New iTong.Components.tbButton()
        Me.pnlSearchFriend = New iTong.Components.tbPanel()
        Me.txtSearchFriend = New iTong.Components.tbSearch()
        Me.pnlState = New iTong.Components.tbPanel()
        Me.btnOK = New iTong.Components.tbButton()
        Me.lblEndTime = New System.Windows.Forms.Label()
        Me.dtpEnd = New System.Windows.Forms.DateTimePicker()
        Me.lblStartTime = New System.Windows.Forms.Label()
        Me.btnCopy = New iTong.Components.tbButton()
        Me.dtpStart = New System.Windows.Forms.DateTimePicker()
        Me.pnlTitle = New iTong.Components.tbPanel()
        Me.lblTitle = New System.Windows.Forms.Label()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.lblSystemInfo = New iTong.Components.tbLabel()
        Me.wbsChat = New iTong.Components.tbWebBrowserEx()
        Me.pnlFriend = New iTong.Components.tbPanel()
        Me.pnlLoading = New iTong.Components.tbPanel()
        Me.picNoLockScreen = New System.Windows.Forms.PictureBox()
        Me.lblLoadingDescription = New System.Windows.Forms.Label()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.picLoading = New System.Windows.Forms.PictureBox()
        Me.pnlDescription = New iTong.Components.tbPanel()
        Me.lblNoRootMedthod = New System.Windows.Forms.Label()
        Me.pnlMethod2 = New iTong.Components.tbPanel()
        Me.lblDescRootAgain = New System.Windows.Forms.Label()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.lblWechatTxtManage = New System.Windows.Forms.Label()
        Me.btnStepRoot = New iTong.Components.tbButton()
        Me.pnlMethod1 = New iTong.Components.tbPanel()
        Me.btnManage = New iTong.Components.tbButton()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.lblWechatMediaManage = New System.Windows.Forms.Label()
        Me.lblDescCacelLimit = New System.Windows.Forms.Label()
        Me.lblAbout = New System.Windows.Forms.Label()
        Me.lblTryRoot = New System.Windows.Forms.Label()
        Me.pnlWechatAssiant = New iTong.Components.tbPanel()
        Me.btnOpenWechatAssisant = New iTong.Components.tbButton()
        Me.PictureBox3 = New System.Windows.Forms.PictureBox()
        Me.lblWechatAssistant = New System.Windows.Forms.Label()
        Me.lblDescWechatAssistant = New System.Windows.Forms.Label()
        Me.lblHowtoRoot = New System.Windows.Forms.Label()
        Me.lblReCheckEx = New System.Windows.Forms.Label()
        Me.lblCacelLimit = New System.Windows.Forms.Label()
        Me.btnShowRootCourse = New iTong.Components.tbButton()
        Me.picError = New System.Windows.Forms.PictureBox()
        Me.lblWeixinTitle = New System.Windows.Forms.Label()
        Me.btnDownloadWeixin = New iTong.Components.tbButton()
        Me.lblDownloadWeixin = New System.Windows.Forms.Label()
        Me.lblWeixinDescription_1 = New System.Windows.Forms.Label()
        Me.lblShowRootCourse = New System.Windows.Forms.Label()
        Me.picWeixinIcon = New System.Windows.Forms.PictureBox()
        Me.lblWeixinDescription = New System.Windows.Forms.Label()
        Me.pnlNoneContents = New iTong.Components.tbPanel()
        Me.lblNoneWeixin = New System.Windows.Forms.Label()
        Me.btnReCheckNone = New iTong.Components.tbButton()
        Me.picNoneIcon = New System.Windows.Forms.PictureBox()
        Me.pnlWelcome = New iTong.Components.tbPanel()
        Me.btnNext = New iTong.Components.tbButton()
        Me.lblWelcome = New System.Windows.Forms.Label()
        Me.lblWelcome2 = New System.Windows.Forms.Label()
        Me.lblWelcome1 = New System.Windows.Forms.Label()
        Me.picWelcome = New iTong.Components.tbPictureBox()
        Me.lblState = New iTong.Components.tbLabel()
        Me.cmsExport = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiTxt = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTxtCurrentFriend = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTxtCurrentFriendTime = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiTxtAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExcel = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExcelCurrentFriend = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExcelCurrentFriendTime = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiExcelAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiPic = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiPicCurrentFriend = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiPicAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAudio = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAudioCurrentFriend = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAudioAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiVideo = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiVideoCurrentFriend = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiVideoAll = New System.Windows.Forms.ToolStripMenuItem()
        Me.cmsRestore = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.lblCancelPwdBackup = New iTong.Components.tbLabel()
        Me.cmsRepair = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.tssRepair = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiSave = New System.Windows.Forms.ToolStripMenuItem()
        Me.pnlLoading_1 = New iTong.Components.tbPanel()
        Me.lblLoading_1 = New System.Windows.Forms.Label()
        Me.picLoading_1 = New System.Windows.Forms.PictureBox()
        Me.tpnlContent.SuspendLayout()
        Me.pnlTop.SuspendLayout()
        Me.pnlSearchFriend.SuspendLayout()
        Me.pnlState.SuspendLayout()
        Me.pnlTitle.SuspendLayout()
        Me.pnlContainer.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.picNoLockScreen, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDescription.SuspendLayout()
        Me.pnlMethod2.SuspendLayout()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlMethod1.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlWechatAssiant.SuspendLayout()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picWeixinIcon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlNoneContents.SuspendLayout()
        CType(Me.picNoneIcon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlWelcome.SuspendLayout()
        CType(Me.picWelcome, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.cmsExport.SuspendLayout()
        Me.cmsRepair.SuspendLayout()
        Me.pnlLoading_1.SuspendLayout()
        CType(Me.picLoading_1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(846, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(822, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(798, 0)
        '
        'tpnlContent
        '
        Me.tpnlContent.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tpnlContent.BackColor = System.Drawing.Color.FromArgb(CType(CType(206, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(215, Byte), Integer))
        Me.tpnlContent.ColumnCount = 2
        Me.tpnlContent.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 268.0!))
        Me.tpnlContent.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlContent.Controls.Add(Me.pnlTop, 0, 0)
        Me.tpnlContent.Controls.Add(Me.pnlSearchFriend, 0, 1)
        Me.tpnlContent.Controls.Add(Me.pnlState, 0, 3)
        Me.tpnlContent.Controls.Add(Me.pnlTitle, 1, 1)
        Me.tpnlContent.Controls.Add(Me.pnlContainer, 1, 2)
        Me.tpnlContent.Controls.Add(Me.pnlFriend, 0, 2)
        Me.tpnlContent.Location = New System.Drawing.Point(1, 32)
        Me.tpnlContent.Margin = New System.Windows.Forms.Padding(0)
        Me.tpnlContent.Name = "tpnlContent"
        Me.tpnlContent.RowCount = 4
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32.0!))
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32.0!))
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 32.0!))
        Me.tpnlContent.Size = New System.Drawing.Size(868, 571)
        Me.tpnlContent.TabIndex = 0
        '
        'pnlTop
        '
        Me.tpnlContent.SetColumnSpan(Me.pnlTop, 2)
        Me.pnlTop.Controls.Add(Me.btnRefresh)
        Me.pnlTop.Controls.Add(Me.btnRestore)
        Me.pnlTop.Controls.Add(Me.btnBackup)
        Me.pnlTop.Controls.Add(Me.cmbTime)
        Me.pnlTop.Controls.Add(Me.cmbFriend)
        Me.pnlTop.Controls.Add(Me.lblSearchRange)
        Me.pnlTop.Controls.Add(Me.txtSearchChat)
        Me.pnlTop.Controls.Add(Me.btnFavourite)
        Me.pnlTop.Controls.Add(Me.btnMoment)
        Me.pnlTop.Controls.Add(Me.btnRepair)
        Me.pnlTop.Controls.Add(Me.btnExport)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTop.Location = New System.Drawing.Point(0, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(868, 32)
        Me.pnlTop.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTop.TabIndex = 4
        Me.pnlTop.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlTop.tbShowWatermark = False
        Me.pnlTop.tbSplit = "3,3,3,3"
        Me.pnlTop.tbWatermark = Nothing
        Me.pnlTop.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTop.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(290, 5)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(57, 23)
        Me.btnRefresh.TabIndex = 29
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = "刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 0
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'btnRestore
        '
        Me.btnRestore.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnRestore.BackColor = System.Drawing.Color.Transparent
        Me.btnRestore.BindingForm = Nothing
        Me.btnRestore.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRestore.Location = New System.Drawing.Point(161, 5)
        Me.btnRestore.Margin = New System.Windows.Forms.Padding(0)
        Me.btnRestore.Name = "btnRestore"
        Me.btnRestore.Padding = New System.Windows.Forms.Padding(0, 2, 0, 2)
        Me.btnRestore.Selectable = True
        Me.btnRestore.Size = New System.Drawing.Size(0, 0)
        Me.btnRestore.TabIndex = 18
        Me.btnRestore.tbAdriftIconWhenHover = False
        Me.btnRestore.tbAutoSize = False
        Me.btnRestore.tbAutoSizeEx = True
        Me.btnRestore.tbBackgroundImage = Nothing
        Me.btnRestore.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRestore.tbBadgeNumber = 0
        Me.btnRestore.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRestore.tbEndEllipsis = False
        Me.btnRestore.tbIconHoldPlace = True
        Me.btnRestore.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_restore
        Me.btnRestore.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRestore.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRestore.tbIconMore = False
        Me.btnRestore.tbIconMouseDown = Nothing
        Me.btnRestore.tbIconMouseHover = Nothing
        Me.btnRestore.tbIconMouseLeave = Nothing
        Me.btnRestore.tbIconPlaceText = 2
        Me.btnRestore.tbIconReadOnly = Nothing
        Me.btnRestore.tbImageMouseDown = Nothing
        Me.btnRestore.tbImageMouseHover = Nothing
        Me.btnRestore.tbImageMouseLeave = Nothing
        Me.btnRestore.tbProgressValue = 50
        Me.btnRestore.tbReadOnly = False
        Me.btnRestore.tbReadOnlyText = False
        Me.btnRestore.tbShadow = False
        Me.btnRestore.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRestore.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRestore.tbShowDot = False
        Me.btnRestore.tbShowMoreIconImg = CType(resources.GetObject("btnRestore.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRestore.tbShowNew = False
        Me.btnRestore.tbShowProgress = False
        Me.btnRestore.tbShowTip = True
        Me.btnRestore.tbShowToolTipOnButton = False
        Me.btnRestore.tbSplit = "13,11,13,11"
        Me.btnRestore.tbText = "还原"
        Me.btnRestore.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestore.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnRestore.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRestore.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRestore.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRestore.tbTextMouseDownPlace = 2
        Me.btnRestore.tbToolTip = ""
        Me.btnRestore.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRestore.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRestore.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRestore.Visible = False
        Me.btnRestore.VisibleEx = False
        '
        'btnBackup
        '
        Me.btnBackup.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnBackup.BackColor = System.Drawing.Color.Transparent
        Me.btnBackup.BindingForm = Nothing
        Me.btnBackup.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnBackup.Location = New System.Drawing.Point(93, 5)
        Me.btnBackup.Margin = New System.Windows.Forms.Padding(0)
        Me.btnBackup.Name = "btnBackup"
        Me.btnBackup.Padding = New System.Windows.Forms.Padding(0, 2, 0, 2)
        Me.btnBackup.Selectable = True
        Me.btnBackup.Size = New System.Drawing.Size(0, 0)
        Me.btnBackup.TabIndex = 17
        Me.btnBackup.tbAdriftIconWhenHover = False
        Me.btnBackup.tbAutoSize = False
        Me.btnBackup.tbAutoSizeEx = True
        Me.btnBackup.tbBackgroundImage = Nothing
        Me.btnBackup.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBackup.tbBadgeNumber = 0
        Me.btnBackup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackup.tbEndEllipsis = False
        Me.btnBackup.tbIconHoldPlace = True
        Me.btnBackup.tbIconImage = Global.iTong.My.Resources.Resources.btn_backup_4
        Me.btnBackup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackup.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnBackup.tbIconMore = False
        Me.btnBackup.tbIconMouseDown = Nothing
        Me.btnBackup.tbIconMouseHover = Nothing
        Me.btnBackup.tbIconMouseLeave = Nothing
        Me.btnBackup.tbIconPlaceText = 2
        Me.btnBackup.tbIconReadOnly = Nothing
        Me.btnBackup.tbImageMouseDown = Nothing
        Me.btnBackup.tbImageMouseHover = Nothing
        Me.btnBackup.tbImageMouseLeave = Nothing
        Me.btnBackup.tbProgressValue = 50
        Me.btnBackup.tbReadOnly = False
        Me.btnBackup.tbReadOnlyText = False
        Me.btnBackup.tbShadow = False
        Me.btnBackup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackup.tbShowDot = False
        Me.btnBackup.tbShowMoreIconImg = CType(resources.GetObject("btnBackup.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBackup.tbShowNew = False
        Me.btnBackup.tbShowProgress = False
        Me.btnBackup.tbShowTip = True
        Me.btnBackup.tbShowToolTipOnButton = False
        Me.btnBackup.tbSplit = "13,11,13,11"
        Me.btnBackup.tbText = "备份"
        Me.btnBackup.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnBackup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnBackup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnBackup.tbTextMouseDownPlace = 2
        Me.btnBackup.tbToolTip = ""
        Me.btnBackup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBackup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackup.Visible = False
        Me.btnBackup.VisibleEx = False
        '
        'cmbTime
        '
        Me.cmbTime.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbTime.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbTime.FormattingEnabled = True
        Me.cmbTime.Items.AddRange(New Object() {"最近一周", "最近一个月", "最近三个月", "最近一年", "全部"})
        Me.cmbTime.Location = New System.Drawing.Point(574, 6)
        Me.cmbTime.Name = "cmbTime"
        Me.cmbTime.Size = New System.Drawing.Size(100, 20)
        Me.cmbTime.TabIndex = 16
        '
        'cmbFriend
        '
        Me.cmbFriend.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cmbFriend.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cmbFriend.FormattingEnabled = True
        Me.cmbFriend.Items.AddRange(New Object() {"当前联系人", "全部"})
        Me.cmbFriend.Location = New System.Drawing.Point(463, 6)
        Me.cmbFriend.Name = "cmbFriend"
        Me.cmbFriend.Size = New System.Drawing.Size(100, 20)
        Me.cmbFriend.TabIndex = 16
        '
        'lblSearchRange
        '
        Me.lblSearchRange.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblSearchRange.AutoSize = True
        Me.lblSearchRange.BackColor = System.Drawing.Color.Transparent
        Me.lblSearchRange.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblSearchRange.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblSearchRange.Location = New System.Drawing.Point(395, 10)
        Me.lblSearchRange.Name = "lblSearchRange"
        Me.lblSearchRange.Size = New System.Drawing.Size(65, 12)
        Me.lblSearchRange.TabIndex = 14
        Me.lblSearchRange.Text = "查找范围："
        Me.lblSearchRange.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'txtSearchChat
        '
        Me.txtSearchChat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtSearchChat.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchChat.Location = New System.Drawing.Point(689, 5)
        Me.txtSearchChat.MaxLength = 32767
        Me.txtSearchChat.Name = "txtSearchChat"
        Me.txtSearchChat.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchChat.ReadOnly = False
        Me.txtSearchChat.SearchText = ""
        Me.txtSearchChat.SearchTipText = "Search"
        Me.txtSearchChat.ShowClear = True
        Me.txtSearchChat.ShowClearAlways = False
        Me.txtSearchChat.ShowMore = False
        Me.txtSearchChat.ShowSearch = True
        Me.txtSearchChat.Size = New System.Drawing.Size(170, 23)
        Me.txtSearchChat.TabIndex = 9
        Me.txtSearchChat.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchChat.Timer = True
        Me.txtSearchChat.TimerInterval = 0.5R
        '
        'btnFavourite
        '
        Me.btnFavourite.BackColor = System.Drawing.Color.Transparent
        Me.btnFavourite.BindingForm = Nothing
        Me.btnFavourite.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFavourite.Location = New System.Drawing.Point(226, 5)
        Me.btnFavourite.Name = "btnFavourite"
        Me.btnFavourite.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnFavourite.Selectable = True
        Me.btnFavourite.Size = New System.Drawing.Size(39, 23)
        Me.btnFavourite.TabIndex = 8
        Me.btnFavourite.tbAdriftIconWhenHover = False
        Me.btnFavourite.tbAutoSize = False
        Me.btnFavourite.tbAutoSizeEx = True
        Me.btnFavourite.tbBackgroundImage = Nothing
        Me.btnFavourite.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnFavourite.tbBadgeNumber = 0
        Me.btnFavourite.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFavourite.tbEndEllipsis = False
        Me.btnFavourite.tbIconHoldPlace = True
        Me.btnFavourite.tbIconImage = Nothing
        Me.btnFavourite.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnFavourite.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnFavourite.tbIconMore = False
        Me.btnFavourite.tbIconMouseDown = Nothing
        Me.btnFavourite.tbIconMouseHover = Nothing
        Me.btnFavourite.tbIconMouseLeave = Nothing
        Me.btnFavourite.tbIconPlaceText = 2
        Me.btnFavourite.tbIconReadOnly = Nothing
        Me.btnFavourite.tbImageMouseDown = Nothing
        Me.btnFavourite.tbImageMouseHover = Nothing
        Me.btnFavourite.tbImageMouseLeave = Nothing
        Me.btnFavourite.tbProgressValue = 50
        Me.btnFavourite.tbReadOnly = False
        Me.btnFavourite.tbReadOnlyText = False
        Me.btnFavourite.tbShadow = False
        Me.btnFavourite.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFavourite.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFavourite.tbShowDot = False
        Me.btnFavourite.tbShowMoreIconImg = CType(resources.GetObject("btnFavourite.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFavourite.tbShowNew = False
        Me.btnFavourite.tbShowProgress = False
        Me.btnFavourite.tbShowTip = True
        Me.btnFavourite.tbShowToolTipOnButton = False
        Me.btnFavourite.tbSplit = "13,11,13,11"
        Me.btnFavourite.tbText = "收藏"
        Me.btnFavourite.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFavourite.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnFavourite.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnFavourite.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnFavourite.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnFavourite.tbTextMouseDownPlace = 2
        Me.btnFavourite.tbToolTip = ""
        Me.btnFavourite.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFavourite.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFavourite.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFavourite.VisibleEx = True
        '
        'btnMoment
        '
        Me.btnMoment.BackColor = System.Drawing.Color.Transparent
        Me.btnMoment.BindingForm = Nothing
        Me.btnMoment.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnMoment.Location = New System.Drawing.Point(181, 5)
        Me.btnMoment.Name = "btnMoment"
        Me.btnMoment.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnMoment.Selectable = True
        Me.btnMoment.Size = New System.Drawing.Size(51, 23)
        Me.btnMoment.TabIndex = 8
        Me.btnMoment.tbAdriftIconWhenHover = False
        Me.btnMoment.tbAutoSize = False
        Me.btnMoment.tbAutoSizeEx = True
        Me.btnMoment.tbBackgroundImage = Nothing
        Me.btnMoment.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnMoment.tbBadgeNumber = 0
        Me.btnMoment.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnMoment.tbEndEllipsis = False
        Me.btnMoment.tbIconHoldPlace = True
        Me.btnMoment.tbIconImage = Nothing
        Me.btnMoment.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnMoment.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnMoment.tbIconMore = False
        Me.btnMoment.tbIconMouseDown = Nothing
        Me.btnMoment.tbIconMouseHover = Nothing
        Me.btnMoment.tbIconMouseLeave = Nothing
        Me.btnMoment.tbIconPlaceText = 2
        Me.btnMoment.tbIconReadOnly = Nothing
        Me.btnMoment.tbImageMouseDown = Nothing
        Me.btnMoment.tbImageMouseHover = Nothing
        Me.btnMoment.tbImageMouseLeave = Nothing
        Me.btnMoment.tbProgressValue = 50
        Me.btnMoment.tbReadOnly = False
        Me.btnMoment.tbReadOnlyText = False
        Me.btnMoment.tbShadow = False
        Me.btnMoment.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnMoment.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnMoment.tbShowDot = False
        Me.btnMoment.tbShowMoreIconImg = CType(resources.GetObject("btnMoment.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnMoment.tbShowNew = False
        Me.btnMoment.tbShowProgress = False
        Me.btnMoment.tbShowTip = True
        Me.btnMoment.tbShowToolTipOnButton = False
        Me.btnMoment.tbSplit = "13,11,13,11"
        Me.btnMoment.tbText = "朋友圈"
        Me.btnMoment.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMoment.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnMoment.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnMoment.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnMoment.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnMoment.tbTextMouseDownPlace = 2
        Me.btnMoment.tbToolTip = ""
        Me.btnMoment.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnMoment.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnMoment.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnMoment.VisibleEx = True
        '
        'btnRepair
        '
        Me.btnRepair.BackColor = System.Drawing.Color.Transparent
        Me.btnRepair.BindingForm = Nothing
        Me.btnRepair.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRepair.Location = New System.Drawing.Point(94, 5)
        Me.btnRepair.Name = "btnRepair"
        Me.btnRepair.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRepair.Selectable = True
        Me.btnRepair.Size = New System.Drawing.Size(81, 23)
        Me.btnRepair.TabIndex = 7
        Me.btnRepair.tbAdriftIconWhenHover = False
        Me.btnRepair.tbAutoSize = False
        Me.btnRepair.tbAutoSizeEx = True
        Me.btnRepair.tbBackgroundImage = Nothing
        Me.btnRepair.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRepair.tbBadgeNumber = 0
        Me.btnRepair.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRepair.tbEndEllipsis = False
        Me.btnRepair.tbIconHoldPlace = True
        Me.btnRepair.tbIconImage = Global.iTong.My.Resources.Resources.btn_repair_4
        Me.btnRepair.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRepair.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRepair.tbIconMore = False
        Me.btnRepair.tbIconMouseDown = Nothing
        Me.btnRepair.tbIconMouseHover = Nothing
        Me.btnRepair.tbIconMouseLeave = Nothing
        Me.btnRepair.tbIconPlaceText = 2
        Me.btnRepair.tbIconReadOnly = Nothing
        Me.btnRepair.tbImageMouseDown = Nothing
        Me.btnRepair.tbImageMouseHover = Nothing
        Me.btnRepair.tbImageMouseLeave = Nothing
        Me.btnRepair.tbProgressValue = 50
        Me.btnRepair.tbReadOnly = False
        Me.btnRepair.tbReadOnlyText = False
        Me.btnRepair.tbShadow = False
        Me.btnRepair.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRepair.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRepair.tbShowDot = False
        Me.btnRepair.tbShowMoreIconImg = CType(resources.GetObject("btnRepair.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRepair.tbShowNew = False
        Me.btnRepair.tbShowProgress = False
        Me.btnRepair.tbShowTip = True
        Me.btnRepair.tbShowToolTipOnButton = False
        Me.btnRepair.tbSplit = "13,11,13,11"
        Me.btnRepair.tbText = "修复闪退"
        Me.btnRepair.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnRepair.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnRepair.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepair.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepair.tbTextMouseDownPlace = 2
        Me.btnRepair.tbToolTip = ""
        Me.btnRepair.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRepair.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRepair.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.Visible = False
        Me.btnRepair.VisibleEx = True
        '
        'btnExport
        '
        Me.btnExport.BackColor = System.Drawing.Color.Transparent
        Me.btnExport.BindingForm = Nothing
        Me.btnExport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnExport.Location = New System.Drawing.Point(6, 5)
        Me.btnExport.Name = "btnExport"
        Me.btnExport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnExport.Selectable = True
        Me.btnExport.Size = New System.Drawing.Size(71, 23)
        Me.btnExport.TabIndex = 7
        Me.btnExport.tbAdriftIconWhenHover = False
        Me.btnExport.tbAutoSize = False
        Me.btnExport.tbAutoSizeEx = True
        Me.btnExport.tbBackgroundImage = Nothing
        Me.btnExport.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbBadgeNumber = 0
        Me.btnExport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnExport.tbEndEllipsis = False
        Me.btnExport.tbIconHoldPlace = True
        Me.btnExport.tbIconImage = Global.iTong.My.Resources.Resources.btn_export_4
        Me.btnExport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnExport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnExport.tbIconMore = True
        Me.btnExport.tbIconMouseDown = Nothing
        Me.btnExport.tbIconMouseHover = Nothing
        Me.btnExport.tbIconMouseLeave = Nothing
        Me.btnExport.tbIconPlaceText = 2
        Me.btnExport.tbIconReadOnly = Nothing
        Me.btnExport.tbImageMouseDown = Nothing
        Me.btnExport.tbImageMouseHover = Nothing
        Me.btnExport.tbImageMouseLeave = Nothing
        Me.btnExport.tbProgressValue = 50
        Me.btnExport.tbReadOnly = False
        Me.btnExport.tbReadOnlyText = False
        Me.btnExport.tbShadow = False
        Me.btnExport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnExport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnExport.tbShowDot = False
        Me.btnExport.tbShowMoreIconImg = CType(resources.GetObject("btnExport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnExport.tbShowNew = False
        Me.btnExport.tbShowProgress = False
        Me.btnExport.tbShowTip = True
        Me.btnExport.tbShowToolTipOnButton = False
        Me.btnExport.tbSplit = "13,11,13,11"
        Me.btnExport.tbText = "导出"
        Me.btnExport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnExport.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnExport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnExport.tbTextMouseDownPlace = 2
        Me.btnExport.tbToolTip = ""
        Me.btnExport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnExport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnExport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnExport.VisibleEx = True
        '
        'pnlSearchFriend
        '
        Me.pnlSearchFriend.BackColor = System.Drawing.SystemColors.Control
        Me.pnlSearchFriend.Controls.Add(Me.txtSearchFriend)
        Me.pnlSearchFriend.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlSearchFriend.Location = New System.Drawing.Point(0, 32)
        Me.pnlSearchFriend.Margin = New System.Windows.Forms.Padding(0, 0, 1, 1)
        Me.pnlSearchFriend.Name = "pnlSearchFriend"
        Me.pnlSearchFriend.Size = New System.Drawing.Size(267, 31)
        Me.pnlSearchFriend.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlSearchFriend.TabIndex = 32
        Me.pnlSearchFriend.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlSearchFriend.tbShowWatermark = False
        Me.pnlSearchFriend.tbSplit = "3,3,3,3"
        Me.pnlSearchFriend.tbWatermark = Nothing
        Me.pnlSearchFriend.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlSearchFriend.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'txtSearchFriend
        '
        Me.txtSearchFriend.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.txtSearchFriend.BackColor = System.Drawing.Color.Transparent
        Me.txtSearchFriend.Location = New System.Drawing.Point(24, 4)
        Me.txtSearchFriend.MaxLength = 32767
        Me.txtSearchFriend.Name = "txtSearchFriend"
        Me.txtSearchFriend.Padding = New System.Windows.Forms.Padding(5)
        Me.txtSearchFriend.ReadOnly = False
        Me.txtSearchFriend.SearchText = ""
        Me.txtSearchFriend.SearchTipText = "Search"
        Me.txtSearchFriend.ShowClear = True
        Me.txtSearchFriend.ShowClearAlways = False
        Me.txtSearchFriend.ShowMore = False
        Me.txtSearchFriend.ShowSearch = True
        Me.txtSearchFriend.Size = New System.Drawing.Size(219, 23)
        Me.txtSearchFriend.TabIndex = 9
        Me.txtSearchFriend.tbImageSearchState = iTong.Components.ImageState.ThreeState
        Me.txtSearchFriend.Timer = True
        Me.txtSearchFriend.TimerInterval = 0.5R
        '
        'pnlState
        '
        Me.pnlState.BackColor = System.Drawing.Color.FromArgb(CType(CType(237, Byte), Integer), CType(CType(240, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.pnlState.Controls.Add(Me.btnOK)
        Me.pnlState.Controls.Add(Me.lblEndTime)
        Me.pnlState.Controls.Add(Me.dtpEnd)
        Me.pnlState.Controls.Add(Me.lblStartTime)
        Me.pnlState.Controls.Add(Me.btnCopy)
        Me.pnlState.Controls.Add(Me.dtpStart)
        Me.pnlState.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlState.Location = New System.Drawing.Point(268, 540)
        Me.pnlState.Margin = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.pnlState.Name = "pnlState"
        Me.pnlState.Size = New System.Drawing.Size(600, 31)
        Me.pnlState.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlState.TabIndex = 9
        Me.pnlState.tbBackgroundImage = Nothing
        Me.pnlState.tbShowWatermark = False
        Me.pnlState.tbSplit = "0,1,0,1"
        Me.pnlState.tbWatermark = Nothing
        Me.pnlState.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlState.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(354, 5)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(41, 21)
        Me.btnOK.TabIndex = 18
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = True
        Me.btnOK.tbBackgroundImage = Nothing
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "确定"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOK.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnOK.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnOK.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnOK.tbTextMouseDownPlace = 2
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOK.VisibleEx = True
        '
        'lblEndTime
        '
        Me.lblEndTime.AutoSize = True
        Me.lblEndTime.BackColor = System.Drawing.Color.Transparent
        Me.lblEndTime.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblEndTime.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblEndTime.Location = New System.Drawing.Point(180, 9)
        Me.lblEndTime.Name = "lblEndTime"
        Me.lblEndTime.Size = New System.Drawing.Size(53, 12)
        Me.lblEndTime.TabIndex = 17
        Me.lblEndTime.Text = "结束日期"
        Me.lblEndTime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'dtpEnd
        '
        Me.dtpEnd.Checked = False
        Me.dtpEnd.CustomFormat = "yyyy-MM-dd"
        Me.dtpEnd.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.dtpEnd.Location = New System.Drawing.Point(239, 5)
        Me.dtpEnd.Name = "dtpEnd"
        Me.dtpEnd.Size = New System.Drawing.Size(100, 21)
        Me.dtpEnd.TabIndex = 16
        '
        'lblStartTime
        '
        Me.lblStartTime.AutoSize = True
        Me.lblStartTime.BackColor = System.Drawing.Color.Transparent
        Me.lblStartTime.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblStartTime.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblStartTime.Location = New System.Drawing.Point(8, 9)
        Me.lblStartTime.Name = "lblStartTime"
        Me.lblStartTime.Size = New System.Drawing.Size(53, 12)
        Me.lblStartTime.TabIndex = 15
        Me.lblStartTime.Text = "起始日期"
        Me.lblStartTime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnCopy
        '
        Me.btnCopy.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCopy.BackColor = System.Drawing.Color.Transparent
        Me.btnCopy.BindingForm = Nothing
        Me.btnCopy.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCopy.Location = New System.Drawing.Point(499, 5)
        Me.btnCopy.Name = "btnCopy"
        Me.btnCopy.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCopy.Selectable = True
        Me.btnCopy.Size = New System.Drawing.Size(0, 0)
        Me.btnCopy.TabIndex = 12
        Me.btnCopy.tbAdriftIconWhenHover = False
        Me.btnCopy.tbAutoSize = False
        Me.btnCopy.tbAutoSizeEx = True
        Me.btnCopy.tbBackgroundImage = Nothing
        Me.btnCopy.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCopy.tbBadgeNumber = 0
        Me.btnCopy.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCopy.tbEndEllipsis = False
        Me.btnCopy.tbIconHoldPlace = True
        Me.btnCopy.tbIconImage = Nothing
        Me.btnCopy.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnCopy.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnCopy.tbIconMore = False
        Me.btnCopy.tbIconMouseDown = Nothing
        Me.btnCopy.tbIconMouseHover = Nothing
        Me.btnCopy.tbIconMouseLeave = Nothing
        Me.btnCopy.tbIconPlaceText = 2
        Me.btnCopy.tbIconReadOnly = Nothing
        Me.btnCopy.tbImageMouseDown = Nothing
        Me.btnCopy.tbImageMouseHover = Nothing
        Me.btnCopy.tbImageMouseLeave = Nothing
        Me.btnCopy.tbProgressValue = 50
        Me.btnCopy.tbReadOnly = False
        Me.btnCopy.tbReadOnlyText = False
        Me.btnCopy.tbShadow = False
        Me.btnCopy.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnCopy.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnCopy.tbShowDot = False
        Me.btnCopy.tbShowMoreIconImg = CType(resources.GetObject("btnCopy.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCopy.tbShowNew = False
        Me.btnCopy.tbShowProgress = False
        Me.btnCopy.tbShowTip = True
        Me.btnCopy.tbShowToolTipOnButton = False
        Me.btnCopy.tbSplit = "13,11,13,11"
        Me.btnCopy.tbText = "复制全部消息"
        Me.btnCopy.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnCopy.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.btnCopy.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(195, Byte), Integer), CType(CType(204, Byte), Integer))
        Me.btnCopy.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnCopy.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnCopy.tbTextMouseDownPlace = 2
        Me.btnCopy.tbToolTip = ""
        Me.btnCopy.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCopy.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCopy.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnCopy.Visible = False
        Me.btnCopy.VisibleEx = False
        '
        'dtpStart
        '
        Me.dtpStart.Checked = False
        Me.dtpStart.CustomFormat = "yyyy-MM-dd"
        Me.dtpStart.Format = System.Windows.Forms.DateTimePickerFormat.Custom
        Me.dtpStart.Location = New System.Drawing.Point(67, 5)
        Me.dtpStart.Name = "dtpStart"
        Me.dtpStart.Size = New System.Drawing.Size(100, 21)
        Me.dtpStart.TabIndex = 12
        '
        'pnlTitle
        '
        Me.pnlTitle.BackColor = System.Drawing.SystemColors.Control
        Me.pnlTitle.Controls.Add(Me.lblTitle)
        Me.pnlTitle.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTitle.Location = New System.Drawing.Point(268, 32)
        Me.pnlTitle.Margin = New System.Windows.Forms.Padding(0, 0, 1, 0)
        Me.pnlTitle.Name = "pnlTitle"
        Me.pnlTitle.Size = New System.Drawing.Size(599, 32)
        Me.pnlTitle.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTitle.TabIndex = 8
        Me.pnlTitle.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_navigation
        Me.pnlTitle.tbShowWatermark = False
        Me.pnlTitle.tbSplit = "3,3,3,3"
        Me.pnlTitle.tbWatermark = Nothing
        Me.pnlTitle.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTitle.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblTitle
        '
        Me.lblTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblTitle.BackColor = System.Drawing.Color.Transparent
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblTitle.Location = New System.Drawing.Point(73, 5)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(452, 23)
        Me.lblTitle.TabIndex = 13
        Me.lblTitle.Text = "欢迎使用微信消息记录管理器"
        Me.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlContainer
        '
        Me.pnlContainer.BackColor = System.Drawing.SystemColors.Control
        Me.pnlContainer.Controls.Add(Me.lblSystemInfo)
        Me.pnlContainer.Controls.Add(Me.wbsChat)
        Me.pnlContainer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContainer.Location = New System.Drawing.Point(268, 64)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(600, 475)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 3
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblSystemInfo
        '
        Me.lblSystemInfo.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblSystemInfo.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer))
        Me.lblSystemInfo.Font = New System.Drawing.Font("宋体", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblSystemInfo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblSystemInfo.Location = New System.Drawing.Point(29, 222)
        Me.lblSystemInfo.Name = "lblSystemInfo"
        Me.lblSystemInfo.Size = New System.Drawing.Size(542, 30)
        Me.lblSystemInfo.TabIndex = 3
        Me.lblSystemInfo.tbAdriftWhenHover = False
        Me.lblSystemInfo.tbAutoEllipsis = False
        Me.lblSystemInfo.tbAutoSize = False
        Me.lblSystemInfo.tbHideImage = False
        Me.lblSystemInfo.tbIconImage = Nothing
        Me.lblSystemInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblSystemInfo.tbIconPlaceText = 5
        Me.lblSystemInfo.tbShadow = False
        Me.lblSystemInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblSystemInfo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblSystemInfo.tbShowScrolling = False
        Me.lblSystemInfo.Text = "查看消息，请选中左侧联系人"
        Me.lblSystemInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblSystemInfo.Visible = False
        '
        'wbsChat
        '
        Me.wbsChat.AllowWebBrowserDrop = False
        Me.wbsChat.Client = ""
        Me.wbsChat.DeviceList = Nothing
        Me.wbsChat.Dock = System.Windows.Forms.DockStyle.Fill
        Me.wbsChat.IsConnected = False
        Me.wbsChat.IsWebBrowserContextMenuEnabled = False
        Me.wbsChat.Jailbreaked = False
        Me.wbsChat.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.wbsChat.LoadingFont = Nothing
        Me.wbsChat.LoadingGif = CType(resources.GetObject("wbsChat.LoadingGif"), System.Drawing.Image)
        Me.wbsChat.Location = New System.Drawing.Point(0, 0)
        Me.wbsChat.MinimumSize = New System.Drawing.Size(20, 20)
        Me.wbsChat.Name = "wbsChat"
        Me.wbsChat.ScriptErrorsSuppressed = True
        Me.wbsChat.ShowLoadingWait = False
        Me.wbsChat.ShowNavigateErrorPage = True
        Me.wbsChat.ShowProgress = True
        Me.wbsChat.Size = New System.Drawing.Size(600, 475)
        Me.wbsChat.SN = ""
        Me.wbsChat.TabIndex = 0
        Me.wbsChat.UserInfo = ""
        Me.wbsChat.UserInfoEncode = ""
        '
        'pnlFriend
        '
        Me.pnlFriend.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFriend.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlFriend.Location = New System.Drawing.Point(0, 64)
        Me.pnlFriend.Margin = New System.Windows.Forms.Padding(0, 0, 1, 0)
        Me.pnlFriend.Name = "pnlFriend"
        Me.tpnlContent.SetRowSpan(Me.pnlFriend, 2)
        Me.pnlFriend.Size = New System.Drawing.Size(267, 507)
        Me.pnlFriend.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFriend.TabIndex = 5
        Me.pnlFriend.tbBackgroundImage = Nothing
        Me.pnlFriend.tbShowWatermark = False
        Me.pnlFriend.tbSplit = "0,0,0,0"
        Me.pnlFriend.tbWatermark = Nothing
        Me.pnlFriend.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFriend.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlLoading
        '
        Me.pnlLoading.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.picNoLockScreen)
        Me.pnlLoading.Controls.Add(Me.lblLoadingDescription)
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.picLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(99, 3)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(417, 422)
        Me.pnlLoading.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoading.TabIndex = 7
        Me.pnlLoading.tbBackgroundImage = Nothing
        Me.pnlLoading.tbShowWatermark = False
        Me.pnlLoading.tbSplit = "0,0,0,0"
        Me.pnlLoading.tbWatermark = Nothing
        Me.pnlLoading.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoading.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'picNoLockScreen
        '
        Me.picNoLockScreen.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picNoLockScreen.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.picNoLockScreen.Image = Global.iTong.My.Resources.Resources.weixin_nolockscreen
        Me.picNoLockScreen.Location = New System.Drawing.Point(-20, 169)
        Me.picNoLockScreen.Margin = New System.Windows.Forms.Padding(0)
        Me.picNoLockScreen.Name = "picNoLockScreen"
        Me.picNoLockScreen.Size = New System.Drawing.Size(150, 98)
        Me.picNoLockScreen.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picNoLockScreen.TabIndex = 33
        Me.picNoLockScreen.TabStop = False
        Me.picNoLockScreen.Visible = False
        '
        'lblLoadingDescription
        '
        Me.lblLoadingDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoadingDescription.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoadingDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer))
        Me.lblLoadingDescription.Location = New System.Drawing.Point(125, 251)
        Me.lblLoadingDescription.Name = "lblLoadingDescription"
        Me.lblLoadingDescription.Size = New System.Drawing.Size(409, 26)
        Me.lblLoadingDescription.TabIndex = 32
        Me.lblLoadingDescription.Text = "微信记录比较多时候，需要较长时间，请务必耐心等待"
        Me.lblLoadingDescription.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblLoadingDescription.Visible = False
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(154, 175)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(228, 45)
        Me.lblLoading.TabIndex = 12
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picLoading
        '
        Me.picLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.picLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.picLoading.Location = New System.Drawing.Point(63, 150)
        Me.picLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(91, 95)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picLoading.TabIndex = 11
        Me.picLoading.TabStop = False
        '
        'pnlDescription
        '
        Me.pnlDescription.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlDescription.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlDescription.Controls.Add(Me.lblNoRootMedthod)
        Me.pnlDescription.Controls.Add(Me.pnlMethod2)
        Me.pnlDescription.Controls.Add(Me.pnlMethod1)
        Me.pnlDescription.Controls.Add(Me.lblAbout)
        Me.pnlDescription.Controls.Add(Me.lblTryRoot)
        Me.pnlDescription.Controls.Add(Me.pnlWechatAssiant)
        Me.pnlDescription.Controls.Add(Me.lblHowtoRoot)
        Me.pnlDescription.Controls.Add(Me.lblReCheckEx)
        Me.pnlDescription.Controls.Add(Me.lblCacelLimit)
        Me.pnlDescription.Controls.Add(Me.btnShowRootCourse)
        Me.pnlDescription.Controls.Add(Me.picError)
        Me.pnlDescription.Controls.Add(Me.lblWeixinTitle)
        Me.pnlDescription.Controls.Add(Me.btnDownloadWeixin)
        Me.pnlDescription.Controls.Add(Me.lblDownloadWeixin)
        Me.pnlDescription.Controls.Add(Me.lblWeixinDescription_1)
        Me.pnlDescription.Controls.Add(Me.lblShowRootCourse)
        Me.pnlDescription.Controls.Add(Me.picWeixinIcon)
        Me.pnlDescription.Controls.Add(Me.lblWeixinDescription)
        Me.pnlDescription.Location = New System.Drawing.Point(95, 120)
        Me.pnlDescription.Name = "pnlDescription"
        Me.pnlDescription.Size = New System.Drawing.Size(868, 367)
        Me.pnlDescription.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDescription.TabIndex = 33
        Me.pnlDescription.tbBackgroundImage = Nothing
        Me.pnlDescription.tbShowWatermark = False
        Me.pnlDescription.tbSplit = "0,0,0,0"
        Me.pnlDescription.tbWatermark = Nothing
        Me.pnlDescription.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDescription.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblNoRootMedthod
        '
        Me.lblNoRootMedthod.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNoRootMedthod.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblNoRootMedthod.Location = New System.Drawing.Point(100, 68)
        Me.lblNoRootMedthod.Name = "lblNoRootMedthod"
        Me.lblNoRootMedthod.Size = New System.Drawing.Size(266, 23)
        Me.lblNoRootMedthod.TabIndex = 45
        Me.lblNoRootMedthod.Text = "您也可尝试以下免ROOT方式"
        Me.lblNoRootMedthod.Visible = False
        '
        'pnlMethod2
        '
        Me.pnlMethod2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlMethod2.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlMethod2.Controls.Add(Me.lblDescRootAgain)
        Me.pnlMethod2.Controls.Add(Me.PictureBox2)
        Me.pnlMethod2.Controls.Add(Me.lblWechatTxtManage)
        Me.pnlMethod2.Controls.Add(Me.btnStepRoot)
        Me.pnlMethod2.ForeColor = System.Drawing.Color.Black
        Me.pnlMethod2.Location = New System.Drawing.Point(100, 330)
        Me.pnlMethod2.Name = "pnlMethod2"
        Me.pnlMethod2.Size = New System.Drawing.Size(705, 118)
        Me.pnlMethod2.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlMethod2.TabIndex = 43
        Me.pnlMethod2.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_border_light
        Me.pnlMethod2.tbShowWatermark = False
        Me.pnlMethod2.tbSplit = "5,5,5,5"
        Me.pnlMethod2.tbWatermark = Nothing
        Me.pnlMethod2.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlMethod2.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlMethod2.Visible = False
        '
        'lblDescRootAgain
        '
        Me.lblDescRootAgain.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDescRootAgain.BackColor = System.Drawing.Color.Transparent
        Me.lblDescRootAgain.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDescRootAgain.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.lblDescRootAgain.Location = New System.Drawing.Point(106, 48)
        Me.lblDescRootAgain.Name = "lblDescRootAgain"
        Me.lblDescRootAgain.Size = New System.Drawing.Size(410, 61)
        Me.lblDescRootAgain.TabIndex = 30
        Me.lblDescRootAgain.Text = "需要ROOT，才能导出文本信息" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "若您的设备已ROOT，优先尝试取消设备中第三方应用限制访问权限后重试"
        '
        'PictureBox2
        '
        Me.PictureBox2.Image = Global.iTong.My.Resources.Resources.more_weixin
        Me.PictureBox2.Location = New System.Drawing.Point(47, 21)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(55, 55)
        Me.PictureBox2.TabIndex = 32
        Me.PictureBox2.TabStop = False
        '
        'lblWechatTxtManage
        '
        Me.lblWechatTxtManage.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWechatTxtManage.BackColor = System.Drawing.Color.Transparent
        Me.lblWechatTxtManage.Font = New System.Drawing.Font("宋体", 11.0!, System.Drawing.FontStyle.Bold)
        Me.lblWechatTxtManage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblWechatTxtManage.Location = New System.Drawing.Point(106, 16)
        Me.lblWechatTxtManage.Name = "lblWechatTxtManage"
        Me.lblWechatTxtManage.Size = New System.Drawing.Size(176, 29)
        Me.lblWechatTxtManage.TabIndex = 30
        Me.lblWechatTxtManage.Text = "微信聊天文本管理"
        Me.lblWechatTxtManage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnStepRoot
        '
        Me.btnStepRoot.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStepRoot.BackColor = System.Drawing.Color.Transparent
        Me.btnStepRoot.BindingForm = Nothing
        Me.btnStepRoot.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnStepRoot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnStepRoot.Location = New System.Drawing.Point(9, 37)
        Me.btnStepRoot.Name = "btnStepRoot"
        Me.btnStepRoot.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnStepRoot.Selectable = True
        Me.btnStepRoot.Size = New System.Drawing.Size(22, 22)
        Me.btnStepRoot.TabIndex = 31
        Me.btnStepRoot.tbAdriftIconWhenHover = False
        Me.btnStepRoot.tbAutoSize = False
        Me.btnStepRoot.tbAutoSizeEx = True
        Me.btnStepRoot.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_weixinNotRoot_step3
        Me.btnStepRoot.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnStepRoot.tbBadgeNumber = 0
        Me.btnStepRoot.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStepRoot.tbEndEllipsis = False
        Me.btnStepRoot.tbIconHoldPlace = True
        Me.btnStepRoot.tbIconImage = Nothing
        Me.btnStepRoot.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStepRoot.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStepRoot.tbIconMore = False
        Me.btnStepRoot.tbIconMouseDown = Nothing
        Me.btnStepRoot.tbIconMouseHover = Nothing
        Me.btnStepRoot.tbIconMouseLeave = Nothing
        Me.btnStepRoot.tbIconPlaceText = 2
        Me.btnStepRoot.tbIconReadOnly = Nothing
        Me.btnStepRoot.tbImageMouseDown = Nothing
        Me.btnStepRoot.tbImageMouseHover = Nothing
        Me.btnStepRoot.tbImageMouseLeave = Nothing
        Me.btnStepRoot.tbProgressValue = 50
        Me.btnStepRoot.tbReadOnly = False
        Me.btnStepRoot.tbReadOnlyText = False
        Me.btnStepRoot.tbShadow = False
        Me.btnStepRoot.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStepRoot.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStepRoot.tbShowDot = False
        Me.btnStepRoot.tbShowMoreIconImg = CType(resources.GetObject("btnStepRoot.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStepRoot.tbShowNew = False
        Me.btnStepRoot.tbShowProgress = False
        Me.btnStepRoot.tbShowTip = True
        Me.btnStepRoot.tbShowToolTipOnButton = False
        Me.btnStepRoot.tbSplit = "0,0,0,0"
        Me.btnStepRoot.tbText = ""
        Me.btnStepRoot.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStepRoot.tbTextColor = System.Drawing.Color.White
        Me.btnStepRoot.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStepRoot.tbTextColorDown = System.Drawing.Color.White
        Me.btnStepRoot.tbTextColorHover = System.Drawing.Color.White
        Me.btnStepRoot.tbTextMouseDownPlace = 0
        Me.btnStepRoot.tbToolTip = ""
        Me.btnStepRoot.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStepRoot.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStepRoot.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStepRoot.VisibleEx = True
        '
        'pnlMethod1
        '
        Me.pnlMethod1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlMethod1.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlMethod1.Controls.Add(Me.btnManage)
        Me.pnlMethod1.Controls.Add(Me.PictureBox1)
        Me.pnlMethod1.Controls.Add(Me.lblWechatMediaManage)
        Me.pnlMethod1.Controls.Add(Me.lblDescCacelLimit)
        Me.pnlMethod1.Location = New System.Drawing.Point(100, 210)
        Me.pnlMethod1.Name = "pnlMethod1"
        Me.pnlMethod1.Size = New System.Drawing.Size(704, 114)
        Me.pnlMethod1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlMethod1.TabIndex = 44
        Me.pnlMethod1.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_border_light
        Me.pnlMethod1.tbShowWatermark = False
        Me.pnlMethod1.tbSplit = "5,5,5,5"
        Me.pnlMethod1.tbWatermark = Nothing
        Me.pnlMethod1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlMethod1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlMethod1.Visible = False
        '
        'btnManage
        '
        Me.btnManage.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnManage.BackColor = System.Drawing.Color.Transparent
        Me.btnManage.BindingForm = Nothing
        Me.btnManage.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnManage.Location = New System.Drawing.Point(593, 39)
        Me.btnManage.Name = "btnManage"
        Me.btnManage.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnManage.Selectable = True
        Me.btnManage.Size = New System.Drawing.Size(84, 36)
        Me.btnManage.TabIndex = 54
        Me.btnManage.tbAdriftIconWhenHover = False
        Me.btnManage.tbAutoSize = False
        Me.btnManage.tbAutoSizeEx = False
        Me.btnManage.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnManage.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnManage.tbBadgeNumber = 0
        Me.btnManage.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnManage.tbEndEllipsis = False
        Me.btnManage.tbIconHoldPlace = True
        Me.btnManage.tbIconImage = Nothing
        Me.btnManage.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnManage.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnManage.tbIconMore = False
        Me.btnManage.tbIconMouseDown = Nothing
        Me.btnManage.tbIconMouseHover = Nothing
        Me.btnManage.tbIconMouseLeave = Nothing
        Me.btnManage.tbIconPlaceText = 2
        Me.btnManage.tbIconReadOnly = Nothing
        Me.btnManage.tbImageMouseDown = Nothing
        Me.btnManage.tbImageMouseHover = Nothing
        Me.btnManage.tbImageMouseLeave = Nothing
        Me.btnManage.tbProgressValue = 50
        Me.btnManage.tbReadOnly = False
        Me.btnManage.tbReadOnlyText = False
        Me.btnManage.tbShadow = False
        Me.btnManage.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnManage.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnManage.tbShowDot = False
        Me.btnManage.tbShowMoreIconImg = CType(resources.GetObject("btnManage.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnManage.tbShowNew = False
        Me.btnManage.tbShowProgress = False
        Me.btnManage.tbShowTip = True
        Me.btnManage.tbShowToolTipOnButton = False
        Me.btnManage.tbSplit = "13,11,13,11"
        Me.btnManage.tbText = "立即管理"
        Me.btnManage.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnManage.tbTextColor = System.Drawing.Color.White
        Me.btnManage.tbTextColorDisable = System.Drawing.Color.White
        Me.btnManage.tbTextColorDown = System.Drawing.Color.White
        Me.btnManage.tbTextColorHover = System.Drawing.Color.White
        Me.btnManage.tbTextMouseDownPlace = 0
        Me.btnManage.tbToolTip = ""
        Me.btnManage.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnManage.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnManage.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnManage.VisibleEx = True
        '
        'PictureBox1
        '
        Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.androidwehcatmedia_recommend
        Me.PictureBox1.Location = New System.Drawing.Point(47, 27)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(52, 51)
        Me.PictureBox1.TabIndex = 32
        Me.PictureBox1.TabStop = False
        '
        'lblWechatMediaManage
        '
        Me.lblWechatMediaManage.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWechatMediaManage.BackColor = System.Drawing.Color.Transparent
        Me.lblWechatMediaManage.Font = New System.Drawing.Font("宋体", 11.0!, System.Drawing.FontStyle.Bold)
        Me.lblWechatMediaManage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblWechatMediaManage.Location = New System.Drawing.Point(107, 26)
        Me.lblWechatMediaManage.Name = "lblWechatMediaManage"
        Me.lblWechatMediaManage.Size = New System.Drawing.Size(176, 29)
        Me.lblWechatMediaManage.TabIndex = 30
        Me.lblWechatMediaManage.Text = "微信媒体管理"
        Me.lblWechatMediaManage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblDescCacelLimit
        '
        Me.lblDescCacelLimit.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDescCacelLimit.BackColor = System.Drawing.Color.Transparent
        Me.lblDescCacelLimit.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDescCacelLimit.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.lblDescCacelLimit.Location = New System.Drawing.Point(107, 52)
        Me.lblDescCacelLimit.Name = "lblDescCacelLimit"
        Me.lblDescCacelLimit.Size = New System.Drawing.Size(473, 34)
        Me.lblDescCacelLimit.TabIndex = 30
        Me.lblDescCacelLimit.Text = "免ROOT批量导出/删除微信图片，小视频，语音，文件（不支持管理文本消息）"
        Me.lblDescCacelLimit.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblAbout
        '
        Me.lblAbout.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblAbout.BackColor = System.Drawing.Color.Transparent
        Me.lblAbout.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblAbout.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.lblAbout.Location = New System.Drawing.Point(79, 387)
        Me.lblAbout.Name = "lblAbout"
        Me.lblAbout.Size = New System.Drawing.Size(701, 42)
        Me.lblAbout.TabIndex = 30
        Me.lblAbout.Text = "其他疑问详询，客服电话：4009981389  QQ：132691727"
        Me.lblAbout.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblAbout.Visible = False
        '
        'lblTryRoot
        '
        Me.lblTryRoot.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblTryRoot.BackColor = System.Drawing.Color.Transparent
        Me.lblTryRoot.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblTryRoot.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.lblTryRoot.Location = New System.Drawing.Point(282, 35)
        Me.lblTryRoot.Name = "lblTryRoot"
        Me.lblTryRoot.Size = New System.Drawing.Size(341, 46)
        Me.lblTryRoot.TabIndex = 30
        Me.lblTryRoot.Text = "您可以尝试ROOT,已Root设备尝试取消第三方权限限制"
        Me.lblTryRoot.Visible = False
        '
        'pnlWechatAssiant
        '
        Me.pnlWechatAssiant.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlWechatAssiant.BackColor = System.Drawing.Color.FromArgb(CType(CType(239, Byte), Integer), CType(CType(242, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlWechatAssiant.Controls.Add(Me.btnOpenWechatAssisant)
        Me.pnlWechatAssiant.Controls.Add(Me.PictureBox3)
        Me.pnlWechatAssiant.Controls.Add(Me.lblWechatAssistant)
        Me.pnlWechatAssiant.Controls.Add(Me.lblDescWechatAssistant)
        Me.pnlWechatAssiant.Location = New System.Drawing.Point(22, 103)
        Me.pnlWechatAssiant.Name = "pnlWechatAssiant"
        Me.pnlWechatAssiant.Size = New System.Drawing.Size(704, 114)
        Me.pnlWechatAssiant.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlWechatAssiant.TabIndex = 44
        Me.pnlWechatAssiant.tbBackgroundImage = Global.iTong.My.Resources.Resources.pnl_bg_border_light
        Me.pnlWechatAssiant.tbShowWatermark = False
        Me.pnlWechatAssiant.tbSplit = "5,5,5,5"
        Me.pnlWechatAssiant.tbWatermark = Nothing
        Me.pnlWechatAssiant.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlWechatAssiant.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlWechatAssiant.Visible = False
        '
        'btnOpenWechatAssisant
        '
        Me.btnOpenWechatAssisant.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOpenWechatAssisant.BackColor = System.Drawing.Color.Transparent
        Me.btnOpenWechatAssisant.BindingForm = Nothing
        Me.btnOpenWechatAssisant.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOpenWechatAssisant.Location = New System.Drawing.Point(593, 39)
        Me.btnOpenWechatAssisant.Name = "btnOpenWechatAssisant"
        Me.btnOpenWechatAssisant.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOpenWechatAssisant.Selectable = True
        Me.btnOpenWechatAssisant.Size = New System.Drawing.Size(84, 36)
        Me.btnOpenWechatAssisant.TabIndex = 54
        Me.btnOpenWechatAssisant.tbAdriftIconWhenHover = False
        Me.btnOpenWechatAssisant.tbAutoSize = False
        Me.btnOpenWechatAssisant.tbAutoSizeEx = False
        Me.btnOpenWechatAssisant.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOpenWechatAssisant.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOpenWechatAssisant.tbBadgeNumber = 0
        Me.btnOpenWechatAssisant.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpenWechatAssisant.tbEndEllipsis = False
        Me.btnOpenWechatAssisant.tbIconHoldPlace = True
        Me.btnOpenWechatAssisant.tbIconImage = Nothing
        Me.btnOpenWechatAssisant.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenWechatAssisant.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOpenWechatAssisant.tbIconMore = False
        Me.btnOpenWechatAssisant.tbIconMouseDown = Nothing
        Me.btnOpenWechatAssisant.tbIconMouseHover = Nothing
        Me.btnOpenWechatAssisant.tbIconMouseLeave = Nothing
        Me.btnOpenWechatAssisant.tbIconPlaceText = 2
        Me.btnOpenWechatAssisant.tbIconReadOnly = Nothing
        Me.btnOpenWechatAssisant.tbImageMouseDown = Nothing
        Me.btnOpenWechatAssisant.tbImageMouseHover = Nothing
        Me.btnOpenWechatAssisant.tbImageMouseLeave = Nothing
        Me.btnOpenWechatAssisant.tbProgressValue = 50
        Me.btnOpenWechatAssisant.tbReadOnly = False
        Me.btnOpenWechatAssisant.tbReadOnlyText = False
        Me.btnOpenWechatAssisant.tbShadow = False
        Me.btnOpenWechatAssisant.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOpenWechatAssisant.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOpenWechatAssisant.tbShowDot = False
        Me.btnOpenWechatAssisant.tbShowMoreIconImg = CType(resources.GetObject("btnOpenWechatAssisant.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOpenWechatAssisant.tbShowNew = False
        Me.btnOpenWechatAssisant.tbShowProgress = False
        Me.btnOpenWechatAssisant.tbShowTip = True
        Me.btnOpenWechatAssisant.tbShowToolTipOnButton = False
        Me.btnOpenWechatAssisant.tbSplit = "13,11,13,11"
        Me.btnOpenWechatAssisant.tbText = "立即查看"
        Me.btnOpenWechatAssisant.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenWechatAssisant.tbTextColor = System.Drawing.Color.White
        Me.btnOpenWechatAssisant.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOpenWechatAssisant.tbTextColorDown = System.Drawing.Color.White
        Me.btnOpenWechatAssisant.tbTextColorHover = System.Drawing.Color.White
        Me.btnOpenWechatAssisant.tbTextMouseDownPlace = 0
        Me.btnOpenWechatAssisant.tbToolTip = ""
        Me.btnOpenWechatAssisant.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpenWechatAssisant.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpenWechatAssisant.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenWechatAssisant.VisibleEx = True
        '
        'PictureBox3
        '
        Me.PictureBox3.Image = Global.iTong.My.Resources.Resources.WechatAssistant_recommend
        Me.PictureBox3.Location = New System.Drawing.Point(47, 27)
        Me.PictureBox3.Name = "PictureBox3"
        Me.PictureBox3.Size = New System.Drawing.Size(52, 51)
        Me.PictureBox3.TabIndex = 32
        Me.PictureBox3.TabStop = False
        '
        'lblWechatAssistant
        '
        Me.lblWechatAssistant.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWechatAssistant.BackColor = System.Drawing.Color.Transparent
        Me.lblWechatAssistant.Font = New System.Drawing.Font("宋体", 11.0!, System.Drawing.FontStyle.Bold)
        Me.lblWechatAssistant.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblWechatAssistant.Location = New System.Drawing.Point(107, 26)
        Me.lblWechatAssistant.Name = "lblWechatAssistant"
        Me.lblWechatAssistant.Size = New System.Drawing.Size(176, 29)
        Me.lblWechatAssistant.TabIndex = 30
        Me.lblWechatAssistant.Text = "微信备份助手"
        Me.lblWechatAssistant.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblDescWechatAssistant
        '
        Me.lblDescWechatAssistant.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDescWechatAssistant.BackColor = System.Drawing.Color.Transparent
        Me.lblDescWechatAssistant.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDescWechatAssistant.ForeColor = System.Drawing.Color.FromArgb(CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer), CType(CType(108, Byte), Integer))
        Me.lblDescWechatAssistant.Location = New System.Drawing.Point(107, 52)
        Me.lblDescWechatAssistant.Name = "lblDescWechatAssistant"
        Me.lblDescWechatAssistant.Size = New System.Drawing.Size(473, 34)
        Me.lblDescWechatAssistant.TabIndex = 30
        Me.lblDescWechatAssistant.Text = "无需数据线，免root导出微信文字，图片，小视频，语音"
        Me.lblDescWechatAssistant.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblHowtoRoot
        '
        Me.lblHowtoRoot.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblHowtoRoot.BackColor = System.Drawing.Color.Transparent
        Me.lblHowtoRoot.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblHowtoRoot.ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.lblHowtoRoot.Location = New System.Drawing.Point(659, 52)
        Me.lblHowtoRoot.Name = "lblHowtoRoot"
        Me.lblHowtoRoot.Size = New System.Drawing.Size(67, 18)
        Me.lblHowtoRoot.TabIndex = 31
        Me.lblHowtoRoot.Text = "如何ROOT"
        Me.lblHowtoRoot.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblHowtoRoot.Visible = False
        '
        'lblReCheckEx
        '
        Me.lblReCheckEx.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblReCheckEx.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblReCheckEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblReCheckEx.Location = New System.Drawing.Point(311, 92)
        Me.lblReCheckEx.Name = "lblReCheckEx"
        Me.lblReCheckEx.Size = New System.Drawing.Size(78, 18)
        Me.lblReCheckEx.TabIndex = 42
        Me.lblReCheckEx.Text = "重新检查"
        Me.lblReCheckEx.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblCacelLimit
        '
        Me.lblCacelLimit.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblCacelLimit.BackColor = System.Drawing.Color.Transparent
        Me.lblCacelLimit.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblCacelLimit.ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.lblCacelLimit.Location = New System.Drawing.Point(739, 52)
        Me.lblCacelLimit.Name = "lblCacelLimit"
        Me.lblCacelLimit.Size = New System.Drawing.Size(98, 18)
        Me.lblCacelLimit.TabIndex = 31
        Me.lblCacelLimit.Text = "如何取消限制 "
        Me.lblCacelLimit.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCacelLimit.Visible = False
        '
        'btnShowRootCourse
        '
        Me.btnShowRootCourse.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnShowRootCourse.BackColor = System.Drawing.Color.Transparent
        Me.btnShowRootCourse.BindingForm = Nothing
        Me.btnShowRootCourse.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnShowRootCourse.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnShowRootCourse.Location = New System.Drawing.Point(562, 144)
        Me.btnShowRootCourse.Name = "btnShowRootCourse"
        Me.btnShowRootCourse.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnShowRootCourse.Selectable = True
        Me.btnShowRootCourse.Size = New System.Drawing.Size(12, 12)
        Me.btnShowRootCourse.TabIndex = 41
        Me.btnShowRootCourse.tbAdriftIconWhenHover = False
        Me.btnShowRootCourse.tbAutoSize = False
        Me.btnShowRootCourse.tbAutoSizeEx = True
        Me.btnShowRootCourse.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_3_goto
        Me.btnShowRootCourse.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnShowRootCourse.tbBadgeNumber = 0
        Me.btnShowRootCourse.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnShowRootCourse.tbEndEllipsis = False
        Me.btnShowRootCourse.tbIconHoldPlace = True
        Me.btnShowRootCourse.tbIconImage = Nothing
        Me.btnShowRootCourse.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnShowRootCourse.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnShowRootCourse.tbIconMore = False
        Me.btnShowRootCourse.tbIconMouseDown = Nothing
        Me.btnShowRootCourse.tbIconMouseHover = Nothing
        Me.btnShowRootCourse.tbIconMouseLeave = Nothing
        Me.btnShowRootCourse.tbIconPlaceText = 2
        Me.btnShowRootCourse.tbIconReadOnly = Nothing
        Me.btnShowRootCourse.tbImageMouseDown = Nothing
        Me.btnShowRootCourse.tbImageMouseHover = Nothing
        Me.btnShowRootCourse.tbImageMouseLeave = Nothing
        Me.btnShowRootCourse.tbProgressValue = 50
        Me.btnShowRootCourse.tbReadOnly = False
        Me.btnShowRootCourse.tbReadOnlyText = False
        Me.btnShowRootCourse.tbShadow = False
        Me.btnShowRootCourse.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnShowRootCourse.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnShowRootCourse.tbShowDot = False
        Me.btnShowRootCourse.tbShowMoreIconImg = CType(resources.GetObject("btnShowRootCourse.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnShowRootCourse.tbShowNew = False
        Me.btnShowRootCourse.tbShowProgress = False
        Me.btnShowRootCourse.tbShowTip = True
        Me.btnShowRootCourse.tbShowToolTipOnButton = False
        Me.btnShowRootCourse.tbSplit = "0,0,0,0"
        Me.btnShowRootCourse.tbText = ""
        Me.btnShowRootCourse.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnShowRootCourse.tbTextColor = System.Drawing.Color.White
        Me.btnShowRootCourse.tbTextColorDisable = System.Drawing.Color.White
        Me.btnShowRootCourse.tbTextColorDown = System.Drawing.Color.White
        Me.btnShowRootCourse.tbTextColorHover = System.Drawing.Color.White
        Me.btnShowRootCourse.tbTextMouseDownPlace = 0
        Me.btnShowRootCourse.tbToolTip = ""
        Me.btnShowRootCourse.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnShowRootCourse.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnShowRootCourse.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnShowRootCourse.VisibleEx = True
        '
        'picError
        '
        Me.picError.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picError.Image = Global.iTong.My.Resources.Resources.weixin_error
        Me.picError.Location = New System.Drawing.Point(180, 2)
        Me.picError.Name = "picError"
        Me.picError.Size = New System.Drawing.Size(70, 69)
        Me.picError.TabIndex = 32
        Me.picError.TabStop = False
        Me.picError.Visible = False
        '
        'lblWeixinTitle
        '
        Me.lblWeixinTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinTitle.AutoSize = True
        Me.lblWeixinTitle.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold)
        Me.lblWeixinTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(85, Byte), Integer), CType(CType(85, Byte), Integer), CType(CType(85, Byte), Integer))
        Me.lblWeixinTitle.Location = New System.Drawing.Point(280, 13)
        Me.lblWeixinTitle.Name = "lblWeixinTitle"
        Me.lblWeixinTitle.Size = New System.Drawing.Size(119, 16)
        Me.lblWeixinTitle.TabIndex = 27
        Me.lblWeixinTitle.Text = "尚未安装 微信"
        '
        'btnDownloadWeixin
        '
        Me.btnDownloadWeixin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnDownloadWeixin.BackColor = System.Drawing.Color.Transparent
        Me.btnDownloadWeixin.BindingForm = Nothing
        Me.btnDownloadWeixin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDownloadWeixin.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDownloadWeixin.Location = New System.Drawing.Point(562, 95)
        Me.btnDownloadWeixin.Name = "btnDownloadWeixin"
        Me.btnDownloadWeixin.Padding = New System.Windows.Forms.Padding(3, 0, 3, 0)
        Me.btnDownloadWeixin.Selectable = True
        Me.btnDownloadWeixin.Size = New System.Drawing.Size(12, 12)
        Me.btnDownloadWeixin.TabIndex = 31
        Me.btnDownloadWeixin.tbAdriftIconWhenHover = False
        Me.btnDownloadWeixin.tbAutoSize = False
        Me.btnDownloadWeixin.tbAutoSizeEx = True
        Me.btnDownloadWeixin.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_3_goto
        Me.btnDownloadWeixin.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btnDownloadWeixin.tbBadgeNumber = 0
        Me.btnDownloadWeixin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDownloadWeixin.tbEndEllipsis = False
        Me.btnDownloadWeixin.tbIconHoldPlace = True
        Me.btnDownloadWeixin.tbIconImage = Nothing
        Me.btnDownloadWeixin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDownloadWeixin.tbIconMore = False
        Me.btnDownloadWeixin.tbIconMouseDown = Nothing
        Me.btnDownloadWeixin.tbIconMouseHover = Nothing
        Me.btnDownloadWeixin.tbIconMouseLeave = Nothing
        Me.btnDownloadWeixin.tbIconPlaceText = 2
        Me.btnDownloadWeixin.tbIconReadOnly = Nothing
        Me.btnDownloadWeixin.tbImageMouseDown = Nothing
        Me.btnDownloadWeixin.tbImageMouseHover = Nothing
        Me.btnDownloadWeixin.tbImageMouseLeave = Nothing
        Me.btnDownloadWeixin.tbProgressValue = 50
        Me.btnDownloadWeixin.tbReadOnly = False
        Me.btnDownloadWeixin.tbReadOnlyText = False
        Me.btnDownloadWeixin.tbShadow = False
        Me.btnDownloadWeixin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDownloadWeixin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDownloadWeixin.tbShowDot = False
        Me.btnDownloadWeixin.tbShowMoreIconImg = CType(resources.GetObject("btnDownloadWeixin.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDownloadWeixin.tbShowNew = False
        Me.btnDownloadWeixin.tbShowProgress = False
        Me.btnDownloadWeixin.tbShowTip = True
        Me.btnDownloadWeixin.tbShowToolTipOnButton = False
        Me.btnDownloadWeixin.tbSplit = "0,0,0,0"
        Me.btnDownloadWeixin.tbText = ""
        Me.btnDownloadWeixin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.tbTextColor = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorDisable = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorDown = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextColorHover = System.Drawing.Color.White
        Me.btnDownloadWeixin.tbTextMouseDownPlace = 0
        Me.btnDownloadWeixin.tbToolTip = ""
        Me.btnDownloadWeixin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDownloadWeixin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDownloadWeixin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDownloadWeixin.VisibleEx = True
        '
        'lblDownloadWeixin
        '
        Me.lblDownloadWeixin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDownloadWeixin.BackColor = System.Drawing.Color.Transparent
        Me.lblDownloadWeixin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblDownloadWeixin.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblDownloadWeixin.Location = New System.Drawing.Point(419, 92)
        Me.lblDownloadWeixin.Name = "lblDownloadWeixin"
        Me.lblDownloadWeixin.Size = New System.Drawing.Size(138, 18)
        Me.lblDownloadWeixin.TabIndex = 29
        Me.lblDownloadWeixin.Text = "立即免费获取微信"
        Me.lblDownloadWeixin.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblWeixinDescription_1
        '
        Me.lblWeixinDescription_1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinDescription_1.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWeixinDescription_1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer))
        Me.lblWeixinDescription_1.Location = New System.Drawing.Point(311, 118)
        Me.lblWeixinDescription_1.Name = "lblWeixinDescription_1"
        Me.lblWeixinDescription_1.Size = New System.Drawing.Size(332, 18)
        Me.lblWeixinDescription_1.TabIndex = 30
        Me.lblWeixinDescription_1.Text = "2、优先尝试取消设备中第三方应用限制访问权限后重试"
        Me.lblWeixinDescription_1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblShowRootCourse
        '
        Me.lblShowRootCourse.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblShowRootCourse.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblShowRootCourse.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(155, Byte), Integer), CType(CType(214, Byte), Integer))
        Me.lblShowRootCourse.Location = New System.Drawing.Point(421, 140)
        Me.lblShowRootCourse.Name = "lblShowRootCourse"
        Me.lblShowRootCourse.Size = New System.Drawing.Size(135, 18)
        Me.lblShowRootCourse.TabIndex = 31
        Me.lblShowRootCourse.Text = "查看Root教程"
        Me.lblShowRootCourse.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'picWeixinIcon
        '
        Me.picWeixinIcon.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picWeixinIcon.Image = Global.iTong.My.Resources.Resources.file_weixin_large
        Me.picWeixinIcon.Location = New System.Drawing.Point(142, -3)
        Me.picWeixinIcon.Name = "picWeixinIcon"
        Me.picWeixinIcon.Size = New System.Drawing.Size(114, 114)
        Me.picWeixinIcon.TabIndex = 26
        Me.picWeixinIcon.TabStop = False
        '
        'lblWeixinDescription
        '
        Me.lblWeixinDescription.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblWeixinDescription.BackColor = System.Drawing.Color.Transparent
        Me.lblWeixinDescription.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWeixinDescription.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer))
        Me.lblWeixinDescription.Location = New System.Drawing.Point(311, 68)
        Me.lblWeixinDescription.Name = "lblWeixinDescription"
        Me.lblWeixinDescription.Size = New System.Drawing.Size(373, 16)
        Me.lblWeixinDescription.TabIndex = 28
        Me.lblWeixinDescription.Text = "请您点击""立即免费获取微信""后，再继续使用该功能。"
        Me.lblWeixinDescription.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlNoneContents
        '
        Me.pnlNoneContents.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlNoneContents.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlNoneContents.Controls.Add(Me.lblNoneWeixin)
        Me.pnlNoneContents.Controls.Add(Me.btnReCheckNone)
        Me.pnlNoneContents.Controls.Add(Me.picNoneIcon)
        Me.pnlNoneContents.Location = New System.Drawing.Point(195, 3)
        Me.pnlNoneContents.Name = "pnlNoneContents"
        Me.pnlNoneContents.Size = New System.Drawing.Size(566, 419)
        Me.pnlNoneContents.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlNoneContents.TabIndex = 33
        Me.pnlNoneContents.tbBackgroundImage = Nothing
        Me.pnlNoneContents.tbShowWatermark = False
        Me.pnlNoneContents.tbSplit = "0,0,0,0"
        Me.pnlNoneContents.tbWatermark = Nothing
        Me.pnlNoneContents.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlNoneContents.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblNoneWeixin
        '
        Me.lblNoneWeixin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNoneWeixin.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblNoneWeixin.ForeColor = System.Drawing.Color.FromArgb(CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer), CType(CType(161, Byte), Integer))
        Me.lblNoneWeixin.Location = New System.Drawing.Point(150, 180)
        Me.lblNoneWeixin.Name = "lblNoneWeixin"
        Me.lblNoneWeixin.Size = New System.Drawing.Size(373, 30)
        Me.lblNoneWeixin.TabIndex = 28
        Me.lblNoneWeixin.Text = "无聊天记录"
        '
        'btnReCheckNone
        '
        Me.btnReCheckNone.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnReCheckNone.BackColor = System.Drawing.Color.Transparent
        Me.btnReCheckNone.BindingForm = Nothing
        Me.btnReCheckNone.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnReCheckNone.Location = New System.Drawing.Point(236, 220)
        Me.btnReCheckNone.Name = "btnReCheckNone"
        Me.btnReCheckNone.Padding = New System.Windows.Forms.Padding(10, 1, 10, 0)
        Me.btnReCheckNone.Selectable = True
        Me.btnReCheckNone.Size = New System.Drawing.Size(73, 28)
        Me.btnReCheckNone.TabIndex = 40
        Me.btnReCheckNone.tbAdriftIconWhenHover = False
        Me.btnReCheckNone.tbAutoSize = True
        Me.btnReCheckNone.tbAutoSizeEx = True
        Me.btnReCheckNone.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnReCheckNone.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnReCheckNone.tbBadgeNumber = 0
        Me.btnReCheckNone.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReCheckNone.tbEndEllipsis = False
        Me.btnReCheckNone.tbIconHoldPlace = True
        Me.btnReCheckNone.tbIconImage = Nothing
        Me.btnReCheckNone.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckNone.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReCheckNone.tbIconMore = False
        Me.btnReCheckNone.tbIconMouseDown = Nothing
        Me.btnReCheckNone.tbIconMouseHover = Nothing
        Me.btnReCheckNone.tbIconMouseLeave = Nothing
        Me.btnReCheckNone.tbIconPlaceText = 2
        Me.btnReCheckNone.tbIconReadOnly = Nothing
        Me.btnReCheckNone.tbImageMouseDown = Nothing
        Me.btnReCheckNone.tbImageMouseHover = Nothing
        Me.btnReCheckNone.tbImageMouseLeave = Nothing
        Me.btnReCheckNone.tbProgressValue = 50
        Me.btnReCheckNone.tbReadOnly = False
        Me.btnReCheckNone.tbReadOnlyText = False
        Me.btnReCheckNone.tbShadow = False
        Me.btnReCheckNone.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnReCheckNone.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnReCheckNone.tbShowDot = False
        Me.btnReCheckNone.tbShowMoreIconImg = CType(resources.GetObject("btnReCheckNone.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReCheckNone.tbShowNew = False
        Me.btnReCheckNone.tbShowProgress = False
        Me.btnReCheckNone.tbShowTip = True
        Me.btnReCheckNone.tbShowToolTipOnButton = False
        Me.btnReCheckNone.tbSplit = "13,11,13,11"
        Me.btnReCheckNone.tbText = "重新检查"
        Me.btnReCheckNone.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckNone.tbTextColor = System.Drawing.Color.White
        Me.btnReCheckNone.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReCheckNone.tbTextColorDown = System.Drawing.Color.White
        Me.btnReCheckNone.tbTextColorHover = System.Drawing.Color.White
        Me.btnReCheckNone.tbTextMouseDownPlace = 0
        Me.btnReCheckNone.tbToolTip = ""
        Me.btnReCheckNone.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReCheckNone.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReCheckNone.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReCheckNone.VisibleEx = True
        '
        'picNoneIcon
        '
        Me.picNoneIcon.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picNoneIcon.Image = Global.iTong.My.Resources.Resources.weixin_error
        Me.picNoneIcon.Location = New System.Drawing.Point(65, 152)
        Me.picNoneIcon.Name = "picNoneIcon"
        Me.picNoneIcon.Size = New System.Drawing.Size(70, 69)
        Me.picNoneIcon.TabIndex = 26
        Me.picNoneIcon.TabStop = False
        '
        'pnlWelcome
        '
        Me.pnlWelcome.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlWelcome.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlWelcome.Controls.Add(Me.btnNext)
        Me.pnlWelcome.Controls.Add(Me.lblWelcome)
        Me.pnlWelcome.Controls.Add(Me.lblWelcome2)
        Me.pnlWelcome.Controls.Add(Me.lblWelcome1)
        Me.pnlWelcome.Controls.Add(Me.picWelcome)
        Me.pnlWelcome.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.pnlWelcome.Location = New System.Drawing.Point(5, 112)
        Me.pnlWelcome.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlWelcome.Name = "pnlWelcome"
        Me.pnlWelcome.Size = New System.Drawing.Size(63, 450)
        Me.pnlWelcome.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlWelcome.TabIndex = 10
        Me.pnlWelcome.tbBackgroundImage = Nothing
        Me.pnlWelcome.tbShowWatermark = False
        Me.pnlWelcome.tbSplit = "0,0,0,0"
        Me.pnlWelcome.tbWatermark = Nothing
        Me.pnlWelcome.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlWelcome.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnNext
        '
        Me.btnNext.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.btnNext.BackColor = System.Drawing.Color.Transparent
        Me.btnNext.BindingForm = Nothing
        Me.btnNext.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnNext.Location = New System.Drawing.Point(338, 499)
        Me.btnNext.Name = "btnNext"
        Me.btnNext.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnNext.Selectable = True
        Me.btnNext.Size = New System.Drawing.Size(85, 35)
        Me.btnNext.TabIndex = 29
        Me.btnNext.tbAdriftIconWhenHover = False
        Me.btnNext.tbAutoSize = False
        Me.btnNext.tbAutoSizeEx = False
        Me.btnNext.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnNext.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnNext.tbBadgeNumber = 0
        Me.btnNext.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnNext.tbEndEllipsis = False
        Me.btnNext.tbIconHoldPlace = True
        Me.btnNext.tbIconImage = Nothing
        Me.btnNext.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNext.tbIconImageState = iTong.Components.ImageState.ThreeState
        Me.btnNext.tbIconMore = False
        Me.btnNext.tbIconMouseDown = Nothing
        Me.btnNext.tbIconMouseHover = Nothing
        Me.btnNext.tbIconMouseLeave = Nothing
        Me.btnNext.tbIconPlaceText = 2
        Me.btnNext.tbIconReadOnly = Nothing
        Me.btnNext.tbImageMouseDown = Nothing
        Me.btnNext.tbImageMouseHover = Nothing
        Me.btnNext.tbImageMouseLeave = Nothing
        Me.btnNext.tbProgressValue = 50
        Me.btnNext.tbReadOnly = False
        Me.btnNext.tbReadOnlyText = False
        Me.btnNext.tbShadow = False
        Me.btnNext.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnNext.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnNext.tbShowDot = False
        Me.btnNext.tbShowMoreIconImg = CType(resources.GetObject("btnNext.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnNext.tbShowNew = False
        Me.btnNext.tbShowProgress = False
        Me.btnNext.tbShowTip = True
        Me.btnNext.tbShowToolTipOnButton = False
        Me.btnNext.tbSplit = "5,5,5,5"
        Me.btnNext.tbText = "下一步"
        Me.btnNext.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNext.tbTextColor = System.Drawing.Color.White
        Me.btnNext.tbTextColorDisable = System.Drawing.Color.White
        Me.btnNext.tbTextColorDown = System.Drawing.Color.White
        Me.btnNext.tbTextColorHover = System.Drawing.Color.White
        Me.btnNext.tbTextMouseDownPlace = 0
        Me.btnNext.tbToolTip = ""
        Me.btnNext.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnNext.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnNext.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnNext.VisibleEx = True
        '
        'lblWelcome
        '
        Me.lblWelcome.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblWelcome.AutoSize = True
        Me.lblWelcome.Font = New System.Drawing.Font("宋体", 21.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWelcome.ForeColor = System.Drawing.Color.Black
        Me.lblWelcome.Location = New System.Drawing.Point(-130, 346)
        Me.lblWelcome.Name = "lblWelcome"
        Me.lblWelcome.Size = New System.Drawing.Size(253, 29)
        Me.lblWelcome.TabIndex = 28
        Me.lblWelcome.Text = "查看微信消息记录"
        '
        'lblWelcome2
        '
        Me.lblWelcome2.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblWelcome2.AutoSize = True
        Me.lblWelcome2.Font = New System.Drawing.Font("宋体", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWelcome2.ForeColor = System.Drawing.Color.Black
        Me.lblWelcome2.Location = New System.Drawing.Point(-150, 432)
        Me.lblWelcome2.Name = "lblWelcome2"
        Me.lblWelcome2.Size = New System.Drawing.Size(286, 24)
        Me.lblWelcome2.TabIndex = 28
        Me.lblWelcome2.Text = "·支持微信3.5以上版本。"
        '
        'lblWelcome1
        '
        Me.lblWelcome1.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblWelcome1.AutoSize = True
        Me.lblWelcome1.Font = New System.Drawing.Font("宋体", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblWelcome1.ForeColor = System.Drawing.Color.Black
        Me.lblWelcome1.Location = New System.Drawing.Point(-150, 392)
        Me.lblWelcome1.Name = "lblWelcome1"
        Me.lblWelcome1.Size = New System.Drawing.Size(322, 24)
        Me.lblWelcome1.TabIndex = 28
        Me.lblWelcome1.Text = "·支持搜索和导出聊天记录；"
        '
        'picWelcome
        '
        Me.picWelcome.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.picWelcome.BackgroundImage = Global.iTong.My.Resources.Resources.weixin_welcome
        Me.picWelcome.Image = Nothing
        Me.picWelcome.Location = New System.Drawing.Point(-404, 0)
        Me.picWelcome.Margin = New System.Windows.Forms.Padding(0)
        Me.picWelcome.Name = "picWelcome"
        Me.picWelcome.Size = New System.Drawing.Size(870, 315)
        Me.picWelcome.TabIndex = 0
        Me.picWelcome.TabStop = False
        Me.picWelcome.tbAutoSize = False
        Me.picWelcome.tbBackgroundImage = Nothing
        Me.picWelcome.tbSplit = "0,0,0,0"
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(172, 608)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(527, 18)
        Me.lblState.TabIndex = 29
        Me.lblState.tbAdriftWhenHover = False
        Me.lblState.tbAutoEllipsis = False
        Me.lblState.tbAutoSize = False
        Me.lblState.tbHideImage = False
        Me.lblState.tbIconImage = Nothing
        Me.lblState.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblState.tbIconPlaceText = 5
        Me.lblState.tbShadow = False
        Me.lblState.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblState.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblState.tbShowScrolling = False
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'cmsExport
        '
        Me.cmsExport.AccessibleDescription = "161x136"
        Me.cmsExport.DropShadowEnabled = False
        Me.cmsExport.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsExport.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiTxt, Me.tsmiExcel, Me.tsmiPic, Me.tsmiAudio, Me.tsmiVideo})
        Me.cmsExport.Name = "munOperate"
        Me.cmsExport.Size = New System.Drawing.Size(161, 114)
        Me.cmsExport.tbBackColor = System.Drawing.Color.White
        Me.cmsExport.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsExport.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsExport.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiTxt
        '
        Me.tsmiTxt.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiTxtCurrentFriend, Me.tsmiTxtCurrentFriendTime, Me.tsmiTxtAll})
        Me.tsmiTxt.Name = "tsmiTxt"
        Me.tsmiTxt.Size = New System.Drawing.Size(160, 22)
        Me.tsmiTxt.Text = "导出为Txt文件"
        '
        'tsmiTxtCurrentFriend
        '
        Me.tsmiTxtCurrentFriend.Name = "tsmiTxtCurrentFriend"
        Me.tsmiTxtCurrentFriend.Size = New System.Drawing.Size(208, 22)
        Me.tsmiTxtCurrentFriend.Text = "导出{0}消息记录"
        '
        'tsmiTxtCurrentFriendTime
        '
        Me.tsmiTxtCurrentFriendTime.Name = "tsmiTxtCurrentFriendTime"
        Me.tsmiTxtCurrentFriendTime.Size = New System.Drawing.Size(208, 22)
        Me.tsmiTxtCurrentFriendTime.Text = "导出{0}当前浏览时段记录"
        '
        'tsmiTxtAll
        '
        Me.tsmiTxtAll.Name = "tsmiTxtAll"
        Me.tsmiTxtAll.Size = New System.Drawing.Size(208, 22)
        Me.tsmiTxtAll.Text = "导出全部消息记录"
        '
        'tsmiExcel
        '
        Me.tsmiExcel.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiExcelCurrentFriend, Me.tsmiExcelCurrentFriendTime, Me.tsmiExcelAll})
        Me.tsmiExcel.Name = "tsmiExcel"
        Me.tsmiExcel.Size = New System.Drawing.Size(160, 22)
        Me.tsmiExcel.Text = "导出为Excel文件"
        '
        'tsmiExcelCurrentFriend
        '
        Me.tsmiExcelCurrentFriend.Name = "tsmiExcelCurrentFriend"
        Me.tsmiExcelCurrentFriend.Size = New System.Drawing.Size(208, 22)
        Me.tsmiExcelCurrentFriend.Text = "导出{0}消息记录"
        '
        'tsmiExcelCurrentFriendTime
        '
        Me.tsmiExcelCurrentFriendTime.Name = "tsmiExcelCurrentFriendTime"
        Me.tsmiExcelCurrentFriendTime.Size = New System.Drawing.Size(208, 22)
        Me.tsmiExcelCurrentFriendTime.Text = "导出{0}当前浏览时段记录"
        '
        'tsmiExcelAll
        '
        Me.tsmiExcelAll.Name = "tsmiExcelAll"
        Me.tsmiExcelAll.Size = New System.Drawing.Size(208, 22)
        Me.tsmiExcelAll.Text = "导出全部消息记录"
        '
        'tsmiPic
        '
        Me.tsmiPic.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiPicCurrentFriend, Me.tsmiPicAll})
        Me.tsmiPic.Name = "tsmiPic"
        Me.tsmiPic.Size = New System.Drawing.Size(160, 22)
        Me.tsmiPic.Text = "导出图片"
        '
        'tsmiPicCurrentFriend
        '
        Me.tsmiPicCurrentFriend.Name = "tsmiPicCurrentFriend"
        Me.tsmiPicCurrentFriend.Size = New System.Drawing.Size(142, 22)
        Me.tsmiPicCurrentFriend.Text = "导出{0}图片"
        '
        'tsmiPicAll
        '
        Me.tsmiPicAll.Name = "tsmiPicAll"
        Me.tsmiPicAll.Size = New System.Drawing.Size(142, 22)
        Me.tsmiPicAll.Text = "导出全部图片"
        '
        'tsmiAudio
        '
        Me.tsmiAudio.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiAudioCurrentFriend, Me.tsmiAudioAll})
        Me.tsmiAudio.Name = "tsmiAudio"
        Me.tsmiAudio.Size = New System.Drawing.Size(160, 22)
        Me.tsmiAudio.Text = "导出语音"
        '
        'tsmiAudioCurrentFriend
        '
        Me.tsmiAudioCurrentFriend.Name = "tsmiAudioCurrentFriend"
        Me.tsmiAudioCurrentFriend.Size = New System.Drawing.Size(142, 22)
        Me.tsmiAudioCurrentFriend.Text = "导出{0}语音"
        '
        'tsmiAudioAll
        '
        Me.tsmiAudioAll.Name = "tsmiAudioAll"
        Me.tsmiAudioAll.Size = New System.Drawing.Size(142, 22)
        Me.tsmiAudioAll.Text = "导出全部语音"
        '
        'tsmiVideo
        '
        Me.tsmiVideo.DropDownItems.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiVideoCurrentFriend, Me.tsmiVideoAll})
        Me.tsmiVideo.Name = "tsmiVideo"
        Me.tsmiVideo.Size = New System.Drawing.Size(160, 22)
        Me.tsmiVideo.Text = "导出小视频"
        '
        'tsmiVideoCurrentFriend
        '
        Me.tsmiVideoCurrentFriend.Name = "tsmiVideoCurrentFriend"
        Me.tsmiVideoCurrentFriend.Size = New System.Drawing.Size(154, 22)
        Me.tsmiVideoCurrentFriend.Text = "导出{0}小视频"
        '
        'tsmiVideoAll
        '
        Me.tsmiVideoAll.Name = "tsmiVideoAll"
        Me.tsmiVideoAll.Size = New System.Drawing.Size(154, 22)
        Me.tsmiVideoAll.Text = "导出全部小视频"
        '
        'cmsRestore
        '
        Me.cmsRestore.AccessibleDescription = "153x26"
        Me.cmsRestore.DropShadowEnabled = False
        Me.cmsRestore.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsRestore.Name = "munOperate"
        Me.cmsRestore.Size = New System.Drawing.Size(61, 4)
        Me.cmsRestore.tbBackColor = System.Drawing.Color.White
        Me.cmsRestore.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsRestore.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsRestore.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'lblCancelPwdBackup
        '
        Me.lblCancelPwdBackup.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblCancelPwdBackup.Location = New System.Drawing.Point(111, 68)
        Me.lblCancelPwdBackup.Name = "lblCancelPwdBackup"
        Me.lblCancelPwdBackup.Size = New System.Drawing.Size(710, 54)
        Me.lblCancelPwdBackup.TabIndex = 63
        Me.lblCancelPwdBackup.tbAdriftWhenHover = False
        Me.lblCancelPwdBackup.tbAutoEllipsis = False
        Me.lblCancelPwdBackup.tbAutoSize = False
        Me.lblCancelPwdBackup.tbHideImage = False
        Me.lblCancelPwdBackup.tbIconImage = Global.iTong.My.Resources.Resources.icon_attention1
        Me.lblCancelPwdBackup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCancelPwdBackup.tbIconPlaceText = 5
        Me.lblCancelPwdBackup.tbShadow = False
        Me.lblCancelPwdBackup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCancelPwdBackup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCancelPwdBackup.tbShowScrolling = False
        Me.lblCancelPwdBackup.Text = "TbLabel1"
        Me.lblCancelPwdBackup.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'cmsRepair
        '
        Me.cmsRepair.AccessibleDescription = "153x76"
        Me.cmsRepair.DropShadowEnabled = False
        Me.cmsRepair.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.cmsRepair.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiDelete, Me.tssRepair, Me.tsmiSave})
        Me.cmsRepair.Name = "munOperate"
        Me.cmsRepair.Size = New System.Drawing.Size(95, 54)
        Me.cmsRepair.tbBackColor = System.Drawing.Color.White
        Me.cmsRepair.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsRepair.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsRepair.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiDelete
        '
        Me.tsmiDelete.Name = "tsmiDelete"
        Me.tsmiDelete.Size = New System.Drawing.Size(94, 22)
        Me.tsmiDelete.Text = "删除"
        '
        'tssRepair
        '
        Me.tssRepair.Name = "tssRepair"
        Me.tssRepair.Size = New System.Drawing.Size(91, 6)
        '
        'tsmiSave
        '
        Me.tsmiSave.Name = "tsmiSave"
        Me.tsmiSave.Size = New System.Drawing.Size(94, 22)
        Me.tsmiSave.Text = "保存"
        '
        'pnlLoading_1
        '
        Me.pnlLoading_1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlLoading_1.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(245, Byte), Integer), CType(CType(248, Byte), Integer))
        Me.pnlLoading_1.Controls.Add(Me.lblLoading_1)
        Me.pnlLoading_1.Controls.Add(Me.picLoading_1)
        Me.pnlLoading_1.Location = New System.Drawing.Point(374, 253)
        Me.pnlLoading_1.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading_1.Name = "pnlLoading_1"
        Me.pnlLoading_1.Size = New System.Drawing.Size(396, 125)
        Me.pnlLoading_1.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlLoading_1.TabIndex = 8
        Me.pnlLoading_1.tbBackgroundImage = Nothing
        Me.pnlLoading_1.tbShowWatermark = False
        Me.pnlLoading_1.tbSplit = "0,0,0,0"
        Me.pnlLoading_1.tbWatermark = Nothing
        Me.pnlLoading_1.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlLoading_1.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblLoading_1
        '
        Me.lblLoading_1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading_1.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading_1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading_1.Location = New System.Drawing.Point(134, 41)
        Me.lblLoading_1.Name = "lblLoading_1"
        Me.lblLoading_1.Size = New System.Drawing.Size(185, 45)
        Me.lblLoading_1.TabIndex = 12
        Me.lblLoading_1.Text = "Loading..."
        Me.lblLoading_1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picLoading_1
        '
        Me.picLoading_1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picLoading_1.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.picLoading_1.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.picLoading_1.Location = New System.Drawing.Point(40, 16)
        Me.picLoading_1.Margin = New System.Windows.Forms.Padding(0)
        Me.picLoading_1.Name = "picLoading_1"
        Me.picLoading_1.Size = New System.Drawing.Size(91, 95)
        Me.picLoading_1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.picLoading_1.TabIndex = 11
        Me.picLoading_1.TabStop = False
        '
        'frmAndroidWeixin
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(206, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(215, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(870, 630)
        Me.Controls.Add(Me.lblState)
        Me.Controls.Add(Me.pnlWelcome)
        Me.Controls.Add(Me.pnlDescription)
        Me.Controls.Add(Me.tpnlContent)
        Me.Controls.Add(Me.pnlNoneContents)
        Me.Controls.Add(Me.pnlLoading)
        Me.Controls.Add(Me.pnlLoading_1)
        Me.MinimumSize = New System.Drawing.Size(870, 600)
        Me.Name = "frmAndroidWeixin"
        Me.tbGuiTitlemarkLocation = New System.Drawing.Point(0, 13)
        Me.tbShowTitleOnForm = True
        Me.Text = "微信消息记录"
        Me.Controls.SetChildIndex(Me.pnlLoading_1, 0)
        Me.Controls.SetChildIndex(Me.pnlLoading, 0)
        Me.Controls.SetChildIndex(Me.pnlNoneContents, 0)
        Me.Controls.SetChildIndex(Me.tpnlContent, 0)
        Me.Controls.SetChildIndex(Me.pnlDescription, 0)
        Me.Controls.SetChildIndex(Me.pnlWelcome, 0)
        Me.Controls.SetChildIndex(Me.lblState, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.tpnlContent.ResumeLayout(False)
        Me.pnlTop.ResumeLayout(False)
        Me.pnlTop.PerformLayout()
        Me.pnlSearchFriend.ResumeLayout(False)
        Me.pnlState.ResumeLayout(False)
        Me.pnlState.PerformLayout()
        Me.pnlTitle.ResumeLayout(False)
        Me.pnlContainer.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.picNoLockScreen, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDescription.ResumeLayout(False)
        Me.pnlDescription.PerformLayout()
        Me.pnlMethod2.ResumeLayout(False)
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlMethod1.ResumeLayout(False)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlWechatAssiant.ResumeLayout(False)
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picError, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picWeixinIcon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlNoneContents.ResumeLayout(False)
        CType(Me.picNoneIcon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlWelcome.ResumeLayout(False)
        Me.pnlWelcome.PerformLayout()
        CType(Me.picWelcome, System.ComponentModel.ISupportInitialize).EndInit()
        Me.cmsExport.ResumeLayout(False)
        Me.cmsRepair.ResumeLayout(False)
        Me.pnlLoading_1.ResumeLayout(False)
        CType(Me.picLoading_1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents tpnlContent As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents pnlFriend As tbPanel
    Friend WithEvents pnlLoading As tbPanel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Friend WithEvents wbsChat As tbWebBrowserEx
    Friend WithEvents pnlTitle As tbPanel
    Friend WithEvents lblTitle As System.Windows.Forms.Label
    Friend WithEvents dtpStart As System.Windows.Forms.DateTimePicker
    Friend WithEvents pnlState As tbPanel
    Friend WithEvents btnCopy As tbButton
    Friend WithEvents lblStartTime As System.Windows.Forms.Label
    Friend WithEvents btnDownloadWeixin As tbButton
    Friend WithEvents lblDownloadWeixin As System.Windows.Forms.Label
    Friend WithEvents lblShowRootCourse As System.Windows.Forms.Label
    Friend WithEvents lblWeixinDescription As System.Windows.Forms.Label
    Friend WithEvents lblWeixinDescription_1 As System.Windows.Forms.Label
    Friend WithEvents lblNoneWeixin As System.Windows.Forms.Label
    Friend WithEvents lblWeixinTitle As System.Windows.Forms.Label
    Friend WithEvents picWeixinIcon As System.Windows.Forms.PictureBox
    Friend WithEvents picNoneIcon As System.Windows.Forms.PictureBox
    Friend WithEvents lblState As tbLabel
    Friend WithEvents cmsExport As tbContextMenuStrip
    Friend WithEvents tsmiExcel As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExcelCurrentFriend As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTxt As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTxtCurrentFriend As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiTxtAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExcelAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlWelcome As tbPanel
    Friend WithEvents picWelcome As tbPictureBox
    Friend WithEvents lblWelcome As System.Windows.Forms.Label
    Friend WithEvents lblWelcome2 As System.Windows.Forms.Label
    Friend WithEvents lblWelcome1 As System.Windows.Forms.Label
    Friend WithEvents btnNext As tbButton
    Friend WithEvents lblSystemInfo As tbLabel
    Friend WithEvents pnlSearchFriend As tbPanel
    Friend WithEvents txtSearchFriend As tbSearch
    Friend WithEvents pnlTop As tbPanel
    Friend WithEvents cmbTime As System.Windows.Forms.ComboBox
    Friend WithEvents cmbFriend As System.Windows.Forms.ComboBox
    Friend WithEvents lblSearchRange As System.Windows.Forms.Label
    Friend WithEvents txtSearchChat As tbSearch
    Friend WithEvents btnExport As tbButton
    Friend WithEvents btnMoment As tbButton
    Friend WithEvents btnFavourite As tbButton
    Friend WithEvents btnRepair As tbButton
    Friend WithEvents btnOK As tbButton
    Friend WithEvents lblEndTime As System.Windows.Forms.Label
    Friend WithEvents dtpEnd As System.Windows.Forms.DateTimePicker
    Friend WithEvents btnRestore As tbButton
    Friend WithEvents btnBackup As tbButton
    Friend WithEvents cmsRestore As tbContextMenuStrip
    Friend WithEvents tsmiPic As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiPicCurrentFriend As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAudio As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAudioCurrentFriend As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiPicAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAudioAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiVideo As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiVideoCurrentFriend As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiVideoAll As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lblLoadingDescription As System.Windows.Forms.Label
    Friend WithEvents pnlDescription As tbPanel
    Friend WithEvents pnlNoneContents As tbPanel
    Friend WithEvents picError As System.Windows.Forms.PictureBox
    Friend WithEvents btnReCheckNone As tbButton
    Friend WithEvents picNoLockScreen As System.Windows.Forms.PictureBox
    Friend WithEvents lblCancelPwdBackup As iTong.Components.tbLabel
    Friend WithEvents cmsRepair As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tssRepair As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiSave As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnShowRootCourse As iTong.Components.tbButton
    Friend WithEvents pnlLoading_1 As iTong.Components.tbPanel
    Friend WithEvents lblLoading_1 As System.Windows.Forms.Label
    Friend WithEvents picLoading_1 As System.Windows.Forms.PictureBox
    Friend WithEvents lblReCheckEx As System.Windows.Forms.Label
    Friend WithEvents pnlMethod2 As iTong.Components.tbPanel
    Friend WithEvents pnlMethod1 As iTong.Components.tbPanel
    Friend WithEvents lblDescCacelLimit As System.Windows.Forms.Label
    Friend WithEvents lblDescRootAgain As System.Windows.Forms.Label
    Friend WithEvents btnStepRoot As iTong.Components.tbButton
    Friend WithEvents lblAbout As System.Windows.Forms.Label
    Friend WithEvents btnRefresh As iTong.Components.tbButton
    Friend WithEvents tsmiTxtCurrentFriendTime As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiExcelCurrentFriendTime As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents lblCacelLimit As System.Windows.Forms.Label
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents lblWechatMediaManage As System.Windows.Forms.Label
    Friend WithEvents btnManage As iTong.Components.tbButton
    Friend WithEvents lblHowtoRoot As System.Windows.Forms.Label
    Friend WithEvents PictureBox2 As System.Windows.Forms.PictureBox
    Friend WithEvents lblWechatTxtManage As System.Windows.Forms.Label
    Friend WithEvents pnlWechatAssiant As iTong.Components.tbPanel
    Friend WithEvents btnOpenWechatAssisant As iTong.Components.tbButton
    Friend WithEvents PictureBox3 As System.Windows.Forms.PictureBox
    Friend WithEvents lblWechatAssistant As System.Windows.Forms.Label
    Friend WithEvents lblDescWechatAssistant As System.Windows.Forms.Label
    Friend WithEvents lblTryRoot As System.Windows.Forms.Label
    Friend WithEvents lblNoRootMedthod As System.Windows.Forms.Label
End Class
