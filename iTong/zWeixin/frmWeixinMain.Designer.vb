﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeixinMain
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeixinMain))
        Me.tblayoutMain = New System.Windows.Forms.TableLayoutPanel()
        Me.pnliCloudDetail = New System.Windows.Forms.Panel()
        Me.pnlDeveloping = New iTong.Components.tbPanel()
        Me.lblCompatibility = New iTong.Components.tbLabel()
        Me.PictureBox3 = New System.Windows.Forms.PictureBox()
        Me.lblIncompatible = New iTong.Components.tbLabel()
        Me.pnliCloudLogin = New System.Windows.Forms.Panel()
        Me.pbShowPwd = New System.Windows.Forms.PictureBox()
        Me.PictureBox2 = New System.Windows.Forms.PictureBox()
        Me.lbliCloudfailture = New iTong.Components.tbLabel()
        Me.txtPwd = New iTong.Components.tbTextBox()
        Me.txtAppleId = New iTong.Components.tbTextBox()
        Me.btniCloudView = New iTong.Components.tbButton()
        Me.btniCloudPrivate = New iTong.Components.tbButton()
        Me.btniCloudForget = New iTong.Components.tbButton()
        Me.lbliCloudTip_2 = New System.Windows.Forms.Label()
        Me.lbliCloudTip_1 = New System.Windows.Forms.Label()
        Me.btniCloudLogin = New iTong.Components.tbButton()
        Me.pnliCloudNodata = New System.Windows.Forms.Panel()
        Me.lbliCloudWechat = New iTong.tbBattery()
        Me.piciCloudSearch = New System.Windows.Forms.PictureBox()
        Me.lbliCloudNodata = New iTong.Components.tbLabel()
        Me.btniCloudReturn = New iTong.Components.tbButton()
        Me.pnliCloudDownStatus = New System.Windows.Forms.Panel()
        Me.lbliCloudAcct = New iTong.Components.tbButton()
        Me.btniCloudLogout = New iTong.Components.tbButton()
        Me.lbliCloudView = New System.Windows.Forms.Label()
        Me.lbliCloudErrorServer = New System.Windows.Forms.Label()
        Me.pnlDownLoadiCloud = New iTong.Components.tbPanel()
        Me.lblPercentage = New System.Windows.Forms.Label()
        Me.lbliCloudWaitingMsg = New System.Windows.Forms.Label()
        Me.btniCloudCancel = New iTong.Components.tbButton()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.pnliCloudWaiting = New System.Windows.Forms.Panel()
        Me.lbliCloudLoading = New System.Windows.Forms.Label()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.pnliCloudGrid = New iTong.Components.tbPanel()
        Me.pnliCloudHead = New System.Windows.Forms.Panel()
        Me.TbButton8 = New iTong.Components.tbButton()
        Me.TbButton10 = New iTong.Components.tbButton()
        Me.TbButton11 = New iTong.Components.tbButton()
        Me.TbButton13 = New iTong.Components.tbButton()
        Me.TbButton12 = New iTong.Components.tbButton()
        Me.btnDevice_3 = New iTong.Components.tbButton()
        Me.btniCloud = New iTong.Components.tbButton()
        Me.btnBackup_3 = New iTong.Components.tbButton()
        Me.pnlContent = New System.Windows.Forms.Panel()
        Me.pnlHeadDevice = New System.Windows.Forms.Panel()
        Me.TbButton14 = New iTong.Components.tbButton()
        Me.TbButton7 = New iTong.Components.tbButton()
        Me.TbButton5 = New iTong.Components.tbButton()
        Me.TbButton1 = New iTong.Components.tbButton()
        Me.TbButton3 = New iTong.Components.tbButton()
        Me.btniCloud_1 = New iTong.Components.tbButton()
        Me.btnBackupEx = New iTong.Components.tbButton()
        Me.btnDeviceEx = New iTong.Components.tbButton()
        Me.pnlDevice = New System.Windows.Forms.Panel()
        Me.pnlTip = New iTong.Components.tbPanel()
        Me.TbLabel1 = New iTong.Components.tbLabel()
        Me.pnlDeviceConnect = New iTong.Components.tbPanel()
        Me.pbDevice = New System.Windows.Forms.PictureBox()
        Me.btnStartLoadDevice = New iTong.Components.tbButton()
        Me.lblBackupPwdMsg = New iTong.Components.tbLabel()
        Me.lblNameValue = New iTong.Components.tbLabel()
        Me.llblDeletePwd = New System.Windows.Forms.LinkLabel()
        Me.lblStartLoadDevice = New iTong.Components.tbLabel()
        Me.lblFirmwareVersion = New iTong.Components.tbLabel()
        Me.lblDeviceType = New iTong.Components.tbLabel()
        Me.lblDericeType = New iTong.Components.tbLabel()
        Me.pnlDeviceUnConnect = New System.Windows.Forms.Panel()
        Me.lblDevNoConn = New System.Windows.Forms.Label()
        Me.picDefault = New System.Windows.Forms.PictureBox()
        Me.pnlBackup = New System.Windows.Forms.Panel()
        Me.btnDeleteTemp = New iTong.Components.tbButton()
        Me.btnChangeSKU = New iTong.Components.tbButton()
        Me.btnImport = New iTong.Components.tbButton()
        Me.btnRepair = New iTong.Components.tbButton()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.btnWeChatDataMigration = New iTong.Components.tbButton()
        Me.btnBackupStart = New iTong.Components.tbButton()
        Me.pnlGridBackList = New iTong.Components.tbPanel()
        Me.pnlCount = New System.Windows.Forms.Panel()
        Me.lblStorageSpace = New iTong.Components.tbLabel()
        Me.lblTitle = New System.Windows.Forms.Label()
        Me.lblPrompt = New System.Windows.Forms.Label()
        Me.pnlFromBackup = New iTong.Components.tbPanel()
        Me.llblRefresh = New System.Windows.Forms.LinkLabel()
        Me.lblFromBackupMsg = New iTong.Components.tbLabel()
        Me.pnlDescribe = New iTong.Components.tbPanel()
        Me.btnRefreshEx = New iTong.Components.tbButton()
        Me.TbLabel3 = New iTong.Components.tbLabel()
        Me.pnlcharge = New iTong.Components.tbPanel()
        Me.RichTextBox1 = New System.Windows.Forms.RichTextBox()
        Me.TbLabel2 = New iTong.Components.tbLabel()
        Me.btnAdd = New iTong.Components.tbButton()
        Me.pnlHeadBackup = New System.Windows.Forms.Panel()
        Me.TbButton15 = New iTong.Components.tbButton()
        Me.TbButton9 = New iTong.Components.tbButton()
        Me.TbButton6 = New iTong.Components.tbButton()
        Me.TbButton2 = New iTong.Components.tbButton()
        Me.TbButton4 = New iTong.Components.tbButton()
        Me.btnDevice = New iTong.Components.tbButton()
        Me.btniCloud_2 = New iTong.Components.tbButton()
        Me.btnBackup = New iTong.Components.tbButton()
        Me.lblState = New iTong.Components.tbLabel()
        Me.munOperator = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiOpenFolder = New System.Windows.Forms.ToolStripMenuItem()
        Me.ToolStripSeparator1 = New System.Windows.Forms.ToolStripSeparator()
        Me.tsmiOpen = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiDelete = New System.Windows.Forms.ToolStripMenuItem()
        Me.btn_Setting = New iTong.Components.tbButton()
        Me.menuMain = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmSetting = New System.Windows.Forms.ToolStripMenuItem()
        Me.btnQQ = New iTong.Components.tbButton()
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.btnBuy = New iTong.Components.tbButton()
        Me.btnOpenVIP = New iTong.Components.tbButton()
        Me.cmsImport = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmiAllbackup = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiAloneBackup = New System.Windows.Forms.ToolStripMenuItem()
        Me.btnWeChat = New iTong.Components.tbButton()
        Me.tblayoutMain.SuspendLayout()
        Me.pnliCloudDetail.SuspendLayout()
        Me.pnlDeveloping.SuspendLayout()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnliCloudLogin.SuspendLayout()
        CType(Me.pbShowPwd, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnliCloudNodata.SuspendLayout()
        CType(Me.piciCloudSearch, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnliCloudDownStatus.SuspendLayout()
        Me.pnlDownLoadiCloud.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnliCloudWaiting.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnliCloudHead.SuspendLayout()
        Me.pnlHeadDevice.SuspendLayout()
        Me.pnlDevice.SuspendLayout()
        Me.pnlTip.SuspendLayout()
        Me.pnlDeviceConnect.SuspendLayout()
        CType(Me.pbDevice, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlDeviceUnConnect.SuspendLayout()
        CType(Me.picDefault, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlBackup.SuspendLayout()
        Me.pnlGridBackList.SuspendLayout()
        Me.pnlCount.SuspendLayout()
        Me.pnlFromBackup.SuspendLayout()
        Me.pnlDescribe.SuspendLayout()
        Me.pnlHeadBackup.SuspendLayout()
        Me.munOperator.SuspendLayout()
        Me.menuMain.SuspendLayout()
        Me.cmsImport.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(2825, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(2801, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(2777, 0)
        '
        'tblayoutMain
        '
        Me.tblayoutMain.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.tblayoutMain.ColumnCount = 5
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 0.0!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 0.0!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 33.33333!))
        Me.tblayoutMain.Controls.Add(Me.pnliCloudDetail, 4, 1)
        Me.tblayoutMain.Controls.Add(Me.pnliCloudHead, 4, 0)
        Me.tblayoutMain.Controls.Add(Me.pnlContent, 2, 0)
        Me.tblayoutMain.Controls.Add(Me.pnlHeadDevice, 0, 0)
        Me.tblayoutMain.Controls.Add(Me.pnlDevice, 0, 1)
        Me.tblayoutMain.Controls.Add(Me.pnlBackup, 1, 1)
        Me.tblayoutMain.Controls.Add(Me.pnlFromBackup, 0, 2)
        Me.tblayoutMain.Controls.Add(Me.pnlDescribe, 3, 0)
        Me.tblayoutMain.Controls.Add(Me.pnlHeadBackup, 1, 0)
        Me.tblayoutMain.Location = New System.Drawing.Point(1, 32)
        Me.tblayoutMain.Margin = New System.Windows.Forms.Padding(0)
        Me.tblayoutMain.Name = "tblayoutMain"
        Me.tblayoutMain.RowCount = 3
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 110.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 24.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.tblayoutMain.Size = New System.Drawing.Size(2847, 590)
        Me.tblayoutMain.TabIndex = 22
        '
        'pnliCloudDetail
        '
        Me.pnliCloudDetail.Controls.Add(Me.pnlDeveloping)
        Me.pnliCloudDetail.Controls.Add(Me.pnliCloudLogin)
        Me.pnliCloudDetail.Controls.Add(Me.pnliCloudNodata)
        Me.pnliCloudDetail.Controls.Add(Me.pnliCloudDownStatus)
        Me.pnliCloudDetail.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnliCloudDetail.Location = New System.Drawing.Point(1898, 110)
        Me.pnliCloudDetail.Margin = New System.Windows.Forms.Padding(0)
        Me.pnliCloudDetail.Name = "pnliCloudDetail"
        Me.pnliCloudDetail.Size = New System.Drawing.Size(949, 436)
        Me.pnliCloudDetail.TabIndex = 34
        '
        'pnlDeveloping
        '
        Me.pnlDeveloping.Controls.Add(Me.lblCompatibility)
        Me.pnlDeveloping.Controls.Add(Me.PictureBox3)
        Me.pnlDeveloping.Controls.Add(Me.lblIncompatible)
        Me.pnlDeveloping.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlDeveloping.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.pnlDeveloping.Location = New System.Drawing.Point(0, 0)
        Me.pnlDeveloping.Name = "pnlDeveloping"
        Me.pnlDeveloping.Size = New System.Drawing.Size(949, 436)
        Me.pnlDeveloping.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDeveloping.TabIndex = 81
        Me.pnlDeveloping.tbBackgroundImage = Nothing
        Me.pnlDeveloping.tbShowWatermark = False
        Me.pnlDeveloping.tbSplit = "0,0,0,0"
        Me.pnlDeveloping.tbWatermark = Nothing
        Me.pnlDeveloping.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDeveloping.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlDeveloping.Visible = False
        '
        'lblCompatibility
        '
        Me.lblCompatibility.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblCompatibility.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblCompatibility.Location = New System.Drawing.Point(28, 313)
        Me.lblCompatibility.Name = "lblCompatibility"
        Me.lblCompatibility.Size = New System.Drawing.Size(892, 30)
        Me.lblCompatibility.TabIndex = 3
        Me.lblCompatibility.tbAdriftWhenHover = False
        Me.lblCompatibility.tbAutoEllipsis = False
        Me.lblCompatibility.tbAutoSize = False
        Me.lblCompatibility.tbHideImage = False
        Me.lblCompatibility.tbIconImage = Nothing
        Me.lblCompatibility.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCompatibility.tbIconPlaceText = 5
        Me.lblCompatibility.tbShadow = False
        Me.lblCompatibility.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCompatibility.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCompatibility.tbShowScrolling = False
        Me.lblCompatibility.Text = "建议您使用Win7或更高版本的系统，XP兼容版敬请期待。"
        Me.lblCompatibility.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox3
        '
        Me.PictureBox3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.PictureBox3.Image = Global.iTong.My.Resources.Resources.icloud_Incompatible
        Me.PictureBox3.Location = New System.Drawing.Point(406, 93)
        Me.PictureBox3.Name = "PictureBox3"
        Me.PictureBox3.Size = New System.Drawing.Size(163, 137)
        Me.PictureBox3.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBox3.TabIndex = 2
        Me.PictureBox3.TabStop = False
        '
        'lblIncompatible
        '
        Me.lblIncompatible.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblIncompatible.Font = New System.Drawing.Font("宋体", 14.5!, System.Drawing.FontStyle.Bold)
        Me.lblIncompatible.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblIncompatible.Location = New System.Drawing.Point(29, 269)
        Me.lblIncompatible.Name = "lblIncompatible"
        Me.lblIncompatible.Size = New System.Drawing.Size(890, 38)
        Me.lblIncompatible.TabIndex = 0
        Me.lblIncompatible.tbAdriftWhenHover = False
        Me.lblIncompatible.tbAutoEllipsis = False
        Me.lblIncompatible.tbAutoSize = False
        Me.lblIncompatible.tbHideImage = False
        Me.lblIncompatible.tbIconImage = Nothing
        Me.lblIncompatible.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblIncompatible.tbIconPlaceText = 5
        Me.lblIncompatible.tbShadow = False
        Me.lblIncompatible.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblIncompatible.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblIncompatible.tbShowScrolling = False
        Me.lblIncompatible.Text = "该功能暂不支持在XP系统上使用"
        Me.lblIncompatible.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnliCloudLogin
        '
        Me.pnliCloudLogin.Controls.Add(Me.pbShowPwd)
        Me.pnliCloudLogin.Controls.Add(Me.PictureBox2)
        Me.pnliCloudLogin.Controls.Add(Me.lbliCloudfailture)
        Me.pnliCloudLogin.Controls.Add(Me.txtPwd)
        Me.pnliCloudLogin.Controls.Add(Me.txtAppleId)
        Me.pnliCloudLogin.Controls.Add(Me.btniCloudView)
        Me.pnliCloudLogin.Controls.Add(Me.btniCloudPrivate)
        Me.pnliCloudLogin.Controls.Add(Me.btniCloudForget)
        Me.pnliCloudLogin.Controls.Add(Me.lbliCloudTip_2)
        Me.pnliCloudLogin.Controls.Add(Me.lbliCloudTip_1)
        Me.pnliCloudLogin.Controls.Add(Me.btniCloudLogin)
        Me.pnliCloudLogin.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnliCloudLogin.Location = New System.Drawing.Point(0, 0)
        Me.pnliCloudLogin.Name = "pnliCloudLogin"
        Me.pnliCloudLogin.Size = New System.Drawing.Size(949, 436)
        Me.pnliCloudLogin.TabIndex = 80
        Me.pnliCloudLogin.Visible = False
        '
        'pbShowPwd
        '
        Me.pbShowPwd.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbShowPwd.BackColor = System.Drawing.Color.Transparent
        Me.pbShowPwd.Cursor = System.Windows.Forms.Cursors.Hand
        Me.pbShowPwd.Image = Global.iTong.My.Resources.Resources.btn_closedeyenew
        Me.pbShowPwd.Location = New System.Drawing.Point(599, 217)
        Me.pbShowPwd.Margin = New System.Windows.Forms.Padding(0)
        Me.pbShowPwd.Name = "pbShowPwd"
        Me.pbShowPwd.Size = New System.Drawing.Size(24, 22)
        Me.pbShowPwd.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.pbShowPwd.TabIndex = 64
        Me.pbShowPwd.TabStop = False
        '
        'PictureBox2
        '
        Me.PictureBox2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.PictureBox2.Image = Global.iTong.My.Resources.Resources.icloud
        Me.PictureBox2.Location = New System.Drawing.Point(400, 26)
        Me.PictureBox2.Name = "PictureBox2"
        Me.PictureBox2.Size = New System.Drawing.Size(148, 94)
        Me.PictureBox2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBox2.TabIndex = 59
        Me.PictureBox2.TabStop = False
        '
        'lbliCloudfailture
        '
        Me.lbliCloudfailture.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lbliCloudfailture.AutoSize = True
        Me.lbliCloudfailture.Cursor = System.Windows.Forms.Cursors.Default
        Me.lbliCloudfailture.ForeColor = System.Drawing.Color.Red
        Me.lbliCloudfailture.Location = New System.Drawing.Point(317, 280)
        Me.lbliCloudfailture.Name = "lbliCloudfailture"
        Me.lbliCloudfailture.Size = New System.Drawing.Size(485, 12)
        Me.lbliCloudfailture.TabIndex = 58
        Me.lbliCloudfailture.tbAdriftWhenHover = False
        Me.lbliCloudfailture.tbAutoEllipsis = False
        Me.lbliCloudfailture.tbAutoSize = True
        Me.lbliCloudfailture.tbHideImage = False
        Me.lbliCloudfailture.tbIconImage = Nothing
        Me.lbliCloudfailture.tbIconImageAlign = System.Drawing.ContentAlignment.TopLeft
        Me.lbliCloudfailture.tbIconPlaceText = 5
        Me.lbliCloudfailture.tbShadow = False
        Me.lbliCloudfailture.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lbliCloudfailture.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lbliCloudfailture.tbShowScrolling = False
        Me.lbliCloudfailture.Text = "身份验证失败！因为您在iCloud上开启了两步验证或双重验证功能，请关掉它，再试一次。"
        Me.lbliCloudfailture.Visible = False
        '
        'txtPwd
        '
        Me.txtPwd.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtPwd.BackColor = System.Drawing.Color.White
        Me.txtPwd.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtPwd.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtPwd.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtPwd.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtPwd.ForeColor = System.Drawing.Color.Black
        Me.txtPwd.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtPwd.Location = New System.Drawing.Point(319, 205)
        Me.txtPwd.MaxLength = 32767
        Me.txtPwd.Name = "txtPwd"
        Me.txtPwd.Size = New System.Drawing.Size(310, 44)
        Me.txtPwd.TabIndex = 56
        Me.txtPwd.Tag = Nothing
        Me.txtPwd.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtPwd.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtPwd.tbSelMark = True
        Me.txtPwd.tbTextBind = ""
        Me.txtPwd.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtPwd.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtPwd.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtPwd.TextTip = "请输入密码"
        '
        'txtAppleId
        '
        Me.txtAppleId.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.txtAppleId.BackColor = System.Drawing.Color.White
        Me.txtAppleId.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtAppleId.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtAppleId.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtAppleId.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtAppleId.ForeColor = System.Drawing.Color.Black
        Me.txtAppleId.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtAppleId.Location = New System.Drawing.Point(319, 141)
        Me.txtAppleId.MaxLength = 32767
        Me.txtAppleId.Name = "txtAppleId"
        Me.txtAppleId.Size = New System.Drawing.Size(310, 44)
        Me.txtAppleId.TabIndex = 55
        Me.txtAppleId.Tag = Nothing
        Me.txtAppleId.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtAppleId.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtAppleId.tbSelMark = True
        Me.txtAppleId.tbTextBind = ""
        Me.txtAppleId.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtAppleId.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtAppleId.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtAppleId.TextTip = "请输入Apple ID"
        '
        'btniCloudView
        '
        Me.btniCloudView.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btniCloudView.BackColor = System.Drawing.Color.Transparent
        Me.btniCloudView.BindingForm = Nothing
        Me.btniCloudView.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btniCloudView.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btniCloudView.Location = New System.Drawing.Point(634, 366)
        Me.btniCloudView.Name = "btniCloudView"
        Me.btniCloudView.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btniCloudView.Selectable = True
        Me.btniCloudView.Size = New System.Drawing.Size(83, 21)
        Me.btniCloudView.TabIndex = 54
        Me.btniCloudView.tbAdriftIconWhenHover = False
        Me.btniCloudView.tbAutoSize = False
        Me.btniCloudView.tbAutoSizeEx = True
        Me.btniCloudView.tbBackgroundImage = Nothing
        Me.btniCloudView.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btniCloudView.tbBadgeNumber = 0
        Me.btniCloudView.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloudView.tbEndEllipsis = False
        Me.btniCloudView.tbIconHoldPlace = True
        Me.btniCloudView.tbIconImage = Nothing
        Me.btniCloudView.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudView.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloudView.tbIconMore = False
        Me.btniCloudView.tbIconMouseDown = Nothing
        Me.btniCloudView.tbIconMouseHover = Nothing
        Me.btniCloudView.tbIconMouseLeave = Nothing
        Me.btniCloudView.tbIconPlaceText = 2
        Me.btniCloudView.tbIconReadOnly = Nothing
        Me.btniCloudView.tbImageMouseDown = Nothing
        Me.btniCloudView.tbImageMouseHover = Nothing
        Me.btniCloudView.tbImageMouseLeave = Nothing
        Me.btniCloudView.tbProgressValue = 50
        Me.btniCloudView.tbReadOnly = False
        Me.btniCloudView.tbReadOnlyText = False
        Me.btniCloudView.tbShadow = False
        Me.btniCloudView.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudView.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloudView.tbShowDot = False
        Me.btniCloudView.tbShowMoreIconImg = CType(resources.GetObject("btniCloudView.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btniCloudView.tbShowNew = False
        Me.btniCloudView.tbShowProgress = False
        Me.btniCloudView.tbShowTip = True
        Me.btniCloudView.tbShowToolTipOnButton = False
        Me.btniCloudView.tbSplit = "13,11,13,11"
        Me.btniCloudView.tbText = "查看如何关闭"
        Me.btniCloudView.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudView.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudView.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudView.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudView.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btniCloudView.tbTextMouseDownPlace = 0
        Me.btniCloudView.tbToolTip = ""
        Me.btniCloudView.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloudView.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloudView.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudView.VisibleEx = True
        '
        'btniCloudPrivate
        '
        Me.btniCloudPrivate.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btniCloudPrivate.BackColor = System.Drawing.Color.Transparent
        Me.btniCloudPrivate.BindingForm = Nothing
        Me.btniCloudPrivate.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btniCloudPrivate.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btniCloudPrivate.Location = New System.Drawing.Point(29, 259)
        Me.btniCloudPrivate.Name = "btniCloudPrivate"
        Me.btniCloudPrivate.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btniCloudPrivate.Selectable = True
        Me.btniCloudPrivate.Size = New System.Drawing.Size(59, 21)
        Me.btniCloudPrivate.TabIndex = 54
        Me.btniCloudPrivate.tbAdriftIconWhenHover = False
        Me.btniCloudPrivate.tbAutoSize = False
        Me.btniCloudPrivate.tbAutoSizeEx = True
        Me.btniCloudPrivate.tbBackgroundImage = Nothing
        Me.btniCloudPrivate.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btniCloudPrivate.tbBadgeNumber = 0
        Me.btniCloudPrivate.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloudPrivate.tbEndEllipsis = False
        Me.btniCloudPrivate.tbIconHoldPlace = True
        Me.btniCloudPrivate.tbIconImage = Nothing
        Me.btniCloudPrivate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudPrivate.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloudPrivate.tbIconMore = False
        Me.btniCloudPrivate.tbIconMouseDown = Nothing
        Me.btniCloudPrivate.tbIconMouseHover = Nothing
        Me.btniCloudPrivate.tbIconMouseLeave = Nothing
        Me.btniCloudPrivate.tbIconPlaceText = 2
        Me.btniCloudPrivate.tbIconReadOnly = Nothing
        Me.btniCloudPrivate.tbImageMouseDown = Nothing
        Me.btniCloudPrivate.tbImageMouseHover = Nothing
        Me.btniCloudPrivate.tbImageMouseLeave = Nothing
        Me.btniCloudPrivate.tbProgressValue = 50
        Me.btniCloudPrivate.tbReadOnly = False
        Me.btniCloudPrivate.tbReadOnlyText = False
        Me.btniCloudPrivate.tbShadow = False
        Me.btniCloudPrivate.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudPrivate.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloudPrivate.tbShowDot = False
        Me.btniCloudPrivate.tbShowMoreIconImg = CType(resources.GetObject("btniCloudPrivate.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btniCloudPrivate.tbShowNew = False
        Me.btniCloudPrivate.tbShowProgress = False
        Me.btniCloudPrivate.tbShowTip = True
        Me.btniCloudPrivate.tbShowToolTipOnButton = False
        Me.btniCloudPrivate.tbSplit = "13,11,13,11"
        Me.btniCloudPrivate.tbText = "隐私政策"
        Me.btniCloudPrivate.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudPrivate.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudPrivate.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudPrivate.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudPrivate.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btniCloudPrivate.tbTextMouseDownPlace = 0
        Me.btniCloudPrivate.tbToolTip = ""
        Me.btniCloudPrivate.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloudPrivate.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloudPrivate.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudPrivate.Visible = False
        Me.btniCloudPrivate.VisibleEx = True
        '
        'btniCloudForget
        '
        Me.btniCloudForget.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btniCloudForget.BackColor = System.Drawing.Color.Transparent
        Me.btniCloudForget.BindingForm = Nothing
        Me.btniCloudForget.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btniCloudForget.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btniCloudForget.Location = New System.Drawing.Point(319, 253)
        Me.btniCloudForget.Name = "btniCloudForget"
        Me.btniCloudForget.Padding = New System.Windows.Forms.Padding(3, 4, 3, 4)
        Me.btniCloudForget.Selectable = True
        Me.btniCloudForget.Size = New System.Drawing.Size(119, 21)
        Me.btniCloudForget.TabIndex = 54
        Me.btniCloudForget.tbAdriftIconWhenHover = False
        Me.btniCloudForget.tbAutoSize = False
        Me.btniCloudForget.tbAutoSizeEx = True
        Me.btniCloudForget.tbBackgroundImage = Nothing
        Me.btniCloudForget.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btniCloudForget.tbBadgeNumber = 0
        Me.btniCloudForget.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloudForget.tbEndEllipsis = False
        Me.btniCloudForget.tbIconHoldPlace = True
        Me.btniCloudForget.tbIconImage = Nothing
        Me.btniCloudForget.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudForget.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloudForget.tbIconMore = False
        Me.btniCloudForget.tbIconMouseDown = Nothing
        Me.btniCloudForget.tbIconMouseHover = Nothing
        Me.btniCloudForget.tbIconMouseLeave = Nothing
        Me.btniCloudForget.tbIconPlaceText = 2
        Me.btniCloudForget.tbIconReadOnly = Nothing
        Me.btniCloudForget.tbImageMouseDown = Nothing
        Me.btniCloudForget.tbImageMouseHover = Nothing
        Me.btniCloudForget.tbImageMouseLeave = Nothing
        Me.btniCloudForget.tbProgressValue = 50
        Me.btniCloudForget.tbReadOnly = False
        Me.btniCloudForget.tbReadOnlyText = False
        Me.btniCloudForget.tbShadow = False
        Me.btniCloudForget.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudForget.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloudForget.tbShowDot = False
        Me.btniCloudForget.tbShowMoreIconImg = CType(resources.GetObject("btniCloudForget.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btniCloudForget.tbShowNew = False
        Me.btniCloudForget.tbShowProgress = False
        Me.btniCloudForget.tbShowTip = True
        Me.btniCloudForget.tbShowToolTipOnButton = False
        Me.btniCloudForget.tbSplit = "13,11,13,11"
        Me.btniCloudForget.tbText = "忘记Apple ID或密码"
        Me.btniCloudForget.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudForget.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudForget.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudForget.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudForget.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(169, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.btniCloudForget.tbTextMouseDownPlace = 0
        Me.btniCloudForget.tbToolTip = ""
        Me.btniCloudForget.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloudForget.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloudForget.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudForget.VisibleEx = True
        '
        'lbliCloudTip_2
        '
        Me.lbliCloudTip_2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lbliCloudTip_2.Cursor = System.Windows.Forms.Cursors.Default
        Me.lbliCloudTip_2.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lbliCloudTip_2.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lbliCloudTip_2.Location = New System.Drawing.Point(40, 345)
        Me.lbliCloudTip_2.Name = "lbliCloudTip_2"
        Me.lbliCloudTip_2.Padding = New System.Windows.Forms.Padding(1)
        Me.lbliCloudTip_2.Size = New System.Drawing.Size(869, 24)
        Me.lbliCloudTip_2.TabIndex = 26
        Me.lbliCloudTip_2.Text = "我们非常重视您的隐私，不会访问或记录您iCloud账户的任何信息，请放心使用。"
        Me.lbliCloudTip_2.TextAlign = System.Drawing.ContentAlignment.TopCenter
        Me.lbliCloudTip_2.UseMnemonic = False
        '
        'lbliCloudTip_1
        '
        Me.lbliCloudTip_1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lbliCloudTip_1.AutoSize = True
        Me.lbliCloudTip_1.Cursor = System.Windows.Forms.Cursors.Default
        Me.lbliCloudTip_1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lbliCloudTip_1.Location = New System.Drawing.Point(232, 369)
        Me.lbliCloudTip_1.Name = "lbliCloudTip_1"
        Me.lbliCloudTip_1.Padding = New System.Windows.Forms.Padding(1)
        Me.lbliCloudTip_1.Size = New System.Drawing.Size(391, 14)
        Me.lbliCloudTip_1.TabIndex = 26
        Me.lbliCloudTip_1.Text = "若您的Apple ID开启了两步验证或双重验证，请先暂时关闭后再进行登录"
        Me.lbliCloudTip_1.UseMnemonic = False
        '
        'btniCloudLogin
        '
        Me.btniCloudLogin.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btniCloudLogin.BackColor = System.Drawing.Color.Transparent
        Me.btniCloudLogin.BindingForm = Nothing
        Me.btniCloudLogin.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btniCloudLogin.Location = New System.Drawing.Point(319, 300)
        Me.btniCloudLogin.Name = "btniCloudLogin"
        Me.btniCloudLogin.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btniCloudLogin.Selectable = True
        Me.btniCloudLogin.Size = New System.Drawing.Size(310, 35)
        Me.btniCloudLogin.TabIndex = 25
        Me.btniCloudLogin.tbAdriftIconWhenHover = False
        Me.btniCloudLogin.tbAutoSize = False
        Me.btniCloudLogin.tbAutoSizeEx = False
        Me.btniCloudLogin.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btniCloudLogin.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btniCloudLogin.tbBadgeNumber = 0
        Me.btniCloudLogin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloudLogin.tbEndEllipsis = False
        Me.btniCloudLogin.tbIconHoldPlace = True
        Me.btniCloudLogin.tbIconImage = Nothing
        Me.btniCloudLogin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudLogin.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloudLogin.tbIconMore = False
        Me.btniCloudLogin.tbIconMouseDown = Nothing
        Me.btniCloudLogin.tbIconMouseHover = Nothing
        Me.btniCloudLogin.tbIconMouseLeave = Nothing
        Me.btniCloudLogin.tbIconPlaceText = 2
        Me.btniCloudLogin.tbIconReadOnly = Nothing
        Me.btniCloudLogin.tbImageMouseDown = Nothing
        Me.btniCloudLogin.tbImageMouseHover = Nothing
        Me.btniCloudLogin.tbImageMouseLeave = Nothing
        Me.btniCloudLogin.tbProgressValue = 50
        Me.btniCloudLogin.tbReadOnly = False
        Me.btniCloudLogin.tbReadOnlyText = False
        Me.btniCloudLogin.tbShadow = False
        Me.btniCloudLogin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudLogin.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloudLogin.tbShowDot = False
        Me.btniCloudLogin.tbShowMoreIconImg = Nothing
        Me.btniCloudLogin.tbShowNew = False
        Me.btniCloudLogin.tbShowProgress = False
        Me.btniCloudLogin.tbShowTip = True
        Me.btniCloudLogin.tbShowToolTipOnButton = False
        Me.btniCloudLogin.tbSplit = "13,11,13,11"
        Me.btniCloudLogin.tbText = "立即登录"
        Me.btniCloudLogin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudLogin.tbTextColor = System.Drawing.Color.White
        Me.btniCloudLogin.tbTextColorDisable = System.Drawing.Color.White
        Me.btniCloudLogin.tbTextColorDown = System.Drawing.Color.White
        Me.btniCloudLogin.tbTextColorHover = System.Drawing.Color.White
        Me.btniCloudLogin.tbTextMouseDownPlace = 0
        Me.btniCloudLogin.tbToolTip = ""
        Me.btniCloudLogin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloudLogin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloudLogin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudLogin.VisibleEx = True
        '
        'pnliCloudNodata
        '
        Me.pnliCloudNodata.Controls.Add(Me.lbliCloudWechat)
        Me.pnliCloudNodata.Controls.Add(Me.piciCloudSearch)
        Me.pnliCloudNodata.Controls.Add(Me.lbliCloudNodata)
        Me.pnliCloudNodata.Controls.Add(Me.btniCloudReturn)
        Me.pnliCloudNodata.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnliCloudNodata.Location = New System.Drawing.Point(0, 0)
        Me.pnliCloudNodata.Name = "pnliCloudNodata"
        Me.pnliCloudNodata.Size = New System.Drawing.Size(949, 436)
        Me.pnliCloudNodata.TabIndex = 60
        Me.pnliCloudNodata.Visible = False
        '
        'lbliCloudWechat
        '
        Me.lbliCloudWechat.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lbliCloudWechat.AutoSize = True
        Me.lbliCloudWechat.BackColor = System.Drawing.Color.Transparent
        Me.lbliCloudWechat.Cursor = System.Windows.Forms.Cursors.Default
        Me.lbliCloudWechat.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.lbliCloudWechat.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lbliCloudWechat.Location = New System.Drawing.Point(269, 288)
        Me.lbliCloudWechat.Name = "lbliCloudWechat"
        Me.lbliCloudWechat.Size = New System.Drawing.Size(411, 17)
        Me.lbliCloudWechat.TabIndex = 97
        Me.lbliCloudWechat.Text = "您可以使用iCloud备份微信聊天记录或尝试设备扫描/iTunes备份文件恢复。"
        Me.lbliCloudWechat.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'piciCloudSearch
        '
        Me.piciCloudSearch.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.piciCloudSearch.Image = Global.iTong.My.Resources.Resources.icon_nodatanew
        Me.piciCloudSearch.Location = New System.Drawing.Point(411, 73)
        Me.piciCloudSearch.Margin = New System.Windows.Forms.Padding(0)
        Me.piciCloudSearch.Name = "piciCloudSearch"
        Me.piciCloudSearch.Size = New System.Drawing.Size(127, 135)
        Me.piciCloudSearch.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.piciCloudSearch.TabIndex = 56
        Me.piciCloudSearch.TabStop = False
        '
        'lbliCloudNodata
        '
        Me.lbliCloudNodata.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lbliCloudNodata.Font = New System.Drawing.Font("宋体", 15.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbliCloudNodata.Location = New System.Drawing.Point(25, 246)
        Me.lbliCloudNodata.Name = "lbliCloudNodata"
        Me.lbliCloudNodata.Size = New System.Drawing.Size(899, 34)
        Me.lbliCloudNodata.TabIndex = 26
        Me.lbliCloudNodata.tbAdriftWhenHover = False
        Me.lbliCloudNodata.tbAutoEllipsis = False
        Me.lbliCloudNodata.tbAutoSize = False
        Me.lbliCloudNodata.tbHideImage = False
        Me.lbliCloudNodata.tbIconImage = Nothing
        Me.lbliCloudNodata.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lbliCloudNodata.tbIconPlaceText = 5
        Me.lbliCloudNodata.tbShadow = False
        Me.lbliCloudNodata.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lbliCloudNodata.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lbliCloudNodata.tbShowScrolling = False
        Me.lbliCloudNodata.Text = "此iCloud备份文件中没有微信数据"
        Me.lbliCloudNodata.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btniCloudReturn
        '
        Me.btniCloudReturn.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btniCloudReturn.BackColor = System.Drawing.Color.Transparent
        Me.btniCloudReturn.BindingForm = Nothing
        Me.btniCloudReturn.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btniCloudReturn.Location = New System.Drawing.Point(403, 328)
        Me.btniCloudReturn.Name = "btniCloudReturn"
        Me.btniCloudReturn.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btniCloudReturn.Selectable = True
        Me.btniCloudReturn.Size = New System.Drawing.Size(142, 36)
        Me.btniCloudReturn.TabIndex = 24
        Me.btniCloudReturn.tbAdriftIconWhenHover = False
        Me.btniCloudReturn.tbAutoSize = False
        Me.btniCloudReturn.tbAutoSizeEx = False
        Me.btniCloudReturn.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btniCloudReturn.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btniCloudReturn.tbBadgeNumber = 0
        Me.btniCloudReturn.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloudReturn.tbEndEllipsis = False
        Me.btniCloudReturn.tbIconHoldPlace = True
        Me.btniCloudReturn.tbIconImage = Nothing
        Me.btniCloudReturn.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudReturn.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloudReturn.tbIconMore = False
        Me.btniCloudReturn.tbIconMouseDown = Nothing
        Me.btniCloudReturn.tbIconMouseHover = Nothing
        Me.btniCloudReturn.tbIconMouseLeave = Nothing
        Me.btniCloudReturn.tbIconPlaceText = 2
        Me.btniCloudReturn.tbIconReadOnly = Nothing
        Me.btniCloudReturn.tbImageMouseDown = Nothing
        Me.btniCloudReturn.tbImageMouseHover = Nothing
        Me.btniCloudReturn.tbImageMouseLeave = Nothing
        Me.btniCloudReturn.tbProgressValue = 50
        Me.btniCloudReturn.tbReadOnly = False
        Me.btniCloudReturn.tbReadOnlyText = False
        Me.btniCloudReturn.tbShadow = False
        Me.btniCloudReturn.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudReturn.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloudReturn.tbShowDot = False
        Me.btniCloudReturn.tbShowMoreIconImg = Nothing
        Me.btniCloudReturn.tbShowNew = False
        Me.btniCloudReturn.tbShowProgress = False
        Me.btniCloudReturn.tbShowTip = True
        Me.btniCloudReturn.tbShowToolTipOnButton = False
        Me.btniCloudReturn.tbSplit = "13,11,13,11"
        Me.btniCloudReturn.tbText = "返回"
        Me.btniCloudReturn.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudReturn.tbTextColor = System.Drawing.Color.White
        Me.btniCloudReturn.tbTextColorDisable = System.Drawing.Color.White
        Me.btniCloudReturn.tbTextColorDown = System.Drawing.Color.White
        Me.btniCloudReturn.tbTextColorHover = System.Drawing.Color.White
        Me.btniCloudReturn.tbTextMouseDownPlace = 0
        Me.btniCloudReturn.tbToolTip = ""
        Me.btniCloudReturn.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloudReturn.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloudReturn.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudReturn.VisibleEx = True
        '
        'pnliCloudDownStatus
        '
        Me.pnliCloudDownStatus.Controls.Add(Me.lbliCloudAcct)
        Me.pnliCloudDownStatus.Controls.Add(Me.btniCloudLogout)
        Me.pnliCloudDownStatus.Controls.Add(Me.lbliCloudView)
        Me.pnliCloudDownStatus.Controls.Add(Me.lbliCloudErrorServer)
        Me.pnliCloudDownStatus.Controls.Add(Me.pnlDownLoadiCloud)
        Me.pnliCloudDownStatus.Controls.Add(Me.pnliCloudWaiting)
        Me.pnliCloudDownStatus.Controls.Add(Me.pnliCloudGrid)
        Me.pnliCloudDownStatus.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnliCloudDownStatus.Location = New System.Drawing.Point(0, 0)
        Me.pnliCloudDownStatus.Name = "pnliCloudDownStatus"
        Me.pnliCloudDownStatus.Size = New System.Drawing.Size(949, 436)
        Me.pnliCloudDownStatus.TabIndex = 59
        Me.pnliCloudDownStatus.Visible = False
        '
        'lbliCloudAcct
        '
        Me.lbliCloudAcct.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lbliCloudAcct.BackColor = System.Drawing.Color.Transparent
        Me.lbliCloudAcct.BindingForm = Nothing
        Me.lbliCloudAcct.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbliCloudAcct.Location = New System.Drawing.Point(764, 28)
        Me.lbliCloudAcct.Margin = New System.Windows.Forms.Padding(0)
        Me.lbliCloudAcct.Name = "lbliCloudAcct"
        Me.lbliCloudAcct.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.lbliCloudAcct.Selectable = True
        Me.lbliCloudAcct.Size = New System.Drawing.Size(57, 21)
        Me.lbliCloudAcct.TabIndex = 90
        Me.lbliCloudAcct.tbAdriftIconWhenHover = False
        Me.lbliCloudAcct.tbAutoSize = False
        Me.lbliCloudAcct.tbAutoSizeEx = True
        Me.lbliCloudAcct.tbBackgroundImage = Nothing
        Me.lbliCloudAcct.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.lbliCloudAcct.tbBadgeNumber = 0
        Me.lbliCloudAcct.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.lbliCloudAcct.tbEndEllipsis = False
        Me.lbliCloudAcct.tbIconHoldPlace = True
        Me.lbliCloudAcct.tbIconImage = Global.iTong.My.Resources.Resources.btn_import_4
        Me.lbliCloudAcct.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lbliCloudAcct.tbIconImageState = iTong.Components.ImageState.FourState
        Me.lbliCloudAcct.tbIconMore = False
        Me.lbliCloudAcct.tbIconMouseDown = Nothing
        Me.lbliCloudAcct.tbIconMouseHover = Nothing
        Me.lbliCloudAcct.tbIconMouseLeave = Nothing
        Me.lbliCloudAcct.tbIconPlaceText = 2
        Me.lbliCloudAcct.tbIconReadOnly = Nothing
        Me.lbliCloudAcct.tbImageMouseDown = Nothing
        Me.lbliCloudAcct.tbImageMouseHover = Nothing
        Me.lbliCloudAcct.tbImageMouseLeave = Nothing
        Me.lbliCloudAcct.tbProgressValue = 50
        Me.lbliCloudAcct.tbReadOnly = False
        Me.lbliCloudAcct.tbReadOnlyText = False
        Me.lbliCloudAcct.tbShadow = False
        Me.lbliCloudAcct.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lbliCloudAcct.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lbliCloudAcct.tbShowDot = False
        Me.lbliCloudAcct.tbShowMoreIconImg = CType(resources.GetObject("lbliCloudAcct.tbShowMoreIconImg"), System.Drawing.Image)
        Me.lbliCloudAcct.tbShowNew = False
        Me.lbliCloudAcct.tbShowProgress = False
        Me.lbliCloudAcct.tbShowTip = True
        Me.lbliCloudAcct.tbShowToolTipOnButton = False
        Me.lbliCloudAcct.tbSplit = "3,3,3,3"
        Me.lbliCloudAcct.tbText = "注销"
        Me.lbliCloudAcct.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lbliCloudAcct.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.lbliCloudAcct.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.lbliCloudAcct.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.lbliCloudAcct.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.lbliCloudAcct.tbTextMouseDownPlace = 2
        Me.lbliCloudAcct.tbToolTip = ""
        Me.lbliCloudAcct.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.lbliCloudAcct.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.lbliCloudAcct.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lbliCloudAcct.VisibleEx = True
        '
        'btniCloudLogout
        '
        Me.btniCloudLogout.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btniCloudLogout.BackColor = System.Drawing.Color.Transparent
        Me.btniCloudLogout.BindingForm = Nothing
        Me.btniCloudLogout.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btniCloudLogout.Location = New System.Drawing.Point(821, 28)
        Me.btniCloudLogout.Margin = New System.Windows.Forms.Padding(0)
        Me.btniCloudLogout.Name = "btniCloudLogout"
        Me.btniCloudLogout.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btniCloudLogout.Selectable = True
        Me.btniCloudLogout.Size = New System.Drawing.Size(57, 21)
        Me.btniCloudLogout.TabIndex = 87
        Me.btniCloudLogout.tbAdriftIconWhenHover = False
        Me.btniCloudLogout.tbAutoSize = False
        Me.btniCloudLogout.tbAutoSizeEx = True
        Me.btniCloudLogout.tbBackgroundImage = Nothing
        Me.btniCloudLogout.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btniCloudLogout.tbBadgeNumber = 0
        Me.btniCloudLogout.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloudLogout.tbEndEllipsis = False
        Me.btniCloudLogout.tbIconHoldPlace = True
        Me.btniCloudLogout.tbIconImage = Global.iTong.My.Resources.Resources.btn_import_4
        Me.btniCloudLogout.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniCloudLogout.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btniCloudLogout.tbIconMore = False
        Me.btniCloudLogout.tbIconMouseDown = Nothing
        Me.btniCloudLogout.tbIconMouseHover = Nothing
        Me.btniCloudLogout.tbIconMouseLeave = Nothing
        Me.btniCloudLogout.tbIconPlaceText = 2
        Me.btniCloudLogout.tbIconReadOnly = Nothing
        Me.btniCloudLogout.tbImageMouseDown = Nothing
        Me.btniCloudLogout.tbImageMouseHover = Nothing
        Me.btniCloudLogout.tbImageMouseLeave = Nothing
        Me.btniCloudLogout.tbProgressValue = 50
        Me.btniCloudLogout.tbReadOnly = False
        Me.btniCloudLogout.tbReadOnlyText = False
        Me.btniCloudLogout.tbShadow = False
        Me.btniCloudLogout.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudLogout.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloudLogout.tbShowDot = False
        Me.btniCloudLogout.tbShowMoreIconImg = CType(resources.GetObject("btniCloudLogout.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btniCloudLogout.tbShowNew = False
        Me.btniCloudLogout.tbShowProgress = False
        Me.btniCloudLogout.tbShowTip = True
        Me.btniCloudLogout.tbShowToolTipOnButton = False
        Me.btniCloudLogout.tbSplit = "3,3,3,3"
        Me.btniCloudLogout.tbText = "注销"
        Me.btniCloudLogout.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudLogout.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btniCloudLogout.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btniCloudLogout.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btniCloudLogout.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btniCloudLogout.tbTextMouseDownPlace = 2
        Me.btniCloudLogout.tbToolTip = ""
        Me.btniCloudLogout.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloudLogout.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloudLogout.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudLogout.VisibleEx = True
        '
        'lbliCloudView
        '
        Me.lbliCloudView.AutoSize = True
        Me.lbliCloudView.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.lbliCloudView.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lbliCloudView.Location = New System.Drawing.Point(34, 27)
        Me.lbliCloudView.Name = "lbliCloudView"
        Me.lbliCloudView.Size = New System.Drawing.Size(232, 15)
        Me.lbliCloudView.TabIndex = 1
        Me.lbliCloudView.Text = "请选择要查看的微信信息备份文件"
        '
        'lbliCloudErrorServer
        '
        Me.lbliCloudErrorServer.AutoSize = True
        Me.lbliCloudErrorServer.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.lbliCloudErrorServer.ForeColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.lbliCloudErrorServer.Location = New System.Drawing.Point(314, 27)
        Me.lbliCloudErrorServer.Name = "lbliCloudErrorServer"
        Me.lbliCloudErrorServer.Size = New System.Drawing.Size(265, 15)
        Me.lbliCloudErrorServer.TabIndex = 2
        Me.lbliCloudErrorServer.Text = "读取数据过程中服务器连接失败......"
        Me.lbliCloudErrorServer.Visible = False
        '
        'pnlDownLoadiCloud
        '
        Me.pnlDownLoadiCloud.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlDownLoadiCloud.Controls.Add(Me.lblPercentage)
        Me.pnlDownLoadiCloud.Controls.Add(Me.lbliCloudWaitingMsg)
        Me.pnlDownLoadiCloud.Controls.Add(Me.btniCloudCancel)
        Me.pnlDownLoadiCloud.Controls.Add(Me.PictureBox1)
        Me.pnlDownLoadiCloud.Location = New System.Drawing.Point(32, 56)
        Me.pnlDownLoadiCloud.Name = "pnlDownLoadiCloud"
        Me.pnlDownLoadiCloud.Size = New System.Drawing.Size(885, 377)
        Me.pnlDownLoadiCloud.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDownLoadiCloud.TabIndex = 91
        Me.pnlDownLoadiCloud.tbBackgroundImage = Nothing
        Me.pnlDownLoadiCloud.tbShowWatermark = False
        Me.pnlDownLoadiCloud.tbSplit = "0,0,0,0"
        Me.pnlDownLoadiCloud.tbWatermark = Nothing
        Me.pnlDownLoadiCloud.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDownLoadiCloud.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlDownLoadiCloud.Visible = False
        '
        'lblPercentage
        '
        Me.lblPercentage.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblPercentage.BackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.lblPercentage.Font = New System.Drawing.Font("宋体", 20.0!, System.Drawing.FontStyle.Bold)
        Me.lblPercentage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lblPercentage.Location = New System.Drawing.Point(396, 62)
        Me.lblPercentage.Name = "lblPercentage"
        Me.lblPercentage.Size = New System.Drawing.Size(92, 29)
        Me.lblPercentage.TabIndex = 88
        Me.lblPercentage.Text = "0%"
        Me.lblPercentage.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lbliCloudWaitingMsg
        '
        Me.lbliCloudWaitingMsg.Anchor = CType((System.Windows.Forms.AnchorStyles.Left Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lbliCloudWaitingMsg.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lbliCloudWaitingMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lbliCloudWaitingMsg.Location = New System.Drawing.Point(36, 194)
        Me.lbliCloudWaitingMsg.Name = "lbliCloudWaitingMsg"
        Me.lbliCloudWaitingMsg.Size = New System.Drawing.Size(812, 65)
        Me.lbliCloudWaitingMsg.TabIndex = 34
        Me.lbliCloudWaitingMsg.Text = "正在获取iCloud备份文件信息，请稍等..."
        Me.lbliCloudWaitingMsg.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btniCloudCancel
        '
        Me.btniCloudCancel.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btniCloudCancel.BackColor = System.Drawing.Color.Transparent
        Me.btniCloudCancel.BindingForm = Nothing
        Me.btniCloudCancel.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btniCloudCancel.Location = New System.Drawing.Point(371, 283)
        Me.btniCloudCancel.Name = "btniCloudCancel"
        Me.btniCloudCancel.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btniCloudCancel.Selectable = True
        Me.btniCloudCancel.Size = New System.Drawing.Size(142, 36)
        Me.btniCloudCancel.TabIndex = 35
        Me.btniCloudCancel.tbAdriftIconWhenHover = False
        Me.btniCloudCancel.tbAutoSize = False
        Me.btniCloudCancel.tbAutoSizeEx = False
        Me.btniCloudCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btniCloudCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btniCloudCancel.tbBadgeNumber = 0
        Me.btniCloudCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloudCancel.tbEndEllipsis = False
        Me.btniCloudCancel.tbIconHoldPlace = True
        Me.btniCloudCancel.tbIconImage = Nothing
        Me.btniCloudCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloudCancel.tbIconMore = False
        Me.btniCloudCancel.tbIconMouseDown = Nothing
        Me.btniCloudCancel.tbIconMouseHover = Nothing
        Me.btniCloudCancel.tbIconMouseLeave = Nothing
        Me.btniCloudCancel.tbIconPlaceText = 2
        Me.btniCloudCancel.tbIconReadOnly = Nothing
        Me.btniCloudCancel.tbImageMouseDown = Nothing
        Me.btniCloudCancel.tbImageMouseHover = Nothing
        Me.btniCloudCancel.tbImageMouseLeave = Nothing
        Me.btniCloudCancel.tbProgressValue = 50
        Me.btniCloudCancel.tbReadOnly = False
        Me.btniCloudCancel.tbReadOnlyText = False
        Me.btniCloudCancel.tbShadow = False
        Me.btniCloudCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloudCancel.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloudCancel.tbShowDot = False
        Me.btniCloudCancel.tbShowMoreIconImg = Nothing
        Me.btniCloudCancel.tbShowNew = False
        Me.btniCloudCancel.tbShowProgress = False
        Me.btniCloudCancel.tbShowTip = True
        Me.btniCloudCancel.tbShowToolTipOnButton = False
        Me.btniCloudCancel.tbSplit = "13,11,13,11"
        Me.btniCloudCancel.tbText = "取消下载"
        Me.btniCloudCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudCancel.tbTextColor = System.Drawing.Color.White
        Me.btniCloudCancel.tbTextColorDisable = System.Drawing.Color.White
        Me.btniCloudCancel.tbTextColorDown = System.Drawing.Color.White
        Me.btniCloudCancel.tbTextColorHover = System.Drawing.Color.White
        Me.btniCloudCancel.tbTextMouseDownPlace = 0
        Me.btniCloudCancel.tbToolTip = ""
        Me.btniCloudCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloudCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloudCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloudCancel.VisibleEx = True
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.gif_loading_132
        Me.PictureBox1.Location = New System.Drawing.Point(376, 10)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(132, 132)
        Me.PictureBox1.TabIndex = 89
        Me.PictureBox1.TabStop = False
        '
        'pnliCloudWaiting
        '
        Me.pnliCloudWaiting.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnliCloudWaiting.BackColor = System.Drawing.Color.Transparent
        Me.pnliCloudWaiting.Controls.Add(Me.lbliCloudLoading)
        Me.pnliCloudWaiting.Controls.Add(Me.pbLoading)
        Me.pnliCloudWaiting.Location = New System.Drawing.Point(32, 56)
        Me.pnliCloudWaiting.Name = "pnliCloudWaiting"
        Me.pnliCloudWaiting.Size = New System.Drawing.Size(885, 377)
        Me.pnliCloudWaiting.TabIndex = 81
        Me.pnliCloudWaiting.Visible = False
        '
        'lbliCloudLoading
        '
        Me.lbliCloudLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lbliCloudLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lbliCloudLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lbliCloudLoading.Location = New System.Drawing.Point(328, 145)
        Me.lbliCloudLoading.Name = "lbliCloudLoading"
        Me.lbliCloudLoading.Size = New System.Drawing.Size(336, 45)
        Me.lbliCloudLoading.TabIndex = 12
        Me.lbliCloudLoading.Text = "正在获取iCloud备份文件列表..."
        Me.lbliCloudLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_24
        Me.pbLoading.Location = New System.Drawing.Point(221, 120)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(91, 95)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 11
        Me.pbLoading.TabStop = False
        '
        'pnliCloudGrid
        '
        Me.pnliCloudGrid.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnliCloudGrid.BackColor = System.Drawing.Color.Silver
        Me.pnliCloudGrid.Location = New System.Drawing.Point(32, 56)
        Me.pnliCloudGrid.Margin = New System.Windows.Forms.Padding(1)
        Me.pnliCloudGrid.Name = "pnliCloudGrid"
        Me.pnliCloudGrid.Padding = New System.Windows.Forms.Padding(1)
        Me.pnliCloudGrid.Size = New System.Drawing.Size(885, 377)
        Me.pnliCloudGrid.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnliCloudGrid.TabIndex = 79
        Me.pnliCloudGrid.tbBackgroundImage = Nothing
        Me.pnliCloudGrid.tbShowWatermark = False
        Me.pnliCloudGrid.tbSplit = "0,0,0,0"
        Me.pnliCloudGrid.tbWatermark = Nothing
        Me.pnliCloudGrid.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnliCloudGrid.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnliCloudHead
        '
        Me.pnliCloudHead.Controls.Add(Me.TbButton8)
        Me.pnliCloudHead.Controls.Add(Me.TbButton10)
        Me.pnliCloudHead.Controls.Add(Me.TbButton11)
        Me.pnliCloudHead.Controls.Add(Me.TbButton13)
        Me.pnliCloudHead.Controls.Add(Me.TbButton12)
        Me.pnliCloudHead.Controls.Add(Me.btnDevice_3)
        Me.pnliCloudHead.Controls.Add(Me.btniCloud)
        Me.pnliCloudHead.Controls.Add(Me.btnBackup_3)
        Me.pnliCloudHead.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnliCloudHead.Location = New System.Drawing.Point(1898, 0)
        Me.pnliCloudHead.Margin = New System.Windows.Forms.Padding(0)
        Me.pnliCloudHead.Name = "pnliCloudHead"
        Me.pnliCloudHead.Size = New System.Drawing.Size(949, 110)
        Me.pnliCloudHead.TabIndex = 33
        '
        'TbButton8
        '
        Me.TbButton8.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.TbButton8.BindingForm = Nothing
        Me.TbButton8.Location = New System.Drawing.Point(531, 18)
        Me.TbButton8.Name = "TbButton8"
        Me.TbButton8.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton8.Selectable = True
        Me.TbButton8.Size = New System.Drawing.Size(1, 80)
        Me.TbButton8.TabIndex = 29
        Me.TbButton8.tbAdriftIconWhenHover = False
        Me.TbButton8.tbAutoSize = False
        Me.TbButton8.tbAutoSizeEx = False
        Me.TbButton8.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_restore_left
        Me.TbButton8.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton8.tbBadgeNumber = 0
        Me.TbButton8.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton8.tbEndEllipsis = False
        Me.TbButton8.tbIconHoldPlace = True
        Me.TbButton8.tbIconImage = Nothing
        Me.TbButton8.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton8.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton8.tbIconMore = False
        Me.TbButton8.tbIconMouseDown = Nothing
        Me.TbButton8.tbIconMouseHover = Nothing
        Me.TbButton8.tbIconMouseLeave = Nothing
        Me.TbButton8.tbIconPlaceText = 2
        Me.TbButton8.tbIconReadOnly = Nothing
        Me.TbButton8.tbImageMouseDown = Nothing
        Me.TbButton8.tbImageMouseHover = Nothing
        Me.TbButton8.tbImageMouseLeave = Nothing
        Me.TbButton8.tbProgressValue = 50
        Me.TbButton8.tbReadOnly = False
        Me.TbButton8.tbReadOnlyText = False
        Me.TbButton8.tbShadow = False
        Me.TbButton8.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton8.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton8.tbShowDot = False
        Me.TbButton8.tbShowMoreIconImg = CType(resources.GetObject("TbButton8.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton8.tbShowNew = False
        Me.TbButton8.tbShowProgress = False
        Me.TbButton8.tbShowTip = True
        Me.TbButton8.tbShowToolTipOnButton = False
        Me.TbButton8.tbSplit = "1,26,6,25"
        Me.TbButton8.tbText = ""
        Me.TbButton8.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton8.tbTextColor = System.Drawing.Color.White
        Me.TbButton8.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton8.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton8.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton8.tbTextMouseDownPlace = 0
        Me.TbButton8.tbToolTip = ""
        Me.TbButton8.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton8.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton8.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton8.VisibleEx = True
        '
        'TbButton10
        '
        Me.TbButton10.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.TbButton10.BindingForm = Nothing
        Me.TbButton10.Location = New System.Drawing.Point(274, 15)
        Me.TbButton10.Name = "TbButton10"
        Me.TbButton10.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton10.Selectable = True
        Me.TbButton10.Size = New System.Drawing.Size(1, 80)
        Me.TbButton10.TabIndex = 29
        Me.TbButton10.tbAdriftIconWhenHover = False
        Me.TbButton10.tbAutoSize = False
        Me.TbButton10.tbAutoSizeEx = False
        Me.TbButton10.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_restore_left
        Me.TbButton10.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton10.tbBadgeNumber = 0
        Me.TbButton10.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton10.tbEndEllipsis = False
        Me.TbButton10.tbIconHoldPlace = True
        Me.TbButton10.tbIconImage = Nothing
        Me.TbButton10.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton10.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton10.tbIconMore = False
        Me.TbButton10.tbIconMouseDown = Nothing
        Me.TbButton10.tbIconMouseHover = Nothing
        Me.TbButton10.tbIconMouseLeave = Nothing
        Me.TbButton10.tbIconPlaceText = 2
        Me.TbButton10.tbIconReadOnly = Nothing
        Me.TbButton10.tbImageMouseDown = Nothing
        Me.TbButton10.tbImageMouseHover = Nothing
        Me.TbButton10.tbImageMouseLeave = Nothing
        Me.TbButton10.tbProgressValue = 50
        Me.TbButton10.tbReadOnly = False
        Me.TbButton10.tbReadOnlyText = False
        Me.TbButton10.tbShadow = False
        Me.TbButton10.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton10.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton10.tbShowDot = False
        Me.TbButton10.tbShowMoreIconImg = CType(resources.GetObject("TbButton10.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton10.tbShowNew = False
        Me.TbButton10.tbShowProgress = False
        Me.TbButton10.tbShowTip = True
        Me.TbButton10.tbShowToolTipOnButton = False
        Me.TbButton10.tbSplit = "1,26,6,25"
        Me.TbButton10.tbText = ""
        Me.TbButton10.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton10.tbTextColor = System.Drawing.Color.White
        Me.TbButton10.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton10.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton10.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton10.tbTextMouseDownPlace = 0
        Me.TbButton10.tbToolTip = ""
        Me.TbButton10.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton10.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton10.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton10.VisibleEx = True
        '
        'TbButton11
        '
        Me.TbButton11.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton11.BackColor = System.Drawing.Color.Transparent
        Me.TbButton11.BindingForm = Nothing
        Me.TbButton11.Location = New System.Drawing.Point(533, 100)
        Me.TbButton11.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton11.Name = "TbButton11"
        Me.TbButton11.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton11.Selectable = True
        Me.TbButton11.Size = New System.Drawing.Size(417, 10)
        Me.TbButton11.TabIndex = 28
        Me.TbButton11.tbAdriftIconWhenHover = False
        Me.TbButton11.tbAutoSize = False
        Me.TbButton11.tbAutoSizeEx = False
        Me.TbButton11.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_bg
        Me.TbButton11.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton11.tbBadgeNumber = 0
        Me.TbButton11.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton11.tbEndEllipsis = False
        Me.TbButton11.tbIconHoldPlace = True
        Me.TbButton11.tbIconImage = Nothing
        Me.TbButton11.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton11.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton11.tbIconMore = False
        Me.TbButton11.tbIconMouseDown = Nothing
        Me.TbButton11.tbIconMouseHover = Nothing
        Me.TbButton11.tbIconMouseLeave = Nothing
        Me.TbButton11.tbIconPlaceText = 2
        Me.TbButton11.tbIconReadOnly = Nothing
        Me.TbButton11.tbImageMouseDown = Nothing
        Me.TbButton11.tbImageMouseHover = Nothing
        Me.TbButton11.tbImageMouseLeave = Nothing
        Me.TbButton11.tbProgressValue = 50
        Me.TbButton11.tbReadOnly = False
        Me.TbButton11.tbReadOnlyText = False
        Me.TbButton11.tbShadow = False
        Me.TbButton11.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton11.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton11.tbShowDot = False
        Me.TbButton11.tbShowMoreIconImg = CType(resources.GetObject("TbButton11.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton11.tbShowNew = False
        Me.TbButton11.tbShowProgress = False
        Me.TbButton11.tbShowTip = True
        Me.TbButton11.tbShowToolTipOnButton = False
        Me.TbButton11.tbSplit = "72,1,1,11"
        Me.TbButton11.tbText = ""
        Me.TbButton11.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton11.tbTextColor = System.Drawing.Color.White
        Me.TbButton11.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton11.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton11.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton11.tbTextMouseDownPlace = 0
        Me.TbButton11.tbToolTip = ""
        Me.TbButton11.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton11.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton11.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton11.VisibleEx = True
        '
        'TbButton13
        '
        Me.TbButton13.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.TbButton13.BackColor = System.Drawing.Color.Transparent
        Me.TbButton13.BindingForm = Nothing
        Me.TbButton13.Location = New System.Drawing.Point(277, 100)
        Me.TbButton13.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton13.Name = "TbButton13"
        Me.TbButton13.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton13.Selectable = True
        Me.TbButton13.Size = New System.Drawing.Size(253, 10)
        Me.TbButton13.TabIndex = 28
        Me.TbButton13.tbAdriftIconWhenHover = False
        Me.TbButton13.tbAutoSize = False
        Me.TbButton13.tbAutoSizeEx = False
        Me.TbButton13.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_left
        Me.TbButton13.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton13.tbBadgeNumber = 0
        Me.TbButton13.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton13.tbEndEllipsis = False
        Me.TbButton13.tbIconHoldPlace = True
        Me.TbButton13.tbIconImage = Nothing
        Me.TbButton13.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton13.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton13.tbIconMore = False
        Me.TbButton13.tbIconMouseDown = Nothing
        Me.TbButton13.tbIconMouseHover = Nothing
        Me.TbButton13.tbIconMouseLeave = Nothing
        Me.TbButton13.tbIconPlaceText = 2
        Me.TbButton13.tbIconReadOnly = Nothing
        Me.TbButton13.tbImageMouseDown = Nothing
        Me.TbButton13.tbImageMouseHover = Nothing
        Me.TbButton13.tbImageMouseLeave = Nothing
        Me.TbButton13.tbProgressValue = 50
        Me.TbButton13.tbReadOnly = False
        Me.TbButton13.tbReadOnlyText = False
        Me.TbButton13.tbShadow = False
        Me.TbButton13.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton13.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton13.tbShowDot = False
        Me.TbButton13.tbShowMoreIconImg = CType(resources.GetObject("TbButton13.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton13.tbShowNew = False
        Me.TbButton13.tbShowProgress = False
        Me.TbButton13.tbShowTip = True
        Me.TbButton13.tbShowToolTipOnButton = False
        Me.TbButton13.tbSplit = "1,1,1,3"
        Me.TbButton13.tbText = ""
        Me.TbButton13.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton13.tbTextColor = System.Drawing.Color.White
        Me.TbButton13.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton13.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton13.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton13.tbTextMouseDownPlace = 0
        Me.TbButton13.tbToolTip = ""
        Me.TbButton13.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton13.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton13.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton13.VisibleEx = True
        '
        'TbButton12
        '
        Me.TbButton12.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.TbButton12.BackColor = System.Drawing.Color.Transparent
        Me.TbButton12.BindingForm = Nothing
        Me.TbButton12.Location = New System.Drawing.Point(0, 100)
        Me.TbButton12.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton12.Name = "TbButton12"
        Me.TbButton12.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton12.Selectable = True
        Me.TbButton12.Size = New System.Drawing.Size(273, 10)
        Me.TbButton12.TabIndex = 28
        Me.TbButton12.tbAdriftIconWhenHover = False
        Me.TbButton12.tbAutoSize = False
        Me.TbButton12.tbAutoSizeEx = False
        Me.TbButton12.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_left
        Me.TbButton12.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton12.tbBadgeNumber = 0
        Me.TbButton12.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton12.tbEndEllipsis = False
        Me.TbButton12.tbIconHoldPlace = True
        Me.TbButton12.tbIconImage = Nothing
        Me.TbButton12.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton12.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton12.tbIconMore = False
        Me.TbButton12.tbIconMouseDown = Nothing
        Me.TbButton12.tbIconMouseHover = Nothing
        Me.TbButton12.tbIconMouseLeave = Nothing
        Me.TbButton12.tbIconPlaceText = 2
        Me.TbButton12.tbIconReadOnly = Nothing
        Me.TbButton12.tbImageMouseDown = Nothing
        Me.TbButton12.tbImageMouseHover = Nothing
        Me.TbButton12.tbImageMouseLeave = Nothing
        Me.TbButton12.tbProgressValue = 50
        Me.TbButton12.tbReadOnly = False
        Me.TbButton12.tbReadOnlyText = False
        Me.TbButton12.tbShadow = False
        Me.TbButton12.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton12.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton12.tbShowDot = False
        Me.TbButton12.tbShowMoreIconImg = CType(resources.GetObject("TbButton12.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton12.tbShowNew = False
        Me.TbButton12.tbShowProgress = False
        Me.TbButton12.tbShowTip = True
        Me.TbButton12.tbShowToolTipOnButton = False
        Me.TbButton12.tbSplit = "1,1,1,3"
        Me.TbButton12.tbText = ""
        Me.TbButton12.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton12.tbTextColor = System.Drawing.Color.White
        Me.TbButton12.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton12.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton12.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton12.tbTextMouseDownPlace = 0
        Me.TbButton12.tbToolTip = ""
        Me.TbButton12.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton12.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton12.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton12.VisibleEx = True
        '
        'btnDevice_3
        '
        Me.btnDevice_3.BackColor = System.Drawing.Color.Transparent
        Me.btnDevice_3.BindingForm = Nothing
        Me.btnDevice_3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDevice_3.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btnDevice_3.Location = New System.Drawing.Point(21, 10)
        Me.btnDevice_3.Name = "btnDevice_3"
        Me.btnDevice_3.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnDevice_3.Selectable = True
        Me.btnDevice_3.Size = New System.Drawing.Size(248, 90)
        Me.btnDevice_3.TabIndex = 27
        Me.btnDevice_3.tbAdriftIconWhenHover = False
        Me.btnDevice_3.tbAutoSize = False
        Me.btnDevice_3.tbAutoSizeEx = False
        Me.btnDevice_3.tbBackgroundImage = Nothing
        Me.btnDevice_3.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDevice_3.tbBadgeNumber = 0
        Me.btnDevice_3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDevice_3.tbEndEllipsis = False
        Me.btnDevice_3.tbIconHoldPlace = True
        Me.btnDevice_3.tbIconImage = Global.iTong.My.Resources.Resources.wechat_device_new
        Me.btnDevice_3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDevice_3.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDevice_3.tbIconMore = False
        Me.btnDevice_3.tbIconMouseDown = Nothing
        Me.btnDevice_3.tbIconMouseHover = Nothing
        Me.btnDevice_3.tbIconMouseLeave = Nothing
        Me.btnDevice_3.tbIconPlaceText = 2
        Me.btnDevice_3.tbIconReadOnly = Nothing
        Me.btnDevice_3.tbImageMouseDown = Nothing
        Me.btnDevice_3.tbImageMouseHover = Nothing
        Me.btnDevice_3.tbImageMouseLeave = Nothing
        Me.btnDevice_3.tbProgressValue = 50
        Me.btnDevice_3.tbReadOnly = False
        Me.btnDevice_3.tbReadOnlyText = False
        Me.btnDevice_3.tbShadow = False
        Me.btnDevice_3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDevice_3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDevice_3.tbShowDot = False
        Me.btnDevice_3.tbShowMoreIconImg = Nothing
        Me.btnDevice_3.tbShowNew = False
        Me.btnDevice_3.tbShowProgress = False
        Me.btnDevice_3.tbShowTip = True
        Me.btnDevice_3.tbShowToolTipOnButton = False
        Me.btnDevice_3.tbSplit = "5,3,1,5"
        Me.btnDevice_3.tbText = "通过扫描设备获取信息"
        Me.btnDevice_3.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDevice_3.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDevice_3.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDevice_3.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDevice_3.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDevice_3.tbTextMouseDownPlace = 0
        Me.btnDevice_3.tbToolTip = ""
        Me.btnDevice_3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDevice_3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDevice_3.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDevice_3.VisibleEx = True
        '
        'btniCloud
        '
        Me.btniCloud.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btniCloud.BackColor = System.Drawing.Color.Transparent
        Me.btniCloud.BindingForm = Nothing
        Me.btniCloud.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btniCloud.Font = New System.Drawing.Font("宋体", 14.0!)
        Me.btniCloud.Location = New System.Drawing.Point(534, 10)
        Me.btniCloud.Name = "btniCloud"
        Me.btniCloud.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btniCloud.Selectable = True
        Me.btniCloud.Size = New System.Drawing.Size(410, 90)
        Me.btniCloud.TabIndex = 27
        Me.btniCloud.tbAdriftIconWhenHover = False
        Me.btniCloud.tbAutoSize = False
        Me.btniCloud.tbAutoSizeEx = False
        Me.btniCloud.tbBackgroundImage = Nothing
        Me.btniCloud.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btniCloud.tbBadgeNumber = 0
        Me.btniCloud.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloud.tbEndEllipsis = False
        Me.btniCloud.tbIconHoldPlace = True
        Me.btniCloud.tbIconImage = Global.iTong.My.Resources.Resources.wechat_icloud
        Me.btniCloud.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniCloud.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloud.tbIconMore = False
        Me.btniCloud.tbIconMouseDown = Nothing
        Me.btniCloud.tbIconMouseHover = Nothing
        Me.btniCloud.tbIconMouseLeave = Nothing
        Me.btniCloud.tbIconPlaceText = 2
        Me.btniCloud.tbIconReadOnly = Nothing
        Me.btniCloud.tbImageMouseDown = Nothing
        Me.btniCloud.tbImageMouseHover = Nothing
        Me.btniCloud.tbImageMouseLeave = Nothing
        Me.btniCloud.tbProgressValue = 50
        Me.btniCloud.tbReadOnly = False
        Me.btniCloud.tbReadOnlyText = False
        Me.btniCloud.tbShadow = False
        Me.btniCloud.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloud.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloud.tbShowDot = False
        Me.btniCloud.tbShowMoreIconImg = Nothing
        Me.btniCloud.tbShowNew = False
        Me.btniCloud.tbShowProgress = False
        Me.btniCloud.tbShowTip = True
        Me.btniCloud.tbShowToolTipOnButton = True
        Me.btniCloud.tbSplit = "3,3,3,3"
        Me.btniCloud.tbText = "通过iCloud备份恢复"
        Me.btniCloud.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniCloud.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btniCloud.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btniCloud.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btniCloud.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btniCloud.tbTextMouseDownPlace = 0
        Me.btniCloud.tbToolTip = "导入iCloud备份文件，支持已备份的微信等数据快速恢复"
        Me.btniCloud.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloud.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniCloud.VisibleEx = True
        '
        'btnBackup_3
        '
        Me.btnBackup_3.BackColor = System.Drawing.Color.Transparent
        Me.btnBackup_3.BindingForm = Nothing
        Me.btnBackup_3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnBackup_3.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btnBackup_3.ForeColor = System.Drawing.SystemColors.ControlText
        Me.btnBackup_3.Location = New System.Drawing.Point(277, 10)
        Me.btnBackup_3.Name = "btnBackup_3"
        Me.btnBackup_3.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnBackup_3.Selectable = True
        Me.btnBackup_3.Size = New System.Drawing.Size(248, 90)
        Me.btnBackup_3.TabIndex = 27
        Me.btnBackup_3.tbAdriftIconWhenHover = False
        Me.btnBackup_3.tbAutoSize = False
        Me.btnBackup_3.tbAutoSizeEx = False
        Me.btnBackup_3.tbBackgroundImage = Nothing
        Me.btnBackup_3.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnBackup_3.tbBadgeNumber = 0
        Me.btnBackup_3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackup_3.tbEndEllipsis = False
        Me.btnBackup_3.tbIconHoldPlace = True
        Me.btnBackup_3.tbIconImage = Global.iTong.My.Resources.Resources.wechat_restore_new
        Me.btnBackup_3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackup_3.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBackup_3.tbIconMore = False
        Me.btnBackup_3.tbIconMouseDown = Nothing
        Me.btnBackup_3.tbIconMouseHover = Nothing
        Me.btnBackup_3.tbIconMouseLeave = Nothing
        Me.btnBackup_3.tbIconPlaceText = 2
        Me.btnBackup_3.tbIconReadOnly = Nothing
        Me.btnBackup_3.tbImageMouseDown = Nothing
        Me.btnBackup_3.tbImageMouseHover = Nothing
        Me.btnBackup_3.tbImageMouseLeave = Nothing
        Me.btnBackup_3.tbProgressValue = 50
        Me.btnBackup_3.tbReadOnly = False
        Me.btnBackup_3.tbReadOnlyText = False
        Me.btnBackup_3.tbShadow = False
        Me.btnBackup_3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackup_3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackup_3.tbShowDot = False
        Me.btnBackup_3.tbShowMoreIconImg = Nothing
        Me.btnBackup_3.tbShowNew = False
        Me.btnBackup_3.tbShowProgress = False
        Me.btnBackup_3.tbShowTip = True
        Me.btnBackup_3.tbShowToolTipOnButton = False
        Me.btnBackup_3.tbSplit = "102,1,2,9"
        Me.btnBackup_3.tbText = "微信备份还原"
        Me.btnBackup_3.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackup_3.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackup_3.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackup_3.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackup_3.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackup_3.tbTextMouseDownPlace = 0
        Me.btnBackup_3.tbToolTip = ""
        Me.btnBackup_3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBackup_3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackup_3.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackup_3.VisibleEx = True
        '
        'pnlContent
        '
        Me.pnlContent.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlContent.Location = New System.Drawing.Point(1898, 0)
        Me.pnlContent.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContent.Name = "pnlContent"
        Me.tblayoutMain.SetRowSpan(Me.pnlContent, 2)
        Me.pnlContent.Size = New System.Drawing.Size(1, 546)
        Me.pnlContent.TabIndex = 30
        '
        'pnlHeadDevice
        '
        Me.pnlHeadDevice.Controls.Add(Me.TbButton14)
        Me.pnlHeadDevice.Controls.Add(Me.TbButton7)
        Me.pnlHeadDevice.Controls.Add(Me.TbButton5)
        Me.pnlHeadDevice.Controls.Add(Me.TbButton1)
        Me.pnlHeadDevice.Controls.Add(Me.TbButton3)
        Me.pnlHeadDevice.Controls.Add(Me.btniCloud_1)
        Me.pnlHeadDevice.Controls.Add(Me.btnBackupEx)
        Me.pnlHeadDevice.Controls.Add(Me.btnDeviceEx)
        Me.pnlHeadDevice.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlHeadDevice.Location = New System.Drawing.Point(0, 0)
        Me.pnlHeadDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlHeadDevice.Name = "pnlHeadDevice"
        Me.pnlHeadDevice.Size = New System.Drawing.Size(949, 110)
        Me.pnlHeadDevice.TabIndex = 29
        '
        'TbButton14
        '
        Me.TbButton14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton14.BackColor = System.Drawing.Color.Transparent
        Me.TbButton14.BindingForm = Nothing
        Me.TbButton14.Location = New System.Drawing.Point(442, 100)
        Me.TbButton14.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton14.Name = "TbButton14"
        Me.TbButton14.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton14.Selectable = True
        Me.TbButton14.Size = New System.Drawing.Size(253, 10)
        Me.TbButton14.TabIndex = 26
        Me.TbButton14.tbAdriftIconWhenHover = False
        Me.TbButton14.tbAutoSize = False
        Me.TbButton14.tbAutoSizeEx = False
        Me.TbButton14.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_left
        Me.TbButton14.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton14.tbBadgeNumber = 0
        Me.TbButton14.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton14.tbEndEllipsis = False
        Me.TbButton14.tbIconHoldPlace = True
        Me.TbButton14.tbIconImage = Nothing
        Me.TbButton14.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton14.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton14.tbIconMore = False
        Me.TbButton14.tbIconMouseDown = Nothing
        Me.TbButton14.tbIconMouseHover = Nothing
        Me.TbButton14.tbIconMouseLeave = Nothing
        Me.TbButton14.tbIconPlaceText = 2
        Me.TbButton14.tbIconReadOnly = Nothing
        Me.TbButton14.tbImageMouseDown = Nothing
        Me.TbButton14.tbImageMouseHover = Nothing
        Me.TbButton14.tbImageMouseLeave = Nothing
        Me.TbButton14.tbProgressValue = 50
        Me.TbButton14.tbReadOnly = False
        Me.TbButton14.tbReadOnlyText = False
        Me.TbButton14.tbShadow = False
        Me.TbButton14.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton14.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton14.tbShowDot = False
        Me.TbButton14.tbShowMoreIconImg = CType(resources.GetObject("TbButton14.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton14.tbShowNew = False
        Me.TbButton14.tbShowProgress = False
        Me.TbButton14.tbShowTip = True
        Me.TbButton14.tbShowToolTipOnButton = False
        Me.TbButton14.tbSplit = "1,1,1,3"
        Me.TbButton14.tbText = ""
        Me.TbButton14.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton14.tbTextColor = System.Drawing.Color.White
        Me.TbButton14.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton14.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton14.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton14.tbTextMouseDownPlace = 0
        Me.TbButton14.tbToolTip = ""
        Me.TbButton14.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton14.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton14.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton14.VisibleEx = True
        '
        'TbButton7
        '
        Me.TbButton7.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton7.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.TbButton7.BindingForm = Nothing
        Me.TbButton7.Location = New System.Drawing.Point(696, 15)
        Me.TbButton7.Name = "TbButton7"
        Me.TbButton7.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton7.Selectable = True
        Me.TbButton7.Size = New System.Drawing.Size(1, 80)
        Me.TbButton7.TabIndex = 28
        Me.TbButton7.tbAdriftIconWhenHover = False
        Me.TbButton7.tbAutoSize = False
        Me.TbButton7.tbAutoSizeEx = False
        Me.TbButton7.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_restore_left
        Me.TbButton7.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton7.tbBadgeNumber = 0
        Me.TbButton7.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton7.tbEndEllipsis = False
        Me.TbButton7.tbIconHoldPlace = True
        Me.TbButton7.tbIconImage = Nothing
        Me.TbButton7.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton7.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton7.tbIconMore = False
        Me.TbButton7.tbIconMouseDown = Nothing
        Me.TbButton7.tbIconMouseHover = Nothing
        Me.TbButton7.tbIconMouseLeave = Nothing
        Me.TbButton7.tbIconPlaceText = 2
        Me.TbButton7.tbIconReadOnly = Nothing
        Me.TbButton7.tbImageMouseDown = Nothing
        Me.TbButton7.tbImageMouseHover = Nothing
        Me.TbButton7.tbImageMouseLeave = Nothing
        Me.TbButton7.tbProgressValue = 50
        Me.TbButton7.tbReadOnly = False
        Me.TbButton7.tbReadOnlyText = False
        Me.TbButton7.tbShadow = False
        Me.TbButton7.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton7.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton7.tbShowDot = False
        Me.TbButton7.tbShowMoreIconImg = CType(resources.GetObject("TbButton7.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton7.tbShowNew = False
        Me.TbButton7.tbShowProgress = False
        Me.TbButton7.tbShowTip = True
        Me.TbButton7.tbShowToolTipOnButton = False
        Me.TbButton7.tbSplit = "1,26,6,25"
        Me.TbButton7.tbText = ""
        Me.TbButton7.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton7.tbTextColor = System.Drawing.Color.White
        Me.TbButton7.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton7.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton7.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton7.tbTextMouseDownPlace = 0
        Me.TbButton7.tbToolTip = ""
        Me.TbButton7.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton7.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton7.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton7.VisibleEx = True
        '
        'TbButton5
        '
        Me.TbButton5.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton5.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.TbButton5.BindingForm = Nothing
        Me.TbButton5.Location = New System.Drawing.Point(440, 16)
        Me.TbButton5.Name = "TbButton5"
        Me.TbButton5.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton5.Selectable = True
        Me.TbButton5.Size = New System.Drawing.Size(1, 80)
        Me.TbButton5.TabIndex = 28
        Me.TbButton5.tbAdriftIconWhenHover = False
        Me.TbButton5.tbAutoSize = False
        Me.TbButton5.tbAutoSizeEx = False
        Me.TbButton5.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_restore_left
        Me.TbButton5.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton5.tbBadgeNumber = 0
        Me.TbButton5.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton5.tbEndEllipsis = False
        Me.TbButton5.tbIconHoldPlace = True
        Me.TbButton5.tbIconImage = Nothing
        Me.TbButton5.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton5.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton5.tbIconMore = False
        Me.TbButton5.tbIconMouseDown = Nothing
        Me.TbButton5.tbIconMouseHover = Nothing
        Me.TbButton5.tbIconMouseLeave = Nothing
        Me.TbButton5.tbIconPlaceText = 2
        Me.TbButton5.tbIconReadOnly = Nothing
        Me.TbButton5.tbImageMouseDown = Nothing
        Me.TbButton5.tbImageMouseHover = Nothing
        Me.TbButton5.tbImageMouseLeave = Nothing
        Me.TbButton5.tbProgressValue = 50
        Me.TbButton5.tbReadOnly = False
        Me.TbButton5.tbReadOnlyText = False
        Me.TbButton5.tbShadow = False
        Me.TbButton5.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton5.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton5.tbShowDot = False
        Me.TbButton5.tbShowMoreIconImg = CType(resources.GetObject("TbButton5.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton5.tbShowNew = False
        Me.TbButton5.tbShowProgress = False
        Me.TbButton5.tbShowTip = True
        Me.TbButton5.tbShowToolTipOnButton = False
        Me.TbButton5.tbSplit = "1,26,6,25"
        Me.TbButton5.tbText = ""
        Me.TbButton5.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton5.tbTextColor = System.Drawing.Color.White
        Me.TbButton5.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton5.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton5.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton5.tbTextMouseDownPlace = 0
        Me.TbButton5.tbToolTip = ""
        Me.TbButton5.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton5.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton5.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton5.VisibleEx = True
        '
        'TbButton1
        '
        Me.TbButton1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton1.BackColor = System.Drawing.Color.Transparent
        Me.TbButton1.BindingForm = Nothing
        Me.TbButton1.Location = New System.Drawing.Point(0, 100)
        Me.TbButton1.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton1.Name = "TbButton1"
        Me.TbButton1.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton1.Selectable = True
        Me.TbButton1.Size = New System.Drawing.Size(439, 10)
        Me.TbButton1.TabIndex = 26
        Me.TbButton1.tbAdriftIconWhenHover = False
        Me.TbButton1.tbAutoSize = False
        Me.TbButton1.tbAutoSizeEx = False
        Me.TbButton1.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_bg
        Me.TbButton1.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton1.tbBadgeNumber = 0
        Me.TbButton1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton1.tbEndEllipsis = False
        Me.TbButton1.tbIconHoldPlace = True
        Me.TbButton1.tbIconImage = Nothing
        Me.TbButton1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton1.tbIconMore = False
        Me.TbButton1.tbIconMouseDown = Nothing
        Me.TbButton1.tbIconMouseHover = Nothing
        Me.TbButton1.tbIconMouseLeave = Nothing
        Me.TbButton1.tbIconPlaceText = 2
        Me.TbButton1.tbIconReadOnly = Nothing
        Me.TbButton1.tbImageMouseDown = Nothing
        Me.TbButton1.tbImageMouseHover = Nothing
        Me.TbButton1.tbImageMouseLeave = Nothing
        Me.TbButton1.tbProgressValue = 50
        Me.TbButton1.tbReadOnly = False
        Me.TbButton1.tbReadOnlyText = False
        Me.TbButton1.tbShadow = False
        Me.TbButton1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton1.tbShowDot = False
        Me.TbButton1.tbShowMoreIconImg = CType(resources.GetObject("TbButton1.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton1.tbShowNew = False
        Me.TbButton1.tbShowProgress = False
        Me.TbButton1.tbShowTip = True
        Me.TbButton1.tbShowToolTipOnButton = False
        Me.TbButton1.tbSplit = "72,1,1,11"
        Me.TbButton1.tbText = ""
        Me.TbButton1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.tbTextColor = System.Drawing.Color.White
        Me.TbButton1.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton1.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton1.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton1.tbTextMouseDownPlace = 0
        Me.TbButton1.tbToolTip = ""
        Me.TbButton1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton1.VisibleEx = True
        '
        'TbButton3
        '
        Me.TbButton3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton3.BackColor = System.Drawing.Color.Transparent
        Me.TbButton3.BindingForm = Nothing
        Me.TbButton3.Location = New System.Drawing.Point(698, 100)
        Me.TbButton3.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton3.Name = "TbButton3"
        Me.TbButton3.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton3.Selectable = True
        Me.TbButton3.Size = New System.Drawing.Size(251, 10)
        Me.TbButton3.TabIndex = 26
        Me.TbButton3.tbAdriftIconWhenHover = False
        Me.TbButton3.tbAutoSize = False
        Me.TbButton3.tbAutoSizeEx = False
        Me.TbButton3.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_left
        Me.TbButton3.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton3.tbBadgeNumber = 0
        Me.TbButton3.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton3.tbEndEllipsis = False
        Me.TbButton3.tbIconHoldPlace = True
        Me.TbButton3.tbIconImage = Nothing
        Me.TbButton3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton3.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton3.tbIconMore = False
        Me.TbButton3.tbIconMouseDown = Nothing
        Me.TbButton3.tbIconMouseHover = Nothing
        Me.TbButton3.tbIconMouseLeave = Nothing
        Me.TbButton3.tbIconPlaceText = 2
        Me.TbButton3.tbIconReadOnly = Nothing
        Me.TbButton3.tbImageMouseDown = Nothing
        Me.TbButton3.tbImageMouseHover = Nothing
        Me.TbButton3.tbImageMouseLeave = Nothing
        Me.TbButton3.tbProgressValue = 50
        Me.TbButton3.tbReadOnly = False
        Me.TbButton3.tbReadOnlyText = False
        Me.TbButton3.tbShadow = False
        Me.TbButton3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton3.tbShowDot = False
        Me.TbButton3.tbShowMoreIconImg = CType(resources.GetObject("TbButton3.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton3.tbShowNew = False
        Me.TbButton3.tbShowProgress = False
        Me.TbButton3.tbShowTip = True
        Me.TbButton3.tbShowToolTipOnButton = False
        Me.TbButton3.tbSplit = "1,1,1,3"
        Me.TbButton3.tbText = ""
        Me.TbButton3.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton3.tbTextColor = System.Drawing.Color.White
        Me.TbButton3.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton3.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton3.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton3.tbTextMouseDownPlace = 0
        Me.TbButton3.tbToolTip = ""
        Me.TbButton3.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton3.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton3.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton3.VisibleEx = True
        '
        'btniCloud_1
        '
        Me.btniCloud_1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btniCloud_1.BackColor = System.Drawing.Color.Transparent
        Me.btniCloud_1.BindingForm = Nothing
        Me.btniCloud_1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btniCloud_1.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btniCloud_1.Location = New System.Drawing.Point(699, 10)
        Me.btniCloud_1.Name = "btniCloud_1"
        Me.btniCloud_1.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btniCloud_1.Selectable = True
        Me.btniCloud_1.Size = New System.Drawing.Size(248, 90)
        Me.btniCloud_1.TabIndex = 27
        Me.btniCloud_1.tbAdriftIconWhenHover = False
        Me.btniCloud_1.tbAutoSize = False
        Me.btniCloud_1.tbAutoSizeEx = False
        Me.btniCloud_1.tbBackgroundImage = Nothing
        Me.btniCloud_1.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btniCloud_1.tbBadgeNumber = 0
        Me.btniCloud_1.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloud_1.tbEndEllipsis = False
        Me.btniCloud_1.tbIconHoldPlace = True
        Me.btniCloud_1.tbIconImage = Global.iTong.My.Resources.Resources.wechat_icloud
        Me.btniCloud_1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniCloud_1.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloud_1.tbIconMore = False
        Me.btniCloud_1.tbIconMouseDown = Nothing
        Me.btniCloud_1.tbIconMouseHover = Nothing
        Me.btniCloud_1.tbIconMouseLeave = Nothing
        Me.btniCloud_1.tbIconPlaceText = 2
        Me.btniCloud_1.tbIconReadOnly = Nothing
        Me.btniCloud_1.tbImageMouseDown = Nothing
        Me.btniCloud_1.tbImageMouseHover = Nothing
        Me.btniCloud_1.tbImageMouseLeave = Nothing
        Me.btniCloud_1.tbProgressValue = 50
        Me.btniCloud_1.tbReadOnly = False
        Me.btniCloud_1.tbReadOnlyText = False
        Me.btniCloud_1.tbShadow = False
        Me.btniCloud_1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloud_1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloud_1.tbShowDot = False
        Me.btniCloud_1.tbShowMoreIconImg = Nothing
        Me.btniCloud_1.tbShowNew = False
        Me.btniCloud_1.tbShowProgress = False
        Me.btniCloud_1.tbShowTip = True
        Me.btniCloud_1.tbShowToolTipOnButton = False
        Me.btniCloud_1.tbSplit = "1,3,6,5"
        Me.btniCloud_1.tbText = "通过iCloud备份恢复"
        Me.btniCloud_1.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloud_1.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud_1.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud_1.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud_1.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud_1.tbTextMouseDownPlace = 0
        Me.btniCloud_1.tbToolTip = ""
        Me.btniCloud_1.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloud_1.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloud_1.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloud_1.VisibleEx = True
        '
        'btnBackupEx
        '
        Me.btnBackupEx.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBackupEx.BackColor = System.Drawing.Color.Transparent
        Me.btnBackupEx.BindingForm = Nothing
        Me.btnBackupEx.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnBackupEx.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btnBackupEx.Location = New System.Drawing.Point(442, 10)
        Me.btnBackupEx.Name = "btnBackupEx"
        Me.btnBackupEx.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnBackupEx.Selectable = True
        Me.btnBackupEx.Size = New System.Drawing.Size(248, 90)
        Me.btnBackupEx.TabIndex = 27
        Me.btnBackupEx.tbAdriftIconWhenHover = False
        Me.btnBackupEx.tbAutoSize = False
        Me.btnBackupEx.tbAutoSizeEx = False
        Me.btnBackupEx.tbBackgroundImage = Nothing
        Me.btnBackupEx.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnBackupEx.tbBadgeNumber = 0
        Me.btnBackupEx.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackupEx.tbEndEllipsis = False
        Me.btnBackupEx.tbIconHoldPlace = True
        Me.btnBackupEx.tbIconImage = Global.iTong.My.Resources.Resources.wechat_restore_new
        Me.btnBackupEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackupEx.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBackupEx.tbIconMore = False
        Me.btnBackupEx.tbIconMouseDown = Nothing
        Me.btnBackupEx.tbIconMouseHover = Nothing
        Me.btnBackupEx.tbIconMouseLeave = Nothing
        Me.btnBackupEx.tbIconPlaceText = 2
        Me.btnBackupEx.tbIconReadOnly = Nothing
        Me.btnBackupEx.tbImageMouseDown = Nothing
        Me.btnBackupEx.tbImageMouseHover = Nothing
        Me.btnBackupEx.tbImageMouseLeave = Nothing
        Me.btnBackupEx.tbProgressValue = 50
        Me.btnBackupEx.tbReadOnly = False
        Me.btnBackupEx.tbReadOnlyText = False
        Me.btnBackupEx.tbShadow = False
        Me.btnBackupEx.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackupEx.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackupEx.tbShowDot = False
        Me.btnBackupEx.tbShowMoreIconImg = Nothing
        Me.btnBackupEx.tbShowNew = False
        Me.btnBackupEx.tbShowProgress = False
        Me.btnBackupEx.tbShowTip = True
        Me.btnBackupEx.tbShowToolTipOnButton = False
        Me.btnBackupEx.tbSplit = "1,3,6,5"
        Me.btnBackupEx.tbText = "微信备份还原"
        Me.btnBackupEx.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackupEx.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackupEx.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackupEx.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackupEx.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackupEx.tbTextMouseDownPlace = 0
        Me.btnBackupEx.tbToolTip = ""
        Me.btnBackupEx.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBackupEx.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackupEx.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackupEx.VisibleEx = True
        '
        'btnDeviceEx
        '
        Me.btnDeviceEx.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDeviceEx.BackColor = System.Drawing.Color.Transparent
        Me.btnDeviceEx.BindingForm = Nothing
        Me.btnDeviceEx.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDeviceEx.Font = New System.Drawing.Font("宋体", 14.0!)
        Me.btnDeviceEx.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnDeviceEx.Location = New System.Drawing.Point(21, 10)
        Me.btnDeviceEx.Name = "btnDeviceEx"
        Me.btnDeviceEx.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnDeviceEx.Selectable = True
        Me.btnDeviceEx.Size = New System.Drawing.Size(401, 90)
        Me.btnDeviceEx.TabIndex = 27
        Me.btnDeviceEx.tbAdriftIconWhenHover = False
        Me.btnDeviceEx.tbAutoSize = False
        Me.btnDeviceEx.tbAutoSizeEx = False
        Me.btnDeviceEx.tbBackgroundImage = Nothing
        Me.btnDeviceEx.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDeviceEx.tbBadgeNumber = 0
        Me.btnDeviceEx.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDeviceEx.tbEndEllipsis = False
        Me.btnDeviceEx.tbIconHoldPlace = True
        Me.btnDeviceEx.tbIconImage = Global.iTong.My.Resources.Resources.wechat_device_new
        Me.btnDeviceEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDeviceEx.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDeviceEx.tbIconMore = False
        Me.btnDeviceEx.tbIconMouseDown = Nothing
        Me.btnDeviceEx.tbIconMouseHover = Nothing
        Me.btnDeviceEx.tbIconMouseLeave = Nothing
        Me.btnDeviceEx.tbIconPlaceText = 2
        Me.btnDeviceEx.tbIconReadOnly = Nothing
        Me.btnDeviceEx.tbImageMouseDown = Nothing
        Me.btnDeviceEx.tbImageMouseHover = Nothing
        Me.btnDeviceEx.tbImageMouseLeave = Nothing
        Me.btnDeviceEx.tbProgressValue = 50
        Me.btnDeviceEx.tbReadOnly = False
        Me.btnDeviceEx.tbReadOnlyText = False
        Me.btnDeviceEx.tbShadow = False
        Me.btnDeviceEx.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDeviceEx.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDeviceEx.tbShowDot = False
        Me.btnDeviceEx.tbShowMoreIconImg = Nothing
        Me.btnDeviceEx.tbShowNew = False
        Me.btnDeviceEx.tbShowProgress = False
        Me.btnDeviceEx.tbShowTip = True
        Me.btnDeviceEx.tbShowToolTipOnButton = True
        Me.btnDeviceEx.tbSplit = "3,3,3,3"
        Me.btnDeviceEx.tbText = "通过扫描设备获取信息"
        Me.btnDeviceEx.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDeviceEx.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnDeviceEx.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnDeviceEx.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnDeviceEx.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnDeviceEx.tbTextMouseDownPlace = 0
        Me.btnDeviceEx.tbToolTip = "通过对设备微信安装目录的扫描，获取包括文本、图片、视频等数据。展示出来并可进行相应的管理。"
        Me.btnDeviceEx.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDeviceEx.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDeviceEx.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDeviceEx.VisibleEx = True
        '
        'pnlDevice
        '
        Me.pnlDevice.Controls.Add(Me.pnlTip)
        Me.pnlDevice.Controls.Add(Me.pnlDeviceConnect)
        Me.pnlDevice.Controls.Add(Me.pnlDeviceUnConnect)
        Me.pnlDevice.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlDevice.Location = New System.Drawing.Point(0, 110)
        Me.pnlDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlDevice.Name = "pnlDevice"
        Me.pnlDevice.Size = New System.Drawing.Size(949, 436)
        Me.pnlDevice.TabIndex = 1
        '
        'pnlTip
        '
        Me.pnlTip.Controls.Add(Me.TbLabel1)
        Me.pnlTip.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlTip.Location = New System.Drawing.Point(0, 372)
        Me.pnlTip.Name = "pnlTip"
        Me.pnlTip.Size = New System.Drawing.Size(949, 64)
        Me.pnlTip.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlTip.TabIndex = 42
        Me.pnlTip.tbBackgroundImage = Nothing
        Me.pnlTip.tbShowWatermark = False
        Me.pnlTip.tbSplit = "0,0,0,0"
        Me.pnlTip.tbWatermark = Nothing
        Me.pnlTip.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlTip.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'TbLabel1
        '
        Me.TbLabel1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.TbLabel1.AutoSize = True
        Me.TbLabel1.Cursor = System.Windows.Forms.Cursors.Hand
        Me.TbLabel1.ForeColor = System.Drawing.Color.Red
        Me.TbLabel1.Location = New System.Drawing.Point(-3, 14)
        Me.TbLabel1.Name = "TbLabel1"
        Me.TbLabel1.Size = New System.Drawing.Size(707, 36)
        Me.TbLabel1.TabIndex = 42
        Me.TbLabel1.tbAdriftWhenHover = False
        Me.TbLabel1.tbAutoEllipsis = False
        Me.TbLabel1.tbAutoSize = True
        Me.TbLabel1.tbHideImage = False
        Me.TbLabel1.tbIconImage = Nothing
        Me.TbLabel1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel1.tbIconPlaceText = 5
        Me.TbLabel1.tbShadow = False
        Me.TbLabel1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel1.tbShowScrolling = False
        Me.TbLabel1.Text = "微信分身版：是指一部手机同时安装多个微信，使其能同时登入多个微信帐号。" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "如果你想要安装分身版、如果你想要管理非同步助手渠道获取的分身版，点击此处加入QQ群（58" & _
    "7033524），联系管理员或者群主咨询" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'pnlDeviceConnect
        '
        Me.pnlDeviceConnect.Controls.Add(Me.pbDevice)
        Me.pnlDeviceConnect.Controls.Add(Me.btnStartLoadDevice)
        Me.pnlDeviceConnect.Controls.Add(Me.lblBackupPwdMsg)
        Me.pnlDeviceConnect.Controls.Add(Me.lblNameValue)
        Me.pnlDeviceConnect.Controls.Add(Me.llblDeletePwd)
        Me.pnlDeviceConnect.Controls.Add(Me.lblStartLoadDevice)
        Me.pnlDeviceConnect.Controls.Add(Me.lblFirmwareVersion)
        Me.pnlDeviceConnect.Controls.Add(Me.lblDeviceType)
        Me.pnlDeviceConnect.Controls.Add(Me.lblDericeType)
        Me.pnlDeviceConnect.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlDeviceConnect.Location = New System.Drawing.Point(0, 0)
        Me.pnlDeviceConnect.Name = "pnlDeviceConnect"
        Me.pnlDeviceConnect.Size = New System.Drawing.Size(949, 436)
        Me.pnlDeviceConnect.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDeviceConnect.TabIndex = 43
        Me.pnlDeviceConnect.tbBackgroundImage = Nothing
        Me.pnlDeviceConnect.tbShowWatermark = False
        Me.pnlDeviceConnect.tbSplit = "0,0,0,0"
        Me.pnlDeviceConnect.tbWatermark = Nothing
        Me.pnlDeviceConnect.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDeviceConnect.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pbDevice
        '
        Me.pbDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbDevice.Location = New System.Drawing.Point(193, 48)
        Me.pbDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.pbDevice.Name = "pbDevice"
        Me.pbDevice.Size = New System.Drawing.Size(240, 321)
        Me.pbDevice.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.pbDevice.TabIndex = 1
        Me.pbDevice.TabStop = False
        '
        'btnStartLoadDevice
        '
        Me.btnStartLoadDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnStartLoadDevice.BackColor = System.Drawing.Color.Transparent
        Me.btnStartLoadDevice.BindingForm = Nothing
        Me.btnStartLoadDevice.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnStartLoadDevice.Location = New System.Drawing.Point(441, 317)
        Me.btnStartLoadDevice.Name = "btnStartLoadDevice"
        Me.btnStartLoadDevice.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnStartLoadDevice.Selectable = True
        Me.btnStartLoadDevice.Size = New System.Drawing.Size(129, 36)
        Me.btnStartLoadDevice.TabIndex = 24
        Me.btnStartLoadDevice.tbAdriftIconWhenHover = False
        Me.btnStartLoadDevice.tbAutoSize = False
        Me.btnStartLoadDevice.tbAutoSizeEx = False
        Me.btnStartLoadDevice.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnStartLoadDevice.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnStartLoadDevice.tbBadgeNumber = 0
        Me.btnStartLoadDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStartLoadDevice.tbEndEllipsis = False
        Me.btnStartLoadDevice.tbIconHoldPlace = True
        Me.btnStartLoadDevice.tbIconImage = Nothing
        Me.btnStartLoadDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartLoadDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStartLoadDevice.tbIconMore = False
        Me.btnStartLoadDevice.tbIconMouseDown = Nothing
        Me.btnStartLoadDevice.tbIconMouseHover = Nothing
        Me.btnStartLoadDevice.tbIconMouseLeave = Nothing
        Me.btnStartLoadDevice.tbIconPlaceText = 2
        Me.btnStartLoadDevice.tbIconReadOnly = Nothing
        Me.btnStartLoadDevice.tbImageMouseDown = Nothing
        Me.btnStartLoadDevice.tbImageMouseHover = Nothing
        Me.btnStartLoadDevice.tbImageMouseLeave = Nothing
        Me.btnStartLoadDevice.tbProgressValue = 50
        Me.btnStartLoadDevice.tbReadOnly = False
        Me.btnStartLoadDevice.tbReadOnlyText = False
        Me.btnStartLoadDevice.tbShadow = False
        Me.btnStartLoadDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStartLoadDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStartLoadDevice.tbShowDot = False
        Me.btnStartLoadDevice.tbShowMoreIconImg = Nothing
        Me.btnStartLoadDevice.tbShowNew = False
        Me.btnStartLoadDevice.tbShowProgress = False
        Me.btnStartLoadDevice.tbShowTip = True
        Me.btnStartLoadDevice.tbShowToolTipOnButton = False
        Me.btnStartLoadDevice.tbSplit = "13,11,13,11"
        Me.btnStartLoadDevice.tbText = "开始"
        Me.btnStartLoadDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartLoadDevice.tbTextColor = System.Drawing.Color.White
        Me.btnStartLoadDevice.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStartLoadDevice.tbTextColorDown = System.Drawing.Color.White
        Me.btnStartLoadDevice.tbTextColorHover = System.Drawing.Color.White
        Me.btnStartLoadDevice.tbTextMouseDownPlace = 0
        Me.btnStartLoadDevice.tbToolTip = ""
        Me.btnStartLoadDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStartLoadDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStartLoadDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartLoadDevice.VisibleEx = True
        '
        'lblBackupPwdMsg
        '
        Me.lblBackupPwdMsg.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblBackupPwdMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblBackupPwdMsg.Location = New System.Drawing.Point(440, 224)
        Me.lblBackupPwdMsg.Name = "lblBackupPwdMsg"
        Me.lblBackupPwdMsg.Size = New System.Drawing.Size(502, 47)
        Me.lblBackupPwdMsg.TabIndex = 41
        Me.lblBackupPwdMsg.tbAdriftWhenHover = False
        Me.lblBackupPwdMsg.tbAutoEllipsis = False
        Me.lblBackupPwdMsg.tbAutoSize = False
        Me.lblBackupPwdMsg.tbHideImage = False
        Me.lblBackupPwdMsg.tbIconImage = Nothing
        Me.lblBackupPwdMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblBackupPwdMsg.tbIconPlaceText = 5
        Me.lblBackupPwdMsg.tbShadow = False
        Me.lblBackupPwdMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblBackupPwdMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblBackupPwdMsg.tbShowScrolling = False
        Me.lblBackupPwdMsg.Text = "此设备未设置备份密码，备份的文件不会被加密，在恢复到设备时无需输入密码，" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "且备份的文件可以查看其内容。"
        '
        'lblNameValue
        '
        Me.lblNameValue.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblNameValue.Font = New System.Drawing.Font("宋体", 15.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblNameValue.Location = New System.Drawing.Point(437, 59)
        Me.lblNameValue.Name = "lblNameValue"
        Me.lblNameValue.Size = New System.Drawing.Size(593, 34)
        Me.lblNameValue.TabIndex = 25
        Me.lblNameValue.tbAdriftWhenHover = False
        Me.lblNameValue.tbAutoEllipsis = False
        Me.lblNameValue.tbAutoSize = False
        Me.lblNameValue.tbHideImage = False
        Me.lblNameValue.tbIconImage = Nothing
        Me.lblNameValue.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblNameValue.tbIconPlaceText = 5
        Me.lblNameValue.tbShadow = False
        Me.lblNameValue.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblNameValue.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblNameValue.tbShowScrolling = False
        Me.lblNameValue.Text = "iPhone"
        Me.lblNameValue.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'llblDeletePwd
        '
        Me.llblDeletePwd.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblDeletePwd.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.llblDeletePwd.AutoSize = True
        Me.llblDeletePwd.BackColor = System.Drawing.Color.Transparent
        Me.llblDeletePwd.Cursor = System.Windows.Forms.Cursors.Hand
        Me.llblDeletePwd.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.llblDeletePwd.ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblDeletePwd.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.llblDeletePwd.LinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblDeletePwd.Location = New System.Drawing.Point(440, 283)
        Me.llblDeletePwd.Name = "llblDeletePwd"
        Me.llblDeletePwd.Size = New System.Drawing.Size(77, 12)
        Me.llblDeletePwd.TabIndex = 40
        Me.llblDeletePwd.TabStop = True
        Me.llblDeletePwd.Text = "删除备份密码"
        Me.llblDeletePwd.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.llblDeletePwd.Visible = False
        Me.llblDeletePwd.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        '
        'lblStartLoadDevice
        '
        Me.lblStartLoadDevice.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblStartLoadDevice.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblStartLoadDevice.ForeColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.lblStartLoadDevice.Location = New System.Drawing.Point(353, 383)
        Me.lblStartLoadDevice.Name = "lblStartLoadDevice"
        Me.lblStartLoadDevice.Size = New System.Drawing.Size(302, 16)
        Me.lblStartLoadDevice.TabIndex = 26
        Me.lblStartLoadDevice.tbAdriftWhenHover = False
        Me.lblStartLoadDevice.tbAutoEllipsis = False
        Me.lblStartLoadDevice.tbAutoSize = False
        Me.lblStartLoadDevice.tbHideImage = False
        Me.lblStartLoadDevice.tbIconImage = Nothing
        Me.lblStartLoadDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblStartLoadDevice.tbIconPlaceText = 5
        Me.lblStartLoadDevice.tbShadow = False
        Me.lblStartLoadDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblStartLoadDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblStartLoadDevice.tbShowScrolling = False
        Me.lblStartLoadDevice.TextAlign = System.Drawing.ContentAlignment.TopCenter
        '
        'lblFirmwareVersion
        '
        Me.lblFirmwareVersion.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFirmwareVersion.AutoSize = True
        Me.lblFirmwareVersion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblFirmwareVersion.Location = New System.Drawing.Point(441, 190)
        Me.lblFirmwareVersion.Name = "lblFirmwareVersion"
        Me.lblFirmwareVersion.Size = New System.Drawing.Size(53, 12)
        Me.lblFirmwareVersion.TabIndex = 27
        Me.lblFirmwareVersion.tbAdriftWhenHover = False
        Me.lblFirmwareVersion.tbAutoEllipsis = False
        Me.lblFirmwareVersion.tbAutoSize = True
        Me.lblFirmwareVersion.tbHideImage = False
        Me.lblFirmwareVersion.tbIconImage = Nothing
        Me.lblFirmwareVersion.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFirmwareVersion.tbIconPlaceText = 5
        Me.lblFirmwareVersion.tbShadow = False
        Me.lblFirmwareVersion.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFirmwareVersion.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFirmwareVersion.tbShowScrolling = False
        Me.lblFirmwareVersion.Text = "固件版本"
        '
        'lblDeviceType
        '
        Me.lblDeviceType.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDeviceType.AutoSize = True
        Me.lblDeviceType.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblDeviceType.Location = New System.Drawing.Point(441, 117)
        Me.lblDeviceType.Name = "lblDeviceType"
        Me.lblDeviceType.Size = New System.Drawing.Size(53, 12)
        Me.lblDeviceType.TabIndex = 27
        Me.lblDeviceType.tbAdriftWhenHover = False
        Me.lblDeviceType.tbAutoEllipsis = False
        Me.lblDeviceType.tbAutoSize = True
        Me.lblDeviceType.tbHideImage = False
        Me.lblDeviceType.tbIconImage = Nothing
        Me.lblDeviceType.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDeviceType.tbIconPlaceText = 5
        Me.lblDeviceType.tbShadow = False
        Me.lblDeviceType.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDeviceType.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDeviceType.tbShowScrolling = False
        Me.lblDeviceType.Text = "设备类型"
        '
        'lblDericeType
        '
        Me.lblDericeType.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDericeType.AutoSize = True
        Me.lblDericeType.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblDericeType.Location = New System.Drawing.Point(440, 151)
        Me.lblDericeType.Name = "lblDericeType"
        Me.lblDericeType.Size = New System.Drawing.Size(53, 12)
        Me.lblDericeType.TabIndex = 27
        Me.lblDericeType.tbAdriftWhenHover = False
        Me.lblDericeType.tbAutoEllipsis = False
        Me.lblDericeType.tbAutoSize = True
        Me.lblDericeType.tbHideImage = False
        Me.lblDericeType.tbIconImage = Nothing
        Me.lblDericeType.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblDericeType.tbIconPlaceText = 5
        Me.lblDericeType.tbShadow = False
        Me.lblDericeType.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblDericeType.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblDericeType.tbShowScrolling = False
        Me.lblDericeType.Text = "设备型号"
        '
        'pnlDeviceUnConnect
        '
        Me.pnlDeviceUnConnect.Controls.Add(Me.lblDevNoConn)
        Me.pnlDeviceUnConnect.Controls.Add(Me.picDefault)
        Me.pnlDeviceUnConnect.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlDeviceUnConnect.Location = New System.Drawing.Point(0, 0)
        Me.pnlDeviceUnConnect.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlDeviceUnConnect.Name = "pnlDeviceUnConnect"
        Me.pnlDeviceUnConnect.Size = New System.Drawing.Size(949, 436)
        Me.pnlDeviceUnConnect.TabIndex = 44
        '
        'lblDevNoConn
        '
        Me.lblDevNoConn.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblDevNoConn.AutoSize = True
        Me.lblDevNoConn.Font = New System.Drawing.Font("宋体", 21.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDevNoConn.Location = New System.Drawing.Point(157, 34)
        Me.lblDevNoConn.Name = "lblDevNoConn"
        Me.lblDevNoConn.Size = New System.Drawing.Size(158, 29)
        Me.lblDevNoConn.TabIndex = 7
        Me.lblDevNoConn.Text = "设备未连接"
        Me.lblDevNoConn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picDefault
        '
        Me.picDefault.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picDefault.BackColor = System.Drawing.Color.Transparent
        Me.picDefault.Image = Global.iTong.My.Resources.Resources.new_device_default
        Me.picDefault.Location = New System.Drawing.Point(162, 136)
        Me.picDefault.Name = "picDefault"
        Me.picDefault.Size = New System.Drawing.Size(630, 266)
        Me.picDefault.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picDefault.TabIndex = 8
        Me.picDefault.TabStop = False
        '
        'pnlBackup
        '
        Me.pnlBackup.Controls.Add(Me.btnDeleteTemp)
        Me.pnlBackup.Controls.Add(Me.btnChangeSKU)
        Me.pnlBackup.Controls.Add(Me.btnImport)
        Me.pnlBackup.Controls.Add(Me.btnRepair)
        Me.pnlBackup.Controls.Add(Me.btnRefresh)
        Me.pnlBackup.Controls.Add(Me.btnWeChatDataMigration)
        Me.pnlBackup.Controls.Add(Me.btnBackupStart)
        Me.pnlBackup.Controls.Add(Me.pnlGridBackList)
        Me.pnlBackup.Controls.Add(Me.lblTitle)
        Me.pnlBackup.Controls.Add(Me.lblPrompt)
        Me.pnlBackup.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlBackup.Location = New System.Drawing.Point(949, 110)
        Me.pnlBackup.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBackup.Name = "pnlBackup"
        Me.pnlBackup.Size = New System.Drawing.Size(949, 436)
        Me.pnlBackup.TabIndex = 2
        '
        'btnDeleteTemp
        '
        Me.btnDeleteTemp.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnDeleteTemp.BackColor = System.Drawing.Color.Transparent
        Me.btnDeleteTemp.BindingForm = Nothing
        Me.btnDeleteTemp.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnDeleteTemp.Location = New System.Drawing.Point(607, 28)
        Me.btnDeleteTemp.Name = "btnDeleteTemp"
        Me.btnDeleteTemp.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDeleteTemp.Selectable = True
        Me.btnDeleteTemp.Size = New System.Drawing.Size(81, 23)
        Me.btnDeleteTemp.TabIndex = 88
        Me.btnDeleteTemp.tbAdriftIconWhenHover = False
        Me.btnDeleteTemp.tbAutoSize = True
        Me.btnDeleteTemp.tbAutoSizeEx = True
        Me.btnDeleteTemp.tbBackgroundImage = Nothing
        Me.btnDeleteTemp.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDeleteTemp.tbBadgeNumber = 0
        Me.btnDeleteTemp.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDeleteTemp.tbEndEllipsis = False
        Me.btnDeleteTemp.tbIconHoldPlace = True
        Me.btnDeleteTemp.tbIconImage = Global.iTong.My.Resources.Resources.btn_delete_4
        Me.btnDeleteTemp.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDeleteTemp.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnDeleteTemp.tbIconMore = False
        Me.btnDeleteTemp.tbIconMouseDown = Nothing
        Me.btnDeleteTemp.tbIconMouseHover = Nothing
        Me.btnDeleteTemp.tbIconMouseLeave = Nothing
        Me.btnDeleteTemp.tbIconPlaceText = 2
        Me.btnDeleteTemp.tbIconReadOnly = Nothing
        Me.btnDeleteTemp.tbImageMouseDown = Nothing
        Me.btnDeleteTemp.tbImageMouseHover = Nothing
        Me.btnDeleteTemp.tbImageMouseLeave = Nothing
        Me.btnDeleteTemp.tbProgressValue = 50
        Me.btnDeleteTemp.tbReadOnly = False
        Me.btnDeleteTemp.tbReadOnlyText = False
        Me.btnDeleteTemp.tbShadow = False
        Me.btnDeleteTemp.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDeleteTemp.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDeleteTemp.tbShowDot = False
        Me.btnDeleteTemp.tbShowMoreIconImg = CType(resources.GetObject("btnDeleteTemp.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDeleteTemp.tbShowNew = False
        Me.btnDeleteTemp.tbShowProgress = False
        Me.btnDeleteTemp.tbShowTip = True
        Me.btnDeleteTemp.tbShowToolTipOnButton = False
        Me.btnDeleteTemp.tbSplit = "3,3,3,3"
        Me.btnDeleteTemp.tbText = "清理缓存"
        Me.btnDeleteTemp.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteTemp.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnDeleteTemp.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnDeleteTemp.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDeleteTemp.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnDeleteTemp.tbTextMouseDownPlace = 2
        Me.btnDeleteTemp.tbToolTip = ""
        Me.btnDeleteTemp.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDeleteTemp.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDeleteTemp.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDeleteTemp.VisibleEx = True
        '
        'btnChangeSKU
        '
        Me.btnChangeSKU.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnChangeSKU.BackColor = System.Drawing.Color.Transparent
        Me.btnChangeSKU.BindingForm = Nothing
        Me.btnChangeSKU.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnChangeSKU.Location = New System.Drawing.Point(657, 393)
        Me.btnChangeSKU.Name = "btnChangeSKU"
        Me.btnChangeSKU.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnChangeSKU.Selectable = True
        Me.btnChangeSKU.Size = New System.Drawing.Size(111, 36)
        Me.btnChangeSKU.TabIndex = 81
        Me.btnChangeSKU.tbAdriftIconWhenHover = False
        Me.btnChangeSKU.tbAutoSize = False
        Me.btnChangeSKU.tbAutoSizeEx = False
        Me.btnChangeSKU.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnChangeSKU.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnChangeSKU.tbBadgeNumber = 0
        Me.btnChangeSKU.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnChangeSKU.tbEndEllipsis = False
        Me.btnChangeSKU.tbIconHoldPlace = True
        Me.btnChangeSKU.tbIconImage = Nothing
        Me.btnChangeSKU.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnChangeSKU.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnChangeSKU.tbIconMore = False
        Me.btnChangeSKU.tbIconMouseDown = Nothing
        Me.btnChangeSKU.tbIconMouseHover = Nothing
        Me.btnChangeSKU.tbIconMouseLeave = Nothing
        Me.btnChangeSKU.tbIconPlaceText = 2
        Me.btnChangeSKU.tbIconReadOnly = Nothing
        Me.btnChangeSKU.tbImageMouseDown = Nothing
        Me.btnChangeSKU.tbImageMouseHover = Nothing
        Me.btnChangeSKU.tbImageMouseLeave = Nothing
        Me.btnChangeSKU.tbProgressValue = 50
        Me.btnChangeSKU.tbReadOnly = False
        Me.btnChangeSKU.tbReadOnlyText = False
        Me.btnChangeSKU.tbShadow = False
        Me.btnChangeSKU.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnChangeSKU.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnChangeSKU.tbShowDot = False
        Me.btnChangeSKU.tbShowMoreIconImg = Nothing
        Me.btnChangeSKU.tbShowNew = False
        Me.btnChangeSKU.tbShowProgress = False
        Me.btnChangeSKU.tbShowTip = True
        Me.btnChangeSKU.tbShowToolTipOnButton = False
        Me.btnChangeSKU.tbSplit = "13,11,13,11"
        Me.btnChangeSKU.tbText = "数据迁移"
        Me.btnChangeSKU.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnChangeSKU.tbTextColor = System.Drawing.Color.White
        Me.btnChangeSKU.tbTextColorDisable = System.Drawing.Color.White
        Me.btnChangeSKU.tbTextColorDown = System.Drawing.Color.White
        Me.btnChangeSKU.tbTextColorHover = System.Drawing.Color.White
        Me.btnChangeSKU.tbTextMouseDownPlace = 0
        Me.btnChangeSKU.tbToolTip = ""
        Me.btnChangeSKU.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnChangeSKU.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnChangeSKU.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnChangeSKU.Visible = False
        Me.btnChangeSKU.VisibleEx = True
        '
        'btnImport
        '
        Me.btnImport.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnImport.BackColor = System.Drawing.Color.Transparent
        Me.btnImport.BindingForm = Nothing
        Me.btnImport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImport.Location = New System.Drawing.Point(821, 28)
        Me.btnImport.Margin = New System.Windows.Forms.Padding(0)
        Me.btnImport.Name = "btnImport"
        Me.btnImport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImport.Selectable = True
        Me.btnImport.Size = New System.Drawing.Size(87, 23)
        Me.btnImport.TabIndex = 87
        Me.btnImport.tbAdriftIconWhenHover = False
        Me.btnImport.tbAutoSize = False
        Me.btnImport.tbAutoSizeEx = True
        Me.btnImport.tbBackgroundImage = Nothing
        Me.btnImport.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnImport.tbBadgeNumber = 0
        Me.btnImport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImport.tbEndEllipsis = False
        Me.btnImport.tbIconHoldPlace = True
        Me.btnImport.tbIconImage = Global.iTong.My.Resources.Resources.btn_import_4
        Me.btnImport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbIconMore = False
        Me.btnImport.tbIconMouseDown = Nothing
        Me.btnImport.tbIconMouseHover = Nothing
        Me.btnImport.tbIconMouseLeave = Nothing
        Me.btnImport.tbIconPlaceText = 2
        Me.btnImport.tbIconReadOnly = Nothing
        Me.btnImport.tbImageMouseDown = Nothing
        Me.btnImport.tbImageMouseHover = Nothing
        Me.btnImport.tbImageMouseLeave = Nothing
        Me.btnImport.tbProgressValue = 50
        Me.btnImport.tbReadOnly = False
        Me.btnImport.tbReadOnlyText = False
        Me.btnImport.tbShadow = False
        Me.btnImport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImport.tbShowDot = False
        Me.btnImport.tbShowMoreIconImg = CType(resources.GetObject("btnImport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImport.tbShowNew = False
        Me.btnImport.tbShowProgress = False
        Me.btnImport.tbShowTip = True
        Me.btnImport.tbShowToolTipOnButton = False
        Me.btnImport.tbSplit = "3,3,3,3"
        Me.btnImport.tbText = "Import to"
        Me.btnImport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnImport.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnImport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextMouseDownPlace = 2
        Me.btnImport.tbToolTip = ""
        Me.btnImport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.VisibleEx = True
        '
        'btnRepair
        '
        Me.btnRepair.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRepair.BackColor = System.Drawing.Color.Transparent
        Me.btnRepair.BindingForm = Nothing
        Me.btnRepair.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRepair.Location = New System.Drawing.Point(694, 28)
        Me.btnRepair.Name = "btnRepair"
        Me.btnRepair.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRepair.Selectable = True
        Me.btnRepair.Size = New System.Drawing.Size(57, 23)
        Me.btnRepair.TabIndex = 86
        Me.btnRepair.tbAdriftIconWhenHover = False
        Me.btnRepair.tbAutoSize = True
        Me.btnRepair.tbAutoSizeEx = True
        Me.btnRepair.tbBackgroundImage = Nothing
        Me.btnRepair.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnRepair.tbBadgeNumber = 0
        Me.btnRepair.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRepair.tbEndEllipsis = False
        Me.btnRepair.tbIconHoldPlace = True
        Me.btnRepair.tbIconImage = Global.iTong.My.Resources.Resources.btn_repair_4
        Me.btnRepair.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRepair.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRepair.tbIconMore = False
        Me.btnRepair.tbIconMouseDown = Nothing
        Me.btnRepair.tbIconMouseHover = Nothing
        Me.btnRepair.tbIconMouseLeave = Nothing
        Me.btnRepair.tbIconPlaceText = 2
        Me.btnRepair.tbIconReadOnly = Nothing
        Me.btnRepair.tbImageMouseDown = Nothing
        Me.btnRepair.tbImageMouseHover = Nothing
        Me.btnRepair.tbImageMouseLeave = Nothing
        Me.btnRepair.tbProgressValue = 50
        Me.btnRepair.tbReadOnly = False
        Me.btnRepair.tbReadOnlyText = False
        Me.btnRepair.tbShadow = False
        Me.btnRepair.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRepair.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRepair.tbShowDot = False
        Me.btnRepair.tbShowMoreIconImg = CType(resources.GetObject("btnRepair.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRepair.tbShowNew = False
        Me.btnRepair.tbShowProgress = False
        Me.btnRepair.tbShowTip = True
        Me.btnRepair.tbShowToolTipOnButton = False
        Me.btnRepair.tbSplit = "3,3,3,3"
        Me.btnRepair.tbText = "修复"
        Me.btnRepair.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRepair.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRepair.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepair.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRepair.tbTextMouseDownPlace = 2
        Me.btnRepair.tbToolTip = ""
        Me.btnRepair.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRepair.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRepair.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRepair.VisibleEx = True
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(759, 28)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(57, 23)
        Me.btnRefresh.TabIndex = 85
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = "刷新"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefresh.tbTextMouseDownPlace = 0
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'btnWeChatDataMigration
        '
        Me.btnWeChatDataMigration.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnWeChatDataMigration.BackColor = System.Drawing.Color.Transparent
        Me.btnWeChatDataMigration.BindingForm = Nothing
        Me.btnWeChatDataMigration.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnWeChatDataMigration.Location = New System.Drawing.Point(677, 393)
        Me.btnWeChatDataMigration.Name = "btnWeChatDataMigration"
        Me.btnWeChatDataMigration.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnWeChatDataMigration.Selectable = True
        Me.btnWeChatDataMigration.Size = New System.Drawing.Size(91, 36)
        Me.btnWeChatDataMigration.TabIndex = 82
        Me.btnWeChatDataMigration.tbAdriftIconWhenHover = False
        Me.btnWeChatDataMigration.tbAutoSize = False
        Me.btnWeChatDataMigration.tbAutoSizeEx = False
        Me.btnWeChatDataMigration.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnWeChatDataMigration.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnWeChatDataMigration.tbBadgeNumber = 0
        Me.btnWeChatDataMigration.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnWeChatDataMigration.tbEndEllipsis = False
        Me.btnWeChatDataMigration.tbIconHoldPlace = True
        Me.btnWeChatDataMigration.tbIconImage = Nothing
        Me.btnWeChatDataMigration.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWeChatDataMigration.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnWeChatDataMigration.tbIconMore = False
        Me.btnWeChatDataMigration.tbIconMouseDown = Nothing
        Me.btnWeChatDataMigration.tbIconMouseHover = Nothing
        Me.btnWeChatDataMigration.tbIconMouseLeave = Nothing
        Me.btnWeChatDataMigration.tbIconPlaceText = 2
        Me.btnWeChatDataMigration.tbIconReadOnly = Nothing
        Me.btnWeChatDataMigration.tbImageMouseDown = Nothing
        Me.btnWeChatDataMigration.tbImageMouseHover = Nothing
        Me.btnWeChatDataMigration.tbImageMouseLeave = Nothing
        Me.btnWeChatDataMigration.tbProgressValue = 50
        Me.btnWeChatDataMigration.tbReadOnly = False
        Me.btnWeChatDataMigration.tbReadOnlyText = False
        Me.btnWeChatDataMigration.tbShadow = False
        Me.btnWeChatDataMigration.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnWeChatDataMigration.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnWeChatDataMigration.tbShowDot = False
        Me.btnWeChatDataMigration.tbShowMoreIconImg = Nothing
        Me.btnWeChatDataMigration.tbShowNew = False
        Me.btnWeChatDataMigration.tbShowProgress = False
        Me.btnWeChatDataMigration.tbShowTip = True
        Me.btnWeChatDataMigration.tbShowToolTipOnButton = False
        Me.btnWeChatDataMigration.tbSplit = "13,11,13,11"
        Me.btnWeChatDataMigration.tbText = "数据迁移"
        Me.btnWeChatDataMigration.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWeChatDataMigration.tbTextColor = System.Drawing.Color.White
        Me.btnWeChatDataMigration.tbTextColorDisable = System.Drawing.Color.White
        Me.btnWeChatDataMigration.tbTextColorDown = System.Drawing.Color.White
        Me.btnWeChatDataMigration.tbTextColorHover = System.Drawing.Color.White
        Me.btnWeChatDataMigration.tbTextMouseDownPlace = 0
        Me.btnWeChatDataMigration.tbToolTip = ""
        Me.btnWeChatDataMigration.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnWeChatDataMigration.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWeChatDataMigration.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWeChatDataMigration.Visible = False
        Me.btnWeChatDataMigration.VisibleEx = True
        '
        'btnBackupStart
        '
        Me.btnBackupStart.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBackupStart.BackColor = System.Drawing.Color.Transparent
        Me.btnBackupStart.BindingForm = Nothing
        Me.btnBackupStart.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnBackupStart.Location = New System.Drawing.Point(774, 393)
        Me.btnBackupStart.Name = "btnBackupStart"
        Me.btnBackupStart.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnBackupStart.Selectable = True
        Me.btnBackupStart.Size = New System.Drawing.Size(142, 36)
        Me.btnBackupStart.TabIndex = 24
        Me.btnBackupStart.tbAdriftIconWhenHover = False
        Me.btnBackupStart.tbAutoSize = False
        Me.btnBackupStart.tbAutoSizeEx = False
        Me.btnBackupStart.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnBackupStart.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBackupStart.tbBadgeNumber = 0
        Me.btnBackupStart.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackupStart.tbEndEllipsis = False
        Me.btnBackupStart.tbIconHoldPlace = True
        Me.btnBackupStart.tbIconImage = Nothing
        Me.btnBackupStart.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackupStart.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBackupStart.tbIconMore = False
        Me.btnBackupStart.tbIconMouseDown = Nothing
        Me.btnBackupStart.tbIconMouseHover = Nothing
        Me.btnBackupStart.tbIconMouseLeave = Nothing
        Me.btnBackupStart.tbIconPlaceText = 2
        Me.btnBackupStart.tbIconReadOnly = Nothing
        Me.btnBackupStart.tbImageMouseDown = Nothing
        Me.btnBackupStart.tbImageMouseHover = Nothing
        Me.btnBackupStart.tbImageMouseLeave = Nothing
        Me.btnBackupStart.tbProgressValue = 50
        Me.btnBackupStart.tbReadOnly = False
        Me.btnBackupStart.tbReadOnlyText = False
        Me.btnBackupStart.tbShadow = False
        Me.btnBackupStart.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackupStart.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackupStart.tbShowDot = False
        Me.btnBackupStart.tbShowMoreIconImg = Nothing
        Me.btnBackupStart.tbShowNew = False
        Me.btnBackupStart.tbShowProgress = False
        Me.btnBackupStart.tbShowTip = True
        Me.btnBackupStart.tbShowToolTipOnButton = False
        Me.btnBackupStart.tbSplit = "13,11,13,11"
        Me.btnBackupStart.tbText = "备份"
        Me.btnBackupStart.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackupStart.tbTextColor = System.Drawing.Color.White
        Me.btnBackupStart.tbTextColorDisable = System.Drawing.Color.White
        Me.btnBackupStart.tbTextColorDown = System.Drawing.Color.White
        Me.btnBackupStart.tbTextColorHover = System.Drawing.Color.White
        Me.btnBackupStart.tbTextMouseDownPlace = 0
        Me.btnBackupStart.tbToolTip = ""
        Me.btnBackupStart.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBackupStart.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackupStart.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackupStart.VisibleEx = True
        '
        'pnlGridBackList
        '
        Me.pnlGridBackList.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlGridBackList.BackColor = System.Drawing.Color.Silver
        Me.pnlGridBackList.Controls.Add(Me.pnlCount)
        Me.pnlGridBackList.Location = New System.Drawing.Point(32, 55)
        Me.pnlGridBackList.Margin = New System.Windows.Forms.Padding(1)
        Me.pnlGridBackList.Name = "pnlGridBackList"
        Me.pnlGridBackList.Padding = New System.Windows.Forms.Padding(1)
        Me.pnlGridBackList.Size = New System.Drawing.Size(885, 324)
        Me.pnlGridBackList.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlGridBackList.TabIndex = 79
        Me.pnlGridBackList.tbBackgroundImage = Nothing
        Me.pnlGridBackList.tbShowWatermark = False
        Me.pnlGridBackList.tbSplit = "0,0,0,0"
        Me.pnlGridBackList.tbWatermark = Nothing
        Me.pnlGridBackList.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlGridBackList.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlCount
        '
        Me.pnlCount.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(131, Byte), Integer))
        Me.pnlCount.Controls.Add(Me.lblStorageSpace)
        Me.pnlCount.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlCount.Location = New System.Drawing.Point(1, 299)
        Me.pnlCount.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlCount.Name = "pnlCount"
        Me.pnlCount.Size = New System.Drawing.Size(883, 24)
        Me.pnlCount.TabIndex = 8
        '
        'lblStorageSpace
        '
        Me.lblStorageSpace.AutoSize = True
        Me.lblStorageSpace.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblStorageSpace.Location = New System.Drawing.Point(3, 5)
        Me.lblStorageSpace.Name = "lblStorageSpace"
        Me.lblStorageSpace.Size = New System.Drawing.Size(377, 12)
        Me.lblStorageSpace.TabIndex = 38
        Me.lblStorageSpace.tbAdriftWhenHover = False
        Me.lblStorageSpace.tbAutoEllipsis = False
        Me.lblStorageSpace.tbAutoSize = True
        Me.lblStorageSpace.tbHideImage = False
        Me.lblStorageSpace.tbIconImage = Nothing
        Me.lblStorageSpace.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblStorageSpace.tbIconPlaceText = 5
        Me.lblStorageSpace.tbShadow = False
        Me.lblStorageSpace.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblStorageSpace.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblStorageSpace.tbShowScrolling = False
        Me.lblStorageSpace.Text = "提示：备份提示存储空间不足时删除无用的备份，可以扩大可用容量。"
        '
        'lblTitle
        '
        Me.lblTitle.AutoSize = True
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.lblTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblTitle.Location = New System.Drawing.Point(34, 27)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(232, 15)
        Me.lblTitle.TabIndex = 1
        Me.lblTitle.Text = "请选择要查看的微信信息备份文件"
        '
        'lblPrompt
        '
        Me.lblPrompt.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblPrompt.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblPrompt.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblPrompt.Location = New System.Drawing.Point(33, 393)
        Me.lblPrompt.Name = "lblPrompt"
        Me.lblPrompt.Padding = New System.Windows.Forms.Padding(1)
        Me.lblPrompt.Size = New System.Drawing.Size(813, 43)
        Me.lblPrompt.TabIndex = 25
        Me.lblPrompt.Text = "1、从备份文件读取微信信息能有效提高获取速度，首次备份所需时间会比较长" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "2、通过备份文件只能查看截止备份时间的数据，查看新数据，请快速备份"
        '
        'pnlFromBackup
        '
        Me.pnlFromBackup.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(131, Byte), Integer))
        Me.tblayoutMain.SetColumnSpan(Me.pnlFromBackup, 5)
        Me.pnlFromBackup.Controls.Add(Me.llblRefresh)
        Me.pnlFromBackup.Controls.Add(Me.lblFromBackupMsg)
        Me.pnlFromBackup.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlFromBackup.Location = New System.Drawing.Point(0, 570)
        Me.pnlFromBackup.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFromBackup.Name = "pnlFromBackup"
        Me.pnlFromBackup.Size = New System.Drawing.Size(2847, 20)
        Me.pnlFromBackup.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFromBackup.TabIndex = 31
        Me.pnlFromBackup.tbBackgroundImage = Nothing
        Me.pnlFromBackup.tbShowWatermark = False
        Me.pnlFromBackup.tbSplit = "0,0,0,0"
        Me.pnlFromBackup.tbWatermark = Nothing
        Me.pnlFromBackup.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFromBackup.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        Me.pnlFromBackup.Visible = False
        '
        'llblRefresh
        '
        Me.llblRefresh.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.llblRefresh.AutoSize = True
        Me.llblRefresh.BackColor = System.Drawing.Color.Transparent
        Me.llblRefresh.Cursor = System.Windows.Forms.Cursors.Hand
        Me.llblRefresh.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.llblRefresh.ForeColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.llblRefresh.LinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        Me.llblRefresh.Location = New System.Drawing.Point(698, 2)
        Me.llblRefresh.Name = "llblRefresh"
        Me.llblRefresh.Size = New System.Drawing.Size(65, 12)
        Me.llblRefresh.TabIndex = 39
        Me.llblRefresh.TabStop = True
        Me.llblRefresh.Text = "获取最新>>"
        Me.llblRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.llblRefresh.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(5, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(242, Byte), Integer))
        '
        'lblFromBackupMsg
        '
        Me.lblFromBackupMsg.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblFromBackupMsg.AutoSize = True
        Me.lblFromBackupMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFromBackupMsg.Location = New System.Drawing.Point(639, 2)
        Me.lblFromBackupMsg.Name = "lblFromBackupMsg"
        Me.lblFromBackupMsg.Size = New System.Drawing.Size(53, 12)
        Me.lblFromBackupMsg.TabIndex = 0
        Me.lblFromBackupMsg.tbAdriftWhenHover = False
        Me.lblFromBackupMsg.tbAutoEllipsis = False
        Me.lblFromBackupMsg.tbAutoSize = True
        Me.lblFromBackupMsg.tbHideImage = False
        Me.lblFromBackupMsg.tbIconImage = Nothing
        Me.lblFromBackupMsg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFromBackupMsg.tbIconPlaceText = 5
        Me.lblFromBackupMsg.tbShadow = False
        Me.lblFromBackupMsg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFromBackupMsg.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFromBackupMsg.tbShowScrolling = False
        Me.lblFromBackupMsg.Text = "TbLabel1"
        '
        'pnlDescribe
        '
        Me.pnlDescribe.Controls.Add(Me.btnRefreshEx)
        Me.pnlDescribe.Controls.Add(Me.TbLabel3)
        Me.pnlDescribe.Controls.Add(Me.pnlcharge)
        Me.pnlDescribe.Controls.Add(Me.RichTextBox1)
        Me.pnlDescribe.Controls.Add(Me.TbLabel2)
        Me.pnlDescribe.Controls.Add(Me.btnAdd)
        Me.pnlDescribe.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlDescribe.Location = New System.Drawing.Point(1901, 3)
        Me.pnlDescribe.Name = "pnlDescribe"
        Me.tblayoutMain.SetRowSpan(Me.pnlDescribe, 3)
        Me.pnlDescribe.Size = New System.Drawing.Size(1, 564)
        Me.pnlDescribe.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlDescribe.TabIndex = 32
        Me.pnlDescribe.tbBackgroundImage = Nothing
        Me.pnlDescribe.tbShowWatermark = False
        Me.pnlDescribe.tbSplit = "0,0,0,0"
        Me.pnlDescribe.tbWatermark = Nothing
        Me.pnlDescribe.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlDescribe.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnRefreshEx
        '
        Me.btnRefreshEx.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnRefreshEx.BackColor = System.Drawing.Color.Transparent
        Me.btnRefreshEx.BindingForm = Nothing
        Me.btnRefreshEx.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefreshEx.Location = New System.Drawing.Point(205, 195)
        Me.btnRefreshEx.Name = "btnRefreshEx"
        Me.btnRefreshEx.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefreshEx.Selectable = True
        Me.btnRefreshEx.Size = New System.Drawing.Size(57, 23)
        Me.btnRefreshEx.TabIndex = 86
        Me.btnRefreshEx.tbAdriftIconWhenHover = False
        Me.btnRefreshEx.tbAutoSize = False
        Me.btnRefreshEx.tbAutoSizeEx = True
        Me.btnRefreshEx.tbBackgroundImage = Nothing
        Me.btnRefreshEx.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefreshEx.tbBadgeNumber = 0
        Me.btnRefreshEx.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefreshEx.tbEndEllipsis = False
        Me.btnRefreshEx.tbIconHoldPlace = True
        Me.btnRefreshEx.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefreshEx.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefreshEx.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefreshEx.tbIconMore = False
        Me.btnRefreshEx.tbIconMouseDown = Nothing
        Me.btnRefreshEx.tbIconMouseHover = Nothing
        Me.btnRefreshEx.tbIconMouseLeave = Nothing
        Me.btnRefreshEx.tbIconPlaceText = 2
        Me.btnRefreshEx.tbIconReadOnly = Nothing
        Me.btnRefreshEx.tbImageMouseDown = Nothing
        Me.btnRefreshEx.tbImageMouseHover = Nothing
        Me.btnRefreshEx.tbImageMouseLeave = Nothing
        Me.btnRefreshEx.tbProgressValue = 50
        Me.btnRefreshEx.tbReadOnly = False
        Me.btnRefreshEx.tbReadOnlyText = False
        Me.btnRefreshEx.tbShadow = False
        Me.btnRefreshEx.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefreshEx.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefreshEx.tbShowDot = False
        Me.btnRefreshEx.tbShowMoreIconImg = CType(resources.GetObject("btnRefreshEx.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefreshEx.tbShowNew = False
        Me.btnRefreshEx.tbShowProgress = False
        Me.btnRefreshEx.tbShowTip = True
        Me.btnRefreshEx.tbShowToolTipOnButton = False
        Me.btnRefreshEx.tbSplit = "0,0,0,0"
        Me.btnRefreshEx.tbText = "刷新"
        Me.btnRefreshEx.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefreshEx.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnRefreshEx.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnRefreshEx.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefreshEx.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnRefreshEx.tbTextMouseDownPlace = 0
        Me.btnRefreshEx.tbToolTip = ""
        Me.btnRefreshEx.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefreshEx.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefreshEx.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefreshEx.VisibleEx = True
        '
        'TbLabel3
        '
        Me.TbLabel3.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.TbLabel3.AutoSize = True
        Me.TbLabel3.Font = New System.Drawing.Font("宋体", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TbLabel3.ForeColor = System.Drawing.Color.Red
        Me.TbLabel3.Location = New System.Drawing.Point(-426, 475)
        Me.TbLabel3.Name = "TbLabel3"
        Me.TbLabel3.Size = New System.Drawing.Size(717, 38)
        Me.TbLabel3.TabIndex = 25
        Me.TbLabel3.tbAdriftWhenHover = False
        Me.TbLabel3.tbAutoEllipsis = False
        Me.TbLabel3.tbAutoSize = True
        Me.TbLabel3.tbHideImage = False
        Me.TbLabel3.tbIconImage = Nothing
        Me.TbLabel3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel3.tbIconPlaceText = 5
        Me.TbLabel3.tbShadow = False
        Me.TbLabel3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel3.tbShowScrolling = False
        Me.TbLabel3.Text = "闪退设备：闪退微信所在的设备          闪退微信SKU：闪退微信的SKU" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "新设备：迁移到的设备                       新微信SKU" & _
    "：迁移到的新微信的SKU"
        '
        'pnlcharge
        '
        Me.pnlcharge.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlcharge.BackColor = System.Drawing.Color.Silver
        Me.pnlcharge.Location = New System.Drawing.Point(-421, 227)
        Me.pnlcharge.Margin = New System.Windows.Forms.Padding(1)
        Me.pnlcharge.Name = "pnlcharge"
        Me.pnlcharge.Padding = New System.Windows.Forms.Padding(1)
        Me.pnlcharge.Size = New System.Drawing.Size(848, 233)
        Me.pnlcharge.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlcharge.TabIndex = 3
        Me.pnlcharge.tbBackgroundImage = Nothing
        Me.pnlcharge.tbShowWatermark = False
        Me.pnlcharge.tbSplit = "0,0,0,0"
        Me.pnlcharge.tbWatermark = Nothing
        Me.pnlcharge.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlcharge.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'RichTextBox1
        '
        Me.RichTextBox1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.RichTextBox1.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.RichTextBox1.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.RichTextBox1.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.RichTextBox1.Location = New System.Drawing.Point(-421, 103)
        Me.RichTextBox1.Name = "RichTextBox1"
        Me.RichTextBox1.ReadOnly = True
        Me.RichTextBox1.Size = New System.Drawing.Size(848, 81)
        Me.RichTextBox1.TabIndex = 2
        Me.RichTextBox1.Text = "分身版数据迁移：" & Global.Microsoft.VisualBasic.ChrW(10) & "把第三方闪退的、不能打开的、不能使用的微信或官方微信的全部数据（图片、语音、视频、文本收藏等信息），" & Global.Microsoft.VisualBasic.ChrW(10) & "迁移到另外的微信（官方或任意第三方微信）上" & _
    "面。" & Global.Microsoft.VisualBasic.ChrW(10) & "一个激活码只能关联：一台闪退设备 一个闪退微信SKU 一台新设备 一个新微信SKU（闪退设备和新设备可以为同一台）"
        '
        'TbLabel2
        '
        Me.TbLabel2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.TbLabel2.AutoSize = True
        Me.TbLabel2.Font = New System.Drawing.Font("宋体", 21.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.TbLabel2.Location = New System.Drawing.Point(-426, 53)
        Me.TbLabel2.Name = "TbLabel2"
        Me.TbLabel2.Size = New System.Drawing.Size(216, 29)
        Me.TbLabel2.TabIndex = 0
        Me.TbLabel2.tbAdriftWhenHover = False
        Me.TbLabel2.tbAutoEllipsis = False
        Me.TbLabel2.tbAutoSize = True
        Me.TbLabel2.tbHideImage = False
        Me.TbLabel2.tbIconImage = Nothing
        Me.TbLabel2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel2.tbIconPlaceText = 5
        Me.TbLabel2.tbShadow = False
        Me.TbLabel2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel2.tbShowScrolling = False
        Me.TbLabel2.Text = "分身版数据迁移"
        '
        'btnAdd
        '
        Me.btnAdd.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnAdd.BackColor = System.Drawing.Color.Transparent
        Me.btnAdd.BindingForm = Nothing
        Me.btnAdd.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnAdd.Location = New System.Drawing.Point(274, 190)
        Me.btnAdd.Name = "btnAdd"
        Me.btnAdd.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnAdd.Selectable = True
        Me.btnAdd.Size = New System.Drawing.Size(153, 31)
        Me.btnAdd.TabIndex = 24
        Me.btnAdd.tbAdriftIconWhenHover = False
        Me.btnAdd.tbAutoSize = False
        Me.btnAdd.tbAutoSizeEx = False
        Me.btnAdd.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnAdd.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnAdd.tbBadgeNumber = 0
        Me.btnAdd.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAdd.tbEndEllipsis = False
        Me.btnAdd.tbIconHoldPlace = True
        Me.btnAdd.tbIconImage = Nothing
        Me.btnAdd.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAdd.tbIconMore = False
        Me.btnAdd.tbIconMouseDown = Nothing
        Me.btnAdd.tbIconMouseHover = Nothing
        Me.btnAdd.tbIconMouseLeave = Nothing
        Me.btnAdd.tbIconPlaceText = 2
        Me.btnAdd.tbIconReadOnly = Nothing
        Me.btnAdd.tbImageMouseDown = Nothing
        Me.btnAdd.tbImageMouseHover = Nothing
        Me.btnAdd.tbImageMouseLeave = Nothing
        Me.btnAdd.tbProgressValue = 50
        Me.btnAdd.tbReadOnly = False
        Me.btnAdd.tbReadOnlyText = False
        Me.btnAdd.tbShadow = False
        Me.btnAdd.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAdd.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAdd.tbShowDot = False
        Me.btnAdd.tbShowMoreIconImg = Nothing
        Me.btnAdd.tbShowNew = False
        Me.btnAdd.tbShowProgress = False
        Me.btnAdd.tbShowTip = True
        Me.btnAdd.tbShowToolTipOnButton = False
        Me.btnAdd.tbSplit = "13,11,13,11"
        Me.btnAdd.tbText = "添加闪退微信"
        Me.btnAdd.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.tbTextColor = System.Drawing.Color.White
        Me.btnAdd.tbTextColorDisable = System.Drawing.Color.White
        Me.btnAdd.tbTextColorDown = System.Drawing.Color.White
        Me.btnAdd.tbTextColorHover = System.Drawing.Color.White
        Me.btnAdd.tbTextMouseDownPlace = 0
        Me.btnAdd.tbToolTip = ""
        Me.btnAdd.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAdd.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAdd.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAdd.VisibleEx = True
        '
        'pnlHeadBackup
        '
        Me.pnlHeadBackup.Controls.Add(Me.TbButton15)
        Me.pnlHeadBackup.Controls.Add(Me.TbButton9)
        Me.pnlHeadBackup.Controls.Add(Me.TbButton6)
        Me.pnlHeadBackup.Controls.Add(Me.TbButton2)
        Me.pnlHeadBackup.Controls.Add(Me.TbButton4)
        Me.pnlHeadBackup.Controls.Add(Me.btnDevice)
        Me.pnlHeadBackup.Controls.Add(Me.btniCloud_2)
        Me.pnlHeadBackup.Controls.Add(Me.btnBackup)
        Me.pnlHeadBackup.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlHeadBackup.Location = New System.Drawing.Point(949, 0)
        Me.pnlHeadBackup.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlHeadBackup.Name = "pnlHeadBackup"
        Me.pnlHeadBackup.Size = New System.Drawing.Size(949, 110)
        Me.pnlHeadBackup.TabIndex = 0
        '
        'TbButton15
        '
        Me.TbButton15.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton15.BackColor = System.Drawing.Color.Transparent
        Me.TbButton15.BindingForm = Nothing
        Me.TbButton15.Location = New System.Drawing.Point(698, 100)
        Me.TbButton15.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton15.Name = "TbButton15"
        Me.TbButton15.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton15.Selectable = True
        Me.TbButton15.Size = New System.Drawing.Size(251, 10)
        Me.TbButton15.TabIndex = 28
        Me.TbButton15.tbAdriftIconWhenHover = False
        Me.TbButton15.tbAutoSize = False
        Me.TbButton15.tbAutoSizeEx = False
        Me.TbButton15.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_left
        Me.TbButton15.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton15.tbBadgeNumber = 0
        Me.TbButton15.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton15.tbEndEllipsis = False
        Me.TbButton15.tbIconHoldPlace = True
        Me.TbButton15.tbIconImage = Nothing
        Me.TbButton15.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton15.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton15.tbIconMore = False
        Me.TbButton15.tbIconMouseDown = Nothing
        Me.TbButton15.tbIconMouseHover = Nothing
        Me.TbButton15.tbIconMouseLeave = Nothing
        Me.TbButton15.tbIconPlaceText = 2
        Me.TbButton15.tbIconReadOnly = Nothing
        Me.TbButton15.tbImageMouseDown = Nothing
        Me.TbButton15.tbImageMouseHover = Nothing
        Me.TbButton15.tbImageMouseLeave = Nothing
        Me.TbButton15.tbProgressValue = 50
        Me.TbButton15.tbReadOnly = False
        Me.TbButton15.tbReadOnlyText = False
        Me.TbButton15.tbShadow = False
        Me.TbButton15.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton15.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton15.tbShowDot = False
        Me.TbButton15.tbShowMoreIconImg = CType(resources.GetObject("TbButton15.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton15.tbShowNew = False
        Me.TbButton15.tbShowProgress = False
        Me.TbButton15.tbShowTip = True
        Me.TbButton15.tbShowToolTipOnButton = False
        Me.TbButton15.tbSplit = "1,1,1,3"
        Me.TbButton15.tbText = ""
        Me.TbButton15.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton15.tbTextColor = System.Drawing.Color.White
        Me.TbButton15.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton15.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton15.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton15.tbTextMouseDownPlace = 0
        Me.TbButton15.tbToolTip = ""
        Me.TbButton15.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton15.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton15.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton15.VisibleEx = True
        '
        'TbButton9
        '
        Me.TbButton9.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton9.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.TbButton9.BindingForm = Nothing
        Me.TbButton9.Location = New System.Drawing.Point(696, 15)
        Me.TbButton9.Name = "TbButton9"
        Me.TbButton9.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton9.Selectable = True
        Me.TbButton9.Size = New System.Drawing.Size(1, 80)
        Me.TbButton9.TabIndex = 29
        Me.TbButton9.tbAdriftIconWhenHover = False
        Me.TbButton9.tbAutoSize = False
        Me.TbButton9.tbAutoSizeEx = False
        Me.TbButton9.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_restore_left
        Me.TbButton9.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton9.tbBadgeNumber = 0
        Me.TbButton9.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton9.tbEndEllipsis = False
        Me.TbButton9.tbIconHoldPlace = True
        Me.TbButton9.tbIconImage = Nothing
        Me.TbButton9.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton9.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton9.tbIconMore = False
        Me.TbButton9.tbIconMouseDown = Nothing
        Me.TbButton9.tbIconMouseHover = Nothing
        Me.TbButton9.tbIconMouseLeave = Nothing
        Me.TbButton9.tbIconPlaceText = 2
        Me.TbButton9.tbIconReadOnly = Nothing
        Me.TbButton9.tbImageMouseDown = Nothing
        Me.TbButton9.tbImageMouseHover = Nothing
        Me.TbButton9.tbImageMouseLeave = Nothing
        Me.TbButton9.tbProgressValue = 50
        Me.TbButton9.tbReadOnly = False
        Me.TbButton9.tbReadOnlyText = False
        Me.TbButton9.tbShadow = False
        Me.TbButton9.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton9.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton9.tbShowDot = False
        Me.TbButton9.tbShowMoreIconImg = CType(resources.GetObject("TbButton9.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton9.tbShowNew = False
        Me.TbButton9.tbShowProgress = False
        Me.TbButton9.tbShowTip = True
        Me.TbButton9.tbShowToolTipOnButton = False
        Me.TbButton9.tbSplit = "1,26,6,25"
        Me.TbButton9.tbText = ""
        Me.TbButton9.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton9.tbTextColor = System.Drawing.Color.White
        Me.TbButton9.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton9.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton9.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton9.tbTextMouseDownPlace = 0
        Me.TbButton9.tbToolTip = ""
        Me.TbButton9.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton9.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton9.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton9.VisibleEx = True
        '
        'TbButton6
        '
        Me.TbButton6.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.TbButton6.BindingForm = Nothing
        Me.TbButton6.Location = New System.Drawing.Point(274, 15)
        Me.TbButton6.Name = "TbButton6"
        Me.TbButton6.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton6.Selectable = True
        Me.TbButton6.Size = New System.Drawing.Size(1, 80)
        Me.TbButton6.TabIndex = 29
        Me.TbButton6.tbAdriftIconWhenHover = False
        Me.TbButton6.tbAutoSize = False
        Me.TbButton6.tbAutoSizeEx = False
        Me.TbButton6.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_restore_left
        Me.TbButton6.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton6.tbBadgeNumber = 0
        Me.TbButton6.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton6.tbEndEllipsis = False
        Me.TbButton6.tbIconHoldPlace = True
        Me.TbButton6.tbIconImage = Nothing
        Me.TbButton6.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton6.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton6.tbIconMore = False
        Me.TbButton6.tbIconMouseDown = Nothing
        Me.TbButton6.tbIconMouseHover = Nothing
        Me.TbButton6.tbIconMouseLeave = Nothing
        Me.TbButton6.tbIconPlaceText = 2
        Me.TbButton6.tbIconReadOnly = Nothing
        Me.TbButton6.tbImageMouseDown = Nothing
        Me.TbButton6.tbImageMouseHover = Nothing
        Me.TbButton6.tbImageMouseLeave = Nothing
        Me.TbButton6.tbProgressValue = 50
        Me.TbButton6.tbReadOnly = False
        Me.TbButton6.tbReadOnlyText = False
        Me.TbButton6.tbShadow = False
        Me.TbButton6.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton6.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton6.tbShowDot = False
        Me.TbButton6.tbShowMoreIconImg = CType(resources.GetObject("TbButton6.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton6.tbShowNew = False
        Me.TbButton6.tbShowProgress = False
        Me.TbButton6.tbShowTip = True
        Me.TbButton6.tbShowToolTipOnButton = False
        Me.TbButton6.tbSplit = "1,26,6,25"
        Me.TbButton6.tbText = ""
        Me.TbButton6.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton6.tbTextColor = System.Drawing.Color.White
        Me.TbButton6.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton6.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton6.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton6.tbTextMouseDownPlace = 0
        Me.TbButton6.tbToolTip = ""
        Me.TbButton6.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton6.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton6.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton6.VisibleEx = True
        '
        'TbButton2
        '
        Me.TbButton2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TbButton2.BackColor = System.Drawing.Color.Transparent
        Me.TbButton2.BindingForm = Nothing
        Me.TbButton2.Location = New System.Drawing.Point(276, 100)
        Me.TbButton2.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton2.Name = "TbButton2"
        Me.TbButton2.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton2.Selectable = True
        Me.TbButton2.Size = New System.Drawing.Size(419, 10)
        Me.TbButton2.TabIndex = 28
        Me.TbButton2.tbAdriftIconWhenHover = False
        Me.TbButton2.tbAutoSize = False
        Me.TbButton2.tbAutoSizeEx = False
        Me.TbButton2.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_bg
        Me.TbButton2.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton2.tbBadgeNumber = 0
        Me.TbButton2.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton2.tbEndEllipsis = False
        Me.TbButton2.tbIconHoldPlace = True
        Me.TbButton2.tbIconImage = Nothing
        Me.TbButton2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton2.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton2.tbIconMore = False
        Me.TbButton2.tbIconMouseDown = Nothing
        Me.TbButton2.tbIconMouseHover = Nothing
        Me.TbButton2.tbIconMouseLeave = Nothing
        Me.TbButton2.tbIconPlaceText = 2
        Me.TbButton2.tbIconReadOnly = Nothing
        Me.TbButton2.tbImageMouseDown = Nothing
        Me.TbButton2.tbImageMouseHover = Nothing
        Me.TbButton2.tbImageMouseLeave = Nothing
        Me.TbButton2.tbProgressValue = 50
        Me.TbButton2.tbReadOnly = False
        Me.TbButton2.tbReadOnlyText = False
        Me.TbButton2.tbShadow = False
        Me.TbButton2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton2.tbShowDot = False
        Me.TbButton2.tbShowMoreIconImg = CType(resources.GetObject("TbButton2.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton2.tbShowNew = False
        Me.TbButton2.tbShowProgress = False
        Me.TbButton2.tbShowTip = True
        Me.TbButton2.tbShowToolTipOnButton = False
        Me.TbButton2.tbSplit = "72,1,1,11"
        Me.TbButton2.tbText = ""
        Me.TbButton2.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton2.tbTextColor = System.Drawing.Color.White
        Me.TbButton2.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton2.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton2.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton2.tbTextMouseDownPlace = 0
        Me.TbButton2.tbToolTip = ""
        Me.TbButton2.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton2.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton2.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton2.VisibleEx = True
        '
        'TbButton4
        '
        Me.TbButton4.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.TbButton4.BackColor = System.Drawing.Color.Transparent
        Me.TbButton4.BindingForm = Nothing
        Me.TbButton4.Location = New System.Drawing.Point(0, 100)
        Me.TbButton4.Margin = New System.Windows.Forms.Padding(0)
        Me.TbButton4.Name = "TbButton4"
        Me.TbButton4.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.TbButton4.Selectable = True
        Me.TbButton4.Size = New System.Drawing.Size(273, 10)
        Me.TbButton4.TabIndex = 28
        Me.TbButton4.tbAdriftIconWhenHover = False
        Me.TbButton4.tbAutoSize = False
        Me.TbButton4.tbAutoSizeEx = False
        Me.TbButton4.tbBackgroundImage = Global.iTong.My.Resources.Resources.wechat_device_left
        Me.TbButton4.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.TbButton4.tbBadgeNumber = 0
        Me.TbButton4.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.TbButton4.tbEndEllipsis = False
        Me.TbButton4.tbIconHoldPlace = True
        Me.TbButton4.tbIconImage = Nothing
        Me.TbButton4.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton4.tbIconImageState = iTong.Components.ImageState.OneState
        Me.TbButton4.tbIconMore = False
        Me.TbButton4.tbIconMouseDown = Nothing
        Me.TbButton4.tbIconMouseHover = Nothing
        Me.TbButton4.tbIconMouseLeave = Nothing
        Me.TbButton4.tbIconPlaceText = 2
        Me.TbButton4.tbIconReadOnly = Nothing
        Me.TbButton4.tbImageMouseDown = Nothing
        Me.TbButton4.tbImageMouseHover = Nothing
        Me.TbButton4.tbImageMouseLeave = Nothing
        Me.TbButton4.tbProgressValue = 50
        Me.TbButton4.tbReadOnly = False
        Me.TbButton4.tbReadOnlyText = False
        Me.TbButton4.tbShadow = False
        Me.TbButton4.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbButton4.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbButton4.tbShowDot = False
        Me.TbButton4.tbShowMoreIconImg = CType(resources.GetObject("TbButton4.tbShowMoreIconImg"), System.Drawing.Image)
        Me.TbButton4.tbShowNew = False
        Me.TbButton4.tbShowProgress = False
        Me.TbButton4.tbShowTip = True
        Me.TbButton4.tbShowToolTipOnButton = False
        Me.TbButton4.tbSplit = "1,1,1,3"
        Me.TbButton4.tbText = ""
        Me.TbButton4.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton4.tbTextColor = System.Drawing.Color.White
        Me.TbButton4.tbTextColorDisable = System.Drawing.Color.White
        Me.TbButton4.tbTextColorDown = System.Drawing.Color.White
        Me.TbButton4.tbTextColorHover = System.Drawing.Color.White
        Me.TbButton4.tbTextMouseDownPlace = 0
        Me.TbButton4.tbToolTip = ""
        Me.TbButton4.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.TbButton4.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.TbButton4.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.TbButton4.VisibleEx = True
        '
        'btnDevice
        '
        Me.btnDevice.BackColor = System.Drawing.Color.Transparent
        Me.btnDevice.BindingForm = Nothing
        Me.btnDevice.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnDevice.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btnDevice.Location = New System.Drawing.Point(21, 10)
        Me.btnDevice.Name = "btnDevice"
        Me.btnDevice.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnDevice.Selectable = True
        Me.btnDevice.Size = New System.Drawing.Size(248, 90)
        Me.btnDevice.TabIndex = 27
        Me.btnDevice.tbAdriftIconWhenHover = False
        Me.btnDevice.tbAutoSize = False
        Me.btnDevice.tbAutoSizeEx = False
        Me.btnDevice.tbBackgroundImage = Nothing
        Me.btnDevice.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDevice.tbBadgeNumber = 0
        Me.btnDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDevice.tbEndEllipsis = False
        Me.btnDevice.tbIconHoldPlace = True
        Me.btnDevice.tbIconImage = Global.iTong.My.Resources.Resources.wechat_device_new
        Me.btnDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDevice.tbIconMore = False
        Me.btnDevice.tbIconMouseDown = Nothing
        Me.btnDevice.tbIconMouseHover = Nothing
        Me.btnDevice.tbIconMouseLeave = Nothing
        Me.btnDevice.tbIconPlaceText = 2
        Me.btnDevice.tbIconReadOnly = Nothing
        Me.btnDevice.tbImageMouseDown = Nothing
        Me.btnDevice.tbImageMouseHover = Nothing
        Me.btnDevice.tbImageMouseLeave = Nothing
        Me.btnDevice.tbProgressValue = 50
        Me.btnDevice.tbReadOnly = False
        Me.btnDevice.tbReadOnlyText = False
        Me.btnDevice.tbShadow = False
        Me.btnDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDevice.tbShowDot = False
        Me.btnDevice.tbShowMoreIconImg = Nothing
        Me.btnDevice.tbShowNew = False
        Me.btnDevice.tbShowProgress = False
        Me.btnDevice.tbShowTip = True
        Me.btnDevice.tbShowToolTipOnButton = False
        Me.btnDevice.tbSplit = "5,3,1,5"
        Me.btnDevice.tbText = "通过扫描设备获取信息"
        Me.btnDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnDevice.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDevice.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDevice.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDevice.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnDevice.tbTextMouseDownPlace = 0
        Me.btnDevice.tbToolTip = ""
        Me.btnDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDevice.VisibleEx = True
        '
        'btniCloud_2
        '
        Me.btniCloud_2.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btniCloud_2.BackColor = System.Drawing.Color.Transparent
        Me.btniCloud_2.BindingForm = Nothing
        Me.btniCloud_2.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btniCloud_2.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btniCloud_2.Location = New System.Drawing.Point(699, 10)
        Me.btniCloud_2.Name = "btniCloud_2"
        Me.btniCloud_2.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btniCloud_2.Selectable = True
        Me.btniCloud_2.Size = New System.Drawing.Size(248, 90)
        Me.btniCloud_2.TabIndex = 27
        Me.btniCloud_2.tbAdriftIconWhenHover = False
        Me.btniCloud_2.tbAutoSize = False
        Me.btniCloud_2.tbAutoSizeEx = False
        Me.btniCloud_2.tbBackgroundImage = Nothing
        Me.btniCloud_2.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btniCloud_2.tbBadgeNumber = 0
        Me.btniCloud_2.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btniCloud_2.tbEndEllipsis = False
        Me.btniCloud_2.tbIconHoldPlace = True
        Me.btniCloud_2.tbIconImage = Global.iTong.My.Resources.Resources.wechat_icloud
        Me.btniCloud_2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btniCloud_2.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btniCloud_2.tbIconMore = False
        Me.btniCloud_2.tbIconMouseDown = Nothing
        Me.btniCloud_2.tbIconMouseHover = Nothing
        Me.btniCloud_2.tbIconMouseLeave = Nothing
        Me.btniCloud_2.tbIconPlaceText = 2
        Me.btniCloud_2.tbIconReadOnly = Nothing
        Me.btniCloud_2.tbImageMouseDown = Nothing
        Me.btniCloud_2.tbImageMouseHover = Nothing
        Me.btniCloud_2.tbImageMouseLeave = Nothing
        Me.btniCloud_2.tbProgressValue = 50
        Me.btniCloud_2.tbReadOnly = False
        Me.btniCloud_2.tbReadOnlyText = False
        Me.btniCloud_2.tbShadow = False
        Me.btniCloud_2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btniCloud_2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btniCloud_2.tbShowDot = False
        Me.btniCloud_2.tbShowMoreIconImg = Nothing
        Me.btniCloud_2.tbShowNew = False
        Me.btniCloud_2.tbShowProgress = False
        Me.btniCloud_2.tbShowTip = True
        Me.btniCloud_2.tbShowToolTipOnButton = False
        Me.btniCloud_2.tbSplit = "1,3,6,5"
        Me.btniCloud_2.tbText = "通过iCloud备份恢复"
        Me.btniCloud_2.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloud_2.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud_2.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud_2.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud_2.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btniCloud_2.tbTextMouseDownPlace = 0
        Me.btniCloud_2.tbToolTip = ""
        Me.btniCloud_2.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btniCloud_2.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btniCloud_2.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btniCloud_2.VisibleEx = True
        '
        'btnBackup
        '
        Me.btnBackup.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBackup.BackColor = System.Drawing.Color.Transparent
        Me.btnBackup.BindingForm = Nothing
        Me.btnBackup.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnBackup.Font = New System.Drawing.Font("宋体", 14.0!)
        Me.btnBackup.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnBackup.Location = New System.Drawing.Point(277, 10)
        Me.btnBackup.Name = "btnBackup"
        Me.btnBackup.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnBackup.Selectable = True
        Me.btnBackup.Size = New System.Drawing.Size(409, 90)
        Me.btnBackup.TabIndex = 27
        Me.btnBackup.tbAdriftIconWhenHover = False
        Me.btnBackup.tbAutoSize = False
        Me.btnBackup.tbAutoSizeEx = False
        Me.btnBackup.tbBackgroundImage = Nothing
        Me.btnBackup.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnBackup.tbBadgeNumber = 0
        Me.btnBackup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackup.tbEndEllipsis = False
        Me.btnBackup.tbIconHoldPlace = True
        Me.btnBackup.tbIconImage = Global.iTong.My.Resources.Resources.wechat_restore_new
        Me.btnBackup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackup.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBackup.tbIconMore = False
        Me.btnBackup.tbIconMouseDown = Nothing
        Me.btnBackup.tbIconMouseHover = Nothing
        Me.btnBackup.tbIconMouseLeave = Nothing
        Me.btnBackup.tbIconPlaceText = 2
        Me.btnBackup.tbIconReadOnly = Nothing
        Me.btnBackup.tbImageMouseDown = Nothing
        Me.btnBackup.tbImageMouseHover = Nothing
        Me.btnBackup.tbImageMouseLeave = Nothing
        Me.btnBackup.tbProgressValue = 50
        Me.btnBackup.tbReadOnly = False
        Me.btnBackup.tbReadOnlyText = False
        Me.btnBackup.tbShadow = False
        Me.btnBackup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackup.tbShowDot = False
        Me.btnBackup.tbShowMoreIconImg = Nothing
        Me.btnBackup.tbShowNew = False
        Me.btnBackup.tbShowProgress = False
        Me.btnBackup.tbShowTip = True
        Me.btnBackup.tbShowToolTipOnButton = True
        Me.btnBackup.tbSplit = "3,3,3,3"
        Me.btnBackup.tbText = "微信备份还原"
        Me.btnBackup.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnBackup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnBackup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnBackup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnBackup.tbTextMouseDownPlace = 0
        Me.btnBackup.tbToolTip = "使用iTunes机制对微信进行整体信息备份，成功后可从备份文件中查看和管理信息。"
        Me.btnBackup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer), CType(CType(153, Byte), Integer))
        Me.btnBackup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBackup.VisibleEx = True
        '
        'lblState
        '
        Me.lblState.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblState.BackColor = System.Drawing.Color.Transparent
        Me.lblState.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblState.Location = New System.Drawing.Point(1, 628)
        Me.lblState.Name = "lblState"
        Me.lblState.Size = New System.Drawing.Size(2847, 18)
        Me.lblState.TabIndex = 37
        Me.lblState.tbAdriftWhenHover = False
        Me.lblState.tbAutoEllipsis = False
        Me.lblState.tbAutoSize = False
        Me.lblState.tbHideImage = False
        Me.lblState.tbIconImage = Nothing
        Me.lblState.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblState.tbIconPlaceText = 5
        Me.lblState.tbShadow = False
        Me.lblState.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblState.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblState.tbShowScrolling = False
        Me.lblState.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.lblState.Visible = False
        '
        'munOperator
        '
        Me.munOperator.AccessibleDescription = "179x98"
        Me.munOperator.DropShadowEnabled = False
        Me.munOperator.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.munOperator.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiOpenFolder, Me.ToolStripSeparator1, Me.tsmiOpen, Me.tsmiDelete})
        Me.munOperator.Name = "munSearch"
        Me.munOperator.Size = New System.Drawing.Size(179, 76)
        Me.munOperator.tbBackColor = System.Drawing.Color.White
        Me.munOperator.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munOperator.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munOperator.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiOpenFolder
        '
        Me.tsmiOpenFolder.Name = "tsmiOpenFolder"
        Me.tsmiOpenFolder.Size = New System.Drawing.Size(178, 22)
        Me.tsmiOpenFolder.Text = "打开文件所在的目录"
        '
        'ToolStripSeparator1
        '
        Me.ToolStripSeparator1.Name = "ToolStripSeparator1"
        Me.ToolStripSeparator1.Size = New System.Drawing.Size(175, 6)
        '
        'tsmiOpen
        '
        Me.tsmiOpen.Name = "tsmiOpen"
        Me.tsmiOpen.Size = New System.Drawing.Size(178, 22)
        Me.tsmiOpen.Text = "打开"
        '
        'tsmiDelete
        '
        Me.tsmiDelete.Name = "tsmiDelete"
        Me.tsmiDelete.Size = New System.Drawing.Size(178, 22)
        Me.tsmiDelete.Text = "删除"
        Me.tsmiDelete.Visible = False
        '
        'btn_Setting
        '
        Me.btn_Setting.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_Setting.BackColor = System.Drawing.Color.Transparent
        Me.btn_Setting.BindingForm = Nothing
        Me.btn_Setting.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btn_Setting.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Setting.Location = New System.Drawing.Point(2750, 1)
        Me.btn_Setting.Name = "btn_Setting"
        Me.btn_Setting.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_Setting.Selectable = True
        Me.btn_Setting.Size = New System.Drawing.Size(24, 24)
        Me.btn_Setting.TabIndex = 39
        Me.btn_Setting.tbAdriftIconWhenHover = False
        Me.btn_Setting.tbAutoSize = True
        Me.btn_Setting.tbAutoSizeEx = True
        Me.btn_Setting.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_setting
        Me.btn_Setting.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_Setting.tbBadgeNumber = 0
        Me.btn_Setting.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_Setting.tbEndEllipsis = False
        Me.btn_Setting.tbIconHoldPlace = True
        Me.btn_Setting.tbIconImage = Nothing
        Me.btn_Setting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Setting.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_Setting.tbIconMore = False
        Me.btn_Setting.tbIconMouseDown = Nothing
        Me.btn_Setting.tbIconMouseHover = Nothing
        Me.btn_Setting.tbIconMouseLeave = Nothing
        Me.btn_Setting.tbIconPlaceText = 2
        Me.btn_Setting.tbIconReadOnly = Nothing
        Me.btn_Setting.tbImageMouseDown = Nothing
        Me.btn_Setting.tbImageMouseHover = Nothing
        Me.btn_Setting.tbImageMouseLeave = Nothing
        Me.btn_Setting.tbProgressValue = 50
        Me.btn_Setting.tbReadOnly = False
        Me.btn_Setting.tbReadOnlyText = False
        Me.btn_Setting.tbShadow = False
        Me.btn_Setting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_Setting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_Setting.tbShowDot = False
        Me.btn_Setting.tbShowMoreIconImg = CType(resources.GetObject("btn_Setting.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_Setting.tbShowNew = False
        Me.btn_Setting.tbShowProgress = False
        Me.btn_Setting.tbShowTip = True
        Me.btn_Setting.tbShowToolTipOnButton = False
        Me.btn_Setting.tbSplit = "3,3,3,3"
        Me.btn_Setting.tbText = ""
        Me.btn_Setting.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btn_Setting.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.btn_Setting.tbTextColorDisable = System.Drawing.Color.Gray
        Me.btn_Setting.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_Setting.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_Setting.tbTextMouseDownPlace = 0
        Me.btn_Setting.tbToolTip = ""
        Me.btn_Setting.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_Setting.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Setting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Setting.VisibleEx = True
        '
        'menuMain
        '
        Me.menuMain.AccessibleDescription = "153x48"
        Me.menuMain.DropShadowEnabled = False
        Me.menuMain.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.menuMain.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmSetting})
        Me.menuMain.Name = "menuMain"
        Me.menuMain.Size = New System.Drawing.Size(143, 26)
        Me.menuMain.Tag = ""
        Me.menuMain.tbBackColor = System.Drawing.Color.White
        Me.menuMain.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuMain.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuMain.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmSetting
        '
        Me.tsmSetting.Name = "tsmSetting"
        Me.tsmSetting.Size = New System.Drawing.Size(142, 22)
        Me.tsmSetting.Text = "参数设置(&O)"
        '
        'btnQQ
        '
        Me.btnQQ.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnQQ.BackColor = System.Drawing.Color.Transparent
        Me.btnQQ.BindingForm = Nothing
        Me.btnQQ.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnQQ.Location = New System.Drawing.Point(2652, 3)
        Me.btnQQ.Margin = New System.Windows.Forms.Padding(0)
        Me.btnQQ.Name = "btnQQ"
        Me.btnQQ.Padding = New System.Windows.Forms.Padding(4, 0, 0, 0)
        Me.btnQQ.Selectable = True
        Me.btnQQ.Size = New System.Drawing.Size(95, 21)
        Me.btnQQ.TabIndex = 25
        Me.btnQQ.tbAdriftIconWhenHover = False
        Me.btnQQ.tbAutoSize = True
        Me.btnQQ.tbAutoSizeEx = True
        Me.btnQQ.tbBackgroundImage = Nothing
        Me.btnQQ.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnQQ.tbBadgeNumber = 0
        Me.btnQQ.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnQQ.tbEndEllipsis = False
        Me.btnQQ.tbIconHoldPlace = True
        Me.btnQQ.tbIconImage = Global.iTong.My.Resources.Resources.icon_warning2
        Me.btnQQ.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnQQ.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnQQ.tbIconMore = False
        Me.btnQQ.tbIconMouseDown = Nothing
        Me.btnQQ.tbIconMouseHover = Nothing
        Me.btnQQ.tbIconMouseLeave = Nothing
        Me.btnQQ.tbIconPlaceText = 2
        Me.btnQQ.tbIconReadOnly = Nothing
        Me.btnQQ.tbImageMouseDown = Nothing
        Me.btnQQ.tbImageMouseHover = Global.iTong.My.Resources.Resources.btn_1_transparent
        Me.btnQQ.tbImageMouseLeave = Nothing
        Me.btnQQ.tbProgressValue = 50
        Me.btnQQ.tbReadOnly = False
        Me.btnQQ.tbReadOnlyText = False
        Me.btnQQ.tbShadow = False
        Me.btnQQ.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnQQ.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnQQ.tbShowDot = False
        Me.btnQQ.tbShowMoreIconImg = CType(resources.GetObject("btnQQ.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnQQ.tbShowNew = False
        Me.btnQQ.tbShowProgress = False
        Me.btnQQ.tbShowTip = True
        Me.btnQQ.tbShowToolTipOnButton = False
        Me.btnQQ.tbSplit = "4,4,4,4"
        Me.btnQQ.tbText = "iOS9必备技能"
        Me.btnQQ.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnQQ.tbTextColor = System.Drawing.Color.White
        Me.btnQQ.tbTextColorDisable = System.Drawing.Color.White
        Me.btnQQ.tbTextColorDown = System.Drawing.Color.White
        Me.btnQQ.tbTextColorHover = System.Drawing.Color.White
        Me.btnQQ.tbTextMouseDownPlace = 0
        Me.btnQQ.tbToolTip = ""
        Me.btnQQ.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnQQ.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnQQ.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnQQ.VisibleEx = True
        '
        'btnBuy
        '
        Me.btnBuy.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnBuy.BackColor = System.Drawing.Color.Transparent
        Me.btnBuy.BindingForm = Nothing
        Me.btnBuy.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnBuy.Location = New System.Drawing.Point(2598, 1)
        Me.btnBuy.Margin = New System.Windows.Forms.Padding(0)
        Me.btnBuy.Name = "btnBuy"
        Me.btnBuy.Padding = New System.Windows.Forms.Padding(0, 3, 1, 0)
        Me.btnBuy.Selectable = True
        Me.btnBuy.Size = New System.Drawing.Size(54, 23)
        Me.btnBuy.TabIndex = 41
        Me.btnBuy.tbAdriftIconWhenHover = False
        Me.btnBuy.tbAutoSize = True
        Me.btnBuy.tbAutoSizeEx = True
        Me.btnBuy.tbBackgroundImage = Nothing
        Me.btnBuy.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnBuy.tbBadgeNumber = 0
        Me.btnBuy.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBuy.tbEndEllipsis = False
        Me.btnBuy.tbIconHoldPlace = True
        Me.btnBuy.tbIconImage = Global.iTong.My.Resources.Resources.btn_buy_3
        Me.btnBuy.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBuy.tbIconImageState = iTong.Components.ImageState.ThreeState
        Me.btnBuy.tbIconMore = False
        Me.btnBuy.tbIconMouseDown = Nothing
        Me.btnBuy.tbIconMouseHover = Nothing
        Me.btnBuy.tbIconMouseLeave = Nothing
        Me.btnBuy.tbIconPlaceText = 0
        Me.btnBuy.tbIconReadOnly = Nothing
        Me.btnBuy.tbImageMouseDown = Nothing
        Me.btnBuy.tbImageMouseHover = Global.iTong.My.Resources.Resources.btn_1_transparent
        Me.btnBuy.tbImageMouseLeave = Nothing
        Me.btnBuy.tbProgressValue = 50
        Me.btnBuy.tbReadOnly = False
        Me.btnBuy.tbReadOnlyText = False
        Me.btnBuy.tbShadow = False
        Me.btnBuy.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBuy.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBuy.tbShowDot = False
        Me.btnBuy.tbShowMoreIconImg = CType(resources.GetObject("btnBuy.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBuy.tbShowNew = False
        Me.btnBuy.tbShowProgress = False
        Me.btnBuy.tbShowTip = True
        Me.btnBuy.tbShowToolTipOnButton = False
        Me.btnBuy.tbSplit = "4,4,4,4"
        Me.btnBuy.tbText = "续费"
        Me.btnBuy.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnBuy.tbTextColor = System.Drawing.Color.White
        Me.btnBuy.tbTextColorDisable = System.Drawing.Color.White
        Me.btnBuy.tbTextColorDown = System.Drawing.Color.White
        Me.btnBuy.tbTextColorHover = System.Drawing.Color.White
        Me.btnBuy.tbTextMouseDownPlace = 0
        Me.btnBuy.tbToolTip = ""
        Me.btnBuy.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBuy.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBuy.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBuy.VisibleEx = True
        '
        'btnOpenVIP
        '
        Me.btnOpenVIP.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOpenVIP.BackColor = System.Drawing.Color.Transparent
        Me.btnOpenVIP.BindingForm = Nothing
        Me.btnOpenVIP.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnOpenVIP.Location = New System.Drawing.Point(2527, 3)
        Me.btnOpenVIP.Margin = New System.Windows.Forms.Padding(0)
        Me.btnOpenVIP.Name = "btnOpenVIP"
        Me.btnOpenVIP.Padding = New System.Windows.Forms.Padding(4, 0, 0, 0)
        Me.btnOpenVIP.Selectable = True
        Me.btnOpenVIP.Size = New System.Drawing.Size(71, 21)
        Me.btnOpenVIP.TabIndex = 26
        Me.btnOpenVIP.tbAdriftIconWhenHover = False
        Me.btnOpenVIP.tbAutoSize = True
        Me.btnOpenVIP.tbAutoSizeEx = True
        Me.btnOpenVIP.tbBackgroundImage = Nothing
        Me.btnOpenVIP.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnOpenVIP.tbBadgeNumber = 0
        Me.btnOpenVIP.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOpenVIP.tbEndEllipsis = False
        Me.btnOpenVIP.tbIconHoldPlace = True
        Me.btnOpenVIP.tbIconImage = Global.iTong.My.Resources.Resources.icon_warning2
        Me.btnOpenVIP.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnOpenVIP.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOpenVIP.tbIconMore = False
        Me.btnOpenVIP.tbIconMouseDown = Nothing
        Me.btnOpenVIP.tbIconMouseHover = Nothing
        Me.btnOpenVIP.tbIconMouseLeave = Nothing
        Me.btnOpenVIP.tbIconPlaceText = 2
        Me.btnOpenVIP.tbIconReadOnly = Nothing
        Me.btnOpenVIP.tbImageMouseDown = Nothing
        Me.btnOpenVIP.tbImageMouseHover = Global.iTong.My.Resources.Resources.btn_1_transparent
        Me.btnOpenVIP.tbImageMouseLeave = Nothing
        Me.btnOpenVIP.tbProgressValue = 50
        Me.btnOpenVIP.tbReadOnly = False
        Me.btnOpenVIP.tbReadOnlyText = False
        Me.btnOpenVIP.tbShadow = False
        Me.btnOpenVIP.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnOpenVIP.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnOpenVIP.tbShowDot = False
        Me.btnOpenVIP.tbShowMoreIconImg = CType(resources.GetObject("btnOpenVIP.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOpenVIP.tbShowNew = False
        Me.btnOpenVIP.tbShowProgress = False
        Me.btnOpenVIP.tbShowTip = True
        Me.btnOpenVIP.tbShowToolTipOnButton = False
        Me.btnOpenVIP.tbSplit = "4,4,4,4"
        Me.btnOpenVIP.tbText = "技术支持"
        Me.btnOpenVIP.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnOpenVIP.tbTextColor = System.Drawing.Color.White
        Me.btnOpenVIP.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOpenVIP.tbTextColorDown = System.Drawing.Color.White
        Me.btnOpenVIP.tbTextColorHover = System.Drawing.Color.White
        Me.btnOpenVIP.tbTextMouseDownPlace = 0
        Me.btnOpenVIP.tbToolTip = ""
        Me.btnOpenVIP.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOpenVIP.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOpenVIP.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOpenVIP.Visible = False
        Me.btnOpenVIP.VisibleEx = True
        '
        'cmsImport
        '
        Me.cmsImport.AccessibleDescription = "153x70"
        Me.cmsImport.DropShadowEnabled = False
        Me.cmsImport.Font = New System.Drawing.Font("微软雅黑", 9.0!)
        Me.cmsImport.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiAllbackup, Me.tsmiAloneBackup})
        Me.cmsImport.Name = "munOperate"
        Me.cmsImport.Size = New System.Drawing.Size(149, 48)
        Me.cmsImport.tbBackColor = System.Drawing.Color.White
        Me.cmsImport.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.cmsImport.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.cmsImport.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiAllbackup
        '
        Me.tsmiAllbackup.Name = "tsmiAllbackup"
        Me.tsmiAllbackup.Size = New System.Drawing.Size(148, 22)
        Me.tsmiAllbackup.Text = "导入整机备份"
        '
        'tsmiAloneBackup
        '
        Me.tsmiAloneBackup.Name = "tsmiAloneBackup"
        Me.tsmiAloneBackup.Size = New System.Drawing.Size(148, 22)
        Me.tsmiAloneBackup.Text = "导入单独备份"
        '
        'btnWeChat
        '
        Me.btnWeChat.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnWeChat.BackColor = System.Drawing.Color.Transparent
        Me.btnWeChat.BindingForm = Nothing
        Me.btnWeChat.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnWeChat.Location = New System.Drawing.Point(2429, 3)
        Me.btnWeChat.Margin = New System.Windows.Forms.Padding(0)
        Me.btnWeChat.Name = "btnWeChat"
        Me.btnWeChat.Padding = New System.Windows.Forms.Padding(4, 0, 0, 0)
        Me.btnWeChat.Selectable = True
        Me.btnWeChat.Size = New System.Drawing.Size(83, 21)
        Me.btnWeChat.TabIndex = 42
        Me.btnWeChat.tbAdriftIconWhenHover = False
        Me.btnWeChat.tbAutoSize = True
        Me.btnWeChat.tbAutoSizeEx = True
        Me.btnWeChat.tbBackgroundImage = Nothing
        Me.btnWeChat.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnWeChat.tbBadgeNumber = 0
        Me.btnWeChat.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnWeChat.tbEndEllipsis = False
        Me.btnWeChat.tbIconHoldPlace = True
        Me.btnWeChat.tbIconImage = Global.iTong.My.Resources.Resources.icon_wechatex
        Me.btnWeChat.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnWeChat.tbIconImageState = iTong.Components.ImageState.ThreeState
        Me.btnWeChat.tbIconMore = False
        Me.btnWeChat.tbIconMouseDown = Nothing
        Me.btnWeChat.tbIconMouseHover = Nothing
        Me.btnWeChat.tbIconMouseLeave = Nothing
        Me.btnWeChat.tbIconPlaceText = 2
        Me.btnWeChat.tbIconReadOnly = Nothing
        Me.btnWeChat.tbImageMouseDown = Nothing
        Me.btnWeChat.tbImageMouseHover = Global.iTong.My.Resources.Resources.btn_1_transparent
        Me.btnWeChat.tbImageMouseLeave = Nothing
        Me.btnWeChat.tbProgressValue = 50
        Me.btnWeChat.tbReadOnly = False
        Me.btnWeChat.tbReadOnlyText = False
        Me.btnWeChat.tbShadow = False
        Me.btnWeChat.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnWeChat.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnWeChat.tbShowDot = False
        Me.btnWeChat.tbShowMoreIconImg = CType(resources.GetObject("btnWeChat.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnWeChat.tbShowNew = False
        Me.btnWeChat.tbShowProgress = False
        Me.btnWeChat.tbShowTip = True
        Me.btnWeChat.tbShowToolTipOnButton = False
        Me.btnWeChat.tbSplit = "4,4,4,4"
        Me.btnWeChat.tbText = "技术支持"
        Me.btnWeChat.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btnWeChat.tbTextColor = System.Drawing.Color.White
        Me.btnWeChat.tbTextColorDisable = System.Drawing.Color.White
        Me.btnWeChat.tbTextColorDown = System.Drawing.Color.White
        Me.btnWeChat.tbTextColorHover = System.Drawing.Color.White
        Me.btnWeChat.tbTextMouseDownPlace = 0
        Me.btnWeChat.tbToolTip = ""
        Me.btnWeChat.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnWeChat.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnWeChat.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnWeChat.Visible = False
        Me.btnWeChat.VisibleEx = True
        '
        'frmWeixinMain
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(2849, 650)
        Me.Controls.Add(Me.btnWeChat)
        Me.Controls.Add(Me.btnOpenVIP)
        Me.Controls.Add(Me.btnBuy)
        Me.Controls.Add(Me.btnQQ)
        Me.Controls.Add(Me.btn_Setting)
        Me.Controls.Add(Me.lblState)
        Me.Controls.Add(Me.tblayoutMain)
        Me.MinimumSize = New System.Drawing.Size(870, 650)
        Me.Name = "frmWeixinMain"
        Me.tbShowIconOnForm = False
        Me.tbSplit = "10,33,10,33"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmWeixinMain"
        Me.Controls.SetChildIndex(Me.tblayoutMain, 0)
        Me.Controls.SetChildIndex(Me.lblState, 0)
        Me.Controls.SetChildIndex(Me.btn_Setting, 0)
        Me.Controls.SetChildIndex(Me.btnQQ, 0)
        Me.Controls.SetChildIndex(Me.btnBuy, 0)
        Me.Controls.SetChildIndex(Me.btnOpenVIP, 0)
        Me.Controls.SetChildIndex(Me.btnWeChat, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.tblayoutMain.ResumeLayout(False)
        Me.pnliCloudDetail.ResumeLayout(False)
        Me.pnlDeveloping.ResumeLayout(False)
        Me.pnlDeveloping.PerformLayout()
        CType(Me.PictureBox3, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnliCloudLogin.ResumeLayout(False)
        Me.pnliCloudLogin.PerformLayout()
        CType(Me.pbShowPwd, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBox2, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnliCloudNodata.ResumeLayout(False)
        Me.pnliCloudNodata.PerformLayout()
        CType(Me.piciCloudSearch, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnliCloudDownStatus.ResumeLayout(False)
        Me.pnliCloudDownStatus.PerformLayout()
        Me.pnlDownLoadiCloud.ResumeLayout(False)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnliCloudWaiting.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnliCloudHead.ResumeLayout(False)
        Me.pnlHeadDevice.ResumeLayout(False)
        Me.pnlDevice.ResumeLayout(False)
        Me.pnlTip.ResumeLayout(False)
        Me.pnlDeviceConnect.ResumeLayout(False)
        Me.pnlDeviceConnect.PerformLayout()
        CType(Me.pbDevice, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlDeviceUnConnect.ResumeLayout(False)
        Me.pnlDeviceUnConnect.PerformLayout()
        CType(Me.picDefault, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlBackup.ResumeLayout(False)
        Me.pnlBackup.PerformLayout()
        Me.pnlGridBackList.ResumeLayout(False)
        Me.pnlCount.ResumeLayout(False)
        Me.pnlFromBackup.ResumeLayout(False)
        Me.pnlFromBackup.PerformLayout()
        Me.pnlDescribe.ResumeLayout(False)
        Me.pnlHeadBackup.ResumeLayout(False)
        Me.munOperator.ResumeLayout(False)
        Me.menuMain.ResumeLayout(False)
        Me.cmsImport.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents tblayoutMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlHeadBackup As System.Windows.Forms.Panel
    Friend WithEvents pnlDevice As System.Windows.Forms.Panel
    Friend WithEvents btnDevice As iTong.Components.tbButton
    Friend WithEvents btnBackup As iTong.Components.tbButton
    Friend WithEvents pnlBackup As System.Windows.Forms.Panel
    Friend WithEvents pnlHeadDevice As System.Windows.Forms.Panel
    Friend WithEvents btnDeviceEx As iTong.Components.tbButton
    Friend WithEvents btnBackupEx As iTong.Components.tbButton
    Friend WithEvents pnlContent As System.Windows.Forms.Panel
    Friend WithEvents pbDevice As System.Windows.Forms.PictureBox
    Friend WithEvents btnStartLoadDevice As iTong.Components.tbButton
    Friend WithEvents btnBackupStart As iTong.Components.tbButton
    Friend WithEvents lblTitle As System.Windows.Forms.Label
    Friend WithEvents lblNameValue As iTong.Components.tbLabel
    Friend WithEvents TbButton1 As iTong.Components.tbButton
    Friend WithEvents TbButton2 As iTong.Components.tbButton
    Friend WithEvents TbButton3 As iTong.Components.tbButton
    Friend WithEvents TbButton4 As iTong.Components.tbButton
    Friend WithEvents TbButton5 As iTong.Components.tbButton
    Friend WithEvents TbButton6 As iTong.Components.tbButton
    Friend WithEvents lblPrompt As System.Windows.Forms.Label
    Friend WithEvents pnlGridBackList As iTong.Components.tbPanel
    Friend WithEvents pnlCount As System.Windows.Forms.Panel
    Friend WithEvents lblState As iTong.Components.tbLabel
    Friend WithEvents lblStorageSpace As iTong.Components.tbLabel
    Friend WithEvents lblStartLoadDevice As iTong.Components.tbLabel
    Friend WithEvents munOperator As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiOpenFolder As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents ToolStripSeparator1 As System.Windows.Forms.ToolStripSeparator
    Friend WithEvents tsmiOpen As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiDelete As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents pnlFromBackup As iTong.Components.tbPanel
    Public WithEvents llblRefresh As System.Windows.Forms.LinkLabel
    Friend WithEvents lblFromBackupMsg As iTong.Components.tbLabel
    Friend WithEvents lblFirmwareVersion As iTong.Components.tbLabel
    Friend WithEvents lblDericeType As iTong.Components.tbLabel
    Friend WithEvents lblDeviceType As iTong.Components.tbLabel
    Friend WithEvents llblDeletePwd As System.Windows.Forms.LinkLabel
    Friend WithEvents lblBackupPwdMsg As iTong.Components.tbLabel
    Friend WithEvents btn_Setting As iTong.Components.tbButton
    Friend WithEvents menuMain As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmSetting As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnQQ As iTong.Components.tbButton
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents btnChangeSKU As iTong.Components.tbButton
    Friend WithEvents pnlTip As iTong.Components.tbPanel
    Friend WithEvents TbLabel1 As iTong.Components.tbLabel
    Friend WithEvents pnlDeviceConnect As iTong.Components.tbPanel
    Friend WithEvents btnWeChatDataMigration As iTong.Components.tbButton
    Friend WithEvents pnlDeviceUnConnect As System.Windows.Forms.Panel
    Friend WithEvents lblDevNoConn As System.Windows.Forms.Label
    Friend WithEvents picDefault As System.Windows.Forms.PictureBox
    Friend WithEvents btnBuy As iTong.Components.tbButton
    Friend WithEvents btnRepair As iTong.Components.tbButton
    Friend WithEvents btnRefresh As iTong.Components.tbButton
    Friend WithEvents btnImport As iTong.Components.tbButton
    Friend WithEvents btnOpenVIP As iTong.Components.tbButton
    Friend WithEvents pnlDescribe As iTong.Components.tbPanel
    Friend WithEvents TbLabel3 As iTong.Components.tbLabel
    Friend WithEvents pnlcharge As iTong.Components.tbPanel
    Friend WithEvents RichTextBox1 As System.Windows.Forms.RichTextBox
    Friend WithEvents TbLabel2 As iTong.Components.tbLabel
    Friend WithEvents btnAdd As iTong.Components.tbButton
    Friend WithEvents btnDeleteTemp As iTong.Components.tbButton
    Friend WithEvents cmsImport As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmiAllbackup As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiAloneBackup As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents btnRefreshEx As iTong.Components.tbButton
    Friend WithEvents btnWeChat As iTong.Components.tbButton
    Friend WithEvents TbButton7 As iTong.Components.tbButton
    Friend WithEvents btniCloud_1 As iTong.Components.tbButton
    Friend WithEvents TbButton9 As iTong.Components.tbButton
    Friend WithEvents btniCloud_2 As iTong.Components.tbButton
    Friend WithEvents pnliCloudHead As System.Windows.Forms.Panel
    Friend WithEvents TbButton8 As iTong.Components.tbButton
    Friend WithEvents TbButton10 As iTong.Components.tbButton
    Friend WithEvents TbButton11 As iTong.Components.tbButton
    Friend WithEvents TbButton12 As iTong.Components.tbButton
    Friend WithEvents btnDevice_3 As iTong.Components.tbButton
    Friend WithEvents btniCloud As iTong.Components.tbButton
    Friend WithEvents btnBackup_3 As iTong.Components.tbButton
    Friend WithEvents pnliCloudDetail As System.Windows.Forms.Panel
    Friend WithEvents pnliCloudGrid As iTong.Components.tbPanel
    Friend WithEvents TbButton13 As iTong.Components.tbButton
    Friend WithEvents TbButton14 As iTong.Components.tbButton
    Friend WithEvents TbButton15 As iTong.Components.tbButton
    Friend WithEvents pnliCloudLogin As System.Windows.Forms.Panel
    Friend WithEvents pnliCloudWaiting As System.Windows.Forms.Panel
    Friend WithEvents lbliCloudTip_2 As System.Windows.Forms.Label
    Friend WithEvents lbliCloudTip_1 As System.Windows.Forms.Label
    Friend WithEvents btniCloudLogin As iTong.Components.tbButton
    Friend WithEvents btniCloudPrivate As iTong.Components.tbButton
    Friend WithEvents btniCloudView As iTong.Components.tbButton
    Friend WithEvents txtPwd As iTong.Components.tbTextBox
    Friend WithEvents txtAppleId As iTong.Components.tbTextBox
    Friend WithEvents lbliCloudfailture As iTong.Components.tbLabel
    Friend WithEvents btniCloudForget As iTong.Components.tbButton
    Friend WithEvents btniCloudLogout As iTong.Components.tbButton
    Friend WithEvents lbliCloudView As System.Windows.Forms.Label
    Friend WithEvents lbliCloudErrorServer As System.Windows.Forms.Label
    Friend WithEvents lbliCloudLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents pnliCloudDownStatus As System.Windows.Forms.Panel
    Friend WithEvents lbliCloudWaitingMsg As System.Windows.Forms.Label
    Friend WithEvents btniCloudCancel As iTong.Components.tbButton
    Friend WithEvents pnliCloudNodata As System.Windows.Forms.Panel
    Friend WithEvents lbliCloudNodata As iTong.Components.tbLabel
    Friend WithEvents btniCloudReturn As iTong.Components.tbButton
    Friend WithEvents piciCloudSearch As System.Windows.Forms.PictureBox
    Friend WithEvents PictureBox2 As System.Windows.Forms.PictureBox
    Friend WithEvents pbShowPwd As System.Windows.Forms.PictureBox
    Friend WithEvents lbliCloudWechat As iTong.tbBattery
    Friend WithEvents lblPercentage As System.Windows.Forms.Label
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents lbliCloudAcct As iTong.Components.tbButton
    Friend WithEvents pnlDownLoadiCloud As iTong.Components.tbPanel
    Friend WithEvents pnlDeveloping As iTong.Components.tbPanel
    Friend WithEvents PictureBox3 As System.Windows.Forms.PictureBox
    Friend WithEvents lblIncompatible As iTong.Components.tbLabel
    Friend WithEvents lblCompatibility As iTong.Components.tbLabel
End Class
