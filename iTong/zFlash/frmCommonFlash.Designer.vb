﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmCommonFlash
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。

    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的

    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的

    '可以使用 Windows 窗体设计器修改它。

    '不要使用代码编辑器修改它。

    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmCommonFlash))
        Me.tvwFlash = New iTong.Components.tbTreeView()
        Me.pnlContainer = New iTong.Components.tbPanel()
        Me.tpnlContent = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlTreeview = New System.Windows.Forms.Panel()
        Me.tpnlContent.SuspendLayout()
        Me.pnlTreeview.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(776, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(752, 0)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(728, 0)
        '
        'tvwFlash
        '
        Me.tvwFlash.AutoScroll = True
        Me.tvwFlash.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.tvwFlash.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tvwFlash.ImageList = Nothing
        Me.tvwFlash.Indent = 13
        Me.tvwFlash.ItemHeight = 28
        Me.tvwFlash.Location = New System.Drawing.Point(0, 0)
        Me.tvwFlash.Margin = New System.Windows.Forms.Padding(0)
        Me.tvwFlash.Name = "tvwFlash"
        Me.tvwFlash.Padding = New System.Windows.Forms.Padding(0, 0, 0, 10)
        Me.tvwFlash.PathSeparator = "\"
        Me.tvwFlash.SelectedNode = Nothing
        Me.tvwFlash.Size = New System.Drawing.Size(195, 600)
        Me.tvwFlash.TabIndex = 0
        Me.tvwFlash.TabStop = True
        Me.tvwFlash.tbBackgroundImage = Nothing
        Me.tvwFlash.tbNodeCountSelectedColor = System.Drawing.Color.Empty
        Me.tvwFlash.tbNodeDiff = 20
        Me.tvwFlash.tbNodeFont = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold)
        Me.tvwFlash.tbNodeForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.tvwFlash.tbNodeLabelFont = New System.Drawing.Font("宋体", 9.0!)
        Me.tvwFlash.tbNodeLabelForeColor = System.Drawing.Color.FromArgb(CType(CType(119, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(178, Byte), Integer))
        Me.tvwFlash.tbNodePadding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.tvwFlash.tbNodeShadowColor = System.Drawing.Color.Empty
        Me.tvwFlash.tbSplit = "1,27,3,3"
        Me.tvwFlash.WrapContents = False
        '
        'pnlContainer
        '
        Me.pnlContainer.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlContainer.BackColor = System.Drawing.SystemColors.Control
        Me.pnlContainer.Location = New System.Drawing.Point(196, 0)
        Me.pnlContainer.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlContainer.Name = "pnlContainer"
        Me.pnlContainer.Size = New System.Drawing.Size(604, 600)
        Me.pnlContainer.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlContainer.TabIndex = 1
        Me.pnlContainer.tbBackgroundImage = Nothing
        Me.pnlContainer.tbShowWatermark = False
        Me.pnlContainer.tbSplit = "0,0,0,0"
        Me.pnlContainer.tbWatermark = Nothing
        Me.pnlContainer.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlContainer.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'tpnlContent
        '
        Me.tpnlContent.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.tpnlContent.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.tpnlContent.ColumnCount = 1
        Me.tpnlContent.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlContent.Controls.Add(Me.pnlTreeview, 0, 0)
        Me.tpnlContent.Location = New System.Drawing.Point(0, 0)
        Me.tpnlContent.Name = "tpnlContent"
        Me.tpnlContent.RowCount = 1
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tpnlContent.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 600.0!))
        Me.tpnlContent.Size = New System.Drawing.Size(195, 600)
        Me.tpnlContent.TabIndex = 0
        '
        'pnlTreeview
        '
        Me.pnlTreeview.Controls.Add(Me.tvwFlash)
        Me.pnlTreeview.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTreeview.Location = New System.Drawing.Point(0, 0)
        Me.pnlTreeview.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTreeview.Name = "pnlTreeview"
        Me.pnlTreeview.Size = New System.Drawing.Size(195, 600)
        Me.pnlTreeview.TabIndex = 0
        '
        'frmCommonFlash
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(206, Byte), Integer), CType(CType(210, Byte), Integer), CType(CType(215, Byte), Integer))
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(800, 600)
        Me.Controls.Add(Me.tpnlContent)
        Me.Controls.Add(Me.pnlContainer)
        Me.Name = "frmCommonFlash"
        Me.tbShowIconOnForm = False
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmDeviceApp"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlContainer, 0)
        Me.Controls.SetChildIndex(Me.tpnlContent, 0)
        Me.tpnlContent.ResumeLayout(False)
        Me.pnlTreeview.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Public WithEvents tvwFlash As tbTreeView
    Friend WithEvents pnlContainer As tbPanel
    Friend WithEvents tpnlContent As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents pnlTreeview As System.Windows.Forms.Panel
End Class
