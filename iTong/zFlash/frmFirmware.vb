﻿Imports System.Threading
Imports ProgressStyle = iTong.Components.ProgressStyle

Public Class frmFirmware

    Private mFlashFirmwareDB As FlashFirmwareDB = Nothing
    Private mTdLoadFirmwareInfos As Thread
    Private mDictFirmwareInfos As New Dictionary(Of String, List(Of FirmwareInfoExpand))
    Private dgvFirmware As tbDataGridViewEx = Nothing
    Private mDictGridRow As New Dictionary(Of String, tbDataGridViewRow)
    Private mDictIPWS As New Dictionary(Of String, List(Of String))

    Private mAll As String = "All"

    Private mListIPSW As List(Of FirmwareInfoExpand) = Nothing
    Private mStrFirst As String = ""

    Private blnInitDeviceMenu As Boolean = False

    '界面样式类型
    Private Enum ViewStyle
        Loading
        Show
        NoData
    End Enum

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal idevice As IDevice)
        MyBase.New(application, idevice)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
      
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Common.SetImageLocation(Me.pbLoading, GuiResource.gif_loading_24)
        Common.SetImageLocation(Me.pbNoData, My.Resources.file_fileempty)
        Me.InitAppsDataGridView()

        Me.LoadFirmwareInfos()

        RemoveHandler FlashHelper.DowmloadFirmwareProgressEventHandler, AddressOf FlashHelper_OnDowmloadFirmwareProgress
        AddHandler FlashHelper.DowmloadFirmwareProgressEventHandler, AddressOf FlashHelper_OnDowmloadFirmwareProgress

        If Me.mFlashFirmwareDB Is Nothing Then
            Me.mFlashFirmwareDB = FlashFirmwareDB.GetInstance()
        End If

        Me.btnFlash.Checked = True

        RemoveHandler Me.btnAll.CheckedChanged, AddressOf btn_CheckedChanged
        AddHandler Me.btnAll.CheckedChanged, AddressOf btn_CheckedChanged

        RemoveHandler Me.btnFlash.CheckedChanged, AddressOf btn_CheckedChanged
        AddHandler Me.btnFlash.CheckedChanged, AddressOf btn_CheckedChanged

        RemoveHandler Me.btnJailBreak.CheckedChanged, AddressOf btn_CheckedChanged
        AddHandler Me.btnJailBreak.CheckedChanged, AddressOf btn_CheckedChanged

        RemoveHandler Me.btnFlashAndJailBreak.CheckedChanged, AddressOf btn_CheckedChanged
        AddHandler Me.btnFlashAndJailBreak.CheckedChanged, AddressOf btn_CheckedChanged

        FlashHelper.InitDownManage()

        '  Me.InitLoad(Me.mDevice)

        SetPrompt()


        Me.lblTitle.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.btnSelectDevice.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.btnIPSW.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.lblTitle.Text = Me.Language.GetString("Flash.Lable.FilterFirmwares") '固件筛选
        Me.btnAll.Text = Me.Language.GetString("Flash.Button.All") '全部
        Me.btnFlash.Text = Me.Language.GetString("Flash.Button.Flash") '可刷机
        Me.btnJailBreak.Text = Me.Language.GetString("Flash.Button.JailBreak") '可越狱
        Me.btnFlashAndJailBreak.Text = Me.Language.GetString("Flash.Button.FlashAndJailBreak") '可刷可越

        Me.btnRefresh.Text = Me.Language.GetString("Common.Button.Refresh") '重新加载
        Me.lblMsg.Text = Me.Language.GetString("App.Message.GetFailure")

        Me.btnSelectDevice.Location = New Point(Me.lblTitle.Size.Width + 12, 3)
        Me.btnIPSW.Location = New Point(Me.btnSelectDevice.Location.X + 125, 3)

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        Try

        Catch ex As Exception
            Common.LogException(ex.ToString, "BeforeFormClose")
        End Try

        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub

    Protected Overrides Sub OnShown(ByVal e As System.EventArgs)
        MyBase.OnShown(e)

    End Sub

    Protected Overrides Sub OnFormClosing(ByVal e As System.Windows.Forms.FormClosingEventArgs)
        MyBase.OnFormClosing(e)
        Common.Log("*****mTdLoadFirmwareInfos.Abort1")
        Try
            If Me.mTdLoadFirmwareInfos IsNot Nothing AndAlso Me.mTdLoadFirmwareInfos.ThreadState <> ThreadState.Stopped Then
                Me.mTdLoadFirmwareInfos.Abort()
            End If
        Catch
        End Try
        Common.Log("*****mTdLoadFirmwareInfos.Abort2")

    End Sub

    Private Sub InitAppsDataGridView()
        Try
            Me.dgvFirmware = New tbDataGridViewEx

            With Me.dgvFirmware
                .SuspendLayout()

                .tbShowNoData = True
                Dim cellStyle As New DataGridViewCellStyle()
                cellStyle.ForeColor = Color.Black
                'cellStyle.BackColor = Color.White

                Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Flash.Column.Name"), 260, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.True)
                colName.DefaultCellStyle = cellStyle
                .tbMouseHoverResponse = True
                .RowTemplate.Height = 58

                '设定【版本】两行的样式一样
                Dim cellStyleVersion As New DataGridViewCellStyle()
                cellStyleVersion.ForeColor = Color.FromArgb(83, 83, 83)
                cellStyleVersion.SelectionForeColor = Color.Black

                Dim colVersion As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colVersion", Me.Language.GetString("App.Column.Version"), 100, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)

                .Columns.Clear()
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colDefault", "", 8, True, False, DataGridViewContentAlignment.MiddleLeft, True))
                .Columns.Add(colName)
                .Columns.Add(colVersion)
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colSize", Me.Language.GetString("Common.Columns.Size"), 65, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))

                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewImageColumn), "colFlash", Me.Language.GetString("App.Column.Compatibility"), 84, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewImageColumn), "colJailbreak", Me.Language.GetString("App.Column.Jailbreak"), 84, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colStatus", Me.Language.GetString("File.Label.Status"), 82, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))

                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", "", 220, True, False, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colurl", "", 5, False, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colState", "", 5, False, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True))

                .AllowDrop = True
                .Dock = DockStyle.Fill
                .GroupRowTemplateArray = Me.GetGroupTemplate()
                .tbNoDataText = IIf(Common.NetworkIsAvailable, Me.Language.GetString("Media.Label.Empty"), Me.Language.GetString("Welcome.Label.NetDisable"))          '"这里没有内容"\网络不可用
                .ResumeLayout()
            End With

            RemoveHandler dgvFirmware.CellButtonClick, AddressOf dgvFirmware_CellButtonClick
            AddHandler dgvFirmware.CellButtonClick, AddressOf dgvFirmware_CellButtonClick

            'RemoveHandler dgvFirmware.ShowToolTip, AddressOf dgvFirmware_ShowToolTip
            'AddHandler dgvFirmware.ShowToolTip, AddressOf dgvFirmware_ShowToolTip

            Utility.AddForm2Panel(Me.dgvFirmware, Me.pnlFirmware)
        Catch ex As Exception

        End Try

    End Sub

    Private Function GetGroupTemplate() As Object()
        Return New Object() {"", _
                                    "", _
                                    "", _
                                    "", _
                                    My.Resources.cell_false, _
                                    My.Resources.cell_false, _
                                    "", _
                                    "", _
                                    "", _
                                    "", _
                                    ""}
    End Function

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
        Me.InitLoad(device)

        Me.SetPrompt()

    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.mStrFirst = ""
        Me.mListIPSW = Nothing

        Me.SetPrompt()

    End Sub

    Private Sub InitLoad(ByVal device As IDevice)
        Try
            Dim strProductType As String = ""
            Select Case Me.mDeviceMode
                Case DeviceMode.NomalMode
                    strProductType = Me.mDevice.ProductType

                Case DeviceMode.RecoveryMode
                    strProductType = Me.mDeviceRecovery.ProductType

                Case DeviceMode.DFUMode
                    strProductType = Me.mDeviceDFU.ProductType
            End Select

            If strProductType.Length > 0 Then
                Me.CreateDeviceMenu(Nothing, strProductType)
                Me.btnIPSW.Text = Me.mAll
                Me.LoadFirmwares()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitLoad")
        End Try
    End Sub

#End Region

#Region "--- 按钮事件 ---"

    Private Sub btnSelectDevice_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSelectDevice.Click
        Try
            Me.CreateDeviceMenu(Me.btnSelectDevice)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnSelectDevice_Click")
        End Try
    End Sub

    Private Sub btnIPSW_Click(sender As Object, e As EventArgs) Handles btnIPSW.Click
        If Me.mListIPSW IsNot Nothing Then
            Me.BindIPSW(Me.mListIPSW)
        End If
    End Sub

    Private Sub btn_CheckedChanged(sender As Object, e As EventArgs)
        Me.LoadFirmwares()
    End Sub

    Private Sub btnRefresh_Click(sender As Object, e As EventArgs) Handles btnRefresh.Click
        Me.blnInitDeviceMenu = False
        Me.LoadFirmwareInfos()
    End Sub


#End Region

#Region "--- 私有方法 ---"

    Private Sub CreateDeviceMenu(ByVal parent As Control, Optional ByVal strProductType As String = "")
        Try
            Dim iCount As Integer = 0

            'While Me.mDictFirmwareInfos.Count <= 0 AndAlso iCount < 10
            '    Utility.WaitSeconds(0.1)
            '    iCount = iCount + 1
            'End While

            If Me.mDictFirmwareInfos.Count <= 0 Then
                Me.blnInitDeviceMenu = False
                Return
            End If

            Dim menu As New tbContextMenuStrip()
            Dim dictDeviceType As New Dictionary(Of String, List(Of FirmwareInfoExpand))

            Dim strFirst As String = ""
            Dim lstTemp As List(Of FirmwareInfoExpand) = Nothing

            For Each strKey As String In Me.mDictFirmwareInfos.Keys
                Dim menuItem As ToolStripMenuItem = Nothing
                menuItem = menu.Items.Add(strKey, Nothing)
                Dim menuInfoItem As ToolStripMenuItem = Nothing
                For Each fInfo As FirmwareInfoExpand In Me.mDictFirmwareInfos(strKey)

                    If Not dictDeviceType.ContainsKey(fInfo.DeviceName) Then
                        menuInfoItem = menuItem.DropDownItems.Add(fInfo.DeviceName, Nothing)
                        lstTemp = New List(Of FirmwareInfoExpand)
                        lstTemp.Add(fInfo)
                        dictDeviceType.Add(fInfo.DeviceName, lstTemp)
                        menuInfoItem.Tag = lstTemp
                        AddHandler menuInfoItem.Click, AddressOf OnDeviceMenuItemClick
                    Else
                        lstTemp = dictDeviceType(fInfo.DeviceName)
                        If Not lstTemp.Contains(fInfo) Then
                            lstTemp.Add(fInfo)
                            dictDeviceType(fInfo.DeviceName) = lstTemp
                            menuInfoItem.Tag = lstTemp
                        End If
                    End If

                    If Me.btnSelectDevice.Text = fInfo.DeviceName Then
                        menuInfoItem.Checked = True
                    Else
                        menuInfoItem.Checked = False
                    End If

                    If Me.mDevice Is Nothing AndAlso Me.mDeviceDFU Is Nothing AndAlso Me.mDeviceRecovery Is Nothing AndAlso strFirst.Length <= 0 Then
                        strFirst = fInfo.DeviceType
                        Me.mStrFirst = fInfo.DeviceType
                        Me.btnSelectDevice.Text = fInfo.DeviceName
                        Me.mListIPSW = lstTemp
                    End If

                    If strProductType.Length > 0 AndAlso fInfo.DeviceType = strProductType Then
                        Me.btnSelectDevice.Text = fInfo.DeviceName
                        Me.mListIPSW = lstTemp
                    End If

                Next
            Next

            AddHandler menu.Closed, AddressOf OnItemMenuClosed

            Me.blnInitDeviceMenu = True

            If parent IsNot Nothing Then
                menu.Show(parent, New Point(0, parent.Height))
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateDeviceMenu")
        End Try
    End Sub

    Private Sub OnItemMenuClosed(ByVal sender As Object, ByVal args As ToolStripDropDownClosedEventArgs)
        Dim menu As tbContextMenuStrip = CType(sender, tbContextMenuStrip)
        RemoveHandler menu.Closed, AddressOf OnItemMenuClosed
    End Sub

    Private Sub OnDeviceMenuItemClick(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            Dim item As ToolStripMenuItem = CType(sender, ToolStripMenuItem)
            Me.btnSelectDevice.Text = item.Text
            Me.btnSelectDevice.Tag = item.Name
            If item.Tag IsNot Nothing Then
                Dim lstTemp As List(Of FirmwareInfoExpand) = CType(item.Tag, List(Of FirmwareInfoExpand))
                Me.mListIPSW = lstTemp
                Me.btnIPSW.Text = Me.mAll
                Me.LoadFirmwares()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnMenuItemClick")
        End Try
    End Sub

    Private Sub BindIPSW(ByVal lstIPWS As List(Of FirmwareInfoExpand))
        Try

            Dim menu As New tbContextMenuStrip()

            If lstIPWS.Count > 0 Then
                Dim menuInfoItem As ToolStripMenuItem = Nothing
                menuInfoItem = menu.Items.Add(Me.mAll, Nothing)
                AddHandler menuInfoItem.Click, AddressOf OnDeviceMenuIPSWIItemClick
                For Each info As FirmwareInfoExpand In lstIPWS
                    menuInfoItem = menu.Items.Add(info.DeviceType, Nothing)
                    If Not Me.mDictIPWS.ContainsKey(info.DeviceType) Then
                        Me.mDictIPWS.Add(info.DeviceType, info.LstVersion)
                    End If

                    If btnIPSW.Text = info.DeviceType Then
                        menuInfoItem.Checked = True
                    Else
                        menuInfoItem.Checked = False
                    End If

                    AddHandler menuInfoItem.Click, AddressOf OnDeviceMenuIPSWIItemClick
                Next
            End If

            AddHandler menu.Closed, AddressOf OnIPSWItemMenuClosed

            menu.Show(btnIPSW, New Point(0, btnIPSW.Height))

            'Me.cbxIPSW.Items.Clear()
            'Me.mDictIPWS.Clear()
            'Me.cbxIPSW.Items.Add(Me.mAll)
            'Me.mDictIPWS.Add(Me.mAll, Nothing)
            'If lstIPWS.Count > 0 Then
            '    For Each info As FirmwareInfoExpand In lstIPWS
            '        Me.cbxIPSW.Items.Add(info.DeviceType)
            '        Me.mDictIPWS.Add(info.DeviceType, info.LstVersion)
            '    Next
            'End If
            'Me.cbxIPSW.SelectedIndex = 0

        Catch ex As Exception
            Common.LogException(ex.ToString(), "BindIPSW")
        End Try
    End Sub

    Private Sub OnIPSWItemMenuClosed(ByVal sender As Object, ByVal args As ToolStripDropDownClosedEventArgs)
        Dim menu As tbContextMenuStrip = CType(sender, tbContextMenuStrip)
        RemoveHandler menu.Closed, AddressOf OnIPSWItemMenuClosed
    End Sub

    Private Sub OnDeviceMenuIPSWIItemClick(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Try
            Dim item As ToolStripMenuItem = CType(sender, ToolStripMenuItem)
            Me.btnIPSW.Text = item.Text
            Me.LoadFirmwares()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnDeviceMenuIPSWIItemClick")
        End Try
    End Sub

    'Private Sub BindVersion()
    '    Try
    '        Dim menu As New tbContextMenuStrip()
    '        menu.Width = txtVersion.Width + btnVersion.Width

    '        Dim strIPWS As String = Me.txtIPSW.Text
    '        Dim menuInfoItem As ToolStripMenuItem = Nothing
    '        menuInfoItem = menu.Items.Add(Me.mAll, Nothing)
    '        AddHandler menuInfoItem.Click, AddressOf OnDeviceMenuVersionItemClick
    '        If strIPWS = Me.mAll Then
    '            For Each lstVersion As List(Of String) In Me.mDictIPWS.Values
    '                If lstVersion Is Nothing Then
    '                    Continue For
    '                End If
    '                For Each strVersion As String In lstVersion
    '                    If Not menu.Items.ContainsKey(strVersion) Then
    '                        menuInfoItem = menu.Items.Add(strVersion, Nothing)
    '                        AddHandler menuInfoItem.Click, AddressOf OnDeviceMenuVersionItemClick
    '                    End If
    '                Next
    '            Next
    '        Else
    '            Dim lstTemp As New List(Of String)
    '            If Me.mDictIPWS.ContainsKey(strIPWS) Then
    '                lstTemp = Me.mDictIPWS(strIPWS)
    '            End If
    '            For Each strVersion As String In lstTemp
    '                menuInfoItem = menu.Items.Add(strVersion, Nothing)
    '                AddHandler menuInfoItem.Click, AddressOf OnDeviceMenuVersionItemClick
    '            Next
    '        End If

    '        AddHandler menu.Closed, AddressOf OnVersionItemMenuClosed

    '        menu.Show(txtVersion, New Point(0, txtVersion.Height))


    '        'Me.cbxVersion.Items.Clear()
    '        'Me.cbxVersion.Items.Add(Me.mAll)

    '        'Dim strIPWS As String = Me.cbxIPSW.Text
    '        'If strIPWS = Me.mAll Then
    '        '    For Each lstVersion As List(Of String) In Me.mDictIPWS.Values
    '        '        If lstVersion Is Nothing Then
    '        '            Continue For
    '        '        End If
    '        '        For Each strVersion As String In lstVersion
    '        '            If Not Me.cbxVersion.Items.Contains(strVersion) Then
    '        '                Me.cbxVersion.Items.Add(strVersion)
    '        '            End If
    '        '        Next
    '        '    Next
    '        'Else
    '        '    Dim lstTemp As New List(Of String)
    '        '    If Me.mDictIPWS.ContainsKey(strIPWS) Then
    '        '        lstTemp = Me.mDictIPWS(strIPWS)
    '        '    End If
    '        '    For Each strVersion As String In lstTemp
    '        '        Me.cbxVersion.Items.Add(strVersion)
    '        '    Next
    '        'End If
    '        'Me.cbxVersion.SelectedIndex = 0
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "BindVersion")
    '    End Try
    'End Sub

    'Private Sub OnVersionItemMenuClosed(ByVal sender As Object, ByVal args As ToolStripDropDownClosedEventArgs)
    '    Dim menu As tbContextMenuStrip = CType(sender, tbContextMenuStrip)
    '    RemoveHandler menu.Closed, AddressOf OnIPSWItemMenuClosed
    'End Sub

    'Private Sub OnDeviceMenuVersionItemClick(ByVal sender As System.Object, ByVal e As System.EventArgs)
    '    Try
    '        Dim item As ToolStripMenuItem = CType(sender, ToolStripMenuItem)
    '        Me.txtVersion.Text = item.Text
    '        Me.LoadFirmwares()
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "OnDeviceMenuIPSWIItemClick")
    '    End Try
    'End Sub

    Private Sub LoadFirmwares()
        Try
            Dim lstResultInfo As New List(Of FirmwareInfo)
            Dim sbWhere As New StringBuilder

            If Me.btnIPSW.Text.Length <= 0 OrElse Me.mListIPSW Is Nothing Then
                Return
            End If

            If Me.btnIPSW.Text <> Me.mAll Then
                sbWhere.AppendFormat(" and ProductTypes like '%{0}%' ", Me.btnIPSW.Text)
            Else
                Dim strProductTypes As String = ""
                For Each item As FirmwareInfoExpand In mListIPSW
                    strProductTypes = String.Format("{0},'{1}'", strProductTypes, item.DeviceType)
                Next
                sbWhere.AppendFormat(" and ProductTypes in ({0}) ", strProductTypes.Substring(1, strProductTypes.Length - 1))
            End If

            If Me.btnFlash.Checked Then
                sbWhere.AppendFormat(" and CanRestore = '{0}' ", True)
            End If

            If Me.btnJailBreak.Checked Then
                sbWhere.AppendFormat(" and CanJailbreak = '{0}' ", True)
            End If

            If Me.btnFlashAndJailBreak.Checked Then
                sbWhere.AppendFormat(" and CanJailbreak='{0}' and CanRestore ='{0}'  ", True)
            End If

            If sbWhere.ToString().Length <= 0 Then
                Return
            End If

            lstResultInfo = FlashHelper.GetFlashFirmwareInfoList(sbWhere.ToString())
            ''有的时候会取不到数据就等一秒后再查一次
            ''多设备快速切换的时候容易出现
            'If lstResultInfo Is Nothing OrElse lstResultInfo.Count <= 0 Then
            '    Utility.WaitSeconds(1)
            '    lstResultInfo = FlashHelper.GetFlashFirmwareInfoList(sbWhere.ToString())
            'End If

            Me.AddListToDataGridView(lstResultInfo)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadFirmwares")
        End Try
    End Sub

    Public Sub LoadFirmwareInfos()
        If mTdLoadFirmwareInfos IsNot Nothing AndAlso mTdLoadFirmwareInfos.ThreadState <> ThreadState.Stopped Then
            Debug.Print("AAAAAAAA1:")
            Return
        End If
        Debug.Print("AAAAAAAA:")
        Me.mTdLoadFirmwareInfos = New Thread(AddressOf DoLoadFirmwareInfos)
        Me.mTdLoadFirmwareInfos.IsBackground = False
        Me.mTdLoadFirmwareInfos.Start()
    End Sub

    Private Sub DoLoadFirmwareInfos()
        Me.SetViewStyle(ViewStyle.Loading)

        Try
            Me.mDictFirmwareInfos = FlashHelper.GetAllFlashFirmwareInfoList()

            '如果获取失败停1秒在获取一次
            If Me.mDictFirmwareInfos Is Nothing OrElse Me.mDictFirmwareInfos.Count <= 0 Then
                Utility.WaitSeconds(1)
                Me.mDictFirmwareInfos = FlashHelper.GetAllFlashFirmwareInfoList()
            End If
          
            If Me.mDictFirmwareInfos.Count <= 0 Then
                Me.SetViewStyle(ViewStyle.NoData)
                Return
            End If

            If Not Me.blnInitDeviceMenu Then
                Dim iDevice As IDevice = Nothing
                Select Case Me.mDeviceMode
                    Case DeviceMode.NomalMode
                        iDevice = Me.mDevice
                        
                    Case DeviceMode.RecoveryMode
                        iDevice = Me.mDeviceRecovery

                    Case DeviceMode.DFUMode
                        iDevice = Me.mDeviceDFU
                End Select
                InitLoad(iDevice)
            End If

            '如果没有设备 就默认选择 iphone 最新
            If Me.mDevice Is Nothing AndAlso Me.mDeviceDFU Is Nothing AndAlso Me.mDeviceRecovery Is Nothing AndAlso Me.mStrFirst.Length <= 0 Then
                Me.btnIPSW.Text = Me.mAll
                Me.CreateDeviceMenu(Nothing)
                Me.LoadFirmwares()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmFirmware_DoLoadFirmwareInfos")
        End Try

        Me.SetViewStyle(ViewStyle.Show)
    End Sub

    Private Delegate Sub SetViewStyleHandler(ByVal style As ViewStyle)
    Private Sub SetViewStyle(ByVal style As ViewStyle)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SetViewStyleHandler(AddressOf SetViewStyle), style)
            Else
                Select Case style
                    Case ViewStyle.Loading
                        If Me.tblayoutMain.ColumnStyles(0).Width <> 100 Then
                            Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(0).Width = 100
                            Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(1).Width = 0
                            Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(2).Width = 0
                        End If

                    Case ViewStyle.Show
                        If Me.tblayoutMain.ColumnStyles(1).Width <> 100 Then
                            Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(0).Width = 0
                            Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(1).Width = 100
                            Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(2).Width = 0
                        End If

                    Case ViewStyle.NoData
                        If Me.tblayoutMain.ColumnStyles(2).Width <> 100 Then
                            Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(0).Width = 0
                            Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(1).Width = 0
                            Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                            Me.tblayoutMain.ColumnStyles(2).Width = 100
                        End If

                End Select

                Application.DoEvents()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetViewStyle")
        End Try
    End Sub


    Private Sub SetPrompt()
        If Me.mDevice IsNot Nothing OrElse Me.mDeviceDFU IsNot Nothing OrElse Me.mDeviceRecovery IsNot Nothing Then

            Dim strProductType As String = ""
            Dim strProductVersion As String = ""
            'Select Case Me.mDeviceMode
            '    Case DeviceMode.NomalMode
            '        strProductType = Me.mDevice.ProductType
            '        strProductVersion = Me.mDevice.ProductVersion

            '    Case DeviceMode.RecoveryMode
            '        strProductType = Me.mDeviceRecovery.ProductType

            '    Case DeviceMode.DFUMode
            '        strProductType = Me.mDeviceDFU.ProductType
            'End Select

            If Me.mDevice IsNot Nothing Then
                strProductType = Me.mDevice.ProductType
                strProductVersion = Me.mDevice.ProductVersion
            ElseIf Me.mDeviceDFU IsNot Nothing Then
                strProductType = Me.mDeviceDFU.ProductType
            ElseIf Me.mDeviceRecovery IsNot Nothing Then
                strProductType = Me.mDeviceRecovery.ProductType
            End If


            Me.lblPrompt.Text = String.Format("{0}:{1}({2})", Me.Language.GetString("Welcome.Label.ProductType"), SummaryInfo.FormatProduct(strProductType), strProductType)
            If strProductVersion.Length > 0 Then
                Me.lblPrompt.Text += String.Format("  {0}:{1}", Me.Language.GetString("Welcome.Label.ProductVersion"), strProductVersion)
            End If
        Else
            Me.lblPrompt.Text = Me.Language.GetString("Flash.Lable.Prompt") ' "【温馨提示】连接设备将会自动筛选适用于您设备的固件！"
        End If
    End Sub

    'Private Delegate Sub SetStatePanelVisibleHandler(ByVal value As Boolean, ByVal text As String)
    'Private Sub SetStatePanelVisible(ByVal value As Boolean, Optional ByVal text As String = "")
    '    If Me.InvokeRequired Then
    '        Me.Invoke(New SetStatePanelVisibleHandler(AddressOf SetStatePanelVisible), value, text)
    '    Else
    '        If value Then
    '            If text.Length > 0 Then
    '                Me.lblPrompt.Text = text
    '            Else
    '                Me.lblPrompt.Text = "【温馨提示】连接设备将会自动筛选适用于您设备的固件！"
    '            End If

    '            If Me.tblayoutMain.RowStyles(5).Height <> 30 Then
    '                Me.tblayoutMain.RowStyles(5).SizeType = SizeType.Absolute
    '                Me.tblayoutMain.RowStyles(5).Height = 30

    '                Me.tblayoutMain.RowStyles(4).SizeType = SizeType.Absolute
    '                Me.tblayoutMain.RowStyles(4).Height = 1

    '                Application.DoEvents()
    '            End If
    '        Else
    '            Me.tblayoutMain.RowStyles(5).SizeType = SizeType.Absolute
    '            Me.tblayoutMain.RowStyles(5).Height = 0

    '            Me.tblayoutMain.RowStyles(4).SizeType = SizeType.Absolute
    '            Me.tblayoutMain.RowStyles(4).Height = 0

    '            Application.DoEvents()
    '        End If
    '    End If
    'End Sub


#End Region

#Region " -- 加载固件列表 -- "

    Private Delegate Sub AddListToDataGridViewHandler(ByVal infoList As List(Of FirmwareInfo))
    Private Sub AddListToDataGridView(ByVal infoList As List(Of FirmwareInfo))
        If Me.InvokeRequired Then
            Me.Invoke(New AddListToDataGridViewHandler(AddressOf AddListToDataGridView), New Object() {infoList})
        Else
            Try
                Me.dgvFirmware.SuspendLayout()
                Me.mDictGridRow.Clear()
                Me.dgvFirmware.Rows.Clear()
                Me.dgvFirmware.DictGroupKeys.Clear()

                Dim isSort As Boolean = True
                If Me.dgvFirmware.Rows.Count > 0 Then
                    isSort = False
                End If

                Dim row As New tbDataGridViewRow()
                Dim imgRestore As Image = My.Resources.photo_chk_true
                Dim imgJailbreak As Image = My.Resources.photo_chk_true
                For Each info As FirmwareInfo In infoList
                    row = New tbDataGridViewRow()

                    If info.CanRestore Then
                        imgRestore = My.Resources.cell_true
                    Else
                        imgRestore = My.Resources.cell_false
                    End If

                    If info.CanJailbreak Then
                        imgJailbreak = My.Resources.cell_true
                    Else
                        imgJailbreak = My.Resources.cell_false
                    End If

                    row.Height = 58
                    'row.CreateCells(Me.dgvFirmware, _
                    '                "", _
                    '                info.Version, _
                    '                info.Name, _
                    '                info.SizeDescription, _
                    '                imgRestore, _
                    '                imgJailbreak, _
                    '                info.Status, _
                    '                info.DowmloadUrl, _
                    '                "", _
                    '                "")
                    If info.Status = FirmwareStatus.Import AndAlso Not File.Exists(info.FirmwarePath) Then '导入后文件删除 或这目录改变
                        info.Status = FirmwareStatus.None
                    End If

                    Dim aryParameters As Object() = New Object() {"", _
                                    info.Name, _
                                    info.Version, _
                                    info.SizeDescription, _
                                    imgRestore, _
                                    imgJailbreak, _
                                    info.Status, _
                                    info.Status, _
                                    info.DowmloadUrl, _
                                    "", _
                                    ""}


                    'Me.dgvFirmware.Rows.Add(row)



                    Dim strGroupText As String = info.ProductType
                    Me.dgvFirmware.AddGroup(strGroupText, Me.dgvFirmware.GroupRowTemplateArray, String.Empty)
                    row = Me.dgvFirmware.AddGroupItem(strGroupText, aryParameters, String.Empty)

                    Me.mDictGridRow.Add(info.Version & info.ProductType, row)

                    Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                    LoadCellProgress(info.Status, cellProgress)

                    Dim cellState As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")
                    LoadCellState(info.Status, cellState)

                    Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                    cellName.tbIcon = My.Resources.download_icon_firmware
                    cellName.tbIconShadow = True

                    If isSort Then
                        '按名称排序
                        Me.dgvFirmware.Sort(Me.dgvFirmware.Columns("colVersion"), System.ComponentModel.ListSortDirection.Descending)
                    End If

                    row.Tag = info

                    Me.dgvFirmware.tbShowNoData = True
                    Me.dgvFirmware.ResumeLayout()
                Next

            Catch ex As Exception
                Common.LogException(ex.ToString(), "AddListToDataGridView")
            End Try
        End If

    End Sub

    Private Sub LoadCellState(ByVal status As FirmwareStatus, cellState As tbDataGridViewTextBoxCellEx)
        Dim strStatus As String = Me.Language.GetString("Flash.Column.UnDowmload") '"未下载"
        Dim colStatus As Color = Color.FromArgb(153, 153, 153)
        Select Case status
            Case FirmwareStatus.None, FirmwareStatus.DownloadCancel, FirmwareStatus.DownloadDeleted, FirmwareStatus.failed, FirmwareStatus.UnDownload

            Case FirmwareStatus.Downloaded, FirmwareStatus.DownloadSucceed, FirmwareStatus.Import

                If FirmwareStatus.Import = status Then
                    strStatus = Me.Language.GetString("Flash.Column.Imported") '"已导入"
                Else
                    strStatus = Me.Language.GetString("Download.Column.Downloaded") '"已下载"
                End If
                colStatus = Color.FromArgb(0, 170, 0)

            Case FirmwareStatus.downloading, FirmwareStatus.waiting, FirmwareStatus.pause
                strStatus = Me.Language.GetString("Download.Label.Waiting") '"Waitting"

            Case FirmwareStatus.ImportFailed
                strStatus = Me.Language.GetString("Weixin.Message.EmportFailure") '导入失败
                colStatus = Color.FromArgb(255, 92, 92)
        End Select
        cellState.tbForeColor = colStatus
        cellState.tbSelectionForeColor = colStatus

        cellState.Value = strStatus
    End Sub
    Private Sub LoadCellProgress(ByVal status As FirmwareStatus, ByVal cellProgress As tbDataGridViewProgressCellEx)
        Dim cellButtonStyle As CellButtonStyle = cellButtonStyle.BackupFolder Or cellButtonStyle.Reinstall Or cellButtonStyle.Failed Or _
                                                                     cellButtonStyle.OneButton Or cellButtonStyle.Ignore Or cellButtonStyle.Document Or cellButtonStyle.Cancel Or cellButtonStyle.Backup Or cellButtonStyle.None

        Select Case status
            Case FirmwareStatus.None, FirmwareStatus.DownloadCancel, FirmwareStatus.DownloadDeleted, FirmwareStatus.failed, FirmwareStatus.UnDownload, FirmwareStatus.ImportFailed

                cellProgress.tbProgressStyle = ProgressStyle.Button
                cellProgress.tbTextBackup = Me.Language.GetString("Site.Label.Download") '下载’
                cellProgress.tbTextUnInstall = Me.Language.GetString("Flash.Column.OpenUrl") '"开打连接"
                ' cellProgress.tbTextImport = Me.Language.GetString("Flash.Label.ImportFirmware") '"导入固件 
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowButtonImport = False

            Case FirmwareStatus.Downloaded, FirmwareStatus.DownloadSucceed, FirmwareStatus.Import

                 cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.Button
                cellProgress.tbShowButtonFolder = False
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowButtonImport = False

                cellProgress.tbTextBackup = Me.Language.GetString("Common.Button.Open") '打开’
                cellProgress.tbTextUnInstall = Me.Language.GetString("App.Lable.Remover") '"移除"

            Case FirmwareStatus.downloading, FirmwareStatus.waiting, FirmwareStatus.pause

                cellProgress.Enabled = True
                cellProgress.tbShowCancelButton = False
                cellProgress.tbProgressStyle = ProgressStyle.Text
                cellProgress.tbText = Me.Language.GetString("Download.Label.Waiting") '"正在等待"
                cellProgress.Tag = DownloadStatus.Waiting

        End Select

    End Sub

#End Region

#Region "--- DataGridView ---"

    Private Sub dgvFirmware_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvFirmware.Rows(e.Cell.RowIndex)
            Dim strUrl As String = row.Cells("colurl").Value
            Dim info As FirmwareInfo = CType(row.Tag, FirmwareInfo)

            Select Case e.CellButton
                Case CellButtonStyle.OneButton, CellButtonStyle.Backup
                    If info.Status = FirmwareStatus.None OrElse info.Status = FirmwareStatus.DownloadCancel OrElse info.Status = FirmwareStatus.DownloadDeleted OrElse info.Status = FirmwareStatus.failed OrElse info.Status = FirmwareStatus.UnDownload Then

                    ElseIf info.Status = FirmwareStatus.Downloaded OrElse info.Status = FirmwareStatus.DownloadSucceed OrElse info.Status = FirmwareStatus.Import Then
                        If File.Exists(info.FirmwarePath) Then
                            Common.OpenExplorer(info.FirmwarePath)
                            Return
                        End If
                    End If

                    Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = Me.Language.GetString("Download.Label.Waiting") '"Waitting"
                    cell.Tag = FirmwareStatus.waiting

                    info.Status = FirmwareStatus.None
                    Me.UpdateDataGridViewRow(info, row)

                    FlashHelper.DownloadIPSW(strUrl)

                Case CellButtonStyle.Uninstall
                    Common.OpenExplorer(strUrl)

                Case CellButtonStyle.Document
                    Common.OpenExplorer(info.FirmwarePath)

                Case CellButtonStyle.Cancel
                    If tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.AreYouSureDeleteTask"), _
                                     Me.Language.GetString("Common.Delete"), _
                                     MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then

                        Dim cellOthers As tbDataGridViewTextBoxCell = row.Cells("colOther")
                        Dim item As MultiThreadDownloadItem = CType(cellOthers.Tag, MultiThreadDownloadItem)
                        FlashHelper.StopDownloadIPSW(item)

                    End If

                Case CellButtonStyle.BackupFolder

                Case CellButtonStyle.Reinstall

                Case CellButtonStyle.Failed

                Case CellButtonStyle.ProgressSpeed

            End Select
        Catch ex As Exception
        End Try
    End Sub

    Private Sub dgvFirmware_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvFirmware.Rows(e.Cell.RowIndex)
            Dim pos As Point = Windows.Forms.Cursor.Position
            Dim secPos As Point = Me.PointToClient(pos)
            Dim newPos As New Point(secPos.X + 10, secPos.Y + 10)

            Select Case e.CellButton
                Case CellButtonStyle.Backup
                    Dim state As FirmwareStatus = CType(row.Cells("colOther").Tag, FirmwareStatus)
                    Dim strText As String = ""
                    Select Case state
                        Case FirmwareStatus.downloading, FirmwareStatus.waiting
                            strText = Me.Language.GetString("Download.Button.Pause")        '"暂停"
                        Case FirmwareStatus.failed
                            strText = Me.Language.GetString("Download.Button.Restart")      '"重新开始"
                        Case FirmwareStatus.pause
                            strText = Me.Language.GetString("Download.Button.Start")         '"开始"
                    End Select
                    Me.tipDgvCell.Show(strText, Me, newPos)
                Case CellButtonStyle.Uninstall
                    Me.tipDgvCell.Show(Me.Language.GetString("Common.Delete"), Me, newPos)  '"删除 "
                Case CellButtonStyle.Cancel

                Case CellButtonStyle.BackupFolder

                Case CellButtonStyle.OneButton

                Case CellButtonStyle.None
                    Me.tipDgvCell.Hide(Me)

                Case CellButtonStyle.ProgressSpeed
                    Dim strText As String = CType(e.Cell, tbDataGridViewProgressCellEx).tbToolTipText
                    If Not String.IsNullOrEmpty(strText) Then
                        Me.tipDgvCell.Show(strText, Me, newPos)
                    End If

            End Select
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Sub UpdateDataGridViewRowHandler(ByVal info As FirmwareInfo, ByVal row As tbDataGridViewRow)
    Private Sub UpdateDataGridViewRow(ByVal info As FirmwareInfo, ByVal row As tbDataGridViewRow)
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateDataGridViewRowHandler(AddressOf UpdateDataGridViewRow), info, row)
        Else
            Try
                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                'LoadCellProgress(FirmwareStatus.Import, cellProgress)
                LoadCellProgress(info.Status, cellProgress)

                Dim oldInfo As FirmwareInfo = CType(row.Tag, FirmwareInfo)
                oldInfo.ProductVersion = info.ProductVersion
                oldInfo.FirmwarePath = info.FirmwarePath
                oldInfo.Status = IIf(info.Status = FirmwareStatus.ImportFailed, FirmwareStatus.None, info.Status)
                row.Tag = oldInfo
                Me.mDictGridRow(oldInfo.Version & oldInfo.Name & oldInfo.ProductBuildVersion) = row

                Me.mFlashFirmwareDB.Update_Firmware_Info(oldInfo)

                Dim cellState As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")
                LoadCellState(info.Status, cellState)

                Common.Log(String.Format("****FlashFirmwareDB**** FirmwarePath:{0} Status:{1}", oldInfo.FirmwarePath, oldInfo.Status.ToString()))

            Catch ex As Exception
                Common.LogException(ex.ToString(), "UpdateDataGridViewRow")
            End Try
        End If
    End Sub

#End Region

#Region "--- 下载进度 ---"

    Private Sub FlashHelper_OnDowmloadFirmwareProgress(ByVal sender As Object, ByVal e As DowmloadFirmwareProgressEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        If item.ItemInfo.Class <> ResourceClass.Firmware Then
            Return
        End If

        Dim iProgress As Integer = e.IntProgress

        Dim row As tbDataGridViewRow = Me.GetRowByUrl(item.ItemInfo.Url)
        If row Is Nothing Then
            Return
        End If

        Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        Dim cellState As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")

        Select Case e.FState
            Case FirmwareStatus.waiting
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = Me.Language.GetString("Download.Label.Waiting") '"Waitting"
                cell.Tag = FirmwareStatus.waiting

            Case FirmwareStatus.downloading
                Me.LoadCellState(FirmwareStatus.downloading, cellState)

                Dim cellOthers As tbDataGridViewTextBoxCell = row.Cells("colOther")

                If cellOthers.Tag Is Nothing OrElse item.ItemInfo.TaskID <> CType(cellOthers.Tag, MultiThreadDownloadItem).ItemInfo.TaskID Then
                    '以便取消下载的时候可以获取到这个数据
                    cellOthers.Tag = item
                End If

                cell.Enabled = True
                cell.tbShowCancelButton = True
                cell.tbProgressStyle = ProgressStyle.Progress
                cell.tbProgressValue = iProgress
                cell.tbText = Me.Language.GetString("App.Cell.Downloading") '"Downloading"
                cell.Tag = FirmwareStatus.downloading

            Case FirmwareStatus.None, FirmwareStatus.DownloadCancel, FirmwareStatus.DownloadDeleted
                cell.tbProgressStyle = ProgressStyle.Button
                cell.tbTextBackup = Me.Language.GetString("Site.Label.Download") '下载’
                cell.tbTextUnInstall = Me.Language.GetString("Flash.Column.OpenUrl") '"开打连接"

            Case FirmwareStatus.failed
                cell.tbProgressStyle = ProgressStyle.OneButton
                cell.tbShowCancelButton = False
                cell.tbText = Me.Language.GetString("Common.Retry") '"重试"
                cell.Tag = DownloadStatus.Nomal

            Case FirmwareStatus.DownloadSucceed
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = Me.Language.GetString("App.Cell.DownloadSucceed") '"下载成功"
                cell.tbShowButtonFolder = True
                Dim info As FirmwareInfo = CType(row.Tag, FirmwareInfo)
                If info IsNot Nothing Then
                    '下载完成给界面赋值
                    info.FirmwarePath = item.ItemInfo.FilePath
                    info.Status = FirmwareStatus.Downloaded
                    Me.mFlashFirmwareDB.Update_Firmware_Info(info)
                End If

        End Select
    End Sub

    Private Function GetRowByUrl(ByVal strUrl As String) As tbDataGridViewRow
        Dim rowResult As tbDataGridViewRow = Nothing
        For Each row As tbDataGridViewRow In dgvFirmware.Rows
            If row.Cells("colurl").Value = strUrl Then
                rowResult = row
                Exit For
            End If
        Next
        Return rowResult
    End Function

#End Region

End Class