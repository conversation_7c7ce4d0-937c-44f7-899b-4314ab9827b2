﻿Imports System.Threading
Imports iTong.CoreFoundation.FlashInfo
Imports ProgressStyle = iTong.Components.ProgressStyle

Public Class frmFlash

#Region "--- 变量 属性 ---"
    Private dgvFirmware As tbDataGridViewEx = Nothing
    Private mFlashHelper As FlashHelper = Nothing
    Private mDictGridRow As New Dictionary(Of String, tbDataGridViewRow)
    Private mTdMathing As Thread
    Private mFlashFirmwareDB As FlashFirmwareDB = Nothing
    Private mCurrentRow As DataGridViewRow = Nothing
    Private mIconSize As New Size(40, 40)
    Private mLstSoftID As New List(Of Long)
    Private mIsRunInChina As Boolean = True

    Private mFontPercent As Font = Nothing
    Private mFontProgress As Font = Nothing

    Private mRestoreStatus As RestoreProgressStatus = RestoreProgressStatus.None

    Private mIntProgress As Integer = 0

    Private mIsAuto As Boolean = False

    Private mColSelect As Color = Color.FromArgb(15, 149, 216)
    Private mColUnSelect As Color = Color.FromArgb(186, 186, 186)

    Private mStrFlashInfo As String = ""

    Const WM_COPYDATA As Integer = &H4A
    Const SC_RESTORE As Int32 = &HF121

    Private mTdFlashLog As Thread

    Private mLoginAppleid As String = ""

    Private mErrorCode As Integer = 0

    Private mServerShowFlashActivate As Boolean = True

    Private mIsInstallTui As Boolean = False

    '界面样式类型
    Private Enum ViewStyle
        UnConnect
        SelectIPSW
        FalshProgress
        FalshResult
        ActivateResult
    End Enum

#End Region

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As IDevice)
        MyBase.New(application, device)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        Me.mIsRunInChina = Not Folder.LangType = LanguageType.en_US
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language

        'Me.mIsRecoverMode = (TypeOf device Is iPhoneRecoveryDevice)

        'If Me.mIsRecoverMode Then
        '    Me.mDeviceRecovery = device
        'Else
        '    Me.mDevice = device
        'End If

        Me.mServerShowFlashActivate = ServerIniSetting.GetShowFlashActivate()

        Dim isShowActivate As Boolean = False
        If Utility.IsTestFlash Then
            isShowActivate = True
            Me.btnActivate.Text = "刷机"
        ElseIf Utility.IsTestFlashActivate Then
            isShowActivate = True
            Me.btnActivate.Text = "激活"
        End If

        Me.btnActivate.Visible = isShowActivate
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.mFontPercent = Common.CreateFont("微软雅黑", 14.25F, FontStyle.Bold)
        Me.mFontProgress = Common.CreateFont("微软雅黑", 36.0F, FontStyle.Bold)

        Me.btnActivate.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.btnBackup.Font = Common.CreateFont("微软雅黑", 9.5!, FontStyle.Regular)
        Me.btnStartFlash.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.Label1.Font = Common.CreateFont("微软雅黑", 14.25!, FontStyle.Regular)
        Me.lblPercentage.Font = Common.CreateFont("微软雅黑", 22.0!, FontStyle.Regular)
        Me.lblProgress.Font = Common.CreateFont("微软雅黑", 12.0!, FontStyle.Regular)
        Me.lblFlashWaiting.Font = Common.CreateFont("微软雅黑", 17.0!, FontStyle.Regular)
        Me.lblFlashWaitingMsg.Font = Common.CreateFont("微软雅黑", 9.0!, FontStyle.Regular)
        Me.lklblQQ.Font = Common.CreateFont("微软雅黑", 9.5!, FontStyle.Regular)
        Me.lblQQ.Font = Common.CreateFont("微软雅黑", 9.5!, FontStyle.Regular)
        Me.lklblBBSUrl.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.lblBBSUrl.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.lblErrorMsg.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.lblEMsg.Font = Common.CreateFont("微软雅黑", 9.5!, FontStyle.Regular)
        Me.lblECode.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.lblErrorCode.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.btnRetry.Font = Common.CreateFont("微软雅黑", 13.0!, FontStyle.Regular)
        Me.btnReturn.Font = Common.CreateFont("微软雅黑", 13.0!, FontStyle.Regular)
        Me.lblFileEmpty.Font = Common.CreateFont("微软雅黑", 17.0!, FontStyle.Regular)
        Me.btnSecond.Font = Common.CreateFont("微软雅黑", 9.75!, FontStyle.Regular)
        Me.btnFirst.Font = Common.CreateFont("微软雅黑", 9.75!, FontStyle.Regular)

        Me.btnFirst.tbShowTip = False
        Me.btnSecond.tbShowTip = False

        Me.btnFirst.tbIconImageState = ImageState.OneState
        Me.btnFirst.tbIconImage = GuiHelper.GetMouseStateImage(My.Resources.flash_first, 0, 2)
        Me.btnFirst.tbTextColor = Me.mColSelect
        Me.btnFirst.tbTextColorDisable = Me.mColSelect
        Me.btnFirst.tbTextColorDown = Me.mColSelect
        Me.btnFirst.tbTextColorHover = Me.mColSelect

        Me.btnSecond.tbIconImageState = ImageState.OneState
        Me.btnSecond.tbIconImage = GuiHelper.GetMouseStateImage(My.Resources.flash_second, 1, 2)
        Me.btnSecond.tbTextColor = Me.mColUnSelect
        Me.btnSecond.tbTextColorDisable = Me.mColUnSelect
        Me.btnSecond.tbTextColorDown = Me.mColUnSelect
        Me.btnSecond.tbTextColorHover = Me.mColUnSelect

        Me.InitAppsDataGridView()

        If Me.mDevice Is Nothing AndAlso Me.mDeviceRecovery Is Nothing AndAlso Me.mDeviceDFU Is Nothing Then
            Me.SetViewStyle(ViewStyle.UnConnect)
        Else
            Me.SetViewStyle(ViewStyle.SelectIPSW)
            Me.InitFlashHelper()
        End If

        If Me.mFlashFirmwareDB Is Nothing Then
            Me.mFlashFirmwareDB = FlashFirmwareDB.GetInstance()
        End If

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.lblDevNoConn.Text = Me.Language.GetString("Welcome.Label.DeviceNoConnected")                     '"设备未连接"
        Me.lblLinkMode.Text = Me.Language.GetString("Flash.Label.LinkModeiOS")                               '"支持iPhone/iPad，请用数据线将设备连接到电脑"
        Me.btnImport.Text = Me.Language.GetString("Flash.Label.ImportFirmware")                             ' "快速导入固件"
        Me.btnFirst.Text = Me.Language.GetString("Flash.Label.First")                                       ' "固件匹配"
        Me.btnSecond.Text = Me.Language.GetString("Flash.Label.Second")                                     '"执行刷机"
        Me.cbxRetainData.Text = Me.Language.GetString("Flash.CheckBox.RetainData")                          '"保留用户资料刷机"
        Me.cbxActivate.Text = Me.Language.GetString("Flash.CheckBox.Activate")                              '"连接成功自动启动设备"
        Me.btnStartFlash.Text = Me.Language.GetString("Flash.Button.StartFlash")                            ' "开始刷机"
        Me.btnRefresh.Text = Me.Language.GetString("Flash.Button.Refresh")                                  ' "刷新数据"
        Me.btnBackup.Text = Me.Language.GetString("Backup.Text.Title")                                      ' "备份还原"
        Me.lblFlashWaiting.Text = Me.Language.GetString("Flash.Label.FlashWaiting")                         '"正在执行刷机，请耐心等待... ..."
        Me.lblFlashWaitingMsg.Text = Me.Language.GetString("Flash.Label.FlashWaitingMsg")                   ' "刷机过程中，请勿断开设备或对设备进行其他操作"
        Me.lblProgress.Text = Me.Language.GetString("Flash.Button.StartFlash")                              ' "开始刷机"
        Me.lblFileEmpty.Text = Me.Language.GetString("Flash.Label.FlashSucceed")                            ' "恭喜您刷机成功！"
        Me.lblBBSUrl.Text = Me.Language.GetString("Flash.Label.BBSUrl")                                     ' "论坛连接："
        Me.lblQQ.Text = Me.Language.GetString("Flash.Label.QQ")                                             ' "教程无法解决，请联系客服"
        Me.btnReturn.Text = Me.Language.GetString("Common.Button.Return")                                   '"返回"

        Me.lblErrorCode.Text = Me.Language.GetString("Flash.Label.ErrorCode")                               '"错误代码"
        Me.lblErrorMsg.Text = Me.Language.GetString("Flash.Label.ErrorMsg")                                 '"错误消息"

        Me.lklblBBSUrl.Text = Me.Language.GetString("Flash.Column.OpenUrl")                                  '"打开链接"

        Me.lblPrompt.Text = Me.Language.GetString("Flash.Column.Prompt")                                    '提示：带有运营商设备锁的有锁设备因其特殊性，尚未兼容，请谨慎操作！

        Me.btnRetry.Text = Me.Language.GetString("Common.Retry")                                             '重试

        Me.lblECode.Location = New Point(Me.lblErrorCode.Width + Me.lblErrorCode.Left, Me.lblErrorCode.Location.Y)
        Me.lblEMsg.Location = New Point(Me.lblErrorMsg.Width + Me.lblErrorMsg.Left, Me.lblErrorMsg.Location.Y)

        Me.lklblBBSUrl.Location = New Point(Me.lblBBSUrl.Width + Me.lblBBSUrl.Left, Me.lblBBSUrl.Location.Y)
        Me.lklblQQ.Location = New Point(Me.lblQQ.Left + Me.lblQQ.Width, Me.lblQQ.Location.Y)

        Me.rtxtTitle.Clear()
        Dim strAoto As String = Me.Language.GetString("Flash.Label.AotoFirmware") '"自动*匹配*固件"
        Dim strOnekey As String = Me.Language.GetString("Flash.Label.OneKeyFlash") '"-一键-智能刷机"

        Dim strNAoti As String = strAoto.Replace("*", "")
        Dim strNOnekey As String = strOnekey.Replace("-", "")

        Me.rtxtTitle.AppendText(strNAoti)
        Me.rtxtTitle.AppendText(" ")
        Me.rtxtTitle.AppendText(strNOnekey)

        Dim colSelect As Color = Color.FromArgb(24, 96, 194)

        Me.Selection(strAoto, Me.rtxtTitle, "*", colSelect, 0)
        Me.Selection(strOnekey, Me.rtxtTitle, "-", colSelect, strNAoti.Length)

        MainForm.SetButtonStatus(Me.btnRefresh)

    End Sub

    Private Sub Selection(strText As String, rtxt As RichTextBox, strTag As String, colSelect As Color, intLength As Integer)
        Dim iStart As Integer = strText.IndexOf(strTag) + intLength '  IIf(intLength = 0, 0, intLength - 1)
        Dim iLength As Integer = intLength + strText.LastIndexOf(strTag) - iStart - IIf(intLength = 0, 1, 0)
        rtxt.SelectionStart = iStart
        rtxt.SelectionLength = iLength
        rtxt.SelectionColor = colSelect
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        Try
            RemoveHandler Me.mFlashHelper.LoadFlashFirmwareInfoListEventHandler, AddressOf FlashHelper_OnLoadFlashFirmwareInfoList

        Catch ex As Exception
            Common.LogException(ex.ToString, "BeforeFormClose")
        End Try

        MyBase.BeforeFormClose(pblnCancelClose)
    End Sub

    Protected Overrides Sub OnSizeChanged(ByVal e As System.EventArgs)
        MyBase.OnSizeChanged(e)
        Me.SetLocation()
    End Sub

    Private Sub SetLocation()
        If Me.pnlStep IsNot Nothing Then
            Dim dAllWdith As Double = Me.btnFirst.Width + Me.btnDecollator.Width + Me.btnSecond.Width
            Dim dAllX As Double = (Me.pnlStep.Width - dAllWdith) / 2

            Me.btnFirst.Location = New Point(dAllX, 3)
            Me.btnDecollator.Location = New Point(Me.btnFirst.Width + btnFirst.Location.X, 16)
            Me.btnSecond.Location = New Point(Me.btnDecollator.Width + Me.btnDecollator.Location.X, 3)

            Dim iWidth As Integer = Me.cbxRetainData.Width
            If iWidth < Me.cbxActivate.Width Then
                iWidth = Me.cbxActivate.Width
            End If

            Me.btnRefresh.Location = New Point(17, 9)

            Dim IsXP As Boolean = False
            If OSHelper.OSType = OSType.WinXP64 OrElse OSHelper.OSType = OSType.XP OrElse OSHelper.OSType = OSType.XP_SP2 Then
                IsXP = True
            End If

            Dim IsShowActivate As Boolean = True
            If IsXP AndAlso Me.mServerShowFlashActivate = False Then
                IsShowActivate = False
            End If
            If Me.cbxActivate.Visible <> IsShowActivate Then
                Me.cbxActivate.Visible = IsShowActivate
            End If
            If IsShowActivate Then
                Me.cbxRetainData.Location = New Point(Me.btnStartFlash.Location.X - iWidth - 10, 8)
                Me.cbxActivate.Location = New Point(Me.btnStartFlash.Location.X - iWidth - 10, 35)
            Else
                Me.cbxRetainData.Location = New Point(Me.btnStartFlash.Location.X - iWidth - 10, 22)
            End If
        End If
    End Sub

    Private Sub InitFlashHelper()
        Try
            Dim strProductType As String = ""

            Select Case Me.mDeviceMode
                Case DeviceMode.NomalMode
                    Me.mFlashHelper = FlashHelper.GetInstance(Me.mDevice)
                    strProductType = Me.mDevice.ProductType

                Case DeviceMode.RecoveryMode
                    Me.mFlashHelper = FlashHelper.GetInstance(Me.mDeviceRecovery)
                    strProductType = Me.mDeviceRecovery.ProductType

                Case DeviceMode.DFUMode
                    Me.mFlashHelper = FlashHelper.GetInstance(Me.mDeviceDFU)
                    strProductType = Me.mDeviceDFU.ProductType
            End Select

            '获取 固件列表
            RemoveHandler Me.mFlashHelper.LoadFlashFirmwareInfoListEventHandler, AddressOf FlashHelper_OnLoadFlashFirmwareInfoList
            AddHandler Me.mFlashHelper.LoadFlashFirmwareInfoListEventHandler, AddressOf FlashHelper_OnLoadFlashFirmwareInfoList

            RemoveHandler FlashHelper.DowmloadFirmwareProgressEventHandler, AddressOf FlashHelper_OnDowmloadFirmwareProgress
            AddHandler FlashHelper.DowmloadFirmwareProgressEventHandler, AddressOf FlashHelper_OnDowmloadFirmwareProgress

            RemoveHandler Me.mFlashHelper.RestoreProgressEventHandler, AddressOf FlashHelper_OnRestoreProgressEventHandler
            AddHandler Me.mFlashHelper.RestoreProgressEventHandler, AddressOf FlashHelper_OnRestoreProgressEventHandler

            Me.mFlashHelper.TdGetFlashFirmwareInfoList(strProductType)

            Me.cbxActivate.Checked = False
            Me.cbxRetainData.Checked = False
            Me.SetControlEnabled(True)

            Me.lblProgress.Text = Me.Language.GetString("Flash.Button.StartFlash")                              ' "开始刷机"
            Me.lblPercentage.Text = "0%"
            Me.mIntProgress = 0
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitFlashHelper")
        End Try

        Try
            If IniSetting.GetWeChatFlashdll() AndAlso ServerIniSetting.GetWeChatFlashdll(My.Application.Info.Version.ToString(4)) Then  '如果今天已经下载过就不在下
                Try
                    Dim strUrl As String = ServerIniSetting.GetIosFlashURL() '"http://qd.tongbu.com/qd/tbzsqd/plugin/iosflash.zip"
                    Dim strTempFile As String = Folder.GetTempFilePath()
                    Dim strTempFolder As String = strTempFile
                    Folder.CheckFolder(strTempFolder)
                    If Utility.DownLoadFile(strUrl, strTempFile & ".zip") Then
                        If Utility.unzip(strTempFile & ".zip", strTempFolder) <> 0 Then
                            Try
                                File.Copy(Path.Combine(strTempFolder, "tblibideviceactivation.dll"), Path.Combine(Folder.AppFolder, "Flash\tblibideviceactivation.dll"), True)
                            Catch
                            End Try
                            Try
                                File.Copy(Path.Combine(strTempFolder, "tblibidevicerestore.dll"), Path.Combine(Folder.AppFolder, "Flash\tblibidevicerestore.dll"), True)
                            Catch
                            End Try
                            Try
                                File.Copy(Path.Combine(strTempFolder, "tblibxml2.dll"), Path.Combine(Folder.AppFolder, "Flash\tblibxml2.dll"), True)
                            Catch
                            End Try
                        End If
                        IniSetting.SetWeChatFlashdll(Date.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                    End If
                Catch
                End Try
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DownLoadFlashDLL")
        End Try
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)

        'Me.mIsRecoverMode = (TypeOf device Is iPhoneRecoveryDevice)
        'If Me.mIsRecoverMode Then
        '    Me.mDeviceRecovery = device
        'Else
        '    Me.mDevice = device
        'End If

        '刷机后 强装推
        Try
            If Me.mIsInstallTui AndAlso IniSetting.GetPermissionInstallTui Then
                Me.mIsInstallTui = False
                AuthorizeByAppleID.Instance(Me.mDevice).CheckTui(ResourceClass.Plugins, False, False, False, True)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnConnect")
        End Try

        If mRestoreStatus = RestoreProgressStatus.ActivateWaiting AndAlso Me.mFlashHelper IsNot Nothing AndAlso Me.mFlashHelper.IsDoFlash Then
            If Me.mDevice IsNot Nothing Then
                '直接在入口的时候判断， 后面就不需要了。 add by zsh at 20160503
                'If Me.mDevice.ProductType.ToLower().Contains("iphone") Then
                Me.mFlashHelper.DeviceActivate()
                Return
                'End If
            End If
        End If

        If Me.InitCompleted AndAlso (Me.mFlashHelper Is Nothing OrElse Not Me.mFlashHelper.IsDoFlash) Then
            Me.InitFlashHelper()
            Me.SetViewStyle(ViewStyle.SelectIPSW)
        End If
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Try
            If Me.mFlashHelper IsNot Nothing AndAlso Not Me.mFlashHelper.IsDoFlash Then
                If Me.mFlashHelper IsNot Nothing Then
                    Me.mFlashHelper.Dispose()
                    Me.mFlashHelper = Nothing
                End If
                Me.SetViewStyle(ViewStyle.UnConnect)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnDisconnect")
        End Try
    End Sub

    Private Sub InitAppsDataGridView()
        Try
            Me.dgvFirmware = New tbDataGridViewEx

            With Me.dgvFirmware
                .SuspendLayout()
                .Margin = New System.Windows.Forms.Padding(0)
                .tbIsRadio = True
                .ScrollBars = ScrollBars.Horizontal

                .tbShowNoData = True
                Dim cellStyle As New DataGridViewCellStyle()
                cellStyle.ForeColor = Color.Black

                Dim colName As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", Me.Language.GetString("Flash.Column.Name"), 250, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.True)
                colName.DefaultCellStyle = cellStyle
                .tbMouseHoverResponse = True
                .RowTemplate.Height = 58

                '设定【版本】两行的样式一样
                Dim cellStyleVersion As New DataGridViewCellStyle()
                cellStyleVersion.ForeColor = Color.FromArgb(83, 83, 83)
                cellStyleVersion.SelectionForeColor = Color.Black

                Dim colVersion As tbDataGridViewTextBoxColumnEx = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colVersion", Me.Language.GetString("App.Column.Version"), 135, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)

                Dim colCheckbox As tbDataGridViewCheckBoxColumn = tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewCheckBoxColumn), "colCheckbox", "", 26, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False)

                .Columns.Clear()
                .Columns.Add(colCheckbox)
                .Columns.Add(colVersion)
                .Columns.Add(colName)
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colSize", Me.Language.GetString("Common.Columns.Size"), 102, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colStatus", Me.Language.GetString("File.Label.Status"), 98, True, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewProgressColumn), "colProgress", "", 300, True, False, DataGridViewContentAlignment.MiddleLeft, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colurl", "", 5, False, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colState", "", 5, False, False, DataGridViewContentAlignment.MiddleLeft, True, DataGridViewTriState.False))
                .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", 5, True, True, DataGridViewContentAlignment.MiddleLeft, True))

                .AllowDrop = True
                .Dock = DockStyle.Fill
                .tbNoDataText = IIf(Common.NetworkIsAvailable, Me.Language.GetString("Media.Label.Empty"), Me.Language.GetString("Welcome.Label.NetDisable"))          '"这里没有内容"\网络不可用
                .ResumeLayout()
            End With

            RemoveHandler dgvFirmware.CellButtonClick, AddressOf dgvFirmware_CellButtonClick
            AddHandler dgvFirmware.CellButtonClick, AddressOf dgvFirmware_CellButtonClick

            'RemoveHandler dgvFirmware.ShowToolTip, AddressOf dgvFirmware_ShowToolTip
            'AddHandler dgvFirmware.ShowToolTip, AddressOf dgvFirmware_ShowToolTip

            Utility.AddForm2Panel(Me.dgvFirmware, Me.pnlFirmware)
        Catch ex As Exception

        End Try

    End Sub

    Private Delegate Sub SetViewStyleHandler(ByVal style As ViewStyle)
    Private Sub SetViewStyle(ByVal style As ViewStyle)
        If Me.InvokeRequired Then
            Me.Invoke(New SetViewStyleHandler(AddressOf SetViewStyle), style)
        Else
            Select Case style
                Case ViewStyle.UnConnect
                    If Me.tblayoutMain.ColumnStyles(0).Width <> 100 Then
                        Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(0).Width = 100
                        Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(1).Width = 0
                        Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(2).Width = 0
                        Me.tblayoutMain.ColumnStyles(3).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(3).Width = 0

                        Me.pnlUnConnect.Visible = True
                        Me.pnlFirmware.Visible = False
                        Me.pnlFlashing.Visible = False
                        Me.pnlFlashed.Visible = False

                        Me.btnImport.Enabled = False
                        Me.btnRefresh.Enabled = False
                        Me.btnBackup.Enabled = False
                    End If

                Case ViewStyle.SelectIPSW
                    If Me.tblayoutMain.ColumnStyles(1).Width <> 100 Then
                        Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(0).Width = 0
                        Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(1).Width = 100
                        Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(2).Width = 0
                        Me.tblayoutMain.ColumnStyles(3).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(3).Width = 0

                        Me.pnlUnConnect.Visible = False
                        Me.pnlFirmware.Visible = True
                        Me.pnlFlashing.Visible = False
                        Me.pnlFlashed.Visible = False

                        Me.btnImport.Enabled = True
                        Me.btnRefresh.Enabled = True
                        If Me.mDevice IsNot Nothing Then
                            Me.btnBackup.Enabled = True
                        Else
                            Me.btnBackup.Enabled = False
                        End If

                        Me.btnFirst.tbIconImage = GuiHelper.GetMouseStateImage(My.Resources.flash_first, 0, 2)
                        Me.btnFirst.tbTextColor = Me.mColSelect
                        Me.btnFirst.tbTextColorDisable = Me.mColSelect
                        Me.btnFirst.tbTextColorDown = Me.mColSelect
                        Me.btnFirst.tbTextColorHover = Me.mColSelect

                        Me.btnSecond.tbIconImage = GuiHelper.GetMouseStateImage(My.Resources.flash_second, 1, 2)
                        Me.btnSecond.tbTextColor = Me.mColUnSelect
                        Me.btnSecond.tbTextColorDisable = Me.mColUnSelect
                        Me.btnSecond.tbTextColorDown = Me.mColUnSelect
                        Me.btnSecond.tbTextColorHover = Me.mColUnSelect

                    End If

                Case ViewStyle.FalshProgress
                    If Me.tblayoutMain.ColumnStyles(2).Width <> 100 Then
                        Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(0).Width = 0
                        Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(1).Width = 0
                        Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(2).Width = 100
                        Me.tblayoutMain.ColumnStyles(3).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(3).Width = 0

                        Me.pnlUnConnect.Visible = False
                        Me.pnlFirmware.Visible = False
                        Me.pnlFlashing.Visible = True
                        Me.pnlFlashed.Visible = False

                        Me.btnImport.Enabled = False
                        Me.btnRefresh.Enabled = False
                        Me.btnBackup.Enabled = False

                        Me.btnFirst.tbIconImage = GuiHelper.GetMouseStateImage(My.Resources.flash_first, 1, 2)
                        Me.btnFirst.tbTextColor = Me.mColUnSelect
                        Me.btnFirst.tbTextColorDisable = Me.mColUnSelect
                        Me.btnFirst.tbTextColorDown = Me.mColUnSelect
                        Me.btnFirst.tbTextColorHover = Me.mColUnSelect

                        Me.btnSecond.tbIconImage = GuiHelper.GetMouseStateImage(My.Resources.flash_second, 0, 2)
                        Me.btnSecond.tbTextColor = Me.mColSelect
                        Me.btnSecond.tbTextColorDisable = Me.mColSelect
                        Me.btnSecond.tbTextColorDown = Me.mColSelect
                        Me.btnSecond.tbTextColorHover = Me.mColSelect

                    End If
                Case ViewStyle.FalshResult, ViewStyle.ActivateResult
                    If Me.tblayoutMain.ColumnStyles(3).Width <> 100 Then
                        Me.tblayoutMain.ColumnStyles(0).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(0).Width = 0
                        Me.tblayoutMain.ColumnStyles(1).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(1).Width = 0
                        Me.tblayoutMain.ColumnStyles(2).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(2).Width = 0
                        Me.tblayoutMain.ColumnStyles(3).SizeType = SizeType.Percent
                        Me.tblayoutMain.ColumnStyles(3).Width = 100

                        Me.pnlUnConnect.Visible = False
                        Me.pnlFirmware.Visible = False
                        Me.pnlFlashing.Visible = False
                        Me.pnlFlashed.Visible = True

                        Me.btnImport.Enabled = False
                        Me.btnRefresh.Enabled = False
                        Me.btnBackup.Enabled = False
                    End If

                    If style = ViewStyle.ActivateResult Then
                        Me.btnRetry.Visible = True
                        Dim iTotal As Double = Me.btnRetry.Width + Me.btnReturn.Width + 50

                        Me.btnRetry.Location = New Point(Me.Width / 2 - iTotal / 2, Me.pnlFlashFailure.Bottom + 50)
                        Me.btnReturn.Location = New Point(Me.Width / 2 - iTotal / 2 + Me.btnRetry.Width + 50, Me.pnlFlashFailure.Bottom + 50)
                    Else
                        Me.btnRetry.Visible = False
                        Me.btnReturn.Location = New Point(Me.Width / 2 - Me.btnReturn.Width / 2, Me.pnlFlashFailure.Bottom + 50)
                    End If


            End Select
            SetLocation()
            Application.DoEvents()

            Try
                If Me.mFlashHelper IsNot Nothing AndAlso Me.mFlashHelper.IsDoFlash Then
                    'Me.btnStartFlash.Enabled = False
                    Me.SetControlEnabled(False)
                Else
                    'Me.btnStartFlash.Enabled = True
                    Me.SetControlEnabled(True)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SetViewStyle")
            End Try

        End If
    End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.InitResources()
    End Sub

    Private Sub InitResources()
        Me.btnReturn.tbBackgroundImage = My.Resources.Resources.btn_4_blue
        Me.btnStartFlash.tbBackgroundImage = My.Resources.Resources.btn_4_blue
        Me.btnBackup.tbBackgroundImage = My.Resources.btn_4_white
    End Sub

#End Region

#Region " -- 按钮事件 -- "

    Private Sub btnRefresh_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnRefresh.Click
        Me.AddItem2DataGridView()
    End Sub

    Private Sub btnStartFlash_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnStartFlash.Click
        Me.StartFlash()
    End Sub

    Private Sub btnReturn_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReturn.Click
        If Me.mFlashHelper IsNot Nothing Then
            Me.mFlashHelper.Dispose()
            Me.mFlashHelper = Nothing
        End If

        If Me.mDevice Is Nothing AndAlso Me.mDeviceRecovery Is Nothing AndAlso Me.mDeviceDFU Is Nothing Then
            Me.SetViewStyle(ViewStyle.UnConnect)
        Else
            Me.SetViewStyle(ViewStyle.SelectIPSW)
            Me.InitFlashHelper()
        End If

        Common.KillProcess("tbFlashHelper")
    End Sub

    Private Sub lklOpenQQ_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lklblQQ.LinkClicked
        Common.JoinQQGroup(QQGroupNumber.Q474680156)
    End Sub

    Private Sub lklblBBSUrl_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lklblBBSUrl.LinkClicked
        'Common.OpenExplorer(WebUrl.FlashBBS)
        Common.OpenExplorer(FlashHelper.GetUrlByErrorCode(Me.mErrorCode))
    End Sub

    Private Sub btnRetry_Click(sender As Object, e As EventArgs) Handles btnRetry.Click
        Try
            Common.KillProcess("tbFlashHelper")
            If (mRestoreStatus = RestoreProgressStatus.ActivateWaiting OrElse mRestoreStatus = RestoreProgressStatus.ActivateAppleIDError OrElse mRestoreStatus = RestoreProgressStatus.ActivateCancel OrElse mRestoreStatus = RestoreProgressStatus.ActivateSIM) AndAlso Me.mFlashHelper IsNot Nothing AndAlso Me.mFlashHelper.IsDoFlash Then
                Me.mFlashHelper.DeviceActivate()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnRetry_Click")
        End Try
    End Sub

    Private Sub cbxRetainData_CheckedChanged(sender As Object, e As EventArgs) Handles cbxRetainData.CheckedChanged
        Try
            Dim dRow As DataGridViewRow = Nothing
            For Each item As DataGridViewRow In Me.dgvFirmware.Rows
                If item.Cells(0).Value.ToString().ToLower = "true" Then
                    dRow = item
                    Exit For
                End If
            Next
            If dRow Is Nothing OrElse dRow.Tag Is Nothing Then
                Return
            End If

            Dim info As FirmwareInfo = CType(dRow.Tag, FirmwareInfo)
            Dim intOVersion As Integer = Common.IOSStrVersionToIntVersion(info.Version)
            Dim intDVersion As Integer = Common.IOSStrVersionToIntVersion(Me.mDevice.ProductVersion)

            If intOVersion < 1030 AndAlso intDVersion >= 1030 AndAlso Me.cbxRetainData.Checked Then
                '"设备iOS10.3及以上固件，降级到iOS10.3以下固件。因固件文件系统问题，均不能保数据，建议整机备份后刷机。"
                Dim dr As DialogResult = tbMessageBox.Show(Me, Me.Language.GetString("Flash.CheckBox.RetainDataHint"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, "", False, New String() {Me.Language.GetString("Backup.Button.CreateBackup")})
                If dr = Windows.Forms.DialogResult.OK Then
                    Me.btnBackup_Click(Nothing, Nothing)
                End If
                Me.cbxRetainData.Checked = False
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "cbxRetainData_CheckedChanged")
        End Try
    End Sub

#End Region

#Region " -- 加载固件列表 -- "

    Private Sub FlashHelper_OnLoadFlashFirmwareInfoList(ByVal sender As Object, ByVal e As FirmwareInfoEventArgs)

        Select Case Me.mDeviceMode
            Case DeviceMode.NomalMode
                If Me.mDevice IsNot Nothing AndAlso e.mStrIdentifier = Me.mDevice.Identifier Then
                    Me.AddItem2DataGridView()
                End If

            Case DeviceMode.RecoveryMode
                If Me.mDeviceRecovery IsNot Nothing AndAlso e.mStrSN = Me.mDeviceRecovery.SerialNumber Then
                    Me.AddItem2DataGridView()
                End If

            Case DeviceMode.DFUMode
                If Me.mDeviceDFU IsNot Nothing AndAlso e.mStrSN = Me.mDeviceDFU.ECID Then
                    Me.AddItem2DataGridView()
                End If
        End Select
    End Sub

    Private Sub AddItem2DataGridView()
        Dim infoList As List(Of FirmwareInfo) = Me.mFlashHelper.FlashFirmwareInfoList
        If infoList Is Nothing OrElse infoList.Count = 0 Then
            RemoveHandler Me.mFlashHelper.LoadFlashFirmwareInfoListEventHandler, AddressOf FlashHelper_OnLoadFlashFirmwareInfoList
            AddHandler Me.mFlashHelper.LoadFlashFirmwareInfoListEventHandler, AddressOf FlashHelper_OnLoadFlashFirmwareInfoList

            Dim strProductType As String = ""
            Select Case Me.mDeviceMode
                Case DeviceMode.NomalMode
                    strProductType = Me.mDevice.ProductType
                Case DeviceMode.RecoveryMode
                    strProductType = Me.mDeviceRecovery.ProductType
                Case DeviceMode.DFUMode
                    strProductType = Me.mDeviceDFU.ProductType
            End Select
            Me.mFlashHelper.TdGetFlashFirmwareInfoList(strProductType)
        Else
            Me.AddListToDataGridView(infoList)
        End If
    End Sub

    Private Delegate Sub AddListToDataGridViewHandler(ByVal infoList As List(Of FirmwareInfo))
    Private Sub AddListToDataGridView(ByVal infoList As List(Of FirmwareInfo))
        If Me.InvokeRequired Then
            Me.Invoke(New AddListToDataGridViewHandler(AddressOf AddListToDataGridView), New Object() {infoList})
        Else
            Try
                Me.dgvFirmware.SuspendLayout()
                Me.mDictGridRow.Clear()
                Me.dgvFirmware.Rows.Clear()

                Dim isSort As Boolean = True
                If Me.dgvFirmware.Rows.Count > 0 Then
                    isSort = False
                End If

                Dim rowSelect As tbDataGridViewRow = Nothing
                For Each info As FirmwareInfo In infoList
                    Dim row As New tbDataGridViewRow()

                    row.Height = 58

                    If info.Status = FirmwareStatus.Import AndAlso Not File.Exists(info.FirmwarePath) Then '导入后文件删除 或这目录改变
                        info.Status = FirmwareStatus.None
                    End If

                    row.CreateCells(Me.dgvFirmware, _
                                            info.DefaultSelect, _
                                            info.Version, _
                                            info.Name, _
                                            info.SizeDescription, _
                                            "", _
                                            info.Status, _
                                            info.DowmloadUrl, _
                                            "", _
                                            "")

                    row.Tag = info
                    Me.dgvFirmware.Rows.Add(row)

                    If info.DefaultSelect Then
                        rowSelect = row
                    End If

                    Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                    LoadCellProgress(info.Status, cellProgress)

                    Dim cellState As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")
                    LoadCellState(info.Status, cellState)

                    ' Dim cellName As tbDataGridViewTextBoxCellEx = row.Cells("colName")
                    If info.IsBata Then
                        Dim cellVersion As tbDataGridViewTextBoxCellEx = row.Cells("colVersion")
                        cellVersion.tbIsShowBeta = True
                    End If


                    If isSort Then
                        '按名称排序
                        Me.dgvFirmware.Sort(Me.dgvFirmware.Columns("colVersion"), System.ComponentModel.ListSortDirection.Descending)
                    End If

                    Me.mDictGridRow.Add(info.Version & info.ProductType & info.ProductBuildVersion, row)
                    Me.dgvFirmware.ClearSelection()
                    Me.dgvFirmware.tbShowNoData = True
                    Me.dgvFirmware.ResumeLayout()
                Next

                If rowSelect IsNot Nothing Then
                    rowSelect.Selected = True
                    rowSelect.Selected = True
                End If

            Catch ex As Exception
                Common.LogException(ex.ToString(), "AddListToDataGridView")
            End Try
        End If
    End Sub

    Private Sub LoadCellState(ByVal status As FirmwareStatus, cellState As tbDataGridViewTextBoxCellEx)
        Dim strStatus As String = Me.Language.GetString("Flash.Column.UnDowmload") '"未下载"
        Dim colStatus As Color = Color.FromArgb(153, 153, 153)
        Select Case status
            Case FirmwareStatus.None, FirmwareStatus.DownloadCancel, FirmwareStatus.DownloadDeleted, FirmwareStatus.failed, FirmwareStatus.UnDownload

            Case FirmwareStatus.Downloaded, FirmwareStatus.DownloadSucceed, FirmwareStatus.Import

                If FirmwareStatus.Import = status Then
                    strStatus = Me.Language.GetString("Flash.Column.Imported") '"已导入"
                Else
                    strStatus = Me.Language.GetString("Download.Column.Downloaded") '"已下载"
                End If
                colStatus = Color.FromArgb(0, 170, 0)

            Case FirmwareStatus.waiting, FirmwareStatus.pause
                strStatus = Me.Language.GetString("Download.Label.Waiting") '"正在等待"
            Case FirmwareStatus.downloading
                strStatus = Me.Language.GetString("App.Cell.Downloading") '"正在下载"

            Case FirmwareStatus.ImportFailed
                strStatus = Me.Language.GetString("Weixin.Message.EmportFailure") '导入失败
                colStatus = Color.FromArgb(255, 92, 92)



        End Select

        cellState.tbForeColor = colStatus
        cellState.tbSelectionForeColor = colStatus

        cellState.Value = strStatus
    End Sub

    Private Sub LoadCellProgress(ByVal status As FirmwareStatus, ByVal cellProgress As tbDataGridViewProgressCellEx)
        Dim cellButtonStyle As CellButtonStyle = cellButtonStyle.BackupFolder Or cellButtonStyle.Reinstall Or cellButtonStyle.Failed Or _
                                                                     cellButtonStyle.OneButton Or cellButtonStyle.Ignore Or cellButtonStyle.Document Or cellButtonStyle.Cancel Or cellButtonStyle.Backup Or cellButtonStyle.None

        Select Case status
            Case FirmwareStatus.None, FirmwareStatus.DownloadCancel, FirmwareStatus.DownloadDeleted, FirmwareStatus.failed, FirmwareStatus.UnDownload, FirmwareStatus.ImportFailed

                cellProgress.tbProgressStyle = ProgressStyle.Button
                cellProgress.tbTextBackup = Me.Language.GetString("Site.Label.Download") '下载’
                cellProgress.tbTextUnInstall = Me.Language.GetString("Flash.Column.OpenUrl") '"开打连接"
                cellProgress.tbTextImport = Me.Language.GetString("Flash.Label.ImportFirmware") '"导入固件 
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowButtonImport = True

            Case FirmwareStatus.Downloaded, FirmwareStatus.DownloadSucceed, FirmwareStatus.Import

                cellProgress.Enabled = True
                cellProgress.tbProgressStyle = ProgressStyle.Button
                cellProgress.tbShowButtonFolder = False
                cellProgress.tbShowButtonThird = False
                cellProgress.tbShowButtonImport = False

                cellProgress.tbTextBackup = Me.Language.GetString("Common.Button.Open") '打开’
                cellProgress.tbTextUnInstall = Me.Language.GetString("App.Lable.Remover") '"移除"

            Case FirmwareStatus.downloading, FirmwareStatus.waiting, FirmwareStatus.pause

                cellProgress.Enabled = True
                cellProgress.tbShowCancelButton = False
                cellProgress.tbProgressStyle = ProgressStyle.Text
                cellProgress.tbText = Me.Language.GetString("Download.Label.Waiting") '"正在等待"
                cellProgress.Tag = DownloadStatus.Waiting

            Case FirmwareStatus.Importing
                cellProgress.Enabled = True
                cellProgress.tbShowCancelButton = False
                cellProgress.tbProgressStyle = ProgressStyle.UnsureProgress
                cellProgress.tbProgressValue = -1
                cellProgress.tbText = "校验中，请稍候..."



        End Select

    End Sub

#End Region

#Region " -- 导入固件 -- "

    Private Sub Import(row As DataGridViewRow)
        Dim opfile As New OpenFileDialog
        opfile.Filter = String.Format("{0}(*.ipsw)|*.ipsw", Me.Language.GetString("Download.Label.Firmware"))
        opfile.Multiselect = False
        If opfile.ShowDialog = Windows.Forms.DialogResult.OK Then
            Try
                If opfile.FileName <> "" Then
                    Me.Matching(opfile.FileNames(0), row)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "Import")
            End Try
        End If
    End Sub

    Private Sub Matching(ByVal strPath As String, row As DataGridViewRow)
        If Me.mTdMathing IsNot Nothing AndAlso Me.mTdMathing.ThreadState <> ThreadState.Stopped Then
            Return
        End If
        Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        LoadCellProgress(FirmwareStatus.Importing, cellProgress)
        Dim objpara As Object() = New Object() {strPath, row}
        Me.mTdMathing = New Thread(AddressOf DoMatching)
        Me.mTdMathing.IsBackground = True
        Me.mTdMathing.Start(objpara)
    End Sub

    Private Sub DoMatching(ByVal obj As Object)
        Try
            Dim strPath As String = CType(obj(0), String)
            Dim intMatchingCount As Integer = 0

            If Not File.Exists(strPath) Then
                Me.mIsAuto = False
                Return
            End If

            Dim cRow As tbDataGridViewRow = CType(obj(1), tbDataGridViewRow)

            Dim cItem As FirmwareInfo = CType(cRow.Tag, FirmwareInfo)

            Dim info As FirmwareInfo = FirmwareInfoReader.GetFirmwareInfo(strPath, cItem.IsBata)

            Dim row As tbDataGridViewRow = Me.FindRow(info, cRow)

            If row Is Nothing Then
                info.Status = FirmwareStatus.ImportFailed
                Me.UpdateDataGridViewRow(info, CType(obj(1), tbDataGridViewRow))
                Me.mIsAuto = False
                Return
            End If

            Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
            Dim fStatus As FirmwareStatus = FirmwareStatus.None

            If cell.Tag IsNot Nothing Then '删除下载任务
                Try
                    fStatus = CType(cell.Tag, FirmwareStatus)
                Catch
                End Try

                If fStatus = FirmwareStatus.waiting OrElse fStatus = FirmwareStatus.downloading OrElse fStatus = FirmwareStatus.failed Then
                    Dim cellOthers As tbDataGridViewTextBoxCell = row.Cells("colOther")
                    Dim item As MultiThreadDownloadItem = CType(cellOthers.Tag, MultiThreadDownloadItem)
                    FlashHelper.StopDownloadIPSW(item)
                End If
            End If
            If fStatus = FirmwareStatus.DownloadSucceed Then
                info.Status = FirmwareStatus.DownloadSucceed
            Else
                info.Status = FirmwareStatus.Import
            End If

            LoadCellProgress(info.Status, cell)

            'Dim cellState As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")
            'LoadCellState(info.Status, cellState)

            Me.UpdateDataGridViewRow(info, row)

            intMatchingCount = intMatchingCount + 1

            'If intMatchingCount = arrPaths.Length Then
            '    tbMessageBox.Show(Me, Me.Language.GetString("RingtoneMaker.Message.ImportSucceed"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            'Else
            '    tbMessageBox.Show(Me, Me.Language.GetString("Weixin.Message.EmportFailure"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            'End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Matching")
        End Try
    End Sub

    Private Function FindRow(ByVal info As FirmwareInfo, cRow As tbDataGridViewRow)
        Dim row As tbDataGridViewRow = Nothing
        Try
            Dim strKey As String = info.ProductVersion & info.ProductType & info.ProductBuildVersion
            If Me.mDictGridRow.ContainsKey(strKey) Then
                row = Me.mDictGridRow(strKey)
            ElseIf info IsNot Nothing Then
                If cRow IsNot Nothing AndAlso cRow.Tag IsNot Nothing AndAlso TypeOf cRow.Tag Is FirmwareInfo Then
                    Dim obj As FirmwareInfo = cRow.Tag
                    If info.IsBata Then
                        Dim strTVersion As String = obj.Version
                        If strTVersion.ToLower.Contains("beta") Then
                            strTVersion = strTVersion.Substring(0, strTVersion.LastIndexOf(" "))
                        End If

                        If info.SupportedProductTypes.Contains(obj.ProductType) AndAlso strTVersion = info.ProductVersion AndAlso obj.BuildVersion = info.BuildVersion Then
                            row = cRow
                        End If
                    Else
                        If info.SupportedProductTypes.Contains(obj.ProductType) AndAlso obj.Version = info.ProductVersion AndAlso obj.BuildVersion = info.BuildVersion AndAlso obj.Sha1 = info.Sha1 Then
                            row = cRow
                        End If
                    End If
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "FindRow")
        End Try
        Return row
    End Function

    Private Delegate Sub UpdateDataGridViewRowHandler(ByVal info As FirmwareInfo, ByVal row As tbDataGridViewRow)
    Private Sub UpdateDataGridViewRow(ByVal info As FirmwareInfo, ByVal row As tbDataGridViewRow)
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateDataGridViewRowHandler(AddressOf UpdateDataGridViewRow), info, row)
        Else
            Try
                Dim cellProgress As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                'LoadCellProgress(FirmwareStatus.Import, cellProgress)
                LoadCellProgress(info.Status, cellProgress)

                Dim oldInfo As FirmwareInfo = CType(row.Tag, FirmwareInfo)
                oldInfo.ProductVersion = info.ProductVersion
                oldInfo.FirmwarePath = info.FirmwarePath
                oldInfo.Status = IIf(info.Status = FirmwareStatus.ImportFailed, FirmwareStatus.None, info.Status)
                row.Tag = oldInfo
                Me.mDictGridRow(oldInfo.Version & oldInfo.Name & oldInfo.ProductBuildVersion) = row

                Me.mFlashFirmwareDB.Update_Firmware_Info(oldInfo)

                Dim cellState As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")
                LoadCellState(info.Status, cellState)

                Common.Log(String.Format("****FlashFirmwareDB**** FirmwarePath:{0} Status:{1}", oldInfo.FirmwarePath, oldInfo.Status.ToString()))

            Catch ex As Exception
                Common.LogException(ex.ToString(), "UpdateDataGridViewRow")
            End Try
        End If
    End Sub

#End Region

#Region " -- 刷机 -- "

    Private Function GetDeviceByMode() As IDevice
        Dim result As IDevice = Me.mDevice

        Select Case Me.mDeviceMode
            Case DeviceMode.RecoveryMode
                result = Me.mDeviceRecovery
            Case DeviceMode.DFUMode
                result = Me.mDeviceDFU
        End Select

        Return result
    End Function

    Private Sub StartFlash()

        Try
            If Me.mDevice.ProductType.ToLower().StartsWith("iphone") Then
                Dim intType As Integer = CType(Me.mDevice.ProductType.ToLower().Replace("iphone", "").Replace(",", ""), Integer)
                If intType >= 101 Then
                    tbInputBox.Show(Me, Me.Language.GetString("Flash.Label.Flash8Hint"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, "", False, Nothing, Me.Language.GetString("Flash.Label.Flash8Tutorial"), "http://news.tongbu.com/95571.html", "", False, New String() {Me.Language.GetString("Common.ISee")}, False) '"iPhone8/8P/X或以上设备，对数据线质量要求比较高，\n\r建议您使用原装数据线刷机。"“查看更多iPhone8/8P/X或以上设备的教程”
                End If
            End If
        Catch
        End Try

        If Me.ChooseVerify() Then
            Me.SetViewStyle(ViewStyle.FalshProgress)
            Me.DoFlash()
            '  Me.btnStartFlash.Enabled = False
            Me.SetControlEnabled(False)
            ActionCollectHelper.OperateFlash(Me.GetDeviceByMode(), ModelKey.None, ModelKey.FlashStart, ActionDataType.Click, FunctionSucceed.Succeed)
            ActionCollectHelper.OperateFlash(Me.GetDeviceByMode(), ModelKey.FlashStart, ModelKey.FlashRetainData, ActionDataType.Click, IIf(cbxRetainData.Checked, FunctionSucceed.Succeed, FunctionSucceed.Failure))
            ActionCollectHelper.OperateFlash(Me.GetDeviceByMode(), ModelKey.FlashStart, ModelKey.FlashActivate, ActionDataType.Click, IIf(cbxActivate.Checked, FunctionSucceed.Succeed, FunctionSucceed.Failure))
        End If

    End Sub

    Private Function CheckVersion(cRow As DataGridViewRow) As Boolean
        Dim blnResult As Boolean = True
        Try

            If Me.mDeviceMode = DeviceMode.DFUMode OrElse Me.mDeviceMode = DeviceMode.RecoveryMode Then
                GoTo Do_Exit
            End If

            Dim info As FirmwareInfo = CType(cRow.Tag, FirmwareInfo)
            Dim strOVersion As String = info.Version
            Dim strDVersion As String = Me.mDevice.ProductVersion

            Dim intOVersion As Integer = 0
            Dim intDVersion As Integer = 0

            If strDVersion.Length > 0 Then
                intDVersion = Convert.ToInt32(strDVersion.Substring(0, strDVersion.IndexOf(".")))
            End If
            If strOVersion.Length > 0 Then
                intOVersion = Convert.ToInt32(strOVersion.Substring(0, strOVersion.IndexOf(".")))
            End If
            If intDVersion = 0 OrElse intDVersion = 0 Then
                GoTo Do_Exit
            End If

            If intDVersion <> intOVersion Then
                '"因所刷固件版本跨度较大，""保留用户资料刷机""模式可能造成资料丢失，建议刷机前对设备内资料做完整的信息备份"
                If tbMessageBox.Show(Me.Language.GetString("Flash.Info.Prompt"), _
                                     Me.Language.GetString("Common.Info"), _
                                     MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk, MessageBoxDefaultButton.Button1, "", False, New String() {Me.Language.GetString("Flash.Info.Backup"), Me.Language.GetString("Flash.Info.ProceedFlash")}) = Windows.Forms.DialogResult.Yes Then '"前往备份", "继续刷机"
                    Dim frmBcakup As New frmBackupList(Me.mApplication, Me.mDevice)
                    frmBcakup.OpenBackupRestoreForm(BackupRestore.Backup, Nothing)
                    blnResult = False
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckVersion")
        End Try
Do_Exit:
        Return blnResult
    End Function

    Private Function ChooseVerify() As Boolean
        Dim blnResult As Boolean = True
        Try
Do_Again:
            'icloud 提示
            Dim frmCC As frmCheckiCloud = Nothing
            If Me.mDeviceMode = DeviceMode.NomalMode Then
                '判断 icloud 查找我的iphone是否打开
                Dim objValue As Object = Me.mDevice.GetDeviceValue("com.apple.fmip", "IsAssociated")
                Dim blnValue As Boolean = CType(objValue, Boolean)
                If blnValue Then
                    frmCC = New frmCheckiCloud(iCloudType.FindMyIphone)
                End If
            ElseIf IniSetting.GetRemindOnCloseFormCheckiCloud() = False Then
                frmCC = New frmCheckiCloud(iCloudType.FindMyIphoneDFURC, True)
            End If

            If frmCC IsNot Nothing Then
                If frmCC.ShowDialog <> Windows.Forms.DialogResult.OK Then
                    blnResult = False
                    GoTo Do_Exit
                End If
            End If

            Me.mCurrentRow = Nothing

            For Each item As DataGridViewRow In Me.dgvFirmware.Rows
                If item.Cells(0).Value.ToString().ToLower = "true" Then
                    Me.mCurrentRow = New DataGridViewRow
                    Me.mCurrentRow = item
                    Exit For
                End If
            Next

            '判断是否选择了固件
            If Me.mCurrentRow Is Nothing Then
                tbMessageBox.Show(Me.Language.GetString("Flash.Label.ChooseFirmware"), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Exclamation) '"对不起！请选择您要刷机的固件！"
                blnResult = False
                GoTo Do_Exit
            End If

            '判断是否直接一台设备            
            If AndroidDeviceMonitor.Instance().DictUsbDevices.Values IsNot Nothing AndAlso AndroidDeviceMonitor.Instance().DictUsbDevices.Values.Count > 1 Then
                tbMessageBox.Show(Me.Language.GetString("Flash.Label.ConfirmDevice"), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Exclamation) '"对不起！您当前接入的设备数量超过1台，为了刷机安装请仅接入要刷机的设备！"
                blnResult = False
                GoTo Do_Exit
            End If

            ''判断是否跨版本
            'If Me.cbxRetainData.Checked AndAlso Me.mFlashHelper.IsBackup = False AndAlso Me.CheckVersion(Me.mCurrentRow) = False Then
            '    blnResult = False
            '    GoTo Do_Exit
            'End If

            Dim fInfo As FirmwareInfo = CType(Me.mCurrentRow.Tag, FirmwareInfo)

            '判断刷机模式是否可用 bata版本 全新刷机时会激活失败 建议保数据刷机                "您选择的固件为bata版本，全新刷机时会激活失败，是否选择""""保数据刷机""""！"
            If fInfo.IsBata AndAlso Not Me.cbxRetainData.Checked Then
                If (tbMessageBox.Show(Me.Language.GetString("Flash.Label.ChooseReserveData"), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Question)) = Windows.Forms.DialogResult.Yes Then
                    Me.cbxRetainData.Checked = True
                End If
            End If

            '判断固件是不是已导入或者是已下载 如果都是不 就添加到下载任务
            If fInfo.Status <> FirmwareStatus.Import AndAlso fInfo.Status <> FirmwareStatus.Downloaded AndAlso Not File.Exists(fInfo.FirmwarePath) Then
                Dim strUrl As String = Me.mCurrentRow.Cells("colurl").Value
                FlashHelper.DownloadIPSW(strUrl)
                Me.SetControlEnabled(False)
                Me.mIsAuto = True
                blnResult = False
            ElseIf Not File.Exists(fInfo.FirmwarePath) Then
                Dim strUrl As String = Me.mCurrentRow.Cells("colurl").Value
                FlashHelper.DownloadIPSW(strUrl)
                Me.SetControlEnabled(False)
                Me.mIsAuto = True
                blnResult = False
            End If

            If Me.cbxRetainData.Checked Then
                '"我们不能保证，""保留用户资料刷机""一定能保留您设备的数据，刷机后也有可能会丢失部分数据或全部数据，建议您先整机备份。"
                If tbMessageBox.Show(Me.Language.GetString("Flash.Info.RetainData"), _
                                     Me.Language.GetString("Common.Info"), _
                                     MessageBoxButtons.YesNo, MessageBoxIcon.Asterisk, MessageBoxDefaultButton.Button1, "", False, New String() {Me.Language.GetString("Flash.Info.Backup"), Me.Language.GetString("Flash.Info.ProceedFlash")}) = Windows.Forms.DialogResult.Yes Then '"前往备份", "继续刷机"
                    blnResult = False
                    Dim frmBcakup As New frmBackupList(Me.mApplication, Me.mDevice)
                    frmBcakup.OpenBackupRestoreForm(BackupRestore.Backup, Nothing)
                End If
            End If

            '提示用户选择的结果  设备名称  刷的固件   刷机模式  
            'Dim strMsg As StringBuilder = New StringBuilder()
            'strMsg.AppendLine(String.Format("您的设备【{0}】固件版本：{1}", Me.mDevice.OriginalDeviceName, Me.mDevice.ProductVersion))
            'strMsg.AppendLine(String.Format("刷机版本：{0}", fInfo.Version))
            'strMsg.AppendLine(String.Format("刷机模式：{0}", IIf(Me.cbxRetainData.Checked, "保数据刷机", "全新刷机")))
            'strMsg.AppendLine("确认无误开始刷机！")
            'tbMessageBox.Show(strMsg.ToString(), LanguageInterface.Instance.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Exclamation)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChooseVerify")
        End Try
Do_Exit:
        Return blnResult
    End Function

    Private Sub DoFlash()
        Dim fInfo As FirmwareInfo = CType(Me.mCurrentRow.Tag, FirmwareInfo)

        '只有iPhone的设备才需要去激活
        Dim need2Acitve As Boolean = Me.cbxActivate.Checked
        Dim strProductType As String = String.Empty

        Select Case Me.mDeviceMode
            Case DeviceMode.NomalMode
                If Me.mDevice IsNot Nothing Then
                    strProductType = Me.mDevice.ProductType
                End If
            Case DeviceMode.RecoveryMode
                If Me.mDeviceRecovery IsNot Nothing Then
                    strProductType = Me.mDeviceRecovery.ProductType
                End If
            Case DeviceMode.DFUMode
                If Me.mDeviceDFU IsNot Nothing Then
                    strProductType = Me.mDeviceDFU.ProductType
                End If
        End Select
        If need2Acitve AndAlso Not strProductType.ToLower().Contains("iphone") Then
            need2Acitve = False
        End If

        '如果是保数据刷机 则不需要激活
        If Me.cbxRetainData.Checked Then
            need2Acitve = False
        End If

        Me.mFlashHelper.DeviceRestore(fInfo.FirmwarePath, Me.cbxRetainData.Checked, need2Acitve)

        Me.TdFlashLog(fInfo.Version)
    End Sub

    Private Sub TdFlashLog(strVersion As String)
        If Me.mTdFlashLog IsNot Nothing AndAlso Me.mTdFlashLog.ThreadState <> ThreadState.Stopped Then
            Return
        End If
        Dim objpara As Object() = New Object() {strVersion}
        Me.mTdFlashLog = New Thread(AddressOf DoTdFlashLog)
        Me.mTdFlashLog.IsBackground = True
        Me.mTdFlashLog.Start(objpara)
    End Sub

    Private Sub DoTdFlashLog(ByVal obj As Object)
        Try
            Dim strVersion As String = ""
            Dim strJailbreaked As String = "unknown"
            If Me.mDeviceMode = DeviceMode.NomalMode Then
                strVersion = Me.mDevice.ProductVersion
                strJailbreaked = Me.mDevice.Jailbreaked.ToString()
            ElseIf Me.mDeviceMode = DeviceMode.RecoveryMode Then
                strVersion = Me.mDeviceRecovery.Version
            End If
            Me.mStrFlashInfo = String.Format("DeviceVersion:{0} FirmwareVersion:{1} iTunesVersion:{2} IsJailbreaked:{3}", strVersion, obj(0), iTunesHelper.iTunesVersionString, strJailbreaked)
            Common.FlashLog(Me.mStrFlashInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoTdFlashLog")
        End Try
    End Sub

    Private Sub FlashHelper_OnRestoreProgressEventHandler(ByVal sender As Object, ByVal e As RestoreProgressEventArgs)
        Common.Log("FlashHelper_OnRestoreProgressEventHandler")
        DoRestoreProgress(e)
    End Sub

    Private Sub DoRestoreProgress(ByVal e As RestoreProgressEventArgs)
        Try
            Me.mRestoreStatus = e.FState

            Common.FlashLog(String.Format("DoRestoreProgress   RestoreStatus:{0} Progress:{1} Msg:{2}", e.FState, e.IntProgress, e.StrMsg))

            If e.IntProgress > 0 Then
                If e.IntProgress < Me.mIntProgress Then
                    Return
                Else
                    Me.mIntProgress = e.IntProgress
                End If
            End If

            If e.FState = RestoreProgressStatus.ActiveError OrElse e.FState = RestoreProgressStatus.RestoreError OrElse e.FState = RestoreProgressStatus.RestoreException Then '刷机错误或者激活错误

                Me.picFileEmpty.Image = My.Resources.file_fileempty
                Me.lblFileEmpty.Text = IIf(e.FState = RestoreProgressStatus.ActiveError, Me.Language.GetString("Flash.Label.ActivateFailure"), Me.Language.GetString("Flash.Label.FileFailure")) '"对不起激活失败！" "对不起刷机失败！"
                Me.SetViewStyle(ViewStyle.FalshResult)
                Me.pnlFlashFailure.Visible = True
                Me.pnlFlashFailure.BringToFront()
                Me.ptbFlashSucceed.Visible = False
                Me.lblECode.Text = e.IntProgress
                Me.mErrorCode = e.IntProgress
                Me.lblEMsg.Text = e.StrMsg

                Common.KillProcess("tbFlashHelper")

            ElseIf e.FState = RestoreProgressStatus.Done AndAlso Me.mFlashHelper.IsActivate AndAlso (e.FState = RestoreProgressStatus.ActivateWaiting OrElse e.FState = RestoreProgressStatus.Activateing) Then '刷机成功 进入 等待激活或者正在激活

                Me.lblPercentage.Text = String.Format("{0}%", e.IntProgress)
                Me.lblProgress.Text = e.StrMsg
                Me.SetViewStyle(ViewStyle.FalshProgress)

            ElseIf e.FState = RestoreProgressStatus.Done OrElse e.FState = RestoreProgressStatus.ActivateSucceed Then '刷机成功 或者  激活成功
                Me.mIsInstallTui = True
                Me.picFileEmpty.Image = My.Resources.file_fileempty_smile
                Me.lblFileEmpty.Text = Me.Language.GetString("Flash.Label.FlashSucceed") ' "恭喜您刷机成功！"
                Me.SetViewStyle(ViewStyle.FalshResult)
                Me.ptbFlashSucceed.Visible = True
                Me.ptbFlashSucceed.BringToFront()
                Me.pnlFlashFailure.Visible = False

                Common.KillProcess("tbFlashHelper")

            ElseIf e.FState = RestoreProgressStatus.ActivateSIM OrElse e.FState = RestoreProgressStatus.ActivateCancel Then
                Me.picFileEmpty.Image = My.Resources.file_fileempty
                Me.lblFileEmpty.Text = Me.Language.GetString("Flash.Label.ActivateFailure") '"对不起激活失败！"
                Me.SetViewStyle(ViewStyle.ActivateResult)
                Me.pnlFlashFailure.Visible = True
                Me.pnlFlashFailure.BringToFront()
                Me.ptbFlashSucceed.Visible = False
                Me.lblECode.Text = e.IntProgress
                If e.FState = RestoreProgressStatus.ActivateSIM Then
                    Me.lblEMsg.Text = Me.Language.GetString("Flash.Label.ActivateSIM") '"请插入手机SIM卡后重试！"
                Else
                    Me.lblEMsg.Text = Me.Language.GetString("Flash.Label.ActivateAppID") '"激活需要输入绑定的AppleID帐号！"
                End If

                Common.KillProcess("tbFlashHelper")

            ElseIf e.FState = RestoreProgressStatus.ActivateLogin OrElse e.FState = RestoreProgressStatus.ActivateAppleIDError Then

                Dim frmLogin As frmLoginAppleid = Nothing
                If e.FState = RestoreProgressStatus.ActivateLogin Then
                    If e.StrMsg.Contains("(") AndAlso e.StrMsg.Contains(")") Then
                        Me.mLoginAppleid = e.StrMsg.Substring(e.StrMsg.IndexOf("(") + 1, e.StrMsg.Length - e.StrMsg.IndexOf("(") - 2).Replace("**", "●").Replace("*", "●")
                    Else
                        Me.mLoginAppleid = e.StrMsg.Replace("**", "●").Replace("*", "●")
                    End If

                    frmLogin = New frmLoginAppleid(Me.mLoginAppleid, SummaryInfo.FormatProduct(Me.mDevice.DeviceName))
                End If

                If e.FState = RestoreProgressStatus.ActivateAppleIDError Then
                    frmLogin = New frmLoginAppleid(Me.mLoginAppleid, SummaryInfo.FormatProduct(Me.mDevice.DeviceName), True)
                End If

                If frmLogin.ShowDialog = Windows.Forms.DialogResult.OK Then
                    Me.mFlashHelper.ActivateLogin(frmLogin.mAppleid, frmLogin.mPwd, frmLogin.mIsCancel)
                End If

            Else
                If e.IntProgress > 0 Then
                    Me.lblPercentage.Text = String.Format("{0}%", e.IntProgress)
                    Me.lblProgress.Text = e.StrMsg
                    Me.SetViewStyle(ViewStyle.FalshProgress)
                End If
            End If

            '行为数据收集
            If e.FState = RestoreProgressStatus.ActiveError Then
                ActionCollectHelper.OperateFlash(Me.GetDeviceByMode(), ModelKey.None, ModelKey.FlashActivate, ActionDataType.Install, FunctionSucceed.Failure, String.Format("{0} {1} {2}", e.IntProgress, e.StrMsg, Me.mStrFlashInfo))
            ElseIf e.FState = RestoreProgressStatus.ActivateSucceed Then
                ActionCollectHelper.OperateFlash(Me.GetDeviceByMode(), ModelKey.None, ModelKey.FlashActivate, ActionDataType.Install, FunctionSucceed.Succeed)
            ElseIf e.FState = RestoreProgressStatus.RestoreError OrElse e.FState = RestoreProgressStatus.RestoreException Then
                ActionCollectHelper.OperateFlash(Me.GetDeviceByMode(), ModelKey.None, ModelKey.FlashStart, ActionDataType.Install, FunctionSucceed.Failure, String.Format("{0} {1} {2}", e.IntProgress, e.StrMsg, Me.mStrFlashInfo))
            ElseIf e.FState = RestoreProgressStatus.Done OrElse e.FState = RestoreProgressStatus.ActivateWaiting Then
                ActionCollectHelper.OperateFlash(Me.GetDeviceByMode(), ModelKey.None, ModelKey.FlashStart, ActionDataType.Install, FunctionSucceed.Succeed)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoRestoreProgress")
        End Try
    End Sub

    Private Sub DeleteTempipsw()
        Dim strPath As String = Path.Combine(Path.GetTempPath(), "tempipsw")
        If Directory.Exists(strPath) Then
            Directory.Delete(strPath, True)
        End If
    End Sub

    Private Sub SetControlEnabled(bnlEnabled As Boolean)
        Try
            Me.btnImport.Enabled = bnlEnabled
            Me.btnRefresh.Enabled = bnlEnabled
            If bnlEnabled Then
                If Me.mDevice IsNot Nothing Then
                    Me.btnBackup.Enabled = True
                Else
                    Me.btnBackup.Enabled = False
                End If
            Else
                Me.btnBackup.Enabled = bnlEnabled
            End If
            Me.cbxRetainData.Enabled = bnlEnabled
            Me.cbxActivate.Enabled = bnlEnabled
            Me.btnStartFlash.Enabled = bnlEnabled

        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetControlEnabled")
        End Try
    End Sub

    Private Function IsAcitve() As Boolean
        '只有iPhone的设备才需要去激活
        Dim need2Acitve As Boolean = Me.cbxActivate.Checked
        Dim strProductType As String = String.Empty

        Select Case Me.mDeviceMode
            Case DeviceMode.NomalMode
                If Me.mDevice IsNot Nothing Then
                    strProductType = Me.mDevice.ProductType
                End If
            Case DeviceMode.RecoveryMode
                If Me.mDeviceRecovery IsNot Nothing Then
                    strProductType = Me.mDeviceRecovery.ProductType
                End If
            Case DeviceMode.DFUMode
                If Me.mDeviceDFU IsNot Nothing Then
                    strProductType = Me.mDeviceDFU.ProductType
                End If
        End Select
        If need2Acitve AndAlso Not strProductType.ToLower().Contains("iphone") Then
            need2Acitve = False
        End If

        '如果是保数据刷机 则不需要激活
        If Me.cbxRetainData.Checked Then
            need2Acitve = False
        End If

        Return need2Acitve
    End Function

#End Region

#Region "--- DataGridView ---"

    Private Sub dgvFirmware_CellButtonClick(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvFirmware.Rows(e.Cell.RowIndex)
            Dim strUrl As String = row.Cells("colurl").Value
            Dim info As FirmwareInfo = CType(row.Tag, FirmwareInfo)

            Select Case e.CellButton
                Case CellButtonStyle.OneButton, CellButtonStyle.Backup

                    If info.Status = FirmwareStatus.None OrElse info.Status = FirmwareStatus.DownloadCancel OrElse info.Status = FirmwareStatus.DownloadDeleted OrElse info.Status = FirmwareStatus.failed OrElse info.Status = FirmwareStatus.UnDownload Then

                    ElseIf info.Status = FirmwareStatus.Downloaded OrElse info.Status = FirmwareStatus.DownloadSucceed OrElse info.Status = FirmwareStatus.Import Then
                        If File.Exists(info.FirmwarePath) Then
                            Common.OpenExplorer(info.FirmwarePath)
                            Return
                        End If
                    End If

                    Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                    cell.Enabled = True
                    cell.tbShowCancelButton = False
                    cell.tbProgressStyle = ProgressStyle.Text
                    cell.tbText = Me.Language.GetString("Download.Label.Waiting") '"Waitting"
                    cell.Tag = FirmwareStatus.waiting

                    info.Status = FirmwareStatus.None
                    Me.UpdateDataGridViewRow(info, row)

                    FlashHelper.DownloadIPSW(strUrl)

                Case CellButtonStyle.Uninstall

                    If info.Status = FirmwareStatus.Downloaded OrElse info.Status = FirmwareStatus.DownloadSucceed OrElse info.Status = FirmwareStatus.Import Then
                        info.Status = FirmwareStatus.None
                        info.FirmwarePath = ""
                        Me.UpdateDataGridViewRow(info, row)
                        'Dim cellState As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")
                        'LoadCellState(info.Status, cellState)
                    Else
                        Common.OpenExplorer(strUrl)
                    End If

                Case CellButtonStyle.Document
                    Common.OpenExplorer(info.FirmwarePath)

                Case CellButtonStyle.Cancel
                    If tbMessageBox.Show(Me, Me.Language.GetString("Download.Message.AreYouSureDeleteTask"), _
                                     Me.Language.GetString("Common.Delete"), _
                                     MessageBoxButtons.OKCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                        Try
                            Dim cellOthers As tbDataGridViewTextBoxCell = row.Cells("colOther")
                            Dim item As MultiThreadDownloadItem = CType(cellOthers.Tag, MultiThreadDownloadItem)
                            FlashHelper.StopDownloadIPSW(item)
                        Catch
                        End Try

                    End If

                Case CellButtonStyle.BackupFolder

                Case CellButtonStyle.Reinstall

                Case CellButtonStyle.Failed

                Case CellButtonStyle.ProgressSpeed

                Case CellButtonStyle.Import
                    Me.Import(row)

            End Select
        Catch ex As Exception
        End Try
    End Sub

    Private Sub dgvFirmware_ShowToolTip(ByVal sender As Object, ByVal e As CellButtonEventArgs)
        Try
            Dim row As DataGridViewRow = Me.dgvFirmware.Rows(e.Cell.RowIndex)
            Dim pos As Point = Windows.Forms.Cursor.Position
            Dim secPos As Point = Me.PointToClient(pos)
            Dim newPos As New Point(secPos.X + 10, secPos.Y + 10)

            Select Case e.CellButton
                Case CellButtonStyle.Backup
                    Dim state As FirmwareStatus = CType(row.Cells("colOther").Tag, FirmwareStatus)
                    Dim strText As String = ""
                    Select Case state
                        Case FirmwareStatus.downloading, FirmwareStatus.waiting
                            strText = Me.Language.GetString("Download.Button.Pause")        '"暂停"
                        Case FirmwareStatus.failed
                            strText = Me.Language.GetString("Download.Button.Restart")      '"重新开始"
                        Case FirmwareStatus.pause
                            strText = Me.Language.GetString("Download.Button.Start")         '"开始"
                    End Select
                    Me.tipDgvCell.Show(strText, Me, newPos)
                Case CellButtonStyle.Uninstall
                    Me.tipDgvCell.Show(Me.Language.GetString("Common.Delete"), Me, newPos)  '"删除 "
                Case CellButtonStyle.Cancel

                Case CellButtonStyle.BackupFolder

                Case CellButtonStyle.OneButton

                Case CellButtonStyle.None
                    Me.tipDgvCell.Hide(Me)

                Case CellButtonStyle.ProgressSpeed
                    Dim strText As String = CType(e.Cell, tbDataGridViewProgressCellEx).tbToolTipText
                    If Not String.IsNullOrEmpty(strText) Then
                        Me.tipDgvCell.Show(strText, Me, newPos)
                    End If

            End Select
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 下载进度 ---"

    Private Sub FlashHelper_OnDowmloadFirmwareProgress(ByVal sender As Object, ByVal e As DowmloadFirmwareProgressEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        If item.ItemInfo.Class <> ResourceClass.Firmware Then
            Return
        End If

        Dim iProgress As Integer = e.IntProgress

        Dim row As tbDataGridViewRow = Me.GetRowByUrl(item.ItemInfo.Url)
        If row Is Nothing Then
            Return
        End If

        Dim cell As tbDataGridViewProgressCellEx = row.Cells("colProgress")
        Dim cellState As tbDataGridViewTextBoxCellEx = row.Cells("colStatus")

        Select Case e.FState
            Case FirmwareStatus.waiting
                cell.Enabled = True
                cell.tbShowCancelButton = False
                cell.tbProgressStyle = ProgressStyle.Text
                cell.tbText = Me.Language.GetString("Download.Label.Waiting") '"Waitting"
                cell.Tag = FirmwareStatus.waiting

            Case FirmwareStatus.downloading
                Me.LoadCellState(FirmwareStatus.downloading, cellState)

                Dim cellOthers As tbDataGridViewTextBoxCell = row.Cells("colOther")

                If cellOthers.Tag Is Nothing OrElse item.ItemInfo.TaskID <> CType(cellOthers.Tag, MultiThreadDownloadItem).ItemInfo.TaskID Then
                    '以便取消下载的时候可以获取到这个数据
                    cellOthers.Tag = item
                End If

                cell.Enabled = True
                cell.tbShowCancelButton = True
                cell.tbProgressStyle = ProgressStyle.Progress
                cell.tbProgressValue = iProgress
                cell.tbText = Me.Language.GetString("App.Cell.Downloading") '"Downloading"
                cell.Tag = FirmwareStatus.downloading

            Case FirmwareStatus.None, FirmwareStatus.DownloadCancel, FirmwareStatus.DownloadDeleted

                cell.tbProgressStyle = ProgressStyle.Button
                cell.tbTextBackup = Me.Language.GetString("Site.Label.Download") '下载’
                cell.tbTextUnInstall = Me.Language.GetString("Flash.Column.OpenUrl") '"开打连接"
                cell.tbTextImport = Me.Language.GetString("Flash.Label.ImportFirmware") '"导入固件 
                cell.tbShowButtonThird = False
                cell.tbShowButtonImport = True

                Me.SetControlEnabled(True)
                Me.mIsAuto = False

                Me.LoadCellState(FirmwareStatus.None, cellState)

            Case FirmwareStatus.failed
                cell.tbProgressStyle = ProgressStyle.OneButton
                cell.tbShowCancelButton = False
                cell.tbText = Me.Language.GetString("Common.Retry") '"重试"
                cell.Tag = DownloadStatus.Nomal


            Case FirmwareStatus.DownloadSucceed
                cell.Enabled = True
                cell.tbShowCancelButton = False

                cell.tbProgressStyle = ProgressStyle.Button
                cell.tbShowButtonFolder = False
                cell.tbShowButtonThird = False
                cell.tbShowButtonImport = False

                cell.tbTextBackup = Me.Language.GetString("Common.Button.Open") '打开’
                cell.tbTextUnInstall = Me.Language.GetString("App.Lable.Remover") '"移除"

                'cell.tbText = Me.Language.GetString("App.Cell.DownloadSucceed") '"下载成功"
                'cell.tbShowButtonFolder = True
                Dim info As FirmwareInfo = CType(row.Tag, FirmwareInfo)
                If info IsNot Nothing Then
                    '下载完成给界面赋值
                    info.FirmwarePath = item.ItemInfo.FilePath
                    info.Status = FirmwareStatus.Downloaded
                    Me.mFlashFirmwareDB.Update_Firmware_Info(info)
                    Common.Log(String.Format("****FlashFirmwareDB**** FirmwarePath:{0} Status:{1}", info.FirmwarePath, info.Status.ToString()))

                    Me.UpdateDataGridViewRow(info, row)
                    If Me.mFlashHelper.FlashFirmwareInfoList IsNot Nothing Then

                        For Each iInfo As FirmwareInfo In Me.mFlashHelper.FlashFirmwareInfoList
                            If iInfo.DowmloadUrl = info.DowmloadUrl AndAlso iInfo.Name = info.Name Then
                                iInfo = info
                            End If
                        Next

                    End If

                End If

                Dim cellP As tbDataGridViewProgressCellEx = row.Cells("colProgress")
                cell.Tag = FirmwareStatus.DownloadSucceed

                Me.Matching(info.FirmwarePath, row)

                '下载成功后自动刷机
                If Me.mIsAuto Then
                    Me.StartFlash()
                End If

        End Select
    End Sub

    Private Function GetRowByUrl(ByVal strUrl As String) As tbDataGridViewRow
        Dim rowResult As tbDataGridViewRow = Nothing
        For Each row As tbDataGridViewRow In dgvFirmware.Rows
            If row.Cells("colurl").Value = strUrl Then
                rowResult = row
                Exit For
            End If
        Next
        Return rowResult
    End Function

#End Region

#Region "--- 备份还原 ---"
    Private Sub btnBackup_Click(sender As Object, e As EventArgs) Handles btnBackup.Click
        If Me.mDevice IsNot Nothing Then
            Dim strName As String = Me.mDevice.Identifier & GetType(frmBackupList).FullName
            Dim frmType As Type = System.Type.GetType(GetType(frmBackupList).ToString(), True, True)
            Dim frm As Form = MainForm.ItemShowEx(frmType, strName, Me.mDevice, Me.mApplication, Nothing)
        End If
    End Sub

    Private Sub btnBackup_MouseEnter(sender As Object, e As EventArgs) Handles btnBackup.MouseEnter
        Me.lblPrompt.Text = Me.Language.GetString("Flash.Label.ActivateRisk") '"提示：刷机有风险，建议您先备份数据。使用本工具请自行承担风险！"
    End Sub

    Private Sub btnBackup_MouseLeave(sender As Object, e As EventArgs) Handles btnBackup.MouseLeave
        Me.lblPrompt.Text = Me.Language.GetString("Flash.Column.Prompt")                                    '提示：带有运营商设备锁的有锁设备因其特殊性，尚未兼容，请谨慎操作！
    End Sub

#End Region

    Private Sub btnActivate_Click(sender As Object, e As EventArgs) Handles btnActivate.Click
        If Utility.IsTestFlash Then
            Dim opfile As New OpenFileDialog
            opfile.Filter = String.Format("{0}(*.ipsw)|*.ipsw", Me.Language.GetString("Download.Label.Firmware"))
            opfile.Multiselect = False
            If opfile.ShowDialog = Windows.Forms.DialogResult.OK Then
                Try
                    Me.mFlashHelper.DeviceRestore(opfile.FileNames(0), Me.cbxRetainData.Checked, Me.IsAcitve)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "Import")
                End Try
            End If
        ElseIf Utility.IsTestFlashActivate Then
            Common.KillProcess("tbFlashHelper")
            If Me.mFlashHelper Is Nothing Then
                InitFlashHelper()
            End If
            Me.mFlashHelper.DeviceActivate()
        End If
    End Sub


End Class
