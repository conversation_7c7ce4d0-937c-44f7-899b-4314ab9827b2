﻿Imports System.Threading
Imports System.Runtime.InteropServices
Imports iTong.CoreFoundation.FlashInfo

Public Class FlashHelper
    Implements IDisposable

    'Private Shared mDevice As iPhoneDevice
    'Private Shared mRecoveryDevice As iPhoneRecoveryDevice
    'Private Shared mDeivceDFU As iPhoneDFUDevice
    Private Shared MobileDevice As IDevice

    Private disposedValue As Boolean = False        ' 检测冗余的调用

    Private Shared mDictInstancesIdentifier As New Dictionary(Of String, FlashHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Private Shared mDictInstancesSerial As New Dictionary(Of String, FlashHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例

    Private mFlashFirmwareDB As FlashFirmwareDB = Nothing
    Private mTdLoadFlashFirmwareInfoList As Thread

    Private Shared mDownManage As MultiThreadDownload = Nothing

    Private mFlashFirmwareInfoList As List(Of FirmwareInfo) = Nothing
    Public Event LoadFlashFirmwareInfoListEventHandler As EventHandler(Of FirmwareInfoEventArgs)

    Public Shared Event DowmloadFirmwareProgressEventHandler(ByVal sender As Object, ByVal args As DowmloadFirmwareProgressEventArgs)
    Public Event RestoreProgressEventHandler(ByVal sender As Object, ByVal args As RestoreProgressEventArgs)

    Private mLanguage As LanguageInterface = Nothing

    Private mTdDeviceRestore As Thread
    Private mTdDeviceActivate As Thread

    Private mCoreUpdate As CoreUpdateHelper = CoreUpdateHelper.Instance()
    Private Shared mFlashHelper As String = Path.Combine(Folder.AppFolder, "tbFlashHelper.exe")

    Private Const LOWVERSION As String = "1.0.0.8"  '刷机模块最小的dll，可以防止刷机dll不符合导致的问题
    Private mIsCheckVersion As Boolean = False

    Public ReadOnly Property FlashFirmwareInfoList() As List(Of FirmwareInfo)
        Get
            Return Me.mFlashFirmwareInfoList
        End Get
    End Property

    Private mIsDoFlash As Boolean = False

    Public ReadOnly Property IsDoFlash() As Boolean
        Get
            Return Me.mIsDoFlash
        End Get
    End Property


    Private mIsBackup As Boolean = False

    Public Property IsBackup() As Boolean
        Get
            Return Me.mIsBackup
        End Get
        Set(value As Boolean)
            Me.mIsBackup = value
        End Set
    End Property

    Private mIsActivate As Boolean = False

    Public ReadOnly Property IsActivate() As Boolean
        Get
            Return Me.mIsActivate
        End Get
    End Property

    Const WM_COPYDATA As Integer = &H4A
    Const SC_RESTORE As Int32 = &HF121

#Region "--- 单例 ---"

    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New(ByVal iPhoneDeviceInstance As IDevice)
        Me.mLanguage = LanguageInterface.Instance()

        If Me.mFlashFirmwareDB Is Nothing Then
            Me.mFlashFirmwareDB = FlashFirmwareDB.GetInstance()
        End If

        'm_restoreDelegate = New restore_progress_callback_t(AddressOf OnRestoreProgressCallback)
        'm_restoreErrorDelegate = New restore_idevice_err_callback_t(AddressOf OniDeviceErrorCallback)
        'm_activeErrorDelegate = New active_idevice_err_callback_t(AddressOf OniDeviceActiveErrorCallback)

    End Sub

    Private Shared mHelper As FlashHelper = Nothing

    Public Shared Function GetInstance(ByVal device As IDevice) As FlashHelper
        If mHelper Is Nothing Then
            mHelper = New FlashHelper(device)
        End If

        If device IsNot Nothing Then
            MobileDevice = device
        End If

        Return mHelper
    End Function

    '    Public Shared Function GetInstance(ByVal device As IDevice) As FlashHelper
    '        Dim helper As FlashHelper = Nothing
    '        Dim blnRetry As Boolean = False

    '        SyncLock _lockGetInstance
    'DO_RETRY:
    '            Try
    '                If device Is Nothing Then
    '                    If Not mDictInstancesIdentifier.ContainsKey(NoDevcieIdentifier) Then
    '                        mDictInstancesIdentifier.Add(NoDevcieIdentifier, New FlashHelper(device))
    '                    End If
    '                    helper = mDictInstancesIdentifier.Item(NoDevcieIdentifier)

    '                Else
    '                    '这个类不随着设备连接事件的移除和重连发生变化
    '                    'If mDictInstances.ContainsKey(device.Identifier) Then
    '                    '    Dim fHelper As FlashHelper = mDictInstances(device.Identifier)
    '                    '    If fHelper.mDevice.IsConnected = False AndAlso device.IsConnected Then
    '                    '        mDictInstances.Remove(device.Identifier)
    '                    '        mDictInstances.Add(device.Identifier, New FlashHelper(device))
    '                    '        '释放资源
    '                    '        fHelper.Dispose()
    '                    '    End If
    '                    'Else
    '                    'End If

    '                    If (TypeOf device Is iPhoneRecoveryDevice) Then
    '                        Dim iRDevice As iPhoneRecoveryDevice = device
    '                        If Not mDictInstancesIdentifier.ContainsKey(iRDevice.SerialNumber) Then
    '                            mDictInstancesIdentifier.Add(iRDevice.SerialNumber, New FlashHelper(iRDevice))
    '                            Common.Log("***刷机-GetInstance iPhoneRecoveryDevice NEW -***")
    '                        End If

    '                        Common.Log("***刷机-GetInstance iPhoneRecoveryDevice  " & iRDevice.SerialNumber)

    '                        helper = mDictInstancesIdentifier.Item(iRDevice.SerialNumber)
    '                    Else
    '                        Dim iDevice As iPhoneDevice = device
    '                        If Not mDictInstancesIdentifier.ContainsKey(iDevice.SerialNumber) Then
    '                            mDictInstancesIdentifier.Add(iDevice.SerialNumber, New FlashHelper(iDevice))
    '                            Common.Log("***刷机-GetInstance iPhoneDevice NEW -***")
    '                        End If

    '                        Common.Log("***刷机-GetInstance iPhoneDevice  " & iDevice.SerialNumber)

    '                        helper = mDictInstancesIdentifier.Item(iDevice.SerialNumber)
    '                    End If


    '                End If

    '            Catch ex As Exception
    '                If Not blnRetry Then
    '                    blnRetry = True
    '                    GoTo DO_RETRY
    '                End If
    '            End Try
    '        End SyncLock

    '        Return helper

    '    End Function

#End Region

#Region "--- 释放 ---"

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源

                'RemoveHandler Me.mDownManage.TaskAdd, AddressOf OnTaskAdd
                'RemoveHandler Me.mDownManage.DownloadItemCallBack, AddressOf OnDownloading
                'RemoveHandler Me.mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
                'RemoveHandler Me.mDownManage.TaskDelete, AddressOf OnTaskDelete
                'RemoveHandler Me.mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse
                'RemoveHandler CoreUpdateHelper.Instance().TaskExist, AddressOf OnTaskExist

                Try
                    If Me.mTdLoadFlashFirmwareInfoList IsNot Nothing AndAlso Me.mTdLoadFlashFirmwareInfoList.ThreadState <> ThreadState.Stopped Then
                        Me.mTdLoadFlashFirmwareInfoList.Abort()
                    End If
                Catch
                End Try

                Try
                    If Me.mTdDeviceRestore IsNot Nothing AndAlso Me.mTdDeviceRestore.ThreadState <> ThreadState.Stopped Then
                        Me.mTdDeviceRestore.Abort()
                    End If
                Catch
                End Try

                Try
                    If Me.mTdDeviceActivate IsNot Nothing AndAlso Me.mTdDeviceActivate.ThreadState <> ThreadState.Stopped Then
                        Me.mTdDeviceActivate.Abort()
                    End If
                Catch
                End Try
            End If
        End If
        disposedValue = True
    End Sub

#End Region

#Region "-- 获取固件列表 --"

    Public Sub TdGetFlashFirmwareInfoList(ByVal strProductType As String)
        If mTdLoadFlashFirmwareInfoList IsNot Nothing AndAlso mTdLoadFlashFirmwareInfoList.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        Me.mTdLoadFlashFirmwareInfoList = New Thread(AddressOf GetFlashFirmwareInfoList)
        Me.mTdLoadFlashFirmwareInfoList.IsBackground = True
        Me.mTdLoadFlashFirmwareInfoList.Start(strProductType)

    End Sub

    Private Sub GetFlashFirmwareInfoList(ByVal objProductType As Object)
        Try
            Me.mIsDoFlash = False

            Try
                Dim jObject As New JsonObject
                jObject.Add("DeviceType", objProductType.ToString())
                jObject.Add("IncludeBeta", "true")
                jObject.Add("Sv", Common.GetSoftVersion())
                Dim strJson As String = JsonParser.SaveString(jObject)
                Me.mFlashFirmwareInfoList = HtmlHelper.RestoreList("RestoreList", strJson)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "GetFlashFirmwareInfo")
            End Try

            Dim listDelFirmwareInfo As New List(Of FirmwareInfo)
            Dim listAddFirmwareInfo As New List(Of FirmwareInfo)

            '获取数据库 保存的固件信息   筛选出 文件还存在的信息
            Dim strWhere As String = String.Format(" and ProductTypes like '%{0}%' ", objProductType.ToString())
            If Me.mFlashFirmwareInfoList.Count > 0 Then
                Dim strVersion As String = ""
                For Each nItem As FirmwareInfo In Me.mFlashFirmwareInfoList
                    strVersion = String.Format("{1},'{0}'", nItem.Version, strVersion)
                Next
                strWhere = strWhere & String.Format(" and version in ({0})", strVersion.Substring(1, strVersion.Length - 1))
            End If

            Dim dictAllOldFInfo As Dictionary(Of String, FirmwareInfo) = Me.mFlashFirmwareDB.Get_AllFirmware_Info(strWhere)
            Dim dictAllTempFInfo As New Dictionary(Of String, FirmwareInfo)

            For Each nInfo As FirmwareInfo In Me.mFlashFirmwareInfoList  '循环服务器获取的数据  
                If dictAllOldFInfo.ContainsKey(nInfo.Version & nInfo.ProductType) Then '如果已经存在本地数据库
                    Dim oldInfo As FirmwareInfo = dictAllOldFInfo(nInfo.Version & nInfo.ProductType) '取出信息
                    If File.Exists(oldInfo.FirmwarePath) Then '如果固件文件存在  赋值给新的info对象
                        nInfo.FirmwarePath = oldInfo.FirmwarePath
                        nInfo.Status = oldInfo.Status
                    End If
                    listDelFirmwareInfo.Add(oldInfo) '删除旧的
                End If
                listAddFirmwareInfo.Add(nInfo) '保留新的
            Next


            'For Each oldInfo As FirmwareInfo In dictAllOldFInfo.Values
            '    If File.Exists(oldInfo.FirmwarePath) Then
            '        dictAllTempFInfo.Add(oldInfo.Version & oldInfo.ProductType, oldInfo)
            '    Else
            '        listDelFirmwareInfo.Add(oldInfo)
            '    End If
            'Next

            'For Each info As FirmwareInfo In Me.mFlashFirmwareInfoList
            '    Dim strKey As String = info.Version & info.ProductType
            '    If dictAllTempFInfo.ContainsKey(strKey) Then
            '        Dim oInfo As FirmwareInfo = CType(dictAllTempFInfo(strKey), FirmwareInfo)
            '        info.FirmwarePath = oInfo.FirmwarePath
            '        info.Status = oInfo.Status
            '    End If
            '    listAddFirmwareInfo.Add(info)
            'Next

            '更新数据库’
            Me.mFlashFirmwareDB.Delete_Firmware_Info(listDelFirmwareInfo)
            Me.mFlashFirmwareDB.Insert_Firmware_Info(listAddFirmwareInfo)

            Try
                Dim strSN As String = ""
                Dim strIdentifier As String = ""

                If TypeOf MobileDevice Is iPhoneDevice Then
                    strIdentifier = CType(MobileDevice, iPhoneDevice).Identifier

                ElseIf TypeOf MobileDevice Is iPhoneRecoveryDevice Then
                    strSN = CType(MobileDevice, iPhoneRecoveryDevice).SerialNumber

                ElseIf TypeOf MobileDevice Is iPhoneDFUDevice Then
                    strSN = CType(MobileDevice, iPhoneDFUDevice).ECID
                End If

                RaiseEvent LoadFlashFirmwareInfoListEventHandler(Me, New FirmwareInfoEventArgs(strSN, strIdentifier))
            Catch ex As Exception
                Common.LogException(ex.ToString(), "GetFlashFirmwareInfoList")
            End Try
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetFlashFirmwareInfoList-all")
        End Try
    End Sub

    Public Shared Function GetAllFlashFirmwareInfoList() As Dictionary(Of String, List(Of FirmwareInfoExpand))
        'list 设备大类  (iPhone， iPad，4) 菜单一 tag 放 list class  设备名称  设备类型
        '二级菜单    菜单一的tag  

        Dim FFDB As FlashFirmwareDB = FlashFirmwareDB.GetInstance()
        Dim dictFirmware As New Dictionary(Of String, List(Of FirmwareInfoExpand))
        Dim listDelFirmwareInfo As New List(Of FirmwareInfo)
        Dim listAddFirmwareInfo As New List(Of FirmwareInfo)
        Dim strInfoKey As String = ""
        Try
            Dim dictAllOldFInfo As Dictionary(Of String, FirmwareInfo) = FFDB.Get_AllFirmware_Info("")

            Dim jObject As New JsonObject
            jObject.Add("IncludeBeta", "true")
            jObject.Add("Sv", Common.GetSoftVersion())
            Dim strJson As String = JsonParser.SaveString(jObject)

            Dim listAllInfo As List(Of FirmwareInfo) = HtmlHelper.RestoreList("All", strJson)

            If listAllInfo.Count <= 0 Then
                listAllInfo = HtmlHelper.RestoreList("All", strJson)
            End If

            Dim dictVersion As New Dictionary(Of String, List(Of String))
            '获取所有的设备大类  iphone ipad iod appletv
            For Each info As FirmwareInfo In listAllInfo '循环服务器获取的数据
                strInfoKey = info.Version & info.ProductType
                If dictAllOldFInfo.ContainsKey(strInfoKey) Then '判断本地数据库是不是存在
                    Dim cInfo As FirmwareInfo = dictAllOldFInfo(strInfoKey)
                    If File.Exists(cInfo.FirmwarePath) Then  '如果本地数据库已存在 且文件存在 则保留新的数据
                        info.FirmwarePath = cInfo.FirmwarePath
                        info.Status = cInfo.Status
                    End If
                    listDelFirmwareInfo.Add(cInfo)
                End If

                listAddFirmwareInfo.Add(info)

                If Not dictFirmware.ContainsKey(info.Category) Then
                    dictFirmware.Add(info.Category, Nothing)
                End If

                Dim listTemp As List(Of String)
                If Not dictVersion.ContainsKey(info.ProductType) Then
                    listTemp = New List(Of String)
                    listTemp.Add(info.Version)
                    dictVersion.Add(info.ProductType, listTemp)
                Else
                    listTemp = dictVersion(info.ProductType)
                    If Not listTemp.Contains(info.Version) Then
                        listTemp.Add(info.Version)
                    End If
                    dictVersion(info.ProductType) = listTemp
                End If
            Next

            Dim lstKeys As New List(Of String)
            For Each strKey As String In dictFirmware.Keys
                lstKeys.Add(strKey)
            Next

            For Each strKey As String In lstKeys
                Dim dictFInfo As New Dictionary(Of String, List(Of FirmwareInfoExpand))
                For Each info As FirmwareInfo In listAllInfo
                    Dim listTemp As List(Of FirmwareInfoExpand)
                    If info.Category = strKey AndAlso Not dictFInfo.ContainsKey(info.ProductType) Then
                        Dim fInfo As New FirmwareInfoExpand
                        fInfo.DeviceName = info.DeviceName
                        fInfo.DeviceType = info.ProductType
                        fInfo.Category = info.Category
                        If dictVersion.ContainsKey(info.ProductType) Then
                            fInfo.LstVersion = dictVersion(info.ProductType)
                        End If

                        listTemp = New List(Of FirmwareInfoExpand)
                        listTemp.Add(fInfo)
                        dictFInfo.Add(fInfo.DeviceType, listTemp)
                    End If
                Next
                Dim lstR As New List(Of FirmwareInfoExpand)
                For Each lst As List(Of FirmwareInfoExpand) In dictFInfo.Values
                    For Each info As FirmwareInfoExpand In lst
                        lstR.Add(info)
                    Next
                Next
                If dictFirmware.ContainsKey(strKey) Then
                    dictFirmware(strKey) = lstR
                End If
            Next

            Try
                '更新数据库’
                'FFDB.Delete_Firmware_Info(listDelFirmwareInfo)
                FFDB.Delete_Firmware_Info_All()
                FFDB.Insert_Firmware_Info(listAddFirmwareInfo)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "GetFlashFirmwareInfoList-DB")
            End Try

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetFlashFirmwareInfoList")
        End Try
        Return dictFirmware
    End Function

    Public Shared Function GetFlashFirmwareInfoList(ByVal strWhere As String) As List(Of FirmwareInfo)
        Return FlashFirmwareDB.GetInstance().Get_Firmware_Info(strWhere)
    End Function

#End Region

#Region "--- 固件下载 ---"

    Public Shared Sub DownloadIPSW(ByVal strUrl As String)
        InitDownManage()
        CoreUpdateHelper.Instance().DownloadIPSW(strUrl)
    End Sub

    Public Shared Sub StopDownloadIPSW(ByVal item As MultiThreadDownloadItem)
        Try
            InitDownManage()
            MultiThreadDownload.Instance.Delete(item)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "StopDownloadIPSW")
        End Try
    End Sub

    Public Shared Sub InitDownManage()
        If mDownManage IsNot Nothing Then
            Return
        End If
        mDownManage = MultiThreadDownload.Instance

        RemoveHandler mDownManage.DownloadItemCallBack, AddressOf OnDownloading
        AddHandler mDownManage.DownloadItemCallBack, AddressOf OnDownloading
        RemoveHandler mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
        AddHandler mDownManage.DownloadItemCompleted, AddressOf OnDownloaded
        RemoveHandler mDownManage.TaskDelete, AddressOf OnTaskDelete
        AddHandler mDownManage.TaskDelete, AddressOf OnTaskDelete
        RemoveHandler mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse
        AddHandler mDownManage.TaskAppleStoreAnalyse, AddressOf OnTaskAppleStoreAnalyse
        RemoveHandler mDownManage.TaskAdd, AddressOf OnTaskAdd
        AddHandler mDownManage.TaskAdd, AddressOf OnTaskAdd
        RemoveHandler CoreUpdateHelper.Instance().TaskExist, AddressOf OnTaskExist
        AddHandler CoreUpdateHelper.Instance().TaskExist, AddressOf OnTaskExist
    End Sub

#End Region

#Region "--- 下载相关 ---"

    Private Shared Sub OnTaskExist(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If item.Action = DownloadAction.Cancel Then
            Dim arg As New DowmloadFirmwareProgressEventArgs(0, FirmwareStatus.None, item.ItemInfo.Identifier)
            RaiseEvent DowmloadFirmwareProgressEventHandler(item, arg)
        End If
    End Sub

    Public Shared Sub OnTaskAdd(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If String.IsNullOrEmpty(item.ItemInfo.Url) Then
            Try
                Dim arg As New DowmloadFirmwareProgressEventArgs(0, FirmwareStatus.waiting, item.ItemInfo.Url)
                RaiseEvent DowmloadFirmwareProgressEventHandler(item, arg)

            Catch ex As Exception
                Common.LogException(ex.ToString, "FlashHelper_OnTaskAdd")
            End Try
        End If
    End Sub

    Private Shared Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Try

            Dim item As MultiThreadDownloadItem = sender

            If item.ItemInfo.Class = ResourceClass.Firmware Then
                Dim arg As New DowmloadFirmwareProgressEventArgs(e.Progress, FirmwareStatus.downloading, item.ItemInfo.Url)
                RaiseEvent DowmloadFirmwareProgressEventHandler(item, arg)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "FlashHelper_OnDownloading")
        End Try
    End Sub

    Private Shared Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Try

            Dim item As MultiThreadDownloadItem = sender
            Dim status As FirmwareStatus = FirmwareStatus.None
            If item.ItemInfo.Class = ResourceClass.Firmware Then

                If e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
                    status = FirmwareStatus.DownloadSucceed

                ElseIf item.DownloadInfo.Cancel Then
                    status = FirmwareStatus.DownloadCancel

                Else
                    status = FirmwareStatus.failed

                End If

                Dim arg As New DowmloadFirmwareProgressEventArgs(0, status, item.ItemInfo.Url)
                RaiseEvent DowmloadFirmwareProgressEventHandler(item, arg)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "FlashHelper_OnDownloaded")
        End Try
    End Sub

    Private Shared Sub OnTaskDelete(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.Status <> TaskState.Completed AndAlso item.ItemInfo.Class = ResourceClass.Firmware Then
                Dim arg As New DowmloadFirmwareProgressEventArgs(0, FirmwareStatus.DownloadCancel, item.ItemInfo.Url)
                RaiseEvent DowmloadFirmwareProgressEventHandler(item, arg)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "FlashHelper_OnTaskDelete")
        End Try
    End Sub

    Private Shared Sub OnTaskAppleStoreAnalyse(ByVal sender As Object, ByVal item As MultiThreadDownloadItem)
        If item.ItemInfo.Class = ResourceClass.Firmware Then
            If Not item.ItemInfo.IsDeleted AndAlso Not String.IsNullOrEmpty(item.ItemInfo.ErrorMsg) Then
                Dim arg As New DowmloadFirmwareProgressEventArgs(0, FirmwareStatus.failed, item.ItemInfo.Url)
                RaiseEvent DowmloadFirmwareProgressEventHandler(item, arg)
            End If
        End If
    End Sub

#End Region

#Region "--- 刷机 ---"
    Public Function DeviceRestore(ByVal ipsw As String, ByVal isRetainData As Boolean, ByVal isActivate As Boolean) As Boolean
        Dim blnResule As Boolean = False

        Dim strFirmwarePlistPath As String = Nothing
        '  Dim strFirmwarePlist As String = Nothing
        If File.Exists(Folder.ConfigFirmwarePlistServerFile) Then
            strFirmwarePlistPath = Folder.ConfigFirmwarePlistServerFile
            ' strFirmwarePlist = iTong.Device.CoreFoundation.ReadPlist(strFirmwarePlistPath)
        End If

        If CheckTBFlashHerper() Then
            Dim strSend As String = String.Format("Restore;{0};{1};{2};{3}", ipsw, isRetainData, isActivate, strFirmwarePlistPath)
            Me.DoSendMessage(strSend)
            Me.mIsDoFlash = True
            blnResule = True
        End If
        Return blnResule
    End Function

    Public Sub DeviceActivate()
        If CheckTBFlashHerper() Then
            Dim strSend As String = "Activate"
            Me.DoSendMessage(strSend)
        End If
    End Sub

    Private Sub DoSendMessage(strSend As String)
        Dim iHandle As Integer = GetIHandle()
        Try
            Dim isOpen As Boolean = False
            If iHandle = 0 Then
                If File.Exists(mFlashHelper) Then
                    Dim pro As New System.Diagnostics.Process
                    pro.StartInfo.FileName = mFlashHelper
                    pro.StartInfo.Arguments = strSend
                    pro.Start()
                    isOpen = True
                End If
            End If

            If iHandle = 0 Then
                Dim intCount As Integer = 0
                While intCount < 10
                    iHandle = GetIHandle()
                    If iHandle > 0 Then
                        Exit While
                    End If
                    intCount = intCount + 1
                    Utility.WaitSeconds(1)
                End While
            End If

            If iHandle > 0 Then
                If isOpen Then
                    If Me.SendiCloverProcFormHandle() Then
                        isOpen = False
                    End If
                End If

                Dim sarr As Byte() = System.Text.Encoding.[Default].GetBytes(strSend)
                Dim len As Integer = sarr.Length
                Dim cds As COPYDATASTRUCT
                cds.dwData = CType(100, IntPtr)
                cds.lpData = strSend
                cds.cbData = len + 1
                SendMessage(iHandle, WM_COPYDATA, SC_RESTORE, cds)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoSendMessage")
        End Try
    End Sub

    Private Function SendiCloverProcFormHandle() As Boolean
        Dim isResult As Boolean = False
        Try
            Dim ProcFormHandle As IntPtr = IntPtr.Zero
            For Each item As Form In Application.OpenForms
                If TypeOf item Is ProcForm Then
                    ProcFormHandle = item.Handle
                    Exit For
                End If
            Next
            Me.DoSendMessage("iCloverProcFormHandle" & ProcFormHandle.ToString())
            isResult = True
        Catch ex As Exception

        End Try
        Return isResult
    End Function


    <DllImport("User32.dll", EntryPoint:="FindWindow")> _
    Private Shared Function FindWindow(lpClassName As String, lpWindowName As String) As Integer
    End Function

    Private Function GetIHandle() As Integer
        Dim iHandle As Integer = 0
        Try
            iHandle = FindWindow(Nothing, "FlashProcForm")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetIHandle")
        End Try
        Return iHandle
    End Function

    <DllImport("User32.dll", EntryPoint:="SendMessage")> _
    Private Shared Function SendMessage(hWnd As Integer, Msg As Integer, wParam As Integer, ByRef lParam As COPYDATASTRUCT) As Integer
    End Function

    Public Sub RestoreProgress(arg As RestoreProgressEventArgs)
        Try
            RaiseEvent RestoreProgressEventHandler(Me, arg)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "RestoreProgress")
        End Try
    End Sub

    Private Function CheckTBFlashHerper()
        Dim blnResult As Boolean = True
        Dim arg As RestoreProgressEventArgs = New RestoreProgressEventArgs(3206, 0, RestoreProgressStatus.RestoreException, Me.mLanguage.GetString("Flash.Message.LoseProgram")) '"刷机程序丢失，请重装同步助手！"

        If Not File.Exists(mFlashHelper) Then
            blnResult = False
            GoTo DoExit
        End If

        Try
            '校验刷机tblibidevicerestore.dll是否存在以及他的版本号是否正确
            If Not mIsCheckVersion Then
                mIsCheckVersion = True

                Dim strFiletblibidevicerestore = Path.Combine(Folder.AppFolder, "Flash\tblibidevicerestore.dll")
                If Not File.Exists(strFiletblibidevicerestore) Then
                    blnResult = False
                    GoTo DoExit
                End If

                Dim strVersion As String = FileVersionInfo.GetVersionInfo(strFiletblibidevicerestore).FileVersion
                Dim ver As New Version(strVersion)
                Dim verLow As New Version(LOWVERSION)
                If ver < verLow Then
                    blnResult = False
                    GoTo DoExit
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckTBFlashHerper")
        End Try

DoExit:
        If Not blnResult Then
            RestoreProgress(arg)
        End If
        Return blnResult
    End Function

    Public Function ActivateLogin(ByVal strAppleID As String, ByVal strPwd As String, ByVal isCancel As Boolean) As Boolean
        Dim blnResule As Boolean = False

        If CheckTBFlashHerper() Then
            Dim strSend As String = String.Format("Activate_Login;{0};{1};{2}", isCancel, strAppleID, strPwd)
            Me.DoSendMessage(strSend)
            Me.mIsDoFlash = True
            blnResule = True
        End If
        Return blnResule
    End Function

#End Region

    Private Shared mDicError As Dictionary(Of Integer, String) = Nothing
    Public Shared ReadOnly Property DicError() As Dictionary(Of Integer, String)
        Get
            If mDicError Is Nothing Then
                mDicError = New Dictionary(Of Integer, String)
                mDicError.Add("-65536", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402427")
                mDicError.Add("-1", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402428")
                mDicError.Add("-2", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402429")
                mDicError.Add("-3", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402430")
                mDicError.Add("-4", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402432")
                mDicError.Add("-5", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402433")
                mDicError.Add("-6", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402434")

                mDicError.Add("-8", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402435")
                mDicError.Add("-9", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402436")
                mDicError.Add("-10", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402437")
                mDicError.Add("-11", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402438")
                mDicError.Add("-12", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402439")
                mDicError.Add("-13", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402440")
                mDicError.Add("-14", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402441")
                mDicError.Add("-15", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402442")
                mDicError.Add("-16", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402444")
                mDicError.Add("-17", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402445")

                mDicError.Add("-25", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402446")
                mDicError.Add("-30", "http://bbs.tongbu.com/forum.php?mod=viewthread&tid=415645&page=1#pid1402447")
            End If

            Return mDicError
        End Get
    End Property

    Public Shared Function GetUrlByErrorCode(iCode As Integer) As String
        Dim strUrl As String = WebUrl.FlashBBS
        If DicError.ContainsKey(iCode) Then
            strUrl = DicError(iCode)
        End If
        Return strUrl
    End Function

End Class

Public Class DowmloadFirmwareProgressEventArgs
    Inherits EventArgs

    Dim mIntProgress As Integer = 0
    Dim mFState As FirmwareStatus = FirmwareStatus.None
    Dim mStrUrl As String = ""

    Public Property IntProgress() As Integer
        Get
            Return Me.mIntProgress
        End Get
        Set(ByVal value As Integer)
            Me.mIntProgress = value
        End Set
    End Property

    Public Property FState() As FirmwareStatus
        Get
            Return Me.mFState
        End Get
        Set(ByVal value As FirmwareStatus)
            Me.mFState = value
        End Set
    End Property

    Public Property StrUrl() As String
        Get
            Return Me.mStrUrl
        End Get
        Set(ByVal value As String)
            Me.mStrUrl = value
        End Set
    End Property

    Public Sub New(ByVal iProgress As Integer, ByVal iState As FirmwareStatus, ByVal strUrl As String)
        Me.mIntProgress = iProgress
        Me.mFState = iState
        Me.mStrUrl = strUrl
    End Sub

End Class

Public Class RestoreProgressEventArgs
    Inherits EventArgs

    Dim mIntProgress As Integer = 0
    Dim mIntCodeEx As Integer = 0
    Dim mFState As FirmwareStatus = FirmwareStatus.None
    Dim mStrMsg As String = ""

    Public Property IntProgress() As Integer
        Get
            Return Me.mIntProgress
        End Get
        Set(ByVal value As Integer)
            Me.mIntProgress = value
        End Set
    End Property

    Public Property IntCodeEx() As Integer
        Get
            Return Me.mIntCodeEx
        End Get
        Set(ByVal value As Integer)
            Me.mIntCodeEx = value
        End Set
    End Property

    Public Property FState() As RestoreProgressStatus
        Get
            Return Me.mFState
        End Get
        Set(ByVal value As RestoreProgressStatus)
            Me.mFState = value
        End Set
    End Property

    Public Property StrMsg() As String
        Get
            Return Me.mStrMsg
        End Get
        Set(ByVal value As String)
            Me.mStrMsg = value
        End Set
    End Property

    Public Sub New(ByVal iProgress As Integer, ByVal icode As Integer, ByVal iState As RestoreProgressStatus, ByVal strMsg As String)
        Me.mIntProgress = iProgress
        Me.mFState = iState
        Me.mStrMsg = strMsg
        Me.mIntCodeEx = icode
    End Sub

End Class

Public Class FirmwareInfoEventArgs
    Inherits EventArgs

    Dim strSN As String
    Dim strIdentifier As String

    Public Sub New(sn As String, identifier As String)
        strSN = sn
        strIdentifier = identifier
    End Sub

    Public Property mStrSN() As String
        Get
            Return Me.strSN
        End Get
        Set(ByVal value As String)
            Me.strSN = value
        End Set
    End Property

    Public Property mStrIdentifier() As String
        Get
            Return Me.strIdentifier
        End Get
        Set(ByVal value As String)
            Me.strIdentifier = value
        End Set
    End Property

End Class

''' <summary>
''' 与 tbFlashHelper.exe 通讯助手类
''' </summary>
''' <remarks></remarks>
Public Class FlashCommunication

    Private Shared mLanguage As LanguageInterface = LanguageInterface.Instance()

    Public Shared Function GetJsonTypeStatus(strStatus As String) As JsonType
        Dim status As JsonType = JsonType.Restore
        Try
            status = DirectCast([Enum].Parse(GetType(JsonType), strStatus, True), JsonType)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetJsonTypeStatus")
        End Try
        Return status
    End Function

    Public Shared Function GetStatusFromString(strStatus As String) As RestoreProgressStatus
        Dim status As RestoreProgressStatus = RestoreProgressStatus.None
        Try
            status = DirectCast([Enum].Parse(GetType(RestoreProgressStatus), strStatus, True), RestoreProgressStatus)
        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
        Return status
    End Function

    Public Shared Function GetStrError(status As RestoreProgressStatus) As String
        Dim strError As String = ""

        Select Case status
            Case RestoreProgressStatus.CheckingDevice
                strError = mLanguage.GetString("Flash.Progress.CheckingDevice") '检查设备

            Case RestoreProgressStatus.InNormalMode
                strError = mLanguage.GetString("Flash.Progress.NormalMode") '检查正常模式

            Case RestoreProgressStatus.CheckingRestoreMode
                strError = mLanguage.GetString("Flash.Progress.CheckingRestoreMode") '检查恢复模式

            Case RestoreProgressStatus.ExtractingBuildManifest
                strError = mLanguage.GetString("Flash.Progress.ExtractingBuildManifest") '解析BuildManifest

            Case RestoreProgressStatus.RetrieveSHSH
                strError = mLanguage.GetString("Flash.Progress.RetrieveSHSH") '查找SHSH

            Case RestoreProgressStatus.ExtractedFilesystem
                strError = mLanguage.GetString("Flash.Progress.CheckExtractedFilesystem") '检查文件系统

            Case RestoreProgressStatus.EnteringRecoveryMode
                strError = mLanguage.GetString("Welcome.Button.InRestoreingMode")

            Case RestoreProgressStatus.LoadTheIBEC
                strError = mLanguage.GetString("Flash.Progress.LoadiBEC") '加载iBEC

            Case RestoreProgressStatus.SendIBECFinised
                strError = mLanguage.GetString("Flash.Progress.SendiBEC") '上传iBEC

            Case RestoreProgressStatus.EnteringRestoreMode
                strError = mLanguage.GetString("Flash.Progress.InRestoreMode") '恢复模式

            Case RestoreProgressStatus.FinallyInRestoreMode
                strError = mLanguage.GetString("Flash.Progress.InRestoreMode")

            Case RestoreProgressStatus.ConnectedToCom
                strError = mLanguage.GetString("Flash.Progress.ConnectedApple") '连接服务器

            Case RestoreProgressStatus.FDRListenerThread
                strError = mLanguage.GetString("Flash.Progress.StartingFDR") '启动FDR监听

            Case RestoreProgressStatus.TheRestoreProcess
                strError = mLanguage.GetString("Flash.Progress.StartRestore") '开始恢复

            Case RestoreProgressStatus.SendNORData
                strError = mLanguage.GetString("Flash.Progress.SendNORData") '发送NORData

            Case RestoreProgressStatus.SendRootTicket
                strError = mLanguage.GetString("Flash.Progress.SendRootTicket") '发送RootTicket

            Case RestoreProgressStatus.PartitionNANDDevice
                strError = mLanguage.GetString("Flash.Progress.PartitionNAND") 'NAND分区

            Case RestoreProgressStatus.StorageDevice
                strError = mLanguage.GetString("Flash.Progress.WaitingStorage") '等待分区

            Case RestoreProgressStatus.CheckingFilesystems
                strError = mLanguage.GetString("Flash.Progress.CheckExtractedFilesystem") '检查文件系统

            Case RestoreProgressStatus.WaitingForNAND
                strError = mLanguage.GetString("Flash.Progress.WaitingNAND") '等待NAND

            Case RestoreProgressStatus.SendFilesystem
                strError = mLanguage.GetString("Flash.Progress.SendFilesystem") '写入filesystem

            Case RestoreProgressStatus.ConnectedToASR
                strError = mLanguage.GetString("Flash.Progress.ConnectedASR") '连接ASR

            Case RestoreProgressStatus.SendingFilesystem
                strError = mLanguage.GetString("Flash.Progress.SendFilesystem")

            Case RestoreProgressStatus.RestoringImage
                strError = mLanguage.GetString("Flash.Progress.RestoringImage") '恢复图像

            Case RestoreProgressStatus.SendKernelCache
                strError = mLanguage.GetString("Flash.Progress.SendKernelCache") '发送KernelCache

            Case RestoreProgressStatus.Done
                strError = mLanguage.GetString("Flash.Progress.Done") '完成

            Case RestoreProgressStatus.ActivateWaiting
                strError = mLanguage.GetString("Flash.Progress.ActivateWaiting") '等待激活

            Case RestoreProgressStatus.Activateing
                strError = mLanguage.GetString("Flash.Progress.Activateing") '正在激活

            Case RestoreProgressStatus.ActivateSucceed
                strError = mLanguage.GetString("Flash.Progress.ActivateComplete")

        End Select

        Return strError
    End Function

    Public Shared Function GetFlashRestoreError(intCode As Integer, iPlanEx As Integer, strOldError As String) As String

        Dim strError As String = ""

        If iPlanEx <> 0 Then
            strError = strOldError
            GoTo Do_Exit
        End If

        Select Case intCode

            Case 0              '  FlashRestoreErrorCode.E_SUCCESS   
                strError = mLanguage.GetString("Flash.Progress.SUCCESS")

            Case -1             'FlashRestoreErrorCode.E_CLIENTNEW_FAILTURE
                strError = mLanguage.GetString("Flash.Progress.CLIENTNEWFAILTURE") '创建设备刷机方式失败

            Case -2             'FlashRestoreErrorCode.E_IPSW_ERROR
                strError = mLanguage.GetString("Flash.Progress.IPSWERROR") '固件文件或路径错误’

            Case -3             'FlashRestoreErrorCode.E_DEVMODE_ERROR
                strError = mLanguage.GetString("Flash.Progress.DEVMODEERROR") '没有发现连接设备

            Case -4             'FlashRestoreErrorCode.E_DEVICETYPE_ERROR
                strError = mLanguage.GetString("Flash.Progress.DEVICETYPEERROR") '没有发现设备类型

            Case -5             'FlashRestoreErrorCode.E_BUILDMANIFEST_ERROR
                strError = mLanguage.GetString("Flash.Progress.BUILDMANIFESTERROR") '不能提取ipsw文件中的BuildManifest

            Case -6             'FlashRestoreErrorCode.E_COMPATIBILITY_ERROR
                strError = mLanguage.GetString("Flash.Progress.COMPATIBILITYERROR") '固件不适合当前设备

            Case -7             'FlashRestoreErrorCode.E_BUILD_IDENTITY_ERROR
                strError = mLanguage.GetString("Flash.Progress.BUILDIDENTITYERROR") '创建manifest plist文件失败

            Case -8             'FlashRestoreErrorCode.E_ECID_ERROR
                strError = mLanguage.GetString("Flash.Progress.ECIDERROR") '不能发现设备ECID

            Case -9             'FlashRestoreErrorCode.E_SHSH_ERROR
                strError = mLanguage.GetString("Flash.Progress.SHSHERROR") '不能获取设备的签名证书（SHSH）（可能网络断开，可能苹果关闭验证服务器）’

            Case -10            'FlashRestoreErrorCode.E_TSS_ERROR
                strError = mLanguage.GetString("Flash.Progress.TSSERROR") '没有TSS记录

            Case -11            'FlashRestoreErrorCode.E_FILE_COMPONET_ERROR
                strError = mLanguage.GetString("Flash.Progress.FILECOMPONETERROR") '无法获取文件系统组件’--》固件文件损坏，请重新下载！

            Case -12            'FlashRestoreErrorCode.E_NORMAL_RECOVERY_ERROR
                strError = mLanguage.GetString("Flash.Progress.NORMALRECOVERYERROR") '不能设置设备的一般模式为恢复模式
            Case -13            'FlashRestoreErrorCode.E_DFU_RECOVERY_ERROR
                strError = mLanguage.GetString("Flash.Progress.DFURECOVERYERROR") '不能设置设备的一般模式为DFU模式

            Case -14            'FlashRestoreErrorCode.E_IBEC_ERROR
                strError = mLanguage.GetString("Flash.Progress.IBECERROR") '不能发送iBEC’

            Case -15            'FlashRestoreErrorCode.E_NONCE_ERROR
                strError = mLanguage.GetString("Flash.Progress.NONCEERROR") '从设备中不能取得nonce

            Case -16            'FlashRestoreErrorCode.E_SRNM_ERROR
                strError = mLanguage.GetString("Flash.Progress.SRNMERROR") '不能获取设备串号

            Case -17            'FlashRestoreErrorCode.E_ERS_ERROR
                strError = mLanguage.GetString("Flash.Progress.ERSERROR") '设备无法进入恢复模式

            Case -18            'FlashRestoreErrorCode.E_IMG4TAG_ERROR 
                strError = mLanguage.GetString("Flash.Progress.IMG4TAGERROR") 'Image4Supported错误’

            Case -19            'FlashRestoreErrorCode.E_IMG3TAG_ERROR
                strError = mLanguage.GetString("Flash.Progress.IMG3TAGERROR") 'Image3Supported error’

            Case -20            'FlashRestoreErrorCode.E_INVALID_BB_REQUEST
                strError = mLanguage.GetString("Flash.Progress.INVALIDBBREQUEST") '’无效 bb 请求

            Case -21            'FlashRestoreErrorCode.E_INVALID_BB_DATA
                strError = mLanguage.GetString("Flash.Progress.INVALIDBBDATA") '无效 bb 数据

            Case -22            'FlashRestoreErrorCode.E_INVALID_REQUEST_BUILD
                strError = mLanguage.GetString("Flash.Progress.INVALIDREQUESTBUILD") '无效 ipws 创建请求’

            Case -23            'FlashRestoreErrorCode.E_SERVER_REQUEST_MALFORMED
                strError = mLanguage.GetString("Flash.Progress.SERVERREQUESTMALFORMED") '服务器请求错误’

            Case -24            'FlashRestoreErrorCode.E_INTERNAL_REQUEST_MALFORMED
                strError = mLanguage.GetString("Flash.Progress.INTERNALREQUESTMALFORMED") '内部请求错误’

            Case -25            'FlashRestoreErrorCode.E_TSS_RESPONSE_MALLFORMED
                strError = mLanguage.GetString("Flash.Progress.TSSRESPONSEMALLFORMED") '错误 TSS 格式响应

            Case -26            'FlashRestoreErrorCode.E_PLISTFILE_NOTEXISTS
                strError = mLanguage.GetString("Flash.Progress.PLISTFILENOTEXISTS") 'plist文件不存在

            Case -30            'FlashRestoreErrorCode.E_RESTORE_RECOVERY_ERROR
                strError = mLanguage.GetString("Flash.Progress.RESTORERECOVERYERROR") '不能设置设备的恢复模式为一般模式

            Case -443           'FlashRestoreErrorCode.E_NETWORK_UNREACHABLE
                strError = mLanguage.GetString("Flash.Progress.NETWORKUNREACHABLE") '’网络无法访问

            Case -65535
                strError = mLanguage.GetString("Flash.Progress.DiskLack") '磁盘空间不足  mLanguage.GetString("Flash.Progress.NETWORKUNREACHABLE")
            Case Else
                strError = strOldError
        End Select

Do_Exit:

        Return strError

    End Function
End Class


