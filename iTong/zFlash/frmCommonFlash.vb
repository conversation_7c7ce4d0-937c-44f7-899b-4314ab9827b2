﻿Public Class frmCommonFlash

    Private Const CONST_LIBRARY_APPLE As String = "apple.library"
    Private Const CONST_LOCALAPP_APPLE As String = "apple.localapp"
    Private Const CONST_LOCALUPDATE_APPLE As String = "apple.localupdate"

    Private Const CONST_DEVICE_APPLE As String = "apple.device"
    Private Const CONST_DEVICEAPP_APPLE As String = "apple.deviceapp"
    Private Const CONST_DEVICEUPDATE_APPLE As String = "apple.deviceupdate"
    Private Const CONST_FILESHARING_APPLE As String = "apple.filesharing"

    Private Const CONST_LIBRARY_ANDROID As String = "android.library"
    Private Const CONST_LOCALAPP_ANDROID As String = "android.localapp"
    Private Const CONST_LOCALUPDATE_ANDROID As String = "android.localupdate"

    Private Const CONST_DEVICE_ANDROID As String = "android.device"
    Private Const CONST_DEVICEAPP_ANDROID As String = "android.deviceapp"
    Private Const CONST_DEVICEAPP_ANDROID_SYS As String = "android.devicesysapp"
    Private Const CONST_DEVICEUPDATE_ANDROID As String = "android.deviceupdate"

    Private Const CONST_WEB As String = "webnode"
    Private Const CONST_WEBAPP As String = "App"
    Private Const CONST_WEBAPPSHARE As String = "AppShare"
    Private Const CONST_WEBAPPANDROID As String = "AppAndroid"

    Private mNodeWeb As tbTreeNode = Nothing

    Private mNodeApple As tbTreeNode = Nothing
    Private mNodeAppleLocalApp As tbTreeNode = Nothing
    Private mNodeAppleDevice As tbTreeNode = Nothing

    '-------------- new -----------------------------

    Private Const CONST_LIBRARY_FLASH As String = "flash.library"
    Private Const CONST_FLASH_ONEKEY As String = "flash.onekey"
    Private Const CONST_FLASH_FIRMWARE As String = "flash.firmware"

    Private mNodeFlashLibrary As tbTreeNode = Nothing
    Private mNodeFlashOneKey As tbTreeNode = Nothing
    Private mNodeFlashFirmware As tbTreeNode = Nothing

    Private mfrmSite As frmSite = Nothing

    Private mFrmFlash As frmFlash = Nothing
    Private mFrmFirmware As frmFirmware = Nothing

    Private mFlashHelper As FlashHelper = Nothing

    '-------------- new -----------------------------

    Private mNodeAndroid As tbTreeNode = Nothing
    Private mNodeAndroidLocalApp As tbTreeNode = Nothing
    Private mNodeAndroidDevice As tbTreeNode = Nothing

    Private mAppleLocalApp As frmLocalApp = Nothing
    Private mAppleLocalAppUpdate As frmUpdate = Nothing

    Private mAndroidLocalApp As frmAndroidLocalApp = Nothing
    Private mAndroidLocalAppUpdate As frmAndroidUpdate = Nothing

    Private mDictInfoMsg As New Dictionary(Of FunctionKey, String)
    Private mSelectedNodeKey As FunctionKey = FunctionKey.None

    Private mblnHasInited As Boolean = False
    Private mActionType As ActionFuncType = ActionFuncType.App

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal device As IDevice)
        MyBase.New(application, device, Nothing, True)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.FunctionMappingKey = FunctionKey.App
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        If Me.mDevice IsNot Nothing Then
            RemoveHandler Me.mDevice.DeviceNameChanged, AddressOf OnDeviceNameChanged
            AddHandler Me.mDevice.DeviceNameChanged, AddressOf OnDeviceNameChanged
        End If

        '网页跳转事件
        RemoveHandler Me.mApplication.GoToSiteEvent, AddressOf mApplication_GoToSiteEvent
        AddHandler Me.mApplication.GoToSiteEvent, AddressOf mApplication_GoToSiteEvent
    End Sub

    Protected Overrides Sub InitTreeView()
        Me.tvwFlash.SuspendLayout()

        Dim node As tbTreeNode = Nothing

        '初始化刷机
        Me.mNodeFlashLibrary = New tbTreeNode(CONST_LIBRARY_FLASH, Me.Language.GetString("Flash.Tree.Flash"), Color.Black, True) ' "刷机"
        Me.tvwFlash.Nodes.Add(Me.mNodeFlashLibrary)

        With mNodeFlashLibrary
            '初始化一键刷机节点
            mNodeFlashOneKey = .ChildNodes.Add(New tbTreeNode(CONST_FLASH_ONEKEY, Me.Language.GetString("Flash.Tree.OneKeyFlash"))) ' "一键刷机"
            mNodeFlashOneKey.NodeIcon = My.Resources.tvw_apps_2
            mNodeFlashOneKey.NodeIconState = ImageState.TwoState

            Me.InitFlashForm()

            '初始化固件下载节点
            node = .ChildNodes.Add(New tbTreeNode(CONST_FLASH_FIRMWARE, Me.Language.GetString("Flash.Tree.DownloadFirmware"))) ' "固件下载"
            node.NodeIcon = My.Resources.tvw_update_2
            node.NodeIconState = ImageState.TwoState
        End With

        '初始化越狱
        Me.mNodeWeb = New tbTreeNode(CONST_WEB, Me.Language.GetString("Main.Button.Jailbreak"), Color.Black, True) '"越狱"
        Me.tvwFlash.Nodes.Add(mNodeWeb)

        '初始化越狱相关子节点
        If Me.mUsbDev IsNot Nothing Then
            Me.mfrmSite = frmSite.Instance(Me, Me.mUsbDev)
            Me.mfrmSite.Tag = Me.mUsbDev.DeviceType
        Else
            Me.mfrmSite = frmSite.Instance(Me, Me.mDevice)
        End If
        'Me.mfrmSite.InitWebSiteJailbreakInFlash(Me.mNodeWeb)

        Me.tvwFlash.ResumeLayout()
    End Sub

    Private Sub InitFlashForm()
        Me.mFrmFlash = Common.CheckFormExist(GetType(frmFlash))
        If Me.mFrmFlash Is Nothing OrElse Me.mFrmFlash.IsDisposed Then
            Dim iDevice As Object = Me.mDevice
            If Me.mDeviceRecovery IsNot Nothing Then
                iDevice = Me.mDeviceRecovery
            ElseIf Me.mDeviceDFU IsNot Nothing Then
                iDevice = Me.mDeviceDFU
            End If
            Me.mFrmFlash = New frmFlash(Me.mApplication, iDevice)

        Else
            Try
                If Me.mDevice IsNot Nothing Then
                    If Me.mFrmFlash.mDevice IsNot Nothing Then
                        Me.mFrmFlash.mDevice = Me.mDevice
                        Me.mFrmFlash.mDevice = Nothing
                        Me.mFrmFlash.mDeviceRecovery = Nothing
                        Me.mFrmFlash.OnConnect(Me.mDevice)
                    End If

                ElseIf Me.mDeviceRecovery IsNot Nothing Then
                    If Me.mFrmFlash.mDeviceRecovery Is Nothing Then
                        Me.mFrmFlash.mDeviceRecovery = Me.mDeviceRecovery
                        Me.mFrmFlash.mDevice = Nothing
                        Me.mFrmFlash.mDeviceDFU = Nothing
                        Me.mFrmFlash.OnConnect(Me.mDeviceRecovery)
                    End If

                ElseIf Me.mDeviceDFU IsNot Nothing Then
                    If Me.mFrmFlash.mDeviceDFU Is Nothing Then
                        Me.mFrmFlash.mDeviceDFU = Me.mDeviceDFU
                        Me.mFrmFlash.mDevice = Nothing
                        Me.mFrmFlash.mDeviceRecovery = Nothing
                        Me.mFrmFlash.OnConnect(Me.mDeviceDFU)
                    End If
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString(), "LoadFlashFirmware")
            End Try

        End If

        Utility.AddForm2Panel(Me.mFrmFlash, Me.pnlContainer, False)
    End Sub

    Private Sub InitFirmwareForm()
        Me.mFrmFirmware = Common.CheckFormExist(GetType(frmFirmware))
        If Me.mFrmFirmware Is Nothing OrElse Me.mFrmFirmware.IsDisposed Then
            Me.mFrmFirmware = New frmFirmware(Me.mApplication, Me.mDevice)
        Else
            Me.mFrmFirmware.OnConnect(Me.mDevice)
        End If

        Utility.AddForm2Panel(Me.mFrmFirmware, Me.pnlContainer, False)
    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)

        Try
            Me.mFrmFlash = Common.CheckFormExist(GetType(frmFlash))
            If Me.mFrmFlash IsNot Nothing Then
                Me.mFrmFlash.OnConnect(device)
            End If

            Me.mFrmFirmware = Common.CheckFormExist(GetType(frmFirmware))
            If Me.mFrmFirmware IsNot Nothing Then
                Me.mFrmFirmware.OnConnect(device)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnConnect")
        End Try
    End Sub

    Public Overrides Sub CheckInit()
        MyBase.CheckInit()

        If Me.mDevice Is Nothing Then
            Return
        End If

        Me.mFrmFlash = Common.CheckFormExist(GetType(frmFlash))
        If Me.mFrmFlash IsNot Nothing Then
            Dim iDevice As Object = Me.mDevice
            If Me.mDeviceRecovery IsNot Nothing Then
                iDevice = Me.mDeviceRecovery
            ElseIf Me.mDeviceDFU IsNot Nothing Then
                iDevice = Me.mDeviceDFU
            End If
            Me.mFrmFlash.OnConnect(iDevice)
        End If

        Me.mFrmFirmware = Common.CheckFormExist(GetType(frmFirmware))
        If Me.mFrmFirmware IsNot Nothing Then
            Me.mFrmFirmware.OnConnect(Me.mDevice)
        End If
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        Try
            If Me.mFlashHelper IsNot Nothing AndAlso Not Me.mFlashHelper.IsDoFlash Then
                If Me.mFlashHelper IsNot Nothing Then
                    Me.mFlashHelper.Dispose()
                    Me.mFlashHelper = Nothing
                End If
                'Me.SetViewStyle(ViewStyle.UnConnect)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnDisconnect")
        End Try
    End Sub



    Private Sub SwitchTreeNode(ByVal type As DeviceType)
       
    End Sub

    Private Delegate Sub SetTreeViewSelectHandler(ByVal strName As String)
    Private Sub SetTreeViewSelect(ByVal strName As String)
        If Me.InvokeRequired Then
            Me.Invoke(New SetTreeViewSelectHandler(AddressOf SetTreeViewSelect), strName)
        Else
            Me.tvwFlash.SelectedNode = Me.tvwFlash.Nodes(strName)
        End If
    End Sub

    Private Sub mApplication_GoToSiteEvent(ByVal sender As tbWebBrowser, ByVal type As ActionFuncType, ByVal strNode As String, ByVal strDeviceID As String, ByVal strUrl As String)
        Try
            If Me.mActionType <> type Then
                Return
            End If

            'Dim node As tbTreeNode = Me.tvwApp.Nodes(strNode)
            'If node Is Nothing Then
            '    node = Me.tvwApp.Nodes(strNode)
            'End If
            'node.Visible = True

            'If node IsNot Nothing AndAlso Me.Name.StartsWith(strDeviceID, StringComparison.OrdinalIgnoreCase) Then
            '    Me.tvwApp.SelectedNode = node
            '    If Not String.IsNullOrEmpty(strUrl) Then
            '        If Me.mAndroid Is Nothing AndAlso Me.mDevice Is Nothing Then
            '            frmSite.Instance(Me.mApplication, Nothing).SelectSite(strNode, strUrl)
            '        ElseIf Me.mAndroid IsNot Nothing Then
            '            frmSite.Instance(Me.mApplication, Me.mAndroid).SelectSite(strNode, strUrl)
            '        Else
            '            frmSite.Instance(Me.mApplication, Me.mDevice).SelectSite(strNode, strUrl)
            '        End If
            '    End If
            'End If
        Catch ex As Exception
            Common.LogException(ex.ToString, "mApplication_GoToSiteEvent")
        End Try
    End Sub


#End Region
   
#Region "--- 窗体事件 ---"

    Private Sub tvwDeviceApp_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As tbTreeView_SelectedIndexChanged_EventArgs) Handles tvwFlash.SelectedIndexChanged
        If Not e.Node.Checked Then
            Return
        End If
        'Me.PrintDeviceNodeInfo()
        Try
            If Me.Visible = False AndAlso e.Node.Tag IsNot Nothing AndAlso TypeOf e.Node.Tag Is WebSiteObject Then
                Return
            End If
            If frmSite.SelectNode(e.Node, Me.mUsbDev) Then
                Me.mApplication.SetLabelText(FunctionKey.App, String.Empty)
                Return
            End If

            Common.Log(e.Node.Name)
            For Each subform As Control In Me.pnlContainer.Controls
                Dim ctrl As Control = CType(subform, Control)
                Debug.Print(ctrl.Name)

                ''本地软件的名字在接入设备的时候会被修改掉。
                'If ctrl.Name.Contains("frmLocalApp") Then
                '    ctrl.Name = "apple.localapp"
                'ElseIf ctrl.Name.Contains("frmUpdate") Then
                '    ctrl.Name = "apple.localupdate"
                'End If

                If ctrl IsNot Nothing Then
                    With ctrl

                        If e.Node.Name.StartsWith(.Name) OrElse (Me.mDevice IsNot Nothing AndAlso String.Format("{0}_{1}", Me.mDevice.Identifier, e.Node.Name) = .Name) Then
                            Debug.Print(.Name)

                            Select Case e.Node.Name
                                Case CONST_LOCALAPP_APPLE
                                    Me.mSelectedNodeKey = FunctionKey.LocalApp

                                Case CONST_LOCALUPDATE_APPLE
                                    Me.mSelectedNodeKey = FunctionKey.LocalUpdate
                                    Me.mAppleLocalAppUpdate.ChangeConnectState(Me.mDevice)

                                Case CONST_DEVICEAPP_APPLE
                                    Me.mSelectedNodeKey = FunctionKey.DeviceApp

                                Case CONST_DEVICEUPDATE_APPLE
                                    Me.mSelectedNodeKey = FunctionKey.DeviceUpdate

                                Case CONST_FILESHARING_APPLE
                                    Me.mSelectedNodeKey = FunctionKey.FileSharing

                                Case CONST_LOCALAPP_ANDROID
                                    Me.mSelectedNodeKey = FunctionKey.AndroidLocalApp

                                Case CONST_LOCALUPDATE_ANDROID
                                    Me.mSelectedNodeKey = FunctionKey.AndroidLocalUpdate
                                    Me.mAndroidLocalAppUpdate.ChangeConnectState(Me.mAndroid)

                                Case CONST_DEVICEAPP_ANDROID
                                    Me.mSelectedNodeKey = FunctionKey.AndroidDeviceApp

                                Case CONST_DEVICEAPP_ANDROID_SYS
                                    Me.mSelectedNodeKey = FunctionKey.AndroidDeviceAppSys

                                Case CONST_DEVICEUPDATE_ANDROID
                                    Me.mSelectedNodeKey = FunctionKey.AndroidDeviceUpdate

                                Case CONST_WEBAPP
                                    CType(ctrl, frmSite).SelectSite("App", "")

                                Case CONST_WEBAPPSHARE
                                    CType(ctrl, frmSite).SelectSite("AppShare", "")

#If IS_ITONG_ZJ Then
                                    Case CONST_IOS_ZHUANQU
                                        Me.mSelectedNodeKey = FunctionKey.IOSZHUANQU

                                    Case CONST_IOS_NEWDEVICE
                                        Me.mSelectedNodeKey = FunctionKey.IOSNEWDEVICE
                                        Me.btnNewDeviceTip.Visible = False
#End If
                            End Select

                            Dim strText As String = String.Empty
                            If Me.mDictInfoMsg.ContainsKey(Me.mSelectedNodeKey) Then
                                strText = Me.mDictInfoMsg(Me.mSelectedNodeKey)
                            End If

                            Me.mApplication.SetLabelText(FunctionKey.App, strText)
                            Application.DoEvents()

                            If Not .Visible Then
                                .SendToBack()
                                .Show()
                            End If

                            '如果第一次打开要在 show后再定位到节点，不然会选中默认节点.
                            If e.Node.Name = CONST_WEBAPP Then
                                CType(ctrl, frmSite).SelectSite("App", "")
                            ElseIf e.Node.Name = CONST_WEBAPPSHARE Then
                                CType(ctrl, frmSite).SelectSite("AppShare", "")
                            End If

                            .BringToFront()
                            Exit For
                        End If
                    End With
                End If
            Next
        Catch ex As Exception
            Common.LogException("App Show SubForm Error:" & ex.ToString())
        End Try
    End Sub

    'Private Sub pnlDrop_DragEnter(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles pnlDrop.DragEnter
    '    If Me.mDevice Is Nothing Then
    '        e.Effect = DragDropEffects.None
    '    ElseIf e.Data.GetDataPresent(DataFormats.FileDrop, False) Then
    '        e.Effect = DragDropEffects.All
    '    End If
    'End Sub

    'Private Sub pnlDrop_DragDrop(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DragEventArgs) Handles pnlDrop.DragDrop
    '    Dim files As String() = DirectCast(e.Data.GetData(DataFormats.FileDrop), String())
    '    Dim lstFiles As List(Of String) = Me.CheckFileLegal(files)

    '    If lstFiles.Count > 0 Then
    '        Dim InstallHelper As iPhoneInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)
    '        If InstallHelper IsNot Nothing Then
    '            'Dim node As tbTreeNode = Me.tvwApp.Nodes("DeviceApp")

    '            'If Not node.Checked Then
    '            '    node.Checked = True
    '            'End If
    '            'mDeviceAppForm.InitControlsFromOutside()

    '            InstallHelper.InstallApplication(lstFiles, False)
    '        End If
    '    Else
    '        tbMessageBox.Show(Me, Me.Language.GetString("App.Message.SupportAppType"), _
    '                       Me.Language.GetString("Common.Info"), _
    '                       MessageBoxButtons.OK, _
    '                       MessageBoxIcon.Information)  '"对不起，我们仅支持 ipa 或者 pxl 格式的文件安装。"   "信息"
    '    End If
    'End Sub

    Private Sub OnDeviceNameChanged(ByVal sender As Object, ByVal e As EventArgs)
        Dim strDevice As String = String.Format("{0} - {1}", Me.Language.GetString("App.TreeNode.Device"), Me.mDevice.DeviceName)
        Me.mNodeAppleDevice.Text = strDevice
    End Sub

#End Region

#Region "--- 私有方法 ---"

    '    Private Sub RemoveDeviceForms()
    '        For index As Integer = Me.pnlContainer.Controls.Count - 1 To 0 Step -1
    '            Dim subform As tbBaseGuiForm = Me.pnlContainer.Controls(index)
    '            Try
    '                Select Case subform.Name
    '                    Case CONST_DEVICEAPP_APPLE, CONST_DEVICEUPDATE_APPLE, CONST_FILESHARING_APPLE, _
    '                         CONST_DEVICEAPP_ANDROID, CONST_DEVICEAPP_ANDROID_SYS, CONST_DEVICEUPDATE_ANDROID

    '                        If Not subform.IsDisposed Then
    '                            Me.pnlContainer.Controls.Remove(subform)
    '                            If TypeOf subform Is frmAndroidBase Then
    '                                CType(subform, frmAndroidBase).OnDisconnect(mAndroid)

    '                            ElseIf TypeOf subform Is frmDeviceBase Then
    '                                CType(subform, frmDeviceBase).OnDisconnect(mDevice)

    '                            End If
    '                            subform.Close()
    '                        End If
    '                End Select
    '            Catch ex As Exception
    '                Common.LogException("App Remove SubForm Error:" & ex.ToString())
    '            End Try
    '        Next
    '    End Sub

    '    Private Function GetNodeByKey(ByVal key As FunctionKey) As tbTreeNode
    '        Dim node As tbTreeNode = Nothing

    '        Select Case key
    '            Case FunctionKey.LocalApp
    '                node = Me.tvwApp.Nodes(CONST_LOCALAPP_APPLE)

    '            Case FunctionKey.LocalUpdate
    '                node = Me.tvwApp.Nodes(CONST_LOCALUPDATE_APPLE)

    '            Case FunctionKey.DeviceApp
    '                node = Me.tvwApp.Nodes(CONST_DEVICEAPP_APPLE)

    '            Case FunctionKey.DeviceUpdate
    '                node = Me.tvwApp.Nodes(CONST_DEVICEUPDATE_APPLE)

    '            Case FunctionKey.AndroidLocalApp
    '                node = Me.tvwApp.Nodes(CONST_LOCALAPP_ANDROID)

    '            Case FunctionKey.AndroidLocalUpdate
    '                node = Me.tvwApp.Nodes(CONST_LOCALUPDATE_ANDROID)

    '            Case FunctionKey.AndroidDeviceApp
    '                node = Me.tvwApp.Nodes(CONST_DEVICEAPP_ANDROID)

    '            Case FunctionKey.AndroidDeviceAppSys
    '                node = Me.tvwApp.Nodes(CONST_DEVICEAPP_ANDROID_SYS)

    '            Case FunctionKey.AndroidDeviceUpdate
    '                node = Me.tvwApp.Nodes(CONST_DEVICEUPDATE_ANDROID)

    '#If IS_ITONG_ZJ Then
    '                Case FunctionKey.IOSZHUANQU
    '                    node = Me.tvwApp.Nodes(CONST_IOS_ZHUANQU)
    '                Case FunctionKey.IOSNEWDEVICE
    '                    node = Me.tvwApp.Nodes(CONST_IOS_NEWDEVICE)
    '#End If
    '        End Select

    '        Return node
    '    End Function

    '    Private Sub PrintDeviceNodeInfo()
    '        If Me.mNodeAppleDevice IsNot Nothing Then
    '            Common.Log("Me.mNodeAppleDevice visible: " & Me.mNodeAppleDevice.Visible.ToString())
    '            Common.Log(String.Format("Me.mNodeAppleDevice text: {0}", Me.mNodeAppleDevice.Text))
    '            If Me.mNodeAppleDevice.Parent IsNot Nothing Then
    '                Common.Log(String.Format("Me.mNodeAppleDevice.parent.name: {0} {1}", Me.mNodeAppleDevice.Parent.Name, Me.mNodeAppleDevice.Parent.Text))
    '            Else
    '                Common.Log("Me.mNodeAppleDevice.parent is nothing")
    '            End If
    '        Else
    '            Common.Log("Me.mNodeAppleDevice is nothing")
    '        End If

    '        If mAndroid IsNot Nothing Then
    '            Common.Log(mAndroid.ToString())
    '        Else
    '            Common.Log("mandroid is nothing")
    '        End If
    '    End Sub

    '    Public Sub CreateNewDeviceTip()
    '#If IS_ITONG_ZJ Then
    '            Dim tn As tbTreeNode = Me.tvwApp.Nodes(CONST_IOS_NEWDEVICE)
    '            Dim rect As Rectangle = tn.Bounds
    '            Dim pLocation As Point = New System.Drawing.Point(rect.Right + 2, rect.Top)
    '            Me.btnNewDeviceTip.BackColor = System.Drawing.Color.FromArgb(242, 244, 247)
    '            Me.btnNewDeviceTip.Anchor = System.Windows.Forms.AnchorStyles.None
    '            Me.btnNewDeviceTip.BindingForm = Nothing
    '            Me.btnNewDeviceTip.Font = New System.Drawing.Font("宋体", 9.0!)
    '            Me.btnNewDeviceTip.Location = pLocation
    '            Me.btnNewDeviceTip.Name = "btnNewDeviceTip"
    '            Me.btnNewDeviceTip.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
    '            Me.btnNewDeviceTip.Selectable = True
    '            Me.btnNewDeviceTip.Size = New System.Drawing.Size(318, 33)
    '            Me.btnNewDeviceTip.TabIndex = 38
    '            Me.btnNewDeviceTip.tbAdriftIconWhenHover = False
    '            Me.btnNewDeviceTip.tbAutoSize = True
    '            Me.btnNewDeviceTip.tbAutoSizeEx = True
    '            Me.btnNewDeviceTip.tbBackgroundImage = My.Resources.app_bg_btn_msg
    '            Me.btnNewDeviceTip.tbBackgroundImageState = iTong.Components.ImageState.OneState
    '            Me.btnNewDeviceTip.tbBadgeNumber = 0
    '            Me.btnNewDeviceTip.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
    '            Me.btnNewDeviceTip.tbEndEllipsis = False
    '            Me.btnNewDeviceTip.tbIconHoldPlace = True
    '            Me.btnNewDeviceTip.tbIconImage = Nothing
    '            Me.btnNewDeviceTip.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
    '            Me.btnNewDeviceTip.tbIconImageState = iTong.Components.ImageState.OneState
    '            Me.btnNewDeviceTip.tbIconMore = False
    '            Me.btnNewDeviceTip.tbIconMouseDown = Nothing
    '            Me.btnNewDeviceTip.tbIconMouseHover = Nothing
    '            Me.btnNewDeviceTip.tbIconMouseLeave = Nothing
    '            Me.btnNewDeviceTip.tbIconPlaceText = 2
    '            Me.btnNewDeviceTip.tbIconReadOnly = Nothing
    '            Me.btnNewDeviceTip.tbImageMouseDown = Nothing
    '            Me.btnNewDeviceTip.tbImageMouseHover = Nothing
    '            Me.btnNewDeviceTip.tbImageMouseLeave = Nothing
    '            Me.btnNewDeviceTip.tbReadOnly = False
    '            Me.btnNewDeviceTip.tbReadOnlyText = False
    '            Me.btnNewDeviceTip.tbShadow = False
    '            Me.btnNewDeviceTip.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
    '            Me.btnNewDeviceTip.tbShadowOffset = New System.Drawing.Point(1, 1)
    '            Me.btnNewDeviceTip.tbShowNew = False
    '            Me.btnNewDeviceTip.tbShowToolTipOnButton = False
    '            Me.btnNewDeviceTip.tbSplit = "10,0,10,0"
    '            Me.btnNewDeviceTip.tbText = " 新机在这里下载安装应用积分更高哦！"
    '            Me.btnNewDeviceTip.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
    '            Me.btnNewDeviceTip.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
    '            Me.btnNewDeviceTip.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
    '            Me.btnNewDeviceTip.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
    '            Me.btnNewDeviceTip.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(125, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(24, Byte), Integer))
    '            Me.btnNewDeviceTip.tbTextMouseDownPlace = 0
    '            Me.btnNewDeviceTip.tbToolTip = ""
    '            Me.btnNewDeviceTip.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
    '            Me.btnNewDeviceTip.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
    '            Me.btnNewDeviceTip.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
    '            Me.btnNewDeviceTip.VisibleEx = True
    '            Me.btnNewDeviceTip.Visible = True

    '            Me.ControlsAdd(Me.btnNewDeviceTip)
    '            RemoveHandler Me.btnNewDeviceTip.Click, AddressOf btnNewDeviceTip_Click
    '            AddHandler Me.btnNewDeviceTip.Click, AddressOf btnNewDeviceTip_Click
    '#End If
    '    End Sub

    '    Private Delegate Sub ControlsAddHandler(ByVal btn As tbButton)
    '    Private Sub ControlsAdd(ByVal btn As tbButton)
    '        If Me.InvokeRequired Then
    '            Me.Invoke(New ControlsAddHandler(AddressOf ControlsAdd), btn)
    '        Else
    '            If Not Me.Controls.Contains(btn) Then
    '                Me.Controls.Add(btn)
    '            End If
    '            If Not Me.Visible Then
    '                Me.Visible = True
    '            End If

    '            btn.BringToFront()
    '        End If
    '    End Sub

#End Region

#Region "--- 公有方法 ---"

    'Public Property IsFavouriteNeedReload() As Boolean Implements IApp.IsFavouriteNeedReload
    '    Get
    '        Return Me.mIsFavouriteNeedReload
    '    End Get
    '    Set(ByVal value As Boolean)
    '        Me.mIsFavouriteNeedReload = value
    '    End Set
    'End Property

    'Public Property IsOldAppNeedReload() As Boolean Implements IApp.IsOldAppNeedReload
    '    Get
    '        Return Me.mIsOldAppNeedReload
    '    End Get
    '    Set(ByVal value As Boolean)
    '        Me.mIsOldAppNeedReload = value
    '    End Set
    'End Property

    'Public Sub UpdateNodeNumber(ByVal key As FunctionKey, ByVal number As Integer) Implements IApp.UpdateNodeNumber
    '    Dim node As tbTreeNode = Me.GetNodeByKey(key)

    '    If node IsNot Nothing Then
    '        node.NodeNumber = number
    '    End If

    '    IniSetting.SetAppCount(key, number)
    'End Sub

    'Public Sub UpdateNodeInSync(ByVal key As FunctionKey, ByVal value As Boolean) Implements IApp.UpdateNodeInSync
    '    Dim node As tbTreeNode = Me.GetNodeByKey(key)

    '    If node IsNot Nothing Then
    '        node.InSync = value
    '        Application.DoEvents()
    '    End If
    'End Sub

    'Public Sub ShowInfoMsg(ByVal key As FunctionKey, ByVal strInfo As String) Implements IApp.ShowInfoMsg
    '    Me.mDictInfoMsg(key) = strInfo

    '    If key = Me.mSelectedNodeKey Then
    '        Me.mApplication.SetLabelText(FunctionKey.App, strInfo)
    '    End If
    'End Sub

    'Public Sub ChangeFavouriteStateByInfo(ByVal info As PackageInfo, ByVal value As Boolean) Implements IApp.ChangeFavouriteStateByInfo
    '    Me.mAppleLocalApp.ChangeFavouriteStateByInfo(info, value)
    '    'Me.mAppleLocalOldApp.ChangeFavouriteStateByInfo(info, value)
    'End Sub

    'Public Function IsNodeChecked(ByVal key As FunctionKey) Implements IApp.IsNodeChecked
    '    Dim result As Boolean = False

    '    If key = Me.mSelectedNodeKey Then
    '        result = True
    '    End If

    '    Return result
    'End Function

    'Public Sub SwitchSelectedNode(ByVal key As FunctionKey) Implements IApp.SwitchSelectedNode
    '    Dim node As tbTreeNode = Me.GetNodeByKey(key)

    '    If node IsNot Nothing Then
    '        node.Select()
    '    End If
    'End Sub

    'Public Sub AddLocalAPKs(ByVal lstFiles As List(Of String)) Implements IApp.AddLocalAPKs
    '    If Me.mAndroidLocalApp IsNot Nothing AndAlso mAndroidLocalApp.Visible = True Then
    '        Me.mAndroidLocalApp.AddPackages(lstFiles)
    '    End If
    'End Sub

    ''判断上传的文件的类型是否都是ipa，pxl或者还有其他    
    'Public Function CheckFileLegal(ByVal files As String(), ByVal devType As DeviceType) As List(Of String) Implements IApp.CheckFileLegal
    '    Dim lstFile As New List(Of String)

    '    If devType = DeviceType.iOS Then
    '        For Each file As String In files
    '            If Directory.Exists(file) Then
    '                lstFile.AddRange(My.Computer.FileSystem.GetFiles(file, FileIO.SearchOption.SearchAllSubDirectories, "*.ipa", ".pxl"))

    '            ElseIf (System.IO.File.Exists(file)) Then
    '                If file.EndsWith(".ipa", StringComparison.OrdinalIgnoreCase) OrElse _
    '                   file.EndsWith(".pxl", StringComparison.OrdinalIgnoreCase) Then

    '                    lstFile.Add(file)
    '                End If
    '            End If
    '        Next

    '    Else
    '        For Each file As String In files
    '            If Directory.Exists(file) Then

    '                lstFile.AddRange(My.Computer.FileSystem.GetFiles(file, FileIO.SearchOption.SearchAllSubDirectories, "*.apk"))

    '            ElseIf (System.IO.File.Exists(file)) Then

    '                If file.EndsWith(".apk", StringComparison.OrdinalIgnoreCase) Then
    '                    lstFile.Add(file)
    '                End If
    '            End If
    '        Next

    '    End If

    '    Return lstFile
    'End Function

    'Public Function GetFriendlyErrorText(ByVal strErrorCode As String) As String Implements IApp.GetFriendlyErrorText
    '    Dim result As String = strErrorCode

    '    Select Case strErrorCode
    '        Case AndroidInstallErrorCode.INSTALL_PARSE_FAILED_INCONSISTENT_CERTIFICATES
    '            result = Me.Language.GetString("Common.Message.AndroidInstallErrorCertificates")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_ALREADY_EXISTS
    '            result = Me.Language.GetString("App.Cell.AlreadyInstall")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_INSUFFICIENT_STORAGE
    '            result = Me.Language.GetString("FIle.Message.StorageNotEnough")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_DEXOPT
    '            result = Me.Language.GetString("Download.Message.LackOfSpace")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_MEDIA_UNAVAILABLE
    '            result = Me.Language.GetString("Welcom.Message.AndroidInstallErrorNotSD")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_UID_CHANGED
    '            result = Me.Language.GetString("Download.Message.AppConflict")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_UPDATE_INCOMPATIBLE
    '            result = Me.Language.GetString("Download.Message.IncompatibleVersion")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_NO_CERTIFICATES
    '            result = Me.Language.GetString("Download.Message.NoCertifcates")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_INVALID_APK
    '            result = Me.Language.GetString("Download.Message.FailedInvalidApk")
    '        Case AndroidInstallErrorCode.INSTAL_FAILED_CANCELED_BY_USER
    '            result = Me.Language.GetString("Download.Message.CanceledByUser")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_UNKNOW_MESSAGE
    '            result = Me.Language.GetString("Download.Message.UnknowMessage")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_INTERNAL_ERROR
    '            result = Me.Language.GetString("Common.Message.AndroidInstallErrorInternal")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_CONTAINER_ERROR
    '            result = Me.Language.GetString("Common.Message.AndroidInstallErrorContainer")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_INVALID_INSTALL_LOCATION
    '            result = Me.Language.GetString("Welcome.Message.AndroidInstallErrorInvalidLocation")
    '        Case AndroidInstallErrorCode.INSTALL_FAILED_VERSION_DOWNGRADE
    '            result = Me.Language.GetString("Welcom.Message.AndroidInstallErrorVersionDowngrage")
    '    End Select

    '    Return result
    'End Function

    'Public Sub CheckLocalUpdate(ByVal apps As List(Of PackageInfo), ByVal devType As DeviceType) Implements IApp.CheckLocalUpdate
    '    If devType = DeviceType.iOS Then
    '    Else
    '        If Me.mAndroidLocalAppUpdate IsNot Nothing Then
    '            mAndroidLocalAppUpdate.AddAppToUpdate(apps)
    '        End If
    '    End If
    'End Sub

    ''Public Sub RemoveRowFormLocalAppUpdate(ByVal app As PackageInfo, ByVal devType As DeviceType) Implements IApp.RemoveRowFormLocalAppUpdate
    ''    If devType = DeviceType.iOS Then
    ''        If mAppleLocalAppUpdate IsNot Nothing Then
    ''            ' mAppleLocalAppUpdate.RemoveFromUpdateList(app)
    ''        End If
    ''    Else
    ''        If mAndroidLocalAppUpdate IsNot Nothing Then
    ''            mAndroidLocalAppUpdate.RemoveFromUpdateList(app)
    ''        End If
    ''    End If
    ''End Sub

    'Public Sub GotoAppSite(ByVal strSiteName As String, Optional ByVal strUrl As String = "")
    '    Try
    '        If strSiteName.Equals("appshare", StringComparison.OrdinalIgnoreCase) Then
    '            Me.tvwApp.SelectedNode = Me.tvwApp.Nodes(CONST_WEBAPPSHARE)
    '        ElseIf strSiteName.Equals("appandroid", StringComparison.OrdinalIgnoreCase) Then
    '            Me.tvwApp.SelectedNode = Me.tvwApp.Nodes(CONST_WEBAPPANDROID)
    '        Else
    '            Me.tvwApp.SelectedNode = Me.tvwApp.Nodes(CONST_WEBAPP)
    '        End If
    '    Catch ex As Exception
    '    End Try

    '    Dim strID As String = ""
    '    If Me.mDevice IsNot Nothing Then
    '        strID = Me.mDevice.Identifier
    '    ElseIf Me.mAndroid IsNot Nothing Then
    '        strID = Me.mAndroid.DeviceID
    '    End If

    '    Try
    '        'For Each subform As Control In Me.pnlContainer.Controls

    '        '    Dim ctrl As Control = CType(subform, Control)

    '        'Next

    '        If Me.tvwApp.SelectedNode.Name.Equals(CONST_WEBAPP, StringComparison.OrdinalIgnoreCase) Then

    '            'CType(ctrl, frmSite).SelectSite("App", strUrl)
    '            Me.mApplication.GotoSite(ActionFuncType.App, "App", strID, strUrl)
    '        ElseIf Me.tvwApp.SelectedNode.Name.Equals(CONST_WEBAPPSHARE, StringComparison.OrdinalIgnoreCase) Then

    '            Me.mApplication.GotoSite(ActionFuncType.App, "AppShare", strID, strUrl)
    '        ElseIf Me.tvwApp.SelectedNode.Name.Equals(CONST_WEBAPPANDROID, StringComparison.OrdinalIgnoreCase) Then

    '            Me.mApplication.GotoSite(ActionFuncType.App, "AppAndroid", strID, strUrl)
    '            'CType(ctrl, frmSite).SelectSite("AppShare", strUrl)
    '        End If

    '    Catch ex As Exception
    '    End Try
    'End Sub

#End Region

    'Public Overrides Sub OnReSelectSiteNode(ByVal usb As UsbDevice)
    '    MyBase.OnReSelectSiteNode(usb)
    '    If Not Me.LaterShowCompleted Then
    '        Return
    '    End If

    '    If Me.tvwFlash.SelectedNode IsNot Nothing AndAlso Me.tvwFlash.SelectedNode.Visible Then
    '        frmSite.SelectNode(Me.tvwFlash.SelectedNode, usb)
    '    Else
    '        Dim node As tbTreeNode = Nothing
    '        If Me.mAndroid IsNot Nothing Then
    '            node = frmSite.Instance(Me.mApplication, Me.mAndroid).tvwDevice.Nodes("AppAndroid")
    '        Else
    '            node = frmSite.Instance(Me.mApplication, Me.mDevice).tvwDevice.Nodes("AppShare")
    '        End If

    '        If node IsNot Nothing Then
    '            frmSite.SelectNode(node, usb)
    '        End If
    '    End If
    'End Sub

    'Protected Overrides Sub FormDelayShow()
    '    MyBase.FormDelayShow()

    '    If Me.mDevice IsNot Nothing Then
    '        Me.SwitchTreeNode(DeviceType.iOS)

    '    ElseIf Me.mAndroid IsNot Nothing Then
    '        Me.SwitchTreeNode(DeviceType.Android)

    '    Else
    '        Me.SwitchTreeNode(DeviceType.Unknown)
    '    End If
    'End Sub

    Public Overrides Sub OverridSkin()
        MyBase.OverridSkin()
        Me.tvwFlash.tbNodeSelectedBackground = My.Resources.tvw_node_bg_select
    End Sub

End Class