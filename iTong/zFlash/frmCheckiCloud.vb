﻿Public Class frmCheckiCloud

    Private miCloudType As iCloudType = iCloudType.FindMyIphone


#Region "--- 初始化 ---"

    Public Sub New(ByVal iType As iCloudType, Optional ByVal bnlShowRemember As Boolean = False)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.FilletRadius = 5
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong
        'Me.ShowInTaskbar = True
        'Me.mApplication = app
        Me.miCloudType = iType

        Me.chkRemember.Visible = bnlShowRemember

    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
       
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.SetLblMessage()

        Me.btnOK.Text = Me.Language.GetString("Common.MenuItem.Continue")
        Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")

        Me.lblStep.Text = Me.Language.GetString("CheckiCloud.Lable.Step")
        Me.lblTitle.Text = Me.Language.GetString("CheckiCloud.Lable.Title")

        Me.chkRemember.Text = Me.Language.GetString("App.Button.IgnoreNumber")

        Me.lblTitle.Location = New Point(Me.Width / 2 - Me.lblTitle.Width / 2, 80)

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        If Me.chkRemember.Visible Then
            IniSetting.SetRemindOnCloseFormCheckiCloud(Me.chkRemember.Checked)
        End If
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnOK.Click
        Me.DialogResult = Windows.Forms.DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.DialogResult = Windows.Forms.DialogResult.No
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Sub SetLblMessage()
        Select Case Me.miCloudType
            Case iCloudType.FindMyIphone
                Me.lblMsg.Text = Me.Language.GetString("CheckiCloud.Lable.Msg")
            Case iCloudType.FindMyIphoneDFURC
                Me.lblMsg.Text = Me.Language.GetString("CheckiCloud.Lable.MsgDFU")
            Case iCloudType.FindMyIphoneClose
                Me.lblMsg.Text = "您的设备上打开了iCloud中[查找我的iPhone]功能，会导致数据迁移失败，请关闭。"
            Case iCloudType.FindMyIphoneCloseBackup
                Me.lblMsg.Text = "您的设备上打开了iCloud中[查找我的iPhone]功能，会导致还原失败，请关闭。"
        End Select
    End Sub

#End Region

End Class

Public Enum iCloudType
    FindMyIphone = 0
    FindMyIphoneDFURC = 1
    FindMyIphoneClose = 2
    FindMyIphoneCloseBackup = 3
End Enum