﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmFirmware
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmFirmware))
        Me.pnlFirmware = New System.Windows.Forms.Panel()
        Me.tblayoutMain = New System.Windows.Forms.TableLayoutPanel()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.lblTitle = New System.Windows.Forms.Label()
        Me.btnFlashAndJailBreak = New iTong.Components.tbRadioButton()
        Me.btnSelectDevice = New iTong.Components.tbButton()
        Me.btnFlash = New iTong.Components.tbRadioButton()
        Me.btnJailBreak = New iTong.Components.tbRadioButton()
        Me.btnIPSW = New iTong.Components.tbButton()
        Me.btnAll = New iTong.Components.tbRadioButton()
        Me.pnlPrompt = New System.Windows.Forms.Panel()
        Me.lblPrompt = New System.Windows.Forms.Label()
        Me.pnlLoading = New System.Windows.Forms.Panel()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.lblLoading = New System.Windows.Forms.Label()
        Me.pnlNoData = New System.Windows.Forms.Panel()
        Me.lblMsg = New System.Windows.Forms.Label()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.pbNoData = New System.Windows.Forms.PictureBox()
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.tblayoutMain.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.pnlPrompt.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlNoData.SuspendLayout()
        CType(Me.pbNoData, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(1423, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(1399, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(1375, 0)
        '
        'pnlFirmware
        '
        Me.pnlFirmware.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlFirmware.BackColor = System.Drawing.Color.FromArgb(CType(CType(233, Byte), Integer), CType(CType(237, Byte), Integer), CType(CType(243, Byte), Integer))
        Me.pnlFirmware.Location = New System.Drawing.Point(400, 32)
        Me.pnlFirmware.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFirmware.Name = "pnlFirmware"
        Me.pnlFirmware.Size = New System.Drawing.Size(647, 626)
        Me.pnlFirmware.TabIndex = 26
        '
        'tblayoutMain
        '
        Me.tblayoutMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(204, Byte), Integer), CType(CType(208, Byte), Integer), CType(CType(215, Byte), Integer))
        Me.tblayoutMain.ColumnCount = 3
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.tblayoutMain.Controls.Add(Me.pnlFirmware, 1, 3)
        Me.tblayoutMain.Controls.Add(Me.Panel2, 1, 1)
        Me.tblayoutMain.Controls.Add(Me.pnlPrompt, 1, 5)
        Me.tblayoutMain.Controls.Add(Me.pnlLoading, 0, 1)
        Me.tblayoutMain.Controls.Add(Me.pnlNoData, 2, 1)
        Me.tblayoutMain.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tblayoutMain.Location = New System.Drawing.Point(0, 0)
        Me.tblayoutMain.Name = "tblayoutMain"
        Me.tblayoutMain.RowCount = 6
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.tblayoutMain.Size = New System.Drawing.Size(1447, 689)
        Me.tblayoutMain.TabIndex = 29
        '
        'Panel2
        '
        Me.Panel2.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.Panel2.Controls.Add(Me.lblTitle)
        Me.Panel2.Controls.Add(Me.btnFlashAndJailBreak)
        Me.Panel2.Controls.Add(Me.btnSelectDevice)
        Me.Panel2.Controls.Add(Me.btnFlash)
        Me.Panel2.Controls.Add(Me.btnJailBreak)
        Me.Panel2.Controls.Add(Me.btnIPSW)
        Me.Panel2.Controls.Add(Me.btnAll)
        Me.Panel2.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Panel2.Location = New System.Drawing.Point(400, 1)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(0)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(647, 30)
        Me.Panel2.TabIndex = 30
        '
        'lblTitle
        '
        Me.lblTitle.AutoSize = True
        Me.lblTitle.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblTitle.Location = New System.Drawing.Point(11, 7)
        Me.lblTitle.Name = "lblTitle"
        Me.lblTitle.Size = New System.Drawing.Size(53, 12)
        Me.lblTitle.TabIndex = 32
        Me.lblTitle.Text = "固件筛选"
        '
        'btnFlashAndJailBreak
        '
        Me.btnFlashAndJailBreak.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnFlashAndJailBreak.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnFlashAndJailBreak.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(170, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btnFlashAndJailBreak.ForeColor_Checked = System.Drawing.Color.White
        Me.btnFlashAndJailBreak.Location = New System.Drawing.Point(554, 4)
        Me.btnFlashAndJailBreak.Margin = New System.Windows.Forms.Padding(0)
        Me.btnFlashAndJailBreak.Name = "btnFlashAndJailBreak"
        Me.btnFlashAndJailBreak.Size = New System.Drawing.Size(84, 22)
        Me.btnFlashAndJailBreak.TabIndex = 31
        Me.btnFlashAndJailBreak.TabStop = True
        Me.btnFlashAndJailBreak.tbAdriftIconWhenHover = False
        Me.btnFlashAndJailBreak.tbAutoSize = False
        Me.btnFlashAndJailBreak.tbAutoSizeEx = False
        Me.btnFlashAndJailBreak.tbBadgeNumber = 0
        Me.btnFlashAndJailBreak.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFlashAndJailBreak.tbEnableToolTip = False
        Me.btnFlashAndJailBreak.tbIconCheckedMouseDown = Nothing
        Me.btnFlashAndJailBreak.tbIconCheckedMouseHover = Nothing
        Me.btnFlashAndJailBreak.tbIconCheckedMouseLeave = Nothing
        Me.btnFlashAndJailBreak.tbIconHoldPlace = True
        Me.btnFlashAndJailBreak.tbIconImage = Nothing
        Me.btnFlashAndJailBreak.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFlashAndJailBreak.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnFlashAndJailBreak.tbIconMore = False
        Me.btnFlashAndJailBreak.tbIconMoreImage = Nothing
        Me.btnFlashAndJailBreak.tbIconPlaceText = 5
        Me.btnFlashAndJailBreak.tbIconReadOnly = Nothing
        Me.btnFlashAndJailBreak.tbIconReadOnlyLight = Nothing
        Me.btnFlashAndJailBreak.tbIconUnCheckedMouseDown = Nothing
        Me.btnFlashAndJailBreak.tbIconUnCheckedMouseHover = Nothing
        Me.btnFlashAndJailBreak.tbIconUnCheckedMouseLeave = Nothing
        Me.btnFlashAndJailBreak.tbImageBackground = Nothing
        Me.btnFlashAndJailBreak.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnFlashAndJailBreak.tbImageCheckedMouseDown = Nothing
        Me.btnFlashAndJailBreak.tbImageCheckedMouseHover = Nothing
        Me.btnFlashAndJailBreak.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnFlashAndJailBreak.tbImageUnCheckedMouseDown = Nothing
        Me.btnFlashAndJailBreak.tbImageUnCheckedMouseHover = Nothing
        Me.btnFlashAndJailBreak.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnFlashAndJailBreak.tbNoShowBadgeNumberImage = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnFlashAndJailBreak.tbReadOnly = False
        Me.btnFlashAndJailBreak.tbShadow = False
        Me.btnFlashAndJailBreak.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFlashAndJailBreak.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFlashAndJailBreak.tbShowBadgeNumber = True
        Me.btnFlashAndJailBreak.tbShowBadgeNumberWhenSelection = True
        Me.btnFlashAndJailBreak.tbShowNew = False
        Me.btnFlashAndJailBreak.tbSplit = "3,3,3,3"
        Me.btnFlashAndJailBreak.tbTag = Nothing
        Me.btnFlashAndJailBreak.tbToolTip = ""
        Me.btnFlashAndJailBreak.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnFlashAndJailBreak.Text = "可刷可越"
        Me.btnFlashAndJailBreak.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFlashAndJailBreak.UseVisualStyleBackColor = True
        '
        'btnSelectDevice
        '
        Me.btnSelectDevice.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnSelectDevice.BackColor = System.Drawing.Color.White
        Me.btnSelectDevice.BindingForm = Nothing
        Me.btnSelectDevice.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSelectDevice.Location = New System.Drawing.Point(72, 3)
        Me.btnSelectDevice.Margin = New System.Windows.Forms.Padding(0)
        Me.btnSelectDevice.Name = "btnSelectDevice"
        Me.btnSelectDevice.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnSelectDevice.Selectable = True
        Me.btnSelectDevice.Size = New System.Drawing.Size(120, 24)
        Me.btnSelectDevice.TabIndex = 28
        Me.btnSelectDevice.tbAdriftIconWhenHover = False
        Me.btnSelectDevice.tbAutoSize = False
        Me.btnSelectDevice.tbAutoSizeEx = False
        Me.btnSelectDevice.tbBackgroundImage = CType(resources.GetObject("btnSelectDevice.tbBackgroundImage"), System.Drawing.Image)
        Me.btnSelectDevice.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSelectDevice.tbBadgeNumber = 0
        Me.btnSelectDevice.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSelectDevice.tbEndEllipsis = False
        Me.btnSelectDevice.tbIconHoldPlace = True
        Me.btnSelectDevice.tbIconImage = Nothing
        Me.btnSelectDevice.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSelectDevice.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSelectDevice.tbIconMore = True
        Me.btnSelectDevice.tbIconMouseDown = Nothing
        Me.btnSelectDevice.tbIconMouseHover = Nothing
        Me.btnSelectDevice.tbIconMouseLeave = Nothing
        Me.btnSelectDevice.tbIconPlaceText = 2
        Me.btnSelectDevice.tbIconReadOnly = Nothing
        Me.btnSelectDevice.tbImageMouseDown = Nothing
        Me.btnSelectDevice.tbImageMouseHover = Nothing
        Me.btnSelectDevice.tbImageMouseLeave = Nothing
        Me.btnSelectDevice.tbProgressValue = 50
        Me.btnSelectDevice.tbReadOnly = False
        Me.btnSelectDevice.tbReadOnlyText = False
        Me.btnSelectDevice.tbShadow = False
        Me.btnSelectDevice.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSelectDevice.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSelectDevice.tbShowDot = False
        Me.btnSelectDevice.tbShowMoreIconImg = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnSelectDevice.tbShowNew = False
        Me.btnSelectDevice.tbShowProgress = False
        Me.btnSelectDevice.tbShowTip = True
        Me.btnSelectDevice.tbShowToolTipOnButton = False
        Me.btnSelectDevice.tbSplit = "2,3,5,3"
        Me.btnSelectDevice.tbText = ""
        Me.btnSelectDevice.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSelectDevice.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnSelectDevice.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnSelectDevice.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnSelectDevice.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSelectDevice.tbTextMouseDownPlace = 2
        Me.btnSelectDevice.tbToolTip = ""
        Me.btnSelectDevice.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSelectDevice.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSelectDevice.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSelectDevice.VisibleEx = True
        '
        'btnFlash
        '
        Me.btnFlash.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnFlash.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnFlash.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(170, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btnFlash.ForeColor_Checked = System.Drawing.Color.White
        Me.btnFlash.Location = New System.Drawing.Point(388, 4)
        Me.btnFlash.Margin = New System.Windows.Forms.Padding(0)
        Me.btnFlash.Name = "btnFlash"
        Me.btnFlash.Size = New System.Drawing.Size(84, 22)
        Me.btnFlash.TabIndex = 31
        Me.btnFlash.TabStop = True
        Me.btnFlash.tbAdriftIconWhenHover = False
        Me.btnFlash.tbAutoSize = False
        Me.btnFlash.tbAutoSizeEx = False
        Me.btnFlash.tbBadgeNumber = 0
        Me.btnFlash.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFlash.tbEnableToolTip = False
        Me.btnFlash.tbIconCheckedMouseDown = Nothing
        Me.btnFlash.tbIconCheckedMouseHover = Nothing
        Me.btnFlash.tbIconCheckedMouseLeave = Nothing
        Me.btnFlash.tbIconHoldPlace = True
        Me.btnFlash.tbIconImage = Nothing
        Me.btnFlash.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFlash.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnFlash.tbIconMore = False
        Me.btnFlash.tbIconMoreImage = Nothing
        Me.btnFlash.tbIconPlaceText = 5
        Me.btnFlash.tbIconReadOnly = Nothing
        Me.btnFlash.tbIconReadOnlyLight = Nothing
        Me.btnFlash.tbIconUnCheckedMouseDown = Nothing
        Me.btnFlash.tbIconUnCheckedMouseHover = Nothing
        Me.btnFlash.tbIconUnCheckedMouseLeave = Nothing
        Me.btnFlash.tbImageBackground = Nothing
        Me.btnFlash.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnFlash.tbImageCheckedMouseDown = Nothing
        Me.btnFlash.tbImageCheckedMouseHover = Nothing
        Me.btnFlash.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnFlash.tbImageUnCheckedMouseDown = Nothing
        Me.btnFlash.tbImageUnCheckedMouseHover = Nothing
        Me.btnFlash.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnFlash.tbNoShowBadgeNumberImage = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnFlash.tbReadOnly = False
        Me.btnFlash.tbShadow = False
        Me.btnFlash.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFlash.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFlash.tbShowBadgeNumber = True
        Me.btnFlash.tbShowBadgeNumberWhenSelection = True
        Me.btnFlash.tbShowNew = False
        Me.btnFlash.tbSplit = "3,3,3,3"
        Me.btnFlash.tbTag = Nothing
        Me.btnFlash.tbToolTip = ""
        Me.btnFlash.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnFlash.Text = "可刷机"
        Me.btnFlash.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFlash.UseVisualStyleBackColor = True
        '
        'btnJailBreak
        '
        Me.btnJailBreak.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnJailBreak.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnJailBreak.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(170, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btnJailBreak.ForeColor_Checked = System.Drawing.Color.White
        Me.btnJailBreak.Location = New System.Drawing.Point(471, 4)
        Me.btnJailBreak.Margin = New System.Windows.Forms.Padding(0)
        Me.btnJailBreak.Name = "btnJailBreak"
        Me.btnJailBreak.Size = New System.Drawing.Size(84, 22)
        Me.btnJailBreak.TabIndex = 31
        Me.btnJailBreak.TabStop = True
        Me.btnJailBreak.tbAdriftIconWhenHover = False
        Me.btnJailBreak.tbAutoSize = False
        Me.btnJailBreak.tbAutoSizeEx = False
        Me.btnJailBreak.tbBadgeNumber = 0
        Me.btnJailBreak.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnJailBreak.tbEnableToolTip = False
        Me.btnJailBreak.tbIconCheckedMouseDown = Nothing
        Me.btnJailBreak.tbIconCheckedMouseHover = Nothing
        Me.btnJailBreak.tbIconCheckedMouseLeave = Nothing
        Me.btnJailBreak.tbIconHoldPlace = True
        Me.btnJailBreak.tbIconImage = Nothing
        Me.btnJailBreak.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJailBreak.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnJailBreak.tbIconMore = False
        Me.btnJailBreak.tbIconMoreImage = Nothing
        Me.btnJailBreak.tbIconPlaceText = 5
        Me.btnJailBreak.tbIconReadOnly = Nothing
        Me.btnJailBreak.tbIconReadOnlyLight = Nothing
        Me.btnJailBreak.tbIconUnCheckedMouseDown = Nothing
        Me.btnJailBreak.tbIconUnCheckedMouseHover = Nothing
        Me.btnJailBreak.tbIconUnCheckedMouseLeave = Nothing
        Me.btnJailBreak.tbImageBackground = Nothing
        Me.btnJailBreak.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnJailBreak.tbImageCheckedMouseDown = Nothing
        Me.btnJailBreak.tbImageCheckedMouseHover = Nothing
        Me.btnJailBreak.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnJailBreak.tbImageUnCheckedMouseDown = Nothing
        Me.btnJailBreak.tbImageUnCheckedMouseHover = Nothing
        Me.btnJailBreak.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnJailBreak.tbNoShowBadgeNumberImage = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnJailBreak.tbReadOnly = False
        Me.btnJailBreak.tbShadow = False
        Me.btnJailBreak.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnJailBreak.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnJailBreak.tbShowBadgeNumber = True
        Me.btnJailBreak.tbShowBadgeNumberWhenSelection = True
        Me.btnJailBreak.tbShowNew = False
        Me.btnJailBreak.tbSplit = "3,3,3,3"
        Me.btnJailBreak.tbTag = Nothing
        Me.btnJailBreak.tbToolTip = ""
        Me.btnJailBreak.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnJailBreak.Text = "可越狱"
        Me.btnJailBreak.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnJailBreak.UseVisualStyleBackColor = True
        '
        'btnIPSW
        '
        Me.btnIPSW.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnIPSW.BackColor = System.Drawing.Color.White
        Me.btnIPSW.BindingForm = Nothing
        Me.btnIPSW.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnIPSW.Location = New System.Drawing.Point(197, 3)
        Me.btnIPSW.Margin = New System.Windows.Forms.Padding(0)
        Me.btnIPSW.Name = "btnIPSW"
        Me.btnIPSW.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnIPSW.Selectable = True
        Me.btnIPSW.Size = New System.Drawing.Size(120, 24)
        Me.btnIPSW.TabIndex = 30
        Me.btnIPSW.tbAdriftIconWhenHover = False
        Me.btnIPSW.tbAutoSize = False
        Me.btnIPSW.tbAutoSizeEx = False
        Me.btnIPSW.tbBackgroundImage = CType(resources.GetObject("btnIPSW.tbBackgroundImage"), System.Drawing.Image)
        Me.btnIPSW.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnIPSW.tbBadgeNumber = 0
        Me.btnIPSW.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnIPSW.tbEndEllipsis = False
        Me.btnIPSW.tbIconHoldPlace = True
        Me.btnIPSW.tbIconImage = Nothing
        Me.btnIPSW.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnIPSW.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnIPSW.tbIconMore = True
        Me.btnIPSW.tbIconMouseDown = Nothing
        Me.btnIPSW.tbIconMouseHover = Nothing
        Me.btnIPSW.tbIconMouseLeave = Nothing
        Me.btnIPSW.tbIconPlaceText = 2
        Me.btnIPSW.tbIconReadOnly = Nothing
        Me.btnIPSW.tbImageMouseDown = Nothing
        Me.btnIPSW.tbImageMouseHover = Nothing
        Me.btnIPSW.tbImageMouseLeave = Nothing
        Me.btnIPSW.tbProgressValue = 50
        Me.btnIPSW.tbReadOnly = False
        Me.btnIPSW.tbReadOnlyText = False
        Me.btnIPSW.tbShadow = False
        Me.btnIPSW.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnIPSW.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnIPSW.tbShowDot = False
        Me.btnIPSW.tbShowMoreIconImg = Global.iTong.My.Resources.Resources.btn_icom
        Me.btnIPSW.tbShowNew = False
        Me.btnIPSW.tbShowProgress = False
        Me.btnIPSW.tbShowTip = True
        Me.btnIPSW.tbShowToolTipOnButton = False
        Me.btnIPSW.tbSplit = "2,3,5,3"
        Me.btnIPSW.tbText = ""
        Me.btnIPSW.tbTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnIPSW.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnIPSW.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnIPSW.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnIPSW.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(88, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnIPSW.tbTextMouseDownPlace = 2
        Me.btnIPSW.tbToolTip = ""
        Me.btnIPSW.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnIPSW.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnIPSW.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnIPSW.VisibleEx = True
        '
        'btnAll
        '
        Me.btnAll.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAll.Appearance = System.Windows.Forms.Appearance.Button
        Me.btnAll.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(171, Byte), Integer), CType(CType(225, Byte), Integer))
        Me.btnAll.ForeColor_Checked = System.Drawing.Color.White
        Me.btnAll.Location = New System.Drawing.Point(305, 4)
        Me.btnAll.Margin = New System.Windows.Forms.Padding(0)
        Me.btnAll.Name = "btnAll"
        Me.btnAll.Size = New System.Drawing.Size(84, 22)
        Me.btnAll.TabIndex = 30
        Me.btnAll.TabStop = True
        Me.btnAll.tbAdriftIconWhenHover = False
        Me.btnAll.tbAutoSize = False
        Me.btnAll.tbAutoSizeEx = False
        Me.btnAll.tbBadgeNumber = 0
        Me.btnAll.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAll.tbEnableToolTip = False
        Me.btnAll.tbIconCheckedMouseDown = Nothing
        Me.btnAll.tbIconCheckedMouseHover = Nothing
        Me.btnAll.tbIconCheckedMouseLeave = Nothing
        Me.btnAll.tbIconHoldPlace = True
        Me.btnAll.tbIconImage = Nothing
        Me.btnAll.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAll.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnAll.tbIconMore = False
        Me.btnAll.tbIconMoreImage = CType(resources.GetObject("btnAll.tbIconMoreImage"), System.Drawing.Image)
        Me.btnAll.tbIconPlaceText = 5
        Me.btnAll.tbIconReadOnly = Nothing
        Me.btnAll.tbIconReadOnlyLight = Nothing
        Me.btnAll.tbIconUnCheckedMouseDown = Nothing
        Me.btnAll.tbIconUnCheckedMouseHover = Nothing
        Me.btnAll.tbIconUnCheckedMouseLeave = Nothing
        Me.btnAll.tbImageBackground = Nothing
        Me.btnAll.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.btnAll.tbImageCheckedMouseDown = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnAll.tbImageCheckedMouseHover = Nothing
        Me.btnAll.tbImageCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_selected
        Me.btnAll.tbImageUnCheckedMouseDown = Nothing
        Me.btnAll.tbImageUnCheckedMouseHover = Nothing
        Me.btnAll.tbImageUnCheckedMouseLeave = Global.iTong.My.Resources.Resources.btn_viewlist_unselected
        Me.btnAll.tbNoShowBadgeNumberImage = CType(resources.GetObject("btnAll.tbNoShowBadgeNumberImage"), System.Drawing.Image)
        Me.btnAll.tbReadOnly = False
        Me.btnAll.tbShadow = False
        Me.btnAll.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnAll.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnAll.tbShowBadgeNumber = True
        Me.btnAll.tbShowBadgeNumberWhenSelection = True
        Me.btnAll.tbShowNew = False
        Me.btnAll.tbSplit = "3,3,3,3"
        Me.btnAll.tbTag = Nothing
        Me.btnAll.tbToolTip = ""
        Me.btnAll.tbVirtualIconSize = New System.Drawing.Size(0, 0)
        Me.btnAll.Text = "全部"
        Me.btnAll.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAll.UseVisualStyleBackColor = True
        '
        'pnlPrompt
        '
        Me.pnlPrompt.BackColor = System.Drawing.Color.FromArgb(CType(CType(205, Byte), Integer), CType(CType(215, Byte), Integer), CType(CType(230, Byte), Integer))
        Me.pnlPrompt.Controls.Add(Me.lblPrompt)
        Me.pnlPrompt.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlPrompt.Location = New System.Drawing.Point(400, 659)
        Me.pnlPrompt.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlPrompt.Name = "pnlPrompt"
        Me.pnlPrompt.Size = New System.Drawing.Size(647, 30)
        Me.pnlPrompt.TabIndex = 31
        '
        'lblPrompt
        '
        Me.lblPrompt.BackColor = System.Drawing.Color.Transparent
        Me.lblPrompt.ForeColor = System.Drawing.Color.FromArgb(CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer), CType(CType(82, Byte), Integer))
        Me.lblPrompt.Location = New System.Drawing.Point(14, 4)
        Me.lblPrompt.Name = "lblPrompt"
        Me.lblPrompt.Size = New System.Drawing.Size(972, 23)
        Me.lblPrompt.TabIndex = 0
        Me.lblPrompt.Text = "Label1"
        Me.lblPrompt.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlLoading
        '
        Me.pnlLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlLoading.Controls.Add(Me.pbLoading)
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlLoading.Location = New System.Drawing.Point(0, 1)
        Me.pnlLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlLoading.Name = "pnlLoading"
        Me.tblayoutMain.SetRowSpan(Me.pnlLoading, 5)
        Me.pnlLoading.Size = New System.Drawing.Size(400, 688)
        Me.pnlLoading.TabIndex = 32
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbLoading.Location = New System.Drawing.Point(93, 297)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(91, 95)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbLoading.TabIndex = 13
        Me.pbLoading.TabStop = False
        '
        'lblLoading
        '
        Me.lblLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblLoading.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLoading.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblLoading.Location = New System.Drawing.Point(187, 322)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(120, 45)
        Me.lblLoading.TabIndex = 12
        Me.lblLoading.Text = "Loading..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlNoData
        '
        Me.pnlNoData.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlNoData.Controls.Add(Me.lblMsg)
        Me.pnlNoData.Controls.Add(Me.btnRefresh)
        Me.pnlNoData.Controls.Add(Me.pbNoData)
        Me.pnlNoData.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlNoData.Location = New System.Drawing.Point(1047, 1)
        Me.pnlNoData.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlNoData.Name = "pnlNoData"
        Me.tblayoutMain.SetRowSpan(Me.pnlNoData, 5)
        Me.pnlNoData.Size = New System.Drawing.Size(400, 688)
        Me.pnlNoData.TabIndex = 33
        '
        'lblMsg
        '
        Me.lblMsg.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblMsg.Font = New System.Drawing.Font("宋体", 12.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.lblMsg.Location = New System.Drawing.Point(171, 317)
        Me.lblMsg.Name = "lblMsg"
        Me.lblMsg.Size = New System.Drawing.Size(153, 28)
        Me.lblMsg.TabIndex = 27
        Me.lblMsg.Text = "取得に失敗しました"
        Me.lblMsg.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(174, 348)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(90, 23)
        Me.btnRefresh.TabIndex = 26
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = False
        Me.btnRefresh.tbAutoSizeEx = False
        Me.btnRefresh.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Nothing
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "3,3,3,3"
        Me.btnRefresh.tbText = "重新加载"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.White
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.White
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.White
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.White
        Me.btnRefresh.tbTextMouseDownPlace = 0
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 7.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'pbNoData
        '
        Me.pbNoData.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbNoData.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbNoData.Location = New System.Drawing.Point(77, 297)
        Me.pbNoData.Margin = New System.Windows.Forms.Padding(0)
        Me.pbNoData.Name = "pbNoData"
        Me.pbNoData.Size = New System.Drawing.Size(91, 95)
        Me.pbNoData.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage
        Me.pbNoData.TabIndex = 15
        Me.pbNoData.TabStop = False
        '
        'frmFirmware
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(1447, 689)
        Me.Controls.Add(Me.tblayoutMain)
        Me.Name = "frmFirmware"
        Me.tbShowIconOnForm = False
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmFirmware"
        Me.Controls.SetChildIndex(Me.tblayoutMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.tblayoutMain.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        Me.pnlPrompt.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlNoData.ResumeLayout(False)
        CType(Me.pbNoData, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlFirmware As System.Windows.Forms.Panel
    Friend WithEvents tblayoutMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents btnSelectDevice As iTong.Components.tbButton
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents btnFlash As iTong.Components.tbRadioButton
    Friend WithEvents btnAll As iTong.Components.tbRadioButton
    Friend WithEvents btnIPSW As iTong.Components.tbButton
    Friend WithEvents btnFlashAndJailBreak As iTong.Components.tbRadioButton
    Friend WithEvents btnJailBreak As iTong.Components.tbRadioButton
    Friend WithEvents lblTitle As System.Windows.Forms.Label
    Friend WithEvents pnlPrompt As System.Windows.Forms.Panel
    Friend WithEvents lblPrompt As System.Windows.Forms.Label
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents pnlNoData As System.Windows.Forms.Panel
    Friend WithEvents pbNoData As System.Windows.Forms.PictureBox
    Friend WithEvents lblMsg As System.Windows.Forms.Label
    Friend WithEvents btnRefresh As iTong.Components.tbButton
End Class
