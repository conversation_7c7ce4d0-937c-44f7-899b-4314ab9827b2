﻿Public Class frmLoginAppleid

    Public mAppleid As String = ""
    Public mPwd As String = ""
    Public mIsCancel As Boolean = False

    Public Sub New(ByVal strTitle As String, ByVal strType As String, Optional ByRef isTryAgain As Boolean = False)
        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance
        Me.Icon = My.Resources.iTong

        Me.txtPwd.PasswordChar = ""
        Me.txtPwd.Text = ""

        Me.Text = String.Format("{0}{1}", Me.Language.GetString("Flash.Label.ActivateText"), strType)

        Me.lblMsg.Visible = isTryAgain
        Me.lblTitle.Text = String.Format(Me.Language.GetString("Flash.Label.ActivateLoginTip"), strTitle) '"此iPhone当前已链接到AppleID（{0}）。请使用此AppleID登录"
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Me.txtId.TextTip = Me.Language.GetString("App.Tip.EnterAppleId")                            '"输入您的 Apple ID"
        Me.txtId.Font = Common.CreateFont("微软雅黑", 10.0!, FontStyle.Regular)
        Me.txtPwd.TextTip = Me.Language.GetString("App.Tip.EnterPwd")                               '"输入 Apple ID 密码"
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
    End Sub

    Private Sub txtPwd_KeyUp(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtPwd.KeyUp
        If Me.txtPwd.Text.Length <= 0 Then
            Me.txtPwd.PasswordChar = ""
        Else
            Me.txtPwd.PasswordChar = "*"
        End If
    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Me.mAppleid = Me.txtId.Text
        Me.mPwd = Me.txtPwd.Text
        Me.DialogResult = Windows.Forms.DialogResult.OK
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.mIsCancel = True
        Me.DialogResult = Windows.Forms.DialogResult.OK
    End Sub
End Class