﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmFlash
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmFlash))
        Me.cbxRetainData = New iTong.Components.tbCheckBox()
        Me.cbxActivate = New iTong.Components.tbCheckBox()
        Me.pnlBottom = New System.Windows.Forms.Panel()
        Me.btnActivate = New iTong.Components.tbButton()
        Me.btnBackup = New iTong.Components.tbButton()
        Me.btnStartFlash = New iTong.Components.tbButton()
        Me.pnlFlashing = New System.Windows.Forms.Panel()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.lblPercentage = New System.Windows.Forms.Label()
        Me.lblProgress = New System.Windows.Forms.Label()
        Me.lblFlashWaiting = New System.Windows.Forms.Label()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.lblFlashWaitingMsg = New System.Windows.Forms.Label()
        Me.pnlFlashed = New System.Windows.Forms.Panel()
        Me.pnlFlashFailure = New System.Windows.Forms.Panel()
        Me.lklblQQ = New System.Windows.Forms.LinkLabel()
        Me.lblQQ = New System.Windows.Forms.Label()
        Me.lblBBSUrl = New System.Windows.Forms.Label()
        Me.lklblBBSUrl = New System.Windows.Forms.LinkLabel()
        Me.lblErrorMsg = New System.Windows.Forms.Label()
        Me.lblEMsg = New System.Windows.Forms.Label()
        Me.lblECode = New System.Windows.Forms.Label()
        Me.lblErrorCode = New System.Windows.Forms.Label()
        Me.btnRetry = New iTong.Components.tbButton()
        Me.btnReturn = New iTong.Components.tbButton()
        Me.picFileEmpty = New System.Windows.Forms.PictureBox()
        Me.lblFileEmpty = New iTong.Components.tbLabel()
        Me.ptbFlashSucceed = New iTong.Components.tbPictureBox()
        Me.pnlTop = New System.Windows.Forms.Panel()
        Me.btnImport = New iTong.Components.tbButton()
        Me.pnlStep = New iTong.Components.tbPanel()
        Me.btnRefresh = New iTong.Components.tbButton()
        Me.btnDecollator = New iTong.Components.tbButton()
        Me.btnSecond = New iTong.Components.tbButton()
        Me.btnFirst = New iTong.Components.tbButton()
        Me.pnlUnConnect = New System.Windows.Forms.Panel()
        Me.rtxtTitle = New System.Windows.Forms.RichTextBox()
        Me.lblDevNoConn = New System.Windows.Forms.Label()
        Me.picDefault = New System.Windows.Forms.PictureBox()
        Me.lblLinkMode = New System.Windows.Forms.Label()
        Me.tipDgvCell = New System.Windows.Forms.ToolTip(Me.components)
        Me.tblayoutMain = New System.Windows.Forms.TableLayoutPanel()
        Me.pnlFirmware = New iTong.Components.tbPanel()
        Me.pnlPrompt = New System.Windows.Forms.Panel()
        Me.lblPrompt = New System.Windows.Forms.Label()
        Me.pnlBottom.SuspendLayout()
        Me.pnlFlashing.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlFlashed.SuspendLayout()
        Me.pnlFlashFailure.SuspendLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ptbFlashSucceed, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlTop.SuspendLayout()
        Me.pnlStep.SuspendLayout()
        Me.pnlUnConnect.SuspendLayout()
        CType(Me.picDefault, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.tblayoutMain.SuspendLayout()
        Me.pnlFirmware.SuspendLayout()
        Me.pnlPrompt.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(2103, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(2079, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(2055, 0)
        '
        'cbxRetainData
        '
        Me.cbxRetainData.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.cbxRetainData.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxRetainData.Font = New System.Drawing.Font("宋体", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxRetainData.ForeColor = System.Drawing.Color.Black
        Me.cbxRetainData.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.cbxRetainData.Location = New System.Drawing.Point(69, 8)
        Me.cbxRetainData.Name = "cbxRetainData"
        Me.cbxRetainData.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxRetainData.Size = New System.Drawing.Size(132, 23)
        Me.cbxRetainData.TabIndex = 9
        Me.cbxRetainData.tbAdriftIconWhenHover = False
        Me.cbxRetainData.tbAutoSize = False
        Me.cbxRetainData.tbAutoSizeEx = True
        Me.cbxRetainData.tbIconChecked = CType(resources.GetObject("cbxRetainData.tbIconChecked"), System.Drawing.Image)
        Me.cbxRetainData.tbIconCheckedMouseDown = Nothing
        Me.cbxRetainData.tbIconCheckedMouseHover = Nothing
        Me.cbxRetainData.tbIconCheckedMouseLeave = Nothing
        Me.cbxRetainData.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxRetainData.tbIconHoldPlace = True
        Me.cbxRetainData.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxRetainData.tbIconIndeterminate = CType(resources.GetObject("cbxRetainData.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxRetainData.tbIconIndeterminateMouseDown = Nothing
        Me.cbxRetainData.tbIconIndeterminateMouseHover = Nothing
        Me.cbxRetainData.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxRetainData.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxRetainData.tbIconPlaceText = 1
        Me.cbxRetainData.tbIconUnChecked = CType(resources.GetObject("cbxRetainData.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxRetainData.tbIconUnCheckedMouseDown = Nothing
        Me.cbxRetainData.tbIconUnCheckedMouseHover = Nothing
        Me.cbxRetainData.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxRetainData.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxRetainData.tbImageBackground = Nothing
        Me.cbxRetainData.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxRetainData.tbImageCheckedMouseDown = Nothing
        Me.cbxRetainData.tbImageCheckedMouseHover = Nothing
        Me.cbxRetainData.tbImageCheckedMouseLeave = Nothing
        Me.cbxRetainData.tbImageUnCheckedMouseDown = Nothing
        Me.cbxRetainData.tbImageUnCheckedMouseHover = Nothing
        Me.cbxRetainData.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxRetainData.tbReadOnly = False
        Me.cbxRetainData.tbShadow = False
        Me.cbxRetainData.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxRetainData.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxRetainData.tbSplit = "3,3,3,3"
        Me.cbxRetainData.tbToolTip = ""
        Me.cbxRetainData.Text = "保数据备份刷机"
        Me.cbxRetainData.UseVisualStyleBackColor = True
        '
        'cbxActivate
        '
        Me.cbxActivate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.cbxActivate.Appearance = System.Windows.Forms.Appearance.Button
        Me.cbxActivate.Font = New System.Drawing.Font("宋体", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.cbxActivate.ForeColor = System.Drawing.Color.Black
        Me.cbxActivate.ForeColor_Checked = System.Drawing.SystemColors.ControlText
        Me.cbxActivate.Location = New System.Drawing.Point(669, 35)
        Me.cbxActivate.Name = "cbxActivate"
        Me.cbxActivate.Padding = New System.Windows.Forms.Padding(5, 0, 5, 0)
        Me.cbxActivate.Size = New System.Drawing.Size(90, 23)
        Me.cbxActivate.TabIndex = 10
        Me.cbxActivate.tbAdriftIconWhenHover = False
        Me.cbxActivate.tbAutoSize = False
        Me.cbxActivate.tbAutoSizeEx = True
        Me.cbxActivate.tbIconChecked = CType(resources.GetObject("cbxActivate.tbIconChecked"), System.Drawing.Image)
        Me.cbxActivate.tbIconCheckedMouseDown = Nothing
        Me.cbxActivate.tbIconCheckedMouseHover = Nothing
        Me.cbxActivate.tbIconCheckedMouseLeave = Nothing
        Me.cbxActivate.tbIconCheckedState = iTong.Components.ImageState.FourState
        Me.cbxActivate.tbIconHoldPlace = True
        Me.cbxActivate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.cbxActivate.tbIconIndeterminate = CType(resources.GetObject("cbxActivate.tbIconIndeterminate"), System.Drawing.Image)
        Me.cbxActivate.tbIconIndeterminateMouseDown = Nothing
        Me.cbxActivate.tbIconIndeterminateMouseHover = Nothing
        Me.cbxActivate.tbIconIndeterminateMouseLeave = Nothing
        Me.cbxActivate.tbIconIndeterminateState = iTong.Components.ImageState.FourState
        Me.cbxActivate.tbIconPlaceText = 1
        Me.cbxActivate.tbIconUnChecked = CType(resources.GetObject("cbxActivate.tbIconUnChecked"), System.Drawing.Image)
        Me.cbxActivate.tbIconUnCheckedMouseDown = Nothing
        Me.cbxActivate.tbIconUnCheckedMouseHover = Nothing
        Me.cbxActivate.tbIconUnCheckedMouseLeave = Nothing
        Me.cbxActivate.tbIconUnCheckedState = iTong.Components.ImageState.FourState
        Me.cbxActivate.tbImageBackground = Nothing
        Me.cbxActivate.tbImageBackgroundState = iTong.Components.ImageState.TwoState
        Me.cbxActivate.tbImageCheckedMouseDown = Nothing
        Me.cbxActivate.tbImageCheckedMouseHover = Nothing
        Me.cbxActivate.tbImageCheckedMouseLeave = Nothing
        Me.cbxActivate.tbImageUnCheckedMouseDown = Nothing
        Me.cbxActivate.tbImageUnCheckedMouseHover = Nothing
        Me.cbxActivate.tbImageUnCheckedMouseLeave = Nothing
        Me.cbxActivate.tbReadOnly = False
        Me.cbxActivate.tbShadow = False
        Me.cbxActivate.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.cbxActivate.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.cbxActivate.tbSplit = "3,3,3,3"
        Me.cbxActivate.tbToolTip = ""
        Me.cbxActivate.Text = "设备激活"
        Me.cbxActivate.UseVisualStyleBackColor = True
        '
        'pnlBottom
        '
        Me.pnlBottom.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlBottom.Controls.Add(Me.btnActivate)
        Me.pnlBottom.Controls.Add(Me.cbxRetainData)
        Me.pnlBottom.Controls.Add(Me.cbxActivate)
        Me.pnlBottom.Controls.Add(Me.btnBackup)
        Me.pnlBottom.Controls.Add(Me.btnStartFlash)
        Me.pnlBottom.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlBottom.Location = New System.Drawing.Point(400, 578)
        Me.pnlBottom.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlBottom.Name = "pnlBottom"
        Me.pnlBottom.Size = New System.Drawing.Size(400, 67)
        Me.pnlBottom.TabIndex = 35
        '
        'btnActivate
        '
        Me.btnActivate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnActivate.BackColor = System.Drawing.Color.Transparent
        Me.btnActivate.BindingForm = Nothing
        Me.btnActivate.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnActivate.Location = New System.Drawing.Point(-338, 13)
        Me.btnActivate.Name = "btnActivate"
        Me.btnActivate.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnActivate.Selectable = True
        Me.btnActivate.Size = New System.Drawing.Size(135, 40)
        Me.btnActivate.TabIndex = 24
        Me.btnActivate.tbAdriftIconWhenHover = False
        Me.btnActivate.tbAutoSize = False
        Me.btnActivate.tbAutoSizeEx = False
        Me.btnActivate.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnActivate.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnActivate.tbBadgeNumber = 0
        Me.btnActivate.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnActivate.tbEndEllipsis = False
        Me.btnActivate.tbIconHoldPlace = True
        Me.btnActivate.tbIconImage = Nothing
        Me.btnActivate.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivate.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnActivate.tbIconMore = False
        Me.btnActivate.tbIconMouseDown = Nothing
        Me.btnActivate.tbIconMouseHover = Nothing
        Me.btnActivate.tbIconMouseLeave = Nothing
        Me.btnActivate.tbIconPlaceText = 2
        Me.btnActivate.tbIconReadOnly = Nothing
        Me.btnActivate.tbImageMouseDown = Nothing
        Me.btnActivate.tbImageMouseHover = Nothing
        Me.btnActivate.tbImageMouseLeave = Nothing
        Me.btnActivate.tbProgressValue = 50
        Me.btnActivate.tbReadOnly = False
        Me.btnActivate.tbReadOnlyText = False
        Me.btnActivate.tbShadow = False
        Me.btnActivate.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnActivate.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnActivate.tbShowDot = False
        Me.btnActivate.tbShowMoreIconImg = CType(resources.GetObject("btnActivate.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnActivate.tbShowNew = False
        Me.btnActivate.tbShowProgress = False
        Me.btnActivate.tbShowTip = True
        Me.btnActivate.tbShowToolTipOnButton = False
        Me.btnActivate.tbSplit = "13,11,13,11"
        Me.btnActivate.tbText = "激活"
        Me.btnActivate.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivate.tbTextColor = System.Drawing.Color.White
        Me.btnActivate.tbTextColorDisable = System.Drawing.Color.White
        Me.btnActivate.tbTextColorDown = System.Drawing.Color.White
        Me.btnActivate.tbTextColorHover = System.Drawing.Color.White
        Me.btnActivate.tbTextMouseDownPlace = 0
        Me.btnActivate.tbToolTip = ""
        Me.btnActivate.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnActivate.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnActivate.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnActivate.VisibleEx = True
        '
        'btnBackup
        '
        Me.btnBackup.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnBackup.BackColor = System.Drawing.Color.Transparent
        Me.btnBackup.BindingForm = Nothing
        Me.btnBackup.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.btnBackup.ForeColor = System.Drawing.SystemColors.ControlText
        Me.btnBackup.Location = New System.Drawing.Point(17, 13)
        Me.btnBackup.Name = "btnBackup"
        Me.btnBackup.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnBackup.Selectable = True
        Me.btnBackup.Size = New System.Drawing.Size(135, 40)
        Me.btnBackup.TabIndex = 23
        Me.btnBackup.tbAdriftIconWhenHover = False
        Me.btnBackup.tbAutoSize = False
        Me.btnBackup.tbAutoSizeEx = False
        Me.btnBackup.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnBackup.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnBackup.tbBadgeNumber = 0
        Me.btnBackup.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnBackup.tbEndEllipsis = False
        Me.btnBackup.tbIconHoldPlace = True
        Me.btnBackup.tbIconImage = Nothing
        Me.btnBackup.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackup.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnBackup.tbIconMore = False
        Me.btnBackup.tbIconMouseDown = Nothing
        Me.btnBackup.tbIconMouseHover = Nothing
        Me.btnBackup.tbIconMouseLeave = Nothing
        Me.btnBackup.tbIconPlaceText = 2
        Me.btnBackup.tbIconReadOnly = Nothing
        Me.btnBackup.tbImageMouseDown = Nothing
        Me.btnBackup.tbImageMouseHover = Nothing
        Me.btnBackup.tbImageMouseLeave = Nothing
        Me.btnBackup.tbProgressValue = 50
        Me.btnBackup.tbReadOnly = False
        Me.btnBackup.tbReadOnlyText = False
        Me.btnBackup.tbShadow = False
        Me.btnBackup.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnBackup.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnBackup.tbShowDot = False
        Me.btnBackup.tbShowMoreIconImg = CType(resources.GetObject("btnBackup.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnBackup.tbShowNew = False
        Me.btnBackup.tbShowProgress = False
        Me.btnBackup.tbShowTip = True
        Me.btnBackup.tbShowToolTipOnButton = False
        Me.btnBackup.tbSplit = "13,11,13,11"
        Me.btnBackup.tbText = "备份还原"
        Me.btnBackup.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackup.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBackup.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBackup.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBackup.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnBackup.tbTextMouseDownPlace = 0
        Me.btnBackup.tbToolTip = ""
        Me.btnBackup.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnBackup.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnBackup.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnBackup.VisibleEx = True
        '
        'btnStartFlash
        '
        Me.btnStartFlash.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnStartFlash.BackColor = System.Drawing.Color.Transparent
        Me.btnStartFlash.BindingForm = Nothing
        Me.btnStartFlash.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.btnStartFlash.Location = New System.Drawing.Point(248, 13)
        Me.btnStartFlash.Name = "btnStartFlash"
        Me.btnStartFlash.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnStartFlash.Selectable = True
        Me.btnStartFlash.Size = New System.Drawing.Size(135, 40)
        Me.btnStartFlash.TabIndex = 23
        Me.btnStartFlash.tbAdriftIconWhenHover = False
        Me.btnStartFlash.tbAutoSize = False
        Me.btnStartFlash.tbAutoSizeEx = False
        Me.btnStartFlash.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnStartFlash.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnStartFlash.tbBadgeNumber = 0
        Me.btnStartFlash.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnStartFlash.tbEndEllipsis = False
        Me.btnStartFlash.tbIconHoldPlace = True
        Me.btnStartFlash.tbIconImage = Nothing
        Me.btnStartFlash.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartFlash.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnStartFlash.tbIconMore = False
        Me.btnStartFlash.tbIconMouseDown = Nothing
        Me.btnStartFlash.tbIconMouseHover = Nothing
        Me.btnStartFlash.tbIconMouseLeave = Nothing
        Me.btnStartFlash.tbIconPlaceText = 2
        Me.btnStartFlash.tbIconReadOnly = Nothing
        Me.btnStartFlash.tbImageMouseDown = Nothing
        Me.btnStartFlash.tbImageMouseHover = Nothing
        Me.btnStartFlash.tbImageMouseLeave = Nothing
        Me.btnStartFlash.tbProgressValue = 50
        Me.btnStartFlash.tbReadOnly = False
        Me.btnStartFlash.tbReadOnlyText = False
        Me.btnStartFlash.tbShadow = False
        Me.btnStartFlash.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnStartFlash.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnStartFlash.tbShowDot = False
        Me.btnStartFlash.tbShowMoreIconImg = CType(resources.GetObject("btnStartFlash.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnStartFlash.tbShowNew = False
        Me.btnStartFlash.tbShowProgress = False
        Me.btnStartFlash.tbShowTip = True
        Me.btnStartFlash.tbShowToolTipOnButton = False
        Me.btnStartFlash.tbSplit = "13,11,13,11"
        Me.btnStartFlash.tbText = "开始刷机"
        Me.btnStartFlash.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartFlash.tbTextColor = System.Drawing.Color.White
        Me.btnStartFlash.tbTextColorDisable = System.Drawing.Color.White
        Me.btnStartFlash.tbTextColorDown = System.Drawing.Color.White
        Me.btnStartFlash.tbTextColorHover = System.Drawing.Color.White
        Me.btnStartFlash.tbTextMouseDownPlace = 0
        Me.btnStartFlash.tbToolTip = ""
        Me.btnStartFlash.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnStartFlash.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnStartFlash.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnStartFlash.VisibleEx = True
        '
        'pnlFlashing
        '
        Me.pnlFlashing.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFlashing.Controls.Add(Me.Label1)
        Me.pnlFlashing.Controls.Add(Me.lblPercentage)
        Me.pnlFlashing.Controls.Add(Me.lblProgress)
        Me.pnlFlashing.Controls.Add(Me.lblFlashWaiting)
        Me.pnlFlashing.Controls.Add(Me.PictureBox1)
        Me.pnlFlashing.Controls.Add(Me.lblFlashWaitingMsg)
        Me.pnlFlashing.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlFlashing.Location = New System.Drawing.Point(800, 35)
        Me.pnlFlashing.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFlashing.Name = "pnlFlashing"
        Me.tblayoutMain.SetRowSpan(Me.pnlFlashing, 5)
        Me.pnlFlashing.Size = New System.Drawing.Size(400, 610)
        Me.pnlFlashing.TabIndex = 31
        '
        'Label1
        '
        Me.Label1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.Label1.BackColor = System.Drawing.Color.FromArgb(CType(CType(244, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(244, Byte), Integer))
        Me.Label1.Font = New System.Drawing.Font("宋体", 14.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.Label1.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.Label1.Location = New System.Drawing.Point(425, 282)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(28, 38)
        Me.Label1.TabIndex = 32
        Me.Label1.Text = "%"
        Me.Label1.TextAlign = System.Drawing.ContentAlignment.BottomRight
        Me.Label1.Visible = False
        '
        'lblPercentage
        '
        Me.lblPercentage.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblPercentage.BackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.lblPercentage.Font = New System.Drawing.Font("宋体", 22.0!, System.Drawing.FontStyle.Bold)
        Me.lblPercentage.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lblPercentage.Location = New System.Drawing.Point(116, 316)
        Me.lblPercentage.Name = "lblPercentage"
        Me.lblPercentage.Size = New System.Drawing.Size(170, 44)
        Me.lblPercentage.TabIndex = 30
        Me.lblPercentage.Text = "0%"
        Me.lblPercentage.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblProgress
        '
        Me.lblProgress.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblProgress.BackColor = System.Drawing.Color.FromArgb(CType(CType(251, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(251, Byte), Integer))
        Me.lblProgress.Font = New System.Drawing.Font("宋体", 12.0!)
        Me.lblProgress.ForeColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.lblProgress.Location = New System.Drawing.Point(114, 361)
        Me.lblProgress.Name = "lblProgress"
        Me.lblProgress.Size = New System.Drawing.Size(174, 21)
        Me.lblProgress.TabIndex = 30
        Me.lblProgress.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblFlashWaiting
        '
        Me.lblFlashWaiting.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFlashWaiting.Font = New System.Drawing.Font("宋体", 17.0!)
        Me.lblFlashWaiting.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFlashWaiting.Location = New System.Drawing.Point(-182, 123)
        Me.lblFlashWaiting.Name = "lblFlashWaiting"
        Me.lblFlashWaiting.Size = New System.Drawing.Size(767, 42)
        Me.lblFlashWaiting.TabIndex = 28
        Me.lblFlashWaiting.Text = "正在执行刷机，请耐心等待..."
        Me.lblFlashWaiting.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.PictureBox1.Image = Global.iTong.My.Resources.Resources.gif_loading_231
        Me.PictureBox1.Location = New System.Drawing.Point(86, 238)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(231, 231)
        Me.PictureBox1.TabIndex = 31
        Me.PictureBox1.TabStop = False
        '
        'lblFlashWaitingMsg
        '
        Me.lblFlashWaitingMsg.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFlashWaitingMsg.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblFlashWaitingMsg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(93, Byte), Integer), CType(CType(95, Byte), Integer), CType(CType(97, Byte), Integer))
        Me.lblFlashWaitingMsg.Location = New System.Drawing.Point(-181, 165)
        Me.lblFlashWaitingMsg.Name = "lblFlashWaitingMsg"
        Me.lblFlashWaitingMsg.Size = New System.Drawing.Size(765, 23)
        Me.lblFlashWaitingMsg.TabIndex = 29
        Me.lblFlashWaitingMsg.Text = "刷机过程中，请勿断开设备或对设备进行其他操作"
        Me.lblFlashWaitingMsg.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'pnlFlashed
        '
        Me.pnlFlashed.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFlashed.Controls.Add(Me.pnlFlashFailure)
        Me.pnlFlashed.Controls.Add(Me.btnRetry)
        Me.pnlFlashed.Controls.Add(Me.btnReturn)
        Me.pnlFlashed.Controls.Add(Me.picFileEmpty)
        Me.pnlFlashed.Controls.Add(Me.lblFileEmpty)
        Me.pnlFlashed.Controls.Add(Me.ptbFlashSucceed)
        Me.pnlFlashed.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlFlashed.Location = New System.Drawing.Point(1200, 35)
        Me.pnlFlashed.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFlashed.Name = "pnlFlashed"
        Me.tblayoutMain.SetRowSpan(Me.pnlFlashed, 5)
        Me.pnlFlashed.Size = New System.Drawing.Size(1000, 610)
        Me.pnlFlashed.TabIndex = 32
        '
        'pnlFlashFailure
        '
        Me.pnlFlashFailure.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pnlFlashFailure.BackColor = System.Drawing.Color.FromArgb(CType(CType(249, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.pnlFlashFailure.Controls.Add(Me.lklblQQ)
        Me.pnlFlashFailure.Controls.Add(Me.lblQQ)
        Me.pnlFlashFailure.Controls.Add(Me.lblBBSUrl)
        Me.pnlFlashFailure.Controls.Add(Me.lklblBBSUrl)
        Me.pnlFlashFailure.Controls.Add(Me.lblErrorMsg)
        Me.pnlFlashFailure.Controls.Add(Me.lblEMsg)
        Me.pnlFlashFailure.Controls.Add(Me.lblECode)
        Me.pnlFlashFailure.Controls.Add(Me.lblErrorCode)
        Me.pnlFlashFailure.Location = New System.Drawing.Point(255, 231)
        Me.pnlFlashFailure.Name = "pnlFlashFailure"
        Me.pnlFlashFailure.Size = New System.Drawing.Size(490, 178)
        Me.pnlFlashFailure.TabIndex = 26
        '
        'lklblQQ
        '
        Me.lklblQQ.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblQQ.AutoSize = True
        Me.lklblQQ.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.lklblQQ.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblQQ.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lklblQQ.LinkColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblQQ.Location = New System.Drawing.Point(205, 137)
        Me.lklblQQ.Name = "lklblQQ"
        Me.lklblQQ.Size = New System.Drawing.Size(84, 13)
        Me.lklblQQ.TabIndex = 1
        Me.lklblQQ.TabStop = True
        Me.lklblQQ.Text = "QQ:11907135"
        '
        'lblQQ
        '
        Me.lblQQ.AutoSize = True
        Me.lblQQ.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.lblQQ.Location = New System.Drawing.Point(34, 137)
        Me.lblQQ.Name = "lblQQ"
        Me.lblQQ.Size = New System.Drawing.Size(163, 13)
        Me.lblQQ.TabIndex = 2
        Me.lblQQ.Text = "教程无法解决，请联系客服"
        '
        'lblBBSUrl
        '
        Me.lblBBSUrl.AutoSize = True
        Me.lblBBSUrl.Font = New System.Drawing.Font("宋体", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lblBBSUrl.Location = New System.Drawing.Point(34, 109)
        Me.lblBBSUrl.Name = "lblBBSUrl"
        Me.lblBBSUrl.Size = New System.Drawing.Size(82, 14)
        Me.lblBBSUrl.TabIndex = 0
        Me.lblBBSUrl.Text = "论坛连接："
        '
        'lklblBBSUrl
        '
        Me.lklblBBSUrl.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblBBSUrl.AutoSize = True
        Me.lklblBBSUrl.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.lklblBBSUrl.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblBBSUrl.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lklblBBSUrl.LinkColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblBBSUrl.Location = New System.Drawing.Point(119, 109)
        Me.lklblBBSUrl.Name = "lklblBBSUrl"
        Me.lklblBBSUrl.Size = New System.Drawing.Size(161, 14)
        Me.lklblBBSUrl.TabIndex = 1
        Me.lklblBBSUrl.TabStop = True
        Me.lklblBBSUrl.Text = "http://bbs.tongbu.com/"
        Me.lklblBBSUrl.VisitedLinkColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        '
        'lblErrorMsg
        '
        Me.lblErrorMsg.AutoSize = True
        Me.lblErrorMsg.Font = New System.Drawing.Font("宋体", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lblErrorMsg.Location = New System.Drawing.Point(34, 56)
        Me.lblErrorMsg.Name = "lblErrorMsg"
        Me.lblErrorMsg.Size = New System.Drawing.Size(82, 14)
        Me.lblErrorMsg.TabIndex = 0
        Me.lblErrorMsg.Text = "错误消息："
        '
        'lblEMsg
        '
        Me.lblEMsg.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.lblEMsg.Location = New System.Drawing.Point(119, 56)
        Me.lblEMsg.Name = "lblEMsg"
        Me.lblEMsg.Size = New System.Drawing.Size(354, 41)
        Me.lblEMsg.TabIndex = 0
        '
        'lblECode
        '
        Me.lblECode.AutoSize = True
        Me.lblECode.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.lblECode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(56, Byte), Integer), CType(CType(56, Byte), Integer))
        Me.lblECode.Location = New System.Drawing.Point(119, 29)
        Me.lblECode.Name = "lblECode"
        Me.lblECode.Size = New System.Drawing.Size(0, 14)
        Me.lblECode.TabIndex = 0
        '
        'lblErrorCode
        '
        Me.lblErrorCode.AutoSize = True
        Me.lblErrorCode.Font = New System.Drawing.Font("宋体", 10.0!, System.Drawing.FontStyle.Bold)
        Me.lblErrorCode.Location = New System.Drawing.Point(34, 29)
        Me.lblErrorCode.Name = "lblErrorCode"
        Me.lblErrorCode.Size = New System.Drawing.Size(82, 14)
        Me.lblErrorCode.TabIndex = 0
        Me.lblErrorCode.Text = "错误代码："
        '
        'btnRetry
        '
        Me.btnRetry.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnRetry.BackColor = System.Drawing.Color.Transparent
        Me.btnRetry.BindingForm = Nothing
        Me.btnRetry.Font = New System.Drawing.Font("宋体", 13.0!)
        Me.btnRetry.Location = New System.Drawing.Point(313, 438)
        Me.btnRetry.Name = "btnRetry"
        Me.btnRetry.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnRetry.Selectable = True
        Me.btnRetry.Size = New System.Drawing.Size(156, 46)
        Me.btnRetry.TabIndex = 27
        Me.btnRetry.tbAdriftIconWhenHover = False
        Me.btnRetry.tbAutoSize = False
        Me.btnRetry.tbAutoSizeEx = False
        Me.btnRetry.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnRetry.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRetry.tbBadgeNumber = 0
        Me.btnRetry.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRetry.tbEndEllipsis = False
        Me.btnRetry.tbIconHoldPlace = True
        Me.btnRetry.tbIconImage = Nothing
        Me.btnRetry.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRetry.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRetry.tbIconMore = False
        Me.btnRetry.tbIconMouseDown = Nothing
        Me.btnRetry.tbIconMouseHover = Nothing
        Me.btnRetry.tbIconMouseLeave = Nothing
        Me.btnRetry.tbIconPlaceText = 2
        Me.btnRetry.tbIconReadOnly = Nothing
        Me.btnRetry.tbImageMouseDown = Nothing
        Me.btnRetry.tbImageMouseHover = Nothing
        Me.btnRetry.tbImageMouseLeave = Nothing
        Me.btnRetry.tbProgressValue = 50
        Me.btnRetry.tbReadOnly = False
        Me.btnRetry.tbReadOnlyText = False
        Me.btnRetry.tbShadow = False
        Me.btnRetry.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRetry.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRetry.tbShowDot = False
        Me.btnRetry.tbShowMoreIconImg = CType(resources.GetObject("btnRetry.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRetry.tbShowNew = False
        Me.btnRetry.tbShowProgress = False
        Me.btnRetry.tbShowTip = True
        Me.btnRetry.tbShowToolTipOnButton = False
        Me.btnRetry.tbSplit = "13,11,13,11"
        Me.btnRetry.tbText = "重试"
        Me.btnRetry.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRetry.tbTextColor = System.Drawing.Color.White
        Me.btnRetry.tbTextColorDisable = System.Drawing.Color.White
        Me.btnRetry.tbTextColorDown = System.Drawing.Color.White
        Me.btnRetry.tbTextColorHover = System.Drawing.Color.White
        Me.btnRetry.tbTextMouseDownPlace = 0
        Me.btnRetry.tbToolTip = ""
        Me.btnRetry.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRetry.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRetry.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRetry.VisibleEx = True
        '
        'btnReturn
        '
        Me.btnReturn.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.btnReturn.BackColor = System.Drawing.Color.Transparent
        Me.btnReturn.BindingForm = Nothing
        Me.btnReturn.Font = New System.Drawing.Font("宋体", 13.0!)
        Me.btnReturn.Location = New System.Drawing.Point(531, 438)
        Me.btnReturn.Name = "btnReturn"
        Me.btnReturn.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnReturn.Selectable = True
        Me.btnReturn.Size = New System.Drawing.Size(156, 46)
        Me.btnReturn.TabIndex = 24
        Me.btnReturn.tbAdriftIconWhenHover = False
        Me.btnReturn.tbAutoSize = False
        Me.btnReturn.tbAutoSizeEx = False
        Me.btnReturn.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnReturn.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnReturn.tbBadgeNumber = 0
        Me.btnReturn.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReturn.tbEndEllipsis = False
        Me.btnReturn.tbIconHoldPlace = True
        Me.btnReturn.tbIconImage = Nothing
        Me.btnReturn.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReturn.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnReturn.tbIconMore = False
        Me.btnReturn.tbIconMouseDown = Nothing
        Me.btnReturn.tbIconMouseHover = Nothing
        Me.btnReturn.tbIconMouseLeave = Nothing
        Me.btnReturn.tbIconPlaceText = 2
        Me.btnReturn.tbIconReadOnly = Nothing
        Me.btnReturn.tbImageMouseDown = Nothing
        Me.btnReturn.tbImageMouseHover = Nothing
        Me.btnReturn.tbImageMouseLeave = Nothing
        Me.btnReturn.tbProgressValue = 50
        Me.btnReturn.tbReadOnly = False
        Me.btnReturn.tbReadOnlyText = False
        Me.btnReturn.tbShadow = False
        Me.btnReturn.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnReturn.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnReturn.tbShowDot = False
        Me.btnReturn.tbShowMoreIconImg = CType(resources.GetObject("btnReturn.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnReturn.tbShowNew = False
        Me.btnReturn.tbShowProgress = False
        Me.btnReturn.tbShowTip = True
        Me.btnReturn.tbShowToolTipOnButton = False
        Me.btnReturn.tbSplit = "13,11,13,11"
        Me.btnReturn.tbText = "返回"
        Me.btnReturn.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReturn.tbTextColor = System.Drawing.Color.White
        Me.btnReturn.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReturn.tbTextColorDown = System.Drawing.Color.White
        Me.btnReturn.tbTextColorHover = System.Drawing.Color.White
        Me.btnReturn.tbTextMouseDownPlace = 0
        Me.btnReturn.tbToolTip = ""
        Me.btnReturn.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReturn.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReturn.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReturn.VisibleEx = True
        '
        'picFileEmpty
        '
        Me.picFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picFileEmpty.Image = Global.iTong.My.Resources.Resources.file_fileempty_smile
        Me.picFileEmpty.Location = New System.Drawing.Point(360, 126)
        Me.picFileEmpty.Name = "picFileEmpty"
        Me.picFileEmpty.Size = New System.Drawing.Size(75, 75)
        Me.picFileEmpty.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picFileEmpty.TabIndex = 3
        Me.picFileEmpty.TabStop = False
        '
        'lblFileEmpty
        '
        Me.lblFileEmpty.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.lblFileEmpty.AutoSize = True
        Me.lblFileEmpty.Font = New System.Drawing.Font("宋体", 17.0!)
        Me.lblFileEmpty.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblFileEmpty.Location = New System.Drawing.Point(443, 144)
        Me.lblFileEmpty.Name = "lblFileEmpty"
        Me.lblFileEmpty.Size = New System.Drawing.Size(194, 23)
        Me.lblFileEmpty.TabIndex = 2
        Me.lblFileEmpty.tbAdriftWhenHover = False
        Me.lblFileEmpty.tbAutoEllipsis = False
        Me.lblFileEmpty.tbAutoSize = True
        Me.lblFileEmpty.tbHideImage = False
        Me.lblFileEmpty.tbIconImage = Nothing
        Me.lblFileEmpty.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblFileEmpty.tbIconPlaceText = 5
        Me.lblFileEmpty.tbShadow = False
        Me.lblFileEmpty.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblFileEmpty.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblFileEmpty.tbShowScrolling = False
        Me.lblFileEmpty.Text = "恭喜您刷机成功！"
        '
        'ptbFlashSucceed
        '
        Me.ptbFlashSucceed.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.ptbFlashSucceed.Image = Global.iTong.My.Resources.Resources.flashsucceed
        Me.ptbFlashSucceed.Location = New System.Drawing.Point(379, 231)
        Me.ptbFlashSucceed.Name = "ptbFlashSucceed"
        Me.ptbFlashSucceed.Size = New System.Drawing.Size(242, 178)
        Me.ptbFlashSucceed.TabIndex = 25
        Me.ptbFlashSucceed.TabStop = False
        Me.ptbFlashSucceed.tbAutoSize = False
        Me.ptbFlashSucceed.tbBackgroundImage = Nothing
        Me.ptbFlashSucceed.tbSplit = "0,0,0,0"
        '
        'pnlTop
        '
        Me.pnlTop.BackColor = System.Drawing.Color.FromArgb(CType(CType(235, Byte), Integer), CType(CType(236, Byte), Integer), CType(CType(238, Byte), Integer))
        Me.tblayoutMain.SetColumnSpan(Me.pnlTop, 3)
        Me.pnlTop.Controls.Add(Me.btnImport)
        Me.pnlTop.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlTop.Location = New System.Drawing.Point(400, 0)
        Me.pnlTop.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlTop.Name = "pnlTop"
        Me.pnlTop.Size = New System.Drawing.Size(1800, 1)
        Me.pnlTop.TabIndex = 33
        '
        'btnImport
        '
        Me.btnImport.BackColor = System.Drawing.Color.Transparent
        Me.btnImport.BindingForm = Nothing
        Me.btnImport.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnImport.ForeColor = System.Drawing.SystemColors.ControlText
        Me.btnImport.Location = New System.Drawing.Point(286, 6)
        Me.btnImport.Margin = New System.Windows.Forms.Padding(0)
        Me.btnImport.Name = "btnImport"
        Me.btnImport.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnImport.Selectable = True
        Me.btnImport.Size = New System.Drawing.Size(111, 23)
        Me.btnImport.TabIndex = 27
        Me.btnImport.tbAdriftIconWhenHover = False
        Me.btnImport.tbAutoSize = False
        Me.btnImport.tbAutoSizeEx = True
        Me.btnImport.tbBackgroundImage = Nothing
        Me.btnImport.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnImport.tbBadgeNumber = 0
        Me.btnImport.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnImport.tbEndEllipsis = False
        Me.btnImport.tbIconHoldPlace = True
        Me.btnImport.tbIconImage = Global.iTong.My.Resources.Resources.btn_import_4
        Me.btnImport.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnImport.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnImport.tbIconMore = False
        Me.btnImport.tbIconMouseDown = Nothing
        Me.btnImport.tbIconMouseHover = Nothing
        Me.btnImport.tbIconMouseLeave = Nothing
        Me.btnImport.tbIconPlaceText = 2
        Me.btnImport.tbIconReadOnly = Nothing
        Me.btnImport.tbImageMouseDown = Nothing
        Me.btnImport.tbImageMouseHover = Nothing
        Me.btnImport.tbImageMouseLeave = Nothing
        Me.btnImport.tbProgressValue = 50
        Me.btnImport.tbReadOnly = False
        Me.btnImport.tbReadOnlyText = False
        Me.btnImport.tbShadow = False
        Me.btnImport.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnImport.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnImport.tbShowDot = False
        Me.btnImport.tbShowMoreIconImg = CType(resources.GetObject("btnImport.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnImport.tbShowNew = False
        Me.btnImport.tbShowProgress = False
        Me.btnImport.tbShowTip = True
        Me.btnImport.tbShowToolTipOnButton = False
        Me.btnImport.tbSplit = "13,11,13,11"
        Me.btnImport.tbText = "快速导入固件"
        Me.btnImport.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnImport.tbTextColorDisable = System.Drawing.Color.LightGray
        Me.btnImport.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer), CType(CType(22, Byte), Integer))
        Me.btnImport.tbTextMouseDownPlace = 2
        Me.btnImport.tbToolTip = ""
        Me.btnImport.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnImport.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnImport.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnImport.Visible = False
        Me.btnImport.VisibleEx = True
        '
        'pnlStep
        '
        Me.pnlStep.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.tblayoutMain.SetColumnSpan(Me.pnlStep, 3)
        Me.pnlStep.Controls.Add(Me.btnRefresh)
        Me.pnlStep.Controls.Add(Me.btnDecollator)
        Me.pnlStep.Controls.Add(Me.btnSecond)
        Me.pnlStep.Controls.Add(Me.btnFirst)
        Me.pnlStep.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlStep.Location = New System.Drawing.Point(400, 0)
        Me.pnlStep.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlStep.Name = "pnlStep"
        Me.pnlStep.Size = New System.Drawing.Size(1800, 34)
        Me.pnlStep.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlStep.TabIndex = 26
        Me.pnlStep.tbBackgroundImage = Nothing
        Me.pnlStep.tbShowWatermark = False
        Me.pnlStep.tbSplit = "0,0,0,0"
        Me.pnlStep.tbWatermark = Nothing
        Me.pnlStep.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlStep.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnRefresh
        '
        Me.btnRefresh.Anchor = System.Windows.Forms.AnchorStyles.Right
        Me.btnRefresh.BackColor = System.Drawing.Color.Transparent
        Me.btnRefresh.BindingForm = Nothing
        Me.btnRefresh.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnRefresh.Location = New System.Drawing.Point(17, 9)
        Me.btnRefresh.Name = "btnRefresh"
        Me.btnRefresh.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnRefresh.Selectable = True
        Me.btnRefresh.Size = New System.Drawing.Size(59, 16)
        Me.btnRefresh.TabIndex = 17
        Me.btnRefresh.tbAdriftIconWhenHover = False
        Me.btnRefresh.tbAutoSize = True
        Me.btnRefresh.tbAutoSizeEx = True
        Me.btnRefresh.tbBackgroundImage = Nothing
        Me.btnRefresh.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbBadgeNumber = 0
        Me.btnRefresh.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRefresh.tbEndEllipsis = False
        Me.btnRefresh.tbIconHoldPlace = True
        Me.btnRefresh.tbIconImage = Global.iTong.My.Resources.Resources.btn_4_refresh
        Me.btnRefresh.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnRefresh.tbIconImageState = iTong.Components.ImageState.FourState
        Me.btnRefresh.tbIconMore = False
        Me.btnRefresh.tbIconMouseDown = Nothing
        Me.btnRefresh.tbIconMouseHover = Nothing
        Me.btnRefresh.tbIconMouseLeave = Nothing
        Me.btnRefresh.tbIconPlaceText = 2
        Me.btnRefresh.tbIconReadOnly = Nothing
        Me.btnRefresh.tbImageMouseDown = Nothing
        Me.btnRefresh.tbImageMouseHover = Nothing
        Me.btnRefresh.tbImageMouseLeave = Nothing
        Me.btnRefresh.tbProgressValue = 50
        Me.btnRefresh.tbReadOnly = False
        Me.btnRefresh.tbReadOnlyText = False
        Me.btnRefresh.tbShadow = False
        Me.btnRefresh.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnRefresh.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnRefresh.tbShowDot = False
        Me.btnRefresh.tbShowMoreIconImg = CType(resources.GetObject("btnRefresh.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRefresh.tbShowNew = False
        Me.btnRefresh.tbShowProgress = False
        Me.btnRefresh.tbShowTip = True
        Me.btnRefresh.tbShowToolTipOnButton = False
        Me.btnRefresh.tbSplit = "0,0,0,0"
        Me.btnRefresh.tbText = "刷机"
        Me.btnRefresh.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.tbTextColor = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorDown = System.Drawing.Color.Black
        Me.btnRefresh.tbTextColorHover = System.Drawing.Color.Black
        Me.btnRefresh.tbTextMouseDownPlace = 0
        Me.btnRefresh.tbToolTip = ""
        Me.btnRefresh.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRefresh.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRefresh.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRefresh.VisibleEx = True
        '
        'btnDecollator
        '
        Me.btnDecollator.BackColor = System.Drawing.Color.Transparent
        Me.btnDecollator.BindingForm = Nothing
        Me.btnDecollator.Location = New System.Drawing.Point(319, 16)
        Me.btnDecollator.Name = "btnDecollator"
        Me.btnDecollator.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnDecollator.Selectable = True
        Me.btnDecollator.Size = New System.Drawing.Size(40, 3)
        Me.btnDecollator.TabIndex = 5
        Me.btnDecollator.tbAdriftIconWhenHover = False
        Me.btnDecollator.tbAutoSize = False
        Me.btnDecollator.tbAutoSizeEx = False
        Me.btnDecollator.tbBackgroundImage = Global.iTong.My.Resources.Resources.flash_decollator
        Me.btnDecollator.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnDecollator.tbBadgeNumber = 0
        Me.btnDecollator.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnDecollator.tbEndEllipsis = False
        Me.btnDecollator.tbIconHoldPlace = True
        Me.btnDecollator.tbIconImage = Nothing
        Me.btnDecollator.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDecollator.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnDecollator.tbIconMore = False
        Me.btnDecollator.tbIconMouseDown = Nothing
        Me.btnDecollator.tbIconMouseHover = Nothing
        Me.btnDecollator.tbIconMouseLeave = Nothing
        Me.btnDecollator.tbIconPlaceText = 2
        Me.btnDecollator.tbIconReadOnly = Nothing
        Me.btnDecollator.tbImageMouseDown = Nothing
        Me.btnDecollator.tbImageMouseHover = Nothing
        Me.btnDecollator.tbImageMouseLeave = Nothing
        Me.btnDecollator.tbProgressValue = 50
        Me.btnDecollator.tbReadOnly = False
        Me.btnDecollator.tbReadOnlyText = False
        Me.btnDecollator.tbShadow = False
        Me.btnDecollator.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnDecollator.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnDecollator.tbShowDot = False
        Me.btnDecollator.tbShowMoreIconImg = CType(resources.GetObject("btnDecollator.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnDecollator.tbShowNew = False
        Me.btnDecollator.tbShowProgress = False
        Me.btnDecollator.tbShowTip = True
        Me.btnDecollator.tbShowToolTipOnButton = False
        Me.btnDecollator.tbSplit = "1,1,1,1"
        Me.btnDecollator.tbText = ""
        Me.btnDecollator.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDecollator.tbTextColor = System.Drawing.Color.White
        Me.btnDecollator.tbTextColorDisable = System.Drawing.Color.White
        Me.btnDecollator.tbTextColorDown = System.Drawing.Color.White
        Me.btnDecollator.tbTextColorHover = System.Drawing.Color.White
        Me.btnDecollator.tbTextMouseDownPlace = 0
        Me.btnDecollator.tbToolTip = ""
        Me.btnDecollator.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnDecollator.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnDecollator.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnDecollator.VisibleEx = True
        '
        'btnSecond
        '
        Me.btnSecond.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnSecond.BackColor = System.Drawing.Color.Transparent
        Me.btnSecond.BindingForm = Nothing
        Me.btnSecond.Font = New System.Drawing.Font("宋体", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSecond.Location = New System.Drawing.Point(365, 3)
        Me.btnSecond.Name = "btnSecond"
        Me.btnSecond.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSecond.Selectable = True
        Me.btnSecond.Size = New System.Drawing.Size(92, 29)
        Me.btnSecond.TabIndex = 4
        Me.btnSecond.tbAdriftIconWhenHover = False
        Me.btnSecond.tbAutoSize = False
        Me.btnSecond.tbAutoSizeEx = True
        Me.btnSecond.tbBackgroundImage = Nothing
        Me.btnSecond.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSecond.tbBadgeNumber = 0
        Me.btnSecond.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSecond.tbEndEllipsis = False
        Me.btnSecond.tbIconHoldPlace = True
        Me.btnSecond.tbIconImage = Global.iTong.My.Resources.Resources.flash_second
        Me.btnSecond.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnSecond.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnSecond.tbIconMore = False
        Me.btnSecond.tbIconMouseDown = Nothing
        Me.btnSecond.tbIconMouseHover = Nothing
        Me.btnSecond.tbIconMouseLeave = Nothing
        Me.btnSecond.tbIconPlaceText = 2
        Me.btnSecond.tbIconReadOnly = Nothing
        Me.btnSecond.tbImageMouseDown = Nothing
        Me.btnSecond.tbImageMouseHover = Nothing
        Me.btnSecond.tbImageMouseLeave = Nothing
        Me.btnSecond.tbProgressValue = 50
        Me.btnSecond.tbReadOnly = False
        Me.btnSecond.tbReadOnlyText = False
        Me.btnSecond.tbShadow = False
        Me.btnSecond.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSecond.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSecond.tbShowDot = False
        Me.btnSecond.tbShowMoreIconImg = CType(resources.GetObject("btnSecond.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnSecond.tbShowNew = False
        Me.btnSecond.tbShowProgress = False
        Me.btnSecond.tbShowTip = True
        Me.btnSecond.tbShowToolTipOnButton = False
        Me.btnSecond.tbSplit = "17,0,2,0"
        Me.btnSecond.tbText = "执行刷机"
        Me.btnSecond.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSecond.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSecond.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSecond.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSecond.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnSecond.tbTextMouseDownPlace = 0
        Me.btnSecond.tbToolTip = ""
        Me.btnSecond.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSecond.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSecond.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSecond.VisibleEx = True
        '
        'btnFirst
        '
        Me.btnFirst.Anchor = System.Windows.Forms.AnchorStyles.Left
        Me.btnFirst.BackColor = System.Drawing.Color.Transparent
        Me.btnFirst.BindingForm = Nothing
        Me.btnFirst.Font = New System.Drawing.Font("宋体", 9.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFirst.Location = New System.Drawing.Point(219, 3)
        Me.btnFirst.Name = "btnFirst"
        Me.btnFirst.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnFirst.Selectable = True
        Me.btnFirst.Size = New System.Drawing.Size(92, 28)
        Me.btnFirst.TabIndex = 2
        Me.btnFirst.tbAdriftIconWhenHover = False
        Me.btnFirst.tbAutoSize = False
        Me.btnFirst.tbAutoSizeEx = True
        Me.btnFirst.tbBackgroundImage = Nothing
        Me.btnFirst.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnFirst.tbBadgeNumber = 0
        Me.btnFirst.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFirst.tbEndEllipsis = False
        Me.btnFirst.tbIconHoldPlace = True
        Me.btnFirst.tbIconImage = Global.iTong.My.Resources.Resources.flash_first
        Me.btnFirst.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnFirst.tbIconImageState = iTong.Components.ImageState.TwoState
        Me.btnFirst.tbIconMore = False
        Me.btnFirst.tbIconMouseDown = Nothing
        Me.btnFirst.tbIconMouseHover = Nothing
        Me.btnFirst.tbIconMouseLeave = Nothing
        Me.btnFirst.tbIconPlaceText = 2
        Me.btnFirst.tbIconReadOnly = Nothing
        Me.btnFirst.tbImageMouseDown = Nothing
        Me.btnFirst.tbImageMouseHover = Nothing
        Me.btnFirst.tbImageMouseLeave = Nothing
        Me.btnFirst.tbProgressValue = 50
        Me.btnFirst.tbReadOnly = False
        Me.btnFirst.tbReadOnlyText = False
        Me.btnFirst.tbShadow = False
        Me.btnFirst.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnFirst.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnFirst.tbShowDot = False
        Me.btnFirst.tbShowMoreIconImg = CType(resources.GetObject("btnFirst.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFirst.tbShowNew = False
        Me.btnFirst.tbShowProgress = False
        Me.btnFirst.tbShowTip = True
        Me.btnFirst.tbShowToolTipOnButton = False
        Me.btnFirst.tbSplit = "17,0,2,0"
        Me.btnFirst.tbText = "选择固件"
        Me.btnFirst.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFirst.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnFirst.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnFirst.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnFirst.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(15, Byte), Integer), CType(CType(149, Byte), Integer), CType(CType(216, Byte), Integer))
        Me.btnFirst.tbTextMouseDownPlace = 0
        Me.btnFirst.tbToolTip = ""
        Me.btnFirst.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFirst.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFirst.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFirst.VisibleEx = True
        '
        'pnlUnConnect
        '
        Me.pnlUnConnect.BackColor = System.Drawing.Color.FromArgb(CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlUnConnect.Controls.Add(Me.rtxtTitle)
        Me.pnlUnConnect.Controls.Add(Me.lblDevNoConn)
        Me.pnlUnConnect.Controls.Add(Me.picDefault)
        Me.pnlUnConnect.Controls.Add(Me.lblLinkMode)
        Me.pnlUnConnect.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlUnConnect.Location = New System.Drawing.Point(0, 0)
        Me.pnlUnConnect.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlUnConnect.Name = "pnlUnConnect"
        Me.tblayoutMain.SetRowSpan(Me.pnlUnConnect, 9)
        Me.pnlUnConnect.Size = New System.Drawing.Size(400, 645)
        Me.pnlUnConnect.TabIndex = 26
        '
        'rtxtTitle
        '
        Me.rtxtTitle.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.rtxtTitle.BackColor = System.Drawing.Color.FromArgb(CType(CType(244, Byte), Integer), CType(CType(245, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.rtxtTitle.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxtTitle.Font = New System.Drawing.Font("方正兰亭超细黑简体", 20.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.rtxtTitle.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.rtxtTitle.Location = New System.Drawing.Point(28, 506)
        Me.rtxtTitle.Multiline = False
        Me.rtxtTitle.Name = "rtxtTitle"
        Me.rtxtTitle.ReadOnly = True
        Me.rtxtTitle.ShowSelectionMargin = True
        Me.rtxtTitle.Size = New System.Drawing.Size(344, 38)
        Me.rtxtTitle.TabIndex = 10
        Me.rtxtTitle.Text = "自动匹配固件  一键智能刷机"
        '
        'lblDevNoConn
        '
        Me.lblDevNoConn.AutoSize = True
        Me.lblDevNoConn.Font = New System.Drawing.Font("宋体", 21.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDevNoConn.Location = New System.Drawing.Point(54, 50)
        Me.lblDevNoConn.Margin = New System.Windows.Forms.Padding(0)
        Me.lblDevNoConn.Name = "lblDevNoConn"
        Me.lblDevNoConn.Size = New System.Drawing.Size(158, 29)
        Me.lblDevNoConn.TabIndex = 7
        Me.lblDevNoConn.Text = "设备未连接"
        Me.lblDevNoConn.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'picDefault
        '
        Me.picDefault.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.picDefault.BackColor = System.Drawing.Color.Transparent
        Me.picDefault.Image = Global.iTong.My.Resources.Resources.new_device_model
        Me.picDefault.Location = New System.Drawing.Point(56, 207)
        Me.picDefault.Name = "picDefault"
        Me.picDefault.Size = New System.Drawing.Size(339, 112)
        Me.picDefault.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picDefault.TabIndex = 9
        Me.picDefault.TabStop = False
        '
        'lblLinkMode
        '
        Me.lblLinkMode.Font = New System.Drawing.Font("宋体", 10.5!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblLinkMode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(126, Byte), Integer), CType(CType(126, Byte), Integer), CType(CType(126, Byte), Integer))
        Me.lblLinkMode.Location = New System.Drawing.Point(56, 99)
        Me.lblLinkMode.Name = "lblLinkMode"
        Me.lblLinkMode.Size = New System.Drawing.Size(777, 23)
        Me.lblLinkMode.TabIndex = 8
        Me.lblLinkMode.Text = "支持iPhone/iPad，请用数据线将设备连接到电脑"
        Me.lblLinkMode.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'tblayoutMain
        '
        Me.tblayoutMain.BackColor = System.Drawing.Color.FromArgb(CType(CType(204, Byte), Integer), CType(CType(208, Byte), Integer), CType(CType(215, Byte), Integer))
        Me.tblayoutMain.ColumnCount = 4
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 400.0!))
        Me.tblayoutMain.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 1000.0!))
        Me.tblayoutMain.Controls.Add(Me.pnlFlashed, 3, 4)
        Me.tblayoutMain.Controls.Add(Me.pnlBottom, 1, 8)
        Me.tblayoutMain.Controls.Add(Me.pnlFlashing, 2, 4)
        Me.tblayoutMain.Controls.Add(Me.pnlStep, 1, 2)
        Me.tblayoutMain.Controls.Add(Me.pnlTop, 1, 0)
        Me.tblayoutMain.Controls.Add(Me.pnlUnConnect, 0, 0)
        Me.tblayoutMain.Controls.Add(Me.pnlFirmware, 1, 4)
        Me.tblayoutMain.Dock = System.Windows.Forms.DockStyle.Fill
        Me.tblayoutMain.Location = New System.Drawing.Point(0, 0)
        Me.tblayoutMain.Name = "tblayoutMain"
        Me.tblayoutMain.RowCount = 9
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 0.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 0.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 34.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 1.0!))
        Me.tblayoutMain.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 67.0!))
        Me.tblayoutMain.Size = New System.Drawing.Size(2127, 645)
        Me.tblayoutMain.TabIndex = 27
        '
        'pnlFirmware
        '
        Me.pnlFirmware.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pnlFirmware.Controls.Add(Me.pnlPrompt)
        Me.pnlFirmware.Dock = System.Windows.Forms.DockStyle.Fill
        Me.pnlFirmware.Location = New System.Drawing.Point(400, 35)
        Me.pnlFirmware.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFirmware.Name = "pnlFirmware"
        Me.tblayoutMain.SetRowSpan(Me.pnlFirmware, 3)
        Me.pnlFirmware.Size = New System.Drawing.Size(400, 542)
        Me.pnlFirmware.SizeMode = iTong.Components.tbPanel.tbSizeMode.StretchImage
        Me.pnlFirmware.TabIndex = 28
        Me.pnlFirmware.tbBackgroundImage = Nothing
        Me.pnlFirmware.tbShowWatermark = False
        Me.pnlFirmware.tbSplit = "100,0,10,0"
        Me.pnlFirmware.tbWatermark = Nothing
        Me.pnlFirmware.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlFirmware.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'pnlPrompt
        '
        Me.pnlPrompt.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(226, Byte), Integer), CType(CType(131, Byte), Integer))
        Me.pnlPrompt.Controls.Add(Me.lblPrompt)
        Me.pnlPrompt.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.pnlPrompt.Location = New System.Drawing.Point(0, 518)
        Me.pnlPrompt.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlPrompt.Name = "pnlPrompt"
        Me.pnlPrompt.Size = New System.Drawing.Size(400, 24)
        Me.pnlPrompt.TabIndex = 0
        '
        'lblPrompt
        '
        Me.lblPrompt.AutoSize = True
        Me.lblPrompt.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer), CType(CType(34, Byte), Integer))
        Me.lblPrompt.Location = New System.Drawing.Point(15, 4)
        Me.lblPrompt.Name = "lblPrompt"
        Me.lblPrompt.Size = New System.Drawing.Size(401, 12)
        Me.lblPrompt.TabIndex = 0
        Me.lblPrompt.Text = "提示：带有运营商设备锁的有锁设备因其特殊性，尚未兼容，请谨慎操作！"
        Me.lblPrompt.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'frmFlash
        '
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.BackColor = System.Drawing.Color.White
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(2127, 645)
        Me.Controls.Add(Me.tblayoutMain)
        Me.Name = "frmFlash"
        Me.tbShowIconOnForm = False
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "frmFlash"
        Me.Controls.SetChildIndex(Me.tblayoutMain, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.pnlBottom.ResumeLayout(False)
        Me.pnlFlashing.ResumeLayout(False)
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlFlashed.ResumeLayout(False)
        Me.pnlFlashed.PerformLayout()
        Me.pnlFlashFailure.ResumeLayout(False)
        Me.pnlFlashFailure.PerformLayout()
        CType(Me.picFileEmpty, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ptbFlashSucceed, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlTop.ResumeLayout(False)
        Me.pnlStep.ResumeLayout(False)
        Me.pnlUnConnect.ResumeLayout(False)
        Me.pnlUnConnect.PerformLayout()
        CType(Me.picDefault, System.ComponentModel.ISupportInitialize).EndInit()
        Me.tblayoutMain.ResumeLayout(False)
        Me.pnlFirmware.ResumeLayout(False)
        Me.pnlPrompt.ResumeLayout(False)
        Me.pnlPrompt.PerformLayout()
        Me.ResumeLayout(False)

    End Sub

    Friend WithEvents cbxRetainData As iTong.Components.tbCheckBox
    Friend WithEvents cbxActivate As iTong.Components.tbCheckBox
    Friend WithEvents btnStartFlash As iTong.Components.tbButton
    Friend WithEvents pnlUnConnect As System.Windows.Forms.Panel
    Friend WithEvents lblDevNoConn As System.Windows.Forms.Label
    Friend WithEvents picDefault As System.Windows.Forms.PictureBox
    Friend WithEvents lblLinkMode As System.Windows.Forms.Label
    Friend WithEvents pnlStep As iTong.Components.tbPanel
    Friend WithEvents tipDgvCell As System.Windows.Forms.ToolTip
    Friend WithEvents lblFlashWaitingMsg As System.Windows.Forms.Label
    Friend WithEvents lblFlashWaiting As System.Windows.Forms.Label
    Friend WithEvents lblProgress As System.Windows.Forms.Label
    Friend WithEvents pnlFlashing As System.Windows.Forms.Panel
    Friend WithEvents pnlFlashed As System.Windows.Forms.Panel
    Friend WithEvents picFileEmpty As System.Windows.Forms.PictureBox
    Friend WithEvents lblFileEmpty As iTong.Components.tbLabel
    Friend WithEvents btnReturn As iTong.Components.tbButton
    Friend WithEvents pnlTop As System.Windows.Forms.Panel
    Friend WithEvents btnImport As iTong.Components.tbButton
    Friend WithEvents pnlBottom As System.Windows.Forms.Panel
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents lblPercentage As System.Windows.Forms.Label
    Friend WithEvents ptbFlashSucceed As iTong.Components.tbPictureBox
    Friend WithEvents pnlFlashFailure As System.Windows.Forms.Panel
    Friend WithEvents lblBBSUrl As System.Windows.Forms.Label
    Friend WithEvents tblayoutMain As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents btnFirst As iTong.Components.tbButton
    Friend WithEvents Label1 As System.Windows.Forms.Label
    Friend WithEvents lblErrorCode As System.Windows.Forms.Label
    Friend WithEvents lklblQQ As System.Windows.Forms.LinkLabel
    Friend WithEvents lklblBBSUrl As System.Windows.Forms.LinkLabel
    Friend WithEvents pnlFirmware As iTong.Components.tbPanel
    Friend WithEvents lblQQ As System.Windows.Forms.Label
    Friend WithEvents btnBackup As iTong.Components.tbButton
    Friend WithEvents btnSecond As iTong.Components.tbButton
    Friend WithEvents btnDecollator As iTong.Components.tbButton
    Friend WithEvents rtxtTitle As System.Windows.Forms.RichTextBox
    Friend WithEvents lblEMsg As System.Windows.Forms.Label
    Friend WithEvents lblECode As System.Windows.Forms.Label
    Friend WithEvents lblErrorMsg As System.Windows.Forms.Label
    Friend WithEvents pnlPrompt As System.Windows.Forms.Panel
    Friend WithEvents lblPrompt As System.Windows.Forms.Label
    Friend WithEvents btnActivate As iTong.Components.tbButton
    Friend WithEvents btnRetry As iTong.Components.tbButton
    Friend WithEvents btnRefresh As iTong.Components.tbButton
End Class
