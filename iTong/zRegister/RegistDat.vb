﻿Imports System.Collections.Generic
Imports System.Text
Imports System.Security.Cryptography
Imports System.IO

Public Class RegistDat

    Private Shared mDatFile As String = String.Empty
    Shared Sub New()
        Dim strDic As String = Path.Combine(System.Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "iClover")

        Folder.CheckFolder(strDic)
        mDatFile = Path.Combine(strDic, "Info.dat")
    End Sub

    Public Shared Sub SetCreateDate(ByVal dtStart As DateTime)
        IniClass.SetIniSectionKey("Regist", "CreateDate", dtStart.ToString("yyyy-MM-dd HH:mm:ss"), mDatFile)
    End Sub

    Public Shared Function GetCreateDate() As DateTime
        Dim dt As Date = Now

        Try
            Dim strDate As String = IniClass.GetIniSectionKey("Regist", "CreateDate", mDatFile)
            DateTime.TryParse(strDate, dt)
        Catch ex As Exception
        End Try

        Return dt
    End Function

    Public Shared Sub SetSoftVersion(ByVal ver As Version)
        IniClass.SetIniSectionKey("Regist", "Version", ver.ToString(4), mDatFile)
    End Sub

    Public Shared Function GetSoftVersion() As Version
        Dim ver As New Version("1.0.0.0")

        Try
            Dim strVer As String = IniClass.GetIniSectionKey("Regist", "Version", mDatFile)
            If Not String.IsNullOrEmpty(strVer) Then
                ver = New Version(strVer)
            End If
        Catch ex As Exception
        End Try

        Return ver
    End Function

End Class
