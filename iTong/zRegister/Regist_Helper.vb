﻿Public Class RegistHelper

    Public Shared ReadOnly Property ExpireDate() As Date
        Get
            Return Date.Parse("2013-08-31 23:59:59")
        End Get
    End Property

    Public Shared ReadOnly Property ExpireDateString() As String
        Get
            Return ExpireDate.ToString("yyyy-MM-dd")
        End Get
    End Property

    Public Shared Function CheckExpire() As Boolean
        If Not Folder.LangType = LanguageType.en_US Then
            Return False
        End If

        Dim blnExpire As Boolean = False

        Dim dtFree As Date = ExpireDate
        Dim dtNow As Date = DateTime.Now
        Dim dtDat As Date = RegistDat.GetCreateDate()
        Dim dtWin32 As Date = RegistWin32.GetCreateDate()


        If dtWin32 > dtFree Then
            blnExpire = True
            GoTo DO_EIXT
        End If

        If dtDat > dtFree Then
            blnExpire = True
            GoTo DO_EIXT
        End If

        If dtNow > dtFree Then
            blnExpire = True

            RegistDat.SetCreateDate(dtNow)
            RegistWin32.SetCreateDate(dtNow)

            GoTo DO_EIXT
        End If

        Dim thd As New System.Threading.Thread(AddressOf InternetTimeThread)
        thd.IsBackground = True
        thd.Start()

DO_EIXT:
        Return blnExpire
    End Function

    Private Shared Sub InternetTimeThread(ByVal state As Object)
        Try
            Dim dtNow As Date = Common.GetInternetTime()
            Dim dtFree As Date = ExpireDate
            If dtNow > dtFree Then
                RegistDat.SetCreateDate(dtNow)
                RegistWin32.SetCreateDate(dtNow)
            End If
        Catch ex As Exception
        End Try
    End Sub

End Class
