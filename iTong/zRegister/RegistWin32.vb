﻿Imports System.Collections.Generic
Imports System.Text
Imports System.Security.Cryptography
Imports System.IO
Imports Microsoft.Win32

Public Class RegistWin32

    Public Shared Sub SetCreateDate(ByVal dtStart As DateTime)
        Dim hkml As RegistryKey = Registry.CurrentUser
        Dim tongbuKey As RegistryKey = Registry.CurrentUser.OpenSubKey("Software\iClover\", True)
        If tongbuKey Is Nothing Then
            tongbuKey = Registry.CurrentUser.CreateSubKey("Software\iClover")
        End If

        tongbuKey.SetValue("CreateDate", dtStart.ToString("yyyy-MM-dd HH:mm:ss"))
    End Sub

    Public Shared Function GetCreateDate() As DateTime
        Dim dt As Date = Now

        Try
            Dim hkml As RegistryKey = Registry.CurrentUser
            Dim tongbuKey As RegistryKey = Registry.CurrentUser.OpenSubKey("Software\iClover\", True)
            If tongbuKey IsNot Nothing Then
                DateTime.TryParse(tongbuKey.GetValue("CreateDate").ToString(), dt)
            End If
        Catch ex As Exception
        End Try

        Return dt
    End Function

    Public Shared Sub SetSoftVersion(ByVal ver As Version)
        Dim hkml As RegistryKey = Registry.CurrentUser
        Dim tongbuKey As RegistryKey = Registry.CurrentUser.OpenSubKey("Software\iClover\", True)
        If tongbuKey Is Nothing Then
            tongbuKey = Registry.CurrentUser.CreateSubKey("Software\iClover")
        End If

        tongbuKey.SetValue("Version", ver.ToString(4))
    End Sub

    Public Shared Function GetSoftVersion() As Version
        Dim ver As New Version("1.0.0.0")

        Try
            Dim hkml As RegistryKey = Registry.CurrentUser
            Dim tongbuKey As RegistryKey = Registry.CurrentUser.OpenSubKey("Software\iClover\", True)
            If tongbuKey IsNot Nothing Then
                ver = New Version(tongbuKey.GetValue("Version").ToString())
            End If
        Catch ex As Exception
        End Try

        Return ver
    End Function

End Class
