﻿'收费模块
Imports System.Threading

Public Class ChargeHelper

    Private Shared mChargeHelper As ChargeHelper = Nothing
    Private Shared mChargeObj As New ChargeInfo                       '与服务器交互对像
    Private Shared mPayVersions As PayVersions
    Private mThrLoadInfoFromServer As Thread
    Private mThrRegistration As Thread
    Private mThrRegistrDevice As Thread
    Private mThrSendDataToServer As Thread
    Private Shared mUrl As String = ""
    Public Event RegistrCompleted As EventHandler(Of RegistrCompletedEventArgs)
    Protected Language As LanguageInterface = Nothing

    Public Event WeChatMigrationChargeHandler As EventHandler(Of EventArgs)

    Private mIsiWeChatMigrationActivate As Boolean = False

    Public Property mChargeInfo() As ChargeInfo
        Get
            Return mChargeObj
        End Get
        Set(value As ChargeInfo)
            mChargeObj = value
        End Set
    End Property

    Public Shared IsChargeGeneral As Boolean = False

    Public Shared IsChargeIOSWechat As Boolean = False

    Public Shared IsChargeAndroidWechat As Boolean = False

    Private Shared mLocker As New Object
    Private Shared mDictInstances As New Dictionary(Of String, ChargeHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Public Shared Function Instance(Optional valus As PayVersions = PayVersions.None) As ChargeHelper
        Dim helper As ChargeHelper = Nothing
        Dim blnRetry As Boolean = False

        Try
#If IS_WECHAT Then
            If Folder.AppType = RunType.iDataRecovery Then
                '数据滚回来
                mPayVersions = PayVersions.iDataRecovery
                IsChargeGeneral = True
            ElseIf Folder.AppType = RunType.iDataRecovery_Abroad Then
                '数据滚回来
                mPayVersions = PayVersions.iDataRecovery_Abroad
                IsChargeGeneral = True

            ElseIf Folder.AppType = RunType.iWeChatMigration Then
                mPayVersions = PayVersions.iWeChatMigration
                IsChargeGeneral = True
            Else
                'win微信管理大师
                mPayVersions = PayVersions.WinWeChatManage
                IsChargeGeneral = True
            End If
#Else
            If Folder.AppType = RunType.Tongbu_Abroad Then
                '海外助手 
                mPayVersions = PayVersions.AbroadAssistant
                IsChargeGeneral = True

            ElseIf Folder.AppType = RunType.iDataRecovery Then
                '数据滚回来
                mPayVersions = PayVersions.iDataRecovery
                IsChargeGeneral = True
            ElseIf Folder.AppType = RunType.iDataRecovery_Abroad Then
                '数据滚回来
                mPayVersions = PayVersions.iDataRecovery_Abroad
                IsChargeGeneral = True

            ElseIf Folder.AppType = RunType.Tongbu Then
                If valus = PayVersions.iTongWeChatAssistant Then
                    If ServerIniSetting.GetiTongWeChatAssistant() OrElse ServerIniSetting.GetiTongWeChatAssistantChannel().Contains(Common.GetChannelID() & ";") OrElse Common.IstbWechatHelper() Then
                        mPayVersions = PayVersions.iTongWeChatAssistant
                        IsChargeIOSWechat = True

                    End If

                ElseIf valus = PayVersions.iTongAndroidMedia Then
                    mPayVersions = PayVersions.iTongAndroidMedia
                    IsChargeAndroidWechat = True

                End If
            End If

            Common.LogException(mPayVersions.ToString(), "ChargeHelper_Instance____1")
#End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_Instance")
        End Try

        SyncLock mLocker
DO_RETRY:
            Try
                If mPayVersions = PayVersions.None Then
                    If Not mDictInstances.ContainsKey(mPayVersions) Then
                        mDictInstances.Add(mPayVersions, New ChargeHelper())
                    End If
                    helper = mDictInstances.Item(PayVersions.None)

                Else
                    If Not mDictInstances.ContainsKey(mPayVersions) Then
                        mDictInstances.Add(mPayVersions, New ChargeHelper())
                    End If

                    helper = mDictInstances.Item(mPayVersions)
                End If

            Catch ex As Exception
                If Not blnRetry Then
                    blnRetry = True
                    GoTo DO_RETRY
                End If
            End Try
        End SyncLock

        Return helper

    End Function

    Public Shared mWCMCInfo As WeChatMigrationCharge = Nothing

    Sub New()

        Dim cultureInfo As System.Globalization.CultureInfo = System.Globalization.CultureInfo.CurrentCulture
        Dim langName As String = IniSetting.GetLanguage()
        If langName.Length > 0 Then
            Try
                cultureInfo = New System.Globalization.CultureInfo(langName)
            Catch ex As Exception
            End Try
        End If
        Me.Language = LanguageInterface.Instance(cultureInfo)

        Select Case mPayVersions
            Case PayVersions.AbroadAssistant
                mUrl = WebUrl.ServerAbroadCharge

            Case PayVersions.iDataRecovery, PayVersions.iTongAndroidMedia, PayVersions.iTongWeChatAssistant, PayVersions.iWeChatMigration
                mUrl = WebUrl.ServeriTongWeChatCharge
            Case PayVersions.iDataRecovery_Abroad
                mUrl = WebUrl.ServerAbroadCharge
            Case PayVersions.WinWeChatManage
                mUrl = WebUrl.ServerWechatCharge
        End Select
        mChargeObj.PayVersion = mPayVersions

    End Sub

#Region "从服务器加载电脑信息"

    Public Sub LoadInfoFromServer()

        If Not Me.GetIsCharge() Then
            Return
        End If
        Try
            If Me.mThrLoadInfoFromServer IsNot Nothing AndAlso Me.mThrLoadInfoFromServer.ThreadState <> ThreadState.Stopped Then
                Me.mThrLoadInfoFromServer.Abort()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_LoadInfoFromServer")
        End Try

        Me.mThrLoadInfoFromServer = New Thread(AddressOf LoadInfoFromServerThread)
        With Me.mThrLoadInfoFromServer
            .IsBackground = True
            .Start()
        End With

        Dim intCount As Integer = 0
        While Me.mThrLoadInfoFromServer.ThreadState <> ThreadState.Stopped
            Utility.WaitSeconds(0.5)
            intCount += 1
            If intCount > 40 Then
                Exit While
            End If
        End While
    End Sub

    Private Sub LoadInfoFromServerThread()
        Try
            Dim strUrl As String = ""
            Select Case mPayVersions
                Case PayVersions.AbroadAssistant
                    strUrl = String.Format(mUrl, "44")

                Case PayVersions.iWeChatMigration
                    strUrl = String.Format(mUrl, "74", "7")

                Case PayVersions.iDataRecovery
                    strUrl = String.Format(mUrl, "64", "5")

                Case PayVersions.iDataRecovery_Abroad
                    strUrl = String.Format(mUrl, "64") & "&projectid=10005"

                Case PayVersions.iTongAndroidMedia
                    strUrl = String.Format(mUrl, "64", "4")

                Case PayVersions.iTongWeChatAssistant
                    strUrl = String.Format(mUrl, "64", "3")

                Case PayVersions.WinWeChatManage
                    strUrl = String.Format(mUrl, "34")

            End Select

            Dim strContact As String = AESFunctionHelper.PostDataToUrl(Me.GetComputerInfoJson(), strUrl)
            Dim blnResult As Boolean = Me.GetChargeObjectFromContact(strContact)
            Me.PrintRegisterLog("LoadRegisterDevFromServer")
            Me.GetRegisteredInfoFromFile(strContact, blnResult)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_LoadInfoFromServerThread")
        End Try
    End Sub

    Private Sub GetRegisteredInfoFromFile(ByVal strContact As String, ByVal blnResult As Boolean)
        '把注册成功信息存到本地，如果没网络可以从本地取得相关数据
        Try
            If blnResult AndAlso ChargeHelper.mChargeObj.Registered Then
                Common.LogException("TRUE", "--->>>服务器获取注册信息")
                File.WriteAllText(Me.GetRegisteredInfoPath(), strContact)
            ElseIf Not blnResult AndAlso Me.GetRegisteredLocalFile Then
                Common.LogException("FALSE", "--->>>服务器获取注册信息")
                '服务器加载失败就从本地加载数据， 加载失败后可以取本地最后一次成功的缓存
                strContact = File.ReadAllText(Me.GetRegisteredInfoPath())
                If Not String.IsNullOrEmpty(strContact) Then
                    Me.GetChargeObjectFromContact(strContact)
                End If
            Else
                Common.LogException(String.Format("LoadSucceed: {0},Registered: {1}", blnResult, ChargeHelper.mChargeObj.Registered), "--->>>服务器获取注册信息")
                Common.LogException("尚未激活 OR " & ChargeHelper.mChargeObj.ServerCType.ToString(), "--->>>服务器获取注册信息")
            End If

            '加载完注册信息后从本地加载注册设备的信息
            If ChargeHelper.mChargeObj.Registered OrElse ChargeHelper.mChargeObj.ServerCType = ServerCodeType.ParameterError OrElse ChargeHelper.mChargeObj.ServerCType = ServerCodeType.ServerException Then
                Me.GetDeviceRegistedLocal()
            End If

            '开个线程单独提交本地注册信息到服务器
            Me.RegistDevice()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetChargeObjectFromContact_SaveFile")
        End Try
    End Sub

    Private Function GetRegisteredLocalFile() As Boolean
        If Not File.Exists(Me.GetRegisteredInfoPath()) Then
            Return False
        End If
        Try
            '最后一次加载注册信息成功后保留三天时间。超过三天就不处理
            Dim info As New FileInfo(Me.GetRegisteredInfoPath())
            If info.LastWriteTime.AddDays(10) < Date.Now Then
                Return False
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetRegisteredLocalFile")
        End Try
        Return True
    End Function

    Private Function GetComputerInfoJson()
        Dim strReturn As String = ""
        Try
            Dim jObject As JsonObject = New JsonObject()
            '#If IS_WECHAT Then
            '            jObject.Add("projectid", "2")
            '            jObject.Add("isWin", "1")
            '#Else
            '            jObject.Add("projectid", "10000")
            '#End If

            If mChargeInfo.PayVersion = PayVersions.AbroadAssistant Then
                jObject.Add("projectid", "10000")

            ElseIf mChargeInfo.PayVersion = PayVersions.iTongWeChatAssistant Then
                jObject.Add("projectid", "3")

            ElseIf mChargeInfo.PayVersion = PayVersions.iWeChatMigration Then
                jObject.Add("projectid", "6")

            ElseIf mChargeInfo.PayVersion = PayVersions.iTongAndroidMedia Then
                jObject.Add("projectid", "4")

            ElseIf mChargeInfo.PayVersion = PayVersions.iDataRecovery Then
                jObject.Add("projectid", "5")

            ElseIf mChargeInfo.PayVersion = PayVersions.iDataRecovery_Abroad Then
                jObject.Add("projectid", "10005")

            ElseIf mChargeInfo.PayVersion = PayVersions.WinWeChatManage Then
                jObject.Add("projectid", "2")
                jObject.Add("isWin", "1")

            ElseIf mChargeInfo.PayVersion = PayVersions.iWeChatMigration Then
                jObject.Add("projectid", "7")

            End If

            jObject.Add("macid", Common.GetMacid())
            jObject.Add("macidnew", Common.GetMacidNew())
            jObject.Add("macudid", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddress().Trim()))
            jObject.Add("macudidnew", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddressFixed().Trim()))
            jObject.Add("sn", Common.GetHardDiskID2())
            jObject.Add("version", My.Application.Info.Version.ToString(4))
            jObject.Add("channel", Common.GetChannelID)
            Dim writer As JsonWriter = New JsonWriter()
            jObject.Write(writer)
            strReturn = writer.ToString()
            strReturn = Me.EncryptDES(strReturn)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetComputerInfoJson")
        End Try
        Return strReturn
    End Function

    Private Function GetChargeObjectFromContact(ByVal strContact As String) As Boolean
        Dim blnReturn As Boolean = False
        Dim strContactDecode As String = Me.DecryptDES(strContact)

        Dim intVipType As Integer = 0
        If ChargeHelper.mChargeObj IsNot Nothing Then
            intVipType = ChargeHelper.mChargeObj.Viptype
        End If

        ChargeHelper.mChargeObj = New ChargeInfo
        'ChargeHelper.mChargeObj.IsAbroad = True
        ChargeHelper.mChargeObj.Loaded = False
        ChargeHelper.mChargeObj.Registered = False
        ChargeHelper.mChargeObj.PayVersion = mPayVersions
        ChargeHelper.mChargeObj.Viptype = intVipType
        Try
            Dim jObj As JsonObject = JsonParser.ParseString(strContactDecode)
            If jObj Is Nothing Then
                Me.GetExceptCountFromSetting()
                Common.LogException("Obj is null!", "--->>>返回异常6")
                Return blnReturn
            End If

            If jObj.ContainsKey("state") AndAlso TypeOf jObj("state") Is JsonString Then
                Dim para As JsonString = CType(jObj("state"), JsonString)
                If Not para.Value = "1" Then
                    Me.GetExceptCountFromSetting()
                    Common.LogException("state is null!", "--->>>返回异常7")
                    Return blnReturn
                End If
            End If

            Dim dataObject As JsonObject = Nothing
            If jObj.ContainsKey("data") AndAlso TypeOf jObj("data") Is JsonObject Then
                dataObject = jObj("data")
            Else
                Me.GetExceptCountFromSetting()
                Common.LogException("data is null!", "--->>>返回异常8")
                Return blnReturn
            End If

            If dataObject.ContainsKey("code") AndAlso TypeOf dataObject("code") Is JsonNumber AndAlso _
               dataObject.ContainsKey("msg") AndAlso TypeOf dataObject("msg") Is JsonString Then
                Dim jCode As JsonNumber = CType(dataObject("code"), JsonNumber)
                Dim jMsg As JsonString = CType(dataObject("msg"), JsonString)     '返回macid

                ChargeHelper.mChargeObj.Loaded = True
                If jCode.Value = "1" AndAlso (jMsg = Common.GetMacid() OrElse jMsg = Common.GetMacidNew()) Then
                    ChargeHelper.mChargeObj.Registered = True
                    Common.LogException(String.Format("{0}-{1} Mac:{2} MacNew{3}", jCode, jMsg, Common.GetMacid(), Common.GetMacidNew()), "--->>>返回异常5")

                ElseIf jCode.Value = "2" Then
                    ChargeHelper.mChargeObj.Registered = False
                    Common.LogException(String.Format("{0}-{1}", jCode, jMsg), "--->>>返回异常4")

                ElseIf jCode.Value = "3" Then
                    ChargeHelper.mChargeObj.Registered = False
                    ChargeHelper.mChargeObj.ServerCType = ServerCodeType.LowVersion
                    Common.LogException(String.Format("{0}-{1}", jCode, jMsg), "--->>>返回异常1")

                ElseIf jCode.Value = "-1" Then
                    ChargeHelper.mChargeObj.Registered = False
                    ChargeHelper.mChargeObj.ServerCType = ServerCodeType.ParameterError
                    Common.LogException(String.Format("{0}-{1}", jCode, jMsg), "--->>>返回异常2")


                ElseIf jCode.Value = "-2" Then
                    ChargeHelper.mChargeObj.Registered = False
                    ChargeHelper.mChargeObj.ServerCType = ServerCodeType.ServerException
                    Common.LogException(String.Format("{0}-{1}", jCode, jMsg), "--->>>返回异常3")

                Else
                    ChargeHelper.mChargeObj.Registered = False
                    Common.LogException(String.Format("{0}-{1}", jCode, jMsg), "--->>>返回异常")
                End If
            End If

            If dataObject.ContainsKey("today") AndAlso TypeOf dataObject("today") Is JsonString Then
                Dim jPara As JsonString = CType(dataObject("today"), JsonString)
                Try
                    ChargeHelper.mChargeObj.Today = CType(jPara.Value, DateTime)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "ChargeHelper_GetTodayError")
                End Try
            End If

            If dataObject.ContainsKey("endtime") AndAlso TypeOf dataObject("endtime") Is JsonString Then
                Dim jPara As JsonString = CType(dataObject("endtime"), JsonString)
                Try
                    ChargeHelper.mChargeObj.EndTime = CType(jPara.Value, DateTime)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "ChargeHelper_GetEndtimeError")
                End Try
            End If

            If dataObject.ContainsKey("projectid") AndAlso TypeOf dataObject("projectid") Is JsonNumber Then
                Dim jPara As JsonNumber = CType(dataObject("projectid"), JsonNumber)
                ChargeHelper.mChargeObj.Projectid = jPara.Value
            End If

            If dataObject.ContainsKey("macid") AndAlso TypeOf dataObject("projectid") Is JsonNumber Then
                Dim jPara As JsonNumber = CType(dataObject("projectid"), JsonNumber)
                ChargeHelper.mChargeObj.Projectid = jPara.Value
            End If

            '------------------------------
            If dataObject.ContainsKey("mediacount") AndAlso TypeOf dataObject("mediacount") Is JsonNumber Then
                Dim jPara As JsonNumber = CType(dataObject("mediacount"), JsonNumber)
                ChargeHelper.mChargeObj.CanExportMedias = jPara.Value
            End If

            If dataObject.ContainsKey("photocount") AndAlso TypeOf dataObject("photocount") Is JsonNumber Then
                Dim jPara As JsonNumber = CType(dataObject("photocount"), JsonNumber)
                ChargeHelper.mChargeObj.CanExportPhotos = jPara.Value
            End If

            If dataObject.ContainsKey("contactscount") AndAlso TypeOf dataObject("contactscount") Is JsonNumber Then
                Dim jPara As JsonNumber = CType(dataObject("contactscount"), JsonNumber)
                ChargeHelper.mChargeObj.CanExportContacts = jPara.Value
            End If

            If dataObject.ContainsKey("smscount") AndAlso TypeOf dataObject("smscount") Is JsonNumber Then
                Dim jPara As JsonNumber = CType(dataObject("smscount"), JsonNumber)
                ChargeHelper.mChargeObj.CanExportSMS = jPara.Value
                Me.SetExceptCountToSetting()
            End If

            '剩余可绑定的设备数量
            If dataObject.ContainsKey("isfindwechatdata") AndAlso TypeOf dataObject("isfindwechatdata") Is JsonNumber Then
                Dim jPara As JsonNumber = CType(dataObject("isfindwechatdata"), JsonNumber)
                ChargeHelper.mChargeObj.Isfindwechatdata = IIf(jPara.Value = 0, False, True)
            End If

            If dataObject.ContainsKey("remainDevices") AndAlso TypeOf dataObject("remainDevices") Is JsonNumber Then
                Dim jPara As JsonNumber = CType(dataObject("remainDevices"), JsonNumber)
                ChargeHelper.mChargeObj.CanBindingDevCount = jPara.Value
            End If
            Common.LogException(String.Format("Helper剩余可绑定的设备数量:{0} 激活：{1}", ChargeHelper.mChargeObj.CanBindingDevCount, ChargeHelper.mChargeObj.Registered))
            '取得绑定的设备信息
            If dataObject.ContainsKey("lstDevices") AndAlso TypeOf dataObject("lstDevices") Is JsonArray Then
                Dim arrDevices As JsonArray = dataObject("lstDevices")
                For Each jDevice As JsonObject In arrDevices
                    Dim strUdid As String = String.Empty
                    Dim strName As String = String.Empty
                    If jDevice.ContainsKey("udid") AndAlso TypeOf jDevice("udid") Is JsonString Then
                        strUdid = CType(jDevice("udid"), JsonString).Value
                    End If
                    If jDevice.ContainsKey("name") AndAlso TypeOf jDevice("name") Is JsonString Then
                        strName = CType(jDevice("name"), JsonString).Value
                    End If
                    If Not String.IsNullOrEmpty(strUdid) AndAlso Not ChargeHelper.mChargeObj.BindingDevices.ContainsKey(strUdid) Then
                        ChargeHelper.mChargeObj.BindingDevices.Add(strUdid, strName)
                        Common.LogException(String.Format("Helper绑定的设备信息:{0}【{1}】", strUdid, strName))

                    End If
                Next
            End If
            Common.LogException(String.Format("Helper已绑定的设备数:{0}", ChargeHelper.mChargeObj.BindingDevices.Count))

            '------------------------------
            If dataObject.ContainsKey("datas") AndAlso TypeOf dataObject("datas") Is JsonArray Then

                Dim arrD As JsonArray = dataObject("datas") 'CType(dataObject("datas"), JsonArray)
                If arrD IsNot Nothing AndAlso arrD.Count > 0 Then
                    Dim LstWeChatMigrationCharge As New List(Of WeChatMigrationCharge)
                    Dim item As WeChatMigrationCharge = Nothing
                    For Each joComputer As JsonObject In arrD
                        item = New WeChatMigrationCharge

                        If joComputer.ContainsKey("id") AndAlso TypeOf joComputer("id") Is JsonNumber Then
                            Dim jPara As JsonNumber = CType(joComputer("id"), JsonNumber)
                            item.ID = jPara.Value
                        End If

                        If joComputer.ContainsKey("name") AndAlso TypeOf joComputer("name") Is JsonString Then
                            Dim jPara As JsonString = CType(joComputer("name"), JsonString)
                            item.OldName = jPara.Value
                        End If

                        If joComputer.ContainsKey("weixin") AndAlso TypeOf joComputer("weixin") Is JsonString Then
                            Dim jPara As JsonString = CType(joComputer("weixin"), JsonString)
                            item.OldSku = jPara.Value
                        End If

                        If joComputer.ContainsKey("udid") AndAlso TypeOf joComputer("udid") Is JsonString Then
                            Dim jPara As JsonString = CType(joComputer("udid"), JsonString)
                            item.OldUDID = jPara.Value
                        End If

                        If joComputer.ContainsKey("desname") AndAlso TypeOf joComputer("desname") Is JsonString Then
                            Dim jPara As JsonString = CType(joComputer("desname"), JsonString)
                            item.NewName = jPara.Value
                        End If

                        If joComputer.ContainsKey("desweixin") AndAlso TypeOf joComputer("desweixin") Is JsonString Then
                            Dim jPara As JsonString = CType(joComputer("desweixin"), JsonString)
                            item.NewSku = jPara.Value
                        End If

                        If joComputer.ContainsKey("desudid") AndAlso TypeOf joComputer("desudid") Is JsonString Then
                            Dim jPara As JsonString = CType(joComputer("desudid"), JsonString)
                            item.NewUDID = jPara.Value
                        End If

                        If joComputer.ContainsKey("endtime") AndAlso TypeOf joComputer("endtime") Is JsonString Then
                            Dim jPara As JsonString = CType(joComputer("endtime"), JsonString)
                            Try
                                item.Endtime = CType(jPara.Value, DateTime)
                            Catch
                            End Try
                        End If

                        If joComputer.ContainsKey("code") AndAlso TypeOf joComputer("code") Is JsonNumber Then
                            Dim jPara As JsonNumber = CType(joComputer("code"), JsonNumber)
                            item.IntCode = jPara.Value
                        End If

                        LstWeChatMigrationCharge.Add(item)
                    Next
                    ChargeHelper.mChargeObj.LstWeChatMigrationCharge = LstWeChatMigrationCharge
                    RaiseEvent WeChatMigrationChargeHandler(Me, New EventArgs())
                End If
            End If
            blnReturn = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetChargeObjectFromContact")
        End Try
        Return blnReturn
    End Function

    Private Sub SetExceptCountToSetting()
        IniSetting.SetExportCount(ChargeHelper.mChargeObj.CanExportMedias, ChargeHelper.mChargeObj.CanExportPhotos, ChargeHelper.mChargeObj.CanExportContacts, ChargeHelper.mChargeObj.CanExportSMS)
    End Sub

    Private Sub GetExceptCountFromSetting()
        ChargeHelper.mChargeObj.EndTime = IniSetting.GetRegisteredDate()
        '没有过期的注册信息是可用的
        If ChargeHelper.mChargeObj.EndTime <> DateTime.MinValue AndAlso Not ChargeHelper.mChargeObj.Overdue Then
            ChargeHelper.mChargeObj.Registered = True
        End If

        ChargeHelper.mChargeObj.CanExportMedias = IniSetting.GetCanExportMedia()
        ChargeHelper.mChargeObj.CanExportPhotos = IniSetting.GetCanExportPhoto()
        ChargeHelper.mChargeObj.CanExportContacts = IniSetting.GetCanExportContact()
        ChargeHelper.mChargeObj.CanExportSMS = IniSetting.GetCanExportSMS()
    End Sub

    Private Function GetIsCharge() As Boolean
        Dim isRelust As Boolean = False
        Try
            If mPayVersions = PayVersions.AbroadAssistant OrElse mPayVersions = PayVersions.iDataRecovery OrElse mPayVersions = PayVersions.iDataRecovery_Abroad OrElse mPayVersions = PayVersions.WinWeChatManage OrElse mPayVersions = PayVersions.iWeChatMigration Then
                isRelust = IsChargeGeneral

            ElseIf mPayVersions = PayVersions.iTongWeChatAssistant Then
                isRelust = IsChargeIOSWechat

            ElseIf mPayVersions = PayVersions.iTongAndroidMedia Then
                isRelust = IsChargeAndroidWechat

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetIsCharge")
        End Try
        Return isRelust
    End Function

    Private Function GetRegisteredInfoPath() As String
        Dim strFilePath As String = Path.Combine(Folder.CacheFolder, "Register")
        Folder.CheckFolder(strFilePath)
        strFilePath = Path.Combine(strFilePath, mPayVersions.ToString() & ".info")
        Return strFilePath
    End Function

#End Region

#Region "判断收费模块"

    Public Sub ShowChargeForm(ByVal owner As Object, Optional ByVal info As WeChatMigrationCharge = Nothing, Optional ByVal blnOverDeviceCount As Boolean = False)
        frmCharge.Show(Me, info, owner, blnOverDeviceCount)
    End Sub

    Public Function CheckIsChargeApp(ByVal owner As Object, Optional blnShowForm As Boolean = True) As Boolean
        Dim resultReturn As Boolean = False
        If Folder.AppType <> RunType.Tongbu_Abroad Then
            Return resultReturn
        End If

        If Not ChargeHelper.mChargeObj.Registered Then
            resultReturn = True
            If blnShowForm Then
                frmCharge.Show(owner, mWCMCInfo)
            End If
        End If
        Return resultReturn
    End Function

    Public Function CheckIsChargeApp(ByVal key As FunctionKey, Optional ByVal intCount As Integer = 0, Optional ByVal strUdid As String = "") As DialogResult
        Return Me.CheckIsChargeApp(Nothing, key, intCount, strUdid)
    End Function

    Public Function CheckIsChargeApp(ByVal owner As Object, ByVal key As FunctionKey, Optional ByVal intCount As Integer = 0, Optional ByVal strUdid As String = "") As DialogResult
        Dim resultReturn As DialogResult = DialogResult.None
        If Not Me.GetIsCharge() Then
            Return resultReturn
        End If

        If Folder.AppType = RunType.Tongbu AndAlso (key <> FunctionKey.Weixin AndAlso key <> FunctionKey.AndroidWeixinMedia) Then
            Return resultReturn
        End If
        If Folder.AppType <> RunType.iWeChatMigration Then
            If ChargeHelper.mChargeObj.Registered Then
                '注册用户判断当前的设备可不可以使用
                If Not String.IsNullOrEmpty(strUdid) AndAlso Not Me.CheckDeviceRegisted(strUdid) Then
                    '该激活码授权已超过正常许可上限，您可在线购买！删除的数据随时可能被系统覆盖，越早恢复越好！
                    'If tbMessageBox.Show(Me.Language.GetString("MainVip.Message.DeviceOverAmount"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, Nothing, False, New String() {Me.Language.GetString("WeChat.Button.Activate.Buy"), Me.Language.GetString("WeChat.Button.Activate.NotBuy")}) = DialogResult.OK Then
                    '    Common.OpenExplorer(WebUrl.PageiTongWeChatAssistant)
                    'End If
                    Me.ShowChargeForm(Nothing, Nothing, True)
                    resultReturn = DialogResult.Cancel
                End If
                Return resultReturn
            End If
        Else
            Me.mIsiWeChatMigrationActivate = False
        End If

        frmCharge.Show(Me, mWCMCInfo, owner)
        Select Case key
            Case FunctionKey.None
                resultReturn = DialogResult.None

            Case FunctionKey.Music, FunctionKey.Video, FunctionKey.TVShows, FunctionKey.Podcasts, FunctionKey.iTunesU, FunctionKey.AudioBook, FunctionKey.VoiceMemo, FunctionKey.Book
                If intCount > ChargeHelper.mChargeObj.CanExportMedias Then
                    resultReturn = DialogResult.Cancel
                End If

            Case FunctionKey.Photo
                If intCount > ChargeHelper.mChargeObj.CanExportPhotos Then
                    resultReturn = DialogResult.Cancel
                End If

            Case FunctionKey.Contact
                If intCount > ChargeHelper.mChargeObj.CanExportContacts Then
                    resultReturn = DialogResult.Cancel
                End If

            Case FunctionKey.SMS
                If intCount > ChargeHelper.mChargeObj.CanExportSMS Then
                    resultReturn = DialogResult.Cancel
                End If

        End Select

        '如果提示之后用户有注册就直接通过
        If ChargeHelper.mChargeObj.Registered Then
            resultReturn = DialogResult.None
        End If

        If Folder.AppType = RunType.iWeChatMigration AndAlso Me.mIsiWeChatMigrationActivate Then
            resultReturn = DialogResult.OK
        End If

        Return resultReturn
    End Function

#End Region

#Region "注册VIP"

    Private mRegistrSucceed As Boolean = True
    Public Function Registration(ByVal strCode As String, ByVal wcmcInfo As WeChatMigrationCharge) As Boolean
        If Not Me.GetIsCharge() Then
            Return True
        End If

        Try
            If Me.mThrRegistration IsNot Nothing AndAlso Me.mThrRegistration.ThreadState <> ThreadState.Stopped Then
                Me.mThrRegistration.Abort()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_Registration")
        End Try

        'Me.mThrRegistration = New Thread(AddressOf RegistrationThread)
        Me.mThrRegistration = New Thread(New ParameterizedThreadStart(AddressOf RegistrationThread))
        With Me.mThrRegistration
            .IsBackground = True
            .Start(New Object() {strCode, wcmcInfo})
        End With
        Me.mRegistrSucceed = True
        Dim intCount As Integer = 0
        While Me.mThrRegistration.ThreadState <> ThreadState.Stopped
            Utility.WaitSeconds(0.5)
            intCount += 1
            If intCount > 40 Then
                Exit While
            End If
        End While
        Return Me.mRegistrSucceed
    End Function

    Public Sub RegistrationThread(ByVal objPara As Object)
        Dim isRelust As Boolean = False
        Dim strMessage As String = ""
        Try
            Dim strCode As String = objPara(0).ToString()
            Dim wcmcInfo As WeChatMigrationCharge = CType(objPara(1), WeChatMigrationCharge)
            Dim strUrl As String = ""
            Select Case mPayVersions
                Case PayVersions.AbroadAssistant
                    strUrl = String.Format(mUrl, "45")

                Case PayVersions.iWeChatMigration
                    strUrl = String.Format(mUrl, "75", "7")

                Case PayVersions.iDataRecovery
                    strUrl = String.Format(mUrl, "65", "5")

                Case PayVersions.iDataRecovery_Abroad
                    strUrl = String.Format(mUrl, "65") & "&projectid=10005"

                Case PayVersions.iTongAndroidMedia
                    strUrl = String.Format(mUrl, "65", "4")

                Case PayVersions.iTongWeChatAssistant
                    strUrl = String.Format(mUrl, "65", "3")

                Case PayVersions.WinWeChatManage
                    strUrl = String.Format(mUrl, "35")

            End Select

            Dim blnSuredelay As Boolean = False

            Dim strContact As String = AESFunctionHelper.PostDataToUrl(Me.GetRegistrationJson(strCode, blnSuredelay, wcmcInfo), strUrl)
            strContact = Me.DecryptDES(strContact)
            Dim isShowMessageBox As Boolean = True
            If strCode.Length = 0 Then
                isShowMessageBox = False
            End If

            isRelust = Me.GetRegistrationResult(strContact, blnSuredelay, isShowMessageBox, strMessage)

            '重新确认注册
            If blnSuredelay Then
                strContact = AESFunctionHelper.PostDataToUrl(Me.GetRegistrationJson(strCode, blnSuredelay, wcmcInfo), strUrl)
                strContact = Me.DecryptDES(strContact)
                isRelust = Me.GetRegistrationResult(strContact, False, isShowMessageBox, strMessage)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_RegistrationThread")
        End Try
        Me.mRegistrSucceed = isRelust
        RaiseEvent RegistrCompleted(Me, New RegistrCompletedEventArgs(isRelust, strMessage))
    End Sub

    Private Function GetRegistrationJson(ByVal strCode As String, ByVal blnSuredelay As Boolean, ByVal WCMCInfo As WeChatMigrationCharge)
        Dim strReturn As String = ""
        Try
            Dim jObject As JsonObject = New JsonObject()

            If mChargeInfo.PayVersion = PayVersions.AbroadAssistant Then
                jObject.Add("projectid", "10000")
                jObject.Add("hdd", Common.GetHardDiskID2())
                jObject.Add("cpu", Common.GetProcessorId().Trim())
                jObject.Add("wifimac", Common.GetMacAddress().Trim())

            ElseIf mChargeInfo.PayVersion = PayVersions.iWeChatMigration Then
                jObject.Add("projectid", "7")
                jObject.Add("id", WCMCInfo.ID)
                jObject.Add("name", WCMCInfo.OldName)
                jObject.Add("weixin", WCMCInfo.OldSku)
                jObject.Add("udid", WCMCInfo.OldUDID)
                jObject.Add("desname", WCMCInfo.NewName)
                jObject.Add("desweixin", WCMCInfo.NewSku)
                jObject.Add("desudid", WCMCInfo.NewUDID)

            ElseIf mChargeInfo.PayVersion = PayVersions.iTongWeChatAssistant Then
                jObject.Add("projectid", "3")

            ElseIf mChargeInfo.PayVersion = PayVersions.iTongAndroidMedia Then
                jObject.Add("projectid", "4")

            ElseIf mChargeInfo.PayVersion = PayVersions.iDataRecovery Then
                jObject.Add("projectid", "5")

            ElseIf mChargeInfo.PayVersion = PayVersions.iDataRecovery_Abroad Then
                jObject.Add("projectid", "10005")

            ElseIf mChargeInfo.PayVersion = PayVersions.WinWeChatManage Then
                jObject.Add("projectid", "2")
                jObject.Add("isWin", "1")

            End If

            jObject.Add("macid", Common.GetMacid())
            jObject.Add("macidnew", Common.GetMacidNew())
            jObject.Add("code", strCode)
            jObject.Add("sn", Common.GetHardDiskID2())
            jObject.Add("macudid", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddress().Trim()))
            jObject.Add("macudidnew", String.Format("{0} || {1}", Common.GetProcessorId().Trim(), Common.GetMacAddressFixed().Trim()))
            jObject.Add("version", My.Application.Info.Version.ToString(4))
            jObject.Add("channel", Common.GetChannelID)
            If blnSuredelay Then
                jObject.Add("suredelay", 1)
            End If

            Dim writer As JsonWriter = New JsonWriter()
            jObject.Write(writer)
            strReturn = writer.ToString()
            strReturn = Me.EncryptDES(strReturn)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetRegistrationJson")
        End Try
        Return strReturn
    End Function

    Private Function GetRegistrationResult(ByVal strContact As String, ByRef blnSuredelay As Boolean, ByVal isShowMessageBox As Boolean, ByRef strMsg As String) As Boolean
        Dim isRelust As Boolean = False
        Try
            Dim strAccessServerException As String = "访问服务器异常"
            If Folder.AppType = RunType.iDataRecovery_Abroad Then
                strAccessServerException = Me.Language.GetString("MainVip.Message.AccessServerException")
            End If
            Dim jObj As JsonObject = JsonParser.ParseString(strContact)
            If jObj Is Nothing Then

                tbMessageBox.Show(strAccessServerException, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                GoTo Do_Exit
            End If

            If jObj.ContainsKey("state") AndAlso TypeOf jObj("state") Is JsonString Then
                Dim para As JsonString = CType(jObj("state"), JsonString)
                If Not para.Value = "1" Then
                    GoTo Do_Exit
                End If
            End If

            Dim dataObject As JsonObject = Nothing
            If jObj.ContainsKey("data") AndAlso TypeOf jObj("data") Is JsonObject Then
                dataObject = jObj("data")
            Else
                GoTo Do_Exit
            End If

            If dataObject.ContainsKey("code") AndAlso TypeOf dataObject("code") Is JsonNumber AndAlso _
              dataObject.ContainsKey("msg") AndAlso TypeOf dataObject("msg") Is JsonString Then
                Dim jCode As JsonNumber = CType(dataObject("code"), JsonNumber)
                Dim jMsg As JsonString = CType(dataObject("msg"), JsonString)     '返回macid
                Dim endTime As DateTime = DateTime.MinValue
                Dim today As DateTime = DateTime.MinValue
                Dim intViptype As Integer = 0
                If dataObject.ContainsKey("endtime") AndAlso TypeOf dataObject("endtime") Is JsonString Then
                    Dim jEndTime As JsonString = CType(dataObject("endtime"), JsonString)
                    Try
                        endTime = CType(jEndTime.Value, DateTime)
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "ChargeHelper_GetRegistrationResult_GetEndDate")
                    End Try
                End If

                If dataObject.ContainsKey("today") AndAlso TypeOf dataObject("today") Is JsonString Then
                    Dim jToday As JsonString = CType(dataObject("today"), JsonString)
                    Try
                        today = CType(jToday.Value, DateTime)
                    Catch ex As Exception
                        Common.LogException(ex.ToString(), "ChargeHelper_GetRegistrationResult_GetToday")
                    End Try
                End If

                If dataObject.ContainsKey("viptype") AndAlso TypeOf dataObject("viptype") Is JsonNumber Then
                    intViptype = CType(dataObject("viptype"), JsonNumber)
                End If

                Dim mbIcon As MessageBoxIcon = MessageBoxIcon.Information
                Dim isblnSuredelay As Boolean = False

                If jCode.Value = "1" AndAlso jMsg.Value = Common.GetMacid() Then
                    ChargeHelper.mChargeObj.Registered = True
                    ChargeHelper.mChargeObj.Loaded = True
                    ChargeHelper.mChargeObj.EndTime = endTime
                    ChargeHelper.mChargeObj.Today = today
                    ChargeHelper.mChargeObj.Viptype = intViptype

                    strMsg = Me.Language.GetString("MainVip.Message.RegisterSucceed")
                    mbIcon = MessageBoxIcon.Information

                    If Folder.AppType = RunType.iWeChatMigration Then
                        Me.mIsiWeChatMigrationActivate = True
                    End If
                    isRelust = True
                ElseIf jCode.Value = "2" Then

                    strMsg = Me.Language.GetString("MainVip.Message.LicenseHasExpired")
                    mbIcon = MessageBoxIcon.Warning
                ElseIf jCode.Value = "3" Then

                    strMsg = Me.Language.GetString("MainVip.Message.ExceedComputerCounts")
                    mbIcon = MessageBoxIcon.Warning
                ElseIf jCode.Value = "4" AndAlso jMsg.Value = Common.GetMacid() Then
                    Dim intMonth As Integer = 0
                    If dataObject.ContainsKey("months") AndAlso TypeOf dataObject("months") Is JsonNumber Then
                        Dim jMonth As JsonNumber = CType(dataObject("months"), JsonNumber)
                        intMonth = jMonth.Value
                    End If
                    '提示用户要不要续期。 已经授权过此电脑！当前到期时间为{0},是否续期{1}个月？
                    If tbMessageBox.Show(String.Format(Me.Language.GetString("MainVip.Message.RenewalTip"), endTime.ToString("yyyy-MM-dd"), intMonth), Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
                        blnSuredelay = True
                    End If
                    isblnSuredelay = True
                ElseIf jCode.Value = "-1" Then
                    '"注册码无效！"
                    strMsg = Me.Language.GetString("MainVip.Message.LicenseIsInvalid")
                    mbIcon = MessageBoxIcon.Error
                ElseIf jCode.Value = "-2" Then
                    strMsg = strAccessServerException ' "服务器处理异常"
                    mbIcon = MessageBoxIcon.Error
                Else
                    '"注册码无效！"
                    strMsg = Me.Language.GetString("MainVip.Message.LicenseIsInvalid")
                    mbIcon = MessageBoxIcon.Error
                End If
                If Not isblnSuredelay AndAlso isShowMessageBox Then
                    tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, mbIcon)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetRegistrationResult")
        End Try
Do_Exit:
        Return isRelust
    End Function

#End Region

#Region "未注册用户导出数据记录传服务器"

    Public Sub SendDataToServer(ByVal key As FunctionKey, ByVal intCount As Integer, Optional ByVal updateToServer As Boolean = True)
        If Folder.AppType <> RunType.Tongbu_Abroad Then
            Return
        End If

        If ChargeHelper.mChargeObj.Registered Then
            Return
        End If

        Select Case key
            Case FunctionKey.Music, FunctionKey.Video, FunctionKey.TVShows, FunctionKey.Podcasts, FunctionKey.iTunesU, FunctionKey.AudioBook, FunctionKey.VoiceMemo, FunctionKey.Book
                ChargeHelper.mChargeObj.CanExportMedias = ChargeHelper.mChargeObj.CanExportMedias - intCount

            Case FunctionKey.Photo
                ChargeHelper.mChargeObj.CanExportPhotos = ChargeHelper.mChargeObj.CanExportPhotos - intCount

            Case FunctionKey.Contact
                ChargeHelper.mChargeObj.CanExportContacts = ChargeHelper.mChargeObj.CanExportContacts - intCount

            Case FunctionKey.SMS
                ChargeHelper.mChargeObj.CanExportSMS = ChargeHelper.mChargeObj.CanExportSMS - intCount

        End Select
        Me.SendDataToServer(key)
    End Sub

    Public Sub SendDataToServer(ByVal key As FunctionKey)
        If Folder.AppType <> RunType.Tongbu_Abroad OrElse ChargeHelper.mChargeObj.Registered Then
            Return
        End If

        Try
            If Me.mThrSendDataToServer IsNot Nothing AndAlso Me.mThrSendDataToServer.ThreadState <> ThreadState.Stopped Then
                Me.mThrSendDataToServer.Abort()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_SendDataToServer")
        End Try

        Me.mThrSendDataToServer = New Thread(New ParameterizedThreadStart(AddressOf SendDataToServerThread))

        With Me.mThrSendDataToServer
            .IsBackground = True
            .Start(key)
        End With
    End Sub

    Public Sub SendDataToServerThread(ByVal paraObj As Object)
        Try
            Dim key As FunctionKey = paraObj
            Dim strUrl As String = String.Format(mUrl, "46")
            Dim strContact As String = AESFunctionHelper.PostDataToUrl(Me.GetExportInfoJson(key), strUrl)
            Me.GetSendDataToServerResult(strContact)
            Me.SetExceptCountToSetting()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_SendDataToServerThread")
        End Try
    End Sub

    Private Function GetExportInfoJson(ByVal key As FunctionKey)
        Dim strReturn As String = ""
        Try
            Dim jObject As JsonObject = New JsonObject()
            jObject.Add("projectid", "10000")
            jObject.Add("macid", Common.GetMacid())
            jObject.Add("cpu", Common.GetProcessorId().Trim())
            jObject.Add("hdd", Common.GetHardDiskID2())
            jObject.Add("wifimac", Common.GetMacAddress().Trim())

            Select Case key
                Case FunctionKey.None  '如果为none就全部更新
                    jObject.Add("mediacount", ChargeHelper.mChargeObj.CanExportMedias)
                    jObject.Add("photocount", ChargeHelper.mChargeObj.CanExportPhotos)
                    jObject.Add("contactscount", ChargeHelper.mChargeObj.CanExportContacts)
                    jObject.Add("smscount", ChargeHelper.mChargeObj.CanExportSMS)
                Case FunctionKey.Music, FunctionKey.Video, FunctionKey.TVShows, FunctionKey.Podcasts, FunctionKey.iTunesU, FunctionKey.AudioBook, FunctionKey.VoiceMemo, FunctionKey.Book
                    jObject.Add("mediacount", ChargeHelper.mChargeObj.CanExportMedias)
                Case FunctionKey.Photo
                    jObject.Add("photocount", ChargeHelper.mChargeObj.CanExportPhotos)
                Case FunctionKey.Contact
                    jObject.Add("contactscount", ChargeHelper.mChargeObj.CanExportContacts)
                Case FunctionKey.SMS
                    jObject.Add("smscount", ChargeHelper.mChargeObj.CanExportSMS)
            End Select

            Dim writer As JsonWriter = New JsonWriter()
            jObject.Write(writer)
            strReturn = writer.ToString()
            strReturn = Me.EncryptDES(strReturn)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetExportInfoJson")
        End Try
        Return strReturn
    End Function

    Private Function GetSendDataToServerResult(ByVal strContact As String) As Boolean
        Dim blnReturn As Boolean = False
        strContact = Me.DecryptDES(strContact)
        Try
            Dim jObj As JsonObject = JsonParser.ParseString(strContact)
            If jObj Is Nothing Then
                Return blnReturn
            End If

            If jObj.ContainsKey("state") AndAlso TypeOf jObj("state") Is JsonString Then
                Dim para As JsonString = CType(jObj("state"), JsonString)
                If Not para.Value = "1" Then
                    Return blnReturn
                End If
            End If

            Dim dataObject As JsonObject = Nothing
            If jObj.ContainsKey("data") AndAlso TypeOf jObj("data") Is JsonObject Then
                dataObject = jObj("data")
            Else
                Return blnReturn
            End If

            If dataObject.ContainsKey("code") AndAlso TypeOf dataObject("code") Is JsonNumber Then
                Dim jCode As JsonNumber = CType(dataObject("code"), JsonNumber)
                If jCode.Value = "1" Then
                    blnReturn = True
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetSendDataToServerResult")
        End Try
        '提交服务器失败后本地错误信息输出
        If Not blnReturn Then
            Common.LogException(strContact, "ChargeHelper_GetSendDataToServerResult")
        End If
        Return blnReturn
    End Function

#End Region

#Region "加解密"

    Private Function EncryptDES(ByVal strContent As String) As String
        Dim strResult As String = ""
        If String.IsNullOrEmpty(strContent) Then
            Return strResult
        End If

        Select Case mPayVersions
            Case PayVersions.AbroadAssistant
                strResult = Common.EncryptDES(strContent, "abzsvp16", "ad283!#0")

            Case PayVersions.iDataRecovery
                strResult = Common.EncryptDES(strContent, "er4c#1!w", "1e5tvi#3")

            Case PayVersions.iDataRecovery_Abroad
                strResult = Common.EncryptDES(strContent, "1t4c#0m7", "0e3cm#i3")

            Case PayVersions.iTongAndroidMedia
                strResult = Common.EncryptDES(strContent, "an!c#28w", "2r5dfr5#")

            Case PayVersions.iTongWeChatAssistant
                strResult = Common.EncryptDES(strContent, "wcos#0!1", "a2#de8fu")

            Case PayVersions.iWeChatMigration
                strResult = Common.EncryptDES(strContent, "wius#0!6", "a6#de8on")

            Case PayVersions.WinWeChatManage
                strResult = Common.EncryptDES(strResult, "mvip2016", "macwchat")

        End Select

        Return strResult
    End Function

    Private Function DecryptDES(ByVal strContent As String) As String
        Dim strResult As String = ""
        If String.IsNullOrEmpty(strContent) Then
            Return strResult
        End If

        strResult = strContent.Replace("_@_", "+")

        Select Case mPayVersions
            Case PayVersions.AbroadAssistant
                strResult = Common.DecryptDES(strResult, "abzsvp16", "ad283!#0")

            Case PayVersions.iDataRecovery
                strResult = Common.DecryptDES(strResult, "er4c#1!w", "1e5tvi#3")

            Case PayVersions.iDataRecovery_Abroad
                strResult = Common.DecryptDES(strResult, "1t4c#0m7", "0e3cm#i3")

            Case PayVersions.iTongAndroidMedia
                strResult = Common.DecryptDES(strResult, "an!c#28w", "2r5dfr5#")

            Case PayVersions.iTongWeChatAssistant
                strResult = Common.DecryptDES(strResult, "wcos#0!1", "a2#de8fu")

            Case PayVersions.iWeChatMigration
                strResult = Common.DecryptDES(strResult, "wius#0!6", "a6#de8on")

            Case PayVersions.WinWeChatManage
                strResult = Common.DecryptDES(strResult, "mvip2016", "macwchat")

        End Select

        Return strResult
    End Function

#End Region

#Region "注册设备"

    Public Function CheckDeviceRegisted(ByVal strUdid As String) As Boolean
        Dim blnReturn As Boolean = False

        ''数据滚回来目前不做设备限制
        'If Folder.AppType = RunType.iDataRecovery Then
        '    Return True
        'End If
        If ChargeHelper.mChargeObj.BindingDevices.ContainsKey(strUdid) Then
            blnReturn = True
        End If

        For Each item As KeyValuePair(Of String, String) In ChargeHelper.mChargeObj.BindingDevices
            Common.LogException(String.Format("KEY:[{0}] VALUE:[{1}]", item.Key, item.Value), "++++GetRegisterInfo--BindingDevices++++")
        Next

        Return blnReturn
    End Function

    Public Sub RegistDevice()
        If Not Me.GetIsCharge() Then
            Return
        End If

        Try
            If Me.mThrRegistrDevice IsNot Nothing AndAlso Me.mThrRegistrDevice.ThreadState <> ThreadState.Stopped Then
                Me.mThrRegistrDevice.Abort()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_RegistDevice")
        End Try

        Me.mThrRegistrDevice = New Thread(New ThreadStart(AddressOf RegistDeviceThread))
        With Me.mThrRegistrDevice
            .IsBackground = True
            .Start()
        End With
    End Sub

    Public Sub RegistDeviceThread()
        Dim strUrl As String = ""
        '如果注册过的手机可以不用再注册了

        Select Case mPayVersions
            Case PayVersions.AbroadAssistant
                strUrl = String.Format(mUrl, "66")

            Case PayVersions.iWeChatMigration
                strUrl = String.Format(mUrl, "66", "7")

            Case PayVersions.iDataRecovery
                strUrl = String.Format(mUrl, "66", "5")

            Case PayVersions.iDataRecovery_Abroad
                strUrl = String.Format(mUrl, "66") & "&projectid=10005"

            Case PayVersions.iTongAndroidMedia
                strUrl = String.Format(mUrl, "66", "4")

            Case PayVersions.iTongWeChatAssistant
                strUrl = String.Format(mUrl, "66", "3")

            Case PayVersions.WinWeChatManage
                strUrl = String.Format(mUrl, "66")

        End Select

        '注册本地的设备信息
        Dim lstSucceed As List(Of String) = New List(Of String)
        For Each item As tbDeviceInfo In ChargeHelper.mChargeObj.BindingDevicesLocal.Values
            Common.LogException(String.Format("Reg:{0}", item.Identifier), "ChargeHelper_RegistDeviceThread")
            Dim strContact As String = AESFunctionHelper.PostDataToUrl(Me.GetRegistDeviceJson(item.Identifier, item.SerialNumber, item.ProductType, item.DeviceName), strUrl)
            strContact = Me.DecryptDES(strContact)
            If Me.GetRegistDeviceResult(strContact, item.DeviceName) Then
                lstSucceed.Add(item.Identifier)
                Common.LogException(String.Format("RegSucceed:{0}", item.Identifier), "ChargeHelper_RegistDeviceThread")
            End If
        Next
        Me.PrintRegisterLog(String.Format("RegisterDevFinished1:count {0}", lstSucceed.Count))
        '注册成功后从列表中删除掉
        For Each item As String In lstSucceed
            If ChargeHelper.mChargeObj.BindingDevicesLocal.ContainsKey(item) Then
                ChargeHelper.mChargeObj.BindingDevicesLocal.Remove(item)
            End If
        Next

        If lstSucceed.Count > 0 Then
            '注册成功后重新写信息到本地设备注册文件中
            Me.SaveDeviceRegistedLocal()
        End If
        
        Me.PrintRegisterLog("RegisterDevFinished")
    End Sub

    Private Function GetRegistDeviceJson(ByVal strUdid As String, ByVal strSN As String, ByVal strProductType As String, ByVal strName As String)
        Dim strReturn As String = ""
        Try
            Dim jObject As JsonObject = New JsonObject()

            If mChargeInfo.PayVersion = PayVersions.AbroadAssistant Then
                jObject.Add("projectid", "10000")

            ElseIf mChargeInfo.PayVersion = PayVersions.iWeChatMigration Then
                jObject.Add("projectid", "7")

            ElseIf mChargeInfo.PayVersion = PayVersions.iTongWeChatAssistant Then
                jObject.Add("projectid", "3")

            ElseIf mChargeInfo.PayVersion = PayVersions.iTongAndroidMedia Then
                jObject.Add("projectid", "4")

            ElseIf mChargeInfo.PayVersion = PayVersions.iDataRecovery Then
                jObject.Add("projectid", "5")

            ElseIf mChargeInfo.PayVersion = PayVersions.iDataRecovery_Abroad Then
                jObject.Add("projectid", "10005")

            ElseIf mChargeInfo.PayVersion = PayVersions.WinWeChatManage Then
                jObject.Add("projectid", "2")

            End If

            jObject.Add("macid", Common.GetMacid())
            jObject.Add("macidnew", Common.GetMacidNew())
            jObject.Add("udid", strUdid)
            jObject.Add("name", strName)
            jObject.Add("sn", strSN)
            jObject.Add("producttype", strProductType)

            Dim writer As JsonWriter = New JsonWriter()
            jObject.Write(writer)
            strReturn = writer.ToString()
            strReturn = Me.EncryptDES(strReturn)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetRegistDeviceJson")
        End Try
        Return strReturn
    End Function

    Private Function GetRegistDeviceResult(ByVal strContact As String, ByVal strName As String) As Boolean
        Dim isRelust As Boolean = False
        Try
            Dim jObj As JsonObject = JsonParser.ParseString(strContact)
            If jObj Is Nothing Then
                GoTo Do_Exit
            End If

            If jObj.ContainsKey("state") AndAlso TypeOf jObj("state") Is JsonString Then
                Dim para As JsonString = CType(jObj("state"), JsonString)
                If Not para.Value = "1" Then
                    GoTo Do_Exit
                End If
            End If

            Dim dataObject As JsonObject = Nothing
            If jObj.ContainsKey("data") AndAlso TypeOf jObj("data") Is JsonObject Then
                dataObject = jObj("data")
            Else
                GoTo Do_Exit
            End If

            If dataObject.ContainsKey("code") AndAlso TypeOf dataObject("code") Is JsonNumber AndAlso _
               dataObject.ContainsKey("msg") AndAlso TypeOf dataObject("msg") Is JsonString Then

                Dim jCode As JsonNumber = CType(dataObject("code"), JsonNumber)                 '1成功 -1参数错误 -2服务器异常 -3没匹配到当前电脑 -4还到设备上限 -5已绑定
                Dim jMsg As JsonString = CType(dataObject("msg"), JsonString)                   '返回macid
                Dim intCode As Integer = jCode.Value
                Dim strUdid As String = String.Empty
                If intCode = 1 AndAlso dataObject.ContainsKey("udid") AndAlso TypeOf dataObject("udid") Is JsonString Then
                    strUdid = CType(dataObject("udid"), JsonString).Value
                    If Not mChargeObj.BindingDevices.ContainsKey(strUdid) Then
                        mChargeObj.BindingDevices.Add(strUdid, strName)
                    End If
                    isRelust = True
                End If
                If intCode = 1 AndAlso dataObject.ContainsKey("remainDevices") AndAlso TypeOf dataObject("remainDevices") Is JsonNumber AndAlso Not mChargeObj.BindingDevicesLocal.ContainsKey(strUdid) Then
                    mChargeObj.CanBindingDevCount = CType(dataObject("remainDevices"), JsonNumber).Value
                End If
                Common.LogException(String.Format("Code:{0} 剩余Count:{1} Msg:{2}", intCode, mChargeObj.CanBindingDevCount, jMsg.Value))
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_GetRegistDeviceResult")
        End Try
Do_Exit:
        Return isRelust
    End Function

    Public Sub SetDeviceRegistedLocal(ByVal dev As iPhoneDevice)
        Dim strProductType As String = SummaryInfo.FormatProduct(dev.ProductType)
        Me.SetDeviceRegistedLocal(dev.Identifier, dev.SerialNumber, strProductType, dev.DeviceName)
    End Sub

    Public Sub SetDeviceRegistedLocal(ByVal strUdid As String, ByVal strSN As String, ByVal strProductType As String, ByVal strName As String)
        Try
            '注册的时候如果可用设备大于0就先记录注册信息
            ChargeHelper.Instance().PrintRegisterLog("SetDeviceRegistedLocal001")
            Common.LogException(String.Format("CanBindingDevCount:{0} BindingDevicesCount:{1}------>>>>>>授权设备", ChargeHelper.mChargeObj.CanBindingDevCount, ChargeHelper.mChargeObj.BindingDevices.Count))
            If ChargeHelper.mChargeObj.CanBindingDevCount > 0 AndAlso Not ChargeHelper.mChargeObj.BindingDevices.ContainsKey(strUdid) Then
                ChargeHelper.mChargeObj.CanBindingDevCount = ChargeHelper.mChargeObj.CanBindingDevCount - 1
                ChargeHelper.mChargeObj.BindingDevices.Add(strUdid, strName)
                ChargeHelper.Instance().PrintRegisterLog("SetDeviceRegistedLocal002")
                If Not ChargeHelper.mChargeObj.BindingDevicesLocal.ContainsKey(strUdid) Then
                    Dim info As New tbDeviceInfo
                    info.Identifier = strUdid
                    info.SerialNumber = strSN
                    info.ProductType = strProductType
                    info.DeviceName = strName
                    ChargeHelper.mChargeObj.BindingDevicesLocal.Add(strUdid, info)
                    ChargeHelper.Instance().PrintRegisterLog("SetDeviceRegistedLocal003")
                    Me.SaveDeviceRegistedLocal()
                    Common.LogException(String.Format("当前注册的设备：{0}", info.Identifier), "ChargeHelper_SetDeviceRegistedLocal")
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetDeviceRegistedLocal")
        End Try
        Common.LogException(String.Format("CanBindingDevCount:{0} BindingDevicesCount:{1}------>>>>>>授权设备Ex", ChargeHelper.mChargeObj.CanBindingDevCount, ChargeHelper.mChargeObj.BindingDevices.Count))
    End Sub

    Private Sub SaveDeviceRegistedLocal()
        Try
            ChargeHelper.Instance().PrintRegisterLog("SaveDeviceRegistedLocal001")
            Dim strValue As String = String.Empty
            '把没有请求服务器的配制信息写到本地文件
            For Each item As tbDeviceInfo In ChargeHelper.mChargeObj.BindingDevicesLocal.Values
                strValue = strValue & String.Format("{0}|{1}|{2}|{3}", item.Identifier, item.SerialNumber, item.ProductType, item.DeviceName) & vbCrLf
            Next
            strValue = strValue.Trim(vbCrLf)
            If strValue.Length > 0 Then
                strValue = EncryptDES(strValue)
            End If
            Try
                ChargeHelper.Instance().PrintRegisterLog("SaveDeviceRegistedLocal002")
                File.WriteAllText(Me.GetRegisterLocalInfoPath(), strValue)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "SaveDeviceRegistedLocal")
            End Try
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SaveDeviceRegistedLocal_1")
        End Try
    End Sub

    '从电脑本地读取没注册服务器的信息
    Private Sub GetDeviceRegistedLocal()
        Try
            Dim strFilePath As String = Me.GetRegisterLocalInfoPath()
            Dim strContent As String = String.Empty
            If File.Exists(strFilePath) Then
                strContent = File.ReadAllText(strFilePath)
                strContent = DecryptDES(strContent)
            End If
            If Not String.IsNullOrEmpty(strContent) Then
                Dim strLines() As String = strContent.Split(vbCrLf)
                If strLines.Length <= 0 Then
                    Return
                End If
                For Each item As String In strLines
                    If item.Length <= 0 Then
                        Continue For
                    End If
                    Dim strValues() As String = item.Split("|")
                    If strValues.Length < 4 Then
                        Continue For
                    End If

                    Dim info As New tbDeviceInfo
                    info.Identifier = strValues(0).Trim(vbCr).Trim(vbLf)
                    info.SerialNumber = strValues(1).Trim(vbCr).Trim(vbLf)
                    info.ProductType = strValues(2).Trim(vbCr).Trim(vbLf)
                    info.DeviceName = strValues(3).Trim(vbCr).Trim(vbLf)
                    If ChargeHelper.mChargeObj.CanBindingDevCount > 0 AndAlso Not ChargeHelper.mChargeObj.BindingDevices.ContainsKey(info.Identifier) Then
                        ChargeHelper.mChargeObj.CanBindingDevCount = ChargeHelper.mChargeObj.CanBindingDevCount - 1
                        ChargeHelper.mChargeObj.BindingDevices.Add(info.Identifier, info.DeviceName)
                        If Not ChargeHelper.mChargeObj.BindingDevicesLocal.ContainsKey(info.Identifier) Then
                            ChargeHelper.mChargeObj.BindingDevicesLocal.Add(info.Identifier, info)
                        End If
                    End If
                Next
            End If
            Me.PrintRegisterLog("LoadRegisterDevFromLocal")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetDeviceRegistedLocal")
        End Try
    End Sub

    Private Function GetRegisterLocalInfoPath()
        Dim strFilePath As String = Path.Combine(Folder.CacheFolder, "Register")
        Folder.CheckFolder(strFilePath)
        strFilePath = Path.Combine(strFilePath, "RegisterDevice.db")
        Return strFilePath
    End Function

#End Region

    Public Sub PrintRegisterLog(ByVal strMark As String)
        '服务器取得注册的设备信息
        Dim strBindingId As String = ""
        Dim strBindingLocal As String = ""
        For Each item As String In ChargeHelper.mChargeObj.BindingDevices.Keys
            strBindingId = strBindingId & item & vbCrLf
        Next

        For Each item As String In ChargeHelper.mChargeObj.BindingDevicesLocal.Keys
            strBindingLocal = strBindingLocal & item & vbCrLf
        Next

        Common.LogException(String.Format("Remain Count:{0} Binding ID :{1} Binding ID Local:{2}", ChargeHelper.mChargeObj.CanBindingDevCount, strBindingId, strBindingLocal), strMark)
    End Sub

End Class

Public Class ChargeInfo
    Private mPayVersions As PayVersions = PayVersions.None
    Private mIsAbroad As Boolean = False                            '判断是不是海外收费版
    Private mLoaded As Boolean = False                              '是否加载过数据
    Private mRegistered As Boolean = False                          '是否注册用户
    Private mCanExportMedia As Integer = 30
    Private mCanExportPhoto As Integer = 30
    Private mCanExportContact As Integer = 30
    Private mCanExportSMS As Integer = 3
    Private mEndTime As DateTime = DateTime.MinValue
    Private mToday As DateTime = DateTime.Now()
    Private mViptype As Integer = 0
    Private mProjectid As Integer = 0
    Private mMacid As String = ""
    Private mLstWeChatMigrationCharge As List(Of WeChatMigrationCharge) = New List(Of WeChatMigrationCharge)
    Private mCanBindingDevCount As Integer = -1                                                                         '剩余可绑定的设备数量
    Private mBindingDevices As Dictionary(Of String, String) = New Dictionary(Of String, String)                        '绑定的设备信息
    Private mBindingDevicesLocal As Dictionary(Of String, tbDeviceInfo) = New Dictionary(Of String, tbDeviceInfo)       '绑定的设备信息(存本地)
    Private mIsfindwechatdata As Boolean = False  '是否可以使用删除找回功能

    Private mServerCodeType As ServerCodeType = ServerCodeType.None

    Sub New()
        If Folder.AppType = RunType.Tongbu_Abroad Then
            Me.mIsAbroad = True
        End If
    End Sub

    Public Property Isfindwechatdata() As Boolean
        Get
            Return Me.mIsfindwechatdata
        End Get
        Set(value As Boolean)
            Me.mIsfindwechatdata = value
        End Set
    End Property

    Public Property PayVersion() As PayVersions
        Get
            Return Me.mPayVersions
        End Get
        Set(value As PayVersions)
            Me.mPayVersions = value
        End Set
    End Property

    Public Property IsAbroad() As Boolean
        Get
            Return Me.mIsAbroad
        End Get
        Set(value As Boolean)
            Me.mIsAbroad = value
        End Set
    End Property

    Public Property Loaded() As Boolean
        Get
            Return Me.mLoaded
        End Get
        Set(value As Boolean)
            Me.mLoaded = value
        End Set
    End Property

    Public Property Registered() As Boolean
        Get
            Return Me.mRegistered
        End Get
        Set(value As Boolean)
            Me.mRegistered = value
        End Set
    End Property

    Public Property CanExportMedias() As Integer
        Get
            Return Me.mCanExportMedia
        End Get
        Set(value As Integer)
            If value >= 0 Then
                Me.mCanExportMedia = value
            End If
        End Set
    End Property

    Public Property CanExportPhotos() As Integer
        Get
            Return Me.mCanExportPhoto
        End Get
        Set(value As Integer)
            If value >= 0 Then
                Me.mCanExportPhoto = value
            End If
        End Set
    End Property

    Public Property CanExportContacts() As Integer
        Get
            Return Me.mCanExportContact
        End Get
        Set(value As Integer)
            If value >= 0 Then
                Me.mCanExportContact = value
            End If
        End Set
    End Property

    Public Property CanExportSMS() As Integer
        Get
            Return Me.mCanExportSMS
        End Get
        Set(value As Integer)
            If value >= 0 Then
                Me.mCanExportSMS = value
            End If
        End Set
    End Property

    Public Property EndTime() As DateTime
        Get
            If Me.mEndTime = DateTime.MinValue Then
                Me.mEndTime = IniSetting.GetRegisteredDate()
            End If
            Return Me.mEndTime
        End Get
        Set(value As DateTime)
            Me.mEndTime = value
            IniSetting.SetRegisteredDate(Me.mEndTime)
        End Set
    End Property

    Public Property Today() As DateTime
        Get
            Return Me.mToday
        End Get
        Set(value As DateTime)
            Me.mToday = value
        End Set
    End Property

    Public Property Viptype() As Integer
        Get
            Return Me.mViptype
        End Get
        Set(value As Integer)
            Me.mViptype = value
        End Set
    End Property

    Public ReadOnly Property Overdue As Boolean
        Get
            Dim isOverdue As Boolean = False
            If Me.mEndTime <> DateTime.MinValue AndAlso Me.mEndTime < Me.mToday Then
                isOverdue = True
            End If
            Return isOverdue
        End Get
    End Property

    Public Property Projectid() As Integer
        Get
            Return Me.mProjectid
        End Get
        Set(value As Integer)
            Me.mProjectid = value
        End Set
    End Property

    Public Property Macid() As String
        Get
            Return Me.mMacid
        End Get
        Set(value As String)
            Me.mMacid = value
        End Set
    End Property

    Public Property LstWeChatMigrationCharge() As List(Of WeChatMigrationCharge)
        Get
            Return Me.mLstWeChatMigrationCharge
        End Get
        Set(value As List(Of WeChatMigrationCharge))
            Me.mLstWeChatMigrationCharge = value
        End Set
    End Property

    Public Property CanBindingDevCount() As Integer
        Get
            Return Me.mCanBindingDevCount
        End Get
        Set(value As Integer)
            Me.mCanBindingDevCount = value
        End Set
    End Property

    Public Property BindingDevices() As Dictionary(Of String, String)
        Get
            Return Me.mBindingDevices
        End Get
        Set(value As Dictionary(Of String, String))
            Me.mBindingDevices = value
        End Set
    End Property

    Public Property BindingDevicesLocal() As Dictionary(Of String, tbDeviceInfo)
        Get
            Return Me.mBindingDevicesLocal
        End Get
        Set(value As Dictionary(Of String, tbDeviceInfo))
            Me.mBindingDevicesLocal = value
        End Set
    End Property

    Public Property ServerCType() As ServerCodeType
        Get
            Return Me.mServerCodeType
        End Get
        Set(value As ServerCodeType)
            Me.mServerCodeType = value
        End Set
    End Property

End Class

Public Enum ServerCodeType
    None
    ParameterError          '参数异常
    ServerException         '服务器异常错误
    LowVersion              '版本太低
End Enum

Public Enum PayVersions
    None
    WinWeChatManage                 'win微信管理大师
    AbroadAssistant                 '海外助手
    iTongWeChatAssistant            '国内助手微信模块
    iDataRecovery                   '数据滚回来
    iDataRecovery_Abroad            '数据滚回来海外
    iTongAndroidMedia               '国内助手安卓媒体
    iWeChatMigration                '微信数据迁移
End Enum

Public Class WeChatMigrationCharge

    Private mID As Integer = -1
    Public Property ID() As Integer
        Get
            Return mID
        End Get
        Set(ByVal value As Integer)
            mID = value
        End Set
    End Property

    Private mOldName As String = ""
    Public Property OldName() As String
        Get
            Return mOldName
        End Get
        Set(ByVal value As String)
            mOldName = value
        End Set
    End Property

    Private mOldSku As String = ""
    Public Property OldSku() As String
        Get
            Return mOldSku
        End Get
        Set(ByVal value As String)
            mOldSku = value
        End Set
    End Property

    Private mOldUDID As String = ""
    Public Property OldUDID() As String
        Get
            Return mOldUDID
        End Get
        Set(ByVal value As String)
            mOldUDID = value
        End Set
    End Property

    Private mNewName As String = ""
    Public Property NewName() As String
        Get
            Return mNewName
        End Get
        Set(ByVal value As String)
            mNewName = value
        End Set
    End Property

    Private mNewSku As String = ""
    Public Property NewSku() As String
        Get
            Return mNewSku
        End Get
        Set(ByVal value As String)
            mNewSku = value
        End Set
    End Property

    Private mNewUDID As String = ""
    Public Property NewUDID() As String
        Get
            Return mNewUDID
        End Get
        Set(ByVal value As String)
            mNewUDID = value
        End Set
    End Property

    Private mEndtime As DateTime = Nothing
    Public Property Endtime() As DateTime
        Get
            Return mEndtime
        End Get
        Set(ByVal value As DateTime)
            mEndtime = value
        End Set
    End Property

    Private mIntCode As Integer = 0 '1.可用   2到期
    Public Property IntCode() As Integer
        Get
            Return mIntCode
        End Get
        Set(ByVal value As Integer)
            mIntCode = value
        End Set
    End Property

    Private mWCMCType As WCMCType = WCMCType.Added
    Public Property WCMCType() As WCMCType
        Get
            Return mWCMCType
        End Get
        Set(ByVal value As WCMCType)
            mWCMCType = value
        End Set
    End Property
End Class

Public Enum WCMCType
    Added
    PastDue
    Official
End Enum

Public Class RegistrCompletedEventArgs
    Inherits EventArgs

    Private mIsSucceed As Boolean = False
    Public Property IsSucceed() As Boolean
        Get
            Return Me.mIsSucceed
        End Get
        Set(ByVal value As Boolean)
            Me.mIsSucceed = value
        End Set
    End Property

    Private mStrMessage As String = ""
    Public Property StrMessage() As String
        Get
            Return Me.mStrMessage
        End Get
        Set(ByVal value As String)
            Me.mStrMessage = value
        End Set
    End Property

    Public Sub New(iss As Boolean, strmsg As String)
        Me.mIsSucceed = iss
        Me.mStrMessage = strmsg
    End Sub
End Class
