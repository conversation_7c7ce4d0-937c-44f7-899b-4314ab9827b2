﻿Imports iTong.CoreModule

Public Class TBGuideHelper

    Public Shared Sub OpenTBGuide(ByVal frm As Form, ByVal tbgType As TBGuideType)

        If Folder.AppType = RunType.Tongbu_Abroad Then
            tbMessageBox.Show(frm, ServerIniSetting.GetTBGuideRemind40, "提示", MessageBoxButtons.OK, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1)
            Return
        End If


        If tbMessageBox.Show(frm, ServerIniSetting.GetTBGuideRemind, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Cancel Then
            Return
        End If

        Dim strUrl As String = ""
        Select Case tbgType
            Case TBGuideType.Linkman
                strUrl = ServerIniSetting.GetTBGuideLinkmanUrl()

            Case TBGuideType.Memorial
                strUrl = ServerIniSetting.GetTBGuideMemorialUrl()

            Case TBGuideType.Msg
                strUrl = ServerIniSetting.GetTBGuideMsgUrl()

            Case TBGuideType.QQ
                strUrl = ServerIniSetting.GetTBGuideQQUrl()

            Case TBGuideType.WeChat
                strUrl = ServerIniSetting.GetTBGuideWeChatUrl()

        End Select
        Common.OpenExplorer(strUrl)
    End Sub

    Public Shared Sub OpenTBGuideByQQ(ByVal frm As Form)
        If Folder.AppType = RunType.Tongbu_Abroad Then
            Common.OpenExplorer(ServerIniSetting.GetGuideiSafeFoneUrl())
        Else
            If ServerIniSetting.GetShowiSafeFoneRecommendCN() Then
                Common.OpenExplorer(ServerIniSetting.GetGuideiSafeFoneUrlCN())
            Else
                If tbMessageBox.Show(frm, ServerIniSetting.GetTBGuideRemindQQ, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Cancel Then
                    Return
                End If
                Common.OpenExplorer(ServerIniSetting.GetTBGuideQQUrl())
            End If
        End If
    End Sub

    Public Shared Sub OpenTBGuidWechatRecoveryDataToPhone(ByVal frm As Form)
        Try
            If Folder.AppType = RunType.Tongbu_Abroad Then
                tbMessageBox.Show(frm, ServerIniSetting.GetTBGuidRemindWechatRecoveryDataToPhone40, "提示", MessageBoxButtons.OK, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1)
                Return
            End If

            If tbMessageBox.Show(frm, ServerIniSetting.GetTBGuidRemindWechatRecoveryDataToPhone, "提示", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Cancel Then
                Return
            End If
            Common.OpenExplorer(ServerIniSetting.GetTBGuidWechatRecoveryDataToPhoneUrl())
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OpenTBGuidWechatRecoveryDataToPhone")
        End Try

    End Sub


End Class

Public Enum TBGuideType
    Linkman
    Memorial
    Msg
    QQ
    WeChat
End Enum