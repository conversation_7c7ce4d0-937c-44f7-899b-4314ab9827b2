﻿'移到CoreModuleCS
Public Class PremiumFeatureInfo

    Private _IsLoadServer As Boolean = False
    Public Property IsLoadServer() As Boolean
        Get
            Return Me._IsLoadServer
        End Get
        Set(ByVal value As Boolean)
            Me._IsLoadServer = value
        End Set
    End Property

    'sku  电脑信息  设备  邮箱  容错次数 是否过期 

    Private _LstSku As New List(Of String)
    Public Property LstSku() As List(Of String)
        Get
            Return Me._LstSku
        End Get
        Set(ByVal value As List(Of String))
            Me._LstSku = value
        End Set
    End Property

    Private _LstComputer As New List(Of String)
    Public Property LstComputer() As List(Of String)
        Get
            Return Me._LstComputer
        End Get
        Set(ByVal value As List(Of String))
            Me._LstComputer = value
        End Set
    End Property

    Private _LstDevice As New List(Of String)
    Public Property LstDevice() As List(Of String)
        Get
            Return Me._LstDevice
        End Get
        Set(ByVal value As List(Of String))
            Me._LstDevice = value
        End Set
    End Property

    Private _StrEmail As String = ""
    Public Property StrEmail() As String
        Get
            Return Me._StrEmail
        End Get
        Set(ByVal value As String)
            Me._StrEmail = value
        End Set
    End Property

    Private _IntTryCount As Integer = 0
    Public Property IntTryCount() As Integer
        Get
            Return Me._IntTryCount
        End Get
        Set(ByVal value As Integer)
            Me._IntTryCount = value
        End Set
    End Property

    Private _IsOverdue As Boolean = False
    Public Property IsOverdue() As Boolean
        Get
            Return Me._IsOverdue
        End Get
        Set(ByVal value As Boolean)
            Me._IsOverdue = value
        End Set
    End Property

    Private _StrNewSku As String = ""
    Public Property StrNewSku() As String
        Get
            Return Me._StrNewSku
        End Get
        Set(ByVal value As String)
            Me._StrNewSku = value
        End Set
    End Property

End Class