﻿'移到CoreModuleCS
Imports System.Threading

Public Class PremiumFeatureHelper
    Implements IDisposable

    Private mLanguage As LanguageInterface = Nothing
    Private mPremiumFeatureInfo As PremiumFeatureInfo = Nothing
    Public Property CPremiumFeatureInfo() As PremiumFeatureInfo
        Get
            Return Me.mPremiumFeatureInfo
        End Get
        Set(ByVal value As PremiumFeatureInfo)
            Me.mPremiumFeatureInfo = value
        End Set
    End Property

    Private mTdLoadServer As Thread = Nothing

#Region "--- 单例 ---"
    Private Shared mDictInstances As New Dictionary(Of String, PremiumFeatureHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Private Shared _lockGetInstance As New Object

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New()
        Me.mLanguage = LanguageInterface.Instance()

        Me.mPremiumFeatureInfo = PremiumFeatureHelper.GetWeChatVIPSpareInfo()
        Me.LoadServer()

    End Sub

    Private Shared mHelper As PremiumFeatureHelper = Nothing

    Public Shared Function GetInstance() As PremiumFeatureHelper
        Dim helper As PremiumFeatureHelper = Nothing
        Dim blnRetry As Boolean = False

        SyncLock _lockGetInstance
DO_RETRY:
            Try
                If Not mDictInstances.ContainsKey(NoDevcieIdentifier) Then
                    mDictInstances.Add(NoDevcieIdentifier, New PremiumFeatureHelper())
                End If
                helper = mDictInstances.Item(NoDevcieIdentifier)
            Catch ex As Exception
                If Not blnRetry Then
                    blnRetry = True
                    GoTo DO_RETRY
                End If
            End Try
        End SyncLock
        Return helper
    End Function

#End Region

#Region "--- 释放 ---"

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
                mDictInstances.Clear()

               Try
                    If Me.mTdLoadServer IsNot Nothing AndAlso Me.mTdLoadServer.ThreadState <> ThreadState.Stopped Then
                        Me.mTdLoadServer.Abort()
                        Me.mTdLoadServer = Nothing
                    End If
                Catch
                End Try
            End If
        End If
        disposedValue = True
    End Sub

#End Region

#Region "--- 配置INI去读 ---"
    Private Shared VIPWC As String = "&88%19@@"
    Public Shared Function GetShowWeChatVIPSpare()
        Dim isResult As Boolean = False
        Try
            Dim strResult As String = Common.DecryptDES(IniClass.GetIniSectionKey("WeChatVIPCoexist", "ShowWeChatVIPSpare", Folder.PremiumFeatureIniFile), VIPWC, VIPWC)
            If strResult.ToLower() = "true" Then
                isResult = True
            End If
        Catch ex As Exception

        End Try
        Return isResult
    End Function

    Public Shared Function GetWeChatVIPSpareInfo() As PremiumFeatureInfo
        Dim info As New PremiumFeatureInfo

        '----------------------配置生成-----------------------
        'info.IsOverdue = False
        'info.StrEmail = "<EMAIL>"

        'Dim lstSku As New List(Of String)
        'lstSku.Add("com.tencent.weixin.********.x")
        'info.LstSku = lstSku

        'Dim lstDevice As New List(Of String)
        'lstDevice.Add("5d0c1ff5729b04b031ed8056f60a442c314f48ae")
        'info.LstDevice = lstDevice

        'Dim lstComputer As New List(Of String)
        'lstComputer.Add("e89bcb415ddaf8a8dd6e7d576e44b56e")
        'info.LstComputer = lstComputer

        'info.StrNewSku = "com.tencent.xin.mjuruangong1"

        'SetWeChatVIPSpareInfo(info)
        '---------------------------------------------

        Try
            Dim strValue As String = Common.DecryptDES(IniClass.GetIniSectionKey("WeChatVIPCoexist", "WeChatVIPSpareInfo", Folder.PremiumFeatureIniFile), VIPWC, VIPWC)
            If strValue.Length = 0 Then
                GoTo De_Exit
            End If
            'sku  电脑信息  设备  邮箱  容错次数 是否过期 

            Dim arrValue As String() = strValue.Split("〖")
            If arrValue.Length < 7 Then
                GoTo De_Exit
            End If

            info.LstSku = GetLst(arrValue(0))
            info.LstComputer = GetLst(arrValue(1))
            info.LstDevice = GetLst(arrValue(2))
            info.StrEmail = arrValue(3)
            info.IntTryCount = arrValue(4)
            info.IsOverdue = arrValue(5)
            info.StrNewSku = arrValue(6)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetWeChatVIPSpareInfo")
        End Try
De_Exit:
        Return info
    End Function

    Private Shared Function GetLst(ByVal strValue As String) As List(Of String)
        Dim lstTemp As New List(Of String)
        Try
            If String.IsNullOrEmpty(strValue) Then
                GoTo Do_Exit
            End If

            Dim arrTemp As String() = strValue.Split("『")
            If arrTemp.Length = 0 Then
                GoTo Do_Exit
            End If

            For Each item As String In arrTemp
                If Not String.IsNullOrEmpty(item) Then
                    lstTemp.Add(item)
                End If
            Next

        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetLst")
        End Try
Do_Exit:
        Return lstTemp
    End Function

    Public Shared Function SetWeChatVIPSpareInfo(ByVal PFInfo As PremiumFeatureInfo) As Boolean
        'sku  电脑信息  设备  邮箱  容错次数 是否过期 
        Dim isRelust As Boolean = False
        Try
            Dim strValue As String = ""
            Dim strSku As String = ""
            If PFInfo.LstSku.Count > 0 Then
                For Each sku As String In PFInfo.LstSku
                    strSku = sku & "『"
                Next
            End If

            Dim strComputer As String = ""
            If PFInfo.LstComputer.Count > 0 Then
                For Each computer As String In PFInfo.LstComputer
                    strComputer = computer & "『"
                Next
            End If

            Dim strDevice As String = ""
            If PFInfo.LstDevice.Count > 0 Then
                For Each device As String In PFInfo.LstDevice
                    strDevice = device & "『"
                Next
            End If

            strValue = String.Format("{0}〖{1}〖{2}〖{3}〖{4}〖{5}〖{6}", strSku, strComputer, strDevice, PFInfo.StrEmail, PFInfo.IntTryCount, PFInfo.IsOverdue, PFInfo.StrNewSku)
            isRelust = IniClass.SetIniSectionKey("WeChatVIPCoexist", "WeChatVIPSpareInfo", Common.EncryptDES(strValue, VIPWC, VIPWC), Folder.PremiumFeatureIniFile)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SetWeChatVIPSpareInfo")
        End Try
        Return isRelust
    End Function

#End Region

    Private Sub LoadServer()
        Try
            If Me.mTdLoadServer IsNot Nothing AndAlso Me.mTdLoadServer.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            Me.mTdLoadServer = New Thread(AddressOf DoLoadServer)
            Me.mTdLoadServer.IsBackground = True
            Me.mTdLoadServer.Start()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadServer")
        End Try
    End Sub

    Private Sub DoLoadServer()
        Try
            If Me.mPremiumFeatureInfo Is Nothing Then
                Me.mPremiumFeatureInfo = PremiumFeatureHelper.GetWeChatVIPSpareInfo()
            End If

            Dim strUrl As String = String.Format("http://pc.api.tongbu.com/vip/v.html?t=2&mail={0}&projectid=1", Me.mPremiumFeatureInfo.StrEmail)
            Dim InfoTemp As PremiumFeatureInfo = Me.GetPFInfoByServer(strUrl)
            Dim intTryCount As Integer = 0
            While InfoTemp Is Nothing AndAlso intTryCount < 3
                Utility.WaitSeconds(0.5)
                InfoTemp = Me.GetPFInfoByServer(strUrl)
                intTryCount += 1
            End While

            If InfoTemp Is Nothing Then
                '七次容错
                If Me.mPremiumFeatureInfo.IntTryCount > 7 Then
                    Me.mPremiumFeatureInfo.IsOverdue = True
                End If
                Common.LogException("获取不到VIP信息：容错 " & Me.mPremiumFeatureInfo.IntTryCount & "  Url:" & strUrl, "DoLoadServer-获取不到VIP信息")
                Me.mPremiumFeatureInfo.IntTryCount = Me.mPremiumFeatureInfo.IntTryCount + 1

            Else
                Me.mPremiumFeatureInfo.IntTryCount = 0

                Me.mPremiumFeatureInfo.IsOverdue = InfoTemp.IsOverdue

                If InfoTemp.LstComputer.Count > 0 Then
                    Me.mPremiumFeatureInfo.LstComputer = InfoTemp.LstComputer
                End If

                If InfoTemp.LstDevice.Count > 0 Then
                    Me.mPremiumFeatureInfo.LstDevice = InfoTemp.LstDevice
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoLoadServer")

            If Me.mPremiumFeatureInfo.IntTryCount > 7 Then
                Me.mPremiumFeatureInfo.IsOverdue = True
            End If
            Common.LogException("获取不到VIP信息：容错 " & Me.mPremiumFeatureInfo.IntTryCount, "DoLoadServer-获取不到VIP信息_Exception")
            Me.mPremiumFeatureInfo.IntTryCount = Me.mPremiumFeatureInfo.IntTryCount + 1

        End Try
        Try
            Me.mPremiumFeatureInfo.IsLoadServer = True
            PremiumFeatureHelper.SetWeChatVIPSpareInfo(Me.mPremiumFeatureInfo)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoLoadServer_1")
        End Try
    End Sub

    Public Function GetPFInfoByServer(ByVal strUrl As String) As PremiumFeatureInfo
        Dim info As PremiumFeatureInfo = Nothing
        Try
            Dim strContent As String = Utility.GetContentStringFromUrl(strUrl, Encoding.UTF8, 20000)

            Common.Log(String.Format("VIP验证： {0}{1}", strUrl, strContent))

            If strContent.Length = 0 Then
                GoTo Do_Exit
            End If
            strContent = Common.DecryptDES(strContent.Replace("_@_", "+"), "v[ip2016", "abcdi8fd")
            Dim objJson As JsonObject = JsonParser.ParseString(strContent)
            If objJson Is Nothing Then
                GoTo Do_Exit
            End If
            If Not objJson.ContainsKey("state") Then
                GoTo Do_Exit
            End If
            Dim strState As JsonString = CType(objJson("state"), JsonString)
            If strState <> "1" Then
                GoTo Do_Exit
            End If

            If Not objJson.ContainsKey("data") Then
                GoTo Do_Exit
            End If

            If TypeOf objJson("data") Is JsonNull Then
                GoTo Do_Exit
            End If
            Dim jsonData As JsonObject = CType(objJson("data"), JsonObject)

            info = New PremiumFeatureInfo()

            Dim intStatus As JsonNumber = CType(jsonData("status"), JsonNumber)
            info.IsOverdue = IIf(intStatus = 0, True, False)  '（0 过期 1没有过期）

            If Not TypeOf jsonData("computers") Is JsonNull Then
                Dim lstComputer As New List(Of String)
                Dim jsonComputer As JsonArray = CType(jsonData("computers"), JsonArray)
                For Each objItem As Object In jsonComputer
                    Dim appItem As JsonObject = Nothing
                    If TypeOf objItem Is JsonNull Then
                        Continue For
                    Else
                        appItem = CType(objItem, JsonObject)
                    End If

                    Dim strMacid As JsonString = CType(appItem("macid"), JsonString)
                    If Not String.IsNullOrEmpty(strMacid) Then
                        lstComputer.Add(strMacid)
                    End If
                Next
                info.LstComputer = lstComputer
            End If

            If Not TypeOf jsonData("devices") Is JsonNull Then
                Dim lstDevice As New List(Of String)
                Dim jsonDevice As JsonArray = CType(jsonData("devices"), JsonArray)
                For Each objItem As Object In jsonDevice
                    Dim appItem As JsonObject = Nothing
                    If TypeOf objItem Is JsonNull Then
                        Continue For
                    Else
                        appItem = CType(objItem, JsonObject)
                    End If

                    Dim strMacid As JsonString = CType(appItem("phoneid"), JsonString)
                    If Not String.IsNullOrEmpty(strMacid) Then
                        lstDevice.Add(strMacid)
                    End If
                Next
                info.LstDevice = lstDevice
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetPFInfoByServer")
        End Try
Do_Exit:
        Return info
    End Function

End Class
