﻿Public Class frmWait

    Private mWaitState As WaitState = WaitState.None

#Region "--- 初始化 ---"
    Public Sub New(ByVal application As IApplication, ByVal strMsg As String, Optional ByVal ws As WaitState = WaitState.PremiumFeature)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.Icon = My.Resources.iTong

        Me.mWaitState = ws

        Me.lblMsg.Text = strMsg
    End Sub

    Protected Overrides Sub OnShown(e As System.EventArgs)
        MyBase.OnShown(e)

        If Me.mWaitState = WaitState.PremiumFeature Then
            While Not PremiumFeatureHelper.GetInstance.CPremiumFeatureInfo.IsLoadServer
                Utility.WaitSeconds(0.5)
            End While
        ElseIf Me.mWaitState = WaitState.FullBackupList Then
            While Not BackupHelper.IsLoadFullBackupList
                Utility.WaitSeconds(0.5)
            End While
        End If
        Me.Close()
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        btn_minimize.Visible = False
        btn_normal.Visible = False
        btn_close.Visible = False

    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

    End Sub

    Protected Overrides Sub OnFormClosed(ByVal e As System.Windows.Forms.FormClosedEventArgs)
        MyBase.OnFormClosed(e)

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

    End Sub

#End Region

End Class

Public Enum WaitState
    None
    PremiumFeature
    FullBackupList
End Enum