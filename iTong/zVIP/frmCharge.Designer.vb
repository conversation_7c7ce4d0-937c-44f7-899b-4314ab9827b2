﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmCharge
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmCharge))
        Me.btn_close = New iTong.Components.tbButton()
        Me.lblTrial = New iTong.Components.tbLabel()
        Me.lblBenefitsDesc = New iTong.Components.tbLabel()
        Me.lblTrialDesc1 = New iTong.Components.tbLabel()
        Me.lblTrialDesc2 = New iTong.Components.tbLabel()
        Me.lblTrialDesc3 = New iTong.Components.tbLabel()
        Me.lblTrialDesc4 = New iTong.Components.tbLabel()
        Me.lblTrialDesc5 = New iTong.Components.tbLabel()
        Me.lblCode = New iTong.Components.tbLabel()
        Me.txtCode = New iTong.Components.tbTextBox()
        Me.btnFreeTry = New iTong.Components.tbButton()
        Me.btnRegister = New iTong.Components.tbButton()
        Me.btnPurchase = New iTong.Components.tbButton()
        Me.pbLoading = New System.Windows.Forms.PictureBox()
        Me.pnlAbroad = New System.Windows.Forms.Panel()
        Me.rtxTrialDesc = New System.Windows.Forms.RichTextBox()
        Me.lblTrialDesc7 = New iTong.Components.tbLabel()
        Me.lblTrialDesc6 = New iTong.Components.tbLabel()
        Me.pnlWinWeChat = New System.Windows.Forms.Panel()
        Me.lblTrialVersion = New iTong.Components.tbLabel()
        Me.pnlFeature = New System.Windows.Forms.Panel()
        Me.txtWeChatDescribe = New System.Windows.Forms.RichTextBox()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.pnlAbroad.SuspendLayout()
        Me.pnlWinWeChat.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(574, 3)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 5
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'lblTrial
        '
        Me.lblTrial.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblTrial.BackColor = System.Drawing.Color.Transparent
        Me.lblTrial.Location = New System.Drawing.Point(7, 50)
        Me.lblTrial.Name = "lblTrial"
        Me.lblTrial.Size = New System.Drawing.Size(566, 26)
        Me.lblTrial.TabIndex = 6
        Me.lblTrial.tbAdriftWhenHover = False
        Me.lblTrial.tbAutoEllipsis = False
        Me.lblTrial.tbAutoSize = False
        Me.lblTrial.tbHideImage = False
        Me.lblTrial.tbIconImage = Nothing
        Me.lblTrial.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrial.tbIconPlaceText = 5
        Me.lblTrial.tbShadow = False
        Me.lblTrial.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrial.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrial.tbShowScrolling = False
        Me.lblTrial.Text = "试用版功能："
        Me.lblTrial.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblBenefitsDesc
        '
        Me.lblBenefitsDesc.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblBenefitsDesc.BackColor = System.Drawing.Color.Transparent
        Me.lblBenefitsDesc.Location = New System.Drawing.Point(7, 10)
        Me.lblBenefitsDesc.Name = "lblBenefitsDesc"
        Me.lblBenefitsDesc.Size = New System.Drawing.Size(557, 38)
        Me.lblBenefitsDesc.TabIndex = 8
        Me.lblBenefitsDesc.tbAdriftWhenHover = False
        Me.lblBenefitsDesc.tbAutoEllipsis = False
        Me.lblBenefitsDesc.tbAutoSize = False
        Me.lblBenefitsDesc.tbHideImage = False
        Me.lblBenefitsDesc.tbIconImage = Nothing
        Me.lblBenefitsDesc.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblBenefitsDesc.tbIconPlaceText = 5
        Me.lblBenefitsDesc.tbShadow = False
        Me.lblBenefitsDesc.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblBenefitsDesc.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblBenefitsDesc.tbShowScrolling = False
        Me.lblBenefitsDesc.Text = "您使用的是未注册的版本，移除试用版限制请从我们的商店购买激活码。"
        '
        'lblTrialDesc1
        '
        Me.lblTrialDesc1.BackColor = System.Drawing.Color.Transparent
        Me.lblTrialDesc1.Location = New System.Drawing.Point(7, 80)
        Me.lblTrialDesc1.Name = "lblTrialDesc1"
        Me.lblTrialDesc1.Size = New System.Drawing.Size(400, 22)
        Me.lblTrialDesc1.TabIndex = 9
        Me.lblTrialDesc1.tbAdriftWhenHover = False
        Me.lblTrialDesc1.tbAutoEllipsis = False
        Me.lblTrialDesc1.tbAutoSize = False
        Me.lblTrialDesc1.tbHideImage = False
        Me.lblTrialDesc1.tbIconImage = Nothing
        Me.lblTrialDesc1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrialDesc1.tbIconPlaceText = 5
        Me.lblTrialDesc1.tbShadow = False
        Me.lblTrialDesc1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrialDesc1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrialDesc1.tbShowScrolling = False
        Me.lblTrialDesc1.Text = "1.相片：可导出30张（还剩30张）"
        '
        'lblTrialDesc2
        '
        Me.lblTrialDesc2.BackColor = System.Drawing.Color.Transparent
        Me.lblTrialDesc2.Location = New System.Drawing.Point(7, 100)
        Me.lblTrialDesc2.Name = "lblTrialDesc2"
        Me.lblTrialDesc2.Size = New System.Drawing.Size(400, 34)
        Me.lblTrialDesc2.TabIndex = 10
        Me.lblTrialDesc2.tbAdriftWhenHover = False
        Me.lblTrialDesc2.tbAutoEllipsis = False
        Me.lblTrialDesc2.tbAutoSize = False
        Me.lblTrialDesc2.tbHideImage = False
        Me.lblTrialDesc2.tbIconImage = Nothing
        Me.lblTrialDesc2.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrialDesc2.tbIconPlaceText = 5
        Me.lblTrialDesc2.tbShadow = False
        Me.lblTrialDesc2.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrialDesc2.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrialDesc2.tbShowScrolling = False
        Me.lblTrialDesc2.Text = "2.音乐铃声及电影、电视节目、Podcasts、iTunes U、有声视频、" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "备忘录和书籍可导出30（还剩30）"
        '
        'lblTrialDesc3
        '
        Me.lblTrialDesc3.BackColor = System.Drawing.Color.Transparent
        Me.lblTrialDesc3.Location = New System.Drawing.Point(6, 136)
        Me.lblTrialDesc3.Name = "lblTrialDesc3"
        Me.lblTrialDesc3.Size = New System.Drawing.Size(400, 22)
        Me.lblTrialDesc3.TabIndex = 11
        Me.lblTrialDesc3.tbAdriftWhenHover = False
        Me.lblTrialDesc3.tbAutoEllipsis = False
        Me.lblTrialDesc3.tbAutoSize = False
        Me.lblTrialDesc3.tbHideImage = False
        Me.lblTrialDesc3.tbIconImage = Nothing
        Me.lblTrialDesc3.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrialDesc3.tbIconPlaceText = 5
        Me.lblTrialDesc3.tbShadow = False
        Me.lblTrialDesc3.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrialDesc3.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrialDesc3.tbShowScrolling = False
        Me.lblTrialDesc3.Text = "3.联系人：可导出30个（还剩30个）"
        '
        'lblTrialDesc4
        '
        Me.lblTrialDesc4.BackColor = System.Drawing.Color.Transparent
        Me.lblTrialDesc4.Location = New System.Drawing.Point(7, 157)
        Me.lblTrialDesc4.Name = "lblTrialDesc4"
        Me.lblTrialDesc4.Size = New System.Drawing.Size(400, 22)
        Me.lblTrialDesc4.TabIndex = 12
        Me.lblTrialDesc4.tbAdriftWhenHover = False
        Me.lblTrialDesc4.tbAutoEllipsis = False
        Me.lblTrialDesc4.tbAutoSize = False
        Me.lblTrialDesc4.tbHideImage = False
        Me.lblTrialDesc4.tbIconImage = Nothing
        Me.lblTrialDesc4.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrialDesc4.tbIconPlaceText = 5
        Me.lblTrialDesc4.tbShadow = False
        Me.lblTrialDesc4.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrialDesc4.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrialDesc4.tbShowScrolling = False
        Me.lblTrialDesc4.Text = "4.短信：可导出30个（还剩3个）"
        '
        'lblTrialDesc5
        '
        Me.lblTrialDesc5.BackColor = System.Drawing.Color.Transparent
        Me.lblTrialDesc5.Location = New System.Drawing.Point(7, 170)
        Me.lblTrialDesc5.Name = "lblTrialDesc5"
        Me.lblTrialDesc5.Size = New System.Drawing.Size(400, 22)
        Me.lblTrialDesc5.TabIndex = 13
        Me.lblTrialDesc5.tbAdriftWhenHover = False
        Me.lblTrialDesc5.tbAutoEllipsis = False
        Me.lblTrialDesc5.tbAutoSize = False
        Me.lblTrialDesc5.tbHideImage = False
        Me.lblTrialDesc5.tbIconImage = Nothing
        Me.lblTrialDesc5.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrialDesc5.tbIconPlaceText = 5
        Me.lblTrialDesc5.tbShadow = False
        Me.lblTrialDesc5.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrialDesc5.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrialDesc5.tbShowScrolling = False
        Me.lblTrialDesc5.Text = "5.导入视频文件前3分钟（还剩3）"
        '
        'lblCode
        '
        Me.lblCode.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblCode.BackColor = System.Drawing.Color.Transparent
        Me.lblCode.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblCode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblCode.Location = New System.Drawing.Point(22, 491)
        Me.lblCode.Name = "lblCode"
        Me.lblCode.Size = New System.Drawing.Size(415, 24)
        Me.lblCode.TabIndex = 18
        Me.lblCode.tbAdriftWhenHover = False
        Me.lblCode.tbAutoEllipsis = False
        Me.lblCode.tbAutoSize = False
        Me.lblCode.tbHideImage = False
        Me.lblCode.tbIconImage = Nothing
        Me.lblCode.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblCode.tbIconPlaceText = 5
        Me.lblCode.tbShadow = False
        Me.lblCode.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblCode.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblCode.tbShowScrolling = False
        Me.lblCode.Text = "激活码："
        Me.lblCode.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'txtCode
        '
        Me.txtCode.Anchor = CType(((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtCode.BackColor = System.Drawing.Color.White
        Me.txtCode.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtCode.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtCode.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtCode.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtCode.ForeColor = System.Drawing.Color.Black
        Me.txtCode.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtCode.Location = New System.Drawing.Point(24, 516)
        Me.txtCode.MaxLength = 32767
        Me.txtCode.Name = "txtCode"
        Me.txtCode.Size = New System.Drawing.Size(552, 21)
        Me.txtCode.TabIndex = 17
        Me.txtCode.Tag = Nothing
        Me.txtCode.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtCode.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtCode.tbSelMark = True
        Me.txtCode.tbTextBind = ""
        Me.txtCode.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtCode.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtCode.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtCode.TextTip = "请输入激活码"
        '
        'btnFreeTry
        '
        Me.btnFreeTry.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.btnFreeTry.BackColor = System.Drawing.Color.Transparent
        Me.btnFreeTry.BindingForm = Nothing
        Me.btnFreeTry.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnFreeTry.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnFreeTry.Location = New System.Drawing.Point(433, 562)
        Me.btnFreeTry.Name = "btnFreeTry"
        Me.btnFreeTry.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnFreeTry.Selectable = True
        Me.btnFreeTry.Size = New System.Drawing.Size(89, 23)
        Me.btnFreeTry.TabIndex = 42
        Me.btnFreeTry.tbAdriftIconWhenHover = False
        Me.btnFreeTry.tbAutoSize = False
        Me.btnFreeTry.tbAutoSizeEx = False
        Me.btnFreeTry.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnFreeTry.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnFreeTry.tbBadgeNumber = 0
        Me.btnFreeTry.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnFreeTry.tbEndEllipsis = False
        Me.btnFreeTry.tbIconHoldPlace = True
        Me.btnFreeTry.tbIconImage = Nothing
        Me.btnFreeTry.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFreeTry.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnFreeTry.tbIconMore = False
        Me.btnFreeTry.tbIconMouseDown = Nothing
        Me.btnFreeTry.tbIconMouseHover = Nothing
        Me.btnFreeTry.tbIconMouseLeave = Nothing
        Me.btnFreeTry.tbIconPlaceText = 2
        Me.btnFreeTry.tbIconReadOnly = Nothing
        Me.btnFreeTry.tbImageMouseDown = Nothing
        Me.btnFreeTry.tbImageMouseHover = Nothing
        Me.btnFreeTry.tbImageMouseLeave = Nothing
        Me.btnFreeTry.tbProgressValue = 50
        Me.btnFreeTry.tbReadOnly = False
        Me.btnFreeTry.tbReadOnlyText = False
        Me.btnFreeTry.tbShadow = False
        Me.btnFreeTry.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnFreeTry.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnFreeTry.tbShowDot = False
        Me.btnFreeTry.tbShowMoreIconImg = CType(resources.GetObject("btnFreeTry.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnFreeTry.tbShowNew = False
        Me.btnFreeTry.tbShowProgress = False
        Me.btnFreeTry.tbShowTip = True
        Me.btnFreeTry.tbShowToolTipOnButton = False
        Me.btnFreeTry.tbSplit = "13,11,13,11"
        Me.btnFreeTry.tbText = "试用"
        Me.btnFreeTry.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFreeTry.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnFreeTry.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnFreeTry.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnFreeTry.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnFreeTry.tbTextMouseDownPlace = 0
        Me.btnFreeTry.tbToolTip = ""
        Me.btnFreeTry.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnFreeTry.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnFreeTry.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnFreeTry.Visible = False
        Me.btnFreeTry.VisibleEx = True
        '
        'btnRegister
        '
        Me.btnRegister.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnRegister.BackColor = System.Drawing.Color.Transparent
        Me.btnRegister.BindingForm = Nothing
        Me.btnRegister.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRegister.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnRegister.Location = New System.Drawing.Point(474, 562)
        Me.btnRegister.Name = "btnRegister"
        Me.btnRegister.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnRegister.Selectable = True
        Me.btnRegister.Size = New System.Drawing.Size(94, 26)
        Me.btnRegister.TabIndex = 41
        Me.btnRegister.tbAdriftIconWhenHover = False
        Me.btnRegister.tbAutoSize = False
        Me.btnRegister.tbAutoSizeEx = False
        Me.btnRegister.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnRegister.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnRegister.tbBadgeNumber = 0
        Me.btnRegister.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnRegister.tbEndEllipsis = False
        Me.btnRegister.tbIconHoldPlace = True
        Me.btnRegister.tbIconImage = Nothing
        Me.btnRegister.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRegister.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnRegister.tbIconMore = False
        Me.btnRegister.tbIconMouseDown = Nothing
        Me.btnRegister.tbIconMouseHover = Nothing
        Me.btnRegister.tbIconMouseLeave = Nothing
        Me.btnRegister.tbIconPlaceText = 2
        Me.btnRegister.tbIconReadOnly = Nothing
        Me.btnRegister.tbImageMouseDown = Nothing
        Me.btnRegister.tbImageMouseHover = Nothing
        Me.btnRegister.tbImageMouseLeave = Nothing
        Me.btnRegister.tbProgressValue = 50
        Me.btnRegister.tbReadOnly = False
        Me.btnRegister.tbReadOnlyText = False
        Me.btnRegister.tbShadow = False
        Me.btnRegister.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnRegister.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnRegister.tbShowDot = False
        Me.btnRegister.tbShowMoreIconImg = CType(resources.GetObject("btnRegister.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnRegister.tbShowNew = False
        Me.btnRegister.tbShowProgress = False
        Me.btnRegister.tbShowTip = True
        Me.btnRegister.tbShowToolTipOnButton = False
        Me.btnRegister.tbSplit = "13,11,13,11"
        Me.btnRegister.tbText = "激活"
        Me.btnRegister.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRegister.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.btnRegister.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.btnRegister.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.btnRegister.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer), CType(CType(102, Byte), Integer))
        Me.btnRegister.tbTextMouseDownPlace = 0
        Me.btnRegister.tbToolTip = ""
        Me.btnRegister.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnRegister.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnRegister.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnRegister.VisibleEx = True
        '
        'btnPurchase
        '
        Me.btnPurchase.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnPurchase.BackColor = System.Drawing.Color.Transparent
        Me.btnPurchase.BindingForm = Nothing
        Me.btnPurchase.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btnPurchase.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.btnPurchase.Location = New System.Drawing.Point(371, 562)
        Me.btnPurchase.Name = "btnPurchase"
        Me.btnPurchase.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnPurchase.Selectable = True
        Me.btnPurchase.Size = New System.Drawing.Size(94, 26)
        Me.btnPurchase.TabIndex = 43
        Me.btnPurchase.tbAdriftIconWhenHover = False
        Me.btnPurchase.tbAutoSize = False
        Me.btnPurchase.tbAutoSizeEx = False
        Me.btnPurchase.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnPurchase.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnPurchase.tbBadgeNumber = 0
        Me.btnPurchase.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnPurchase.tbEndEllipsis = False
        Me.btnPurchase.tbIconHoldPlace = True
        Me.btnPurchase.tbIconImage = Nothing
        Me.btnPurchase.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnPurchase.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnPurchase.tbIconMore = False
        Me.btnPurchase.tbIconMouseDown = Nothing
        Me.btnPurchase.tbIconMouseHover = Nothing
        Me.btnPurchase.tbIconMouseLeave = Nothing
        Me.btnPurchase.tbIconPlaceText = 2
        Me.btnPurchase.tbIconReadOnly = Nothing
        Me.btnPurchase.tbImageMouseDown = Nothing
        Me.btnPurchase.tbImageMouseHover = Nothing
        Me.btnPurchase.tbImageMouseLeave = Nothing
        Me.btnPurchase.tbProgressValue = 50
        Me.btnPurchase.tbReadOnly = False
        Me.btnPurchase.tbReadOnlyText = False
        Me.btnPurchase.tbShadow = False
        Me.btnPurchase.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnPurchase.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnPurchase.tbShowDot = False
        Me.btnPurchase.tbShowMoreIconImg = CType(resources.GetObject("btnPurchase.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnPurchase.tbShowNew = False
        Me.btnPurchase.tbShowProgress = False
        Me.btnPurchase.tbShowTip = True
        Me.btnPurchase.tbShowToolTipOnButton = False
        Me.btnPurchase.tbSplit = "13,11,13,11"
        Me.btnPurchase.tbText = "购买激活码"
        Me.btnPurchase.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnPurchase.tbTextColor = System.Drawing.Color.White
        Me.btnPurchase.tbTextColorDisable = System.Drawing.Color.White
        Me.btnPurchase.tbTextColorDown = System.Drawing.Color.White
        Me.btnPurchase.tbTextColorHover = System.Drawing.Color.White
        Me.btnPurchase.tbTextMouseDownPlace = 0
        Me.btnPurchase.tbToolTip = ""
        Me.btnPurchase.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnPurchase.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnPurchase.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnPurchase.VisibleEx = True
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pbLoading.BackColor = System.Drawing.Color.Transparent
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.gif_loading_16
        Me.pbLoading.Location = New System.Drawing.Point(572, 567)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(16, 16)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.pbLoading.TabIndex = 73
        Me.pbLoading.TabStop = False
        Me.pbLoading.Visible = False
        '
        'pnlAbroad
        '
        Me.pnlAbroad.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlAbroad.Controls.Add(Me.rtxTrialDesc)
        Me.pnlAbroad.Controls.Add(Me.lblTrialDesc7)
        Me.pnlAbroad.Controls.Add(Me.lblTrialDesc6)
        Me.pnlAbroad.Controls.Add(Me.lblBenefitsDesc)
        Me.pnlAbroad.Controls.Add(Me.lblTrial)
        Me.pnlAbroad.Controls.Add(Me.lblTrialDesc1)
        Me.pnlAbroad.Controls.Add(Me.lblTrialDesc2)
        Me.pnlAbroad.Controls.Add(Me.lblTrialDesc3)
        Me.pnlAbroad.Controls.Add(Me.lblTrialDesc4)
        Me.pnlAbroad.Controls.Add(Me.lblTrialDesc5)
        Me.pnlAbroad.Location = New System.Drawing.Point(12, 46)
        Me.pnlAbroad.Name = "pnlAbroad"
        Me.pnlAbroad.Size = New System.Drawing.Size(576, 444)
        Me.pnlAbroad.TabIndex = 74
        '
        'rtxTrialDesc
        '
        Me.rtxTrialDesc.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.rtxTrialDesc.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(253, Byte), Integer))
        Me.rtxTrialDesc.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.rtxTrialDesc.Location = New System.Drawing.Point(9, 79)
        Me.rtxTrialDesc.Name = "rtxTrialDesc"
        Me.rtxTrialDesc.ReadOnly = True
        Me.rtxTrialDesc.Size = New System.Drawing.Size(555, 357)
        Me.rtxTrialDesc.TabIndex = 16
        Me.rtxTrialDesc.Text = ""
        '
        'lblTrialDesc7
        '
        Me.lblTrialDesc7.BackColor = System.Drawing.Color.Transparent
        Me.lblTrialDesc7.Location = New System.Drawing.Point(6, 214)
        Me.lblTrialDesc7.Name = "lblTrialDesc7"
        Me.lblTrialDesc7.Size = New System.Drawing.Size(400, 22)
        Me.lblTrialDesc7.TabIndex = 15
        Me.lblTrialDesc7.tbAdriftWhenHover = False
        Me.lblTrialDesc7.tbAutoEllipsis = False
        Me.lblTrialDesc7.tbAutoSize = False
        Me.lblTrialDesc7.tbHideImage = False
        Me.lblTrialDesc7.tbIconImage = Nothing
        Me.lblTrialDesc7.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrialDesc7.tbIconPlaceText = 5
        Me.lblTrialDesc7.tbShadow = False
        Me.lblTrialDesc7.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrialDesc7.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrialDesc7.tbShowScrolling = False
        Me.lblTrialDesc7.Text = "7.显示微信找回的删除数据"
        '
        'lblTrialDesc6
        '
        Me.lblTrialDesc6.BackColor = System.Drawing.Color.Transparent
        Me.lblTrialDesc6.Location = New System.Drawing.Point(7, 192)
        Me.lblTrialDesc6.Name = "lblTrialDesc6"
        Me.lblTrialDesc6.Size = New System.Drawing.Size(400, 22)
        Me.lblTrialDesc6.TabIndex = 14
        Me.lblTrialDesc6.tbAdriftWhenHover = False
        Me.lblTrialDesc6.tbAutoEllipsis = False
        Me.lblTrialDesc6.tbAutoSize = False
        Me.lblTrialDesc6.tbHideImage = False
        Me.lblTrialDesc6.tbIconImage = Nothing
        Me.lblTrialDesc6.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrialDesc6.tbIconPlaceText = 5
        Me.lblTrialDesc6.tbShadow = False
        Me.lblTrialDesc6.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrialDesc6.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrialDesc6.tbShowScrolling = False
        Me.lblTrialDesc6.Text = "6.微信文本，媒体导出"
        '
        'pnlWinWeChat
        '
        Me.pnlWinWeChat.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlWinWeChat.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlWinWeChat.Controls.Add(Me.lblTrialVersion)
        Me.pnlWinWeChat.Controls.Add(Me.pnlFeature)
        Me.pnlWinWeChat.Controls.Add(Me.txtWeChatDescribe)
        Me.pnlWinWeChat.Location = New System.Drawing.Point(12, 46)
        Me.pnlWinWeChat.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlWinWeChat.Name = "pnlWinWeChat"
        Me.pnlWinWeChat.Size = New System.Drawing.Size(576, 444)
        Me.pnlWinWeChat.TabIndex = 75
        '
        'lblTrialVersion
        '
        Me.lblTrialVersion.AutoEllipsis = True
        Me.lblTrialVersion.BackColor = System.Drawing.Color.Transparent
        Me.lblTrialVersion.Font = New System.Drawing.Font("宋体", 14.0!)
        Me.lblTrialVersion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblTrialVersion.Location = New System.Drawing.Point(8, 7)
        Me.lblTrialVersion.Name = "lblTrialVersion"
        Me.lblTrialVersion.Size = New System.Drawing.Size(521, 27)
        Me.lblTrialVersion.TabIndex = 77
        Me.lblTrialVersion.tbAdriftWhenHover = False
        Me.lblTrialVersion.tbAutoEllipsis = True
        Me.lblTrialVersion.tbAutoSize = False
        Me.lblTrialVersion.tbHideImage = False
        Me.lblTrialVersion.tbIconImage = Nothing
        Me.lblTrialVersion.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTrialVersion.tbIconPlaceText = 5
        Me.lblTrialVersion.tbShadow = False
        Me.lblTrialVersion.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTrialVersion.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTrialVersion.tbShowScrolling = False
        Me.lblTrialVersion.Text = "激活码："
        Me.lblTrialVersion.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'pnlFeature
        '
        Me.pnlFeature.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.pnlFeature.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.pnlFeature.Location = New System.Drawing.Point(12, 134)
        Me.pnlFeature.Margin = New System.Windows.Forms.Padding(0)
        Me.pnlFeature.Name = "pnlFeature"
        Me.pnlFeature.Size = New System.Drawing.Size(553, 305)
        Me.pnlFeature.TabIndex = 76
        '
        'txtWeChatDescribe
        '
        Me.txtWeChatDescribe.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtWeChatDescribe.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.txtWeChatDescribe.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.txtWeChatDescribe.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.txtWeChatDescribe.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.txtWeChatDescribe.Location = New System.Drawing.Point(12, 45)
        Me.txtWeChatDescribe.Margin = New System.Windows.Forms.Padding(0)
        Me.txtWeChatDescribe.Name = "txtWeChatDescribe"
        Me.txtWeChatDescribe.ReadOnly = True
        Me.txtWeChatDescribe.Size = New System.Drawing.Size(553, 89)
        Me.txtWeChatDescribe.TabIndex = 0
        Me.txtWeChatDescribe.Text = "您当前是试用版：仅支持查看聊天记录，不支持导出功能，不支持查看图片大图，不支持查看删除找回数据，购买正式版即可解锁所有功能。" & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'frmCharge
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(253, Byte), Integer))
        Me.ClientSize = New System.Drawing.Size(600, 607)
        Me.Controls.Add(Me.pbLoading)
        Me.Controls.Add(Me.btnPurchase)
        Me.Controls.Add(Me.btnRegister)
        Me.Controls.Add(Me.lblCode)
        Me.Controls.Add(Me.txtCode)
        Me.Controls.Add(Me.btn_close)
        Me.Controls.Add(Me.pnlWinWeChat)
        Me.Controls.Add(Me.pnlAbroad)
        Me.Controls.Add(Me.btnFreeTry)
        Me.Name = "frmCharge"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "3,45,3,60"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "同步助手(未注册)"
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.pnlAbroad.ResumeLayout(False)
        Me.pnlWinWeChat.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents lblTrial As iTong.Components.tbLabel
    Friend WithEvents lblBenefitsDesc As iTong.Components.tbLabel
    Friend WithEvents lblTrialDesc1 As iTong.Components.tbLabel
    Friend WithEvents lblTrialDesc2 As iTong.Components.tbLabel
    Friend WithEvents lblTrialDesc3 As iTong.Components.tbLabel
    Friend WithEvents lblTrialDesc4 As iTong.Components.tbLabel
    Friend WithEvents lblTrialDesc5 As iTong.Components.tbLabel
    Friend WithEvents lblCode As iTong.Components.tbLabel
    Friend WithEvents txtCode As iTong.Components.tbTextBox
    Friend WithEvents btnFreeTry As iTong.Components.tbButton
    Friend WithEvents btnRegister As iTong.Components.tbButton
    Friend WithEvents btnPurchase As iTong.Components.tbButton
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox
    Friend WithEvents pnlAbroad As System.Windows.Forms.Panel
    Friend WithEvents pnlWinWeChat As System.Windows.Forms.Panel
    Friend WithEvents txtWeChatDescribe As System.Windows.Forms.RichTextBox
    Friend WithEvents pnlFeature As System.Windows.Forms.Panel
    Friend WithEvents lblTrialDesc6 As iTong.Components.tbLabel
    Friend WithEvents lblTrialDesc7 As iTong.Components.tbLabel
    Friend WithEvents rtxTrialDesc As System.Windows.Forms.RichTextBox
    Friend WithEvents lblTrialVersion As iTong.Components.tbLabel
End Class
