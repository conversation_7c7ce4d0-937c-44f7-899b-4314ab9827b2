﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmPayCoexist
    Inherits frmDeviceBase

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmPayCoexist))
        Me.btnUDID = New iTong.Components.tbButton()
        Me.btnPay = New iTong.Components.tbButton()
        Me.btnQQ = New iTong.Components.tbButton()
        Me.btnCancel = New iTong.Components.tbButton()
        Me.txtRemind = New System.Windows.Forms.RichTextBox()
        Me.lblHint = New System.Windows.Forms.Label()
        Me.txtUDID = New System.Windows.Forms.TextBox()
        Me.TbLabel1 = New iTong.Components.tbLabel()
        Me.cbxMeal = New System.Windows.Forms.ComboBox()
        Me.Label1 = New System.Windows.Forms.Label()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(571, 0)
        Me.btn_close.TabIndex = 7
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(547, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(523, 0)
        '
        'btnUDID
        '
        Me.btnUDID.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnUDID.BackColor = System.Drawing.Color.Transparent
        Me.btnUDID.BindingForm = Nothing
        Me.btnUDID.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnUDID.Location = New System.Drawing.Point(23, 278)
        Me.btnUDID.Name = "btnUDID"
        Me.btnUDID.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnUDID.Selectable = True
        Me.btnUDID.Size = New System.Drawing.Size(93, 34)
        Me.btnUDID.TabIndex = 1
        Me.btnUDID.tbAdriftIconWhenHover = False
        Me.btnUDID.tbAutoSize = False
        Me.btnUDID.tbAutoSizeEx = False
        Me.btnUDID.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnUDID.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnUDID.tbBadgeNumber = 0
        Me.btnUDID.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnUDID.tbEndEllipsis = False
        Me.btnUDID.tbIconHoldPlace = True
        Me.btnUDID.tbIconImage = Nothing
        Me.btnUDID.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnUDID.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnUDID.tbIconMore = False
        Me.btnUDID.tbIconMouseDown = Nothing
        Me.btnUDID.tbIconMouseHover = Nothing
        Me.btnUDID.tbIconMouseLeave = Nothing
        Me.btnUDID.tbIconPlaceText = 2
        Me.btnUDID.tbIconReadOnly = Nothing
        Me.btnUDID.tbImageMouseDown = Nothing
        Me.btnUDID.tbImageMouseHover = Nothing
        Me.btnUDID.tbImageMouseLeave = Nothing
        Me.btnUDID.tbProgressValue = 50
        Me.btnUDID.tbReadOnly = False
        Me.btnUDID.tbReadOnlyText = False
        Me.btnUDID.tbShadow = False
        Me.btnUDID.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnUDID.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnUDID.tbShowDot = False
        Me.btnUDID.tbShowMoreIconImg = CType(resources.GetObject("btnUDID.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnUDID.tbShowNew = False
        Me.btnUDID.tbShowProgress = False
        Me.btnUDID.tbShowTip = True
        Me.btnUDID.tbShowToolTipOnButton = False
        Me.btnUDID.tbSplit = "13,11,13,11"
        Me.btnUDID.tbText = "获取UDID"
        Me.btnUDID.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnUDID.tbTextColor = System.Drawing.Color.White
        Me.btnUDID.tbTextColorDisable = System.Drawing.Color.White
        Me.btnUDID.tbTextColorDown = System.Drawing.Color.White
        Me.btnUDID.tbTextColorHover = System.Drawing.Color.White
        Me.btnUDID.tbTextMouseDownPlace = 0
        Me.btnUDID.tbToolTip = ""
        Me.btnUDID.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnUDID.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnUDID.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnUDID.Visible = False
        Me.btnUDID.VisibleEx = True
        '
        'btnPay
        '
        Me.btnPay.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnPay.BackColor = System.Drawing.Color.Transparent
        Me.btnPay.BindingForm = Nothing
        Me.btnPay.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnPay.Location = New System.Drawing.Point(262, 278)
        Me.btnPay.Name = "btnPay"
        Me.btnPay.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnPay.Selectable = True
        Me.btnPay.Size = New System.Drawing.Size(93, 34)
        Me.btnPay.TabIndex = 0
        Me.btnPay.tbAdriftIconWhenHover = False
        Me.btnPay.tbAutoSize = False
        Me.btnPay.tbAutoSizeEx = False
        Me.btnPay.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnPay.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnPay.tbBadgeNumber = 0
        Me.btnPay.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnPay.tbEndEllipsis = False
        Me.btnPay.tbIconHoldPlace = True
        Me.btnPay.tbIconImage = Nothing
        Me.btnPay.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnPay.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnPay.tbIconMore = False
        Me.btnPay.tbIconMouseDown = Nothing
        Me.btnPay.tbIconMouseHover = Nothing
        Me.btnPay.tbIconMouseLeave = Nothing
        Me.btnPay.tbIconPlaceText = 2
        Me.btnPay.tbIconReadOnly = Nothing
        Me.btnPay.tbImageMouseDown = Nothing
        Me.btnPay.tbImageMouseHover = Nothing
        Me.btnPay.tbImageMouseLeave = Nothing
        Me.btnPay.tbProgressValue = 50
        Me.btnPay.tbReadOnly = False
        Me.btnPay.tbReadOnlyText = False
        Me.btnPay.tbShadow = False
        Me.btnPay.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnPay.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnPay.tbShowDot = False
        Me.btnPay.tbShowMoreIconImg = CType(resources.GetObject("btnPay.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnPay.tbShowNew = False
        Me.btnPay.tbShowProgress = False
        Me.btnPay.tbShowTip = True
        Me.btnPay.tbShowToolTipOnButton = False
        Me.btnPay.tbSplit = "13,11,13,11"
        Me.btnPay.tbText = "付费"
        Me.btnPay.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnPay.tbTextColor = System.Drawing.Color.White
        Me.btnPay.tbTextColorDisable = System.Drawing.Color.White
        Me.btnPay.tbTextColorDown = System.Drawing.Color.White
        Me.btnPay.tbTextColorHover = System.Drawing.Color.White
        Me.btnPay.tbTextMouseDownPlace = 0
        Me.btnPay.tbToolTip = ""
        Me.btnPay.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnPay.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnPay.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnPay.VisibleEx = True
        '
        'btnQQ
        '
        Me.btnQQ.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnQQ.BackColor = System.Drawing.Color.Transparent
        Me.btnQQ.BindingForm = Nothing
        Me.btnQQ.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnQQ.Location = New System.Drawing.Point(367, 278)
        Me.btnQQ.Name = "btnQQ"
        Me.btnQQ.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnQQ.Selectable = True
        Me.btnQQ.Size = New System.Drawing.Size(93, 34)
        Me.btnQQ.TabIndex = 3
        Me.btnQQ.tbAdriftIconWhenHover = False
        Me.btnQQ.tbAutoSize = False
        Me.btnQQ.tbAutoSizeEx = False
        Me.btnQQ.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnQQ.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnQQ.tbBadgeNumber = 0
        Me.btnQQ.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnQQ.tbEndEllipsis = False
        Me.btnQQ.tbIconHoldPlace = True
        Me.btnQQ.tbIconImage = Nothing
        Me.btnQQ.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnQQ.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnQQ.tbIconMore = False
        Me.btnQQ.tbIconMouseDown = Nothing
        Me.btnQQ.tbIconMouseHover = Nothing
        Me.btnQQ.tbIconMouseLeave = Nothing
        Me.btnQQ.tbIconPlaceText = 2
        Me.btnQQ.tbIconReadOnly = Nothing
        Me.btnQQ.tbImageMouseDown = Nothing
        Me.btnQQ.tbImageMouseHover = Nothing
        Me.btnQQ.tbImageMouseLeave = Nothing
        Me.btnQQ.tbProgressValue = 50
        Me.btnQQ.tbReadOnly = False
        Me.btnQQ.tbReadOnlyText = False
        Me.btnQQ.tbShadow = False
        Me.btnQQ.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnQQ.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnQQ.tbShowDot = False
        Me.btnQQ.tbShowMoreIconImg = CType(resources.GetObject("btnQQ.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnQQ.tbShowNew = False
        Me.btnQQ.tbShowProgress = False
        Me.btnQQ.tbShowTip = True
        Me.btnQQ.tbShowToolTipOnButton = False
        Me.btnQQ.tbSplit = "13,11,13,11"
        Me.btnQQ.tbText = "联系客服"
        Me.btnQQ.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnQQ.tbTextColor = System.Drawing.Color.White
        Me.btnQQ.tbTextColorDisable = System.Drawing.Color.White
        Me.btnQQ.tbTextColorDown = System.Drawing.Color.White
        Me.btnQQ.tbTextColorHover = System.Drawing.Color.White
        Me.btnQQ.tbTextMouseDownPlace = 0
        Me.btnQQ.tbToolTip = ""
        Me.btnQQ.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnQQ.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnQQ.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnQQ.VisibleEx = True
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.Location = New System.Drawing.Point(472, 278)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(93, 34)
        Me.btnCancel.TabIndex = 5
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbProgressValue = 50
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowDot = False
        Me.btnCancel.tbShowMoreIconImg = CType(resources.GetObject("btnCancel.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowProgress = False
        Me.btnCancel.tbShowTip = True
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.White
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.White
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.White
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.White
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'txtRemind
        '
        Me.txtRemind.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.txtRemind.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.txtRemind.Location = New System.Drawing.Point(32, 44)
        Me.txtRemind.Name = "txtRemind"
        Me.txtRemind.ReadOnly = True
        Me.txtRemind.Size = New System.Drawing.Size(533, 142)
        Me.txtRemind.TabIndex = 6
        Me.txtRemind.Text = "付费操作提示：" & Global.Microsoft.VisualBasic.ChrW(10) & "1.请将你要安装分身版的手机连接到同步助手（不能同时连接多台）；" & Global.Microsoft.VisualBasic.ChrW(10) & "2.请选择你要购买的版本；" & Global.Microsoft.VisualBasic.ChrW(10) & "3.选择后点击「付费」按钮，进行付费；" & Global.Microsoft.VisualBasic.ChrW(10) & "4.付费成功" & _
    "后请点击「联系客服」，将UDID发送给客服。" & Global.Microsoft.VisualBasic.ChrW(10) & "提示：实时通知需要保持在后台才能收到通知， 会比较耗电。技术支持一年。" & Global.Microsoft.VisualBasic.ChrW(10)
        '
        'lblHint
        '
        Me.lblHint.AutoSize = True
        Me.lblHint.BackColor = System.Drawing.Color.Transparent
        Me.lblHint.ForeColor = System.Drawing.Color.Red
        Me.lblHint.Location = New System.Drawing.Point(106, 261)
        Me.lblHint.Name = "lblHint"
        Me.lblHint.Size = New System.Drawing.Size(53, 12)
        Me.lblHint.TabIndex = 51
        Me.lblHint.Text = "复制成功"
        Me.lblHint.Visible = False
        '
        'txtUDID
        '
        Me.txtUDID.BackColor = System.Drawing.Color.FromArgb(CType(CType(250, Byte), Integer), CType(CType(251, Byte), Integer), CType(CType(252, Byte), Integer))
        Me.txtUDID.Location = New System.Drawing.Point(103, 236)
        Me.txtUDID.Name = "txtUDID"
        Me.txtUDID.ReadOnly = True
        Me.txtUDID.Size = New System.Drawing.Size(462, 21)
        Me.txtUDID.TabIndex = 2
        Me.txtUDID.Text = "点击「付费」后程序会自动获取UDID。"
        '
        'TbLabel1
        '
        Me.TbLabel1.AutoSize = True
        Me.TbLabel1.BackColor = System.Drawing.Color.Transparent
        Me.TbLabel1.Location = New System.Drawing.Point(30, 207)
        Me.TbLabel1.Name = "TbLabel1"
        Me.TbLabel1.Size = New System.Drawing.Size(77, 12)
        Me.TbLabel1.TabIndex = 54
        Me.TbLabel1.tbAdriftWhenHover = False
        Me.TbLabel1.tbAutoEllipsis = False
        Me.TbLabel1.tbAutoSize = True
        Me.TbLabel1.tbHideImage = False
        Me.TbLabel1.tbIconImage = Nothing
        Me.TbLabel1.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.TbLabel1.tbIconPlaceText = 5
        Me.TbLabel1.tbShadow = False
        Me.TbLabel1.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.TbLabel1.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.TbLabel1.tbShowScrolling = False
        Me.TbLabel1.Text = "请选择版本："
        '
        'cbxMeal
        '
        Me.cbxMeal.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList
        Me.cbxMeal.FormattingEnabled = True
        Me.cbxMeal.Items.AddRange(New Object() {"59", "79"})
        Me.cbxMeal.Location = New System.Drawing.Point(103, 203)
        Me.cbxMeal.Name = "cbxMeal"
        Me.cbxMeal.Size = New System.Drawing.Size(462, 20)
        Me.cbxMeal.TabIndex = 4
        '
        'Label1
        '
        Me.Label1.AutoSize = True
        Me.Label1.BackColor = System.Drawing.Color.Transparent
        Me.Label1.ForeColor = System.Drawing.Color.Black
        Me.Label1.Location = New System.Drawing.Point(65, 241)
        Me.Label1.Name = "Label1"
        Me.Label1.Size = New System.Drawing.Size(41, 12)
        Me.Label1.TabIndex = 55
        Me.Label1.Text = "UDID："
        '
        'frmPayCoexist
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(595, 334)
        Me.Controls.Add(Me.txtUDID)
        Me.Controls.Add(Me.Label1)
        Me.Controls.Add(Me.cbxMeal)
        Me.Controls.Add(Me.TbLabel1)
        Me.Controls.Add(Me.lblHint)
        Me.Controls.Add(Me.txtRemind)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnQQ)
        Me.Controls.Add(Me.btnPay)
        Me.Controls.Add(Me.btnUDID)
        Me.Name = "frmPayCoexist"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowIconOnForm = False
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "3,35,3,60"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "共存版付费"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.btnUDID, 0)
        Me.Controls.SetChildIndex(Me.btnPay, 0)
        Me.Controls.SetChildIndex(Me.btnQQ, 0)
        Me.Controls.SetChildIndex(Me.btnCancel, 0)
        Me.Controls.SetChildIndex(Me.txtRemind, 0)
        Me.Controls.SetChildIndex(Me.lblHint, 0)
        Me.Controls.SetChildIndex(Me.TbLabel1, 0)
        Me.Controls.SetChildIndex(Me.cbxMeal, 0)
        Me.Controls.SetChildIndex(Me.Label1, 0)
        Me.Controls.SetChildIndex(Me.txtUDID, 0)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btnUDID As iTong.Components.tbButton
    Friend WithEvents btnPay As iTong.Components.tbButton
    Friend WithEvents btnQQ As iTong.Components.tbButton
    Friend WithEvents btnCancel As iTong.Components.tbButton
    Friend WithEvents txtRemind As System.Windows.Forms.RichTextBox
    Friend WithEvents lblHint As System.Windows.Forms.Label
    Friend WithEvents txtUDID As System.Windows.Forms.TextBox
    Friend WithEvents TbLabel1 As iTong.Components.tbLabel
    Friend WithEvents cbxMeal As System.Windows.Forms.ComboBox
    Friend WithEvents Label1 As System.Windows.Forms.Label
End Class
