﻿Imports System.Threading


Public Class frmCharge

    Private mDgvFeatureList As New tbDataGridViewEx
    Private mTDLoadImprint As Thread = Nothing
    Private Shared mChargeHelper As ChargeHelper = Nothing
    Private Shared mWCMCInfo As New WeChatMigrationCharge
    Private mOverDeviceCount As Boolean = False

    Sub New(ByVal cHepler As ChargeHelper, ByVal wcmcInfo As WeChatMigrationCharge)
        Me.New(cHepler, wcmcInfo, False)
    End Sub

    Sub New(ByVal cHepler As ChargeHelper, ByVal wcmcInfo As WeChatMigrationCharge, ByVal blnOverDeviceCount As Boolean)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Icon = My.Resources.iTong
        Me.Language = LanguageInterface.Instance()
        Me.mOverDeviceCount = blnOverDeviceCount
        mChargeHelper = cHepler
        mWCMCInfo = wcmcInfo
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.InitGridView()
        If mChargeHelper.mChargeInfo.PayVersion = PayVersions.WinWeChatManage OrElse _
           mChargeHelper.mChargeInfo.PayVersion = PayVersions.iTongWeChatAssistant OrElse _
           mChargeHelper.mChargeInfo.PayVersion = PayVersions.iDataRecovery OrElse _
           mChargeHelper.mChargeInfo.PayVersion = PayVersions.iTongAndroidMedia OrElse _
           mChargeHelper.mChargeInfo.PayVersion = PayVersions.iWeChatMigration Then

            Me.Size = New Size(600, 530)
            Me.pnlWinWeChat.Visible = True
            Me.pnlAbroad.Location = New Point(12, 46)
            Me.pnlAbroad.Size = New Size(576, 444)
            Me.pnlAbroad.Visible = False

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iDataRecovery_Abroad Then
            Me.Size = New Size(560, 445)
            Me.pnlWinWeChat.Visible = True
            Me.pnlAbroad.Location = New Point(12, 46)
            Me.pnlAbroad.Size = New Size(536, 292)
            Me.pnlAbroad.Visible = False

        Else

            Me.Size = New Size(550, 470)
            Me.pnlAbroad.Visible = True
            Me.pnlAbroad.Location = New Point(12, 46)
            Me.pnlAbroad.Size = New Size(536, 352)
            Me.pnlWinWeChat.Visible = False
        End If

        If Me.mTDLoadImprint IsNot Nothing AndAlso Me.mTDLoadImprint.ThreadState <> ThreadState.Stopped Then
            Return
        End If
        Me.mTDLoadImprint = New Thread(AddressOf DoLoadImprint)
        Me.mTDLoadImprint.IsBackground = True
        Me.mTDLoadImprint.Start()

        If Folder.AppType = RunType.iDataRecovery_Abroad OrElse Folder.AppType = RunType.iDataRecovery Then
            Me.tbGuiBackground = My.Resources.Resources.frm_bg_state1
            Me.btnRegister.tbBackgroundImage = My.Resources.Resources.btn_4_white2
            Me.btn_close.tbBackgroundImage = My.Resources.Resources.btn_close1

            Me.pnlWinWeChat.BackColor = System.Drawing.Color.FromArgb(255, 255, 255)
            Me.txtWeChatDescribe.BackColor = System.Drawing.Color.FromArgb(255, 255, 255)
            Me.BackColor = System.Drawing.Color.FromArgb(255, 255, 255)
            Me.btnRegister.ForeColor = Color.FromArgb(51, 51, 51)
            Me.btnRegister.tbTextColor = Color.FromArgb(51, 51, 51)

            Me.tbTitleForeColor = Color.FromArgb(51, 51, 51)
        End If

        If Common.GetChannelID() = "z8VfZafJCcw=" Then
            Me.btnPurchase.Visible = False
        Else
            Me.btnPurchase.Visible = True
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()
        Dim strTest As String = ""

        Me.txtWeChatDescribe.Clear()
        Me.txtWeChatDescribe.AppendText("解锁前可以对设备进行扫描、预览数据，不可恢复。")
        Me.txtWeChatDescribe.AppendText(vbCrLf)
        Me.txtWeChatDescribe.AppendText(vbCrLf)
        Me.txtWeChatDescribe.AppendText("点击下方的‘购买激活码’按钮即可在线购买激活码，购买成功几分钟后，您填写的邮箱将会收到含有激活码的邮件。")

        Me.lblCode.Text = Me.Language.GetString("MainVip.Message.LicenseKey")  '"激活码："
        Me.txtCode.TextTip = Me.Language.GetString("MainVip.Message.EnterLicense")  '"请输入激活码"
        Me.txtCode.Text = String.Empty
        Me.btnFreeTry.Text = Me.Language.GetString("MainVip.Button.FreeTrial") '"免费试用"
        Me.btnPurchase.Text = Me.Language.GetString("MainVip.Button.Purchase") '"购买"
        Me.btnRegister.Text = Me.Language.GetString("MainVip.Button.Register") '"激活"

        If mChargeHelper.mChargeInfo.PayVersion = PayVersions.WinWeChatManage Then
            Me.lblTrialVersion.Text = Me.Language.GetString("Chage.Lable.Describe") '"您当前是试用版：购买正式版即可解锁所有功能。"
            Me.btnFreeTry.Enabled = True
            strTest = Me.Language.GetString("Chage.Lable.Activate") '"激活正式版"
            If mChargeHelper.mChargeInfo.Registered Then
                strTest = Me.Language.GetString("Chage.Lable.Renewal") '"续期正式版"
                Me.btnFreeTry.Enabled = False
                Me.lblTrialVersion.Text = String.Format("您当前是正式版，过期时间：{0}", mChargeHelper.mChargeInfo.EndTime.ToString("yyyy-MM-dd"))
            ElseIf Not mChargeHelper.mChargeInfo.Registered AndAlso mChargeHelper.mChargeInfo.Overdue Then
                strTest = Me.Language.GetString("Chage.Lable.Overdue") '"续期正式版(已过期)"
            End If

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iWeChatMigration Then
            Me.lblTrialVersion.Text = Me.Language.GetString("Chage.Lable.Describe") '"您当前是试用版：购买正式版即可解锁所有功能。"
            Me.btnFreeTry.Enabled = True
            strTest = Me.Language.GetString("Chage.Lable.Activate") '"激活正式版"
            If mWCMCInfo.WCMCType = WCMCType.Official Then
                strTest = Me.Language.GetString("Chage.Lable.Renewal") '"续期正式版"
                Me.btnFreeTry.Enabled = False
                Me.lblTrialVersion.Text = String.Format("您当前是正式版，过期时间：{0}", mWCMCInfo.Endtime.ToString("yyyy-MM-dd"))
            ElseIf mWCMCInfo.WCMCType = WCMCType.PastDue Then
                strTest = Me.Language.GetString("Chage.Lable.Overdue") '"续期正式版(已过期)"
            End If

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.AbroadAssistant Then
            strTest = String.Format("{0}({1})", Me.Language.GetString("Common.Tongbu"), Me.Language.GetString("MainVip.Message.Unregistered"))  'String.Format("{0}({1})", "同步助手", "未注册")
            If mChargeHelper.mChargeInfo.Registered Then
                'strTest = String.Format("{0}({1}{2})", "同步助手", "过期时间：", mChargeHelper.ChargeObj.EndTime.ToString("yyyy-MM-dd"))
                strTest = String.Format("{0}({1}{2})", Me.Language.GetString("Common.Tongbu"), Me.Language.GetString("MainVip.Message.OverdueDate"), mChargeHelper.mChargeInfo.EndTime.ToString("yyyy-MM-dd"))
            ElseIf Not mChargeHelper.mChargeInfo.Registered AndAlso mChargeHelper.mChargeInfo.Overdue Then
                'strTest = String.Format("{0}({1}{2})", "同步助手", "已过期:", mChargeHelper.ChargeObj.EndTime.ToString("yyyy-MM-dd"))
                strTest = String.Format("{0}({1}{2})", Me.Language.GetString("Common.Tongbu"), Me.Language.GetString("MainVip.Message.Expired"), mChargeHelper.mChargeInfo.EndTime.ToString("yyyy-MM-dd"))
            End If

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iTongWeChatAssistant OrElse mChargeHelper.mChargeInfo.PayVersion = PayVersions.iTongAndroidMedia Then
            Me.btnFreeTry.Visible = False
            strTest = Me.Language.GetString("Chage.Lable.WeChatClear") '"解锁微信功能"

            Me.lblTrialVersion.Text = Me.Language.GetString("Chage.Lable.WeChatPayClear") '"您尚未解锁此功能，购买后激活即可解锁全部功能。"
            If mChargeHelper.mChargeInfo.Registered Then
                strTest = Me.Language.GetString("Chage.Lable.Renewal") '"续期正式版"
                Me.lblTrialVersion.Text = String.Format("{0}，{1}{2}", Me.Language.GetString("Chage.Lable.AllClear"), Me.Language.GetString("MainVip.Message.OverdueDate"), mChargeHelper.mChargeInfo.EndTime.ToString("yyyy-MM-dd")) '"您已解锁全部功能，"
            ElseIf Not mChargeHelper.mChargeInfo.Registered AndAlso mChargeHelper.mChargeInfo.Overdue Then
                'strTest = String.Format("{0}({1}{2})", "同步助手", "已过期:", mChargeHelper.ChargeObj.EndTime.ToString("yyyy-MM-dd"))
                Me.lblTrialVersion.Text = String.Format("{0}({1}:{2})", Me.Language.GetString("Chage.Lable.ActivateOverdue"), Me.Language.GetString("MainVip.Message.Expired"), mChargeHelper.mChargeInfo.EndTime.ToString("yyyy-MM-dd")) ' "您的激活码已经过期，"
            End If

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iDataRecovery_Abroad OrElse mChargeHelper.mChargeInfo.PayVersion = PayVersions.iDataRecovery Then

            strTest = Me.Language.GetString("Chage.Lable.BuyNow") '新语言

            Me.lblTrialVersion.Text = Me.Language.GetString("Chage.Lable.TrialVersion") '新语言
            If mChargeHelper.mChargeInfo.Registered Then
                Me.lblTrialVersion.Text = String.Format("{0}，{1}{2}", Me.Language.GetString("Chage.Lable.AllClear"), Me.Language.GetString("MainVip.Message.OverdueDate"), mChargeHelper.mChargeInfo.EndTime.ToString("yyyy-MM-dd"))  '新语言
            ElseIf Not mChargeHelper.mChargeInfo.Registered AndAlso mChargeHelper.mChargeInfo.Overdue Then
                Me.lblTrialVersion.Text = String.Format("{0}({1}:{2})", Me.Language.GetString("Chage.Lable.ActivateOverdue"), Me.Language.GetString("MainVip.Message.Expired"), mChargeHelper.mChargeInfo.EndTime.ToString("yyyy-MM-dd")) '新语言
            End If

            Me.txtWeChatDescribe.Text = Me.Language.GetString("Chage.Lable.PaidDescribe") '新语言

            Me.lblCode.Text = Me.Language.GetString("Chage.Lable.Keycode")  '新语言

            Me.txtCode.TextTip = Me.Language.GetString("Chage.TextBox.KeycodeHint")  '新语言
            Me.txtCode.Text = String.Empty
            Me.btnFreeTry.Text = Me.Language.GetString("Chage.Button.FreeTrial") '新语言
            Me.btnPurchase.Text = Me.Language.GetString("Chage.Button.BuyNow") '新语言
            Me.btnRegister.Text = Me.Language.GetString("Chage.Button.Register") '新语言

        End If

        Me.Text = strTest

        Me.lblBenefitsDesc.Text = Me.Language.GetString("MainVip.Message.PurchaseTip")    '"您使用的是未注册的版本，移除试用版限制请从我们的商店购买激活码。"
        Me.lblTrial.Text = Me.Language.GetString("MainVip.Message.TrialVersion")   '"试用版功能："
        Me.lblTrialDesc1.Text = String.Format(Me.Language.GetString("MainVip.Message.PhotoLimitInfo"), mChargeHelper.mChargeInfo.CanExportPhotos)     'String.Format("1.相片：可导出30张（还剩{0}张）", mChargeHelper.ChargeObj.CanExportPhotos)
        Me.lblTrialDesc2.Text = String.Format(Me.Language.GetString("MainVip.Message.MediaLimitInfo"), mChargeHelper.mChargeInfo.CanExportMedias, vbCrLf) 'String.Format("2.音乐铃声、电影、电视节目、Podcasts、iTunes U、有声视频、{1}备忘录、书籍可导出30（还剩{0}）", mChargeHelper.ChargeObj.CanExportMedias, vbCrLf)
        Me.lblTrialDesc3.Text = String.Format(Me.Language.GetString("MainVip.Message.ContactsLimitInfo"), mChargeHelper.mChargeInfo.CanExportContacts)   'String.Format("3.联系人：可导出30个（还剩{0}个）", mChargeHelper.ChargeObj.CanExportContacts)
        Me.lblTrialDesc4.Text = String.Format(Me.Language.GetString("MainVip.Message.SMSLimitInfo"), mChargeHelper.mChargeInfo.CanExportSMS)   'String.Format("4.短信：可导出3个联系人记录（还剩{0}个）", mChargeHelper.ChargeObj.CanExportSMS)
        Me.lblTrialDesc5.Text = String.Format(Me.Language.GetString("MainVip.Message.VideoLimitInfo"))   '"5.导入视频文件前3分钟"

        Me.lblTrialDesc6.Text = Me.Language.GetString("MainVip.Message.WeChatLimitInfo")
        Me.lblTrialDesc7.Text = Me.Language.GetString("MainVip.Message.WeChat1LimitInfo")

        Me.rtxTrialDesc.Clear()
        Me.rtxTrialDesc.AppendText(String.Format(Me.Language.GetString("MainVip.Message.PhotoLimitInfo"), mChargeHelper.mChargeInfo.CanExportPhotos))
        Me.rtxTrialDesc.AppendText(vbCrLf)

        Me.rtxTrialDesc.AppendText(String.Format(Me.Language.GetString("MainVip.Message.MediaLimitInfo"), mChargeHelper.mChargeInfo.CanExportMedias, vbCrLf))
        Me.rtxTrialDesc.AppendText(vbCrLf)

        Me.rtxTrialDesc.AppendText(String.Format(Me.Language.GetString("MainVip.Message.ContactsLimitInfo"), mChargeHelper.mChargeInfo.CanExportContacts))
        Me.rtxTrialDesc.AppendText(vbCrLf)

        Me.rtxTrialDesc.AppendText(String.Format(Me.Language.GetString("MainVip.Message.SMSLimitInfo"), mChargeHelper.mChargeInfo.CanExportSMS))
        Me.rtxTrialDesc.AppendText(vbCrLf)

        Me.rtxTrialDesc.AppendText(String.Format(Me.Language.GetString("MainVip.Message.VideoLimitInfo")))
        Me.rtxTrialDesc.AppendText(vbCrLf)

        Me.rtxTrialDesc.AppendText(Me.Language.GetString("MainVip.Message.WeChatLimitInfo")) '"6.微信文本，媒体导出"
        Me.rtxTrialDesc.AppendText(vbCrLf)

        Me.rtxTrialDesc.AppendText(Me.Language.GetString("MainVip.Message.WeChat1LimitInfo")) '"7.找回微信删除的数据"

        If Me.mOverDeviceCount Then
            Me.lblTrialVersion.Text = Me.Language.GetString("MainVip.Message.DeviceOverAmountEx")
        End If
    End Sub

    Protected Overrides Sub OnClosing(e As System.ComponentModel.CancelEventArgs)
        MyBase.OnClosing(e)

        Try
            If Me.mTDLoadImprint IsNot Nothing AndAlso Me.mTDLoadImprint.ThreadState <> ThreadState.Stopped Then
                Me.mTDLoadImprint.Abort()
            End If
        Catch
        End Try

    End Sub

    Private Sub InitGridView()
        Me.mDgvFeatureList = New tbDataGridViewEx
        With Me.mDgvFeatureList
            .SuspendLayout()
            .tbMouseHoverResponse = True
            .RowTemplate.Height = 29
            .AllowDrop = True
            .MultiSelect = True
            .tbNoDataText = Me.Language.GetString("Media.Label.Empty")
            .ShowCellToolTips = True
            .Dock = DockStyle.Fill
            .RowsDefaultCellStyle.BackColor = Color.White
            .AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(250, 250, 250)
            .RowsDefaultCellStyle.SelectionBackColor = Color.FromArgb(226, 244, 251)
            .BackgroundColor = Color.White
            .HeaderImageBackground = My.Resources.dgv_bg_header_green
            'If Folder.AppType = RunType.iDataRecovery_Abroad Then
            '    .HeaderImageBackground = My.Resources.dgv_bg_header_blue
            'End If
            .IsHeaderTextBold = True
            .Columns.Clear()

            Dim strMainBenefits As String = "主要功能"
            Dim strTrial As String = "试用版"
            Dim strRegistered As String = "标准/高级版"
            Dim strPhone As String = "恢复到手机版"
            If Folder.AppType = RunType.iDataRecovery_Abroad Then
                strMainBenefits = Me.Language.GetString("Chage.Lable.MainBenefits") '新语言
                strTrial = Me.Language.GetString("Chage.Lable.Trial") '新语言
                strRegistered = Me.Language.GetString("Chage.Lable.Registered") '新语言
            End If

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumnEx), "colName", strMainBenefits, _
                                                                   282, True, False, DataGridViewContentAlignment.MiddleCenter, False, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewImageColumn), "colFlash1", strTrial, _
                                                                   85, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewImageColumn), "colFlash2", strRegistered, _
                                                                   90, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewImageColumn), "colFlash3", strPhone, _
                                                       95, True, False, DataGridViewContentAlignment.MiddleCenter, True, DataGridViewTriState.False))

            .Columns.Add(tbDataGridViewHelper.CreateDataGridColumn(GetType(tbDataGridViewTextBoxColumn), "colOther", "", _
                                                                   5, False, True, DataGridViewContentAlignment.MiddleLeft, True))

            .ResumeLayout()
        End With
        Me.pnlFeature.Controls.Add(Me.mDgvFeatureList)
    End Sub

    Private Sub btnFreeTry_Click(sender As Object, e As EventArgs) Handles btnFreeTry.Click
        Me.Close()
    End Sub

    Private Sub btnPurchase_Click(sender As Object, e As EventArgs) Handles btnPurchase.Click
        Dim strUrlResult As String = String.Empty
        Dim strChannel As String = System.Web.HttpUtility.UrlEncode(Common.GetChannelID)

        If mChargeHelper.mChargeInfo.PayVersion = PayVersions.AbroadAssistant Then
            strUrlResult = WebUrl.PageAbroadPurchase

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.WinWeChatManage Then
            Common.OpenExplorer(WebUrl.PageWinWeChatPurchase)

            '微信管理大师里面已经有用到s的参数，所以用自己的逻辑
            Return

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iTongWeChatAssistant Then
            strUrlResult = WebUrl.PageiTongWeChatAssistant

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iDataRecovery Then
            strUrlResult = WebUrl.PageDataRecoveryPurchase

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iTongAndroidMedia Then
            strUrlResult = WebUrl.PageAndroidWechatMediaPurchase

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iWeChatMigration Then
            strUrlResult = WebUrl.PageiWeChatMigrationPurchase

        ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iDataRecovery_Abroad Then
            strUrlResult = WebUrl.PageiWeChatMigrationAbroadPurchase

        End If

        '和web约定，现在的链接中用os来代表渠道号，s就可以去掉了
        If Not strUrlResult.Contains(String.Format("c_s={0}", strChannel)) Then
            If strUrlResult.Contains("?") Then
                strUrlResult = String.Format("{0}&c_s={1}", strUrlResult, strChannel)
            Else
                strUrlResult = String.Format("{0}?c_s={1}", strUrlResult, strChannel)
            End If
        End If

        Common.OpenExplorer(strUrlResult)
    End Sub

    Private Sub btnRegister_Click(sender As Object, e As EventArgs) Handles btnRegister.Click
        Me.DoRegister()
    End Sub

    Private Sub txtCode_KeyDown(sender As Object, e As KeyEventArgs) Handles txtCode.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.DoRegister()
        End If
    End Sub

    Private Sub DoRegister()
        Dim strCode As String = Me.txtCode.Text.Trim()
        If String.IsNullOrEmpty(strCode) Then
            Dim strLicenseCouldntEmpty As String = Me.Language.GetString("MainVip.Message.LicenseCouldntEmpty")
            If Folder.AppType = RunType.iDataRecovery_Abroad OrElse Folder.AppType = RunType.iDataRecovery Then
                strLicenseCouldntEmpty = Me.Language.GetString("Chage.Message.LicenseCouldntEmpty") '新语言
            End If
            tbMessageBox.Show(Me, strLicenseCouldntEmpty, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
            Me.txtCode.Focus()
            Return
        End If
        Dim thr As New Thread(New ParameterizedThreadStart(AddressOf Register))
        With thr
            .IsBackground = True
            .Start(strCode)
        End With
    End Sub

    Private Sub Register(ByVal objCode As Object)
        Dim strCode As String = objCode
        Me.SetControlEnable(False)
        Dim blnRegistered As Boolean = mChargeHelper.mChargeInfo.Registered  '判断是不是续期
        Dim blnCurrent As Boolean = mChargeHelper.Registration(strCode, mWCMCInfo)
        Me.SetControlEnable(True)

        '注册成功
        If Not blnRegistered AndAlso mChargeHelper.mChargeInfo.Registered = True Then
            Me.DialogResult = Windows.Forms.DialogResult.OK
            MeClose()

        ElseIf blnRegistered AndAlso blnCurrent Then
            '续期逻辑
            Me.DialogResult = Windows.Forms.DialogResult.OK
            MeClose()

        Else
            Me.SetCodeFocus()

        End If
    End Sub

    Private Delegate Sub SetCodeFocusHandler()
    Private Sub SetCodeFocus()
        If Me.InvokeRequired Then
            Me.Invoke(New SetCodeFocusHandler(AddressOf SetCodeFocus))
        Else
            Me.txtCode.Focus()
        End If
    End Sub

    Private Delegate Sub MeCloseHandler()
    Private Sub MeClose()
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New MeCloseHandler(AddressOf MeClose))
            Else
                Me.Close()
            End If
        Catch
        End Try
    End Sub


    Private Delegate Sub SetControlEnableHandler(ByVal blnEnable As Boolean)
    Private Sub SetControlEnable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetControlEnableHandler(AddressOf SetControlEnable), blnEnable)
        Else
            Me.btnRegister.Enabled = blnEnable
            Me.btnFreeTry.Enabled = blnEnable
            Me.txtCode.Enabled = blnEnable
            Me.pbLoading.Visible = Not blnEnable
        End If
    End Sub

    Private Sub DoLoadImprint()
        Try
            Dim lstImprint As New List(Of ImprintInfo)
            Dim info As New ImprintInfo
            If mChargeHelper.mChargeInfo.PayVersion = PayVersions.WinWeChatManage OrElse _
                       mChargeHelper.mChargeInfo.PayVersion = PayVersions.iTongWeChatAssistant Then


                info = New ImprintInfo
                info.StrDescribe = "查看未删除的微信记录"
                info.BlnOfficial = True
                info.BlnTry = True
                info.BlnPhone = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "备份/还原微信记录"
                info.BlnOfficial = True
                info.BlnTry = True
                info.BlnPhone = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "扫描、预览可恢复的微信数据"
                info.BlnOfficial = ServerIniSetting.GetPreviewDeletedChat()
                info.BlnTry = True
                info.BlnPhone = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "导出可恢复的数据到电脑"
                info.BlnOfficial = False
                info.BlnTry = True
                info.BlnPhone = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "导出聊天消息到电脑（html、txt、excel）"
                info.BlnOfficial = False
                info.BlnTry = True
                info.BlnPhone = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "导出图片/语音/视频到电脑"
                info.BlnOfficial = False
                info.BlnTry = True
                info.BlnPhone = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "恢复删除的聊天记录到手机"
                info.BlnOfficial = False
                info.BlnTry = False
                info.BlnPhone = True
                lstImprint.Add(info)

            ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iTongAndroidMedia Then
                info = New ImprintInfo
                info.StrDescribe = "查看微信媒体文件"
                info.BlnOfficial = True
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "批量导出图片"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "批量导出语音"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "批量导出小视频"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "批量导出文件"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)
            ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iWeChatMigration Then
                info.StrDescribe = "查看未删除的微信记录"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "数据迁移"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "扫描、预览可恢复的微信数据"
                info.BlnOfficial = ServerIniSetting.GetPreviewDeletedChat()
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "导出可恢复的数据到电脑"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "导出聊天消息到电脑（html、txt、excel）"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = "导出图片/语音/视频到电脑"
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

            ElseIf mChargeHelper.mChargeInfo.PayVersion = PayVersions.iDataRecovery_Abroad OrElse _
                   mChargeHelper.mChargeInfo.PayVersion = PayVersions.iDataRecovery Then
                Dim strFileScan As String = Me.Language.GetString("Chage.Lable.FileScan") '新语言
                Dim strFilePreview As String = Me.Language.GetString("Chage.Lable.FilePreview") '新语言
                Dim strFileRecovery As String = Me.Language.GetString("Chage.Lable.FileRecovery") '新语言
                Dim strLifetimeFreeUpdate As String = Me.Language.GetString("Chage.Lable.LifetimeFreeUpdate") '新语言

                info = New ImprintInfo
                info.StrDescribe = strFileScan
                info.BlnOfficial = True
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = strFilePreview
                info.BlnOfficial = True
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = strFileRecovery
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

                info = New ImprintInfo
                info.StrDescribe = strLifetimeFreeUpdate
                info.BlnOfficial = False
                info.BlnTry = True
                lstImprint.Add(info)

            End If
            If lstImprint.Count > 0 Then
                Me.AddListToDataGridView(lstImprint)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoLoadImprint")
        End Try
    End Sub

    Private Delegate Sub AddListToDataGridViewHandler(ByVal infoList As List(Of ImprintInfo))
    Private Sub AddListToDataGridView(ByVal infoList As List(Of ImprintInfo))
        If Me.InvokeRequired Then
            Me.Invoke(New AddListToDataGridViewHandler(AddressOf AddListToDataGridView), New Object() {infoList})
        Else
            Try
                Me.mDgvFeatureList.SuspendLayout()
                Me.mDgvFeatureList.Rows.Clear()

                Dim row As New tbDataGridViewRow()

                Dim imgFalse As Image = My.Resources.cell_false
                Dim imgTrue As Image = My.Resources.cell_true
                If Folder.AppType = RunType.iDataRecovery_Abroad Then
                    imgFalse = My.Resources.cell_false1
                    imgTrue = My.Resources.cell_true1
                End If

                Dim imgOfficial As Image = imgFalse
                Dim imgTry As Image = imgFalse
                Dim imgPhone As Image = imgFalse
                For Each info As ImprintInfo In infoList
                    row = New tbDataGridViewRow()

                    If info.BlnOfficial Then
                        imgOfficial = imgTrue
                    Else
                        imgOfficial = imgFalse
                    End If

                    If info.BlnTry Then
                        imgTry = imgTrue
                    Else
                        imgTry = imgFalse
                    End If

                    If info.BlnPhone Then
                        imgPhone = imgTrue
                    Else
                        imgPhone = imgFalse
                    End If

                    row.CreateCells(Me.mDgvFeatureList, _
                                    info.StrDescribe, _
                                    imgOfficial, _
                                    imgTry, _
                                    imgPhone, _
                                    "")
                    row.Height = 29
                    Me.mDgvFeatureList.Rows.Add(row)
                    row.Tag = info

                    Me.mDgvFeatureList.tbShowNoData = True
                    Me.mDgvFeatureList.ResumeLayout()
                Next

            Catch ex As Exception
                Common.LogException(ex.ToString(), "AddListToDataGridView")
            End Try
        End If

    End Sub

#Region "--- 显示收费窗体逻辑 ---"

    Public Overloads Shared Function Show(ByVal cHelper As ChargeHelper, ByVal WCMCInfo As WeChatMigrationCharge, Optional owner As Object = Nothing, Optional ByVal blnOverDeviceCount As Boolean = False) As DialogResult
        Dim result As DialogResult
        Dim frm As New frmCharge(cHelper, WCMCInfo, blnOverDeviceCount)

        If owner Is Nothing Then
            For Each frmOpen As Form In Application.OpenForms
                If frmOpen.Name = "MainForm" AndAlso frmOpen.Visible Then
                    owner = frmOpen
                    Exit For
                End If
            Next
        End If

        If owner IsNot Nothing AndAlso TypeOf owner Is Form AndAlso CType(owner, Form).WindowState <> FormWindowState.Minimized Then
            If Not CType(owner, Form).InvokeRequired Then
                frm.Tag = owner
                frm.StartPosition = FormStartPosition.CenterParent
            Else
                frm.StartPosition = FormStartPosition.Manual
                frm.Location = New Point(owner.Left + (owner.Width - frm.Width) \ 2, _
                                         owner.Top + (owner.Height - frm.Height) \ 2)
            End If
        Else
            frm.StartPosition = FormStartPosition.CenterScreen
        End If

        result = ShowForm(frm)
        Return result
    End Function

    Private Delegate Function ShowFormHandler(ByVal frm As frmCharge) As DialogResult
    Private Shared Function ShowForm(ByVal frm As frmCharge) As DialogResult
        If frm.InvokeRequired Then
            frm.Invoke(New ShowFormHandler(AddressOf ShowForm), frm)
        Else
            Dim result As DialogResult = Windows.Forms.DialogResult.None
            Try
                If frm.Tag IsNot Nothing AndAlso TypeOf frm.Tag Is Form Then
                    result = frm.ShowDialog(frm.Tag)
                Else
                    result = frm.ShowDialog()
                End If
            Catch ex As Exception
                Common.LogException("frmCharge error:" & ex.ToString())
            End Try
            Return result
        End If
    End Function

#End Region

End Class

Public Class ImprintInfo
    Private _strDescribe As String = ""
    Public Property StrDescribe() As String
        Get
            Return _strDescribe
        End Get
        Set(ByVal value As String)
            _strDescribe = value
        End Set
    End Property

    Private _blnOfficial As Boolean = False
    Public Property BlnOfficial() As Boolean
        Get
            Return _blnOfficial
        End Get
        Set(ByVal value As Boolean)
            _blnOfficial = value
        End Set
    End Property

    Private _blnTry As Boolean = False
    Public Property BlnTry() As Boolean
        Get
            Return _blnTry
        End Get
        Set(ByVal value As Boolean)
            _blnTry = value
        End Set
    End Property

    Private _blnPhone As Boolean = False
    Public Property BlnPhone() As Boolean
        Get
            Return _blnPhone
        End Get
        Set(ByVal value As Boolean)
            _blnPhone = value
        End Set
    End Property

End Class