﻿Imports System.Threading
Imports System.Data.SQLite
Public Class DataRecoveryHelper
    Implements IDisposable

    Private mLanguage As LanguageInterface = Nothing
    Private mTdWeChatDataRecovery As Thread = Nothing

    Public Event WeChatDataRecoveryToPhoneHandler(ByVal sender As Object, ByVal args As WeChatDataRecoveryToPhoneEventArgs)

#Region "--- 单例 ---"
    Private Shared mDictInstances As New Dictionary(Of String, DataRecoveryHelper)(StringComparer.InvariantCultureIgnoreCase)                '单一例，一个设备返回一个实例
    Private disposedValue As Boolean = False        ' 检测冗余的调用
    Private Shared _lockGetInstance As New Object
    Private mConn As SQLiteConnection = Nothing
    Private mIniPath As String = ""
    Private mDBPwd As String = "" '加密数据的秘钥

    Public Shared ReadOnly Property NoDevcieIdentifier() As String
        Get
            Return "Nonedevice"
        End Get
    End Property

    Public Sub New()
        Me.mLanguage = LanguageInterface.Instance()

    End Sub

    Private Shared mHelper As DataRecoveryHelper = Nothing

    Public Shared Function GetInstance() As DataRecoveryHelper
        Dim helper As DataRecoveryHelper = Nothing
        Dim blnRetry As Boolean = False

        SyncLock _lockGetInstance
DO_RETRY:
            Try
                If Not mDictInstances.ContainsKey(NoDevcieIdentifier) Then
                    mDictInstances.Add(NoDevcieIdentifier, New DataRecoveryHelper())
                End If
                helper = mDictInstances.Item(NoDevcieIdentifier)
            Catch ex As Exception
                If Not blnRetry Then
                    blnRetry = True
                    GoTo DO_RETRY
                End If
            End Try
        End SyncLock
        Return helper
    End Function

#End Region

#Region "--- 释放 ---"

    Public Sub Dispose() Implements IDisposable.Dispose
        ' 不要更改此代码。请将清理代码放入上面的 Dispose(ByVal disposing As Boolean) 中。

        Dispose(True)
        GC.SuppressFinalize(Me)
    End Sub

    Protected Overridable Sub Dispose(ByVal disposing As Boolean)
        If Not disposedValue Then
            If disposing Then
                ' TODO: 显式调用时释放非托管资源
                mDictInstances.Clear()

                Try
                    If Me.mTdWeChatDataRecovery IsNot Nothing AndAlso Me.mTdWeChatDataRecovery.ThreadState <> ThreadState.Stopped Then
                        Me.mTdWeChatDataRecovery.Abort()
                        Me.mTdWeChatDataRecovery = Nothing
                    End If
                Catch
                End Try
            End If
        End If
        disposedValue = True
    End Sub

#End Region

#Region "--- 恢复微信删除数据 ---"
    Public Sub WeChatDataRecovery(ByVal strMMdb As String, ByVal strSessiondb As String, ByVal strWCDBPath As String, ByVal dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)), ByVal dictDelFriendInfo As Dictionary(Of String, WeChatFriendInfo), lstR As List(Of DataGridViewRow), ByVal username As String, ByVal devicename As String, ByVal strPath As String, ByVal strDbPwd As String, ByVal strMMDbShm As String, ByVal strMMDbWal As String, ByVal strSessiondbShm As String, ByVal strSessiondbWal As String)
        Try
            If Me.mTdWeChatDataRecovery IsNot Nothing AndAlso Me.mTdWeChatDataRecovery.ThreadState <> ThreadState.Stopped Then
                Return
            End If

            'Me.mTdWeChatDataRecovery = New Thread(AddressOf DoWeChatDataRecovery)
            'Me.mTdWeChatDataRecovery.IsBackground = True
            'Me.mTdWeChatDataRecovery.Start(New Object() {strMMdb, strSessiondb, dictDelChatInfo, dictDelFriendInfo})

            Me.mTdWeChatDataRecovery = New Thread(AddressOf DoExportRecoveryData)
            Me.mTdWeChatDataRecovery.IsBackground = True
            Me.mTdWeChatDataRecovery.Start(New Object() {strMMdb, strSessiondb, strWCDBPath, dictDelChatInfo, dictDelFriendInfo, lstR, username, devicename, strPath, strDbPwd, strMMDbShm, strMMDbWal, strSessiondbShm, strSessiondbWal})

        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeChatDataRecovery")
        End Try
    End Sub
    Private Sub DoWeChatDataRecovery(ByVal obj As Object)
        Dim args As New WeChatDataRecoveryToPhoneEventArgs()
        Dim blnError As Boolean = False
        Try
            args.Progress = 1
            args.Status = WeChatDataRecoveryToPhoneType.Start
            RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)

            Dim strMMdb As String = obj(0)
            Dim strSessiondb As String = obj(1)
            Dim dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)) = obj(2)
            Dim dictDelFriendInfo As Dictionary(Of String, WeChatFriendInfo) = obj(3)

            '备份两个db文件
            File.Copy(strMMdb, String.Format("{0}_backup{1}", strMMdb, Date.Now.ToString("yyyyMMddHHmmss")))
            File.Copy(strSessiondb, String.Format("{0}_backup{1}", strSessiondb, DateTime.Now.ToString("yyyyMMddHHmmss")))

            Dim MMdbHelper As WeixinMMDB = WeixinMMDB.GetInstance(strMMdb)
            MMdbHelper.Dispose()
            MMdbHelper = WeixinMMDB.GetInstance(strMMdb)

            Dim SessiondbHelper As WeixinMMDB = WeixinMMDB.GetInstance(strSessiondb)
            SessiondbHelper.Dispose()
            SessiondbHelper = WeixinMMDB.GetInstance(strSessiondb)

            Common.OpenExplorer(Path.GetDirectoryName(strMMdb))
            Common.OpenExplorer(Path.GetDirectoryName(strSessiondb))
            MessageBox.Show(String.Format("请备份的文件 {0}  {1}  切记备份成功后 才能点击确定", Path.GetFileName(strMMdb), Path.GetFileName(strSessiondb)))


            args.Progress = 5
            args.Status = WeChatDataRecoveryToPhoneType.Modify
            RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)
            Dim intIndex As Integer = 0
            For Each item As KeyValuePair(Of String, List(Of WeChatChatInfo)) In dictDelChatInfo
                intIndex = intIndex + 1
                '获取 username  是否是群聊
                Dim strUserName As String = ""
                Me.IsChatRoom(strUserName, item.Key, dictDelFriendInfo)
                'If Me.IsChatRoom(strUserName, item.Key, dictDelFriendInfo) Then
                '    Continue For
                'End If
                '未知联系人过滤
                If strUserName = "" Then
                    Continue For
                End If

                Dim lstChatInfo As New List(Of WeChatChatInfo)
                Dim isInDB As Boolean = MMdbHelper.CheckChatInDB(item.Key)
                If Not isInDB Then
                    '如果数据库没有这个表  则需要创建表 和 在Sessiondb 添加一条记录
                    MMdbHelper.CreateChatTable(item.Key)
                    SessiondbHelper.InsertSession(strUserName, 0, item.Value.Count)
                Else
                    'Dim lstChat As List(Of ChatInfo) = MMdbHelper.Get_Chat_Info(strUserName)
                    'If lstChat.Count > 0 Then
                    '    For Each dInfo As ChatInfo In item.Value
                    '        lstChat.Add(dInfo)
                    '    Next
                    'Else
                    '    lstChat = item.Value
                    'End If
                    'lstChatInfo = lstChat
                    lstChatInfo = item.Value
                    '更新 Sessiondb 未读数量*
                    SessiondbHelper.UpdateSessionUnReadCount(strUserName, item.Value.Count)
                End If
                If lstChatInfo.Count > 0 Then
                    lstChatInfo.Sort(New CreateTimeComparer(SortType.ASC))
                    '先清空表* 在插入数据
                    'MMdbHelper.ClearChatInfo(item.Key)
                    MMdbHelper.InsertChatInfo(lstChatInfo, item.Key)
                End If

                Dim intProgress = intIndex / dictDelChatInfo.Count * 100 + 5
                If intProgress >= 100 Then
                    intProgress = 99
                End If
                args.Progress = intProgress
                args.Status = WeChatDataRecoveryToPhoneType.Modify
                RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)
            Next
            MMdbHelper.Dispose()
            SessiondbHelper.Dispose()

            args.Progress = 100
            args.Status = WeChatDataRecoveryToPhoneType.Succeed
            RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoWeChatDataRecovery")
            blnError = True
            'args.Status = WeChatDataRecoveryToPhoneType.Failure
            'args.ErrorMsg = ex.ToString()
            'RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)
        End Try

        Try
            If blnError Then
                RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, New WeChatDataRecoveryToPhoneEventArgs(WeChatDataRecoveryToPhoneType.Failure, 0, ""))
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoWeChatDataRecovery_1")
        End Try
       

    End Sub

    Private Function IsChatRoom(ByRef strUserName As String, ByVal strMD5 As String, ByVal dictDelFriendInfo As Dictionary(Of String, WeChatFriendInfo)) As Boolean
        Dim isRelust As Boolean = False
        Try
            For Each fInfo As WeChatFriendInfo In dictDelFriendInfo.Values
                If fInfo.Md5 = strMD5 Then
                    strUserName = fInfo.UsrName
                    If fInfo.StrChatRoomMem.TrimEnd().Length > 0 Then
                        isRelust = True
                    End If
                    Exit For
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "IsChatRoom")
        End Try
        Return isRelust
    End Function


    Private Sub DoExportRecoveryData(ByVal obj As Object)
        Dim args As New WeChatDataRecoveryToPhoneEventArgs()
        Dim blnError As Boolean = False
        Try
            args.Progress = 1
            args.Status = WeChatDataRecoveryToPhoneType.Start
            RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)

            Dim strMMdb As String = obj(0)
            Dim strSessiondb As String = obj(1)
            Dim strWCDB As String = obj(2)
            Dim dictDelChatInfo As Dictionary(Of String, List(Of WeChatChatInfo)) = obj(3)
            Dim dictDelFriendInfo As Dictionary(Of String, WeChatFriendInfo) = obj(4)
            Dim lstRows As List(Of DataGridViewRow) = obj(5)
            Dim strWCUName As String = obj(6)
            Dim strDName As String = obj(7)
            Dim strPath As String = obj(8)

            Dim strMMdbShm As String = obj(10)
            Dim strMMdbWal As String = obj(11)
            Dim strSessiondbShm As String = obj(12)
            Dim strSessiondbWal As String = obj(13)

            args.ExportPath = strPath
            mDBPwd = obj(9)
            mIniPath = Path.Combine(strPath, "Settings.ini")
            '获取导出的路径

            'Dim fbdWeixin As New FolderBrowserDialog
            'fbdWeixin.Description = Me.mLanguage.GetString("Media.Button.Export") '导出
            'fbdWeixin.ShowNewFolderButton = True
            'fbdWeixin.SelectedPath = IniSetting.GetWeChatExportPath
            'If fbdWeixin.ShowDialog() <> Windows.Forms.DialogResult.OK Then
            '    Return
            'End If

            'IniSetting.SetWeChatExportPath(fbdWeixin.SelectedPath)

            'Dim strPath As String = Path.Combine(fbdWeixin.SelectedPath, Me.mLanguage.GetString("Weixin.Lable.WeixinMsgRecord") & "-" & Utility.ReplaceWinIllegalName(strDName)) '"微信消息记录"
            ''strPath = Path.Combine(strPath, Date.Now.ToString("yyyyMMddHHmm"))
            'If Utility.ReplaceWinIllegalName(strWCUName).Length > 0 Then
            '    strPath = strPath & "-" & Utility.ReplaceWinIllegalName(strWCUName)
            'End If
            '备份两个db文件
            Folder.CheckFolder(strPath)

            File.Copy(strMMdb, Path.Combine(strPath, Path.GetFileName(strMMdb)), True)
            File.Copy(strSessiondb, Path.Combine(strPath, Path.GetFileName(strSessiondb)), True)
            If File.Exists(strWCDB) Then
                File.Copy(strWCDB, Path.Combine(strPath, Path.GetFileName(strWCDB)), True)
                IniClass.SetIniSectionKey("Setting", "WCDB", strWCDB, mIniPath)
            End If
            IniClass.SetIniSectionKey("Setting", "MM", strMMdb, mIniPath)
            IniClass.SetIniSectionKey("Setting", "Session", strSessiondb, mIniPath)

            If File.Exists(strMMdbShm) Then
                File.Copy(strMMdbShm, Path.Combine(strPath, Path.GetFileName(strMMdbShm)), True)
            End If

            If File.Exists(strMMdbWal) Then
                File.Copy(strMMdbWal, Path.Combine(strPath, Path.GetFileName(strMMdbWal)), True)
            End If

            If File.Exists(strSessiondbShm) Then
                File.Copy(strSessiondbShm, Path.Combine(strPath, Path.GetFileName(strSessiondbShm)), True)
            End If

            If File.Exists(strSessiondbWal) Then
                File.Copy(strSessiondbWal, Path.Combine(strPath, Path.GetFileName(strSessiondbWal)), True)
            End If

            IniClass.SetIniSectionKey("Setting", "MMShm", strMMdbShm, mIniPath)
            IniClass.SetIniSectionKey("Setting", "MMWal", strMMdbWal, mIniPath)
            IniClass.SetIniSectionKey("Setting", "SessionShm", strSessiondbShm, mIniPath)
            IniClass.SetIniSectionKey("Setting", "SessionWal", strSessiondbWal, mIniPath)

            Dim strDBPath As String = Path.Combine(strPath, "wx.db")
            CheckDbFileExist(strDBPath)
            Dim intIndex As Integer = 0
            For Each Row As DataGridViewRow In lstRows
                intIndex = intIndex + 1
                Try
                    Dim intProgress = intIndex / lstRows.Count * 100 + 5
                    If intProgress >= 100 Then
                        intProgress = 99
                    End If

                    args.Progress = intProgress
                    args.Status = WeChatDataRecoveryToPhoneType.Modify
                    RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)

                    Dim wInfo As WeChatFriendInfo = CType(Row.Tag, WeChatFriendInfo)
                    Dim lstChatInfo As New List(Of WeChatChatInfo)
                    If dictDelChatInfo.ContainsKey(wInfo.Md5) Then
                        lstChatInfo = dictDelChatInfo(wInfo.Md5)
                    End If

                    Dim strTableName As String = wInfo.Md5
                    If wInfo.NoteName.StartsWith("未知联系人") Then
                        Dim arrName As String() = wInfo.NoteName.Split("_")
                        If arrName.Length > 1 Then
                            strTableName = arrName(1)
                        End If
                    End If

                    If Not CheckChatInDB(strTableName) Then
                        CreateChatTable(strTableName)
                    End If

                    '判断是否存在Friend表
                    If Not CheckChatInDB("Friend", True) Then
                        CreateFriendTable()
                    End If

                    lstChatInfo.Sort(New CreateTimeComparer(SortType.ASC))
                    '先清空表* 在插入数据
                    'MMdbHelper.ClearChatInfo(item.Key)
                    InsertChatInfoByTransaction(lstChatInfo, strTableName)

                    Dim strName As String = wInfo.UsrName
                    If wInfo.NoteName IsNot Nothing AndAlso wInfo.NoteName.Length > 0 Then
                        strName = Utility.ReplaceWinIllegalName(wInfo.NoteName)
                    ElseIf wInfo.NickName IsNot Nothing Then
                        strName = Utility.ReplaceWinIllegalName(wInfo.NickName)
                    End If
                    InsertFriend(IIf(wInfo.NoteName.StartsWith("未知联系人"), strTableName, wInfo.UsrName), strName)
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "DoExportRecoveryData_1")
                End Try
            Next
            mConn.Close()
            mConn.Dispose()

            '记录导出的最新文件路径
            Dim strExportFolder As String = Path.Combine(Folder.CacheFolder, "WeixinRecoveryToPhone")
            Dim strConfigIni As String = Path.Combine(strExportFolder, "Config.ini")
            IniClass.SetIniSectionKey("Setting", "LastExportPath", strPath, strConfigIni)
            Utility.WaitSeconds(0.5)
            args.Progress = 100
            args.Status = WeChatDataRecoveryToPhoneType.Succeed
            args.ExportPath = strPath
            RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)
            'Utility.WaitSeconds(1)
            'Common.OpenExplorer(strPath)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportRecoveryData")
            blnError = True
            'args.Status = WeChatDataRecoveryToPhoneType.Failure
            'args.ErrorMsg = "导出失败"
            'RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, args)

        End Try
        Try
            If blnError Then
                RaiseEvent WeChatDataRecoveryToPhoneHandler(Me, New WeChatDataRecoveryToPhoneEventArgs(WeChatDataRecoveryToPhoneType.Failure, 0, ""))
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoExportRecoveryData_RaiseError")
        End Try
    End Sub

    Private Function CheckDbFileExist(ByVal dbFilePath As String) As Boolean
        Dim blnResult As Boolean = False
        Try
            If Not File.Exists(dbFilePath) Then
                SQLiteConnection.CreateFile(dbFilePath)
                mConn = New SQLiteConnection("Data Source = " & dbFilePath)
                'dbFilePath = Path.Combine(Path.GetDirectoryName(dbFilePath), "wx_en")
                'mcon()
                mConn.Open()
                mConn.ChangePassword(mDBPwd)

            End If
            blnResult = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckDbFileExist")
        End Try
        Return blnResult
    End Function

    Private Function GetDBPwdFromServer() As String
        Dim strResult As String = ""
        Try
            If Not String.IsNullOrEmpty(mDBPwd) Then
                Return mDBPwd
            End If
            Dim strUrl As String = "http://pc.api.tongbu.com/vip/v.html?t=131"
            Dim strContent As String = Utility.GetContentStringFromUrl(strUrl, Encoding.UTF8) 'AESFunctionHelper.PostDataToUrl(Me.GetComputerInfoJson(), strUrl)
            If String.IsNullOrEmpty(strContent) Then
                Common.LogException(String.Format("获取数据库秘钥错误,返回:{0}", strContent))
                Return strResult
            End If
            Common.LogException(String.Format("131接口返回:{0}", strContent))
            Dim strDeResult As String = strContent.Replace("_@_", "+")
            strDeResult = Common.DecryptDES(strDeResult, "auismp#6", "cd233!#9")
            Dim jObj As JsonObject = JsonParser.ParseString(strDeResult)
            If jObj Is Nothing Then
                Common.LogException("解析获取的加密数据库秘钥错误")
                Return strResult
            End If

            Dim dataObject As JsonObject = Nothing
            If jObj.ContainsKey("data") AndAlso TypeOf jObj("data") Is JsonObject Then
                dataObject = jObj("data")
            Else
                Common.LogException("解析获取的加密数据库秘钥错误")
                Return strResult
            End If

            If dataObject.ContainsKey("code") AndAlso TypeOf dataObject("code") Is JsonNumber AndAlso _
              dataObject.ContainsKey("msg") AndAlso TypeOf dataObject("msg") Is JsonString Then
                Dim jCode As JsonNumber = CType(dataObject("code"), JsonNumber)
                Dim jMsg As JsonString = CType(dataObject("msg"), JsonString)     '

                If jCode.Value = 1 Then
                    strResult = jMsg.ToString()
                End If
            Else
                Common.LogException("解析获取的加密数据库秘钥错误")
                Return strResult
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ChargeHelper_LoadInfoFromServerThread")

        End Try
        mDBPwd = strResult
        Return strResult
    End Function


    Public Function CheckChatInDB(ByVal strName As String, Optional ByVal isFriendTable As Boolean = False) As Boolean
        Dim isRelust As Boolean = False
        Try
            Dim strSql As String = String.Format(" Select * From sqlite_master where type='table' and tbl_name='Chat_{0}' ", strName)
            If isFriendTable Then
                strSql = String.Format(" Select * From sqlite_master where type='table' and tbl_name='Friend' ")
            End If
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(strSql, mConn)
            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                isRelust = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckChatInDB")
        End Try
        Return isRelust
    End Function

    Public Function CreateChatTable(ByVal strName As String)
        Dim isRelust As Boolean = False
        Try

            Dim sbSql As StringBuilder = New StringBuilder()
            sbSql.AppendLine(String.Format(" CREATE TABLE Chat_{0}(TableVer integer default 1, MesLocalID integer primary key autoincrement, MesSvrID bigint default 0, CreateTime integer default 0, Message text, Status integer default 0, ImgStatus integer default 0, Type integer, Des integer);", strName))
            sbSql.AppendLine(String.Format("CREATE INDEX Chat_{0}_Index on Chat_{0}(MesSvrID);", strName))
            sbSql.AppendLine(String.Format("CREATE INDEX Chat_{0}_Index2 on Chat_{0}(CreateTime);", strName))
            sbSql.AppendLine(String.Format("CREATE INDEX Chat_{0}_Index3 on Chat_{0}(Status);", strName))
            Dim intUpdate As Integer = SQLiteClass.ExecuteNoneQuery(sbSql.ToString(), mConn)
            If intUpdate > 0 Then
                isRelust = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateChatTable")
        End Try
        Return isRelust
    End Function

    Public Function CreateFriendTable()
        Dim isRelust As Boolean = False
        Try
            Dim sbSql As StringBuilder = New StringBuilder()
            sbSql.AppendLine("CREATE TABLE Friend(userName text primary key,noteName text) ")
            Dim intUpdate As Integer = SQLiteClass.ExecuteNoneQuery(sbSql.ToString(), mConn)
            If intUpdate > 0 Then
                isRelust = True
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateFriendTable")
        End Try
        Return isRelust
    End Function

    Public Sub InsertFriend(ByVal userName As String, ByVal noteName As String)
        Try
            Dim strSql As String = String.Format("insert into Friend(userName,noteName) values('{0}' , '{1}')", userName, noteName.Replace("�", "").Replace("""", "").Replace("|", "").Replace("~", "").Replace("'", ""))
            Dim iResult As Integer = SQLiteClass.ExecuteNoneQuery(strSql, Me.mConn)
            If iResult > 0 Then

            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InsertFriend")
        End Try
    End Sub

    Public Function InsertChatInfoByTransaction(ByVal lstChatInfo As List(Of WeChatChatInfo), ByVal strName As String) As Boolean
        Dim isRelust As Boolean = False
        Try
            Dim intIndex As Integer = 1
            Dim lastChatInfo As WeChatChatInfo = Get_Last_Chat_Info(strName)
            If lastChatInfo IsNot Nothing Then
                intIndex = lastChatInfo.IntID + 1
            End If
            Dim intCount As Integer = 0
            Dim strSql As String = "insert into Chat_{0}(TableVer,MesLocalID,MesSvrID,CreateTime,Message,Status,ImgStatus,Type,Des)values(1,{1},{2},{3},'{4}',{5},1,{6},{7});"


            Using trans As IDbTransaction = CType(mConn, System.Data.SQLite.SQLiteConnection).BeginTransaction
                Try
                    Using cmd As IDbCommand = CType(mConn, System.Data.SQLite.SQLiteConnection).CreateCommand
                        Dim sbSql As New StringBuilder()
                        For Each info As WeChatChatInfo In lstChatInfo
                            sbSql.AppendLine(String.Format(strSql, strName, intIndex, intIndex, info.IntCreateTime, info.StrMessage.Replace("�", "").Replace("""", "").Replace("|", "").Replace("~", "").Replace("'", ""), info.IntStatus, GetIntChatType(info.SCType), info.IntDes))
                            intIndex = intIndex + 1
                            intCount = intCount + 1
                            If intCount > 200 Then
                                cmd.CommandText = sbSql.ToString()
                                cmd.ExecuteNonQuery()
                                sbSql.Remove(0, sbSql.Length)
                                intCount = 0
                            End If
                        Next
                        cmd.CommandText = sbSql.ToString()
                        cmd.ExecuteNonQuery()
                        trans.Commit()
                        isRelust = True
                    End Using
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "InsertChatInfoByTransaction1")
                    trans.Rollback()
                End Try
            End Using
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InsertChatInfoByTransaction")
        End Try
        Return isRelust
    End Function


    Public Function Get_Last_Chat_Info(ByVal usrName As String) As WeChatChatInfo
        Dim chatInfo As WeChatChatInfo = Nothing
        Try
            'Dim strMD5 As String = Common.ToHexString(Common.GetMd5Array(usrName))
            Dim strTableName As String = String.Format("Chat_{0}", usrName)
            Dim pstrSQL As String = String.Format("select * from {0} order by MesLocalID desc limit 0,1", strTableName)

            Dim lstInfos As List(Of WeChatChatInfo) = Me.ExecuteSQL(usrName, pstrSQL)
            If lstInfos IsNot Nothing AndAlso lstInfos.Count > 0 Then
                chatInfo = lstInfos(0)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Get_Last_Chat_Info")
        End Try
        Return chatInfo
    End Function

    Private Function ExecuteSQL(ByVal usrName As String, ByVal pstrSQL As String) As List(Of WeChatChatInfo)
        Dim lstInfos As New List(Of WeChatChatInfo)
        Try
            Dim dt As DataTable = SQLiteClass.ExecuteSQL(pstrSQL, mConn)

            If dt IsNot Nothing AndAlso dt.Rows.Count > 0 Then
                For Each dr As DataRow In dt.Rows
                    Try
                        Dim info As New WeChatChatInfo()
                        info.StrUsrName = usrName
                        info.IntID = Common.GetValue(Of Integer)(dr("MesLocalID"), 0)
                        info.StrMessage = Common.GetValue(Of String)(dr("Message"), String.Empty)
                        info.IntStatus = Common.GetValue(Of Integer)(dr("Status"), 0)
                        info.ImgStatus = Common.GetValue(Of Integer)(dr("ImgStatus"), 0)
                        'info.SCType = Me.OperateSpecialChatType(Common.GetValue(Of Integer)(dr("Type"), 0), usrName, info.StrMessage)
                        info.IntDes = Common.GetValue(Of Integer)(dr("Des"), 0)

                        Dim longCreateTime As Long = Common.GetValue(Of Long)(dr("CreateTime"), 0)
                        info.IntCreateTime = longCreateTime
                        info.DTCreateTime = WeixinHelper.ConvertWeixinToPcTime(longCreateTime)

                        lstInfos.Add(info)
                    Catch ex As Exception
                        System.Diagnostics.Debug.WriteLine(ex.ToString())
                        Common.LogException(ex.ToString(), "ExecuteSQL")
                    End Try
                Next
            End If
        Catch
        End Try
        Return lstInfos
    End Function

    Private Function GetIntChatType(ByVal socailType As SociaChatType) As Integer
        Dim intType As Integer = 1
        Try
            If socailType = SociaChatType.Text Then
                intType = 1
            ElseIf socailType = SociaChatType.SystemMessages Then
                intType = 1000
            ElseIf socailType = SociaChatType.Picture Then
                intType = 3
            ElseIf socailType = SociaChatType.Voice Then
                intType = 34
            ElseIf socailType = SociaChatType.webpage Then
                intType = 49
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetIntType")
        End Try
        Return intType
    End Function
#End Region

    Public Sub StopExport()
        Try
            If Me.mTdWeChatDataRecovery IsNot Nothing AndAlso Me.mTdWeChatDataRecovery.ThreadState <> ThreadState.Stopped Then
                Try
                    Me.mTdWeChatDataRecovery.Abort()
                Catch
                End Try
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DataRecoveryHelper_StopExport")
        End Try
    End Sub

End Class

Public Class WeChatDataRecoveryToPhoneEventArgs
    Inherits EventArgs

    Private mIntProgress As Integer = 0
    Private mStatus As WeChatDataRecoveryToPhoneType = WeChatDataRecoveryToPhoneType.None
    Private mstrErrorMsg As String = String.Empty
    Private mExportPath As String = String.Empty

    Public Property Progress() As Integer
        Get
            Return Me.mintProgress
        End Get
        Set(ByVal value As Integer)
            Me.mintProgress = value
        End Set
    End Property

    Public Property ErrorMsg() As String
        Get
            Return Me.mstrErrorMsg
        End Get
        Set(ByVal value As String)
            Me.mstrErrorMsg = value
        End Set
    End Property

    Public Property Status() As WeChatDataRecoveryToPhoneType
        Get
            Return Me.mStatus
        End Get
        Set(ByVal value As WeChatDataRecoveryToPhoneType)
            Me.mStatus = value
        End Set
    End Property

    Public Property ExportPath() As String
        Get
            Return Me.mExportPath
        End Get
        Set(value As String)
            Me.mExportPath = value
        End Set
    End Property

    Public Sub New()

    End Sub

    Public Sub New(ByVal status As WeChatDataRecoveryToPhoneType, ByVal progress As Integer, ByVal errorMsg As String)
        Me.mStatus = status
        Me.mIntProgress = progress
        Me.mstrErrorMsg = errorMsg
    End Sub
End Class

Public Enum WeChatDataRecoveryToPhoneType
    None
    Start
    Modify
    Succeed
    Failure
End Enum

