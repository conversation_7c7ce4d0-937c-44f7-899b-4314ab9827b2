﻿Public Class frmPayCoexist

#Region "--- 初始化 ---"

    Public Sub New(ByVal application As IApplication, ByVal iDev As IDevice)
        MyBase.New(application, iDev)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.mApplication = application
        Me.Language = application.Language
        Me.FunctionMappingKey = FunctionKey.Weixin
        Me.tbAutoSetFormSize = True
        Me.Icon = My.Resources.iTong

        Me.mDevice = iDev
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False

        Me.LoadMeal()
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        If ServerIniSetting.GetCoexistMealRemind().Length > 0 Then
            Me.txtRemind.Text = ServerIniSetting.GetCoexistMealRemind()
        End If
    End Sub

    Protected Overrides Sub OnFormClosed(ByVal e As System.Windows.Forms.FormClosedEventArgs)
        MyBase.OnFormClosed(e)

    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

    End Sub

    Public Overrides Sub OnConnect(ByVal device As IDevice)
        MyBase.OnConnect(device)
        Me.mDevice = device
    End Sub

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)
        Me.Close()
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnPay_Click(sender As Object, e As EventArgs) Handles btnPay.Click
        If Not CheckDevice() Then
            Return
        End If
        Dim strMsg As String = String.Format("您的分身版将安装在设备【{0}】{1}选择的是[{2}]{1}确认信息并去付款？", Me.mDevice.DeviceName, vbCrLf, Me.cbxMeal.Text)
        If tbMessageBox.Show(Me, strMsg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Cancel Then
            Return
        End If

        Common.OpenExplorer(String.Format(Me.cbxMeal.SelectedValue, Me.mDevice.UniqueDeviceID()))
        Me.txtUDID.Text = Me.mDevice.UniqueDeviceID()
        Clipboard.Clear()
        Clipboard.SetText(Me.txtUDID.Text.Trim())
    End Sub

    Private Sub btnUDID_Click(sender As Object, e As EventArgs) Handles btnUDID.Click
        If Not CheckDevice() Then
            Return
        End If
        Me.txtUDID.Text = Me.mDevice.UniqueDeviceID()
        Clipboard.Clear()
        Clipboard.SetText(Me.txtUDID.Text.Trim())
        Me.lblHint.Visible = True
    End Sub

    Private Sub btnQQ_Click(sender As Object, e As EventArgs) Handles btnQQ.Click
        Common.OpenExplorer("http://wpa.b.qq.com/cgi/wpa.php?ln=1&key=XzkzODAxNDg3NF80NDU1NDZfNDAwOTk4MTM4OV8yXw")
    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub
    Private Sub txtUDID_Click(sender As Object, e As EventArgs) Handles txtUDID.Click
        If Me.txtUDID.Text.Trim().Length > 0 AndAlso Me.txtUDID.Text <> "点击「付费」后程序会自动获取UDID。" Then
            Clipboard.Clear()
            Clipboard.SetText(Me.txtUDID.Text.Trim())
            Me.lblHint.Visible = True
        End If
    End Sub

#End Region

#Region "--- 私有方法 ---"

    Private Function CheckDevice() As Boolean
        Dim isRelset As Boolean = True
        If Me.mDevice Is Nothing Then
            tbMessageBox.Show(Me, "请连接设备。", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1)
            isRelset = False
        End If
        Return isRelset
    End Function

    Private Sub LoadMeal()
        Try
            Dim dt As DataTable = Me.CreateDT()
            Dim dictCoexistMeal As Dictionary(Of String, String) = ServerIniSetting.DictCoexistMeal()
            If dictCoexistMeal.Count > 0 Then
                Dim dr As DataRow = Nothing
                For Each item As KeyValuePair(Of String, String) In dictCoexistMeal
                    dr = dt.NewRow()
                    dr("key") = item.Key
                    dr("value") = item.Value
                    dt.Rows.Add(dr)
                Next
            End If

            Me.cbxMeal.DataSource = dt
            Me.cbxMeal.DisplayMember = "key"
            Me.cbxMeal.ValueMember = "value"

        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadMeal")
        End Try
    End Sub

    Private Function CreateDT() As DataTable
        Dim dt As New DataTable
        dt.Columns.Add("key", Type.GetType("System.String"))
        dt.Columns.Add("value", Type.GetType("System.String"))
        Return dt
    End Function

#End Region


End Class