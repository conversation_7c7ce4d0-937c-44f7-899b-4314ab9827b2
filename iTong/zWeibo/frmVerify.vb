﻿Imports System.Text
Imports System.IO
Imports System.Text.RegularExpressions

Public Class frmVerify

    Private mUrl As String = String.Empty

    Private mAccessToken As New AccessToken()
    Private mRequestToken As New RequestToken()
    Private mErrorDocumentText As String = String.Empty

    Public ReadOnly Property RequestToken() As RequestToken
        Get
            Return mRequestToken
        End Get
    End Property

    Public ReadOnly Property AccessToken() As AccessToken
        Get
            Return mAccessToken
        End Get
    End Property

    Public Sub New(ByVal url As String)
        InitializeComponent()

        Me.CanbeMove = True
        Me.ShowInTaskbar = True
        Me.CanResize = False

        Me.tbShowTitleOnForm = True
        Me.Icon = My.Resources.iTong

        Me.FilletRadius = 5

        Me.btn_close.tbBackgroundImage = My.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_normal.tbBackgroundImage = My.Resources.btn_max
        Me.btn_normal.tbBackgroundImageState = ImageState.ThreeState
        Me.btn_minimize.tbBackgroundImage = My.Resources.btn_min
        Me.btn_minimize.tbBackgroundImageState = ImageState.ThreeState

        Me.StartPosition = FormStartPosition.CenterScreen
        Me.pnlMain.Size = New System.Drawing.Size(798, 566)

        Me.mUrl = url

        Me.Language = LanguageInterface.Instance()
        Me.Text = Me.Language.GetString("WeiBo.Label.Verify")           '"授权登录"

    End Sub

    Private Sub frmVerify_Load(ByVal sender As Object, ByVal e As EventArgs) Handles MyBase.Load
        Me.webLogin.Navigate(Me.mUrl)
        If File.Exists(Path.Combine(Folder.AppFolder, "debug.dll")) Then
            Me.webLogin.IsWebBrowserContextMenuEnabled = True
        End If

    End Sub

    Private Sub webLogin_ProgressChanged(ByVal sender As System.Object, ByVal e As System.Windows.Forms.WebBrowserProgressChangedEventArgs) Handles webLogin.ProgressChanged
        If webLogin.Url IsNot Nothing AndAlso Me.mUrl.Contains("api.weibo.com") AndAlso webLogin.Url.Query.Contains("code=") Then
            System.Diagnostics.Debug.Print(webLogin.Url.ToString())

            Dim col As System.Collections.Specialized.NameValueCollection = System.Web.HttpUtility.ParseQueryString(webLogin.Url.Query)
            Me.mRequestToken.Code = col("code")

            Me.DialogResult = Windows.Forms.DialogResult.OK
        End If
    End Sub

    Private Sub webLogin_DocumentCompleted(ByVal sender As Object, ByVal e As WebBrowserDocumentCompletedEventArgs) Handles webLogin.DocumentCompleted
        If Not String.IsNullOrEmpty(Me.webLogin.DocumentText) Then
            System.Diagnostics.Debug.Print(webLogin.Url.ToString())

            If webLogin.Url.ToString().Contains("facebook") Then
                Dim fbUri As Uri = e.Url
                If fbUri.Query.Contains("code") Then
                    Dim col As System.Collections.Specialized.NameValueCollection = System.Web.HttpUtility.ParseQueryString(fbUri.Query)
                    Dim code As String = col("code")
                    Dim authFacebook As New OAuthFacebook
                    If Not String.IsNullOrEmpty(code) Then
                        Dim tokenUrl As String = authFacebook.GetCodeUrl(code)
                        Dim context = Utility.GetContentStringFromUrl(tokenUrl, System.Text.Encoding.UTF8)
                        Dim response As System.Collections.Specialized.NameValueCollection = System.Web.HttpUtility.ParseQueryString(context)
                        Dim accessToken As String = response("access_token")
                        If accessToken IsNot Nothing Then
                            Me.mAccessToken.Token = response("access_token")
                            Me.DialogResult = Windows.Forms.DialogResult.OK
                        End If
                    End If

                    'Me.mAccessToken.Token = col("access_token")



                End If
            ElseIf webLogin.Url.ToString().Contains("t.qq.com") Then
                Dim reg As New Regex("url=(?<url>.*?)""")
                Dim math As Match = reg.Match(Me.webLogin.DocumentText)
                If math.Success Then
                    Dim tcUri As New Uri(math.Groups("url").Value)
                    Dim col As System.Collections.Specialized.NameValueCollection = System.Web.HttpUtility.ParseQueryString(tcUri.Query)

                    Me.RequestToken.Code = col("code")
                    Me.RequestToken.OpenID = col("openid")
                    Me.RequestToken.OpenKey = col("openkey")

                    If Not String.IsNullOrEmpty(Me.RequestToken.Code) AndAlso Not String.IsNullOrEmpty(Me.RequestToken.OpenID) AndAlso Not String.IsNullOrEmpty(Me.RequestToken.OpenKey) Then
                        Me.DialogResult = Windows.Forms.DialogResult.OK
                    End If

                End If
            Else
                If Me.mUrl.Contains("api.weibo.com") AndAlso e.Url.Query.Contains("code=") Then
                    Dim col As System.Collections.Specialized.NameValueCollection = System.Web.HttpUtility.ParseQueryString(e.Url.Query)
                    Me.mRequestToken.Code = col("code")

                    Me.DialogResult = Windows.Forms.DialogResult.OK

                Else
                    'System.Diagnostics.Debug.Print(Me.webLogin.DocumentText)
                    Dim reg As New Regex("<code>(?<code>.*?)</code>", RegexOptions.IgnoreCase)
                    Dim math As Match = reg.Match(Me.webLogin.DocumentText)

                    If math.Success Then
                        Me.mRequestToken.Code = math.Groups("code").Value
                        Me.DialogResult = Windows.Forms.DialogResult.OK
                    End If
                End If

            End If
        End If
        Common.Log("Verfy Completed" & e.Url.ToString() & "")
        Common.Log("Verfy Completed" & Me.webLogin.DocumentText & "")
    End Sub

    Private Sub webLogin_Navigating(ByVal sender As System.Object, ByVal e As WebBrowserNavigatingEventArgs) Handles webLogin.Navigating
        Try
            System.Diagnostics.Debug.Print("webLogin_Navigating: " & webLogin.Url.ToString())
            If e.Url.ToString().ToLower().Contains("itong://refresh/") Then
                e.Cancel = True
                Me.webLogin.Navigate(Me.mUrl)
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub webLogin_NavigateError(ByVal sender As System.Object, ByVal e As WebBrowserNavigateErrorEventArgs) Handles webLogin.NavigateError
        Dim wb As tbWebBrowser = CType(sender, tbWebBrowserEx)
        Dim strImageFile As String = Folder.CacheFolder & "welcome_gif_error.gif"
        If Not System.IO.File.Exists(strImageFile) Then
            My.Resources.welcome_gif_error.Save(strImageFile)
        End If

        Common.LogException("Verfy Error" & e.Url.ToString())

        wb.Url = New Uri(e.Url)

        If String.IsNullOrEmpty(mErrorDocumentText) Then
            mErrorDocumentText = My.Resources.welcome_htm_error.Replace("error_icon.gif", strImageFile).Replace("#E9EDF3", "#F2F2F7")

            mErrorDocumentText = mErrorDocumentText.Replace("{0}", Me.Language.GetString("Welcome.Label.ConnFailed"))     '"Connection failed."
            '"Please try one of the following:{0}1. Check if your network is {0}working properly.{0}2.{1}Refresh{2} this page."
            Dim strPara0 As String = String.Format("<br/><div class=""{0}"">", Me.Language.CurrentLanguage.LangName)
            Dim strPara1 As String = String.Format("</div><div class=""{0}"">", Me.Language.CurrentLanguage.LangName)
            Dim strPara2 As String = "<a href=""iTong://Refresh"">"
            Dim strPara3 As String = "</a>"

            mErrorDocumentText = mErrorDocumentText.Replace("{1}", String.Format(Me.Language.GetString("Welcome.Message.HowToRefreshPage"), strPara0, strPara1, strPara2, strPara3))
        End If

        wb.DocumentText = mErrorDocumentText
    End Sub

    Private Sub webLogin_NewWindow3(ByVal sender As tbWebBrowserEx, ByRef ppDisp As System.Object, ByRef Cancel As System.Boolean, ByVal dwFlags As System.UInt32, ByVal bstrUrlContext As System.String, ByVal bstrUrl As System.String) Handles webLogin.NewWindow3
        Cancel = True
    End Sub

End Class