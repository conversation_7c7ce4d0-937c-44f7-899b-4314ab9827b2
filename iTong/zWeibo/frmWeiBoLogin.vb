﻿Public Class frmWeiBoLogin

    Private mOAuth As OAuthSina

    Public Sub New(ByVal oauth As OAuthSina)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.Icon = My.Resources.iTong
        Me.tbShowTitleOnForm = True
        Me.CanResize = False
        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False

        Me.mOAuth = oauth
        'Me.AutoSetFont = False
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.StartPosition = FormStartPosition.CenterScreen

        Me.txtUserID.Text = WeiboConfig.GetUser()
        Me.txtPwd.Text = WeiboConfig.GetPwd()

        Me.chkRemember.Checked = (Me.txtPwd.Text.Length > 0)
        Me.lblErrorInfo.Text = String.Empty
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.Language.GetString("WeiBo.Label.Login")                                                                '"新浪微博登录"
        Me.lblNoLogin.Text = Me.Language.GetString("WeiBo.Label.NoLogin")                                                   '"您还没有登录"
        Me.lblRegister.Text = Me.Language.GetString("WeiBo.Label.Register")                                                 '"没有账号？立即注册微博"
        Me.lblUser.Text = Me.Language.GetString("WeiBo.Label.Account") & Me.Language.GetString("Common.Symbol.Colon")       '"帐号："
        Me.lblPwd.Text = Me.Language.GetString("Common.Password") & Me.Language.GetString("Common.Symbol.Colon")            '"密码："
        Me.chkRemember.Text = Me.Language.GetString("WeiBo.Label.AoutLoginNextTime")                                        '"下次自动登录"
        Me.lblShowTip.Text = Me.Language.GetString("App.Lable.Logging")                                                     '"正在登录..."
        Me.btnLogin.Text = Me.Language.GetString("Common.Login")                                                            '"登录"
        Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")                                                          '"取消"
    End Sub

    Private Sub lblRegister_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblRegister.LinkClicked
        Common.OpenExplorer("http://weibo.com/signup/signup.php")
    End Sub

    Private Sub btnCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

    Private Sub btnLogin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnLogin.Click
        Me.Login()
    End Sub

    Private Sub txtPwd_KeyDown(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyEventArgs) Handles txtPwd.KeyDown
        If e.KeyCode = Keys.Enter Then
            Me.Login()
        End If
    End Sub

    Private Sub bgwLogin_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwLogin.DoWork
        Try
            Dim arrData() As String = e.Argument
            If Me.mOAuth.ClientLogin(arrData(0), arrData(1)) Then
                'Dim strUser As String = Me.mOAuth.GetUserInfo(Me.mOAuth.AccessToken.UID)
                'Dim json As JsonObject = JsonParser.ParseString(strUser)
                'If json.ContainsKey("name") Then
                '    Me.mOAuth.OAuth.AccessToken.NickName = CType(json.Item("name"), JsonString).Value
                'End If
                'If json IsNot Nothing andalso ctype(json,json Then

                'End If

                e.Result = True
            Else
                e.Result = False
            End If

        Catch ex As Exception
        End Try
    End Sub

    Private Sub bgwLogin_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwLogin.RunWorkerCompleted
        Me.btnLogin.Enabled = True
        Me.txtUserID.ReadOnly = False
        Me.txtPwd.ReadOnly = False
        Me.chkRemember.Enabled = True

        If CBool(e.Result) Then
            Me.Close()
        Else
            Me.lblErrorInfo.Text = Me.Language.GetString("WeiBo.Message.TypeInIdAndPassword")   '"您输入的帐号或者密码错误！"
        End If
    End Sub

    Private Sub frmLoginSina_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shown
        If Me.txtUserID.Text.Trim().Length AndAlso Me.txtPwd.Text.Length > 0 Then
            Me.Login()
        End If
    End Sub

    Private Sub Login()
        If Me.txtUserID.Text.Trim().Length = 0 Then
            Me.lblErrorInfo.Text = Me.Language.GetString("WeiBo.Message.TypeInId")              '"请输入账户！"
            Me.txtUserID.Focus()
            Return
        End If

        If Me.txtPwd.Text.Trim().Length = 0 Then
            Me.lblErrorInfo.Text = Me.Language.GetString("WeiBo.Message.TypeInPassword")        '"请输入密码！"
            Me.txtPwd.Focus()
            Return
        End If

        Me.btnLogin.Enabled = False
        Me.txtUserID.ReadOnly = True
        Me.txtPwd.ReadOnly = True
        Me.chkRemember.Enabled = False

        If Me.chkRemember.Checked Then
            WeiboConfig.SetValue(Me.txtUserID.Text.Trim(), Me.txtPwd.Text)
        Else
            WeiboConfig.SetValue(Me.txtUserID.Text.Trim(), String.Empty)
        End If

        Me.bgwLogin.RunWorkerAsync(New String() {Me.txtUserID.Text.Trim(), Me.txtPwd.Text})
    End Sub

End Class
