﻿Imports System.Collections.Generic
Imports System.Text
Imports System.Security.Cryptography
Imports System.IO
Imports Microsoft.Win32

Public NotInheritable Class WeiboConfig

    Shared rgbKey As Byte() = New Byte() {&H44, &H4A, &H45, &H53, &H4C, &H79, &H18, &H99}
    Shared rgbIV As Byte() = New Byte() {&H20, 9, 9, 7, &H20, &H10, 4, &H10}

    Private Shared Function Encrypt(ByVal encryptString As String) As String
        If String.IsNullOrEmpty(encryptString) Then
            Return String.Empty
        End If
        Dim bytes As Byte() = Encoding.UTF8.GetBytes(encryptString)
        Try
            Dim provider As New DESCryptoServiceProvider()
            Dim stream As New MemoryStream()
            Dim stream2 As New CryptoStream(stream, provider.CreateEncryptor(rgbKey, rgbIV), CryptoStreamMode.Write)
            stream2.Write(bytes, 0, bytes.Length)
            stream2.FlushFinalBlock()
            Return Convert.ToBase64String(stream.ToArray())
        Catch
            Return String.Empty
        End Try
    End Function

    Private Shared Function Decrypt(ByVal decryptStr As String) As String
        If String.IsNullOrEmpty(decryptStr) Then
            Return String.Empty
        End If
        Try
            Dim buffer As Byte() = Convert.FromBase64String(decryptStr)
            Dim provider As New DESCryptoServiceProvider()
            Dim stream As New MemoryStream()
            Dim stream2 As New CryptoStream(stream, provider.CreateDecryptor(rgbKey, rgbIV), CryptoStreamMode.Write)
            stream2.Write(buffer, 0, buffer.Length)
            stream2.FlushFinalBlock()
            Return Encoding.UTF8.GetString(stream.ToArray())
        Catch
            Return String.Empty
        End Try
    End Function

    Public Shared Sub SetValue(ByVal uid As String, ByVal pwd As String)
        Dim hkml As RegistryKey = Registry.CurrentUser
        Dim tongbuKey As RegistryKey = Registry.CurrentUser.OpenSubKey("Software\TongBu\", True)
        If tongbuKey Is Nothing Then
            tongbuKey = Registry.CurrentUser.CreateSubKey("Software\TongBu")
        End If

        tongbuKey.SetValue("SinaUser", Encrypt(uid))
        tongbuKey.SetValue("SinaPwd", Encrypt(pwd))
    End Sub

    Public Shared Sub ClearUser()
        Dim hkml As RegistryKey = Registry.CurrentUser
        Dim tongbuKey As RegistryKey = Registry.CurrentUser.OpenSubKey("Software\TongBu\", True)
        If tongbuKey Is Nothing Then
            tongbuKey = Registry.CurrentUser.CreateSubKey("Software\TongBu")
        End If
        tongbuKey.SetValue("SinaPwd", Encrypt(""))
    End Sub

    Public Shared Function GetUser() As String
        Return GetValue("SinaUser")
    End Function

    Public Shared Function GetPwd() As String
        Return GetValue("SinaPwd")
    End Function

    Private Shared Function GetValue(ByVal key As String) As String
        Dim hkml As RegistryKey = Registry.CurrentUser
        Dim tongbuKey As RegistryKey = Registry.CurrentUser.OpenSubKey("Software\TongBu\", True)
        If tongbuKey IsNot Nothing Then
            Return Decrypt(tongbuKey.GetValue(key).ToString())
        Else
            Return String.Empty
        End If
    End Function

    Public Shared Function SaveConfig(ByVal clientType As ClientType, ByVal oauth As OAuthBase) As Boolean
        Dim blnResult As Boolean = False

        Try
            Dim dict As New Dictionary(Of Object, Object)

            dict.Add("Code", oauth.RequestToken.Code)
            dict.Add("OpenID", oauth.RequestToken.OpenID)
            dict.Add("OpenKey", oauth.RequestToken.OpenKey)

            dict.Add("Token", oauth.AccessToken.Token)
            dict.Add("TokenSecret", oauth.AccessToken.TokenSecret)
            dict.Add("UID", oauth.AccessToken.UID)
            dict.Add("Name", oauth.AccessToken.Name)
            dict.Add("NickName", oauth.AccessToken.NickName)
            dict.Add("ExpireDate", oauth.AccessToken.ExpireDate)

            Dim strPath As String = Path.Combine(Folder.CacheFolder, clientType.ToString() & ".tmp")
            Dim arrData() As Byte = iTong.Device.CoreFoundation.CreatePlistBinaryData(dict)
            Dim arrEncrypt() As Byte = Common.EncryptDES(arrData)

            Using fs As New FileStream(strPath, FileMode.Create)
                fs.Write(arrEncrypt, 0, arrEncrypt.Length)
            End Using

            arrEncrypt = Nothing
            arrData = Nothing

            blnResult = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SaveConfig")
        End Try

        Return blnResult
    End Function

    Public Shared Function LoadConfig(ByVal clientType As ClientType) As ClientBase
        Dim client As ClientBase = Nothing

        Try
            Dim strPath As String = Path.Combine(Folder.CacheFolder, clientType.ToString() & ".tmp")
            If Not File.Exists(strPath) Then
                GoTo DO_EXIT
            End If

            Dim oauth As OAuthBase = Nothing
            Dim arrData() As Byte = File.ReadAllBytes(strPath)
            Dim arrDescrypt() As Byte = Common.DecryptDES(arrData)

            Dim dict As Dictionary(Of Object, Object) = iTong.Device.CoreFoundation.ManagedPropertyListFromXMLData(arrDescrypt)

            arrDescrypt = Nothing
            arrData = Nothing

            If dict Is Nothing Then
                GoTo DO_EXIT
            End If

            Select Case clientType
                Case ClientType.Sina
                    oauth = New OAuthSina()

                Case clientType.Tecent
                    oauth = New OAuthTecent()

                Case clientType.Facebook
                    oauth = New OAuthFacebook()

                Case clientType.Twitter
                    oauth = New OAuthTwitter()

            End Select

            If dict.ContainsKey("Code") Then
                oauth.RequestToken.Code = dict("Code")
            End If

            oauth.RequestToken.OpenID = dict("OpenID")
            oauth.RequestToken.OpenKey = dict("OpenKey")
            oauth.AccessToken.Token = dict("Token")
            oauth.AccessToken.TokenSecret = dict("TokenSecret")
            oauth.AccessToken.UID = dict("UID")
            oauth.AccessToken.Name = dict("Name")
            oauth.AccessToken.NickName = dict("NickName")
            oauth.AccessToken.ExpireDate = dict("ExpireDate")

            dict.Clear()

            '如果存在Token超时时间，则进行检验
            If Not String.IsNullOrEmpty(oauth.AccessToken.ExpireDate) Then
                '服务器与电脑有时间差别，故减掉一个小时
                Dim dtExpire As Date = Now
                If Date.TryParse(oauth.AccessToken.ExpireDate, dtExpire) Then
                    If dtExpire.AddHours(-1) < Now Then
                        GoTo DO_EXIT
                    End If
                End If
            End If


            Select Case clientType
                Case clientType.Sina
                    client = New ClientSina(oauth)

                Case clientType.Tecent
                    client = New ClientTecent(oauth)

                Case clientType.Facebook
                    client = New ClientFacebook(oauth)

                Case clientType.Twitter
                    client = New ClientTwitter(oauth)

            End Select
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadConfig")
        End Try

DO_EXIT:
        Return client
    End Function

    Public Shared Function DeleteConfig(ByVal clientType As ClientType) As Boolean
        Dim blnResult As Boolean = False

        Try
            Dim strPath As String = Path.Combine(Folder.CacheFolder, clientType.ToString() & ".tmp")
            If File.Exists(strPath) Then
                File.Delete(strPath)
            End If
            blnResult = True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DeleteConfig")
        End Try

DO_EXIT:
        Return blnResult
    End Function

End Class
