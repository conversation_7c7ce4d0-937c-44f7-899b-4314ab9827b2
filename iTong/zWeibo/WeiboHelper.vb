﻿Public Class WeiboHelper

    'Private Shared mClient As ClientSina
    Private Shared mLocker As New Object

    Public Shared Function SendMessage(ByVal clientType As ClientType, ByVal owner As Object, ByVal strMsg As String, ByVal imgPicture As Image, ByVal isBlack As Boolean) As Boolean
        Return SendMessage(clientType, owner, strMsg, imgPicture, String.Empty, Nothing, 0.0F, 0.0F, isBlack)
    End Function

    Public Shared Function SendMessage(ByVal clientType As ClientType, ByVal owner As Object, ByVal strMsg As String, ByVal imgPicture As Image, ByVal strDeviceType As String, ByVal isBlack As Boolean) As Boolean
        Return SendMessage(clientType, owner, strMsg, imgPicture, strDeviceType, Nothing, 0.0F, 0.0F, isBlack)
    End Function

    Public Shared Function SendMessage(ByVal clientType As ClientType, ByVal owner As Object, ByVal strMsg As String, ByVal imgPicture As Image, ByVal strDeviceType As String, ByVal callbackReloadScreeenShot As EventHandler(Of WeiboImageEventArgs), ByVal isBlack As Boolean) As Boolean
        Return SendMessage(clientType, owner, strMsg, imgPicture, strDeviceType, callbackReloadScreeenShot, 0.0F, 0.0F, isBlack)
    End Function

    Public Shared Function SendMessage(ByVal clientType As ClientType, ByVal owner As Object, ByVal strMsg As String, ByVal imgPicture As Image, ByVal strDeviceType As String, ByVal callbackReloadScreeenShot As EventHandler(Of WeiboImageEventArgs), ByVal isBlack As Boolean, ByVal isAndroid As Boolean) As Boolean
        Return SendMessage(clientType, owner, strMsg, imgPicture, strDeviceType, callbackReloadScreeenShot, 0.0F, 0.0F, isBlack, isAndroid)
    End Function

    Public Shared Function SendMessage(ByVal clientType As ClientType, ByVal owner As Object, ByVal strMsg As String, ByVal imgPicture As Image, ByVal strDeviceType As String, ByVal callbackReloadScreeenShot As EventHandler(Of WeiboImageEventArgs), ByVal latitude As Single, ByVal longitude As Single, ByVal isBlack As Boolean) As Boolean
        Return SendMessage(clientType, owner, strMsg, imgPicture, strDeviceType, callbackReloadScreeenShot, latitude, longitude, isBlack, False)
    End Function

    Public Shared Function SendMessage(ByVal clientType As ClientType, ByVal owner As Object, ByVal strMsg As String, ByVal imgPicture As Image, ByVal strDeviceType As String, ByVal callbackReloadScreeenShot As EventHandler(Of WeiboImageEventArgs), ByVal latitude As Single, ByVal longitude As Single, ByVal isBlack As Boolean, ByVal isAndroid As Boolean) As Boolean
        Dim blnResult As Boolean = False
        If Utility.IsPanda Then
            Return blnResult
        End If

        Try
            If String.IsNullOrEmpty(strMsg) Then
                Select Case Folder.LangType
                    Case LanguageType.zh_CN
                        If clientType <> CoreFoundation.ClientType.Sina Then
                            strMsg = "#同步助手#"

                            If Utility.IsPanda Then
                                strMsg = "#熊猫苹果助手#"
                            End If
                        End If

                    Case LanguageType.th_TH
                        strMsg = "#ผู้ช่วย AA#"
                    Case LanguageType.th_TH
                        strMsg = "#Dongbo#"
                    Case LanguageType.en_US
                        strMsg = "#iClover#"
                End Select
            End If
DO_LOGIN:
            'If mClient Is Nothing Then
            '    mClient = New ClientSina(New OAuthSina())
            'End If

            'If String.IsNullOrEmpty(mClient.OAuth.AccessToken.Token) Then
            '    Dim frm As New frmWeiBoLogin(mClient)
            '    frm.ShowDialog()
            'End If

            'If String.IsNullOrEmpty(mClient.OAuth.AccessToken.Token) Then
            '    GoTo DO_EXIT
            'End If

            'clientType = CoreFoundation.ClientType.Facebook

            Using frmSend As New frmWeiboSend(clientType, strMsg, imgPicture, strDeviceType, callbackReloadScreeenShot, latitude, longitude, isBlack)
                frmSend.btnSetting.Enabled = Not isAndroid
                frmSend.mBlnIsAndroid = isAndroid
                frmSend.ShowDialog(owner)
                blnResult = frmSend.mResult
                If owner IsNot Nothing AndAlso TypeOf owner Is Form Then
                    CType(owner, Form).Activate()
                End If
            End Using

            'Dim dResult As DialogResult = DialogResult.OK
            'If owner IsNot Nothing Then
            '    dResult = frmSend.ShowDialog(owner)

            '    If TypeOf owner Is MainForm Then
            '        CType(owner, MainForm).BringToFront()
            '    End If
            'Else
            '    dResult = frmSend.ShowDialog()
            'End If

            'Select Case dResult
            '    Case DialogResult.OK
            '        blnResult = True

            '    Case DialogResult.Abort
            '        GoTo DO_LOGIN

            'End Select

        Catch ex As Exception
            Common.LogException(ex.ToString(), "WeiboHelper.SendMessage")
        End Try

DO_EXIT:
        Return blnResult
    End Function

End Class

Public Class WeiboImageEventArgs
    Inherits EventArgs

    Private mImage As Image = Nothing
    Public Property Image() As Image
        Get
            Return Me.mImage
        End Get
        Set(ByVal value As Image)
            Me.mImage = value
        End Set
    End Property

    Private mHasFrame As Boolean = False
    Public Property HasFrame() As Boolean
        Get
            Return Me.mHasFrame
        End Get
        Set(ByVal value As Boolean)
            Me.mHasFrame = value
        End Set
    End Property

    Private mIsBlack As Boolean = False
    Public Property IsBlack() As Boolean
        Get
            Return Me.mIsBlack
        End Get
        Set(ByVal value As Boolean)
            Me.mIsBlack = value
        End Set
    End Property

End Class