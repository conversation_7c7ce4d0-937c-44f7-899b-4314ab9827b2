﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeiBoLogin
    Inherits frmDeviceBase

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeiBoLogin))
        Me.pnlMain = New tbPanel
        Me.lblRegister = New System.Windows.Forms.LinkLabel
        Me.lblNoLogin = New System.Windows.Forms.Label
        Me.txtUserID = New System.Windows.Forms.TextBox
        Me.txtPwd = New System.Windows.Forms.TextBox
        Me.chkRemember = New System.Windows.Forms.CheckBox
        Me.lblErrorInfo = New System.Windows.Forms.Label
        Me.lblPwd = New System.Windows.Forms.Label
        Me.lblUser = New System.Windows.Forms.Label
        Me.lblShowTip = New System.Windows.Forms.Label
        Me.btnCancel = New tbButton
        Me.btnLogin = New tbButton
        Me.bgwLogin = New System.ComponentModel.BackgroundWorker
        Me.pnlMain.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(336, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(312, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(288, 0)
        '
        'pnlMain
        '
        Me.pnlMain.BackColor = System.Drawing.Color.Transparent
        Me.pnlMain.Controls.Add(Me.lblRegister)
        Me.pnlMain.Controls.Add(Me.lblNoLogin)
        Me.pnlMain.Controls.Add(Me.txtUserID)
        Me.pnlMain.Controls.Add(Me.txtPwd)
        Me.pnlMain.Controls.Add(Me.chkRemember)
        Me.pnlMain.Controls.Add(Me.lblErrorInfo)
        Me.pnlMain.Controls.Add(Me.lblPwd)
        Me.pnlMain.Controls.Add(Me.lblUser)
        Me.pnlMain.Location = New System.Drawing.Point(2, 32)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(356, 171)
        Me.pnlMain.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlMain.TabIndex = 22
        Me.pnlMain.tbBackgroundImage = Nothing
        Me.pnlMain.tbShowWatermark = False
        Me.pnlMain.tbSplit = "8,22,8,9"
        Me.pnlMain.tbWatermark = Nothing
        Me.pnlMain.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlMain.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblRegister
        '
        Me.lblRegister.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblRegister.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(210, Byte), Integer))
        Me.lblRegister.Location = New System.Drawing.Point(164, 22)
        Me.lblRegister.Name = "lblRegister"
        Me.lblRegister.Size = New System.Drawing.Size(176, 17)
        Me.lblRegister.TabIndex = 0
        Me.lblRegister.TabStop = True
        Me.lblRegister.Text = "没有账号？立即注册微博"
        Me.lblRegister.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblNoLogin
        '
        Me.lblNoLogin.Location = New System.Drawing.Point(26, 22)
        Me.lblNoLogin.Name = "lblNoLogin"
        Me.lblNoLogin.Size = New System.Drawing.Size(99, 17)
        Me.lblNoLogin.TabIndex = 0
        Me.lblNoLogin.Text = "您还没有登录"
        Me.lblNoLogin.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'txtUserID
        '
        Me.txtUserID.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtUserID.Location = New System.Drawing.Point(109, 57)
        Me.txtUserID.Name = "txtUserID"
        Me.txtUserID.Size = New System.Drawing.Size(169, 21)
        Me.txtUserID.TabIndex = 1
        '
        'txtPwd
        '
        Me.txtPwd.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.txtPwd.Location = New System.Drawing.Point(109, 94)
        Me.txtPwd.Name = "txtPwd"
        Me.txtPwd.PasswordChar = Global.Microsoft.VisualBasic.ChrW(42)
        Me.txtPwd.Size = New System.Drawing.Size(169, 21)
        Me.txtPwd.TabIndex = 2
        '
        'chkRemember
        '
        Me.chkRemember.AutoSize = True
        Me.chkRemember.Location = New System.Drawing.Point(109, 125)
        Me.chkRemember.Name = "chkRemember"
        Me.chkRemember.Size = New System.Drawing.Size(96, 16)
        Me.chkRemember.TabIndex = 3
        Me.chkRemember.Text = "下次自动登录"
        Me.chkRemember.UseVisualStyleBackColor = True
        '
        'lblErrorInfo
        '
        Me.lblErrorInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblErrorInfo.ForeColor = System.Drawing.Color.Red
        Me.lblErrorInfo.Location = New System.Drawing.Point(15, 147)
        Me.lblErrorInfo.Name = "lblErrorInfo"
        Me.lblErrorInfo.Size = New System.Drawing.Size(327, 21)
        Me.lblErrorInfo.TabIndex = 6
        Me.lblErrorInfo.Text = "请输入密码"
        Me.lblErrorInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'lblPwd
        '
        Me.lblPwd.Location = New System.Drawing.Point(28, 96)
        Me.lblPwd.Name = "lblPwd"
        Me.lblPwd.Size = New System.Drawing.Size(80, 17)
        Me.lblPwd.TabIndex = 3
        Me.lblPwd.Text = "密码："
        Me.lblPwd.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblUser
        '
        Me.lblUser.Location = New System.Drawing.Point(28, 59)
        Me.lblUser.Name = "lblUser"
        Me.lblUser.Size = New System.Drawing.Size(80, 17)
        Me.lblUser.TabIndex = 1
        Me.lblUser.Text = "帐号："
        Me.lblUser.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblShowTip
        '
        Me.lblShowTip.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblShowTip.AutoEllipsis = True
        Me.lblShowTip.BackColor = System.Drawing.Color.Transparent
        Me.lblShowTip.ForeColor = System.Drawing.Color.Black
        Me.lblShowTip.Location = New System.Drawing.Point(7, 210)
        Me.lblShowTip.Name = "lblShowTip"
        Me.lblShowTip.Size = New System.Drawing.Size(187, 15)
        Me.lblShowTip.TabIndex = 23
        Me.lblShowTip.Text = "正在登录微博，请稍候..."
        Me.lblShowTip.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblShowTip.Visible = False
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.Location = New System.Drawing.Point(275, 206)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(65, 23)
        Me.btnCancel.TabIndex = 25
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = My.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.Black
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.Black
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'btnLogin
        '
        Me.btnLogin.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnLogin.BackColor = System.Drawing.Color.Transparent
        Me.btnLogin.BindingForm = Nothing
        Me.btnLogin.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnLogin.Location = New System.Drawing.Point(200, 206)
        Me.btnLogin.Name = "btnLogin"
        Me.btnLogin.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnLogin.Selectable = True
        Me.btnLogin.Size = New System.Drawing.Size(65, 23)
        Me.btnLogin.TabIndex = 24
        Me.btnLogin.tbAdriftIconWhenHover = False
        Me.btnLogin.tbAutoSize = False
        Me.btnLogin.tbAutoSizeEx = False
        Me.btnLogin.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnLogin.tbBackgroundImageState = ImageState.FourState
        Me.btnLogin.tbBadgeNumber = 0
        Me.btnLogin.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnLogin.tbEndEllipsis = False
        Me.btnLogin.tbIconHoldPlace = True
        Me.btnLogin.tbIconImage = Nothing
        Me.btnLogin.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.tbIconImageState = ImageState.OneState
        Me.btnLogin.tbIconMore = False
        Me.btnLogin.tbIconMouseDown = Nothing
        Me.btnLogin.tbIconMouseHover = Nothing
        Me.btnLogin.tbIconMouseLeave = Nothing
        Me.btnLogin.tbIconPlaceText = 2
        Me.btnLogin.tbIconReadOnly = Nothing
        Me.btnLogin.tbImageMouseDown = Nothing
        Me.btnLogin.tbImageMouseHover = Nothing
        Me.btnLogin.tbImageMouseLeave = Nothing
        Me.btnLogin.tbReadOnly = False
        Me.btnLogin.tbShadow = False
        Me.btnLogin.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnLogin.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnLogin.tbShowNew = False
        Me.btnLogin.tbShowToolTipOnButton = False
        Me.btnLogin.tbSplit = "13,11,13,11"
        Me.btnLogin.tbText = "登录"
        Me.btnLogin.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnLogin.tbTextColor = System.Drawing.Color.White
        Me.btnLogin.tbTextColorDisable = System.Drawing.Color.White
        Me.btnLogin.tbTextColorDown = System.Drawing.Color.White
        Me.btnLogin.tbTextColorHover = System.Drawing.Color.White
        Me.btnLogin.tbTextMouseDownPlace = 0
        Me.btnLogin.tbToolTip = ""
        Me.btnLogin.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnLogin.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnLogin.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'bgwLogin
        '
        Me.bgwLogin.WorkerSupportsCancellation = True
        '
        'frmLoginSina
        '
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(360, 240)
        Me.Controls.Add(Me.lblShowTip)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnLogin)
        Me.Controls.Add(Me.pnlMain)
        Me.FilletRadius = 5
        Me.MinimumSize = New System.Drawing.Size(20, 66)
        Me.Name = "frmLoginSina"
        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbSplit = "10,33,10,33"
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.pnlMain, 0)
        Me.Controls.SetChildIndex(Me.btnLogin, 0)
        Me.Controls.SetChildIndex(Me.btnCancel, 0)
        Me.Controls.SetChildIndex(Me.lblShowTip, 0)
        Me.pnlMain.ResumeLayout(False)
        Me.pnlMain.PerformLayout()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents pnlMain As tbPanel
    Private WithEvents lblRegister As System.Windows.Forms.LinkLabel
    Private WithEvents lblNoLogin As System.Windows.Forms.Label
    Friend WithEvents txtUserID As System.Windows.Forms.TextBox
    Friend WithEvents txtPwd As System.Windows.Forms.TextBox
    Friend WithEvents chkRemember As System.Windows.Forms.CheckBox
    Friend WithEvents lblErrorInfo As System.Windows.Forms.Label
    Friend WithEvents lblPwd As System.Windows.Forms.Label
    Friend WithEvents lblUser As System.Windows.Forms.Label
    Private WithEvents lblShowTip As System.Windows.Forms.Label
    Friend WithEvents btnCancel As tbButton
    Friend WithEvents btnLogin As tbButton
    Friend WithEvents bgwLogin As System.ComponentModel.BackgroundWorker

End Class
