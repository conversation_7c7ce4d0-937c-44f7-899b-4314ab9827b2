﻿Public Class frmWeiboSend

    Private mClient As ClientBase = Nothing
    Private mClientType As ClientType = ClientType.Sina
    Private mImageSend As Image
    Private mTimeElapse As Integer = 6
    Public mBlnIsAndroid As Boolean = False

    Private ReloadImage As EventHandler(Of WeiboImageEventArgs)

    Public mResult As Boolean = False

    Public Sub New(ByVal clientType As ClientType, _
                   ByVal strMsg As String, _
                   ByVal imgSend As Image, _
                   ByVal strDeviceType As String, _
                   Optional ByVal callbackScreenShot As EventHandler(Of WeiboImageEventArgs) = Nothing, _
                   Optional ByVal latitude As Single = 0.0F, _
                   Optional ByVal longitude As Single = 0.0F, _
                   Optional ByVal isBlack As Boolean = False)

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Language = LanguageInterface.Instance()
        Me.Icon = My.Resources.iTong
        Me.tbShowTitleOnForm = True
        Me.CanResize = False
        Me.btn_minimize.Visible = False
        Me.btn_normal.Visible = False

        Me.mClientType = clientType
		Me.txtMsg.Text = strMsg
		If imgSend IsNot Nothing Then
			Try
				Me.mImageSend = imgSend
				Me.picSend.Image = imgSend
			Catch ex As Exception

			End Try
			
		End If
        
        'Me.picSend.BackColor = Color.FromArgb(233, 237, 243)

        ReloadImage = callbackScreenShot

        If String.IsNullOrEmpty(strDeviceType) Then
            Me.pnlScreenshot.Visible = False
            Me.pnlWeiBo.Visible = True
            Me.lblMessage.Visible = False

        Else
            Me.pnlScreenshot.Visible = True
            Me.pnlWeiBo.Visible = False

            Me.lblMessage.Visible = isBlack
            If strDeviceType.ToLower().Contains("iphone") Then
                Me.lblMessage.Width = 95
            Else
                Me.lblMessage.Width = 138
            End If
            If Common.VerIs30() Then
                Me.lblMessage.Location = New Point((Me.Width - Me.lblMessage.Width) \ 2, Me.lblErrMgs.Location.Y)
                lblCloseInfo.Location = New Point(lblCloseInfo.Left, Me.lblErrMgs.Location.Y)
            Else
                Me.lblMessage.Location = New Point((Me.Width - Me.lblMessage.Width) \ 2, Me.lblMessage.Location.Y)
            End If
        End If

        Me.btnSend.Enabled = True
        'Me.TopMost = True
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.tbGuiBackground = My.Resources.frm_bg_blank.Clone
        Me.StartPosition = FormStartPosition.CenterScreen

        Me.lblLogout.Enabled = True
        Me.pnlLoading.Visible = False
        Me.lblGotoWeibo.Visible = False
        Me.ShowInTaskbar = True

        Me.lblErrMgs.Visible = False

        Me.InitClient()
    End Sub

    Private Sub InitClient()
        Select Case Me.mClientType
            Case ClientType.Sina
                Me.Text = Me.Language.GetString("File.Menu.ShareToSina")            '"分享到新浪微博"
                Me.lblGotoWeibo.Text = String.Format(Me.Language.GetString("WeiBo.Label.ViewTo"), Me.Language.GetString("WeiBo.Label.Sina"))

            Case ClientType.Tecent
                Me.Text = Me.Language.GetString("WeiBo.Button.ShareToTencent")      '"分享到腾讯微博"
                Me.lblGotoWeibo.Text = String.Format(Me.Language.GetString("WeiBo.Label.ViewTo"), Me.Language.GetString("WeiBo.Label.Tencent"))

            Case ClientType.Facebook
                Me.Text = String.Format(Me.Language.GetString("WeiBo.Label.ShareTo"), "Facebook")                   '"分享到Facebook"
                Me.lblGotoWeibo.Text = String.Format(Me.Language.GetString("WeiBo.Label.ViewTo"), "Facebook")       '"到Facebook看看"

            Case ClientType.Twitter
                Me.Text = String.Format(Me.Language.GetString("WeiBo.Label.ShareTo"), "Twitter")                    '"分享到Twitter"
                Me.lblGotoWeibo.Text = String.Format(Me.Language.GetString("WeiBo.Label.ViewTo"), "Twitter")        '"到Twitter看看"

        End Select

        Me.mClient = WeiboConfig.LoadConfig(Me.mClientType)

        Me.UpdateUI()
    End Sub

    Private Sub UpdateUI()
        If Me.mClient IsNot Nothing Then
            If Not String.IsNullOrEmpty(Me.mClient.OAuth.AccessToken.NickName) Then
                Me.lblUserName.Text = Me.Language.GetString("Components.Login.Label.Nickname") & Me.Language.GetString("Common.Symbol.Colon") & Me.mClient.OAuth.AccessToken.NickName      '"昵称："

            ElseIf Not String.IsNullOrEmpty(Me.mClient.OAuth.AccessToken.Name) Then
                Me.lblUserName.Text = Me.Language.GetString("Contact.Label.UserName") & Me.Language.GetString("Common.Symbol.Colon") & Me.mClient.OAuth.AccessToken.Name   '"用户名："

            Else
                Me.lblUserName.Text = String.Empty
            End If

            Me.lblLogout.Text = Me.Language.GetString("WeiBo.Message.ChangeUser")       '"更改用户"
        Else
            Me.lblUserName.Text = String.Empty
            Me.lblLogout.Text = Me.Language.GetString("Common.Login")                           '"登录"
        End If
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        'Me.Text = Me.Language.GetString("WeiBo.Label.ShareToWeiBo")                                                         '"分享到微博"

        Me.lblMessage.Text = Me.Language.GetString("Welcome.Message.UnlockDeviceIfBlackScreenshot")                         '"截取图片均为设备实时界面。如遇黑屏，请解锁设备重新截屏。"
        Me.lblLoading.Text = Me.Language.GetString("WeiBo.Message.Loading")                                                 '"正在获取信息，请稍候 ..."
        'Me.lblLeftNumInfo.Text = String.Format(Me.Language.GetString("WeiBo.Message.CharCount"), 140)                       '"你还可以输入140个字"

        Me.btnSend.Text = Me.Language.GetString("WeiBo.Button.Send")                                                        '"发布"

        Me.tsmiScreenshot.Text = Me.Language.GetString("Welcome.Text.ScreenshotNoShell")                                    '"截屏(无外壳)"
        Me.tsmiScreenshotShell.Text = Me.Language.GetString("Welcome.Text.ScreenshotHaveShell")                             '"截屏(带外壳)"

        'If Me.mSinaClient IsNot Nothing AndAlso Me.mSinaClient.OAuth.AccessToken.NickName.Length > 0 Then
        '    Me.lblUserName.Text = Me.lblUserName.Text & Me.mSinaClient.OAuth.AccessToken.NickName
        'End If

    End Sub

    '判断图片是不是黑色的
    Private Function CheckPicBlack(ByVal img As Image) As Boolean
        Dim blnReturn As Boolean = True

        If img Is Nothing Then
            Return blnReturn
        End If

        Dim bmpobj As New Bitmap(img)

        Try

            Dim intCenterX As Integer = bmpobj.Width \ 2
            Dim intCenterY As Integer = bmpobj.Height \ 2

            For index As Integer = 0 To bmpobj.Height - 1
                Dim colorPoint As Color = bmpobj.GetPixel(intCenterX, index)
                If colorPoint.A <> Color.Black.A OrElse colorPoint.R <> Color.Black.R OrElse colorPoint.G <> Color.Black.G OrElse colorPoint.B <> Color.Black.B Then
                    blnReturn = False
                    Return blnReturn
                End If
            Next

            For index As Integer = 0 To bmpobj.Width - 1
                Dim colorPoint As Color = bmpobj.GetPixel(index, intCenterY)
                If colorPoint.A <> Color.Black.A OrElse colorPoint.R <> Color.Black.R OrElse colorPoint.G <> Color.Black.G OrElse colorPoint.B <> Color.Black.B Then
                    blnReturn = False

                    Return blnReturn
                End If
            Next

        Catch ex As InvalidOperationException
            Debug.Write(ex)
        Catch ex As Exception
            Debug.Write(ex)
        Finally
            bmpobj.Dispose()
            bmpobj = Nothing
        End Try

        Return blnReturn
    End Function

    Private Sub DoOAuth(ByVal sender As Object, ByVal e As EventArgs)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New EventHandler(AddressOf DoOAuth), sender, e)
            Else
                Dim newClient As ClientBase = Nothing

                Select Case Me.mClientType
                    Case ClientType.Sina
                        Dim oauth As New OAuthSina()

                        Dim strUrl As String = oauth.GetAuthorizeURL()

                        If Not String.IsNullOrEmpty(strUrl) Then
                            Using frm As New frmVerify(strUrl)
                                If frm.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
                                    oauth.RequestToken = frm.RequestToken
                                    oauth.GetAccessTokenByAuthorizationCode(oauth.RequestToken.Code)

                                    If oauth.AccessToken IsNot Nothing AndAlso Not String.IsNullOrEmpty(oauth.AccessToken.Token) Then
                                        newClient = New ClientSina(oauth)
                                        newClient.GetUserInfo()
                                    End If
                                End If
                            End Using
                        End If

                        'Dim frmLogin As New frmWeiBoLogin(oauth)
                        'frmLogin.ShowDialog(Me)

                        'If Not String.IsNullOrEmpty(oauth.AccessToken.Token) Then
                        '    Dim sina As New ClientSina(oauth)
                        '    Dim strInfo As String = sina.GetUserInfo(oauth.AccessToken.UID)

                        '    Dim json As JsonObject = JsonParser.ParseString(strInfo)
                        '    If json.ContainsKey("name") Then
                        '        oauth.AccessToken.NickName = CType(json.Item("name"), JsonString).Value
                        '        e.Client = sina
                        '    End If
                        'End If

                    Case ClientType.Tecent
                        Dim oauth As New OAuthTecent()
                        Dim strUrl As String = oauth.GetAuthorizeURL()

                        If Not String.IsNullOrEmpty(strUrl) Then
                            Using frm As New frmVerify(strUrl)
                                If frm.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
                                    oauth.RequestToken = frm.RequestToken
                                    oauth.GetAccessTokenByAuthorizationCode(oauth.RequestToken.Code)

                                    If oauth.AccessToken IsNot Nothing AndAlso Not String.IsNullOrEmpty(oauth.AccessToken.Token) Then
                                        newClient = New ClientTecent(oauth)
                                    End If
                                End If
                            End Using
                        End If

                    Case ClientType.Facebook
                        Dim oauth As New OAuthFacebook()
                        Dim strUrl As String = oauth.GetAuthorizeURL()

                        If Not String.IsNullOrEmpty(strUrl) Then
                            Using frm As New frmVerify(strUrl)
                                If frm.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
                                    oauth.AccessToken = frm.AccessToken

                                    If oauth.AccessToken IsNot Nothing AndAlso Not String.IsNullOrEmpty(oauth.AccessToken.Token) Then
                                        newClient = New ClientFacebook(oauth)
                                        newClient.GetUserInfo()
                                    End If
                                End If
                            End Using
                        End If

                    Case ClientType.Twitter
                        Dim oauth As New OAuthTwitter()
                        Dim strUrl As String = oauth.GetAuthorizeURL()

                        If Not String.IsNullOrEmpty(strUrl) Then
                            Using frm As New frmVerify(strUrl)
                                If frm.ShowDialog(Me) = Windows.Forms.DialogResult.OK Then
                                    oauth.RequestToken = frm.RequestToken
                                    oauth.GetAccessTokenByAuthorizationCode(oauth.RequestToken.Code)

                                    If oauth.AccessToken IsNot Nothing AndAlso Not String.IsNullOrEmpty(oauth.AccessToken.Token) Then
                                        newClient = New ClientTwitter(oauth)
                                        newClient.GetUserInfo()
                                    End If
                                End If
                            End Using
                        End If

                End Select


                Me.mClient = newClient
                If Me.mClient IsNot Nothing Then
                    WeiboConfig.SaveConfig(Me.mClientType, Me.mClient.OAuth)
                End If
                Me.UpdateUI()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DoOAuth")
        End Try
    End Sub

    Private Sub bgwSend_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwSend.DoWork
        Try
            e.Result = False

            Dim arrData() As Object = e.Argument

            If Me.mClient Is Nothing Then
                Me.DoOAuth(Me, New EventArgs())
            End If

            If Me.mClient IsNot Nothing Then
                Dim strMsg As String = arrData(0)
                Dim imgShow As Image = CType(arrData(1), Image).Clone()
                e.Result = Me.mClient.SendMessage(strMsg, imgShow)
                imgShow.Dispose()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwSend_DoWork")
        End Try
DO_EXIT:
        Me.mResult = e.Result
    End Sub

    Private Sub bgwSend_RunWorkerCompleted(ByVal sender As System.Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwSend.RunWorkerCompleted
        Me.pnlLoading.Visible = False

        If CBool(e.Result) Then
            'Me.DialogResult = Windows.Forms.DialogResult.OK
            Me.tmrClose.Start()
            Me.lblGotoWeibo.Visible = True
        Else
            Me.btnSend.Enabled = True
            Me.lblGotoWeibo.Visible = False
            Me.lblCloseInfo.Text = String.Empty

            If ReloadImage IsNot Nothing Then
                Me.pnlScreenshot.Enabled = True
            Else
                Me.pnlWeiBo.Enabled = True
            End If

            If Me.mClient IsNot Nothing Then
                If Me.mClient.OAuth.TimeOut Then
                    Me.lblErrMgs.Text = Me.Language.GetString("WeiBo.Message.SendFailureTimeOut")          '"发送失败，网络超时！"
                Else
                    Me.lblErrMgs.Text = Me.Language.GetString("WeiBo.Message.SendFailure")          '"发送失败，点击此处重新登录！"
                End If
                Me.lblErrMgs.Visible = True
            End If

            'tbMessageBox.Show(Me, Me.Language.GetString("WeiBo.Message.SendTimeOut"), Me.Language.GetString("Common.Info"), System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error)
            Me.txtMsg.ReadOnly = False
        End If
    End Sub

    Private Sub lblLogout_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblLogout.LinkClicked, lblErrMgs.LinkClicked
        'WeiboConfig.clearUser()
        Select Case Me.mClientType
            Case ClientType.Sina
                'CookieApi.SetCookie("http://sina.com.cn/", "U_TRS1", "", Now.AddYears(-1))
                'CookieApi.SetCookie("http://sina.com.cn/", "sso_info", "", Now.AddYears(-1))

            Case ClientType.Tecent

            Case ClientType.Facebook
                CookieApi.SetCookie("http://facebook.com/", "datr", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://facebook.com/", "c_user", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://facebook.com/", "csm", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://facebook.com/", "fr", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://facebook.com/", "lu", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://facebook.com/", "s", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://facebook.com/", "xs", "", Now.AddYears(-1))


            Case ClientType.Twitter
                CookieApi.SetCookie("http://twitter.com/", "auth_token", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://twitter.com/", "secure_session", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://twitter.com/", "twll", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://twitter.com/", "remember_checked", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://twitter.com/", "remember_checked_on", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://twitter.com/", "guest_id", "", Now.AddYears(-1))
                CookieApi.SetCookie("http://twitter.com/", "dnt", "", Now.AddYears(-1))

        End Select


        WeiboConfig.DeleteConfig(Me.mClientType)
        Me.DoOAuth(Me, New EventArgs())
    End Sub

    Private Sub lblGotoWeibo_LinkClicked(ByVal sender As System.Object, ByVal e As System.Windows.Forms.LinkLabelLinkClickedEventArgs) Handles lblGotoWeibo.LinkClicked
        Select Case Me.mClientType
            Case ClientType.Sina
                Common.OpenExplorer("http://t.sina.com.cn/")

            Case ClientType.Tecent
                Common.OpenExplorer("http://t.qq.com/")

            Case ClientType.Facebook
                Common.OpenExplorer("http://www.facebook.com/")

            Case ClientType.Twitter
                Common.OpenExplorer("http://www.twitter.com/")

        End Select
    End Sub

    Private Sub btnView_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnView.Click, picSend.DoubleClick
        Using frmShow As New tbImageViewForm()
            '会被释放掉，所以要先克隆一份 add by zsh
            frmShow.SrcPicture = mImageSend.Clone()
            frmShow.ViewType = ViewType.FromPC
            frmShow.ShowListView = False
            frmShow.HideButton = ToolBarButtonType.Main Or ToolBarButtonType.Delete Or ToolBarButtonType.Prev Or ToolBarButtonType.[Next] Or ToolBarButtonType.WeiBo

            frmShow.ShowDialog()
        End Using
    End Sub

    Private Sub btnTurnLeft_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnTurnLeft.Click
        If Me.mImageSend IsNot Nothing Then
            Me.mImageSend.RotateFlip(RotateFlipType.Rotate90FlipXY)
            Me.picSend.Image = Me.mImageSend
        End If
    End Sub

    Private Sub btnTurnRight_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnTurnRight.Click
        If Me.mImageSend IsNot Nothing Then
            Me.mImageSend.RotateFlip(RotateFlipType.Rotate270FlipXY)
            Me.picSend.Image = Me.mImageSend
        End If
    End Sub

    Private Sub btnReload_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnReload.Click
        Me.SetControlStatus(False)

        Dim args As New WeiboImageEventArgs()
        args.HasFrame = Me.tsmiScreenshotShell.Checked

        If ReloadImage IsNot Nothing Then
            ReloadImage(Me, args)
        End If

        If args.Image IsNot Nothing Then
            Me.mImageSend = args.Image
            Me.picSend.Image = args.Image
            Me.picSend.Refresh()

            Me.lblMessage.Visible = args.IsBlack
        End If

        Me.SetControlStatus(True)
    End Sub

    Private Sub btnSetting_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSetting.Click
        If IniSetting.GetScreenshotShell() Then
            Me.tsmiScreenshotShell.Image = My.Resources.tsmi_checked
            Me.tsmiScreenshot.Image = Nothing
        Else
            Me.tsmiScreenshotShell.Image = Nothing
            Me.tsmiScreenshot.Image = My.Resources.tsmi_checked
        End If
        Me.munSetUp.Show(Me.btnSetting, New Point(0, Me.btnSetting.Bottom + 1))
    End Sub

    Public Sub SetControlStatus(ByVal blnEnable As Boolean)
        Me.btnReload.Enabled = blnEnable
        If mBlnIsAndroid Then
            Me.btnSetting.Enabled = False
        Else
            Me.btnSetting.Enabled = blnEnable
        End If
        Me.btnSend.Enabled = blnEnable

        Application.DoEvents()
    End Sub

    Private Sub btnSend_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles btnSend.Click

        If Me.txtMsg.Text.Trim.Length = 0 Then
            '弹框的提示，会让同步助手也展示出来，因此改为界面上Lable的提示。
            ' tbMessageBox.Show("文字不能为空！", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            Me.lblCloseInfo.Text = Me.Language.GetString("WeiBo.Message.TxtCannotEmpty")        '"文字不能为空！"
            Me.txtMsg.Focus()
            Return
        End If

        Me.btnSend.Enabled = False
        Me.pnlWeiBo.Enabled = False
        Me.pnlScreenshot.Enabled = False
        Me.txtMsg.ReadOnly = True
        'Me.pnlLoading.Visible = Me.pnlScreenshot.Visible

        Me.lblCloseInfo.Text = Me.Language.GetString("WeiBo.Message.Sending")                   '"正在发送微博，请稍候..."
        Me.lblErrMgs.Visible = False

        Me.bgwSend.RunWorkerAsync(New Object() {Me.txtMsg.Text.Trim(), Me.mImageSend})
    End Sub

    Private Sub txtMsg_TextChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles txtMsg.TextChanged
        'Dim strTem As String = "你还可以输入{0}个字"
        Dim hanziNum As Integer = 0
        Dim intLeft As Integer = 0

        If Folder.LangType = LanguageType.zh_CN Then
            hanziNum = System.Text.RegularExpressions.Regex.Matches(Me.txtMsg.Text, "[\u2e80-\ufffd]").Count
            intLeft = 135 - hanziNum - (Me.txtMsg.Text.Length - hanziNum) \ 2
        Else
            Dim intMax As Integer = IIf(Me.mClientType = ClientType.Twitter, 117, 500)
            hanziNum = Me.txtMsg.Text.Length
            intLeft = intMax - hanziNum
        End If
        Me.txtMsg.MaxLength = Me.txtMsg.Text.Length + intLeft
        '"你还可以输入{0}个字"
        Me.lblLeftNumInfo.Text = String.Format(Me.Language.GetString("WeiBo.Message.CharCount"), intLeft)

        If Me.txtMsg.Text.Trim.Length = 0 Then
            Me.lblCloseInfo.Text = Me.Language.GetString("WeiBo.Message.TxtCannotEmpty")        '"文字不能为空！"
        Else
            Me.lblCloseInfo.Text = String.Empty
        End If
    End Sub

    Private Sub tmrClose_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrClose.Tick
        Me.mTimeElapse -= 1
        If Me.mTimeElapse = 0 Then
            Me.DialogResult = Windows.Forms.DialogResult.OK
        Else
            '"发送成功，再过{0}秒, 窗体关闭！"
            Me.lblCloseInfo.Text = String.Format(Me.Language.GetString("WeiBo.Message.SendSucceed"), Me.mTimeElapse)
        End If
    End Sub

    Private Sub frmSendSina_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shown
        Me.txtMsg.Focus()
        'Me.txtMsg.SelectionStart = Me.txtMsg.TextLength
        Me.txtMsg.Select(Me.txtMsg.TextLength, 0)
    End Sub

    Private Sub tsmiScreenshot_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiScreenshot.Click
        IniSetting.SetScreenshotShell(False)
    End Sub

    Private Sub tsmiScreenshotShell_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiScreenshotShell.Click
        IniSetting.SetScreenshotShell(True)
    End Sub

    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)
        If Me.Owner IsNot Nothing AndAlso TypeOf Me.Owner Is MainForm Then
            CType(Me.Owner, MainForm).BringToFront()
        End If
    End Sub

End Class