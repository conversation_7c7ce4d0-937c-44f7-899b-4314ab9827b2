﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmWeiboSend
    Inherits frmDeviceBase

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmWeiboSend))
        Me.bgwSend = New System.ComponentModel.BackgroundWorker
        Me.btnSend = New tbButton
        Me.pnlMain = New tbPanel
        Me.lblMessage = New System.Windows.Forms.Label
        Me.pnlScreenshot = New tbPanel
        Me.btnSetting = New tbButton
        Me.btnReload = New tbButton
        Me.pnlWeiBo = New tbPanel
        Me.btnTurnRight = New tbButton
        Me.btnTurnLeft = New tbButton
        Me.btnView = New tbButton
        Me.pnlLoading = New System.Windows.Forms.Panel
        Me.lblLoading = New System.Windows.Forms.Label
        Me.picLoading = New System.Windows.Forms.PictureBox
        Me.picSend = New System.Windows.Forms.PictureBox
        Me.lblLogout = New System.Windows.Forms.LinkLabel
        Me.lblLeftNumInfo = New System.Windows.Forms.Label
        Me.txtMsg = New System.Windows.Forms.TextBox
        Me.lblUserName = New System.Windows.Forms.Label
        Me.lblGotoWeibo = New System.Windows.Forms.LinkLabel
        Me.munSetUp = New tbContextMenuStrip(Me.components)
        Me.tsmiScreenshot = New System.Windows.Forms.ToolStripMenuItem
        Me.tsmiScreenshotShell = New System.Windows.Forms.ToolStripMenuItem
        Me.lblCloseInfo = New System.Windows.Forms.Label
        Me.tmrClose = New System.Windows.Forms.Timer(Me.components)
        Me.lblErrMgs = New System.Windows.Forms.LinkLabel
        Me.pnlMain.SuspendLayout()
        Me.pnlScreenshot.SuspendLayout()
        Me.pnlWeiBo.SuspendLayout()
        Me.pnlLoading.SuspendLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.picSend, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.munSetUp.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Location = New System.Drawing.Point(464, 0)
        '
        'btn_normal
        '
        Me.btn_normal.Location = New System.Drawing.Point(440, 0)
        Me.btn_normal.tbBackgroundImage = CType(resources.GetObject("btn_normal.tbBackgroundImage"), System.Drawing.Image)
        '
        'btn_minimize
        '
        Me.btn_minimize.Location = New System.Drawing.Point(416, 0)
        '
        'bgwSend
        '
        Me.bgwSend.WorkerSupportsCancellation = True
        '
        'btnSend
        '
        Me.btnSend.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSend.BackColor = System.Drawing.Color.Transparent
        Me.btnSend.BindingForm = Nothing
        Me.btnSend.Enabled = False
        Me.btnSend.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSend.Location = New System.Drawing.Point(375, 441)
        Me.btnSend.Name = "btnSend"
        Me.btnSend.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnSend.Selectable = True
        Me.btnSend.Size = New System.Drawing.Size(77, 25)
        Me.btnSend.TabIndex = 3
        Me.btnSend.tbAdriftIconWhenHover = False
        Me.btnSend.tbAutoSize = False
        Me.btnSend.tbAutoSizeEx = False
        Me.btnSend.tbBackgroundImage = My.Resources.btn_4_blue
        Me.btnSend.tbBackgroundImageState = ImageState.FourState
        Me.btnSend.tbBadgeNumber = 0
        Me.btnSend.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSend.tbEndEllipsis = False
        Me.btnSend.tbIconHoldPlace = True
        Me.btnSend.tbIconImage = Nothing
        Me.btnSend.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSend.tbIconImageState = ImageState.OneState
        Me.btnSend.tbIconMore = False
        Me.btnSend.tbIconMouseDown = Nothing
        Me.btnSend.tbIconMouseHover = Nothing
        Me.btnSend.tbIconMouseLeave = Nothing
        Me.btnSend.tbIconPlaceText = 2
        Me.btnSend.tbIconReadOnly = Nothing
        Me.btnSend.tbImageMouseDown = Nothing
        Me.btnSend.tbImageMouseHover = Nothing
        Me.btnSend.tbImageMouseLeave = Nothing
        Me.btnSend.tbReadOnly = False
        Me.btnSend.tbReadOnlyText = False
        Me.btnSend.tbShadow = False
        Me.btnSend.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnSend.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnSend.tbShowNew = False
        Me.btnSend.tbShowToolTipOnButton = False
        Me.btnSend.tbSplit = "13,11,13,11"
        Me.btnSend.tbText = "发布"
        Me.btnSend.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSend.tbTextColor = System.Drawing.Color.White
        Me.btnSend.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSend.tbTextColorDown = System.Drawing.Color.White
        Me.btnSend.tbTextColorHover = System.Drawing.Color.White
        Me.btnSend.tbTextMouseDownPlace = 0
        Me.btnSend.tbToolTip = ""
        Me.btnSend.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSend.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSend.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSend.VisibleEx = True
        '
        'pnlMain
        '
        Me.pnlMain.BackColor = System.Drawing.Color.Transparent
        Me.pnlMain.Controls.Add(Me.lblMessage)
        Me.pnlMain.Controls.Add(Me.pnlScreenshot)
        Me.pnlMain.Controls.Add(Me.pnlWeiBo)
        Me.pnlMain.Controls.Add(Me.pnlLoading)
        Me.pnlMain.Controls.Add(Me.picSend)
        Me.pnlMain.Controls.Add(Me.lblLogout)
        Me.pnlMain.Controls.Add(Me.lblLeftNumInfo)
        Me.pnlMain.Controls.Add(Me.txtMsg)
        Me.pnlMain.Controls.Add(Me.lblUserName)
        Me.pnlMain.Location = New System.Drawing.Point(3, 34)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(482, 398)
        Me.pnlMain.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlMain.TabIndex = 0
        Me.pnlMain.tbBackgroundImage = Nothing
        Me.pnlMain.tbShowWatermark = False
        Me.pnlMain.tbSplit = "8,22,8,9"
        Me.pnlMain.tbWatermark = Nothing
        Me.pnlMain.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlMain.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'lblMessage
        '
        Me.lblMessage.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblMessage.BackColor = System.Drawing.Color.Black
        Me.lblMessage.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblMessage.ForeColor = System.Drawing.Color.White
        Me.lblMessage.Location = New System.Drawing.Point(174, 109)
        Me.lblMessage.Name = "lblMessage"
        Me.lblMessage.Size = New System.Drawing.Size(123, 82)
        Me.lblMessage.TabIndex = 25
        Me.lblMessage.Text = "截取图片均为设备实时界面。如遇黑屏，请解锁设备重新截屏。"
        Me.lblMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblMessage.Visible = False
        '
        'pnlScreenshot
        '
        Me.pnlScreenshot.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.pnlScreenshot.Controls.Add(Me.btnSetting)
        Me.pnlScreenshot.Controls.Add(Me.btnReload)
        Me.pnlScreenshot.Location = New System.Drawing.Point(201, 280)
        Me.pnlScreenshot.Name = "pnlScreenshot"
        Me.pnlScreenshot.Size = New System.Drawing.Size(80, 28)
        Me.pnlScreenshot.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlScreenshot.TabIndex = 4
        Me.pnlScreenshot.tbBackgroundImage = Nothing
        Me.pnlScreenshot.tbShowWatermark = False
        Me.pnlScreenshot.tbSplit = "0,0,0,0"
        Me.pnlScreenshot.tbWatermark = Nothing
        Me.pnlScreenshot.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlScreenshot.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnSetting
        '
        Me.btnSetting.BackColor = System.Drawing.Color.Transparent
        Me.btnSetting.BindingForm = Nothing
        Me.btnSetting.Location = New System.Drawing.Point(47, 5)
        Me.btnSetting.Name = "btnSetting"
        Me.btnSetting.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSetting.Selectable = True
        Me.btnSetting.Size = New System.Drawing.Size(18, 18)
        Me.btnSetting.TabIndex = 1
        Me.btnSetting.tbAdriftIconWhenHover = False
        Me.btnSetting.tbAutoSize = False
        Me.btnSetting.tbAutoSizeEx = False
        Me.btnSetting.tbBackgroundImage = My.Resources.btn_Setup
        Me.btnSetting.tbBackgroundImageState = ImageState.ThreeState
        Me.btnSetting.tbBadgeNumber = 0
        Me.btnSetting.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSetting.tbEndEllipsis = False
        Me.btnSetting.tbIconHoldPlace = True
        Me.btnSetting.tbIconImage = Nothing
        Me.btnSetting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetting.tbIconImageState = ImageState.OneState
        Me.btnSetting.tbIconMore = False
        Me.btnSetting.tbIconMouseDown = Nothing
        Me.btnSetting.tbIconMouseHover = Nothing
        Me.btnSetting.tbIconMouseLeave = Nothing
        Me.btnSetting.tbIconPlaceText = 2
        Me.btnSetting.tbIconReadOnly = Nothing
        Me.btnSetting.tbImageMouseDown = Nothing
        Me.btnSetting.tbImageMouseHover = Nothing
        Me.btnSetting.tbImageMouseLeave = Nothing
        Me.btnSetting.tbReadOnly = False
        Me.btnSetting.tbReadOnlyText = False
        Me.btnSetting.tbShadow = False
        Me.btnSetting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSetting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSetting.tbShowNew = False
        Me.btnSetting.tbShowToolTipOnButton = False
        Me.btnSetting.tbSplit = "3,3,3,3"
        Me.btnSetting.tbText = ""
        Me.btnSetting.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetting.tbTextColor = System.Drawing.Color.White
        Me.btnSetting.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSetting.tbTextColorDown = System.Drawing.Color.White
        Me.btnSetting.tbTextColorHover = System.Drawing.Color.White
        Me.btnSetting.tbTextMouseDownPlace = 0
        Me.btnSetting.tbToolTip = ""
        Me.btnSetting.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSetting.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSetting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSetting.VisibleEx = True
        '
        'btnReload
        '
        Me.btnReload.BackColor = System.Drawing.Color.Transparent
        Me.btnReload.BindingForm = Nothing
        Me.btnReload.Location = New System.Drawing.Point(16, 5)
        Me.btnReload.Name = "btnReload"
        Me.btnReload.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnReload.Selectable = True
        Me.btnReload.Size = New System.Drawing.Size(18, 18)
        Me.btnReload.TabIndex = 0
        Me.btnReload.tbAdriftIconWhenHover = False
        Me.btnReload.tbAutoSize = True
        Me.btnReload.tbAutoSizeEx = False
        Me.btnReload.tbBackgroundImage = My.Resources.btn_refresh
        Me.btnReload.tbBackgroundImageState = ImageState.ThreeState
        Me.btnReload.tbBadgeNumber = 0
        Me.btnReload.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnReload.tbEndEllipsis = False
        Me.btnReload.tbIconHoldPlace = True
        Me.btnReload.tbIconImage = Nothing
        Me.btnReload.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReload.tbIconImageState = ImageState.OneState
        Me.btnReload.tbIconMore = False
        Me.btnReload.tbIconMouseDown = Nothing
        Me.btnReload.tbIconMouseHover = Nothing
        Me.btnReload.tbIconMouseLeave = Nothing
        Me.btnReload.tbIconPlaceText = 2
        Me.btnReload.tbIconReadOnly = Nothing
        Me.btnReload.tbImageMouseDown = Nothing
        Me.btnReload.tbImageMouseHover = Nothing
        Me.btnReload.tbImageMouseLeave = Nothing
        Me.btnReload.tbReadOnly = False
        Me.btnReload.tbReadOnlyText = False
        Me.btnReload.tbShadow = False
        Me.btnReload.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnReload.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnReload.tbShowNew = False
        Me.btnReload.tbShowToolTipOnButton = False
        Me.btnReload.tbSplit = "3,3,3,3"
        Me.btnReload.tbText = ""
        Me.btnReload.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReload.tbTextColor = System.Drawing.Color.White
        Me.btnReload.tbTextColorDisable = System.Drawing.Color.White
        Me.btnReload.tbTextColorDown = System.Drawing.Color.White
        Me.btnReload.tbTextColorHover = System.Drawing.Color.White
        Me.btnReload.tbTextMouseDownPlace = 0
        Me.btnReload.tbToolTip = ""
        Me.btnReload.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnReload.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnReload.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnReload.VisibleEx = True
        '
        'pnlWeiBo
        '
        Me.pnlWeiBo.Anchor = System.Windows.Forms.AnchorStyles.Bottom
        Me.pnlWeiBo.Controls.Add(Me.btnTurnRight)
        Me.pnlWeiBo.Controls.Add(Me.btnTurnLeft)
        Me.pnlWeiBo.Controls.Add(Me.btnView)
        Me.pnlWeiBo.Location = New System.Drawing.Point(181, 280)
        Me.pnlWeiBo.Name = "pnlWeiBo"
        Me.pnlWeiBo.Size = New System.Drawing.Size(120, 28)
        Me.pnlWeiBo.SizeMode = tbPanel.tbSizeMode.StretchImage
        Me.pnlWeiBo.TabIndex = 3
        Me.pnlWeiBo.tbBackgroundImage = Nothing
        Me.pnlWeiBo.tbShowWatermark = False
        Me.pnlWeiBo.tbSplit = "0,0,0,0"
        Me.pnlWeiBo.tbWatermark = Nothing
        Me.pnlWeiBo.tbWatermarkAlignment = System.Drawing.ContentAlignment.TopLeft
        Me.pnlWeiBo.tbWatermarkLocation = New System.Drawing.Point(0, 0)
        '
        'btnTurnRight
        '
        Me.btnTurnRight.BackColor = System.Drawing.Color.Transparent
        Me.btnTurnRight.BindingForm = Nothing
        Me.btnTurnRight.Location = New System.Drawing.Point(84, 5)
        Me.btnTurnRight.Name = "btnTurnRight"
        Me.btnTurnRight.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnTurnRight.Selectable = True
        Me.btnTurnRight.Size = New System.Drawing.Size(18, 18)
        Me.btnTurnRight.TabIndex = 1
        Me.btnTurnRight.tbAdriftIconWhenHover = False
        Me.btnTurnRight.tbAutoSize = False
        Me.btnTurnRight.tbAutoSizeEx = False
        Me.btnTurnRight.tbBackgroundImage = My.Resources.btn_turn_right
        Me.btnTurnRight.tbBackgroundImageState = ImageState.ThreeState
        Me.btnTurnRight.tbBadgeNumber = 0
        Me.btnTurnRight.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTurnRight.tbEndEllipsis = False
        Me.btnTurnRight.tbIconHoldPlace = True
        Me.btnTurnRight.tbIconImage = Nothing
        Me.btnTurnRight.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTurnRight.tbIconImageState = ImageState.OneState
        Me.btnTurnRight.tbIconMore = False
        Me.btnTurnRight.tbIconMouseDown = Nothing
        Me.btnTurnRight.tbIconMouseHover = Nothing
        Me.btnTurnRight.tbIconMouseLeave = Nothing
        Me.btnTurnRight.tbIconPlaceText = 2
        Me.btnTurnRight.tbIconReadOnly = Nothing
        Me.btnTurnRight.tbImageMouseDown = Nothing
        Me.btnTurnRight.tbImageMouseHover = Nothing
        Me.btnTurnRight.tbImageMouseLeave = Nothing
        Me.btnTurnRight.tbReadOnly = False
        Me.btnTurnRight.tbReadOnlyText = False
        Me.btnTurnRight.tbShadow = False
        Me.btnTurnRight.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnTurnRight.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnTurnRight.tbShowNew = False
        Me.btnTurnRight.tbShowToolTipOnButton = False
        Me.btnTurnRight.tbSplit = "3,3,3,3"
        Me.btnTurnRight.tbText = ""
        Me.btnTurnRight.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTurnRight.tbTextColor = System.Drawing.Color.White
        Me.btnTurnRight.tbTextColorDisable = System.Drawing.Color.White
        Me.btnTurnRight.tbTextColorDown = System.Drawing.Color.White
        Me.btnTurnRight.tbTextColorHover = System.Drawing.Color.White
        Me.btnTurnRight.tbTextMouseDownPlace = 0
        Me.btnTurnRight.tbToolTip = ""
        Me.btnTurnRight.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnTurnRight.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTurnRight.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTurnRight.VisibleEx = True
        '
        'btnTurnLeft
        '
        Me.btnTurnLeft.BackColor = System.Drawing.Color.Transparent
        Me.btnTurnLeft.BindingForm = Nothing
        Me.btnTurnLeft.Location = New System.Drawing.Point(51, 5)
        Me.btnTurnLeft.Name = "btnTurnLeft"
        Me.btnTurnLeft.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnTurnLeft.Selectable = True
        Me.btnTurnLeft.Size = New System.Drawing.Size(18, 18)
        Me.btnTurnLeft.TabIndex = 0
        Me.btnTurnLeft.tbAdriftIconWhenHover = False
        Me.btnTurnLeft.tbAutoSize = False
        Me.btnTurnLeft.tbAutoSizeEx = False
        Me.btnTurnLeft.tbBackgroundImage = My.Resources.btn_turn_left
        Me.btnTurnLeft.tbBackgroundImageState = ImageState.ThreeState
        Me.btnTurnLeft.tbBadgeNumber = 0
        Me.btnTurnLeft.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnTurnLeft.tbEndEllipsis = False
        Me.btnTurnLeft.tbIconHoldPlace = True
        Me.btnTurnLeft.tbIconImage = Nothing
        Me.btnTurnLeft.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTurnLeft.tbIconImageState = ImageState.OneState
        Me.btnTurnLeft.tbIconMore = False
        Me.btnTurnLeft.tbIconMouseDown = Nothing
        Me.btnTurnLeft.tbIconMouseHover = Nothing
        Me.btnTurnLeft.tbIconMouseLeave = Nothing
        Me.btnTurnLeft.tbIconPlaceText = 2
        Me.btnTurnLeft.tbIconReadOnly = Nothing
        Me.btnTurnLeft.tbImageMouseDown = Nothing
        Me.btnTurnLeft.tbImageMouseHover = Nothing
        Me.btnTurnLeft.tbImageMouseLeave = Nothing
        Me.btnTurnLeft.tbReadOnly = False
        Me.btnTurnLeft.tbReadOnlyText = False
        Me.btnTurnLeft.tbShadow = False
        Me.btnTurnLeft.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnTurnLeft.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnTurnLeft.tbShowNew = False
        Me.btnTurnLeft.tbShowToolTipOnButton = False
        Me.btnTurnLeft.tbSplit = "3,3,3,3"
        Me.btnTurnLeft.tbText = ""
        Me.btnTurnLeft.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTurnLeft.tbTextColor = System.Drawing.Color.White
        Me.btnTurnLeft.tbTextColorDisable = System.Drawing.Color.White
        Me.btnTurnLeft.tbTextColorDown = System.Drawing.Color.White
        Me.btnTurnLeft.tbTextColorHover = System.Drawing.Color.White
        Me.btnTurnLeft.tbTextMouseDownPlace = 0
        Me.btnTurnLeft.tbToolTip = ""
        Me.btnTurnLeft.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnTurnLeft.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnTurnLeft.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnTurnLeft.VisibleEx = True
        '
        'btnView
        '
        Me.btnView.BackColor = System.Drawing.Color.Transparent
        Me.btnView.BindingForm = Nothing
        Me.btnView.Location = New System.Drawing.Point(18, 5)
        Me.btnView.Name = "btnView"
        Me.btnView.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnView.Selectable = True
        Me.btnView.Size = New System.Drawing.Size(18, 18)
        Me.btnView.TabIndex = 0
        Me.btnView.tbAdriftIconWhenHover = False
        Me.btnView.tbAutoSize = True
        Me.btnView.tbAutoSizeEx = False
        Me.btnView.tbBackgroundImage = My.Resources.btn_view
        Me.btnView.tbBackgroundImageState = ImageState.ThreeState
        Me.btnView.tbBadgeNumber = 0
        Me.btnView.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnView.tbEndEllipsis = False
        Me.btnView.tbIconHoldPlace = True
        Me.btnView.tbIconImage = Nothing
        Me.btnView.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnView.tbIconImageState = ImageState.OneState
        Me.btnView.tbIconMore = False
        Me.btnView.tbIconMouseDown = Nothing
        Me.btnView.tbIconMouseHover = Nothing
        Me.btnView.tbIconMouseLeave = Nothing
        Me.btnView.tbIconPlaceText = 2
        Me.btnView.tbIconReadOnly = Nothing
        Me.btnView.tbImageMouseDown = Nothing
        Me.btnView.tbImageMouseHover = Nothing
        Me.btnView.tbImageMouseLeave = Nothing
        Me.btnView.tbReadOnly = False
        Me.btnView.tbReadOnlyText = False
        Me.btnView.tbShadow = False
        Me.btnView.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnView.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnView.tbShowNew = False
        Me.btnView.tbShowToolTipOnButton = False
        Me.btnView.tbSplit = "3,3,3,3"
        Me.btnView.tbText = ""
        Me.btnView.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnView.tbTextColor = System.Drawing.Color.White
        Me.btnView.tbTextColorDisable = System.Drawing.Color.White
        Me.btnView.tbTextColorDown = System.Drawing.Color.White
        Me.btnView.tbTextColorHover = System.Drawing.Color.White
        Me.btnView.tbTextMouseDownPlace = 0
        Me.btnView.tbToolTip = ""
        Me.btnView.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnView.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnView.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnView.VisibleEx = True
        '
        'pnlLoading
        '
        Me.pnlLoading.BackColor = System.Drawing.Color.Transparent
        Me.pnlLoading.Controls.Add(Me.lblLoading)
        Me.pnlLoading.Controls.Add(Me.picLoading)
        Me.pnlLoading.Location = New System.Drawing.Point(145, 84)
        Me.pnlLoading.Name = "pnlLoading"
        Me.pnlLoading.Size = New System.Drawing.Size(192, 142)
        Me.pnlLoading.TabIndex = 6
        '
        'lblLoading
        '
        Me.lblLoading.ForeColor = System.Drawing.Color.Black
        Me.lblLoading.Location = New System.Drawing.Point(10, 110)
        Me.lblLoading.Name = "lblLoading"
        Me.lblLoading.Size = New System.Drawing.Size(171, 12)
        Me.lblLoading.TabIndex = 0
        Me.lblLoading.Text = "正在获取信息，请稍候 ..."
        Me.lblLoading.TextAlign = System.Drawing.ContentAlignment.MiddleCenter
        '
        'picLoading
        '
        Me.picLoading.Image = My.Resources.gif_loading_32
        Me.picLoading.Location = New System.Drawing.Point(78, 43)
        Me.picLoading.Name = "picLoading"
        Me.picLoading.Size = New System.Drawing.Size(32, 32)
        Me.picLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.picLoading.TabIndex = 0
        Me.picLoading.TabStop = False
        '
        'picSend
        '
        Me.picSend.BackColor = System.Drawing.Color.Transparent
        Me.picSend.Cursor = System.Windows.Forms.Cursors.Hand
        Me.picSend.Location = New System.Drawing.Point(31, 29)
        Me.picSend.Name = "picSend"
        Me.picSend.Size = New System.Drawing.Size(419, 251)
        Me.picSend.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.picSend.TabIndex = 0
        Me.picSend.TabStop = False
        '
        'lblLogout
        '
        Me.lblLogout.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblLogout.LinkBehavior = System.Windows.Forms.LinkBehavior.HoverUnderline
        Me.lblLogout.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(210, Byte), Integer))
        Me.lblLogout.Location = New System.Drawing.Point(377, 8)
        Me.lblLogout.Name = "lblLogout"
        Me.lblLogout.Size = New System.Drawing.Size(73, 17)
        Me.lblLogout.TabIndex = 2
        Me.lblLogout.TabStop = True
        Me.lblLogout.Text = "更换帐号"
        Me.lblLogout.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblLeftNumInfo
        '
        Me.lblLeftNumInfo.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblLeftNumInfo.BackColor = System.Drawing.Color.Transparent
        Me.lblLeftNumInfo.Location = New System.Drawing.Point(295, 296)
        Me.lblLeftNumInfo.Name = "lblLeftNumInfo"
        Me.lblLeftNumInfo.Size = New System.Drawing.Size(157, 15)
        Me.lblLeftNumInfo.TabIndex = 5
        Me.lblLeftNumInfo.Text = "你还可以输入140个字"
        Me.lblLeftNumInfo.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'txtMsg
        '
        Me.txtMsg.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.txtMsg.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle
        Me.txtMsg.Font = New System.Drawing.Font("宋体", 10.0!)
        Me.txtMsg.Location = New System.Drawing.Point(31, 313)
        Me.txtMsg.MaxLength = 140
        Me.txtMsg.Multiline = True
        Me.txtMsg.Name = "txtMsg"
        Me.txtMsg.Size = New System.Drawing.Size(419, 79)
        Me.txtMsg.TabIndex = 0
        '
        'lblUserName
        '
        Me.lblUserName.BackColor = System.Drawing.Color.Transparent
        Me.lblUserName.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblUserName.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(210, Byte), Integer))
        Me.lblUserName.Location = New System.Drawing.Point(29, 8)
        Me.lblUserName.Name = "lblUserName"
        Me.lblUserName.Size = New System.Drawing.Size(302, 17)
        Me.lblUserName.TabIndex = 1
        Me.lblUserName.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblGotoWeibo
        '
        Me.lblGotoWeibo.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblGotoWeibo.BackColor = System.Drawing.Color.Transparent
        Me.lblGotoWeibo.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lblGotoWeibo.LinkColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(210, Byte), Integer))
        Me.lblGotoWeibo.Location = New System.Drawing.Point(255, 448)
        Me.lblGotoWeibo.Name = "lblGotoWeibo"
        Me.lblGotoWeibo.Size = New System.Drawing.Size(114, 17)
        Me.lblGotoWeibo.TabIndex = 2
        Me.lblGotoWeibo.TabStop = True
        Me.lblGotoWeibo.Text = "到新浪微博看看"
        Me.lblGotoWeibo.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'munSetUp
        '
        Me.munSetUp.AccessibleDescription = "153x70"
        Me.munSetUp.DropShadowEnabled = False
        Me.munSetUp.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.munSetUp.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmiScreenshot, Me.tsmiScreenshotShell})
        Me.munSetUp.Name = "munSearch"
        Me.munSetUp.Size = New System.Drawing.Size(143, 48)
        Me.munSetUp.Tag = ""
        Me.munSetUp.tbBackColor = System.Drawing.Color.White
        Me.munSetUp.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.munSetUp.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.munSetUp.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmiScreenshot
        '
        Me.tsmiScreenshot.Name = "tsmiScreenshot"
        Me.tsmiScreenshot.Size = New System.Drawing.Size(142, 22)
        Me.tsmiScreenshot.Text = "截屏(无外壳)"
        '
        'tsmiScreenshotShell
        '
        Me.tsmiScreenshotShell.Name = "tsmiScreenshotShell"
        Me.tsmiScreenshotShell.Size = New System.Drawing.Size(142, 22)
        Me.tsmiScreenshotShell.Text = "截屏(带外壳)"
        '
        'lblCloseInfo
        '
        Me.lblCloseInfo.AutoSize = True
        Me.lblCloseInfo.BackColor = System.Drawing.Color.Transparent
        Me.lblCloseInfo.ForeColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(210, Byte), Integer))
        Me.lblCloseInfo.Location = New System.Drawing.Point(32, 448)
        Me.lblCloseInfo.Name = "lblCloseInfo"
        Me.lblCloseInfo.Size = New System.Drawing.Size(0, 12)
        Me.lblCloseInfo.TabIndex = 1
        '
        'tmrClose
        '
        Me.tmrClose.Interval = 1000
        '
        'lblErrMgs
        '
        Me.lblErrMgs.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.lblErrMgs.BackColor = System.Drawing.Color.Transparent
        Me.lblErrMgs.ForeColor = System.Drawing.Color.Red
        Me.lblErrMgs.LinkBehavior = System.Windows.Forms.LinkBehavior.AlwaysUnderline
        Me.lblErrMgs.Location = New System.Drawing.Point(34, 448)
        Me.lblErrMgs.Name = "lblErrMgs"
        Me.lblErrMgs.Size = New System.Drawing.Size(308, 17)
        Me.lblErrMgs.TabIndex = 23
        Me.lblErrMgs.TabStop = True
        Me.lblErrMgs.Text = "发送失败..."
        '
        'frmWeiboSend
        '
        Me.CanbeMove = False
        Me.ClientSize = New System.Drawing.Size(488, 478)
        Me.Controls.Add(Me.lblCloseInfo)
        Me.Controls.Add(Me.btnSend)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.lblGotoWeibo)
        Me.Controls.Add(Me.lblErrMgs)
        Me.FilletRadius = 5
        Me.MinimumSize = New System.Drawing.Size(20, 66)
        Me.Name = "frmWeiboSend"
        Me.tbGuiBackground = My.Resources.frm_bg_blank
        Me.tbSplit = "10,33,10,33"
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Controls.SetChildIndex(Me.lblErrMgs, 0)
        Me.Controls.SetChildIndex(Me.btn_minimize, 0)
        Me.Controls.SetChildIndex(Me.btn_normal, 0)
        Me.Controls.SetChildIndex(Me.btn_close, 0)
        Me.Controls.SetChildIndex(Me.lblGotoWeibo, 0)
        Me.Controls.SetChildIndex(Me.pnlMain, 0)
        Me.Controls.SetChildIndex(Me.btnSend, 0)
        Me.Controls.SetChildIndex(Me.lblCloseInfo, 0)
        Me.pnlMain.ResumeLayout(False)
        Me.pnlMain.PerformLayout()
        Me.pnlScreenshot.ResumeLayout(False)
        Me.pnlWeiBo.ResumeLayout(False)
        Me.pnlLoading.ResumeLayout(False)
        Me.pnlLoading.PerformLayout()
        CType(Me.picLoading, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.picSend, System.ComponentModel.ISupportInitialize).EndInit()
        Me.munSetUp.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents bgwSend As System.ComponentModel.BackgroundWorker
    Friend WithEvents btnSend As tbButton
    Private WithEvents pnlMain As tbPanel
    Friend WithEvents pnlLoading As System.Windows.Forms.Panel
    Friend WithEvents lblLoading As System.Windows.Forms.Label
    Friend WithEvents picLoading As System.Windows.Forms.PictureBox
    Private WithEvents picSend As System.Windows.Forms.PictureBox
    Private WithEvents lblLogout As System.Windows.Forms.LinkLabel
    Private WithEvents lblLeftNumInfo As System.Windows.Forms.Label
    Private WithEvents txtMsg As System.Windows.Forms.TextBox
    Private WithEvents lblUserName As System.Windows.Forms.Label
    Private WithEvents lblGotoWeibo As System.Windows.Forms.LinkLabel
    Friend WithEvents pnlWeiBo As tbPanel
    Friend WithEvents btnTurnRight As tbButton
    Friend WithEvents btnTurnLeft As tbButton
    Friend WithEvents btnView As tbButton
    Friend WithEvents pnlScreenshot As tbPanel
    Friend WithEvents btnSetting As tbButton
    Friend WithEvents btnReload As tbButton
    Friend WithEvents munSetUp As tbContextMenuStrip
    Friend WithEvents tsmiScreenshot As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiScreenshotShell As System.Windows.Forms.ToolStripMenuItem
    Private WithEvents lblCloseInfo As System.Windows.Forms.Label
    Friend WithEvents tmrClose As System.Windows.Forms.Timer
    Public WithEvents lblMessage As System.Windows.Forms.Label
    Friend WithEvents lblErrMgs As System.Windows.Forms.LinkLabel

End Class
