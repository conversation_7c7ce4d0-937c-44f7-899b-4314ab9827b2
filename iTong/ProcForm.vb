﻿Imports System.Threading
Imports Microsoft.Win32
Imports iTong.CoreFoundation.FlashInfo

Public Class ProcForm

    Private _AllowCloseForm As Boolean = False
    Private _WaitForCloseForm As Boolean = False
    Public SplashForm As frmSplash
    Private _timeStart As DateTime
    Private _LstHideChildForms As New List(Of Form)
    Private _FirstRunFromUpgrade As Boolean = False     '是否是升级后第一次运行

    Private _language As LanguageInterface

    '' Added by Utmost20140903
    '' 命令行参数
    Private m_objCmdLineArgs As Object = Nothing
    Private m_ReActive As Boolean = False
    Private m_ProcFormClosing As Boolean = False

    Private Shared mTdCreateError As Thread


    Public Property ReActive() As Boolean
        Get
            Return Me.m_ReActive
        End Get
        Set(ByVal value As Boolean)
            Me.m_ReActive = value
        End Set
    End Property

    Public Property ProcFormClosing() As Boolean
        Get
            Return Me.m_ProcFormClosing
        End Get
        Set(ByVal value As Boolean)
            Me.m_ProcFormClosing = value
        End Set
    End Property

#Region "--- 单实例、构造 ---"

    Private Shared mProcForm As ProcForm = Nothing
    Private Shared mLocker As New Object

    Public Shared Function Instance() As ProcForm
        If mProcForm Is Nothing Then
            SyncLock mLocker
                If mProcForm Is Nothing Then
                    mProcForm = New ProcForm()
                    'mProcForm.Show()
                End If
            End SyncLock
        End If
        Return mProcForm
    End Function

    Public Sub New()
        '载入语言包
        Dim cultureInfo As System.Globalization.CultureInfo = System.Globalization.CultureInfo.CurrentCulture
        Dim langName As String = IniSetting.GetLanguage()
        If langName.Length > 0 Then
            Try
                cultureInfo = New System.Globalization.CultureInfo(langName)
            Catch ex As Exception
            End Try
        End If
        Me._language = LanguageInterface.Instance(cultureInfo)
        Common.CultureName = Me._language.CurrentLanguage.LangName

        ' 此调用是 Windows 窗体设计器所必需的。
        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。
        Me.Size = New Size(1, 1)
        Me.mNotifyIcon.Icon = My.Resources.iTong

        If Folder.AppType <> RunType.DuoBao Then
            Me.SplashForm = New frmSplash()
            Me.SplashForm.StartPosition = FormStartPosition.CenterScreen
            Me.SplashForm.Status = ""
            Me.SplashForm.Show()
        End If

        Application.DoEvents()
        Me._timeStart = DateTime.Now
        mProcForm = Me

        Me.InitLang(Me.tsmiSelectLang)

        Me.InitPlugins()

        '从服务器异步动态下载配置
        ServerIniSetting.DownloadConfig()


        '处理主线程内未处理的异常
        RemoveHandler Application.ThreadException, AddressOf OnThreadException
        AddHandler Application.ThreadException, AddressOf OnThreadException

        '处理非主线程中未处理的异常
        RemoveHandler AppDomain.CurrentDomain.UnhandledException, AddressOf UnhandledException
        AddHandler AppDomain.CurrentDomain.UnhandledException, AddressOf UnhandledException

        '处理一键安装
        If My.Application.CommandLineArgs.Count > 0 Then
            Dim strCommond As String = My.Application.CommandLineArgs.Item(0).Trim
            Me.ProcessAutoInstallArgs(strCommond, True)
        End If

#If IS_ITONG Then
        If Not Folder.LangType = LanguageType.en_US AndAlso CheckImport.CheckDB() AndAlso Folder.AppType <> RunType.DuoBao Then
            ProcForm.Instance().SplashForm.Status = Me._language.GetString("Main.Message.ImportDB")          '"正在为您导入同步助手1.x版本的资料库..."
            CheckImport.Import()
        End If
#End If

        Try
            ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf ChatWebPage.InitChatWebPage), False)  ''' 初始化网页，释放资源到本地，解压微信显示包

            '删除之前不小心释放出去的微信删除恢复的dll
            Dim strFileWeChatHelperOld As String = Path.Combine(Folder.AppFolder, "WeChat\undark.exe")
            If File.Exists(strFileWeChatHelperOld) Then
                File.Delete(strFileWeChatHelperOld)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "Delete_strFileWeChatHelperOld")
        End Try
    End Sub



    ''' 通用的驱动直接从网络上下载——Added by Utmost20150507
    'Private Sub CopyUsbDriver()
    '    Try
    '        Dim driverPath As String = Path.Combine(Folder.DocumentFolder, "AndroidDriver")

    '        Folder.CheckFolder(driverPath)

    '        Dim srcName As String
    '        If SystemPlatform.IsWin64 Then
    '            srcName = Path.Combine(Application.StartupPath, "Codes\usbdriver64.zip")
    '        Else
    '            srcName = Path.Combine(Application.StartupPath, "Codes\usbdriver32.zip")
    '        End If

    '        Dim destName As String = Path.Combine(driverPath, "usbdriver.zip")
    '        If File.Exists(srcName) AndAlso Not File.Exists(destName) Then
    '            File.Copy(srcName, destName, True)
    '        End If
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "CopyUsbDriver")
    '    End Try
    'End Sub

    Private Sub InitPlugins()
        ProcFormHelper.Instance().InitPlugins()

        Dim downloadMgr As MultiThreadDownload = MultiThreadDownload.Instance()
        AddHandler downloadMgr.DownloadItemCompleted, AddressOf OnDownloaded

        If Folder.LangType = LanguageType.zh_CN Then
            AddHandler downloadMgr.ActvieID, AddressOf OnActiveID
        End If
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Dim item As MultiThreadDownloadItem = CType(sender, MultiThreadDownloadItem)

        Common.Log("Music OnDownloaded: " & item.ItemInfo.FilePath)
        If Not e.Cancel AndAlso e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0 Then
            If item.ItemInfo.Class = ResourceClass.Music Then
                Common.Log("Music OnDownloaded 1: " & item.ItemInfo.FilePath)

                Dim folder As String = Path.GetDirectoryName(e.FilePath)
                Dim mgrMusic As MusicManage = MusicManage.Instance()

                'If Not mgrMusic.CheckMusicFolderExist(folder) OrElse _
                '   mgrMusic.MusicDB.Get_MusicInfo_FilePath_byFolders(folder).Count = 0 Then

                Dim fInfo As FolderInfo = MusicSearch.CreateOtherFolderInfo(folder)
                Dim list As New List(Of String)
                list.Add(e.FilePath)

                '添加导入音乐文件
                fInfo.SongList.AddRange(list)
                fInfo.MonitorState = MonitorState.Monitor_File

                mgrMusic.AddMusicFolder(fInfo)

                '    Common.Log("AddMusicFolder: " & fInfo.MusicFolder & fInfo.SongList.Count)
                'Else
                '    Common.Log("AddMusicFolder Failed: " & mgrMusic.CheckMusicFolderExist(Folder))
                'End If
            End If
        End If

    End Sub

    Private Sub OnActiveID(ByVal sender As Object, ByVal e As EventArgs)
        If Common.CheckiTunes AndAlso iTong.Device.CoreFoundation.InitDllSuccess Then
            AppRegistry.CheckActive()
        End If
    End Sub

    'Overrides
    Protected Sub SetInterface()
        'MyBase.SetInterface()
        Me.tsmiOpen.Text = Me._language.GetString("Main.Menu.Open")                          '"打开同步助手"
        Me.tsmiCheckUpdate.Text = Me._language.GetString("Main.Menu.CheckUpdate") & "(&K)"     '"检查更新(&K)..."
        Me.tsmiSetting.Text = Me._language.GetString("Main.Menu.Setting") & "(&O)"             '"参数设置(&O)"
        Me.tsmiSelectLang.Text = Me._language.GetString("Main.Menu.SelectLang") & "(&L)"       '"选择语言(&L)"
        Me.tsmiCreateError.Text = Me._language.GetString("Main.Menu.CreateError")        '"错误日志(&E)"
        If Folder.AppType = RunType.Tongbu_Abroad Then
            Me.tsmiCreateComputerInfo.Visible = True
            Me.tsmiCreateComputerInfo.Text = Me._language.GetString("Main.Menu.CreateComputerInfo") '电脑信息
        End If

        Me.tsmiExist.Text = Me._language.GetString("Setting.RadioButton.ExitForm") & "(&X)"    '"退出(&X)"
    End Sub

    Protected Overrides Sub OnFormClosing(e As FormClosingEventArgs)
        Try
            If ProcForm.mTdCreateError IsNot Nothing AndAlso ProcForm.mTdCreateError.ThreadState <> ThreadState.Stopped Then
                ProcForm.mTdCreateError.Abort()
            End If
        Catch
        End Try

        MyBase.OnFormClosing(e)
    End Sub

#End Region

#Region "--- 一键安装 ---"

    Protected Overrides Sub WndProc(ByRef m As System.Windows.Forms.Message)
        Const WM_COPYDATA As Integer = &H4A

        If m.Msg = WM_COPYDATA AndAlso CInt(m.WParam) = &HF120 Then
            Dim mystr As COPYDATASTRUCT = New COPYDATASTRUCT
            Dim mytype As Type = mystr.GetType
            mystr = CType(m.GetLParam(mytype), COPYDATASTRUCT)

            Dim strArgs As String = mystr.lpData
            If strArgs = "ReActive" Then
                If Me.ProcFormClosing Then
                    Return
                End If
                Me.m_ReActive = True
                Me.ReactiveForm()
                Me.m_ReActive = False
            Else
                Me.ProcessAutoInstallArgs(strArgs)
                'Common.Log("最小化截获的地址：" & strArgs)
            End If

        ElseIf m.Msg = WM_COPYDATA AndAlso CInt(m.WParam) = &HF121 Then
            Select Case m.Msg
                Case WM_COPYDATA
                    Dim mystr As New COPYDATASTRUCT()
                    Dim mytype As Type = mystr.[GetType]()
                    mystr = DirectCast(m.GetLParam(mytype), COPYDATASTRUCT)
                    Common.Log("WndProc RestoreProgress:" & mystr.lpData)
                    Dim strCommand As String = mystr.lpData.Replace("FlashProgress", "")
                    Me.RestoreProgress(strCommand)
                    Exit Select
                Case Else
                    MyBase.DefWndProc(m)
                    Exit Select
            End Select

        ElseIf m.Msg = WM_COPYDATA AndAlso CInt(m.WParam) = &HF122 Then
            Select Case m.Msg
                Case WM_COPYDATA
                    Dim mystr As New COPYDATASTRUCT()
                    Dim mytype As Type = mystr.[GetType]()
                    mystr = DirectCast(m.GetLParam(mytype), COPYDATASTRUCT)

                    'Common.Log(String.Format("WndProc WCRDataProgress:{0} ", mystr.lpData))
                    Dim strValue As String = Base64Decode(Encoding.UTF8, mystr.lpData)
                    ' Common.Log(String.Format("WndProc WCRDataProgressBase64Decode:{0} ", strValue))
                    Dim strCommand As String = strValue.Replace("WCRProgress", "")
                    Me.WCRDataProgress(strCommand)
                    Exit Select
                Case Else
                    MyBase.DefWndProc(m)
                    Exit Select
            End Select

        End If

        MyBase.WndProc(m)
    End Sub

    Private Function Base64Decode(ByVal enclde As Encoding, ByVal strValue As String)
        Dim decode As String = ""
        Try
            Dim bytes As Byte() = Convert.FromBase64String(strValue)
            decode = enclde.GetString(bytes)
        Catch ex As Exception
            decode = strValue
        End Try
        Return decode
    End Function

    Private Sub ReactiveForm()
        Try
            For Each frm As Form In Application.OpenForms
                If TypeOf frm Is MainForm Then
                    '当窗体最小化时，先SHOW再设置WindowState，这样会窗体显示出来
                    frm.Show()
                    If frm.WindowState = FormWindowState.Minimized Then
                        frm.WindowState = FormWindowState.Normal
                    End If
                    frm.Activate()
                    Exit For
                End If

#If IS_ITONG Then
                If Folder.AppType = RunType.DuoBao AndAlso TypeOf frm Is frmDuobao Then
                    frm.Show()
                    If frm.WindowState = FormWindowState.Minimized Then
                        frm.WindowState = FormWindowState.Normal
                    End If
                    frm.Activate()
                    Exit For
                End If
#End If

            Next

        Catch ex As Exception
        End Try
    End Sub

    Private Sub SetMainFormUpdateMessage()
        Try
            For Each frm As Form In Application.OpenForms
                If TypeOf frm Is MainForm Then
                    CType(frm, MainForm).SetIsNewVersion()
                    Exit For
                End If
            Next
        Catch ex As Exception
        End Try
    End Sub

    '1. soft
    '直接获取url后面的数据下载

    '2. navigate
    '如果url是115等可以解析的地址，则进行解析下载。否则在默认的webbrower里面弹出新的页面进行浏览。

    '3. other
    '客户端需要经过对url的值解密。 解密出来的格式会进入1或者2的流程。

    '4. forward
    '外站的一键安装
    Public Sub ProcessAutoInstallArgs(ByVal strArgs As String, Optional ByVal isFormAtStartup As Boolean = False)
        Try
            Dim strSeparater As String = "@@"
            Dim strType As String = Utility.GetParamValueFromQuery("type", strArgs, strSeparater).ToLower.Trim
            Dim strUrl As String = Utility.GetParamValueFromQuery("url", strArgs, strSeparater)

            'MessageBox.Show(strArgs)
            If strArgs = "tbNoNewVersion:" Then
                Me.SetMainFormUpdateMessage()

            ElseIf strType = "soft" Then
                '直接下载
                'Dim strUrl As String = Utility.GetParamValueFromQuery("url", strArgs, strSeparater)
                If strUrl.Length > 0 AndAlso strUrl.ToLower.Contains(".ipa") = True Then
                    Me.DownloadSoftInAutoInstall(strUrl, "")
                Else
                    Me.ShowVersion2Low()
                End If

            ElseIf strType = "navigate" Then
                Me.AutoInstallForNavigate(strArgs, strSeparater)

            ElseIf strType = "other" Then
                '内部的加密处理
                Me.AutoInstallForOther(strArgs, strSeparater)

            ElseIf strType = "forward" Then
                '外站一键安装
                Me.AutoInstallForForward(strArgs, strSeparater)

            ElseIf strType = "forwardios" Then
                '外站ios下载接口走助手下载中心
                strUrl = System.Web.HttpUtility.UrlDecode(strUrl)
                WebSiteHelper.Instance().DownloadApp(Me.GetMainForm, Me.GetCurrentDevice(DeviceType.iOS), strUrl, DownloadCenterSource.None)

            ElseIf strType = "forwardandroid" Then
                '外站一键安装android
                Me.AutoInstallForForwardAndroid(strArgs, strSeparater)

            ElseIf strType = "forwardring" Then
                '铃声壁纸一键下载。
                WebSiteHelper.Instance().AutoDownloadRing(strUrl)

            ElseIf strType = "forwardwallpaper" Then
                '铃声壁纸一键下载。
                WebSiteHelper.Instance().AutoDownloadWallpaper(strUrl)

            ElseIf strArgs.StartsWith("PopMsg", StringComparison.InvariantCultureIgnoreCase) Then
                Me.PopMsgEvent(strArgs, strSeparater)

            Else
                Dim strFilter As String = Utility.GetParamValueFromQuery("filter", strArgs, strSeparater)
                Me.ShowVersion2Low(strFilter, isFormAtStartup)

            End If
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

#Region "助手弹窗广告处理事件"

    Dim thrPopMsgEvent As Thread
    Private Sub PopMsgEvent(ByVal strArgs As String, ByVal strSeparater As String)
        Try
            If Me.thrPopMsgEvent IsNot Nothing AndAlso Me.thrPopMsgEvent.ThreadState <> ThreadState.Stopped Then
                Return
            End If
            Dim obj() As Object = New Object() {strArgs, strSeparater}

            Me.thrPopMsgEvent = New Thread(New ParameterizedThreadStart(AddressOf PopMsgEventThread))
            Me.thrPopMsgEvent.IsBackground = True
            Me.thrPopMsgEvent.Start(obj)

        Catch ex As Exception
            Common.LogException(ex.ToString, "ProcForm_PopMsgEvent")
        End Try
    End Sub

    Private Sub PopMsgEventThread(ByVal objPara As Object)
        Try
            Dim strArgs As String = objPara(0)
            Dim strSeparater As String = objPara(1)
            Dim strArrPara() As String = strArgs.Replace(strSeparater, Chr(1)).Split(Chr(1))
            Dim strActive As String = strArrPara(1)

            '1.当窗体第一次启动的时候等待主窗体启动后再跳转
            Dim intCount As Integer = 0
            While True
                Dim frm As MainForm = Me.GetMainForm()
                If frm IsNot Nothing AndAlso frm.FormLoaded Then
                    Exit While
                End If
                If intCount > 10 Then
                    Exit While
                End If
                intCount += 1
                Utility.WaitSeconds(1)
            End While

            ''2.如果用户要打开微信表情的窗体得等连接手机加载完成后再打开。
            'If strArrPara(1) = "OpenWeixin" Then
            '    Dim blnLoaded As Boolean = False
            '    intCount = 0
            '    While True
            '        For Each frm As Form In Application.OpenForms
            '            If TypeOf frm Is frmSummary Then
            '                Dim frmSumm As frmSummary = frm
            '                If frmSumm IsNot Nothing AndAlso frmSumm.mDevice IsNot Nothing AndAlso frmSumm.mDevice.IsConnected Then
            '                    '如果一运行进来窗体就存在说明是已经打开了。
            '                    If intCount > 0 Then
            '                        'Utility.WaitSeconds(3)
            '                    End If
            '                    blnLoaded = True
            '                    Exit While
            '                End If
            '            End If
            '        Next
            '        'Utility.WaitSeconds(1)
            '        If intCount > 10 Then
            '            Exit While
            '        End If
            '        intCount += 1
            '    End While

            '    '如果没有连接手机就不打开微信表情
            '    If Not blnLoaded Then
            '        Return
            '    End If
            'End If

            Me.PopMsgEventStart(strArrPara)
        Catch ex As Exception
            Common.LogException(ex.ToString, "ProcForm_PopMsgEventThread")
        End Try
    End Sub

    Private Delegate Sub PopMsgEventStartHandler(ByVal objPara As Object)
    Private Sub PopMsgEventStart(ByVal objPara As Object)
        If Me.InvokeRequired Then
            Me.Invoke(New PopMsgEventStartHandler(AddressOf PopMsgEventStart), objPara)
        Else
            Try
                Dim strArrPara() As String = objPara
                If strArrPara.Length >= 2 Then
                    Select Case strArrPara(1)
                        Case "OpenDetail"
                            Me.PopMsgGotoDetail(strArrPara)

                        Case "OpenUrl"
                            Me.PopMsgOpenUrl(strArrPara)

                            'Case "OpenWeixin"
                            '    Me.PopMsgOpenWeixin(strArrPara)

                        Case "OpenAnniversary"
                            Me.PopMsgOpenAnniversary(strArrPara)

                        Case "Down"
                            Me.PopMsgDown(strArrPara)

                        Case "OpenSpecial"      '打开专题
                            Me.PopMsgOpenSpecial(strArrPara)

                    End Select
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "ProcForm_PopMsgEventStart")
            End Try
        End If
    End Sub

    Public Sub PopMsgGotoDetail(ByVal strArrPara() As String)
        If strArrPara.Length >= 4 Then
            Dim strNode As String = "AppShare"
            Dim strUrl As String = strArrPara(3)
            If strArrPara(2).ToLower = "android" Then
                strNode = "AppAndroid"
                'strUrl = String.Format(WebUrl.AndroidDetail, strArrPara(3))
            End If
            Dim frm As MainForm = Me.GetMainForm()
            Utility.WaitSeconds(2)
            frm.GotoSite(ActionFuncType.App, strNode, "", strUrl)
        End If
    End Sub

    '    Private Sub PopMsgOpenWeixin(ByVal strArrPara() As String)
    '#If IS_ITONG Then
    '        Try
    '            For Each frm As Form In Application.OpenForms
    '                If TypeOf frm Is frmSummary Then
    '                    Dim frmSumm As frmSummary = frm
    '                    If frmSumm IsNot Nothing AndAlso frmSumm.mDevice IsNot Nothing AndAlso frmSumm.mDevice.IsConnected Then

    '                        frmSumm.RunApp(GetType(frmWeixinEmoticon))
    '                    End If
    '                End If
    '            Next

    '            '如果窗体打开就选中资源节点
    '            For Each frm As Form In Application.OpenForms
    '                If TypeOf frm Is frmWeixinEmoticon Then
    '                    Dim frmWeixinEmo As frmWeixinEmoticon = frm
    '                    frmWeixinEmo.SelectWebNode()

    '                End If
    '            Next
    '        Catch ex As Exception
    '            Common.LogException(ex.ToString, "ProcForm_PopMsgOpenWeixin")
    '        End Try
    '#End If
    '    End Sub

    Private Sub PopMsgOpenAnniversary(ByVal strArrPara() As String)
        If strArrPara.Length >= 3 Then
            Dim strUrl As String = strArrPara(2)
            'Dim frm As MainForm = Me.GetMainForm()
            'frm.GotoSite(ActionFuncType.App, "AppAdvertising", "", strUrl)
            Try
                Dim frm As MainForm = Me.GetMainForm()
                If frm IsNot Nothing Then
                    frm.ShowSiteAnniversary(strUrl)
                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "PopMsgOpenAnniversary")
            End Try
        End If
    End Sub

    Private Sub PopMsgOpenUrl(ByVal strArrPara() As String)
        If strArrPara.Length >= 3 Then
            Dim strUrl As String = strArrPara(2)
            'Dim frm As MainForm = Me.GetMainForm()
            'frm.GotoSite(ActionFuncType.App, "AppAdvertising", "", strUrl)
            Common.OpenExplorer(strUrl)
        End If
    End Sub

    Private Sub PopMsgDown(ByVal strArrPara() As String)
        Try
            '从弹窗下载软件
            Dim frmMain As MainForm = Me.GetMainForm()
            If frmMain IsNot Nothing Then
                Dim device As IDevice = Me.GetCurrentDevice(DeviceType.iOS)
                '直接下载
                Dim e As New WebBrowserNavigatingEventArgs(New Uri(strArrPara(2)), "")
                WebSiteHelper.Instance().WebBrowserNavigating(frmMain, device, Nothing, e, "popup")
                frmMain.GotoSite(ActionFuncType.App, "", "", "")
                frmMain.ShowDownloadCenter()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ProcForm_PopMsgDown")
        End Try
    End Sub

    Public Function GetCurrentDevice(ByVal type As DeviceType) As IDevice
        Dim dev As IDevice = Nothing

        Try

            Dim frmMain As MainForm = Me.GetMainForm()

            For Each Item As UsbDevice In frmMain.UsbDevices.Values
                If Item IsNot Nothing AndAlso Item.DeviceType = type AndAlso Item.MobileDevice IsNot Nothing AndAlso Item.MobileDevice.IsConnected Then
                    dev = Item.MobileDevice
                    Exit For
                End If
            Next
        Catch ex As Exception
            Debug.Write(ex)
        End Try

        Return dev
    End Function

    Private Sub PopMsgOpenSpecial(ByVal strArrPara() As String)
        If strArrPara.Length >= 3 Then
            Dim strUrl As String = strArrPara(2)
            Dim frm As MainForm = Me.GetMainForm()
            frm.GotoSite(ActionFuncType.App, "AppShare@3", "", strUrl)
            'Common.OpenExplorer(strUrl)
        End If
    End Sub

#End Region

    '''''''''''''''''''''''''''''''''''''''''''
    '' Added by Utmost20140903
    '' 由弹窗程序进入助手的命令行参数
    Public Sub ExecuteSiteByUsb()
        Dim loaded As Boolean = False
        Try
            If m_objCmdLineArgs IsNot Nothing AndAlso TypeOf m_objCmdLineArgs Is System.Array Then
                Dim aryLines() As String = CType(m_objCmdLineArgs, System.Array)

                If aryLines.Length = 4 Then
                    If aryLines(0) = "PopupSite" Then
                        For Each frm As Form In Application.OpenForms
                            If TypeOf frm Is MainForm Then
                                CType(frm, MainForm).GotoSite(ActionFuncType.App, aryLines(1), aryLines(2), aryLines(3))
                                Exit For
                            End If
                        Next

                        For Each frm As Form In Application.OpenForms
                            If TypeOf frm Is frmSite AndAlso frm.Name = "web" Then
                                CType(frm, frmSite).SelectSite(aryLines(1), aryLines(3))
                                loaded = True
                                Exit For
                            End If
                        Next
                        'Else
                        '    For Each frm As Form In Application.OpenForms
                        '        If TypeOf frm Is frmSummary Then
                        '            CType(frm, frmSummary).ExecFrm(aryLines(3))
                        '            loaded = True
                        '            Exit For
                        '        End If
                        '    Next
                    End If
                End If
                If loaded Then
                    m_objCmdLineArgs = Nothing
                End If
            End If
        Catch ex As Exception
            Debug.Write(ex)
            Common.LogException(ex.ToString, "frmProc_ExecuteSiteByUsb")
        End Try

    End Sub

    Private Sub AutoInstallForNavigate(ByVal strArgs As String, ByVal strSeparater As String)
        '判断是否115链接，如果可以直接下载就直接下载，不行的话就在webbrower中显示
        Dim strUrl As String = Utility.GetParamValueFromQuery("url", strArgs, strSeparater)

        '"ms.tongbu.com/discuzapi"
        If strUrl.ToLower.Contains(WebUrl.ServerOneKeyAPI) Then   '"ms.tongbu.com/discuzapi"
            '判断是否bbs.tongbu.com的链接
            Dim strName As String = Utility.GetParamValueFromQuery("title", strUrl)
            If strName.ToLower.Contains(".ipa") Then
                Me.DownloadSoftInAutoInstall(strUrl, strName)
                Return
            Else
                Me.ShowVersion2Low()
                Return
            End If
        End If

        '弹到webbrowser
        Me.NavigateInTonbutui(strUrl)
    End Sub

    Private Sub AutoInstallForOther(ByVal strArgs As String, ByVal strSeparater As String)
        '如果是 other类型的 URL是有加密的。得先解密后得到（type=xxx &url=xxx）再分析 type 跟 url
        'Web 在处理　"+"　的时候替换为　"%2b" 所以得还原回去再解密
        Dim isDecode As Boolean = IIf(strArgs.IndexOf("+") >= 0, False, True)
        Dim strUrl As String = Utility.GetParamValueFromQuery("url", strArgs, strSeparater, isDecode).Replace("%2b", "+")
        Dim strOtherString As String = Common.DecryptDES(strUrl, Utility.KeyArray(0), Utility.KeyArray(0))
        Dim strOtherType As String = Utility.GetParamValueFromQuery("type", strOtherString, strSeparater).ToLower.Trim
        Dim strOtherUrl As String = Utility.GetParamValueFromQuery("url", strOtherString, strSeparater).ToLower.Trim

        If strOtherType = "soft" Then
            '如果 type = "soft" 下载;
            Me.DownloadSoftInAutoInstall(strOtherUrl, "")
        ElseIf strOtherType = "navigate" Then
            '弹到webbrowser
            Me.NavigateInTonbutui(strOtherUrl)
        Else
            Me.ShowVersion2Low()
        End If
    End Sub

    Private Sub AutoInstallForForward(ByVal strArgs As String, ByVal strSeparater As String)
        '如果是 forward类型的先解密。得先解密，再取得url，
        'Web 在处理　"+"　的时候替换为　"%2b" 所以得还原回去再解密
        Dim strRgbKey As String = "ac68!3#1"
        Dim isDecode As Boolean = IIf(strArgs.IndexOf("+") >= 0, False, True)
        Dim strEncryptUrl As String = Utility.GetParamValueFromQuery("url", strArgs, strSeparater, isDecode).Replace("%2b", "+")

        '不知道为什么要进行trimend("/"),这样会导致无法解密，所以暂时去掉
        'Dim strEncryptUrl As String = Utility.GetParamValueFromQuery("url", strArgs, strSeparater, isDecode).Replace("%2b", "+").TrimEnd("/")

        If strEncryptUrl.Length = 0 Then
            Common.LogException("解析出来的地址为空:" & strArgs)
            Me.MessageBoxNotExistUrl()
            Return
        End If

        If Me.bgwAnalyseAppSite.IsBusy Then
            Return
        End If

        Dim strDecryptUrl As String = String.Empty
        strDecryptUrl = Common.DecryptDES(strEncryptUrl, strRgbKey, strRgbKey)

        'strDecryptUrl结尾处多“/”,web页面是没有“/”,但不知为什么PC端接收到的数据有“/”
        '举例：http://app.tongbu.com/305343404_qingboke_tumblr.html
        'Tumblr 3.3.2 破解版
        If String.Compare(strDecryptUrl, strEncryptUrl, True) = 0 AndAlso strEncryptUrl.EndsWith("/") Then
            strEncryptUrl = strEncryptUrl.TrimEnd("/")
            strDecryptUrl = Common.DecryptDES(strEncryptUrl, strRgbKey, strRgbKey)
        End If

        If String.Compare(strDecryptUrl, strEncryptUrl, True) = 0 Then
            Common.LogException("解密失败:" & strArgs)
            Me.MessageBoxNotExistUrl()
            Return
        End If

        'uid 暂时没用到，web为了后面扩展需要。
        Dim strUid As String = String.Format("&uid={0}", Utility.GetParamValueFromQuery("uid", strArgs, strSeparater))

        '1、获取软件的下载地址
        Dim strRequestUrl As String = Utility.GetParamValueFromQuery("url", strDecryptUrl, strSeparater)
        strRequestUrl = strRequestUrl & strUid

        '2、获取软件的名字
        Dim strAppName As String = Utility.GetParamValueFromQuery("appname", strDecryptUrl, strSeparater)
        If strAppName.Length > 0 Then
            strAppName = System.Web.HttpUtility.UrlDecode(strAppName)
        End If

        '3、获取软件的版本
        Dim strAppVersion As String = Utility.GetParamValueFromQuery("ver", strDecryptUrl, strSeparater)

        '4、获取软件的icon
        Dim strAppIcon As String = Utility.GetParamValueFromQuery("icon", strDecryptUrl, strSeparater)

        'Dim kValue As New KeyValuePair(Of String, String)(strRequestUrl, strAppName)

        Dim dicValues As New Dictionary(Of String, String)
        dicValues.Add("requesturl", strRequestUrl)
        dicValues.Add("appname", strAppName)
        dicValues.Add("ver", strAppVersion)
        dicValues.Add("icon", strAppIcon)

        RemoveHandler bgwAnalyseAppSite.DoWork, AddressOf bgwCheckAnalyseForward_DoWork
        RemoveHandler bgwAnalyseAppSite.RunWorkerCompleted, AddressOf bgwCheckAnalyseForward_RunWorkerCompleted
        AddHandler bgwAnalyseAppSite.DoWork, AddressOf bgwCheckAnalyseForward_DoWork
        AddHandler bgwAnalyseAppSite.RunWorkerCompleted, AddressOf bgwCheckAnalyseForward_RunWorkerCompleted

        Me.bgwAnalyseAppSite.WorkerSupportsCancellation = True
        Me.bgwAnalyseAppSite.RunWorkerAsync(dicValues)
    End Sub

    Private Sub AutoInstallForForwardAndroid(ByVal strArgs As String, ByVal strSeparater As String)
        Try
            Dim isDecode As Boolean = IIf(strArgs.IndexOf("+") >= 0, False, True)
            Dim strUrl As String = Utility.GetParamValueFromQuery("url", strArgs, strSeparater, isDecode).Replace("%2b", "+")
            Dim dev As IDevice = Me.GetCurrentDevice(DeviceType.Android)
            WebSiteHelper.Instance().DownloadAPKEx(Me.GetMainForm(), dev, strUrl)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ProcForm_AutoInstallForForwardAndroid")
        End Try
    End Sub

    Private Sub MessageBoxNotExistUrl()
        '此链接已失效，
        '请选择其它下载地址。
        '"此链接已失效，请选择其它下载地址。"
        tbMessageBox.Show(Me._language.GetString("Site.Message.LinkDisable"), Me._language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)

    End Sub

    Private Sub bgwCheckAnalyseForward_DoWork(ByVal sender As Object, ByVal e As System.ComponentModel.DoWorkEventArgs)
        Try
            'Dim objArgument As KeyValuePair(Of String, String) = CType(e.Argument, KeyValuePair(Of String, String))   'key是请求服务器的地址，Value是软件名称
            Dim objArgument As Dictionary(Of String, String) = CType(e.Argument, Dictionary(Of String, String))

            Dim strDownloadUrl As String = Utility.GetContentStringFromUrl(objArgument("requesturl"), System.Text.Encoding.UTF8, 20000).Trim '请求服务器

            Dim strSeparater As String = "@@"
            Dim strUrl As String = Utility.GetParamValueFromQuery("url", strDownloadUrl, strSeparater)

            If strUrl.Length = 0 Then
                '在1.35版本的时候，web要求每个下载链接都进行解密
                strDownloadUrl = Common.DecryptDES(strDownloadUrl, "ac68!3#1", "ac68!3#1")
                strUrl = Utility.GetParamValueFromQuery("url", strDownloadUrl, strSeparater)
            End If

            objArgument.Add("url", strUrl)

            e.Result = objArgument

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try
    End Sub

    Private Sub bgwCheckAnalyseForward_RunWorkerCompleted(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs)
        'Dim objResult As KeyValuePair(Of String, String) = CType(e.Result, KeyValuePair(Of String, String))   'key是下载地址，Value是软件名称
        Dim objResult As Dictionary(Of String, String) = CType(e.Result, Dictionary(Of String, String))
        Dim strDownloadUrl As String = objResult("url")
        Dim strAppName As String = objResult("appname")
        Dim strAppVersion As String = objResult("ver")
        Dim strAppIcon As String = objResult("icon")

        If strDownloadUrl.Length = 0 Then
            Common.LogException("返回的下载地址为空:" & strDownloadUrl)
            Me.MessageBoxNotExistUrl()
            Return
        End If

        If strDownloadUrl.Contains(".ipa") Then
            '直接下载
            Me.DownloadSoftInAutoInstall(strDownloadUrl, strAppName, strAppVersion, strAppIcon)
            Return
        End If

        '弹到webbrowser
        Me.NavigateInTonbutui(strDownloadUrl)
    End Sub

    Private Sub DownloadSoftInAutoInstall(ByVal url As String, _
                                                                ByVal strName As String, _
                                                                Optional ByVal strAppVersion As String = "", _
                                                                Optional ByVal strAppIcon As String = "")
        Try
            Dim info As New MultiThreadDownloadItemInfo
            With info
                .IsSingleThread = False

                .SaveFolder = IniSetting.GetDownloadSoftFolder()
                .Type = ResourceType.IPA
                .Class = ResourceClass.Software
                .Source = TaskSource.Tongbu
                .Url = url
                .IconUrl = strAppIcon
                If strAppIcon.Length > 0 Then
                    'Guid.NewGuid().ToString("N").Substring(0, 10)
                    .ItemId = Guid.NewGuid().ToString("N").Substring(0, 10)
                End If

                If strName.Length > 0 Then
                    If strAppVersion.Length > 0 Then
                        strName &= String.Format("_v{0}", strAppVersion)
                    End If

                    If strName.ToLower().EndsWith(".ipa") = False Then
                        strName &= ".ipa"
                    End If
                End If

                If strName.Length = 0 Then
                    strName = System.IO.Path.GetFileName(New Uri(info.Url).AbsolutePath)
                End If
                info.Name = System.Web.HttpUtility.UrlDecode(strName)

                If .Url.ToLower().Contains("tbtui") Then
                    .IsUpdate = True
                End If

                Dim deviceMgr As MobileDeviceManager = MobileDeviceManager.Instance()
                If deviceMgr.ConnectedDevices.Count > 0 Then
                    .Identifier = deviceMgr.ConnectedDevices(0).Identifier
                End If
            End With


            MultiThreadDownload.Instance().NewTask(info)

        Catch ex As Exception
            Common.LogException(ex.ToString, "DownloadSoftInAutoInstall")
        End Try
    End Sub

    Private Sub NavigateInTonbutui(ByVal strUrl As String)
        Common.OpenExplorer(strUrl)
    End Sub

    Private Sub ShowVersion2Low()
        ''同步助手无法识别该文件。请检查文件类型是否支持，或者下载最新版的同步助手再次尝试。
        'tbMessageBox.Show(Me._language.GetString("Main.Form.Message.OneKeyInstallHaveNewVersion"), _
        '                Me._language.GetString("Common.Info"), _
        '                MessageBoxButtons.OK, _
        '                MessageBoxIcon.Information)
        Common.OpenExplorer("www.tongbu.com")
    End Sub

    Private Sub ShowVersion2Low(ByVal strFilter As String, ByVal isFormAtStartup As Boolean)
        If isFormAtStartup AndAlso String.Compare(strFilter, "true", True) = 0 Then
            Me._AllowCloseForm = True
            Me.Close()

            Return
        End If
        Me.ShowVersion2Low()
    End Sub

    Private Sub CheckAutoInstallExistInRegester()
#If IS_ITONG Then
        Try
            AutoInstallHelper.RegesterAutoInstall()
        Catch ex As Exception
            Common.LogException(ex.ToString, "CheckAutoInstallExistInRegester")
        End Try
#End If
    End Sub

#End Region

#Region "--- 菜单事件 ---"

    Private Sub tsmiOpen_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiOpen.Click
        If ProcForm.Instance().ProcFormClosing = True Then
            Return
        End If

        For Each frm As Form In Application.OpenForms
            'Debug.Print(frm.Name & vbTab & frm.Text)
            If TypeOf frm Is MainForm Then
                '当窗体最小化时，先SHOW再设置WindowState，这样会窗体显示出来
                frm.Show()
                If frm.WindowState = FormWindowState.Minimized Then
                    frm.WindowState = FormWindowState.Normal
                End If
                frm.Activate()

                Exit For
            End If
        Next

        '显示最小化时隐藏的窗体
        For Each frm As Form In Me._LstHideChildForms
            Try
                frm.Show()
                If frm.WindowState = FormWindowState.Minimized Then
                    frm.WindowState = FormWindowState.Normal
                End If
                frm.Activate()
            Catch
            End Try
        Next

        Me._LstHideChildForms.Clear()
    End Sub

    Private Sub tsmiSetting_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiSetting.Click
        If Common.VerIs30 Then
            If Common.CheckFormExist(GetType(frmSettingV3)) Is Nothing Then
                Dim frmSetting As frmSettingV3 = New frmSettingV3(FunctionKey.None)
                frmSetting.StartPosition = FormStartPosition.CenterScreen
                frmSetting.ShowDialog()
            End If
        Else
#If IS_ITONG Then
            If Common.CheckFormExist(GetType(frmSetting)) Is Nothing Then
                Dim frmSetting As frmSetting = New frmSetting(FunctionKey.None)
                frmSetting.StartPosition = FormStartPosition.CenterScreen
                frmSetting.ShowDialog()
            End If
#End If
        End If

    End Sub

    Private Sub tsmiCheckUpdate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiCheckUpdate.Click
        Me.StartToCheckUpdate(True, True)
    End Sub

    Private Sub tsmiExist_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiExist.Click
        Me._AllowCloseForm = True

        Me.ExitApp()
    End Sub

    Private Sub mNotefyIcon_DoubleClick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles mNotifyIcon.DoubleClick
        Me.tsmiOpen_Click(Me.tsmiOpen, New EventArgs())
    End Sub

    Private Sub tsmiCreateError_Click(sender As Object, e As EventArgs) Handles tsmiCreateError.Click
        CreateError()
    End Sub

#End Region

#Region "--- 切换语言 ---"

    '加载语言包信息
    Public Sub InitLang(ByVal itemLang As ToolStripMenuItem)
        Try
            itemLang.DropDownItems.Clear()
            For Each info As LangInfo In Me._language.Languages
                If Not itemLang.DropDownItems.ContainsKey(info.LangName) Then
                    Dim item As New ToolStripMenuItem(info.LangDisplayName, Nothing, New EventHandler(AddressOf LangMenuItem_Click), info.LangName)
                    itemLang.DropDownItems.Add(item)
                End If
            Next
            CType(itemLang.DropDownItems.Item(Me._language.CurrentLanguage.LangName), ToolStripMenuItem).Checked = True
        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

    Private Sub LangMenuItem_Click(ByVal sender As Object, ByVal e As EventArgs)
        Dim SelectItem As ToolStripMenuItem = sender

        For Each info As LangInfo In Me._language.Languages
            If info.LangName = SelectItem.Name Then
                '保存设置语言
                IniSetting.SetLanguage(info.LangName)
                '"设置语言生效需要重启程序，是否立即执行重启？"
                '"提示"
                Dim result As DialogResult = tbMessageBox.Show(Me._language.GetString("Main.Message.RestarForSetLang"), _
                                                             Me._language.GetString("Common.Info"), _
                                                             MessageBoxButtons.YesNo, _
                                                             MessageBoxIcon.Question, _
                                                             MessageBoxDefaultButton.Button2)

                If result = Windows.Forms.DialogResult.Yes Then
                    '关闭程序
                    Me._AllowCloseForm = True
                    Me.ExitApp()

                    '重启助手
                    HelperExeManager.RestartApp()
                End If
            End If
        Next

    End Sub

#End Region

#Region "--- 检测升级 ---"

    Private _FolderUpgrade As String = ""
    Private _strUpdatePackageFile As String = ""
    Private _CheckUpdateForm As LiveUpdateForm = Nothing
    Private _SoftStartTime As DateTime = DateTime.MinValue
    Private _strVersion As String = ""
    Private _lngPassTime As Double = 0
    Private _UserIDInSoftInfo As String = -1    '当前收集的软件信息在文件中保存的id
    Private _CheckEnviromentThread As Thread = Nothing

    Private Sub CheckUpgradFile()
        Me._strUpdatePackageFile = Path.Combine(Folder.AppFolder, "Upgrade.dat")
        Me._FolderUpgrade = Path.Combine(Path.GetTempPath(), "tbUpgrade")
    End Sub

    Private Sub CheckUpgradeFileExist()
        Dim strPathUpgradeSuceed As String = Path.Combine(Folder.AppFolder, "UpgradeSuceed.dll")

        Me.CheckUpgradFile()

        Dim blnNewIncrease As Boolean = ServerIniSetting.GetIsZSNewIncrease()
        If File.Exists(Me._strUpdatePackageFile) Then

            '助手升级增量更新时，不需要删除tbUpgrade目录
            If blnNewIncrease = False Then
                Try
                    '删除临时目录中的tbUpgrade目录,以免以前的升级程序失败后会遗留文件
                    If Directory.Exists(Me._FolderUpgrade) Then
                        Directory.Delete(Me._FolderUpgrade, True)
                    End If
                Catch
                End Try
            End If


            If File.Exists(strPathUpgradeSuceed) Then

                Me._FirstRunFromUpgrade = True

                '升级成功后清理升级产生的临时文件
                Try
                    File.Delete(strPathUpgradeSuceed)
                Catch
                End Try

                Try
                    File.Delete(_strUpdatePackageFile)
                Catch
                End Try

            Else
                If Not Folder.LangType = LanguageType.en_US Then

                    '助手升级增量更新时，每个下载的文件都会先比较md5，所以这边不需要再比较
                    If blnNewIncrease = False Then
                        '如果包不完整则删除
                        Dim strServerFileLength As String = IniSetting.GetUpdateFileLength()
                        If Not Me.CheckUpdateFileIsFull(Me._strUpdatePackageFile, strServerFileLength) Then
                            Try
                                File.Delete(_strUpdatePackageFile)
                            Catch
                            End Try

                            Return
                        End If
                    ElseIf Not Directory.Exists(Me._FolderUpgrade) Then
                        Return
                    End If

                    '升级文件已经下载完毕，但是用户没有立刻升级，所以启动的时候需要程序自动升级
                    LiveUpdateHelper.StartLiveUpdateExe(Me._strUpdatePackageFile, Me._FolderUpgrade, Application.ExecutablePath, "")
                End If
            End If
        End If
    End Sub

    Public Sub StartToCheckUpdate(Optional ByVal blnUserCheckUpgrade As Boolean = False, Optional ByVal blnClick As Boolean = False)
        If Folder.AppFolder.Contains("\bin\") Then
            Return
        End If

        '新升级逻辑（国内版）
        If Folder.LangType <> LanguageType.vi_VN Then
            If blnClick OrElse _lngPassTime Mod 3600 = 0 Then
                Try
                    For Each Item As Form In Application.OpenForms
                        If Item IsNot Nothing AndAlso TypeOf Item Is MainForm Then
                            If MainForm.CheckUpdate(Item, Me._language) Then
                                Return
                            End If
                        End If
#If IS_ITONG Then
                        '进宝升级验证与iTunes是不是匹配
                        If Folder.AppType = RunType.DuoBao AndAlso Item IsNot Nothing AndAlso TypeOf Item Is frmDuobao Then
                            If MainForm.CheckUpdate(Item, Me._language) Then
                                Return
                            End If
                        End If
#End If

                    Next
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "frmProc_CheckUpdate")
                End Try
            End If

            Dim strAppName As String = Me._language.GetString("Common.Tongbu")
            If Folder.AppType = RunType.DuoBao Then
                strAppName = "进宝安装器"
            End If

            HelperExeManager.CheckUpdate(blnUserCheckUpgrade, strAppName, Application.ExecutablePath)

            Return
        End If

        ''检查升级程序是否存在，如果存在则不提示更新
        'If Me.bgwCheckUpdate.IsBusy OrElse _
        '   Me._CheckUpdateForm IsNot Nothing AndAlso Me._CheckUpdateForm.IsDisposed = False Then

        '    Return
        'End If

        'Me.bgwCheckUpdate.RunWorkerAsync(blnUserCheckUpgrade)
    End Sub

    Private Sub bgwCheckUpdate_DoWork(ByVal sender As Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwCheckUpdate.DoWork
        Try
            '生成路径信息
            Me.CheckUpgradFile()

            '检察当前是不是用户点击 检查升级
            'Dim blnUserCheckUpgrade As Boolean = True
            Dim blnUserCheckUpgrade As Boolean = (e.Argument IsNot Nothing AndAlso e.Argument)

            '没有网络则不检查更新
            If My.Computer.Network.IsAvailable = False Then
                '"网络不通，无法完成升级，请检查您的网络"
                Me.SetBalloonTipTextUserCheckUpgrade(blnUserCheckUpgrade, Me._language.GetString("Main.Message.NetworkDisconnectCanNotUpgrade"))
                Return
            End If

            '"正在检测升级 ..."
            Me.SetBalloonTipTextUserCheckUpgrade(blnUserCheckUpgrade, Me._language.GetString("Main.Message.CheckingUpgrade"))
            Try
                '_strUpdatePackageFile = Path.Combine(Directory.GetCurrentDirectory(), "Upgrade.dat")

                '删除文件中的 Upgrade.dat,去掉此逻辑，判断已经下载的文件是否是最新的
                'If File.Exists(Me._strUpdatePackageFile) Then
                '    File.Delete(Me._strUpdatePackageFile)
                'End If

                '删除临时目录中的tbUpgrade目录,以免以前的升级程序失败后会遗留文件
                'Me._FolderUpgrade = Path.Combine(Path.GetTempPath(), "tbUpgrade")
                If Directory.Exists(Me._FolderUpgrade) Then
                    Directory.Delete(Me._FolderUpgrade, True)
                End If
            Catch
            End Try

            '1. 检查服务器更新
            Dim strUrl As String = Me.GetWebUpdateUrl(My.Application.Info.Version.ToString(), _strVersion)

            If Not String.IsNullOrEmpty(strUrl) Then
                Me.tmrUpgrade.Start()

                Dim strTempFile As String = _strUpdatePackageFile & ".tmp"

                '获取服务器包的大小
                Dim lngServerFileLength As Long = Utility.GetFileSizeByUrl(strUrl)
                IniSetting.SetUpdateFileLength(lngServerFileLength.ToString())

                '如果Upgrade.dat已经存在，则判断与服务器的版本是否一致，如果不一致才重新下载
                If Me.CheckUpdateFileIsFull(Me._strUpdatePackageFile, lngServerFileLength.ToString()) Then
                    Return
                End If

                'If File.Exists(Me._strUpdatePackageFile) Then
                '    Try
                '        Dim lngFileLength As Long = New FileInfo(Me._strUpdatePackageFile).Length
                '        If lngServerFileLength = lngFileLength Then
                '            Return
                '        End If
                '    Catch ex As Exception
                '        Debug.Print(ex.ToString)
                '    End Try
                'End If

                '"正在下载最新版本，请稍候 ..."
                Me.SetBalloonTipTextUserCheckUpgrade(blnUserCheckUpgrade, Me._language.GetString("Main.Message.DownloadingNewVersion"))

                Try
                    '检查下载的升级包是否下载一半
                    If File.Exists(strTempFile) = True Then
                        File.Delete(strTempFile)
                    End If
                Catch ex As Exception
                    Debug.Print("已经检查到升级的App.dat存在，并且删除不掉或者去掉.tmp失败:" & ex.ToString)
                End Try

                '2. 下载升级包
                If Utility.DownLoadFile(strUrl, strTempFile) Then
                    Try
                        '检查下载的升级包是否存在，如果存在的话则尝试删除，
                        '如果删除不掉的话, 则代表可能已经在升级了，则当前的升级程序不继续
                        If File.Exists(_strUpdatePackageFile) Then
                            File.Delete(_strUpdatePackageFile)
                        End If

                        '下载成功后去掉文件的.tmp
                        File.Move(strTempFile, _strUpdatePackageFile)
                    Catch ex As Exception
                        '"下载失败,请重试"
                        Me.SetBalloonTipTextUserCheckUpgrade(blnUserCheckUpgrade, Me._language.GetString("Main.Message.DownloadFailureAndRetry"))
                        Debug.Print("已经检查到升级的App.dat存在，并且删除不掉或者去掉.tmp失败:" & ex.ToString)

                        '退出当前升级程序
                        Exit Sub
                    End Try
                End If
            Else
                '"已经是最新版本了"
                Me.SetBalloonTipTextUserCheckUpgrade(blnUserCheckUpgrade, Me._language.GetString("Main.Message.AlreadyNewVersion"))

                If File.Exists(Me._strUpdatePackageFile) Then
                    File.Delete(Me._strUpdatePackageFile)
                End If
            End If

        Catch ex As Exception
            Debug.Print("升级过程中出错：" & ex.ToString)
        End Try

    End Sub

    Private Sub bgwCheckUpdate_RunWorkerCompleted(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles bgwCheckUpdate.RunWorkerCompleted
        If File.Exists(_strUpdatePackageFile) Then

            '恢复任务栏原始图标
            Me.tmrUpgrade.Stop()
            Me.mNotifyIcon.Icon = My.Resources.iTong


            '解压升级历史的rtf文件
            Dim strUpdateHistory As String = Path.Combine(Me._FolderUpgrade, "readme.rtf")
            Utility.unzip(_strUpdatePackageFile, Me._FolderUpgrade, New String() {"readme.rtf"})

            '是否存在App.zip
            '3. 弹出升级界面
            Me._CheckUpdateForm = New LiveUpdateForm(Application.ExecutablePath, _
                                                    _strVersion, _
                                                    strUpdateHistory, _
                                                    _strUpdatePackageFile, _
                                                    Me._FolderUpgrade)

            If Common.CheckFormExist(Me._CheckUpdateForm.GetType) Is Nothing Then
                Me._CheckUpdateForm.BringToFront()
                Me._CheckUpdateForm.ShowDialog()
            End If
        End If
    End Sub

    Private Function GetWebUpdateUrl(ByVal version As String, ByRef newVersion As String) As String
        Dim strUrl As String = ""

        Dim strVersionType As String = "tbzs20"
        If Common.VerIs30 Then
            strVersionType = "tbzs30"
        ElseIf Folder.LangType = LanguageType.en_US Then
            strVersionType = "tbzs20sea"
        ElseIf Folder.LangType = LanguageType.th_TH AndAlso Folder.AppType = RunType.Tongbu Then
            strVersionType = "tbzs20mall"
        End If

        '升级接口
        Dim strUpdateUrl As String = String.Format(WebUrl.ServerAssitantUpdate, version, strVersionType)

        Dim strContent As String = Utility.GetContentStringFromUrl(strUpdateUrl, System.Text.Encoding.UTF8, 15000)
        If strContent.StartsWith("1=") Then
            newVersion = strContent.Substring(2, 7)
            strUrl = strContent.Substring(10)
        End If

        Return strUrl
    End Function

    Private Sub SetBalloonTipTextUserCheckUpgrade(ByVal blnUserCheckUpgrade As Boolean, ByVal strMessage As String)
        If blnUserCheckUpgrade Then
            Me.ShowTip(strMessage, 3)
        End If
    End Sub

    Private _CheckUpgradeShowIcon As Integer = 1
    Private Sub tmrUpgrade_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrUpgrade.Tick
        If Folder.LangType = LanguageType.en_US Then
            Me.tmrUpgrade.Stop()
            Return
        End If

        'If Me._CheckUpgrade Then
        If Me._CheckUpgradeShowIcon = 1 Then
            Me.mNotifyIcon.Icon = My.Resources.iTong_check1
            Me._CheckUpgradeShowIcon = 2

        ElseIf Me._CheckUpgradeShowIcon = 2 Then
            Me.mNotifyIcon.Icon = My.Resources.iTong_check2
            Me._CheckUpgradeShowIcon = 3

        ElseIf Me._CheckUpgradeShowIcon = 3 Then
            Me.mNotifyIcon.Icon = My.Resources.iTong_check3
            Me._CheckUpgradeShowIcon = 4

        ElseIf Me._CheckUpgradeShowIcon = 4 Then
            Me.mNotifyIcon.Icon = My.Resources.iTong_check4
            Me._CheckUpgradeShowIcon = 1
        End If
        'Else
        'Me.mNotifyIcon.Icon = My.Resources.iTong
        'Me.tmrUpgrade.Stop()
        'End If
    End Sub

    Private Function CheckUpdateFileIsFull(ByVal strUpdateFile As String, ByVal strServerFileLength As String) As Boolean
        Dim blnFull As Boolean = False

        If File.Exists(strUpdateFile) Then
            Try
                Dim lngFileLength As Long = New FileInfo(strUpdateFile).Length
                If String.Compare(lngFileLength.ToString(), strServerFileLength) = 0 Then
                    blnFull = True
                End If
            Catch ex As Exception
                Debug.Print(ex.ToString)
            End Try
        End If

        Return blnFull
    End Function

#End Region

#Region "--- 计时器 ---"

    Private _CanMoveToVirtualMemory As Boolean = False  '能否进行从物理内存转移到虚拟内存的操作
    'Private _NeedMoveToVirtualMemory As Boolean = False '是否需要进行从物理内存转移到虚拟内存的操作

    '进行从物理内存转移到虚拟内存的操作
    Private Sub MoveToVirvalMemory()
        Dim hPtr As IntPtr = iTong.Components.NativeMethods.GetCurrentProcess()
        iTong.Components.NativeMethods.SetProcessWorkingSetSize(hPtr, -1, -1)
    End Sub

    Public Sub SetVirtualMemory()
        IniSetting.SetNeedMoveToVirtualMemory(True)
    End Sub

    Private Sub tmrTimer_Tick(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tmrTimer.Tick
        Try
            '计时器的响应间隔是1秒,如有改动需要变更下面自增量
            _lngPassTime += 1

            'Me.ShowMsg()

            If Me._CanMoveToVirtualMemory AndAlso (IniSetting.GetNeedMoveToVirtualMemory AndAlso _lngPassTime Mod 13 = 0 OrElse _lngPassTime Mod 61 = 0) Then
                '检测是否需要进入物理内在转移到虚拟内存的操作
                Me.MoveToVirvalMemory()
                IniSetting.SetNeedMoveToVirtualMemory(False)

            ElseIf _lngPassTime = 1 Then
                '检查是否有上次没有升级的升级包(_FirstRunFromUpgrade在验证升级成功会进行操作一定在放在一秒之前)
                Me.CheckUpgradeFileExist()

                If Not Me._FirstRunFromUpgrade Then
                    Return
                End If

                Try
                    Dim strFileTag As String = Path.Combine(My.Application.Info.DirectoryPath.TrimEnd("\"), "OpenOK.dll")

                    If Not File.Exists(strFileTag) Then
                        Dim fileStream As FileStream = File.Create(strFileTag)
                        fileStream.Close()
                    End If
                Catch ex As Exception
                    Common.LogException(ex.ToString(), "OpenOKException")
                End Try

            ElseIf _lngPassTime = 5 Then
                ThreadPool.QueueUserWorkItem(AddressOf DelayDoThread)
                ''移到tbMobileService
                'If ServerIniSetting.GetIsUsingPush() AndAlso Not String.IsNullOrEmpty(ServerIniSetting.GetPushURL()) Then
                '    ThreadPool.QueueUserWorkItem(AddressOf StartPushListenning)
                'Else

                'End If

            ElseIf _lngPassTime = 6 Then
                '解密检查
                CheckMD5CryptoServiceProvider()

                '2015-08-17 bychenbihai 添加强装商店
#If IS_ITONG_ZJ Then

            ElseIf _lngPassTime = 7 Then
                '修复错误数据 2015-10-20
                RepairHelper.Repair()

            ElseIf _lngPassTime = 8 Then
                '下载强装软件
                StrongInstallHelper.Instance.Start2CheckDownloadTBTui()

            ElseIf _lngPassTime = 10 Then
                'check苹果应用商店
                If Common.IsTestMode = False Then '测试个人签名
                    AppleStoreHelper.Instance().Start2CheckGameStore()
                End If
#End If

                'ElseIf _lngPassTime = 11 Then
                '    Me.CheckDownloadTui()

            ElseIf _lngPassTime = 16 Then
                '解析App信息
                Me.RunAnalysisApp()

            ElseIf _lngPassTime = 20 Then
                '开始收集数据
                Me.StartToSendData()

            ElseIf _lngPassTime = 23 OrElse _lngPassTime Mod 301 = 0 Then  '每五分钟检测一次
                '提交操作行为数据
                Dim blnIsFirst As Boolean = False
                If _lngPassTime = 23 Then
                    blnIsFirst = True
                End If

                Me.StartToSendOperateData(blnIsFirst)

            ElseIf _lngPassTime = 25 Then
                '注册一键安装
                Me.CheckAutoInstallExistInRegester()

            ElseIf Not Common.IsVshostMode Then
                '调试模式默认不自动更新，要不每次发布后都要改项目版本 add by zsh
                If Folder.LangType = LanguageType.vi_VN Then
                    If _lngPassTime = 30 OrElse _lngPassTime Mod 3600 = 0 Then
                        '旧升级逻辑，启动30检测，然后每隔一小时进行检查软件升级
                        Me.StartToCheckUpdate()
                    End If
                Else
                    If _lngPassTime = 2 OrElse _lngPassTime Mod 3600 = 0 Then
                        '程序开启时已启动检测升级，然后每隔一小时进行检查软件升级
                        Me.StartToCheckUpdate()
                    End If
                End If

            End If

        Catch ex As Exception
            Debug.Print(ex.ToString)
            Common.Log(ex.ToString)
        End Try
    End Sub

    'Private Sub CheckDownloadTui()
    '    Try
    '        Dim downDB As SoftManageDB = SoftManageDB.GetInstance()
    '        If Not downDB.CheckTuiExist() Then
    '            'Dim strUUID As String = String.Empty
    '            'Dim strUUID_iPad As String = String.Empty

    '            'For Each device As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
    '            '    If device.ProductType.ToLower.StartsWith("ipad") Then
    '            '        strUUID_iPad = device.Identifier
    '            '    Else
    '            '        strUUID = device.Identifier
    '            '    End If
    '            'Next

    '            'frmTuiInstall.DownloadTbTui(strUUID, "iPhone4,1", "5.1.1")
    '            'frmTuiInstall.DownloadTbTuiHD(strUUID_iPad, "iPad3,1", "5.1.1")
    '        End If
    '    Catch ex As Exception
    '    End Try
    'End Sub

    'Private Sub ShowMsg()
    '    Dim thd As New Thread(AddressOf ShowMsgThread)
    '    thd.IsBackground = True
    '    thd.Start()
    'End Sub

    'Private Sub ShowMsgThread()
    '    tbMessageBox.Show("gxf")
    'End Sub

#End Region

#Region "--- 弹窗消息 ---"

    Private Sub DelayDoThread(ByVal obj As Object)

        Try

            ProcFormHelper.Instance().DoWork(Me._language)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckPopThread_1")
        End Try

        Try
            '如果是熊猫助手不出广告弹窗
            If Utility.IsPanda Then
                Return
            End If

            Try
                Dim strJson As String = PopMsgHelper.Instance.GetContentFromServer(IniSetting.GetUserConnectionType)
                Common.Log("Pop Get Json from server")
                Common.Log(strJson)

                Me.BeginInvoke(New ShowPopMsgHandlerEx(AddressOf ShowPopMsg), strJson)
            Catch ex As Exception
                Common.LogException(ex.ToString(), "CheckPopThread")
            End Try
            '删除特殊log
            Common.LogExDelete()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckPopThread_2")
        End Try
    End Sub

    Private Delegate Sub ShowPopMsgHandlerEx(ByVal strUrl As String)
    Private Sub ShowPopMsg(ByVal strJson As String)
        Try
            PopMsgHelper.Instance.ShowPopMsg(strJson)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "ShowPopMsg")
        End Try
    End Sub

#End Region

#Region "--- 收集数据 ---"

    Private mSendingData As Boolean = False
    Private mCancelSendData As Boolean = False
    Private Sub StartToSendData()
        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf DoSendData))
    End Sub

    Private Sub DoSendData(ByVal obj As Object)
        If Me.mSendingData Then
            Return
        End If
        Me.mSendingData = True

        Try
            '1. 发送之前收集的软件信息
            Me.SendSoftDataPrevious()

            '2. 收集当前的软件信息
            Dim info As tbVersionInfo = Me.CollectSoftData()

            '3 发送目前收集的软件信息
            Me.SendSoftDataNow(info)

            If Not Folder.LangType = LanguageType.en_US Then
                '3. 发送收集到的设备信息
                Me.Start2SendDeviceData()

                '4. 发送已经安装的软件信息
                tbDeviceCache.GetInstanse().SendInstalledSoft()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

        Me.mSendingData = False
        '收集framwork版本号
        ActionCollectHelper.FramworkVersion()
    End Sub

    Private Sub UpdateSoftRunTime()
        Try
            Dim tsRunTime As TimeSpan = Now.Subtract(Me._SoftStartTime)
            Dim iRunTime As Integer = tsRunTime.Minutes

            If iRunTime < 0 Then
                iRunTime = 1
            End If

            Dim tbVersionCache As tbVersionInfoCache = tbVersionInfoCache.GetInstanse()
            tbVersionCache.AddLastSoftUseState(iRunTime)
            tbVersionCache.Save()
        Catch
        End Try
    End Sub

    Private Function CollectSoftData() As tbVersionInfo
        Dim info As New tbVersionInfo()

        Try
            Dim tbVersionCache As tbVersionInfoCache = tbVersionInfoCache.GetInstanse()

            With info
                .Key = Guid.NewGuid.ToString("N")

                '保存当前软件信息在缓存文件中的key
                Me._UserIDInSoftInfo = .Key

                .mac = Common.GetMacAddress
                .Version = Common.GetSoftVersion
                .osType = Common.GetOsType

                .uid = PluginLogin.Instance.Uid

                'Dim pluginLogin As IPluginLogin = Me.GetService(GetType(IPluginLogin))
                'If pluginLogin IsNot Nothing AndAlso String.IsNullOrEmpty(pluginLogin.Uid) = False Then
                '    .uid = pluginLogin.Uid
                'Else
                '    .uid = 0
                'End If

                .startTime = DateTime.Now
                Integer.TryParse(UtilityEx.GetSoftInstallStatus(), .Soft91Status)

                Common.Log("State: " & CInt(.Soft91Status).ToString())

                tbVersionCache.GetLastSoftUseState(.runTimes, .FunctionMapping)
            End With
            'tbVersionCache.AddCacheData(info)
            'tbVersionCache.Save()

        Catch ex As Exception
            Debug.Print(ex.ToString())
        End Try

        Return info
    End Function

    Public Sub Start2SendDeviceData()
        ThreadPool.QueueUserWorkItem(New WaitCallback(AddressOf SendPhoneData))
    End Sub

    Private Sub SendPhoneData(ByVal obj As Object)
        Try
            Me.SendiOSDataByDevice(obj)

            If Common.VerIs30() Then
                Me.SendAndroidDataByDevice(obj)
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString())
        End Try
    End Sub

    Private mSendingiOSDeviceData As Boolean = False
    Private Sub SendiOSDataByDevice(ByVal obj As Object)
        If Me.mSendingiOSDeviceData Then
            Return
        End If
        Me.mSendingiOSDeviceData = True

        Try
            If My.Computer.Network.IsAvailable = False Then
                GoTo DoExit
            End If

            Dim tbDeviceCache As tbDeviceInfoCache = tbDeviceInfoCache.GetInstanse()

            Dim lstVersionInfo As List(Of tbDeviceInfo) = tbDeviceCache.Datas
            If lstVersionInfo IsNot Nothing AndAlso lstVersionInfo.Count > 0 Then

                '发送设备数据
                Dim lstKeys As New List(Of String)
                Dim isSucceed As Boolean = False

                For Each info As tbDeviceInfo In lstVersionInfo
                    If info.Key = tbDeviceCache.UPLOADTIMEKEY Then
                        Continue For
                    End If

                    Dim isSucc As FunctionSucceed = FunctionSucceed.Failure
                    '传输今天以前收集的数据
                    isSucceed = info.SendiOSDeviceData()
                    If isSucceed = True Then
                        lstKeys.Add(info.Key)
                        isSucc = FunctionSucceed.Succeed
                    End If
                    '收集iOS数据成功失败信息
                    ActionCollectHelper.CollectiOSDeviceInfo(Nothing, ModelKey.None, ModelKey.SummarySendiOSDeviceData, ActionDataType.View, isSucc)
                Next

                '删除发送成功的设备数据
                For Each key As String In lstKeys
                    tbDeviceCache.RemoveCacheData(key)
                Next

                '保存文件
                tbDeviceCache.Save()
            End If

            If lstVersionInfo IsNot Nothing Then
                lstVersionInfo.Clear()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

DoExit:
        Me.mSendingiOSDeviceData = False
    End Sub

    Private mSendingAndroidDeviceData As Boolean = False
    Private Sub SendAndroidDataByDevice(ByVal obj As Object)
        If Me.mSendingAndroidDeviceData Then
            Return
        End If
        Me.mSendingAndroidDeviceData = True

        Try
            If My.Computer.Network.IsAvailable = False Then
                GoTo DoExit
            End If

            Dim tbDeviceCache As tbAndroidDeviceInfoCache = tbAndroidDeviceInfoCache.GetInstanse()

            Dim lstVersionInfo As List(Of tbDeviceInfo) = tbDeviceCache.Datas
            If lstVersionInfo IsNot Nothing AndAlso lstVersionInfo.Count > 0 Then

                '发送设备数据
                Dim lstKeys As New List(Of String)
                Dim isSucceed As Boolean = False

                For Each info As tbDeviceInfo In lstVersionInfo
                    If info.Key = tbDeviceCache.UPLOADTIMEKEY Then
                        Continue For
                    End If

                    Dim isSucc As FunctionSucceed = FunctionSucceed.Failure
                    '传输今天以前收集的数据
                    isSucceed = info.SendAndroidDeviceData()
                    If isSucceed = True Then
                        lstKeys.Add(info.Key)
                        isSucc = FunctionSucceed.Succeed
                    End If

                    '收集Android数据成功失败信息
                    ActionCollectHelper.CollectiOSDeviceInfo(Nothing, ModelKey.None, ModelKey.AndroidSendAndroidDeviceData, ActionDataType.View, isSucc)
                Next

                '删除发送成功的设备数据
                For Each key As String In lstKeys
                    tbDeviceCache.RemoveCacheData(key)
                Next

                '保存文件
                tbDeviceCache.Save()
            End If

            If lstVersionInfo IsNot Nothing Then
                lstVersionInfo.Clear()
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString)
        End Try

DoExit:
        Me.mSendingAndroidDeviceData = False
    End Sub

    Private Sub SendSoftDataPrevious()
        Try
            If My.Computer.Network.IsAvailable = False Then
                Return
            End If

            Dim tbVersionCache As tbVersionInfoCache = tbVersionInfoCache.GetInstanse()

            Dim lstVersionInfo As List(Of tbVersionInfo) = tbVersionCache.Datas
            If lstVersionInfo IsNot Nothing AndAlso lstVersionInfo.Count > 0 Then

                '发送软件数据
                Dim lstKeys As New List(Of String)
                Dim uploadTime As DateTime = tbVersionCache.GetUploadTime()
                Dim dt As TimeSpan = DateTime.Now.Date - uploadTime.Date

                If lstVersionInfo.Count > 100 OrElse _
                   uploadTime = DateTime.MinValue OrElse _
                   dt.Days > 0 Then

                    For Each info As tbVersionInfo In lstVersionInfo
                        If info.Key = tbVersionCache.UPLOADTIMEKEY OrElse info.Key = tbVersionCache.SOFTUSESTATEKEY Then
                            Continue For
                        End If

                        '传输今天以前收集的数据
                        info.SendZhushouData()

                        '当前数据显示程序的运行时间大于0分钟，或者当前数据显示的启动时间大于1天，则不再保存
                        If (info.runTimes > 0 OrElse (DateTime.Now.Date - info.startTime.Date).Days > 0) Then
                            lstKeys.Add(info.Key)
                        End If

                        Application.DoEvents()
                    Next

                    '删除发送成功的软件数据
                    For Each key As String In lstKeys
                        tbVersionCache.RemoveCacheData(key)
                    Next

                    '保存上传日期
                    If lstKeys.Count > 0 Then
                        tbVersionCache.AddUploadTime(DateTime.Now)
                    End If

                    '保存文件
                    tbVersionCache.Save()
                End If
            End If

            If lstVersionInfo IsNot Nothing Then
                lstVersionInfo.Clear()
            End If

        Catch ex As Exception
            Debug.Print(ex.ToString())
        End Try
    End Sub

    Private Sub SendSoftDataNow(ByVal info As tbVersionInfo)
        Try
            If My.Computer.Network.IsAvailable = False Then
                Return
            End If

            info.SendZhushouData()

        Catch ex As Exception
            Debug.Print(ex.ToString())
        End Try
    End Sub

    Private Sub RunAnalysisApp()
        'If Folder.LangType = LanguageType.en_US Then
        '    Return
        'End If

        'If Me.bgwAnalysisApp.IsBusy Then
        '    Return
        'End If

        'Me.bgwAnalysisApp.RunWorkerAsync()
    End Sub

    Private Sub bgwAnalysisApp_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwAnalysisApp.DoWork
        Try
            If Not ServerIniSetting.GetAnalysisApp() OrElse Folder.LangType <> LanguageType.zh_CN Then
                Return
            End If

            If IniSetting.GetAnalysisAppDate().Date >= Now.Date Then
                Return
            End If
            '"http://sss.tongbu.com/test/itunes/getUpdateUrl.php"
            'https://itunes.apple.com/cn/lookup?id=490388538
            Dim strAppUrl As String = Utility.GetContentStringFromUrl(WebUrl.ChinaGetAppUrl, Encoding.UTF8, 60000)
            If String.IsNullOrEmpty(strAppUrl) Then
                Exit Sub
            End If

            Dim desUrl As String = AESFunctionHelper.AES_decrypt(strAppUrl)
            Dim strContent As String = Utility.GetContentStringFromUrl(desUrl, Encoding.UTF8, 60000)

            If Not String.IsNullOrEmpty(strContent) AndAlso strContent.Length > 200 Then
                Dim encContent As String = AESFunctionHelper.AES_encrypt(strContent)
                Dim strReturn As String = AESFunctionHelper.PostDataToUrl(encContent, WebUrl.ChinaGetAppData)     '"http://sss.tongbu.com/test/itunes/getAppsData.php"
                IniSetting.SetAnalysisAppDate()
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwAnalysisApp_DoWork")
        End Try
    End Sub

    Public Sub StartToSendOperateData(ByVal blnIsFirst As Boolean)
        If ActionCollectHelper.CollectClose Then
            Return
        End If

        If Folder.LangType <> LanguageType.zh_CN Then
            Return
        End If
        '检查获取数据程序是否存在，如果存在则不继续发送
        If Me.bgwSendOperateData.IsBusy Then
            Return
        End If

        '升级成功行为收集
        Dim succ As FunctionSucceed = IniSetting.GetUpdateSucceed
        If succ <> FunctionSucceed.None Then
            ActionCollectHelper.iTongUpdate(Nothing, succ, IniSetting.GetUpdateVersion)
            '收集到数据到把数据置空。
            IniSetting.SetUpdateSucceed()
            IniSetting.SetUpdateVersion()
        End If

        Me.bgwSendOperateData.RunWorkerAsync(blnIsFirst)
    End Sub

    Public Function CheckSendingOperateData() As Boolean
        If Me.bgwSendOperateData.IsBusy Then
            Utility.WaitSeconds(0.1)
            Return True
        End If
    End Function

    Private Sub bgwSendOperateData_DoWork(ByVal sender As System.Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles bgwSendOperateData.DoWork
        Try
            '1、检查配置是否允许发布
            Dim IsOpen As Boolean = ServerIniSetting.GetIsSendWebOperateData()
            If Not IsOpen Then
                Return
            End If

            Dim blnIsFirst As Boolean = CType(e.Argument, Boolean)
            Dim maxCount As Integer = ServerIniSetting.GetMaxWebOperateCount()
            ActionCollectDB.GetInstance().SendDataToServer(maxCount, blnIsFirst, My.Application.Info.Version.ToString(4))
            '操作时长收集
            ActionCollectDB.GetInstance(ActionCollectType.TimeSpan).SendDataToServer(1, blnIsFirst, My.Application.Info.Version.ToString(4))

            ''2、读取本地缓存的数据
            'Dim blnIsFirst As Boolean = CType(e.Argument, Boolean)
            'Dim maxCount As Integer = ServerIniSetting.GetMaxWebOperateCount()
            'Dim rows As DataRow() = ActionCollectDB.GetInstance().GetAllRow()


            'Dim lstJson As New List(Of String)

            'For Each Item As DataRow In rows
            '    lstJson.Add(Item("Json").ToString())
            'Next

            ''程序启动后，第一次不论多少条，都直接提交。
            'Dim blnIsSend As Boolean = True
            'If Not blnIsFirst Then
            '    Dim count As Integer = ActionCollectHelper.GetActionCount(lstJson)
            '    If count < maxCount Then
            '        blnIsSend = False
            '    End If
            'End If

            'If Not blnIsSend Then
            '    ActionCollectDB.GetInstance().SaveDataTable()
            '    Return
            'End If

            ''3、提交数据
            'Dim strIP As String = Utility.GetMyPublicIP()
            'Dim data As Byte() = ActionCollectProtobuf.ConvertJsonToProtobuf(lstJson, strIP)

            'If data Is Nothing Then
            '    Return
            'End If

            'Dim strUrl As String = String.Format(WebUrl.PostActionData, My.Application.Info.Version.ToString(4))  '添加版本号
            'Dim result As String = Utility.PostData(strUrl, data)

            'Common.LogTest("发送行为数据，服务器返回结果：" & result)
            ''提交成功，删除缓存
            'If result = "1" Then
            '    ActionCollectDB.GetInstance().DeleteRow(rows)

            '    For Each Item As String In lstJson
            '        Common.LogTest(Item)
            '    Next

            'End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "bgwSendWebOperateData_DoWork")
        End Try
    End Sub

#End Region

#Region "--- 公共接口 ---"

    Public Sub ShowTip(ByVal strText As String, _
                         Optional ByVal timeOut As Integer = 0, _
                         Optional ByVal isShowAlways As Boolean = True)

        Try
            If String.IsNullOrEmpty(strText) Then
                Return
            End If

            '没设置为常显示并且主窗体不是在最小状态则不显示提示气泡
            If isShowAlways = False AndAlso Me.WindowState <> FormWindowState.Minimized Then
                Return
            End If

            With Me.mNotifyIcon
                .BalloonTipIcon = ToolTipIcon.None
                .BalloonTipText = strText

                .ShowBalloonTip(IIf(timeOut > 0, timeOut * 1000, 2000))
            End With

        Catch ex As Exception
            Debug.Print(ex.ToString)
        End Try
    End Sub

#End Region

#Region "--- 设备连接事件 ---"

    Private Sub HandleAutoRunRegistry()
        ''''' 读写注册表判断是否关闭自动播放
        'If IniSetting.GetDisableAutorun() Then
        Dim objValue As Object = RegistryHelper.GetRegister(RegistryHive.LocalMachine, _
                                                                   "SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer\", _
                                                                   "NoDriveTypeAutoRun")

        RegistryHelper.SetRegister(RegistryHive.LocalMachine, _
                                        "SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\Explorer\", _
                                        "NoDriveTypeAutoRun", 181)
        ' End If
    End Sub

    Private Sub ShowOrHideNewDeviceForm(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
#If IS_ITONG Then
        If args.Action = DeviceAction.kConnected Then
            frmNewDevice.ShowDeviceInfoFrom(args.Device)
        ElseIf args.Action = DeviceAction.kDisConnected Then
            frmNewDevice.HideDeviceInfoFrom(args.Device)
        End If
#End If
    End Sub

    Public Sub OnConnectionChanged(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
        Try
            Dim blnResponse As Boolean = True

            If args.Action = DeviceAction.kConnected Then
                '初始化安装类, 以便对外可以调用安装服务
                iPhoneInstallHelper.GetInstance(args.Device)
            ElseIf args.Action = DeviceAction.kDisConnected Then
                '关闭此设备的frmNewDevice窗体
                Me.BeginInvoke(New EventHandler(Of ConnectionChangedEventArgs)(AddressOf ShowOrHideNewDeviceForm), sender, args)
            End If

            If args.Action = DeviceAction.kConnected AndAlso MobileDeviceManager.Instance().ConnectedDevices.Count > 1 Then '
                Dim listForm As New List(Of Form)
                For Each frm As Form In Application.OpenForms
                    listForm.Add(frm)
                Next
                For Each frm As Form In listForm
                    If TypeOf frm Is MainForm Then
                        Dim frmMain As MainForm = frm
                        If frmMain.mDevice IsNot Nothing AndAlso frmMain.mDevice.IsConnected Then
                            blnResponse = False
                            Me.BeginInvoke(New EventHandler(Of ConnectionChangedEventArgs)(AddressOf ShowOrHideNewDeviceForm), sender, args)
                            'Debug.Print("B:" & vbTab & args.Action.ToString() & vbTab & args.Device.DeviceName)
                            Exit For
                        End If
                    End If
                Next
                listForm.Clear()
            End If

            If blnResponse Then
                'Debug.Print("A:" & vbTab & args.Action.ToString() & vbTab & args.Device.DeviceName)
                Dim listForm As New List(Of Form)
                For Each frm As Form In Application.OpenForms
                    listForm.Add(frm)
                Next
                For Each frm As Form In listForm
                    If TypeOf frm Is MainForm Then
                        Dim frmMain As MainForm = frm
                        frmMain.OnConnectionChanged(sender, args)
                    End If
                Next
                listForm.Clear()
            End If

        Catch ex As Exception
        End Try
    End Sub

    Public Sub OnRecoveryConnectionChanged(ByVal sender As Object, ByVal args As RecoveryConnectionChangedEventArgs)
        Try
            For Each frm As Form In Application.OpenForms
                If TypeOf frm Is MainForm Then
                    CType(frm, MainForm).OnRecoveryConnectionChanged(sender, args)
                End If
            Next
        Catch ex As Exception
        End Try
    End Sub

    Private mblnShowTrust As Boolean = False
    Public Delegate Sub OnConnectFailedHandler(ByVal sender As Object, ByVal args As ConnectFailedEventArgs)
    Public Sub OnConnectFailed(ByVal sender As Object, ByVal args As ConnectFailedEventArgs)
        'If Me.InvokeRequired Then
        '    Me.Invoke(New OnConnectFailedHandler(AddressOf OnConnectFailed), sender, args)
        'Else
        Try
            Dim device As iPhoneDevice = sender
            Common.LogException("ConnectFailed: " & device.DeviceName & vbTab & device.ProductVersion & vbTab & CInt(args.ErrorCode).ToString())
            Dim img As Image = Nothing
            Dim strMsg As String = String.Empty
            Select Case args.ErrorCode
                Case kAMDError.kAMDPasswordProtectedError
                    '"未能连接到设备 {0}，您必须在设备上输入密码，然后再尝试连接。"
                    strMsg = String.Format(Me._language.GetString("Main.Message.ShouldTypePwd"), device.DeviceName)
                    If Val(device.ProductVersion.Substring(0, 1)) >= 7 Then
                        strMsg = String.Format(Me._language.GetString("Main.Message.TrustPC"), device.DeviceName)
                        img = My.Resources.pic_trust
                    End If
                Case kAMDError.kAMDTrustComputerError
                    '"未能连接到设备 {0}，您必须在设备上信任此电脑，然后再尝试连接。"
                    strMsg = String.Format(Me._language.GetString("Main.Message.TrustPC"), device.DeviceName)
                    img = My.Resources.pic_trust
                Case Else
                    strMsg = Me._language.GetString("Main.Message.DeviceIsLocked")
                    img = My.Resources.pic_trust
            End Select
            '当信任窗体有显示过一次后如果再重新接入设备就不提示重复的提示信息
            If Me.mblnShowTrust Then
                '当message在显示的时候直接重试，关闭message后如果设备上没有点信任还可以提示用户。
                args.Retry = True
                Utility.WaitSeconds(3)
                Return
            End If
            Me.mblnShowTrust = True
            '"您的设备已被密码锁定，iTong无法连接。您必须输入密码，然后才能继续使用。"
            If tbMessageBox.Show(Me.GetMainForm(), strMsg, Me._language.GetString("Common.Info"), MessageBoxButtons.RetryCancel, MessageBoxIcon.Warning, MessageBoxDefaultButton.Button1, img) = Windows.Forms.DialogResult.Retry Then
                args.Retry = True
            End If
            Me.mblnShowTrust = False
        Catch ex As Exception
        End Try
        'End If
    End Sub

    Private Sub DetectDeviceEvent(ByVal sender As Object, ByVal e As EventArgs)
        Try
            If MobileDeviceManager.Instance().ConnectedDevices.Count = 0 Then
                '"The discovery of new equipment, being ready for connection."
                Me.ShowBalloonTip(Me._language.GetString("Main.Message.DiscoveryNewEquipment"))
            End If

            Dim listForm As New List(Of Form)
            For Each frm As Form In Application.OpenForms
                listForm.Add(frm)
            Next
            For Each frm As Form In listForm
                If TypeOf frm Is MainForm Then
                    CType(frm, MainForm).OnConnecting(sender, e)
                End If
            Next
            listForm.Clear()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "DetectDeviceEvent")
        End Try
    End Sub

#End Region

#Region "--- 错误处理 ---"

    '************************************************************
    '**
    '** 名    称：OnThreadException
    '** 功能描述：处理主线程中未处理的异常
    '** 备    注：当主线程出现异常，并且没有try Catch，会捕获到异常，程序不会崩溃。
    '** 参    数：ByVal sender As Object
    '**           ByVal e As Threading.ThreadExceptionEventArgs
    '** 返 回 值：
    '** 全局变量：
    '** 调用模块：
    '** 版本历史：
    '**
    '************************************************************
    Private Sub OnThreadException(ByVal sender As Object, ByVal e As Threading.ThreadExceptionEventArgs)
        Dim strException As String = e.Exception.ToString()
        Common.LogException(strException, "OnThreadException")

        Me.DoProcessException(strException)
    End Sub

    '************************************************************
    '**
    '** 名    称：UnhandledException
    '** 功能描述：处理非主线程中的未处理的异常
    '** 备    注：当非主线程出现异常，并且没有try Catch，会捕获到异常，程序直接崩溃。
    '** 参    数：ByVal sender As Object
    '**           ByVal e As System.UnhandledExceptionEventArgs
    '** 返 回 值：
    '** 全局变量：
    '** 调用模块：
    '** 版本历史：
    '**
    '************************************************************
    Private Sub UnhandledException(ByVal sender As Object, ByVal e As System.UnhandledExceptionEventArgs)
        'Common.LogException(e.ExceptionObject.ToString(), "UnhandledException")

        Dim strException As String = e.ExceptionObject.ToString()
        Common.LogException(strException, "UnhandledException")

        Me.DoProcessException(strException)
    End Sub

    Private mShowGdipWebHelper As Boolean = False
    Private Sub DoProcessException(ByVal strException As String)
        If Utility.IsPanda Then
            Return
        End If

        '服务器配制要显示的错误类型。
        '如果不在错误类型里面就直接传到服务器不显示窗体。
        Dim lseKey As List(Of String) = ServerIniSetting.GetShowFeedbackKeys()
        If lseKey IsNot Nothing AndAlso lseKey.Count > 0 Then
            For Each Item As String In lseKey
                If strException.Contains(Item) Then
                    Utility.ShowFeedback(strException)
                    Exit For
                End If
            Next
        End If

        ActionCollectHelper.ShowFeedbackErrorMsg(PluginLogin.Instance.Account & "," & strException)
        '检测是否有红x的问题存在
        If Not Me.mShowGdipWebHelper AndAlso strException.Contains("System.Drawing.SafeNativeMethods.Gdip") Then
            Me.mShowGdipWebHelper = True
            Common.OpenExplorer("http://news.tongbu.com/86668.html")
        End If
    End Sub

#End Region

    Private Sub ProcForm_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Me.SetInterface()
    End Sub

    Private Sub ProcForm_Shown(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Shown
#If IS_ITONG_ZJ Then
        '设置开机自启动
        If Not DesignMode Then
            AutoRunHelper.CheckAutoRun()
        End If
#End If

        If Common.VerIs30() Then
            '准备Android 设备环境
            AndroidDeviceMonitor.Instance.MakeAndroidEnvoinment(True)
        Else
            Dim deviceMgr As MobileDeviceManager = MobileDeviceManager.Instance()
            AddHandler deviceMgr.DetectDevice, AddressOf DetectDeviceEvent

            AddHandler deviceMgr.ConnectionChanged, AddressOf OnConnectionChanged
            AddHandler deviceMgr.RecoveryConnectionChanged, AddressOf OnRecoveryConnectionChanged
            AddHandler deviceMgr.ConnectFailed, AddressOf OnConnectFailed
        End If

        If Folder.AppType = RunType.DuoBao Then
#If IS_ITONG Then
            Dim frm As New frmDuobao(Me._language)
            frm.Show()
#End If
        Else

            Dim frm As New MainForm(Nothing)
            frm.Show()
        End If

        Me._CanMoveToVirtualMemory = (System.Environment.OSVersion.Platform = PlatformID.Win32NT)

        Me.tmrTimer.Start()
    End Sub

    Public Function GetMainForm() As Form
        Dim frmMain As Form = Nothing
        Try
            For Each frm As Form In Application.OpenForms
                'Debug.Print(frm.Name & vbTab & frm.Text)
                If TypeOf frm Is MainForm Then
                    frmMain = frm
                    Exit For
                    'End If
                End If
            Next
        Catch ex As Exception
        End Try

        Return frmMain
    End Function

    ''获取当前活动窗体 by chenbihai 2015-07-02--------
    'Private Declare Function GetForegroundWindow Lib "user32" () As Long
    'Private Declare Function GetWindowText Lib "user32.dll" Alias "GetWindowTextA" (ByVal hWnd As Long, ByVal lpString As String, ByVal cch As Long) As Long
    'Private Declare Function GetWindowTextLength Lib "user32.dll" Alias "GetWindowTextLengthA" (ByVal hWnd As Long) As Long
    'Public Function GetActivityForm() As Form
    '    Dim frmActivity As Form = Nothing
    '    Dim strActiveWindow As String = GetCaption().ToString()
    '    Dim CurrentWindow As String = String.Empty

    '    Try
    '        For Each frm As Form In Application.OpenForms
    '            CurrentWindow = frm.Handle.ToString()
    '            If String.Compare(strActiveWindow, CurrentWindow, True) = 0 Then
    '                frmActivity = frm
    '                Exit For
    '            End If
    '        Next
    '    Catch ex As Exception
    '    End Try

    '    Return frmActivity
    'End Function
    'Function GetCaption()
    '    Dim hWnd As Long = GetForegroundWindow()
    '    Dim hWndlength As Long, hWndTitle As String, A As Long
    '    hWndlength = GetWindowTextLength(hWnd)
    '    hWndTitle = hWndlength
    '    A = GetWindowText(hWnd, hWndTitle, (hWndlength + 1))
    '    GetCaption = hWnd
    'End Function
    '获取当前活动窗体 by chenbihai 2015-07-02--------

    Public Function CheckMainFormIsOnlyOne() As Boolean
        Dim frmMainCount As Integer = 0

        Try
            For Each frm As Form In Application.OpenForms
                'Debug.Print(frm.Name & vbTab & frm.Text)
                If TypeOf frm Is MainForm Then
                    frmMainCount += 1
                End If
            Next
        Catch ex As Exception
        End Try

        Return (frmMainCount <= 1)
    End Function

    Public Sub CheckMainFormClosing(ByVal sender As Form, ByVal e As FormClosingEventArgs)
        If e.CloseReason = CloseReason.WindowsShutDown OrElse e.CloseReason = CloseReason.TaskManagerClosing OrElse e.CloseReason = CloseReason.None Then
            e.Cancel = False
            Me._AllowCloseForm = True
        End If

        If e.Cancel Then
            '其它子程序正在运行暂不退出
            Me._WaitForCloseForm = True

            Dim act As CloseAction = frmAppExist.CheckClosingFromSetting(Me)

            If act = CloseAction.Close Then
                sender.Hide()
                Me.mNotifyIcon.Visible = False
            ElseIf act = CloseAction.Minize Then
                Me.HideChildForms()
                sender.WindowState = FormWindowState.Minimized
                sender.Hide()
            End If

        Else
            'Me.ClearAppRecordFolder()

            If Me._AllowCloseForm Then
                '配置：退出程序||单击菜单:退出
                Folder.ClearFolder(Folder.TempFolder)
            Else
                Dim act As CloseAction = frmAppExist.CheckClosingFromSetting(sender)
                Select Case act
                    Case CloseAction.Cancel
                        e.Cancel = True

                    Case CloseAction.Close
                        '配置：退出程序||单击菜单:退出
                        sender.Hide()

                    Case CloseAction.Minize
                        '配置：最小化托盘
                        Me.HideChildForms()
                        sender.WindowState = FormWindowState.Minimized
                        sender.Hide()
                        e.Cancel = True
                End Select
            End If

        End If

        If e.Cancel = False Then
            '窗体退出时，执行相关操作
            sender.Hide()
            'Application.DoEvents()

            '媒体管理中有用到第三方的转换工具，在软件结束的时候把进程结束。
            MediaHelper.ExitProcess()

            '退出的时候如果线程还在就结束线程
            Try
                If Me._CheckEnviromentThread IsNot Nothing AndAlso Me._CheckEnviromentThread.ThreadState <> ThreadState.Stopped Then
                    Me._CheckEnviromentThread.Abort()
                End If
            Catch ex As Exception
            End Try

            Try
                If Me.thrPopMsgEvent IsNot Nothing AndAlso Me.thrPopMsgEvent.ThreadState <> ThreadState.Stopped Then
                    Me.thrPopMsgEvent.Abort()
                End If
            Catch ex As Exception
            End Try
        End If
    End Sub

    '清空15天之前备份的软件记录
    Private Sub ClearAppRecordFolder()
        Try
            For Each strDevice As String In Directory.GetDirectories(Folder.BackupAppRecordFolder)
                For Each strApp As String In Directory.GetDirectories(strDevice)
                    Dim strArr() As String = Path.GetFileName(strApp).Split("_")
                    If strArr IsNot Nothing AndAlso strArr.Length = 3 Then
                        If Val(strArr(2)) < Val(DateTime.Now.AddDays(-15).ToString("yyyyMMddHHmmmss")) Then
                            Try
                                Directory.Delete(strApp, True)
                            Catch ex As Exception
                            End Try
                        End If
                    End If
                Next
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString, "ProcForm_ClearAppRecordFolder")
        End Try
    End Sub

    '隐藏正在显示的子窗体
    Private Sub HideChildForms()
        Try
            Me._LstHideChildForms.Clear()

            For Each frm As Form In Application.OpenForms
                If TypeOf frm Is MainForm OrElse TypeOf frm Is ProcForm Then
                    Continue For
                End If

                If frm.Parent Is Nothing AndAlso frm.Visible Then
                    frm.WindowState = FormWindowState.Minimized
                    frm.Hide()
                    Me._LstHideChildForms.Add(frm)
                End If
            Next
        Catch ex As Exception
        End Try
    End Sub

    Public Sub ExitApp()
        Try
            Dim listForms As New List(Of Form)

            For Each frm As Form In Application.OpenForms
                listForms.Add(frm)
            Next

            Try
                For Each frm As Form In listForms
                    frm.Hide()
                Next
                Application.DoEvents()
            Catch ex As Exception
                Common.LogException(ex.ToString(), "ExitApp_HideForm")
            End Try

            For Each frm As Form In listForms
                If TypeOf frm Is tbBaseGuiForm Then
                    CType(frm, tbBaseGuiForm).DisposeResources()
                ElseIf TypeOf frm Is PNGForm Then
                    frm.Hide()
                End If
            Next

            Me.CheckTaskColse()

            '保存web操作行为数据
            ActionCollectDB.GetInstance().SaveDataTable()
            ActionCollectDB.GetInstance(ActionCollectType.TimeSpan).SaveDataTable()     '行为时长

#If IS_ITONG Then
            '收费模块关闭前把信息提交到服务器
            ChargeHelper.Instance(ChargeProjectType.Assistant_Abroad).SendDataToServer(FunctionKey.None)

            SharePodLib.Parsers.Artwork.SqlitePhotoDBHelper.Dispose()
#End If

            Folder.ClearFolder(Folder.TempFolder)

            '如果用户打开feedback就关掉
            Common.KillProcess("Feedback")

            '刷机helper 如果开打就关闭
            Common.KillProcess("tbFlashHelper")

            '微信数据找回helper 如果开打就关闭
            Common.KillProcess("tbWCRHelper")

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ExitApp of ProcForm")
        Finally
            ''MSDN解释：因为这是一个静态事件，所以释放应用程序时必须分离事件处理程序，否则会导致内存泄漏
            RemoveHandler Application.ThreadException, AddressOf OnThreadException

            Me.Close()
        End Try
    End Sub

    Public Sub SetNotifyIconText(ByVal strText As String)
        If strText.Length >= 64 Then
            Me.mNotifyIcon.Text = strText.Substring(0, 59) & " ..."
        Else
            Me.mNotifyIcon.Text = strText
        End If
    End Sub

    Public Sub ShowBalloonTip(ByVal strText As String)
        If String.IsNullOrEmpty(strText) Then
            strText = Path.GetFileNameWithoutExtension(Application.ExecutablePath) & " Running!"
        End If

        With Me.mNotifyIcon
            .BalloonTipIcon = ToolTipIcon.None
            .BalloonTipText = strText

            .ShowBalloonTip(2000)
        End With
    End Sub

    Private Sub CheckTaskColse()
        '停止当前正在下载的任务，并更新下载任务的进度信息到数据库
        Dim downPlugin As MultiThreadDownload = MultiThreadDownload.Instance()
        If downPlugin IsNot Nothing Then
            downPlugin.AppExit()
        End If

        '记录当前软件的运行时间
        Me.UpdateSoftRunTime()

        '退出升级程序
        Try
            If Me.bgwCheckUpdate IsNot Nothing AndAlso Me.bgwCheckUpdate.IsBusy = True Then
                Me.bgwCheckUpdate.CancelAsync()
            End If
        Catch ex As Exception
            Debug.Print("关闭升级程序出错：" & ex.Message)
        End Try

        '退出发送数据的程序
        Me.mCancelSendData = True
    End Sub

    Public Sub SetNotifyIconVisable(ByVal blnVisable As Boolean)
        Me.mNotifyIcon.Visible = blnVisable
    End Sub

    Public Sub CloseSplash()
        While DateTime.Now.Subtract(Me._timeStart).TotalMilliseconds < 500
            Application.DoEvents()
            Thread.Sleep(50)
        End While

        If Me.SplashForm IsNot Nothing Then
            Me.SplashForm.Close()

            '' Added by Utmost20141110
            '' 添加此两行代码可以释放：588bytes
            Me.SplashForm.Dispose()
            Me.SplashForm = Nothing
        End If

    End Sub

    '重新启动iTong
    Public Sub RestartApp()
        '关闭程序
        Me._AllowCloseForm = True
        Me.ExitApp()

        '重启助手
        HelperExeManager.RestartApp()
    End Sub

#Region "--- 刷机 ---"
    Public Sub RestoreProgress(lpData As String)
#If IS_ITONG Then
        Try
            Common.Log("Accept:" & lpData)
            Dim arrData As String() = Split(lpData, ";")  '刷机 激活 格式 Type;Plan;Status;ErrorMsg ，  行为数据收集 Type;ModelKey;StartTime;consuming;StrStatus

            Dim strType As String = arrData(0)
            Dim statusJsonType As JsonType = FlashCommunication.GetJsonTypeStatus(strType)

            If statusJsonType = JsonType.ActionCollect Then '行为数据的收集  格式  Type;ModelKey;StartTime;consuming;StrStatus
                ActionCollectHelper.OperateFlashTimeSpan(ModelKey.FlashStart, CType(arrData(2).Replace("_", " "), DateTime), arrData(3), arrData(4))  '进程间通讯 存在空格就会被隔断 所以两边做了处理 传的时候用“_”替换“ ” 接收的时候 在用 “_”转为“ ”
                Return
            End If


            '刷机 激活 格式
            Dim iPlan As Integer = CType(arrData(1), Integer)
            Dim iPlanEx As Integer = CType(arrData(2), Integer)
            Dim strStatus As String = arrData(3)
            Dim strErrorMsg As String = arrData(4)

            Common.Log("arrData:" & strStatus)

            Dim statusRestoreProgressStatus As RestoreProgressStatus = FlashCommunication.GetStatusFromString(strStatus)

            Dim strError As String = ""
            If String.IsNullOrEmpty(strErrorMsg) Then
                strError = FlashCommunication.GetStrError(statusRestoreProgressStatus)
            Else
                strError = strErrorMsg.Replace("-", " ")
            End If

            If statusRestoreProgressStatus = RestoreProgressStatus.RestoreError Then
                strError = FlashCommunication.GetFlashRestoreError(iPlan, iPlanEx, strError)
            End If

            Dim rpea As New RestoreProgressEventArgs(iPlan, iPlanEx, FlashCommunication.GetStatusFromString(strStatus), strError)

            Dim iDervice As IDevice = Nothing
            If MobileDeviceManager.Instance.ConnectedDevices IsNot Nothing AndAlso MobileDeviceManager.Instance.ConnectedDevices.Count Then
                iDervice = MobileDeviceManager.Instance.ConnectedDevices(0)
            End If

            ' If iDervice IsNot Nothing Then
            Dim flashhelper As FlashHelper = flashhelper.GetInstance(iDervice)
            flashhelper.RestoreProgress(rpea)
            ' End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "ProcForm_RestoreProgress")
        End Try
#End If
    End Sub


#End Region

    Public Sub CreateError()
        If tbMessageBox.Show(Me._language.GetString("Main.Message.CreateError"), Me._language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question) = Windows.Forms.DialogResult.OK Then '"您确定要生成错误日志吗？"’
            CreateErrorZip()
        End If
    End Sub

    Public Shared Sub CreateErrorZip()

        If mTdCreateError IsNot Nothing AndAlso mTdCreateError.ThreadState <> ThreadState.Stopped Then
            Return
        End If

        mTdCreateError = New Thread(AddressOf DoCreateErrorZip)
        mTdCreateError.IsBackground = True
        mTdCreateError.Start()
    End Sub
    Public Shared Sub DoCreateErrorZip()
        Try
            Dim tmpFile As String = Path.Combine(Folder.TempFolder, "ErrorZip\" & Guid.NewGuid().ToString("N").Substring(0, 10))
            Dim tmpFileException As String = Path.Combine(tmpFile, "Exception")
            Dim tmpFileLog As String = Path.Combine(tmpFile, "Logs")
            Dim tmpFileFlashLogs As String = Path.Combine(tmpFile, "FlashLogs")
            Dim tmpFileITUNESLogs As String = Path.Combine(tmpFile, "ITUNESLogs")
            Dim tmpExtractWeChatDB As String = Path.Combine(tmpFile, "ExtractWeChatDB")
            Dim tmpWeChatAud2Wav As String = Path.Combine(tmpFile, "WeChatAud2Wav")
            Dim tmpFileComputerInfo As String = Path.Combine(tmpFile, "ComputerInfo")
            Dim tmpWCRLogs As String = Path.Combine(tmpFile, "WCRLogs")

            Try
                Directory.Delete(tmpFile, True)
            Catch ex As Exception
            End Try

            Folder.CheckFolder(tmpFileException)
            Folder.CheckFolder(tmpFileLog)
            Folder.CheckFolder(tmpWCRLogs)
            Folder.CheckFolder(tmpFileFlashLogs)
            Folder.CheckFolder(tmpFileITUNESLogs)
            Folder.CheckFolder(tmpExtractWeChatDB)
            Folder.CheckFolder(tmpWeChatAud2Wav)
            Folder.CheckFolder(tmpFileComputerInfo)

            CreateMacInfo(tmpFileComputerInfo)

            Dim strSourcePath As String = Path.GetDirectoryName(Folder.ExceptionFolder)
            GetErrorFile(Path.Combine(strSourcePath, "Exception"), tmpFileException)
            GetErrorFile(Path.Combine(strSourcePath, "Exception"), tmpFileException, "ExceptionAndroid")
            GetErrorFile(Path.Combine(strSourcePath, "Logs"), tmpFileLog)
            GetErrorFile(Path.Combine(strSourcePath, "WCRLogs"), tmpWCRLogs)
            GetErrorFile(Path.Combine(strSourcePath, "Logs"), tmpFileLog, "AppleLog")
            GetErrorFile(Path.Combine(strSourcePath, "Logs"), tmpFileLog, "Apple Log")
            GetErrorFile(Path.Combine(strSourcePath, "FlashLogs"), tmpFileFlashLogs, "DebugLog")
            GetErrorFile(Path.Combine(strSourcePath, "FlashLogs"), tmpFileFlashLogs, "Flash")

            GetCopyFile(Path.Combine(strSourcePath, "ExtractWeChatDB"), tmpExtractWeChatDB)
            GetCopyFile(Path.Combine(strSourcePath, "WeChatAud2Wav"), tmpWeChatAud2Wav)

            'ITUNES刷机日志收集
            Dim strItunesiPhonePath As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Apple Computer\iTunes\iPhone Updater Logs")
            Dim strItunesiPadPath As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Apple Computer\iTunes\iPad Updater Logs")
            Dim strItunesiPodPath As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Apple Computer\iTunes\iPod Updater Logs")

            GetErrorFileItunes(strItunesiPhonePath, tmpFileITUNESLogs)
            GetErrorFileItunes(strItunesiPadPath, tmpFileITUNESLogs)
            GetErrorFileItunes(strItunesiPodPath, tmpFileITUNESLogs)

            '压缩
            Dim strZipFilePath As String = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), String.Format("LOG{0}.zip", DateTime.Now.ToString("yyyyMMddHHmmss")))
            Utility.PackFiles(strZipFilePath, tmpFile)
            Common.OpenExplorer(strZipFilePath)

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateErrorZip")
        End Try

    End Sub

    Private Shared Sub GetCopyFile(ByVal sourcePath As String, ByVal desPath As String)
        If Not Directory.Exists(sourcePath) Then
            Return
        End If
        Try
            For Each Item As String In Directory.GetFiles(sourcePath)
                Try
                    File.Copy(Item, Path.Combine(desPath, Path.GetFileName(Item)))
                Catch ex As Exception
                End Try
            Next
        Catch ex As Exception

        End Try

    End Sub
    Private Shared Sub GetErrorFile(ByVal sourcePath As String, ByVal desPath As String, Optional ByVal strName As String = "", Optional ByVal strSuffix As String = "txt")
        Try
            If Directory.Exists(sourcePath) = False Then
                Return
            End If

            If strName.Length = 0 Then
                strName = Path.GetFileNameWithoutExtension(sourcePath).TrimEnd("s")
            End If

            'Dim strFileName As String = String.Format("{0}{1}.{2}", strName, "{0}", strSuffix)
            Dim fileList As New List(Of String)

            '只取今天之前7天的数据
            For index As Integer = 0 To 6
                fileList.Add((DateTime.Now.AddDays(-index)).ToString("yyyyMMdd"))
            Next

            For Each Item As String In Directory.GetFiles(sourcePath)
                Dim strFileName As String = Path.GetFileName(Item)
                For Each itemDate As String In fileList
                    If strFileName.Contains(itemDate) Then
                        Try
                            File.Copy(Item, Path.Combine(desPath, strFileName))
                        Catch ex As Exception
                        End Try
                    End If
                Next
            Next

            fileList.Clear()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetErrorFile")
        End Try


    End Sub

    Private Shared Sub GetErrorFileItunes(ByVal sourcePath As String, ByVal desPath As String)
        Try
            If Directory.Exists(sourcePath) = False Then
                Return
            End If

            Dim strFileInfo As System.IO.FileInfo
            Dim strDirInfo As New System.IO.DirectoryInfo(sourcePath)

            For Each strFileInfo In strDirInfo.GetFiles("*.log", SearchOption.AllDirectories)
                '只取今天之前7天的数据
                If DateDiff(DateInterval.Day, Date.Now, strFileInfo.CreationTime) <= 7 Then
                    Try
                        File.Copy(strFileInfo.FullName, Path.Combine(desPath, Path.GetFileName(strFileInfo.FullName)))
                    Catch ex As Exception
                    End Try
                End If
            Next
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetErrorFile")
        End Try


    End Sub

    Private Shared Sub GetSendLog(ByVal sourcePath As String, ByVal desPath As String)
        Try

            If Not Directory.Exists(sourcePath) Then
                Return
            End If

            Dim fileList As New List(Of String)
            For index As Integer = 0 To 6
                fileList.Add(String.Format("{0}.txt", (DateTime.Now.AddDays(-index)).ToString("yyyyMMdd")))
            Next
            For Each Item As String In Directory.GetFiles(sourcePath)
                If fileList.Contains(Path.GetFileName(Item)) Then
                    Try
                        File.Copy(Item, Path.Combine(desPath, Path.GetFileName(Item)))
                    Catch ex As Exception
                    End Try
                End If
            Next

            fileList.Clear()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetSendLog")
        End Try

    End Sub

    Private Sub CheckMD5CryptoServiceProvider()
        If Not IniSetting.GetCheckMD5CryptoServiceProvider() Then
            Return
        End If

        Dim isError As Boolean = False
        Try
            Dim md5 As System.Security.Cryptography.MD5CryptoServiceProvider = New System.Security.Cryptography.MD5CryptoServiceProvider()
        Catch ex As System.InvalidOperationException
            Common.LogException(ex.ToString(), "CheckMD5CryptoServiceProvider")
            If ex.Message.ToUpper().Contains("FIPS") Then
                isError = True
            End If
        End Try

        If Not isError Then
            Return
        End If

        Dim isModify As Boolean = False
        Try
            Dim strRegPath As String = "SYSTEM\CurrentControlSet\Control\Lsa"

            If Common.IsOS_Of_64Bit() Then
                Dim key64Lsa As RegistryKey64 = RegistryKey64.OpenKey(RegistryHive.LocalMachine, strRegPath, True)

                Try
                    If key64Lsa IsNot Nothing Then
                        key64Lsa.SetValue("FipsAlgorithmPolicy", 0)
                        isModify = True
                    End If
                Catch
                End Try

                If key64Lsa IsNot Nothing Then
                    key64Lsa.Close()
                End If
            End If

            If Not isModify Then
                Dim keyLsa As RegistryKey = Registry.LocalMachine.OpenSubKey(strRegPath, True)
                Try
                    If keyLsa IsNot Nothing Then
                        keyLsa.SetValue("FipsAlgorithmPolicy", 0)
                        isModify = True
                    End If
                Catch
                End Try

                If keyLsa IsNot Nothing Then
                    keyLsa.Close()
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckMD5CryptoServiceProvider")
        End Try

        '兼容扩展
        Dim isModifyEx As Boolean = False
        Try
            Dim strRegPath As String = "SYSTEM\CurrentControlSet\Control\Lsa\FipsAlgorithmPolicy"

            If Common.IsOS_Of_64Bit() Then
                Dim key64Lsa As RegistryKey64 = RegistryKey64.OpenKey(RegistryHive.LocalMachine, strRegPath, True)

                Try
                    If key64Lsa IsNot Nothing Then
                        key64Lsa.SetValue("Enabled", 0)
                        isModifyEx = True
                    End If
                Catch
                End Try

                If key64Lsa IsNot Nothing Then
                    key64Lsa.Close()
                End If
            End If

            If Not isModifyEx Then
                Dim keyLsa As RegistryKey = Registry.LocalMachine.OpenSubKey(strRegPath, True)
                Try
                    If keyLsa IsNot Nothing Then
                        keyLsa.SetValue("Enabled", 0)
                        isModifyEx = True
                    End If
                Catch
                End Try

                If keyLsa IsNot Nothing Then
                    keyLsa.Close()
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckMD5CryptoServiceProvider_Ex")
        End Try

        If isModify OrElse isModifyEx Then
            Dim strMsg As String = Me._language.GetString("Common.System.Restart") '"您的系统存在异常，导致部分功能不能正常使用。" & vbCrLf & "需要重启同步助手进行修复，是否立即重启？"
            If tbMessageBox.Show(strMsg, Me._language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Yes Then

                '关闭程序
                Me._AllowCloseForm = True
                Me.ExitApp()

                '重启助手
                HelperExeManager.RestartApp()
            End If

        Else
            Dim strMsg As String = Me._language.GetString("Common.Info.Regedit") ' "您的注册表有一个异常，将会导致助手【微信】、【正版授权】等功能无法使用。" & vbCrLf & "请点击【确定】查看修复教程。"
            If tbMessageBox.Show(strMsg, Me._language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then

                Common.OpenExplorer("http://news.tongbu.com/92596.html")
            End If

        End If


    End Sub

    Private Sub tsmiCreateComputerInfo_Click(sender As Object, e As EventArgs) Handles tsmiCreateComputerInfo.Click
        Try
            Dim strFolder As String = Path.Combine(Folder.AppFolder, "MacInfo")
            CreateMacInfo(strFolder)
            'If Directory.Exists(strFolder) Then
            '    Directory.Delete(strFolder, True)
            'End If

            'Folder.CheckFolder(strFolder)
            'Dim strDESKey As String = "am58!2#0"
            'Dim strFilePath As String = Path.Combine(strFolder, String.Format("Computer at {0}.txt", DateTime.Now.ToString("yyyyMMdd")))
            'Dim hardDiskId As String = Common.EncryptDES("DiskId=" & Common.GetHardDiskID2(), strDESKey, strDESKey)
            'Dim cpuid As String = Common.EncryptDES("CpuId=" & Common.GetProcessorId(), strDESKey, strDESKey)
            'Dim macAddress As String = Common.EncryptDES("WifiMac=" & Common.GetMacAddress(), strDESKey, strDESKey)
            'Dim macid As String = Common.EncryptDES("MacId=" & Common.GetMacid(), strDESKey, strDESKey)
            'Dim allInfo As String = String.Format("{0}_@_{1}_@_{2}_@_{3}", hardDiskId, cpuid, macAddress, macid)
            'If File.Exists(strFilePath) Then
            '    File.Delete(strFilePath)
            'End If
            'Using sw As New StreamWriter(strFilePath, True, Encoding.UTF8)
            '    sw.WriteLine(allInfo)
            'End Using
            Common.OpenExplorer(strFolder)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "tsmiCreateComputerInfo_Click")
        End Try
    End Sub

    Private Shared Sub CreateMacInfo(ByVal strPath As String)
        Try
            'Dim strFolder As String = Path.Combine(Folder.AppFolder, "MacInfo")
            If Directory.Exists(strPath) Then
                Directory.Delete(strPath, True)
            End If

            Folder.CheckFolder(strPath)
            Dim strDESKey As String = "am58!2#0"
            Dim strFilePath As String = Path.Combine(strPath, String.Format("Computer at {0}.txt", DateTime.Now.ToString("yyyyMMdd")))
            Dim hardDiskId As String = Common.EncryptDES("DiskId=" & Common.GetHardDiskID2(), strDESKey, strDESKey)
            Dim cpuid As String = Common.EncryptDES("CpuId=" & Common.GetProcessorId(), strDESKey, strDESKey)
            Dim macAddress As String = Common.EncryptDES("WifiMac=" & Common.GetMacAddress(), strDESKey, strDESKey)
            Dim macid As String = Common.EncryptDES("MacId=" & Common.GetComputerID(), strDESKey, strDESKey)
            Dim allInfo As String = String.Format("{0}_@_{1}_@_{2}_@_{3}", hardDiskId, cpuid, macAddress, macid)
            If File.Exists(strFilePath) Then
                File.Delete(strFilePath)
            End If
            Using sw As New StreamWriter(strFilePath, True, Encoding.UTF8)
                sw.WriteLine(allInfo)
            End Using
            'Common.OpenExplorer(strFolder)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateMacInfo")
        End Try
    End Sub

#Region "--- 微信删除找回 ---"

    Private mTdWCRData As Thread
    Private mQueueWCRData As New Queue(Of String)

    Public Sub WCRDataProgress(lpData As String)
        FindWeChatDataHelper.Instance().WCRDataProgress(lpData)
    End Sub

#End Region


End Class