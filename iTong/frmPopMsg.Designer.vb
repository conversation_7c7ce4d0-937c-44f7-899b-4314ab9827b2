﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmPopMsg
    Inherits tbBaseGuiForm

    '窗体重写释放，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意: 以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmPopMsg))
        Me.btn_close = New iTong.Components.tbButton
        Me.pnlMain = New System.Windows.Forms.Panel
        Me.webMsg = New iTong.Components.tbWebBrowserEx
        Me.pbLoading = New System.Windows.Forms.PictureBox
        Me.pnlMain.SuspendLayout()
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(295, 1)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 23
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'pnlMain
        '
        Me.pnlMain.BackColor = System.Drawing.Color.Transparent
        Me.pnlMain.Controls.Add(Me.webMsg)
        Me.pnlMain.Location = New System.Drawing.Point(1, 32)
        Me.pnlMain.Name = "pnlMain"
        Me.pnlMain.Size = New System.Drawing.Size(318, 187)
        Me.pnlMain.TabIndex = 25
        '
        'webMsg
        '
        Me.webMsg.AllowWebBrowserDrop = False
        Me.webMsg.Client = ""
        Me.webMsg.IsConnected = False
        Me.webMsg.IsWebBrowserContextMenuEnabled = False
        Me.webMsg.Jailbreaked = False
        Me.webMsg.LoadingColor = System.Drawing.Color.FromArgb(CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer), CType(CType(106, Byte), Integer))
        Me.webMsg.LoadingFont = Nothing
        Me.webMsg.LoadingGif = CType(resources.GetObject("webMsg.LoadingGif"), System.Drawing.Image)
        Me.webMsg.Location = New System.Drawing.Point(-2, -2)
        Me.webMsg.MinimumSize = New System.Drawing.Size(20, 20)
        Me.webMsg.Name = "webMsg"
        Me.webMsg.ScriptErrorsSuppressed = True
        Me.webMsg.ScrollBarsEnabled = False
        Me.webMsg.ShowLoadingWait = False
        Me.webMsg.ShowNavigateErrorPage = True
        Me.webMsg.ShowProgress = True
        Me.webMsg.Size = New System.Drawing.Size(322, 191)
        Me.webMsg.SN = ""
        Me.webMsg.TabIndex = 0
        Me.webMsg.UserInfo = ""
        Me.webMsg.UserInfoEncode = ""
        '
        'pbLoading
        '
        Me.pbLoading.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.pbLoading.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pbLoading.Image = Global.iTong.My.Resources.Resources.pnl_loading
        Me.pbLoading.Location = New System.Drawing.Point(1, 29)
        Me.pbLoading.Margin = New System.Windows.Forms.Padding(0)
        Me.pbLoading.Name = "pbLoading"
        Me.pbLoading.Size = New System.Drawing.Size(318, 190)
        Me.pbLoading.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.pbLoading.TabIndex = 27
        Me.pbLoading.TabStop = False
        '
        'frmPopMsg
        '
        Me.ClientSize = New System.Drawing.Size(320, 220)
        Me.Controls.Add(Me.pbLoading)
        Me.Controls.Add(Me.pnlMain)
        Me.Controls.Add(Me.btn_close)
        Me.MinimumSize = New System.Drawing.Size(11, 40)
        Me.Name = "frmPopMsg"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.Manual
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "5,32,6,8"
        Me.pnlMain.ResumeLayout(False)
        CType(Me.pbLoading, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As tbButton
    Friend WithEvents pnlMain As System.Windows.Forms.Panel
    Friend WithEvents webMsg As tbWebBrowserEx
    Friend WithEvents pbLoading As System.Windows.Forms.PictureBox

End Class
