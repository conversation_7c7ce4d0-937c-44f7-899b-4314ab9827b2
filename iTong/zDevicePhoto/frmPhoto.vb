﻿Imports System.Threading
Imports System.Drawing.Imaging
Imports SharePodLib
Imports SharePodLib.Parsers.Artwork
Imports iTong.CoreFoundation
Imports iTong.Device
Imports iTong.Components

Public Class frmPhoto

    Private mSyncPhoto As Boolean = False
    Private mIsSelectTbWallpaper As Boolean = False
    Private mDCIMLoad As Boolean = False

    Private mDicCameraPics As New Dictionary(Of String, String)
    Private mCameraFolder As String = ""
    Private mCameraFolderMISC As String = ""
    Private mCameraMaxIndex As Integer = 0

    Private mlstAddedWallpaper As List(Of String)                           '用来记录当前操作已经添加到设备上的壁纸
    Private mLstUploadToPhoneVideoTemp As List(Of String) = Nothing

    Private mDicThread As New Dictionary(Of ButtonSelect, Thread)
    Private mLstThread As New List(Of Thread)
    Private mPhotosDBUpdateTime As DateTime = Now
    Private mWaitListOfMusicFiles As New List(Of String)
    Private mWaitListOfPhotoLibFiles As New List(Of String)            '保存添加到图库的相片

    Private mPhotoLibNode As tbTreeNode = Nothing
    Private mNodeLocal As tbTreeNode
    Private mNodeAndroid As tbTreeNode
    Private mNodeDevice As tbTreeNode
    Private mNodeSite As tbTreeNode
    Private mLoadPhotoLibThread As Thread                                   '第一次加载PhotoLib节点
    Private mFormWallpaper As frmWallpaper                                   'wallpaper 和 photo窗体设置 item显示 Largeicon 和detial模式要相互联动
    Private mPanelForm As New Panel
    Private mFormWallpaperLocal As frmWallpaperLocal
    Private mThreadLoadPicCount As Thread
    Private mNoShowInfo As Boolean = False
    '是否多次添加图片到设备。如果还在添加就先缓存起来。
    Private mPhotoQueue As Queue(Of Object)
    Private mblnImporting As Boolean = False

    'iOS6上传图片
    Private mWorkerUploadPhotoForiOS6 As System.ComponentModel.BackgroundWorker
    Private mTuiAFC As IntPtr = IntPtr.Zero
    Private mTuiTxt As String = String.Empty
    Private mCoreUpdate As CoreUpdateHelper = Nothing
    Private mDownloadManager As MultiThreadDownload = Nothing
    Private mInstallHelper As iPhoneInstallHelper = Nothing
    'Private mAppSKU As String = ""                                        '记录正在下载的软件SKU
    Private mcHelper As CameraHelper = Nothing
    Private mCamerHelperThread As Thread
    Private mfrmMessage As Form = Nothing
    Private mfrmErrorMessage As tbMessageBox = Nothing
    Private mActionType As ActionFuncType = ActionFuncType.Photo
    Private mLoadDBErrorMessage As Boolean = False

    '2015-06-24 by chenbihai 第一次进入默认点击导入到照相机
    Private mDefaultClick As Boolean = False
    Private mPhotoConvert As frmPhotoConvert = Nothing

#Region "--- 初始化 窗体关闭---"

    Public Sub New(ByVal application As IApplication, ByVal iPhone As iPhoneDevice)
        Me.mApplication = application
        Me.mDevice = iPhone
        Me.Language = Me.mApplication.Language
        Me.FunctionMappingKey = FunctionKey.Photo
        Me.Icon = My.Resources.iTong
        'Me.FilletRadius = 5
        Me.StartPosition = FormStartPosition.CenterParent
        Me.Owner = Me.mApplication

        If Me.mDevice IsNot Nothing Then
            RemoveHandler mDevice.AddWallpaperEvent, AddressOf OnAddWallpaper
            AddHandler mDevice.AddWallpaperEvent, AddressOf OnAddWallpaper
        End If
    End Sub

    Public Overrides Sub CheckInit()
        MyBase.CheckInit()

        Try
            If Me.mDevice IsNot Nothing Then
                ''设置初始化参数
                Me.mCameraDataLoad = False
                Me.mScreenShotDataLoad = False
                Me.mVideoDataLoad = False
                Me.mPhotoLibLoad = False
                Me.mButtonSelect = ButtonSelect.Camera
                Me.mblnImporting = False

                If Common.VerIs30() Then
                    Me.btnViewList.Checked = True
                Else
                    Me.btnViewLargeIcon.Checked = True
                End If
                Me.tvwDevice.Enabled = False

                If Me.tvwDevice.SelectedNode IsNot Nothing AndAlso Me.mNodeAndroid IsNot Nothing AndAlso Me.tvwDevice.SelectedNode Is Me.mNodeAndroid Then
                    Me.tvwDevice.SelectedNode = tvwDevice.Nodes("iphone")
                End If

                If Me.mNodeAndroid IsNot Nothing Then
                    Me.mNodeAndroid.Visible = False
                End If

                If Me.mPhotoThumb IsNot Nothing Then
                    Me.mPhotoThumb.Dispose()
                    Me.mPhotoThumb = Nothing
                End If

                Me.mSyncPhoto = False
                Me.mDCIMLoad = False

                If Me.mDCIMLoad = False Then
                    Me.pnlLoading.Dock = DockStyle.Fill
                    Me.pnlLoading.BringToFront()
                    Me.pnlLoading.Visible = True

                    'Application.DoEvents()
                End If

                Me.InitAllListView()
                Me.InitDeviceNode()

                Me.btnImportLivePhoto.Visible = True

                RemoveHandler mDevice.AddWallpaperEvent, AddressOf OnAddWallpaper
                AddHandler mDevice.AddWallpaperEvent, AddressOf OnAddWallpaper

                RemoveHandler Me.mDevice.DeviceNameChanged, AddressOf OnDeviceNameChanged
                AddHandler Me.mDevice.DeviceNameChanged, AddressOf OnDeviceNameChanged

            Else
                Me.tvwDevice.SelectedNode = tvwDevice.Nodes("iphone")
                Me.tvwDevice.Enabled = True

            End If
        Catch ex As Exception
        End Try

        Me.SetSiteNodeVisible()
    End Sub

    Protected Overrides Sub InitControls()
        Me.InitAllListView()
        Me.InitWallpaperLocal()
        Me.InitPhotoTreeView()
        If Me.Parent IsNot Nothing Then
            Me.tlpMain.Dock = DockStyle.Fill
        End If

        Me.pnlLoading.BringToFront()
        Me.UpdateTranslateStatus(True)

        MyBase.InitControls()

        '隐藏导入按钮并移动删除按钮
        Me.HideImportFileButton()
        Me.btnDelete.Location = New Point(78, 5)

        Try
            Me.mThreadLoadPicCount = New Thread(AddressOf LoadPicCount)
            With Me.mThreadLoadPicCount
                .IsBackground = True
                .Start()
            End With
        Catch ex As Exception
        End Try

        '网页跳转事件
        RemoveHandler Me.mApplication.GoToSiteEvent, AddressOf mApplication_GoToSiteEvent
        AddHandler Me.mApplication.GoToSiteEvent, AddressOf mApplication_GoToSiteEvent
    End Sub

    Private Sub mApplication_GoToSiteEvent(ByVal sender As tbWebBrowser, ByVal type As ActionFuncType, ByVal strNode As String, ByVal strDeviceID As String, ByVal strUrl As String)
        Try
            If Me.mActionType <> type Then
                Return
            End If

            Dim node As tbTreeNode = Me.tvwDevice.Nodes(strNode)
            If node Is Nothing Then
                node = Me.tvwDevice.Nodes("web" & strNode)
            End If

            If node IsNot Nothing AndAlso String.IsNullOrEmpty(strUrl) Then 'Me.Name.ToLower.StartsWith(strDeviceID.ToLower)
                Me.tvwDevice.SelectedNode = node
            End If

            If Not String.IsNullOrEmpty(strUrl) Then
                frmSite.Instance(Me.mApplication, Me.mDevice).SelectSite(strNode, strUrl)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub LoadPicCount()
        Try
            If Me.mNodeLocal Is Nothing Then
                Return
            End If

            If Me.InvokeRequired Then
                Me.Invoke(New Threading.ThreadStart(AddressOf LoadPicCount))
            Else
                Me.mFormWallpaperLocal.LoadPicCount(WallpaperLoaclType.iPhone, Me.mNodeLocal.ChildNodes("iphone"))
                Me.mFormWallpaperLocal.LoadPicCount(WallpaperLoaclType.iPad, Me.mNodeLocal.ChildNodes("ipad"))
                If Common.VerIs30 Then
                    Me.mFormWallpaperLocal.LoadPicCount(WallpaperLoaclType.Android, Me.mNodeLocal.ChildNodes("Android"))
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Private Sub InitAllListView()
        'ListView的ImageList
        Me.InitImageList(Me.mCameraSmallImageList, Me.mCameraLargeImageList)
        Me.InitImageList(Me.mScreenShotSmallImageList, Me.mScreenShotLargeImageList)
        Me.InitImageList(Me.mVideoSmallImageList, Me.mVideoLargeImageList)
        Me.InitImageList(Me.mPhotoLibSmallImageList, Me.mPhotoLibLargeImageList)

        '初始化右侧的窗体
        Me.mCameraListView = Me.CreateListView(Me.mCameraSmallImageList, Me.mCameraLargeImageList)
        Me.mScreenShotListView = Me.CreateListView(Me.mScreenShotSmallImageList, Me.mScreenShotLargeImageList)
        Me.mVideoListView = Me.CreateListView(Me.mVideoSmallImageList, Me.mVideoLargeImageList)
        Me.mPhotoLibListView = Me.CreateListView(Me.mPhotoLibSmallImageList, Me.mPhotoLibLargeImageList)

        Me.mFormWallpaper = New frmWallpaper(Me.mApplication, Me.mDevice)
        Me.mFormWallpaper.mfrmPhoto = Me
        Utility.AddForm2Panel(Me.mFormWallpaper, Me.mPanelForm, False)
        Me.mFormWallpaper.SendToBack()
        Me.mFormWallpaper.Dock = DockStyle.Fill

        If Not Me.tlpMain.Controls.Contains(Me.mPanelForm) Then
            Me.tlpMain.Controls.Add(Me.mPanelForm, 2, 0)
            Me.tlpMain.SetRowSpan(Me.mPanelForm, 3)
            Me.mPanelForm.Dock = DockStyle.Fill
            Me.mPanelForm.Margin = System.Windows.Forms.Padding.Empty
            Me.mPanelForm.Visible = False
        End If
    End Sub

    Private Sub InitWallpaperLocal()
        Me.mFormWallpaperLocal = New frmWallpaperLocal(Me.mApplication, Me.mDevice, FunctionKey.Photo)
        Me.mFormWallpaperLocal.photoForm = Me
        Utility.AddForm2Panel(Me.mFormWallpaperLocal, Me.mPanelForm, False)
        Me.mFormWallpaperLocal.Dock = DockStyle.Fill
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.btnImport.Text = Me.Language.GetString("Media.Button.Import")                    '"导入"
        Me.btnExport.Text = Me.Language.GetString("Media.Button.Export")                    '"导出"
        Me.btnDelete.Text = Me.Language.GetString("Common.Delete")                          '"删除"
        Me.btnRefresh.tbText = Me.Language.GetString("Common.Button.Refresh")            '"刷新"

        Me.tsmiImportFile.Text = Me.Language.GetString("File.Button.UploadFiles")               '"导入文件"
        Me.tsmiImportFolder.Text = Me.Language.GetString("File.Menu.UploadFolder")          '"导入文件夹"
        Me.tsmiExport.Text = Me.Language.GetString("Media.Button.Export")                   '"导出"
        Me.tsmiRefresh.Text = Me.Language.GetString("Common.Button.Refresh")                '"刷新"
        Me.tsmiDelete.Text = Me.Language.GetString("Common.Delete")                         '"删除"

        Me.tsmiImportFileButton.Text = Me.Language.GetString("File.Button.UploadFiles")               '"导入文件"
        Me.tsmiImportFolderButton.Text = Me.Language.GetString("File.Menu.UploadFolder")    '"导入文件夹"
        Me.Text = Me.Language.GetString("Main.Button.Photo")                                '"图片"

    End Sub

    Private Sub InitPhotoTreeView()
        MyBase.InitTreeView()

        Me.tvwDevice.SuspendLayout()
        Me.tvwDevice.Nodes.Clear()

        If Folder.LangType = LanguageType.zh_CN Then
            Me.mNodeLocal = New tbTreeNode("Library", Me.Language.GetString("App.TreeNode.Library"), Color.FromArgb(119, 143, 178), True)                           '"Library"
            Me.mNodeLocal.CanCollapse = False
            Me.tvwDevice.Nodes.Add(Me.mNodeLocal)

            Dim strTitle As String = Me.Language.GetString("Photo.Label.DeviceWallpaper")                                                                           '"{0} 壁纸"
            Dim iPhoneNodeLocal As New tbTreeNode("iphone", String.Format(strTitle, "iPhone"))                                                                      '"LocalWallpaper"        
            iPhoneNodeLocal.NodeIcon = My.Resources.tvw_iphone_2
            iPhoneNodeLocal.NodeIconState = ImageState.TwoState
            Me.mNodeLocal.ChildNodes.Add(iPhoneNodeLocal)

            Dim iPadNodeLocal As New tbTreeNode("ipad", String.Format(strTitle, "iPad"))                                                                            '"LocalWallpaper"
            iPadNodeLocal.NodeIcon = My.Resources.tvw_ipad_2
            iPadNodeLocal.NodeIconState = ImageState.TwoState
            Me.mNodeLocal.ChildNodes.Add(iPadNodeLocal)

            If Common.VerIs30 Then
                mNodeAndroid = New tbTreeNode("Android", String.Format(strTitle, "Android"))                                                                            '"LocalWallpaper"
                mNodeAndroid.NodeIcon = My.Resources.tvw_iphone_2
                mNodeAndroid.NodeIconState = ImageState.TwoState
                Me.mNodeLocal.ChildNodes.Add(mNodeAndroid)
            End If

            If Common.VerIs30 Then
                Dim nodeWeb As New tbTreeNode("webnode", Me.Language.GetString("Site.Label.WebResource"), Color.Black, True) '网络资源
                Me.tvwDevice.Nodes.Add(nodeWeb)
                frmSite.CreateSiteNode(nodeWeb, WebSiteType.Wallpaper)
                Me.mNodeSite = nodeWeb
                For Each Item As tbTreeNode In Me.mNodeSite.ChildNodes
                    If Item.Name = "WebWallpaperLivePhotos" AndAlso IniSetting.GetShowLivePhotoWebDot() = True Then
                        Item.NodeShowDot = True
                        Exit For
                    End If
                Next
                Me.SetSiteNodeVisible()

            End If
        End If

        Dim strText As String = String.Empty

        If Me.mDevice IsNot Nothing Then
            strText = String.Format("{0} - {1}", Me.Language.GetString("App.TreeNode.Device"), Me.mDevice.DeviceName)
        Else
            strText = Me.Language.GetString("App.TreeNode.NotConnectDevice")  '"设备(未连接)"
        End If
        Me.mNodeDevice = New tbTreeNode("Device", strText, Color.FromArgb(119, 143, 178), True)
        Me.mNodeDevice.CanCollapse = False

        Me.tvwDevice.Nodes.Add(Me.mNodeDevice)
    End Sub

    Private Sub SetSiteNodeVisible()
        If Me.mNodeSite IsNot Nothing Then
            If Me.mDevice IsNot Nothing Then
                For Each Item As tbTreeNode In Me.mNodeSite.ChildNodes
                    If Item.Name.ToLower.Contains("android") Then
                        Item.Visible = False
                    Else
                        Item.Visible = True
                    End If
                Next
            ElseIf Me.mAndroid IsNot Nothing Then
                For Each Item As tbTreeNode In Me.mNodeSite.ChildNodes
                    If Item.Name.ToLower.Contains("android") Then
                        Item.Visible = True
                    Else
                        Item.Visible = False
                    End If
                Next
            Else
                For Each Item As tbTreeNode In Me.mNodeSite.ChildNodes
                    Item.Visible = True
                Next
            End If
        End If

        If Me.mNodeLocal IsNot Nothing Then
            If Me.mDevice IsNot Nothing Then
                For Each Item As tbTreeNode In Me.mNodeLocal.ChildNodes
                    If Item.Name.ToLower.Contains("android") Then
                        Item.Visible = False
                    Else
                        Item.Visible = True
                    End If
                Next
            ElseIf Me.mAndroid IsNot Nothing Then
                For Each Item As tbTreeNode In Me.mNodeLocal.ChildNodes
                    If Item.Name.ToLower.Contains("android") Then
                        Item.Visible = True
                    Else
                        Item.Visible = False
                    End If
                Next
            Else
                For Each Item As tbTreeNode In Me.mNodeLocal.ChildNodes
                    Item.Visible = True
                Next
            End If
        End If
    End Sub

    Private Sub InitDeviceNode()
        Me.mNodeDevice.ChildNodes.Clear()

        Dim node As tbTreeNode
        Me.mNodeDevice.Text = String.Format("{0} - {1}", Me.Language.GetString("App.TreeNode.Device"), Me.mDevice.DeviceName)
        node = Me.mNodeDevice.ChildNodes("Camera")
        If node Is Nothing Then
            node = Me.mNodeDevice.ChildNodes.Add("Camera", Me.Language.GetString("Photo.Label.Camera"), False, False)                         '"照相机"
        End If
        node.NodeIcon = My.Resources.tvw_camera_2
        node.NodeIconState = ImageState.TwoState

        If Me.mDevice.VersionNumber >= 320 Then
            node = Me.mNodeDevice.ChildNodes("Video")
            If node Is Nothing Then
                node = Me.mNodeDevice.ChildNodes.Add("Video", Me.Language.GetString("Media.Type.Video"), False, False)                        '"视频"
            End If
            node.NodeIcon = My.Resources.tvw_video_2
            node.NodeIconState = ImageState.TwoState
        End If

        If Me.mDevice.VersionNumber >= 900 Then
            Dim strName As String = pAlbumKind.Selfie.ToString()
            Dim strTitle As String = Me.Language.GetString("Media.Type.Selfie")                     '"自拍"
            Dim imgNode As Image = My.Resources.tvw_photolib_2

            node = Me.mNodeDevice.ChildNodes("PhotoLib." & strName)
            If node Is Nothing Then
                Try
                    node = Me.mNodeDevice.ChildNodes.Add("PhotoLib." & strName, strTitle, False, False)                        '"视频"
                    node.NodeIcon = imgNode
                    node.NodeIconState = ImageState.TwoState
                    node.Visible = False
                Catch ex As Exception
                End Try
            End If
        End If

        node = Me.mNodeDevice.ChildNodes("ScreenShot")
        If node Is Nothing Then
            node = Me.mNodeDevice.ChildNodes.Add("ScreenShot", Me.Language.GetString("Media.Type.ScreenSnapshot"), False, False)                         '"屏幕快照"
        End If
        node.NodeIcon = My.Resources.tvw_photolib_2
        node.NodeIconState = ImageState.TwoState

        If Me.mDevice.VersionNumber >= 600 Then
            Dim list As New List(Of pAlbumKind)
            list.Add(pAlbumKind.SlowMotion)
            list.Add(pAlbumKind.TimeLap)
            list.Add(pAlbumKind.Panoramic)
            list.Add(pAlbumKind.Hidden)
            list.Add(pAlbumKind.Delete)
            list.Add(pAlbumKind.ContinuousShots)

            For Each pType As pAlbumKind In list
                Dim strName As String = pType.ToString()
                Dim strTitle As String = String.Empty
                Dim imgNode As Image = My.Resources.tvw_photolib_2

                Select Case pType
                    Case pAlbumKind.Panoramic
                        strTitle = Me.Language.GetString("Photo.Label.Panoramas") '"全景照片"
                    Case pAlbumKind.SlowMotion
                        strTitle = Me.Language.GetString("Photo.Label.SlowMotion") '"慢动作"
                        imgNode = My.Resources.tvw_video_2
                    Case pAlbumKind.TimeLap
                        strTitle = Me.Language.GetString("Photo.Label.TimeLapse") '"延迟摄影"
                        imgNode = My.Resources.tvw_video_2
                    Case pAlbumKind.Hidden
                        strTitle = Me.Language.GetString("Photo.Label.Hidden") ' "已隐藏"
                    Case pAlbumKind.Delete
                        strTitle = Me.Language.GetString("Photo.Label.RecentlyDeleted") '"最近删除"
                    Case pAlbumKind.ContinuousShots
                        strTitle = Me.Language.GetString("Photo.Label.ContinuousShots") '"#连拍快照"

                End Select

                node = Me.mNodeDevice.ChildNodes("PhotoLib." & strName)
                If node Is Nothing Then
                    Try
                        node = Me.mNodeDevice.ChildNodes.Add("PhotoLib." & strName, strTitle, False, False)                        '"视频"
                        node.NodeIcon = imgNode
                        node.NodeIconState = ImageState.TwoState
                        node.Visible = False
                    Catch ex As Exception
                    End Try
                End If
            Next
        End If


        If Me.mDevice.Jailbreaked Then
            node = Me.mNodeDevice.ChildNodes("WallPaper")
            If node Is Nothing Then
                node = mNodeDevice.ChildNodes.Add("WallPaper", Me.Language.GetString("Photo.Label.WallPaper"), False, False)                  '"壁纸"
            End If
            node.NodeIcon = My.Resources.tvw_wallpaper_2
            node.NodeIconState = ImageState.TwoState
        End If
        If Me.mPhotoLibNode Is Nothing OrElse mNodeDevice.ChildNodes(Me.mPhotoLibNode.Name) Is Nothing Then
            Me.mPhotoLibNode = mNodeDevice.ChildNodes.Add("PhotoLib", Me.Language.GetString("Photo.Label.Gallery"), False, False)      '"照片图库"
        End If
        Me.mPhotoLibNode.CanCollapse = False
        Me.mPhotoLibNode.NodeIcon = My.Resources.tvw_photolib_2
        Me.mPhotoLibNode.NodeIconState = ImageState.TwoState


        Me.tvwDevice.ResumeLayout()

        'If Me.tvwDevice.SelectedNode Is Nothing Then
        If Me.mButtonSelect = ButtonSelect.PhotoLib Then
            Me.tvwDevice.SelectedNode = tvwDevice.Nodes("PhotoLib")
        Else
            Me.tvwDevice.SelectedNode = tvwDevice.Nodes("Camera")
        End If
        'Else
        '    Me.SwitchListView(Me.tvwDevice.SelectedNode.Name)
        'End If
    End Sub

    Private Sub InitDownload()
        If Me.mDownloadManager Is Nothing Then
            Me.mDownloadManager = MultiThreadDownload.Instance

            RemoveHandler Me.mDownloadManager.DownloadItemCallBack, AddressOf OnDownloading
            AddHandler Me.mDownloadManager.DownloadItemCallBack, AddressOf OnDownloading

            RemoveHandler Me.mDownloadManager.DownloadItemCompleted, AddressOf OnDownloaded
            AddHandler Me.mDownloadManager.DownloadItemCompleted, AddressOf OnDownloaded
        End If

        If Me.mCoreUpdate Is Nothing Then
            Me.mCoreUpdate = CoreUpdateHelper.Instance()

            RemoveHandler Me.mCoreUpdate.CheckDownloadEventHandler, AddressOf OnCheckDownload
            AddHandler Me.mCoreUpdate.CheckDownloadEventHandler, AddressOf OnCheckDownload

            RemoveHandler Me.mCoreUpdate.CombineSingling, AddressOf OnCombineSingling
            AddHandler Me.mCoreUpdate.CombineSingling, AddressOf OnCombineSingling
        End If

        If Me.mInstallHelper Is Nothing Then
            Me.mInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)

            RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
            AddHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
        End If
    End Sub

    Private Sub RemoveDownload()
        If Me.mDownloadManager IsNot Nothing Then
            RemoveHandler Me.mDownloadManager.DownloadItemCallBack, AddressOf OnDownloading
            RemoveHandler Me.mDownloadManager.DownloadItemCompleted, AddressOf OnDownloaded
            Me.mDownloadManager = Nothing
        End If

        If Me.mCoreUpdate IsNot Nothing Then
            RemoveHandler Me.mCoreUpdate.CheckDownloadEventHandler, AddressOf OnCheckDownload
            RemoveHandler Me.mCoreUpdate.CombineSingling, AddressOf OnCombineSingling
            Me.mCoreUpdate = Nothing
        End If

        If Me.mInstallHelper IsNot Nothing Then
            RemoveHandler Me.mInstallHelper.InstallEventHandler, AddressOf mInstallHelper_OnInstall
            Me.mInstallHelper = Nothing
        End If
    End Sub

    Private Function AddPhotoListView(ByVal strName As String) As tbListViewEx
        Dim imgListSmall As System.Windows.Forms.ImageList = Nothing
        Dim imgListLarge As System.Windows.Forms.ImageList = Nothing
        Dim lvw As tbListViewEx = Nothing

        Me.InitImageList(imgListSmall, imgListLarge)

        lvw = Me.CreateListView(imgListSmall, imgListLarge)
        lvw.Name = strName

        'Me.pnlContainer.Controls.Add(lvw)

        Return lvw
    End Function

    Private Sub Remove(ByVal folderID As String)
        Dim lvw As tbListViewEx = Me.pnlContainer.Controls(folderID)
        If lvw IsNot Nothing Then
            Me.pnlContainer.Controls.Remove(lvw)

            lvw.Dispose()
            lvw = Nothing
        End If
    End Sub

#End Region

#Region "--- 内部函数 ---"

    Private Function AddPhotoListViewItem(ByVal imgItem As IPodImage) As ListViewItem
        Dim ithmbFormat As IPodImageFormat = imgItem.iThumbnailFormat
        Dim srcFormat As IPodImageFormat = imgItem.SrcImageFormat
        Dim strPath As String = String.Empty
        Dim info As iPhoneFileInfo = Nothing
        Dim item As ListViewItem = Nothing

        If ithmbFormat IsNot Nothing AndAlso srcFormat IsNot Nothing Then
            If srcFormat.Width = 0 AndAlso srcFormat.Height = 0 Then
                strPath = srcFormat.iThmbPathOnPhone
                Me.mDevice.IsDirectory(Me.mPhotoDB.iPod.MobileAfcHandle, strPath, info)
            Else
                strPath = imgItem.Id
            End If

            item = Me.CreateListViewItem_4X(strPath, info, Me.mCurrentView = View.LargeIcon, True)
            item.SubItems(0).Tag = ithmbFormat
            item.SubItems(2).Tag = imgItem

            If String.IsNullOrEmpty(item.SubItems(1).Text) AndAlso _
               File.Exists(srcFormat.iThmbPathOnPcTemp) Then

                Dim fInfo As New FileInfo(srcFormat.iThmbPathOnPcTemp)
                item.SubItems(1).Text = Utility.FormatFileSize(fInfo.Length)
                item.SubItems(1).Tag = fInfo.Length
            End If
        End If

        Return item
    End Function

    Private Function AddPhotoListViewItem_5X(ByVal pItem As PhotoItem) As ListViewItem
        Dim item As ListViewItem = Me.CreateListViewItem_5X(pItem, Me.mCurrentView = View.LargeIcon)
        item.SubItems(2).Tag = pItem

        If String.IsNullOrEmpty(item.SubItems(1).Text) AndAlso _
           File.Exists(pItem.FilePathOnPC) Then

            Dim fInfo As New FileInfo(pItem.FilePathOnPC)
            item.SubItems(1).Text = Utility.FormatFileSize(fInfo.Length)
            item.SubItems(1).Tag = fInfo.Length
        End If

        Return item
    End Function

    Private Function AddSubPhotoTreeNode(ByVal folder As ImageAlbum) As tbTreeNode
        Dim node As tbTreeNode = Nothing
        If Me.InvokeRequired Then
            node = Me.Invoke(New AddTreeNodeByNewPhotoLibHandler(AddressOf AddSubPhotoTreeNode), folder)
        Else
            node = Me.mPhotoLibNode.ChildNodes.Add("PhotoLib." & folder.FolderID, folder.Title, False, False)
            Dim lvw As tbListViewEx = Me.AddPhotoListView(folder.FolderID)

            node.ShowDischarge = True
            node.NodeDischargeStyle = tbTreeNode.DischargeStyle.Delete
            node.NodeDischargePadding = New Padding(0, 0, 15, 0)
            node.Tag = folder
            node.RenameEnable = True
            node.NodeNumber = folder.ImageCount
            node.NodeIcon = My.Resources.tvw_photolib_2
            node.NodeIconState = ImageState.TwoState

            RemoveHandler node.TreeNodeTextValidating, AddressOf SubNode_TreeNodeTextValidating
            AddHandler node.TreeNodeTextValidating, AddressOf SubNode_TreeNodeTextValidating

            lvw.Tag = folder
        End If

        Return node
    End Function

    Private Delegate Function AddSubPhotoTreeNodeHandler_5X(ByVal folder As AlbumItem) As tbTreeNode
    Private Function AddSubPhotoTreeNode_5X(ByVal folder As AlbumItem) As tbTreeNode
        Dim node As tbTreeNode = Nothing
        If Me.InvokeRequired Then
            node = Me.Invoke(New AddSubPhotoTreeNodeHandler_5X(AddressOf AddSubPhotoTreeNode_5X), folder)
        Else
            If String.IsNullOrEmpty(folder.Title) Then
                If folder.Kind = pAlbumKind.AllImport Then
                    folder.Title = Me.Language.GetString("Photo.Label.AllImportPhoto")          '"所有导入的照片"
                ElseIf folder.Kind = pAlbumKind.NewImport Then
                    folder.Title = Me.Language.GetString("Photo.Label.NewImportPhoto")          '"最新导入的照片"
                End If
            End If
            Dim strNodeKey As String = "PhotoLib." & folder.ID
            node = Me.mPhotoLibNode.ChildNodes(strNodeKey)
            If node Is Nothing Then
                node = Me.mPhotoLibNode.ChildNodes.Add(strNodeKey, folder.Title, False, False)
            End If

            Dim lvw As tbListViewEx = Me.AddPhotoListView(folder.ID)

            node.Tag = folder
            node.NodeNumber = folder.Count
            node.NodeIcon = My.Resources.tvw_photolib_2
            node.NodeIconState = ImageState.TwoState

            If folder.Kind <> pAlbumKind.AllImport AndAlso folder.Kind <> pAlbumKind.NewImport Then
                If Me.mDevice.VersionNumber < 600 OrElse folder.Kind = pAlbumKind.AlbumPC Then
                    node.ShowDischarge = True
                    node.NodeDischargeStyle = tbTreeNode.DischargeStyle.Delete
                    node.NodeDischargePadding = New Padding(0, 0, 15, 0)
                    node.RenameEnable = True
                End If
            End If

            RemoveHandler node.TreeNodeTextValidating, AddressOf SubNode_TreeNodeTextValidating
            AddHandler node.TreeNodeTextValidating, AddressOf SubNode_TreeNodeTextValidating

            lvw.Tag = folder
        End If

        Return node
    End Function

    Private Delegate Function AddTreeNodeByNewPhotoLibHandler(ByVal folder As ImageAlbum) As tbTreeNode
    Private Function AddTreeNodeByNewPhotoLib(ByVal folder As ImageAlbum) As tbTreeNode
        Dim node As tbTreeNode = Nothing
        If Me.InvokeRequired Then
            node = Me.Invoke(New AddTreeNodeByNewPhotoLibHandler(AddressOf AddTreeNodeByNewPhotoLib), folder)
        Else
            Dim strName As String = Me.Language.GetString("Photo.Label.NewGallery")         '"New Gallery"
            '添加之前先删除节点
            Dim nodeNew As tbTreeNode = Nothing
            For Each Item As tbTreeNode In Me.mPhotoLibNode.ChildNodes
                If Item.Name = "PhotoLib.New" Then
                    nodeNew = Item
                    Exit For
                End If
            Next

            If nodeNew IsNot Nothing Then
                Me.mPhotoLibNode.ChildNodes.Remove(nodeNew)
            End If

            node = Me.mPhotoLibNode.ChildNodes.Add("PhotoLib.New", strName, False, False)
            Dim lvw As tbListViewEx = Me.AddPhotoListView("PhotoLib.New")

            With node
                .Tag = Nothing
                .RenameEnable = True
                .ShowDischarge = False
                .NodeDischargePadding = New Padding(0, 0, 15, 0)
                .ForeColor = Color.FromArgb(101, 129, 168)
                .NodeIcon = GuiHelper.GetMouseStateImage(GuiResource.tvw_node_new, 0, 3)
                .NodeIconState = ImageState.OneState
            End With

            RemoveHandler node.TreeNodeTextValidating, AddressOf SubNode_TreeNodeTextValidating
            RemoveHandler node.TreeNodeAfterEdited, AddressOf SubNode_TreeNodeAfterEdited

            AddHandler node.TreeNodeTextValidating, AddressOf SubNode_TreeNodeTextValidating
            AddHandler node.TreeNodeAfterEdited, AddressOf SubNode_TreeNodeAfterEdited

            lvw.Tag = Nothing
        End If

        Return node
    End Function

    Public Sub SubNode_TreeNodeAfterEdited(ByVal sender As Object, ByVal e As tbTreeView_DischargeButtonClick_EventArgs)
        If e.Cancel Then
            Me.tvwDevice.SelectedNode = Me.mPhotoLibNode
        End If
    End Sub

    '修改图库名称事件
    Public Sub SubNode_TreeNodeTextValidating(ByVal sender As Object, ByVal e As tbTreeNodeCancelEventArgs)

        '新增照片图库处理事件
        Dim node As tbTreeNode = CType(sender, tbTreeNode)
        If node.Tag Is Nothing AndAlso node.Name = "PhotoLib.New" AndAlso e.NewText <> Me.Language.GetString("Photo.Label.NewGallery") Then
            Dim strNewFolderName As String = Me.GetNewFolderName()
            Dim nodeSelect As tbTreeNode = Nothing
            Me.tvwDevice.SuspendLayout()
            If Me.mDevice.VersionNumber >= 500 Then
                If Me.mPhotoThumb.ContainsFolder(e.NewText) Then
                    tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Photo.Message.GalleryAlreadyExist"), e.NewText))             '"{0} 图库分类已经存在，请重新输入"
                    e.Cancel = True
                    Me.tvwDevice.ResumeLayout()
                    Return
                End If

                Dim folder As AlbumItem = Me.mPhotoThumb.AddFolder(strNewFolderName)
                folder.Title = e.NewText
                nodeSelect = Me.AddSubPhotoTreeNode_5X(folder)
            Else
                If Me.mPhotoDB.ContainsFolder(e.NewText) Then
                    tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Photo.Message.GalleryAlreadyExist"), e.NewText))             '"{0} 图库分类已经存在，请重新输入"
                    e.Cancel = True
                    Me.tvwDevice.ResumeLayout()
                    Return
                End If

                Dim folder As ImageAlbum = Me.mPhotoDB.AddFolder(strNewFolderName)
                folder.Title = e.NewText
                nodeSelect = Me.AddSubPhotoTreeNode(folder)
            End If

            node.Parent.ChildNodes.Remove(node)
            'node.Dispose()
            'node = Nothing

            Me.AddTreeNodeByNewPhotoLib(Nothing)
            Me.tvwDevice.ResumeLayout()
            Me.tvwDevice.SelectedNode = nodeSelect
            Me.tvwDevice.Refresh()
            Return
        End If

        '修改列表名称处理
        If Me.mDevice.VersionNumber >= 500 Then
            If Me.mPhotoThumb.ContainsFolder(e.NewText) Then
                tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Photo.Message.GalleryAlreadyExist"), e.NewText))                '"{0} 图库分类已经存在，请重新输入"
                e.Cancel = True
            Else
                Dim folder As AlbumItem = CType(sender, tbTreeNode).Tag
                If folder IsNot Nothing Then
                    Me.mPhotoThumb.RenameFolder(folder, e.NewText)
                    If folder.Operation <> pChangeType.Add Then
                        Try
                            Dim thr As New Thread(AddressOf Sync)
                            With thr
                                .IsBackground = True
                                .Start()
                            End With
                        Catch ex As Exception
                        End Try

                    End If
                End If

            End If

        Else
            If Me.mPhotoDB.ContainsFolder(e.NewText) Then
                tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Photo.Message.GalleryAlreadyExist"), e.NewText))                '"{0} 图库分类已经存在，请重新输入"
                e.Cancel = True
            Else
                Dim folder As ImageAlbum = CType(sender, tbTreeNode).Tag
                If folder IsNot Nothing Then
                    Me.mPhotoDB.RenameFolder(folder.FolderID, e.NewText)
                    If folder.Operation <> pChangeType.Add Then
                        Try
                            Dim thr As New Thread(AddressOf Sync)
                            With thr
                                .IsBackground = True
                                .Start()
                            End With
                        Catch ex As Exception
                        End Try
                    End If
                End If

            End If

        End If
    End Sub

    Private Sub NewPhotoFolder(ByVal strName As String)
        If Me.mDevice.VersionNumber >= 500 Then
            Dim folder As AlbumItem = Me.mPhotoThumb.AddFolder(strName)
            Me.AddSubPhotoTreeNode_5X(folder)
        Else
            Dim folder As ImageAlbum = Me.mPhotoDB.AddFolder(strName)
            Me.AddSubPhotoTreeNode(folder)
        End If
    End Sub

    Private Sub DeletePhotoFolder(ByVal node As tbTreeNode)
        If Not Me.CheckSupport(OpType.Del, ImportSource.File, Nothing, node) Then
            Return
        End If

        '8.3及经上的固件提示删除有风险
        If Me.mDevice.VersionNumber >= 830 Then
            If Me.Check830PhotoLibDelete(True) Then
                Return
            End If
        End If

        If Me.mDevice.VersionNumber >= 500 Then
            Dim folder As AlbumItem = node.Tag
            '"您确定要删除 {0} 图库分类吗？"
            If tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Photo.Message.SureDeleteGallery"), folder.Title), _
                               Me.Language.GetString("Common.Info"), _
                               MessageBoxButtons.OKCancel, _
                               MessageBoxIcon.Warning, _
                               MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Cancel Then

                Return
            End If

            Me.pnlContainer.Controls.RemoveByKey(folder.ID.ToString())
            Me.mPhotoThumb.RemoveFolder(folder)

        Else
            Dim folder As ImageAlbum = node.Tag
            '"您确定要删除 {0} 图库分类吗？"
            If tbMessageBox.Show(Me, String.Format(Me.Language.GetString("Photo.Message.SureDeleteGallery"), folder.Title), _
                               Me.Language.GetString("Common.Info"), _
                               MessageBoxButtons.OKCancel, _
                               MessageBoxIcon.Warning, _
                               MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.Cancel Then

                Return
            End If

            Me.pnlContainer.Controls.RemoveByKey(folder.FolderID.ToString())
            Me.mPhotoDB.RemoveFolder(folder.FolderID)
        End If

        node.Remove()
        If Me.tvwDevice.SelectedNode Is node OrElse Me.tvwDevice.SelectedNode Is Nothing Then
            Me.tvwDevice.SelectedNode = Me.mPhotoLibNode
        End If
        Application.DoEvents()

        Me.Sync(True)
    End Sub

    Private Sub AddPhoto()
        If Not Me.CheckSupport(OpType.Upload) Then
            Return
        End If

        Dim fileDlg As OpenFileDialog = New OpenFileDialog()
        fileDlg.FileName = Me.Language.GetString("Music.OpenFileDialog.SelectPhoto")        '"选择图片"
        fileDlg.Multiselect = True
        fileDlg.Title = Me.Language.GetString("Media.Button.Import")                        '"导入"

        '暂时不开放5.X照片略为传视频文件。
        If Me.mDevice.VersionNumber >= 500 Then
            fileDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") & "(*.PNG;*.JPG;*.MOV;*.MP4;*.M4V;*.JPEG;*.BMP;*.GIF;*.HEIC)|*.PNG;*.JPG;*.MOV;*.MP4;*.M4V;*.JPEG;*.BMP;*.GIF;*.HEIC" '图片文件
        Else
            fileDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") & "(*.PNG;*.JPG;*.JPEG;*.GIF;*.BMP)|*.PNG;*.JPG;*.JPEG;*.GIF;*.BMP" '图片文件
        End If

        If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
            For Each Item As String In fileDlg.FileNames
                If CheckNotSupportHeic(Item) Then
                    Return
                End If
            Next
            Me.AddPhotoOK(fileDlg.FileNames)
        End If
    End Sub

    Private Sub AddPhoto(ByVal state As Object)
        Dim arrFiles As String() = state
        Me.AddPhotoOK(arrFiles)
    End Sub

    Private Sub AddPhotoOK(ByVal arrFiles As String(), Optional ByVal dicFolders As Dictionary(Of String, List(Of String)) = Nothing, Optional ByVal blnWallpaperLoad As Boolean = False)
        '要同步的时候拖图片进来会出错，同步的时候不让用户操作
        If Me.mSyncPhoto Then
            Return
        End If

        Dim blnSyncByself As Boolean = False
        If Me.tvwDevice.SelectedNode Is Me.mPhotoLibNode Then
            Me.mPhotoLibNode.InSync = True
            blnSyncByself = True
        End If

        Try
            Dim objParas As Object = New Object() {arrFiles, dicFolders, blnWallpaperLoad, blnSyncByself}
            Dim threadAddPhotoOK As New Thread(New ParameterizedThreadStart(AddressOf AddPhotoOKThread))

            Me.mLstThread.Add(threadAddPhotoOK)

            With threadAddPhotoOK
                .IsBackground = True
                .Start(objParas)
            End With
        Catch ex As Exception
            Common.Log("AddPhotoThread Error:" & ex.ToString())
        End Try

    End Sub

    Private Sub AddPhotoOKThread(ByVal objParas As Object)
        Try
            Me.SetDeviceNodeEnable(False)
            If Me.mblnImporting Then
                If Me.mPhotoQueue Is Nothing Then
                    Me.mPhotoQueue = New Queue(Of Object)
                End If

                Me.mPhotoQueue.Enqueue(objParas)
                Return
            End If

            Me.mblnImporting = True

            Dim objs As Object() = objParas
            If objs.Length <> 4 Then
                Return
            End If

            Dim arrFiles As String() = objs(0)
            Dim dicFolders As Dictionary(Of String, List(Of String)) = objs(1)
            Dim blnWallpaperLoad As Boolean = objs(2)
            Dim blnSyncByself As Boolean = objs(3)

            Me.AddPhotoOKAddItem(arrFiles, dicFolders, blnWallpaperLoad)
            Me.AddPhotoOKSync(blnSyncByself)

            Me.mblnImporting = False

            If Me.mPhotoQueue IsNot Nothing AndAlso Me.mPhotoQueue.Count > 0 Then
                Me.AddPhotoOKThread(Me.mPhotoQueue.Dequeue)
            End If
            Me.SetDeviceNodeEnable(True)
        Catch ex As Exception
        End Try
    End Sub

    Private Delegate Function ShowCreateFolderFormHandler(ByVal arg As CreateFolderEventArgs) As Boolean
    Private Function ShowCreateFolderForm(ByVal arg As CreateFolderEventArgs) As Boolean
        Dim blnReturn As Boolean = True
        If Me.InvokeRequired Then
            blnReturn = Me.Invoke(New ShowCreateFolderFormHandler(AddressOf ShowCreateFolderForm), arg)
        Else
            Dim photoOrderForm As New frmPhotoOrder(Me.mApplication, Me.Language.GetString("Common.Info"), arg.ShowCreatePhotoLibMessage)
            If arg.WallpaperLoad = False Then
                '提示用户排序规则，是否要为文件夹创建图库
                If arg.Count > 1 OrElse (arg.Folders IsNot Nothing AndAlso arg.Folders.Count > 0) Then
                    photoOrderForm.ShowDialog(Me.mApplication)
                    '如果用户关闭窗体的时候就不上传
                    If photoOrderForm.DialogResult <> Windows.Forms.DialogResult.OK Then
                        Return False
                    End If
                End If
            End If
            arg.OrderbyTime = photoOrderForm.rbtnOrderByTime.Checked
            arg.CreatePhotoLib = photoOrderForm.cbxCreatePhotoLib.Checked
        End If

        Return blnReturn
    End Function

    '清空上一次操作的提示信息
    Private Delegate Sub ClearMessageHandler()
    Private Sub ClearMessage()
        If Me.InvokeRequired Then
            Me.Invoke(New ClearMessageHandler(AddressOf ClearMessage))
        Else
            Me.lblCurrentFile.Text = ""
            Me.pgbProgress.tbPlayValue = 0
        End If
    End Sub

    Private Sub AddPhotoOKAddItem(ByVal arrFiles As String(), ByVal dicFolders As Dictionary(Of String, List(Of String)), ByVal blnWallpaperLoad As Boolean)
        Dim listItem As New List(Of String)
        Dim folder As Object = Me.tvwDevice.SelectedNode.Tag
        Dim listPhoto As New List(Of Object)
        Dim lvw As tbListViewEx = Nothing
        Dim lstLvwItemFiles As New List(Of ListViewItem)
        Dim blnShowCreatePhotoLibMessage As Boolean = False

        If dicFolders IsNot Nothing AndAlso dicFolders.Count > 0 Then
            blnShowCreatePhotoLibMessage = True
        End If

        Me.SetControlStatus(False)

        'Debug.Print("AAAAA:" & Me.tvwDevice.SelectedNode.Text)

        Try
            Dim index As Integer = 0
            Dim count As Integer = 0
            Dim dictOrderByTime As New Dictionary(Of String, DateTime)
            '统计要上传的图片总共的多少张
            Dim arg As New CreateFolderEventArgs

            arg.Folders = dicFolders
            arg.ShowCreatePhotoLibMessage = blnShowCreatePhotoLibMessage
            arg.WallpaperLoad = blnWallpaperLoad
            arg.Count = arrFiles.Length

            If Not Me.ShowCreateFolderForm(arg) Then
                Return
            End If
            Me.ClearMessage()
            Me.UpdateTranslateStatus(False)

            If arg.OrderbyTime AndAlso arrFiles.Length > 1 Then
                arrFiles = Me.PhotoOrderByTime(arrFiles).ToArray()
            End If

            If dicFolders IsNot Nothing AndAlso dicFolders.Count > 0 Then
                For Each Item As List(Of String) In dicFolders.Values
                    count += Item.Count
                Next

                Dim newNode As tbTreeNode = Nothing
                For Each filderKey As String In dicFolders.Keys
                    Dim lstPhotoFiles As New List(Of String)

                    If arg.OrderbyTime = True Then
                        lstPhotoFiles = Me.PhotoOrderByTime(dicFolders(filderKey).ToArray())
                    Else
                        lstPhotoFiles = dicFolders(filderKey)
                    End If
                    '添加图片到图库目录下
                    If Me.mDevice.VersionNumber >= 500 Then
                        If arg.CreatePhotoLib Then
                            Dim newFolder As AlbumItem = Me.CreateFolder_5X(filderKey)
                            newNode = Me.AddSubPhotoTreeNode_5X(newFolder)
                            Me.AddTreeNodeByNewPhotoLib(Nothing)

                            Me.AddPhotoToFolder_5X(listPhoto, lstPhotoFiles.ToArray, newFolder, count, index)
                            folder = newFolder
                            newNode.NodeNumber = newFolder.Count
                            Utility.WaitSeconds(0.5)
                        Else
                            Me.AddPhotoToFolder_5X(listPhoto, lstPhotoFiles.ToArray, folder, count, index)
                            Me.UpdatePhotoLibPicCount(folder)

                        End If
                    Else
                        If arg.CreatePhotoLib Then
                            Dim newFolder As ImageAlbum = Me.CreateFolder(filderKey)
                            newNode = Me.AddSubPhotoTreeNode(newFolder)
                            Me.AddTreeNodeByNewPhotoLib(Nothing)

                            Me.AddPhotoToFolder(listPhoto, lstPhotoFiles.ToArray, newFolder, count, index)
                            folder = newFolder
                            newNode.NodeNumber = newFolder.ImageCount
                            Utility.WaitSeconds(0.5)
                        Else
                            Me.AddPhotoToFolder(listPhoto, lstPhotoFiles.ToArray, folder, count, index)
                            Me.UpdatePhotoLibPicCount(folder)

                        End If
                    End If
                Next

                If newNode IsNot Nothing Then
                    Me.SetSelectNode(newNode)
                    Application.DoEvents()
                End If

            Else
                If Me.mDevice.VersionNumber >= 500 Then
                    Me.AddPhotoToFolder_5X(listPhoto, arrFiles, folder, count, index)
                    Me.UpdatePhotoLibPicCount(folder)

                Else
                    Me.AddPhotoToFolder(listPhoto, arrFiles, folder, count, index)
                    Me.UpdatePhotoLibPicCount(folder)

                End If
            End If

            If Me.mDevice.VersionNumber >= 500 Then
                If Me.mButtonSelect = ButtonSelect.Camera Then
                    lvw = Me.mCameraListView

                ElseIf Me.mButtonSelect = ButtonSelect.Screenshot Then
                    lvw = Me.mScreenShotListView

                ElseIf Me.mButtonSelect = ButtonSelect.Video Then
                    lvw = Me.mVideoListView

                ElseIf Me.tvwDevice.SelectedNode.Name = "PhotoLib" Then
                    lvw = Me.mPhotoLibListView

                Else
                    If Me.mDevice.VersionNumber >= 500 Then
                        lvw = Me.pnlContainer.Controls.Item(folder.ID.ToString())
                    Else
                        lvw = Me.pnlContainer.Controls.Item(folder.FolderID.ToString())
                    End If

                End If

                For Each imgItem As PhotoItem In listPhoto
                    If imgItem IsNot Nothing Then
                        lstLvwItemFiles.Add(Me.AddPhotoListViewItem_5X(imgItem))
                    End If
                Next

            Else
                If Me.tvwDevice.SelectedNode.Name = "PhotoLib" Then
                    lvw = Me.mPhotoLibListView
                Else
                    lvw = Me.pnlContainer.Controls.Item(folder.FolderID.ToString())
                End If

                For Each imgItem As IPodImage In listPhoto
                    If imgItem IsNot Nothing Then
                        lstLvwItemFiles.Add(Me.AddPhotoListViewItem(imgItem))
                    End If
                Next
            End If

            '统计总的数量
            If folder IsNot Me.mPhotoLibNode.Tag Then
                folder = Me.mPhotoLibNode.Tag
                Me.UpdatePhotoLibPicCount(folder)
            End If

            If lvw Is Nothing Then
                Return
            End If

            Common.Log("AddItemToListView")
            Me.AddItemToListView(lvw, lstLvwItemFiles, True)

            Me.ShowFileEmpty(lvw.Items.Count)

            Application.DoEvents()

            '使用线程加载界面，否则会被UI卡，无法执行下面导入照片逻辑

            'If Me.mButtonSelect = ButtonSelect.Camera Then
            '    Me.UpdateThumbnailCamera(lvw)
            'ElseIf Me.mButtonSelect = ButtonSelect.Vedio Then
            '    Me.UpdateThumbnailVedio(lvw)
            'Else
            '    Common.Log("UpdateThumbnailPhotoLib")
            '    Me.UpdateThumbnailPhotoLib(lvw)
            'End If

            Select Case Me.mButtonSelect
                Case ButtonSelect.Camera
                    Dim thd As New Thread(New ParameterizedThreadStart(AddressOf UpdateThumbnailCamera))
                    thd.IsBackground = True
                    thd.Name = "UpdateThumbnailCamera1"
                    thd.Start(New Object() {lvw, Me.mButtonSelect})

                Case ButtonSelect.Screenshot
                    Dim thd As New Thread(New ParameterizedThreadStart(AddressOf UpdateThumbnailCamera))
                    thd.IsBackground = True
                    thd.Name = "UpdateThumbnailScreenShot1"
                    thd.Start(New Object() {lvw, Me.mButtonSelect})

                Case ButtonSelect.Video
                    Dim thd As New Thread(New ParameterizedThreadStart(AddressOf UpdateThumbnailCamera))
                    thd.IsBackground = True
                    thd.Name = "UpdateThumbnailVedio1"
                    thd.Start(New Object() {lvw, Me.mButtonSelect})

                Case Else
                    Dim thd As New Thread(New ParameterizedThreadStart(AddressOf UpdateThumbnailPhotoLib))
                    thd.IsBackground = True
                    thd.Name = "UpdateThumbnailPhotoLib1"
                    thd.Start(lvw)

            End Select

            Common.Log("UpdateListViewItemCount")
            Me.UpdateListViewItemCount(lvw.Items.Count)

        Catch ex As Exception
            Common.Log(ex.Message & vbCrLf & _
                       "folder Is Nothing：" & (folder Is Nothing).ToString() & vbCrLf & _
                       "lvw Is Nothing：" & (lvw Is Nothing).ToString() & vbCrLf & _
                       "listPhoto Count：" & listPhoto.Count.ToString() & vbCrLf & _
                       "lstLvwItemFiles Count：" & lstLvwItemFiles.Count.ToString())
        End Try

    End Sub
    Private Delegate Sub SetSelectNodeHandler(ByVal node As tbTreeNode)
    Private Sub SetSelectNode(ByVal node As tbTreeNode)
        If Me.InvokeRequired Then
            Me.Invoke(New SetSelectNodeHandler(AddressOf SetSelectNode), node)
        Else
            Me.tvwDevice.SelectedNode = node
        End If
    End Sub

    Private Sub AddPhotoOKSync(ByVal blnSyncByself As Boolean)
        Me.UpdateButtonAbortStatus(True)

        If blnSyncByself Then
            Me.mPhotoLibNode.InSync = False
        End If

        Me.Sync()

        Me.SetControlStatus(True)
        Me.UpdateButtonStatus()
        Me.UpdateButtonAbortStatus(False)
        Me.UpdateTranslateStatus(True)

    End Sub

    '更新当前图库下总数
    Private Delegate Sub UpdatePhotoLibPicCountHandler(ByVal folder As Object)
    Private Sub UpdatePhotoLibPicCount(ByVal folder As Object)
        If Me.InvokeRequired Then
            Me.Invoke(New UpdatePhotoLibPicCountHandler(AddressOf UpdatePhotoLibPicCount), folder)
        Else
            If folder IsNot Nothing Then
                If Me.mDevice.VersionNumber >= 500 Then
                    If folder IsNot Me.mPhotoLibNode.Tag Then
                        Me.tvwDevice.Nodes("PhotoLib." & folder.ID).NodeNumber = folder.Count
                    Else
                        Me.tvwDevice.Nodes("PhotoLib").NodeNumber = folder.Count
                    End If
                Else
                    If folder IsNot Me.mPhotoLibNode.Tag Then
                        Me.tvwDevice.Nodes("PhotoLib." & folder.FolderID).NodeNumber = folder.ImageCount
                    Else
                        Me.tvwDevice.Nodes("PhotoLib").NodeNumber = folder.ImageCount
                    End If
                End If
            End If
        End If
    End Sub

    Private Function PhotoOrderByTime(ByVal arrFiles As String()) As List(Of String)
        Dim lstReturn As New List(Of String)
        Dim dictOrderByTime As New Dictionary(Of String, Long)
        For Each Item As String In arrFiles
            '取得照片爆光时间
            Dim picReader As New ExifReader
            Dim picInfo As JPEGInfo = picReader.Reader(Item)
            Dim timeTicks As Long = 0
            Try
                If picInfo.orgTime IsNot Nothing AndAlso picInfo.orgTime.Length > 0 Then
                    timeTicks = DateTime.Parse(picInfo.orgTime).Ticks
                ElseIf picInfo.createTime IsNot Nothing AndAlso picInfo.createTime.Length > 0 Then
                    timeTicks = DateTime.Parse(picInfo.createTime).Ticks
                End If
            Catch ex As Exception
            End Try

            If timeTicks <= 0 Then
                '如果爆光时间取不到就取文件创建时间
                Dim fileInfo As New FileInfo(Item)
                timeTicks = fileInfo.LastWriteTime.Ticks
            End If

            dictOrderByTime.Add(Item, timeTicks)

            picReader.Dispose()
            picReader = Nothing
        Next

        Dim strFileTemp As String = ""
        For i As Integer = 0 To arrFiles.Length - 2
            For j As Integer = i + 1 To arrFiles.Length - 1
                If dictOrderByTime(arrFiles(i)) > dictOrderByTime(arrFiles(j)) Then
                    strFileTemp = arrFiles(i)
                    arrFiles(i) = arrFiles(j)
                    arrFiles(j) = strFileTemp
                End If
            Next
        Next

        For Each Item As String In arrFiles
            lstReturn.Add(Item)
        Next

        Return lstReturn
    End Function

    Private Function CreateFolder(ByVal strFolderName As String) As ImageAlbum
        Dim strNewFolderName As String = strFolderName
        Dim intCount As Integer = 0
        Dim returnFolder As ImageAlbum
        If Me.mPhotoDB.ContainsFolder(strNewFolderName) Then
            Do
                intCount = intCount + 1
                strNewFolderName = String.Format("{0}({1})", strFolderName, intCount)
                If Me.mPhotoDB.ContainsFolder(strNewFolderName) = False Then
                    Exit Do
                End If
            Loop
        End If
        returnFolder = Me.mPhotoDB.AddFolder(strNewFolderName)
        Return returnFolder
    End Function

    Private Function CreateFolder_5X(ByVal strFolderName As String) As AlbumItem
        Dim strNewFolderName As String = strFolderName
        Dim intCount As Integer = 0
        Dim returnFolder As AlbumItem
        If Me.mPhotoThumb.ContainsFolder(strNewFolderName) Then
            Do
                intCount = intCount + 1
                strNewFolderName = String.Format("{0}({1})", strFolderName, intCount)
                If Me.mPhotoThumb.ContainsFolder(strNewFolderName) = False Then
                    Exit Do
                End If
            Loop
        End If
        returnFolder = Me.mPhotoThumb.AddFolder(strNewFolderName)
        Return returnFolder
    End Function

    Private Sub AddPhotoToFolder(ByRef listPhoto As List(Of Object), ByVal lstPhotoFiles As String(), ByVal folder As ImageAlbum, ByVal intCount As Integer, ByRef intIndex As Integer)
        Me.mIsUpload = True
        For Each strFile As String In lstPhotoFiles
            Me.ShowProgress(System.IO.Path.GetFileName(strFile), intIndex, intCount, ProgressAction.CreateThumbnail)

            Application.DoEvents()
            Dim isOrderbyTime As Boolean = False
            listPhoto.Add(Me.mPhotoDB.AddPhoto(folder, strFile))
            intIndex += 1
        Next
        Me.ShowProgress("", 0, 0, ProgressAction.Prepair)
    End Sub

    Private Sub AddPhotoToFolder_5X(ByRef listPhoto As List(Of Object), ByVal lstPhotoFiles As String(), ByVal folder As AlbumItem, ByVal intCount As Integer, ByRef intIndex As Integer)
        Me.mIsUpload = True
        For Each strFile As String In lstPhotoFiles
            Me.ShowProgress(System.IO.Path.GetFileName(strFile), intIndex, lstPhotoFiles.Length, ProgressAction.CreateThumbnail)

            Application.DoEvents()
            listPhoto.Add(Me.mPhotoThumb.AddPhoto(strFile, folder, Me.mCameraListView.LargeImageList.ImageSize))
            intIndex += 1
        Next
        Me.ShowProgress("", 0, 0, ProgressAction.Prepair)
    End Sub

    Private Function CheckSupport(Optional ByVal pType As OpType = OpType.None, Optional ByVal sources As ImportSource = ImportSource.File, Optional ByVal arr As Array = Nothing, Optional ByVal deleteNode As tbTreeNode = Nothing) As Boolean
        Try
            '2015-06-24 by chenbihai 
            If (arr IsNot Nothing OrElse pType = OpType.Upload) AndAlso Me.CheckSupportiOS8PhotoLib() Then
                If Me.mButtonSelect = ButtonSelect.PhotoLib Then
                    Dim msg As String = Me.Language.GetString("Photo.Message.DelectErrorMessage") & vbCrLf & vbCrLf & Me.Language.GetString("Photo.Message.ImportAlert")
                    If tbMessageBox.Show(Me, msg, Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) = Windows.Forms.DialogResult.OK Then

                    Else
                        Return False
                    End If
                End If
            End If

            If Me.mDevice.VersionNumber >= 800 AndAlso iTunesHelper.iTunesVersion < New Version("11.2.0.114") Then
                '"您的iTunes版本过低,请下载最新版的iTunes。"
                If tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.iTunesIsTooLow"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = Windows.Forms.DialogResult.OK Then
                    Common.OpenExplorer(WebUrl.PageiTunesDownload)       '"http://www.apple.com/cn/itunes/download/"
                End If
                Return False
            End If

            If Me.mDevice.VersionNumber >= 600 Then
                If Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Screenshot Then
                    ''"此设备尚未支持，即将开放，敬请期待！！"
                    'If pType = OpType.Upload OrElse pType = OpType.Del Then
                    '    '"暂不支持上传图片到iOS6设备的照相机目录中，您可以上传到照片图库。"
                    '    'tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.NoSupportCamera"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                    '    Return True

                    'ElseIf pType = OpType.Sync Then
                    '    Return True

                    '    'ElseIf pType = OpType.Del Then
                    '    '    '"暂不支持删除iOS6设备照相机目录中的图片，您可以在设备上直接删除。"
                    '    '    tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.NoSupportDeleteCamera"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                    'End If
                    'Return False
                    If pType = OpType.None Then
                        Return False
                    Else
                        Return True
                    End If
                ElseIf Me.mButtonSelect = ButtonSelect.Video AndAlso Not OSHelper.OverWin7 Then
                    '"暂不支持管理iOS6设备的视频。"
                    tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.NoSupportVideo"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Return False
                End If

                If Me.mPhotoThumb.NeedRepair Then
                    ''"照片图库的数据有异常，为了避免图片丢失，需要进行修复，点""确定""立即修复。"
                    'If tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.GalleryDataMistake"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                    '    Me.Repair()
                    '    Me.OnRefresh()
                    'End If
                    'http://bbs.tongbu.com/thread-89362-1-1.html
                    '"照片图库的数据有异常，请根据教程进行修复！"
                    tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.PhotoLibException"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1)
                    Common.OpenExplorer(WebUrl.PagePhotoRepair)
                    Return False
                End If
            End If

            If Me.mDevice.VersionNumber >= 500 AndAlso Me.mDevice.VersionNumber < 600 AndAlso System.IO.File.Exists(Path.Combine(Folder.AppFolder, "tbPhoto.dll")) Then
                If Me.mPhotoThumb.NeedRepair Then
                    '"照片图库的数据有异常，为了避免图片丢失，需要进行修复，点""确定""立即修复。"
                    If tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.GalleryDataMistake"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.OK Then
                        Me.Repair()
                        Me.OnRefresh()
                    End If
                    Return False
                End If
            End If

            If Me.mDevice.VersionNumber >= 600 AndAlso Me.mButtonSelect = ButtonSelect.PhotoLib AndAlso (pType = OpType.Upload OrElse pType = OpType.Del) Then
                Dim node As tbTreeNode = Me.tvwDevice.SelectedNode
                If node Is Nothing OrElse node.Name = "PhotoLib.New" Then
                    Me.tvwDevice.SelectedNode = Me.mPhotoLibNode
                End If
                If deleteNode IsNot Nothing Then
                    node = deleteNode
                End If
                If node.Tag IsNot Nothing AndAlso TypeOf node.Tag Is AlbumItem Then
                    Dim folder As AlbumItem = node.Tag
                    If folder IsNot Nothing AndAlso folder.Kind = pAlbumKind.AlbumPhone Then
                        '"暂不支持修改iOS6设备上创建的相簿！"
                        tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.NoSupportDeviceGallery"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
                        Return False
                    End If
                End If
            End If

            If Me.mDevice.VersionNumber >= 500 AndAlso Me.mDevice.VersionNumber < 600 Then
                'Dim strMessage As String = "您正在操作iOS设备的【相机胶卷】,为了保证设备数据正常，" & vbCrLf & _
                '             "您必须先退出设备上的【照片】和【相机】程序，并关闭后台的【照片】和【相机】程序。" & vbCrLf & _
                '             "在操作过程中，请勿打开设备上的【照片】和【相机】程序！" & vbCrLf & _
                '             "您按照上述说明操作了吗？"

                'If MessageBox.Show(strMessage, Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Information) = Windows.Forms.DialogResult.No Then
                '    Return False
                'End If

                If pType <> OpType.Sync Then
                    'If IniSetting.GetNoShowPhotoInfo = False Then
                    If Not mNoShowInfo Then
                        Dim frmInfo As New frmPhotoInfo(Me.mApplication, Me.mDevice)
                        Dim dlg As DialogResult = frmInfo.ShowDialog()
                        mNoShowInfo = frmInfo.cbxNoShow.Checked

                        If dlg = Windows.Forms.DialogResult.Cancel Then
                            Return False
                        End If
                    End If

                    'End If
                End If
            End If

            If Me.mDevice.VersionNumber >= 500 AndAlso Me.mDevice.VersionNumber < 600 Then
                Dim dtPhotosDB As Date = Me.GetPhotosDBUpdateTime()
                Dim dtPreviewImage As Date = Me.GetPreviewImgUpdateTime()
                If dtPreviewImage > DateTime.MinValue AndAlso dtPhotosDB < dtPreviewImage.AddSeconds(-1) Then
                    '"如果您打开了设备中的【相机】程序，请按【Home】键退出！\r\n您确定已经退出【相机】程序了吗？"
                    If tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.ClosePhoto"), Me.Language.GetString("Common.Info"), MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
                        Return False
                    End If

                End If

                If Me.mPhotosDBUpdateTime <> dtPhotosDB Then
                    '"设备上的照片可能有更新，请先点击""确定""以重新加载，然后再进行同步操作。"
                    tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.PhotoUpdated"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Me.LoadData(True)

                    Return False
                End If
            End If
            Return True
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckSupport")
            Return False
        End Try
    End Function

    '上传文件到设备中
    Private Sub UploadToPhoneEx()
        '删除
        Dim blnDelete As Boolean = False
        For Each strPath As String In Me.mPhotoDB.DeletePhotos
            blnDelete = True
            Me.mDevice.DeleteFile(Me.mPhotoDB.iPod.MobileAfcHandle, strPath)
            If Me.mDevice.Jailbreaked Then
                Dim strTmpPath As String = "/var/mobile/Media" & strPath
                If Me.mDevice.Exists(strTmpPath) Then
                    Me.mDevice.DeleteFile(strTmpPath)
                End If
            End If
            Application.DoEvents()
        Next
        Me.mPhotoDB.DeletePhotos.Clear()

        If Me.mPhotoDB.iPod.DicUploadFilesPhoto.Count = 0 Then
            Return
        End If

        '初始化参数
        Me.mCancel = False
        Me.mIsUpload = True
        Me.mTotalSize = 0
        Me.mTranslateSize = 0

        Dim listUploadFailFiles As New List(Of String)
        Dim listNewItems As New List(Of ListViewItem)

        '计算文件的总大小
        Try
            For Each pair As KeyValuePair(Of String, String) In Me.mPhotoDB.iPod.DicUploadFilesPhoto
                If System.IO.File.Exists(pair.Value) Then
                    mTotalSize += New FileInfo(pair.Value).Length
                Else
                    mTotalSize += Me.CaculateSize(pair.Value)
                End If

                If Me.mCancel Then
                    GoTo DO_EXIST
                End If
            Next
        Catch ex As Exception
        End Try

        For Each pair As KeyValuePair(Of String, String) In Me.mPhotoDB.iPod.DicUploadFilesPhoto
            Dim info As iPhoneFileInfo = Nothing

            If Not Me.mDevice.CopyToPhoneByAFC(pair.Value, pair.Key, New DataTransferEventHandler(AddressOf DataTransferProgress), Nothing, Me.mCancel) Then
                '添加上传失败列表
                listUploadFailFiles.Add(pair.Key)
            End If

            '退出循环，并更新文件或者文件夹到列表中
            If Me.mCancel Then
                Exit For
            End If
        Next
DO_EXIST:
        '清除修正缓存列表
        Me.mPhotoDB.iPod.ClearPhoto()

        If mCancel = False AndAlso listUploadFailFiles.Count = 0 Then
            If blnDelete Then
                Me.UpdateMainLableText(Me.Language.GetString("Music.Message.DeleteSuccessed")) '删除成功。
            Else
                Me.UpdateMainLableText(Me.Language.GetString("RingtoneMaker.Message.ImportSucceed")) '导入成功！
            End If

        End If

        Me.ShowErrorMessage(listUploadFailFiles)

    End Sub

    Private Sub UploadToPhoneEx_5X()
        '删除
        Dim blnDelete As Boolean = False
        For Each pItem As PhotoItem In Me.mPhotoThumb.Photos
            If pItem.Operation = pChangeType.Del Then
                Me.mDevice.DeleteFileByAFC(pItem.FilePathOnPhone)
                Application.DoEvents()
                blnDelete = True
            End If
        Next

        '初始化参数
        Me.mCancel = False
        Me.mIsUpload = True
        Me.mTotalSize = 0
        Me.mTranslateSize = 0

        Dim listUploadFailFiles As New List(Of String)
        Dim listNewItems As New List(Of ListViewItem)

        '计算文件的总大小
        Try
            For Each pItem As PhotoItem In Me.mPhotoThumb.Photos
                If pItem.Operation <> pChangeType.Add Then
                    Continue For
                End If

                If System.IO.File.Exists(pItem.FilePathOnPC) Then
                    mTotalSize += New FileInfo(pItem.FilePathOnPC).Length
                Else
                    mTotalSize += Me.CaculateSize(pItem.FilePathOnPC)
                End If

                If Me.mCancel Then
                    GoTo DO_EXIST
                End If
            Next

        Catch ex As Exception
        End Try

        Me.mDevice.CheckFolderByAFC("/DCIM/100APPLE")
        Me.mDevice.CheckFolderByAFC("/PhotoData/Sync/100SYNCD")
        For Each pItem As PhotoItem In Me.mPhotoThumb.Photos
            If pItem.Operation <> pChangeType.Add Then
                Continue For
            End If

            If Not Me.mDevice.CopyToPhoneByAFC(pItem.FilePathOnPC, pItem.FilePathOnPhone, New DataTransferEventHandler(AddressOf DataTransferProgress), Nothing, Me.mCancel) Then
                '添加上传失败列表
                listUploadFailFiles.Add(pItem.FilePathOnPhone)
            End If

            '退出循环，并更新文件或者文件夹到列表中
            If Me.mCancel Then
                Exit For
            End If
        Next

DO_EXIST:
        If mCancel = False AndAlso listUploadFailFiles.Count = 0 Then
            If blnDelete Then
                Me.UpdateMainLableText(Me.Language.GetString("Music.Message.DeleteSuccessed")) '删除成功。
            Else
                Me.UpdateMainLableText(Me.Language.GetString("RingtoneMaker.Message.ImportSucceed")) '导入成功！
            End If

        End If

        Me.ShowErrorMessage(listUploadFailFiles)

    End Sub

    Private Sub ShowErrorMessage(ByVal listUploadFailFiles As List(Of String))
        If listUploadFailFiles.Count > 0 Then
            Dim strText As String = ""
            Dim intCount As Integer = 0
            For Each strPath As String In listUploadFailFiles
                intCount += 1
                strText &= vbCrLf & Path.GetFileName(strPath)

                If intCount > 10 AndAlso listUploadFailFiles.Count > intCount Then
                    strText &= vbCrLf & "..." & vbCrLf & vbCrLf
                    strText &= vbCrLf & String.Format(Me.Language.GetString("File.Message.ItemCount"), listUploadFailFiles.Count)
                    Exit For
                End If

            Next
            '"以下文件导入失败"  
            tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.FileUploadFailure") & Me.Language.GetString("Common.Symbol.Colon") & strText, _
                            Me.Language.GetString("Common.info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If
    End Sub

    Private Sub Sync(Optional ByVal objPara As Object = Nothing)
        Try
            Dim isDelete As Boolean = False
            If objPara IsNot Nothing AndAlso TypeOf objPara Is Boolean Then
                isDelete = objPara
            End If

            If Not Me.CheckSupport(OpType.Sync) Then
                Return
            End If

            Me.mSyncPhoto = True
            Me.SetControlStatus(False)
            '初始化界面控件状态
            Me.mDevice.StartSync()
            '"正在生成媒体数据，请稍后 ..."
            Me.mIsUpload = True
            Me.ShowProgress(Me.Language.GetString("Photo.Message.CreateingMediaData"), 0, 0, ProgressAction.Prepair)                '"正在生成媒体数据，请稍候 ..."
            Me.UpdateTranslateStatus(False)
            Application.DoEvents()

            If Me.mDevice.VersionNumber >= 600 Then
                If Me.mButtonSelect = ButtonSelect.Camera Then
                    Me.mPhotoThumb.SyncWithTui(Me.mTuiAFC)
                Else
                    Me.mPhotoThumb.Sync()
                    'Me.ShowProgress(Me.Language.GetString("Device.iPod.Label.CreatingMediaData"), 0, 0, ProgressAction.Prepair)
                End If

            ElseIf Me.mDevice.VersionNumber >= 500 Then
                'If Me.mDevice.VersionNumber >= 500 Then
                Me.mPhotoThumb.Save()
                Me.UploadToPhoneEx_5X()
                Me.mPhotoThumb.Clear()
                'Me.ShowProgress(Me.Language.GetString("Device.iPod.Label.CreatingMediaData"), 0, 0, ProgressAction.Prepair)

            Else
                Me.mPhotoDB.Save()
                Me.UploadToPhoneEx()
            End If
            If Me.mlstAddedWallpaper IsNot Nothing AndAlso Me.mlstAddedWallpaper.Count > 0 Then
                Me.mlstAddedWallpaper.Clear()
            End If

            '恢复可用状态
            If Me.mDevice.VersionNumber < 600 Then
                Me.ShowProgress("", mTotalSize, mTotalSize, ProgressAction.Completed)
            End If
            Me.UpdateTranslateStatus(True)
            Me.mDevice.EndSync()

            Me.SetControlStatus(True)

            Me.mSyncPhoto = False

            'If Me.mDevice.VersionNumber >= 500 AndAlso Me.mDevice.VersionNumber < 600 Then
            '    ''"如果您打开了设备中的【照片】程序，请按【Home键】退出，" & vbCrLf & "并关闭后台的【照片】程序，以更新新添加的照片！"
            '    Dim strMessage As String = Me.Language.GetString("Device.PhotoManage.Message.ClosePhoto")
            '    If Me.mDevice.VersionNumber >= 500 Then
            '        strMessage &= vbCrLf & Me.Language.GetString("Device.PhotoManage.Message.RestarDeviceIfPicObscure")     '"如果图片出现模糊，请重启设备。"
            '    End If
            '    MessageBox.Show(strMessage, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
            'End If

            '更新图片资料库的最新更新时间
            Me.UpdatePhotosUpdateTime()
            Me.UpdateButtonStatus()
        Catch ex As Exception
        End Try
    End Sub

    Private Sub Repair()
        '初始化下载界面
        Me.ShowProgress("", 0, mTotalSize, ProgressAction.Prepair)
        Me.UpdateTranslateStatus(False)
        Me.UpdateButtonAbortStatus(False)

        Me.mPhotoThumb.Repair()

        '恢复下载前界面
        Me.UpdateTranslateStatus(True)
        Me.UpdateButtonAbortStatus(False)

        Me.SetFormStatus(True)
    End Sub

#End Region

#Region "--- 内部私有函数 ---"

    '获取照片/屏幕截图的缩略图路径
    Private Function GetCameraThumnailFolder(ByVal srcCameraFolder As String, ByVal strVersion As String) As String
        Dim thumnailPath As String = srcCameraFolder.TrimEnd("/") & "/"

        If strVersion.StartsWith("4") Then
            '4.X的版本
            thumnailPath = thumnailPath.Replace("/DCIM", "/PhotoData")

        ElseIf strVersion.StartsWith("3") Then
            '3.X的版本
            thumnailPath = thumnailPath.Replace("APPLE/", "APPLE/.MISC")

        Else
            '2.X、1.X版本
        End If

        Return thumnailPath
    End Function

    '好得新的相册名称　IMG_****.JPG
    Private Function CreateCameraName() As String
        Dim strNewName As String = "IMG_{0}"
        Me.mCameraMaxIndex += 1

        If Me.mCameraMaxIndex > 999 Then
            Me.mCameraMaxIndex = 1

            Dim strCameraFolderIndex As Integer = Val(Path.GetFileName(mCameraFolder.TrimEnd("/")))
            strCameraFolderIndex += 1

            Me.mCameraFolder = Me.GetiPhoneDirectory(Me.mCameraFolder).TrimEnd("/") & String.Format("/{0}APPLE", strCameraFolderIndex)
        End If

        Return String.Format(strNewName, Me.mCameraMaxIndex.ToString().PadLeft(4, "0"))
    End Function

    '创建照片缩略图
    Private Function CreateCameraThumbnail(ByVal strFile As String, ByVal intVersion As Integer, ByRef addList As List(Of String)) As List(Of String)
        Dim listNewFiles As New List(Of String)
        'Dim str150x150 As String = Me.mTempFolder & strName & ".THM"
        '把图片先生成在临时文件夹中再上传到手机上面
        If intVersion >= 320 Then
            ''iPhone 4     生成  640*960     320*480　150*150
            'Utility.CreateThumbnail(strFile, str150x150, New Size(150, 150), True)
            'listNewFiles.Add(str150x150)
        Else
            '其它         生成  320*480　   75*75
            Dim strName As String = Me.CreateCameraName()
            Dim str75x75 As String = Path.Combine(Me.mTempFolder, strName & ".THM")

            Utility.CreateThumbnail(strFile, str75x75, New Size(75, 75), True)
            mDicCameraPics.Add(strFile, strName)

            listNewFiles.Add(str75x75)
        End If

        listNewFiles.Add(strFile)
        addList.Add(strFile)

        Return listNewFiles
    End Function

    Private Function GetNewFolderName()
        Dim strFolderName As String = Me.Language.GetString("Photo.Label.NewGallery") & "{0}"   '"新建图库{0}"
        Dim intIndex As Integer = 1

        If Me.mDevice.VersionNumber >= 500 Then
            While Me.mPhotoThumb.ContainsFolder(String.Format(strFolderName, intIndex))
                intIndex += 1
            End While
        Else
            While Me.mPhotoDB.ContainsFolder(String.Format(strFolderName, intIndex))
                intIndex += 1
            End While
        End If

        strFolderName = String.Format(strFolderName, intIndex)
        Return strFolderName
    End Function

    Private Function GetButtonSelect() As ButtonSelect
        Dim selectButton As ButtonSelect = ButtonSelect.Camera

        If Me.tvwDevice.SelectedNode IsNot Nothing Then
            Dim strName As String = Me.tvwDevice.SelectedNode.Name

            If strName = "Camera" Then
                selectButton = ButtonSelect.Camera
                'FunctionMapping.GetInstanse().AddFunctionMapping(FunctionKey.DeviceCamera)  '功能统计

            ElseIf strName = "ScreenShot" Then
                selectButton = ButtonSelect.Screenshot
                'FunctionMapping.GetInstanse().AddFunctionMapping(FunctionKey.DeviceCamera)  '功能统计

            ElseIf strName = "Video" Then
                selectButton = ButtonSelect.Video
                'FunctionMapping.GetInstanse().AddFunctionMapping(FunctionKey.DeviceCamera)  '功能统计

                'ElseIf strName = "WallPaper" Then
                '    selectButton = ButtonSelect.Wallpaper
                '    FunctionMapping.GetInstanse().AddFunctionMapping(FunctionKey.DeviceWallpaper)  '功能统计

            ElseIf strName.StartsWith("PhotoLib") Then
                selectButton = ButtonSelect.PhotoLib
                'FunctionMapping.GetInstanse().AddFunctionMapping(FunctionKey.DevicePhotoLib)  '功能统计

            End If
        End If

        Return selectButton
    End Function

#End Region

#Region "--- 上传文件 ---"

    Private Overloads Function GetUploadKeyAndValue(ByRef listFiles As List(Of String), ByVal strVersion As String) As Dictionary(Of String, String)
        Dim dicTasks As New Dictionary(Of String, String)

        For Each strPath As String In listFiles
            Dim strValue As String = ""
            Select Case Me.mButtonSelect
                Case ButtonSelect.Camera, ButtonSelect.Video
                    If strPath.EndsWith(".THM") Then
                        If strVersion.StartsWith("4.") Then
                            strValue = Me.mCameraFolderMISC & "/" & Path.GetFileName(strPath)
                        ElseIf strVersion.StartsWith("3.") Then
                            strValue = Me.mCameraFolderMISC & "/" & Path.GetFileName(strPath)
                        Else
                            strValue = Me.mCameraFolderMISC & "/" & Path.GetFileName(strPath)
                        End If
                    Else
                        Dim strName As String = ""
                        Dim strEx As String = Path.GetExtension(strPath).ToUpper()
                        If Me.mDicCameraPics.ContainsKey(strPath) Then
                            strName = Me.mDicCameraPics(strPath)
                        Else
                            strName = Me.CreateCameraName()
                        End If
                        strValue = Me.mCameraFolder & "/" & strName & IIf(strEx = ".JPEG", ".JPG", strEx)
                    End If

            End Select

            dicTasks.Add(strPath, strValue)
        Next

        Return dicTasks
    End Function

    Public Sub UploadToPhoneThread(ByRef listUploadFiles As List(Of String))
        If Me.mDevice.VersionNumber >= 500 AndAlso Me.mButtonSelect <> ButtonSelect.Wallpaper Then
            Me.AddPhoto(listUploadFiles.ToArray())
        Else
            Dim thd As New Thread(New ParameterizedThreadStart(AddressOf UploadToPhone))
            thd.IsBackground = True

            thd.Start(listUploadFiles)
        End If
    End Sub

    '上传文件到设备中
    Private Overloads Sub UploadToPhone(ByVal state As Object)
        Try
            Dim listUploadFiles As List(Of String) = state

            If listUploadFiles Is Nothing OrElse listUploadFiles.Count = 0 Then
                Return
            End If


           

            '初始化参数
            Me.mCancel = False
            Me.mIsUpload = True
            Me.mTotalSize = 0
            Me.mTranslateSize = 0

            '初始化界面控件状态
            Me.ShowProgress("", 0, 0, ProgressAction.Prepair)
            Me.UpdateTranslateStatus(False)
            Me.UpdateButtonAbortStatus(True)
            Me.SetControlStatus(False)

            '=== 获取设备信息 =================================================================
            Dim strProductType As String = ""
            Dim strVersion As String = ""
            Dim intVersion As Integer = 200

            strProductType = Me.mDevice.ProductType
            strVersion = Me.mDevice.ProductVersion
            intVersion = strVersion.Replace(".", "").PadRight(3, "0")
            '==================================================================================

            Dim listNewFiles As New List(Of String)
            Dim listAddToListViewOnPC As New List(Of String)
            Dim listUploadFailFiles As New List(Of String)
            Dim listNewItems As New List(Of ListViewItem)
            Dim listNewItemsPng As New List(Of ListViewItem)
            Dim listNewItemsMov As New List(Of ListViewItem)

            '生成缩略图
            Try
                Dim intCount As Integer = listUploadFiles.Count
                Dim intCurrent As Integer = 0
                For Each strPath As String In listUploadFiles
                    intCurrent += 1
                    If File.Exists(strPath) Then
                        Me.ShowProgress(Path.GetFileName(strPath), intCurrent, intCount, ProgressAction.CreateThumbnail)

                        'bmp转换为jpg图片再上传
                        If String.Compare(Path.GetExtension(strPath), ".BMP", True) = 0 Then
                            Dim strTempJPGPath = Path.Combine(Me.mTempFolder, Path.GetFileNameWithoutExtension(strPath) & ".JPG")
                            Dim bmpTemp As New Bitmap(strPath)

                            bmpTemp.Save(strTempJPGPath, ImageFormat.Jpeg)
                            bmpTemp.Dispose()

                            strPath = strTempJPGPath
                        End If

                        'If Me.mButtonSelect = ButtonSelect.Wallpaper Then
                        '    '添加壁纸及壁纸缩略图到新上传列表
                        '    listNewFiles.AddRange(Me.CreateWallPaperThumbnail(strPath, strProductType, listAddToListViewOnPC))

                        'Else
                        If Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video Then
                            '添加照片缩略图到新上传列表
                            listNewFiles.AddRange(Me.CreateCameraThumbnail(strPath, intVersion, listAddToListViewOnPC))

                        End If

                    End If

                    If Me.mCancel Then
                        GoTo DO_EXIST
                    End If
                Next
            Catch ex As Exception
            End Try

            '计算文件的总大小
            For Each strPath As String In listNewFiles
                mTotalSize += New FileInfo(strPath).Length

                If Me.mCancel Then
                    GoTo DO_EXIST
                End If
            Next

            '添加壁纸及壁纸缩略图、照片缩略图到上传列表
            listUploadFiles.AddRange(listNewFiles)

            Dim blnCreateMobileConnection As Boolean = False
            Dim hAFC As IntPtr = IntPtr.Zero

            '检测是否需要启动Mobile连接
            If Me.mDevice.Jailbreaked AndAlso (Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video) Then
                '创建Moblile连接
                hAFC = Me.mDevice.MobileAfcHandle
                blnCreateMobileConnection = True

            Else
                hAFC = Me.mDevice.DefaultAfcHandle
                blnCreateMobileConnection = False

            End If

            '开始同步
            Me.mDevice.StartSync()

            If Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video Then
                Dim strTempCameraFolder As String = Me.mCameraFolder
                If blnCreateMobileConnection Then
                    strTempCameraFolder = Me.mDevice.FormatToMobilePath(strTempCameraFolder)
                End If

                '检测照相机目录
                Me.mDevice.CheckFolder(strTempCameraFolder, hAFC)

                If intVersion >= 200 Then
                    '对mCameraFolderMISC赋值，并检测MISC目录
                    Me.mCameraFolderMISC = Me.GetCameraThumnailFolder(Me.mCameraFolder, strVersion)

                    Dim strTempCameraFolderMISC As String = Me.mCameraFolderMISC
                    If blnCreateMobileConnection Then
                        strTempCameraFolderMISC = Me.mDevice.FormatToMobilePath(strTempCameraFolderMISC)
                    End If
                    Me.mDevice.CheckFolder(strTempCameraFolderMISC, hAFC)
                End If
            End If

            '生成上传路径字典
            Dim listKeyAndValue As Dictionary(Of String, String) = Me.GetUploadKeyAndValue(listNewFiles, strVersion)

            For Each pair As KeyValuePair(Of String, String) In listKeyAndValue
                Dim strPathOnPhone As String = pair.Value
                Dim strPathOnPc As String = pair.Key
                Dim strPathOnPhoneOfMobile As String = String.Empty
                Dim info As iPhoneFileInfo = Nothing

                If blnCreateMobileConnection Then
                    '注意替换iPhone路径
                    strPathOnPhoneOfMobile = Me.mDevice.FormatToMobilePath(strPathOnPhone)
                Else
                    strPathOnPhoneOfMobile = strPathOnPhone
                End If

                If Me.mDevice.CopyToPhone(hAFC, strPathOnPc, strPathOnPhoneOfMobile, New DataTransferEventHandler(AddressOf DataTransferProgress)) Then
                    '添加上传成功的图片列表
                    If listAddToListViewOnPC.Contains(strPathOnPc) Then
                        Me.mDevice.IsDirectory(hAFC, strPathOnPhoneOfMobile, info)
                        If info IsNot Nothing Then
                            Dim strExtension As String = Path.GetExtension(strPathOnPhone).ToUpper()
                            If strExtension = ".JPG" OrElse strExtension = ".GIF" Then
                                listNewItems.Add(Me.CreateListViewItem_4X(strPathOnPhone, info, Me.mCurrentView = View.LargeIcon, True))

                            ElseIf strExtension = ".PNG" Then
                                listNewItems.Add(Me.CreateListViewItem_4X(strPathOnPhone, info, Me.mCurrentView = View.LargeIcon, True))
                                listNewItemsPng.Add(Me.CreateListViewItem_4X(strPathOnPhone, info, Me.mCurrentView = View.LargeIcon, True))

                            ElseIf strExtension = ".MOV" OrElse strExtension = ".MP4" OrElse strExtension = ".M4V" Then
                                listNewItemsMov.Add(Me.CreateListViewItem_4X(strPathOnPhone, info, Me.mCurrentView = View.LargeIcon, True))

                            End If
                        End If
                    End If

                ElseIf Me.mCancel = False Then
                    '添加下载失败列表
                    listUploadFailFiles.Add(pair.Key)
                End If

                '退出循环，并更新已经上传图片到列表中
                If Me.mCancel Then
                    Exit For
                End If
            Next

            '结束同步
            Me.mDevice.EndSync()

            '添加新上传图片到ListView
            Select Case Me.mButtonSelect
                Case ButtonSelect.Camera, ButtonSelect.Screenshot, ButtonSelect.Video
                    Me.AddItemToListView(Me.mCameraListView, listNewItems, False)
                    Me.AddItemToListView(Me.mScreenShotListView, listNewItemsPng, False)
                    Me.AddItemToListView(Me.mVideoListView, listNewItemsMov, False)

                    'Case ButtonSelect.Wallpaper
                    '    Me.AddItemToListView(Me.mWallpaperListView, listNewItems, False)

            End Select
DO_EXIST:
            '恢复可用状态
            Me.ShowProgress("", mTotalSize, mTotalSize, ProgressAction.Completed)
            Me.UpdateTranslateStatus(True)
            Me.UpdateButtonAbortStatus(False)
            Me.SetControlStatus(True)

            '设置ListView的滚动条状态为变更
            Me.mDicCameraPics.Clear()

            Me.ShowUploadMessage(intVersion, listNewItems, listNewItemsMov, listUploadFailFiles)

            Me.UpdateListViewItemCount(Me.GetCurrentListView().Items.Count)

            '更新列表
            If Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Screenshot OrElse Me.mButtonSelect = ButtonSelect.Video Then
                Me.UpdateThumbnailCamera(New Object() {Me.GetCurrentListView(), Me.mButtonSelect})
            End If

        Catch ex As Exception
            Common.Log("UploadToPhone" & vbTab & ex.ToString())
        End Try
    End Sub

    Private Delegate Sub ShowUploadMessageHandler(ByVal intVersion As Integer, ByVal listNewItems As List(Of ListViewItem), ByVal listNewItemsMov As List(Of ListViewItem), ByVal listUploadFailFiles As List(Of String))
    Private Sub ShowUploadMessage(ByVal intVersion As Integer, ByVal listNewItems As List(Of ListViewItem), ByVal listNewItemsMov As List(Of ListViewItem), ByVal listUploadFailFiles As List(Of String))
        If Me.InvokeRequired Then
            Me.Invoke(New ShowUploadMessageHandler(AddressOf ShowUploadMessage), intVersion, listNewItems, listNewItemsMov, listUploadFailFiles)
        Else
            If (Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video) AndAlso _
                                  (listNewItemsMov.Count > 0 OrElse listNewItems.Count > 0) Then

                Me.DeleteCameraDBFiles()
                Dim strText As String = ""
                If listNewItems.Count > 0 Then
                    strText &= String.Format(Me.Language.GetString("Photo.Message.UploadSucceedInCamera"), listNewItems.Count) '"【照相机】中成功上传了 {0} 张图片，"
                End If
                If listNewItemsMov.Count > 0 Then
                    strText &= String.Format(Me.Language.GetString("Photo.Message.UploadSucceedInVideo"), listNewItemsMov.Count) '"【截屏】中成功上传了 {0} 张图片，"
                End If

                '"如果您已打开了设备中的【照片】程序，" & vbCrLf & _
                '"请在操作完成后按【Home键】退出，" & vbCrLf & _
                '"并请双击【Home键】关闭后台的【照片】程序，" & vbCrLf & _
                '"以刷新照片数据!"
                strText &= vbCrLf & vbCrLf & Me.Language.GetString("Photo.Message.ClosePhotoInfo")

                If Me.mDevice.VersionNumber >= 500 Then
                    strText &= vbCrLf & Me.Language.GetString("Photo.Message.RestarDevice")     '"如果图片出现模糊，请重启设备。"
                End If

                If intVersion >= 320 Then
                    tbMessageBox.Show(Me, strText, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                End If
            End If

            '显示上传成功信息
            If mCancel = False AndAlso listUploadFailFiles.Count = 0 Then
                Me.UpdateMainLableText(Me.Language.GetString("RingtoneMaker.Message.ImportSucceed")) '导入成功！
            End If

            '显示下载失败列表
            If listUploadFailFiles.Count > 0 Then
                Dim strText As String = ""
                For Each strPath As String In listUploadFailFiles
                    strText &= vbCrLf & Path.GetFileName(strPath)
                Next
                '以下文件导入失败
                tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.FileUploadFailure") & Me.Language.GetString("Common.Symbol.Colon") & strText, _
                                Me.Language.GetString("Common.Error"), MessageBoxButtons.OK, MessageBoxIcon.Error) '"以下文件上传失败："
            End If
        End If

    End Sub

    Private Sub UploadArrayToPhone(ByVal arrFiles As Array)
        Dim list As New List(Of String)
        list.Add(".PNG")
        list.Add(".JPG")
        list.Add(".JPEG")
        list.Add(".GIF")
        list.Add(".BMP")
        list.Add(".LIVEPHOTO")
        '在照片图库里面暂时不添加视频文件，因不缩略图要用插件取得，如果没有下载插件就会出问题。
        'list.Add(".MOV")
        'list.Add(".M4V")
        'list.Add(".MP4")

        UploadArrayToPhone(arrFiles, list)
        list.Clear()
    End Sub

    Private Sub UploadArrayToPhoneByVedio(ByVal arrFiles As Array)
        Dim list As New List(Of String)
        list.Add(".MOV")
        list.Add(".M4V")
        list.Add(".MP4")

        UploadArrayToPhone(arrFiles, list)
        list.Clear()
    End Sub

    Private Sub UploadArrayToPhone(ByVal arrFiles As Array, ByVal listExtensions As List(Of String))
        If Not Me.CheckSupport(OpType.Upload, ImportSource.DragDrop, arrFiles) Then
            Return
        End If

        Try
            Dim lstFiles As New List(Of String)
            Dim dicFolders As New Dictionary(Of String, List(Of String))

            If arrFiles.Length > 0 Then
                For Each strFile As String In arrFiles
                    Dim strEx As String = Path.GetExtension(strFile).ToUpper()
                    If listExtensions.Contains(strEx) Then

                        lstFiles.Add(strFile)
                    ElseIf Directory.Exists(strFile) Then
                        Me.AddFolderToDict(strFile, dicFolders, listExtensions)

                        '限得第二层文件夹下的文件
                        For Each Item As String In Directory.GetDirectories(strFile)
                            Me.AddFolderToDict(Item, dicFolders, listExtensions)
                        Next

                    End If
                    Application.DoEvents()
                Next
                If Me.CheckDiskFull(lstFiles.ToArray(), dicFolders) Then
                    tbMessageBox.Show(IIf(Me.Parent Is Nothing, Me, Me.mApplication), _
                               Me.Language.GetString("Common.Message.DiskSpaceNotEnough"), _
                              Me.Language.GetString("Common.Info"), _
                              MessageBoxButtons.OK, _
                              MessageBoxIcon.Warning)
                    Return
                End If
                If lstFiles.Count > 0 OrElse dicFolders.Count > 0 Then
                    If Me.mButtonSelect = ButtonSelect.PhotoLib Then
                        Me.AddPhotoOK(lstFiles.ToArray(), dicFolders)

                    Else
                        '如果不是照片图库就取出文件夹中所有的文件进行上传
                        If dicFolders IsNot Nothing AndAlso dicFolders.Count > 0 Then
                            For Each listPhotos As List(Of String) In dicFolders.Values
                                For Each strPhoto As String In listPhotos
                                    lstFiles.Add(strPhoto)
                                    Application.DoEvents()

                                Next
                            Next
                        End If
                        Me.UploadToPhoneThread(lstFiles)

                    End If
                End If
            End If

        Catch ex As Exception
            Me.SetDeviceNodeEnable(True)
        End Try
    End Sub

    Private Function CheckDiskFull(ByVal arrFiles As String(), ByVal dicFolders As Dictionary(Of String, List(Of String)))
        Dim blnIsFull As Boolean = False
        Dim size As Long = 0
        Try
            If arrFiles IsNot Nothing AndAlso arrFiles.Length > 0 Then
                For Each strPath As String In arrFiles
                    If File.Exists(strPath) Then
                        size += New FileInfo(strPath).Length
                        Application.DoEvents()
                    Else
                        size += Me.CaculateSize(strPath)
                    End If
                Next
            End If

            If dicFolders IsNot Nothing Then
                For Each Item As List(Of String) In dicFolders.Values
                    For Each strPath As String In Item
                        If File.Exists(strPath) Then
                            size += New FileInfo(strPath).Length
                            Application.DoEvents()
                        Else
                            size += Me.CaculateSize(strPath)
                        End If
                    Next
                Next
            End If

            If frmSummary.DeviceFreeSize > -1 AndAlso frmSummary.DeviceFreeSize < size Then
                blnIsFull = True
            End If

        Catch ex As Exception
        End Try

        Return blnIsFull
    End Function

    Private Sub AddFolderToDict(ByVal strPath As String, ByRef dicFolder As Dictionary(Of String, List(Of String)), ByVal listExtensions As List(Of String))
        Dim lstFolderItems As New List(Of String)

        For Each strFile As String In Directory.GetFiles(strPath)
            Dim strExInFolder As String = Path.GetExtension(strFile).ToUpper()
            If listExtensions.Contains(strExInFolder) Then
                lstFolderItems.Add(strFile)
            End If

            Application.DoEvents()
        Next

        If lstFolderItems.Count > 0 Then
            Dim strName As String = Me.GetNewName(Path.GetFileName(strPath), dicFolder)
            dicFolder.Add(strName, lstFolderItems)
        End If

    End Sub

    Private Function GetNewName(ByVal strName As String, ByVal dicFolder As Dictionary(Of String, List(Of String))) As String
        Dim strNameReturn As String = strName
        Dim count As Integer = 0
        While True
            If dicFolder.ContainsKey(strNameReturn) Then
                count += 1
                strNameReturn = String.Format("{0}({1})", strName, count)
            Else
                Exit While
            End If
        End While

        Return strNameReturn
    End Function

#End Region

#Region "--- 更新UI ---"

    Private Sub lblFileCount_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles lblFileCount.TextChanged
        '如果多个设备连接并且不是当前的设备就不显示消息
        Dim frmMain As MainForm = CType(Me.mApplication, MainForm)
        Try
            If Me.mDevice IsNot Nothing AndAlso frmMain.CurrentDevice IsNot Nothing AndAlso Me.mDevice.Identifier <> frmMain.CurrentDevice.DeviceID Then
                Return
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmPhotoBase_lblFileCount_TextChanged")
        End Try
        frmMain.SetLabelText(FunctionKey.Photo, Me.lblFileCount.Text.TrimStart)
    End Sub

    Protected Overrides Sub UpdateMainLableText(ByVal strText As String)
        MyBase.UpdateMainLableText(strText)
        Me.mApplication.SetLabelText(FunctionKey.Photo, strText)
    End Sub

    Private Sub SyncStart(ByVal sender As Object, ByVal e As EventArgs)
        Me.ShowProgress(Me.Language.GetString("Photo.Message.CreateingMediaData"), 0, 0, ProgressAction.Prepair)                '"正在生成媒体数据，请稍候 ..."
    End Sub

    Private Sub SyncCallBack(ByVal sender As Object, ByVal e As SyncPhotoEventArgs)
        Me.ShowProgress(Path.GetFileName(e.Current.FilePathOnPC), e.TransSize, e.TotalSize, ProgressAction.Transfer)
    End Sub

    Private Sub SyncEnd(ByVal sender As Object, ByVal e As EventArgs)
        If Me.mDevice.VersionNumber >= 600 AndAlso Me.mButtonSelect = ButtonSelect.Camera AndAlso e IsNot Nothing Then
            Dim args As SyncPhotoEventArgs = CType(e, SyncPhotoEventArgs)
            '            Me.UpdateTranslateStatus(False)
            Dim strInfo As String = ""
            If args.ErrorType = SyncPhotoErrorType.NoRunTui Then
                strInfo = Me.Language.GetString("Photo.Message.SyncFailedNoRunTui") '"导入图片中断，请确认同步推是否在运行。"

            ElseIf args.ErrorType = SyncPhotoErrorType.AccessLimit Then
                If String.IsNullOrEmpty(Me.mTuiTxt) Then
                    Me.mTuiTxt = Me.Language.GetString("File.Label.tbTui")
                End If
                If args.Current.FilePathOnPC.Length > 0 AndAlso Path.GetExtension(args.Current.FilePathOnPC).ToLower() = ".livephoto" Then
                    strInfo = String.Format(Me.Language.GetString("Photo.Message.SyncFailedAccessLimitEx"), Me.mTuiTxt)
                    '"导入图片失败\n\r1、请确认同步推有访问照片的权限（系统设置->隐私->照片）\n\r2、LivePhoto需要图片与视频完全匹配。
                Else
                    strInfo = String.Format(Me.Language.GetString("Photo.Message.SyncFailedAccessLimit"), Me.mTuiTxt)
                    '"导入图片失败,请确认同步推有访问照片的权限（系统设置->隐私->照片）。"
                End If

            ElseIf args.ErrorType = SyncPhotoErrorType.ErrorTui Then
                If String.IsNullOrEmpty(Me.mTuiTxt) Then
                    Me.mTuiTxt = Me.Language.GetString("File.Label.tbTui")
                End If
                strInfo = String.Format(Me.Language.GetString("Photo.Message.SyncFailedErrorTui"), Me.mTuiTxt)
                '"导入图片失败，请确认：" + vbCrLf + vbCrLf + _
                '"1、同步推是否在运行；" + vbCrLf + vbCrLf + _
                '"2、同步推是否有访问照片的权限（系统设置->隐私->照片）。"

            End If

            If strInfo.Length > 0 Then
                tbMessageBox.Show(Me, strInfo, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                Me.OnRefresh()
            End If

        End If
    End Sub

    Private Sub BackupStart(ByVal sender As Object, ByVal e As EventArgs)
        Me.ShowBackupProgress("", 0, 0, ProgressAction.Prepair)                '"正在生成媒体数据，请稍候 ..."
    End Sub

    Private Sub BackupCallBack(ByVal sender As Object, ByVal e As SyncPhotoEventArgs)
        Me.ShowBackupProgress(Path.GetFileName(e.Current.FilePathOnPhone), e.TransSize, e.TotalSize, ProgressAction.Transfer)
    End Sub

    Private Sub BackupEnd(ByVal sender As Object, ByVal e As EventArgs)
        Me.ShowBackupProgress("", 0, 0, ProgressAction.Completed)
        'Common.OpenExplorer(CType(sender, ISqlitePhotoDB).BackupFolder)
    End Sub

    Private Sub ShowBackupProgress(ByVal strFile As String, ByVal longFinish As Long, ByVal longTotal As Long, ByVal action As ProgressAction)
        If Me.InvokeRequired Then
            Me.Invoke(New ShowProgressHandler(AddressOf ShowBackupProgress), strFile, longFinish, longTotal, action)
        Else
            If longTotal > 0 Then
                Me.pgbProgress.tbPlayValue = longFinish * 100 / longTotal
            Else
                Me.pgbProgress.tbPlayValue = 0
            End If



            If action = ProgressAction.Prepair Then
                Me.pgbProgress.tbIsWaiting = True
                Me.lblCurrentFile.Text = Me.Language.GetString("File.Label.DownLoadPrepare")

            ElseIf action = ProgressAction.Completed Then
                Me.pgbProgress.tbIsWaiting = True
                Me.lblCurrentFile.Text = Me.Language.GetString("Photo.Message.CreateingMediaData")

            Else
                Me.pgbProgress.tbIsWaiting = False
                '"正在下载：{1}  总共：{2}    文件名：{0}"
                Me.lblCurrentFile.Text = String.Format(Me.Language.GetString("Photo.Message.DownloadingPhoto"), strFile, Utility.FormatFileSize(longFinish), Utility.FormatFileSize(longTotal))
            End If
        End If

        Application.DoEvents()
    End Sub

#End Region

#Region "--- Overrides ---"

    Public Overrides Sub OnDisconnect(ByVal device As IDevice)
        MyBase.OnDisconnect(device)

        Dim blnSelectLocal As Boolean = False

        Try
            '==================更新树节点
            Me.tvwDevice.SuspendLayout()

            If Me.mNodeAndroid IsNot Nothing Then
                Me.mNodeAndroid.Visible = True
            End If

            If Folder.LangType <> LanguageType.zh_CN Then
                Me.mApplication.GotoWelcome(Me)
            End If

            If Me.mDevice IsNot Nothing Then
                RemoveHandler mDevice.AddWallpaperEvent, AddressOf OnAddWallpaper
            End If

            If Me.mNodeDevice IsNot Nothing Then
                If Me.mNodeDevice.ChildNodes.Contains(Me.tvwDevice.SelectedNode) AndAlso Me.mNodeLocal IsNot Nothing AndAlso Me.mNodeLocal.ChildNodes.Count > 0 Then
                    blnSelectLocal = True
                End If

                Me.mNodeDevice.ChildNodes.Clear()
                Me.mNodeDevice.Text = Me.Language.GetString("App.TreeNode.NotConnectDevice")
            End If

            Me.SetSiteNodeVisible()

            Me.tvwDevice.ResumeLayout()
            '===========================

            If Me.pnlContainer IsNot Nothing AndAlso Me.pnlContainer.Controls.Count > 0 Then
                For Each Item As Control In Me.pnlContainer.Controls
                    Item.Dispose()
                    Item = Nothing
                Next
                Me.pnlContainer.Controls.Clear()
            End If

            Me.UpdateTranslateStatus(True)

            Me.RemoveDownload()
        Catch ex As Exception
            Debug.Print("Photo disconnect failure.")
        End Try

        Try
            If Me.mfrmMessage IsNot Nothing Then
                Me.mfrmMessage.Close()
                Me.mfrmMessage.Dispose()
                Me.mfrmMessage = Nothing
            End If
        Catch ex As Exception
        End Try

        Try
            If Me.mfrmErrorMessage IsNot Nothing Then
                Me.mfrmErrorMessage.Close()
                Me.mfrmErrorMessage = Nothing
            End If
        Catch
        End Try

        Me.DisposeAddPhotoOKThread()


        Try
            If Me.tvwDevice.SelectedNode Is Nothing OrElse blnSelectLocal Then
                Me.tvwDevice.SelectedNode = Me.mNodeLocal.ChildNodes(0)
            End If
        Catch ex As Exception
        End Try
    End Sub

    Protected Overrides Function GetCurrentListView() As tbListView
        Dim lvw As tbListViewEx = Nothing

        Select Case Me.mButtonSelect
            Case ButtonSelect.Camera
                lvw = Me.mCameraListView

            Case ButtonSelect.Screenshot
                lvw = Me.mScreenShotListView

            Case ButtonSelect.Video
                lvw = Me.mVideoListView

            Case ButtonSelect.PhotoLib
                lvw = Me.mPhotoLibListView
                If Me.tvwDevice IsNot Nothing AndAlso Me.tvwDevice.SelectedNode IsNot Nothing Then
                    If Me.tvwDevice.SelectedNode.Name = "PhotoLib" Then
                        lvw = Me.mPhotoLibListView
                    Else
                        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber >= 500 Then
                            Dim folder As AlbumItem = Me.tvwDevice.SelectedNode.Tag
                            If folder IsNot Nothing AndAlso Me.pnlContainer.Controls.ContainsKey(folder.ID.ToString()) Then
                                lvw = Me.pnlContainer.Controls(folder.ID.ToString())
                            End If
                        Else
                            Dim folder As ImageAlbum = Me.tvwDevice.SelectedNode.Tag
                            If folder IsNot Nothing AndAlso Me.pnlContainer.Controls.ContainsKey(folder.FolderID.ToString()) Then
                                lvw = Me.pnlContainer.Controls(folder.FolderID.ToString())
                            End If
                        End If
                    End If
                End If

        End Select

        Return lvw
    End Function

    Protected Overrides Sub LoadData(Optional ByVal blnRefresh As Boolean = False)
        MyBase.LoadData(blnRefresh)
    End Sub

    Protected Overrides Sub OnLoadData(ByVal blnRefresh As Boolean)
        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber >= 500 Then
            Dim strSQLite3 As String = Path.Combine(iTunesHelper.AppleApplicationSupportFolder, "SQLite3.dll")
            Dim blnShowMsg As Boolean = False

            If Not File.Exists(strSQLite3) Then
                blnShowMsg = True
            Else
                Dim vInfo As FileVersionInfo = FileVersionInfo.GetVersionInfo(strSQLite3)
                Dim strVer As String = String.Format("{0}.{1}.{2}", vInfo.ProductMajorPart, vInfo.ProductMinorPart, vInfo.ProductBuildPart)

                If strVer = "0.0.0" Then
                    strVer = vInfo.FileVersion
                End If

                If New Version(strVer) < New Version("3.7.5") Then
                    blnShowMsg = True
                End If
            End If

            If blnShowMsg Then
                '"很抱歉，无法兼容当前的iTunes版本，同步服务需要 iTunes 10.5 或更高的版本。" & vbCrLf & _
                '"请下载并安装最新版本的iTunes后重试。" & vbCrLf & _
                '"如果您已经是最新版的iTunes，请卸载干净后重新安装！"
                Dim strMessage As String = Me.Language.GetString("Photo.Message.NeedHightVersion")
                tbMessageBox.Show(Me, strMessage, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                '"http://www.tongbu.com/install/itunes_all.html"
                Common.OpenExplorer(WebUrl.PageInstalliTunes)
            End If

            If mPhotoThumb Is Nothing OrElse blnRefresh Then
                Dim dtLast As DateTime = Now
                'Debug.Print("初始化：" & Now.Subtract(dtLast).TotalMilliseconds)
                If mPhotoThumb IsNot Nothing Then
                    RemoveHandler mPhotoThumb.SyncStart, AddressOf SyncStart
                    RemoveHandler mPhotoThumb.SyncProgress, AddressOf SyncCallBack
                    RemoveHandler mPhotoThumb.SyncEnd, AddressOf SyncEnd
                    RemoveHandler mPhotoThumb.BackupStart, AddressOf BackupStart
                    RemoveHandler mPhotoThumb.BackupProgress, AddressOf BackupCallBack
                    RemoveHandler mPhotoThumb.BackupEnd, AddressOf BackupEnd
                End If

                If Me.mDevice IsNot Nothing Then
                    Application.DoEvents()

                    mPhotoThumb = SqlitePhotoDBHelper.InstancePhotoDB(Me.mDevice, blnRefresh)

                    If mPhotoThumb IsNot Nothing Then
                        AddHandler mPhotoThumb.SyncStart, AddressOf SyncStart
                        AddHandler mPhotoThumb.SyncProgress, AddressOf SyncCallBack
                        AddHandler mPhotoThumb.SyncEnd, AddressOf SyncEnd
                        AddHandler mPhotoThumb.BackupStart, AddressOf BackupStart
                        AddHandler mPhotoThumb.BackupProgress, AddressOf BackupCallBack
                        AddHandler mPhotoThumb.BackupEnd, AddressOf BackupEnd
                        If Not String.IsNullOrEmpty(mPhotoThumb.ErrorMsg) Then
                            '"照片图库加载失败。"
                            tbMessageBox.Show(Me.Language.GetString("Photo.Message.PhotosLoadFailure") & Me.Language.GetString("App.Message.GotoQQ"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Error)
                        End If
                    End If

                End If

                Debug.Print("加载数据库的耗时：" & Now.Subtract(dtLast).TotalMilliseconds)
            End If

            '加载资料的时候取得最新的PhotosDB的修改时间
            If Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber < 600 Then
                Me.UpdatePhotosUpdateTime()
            End If

        ElseIf Me.mDevice IsNot Nothing AndAlso Me.mDevice.VersionNumber >= 420 Then
            If mPhotoThumb Is Nothing OrElse blnRefresh Then
                Application.DoEvents()
                mPhotoThumb = SqlitePhotoDBHelper.InstancePhotoDB(Me.mDevice, blnRefresh)
            End If
        End If
    End Sub

    Protected Overrides Sub OnLoadDataCompleted(ByVal blnRefresh As Boolean)
        Try
            Select Case Me.mButtonSelect
                Case ButtonSelect.Camera, ButtonSelect.Screenshot, ButtonSelect.Video
                    If Me.mDCIMLoad = False OrElse blnRefresh Then
                        If Me.mDicThread.ContainsKey(ButtonSelect.Camera) Then
                            If Me.mDicThread(ButtonSelect.Camera) IsNot Nothing AndAlso Me.mDicThread(ButtonSelect.Camera).ThreadState <> ThreadState.Stopped Then
                                Try
                                    Me.mDicThread(ButtonSelect.Camera).Abort()
                                Catch ex As Exception
                                End Try
                            End If
                            Me.mDicThread.Remove(ButtonSelect.Camera)
                        End If

                        Me.mDCIMLoad = True
                        Application.DoEvents()

                        Dim thd As New Thread(AddressOf LoadFromCameraThread)
                        thd.IsBackground = True

                        If Me.mDicThread.ContainsKey(ButtonSelect.Camera) = False Then
                            Me.mDicThread.Add(ButtonSelect.Camera, thd)
                            thd.Start()
                        End If

                        '加载图库分类=================================================
                        If Me.mDevice.VersionNumber >= 500 Then
                            Me.LoadPhotoLibToNode_5X(blnRefresh, False)
                        Else
                            Me.LoadPhotoLibToNode(blnRefresh, False)
                        End If
                        Me.mPhotoLibLoad = True
                        '=============================================================

                    ElseIf Me.mDCIMLoad AndAlso blnRefresh = False Then
                        Me.UpdateThumbnailCamera(New Object() {Me.GetCurrentListView(), Me.mButtonSelect})

                    End If

                Case ButtonSelect.PhotoLib
                    If Me.mDevice.VersionNumber >= 500 Then
                        Me.LoadPhoneLib_5X(blnRefresh)
                    Else
                        Me.LoadPhoneLib(blnRefresh)
                    End If

            End Select

            '如果数据库加载失败提示用户操作，只提示一次。
            If Not Me.mPhotoThumb.LoadDBSucceed AndAlso Not Me.mLoadDBErrorMessage Then
                Me.mLoadDBErrorMessage = True
                Dim strMessage As String = Me.Language.GetString("Photo.Message.LoadPhotosFailure")

                '"无法加载照片图库，可能原因如下：" & vbCrLf & _
                '"1.电脑空间不足（清理后再操作）。" & vbCrLf & _
                '"2.设备""照片""应用存在问题。" & vbCrLf & _
                '"3.手机有锁屏密码（解锁后再操作）。"

                tbMessageBox.Show(Me, strMessage, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
            End If
            Me.SetControlStatus(True)
        Catch ex As Exception
        End Try
    End Sub

    Protected Overrides Sub OnViewLargeIcon()
        If Common.VerIs30 Then
            Me.mFormWallpaper.btnViewLargeIcon.Checked = True
            Me.LoadThumbnail()
            Return
        End If

        Me.LoadThumbnail()
        Me.mFormWallpaper.btnViewLargeIcon.Checked = True
    End Sub

    Protected Overrides Sub OnViewList()
        If Me.mFormWallpaper IsNot Nothing Then
            Me.mFormWallpaper.btnViewList.Checked = True
        End If

        If Common.VerIs30 AndAlso Me.mCurrentView = View.LargeIcon Then
            Me.LoadThumbnail()
            Return
        End If

        Dim lvw As tbListViewEx = Me.GetCurrentListView()
        lvw.ShowGroups = False
        Me.SwitchThumbnail(lvw)
    End Sub

    Private Sub LoadThumbnail()
        Dim lvw As tbListViewEx = Me.GetCurrentListView()
        Me.SwitchThumbnail(lvw)

        Select Case Me.mButtonSelect
            Case ButtonSelect.Camera, ButtonSelect.Screenshot, ButtonSelect.Video
                Me.UpdateThumbnailCamera(New Object() {lvw, Me.mButtonSelect})

            Case ButtonSelect.PhotoLib
                Me.UpdateThumbnailPhotoLib(lvw)
        End Select
    End Sub

    Protected Overrides Sub OnViewDetail()
        If Me.mFormWallpaper IsNot Nothing Then
            Me.mFormWallpaper.btnViewDetail.Checked = True
        End If

        Dim lvw As tbListViewEx = Me.GetCurrentListView()
        lvw.ShowGroups = False
        Me.SwitchThumbnail(lvw)
    End Sub

    Protected Overrides Sub OnListView_DragDrop(ByVal arrFiles As Array)
        If Me.mButtonSelect = ButtonSelect.Video Then
            If Me.mDevice.VersionNumber >= 600 Then
                Me.UploadVideoForiOS6()
            Else
                Me.UploadArrayToPhoneByVedio(arrFiles)
            End If
        Else
            '拖拽的时候如果是放到图片节点只上传图片
            Dim lstPhoto As New List(Of String)
            For Each Item As String In arrFiles
                '图片,和文件夹都传进去
                If Me.CheckIfImg(Item) OrElse Directory.Exists(Item) Then
                    lstPhoto.Add(Item)
                End If
            Next

            If Me.mDevice.VersionNumber >= 600 AndAlso Me.mButtonSelect = ButtonSelect.Camera Then
                Me.UploadPhotoForiOS6(New KeyValuePair(Of ImportSource, Array)(ImportSource.DragDrop, lstPhoto.ToArray))

            Else
                Me.UploadArrayToPhone(lstPhoto.ToArray)

            End If
        End If
    End Sub

    Protected Overrides Sub OnMenuOpening()
        Try
            Dim curFolder As Object = Me.tvwDevice.SelectedNode.Tag

            'For Each item As ToolStripItem In Me.cmsOperate.Items
            '    Debug.Print(item.Enabled.ToString() & vbTab & item.Visible.ToString() & vbTab & item.Text)
            'Next

            Me.cmsOperate.SuspendLayout()
            Me.tsmiAddToPhotoLib.DropDownItems.Clear()

            If Me.mButtonSelect = ButtonSelect.PhotoLib AndAlso Me.GetCurrentListView.SelectedItems.Count > 0 Then
                Me.tsmiAddToPhotoLib.Enabled = True

                If Me.tvwDevice.SelectedNode Is Me.mPhotoLibNode Then
                    Me.tsmiRemoveFromPhotoLib.Enabled = False
                Else
                    Me.tsmiRemoveFromPhotoLib.Enabled = True
                End If

                If Me.mDevice.VersionNumber >= 500 Then
                    For Each folder As AlbumItem In Me.mPhotoThumb.Albums
                        If folder.IsMaster = False AndAlso folder.Title.Length > 0 AndAlso curFolder.ID <> folder.ID Then
                            If folder.Kind = pAlbumKind.AlbumPhone AndAlso Me.mDevice.VersionNumber >= 600 Then
                                Continue For
                            End If

                            Dim item As New ToolStripMenuItem(folder.Title, Nothing, New EventHandler(AddressOf tsmiAddPhotoToLib))
                            item.Tag = folder

                            Me.tsmiAddToPhotoLib.DropDownItems.AddRange(New ToolStripItem() {item})
                        End If
                    Next
                Else
                    For Each folder As ImageAlbum In Me.mPhotoDB.PhotoAlbumList.Albums
                        If folder.IsMaster = False AndAlso curFolder.FolderID <> folder.FolderID Then
                            Dim item As New ToolStripMenuItem(folder.Title, Nothing, New EventHandler(AddressOf tsmiAddPhotoToLib))
                            item.Tag = folder

                            Me.tsmiAddToPhotoLib.DropDownItems.AddRange(New ToolStripItem() {item})
                        End If
                    Next
                End If

                If Me.tsmiAddToPhotoLib.DropDownItems.Count > 0 Then
                    Me.tsmiAddToPhotoLib.Enabled = True
                Else
                    Me.tsmiAddToPhotoLib.Enabled = False
                End If

            Else
                Me.tsmiAddToPhotoLib.Enabled = False
                Me.tsmiRemoveFromPhotoLib.Enabled = False
            End If

            Me.cmsOperate.ResumeLayout()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmPhoto_OnMenuOpening")
        End Try
    End Sub

    Protected Overrides Sub OnImportFile()
        If Me.mButtonSelect = ButtonSelect.PhotoLib Then
            Me.AddPhoto()
        Else
            Me.UploadToPhone()
        End If
    End Sub

    Protected Overrides Sub OnRefresh()
        If Me.mLoadPhotoLibThread IsNot Nothing AndAlso Me.mLoadPhotoLibThread.ThreadState <> ThreadState.Stopped Then
            Try
                Me.mLoadPhotoLibThread.Abort()
            Catch ex As Exception
            End Try
        End If

        MyBase.OnRefresh()
    End Sub




    Protected Overrides Sub OnDelete()
        If Not Me.CheckSupport(OpType.Del) Then
            Return
        End If

        Dim lvw As ListView = Me.GetCurrentListView()

        If lvw.SelectedItems.Count = 0 Then
            Return
        End If

        Dim strMessage As String = ""

        '如果是 自拍 屏幕快照 全景算是照相机的分类不用提示照片图库的风险
        If Me.mDevice.VersionNumber >= 830 AndAlso Me.mButtonSelect = ButtonSelect.PhotoLib AndAlso Not Me.IsCameraLib Then
            strMessage = Me.Language.GetString("Photo.Message.DelectErrorMessage") & vbCrLf & vbCrLf
        End If

        Dim strName As String = lvw.SelectedItems(0).Text
        If String.IsNullOrEmpty(strName) Then
            strName = Path.GetFileName(lvw.SelectedItems(0).Name)
        End If
        Dim strType As String = IIf(Me.mButtonSelect = ButtonSelect.Video, Me.Language.GetString("Main.Button.Video"), Me.Language.GetString("Main.Button.Photo"))
        If lvw.SelectedItems.Count = 1 Then
            strMessage &= String.Format(Me.Language.GetString("Common.Message.DeleteMessageOneItem"), strName)
        Else
            If Me.mButtonSelect = ButtonSelect.Video Then
                strMessage &= String.Format(Me.Language.GetString("Common.Message.DeleteMessageMultiItem"), strName, lvw.SelectedItems.Count, Me.Language.GetString("Main.Button.Video"))
            Else
                strMessage &= String.Format(Me.Language.GetString("Photo.Message.DeleteMessageMultiPhoto"), strName, lvw.SelectedItems.Count)
            End If
        End If

        If Me.mDevice.VersionNumber < 500 AndAlso (Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video) Then
            Me.DeleteCameraDBFiles()

            '"如果您已打开了设备中的【照片】程序，" & vbCrLf & _
            '"请在操作完成后按【Home键】退出，" & vbCrLf & _
            '"并请双击【Home键】关闭后台的【照片】程序，" & vbCrLf & _
            '"以刷新照片数据!"
            strMessage &= vbCrLf & vbCrLf & Me.Language.GetString("Photo.Message.ClosePhotoInfo")

            If Me.mDevice.VersionNumber >= 500 Then
                strMessage &= vbCrLf & Me.Language.GetString("Photo.Message.RestarDevice")          '"如果图片出现模糊，请重启设备。"
            End If
        End If

        '删除的提示用户是否确定要删除,如果是
        'String.Format(strMessage, lvw.SelectedItems.Count)
        If tbMessageBox.Show(Me.mApplication, strMessage, _
                      Me.Language.GetString("Common.Info"), _
                      MessageBoxButtons.YesNo, _
                      MessageBoxIcon.Warning, _
                      MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.No Then
            Return
        End If

        Dim listItems As New List(Of ListViewItem)

        For Each item As ListViewItem In lvw.SelectedItems
            listItems.Add(item)
        Next
        Me.OnDeleteItems(lvw, listItems)
        Me.UpdateButtonStatus()
    End Sub

    Private Sub ReloadCameraInThread()
        Try
            Me.mcHelper = New CameraHelper()
            Dim result As Boolean = Me.mcHelper.Reload(Me.mDevice.OriginalDeviceName, Me.mDevice.SerialNumber)
            If Not result Then
                Common.LogException("iOS6加载相片失败")
            End If
        Catch ex As Exception
        End Try
    End Sub

    Protected Overrides Sub OnDeleteItems(ByVal lvw As ListView, ByVal listItems As List(Of ListViewItem), Optional ByRef isSucceed As Boolean = True)
        Try
            If Not Me.CheckSupport(OpType.Del) Then
                isSucceed = False
                Return
            End If
            Me.Enabled = False

            lvw.SuspendLayout()

            Dim index As Integer = -1
            If lvw.FocusedItem IsNot Nothing AndAlso lvw.FocusedItem.Index = 0 Then
                index = listItems(listItems.Count - 1).Index - 1
            End If

            'iOS6新导入的图片无法删除，因此出现删除失败。
            Dim lstFails As New List(Of String)

            For Each item As ListViewItem In listItems
                If Me.mDevice.VersionNumber >= 600 AndAlso (Me.mButtonSelect = ButtonSelect.Camera OrElse Me.IsCameraLib() OrElse Me.mButtonSelect = ButtonSelect.Video) Then
                    Me.ChangeUploadText(Me.Language.GetString("Photo.Message.DeletingPhoto"), False, False, 0) '"正在准备删除图片..."

                    If Me.mcHelper Is Nothing Then
                        If Me.mCamerHelperThread IsNot Nothing AndAlso Me.mCamerHelperThread.ThreadState <> ThreadState.Stopped Then
                            Try
                                Me.mCamerHelperThread.Abort()
                            Catch ex As Exception
                            End Try
                        End If
                        Common.Log("Init Camera1")
                        Try
                            Me.mCamerHelperThread = New Thread(AddressOf ReloadCameraInThread)
                            With Me.mCamerHelperThread
                                .IsBackground = True
                                .Start()
                            End With
                        Catch
                        End Try

                        While Me.mCamerHelperThread.ThreadState <> ThreadState.Stopped
                            Utility.WaitSeconds(0.5)
                        End While

                        '设备速度漫的时候还会出现不空
                    End If

                    If Me.mcHelper Is Nothing Then
                        Continue For
                    End If
                    Dim strItemName As String = item.Text
                    If String.IsNullOrEmpty(strItemName) Then
                        strItemName = Path.GetFileName(item.Name)
                    End If

                    'HEIC格式的图片获取的会是.JPG后缀的，删除的时候要把HEIC改为JPG，否则会找不到文件 
                    Dim result As Boolean = Me.mcHelper.Delete(strItemName.Replace(".HEIC", ".JPG"))

                    If Not result Then
                        '如果删除失败再用FileOriginalName删除一次
                        strItemName = Me.GetFileOriginalName(item)
                        result = Me.mcHelper.Delete(strItemName)
                    End If

                    If Not result AndAlso Not strItemName.Contains(".HEIC") Then
                        lstFails.Add(strItemName)
                        Continue For
                    End If

                    'iPhone6s 和6s plus 删除图片逻辑。
                    If Me.mDevice IsNot Nothing AndAlso Me.mDevice.IsLivePhotoDevice Then
                        'livePhoto删除视频就可以把图片删除掉
                        If strItemName.Contains(".HEIC") Then
                            result = Me.mcHelper.Delete(strItemName.Replace(".HEIC", ".MOV"))
                        Else
                            result = Me.mcHelper.Delete(strItemName.Replace(".JPG", ".MOV"))
                        End If
                    End If
                ElseIf Me.mDevice.VersionNumber >= 500 Then
                    Me.mPhotoThumb.RemovePhoto(item.SubItems(2).Tag)

                Else
                    If Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video Then
                        Dim strTHM As String = Me.GetCameraThumnailPath(item.Name, Me.mCurDeviceVersion)

                        Me.mDevice.DeleteFile(item.Name)
                        Me.mDevice.DeleteFile(strTHM)
                        Me.mDevice.DeleteFile(strTHM.Replace(".THM", ".BTH"))

                        If Me.mButtonSelect = ButtonSelect.Video Then
                            Me.mDevice.DeleteFile(strTHM.Replace(".THM", ".JPG"))
                        End If

                    ElseIf Me.mButtonSelect = ButtonSelect.PhotoLib Then
                        Me.mPhotoDB.iPod.DeletedPhotos.Add(item.Name)

                        Dim imgPod As IPodImage = item.SubItems(2).Tag

                        Me.mPhotoDB.RemovePhoto(imgPod)
                    End If
                End If

                lvw.Items.Remove(item)

                Try
                    Dim strFile As String = Me.mTempFolder
                    If Me.mButtonSelect = ButtonSelect.PhotoLib Then
                        strFile = Path.Combine(Me.mTempFolder, "Lib")
                    End If

                    strFile = Path.Combine(Me.mTempFolder, IIf(String.IsNullOrEmpty(item.Text), Path.GetFileName(item.Name), item.Text))

                    If String.IsNullOrEmpty(Path.GetExtension(item.Name)) Then
                        strFile &= ".JPG"
                    End If
                    If File.Exists(strFile) Then
                        File.Delete(strFile)
                    End If
                Catch ex As Exception
                End Try

                Application.DoEvents()
            Next

            If lvw.Items.Count > 0 AndAlso index >= 0 Then
                If lvw.Items.Count <= index Then
                    index = lvw.Items.Count - 1
                End If
                lvw.Items(index).EnsureVisible()
            End If

            lvw.ResumeLayout()



            If Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video OrElse Me.IsCameraLib() Then
                Me.tvwDevice.SelectedNode.NodeNumber = Me.GetCurrentListView().Items.Count
            ElseIf Me.mButtonSelect = ButtonSelect.PhotoLib Then
                For Each node As tbTreeNode In Me.tvwDevice.Nodes("Device").ChildNodes
                    Dim folder As Object = node.Tag
                    If folder Is Nothing Then
                        Continue For
                    End If

                    If Me.mDevice.VersionNumber >= 500 Then
                        node.NodeNumber = folder.Count
                    Else
                        node.NodeNumber = folder.ImageCount
                    End If
                Next
                For Each node As tbTreeNode In Me.mPhotoLibNode.ChildNodes
                    Dim folder As Object = node.Tag
                    If folder Is Nothing Then
                        Continue For
                    End If

                    If Me.mDevice.VersionNumber >= 500 Then
                        node.NodeNumber = folder.Count
                    Else
                        node.NodeNumber = folder.ImageCount
                    End If
                Next

            End If

            If Me.mblnImageDelete = False Then
                If Me.mDevice.VersionNumber >= 500 OrElse (Me.mDevice.VersionNumber >= 400 AndAlso Me.mButtonSelect = ButtonSelect.PhotoLib) Then
                    Me.Sync(True)
                ElseIf Me.mDevice.VersionNumber >= 400 AndAlso (Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video) Then
                    Me.DeleteCameraDBFiles()
                End If
            End If

            Me.Enabled = True
            Me.UpdateListViewItemCount(lvw.Items.Count)

            Dim count As Integer = lstFails.Count
            If count > 0 Then
                isSucceed = False
                Dim strName As String = String.Empty
                If count <= 3 Then
                    For Each Item As String In lstFails
                        strName = strName + Item + vbCrLf
                    Next
                Else
                    strName = lstFails(0) + vbCrLf + lstFails(1) + vbCrLf + "......" + vbCrLf + lstFails(count - 1) + vbCrLf
                End If

                '"{0}共{1}张图片删除失败,请重新拔插设备，再尝试删除。"
                Dim strMessage As String = String.Format(Me.Language.GetString("Photo.Message.DeleteFailed"), strName, count)

                If Me.mcHelper IsNot Nothing AndAlso Me.mcHelper.CheckLoadDeviceSucceed() Then
                    '加载照相机设备没权限，提示用户。
                    '{0}共{1}张图片删除失败。
                    '您没有删除此项目的权限，请在设备上尝试删除。"
                    strMessage = String.Format(Me.Language.GetString("Photo.Message.DeleteFailedNoRight"), strName, count)
                End If

                Me.mfrmErrorMessage = New tbMessageBox(strMessage, Me.Language.GetString("Common.Info"), _
                                                       MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1)
                If Me.mfrmErrorMessage IsNot Nothing Then
                    Dim owner As Form = Me
                    If Me.mblnImageDelete = True Then
                        owner = frmImageView
                    End If
                    Me.mfrmErrorMessage.Show(owner)
                End If

            End If

            If Me.mcHelper IsNot Nothing Then
                Me.mcHelper.Dispose()
                Me.mcHelper = Nothing
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnDeleteItems")
        End Try
    End Sub

    Private Function GetFileOriginalName(ByVal item As ListViewItem)
        Dim strName As String = item.Text
        Try
            If item.SubItems(2).Tag IsNot Nothing AndAlso TypeOf item.SubItems(2).Tag Is PhotoItem Then
                strName = CType(item.SubItems(2).Tag, PhotoItem).OriginalFileName
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmPhoto_GetFileOriginalName")
        End Try
        Return strName
    End Function

    Protected Overrides Sub UploadToPhone()
        If Me.mDevice.VersionNumber >= 600 AndAlso Me.mButtonSelect = ButtonSelect.Camera Then
            Me.UploadPhotoForiOS6(New KeyValuePair(Of ImportSource, Array)(ImportSource.File, Nothing))

        ElseIf Me.mDevice.VersionNumber >= 600 AndAlso Me.mButtonSelect = ButtonSelect.Video Then
            Me.UploadVideoForiOS6()

        Else
            If Not Me.CheckSupport(OpType.Upload) Then
                Return
            End If

            Me.UploadToPhoneForFile()
        End If
    End Sub

    Private Sub UploadToPhoneForFile()
        Dim fileDlg As OpenFileDialog = New OpenFileDialog()
        fileDlg.Multiselect = True
        fileDlg.Title = Me.Language.GetString("Media.Button.Import")         '"导入"
        Select Case Me.mButtonSelect
            Case ButtonSelect.Video
                fileDlg.FileName = Me.Language.GetString("Setting.Button.SelectVideo")                                          '"选择视频"
                fileDlg.Filter = Me.Language.GetString("Setting.Label.VideoFile") & "(*.MOV;*.MP4;*.M4V)|*.MOV;*.MP4;*.M4V"     '视频文件

            Case Else
                fileDlg.FileName = Me.Language.GetString("Music.OpenFileDialog.SelectPhoto")                                    '"选择图片"
                fileDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") & "(*.PNG;*.JPG;*.JPEG;*.GIF;*.BMP;*.HEIC)|*.PNG;*.JPG;*.JPEG;*.GIF;*.BMP;*.HEIC"                 '图像文件

        End Select

        If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
            Dim listItem As New List(Of String)

            For Each Item As String In fileDlg.FileNames
                listItem.Add(Item)

                If CheckNotSupportHeic(Item) Then
                    Return
                End If
            Next

            Me.UploadToPhoneThread(listItem)
        End If
    End Sub

    Private Function CheckNotSupportHeic(ByVal strFile As String) As Boolean
        Dim blnNotSupport As Boolean = False
        Try
            Dim strPath As String = strFile.ToLower()
            If strPath.EndsWith(".heic") OrElse strPath.EndsWith(".heif") Then
                blnNotSupport = True
                '导入失败，导入的文件包含暂不支持的格式。" & "\r\n" & "您可以使用转换工具转换格式，是否使用?
                If tbMessageBox.Show(Me, Me.Language.GetString("Media.Message.ImportFailedFirstConvert"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2) = Windows.Forms.DialogResult.OK Then
                    If Me.mPhotoConvert Is Nothing OrElse Me.mPhotoConvert.IsDisposed Then
                        Me.mPhotoConvert = New frmPhotoConvert(Me.mApplication, Me.mDevice)
                        Me.mPhotoConvert.StartPosition = FormStartPosition.CenterParent
                    End If

                    Me.mPhotoConvert.Show()
                    Me.mPhotoConvert.BringToFront()
                End If
                Return blnNotSupport
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckNotSupportHeic")
        End Try
        Return blnNotSupport
    End Function

    Protected Overrides Sub UploadFolderToPhone()

        If Me.mDevice.VersionNumber >= 600 AndAlso Me.mButtonSelect = ButtonSelect.Camera Then
            Me.UploadPhotoForiOS6(New KeyValuePair(Of ImportSource, Array)(ImportSource.Folder, Nothing))

        ElseIf Me.mDevice.VersionNumber >= 600 AndAlso Me.mButtonSelect = ButtonSelect.Video Then
            Me.UploadVideoForiOS6()

        Else
            If Not Me.CheckSupport(OpType.Upload) Then
                Return
            End If

            Me.UploadToPhoneForFolder()
        End If

    End Sub

    Private Sub UploadToPhoneForFolder()
        Dim fbd As New FolderBrowserDialog
        fbd.ShowNewFolderButton = False

        If fbd.ShowDialog = Windows.Forms.DialogResult.OK Then
            Dim lstName As New List(Of String)
            lstName.Add(fbd.SelectedPath)

            If Me.mButtonSelect = ButtonSelect.Video Then
                Me.UploadArrayToPhoneByVedio(lstName.ToArray)
            Else
                '判断是否包含heic格式
                Try
                    For Each Item As String In Directory.GetFiles(fbd.SelectedPath, "*.*", SearchOption.AllDirectories)
                        If CheckNotSupportHeic(Item) Then
                            Return
                        End If
                    Next
                Catch ex As Exception

                End Try
                Me.UploadArrayToPhone(lstName.ToArray)
            End If

        End If
    End Sub

    Private Sub OnAddPhotoLib(ByVal arrFiles As Array)
        If arrFiles Is Nothing OrElse arrFiles.Length = 0 Then
            Return
        End If

        Me.mButtonSelect = ButtonSelect.PhotoLib

        If Me.mSyncPhoto Then
            For Each Item As String In arrFiles
                Me.mWaitListOfPhotoLibFiles.Add(Item)
            Next
            Return
        End If

        If Not Me.CheckSupport() Then
            Return
        End If

        If Me.mDevice.VersionNumber >= 500 Then
            Me.tvwDevice.SelectedNode = Me.mPhotoLibNode
            Me.LoadPhoneLib_5X(False)
        Else
            Me.tvwDevice.SelectedNode = Me.mPhotoLibNode
            Me.LoadPhoneLib(False)
        End If

        Dim lstFiles As New List(Of String)
        For Each Item As String In arrFiles
            lstFiles.Add(Item)
        Next

DO_SYNC_WAITING:

        If Me.mWaitListOfPhotoLibFiles.Count > 0 Then
            lstFiles.AddRange(Me.mWaitListOfPhotoLibFiles)
            Me.mWaitListOfPhotoLibFiles.Clear()
        End If

        If lstFiles.Count = 0 Then
            Return
        End If

        Application.DoEvents()
        Me.AddPhotoOK(lstFiles.ToArray(), Nothing, True)
        lstFiles.Clear()

        If Me.mWaitListOfPhotoLibFiles.Count > 0 Then
            GoTo DO_SYNC_WAITING
        End If
    End Sub

    Private Sub OnAddWallpaper(ByVal sender As Object, ByVal e As FuncFilesEventArgs)
        Common.LogException(String.Format("OnAddWallpaper(sender:{0})", sender))
        Me.mButtonSelect = ButtonSelect.PhotoLib

        If Not Me.Visible Then
            Me.Show()
        End If

        If Me.mSyncPhoto Then
            Me.mWaitListOfMusicFiles.AddRange(e.Files)

            Return
        End If

        If Not Me.CheckSupport(OpType.Upload, ImportSource.DragDrop, e.Files.ToArray) Then
            Return
        End If

        Me.mIsSelectTbWallpaper = True

        If Me.mDevice.VersionNumber >= 500 Then
            Me.tvwDevice.SelectedNode = Me.GetTongbuWallpaperNode_5X()
            Me.AddTreeNodeByNewPhotoLib(Nothing)
            Me.LoadPhoneLib_5X(False)
        Else
            Me.tvwDevice.SelectedNode = Me.GetTongbuWallpaperNode()
            Me.AddTreeNodeByNewPhotoLib(Nothing)
            Me.LoadPhoneLib(False)
        End If

        'Me.AddPhotoOK(e.Files.ToArray(), Nothing, True)
        'Me.Sync()

        '同步壁纸
        Dim addFiles As New List(Of String)
        addFiles.AddRange(e.Files)

DO_SYNC_WAITING:
        If Me.mWaitListOfMusicFiles.Count > 0 Then
            addFiles.AddRange(mWaitListOfMusicFiles)
            mWaitListOfMusicFiles.Clear()
        End If

        If addFiles.Count = 0 Then
            Return
        End If

        'Dim selectNode As tbTreeNode = Nothing
        'Dim strText As String = Me.Language.GetString("Device.iPod.Wallpaper.Label.AddingToPhotoLib")       '"正在添加 {0} 张图片到  {1}  的照片图库"
        'Me.mApplication.ShowMainTipForm(String.Format(strText, addFiles.Count, Me.mDevice.DeviceName))
        Application.DoEvents()

        If Me.mlstAddedWallpaper Is Nothing Then
            Me.mlstAddedWallpaper = New List(Of String)
        End If

        '添加过的壁纸不再添加
        For Each Item As String In Me.mlstAddedWallpaper
            If addFiles.Contains(Item) Then
                addFiles.Remove(Item)
            End If
        Next
        '添加入设备的都记录下来

        If addFiles.Count > 0 Then
            For Each Item As String In addFiles
                Me.mlstAddedWallpaper.Add(Item)
            Next
        End If

        Me.AddPhotoOK(addFiles.ToArray(), Nothing, True)

        addFiles.Clear()

        If mWaitListOfMusicFiles.Count > 0 Then
            GoTo DO_SYNC_WAITING
        End If
    End Sub

    Protected Overrides Sub OnImageViewClosing()
        '启动一个线程同步照片图库，不然关闭大图窗体会卡住
        If Me.mblnImageDelete = True Then
            Dim thr As New Thread(AddressOf SyncThread)
            With thr
                .IsBackground = True
                .Start()
            End With
        End If
        MyBase.OnImageViewClosing()
    End Sub

    Private Delegate Sub SyncThreadHandler()
    Private Sub SyncThread()
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New SyncThreadHandler(AddressOf SyncThread))
            Else
                If Me.mDevice.VersionNumber >= 500 OrElse (Me.mDevice.VersionNumber >= 400 AndAlso Me.mButtonSelect = ButtonSelect.PhotoLib) Then
                    Me.Sync()
                ElseIf Me.mDevice.VersionNumber >= 400 AndAlso (Me.mButtonSelect = ButtonSelect.Camera OrElse Me.mButtonSelect = ButtonSelect.Video) Then
                    Me.DeleteCameraDBFiles()
                End If
            End If
        Catch ex As Exception
        End Try
    End Sub

    Protected Overrides Sub BeforeFormShow()
        MyBase.BeforeFormShow()
        Try
            If Me.mThreadLoadPicCount IsNot Nothing AndAlso Me.mThreadLoadPicCount.ThreadState <> ThreadState.Stopped Then
                Me.mThreadLoadPicCount.Abort()
            End If
        Catch ex As Exception
        End Try

    End Sub

    Public Overrides Sub GoToLocalNode(ByVal actType As Components.ActionFuncType)
        MyBase.GoToLocalNode(actType)

        If Me.mDevice IsNot Nothing AndAlso Me.mDevice.ProductType.ToLower.StartsWith("ipad") AndAlso Me.mNodeLocal.ChildNodes("ipad") IsNot Nothing Then
            If Me.mNodeLocal.ChildNodes("ipad") IsNot Nothing Then
                Me.tvwDevice.SelectedNode = Me.mNodeLocal.ChildNodes("ipad")
            End If
        ElseIf Me.mNodeLocal.ChildNodes("iphone") IsNot Nothing Then
            Me.tvwDevice.SelectedNode = Me.mNodeLocal.ChildNodes("iphone")
        End If
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Function GetNodeUrl(ByVal node As tbTreeNode) As String
        Dim strUrl As String = String.Empty
        If node IsNot Nothing AndAlso node.Tag IsNot Nothing AndAlso TypeOf node.Tag Is WebSiteObject Then
            strUrl = CType(node.Tag, WebSiteObject).Url
        End If
        Return strUrl
    End Function

    Private Sub tvwDevice_SelectedIndexChanged(ByVal sender As System.Object, ByVal e As tbTreeView_SelectedIndexChanged_EventArgs) Handles tvwDevice.SelectedIndexChanged

        If e.Node.Checked AndAlso e.Node.Name = "WebWallpaperLivePhotos" Then
            e.Cancel = True
            e.Node.NodeShowDot = False
            IniSetting.SetShowLivePhotoWebDot()
            ActionCollectHelper.ClickDeviceHomePageChild(Me.mDevice, ModelKey.LivePhotosWeb)
            Dim strUrl As String = String.Empty
            strUrl = Me.GetNodeUrl(e.Node)
            If String.IsNullOrEmpty(strUrl) Then
                strUrl = "https://s.weibo.com/weibo?q=%23%E6%8E%A8livephotos%23&from=default"
            End If
            Common.OpenExplorer(strUrl)  ' http://gamebbs.tongbu.com/thread-421999-1-1.html
            Return
        End If

        If Common.VerIs30 AndAlso frmSite.SelectNode(e.Node, Me.mUsbDev) Then
            Me.lblFileCount.Text = ""
            Return
        End If

        If e.Node.Checked Then
            If e.Node.Name = "WallPaper" Then
                If Me.mFormWallpaper Is Nothing Then
                    Me.mFormWallpaper = New frmWallpaper(Me.mApplication, Me.mDevice)
                End If
                Me.mFormWallpaper.Show()
                Me.mFormWallpaper.BringToFront()
                Me.mPanelForm.Visible = True
                Me.pnlTop.Visible = False
                Application.DoEvents()
                Me.lblFileCount.Text = Me.mFormWallpaper.lblFileCount.Text
                Me.lblFileCount.TextAlign = Me.mFormWallpaper.lblFileCount.TextAlign

            ElseIf e.Node.Name = "PhotoLib.New" Then
                e.Node.StartEdit()

            ElseIf e.Node.Name = "iphone" Then
                Me.mFormWallpaperLocal.Show()
                Me.mFormWallpaperLocal.BringToFront()
                Me.mPanelForm.Visible = True
                Me.pnlTop.Visible = False
                Application.DoEvents()
                Me.mFormWallpaperLocal.SwitchWallpaperType(WallpaperLoaclType.iPhone, e.Node)

            ElseIf e.Node.Name = "ipad" Then
                Me.mFormWallpaperLocal.Show()
                Me.mFormWallpaperLocal.BringToFront()
                Me.mPanelForm.Visible = True
                Me.pnlTop.Visible = False
                Application.DoEvents()
                Me.mFormWallpaperLocal.SwitchWallpaperType(WallpaperLoaclType.iPad, e.Node)

            ElseIf e.Node.Name = "Android" Then
                Me.mFormWallpaperLocal.Show()
                Me.mFormWallpaperLocal.BringToFront()
                Me.mPanelForm.Visible = True
                Me.pnlTop.Visible = False
                Application.DoEvents()
                Me.mFormWallpaperLocal.SwitchWallpaperType(WallpaperLoaclType.Android, e.Node)

            Else
                Me.mPanelForm.Visible = False
                Me.pnlTop.Visible = True
                Me.pnlTop.Refresh()
                Me.pnlTop.BringToFront()
                Me.pnlContainer.BringToFront()
                Me.mButtonSelect = Me.GetButtonSelect()
                Me.SwitchListView(e.Node.Name)

                'Application.DoEvents()

                Me.lblInfo.Visible = False
                Me.btnImport.Visible = True
                If Me.mDevice.VersionNumber >= 600 AndAlso e.Node.Name.StartsWith("PhotoLib.") Then
                    Dim album As AlbumItem = Me.tvwDevice.SelectedNode.Tag
                    If album IsNot Nothing AndAlso album.Kind = pAlbumKind.AlbumPhone Then
                        Me.lblInfo.Visible = True
                        Me.btnImport.Visible = False
                    End If
                End If

                Dim folder As AlbumItem = e.Node.Tag
                If folder IsNot Nothing Then
                    Select Case folder.Kind
                        Case pAlbumKind.SlowMotion, pAlbumKind.TimeLap, pAlbumKind.Hidden, pAlbumKind.Delete
                            Me.btnImport.Visible = False
                            Me.btnDelete.Visible = False

                            Me.tsmiImportFile.Visible = False
                            Me.tsmiImportFolder.Visible = False
                            Me.tsmiDelete.Visible = False
                            Me.tsmiRemoveFromPhotoLib.Visible = False
                            Me.tsmiAddToPhotoLib.Visible = False
                            Me.tss2.Visible = False
                        Case pAlbumKind.Panoramic, pAlbumKind.Selfie
                            Me.btnImport.Visible = True
                            Me.btnDelete.Visible = True
                            Me.btnImport.Enabled = False

                        Case Else
                            Me.btnImport.Visible = True
                            Me.btnDelete.Visible = True

                            Me.tsmiImportFile.Visible = True
                            Me.tsmiImportFolder.Visible = True
                            Me.tsmiDelete.Visible = True
                            Me.tsmiRemoveFromPhotoLib.Visible = True
                            Me.tsmiAddToPhotoLib.Visible = True
                            Me.tss2.Visible = True

                    End Select
                Else
                    Me.btnImport.Visible = True
                    Me.btnDelete.Visible = True

                    Me.tsmiImportFile.Visible = True
                    Me.tsmiImportFolder.Visible = True
                    Me.tsmiDelete.Visible = True
                End If

                If e.Node.Name.StartsWith("Camera") Then
                    Me.btnImportLivePhoto.Visible = True
                    Me.btnImportLivePhoto.Enabled = True
                    'If Me.mDevice.IsLivePhotoDevice OrElse Me.mDevice.VersionNumber >= 910 Then
                    '    Me.btnImportLivePhoto.Enabled = True
                    'Else
                    '    Me.btnImportLivePhoto.Enabled = False
                    'End If
                Else
                    Me.btnImportLivePhoto.Visible = False
                End If

                Dim intLeft As Integer = 0
                If Me.btnImportLivePhoto.Visible Then
                    intLeft = Me.btnImportLivePhoto.Right
                ElseIf Me.btnDelete.Visible Then
                    intLeft = Me.btnDelete.Right
                Else
                    intLeft = Me.btnExport.Right
                End If

                'Me.btnRefresh.Location = New Point(intLeft + 10, Me.btnRefresh.Top)
            End If
        End If

        '强制隐藏导入功能
        Me.HideImportFileButton()
    End Sub

    Private Sub tvwDevice_DischargeButtonClick(ByVal sender As System.Object, ByVal e As tbTreeView_DischargeButtonClick_EventArgs) Handles tvwDevice.DischargeButtonClick
        Me.DeletePhotoFolder(e.Node)
    End Sub

    Private Sub tsmiAddPhotoToLib(ByVal sender As Object, ByVal e As EventArgs)
        Dim folder As Object = sender.Tag

        For Each item As ListViewItem In Me.GetCurrentListView().SelectedItems
            Dim photo As Object = item.SubItems(2).Tag

            If Me.mDevice.VersionNumber >= 500 Then
                If Not CType(folder, AlbumItem).Assets.Contains(CType(photo, PhotoItem).ID) Then
                    Me.mPhotoThumb.AddPhotoToLib(folder, photo)
                End If
            Else
                Me.mPhotoDB.RemovePhotoFromLib(folder, photo)
                Me.mPhotoDB.AddPhotoToLib(folder, photo)

            End If
        Next

        If Me.mDevice.VersionNumber >= 500 Then
            Me.tvwDevice.Nodes("PhotoLib." & folder.ID).NodeNumber = folder.Count
        Else
            Me.tvwDevice.Nodes("PhotoLib." & folder.FolderID).NodeNumber = folder.ImageCount
        End If

        Me.Sync()
    End Sub

    Private Sub tsmiRemoveFromPhotoLib_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles tsmiRemoveFromPhotoLib.Click
        If Not Me.CheckSupport(OpType.Del) Then
            Return
        End If

        Dim folder As Object = Me.tvwDevice.SelectedNode.Tag
        Dim listDelete As New List(Of ListViewItem)
        Dim lvw As tbListViewEx = Me.GetCurrentListView()

        For Each item As ListViewItem In lvw.SelectedItems
            Dim photo As Object = item.SubItems(2).Tag
            If Me.mDevice.VersionNumber >= 500 Then
                Me.mPhotoThumb.RemovePhotoFromLib(folder, photo)
            Else
                Me.mPhotoDB.RemovePhotoFromLib(folder, photo)
            End If
            listDelete.Add(item)
        Next

        lvw.SuspendLayout()
        For Each item As ListViewItem In listDelete
            lvw.Items.Remove(item)
        Next
        lvw.ResumeLayout()

        If Me.mDevice.VersionNumber >= 500 Then
            Me.tvwDevice.Nodes("PhotoLib." & folder.ID).NodeNumber = folder.Count
        Else
            Me.tvwDevice.Nodes("PhotoLib." & folder.FolderID).NodeNumber = folder.ImageCount
        End If

        If lvw.Items.Count <= 0 Then
            Me.btnSelectAll.Enabled = False
        Else
            Me.btnSelectAll.Enabled = True
        End If

        Me.Sync()
    End Sub

    '清空线程池
    Protected Overrides Sub BeforeFormClose(ByRef pblnCancelClose As Boolean)
        MyBase.BeforeFormClose(pblnCancelClose)

        Try
            If Me.mPhotoThumb IsNot Nothing Then
                Me.mPhotoThumb.Dispose()
            End If

            If mCameraListView IsNot Nothing Then
                Me.mCameraListView.ResponseIndex = -1
                Me.mVideoListView.ResponseIndex = -1
            End If

            For Each thd As Thread In Me.mDicThread.Values
                Try
                    If thd IsNot Nothing AndAlso thd.ThreadState <> ThreadState.Stopped Then
                        thd.Abort()
                    End If
                    Application.DoEvents()
                Catch ex As Exception
                End Try
            Next

            Me.mDicThread.Clear()
        Catch
        End Try

        Try
            If Me.mfrmMessage IsNot Nothing Then
                Me.mfrmMessage.Close()
                Me.mfrmMessage = Nothing
            End If
        Catch ex As Exception
        End Try

        Try
            If Me.bgwLoadData IsNot Nothing AndAlso Me.bgwLoadData.IsBusy Then
                Me.bgwLoadData.CancelAsync()
            End If
        Catch ex As Exception
        End Try

        Me.DisposeAddPhotoOKThread()
    End Sub

    Private Sub OnDeviceNameChanged(ByVal sender As Object, ByVal e As EventArgs)
        Dim strDevice As String = String.Format("{0} - {1}", Me.Language.GetString("App.TreeNode.Device"), Me.mDevice.DeviceName)
        Me.mNodeDevice.Text = strDevice
    End Sub

#End Region

#Region "--- 内部函数 ---"

    Private Sub HideImportFileButton()
        Me.btnImport.Visible = False
        Me.btnImportLivePhoto.Visible = False
        Me.tsmiImportFile.Visible = False
        Me.tsmiImportFolder.Visible = False
        Me.tsmiImportFileButton.Visible = False
        Me.tsmiImportFolderButton.Visible = False
    End Sub

    Private Delegate Sub UpdateCameraCountHandler(ByVal countCamera As Integer, ByVal countScreenShot As Integer, ByVal countVideo As Integer)
    Private Sub UpdateCameraCount(ByVal countCamera As Integer, ByVal countScreenShot As Integer, ByVal countVideo As Integer)
        If Me.tvwDevice.InvokeRequired Then
            Me.tvwDevice.Invoke(New UpdateCameraCountHandler(AddressOf UpdateCameraCount), countCamera, countScreenShot, countVideo)
        Else
            Me.tvwDevice.Nodes("Camera").NodeNumber = countCamera
            Me.tvwDevice.Nodes("ScreenShot").NodeNumber = countScreenShot
            Me.tvwDevice.Nodes("Video").NodeNumber = countVideo
        End If
    End Sub

    Private Sub LoadFromCameraThread()
        Try
            Dim dtLast As DateTime = Now

            If Me.mButtonSelect = ButtonSelect.Camera Then
                Me.mCameraDataLoad = False
            ElseIf Me.mButtonSelect = ButtonSelect.Screenshot Then
                Me.mScreenShotDataLoad = False
            Else
                Me.mVideoDataLoad = False
            End If

            Dim strCameraFolder As String = Me.mDevice.iPath.CameraFolder.TrimEnd("/")

            Dim info As iPhoneFileInfo = Nothing
            Dim lstFilesJpg As New List(Of ListViewItem)
            Dim lstFilesPng As New List(Of ListViewItem)
            Dim lstFilesMov As New List(Of ListViewItem)

            If Me.mDevice.VersionNumber >= 500 Then
                Me.LoadItems_5X(Me.mPhotoThumb.GetItems(Nothing), lstFilesJpg, lstFilesPng, lstFilesMov)
            Else
                Me.mCameraFolder = Me.mDevice.iPath.Camera100APPLEFolder

                Dim strIMPRTDirectories As New List(Of String)
                Dim strAPPLEDirectories As New List(Of String)
                For Each strFolderName As String In Me.mDevice.GetDirectories(strCameraFolder)
                    If strFolderName.Contains("IMPRT") Then
                        strIMPRTDirectories.Add(strFolderName)
                    End If

                    If strFolderName.Contains("APPLE") Then
                        strAPPLEDirectories.Add(strFolderName)
                    End If
                Next

                '获取从SD卡导入的高清图片
                For Each strFolderName As String In strIMPRTDirectories
                    Me.LoadItems(strCameraFolder, strFolderName, lstFilesJpg, lstFilesMov, info)
                Next

                '获取设备的照相机图片和视频
                For Each strFolderName As String In strAPPLEDirectories
                    Me.LoadItems(strCameraFolder, strFolderName, lstFilesJpg, lstFilesMov, info)
                Next
            End If

            '加载的时候设置控件不可用。
            Me.SetControlStatus(False)

            If Me.mCameraDataLoad = False Then
                Me.mCameraDataLoad = True

                'Me.ClearListView(Me.mCameraListView)
                Me.AddItemToListView(Me.mCameraListView, lstFilesJpg, False, True)
            End If

            If Me.mScreenShotDataLoad = False Then
                Me.mScreenShotDataLoad = True

                'Me.ClearListView(Me.mScreenShotListView)
                Me.AddItemToListView(Me.mScreenShotListView, lstFilesPng, False, True)
            End If

            If Me.mVideoDataLoad = False Then
                Me.mVideoDataLoad = True

                'Me.ClearListView(Me.mVideoListView)
                Me.AddItemToListView(Me.mVideoListView, lstFilesMov, False, True)
            End If

            Me.SetControlStatus(True)

            Me.UpdateCameraCount(lstFilesJpg.Count, lstFilesPng.Count, lstFilesMov.Count)
            Me.UpdateButtonStatus()

            If Me.mButtonSelect = ButtonSelect.Camera Then
                Me.UpdateListViewItemCount(lstFilesJpg.Count)

            ElseIf Me.mButtonSelect = ButtonSelect.Screenshot Then
                Me.UpdateListViewItemCount(lstFilesPng.Count)

            Else
                Me.UpdateListViewItemCount(lstFilesMov.Count)

            End If

            If Me.mCurrentView = View.LargeIcon Then
                Me.BeginInvoke(New UpdateThumbnailHandler(AddressOf UpdateThumbnailCamera), CType(New Object() {Me.GetCurrentListView(), Me.mButtonSelect}, Object))
            End If

            Debug.Print("加载照片的时间是：" & Now.Subtract(dtLast).TotalMilliseconds)
        Catch ex As Exception
            Me.SetControlStatus(True)
            Common.Log("LoadFromCameraThread" & vbTab & ex.ToString())
        End Try
    End Sub

    Private Sub LoadItems(ByVal strCameraFolder As String, ByVal strFolderName As String, ByVal lstFilesJpg As List(Of ListViewItem), ByVal lstFilesMov As List(Of ListViewItem), ByVal info As iPhoneFileInfo)
        Dim strFolder As String = strCameraFolder & "/" & strFolderName
        Dim strFiles() As String = Me.mDevice.GetFiles(strFolder, True)

        Me.mCameraMaxIndex = 0
        Me.mCameraFolder = strFolder
        For Each strFileName As String In strFiles
            Dim strExtension As String = Path.GetExtension(strFileName).ToUpper()

            Select Case strExtension
                Case ".JPG", ".PNG", ".GIF"
                    If strFileName.Length = 12 Then
                        '取得当前目录下面相片名称序号最大的值
                        Dim intCurrentValue As Integer = Val(strFileName.Substring(4, 4))
                        If Me.mCameraMaxIndex < intCurrentValue Then
                            mCameraMaxIndex = intCurrentValue
                        End If
                    End If

                    Dim strPic As String = strFolder & "/" & strFileName
                    If Me.mDevice.IsDirectory(strPic, info) = False Then
                        lstFilesJpg.Add(Me.CreateListViewItem_4X(strPic, info, Me.mCurrentView = View.LargeIcon, True))
                    End If

                Case ".MOV", ".MP4", ".M4V"
                    If strFileName.Length = 12 Then
                        '取得当前目录下面相片名称序号最大的值
                        Dim intCurrentValue As Integer = Val(strFileName.Substring(4, 4))
                        If Me.mCameraMaxIndex < intCurrentValue Then
                            mCameraMaxIndex = intCurrentValue
                        End If
                    End If

                    Dim strPic As String = strFolder & "/" & strFileName
                    If Me.mDevice.IsDirectory(strPic, info) = False Then
                        lstFilesMov.Add(Me.CreateListViewItem_4X(strPic, info, Me.mCurrentView = View.LargeIcon, True))
                    End If

            End Select
        Next
    End Sub

    Private Sub LoadItems_5X(ByVal list As List(Of PhotoItem), ByVal lstFilesJpg As List(Of ListViewItem), ByVal lstFilesPng As List(Of ListViewItem), ByVal lstFilesMov As List(Of ListViewItem))

        Dim isLarge As Boolean = (Me.mCurrentView = View.LargeIcon)
        For Each pItem As PhotoItem In list
            Dim item As ListViewItem = Me.AddPhotoListViewItem_5X(pItem)
            'ios9及以上设备从数据库里面判断是不是 屏幕快照。
            '目前屏幕快照只显示照相机里面的，照片图库里面的不显示出来。
            If Me.mDevice.VersionNumber >= 900 AndAlso pItem.TypeSub = pItemTypeSub.ScreenShot Then
                lstFilesJpg.Add(item)
                lstFilesPng.Add(Me.AddPhotoListViewItem_5X(pItem))
                Continue For
            End If

            Select Case pItem.Type
                Case pItemType.JPG, pItemType.GIF
                    lstFilesJpg.Add(item)
                Case pItemType.PNG
                    lstFilesJpg.Add(item)
                    If Me.mDevice.VersionNumber < 900 Then
                        lstFilesPng.Add(Me.AddPhotoListViewItem_5X(pItem))
                    End If
                Case Else
                    lstFilesMov.Add(item)
            End Select
        Next
    End Sub

    Private Sub LoadFromPhotoLibThread(ByVal state As Object)
        Dim lvw As tbListViewEx = Nothing

        Try
            lvw = state
            If lvw.Tag Is Nothing Then
                Return
            End If

            lvw.SuspendLayout()
            Dim lstLvwItemFiles As New List(Of ListViewItem)

            If Me.mDevice.VersionNumber >= 500 Then
                Dim imgAlbum As AlbumItem = lvw.Tag
                Dim listItem As List(Of PhotoItem) = Me.mPhotoThumb.GetItems(imgAlbum)
                For Each pItem As PhotoItem In listItem
                    lstLvwItemFiles.Add(Me.AddPhotoListViewItem_5X(pItem))
                Next
            Else
                Dim imgAlbum As ImageAlbum = lvw.Tag
                For Each imgItem As IPodImage In imgAlbum.Images
                    lstLvwItemFiles.Add(Me.AddPhotoListViewItem(imgItem))
                Next
            End If

            'Me.ClearListView(lvw)
            Me.AddItemToListView(lvw, lstLvwItemFiles, False, True)
            Me.UpdateButtonStatus()
            Me.UpdateListViewItemCount(lstLvwItemFiles.Count)

            Application.DoEvents()

            If Me.mCurrentView = View.LargeIcon AndAlso Me.IsHandleCreated Then
                Me.BeginInvoke(New UpdateThumbnailHandler(AddressOf UpdateThumbnailPhotoLib), lvw)
            End If

        Catch ex As Exception
            Common.Log("LoadFromPhotoLibThread" & vbTab & ex.ToString())
        Finally
            If lvw IsNot Nothing Then
                lvw.ResumeLayout()
            End If
        End Try
    End Sub

    Protected Overrides Sub UpdateThumbnailPhotoLib(ByVal objListView As Object)
        If Me.InvokeRequired Then
            Me.Invoke(New UpdateThumbnailHandler(AddressOf UpdateThumbnailPhotoLib), objListView)
        Else
            Try

                Dim lvw As tbListView = objListView

                lvw.ScrollBar = True
                lvw.ResponseIndex += 1

                Dim listItems As List(Of ListViewItem) = Nothing
                Dim intResponseIndex As Integer = lvw.ResponseIndex

DO_RELOAD:
                If listItems IsNot Nothing Then
                    listItems.Clear()
                End If
                listItems = Me.GetItems(lvw)
                For Each item As ListViewItem In listItems

                    '退出壁纸缩略图下载
                    If Me.mButtonSelect <> ButtonSelect.PhotoLib OrElse intResponseIndex <> lvw.ResponseIndex Then
                        Return
                    End If

                    Application.DoEvents()

                    '用户滚动ListView
                    If lvw.ScrollBar Then
                        lvw.ScrollBar = False

                        GoTo DO_RELOAD
                    End If

                    If lvw.LargeImageList.Images.ContainsKey(item.Name) AndAlso lvw.View = View.LargeIcon Then
                        If item.ImageKey <> item.Name Then
                            item.ImageKey = item.Name
                        End If
                        Continue For
                    End If

                    Dim imgLargeIcon As Image = Nothing
                    If Me.mDevice.VersionNumber >= 500 Then
                        imgLargeIcon = Me.mPhotoThumb.GetThumbnail(item.Name)

                        If imgLargeIcon Is Nothing Then
                            Dim pItem As PhotoItem = item.SubItems(2).Tag
                            If pItem.Thumbnail IsNot Nothing Then
                                imgLargeIcon = pItem.Thumbnail
                            Else
                                If File.Exists(pItem.FilePathOnPC) Then
                                    If pItem.Type = pItemType.JPG OrElse pItem.Type = pItemType.PNG OrElse pItem.Type = pItemType.GIF Then
                                        imgLargeIcon = Utility.GetThumbnail(Utility.AdjustImage(Common.ImageFromFile(pItem.FilePathOnPC)), lvw.LargeImageList.ImageSize, True)
                                    Else
                                        If File.Exists(pItem.ThumbPathOnPC) Then
                                            imgLargeIcon = Utility.GetThumbnail(Utility.AdjustImage(Common.ImageFromFile(pItem.ThumbPathOnPC)), lvw.LargeImageList.ImageSize, True)
                                        End If
                                    End If
                                End If
                            End If
                        End If
                    Else
                        '如果是图片就进行处理缩略图
                        Dim format As IPodImageFormat = item.SubItems(0).Tag
                        Try
                            '4.3.3的固件会出现取不到iThumbnailFormat。只能从tag里取。
                            If format Is Nothing AndAlso item.Tag IsNot Nothing AndAlso item.Tag.Tag IsNot Nothing AndAlso TypeOf item.Tag.Tag Is IPodImage Then
                                format = CType(item.Tag.Tag, IPodImage).iThumbnailFormat
                            End If
                        Catch ex As Exception
                        End Try
                        imgLargeIcon = format.LoadFromFile()
                    End If

                    If imgLargeIcon IsNot Nothing Then
                        Dim imgSize As Size = lvw.LargeImageList.ImageSize
                        Dim imgCut As Image = imgLargeIcon
                        'imgCut = Utility.GetThumbnail(imgLargeIcon, imgSize, False, False)

                        '线程操作，有可能切换到小图标的时候线程还没有结束，所的加一个判断
                        If lvw.View = View.Details Then
                            Return
                        End If

                        lvw.LargeImageList.Images.Add(item.Name, imgCut)
                        Me.mdicThumbnail(item.Name) = Me.GetThumbnailImage(imgCut, lvw.LargeImageList.ImageSize)
                        item.ImageKey = item.Name
                    End If

                    Application.DoEvents()
                Next

                If listItems IsNot Nothing Then
                    listItems.Clear()
                End If
                Me.UpdateListViewItemCount(lvw.Items.Count)

            Catch ex As Exception
            End Try
        End If
    End Sub

    Private Sub SwitchListView(ByVal strNodeName As String)
        Dim lvw As tbListViewEx = Me.GetCurrentListView()

        Me.SwitchThumbnail(lvw)
        Me.UpdateButtonStatus()
        If Me.mCameraListView IsNot Nothing Then
            lvw.Font = Me.mCameraListView.Font
        End If
        'lvw.Font = Me.Font
        lvw.BringToFront()
        lvw.Focus()

        Application.DoEvents()
        If strNodeName = "Video" Then
            Me.UpdateListViewItemCount(lvw.Items.Count)
        End If

        Me.LoadData()
    End Sub

    Private Sub LoadPhoneLib(ByVal blnRefresh As Boolean, Optional ByVal blnSelectedNode As Boolean = True)
        Try
            If Me.mPhotoLibLoad = False OrElse blnRefresh Then
                If Me.mDicThread.ContainsKey(ButtonSelect.PhotoLib) Then
                    If Me.mDicThread(ButtonSelect.PhotoLib).ThreadState <> ThreadState.Stopped Then
                        Try
                            Me.mDicThread(ButtonSelect.PhotoLib).Abort()
                        Catch ex As Exception
                        End Try
                        Utility.WaitSeconds(0.5)
                    End If
                    Me.mDicThread.Remove(ButtonSelect.PhotoLib)
                End If

                Me.mPhotoLibLoad = True

                Me.LoadPhotoLibToNode(blnRefresh, blnSelectedNode)

                If Me.mDicThread.ContainsKey(ButtonSelect.PhotoLib) = False Then
                    Dim thd As New Thread(New Threading.ParameterizedThreadStart(AddressOf LoadFromPhotoLibThread))
                    thd.IsBackground = True

                    Me.mDicThread.Add(ButtonSelect.PhotoLib, thd)

                    Dim lvw As tbListViewEx = Me.GetCurrentListView()
                    thd.Start(lvw)
                End If

            Else
                Dim folder As ImageAlbum = Me.tvwDevice.SelectedNode.Tag
                Dim lvw As tbListViewEx = Me.GetCurrentListView()

                If lvw.Items.Count <> folder.ImageCount Then
                    Me.LoadFromPhotoLibThread(lvw)
                Else
                    Me.UpdateThumbnailPhotoLib(lvw)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadPhoneLib")
        End Try
    End Sub

    Private Delegate Sub LoadPhotoLibToNodeHandler(ByVal blnRefresh As Boolean, ByVal blnSelectedNode As Boolean)
    Private Sub LoadPhotoLibToNode(ByVal blnRefresh As Boolean, Optional ByVal blnSelectedNode As Boolean = True)
        If Me.InvokeRequired Then
            Me.Invoke(New LoadPhotoLibToNodeHandler(AddressOf LoadPhotoLibToNode), blnRefresh, blnSelectedNode)
        Else
            Common.Log("初始化PhotoDB")
            Me.mPhotoDB = New PhotoDB(IPod.InstancePhoto(Me.mDevice, blnRefresh))

            Common.Log("PhotoDB Parse")
            Me.mPhotoDB.Parse()

            Me.tvwDevice.SuspendLayout()

            For Each node As tbTreeNode In Me.mPhotoLibNode.ChildNodes
                Dim folder As ImageAlbum = node.Tag
                If folder IsNot Nothing Then
                    Me.pnlContainer.Controls.RemoveByKey(folder.FolderID.ToString())
                End If
            Next

            '清空图库列表
            Dim lstNode As New List(Of tbTreeNode)
            For Each Item As tbTreeNode In Me.mPhotoLibNode.ChildNodes
                If Item.Name.Contains("PhotoLib.") Then
                    lstNode.Add(Item)
                End If
            Next

            For Each Item As tbTreeNode In lstNode
                Me.mPhotoLibNode.ChildNodes.Remove(Item)
            Next

            If Me.mPhotoDB.PhotoAlbumList IsNot Nothing Then
                For Each folder As ImageAlbum In Me.mPhotoDB.PhotoAlbumList.Albums

                    If folder.IsMaster Then
                        Me.mPhotoLibNode.Tag = folder
                        Me.mPhotoLibNode.NodeNumber = folder.ImageCount

                        Me.mPhotoLibListView.Tag = folder
                    Else
                        Me.AddSubPhotoTreeNode(folder)
                    End If
                Next
            End If

            Me.AddTreeNodeByNewPhotoLib(Nothing)

            Dim selectNode As tbTreeNode = Me.mPhotoLibNode
            If Me.mIsSelectTbWallpaper AndAlso Not blnRefresh Then
                selectNode = Me.GetTongbuWallpaperNode()
            End If

            Me.tvwDevice.ResumeLayout()

            If Me.tvwDevice.SelectedNode Is Nothing OrElse Not (Me.tvwDevice.SelectedNode.Name <> Me.mPhotoLibSelectNodeName) AndAlso blnSelectedNode Then
                Dim node As tbTreeNode = Me.tvwDevice.Nodes(Me.mPhotoLibSelectNodeName)
                If node Is Nothing Then
                    node = Me.GetNodeByText()
                End If

                If node Is Nothing Then
                    node = selectNode
                End If

                If node.Visible = False Then
                    node = selectNode
                End If

                Me.tvwDevice.SelectedNode = node

            End If
        End If
    End Sub

    Private Sub LoadPhoneLib_5X(ByVal blnRefresh As Boolean, Optional ByVal blnSelectedNode As Boolean = True)
        Try
            If Me.mPhotoLibLoad = False OrElse blnRefresh Then
                If Me.mDicThread.ContainsKey(ButtonSelect.PhotoLib) Then
                    If Me.mDicThread(ButtonSelect.PhotoLib).ThreadState <> ThreadState.Stopped Then
                        Try
                            Me.mDicThread(ButtonSelect.PhotoLib).Abort()
                        Catch ex As Exception
                        End Try
                        Utility.WaitSeconds(0.5)
                    End If
                    Me.mDicThread.Remove(ButtonSelect.PhotoLib)
                End If

                Me.mPhotoLibLoad = True

                Me.LoadPhotoLibToNode_5X(blnRefresh, blnSelectedNode)

                If Me.mDicThread.ContainsKey(ButtonSelect.PhotoLib) = False Then
                    Dim thd As New Thread(New Threading.ParameterizedThreadStart(AddressOf LoadFromPhotoLibThread))
                    thd.IsBackground = True

                    Me.mDicThread.Add(ButtonSelect.PhotoLib, thd)

                    Dim lvw As tbListViewEx = Me.GetCurrentListView()
                    lvw.BringToFront()

                    thd.Start(lvw)
                End If

            Else
                Dim fCount As Integer = 0
                If Me.mDevice.VersionNumber >= 500 Then
                    Dim folder As AlbumItem = Me.tvwDevice.SelectedNode.Tag
                    fCount = folder.Assets.Count
                Else
                    Dim folder As ImageAlbum = Me.tvwDevice.SelectedNode.Tag
                    fCount = folder.ImageCount
                End If

                Dim lvw As tbListViewEx = Me.GetCurrentListView()

                If lvw.Items.Count <> fCount Then
                    Me.LoadFromPhotoLibThread(lvw)
                Else
                    Me.UpdateThumbnailPhotoLib(lvw)
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "LoadPhoneLib_5X")
        End Try
    End Sub

    Private Sub LoadPhotoLibToNode_5X(ByVal blnRefresh As Boolean, ByVal blnSelectedNode As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New LoadPhotoLibToNodeHandler(AddressOf LoadPhotoLibToNode_5X), blnRefresh, blnSelectedNode)
        Else
            Try
                For Each node As tbTreeNode In Me.mPhotoLibNode.ChildNodes
                    Dim folder As AlbumItem = node.Tag
                    If folder IsNot Nothing Then
                        Me.pnlContainer.Controls.RemoveByKey(folder.ID.ToString())
                    End If
                Next

                Me.tvwDevice.SuspendLayout()

                For Each node As tbTreeNode In Me.mNodeDevice.ChildNodes
                    If node.Name.StartsWith("PhotoLib.") Then
                        Dim folder As AlbumItem = node.Tag
                        If folder IsNot Nothing Then
                            Me.pnlContainer.Controls.RemoveByKey(folder.ID.ToString())
                        End If
                        '最新删除没有内容的时候要隐藏起来。所以在刷新的时候先隐藏，如果查询到有内容再显示出来。
                        If node.Name = "PhotoLib.Delete" OrElse node.Name = "PhotoLib.Selfie" OrElse node.Name = "PhotoLib.Panoramic" Then
                            node.Visible = False
                        End If
                    End If
                Next

                For Each pair As KeyValuePair(Of pItemTypeSub, AlbumItem) In Me.mPhotoThumb.SpecialAlbums
                    Dim node As tbTreeNode = Me.mNodeDevice.ChildNodes("PhotoLib." & pair.Key.ToString())
                    If node IsNot Nothing Then
                        Dim lvw As tbListViewEx = Me.AddPhotoListView(pair.Value.ID)
                        lvw.Tag = pair.Value

                        node.Visible = True
                        node.Tag = pair.Value
                        node.NodeNumber = pair.Value.Count
                    End If
                Next

                '清空图库列表
                Dim lstNode As New List(Of tbTreeNode)
                For Each Item As tbTreeNode In Me.mPhotoLibNode.ChildNodes
                    If Item.Name.Contains("PhotoLib.") Then
                        lstNode.Add(Item)
                    End If
                Next

                For Each Item As tbTreeNode In lstNode
                    Me.mPhotoLibNode.ChildNodes.Remove(Item)
                    Dim folder As AlbumItem = Item.Tag
                    If folder IsNot Nothing Then
                        Me.pnlContainer.Controls.RemoveByKey(folder.ID.ToString())
                    End If
                Next

                For Each folder As AlbumItem In Me.mPhotoThumb.Albums
                    If folder.Kind = pAlbumKind.CameraAsc OrElse folder.Kind = pAlbumKind.CameraDesc Then
                        Continue For
                    End If

                    If folder.IsMaster Then
                        Me.mPhotoLibNode.Tag = folder
                        Me.mPhotoLibNode.NodeNumber = folder.Count

                        Me.mPhotoLibListView.Tag = folder
                    Else
                        Me.AddSubPhotoTreeNode_5X(folder)
                    End If
                Next

                Me.AddTreeNodeByNewPhotoLib(Nothing)

                Dim selectNode As tbTreeNode = Me.mPhotoLibNode
                If Me.mIsSelectTbWallpaper AndAlso Not blnRefresh Then
                    selectNode = Me.GetTongbuWallpaperNode_5X()
                End If

                Me.tvwDevice.ResumeLayout()
                Me.tvwDevice.Refresh()

                If Me.tvwDevice.SelectedNode Is Nothing OrElse Not (Me.tvwDevice.SelectedNode.Name <> Me.mPhotoLibSelectNodeName) AndAlso blnSelectedNode Then
                    Dim node As tbTreeNode = Me.tvwDevice.Nodes(Me.mPhotoLibSelectNodeName)

                    If node Is Nothing Then
                        node = Me.GetNodeByText()
                    End If

                    If node Is Nothing Then
                        node = selectNode
                    End If

                    If node.Visible = False Then
                        node = selectNode
                    End If
                    Me.tvwDevice.SelectedNode = node

                End If
            Catch ex As Exception
                Common.LogException(ex.ToString, "frmPhoto_LoadPhotoLibToNode_5X")
            End Try
        End If
    End Sub

    Private Function GetNodeByText() As tbTreeNode
        Dim node As tbTreeNode = Nothing
        If Me.mPhotoLibSelectNodeText.Length > 0 Then
            For Each Item As tbTreeNode In mPhotoLibNode.ChildNodes
                If Item.Text = Me.mPhotoLibSelectNodeText Then
                    node = Item '
                    Exit For
                End If
            Next
        End If
        Return node
    End Function

    Private Function GetTongbuWallpaperNode() As tbTreeNode
        Dim tbWallpaperNode As tbTreeNode = Nothing
        Dim strTbWallpaper As String = Me.Language.GetString("Photo.Message.iTongWallpaper") '"同步壁纸"
        Dim folder As ImageAlbum = Me.mPhotoDB.GetFolder(strTbWallpaper)

        If folder Is Nothing Then
            folder = Me.mPhotoDB.AddFolder(strTbWallpaper)
            tbWallpaperNode = Me.AddSubPhotoTreeNode(folder)
        Else
            For Each node As tbTreeNode In Me.mPhotoLibNode.ChildNodes
                If node.Text = strTbWallpaper Then
                    tbWallpaperNode = node

                    Exit For
                End If
            Next
        End If

        Return tbWallpaperNode
    End Function

    Private Function GetTongbuWallpaperNode_5X() As tbTreeNode
        Dim tbWallpaperNode As tbTreeNode = Nothing
        Dim strTbWallpaper As String = Me.Language.GetString("Photo.Message.iTongWallpaper") '"同步壁纸"
        Dim folder As AlbumItem = Nothing 'Me.mPhotoThumb .GetFolder(strTbWallpaper)

        For Each item As AlbumItem In Me.mPhotoThumb.Albums
            If item.Title = strTbWallpaper Then
                folder = item
                Exit For
            End If
        Next

        If folder Is Nothing Then
            folder = Me.mPhotoThumb.AddFolder(strTbWallpaper)
            tbWallpaperNode = Me.AddSubPhotoTreeNode_5X(folder)
        Else
            For Each node As tbTreeNode In Me.mPhotoLibNode.ChildNodes
                If node.Text = strTbWallpaper Then
                    tbWallpaperNode = node

                    Exit For
                End If
            Next
        End If

        Return tbWallpaperNode
    End Function

    Private Delegate Sub SetDeviceNodeEnableHandler(ByVal blnEnable As Boolean)
    Public Sub SetDeviceNodeEnable(ByVal blnEnable As Boolean)
        If Me.InvokeRequired Then
            Me.Invoke(New SetDeviceNodeEnableHandler(AddressOf SetDeviceNodeEnable), blnEnable)
        Else
            Me.tvwDevice.Enabled = blnEnable
        End If
    End Sub

    '释放 AddPhotoOKThread 所有线程
    Private Sub DisposeAddPhotoOKThread()
        Try
            For Each thd As Thread In Me.mLstThread
                Try
                    If thd IsNot Nothing AndAlso thd.ThreadState <> ThreadState.Stopped Then
                        thd.Abort()
                    End If
                Catch
                End Try
            Next

            Me.mLstThread.Clear()
        Catch
        End Try
    End Sub

#End Region

#Region "--- 验证照片在设备上是否更新的逻辑 ---"

    Private Sub UpdatePhotosUpdateTime()
        If Me.mDevice.VersionNumber >= 500 Then
            Me.mPhotosDBUpdateTime = Me.GetPhotosDBUpdateTime()
        End If
    End Sub

    Private Function GetPhotosDBUpdateTime() As DateTime
        Dim dtReturn As DateTime = Now.AddYears(-1)
        Dim strPhotosDBPath As String = "/PhotoData/Photos.sqlite"
        Dim strPhotosDBPathSHM As String = "/PhotoData/Photos.sqlite-shm"
        Dim strPhotosDBPathWAL As String = "/PhotoData/Photos.sqlite-wal"
        Dim info As iPhoneFileInfo = Nothing

        '三个文件的修改日期取最新的日期
        If Me.mDevice.ExistsByAFC(strPhotosDBPath) Then
            Me.mDevice.IsDirectory(Me.mDevice.MobileAfcHandle, strPhotosDBPath, info)
            If info IsNot Nothing Then
                dtReturn = info.st_mtime
            End If
        End If

        If Me.mDevice.ExistsByAFC(strPhotosDBPathSHM) Then
            Me.mDevice.IsDirectory(Me.mDevice.MobileAfcHandle, strPhotosDBPathSHM, info)
            If info IsNot Nothing AndAlso dtReturn < info.st_mtime Then
                dtReturn = info.st_mtime
            End If
        End If

        If Me.mDevice.ExistsByAFC(strPhotosDBPathWAL) Then
            Me.mDevice.IsDirectory(Me.mDevice.MobileAfcHandle, strPhotosDBPathWAL, info)
            If info IsNot Nothing AndAlso dtReturn < info.st_mtime Then
                dtReturn = info.st_mtime
            End If
        End If

        info = Nothing
        Return dtReturn
    End Function

    Private Function GetPreviewImgUpdateTime() As DateTime
        Dim dtReturn As DateTime = Now.AddYears(-1)
        Dim info As iPhoneFileInfo = Nothing
        Dim strPreviewImgPath As String = "/PhotoData/MISC/PreviewWellImage.tiff"

        If Me.mDevice.ExistsByAFC(strPreviewImgPath) Then
            Me.mDevice.IsDirectory(Me.mDevice.MobileAfcHandle, strPreviewImgPath, info)
            If info IsNot Nothing Then
                dtReturn = info.st_mtime
            End If
        End If

        Return dtReturn
    End Function

    Private Function CheckPhotosDBUpdated() As Boolean
        Dim blnReturn As Boolean = False
        If Me.GetPhotosDBUpdateTime() <> Me.mPhotosDBUpdateTime Then
            blnReturn = True
        End If
        Return blnReturn
    End Function

#End Region

#Region "--- iOS6上传图片到相册 ---"

    Private Function CreatePlistForUploadPhoto(ByVal index As Integer, ByVal total As Integer) As String
        Dim tempDir As String = Path.Combine(Folder.TempFolder, Guid.NewGuid().ToString("N"))
        Dim strFileOnPC As String = Path.Combine(tempDir, "PhotoSync.tmp")

        Folder.CheckFolder(tempDir)

        Dim dicValues As New Dictionary(Of Object, Object)
        dicValues.Add("index", index)
        dicValues.Add("total", total)

        iTong.Device.CoreFoundation.WritePlist(dicValues, strFileOnPC)

        Return strFileOnPC
    End Function

    Public Function GetTuiStoreAppAFC(ByVal dictInstalls As Dictionary(Of String, FileSharingPackageInfo)) As Boolean
        Dim isHasInstallOne As Boolean = False

        Me.mDicAppsSupportCamera.Clear()

        Dim strTuiSKU As String = Common.GetTuiAppSKU(False)
        Dim strTuiSKUHD As String = Common.GetTuiAppSKU(True)
        Dim blnInstallQianMinTui As Boolean = AppSetting.GetInstallQianMinTui()

        '从配制已安装软件面取出已安装的同步推
        For Each strSKU As String In dictInstalls.Keys
            Dim info As FileSharingPackageInfo = dictInstalls(strSKU)
            If info Is Nothing Then
                Continue For
            End If
            If strSKU.StartsWith(strTuiSKU) OrElse strSKU.StartsWith(strTuiSKUHD) OrElse _
              info.TuiType = tbTuiType.Tui OrElse info.TuiType = tbTuiType.TuiHD OrElse _
              Not blnInstallQianMinTui AndAlso (info.TuiType = tbTuiType.TuiStore OrElse info.TuiType = tbTuiType.TuiStoreHD) Then

                Dim package As New ImportPhotoPackageInfo '= dicApps(strSKU)
                package.SKU = strSKU
                package.MiniVersion = "1.6"
                package.IsInstall = True
                package.ApplicationType = info.ApplicationType
                package.CrackedInfo = info.CrackedInfo
                package.IsVIP = info.IsVip
                isHasInstallOne = True

                Me.mDicAppsSupportCamera(strSKU) = package
            End If
        Next
        Return isHasInstallOne
    End Function

    Public Function GetTuiStoreAppAFC12(ByVal dictInstalls As Dictionary(Of String, FileSharingPackageInfo)) As Boolean
        Dim isHasInstallOne As Boolean = False
        Me.mDicAppsSupportCamera.Clear()

        Dim dictTuis As Dictionary(Of String, ImportPhotoPackageInfo) = ServerIniSetting.GetAppsForCamera(Me.mDevice)

        '从配制已安装软件面取出已安装的同步推
        For Each strSKU As String In dictInstalls.Keys
            Dim info As FileSharingPackageInfo = dictInstalls(strSKU)
            If info Is Nothing Then
                Continue For
            End If

            Dim package As ImportPhotoPackageInfo = Nothing
            For Each item As String In dictTuis.Keys
                If info.UrlSchemes.ToLower().Contains(item) Then
                    package = dictTuis(item)
                End If
            Next

            If package Is Nothing OrElse Common.CompareVer(info.Version, package.MiniVersion) < 0 Then
                Continue For
            End If

            package.SKU = strSKU
            package.IsInstall = True
            package.ApplicationType = info.ApplicationType
            package.CrackedInfo = info.CrackedInfo
            If Not package.IsVIP Then
                package.IsVIP = info.IsVip
            End If
            isHasInstallOne = True
            Me.mDicAppsSupportCamera(strSKU) = package
        Next
        Return isHasInstallOne
    End Function

    '取得用户已经安装的同步推的对像 
    '现在同步推的id 有变。
    Private Function GetTuiByIdentify(ByVal strSKU As String, ByVal dictInstalls As Dictionary(Of String, FileSharingPackageInfo)) As FileSharingPackageInfo
        Dim tempPackage As FileSharingPackageInfo = Nothing

        For Each Item As String In dictInstalls.Keys
            If Item.StartsWith(strSKU) Then
                tempPackage = dictInstalls(Item)
            End If
        Next
        Return tempPackage
    End Function

    Private Sub UploadPhotoForiOS6(ByVal argument As KeyValuePair(Of ImportSource, Array))
        Try
            If Folder.LangType = LanguageType.en_US Then
                tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.NoSupportCamera"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
                Return
            End If

            If Me.mWorkerUploadPhotoForiOS6 Is Nothing Then
                Me.mWorkerUploadPhotoForiOS6 = New System.ComponentModel.BackgroundWorker()
                Me.mWorkerUploadPhotoForiOS6.WorkerSupportsCancellation = True
                AddHandler mWorkerUploadPhotoForiOS6.DoWork, AddressOf mWorkerUploadPhotoForiOS6_DoWork
                AddHandler mWorkerUploadPhotoForiOS6.RunWorkerCompleted, AddressOf mWorkerUploadPhotoForiOS6_Completed
            End If

            If Me.mWorkerUploadPhotoForiOS6.IsBusy Then
                Me.mWorkerUploadPhotoForiOS6.CancelAsync()
            End If

            Me.mWorkerUploadPhotoForiOS6.RunWorkerAsync(argument)
        Catch ex As Exception
            Common.LogException(ex.ToString, "frmPhoto_UploadPhotoForiOS6")
        End Try
    End Sub

    Private Sub UploadVideoForiOS6()
        '由于iOS固件的限制，目前无法将文件导入 "视频" 节点！
        '请点击 "确定" 到 "照片图库" 导入视频文件！
        If tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.CheckVideo"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OKCancel, MessageBoxIcon.Information) = Windows.Forms.DialogResult.OK Then
            Me.tvwDevice.SelectedNode = Me.mPhotoLibNode
        End If
    End Sub

    Private mDicAppsSupportCamera As New Dictionary(Of String, ImportPhotoPackageInfo) '格式（sku，ImportPhotoPackageInfo)
    Private Sub mWorkerUploadPhotoForiOS6_DoWork(ByVal sender As Object, ByVal e As System.ComponentModel.DoWorkEventArgs)
        Try
            Me.UpdateTranslateStatus(False)
            Me.ChangeUploadText(Me.Language.GetString("Photo.Message.CheckingTuiIsInstall"), False, False, 0) '"正在检测是否安装同步推..."

            Dim dict As Dictionary(Of String, FileSharingPackageInfo) = Me.mDevice.InstalledApplications(ApplicationType.Any, False)
            If Me.mDevice.VersionNumber >= 1210 Then
                Me.GetTuiStoreAppAFC12(dict)
            Else
                Me.GetTuiStoreAppAFC(dict)
            End If

            e.Result = e.Argument
        Catch ex As Exception
            Common.LogException(ex.ToString(), "mWorkerUploadPhotoForiOS6_DoWork")
        End Try
    End Sub

    Private Sub mWorkerUploadPhotoForiOS6_Completed(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs)
        Me.UpdateTranslateStatus(True)

        'If Me.mDicAppsSupportCamera.Count = 0 Then
        '    Dim strInfo As String = Me.Language.GetString("Photo.Message.NotSupportImportPhotoForiOS6") '"暂不支持iOS6 导入图片到""照相机""，可导入到""照片图库"""
        '    tbMessageBox.Show(Me, strInfo, Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
        '    Return
        'End If

        Dim isInstallOne As Boolean = False
        Dim isOpenOne As Boolean = False
        'Dim openingPackage As ImportPhotoPackageInfo = Nothing
        Dim dtNow As DateTime = Me.mDevice.GetDeviceTime()
        For Each key As String In Me.mDicAppsSupportCamera.Keys
            Dim package As ImportPhotoPackageInfo = Me.mDicAppsSupportCamera(key)
            If package.IsInstall Then
                isInstallOne = True
                package.HAfc = Me.mDevice.GetAfcByFileSharing(package.SKU)

                '如果是deb的推就取default
                If package.HAfc = IntPtr.Zero AndAlso package.ApplicationType = ApplicationType.System Then
                    package.HAfc = Me.mDevice.DefaultAfcHandle
                End If

                '判断条件：
                '1.文件是否存在
                '2.文件时间超过一天的就提示用户重新打开
                'If package.HAfc <> IntPtr.Zero AndAlso _
                '   (Me.mDevice.Exists(package.HAfc, "/Library/Caches/TongbuPhotoWatch/PhotoSyncReady.plist") OrElse _
                '    Me.mDevice.Exists(package.HAfc, "/Documents/Tongbu/TongbuPhotoWatch/PhotoSyncReady.plist")) AndAlso _
                '    (Me.CheckFileInfo("/Documents/Tongbu/TongbuPhotoWatch/PhotoSyncReady.plist", package.HAfc, dtNow) OrElse _
                '    Me.CheckFileInfo("/Library/Caches/TongbuPhotoWatch/PhotoSyncReady.plist", package.HAfc, dtNow)) Then

                '    isOpenOne = True
                '    'package.IsOpen = True
                '    'openingPackage = package
                '    Me.mTuiAFC = package.HAfc
                '    Exit For
                'End If

                '判断条件：
                '1.文件是否存在
                '2.文件时间超过一天的就提示用户重新打开
                If package.HAfc <> IntPtr.Zero Then
                    Dim strTuiPath As String = Me.mDevice.iPath.TuiPhotoPath(package.HAfc)
                    '判断同步推是不是打开有两个逻辑
                    '旧PhotoSyncReady.plist是不是存在
                    '新s.plist status=1
                    Common.LogException(String.Format("Import Photo Status File Path:{0} {1}", strTuiPath, package.HAfc))
                    If (Not String.IsNullOrEmpty(strTuiPath) AndAlso Me.CheckFileInfo(strTuiPath & "PhotoSyncReady.plist", package.HAfc, dtNow)) OrElse Me.mDevice.GetTuiPhotoStatus(package.HAfc) <> TuiPhotoStatus.None Then
                        Common.LogException("Tui Open One")
                        isOpenOne = True
                        Me.mTuiAFC = package.HAfc
                        '导入出错的时候提示用户是在导入哪个同步推的时候出错的
                        If Me.mDevice.VersionNumber >= 1210 AndAlso package.IsVIP Then
                            Me.mTuiTxt = Me.Language.GetString("Welcome.Button.Tuivip")
                        ElseIf Me.mDevice.VersionNumber >= 1210 Then
                            Me.mTuiTxt = Me.Language.GetString("Welcome.Button.TuiOriginal")
                        Else
                            Me.mTuiTxt = Me.Language.GetString("File.Label.tbTui")
                        End If
                        Me.UpdateTuiUploadingStatus(Me.mTuiAFC)
                        Exit For
                    End If
                End If
            End If
        Next

        Dim argument As KeyValuePair(Of ImportSource, Array) = e.Result

        If Not isInstallOne OrElse Not isOpenOne Then
            If Me.mDevice.VersionNumber >= 1210 Then
                If Not Me.ShowPhotoWithTui12(isInstallOne, isOpenOne, argument) Then
                    Common.LogException("Import Photo Cannot find tui ios12.")
                    Return
                End If
            Else
                If Not Me.ShowPhotoWithTui(isInstallOne, isOpenOne, argument) Then
                    Common.LogException("Import Photo Cannot find tui.")
                    Return
                End If
            End If
        End If

        Select Case argument.Key
            Case ImportSource.Folder
                Me.UploadToPhoneForFolder()

            Case ImportSource.File
                Me.UploadToPhoneForFile()

            Case ImportSource.DragDrop
                Me.UploadArrayToPhone(argument.Value)

        End Select
    End Sub

    Private Sub UpdateTuiUploadingStatus(ByVal tuiAFC As IntPtr)
        If tuiAFC = IntPtr.Zero Then
            Return
        End If

        Try
            Dim strWillSyncFile As String = Me.mDevice.iPath.TuiPhotoPath(tuiAFC) & "PhotoWillSync.plist"
            Dim strWillSyncFileTmp As String = strWillSyncFile & "tmp"
            Me.mDevice.CreateFile(tuiAFC, strWillSyncFileTmp)
            Me.mDevice.Rename(tuiAFC, strWillSyncFileTmp, strWillSyncFile)
            '兼容同步推新同步照片逻辑
            Me.mDevice.SetTuiPhotoStatus(tuiAFC, TuiPhotoStatus.Ready, 0, 0)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmPhoto_UpdateTuiCameraStatus")
        End Try
    End Sub

    Private Function CheckFileInfo(ByVal strFilePath As String, ByVal Hafc As IntPtr, ByVal dtNow As DateTime) As Boolean
        Dim blnReturn As Boolean = False
        Dim info As iPhoneFileInfo = Nothing
        Me.mDevice.IsDirectory(Hafc, strFilePath, info)
        If info IsNot Nothing AndAlso info.st_birthtime > dtNow.AddHours(-3) Then
            blnReturn = True
        End If
        Return blnReturn
    End Function

    Private Function ShowPhotoWithTui12(ByVal isInstallOne As Boolean, ByVal isOpenOne As Boolean, ByVal argument As KeyValuePair(Of ImportSource, Array)) As Boolean
        Dim result As Boolean = False
        Me.mTuiTxt = String.Empty
        Dim frmTui As frmPhotoWithTui12 = New frmPhotoWithTui12(Me.mApplication, Me.mDevice)
        Me.mfrmMessage = frmTui
        frmTui.IsTuiInstalled = isInstallOne
        frmTui.IsTuiOpened = isOpenOne
        frmTui.AppsSupportCamera = Me.mDicAppsSupportCamera

        Me.mfrmMessage.ShowDialog(Me.mApplication)
        If Me.mfrmMessage Is Nothing Then
            Return result
        End If

        mDefaultClick = False

        Select Case frmTui.ButtonClickResult
            Case frmPhotoWithTui.ButtonClick.None
            Case frmPhotoWithTui.ButtonClick.Cancel
            Case frmPhotoWithTui.ButtonClick.Camera
                Me.mTuiAFC = frmTui.TuiAFC
                Me.mTuiTxt = frmTui.TuiTxt
                result = True

            Case frmPhotoWithTui.ButtonClick.InstallTui
                ''安装同步推
                'Me.InitDownload()
                'AuthorizeByAppleID.Instance(Me.mDevice).CheckTui(ResourceClass.Software, True)

            Case frmPhotoWithTui.ButtonClick.PhotoLib
                '跳转到照片图库
                If argument.Key = ImportSource.DragDrop Then
                    Me.OnAddPhotoLib(argument.Value)
                Else
                    Me.tvwDevice.SelectedNode = Me.mPhotoLibNode
                End If
        End Select

        Return result
    End Function

    Private Function ShowPhotoWithTui(ByVal isInstallOne As Boolean, ByVal isOpenOne As Boolean, ByVal argument As KeyValuePair(Of ImportSource, Array)) As Boolean
        Dim result As Boolean = False
        Me.mTuiTxt = String.Empty
        Dim frmTui As frmPhotoWithTui = New frmPhotoWithTui(Me.mApplication, Me.mDevice, Me.mDefaultClick)
        Me.mfrmMessage = frmTui
        frmTui.IsTuiInstalled = isInstallOne
        frmTui.IsTuiOpened = isOpenOne
        frmTui.AppsSupportCamera = Me.mDicAppsSupportCamera

        Me.mfrmMessage.ShowDialog(Me.mApplication)
        If Me.mfrmMessage Is Nothing Then
            Return result
        End If

        mDefaultClick = False

        Select Case frmTui.ButtonClickResult
            Case frmPhotoWithTui.ButtonClick.None
            Case frmPhotoWithTui.ButtonClick.Cancel
            Case frmPhotoWithTui.ButtonClick.Camera
                Me.mTuiAFC = frmTui.TuiAFC
                Me.mTuiTxt = frmTui.TuiTxt
                result = True

            Case frmPhotoWithTui.ButtonClick.InstallTui
                '安装同步推

                'Me.mAppSKU = Common.GetTuiAppSKU(Me.mDevice.ProductType)
                Me.InitDownload()

                'Dim blnJailbreak As Boolean = False
                'If Me.mDevice.Jailbreaked OrElse Me.mDevice.InstallCydia Then
                '    blnJailbreak = True
                'End If

                'If Me.mDevice.ProductType.ToLower().StartsWith("ipad") Then
                '    TuiInstallHelper.DownloadTbTuiHD(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, blnJailbreak, ResourceClass.Plugins, True, True)
                'Else
                '    TuiInstallHelper.DownloadTbTui(Me.mDevice, Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, blnJailbreak, ResourceClass.Plugins, True, True)
                'End If

                'If Me.mDevice.InstallCydia Then
                '    If Me.mDevice.ProductType.ToLower().StartsWith("ipad") Then
                '        frmTuiInstall.DownloadTbTuiHD(Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, True)
                '    Else
                '        frmTuiInstall.DownloadTbTui(Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion, True)
                '    End If

                'Else
                '    If Me.mDevice.ProductType.ToLower().StartsWith("ipad") Then
                '        frmTuiInstall.DownloadTbTuiStoreHD(Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion)
                '    Else
                '        frmTuiInstall.DownloadTbTuiStore(Me.mDevice.Identifier, Me.mDevice.ProductType, Me.mDevice.ProductVersion)
                '    End If
                'End If

                AuthorizeByAppleID.Instance(Me.mDevice).CheckTui(ResourceClass.Software, True)

            Case frmPhotoWithTui.ButtonClick.PhotoLib
                '跳转到照片图库
                If argument.Key = ImportSource.DragDrop Then
                    Me.OnAddPhotoLib(argument.Value)
                Else
                    Me.tvwDevice.SelectedNode = Me.mPhotoLibNode
                End If
        End Select

        Return result
    End Function

    'Private Sub mWorkerUploadPhotoForiOS6_DoWork(ByVal sender As Object, ByVal e As System.ComponentModel.DoWorkEventArgs)
    '    Try
    '        Me.UpdateTranslateStatus(False)
    '        Me.ChangeUploadText(False, "正在检测是否安装同步推。")

    '        If Me.mAFC = IntPtr.Zero Then
    '            Me.mAFC = Me.mDevice.GetTuiStoreAppAFC()
    '        End If

    '        e.Result = Me.mAFC
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "mWorkerUploadPhotoForiOS6_DoWork")
    '    End Try
    'End Sub

    'Private Sub mWorkerUploadPhotoForiOS6_Completed(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs)
    '    Me.UpdateTranslateStatus(True)

    '    Dim hAFC As IntPtr = e.Result
    '    If hAFC = IntPtr.Zero Then
    '        tbMessageBox.Show(Me, "同步推没有安装", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
    '        '判断同步推（store）2.0是否安装，如果没有安装则提示购买安装。
    '        Return
    '    End If

    '    If Not Me.mDevice.Exists(hAFC, "/Library/Caches/TongbuPhotoWatch/PhotoSyncReady.plist") Then
    '        tbMessageBox.Show(Me, "同步推没有运行", Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information)
    '        '引导开启同步推
    '        Return
    '    End If

    '    Dim fileDlg As OpenFileDialog = New OpenFileDialog()
    '    fileDlg.Multiselect = True
    '    fileDlg.Title = Me.Language.GetString("Media.Button.Import")         '"导入"
    '    fileDlg.FileName = Me.Language.GetString("Music.OpenFileDialog.SelectPhoto")                                    '"选择图片"
    '    fileDlg.Filter = Me.Language.GetString("Common.OpenFileDialog.PicFile") & "(*.PNG;*.JPG;*.JPEG;*.GIF;*.BMP)|*.PNG;*.JPG;*.JPEG;*.GIF;*.BMP"                 '图像文件

    '    If fileDlg.ShowDialog() = Windows.Forms.DialogResult.OK Then
    '        Me.UpdateTranslateStatus(False)
    '        Me.ChangeUploadText(False, "开始上传")

    '        Dim listItem As New List(Of String)
    '        For Each Item As String In fileDlg.FileNames
    '            listItem.Add(Item)
    '        Next
    '        If listItem.Count = 0 Then
    '            Return
    '        End If

    '        Dim count As Integer = listItem.Count
    '        For index As Integer = 0 To count - 1
    '            '判断同步推是否打开，如果没有提示打开。根据PhotoSyncReady.plist文件
    '            Dim filePlistReady As String = "/Library/Caches/TongbuPhotoWatch/PhotoSyncReady.plist"
    '            If Not Me.mDevice.Exists(hAFC, filePlistReady) Then
    '                '上传过程中，提示中断。
    '                Me.ChangeUploadText(False, "程序中断了")
    '                Return
    '            End If

    '            '开始上传图片，一张一张上传。
    '            Dim fileOnPC As String = listItem(index)
    '            Dim fileName As String = Path.GetFileName(fileOnPC)
    '            Dim fileOnDevice As String = "/Library/Caches/TongbuPhoto/" & fileName
    '            Dim fileTmpOnDevice As String = "/Library/Caches/TongbuPhotoWatch/PhotoSync.tmp"
    '            Dim filePlistOnDevice As String = "/Library/Caches/TongbuPhotoWatch/PhotoSync.plist"

    '            '上传图片
    '            Dim isSucceed As Boolean = Me.mDevice.CopyToPhone(hAFC, fileOnPC, fileOnDevice) 'New DataTransferEventHandler(AddressOf DataTransferProgress))

    '            '展示进度
    '            Dim strProgress As String = String.Format(Me.Language.GetString("Photo.Message.UploadingPhoto"), fileName, index + 1, count)
    '            Me.ChangeUploadText(False, strProgress)

    '            '上传监控文件
    '            Dim filePlistOnPC As String = Me.CreatePlistForUploadPhoto(index + 1, count)
    '            isSucceed = Me.mDevice.CopyToPhone(hAFC, filePlistOnPC, fileTmpOnDevice)
    '            'PhotoSync.tmp 改名为 PhotoSync.plist。（防止没有写完，iPhone端就去读plist文件）
    '            isSucceed = Me.mDevice.Rename(hAFC, fileTmpOnDevice, filePlistOnDevice)

    '            '监控是否完成
    '            Dim waitTime As Double = 0
    '            While (Me.mDevice.Exists(hAFC, filePlistOnDevice))
    '                Utility.WaitSeconds(0.5)
    '                waitTime = waitTime + 0.5
    '                Debug.Print(fileOnPC)

    '                If waitTime >= 30 Then
    '                    Me.mDevice.DeleteFile(hAFC, filePlistOnDevice)
    '                    Me.mDevice.DeleteFile(hAFC, fileOnDevice)
    '                    Exit While
    '                End If
    '            End While
    '        Next

    '        '上传完成
    '        Me.ChangeUploadText(False, "上传完成")
    '        Utility.WaitSeconds(1)
    '        Me.UpdateTranslateStatus(True)

    '        '重新加载所有图片
    '        Me.OnRefresh()
    '    End If
    'End Sub

    Private Delegate Sub ChangeUploadTextHandler(ByVal strContent As String, ByVal isShowProgress As Boolean, ByVal isWaiting As Boolean, ByVal progress As Integer)
    Private Sub ChangeUploadText(ByVal strContent As String, ByVal isShowProgress As Boolean, ByVal isWaiting As Boolean, ByVal progress As Integer)
        If Me.InvokeRequired Then
            Me.Invoke(New ChangeUploadTextHandler(AddressOf ChangeUploadText), strContent, isShowProgress, isWaiting, progress)
        Else
            Me.pgbProgress.Visible = isShowProgress
            Me.pgbProgress.tbIsWaiting = isWaiting
            Me.pgbProgress.tbPlayValue = progress

            Me.lblCurrentFile.Text = strContent
            Me.UpdateTranslateStatus(False)
            Application.DoEvents()
        End If
    End Sub

#End Region

#Region "--- 进度展示 ---"

    Private Sub OnCheckDownload(ByVal sender As Object, ByVal args As CheckDownloadArgs)
        Try
            'Me.mAppSKU 就是同步推identify,所以只要比较一次就可以了
            If Not args.Identifier.StartsWith(Common.GetTuiAppSKU(Me.mDevice.ProductType)) Then
                Return
            End If

            Select Case args.State
                Case SoftDownloadErrorState.Succeed, SoftDownloadErrorState.Nomal

                Case Else
                    Dim strFailText As String = Me.Language.GetString("Download.Label.Failed")      '"Failed"
                    If args.DownloadInfo IsNot Nothing AndAlso args.DownloadInfo.ErrorMsg.Length > 0 Then
                        strFailText = args.DownloadInfo.ErrorMsg
                    Else
                        strFailText = args.ErrorDescription
                    End If

                    Me.ChangeUploadText(strFailText, False, False, 0)
            End Select
        Catch
        End Try
    End Sub

    Private Sub OnCombineSingling(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        Me.ChangeDownloadState(item, DownloadStatus.DownloadCombining)
    End Sub

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        If Me.mCancel Then
            e.Cancel = True
            Return
        End If

        Me.ChangeDownloadState(item, DownloadStatus.Downloading, e.Progress)
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Dim item As MultiThreadDownloadItem = sender
        If item.ItemInfo.Status = TaskState.Completed Then
            '安装
            Me.ChangeDownloadState(item, DownloadStatus.DownloadSucceed)

        ElseIf item.ItemInfo.IsDeleted Then
            '删除
            Me.ChangeDownloadState(item, DownloadStatus.DownloadCancel)

        ElseIf e.Cancel AndAlso item.ItemInfo.Status <> TaskState.Completed Then
            '取消
            Me.ChangeDownloadState(item, DownloadStatus.DownloadCancel)
            Me.mCancel = False
        Else
            '下载失败
            Me.ChangeDownloadState(item, DownloadStatus.DownloadFailed)

        End If

    End Sub

    Private Sub mInstallHelper_OnInstall(ByVal sender As Object, ByVal args As InstallArgs)
        Try
            'Me.mAppSKU 就是同步推identify,所以只要比较一次就可以了
            If Not args.PackageInfo.IsTui Then
                Return
            End If

            If Me.mCancel = True Then
                args.Cancel = True
                Me.mInstallHelper.CancelInstallApp(args.PackageInfo)
                Me.mCancel = False
                Me.UpdateTranslateStatus(True)
            End If

            Select Case args.InstallState
                Case InstallState.Nomal

                Case InstallState.Waiting
                    Me.ChangeUploadText(Me.Language.GetString("App.Cell.WaitingInstall"), True, True, 0)

                Case InstallState.AuthBegin
                    Me.ChangeUploadText(Me.Language.GetString("App.Message.Calculating"), True, True, 0)

                Case InstallState.Transfering
                    Me.ChangeUploadText(Me.Language.GetString("App.Cell.Installing"), True, False, args.TransferProgress)

                Case InstallState.Installing
                    Me.ChangeUploadText(Me.Language.GetString("App.Cell.Installing"), True, False, args.TransferProgress)

                Case InstallState.HaveNotInstall
                    Me.ChangeUploadText(args.ErrorMsg, False, True, args.TransferProgress)

                Case InstallState.UnSucceed
                    Me.ChangeUploadText(args.ErrorMsg, False, True, args.TransferProgress)

                Case InstallState.CancelInstall
                    Me.ChangeUploadText(Me.Language.GetString("App.Cell.InstalCancel"), False, True, args.TransferProgress)

                Case InstallState.AlreadyInstall
                    Me.ChangeUploadText(Me.Language.GetString("App.Cell.AlreadyInstall"), False, True, args.TransferProgress)

                Case InstallState.Succeed
                    '"。请在设备中打开同步推，并允许访问您的照片。"
                    Me.ChangeUploadText(Me.Language.GetString("App.Cell.InstallSucceed") & Me.Language.GetString("Common.Symbol.FullStop") & String.Format(Me.Language.GetString("Photo.Message.OpenTui"), Me.Language.GetString("File.Label.tbTui")), False, True, args.TransferProgress)
                    'Me.mAppSKU = ""

            End Select
        Catch
        End Try
    End Sub

    Private Sub ChangeDownloadState(ByVal state As DownloadStatus, Optional ByVal item As MultiThreadDownloadItem = Nothing, Optional ByVal iProgress As Integer = 0)

        Select Case state
            Case DownloadStatus.Downloading
                Me.ChangeUploadText(Me.Language.GetString("App.Cell.Downloading"), True, False, iProgress)

            Case DownloadStatus.Nomal, DownloadStatus.DownloadDeleted
                Me.ChangeUploadText(Me.Language.GetString("Download.Label.Waiting"), True, False, iProgress)

            Case DownloadStatus.DownloadCancel
                Me.ChangeUploadText(Me.Language.GetString("Download.Label.Cancel"), False, False, iProgress) '"下载取消"

            Case DownloadStatus.DownloadFailed
                Me.ChangeUploadText(Me.Language.GetString("Download.Label.Failed"), False, False, iProgress)

            Case DownloadStatus.DownloadSucceed
                Me.ChangeUploadText(Me.Language.GetString("App.Cell.DownloadSucceed"), False, False, iProgress)

            Case DownloadStatus.Waiting
                Me.ChangeUploadText(Me.Language.GetString("Download.Label.Waiting"), True, True, iProgress)

            Case DownloadStatus.DownloadCombining
                Me.ChangeUploadText(Me.Language.GetString("Download.Label.Waiting"), True, True, iProgress)

        End Select
    End Sub

    Private Sub ChangeDownloadState(ByVal item As MultiThreadDownloadItem, ByVal state As DownloadStatus, Optional ByVal iProgress As Integer = 0)
        If item.ItemInfo.Type <> ResourceType.pTui Then
            Return
        End If

        Me.ChangeDownloadState(state, item, iProgress)
    End Sub

#End Region

    Public Overrides Sub OnReSelectSiteNode(ByVal usb As UsbDevice)
        MyBase.OnReSelectSiteNode(usb)
        If Common.VerIs30 AndAlso Me.tvwDevice.SelectedNode IsNot Nothing Then
            frmSite.SelectNode(Me.tvwDevice.SelectedNode, usb)

            '' Added by Utmost20150318
            If usb Is Nothing AndAlso mFormWallpaperLocal IsNot Nothing Then

                Me.mFormWallpaperLocal.Show()
                Me.mFormWallpaperLocal.BringToFront()
                Me.mPanelForm.Visible = True
                Me.pnlTop.Visible = False

                Application.DoEvents()
                Me.mFormWallpaperLocal.SwitchWallpaperType(WallpaperLoaclType.iPhone, tvwDevice.SelectedNode)
            End If

        End If
    End Sub

    Protected Overrides Sub ImportLivePhoto()
        MyBase.ImportLivePhoto()
        Try
            If Not Me.mDevice.IsLivePhotoDevice AndAlso Me.mDevice.VersionNumber < 910 Then
                tbMessageBox.Show(Me, Me.Language.GetString("Photo.Message.NoLivePhoto"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Information) '"您的设备无法使用该功能。\r\n请确认设备是否支持LivePhoto功能或者设备固件是否是9.1及以上版本！"
                Return
            End If

            Dim intFileNumber As Integer = 1
            If TypeOf Me.mPhotoThumb Is SqlitePhotoDB_5X Then
                intFileNumber = CType(Me.mPhotoThumb, SqlitePhotoDB_5X).LastFileNumberCamera
            End If
            Dim frmLivePhoto As New frmImportLivePhoto(Me.mApplication, Me.mDevice, String.Empty, intFileNumber)
            frmLivePhoto.ShowDialog(Me.mApplication)

            If String.IsNullOrEmpty(frmImportLivePhoto.mStrPathLivePhoto) Then
                Return
            End If

            Dim listItem As New List(Of String)
            listItem.Add(frmImportLivePhoto.mStrPathLivePhoto)
            Dim typeFrmPhoto As Type = GetType(frmPhoto)
            If Me.mButtonSelect = ButtonSelect.Camera Then
                Me.UploadPhotoForiOS6(New KeyValuePair(Of ImportSource, Array)(ImportSource.DragDrop, listItem.ToArray))
            Else
                Me.UploadArrayToPhone(listItem.ToArray)
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnImportLivePhoto_Click")
        End Try
    End Sub

End Class

Friend Enum OpType
    None
    Upload
    Del
    Sync
End Enum

Friend Enum ImportSource
    None
    File
    Folder
    DragDrop
End Enum

Public Class CreateFolderEventArgs
    Inherits EventArgs

    Private mFolders As Dictionary(Of String, List(Of String))
    Public Property Folders() As Dictionary(Of String, List(Of String))
        Get
            Return Me.mFolders
        End Get
        Set(ByVal value As Dictionary(Of String, List(Of String)))
            Me.mFolders = value
        End Set
    End Property

    Private mShowCreatePhotoLibMessage As Boolean = False
    Public Property ShowCreatePhotoLibMessage() As Boolean
        Get
            Return Me.mShowCreatePhotoLibMessage
        End Get
        Set(ByVal value As Boolean)
            Me.mShowCreatePhotoLibMessage = value
        End Set
    End Property

    Private mWallpaperLoad As Boolean = False
    Public Property WallpaperLoad() As Boolean
        Get
            Return Me.mWallpaperLoad
        End Get
        Set(ByVal value As Boolean)
            Me.mWallpaperLoad = value
        End Set
    End Property

    Private mCount As Integer = False
    Public Property Count() As Integer
        Get
            Return Me.mCount
        End Get
        Set(ByVal value As Integer)
            Me.mCount = value
        End Set
    End Property

    Private mOrderbyTime As Boolean = False
    Public Property OrderbyTime() As Boolean
        Get
            Return Me.mOrderbyTime
        End Get
        Set(ByVal value As Boolean)
            Me.mOrderbyTime = value
        End Set
    End Property

    Private mCreatePhotoLib As Boolean = False
    Public Property CreatePhotoLib() As Boolean
        Get
            Return Me.mCreatePhotoLib
        End Get
        Set(ByVal value As Boolean)
            Me.mCreatePhotoLib = value
        End Set
    End Property

End Class