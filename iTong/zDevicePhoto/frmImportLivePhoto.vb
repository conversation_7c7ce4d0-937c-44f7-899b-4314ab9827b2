﻿Public Class frmImportLivePhoto

    Private mApplication As IApplication
    Private mDevice As iPhoneDevice
    Private misOk As Boolean = False
    Public Shared mStrPathLivePhoto As String = String.Empty
    Private mintFileNumber As Integer = 1

#Region "--- 初始化 ---"
    Public Sub New(ByVal app As IApplication, ByVal iPhone As iPhoneDevice, ByRef strPathLivePhoto As String, ByVal intFileNumber As Integer)
        ' 此调用是 Windows 窗体设计器所必需的。

        InitializeComponent()
        ' 在 InitializeComponent() 调用之后添加任何初始化。

        Me.mApplication = app
        Me.Language = app.Language
        Me.StartPosition = FormStartPosition.CenterParent
        Me.mDevice = iPhone

        Me.FilletRadius = 5
        Me.Owner = Me.mApplication
        Me.StartPosition = FormStartPosition.CenterParent
        Me.Icon = My.Resources.iTong
        mStrPathLivePhoto = strPathLivePhoto
        Me.mintFileNumber = intFileNumber + 1
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()

        Me.lklblUrl.Font = Common.CreateFont("微软雅黑", 9.5!, FontStyle.Regular)
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

        Me.Text = Me.Language.GetString("LivePhoto.Button.Import")        '导入Live Photos
        Me.btnOK.Text = Me.Language.GetString("Common.OK")                  '确定
        Me.btnCancel.Text = Me.Language.GetString("Common.Cancel")          '取消

        Me.lklblUrl.Text = Me.Language.GetString("LivePhoto.Button.MoreResources")          '更多资源，前往查看>>
    End Sub


#End Region

#Region "--- 窗体事件 ---"
    Private Sub btnAddJpg_DragDrop(sender As Object, e As DragEventArgs) Handles btnAddJpg.DragDrop
        Try
            Dim arrFileNames As String() = CType(e.Data.GetData(DataFormats.FileDrop, False), String())
            If Path.GetExtension(arrFileNames(0)).ToLower() <> ".jpg" AndAlso Path.GetExtension(arrFileNames(0)).ToLower() <> ".heic" Then
                Return
            End If

            'If CheckImageSize(arrFileNames(0)) = False Then
            '    Return
            'End If

            Me.AddJpgToShow(arrFileNames(0))

            Dim strPathMOV As String = arrFileNames(0).Replace(Path.GetExtension(arrFileNames(0)), ".MOV")
            If File.Exists(strPathMOV) Then
                Me.AddMovToShow(strPathMOV)
            End If

            Me.CheckBtnOkEnable()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnAddJpg_DragDrop")
        End Try
    End Sub

    Private Sub btnAddJpg_DragEnter(sender As Object, e As DragEventArgs) Handles btnAddJpg.DragEnter
        If e.Data.GetDataPresent(DataFormats.FileDrop) Then
            e.Effect = DragDropEffects.Copy
        Else
            e.Effect = DragDropEffects.None
        End If
    End Sub

    Private Sub btnAddMOV_DragDrop(sender As Object, e As DragEventArgs) Handles btnAddMOV.DragDrop
        Try
            Dim arrFileNames As String() = CType(e.Data.GetData(DataFormats.FileDrop, False), String())
            If Path.GetExtension(arrFileNames(0)).ToLower() <> ".mov" Then
                Return
            End If
            Me.AddMovToShow(arrFileNames(0))
            Me.CheckBtnOkEnable()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnAddMOV_DragDrop")
        End Try
    End Sub

    Private Sub btnAddMOV_DragEnter(sender As Object, e As DragEventArgs) Handles btnAddMOV.DragEnter
        If e.Data.GetDataPresent(DataFormats.FileDrop) Then
            e.Effect = DragDropEffects.Copy
        Else
            e.Effect = DragDropEffects.None
        End If
    End Sub
    Private Sub btnAddJpg_Click(sender As Object, e As EventArgs) Handles btnAddJpg.Click
        Try
            Using ofdJPG As New OpenFileDialog()
                ofdJPG.Multiselect = False
                ofdJPG.Filter = "JPG (*.JPG) | *.jpg"

                If ofdJPG.ShowDialog() = Windows.Forms.DialogResult.OK Then

                    'If CheckImageSize(ofdJPG.FileName) = False Then
                    '    Return
                    'End If

                    Me.AddJpgToShow(ofdJPG.FileName)

                    Dim strPathMOV As String = ofdJPG.FileName.Replace(Path.GetExtension(ofdJPG.FileName), ".MOV")
                    If File.Exists(strPathMOV) Then
                        Me.AddMovToShow(strPathMOV)
                    End If
                End If
                Me.CheckBtnOkEnable()
            End Using
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnAddJpg_Click")
        End Try
    End Sub

    Private Sub btnAddMOV_Click(sender As Object, e As EventArgs) Handles btnAddMOV.Click
        Try
            Using ofdMOV As New OpenFileDialog()
                ofdMOV.Multiselect = False
                ofdMOV.Filter = "MOV (*.MOV) | *.mov"

                If ofdMOV.ShowDialog() = Windows.Forms.DialogResult.OK Then
                    Me.AddMovToShow(ofdMOV.FileName)
                End If
                Me.CheckBtnOkEnable()
            End Using
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnAddMOV_Click")
        End Try

    End Sub

    Private Sub btnOK_Click(sender As Object, e As EventArgs) Handles btnOK.Click
        Try
            If btnAddJpg.Tag Is Nothing OrElse btnAddMOV.Tag Is Nothing Then
                Return
            End If

            Dim strPathLivePhotoOnPC As String = CreateLivePhotosFile(btnAddJpg.Tag.ToString(), btnAddMOV.Tag.ToString())
            mStrPathLivePhoto = strPathLivePhotoOnPC
            Me.Close()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnOK_Click")
        End Try

    End Sub

    Private Sub btnCancel_Click(sender As Object, e As EventArgs) Handles btnCancel.Click
        Me.Close()
    End Sub

#End Region

#Region "--- 私有方法 ---"
    Private Sub AddJpgToShow(ByVal strFilePath As String)
        Try
            btnAddJpg.Tag = strFilePath
            Me.btnAddJpg.tbIconImage = Nothing
            Dim strOutPutFile As String = Path.Combine(Folder.CacheFolder, Path.GetFileName(strFilePath))

            Utility.CreateThumbnail(strFilePath, strOutPutFile, New Size(121, 146))
            Dim img As Image = Utility.GetThumbnailMerge(Common.ImageFromFile(strOutPutFile), My.Resources.livephoto_pic_jpg.Clone, False)

            Me.btnAddJpg.tbBackgroundImage = img
            Me.btnAddJpg.Text = Me.Language.GetString("LivePhoto.Button.ClickUpdate")                      '"点击修改"
            Me.btnAddJpg.tbTextColor = Color.White
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddJpgToShow")
        End Try
    End Sub

    Private Sub AddMovToShow(ByVal strFilePath As String)
        Try
            btnAddMOV.Tag = strFilePath
            Dim strInputFile As String = strFilePath
            Dim strOutputImage As String = Folder.GetTempFilePath()
            Dim info As VideoInfo = MediaMaster.CreateCover(strInputFile, strOutputImage)
            Utility.CreateThumbnail(strOutputImage, strOutputImage, New Size(121, 146))
            Dim img As Image = Utility.GetThumbnailMerge(Common.ImageFromFile(strOutputImage), My.Resources.livephoto_pic_mov.Clone, False)
            Me.btnAddMOV.tbBackgroundImage = img
            Me.btnAddMOV.Text = Me.Language.GetString("LivePhoto.Button.ClickUpdate")   '  "点击修改"
            Me.btnAddMOV.tbTextColor = Color.White
        Catch ex As Exception
            Common.LogException(ex.ToString(), "AddMovToShow")
        End Try
    End Sub
    Private Sub CheckBtnOkEnable()
        Try
            If Me.btnAddJpg.Tag IsNot Nothing AndAlso Me.btnAddMOV.Tag IsNot Nothing Then
                Me.btnOK.Enabled = True
            Else
                Me.btnOK.Enabled = False
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckBtnOkEnable")
        End Try
    End Sub
    Private Function CreateLivePhotosFile(ByVal strJPGPath As String, ByVal strMOVPath As String) As String
        Dim strLivePhotoPath As String = String.Empty
        Try
            If String.IsNullOrEmpty(strJPGPath) OrElse String.IsNullOrEmpty(strMOVPath) Then
                Return strLivePhotoPath
            End If
            Dim strTemp As String = String.Format("IMG_{0}", Me.mintFileNumber.ToString.PadLeft(4, "0"))
            Dim strSourceDirectory As String = Path.Combine(Folder.CacheFolder, strTemp) ' Path.GetFileNameWithoutExtension(strJPGPath)
            Try
                Directory.Delete(strSourceDirectory, True)
            Catch ex As Exception

            End Try
            Folder.CheckFolder(strSourceDirectory)
            File.Copy(strJPGPath, Path.Combine(strSourceDirectory, strTemp & Path.GetExtension(strJPGPath)), True)
            File.Copy(strMOVPath, Path.Combine(strSourceDirectory, strTemp & Path.GetExtension(strMOVPath)), True)
            strLivePhotoPath = strSourceDirectory & ".livephoto"
            Utility.PackFiles(strLivePhotoPath, strSourceDirectory)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "CreateLivePhotosFile")
        End Try
        Return strLivePhotoPath
    End Function

    Private Function CheckImageSize(strPath As String) As Boolean
        Dim result As Boolean = True
        Dim pic As Image = Image.FromFile(strPath)
        If (pic.Width <> 4032 OrElse pic.Height <> 3024) AndAlso (pic.Width <> 3024 OrElse pic.Height <> 4032) Then
            tbMessageBox.Show(Me.mApplication, Me.Language.GetString("LivePhoto.Message.LimitResolvingPower"), Me.Language.GetString("Common.Info"), MessageBoxButtons.OK, MessageBoxIcon.Warning)
            result = False
        End If
        Return result
    End Function
#End Region

    Private Sub lklblUrl_LinkClicked(sender As Object, e As LinkLabelLinkClickedEventArgs) Handles lklblUrl.LinkClicked
        Common.OpenExplorer("https://s.weibo.com/weibo?q=%23%E6%8E%A8livephotos%23&from=default")     '打开论坛链接 http://bbs.tongbu.com/thread-421999-1-1.html
    End Sub
End Class