﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmImportLivePhoto
    Inherits iTong.Components.tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmImportLivePhoto))
        Me.btn_close = New iTong.Components.tbButton()
        Me.btnCancel = New iTong.Components.tbButton()
        Me.btnOK = New iTong.Components.tbButton()
        Me.btnAddJpg = New iTong.Components.tbButton()
        Me.btnAddMOV = New iTong.Components.tbButton()
        Me.lklblUrl = New System.Windows.Forms.LinkLabel()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(353, 3)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 9
        Me.btn_close.TabStop = False
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = True
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'btnCancel
        '
        Me.btnCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnCancel.BackColor = System.Drawing.Color.Transparent
        Me.btnCancel.BindingForm = Nothing
        Me.btnCancel.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnCancel.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnCancel.Location = New System.Drawing.Point(295, 223)
        Me.btnCancel.Name = "btnCancel"
        Me.btnCancel.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnCancel.Selectable = True
        Me.btnCancel.Size = New System.Drawing.Size(65, 23)
        Me.btnCancel.TabIndex = 42
        Me.btnCancel.tbAdriftIconWhenHover = False
        Me.btnCancel.tbAutoSize = False
        Me.btnCancel.tbAutoSizeEx = False
        Me.btnCancel.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_white
        Me.btnCancel.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnCancel.tbBadgeNumber = 0
        Me.btnCancel.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnCancel.tbEndEllipsis = False
        Me.btnCancel.tbIconHoldPlace = True
        Me.btnCancel.tbIconImage = Nothing
        Me.btnCancel.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnCancel.tbIconMore = False
        Me.btnCancel.tbIconMouseDown = Nothing
        Me.btnCancel.tbIconMouseHover = Nothing
        Me.btnCancel.tbIconMouseLeave = Nothing
        Me.btnCancel.tbIconPlaceText = 2
        Me.btnCancel.tbIconReadOnly = Nothing
        Me.btnCancel.tbImageMouseDown = Nothing
        Me.btnCancel.tbImageMouseHover = Nothing
        Me.btnCancel.tbImageMouseLeave = Nothing
        Me.btnCancel.tbProgressValue = 50
        Me.btnCancel.tbReadOnly = False
        Me.btnCancel.tbReadOnlyText = False
        Me.btnCancel.tbShadow = False
        Me.btnCancel.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnCancel.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnCancel.tbShowDot = False
        Me.btnCancel.tbShowMoreIconImg = CType(resources.GetObject("btnCancel.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnCancel.tbShowNew = False
        Me.btnCancel.tbShowProgress = False
        Me.btnCancel.tbShowTip = True
        Me.btnCancel.tbShowToolTipOnButton = False
        Me.btnCancel.tbSplit = "13,11,13,11"
        Me.btnCancel.tbText = "取消"
        Me.btnCancel.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnCancel.tbTextMouseDownPlace = 0
        Me.btnCancel.tbToolTip = ""
        Me.btnCancel.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnCancel.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnCancel.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnCancel.VisibleEx = True
        '
        'btnOK
        '
        Me.btnOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnOK.BackColor = System.Drawing.Color.Transparent
        Me.btnOK.BindingForm = Nothing
        Me.btnOK.Enabled = False
        Me.btnOK.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnOK.Location = New System.Drawing.Point(221, 223)
        Me.btnOK.Name = "btnOK"
        Me.btnOK.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btnOK.Selectable = True
        Me.btnOK.Size = New System.Drawing.Size(65, 23)
        Me.btnOK.TabIndex = 41
        Me.btnOK.tbAdriftIconWhenHover = False
        Me.btnOK.tbAutoSize = False
        Me.btnOK.tbAutoSizeEx = False
        Me.btnOK.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnOK.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnOK.tbBadgeNumber = 0
        Me.btnOK.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnOK.tbEndEllipsis = False
        Me.btnOK.tbIconHoldPlace = True
        Me.btnOK.tbIconImage = Nothing
        Me.btnOK.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnOK.tbIconMore = False
        Me.btnOK.tbIconMouseDown = Nothing
        Me.btnOK.tbIconMouseHover = Nothing
        Me.btnOK.tbIconMouseLeave = Nothing
        Me.btnOK.tbIconPlaceText = 2
        Me.btnOK.tbIconReadOnly = Nothing
        Me.btnOK.tbImageMouseDown = Nothing
        Me.btnOK.tbImageMouseHover = Nothing
        Me.btnOK.tbImageMouseLeave = Nothing
        Me.btnOK.tbProgressValue = 50
        Me.btnOK.tbReadOnly = False
        Me.btnOK.tbReadOnlyText = False
        Me.btnOK.tbShadow = False
        Me.btnOK.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(2, Byte), Integer), CType(CType(144, Byte), Integer), CType(CType(21, Byte), Integer))
        Me.btnOK.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnOK.tbShowDot = False
        Me.btnOK.tbShowMoreIconImg = CType(resources.GetObject("btnOK.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnOK.tbShowNew = False
        Me.btnOK.tbShowProgress = False
        Me.btnOK.tbShowTip = True
        Me.btnOK.tbShowToolTipOnButton = False
        Me.btnOK.tbSplit = "13,11,13,11"
        Me.btnOK.tbText = "确定"
        Me.btnOK.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.tbTextColor = System.Drawing.Color.White
        Me.btnOK.tbTextColorDisable = System.Drawing.Color.White
        Me.btnOK.tbTextColorDown = System.Drawing.Color.White
        Me.btnOK.tbTextColorHover = System.Drawing.Color.White
        Me.btnOK.tbTextMouseDownPlace = 0
        Me.btnOK.tbToolTip = ""
        Me.btnOK.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnOK.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnOK.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnOK.VisibleEx = True
        '
        'btnAddJpg
        '
        Me.btnAddJpg.AllowDrop = True
        Me.btnAddJpg.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAddJpg.BackColor = System.Drawing.Color.Transparent
        Me.btnAddJpg.BindingForm = Nothing
        Me.btnAddJpg.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnAddJpg.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAddJpg.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnAddJpg.Location = New System.Drawing.Point(52, 55)
        Me.btnAddJpg.Name = "btnAddJpg"
        Me.btnAddJpg.Padding = New System.Windows.Forms.Padding(0, 1, 0, 8)
        Me.btnAddJpg.Selectable = True
        Me.btnAddJpg.Size = New System.Drawing.Size(121, 146)
        Me.btnAddJpg.TabIndex = 42
        Me.btnAddJpg.tbAdriftIconWhenHover = False
        Me.btnAddJpg.tbAutoSize = False
        Me.btnAddJpg.tbAutoSizeEx = False
        Me.btnAddJpg.tbBackgroundImage = Global.iTong.My.Resources.Resources.livephoto_pic_jpgini
        Me.btnAddJpg.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnAddJpg.tbBadgeNumber = 0
        Me.btnAddJpg.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAddJpg.tbEndEllipsis = False
        Me.btnAddJpg.tbIconHoldPlace = True
        Me.btnAddJpg.tbIconImage = Nothing
        Me.btnAddJpg.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddJpg.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAddJpg.tbIconMore = False
        Me.btnAddJpg.tbIconMouseDown = Nothing
        Me.btnAddJpg.tbIconMouseHover = Nothing
        Me.btnAddJpg.tbIconMouseLeave = Nothing
        Me.btnAddJpg.tbIconPlaceText = 2
        Me.btnAddJpg.tbIconReadOnly = Nothing
        Me.btnAddJpg.tbImageMouseDown = Nothing
        Me.btnAddJpg.tbImageMouseHover = Nothing
        Me.btnAddJpg.tbImageMouseLeave = Nothing
        Me.btnAddJpg.tbProgressValue = 50
        Me.btnAddJpg.tbReadOnly = False
        Me.btnAddJpg.tbReadOnlyText = False
        Me.btnAddJpg.tbShadow = False
        Me.btnAddJpg.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnAddJpg.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnAddJpg.tbShowDot = False
        Me.btnAddJpg.tbShowMoreIconImg = CType(resources.GetObject("btnAddJpg.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAddJpg.tbShowNew = False
        Me.btnAddJpg.tbShowProgress = False
        Me.btnAddJpg.tbShowTip = True
        Me.btnAddJpg.tbShowToolTipOnButton = False
        Me.btnAddJpg.tbSplit = "13,11,13,11"
        Me.btnAddJpg.tbText = ""
        Me.btnAddJpg.tbTextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.btnAddJpg.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddJpg.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddJpg.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddJpg.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddJpg.tbTextMouseDownPlace = 0
        Me.btnAddJpg.tbToolTip = ""
        Me.btnAddJpg.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAddJpg.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAddJpg.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddJpg.VisibleEx = True
        '
        'btnAddMOV
        '
        Me.btnAddMOV.AllowDrop = True
        Me.btnAddMOV.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnAddMOV.BackColor = System.Drawing.Color.Transparent
        Me.btnAddMOV.BindingForm = Nothing
        Me.btnAddMOV.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btnAddMOV.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnAddMOV.ForeColor = System.Drawing.Color.FromArgb(CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer), CType(CType(64, Byte), Integer))
        Me.btnAddMOV.Location = New System.Drawing.Point(207, 55)
        Me.btnAddMOV.Name = "btnAddMOV"
        Me.btnAddMOV.Padding = New System.Windows.Forms.Padding(0, 1, 0, 8)
        Me.btnAddMOV.Selectable = True
        Me.btnAddMOV.Size = New System.Drawing.Size(121, 146)
        Me.btnAddMOV.TabIndex = 42
        Me.btnAddMOV.tbAdriftIconWhenHover = False
        Me.btnAddMOV.tbAutoSize = False
        Me.btnAddMOV.tbAutoSizeEx = False
        Me.btnAddMOV.tbBackgroundImage = Global.iTong.My.Resources.Resources.livephoto_pic_movini
        Me.btnAddMOV.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnAddMOV.tbBadgeNumber = 0
        Me.btnAddMOV.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnAddMOV.tbEndEllipsis = False
        Me.btnAddMOV.tbIconHoldPlace = True
        Me.btnAddMOV.tbIconImage = Nothing
        Me.btnAddMOV.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddMOV.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnAddMOV.tbIconMore = False
        Me.btnAddMOV.tbIconMouseDown = Nothing
        Me.btnAddMOV.tbIconMouseHover = Nothing
        Me.btnAddMOV.tbIconMouseLeave = Nothing
        Me.btnAddMOV.tbIconPlaceText = 2
        Me.btnAddMOV.tbIconReadOnly = Nothing
        Me.btnAddMOV.tbImageMouseDown = Nothing
        Me.btnAddMOV.tbImageMouseHover = Nothing
        Me.btnAddMOV.tbImageMouseLeave = Nothing
        Me.btnAddMOV.tbProgressValue = 50
        Me.btnAddMOV.tbReadOnly = False
        Me.btnAddMOV.tbReadOnlyText = False
        Me.btnAddMOV.tbShadow = False
        Me.btnAddMOV.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(50, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(156, Byte), Integer))
        Me.btnAddMOV.tbShadowOffset = New System.Drawing.Point(0, -1)
        Me.btnAddMOV.tbShowDot = False
        Me.btnAddMOV.tbShowMoreIconImg = CType(resources.GetObject("btnAddMOV.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnAddMOV.tbShowNew = False
        Me.btnAddMOV.tbShowProgress = False
        Me.btnAddMOV.tbShowTip = True
        Me.btnAddMOV.tbShowToolTipOnButton = False
        Me.btnAddMOV.tbSplit = "13,11,13,11"
        Me.btnAddMOV.tbText = ""
        Me.btnAddMOV.tbTextAlign = System.Drawing.ContentAlignment.BottomCenter
        Me.btnAddMOV.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddMOV.tbTextColorDisable = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddMOV.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddMOV.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer), CType(CType(86, Byte), Integer))
        Me.btnAddMOV.tbTextMouseDownPlace = 0
        Me.btnAddMOV.tbToolTip = ""
        Me.btnAddMOV.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnAddMOV.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnAddMOV.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnAddMOV.VisibleEx = True
        '
        'lklblUrl
        '
        Me.lklblUrl.ActiveLinkColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblUrl.AutoSize = True
        Me.lklblUrl.Font = New System.Drawing.Font("宋体", 9.5!)
        Me.lklblUrl.ForeColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblUrl.LinkBehavior = System.Windows.Forms.LinkBehavior.NeverUnderline
        Me.lklblUrl.LinkColor = System.Drawing.Color.FromArgb(CType(CType(70, Byte), Integer), CType(CType(152, Byte), Integer), CType(CType(197, Byte), Integer))
        Me.lklblUrl.Location = New System.Drawing.Point(12, 227)
        Me.lklblUrl.Name = "lklblUrl"
        Me.lklblUrl.Size = New System.Drawing.Size(142, 19)
        Me.lklblUrl.TabIndex = 44
        Me.lklblUrl.TabStop = True
        Me.lklblUrl.Text = "更多LivePhoto壁纸>>"
        '
        'frmImportLivePhoto
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(380, 262)
        Me.Controls.Add(Me.lklblUrl)
        Me.Controls.Add(Me.btnAddMOV)
        Me.Controls.Add(Me.btnAddJpg)
        Me.Controls.Add(Me.btnCancel)
        Me.Controls.Add(Me.btnOK)
        Me.Controls.Add(Me.btn_close)
        Me.Name = "frmImportLivePhoto"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_blank
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "3,33,4,6"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.Text = "导入Live Photos"
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents btnCancel As iTong.Components.tbButton
    Friend WithEvents btnOK As iTong.Components.tbButton
    Friend WithEvents btnAddJpg As iTong.Components.tbButton
    Friend WithEvents btnAddMOV As iTong.Components.tbButton
    Friend WithEvents lklblUrl As System.Windows.Forms.LinkLabel
End Class
