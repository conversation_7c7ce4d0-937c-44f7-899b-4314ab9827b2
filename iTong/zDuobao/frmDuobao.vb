﻿Public Class frmDuobao

    Private devManager As MobileDeviceManager

    Private mDevice As iPhoneDevice

    '下载要用到的对像
    Private mCoreUpdate As CoreUpdateHelper = Nothing
    Private mDownloadManager As MultiThreadDownload = Nothing
    Private mInstallHelper As iPhoneInstallHelper = Nothing

    Private mJinBaoHelper As JinBaoHelper


#Region "---初始化---"

    Public Sub New(ByVal lang As LanguageInterface)
        ' 此调用是设计器所必需的。
        InitializeComponent()
        Me.Language = lang

    

        ' 在 InitializeComponent() 调用之后添加任何初始化。
        devManager = MobileDeviceManager.Instance()
        Me.mJinBaoHelper = JinBaoHelper.Instance()
        AddHandler Me.mJinBaoHelper.InstallJinBaoEvent, AddressOf OnInstallJinBao
        Me.tmrSave = New Timer
        Me.tmrSave.Interval = 100
        Me.tmrSave.Enabled = True
        RemoveHandler Me.tmrSave.Tick, AddressOf TmrSave_Tick
        AddHandler Me.tmrSave.Tick, AddressOf TmrSave_Tick

        RemoveHandler Me.txtInvationCode.TextChanged, AddressOf txtInvationCode_TextChanged
        AddHandler Me.txtInvationCode.TextChanged, AddressOf txtInvationCode_TextChanged

        Dim pinfo As PackageInfo = HtmlHelper.GetAppInfoByItemId(ServerIniSetting.GetJinBaoItemId())
        If pinfo IsNot Nothing Then
            Me.lblVersionValue.Text = pinfo.Version
        End If

        Me.txtInvationCode.Text = IniSetting.GetJinBaoInvitationCode()
    End Sub

    Protected Overrides Sub InitControls()
        MyBase.InitControls()
        Me.Icon = My.Resources.iTong
        Me.InitDeviceManager()
        Me.btnInstall.Enabled = False
        Me.btnInstall.Text = "请连接设备"

        Dim strVer As String = String.Empty
        If My.Application.Info.Version.MinorRevision = 0 Then
            strVer = My.Application.Info.Version.ToString(3)
        Else
            strVer = My.Application.Info.Version.ToString(4)
        End If
        Me.Text = String.Format("{0} v{1}_{2}", Me.Text, strVer, IIf(Common.IsX64, "64bit", "32bit"))

        Dim thd As New Threading.Thread(AddressOf Start2CheckiTunesSupport)
        thd.IsBackground = True
        thd.Start()

        Me.lblName.Font = Common.CreateFont("微软雅黑", 20.0!, FontStyle.Regular)

    End Sub

    Private Sub Start2CheckiTunesSupport()
        Try
            Utility.WaitSeconds(1)
            MainForm.CheckUpdate(Me, Me.Language)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "frmDuobao_Start2CheckiTunesSupport")
        End Try
    End Sub

    Protected Overrides Sub SetInterface()
        MyBase.SetInterface()

    End Sub

    Private Sub InitDeviceManager()
        RemoveHandler devManager.ConnectionChanged, AddressOf iPhone_ConnectionChangedEvent
        AddHandler devManager.ConnectionChanged, AddressOf iPhone_ConnectionChangedEvent

        RemoveHandler devManager.ConnectFailed, AddressOf iPhone_ConnectFailed
        AddHandler devManager.ConnectFailed, AddressOf iPhone_ConnectFailed

        devManager.StartListen()
    End Sub

#End Region

#Region "---手机连接---"

    Private Sub iPhone_ConnectionChangedEvent(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
        '等待窗口句柄创建成功，避免 BeginInvoke 失败
        While Me.IsHandleCreated = False
            Application.DoEvents()
        End While

        BeginInvoke(New iPhone_ConnectionChangedDelegate(AddressOf iPhone_ConnectionChanged), sender, args)
    End Sub

    Private Delegate Sub iPhone_ConnectionChangedDelegate(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
    Private Sub iPhone_ConnectionChanged(ByVal sender As Object, ByVal args As ConnectionChangedEventArgs)
        Try
            If args.Action = DeviceAction.kConnected AndAlso Me.mDevice Is Nothing Then
                'Me.btnInstall.Text = "一键安装"
                Me.mDevice = args.Device
                Me.btnInstall.Text = "安装到" & Common.StringFormat(args.Device.DeviceName, 10, True, False, True)
                Me.btnInstall.Enabled = True
                Me.lblInstallInfo.Text = ""
                Me.pbInstallProcess.Visible = False
            ElseIf args.Action = DeviceAction.kDisConnected Then
                If MobileDeviceManager.Instance().ConnectedDevices.Count = 0 Then
                    Me.btnInstall.Text = "请连接设备"
                    Me.btnInstall.Enabled = False
                    Me.mDevice = Nothing
                    Return
                End If

                For Each Item As iPhoneDevice In MobileDeviceManager.Instance().ConnectedDevices
                    If Item IsNot Nothing AndAlso Item.IsConnected Then
                        Me.mDevice = Item
                        Me.btnInstall.Text = "安装到" & Common.StringFormat(args.Device.DeviceName, 10, True, False, True)
                        Me.btnInstall.Enabled = True
                        Exit For
                    End If
                Next
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "iPhone_ConnectionChanged")
        End Try
    End Sub

    Public Sub iPhone_ConnectFailed(ByVal sender As Object, ByVal args As ConnectFailedEventArgs)
        Try
            Dim device As iPhoneDevice = sender
            Common.LogException("ConnectFailed: " & device.DeviceName & vbTab & device.ProductVersion & vbTab & CInt(args.ErrorCode).ToString())

            Dim strMsg As String = String.Empty
            Select Case args.ErrorCode
                Case kAMDError.kAMDPasswordProtectedError
                    '"未能连接到设备 {0}，您必须在设备上输入密码，然后再尝试连接。"
                    strMsg = String.Format(Me.Language.GetString("Main.Message.ShouldTypePwd"), device.DeviceName)
                    If Val(device.ProductVersion.Substring(0, 1)) >= 7 Then
                        strMsg = String.Format(Me.Language.GetString("Main.Message.TrustPC"), device.DeviceName)
                    End If
                Case kAMDError.kAMDTrustComputerError
                    '"未能连接到设备 {0}，您必须在设备上信任此电脑，然后再尝试连接。"
                    strMsg = String.Format(Me.Language.GetString("Main.Message.TrustPC"), device.DeviceName)
                Case Else
                    strMsg = Me.Language.GetString("Main.Message.DeviceIsLocked")
            End Select

            '"您的设备已被密码锁定，iTong无法连接。您必须输入密码，然后才能继续使用。"
            If MessageBox.Show(Me, strMsg, _
                                 Me.Language.GetString("Common.Info"), _
                                 MessageBoxButtons.RetryCancel, _
                                 MessageBoxIcon.None, _
                                 MessageBoxDefaultButton.Button1) = Windows.Forms.DialogResult.Retry Then
                args.Retry = True
            End If
        Catch ex As Exception
        End Try
    End Sub

#End Region

#Region "--- 窗体事件 ---"

    Private Sub btnInstall_Click(sender As Object, e As EventArgs) Handles btnInstall.Click
        Try
            Me.Height = 390

            'Me.btnInstall.Location = New Point(Me.btnInstall.Left, pbInstallProcess.Bottom + 20)
            Me.btnInstall.Enabled = False
            Me.lblInstallInfo.Text = "准备安装"
            Me.pbInstallProcess.Visible = True
            Me.pbInstallProcess.tbPlayValue = 0
            Application.DoEvents()
            mJinBaoHelper.OnKeyInstall(Me.mDevice)
        Catch ex As Exception
            Common.LogException(ex.ToString(), "btnInstall_Click")
        End Try
    End Sub

    Private Sub lblGetInvitationCode_Click(sender As Object, e As EventArgs) Handles lblGetInvitationCode.Click
        Me.mJinBaoHelper.GetInivitationCode()
    End Sub

    Protected Overrides Sub OnFormClosed(e As FormClosedEventArgs)
        MyBase.OnFormClosed(e)
        ProcForm.Instance.ExitApp()
    End Sub

#End Region

#Region "--- timer"

    Private Sub txtInvationCode_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs)

        If Me.m_dblLastKeyTime = 0 Then
            Me.m_dblLastKeyTime = DateAndTime.Timer
        End If
        MyBase.OnTextChanged(e)

    End Sub

    Private tmrSave As Timer
    Private m_dblLastKeyTime As Double = 0
    Private Sub TmrSave_Tick(ByVal sender As Object, ByVal e As System.EventArgs)
        If Me.m_dblLastKeyTime > 0 AndAlso DateAndTime.Timer - Me.m_dblLastKeyTime > 2 Then

            Me.m_dblLastKeyTime = 0
            If txtInvationCode.Text.Trim().Length = 0 OrElse Me.mDevice Is Nothing Then
                Return
            End If

            Me.lblTip.Visible = False
            IniSetting.SetJinBaoInvitationCode(Me.txtInvationCode.Text.Trim())
            If mJinBaoHelper.SaveIniviteAndChannel(Me.mDevice, txtInvationCode.Text.Trim()) Then
                Me.lblTip.ForeColor = Color.Green
                Me.lblTip.Text = "保存成功"
            Else
                Me.lblTip.ForeColor = Color.Red
                Me.lblTip.Text = "保存失败"
            End If
            Me.lblTip.Visible = True
        End If

    End Sub

#End Region

#Region "---  错误信息处理  ---"

    Private Sub tsmiGetLogs_Click(sender As Object, e As EventArgs) Handles tsmiGetLogs.Click
        ProcForm.CreateError()
    End Sub
    Private Sub btn_Setting_Click(sender As Object, e As EventArgs) Handles btn_Setting.Click
        Me.menuMain.Show(Me.btn_Setting, New Point(0, Me.btn_Setting.Height))
    End Sub

    Private Sub tsmUpdate_Click(sender As Object, e As EventArgs) Handles tsmUpdate.Click
        If MainForm.CheckUpdate(Me, Me.Language) Then
            Return
        End If
        ProcForm.Instance().StartToCheckUpdate(True)
    End Sub

    'Public Function CheckUpdate() As Boolean
    '    Dim blnReturn As Boolean = False
    '    Try
    '        Dim kry As ModelKey = ModelKey.iTong32UnSupport
    '        '检测是否需要升级到32位助手
    '        '"检测到您安装的是32位的iTunes，\r\n无法正常使用同步助手（64位），\r\n请点击""确定""升级到正确的同步助手版本。"
    '        If Common.UpdateAppToX86() Then
    '            tbMessageBox.Show(Me, "检测到您安装的是{0}位的iTunes，无法正常使用进宝安装器（{1}位），请点击 ""确定"" 升级到正确的同步助手版本。", "32", "64", _
    '                             Me.Language.GetString("Common.Info"), _
    '                             MessageBoxButtons.OK, _
    '                             MessageBoxIcon.Information, _
    '                             MessageBoxDefaultButton.Button1)

    '            Dim strAppName As String = "进宝安装器"
    '            HelperExeManager.CheckUpdateApp2FitiTunes(True, strAppName, Application.ExecutablePath, HelperExeManager.CheckUpdateAction.CheckUpdateForX86)
    '            Common.LogException("需要升级到32位的进宝安装器")

    '            blnReturn = True
    '            kry = ModelKey.iTong64UnSupport
    '        End If

    '        '检测是否需要升级到64位助手
    '        If Common.UpdateAppToX64() Then
    '            tbMessageBox.Show(Me, "检测到您安装的是{0}位的iTunes，无法正常使用进宝安装器（{1}位），请点击 ""确定"" 升级到正确的同步助手版本。", "64", "32", _
    '                             Me.Language.GetString("Common.Info"), _
    '                             MessageBoxButtons.OK, _
    '                             MessageBoxIcon.Information, _
    '                             MessageBoxDefaultButton.Button1)

    '            Dim strAppName As String = "进宝安装器"
    '            HelperExeManager.CheckUpdateApp2FitiTunes(True, strAppName, Application.ExecutablePath, HelperExeManager.CheckUpdateAction.CheckUpdateForX64)
    '            Common.LogException("需要升级到64位的进宝安装器")

    '            blnReturn = True
    '            kry = ModelKey.iTong32UnSupport
    '        End If

    '        'If blnReturn Then
    '        '    '收集助手是32还是64位
    '        '    ActionCollectHelper.ShowiTongAccition(Nothing, kry)
    '        'End If
    '    Catch ex As Exception
    '        Common.LogException(ex.ToString(), "frmMain_CheckUpdate")
    '    End Try
    '    Return blnReturn
    'End Function
#End Region

    Private Delegate Sub OnInstallJinBaoHandler(ByVal sender As Object, ByVal e As InstallJinBaoProgress)
    Private Sub OnInstallJinBao(ByVal sender As Object, ByVal e As InstallJinBaoProgress)
        Try
            If Me.InvokeRequired Then
                Me.Invoke(New OnInstallJinBaoHandler(AddressOf OnInstallJinBao), New Object() {sender, e})
            Else
                If e.Status = InstallJinBaoProgress.InstallJinBaoStatus.Starting Then
                    Me.lblInstallInfo.Text = "开始安装"
                    Me.lblInstallInfo.ForeColor = Color.Black
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.Downloading Then
                    Me.lblInstallInfo.Text = "正在下载..."
                    Me.lblInstallInfo.ForeColor = Color.Black
                    Me.pbInstallProcess.Visible = True
                    Me.pbInstallProcess.tbPlayValue = e.Value
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.DownloadSucceed Then
                    Me.lblInstallInfo.Text = "下载成功"
                    Me.lblInstallInfo.ForeColor = Color.Black
                    Me.pbInstallProcess.Visible = True
                    Me.pbInstallProcess.tbPlayValue = e.Value
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.DownloadFailed Then
                    Me.lblInstallInfo.Text = "下载失败"
                    Me.lblInstallInfo.ForeColor = Color.Red
                    If Me.mDevice IsNot Nothing Then
                        Me.btnInstall.Enabled = True
                    End If
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.Authorizeing Then
                    Me.lblInstallInfo.Text = "正在授权"
                    Me.lblInstallInfo.ForeColor = Color.Black
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.AuthorizeFailed Then
                    Me.lblInstallInfo.Text = "授权失败"      '授权失败
                    Me.lblInstallInfo.ForeColor = Color.Red
                    If Me.mDevice IsNot Nothing Then
                        Me.btnInstall.Enabled = True
                    End If
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.WaitInstalling Then
                    Me.lblInstallInfo.Text = e.Message
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.UnZipping Then
                    Me.lblInstallInfo.Text = "正在解压"
                    Me.lblInstallInfo.ForeColor = Color.Black
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.Transfering Then
                    Me.lblInstallInfo.Text = "正在上传"
                    Me.lblInstallInfo.ForeColor = Color.Black
                    Me.pbInstallProcess.Visible = True
                    Me.pbInstallProcess.tbPlayValue = e.Value
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.Installing Then
                    Me.lblInstallInfo.Text = "正在安装"
                    Me.lblInstallInfo.ForeColor = Color.Black
                    If e.Value <> 0 Then
                        Me.pbInstallProcess.tbPlayValue = e.Value
                    End If
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.InstallFailed Then
                    Me.lblInstallInfo.Text = e.Message        '安装失败
                    ' Me.pbInstallProcess.tbPlayValue = e.Value
                    Me.lblInstallInfo.ForeColor = Color.Red
                    If Me.mDevice IsNot Nothing Then
                        Me.btnInstall.Enabled = True
                    End If
                ElseIf e.Status = InstallJinBaoProgress.InstallJinBaoStatus.InstallSucceed Then

                    If GetJinBaoInstallDownloadFailed() = True Then
                        Me.lblInstallInfo.Text = "下载失败"
                        Me.lblInstallInfo.ForeColor = Color.Red
                        If Me.mDevice IsNot Nothing Then
                            Me.btnInstall.Enabled = True
                        End If
                        Return
                    ElseIf GetJinBaoAuthorizedFailed() = True Then
                        Me.lblInstallInfo.Text = "授权失败"
                        Me.lblInstallInfo.ForeColor = Color.Red
                        If Me.mDevice IsNot Nothing Then
                            Me.btnInstall.Enabled = True
                        End If
                        Return
                    ElseIf GetJinBaoInstallFailed() = True Then
                        Me.lblInstallInfo.Text = "安装失败"
                        Me.lblInstallInfo.ForeColor = Color.Red
                        If Me.mDevice IsNot Nothing Then
                            Me.btnInstall.Enabled = True
                        End If
                        Return
                    End If

                    Me.lblInstallInfo.Text = "安装成功"
                    Me.lblInstallInfo.ForeColor = Color.Green
                    'Me.btnInstall.Enabled = True
                    Me.pbInstallProcess.tbPlayValue = e.Value
                    mJinBaoHelper.SaveIniviteAndChannel(Me.mDevice, txtInvationCode.Text.Trim())   '保存渠道号

                End If

            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnInstallJinBao")
        End Try
    End Sub


#Region "--- 测试的setting"

#Region "--- 设置安装失败的标记 ---"
    Public Shared Function GetJinBaoInstallFailed() As Boolean
        Dim blnReturn As Boolean = False
        Dim strValue As String = IniClass.GetIniSectionKey("Setting", "JinBaoInstallFailed", Folder.ConfigIniFile)
        If strValue = "True" Then
            blnReturn = True
        End If
        Return blnReturn

    End Function

#End Region

#Region "--- 设置下载失败的标记 ---"
    Public Shared Function GetJinBaoInstallDownloadFailed() As Boolean
        Dim blnReturn As Boolean = False
        Dim strValue As String = IniClass.GetIniSectionKey("Setting", "JinBaoDownloadFailed", Folder.ConfigIniFile)
        If strValue = "True" Then
            blnReturn = True
        End If
        Return blnReturn

    End Function
#End Region

#Region "--- 设置授权失败的标记 ---"
    Public Shared Function GetJinBaoAuthorizedFailed() As Boolean
        Dim blnReturn As Boolean = False
        Dim strValue As String = IniClass.GetIniSectionKey("Setting", "JinBaoAuthorizedFailed", Folder.ConfigIniFile)
        If strValue = "True" Then
            blnReturn = True
        End If
        Return blnReturn

    End Function
#End Region

#End Region

End Class