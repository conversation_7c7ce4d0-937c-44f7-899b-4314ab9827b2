﻿'移到CoreModuleCS
Public Class JinBaoHelper

    Private mDownManager As MultiThreadDownload
    Protected Shared mJinBaoHelper As JinBaoHelper
    Protected Shared mLocker As New Object
    Public Event InstallJinBaoEvent(ByVal sender As Object, ByVal e As InstallJinBaoProgress)
    Private mItemid As String = ServerIniSetting.GetJinBaoItemId()
    Private mAuthorizeHelper As AuthorizeHelper
    Private mDevice As iPhoneDevice

    Public Sub New()
        InitDownloadManager()
    End Sub

#Region "--- 单实例 ---"

    Private Shared locker As New Object

    Public Shared Function Instance() As JinBaoHelper
        Try
            SyncLock mLocker
                If mJinBaoHelper Is Nothing Then
                    mJinBaoHelper = New JinBaoHelper()

                End If
            End SyncLock
        Catch ex As Exception
        End Try

        Return mJinBaoHelper
    End Function
#End Region

#Region "--- 私有方法 ---"
    Public Sub OnKeyInstall(ByVal device As iPhoneDevice)
        Try
            Me.mDevice = device
            Me.InitiPhoneInstallHelper()
            ' RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.Starting))
            Dim strFilePath As String = ""
            If CheckFileExist(strFilePath) Then
                iPhoneInstallHelper.GetInstance(device).InstallApplication(strFilePath, True, False)
                Return
            End If

            WebSiteHelper.Instance.DownloadAppByIDEx(Nothing, device, Me.mItemid, "com.newsand.duobao", "1元夺宝")
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnKeyInstall")
        End Try
    End Sub

    Public Function CheckFileExist(ByRef filepath As String) As Boolean
        Dim blnResult As Boolean = False
        Try
            Dim pinfo As PackageInfo = HtmlHelper.GetAppInfoByItemId(Me.mItemid)
            Dim strFileName As String = String.Format("{0}_v{1}.ipa", pinfo.Name, pinfo.Version)
            filepath = Path.Combine(Folder.AppFolder & "\Download", strFileName)
            If File.Exists(filepath) Then
                blnResult = True
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString(), "CheckFileExist")
        End Try
        Return blnResult
    End Function

    Public Function SaveIniviteAndChannel(ByVal device As iPhoneDevice, ByVal inivationCode As String) As Boolean
        Dim blnResult As Boolean = False
        Try
            If device Is Nothing Then
                Return blnResult
            End If
            Dim strChannel As String = "tbzs"
            If Folder.AppType = RunType.DuoBao Then
                strChannel = "jinbaoazq"
            End If

            Dim strFileOnDevice As String = "/iTunes_Control/iTunes/duobao.plist"
            Dim dicValues As Dictionary(Of Object, Object) = New Dictionary(Of Object, Object)
            dicValues.Add("Invitation_code", inivationCode)
            dicValues.Add("channel", strChannel)
            Dim strPlist As String = Path.Combine(Folder.CacheFolder, "dubao.plist")
            If iTong.Device.CoreFoundation.WritePlist(dicValues, strPlist) Then
                If device.CopyToPhone(device.DefaultAfcHandle, strPlist, strFileOnDevice) Then
                    blnResult = True
                End If
            End If
        Catch ex As Exception
            Common.LogException(ex.ToString(), "SaveIniviteAndChannel")
        End Try
        Return blnResult
    End Function

    Public Sub GetInivitationCode()
        Try
            Dim frm As frmJinbaoCourse
            For Each ct1 As Form In Application.OpenForms
                If TypeOf ct1 Is frmJinbaoCourse Then
                    frm = ct1
                    Exit For
                End If
            Next

            If frm Is Nothing OrElse frm.IsDisposed Then
                frm = New frmJinbaoCourse()
                frm.StartPosition = FormStartPosition.CenterScreen
            End If

            frm.Show()
            frm.Activate()
        Catch ex As Exception
            Common.LogException(ex.ToString(), "GetInivitationCode")
        End Try
    End Sub

#End Region

#Region "--- 下载事件 ---"
    Private Sub InitDownloadManager()
        Try
            Me.mDownManager = MultiThreadDownload.Instance
            tbPlugins.DownloadManager = MultiThreadDownload.Instance()

            AddHandler Me.mDownManager.TaskDelete, AddressOf OnTaskDelete
            AddHandler Me.mDownManager.DownloadItemCallBack, AddressOf OnDownloading
            AddHandler Me.mDownManager.DownloadItemCompleted, AddressOf OnDownloaded
            AddHandler Me.mDownManager.TaskCombine, AddressOf OnDownTaskCombine
            'AddHandler Me.mDownManager.TaskHasDownloadSucceed, AddressOf OnTaskHasDownloadSucceed
            'AddHandler Me.mDownManager.TaskExist, AddressOf OnTaskExists
        Catch ex As Exception
            Common.LogException(ex.ToString(), "InitDownloadManager")
        End Try

    End Sub

    Private Sub InitiPhoneInstallHelper()
        Dim helper As iPhoneInstallHelper = iPhoneInstallHelper.GetInstance(Me.mDevice)
        RemoveHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
        AddHandler helper.InstallEventHandler, AddressOf Me.OnInstallEvent
    End Sub

    Private Sub OnDownloading(ByVal sender As Object, ByVal e As MultiThreadDownloadEventArgs)
        Try
            Dim item As MultiThreadDownloadItem = sender
            If item.ItemInfo.ItemId <> mItemid Then
                Return
            End If
            If item.ItemInfo.ResourceSize = 0 Then
                Return
            End If
            Dim intValue As Integer = IIf(e.ReceiveSize = e.ResourceSize, 99, Math.Round(e.ReceiveSize / e.ResourceSize, 2) * 100)

            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(intValue, InstallJinBaoProgress.InstallJinBaoStatus.Downloading))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnDownloading")
        End Try

    End Sub

    Private Sub OnDownTaskCombine(ByVal item As MultiThreadDownloadItem)
        AppleDownload.CombineAppInfo(item.ItemInfo)
    End Sub

    Private Sub OnDownloaded(ByVal sender As Object, ByVal e As MultiThreadDownloadCompletedEventArgs)
        Try
            Dim item As MultiThreadDownloadItem = sender
            If item.ItemInfo.ItemId <> mItemid Then
                Return
            End If

            Dim isSucceed As Boolean = (e.Cancel = False AndAlso e.ReceiveSize >= e.ResourceSize AndAlso e.ResourceSize > 0)
            If Not isSucceed Then
                RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(100, InstallJinBaoProgress.InstallJinBaoStatus.DownloadFailed))
                Return
            End If
            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(100, InstallJinBaoProgress.InstallJinBaoStatus.DownloadSucceed))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnDownloaded")
        End Try
    End Sub

    Private Sub OnTaskDelete(ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.ItemId <> mItemid Then
                Return
            End If
            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.TaskDelete))

        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnTaskDelete")
        End Try
    End Sub

    Private Sub OnTaskStop(ByVal item As MultiThreadDownloadItem)
        Try
            If item.ItemInfo.ItemId <> mItemid Then
                Return
            End If
            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.TaskStop))
        Catch ex As Exception
            Common.LogException(ex.ToString(), "OnTaskStop")
        End Try
    End Sub
#End Region

#Region "--- 授权 ---"
    'Private Sub InitAuthorizeHelper(ByVal isConnect As Boolean, ByVal device As iPhoneDevice)
    '    If isConnect Then
    '        If device Is Nothing Then
    '            Return
    '        End If

    '        Me.mAuthorizeHelper = AuthorizeHelper.GetInstance(device)

    '        RemoveHandler mAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart
    '        RemoveHandler mAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted
    '        AddHandler mAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart
    '        AddHandler mAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted

    '    Else
    '        If Me.mAuthorizeHelper IsNot Nothing Then
    '            RemoveHandler mAuthorizeHelper.AuthorizeStart, AddressOf AuthorizeHelper_AuthorizeStart
    '            RemoveHandler mAuthorizeHelper.AuthorizeCompleted, AddressOf AuthorizeHelper_AuthorizeCompleted

    '            Me.mAuthorizeHelper = Nothing

    '        End If
    '    End If
    'End Sub

    'Private Sub AuthorizeHelper_AuthorizeStart(ByVal sender As Object, ByVal e As EventArgs)
    '    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.Authorizeing))
    'End Sub

    'Private Sub AuthorizeHelper_AuthorizeCompleted(ByVal sender As Object, ByVal e As AuthorizeEventArgs)
    '    '不再继续授权 或 不是修复闪退
    '    If e.Result = SoftDownloadErrorState.AuthNoCotinue OrElse Not e.IsRepaire Then
    '        Return
    '    End If

    '    Dim strText As String = ""
    '    Dim isSucceed As Boolean = False

    '    Select Case e.Result
    '        Case SoftDownloadErrorState.AuthFailed
    '            isSucceed = False
    '            If e.Code = "-6" Then
    '                strText = "由于网络繁忙，您此次没有授权成功，请稍候再试"            '"由于网络繁忙，您此次没有授权成功，请稍候再试。"
    '            ElseIf e.Code = "-7" Then
    '                strText = "授权计算失败"    '"授权计算失败"
    '            Else
    '                strText = "修复失败"   '"修复失败。"
    '            End If

    '        Case SoftDownloadErrorState.AuthFailedGetFairPlayCertificate
    '            isSucceed = False
    '            strText = "授权失败，无法获取设备数据，请尝试重启"     '"无法获取{0}数据，请尝试重启{0}"

    '        Case SoftDownloadErrorState.AuthFailedGetAfsyncRq
    '            isSucceed = False
    '            strText = "授权服务启动失败" '"授权服务启动失败
    '        Case SoftDownloadErrorState.LoginFailedWithNetworkError
    '            isSucceed = False
    '            strText = "授权失败，无法访问服务器"    '"无法访问服务器"

    '        Case SoftDownloadErrorState.Succeed
    '            isSucceed = True

    '        Case SoftDownloadErrorState.AuthSucceedServerNoData
    '            isSucceed = True
    '    End Select

    '    If isSucceed Then
    '        RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.AuthorizeSucceed))
    '        Dim blnInstalResult As Boolean = Me.mDevice.InstallApplication(Me.mPackagePath, _
    '                                          New InstallApplicationEventHandler(AddressOf OnInstallApplication), _
    '                                          New DataTransferEventHandler(AddressOf OnDataTransfer), Nothing, Nothing, Nothing)
    '    Else
    '        RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.AuthorizeFailed, strText))
    '    End If
    'End Sub
#End Region

#Region "--- 安装 ---"
    'Private Sub OnInstallApplication(ByVal sender As Object, ByVal e As InstallApplicationEventArgs)

    '    Select Case e.InstallProgress
    '        Case InstallapplicationProgress.OnUnzip
    '            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(Me.m_LastTransferProgress, InstallJinBaoProgress.InstallJinBaoStatus.UnZipping, "正在解压"))

    '        Case InstallapplicationProgress.OnTransfer
    '            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(Me.m_LastTransferProgress, InstallJinBaoProgress.InstallJinBaoStatus.Transfering, "正在上传"))

    '        Case InstallapplicationProgress.OnInstall
    '            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(Me.m_LastTransferProgress, InstallJinBaoProgress.InstallJinBaoStatus.Installing, "正在安装"))

    '        Case InstallapplicationProgress.Completed
    '            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(Me.m_LastTransferProgress, InstallJinBaoProgress.InstallJinBaoStatus.InstallSucceed))

    '        Case InstallapplicationProgress.InstallFailed
    '            '"安装失败 "
    '            Dim strFailMsg As String = "安装失败"

    '            Select Case e.LastErrType
    '                Case InstallFailureType.InstallFailureUnzip
    '                    strFailMsg = "解压失败，该软件包可能已损坏"           '"解压失败，该软件包可能已损坏！"
    '                Case InstallFailureType.InstallFailureFileNoExist
    '                    strFailMsg = "没有发现 app 程序，不能安装此 pxl"         '"没有发现 app 程序，不能安装此 pxl！"
    '                Case InstallFailureType.InstallFailureFileNoMatch
    '                    strFailMsg = "软件包格式不符合"             '"软件包格式不符合"
    '                Case InstallFailureType.InstallFailureUpload
    '                    strFailMsg = "连接不稳，请拔插设备后重试！"          '"连接不稳，请拔插设备后重试！"
    '                Case InstallFailureType.InstallFailureNotCompatible
    '                    strFailMsg = "安装包与设备不匹配，请重新下载"          '"安装包与设备不匹配，请重新下载！"
    '                Case InstallFailureType.InstallFailureOverdue
    '                    strFailMsg = "安装包已过期，请重新下载！"               '"安装包已过期，请重新下载！"
    '                Case InstallFailureType.InstallFailurePathIllegal
    '                    strFailMsg = "文件路径非法！"         '"文件路径非法！"
    '                Case InstallFailureType.InstallFailureStartServer
    '                    strFailMsg = "安装服务启动失败！"         '"安装服务启动失败！"                    
    '                Case Else
    '                    If e.LastErrMsg = "" Then
    '                        strFailMsg = "安装失败，请卸载后再重新安装。"         '"安装失败，请卸载后再重新安装。"
    '                    Else
    '                        strFailMsg &= e.LastErrMsg
    '                        If iPhoneDevice.IsCodesignOverdue(strFailMsg) Then
    '                            strFailMsg = "安装失败,软件已过期"         '"安装失败,软件已过期"
    '                        End If
    '                    End If

    '            End Select
    '            RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.InstallFailed, strFailMsg))
    '    End Select

    'End Sub

    'Private Sub OnDataTransfer(ByVal e As DataTransferEventArgs)

    '    Dim transferProgress As Integer = Me.m_LastTransferProgress

    '    If e.FileSize = 0 Then
    '        transferProgress = 0
    '    Else
    '        transferProgress = e.TransSize * 100 \ e.FileSize
    '    End If


    '    If Me.m_LastTransferProgress <> transferProgress Then
    '        Me.m_LastTransferProgress = transferProgress

    '    End If

    'End Sub
#End Region

#Region "--- 进度回调 ---"

    Private Delegate Sub OnInstallEventHandler(ByVal sender As Object, ByVal args As InstallArgs)
    Private Sub OnInstallEvent(ByVal sender As Object, ByVal args As InstallArgs)
        Try
            If args.PackageInfo.ItemId <> mItemid Then
                Return
            End If

            If args.PackageInfo IsNot Nothing Then
                If args.InstallState = InstallState.Transfering Then
                    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(args.TransferProgress, InstallJinBaoProgress.InstallJinBaoStatus.Transfering, "正在上传"))
                ElseIf args.InstallState = InstallState.Waiting Then
                    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(args.TransferProgress, InstallJinBaoProgress.InstallJinBaoStatus.WaitInstalling, "等待安装"))
                ElseIf args.InstallState = InstallState.Installing Then
                    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(args.TransferProgress, InstallJinBaoProgress.InstallJinBaoStatus.Installing, "正在安装"))
                ElseIf args.InstallState = InstallState.Succeed Then
                    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(100, InstallJinBaoProgress.InstallJinBaoStatus.InstallSucceed, "安装成功"))
                ElseIf args.InstallState = InstallState.HaveNotInstall Then
                    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.InstallFailed, args.ErrorMsg))

                ElseIf args.InstallState = InstallState.UnSucceed Then
                    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(args.TransferProgress, InstallJinBaoProgress.InstallJinBaoStatus.InstallFailed, IIf(String.IsNullOrEmpty(args.ErrorMsg), "安装失败", args.ErrorMsg)))
                ElseIf args.InstallState = InstallState.AuthBegin Then
                    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.Authorizeing, "正在授权"))
                ElseIf args.InstallState = InstallState.AuthFail Then
                    RaiseEvent InstallJinBaoEvent(Me, New InstallJinBaoProgress(0, InstallJinBaoProgress.InstallJinBaoStatus.AuthorizeFailed, "授权失败"))
                End If
            End If

        Catch ex As Exception
            Common.LogException(ex.ToString, "frmDownloaded.OnInstallEventHandler")
        End Try

    End Sub

#End Region

End Class


Public Class InstallJinBaoProgress
    Inherits System.EventArgs

    Sub New(ByVal iValue As Integer, ByVal status As InstallJinBaoStatus, Optional ByVal message As String = "")
        Me.mValue = iValue
        Me.mStatus = status
        Me.mMessage = message
    End Sub

    Private mValue As Integer = 0
    Public Property Value() As Integer
        Get
            Return Me.mValue
        End Get
        Set(ByVal value As Integer)
            Me.mValue = value
        End Set
    End Property

    Private mStatus As InstallJinBaoStatus
    Public Property Status() As Integer
        Get
            Return Me.mStatus
        End Get
        Set(ByVal value As Integer)
            Me.mStatus = value
        End Set
    End Property

    Private mMessage As String = ""
    Public Property Message() As String
        Get
            Return Me.mMessage
        End Get
        Set(value As String)
            Me.mMessage = value
        End Set
    End Property

    Public Enum InstallJinBaoStatus
        Starting = 0
        DownloadFailed = 1
        Downloading = 2
        DownloadSucceed = 3
        Authorizeing = 4
        AuthorizeFailed = 5
        AuthorizeSucceed = 6
        UnZipping = 7
        Transfering = 8
        WaitInstalling = 9
        Installing = 10
        InstallFailed = 11
        InstallSucceed = 12
        TaskDelete = 13
        TaskStop = 14
    End Enum
End Class
