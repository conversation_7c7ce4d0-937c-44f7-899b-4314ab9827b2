﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class frmDuobao
    Inherits tbBaseGuiForm

    'Form 重写 Dispose，以清理组件列表。
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Windows 窗体设计器所必需的
    Private components As System.ComponentModel.IContainer

    '注意:  以下过程是 Windows 窗体设计器所必需的
    '可以使用 Windows 窗体设计器修改它。  
    '不要使用代码编辑器修改它。
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(frmDuobao))
        Me.btn_close = New iTong.Components.tbButton()
        Me.lblTip = New iTong.Components.tbLabel()
        Me.btnInstall = New iTong.Components.tbButton()
        Me.txtInvationCode = New iTong.Components.tbTextBox()
        Me.lblInvite = New System.Windows.Forms.Label()
        Me.lblName = New System.Windows.Forms.Label()
        Me.lblMinOSVersionValue = New System.Windows.Forms.Label()
        Me.lblMinOSVersion = New System.Windows.Forms.Label()
        Me.lblDeviceTypeValue = New System.Windows.Forms.Label()
        Me.lblDeviceType = New System.Windows.Forms.Label()
        Me.lblVersionValue = New System.Windows.Forms.Label()
        Me.lblVersion = New System.Windows.Forms.Label()
        Me.pic_iconLarge = New iTong.Components.tbPictureBox()
        Me.pbInstallProcess = New iTong.Components.tbControlBar()
        Me.lblInstallInfo = New iTong.Components.tbLabel()
        Me.lblGetInvitationCode = New iTong.Components.tbLabel()
        Me.btnSplit = New iTong.Components.tbButton()
        Me.btn_Setting = New iTong.Components.tbButton()
        Me.menuMain = New iTong.Components.tbContextMenuStrip(Me.components)
        Me.tsmUpdate = New System.Windows.Forms.ToolStripMenuItem()
        Me.tsmiGetLogs = New System.Windows.Forms.ToolStripMenuItem()
        CType(Me.pic_iconLarge, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.menuMain.SuspendLayout()
        Me.SuspendLayout()
        '
        'btn_close
        '
        Me.btn_close.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_close.BackColor = System.Drawing.Color.Transparent
        Me.btn_close.BindingForm = Nothing
        Me.btn_close.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btn_close.Location = New System.Drawing.Point(507, 0)
        Me.btn_close.Name = "btn_close"
        Me.btn_close.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_close.Selectable = True
        Me.btn_close.Size = New System.Drawing.Size(24, 24)
        Me.btn_close.TabIndex = 5
        Me.btn_close.tbAdriftIconWhenHover = False
        Me.btn_close.tbAutoSize = False
        Me.btn_close.tbAutoSizeEx = False
        Me.btn_close.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_close
        Me.btn_close.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_close.tbBadgeNumber = 0
        Me.btn_close.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_close.tbEndEllipsis = False
        Me.btn_close.tbIconHoldPlace = True
        Me.btn_close.tbIconImage = Nothing
        Me.btn_close.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_close.tbIconMore = False
        Me.btn_close.tbIconMouseDown = Nothing
        Me.btn_close.tbIconMouseHover = Nothing
        Me.btn_close.tbIconMouseLeave = Nothing
        Me.btn_close.tbIconPlaceText = 2
        Me.btn_close.tbIconReadOnly = Nothing
        Me.btn_close.tbImageMouseDown = Nothing
        Me.btn_close.tbImageMouseHover = Nothing
        Me.btn_close.tbImageMouseLeave = Nothing
        Me.btn_close.tbProgressValue = 50
        Me.btn_close.tbReadOnly = False
        Me.btn_close.tbReadOnlyText = False
        Me.btn_close.tbShadow = False
        Me.btn_close.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_close.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_close.tbShowDot = False
        Me.btn_close.tbShowMoreIconImg = CType(resources.GetObject("btn_close.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_close.tbShowNew = False
        Me.btn_close.tbShowProgress = False
        Me.btn_close.tbShowTip = True
        Me.btn_close.tbShowToolTipOnButton = False
        Me.btn_close.tbSplit = "3,3,3,3"
        Me.btn_close.tbText = ""
        Me.btn_close.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.tbTextColor = System.Drawing.Color.White
        Me.btn_close.tbTextColorDisable = System.Drawing.Color.White
        Me.btn_close.tbTextColorDown = System.Drawing.Color.White
        Me.btn_close.tbTextColorHover = System.Drawing.Color.White
        Me.btn_close.tbTextMouseDownPlace = 0
        Me.btn_close.tbToolTip = ""
        Me.btn_close.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_close.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_close.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_close.VisibleEx = True
        '
        'lblTip
        '
        Me.lblTip.AutoSize = True
        Me.lblTip.ForeColor = System.Drawing.Color.Green
        Me.lblTip.Location = New System.Drawing.Point(209, 244)
        Me.lblTip.Name = "lblTip"
        Me.lblTip.Size = New System.Drawing.Size(53, 12)
        Me.lblTip.TabIndex = 136
        Me.lblTip.tbAdriftWhenHover = False
        Me.lblTip.tbAutoEllipsis = False
        Me.lblTip.tbAutoSize = True
        Me.lblTip.tbHideImage = False
        Me.lblTip.tbIconImage = Nothing
        Me.lblTip.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblTip.tbIconPlaceText = 5
        Me.lblTip.tbShadow = False
        Me.lblTip.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblTip.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblTip.tbShowScrolling = False
        Me.lblTip.Text = "保存成功"
        Me.lblTip.Visible = False
        '
        'btnInstall
        '
        Me.btnInstall.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnInstall.BackColor = System.Drawing.Color.Transparent
        Me.btnInstall.BindingForm = Nothing
        Me.btnInstall.Font = New System.Drawing.Font("宋体", 11.0!)
        Me.btnInstall.ForeColor = System.Drawing.Color.White
        Me.btnInstall.Location = New System.Drawing.Point(343, 313)
        Me.btnInstall.MinimumSize = New System.Drawing.Size(70, 23)
        Me.btnInstall.Name = "btnInstall"
        Me.btnInstall.Padding = New System.Windows.Forms.Padding(10, 2, 10, 2)
        Me.btnInstall.Selectable = True
        Me.btnInstall.Size = New System.Drawing.Size(147, 30)
        Me.btnInstall.TabIndex = 133
        Me.btnInstall.tbAdriftIconWhenHover = False
        Me.btnInstall.tbAutoSize = False
        Me.btnInstall.tbAutoSizeEx = False
        Me.btnInstall.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_4_blue
        Me.btnInstall.tbBackgroundImageState = iTong.Components.ImageState.FourState
        Me.btnInstall.tbBadgeNumber = 0
        Me.btnInstall.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnInstall.tbEndEllipsis = False
        Me.btnInstall.tbIconHoldPlace = True
        Me.btnInstall.tbIconImage = Nothing
        Me.btnInstall.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnInstall.tbIconMore = False
        Me.btnInstall.tbIconMouseDown = Nothing
        Me.btnInstall.tbIconMouseHover = Nothing
        Me.btnInstall.tbIconMouseLeave = Nothing
        Me.btnInstall.tbIconPlaceText = 2
        Me.btnInstall.tbIconReadOnly = Nothing
        Me.btnInstall.tbImageMouseDown = Nothing
        Me.btnInstall.tbImageMouseHover = Nothing
        Me.btnInstall.tbImageMouseLeave = Nothing
        Me.btnInstall.tbProgressValue = 50
        Me.btnInstall.tbReadOnly = False
        Me.btnInstall.tbReadOnlyText = False
        Me.btnInstall.tbShadow = False
        Me.btnInstall.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnInstall.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnInstall.tbShowDot = False
        Me.btnInstall.tbShowMoreIconImg = CType(resources.GetObject("btnInstall.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btnInstall.tbShowNew = False
        Me.btnInstall.tbShowProgress = False
        Me.btnInstall.tbShowTip = True
        Me.btnInstall.tbShowToolTipOnButton = False
        Me.btnInstall.tbSplit = "13,11,13,11"
        Me.btnInstall.tbText = "一键安装"
        Me.btnInstall.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnInstall.tbTextColor = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDisable = System.Drawing.Color.White
        Me.btnInstall.tbTextColorDown = System.Drawing.Color.White
        Me.btnInstall.tbTextColorHover = System.Drawing.Color.White
        Me.btnInstall.tbTextMouseDownPlace = 0
        Me.btnInstall.tbToolTip = ""
        Me.btnInstall.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnInstall.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnInstall.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.btnInstall.VisibleEx = True
        '
        'txtInvationCode
        '
        Me.txtInvationCode.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.txtInvationCode.BackColor = System.Drawing.Color.White
        Me.txtInvationCode.BorderColor = System.Drawing.Color.FromArgb(CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer), CType(CType(189, Byte), Integer))
        Me.txtInvationCode.BorderColorFocus = System.Drawing.Color.FromArgb(CType(CType(211, Byte), Integer), CType(CType(227, Byte), Integer), CType(CType(245, Byte), Integer))
        Me.txtInvationCode.BorderColorFocusIn = System.Drawing.Color.FromArgb(CType(CType(63, Byte), Integer), CType(CType(131, Byte), Integer), CType(CType(209, Byte), Integer))
        Me.txtInvationCode.BorderStyle = iTong.Components.tbBorderStyle.FixedSingle
        Me.txtInvationCode.ForeColor = System.Drawing.Color.Black
        Me.txtInvationCode.ImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtInvationCode.Location = New System.Drawing.Point(279, 208)
        Me.txtInvationCode.MaxLength = 32767
        Me.txtInvationCode.Name = "txtInvationCode"
        Me.txtInvationCode.Size = New System.Drawing.Size(138, 26)
        Me.txtInvationCode.TabIndex = 132
        Me.txtInvationCode.Tag = Nothing
        Me.txtInvationCode.tbAutoTab = iTong.Components.tbTextBox.AutoTabType.EnterUpDown
        Me.txtInvationCode.tbReadOnly = iTong.Components.tbTextBox.ReadOnlyType.Normal
        Me.txtInvationCode.tbSelMark = True
        Me.txtInvationCode.tbTextBind = ""
        Me.txtInvationCode.TextFormat = iTong.Components.tbTextFormat.Normal
        Me.txtInvationCode.TextImeMode = System.Windows.Forms.ImeMode.[On]
        Me.txtInvationCode.TextPadding = New System.Windows.Forms.Padding(3)
        Me.txtInvationCode.TextTip = ""
        '
        'lblInvite
        '
        Me.lblInvite.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblInvite.BackColor = System.Drawing.Color.Transparent
        Me.lblInvite.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.lblInvite.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblInvite.Location = New System.Drawing.Point(201, 211)
        Me.lblInvite.Name = "lblInvite"
        Me.lblInvite.Size = New System.Drawing.Size(74, 21)
        Me.lblInvite.TabIndex = 131
        Me.lblInvite.Text = "邀 请 码："
        Me.lblInvite.TextAlign = System.Drawing.ContentAlignment.MiddleRight
        '
        'lblName
        '
        Me.lblName.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblName.BackColor = System.Drawing.Color.Transparent
        Me.lblName.Font = New System.Drawing.Font("宋体", 20.0!)
        Me.lblName.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblName.Location = New System.Drawing.Point(207, 79)
        Me.lblName.Name = "lblName"
        Me.lblName.Size = New System.Drawing.Size(230, 39)
        Me.lblName.TabIndex = 130
        Me.lblName.Text = "1元夺宝"
        '
        'lblMinOSVersionValue
        '
        Me.lblMinOSVersionValue.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblMinOSVersionValue.BackColor = System.Drawing.Color.Transparent
        Me.lblMinOSVersionValue.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblMinOSVersionValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblMinOSVersionValue.Location = New System.Drawing.Point(275, 177)
        Me.lblMinOSVersionValue.Name = "lblMinOSVersionValue"
        Me.lblMinOSVersionValue.Size = New System.Drawing.Size(162, 14)
        Me.lblMinOSVersionValue.TabIndex = 129
        Me.lblMinOSVersionValue.Text = "7.0或更高版本"
        '
        'lblMinOSVersion
        '
        Me.lblMinOSVersion.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblMinOSVersion.BackColor = System.Drawing.Color.Transparent
        Me.lblMinOSVersion.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblMinOSVersion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblMinOSVersion.Location = New System.Drawing.Point(171, 177)
        Me.lblMinOSVersion.Name = "lblMinOSVersion"
        Me.lblMinOSVersion.Size = New System.Drawing.Size(104, 14)
        Me.lblMinOSVersion.TabIndex = 128
        Me.lblMinOSVersion.Text = "固件需求："
        Me.lblMinOSVersion.TextAlign = System.Drawing.ContentAlignment.TopRight
        '
        'lblDeviceTypeValue
        '
        Me.lblDeviceTypeValue.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblDeviceTypeValue.BackColor = System.Drawing.Color.Transparent
        Me.lblDeviceTypeValue.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDeviceTypeValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceTypeValue.Location = New System.Drawing.Point(275, 152)
        Me.lblDeviceTypeValue.Name = "lblDeviceTypeValue"
        Me.lblDeviceTypeValue.Size = New System.Drawing.Size(162, 14)
        Me.lblDeviceTypeValue.TabIndex = 127
        Me.lblDeviceTypeValue.Text = "iPhone/iPod touch"
        '
        'lblDeviceType
        '
        Me.lblDeviceType.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblDeviceType.BackColor = System.Drawing.Color.Transparent
        Me.lblDeviceType.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblDeviceType.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblDeviceType.Location = New System.Drawing.Point(171, 152)
        Me.lblDeviceType.Name = "lblDeviceType"
        Me.lblDeviceType.Size = New System.Drawing.Size(104, 14)
        Me.lblDeviceType.TabIndex = 126
        Me.lblDeviceType.Text = "支持设备："
        Me.lblDeviceType.TextAlign = System.Drawing.ContentAlignment.TopRight
        '
        'lblVersionValue
        '
        Me.lblVersionValue.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblVersionValue.BackColor = System.Drawing.Color.Transparent
        Me.lblVersionValue.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblVersionValue.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblVersionValue.Location = New System.Drawing.Point(275, 129)
        Me.lblVersionValue.Name = "lblVersionValue"
        Me.lblVersionValue.Size = New System.Drawing.Size(162, 14)
        Me.lblVersionValue.TabIndex = 125
        Me.lblVersionValue.Text = "1.1.0"
        '
        'lblVersion
        '
        Me.lblVersion.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.lblVersion.BackColor = System.Drawing.Color.Transparent
        Me.lblVersion.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.lblVersion.ForeColor = System.Drawing.Color.FromArgb(CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer), CType(CType(51, Byte), Integer))
        Me.lblVersion.Location = New System.Drawing.Point(171, 129)
        Me.lblVersion.Name = "lblVersion"
        Me.lblVersion.Size = New System.Drawing.Size(104, 14)
        Me.lblVersion.TabIndex = 124
        Me.lblVersion.Text = "软件版本："
        Me.lblVersion.TextAlign = System.Drawing.ContentAlignment.TopRight
        '
        'pic_iconLarge
        '
        Me.pic_iconLarge.BackColor = System.Drawing.Color.FromArgb(CType(CType(242, Byte), Integer), CType(CType(244, Byte), Integer), CType(CType(247, Byte), Integer))
        Me.pic_iconLarge.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch
        Me.pic_iconLarge.Image = Global.iTong.My.Resources.Resources.app_icon_jinbao
        Me.pic_iconLarge.Location = New System.Drawing.Point(40, 110)
        Me.pic_iconLarge.Name = "pic_iconLarge"
        Me.pic_iconLarge.Size = New System.Drawing.Size(100, 100)
        Me.pic_iconLarge.SizeMode = System.Windows.Forms.PictureBoxSizeMode.Zoom
        Me.pic_iconLarge.TabIndex = 123
        Me.pic_iconLarge.TabStop = False
        Me.pic_iconLarge.tbAutoSize = False
        Me.pic_iconLarge.tbBackgroundImage = Nothing
        Me.pic_iconLarge.tbSplit = "0,0,0,0"
        '
        'pbInstallProcess
        '
        Me.pbInstallProcess.BackColor = System.Drawing.Color.Transparent
        Me.pbInstallProcess.Font = New System.Drawing.Font("宋体", 8.0!)
        Me.pbInstallProcess.Location = New System.Drawing.Point(40, 287)
        Me.pbInstallProcess.Name = "pbInstallProcess"
        Me.pbInstallProcess.Size = New System.Drawing.Size(450, 14)
        Me.pbInstallProcess.TabIndex = 137
        Me.pbInstallProcess.tbBackgroundImage = CType(resources.GetObject("pbInstallProcess.tbBackgroundImage"), System.Drawing.Image)
        Me.pbInstallProcess.tbCanDragValue = False
        Me.pbInstallProcess.tbDirection = iTong.Components.tbControlBar.tbBarDirection.Horizontal
        Me.pbInstallProcess.tbDotImage = CType(resources.GetObject("pbInstallProcess.tbDotImage"), System.Drawing.Image)
        Me.pbInstallProcess.tbDotImageState = iTong.Components.ImageState.ThreeState
        Me.pbInstallProcess.tbDotMouseDown = Nothing
        Me.pbInstallProcess.tbDotMouseHover = Nothing
        Me.pbInstallProcess.tbDotMouseLeave = Nothing
        Me.pbInstallProcess.tbDownloadImage = CType(resources.GetObject("pbInstallProcess.tbDownloadImage"), System.Drawing.Image)
        Me.pbInstallProcess.tbDownMax = 100
        Me.pbInstallProcess.tbDownMin = 0
        Me.pbInstallProcess.tbDownValue = 0
        Me.pbInstallProcess.tbIsWaiting = False
        Me.pbInstallProcess.tbPlayImage = Global.iTong.My.Resources.Resources.app_progress_value
        Me.pbInstallProcess.tbPlayMax = 100
        Me.pbInstallProcess.tbPlayMin = 0
        Me.pbInstallProcess.tbPlayValue = 0
        Me.pbInstallProcess.tbShowDot = False
        Me.pbInstallProcess.tbShowText = True
        Me.pbInstallProcess.tbSplit = "13,4,11,5"
        Me.pbInstallProcess.Visible = False
        '
        'lblInstallInfo
        '
        Me.lblInstallInfo.AutoEllipsis = True
        Me.lblInstallInfo.BackColor = System.Drawing.Color.Transparent
        Me.lblInstallInfo.Location = New System.Drawing.Point(40, 262)
        Me.lblInstallInfo.Name = "lblInstallInfo"
        Me.lblInstallInfo.Size = New System.Drawing.Size(286, 18)
        Me.lblInstallInfo.TabIndex = 138
        Me.lblInstallInfo.tbAdriftWhenHover = False
        Me.lblInstallInfo.tbAutoEllipsis = True
        Me.lblInstallInfo.tbAutoSize = False
        Me.lblInstallInfo.tbHideImage = False
        Me.lblInstallInfo.tbIconImage = Nothing
        Me.lblInstallInfo.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblInstallInfo.tbIconPlaceText = 5
        Me.lblInstallInfo.tbShadow = False
        Me.lblInstallInfo.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblInstallInfo.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblInstallInfo.tbShowScrolling = False
        Me.lblInstallInfo.TextAlign = System.Drawing.ContentAlignment.MiddleLeft
        '
        'lblGetInvitationCode
        '
        Me.lblGetInvitationCode.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.lblGetInvitationCode.AutoSize = True
        Me.lblGetInvitationCode.BackColor = System.Drawing.Color.Transparent
        Me.lblGetInvitationCode.Cursor = System.Windows.Forms.Cursors.Hand
        Me.lblGetInvitationCode.ForeColor = System.Drawing.Color.FromArgb(CType(CType(34, Byte), Integer), CType(CType(168, Byte), Integer), CType(CType(255, Byte), Integer))
        Me.lblGetInvitationCode.Location = New System.Drawing.Point(425, 215)
        Me.lblGetInvitationCode.Name = "lblGetInvitationCode"
        Me.lblGetInvitationCode.Size = New System.Drawing.Size(65, 12)
        Me.lblGetInvitationCode.TabIndex = 135
        Me.lblGetInvitationCode.tbAdriftWhenHover = False
        Me.lblGetInvitationCode.tbAutoEllipsis = False
        Me.lblGetInvitationCode.tbAutoSize = True
        Me.lblGetInvitationCode.tbHideImage = False
        Me.lblGetInvitationCode.tbIconImage = Nothing
        Me.lblGetInvitationCode.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleLeft
        Me.lblGetInvitationCode.tbIconPlaceText = 5
        Me.lblGetInvitationCode.tbShadow = False
        Me.lblGetInvitationCode.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.lblGetInvitationCode.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.lblGetInvitationCode.tbShowScrolling = False
        Me.lblGetInvitationCode.Text = "获取邀请码"
        '
        'btnSplit
        '
        Me.btnSplit.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btnSplit.BackColor = System.Drawing.Color.FromArgb(CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer), CType(CType(224, Byte), Integer))
        Me.btnSplit.BindingForm = Nothing
        Me.btnSplit.Font = New System.Drawing.Font("宋体", 9.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(134, Byte))
        Me.btnSplit.Location = New System.Drawing.Point(174, 79)
        Me.btnSplit.Name = "btnSplit"
        Me.btnSplit.Padding = New System.Windows.Forms.Padding(5, 2, 5, 2)
        Me.btnSplit.Selectable = True
        Me.btnSplit.Size = New System.Drawing.Size(1, 148)
        Me.btnSplit.TabIndex = 139
        Me.btnSplit.tbAdriftIconWhenHover = False
        Me.btnSplit.tbAutoSize = False
        Me.btnSplit.tbAutoSizeEx = False
        Me.btnSplit.tbBackgroundImage = Nothing
        Me.btnSplit.tbBackgroundImageState = iTong.Components.ImageState.OneState
        Me.btnSplit.tbBadgeNumber = 0
        Me.btnSplit.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btnSplit.tbEndEllipsis = False
        Me.btnSplit.tbIconHoldPlace = True
        Me.btnSplit.tbIconImage = Nothing
        Me.btnSplit.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btnSplit.tbIconMore = False
        Me.btnSplit.tbIconMouseDown = Nothing
        Me.btnSplit.tbIconMouseHover = Nothing
        Me.btnSplit.tbIconMouseLeave = Nothing
        Me.btnSplit.tbIconPlaceText = 2
        Me.btnSplit.tbIconReadOnly = Nothing
        Me.btnSplit.tbImageMouseDown = Nothing
        Me.btnSplit.tbImageMouseHover = Nothing
        Me.btnSplit.tbImageMouseLeave = Nothing
        Me.btnSplit.tbProgressValue = 50
        Me.btnSplit.tbReadOnly = False
        Me.btnSplit.tbReadOnlyText = False
        Me.btnSplit.tbShadow = False
        Me.btnSplit.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btnSplit.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btnSplit.tbShowDot = False
        Me.btnSplit.tbShowMoreIconImg = Global.iTong.My.Resources.Resources.welcome_btn_split
        Me.btnSplit.tbShowNew = False
        Me.btnSplit.tbShowProgress = False
        Me.btnSplit.tbShowTip = True
        Me.btnSplit.tbShowToolTipOnButton = False
        Me.btnSplit.tbSplit = "35,0,35,0"
        Me.btnSplit.tbText = ""
        Me.btnSplit.tbTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.tbTextColor = System.Drawing.Color.White
        Me.btnSplit.tbTextColorDisable = System.Drawing.Color.White
        Me.btnSplit.tbTextColorDown = System.Drawing.Color.White
        Me.btnSplit.tbTextColorHover = System.Drawing.Color.White
        Me.btnSplit.tbTextMouseDownPlace = 0
        Me.btnSplit.tbToolTip = ""
        Me.btnSplit.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btnSplit.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btnSplit.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btnSplit.VisibleEx = True
        '
        'btn_Setting
        '
        Me.btn_Setting.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.btn_Setting.BackColor = System.Drawing.Color.Transparent
        Me.btn_Setting.BindingForm = Nothing
        Me.btn_Setting.Cursor = System.Windows.Forms.Cursors.Hand
        Me.btn_Setting.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Setting.Location = New System.Drawing.Point(483, 0)
        Me.btn_Setting.Name = "btn_Setting"
        Me.btn_Setting.Padding = New System.Windows.Forms.Padding(0, 1, 0, 0)
        Me.btn_Setting.Selectable = True
        Me.btn_Setting.Size = New System.Drawing.Size(24, 24)
        Me.btn_Setting.TabIndex = 140
        Me.btn_Setting.tbAdriftIconWhenHover = False
        Me.btn_Setting.tbAutoSize = True
        Me.btn_Setting.tbAutoSizeEx = True
        Me.btn_Setting.tbBackgroundImage = Global.iTong.My.Resources.Resources.btn_setting
        Me.btn_Setting.tbBackgroundImageState = iTong.Components.ImageState.ThreeState
        Me.btn_Setting.tbBadgeNumber = 0
        Me.btn_Setting.tbBadgeNumberOffset = New System.Drawing.Point(0, 0)
        Me.btn_Setting.tbEndEllipsis = False
        Me.btn_Setting.tbIconHoldPlace = True
        Me.btn_Setting.tbIconImage = Nothing
        Me.btn_Setting.tbIconImageAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Setting.tbIconImageState = iTong.Components.ImageState.OneState
        Me.btn_Setting.tbIconMore = False
        Me.btn_Setting.tbIconMouseDown = Nothing
        Me.btn_Setting.tbIconMouseHover = Nothing
        Me.btn_Setting.tbIconMouseLeave = Nothing
        Me.btn_Setting.tbIconPlaceText = 2
        Me.btn_Setting.tbIconReadOnly = Nothing
        Me.btn_Setting.tbImageMouseDown = Nothing
        Me.btn_Setting.tbImageMouseHover = Nothing
        Me.btn_Setting.tbImageMouseLeave = Nothing
        Me.btn_Setting.tbProgressValue = 50
        Me.btn_Setting.tbReadOnly = False
        Me.btn_Setting.tbReadOnlyText = False
        Me.btn_Setting.tbShadow = False
        Me.btn_Setting.tbShadowColor = System.Drawing.Color.FromArgb(CType(CType(22, Byte), Integer), CType(CType(74, Byte), Integer), CType(CType(160, Byte), Integer))
        Me.btn_Setting.tbShadowOffset = New System.Drawing.Point(1, 1)
        Me.btn_Setting.tbShowDot = False
        Me.btn_Setting.tbShowMoreIconImg = CType(resources.GetObject("btn_Setting.tbShowMoreIconImg"), System.Drawing.Image)
        Me.btn_Setting.tbShowNew = False
        Me.btn_Setting.tbShowProgress = False
        Me.btn_Setting.tbShowTip = True
        Me.btn_Setting.tbShowToolTipOnButton = False
        Me.btn_Setting.tbSplit = "3,3,3,3"
        Me.btn_Setting.tbText = ""
        Me.btn_Setting.tbTextAlign = System.Drawing.ContentAlignment.MiddleRight
        Me.btn_Setting.tbTextColor = System.Drawing.Color.FromArgb(CType(CType(0, Byte), Integer), CType(CType(53, Byte), Integer), CType(CType(94, Byte), Integer))
        Me.btn_Setting.tbTextColorDisable = System.Drawing.Color.Gray
        Me.btn_Setting.tbTextColorDown = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_Setting.tbTextColorHover = System.Drawing.Color.FromArgb(CType(CType(24, Byte), Integer), CType(CType(65, Byte), Integer), CType(CType(146, Byte), Integer))
        Me.btn_Setting.tbTextMouseDownPlace = 0
        Me.btn_Setting.tbToolTip = ""
        Me.btn_Setting.tbToolTipColor = System.Drawing.Color.FromArgb(CType(CType(122, Byte), Integer), CType(CType(117, Byte), Integer), CType(CType(117, Byte), Integer))
        Me.btn_Setting.tbToolTipFont = New System.Drawing.Font("宋体", 9.0!)
        Me.btn_Setting.tbToolTipTextAlign = System.Drawing.ContentAlignment.MiddleCenter
        Me.btn_Setting.VisibleEx = True
        '
        'menuMain
        '
        Me.menuMain.AccessibleDescription = "153x70"
        Me.menuMain.DropShadowEnabled = False
        Me.menuMain.Font = New System.Drawing.Font("宋体", 9.0!)
        Me.menuMain.Items.AddRange(New System.Windows.Forms.ToolStripItem() {Me.tsmUpdate, Me.tsmiGetLogs})
        Me.menuMain.Name = "menuMain"
        Me.menuMain.Size = New System.Drawing.Size(137, 48)
        Me.menuMain.Tag = ""
        Me.menuMain.tbBackColor = System.Drawing.Color.White
        Me.menuMain.tbBorderColor = System.Drawing.Color.FromArgb(CType(CType(182, Byte), Integer), CType(CType(192, Byte), Integer), CType(CType(201, Byte), Integer))
        Me.menuMain.tbSelectedItemBackColor = System.Drawing.Color.FromArgb(CType(CType(31, Byte), Integer), CType(CType(143, Byte), Integer), CType(CType(219, Byte), Integer))
        Me.menuMain.tbSelectedItemForeColor = System.Drawing.Color.White
        '
        'tsmUpdate
        '
        Me.tsmUpdate.Name = "tsmUpdate"
        Me.tsmUpdate.Size = New System.Drawing.Size(136, 22)
        Me.tsmUpdate.Text = "检查更新(&K)"
        '
        'tsmiGetLogs
        '
        Me.tsmiGetLogs.Name = "tsmiGetLogs"
        Me.tsmiGetLogs.Size = New System.Drawing.Size(136, 22)
        Me.tsmiGetLogs.Text = "错误日志"
        '
        'frmDuobao
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 12.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None
        Me.ClientSize = New System.Drawing.Size(530, 355)
        Me.Controls.Add(Me.btn_Setting)
        Me.Controls.Add(Me.btnSplit)
        Me.Controls.Add(Me.btnInstall)
        Me.Controls.Add(Me.lblInstallInfo)
        Me.Controls.Add(Me.pbInstallProcess)
        Me.Controls.Add(Me.lblTip)
        Me.Controls.Add(Me.lblGetInvitationCode)
        Me.Controls.Add(Me.txtInvationCode)
        Me.Controls.Add(Me.lblInvite)
        Me.Controls.Add(Me.lblName)
        Me.Controls.Add(Me.lblMinOSVersionValue)
        Me.Controls.Add(Me.lblMinOSVersion)
        Me.Controls.Add(Me.lblDeviceTypeValue)
        Me.Controls.Add(Me.lblDeviceType)
        Me.Controls.Add(Me.lblVersionValue)
        Me.Controls.Add(Me.lblVersion)
        Me.Controls.Add(Me.pic_iconLarge)
        Me.Controls.Add(Me.btn_close)
        Me.Name = "frmDuobao"
        Me.tbGuiBackground = Global.iTong.My.Resources.Resources.frm_bg_sub
        Me.tbShowTitleOnForm = True
        Me.tbSplit = "10,33,10,55"
        Me.tbTitleBackColor = System.Drawing.Color.Transparent
        Me.tbTitleLocation = New System.Drawing.Point(4, 6)
        Me.Text = "1元夺宝安装器"
        CType(Me.pic_iconLarge, System.ComponentModel.ISupportInitialize).EndInit()
        Me.menuMain.ResumeLayout(False)
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents btn_close As iTong.Components.tbButton
    Friend WithEvents lblTip As iTong.Components.tbLabel
    Friend WithEvents btnInstall As iTong.Components.tbButton
    Friend WithEvents txtInvationCode As iTong.Components.tbTextBox
    Friend WithEvents lblInvite As System.Windows.Forms.Label
    Friend WithEvents lblName As System.Windows.Forms.Label
    Friend WithEvents lblMinOSVersionValue As System.Windows.Forms.Label
    Friend WithEvents lblMinOSVersion As System.Windows.Forms.Label
    Friend WithEvents lblDeviceTypeValue As System.Windows.Forms.Label
    Friend WithEvents lblDeviceType As System.Windows.Forms.Label
    Friend WithEvents lblVersionValue As System.Windows.Forms.Label
    Friend WithEvents lblVersion As System.Windows.Forms.Label
    Friend WithEvents pic_iconLarge As iTong.Components.tbPictureBox
    Protected WithEvents pbInstallProcess As iTong.Components.tbControlBar
    Friend WithEvents lblInstallInfo As iTong.Components.tbLabel
    Friend WithEvents lblGetInvitationCode As iTong.Components.tbLabel
    Friend WithEvents btnSplit As iTong.Components.tbButton
    Friend WithEvents btn_Setting As iTong.Components.tbButton
    Friend WithEvents menuMain As iTong.Components.tbContextMenuStrip
    Friend WithEvents tsmUpdate As System.Windows.Forms.ToolStripMenuItem
    Friend WithEvents tsmiGetLogs As System.Windows.Forms.ToolStripMenuItem
End Class
